import { camelCase } from 'lodash';
import * as utils from '../../step-definitions-utils';
import AbstractPageObject from '../abstract-page-object';
import { DialogObject } from '../container/dialog/dialog-object';
import { DropDownMenuObject } from '../dropdown-menu-object';
import { waitForPromises } from '../field/wait-util';

export class Header extends AbstractPageObject {
    public readonly dropDownMenu: DropDownMenuObject;

    constructor(parent: string) {
        super(`${parent} ${utils.getDataTestIdSelector({ domSelector: 'header', dataTestIdValue: 'e-header' })}`);
        this.dropDownMenu = new DropDownMenuObject(this.cssSelector);
    }

    private async findTab(anchorLabel: string, contextSelector = '.e-header-nav') {
        const foundElements = await this.find(
            `${contextSelector} [data-testid~="e-xtrem-tab-${camelCase(anchorLabel)}"]`,
            true,
        );
        // there is a special case when there are 2 tabs with the same name for exemple tab "Information" from smoke-test-pr-cd-sales-invoice , in this case we need to return the "active" tab the one with class = active
        if (Array.isArray(foundElements) && foundElements.length === 2) {
            return this.find(
                `${contextSelector} [data-testid~="e-xtrem-tab-${camelCase(anchorLabel)}"][class="e-xtrem-tab-item e-xtrem-tab-item-active"]`,
                true,
            );
        }
        return this.find(`${contextSelector} [data-testid~="e-xtrem-tab-${camelCase(anchorLabel)}"]`, true);
    }

    private findSidebarTab(anchorLabel: string) {
        return this.findTab(anchorLabel, '[role="tablist"]');
    }

    private findDialogTab(anchorLabel: string) {
        return this.findTab(anchorLabel, '[data-component="dialog-full-screen"]');
    }

    // eslint-disable-next-line class-methods-use-this
    async expectSidebarTitle(title: string) {
        let selectorToUse = 'div[aria-label="sidebar"] h3';
        let field = await $('div[aria-label="sidebar"] h3');
        if (!(await field.isExisting())) {
            // bug XT-90889 caused by  XT-87354  . Bauxite To investigate
            selectorToUse = 'div.e-sidebar-title';
            field = await $(selectorToUse);
            if (!(await field.isExisting())) {
                selectorToUse = 'div[data-element="sidebar"][aria-modal="true"] h1';
                field = await $(selectorToUse);
                if (!(await field.isExisting())) {
                    throw new Error(`Expected element could not be found: ${title}\nSelector: ${selectorToUse}`);
                }
            }
        }
        const foundText = await field.getText();
        if (!(foundText === title)) {
            throw new Error(
                `Expected element to be displayed: ${title} - (actual: ${foundText}) \nSelector: ${selectorToUse}`,
            );
        }
    }

    async expectPageTitle(expectedTitle: string, contains: boolean = false) {
        const dialog = new DialogObject();

        // Sometimes the error doesn't display instantly so wait for any errors first.
        await waitForPromises(200, 'Wait for any dialogs to display');

        if (await (await dialog.get()).isExisting()) {
            if (await dialog.isErrorMessage()) {
                const errorMessage = await dialog.getErrorMessageText();

                await this.takePuppeteerPageScreenshot({ captureBeyondViewport: true });
                throw new Error(`Unexpected Error dialog detected with text: "${errorMessage}"`);
            }
        }

        const browserURL = await browser.getUrl();
        let titleSelector = '.e-header-title';
        if (utils.regex_handheld(browserURL)) {
            titleSelector = 'div.se-header-title';
        }

        if (contains) {
            await this.expectTextContains({
                toBe: expectedTitle,
                ignoreCase: false,
                cssSelector: titleSelector,
                ignoreContext: true,
            });
        } else {
            await this.expectTextContent({
                toBe: expectedTitle,
                ignoreCase: false,
                cssSelector: titleSelector,
                ignoreContext: true,
            });
        }
    }

    async expectPageSubTitle(expectedSubTitle: string, contains: boolean = false) {
        const browserURL = await browser.getUrl();
        let titleSelector = 'p.e-header-subtitle';
        if (utils.regex_handheld(browserURL)) {
            titleSelector = 'div.se-header-subtitle';
        }

        if (contains) {
            await this.expectTextContains({
                toBe: expectedSubTitle,
                ignoreCase: false,
                cssSelector: titleSelector,
                ignoreContext: true,
            });
        } else {
            await this.expectTextContent({
                toBe: expectedSubTitle,
                ignoreCase: false,
                cssSelector: titleSelector,
                ignoreContext: true,
            });
        }
    }

    async expectToHaveNumberOfSections(expectedNumberOfTabs: number) {
        const tabs: any[] = (await this.findAll(
            utils.getMultipleDataTestIdSelector('button', ['e-header-nav-tab-field']),
        )) as any;
        if (tabs.length !== expectedNumberOfTabs) {
            await browser.takeScreenshot();
            throw new Error(
                `Expected number of tabs: ${expectedNumberOfTabs}, actual: ${tabs.length} (selector: ${this.cssSelector} 'e-header-nav-tab'`,
            );
        }
    }

    async selectNavigationAnchor(anchorLabel: string, context: utils.ElementContext) {
        let tab = null;
        await this.expectToBeReady(
            utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-page' }),
            true,
        );
        switch (context) {
            case utils.ElementContext.SIDEBAR:
                await browser.waitUntil(
                    async () => (await this.find("div[data-component='sidebar']", true)).isExisting(),
                    {
                        timeout: this.timeoutWaitFor,
                        timeoutMsg: `Cannot find sidebar.\nAnchor label: ${anchorLabel}`,
                    },
                );
                await browser.waitUntil(async () => !!(await this.findSidebarTab(anchorLabel)), {
                    timeout: this.timeoutWaitFor,
                    timeoutMsg: `Cannot find tab.\nAnchor label: ${anchorLabel}`,
                });
                tab = await this.findSidebarTab(anchorLabel)!;
                break;
            case utils.ElementContext.FULL_MODAL:
                await browser.waitUntil(
                    async () => (await this.find('div[data-component="full-screen-heading"]', true)).isExisting(),
                    { timeout: this.timeoutWaitFor },
                );
                await browser.waitUntil(async () => !!(await this.findDialogTab(anchorLabel)), {
                    timeout: this.timeoutWaitFor,
                });
                tab = await this.findDialogTab(anchorLabel)!;
                break;
            default:
                await browser.waitUntil(async () => !!(await this.findTab(anchorLabel)), {
                    timeout: this.timeoutWaitFor,
                    timeoutMsg: `Cannot find tab.\nAnchor label: ${anchorLabel}`,
                });
                tab = await this.findTab(anchorLabel)!;
                break;
        }
        // await tab.waitForClickable({ timeout: 10000 });
        await tab.click();
        await waitForPromises(500, 'Wait for the anchor to be selected');
    }

    async expectNavigationAnchorSelected(anchorLabel: string) {
        // eslint-disable-next-line no-void
        void browser.waitUntil(async () => !!(await this.findTab(anchorLabel)), {
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Cannot find tab.\nAnchor label: ${anchorLabel}`,
        });
        const tab = await this.findTab(anchorLabel);

        if (!(await tab.isExisting())) {
            throw new Error(`Expected element could not be found: "${anchorLabel}"`);
        }
        // eslint-disable-next-line no-void
        void browser.waitUntil(
            async () => {
                const isSelected = await tab?.getAttribute('aria-selected');
                return Boolean(isSelected);
            },
            { timeoutMsg: `The ${anchorLabel} navigation anchor is not selected.`, timeout: this.timeoutWaitFor },
        );
    }

    async clickHeaderIcon(name: string) {
        const iconBtn = await this.find(
            utils.getDataTestIdSelector({ domSelector: 'button', dataTestIdValue: 'e-page-close-button' }),
        );
        await utils.waitForElementToExist({ name, selector: iconBtn.selector.toString() });

        const iconBtnTxt = await iconBtn.getAttribute('aria-label');

        if (utils.formatString(iconBtnTxt) === utils.formatString(name)) {
            await iconBtn.click();
            return;
        }
        throw new Error(`Expected element could not be found: "${name}".\nSelector: ${iconBtn.selector}`);
    }

    async expectNavigationArrowButtonToBeDisplayed(actionName: 'previous' | 'next' | 'return', reverse: boolean) {
        const selector = `[data-testid="e-header-navigation-arrow-${actionName}"]`;
        await this.expectToBeDisplayed(selector, reverse);
    }

    async expectNavigationArrowButtonToBeEnabled(actionName: 'previous' | 'next', reverse = false) {
        const navigationArrowButton = `[data-testid="e-header-navigation-arrow-${actionName}"]`;
        await this.expectToBeEnabled(navigationArrowButton, reverse);
    }

    async clickNavigationArrowButton(actionName: string) {
        const selector = `[data-testid="e-header-navigation-arrow-${actionName}"]`;
        await this.expectToAppear({ cssSelector: selector, ignoreContext: true });
        await this.click(selector, true);
    }

    async clickQuickAction(actionLabel: string) {
        const selector = `[data-testid="e-header-quick-action-label-${camelCase(actionLabel)}"]`;
        await this.expectToAppear({ cssSelector: selector });
        await this.click(selector);
        await waitForPromises(500, 'Wait for triggered action to complete');
    }

    async expectQuickActionToBeEnabled(actionLabel: string, reverse?: boolean) {
        const selector = `[data-testid="e-header-quick-action-label-${camelCase(actionLabel)}"]`;
        await this.expectToAppear({ cssSelector: selector });
        await this.expectToBeEnabled(selector, reverse);
    }

    async expectQuickActionToBeDisplayed(actionLabel: string, reverse?: boolean) {
        const selector = `[data-testid="e-header-quick-action-label-${camelCase(actionLabel)}"]`;
        await this.expectToAppear({ cssSelector: selector });
        await this.expectToBeDisplayed(selector, reverse);
    }

    async clickHeaderSectionToggleButton() {
        const selector = '[data-testid="e-toggle-header-section"]';
        await this.expectToAppear({ cssSelector: selector });
        await this.click(selector);
    }

    async clickHeader360ViewSwitch() {
        const selector = utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-page-360-switch' });
        await this.expectToAppear({ cssSelector: selector });
        await this.click(selector);
    }

    async expect360ViewSwitchState(value: string) {
        const selectorSwitchToggle = '[data-testid="e-page-360-switch"] [data-role="checkable-input"]';
        const selectorSwitch = '[data-testid="e-page-360-switch"] span';
        if (value === 'ON' || value === 'OFF') {
            await this.expectToBeEnabled(selectorSwitch);
            await this.expectToAppear({ cssSelector: selectorSwitchToggle });
            await this.expectTextContent({
                toBe: value,
                ignoreCase: false,
                cssSelector: selectorSwitchToggle,
                ignoreContext: false,
            });
        } else if (value === 'disabled') {
            await this.expectNotToBeEnabled(selectorSwitch);
        } else {
            throw new Error(`Invalid value: ${value}`);
        }
    }
}
