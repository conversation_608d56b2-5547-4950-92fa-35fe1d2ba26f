import { Then, When } from '@cucumber/cucumber';
import * as utils from '../../../step-definitions-utils';
import pageModel from '../../main-page';
import * as StaticStore from '../../static-store';
import { CardNestedFieldObject } from '../table-card-view/card-nested-field-object';
import { CardObject } from '../table-card-view/card-object';
import { expectImageStatus } from '../table-card-view/card-utils';
import { waitForPromises } from '../wait-util';
import { NestedGridObject } from './nested-grid-object';

When(
    /^the user selects the "(.*)" (bound|labelled) nested grid field on (the main page|a modal|a full width modal|the helper panel|the sidebar|the navigation panel)$/,
    async (identifier: string, lookupStrategy: utils.LookupStrategy, context: utils.ElementContext) => {
        const field = new NestedGridObject({ identifier, lookupStrategy, context });
        await field.dismissAllNotification();
        await utils.waitForElementToBeDisplayed({ name: 'nested grid field', selector: field.cssSelector });
        await StaticStore.storeField(utils.fieldTypes.nestedGrid, field);
    },
);

When(
    /^the user selects row with text "([^"\n\r]*)" in column with header "([^"\n\r]*)" in the nested grid field$/,
    async (expectedText: string, expectedColumnHeader: string) => {
        await pageModel.waitForFinishLoading();
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await utils.waitForElementToExist({ name: 'nested grid field', selector: field.cssSelector });
        await field.waitForTableFieldToLoad();
        await field.dismissAllNotification();
        await field.searchRowWithText({ expectedText, expectedColumnHeader, select: true });
    },
);

Then(
    /^the row with text "([^"\n\r]*)" in column with header "([^"\n\r]*)" in the nested grid field is (displayed|hidden)$/,
    async (expectedText: string, expectedColumnHeader: string, cssState: 'displayed' | 'hidden') => {
        await pageModel.waitForFinishLoading();
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await utils.waitForElementToExist({ name: 'nested grid field', selector: field.cssSelector });
        await field.waitForTableFieldToLoad();
        await field.dismissAllNotification();
        await field.searchRowWithText({
            expectedText,
            expectedColumnHeader,
            select: false,
            expectedDisplayed: cssState === 'displayed',
        });
    },
);

When(/^the user selects the row with the following content in the nested grid field$/, async (table: any) => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await (
        await $(field.cssSelector)
    ).waitForExist({
        timeoutMsg: `Expected nested grid object could not be found.\nSelector: ${field.cssSelector})`,
    });
    await pageModel.waitForFinishLoading();
    await field.selectRowWithDataTable(table);
});

When(
    /^the user (ticks|unticks) the main checkbox of the selected row in the nested grid field$/,
    async (toggleState: string) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await pageModel.waitForFinishLoading();
        await field.checkSelectRowWithText(toggleState);
    },
);

When(
    /^the user (expands|collapses) the selected row of the nested grid field$/,
    async (toggle: utils.ExpandedOrCollapsed) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await pageModel.waitForFinishLoading();
        await field.expandSelectRowWithText(toggle);
    },
);

When(/^the user clicks the "(.*)" action of the selected row in the nested grid field$/, async (actionName: string) => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await pageModel.waitForFinishLoading();
    await field.selectAction(actionName);
});

When(
    /^the user (ticks|unticks) the main checkbox of the card (\d+) in the nested grid field$/,
    async (selection: 'ticks' | 'unticks', cardNumber: number) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await pageModel.waitForFinishLoading();
        const card = new CardObject({ parent: field, cardNumber });
        await card.tickCheckbox(selection);
        await waitForPromises(500, 'main checkbox selection');
    },
);

When(/^the value in the header of the mobile nested grid field is "(.*)"$/, async (headerValue: string) => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await pageModel.waitForFinishLoading();
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(headerValue);
    await field.expectMobileTitle('value', storeValue);
});

When(/^the level in the header of the mobile nested grid field is "(.*)"$/, async (headerValue: string) => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await pageModel.waitForFinishLoading();
    await field.expectMobileTitle('level', headerValue);
});

When(/^the user clicks the back arrow in the header of the mobile nested grid field$/, async () => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await pageModel.waitForFinishLoading();
    await field.clickBackButton();
});

When(/^the user clicks the card (\d+) in the nested grid field?$/, async (cardNumber: number) => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await pageModel.waitForFinishLoading();
    const card = new CardObject({ parent: field, cardNumber });
    await card.click();
});

When(
    /^the user (ticks|unticks) the main checkbox of the card with the text "(.*)" in the nested grid field$/,
    async (selection: 'ticks' | 'unticks', label: string) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await pageModel.waitForFinishLoading();
        const card = new CardObject({ parent: field, label });
        await card.tickCheckbox(selection);
        await waitForPromises(500, 'main checkbox selection');
    },
);

When(
    /^all the cards in the nested grid field are (selected|unselected)$/,
    async (selection: 'selected' | 'unselected') => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await pageModel.waitForFinishLoading();
        await field.validateCardSelection(selection);
        await waitForPromises(500, 'main checkbox selection');
    },
);

Then(
    /^the nested grid field is (empty|not empty) at level (\d+)(?: with parent id (\d+))?$/,
    async (state: 'empty' | 'not empty', level: number, parentId?: number) => {
        const nestedGrid = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await nestedGrid.expectNestedGridToBeEmpty({ level, parentId, state });
    },
);

Then(
    /^the main checkbox of the selected row in the nested grid field is (checked|unchecked)$/,
    async (checkBox: 'checked' | 'unchecked') => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await pageModel.waitForFinishLoading();
        await field.validateRowSelection(checkBox === 'checked');
        await waitForPromises(500, 'main checkbox selection');
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested (date|label|numeric|reference|select|text|link) field of the card (\d+) in the nested grid field is "(.*)"$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.NestedFieldTypes,
        cardNumber: number,
        expectedContent: string,
    ) => {
        const nestedGrid = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await pageModel.waitForFinishLoading();
        const card = new CardObject({ parent: nestedGrid, cardNumber });
        await utils.waitForElementToExist({ name: `"${identifier}" nested field`, selector: card.cssSelector });
        await card.scrollTo();
        const field = new CardNestedFieldObject({ parent: card, identifier, lookupStrategy, fieldType });
        await utils.waitForElementToBeFound({ name: `"${identifier}" nested field`, selector: field.cssSelector });
        await field.loseFocus();
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedContent);
        await field.expectTextContent({
            toBe: storeValue,
            ignoreCase: false,
            cssSelector: fieldType === 'label' ? 'span span' : 'span',
        });
    },
);

When(
    /^the user stores the value of the "([^"\n\r]*)" (bound|labelled) nested (date|label|numeric|reference|select|text|link) field of the card (\d+) in the nested grid field with the key "(.*)"$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.NestedFieldTypes,
        cardNumber: number,
        storeKey: string,
    ) => {
        const nestedGrid = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await pageModel.waitForFinishLoading();
        const card = new CardObject({ parent: nestedGrid, cardNumber });
        await (
            await $(card.cssSelector)
        ).waitForExist({
            timeout: utils.atpEnv.timeoutWaitFor,
            timeoutMsg: `Expected card "${cardNumber}" could not be found.\nSelector: ${card.cssSelector})`,
        });
        await card.scrollTo();
        const field = new CardNestedFieldObject({ parent: card, identifier, lookupStrategy, fieldType });
        const element = await field.waitForDisplayedAndGetElement({
            ignoreContext: true,
            selector: field.cssSelector,
            timeout: utils.atpEnv.timeoutWaitFor,
            timeoutMsg: selector =>
                `Expected element could not be found: "${identifier}" nested field and card number ${cardNumber}.\nSelector: ${selector}`,
        });
        const elementValue = await element.getText();
        StaticStore.storeObject(storeKey, elementValue);
        await utils.takeScreenshot();
        // await field.loseFocus();
    },
);

When(
    /^the user clicks the "([^"\n\r]*)" dropdown action of the card (\d+) in the nested grid field$/,
    async (actionName: string, cardNumber: number) => {
        const nestedGrid = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await pageModel.waitForFinishLoading();
        const card = new CardObject({ parent: nestedGrid, cardNumber });
        await card.clickDropdownAction(actionName);
        await waitForPromises(500, 'dropdown action selection');
    },
);

Then(
    /^the "([^"\n\r]*)" dropdown action of the card (\d+) in the nested grid field is (enabled|disabled)$/,
    async (actionName: string, cardNumber: any, expectedState: utils.EnabledState) => {
        const nestedGrid = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await pageModel.waitForFinishLoading();
        const card = new CardObject({ parent: nestedGrid, cardNumber });
        await card.expectDropdownActionToBeEnabled(actionName, expectedState);
    },
);

When(
    /^the "([^"\n\r]*)" (bound|labelled) nested image field of the card (\d+) in the nested grid field is (defined|undefined)$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        cardNumber: number,
        status: 'defined' | 'undefined',
    ) => {
        const nestedGrid = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await pageModel.waitForFinishLoading();
        const card = new CardObject({ parent: nestedGrid, cardNumber });
        await (
            await $(card.cssSelector)
        ).waitForExist({
            timeout: utils.atpEnv.timeoutWaitFor,
            timeoutMsg: `Expected card "${cardNumber}" could not be found.\nSelector: ${card.cssSelector})`,
        });
        const field = new CardNestedFieldObject({
            parent: card,
            identifier,
            lookupStrategy,
            fieldType: utils.nestedFieldTypes.image,
        });
        await field.loseFocus();
        await expectImageStatus({ selector: field.cssSelector, identifier, status });
    },
);

When(/^the user clicks the selected row of the nested grid field$/, async () => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await pageModel.waitForFinishLoading();
    await field.clickOnSelectedRow();
});

When(/^the user adds a new row to the nested grid field$/, async () => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await field.clickAddRowButton();
});

When(
    /^the user clicks the "(.*)" (bound|labelled) action of the nested grid field$/,
    async (actionId: string, actionLookupStrategy: utils.LookupStrategy) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await field.clickFieldAction(actionLookupStrategy, actionId);
    },
);

When(
    /^the user clicks the "(.*)" (bound|labelled) column of the nested grid field$/,
    async (columnName: string, columnLookupStrategy: utils.LookupStrategy) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        const header = await field.filter.getHeader(columnName, columnLookupStrategy);
        try {
            await (await header.$('div')).click();
            await waitForPromises(500, 'option click');
        } catch (error) {
            throw new Error(`Expected element could not be found: ${columnName}\nSelector: ${field.cssSelector}`);
        }
    },
);

When(
    /^the user filters the "([^"]+)" (bound|labelled) column in the nested grid field with value "([^"]+)"$/,
    async (column: string, lookupStrategy: utils.LookupStrategy, value: string) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        await field.filter.filterColumn({ id: column, lookupStrategy, value: storeValue });
        await waitForPromises(500, 'Filtering nested grid');
    },
);

When(
    /^the user filters the "([^"]+)" (bound|labelled) column in the nested grid field with filter "([^"]+)" and value "([^"]+)"$/,
    async (column: string, lookupStrategy: utils.LookupStrategy, filter: string, value: string) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        await field.filter.filterColumn({ id: column, lookupStrategy, value: storeValue, filter });
        await waitForPromises(500, 'Filtering nested grid');
    },
);

When(
    /^the user filters the "([^"\n\r]*)" (bound|labelled) column in the nested grid field with filter type "(.*)"$/,
    async (column: string, lookupStrategy: utils.LookupStrategy, filter: string) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await field.filter.filterColumn({ id: column, lookupStrategy, filter });
        await waitForPromises(500, 'Filtering table');
    },
);

When(
    /^the user (opens|closes) the filter of the "(.*)" (bound|labelled) column in the nested grid field$/,
    async (state: 'opens' | 'closes', columnName: string, lookupStrategy: utils.LookupStrategy) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        if (state === 'opens') {
            await field.filter.openFilter(columnName, lookupStrategy);
        } else {
            await field.filter.closeFilter();
        }
    },
);

Then(/^the user searches "(.*)" in the filter of the nested grid field$/, async (searchValue: string) => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(searchValue);
    await field.filter.searchFilter(storeValue);
    await waitForPromises(500, 'Filtering table');
});

Then(
    /^the user (ticks|unticks) the item with text "(.*)" in the filter of the nested grid field$/,
    async (tickAction: utils.TickStateType, itemText: string) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(itemText);
        await field.filter.tickFilterItem(storeValue, tickAction);
        await waitForPromises(500, 'Filtering table');
    },
);

Then(/^the title of the nested grid field is "(.*)"$/, async (title: string) => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await field.loseFocus();
    await field.expectTitle(title);
});

Then(/^the title of the nested grid field is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await field.loseFocus();
    await field.expectTitleToBeDisplayed(cssState === 'hidden');
});

Then(/^the helper text of the nested grid field is "(.*)"$/, async (value: string) => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await field.loseFocus();
    await field.expectHelperText(value);
});

Then(/^the helper text of the nested grid field is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await field.loseFocusForHelperText();
    await field.expectHelperTextToBeDisplayed(cssState === 'hidden');
});

Then(/^the selected nested grid field is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await field.loseFocus();
    await field.expectToBeDisplayed(field.cssSelector, cssState === 'hidden');
});

When(
    /^the user selects the floating row of the selected nested grid field at level (\d+)(?: with parent id (\d+))?$/,
    (level: number, parentId?: number) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        field.selectFloatingRow({ level, parentId });
    },
);

When(
    /^the user clicks the (next|previous) page button of the selected nested grid field at level (\d+)(?: with parent id (\d+))?$/,
    async (page: 'next' | 'previous', level: number, parentId?: number) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await field.stepToPage({ level, parentId, page });
    },
);

When(
    /^the (First|Next|Previous|Last) page button of the nested grid field is (enabled|disabled) at level (\d+)(?: with parent id (\d+))$/,
    async (buttonName: utils.TableButton, cssState: 'enabled' | 'disabled', level: number, parentId?: number) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await field.expectPageButtonToBeEnabledAtLevel({ level, parentId, buttonName }, cssState === 'disabled');
    },
);

When(
    /^the user scrolls down to the row with text "([^"\n\r]*)" and column header "([^"\n\r]*)" in the nested grid field$/,
    async (rowText: string, headerName: string) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await field.scrollToRowByText(rowText, headerName);
    },
);

Then(
    /^the page number of the nested grid field is "(.*)" at level (\d+)(?: with parent id (\d+))$/,
    async (expectedPageNumber: string, level: number, parentId?: number) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await field.expectPageNumberToBeAtLevel({ level, parentId, expectedPageNumber });
    },
);

Then(
    /^the summary row paging information of the nested grid field is "(.*)" at level (\d+)(?: with parent id (\d+))$/,
    async (expectedSummaryRow: string, level: number, parentId?: number) => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await field.expectSummaryRowToBeAtLevel({ level, parentId, expectedSummaryRow });
    },
);

Then(
    /^the (First|Next|Previous|Last) page button of the nested grid field is (enabled|disabled)$/,
    async (buttonName: utils.TableButton, cssState: 'enabled' | 'disabled') => {
        const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await field.expectPageButtonToBeEnabled(buttonName, cssState === 'disabled');
    },
);

Then(/^the page number of the nested grid field is "(.*)"$/, async (expectedPageNumber: string) => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await field.expectPageNumberToBe(expectedPageNumber);
});

Then(/^the summary row paging information of the nested grid field is "(.*)"$/, async (expectedSummaryRow: string) => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await field.expectSummaryRowToBe(expectedSummaryRow);
});

Then(/^the option menu of the nested grid field is displayed$/, async () => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await field.expectOptionMenuDisplayed();
});

Then(/^the user clicks the option menu of the nested grid field$/, async () => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await field.clickOnOptionMenu();
});

Then(/^the user clicks the "(.*)" value in the option menu of the nested grid field$/, async (input: string) => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    await field.selectOptionMenuInput(input);
});
