import { Then, When } from '@cucumber/cucumber';
import { runtimeParameters } from '../../../../../../../parameters';
import { getDataTestIdSelector, waitForElementToBeFound } from '../../../step-definitions-utils';
import { waitForPromises } from '../../field/wait-util';
import pageModel from '../../main-page';
import { DialogDisplayTypes, DialogObject } from '../dialog/dialog-object';
import { NavigationBarObject } from './navigation-bar-object';

const getNavigationBar = (): NavigationBarObject => {
    return new NavigationBarObject();
};

// Currently not usable in Production. Usable with xtrem-x3.
When(/^the user clicks the "(.*)" sticker in the navigation bar$/, async (icon: string) => {
    await pageModel.waitForFinishLoading();
    const navigation = getNavigationBar();
    await navigation.clickOnIcon(icon);
});

When(/^the user sets the userName "(.*)" in the navigation bar$/, async (userName: string) => {
    await pageModel.waitForFinishLoading();
    const selection = runtimeParameters.getStringOrParameter(userName);
    const navigation = getNavigationBar();
    await navigation.writeUserName(selection);
});

When(/^the user selects the "(.*)" endpoint$/, async (endPointName: string) => {
    await pageModel.waitForFinishLoading();
    const selection = runtimeParameters.getStringOrParameter(endPointName);
    const navigation = getNavigationBar();
    await navigation.selectEndPoint(selection);
});

Then(/^the "(.*)" endpoint is selected$/, async (endPointName: string) => {
    await pageModel.waitForFinishLoading();
    const selection = runtimeParameters.getStringOrParameter(endPointName);
    const navigation = getNavigationBar();
    await navigation.expectEndpointToBeSelected(selection);
});

When(/^the user clicks the (close|arrow left) button in the navigation bar$/, async (button: string) => {
    await pageModel.waitForFinishLoading();
    const navigation = getNavigationBar();
    await navigation.clickNavigationButton(button);
});

When(/^the (?:site|site and depositor) in the navigation bar is "(.*)"$/, async (value: string) => {
    await pageModel.waitForFinishLoading();
    const navigation = getNavigationBar();
    await navigation.expectValue({
        toBe: value,
        selectorToUse: '.se-header-secondary-block',
    });
});

Then(/^the help center icon is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    await pageModel.waitForFinishLoading();
    const navigation = getNavigationBar();
    await navigation.expectHelpCenterIconIsDisplayed(cssState === 'hidden');
});

Then(
    /^the help center "(.*)" resource is (displayed|hidden)$/,
    async (resource: string, cssState: 'displayed' | 'hidden') => {
        await pageModel.waitForFinishLoading();
        const navigation = getNavigationBar();
        await navigation.expectHelpCenterResourceIsDisplayed(resource, cssState === 'hidden');
    },
);

Then(/^the help center page contains "(.*)"$/, async (expectedText: string) => {
    await pageModel.waitForFinishLoading();
    await pageModel.verifyPageContainsText(expectedText, 'body');
});

Then(/^the help center page contains$/, async (expectedText: string) => {
    await pageModel.waitForFinishLoading();
    await pageModel.verifyPageContainsText(expectedText, 'body');
});

Then(/^the user opens the help center "(.*)" resource$/, async (resource: string) => {
    await pageModel.waitForFinishLoading();
    const navigation = getNavigationBar();
    await navigation.openHelpCenterResource(resource);
});

When(/^the user opens the help center$/, async () => {
    await pageModel.waitForFinishLoading();
    const navigation = getNavigationBar();
    await navigation.openHelpCenter();
});

When(/^the user closes the help center$/, async () => {
    await pageModel.waitForFinishLoading();
    const navigation = getNavigationBar();
    await navigation.closeHelpCenter();
});

When(/^the user switches to the (first|last) opened tab$/, async (tabPosition: 'first' | 'last') => {
    await pageModel.waitForFinishLoading();
    await pageModel.switchToNewTab(tabPosition);
    await pageModel.waitForFinishLoading();
});

When(/^the user opens the persona$/, async () => {
    const personnaBtn = `${getDataTestIdSelector({ domSelector: 'button', dataTestIdValue: 'DemoPersonaSticker' })} [data-element="person"][type="person"]`;
    await waitForElementToBeFound({ name: 'Personna button', selector: personnaBtn });
    const $personnaBtn = await $(personnaBtn);
    await $personnaBtn.moveTo();
    await $personnaBtn.click();
    await waitForPromises(500, 'wait for the personna to be opened');
    const dialog = new DialogObject(DialogDisplayTypes.mainpage);
    await dialog.expectDialogToAppear('info');
    await dialog.expectTitleDisplayed('Persona');
});
