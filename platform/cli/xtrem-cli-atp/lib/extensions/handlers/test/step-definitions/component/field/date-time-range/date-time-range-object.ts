import { Key } from 'webdriverio';
import * as utils from '../../../step-definitions-utils';
import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { isMobileDevice } from '../../test-globals';
import { getUserLocale } from '../date-utils';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

const SELECTORS = {
    datePickerContext: (startOrEnd: 'start' | 'end') =>
        utils.getDataTestIdSelector({
            domSelector: 'div',
            dataTestIdValue: `e-datetime-input-${startOrEnd}-date-picker`,
        }),
    dateTimeInput: (startOrEnd: 'start' | 'end') =>
        utils.getDataTestIdSelector({
            domSelector: 'input',
            dataTestIdValue: `e-datetime-input-${startOrEnd}`,
        }),
};

export class DateTimeFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'datetime-range', identifier, lookupStrategy, context });
    }

    async openDateRangePopup(identifier: string): Promise<void> {
        const dateFieldSelector = utils.getDataTestIdSelector({
            domSelector: 'input',
            dataTestIdValue: `e-datetime-input-${identifier}`,
        });

        const popupSelector = utils.getDataTestIdSelector({
            domSelector: 'div',
            dataTestIdValue: `e-datetime-input-${identifier}-date-picker`,
        });

        try {
            const dateTimeElement = await this.get();
            const dateFieldElement = await $(dateFieldSelector);
            const popupElement = await $(popupSelector);

            await dateTimeElement.scrollIntoView();
            const isPopupVisible = await popupElement.isDisplayedInViewport();

            if (isPopupVisible) {
                await utils.scrollIntoViewViaJS(`${popupElement.selector}`);
                await waitForPromises(500, 'Waiting after popup already visible');
                return;
            }
            await utils.waitForElementToBeDisplayed({ name: 'date field element', selector: dateFieldSelector });
            await utils.safeClick(dateTimeElement);
            await utils.scrollIntoViewViaJS(`${popupElement.selector}`);
            await utils.waitForElementToBeDisplayed({ name: 'date field element', selector: dateFieldSelector });
            await utils.safeClick(dateFieldElement);
            await waitForPromises(500, 'Waiting after opening popup');
        } catch (error) {
            throw new Error(
                `Expected element could not be found: "${identifier}".\n` +
                    `Selector: ${dateFieldSelector}\nError: ${error.message}`,
            );
        }
    }

    async selectDateRangeMonth(identifier: string, targetMonth: string): Promise<void> {
        await this.openDateRangePopup(identifier);

        const datePickerSelector = utils.getDataTestIdSelector({
            domSelector: 'div',
            dataTestIdValue: `e-datetime-input-${identifier}-date-picker`,
        });

        const monthSelector = await $(`${datePickerSelector} select.e-month-selector`);

        const monthNames = [
            'january',
            'february',
            'march',
            'april',
            'may',
            'june',
            'july',
            'august',
            'september',
            'october',
            'november',
            'december',
        ];

        const monthIndex = monthNames.indexOf(targetMonth.toLocaleLowerCase());
        try {
            if (monthIndex === -1) {
                throw new Error(`Invalid month: "${targetMonth}". Month not found in the list.`);
            }
            await monthSelector.waitForExist();
            await monthSelector.waitForDisplayed();
            await monthSelector.scrollIntoView();
            // Using jsClick to avoid issues with mobile devices where click might not work as expected
            // await monthSelector.click();
            await this.jsClick({
                cssSelector: monthSelector.selector.toString(),
                ignoreContext: false,
                skipVisibilityCheck: true,
            });
            const monthValue = await $$(`${monthSelector.selector} option`);
            // eslint-disable-next-line no-restricted-syntax
            for (const option of monthValue) {
                const cellText = await option.getAttribute('value');
                if (cellText === monthIndex.toString()) {
                    await option.click();
                    break;
                }
            }
            await waitForPromises(500, 'Waiting for month actions');
        } catch (error) {
            throw new Error(
                `Expected option to be selected could not be found: "${targetMonth}". \nSelector: ${monthSelector.selector}\nError: ${error.message}`,
            );
        }
    }

    async selectDateRangeYear(startOrEnd: 'start' | 'end', targetYear: string): Promise<void> {
        await this.openDateRangePopup(startOrEnd);

        const regex = /^\d{4}$/;
        const isValidYear = regex.test(targetYear);
        const errorMessage = `Expected option to be selected could not be found: "${targetYear}".\nSelector: ${this.cssSelector}`;

        if (!isValidYear) throw new Error(errorMessage);

        const yearSelector = await $(`${SELECTORS.datePickerContext(startOrEnd)} select.e-year-selector`);

        let found;

        try {
            await utils.waitForElementToBeDisplayed({
                name: 'Year selector',
                selector: yearSelector.selector.toString(),
            });
            await yearSelector.scrollIntoView();
            // Using jsClick to avoid issues with mobile devices where click might not work as expected
            // await yearSelector.click();
            await this.jsClick({
                cssSelector: yearSelector.selector.toString(),
                ignoreContext: false,
                skipVisibilityCheck: true,
            });
            const yearValue = await $$(`${yearSelector.selector} option`);

            // eslint-disable-next-line no-restricted-syntax
            for (const option of yearValue) {
                const cellText = await option.getAttribute('value');
                if (cellText === targetYear) {
                    await option.click();
                    await waitForPromises(500, 'Waiting for click');
                    found = true;
                    return;
                }
            }
            if (!found) throw new Error(errorMessage);
        } catch (error) {
            throw new Error(errorMessage);
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async selectDateRangeDay(startOrEnd: 'start' | 'end', targetDay: string): Promise<void> {
        const dayNumber = Number(targetDay);
        const options = await $$(
            `${SELECTORS.datePickerContext(startOrEnd)} .react-aria-CalendarGridBody .react-aria-CalendarCell`,
        );

        try {
            // eslint-disable-next-line no-restricted-globals
            if (isNaN(dayNumber) || dayNumber < 1 || dayNumber > 31) {
                throw new Error(`Invalid day: "${dayNumber}". Must be between 1 and 31.`);
            }

            // eslint-disable-next-line no-restricted-syntax
            for (const option of options) {
                const cellText = await option.getText();
                if (cellText === targetDay) {
                    await utils.waitForElementToBeDisplayed({
                        name: 'Date cell',
                        selector: option.selector.toString(),
                    });
                    await option.scrollIntoView();
                    await browser.execute(el => el.click(), option);
                    await waitForPromises(500, 'Waiting for date cell to be clicked');
                    return;
                }
            }
        } catch (error) {
            throw new Error(
                `Expected element "${targetDay}" could not be found.\nSelector: ${options[0].selector.toString()}`,
            );
        }
    }

    async selectDateRangeValue(startOrEnd: 'start' | 'end', expectedValue: string): Promise<void> {
        const dateRangeSelector = await this.find(SELECTORS.dateTimeInput(startOrEnd));
        await utils.waitForElementToBeDisplayed({
            name: 'Date range input',
            selector: dateRangeSelector.selector.toString(),
        });
        await dateRangeSelector.scrollIntoView();
        const actualValue = await dateRangeSelector.getAttribute('value');
        if (actualValue.toLocaleLowerCase() !== expectedValue.toLocaleLowerCase()) {
            throw new Error(
                `Expected value: "${expectedValue}", actual: "${actualValue}".\nSelector: ${dateRangeSelector.selector.toString()}`,
            );
        }
    }

    async selectDateRangeCheckbox({
        startOrEnd,
        targetString,
        checkOrUncheck,
    }: {
        startOrEnd: 'start' | 'end';
        targetString: string;
        checkOrUncheck: 'ticks' | 'unticks';
    }): Promise<void> {
        await this.openDateRangePopup(startOrEnd);

        const checkboxSelector = utils.getDataTestIdSelector({
            domSelector: 'input',
            dataTestIdValue: `e-datetime-input-${startOrEnd}-date-picker-checkbox`,
        });

        const checkboxLabelSelector = `//label[text()="${targetString}"]`;
        const checkboxLabel = await $(checkboxLabelSelector);

        try {
            await utils.waitForElementToExist({
                name: 'Checkbox label',
                selector: checkboxLabel.selector.toString(),
            });
            const checkboxElement = await $(`${checkboxSelector}`);
            await utils.waitForElementToExist({
                name: 'Checkbox element',
                selector: checkboxElement.selector.toString(),
            });
            const checkboxValue = await checkboxElement.getProperty('checked');
            const shouldSelect = checkOrUncheck === 'ticks';
            const isSelected = checkboxValue;

            if ((shouldSelect && !isSelected) || (!shouldSelect && isSelected)) {
                // scrollIntoView causes issues for mobile device view, it is not needed
                if (!isMobileDevice()) await checkboxElement.scrollIntoView();
                await checkboxElement.click();
                await $(this.cssSelector).scrollIntoView();
                await waitForPromises(300, 'Waiting for checkbox click');
            }
        } catch (error) {
            throw new Error(
                `Expected element could not be found: "${targetString}". \nSelector: ${this.cssSelector}${checkboxLabelSelector}`,
            );
        }
    }

    async toggleDateRangeButton(startOrEnd: 'start' | 'end', toggleButton: 'AM' | 'PM'): Promise<void> {
        await this.openDateRangePopup(startOrEnd);

        const parentElement = `div[id*="bind-"][id*="-${startOrEnd}"] .e-time-field`;
        const selectorToUse = utils.getDataComponentSelector('div', 'button-toggle-group');

        const $toggleButton = await $(`${parentElement} ${selectorToUse} button[aria-label="${toggleButton}"]`);

        const errorMessage = `Expected option could not be found: "${toggleButton}". \nSelector: ${parentElement} ${selectorToUse}`;
        if (!(await $toggleButton.isExisting())) throw new Error(errorMessage);

        // Check if the button is enabled
        await this.timeState($toggleButton.selector.toString(), toggleButton);

        await browser.waitUntil(
            async () => {
                await $toggleButton.click();
                await waitForPromises(300, 'Waiting for popup to open');
                const isPressed = (await $toggleButton.getAttribute('aria-pressed')) === 'true';
                return isPressed;
            },
            {
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected element to be displayed: "${toggleButton}". \nSelector: ${$toggleButton.selector.toString()}`,
            },
        );
    }

    async loseFocusDateRange(startOrEnd: 'start' | 'end'): Promise<void> {
        await this.openDateRangePopup(startOrEnd);

        const backgroundElement = await $(this.cssSelector);
        await backgroundElement.click();
    }

    // eslint-disable-next-line class-methods-use-this
    async timeState(selector: string, identifier?: string): Promise<void> {
        const isDisabled = await $(selector).getProperty('disabled');

        if (isDisabled) {
            const label = identifier ? ` "${identifier}" ` : ' ';
            throw new Error(`Expected element${label}to be enabled.\nSelector: ${selector}`);
        }
    }

    async timeZoneDateRangeButton(startOrEnd: 'start' | 'end'): Promise<void> {
        await this.openDateRangePopup(startOrEnd);
        // await waitForPromises(500, 'Waiting for popup to open');
        const timeZoneOpen = utils.getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: `e-time-component-open-timezone-${startOrEnd}`,
        });

        const timeZoneClose = utils.getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: `e-time-component-close-timezone-${startOrEnd}`,
        });

        const buttonSelectors = [`${timeZoneOpen}`, `${timeZoneClose}`];
        await this.timeState(`${buttonSelectors}`);

        // eslint-disable-next-line no-restricted-syntax
        for (const selector of buttonSelectors) {
            const fullSelector = `div.e-time-field ${selector}`;
            const button = await $(fullSelector);

            if (await button.isExisting()) {
                await button.scrollIntoView();
                await browser.execute(btn => btn.click(), button);
                await waitForPromises(300, 'Waiting after clicking timezone button');
                return;
            }
        }

        throw new Error(
            `Expected element could not be found: "${startOrEnd}". \nSelector: ${buttonSelectors.join(', ')}`,
        );
    }

    // eslint-disable-next-line class-methods-use-this
    async timeZoneExpectedValue(startOrEnd: 'start' | 'end', expectedValue: string): Promise<boolean> {
        const selector = utils.getDataTestIdSelector({
            domSelector: 'input',
            dataTestIdValue: `e-datetime-input-${startOrEnd}-time-input-timezone`,
        });

        try {
            const timeZoneElement = await $(selector);
            await utils.waitForElementToExist({
                name: 'Time zone input',
                selector: timeZoneElement.selector.toString(),
            });
            const actualValue = await timeZoneElement.getAttribute('value');

            if (actualValue !== expectedValue) {
                throw new Error(
                    `Expected value: "${expectedValue}", actual: "${actualValue}".\nSelector: ${timeZoneElement.selector}`,
                );
            }

            return true;
        } catch (error) {
            if (error.message.includes('Expected value:')) {
                throw error; // rethrow value mismatch
            }

            // Catch all other errors, such as selector failure
            throw new Error(`Expected element could not be found. \nSelector: ${selector}\n${error.message}`);
        }
    }

    async setHoursMin(startOrEnd: 'start' | 'end', targetTime: string) {
        await this.openDateRangePopup(startOrEnd);
        const [hours, minutes] = await this.extractTime(targetTime);
        await this.setTimeField({ startOrEnd, field: 'hours', value: hours });
        await this.setTimeField({ startOrEnd, field: 'minutes', value: minutes });
        await waitForPromises(500, 'Waiting for set time');
    }

    // eslint-disable-next-line class-methods-use-this
    private async extractTime(targetDate: string): Promise<[number, number]> {
        const regex = /(\d{1,2})[,:.](\d{2})/;
        const match = targetDate.match(regex);
        const lang = await getUserLocale();

        if (!match) {
            throw new Error('Invalid time format. Expected HH:MM');
        }

        const hours = parseInt(match[1], 10);
        const minutes = parseInt(match[2], 10);

        const isEnUS = lang === 'en-US';
        // eslint-disable-next-line no-restricted-globals
        const isHourInvalid = isNaN(hours) || (isEnUS ? hours < 1 || hours > 12 : hours < 0 || hours > 23);

        if (isHourInvalid) {
            const hourRange = isEnUS ? '1 and 12' : '0 and 23';
            throw new Error(`Invalid hour: "${hours}". Must be between ${hourRange}.`);
        }

        // eslint-disable-next-line no-restricted-globals
        if (isNaN(minutes) || minutes < 0 || minutes > 59) {
            throw new Error(`Invalid minutes: "${minutes}". Must be between 0 and 59.`);
        }

        return [hours, minutes];
    }

    private async setTimeField({
        startOrEnd,
        field,
        value,
    }: {
        startOrEnd: 'start' | 'end';
        field: 'hours' | 'minutes';
        value: number;
    }) {
        const inputField = await $(
            utils.getDataTestIdSelector({
                domSelector: 'input',
                dataTestIdValue: `e-datetime-input-${startOrEnd}-time-input-${field}`,
            }),
        );
        await this.timeState(inputField.selector.toString());
        await utils.waitForElementToBeDisplayed({
            name: 'Time input field',
            selector: inputField.selector.toString(),
        });
        await inputField.moveTo();
        await browser.execute(el => el.click(), inputField);
        await waitForPromises(500, 'Waiting for click');
        await inputField.clearValue();
        await this.write({
            content: value.toString(),
            cssSelector: inputField.selector.toString(),
            ignoreContext: true,
        }); // e.g., "08"
        await browser.keys(Key.Tab);
    }

    async isMandatory() {
        const selectorToUse = utils.getDataTestIdSelector({
            domSelector: 'label',
            dataTestIdValue: `e-field-label`,
        });
        const isMandatoryElement = await $(`${this.cssSelector} ${selectorToUse}`);

        if (!(await isMandatoryElement.isExisting()) || !(await isMandatoryElement.getText()).includes('*')) {
            throw new Error(` Expected element to be mandatory.\nSelector: ${this.cssSelector} ${selectorToUse}`);
        }
    }

    async timezoneState(identifier: 'start', state: 'displayed' | 'hidden') {
        const expectedState = state === 'displayed';
        await waitForPromises(500, 'Waiting for popup to open');
        await this.openDateRangePopup(identifier);
        const timeZoneOpen = utils.getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: `e-time-component-open-timezone-${identifier}`,
        });

        const buttonSelectors = await $(timeZoneOpen);
        const parentElement = $(`div[id="--DatetimeRange-bind-field-${identifier}"] .e-time-field`);
        await parentElement.scrollIntoView();
        const isDisplayed = await buttonSelectors.isExisting();
        if ((isDisplayed && !expectedState) || (!isDisplayed && expectedState)) {
            throw new Error(` Expected element to be"${state}".\nSelector: ${buttonSelectors.selector}`);
        }
    }
}
