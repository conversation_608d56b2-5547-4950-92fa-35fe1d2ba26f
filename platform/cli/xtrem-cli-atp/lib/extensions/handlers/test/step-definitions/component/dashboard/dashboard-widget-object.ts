/* eslint-disable class-methods-use-this */
import { expect } from 'chai';
import { camelCase, startCase } from 'lodash';
import {
    getDataTestIdSelector,
    takeScreenshot,
    waitForElementToBeDisplayed,
    waitForElementToBeFound,
} from '../../step-definitions-utils';
import AbstractPageObject from '../abstract-page-object';
import { scroll } from '../field/table/tableUtils';
import { waitForPromises } from '../field/wait-util';
import * as StaticStore from '../static-store';

export enum WidgetType {
    BASIC = 'basic',
    BAR_CHART = 'bar-chart',
    GAUGE = 'gauge',
    LINE_CHART = 'line-chart',
    TABLE = 'table',
    TILE_INDICATOR = 'tile-indicator',
    TILE_GROUP_INDICATOR = 'tile-group-indicator',
    VISUAL_PROCESS = 'visual-process',
    CONTACT_CARD = 'contact-card',
    PIE_CHART = 'pie-chart',
}

export enum Alignment {
    LEFT = 'left',
    RIGHT = 'right',
}

export const getWidgetTypeSelector = (Type: WidgetType) => {
    switch (Type) {
        case WidgetType.BASIC:
            return 'basic';
        case WidgetType.BAR_CHART:
            return 'bar-chart';
        case WidgetType.GAUGE:
            return 'gauge';
        case WidgetType.LINE_CHART:
            return 'line-chart';
        case WidgetType.TABLE:
            return 'table';
        case WidgetType.TILE_INDICATOR:
            return 'simple-indicator';
        case WidgetType.TILE_GROUP_INDICATOR:
            return 'tile-group-indicator';
        case WidgetType.VISUAL_PROCESS:
            return 'visual-process';
        case WidgetType.CONTACT_CARD:
            return 'contact-card';
        case WidgetType.PIE_CHART:
            return 'pie-chart';
        default:
            throw new Error(`Invalid widget type: ${Type}`);
    }
};

const widgetContext = (context: 'dashboard' | 'dashboard editor' | '') =>
    context === 'dashboard editor' ? '.e-dashboard-editor' : 'div[id="root"] .e-dashboard';
const widgetContainer = (name: string) =>
    getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: `db-widget-container-${camelCase(name)}` });
const widgetType = (type: WidgetType) => `.db-widget-type-${getWidgetTypeSelector(type)}`;

export class DashboardWidget extends AbstractPageObject {
    public context: 'dashboard' | 'dashboard editor' | '';

    public type: WidgetType;

    public name: string;

    constructor({
        context,
        type,
        name,
    }: {
        context: 'dashboard' | 'dashboard editor' | '';
        type: WidgetType;
        name: string;
    }) {
        super(`${widgetContext(context)} ${widgetContainer(name)} ${widgetType(type)}`);
        this.name = name;
    }

    getContainerSelector = async () => {
        const widget = await this.get();
        await widget.waitForDisplayed({
            timeoutMsg: `Expected element could not be found: "${this.name}" ${this.type} widget.\nSelector: ${this.cssSelector}`,
            timeout: this.timeoutWaitFor,
        });
        const containerSelector = this.cssSelector.replace(/\.db-widget-type.*/, '').trimEnd();
        const widgetAttr = await widget.getAttribute('data-testid');
        const containerID = widgetAttr.match(/\d+/) ? widgetAttr.match(/\d+/)![0] : null;
        return `${containerSelector}[id="${containerID}"]`;
    };

    async expectWidgetValueToContain({
        expectedValue,
        valueType,
        selectorToUse = '',
    }: {
        expectedValue: string;
        valueType: 'text' | 'html' | 'value';
        selectorToUse?: string;
    }) {
        let element;
        if (selectorToUse.length > 0) {
            element = await this.find(selectorToUse);
        } else {
            element = await this.get();
        }
        let actualValue = '';
        switch (valueType) {
            case 'text':
                actualValue = await element.getText();
                break;
            case 'html':
                actualValue = await element.getHTML(false);
                break;
            case 'value':
                actualValue = await element.getValue();
                break;
            default:
                actualValue = await element.getText();
                break;
        }
        const widgetText = actualValue.replace(/\s+/g, '').trim();
        const expectedText = expectedValue.replace(/\s+/g, '').trim();

        try {
            expect(widgetText).to.contain(expectedText);
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(
                `Expected value: "${expectedValue}" Actual: "${widgetText}"\nSelector: ${element.selector}`,
            );
        }
    }

    async storeWidget() {
        const element = await this.get();
        if (this.context === 'dashboard') {
            try {
                await browser.waitUntil(
                    async () => {
                        const dashBoardEditor = await this.find('.e-dashboard-editor', true);
                        const isNotOpen =
                            !(await dashBoardEditor.isExisting()) && !(await dashBoardEditor.isDisplayed());
                        return isNotOpen;
                    },
                    { timeout: this.timeoutWaitFor },
                );
            } catch (error) {
                await browser.takeScreenshot();
                throw new Error('Expected dashboard editor not to be open.');
            }
        }
        await element.waitForDisplayed({
            timeoutMsg: `Expected element could not be found: "${this.name}" ${this.type} widget. \nSelector: ${this.cssSelector}`,
            timeout: this.timeoutWaitFor,
        });
        await this.scrollTo();
        StaticStore.storeObject(StaticStore.StoredKeys.DASHBOARD_WIDGET, this);
    }

    async openHeaderActionMenu(type: WidgetType) {
        const containerSelector = await this.getContainerSelector();
        let selectorToUse = '';
        switch (type) {
            case WidgetType.BASIC:
            case WidgetType.BAR_CHART:
            case WidgetType.GAUGE:
            case WidgetType.LINE_CHART:
            case WidgetType.TABLE:
            case WidgetType.VISUAL_PROCESS:
                selectorToUse = `${containerSelector} div[data-testid^="db-widget-container-dropdown-actions-"]`;
                break;
            case WidgetType.TILE_INDICATOR:
                selectorToUse = `${containerSelector} div[data-component="action-popover-wrapper"]`;
                break;
            case WidgetType.CONTACT_CARD:
                selectorToUse = `${containerSelector} div[data-testid^="db-widget-type-contact-card"]`;
                break;
            default:
                break;
        }

        const element = await $(selectorToUse);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${this.name}" ${this.type} widget. \nSelector: ${this.cssSelector}`,
        });

        await this.jsClick({ cssSelector: selectorToUse, ignoreContext: true, skipVisibilityCheck: true });
        await waitForPromises(900, 'Wait for click to be processed');
    }

    async clickButtonOnHeader(btnName: string) {
        const selector = getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: `db-widget-action-item-${camelCase(btnName)}`,
        });
        const headerBtn = await $(selector);
        await headerBtn.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${btnName}" more actions button. \nSelector: ${headerBtn.selector}`,
        });
        await headerBtn.click();
    }

    async expectActionButtonToBeDisplayed(name: string, reverse: boolean = false) {
        await waitForElementToBeDisplayed({ name: 'Action popover', selector: '[data-component="action-popover"]' });
        const selector = getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: `db-widget-action-item-${camelCase(name)}`,
        });
        await this.expectToBeDisplayed(selector, reverse);
    }

    async expectActionButtonToBeEnabled(name: string, reverse: boolean = false) {
        await waitForElementToBeDisplayed({ name: 'Action popover', selector: '[data-component="action-popover"]' });
        const selector = getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: `db-widget-action-item-${camelCase(name)}`,
        });
        const element = await $(selector);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${name}" more actions button. \nSelector: ${selector}`,
        });
        await this.expectToBeEnabled(selector, reverse);
    }

    async clickButtonOnFooter(btnName: string) {
        const containerSelector = await this.getContainerSelector();
        const element = await $(
            `${containerSelector} ${getDataTestIdSelector({
                domSelector: 'button',
                dataTestIdValue: `db-widget-call-to-actions-${camelCase(btnName)}`,
            })}`,
        );
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${btnName}" button. \nSelector: ${element.selector}`,
        });
        await element.click();
    }

    async expectDashboardWidgetButtonToBeDisplayed(name: string, reverse: boolean = false) {
        const selector = getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: `db-widget-call-to-actions-${camelCase(name)}`,
        });
        await this.expectToBeDisplayed(selector, reverse);
    }

    async expectDashboardWidgetButtonEnabled(name: string, reverse: boolean = false) {
        const selector = getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: `db-widget-call-to-actions-${camelCase(name)}`,
        });
        const element = await $(selector);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${name}" button. \nSelector: ${selector}`,
        });
        await this.expectToBeEnabled(selector, reverse);
    }

    async selectCard(rowId: string) {
        const selectedItem = await this.find(`div[data-testid^="db-card-list-row-"]:nth-child(${rowId})`);
        await selectedItem.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: card "${rowId}". \nSelector: ${this.cssSelector} div[data-testid^="db-card-list-row-"]:nth-child(${rowId})`,
        });
        await selectedItem.scrollIntoView();
        await selectedItem.moveTo();
        await selectedItem.waitForClickable(); // fix for XT-85445 using js click
        await browser.execute(`document.querySelector('${selectedItem.selector}').click()`);

        const datatestid = await selectedItem.getAttribute('data-testid');

        StaticStore.storeString(StaticStore.StoredKeys.DASHBOARD_WIDGET_TABLE_CARD, datatestid);
    }

    async tickUntickSelectedCard(state: string) {
        const datatestid = StaticStore.getStoredString(StaticStore.StoredKeys.DASHBOARD_WIDGET_TABLE_CARD);
        const selectorToUse = getDataTestIdSelector({
            domSelector: 'input',
            dataTestIdValue: `db-card-list-select-${datatestid.replace('db-card-list-row-', '')}`,
        });
        const selectedItem = await this.find(selectorToUse);

        await selectedItem.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found. \nSelector: ${selectorToUse} `,
        });

        const select = state === 'ticks';
        const selectionState = await selectedItem.isSelected();
        if ((select && !selectionState) || (!select && selectionState)) {
            await selectedItem.click({ x: 0, y: 0 });
        }
    }

    async clickRowActionsButton(mode: string) {
        let selectedItem;
        if (mode === 'card') {
            const datatestid = StaticStore.getStoredString(StaticStore.StoredKeys.DASHBOARD_WIDGET_TABLE_CARD);
            const selectorToUse = `[data-testid="${datatestid}"] [data-testid="db-card-list-row-actions"] [data-component="action-popover-button"]`;
            selectedItem = await this.find(selectorToUse);
        } else {
            const row: any = StaticStore.getStoredObject(
                StaticStore.StoredKeys.DASHBOARD_WIDGET_TABLE_CARD_SWITCH_CARD_ROW,
            );
            selectedItem = await row.$('[data-testid="db-table-row-actions"] [data-component="action-popover-button"]');
        }

        await selectedItem.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found. \nSelector: ${selectedItem} `,
        });
        await selectedItem.click();
    }

    async clickRowActionsDropdownButton(actionName: string) {
        const popOver = '[data-component="action-popover"]';
        const popOverBtns = await this.findAll(`${popOver} [type="button"]`, true);
        const btnTitles = await Promise.all(popOverBtns.map(btn => btn.getText()));
        const index = btnTitles.findIndex(title => title.toLowerCase() === actionName.toLowerCase());

        if (index !== -1) {
            await popOverBtns[index].click();
        } else {
            throw new Error(
                `Expected dropdown action "${actionName}" could not be found.\nSelector: ${popOverBtns.selector.toString()}`,
            );
        }
    }

    async tickUntickAll(state: string, tableWidgetType: 'table' | 'card') {
        let controlSelector;
        if (tableWidgetType === 'table') {
            controlSelector = 'db-table-table-select-all';
        } else {
            controlSelector = 'db-card-list-select-all';
        }
        const selector = getDataTestIdSelector({ domSelector: 'input', dataTestIdValue: controlSelector });
        const selectedItem = await $(selector);

        await selectedItem.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found. \nSelector: ${selector}`,
        });

        const select = state === 'ticks';
        const selectionState = await selectedItem.isSelected();
        if ((select && !selectionState) || (!select && selectionState)) {
            if (!(await selectedItem.isClickable())) await browser.execute(elem => elem.scrollIntoView(), selectedItem);
            await browser.execute(elem => elem.click({ x: 0, y: 0 }), selectedItem);
        }
    }

    getCorrectSelectorperAlignment = (rowId: string, alignment: Alignment) => {
        let selectedcarddatatestid = StaticStore.getStoredString(StaticStore.StoredKeys.DASHBOARD_WIDGET_TABLE_CARD);
        selectedcarddatatestid = selectedcarddatatestid.replace('db-card-list-row-', '');
        let selector;
        switch (alignment) {
            case Alignment.LEFT:
                if (rowId === '1') {
                    selector = getDataTestIdSelector({
                        domSelector: 'span',
                        dataTestIdValue: `db-card-list-row-field-${selectedcarddatatestid}-title`,
                    });
                } else {
                    selector = getDataTestIdSelector({
                        domSelector: 'div',
                        dataTestIdValue: `db-card-list-row-field-${selectedcarddatatestid}-line${rowId}`,
                    });
                }
                break;
            case Alignment.RIGHT:
                if (rowId === '1') {
                    selector = getDataTestIdSelector({
                        domSelector: 'span',
                        dataTestIdValue: `db-card-list-row-field-${selectedcarddatatestid}-title-${alignment}`,
                    });
                } else {
                    selector = getDataTestIdSelector({
                        domSelector: 'span',
                        dataTestIdValue: `db-card-list-row-field-${selectedcarddatatestid}-line${rowId}-${alignment}`,
                    });
                }
                break;
            default:
                throw Error(`Unexpected alignmentment: ${alignment}`);
        }
        return selector;
    };

    async expectedValue({ rowId, alignment, value }: { rowId: string; alignment: Alignment; value: string }) {
        const selector = this.getCorrectSelectorperAlignment(rowId, alignment);
        await this.expectTextContent({ toBe: value, ignoreCase: true, cssSelector: `${selector} span` });
    }

    async selectLink(rowId: string, alignment: Alignment) {
        const selector = this.getCorrectSelectorperAlignment(rowId, alignment);
        const selectedItem = await $(`${this.cssSelector} ${selector} span[data-component="link"] button`);
        await selectedItem.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found. \nSelector: ${selector}`,
        });

        await selectedItem.moveTo();
        await browser.execute(
            `var element = document.querySelector('${selectedItem.selector}');
            if (element){
                element.click();
            }`,
        );
        // await selectedItem.click({ x: 0, y: 0 });
    }

    getHeaderColIndex = async (headerValue: string) => {
        const headercols = await this.findAll(
            `${this.cssSelector} ${getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'db-table-table-selection-column-header' })}`,
            true,
        );

        // eslint-disable-next-line no-restricted-syntax
        for (const headercol of headercols) {
            const actualHeaderValue = await headercol.getText();
            if (actualHeaderValue.toLowerCase() === headerValue.toLowerCase()) {
                return headercol.index as number;
            }
        }
        return -1;
    };

    async selectSwitchTableRow(rowText: string, headerValue: string) {
        const headerIndex = await this.getHeaderColIndex(headerValue);
        const rows = await this.findAll(
            `${this.cssSelector} ${getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'db-table-table-row' })}`,
            true,
        );

        try {
            // eslint-disable-next-line no-restricted-syntax
            for (const row of rows) {
                const cols = await row.$$('.db-table-table-cell-wrapper');
                const col = cols[headerIndex];
                const actualrowText = await col.getText();
                if (actualrowText.toLowerCase().includes(rowText.toLowerCase())) {
                    StaticStore.storeObject(StaticStore.StoredKeys.DASHBOARD_WIDGET_TABLE_CARD_SWITCH_CARD_ROW, row);
                    await row.moveTo();
                    await row.click({ x: 20, y: 20 });
                    return;
                }
            }
        } catch (error) {
            throw new Error(
                `Expected element could not be found: row with value "${rowText}". \nSelector: ${this.cssSelector}`,
            );
        }

        throw new Error(
            `Expected element could not be found: row with value "${rowText}". \nSelector: ${this.cssSelector}`,
        );
    }

    async tickUntickSelectedTableRow(state: string) {
        const row: any = StaticStore.getStoredObject(
            StaticStore.StoredKeys.DASHBOARD_WIDGET_TABLE_CARD_SWITCH_CARD_ROW,
        );
        const selectedItem = await row.$('.db-table-table-selection-column [data-component="checkbox"] input');

        await selectedItem.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found. \nSelector: ${this.cssSelector} `,
        });

        const select = state === 'ticks';
        const selectionState = await selectedItem.isSelected();
        if ((select && !selectionState) || (!select && selectionState)) {
            if (!(await selectedItem.isClickable())) await browser.execute(elem => elem.scrollIntoView(), selectedItem);
            await browser.execute(elem => elem.click({ x: 0, y: 0 }), selectedItem);
            await waitForPromises(300, 'wait for click to be processed');
        }
    }

    async expandCollapseSelectedTableRow(state: string) {
        const row: any = StaticStore.getStoredObject(
            StaticStore.StoredKeys.DASHBOARD_WIDGET_TABLE_CARD_SWITCH_CARD_ROW,
        );
        const selectedItem = await row.$('.db-table-table-expand-column [aria-label="Expand row"]');

        await selectedItem.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found. \nSelector: ${this.cssSelector} `,
        });

        const select = state === 'expands';
        const ariaExpanded = await selectedItem.getAttribute('aria-expanded');
        if ((select && (!ariaExpanded || ariaExpanded === 'false')) || (!select && ariaExpanded === 'true')) {
            if (!(await selectedItem.isClickable())) await browser.execute(elem => elem.scrollIntoView(), selectedItem);
            await browser.execute(elem => elem.click({ x: 0, y: 0 }), selectedItem);
            await waitForPromises(300, 'wait for click to be processed');
        }
    }

    async expectTableWidgetTableValue(value: string, headerValue: string) {
        const col = await this.getCorrectColumnFromRow(headerValue);
        await this.expectTextContentWithElement({
            element: col,
            selectorToUse: this.cssSelector,
            toBe: value,
            ignoreCase: true,
        });
    }

    async clickTableWidgetTableRowColumn(headerValue: string) {
        const col = await this.getCorrectColumnFromRow(headerValue);
        const linkCol = await col.$('[data-component="link"] button');

        await linkCol.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found:  link field. \nSelector: ${this.cssSelector}`,
        });
        await linkCol.waitForDisplayed({
            timeoutMsg: `Element not displayed.\nSelector: ${this.cssSelector}`,
        });
        await linkCol.moveTo();
        await linkCol.waitForClickable();
        await linkCol.click({ x: 0, y: 0 });
    }

    private async getCorrectColumnFromRow(headerValue: string) {
        const row = StaticStore.getStoredObject(
            StaticStore.StoredKeys.DASHBOARD_WIDGET_TABLE_CARD_SWITCH_CARD_ROW,
        ) as WebdriverIO.Element;
        const headerIndex = await this.getHeaderColIndex(headerValue);
        const cols = await row.$$('.db-table-table-cell-wrapper > span:last-of-type');
        const col = cols[headerIndex];
        return col;
    }

    async expectWidgetBasicValue(value: string) {
        await this.expectWidgetValueToContain({ expectedValue: value, valueType: 'text' });
    }

    async expectWidgetGaugeValue(value: string) {
        const selectorToUse = '.recharts-pie-sector text';
        await this.expectWidgetValueToContain({ expectedValue: value, valueType: 'html', selectorToUse });
    }

    async expectWidgetTileIndicatorValue(value: string) {
        const selectorToUse = getDataTestIdSelector({ domSelector: 'span', dataTestIdValue: 'indicator-widget-title' });
        await this.expectWidgetValueToContain({ expectedValue: value, valueType: 'text', selectorToUse });
    }

    async expectPillValue(value: string) {
        await this.expectTextContent({
            toBe: value,
            ignoreCase: false,
            cssSelector: '[data-element="db-table-count"]',
            ignoreContext: false,
        });
    }

    async expectTotalCountValue(value: string) {
        await this.expectTextContent({
            toBe: value,
            ignoreCase: false,
            cssSelector: 'span.db-custom-header-value',
            ignoreContext: false,
        });
    }

    async scrollFirstLast(scrollOption: string, value: string) {
        const selectorToUse =
            value === 'card'
                ? `${this.cssSelector} div[class="ReactVirtualized__Grid ReactVirtualized__List"]`
                : `${this.cssSelector} div[class="ReactVirtualized__Grid ReactVirtualized__Table__Grid"]`;

        let isNotDisplayed = true;
        const pixelToScroll = scrollOption === 'first' ? -2000 : 2000;

        while (isNotDisplayed) {
            let firstItemCard = await this.find('div[data-testid^="db-card-list-row-"]:nth-child(1)');
            let firstItemRow = await this.find('div[data-testid^="db-table-table-row"]:nth-child(1)');

            let top =
                value === 'card' ? await firstItemCard.getCSSProperty('top') : await firstItemRow.getCSSProperty('top');
            const lastTop = top;

            await browser.execute(
                `var element = document.querySelector('${selectorToUse}');
                        if (element){
                            element.scrollTop += ${pixelToScroll};
                        }`,
            );
            await waitForPromises(500, 'scroll');
            if (scrollOption === 'first') {
                isNotDisplayed = top.value !== '0px';
            } else {
                firstItemCard = await this.find('div[data-testid^="db-card-list-row-"]:nth-child(1)');
                firstItemRow = await this.find('div[data-testid^="db-table-table-row"]:nth-child(1)');
                top =
                    value === 'card'
                        ? await firstItemCard.getCSSProperty('top')
                        : await firstItemRow.getCSSProperty('top');
                isNotDisplayed = top.value !== lastTop.value;
            }
        }
        if (isNotDisplayed) {
            await takeScreenshot();
            throw new Error('Could not scroll to element.');
        }
    }

    async scrollDownToItem({
        representationName,
        rowText,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        headerOrSection,
        headerOrSectionName,
    }: {
        representationName: string;
        rowText: string;
        headerOrSection: string;
        headerOrSectionName: string;
    }) {
        let isNotDisplayed = true;
        let bottomNotReached = true;
        const selectorToUse = `${this.cssSelector} div[class="ReactVirtualized__Grid ReactVirtualized__${representationName === 'card' ? 'List' : 'Table__Grid'}"]`;
        const tableSelectorToUse = representationName === 'card' ? `card-list` : `table-table`;
        let firstRowInViewport = await this.find(`.db-${tableSelectorToUse}-row:nth-child(1)`);

        await browser.execute(
            `var element = document.querySelector('${selectorToUse}');
                if (element){
                    element.scrollTop += ${(await firstRowInViewport.getSize()).height};
                }`,
        );
        await waitForPromises(10, 'scroll');

        while (isNotDisplayed && bottomNotReached) {
            // check if searched row is displayed
            const thirdRowInViewport = await this.find(`.db-${tableSelectorToUse}-row:nth-child(3)`);
            const col =
                representationName === 'card'
                    ? await thirdRowInViewport.$('.db-card-list-title')
                    : (await thirdRowInViewport.$$('.db-table-table-cell-wrapper'))[
                          await this.getHeaderColIndex(headerOrSectionName)
                      ];
            const actualText = await col.getText();
            if (actualText.toLowerCase().includes(rowText.toLowerCase())) {
                isNotDisplayed = false;
                break;
            }

            firstRowInViewport = await this.find(`.db-${tableSelectorToUse}-row:nth-child(1)`);
            let top = await firstRowInViewport.getCSSProperty('top');
            const lastTop = top;

            // scroll rows one by one
            await browser.execute(
                `var element = document.querySelector('${selectorToUse}');
                    if (element){
                        element.scrollTop += ${(await firstRowInViewport.getSize()).height};
                    }`,
            );
            await waitForPromises(10, 'scroll');

            // check if bottom is reached
            firstRowInViewport = await this.find(`.db-${tableSelectorToUse}-row:nth-child(1)`);
            top = await firstRowInViewport.getCSSProperty('top');
            bottomNotReached = top.value !== lastTop.value;
        }

        if (isNotDisplayed) {
            await takeScreenshot();
            throw new Error(
                `${representationName} with text "${rowText}" at "${headerOrSectionName}" was not found in table widget. \nSelector: ${this.cssSelector}`,
            );
        }
    }

    async waitForFilterDropdownToBeDisplayed(filterName: string, reverse: boolean = false) {
        const filterDropdown = await this.find(`input[aria-label="${filterName}"]`);
        await waitForElementToBeDisplayed({
            name: filterName,
            selector: String(filterDropdown.selector),
            reverse,
        });
    }

    async clickFilterDropdown(filterName: string) {
        const filterDropdown = await this.find(`[data-component="filterable-select"] [aria-label="${filterName}"]`);
        await filterDropdown.click();
    }

    async selectFilterDropdownValue(value: string) {
        const selectorToUse = '[data-component="filterable-select"] [data-component="option"]';
        const filterDropdownValue = await this.findElementByTextContent({
            cssSelector: selectorToUse,
            ignoreCase: false,
            text: value,
        });
        await filterDropdownValue.scrollIntoView();
        await filterDropdownValue.click();
    }

    async toggleHeaderButton(buttonName: string) {
        const selectorToUse = `div[data-testid="db-toggle-container"] [aria-label="${buttonName}"]`;
        const toggleButton = await this.find(selectorToUse);
        await toggleButton.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${buttonName}" switch button. \nSelector: ${toggleButton.selector}`,
        });
        await toggleButton.waitForClickable();
        await toggleButton.click();
    }

    async clearFilterDropdownValue() {
        const clearButton = $('button [data-element="cross_circle"]');
        await clearButton.scrollIntoView();
        await clearButton.waitForClickable();
        await clearButton.click();
    }

    async resize(xStep: number, yStep: number) {
        const container = widgetContainer(this.name);
        const origin = await $(`.react-draggable ${container} +span.react-resizable-handle`);
        await origin.waitForExist();
        await origin.waitForClickable();
        const x = Number(xStep);
        const y = Number(yStep);
        await browser
            .action('pointer')
            .move({ duration: 0, origin })
            .down({ button: 0 }) // left button
            .pause(1000)
            .move({ duration: 100, origin, x: 0 + x, y: 0 + y })
            .pause(1000)
            .up({ button: 0 })
            .perform();
    }

    async expectFilterDropdownValue(filterName: string, value: string) {
        await this.expectValue({
            toBe: value,
            cssSelector: `input[aria-label="${filterName}"]`,
        });
    }

    clickPeriodButton = async (name: string) => {
        const $button = await this.find(
            getDataTestIdSelector({
                domSelector: 'button',
                dataTestIdValue: `db-table-date-${name.toLowerCase().trim()}-period`,
            }),
        );
        const selector = $button.selector.toString();
        await waitForElementToBeFound({ name, selector });
        await $button.waitForClickable();
        await $button.click();
        await waitForPromises(300, 'wait for click to be processed');
    };

    selectPeriodType = async (expectedPeriod: 'day' | 'week' | 'month') => {
        const $button = await (
            await this.find('')
        ).$(`.//button[@data-element="button-toggle-button" and contains(text(),"${startCase(expectedPeriod)}")]`);
        await $button.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${expectedPeriod}" button. \nSelector: ${this.getSelectorForOperation('')} ${$button.selector}`,
        });
        await $button.waitForClickable();
        await $button.click();
        await waitForPromises(300, 'wait for click to be processed');
        await browser.waitUntil(async () => (await $button.getAttribute('aria-pressed')) === 'true', {
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected button to be pressed: ${expectedPeriod}`,
        });
    };

    selectDatePeriod = async (expectedValue: string) => {
        const $dateFilterBtn = await this.find(
            getDataTestIdSelector({
                domSelector: 'button',
                dataTestIdValue: 'db-date-filter-dropdown-button',
            }),
        );
        await $dateFilterBtn.waitForClickable();
        await $dateFilterBtn.click();
        await waitForPromises(300, 'wait for click to be processed');

        // Wait for the date filter to be displayed
        await waitForElementToBeDisplayed({
            name: 'Date filter',
            selector: getDataTestIdSelector({
                domSelector: 'div',
                dataTestIdValue: 'db-date-filter-selector',
            }),
        });

        const target = await this.find(`.db-date-filter-selector-row-element[aria-label="${expectedValue}"]`);

        // Scroll to end of list to reach current date
        const container = await $('.ReactVirtualized__Grid__innerScrollContainer');
        const containerHeight = await container.getSize('height');
        await scroll({
            selector: '.ReactVirtualized__Grid.ReactVirtualized__List',
            amount: containerHeight,
            direction: 'top',
        });

        await browser.waitUntil(
            async () => {
                if (!(await target.isClickable())) {
                    await scroll({
                        selector: '.ReactVirtualized__Grid.ReactVirtualized__List',
                        amount: -100,
                        direction: 'top',
                    });
                    await waitForPromises(300, 'wait for scroll to be processed');
                }
                return target.isClickable();
            },
            {
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected element to be clickable in "${this.timeoutWaitFor}" ms:\nSelector: ${target.selector}`,
            },
        );

        await target.moveTo();
        await waitForPromises(300, 'wait for scroll');

        await target.click();
        await waitForPromises(300, 'wait for click');
    };
}
