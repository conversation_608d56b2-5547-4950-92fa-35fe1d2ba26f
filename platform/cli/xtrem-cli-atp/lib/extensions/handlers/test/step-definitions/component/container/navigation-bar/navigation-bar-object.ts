import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import { waitForPromises } from '../../field/wait-util';

export class NavigationBarObject extends AbstractPageObject {
    constructor() {
        super('.consumer-app-bar');
    }

    private readonly helpCenterIconSelector: string = '#pendo-help';

    private readonly helpCenterPanelSelector: string = '#pendo-resource-center-container';

    async clickHamburgerMenu() {
        const hamburgerBtn = await this.find(
            '[data-component="navigation-bar"] [data-component="button"][aria-label="list_view"]',
            true,
        );

        try {
            await browser.waitUntil(() => hamburgerBtn.isClickable(), {
                timeout: this.timeoutWaitFor,
                timeoutMsg: 'Error: Button expected to be clickable.',
            });
        } catch (error) {
            await browser.takeScreenshot();
            throw error;
        }

        await this.click(hamburgerBtn.selector.toString(), true);
    }

    async clickOnIcon(icon: string) {
        const browserURL = await browser.getUrl();

        if (utils.regex_handheld_home(browserURL)) {
            if (!utils.regex_handheld_settings(browserURL)) {
                await this.clickHamburgerMenu();
                await waitForPromises(0, 'click hamburger menu');
            }

            const stickers = await this.findAll('.se-settings-page-sticker', true);

            // eslint-disable-next-line no-restricted-syntax
            for (const sticker of stickers) {
                const stickerTxt = await sticker.getText();
                if (stickerTxt === icon) {
                    await sticker.click();
                    await waitForPromises(0, 'click on sticker');
                    return;
                }
            }
        } else {
            const stickerIcon = await this.find(`[title='${icon}'] [data-component='icon']`);
            await browser.waitUntil(
                () => {
                    return stickerIcon.isClickable();
                },
                { timeout: this.timeoutWaitFor },
            );
            await stickerIcon.click();
        }
    }

    async selectEndPoint(selection: string) {
        const browserURL = await browser.getUrl();
        if (utils.regex_handheld_home(browserURL)) {
            await this.clickHamburgerMenu();
            await waitForPromises(500, 'clicking hamburger menu');
        }

        await this.expectToBeDisplayed(
            utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'settings-page' }),
        );

        const endPoints = await this.findAll('.se-settings-endpoint-option', true);

        const endPointNames = await Promise.all(
            endPoints.map(async (endPoint, index) => {
                const el = await endPoint.$('.se-endpoints-name');
                const value = await el.getText();
                return { value, index };
            }),
        );

        const elFound = endPointNames.find(endPoint => endPoint.value === selection);
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        elFound && (await endPoints[elFound.index].click());

        if (!endPointNames.some(endPoint => endPoint.value === selection)) {
            throw new Error(`The selected endpoint "${selection}" does not exist`);
        }
    }

    async writeUserName(selection: string) {
        const userName = await browser.$('.consumer-username input');
        try {
            const browserURL = await browser.getUrl();
            if (utils.regex_handheld_home(browserURL)) {
                await this.clickHamburgerMenu();
                await waitForPromises(500, 'clicking hamburger menu');
            }
            await this.write({ content: selection, cssSelector: userName.selector as string });
        } catch (error) {
            throw new Error(
                `Expected element could not be found: "UserName" field.\nSelector: ${this.cssSelector} ${userName.selector}`,
            );
        }
    }

    async expectEndpointToBeSelected(selection: string) {
        const browserURL = await browser.getUrl();
        if (utils.regex_handheld_home(browserURL)) {
            await this.clickHamburgerMenu();
            await waitForPromises(500, 'clicking hamburger menu');
        }

        await this.expectToBeDisplayed(
            utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'settings-page' }),
        );

        const endPoints = await this.findAll('.se-endpoints-name', true);
        const endPointNames = await Promise.all(
            endPoints.map(async endPoint => {
                const value = await endPoint.getText();
                return value;
            }),
        );

        if (!endPointNames.includes(selection)) {
            throw new Error(`"${selection}" is not in the list of available endpoints.`);
        }

        const selectedEndPoint = await this.find(
            '.se-settings-endpoint-option [data-element="tick"][type="tick"]',
            true,
        );
        const selectedEndPointName = await (await selectedEndPoint.nextElement()).getText();

        if (selectedEndPointName !== selection) {
            await browser.takeScreenshot();
            throw new Error(
                `Expected endpoint is not selected. Expected: ${selection}. Actual: ${selectedEndPointName}.`,
            );
        }
    }

    logOut = async () => {
        const browserURL = await browser.getUrl();
        if (utils.regex_handheld_home(browserURL)) {
            await this.clickHamburgerMenu();
            await waitForPromises(500, 'clicking hamburger menu');
        }

        await this.expectToBeDisplayed(
            utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'settings-page' }),
        );

        const logOutBtn = await browser.$('button.se-settings-log-out');
        await utils.waitForElementToBeDisplayed({ name: 'sageIdPageBtn', selector: logOutBtn.selector.toString() });
        await logOutBtn.click();
        await waitForPromises(500, 'waiting for logout');
    };

    async clickNavigationButton(button: string) {
        const navButton = await this.find(
            `[data-component='navigation-bar'] [data-component='button'] [data-element='${button
                .replace(' ', '_')
                .toLowerCase()}']`,
            true,
        );

        await browser.waitUntil(
            () => {
                return navButton.isClickable();
            },
            { timeout: this.timeoutWaitFor },
        );

        await this.click(navButton.selector.toString(), true);

        await waitForPromises(500, 'clicking on navigation button');
    }

    override async expectValue({ toBe, selectorToUse }: { toBe: string; selectorToUse: string }) {
        const element = await $(selectorToUse);
        await element.waitForExist({ timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}` });
        await element.waitForDisplayed({
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
        });
        let textContent = '';
        try {
            await browser.waitUntil(
                async () => {
                    textContent = (await element.getText()).trim();
                    await waitForPromises(500, 'get text');
                    return (textContent || '').replace(/…/g, '...') === (toBe || '');
                },
                {
                    timeout: this.valueCheckTimeout,
                },
            );
        } catch (error) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected value: "${toBe}", actual: "${textContent}".\nSelector: ${selectorToUse}`);
        }
    }

    async expectHelpCenterIconIsDisplayed(reverse = false) {
        await this.expectToBeDisplayed(this.helpCenterIconSelector, reverse);
    }

    async expectHelpCenterResourceIsDisplayed(resource: string, reverse = false) {
        const resourceSelector = `//div[contains(@class, "_pendo-resource-center-module-list-item-title-text") and contains(text(), "${resource}")]`;
        await this.expectToBeDisplayed(resourceSelector, reverse);
    }

    async openHelpCenterResource(resource: string) {
        const resourceSelector = `//div[contains(@class, "_pendo-resource-center-module-list-item-title-text") and contains(text(), "${resource}")]`;
        await this.expectToBeDisplayed(resourceSelector);
        const resourceElement = await browser.$(resourceSelector);
        await resourceElement.click();
    }

    async isHelpCenterPanelDisplayed(): Promise<boolean> {
        const helpCenterPanel = await browser.$(this.helpCenterPanelSelector);
        return helpCenterPanel.isDisplayed();
    }

    async openHelpCenter(): Promise<void> {
        const helpCenterIcon = await browser.$(this.helpCenterIconSelector);
        const isHelpCenterPanelDisplayed = await this.isHelpCenterPanelDisplayed();
        await helpCenterIcon.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: help center icon . \nSelector: ${this.helpCenterIconSelector} `,
        });
        await helpCenterIcon.waitForClickable();
        if (!isHelpCenterPanelDisplayed) {
            await helpCenterIcon.click();
        }
    }

    async closeHelpCenter(): Promise<void> {
        const isHelpCenterPanelDisplayed = await this.isHelpCenterPanelDisplayed();
        const closeButton = await browser.$(`${this.helpCenterPanelSelector} button`);

        if (isHelpCenterPanelDisplayed) {
            await closeButton.waitForClickable({
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected element could not be clicked: close button.\nSelector: ${this.helpCenterPanelSelector} button`,
            });
            await closeButton.click();
        }
    }
}
