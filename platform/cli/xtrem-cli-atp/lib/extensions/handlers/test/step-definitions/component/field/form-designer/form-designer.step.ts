import { Then, When } from '@cucumber/cucumber';
import { ElementContext, FieldTypes, fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import {
    FormDesignerFieldObject,
    FormDesignerLocation,
    FormDesignerTableRelativeLocation,
} from './form-designer-object';

/* ------------------------Form designer step --------------------------*/

When(/^the user clicks in the (header|body|footer) of the form designer$/, async (location: FormDesignerLocation) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    await field.click(`div.document-editor div#${location}`);
});

When(
    /^the user writes "(.*)" in the (header|body|footer) of the form designer$/,
    async (value: string, location: FormDesignerLocation) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.write({ content: value, cssSelector: `div.document-editor div#${location}` });
    },
);

When(
    /^the user selects the element on line (.*) in the (header|body|footer) of the form designer$/,
    async (lineNo: number, location: FormDesignerLocation) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.selectFormDesignerText(location, lineNo);
    },
);

When(
    /^the user selects all the content of the (header|body|footer) of the form designer$/,
    async (location: FormDesignerLocation) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.selectFormDesignerText(location);
    },
);

When(/^the user clicks the "(.*)" button in the form designer toolbar$/, async (buttonName: string) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    await field.clickFormDesignerToolbarButton(buttonName);
});

When(/^the user selects the "(.*)" option in the form designer toolbar$/, async (optionName: string) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    await field.clickFormDesignerToolbarOption(optionName);
});

When(
    /^the user inserts a table with ([0-9]*) rows and ([0-9]*) columns in the form designer$/,
    async (rows: number, columns: number) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.addFormDesignerTable(rows, columns);
    },
);

When(
    /^the user sets the (font|background) color to "(.*)" in the form designer$/,
    async (button: 'font' | 'background', color: string) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.selectFormDesignerColor(button, color);
    },
);

When(/^the user clicks the Close button of the "(.*)" panel in the form designer$/, async (panelName: string) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    await field.closeFormDesignerPanel(panelName);
});

When(
    /^the user searches "(.*)" in the node-step-tree of the form designer on (the main page|a modal)$/,
    async (value, context: ElementContext) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.searchNodeStepTree(value, context);
    },
);

Then(
    /^the "(.*)" button in the form designer toolbar is (enabled|disabled)$/,
    async (buttonName: string, cssState: 'enabled' | 'disabled') => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.expectFormDesignerToolbarButtonState(buttonName, cssState === 'disabled');
    },
);

Then(
    /^the "(.*)" panel in the form designer is (displayed|hidden)$/,
    async (panelName: string, cssState: 'displayed' | 'hidden') => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.expectPanelToBeDisplayed(panelName, cssState === 'hidden');
    },
);

/* ------------------------Form designer editor dialog step --------------------------*/

Then(/^the title of the form designer editor dialog is "(.*)"$/, async (expectedTitle: string) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    await field.expectFormDesignerDialogTitle(expectedTitle);
});

Then(/^the title step of the form designer editor dialog is "(.*)"$/, async (expectedTitle: string) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    await field.expectFormDesignerStepTitle(expectedTitle);
});

When(/^the user clicks the "(.*)" button of the form designer editor dialog$/, async (buttonName: string) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    await field.clickFormDesignerDialogButton(buttonName);
});

When(/^the user clicks the Close button of the form designer editor dialog$/, async () => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    await field.clickFormDesignerDialogCloseButton();
});

When(
    /^the user selects the card with "(.*)" value in the selection card of the form designer editor dialog$/,
    async (value: string) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.selectFormDesignerCard(value);
    },
);

Then(
    /^the card with "(.*)" value in the selection card of the form designer editor dialog is (selected|unselected)$/,
    async (value: string, state: 'selected' | 'unselected') => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.expectFormDesignerCardToBeSelected(value, state === 'unselected');
    },
);

Then(
    /^the "(.*)" button of the form designer editor dialog is (enabled|disabled)$/,
    async (buttonName: string, state: 'enabled' | 'disabled') => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.expectFormDesignerDialogButtonState(buttonName, state === 'disabled');
    },
);

/* ------------------------Form designer widget editor step --------------------------*/

When(
    /^the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (text|dropdown-list|date) field of row "(.*)" in the form designer widget editor$/,
    async (value: string, columnName: string, fieldType: FieldTypes, row: number) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.writeToWidgetEditorField({ columnName, fieldType, row, value });
    },
);

When(
    /^the user opens the "([^"\n\r]*)" dropdown field of row "(.*)" in the form designer editor$/,
    async (columnName: string, row: number) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.openEditorDropdownField(columnName, row);
    },
);

When(
    /^the user selects the "([^"\n\r]*)" option in the "([^"\n\r]*)" dropdown field of row "(.*)" in the form designer editor$/,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (option: string, columnName?: string, row?: number) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.selectEditorDropdownFieldOption(option);
    },
);

When(
    /^the user clears the "([^"\n\r]*)" (text|dropdown-list|date) field of row "(.*)" in the form designer widget editor$/,
    async (columnName: string, fieldType: FieldTypes, row: number) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.writeToWidgetEditorField({ columnName, fieldType, row });
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (text|dropdown-list|date) field of row "(.*)" in the form designer widget editor is "([^"\n\r]*)"$/,
    async (columnName: string, fieldType: FieldTypes, row: number, value: string) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.expectWidgetEditorFieldToBe({ columnName, fieldType, row, value });
    },
);

Then(
    /^the "([^"\n\r]*)" (text|dropdown-list|date) field of row "(.*)" in the form designer widget editor is (enabled|disabled)$/,
    async (columnName: string, fieldType: FieldTypes, row: number, cssState: 'enabled' | 'disabled') => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.expectWidgetEditorFieldToBeDisabled({
            columnName,
            fieldType,
            row,
            reverse: cssState === 'disabled',
        });
    },
);

Then(
    /^the "([^"\n\r]*)" (text|dropdown-list|date) field of row "(.*)" in the form designer widget editor dialog is (displayed|hidden)$/,
    async (columnName: string, fieldType: FieldTypes, row: number, cssState: 'displayed' | 'hidden') => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.expectWidgetEditorFieldToBeDisplayed({
            columnName,
            fieldType,
            row,
            reverse: cssState === 'hidden',
        });
    },
);

When(
    /^the user clicks the "([^"\n\r]*)" table button in the form designer widget editor$/,
    async (buttonName: string) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.clickWidgetEditorButton({ buttonName, row: 1, tableButton: true });
    },
);

When(
    /^the user clicks the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the form designer widget editor$/,
    async (buttonName: string, row: number) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.clickWidgetEditorButton({ buttonName, row, tableButton: false });
    },
);

Then(
    /^the "([^"\n\r]*)" action button of row "([^"\n\r]*)" the form designer widget editor is (displayed|hidden)$/,
    async (buttonName: string, row: number, cssState: 'displayed' | 'hidden') => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.expectWidgetEditorButtonToBeDisplayed({
            buttonName,
            row,
            tableButton: false,
            reverse: cssState === 'hidden',
        });
    },
);

Then(
    /the user clicks the "([^"\n\r]*)" switch of row "([^"\n\r]*)" in the form designer widget editor$/,
    async (name: string, rowId: number) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.clickSwitch(name, rowId);
    },
);

When(
    /^the user drags row "([^"\n\r]*)" (up|down) "([^"\n\r]*)" rows in the form designer editor$/,
    async (row: number, direction: 'up' | 'down', yStep: number) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        const sign = direction === 'down' ? 1 : -1;
        await field.dragRow(row, sign * 45 * yStep);
    },
);

/* ------------------------Form designer - table / data container step --------------------------*/
When(
    /^the user selects the "(.*)" occurrence of "([^"]*)" (table|data container) in the form designer (header|body|footer)$/,
    async (
        occurrence: string,
        name: string,
        containerType: 'table' | 'data container',
        location: FormDesignerLocation,
    ) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        const dataTableContainer = await field.selectFormDesignerTableDataContainer({
            name,
            occurrence,
            containerType,
            location,
        });
        await StaticStore.storeField(
            containerType === 'table' ? fieldTypes.formDesigner_table : fieldTypes.formDesigner_dataContainer,
            dataTableContainer,
        );
    },
);

When(
    /^the user selects the cell with the "(.*)" column header of the query table (header|body|footer) in the table of the form designer$/,
    async (columnHeader: string, relativeLocation: FormDesignerTableRelativeLocation) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        const formDesignerTable = <WebdriverIO.Element>StaticStore.getStoredField(fieldTypes.formDesigner_table);
        const formDesignerTableCell = await field.selectFormDesignerTableCellByHeader({
            formDesignerTable,
            columnHeader,
            relativeLocation,
        });
        await StaticStore.storeField(fieldTypes.formDesigner_tableCell, formDesignerTableCell);
    },
);

When(
    /^the user selects the cell with the "([^"]*)" column header of the query table footer group number "(.*)" in the table of the form designer$/,
    async (columnHeader: string, footerGroupNumber: string) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        const formDesignerTable = <WebdriverIO.Element>StaticStore.getStoredField(fieldTypes.formDesigner_table);
        const formDesignerTableCell = await field.selectFormDesignerTableCellByHeader({
            formDesignerTable,
            columnHeader,
            relativeLocation: FormDesignerTableRelativeLocation.footerGroup,
            footerGroupNumber: parseInt(footerGroupNumber, 10),
        });
        await StaticStore.storeField(fieldTypes.formDesigner_tableCell, formDesignerTableCell);
    },
);

When(/^the user clicks in the selected cell of the table in the form designer$/, async () => {
    const formDesignerTableCell = <WebdriverIO.Element>StaticStore.getStoredField(fieldTypes.formDesigner_tableCell);
    await formDesignerTableCell.scrollIntoView();
    await formDesignerTableCell.click();
});

When(/^the user clicks in the data container of the form designer$/, async () => {
    const formDesignerDataContainer = <WebdriverIO.Element>(
        StaticStore.getStoredField(fieldTypes.formDesigner_dataContainer)
    );
    await formDesignerDataContainer.scrollIntoView();
    await formDesignerDataContainer.click();
});

When(
    /^the user clicks the insert paragraph (before|after) block of the selected (table|data container) in the form designer$/,
    async (paragraphPosition: 'before' | 'after', containerType: 'table' | 'data container') => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        const dataTableContainer = <WebdriverIO.Element>(
            StaticStore.getStoredField(
                containerType === 'table' ? fieldTypes.formDesigner_table : fieldTypes.formDesigner_dataContainer,
            )
        );

        await field.insertParagraphBlock({ parentContainer: dataTableContainer, paragraphPosition, containerType });
    },
);

When(
    /^the user selects the unbreakable container in the selected cell of the table in the form designer$/,
    async () => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        const formDesignerTableCell = StaticStore.getStoredField(fieldTypes.formDesigner_tableCell);
        const unbreakableContainer = await field.selectUnbreakableContainer(formDesignerTableCell);

        await StaticStore.storeField(fieldTypes.formDesigner_unbreakableContainer, unbreakableContainer);
    },
);

When(/^the user selects the unbreakable container in the data container of the form designer$/, async () => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    const formDesignerDataContainer = StaticStore.getStoredField(fieldTypes.formDesigner_dataContainer);
    const unbreakableContainer = await field.selectUnbreakableContainer(formDesignerDataContainer);

    await StaticStore.storeField(fieldTypes.formDesigner_unbreakableContainer, unbreakableContainer);
});

When(
    /^the user clicks the insert paragraph (before|after) block of the selected unbreakable container of the form designer$/,
    async (paragraphPosition: 'before' | 'after') => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        const unbreakableContainer = <WebdriverIO.Element>(
            StaticStore.getStoredField(fieldTypes.formDesigner_unbreakableContainer)
        );

        await field.insertParagraphBlock({
            parentContainer: unbreakableContainer,
            paragraphPosition,
            containerType: 'unbreakable container',
        });
    },
);

When(/^the user clicks in the selected unbreakable container of the form designer$/, async () => {
    const unbreakableContainer = <WebdriverIO.Element>(
        StaticStore.getStoredField(fieldTypes.formDesigner_unbreakableContainer)
    );

    await unbreakableContainer.scrollIntoView();
    await unbreakableContainer.click();
});

/* ------------------------Form designer - formatting panel --------------------------*/
When(
    /^the user clicks the "([^"]*)" button in the formatting panel of the form designer$/,
    async (buttonText: string) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.clickTableFormatButtonByText(buttonText);
    },
);

When(
    /^the user clicks the "([^"]*)" button of the "([^"]*)" toggle button group in the formatting panel of the form designer$/,
    async (buttonText: string, groupLabel: string) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.clickToggleButton(groupLabel, buttonText);
    },
);

When(
    /^the user sets the (background|border) color to "(.*)" in the formatting panel of the form designer$/,
    async (elementType: 'background' | 'border', colorHex: string) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.setColor(elementType, colorHex);
    },
);

When(
    /^the user selects the "([^"]*)" option in the "(.*)" dropdown field of the formatting panel of the form designer$/,
    async (option: string, fieldName: string) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.setPanelFieldValue({ elementName: fieldName, fieldType: fieldTypes.dropdownList, value: option });
    },
);

When(
    /^the user writes "([^"]*)" in the "(.*)" text field of the formatting panel of the form designer$/,
    async (text: string, fieldName: string) => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.setPanelFieldValue({ elementName: fieldName, fieldType: fieldTypes.text, value: text });
    },
);

When(/^the user writes "(.*)" in the selected cell of the table in the form designer$/, async (text: string) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    const formDesignerTableCell = <WebdriverIO.Element>StaticStore.getStoredField(fieldTypes.formDesigner_tableCell);
    await field.writeTextValue(text, formDesignerTableCell);
});

When(/^the value in the selected cell of the table in the form designer is "(.*)"$/, async (text: string) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    const formDesignerTableCell = <WebdriverIO.Element>StaticStore.getStoredField(fieldTypes.formDesigner_tableCell);
    await field.expectedValue(text, formDesignerTableCell);
});

When(/^the user writes "(.*)" in the selected data container of the form designer$/, async (text: string) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    const dataTableContainer = <WebdriverIO.Element>StaticStore.getStoredField(fieldTypes.formDesigner_dataContainer);
    await field.writeTextValue(text, dataTableContainer);
});

When(/^the value in the selected data container of the form designer is "(.*)"$/, async (text: string) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    const dataTableContainer = <WebdriverIO.Element>StaticStore.getStoredField(fieldTypes.formDesigner_dataContainer);
    await field.expectedValue(text, dataTableContainer);
});

When(/^the user writes "(.*)" in the selected unbreakable container of the form designer$/, async (text: string) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    const unbreakableContainer = <WebdriverIO.Element>(
        StaticStore.getStoredField(fieldTypes.formDesigner_unbreakableContainer)
    );
    await field.writeTextValue(text, unbreakableContainer);
});

When(/^the value in the selected unbreakable container of the form designer is "(.*)"$/, async (text: string) => {
    const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
    const unbreakableContainer = <WebdriverIO.Element>(
        StaticStore.getStoredField(fieldTypes.formDesigner_unbreakableContainer)
    );
    await field.expectedValue(text, unbreakableContainer);
});

Then(
    /^the "([^"]*)" button in the formatting panel is (displayed|hidden)$/,
    async (buttonName: string, cssState: 'displayed' | 'hidden') => {
        const field = <FormDesignerFieldObject>StaticStore.getStoredField(fieldTypes.formDesigner);
        await field.expectFormattingPanelButtonVisibility(buttonName, cssState === 'displayed');
    },
);
