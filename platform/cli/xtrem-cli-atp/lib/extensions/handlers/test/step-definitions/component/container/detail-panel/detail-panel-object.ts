import { camelCase } from 'lodash';
import { getDataTestIdSelector, LookupStrategy } from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';

export class DetailPanelObject extends AbstractPageObject {
    constructor() {
        super(getDataTestIdSelector({ domSelector: 'aside', dataTestIdValue: 'e-detail-panel' }));
    }

    async expectDetailPanelToAppear(cssState: 'displayed' | 'hidden') {
        if (cssState === 'displayed') {
            await this.expectToAppear();
        } else if (cssState === 'hidden') {
            await this.expectToDisappear();
        }
    }

    async getDetailPanelTabByNumber(tabNumber: number) {
        const tabs = await this.findAll('.e-detail-panel-tab-container [role="tab"]');
        return tabs[tabNumber - 1];
    }

    getDetailPanelTabByText(tabLabel: string) {
        return this.find(`.e-detail-panel-tab-container [data-testid~="e-xtrem-tab-${camelCase(tabLabel)}"]`);
    }

    async expectDetailPanelTitleToBeDisplayed({
        identifier,
        lookupStrategy,
        reverse = false,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        reverse?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(
            `[data-testid~="e-field-${
                lookupStrategy === LookupStrategy.BIND ? `bind-${identifier}` : `label-${camelCase(identifier)}`
            }"]#detailPanelHeaderSection`,
        );
        await this.expectToBeDisplayed(selectorToUse, reverse);
    }

    getDetailPanelCloseButton() {
        return this.find(getDataTestIdSelector({ domSelector: 'button', dataTestIdValue: 'e-detail-panel-close' }));
    }
}
