import { trimEnd } from 'lodash';
import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';

export class ProgressFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'progress', identifier, lookupStrategy, context });
    }

    override async expectTitle(expectedTitle: string) {
        await this.expectTextContent({ toBe: expectedTitle, ignoreCase: false, cssSelector: 'label' });
    }

    async expectProgressFieldValue(toBe: string) {
        const element = await this.find('[data-element="current-progress-label"');
        const trimmedToBe = trimEnd(toBe, '%').trim();
        let trimmedResult = '';
        try {
            await browser.waitUntil(
                async () => {
                    const result = await element.getText();
                    trimmedResult = trimEnd(result, '%').trim();
                    return (trimmedResult || '') === (trimmedToBe || '');
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Expected value: ${trimmedToBe}, actual: ${trimmedResult}.\nSelector: ${element.selector}`);
        }
    }
}
