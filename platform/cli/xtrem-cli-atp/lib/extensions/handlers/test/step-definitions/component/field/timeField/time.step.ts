import { Then, When } from '@cucumber/cucumber';
import { runtimeParameters } from '../../../../../../../parameters';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { TimeFieldObject } from './time-object';

// ----------
// Static store field steps
// ----------
When(/^the user writes "(.*)" in the time field$/, async (dateString: string) => {
    const field = <TimeFieldObject>StaticStore.getStoredField(fieldTypes.time);
    const parsedTimeString = runtimeParameters.getStringOrParameter(dateString);
    await field.setHoursMin(parsedTimeString);
});

Then(
    /^the value of the (hours|minutes) segment of the time field is "(.*)"$/,
    async (timeSegment: 'hours' | 'minutes', timeSegmentValue: string) => {
        const field = <TimeFieldObject>StaticStore.getStoredField(fieldTypes.time);
        await field.checkHoursMinSegment(timeSegment, timeSegmentValue);
    },
);

Then(/^the user clicks the "(.*)" toggle button of the time field$/, async (dateString: string) => {
    const field = <TimeFieldObject>StaticStore.getStoredField(fieldTypes.time);
    const storedDateString = StaticStore.getUserdefinedKeyValueFromStore(dateString);
    await field.toggleDateRangeButton(storedDateString);
});

Then(/^the value of the time field is "(.*)"$/, async (dateString: string) => {
    const field = <TimeFieldObject>StaticStore.getStoredField(fieldTypes.time);
    const storedDateString = StaticStore.getUserdefinedKeyValueFromStore(dateString);
    await field.expectHoursMinToEqual(storedDateString);
});

When(/^the time field is mandatory$/, async () => {
    const field = <TimeFieldObject>StaticStore.getStoredField(fieldTypes.time);
    await field.isMandatory();
});
