import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';

export class LabelFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'label', identifier, lookupStrategy, context });
    }

    /**
     * This will check if element is enabled or not
     *
     * @param domElement Element to check if it is enabled or not in DOM
     * @param reverse if set to true, it will return check for disable element
     */
    override async expectToBeEnabledClass(selector: string, reverse = false) {
        await super.expectToBeEnabledClass(selector, reverse);
    }
}
