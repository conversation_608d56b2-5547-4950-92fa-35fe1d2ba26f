import AbstractPageObject from '../../abstract-page-object';

export type LookupActionIdentifier = 'cancel' | 'select';
export enum LookupActionStatus {
    Disabled = 'disabled',
    Enabled = 'enabled',
}

export class LookupActionObject extends AbstractPageObject {
    constructor({ identifier }: { identifier: LookupActionIdentifier }) {
        const testId: string = `e-lookup-dialog-button-${identifier}`;
        super(`[data-testid="${testId}"]`);
    }

    async expectStatusToBe(status: LookupActionStatus) {
        switch (status) {
            case LookupActionStatus.Disabled:
                await this.expectNotToBeEnabled(this.cssSelector);
                break;
            case LookupActionStatus.Enabled:
                await this.expectToBeEnabled(this.cssSelector);
                break;
            default:
                throw new Error(`Invalid lookup action status provided. Expected: 'disabled' | 'enabled' - Received: '${status}'`);
        }
    }
}