import { kebabCase } from 'lodash';
import * as utils from '../../../step-definitions-utils';
import { DialogObject } from '../../container/dialog/dialog-object';
import { isMobileDevice } from '../../test-globals';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

export class ReferenceFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
        fieldType,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
        fieldType?: string;
    }) {
        super({ fieldType: fieldType ?? 'reference', identifier, lookupStrategy, context });
    }

    override async select(value: string) {
        if (isMobileDevice()) {
            await utils.selectFromLookUpDialog(value);
            return;
        }
        await super.select(value);
    }

    async clickLookupButton() {
        let lookupButton = await this.find(
            `${utils.getMultipleDataTestIdSelector('button', ['e-ui-select-lookup-button'])} [data-element='lookup']`,
        );
        await lookupButton.waitForDisplayed({
            reverse: false,
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element is not displayed.\nSelector: ${lookupButton.selector}`,
        });

        lookupButton = await this.find(
            `${utils.getMultipleDataTestIdSelector('button', ['e-ui-select-lookup-button'])}`,
        );
        await browser.waitUntil(() => lookupButton.isClickable(), {
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element is not clickable.\nSelector: ${lookupButton.selector}`,
        });
        await lookupButton.click();
        await waitForPromises(500, 'open button lookup');
    }

    // eslint-disable-next-line class-methods-use-this
    async expectLookupDialogToAppear() {
        const lookUpDialog = new DialogObject();
        await lookUpDialog.expectToAppear();
        // await lookUpDialog.findOrFail(getDataTestIdSelector('div', 'e-lookup-dialog'));
        await lookUpDialog.findOrFail('div[data-component="dialog-full-screen"]');
    }

    override async expectHelperTextToBeDisplayed(reverse = false) {
        await this.expectToBeDisplayed(`${this.cssSelector} ${this.helperTextSelector}`, reverse);
    }

    override async write({
        content,
        cssSelector = 'input',
        ignoreContext = false,
    }: {
        content: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        await super.write({ content, cssSelector, ignoreContext });
    }

    // eslint-disable-next-line class-methods-use-this
    async expectTunnelLinkToBeDisplayed(reverse: boolean = false) {
        const tunnelLinkSelector = '.e-tunnel-link a';
        await utils.expectElementToBeDisplayed({ selector: tunnelLinkSelector, reverse });
    }

    async clickReferenceTunnelLink() {
        const tunnelLinkSelector = '.e-tunnel-link a';
        const tunnelLink = await this.find(tunnelLinkSelector);
        await tunnelLink.waitForClickable();
        await this.jsClick({ cssSelector: tunnelLinkSelector, skipVisibilityCheck: true });
    }

    async clickLookupActionLink(name: string) {
        const linkButtonName = kebabCase(name);
        const lookupLinkButton = await this.find(`[data-testid="e-ui-select-dropdown-${linkButtonName}-link"] button`);
        if (!(await lookupLinkButton.isDisplayed())) {
            await this.click();
        }
        await lookupLinkButton.click();
    }
}
