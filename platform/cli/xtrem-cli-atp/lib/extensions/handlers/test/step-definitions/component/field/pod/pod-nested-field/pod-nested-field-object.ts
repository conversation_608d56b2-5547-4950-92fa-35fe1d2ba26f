import * as utils from '../../../../step-definitions-utils';
import AbstractPageObject from '../../../abstract-page-object';
import * as StaticStore from '../../../static-store';
import { SelectDropDownObject } from '../../select-dropdown-object';
import { waitForPromises } from '../../wait-util';
import { PodObject } from '../pod-field/pod-object';

const getPodSelector = (pod?: PodObject) => {
    if (!pod) {
        const storedPod = StaticStore.getStoredObject(StaticStore.StoredKeys.POD_OBJECT) as PodObject;
        return storedPod.cssSelector;
    }
    return pod.cssSelector;
};

export class PodNestedFieldObject extends AbstractPageObject {
    public readonly selectDropDown: SelectDropDownObject;

    constructor({
        identifier,
        lookupStrategy,
        fieldType,
        pod,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        fieldType: utils.FieldTypes;
        pod?: PodObject;
    }) {
        super(
            `${getPodSelector(pod)} ${utils.getLookupStrategySelector({ fieldType: utils.getFieldType(fieldType), lookupStrategy, identifier })}`,
        );
        this.selectDropDown = new SelectDropDownObject(this.cssSelector);
    }

    clickMultiField = async (identifier: string) => {
        const element = await this.get();
        await element.waitForDisplayed({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${identifier}" nested field.\nSelector: ${this.cssSelector}`,
        });
        await element.scrollIntoView();
        await this.click(`${element.selector} .e-ui-select-input-wrapper input`, true);
        await waitForPromises(500, 'clickMultiField');
    };

    async setSwitch(value: string) {
        const element = await this.find(utils.getElementTypeSelector(utils.fieldTypes.switch));
        const selectionState = await element.isSelected();
        const expectedState = value === 'selects';
        if (selectionState !== expectedState || value === 'click') {
            await element.click();
        }
    }

    async setPodItemValue(value: string, fieldType: utils.FieldTypes) {
        switch (fieldType) {
            case utils.fieldTypes.date:
            case utils.fieldTypes.dynamicSelect:
            case utils.fieldTypes.numeric:
            case utils.fieldTypes.text:
            case utils.fieldTypes.reference:
            case utils.fieldTypes.multiReference:
                await this.clearInput();
                await this.write({ content: value });
                await waitForPromises(300, 'after write');
                break;
            case utils.fieldTypes.textArea:
                await this.clearInput({ cssSelector: 'textarea' });
                await this.write({ content: value, cssSelector: 'textarea' });
                await waitForPromises(300, 'after write');
                break;
            default:
                throw new Error(`Field type ${fieldType} not implemented`);
        }
    }

    async expectMultiReferenceValue(toBe: string) {
        const elements = await this.findAll('.e-ui-select-label [data-component="pill"]');
        const values = await Promise.all(elements.map(el => el.getText()));
        const expectedValues = toBe.split('|');
        const allElementsPresent = expectedValues.every(el => values.includes(el));
        if (toBe === '' && values.length === 0) return;
        if (!allElementsPresent) {
            throw new Error(`Expected value: ${toBe}, actual: ${values.join('|')}.\nSelector: ${this.cssSelector}`);
        }
    }

    async expectPodItemValue(value: string, fieldType: utils.FieldTypes) {
        switch (fieldType) {
            case utils.fieldTypes.date:
            case utils.fieldTypes.numeric:
            case utils.fieldTypes.text:
            case utils.fieldTypes.reference:
            case utils.fieldTypes.dynamicSelect:
            case utils.fieldTypes.multiDropdown:
                if (await (await this.find('span[data-testid="e-field-value"]')).isExisting()) {
                    await this.expectTextContent({ toBe: value, ignoreCase: false, cssSelector: 'span' });
                } else {
                    await this.expectValue({ toBe: value, cssSelector: 'input' });
                }
                break;
            case utils.fieldTypes.textArea: {
                const textAreaElement = await $(`${this.cssSelector} :is(span, textarea)`);
                const tagName = await textAreaElement.getTagName();
                const actualValue = utils.formatString(
                    tagName === 'textarea' ? await textAreaElement.getValue() : await textAreaElement.getText(),
                );

                if (actualValue !== utils.formatString(value)) {
                    await browser.takeScreenshot();
                    throw new Error(`Expected value: ${value}, actual: ${actualValue}\nSelector: ${this.cssSelector}`);
                }
                break;
            }
            case utils.fieldTypes.switch:
                if (await (await this.find('span[data-testid="e-field-value"]')).isExisting()) {
                    await this.expectTextContent({ toBe: value, ignoreCase: false, cssSelector: 'span' });
                } else {
                    await this.expectTextContent({ toBe: value, ignoreCase: false, cssSelector: 'div[type]' });
                }
                break;
            case utils.fieldTypes.multiReference:
                await this.expectMultiReferenceValue(value);
                break;
            default:
                throw new Error(`Field type ${fieldType} not implemented`);
        }
    }

    async clearNestedField(fieldType: utils.FieldTypes) {
        const element = await this.get();
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: nested field.\nSelector: ${this.cssSelector}`,
        });
        await element.scrollIntoView();
        await browser.pause(200);
        await this.clearInput({ ignoreContext: false, fieldType });
    }

    static selectOptionsInNestedField = async (
        nestedField: PodNestedFieldObject,
        value: string,
        nestedFieldType: utils.FieldTypes,
    ): Promise<void> => {
        // Split the value by "|" to handle multiple options
        const values = value.split('|').map(v => v.trim());
        // Iterate over each option and select it sequentially
        // eslint-disable-next-line no-restricted-syntax
        for (const option of values) {
            await nestedField.selectDropDown.selectOption(option);
        }
        // Close the dropdown list if the field is not a reference
        if (nestedFieldType !== utils.fieldTypes.reference) {
            await nestedField.closeList();
        }
    };

    clickActionButton = async (actionButton: string): Promise<void> => {
        const $actionButton = await $(
            await this.find(
                utils.getDataTestIdSelector({
                    domSelector: 'button',
                    dataTestIdValue: 'e-ui-dynamic-select-open-button',
                }),
            ),
        );

        if ((await $actionButton.getAttribute('aria-label')) !== actionButton) {
            throw new Error(
                `Expected action button with label "${actionButton}" not found.\nSelector: ${$actionButton.selector}`,
            );
        }

        await $actionButton.waitForClickable();
        await $actionButton.moveTo();
        await browser.execute(el => el.click(), $actionButton);
        await waitForPromises(500, 'clickActionButton');
    };
}
