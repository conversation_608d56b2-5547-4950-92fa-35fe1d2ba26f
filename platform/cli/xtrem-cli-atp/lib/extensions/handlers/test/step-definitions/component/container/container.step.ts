import { Then } from '@cucumber/cucumber';
import { ContainerElementTypes, ElementContext, LookupStrategy } from '../../step-definitions-utils';
import { BlockContainerObject } from './block/block-object';
import { ContainerObject } from './container-object';
import { SectionContainerObject } from './section/section-object';

const getContainer = ({
    identifier,
    lookupStrategy,
    context,
    type,
}: {
    identifier: string;
    lookupStrategy: LookupStrategy;
    context: ElementContext;
    type: ContainerElementTypes;
}): ContainerObject => {
    switch (type) {
        case ContainerElementTypes.SECTION:
            return new SectionContainerObject({ identifier, lookupStrategy, context });
        case ContainerElementTypes.BLOCK:
            return new BlockContainerObject({ identifier, lookupStrategy, context });
        default:
            throw new Error(`${identifier}: bad container type: ${type}`);
    }
};

Then(
    /^the "(.*)" (bound|labelled) (section) container on (the main page|a modal|a full width modal) looks like before$/,
    async ({
        identifier,
        lookupStrategy,
        type,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        type: ContainerElementTypes;
        context: ElementContext;
    }) => {
        const container = getContainer({ identifier, lookupStrategy, context, type });
        await container.expectToMatchSnapshot();
    },
);
