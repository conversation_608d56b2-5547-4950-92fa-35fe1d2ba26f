import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import * as StaticStore from '../../static-store';
import { testGlobals } from '../../test-globals';
import { waitForPromises } from '../wait-util';

export class NavigationPanelObject extends AbstractPageObject {
    constructor() {
        super('.e-page-navigation-panel');
    }

    async clickOnPanelRow(rowNb: string) {
        let selectorToUse = '';

        if (rowNb.match(/^(first|last)$/g)) {
            selectorToUse = `div:${rowNb}-child>${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-card' })} button`;
        } else if (rowNb.match(/^[0-9]+$/g)) {
            selectorToUse = `div:nth-child(${rowNb})>${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-card' })} button`;
        } else {
            throw new Error(`Expected element to be a number: "${rowNb}"`);
        }

        const element = await this.find(selectorToUse);
        await utils.waitForElementToExist({ name: 'navigation panel row', selector: selectorToUse });
        await utils.waitForElementToBeDisplayed({ name: 'navigation panel row', selector: selectorToUse });
        await element.click();
    }

    async clickOnRecord(recordName: string) {
        const panelItems = await this.findAll(
            `.e-page-navigation-panel-body ${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-card' })}`,
        );

        // eslint-disable-next-line no-restricted-syntax
        for (const item of panelItems) {
            const label = await item.$(
                `${utils.getDataTestIdSelector({ domSelector: 'span', dataTestIdValue: 'e-field-value' })}`,
            );

            const title = await label.getText();

            if (title.trim() === recordName.trim()) {
                await item.click();
                return;
            }
        }

        throw new Error(`Could not find navigation panel card with the following text: ${recordName}`);
    }

    async selectToggleButton(toggleName: string) {
        const toggleButton = await this.find(
            `.e-option-item-menu [data-component='button-toggle-group'] button[value='${toggleName}']`,
        );

        await browser.waitUntil(() => toggleButton.isDisplayed(), {
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Timeout waiting for toggle button ${toggleName} to be displayed`,
        });

        await toggleButton.click();
    }

    async removeFilterPill(text: string) {
        await (
            await this.find(
                `//*[@class="e-page-navigation-panel"]//span[@data-component="pill"][contains(text(),"${text}")]//button[@data-element="close"]`,
                true,
            )
        ).click();
    }

    async selectDropdownOption(label: string) {
        const fieldSelector = '.e-option-item-menu[data-options-menu-type="dropdown"]';
        const fieldElement = await this.find(fieldSelector);
        await browser.waitUntil(() => fieldElement.isDisplayed(), {
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected dropdown field to be displayed\nSelector: ${fieldSelector}`,
        });
        await fieldElement.click();

        const optionSelector = `.e-ui-select-suggestion[id="${label}"]`;
        const optionElement = await this.find(optionSelector);
        try {
            await utils.waitForElementToBeDisplayed({ name: 'list of options', selector: optionSelector });
        } catch {
            await (await $('body')).click();
            await fieldElement.click();
            await utils.waitForElementToBeDisplayed({ name: 'list of options', selector: optionSelector });
        }
        await optionElement.click();
    }

    async expectNavigationPanelToBeEmpty() {
        const emptyContainer = await this.find(
            utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-no-rows-found-component' }),
        );
        await emptyContainer.waitForDisplayed();
    }

    async openNavigationPanel(inverted = false) {
        const openBtn = await this.find(
            utils.getDataTestIdSelector({
                domSelector: inverted === false ? 'button' : 'span',
                dataTestIdValue:
                    inverted === false ? 'nav-panel-toggle-button-open' : 'e-field-label-toggleNavigationPanel',
                partialMatch: true,
            }),
            true,
        );

        await utils.waitForElementToBeDisplayed({
            name: openBtn.selector.toString(),
            selector: openBtn.selector.toString(),
        });
        await openBtn.waitForClickable();
        await openBtn.click();
        await waitForPromises(300, 'wait for panel to open or close');
        if (!(testGlobals.device === 'mobile' && inverted))
            await utils.waitForElementToBeDisplayed({
                name: 'navigation panel',
                selector: utils.getDataTestIdSelector({
                    domSelector: 'nav',
                    dataTestIdValue: 'e-page-navigation-panel-split-view',
                }),
                reverse: inverted,
            });

        if (testGlobals && testGlobals.device === 'mobile' && !inverted) {
            await browser.waitUntil(
                async () => {
                    const animationName = await browser.execute(
                        'return window.getComputedStyle(document.querySelector(".e-page-navigation-panel")).getPropertyValue("animation-name")',
                    );
                    return animationName === 'e-navigation-panel-mobile-content-move-in' && !inverted;
                },
                {
                    timeout: this.timeoutWaitFor,
                    timeoutMsg: `Expected navigation panel to be fully open within ${this.timeoutWaitFor} ms`,
                },
            );
        }
    }

    async searchFor(query: string) {
        const searchInput = `${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-table-field-mobile-search' })} input`;
        await utils.waitForElementToBeDisplayed({ name: 'search input', selector: searchInput });

        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(query);

        await $(searchInput).waitForClickable({
            timeout: this.timeoutWaitFor,
            timeoutMsg: 'Timeout waiting for search input to be clickable',
        });

        await this.write({ content: storeValue, cssSelector: searchInput, ignoreContext: true });
        await waitForPromises(500, 'search in navigation panel');
    }

    async clearNavigationPanel() {
        const searchInput = `${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-table-field-mobile-search' })} input`;
        const clearIcon = `button [data-element="cross"]`;
        await utils.waitForElementToBeDisplayed({ name: 'search input', selector: searchInput });

        await $(searchInput).waitForClickable({
            timeout: this.timeoutWaitFor,
            timeoutMsg: 'Timeout waiting for search input to be clickable',
        });
        await (await $(searchInput)).click();
        await (await $(clearIcon)).click();
        await waitForPromises(500, 'clear navigation panel');
    }

    async expectSearchValue(expectedContent: string) {
        const searchInput = `${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-table-field-mobile-search' })} input`;
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedContent);
        await utils.waitForElementToBeDisplayed({ name: 'search input', selector: searchInput });

        await $(searchInput).waitForClickable({
            timeout: this.timeoutWaitFor,
            timeoutMsg: 'Timeout waiting for search input to be clickable',
        });
        await this.expectValue({ toBe: storeValue, cssSelector: searchInput });
        await waitForPromises(500, 'check value navigation panel');
    }

    async expectFilterPillDisplayed({
        filterType,
        value,
        columnName,
        reverse = false,
    }: {
        filterType: string;
        value: string;
        columnName: string;
        reverse?: boolean;
    }) {
        const pillSelector = `[data-testid="e-filter-label-${columnName}-${value}-${filterType}"]`;
        await this.expectToBeDisplayed(pillSelector, reverse);
    }

    async closePill({ filterType, value, columnName }: { filterType: string; value: string; columnName: string }) {
        const pillCloseButton = await this.find(
            `[data-testid="e-filter-label-${columnName}-${value}-${filterType}"] button[data-element="close"]`,
        );
        await pillCloseButton.click();
        await waitForPromises(500, 'close pill');
    }
}
