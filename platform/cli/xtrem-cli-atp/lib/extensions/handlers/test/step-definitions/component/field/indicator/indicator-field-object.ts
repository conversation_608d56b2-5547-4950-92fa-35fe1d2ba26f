import * as utils from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';

export class IndicatorFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        fieldType,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        fieldType: utils.FieldTypes;
        context?: utils.ElementContext;
    }) {
        super({ fieldType, identifier, lookupStrategy, context, blankDomSelector: true });
    }

    async expectedValue(value: string) {
        await this.expectTextContent({ toBe: value, ignoreCase: true, cssSelector: '.e-tile-field-value' });
    }

    async expectTileToBeDisplayed(cssState: string) {
        const initialElement = await browser.$(this.cssSelector);
        // Go up 4 levels to find the desired parent
        let parentElement = initialElement;
        for (let i = 0; i < 4; i += 1) {
            parentElement = await parentElement.$('..');
        }
        const tile = parentElement;
        const tileSelectorToString = tile.selector.toString();
        const tileStateSelector = await tile.getAttribute('class');
        if (tileStateSelector.includes('e-hidden')) {
            await tile.waitForExist();
        } else {
            await utils.expectElementToBeDisplayed({ selector: tileSelectorToString, reverse: false });
        }
        if (tileStateSelector.includes('e-hidden') !== (cssState === 'hidden')) {
            throw new Error(`Expected Element to be ${cssState}.\nSelector: ${this.cssSelector}`);
        }
    }
}
