import { camelCase } from 'lodash';
import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import * as StaticStore from '../../static-store';

export class StepSequenceFieldObject extends AbstractPageObject {
    constructor({
        context,
        lookupStrategy,
        identifier,
    }: {
        context: utils.ElementContext;
        lookupStrategy: utils.LookupStrategy;
        identifier: string;
    }) {
        const selectorToUse = `${context ? `${utils.getContextSelector(context)} ` : ''} div[data-testid~="e-field-${
            lookupStrategy === utils.LookupStrategy.BIND ? `bind-${identifier}` : `label-${camelCase(identifier)}`
        }"]`;
        super(selectorToUse);
    }

    async select(identifier: string) {
        await (
            await this.get()
        ).waitForExist({
            timeoutMsg: `Expected element could not be found: "${identifier}".\nSelector: ${this.cssSelector}`,
        });

        await this.scrollTo();
        StaticStore.storeObject(StaticStore.StoredKeys.STEPSEQUENCE, this);
    }

    async expectTitle(expectedTitle: string) {
        await this.expectTextContent({
            toBe: expectedTitle,
            ignoreCase: false,
            cssSelector: utils.getDataTestIdSelector({ domSelector: 'label', dataTestIdValue: 'e-field-label' }),
        });
    }

    async expectHelperText(expectedHelperTextContent: string) {
        await this.expectTextContent({
            toBe: expectedHelperTextContent,
            ignoreCase: false,
            cssSelector: utils.getDataTestIdSelector({ domSelector: 'span', dataTestIdValue: 'e-field-helper-text' }),
        });
    }

    async expectTitleState(reverse: boolean = false) {
        await this.expectToBeDisplayed(
            utils.getDataTestIdSelector({ domSelector: 'label', dataTestIdValue: 'e-field-label' }),
            reverse,
        );
    }

    async expectHelperTextState(reverse: boolean = false) {
        await this.expectToBeDisplayed(
            `${this.cssSelector} ${utils.getDataTestIdSelector({ domSelector: 'span', dataTestIdValue: 'e-field-helper-text' })}`,
            reverse,
        );
    }

    async statusOfStepItem(stepItem: string, status: 'complete' | 'current' | 'incomplete') {
        const element = await this.find(`li[data-testid*="${stepItem}-step"]`);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${stepItem}".\nSelector: ${this.cssSelector} li[data-testid*="${stepItem}-step"]`,
        });
        const dataStatus = await element.getAttribute('data-status');
        if (dataStatus.toLowerCase() !== status.toLowerCase()) {
            throw new Error(
                `Expected element status to be "${status}" : Actual: "${dataStatus}".\nSelector: ${this.cssSelector} li[data-testid*="${stepItem}-step"]`,
            );
        }
    }

    async orientationOfStepItem(orientation: 'horizontal' | 'vertical') {
        const element = await this.find('ol[data-component="step-sequence"]');
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found. \nSelector: ${this.cssSelector}`,
        });
        const elementOrientation = await element.getAttribute('orientation');
        if (elementOrientation.toLowerCase() !== orientation.toLowerCase()) {
            throw new Error(
                `Expected element orientation to be "${orientation}", Actual: "${elementOrientation}".\nSelector: ${this.cssSelector}`,
            );
        }
    }
}
