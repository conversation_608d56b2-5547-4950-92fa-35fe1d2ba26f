import * as utils from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

export class SwitchFieldObject extends FieldObject {
    protected readonly helpSelector = '[data-component="help"]';

    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        super({ fieldType: 'switch', identifier, lookupStrategy, context });
    }

    // eslint-disable-next-line class-methods-use-this
    async clickOnSwitch() {
        await utils.waitForElementToExist({
            name: 'switch',
            selector: `${this.cssSelector} ${utils.getElementTypeSelector(utils.fieldTypes.switch)}`,
        });
        await this.scrollTo();
        await this.jsClick({ cssSelector: 'input', ignoreContext: false, skipVisibilityCheck: true });
        await waitForPromises(100, 'click on switch');
    }

    async setSwitchToStatus(status: string) {
        const expectedSelectionStatus = status === 'ON';
        const selectorToUse = this.getSelectorForOperation('div[type]', false);
        const element = await this.find('input');
        await this.scrollTo();
        if ((await element.isSelected()) !== expectedSelectionStatus) {
            await this.clickOnSwitch();
            await waitForPromises(500, 'click on switch');
        }

        try {
            await browser.waitUntil(
                async () => {
                    const value = (await element.isSelected());
                    return expectedSelectionStatus === value;
                },
                {
                    timeout: this.valueCheckTimeout,
                },
            );
        } catch {
            await browser.takeScreenshot();
            throw new Error(`The switch Value "${status}" could not be found.\nSelector: ${selectorToUse}`);
        }
    }

    async expectStatusToBe(status: string) {
        const expectedSelectionStatus = status === 'ON';
        const element = await this.find('input');
        await browser.waitUntil(
            async () => {
                const value = (await element.isSelected());
                return expectedSelectionStatus === value;
            },
            {
                timeout: this.valueCheckTimeout,
            },
        );
    }

    async expectTitleHelpToBe(toBe: string) {
        let result = '';
        try {
            await browser.waitUntil(
                async () => {
                    const element = await this.find(this.helpSelector);
                    await element.click();
                    const describedByAttributeValue = await element.getAttribute('aria-describedby');
                    const tooltipElement = await this.find(`[id="${describedByAttributeValue}"]`, true);
                    result = await tooltipElement.getText();
                    return result === toBe;
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Expected value: ${toBe}, actual: ${result}. \nSelector: ${this.helpSelector}`);
        }
    }

    async expectLargeSize() {
        const element = await this.find('input');
        try {
            await browser.waitUntil(
                async () => {
                    const elementWidth = await element.getSize('width');
                    const elementHeight = await element.getSize('height');
                    return elementWidth === 77.5 && elementHeight === 44;
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Expected element to be in large size: ${element.selector}`);
        }
    }
}
