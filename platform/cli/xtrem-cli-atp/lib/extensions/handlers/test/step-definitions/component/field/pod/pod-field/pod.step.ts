import { Then, When } from '@cucumber/cucumber';
import * as utils from '../../../../step-definitions-utils';
import pageModel from '../../../main-page';
import * as StaticStore from '../../../static-store';
import { PodObject, getPodField } from './pod-object';

//
/* -------------------------------static store step definitions -----------------------------*/
//
Then(
    /^the helper text of the (pod|vital pod|dynamic-pod) field is "(.*)"$/,
    async (fieldType: utils.FieldTypes, value: string) => {
        const field = StaticStore.getStoredField(fieldType);
        await field.loseFocusForHelperText();
        await field.expectPodHelperText(value);
    },
);

Then(
    /^the helper text of the (pod|vital pod|dynamic-pod) field is (displayed|hidden)$/,
    async (fieldType: utils.FieldTypes, cssState: 'displayed' | 'hidden') => {
        const field = StaticStore.getStoredField(fieldType);
        await field.loseFocusForHelperText();
        await field.expectPodHelperTextToBeDisplayed(cssState === 'hidden');
    },
);

Then(
    /^the (pod|vital pod|dynamic-pod) field is (enabled|disabled)$/,
    async (fieldType: utils.FieldTypes, cssState: 'enabled' | 'disabled') => {
        const field = StaticStore.getStoredField(fieldType);
        await field.loseFocus();
        await field.expectPodToBeEnabled(cssState === 'disabled');
    },
);

Then(/^the (pod|vital pod|dynamic-pod) field is read-only$/, async (fieldType: utils.FieldTypes) => {
    const field = StaticStore.getStoredField(fieldType);
    await field.loseFocus();
    await field.expectPodToBeReadOnly();
});

Then(
    /^the title of the (pod|vital pod|dynamic-pod) field is (displayed|hidden)$/,
    async (fieldType: utils.FieldTypes, cssState: 'displayed' | 'hidden') => {
        const field = StaticStore.getStoredField(fieldType);
        await field.loseFocus();
        await field.expectPodTitleToBeDisplayed(cssState === 'hidden');
    },
);

Then(
    /^the title of the (pod|vital pod|dynamic-pod) field is "(.*)"$/,
    async (fieldType: utils.FieldTypes, value: string) => {
        const field = StaticStore.getStoredField(fieldType);
        await field.loseFocus();
        await field.expectPodTitle(value);
    },
);

When(
    /^the user clicks the "(.*)" button of the (pod|dynamic-pod|vital pod) field$/,
    async (name: string, fieldType: utils.FieldTypes) => {
        const field = StaticStore.getStoredField(fieldType) as PodObject;
        await pageModel.waitForFinishLoading();
        await field.selectPodBtn(name);
    },
);

When(
    /^the user clicks the "(.*)" icon of the (pod|vital pod) field$/,
    async (name: string, fieldType: utils.FieldTypes) => {
        const field = StaticStore.getStoredField(fieldType) as PodObject;
        await pageModel.waitForFinishLoading();
        await field.selectPodIcon(name);
    },
);

When(
    /^the user clicks the "(.*)" action of the (pod|vital pod) field$/,
    async (actionName: string, fieldType: utils.FieldTypes) => {
        const field = StaticStore.getStoredField(fieldType) as PodObject;
        await field.loseFocus();
        await field.dropDownMenu.selectMenuAction(actionName);
    },
);

When(
    /^the action "(.*)" of the (pod|vital pod) field is (enabled|disabled)$/,
    async (expectedAction: string, fieldType: utils.FieldTypes, cssState: 'enabled' | 'disabled') => {
        const field = StaticStore.getStoredField(fieldType) as PodObject;
        await field.loseFocus();
        await field.dropDownMenu.expectDropdownMenuActionToBeEnabled(expectedAction, cssState);
    },
);

When(
    /^the action "(.*)" of the (pod|vital pod) field is (displayed|hidden)$/,
    async (expectedAction: string, fieldType: utils.FieldTypes, cssState: 'displayed' | 'hidden') => {
        const field = StaticStore.getStoredField(fieldType) as PodObject;
        await field.loseFocus();
        await field.dropDownMenu.expectDropdownMenuActionToBeDisplayed(expectedAction, cssState === 'hidden');
    },
);

When(
    /^the (pod|vital pod|dynamic-pod) field is (empty|not empty)$/,
    async (fieldType: utils.FieldTypes, cssState: 'empty' | 'not empty') => {
        const field = <PodObject>StaticStore.getStoredField(fieldType);
        await field.loseFocus();
        await field.expectPodToBeEmpty(cssState === 'not empty');
    },
);

When(
    /^the (pod|vital pod|dynamic-pod) field header container value is "(.*)"$/,
    async (fieldType: utils.FieldTypes, value: string) => {
        const field = <PodObject>StaticStore.getStoredField(fieldType);
        await field.loseFocus();
        await field.expectPodContainerValue(value);
    },
);

/* -------------------------------classic step definitions -----------------------------*/

Then(
    /^the "(.*)" (bound|labelled) (pod||vital pod|dynamic-pod) field on (the main page|a modal|a full width modal|the detail panel|the sidebar) is (displayed|hidden)$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        context: utils.ElementContext,
        cssState: 'displayed' | 'hidden',
    ) => {
        const field = getPodField({ fieldType, identifier, lookupStrategy, context }) as PodObject;
        await field.loseFocus();
        await utils.expectElementToBeDisplayed({ selector: field.cssSelector, reverse: cssState === 'hidden' });
    },
);
