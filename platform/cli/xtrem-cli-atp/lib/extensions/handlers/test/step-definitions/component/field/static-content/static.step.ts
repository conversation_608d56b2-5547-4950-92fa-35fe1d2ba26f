import { Then } from '@cucumber/cucumber';
import * as utils from '../../../step-definitions-utils';
import pageModel from '../../main-page';
import * as StaticStore from '../../static-store';
import { FieldObject } from '../field-object';

Then(/^the value of the static-content field is$/, async (value: string) => {
    const fieldType = utils.fieldTypes.staticContent;
    await pageModel.waitForFinishLoading();
    const storefield = <FieldObject>StaticStore.getStoredField(fieldType);
    await storefield?.checkValue(fieldType, value);
});
