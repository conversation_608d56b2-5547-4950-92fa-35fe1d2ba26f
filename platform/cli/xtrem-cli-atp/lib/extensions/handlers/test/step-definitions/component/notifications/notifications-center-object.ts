import { waitForPromises } from '../field/wait-util';
import { Sidebar } from '../sidebar/sidebar-object';

export class NotificationsCenter extends Sidebar {
    async clickNotificationsIcon() {
        const notificationButton = await this.find(
            '.xe-actions .xe-icon-button.xe-icon-button-link-notification',
            true,
        );
        await notificationButton.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found.\nSelector: ${this.cssSelector})`,
        });
        await notificationButton.click();
        await waitForPromises(800, 'click action');
    }
}
