import * as StaticStore from '../../../static-store';
import { TableObject } from '../table-object';

export interface SelectedRowDetails {
    isFloatingRow: boolean;
    rowNumber: number;
}

export const getTableObject = (): TableObject => {
    const tableObject = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
    return tableObject;
};

export const getSelectedRow = (): SelectedRowDetails => {
    const selectRow = StaticStore.getStoredObject<SelectedRowDetails>(StaticStore.StoredKeys.ROW);
    return selectRow;
};

export const rowIndexSelector = (rowNumber: number, isFloatingRow?: boolean): string => {
    const rowIndex = isFloatingRow ? 't-0' : (rowNumber - 1).toString();
    return `div[row-index="${rowIndex}"]`;
};

export function handleScrollError({
    error,
    rowNum,
    columnName,
    field,
}: {
    error: Error;
    rowNum: number | null;
    columnName: string;
    field: {
        cssSelector: string;
    };
}): never {
    const errorMessages = [
        'Expected column could not be found:',
        'Expected row could not be found:',
        'Could not scroll to element:',
    ];

    if (errorMessages.some(message => error.message.includes(message))) {
        throw new Error(
            `Expected row "${rowNum}" and "${columnName}" nested field could not be found.\nSelector: ${field.cssSelector}`,
        );
    }
    throw error;
}
