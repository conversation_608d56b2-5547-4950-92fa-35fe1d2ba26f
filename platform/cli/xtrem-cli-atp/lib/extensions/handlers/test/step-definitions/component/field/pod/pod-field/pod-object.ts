import * as utils from '../../../../step-definitions-utils';
import { DropDownMenuObject } from '../../../dropdown-menu-object';
import { FieldObject } from '../../field-object';
import { waitForPromises } from '../../wait-util';

export const getPodField = ({
    fieldType,
    identifier,
    lookupStrategy,
    context,
}: {
    fieldType: utils.FieldTypes;
    identifier: string;
    lookupStrategy: utils.LookupStrategy;
    context?: utils.ElementContext;
}): PodObject => {
    switch (fieldType) {
        case utils.fieldTypes.pod:
            return new PodObject({ fieldType: 'pod', identifier, lookupStrategy, context });
        case utils.fieldTypes.vitalPod:
            return new PodObject({ fieldType: 'vital-pod', identifier, lookupStrategy, context });
        case utils.fieldTypes.dynamicPod:
            return new PodObject({ fieldType: 'dynamic-pod', identifier, lookupStrategy, context });
        default:
            throw new Error(`Type ${fieldType} not implemented yet`);
    }
};

export class PodObject extends FieldObject {
    public readonly dropDownMenu: DropDownMenuObject;

    constructor({
        fieldType,
        identifier,
        lookupStrategy,
        context,
    }: {
        fieldType: string;
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        super({ fieldType, identifier, lookupStrategy, context });
        this.dropDownMenu = new DropDownMenuObject(this.cssSelector);
    }

    protected readonly podTitle = `${utils.getDataTestIdSelector({ domSelector: 'h3', dataTestIdValue: 'e-pod-title' })} .e-pod-title-text`;

    protected readonly podHelperText = utils.getDataTestIdSelector({
        domSelector: 'span',
        dataTestIdValue: 'e-field-helper-text',
    });

    // Override loseFocus to inject a waitForPromises
    override async loseFocus() {
        await waitForPromises(300, 'before lose focus override');
        await super.loseFocus();
    }

    async expectPodToBeEnabled(reverse = false) {
        const element = await this.get();
        await browser.waitUntil(
            async () => {
                const getClass = await element.getAttribute('class');
                const isDisabled = getClass.split(' ').includes('e-disabled');
                return reverse ? isDisabled : !isDisabled;
            },
            {
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected Element to be ${reverse ? 'disabled' : 'enabled'}.\nSelector: ${
                    this.cssSelector
                }`,
            },
        );
    }

    async expectPodToBeReadOnly() {
        const element = await this.get();
        try {
            await browser.waitUntil(
                async () => {
                    const classes = await element.getAttribute('class');
                    const readOnly = classes.includes('e-read-only');
                    return readOnly;
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            throw new Error(`Expected Element to be read-only.\nSelector: ${this.cssSelector}`);
        }
    }

    async expectPodTitle(value: string) {
        await this.expectTextContent({ toBe: value, ignoreCase: false, cssSelector: this.podTitle });
    }

    async expectPodHelperText(value: string) {
        await this.expectTextContent({ toBe: value, ignoreCase: false, cssSelector: this.podHelperText });
    }

    async expectPodTitleToBeDisplayed(reverse = false) {
        await utils.expectElementToBeDisplayed({ selector: `${this.cssSelector} ${this.podTitle}`, reverse });
    }

    async expectPodHelperTextToBeDisplayed(reverse = false) {
        await utils.expectElementToBeDisplayed({ selector: `${this.cssSelector} ${this.podHelperText}`, reverse });
    }

    async selectPodBtn(name: string) {
        const addNewItemBtn = await this.find(
            utils.getDataTestIdSelector({ domSelector: 'button', dataTestIdValue: 'e-pod-add-new' }),
        );
        await utils.waitForElementToBeDisplayed({
            name: `"${name}" button`,
            selector: addNewItemBtn.selector.toString(),
        });
        const addNewItemBtnTxt = await addNewItemBtn.getText();
        await addNewItemBtn.isExisting();
        await addNewItemBtn.isDisplayed();
        if (utils.formatString(addNewItemBtnTxt) === utils.formatString(name)) {
            await browser.execute(
                `var element = document.querySelector('${addNewItemBtn.selector}');
                if (element){
                    element.click();
                }`,
            );
            await waitForPromises(300, 'wait add new item button click');
            await addNewItemBtn.waitForClickable({ reverse: true });
            return;
        }
        throw new Error(
            `Expected element could not be found: "${name}" button.\nSelector: ${addNewItemBtn.selector.toString()}`,
        );
    }

    async selectPodIcon(name: string) {
        const iconBtn = await this.find(
            utils.getDataTestIdSelector({
                domSelector: 'button',
                dataTestIdValue: `e-pod-${utils.formatString(name)}`,
            }),
        );
        await utils.waitForElementToBeDisplayed({ name: `"${name}" icon`, selector: iconBtn.selector.toString() });
        await iconBtn.click();
    }

    expectPodToBeEmpty = async (reverse = false) => {
        try {
            await browser.waitUntil(
                async () => {
                    const podBody = await this.find('.e-pod-body');
                    const podBodyisDisplayed = await podBody.isDisplayed();
                    const podAdd = await this.find('.e-pod-add');
                    const podAddisDisplayed = await podAdd.isDisplayed();
                    return reverse
                        ? podBodyisDisplayed && !podAddisDisplayed
                        : !podBodyisDisplayed && podAddisDisplayed;
                },
                {
                    timeout: this.valueCheckTimeout,
                    timeoutMsg: `Expected Element to be ${reverse ? 'not empty' : 'empty'}.\nSelector: ${
                        this.cssSelector
                    }`,
                },
            );
        } catch (error) {
            throw new Error(
                `Expected Element to be ${reverse ? 'not empty' : 'empty'}.\nSelector: ${this.cssSelector}`,
            );
        }
    };

    async expectPodContainerValue(value: string) {
        const podContainer = await this.find('[data-element="header-container"] h1');
        await utils.waitForElementToBeDisplayed({ name: 'pod container', selector: podContainer.selector.toString() });
        await this.expectTextContent({
            toBe: value,
            ignoreCase: false,
            cssSelector: podContainer.selector.toString(),
            ignoreContext: true,
        });
    }
}
