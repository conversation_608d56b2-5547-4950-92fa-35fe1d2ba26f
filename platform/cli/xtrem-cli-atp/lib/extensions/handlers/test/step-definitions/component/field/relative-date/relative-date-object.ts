import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';

export class RelativeDateFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'relative-date', identifier, lookupStrategy, context });
    }

    override async expectValue({
        toBe,
        cssSelector,
        ignoreContext = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        await this.expectTextContent({ toBe, ignoreCase: false, cssSelector, ignoreContext });
    }
}
