import { addAttachment } from '@wdio/allure-reporter';
import { Key } from 'webdriverio';
import { waitForElementToBeDisplayed, waitForElementToExist } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { FieldObject } from '../field-object';

export class CodeEditorFieldObject extends FieldObject {
    override async write({ content }: { content: string }) {
        const line = await this.find('.view-lines.monaco-mouse-cursor-text .view-line');
        await line.click();
        await browser.keys(content);
        // Code added to retain the input value.
        // eslint-disable-next-line no-void
        void this.click('.monaco-scrollable-element canvas.decorationsOverviewRuler');
    }

    override async clearInput({ ignoreContext = false }: { ignoreContext?: boolean }) {
        const cssSelector = '.view-lines.monaco-mouse-cursor-text .view-line';
        const line = await this.find(cssSelector, ignoreContext);
        await waitForElementToBeDisplayed({ name: 'line', selector: line.selector as string });
        let lineCount = (await this.findAll(cssSelector, ignoreContext)).length;

        while (lineCount > 1) {
            await line.click();
            await browser.keys([Key.Ctrl, Key.Home]);
            await browser.pause(100);
            await browser.keys([Key.Shift, Key.Ctrl, Key.End]);
            await browser.pause(100);
            await browser.keys(Key.Delete);
            lineCount = (await this.findAll(cssSelector, ignoreContext)).length;
        }

        if (lineCount === 1) {
            await line.click();
            const words = await line.getText();
            let wordsCount = words.length;
            while (wordsCount > 0) {
                await browser.keys(Key.Backspace);
                await browser.keys(Key.Delete);
                await browser.pause(50);
                wordsCount = (await line.getText()).length;
            }
        }
    }

    async searchSuggestions(value: string) {
        const suggestionsWidget = await this.find('.suggest-widget');
        let currentText;

        if (/(^\[\d+\]$|^\d+$)/.test(value)) {
            const element = await suggestionsWidget.$('.monaco-list-row.show-file-icons.string-label.focused');
            currentText = await element.getText();
            if (currentText === '[]') {
                currentText = value;
            }
            await browser.keys(value);
        } else {
            await browser.keys(value);
            const element = await suggestionsWidget.$('.monaco-list-row.show-file-icons.string-label.focused');
            currentText = await element.getText();
        }
        return currentText;
    }

    async expectedSuggestions(expectedLine: string) {
        const expectedSegments = expectedLine.split('.');

        await browser.keys([Key.Ctrl, Key.Space]);
        const result = [];

        /* eslint-disable no-await-in-loop, no-restricted-syntax */
        for (const value of expectedSegments) {
            result.push(await this.searchSuggestions(value));
            await browser.keys('.');
        }
        /* eslint-enable */

        const resultLine = result.join('.');

        if (resultLine !== expectedLine) {
            throw new Error(`Expected value: ${expectedLine}, actual: ${resultLine}, selector: ${this.cssSelector}`);
        }
    }

    async writeByLineNumber(content: string, lineNumber: number) {
        const line = await this.find(`.view-lines.monaco-mouse-cursor-text .view-line:nth-child(${lineNumber})`);
        await line.click();
        await browser.keys(content);
    }

    async insertLine(lineNumber: number) {
        const line = await this.find(`.margin-view-overlays div:nth-child(${lineNumber}) .line-numbers`);
        await line.click();
        // eslint-disable-next-line class-methods-use-this
        await browser.keys(Key.ArrowRight);
        await browser.keys(Key.Enter);
    }

    async expectSuggestionDetailsToBeVisible(expectedDetails: string, suggestion: string) {
        await browser.keys([Key.Ctrl, Key.Space]);

        const currentText = await this.searchSuggestions(suggestion);

        if (currentText === suggestion) {
            await browser.keys([Key.Ctrl, Key.Space]);

            const helpDetails = await (await this.find('.details p.docs.markdown-docs')).getText();

            if (!helpDetails.includes(expectedDetails)) {
                throw new Error(`Details not found: ${expectedDetails}`);
            }
        }
    }

    override async expectValue({
        toBe,
        ignoreContext = false,
        valueType = 'value',
        isContains = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
        valueType?: string;
        isContains?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation('.view-lines.monaco-mouse-cursor-text', ignoreContext);
        const element = await browser.$(selectorToUse);
        await waitForElementToExist({ name: 'element', selector: element.selector as string });
        await waitForElementToBeDisplayed({ name: 'element', selector: element.selector as string });
        let currentValue = '';
        const formatText =
            valueType === 'value'
                ? (text: string) =>
                      text
                          .replace(/(\r\n|\r|\n)/g, ' ')
                          .replace(/\u00A0/g, ' ')
                          .replace(/\\n/g, ' ')
                          .trim()
                : (html: string): string => html.replace(/\s+/g, '');
        try {
            await browser.waitUntil(
                async () => {
                    currentValue = formatText(await element.getText());
                    if (isContains) {
                        return (currentValue || '').includes(formatText(toBe) || '');
                    }
                    return (currentValue || '') === (formatText(toBe) || '');
                },
                { timeout: this.valueCheckTimeout },
            );
            addAttachment('Expected value', `Expected ${valueType}:\n${toBe}`, 'text/plain');
        } catch (error) {
            await browser.takeScreenshot();
            const errorDetails = `Expected ${valueType}: "${toBe}"\n\nActual ${valueType}: "${currentValue}"\n\nSelector: ${selectorToUse}`;
            addAttachment('Error details', errorDetails, 'text/plain');
            throw new Error(errorDetails);
        }
    }

    override async setElementValueToStore({ keyValue }: { keyValue: string }) {
        const selectorToUse = this.getSelectorForOperation('.view-lines.monaco-mouse-cursor-text');
        const element = await browser.$(selectorToUse);
        await element.waitForExist({
            timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
            timeout: this.valueCheckTimeout,
        });
        await element.waitForDisplayed({
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
            timeout: this.valueCheckTimeout,
        });
        let textContent = '';
        try {
            textContent = (await element.getText())
                .replace(/(\r\n|\r|\n)/g, ' ')
                .replace(/\u00A0/g, ' ')
                .trim();
            StaticStore.storeObject(keyValue, textContent);
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Value not set".\nSelector: ${selectorToUse}`);
        }
    }
}
