import { Then, When } from '@cucumber/cucumber';
import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { NavigationBarObject } from '../navigation-bar/navigation-bar-object';
import { BusinessActionObject, BusinessActionStatus } from './business-action-object';

When(
    /^the user clicks the "([$A-Za-z0-9\s]*)" (bound|labelled) business action button on (the main page|a modal|a full width modal|a modal header|the sidebar|the mobile sidebar|the detail panel|the navigation panel)$/,
    async (identifier: string, lookupStrategy: LookupStrategy, context: ElementContext) => {
        const businessAction = new BusinessActionObject({ identifier, lookupStrategy, context });
        const navigationBar = new NavigationBarObject();
        await businessAction.dismissAllNotification();
        await navigationBar.closeHelpCenter();
        await businessAction.selectBusinessAction(context);
    },
);

Then(
    /^the "([$A-Za-z0-9\s]*)" (bound|labelled) business action button on (the main page|a modal|a modal header|a full width modal|the detail panel|the navigation panel) is (disabled|enabled|hidden|visible)$/,
    async (
        identifier: string,
        lookupStrategy: LookupStrategy,
        context: ElementContext,
        status: BusinessActionStatus,
    ) => {
        const businessAction = new BusinessActionObject({ identifier, lookupStrategy, context });
        try {
            await businessAction.expectStatusToBe(status);
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(error);
            throw error;
        }
    },
);
