import { Then, When } from '@cucumber/cucumber';
import { ElementContext, ExpandedOrCollapsed } from '../../../step-definitions-utils';
import { AccordionObject } from './accordion-object';

Then(
    /^the accordion with title "(.*)" is (expanded|collapsed)$/,
    async (title: string, toggleStatus: 'expanded' | 'collapsed') => {
        const accordion = new AccordionObject('the main page' as ElementContext);
        await accordion.setAccordionSelectorByTitle(title);
        await accordion.expectAccordionExpandedCollapsed(toggleStatus);
    },
);

When(
    /^the user (expands|collapses) accordion with title "(.*)" on (the main page|the detail panel|a modal| a full width modal)$/,
    async (toggle: ExpandedOrCollapsed, title: string, context: ElementContext) => {
        const accordion = new AccordionObject(context);
        await accordion.setAccordionSelectorByTitle(title);
        await accordion.toggle(toggle);
    },
);
