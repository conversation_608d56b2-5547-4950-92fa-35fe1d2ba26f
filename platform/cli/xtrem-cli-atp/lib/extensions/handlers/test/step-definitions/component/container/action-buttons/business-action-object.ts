import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import { waitForPromises } from '../../field/wait-util';
import { testGlobals } from '../../test-globals';

export enum BusinessActionStatus {
    disabled = 'disabled',
    enabled = 'enabled',
    visible = 'visible',
    hidden = 'hidden',
}

export class BusinessActionObject extends AbstractPageObject {
    private readonly contextSelector: string;

    private context: utils.ElementContext | undefined;

    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        let selector = '';
        if (lookupStrategy !== utils.LookupStrategy.NONE) {
            selector = utils.getLookupStrategySelector({
                fieldType: 'business-action',
                lookupStrategy,
                identifier,
                domSelector: 'span',
            });
        } else {
            selector = utils.getPageBusinessActionSelector({
                fieldType: 'business-actions',
                identifier,
                domSelector: 'span',
            });
        }

        super(selector);

        this.contextSelector = context ? utils.getContextSelector(context) : '';
    }

    async selectBusinessAction(context: utils.ElementContext): Promise<void> {
        let selectorToUse: string;
        let params: any[] = [];

        const isInModal =
            context === utils.ElementContext.MODAL ||
            context === utils.ElementContext.FULL_MODAL ||
            context === utils.ElementContext.MODAL_HEADER;

        if (testGlobals.device !== 'mobile' && this.context === utils.ElementContext.SIDEBAR) {
            selectorToUse = `${this.contextSelector} ${this.cssSelector} button`;
            await utils.waitForElementToExist({
                name: 'Waiting for business action button to exist',
                selector: selectorToUse,
            });

            params = [selectorToUse, true];
        } else {
            selectorToUse = this.cssSelector;
        }

        await this.scrollTo(...params);
        if (!isInModal) {
            await waitForPromises(500, 'business action button scroll to');
        }
        await this.expectStatusToBe(BusinessActionStatus.visible, context);
        await this.expectStatusToBe(BusinessActionStatus.enabled, context);
        // Dismiss all notifications before clicking the business action button in case a new notification is displayed
        await this.dismissAllNotification();
        await this.click(...params);
        await waitForPromises(500, 'business action button click');
    }

    private openMoreMenu = async (): Promise<void> => {
        const moreButton = await this.find(
            `${this.contextSelector} [data-testid="e-page-business-actions-more"]`,
            true,
        );

        if (await moreButton.isDisplayed()) {
            await moreButton.click();
        }
    };

    private closeMoreMenu = async (): Promise<void> => {
        const additionalButtonsContainer = await this.find('[data-element="additional-buttons"]', true);
        if (await additionalButtonsContainer.isDisplayed()) {
            // Just click somewhere else to close the menu
            await super.click('.e-header', true);
            await browser.pause(1000);
        }
    };

    override async click(cssSelector?: string | undefined, ignoreContext?: boolean): Promise<void> {
        await this.openMoreMenu();
        const buttonWithContext = await this.find(
            `${this.contextSelector} ${this.cssSelector} ${cssSelector || ''}`,
            true,
        );
        if (await buttonWithContext.isDisplayed()) {
            await super.click(`${this.contextSelector} ${this.cssSelector} ${cssSelector || ''}`, true);
        } else {
            await super.click(cssSelector, ignoreContext);
        }
        await this.closeMoreMenu();
    }

    async expectStatusToBe(status: BusinessActionStatus, context?: utils.ElementContext): Promise<void> {
        await this.openMoreMenu();

        let selector;
        if (context === utils.ElementContext.MODAL) {
            selector =
                status === BusinessActionStatus.enabled || status === BusinessActionStatus.disabled
                    ? `[data-component="modal"] ${this.cssSelector} button`
                    : `[data-component="modal"] ${this.cssSelector}`;
        } else {
            selector =
                status === BusinessActionStatus.enabled || status === BusinessActionStatus.disabled
                    ? `${this.cssSelector} button`
                    : this.cssSelector;
        }

        switch (status) {
            case BusinessActionStatus.visible:
                await this.expectToBeDisplayed(selector);
                break;
            case BusinessActionStatus.hidden:
                await this.expectToDisappear(this.cssSelector);
                break;
            case BusinessActionStatus.enabled:
                await this.expectToBeEnabled(selector);
                break;
            case BusinessActionStatus.disabled:
                await this.expectNotToBeEnabled(selector);
                break;
            default:
                throw new Error('STATUS NOT DEFINED');
        }

        await this.closeMoreMenu();
    }
}
