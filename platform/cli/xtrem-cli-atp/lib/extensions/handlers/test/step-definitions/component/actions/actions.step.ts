import { Then } from '@cucumber/cucumber';
import { Actions } from './actions-object';

Then(
    /^the "(.*)" notifications icon in the actions header is (displayed|hidden)$/,
    async (notificationState: string, cssState: 'displayed' | 'hidden') => {
        const actions = new Actions();
        await actions.expectNotificationsIconDisplayed(notificationState, cssState === 'hidden');
    },
);

Then(
    /the settings profile action in the application top bar is (displayed|hidden)$/,
    async (cssState: 'displayed' | 'hidden') => {
        const actions = new Actions();
        await actions.expectProfilePictureIconDisplayed(cssState === 'hidden');
    },
);

Then('the user clicks the settings profile action in the application top bar', async () => {
    const actions = new Actions();
    await actions.clickProfileIcon();
});

Then(
    /the settings profile action in the application top bar is (opened|closed)$/,
    async (cssState: 'opened' | 'closed') => {
        const actions = new Actions();
        await actions.expectProfileDisplayed(cssState === 'closed');
    },
);
