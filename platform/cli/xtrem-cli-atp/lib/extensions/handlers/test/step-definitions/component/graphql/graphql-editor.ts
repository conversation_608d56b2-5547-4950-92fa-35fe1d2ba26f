import { expect } from 'chai';
import { Key } from 'webdriverio';
import { runtimeParameters } from '../../../../../../parameters';
import AbstractPageObject from '../abstract-page-object';
import * as StaticStore from '../static-store';

export class GraphQlEditor extends AbstractPageObject {
    constructor() {
        super('.graphiql-sessions');
    }

    async writeToEditorPanel(query: string) {
        const editor = await this.find('section.graphiql-query-editor div.CodeMirror');
        await editor.click();
        await browser.keys([Key.Ctrl, 'a']);
        await browser.keys(Key.Backspace);

        const selection = runtimeParameters.getStringOrParameter(query);
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(selection);
        await browser.execute('arguments[0].CodeMirror.setValue(arguments[1])', editor, storeValue);
    }

    async readResultPanel(): Promise<string> {
        const resultWindowSelector = 'section.result-window div.CodeMirror';
        const editor = await this.find(resultWindowSelector);
        await editor.click();

        const result: string = await browser.execute('arguments[0].CodeMirror.getValue()', editor);

        try {
            JSON.parse(result);
        } catch (error) {
            throw new Error('Unable to read results panel');
        }

        return result;
    }

    async compareResultPanel(graphqlResponse: string) {
        const graphqlResults = (await this.readResultPanel()).normalize();

        this.attachTextValue('Actual response', graphqlResults);
        const selection = runtimeParameters.getStringOrParameter(graphqlResponse);
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(selection);
        // parse both values into JSON to avoid whitespace or new line character issues

        expect(JSON.stringify(JSON.parse(graphqlResults))).to.eq(
            JSON.stringify(JSON.parse(storeValue.normalize())),
            'Expected graphQL response does not match actual graphQL response.',
        );
    }

    async getPropertyFromResultPanel(propertyName: string) {
        const results = JSON.parse(await this.readResultPanel());

        let subProperty = results;
        const propertyList = propertyName.split('.');

        // eslint-disable-next-line no-restricted-syntax
        for await (const property of propertyList) {
            let occurrence = -1;
            const occurrenceEntry = property.match(/\[\d+\]/);

            if (occurrenceEntry) {
                // eslint-disable-next-line radix
                occurrence = Number.parseInt(occurrenceEntry[0].replace(/\[|\]/g, ''));
            }

            subProperty = subProperty[property.replace(/\[\d+\]/, '')];

            if (subProperty === undefined) {
                throw new Error(
                    `Expected element could not be found: "${property}" property.\nSelector: ${propertyName}`,
                );
            }

            if (occurrence > 0) {
                if (!Array.isArray(subProperty)) {
                    throw new Error(
                        `Unable to select occurrence ${occurrence} of property ${property.replace(
                            /\[\d+\]/,
                            '',
                        )} as property is not an Array`,
                    );
                }
                subProperty = subProperty[occurrence - 1];
            }
        }

        return subProperty;
    }

    // eslint-disable-next-line require-await
    async compareProperties(
        testType: string,
        resultProperty: any,
        expectedProperty: string,
        alwaysAttachExpected = false,
    ) {
        let resultPropertyParsed: string;
        let resultPropertyReport: string;
        if (typeof resultProperty === 'object') {
            resultPropertyReport = JSON.stringify(resultProperty, undefined, 1);
            resultPropertyParsed = resultPropertyReport.replace(/\n/gm, '').replace(/\r/gm, '').normalize().trim();
        } else {
            resultPropertyReport = resultProperty.toString();
            resultPropertyParsed = resultPropertyReport.normalize().trim();
        }

        let expectedPropertyParsed: string;
        let expectedPropertyReport: string;
        const selection = runtimeParameters.getStringOrParameter(expectedProperty);
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(selection);
        // eslint-disable-next-line no-param-reassign
        expectedProperty = storeValue;

        if (expectedProperty.startsWith('{')) {
            expectedPropertyReport = JSON.stringify(JSON.parse(expectedProperty), undefined, 1);
            expectedPropertyParsed = expectedPropertyReport.replace(/\n/gm, '').normalize().trim();
        } else {
            expectedPropertyParsed = expectedProperty.replace(/\n/gm, ' ').replace(/\r/gm, '').normalize().trim();
            expectedPropertyReport = expectedProperty;
        }

        if (alwaysAttachExpected) {
            this.attachTextValue('Expected value', expectedPropertyReport);
        }

        const resultPropertyParsedAsNumber = parseFloat(resultPropertyParsed);
        const expectedPropertyParsedAsNumber = parseFloat(expectedPropertyParsed);

        try {
            switch (testType) {
                case 'is not':
                    expect(resultPropertyParsed).to.not.eq(
                        expectedPropertyParsed,
                        `Expected value: "${expectedPropertyParsed}", actual: "${resultPropertyParsed}".`,
                    );
                    break;

                case 'is':
                    expect(resultPropertyParsed).to.eq(
                        expectedPropertyParsed,
                        `Expected value: "${expectedPropertyParsed}", actual: "${resultPropertyParsed}".`,
                    );
                    break;

                case 'contains':
                    expect(resultPropertyParsed).to.contains(
                        expectedPropertyParsed,
                        `Expected value: "${expectedPropertyParsed}", actual: "${resultPropertyParsed}".`,
                    );
                    break;
                case 'lower than':
                    expect(resultPropertyParsedAsNumber).to.lessThanOrEqual(
                        expectedPropertyParsedAsNumber,
                        `Expected value: "${expectedPropertyParsed}", actual: "${resultPropertyParsed}".`,
                    );
                    break;

                case 'equal to':
                    expect(resultPropertyParsedAsNumber).to.equal(
                        expectedPropertyParsedAsNumber,
                        `Expected value: "${expectedPropertyParsed}", actual: "${resultPropertyParsed}".`,
                    );
                    break;

                case 'greater than':
                    expect(resultPropertyParsedAsNumber).to.greaterThanOrEqual(
                        expectedPropertyParsedAsNumber,
                        `Expected value: "${expectedPropertyParsed}", actual: "${resultPropertyParsed}".`,
                    );
                    break;

                default:
                    throw new Error(`Error: Unknown test type ${testType}!`);
            }
        } catch (exc) {
            // eslint-disable-next-line global-require
            if (exc instanceof require('chai').AssertionError) {
                if (!alwaysAttachExpected) {
                    this.attachTextValue('Expected value', expectedPropertyReport);
                }
                this.attachTextValue('Actual value', resultPropertyReport);
            }
            throw exc;
        }
    }

    async clickEditorButton(buttonName: string) {
        switch (buttonName) {
            case 'Run':
                // eslint-disable-next-line no-case-declarations
                const runButton = await this.find('.graphiql-toolbar button.graphiql-execute-button');
                await runButton.click();

                await browser.waitUntil(
                    async () => {
                        return !(await (await $('div.graphiql-response div.graphiql-spinner')).isExisting());
                    },
                    { timeoutMsg: 'Query took too long to run' },
                );
                break;

            case 'Docs':
                // eslint-disable-next-line no-case-declarations
                const docsButton = await this.find('.graphiql-sidebar button.docExplorerShow');
                await docsButton.click();
                break;
            default:
                // eslint-disable-next-line no-case-declarations
                const toolbarButtons: WebdriverIO.ElementArray = await this.findAll(
                    '.graphiql-toolbar button.graphiql-toolbar-button',
                );
                // eslint-disable-next-line no-case-declarations
                let found = false;
                // eslint-disable-next-line no-restricted-syntax
                for (const toolbarButton of toolbarButtons) {
                    if ((await toolbarButton.getText()) === buttonName) {
                        await toolbarButton.click();
                        found = true;
                        return;
                    }
                }

                if (!found) {
                    throw new Error(`Expected button could not be found: "${buttonName}"`);
                }
                break;
        }
    }
}

export const graphQLEditor = new GraphQlEditor();
