import { Then, When } from '@cucumber/cucumber';
import { waitForPromises } from '../../field/wait-util';
import { DialogDisplayTypes, DialogObject, DialogSectionTypes, DialogTypes, InputDialogLevel } from './dialog-object';

Then(
    /^(an error|an info|a success|a warn) dialog appears( on the main page| on the sidebar| on a full width modal)?$/,
    async (inputDialogLevel: InputDialogLevel, dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        const dialogLevel = inputDialogLevel.split(' ')[1];
        await dialog.expectDialogToAppear(dialogLevel);
    },
);

Then(
    /^the user clicks the "([$A-Za-z0-9\s]*)" labelled more actions button( on the main page| on the sidebar| on a full width modal)$/,
    async (actionName: string, dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        await dialog.dropDownMenu.selectMenuAction(actionName, '.e-sidebar-dropdown-action-container');
    },
);

Then(
    /^an info dialog disappears( on the main page| on the sidebar| on a full width modal)?$/,
    async (dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        await dialog.expectDialogToDisappear();
    },
);

Then(
    /^the text in the (header|header pill label|body) of the dialog is "(.*)"( on the main page| on the sidebar| on a full width modal)?$/,
    async (dialogSectionType: DialogSectionTypes, value: string, dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        await dialog.expectDialogText({ dialogText: value, dialogSectionType });
    },
);

Then(
    /^the text in the (header|body) of the dialog is$/,
    async (dialogSectionType: DialogSectionTypes, value: string) => {
        const dialog = new DialogObject();
        await dialog.expectDialogText({ dialogText: value, dialogSectionType });
    },
);

Then(
    /^the text in the body of the dialog contains "(.*)"( on the main page| on the sidebar| on a full width modal)?$/,
    async (value: string, dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        await dialog.dialogTextContain(value);
    },
);

Then(
    /^the user extracts the value from the confirmation dialog starting at (\d+) for (\d+) characters and stores it in key "(.*)"$/,
    async (startPosition: number, numberOfCharacters: number, key: string) => {
        const dialog = new DialogObject();
        await dialog.dialogExtractCharacter(startPosition, numberOfCharacters, key);
    },
);

Then(
    /^the text in the (header|body) of the error dialog is "(.*)"( on the main page| on the sidebar| on a full width modal)?$/,
    async (dialogSectionType: DialogSectionTypes, value: string, dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        await dialog.expectTextRex({ toBe: value, dialogSectionType, inputDialogLevel: 'an error' });
    },
);

Then(
    /^the text in the body of the error dialog contains "(.*)"( on the main page| on the sidebar| on a full width modal)?$/,
    async (value: string, dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        await dialog.dialogTextContain(value, 'an error');
    },
);

Then(
    /^the "(.*)" button of the dialog is (displayed|hidden)( on the main page| on the sidebar| on a full width modal)?$/,
    async (buttonType: string, cssState: 'displayed' | 'hidden', dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        await dialog.expectDialogButtonToBeDisplayed(buttonType, cssState === 'hidden');
    },
);

Then(
    /^the "(.*)" button of the dialog is (enabled|disabled)( on the main page| on the sidebar| on a full width modal)?$/,
    async (buttonType: string, cssState: 'enabled' | 'disabled', dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        await dialog.expectDialogButtonToBeEnabled(buttonType, cssState === 'disabled');
    },
);

When(
    /^the user clicks the Close button of the dialog( on the main page| on the sidebar| on a full width modal)?$/,
    async (dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        const dialogCloseButton = await dialog.getCloseButton();
        await dialogCloseButton.click();
        await waitForPromises(500, 'wait for dialog close');
    },
);

When(
    /^the user clicks the "(.*)" button of the( Message| Custom| Confirm| external link confirmation| Lookup)? dialog( on the main page| on the sidebar| on a full width modal)?$/,
    async (buttontext: string, buttonType?: DialogTypes, dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        const dialogButton = await dialog.getDialogButton(buttontext, buttonType);
        await dialogButton.click();
        await waitForPromises(500, 'dialog search');
    },
);

When(
    /^searches for "(.*)" in the lookup dialog( on the main page| on the sidebar| on a full width modal)?$/,
    async (query: string, dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType || DialogDisplayTypes.fullscreen);
        await dialog.searchFor(query);
        await waitForPromises(1200, 'dialog search');
    },
);

When(/^no dialogs are displayed/, async () => {
    await new DialogObject(DialogDisplayTypes.fullscreen).expectToDisappear();
    await new DialogObject(DialogDisplayTypes.sidebar).expectToDisappear();
    await new DialogObject(DialogDisplayTypes.fullscreen).expectToDisappear();
    await new DialogObject().expectToDisappear();
});

Then(
    /^the dialog title is "(.*)"( on the main page| on the sidebar| on a full width modal)?$/,
    async (expectedTitle: string, dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        await dialog.expectToAppear();
        await dialog.expectTitleDisplayed(expectedTitle);
        const actualTitle = await dialog.getTitle();
        if (expectedTitle !== actualTitle) {
            throw Error(`dialog title does't match. expected: ${expectedTitle} actual: ${actualTitle}`);
        }
    },
);

Then(
    /^the dialog subtitle is "(.*)"( on the main page| on the sidebar| on a full width modal)?$/,
    async (expectedSubtitle: string, dialogDisplayType?: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        await dialog.expectToAppear();
        const actualSubtitle = await dialog.getSubtitle();
        if (expectedSubtitle !== actualSubtitle) {
            throw Error(`dialog subtitle does't match. expected: ${expectedSubtitle} actual: ${actualSubtitle}`);
        }
    },
);

When(/^the user clicks the Close button of the sidebar$/, async () => {
    const dialog = new DialogObject(DialogDisplayTypes.sidebar);
    const dialogCloseButton = await dialog.getCloseButton();
    await dialogCloseButton.click();
});

Then(/^the user clicks the breadcrumb with text "(.*)" on a full width modal$/, async (text: string) => {
    const dialog = new DialogObject(DialogDisplayTypes.fullscreen);
    await dialog.clickBreadcrumbWithText(text);
});

Then(/^the user clicks the create button of the lookup dialog on the main page$/, async () => {
    const dialog = new DialogObject(DialogDisplayTypes.mainpage);
    await dialog.clickCreateButton();
});

Then(
    /^(a|no) (Message|Custom|Confirm) dialog is displayed( on the main page| on the sidebar| on a full width model)?$/,
    async (reverse: 'a' | 'no', dialogType: DialogTypes, dialogDisplayType: DialogDisplayTypes) => {
        const dialog = new DialogObject(dialogDisplayType);
        await dialog.expectDialogTypeToExist(dialogType, reverse === 'no');
    },
);
