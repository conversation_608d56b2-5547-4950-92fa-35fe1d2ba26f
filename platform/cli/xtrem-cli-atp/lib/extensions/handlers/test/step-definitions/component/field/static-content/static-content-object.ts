import allureReporter from '@wdio/allure-reporter';
import { runtimeParameters } from '../../../../../../../parameters';
import * as utils from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

export class StaticContentObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        super({ fieldType: 'static-content', identifier, lookupStrategy, context });
    }

    override async expectValue({
        toBe,
        cssSelector,
        ignoreContext = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        let value = '';
        let result;
        let selectorToUse = '';
        try {
            selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
            await waitForPromises(500, 'wait for result values to be loaded');
            let element = await $(selectorToUse);
            await element.waitForExist({
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
            });

            await browser.waitUntil(
                async () => {
                    element = await $(selectorToUse);
                    result = (await element.getText()) as unknown as string[];
                    value = Array.isArray(result) ? result[0] : result;
                    const expectedVal = runtimeParameters.getStringOrParameter(toBe);
                    return (value || '') === (expectedVal || '');
                },
                {
                    timeout: this.valueCheckTimeout,
                    timeoutMsg: `Expected value: \n${toBe}\nactual: \n${value}\nselector: ${selectorToUse}`,
                },
            );
            allureReporter.addAttachment('Expected value', `${toBe.toString()}`, 'text/plain');
        } catch (error) {
            allureReporter.addAttachment('Expected value', `${toBe.toString()}`, 'text/plain');
            allureReporter.addAttachment('Actual value', `${value.toString()}`, 'text/plain');
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected value: \n${toBe}\nactual: \n${value}\nselector: ${selectorToUse}`);
        }
    }
}

/*

Then(
    /^the value of the (code editor|dropdown-list|relative date|filter select|graphiql editor|multi dropdown|multi reference|numeric|reference|select|static-content|text|text area) field is$/,
    async (fieldType: utils.FieldTypes, value: string) => {
        await pageModel.waitForFinishLoading();
        const storefield = <FieldObject>StaticStore.getStoredField(fieldType);
        await storefield?.checkValue(fieldType, value);
    },
);


*/
