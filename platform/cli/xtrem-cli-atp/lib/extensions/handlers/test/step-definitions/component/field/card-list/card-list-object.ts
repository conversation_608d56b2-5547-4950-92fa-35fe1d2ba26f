/* eslint-disable @typescript-eslint/naming-convention */
import { camelCase } from 'lodash';
import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import * as StaticStore from '../../static-store';
import { testGlobals } from '../../test-globals';
import { TableObject } from '../table/table-object';
import { waitForPromises } from '../wait-util';

export interface ICardListObject {
    identifier: string;
    lookupStrategy: utils.LookupStrategy;
    context?: utils.ElementContext;
}

export class CardListObject extends AbstractPageObject {
    constructor({ identifier, lookupStrategy, context }: ICardListObject) {
        super(
            `${context ? `${utils.getContextSelector(context)} ` : ''} ${utils.getLookupStrategySelector({
                fieldType: 'table',
                lookupStrategy,
                identifier,
            })} .e-table-field-mobile`,
        );
    }

    async selectCard(rowId: string) {
        const selectedItem = await this.find(
            `[data-testid="e-card"]:${rowId === 'first' || rowId === 'last' ? `${rowId}-child` : `nth-child(${rowId})`
            }`,
            true,
        );
        await selectedItem.click();
    }

    async selectCardCheckbox({
        selection,
        rowId,
        identifier,
        lookupStrategy,
        context,
    }: {
        selection: string;
        rowId: number;
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        const select = selection === 'selects';
        await this.checkTableFieldExists({ identifier, lookupStrategy, context });
        await this.checkRowExists(rowId);
        const selectedItem = await this.find(`[data-testid="e-card"]:nth-child(${rowId}) input`, false);

        const selectionState = await selectedItem.isSelected();
        if ((select && !selectionState) || (!select && selectionState)) {
            await selectedItem.click();
        }
    }

    async selectCardCheckboxByText({
        selection,
        textLabel,
        identifier,
        lookupStrategy,
        context,
    }: {
        selection: string;
        textLabel: string;
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        const select = selection === 'selects';
        await this.checkTableFieldExists({ identifier, lookupStrategy, context });

        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(textLabel);
        const selectedItem = await this.find(`[data-label="${storeValue}"][data-testid="e-card"] input`, false);

        try {
            const selectionState = await selectedItem.isSelected();
            if ((select && !selectionState) || (!select && selectionState)) {
                await selectedItem.click();
            }
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(
                `Expected element could not be found: card list with text "${storeValue}" .\nSelector: ${selectedItem.selector}`,
            );
        }
    }

    async checkRowExists(rowId: any) {
        const rows = await this.findAll('[data-testid="e-card"]', false);
        if (rowId !== 'first' && rowId !== 'last')
            if (Number(rowId) > rows.length) {
                throw new Error(`Card "${rowId}" does not exist.`);
            }
    }

    async checkTableFieldExists({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        try {
            const table = new TableObject({ identifier, lookupStrategy, context });

            const tableCss = await browser.$(table.cssSelector);
            await tableCss.waitForDisplayed({
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected element to be displayed: ${identifier}`,
            });
        } catch (error) {
            throw new Error(`The "${identifier}" table field could not be found.`);
        }
    }

    async clickOnHeaderButton(buttonName: 'Calendar' | 'Filter') {
        let selectorToUse;

        switch (buttonName) {
            case utils.ButtonName.CALENDAR:
                selectorToUse = `${utils.getDataTestIdSelector({
                    domSelector: 'div',
                    dataTestIdValue: 'e-field-header-actions',
                })} span[data-element="calendar"]`;
                break;
            case utils.ButtonName.FILTER:
                selectorToUse = utils.getDataTestIdSelector({
                    domSelector: 'button',
                    dataTestIdValue: 'e-filter-button',
                });
                break;
            default:
                throw Error(`Unexpected button type: ${buttonName}`);
        }

        await this.click(selectorToUse, true);
    }

    async clickDropdownAction(actionName: string, cardNumber: number) {
        const element = await this.get();
        await utils.waitForElementToExist({ name: 'mobile table field', selector: `${element.selector}` });
        await utils.waitForElementToBeDisplayed({ name: 'mobile table field', selector: `${element.selector}` });

        const rows = await this.findAll('[data-testid="e-card"]', true);

        if (cardNumber > rows.length) {
            throw new Error(`Card ${cardNumber} does not exist.`);
        }

        const rowSelector = `[data-testid="e-card"]:nth-child(${cardNumber.toString()})`;
        await this.scrollTo({ selector: rowSelector, ignoreContext: true });

        const popoverContainerSelector =
            testGlobals.device === 'mobile'
                ? '[data-element="action-popover-button"]'
                : '[data-component="action-popover-button"]';

        const actionPopOverBtnSelector = await this.find(`${rowSelector} ${popoverContainerSelector}`, true);

        if ((await actionPopOverBtnSelector.isExisting()) && (await actionPopOverBtnSelector.isDisplayed())) {
            await actionPopOverBtnSelector.click();

            await waitForPromises(500, 'popover button click');

            const popOver =
                testGlobals.device === 'mobile' ? '.e-action-popover-mobile' : '[data-component="action-popover"]';
            const popOverElt = await browser.$(popOver);

            await popOverElt.waitForDisplayed({
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected element to be displayed: ${popOver}`,
            });

            const popOverBtns = await this.findAll(`${popOver} [type="button"]`, true);

            // eslint-disable-next-line no-restricted-syntax
            for (const btn of popOverBtns) {
                // eslint-disable-next-line no-await-in-loop
                const btnTitle = await btn.getText();

                if (btnTitle.toLowerCase() === actionName.toLowerCase()) {
                    // eslint-disable-next-line no-await-in-loop
                    await btn.click();
                    return;
                }
            }
        }

        const actionSelector = `${rowSelector} button[aria-label="${actionName}"]`;
        const actionBtn = await this.find(actionSelector);

        if ((await actionBtn.isExisting()) && (await actionBtn.isDisplayed())) {
            await actionBtn.click();
        }
    }

    async expectEnabledState({
        dropdownAction,
        rowId,
        state,
    }: {
        dropdownAction: string;
        rowId: string;
        state: utils.EnabledState;
    }) {
        await this.click(
            `[data-testid="e-card"]:${rowId === 'first' || rowId === 'last' ? `${rowId}-child` : `nth-child(${rowId})`
            } ${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-table-field-row-actions-icon' })}`,
        );

        await this.findOrFail(
            utils.getDataTestIdSelector({
                domSelector: 'div',
                dataTestIdValue: `e-table-field-row-action-${camelCase(dropdownAction)}-${state}`,
            }),
        );
    }
}
