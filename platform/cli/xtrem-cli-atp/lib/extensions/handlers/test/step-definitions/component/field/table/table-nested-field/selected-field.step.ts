import { Then, When } from '@cucumber/cucumber';
import { FieldTypes, LookupStrategy, waitForElementToBeFound } from '../../../../step-definitions-utils';
import * as StaticStore from '../../../static-store';
import { scrollToTableColumn, scrollToTableRow } from '../tableUtils';
import { NestedFieldObject } from './table-nested-field-object';
import { getTableObject, SelectedRowDetails } from './table-nested-field-utils';

When(
    /^the user selects the "([^"\n\r]*)" (bound|labelled) nested (date-time-range) field of the selected row in the table field$/,
    async (columnName: string, nestedLookupStrategy: LookupStrategy, fieldType: FieldTypes) => {
        const { cssSelector: tableSelector } = getTableObject();
        const { isFloatingRow, rowNumber }: SelectedRowDetails = StaticStore.getStoredObject(
            StaticStore.StoredKeys.ROW,
        );

        const field = new NestedFieldObject({
            tableSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
            fieldType,
        });
        await field.waitForTableStopLoading();

        await scrollToTableColumn({ tableSelector, columnName, lookupStrategy: nestedLookupStrategy });
        await scrollToTableRow(field.cssSelector, rowNumber);

        await waitForElementToBeFound({ name: `${columnName} nested field`, selector: field.cssSelector });
        StaticStore.storeObject(StaticStore.StoredKeys.TABLE_NESTED_FIELD, field);
    },
);

When(
    /^the user selects the "(.*)" (month|year|day) of (start|end) nested date-time-range field of the selected row in the table field$/,
    async (value: string, part: 'month' | 'year' | 'day', startOrEnd: 'start' | 'end') => {
        const field = <NestedFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE_NESTED_FIELD);
        await field.selectDateTimeRangePart({ startOrEnd, value, part });
    },
);

When(
    /^the user writes "(.*)" in time field of the (start|end) nested date-time-range field of the selected row in the table field$/,
    async (targetTime: string, startOrEnd: 'start' | 'end') => {
        const field = <NestedFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE_NESTED_FIELD);
        await field.setHoursMin(startOrEnd, targetTime);
    },
);

When(
    /^the user clicks the "(.*)" toggle button of the (start|end) nested date-time-range field of the selected row in the table field$/,
    async (toggleButton: 'AM' | 'PM', startOrEnd: 'start' | 'end') => {
        const field = <NestedFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE_NESTED_FIELD);
        await field.toggleDateRangeButton(startOrEnd, toggleButton);
    },
);

When(
    /^the user (ticks|unticks) the "(.*)" checkbox of the (start|end) nested date-time-range field of the selected row in the table field$/,
    async (checkOrUncheck: 'ticks' | 'unticks', targetString: string, startOrEnd: 'start' | 'end') => {
        const field = <NestedFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE_NESTED_FIELD);
        await field.selectDateRangeCheckbox(startOrEnd, targetString, checkOrUncheck);
    },
);

// 'start' option and 'end' option are for when the date-time-range input field is focused, will check the value of the respective input field
// 'start and end' option is for when the date-time-range field is not focused, will check the value of the whole date-time-range field
Then(
    /^the value of the (start|end|start and end) nested date-time-range field of the selected row in the table field is "(.*)"$/,
    async (startEnd: 'start' | 'end' | 'start and end', expectedValue: string) => {
        const field = <NestedFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE_NESTED_FIELD);
        await field.expectDateTimeRangeValue(startEnd, expectedValue);
    },
);

When(
    /^the user leaves the focus from the(?: start | end )?nested date-time-range field of the selected row in the table field$/,
    async () => {
        const field = <NestedFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE_NESTED_FIELD);
        await field.leaveFocusDateTimeRangeField();
    },
);

When(
    /^the user clicks the time zone field in the (start|end) nested date-time-range field of the selected row in the table field$/,
    async (startOrEnd: 'start' | 'end') => {
        const field = <NestedFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE_NESTED_FIELD);
        await field.clickTimeZoneField(startOrEnd);
    },
);

When(
    /^the time-zone value in the (start|end) nested date-time-range field of the selected row in the table field is "(.*)"$/,
    async (startOrEnd: 'start' | 'end', expectedValue: string) => {
        const field = <NestedFieldObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE_NESTED_FIELD);
        await field.expectTimeZoneValue(startOrEnd, expectedValue);
    },
);
