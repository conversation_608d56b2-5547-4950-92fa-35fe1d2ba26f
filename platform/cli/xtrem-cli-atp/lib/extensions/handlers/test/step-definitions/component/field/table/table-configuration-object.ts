import { ChainablePromiseArray, ElementArray } from 'webdriverio';
import AbstractPageObject from '../../abstract-page-object';

export class TableConfigObject extends AbstractPageObject {
    name: string;

    constructor(configName: string) {
        super(`//*[@class='e-table-configuration-dialog-line']//*/label[. = "${configName}"]/../../*/input`);
        this.name = configName;
    }

    public static getList(): ChainablePromiseArray<ElementArray> {
        return browser.$$('//*[@class="e-table-configuration-dialog-line"]//*/label');
    }

    public static async checkListOrder(list: string) {
        const listItems = list.split(',');
        const listedConfig = await this.getList();
        let startIndex = -1;

        for (let index = 0; index < listedConfig.length; index += 1) {
            const item = listedConfig[index];
            if ((await item.getText()) === listItems[0]) {
                startIndex = index;

                break;
            }
        }
        for (let index = 0; index < listItems.length; index += 1) {
            const item = listItems[index];
            const elem = listedConfig[startIndex + index];
            const expectedItem = await elem.getText();
            if (item !== expectedItem) {
                throw new Error(
                    `Expected element did not follow order: "${expectedItem}" - found: "${item}".\nSelector:${elem.selector} `,
                );
            }
        }
    }

    public async tickCheck(ticked: string) {
        await this.expectToBeDisplayed(this.cssSelector);
        const shouldBeTicked = ticked === 'ticked';
        let input;
        try {
            input = await $(this.cssSelector);
            const isSelected = await input.isSelected();
            if (isSelected !== shouldBeTicked) {
                throw new Error(`Expected element to be ${ticked}.\nSelector: ${this.cssSelector}`);
            }
        } catch (error) {
            throw new Error(error);
        }
    }

    public async tick(ticked: string) {
        await this.expectToBeDisplayed(this.cssSelector);
        const shouldBeReadyToBeTicked = ticked === 'unticks';
        let input;
        try {
            input = await $(this.cssSelector);

            const isSelected = await input.isSelected();
            if (isSelected === shouldBeReadyToBeTicked) await input.click();
        } catch (error) {
            throw new Error(error);
        }
    }

    public async isLocked(lock: string) {
        await this.expectToBeDisplayed(this.cssSelector);
        let input;
        try {
            input = await $(this.cssSelector);

            const isLocked = !(await input.isEnabled());
            const shouldBeLocked = lock === 'locked';
            if (isLocked !== shouldBeLocked) {
                throw new Error(`Expected element to be ${lock}.\nSelector: ${this.cssSelector}`);
            }
        } catch (error) {
            throw new Error(error);
        }
    }
}
