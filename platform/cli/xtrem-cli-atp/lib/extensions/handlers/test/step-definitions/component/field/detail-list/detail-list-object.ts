import AbstractPageObject from '../../abstract-page-object';
import pageModel from '../../main-page';

export class DetailListObject extends AbstractPageObject {
    constructor() {
        super('.e-detail-list .e-detail-list-container');
    }

    async expectTitle(rowId: string, title: string) {
        const selectorToUse = `.e-detail-list-item:${
            rowId === 'first' || rowId === 'last' ? `${rowId}-child` : `nth-child(${rowId})`
        } .e-detail-list-item-title`;
        await pageModel.scrollTo({ selector: selectorToUse, ignoreContext: true });
        await this.expectTextContent({ toBe: title, ignoreCase: false, cssSelector: selectorToUse });
    }

    async linkClick(rowId: string, title: string) {
        const selectorToUse = `.e-detail-list-item:${
            rowId === 'first' || rowId === 'last' ? `${rowId}-child` : `nth-child(${rowId})`
        } .e-detail-list-item-value`;
        const linkTagA = browser.$(`${selectorToUse} a`);

        let linkExist = (await linkTagA).isExisting();
        if (await linkExist) {
            await this.expectTextContent({ toBe: title, ignoreCase: false, cssSelector: `${selectorToUse} a` });
            await linkTagA.click();
        } else {
            const linkTagButton = browser.$(`${selectorToUse} button`);
            linkExist = (await linkTagButton).isExisting();

            if (await linkExist) {
                await this.expectTextContent({
                    toBe: title,
                    ignoreCase: false,
                    cssSelector: `${selectorToUse} button`,
                });
                await linkTagButton.click();
            }
            if (!(await linkExist)) {
                throw new Error(`Element does not exist.\nSelector:${this.cssSelector}`);
            }
        }
    }
}
