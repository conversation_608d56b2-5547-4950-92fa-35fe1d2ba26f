import { Then, When } from '@cucumber/cucumber';
import { ElementContext } from '../../../step-definitions-utils';
import { waitForPromises } from '../../field/wait-util';
import { CrudButtonObject, CrudButtonStatus } from './crud-button-object';

const getCrudButton = (identifier: string, context: ElementContext) => new CrudButtonObject(identifier, context);

When(
    /^the user clicks the (create|save|delete|cancel|duplicate) CRUD button on (the main page|a modal|a full width modal|the sidebar)$/,
    async (crudAction: string, context: ElementContext) => {
        const crudButton = getCrudButton(crudAction, context);
        await crudButton.click();
        await waitForPromises(500, 'CRUD button click');
    },
);

Then(
    /^the (create|save|delete|cancel|duplicate) CRUD button on (the main page|a modal|a full width modal|the sidebar) is (disabled|enabled|hidden|visible)$/,
    async (crudAction: string, context: ElementContext, status: CrudButtonStatus) => {
        const crudButton = getCrudButton(crudAction, context);
        await crudButton.expectStatusToBe(status);
    },
);
