class AtpEnv {
    #timeout: number | undefined;

    #timeoutWaitFor: number | undefined;

    #timeoutWaitForLoading: number | undefined;

    #timeoutCucumber: number | undefined;

    #timeoutLocks: number | undefined;

    #downloadFolder: string | undefined;

    /** implicit timeout*/
    get timeout() {
        if (this.#timeout === undefined) {
            this.#timeout = Number(process.env.ATP_TIMEOUT || process.env.timeout) || 5000;
        }
        return this.#timeout;
    }

    /** visibility and action timeout */
    get timeoutWaitFor() {
        if (this.#timeoutWaitFor === undefined) {
            this.#timeoutWaitFor = Number(process.env.ATP_TIMEOUT_WAIT_FOR || process.env.timeoutWaitFor) || 10000;
        }
        return this.#timeoutWaitFor;
    }

    /** page loading timeout */
    get timeoutWaitForLoading() {
        if (this.#timeoutWaitForLoading === undefined) {
            this.#timeoutWaitForLoading =
                Number(process.env.ATP_TIMEOUT_WAIT_FOR_LOADING || process.env.timeoutWaitForLoading) || 58000;
        }
        return this.#timeoutWaitForLoading;
    }

    /** cucumber timeout */
    get timeoutCucumber() {
        if (this.#timeoutCucumber === undefined) {
            this.#timeoutCucumber = Number(process.env.ATP_TIMEOUT_CUCUMBER || process.env.timeoutCucumber) || 60000;
        }
        return this.#timeoutCucumber;
    }

    /** lock timeout */
    get timeoutLocks() {
        if (this.#timeoutLocks === undefined) {
            this.#timeoutLocks = Number(process.env.ATP_TIMEOUT_LOCKS || process.env.timeoutLocks) || 55000;
        }
        return this.#timeoutLocks;
    }

    get isValidCucumberTimeout() {
        return (
            this.timeoutCucumber >= this.timeoutLocks &&
            this.timeoutCucumber >= this.timeoutWaitFor &&
            this.timeoutCucumber >= this.timeoutWaitForLoading &&
            this.timeoutCucumber >= this.timeout
        );
    }

    /** download folder */
    get downloadFolder() {
        if (this.#downloadFolder === undefined) {
            this.#downloadFolder =
                process.env.ATP_DOWNLOAD_FOLDER ||
                process.env.downloadFolder ||
                `${process.env.USERPROFILE}r:!('\\Downloads`;
            this.#downloadFolder = this.#downloadFolder.replace('~', process.env.HOME ? process.env.HOME : '');
        }
        return this.#downloadFolder;
    }
}

export const atpEnv = new AtpEnv();
