/* eslint-disable no-restricted-syntax */
import { timeoutWaitFor, valueCheckTimeout } from '../index';

export const expectElementToBeDisplayed = async ({
    selector,
    reverse = false,
    name,
}: {
    selector: string;
    reverse?: boolean;
    name?: string;
}) => {
    const selectorToUse = await browser.$(selector);
    try {
        await selectorToUse.waitForDisplayed({ timeout: valueCheckTimeout, reverse });
    } catch (error) {
        throw new Error(
            `Expected Element to be ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${selector} \n ${name} `,
        );
    }
};

export const waitForElementToBeDisplayed = async ({
    name,
    selector,
    reverse = false,
}: {
    name: string;
    selector: string;
    reverse?: boolean;
}) => {
    const element = await browser.$(selector);
    await element.waitForDisplayed({
        reverse,
        timeout: timeoutWaitFor,
        timeoutMsg: `Expected element${
            reverse ? ' not ' : ' '
        }to be displayed: ${name} in ${timeoutWaitFor} ms.\nSelector: ${selector})`,
    });
};

export const waitForElementToBeFound = async ({
    name,
    selector,
    reverse = false,
    timeout = 10000,
}: {
    name: string;
    selector: string;
    reverse?: boolean;
    timeout?: number;
}) => {
    const elements = await browser.$$(selector);
    if (elements.length > 1) {
        // fix XT-95813
        await browser.waitUntil(
            async () => {
                let firstFound;
                for (const element of elements) {
                    if (await element.isDisplayed()) {
                        firstFound = element;
                        break;
                    }
                }
                return firstFound;
            },
            { timeout },
        );
    } else {
        // old code
        const element = await browser.$(selector);
        await element.waitForDisplayed({
            reverse,
            timeout,
            timeoutMsg: `Expected element could not be found: ${name}.\nSelector: ${selector}`,
        });
    }
};

export const waitForElementNotToBeDisplayed = async (name: string, selector: string) => {
    await waitForElementToBeDisplayed({ name, selector, reverse: true });
};

export const waitForElementToExist = async ({
    name,
    selector,
    reverse = false,
}: {
    name: string;
    selector: string;
    reverse?: boolean;
}) => {
    const element = await browser.$(selector);
    await element.waitForExist({
        reverse,
        timeout: timeoutWaitFor,
        timeoutMsg: `Expected element${
            reverse ? ' not ' : ' '
        }to exist: ${name} in ${timeoutWaitFor} ms.\nSelector: ${selector}`,
    });
};

export const waitForElementNotToExist = async (name: string, selector: string) => {
    await waitForElementToExist({ name, selector, reverse: true });
};

export const safeClick = (element: WebdriverIO.Element) => browser.execute(el => el.click(), element);

export const scrollIntoViewViaJS = (selector: string) =>
    browser.execute(`document.querySelector('${selector}').scrollIntoView()`);
