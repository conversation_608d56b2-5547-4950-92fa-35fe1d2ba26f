import { Then } from '@cucumber/cucumber';
import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { ButtonFieldObject } from './button-object';

Then(
    /^the user clicks the "(.*)" (bound|labelled) button on (the main page|a modal|a full width modal|the sidebar)$/,
    async (identifier: string, lookupStrategy: LookupStrategy, context: ElementContext) => {
        const button = new ButtonFieldObject({ identifier, lookupStrategy, context });
        await button.click();
    },
);
