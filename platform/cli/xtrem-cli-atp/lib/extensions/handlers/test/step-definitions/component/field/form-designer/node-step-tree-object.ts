import { camelCase } from 'lodash';
import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import { waitForPromises } from '../wait-util';

export class NodeStepTreeObject extends AbstractPageObject {
    constructor(context: utils.ElementContext) {
        super(
            `${utils.getContextSelector(context)} ${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-data-step-tree-container' })}`,
        );
    }

    async getNodeElement(nodeLevel: string, nodeIdentifier: string) {
        const selectorToUse = `.e-tree-view-element-level-${nodeLevel} > .e-tree-view-element-container > .e-tree-view-icon-container[data-testid~="e-tree-view-container-label-${camelCase(nodeIdentifier)}"]`;
        const selectorToUse1 = `.e-tree-view-element-level-${nodeLevel} > .e-tree-view-element-container > .e-tree-view-element-node-container`;

        let sectionElement;

        if (await (await $(`${selectorToUse}`)).isExisting()) {
            sectionElement = await $(`${selectorToUse}`);
        } else {
            const nodeCollapseElements = await this.findAll(selectorToUse1);

            // eslint-disable-next-line no-restricted-syntax
            for (const nodeElement of nodeCollapseElements) {
                if ((await nodeElement.$('label').getText()).toLowerCase() === nodeIdentifier.toLowerCase()) {
                    sectionElement = nodeElement;
                }
            }
        }

        if (sectionElement === undefined) {
            throw new Error(
                `Expected tree element "${nodeIdentifier}" with level "${nodeLevel}" could not be found.\nSelector: ${selectorToUse1}`,
            );
        }

        await waitForPromises(500, 'Filtering table');
        return sectionElement;
    }

    // eslint-disable-next-line class-methods-use-this
    expandCollapse = async (toggleState: utils.ExpandedOrCollapsed, sectionElement: WebdriverIO.Element) => {
        const selectorToUse = await sectionElement.$(`[data-testid~="e-tree-view-switcher-icon-container"] span`);
        const actualState = await selectorToUse.getAttribute('type');
        const toggle = toggleState === utils.ExpandedOrCollapsed.EXPANDED ? 'minus' : 'plus';

        if (toggleState !== utils.ExpandedOrCollapsed.EXPANDED && toggle !== actualState) {
            await (await sectionElement.$(`button[data-testid~="e-tree-view-switcher-icon-container"]`)).click();
        } else if (toggleState !== utils.ExpandedOrCollapsed.COLLAPSED && toggle !== actualState) {
            await (await sectionElement.$(`button[data-testid~="e-tree-view-switcher-icon-container"]`)).click();
        }

        await waitForPromises(500, `${toggleState} the folder element of the node-step-tree`);
    };

    // eslint-disable-next-line class-methods-use-this
    selectNodeElementWithText = async (toggleState: string, sectionElement: WebdriverIO.Element) => {
        const toggle = toggleState === 'ticks';

        if ((await (await sectionElement.$('span')).getAttribute('data-component')) === 'link') {
            await (await sectionElement.$(`span > button`)).click();
            await waitForPromises(500, `click link element of node-step-tree`);
        } else if (sectionElement.selector.toString().includes(`e-tree-view-element-node-container`)) {
            const selectNodeElement = await sectionElement.$(
                `.e-tree-view-switcher-container button[data-testid~="e-tree-view-switcher-icon-container"]`,
            );
            await selectNodeElement.click();
        } else {
            const selectorToUse = await (
                await sectionElement.parentElement()
            ).$(`.e-tree-view-checkbox-container input[data-testid~="e-tree-view-checkbox"]`);
            const isChecked = await selectorToUse.isSelected();
            if (toggle !== isChecked || toggleState === 'any') {
                await selectorToUse.click();
            }

            await waitForPromises(500, `click checkbox element of node-step-tree`);
        }
    };
}
