import AbstractPageObject from '../../abstract-page-object';
import { waitForPromises } from '../wait-util';

export const SELECTORS = {
    workflowNodeAction: 'div.e-workflow-node-add-button button[data-component="button"]',
    workflowNodeShowMoreButton: 'div.e-workflow-node-add-button button[data-element="toggle-button"]',
    workflowNodeSideButton: (side: string) => `button.e-workflow-node-floating-button-${side}`,
    workflowNodeSideLabel: 'span.e-workflow-condition-path-label',
};

export class WorkflowNodeObject extends AbstractPageObject {
    async clickNodeIcon(iconName: string) {
        const iconN = iconName.toLowerCase();
        const nodeIconSelector =
            iconN === 'view'
                ? `div.e-workflow-node-event-view-details [data-element="${iconN}"]`
                : `div.e-workflow-node-body button[aria-label="${iconN}"]`;
        const nodeIcon = await this.find(nodeIconSelector);

        try {
            await nodeIcon.waitForClickable();
            await nodeIcon.moveTo();
            await nodeIcon.click();
            await waitForPromises(500, 'node icon click');
        } catch {
            throw new Error(
                `Expected element could not be found: ${iconName} icon.\nSelector: ${this.cssSelector} ${nodeIconSelector}`,
            );
        }
    }

    async clickNodeAction(actionName: string) {
        const selectorToUse = this.cssSelector.substring(0, this.cssSelector.lastIndexOf(' '));
        const openShowMore = await $(`${selectorToUse} ${SELECTORS.workflowNodeShowMoreButton}`);
        await openShowMore.waitForClickable({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: ${actionName} icon.\nSelector: ${selectorToUse} ${SELECTORS.workflowNodeShowMoreButton}`,
        });
        await openShowMore.moveTo();
        await waitForPromises(500, 'Waiting for moveTo');
        await browser.execute(el => el.click(), openShowMore);
        await waitForPromises(500, 'Waiting for click');

        const actions = await $$(`${selectorToUse} ${SELECTORS.workflowNodeAction}`);

        let workflowNodeAction;
        // eslint-disable-next-line no-restricted-syntax
        for (const button of actions) {
            if (
                (await button.$('span[data-element="main-text"]').getText()).toLowerCase() === actionName.toLowerCase()
            ) {
                workflowNodeAction = button;
                break;
            }
        }

        if (workflowNodeAction === undefined) {
            throw new Error(
                `Expected element could not be found: "${actionName}" action.\nSelector: ${selectorToUse} ${SELECTORS.workflowNodeAction}`,
            );
        }

        await workflowNodeAction.click();
        await waitForPromises(200, 'click workflow node action');
    }

    private validateBranchPosition(buttonName: string, side: 'left' | 'right') {
        if ((buttonName === 'If true' && side !== 'left') || (buttonName === 'else' && side !== 'right')) {
            throw new Error(
                `Expected ${side} side button with "${buttonName}" label could not be found.\nSelector: ${this.cssSelector}`,
            );
        }
    }

    // eslint-disable-next-line class-methods-use-this
    private async verifyLabelExists(parentSelector: string, buttonName: string, side: string) {
        const labels = await $$(`${parentSelector} ${SELECTORS.workflowNodeSideLabel}`);

        // eslint-disable-next-line no-restricted-syntax
        for (const label of labels) {
            if ((await label.getText()).toLowerCase() === buttonName.toLowerCase()) {
                return;
            }
        }

        throw new Error(
            `Expected ${side} side button with "${buttonName}" label could not be found.\nSelector: ${parentSelector} ${SELECTORS.workflowNodeSideLabel}`,
        );
    }

    private async clickButton(parentSelector: string, side: string, buttonName: string) {
        const sideButton = await $(`${parentSelector} ${SELECTORS.workflowNodeSideButton(side)}`);

        await sideButton.waitForClickable({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected ${side} side button "${buttonName}" could not be found or is not clickable.\nSelector: ${parentSelector} ${SELECTORS.workflowNodeSideButton(side)}`,
        });

        await sideButton.moveTo();
        await waitForPromises(500, 'Waiting for mouse move');
        await browser.execute(el => el.click(), sideButton);
        await waitForPromises(500, 'Waiting for click');
    }

    async clickSideButton(buttonName: string, side: 'left' | 'right') {
        try {
            const parentSelector = this.cssSelector.substring(0, this.cssSelector.lastIndexOf('div[data-testid'));
            await this.verifyLabelExists(parentSelector, buttonName, side);
            this.validateBranchPosition(buttonName, side);
            await this.clickButton(parentSelector, side, buttonName);
        } catch (error) {
            await browser.takeScreenshot();
            throw error;
        }
    }
}
