import allureReporter from '@wdio/allure-reporter';
import { escapeRegExp } from 'lodash';
import AbstractPageObject from '../../abstract-page-object';
import { waitForPromises } from '../../field/wait-util';

export class ToastObject extends AbstractPageObject {
    constructor() {
        super('.carbon-portal');
    }

    protected readonly toastContentSelector = 'span[data-testid~="e-toast-content"]';

    protected readonly toastSelector = '.carbon-portal div[data-component="toast"]';

    protected readonly errorToastSelector = 'div.e-page-validation-error-list';

    // eslint-disable-next-line class-methods-use-this
    getToastsMessages = async (selector: string): Promise<string[]> => {
        const toasts = await browser.$$(selector);
        if (toasts.length > 0) {
            const messages = await Promise.all(
                toasts.map(async toast => {
                    await toast.isExisting();
                    return toast.getText();
                }),
            );
            return messages;
        }
        return [];
    };

    private async validateToast(
        expectToastTextContent: string,
        regexp: RegExp,
        expectedType: string,
        selector: string,
    ) {
        process.env.toastFallback = <string>process.env.toastFallback || 'false';
        const toastFallback = process.env.toastFallback !== 'false';
        let actualType = '';

        const actualMessage = await this.getToastsMessages(selector);
        if (expectedType.length > 0) {
            actualType = await this.getToastType();
        }
        await waitForPromises(500, 'Waiting for message');

        const msgFound = actualMessage.some(message => {
            return Boolean(RegExp(regexp).exec(message));
        });

        allureReporter.addAttachment('Toast message', `${actualMessage.toString()}`, 'text/plain');
        // eslint-disable-next-line no-console
        console.log(`Toast message: ${actualMessage.toString()}`);

        if (!msgFound && actualMessage.length > 0) {
            await browser.takeScreenshot();
            throw new Error(
                `Expected toast value: "${expectToastTextContent}". Actual: "${actualMessage.toString()}".\nSelector: ${this.toastSelector}`,
            );
        }

        if (actualMessage.length > 0) {
            if (expectedType.length > 0) {
                if (actualType !== expectedType) {
                    await browser.takeScreenshot();
                    throw new Error(
                        `Expected toast type: "${expectedType}". Actual: "${actualType}".\nSelector: ${this.toastContentSelector}`,
                    );
                }
            }
        }

        if (toastFallback && !msgFound && actualMessage.length === 0) {
            await browser.takeScreenshot();
            throw new Error(`Toast could not be found.\nSelector: ${this.toastContentSelector}`);
        }
    }

    async expectSomeToastWithText(expectToastTextContent: string) {
        const regexp = new RegExp(`^${expectToastTextContent}$`);
        await this.validateToast(expectToastTextContent, regexp, '', this.toastContentSelector);
    }

    async expectSomeToastWithTextContent(expectToastTextContent: string) {
        const regexp = new RegExp(escapeRegExp(expectToastTextContent));
        await this.validateToast(expectToastTextContent, regexp, '', this.toastContentSelector);
    }

    async expectSomeToastWithTypeAndText(type: string, expectToastTextContent: string) {
        const regexp = new RegExp(`^${expectToastTextContent}$`);
        const expectedType = type === 'default' ? 'info' : type;
        await this.validateToast(expectToastTextContent, regexp, expectedType, this.toastContentSelector);
    }

    async expectSomeToastWithTypeAndTextContent(type: string, expectToastTextContent: string) {
        const regexp = new RegExp(escapeRegExp(expectToastTextContent));
        const expectedType = type === 'default' ? 'info' : type;
        await this.validateToast(expectToastTextContent, regexp, expectedType, this.toastContentSelector);
    }

    async errorSomeToastWithText(expectToastTextContent: string) {
        const regexp = new RegExp(`^${expectToastTextContent}$`);
        await this.validateToast(expectToastTextContent, regexp, '', this.errorToastSelector);
    }

    async errorSomeToastWithTextContent(expectToastTextContent: string) {
        const regexp = new RegExp(escapeRegExp(expectToastTextContent));
        await this.validateToast(expectToastTextContent, regexp, '', this.errorToastSelector);
    }

    async dismissAllToasts() {
        const closeButtonSelector = `${this.toastSelector} button[data-element="close"]`;
        await browser.waitUntil(
            async () => {
                const closeButtons = await this.findAll(closeButtonSelector, true);
                await Promise.all(closeButtons.map(b => b.click()));
                return (await this.findAll(this.toastSelector, true)).length === 0;
            },
            { timeout: this.timeoutWaitFor, interval: 2000 },
        );
    }

    async getToastType() {
        let type = '';
        const element = await $(this.toastContentSelector);
        if (await element.isExisting()) {
            const dataTestId = await element.getAttribute('data-testid');
            type = dataTestId.split('e-toast-content-type-')[1];
        }
        return type;
    }

    async detectError() {
        if ((await this.getToastType()) === '') {
            const validationErrorSelector = this.getSelectorForOperation(this.errorToastSelector, false);
            const errorElement = await browser.$(validationErrorSelector);
            if (await errorElement.isExisting()) {
                await this.detectErrorOrToastError(this.errorToastSelector);
            }
        }

        if ((await this.getToastType()) === 'error') {
            await this.detectErrorOrToastError(this.toastSelector);
        }
    }

    private async detectErrorOrToastError(selectorToUse: string) {
        const actualMessage: string[] = [];
        const messages = await this.getToastsMessages(selectorToUse);
        await waitForPromises(500, 'Waiting for message');
        if (messages.length > 0) {
            const messageStr = messages.toString();
            actualMessage.push(messageStr);

            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: true });
            throw new Error(
                `Validation error message or error toast detected with text: "${actualMessage[0]}".\nSelector: ${this.cssSelector}`,
            );
        }
    }
}
