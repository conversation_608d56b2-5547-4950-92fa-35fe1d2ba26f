import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

export class RadioFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'radio', identifier, lookupStrategy, context });
    }

    override async expectTitle(expectedTitle: string) {
        await this.expectTextContent({ toBe: expectedTitle, ignoreCase: false, cssSelector: this.legendSelector });
    }

    override async expectTitleToBeDisplayed(reverse = false) {
        await this.expectToBeDisplayed(`${this.cssSelector} ${this.legendSelector}`, reverse);
    }

    async selectValue(value: string) {
        const item = await this.find(`input[type="radio"][value="${value}"]`);
        await item.waitForExist({
            timeoutMsg: `The radio button with value '${value}' could not be found.\nSelector: ${this.cssSelector} input[type="radio"][value="${value}"]`,
            timeout: this.timeoutWaitFor,
        });
        await item.click();
        await waitForPromises(500, 'click on radio button');
    }

    async selectValueByLabel(label: string) {
        await waitForPromises(200, 'wait for radio field');
        const selectorToUse = 'div[data-component="radio-button"] label[data-element="label"]';
        const items = await this.findAll(selectorToUse, true);

        for (let i = 0; i < items.length; i += 1) {
            // eslint-disable-next-line no-await-in-loop
            const text = await items[i].getText();
            if (text.trim() === label.trim()) {
                // eslint-disable-next-line no-await-in-loop
                await items[i].click();
                await waitForPromises(500, 'click on radio button');
                return;
            }
        }

        await browser.takeScreenshot();
        throw new Error(`Radio button with label '${label}' was not found.`);
    }

    async expectSelectedValue(value: string, reverse = false) {
        const item = await this.find(`input[type="radio"][value="${value}"]`);
        try {
            await browser.waitUntil(
                async () => {
                    const selectionState = await item.isSelected();
                    return selectionState === !reverse;
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(
                `Expected value ${reverse ? 'not to be selected' : 'to be selected'}: "${value}".\nSelector: ${this.cssSelector
                } input[type="radio"][value="${value}"]`,
            );
        }
    }

    async expectRadioFieldToBeEnabled(reverse = false) {
        const element = await browser.$(this.cssSelector);
        await browser.waitUntil(
            async () => {
                const className = await element.getAttribute('class');
                const enabledField = className.indexOf('e-disabled') === -1;
                return reverse ? !enabledField : enabledField;
            },
            {
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected Element to be ${reverse ? 'disabled' : 'enabled'}.\nSelector: ${this.cssSelector
                    }`,
            },
        );
    }

    override async expectToBeReadOnly() {
        const element = await browser.$(this.cssSelector);
        await browser.waitUntil(
            async () => {
                const className = await element.getAttribute('class');
                const readonly = className.indexOf('e-read-only') > -1;
                return readonly;
            },
            {
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected Element to be read-only.\nSelector: ${this.cssSelector}`,
            },
        );
    }
}
