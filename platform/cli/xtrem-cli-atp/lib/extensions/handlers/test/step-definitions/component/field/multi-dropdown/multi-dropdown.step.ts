import { Then } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { MultiDropdownFieldObject } from './multi-dropdown-object';

Then(
    /^at least the following list of options is displayed for the multi dropdown field: "(.*)"$/,
    async (value: string) => {
        const field = <MultiDropdownFieldObject>StaticStore.getStoredField(fieldTypes.multiDropdown);
        await field.selectDropDown.expectOptionsToBe(value);
    },
);
