/* eslint-disable @typescript-eslint/naming-convention */
import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import { scrollUntilClickable } from '../table/tableUtils';
import { waitForPromises } from '../wait-util';

export interface IDetailListNestedFieldObject {
    identifier: string;
    lookupStrategy: utils.LookupStrategy;
    fieldType: utils.NestedFieldTypes;
    rowNumber: number;
    context?: utils.ElementContext;
}

export class DetailListNestedFieldObject extends AbstractPageObject {
    constructor({ identifier, lookupStrategy, fieldType, rowNumber, context }: IDetailListNestedFieldObject) {
        super(
            `${context ? utils.getContextSelector(context) : ''} .e-detail-list-item:nth-child(${rowNumber})
          ${utils.getLookupStrategySelector({ fieldType, lookupStrategy, identifier })}`,
        );
    }

    async expectDetailListNestedFieldTextContent(toBe: string) {
        await scrollUntilClickable(this.cssSelector);
        await waitForPromises(500, 'detail list nested field text content');

        await this.expectTextContent({ toBe, ignoreCase: false, cssSelector: 'span' });
    }
}
