import { Then, When } from '@cucumber/cucumber';
import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { BlockContainerObject } from '../block/block-object';
import { DetailPanelObject } from './detail-panel-object';

Then(/^the detail panel is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const panel = new DetailPanelObject();
    await panel.expectDetailPanelToAppear(cssState);
});

When(/^the user selects the tab ([0-9]*) in the detail panel$/, async (tabNumber: number) => {
    const panel = new DetailPanelObject();
    const detailPanelTab = await panel.getDetailPanelTabByNumber(tabNumber);
    await detailPanelTab.scrollIntoView();
    await browser.pause(100);
    await detailPanelTab.click();
});

Then(
    /^the "(.*)" (bound|labelled) titled detail panel header is (displayed|hidden)$/,
    async (identifier: string, lookupStrategy: LookupStrategy, cssState: 'displayed' | 'hidden') => {
        const panel = new DetailPanelObject();
        await panel.expectDetailPanelTitleToBeDisplayed({ identifier, lookupStrategy, reverse: cssState === 'hidden' });
    },
);

When(/^the user selects the "(.*)" labelled tab in the detail panel$/, async (tabLabel: string) => {
    const panel = new DetailPanelObject();
    const detailPanelTab = await panel.getDetailPanelTabByText(tabLabel);
    await detailPanelTab.scrollIntoView();
    await browser.pause(100);
    await detailPanelTab.click();
});

When(/^the user clicks the detail panel closing button$/, async () => {
    const panel = new DetailPanelObject();
    const detailPanelCloseButton = await panel.getDetailPanelCloseButton();
    await detailPanelCloseButton.click();
});

Then(
    /^the "(.*)" (bound|labelled) block container on (the main page|a modal|a full width modal|the detail panel) is (displayed|hidden)$/,
    async (
        identifier: string,
        lookupStrategy: LookupStrategy,
        context: ElementContext,
        cssState: 'displayed' | 'hidden',
    ) => {
        const container = new BlockContainerObject({ identifier, lookupStrategy, context });
        await container.expectBlockToAppear(cssState);
    },
);
