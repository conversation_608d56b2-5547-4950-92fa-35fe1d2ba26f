import { Then, When } from '@cucumber/cucumber';
import { runtimeParameters } from '../../../../../../../parameters';
import { fieldTypes, takeScreenshot } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { DateFieldObject } from './date-object';

// ----------
// Static store field steps
// ----------

Then(/^the user stores the value of the date field with the key "(.*)"$/, async (keyValue: string) => {
    const field = <DateFieldObject>StaticStore.getStoredField(fieldTypes.date);
    await field.setElementValueToStore({ keyValue, fieldType: fieldTypes.date, TypeSelector: 'input' });
    await takeScreenshot();
});

When(
    /^the user stores the date value of the (first|last) day of the date picker with key "(.*)"$/,
    async (firstLast: string, keyValue: string) => {
        const field = <DateFieldObject>StaticStore.getStoredField(fieldTypes.date);
        await field.storeDateValueForDayFromDatePicker(firstLast, keyValue);
        await takeScreenshot();
    },
);

When(/^the user writes "(.*)" in the date field$/, async (dateString: string) => {
    const field = <DateFieldObject>StaticStore.getStoredField(fieldTypes.date);
    const parsedDateString = runtimeParameters.getStringOrParameter(dateString);
    const date = await field.generateDate(parsedDateString);
    await field.expectToAppear();
    await field.expectToBeEnabled(`${field.cssSelector} input`, false);
    await field.expectToBeReadOnly({ selector: field.cssSelector, FieldType: fieldTypes.date, reverse: true });
    await field.writeDate({ content: date });
    await field.clickSelectedDate(parsedDateString);
});

Then(/^the value of the date field is "(.*)"$/, async (dateString: string) => {
    const field = <DateFieldObject>StaticStore.getStoredField(fieldTypes.date);
    const storedDateString = StaticStore.getUserdefinedKeyValueFromStore(dateString);
    await field.expectValue({ toBe: storedDateString || '', cssSelector: 'input' });
    await field.clickSelectedDate(storedDateString);
});

When(/^the user writes a generated date in the date field with value "(.*)"$/, async (dateFormat: string) => {
    const field = <DateFieldObject>StaticStore.getStoredField(fieldTypes.date);
    const date = await field.generateDate(dateFormat);
    await field.expectToAppear();
    await field.expectToBeEnabled(`${field.cssSelector} input`, false);
    await field.expectToBeReadOnly({ selector: field.cssSelector, FieldType: fieldTypes.date, reverse: true });
    await field.writeDate({ content: date });
    await field.clickSelectedDate(date);
});

Then(/^the value of the date field is a generated date with value "(.*)"$/, async (dateFormat: string) => {
    const field = <DateFieldObject>StaticStore.getStoredField(fieldTypes.date);
    const date = await field.generateDate(dateFormat);
    await field.expectValue({ toBe: date });
    await field.clickSelectedDate(date);
});

Then(
    /^the date equal to "(.*)" is (enabled|disabled|selected|not selected|out of period)$/,
    async (dateString: string, state: string) => {
        const field = <DateFieldObject>StaticStore.getStoredField(fieldTypes.date);
        const date = await field.generateDate(dateString);
        if (date === 'Invalid Date') {
            throw new Error('Expected date could not be found.');
        }
        await field.expectDatesStateToBe(date, state);
    },
);

Then(
    // eslint-disable-next-line @sage/redos/no-vulnerable
    /^the dates from "(.*)" to "(.*)" are (enabled|disabled|out of period)$/,
    async (dateStringFrom: string, dateStringTo: string, state: string) => {
        const field = <DateFieldObject>StaticStore.getStoredField(fieldTypes.date);
        const dateFrom = dateStringFrom.startsWith('[ENV')
            ? StaticStore.getUserdefinedKeyValueFromStore(dateStringFrom)
            : await field.generateDate(dateStringFrom);
        const dateTo = dateStringTo.startsWith('[ENV')
            ? StaticStore.getUserdefinedKeyValueFromStore(dateStringTo)
            : await field.generateDate(dateStringTo);

        if (dateFrom === 'Invalid Date' || dateTo === 'Invalid Date') {
            throw new Error('Expected date could not be found.');
        }
        await field.expectDatesRangeStateToBe({ textDateFrom: dateFrom, textDateTo: dateTo, state });
    },
);

When(
    /^the user writes a generated date with value "(.*)" from today to the selected date field$/,
    async (dateFormat: string) => {
        const field = <DateFieldObject>StaticStore.getStoredField(fieldTypes.date);
        const date = await field.generateDate(dateFormat);
        await field.expectToAppear();
        await field.expectToBeEnabled(`${field.cssSelector} input`, false);
        await field.expectToBeReadOnly({ selector: field.cssSelector, FieldType: fieldTypes.date, reverse: true });
        await field.writeDate({ content: date });
        await field.clickSelectedDate(dateFormat);
    },
);

Then(/^the value of the date field is a generated date from today with value "(.*)"$/, async (dateFormat: string) => {
    const field = <DateFieldObject>StaticStore.getStoredField(fieldTypes.date);
    const date = await field.generateDate(dateFormat);
    await field.expectValue({ toBe: date });
    await field.clickSelectedDate(date);
});
