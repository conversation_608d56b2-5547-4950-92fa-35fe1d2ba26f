import { trimEnd } from 'lodash';
import * as utils from '../../../../step-definitions-utils';
import AbstractPageObject from '../../../abstract-page-object';
import * as StaticStore from '../../../static-store';
import { testGlobals } from '../../../test-globals';
import { SelectDropDownObject } from '../../select-dropdown-object';
import { waitForPromises } from '../../wait-util';
import { PodCollectionItemObject } from '../pod-collection-item/pod-collection-item-object';

const getItemSelector = () => {
    const item = StaticStore.getStoredObject(StaticStore.StoredKeys.POD_COLLECTION_ITEM) as PodCollectionItemObject;
    return item.cssSelector;
};

export class PodCollectionItemNestedFieldObject extends AbstractPageObject {
    public readonly selectDropDown: SelectDropDownObject;

    constructor({
        identifier,
        lookupStrategy,
        fieldType,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        fieldType: utils.FieldTypes;
    }) {
        super(
            `${getItemSelector()} ${utils.getLookupStrategySelector({ fieldType: utils.getFieldType(fieldType), lookupStrategy, identifier })}`,
        );
        this.selectDropDown = new SelectDropDownObject(this.cssSelector);
    }

    // Override click to inject a waitForPromises
    override async click(cssSelector?: string, ignoreContext = false) {
        await super.click(cssSelector, ignoreContext);
        await waitForPromises(500, 'click override');
    }

    clickMultiField = async (identifier: string) => {
        const element = await this.get();
        await element.waitForDisplayed({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${identifier}" nested field.\nSelector: ${this.cssSelector}`,
        });
        await browser.waitUntil(
            async () => {
                await element.scrollIntoView();
                return element.isClickable();
            },
            {
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Element is not clickable: ${element.selector}`,
            },
        );
        await this.click(`${element.selector} .e-ui-select-input-wrapper input`, true);
        await waitForPromises(500, 'clickMultiField');
    };

    selectSwitch = async (identifier: string) => {
        const selectorToUse = await this.find('input');
        try {
            await selectorToUse.scrollIntoView();
            await selectorToUse.click();
            await waitForPromises(600, 'finish loading');
        } catch (error) {
            throw new Error(
                `Expected element could not be found: ${identifier} nested switch.\nSelector: ${selectorToUse.selector}`,
            );
        }
    };

    async setNestedFieldValue({
        value,
        fieldType,
        identifier,
    }: {
        value: string;
        fieldType: utils.FieldTypes;
        identifier: string;
    }) {
        const element = await this.get();
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${identifier}" nested ${fieldType.toString()} field.\nSelector: ${
                this.cssSelector
            }`,
        });

        await element.scrollIntoView();

        if (fieldType === utils.fieldTypes.textArea) {
            await this.write({ content: value, cssSelector: 'textarea' });
        } else {
            await this.write({ content: value, cssSelector: 'input' });
        }

        await waitForPromises(1000, 'wait before selection etc');
    }

    expectNestedFieldValue = async ({
        expectedValue,
        fieldType,
        identifier,
    }: {
        expectedValue: string;
        fieldType: utils.FieldTypes;
        identifier: string;
    }) => {
        const element = await this.get();
        await element.waitForExist({
            timeout: this.valueCheckTimeout,
            timeoutMsg: `Expected element could not be found: "${identifier}" nested ${fieldType.toString()} field.\nSelector: ${
                this.cssSelector
            }`,
        });

        await element.scrollIntoView();
        await browser.pause(300);

        const classList = await element.getAttribute('class');
        const classListArray = classList.split(' ');

        if (classListArray.indexOf('e-read-only') > -1) {
            switch (fieldType) {
                case utils.fieldTypes.label:
                    await this.expectTextContent({ toBe: expectedValue, ignoreCase: false, cssSelector: 'span span' });
                    break;
                case utils.fieldTypes.textArea:
                    await this.expectNestedTextAreaContent({
                        toBe: expectedValue,
                        ignoreCase: false,
                        cssSelector: 'span',
                    });
                    break;
                case utils.fieldTypes.dropdownList:
                case utils.fieldTypes.numeric:
                case utils.fieldTypes.reference:
                case utils.fieldTypes.select:
                case utils.fieldTypes.progress:
                    await this.expectTextContent({ toBe: expectedValue, ignoreCase: false, cssSelector: 'span' });
                    break;
                default:
                    throw new Error(`Invalid fieldType type: ${fieldType}`);
            }
        } else {
            switch (fieldType) {
                case utils.fieldTypes.label:
                    await this.expectTextContent({ toBe: expectedValue, ignoreCase: false, cssSelector: 'span span' });
                    break;
                case utils.fieldTypes.dropdownList:
                case utils.fieldTypes.numeric:
                case utils.fieldTypes.reference:
                case utils.fieldTypes.select:
                case utils.fieldTypes.multiDropdown:
                    await this.expectValue({ toBe: expectedValue, cssSelector: 'input' });
                    break;
                case utils.fieldTypes.progress:
                    await this.expectNestedProgressValue(expectedValue);
                    break;
                case utils.fieldTypes.textArea:
                    await this.expectValue({ toBe: expectedValue, cssSelector: 'textarea' });
                    break;
                case utils.fieldTypes.multiReference:
                    await this.expectMultiReferenceValue(expectedValue);
                    break;
                default:
                    throw new Error(`Invalid fieldType type: ${fieldType}`);
            }
        }
    };

    async clearNestedField(fieldType: utils.FieldTypes) {
        const element = await this.get();
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: nested field.\nSelector: ${this.cssSelector}`,
        });
        await element.scrollIntoView();
        await browser.pause(200);
        await this.clearInput({ ignoreContext: false, fieldType });
    }

    async expectNestedTextAreaContent({
        toBe,
        ignoreCase = false,
        cssSelector,
        ignoreContext = false,
    }: {
        toBe: string;
        ignoreCase?: boolean;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        let textContent = '';
        try {
            await browser.waitUntil(
                async () => {
                    textContent = await (await $(selectorToUse)).getText();
                    textContent = utils.formatString(textContent);
                    // eslint-disable-next-line no-param-reassign
                    toBe = utils.formatString(toBe);
                    return (textContent || '') === (toBe || '') || (ignoreCase && (textContent || '') === (toBe || ''));
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            throw new Error(`Expected value: ${toBe}, actual: ${textContent}.\nSelector: ${selectorToUse}`);
        }
    }

    async expectNestedProgressValue(toBe: string) {
        const trimmedToBe = trimEnd(toBe, '%').trim();

        const element = await this.find('[data-element="current-progress-label"]');
        await element.waitForExist({
            timeout: this.valueCheckTimeout,
            timeoutMsg: `Element does not exist.\nSelector: ${this.cssSelector}`,
        });
        await element.waitForDisplayed({
            timeout: this.valueCheckTimeout,
            timeoutMsg: `Element not displayed.\nSelector: ${this.cssSelector}`,
        });
        let trimmedResult = '';
        try {
            await browser.waitUntil(
                async () => {
                    const result = await element.getText();
                    trimmedResult = trimEnd(result, '%').trim();
                    return (trimmedResult || '') === (trimmedToBe || '');
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Expected value: ${trimmedToBe}, actual: ${trimmedResult}.\nSelector: ${this.cssSelector}`);
        }
    }

    async expectMultiReferenceValue(toBe: string) {
        const elements = await this.findAll('.e-ui-select-label [data-component="pill"]');
        const values = await Promise.all(elements.map(el => el.getText()));
        const expectedValues = toBe.split('|');
        const allElementsPresent = expectedValues.every(el => values.includes(el));
        if (toBe === '' && values.length === 0) return;
        if (!allElementsPresent) {
            throw new Error(`Expected value: ${toBe}, actual: ${values.join('|')}.\nSelector: ${this.cssSelector}`);
        }
    }

    selectCheckBox = async (reverse: boolean, identifier: string) => {
        const checkBox = await this.find('input');
        await checkBox.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${identifier}" nested checkbox field.\nSelector: ${checkBox.selector}`,
        });
        await checkBox.scrollIntoView();

        const selectionState = await checkBox.isSelected();

        if ((!reverse && selectionState) || (reverse && !selectionState)) {
            throw new Error(
                `The "${identifier}" nested checkbox field is already ${
                    reverse && !selectionState ? 'un' : ''
                }selected`,
            );
        }

        await checkBox.click();
    };

    expectCheckBoxToBeSelected = async (reverse: boolean, identifier: string) => {
        const checkBox = await this.find('input');
        await checkBox.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${identifier}" nested checkbox field.\nSelector: ${checkBox.selector}`,
        });
        await checkBox.scrollIntoView();

        try {
            await browser.waitUntil(
                async () => {
                    const selectionState = await checkBox.isSelected();
                    return (!reverse && selectionState) || (reverse && !selectionState);
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            throw new Error(
                `The "${identifier}" nested checkbox is expected to be ${reverse === true ? 'un' : ''}selected`,
            );
        }
    };

    async exceptSwitchValue(value: string, identifier: string) {
        const selectorToUse = await this.find('input');
        await selectorToUse.waitForExist({
            timeoutMsg: `Expected element could not be found: ${identifier} nested switch.\nSelector: ${selectorToUse.selector}`,
        });

        const selectionState = await selectorToUse.isSelected();
        if ((selectionState && value.toLowerCase() === 'off') || (!selectionState && value.toLowerCase() === 'on')) {
            throw new Error(
                `Expected value: ${value} and actual: ${selectionState ? 'ON' : 'OFF'}.\nSelector: ${
                    selectorToUse.selector
                }`,
            );
        }
        await waitForPromises(600, 'finish loading');
    }

    static selectMultiOptionsInCollectionItemNestedField = async (
        nestedField: PodCollectionItemNestedFieldObject,
        value: string,
    ): Promise<void> => {
        // Split the value by "|" to handle multiple options
        const values = value.split('|').map(v => v.trim());
        // Open dropdown if desktop
        if (testGlobals.device === 'desktop') {
            const selector = nestedField.cssSelector;
            const dropdownArrow = await browser.$(`${selector} .e-ui-select-inline-dropdown`);
            await dropdownArrow.scrollIntoView();
            await dropdownArrow.click();
        }
        // Select each option sequentially
        // eslint-disable-next-line no-restricted-syntax
        for (const option of values) {
            await nestedField.selectDropDown.selectOption(option);
        }
        // Close the dropdown
        await nestedField.closeList();
    };
}
