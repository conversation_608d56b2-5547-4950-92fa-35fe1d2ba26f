/* pdf steps definition file */
import { Then, When } from '@cucumber/cucumber';
import * as fs from 'fs';
import { atpEnv } from '../../step-definitions-utils';
import * as StaticStore from '../static-store';
import { testGlobals } from '../test-globals';
import { textFile } from '../textfile';
import path = require('path');

When(/^the user reads the "(.*)" (txt|csv) file$/, (file: string, filetype: string) => {
    try {
        if (atpEnv.downloadFolder)
            textFile.readFile(atpEnv.downloadFolder, StaticStore.getUserdefinedKeyValueFromStore(file), filetype);
    } catch (error) {
        testGlobals._CURRENT_STEP_SCREENSHOT = true;
        throw new Error(error);
    }
});

When(/^the user sets the csv separator to "(.*)"$/, (separator: string) => {
    textFile.setCSVSeparator(separator);
});

Then(/^the user verifies the (csv|txt) file contains$/, (fileType: string, requiredText: string) => {
    try {
        textFile.assertTextInFile(StaticStore.getUserdefinedKeyValueFromStore(requiredText), fileType); // await Pages.emPage.replaceByStoredValues(requiredText));
    } catch (error) {
        testGlobals._CURRENT_STEP_SCREENSHOT = true;
        throw new Error(error);
    }
});
When(
    // eslint-disable-next-line @sage/redos/no-vulnerable
    /^the user renames the (csv|txt) file "(.*)" with name containing "(.*)"$/,
    async (fileType: string, fileName: string, filePattern: string) => {
        try {
            await textFile.renameFile(fileName, filePattern);
        } catch (error) {
            testGlobals._CURRENT_STEP_SCREENSHOT = true;
            throw new Error(error);
        }
    },
);
Then(/^the (csv|txt) file content matches the file "(.*)"$/, (fileType: string, fileName: string) => {
    try {
        const featurePath = path
            .resolve(process.env.FEATURE_PATH!)
            .substring(0, path.resolve(process.env.FEATURE_PATH!).lastIndexOf('/'));
        const absolutePath = path.resolve(path.join(featurePath, fileName));

        if (!fs.existsSync(absolutePath)) {
            throw new Error(`Expected file could not be found: ${fileName}`);
        }

        const requiredText = fs
            .readFileSync(StaticStore.getUserdefinedKeyValueFromStore(absolutePath))
            .toString('utf-8');
        textFile.assertTextInFile(StaticStore.getUserdefinedKeyValueFromStore(requiredText), fileType, true, fileName);
    } catch (error) {
        testGlobals._CURRENT_STEP_SCREENSHOT = true;
        throw new Error(error);
    }
});
