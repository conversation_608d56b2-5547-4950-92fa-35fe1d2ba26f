import { Then, When } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { DateTimeFieldObject } from './date-time-range-object';

// ----------
// Static store field steps
// ----------

When(
    /^the user selects the "(.*)" month of the (start|end) date-time-range field$/,
    async (targetMonth: string, startOrEnd: 'start' | 'end') => {
        const field = <DateTimeFieldObject>StaticStore.getStoredField(fieldTypes.dateTimeRange);
        await field.selectDateRangeMonth(startOrEnd, targetMonth);
    },
);

When(
    /^the user selects the "(.*)" year of the (start|end) date-time-range field$/,
    async (targetYear: string, startOrEnd: 'start' | 'end') => {
        const field = <DateTimeFieldObject>StaticStore.getStoredField(fieldTypes.dateTimeRange);
        await field.selectDateRangeYear(startOrEnd, targetYear);
    },
);

When(
    /^the user selects the "(.*)" day in the (start|end) date-time-range field$/,
    async (targetDay: string, startOrEnd: 'start' | 'end') => {
        const field = <DateTimeFieldObject>StaticStore.getStoredField(fieldTypes.dateTimeRange);
        await field.selectDateRangeDay(startOrEnd, targetDay);
    },
);

When(
    /^the user writes "(.*)" in time field of the (start|end) date-time-range field$/,
    async (targetTime: string, startOrEnd: 'start' | 'end') => {
        const field = <DateTimeFieldObject>StaticStore.getStoredField(fieldTypes.dateTimeRange);
        await field.setHoursMin(startOrEnd, targetTime);
    },
);

When(
    /^the user clicks the "(.*)" toggle button of the (start|end) date-time-range field$/,
    async (toggleButton: 'AM' | 'PM', startOrEnd: 'start' | 'end') => {
        const field = <DateTimeFieldObject>StaticStore.getStoredField(fieldTypes.dateTimeRange);
        await field.toggleDateRangeButton(startOrEnd, toggleButton);
    },
);

When(
    /^the user (ticks|unticks) the "(.*)" checkbox of the (start|end) date-time-range field$/,
    async (checkOrUncheck: 'ticks' | 'unticks', targetString: string, startOrEnd: 'start' | 'end') => {
        const field = <DateTimeFieldObject>StaticStore.getStoredField(fieldTypes.dateTimeRange);
        await field.selectDateRangeCheckbox({ startOrEnd, targetString, checkOrUncheck });
    },
);

Then(
    /^the value of the (start|end) date-time-range field is "(.*)"$/,
    async (startOrEnd: 'start' | 'end', expectedValue: string) => {
        const field = <DateTimeFieldObject>StaticStore.getStoredField(fieldTypes.dateTimeRange);
        await field.selectDateRangeValue(startOrEnd, expectedValue);
    },
);

Then(/^the user leaves the focus from the (start|end) date-time-range field$/, async (startOrEnd: 'start' | 'end') => {
    const field = <DateTimeFieldObject>StaticStore.getStoredField(fieldTypes.dateTimeRange);
    await field.loseFocusDateRange(startOrEnd);
});

When(
    /^the user clicks the time zone field in the (start|end) date-time-range field$/,
    async (startOrEnd: 'start' | 'end') => {
        const field = <DateTimeFieldObject>StaticStore.getStoredField(fieldTypes.dateTimeRange);
        await field.timeZoneDateRangeButton(startOrEnd);
    },
);

When(
    /^the time-zone value in the (start|end) date-time-range field is "(.*)"$/,
    async (startOrEnd: 'start' | 'end', expectedValue: string) => {
        const field = <DateTimeFieldObject>StaticStore.getStoredField(fieldTypes.dateTimeRange);
        await field.timeZoneExpectedValue(startOrEnd, expectedValue);
    },
);

When(/^the date-time-range field is mandatory$/, async () => {
    const field = <DateTimeFieldObject>StaticStore.getStoredField(fieldTypes.dateTimeRange);
    await field.scrollTo();
    await field.isMandatory();
});

When(
    /^the time-zone in the date-time-range field is (displayed|hidden)$/,
    async (fieldState: 'displayed' | 'hidden') => {
        const field = <DateTimeFieldObject>StaticStore.getStoredField(fieldTypes.dateTimeRange);
        await field.scrollTo();
        await field.timezoneState('start', fieldState);
    },
);
