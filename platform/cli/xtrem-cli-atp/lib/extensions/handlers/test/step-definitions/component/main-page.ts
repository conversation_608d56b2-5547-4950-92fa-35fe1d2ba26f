import { ExecutionMode, printInfo } from '@sage/xtrem-cli-lib';
import { addAttachment } from '@wdio/allure-reporter';
import { createHmac } from 'node:crypto';
import { runtimeParameters } from '../../../../../parameters';
import * as utils from '../step-definitions-utils';
import { takeScreenshot } from '../step-definitions-utils';
import AbstractPageObject from './abstract-page-object';
import { DialogDisplayTypes, DialogObject } from './container/dialog/dialog-object';
import { NavigationBarObject } from './container/navigation-bar/navigation-bar-object';
import { Body } from './container/page/page-body';
import { waitForPromises, waitMillis } from './field/wait-util';
import { Header } from './header/header';
import { MobileSidebar } from './sidebar/mobile-sidebar-object';
import { Sidebar } from './sidebar/sidebar-object';

const { TARGET_URL } = process.env;
const regex_unsecure_login = () => /unsecuredevlogin/g;
const regex_auth_login = () => /auth\/login\/page/g;
const regex_shadow_login = () => /https:\/\/(id-shadow|id)\.sage\.com\/u?\/?login/g;
// eslint-disable-next-line @sage/redos/no-vulnerable
const isTenantPage = (url: string) => /.*\/(tenants|selection)(\/$|$)/g.test(url);

const resizeScreen = async (device: utils.SupportedDevices) => {
    const desktopProfile: Parameters<typeof browser.emulateDevice>[0] = {
        viewport: {
            width: 1280, // <number> page width in pixels.
            height: 720, // <number> page height in pixels.
            deviceScaleFactor: 1, //  <number> Specify device scale factor (can be thought of as dpr). Defaults to 1
            isMobile: false, // <boolean> Whether the meta viewport tag is taken into account. Defaults to false
            hasTouch: false, // <boolean> Specifies if viewport supports touch events. Defaults to false
            isLandscape: true, // <boolean> Specifies if viewport is in landscape mode. Defaults to false
        },
        userAgent:
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Safari/537.36',
    };

    let deviceConf;

    switch (device) {
        case 'ultrawide desktop':
            deviceConf = {
                ...desktopProfile,
                viewport: {
                    ...desktopProfile.viewport,
                    width: 2560,
                    height: 1080,
                },
            };

            break;
        case 'HD desktop':
            deviceConf = {
                ...desktopProfile,
                viewport: {
                    ...desktopProfile.viewport,
                    width: 1920,
                    height: 1080,
                },
            };

            break;
        case 'desktop':
            deviceConf = desktopProfile;
            break;
        case 'mobile':
            deviceConf = 'Galaxy S5';
            break;
        case 'tablet':
            deviceConf = 'iPad Mini';
            break;
        default:
            throw new Error(`${device}: invalid device`);
    }

    await browser
        .emulateDevice(deviceConf)
        .catch(err => {
            throw new Error(err);
        })
        .catch(() => 'obligatory catch');
};

class MainPage extends AbstractPageObject {
    public readonly header: Header;

    public readonly sidebar: Sidebar;

    public readonly mobileSidebar: MobileSidebar;

    public readonly body: Body;

    timeoutLoading = utils.atpEnv.timeoutWaitFor;

    constructor() {
        super(utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-page' }));
        this.header = new Header(this.cssSelector);
        this.body = new Body(this.cssSelector);
        this.sidebar = new Sidebar();
        this.mobileSidebar = new MobileSidebar();
    }

    async waitForFinishLoading() {
        const loadingIndicator = await browser.$(
            utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-loading-indicator' }),
        );
        try {
            await loadingIndicator.waitForExist({
                reverse: true,
                timeout: this.timeoutWaitForLoading,
                timeoutMsg: `Screen refresh not completed after ${this.timeoutWaitForLoading} ms`,
            });
        } catch (err) {
            await browser.takeScreenshot();
            throw new Error(err.message);
        }
        await waitForPromises(600, 'finish loading');
    }

    async waitForInitialLoader() {
        const wrapper = await browser.$('.initial-loader-wrapper');
        await wrapper.waitForExist({
            reverse: true,
            timeout: this.timeoutLoading,
            timeoutMsg: 'Timeout connecting to tenant',
        });
    }

    // eslint-disable-next-line class-methods-use-this
    async wait(seconds: number) {
        const milliseconds = seconds * 1000;
        await waitMillis(milliseconds, 'user');
    }

    // eslint-disable-next-line class-methods-use-this
    async loginUnsecureElevated(targetUrl: string) {
        const env = process.env.smokeTestAuthEnvName;
        const secret = process.env.smokeTestsAuthSecret ? process.env.smokeTestsAuthSecret : '';
        const tenant = process.env.tenantId;
        const d = new Date();
        const time = d.getTime() / 1000;
        const hash = createHmac('SHA256', secret).update(`${env}.${time}.${tenant}`).digest('base64');
        const token = Buffer.from(`${time};${hash}`).toString('base64');
        const url = targetUrl ? targetUrl.replace('unsecuredevlogin', 'smoketests/login') : '';
        if (!env || !secret || !tenant || !url) {
            throw new Error(
                'Env parameters smokeTestAuthEnvName, smokeTestsAuthSecret, tenantId & TARGET_URL are required for elevated unsecure login.',
            );
        }
        await browser.url(url);
        await browser.setCookies([{ name: 'xtreemusendpoint', value: token }]);
        const apiLink = await browser.$('#login');
        await apiLink.waitForExist();
        await apiLink.click();
    }

    async checkLoginUnsecureElevated(targetUrl: string): Promise<boolean> {
        if (!TARGET_URL) return false;
        if (process.env.smokeTestsAuthSecret && process.env.smokeTestsAuthSecret !== '$(smokeTestsAuthSecret)') {
            if (regex_unsecure_login().test(TARGET_URL)) {
                printInfo(ExecutionMode.STANDALONE, 'Login using unsecure elevated mode');
                await this.loginUnsecureElevated(targetUrl);
            } else
                throw new Error('Unsecure login URL should be used when setting smokeTestsAuthSecret library variable');
            return true;
        }
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    async checkLoginUnsecureClassic(targetUrl: string): Promise<boolean> {
        if (!TARGET_URL) return false;
        if (regex_unsecure_login().test(TARGET_URL)) {
            await browser.url(targetUrl);
            await waitForPromises(1000, 'Wait after loading page');
            await utils.expectPageToBeDisplayed('div#root');
            return true;
        }
        return false;
    }

    async checkLoginSageId(targetUrl: string): Promise<boolean> {
        const { loginUserName = '', loginPassword = '' } = process.env;

        if (!targetUrl) return false;
        await browser.url(targetUrl);
        await waitForPromises(1000, 'Wait after loading page');
        const browserURL = await browser.getUrl();
        if (regex_auth_login().test(browserURL)) {
            const sageIdPageBtn = await browser.$('#s_ext .s_action');
            await sageIdPageBtn.waitForClickable();
            if (await sageIdPageBtn.isClickable()) {
                await sageIdPageBtn.click();
            } else {
                throw new Error('Sage Id button not clickable');
            }
            await this.sageIdLogin(loginUserName, loginPassword);
            return true;
        }
        if (regex_shadow_login().test(browserURL)) {
            await this.sageIdLogin(loginUserName, loginPassword);
            return true;
        }
        return false;
    }

    async handleLogin() {
        if (!TARGET_URL) return;
        const { loginUserName = '' } = process.env;
        const targetUrlUnsecure = `${TARGET_URL}&user=${loginUserName}`;
        if (await this.checkLoginUnsecureElevated(targetUrlUnsecure)) return;
        if (await this.checkLoginUnsecureClassic(targetUrlUnsecure)) return;
        if (await this.checkLoginSageId(TARGET_URL)) return;
        // eslint-disable-next-line no-console
        console.log('No login method found');
    }

    // eslint-disable-next-line class-methods-use-this
    async openTargetURL(url: string = '', newTab: boolean = false) {
        await browser.execute('window.__REMOVE_UNLOAD_LISTENER && window.__REMOVE_UNLOAD_LISTENER();');
        const browserURL = await browser.getUrl();

        let targetUrl = utils.addTrailingSlash(TARGET_URL || 'http://127.0.0.1:8240');

        /**
         * TODO: @BL When the unsecuredevlogin is used the targetUrl is not actually the target url of the tests.
         * The target URL is a complex URL of the login service with the unsecuredevlogin parameter, therefore
         * the actual URL must be determined from the browser URL that the user is redirected to.
         *
         * This must change, the unsecure parameter should not be used in the target URL and should have its own parameter
         */
        if (regex_unsecure_login().test(targetUrl)) {
            targetUrl = utils.addTrailingSlash(new URL(browserURL).origin);
        }

        const mergedTargetUrl = targetUrl + url;
        // mobile automation
        if (utils.regex_handheld(browserURL)) {
            if (browserURL === mergedTargetUrl) {
                await browser.refresh();
            } else {
                await browser.url(mergedTargetUrl);
            }
        } else if (browserURL === mergedTargetUrl) {
            await browser.refresh();
        } else {
            await (newTab ? browser.newWindow(mergedTargetUrl) : browser.url(mergedTargetUrl));
        }
    }

    // eslint-disable-next-line @typescript-eslint/default-param-last
    async open(url: string = '', device: utils.SupportedDevices, retries: number) {
        // eslint-disable-next-line no-console
        console.log('login state:', this.getLoginState(), ' - opening start');
        if (!this.getLoginState()) {
            // Disables the alert dialog that would prevent the page from being closed.
            await browser.execute('window.__REMOVE_UNLOAD_LISTENER && window.__REMOVE_UNLOAD_LISTENER();');
            await waitForPromises(300, 'Wait before loading page');
            await browser.url('about:blank');
        }
        await resizeScreen(device);
        if (TARGET_URL) {
            if (!this.getLoginState()) {
                // check which login method to use
                await this.handleLogin();
                await waitForPromises(1000, 'Wait after loading page');
            }
            await this.checkForTenantSelectionPage(retries);
            await this.openTargetURL(url);
        } else {
            await browser.url(encodeURI('/') + url);
        }
        // eslint-disable-next-line no-console
        console.log('login state:', this.getLoginState(), ' -  opening end');
    }

    logIn = async ({
        device,
        usernameParam = '',
        passwordParam = '',
    }: {
        device: utils.SupportedDevices;
        usernameParam?: string;
        passwordParam?: string;
    }) => {
        // eslint-disable-next-line no-console
        console.log('login state:', this.getLoginState(), ' - login in');

        // Disables the alert dialog that would prevent the page from being closed.
        await browser.execute('window.__REMOVE_UNLOAD_LISTENER && window.__REMOVE_UNLOAD_LISTENER();');
        this.setLoginState(true);

        await waitForPromises(300, 'Wait before loading page');
        await browser.url('about:blank');

        await resizeScreen(device);

        const loginUserName = runtimeParameters.getStringOrParameter(usernameParam);
        const loginPassword = runtimeParameters.getStringOrParameter(passwordParam);

        if (TARGET_URL) {
            if (regex_unsecure_login().test(TARGET_URL)) {
                const targetUrl = `${TARGET_URL}&user=${loginUserName}`;
                await browser.url(targetUrl);
                await waitForPromises(1000, 'Wait after loading page');
                await utils.expectPageToBeDisplayed('div#root');
            } else {
                await browser.url(TARGET_URL);
                await waitForPromises(500, 'Wait after loading page');
                const browserURL = await browser.getUrl();

                if (regex_auth_login().test(browserURL)) {
                    const sageIdPageBtn = await browser.$('#s_ext .s_action');

                    await utils.waitForElementToBeDisplayed({
                        name: 'sageIdPageBtn',
                        selector: sageIdPageBtn.selector.toString(),
                    });

                    await sageIdPageBtn.click();
                    await this.sageIdLogin(loginUserName, loginPassword);
                    await this.checkForTenantSelectionPage(0);
                } else if (regex_shadow_login().test(browserURL)) {
                    await this.sageIdLogin(loginUserName, loginPassword);
                    await this.checkForTenantSelectionPage(0);
                }
            }
        } else {
            throw new Error('Expected TARGET_URL env variable.');
        }
        // eslint-disable-next-line no-console
        console.log('login state:', this.getLoginState(), ' - logged in');
    };

    // eslint-disable-next-line class-methods-use-this
    sageIdLogin = async (username = '', password = '') => {
        const submitInput = await browser.$('button[type="submit"]');
        const emailInputSelector = 'input[id$="user_reference"]';
        const emailInput = await browser.$(emailInputSelector);
        await emailInput.click();
        await browser.execute(`document.querySelector('${emailInputSelector}').value='';`);
        await waitForPromises(100, 'before login user name');
        await browser.keys(username);
        await waitForPromises(100, 'after login user name');

        await utils.expectPageToBeDisplayed('div.sageid-core');
        await submitInput.click();

        const pwdInputSelector = 'input[id$="password"]';
        const pwdInput = await browser.$(pwdInputSelector);

        await pwdInput.click();
        await browser.execute(`document.querySelector('${pwdInputSelector}').value='';`);
        await waitForPromises(100, 'before login password');
        await browser.keys(password);
        await waitForPromises(100, 'after login password');
        await submitInput.click();

        await waitForPromises(1000, 'login click');
    };

    checkForTenantSelectionPage = async (retries: number) => {
        const currentUrl = await browser.getUrl();
        await this.discardPreferenceDialog();
        if (isTenantPage(currentUrl)) await this.selectTenant(retries);
        await waitForPromises(1000, 'tenant selection');

        const isAppSelectionScreen = await this.isAppSelectionScreen();
        if (isAppSelectionScreen) await this.selectTenantApp(retries);

        await this.waitForInitialLoader();
        await waitForPromises(500, 'tenant connect');

        await utils.expectPageToBeDisplayed('div#root');
    };

    // eslint-disable-next-line class-methods-use-this
    isAppSelectionScreen = async () => {
        const sectionTitle = utils.getDataTestIdSelector({
            domSelector: 'div',
            dataTestIdValue: 'tenant-count-container',
        });

        if (await (await browser.$(sectionTitle)).isExisting()) {
            const sectionElement = await browser.$(`${sectionTitle} p`);
            const sectionTitleText = await sectionElement.getText();
            const secondItem = sectionTitleText.split(' / ')[1].trim();
            // const sanitizedText = secondItem.replace(/\n/g, ' ');

            const regex = /^(?:[2-9]|10) applications$/;

            if (regex.test(secondItem)) {
                return true;
            }
        }
        return false;
    };

    selectTenant = async (retries: number) => {
        const { tenantName: expectedTenantName } = process.env;

        if (expectedTenantName === undefined) {
            throw new Error('Tenant selection page displayed but no Tenant name specified in parameters');
        }

        await this.selectTenantCard(expectedTenantName, retries);
    };

    selectTenantApp = async (retries: number) => {
        const { tenantAppName: expectedTenantApp } = process.env;

        if (expectedTenantApp === undefined) {
            throw new Error('Tenant App selection page displayed but no Tenant App name specified in parameters');
        }

        await this.selectTenantCard(expectedTenantApp, retries);
    };

    // eslint-disable-next-line class-methods-use-this
    selectTenantCard = async (expectedCardName: string, retries: number) => {
        const container = utils.getDataTestIdSelector({
            domSelector: 'div',
            dataTestIdValue: 'tenant-cards-container',
        });
        // eslint-disable-next-line no-void
        void utils.waitForElementToBeDisplayed({ name: 'tenant cards container', selector: container });

        const cards = await browser.$$(`${container} [data-component="card"]`);

        const currentTenantCardNames = await Promise.all(
            cards.map(async card => {
                const name = await (await card.$('[data-role="tenant-name"]')).getText();
                return name;
            }),
        );

        const index = currentTenantCardNames.findIndex(
            currentTenantCardName => currentTenantCardName === expectedCardName,
        );

        if (index < 0) {
            if (retries === 0) {
                await takeScreenshot();
            }
            throw new Error(
                `Expected value to be selected could not be found: "${expectedCardName}". Retry count: ${retries}`,
            );
        }

        await cards[index].scrollIntoView();

        await (await cards[index].$('[data-role="access-button"]')).click();

        await waitForPromises(500, 'tenant button click');
    };

    async openApp({
        url = '',
        device,
        retries = 0,
    }: {
        url: string;
        device: utils.SupportedDevices;
        retries?: number;
    }) {
        const maxRetries = 3;

        if (retries <= maxRetries) {
            try {
                await this.open(url, device, retries);
                await this.waitForFinishLoading();
                await browser.execute('window.__ACTIVATE_CONSOLE_WATCH && window.__ACTIVATE_CONSOLE_WATCH();');
                await browser.execute('window.DEBUGGING_XTREM=true;');
            } catch (err) {
                if (retries === maxRetries) {
                    throw err;
                } else {
                    await this.openApp({
                        url,
                        device,
                        retries: retries + 1,
                    });
                }
            }
        }
    }

    logOut = async () => {
        const browserURL = await browser.getUrl();
        if (utils.regex_handheld(browserURL)) {
            const navBar = new NavigationBarObject();
            await navBar.logOut();
        } else {
            const xeProfile = await browser.$('.xe-profile-profile');
            await xeProfile.waitForDisplayed();
            await xeProfile.click();

            const xeProfileOpened = await browser.$('.xe-profile-profile.xe-opened');
            await xeProfileOpened.waitForDisplayed();

            const menuLogOut = await browser.$('.xe-profile-menu-logout [data-component="link"]');
            await menuLogOut.waitForDisplayed();
            await menuLogOut.click();

            await waitForPromises(500, 'waiting for logout');
        }
        this.setLoginState(false);
        // eslint-disable-next-line no-console
        console.log('login state:', this.getLoginState(), 'logged out');
    };

    /* eslint-disable-next-line class-methods-use-this */
    async switchToNewTab(tabPosition: 'first' | 'last' = 'last') {
        // Wait until a new window/tab is available
        await browser.waitUntil(
            async () => {
                const allWindowHandles = await browser.getWindowHandles();
                return allWindowHandles.length > 1; // Ensure more than one window is open
            },
            {
                timeoutMsg: 'Expected a new tab/window to be opened, but none were found',
            },
        );
        // Get the list of all window handles after the wait
        const allWindowHandles = await browser.getWindowHandles();
        // Determine which tab (first or last) to switch to
        const targetHandle =
            tabPosition === 'first' ? allWindowHandles[0] : allWindowHandles[allWindowHandles.length - 1];
        // Switch to the selected tab
        await browser.switchToWindow(targetHandle);
        // Ensure the active window handle matches the target handle to confirm the switch
        await browser.waitUntil(
            async () => {
                const currentHandle = await browser.getWindowHandle(); // Get the current active window handle
                return currentHandle === targetHandle; // Ensure that the browser switched to the new tab
            },
            {
                timeoutMsg: 'Failed to switch to the new tab',
            },
        );
    }

    /* eslint-disable-next-line class-methods-use-this */
    async verifyPageContainsText(expectedText: string, selector: string = 'body') {
        const element = await $(selector);
        // Retrieve the element's text content
        const elementText = await element.getText();
        await takeScreenshot();
        // Attach expected text to Allure report
        addAttachment('Expected value', expectedText, 'text/plain');
        // Perform the text validation
        if (!elementText.includes(expectedText)) {
            throw new Error(
                `Text "${expectedText}" not found in the element with selector "${selector}". Actual text: "${elementText}"`,
            );
        }
    }

    async discardPreferenceDialog() {
        // Detect if the preference dialog is displayed and discard it
        const dialog = new DialogObject(DialogDisplayTypes.mainpage);
        try {
            await dialog.expectToDisappear();
        } catch {
            const actualDialogTitle = await dialog.getTitle();
            await takeScreenshot();
            // discard the dialog by clicking first button = Accept selection
            const dialogButton = await this.find('[data-component="button"]:nth-of-type(2)', true);
            await dialogButton.click();
            await waitForPromises(600, 'wait after dialog button click');
            // eslint-disable-next-line no-console
            console.log(`Dialog "${actualDialogTitle}" was displayed and has been automatically accepted`);
            addAttachment(
                'Log',
                `Dialog "${actualDialogTitle}" was displayed and has been automatically accepted`,
                'text/plain',
            );
        }
    }
}

export const mainPage = new MainPage();
export default mainPage;
