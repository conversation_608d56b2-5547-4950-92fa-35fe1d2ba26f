import { Then, When } from '@cucumber/cucumber';
import { LookupStrategy, fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { FilterSelectFieldObject } from './filter-select-object';

When(/^the user clicks the lookup button of the filter select field$/, async () => {
    const field = <FilterSelectFieldObject>StaticStore.getStoredField(fieldTypes.filterSelect);
    await field.clickOnFieldLookupButton();
});

Then(/^the options of the filter select field include "(.*)"$/, async (value: string) => {
    const field = <FilterSelectFieldObject>StaticStore.getStoredField(fieldTypes.filterSelect);
    // eslint-disable-next-line @sage/redos/no-vulnerable
    const regex = /"e-field-(.*)-(.*)"/g;
    const matches = regex.exec(field.cssSelector);
    const identifier = matches != null ? matches[2] : '';
    const lookupStrategy =
        matches != null ? (matches[1] === 'bind' ? LookupStrategy.BIND : LookupStrategy.LABEL) : LookupStrategy.NONE;
    await field.expectOptionsToContain({ value, identifier, lookupStrategy });
});
