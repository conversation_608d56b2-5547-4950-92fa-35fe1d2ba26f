import * as sizeOf from 'image-size';
import * as fs from 'fs';
import { TextFormatOptions } from './pdf-configuration';

export const calculateDuration = (durationMs: number) => {
    const totalSeconds = Math.trunc(durationMs / 1000);
    const milliseconds = durationMs - totalSeconds * 1000;
    let minutes = Math.trunc(totalSeconds / 60);
    const seconds = totalSeconds - minutes * 60;
    const hours = Math.trunc(minutes / 60);
    minutes -= hours * 60;

    return { Hours: hours, Minutes: minutes, Seconds: seconds, Milliseconds: milliseconds };
};

export const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
    const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180.0;

    return {
        x: centerX + radius * Math.cos(angleInRadians),
        y: centerY + radius * Math.sin(angleInRadians),
    };
};

export const describeArc = (x: number, y: number, radius: number, startAngle: number, endAngle: number) => {
    const start = polarToCartesian(x, y, radius, endAngle);
    const end = polarToCartesian(x, y, radius, startAngle);

    const largeArcFlag = endAngle - startAngle <= 180 ? '0' : '1';

    const d = ['M', start.x, start.y, 'A', radius, radius, 0, largeArcFlag, 0, end.x, end.y].join(' ');

    return d;
};

export const getFont = (fontName: string, fontFormat: TextFormatOptions) => {
    switch (fontName) {
        case 'Helvetica':
        case 'Courier':
            return `${fontName}${fontFormat.Bold || fontFormat.Italic ? '-' : ''}${fontFormat.Bold ? 'Bold' : ''}${
                fontFormat.Italic ? 'Oblique' : ''
            }`;

        case 'Times-Roman':
            if (fontFormat.Bold || fontFormat.Italic) {
                // eslint-disable-next-line no-param-reassign
                fontName = `${fontName.replace('Roman', '')}${fontFormat.Bold ? 'Bold' : ''}${
                    fontFormat.Italic ? 'Italic' : ''
                }`;
            }
            return fontName;

        default:
            throw new Error(`Unknown Font ${fontName}. Supported fonts are:\nHelvetica\nCourier\nTimes-Roman`);
    }
};

export const getImageDimensions = (imagePath: string, maxWidth = 0, maxHeight = 0) => {
    const imageDimensions = sizeOf.imageSize(fs.readFileSync(imagePath));
    if (imageDimensions.height === undefined || imageDimensions.width === undefined) {
        throw new Error('Image dimensions could not be determined.');
    }
    const imageRatio = imageDimensions.height / imageDimensions.width;
    let imageWidth = imageDimensions.width;
    let imageHeight = imageDimensions.height;

    if (maxWidth > 0) {
        imageWidth = maxWidth;
        imageHeight = maxWidth * imageRatio;
    }

    if (maxHeight > 0) {
        imageHeight = maxHeight;
        imageWidth = maxHeight * imageRatio;
    }

    return { width: imageWidth, height: imageHeight };
};
