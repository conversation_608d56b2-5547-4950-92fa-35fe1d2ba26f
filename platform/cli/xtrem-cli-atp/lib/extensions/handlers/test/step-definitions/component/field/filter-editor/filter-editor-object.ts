import { ElementContext, expectElementToBeDisplayed, KEY, LookupStrategy } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

export class FilterEditorObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
        fieldType,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
        fieldType?: string;
    }) {
        super({ fieldType: fieldType ?? 'filter editor', identifier, lookupStrategy, context });
    }

    async clickAddButton(value: string) {
        const addButtonSelector = 'button[data-testid="add-item-button"]';
        const addButton = await this.find(addButtonSelector);
        await this.expectTextContent({
            toBe: value,
            ignoreCase: true,
            cssSelector: addButtonSelector,
            ignoreContext: true,
        });
        await addButton.click();
    }

    async setFieldValue({ value, name, rowId }: { value: string; name: string; rowId: number }) {
        const cssSelector = `[data-testid~="e-widget-editor-${name}-${rowId}"]`;
        await this.write({ content: value, cssSelector, ignoreContext: true });
        await FilterEditorObject.press(KEY.Enter);
    }

    async expectRowFieldDisplayed({ name, rowId, reverse = false }: { name: string; rowId: number; reverse: boolean }) {
        const element = await this.find(`[data-testid~="e-widget-editor-${name}-${rowId}"]`, true);
        await this.expectToBeDisplayed(`${element.selector.toString()}`, reverse);
    }

    async expectRowFieldEnabled({ name, rowId, reverse = false }: { name: string; rowId: number; reverse: boolean }) {
        const element = await this.find(`[data-testid~="e-widget-editor-${name}-${rowId}"]`, true);
        await this.expectToBeEnabled(`${element.selector.toString()}`, reverse);
    }

    async clickSwitch(name: string, rowId: number) {
        const selectorToUse = `[data-testid="e-widget-editor-${name}-${rowId}"]`;
        const switchButton = await this.find(selectorToUse, true);
        await switchButton.click();
    }

    async expectSwitchValue({ name, rowId, value }: { name: string; rowId: number; value: string }) {
        const selectorToUse = await this.find(`input[data-testid="e-widget-editor-${name}-${rowId}"]`);
        await selectorToUse.waitForExist({
            timeoutMsg: `Expected element could not be found: switch.\nSelector: ${selectorToUse.selector}`,
        });

        const selectionState = await selectorToUse.isSelected();
        if (
            (selectionState && value.toLowerCase() === 'off') ||
            (!selectionState && value.toLowerCase() === 'on')
        ) {
            throw new Error(
                `Expected value: ${value} and actual: ${selectionState ? 'ON' : 'OFF'}.\nSelector: ${selectorToUse.selector
                }`,
            );
        }
        await waitForPromises(600, 'finish loading');
    }

    async expectEditorActionButtonDisplayed({
        buttonName,
        rowId,
        reverse = false,
    }: {
        buttonName: string;
        rowId: number;
        reverse: boolean;
    }) {
        const actionButton = await this.find(
            `[data-element="flat-table-row"][data-testid="${rowId}"] [data-testid="flat-table-${buttonName}-button"]`,
            true,
        );
        await expectElementToBeDisplayed({ selector: actionButton.selector.toString(), reverse });
    }

    async clickEditorActionButton(buttonName: string, rowId: number) {
        const actionButton = await this.find(
            `[data-element="flat-table-row"][data-testid="${rowId}"] [data-testid="flat-table-${buttonName}-button"]`,
            true,
        );
        await actionButton.click();
    }

    async clearRowFieldValue(name: string, rowId: number) {
        const selectorToUse = `[data-testid~="e-widget-editor-${name}-${rowId}"]`;
        await this.clearInput({ cssSelector: selectorToUse });
    }
}
