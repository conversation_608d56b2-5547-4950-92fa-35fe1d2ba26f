import { Then, When } from '@cucumber/cucumber';
import { runtimeParameters } from '../../../../../../parameters';
import * as utils from '../../step-definitions-utils';
import AbstractPageObject from '../abstract-page-object';
import pageModel from '../main-page';
import * as StaticStore from '../static-store';
import { testGlobals } from '../test-globals';
import { CalendarFieldObject } from './calendar/calendar-object';
import { CheckBoxFieldObject } from './checkbox/checkbox-object';
import { CodeEditorFieldObject } from './code-editor/code-editor-object';
import { ContentTableObject } from './content-table/content-table-object';
import { DateTimeFieldObject } from './date-time-range/date-time-range-object';
import { DateFieldObject } from './date/date-object';
import { FieldObject } from './field-object';
import { FilterEditorObject } from './filter-editor/filter-editor-object';
import { FilterSelectFieldObject } from './filter-select/filter-select-object';
import { FormDesignerFieldObject } from './form-designer/form-designer-object';
import { GraphiqlEditorFieldObject } from './graphiql-editor/graphiql-editor-object';
import { LabelFieldObject } from './label/label-field-object';
import { LinkFieldObject } from './link/link-object';
import { MultiDropdownFieldObject } from './multi-dropdown/multi-dropdown-object';
import { MultiReferenceFieldObject } from './multi-reference/multi-reference-object';
import { NestedGridObject } from './nested-grid/nested-grid-object';
import { NodeBrowserTreeObject } from './node-browser-tree/node-browser-tree-object';
import { PdfViewerFieldObject } from './pdf-viewer/pdf-viewer-object';
import { PodObject } from './pod/pod-field/pod-object';
import { ProgressFieldObject } from './progress/progress-object';
import { RadioFieldObject } from './radio/radio-field-object';
import { ReferenceFieldObject } from './reference/reference-object';
import { RelativeDateFieldObject } from './relative-date/relative-date-object';
import { RichTextFieldObject } from './rich-text/rich-text-object';
import { SelectionCardObject } from './selection-card/selection-card-object';
import { StaticContentObject } from './static-content/static-content-object';
import { SwitchFieldObject } from './switch/switch-object';
import { TableObject } from './table/table-object';
import { TextAreaFieldObject } from './text-area/text-area-object';
import { TimeFieldObject } from './timeField/time-object';
import { waitForPromises } from './wait-util';
import { WorkflowDesignerFieldObject } from './workflow-designer/workflow-designer-object';

const getField = ({
    identifier,
    lookupStrategy,
    context,
    fieldType,
}: {
    identifier: string;
    lookupStrategy: utils.LookupStrategy;
    context: utils.ElementContext;
    fieldType: utils.FieldTypes;
}): FieldObject => {
    // TODO Define the identifiers in etna-ui and import them
    switch (fieldType) {
        case utils.fieldTypes.button:
            return new FieldObject({ fieldType: 'button', identifier, lookupStrategy, context });
        case utils.fieldTypes.calendar:
            return new CalendarFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.checkbox:
            return new CheckBoxFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.count:
            return new FieldObject({ fieldType: 'count', identifier, lookupStrategy, context });
        case utils.fieldTypes.date:
            return new DateFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.time:
            return new TimeFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.dateTimeRange:
            return new DateTimeFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.dropdownList:
            return new FieldObject({ fieldType: 'dropdown-list', identifier, lookupStrategy, context });
        case utils.fieldTypes.filterSelect:
            return new FilterSelectFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.formDesigner:
            return new FormDesignerFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.icon:
            return new FieldObject({ fieldType: 'icon', identifier, lookupStrategy, context });
        case utils.fieldTypes.image:
            return new FieldObject({ fieldType: 'image', identifier, lookupStrategy, context });
        case utils.fieldTypes.label:
            return new LabelFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.link:
            return new LinkFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.multiDropdown:
            return new MultiDropdownFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.multiReference:
            return new MultiReferenceFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.nestedGrid:
            return new NestedGridObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.numeric:
            return new FieldObject({ fieldType: 'numeric', identifier, lookupStrategy, context });
        case utils.fieldTypes.relativeDate:
            return new RelativeDateFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.progress:
            return new ProgressFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.radio:
            return new RadioFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.reference:
            return new ReferenceFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.richText:
            return new RichTextFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.scan:
            return new FieldObject({ fieldType: 'scan', identifier, lookupStrategy, context });
        case utils.fieldTypes.select:
            return new FieldObject({ fieldType: 'select', identifier, lookupStrategy, context });
        case utils.fieldTypes.separator:
            return new FieldObject({ fieldType: 'separator', identifier, lookupStrategy, context });
        case utils.fieldTypes.switch:
            return new SwitchFieldObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.staticContent:
            return new StaticContentObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.text:
            return new FieldObject({ fieldType: 'text', identifier, lookupStrategy, context });
        case utils.fieldTypes.textArea:
            return new TextAreaFieldObject({ fieldType: 'text-area', identifier, lookupStrategy, context });
        case utils.fieldTypes.codeEditor:
            return new CodeEditorFieldObject({ fieldType: 'plugin', identifier, lookupStrategy, context });
        case utils.fieldTypes.graphiqlEditor:
            return new GraphiqlEditorFieldObject({ fieldType: 'plugin', identifier, lookupStrategy, context });
        case utils.fieldTypes.pdfViewer:
            return new PdfViewerFieldObject({ fieldType: 'plugin', identifier, lookupStrategy, context });
        case utils.fieldTypes.table:
            return new TableObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.pod:
            return new PodObject({ fieldType, identifier, lookupStrategy, context }) as FieldObject;
        case utils.fieldTypes.dynamicPod:
            return new PodObject({ fieldType: 'dynamic-pod', identifier, lookupStrategy, context }) as FieldObject;
        case utils.fieldTypes.vitalPod:
            return new PodObject({ fieldType: 'vital-pod', identifier, lookupStrategy, context }) as FieldObject;
        case utils.fieldTypes.selectionCard:
            return new SelectionCardObject({ identifier, lookupStrategy, context });
        case utils.fieldTypes.summaryTable:
            return new TableObject({ identifier, lookupStrategy, context, subtype: 'table-summary' });
        case utils.fieldTypes.contentTable:
            return new ContentTableObject({ identifier, lookupStrategy, context, fieldType: 'content-table' });
        case utils.fieldTypes.filterEditor:
            return new FilterEditorObject({ identifier, lookupStrategy, context, fieldType: 'filter-editor' });
        case utils.fieldTypes.nodeBrowserTree:
            return new NodeBrowserTreeObject({ identifier, lookupStrategy, context }) as FieldObject;
        case utils.fieldTypes.workflowDesigner:
            return new WorkflowDesignerFieldObject({ identifier, lookupStrategy, context });
        default:
            throw new Error(`Type ${fieldType} not implemented yet`);
    }
};

Then(
    /^the title of the "([^"\n\r]*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal) is "(.*)"$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        context: utils.ElementContext,
        title: string,
    ) => {
        const field = getField({ identifier, lookupStrategy, context, fieldType });
        await field.loseFocus();
        await field.expectTitle(title);
    },
);

Then(
    /^the title of the "(.*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal) is (displayed|hidden)$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        context: utils.ElementContext,
        cssState: 'displayed' | 'hidden',
    ) => {
        const field = getField({ identifier, lookupStrategy, context, fieldType });
        await field.loseFocus();
        await field.expectTitleToBeDisplayed(cssState === 'hidden');
    },
);

Then(
    /^the helper text of the "([^"\n\r]*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal|the detail panel|the sidebar) is "(.*)"$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        context: utils.ElementContext,
        value: string,
    ) => {
        const field = getField({ identifier, lookupStrategy, context, fieldType });
        await field.loseFocusForHelperText();
        await field.expectHelperText(value);
    },
);

Then(
    /^the postfix of the "([^"\n\r]*)" (bound|labelled) text field on the detail panel is "(.*)"$/,
    async (identifier: string, lookupStrategy: utils.LookupStrategy, value: string) => {
        const field = getField({
            identifier,
            lookupStrategy,
            context: utils.ElementContext.HELPER_PANEL,
            fieldType: utils.fieldTypes.text,
        });
        await field.loseFocus();
        await field.expectPostFix(value);
    },
);

Then(
    /^the helper text of the "(.*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal) is (displayed|hidden)$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        context: utils.ElementContext,
        cssState: 'displayed' | 'hidden',
    ) => {
        const field = getField({ identifier, lookupStrategy, context, fieldType });
        await field.loseFocusForHelperText();
        await field.expectHelperTextToBeDisplayed(cssState === 'hidden');
    },
);

Then(
    /^the "([^"\n\r]*)" (bound|labelled) (button|calendar|checkbox|count|date|date-time-range|relative date|dropdown-list|filter select|form designer|icon|image|label|link|multi dropdown|multi reference|numeric|node-browser-tree|progress|radio|reference|rich text|scan|separator|static-content|text|text area|time|select|switch|code editor|graphiql editor|pdf viewer|table|summary table) field on (the main page|a modal|a full width modal|the detail panel|the sidebar) is (displayed|hidden)$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        context: utils.ElementContext,
        cssState: 'displayed' | 'hidden',
    ) => {
        const field = getField({ identifier, lookupStrategy, context, fieldType });
        await field.loseFocus();
        await field.scrollTo();
        await field.expectToBeDisplayed(field.cssSelector, cssState === 'hidden');
    },
);

Then(
    /^the "(.*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal|the sidebar) is (enabled|disabled)$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        context: utils.ElementContext,
        cssState: 'enabled' | 'disabled',
    ) => {
        const field = getField({ identifier, lookupStrategy, context, fieldType });
        await field.loseFocus();
        await field.expectToBeEnabled(
            `${field.cssSelector} ${utils.getElementTypeSelector(fieldType)}`,
            cssState === 'disabled',
        );
    },
);

Then(
    /^the "(.*)" (bound|labelled) (reference|table) field on (the main page|a modal|a full width modal|the detail panel|the sidebar) is (valid|invalid)$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        context: utils.ElementContext,
        valid: 'valid' | 'invalid',
    ) => {
        const field = getField({ identifier, lookupStrategy, context, fieldType });
        await field.loseFocus();
        await field.expectsValidity(valid === 'valid');
    },
);

When(
    /^the user clicks in the "(.*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal|the detail panel|the sidebar)$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        context: utils.ElementContext,
    ) => {
        await browser.pause(500);
        const field = getField({ identifier, lookupStrategy, context, fieldType });
        const f = await $(field.cssSelector);
        if (!(await (await $(field.cssSelector)).isClickable()))
            await browser.execute(elem => elem.scrollIntoView(), f);
        await f.waitForClickable();
        await field.click(utils.getElementTypeSelector(fieldType));
    },
);

When(
    /^the user clicks the "([^"\n\r]*)" (bound|labelled) action of the "([^"\n\r]*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal|the detail panel|the sidebar)$/,
    async (
        actionId: string,
        actionLookupStrategy: utils.LookupStrategy,
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        context: utils.ElementContext,
    ) => {
        const field = getField({ identifier, lookupStrategy, context, fieldType });
        await field.clickFieldAction(actionLookupStrategy, actionId);
    },
);

When(
    /^scrolls to the "(.*)" (bound|labelled) (button) field on (the main page|a modal|a full width modal|the sidebar)$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        context: utils.ElementContext,
    ) => {
        const field = getField({ identifier, lookupStrategy, context, fieldType });
        await field.scrollTo();
    },
);

Then(/^the value of the clipboard is "(.*)"$/, async (value: string) => {
    await AbstractPageObject.assertClipboardValue(value);
});

When(
    /^the user presses ((?:(?:Ctrl|Alt|ArrowDown|ArrowLeft|ArrowRight|ArrowUp|Backspace|Cancel|Clear|Comma|Control|Delete|Dot|Eight|End|Enter|Equal|Escape|F1|F10|F11|F12|F2|F3|F4|F5|F6|F7|F8|F9|Five|Four|Help|Home|Insert|Meta|Minus|Nine|One|PageDown|PageUp|Pause|Plus|Return|Semicolon|Seven|Shift|Six|Slash|Space|Star|Tab|Three|Two|Unidentified|ZenkakuHankaku|Zero|c|v)\+?)+)$/,
    async (key: string) => {
        await AbstractPageObject.press(key);
    },
);

Then(/^the element with the following selector is focused: "(.*)"$/, async (selector: string) => {
    await AbstractPageObject.isFocused(selector);
});

Then(
    /^the "(.*)" (bound|labelled) (button) field (appears|disappears) on (the main page|a modal|a full width modal|the sidebar|the detail panel)$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        state: 'appears' | 'disappears',
        context: utils.ElementContext,
    ) => {
        const field = getField({ identifier, lookupStrategy, context, fieldType });
        await field.expectToAppear({ ignoreContext: false, reverse: state === 'disappears' });
    },
);

// TODO Fix visual snapshot tests
// Then(
//     /^the "(.*)" (bound|labelled) (button|calendar|checkbox|date|label|numeric|progress|rich text|text) field on (the main page|a modal) looks like before$/,
//     async (identifier: string, lookupStrategy: LookupStrategy, fieldType: utils.fieldTypes, context: ElementContext) => {
//         const field = getField(identifier, lookupStrategy, context, fieldType);
//         await field.expectToMatchSnapshot();
//     },
// );

// ----------
// Static store field steps
// ----------
When(
    /^the user selects the "(.*)" (bound|labelled) (image|calendar|checkbox|code editor|count|date|date-time-range|relative date|dropdown-list|filter select|form designer|graphiql editor|icon|label|link|multi dropdown|multi reference|numeric|pod|dynamic-pod|progress|radio|reference|rich text|scan|select|static-content|switch|text|text area|time|vital pod|selection card|content table|filter editor|workflow designer) field on (the main page|a modal|a full width modal|the sidebar|the mobile sidebar|the navigation panel|the detail panel)$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        context: utils.ElementContext,
    ) => {
        // This was added to resolve both XT-80511 and XT-81818 // click on the heading-title in order to close dropdown which is open by default and click on pagination otherwise
        if (context.includes('modal')) {
            const selector =
                'div[aria-hidden="false"] .ag-paging-row-summary-panel, [data-component="dialog"] h1 [data-component="heading"], div[data-element="form-summary"], div[data-element="header-container"] h1';
            // Attempt to click only if it is not mobile device
            if (!(testGlobals.device === 'mobile' || testGlobals.device === 'tablet')) {
                const elementHandle = await browser.$(selector);
                await elementHandle.click();
            }
        }

        const field = getField({ identifier, lookupStrategy, context, fieldType });
        await field.dismissAllNotification();
        const $field = await field.get();
        await utils.waitForElementToExist({
            name: `"${identifier}" ${fieldType} field`,
            selector: field.cssSelector,
        });

        if (testGlobals.device !== 'mobile') {
            await $field.scrollIntoView();
        } else {
            await browser.execute(
                sel => document.querySelector(sel)?.scrollIntoView({ block: 'center', inline: 'nearest' }),
                field.cssSelector,
            );
        }
        await StaticStore.storeField(fieldType, field);
    },
);

When(/^the image field is (defined|undefined)$/, async (status: 'defined' | 'undefined') => {
    const field = <FieldObject>StaticStore.getStoredField(utils.fieldTypes.image);
    await field.expectImageStatus(field.cssSelector, status);
});

When(/^the user adds the "(.*)" image to the image field$/, async (relativeFilePath: string) => {
    const field = <FieldObject>StaticStore.getStoredField(utils.fieldTypes.image);
    await field.uploadImage(relativeFilePath);
});

When(/^the user removes the image from the image field$/, async () => {
    const field = <FieldObject>StaticStore.getStoredField(utils.fieldTypes.image);
    await field.removeImage(field.cssSelector);
});

When(
    /^the user clears the (code editor|date|dropdown-list|filter select|graphiql editor|multi dropdown|multi reference|numeric|reference|rich text|select|text) field$/,
    async (fieldType: utils.FieldTypes) => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        if (fieldType === utils.fieldTypes.multiDropdown) {
            const selector = field.cssSelector;
            const dropdownArrow = await browser.$(`${selector} .e-ui-select-inline-dropdown`);
            // Scroll into view and click the dropdown arrow to close it
            await dropdownArrow.scrollIntoView();
            await dropdownArrow.click();
        }
        await field.clearInput({ ignoreContext: false, fieldType });
    },
);

When(
    /^the user writes "(.*)" in the (code editor|dropdown-list|filter select|graphiql editor|multi dropdown|multi reference|numeric|rich text|scan|select|text|text area) field$/,
    async (value: string, fieldType: utils.FieldTypes) => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        const selection = runtimeParameters.getStringOrParameter(value);
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(selection);
        if (!(await (await $(field.cssSelector)).isClickable())) await field.scrollTo();
        await field.expectToAppear();
        await field.expectToBeEnabled(`${field.cssSelector} ${utils.getElementTypeSelector(fieldType)}`);
        await field.expectToBeReadOnly({ selector: field.cssSelector, FieldType: fieldType, reverse: true });
        await field.write({ content: storeValue });
        await waitForPromises(300, 'write to field');
    },
);

When(
    /^the user selects "(.*)" in the (dropdown-list|filter select|multi dropdown|multi reference|reference|select) field$/,
    async (value: string, fieldType: utils.FieldTypes) => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        // Split the value by "|" to handle multiple options
        const values = value.split('|').map(v => v.trim());
        // Iterate over each option and select it sequentially
        // eslint-disable-next-line no-restricted-syntax
        for (const singleValue of values) {
            const storedValue = StaticStore.getUserdefinedKeyValueFromStore(singleValue);
            await field.select(storedValue);
        }
        // close dropdown by clicking on arrow dropdown
        const fieldElement = await field.get();
        if (fieldType === utils.fieldTypes.multiDropdown) {
            const dropdownArrow = await fieldElement.$('.e-ui-select-inline-dropdown');
            // Scroll into view and click the dropdown arrow to close it
            await dropdownArrow.scrollIntoView();
            await dropdownArrow.click();
        }
        // close the multiReference by pressing key ESC
        if (fieldType === utils.fieldTypes.multiReference) {
            await field.closeList();
        }
    },
);

Then(
    /^the "(.*)" validation (error|warning|info) message of the (checkbox|dropdown-list|multi dropdown|multi reference|reference|select|text|text area|time) field is (displayed|hidden)$/,
    async (value: string, identifier: string, fieldType: utils.FieldTypes, cssState: 'displayed' | 'hidden') => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.expectedValidationMessage({ identifier, value, reverse: cssState === 'hidden', fieldType });
    },
);

When(
    /^the user clicks in the (checkbox|count|date|relative date|dropdown-list|filter select|icon|label|link|multi dropdown|multi reference|numeric|progress|reference|rich text|scan|select|separator|text) field$/,
    async (fieldType: utils.FieldTypes) => {
        await waitForPromises(500, 'Wait loadings');
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.click(utils.getElementTypeSelector(fieldType));
        await waitForPromises(500, 'Wait events');
    },
);

When(
    /^the user clicks the "(.*)" (bound|labelled) action of the (checkbox|date|label|link|multi dropdown|multi reference|numeric|progress|reference|rich text|separator|text) field$/,
    async (actionId: string, actionLookupStrategy: utils.LookupStrategy, fieldType: utils.FieldTypes) => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.clickFieldAction(actionLookupStrategy, actionId);
    },
);

When(
    /^the user scrolls to the (calendar|checkbox|date|relative date|dropdown-list|filter select|label|numeric|progress|reference|multi dropdown|multi reference|rich text|select|separator|text) field$/,
    async (fieldType: utils.FieldTypes) => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.scrollTo({ scrollBlock: 'start' });
    },
);

When(
    /^the user blurs the (checkbox|date|numeric|rich text|text|text area|reference) field$/,
    async (fieldType: utils.FieldTypes) => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.loseFocus();
        await waitForPromises(300, `blur field ${fieldType}`);
    },
);

When(
    /^the user writes "(.*)" to line ([0-9]*) of the (code editor|graphiql editor) field$/,
    async (value: string, lineNumber: number, fieldType: utils.FieldTypes) => {
        const field = <CodeEditorFieldObject>StaticStore.getStoredField(fieldType);
        const selection = runtimeParameters.getStringOrParameter(value);
        await field.writeByLineNumber(selection, lineNumber);
    },
);

When(
    /^the user inserts line ([0-9]*) in the (code editor|graphiql editor) field$/,
    async (lineNumber: number, fieldType: utils.FieldTypes) => {
        const field = <CodeEditorFieldObject>StaticStore.getStoredField(fieldType);
        await field.insertLine(lineNumber);
    },
);

Then(
    /^the value of the (code editor|dropdown-list|relative date|filter select|graphiql editor|multi dropdown|multi reference|numeric|reference|select|static-content|text) field is "(.*)"$/,
    async (fieldType: utils.FieldTypes, value: string) => {
        await pageModel.waitForFinishLoading();
        const storefield = <FieldObject>StaticStore.getStoredField(fieldType);
        await storefield?.checkValue(fieldType, value);
    },
);

Then(
    /^the (html value|value) of the code editor field (is|contains)$/,
    async (valueType: 'html value' | 'value', isContains: 'is' | 'contains', value: string) => {
        await pageModel.waitForFinishLoading();
        const storefield = <CodeEditorFieldObject>StaticStore.getStoredField(utils.fieldTypes.codeEditor);
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        await storefield.click();
        await storefield.expectValue({
            toBe: storeValue || '',
            cssSelector: utils.getElementTypeSelector(utils.fieldTypes.codeEditor),
            ignoreContext: false,
            valueType,
            isContains: isContains === 'contains',
        });
    },
);

Then(
    /^the value of the text area field (is|contains) "(.*)"$/,
    async (isContains: 'is' | 'contains', value: string) => {
        await pageModel.waitForFinishLoading();
        const storefield = <TextAreaFieldObject>StaticStore.getStoredField(utils.fieldTypes.textArea);
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        await storefield.expectValue({
            toBe: storeValue || '',
            cssSelector: utils.getElementTypeSelector(utils.fieldTypes.textArea),
            ignoreContext: false,
            isContains: isContains === 'contains',
        });
    },
);

Then(/^the value of the text area field (is|contains)$/, async (isContains: 'is' | 'contains', value: string) => {
    await pageModel.waitForFinishLoading();
    const storefield = <TextAreaFieldObject>StaticStore.getStoredField(utils.fieldTypes.textArea);
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
    await storefield.expectValue({
        toBe: storeValue || '',
        cssSelector: utils.getElementTypeSelector(utils.fieldTypes.textArea),
        ignoreContext: false,
        isContains: isContains === 'contains',
    });
});

Then(
    /^the suggestion on the (code editor|graphiql editor) field is "(.*)"$/,
    async (fieldType: utils.FieldTypes, expectedLine: string) => {
        const field = <CodeEditorFieldObject>StaticStore.getStoredField(fieldType);
        await field.expectedSuggestions(expectedLine);
    },
);

Then(
    /^the details of the "([^"\n\r]*)" suggestion on the (code editor|graphiql editor) field contain "(.*)"$/,
    async (helperSuggestion: string, fieldType: utils.FieldTypes, value: string) => {
        const field = <CodeEditorFieldObject>StaticStore.getStoredField(fieldType);
        await field.expectSuggestionDetailsToBeVisible(value, helperSuggestion);
    },
);

Then(
    /^the title of the (image|calendar|checkbox|date|date-time-range|relative date|dropdown-list|filter select|form designer|icon|label|numeric|progress|radio|reference|multi dropdown|multi reference|static-content|rich text|text|text area|time|select|switch) field is "(.*)"$/,
    async (fieldType: utils.FieldTypes, title: string) => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.loseFocus();
        await field.scrollTo();
        await field.expectTitle(title);
    },
);

Then(
    /^the title of the (image|calendar|checkbox|date|date-time-range|relative date|dropdown-list|filter select|form designer|icon|label|numeric|progress|radio|reference|multi dropdown|multi reference|rich text|select|switch|text|text area|time) field is (displayed|hidden)$/,
    async (fieldType: utils.FieldTypes, cssState: 'displayed' | 'hidden') => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.loseFocus();
        await field.scrollTo();
        await field.expectTitleToBeDisplayed(cssState === 'hidden', fieldType);
    },
);

Then(
    /^the helper text of the (image|calendar|checkbox|count|date|date-time-range|relative date|dropdown-list|filter select|form designer|icon|label|link|numeric|progress|radio|reference|multi dropdown|multi reference|rich text|scan|select|switch|static-content|text|text area|time) field is "(.*)"$/,
    async (fieldType: utils.FieldTypes, value: string) => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.loseFocusForHelperText();
        await field.scrollTo();
        await field.expectHelperText(value);
    },
);

Then(
    /^the helper text of the (image|calendar|checkbox|date|date-time-range|dropdown-list|filter select|form designer|icon|label|numeric|progress|radio|reference|multi dropdown|multi reference|rich text|select|switch|text|text area|time) field is (displayed|hidden)$/,
    async (fieldType: utils.FieldTypes, cssState: 'displayed' | 'hidden') => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.loseFocusForHelperText();
        await field.scrollTo();
        await field.expectHelperTextToBeDisplayed(cssState === 'hidden');
    },
);

Then(
    /^the (image|calendar|checkbox|date|date-time-range|relative date|dropdown-list|filter select|form designer|multi dropdown|multi reference|numeric|reference|rich text|text|text area|time|select|switch) field is (enabled|disabled)$/,
    async (fieldType: utils.FieldTypes, cssState: 'enabled' | 'disabled') => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.loseFocus();
        await field.scrollTo();

        if (
            fieldType === utils.fieldTypes.image ||
            fieldType === utils.fieldTypes.formDesigner ||
            fieldType === utils.fieldTypes.time ||
            fieldType === utils.fieldTypes.dateTimeRange
        ) {
            await field.expectToBeEnabledClass(field.cssSelector, cssState === 'disabled');
        } else {
            await field.expectToBeEnabled(
                `${field.cssSelector} ${utils.getElementTypeSelector(fieldType)}`,
                cssState === 'disabled',
            );
        }
    },
);

Then(
    /^the (image|calendar|checkbox|code editor|date|date-time-range|dropdown-list|filter select|graphiql editor|multi dropdown|multi reference|numeric|reference|rich text|select|switch|text|text area|time) field is read-only$/,
    async (fieldType: utils.FieldTypes) => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.loseFocus();
        await field.scrollTo();
        await field.expectToBeReadOnly({ selector: field.cssSelector, FieldType: fieldType });
    },
);

Then(
    /^the (checkbox|date|relative date|numeric|rich text|text|nested grid|pod collection) field is (valid|invalid)$/,
    async (fieldType: utils.FieldTypes, valid: 'valid' | 'invalid') => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.loseFocus();
        await field.expectsValidity(valid === 'valid');
    },
);

Then(
    /^the focus on the (dropdown-list|filter select|multi dropdown|multi reference|reference|select|switch) field is visible$/,
    async (fieldType: utils.FieldTypes) => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.expectFocusToBeVisible();
    },
);

Then(/^the placeholder of the (select) field is "(.*)"$/, async (fieldType: utils.FieldTypes, placeholder: string) => {
    const field = <FieldObject>StaticStore.getStoredField(fieldType);
    await field.loseFocus();
    await field.expectPlaceholder(placeholder);
});

Then(
    /^the (calendar|checkbox|date|relative date|label|numeric|progress|rich text|separator|text) field (appears|disappears)$/,
    async (fieldType: utils.FieldTypes, state: 'appears' | 'disappears') => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.expectToAppear({ ignoreContext: false, reverse: state === 'disappears' });
    },
);

Then(/^the postfix of the text field on the detail panel is "(.*)"$/, async (value: string) => {
    const field = <FieldObject>StaticStore.getStoredField(utils.fieldTypes.text);
    await field.loseFocus();
    await field.expectPostFix(value);
});

Then(
    /^the user stores the value of the (calendar|checkbox|code editor|count|dropdown-list|filter select|graphiql editor|icon|label|link|multi dropdown|multi reference|numeric|progress|radio|reference|rich text|scan|select|switch|text|text area) field with the key "(.*)"$/,
    async (fieldType: utils.FieldTypes, keyValue: string) => {
        const storefield = <FieldObject>StaticStore.getStoredField(fieldType);
        await storefield.setElementValueToStore({
            keyValue,
            fieldType,
            TypeSelector: utils.getElementTypeSelector(fieldType),
        });
        await utils.takeScreenshot();
    },
);

When(/^the user refreshes the screen$/, async () => {
    await browser.refresh();
    await pageModel.waitForFinishLoading();
});

When(/^the user navigates back using the browser back button$/, async () => {
    await browser.back();
    // the refresh is needed for the page to load - this workaround will be removed when XT-91280 is fixed
    await browser.refresh();
    await pageModel.waitForFinishLoading();
});

When(
    /^at least the following list of options is displayed for the (dropdown-list|filter select|select|reference) field:"(.*)"$/,
    async (fieldType: utils.FieldTypes, value: string) => {
        const field = <FieldObject>StaticStore.getStoredField(fieldType);
        await field.selectDropDown.expectOptionsToBe(value);
    },
);

Then(/^the time elapsed in the text field is greater than "(.*)"$/, async (value: string) => {
    const field = <FieldObject>StaticStore.getStoredField(utils.fieldTypes.text);
    if (value.includes('[') && value.includes(']')) {
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        await field.expectTimeElapsedToBeGreaterThan(storeValue);
    } else {
        await field.expectTimeElapsedToBeGreaterThan(value);
    }
});

When(/^the user hovers over the (checkbox|dropdown-list) field$/, async (fieldType: utils.FieldTypes) => {
    const field = <FieldObject>StaticStore.getStoredField(fieldType);
    await field.hoverOverField();
});
