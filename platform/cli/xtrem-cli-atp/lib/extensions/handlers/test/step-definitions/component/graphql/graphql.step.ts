import { Then, When } from '@cucumber/cucumber';
import * as StaticStore from '../static-store';
import { GraphQLPage } from './graphql-page';

const graphQLPage = new GraphQLPage();

When(/^the user writes "(.*)" GraphQL request/, async (requestFile: string) => {
    const graphqlRequest = await graphQLPage.loadGraphQLFile(requestFile);
    graphQLPage.attachTextValue('Request file', graphqlRequest);
    await graphQLPage.graphQlEditor.writeToEditorPanel(graphqlRequest);
});

When(/^the user clicks the "(.*)" button in the GraphQL page header$/, async (button: string) => {
    await graphQLPage.graphQlEditor.clickEditorButton(button);
});

Then(/^the "(.*)" GraphQL response is valid/, async (responseFile: string) => {
    const graphqlResponse = await graphQLPage.loadResponseFile(responseFile);
    graphQLPage.attachTextValue('Expected response file', graphqlResponse);
    await graphQLPage.graphQlEditor.compareResultPanel(graphqlResponse);
});

When(/^the user selects the "(.*)" GraphQL property$/, async (property: string) => {
    const resultProperty = await graphQLPage.graphQlEditor.getPropertyFromResultPanel(property);
    StaticStore.storeObject(StaticStore.StoredKeys.GRAPHIQL_EDITOR, resultProperty);
});

Then(/^the selected GraphQL property value (is|is not|contains) "(.*)"$/, async (testType: string, value: string) => {
    const resultProperty = StaticStore.getStoredObject(StaticStore.StoredKeys.GRAPHIQL_EDITOR);
    await graphQLPage.graphQlEditor.compareProperties(testType, resultProperty, value, true);
});

Then(/^the selected GraphQL property value (is|is not|contains)$/, async (testType: string, value: string) => {
    const resultProperty = StaticStore.getStoredObject(StaticStore.StoredKeys.GRAPHIQL_EDITOR);
    await graphQLPage.graphQlEditor.compareProperties(testType, resultProperty, value, true);
});

Then(
    /^the selected GraphQL property value is (lower than|equal to|greater than) "(.*)"$/,
    async (testType: string, value: string) => {
        const resultProperty = StaticStore.getStoredObject(StaticStore.StoredKeys.GRAPHIQL_EDITOR);
        await graphQLPage.graphQlEditor.compareProperties(testType, resultProperty, value.toString(), true);
    },
);

Then(/^the user attaches the actual GraphQL response to allure report$/, async () => {
    const actualResponse = await graphQLPage.graphQlEditor.readResultPanel();
    graphQLPage.attachTextValue('Actual response', actualResponse);
});
