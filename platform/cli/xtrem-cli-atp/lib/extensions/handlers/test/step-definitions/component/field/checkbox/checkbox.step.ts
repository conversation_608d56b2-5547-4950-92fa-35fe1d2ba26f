import { Then, When } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { CheckBoxFieldObject } from './checkbox-object';

// ----------
// Static store field steps
// ----------
When(/^the user (ticks|unticks) the checkbox field$/, async (state: 'ticks' | 'unticks') => {
    const field = <CheckBoxFieldObject>StaticStore.getStoredField(fieldTypes.checkbox);
    await field.setCheckboxToValue(state);
});

Then(/^the value of the checkbox field is "(.*)"$/, async (value: string) => {
    const field = <CheckBoxFieldObject>StaticStore.getStoredField(fieldTypes.checkbox);
    await field.loseFocus();
    await field.expectCheckedValue(value);
});
