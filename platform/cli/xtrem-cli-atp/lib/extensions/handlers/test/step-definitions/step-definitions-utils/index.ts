import { expect as chaiExpect } from 'chai';
import { testGlobals } from '../component/test-globals';
import { atpEnv } from './atp-env';
import { BaseFieldTypes, NonNestedFieldTypes } from './enums';

export * from './enums';
export * from './function-types';
export * from './types';

export * from './atp-env';

/*
###########################################################
#                       CONSTANTS                         #
###########################################################
*/

export const timeoutWaitFor = atpEnv.timeoutWaitFor; // testGlobals.cfgTimeout ? testGlobals.cfgTimeout - 2000 : 4000;

export const valueCheckTimeout = atpEnv.timeout;

export const globalTimeout = testGlobals.cfgTimeout ? testGlobals.cfgTimeout - 2000 : 4000;

export const fieldTypes = { ...BaseFieldTypes, ...NonNestedFieldTypes };

export const nestedFieldTypes = { ...BaseFieldTypes };

export const expect = chaiExpect;

/*
###########################################################
#                       REGEX PATTERNS                    #
###########################################################
*/

export const regex_urls =
    /^(http|https):\/\/([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)(:[0-9]{1,5})?(\/handheld(\/settings)?)(?:\/|$)/;

/*
###########################################################
#                        FUNCTIONS                        #
###########################################################
*/

export * from './functions/element-functions';
export * from './functions/expectation-functions';
export * from './functions/regex-functions';
export * from './functions/selector-functions';
export * from './functions/util-functions';
