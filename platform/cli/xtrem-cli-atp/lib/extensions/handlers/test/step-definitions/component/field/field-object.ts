import * as fs from 'fs';
import { camelCase } from 'lodash';
import * as utils from '../../step-definitions-utils';
import AbstractPageObject from '../abstract-page-object';
import * as StaticStore from '../static-store';
import { isMobileDevice } from '../test-globals';
import { SelectDropDownObject } from './select-dropdown-object';
import { scrollUntilClickable } from './table/tableUtils';
import { waitForPromises } from './wait-util';
import path = require('node:path');

export class FieldObject extends AbstractPageObject {
    public readonly selectDropDown: SelectDropDownObject;

    protected readonly helperTextSelector = '[data-element="help"]';

    protected readonly labelSelector = '[data-element="label"]';

    protected readonly legendSelector = 'legend';

    protected readonly postFixSelector = 'span.e-field-postfix';

    protected fieldType: string;

    constructor({
        fieldType,
        identifier,
        lookupStrategy,
        context,
        blankDomSelector = false,
    }: {
        fieldType: string;
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
        blankDomSelector?: boolean;
    }) {
        let superSelectorToUse;

        if (!blankDomSelector) {
            superSelectorToUse =
                (context ? `${utils.getContextSelector(context)} ` : '') +
                utils.getLookupStrategySelector({ fieldType, lookupStrategy, identifier });
        } else {
            superSelectorToUse =
                (context ? `${utils.getContextSelector(context)} ` : '') +
                utils.getLookupStrategySelectorwithNoDom({ fieldType, lookupStrategy, identifier });
        }

        super(superSelectorToUse);
        this.selectDropDown = new SelectDropDownObject(this.cssSelector);
    }

    // Override loseFocus to inject a waitForPromises
    override async click(cssSelector?: string, ignoreContext = false) {
        await utils.waitForElementToExist({
            name: 'element',
            selector: this.getSelectorForOperation(cssSelector, ignoreContext),
        });
        await utils.waitForElementToBeDisplayed({
            name: 'element',
            selector: this.getSelectorForOperation(cssSelector, ignoreContext),
        });
        await super.click(cssSelector, ignoreContext);
        await waitForPromises(100, 'click override in field');
    }

    async expectTableToBeEnabled(reverse = false) {
        await this.expectToBeEnabledClass(this.cssSelector, reverse);
    }

    async expectTitle(expectedTitle: string) {
        await this.expectTextContent({ toBe: expectedTitle, ignoreCase: false, cssSelector: this.labelSelector });
    }

    async expectTitleToBeDisplayed(reverse = false, fieldType?: string) {
        const isDateTime = fieldType === utils.fieldTypes.dateTimeRange ? ' > ' : ' ';
        await this.expectToBeDisplayed(`${this.cssSelector}${isDateTime}${this.labelSelector}`, reverse);
    }

    async expectHelperText(expectedHelperTextContent: string) {
        await this.expectTextContent({
            toBe: expectedHelperTextContent,
            ignoreCase: false,
            cssSelector: this.helperTextSelector,
        });
    }

    async expectHelperTextToBeDisplayed(reverse = false) {
        await this.expectToBeDisplayed(`${this.cssSelector} ${this.helperTextSelector}`, reverse);
    }

    async expectTableHelperText(expectedHelperTextContent: string) {
        const selectorToUse = `${this.helperTextSelector}:not(table [data-element="help"])`;
        await this.expectTextContent({
            toBe: expectedHelperTextContent,
            ignoreCase: false,
            cssSelector: selectorToUse,
        });
    }

    async expectTableHelperTextToBeDisplayed(reverse = false) {
        await this.expectToBeDisplayed(
            `${this.cssSelector} ${this.helperTextSelector}:not(table [data-element="help"])`,
            reverse,
        );
    }

    async expectPostFix(expectedPostFixContent: string) {
        await this.expectTextContent({
            toBe: expectedPostFixContent,
            ignoreCase: false,
            cssSelector: this.postFixSelector,
        });
    }

    async expectPlaceholder(expectedPlaceholder: string) {
        const selectorToUse = this.getSelectorForOperation(`${this.cssSelector} input`, true);
        let result = '';
        try {
            await browser.waitUntil(
                async () => {
                    const element = await browser.$(selectorToUse);
                    result = await element.getAttribute('placeholder');
                    return result === expectedPlaceholder;
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(
                `Expected placeholder: ${expectedPlaceholder}, actual: ${result}.\nSelector: ${selectorToUse}`,
            );
        }
    }

    async expectsValidity(expectedToBeValid: boolean) {
        const selectorToUse = this.getSelectorForOperation('span[type="error"]');
        await this.loseFocus();

        try {
            if (expectedToBeValid) {
                await this.expectToDisappear(selectorToUse, true);
            } else {
                await this.expectToAppear({ cssSelector: selectorToUse, ignoreContext: true });
            }
        } catch (error) {
            throw new Error(
                `Expected Element to be ${expectedToBeValid ? '' : 'in'}valid.\nSelector: ${selectorToUse}`,
            );
        }
    }

    async expectedValidationMessage({
        identifier,
        value,
        reverse = false,
        fieldType,
    }: {
        identifier: string;
        value: string;
        reverse?: boolean;
        fieldType?: string;
    }) {
        let actualValue = '';
        const selectorToUse = this.getSelectorForOperation(`span[type="${identifier}"]`);
        await this.expectToBeDisplayed(selectorToUse, reverse);
        if (!reverse) {
            if (fieldType !== utils.fieldTypes.time) {
                const portalEntrance = await this.find('[data-portal-entrance]');
                const portalId = await portalEntrance.getAttribute('data-portal-entrance');
                const portalExit = await this.find(`[data-portal-exit="${portalId}"]`, true);
                actualValue = (await portalExit.getText()).replace(/(\r\n|\n|\r)/gm, '');
                if (actualValue.toLowerCase() !== value.toLowerCase()) {
                    throw new Error(`Expected value: ${value}, actual: ${actualValue}.\nSelector: ${selectorToUse}`);
                }
            } else {
                const textSelector = await $(selectorToUse);
                await textSelector.moveTo();
                actualValue = (await textSelector.getAttribute('aria-label')).replace(/(\r\n|\n|\r)/gm, '');
                if (actualValue.toLowerCase() !== value.toLowerCase()) {
                    throw new Error(`Expected value: ${value}, actual: ${actualValue}.\nSelector: ${selectorToUse}`);
                }
            }
        }
    }

    override async expectValue({
        toBe,
        cssSelector = 'input',
        ignoreContext = false,
        multiLine = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
        multiLine?: boolean;
    }) {
        await super.expectValue({ toBe, cssSelector, ignoreContext, multiLine });
    }

    override async expectBackgroundColor({
        toBe,
        cssSelector = 'input',
        ignoreContext = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        await super.expectBackgroundColor({ toBe, cssSelector, ignoreContext });
    }

    override async expectBorderColor({
        toBe,
        cssSelector = 'input',
        ignoreContext = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        await super.expectBorderColor({ toBe, cssSelector, ignoreContext });
    }

    override async expectTextColor({
        toBe,
        cssSelector = 'input',
        ignoreContext = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        await super.expectTextColor({ toBe, cssSelector, ignoreContext });
    }

    async clearContent() {
        await this.write({ content: '' });
    }

    async clickFieldAction(actionLookupStrategy: utils.LookupStrategy, actionId: string) {
        await waitForPromises(0, 'before click field action');
        const selectorToUse = `[data-testid~="e-header-action-${
            actionLookupStrategy === utils.LookupStrategy.BIND ? `bind-${actionId}` : `label-${camelCase(actionId)}`
        }"]`;
        const fieldActionbtn = await this.find(selectorToUse);
        await fieldActionbtn.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${actionId}" action.\nSelector: ${selectorToUse}`,
        });
        await fieldActionbtn.scrollIntoView();
        await browser.execute(`document.querySelector('${selectorToUse}').click()`);
        await waitForPromises(0, 'click field action');
    }

    async select(value: string) {
        const browserURL = await browser.getUrl();
        if (utils.regex_handheld(browserURL)) {
            const navBar = await this.find('[data-component="navigation-bar"]', true);
            const yOffset = (await navBar.getSize()).height;
            await (await browser.$(this.cssSelector)).moveTo({ yOffset });
        } else {
            await this.scrollTo();
        }

        await this.selectDropDown.selectOption(value);
    }

    async expectFocusToBeVisible() {
        await browser.waitUntil(() =>
            browser.execute<boolean, any[]>(
                `const isDescendant = function(parent, child) {
                        let node = child.parentNode;
                        while (node) {
                            if (node === parent) {
                                return true;
                            }

                            node = node.parentNode;
                        }
                        return false;
                    };

                    document.activeElement && isDescendant(document.querySelector('${this.cssSelector}'), document.activeElement);`,
                { timeout: this.valueCheckTimeout },
            ),
        );
    }

    override async expectToBeMandatory(selector: string) {
        await super.expectToBeMandatory(`${selector} ${this.labelSelector}`);
    }

    async setElementValueToStore({
        keyValue,
        fieldType,
        TypeSelector,
    }: {
        keyValue: string;
        fieldType: utils.FieldTypes;
        TypeSelector: string;
    }) {
        const selectorToUse = this.getSelectorForOperation(TypeSelector, false);
        const element = await browser.$(selectorToUse);
        await element.waitForExist({
            timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
            timeout: this.timeoutWaitFor,
        });

        const parentElement = await browser.$(this.cssSelector);
        const elemAtt = await parentElement.getAttribute('class');
        if (!elemAtt.includes('e-hidden')) {
            await element.scrollIntoView();
            await element.waitForDisplayed({
                timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
                timeout: this.timeoutWaitFor,
            });
        }

        let textContent = '';

        try {
            switch (fieldType) {
                case utils.fieldTypes.dropdownList:
                case utils.fieldTypes.filterSelect:
                case utils.fieldTypes.multiDropdown:
                case utils.fieldTypes.numeric:
                case utils.fieldTypes.reference:
                case utils.fieldTypes.select:
                case utils.fieldTypes.text:
                case utils.fieldTypes.date:
                case utils.fieldTypes.textArea:
                    textContent = await element.getValue();
                    break;
                case utils.fieldTypes.multiReference:
                    textContent = (await Promise.all((await element.$$('.e-ui-select-label')).map(e => e.getText())))
                        .map(t => t.trim())
                        .join('|');
                    break;
                default:
                    textContent = await element.getText();
            }
            StaticStore.storeObject(keyValue, textContent);
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Value not set".\nSelector: ${selectorToUse}`);
        }
    }

    async uploadImage(relativeFilePath: string) {
        const parentSelector = (await this.get()).selector.toString();
        const inputFileSelector = 'input[id="inputFile"]';
        const selector = `${parentSelector} ${inputFileSelector}`;
        const inputFile = await this.find(selector, true);

        await inputFile.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${relativeFilePath}" deposit field.\nSelector: ${selector}`,
        });

        const featurePath = path
            .resolve(process.env.FEATURE_PATH!)
            .substring(0, path.resolve(process.env.FEATURE_PATH!).lastIndexOf('/'));
        const absolutePath = path.resolve(path.join(featurePath, relativeFilePath));

        if (!fs.existsSync(absolutePath)) {
            throw new Error(`Expected file could not be found: ${relativeFilePath}`);
        }

        await browser.execute(`document.querySelector('${inputFileSelector}').classList.remove("e-hidden")`);
        await inputFile.waitForExist();
        await inputFile.waitForDisplayed();
        await inputFile.setValue(process.platform === 'win32' ? absolutePath : absolutePath.replace(/\\/g, '/'));
    }

    async removeImage(selector: string) {
        const element = await this.find(selector, true);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found.\nSelector: ${element.selector}`,
        });

        const selectorToUse = utils.getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: 'e-image-field-remove',
        });
        const placeHolder = await element.$(selectorToUse);
        const isDisplayed = (await placeHolder.isExisting()) && (await placeHolder.isDisplayed());

        if (!isDisplayed) {
            await browser.takeScreenshot();
            throw new Error(`Expected element could not be removed.\nSelector: ${element.selector} ${selectorToUse}`);
        }
        await placeHolder.click();
    }

    async expectImageStatus(selector: string, status: 'defined' | 'undefined') {
        const element = await this.find(selector, true);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found.\nSelector: ${element.selector}`,
        });

        let selectorToUse = '';
        let isDisplayed;
        let placeHolder;

        if (status === 'defined') {
            selectorToUse = '.e-image-field-content-wrapper img';
            placeHolder = await element.$(selectorToUse);
            isDisplayed = (await placeHolder.isExisting()) && (await placeHolder.isDisplayed());
        } else if (status === 'undefined') {
            selectorToUse = utils.getDataTestIdSelector({
                domSelector: 'div',
                dataTestIdValue: 'e-image-field-placeholder',
            });
            placeHolder = await element.$(selectorToUse);
            isDisplayed = (await placeHolder.isExisting()) && (await placeHolder.isDisplayed());
            if (!isDisplayed) {
                selectorToUse = 'span[data-element="plus"]';
                placeHolder = await element.$(selectorToUse);
                isDisplayed = (await placeHolder.isExisting()) && (await placeHolder.isDisplayed());
            }
        }

        if (!isDisplayed) {
            await browser.takeScreenshot();
            throw new Error(`Expected element to be ${status}.\nSelector: ${element.selector} ${selectorToUse}`);
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async checkValue(fieldType: utils.FieldTypes, value: string) {
        const storefield = <FieldObject>StaticStore.getStoredField(fieldType);

        const browserURL = await browser.getUrl();
        if (isMobileDevice() && utils.regex_handheld(browserURL)) {
            await scrollUntilClickable(storefield.cssSelector);
        }

        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        switch (fieldType) {
            case utils.fieldTypes.graphiqlEditor:
            case utils.fieldTypes.multiDropdown:
                await storefield.expectValue({
                    toBe: storeValue || '',
                    cssSelector: utils.getElementTypeSelector(fieldType),
                });
                break;
            default:
                await storefield.loseFocus();
                await storefield.expectValue({
                    toBe: storeValue || '',
                    cssSelector: utils.getElementTypeSelector(fieldType),
                });
                break;
        }
    }

    async expectTimeElapsedToBeGreaterThan(value: string) {
        const selectorToUse = `${this.cssSelector} input`;
        const element = await this.find(selectorToUse, true);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found.\nSelector: ${selectorToUse}`,
        });
        const timeElapsed = await element.getValue();
        const timeElapsedInSec = utils.timeToSeconds(timeElapsed);
        if (utils.timeToSeconds(value) >= timeElapsedInSec) {
            throw new Error(`Unexpected time elapsed.\nSelector: ${selectorToUse}`);
        }
    }

    async hoverOverField() {
        const selectorToUse = this.getSelectorForOperation('input');
        await this.expectToBeDisplayed(selectorToUse);
        const element = await browser.$(selectorToUse);
        await element.moveTo();
    }
}
