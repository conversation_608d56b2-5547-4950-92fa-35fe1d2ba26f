/* step defintition to remove after merge*/

// // step generating ambiguity and to remove once EPIC XT-49269 is delivered
// //
// // When(/^the user clicks on the "([^"\n\r]*)" card list's record$/, async (rowId: string) => {
// //     const cardList = new CardListObject('field', LookupStrategy.BIND, ElementContext.MAIN_PAGE);
// //     await cardList.selectCard(rowId);
// //     await pageModel.waitForFinishLoading();
// // });

// /**
//  * @deprecated the user selects the "$3" $4 table field on $5\nthe user $1 the main checkbox of the card $2 in the table field
//  */
// When(
//     /^the user (selects|unselects) the main checkbox on the card ([0-9]*) of the "([^"\n\r]*)" (bound|labelled) table field on (the main page|a modal|the detail panel|the sidebar)$/,
//     async (
//         selection: string,
//         cardNumber: number,
//         identifier: string,
//         lookupStrategy: LookupStrategy,
//         context: ElementContext,
//     ) => {
//         const cardList = new CardListObject(identifier, lookupStrategy, context);
//         await cardList.selectCardCheckbox(selection, cardNumber, identifier, lookupStrategy, context);
//         await waitForPromises(500, 'main checkbox selection');
//     },
// );

// /**
//  * @deprecated the user selects the "$3" $4 table field on $5\nthe user $1 the main checkbox of the card with the text "$2" in the table field
//  */
// When(
//     /^the user (selects|unselects) the main checkbox on the card with the text "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) table field on (the main page|a modal|the detail panel|the sidebar)$/,
//     async (
//         selection: string,
//         cardValue: string,
//         identifier: string,
//         lookupStrategy: LookupStrategy,
//         context: ElementContext,
//     ) => {
//         const cardList = new CardListObject(identifier, lookupStrategy, context);
//         await cardList.selectCardCheckboxByText(selection, cardValue, identifier, lookupStrategy, context);
//         await waitForPromises(500, 'main checkbox selection');
//     },
// );

// /**
//  * @deprecated the user selects the "" bound table field on the main page\nthe value of the "$1" bound nested $2 field of the card $3 in the table field is "$4"
//  */
// Then(
//     /^the value of the "([^"\n\r]*)" bound nested (date|label|numeric|reference|select|text|link) field of the row (.*) in the card list is "(.*)"$/,
//     async (identifier: string, fieldType: NestedFieldTypes, rowNumber: number, expectedContent: string) => {
//         const field = new CardListNestedFieldObject(
//             identifier,
//             LookupStrategy.BIND,
//             fieldType,
//             rowNumber,
//             ElementContext.CARD_LIST,
//         );
//         await field.loseFocus();
//         await field.expectTextContent(expectedContent, false, fieldType === 'label' ? 'span span' : 'span');
//     },
// );

// When(
//     /^the user clicks on the "(Calendar|Filter)" button in the header of the card list$/,
//     async (buttonName: 'Calendar' | 'Filter') => {
//         const cardList = new CardListObject('field', LookupStrategy.BIND, ElementContext.MAIN_PAGE);
//         await cardList.clickOnHeaderButton(buttonName);
//     },
// );

// /**
//  * @deprecated the user selects the "" bound table field on the main page\nthe "$1" $2 nested image field of the card $3 in the table field is $4
//  */
// When(
//     /^the "([^"\n\r]*)" (bound|labelled) nested image field of row ([0-9]*) in the card list is (defined|undefined)$/,
//     async (identifier: string, lookupStrategy: LookupStrategy, rowNumber: number, status: 'defined' | 'undefined') => {
//         const field = new CardListNestedFieldObject(
//             identifier,
//             lookupStrategy,
//             nestedFieldTypes.image,
//             rowNumber,
//             ElementContext.CARD_LIST,
//         );
//         await field.loseFocus();
//         await field.expectRowImageStatus(identifier, status);
//     },
// );

// /**
//  * @deprecated the user selects the "" bound table field on the main page\nthe dropdown action "$1" of the card $2 in the table field is $3
//  */
// Then(
//     /^the dropdown action "([^"\n\r]*)" of the "([^"\n\r]*)" card list's record is (enabled|disabled)$/,
//     async (dropdownAction: string, rowId: string, expectedValue: EnabledState) => {
//         const cardList = new CardListObject('field', LookupStrategy.BIND, ElementContext.MAIN_PAGE);
//         await cardList.expectEnabledState(dropdownAction, rowId, expectedValue);
//     },
// );

// /**
//  * @deprecated the user selects the "$3" $4 table field on $5\nthe user clicks the dropdown action "$1" of the card $2 in the table field
//  */
// When(
//     /^the user clicks on the dropdown action "([^"\n\r]*)" of card ([0-9]*) of the "([^"\n\r]*)" (bound|labelled) table field on (the main page|a modal)$/,
//     async (
//         actionName: string,
//         cardNumber: number,
//         identifier: string,
//         lookupStrategy: LookupStrategy,
//         context: ElementContext,
//     ) => {
//         const cardList = new CardListObject(identifier, lookupStrategy, context);
//         await cardList.clickDropdownAction(actionName, cardNumber);
//         await waitForPromises(500, 'dropdown action selection');
//     },
// );

// /**
//  * @deprecated the user selects the "$2" $3 table field on $4\nthe user clicks the card $1 in the table field
//  */
// When(
//     /^the user clicks card ([0-9]*) of the "([^"\n\r]*)" (bound|labelled) table field(?: in )?(the main page|a modal|the navigation panel)?$/,
//     async (rowNumber: number, identifier: string, lookupStrategy: LookupStrategy, context: ElementContext) => {
//         const field = new TableObject(identifier, lookupStrategy, context);
//         await field.clickCard(rowNumber);
//     },
// );
