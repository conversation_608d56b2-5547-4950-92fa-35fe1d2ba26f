/* eslint-disable class-methods-use-this */
import { camelCase, trimEnd } from 'lodash';
import * as utils from '../../../../step-definitions-utils';
import AbstractPageObject from '../../../abstract-page-object';
import { DropDownMenuObject } from '../../../dropdown-menu-object';
import { SELECTORS as FilterObjectSelectors } from '../../../filter-object';
import { waitForPromises } from '../../wait-util';
import { TableObject } from '../table-object';

const SELECTOR = {
    rowIndexSelector: (rowNumber: number) => `[row-index="${rowNumber - 1}"]`,
};

export class TableExpect extends AbstractPageObject {
    static async expectButtonEnabled(elem: WebdriverIO.Element) {
        await browser.waitUntil(
            async () => {
                const isDisabled = await elem.getAttribute('aria-disabled');
                return isDisabled !== 'true';
            },
            {
                timeoutMsg: `Expected element to be enabled.\nSelector: ${elem.selector.toString()}`,
            },
        );
    }

    private table: TableObject;

    constructor(table: TableObject) {
        super(table.cssSelector);
        this.table = table;
    }

    expectBulkActionBarToBeDisplayed: utils.AsyncBooleanReturnVoid = async reverse => {
        const bulkActionBar = '.e-page-navigation-panel-bulk-actions-bar';
        const elem = await this.find(bulkActionBar);
        await utils.expectElementToBeDisplayed({ selector: elem.selector.toString(), reverse, name: 'bulk-bar' });
        const isElement = await (await this.find(bulkActionBar)).isExisting();
        if ((isElement && reverse) || (!isElement && !reverse)) {
            await utils.takeScreenshot();
            throw new Error(`Expected Element to be ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${bulkActionBar}`);
        }
    };

    expectClearSelectedItemsLinkToBeEnabled: utils.AsyncObjectStringLookupStrategyReturnVoid = async ({
        id,
        lookupStrategy,
        reverse,
    }) => {
        const clearItemSelectionLink = '.e-reference-custom-filter-clear-container span';

        if (!(await (await $(clearItemSelectionLink)).isExisting())) {
            await this.table.filter.openFilter(id, lookupStrategy);
        }

        await this.expectToBeEnabled(clearItemSelectionLink, reverse);
    };

    expectClearSelectionToBeDisplayed: utils.AsyncBooleanReturnVoid = async reverse => {
        const clearButton = '.e-page-navigation-panel-bulk-actions-bar-clear-selection';
        const elem = await this.find(clearButton);
        await utils.expectElementToBeDisplayed({
            selector: elem.selector.toString(),
            reverse,
            name: 'clear-selection',
        });
        const isElement = await elem.isExisting();
        if ((isElement && reverse) || (!isElement && !reverse)) {
            await utils.takeScreenshot();
            throw new Error(`Expected Element to be ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${clearButton}`);
        }
    };

    expectColumnError: utils.AsyncObjectStringLookupStrategyReturnVoid = async ({ id, lookupStrategy, reverse }) => {
        const selectorToUse = FilterObjectSelectors.getColumnHeaderSelector(id, lookupStrategy);
        const columnHeader = await this.find(selectorToUse);

        if (!(await columnHeader.isExisting()))
            throw new Error(`Expected element could not be found: "${id}" field.\nSelector: ${selectorToUse}`);

        const newError = await browser.$(`${selectorToUse}.e-table-field-header-column-error`);
        const errorExists = await newError.isExisting();

        if (reverse !== errorExists) {
            const expectedError = reverse ? 'errors' : 'no error';
            throw new Error(`Expected "${id}" column to contain: ${expectedError}.\nSelector: ${selectorToUse}`);
        }
    };

    expectDropdownActionEnabledState = async ({
        expectedDropdownActionText,
        rowNumber,
        expectedState,
    }: {
        expectedDropdownActionText: string;
        rowNumber: number;
        expectedState: utils.EnabledState;
    }): Promise<void> => {
        await this.scrollTo({
            selector: `${SELECTOR.rowIndexSelector(rowNumber)} .e-table-field-dropdown-actions-cell`,
        });
        const dropdownPopover = await this.find(
            `${SELECTOR.rowIndexSelector(rowNumber)} [data-component="action-popover-button"]`,
        );
        if (await dropdownPopover.isExisting()) {
            const dropdownMenu = new DropDownMenuObject(`${this.cssSelector} ${SELECTOR.rowIndexSelector(rowNumber)}`);
            await waitForPromises(500, 'Waiting for dropdown menu');
            await dropdownMenu.expectDropdownMenuActionToBeEnabled(expectedDropdownActionText, expectedState);
        } else {
            const dropdownSingle = await this.find(
                `${SELECTOR.rowIndexSelector(rowNumber)} [data-testid="e-popover-action-single-button"]`,
            );
            const actionText = await dropdownSingle.getText();
            if (utils.formatString(actionText) === utils.formatString(expectedDropdownActionText)) {
                const ariaDisabled = await dropdownSingle.getAttribute('aria-disabled');
                const disabled = await dropdownSingle.getAttribute('disabled');
                const currentState: utils.EnabledState =
                    ariaDisabled === 'true' || disabled === '' ? 'disabled' : 'enabled';

                if (currentState !== expectedState) {
                    throw new Error(
                        `Expected element to be ${expectedState}.\nSelector: ${dropdownSingle.selector.toString()}`,
                    );
                }
            }
        }
    };

    expectDropdownActionToBeDisplayed: utils.AsyncObjectStringNumberBooleanReturnVoid = async ({
        id,
        rowNumber,
        reverse = false,
    }) => {
        await this.scrollTo({
            selector: `${SELECTOR.rowIndexSelector(rowNumber)} .e-table-field-dropdown-actions-cell`,
        });
        const dropdownPopover = await this.find(
            `${SELECTOR.rowIndexSelector(rowNumber)} [data-component="action-popover-button"]`,
        );
        if (await dropdownPopover.isExisting()) {
            const dropdownMenu = new DropDownMenuObject(`${this.cssSelector} ${SELECTOR.rowIndexSelector(rowNumber)}`);
            await waitForPromises(500, 'Waiting for dropdown menu');
            await dropdownMenu.expectDropdownMenuActionToBeDisplayed(id, reverse);
        } else {
            const dropdownSingle = await this.find(
                `${SELECTOR.rowIndexSelector(rowNumber)} [data-testid="e-popover-action-single-button"]`,
            );
            const actionText = await dropdownSingle.getText();
            if (utils.formatString(actionText) === utils.formatString(id)) {
                const isElement = await dropdownSingle.isExisting();
                if ((isElement && reverse) || (!isElement && !reverse)) {
                    await utils.takeScreenshot();
                    throw new Error(
                        `Expected Element to be ${
                            reverse ? 'hidden' : 'displayed'
                        }.\nSelector: ${dropdownSingle.selector.toString()}`,
                    );
                }
            }
        }
    };

    expectErrorIconToBeDisplayed: utils.AsyncBooleanReturnVoid = async reverse => {
        const selectorToUse = this.getSelectorForOperation('span[type="error"]');
        const isElement = await (await this.find(selectorToUse)).isExisting();
        if ((isElement && reverse) || (!isElement && !reverse)) {
            await browser.takeScreenshot();
            throw new Error(`Expected Element to be ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${selectorToUse}`);
        }
    };

    expectGroupByToBeDisplayed: utils.AsyncObjectStringLookupStrategyBooleanReturnElement = async ({
        id,
        lookupStrategy,
        reverse = false,
    }) => {
        const generalMenu = await this.table.columns.openColumnMenu(id, lookupStrategy);
        const element = this.waitForDisplayedAndGetElement({
            selector: '.e-table-field-group-by',
            reverse,
            parent: generalMenu,
            timeout: this.valueCheckTimeout,
            timeoutMsg: selector =>
                `Expected Element to be ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${selector}.`,
        });
        return element;
    };

    expectInlineActionDisplayed: utils.AsyncObjectStringNumberBooleanReturnVoid = async ({
        id,
        rowNumber,
        reverse,
    }) => {
        const actionRow = await this.find(SELECTOR.rowIndexSelector(rowNumber));
        await actionRow.waitForExist({
            timeout: this.valueCheckTimeout,
            timeoutMsg: `Expected element could not be found: row "${rowNumber}".\nSelector: ${actionRow.selector.toString()}`,
        });

        const inlineActionButton = await this.find(
            `${SELECTOR.rowIndexSelector(rowNumber)} [aria-label="${id}"][data-testid~="e-table-inline-action"]`,
        );
        await inlineActionButton.waitForExist({
            timeout: this.valueCheckTimeout,
            timeoutMsg: `Expected element could not be found: "${id}".\nSelector: ${inlineActionButton.selector.toString()}`,
        });

        await inlineActionButton.scrollIntoView();
        await inlineActionButton.waitForDisplayed({
            reverse,
            timeout: this.valueCheckTimeout,
            timeoutMsg: `Expected element to be ${
                reverse ? 'hidden' : 'displayed'
            }.\nSelector: ${inlineActionButton.selector.toString()}`,
        });
    };

    expectNotToBeSelected: utils.AsyncNumberReturnVoid = async rowNumber => {
        const element = await this.find(`.ag-row${SELECTOR.rowIndexSelector(rowNumber)}`);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: row "${rowNumber}".\nSelector: ${element.selector.toString()})`,
        });

        try {
            await element.moveTo();
            await browser.waitUntil(
                async () => {
                    const classList = await element.getAttribute('class');
                    return classList.indexOf('ag-row-selected') === -1;
                },
                {
                    timeout: this.valueCheckTimeout,
                    timeoutMsg: `Expected element to be unselected.\nSelector: ${element.selector.toString()}`,
                },
            );
        } catch (error) {
            throw new Error(`Expected element to be unselected.\nSelector: ${element.selector.toString()}`);
        }
    };

    expectNumberOfErrorsToBe: utils.AsyncStringReturnVoid = async expectedErrorNb => {
        const errorButton = await this.find('.e-link-error-numbers button');
        await waitForPromises(500, 'Wait for find');

        const errorNb: string = (await errorButton.getText()).split(' ')[0];

        if (errorNb !== expectedErrorNb) {
            throw new Error(`Expected value: ${expectedErrorNb} actual: ${errorNb}`);
        }
    };

    expectNumberOfItemsSelectedOnBulkActionBar: utils.AsyncNumberReturnVoid = async expectedNumberOfItems => {
        const selector = '[data-testid~="e-table-field-bulk-actions-bar-selected-items"]';
        const messageContainerElement = await this.find(selector);
        await waitForPromises(500, 'bulk action bar selected items wait time');
        const messageContainerText = await messageContainerElement.getText();
        const numberOfItems = parseInt(messageContainerText.replace(/\D/g, ''), 10);
        if (numberOfItems !== Number(expectedNumberOfItems)) {
            throw new Error(
                `Expected value: ${expectedNumberOfItems}, actual value: ${numberOfItems}.\nSelector: ${messageContainerElement.selector.toString()}`,
            );
        }
    };

    expectOptionMenuValue: utils.AsyncStringReturnVoid = async value => {
        const fieldSelector = '.e-option-item-menu .e-field-select-input-text';
        const fieldElement = await this.find(fieldSelector);
        await browser.waitUntil(() => fieldElement.isDisplayed(), {
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected option menu to be displayed.\nSelector: ${fieldSelector}`,
        });
        await this.expectValue({ toBe: value, cssSelector: fieldSelector.toString(), ignoreContext: false });
    };

    expectPageButtonToBeEnabled = async (buttonName: utils.TableButton, reverse: boolean = false): Promise<void> => {
        const button = await this.find(`.ag-paging-button[data-ref="bt${buttonName}"]`);
        await browser.waitUntil(
            async () => {
                const disabled = await button.getAttribute('aria-disabled');
                return reverse ? disabled === 'true' : disabled === 'false';
            },
            {
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected element to be ${
                    reverse ? 'disabled' : 'enabled'
                }.\nSelector: ${button.selector.toString()}`,
            },
        );
    };

    expectPageNumberToBe: utils.AsyncStringReturnVoid = async expectedPageNumber => {
        const pageNumber = await this.find('.ag-paging-description');
        await utils.waitForElementToExist({ name: 'pageNumber', selector: pageNumber.selector.toString() });
        await pageNumber.scrollIntoView();
        const text = await pageNumber.getText();
        if (text !== expectedPageNumber) {
            throw new Error(
                `Expected value: ${expectedPageNumber}, actual: ${text}.\nSelector: ${pageNumber.selector.toString()}`,
            );
        }
    };

    expectProgressValue = async ({
        toBe,
        cssSelector,
        ignoreContext = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }): Promise<void> => {
        const selectorToUse = `${this.getSelectorForOperation(
            cssSelector,
            ignoreContext,
        )} [data-element="current-progress-label"]`;
        const trimmedToBe = toBe.replace('%', '').trim();

        const element = await browser.$(selectorToUse);
        await utils.waitForElementToExist({ name: 'progress bar', selector: selectorToUse });

        let value = '';
        let trimmedResult = '';
        try {
            await browser.waitUntil(
                async () => {
                    const result = (await element.getText()) as unknown as string[];
                    value = Array.isArray(result) ? result[0] : result;
                    trimmedResult = trimEnd(value, '%').trim();
                    return (trimmedResult || '') === (trimmedToBe || '');
                },
                {
                    timeout: this.valueCheckTimeout,
                    timeoutMsg: `Expected value: ${trimmedToBe}, actual: ${trimmedResult}.\nSelector: ${selectorToUse}`,
                },
            );
        } catch (error) {
            await utils.takeScreenshot();
            throw new Error(`Expected value: ${trimmedToBe}, actual: ${trimmedResult}.\nSelector: ${selectorToUse}`);
        }
    };

    expectSummaryRowToBe: utils.AsyncStringReturnVoid = async expectedSummaryRow => {
        const summaryRow = await this.find('.ag-paging-row-summary-panel');
        await utils.waitForElementToExist({ name: 'summaryRow', selector: summaryRow.selector.toString() });
        await summaryRow.scrollIntoView();
        const text = await summaryRow.getText();
        if (text !== expectedSummaryRow) {
            throw new Error(
                `Expected value: ${expectedSummaryRow}, actual: ${text}.\nSelector: ${summaryRow.selector.toString()}`,
            );
        }
    };

    expectTableError = async (value: number, reverse: boolean): Promise<void> => {
        const selectorToUse = await browser.$('.e-xtrem-tab-item span[data-element="error"]');
        const errorElement = await selectorToUse.isExisting();
        if ((errorElement === true && reverse === false) || (errorElement === false && reverse === true)) {
            throw new Error(
                `Expected table to contain "${value}" ${reverse === false ? 'error' : 'errors'}. \nSelector: ${selectorToUse.selector}`,
            );
        }
        if (errorElement && reverse === true) {
            const messages = await selectorToUse.getAttribute('aria-label');
            if (!messages.includes(value.toString())) {
                throw new Error(`Expected table to contain "${value}" errors. \nSelector: ${selectorToUse.selector}`);
            }
        }
    };

    expectTableToBeEmpty: utils.AsyncBooleanReturnVoid = async reverse => {
        const emptyContainer = await this.find('[data-testid~="e-no-rows-found-component"');
        await waitForPromises(500, 'empty container wait time');
        await emptyContainer.waitForDisplayed({
            reverse,
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element to be ${
                reverse ? 'not empty' : 'empty'
            }.\nSelector: ${this.cssSelector.toString()}`,
        });
    };

    expectTableTunnelLinkToBeDisplayed = async ({
        id,
        lookupStrategy,
        rowNumber,
        reverse = false,
    }: {
        id: string;
        lookupStrategy: utils.LookupStrategy;
        rowNumber: number;
        reverse: boolean;
    }): Promise<void> => {
        const tunnelLink = await this.find(
            `.e-nested-cell-${lookupStrategy === utils.LookupStrategy.BIND ? `bind-${id}` : `label-${camelCase(id)}`} [data-testid="fieldReadOnlyTable-${rowNumber - 1}"]`,
        );
        await tunnelLink.waitForExist();
        await utils.expectElementToBeDisplayed({ selector: tunnelLink.selector.toString(), reverse });
    };

    expectToBeSelected: utils.AsyncNumberReturnVoid = async rowNumber => {
        const element = await this.find(`.ag-row${SELECTOR.rowIndexSelector(rowNumber)}`);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: row "${rowNumber}".\nSelector: ${element.selector.toString()})`,
        });

        try {
            await element.moveTo();
            await browser.waitUntil(
                async () => {
                    const classList = await element.getAttribute('class');
                    return classList.indexOf('ag-row-selected') > 0;
                },
                {
                    timeout: this.valueCheckTimeout,
                    timeoutMsg: `Expected element to be selected.\nSelector: ${element.selector.toString()}`,
                },
            );
        } catch (error) {
            throw new Error(`Expected element to be selected.\nSelector: ${element.selector.toString()}`);
        }
    };

    expectTooltipDisplayedError: utils.AsyncStringBooleanReturnVoid = async (errorText, reverse) => {
        const selectorToUse = '.carbon-portal [data-element="tooltip"] span[class="e-ui-table-tooltip-content"]';
        const tooltip = await this.find(selectorToUse);
        await tooltip.waitForExist({
            timeout: this.valueCheckTimeout,
            reverse,
            timeoutMsg: `Expected element could not be found:value.\nSelector: ${tooltip.selector.toString()}`,
        });
        const tooltipEntrance = await browser.$(selectorToUse).isExisting();

        if ((reverse === false && tooltipEntrance) || (reverse === true && !tooltipEntrance)) {
            throw new Error(
                `Expected Element to be ${reverse === false ? 'hidden' : 'displayed'}.\nSelector: ${selectorToUse}`,
            );
        }

        const tooltipContent = await browser.$(`${selectorToUse} .error-list--inside-tooltip`).getText();
        const regex = /[^A-Z0-9]/gi;
        const expectedErrorText = errorText.replace(regex, '');
        const tooltipText = tooltipContent.replace(regex, '');
        if (!tooltipText.includes(expectedErrorText)) {
            throw new Error(`Expected value: "${errorText}", actual: "${tooltipContent}"\nSelector: ${selectorToUse}`);
        }
    };

    expectTooltipValue: utils.AsyncStringReturnVoid = async value => {
        for (let retry = 0; retry <= 5; retry += 1) {
            try {
                const tooltipEntrance = await this.find(
                    '[data-testid~="e-table-inline-action"] [data-portal-entrance]',
                    true,
                );
                await tooltipEntrance.waitForExist({
                    timeout: this.valueCheckTimeout,
                    timeoutMsg: `Expected element could not be found:value.\nSelector: ${tooltipEntrance.selector.toString()}`,
                });
                const tooltipId = await tooltipEntrance.getAttribute('data-portal-entrance');
                const tooltip = await $(`[data-portal-exit="${tooltipId}"]`);
                await tooltip.waitForExist({
                    timeout: this.valueCheckTimeout,
                });
                await tooltip.waitForDisplayed({
                    timeout: this.valueCheckTimeout,
                });
                const tooltipContent = await tooltip.getText();
                if (tooltipContent !== value) {
                    throw new Error(
                        `Expected value: "${value}", actual: "${tooltipContent}".\nSelector: ${tooltip.selector.toString()}`,
                    );
                }
                break;
            } catch (error) {
                if (retry >= 5) throw new Error(error);
                console.log(`retry n ${retry}`);
            }
        }
    };

    expectUnGroupToBeDisplayed: utils.AsyncObjectStringLookupStrategyBooleanReturnElement = async ({
        id,
        lookupStrategy,
        reverse = false,
    }) => {
        const generalMenu = await this.table.columns.openColumnMenu(id, lookupStrategy);
        return this.waitForDisplayedAndGetElement({
            selector: '.e-table-field-ungroup',
            reverse,
            parent: generalMenu,
            timeout: this.valueCheckTimeout,
        });
    };

    expectValidationIcon: utils.AsyncStringReturnVoid = async value => {
        const tooltipEntrance = await this.find('.e-table-field-validation-summary [data-portal-entrance]');
        await tooltipEntrance.waitForExist({
            timeout: this.valueCheckTimeout,
            timeoutMsg: `Expected element could not be found:value.\nSelector: ${tooltipEntrance.selector.toString()}`,
        });
        const tooltipId = await tooltipEntrance.getAttribute('data-portal-entrance');
        const tooltip = await browser.$(`[data-portal-exit="${tooltipId}"]`);
        const tooltipContent = await tooltip.getText();
        if (tooltipContent !== value) {
            throw new Error(
                `Expected value: "${value}", actual: "${tooltipContent}".\nSelector: ${tooltip.selector.toString()}`,
            );
        }
    };

    expectViewMode = async (expectedView: utils.ViewMode): Promise<void> => {
        const expectedClassName = expectedView === 'compact' ? 'e-compact-table' : 'e-comfortable-table';
        const selectorToUse = this.getSelectorForOperation(undefined, true);

        try {
            await browser.waitUntil(
                async () => {
                    const hasClassName: boolean = await browser.execute(
                        `return document.querySelector('${selectorToUse}').parentNode.classList.contains('${expectedClassName}');`,
                    );
                    return hasClassName;
                },
                {
                    timeout: this.timeoutWaitFor,
                    timeoutMsg: `Element expected to have className '${expectedClassName}' in ${this.timeoutWaitFor} ms: (selector: ${selectorToUse})`,
                },
            );
        } catch (error) {
            throw new Error(
                `Element expected to have className '${expectedClassName}' in ${this.timeoutWaitFor} ms: (selector: ${selectorToUse})`,
            );
        }
    };

    getToastsMessages = async (selector: string): Promise<string[]> => {
        const toasts = await browser.$$(selector);
        const messages = await Promise.all(
            toasts.map(async toast => {
                await toast.waitForExist();
                return toast.getText();
            }),
        );
        return messages;
    };

    waitForOptionMenuToBeDisplayed = async () => {
        const optionMenu = await this.find('.e-option-item-menu');
        await utils.waitForElementToBeDisplayed({
            name: 'option menu',
            selector: optionMenu.selector.toString(),
            reverse: false,
        });
    };

    waitForTableStopLoading = async () => {
        const loader = await this.find('[data-component="loader"]');
        await utils.waitForElementNotToExist('table loader', loader.selector.toString());
    };
}
