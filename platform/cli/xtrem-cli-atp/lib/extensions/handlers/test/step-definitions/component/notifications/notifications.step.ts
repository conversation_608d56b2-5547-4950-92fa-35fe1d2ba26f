import { Then } from '@cucumber/cucumber';
import * as StaticStore from '../static-store';
import { NotificationsCenterCard } from './notifications-center-card-object';
import { NotificationsCenter } from './notifications-center-object';

Then(
    /^the notification with title "(.*)" in the notification center is (displayed|hidden)$/,
    async (value: string, cssState: 'displayed' | 'hidden') => {
        const notificationsCenterCard = new NotificationsCenterCard();
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        await notificationsCenterCard.expectNotificationTitleDisplayed(storeValue, cssState === 'hidden');
    },
);

Then('the user clicks the notifications icon in the actions header', async () => {
    const notificationsCenter = new NotificationsCenter();
    await notificationsCenter.clickNotificationsIcon();
});

Then(/^the user selects the notification card with title "(.*)"$/, async (value: string) => {
    const notificationsCenterCard = new NotificationsCenterCard();
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
    await notificationsCenterCard.selectNotification(storeValue);
});

Then(/^the title of the notification card is "(.*)"$/, async (value: string) => {
    const notificationsCenterCard = new NotificationsCenterCard();
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
    await notificationsCenterCard.expectValue({
        toBe: storeValue,
        selectorToUse: '[data-testid="xe-notification-title"]',
    });
});

Then(/^the relative date of the notification card is "(.*)"$/, async (dateValue: string) => {
    const notificationsCenterCard = new NotificationsCenterCard();
    await notificationsCenterCard.expectValue({
        toBe: dateValue,
        selectorToUse: '[data-testid="xe-notification-time-ago"]',
    });
});

Then(/^the description of the notification card is "(.*)"$/, async (value: string) => {
    const notificationsCenterCard = new NotificationsCenterCard();
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
    await notificationsCenterCard.expectValue({
        toBe: storeValue,
        selectorToUse: '[data-testid="xe-notification-description"]',
    });
});

Then(/^the user clicks the "(.*)" notification card header action$/, async (actionValue: string) => {
    const notificationsCenterCard = new NotificationsCenterCard();
    await notificationsCenterCard.clickAction(actionValue, 'header');
});

Then(/^the user clicks the "(.*)" notification card body action$/, async (actionValue: string) => {
    const notificationsCenterCard = new NotificationsCenterCard();
    await notificationsCenterCard.clickAction(actionValue, 'body');
});
