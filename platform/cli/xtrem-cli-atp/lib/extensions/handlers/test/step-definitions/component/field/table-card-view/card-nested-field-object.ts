/* eslint-disable @typescript-eslint/naming-convention */
import { getLookupStrategySelector, LookupStrategy, NestedFieldTypes } from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import { CardObject } from './card-object';

export interface ICardNestedFieldObject {
    parent: CardObject;
    identifier: string;
    lookupStrategy: LookupStrategy;
    fieldType: NestedFieldTypes;
}

export class CardNestedFieldObject extends AbstractPageObject {
    constructor({ parent, identifier, lookupStrategy, fieldType }: ICardNestedFieldObject) {
        super(`${parent.cssSelector} ${getLookupStrategySelector({ fieldType, lookupStrategy, identifier })}`);
    }
}
