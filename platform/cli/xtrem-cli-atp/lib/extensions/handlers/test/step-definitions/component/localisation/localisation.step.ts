import { When } from '@cucumber/cucumber';
import { getDataTestIdSelector } from '../../step-definitions-utils';
import { waitMillis } from '../field/wait-util';
import { testGlobals } from '../test-globals';

When(/^the user switches language to "(.*)"$/, async (language: string) => {
    const select = await browser.$(
        `${getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'consumer-locale-selection' })} span[data-element="select-text"]`,
    );
    await select.waitForDisplayed();
    await select.waitForClickable();
    await select.click();
    await waitMillis(500, 'before language switch');
    const options = await browser.$$('div[data-element="select-list-wrapper"] li[role="option"]');

    for (let i = 0; i < options.length; i += 1) {
        // eslint-disable-next-line no-await-in-loop
        const text = await options[i].getText();
        if (text === language) {
            testGlobals.language = language;
            // eslint-disable-next-line no-await-in-loop
            await options[i].click();
            return;
        }
    }

    await waitMillis(500, 'after language switch');
});
