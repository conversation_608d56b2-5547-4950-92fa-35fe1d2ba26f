import { Then } from '@cucumber/cucumber';
import pageModel from '../main-page';

Then(
    /^the user clicks the "([$A-Za-z0-9\s]*)" labelled more actions button in the sidebar header$/,
    async (actionName: string) => {
        await pageModel.waitForFinishLoading();
        await pageModel.sidebar.dropDownMenu.selectMenuAction(actionName, '.e-sidebar-dropdown-action-container');
    },
);

Then(
    /^the "([$A-Za-z0-9\s]*)" titled mobile sidebar is (displayed|hidden)$/,
    async (title: string, cssState: 'displayed' | 'hidden') => {
        await pageModel.waitForFinishLoading();
        await pageModel.mobileSidebar.expectMobileSidebarTitle(title, cssState === 'hidden');
    },
);

Then(
    /^the user clicks the "([$A-Za-z0-9\s]*)" labelled action button in the sidebar header$/,
    async (actionName: string) => {
        await pageModel.waitForFinishLoading();
        await pageModel.sidebar.clickActionButton(actionName);
    },
);
