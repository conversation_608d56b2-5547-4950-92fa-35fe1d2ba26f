import { Then, When } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { RadioFieldObject } from './radio-field-object';

// ----------
// Static store field steps
// ----------
When(/^the user selects the value "(.*)" in the radio field$/, async (value: string) => {
    const radioField = <RadioFieldObject>StaticStore.getStoredField(fieldTypes.radio);
    await radioField.selectValue(value);
});

When(/^the user selects the label "(.*)" in the radio field$/, async (label: string) => {
    const radioField = <RadioFieldObject>StaticStore.getStoredField(fieldTypes.radio);
    await radioField.selectValueByLabel(label);
});

Then(
    /^the value "(.*)" of the radio field is (selected|not selected)$/,
    async (value: string, state: 'selected' | 'not selected') => {
        const radioField = <RadioFieldObject>StaticStore.getStoredField(fieldTypes.radio);
        await radioField.expectSelectedValue(value, state === 'not selected');
    },
);

Then(/^the radio field is (enabled|disabled)$/, async (cssState: 'enabled' | 'disabled') => {
    const radioField = <RadioFieldObject>StaticStore.getStoredField(fieldTypes.radio);
    await radioField.loseFocus();
    await radioField.expectRadioFieldToBeEnabled(cssState === 'disabled');
});

Then(/^the radio field is read-only$/, async () => {
    const radioField = <RadioFieldObject>StaticStore.getStoredField(fieldTypes.radio);
    await radioField.loseFocus();
    await radioField.expectToBeReadOnly();
});
