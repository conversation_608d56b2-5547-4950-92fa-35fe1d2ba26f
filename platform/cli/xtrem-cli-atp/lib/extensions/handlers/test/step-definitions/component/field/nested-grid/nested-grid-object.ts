/* eslint-disable no-restricted-syntax */
/* eslint-disable class-methods-use-this */
import { camelCase } from 'lodash';
import * as utils from '../../../step-definitions-utils';
import { FilterObject } from '../../filter-object';
import * as StaticStore from '../../static-store';
import { isMobileDevice, testGlobals } from '../../test-globals';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';
import { getMobileRowAttributes, getRowAttributes, SCROLL_STEP, scrollToLeft } from './nested-grid-utils';

type RectReturn = {
    x: number;
    y: number;
    width: number;
    height: number;
};

export const SELECTORS = {
    cell: '.ag-center-cols-container .ag-cell',
    row: '.ag-center-cols-container .ag-row.ag-row-group',
    scroll: '.ag-body-horizontal-scroll-viewport',
    scrollContainer: '.ag-body-horizontal-scroll-container',
};

export class NestedGridObject extends FieldObject {
    public filter: FilterObject;

    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        super({ fieldType: 'nestedGrid', identifier, lookupStrategy, context });
        this.filter = new FilterObject(this);
    }

    override expectTitleToBeDisplayed = async (reverse = false) => {
        const selectorToUse = isMobileDevice()
            ? `[data-testid~="e-mobile-nested-grid-header-title"]`
            : this.labelSelector;
        await this.expectToBeDisplayed(`${this.cssSelector} ${selectorToUse}`, reverse);
    };

    override expectTitle = async (expectedTitle: string) => {
        const selectorToUse = isMobileDevice()
            ? `[data-testid~="e-mobile-nested-grid-header-title"]`
            : this.labelSelector;
        await this.expectTextContent({ toBe: expectedTitle, ignoreCase: false, cssSelector: selectorToUse });
    };

    expectMobileTitle = async (headerType: string, expectedTitle: string) => {
        let selectorToUse = '';
        if (headerType === 'value') {
            selectorToUse = utils.getDataTestIdSelector({
                domSelector: 'span',
                dataTestIdValue: 'e-mobile-nested-grid-header-child-title',
            });
        } else {
            selectorToUse = utils.getDataTestIdSelector({
                domSelector: 'span',
                dataTestIdValue: 'e-mobile-grid-header-level',
            });
        }
        await super.expectTextContent({ toBe: expectedTitle, ignoreCase: false, cssSelector: selectorToUse });
    };

    storeCell = async (cell: WebdriverIO.Element) => {
        const parentElt = await cell.parentElement();
        await this.storeRow(parentElt);
    };

    storeRow = async (row: WebdriverIO.Element) => {
        await row.scrollIntoView();
        const rowId = await row.getAttribute('row-id');
        const rowIndex = await row.getAttribute('row-index');
        const ariaRowIndex = await row.getAttribute('aria-rowindex');
        const parentclass = await row.getAttribute('class');
        const startIndex = parentclass.search('nested-row-level-');
        const endIndex = parentclass.indexOf(' ', startIndex);
        const parentID = parentclass.substring(startIndex, endIndex);

        StaticStore.storeObject(StaticStore.StoredKeys.NESTED_GRID_ROW_ATTRIBUTES, {
            rowId,
            rowIndex,
            ariaRowIndex,
        });

        StaticStore.storeObject(StaticStore.StoredKeys.NESTED_GRID_ROW_PARENT, parentID);
    };

    expectNestedGridToBeEmpty = async ({
        level,
        parentId,
        state,
    }: {
        level: number;
        parentId?: number;
        state: 'empty' | 'not empty';
    }) => {
        const emptyContainerSelector = this.getCssSelector(
            'css selector',
            parentId !== undefined && level > 1
                ? `[aria-grid="nested-grid-${level - 1}-${parentId}"] [data-testid~="e-no-rows-found-component"]`
                : '[grid-id] [data-testid~="e-no-rows-found-component"]',
        );
        const emptyContainer = await this.find(emptyContainerSelector);
        await waitForPromises(500, 'empty container wait time');
        await emptyContainer.waitForDisplayed({
            reverse: state === 'not empty',
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element to be ${state}.\nSelector: ${emptyContainerSelector}`,
        });
    };

    scrollToRowByText = async (text: string, columnHeader: string, scrollValue: number = 500) => {
        const columnClass = `e-nested-cell-label-${camelCase(columnHeader)}`;
        let rowElement: WebdriverIO.Element | null = null;

        let scrollAttempts = 0;
        const maxScrollAttempts = 20;

        while (!rowElement && scrollAttempts < maxScrollAttempts) {
            const rows = await this.findAll(SELECTORS.row);

            for (const row of rows) {
                const cell = await row.$(`.${columnClass}`);
                if (await cell.isExisting()) {
                    const cellText = await cell.getText();
                    if (cellText.trim() === text.trim()) {
                        rowElement = row;
                        break;
                    }
                }
            }

            if (!rowElement) {
                const selectorToUse = '.ag-body-viewport';
                await browser.execute(
                    `var element = document.querySelector('${selectorToUse}');
                        if (element){
                            element.scrollBy(0, '${scrollValue}');
                        }`,
                );
                await waitForPromises(300, 'Scroll down to load more rows');
            }

            scrollAttempts += 1;
        }

        if (!rowElement) {
            throw new Error(
                `Row with text "${text}" in column "${columnHeader}" does not exist or could not be loaded.`,
            );
        }

        await rowElement.scrollIntoView();
        await waitForPromises(500, 'Scroll to row');
    };

    searchAndScrollToCell = async ({
        expectedColumnHeader,
        storeValue,
        select,
    }: {
        expectedColumnHeader: string;
        storeValue: string;
        select: boolean;
    }): Promise<boolean> => {
        const scrollBarContainer = await $(SELECTORS.scrollContainer);
        const scrollWidth = await this.getScrollWidth(scrollBarContainer);

        await scrollToLeft(SELECTORS.scroll, 0);
        await waitForPromises(200, 'Scroll start');
        for (let index = 0; index <= scrollWidth; index += SCROLL_STEP) {
            const cells = await this.findAll(SELECTORS.cell);
            const headerSelector = `.e-nested-cell-label-${camelCase(expectedColumnHeader)}`;
            const header = await $(headerSelector);
            if (await header.isExisting()) {
                const scrollRect = await browser.getElementRect(scrollBarContainer.elementId);
                if (await this.isCellMatching(cells, expectedColumnHeader, storeValue)) {
                    if (select) {
                        await this.scrollToCell(scrollRect, cells, expectedColumnHeader, storeValue);
                    }
                    return true;
                }
            }
            await scrollToLeft(SELECTORS.scroll, index);
            await waitForPromises(500, 'Wait post scroll');
        }
        return false;
    };

    searchRowWithText = async ({
        expectedText,
        expectedColumnHeader,
        select = false,
        expectedDisplayed = true,
    }: {
        expectedText: string;
        expectedColumnHeader: string;
        select: boolean;
        expectedDisplayed?: boolean;
    }): Promise<boolean> => {
        if (isMobileDevice()) {
            const selectorToUse = `[data-label="${camelCase(expectedText)}"][data-testid~="e-card"]`;
            StaticStore.storeObject<string>(StaticStore.StoredKeys.MOBILE_NESTED_GRID_ROW_SELECTOR, selectorToUse);
            return true;
        }

        await (await $(SELECTORS.cell)).waitForExist({ timeout: this.timeoutWaitFor, timeoutMsg: 'No cell found' });
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedText);
        await scrollToLeft(SELECTORS.scroll, 0);
        await waitForPromises(200, 'Scroll start');

        const foundOrSelected = await this.searchAndScrollToCell({
            expectedColumnHeader,
            storeValue,
            select,
        });
        if (foundOrSelected === expectedDisplayed) {
            return true;
        }

        const errorMessage = expectedDisplayed
            ? 'Element was not found, expected to be found'
            : 'Element was found, expected not to be found';
        throw new Error(
            `${errorMessage}: nested grid row with text "${storeValue}" at column "${expectedColumnHeader}".\nSelector: ${this.cssSelector}`,
        );
    };

    private getScrollWidth = async (scrollBarContainer: WebdriverIO.Element) => {
        if (await scrollBarContainer.isExisting()) {
            return (await browser.getElementRect(scrollBarContainer.elementId)).width;
        }
        return 0;
    };

    private isCellMatching = async (cells: WebdriverIO.Element[], expectedColumnHeader: string, storeValue: string) => {
        for (const cell of cells) {
            if (await cell.isExisting()) {
                const classList = await cell.getAttribute('class');
                const text = await cell.getText();
                const classListArray = classList.split(' ');
                if (
                    classListArray.includes(`e-nested-cell-label-${camelCase(expectedColumnHeader)}`) &&
                    text === storeValue.replace(/\s\s+/g, ' ') // Remove extra spaces XT-74618
                ) {
                    return true;
                }
            }
        }
        return false;
    };

    private scrollToCell = async (
        scrollRect: RectReturn,
        cells: WebdriverIO.Element[],
        expectedColumnHeader: string,
        storeValue: string,
    ) => {
        for (const cell of cells) {
            if (await cell.isExisting()) {
                const classList = await cell.getAttribute('class');
                const text = await cell.getText();
                const classListArray = classList.split(' ');
                if (
                    classListArray.includes(`e-nested-cell-label-${camelCase(expectedColumnHeader)}`) &&
                    text === storeValue.replace(/\s\s+/g, ' ') // Remove extra spaces XT-74618
                ) {
                    const cellRect = await browser.getElementRect(cell.elementId);
                    await this.storeCell(cell);
                    await waitForPromises(200, 'Display field');
                    await scrollToLeft(SELECTORS.scroll, -scrollRect.x + cellRect.x);
                    await waitForPromises(500, 'Wait post scroll');
                    return;
                }
            }
        }
    };

    selectRowWithDataTable = async (table: any): Promise<boolean> => {
        const expectedCells = await table.hashes();

        await (await $(SELECTORS.row)).waitForExist({ timeout: this.timeoutWaitFor, timeoutMsg: 'No row found' });
        const scrollBarContainer = await $(SELECTORS.scrollContainer);

        let scrollWidth = 0;

        if (await scrollBarContainer.isExisting()) {
            scrollWidth = (await browser.getElementRect(scrollBarContainer.elementId)).width;
        }
        await scrollToLeft(SELECTORS.scroll, 0);

        for (let index = 0; index <= scrollWidth; index += SCROLL_STEP) {
            const rows = await this.findAll(SELECTORS.row);

            for (const row of rows) {
                const cells = await row.$$(SELECTORS.cell);
                const matchedCells = await this.getMatchedCells(cells, expectedCells);

                if (JSON.stringify(matchedCells) === JSON.stringify(expectedCells)) {
                    await this.storeRow(row);
                    await scrollToLeft(
                        SELECTORS.scroll,
                        -(await browser.getElementRect(scrollBarContainer.elementId)).x + index,
                    );
                    await waitForPromises(500, 'Display field');
                    return true;
                }
            }
            await scrollToLeft(SELECTORS.scroll, index);
        }
        throw new Error('The row you selected is not visible or does not exist.');
    };

    private getMatchedCells = async (cells: WebdriverIO.Element[], expectedCells: any[]): Promise<any[]> => {
        const matchedCells = [];
        for (const cell of cells) {
            if (await cell.isExisting()) {
                const { classListArray, text } = await this.getCellAttributes(cell);
                for (const expectedCell of expectedCells) {
                    const { columnHeader: expectedColumnHeader, cellText: expectedText } = expectedCell;
                    if (this.isCellMatchingAttributes(classListArray, expectedColumnHeader, text, expectedText)) {
                        matchedCells.push({ columnHeader: expectedColumnHeader, cellText: expectedText });
                    }
                }
            }
        }
        return matchedCells;
    };

    private getCellAttributes = async (cell: WebdriverIO.Element) => {
        const classList = await cell.getAttribute('class');
        const text = await cell.getText();
        const classListArray = classList.split(' ');
        return { classListArray, text };
    };

    private isCellMatchingAttributes = (
        classListArray: string[],
        expectedColumnHeader: string,
        text: string,
        expectedText: string,
    ): boolean => {
        const isMatching =
            classListArray.includes(`e-nested-cell-label-${camelCase(expectedColumnHeader)}`) &&
            text.trim() === expectedText.trim();
        return isMatching;
    };

    checkSelectRowWithText = async (toggleState: string) => {
        const toggle = this.getToggleState(toggleState);
        const inputSelector = this.getInputSelector(toggle);

        const row = await this.findRow();
        const classesArray = await this.getRowClasses(row);

        if (this.isRowAlreadyInDesiredState(toggle, classesArray)) {
            return;
        }

        await this.toggleCheckbox(inputSelector);
    };

    private getToggleState = (toggleState: string): utils.CheckedOrUnchecked => {
        return toggleState === 'selects' || toggleState === 'ticks'
            ? utils.CheckedOrUnchecked.CHECKED
            : utils.CheckedOrUnchecked.UNCHECKED;
    };

    private getInputSelector = (toggle: utils.CheckedOrUnchecked): string => {
        return `.ag-checkbox-input-wrapper${toggle === utils.CheckedOrUnchecked.UNCHECKED ? '.ag-checked' : ' '} input`;
    };

    private findRow = (): Promise<WebdriverIO.Element> => {
        return this.find(getRowAttributes(), true);
    };

    private getRowClasses = async (row: WebdriverIO.Element): Promise<string[]> => {
        const classesString = await row.getAttribute('class');
        return classesString.split(' ');
    };

    private isRowAlreadyInDesiredState = (toggle: utils.CheckedOrUnchecked, classesArray: string[]): boolean => {
        return (
            (toggle === utils.CheckedOrUnchecked.CHECKED && classesArray.includes('ag-row-selected')) ||
            (toggle === utils.CheckedOrUnchecked.UNCHECKED && !classesArray.includes('ag-row-selected'))
        );
    };

    private toggleCheckbox = async (inputSelector: string) => {
        const checkBoxElt = await this.find(`${getRowAttributes()} ${inputSelector}`, false);
        await checkBoxElt.scrollIntoView();
        await browser.execute(elem => elem.click(), checkBoxElt);
        await waitForPromises(500, 'option click');
    };

    expandSelectRowWithText = async (toggle: utils.ExpandedOrCollapsed) => {
        if (isMobileDevice()) {
            await this.handleMobileToggle(toggle);
            return;
        }

        const iconSelector = this.getIconSelector(toggle);
        const row = await this.getRowElement();
        await this.ensureRowIsDisplayed(row);

        const classesArray = await this.getRowClasses(row);
        this.checkRowState(toggle, classesArray);

        await this.resetScroll();
        await this.clickIcon(row, iconSelector);
        await waitForPromises(500, 'expand and collapse element');
    };

    private getIconSelector = (toggle: utils.ExpandedOrCollapsed): string => {
        return `.ag-group-${toggle === utils.ExpandedOrCollapsed.EXPANDED ? 'contracted' : 'expanded'} .ag-icon`;
    };

    private getRowElement = (): Promise<WebdriverIO.Element> => {
        const parentID = StaticStore.getStoredObject(StaticStore.StoredKeys.NESTED_GRID_ROW_PARENT);
        return this.find(`.ag-center-cols-container .${parentID}`);
    };

    private ensureRowIsDisplayed = async (row: WebdriverIO.Element) => {
        await utils.waitForElementToExist({ name: 'row', selector: row.selector.toString() });
        await utils.waitForElementToBeDisplayed({ name: 'row', selector: row.selector.toString() });
    };

    private checkRowState = (toggle: utils.ExpandedOrCollapsed, classesArray: string[]) => {
        if (toggle === utils.ExpandedOrCollapsed.EXPANDED && classesArray.includes('ag-row-group-expanded')) {
            throw new Error('The row is already expanded.');
        } else if (toggle === utils.ExpandedOrCollapsed.COLLAPSED && classesArray.includes('ag-row-group-contracted')) {
            throw new Error('The row is already collapsed.');
        }
    };

    private handleMobileToggle = async (toggle: utils.ExpandedOrCollapsed) => {
        const selectorToUse = getMobileRowAttributes();
        if (toggle === utils.ExpandedOrCollapsed.EXPANDED) {
            await utils.waitForElementToExist({ name: 'row', selector: selectorToUse });
            await utils.waitForElementToBeDisplayed({ name: 'row', selector: selectorToUse });
            await this.click(selectorToUse);
        } else if (toggle === utils.ExpandedOrCollapsed.COLLAPSED) {
            const parentID: string = getMobileRowAttributes();
            const parentRow = parentID.match(/\d+/g)?.toString();
            const headerChildElement = await this.find('span[data-testid="e-mobile-nested-grid-header-child-title"]');
            const headerChildTitle = await headerChildElement.getText();

            if (parentRow === headerChildTitle) {
                const backButton = await this.find('button[data-testid="e-mobile-nested-grid-header-back-button"]');
                await utils.waitForElementToExist({ name: 'back button', selector: backButton.selector.toString() });
                await utils.waitForElementToBeDisplayed({
                    name: 'back button',
                    selector: backButton.selector.toString(),
                });
                await backButton.click();
            } else {
                throw new Error('Parent row does not match header title.');
            }
        }
    };

    private resetScroll = async () => {
        await browser.execute(
            `var element = document.querySelector('.ag-center-cols-viewport');
            if (element) {
                element.scrollLeft = 0;
            }`,
        );
    };

    private clickIcon = async (row: WebdriverIO.Element, iconSelector: string) => {
        const $target = $(`${row.selector} ${iconSelector}`);
        await $target.moveTo({ xOffset: 0, yOffset: 0 });
        await $target.waitForClickable({ timeout: this.valueCheckTimeout });
        await browser.execute(`document.querySelector('${row.selector} ${iconSelector}').click()`);
    };

    clickOnSelectedRow = async () => {
        const selectedRowCell = await this.find(`${getRowAttributes()} > .ag-cell-value`);
        await selectedRowCell.click();
        await waitForPromises(500, 'option click');
    };

    validateRowSelection = async (expectedCheckBoxChecked: boolean) => {
        const selectorToUse = `${getRowAttributes()} input`;
        const checkBox = await this.find(selectorToUse, true);
        const isSelected = await checkBox.isSelected();

        if (isSelected !== expectedCheckBoxChecked) {
            const expectedState = expectedCheckBoxChecked ? 'checked' : 'unchecked';
            throw new Error(
                `Expected row main checkbox to be ${expectedState}.\nSelector: ${this.cssSelector} ${selectorToUse}`,
            );
        }
    };

    selectFloatingRow = ({ level, parentId }: { level: number; parentId?: number }) => {
        const phantomRowSelector = this.getCssSelector(
            'css selector',
            parentId !== undefined && level > 1 ? `[aria-grid="nested-grid-${level - 1}-${parentId}"]` : '[grid-id]',
        );
        StaticStore.storeObject(StaticStore.StoredKeys.NESTED_GRID_PHANTOM_ROW_SELECTOR, phantomRowSelector);
    };

    stepToPage = async ({ level, parentId, page }: { level: number; parentId?: number; page: 'next' | 'previous' }) => {
        const button = await this.find(
            `${utils.getPagingSelector(level, parentId)} .ag-paging-button .ag-icon-${page}`,
        );
        await utils.waitForElementToExist({ name: 'button', selector: button.selector.toString() });
        await button.scrollIntoView();
        await button.moveTo();
        await button.waitForClickable();
        await button.click();
        await waitForPromises(500, 'option click');
        await this.waitForTableStopLoading();
    };

    expectPageButtonToBeEnabled = async (buttonName: utils.TableButton, reverse = false) => {
        const button = await this.find(`.ag-paging-button[data-ref="bt${buttonName}"]`);
        await browser.waitUntil(
            async () => {
                const disabled = await button.getAttribute('aria-disabled');
                return reverse ? disabled === 'true' : disabled === 'false';
            },
            {
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected element to be ${
                    reverse ? 'disabled' : 'enabled'
                }.\nSelector: ${button.selector.toString()}`,
            },
        );
    };

    expectPageButtonToBeEnabledAtLevel = async (
        { level, parentId, buttonName }: { level: number; parentId?: number; buttonName: utils.TableButton },
        reverse = false,
    ) => {
        const button = await this.find(
            `${utils.getPagingSelector(level, parentId)} .ag-paging-button[data-ref="bt${buttonName}"]`,
        );
        await button.waitForExist({
            timeout: this.valueCheckTimeout,
            timeoutMsg: `Expected level ${level} and Id ${parentId} could not be found.\nSelector: ${button.selector.toString()}`,
        });

        await browser.waitUntil(
            async () => {
                const disabled = await button.getAttribute('aria-disabled');
                return reverse ? disabled === 'true' : disabled === 'false';
            },
            {
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected element to be ${
                    reverse ? 'disabled' : 'enabled'
                }.\nSelector: ${button.selector.toString()}`,
            },
        );
    };

    expectPageNumberToBe = async (expectedPageNumber: string) => {
        const pageNumber = await this.find('.ag-paging-description');
        await utils.waitForElementToExist({ name: 'pageNumber', selector: pageNumber.selector.toString() });
        await pageNumber.scrollIntoView();
        const text = await pageNumber.getText();
        if (text !== expectedPageNumber) {
            throw new Error(
                `Expected value: ${expectedPageNumber}, actual: ${text}.\nSelector: ${pageNumber.selector.toString()}`,
            );
        }
    };

    expectPageNumberToBeAtLevel = async ({
        level,
        parentId,
        expectedPageNumber,
    }: {
        level: number;
        parentId?: number;
        expectedPageNumber: string;
    }) => {
        const pageNumber = await this.find(`${utils.getPagingSelector(level, parentId)} .ag-paging-description`);
        await pageNumber.waitForExist({
            timeout: this.valueCheckTimeout,
            timeoutMsg: `Expected level ${level} and Id ${parentId} could not be found.\nSelector: ${pageNumber.selector.toString()}`,
        });
        await pageNumber.scrollIntoView();
        const text = await pageNumber.getText();
        if (text !== expectedPageNumber) {
            throw new Error(
                `Expected value: ${expectedPageNumber}, actual: ${text}.\nSelector: ${pageNumber.selector.toString()}`,
            );
        }
    };

    expectSummaryRowToBe = async (expectedSummaryRow: string) => {
        const summaryRow = await this.find('.ag-paging-row-summary-panel');
        await utils.waitForElementToExist({ name: 'summaryRow', selector: summaryRow.selector.toString() });
        await summaryRow.scrollIntoView();
        const text = await summaryRow.getText();
        if (text !== expectedSummaryRow) {
            throw new Error(
                `Expected value: ${expectedSummaryRow}, actual: ${text}.\nSelector: ${summaryRow.selector.toString()}`,
            );
        }
    };

    expectSummaryRowToBeAtLevel = async ({
        level,
        parentId,
        expectedSummaryRow,
    }: {
        level: number;
        parentId?: number;
        expectedSummaryRow: string;
    }) => {
        const summaryRow = await this.find(`${utils.getPagingSelector(level, parentId)} .ag-paging-row-summary-panel`);
        await summaryRow.waitForExist({
            timeout: this.valueCheckTimeout,
            timeoutMsg: `Expected level ${level} and Id ${parentId} could not be found.\nSelector: ${summaryRow.selector.toString()}`,
        });
        await summaryRow.scrollIntoView();
        const text = await summaryRow.getText();
        if (text !== expectedSummaryRow) {
            throw new Error(
                `Expected value: ${expectedSummaryRow}, actual: ${text}.\nSelector: ${summaryRow.selector.toString()}`,
            );
        }
    };

    clickAddRowButton = async () => {
        let selectorToUse;
        if (isMobileDevice()) {
            selectorToUse = utils.getDataComponentSelector('button', 'button');
        } else {
            selectorToUse = utils.getDataTestIdSelector({
                domSelector: 'button',
                dataTestIdValue: 'e-table-button-add-new-row-phantom',
            });
        }
        const addNewRowPhantomButton = await this.find(selectorToUse);
        await utils.waitForElementToExist({
            name: 'addPhantomRowButton',
            selector: addNewRowPhantomButton.selector.toString(),
        });
        await addNewRowPhantomButton.click();
        await waitForPromises(300, 'Waiting for add new row click');
    };

    selectAction = async (actionName: string) => {
        const container = await this.getActionContainer();
        await utils.waitForElementToExist({ name: 'container', selector: container.selector.toString() });
        await utils.waitForElementToBeDisplayed({ name: 'container', selector: container.selector.toString() });
        await this.scrollTo({ selector: container.selector.toString() });

        const singleAction = await container.$('button[data-testid="e-popover-action-single-button"]');
        if (await singleAction.isExisting()) {
            await this.handleSingleAction(singleAction, actionName);
            return;
        }

        await this.handleMoreActions(container, actionName);
    };

    private getActionContainer = (): Promise<WebdriverIO.Element> => {
        if (isMobileDevice()) {
            return this.find(`${getMobileRowAttributes()} [data-component*="action-popover"]`);
        }
        return this.find(`.ag-pinned-right-cols-container ${getRowAttributes()}`);
    };

    private handleSingleAction = async (singleAction: WebdriverIO.Element, actionName: string) => {
        const singleActionLabel = await singleAction.getAttribute('aria-label');
        if (utils.formatString(singleActionLabel) === utils.formatString(actionName)) {
            await singleAction.moveTo();
            await browser.waitUntil(
                async () => {
                    const isActionDisabled = await singleAction.getAttribute('aria-disabled');
                    return isActionDisabled !== 'true';
                },
                {
                    timeout: this.timeoutWaitFor,
                    timeoutMsg: `Expected element to be enabled.\nSelector: ${singleAction.selector.toString()}`,
                },
            );
            await singleAction.waitForClickable();
            await singleAction.click();
            await waitForPromises(500, 'option click');
        } else {
            throw new Error(`Expected ${actionName} dropdown action label, but got ${singleActionLabel}`);
        }
    };

    private handleMoreActions = async (container: WebdriverIO.Element, actionName: string) => {
        const moreActions = await this.getMoreActionsButton(container);
        await utils.waitForElementToExist({ name: 'More Actions button', selector: moreActions.selector.toString() });
        await utils.waitForElementToBeDisplayed({
            name: 'More Actions button',
            selector: moreActions.selector.toString(),
        });
        await moreActions.moveTo();
        await moreActions.waitForClickable();
        await moreActions.click();
        await waitForPromises(500, 'Waiting for click on More Actions button');

        const actionPopover = '[data-component="action-popover"]';
        await utils.waitForElementToBeDisplayed({ name: 'Action PopOver', selector: actionPopover });
        const menuItems = await $$(`${actionPopover} button[type="button"]`);

        for (const item of menuItems) {
            const itemName = await item.getText();
            if (utils.formatString(itemName) === utils.formatString(actionName)) {
                await item.moveTo();
                await browser.waitUntil(
                    async () => {
                        const isActionDisabled = await item.getAttribute('aria-disabled');
                        return isActionDisabled !== 'true';
                    },
                    {
                        timeout: this.timeoutWaitFor,
                        timeoutMsg: `Expected element to be enabled.\nSelector: ${item.selector.toString()}`,
                    },
                );
                await item.waitForClickable();
                await item.click();
                await waitForPromises(500, 'option click');
                return;
            }
        }
        throw new Error(`${actionName} was not found.`);
    };

    private getMoreActionsButton = (container: WebdriverIO.Element): Promise<WebdriverIO.Element> => {
        if (testGlobals.device === 'mobile') {
            return this.find(`${getMobileRowAttributes()} [data-testid="e-action-popover-mobile-button"]`);
        }
        return container.$('[data-component="action-popover-button"]');
    };

    override clickFieldAction = async (actionLookupStrategy: utils.LookupStrategy, actionId: string) => {
        await waitForPromises(0, 'before click field action');
        const selectorToUse =
            actionLookupStrategy === utils.LookupStrategy.BIND ? `bind-${actionId}` : `label-${camelCase(actionId)}`;
        await this.click(`[data-testid~="e-header-action-${selectorToUse}"]`);
        await waitForPromises(500, 'option click');
    };

    waitForOptionMenuDisplayed = async () => {
        const optionMenu = await this.find('.e-option-item-menu');
        await utils.waitForElementToBeDisplayed({ name: 'option menu', selector: optionMenu.selector.toString() });
    };

    expectOptionMenuDisplayed = async () => {
        const optionMenu = await this.find('.e-option-item-menu');
        await utils.expectElementToBeDisplayed({
            selector: optionMenu.selector.toString(),
            reverse: false,
            name: 'option menu',
        });
    };

    clickOnOptionMenu = async () => {
        const optionMenu = await this.find('.e-option-item-menu');
        await optionMenu.click();
        await waitForPromises(500, 'option click');
    };

    selectOptionMenuInput = async (input: string) => {
        await this.expectOptionMenuDisplayed();
        const optionMenuItem = await this.find(`[data-testid~="e-ui-select-suggestion"][id*="${input}"] p`);
        try {
            await optionMenuItem.click();
            await waitForPromises(500, 'option click');
        } catch (error) {
            throw new Error(`Expected element could not be found: ${input}\nSelector: ${this.cssSelector}`);
        }
    };

    waitForTableStopLoading = async () => {
        const loadingIndicator = await this.find('.ag-loading');
        await loadingIndicator.waitForExist({ reverse: true });
    };

    waitForTableFieldToLoad = async () => {
        const loadingIndicator = await this.find(
            utils.getDataTestIdSelector({
                domSelector: 'div',
                dataTestIdValue: 'e-table-field-loading-field',
            }),
        );
        await loadingIndicator.waitForExist({
            reverse: true,
            timeout: this.timeoutWaitFor,
            timeoutMsg: 'Expected element to be loaded',
        });
    };

    clickBackButton = async () => {
        const backButton = await this.find('[data-testid="e-mobile-nested-grid-header-back-button"]');
        if (await backButton.isExisting()) {
            await backButton.moveTo();
            await backButton.waitForClickable();
            await backButton.click();
            await waitForPromises(500, 'back click');
        } else {
            throw new Error(
                `Expected element could not be found: "back arrow".\nSelector: ${this.cssSelector} [data-testid="e-mobile-nested-grid-header-back-button"]`,
            );
        }
    };
}
