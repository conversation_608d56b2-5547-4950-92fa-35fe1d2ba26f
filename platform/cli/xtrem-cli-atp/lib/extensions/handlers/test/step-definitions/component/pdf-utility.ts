import * as fs from 'fs';
import * as path from 'path';
import * as pdfParse from 'pdf-parse-new';
import { LignAlignment } from '../step-definitions-utils';
import AbstractPageObject from './abstract-page-object';
import { textFileUtility } from './textfile-utility';

interface PDFData {
    numpages: number;
    numrender: number;
    info: any;
    metadata: any;
    version: string;
    text: string;
}
interface TextItem {
    filtered: TextItem[];
    str: string;
    hasEOL: boolean;
    transform: number[];
    width: number;
    height: number;
    fontName: string;
}

const DEFAULT_Y_THRESHOLD = 2;
let Y_THRESHOLD = DEFAULT_Y_THRESHOLD;
const DEFAULT_Y_ALIGNMENT = LignAlignment.CENTER;
let Y_ALIGNMENT = DEFAULT_Y_ALIGNMENT;
function isHorizontallyAligned(item1: TextItem, item2: TextItem): boolean {
    return (
        Math.abs(item2.transform[4] - item1.transform[4]) < 1 ||
        Math.abs(item2.transform[4] + item2.width - (item1.transform[4] + item1.width)) < 1 ||
        Math.abs(item2.transform[4] + item2.width / 2 - (item1.transform[4] + item1.width / 2)) < 1
    );
}

function isVerticallyAligned(item1: TextItem, item2: TextItem): boolean {
    return Math.abs(item2.transform[5] - item1.transform[5]) < item1.height * 2;
}

function isAboveOrAligned(item1: TextItem, item2: TextItem): boolean {
    return item1.transform[5] - item2.transform[5] >= 0;
}

function isOverlapping(item1: TextItem, item2: TextItem) {
    const OVERLAP_THRESHOLD = 0.01;

    return (
        item1 !== item2 &&
        item1.transform[4] < item2.transform[4] &&
        item1.width + item1.transform[4] > item2.transform[4] - OVERLAP_THRESHOLD &&
        item1.height + item1.transform[5] > item2.transform[5] - OVERLAP_THRESHOLD &&
        item1.transform[5] === item2.transform[5] &&
        item2.str.trim() !== '' &&
        item1.str.trim() !== ''
    );
}

function hasOverlapping(item: TextItem, items: TextItem[]) {
    return items.filter(
        (item2: TextItem) => isOverlapping(item, item2) && item.str.trim() !== '' && item2.str.trim() !== '',
    );
}

function collectNestedValues(items: TextItem[], field: 'str' | 'transform'): string[] {
    const result: string[] = [];
    const visited = new Set<TextItem>(); // Utilisé pour suivre les éléments déjà visités

    function traverse(item: TextItem) {
        // Vérifie si l'élément a déjà été visité pour éviter les boucles infinies
        if (visited.has(item)) {
            return;
        }

        // Marque l'élément comme visité
        visited.add(item);

        // Ajoute la valeur du champ spécifié au tableau
        if (item[field]) {
            result.push(item[field] as string);
            if (field === 'str') item[field] = '';
        }

        // Si l'élément a un champ "filtered", on continue la récursion
        if (item.filtered && item.filtered.length > 0) {
            item.filtered.forEach(traverse);
        }
    }

    // Parcourt tous les éléments de la liste initiale
    items.forEach(traverse);

    return result;
}

function checkForTruncated(textContent: { items: any[] }) {
    // CHECKS FOR TRUNCATED ELEMENTS

    // filter all strings with end of lines and on empty strings
    const truncated = textContent.items.filter((item: any) => item.hasEOL && item.str);
    // sort truncated array by vertical position (Y) top to bottom
    truncated.sort((a: any, b: any) => b.transform[5] - a.transform[5]);
    // for each element
    truncated.forEach(truncatedItem => {
        // filter all strings aligned horizontaly left or right and in the Y threshold, that are below, with same font and not empty
        const filtered = textContent.items
            .filter(
                (filteredItem: any) =>
                    isHorizontallyAligned(truncatedItem, filteredItem) &&
                    isVerticallyAligned(truncatedItem, filteredItem) &&
                    isAboveOrAligned(truncatedItem, filteredItem) &&
                    // item2.fontName === item.fontName &&
                    filteredItem.str.trim() !== '' &&
                    truncatedItem.str.trim() !== '',
            )
            // sort filtered array by vertical position (Y) top to bottom
            .sort((a: any, b: any) => b.transform[5] - a.transform[5]);
        // for all elements fltered
        for (let i = 0; i < filtered.length; i += 1) {
            // check if end of string is reached and truncate array if so.
            if (i < filtered.length - 1 && !filtered[i].hasEOL && filtered[i + 1].hasEOL) {
                filtered.length = i + 1;
                console.log('Threshold value might be higher than required.');
            }
        }
        // add filtered elements only to first item checked.
        if (truncatedItem.str === filtered[0].str) truncatedItem.filtered = filtered;
    });
    // for all truncated items
    truncated.forEach(item => {
        const collectedValues = collectNestedValues(item.filtered, 'str');
        const collectedTransform = collectNestedValues(item.filtered, 'transform');
        item.str = collectedValues.join(' ');

        const arr: number[] = [];
        collectedTransform.forEach(transform => {
            arr.push(Number(transform[5]));
        });
        const sum = arr.reduce((a: number, b: number) => a + b, 0);
        const avg = sum / arr.length || 0;
        if (Y_ALIGNMENT === LignAlignment.CENTER) item.transform[5] = avg;
        if (Y_ALIGNMENT === LignAlignment.TOP) item.transform[5] = arr[0];
        if (Y_ALIGNMENT === LignAlignment.BOTTOM) item.transform[5] = arr[arr.length - 1];
    });
}

function checkForOverlapping(textContent: { items: any[] }) {
    // CHECKS FOR OVERLAPPING ELEMENTS

    // pour i de 0 à la longueur du tableau d'items de textContent
    // for i from 0 to the length of the textContent items array
    for (let i = 0; i < textContent.items.length; i += 1) {
        // overlappedItems est le tableau des items qui se chevauchent avec l'item i renvoyé par la fonction hasOverlapping
        // overlappedItems is the array of items that overlap with item i returned by the hasOverlapping function

        let overlappedItems = hasOverlapping(textContent.items[i], textContent.items);
        while (overlappedItems.length > 0 && textContent.items[i].str.trim() !== '') {
            overlappedItems = hasOverlapping(textContent.items[i], textContent.items);
            overlappedItems.forEach((overlappedItem: TextItem) => {
                // on ajoute à l'item i le texte de  l'item qui se chevauche
                // we add to item i the text of the overlapping item
                if (overlappedItem.hasEOL) textContent.items[i].hasEOL = overlappedItem.hasEOL;
                textContent.items[i].str += overlappedItem.str;
                // on réajuste la nouvelle longueur réelle de la chaine mergée
                // we readjust the new actual length of the merged string
                textContent.items[i].width =
                    overlappedItem.transform[4] - textContent.items[i].transform[4] + overlappedItem.width;
                overlappedItem.str = '';
            });
            console.log('Merging overlapping', textContent.items[i].str);
        }
    }
}

async function render_page(pageData: {
    getTextContent: (OPTIONS: {
        // replaces all occurrences of whitespace with standard spaces (0x20). The default value is `false`.

        normalizeWhitespace: boolean;

        // do not attempt to combine same line TextItem's. The default value is `false`.

        disableCombineTextItems: boolean;

        // defines a threshold for ordering texts as being on the same vertical position

        threshold: number;
    }) => Promise<any>;
}): Promise<string> {
    const render_options = {
        // replaces all occurrences of whitespace with standard spaces (0x20). The default value is `false`.

        normalizeWhitespace: false,

        // do not attempt to combine same line TextItem's. The default value is `false`.

        disableCombineTextItems: false,

        // defines a threshold for ordering texts as being on the same vertical position

        threshold: Y_THRESHOLD,
    };

    // it uses the .then() method to return the text content of the page as required by pdf-parse-new
    let text = '';

    await pageData.getTextContent(render_options).then((textContent: { items: any[] }) => {
        text = '';

        // Sort text items first by vertical (Y) position, then by horizontal (X) position

        checkForOverlapping(textContent);
        checkForTruncated(textContent);

        const sortedItems = textContent.items
            .filter((item: TextItem) => item.str.trim() !== '')
            .sort((a: TextItem, b: TextItem) => {
                const aX = a.transform[4];
                const bX = b.transform[4];
                const aY = a.transform[5];
                const bY = b.transform[5];

                if (Math.abs(aY - bY) > Y_THRESHOLD) {
                    return bY - aY;
                }

                // If Y positions are the same (within threshold), sort by X (ascending)

                return aX - bX;
            });

        let lastY: number | null = null;

        let currentLine: any[] = [];

        const appendCurrentLine = () => {
            if (currentLine.length > 0) {
                currentLine.forEach((item: any) => {
                    text += `${item.str} `;
                });

                text += '\n';

                currentLine = [];
            }
        };

        // eslint-disable-next-line no-restricted-syntax
        for (const item of sortedItems) {
            const currentY = item.transform[5];

            if (lastY !== null && Math.abs(lastY - currentY) > Y_THRESHOLD) {
                appendCurrentLine();
            }

            currentLine.push(item);

            lastY = currentY;
        }

        appendCurrentLine();

        text = text.trim();
    });

    return text.trim();
}
export default class PDFUtility extends AbstractPageObject {
    // eslint-disable-next-line class-methods-use-this
    public async renameFile(fileName: string, filePattern: string) {
        await textFileUtility.renameFile(fileName, filePattern);
    }

    public async clickButton(actionName: string) {
        const toolbar = await $('.e-preview-field-toolbar');
        await toolbar.waitForExist();
        let btn: WebdriverIO.Element | undefined;
        await browser.waitUntil(
            async () => {
                btn = await toolbar.$(`button=${actionName}`);
                if (!(await btn.isExisting())) {
                    btn = await toolbar.$(`[aria-label="${actionName}"]`);
                }
                if (!(await btn.isExisting())) {
                    const btnPopOver = await $$('[data-element="action-popover-menu-item-inner-text"]');
                    for (let i = 0; i < btnPopOver.length; i += 1) {
                        if ((await btnPopOver[i].getText()) === actionName) {
                            btn = btnPopOver[i];
                        }
                    }
                }
                if (!(await btn.isExisting())) {
                    const moreBtn = await toolbar.$(`[aria-label="More options"]`);

                    await moreBtn.click();
                    const btnPopOver = await $$('[data-element="action-popover-menu-item-inner-text"]');
                    for (let i = 0; i < btnPopOver.length; i += 1) {
                        if ((await btnPopOver[i].getText()) === actionName) {
                            btn = btnPopOver[i];
                        }
                    }
                }
                // if still not existing ... click more ation ?

                return btn.isExisting();
            },
            { timeout: this.timeoutWaitFor, timeoutMsg: `Expected action "${actionName}" could not be found` },
        );

        if (btn === null && btn === undefined) {
            return;
        }

        await (btn as unknown as WebdriverIO.Element).waitForClickable();
        await (btn as unknown as WebdriverIO.Element).click();
    }

    private pdfData: PDFData | undefined = undefined;

    public async readPDF(filePath: string, fileName: string): Promise<void> {
        let dataBuffer;
        try {
            dataBuffer = fs.readFileSync(path.join(filePath, fileName));
        } catch (error) {
            throw new Error(`Expected PDF file ${path.join(filePath, fileName)} could not be found \n ${error}`);
        }

        // Define the options to pass to pdf-parse-new
        const options = {
            pagerender: render_page,
            verbosityLevel: 0 as 0 | 1 | 5, // errors: 0, warnings: 1, infos: 5
        };

        try {
            this.pdfData = (await pdfParse(dataBuffer, options as unknown as pdfParse.Options)) as PDFData;
        } catch (error) {
            throw new Error(`Expected PDF file ${fileName} could not be found`);
        }
        this.attachAllureFile(`Actual value: ${fileName}`, this.pdfData.text, 'text/plain');
        // attach file directly to report
        this.attachAllureFile(`PDF file: ${fileName}`, dataBuffer, 'application/pdf');
    }

    // eslint-disable-next-line class-methods-use-this
    private normalizePDF(text: string): string {
        return textFileUtility.normalizeText(text);
    }

    // eslint-disable-next-line class-methods-use-this
    private comparePDFTextWithInput(inputText: string, pdfText: string): string {
        const diffsArray: any[] = textFileUtility.extractDifferencesFromText(
            inputText.split('\n'),
            pdfText.split('\n'),
        );
        return textFileUtility.getErrorsList(diffsArray);
    }

    // eslint-disable-next-line class-methods-use-this
    public setThreshold(threshold: number) {
        Y_THRESHOLD = threshold;
    }

    // eslint-disable-next-line class-methods-use-this
    public resetThreshold() {
        Y_THRESHOLD = DEFAULT_Y_THRESHOLD;
    }

    // eslint-disable-next-line class-methods-use-this
    public setAlignment(alignment: LignAlignment) {
        Y_ALIGNMENT = alignment;
    }

    // eslint-disable-next-line class-methods-use-this
    public resetAlignment() {
        Y_ALIGNMENT = DEFAULT_Y_ALIGNMENT;
    }

    public assertTextInPDF(requiredText: string) {
        if (!this.pdfData) {
            throw new Error('PDF data is not loaded');
        }
        const normalizedPDFText = this.normalizePDF(this.pdfData.text);
        const errorLines = this.comparePDFTextWithInput(requiredText, normalizedPDFText);
        const numberedText = requiredText
            .split('\n')
            .map((line: any, index: number) => `Line ${index + 1}: ${line}`)
            .join('\n');

        this.attachAllureFile('Expected value', numberedText, 'text/plain');
        this.attachAllureFile('Actual value', normalizedPDFText, 'text/plain');

        if (errorLines) {
            this.attachAllureFile('Error lines', errorLines, 'text/plain');
            throw new Error(`Expected value doesn't match the pdf actual content.`);
        }
    }
}

export const pdfUtility = new PDFUtility('');
