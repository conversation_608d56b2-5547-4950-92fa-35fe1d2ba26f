import { ElementContext, getContextSelector, getDataTestIdSelector } from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';

export enum CrudButtonStatus {
    disabled = 'disabled',
    enabled = 'enabled',
    visible = 'visible',
    hidden = 'hidden',
}

export class CrudButtonObject extends AbstractPageObject {
    constructor(crudAction: string, context?: ElementContext) {
        super(
            `${context ? `${getContextSelector(context)} ` : ''}${getDataTestIdSelector({
                domSelector: 'button',
                dataTestIdValue: `e-page-crud-button-${crudAction === 'cancel' ? 'close' : crudAction}`,
            })}`,
        );
    }

    async expectStatusToBe(status: CrudButtonStatus) {
        switch (status) {
            case CrudButtonStatus.enabled:
                await this.expectToBeEnabled(this.cssSelector);
                break;
            case CrudButtonStatus.disabled:
                await this.expectNotToBeEnabled(this.cssSelector);
                break;
            case CrudButtonStatus.visible:
                await this.expectToBeDisplayed(this.cssSelector);
                break;
            case CrudButtonStatus.hidden:
                await this.expectToDisappear(this.cssSelector);
                break;
            default:
                break;
        }
    }
}
