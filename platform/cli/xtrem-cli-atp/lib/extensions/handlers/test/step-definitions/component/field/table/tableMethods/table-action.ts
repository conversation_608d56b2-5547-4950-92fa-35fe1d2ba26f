/* eslint-disable no-restricted-syntax */
/* eslint-disable class-methods-use-this */
import { camelCase } from 'lodash';
import {
    AsyncNumberReturnVoid,
    AsyncObjectStringLookupStrategyReturnVoid,
    AsyncStringBooleanReturnVoid,
    AsyncStringLookupStrategyReturnVoid,
    AsyncStringNumberReturnVoid,
    AsyncStringReturnVoid,
    EnabledState,
    expectElementToBeDisplayed,
    formatString,
    getDataTestIdSelector,
    LookupStrategy,
    takeScreenshot,
    waitForElementToBeDisplayed,
    waitForElementToExist,
} from '../../../../step-definitions-utils';
import AbstractPageObject from '../../../abstract-page-object';
import { waitForPromises } from '../../wait-util';
import { TableObject } from '../table-object';
import { TableExpect } from './table-expect';

const SELECTOR = {
    rowIndexSelector: (rowNumber: number) => `[row-index="${rowNumber - 1}"]`,
};

const MODAL_ROOT_SELECTOR = 'div[data-component="dialog"][role="dialog"][aria-modal="true"]';

export class TableAction extends AbstractPageObject {
    constructor(table: TableObject) {
        super(table.cssSelector);
    }

    static btnSelector = (selector: String) => `button[aria-label="${selector}"]`;

    clickBulkActionButton: AsyncStringLookupStrategyReturnVoid = async (buttonIdentifier, lookupStrategy) => {
        const BulkActionButton = await this.find(
            `[data-testid~="bulk-action-${
                lookupStrategy === LookupStrategy.BIND
                    ? `bind-${buttonIdentifier}`
                    : `label-${camelCase(buttonIdentifier)}`
            }"]`,
        );
        await waitForElementToBeDisplayed({
            name: 'bulk action button',
            selector: BulkActionButton.selector.toString(),
        });
        await BulkActionButton.click();
    };

    clickBusinessActionButton: AsyncStringLookupStrategyReturnVoid = async (identifier, lookupStrategy) => {
        const headerActionButton = await this.find(
            `[data-testid~="e-field-${
                lookupStrategy === LookupStrategy.BIND ? `bind-${identifier}` : `label-${camelCase(identifier)}`
            }"]`,
        );
        await waitForElementToBeDisplayed({
            name: 'business action button',
            selector: headerActionButton.selector.toString(),
        });
        await headerActionButton.click();
    };

    clickButton: AsyncStringReturnVoid = async selector => {
        const btnSelector = TableAction.btnSelector(selector);
        const button = await (await this.get()).$(btnSelector);
        await button.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${selector}" button.\nSelector:${btnSelector}`,
        });
        await button.moveTo();
        await button.waitForClickable();
        await button.click();
        await waitForPromises(200, 'wait button to be clicked');
    };

    openActionsDropdown: AsyncNumberReturnVoid = async rowNumber => {
        const $moreActions = await this.find(
            `${SELECTOR.rowIndexSelector(rowNumber)} [data-component="action-popover-button"] span`,
        );
        await waitForElementToBeDisplayed({
            name: 'More actions button',
            selector: $moreActions.selector.toString(),
        });
        await $moreActions.scrollIntoView();
        await $moreActions.moveTo();
        await browser.execute(`document.querySelector('${$moreActions.selector}').click()`);
        await waitForPromises(900, 'Waiting for click on More Actions button');
    };

    clickDropdownAction: AsyncStringNumberReturnVoid = async (expectedAction, rowNumber) => {
        // scrollto() should be deprecated (wdio 8)
        await this.scrollTo({ selector: `.ag-pinned-right-cols-container ${SELECTOR.rowIndexSelector(rowNumber)}` });
        await waitForPromises(500, 'Wait for extra processing before click');

        const singleAction = `${SELECTOR.rowIndexSelector(rowNumber)} ${getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: 'e-popover-action-single-button',
        })}`;
        const $singleAction = await this.find(singleAction);

        if (await $singleAction.isDisplayed()) {
            await this.handleSingleAction($singleAction, expectedAction);
            return;
        }

        await this.openActionsDropdown(rowNumber);
        await this.handleMoreActions(expectedAction);
    };

    handleSingleAction = async ($singleAction: WebdriverIO.Element, expectedAction: string): Promise<void> => {
        const singleActionLabel = await $singleAction.getAttribute('aria-label');
        if (formatString(singleActionLabel) === formatString(expectedAction)) {
            await $singleAction.moveTo();
            await TableExpect.expectButtonEnabled($singleAction);
            await $singleAction.waitForClickable();
            await browser.execute(`document.querySelector('${$singleAction.selector}').click()`);
            await waitForPromises(900, 'Waiting for button click');
        } else {
            throw new Error(
                `Expected element could not be found: "${expectedAction}".\nSelector: ${$singleAction.selector.toString()}`,
            );
        }
    };

    handleMoreActions: AsyncStringReturnVoid = async expectedAction => {
        const actionPopOverSelector = '[data-component="action-popover"]';
        await waitForElementToBeDisplayed({
            name: 'Action PopOver',
            selector: actionPopOverSelector,
        });

        const menuItems = await $$(`${actionPopOverSelector} button[type="button"]`);

        for (const item of menuItems) {
            const itemName = await item.getText();
            if (formatString(itemName) === formatString(expectedAction)) {
                await item.moveTo();
                await TableExpect.expectButtonEnabled(item);
                await item.waitForClickable();
                await item.click();
                await waitForPromises(500, 'Waiting for click on action');
                return;
            }
        }
        throw new Error(
            `Expected element could not be found: "${expectedAction}".\nSelector: ${actionPopOverSelector}`,
        );
    };

    clickHeaderActionButton: AsyncStringLookupStrategyReturnVoid = async (identifier, lookupStrategy) => {
        const selector = `[data-testid~="e-header-action-${
            lookupStrategy === LookupStrategy.BIND ? `bind-${identifier}` : `label-${camelCase(identifier)}`
        }"]`;
        const headerActionButton = await this.find(selector);
        await headerActionButton.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${identifier}" action button.\nSelector: ${selector}`,
        });
        await headerActionButton.click();
        // await this.jsClick({ cssSelector: selector, ignoreContext: false, skipVisibilityCheck: true });
        await waitForPromises(500, 'header action refresh wait time');
    };

    clickInlineActionButton: AsyncStringNumberReturnVoid = async (buttonName, rowNumber) => {
        const actionRow = await this.find(SELECTOR.rowIndexSelector(rowNumber));
        await actionRow.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: row "${rowNumber}".\nSelector: ${actionRow.selector.toString()}`,
        });
        await actionRow.scrollIntoView();
        const inlineActionButton = await $(
            `${SELECTOR.rowIndexSelector(rowNumber)} [aria-label="${buttonName}"][data-testid~="e-table-inline-action"]`,
        );
        await inlineActionButton.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${buttonName}".\nSelector: ${inlineActionButton.selector.toString()}`,
        });
        await inlineActionButton.scrollIntoView();
        await inlineActionButton.moveTo();
        await inlineActionButton.waitForClickable();
        await waitForPromises(500, 'Wait for click');
        await inlineActionButton.click();
        const isModalOpen = await $(MODAL_ROOT_SELECTOR).isExisting();
        if (!isModalOpen) {
            await waitForPromises(500, 'Wait for click');
        }
    };

    enableSelectAll = async () => {
        const cssSelector = '.ag-header-select-all';
        const selectAllCheckbox = await this.find(cssSelector);
        await waitForElementToExist({ name: 'element', selector: selectAllCheckbox.selector.toString() });
        // Wait for clickable does not work ; since wdio 8 issue also with jsclick
        await selectAllCheckbox.click();
        await waitForPromises(500, 'all rows click wait time');
    };

    expectActionButtonToBeDisplayed: AsyncObjectStringLookupStrategyReturnVoid = async ({
        id,
        lookupStrategy,
        reverse,
    }) => {
        const selector = `[data-testid~="bulk-action-${
            lookupStrategy === LookupStrategy.BIND ? `bind-${id}` : `label-${camelCase(id)}`
        }"]`;
        const elem = await this.find(selector);
        await expectElementToBeDisplayed({ selector: elem.selector.toString(), reverse, name: 'action-button' });
        const isElement = await (await this.find(selector)).isExisting();
        if ((isElement && reverse) || (!isElement && !reverse)) {
            await takeScreenshot();
            throw new Error(`Expected Element to be ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${selector}`);
        }
    };

    expectButtonEnabledState = async (buttonName: string, expectedState: EnabledState): Promise<void> => {
        const btnSelector = TableAction.btnSelector(buttonName);
        const button = await $(btnSelector);
        await button.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${buttonName}" button.\nSelector: ${btnSelector}`,
        });
        const ariaDisabled = await button.getAttribute('aria-disabled');
        const disabled = await button.getAttribute('disabled');
        const currentState: EnabledState = ariaDisabled === 'true' || disabled === '' ? 'disabled' : 'enabled';

        if (currentState !== expectedState) {
            throw new Error(`Expected element to be ${expectedState}.\nSelector: ${btnSelector}`);
        }
    };

    expectButtonToBeDisplayed: AsyncStringBooleanReturnVoid = async (selector, reverse = false) => {
        const btnSelector = TableAction.btnSelector(selector);
        const button = await $(btnSelector);
        await expectElementToBeDisplayed({
            selector: button.selector.toString(),
            reverse,
            name: 'header-action-button',
        });
        const isElement = await button.isExisting();
        if ((isElement && reverse) || (!isElement && !reverse)) {
            await takeScreenshot();
            throw new Error(`Expected Element to be ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${btnSelector}`);
        }
    };

    expectHeaderActionButtonEnabledState = async ({
        id,
        lookupStrategy,
        expectedState,
    }: {
        id: string;
        lookupStrategy: LookupStrategy;
        expectedState: EnabledState;
    }): Promise<void> => {
        const selector = `[data-testid~="e-header-action-${
            lookupStrategy === LookupStrategy.BIND ? `bind-${id}` : `label-${camelCase(id)}`
        }"]`;
        const element = await this.find(selector);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${id}" button.\nSelector: ${element.selector.toString()})`,
        });
        const ariaDisabled = await element.getAttribute('aria-disabled');
        const disabled = await element.getAttribute('disabled');
        const currentState: EnabledState = ariaDisabled === 'true' || disabled === '' ? 'disabled' : 'enabled';

        if (currentState !== expectedState) {
            throw new Error(`Expected element to be ${expectedState}.\nSelector: ${selector}`);
        }
    };

    expectHeaderActionButtonToBeDisplayed: AsyncObjectStringLookupStrategyReturnVoid = async ({
        id,
        lookupStrategy,
        reverse,
    }) => {
        const selector = `[data-testid~="e-header-action-${
            lookupStrategy === LookupStrategy.BIND ? `bind-${id}` : `label-${camelCase(id)}`
        }"]`;
        const elem = await this.find(selector);
        await expectElementToBeDisplayed({
            selector: elem.selector.toString(),
            reverse,
            name: 'header-action-button',
        });
        const isElement = await elem.isExisting();
        if ((isElement && reverse) || (!isElement && !reverse)) {
            await takeScreenshot();
            throw new Error(`Expected Element to be ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${selector}`);
        }
    };

    hoverOverInlineAction: AsyncStringNumberReturnVoid = async (inlineAction, rowNumber) => {
        const actionRow = await this.find(SELECTOR.rowIndexSelector(rowNumber));
        await actionRow.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: row "${rowNumber}".\nSelector: ${actionRow.selector.toString()}`,
        });

        const inlineActionButton = await this.find(
            `${SELECTOR.rowIndexSelector(rowNumber)} [aria-label="${inlineAction}"][data-testid~="e-table-inline-action"]`,
        );
        await inlineActionButton.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${inlineAction}".\nSelector: ${inlineActionButton.selector.toString()}`,
        });

        await browser.execute(elem => elem.scrollIntoView(), inlineActionButton);

        await inlineActionButton.waitForDisplayed();
        await inlineActionButton.waitForClickable();
        await waitForPromises(500, 'Wait for displayed');
        await inlineActionButton.moveTo();
        await waitForPromises(500, 'Wait for moved to');
    };
}
