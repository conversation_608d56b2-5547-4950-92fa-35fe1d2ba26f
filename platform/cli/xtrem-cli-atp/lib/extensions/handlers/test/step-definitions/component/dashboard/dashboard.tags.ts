import { After, Before } from '@cucumber/cucumber';
import pageModel from '../main-page';
import { Dashboard } from './dashboard-object';

After({ tags: '@ClearDashboards' }, async () => {
    await pageModel.openApp({ url: '', device: 'desktop' });
    const dashboard = new Dashboard();
    await dashboard.deleteAllDashboards();
});

Before({ tags: '@ClearDashboardsBefore' }, async () => {
    await pageModel.openApp({ url: '', device: 'desktop' });
    const dashboard = new Dashboard();
    await dashboard.deleteAllDashboards();
});
