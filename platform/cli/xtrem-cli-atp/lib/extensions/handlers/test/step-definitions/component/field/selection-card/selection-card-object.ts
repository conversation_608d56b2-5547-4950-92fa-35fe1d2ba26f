/* eslint-disable @typescript-eslint/naming-convention */
import { ElementContext, LookupStrategy, waitForElementToBeDisplayed } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

export interface ISelectionCardObject {
    identifier: string;
    lookupStrategy: LookupStrategy;
    context?: ElementContext;
}

export class SelectionCardObject extends FieldObject {
    constructor({ identifier, lookupStrategy, context }: ISelectionCardObject) {
        super({ fieldType: 'selection-card', identifier, lookupStrategy, context });
    }

    async clickCardToSelect(value: string) {
        const item = await this.find(`[aria-label="${value}"]`);
        await item.waitForExist({
            timeoutMsg: `The radio button with value '${value}' could not be found.\nSelector: ${this.cssSelector} input[type="button"][value="${value}"]`,
            timeout: this.timeoutWaitFor,
        });
        await item.click();
        await waitForPromises(500, 'click on selection card button');
    }

    async expectCardSelected(value: string, reverse: boolean = false) {
        const selector = `[aria-label="${value}"].e-selection-card-selected`;
        const selectionCardSelected = await this.find(selector, true);
        await waitForElementToBeDisplayed({
            name: 'selection card selected',
            selector: selectionCardSelected.selector.toString(),
            reverse,
        });
    }
}
