import { expect, valueCheckTimeout } from '../index';

export const expectPageURL = async (toBe: string) => {
    const browserURL = await browser.getUrl();
    return !!browserURL.match(toBe);
};

export const expectPageToBeDisplayed = async (selector: string) => {
    try {
        await browser.waitUntil(
            async () => {
                const isDisplayed: any = await browser.execute(
                    `return document.querySelector('${selector}') !== null;`,
                );
                return isDisplayed;
            },
            { timeout: valueCheckTimeout },
        );
    } catch (error) {
        throw new Error(`Element expected to appear in ${valueCheckTimeout} ms.\nSelector: '${selector}')`);
    }
};

export const expectAriaChecked = ({
    toBe,
    reverse = false,
    selector = '',
}: {
    toBe: string;
    reverse?: boolean;
    selector?: string;
}) => {
    const elementChecked = reverse ? toBe === 'false' : toBe === 'true';
    try {
        expect(elementChecked).equals(true);
    } catch {
        throw new Error(`Expected Element to be ${reverse ? 'unchecked' : 'checked'}.\nSelector: ${selector}`);
    }
};
