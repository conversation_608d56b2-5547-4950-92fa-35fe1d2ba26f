/* eslint-disable no-restricted-syntax */

import { camelCase } from 'lodash';
import * as utils from '../../../../step-definitions-utils';
import AbstractPageObject from '../../../abstract-page-object';
import { waitForPromises } from '../../wait-util';
import { TableObject } from '../table-object';

export class TableColumns extends AbstractPageObject {
    private table: TableObject;

    constructor(table: TableObject) {
        super(table.cssSelector);
        this.table = table;
    }

    clickOnHeaderButton = async (buttonName: utils.ButtonName): Promise<void> => {
        const buttonSelectors: { [key in utils.ButtonName]: string } = {
            [utils.ButtonName.CALENDAR]: `${utils.getDataTestIdSelector({
                domSelector: 'div',
                dataTestIdValue: 'e-field-header-actions',
            })} span[data-element="calendar"]`,
            [utils.ButtonName.COLUMN_DISPLAY]: '[data-element="settings"]',
            [utils.ButtonName.FILTER]: '[data-element="filter"]',
            [utils.ButtonName.VIEW_MODE]: '.e-table-field-view-mode',
        };
        const buttonSelector = buttonSelectors[buttonName];
        if (!buttonSelector) {
            throw Error(`Unexpected button type: ${buttonName}`);
        }
        await this.click(`.e-field-header button ${buttonSelector}`, true);
        await waitForPromises(500, 'click header button');
    };

    expectColumnsToBeDisplayed: utils.AsyncObjectStringLookupStrategyReturnVoid = async ({
        id,
        lookupStrategy,
        reverse,
    }) => {
        const expectedColumns: string[] = id
            .split(',')
            .map((columnIdentifier: string) =>
                lookupStrategy === utils.LookupStrategy.BIND
                    ? columnIdentifier.trim()
                    : camelCase(columnIdentifier.trim()),
            );

        const currentColumns = await this.getCurrentColumns(lookupStrategy);
        const result = expectedColumns.every(expectedColumn => {
            return reverse ? !currentColumns.includes(expectedColumn) : currentColumns.includes(expectedColumn);
        });

        if (!result) {
            const element = await this.table.filter.getColumnHeader(id, lookupStrategy);
            throw new Error(
                `Expected element to be: ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${element.selector.toString()}`,
            );
        }
    };

    expectHeaderToBeReady = async (headerSelector: string, elementSelector: string): Promise<void> => {
        await this.table.expect.waitForTableStopLoading();
        await waitForPromises(200, 'wait for header ready');
        await utils.waitForElementToExist({ name: 'header', selector: headerSelector });
        await utils.waitForElementToExist({ name: 'header inline container', selector: elementSelector });
    };

    getColumnIndex = async (columnIdentifier: string): Promise<number> => {
        const columns = await this.findAll('.ag-header-cell');

        for (let i = 0; i < columns.length; i += 1) {
            const column = columns[i];
            const colId = await column.getAttribute('col-id');
            if (colId === columnIdentifier) {
                return i + 1;
            }
        }
        return -1;
    };

    getCurrentColumns = async (columnLookupStrategy: utils.LookupStrategy): Promise<string[]> => {
        const regex = columnLookupStrategy === utils.LookupStrategy.BIND ? /-bind-/ : /-label-/;

        const columns = await this.findAll('.ag-header-cell');
        const currentColumnIdentifiers: string[] = [];

        for (const column of columns) {
            const columnClassList = (await column.getAttribute('class')).split(' ');
            const match = columnClassList.find(columnClass => regex.test(columnClass));

            if (match) {
                const matchClass = match.split(regex);
                currentColumnIdentifiers.push(matchClass[1]);
            }
        }

        return currentColumnIdentifiers;
    };

    openColumnMenu: utils.AsyncStringLookupStrategyReturnElement = async (columnIdentifier, columnLookupStrategy) => {
        const headerSelector = this.table.filter.getHeaderSelector(columnIdentifier, columnLookupStrategy);
        const header = (await this.table.filter.getHeader(
            columnIdentifier,
            columnLookupStrategy,
        )) as WebdriverIO.Element;
        await header.scrollIntoView();
        await header.moveTo();
        const element = await this.find(`${headerSelector} span[data-ref="eMenu"]`);
        await this.expectHeaderToBeReady(headerSelector, element.selector.toString());
        await element.moveTo();
        const isMenuVisible = await (await this.find('.ag-popup [data-ref="eColumnMenu"]')).isDisplayed();
        if (!isMenuVisible) {
            await element.click();
        }
        await waitForPromises(200, 'click header');
        await (await this.find('.ag-popup [data-ref="eColumnMenu"]')).isDisplayed();
        return this.waitForDisplayedAndGetElement({
            selector: '.ag-popup [data-ref="eColumnMenu"]',
        });
    };

    selectColumn: utils.AsyncStringReturnVoid = async columnName => {
        let columnsToDisplay = columnName.split(',');
        columnsToDisplay = columnsToDisplay.map(column => column.trim());
        const menuItems = await this.findAll('.e-table-columns-display-menu-item');
        await this.table.tableClick.unselectAllMenuItems(menuItems);
        await this.table.tableClick.selectMenuItems(columnsToDisplay, menuItems);
    };
}
