import { Then, When } from '@cucumber/cucumber';
import pageModel from '../../main-page';
import * as StaticStore from '../../static-store';
import { NavigationPanelObject } from './navigation-panel-object';

When(/^the user searches for "(.*)" in the navigation panel$/, async (query: string) => {
    const navigationPanel = new NavigationPanelObject();
    await navigationPanel.searchFor(query);
    await pageModel.waitForFinishLoading();
});

When(/^the user clears the search field in the navigation panel$/, async () => {
    const navigationPanel = new NavigationPanelObject();
    await navigationPanel.clearNavigationPanel();
    await pageModel.waitForFinishLoading();
});

When(/^the search field value in the navigation panel is "(.*)"$/, async (expectedContent: string) => {
    const navigationPanel = new NavigationPanelObject();
    await navigationPanel.expectSearchValue(expectedContent);
    await pageModel.waitForFinishLoading();
});

When(/^the user clicks the "(.*)" navigation panel's row$/, async (rowNb: string) => {
    const navigationPanel = new NavigationPanelObject();
    await navigationPanel.clickOnPanelRow(rowNb);
    await pageModel.waitForFinishLoading();
});

When(/^the user clicks the record with the text "(.*)" in the navigation panel$/, async (recordName: string) => {
    const navigationPanel = new NavigationPanelObject();
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(recordName);
    await navigationPanel.clickOnRecord(storeValue);
    await pageModel.waitForFinishLoading();
});

When(/^the user clicks the "(.*)" toggle button in the navigation panel$/, async (toggleButton: string) => {
    const navigationPanel = new NavigationPanelObject();
    await navigationPanel.selectToggleButton(toggleButton);
    await pageModel.waitForFinishLoading();
});

When(/^the user removes the "(.*)" filter from the header of the navigation panel$/, async (text: string) => {
    const navigationPanel = new NavigationPanelObject();
    await navigationPanel.removeFilterPill(text);
    await pageModel.waitForFinishLoading();
});

When(/^the user selects the "(.*)" dropdown option in the navigation panel$/, async (label: string) => {
    const navigationPanel = new NavigationPanelObject();
    await navigationPanel.selectDropdownOption(label);
    await pageModel.waitForFinishLoading();
});

When('the navigation panel is empty', async () => {
    const navigationPanel = new NavigationPanelObject();
    await navigationPanel.expectNavigationPanelToBeEmpty();
});
When(/^the user (opens|closes) the navigation panel$/, async (open: string) => {
    const navigationPanel = new NavigationPanelObject();
    await navigationPanel.openNavigationPanel(open !== 'opens');
});

Then(
    /^the pill containing the "([^"\n\r]*)" filter with value "([^"\n\r]*)" for the "([^"\n\r]*)" column in the navigation panel is (displayed|hidden)$/,
    async (filterType: string, value: string, columnName: string, cssState: 'displayed' | 'hidden') => {
        const navigationPanel = new NavigationPanelObject();
        await navigationPanel.expectFilterPillDisplayed({
            filterType,
            value,
            columnName,
            reverse: cssState === 'hidden',
        });
    },
);

Then(
    /^the user removes the pill containing the "([^"\n\r]*)" filter with value "([^"\n\r]*)" for the "([^"\n\r]*)" column in the navigation panel$/,
    async (filterType: string, value: string, columnName: string) => {
        const navigationPanel = new NavigationPanelObject();
        await navigationPanel.closePill({ filterType, value, columnName });
    },
);
