/* eslint-disable radix */
import { ElementContext, getDataTestIdSelector, LookupStrategy } from '../../../step-definitions-utils';
import { changeDateWithFormat, getUserLocale} from '../date-utils';
import { FieldObject } from '../field-object';
import { waitMillis } from '../wait-util';

const months = [
    'january',
    'february',
    'march',
    'april',
    'may',
    'june',
    'july',
    'august',
    'september',
    'october',
    'november',
    'december',
];

export class CalendarFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'calendar', identifier, lookupStrategy, context });
    }

    async getDateFromUserFormat(rawDate: string) {
        const currentView = await this.getCurrentViewName();
        let month = 0;
        let day = 0;
        let year = 0;
        let parts: RegExpMatchArray | null = null;
        switch (currentView) {
            case 'day':
                // eslint-disable-next-line @sage/redos/no-vulnerable
                parts = rawDate.match(/([A-Za-z]+) ([0-9]{1,2}).*([0-9]{4})/);
                if (!parts) {
                    throw new Error(`The supplied date format is invalid for this view (${currentView}): ${rawDate}`);
                }
                month = months.indexOf(parts[1].toLowerCase());
                year = parseInt(parts[3]);
                day = parseInt(parts[2]);
                break;
            case 'week':
                // eslint-disable-next-line @sage/redos/no-vulnerable
                parts = rawDate.match(/([A-Za-z]+) ([0-9]{1,2}).*([0-9]{4})/);
                if (!parts) {
                    throw new Error(`The supplied date format is invalid for this view (${currentView}): ${rawDate}`);
                }
                month = months.findIndex(m => m.startsWith(parts![1].toLowerCase()));
                year = parseInt(parts[3]);
                day = parseInt(parts[2]);
                break;
            case 'month':
                // eslint-disable-next-line @sage/redos/no-vulnerable
                parts = rawDate.match(/([A-Za-z]+) ([0-9]+)/);
                if (!parts) {
                    throw new Error(`The supplied date format is invalid for this view (${currentView}): ${rawDate}`);
                }
                month = months.indexOf(parts[1].toLowerCase());
                year = parseInt(parts[2]);
                day = 1;
                break;
            default:
                throw new Error(`invalid date view: ${currentView}`);
        }
        return Date.UTC(year, month, day, 0, 0, 0, 0);
    }

    async getCurrentViewName() {
        const selectedView = await this.find(
            `${getDataTestIdSelector({ domSelector: 'button', dataTestIdValue: 'e-calendar-view-select' })} span span`,
        );
        const textView = await selectedView.getText();
        return textView.toLowerCase() as 'day' | 'week' | 'month' | '3 day';
    }

    async selectView(view: string) {
        const currentView = await this.getCurrentViewName();
        if (currentView === view.toLowerCase()) {
            return;
        }
        await this.click(getDataTestIdSelector({ domSelector: 'button', dataTestIdValue: 'e-calendar-view-select' }));
        await waitMillis(500, 'calendar view');
        await this.click(
            getDataTestIdSelector({
                domSelector: 'button',
                dataTestIdValue: `e-calendar-view-select-item-dayGrid${view}`,
            }),
            true,
        );
    }

    async checkView(view: string, date: string) {
        const lang = await getUserLocale();
        const title = await this.find('.fc-toolbar-title');
        const actualPeriod = await title.getText();

        const formattedDateString = changeDateWithFormat(date, lang);
        const [month, day, year] = formattedDateString.split('/').map(part => parseInt(part, 10));
        const formattedDate = new Date(year, month - 1, day);
        const monthLong = formattedDate.toLocaleString(lang, { month: 'long' });
        const monthShort = formattedDate.toLocaleString(lang, { month: 'short' });

        const expectedPeriod = await this.getExpectedPeriod(view, formattedDate, year, monthLong, monthShort);

        if (actualPeriod === expectedPeriod) {
            return true;
        }
        throw new Error(`Expected period "${expectedPeriod}" but got "${actualPeriod}"`);
    }

    private getExpectedPeriod(view: string, date: Date, year: number, monthLong: string, monthShort: string): Promise<string> {
        const periodHandlers: Record<string, () => Promise<string>> = {
            Week: () => this.getWeekPeriod(date, year, monthShort),
            Month: () => Promise.resolve(`${monthLong} ${year}`),
            Three: () => this.getThreeDayPeriod(date, year, monthShort),
            Day: () => Promise.resolve(this.getDay(date, year, monthLong)),
        };

        return periodHandlers[view]?.() ?? this.getDay(date, year, monthLong);
    }

    private getWeekPeriod(date: Date, year: number, monthShort: string): Promise<string> {
        const startOfWeek = this.getStartOfWeek(date);
        const endOfWeek = this.getEndOfWeek(date);

        if (this.isDifferentMonth(startOfWeek, endOfWeek)) {
            return this.formatPeriodForDifferentMonths(startOfWeek, endOfWeek);
        }

        return Promise.resolve(`${monthShort} ${startOfWeek.getDate()} – ${endOfWeek.getDate()}, ${year}`);
    }

    private getThreeDayPeriod(date: Date, year: number, monthShort: string): Promise<string> {
        const dayNumber = date.getDate();
        const dayThree = new Date(date);
        dayThree.setDate(date.getDate() + 2);
        if (this.isDifferentMonth(date, dayThree)) {
            return this.formatPeriodForDifferentMonths(date, dayThree);
        }

        return Promise.resolve(`${monthShort} ${dayNumber} – ${dayThree.getDate()}, ${year}`);
    }

    // eslint-disable-next-line class-methods-use-this
    private getDay(date: Date, year: number, monthLong: string): string {
        const dayNumber = date.getDate();
        return `${monthLong} ${dayNumber}, ${year}`;
    }

    // eslint-disable-next-line class-methods-use-this
    private getStartOfWeek(date: Date): Date {
        const startOfWeek = new Date(date);
        startOfWeek.setDate(date.getDate() - date.getDay());
        return startOfWeek;
    }

    // eslint-disable-next-line class-methods-use-this
    private getEndOfWeek(date: Date): Date {
        const endOfWeek = new Date(date);
        endOfWeek.setDate(date.getDate() + (6 - date.getDay()));
        return endOfWeek;
    }

    // eslint-disable-next-line class-methods-use-this
    private isDifferentMonth(start: Date, end: Date): boolean {
        return start.getMonth() !== end.getMonth();
    }

    // eslint-disable-next-line class-methods-use-this
    private async formatPeriodForDifferentMonths(start: Date, end: Date): Promise<string> {
        const lang = await getUserLocale();
        const startMonth = start.toLocaleString(lang, { month: 'short' });
        const endMonth = end.toLocaleString(lang, { month: 'short' });
        const startYear = start.getFullYear();
        const endYear = end.getFullYear();
        if (startYear !== endYear) {
            return `${startMonth} ${start.getDate()}, ${startYear} – ${endMonth} ${end.getDate()}, ${endYear}`;
        }
        return `${startMonth} ${start.getDate()} – ${endMonth} ${end.getDate()}, ${endYear}`;
    }

    async checkEvents(targetDate: string) {
        const selector = `.fc-event.fc-event-start.fc-event-end ${getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-card' })}`;
        const selectedEvents = await this.findAll(selector, true);
        if (selectedEvents.length > 0) {
            return true;
        }
        throw new Error(`Couldn't find events for this date: ${targetDate}`);
    }

    async checkEventValue({
        eventNumber,
        expectedValue,
        targetDate,
    }: {
        eventNumber: string;
        expectedValue: string;
        targetDate: string;
    }) {
        const selector = `.fc-event.fc-event-start.fc-event-end ${getDataTestIdSelector({
            domSelector: 'div',
            dataTestIdValue: 'e-card',
        })} .e-card-title`;
        const selectedEvents = await this.findAll(selector, true);
        if (selectedEvents.length === 0) {
            throw new Error(`Couldn't find events for this date: ${targetDate}`);
        }

        const newEventNumber = parseInt(eventNumber) - 1;
        const targetValue = await selectedEvents[newEventNumber].getText();
        if (targetValue === expectedValue) {
            return true;
        }

        throw new Error(`For event number ${eventNumber} expected ${expectedValue} but got ${targetValue}`);
    }

    async getCurrentlyDisplayedFirstDate() {
        const element = await this.find(
            getDataTestIdSelector({ domSelector: 'a', dataTestIdValue: 'e-fc-calendar-event-container' }),
        );
        return element.getAttribute('data-date');
    }

    async stepToDate(date: string) {
        for (let i = 0; i < 100; i += 1) {
            const title = await this.find('.fc-toolbar-title');
            const targetDate = await this.getDateFromUserFormat(date);
            const titleContent = await title.getText();
            const currentlyDisplayedDate = await this.getDateFromUserFormat(titleContent);
            if (targetDate === currentlyDisplayedDate) {
                return;
            }
            if (targetDate > currentlyDisplayedDate) {
                await this.click('.fc-next-button');
            } else {
                await this.click('.fc-prev-button');
            }
        }

        throw new Error(`Couldn't step to selected date within 100 attempts: ${date}`);
    }

    async stepInDirection(steps: number, direction: string) {
        for (let i = 0; i < steps; i += 1) {
            await this.click(`.fc-${direction}-button`);
        }
    }

    override async expectToBeEnabled(selector: string, reverse = false) {
        const element = await browser.$(selector);
        await browser.waitUntil(
            async () => {
                const disabledAttribute = await element.getAttribute('class');
                return reverse ? disabledAttribute.includes('e-disabled') : !disabledAttribute.includes('e-disabled');
            },
            {
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected Element to be ${reverse ? 'disabled' : 'enabled'}.\nSelector: ${selector}`,
            },
        );
    }

    // async expectNotToBeDisplayed(selector: string) {
    //     const element = await this.find(selector, true);
    //     const className = await element.getAttribute('class');
    //     if (className.indexOf('e-hidden') === -1) {
    //         throw new Error(`Expected element not to be visible, but it is visible: ${this.cssSelector} ${selector}`);
    //     }
    // }

    // async expectToBeDisplayed(selector: string, reverse = false) {
    //     if (reverse) {
    //         await super.expectNotToBeDisplayed(selector);
    //     } else {
    //         await super.expectToBeDisplayed(selector, reverse);
    //     }
    // }
}
