/* eslint-disable no-restricted-syntax */
/* eslint-disable class-methods-use-this */

import { camelCase } from 'lodash';
import { Key } from 'webdriverio';
import * as utils from '../step-definitions-utils';
import AbstractPageObject from './abstract-page-object';
import { NestedGridObject } from './field/nested-grid/nested-grid-object';
import { TableObject } from './field/table/table-object';
import { waitForPromises } from './field/wait-util';

export type FilterActionsType = Array<{
    name: string;
    selector: string;
    action: (input: { value?: string; filter?: string }) => Promise<void>;
}>;

export const SELECTORS = {
    clearAllFiltersButton: utils.getDataTestIdSelector({
        domSelector: 'button',
        dataTestIdValue: 'e-no-rows-found-component-button',
    }),
    customReferenceFilter: utils.getDataTestIdSelector({
        domSelector: 'input',
        dataTestIdValue: 'e-reference-custom-filter-input',
    }),
    customReferenceFilterCheckbox: (value: string) =>
        utils.getMultipleDataTestIdSelector('input', [`e-reference-custom-filter-checkbox-label-${camelCase(value)}`]),
    dateFilter: `.e-filter-date-container ${utils.getDataTestIdSelector({
        domSelector: 'input',
        dataTestIdValue: 'e-date-filter',
    })}`,
    displayField: '[data-ref="eWrapper"] [data-ref="eDisplayField"]',
    filter: '.ag-popup .ag-filter',
    filterBody: '.ag-popup [data-ref="eFilterBody"]',
    floatingFilter: '.ag-header-viewport .ag-header-row.ag-header-row-column-filter',
    getColumnHeaderSelector: (column: string, lookupStrategy: utils.LookupStrategy): string => {
        return `.ag-header-cell.e-nested-header-${
            lookupStrategy === utils.LookupStrategy.BIND ? `bind-${column}` : `label-${camelCase(column)}`
        }`;
    },
    iconFilter: (index: string) => `.ag-floating-filter[aria-colindex="${index}"] .ag-icon-filter`,
    inputField: '[data-ref="eInput"]',
    listItems: '.ag-popup .ag-list.ag-select-list .ag-list-item',
    miniFilter: '[data-ref="eMiniFilter"]',
    numericFilterInput: utils.getDataTestIdSelector({
        domSelector: 'input',
        dataTestIdValue: 'e-ui-numeric-filter-input',
    }),
    pageTitle: '.e-page-navigation-panel-title, .e-section-header, .e-header-title-col',
    searchOverride: '.e-ui-select-input-wrapper.e-ui-select-search-override',
    selectDropdownList: `${utils.getDataTestIdSelector({
        domSelector: 'ul',
        dataTestIdValue: 'e-ui-select-dropdown',
    })} li`,
};

export class FilterObject extends AbstractPageObject {
    private component: TableObject | NestedGridObject;

    constructor(component: TableObject | NestedGridObject) {
        super(component.cssSelector);
        this.component = component;
    }

    applyNumericFilter = async ({ value, filter }: { value: string; filter?: string }): Promise<void> => {
        const $selectFilter = await this.find(
            `${SELECTORS.filter} ${SELECTORS.searchOverride} [data-element="dropdown"]`,
        );
        await $selectFilter.click();
        await waitForPromises(300, 'await open dropdown');

        const options = await $$(SELECTORS.selectDropdownList);
        await this.selectOption({ list: options, filter, defaultFilter: 'Equal to' });

        const numericFilter = SELECTORS.numericFilterInput;
        const $numericFilter = await this.find(numericFilter);

        await $numericFilter.click();
        await browser.waitUntil(
            async () => {
                await this.write({ content: value, cssSelector: numericFilter });
                await waitForPromises(500, 'await write numeric filter value');
                const test = await $(numericFilter);
                return (await test.getValue()) === value;
            },
            {
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected element to have value ${value}.\nSelector: ${$numericFilter.selector.toString()}`,
            },
        );

        await browser.keys(Key.Enter);
        await waitForPromises(500, 'await write numeric filter value');
    };

    applyCustomReferenceFilter = async ({ value }: { value: string }): Promise<void> => {
        await this.write({ content: value, cssSelector: SELECTORS.customReferenceFilter });
        await waitForPromises(500, 'filter input');
        await this.tickFilterItem(value);
    };

    applySelectorFilter = async ({ value, filter }: { value?: string; filter?: string }): Promise<void> => {
        const $selectFilter = await this.find(
            `${SELECTORS.filter} ${SELECTORS.searchOverride} [data-element="dropdown"]`,
        );
        await $selectFilter.click();
        await waitForPromises(300, 'await open dropdown');

        const options = await $$(SELECTORS.selectDropdownList);
        await this.selectOption({ list: options, filter, defaultFilter: 'Equal to' });

        if (!value && filter) return;

        if (value || (value && filter)) {
            await browser.waitUntil(
                async () => {
                    const $dateFilter = await $(`${SELECTORS.filter} ${SELECTORS.dateFilter}`);

                    await this.write({
                        content: value,
                        cssSelector: $dateFilter.selector.toString(),
                        ignoreContext: true,
                    });
                    await waitForPromises(300, 'waiting for writing date');

                    return (await $dateFilter.getValue()) === value;
                },
                {
                    timeout: this.timeoutWaitFor,
                    timeoutMsg: `Expected element to have value ${value}.\nSelector: ${$selectFilter.selector.toString()}`,
                },
            );
            await browser.keys(Key.Enter);
            await waitForPromises(300, 'await write date');

            const calIcon = '[data-component="icon"][data-element="calendar"]';
            const selectedDate = '#styled-day-picker [data-selected="true"]';

            await browser.waitUntil(
                async () => {
                    const $calIcon = await $(calIcon);
                    await $calIcon.click();
                    await waitForPromises(300, 'click calendar icon');

                    const $selectedDate = await $(selectedDate);
                    return $selectedDate.isDisplayed();
                },
                {
                    timeout: this.timeoutWaitFor,
                    timeoutMsg: `Expected element to be displayed.\nSelector: ${selectedDate}`,
                },
            );

            const $selectedDate = await $(selectedDate);
            await $selectedDate.click();
            await waitForPromises(300, 'click date picker');
        }
    };

    applyVirtualListContainerFilter = async ({ value }: { value: string }): Promise<void> => {
        const items = await $$('.ag-virtual-list-item');
        for await (const item of items) {
            const $itemLabel = await item.$('.ag-input-field-label.ag-label.ag-checkbox-label');
            const $itemInput = await item.$('input');
            if ((await $itemLabel.getText()) === value) {
                await item.moveTo();
                await $itemInput.click();
                await waitForPromises(500, 'click checkbox');
                break;
            }
        }
    };

    applyDefaultFilter = async ({ value, filter }: { value: string; filter?: string }): Promise<void> => {
        await utils.waitForElementToExist({
            name: 'display field selector',
            selector: `${SELECTORS.filterBody} ${SELECTORS.displayField}`,
        });
        await this.click(`${SELECTORS.filterBody} ${SELECTORS.displayField}`);

        const listItems = await $$(SELECTORS.listItems);
        await this.selectOption({ list: listItems, filter });

        await this.write({ content: value, cssSelector: `${SELECTORS.filterBody} ${SELECTORS.inputField}` });
        await waitForPromises(500, 'filter input');
    };

    applyFilter = async ({
        filterActions,
        value,
        filter,
    }: {
        filterActions: FilterActionsType;
        value?: string;
        filter?: string;
    }): Promise<boolean> => {
        for (const { selector, action } of filterActions) {
            const $filter = await this.find(selector);
            if (await $filter.isDisplayed()) {
                await action({ value, filter });
                return true;
            }
        }
        return false;
    };

    getFilterActions = (): FilterActionsType => [
        {
            name: 'Numeric Filter',
            selector: SELECTORS.numericFilterInput,
            action: this.applyNumericFilter.bind(this),
        },
        {
            name: 'Custom Reference Filter',
            selector: SELECTORS.customReferenceFilter,
            action: this.applyCustomReferenceFilter.bind(this),
        },
        {
            name: 'Selector Filter',
            selector: `${SELECTORS.filter} ${SELECTORS.searchOverride}`,
            action: this.applySelectorFilter.bind(this),
        },
        {
            name: 'Virtual List Container Filter',
            selector: '.ag-virtual-list-container',
            action: this.applyVirtualListContainerFilter.bind(this),
        },
        {
            name: 'Default Filter',
            selector: `${SELECTORS.filterBody} ${SELECTORS.displayField}`,
            action: this.applyDefaultFilter.bind(this),
        },
    ];

    private async waitForLoadingToDisappear(): Promise<void> {
        const loadingRowSelector = utils.getDataTestIdSelector({
            domSelector: 'div',
            dataTestIdValue: 'e-table-field-loading-field',
        });
        await browser.waitUntil(
            async () => {
                const loadingRow = await $(loadingRowSelector);
                return !(await loadingRow.isDisplayed());
            },
            {
                timeout: this.timeoutWaitFor,
                timeoutMsg: 'Loading row did not disappear in time',
            },
        );
    }

    filterColumn = async ({
        id,
        lookupStrategy,
        value,
        filter,
    }: {
        id: string;
        lookupStrategy: utils.LookupStrategy;
        value?: string;
        filter?: string;
    }): Promise<void> => {
        await this.openFilter(id, lookupStrategy);

        const filterActions: FilterActionsType = this.getFilterActions();
        const filterApplied = await this.applyFilter({ filterActions, value, filter });

        if (!filterApplied) {
            throw new Error('No filter found');
        }

        await this.waitForLoadingToDisappear();

        await this.closeFilter();
    };

    searchDefaultFilter = async (value: string): Promise<void> => {
        const defaultFilter = `${SELECTORS.filterBody} ${SELECTORS.inputField}`;
        await utils.waitForElementToBeDisplayed({ name: 'default filter', selector: defaultFilter });
        await this.write({ content: value, cssSelector: defaultFilter });
        await waitForPromises(500, 'filter input');
    };

    searchMiniFilter = async (value: string): Promise<void> => {
        const miniFilter = `${SELECTORS.filterBody} ${SELECTORS.miniFilter} ${SELECTORS.inputField}`;
        await utils.waitForElementToBeDisplayed({ name: 'mini filter', selector: miniFilter });
        await this.write({ content: value, cssSelector: miniFilter });
        await waitForPromises(500, 'filter input');
    };

    searchCustomReferenceFilter = async (value: string): Promise<void> => {
        const customReferenceFilter = SELECTORS.customReferenceFilter;
        await utils.waitForElementToBeDisplayed({ name: 'custom reference filter', selector: customReferenceFilter });
        await this.write({ content: value, cssSelector: customReferenceFilter });
        await waitForPromises(500, 'filter input');
    };

    getSearchFilterActions = () => [
        {
            name: 'Mini Filter',
            selector: `${SELECTORS.filterBody} ${SELECTORS.miniFilter} ${SELECTORS.inputField}`,
            action: this.searchMiniFilter.bind(this),
        },
        {
            name: 'Custom Reference Filter',
            selector: SELECTORS.customReferenceFilter,
            action: this.searchCustomReferenceFilter.bind(this),
        },
        {
            name: 'Default Filter',
            selector: `${SELECTORS.filterBody} ${SELECTORS.inputField}`,
            action: this.searchDefaultFilter.bind(this),
        },
    ];

    applySearchFilter = async ({
        searchFilterActions,
        value,
    }: {
        searchFilterActions: any;
        value?: string;
    }): Promise<boolean> => {
        for (const { selector, action } of searchFilterActions) {
            const $searchFilter = await this.find(selector);
            if (await $searchFilter.isDisplayed()) {
                await action(value);
                return true;
            }
        }
        return false;
    };

    searchFilter = async (value: string): Promise<void> => {
        const searchFilterActions = this.getSearchFilterActions();
        const searchFilterApplied = await this.applySearchFilter({ searchFilterActions, value });

        if (!searchFilterApplied) {
            throw new Error('No search filter found');
        }
    };

    clickFilterTab = async (tabName: string): Promise<void> => {
        const tab = `[data-testid~="e-xtrem-tab-${camelCase(tabName)}"]`;
        const $tab = await this.find(tab);
        await utils.waitForElementToExist({ name: 'tab', selector: tab });
        await $tab.click();
    };

    clickClearAllFiltersButton = async () => {
        const $clearAllFiltersButton = await $(SELECTORS.clearAllFiltersButton);
        await $clearAllFiltersButton.waitForClickable();
        await $clearAllFiltersButton.moveTo();
        await $clearAllFiltersButton.click();
        await waitForPromises(300, 'click clear all filters button');
    };

    clickClearFloatingFilterButton = async () => {
        const clearFilterButton = utils.getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: 'e-table-remove-all-filters',
        });
        const $clearFilterButton = await this.find(clearFilterButton);
        await utils.waitForElementToBeDisplayed({ name: 'create filter button', selector: clearFilterButton });
        await browser.execute(elem => elem.click({ x: 0, y: 0 }), $clearFilterButton);
    };

    clickFloatingFilterIcon = async (id: string, lookupStrategy: utils.LookupStrategy): Promise<void> => {
        const headerColumnIndex = await this.getColumnHeaderIndex(id, lookupStrategy);
        const filterIcon = SELECTORS.iconFilter(headerColumnIndex);
        const $filterIcon = await $(filterIcon);
        await $filterIcon.waitForExist();
        await $filterIcon.moveTo();
        await $filterIcon.waitForClickable();
        await this.click(filterIcon);
        await waitForPromises(500, 'click filter icon');
    };

    expectFloatingFilterEnabled = async ({
        id,
        lookupStrategy,
        reverse,
    }: {
        id: string;
        lookupStrategy: utils.LookupStrategy;
        reverse: boolean;
    }): Promise<void> => {
        const headerContainer = await this.find(SELECTORS.getColumnHeaderSelector(id, lookupStrategy));
        try {
            await utils.waitForElementToExist({ name: 'column header', selector: headerContainer.selector.toString() });
            const colIndex = await headerContainer.getAttribute('aria-colindex');
            const selectorToUse = `.ag-header-cell[aria-colindex="${colIndex}"] .ag-floating-filter-input input`;
            await this.expectToBeEnabled(selectorToUse, reverse);
        } catch (error) {
            if (error.message.includes(`wasn't found`)) {
                throw new Error(
                    `Expected element could not be found: "${id}". \nSelector: ${headerContainer.selector}`,
                );
            } else {
                throw error;
            }
        }
    };

    expectFloatingFilterValue = async ({
        id,
        lookupStrategy,
        value,
    }: {
        id: string;
        lookupStrategy: utils.LookupStrategy;
        value: string;
    }): Promise<void> => {
        const headerContainer = await this.find(SELECTORS.getColumnHeaderSelector(id, lookupStrategy));
        await utils.waitForElementToExist({ name: 'column header', selector: headerContainer.selector.toString() });
        const colIndex = await headerContainer.getAttribute('aria-colindex');
        await this.expectValue({
            toBe: value,
            cssSelector: `.ag-header-cell[aria-colindex="${colIndex}"] .ag-floating-filter-input input`,
        });
    };

    expectCustomReferenceFilterValue = async (expectedValue: string): Promise<void> => {
        const referenceFilter = SELECTORS.customReferenceFilter;
        const $referenceFilter = await this.find(referenceFilter);

        const selectorToUse = (await $referenceFilter.isDisplayed())
            ? referenceFilter
            : `${SELECTORS.filterBody} ${SELECTORS.inputField}`;

        await this.expectValue({ toBe: expectedValue, cssSelector: selectorToUse });
        await waitForPromises(200, 'filter input');
    };

    getHeaderSelector = (columnIdentifier: string, columnLookupStrategy: utils.LookupStrategy): string => {
        const columnHeaderSelector = SELECTORS.getColumnHeaderSelector(columnIdentifier, columnLookupStrategy);
        return `${columnHeaderSelector} .ag-cell-label-container`;
    };

    getColumnHeaderIndex: utils.AsyncStringLookupStrategyReturnString = async (
        columnIdentifier,
        columnLookupStrategy,
    ) => {
        const element = await this.getColumnHeader(columnIdentifier, columnLookupStrategy);
        return element.getAttribute('aria-colindex');
    };

    getColumnHeader = async (id: string, lookupStrategy: utils.LookupStrategy): Promise<WebdriverIO.Element> => {
        const columnHeaderSelector = SELECTORS.getColumnHeaderSelector(id, lookupStrategy);
        const element = await this.component.findOrFail(columnHeaderSelector);
        return element;
    };

    getHeader = async (
        columnIdentifier: string,
        columnLookupStrategy: utils.LookupStrategy,
    ): Promise<WebdriverIO.Element> => {
        const header = this.getHeaderSelector(columnIdentifier, columnLookupStrategy);
        const $header = await this.component.find(header);
        await $header.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${columnIdentifier}" column header\nSelector: ${header}`,
        });
        await this.expectToBeDisplayed(header);
        return $header;
    };

    openFilter = async (id: string, lookupStrategy: utils.LookupStrategy): Promise<void> => {
        const $filter = await $(SELECTORS.filter);
        if (await $filter.isDisplayed()) await this.closeFilter();

        if (await this.isFloatingFilterDisplayed()) {
            await this.clickFloatingFilterIcon(id, lookupStrategy);
        } else {
            const header = <WebdriverIO.Element>await this.getHeader(id, lookupStrategy);
            const $filterIcon = await header.$('span[data-ref="eFilterButton"]');
            await $filterIcon.waitForClickable();
            await $filterIcon.moveTo();
            await $filterIcon.click();
            await waitForPromises(500, 'click header');
        }
        await utils.waitForElementToBeDisplayed({ name: 'filter', selector: SELECTORS.filter });
    };

    closeFilter = async (): Promise<void> => {
        await browser.execute(`document.querySelector('${SELECTORS.filter}')?.remove()`);
        await waitForPromises(500, 'close filter');
        await utils.waitForElementNotToBeDisplayed('filter', SELECTORS.filter);
    };

    tickFilterItem = async (
        expectedValue: string,
        tickAction: utils.TickStateType = utils.TickState.TICKS,
    ): Promise<void> => {
        await utils.waitForElementToBeDisplayed({ name: 'filter', selector: SELECTORS.filter });

        const miniFilter = await $(`${SELECTORS.filterBody} ${SELECTORS.miniFilter} ${SELECTORS.inputField}`);
        const customReferenceFilter = await $(SELECTORS.customReferenceFilter);
        const defaultFilter = await $(`${SELECTORS.filterBody} ${SELECTORS.inputField}`);
        let filterType = '';
        let items: WebdriverIO.ElementArray;
        let getCheckboxState: (item: WebdriverIO.Element) => Promise<boolean>;

        const miniFilterDisplayed = await miniFilter.isDisplayed();

        if (miniFilterDisplayed || (await defaultFilter.isDisplayed())) {
            filterType = miniFilterDisplayed ? 'mini filter' : 'default filter';
            items = await $$(`${SELECTORS.filterBody} .ag-virtual-list-item`);
            getCheckboxState = async item => {
                const checkboxState = await item.getAttribute('aria-checked');
                return checkboxState === 'true';
            };
        } else if (await customReferenceFilter.isDisplayed()) {
            filterType = 'custom reference filter';
            items = await $$(
                utils.getDataTestIdSelector({
                    domSelector: 'div',
                    dataTestIdValue: 'e-reference-custom-filter-item',
                }),
            );
            getCheckboxState = async item => {
                const checkboxState = await (await item.$('input')).isSelected();
                return checkboxState;
            };
        } else {
            throw new Error('Expected filter type not found.');
        }

        await this.processFilterItems({
            items,
            expectedValue,
            tickAction,
            getCheckboxState,
            filterType,
        });
    };

    selectOption = async ({
        list,
        filter,
        defaultFilter = 'Contains',
    }: {
        list: WebdriverIO.ElementArray;
        filter?: string;
        defaultFilter?: string;
    }): Promise<void> => {
        if (filter) {
            for await (const li of list) {
                const text = await li.getText();
                if (text.trim() === filter.trim()) {
                    await li.waitForClickable();
                    await li.moveTo();
                    await li.click();
                    await waitForPromises(300, 'click filter');
                    return;
                }
            }
            throw new Error(`Filter "${filter}" not found`);
        } else {
            for await (const li of list) {
                const text = await li.getText();
                if (text.trim() === defaultFilter.trim()) {
                    await li.waitForClickable();
                    await li.moveTo();
                    await li.click();
                    await waitForPromises(300, 'click filter');
                    return;
                }
            }
            throw new Error(`Filter "${defaultFilter}" not found`);
        }
    };

    filterByErrors = async () => {
        const errBtn = '.e-link-error-numbers button';
        const $errBtn = await this.find(errBtn);
        await utils.waitForElementToExist({ name: 'error button', selector: errBtn });
        await $errBtn.click();
        const filterErrBtn = '.e-button-filter-validation-errors';
        const $filterErrBtn = await this.find(filterErrBtn, true);
        await utils.waitForElementToExist({ name: 'filter error button', selector: filterErrBtn });
        await $filterErrBtn.click();
    };

    unfilterByErrors = async () => {
        const button = '.e-link-back-to-full-display button';
        const $button = await this.find(button);
        await utils.waitForElementToExist({ name: 'back to full display button', selector: button });
        await $button.click();
    };

    private async clickCheckbox(item: WebdriverIO.Element): Promise<void> {
        await item.moveTo();
        await item.$('input').click();
        await waitForPromises(300, 'click checkbox');
    }

    private async processFilterItems({
        items,
        expectedValue,
        tickAction,
        getCheckboxState,
        filterType,
    }: {
        items: WebdriverIO.ElementArray;
        expectedValue: string;
        tickAction: utils.TickStateType;
        getCheckboxState: (item: WebdriverIO.Element) => Promise<boolean>;
        filterType: string;
    }): Promise<void> {
        for await (const item of items) {
            const currentValue = await item.getText();

            if (utils.removeWhiteSpaces(currentValue) === utils.removeWhiteSpaces(expectedValue)) {
                const checkboxState = await getCheckboxState(item);
                const shouldClick =
                    (tickAction === utils.TickState.TICKS && !checkboxState) ||
                    (tickAction === utils.TickState.UNTICKS && checkboxState);

                if (shouldClick) {
                    await this.clickCheckbox(item);
                }
                return;
            }
        }
        throw new Error(`No ${filterType} item found with value: "${expectedValue}"`);
    }

    isFloatingFilterDisplayed = async () => {
        const $floatingFilter = await this.component.find(SELECTORS.floatingFilter);
        return $floatingFilter.isDisplayed();
    };
}
