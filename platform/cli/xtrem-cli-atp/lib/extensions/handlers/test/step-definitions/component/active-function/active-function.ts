/* eslint-disable class-methods-use-this */
// eslint-disable-next-line import/no-extraneous-dependencies
import { getValue, setValue } from '@wdio/shared-store-service';
import * as chalk from 'chalk';
import { atpEnv } from '../../step-definitions-utils';
import * as StaticStore from '../static-store';

export interface ActiveFunction {
    lockEntryCode: string;
    featurePath: string;
}

export interface ActiveFunctionsList extends Array<ActiveFunction> {}

export class ActiveFunctionHelper {
    private activeFunction: ActiveFunction = {
        lockEntryCode: '',
        featurePath: '',
    };

    private activeFunctionsList: ActiveFunctionsList = [];

    getFeaturePath(): string {
        return this.activeFunction.featurePath;
    }

    getFunctionCode(): string {
        return this.activeFunction.lockEntryCode;
    }

    getTimeoutLocks(): number {
        return atpEnv.timeoutLocks;
    }

    setFeaturePath(featurePath: string) {
        this.activeFunction.featurePath = featurePath;
    }

    setLockEntryCode(lockEntryCode: string) {
        this.activeFunction.lockEntryCode = lockEntryCode;
    }

    private async getActiveFunctionsValue(): Promise<string> {
        return (await getValue('activeFunctions'))?.toString() ?? '';
    }

    private async getSharedInstanceIterations() {
        let instanceIterations = 10;
        const maxInstanceIterations = Number(process.env.XTREM_TEST_MAX_INSTANCES ?? 1) * 10;
        const sharedInstanceIterations = <number>await getValue('sharedInstanceIterations');

        if (sharedInstanceIterations) {
            instanceIterations = sharedInstanceIterations + 10;
        }
        if (instanceIterations > maxInstanceIterations) {
            instanceIterations = maxInstanceIterations;
        }
        await setValue('sharedInstanceIterations', instanceIterations);

        return instanceIterations;
    }

    private async setActiveFunctionsList() {
        const activeFunctionsValue = await this.getActiveFunctionsValue();
        this.activeFunctionsList = <ActiveFunction[]>JSON.parse(activeFunctionsValue) || [];
    }

    async checkForActiveLockEntry(lockEntryCode: string) {
        this.setLockEntryCode(lockEntryCode);
        await this.setPauseUnlockedInstance();
        await this.lockEntryNotActiveInInstance(lockEntryCode);
        await this.insert();
    }

    async lockEntryNotActiveInInstance(lockEntryCode: string): Promise<boolean> {
        const instanceIterations = await this.getSharedInstanceIterations();
        const timeout = this.getTimeoutLocks();
        const timeoutInterval = 100;
        let lockIteration = 0;
        let lockNotActiveIteration = 0;

        return <boolean>await browser.waitUntil(
            async () => {
                const lockNotActive = !(await this.lockEntryCodeExists(lockEntryCode));
                lockIteration += 1;

                if (!lockNotActive) {
                    if (lockIteration % 200 === 0) {
                        // eslint-disable-next-line no-console
                        console.log(
                            chalk.blue.bold(
                                `\t ℹ️\t LOCK ENTITY ${lockEntryCode} STILL RUNNING IN ANOTHER INSTANCE! (timing out in ${timeout - lockIteration * timeoutInterval} ms)`,
                            ),
                        );
                    }
                } else {
                    lockNotActiveIteration += 1;
                }

                return lockNotActive && lockNotActiveIteration === instanceIterations;
            },
            {
                timeout,
                timeoutMsg: `Checking for Lock Entity ${lockEntryCode} timed out.`,
                interval: timeoutInterval,
            },
        );
    }

    private async setPauseUnlockedInstance() {
        const sharedInstancePause = <number>await getValue('sharedInstancePause');
        const lowerRange = sharedInstancePause || 10;
        const upperRange = 30;
        const pause = Math.floor(Math.random() * (1 + upperRange - lowerRange)) + lowerRange;

        await setValue('sharedInstancePause', pause);
        await new Promise(resolve => {
            setTimeout(resolve, pause * 100);
        });
    }

    private async lockEntryCodeExists(lockEntryCode: string): Promise<boolean> {
        if (!(await this.getActiveFunctionsValue())) return false;

        await this.setActiveFunctionsList();
        return this.activeFunctionsList.some(af => af.lockEntryCode === lockEntryCode);
    }

    async setLockEntryAfterRemoved(featurePath: string) {
        const currentLockedFunc = StaticStore.getStoredString(StaticStore.StoredKeys.CURRENT_LOCKED_FUNCTION);
        this.setFeaturePath(featurePath);

        if (currentLockedFunc && !(await this.getByLockEntryCodeAndFeaturePath(currentLockedFunc, featurePath))) {
            this.setLockEntryCode(currentLockedFunc);
            await this.insert();
        }
    }

    async insert() {
        if (!this.activeFunctionsList) this.activeFunctionsList = <ActiveFunctionsList>[];
        this.activeFunctionsList.push(this.activeFunction);
        await setValue('activeFunctions', JSON.stringify(this.activeFunctionsList));
    }

    async getByLockEntryCode(lockEntryCode: string): Promise<ActiveFunction | null> {
        let actFunction: ActiveFunction = <ActiveFunction>(<unknown>null);
        if (!(await this.getActiveFunctionsValue())) return null;

        this.activeFunctionsList = <ActiveFunction[]>JSON.parse(await this.getActiveFunctionsValue());
        if (this.activeFunctionsList && this.activeFunctionsList.length > 0) {
            actFunction = <ActiveFunction>this.activeFunctionsList.find(af => af.lockEntryCode === lockEntryCode);
        }
        return actFunction;
    }

    async getByLockEntryCodeAndFeaturePath(lockEntryCode: string, featurePath: string): Promise<ActiveFunction | null> {
        if (!(await this.getActiveFunctionsValue())) return null;

        await this.setActiveFunctionsList();
        return <ActiveFunction>(
            this.activeFunctionsList.find(af => af.lockEntryCode === lockEntryCode && af.featurePath === featurePath)
        );
    }

    async deleteByFunctionCode(lockEntryCode: string) {
        const actFunction = await this.getByLockEntryCode(lockEntryCode);
        if (actFunction) await this.removeFunctionFromListByProperty(actFunction, 'lockEntryCode');
    }

    async deleteByFeaturePath(featurePath: string) {
        const actFunction = await this.getByFeaturePath(featurePath);
        if (actFunction) await this.removeFunctionFromListByProperty(actFunction, 'featurePath');
    }

    async deleteByLockEntryCodeAndFeaturePath(lockEntryCode: string, featurePath: string) {
        if (!(await this.getActiveFunctionsValue())) return;
        let functionIndex = -1;

        this.activeFunctionsList = <ActiveFunction[]>JSON.parse(await this.getActiveFunctionsValue());
        if (!this.activeFunctionsList) return;
        functionIndex = this.activeFunctionsList.findIndex(
            af => af.lockEntryCode === lockEntryCode && af.featurePath === featurePath,
        );
        if (functionIndex > -1) await this.removeItemFromListByIndex(functionIndex);
    }

    private async removeFunctionFromListByProperty(actFunction: ActiveFunction, propName: keyof ActiveFunction) {
        let functionIndex = -1;

        do {
            functionIndex = await this.findItemIndexByPropertyValue(propName, actFunction[propName]);
            if (functionIndex > -1) await this.removeItemFromListByIndex(functionIndex);
        } while (functionIndex > -1);
    }

    private async removeItemFromListByIndex(functionIndex: number, records = 1) {
        await this.setActiveFunctionsList();
        this.activeFunctionsList.splice(functionIndex, records);
        await setValue('activeFunctions', JSON.stringify(this.activeFunctionsList));
    }

    async getByFeaturePath(featurePath: string): Promise<ActiveFunction | null> {
        if (!(await this.getActiveFunctionsValue())) return null;

        await this.setActiveFunctionsList();
        return <ActiveFunction>this.activeFunctionsList.find(af => af.featurePath === featurePath);
    }

    private async findItemIndexByPropertyValue(propName: keyof ActiveFunction, value: any): Promise<number> {
        await this.setActiveFunctionsList();
        return this.activeFunctionsList.findIndex(af => af[propName] === value);
    }
}

export const activeFunction = new ActiveFunctionHelper();
