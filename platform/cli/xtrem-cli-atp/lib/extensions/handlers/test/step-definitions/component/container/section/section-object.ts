import * as utils from '../../../step-definitions-utils';
import { ContainerObject } from '../container-object';

export class SectionContainerObject extends ContainerObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        super({
            containerType: utils.ContainerElementTypes.SECTION,
            identifier: utils.getLookupStrategySelector({
                fieldType: 'e-section',
                lookupStrategy,
                identifier,
                domSelector: 'section',
            }),
            context,
        });
    }
}
