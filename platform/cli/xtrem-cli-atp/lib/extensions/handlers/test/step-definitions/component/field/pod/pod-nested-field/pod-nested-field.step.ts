import { Then, When } from '@cucumber/cucumber';
import * as utils from '../../../../step-definitions-utils';
import pageModel from '../../../main-page';
import * as StaticStore from '../../../static-store';
import { waitForPromises } from '../../wait-util';
import { PodNestedFieldObject } from './pod-nested-field-object';

When(
    /^the user clicks the "(.*)" (bound|labelled) nested switch field of the (vital pod|pod|dynamic-pod) field$/,
    async (fieldIdentifier: string, fieldLookupStrategy: utils.LookupStrategy, podType: utils.FieldTypes) => {
        await pageModel.waitForFinishLoading();
        const field = StaticStore.getStoredField(podType);

        const nestedField = new PodNestedFieldObject({
            identifier: fieldIdentifier,
            lookupStrategy: fieldLookupStrategy,
            fieldType: utils.fieldTypes.switch,
            pod: field,
        });
        await utils.waitForElementToBeFound({
            name: `"${fieldIdentifier}" nested field`,
            selector: nestedField.cssSelector,
        });

        const $field = await field.get();
        const isReadOnly = await $field
            .getAttribute('class')
            .then((classes: string) => classes.split(' ').includes('e-read-only'));
        if (isReadOnly) throw new Error(`The "${fieldIdentifier}" nested field is read-only, cannot click it.`);

        const $nestedField = await nestedField.get();
        const isDisabled = await $nestedField
            .getAttribute('class')
            .then(classes => classes.split(' ').includes('e-disabled'));
        if (isDisabled) throw new Error(`The "${fieldIdentifier}" nested field is disabled, cannot click it.`);

        const element = await nestedField.find(utils.getElementTypeSelector(utils.fieldTypes.switch));
        await element.moveTo();
        await element.click();
        await waitForPromises(500, 'Waiting for click');
    },
);

Then(
    /^the "([^"\n\r]*)" (bound|labelled) nested switch field in the (pod|vital pod|dynamic-pod) field is set to "(.*)"$/,
    async (
        fieldIdentifier: string,
        fieldLookupStrategy: utils.LookupStrategy,
        podType: utils.FieldTypes,
        value: string,
    ) => {
        await pageModel.waitForFinishLoading();
        const field = StaticStore.getStoredField(podType);
        const nestedField = new PodNestedFieldObject({
            identifier: fieldIdentifier,
            lookupStrategy: fieldLookupStrategy,
            fieldType: utils.fieldTypes.switch,
            pod: field,
        });
        await utils.waitForElementToBeFound({
            name: `"${fieldIdentifier}" nested field`,
            selector: nestedField.cssSelector,
        });
        await nestedField.expectPodItemValue(value, utils.fieldTypes.switch);
    },
);

When(
    /^the user clicks in the "(.*)" (bound|labelled) nested (multi dropdown|multi reference) field of the vital pod field$/,
    async (identifier: string, lookupStrategy: utils.LookupStrategy, fieldType: utils.FieldTypes) => {
        const field = StaticStore.getStoredField(utils.fieldTypes.vitalPod);
        const nestedField = new PodNestedFieldObject({ identifier, lookupStrategy, fieldType, pod: field });
        await nestedField.clickMultiField(identifier);
    },
);

When(
    /^the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (button|calendar|checkbox|count|date|relative date|filter select|dynamic-select|icon|label|link|numeric|progress|radio|reference|multi reference|rich text|scan|select|table|text|text area) field of the (pod|dynamic-pod|vital pod) field$/,
    async (
        value: string,
        fieldIdentifier: string,
        fieldLookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        podType: utils.FieldTypes,
    ) => {
        await pageModel.waitForFinishLoading();
        const field = StaticStore.getStoredField(podType);
        const nestedField = new PodNestedFieldObject({
            identifier: fieldIdentifier,
            lookupStrategy: fieldLookupStrategy,
            fieldType,
            pod: field,
        });
        await utils.waitForElementToBeFound({
            name: `"${fieldIdentifier}" nested field`,
            selector: nestedField.cssSelector,
        });

        const fieldElement = await field.get();
        const isReadOnly = await fieldElement
            .getAttribute('class')
            .then((classes: string) => classes.split(' ').includes('e-read-only'));
        if (isReadOnly) throw new Error(`The "${fieldIdentifier}" nested field is read-only, cannot write on it.`);

        const element = await nestedField.get();
        const isDisabled = await element
            .getAttribute('class')
            .then(classes => classes.split(' ').includes('e-disabled'));
        if (isDisabled) throw new Error(`The "${fieldIdentifier}" nested field is disabled, cannot write on it.`);

        await nestedField.setPodItemValue(value, fieldType);
    },
);

When(
    /^the user clears the "(.*)" (bound|labelled) nested (multi dropdown|multi reference) field of the vital pod field$/,
    async (identifier: string, lookupStrategy: utils.LookupStrategy, fieldType: utils.FieldTypes) => {
        const field = StaticStore.getStoredField(utils.fieldTypes.vitalPod);
        const nestedField = new PodNestedFieldObject({ identifier, lookupStrategy, fieldType, pod: field });
        // open the dropdown by clicking on arrow dropdown
        const selector = nestedField.cssSelector;
        const dropdownArrow = await browser.$(`${selector} .e-ui-select-inline-dropdown`);
        await dropdownArrow.scrollIntoView();
        await dropdownArrow.click();
        await nestedField.clearNestedField(fieldType);
        await nestedField.closeList();
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested (button|calendar|checkbox|count|date|filter select|icon|label|link|multi dropdown|multi reference|numeric|progress|radio|reference|rich text|scan|select|dynamic-select|table|text|text area) field in the (pod|dynamic-pod|vital pod) field is "(.*)"$/,
    async (
        fieldIdentifier: string,
        fieldLookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        podType: utils.FieldTypes,
        value: string,
    ) => {
        await pageModel.waitForFinishLoading();
        const field = StaticStore.getStoredField(podType);

        const nestedField = new PodNestedFieldObject({
            identifier: fieldIdentifier,
            lookupStrategy: fieldLookupStrategy,
            fieldType,
            pod: field,
        });
        await utils.waitForElementToBeFound({
            name: `"${fieldIdentifier}" nested field`,
            selector: nestedField.cssSelector,
        });
        await nestedField.expectPodItemValue(value, fieldType);
    },
);

When(
    /^the user selects "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (filter select|reference|multi dropdown|multi reference|select|dynamic-select) field of the (vital pod|pod|dynamic-pod) field$/,
    async (
        value: string,
        fieldIdentifier: string,
        fieldLookupStrategy: utils.LookupStrategy,
        nestedFieldType: utils.FieldTypes,
        podType: utils.FieldTypes,
    ) => {
        await pageModel.waitForFinishLoading();
        const field = StaticStore.getStoredField(podType);
        const nestedField = new PodNestedFieldObject({
            identifier: fieldIdentifier,
            lookupStrategy: fieldLookupStrategy,
            fieldType: nestedFieldType,
            pod: field,
        });
        await utils.waitForElementToBeFound({
            name: `"${fieldIdentifier}" nested field`,
            selector: nestedField.cssSelector,
        });
        await PodNestedFieldObject.selectOptionsInNestedField(nestedField, value, nestedFieldType);
    },
);

Then(
    /^at least the following list of options is displayed "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (multi dropdown|multi reference|reference|select|dynamic-select) field of the (vital pod|pod|dynamic-pod) field$/,
    async (
        expectedOptions: string,
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.FieldTypes,
        podType: utils.FieldTypes,
    ) => {
        const field = StaticStore.getStoredField(podType);
        const nestedField = new PodNestedFieldObject({ identifier, lookupStrategy, fieldType, pod: field });
        await nestedField.selectDropDown.expectOptionsToBe(expectedOptions);
    },
);

When(
    /^the user clicks the "([^"\n\r]*)" action button of nested "([^"\n\r]*)" (bound|labelled) dynamic-select field of the dynamic-pod field$/,
    async (actionButton: string, identifier: string, lookupStrategy: utils.LookupStrategy) => {
        const field = StaticStore.getStoredField(utils.fieldTypes.dynamicPod);
        const nestedField = new PodNestedFieldObject({
            identifier,
            lookupStrategy,
            fieldType: utils.fieldTypes.dynamicSelect,
            pod: field,
        });
        await utils.waitForElementToBeFound({
            name: `"${identifier}" nested field`,
            selector: nestedField.cssSelector,
        });

        const fieldElement = await field.get();
        const isReadOnly = await fieldElement
            .getAttribute('class')
            .then((classes: string) => classes.split(' ').includes('e-read-only'));
        if (isReadOnly)
            throw new Error(`The "${identifier}" nested field is read-only, cannot click the action button.`);

        const element = await nestedField.get();
        const isDisabled = await element
            .getAttribute('class')
            .then(classes => classes.split(' ').includes('e-disabled'));
        if (isDisabled)
            throw new Error(`The "${identifier}" nested field is disabled, cannot click the action button.`);

        await nestedField.clickActionButton(actionButton);
    },
);
