import { Then } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { ProgressFieldObject } from './progress-object';

// ----------
// Static store field steps
// ----------
Then(/^the value of the progress field is "(.*)"$/, async (expectedValue: string) => {
    const field = <ProgressFieldObject>StaticStore.getStoredField(fieldTypes.progress);
    await field.loseFocus();
    await field.expectProgressFieldValue(expectedValue);
});

Then(/^the progress field is (enabled|disabled)$/, async (cssState: 'enabled' | 'disabled') => {
    const field = <ProgressFieldObject>StaticStore.getStoredField(fieldTypes.progress);
    await field.loseFocus();
    await field.expectToBeEnabledClass(`${field.cssSelector}`, cssState === 'disabled');
});
