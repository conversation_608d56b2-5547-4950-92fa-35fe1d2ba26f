import { padStart } from 'lodash';
import { Key } from 'webdriverio';
import { ElementContext, LookupStrategy, takeScreenshot } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { testGlobals } from '../../test-globals';
import {
    DATE_FORMAT_MAP,
    changeDateWithFormat,
    formatDateNth,
    getUserLocale,
    openDatePicker,
    parseDateString,
} from '../date-utils';
import { FieldObject } from '../field-object';

export class DateFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'date', identifier, lookupStrategy, context });
    }

    async generateDate(dateFormat: string): Promise<string> {
        /**
         * XT-66305 - work around for readonly date fields. Once DOM is enhanced to include locale outside of the date
         * picker object then replace with code to select from there.
         * XT-95945 - Open date picker moved to writeDate as locale no longer needs to be retrieved from picker object
         */
        const lang = await getUserLocale();
        const readonlyInput = await this.find('input[readonly]');
        if (!(await readonlyInput.isExisting())) {
            await openDatePicker(this);
        }
        // XT-95945 - If provided with a date string that does not contain separators (e.g. 22042024) don't try to calculate date
        if (/\d{8}/g.test(dateFormat)) {
            return dateFormat;
        }
        const dateField = formatDateNth(dateFormat, lang);
        return changeDateWithFormat(dateField, lang);
    }

    async writeDate({
        content,
        cssSelector = 'input',
        ignoreContext = false,
    }: {
        content: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const lang = await getUserLocale();

        try {
            const dateField = await this.find(cssSelector, ignoreContext);
            await dateField.click();
            await dateField.clearValue();
            if (testGlobals.device !== 'desktop') {
                await dateField.setValue(content);
            } else {
                const arrValue = [...content];
                for (let i = 0; i < arrValue.length; i += 1) {
                    await browser.keys(arrValue[i]);
                }
            }

            let isDayPickerOpen = await (await this.find('.rdp-root', true)).isExisting();

            if (!isDayPickerOpen) {
                await openDatePicker(this);
                isDayPickerOpen = await (await this.find('.rdp-root', true)).isExisting();
            }

            if (isDayPickerOpen) {
                const { day, year, month } = parseDateString(content, lang, /\d{8}/g.test(content));

                // click the selected date so the date picker to closes
                await (await this.find(`.rdp-root [data-day="${year}-${month}-${day}"]`, true)).click();
            }
        } catch (error) {
            throw new Error(`Error writing date: ${error}`);
        }
    }

    override async clearInput({ ignoreContext = false }: { ignoreContext?: boolean }) {
        const cssSelector = 'input';
        await this.expectToAppear({ cssSelector, ignoreContext });
        await this.click(cssSelector, ignoreContext);
        while ((await (await this.find(cssSelector)).getValue()) !== '') {
            await browser.keys(Key.ArrowRight);
            await browser.keys(Key.Backspace);
        }
    }

    // eslint-disable-next-line class-methods-use-this
    private async validateDateState(state: string, dateElement: WebdriverIO.Element | null) {
        if (!dateElement) {
            throw new Error('Expected date could not be found.');
        }
        const attributes = await Promise.all([
            dateElement.getAttribute('disabled'),
            dateElement.parentElement().getAttribute('aria-selected'),
            dateElement.parentElement().getAttribute('data-outside'),
        ]);
        const isDisabled = attributes[0] === '';
        const isSelected = attributes[1] === 'true';
        const isOutsideFocusedMonth = attributes[2] === 'true';

        const stateConditions: Record<string, boolean> = {
            enabled: !isDisabled,
            disabled: isDisabled,
            selected: isSelected,
            'not selected': !isSelected,
            'out of period': isOutsideFocusedMonth,
        };
        if (stateConditions[state]) {
            return true;
        }
        const dayText = await dateElement.parentElement().getAttribute('data-day');
        throw new Error(
            `Expected day "${dayText}" from the date picker to be ${state}.\nSelector: ${dateElement.selector}`,
        );
    }

    async storeDateValueForDayFromDatePicker(firstLast: string, keyValue: string) {
        await openDatePicker(this);
        const dateElements = await this.findAll('.rdp-root td[data-day]', true);
        const dateElement = firstLast === 'first' ? dateElements[0] : dateElements[dateElements.length - 1];
        const dateStr = await dateElement.getAttribute('data-day');
        const [year, month, day] = dateStr.split('-');
        const locale = await getUserLocale();
        const dateFormat = DATE_FORMAT_MAP[locale];
        const formattedDateStr = dateFormat
            .replace('MM', padStart(month, 2, '0'))
            .replace('DD', padStart(day, 2, '0'))
            .replace('YYYY', year);
        StaticStore.storeObject(keyValue, formattedDateStr);
    }

    async expectDatesRangeStateToBe({
        textDateFrom,
        textDateTo,
        state,
    }: {
        textDateFrom: string;
        textDateTo: string;
        state: string;
    }) {
        try {
            const locale = await getUserLocale();

            // eslint-disable-next-line prefer-const
            let newDateFrom = await this.parseAndFormatDate(textDateFrom, locale);
            const newDateTo = await this.parseAndFormatDate(textDateTo, locale);

            if (state !== 'out of period') {
                if (newDateFrom > newDateTo) {
                    throw new Error('Incorrect date range.');
                }
            }

            await openDatePicker(this);
            while (newDateFrom <= newDateTo) {
                const dateStr = this.formatDate(newDateFrom);
                const dateElement = await this.find(`.rdp-root [data-day="${dateStr}"] button`, true);
                await this.validateDateState(state, dateElement);
                newDateFrom.setDate(newDateFrom.getDate() + 1);
            }

            await takeScreenshot();
        } catch (error) {
            await takeScreenshot();
            console.error(error.message);
            throw error;
        }
    }

    // eslint-disable-next-line require-await, class-methods-use-this
    private async parseAndFormatDate(dateValue: string, locale: string): Promise<Date> {
        const { day, month, year } = parseDateString(dateValue, locale);
        return new Date(Number(year), Number(month) - 1, Number(day));
    }

    // eslint-disable-next-line class-methods-use-this
    private formatDate(date: Date): string {
        const year = date.getFullYear();
        const month = padStart(String(date.getMonth() + 1), 2, '0');
        const day = padStart(String(date.getDate()), 2, '0');
        return `${year}-${month}-${day}`;
    }

    async expectDatesStateToBe(dateString: string, state: string) {
        const locale = await getUserLocale();
        try {
            const parsedDate = await this.parseAndFormatDate(dateString, locale);
            const dateStr = this.formatDate(parsedDate);

            await openDatePicker(this);
            const dateElement = await this.find(`.rdp-root [data-day="${dateStr}"] button`, true);
            await this.validateDateState(state, dateElement);

            await takeScreenshot();
        } catch (error) {
            await takeScreenshot();
            // eslint-disable-next-line no-console
            console.error(error.message);
            throw error;
        }
    }

    async clickSelectedDate(date: string) {
        const isDayPickerOpen = await (await this.find('.rdp-root', true)).isExisting();
        if (isDayPickerOpen && date) {
            const selectedDay = await this.find('.rdp-selected', true);
            await selectedDay.click();
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async dateFormat(date: string) {
        const lang = await getUserLocale();
        const { day, month, year } = parseDateString(date, lang);

        const dateFormat = DATE_FORMAT_MAP[lang];

        const formattedDate = dateFormat.replace('MM', month).replace('DD', day).replace('YYYY', year);

        return formattedDate;
    }
}
