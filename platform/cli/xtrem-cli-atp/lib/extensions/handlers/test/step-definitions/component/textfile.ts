import * as fs from 'fs';
import * as path from 'path';
import AbstractPageObject from './abstract-page-object';
import { textFileUtility } from './textfile-utility';

export default class TextFile extends AbstractPageObject {
    private text: string = '';

    public fileName: string;

    getText(): string {
        return this.text;
    }

    // eslint-disable-next-line class-methods-use-this
    async renameFile(fileName: string, filePattern: string) {
        await textFileUtility.renameFile(fileName, filePattern);
    }

    // eslint-disable-next-line class-methods-use-this
    setCSVSeparator(separator: string) {
        textFileUtility.setCSVSeparator(separator);
    }

    public readFile(filePath: string, fileName: string, fileType: string) {
        this.fileName = fileName;
        let dataBuffer;
        try {
            dataBuffer = fs.readFileSync(path.join(filePath, fileName));
        } catch (error) {
            throw new Error(`Expected text file ${path.join(filePath, fileName)} could not be found \n ${error}`);
        }

        try {
            this.text = dataBuffer.toString('utf-8');
        } catch (error) {
            throw new Error(`Expected ${fileType} file ${fileName} could not be found`);
        }
        this.attachAllureFile(`Actual value: ${fileName}`, this.text, 'text');
    }

    // eslint-disable-next-line class-methods-use-this
    public compareTextWithInput(inputText: string, text: string, checkDiff: boolean = false): string {
        const diffsArray: any[] = textFileUtility.extractDifferencesFromText(
            inputText.split('\n'),
            text.split('\n'),
            textFileUtility.getCSVSeparator(),
            checkDiff,
        );

        return textFileUtility.getErrorsList(diffsArray, checkDiff);
    }

    public assertTextInFile(requiredText: string, fileType: string, checkDiff: boolean = false, fileName: string = '') {
        if (!this.text) {
            throw new Error('Text data is not loaded');
        }

        const errorLines = this.compareTextWithInput(requiredText, this.text, checkDiff);
        const numberedText = requiredText
            .split('\n')
            .map((line: any, index: number) => `Line ${index + 1}: ${line}`)
            .join('\n');
        if (checkDiff) {
            this.attachAllureFile(`Expected value ${fileName}`, requiredText, `${fileType}`);
            this.attachAllureFile(`Actual value ${this.fileName}`, this.text, `${fileType}`);
        } else {
            this.attachAllureFile('Expected value', numberedText, 'text/plain');
            this.attachAllureFile(`Actual value ${this.fileName}`, this.text, `${fileType}`);
        }
        if (errorLines) {
            this.attachAllureFile('Error lines', errorLines, 'text/plain');

            throw new Error(`Expected value doesn't match the ${fileType} actual content.`);
        }
    }
}

export const textFile = new TextFile('');
