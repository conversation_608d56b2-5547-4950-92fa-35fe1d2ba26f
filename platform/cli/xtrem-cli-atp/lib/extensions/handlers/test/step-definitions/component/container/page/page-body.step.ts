import { Then } from '@cucumber/cucumber';
import { FieldTypes, LookupStrategy } from '../../../step-definitions-utils';
import pageModel from '../../main-page';

Then(/^scrolls to the "(.*)" (bound|labelled) section$/, async (identifier: string, lookupStrategy: LookupStrategy) => {
    await pageModel.body.scrollToSection(identifier, lookupStrategy);
});

Then(/^scrolls to the "(.*)" (bound|labelled) block$/, async (identifier: string, lookupStrategy: LookupStrategy) => {
    await pageModel.body.scrollToBlock(identifier, lookupStrategy);
});

Then(
    /^scrolls to the "(.*)" (bound|labelled) (button|calendar|checkbox|date|label|numeric|progress|reference|rich text|separator|text|text area) field$/,
    async (identifier: string, lookupStrategy: LookupStrategy, fieldType: FieldTypes) => {
        await pageModel.body.scrollToField({ identifier, lookupStrategy, fieldType });
    },
);
