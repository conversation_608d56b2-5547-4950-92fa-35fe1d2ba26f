/* eslint-disable @typescript-eslint/naming-convention */
import * as fs from 'fs';
import * as path from 'path';
import { ElementContext, getDataTestIdSelector, LookupStrategy } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

enum fileDepositType {
    DOWNLOAD = 'download',
    DELETE = 'delete',
    SELECT_FILE = 'select file',
    CSVFILE = 'csvfile',
}

export class FileDepositFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'file', identifier, lookupStrategy, context });
    }

    async uploadFile(relativeFilePath: string) {
        const parentSelector = (await this.get()).selector.toString();
        const inputFileSelector = getDataTestIdSelector({
            domSelector: 'input',
            dataTestIdValue: 'e-file-deposit-field-upload-area',
        });
        const selector = `${parentSelector} ${inputFileSelector}`;
        const inputFile = await this.find(selector, true);

        await inputFile.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${relativeFilePath}" deposit field.\nSelector: ${selector}`,
        });

        const featurePath = path
            .resolve(process.env.FEATURE_PATH!)
            .substring(0, path.resolve(process.env.FEATURE_PATH!).lastIndexOf('/'));
        const absolutePath = path.resolve(path.join(featurePath, relativeFilePath));

        if (!fs.existsSync(absolutePath)) {
            throw new Error(`Expected file could not be found: ${relativeFilePath}`);
        }

        await browser.execute(`document.querySelector('${inputFileSelector}').classList.remove("e-hidden")`);
        await inputFile.waitForExist();
        await inputFile.setValue(process.platform === 'win32' ? absolutePath : absolutePath.replace(/\\/g, '/'));

        const fileDepositDetails = await this.find(`${parentSelector} [data-component="link"] a`, true);
        await fileDepositDetails.waitForExist();
        await fileDepositDetails.waitForDisplayed();
        await waitForPromises(500, 'file uploaded');
        StaticStore.storeObject(StaticStore.StoredKeys.FILE_DEPOSIT, fileDepositDetails);
    }

    async clickDeleteFileButton() {
        const selector = (await this.get()).selector.toString();
        const deleteButtonSelector = `${selector} button[type="button"]`;
        const deleteButton = await this.find(deleteButtonSelector, true);
        await deleteButton.waitForDisplayed();
        await deleteButton.click();
    }

    async clickFileButton(identifier: string) {
        if (!identifier.includes('.csv')) {
            switch (identifier) {
                case fileDepositType.DOWNLOAD:
                    await this.click('[data-component="link"] a');
                    break;
                case fileDepositType.DELETE:
                    await this.click('button[type="button"]');
                    break;
                default:
                    throw new Error(
                        `Expected element could not be found: "${identifier}".\nSelector: ${this.cssSelector}`,
                    );
            }
        } else {
            await this.click(`[data-component="link"] a`);
        }
    }
}
