import { getDataTestIdSelector } from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';

export class SiteMapObject extends AbstractPageObject {
    constructor() {
        super(getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'sitemap-page' }));
    }

    async expectSiteMapToBeDisplayed(menuName: string, cssState: 'displayed' | 'hidden') {
        const menuitem = await this.find('.se-navigation-menu-button');
        const selectorToUse = this.getSelectorForOperation(this.cssSelector, true);

        try {
            await menuitem.waitForDisplayed({ timeout: this.timeoutWaitFor });
        } catch (error) {
            throw new Error(`The "${menuName}" menu button could not be found.\nSelector: ${selectorToUse}`);
        }

        const menus = await this.findAll('.se-navigation-menu-button-title', true);
        let menuFound = false;

        // eslint-disable-next-line no-restricted-syntax
        for (const menu of menus) {
            // eslint-disable-next-line no-await-in-loop
            const menuTxt = await menu.getText();
            if (menuTxt === menuName) {
                menuFound = true;
                if (cssState === 'displayed') {
                    // eslint-disable-next-line no-await-in-loop
                    await this.checkState({ cssState, reverse: false, cssSelector: menu.selector.toString() });
                } else if (cssState === 'hidden') {
                    // eslint-disable-next-line no-await-in-loop
                    await this.checkState({ cssState, reverse: true, cssSelector: menu.selector.toString() });
                }
            }
        }

        if (!menuFound && cssState === 'displayed') {
            throw new Error(`The "${menuName}" menu button could not be found.\nSelector: ${selectorToUse}`);
        }
    }

    async checkState({
        cssState,
        reverse = false,
        cssSelector,
        ignoreContext = false,
    }: {
        cssState: string;
        reverse?: boolean;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        try {
            await browser.waitUntil(
                async () => {
                    const isDisplayed: any = await browser.execute(
                        `return document.querySelector('${selectorToUse}') !== null;`,
                    );
                    return (isDisplayed && !reverse) || (!isDisplayed && reverse);
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Expected element to be ${cssState}.\nSelector: ${selectorToUse}`);
        }
    }
}
