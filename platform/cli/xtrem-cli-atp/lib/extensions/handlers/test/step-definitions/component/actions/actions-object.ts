import { camelCase } from 'lodash';
import { expectElementToBeDisplayed } from '../../step-definitions-utils';
import AbstractPageObject from '../abstract-page-object';

export class Actions extends AbstractPageObject {
    constructor() {
        super('.xe-actions');
    }

    async expectNotificationsIconDisplayed(state: string, reverse: boolean = false) {
        const notificationState = camelCase(state);
        const notificationIcon = await this.find(`[data-testid="xe-notification-${notificationState}"]`);
        await expectElementToBeDisplayed({
            selector: notificationIcon.selector.toString(),
            reverse,
        });
    }

    async expectProfilePictureIconDisplayed(reverse: boolean = false) {
        const profileIcon = await this.find('button.xe-profile-picture');
        await expectElementToBeDisplayed({ selector: profileIcon.selector.toString(), reverse });
    }

    async clickProfileIcon() {
        const profileIcon = await this.find('button.xe-profile-picture');
        await profileIcon.click();
    }

    async expectProfileDisplayed(reverse: boolean = false) {
        const profile = await this.find('.xe-profile-action.xe-profile-profile.xe-opened');
        await expectElementToBeDisplayed({ selector: profile.selector.toString(), reverse });
    }
}
