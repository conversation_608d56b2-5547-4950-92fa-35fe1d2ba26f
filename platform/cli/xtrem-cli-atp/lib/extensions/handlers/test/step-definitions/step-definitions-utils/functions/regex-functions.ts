import { regex_urls } from '../index';

export const regex_handheld = (url: string) => {
    return url.match(regex_urls);
};

export const regex_handheld_home = (url: string) => {
    return (
        (url.match(regex_urls) && url.substr(url.length - 9) === '/handheld') ||
        url.substr(url.length - 9) === 'handheld/'
    );
};

export const regex_handheld_settings = (url: string) => {
    const match = url.match(regex_urls);
    return match && (match[3] === '/settings' || match[3] === 'settings/');
};
