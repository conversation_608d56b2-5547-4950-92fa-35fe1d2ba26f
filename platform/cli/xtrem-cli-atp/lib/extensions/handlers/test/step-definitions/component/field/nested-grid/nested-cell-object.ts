import { trimEnd } from 'lodash';
import { Key } from 'webdriverio';
import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import * as StaticStore from '../../static-store';
import { SelectDropDownObject } from '../select-dropdown-object';
import { waitForPromises } from '../wait-util';
import { NestedGridObject, SELECTORS } from './nested-grid-object';
import { getRowAttributes, SCROLL_STEP, scrollToLeft } from './nested-grid-utils';

const getFieldSelector = () => {
    const field = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
    return field.cssSelector;
};
export class NestedCellObject extends AbstractPageObject {
    public readonly selectDropDown: SelectDropDownObject;

    constructor({
        columnName,
        lookupStrategy,
        isPinnedToTop = false,
    }: {
        columnName: string;
        lookupStrategy: utils.LookupStrategy;
        isPinnedToTop?: boolean;
    }) {
        super(
            !isPinnedToTop
                ? `${getFieldSelector()} .ag-center-cols-container ${getRowAttributes()} ${utils.getTableNestedFieldSelector(
                      { lookupStrategy, identifier: columnName },
                  )}`
                : `${getFieldSelector()} ${<NestedGridObject>StaticStore.getStoredObject(StaticStore.StoredKeys.NESTED_GRID_PHANTOM_ROW_SELECTOR)} ${utils.getTableNestedFieldSelector(
                      { lookupStrategy, identifier: columnName },
                  )}`,
        );
        this.selectDropDown = new SelectDropDownObject(this.cssSelector);
    }

    async scrollUntilExists() {
        const scrollBarContainer = await $(SELECTORS.scrollContainer);

        let scrollWidth = 0;

        if (await scrollBarContainer.isExisting()) {
            scrollWidth = (await browser.getElementRect(scrollBarContainer.elementId)).width;
        }

        for (let index = 0; index <= scrollWidth; index += SCROLL_STEP) {
            if (await (await $(this.cssSelector)).isExisting()) {
                let xx = 0;
                if (await scrollBarContainer.isExisting())
                    xx = (await browser.getElementRect(scrollBarContainer.elementId)).x;
                await scrollToLeft(
                    SELECTORS.scroll,
                    (await browser.getElementRect(await $(this.cssSelector).elementId)).x - xx,
                );
                await waitForPromises(500, 'Display field');
                return;
            }
            await scrollToLeft(SELECTORS.scroll, index);
        }
        throw new Error(`No nested cell object found:${this.cssSelector}`);
    }

    setNestedCellValue = async ({
        value,
        fieldType,
        columnName,
    }: {
        value: string;
        fieldType: utils.NestedFieldTypes;
        columnName: string;
    }) => {
        const element = await this.get();

        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${columnName}" nested field.\nSelector: ${this.cssSelector}`,
        });

        await browser.execute(elem => elem.scrollIntoView(), element);
        await browser.execute(`document.querySelector('${element.selector}').click()`)
        await browser.execute(`document.querySelector('${element.selector}').click()`)

        switch (fieldType) {
            case utils.nestedFieldTypes.date:
            case utils.nestedFieldTypes.numeric:
            case utils.nestedFieldTypes.text:
                await browser.keys([Key.Ctrl, 'a']);
                await browser.keys(Key.Backspace);
                await browser.keys(value);
                await browser.keys(Key.Enter);
                await waitForPromises(100, 'value changed in simple field');
                break;
            case utils.nestedFieldTypes.dropdownList:
            case utils.nestedFieldTypes.reference: {
                const fieldTestId = await (await element.$('span[data-testid]')).getAttribute('data-testid');

                if (!(await (await $(`div[data-testid=${fieldTestId}]`)).isExisting())) {
                    await browser.keys(Key.Enter);
                }

                await browser.keys([Key.Ctrl, 'a']);
                await browser.keys(Key.Backspace);
                await browser.keys(value);
                await waitForPromises(300, 'value changed in complex field');

                const dropDown = await $(
                    `${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: `${fieldTestId}` })} div`,
                );
                const dropDownRef = await dropDown.getAttribute('aria-owns');

                await this.selectDropDown.selectOption(value, dropDownRef.toString());
                break;
            }
            default:
                break;
        }
    };

    expectNestedCellValue = async ({
        expectedValue,
        fieldType,
        columnName,
        cssSelector = 'div',
        ignoreContext = false,
    }: {
        expectedValue: string;
        fieldType: utils.NestedFieldTypes;
        columnName?: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) => {
        switch (fieldType) {
            case utils.nestedFieldTypes.checkbox:
                await this.expectCheckedValue(expectedValue);
                break;
            case utils.nestedFieldTypes.numeric:
            case utils.nestedFieldTypes.text:
            case utils.nestedFieldTypes.date:
            case utils.nestedFieldTypes.dropdownList:
            case utils.nestedFieldTypes.label:
            case utils.nestedFieldTypes.relativeDate:
            case utils.nestedFieldTypes.reference: {
                try {
                    await this.expectTextContent({
                        toBe: expectedValue,
                        ignoreCase: false,
                        cssSelector,
                        ignoreContext,
                    });
                } catch (error) {
                    throw new Error(
                        `Expected element could not be found: "${columnName}" nested field.\nSelector: ${this.cssSelector}\n${error}`,
                    );
                }
                break;
            }
            case utils.nestedFieldTypes.progress:
                await this.expectNestedProgressValue(expectedValue);
                break;
            default:
                throw new Error(`Invalid fieldType type: ${fieldType}`);
        }
    };

    expectNestedProgressValue = async (expectedValue: string) => {
        const element = await browser.$(`${this.cssSelector} [data-element="current-progress-label"]`);
        const trimmedToBe = trimEnd(expectedValue, '%').trim();
        let trimmedResult = '';

        await utils.waitForElementToBeFound({ name: 'nested progress bar', selector: this.cssSelector });
        await this.scrollTo({ selector: element.selector as string, ignoreContext: true });

        try {
            await browser.waitUntil(
                async () => {
                    const result = await element.getText();
                    trimmedResult = trimEnd(result, '%').trim();
                    return (trimmedResult || '') === (trimmedToBe || '');
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Expected value: ${trimmedToBe}, actual: ${trimmedResult}.\nSelector: ${element.selector}`);
        }
    };

    async getNestedValue(fieldType: utils.NestedFieldTypes): Promise<string> {
        try {
            await this.scrollTo({ selector: this.cssSelector.toString(), ignoreContext: true });
            await waitForPromises(500, 'waiting for scroll to element');
        } catch (error) {
            throw new Error(`Could not scroll to element: ${this.cssSelector}`);
        }

        const element = await this.get();
        const elementValue =
            fieldType === utils.fieldTypes.progress ? await element.getValue() : await element.getText();

        return elementValue;
    }
}
