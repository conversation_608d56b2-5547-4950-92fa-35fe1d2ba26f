/* eslint-disable no-param-reassign */
import * as Diff from 'diff';
import * as fs from 'fs';
import { glob } from 'glob';
import { atpEnv } from '../step-definitions-utils';
import AbstractPageObject from './abstract-page-object';
import * as StaticStore from './static-store';
import path = require('path');

export type LineComparisonResult = {
    lineNumber: number;
    original: string;
    modified: string;
    differences: ResultEntry[];
};

export type ResultEntry = {
    expectedWord: string;
    originalWord: string;
    removedWord?: string;
    similarity: number;
};

const PHRASE_SIMILARITY_THRESHOLD = 70;
const WORD_SIMILARITY_THRESHOLD = 60;
const MINIMUM_WORDS_SIMILARITY_THRESHOLD = 4;
let CSV_SEPARATOR = ';';
export default class TextFileUtility extends AbstractPageObject {
    // eslint-disable-next-line class-methods-use-this
    getCSVSeparator(): string | undefined {
        return CSV_SEPARATOR;
    }

    // eslint-disable-next-line class-methods-use-this
    setCSVSeparator(separator: string | undefined) {
        if (separator) {
            CSV_SEPARATOR = separator;
        }
    }

    async renameFile(fileName: string, filePattern: string) {
        const fileNameStored = StaticStore.getUserdefinedKeyValueFromStore(fileName);
        const filePatternStored = StaticStore.getUserdefinedKeyValueFromStore(filePattern);
        const filePath = `${atpEnv.downloadFolder}/${filePatternStored}`;
        const foundFiles = await glob(filePath);
        if (foundFiles.length === 0) {
            throw new Error(`Expected file ${filePatternStored} could not be found`);
        }
        const latestFile = this.findLatestFile(foundFiles);
        fs.rename(latestFile, path.join(`${atpEnv.downloadFolder}`, `${fileNameStored}`), err => {
            if (err) console.log(err);
        });
    }

    // eslint-disable-next-line class-methods-use-this
    public normalizeText(text: string): string {
        return (
            text
                .replace(/ {2,}/g, ' ')
                .replace(/\n\s*\n/g, '\n')
                // eslint-disable-next-line @sage/redos/no-vulnerable
                .replace(/\s+\n/g, '\n')
                .trim()
        );
    }

    // eslint-disable-next-line class-methods-use-this
    public removeExactOrPartialEntriesFromArrays(
        inputLines: string[],
        textLines: string[],
        exactMatch = false,
    ): { filteredInputLines: { line: number; text: string }[]; filteredTexLines: string[] } {
        let filteredInputLines = [];
        const textTrimmedArray = textLines.map(entry => entry.trim());

        if (!exactMatch) {
            filteredInputLines = inputLines
                .map((entry, index) => ({ line: index + 1, text: entry })) // Include original line numbers
                .filter(({ text }) => {
                    const trimmedText = text.trim();
                    return !textTrimmedArray.some(
                        trimmedLineInTexArray =>
                            trimmedText === trimmedLineInTexArray || trimmedLineInTexArray.includes(trimmedText),
                    );
                });
        } else {
            const setTexArray = new Set(textTrimmedArray);
            filteredInputLines = inputLines
                .map((entry, index) => ({ line: index + 1, text: entry }))
                .filter(({ text }) => !setTexArray.has(text.trim()));
        }

        const trimmedArray1 = inputLines.map(entry => entry.trim());
        const setArray1 = new Set(trimmedArray1);
        const filteredTexLines = textLines.filter(entry => !setArray1.has(entry.trim()));

        return { filteredInputLines, filteredTexLines };
    }

    // Levenshtein distance function
    // eslint-disable-next-line class-methods-use-this
    private calculateLevenshteinDistance(source: string, target: string): number {
        const sourceLength = source.length;
        const targetLength = target.length;

        const distanceMatrix: number[][] = Array.from(Array(sourceLength + 1), () => Array(targetLength + 1).fill(0));

        for (let i = 0; i <= sourceLength; i += 1) {
            distanceMatrix[i][0] = i;
        }
        for (let j = 0; j <= targetLength; j += 1) {
            distanceMatrix[0][j] = j;
        }

        for (let i = 1; i <= sourceLength; i += 1) {
            for (let j = 1; j <= targetLength; j += 1) {
                if (source[i - 1] === target[j - 1]) {
                    distanceMatrix[i][j] = distanceMatrix[i - 1][j - 1];
                } else {
                    distanceMatrix[i][j] =
                        Math.min(
                            distanceMatrix[i - 1][j], // Deletion
                            distanceMatrix[i][j - 1], // Insertion
                            distanceMatrix[i - 1][j - 1], // Substitution
                        ) + 1;
                }
            }
        }

        return distanceMatrix[sourceLength][targetLength];
    }

    // Similarity function based on Levenshtein distance
    private calculateWordsSimilarity(source: string, target: string): number {
        const levenshteinDist = this.calculateLevenshteinDistance(source, target);
        const maxLength = Math.max(source.length, target.length);
        return maxLength === 0 ? 1 : 1 - levenshteinDist / maxLength;
    }

    // Main function to find similar word pairs independently of position
    private findSimilarWords(
        inputWords: string[],
        textWords: string[],
    ): { similarityPercentage: number; results: ResultEntry[] } {
        const missingWords: string[] = [];
        const results: ResultEntry[] = [];
        const missingResults: ResultEntry[] = [];
        let matchedWords = 0;

        // Search for exact matches
        // eslint-disable-next-line no-restricted-syntax
        for (const expectedWord of inputWords) {
            const wordIndex = textWords.indexOf(expectedWord);
            if (wordIndex > -1) {
                textWords.splice(wordIndex, 1);
                matchedWords += 1;
            } else {
                missingWords.push(expectedWord);
            }
        }

        // Search for similar words
        // eslint-disable-next-line no-restricted-syntax
        for (const expectedWord of missingWords) {
            const { maxWordSimilarity, mostSimilarWord, mostSimilarWordIndex } = this.findMostSimilarWord(
                expectedWord,
                textWords,
            );
            if (maxWordSimilarity > 0) {
                results.push({
                    expectedWord,
                    originalWord: mostSimilarWord,
                    similarity: maxWordSimilarity,
                });
                matchedWords += maxWordSimilarity;
                textWords.splice(mostSimilarWordIndex, 1);
            } else {
                missingResults.push({
                    expectedWord,
                    originalWord: 'Non-existent element',
                    similarity: 0,
                });
            }
        }

        let similarityPercentage = (matchedWords * 100) / inputWords.length;
        similarityPercentage = Math.round(similarityPercentage * 100) / 100;
        if (this.isSimilarPercentage(similarityPercentage, inputWords.length)) {
            results.push(...missingResults);
        }
        return { similarityPercentage, results };
    }

    // eslint-disable-next-line class-methods-use-this
    private isSimilarPercentage(similarityPercentage: number, totalWords: number): boolean {
        return (
            similarityPercentage >= PHRASE_SIMILARITY_THRESHOLD ||
            (totalWords < MINIMUM_WORDS_SIMILARITY_THRESHOLD &&
                similarityPercentage >=
                    PHRASE_SIMILARITY_THRESHOLD - (MINIMUM_WORDS_SIMILARITY_THRESHOLD - totalWords) * 10)
        );
    }

    private findMostSimilarWord(
        expectedWord: string,
        textWords: string[],
    ): { maxWordSimilarity: number; mostSimilarWord: string; mostSimilarWordIndex: number } {
        let maxWordSimilarity = 0;
        let mostSimilarWord = '';
        let mostSimilarWordIndex = -1; // Not found

        // eslint-disable-next-line no-restricted-syntax
        for (const originalWord of textWords) {
            const similarity = this.calculateWordsSimilarity(expectedWord, originalWord);
            if (similarity >= WORD_SIMILARITY_THRESHOLD / 100 && similarity < 1 && similarity > maxWordSimilarity) {
                maxWordSimilarity = similarity;
                mostSimilarWord = originalWord;
                mostSimilarWordIndex = textWords.indexOf(originalWord);
            }
        }

        return { maxWordSimilarity, mostSimilarWord, mostSimilarWordIndex };
    }

    // eslint-disable-next-line class-methods-use-this
    public getErrorsList(diffsArray: any[], checkDiff: boolean = false, showMostSimilar: boolean = false): string {
        return diffsArray
            .map(item => {
                if (item.differences.length > 0) {
                    const wordDifferences = item.differences
                        .map((diff: { similarity: number; expectedWord: any; originalWord: any; removedWord: any }) => {
                            if (diff.similarity > 0 || diff.removedWord) {
                                return `    - Expected: ${diff.expectedWord} ${diff.removedWord ? `Actual: ${diff.removedWord} ${showMostSimilar ? `Most similar: ${diff.originalWord}` : ``} ` : `Actual: ${diff.originalWord}`}\n`;
                            }
                            return `    - ${diff.expectedWord} - ${diff.originalWord}  ${diff.removedWord ? `Actual: ${diff.removedWord}` : ''}\n`;
                        })
                        .join('');
                    return `\nError line ${item.lineNumber}: ${item.modified}\n\n${wordDifferences}`;
                }
                if (checkDiff)
                    return item.lineNumber
                        ? `\nError line ${item.lineNumber}: \n Expected: ${item.modified} \n Actual: ${item.original}\n`
                        : `\nError: \n Expected: ${item.modified} \n Actual: ${item.original}\n`;

                return `\nError line ${item.lineNumber}: ${item.modified} \n ${item.original}`;
            })
            .join('');
    }

    public extractDifferencesFromText(
        inputLines: string[],
        textLines: string[],
        separator: string = '',
        checkDiff: boolean = false,
    ): LineComparisonResult[] {
        const modifications: LineComparisonResult[] = [];

        if (checkDiff) {
            const expectedLines = inputLines;
            const actualLines = textLines;
            return searchDiff(expectedLines, actualLines);
        }

        const filteredArrays = this.removeExactOrPartialEntriesFromArrays(inputLines, textLines, checkDiff);

        filteredArrays.filteredInputLines.forEach(inputLineSel => {
            const inputLine = inputLineSel.text.trim();
            let maxPhraseSimilarity = 0;
            let mostSimilarPhrase = '';
            let mostSimilarPhraseResults: ResultEntry[] = [];

            // eslint-disable-next-line no-restricted-syntax
            for (const textLine of filteredArrays.filteredTexLines) {
                const trimmedTextLine = textLine.trim();
                const inputLineWords = inputLine.split(new RegExp(`[\\s${separator}]`, 'g')).filter(n => n);
                const totalInputWords = inputLineWords.length;

                let similarWords = this.findSimilarWords(
                    inputLineWords,
                    trimmedTextLine.split(new RegExp(`[\\s${separator}]`, 'g')).filter(n => n),
                );

                if (
                    this.isSimilarPercentage(similarWords.similarityPercentage, totalInputWords) &&
                    similarWords.similarityPercentage > maxPhraseSimilarity
                ) {
                    maxPhraseSimilarity = similarWords.similarityPercentage;
                    mostSimilarPhrase = trimmedTextLine;

                    similarWords = searchDiffsimilarWord(textLine, inputLine, similarWords);
                    mostSimilarPhraseResults = similarWords.results;
                }
            }

            if (maxPhraseSimilarity > 0) {
                if (mostSimilarPhraseResults.length === 0) {
                    modifications.push({
                        lineNumber: inputLineSel.line,
                        original: '\n    Non-existent line\n',
                        modified: inputLine,
                        differences: [],
                    });
                } else {
                    if (mostSimilarPhrase.split(/\s+/).length > inputLine.split(/\s+/).length) {
                        mostSimilarPhraseResults.push({
                            expectedWord: inputLine,
                            originalWord: '\n    Non-existent line\n',
                            similarity: 0,
                        });
                    }
                    modifications.push({
                        lineNumber: inputLineSel.line,
                        original: mostSimilarPhrase,
                        modified: inputLine,
                        differences: mostSimilarPhraseResults,
                    });
                }
            } else {
                modifications.push({
                    lineNumber: inputLineSel.line,
                    original: '\n    Non-existent line\n',
                    modified: inputLine,
                    differences: [],
                });
            }
        });

        return modifications;
    }
}

export const textFileUtility = new TextFileUtility('');

function searchDiffsimilarWord(
    textLine: string,
    inputLine: string,
    similarWords: { similarityPercentage: number; results: ResultEntry[] },
) {
    const tokenizer = function (value: string) {
        // This regex will split by semicolumns and keep the semicolumns in the result
        const sep = CSV_SEPARATOR;
        return value.split(new RegExp(`(${sep}|\\s)`)).filter(token => token.length > 0);
    };
    const diff = Diff.diffArrays(tokenizer(textLine), tokenizer(inputLine), { oneChangePerToken: false });

    const results: { expectedWord: any; originalWord: any; similarity: any; removedWord: any }[] = [];
    for (let i = 0; i < diff.length; i += 1) {
        const part = diff[i];

        let prevPart;
        if (i > 0) prevPart = diff[i - 1];
        else prevPart = undefined;

        let nextPart;
        if (i < diff.length - 1) nextPart = diff[i + 1];
        else nextPart = undefined;

        if (part.added && i === diff.length - 1) {
            results.push({
                expectedWord: part.value,
                originalWord: 'Non-existent element',
                similarity: 0,
                removedWord: undefined,
            });
        } else if (part.added) {
            if (!prevPart || (!prevPart.removed && !prevPart.added)) {
                results.push({
                    expectedWord: Array.isArray(part.value) ? part.value.join('') : part.value,
                    originalWord: 'Non-existent element',
                    similarity: 0,
                    removedWord: undefined,
                });
            } else if (prevPart && prevPart.removed) {
                results.push({
                    expectedWord: Array.isArray(part.value) ? part.value.join('') : part.value,
                    originalWord: Array.isArray(prevPart.value) ? prevPart.value.join('') : prevPart.value,
                    removedWord: Array.isArray(prevPart.value) ? prevPart.value.join('') : prevPart.value,
                    similarity: 0,
                });
            }
        } else if (part.removed && nextPart && !nextPart.added) {
            if (prevPart) {
                /* results.push({
                    expectedWord: 'Non-existent element',
                    originalWord: part.value,
                    similarity: 0,
                    removedWord: undefined,
                });
                */
            }
        }
    }
    if (results.length > 0) similarWords.results = results;
    return similarWords;
}

function searchDiff(expectedLines: string[], actualLines: string[]): LineComparisonResult[] {
    const modifications: LineComparisonResult[] = [];
    if (expectedLines.length !== actualLines.length) {
        modifications.push({
            lineNumber: 0,
            // eslint-disable-next-line prefer-template
            original: actualLines.length + ' lines',
            // eslint-disable-next-line prefer-template
            modified: expectedLines.length + ' lines',
            differences: [],
        });
    }

    for (let index = 0; index < expectedLines.length; index += 1) {
        if (index > actualLines.length - 1) {
            modifications.push({
                lineNumber: index + 1,
                original: 'Non-existing line\n',
                modified: expectedLines[index],
                differences: [],
            });

            // eslint-disable-next-line no-continue
            continue;
        }
        const diff = Diff.diffWordsWithSpace(expectedLines[index], actualLines[index]);
        if (index < expectedLines.length - 1 && index < actualLines.length - 1)
            if (expectedLines[index] === actualLines[index])
                // if lines are the same go directly to next index loop
                // eslint-disable-next-line no-continue
                continue;

        let actualWord = '';
        let expectedWord = '';
        const differences: ResultEntry[] = [];

        // Process the differences and pair removals with additions intelligently
        for (let i = 0; i < diff.length; i += 1) {
            const part = diff[i];
            if (part.removed) {
                // if part removed and next part added it can be paired
                if (i + 1 < diff.length && diff[i + 1].added) {
                    differences.push({
                        expectedWord: part.value,
                        originalWord: diff[i + 1].value,
                        similarity: 1,
                    });

                    // Accumulate for the whole line comparison
                    actualWord += part.value;
                    expectedWord += diff[i + 1].value;

                    // Skip the next part as we've already processed it
                    i += 1;
                } else {
                    // Removal but no addition next
                    differences.push({
                        expectedWord: part.value,
                        originalWord: 'Non-existent element',
                        similarity: 0,
                    });
                    actualWord += part.value;
                }
            } else if (part.added) {
                // Addition with no removal before (already processed by condition above)
                differences.push({
                    expectedWord: 'Non-existent element',
                    originalWord: part.value,
                    similarity: 0,
                });
                expectedWord += part.value;
            } else {
                // Unchanged text
                actualWord += part.value;
                expectedWord += part.value;
            }
        } // Add the modifications for this line
        if (expectedLines[index] !== actualLines[index]) {
            if (!expectedLines[index].trim()) {
                modifications.push({
                    lineNumber: index + 1,
                    modified: actualLines[index],
                    original: 'Non-existing lines',
                    differences: [],
                });
            } else if (!actualLines[index].trim()) {
                modifications.push({
                    lineNumber: index + 1,
                    modified: 'Non-existing lines',
                    original: expectedLines[index],
                    differences: [],
                });
            } else if (actualWord || expectedWord) {
                modifications.push({
                    lineNumber: index + 1,
                    original: expectedWord || 'Non-existing line',
                    modified: actualWord || 'Non-existing line',
                    differences,
                });
            }
        }
    }
    return modifications;
}
