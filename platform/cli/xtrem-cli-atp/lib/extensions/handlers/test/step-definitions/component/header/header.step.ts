import { DataTable, Then, When } from '@cucumber/cucumber';
import { ElementContext } from '../../step-definitions-utils';
import pageModel from '../main-page';
import * as StaticStore from '../static-store';

When(
    /^selects the "(.*)" labelled navigation anchor on (the main page|a modal|a full width modal|the sidebar)$/,
    async (anchorLabel: string, context: ElementContext) => {
        await pageModel.waitForFinishLoading();
        await pageModel.header.dismissAllNotification();
        await pageModel.header.selectNavigationAnchor(anchorLabel, context);
    },
);

Then(/^the "(.*)" labelled navigation anchor is selected$/, async (anchorLabel: string) => {
    await pageModel.header.dismissAllNotification();
    await pageModel.header.expectNavigationAnchorSelected(anchorLabel);
});

Then(/^the "(.*)" titled page is displayed$/, async (value: string) => {
    await pageModel.waitForFinishLoading();
    const storedValue = StaticStore.getUserdefinedKeyValueFromStore(value);
    await pageModel.header.expectPageTitle(storedValue);
});

Then(/^the "(.*)" subtitled page is displayed$/, async (value: string) => {
    await pageModel.waitForFinishLoading();
    const storedValue = StaticStore.getUserdefinedKeyValueFromStore(value);
    await pageModel.header.expectPageSubTitle(storedValue);
});

Then(/^the titled page containing "(.*)" is displayed$/, async (value: string) => {
    await pageModel.waitForFinishLoading();
    const storedValue = StaticStore.getUserdefinedKeyValueFromStore(value);
    await pageModel.header.expectPageTitle(storedValue, true);
});

Then(/^the subtitled page containing "(.*)" is displayed$/, async (value: string) => {
    await pageModel.waitForFinishLoading();
    const storedValue = StaticStore.getUserdefinedKeyValueFromStore(value);
    await pageModel.header.expectPageSubTitle(storedValue, true);
});

When(/^the "(.*)" titled sidebar is displayed?$/, async (sbIdentifier: string) => {
    await pageModel.waitForFinishLoading();
    await pageModel.header.expectSidebarTitle(sbIdentifier);
});

When(/^the user clicks the "(.*)" icon in the header on the main page$/, async (name: string) => {
    await pageModel.header.dismissAllNotification();
    await pageModel.header.clickHeaderIcon(name);
});

Then(
    /^the "(.*)" navigation arrow button in the header on the main page is (displayed|hidden)$/,
    async (actionName: 'previous' | 'next' | 'return', cssState: 'displayed' | 'hidden') => {
        await pageModel.waitForFinishLoading();
        await pageModel.header.expectNavigationArrowButtonToBeDisplayed(actionName, cssState === 'hidden');
    },
);

Then(
    /^the "(.*)" navigation arrow button in the header on the main page is (enabled|disabled)$/,
    async (actionName: 'previous' | 'next', cssState: 'enabled' | 'disabled') => {
        await pageModel.waitForFinishLoading();
        await pageModel.header.expectNavigationArrowButtonToBeEnabled(actionName, cssState === 'disabled');
    },
);

Then(
    /^the user clicks the "(.*)" navigation arrow button in the header on the main page$/,
    async (actionName: string) => {
        await pageModel.waitForFinishLoading();
        await pageModel.header.clickNavigationArrowButton(actionName);
    },
);

Then(/^the user clicks the "(.*)" labelled button in the header$/, async (actionLabel: string) => {
    await pageModel.waitForFinishLoading();
    await pageModel.header.dismissAllNotification();
    await pageModel.header.clickQuickAction(actionLabel);
});

Then(/^the user clicks the 360 view switch in the header$/, async () => {
    await pageModel.waitForFinishLoading();
    await pageModel.header.dismissAllNotification();
    await pageModel.header.clickHeader360ViewSwitch();
});

Then(/^the 360 view switch in the header is (ON|OFF|disabled)$/, async (state: string) => {
    await pageModel.waitForFinishLoading();
    await pageModel.header.dismissAllNotification();
    await pageModel.header.expect360ViewSwitchState(state);
});

Then('the user selects the header section toggle button in the header', async () => {
    await pageModel.waitForFinishLoading();
    await pageModel.header.clickHeaderSectionToggleButton();
});

Then(
    /^the "(.*)" labelled button in the header is (enabled|disabled)$/,
    async (actionLabel: string, cssState: 'enabled' | 'disabled') => {
        await pageModel.waitForFinishLoading();
        await pageModel.header.expectQuickActionToBeEnabled(actionLabel, cssState === 'disabled');
    },
);

Then(
    /^the "(.*) labelled button in the header is (displayed|hidden)$/,
    async (actionLabel: string, cssState: 'displayed' | 'hidden') => {
        await pageModel.waitForFinishLoading();
        await pageModel.header.expectQuickActionToBeDisplayed(actionLabel, cssState === 'hidden');
    },
);

When(/^the user clicks the more actions button in the header$/, async () => {
    await pageModel.waitForFinishLoading();
    await pageModel.header.dismissAllNotification();
    await pageModel.header.dropDownMenu.clickMoreActionsMenu();
});

Then(/^the header actions dropdown menu elements are:$/, async (table: DataTable) => {
    const dropdownElements: string[] = table.raw().map(row => row[0]);
    await pageModel.waitForFinishLoading();
    await pageModel.header.dismissAllNotification();
    await pageModel.header.dropDownMenu.expectDropdownMenuElements(dropdownElements);
});

Then(/^the user clicks the "(.*)" labelled more actions button in the header$/, async (actionName: string) => {
    await pageModel.waitForFinishLoading();
    await pageModel.header.dismissAllNotification();
    await pageModel.header.dropDownMenu.selectMenuAction(actionName);
});

Then(
    /^the "(.*)" labelled more actions button in the header is (enabled|disabled)$/,
    async (actionLabel: string, cssState: 'enabled' | 'disabled') => {
        await pageModel.waitForFinishLoading();
        await pageModel.header.dropDownMenu.expectDropdownMenuActionToBeEnabled(actionLabel, cssState);
    },
);

Then(
    /^the "(.*)" labelled more actions button in the header is (displayed|hidden)$/,
    async (actionLabel: string, cssState: 'displayed' | 'hidden') => {
        await pageModel.waitForFinishLoading();
        await pageModel.header.dropDownMenu.expectDropdownMenuActionToBeDisplayed(actionLabel, cssState === 'hidden');
    },
);
