import { Then, When } from '@cucumber/cucumber';
import { fieldTypes } from '../../../../step-definitions-utils';
import pageModel from '../../../main-page';
import * as StaticStore from '../../../static-store';
import { PodCollectionObject } from '../pod-collection/pod-collection-object';
import { PodCollectionItemObject } from './pod-collection-item-object';

When(
    /^the user selects the "(.*)" (id|labelled) pod collection item of the selected pod collection field$/,
    async (identifier: string, lookupStrategy: 'id' | 'labelled') => {
        const field = <PodCollectionObject>StaticStore.getStoredField(fieldTypes.podCollection);
        await field.dismissAllNotification();
        await field.loseFocus();
        await field.storePodCollectionItem(identifier, lookupStrategy);
    },
);

When(/^the user selects the "(.*)" icon of the selected pod collection item$/, async (name: string) => {
    const item = <PodCollectionItemObject>StaticStore.getStoredObject(StaticStore.StoredKeys.POD_COLLECTION_ITEM);
    await item.selectPodCollectionIcon(name);
});

Then(/^the selected pod collection item is hidden$/, async () => {
    const item = <PodCollectionItemObject>StaticStore.getStoredObject(StaticStore.StoredKeys.POD_COLLECTION_ITEM);
    await item.expectNotToBeDisplayed(item.cssSelector);
});

When(/^the user clicks the "(.*)" action of the selected pod collection item$/, async (actionName: string) => {
    const item = <PodCollectionItemObject>StaticStore.getStoredObject(StaticStore.StoredKeys.POD_COLLECTION_ITEM);
    await item.dropDownMenu.selectMenuAction(actionName);
});

When(
    /^the user (selects|unselects) the main checkbox of the selected pod collection item$/,
    async (action: 'selects' | 'unselects') => {
        await pageModel.waitForFinishLoading();
        const item = <PodCollectionItemObject>StaticStore.getStoredObject(StaticStore.StoredKeys.POD_COLLECTION_ITEM);
        await item.selectMainCheckbox(action);
    },
);
