import { Then, When } from '@cucumber/cucumber';
import { XeNavigationObject } from './xe-navigation-object';

When(/^the user (minimizes|maximizes) the sitemap$/, async (action: 'minimizes' | 'maximizes') => {
    const sitemap = new XeNavigationObject();
    await sitemap.toggleSiteMap(action);
});

Then(
    /^the "(.*)"(?: (sub))? menu item on the sitemap is (displayed|hidden)$/,
    async (menuItem: string, menuLevel: 'sub' | null, expectedState: 'displayed' | 'hidden') => {
        const sitemap = new XeNavigationObject();
        await sitemap.isMenuItemDisplayed({ menuItem, menuLevel, expectedState });
    },
);

When(/^the user clicks the "(.*)"(?: (sub))? menu item$/, async (menuItem: string, menuLevel: 'sub' | null) => {
    const sitemap = new XeNavigationObject();
    await sitemap.clickMenuItem(menuItem, menuLevel);
});

When(
    /^the user (expands|collapses) the "(.*)" sub menu item$/,
    async (action: 'expands' | 'collapses', menuItem: string) => {
        const sitemap = new XeNavigationObject();
        await sitemap.expandOrCollapse(action, menuItem);
    },
);
