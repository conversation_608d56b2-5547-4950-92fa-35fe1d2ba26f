import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';

export class NavigationPanelNestedFieldObject extends AbstractPageObject {
    constructor({
        identifier,
        lookupStrategy,
        fieldType,
        rowId,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        fieldType: utils.NestedFieldTypes;
        rowId: string;
        context?: utils.ElementContext;
    }) {
        super(
            `${context ? `${utils.getContextSelector(context)} ` : ''} div:${
                rowId === 'first' || rowId === 'last' ? `${rowId}-child` : `nth-child(${rowId})`
            }>${utils.getDataTestIdSelector({ domSelector: '', dataTestIdValue: 'e-card' })} ${utils.getLookupStrategySelector({ fieldType, lookupStrategy, identifier })}`,
        );
    }
}
