import { Then, When } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { WorkflowDesignerFieldObject } from './workflow-designer-object';

When(/^the user clicks the "(.*)" button in the workflow designer field$/, async (button: string) => {
    const field = <WorkflowDesignerFieldObject>StaticStore.getStoredField(fieldTypes.workflowDesigner);
    await field.clickWorkflowDesignerButton(button);
});

When(
    /^the user clicks the "(.*)" action in the toolbar of the workflow designer$/,
    async (toolbarActionName: string) => {
        const field = <WorkflowDesignerFieldObject>StaticStore.getStoredField(fieldTypes.workflowDesigner);
        await field.clickWorkflowToolbarAction(toolbarActionName);
    },
);

When(/^the user selects the "(.*)" selection card on the workflow designer add sidebar$/, async (cardTitle: string) => {
    const field = <WorkflowDesignerFieldObject>StaticStore.getStoredField(fieldTypes.workflowDesigner);
    await field.selectCardInWorkflowAddDialog(cardTitle);
});

When(/^the user clicks the "(.*)" button on the workflow designer add sidebar$/, async (buttonName: string) => {
    const field = <WorkflowDesignerFieldObject>StaticStore.getStoredField(fieldTypes.workflowDesigner);
    await field.clickWorkflowAddSidebarButton(buttonName);
});

Then(/^the text content in the workflow designer field is "(.*)"$/, async (expectedValue: string) => {
    const field = <WorkflowDesignerFieldObject>StaticStore.getStoredField(fieldTypes.workflowDesigner);
    await field.checkWorkflowDesignerTextContent(expectedValue);
});

When(
    /^the user searches for "(.*)" in the selection card on the Workflow Designer add sidebar$/,
    async (cardTitle: string) => {
        const field = <WorkflowDesignerFieldObject>StaticStore.getStoredField(fieldTypes.workflowDesigner);
        await field.searchCardInWorkflowAddDialog(cardTitle);
    },
);
