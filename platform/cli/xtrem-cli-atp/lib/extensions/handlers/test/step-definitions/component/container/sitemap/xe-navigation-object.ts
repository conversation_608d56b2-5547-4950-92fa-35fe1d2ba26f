/* eslint-disable no-restricted-syntax */
/* eslint-disable class-methods-use-this */
import { getDataTestIdSelector, waitForElementToBeFound } from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';
import { waitForPromises } from '../../field/wait-util';

const SELECTORS = {
    SECONDARY_MENU: '.xe-navigation-secondary .xe-navigation-sub-menu',
    PRIMARY_MENU: '.xe-navigation-primary .xe-navigation-menu-item',
    SUB_MENU_TITLE_LINE: '.xe-navigation-sub-menu-title-line',
    SUB_MENU_TITLE_LABEL: '.xe-navigation-sub-menu-title-label',
    MENU_ITEM_TITLE: '.xe-navigation-menu-item-title',
};

type NavigationLevel = 'primary' | 'secondary';

export class XeNavigationObject extends AbstractPageObject {
    constructor() {
        super(getDataTestIdSelector({ domSelector: 'nav', dataTestIdValue: 'xe-navigation-menu' }));
    }

    toggleSiteMap = async (action: 'minimizes' | 'maximizes') => {
        const toggleButton = getDataTestIdSelector({ domSelector: 'a', dataTestIdValue: 'xe-navigation-toggle' });
        await waitForElementToBeFound({ name: 'Toggle button', selector: toggleButton });
        const $toggleButton = await $(toggleButton);

        const classes = await (await this.get()).getAttribute('class');
        const navigationState = classes.includes('xe-navigation-primary-closed') ? 'minimized' : 'maximized';

        if (
            (action === 'minimizes' && navigationState === 'minimized') ||
            (action === 'maximizes' && navigationState === 'maximized')
        ) {
            return;
        }

        await $toggleButton.moveTo();
        await $toggleButton.click();
        await waitForPromises(500, 'wait for the sitemap to be toggled');
        await $toggleButton.moveTo({ xOffset: 500 });
    };

    private getNavigationType(menuLevel: 'sub' | null): NavigationLevel {
        return menuLevel === 'sub' ? 'secondary' : 'primary';
    }

    private getMenuItemsSelector(navigation: NavigationLevel): string {
        return navigation === 'secondary' ? SELECTORS.SECONDARY_MENU : SELECTORS.PRIMARY_MENU;
    }

    private async getMenuItemText(item: WebdriverIO.Element, navigation: NavigationLevel): Promise<string> {
        const selector = navigation === 'secondary' ? SELECTORS.SUB_MENU_TITLE_LABEL : SELECTORS.MENU_ITEM_TITLE;
        const element = await item.$(selector);
        const elementText = await element.getText();
        return elementText;
    }

    private async findMenuItem({
        items,
        menuItem,
        navigation,
    }: {
        items: WebdriverIO.Element[];
        menuItem: string;
        navigation: NavigationLevel;
    }): Promise<WebdriverIO.Element | null> {
        for (const item of items) {
            const itemText = await this.getMenuItemText(item, navigation);
            if (itemText.trim() === menuItem.trim()) return item;
        }
        return null;
    }

    isMenuItemDisplayed = async ({
        menuItem,
        menuLevel,
        expectedState,
    }: {
        menuItem: string;
        menuLevel: 'sub' | null;
        expectedState: 'displayed' | 'hidden';
    }) => {
        const navigation = this.getNavigationType(menuLevel);
        if (navigation === 'secondary' && expectedState === 'hidden') {
            const submenuIsDisplayed = await (await $('.xe-navigation-secondary')).isDisplayed();
            if (!submenuIsDisplayed) return;
        }

        const $$items = await $$(this.getMenuItemsSelector(navigation));
        const item = await this.findMenuItem({ items: $$items, menuItem, navigation });
        const isDisplayed = item ? await item.isDisplayed() : false;
        if (isDisplayed === (expectedState === 'displayed')) return;

        const errorMessage = this.getMenuItemErrorMessage({ $$items, item, menuItem, navigation, expectedState });
        throw new Error(errorMessage);
    };

    private getMenuItemErrorMessage({
        $$items,
        item,
        menuItem,
        navigation,
        expectedState,
    }: {
        $$items: WebdriverIO.Element[];
        item: WebdriverIO.Element | null;
        menuItem: string;
        navigation: NavigationLevel;
        expectedState: 'displayed' | 'hidden';
    }): string {
        const navigationLevel = navigation === 'secondary' ? ' sub ' : ' ';
        const selector = item ? item.selector : $$items[0]?.selector;
        return `Expected menu item "${menuItem}" to be ${expectedState} in the${navigationLevel}menu.\nSelector: ${selector}\n`;
    }

    clickMenuItem = async (menuItem: string, menuLevel: 'sub' | null) => {
        const navigation = this.getNavigationType(menuLevel);
        const $$items = await $$(this.getMenuItemsSelector(navigation));
        const item = await this.findMenuItem({ items: $$items, menuItem, navigation });

        if (item) {
            await item.moveTo();
            await item.click();
            await waitForPromises(500, 'wait for the sitemap to be toggled');
            return;
        }

        throw new Error(
            `Expected menu item "${menuItem}" could not be found in the${navigation === 'secondary' ? ' sub ' : ' '}menu.`,
        );
    };

    expandOrCollapse = async (action: 'expands' | 'collapses', menuItem: string) => {
        const $$items = await $$(SELECTORS.SECONDARY_MENU);
        const item = await this.findMenuItem({ items: $$items, menuItem, navigation: 'secondary' });

        if (item) {
            const title = await item.$(SELECTORS.SUB_MENU_TITLE_LINE);
            const classes = await title.getAttribute('class');
            const hasOpenClass = classes.includes('xe-navigation-sub-menu-title-line-open');
            if ((action === 'expands' && !hasOpenClass) || (action === 'collapses' && hasOpenClass)) {
                await title.moveTo();
                await title.click();
                await waitForPromises(500, 'wait for the sitemap to be toggled');
            }
            return;
        }

        throw new Error(`Expected menu item "${menuItem}" could not be found in the sub menu.`);
    };
}
