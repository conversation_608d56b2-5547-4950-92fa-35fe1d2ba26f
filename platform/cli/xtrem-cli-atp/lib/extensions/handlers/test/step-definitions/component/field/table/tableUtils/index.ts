import { capitalize } from 'lodash';
import * as utils from '../../../../step-definitions-utils';
import { SELECTORS } from '../../../filter-object';
import { waitForPromises } from '../../wait-util';

export const waitForTableToBePopulated = async (tableSelector: string) => {
    try {
        const rowElement = await browser.$(`${tableSelector} div[class="ag-center-cols-container"] div[role="row"]`);
        await rowElement.waitForDisplayed({ timeout: utils.atpEnv.timeoutWaitFor });
    } catch (error) {
        throw new Error('Expected table field to be populated with at least one row.');
    }
};

export const waitForGridCellsContent = async (tableSelector: string): Promise<void> => {
    const gridCells = await browser.$$(`${tableSelector} div[class="ag-center-cols-container"] [role="gridcell"]`);
    await browser.waitUntil(
        async () => {
            const gridCellsHtml = await Promise.all(gridCells.map(cell => cell.getHTML()));
            return gridCellsHtml.every(html => html.length > 0);
        },
        {
            timeout: utils.atpEnv.timeoutWaitFor,
            timeoutMsg: `Expected grid cells to have html content in ${utils.atpEnv.timeoutWaitFor} ms`,
        },
    );
};

// This function will be replaced by the scroll() function (just below) in the future
export const scrollToLeft = async (selector: string, amount: number) => {
    await browser.execute(
        `var element = document.querySelector('${selector}');
            if (element) {
                if (${amount} === 0) {
                    element.scrollLeft = 0;
                } else {
                    element.scrollLeft = ${amount};
                }
            }`,
    );
};

// This function will be replaced by the scroll() function (just below) in the future
export const scrollToTop = async (selector: string, amount: number) => {
    await browser.execute(
        `var element = document.querySelector('${selector}');
            if (element) {
                if (${amount} === 0) {
                    element.scrollTop = 0;
                } else {
                    element.scrollTop = ${amount};
                }
            }`,
    );
};

export const scroll = async ({
    selector,
    amount,
    direction,
}: {
    selector: string;
    amount: number;
    direction: 'left' | 'top';
}) => {
    await browser.execute(
        `const scrollKey = \`scroll${capitalize(direction)}\`;
        const element = document.querySelector('${selector}');
        if (element) {
            element[scrollKey] = Math.max(element[scrollKey] + ${amount}, 0);
        }
        `,
    );
};

export const scrollUntilClickable = async (selector: string, scrollStep: number = 50) => {
    await browser.waitUntil(
        async () => {
            const element = await $(selector);
            await element.scrollIntoView();
            const isClickable = await element.isClickable();
            if (!isClickable) {
                await scroll({ selector, amount: scrollStep, direction: 'top' });
                await waitForPromises(300, 'wait for scroll to be processed');
            }
            return isClickable;
        },
        {
            timeout: utils.atpEnv.timeoutWaitFor,
            timeoutMsg: 'Element is not clickable after scrolling',
        },
    );
};

export const scrollToTableRow = async (selectorToUse: string, rowNumber: number | null | undefined) => {
    // Using loose equality (==) to handle both null and undefined cases
    if (rowNumber == null) return;

    if (await (await browser.$(selectorToUse)).isDisplayed()) {
        return;
    }

    const scrollStep = 450; // 10 rows
    const firstRowElement = await browser.$(
        'div.ag-body-viewport div.ag-center-cols-viewport div[role="row"][row-index]:first-child',
    );
    await firstRowElement.waitForDisplayed();
    let lastRowElement = await browser.$(
        'div.ag-body-viewport div.ag-center-cols-viewport div[role="row"][row-index]:last-child',
    );
    await lastRowElement.waitForDisplayed();
    const actualRowNumber = rowNumber - 1;
    const firstElementRowIndex = Number(await firstRowElement.getAttribute('row-index'));
    const lastElementRowIndex = Number(await lastRowElement.getAttribute('row-index'));
    const isLowerThanFirst = actualRowNumber < firstElementRowIndex;
    const isGreaterThanLast = actualRowNumber > lastElementRowIndex;
    if (!isLowerThanFirst && !isGreaterThanLast) {
        return;
    }
    try {
        while (!((await (await $(selectorToUse)).isExisting()) && (await (await $(selectorToUse)).isDisplayed()))) {
            const lastY = (await browser.getElementRect(lastRowElement.elementId)).y;
            lastRowElement = await browser.$(
                'div.ag-body-viewport div.ag-center-cols-viewport div[role="row"][row-index]:last-child',
            );
            const lastRow = Number(await lastRowElement.getAttribute('row-index'));
            await scroll({
                selector: 'div.ag-body-viewport',
                amount: isLowerThanFirst ? -scrollStep : scrollStep,
                direction: 'top',
            });
            await waitForPromises(500, 'test');
            lastRowElement = await browser.$(
                'div.ag-body-viewport div.ag-center-cols-viewport div[role="row"][row-index]:last-child',
            );
            await waitForPromises(200, 'test');

            const currY = await browser.getElementRect(lastRowElement.elementId);
            const lastChild = await browser.$(
                'div.ag-body-viewport div.ag-center-cols-viewport div[role="row"][row-index]:last-child',
            );
            const currRow = await lastChild.getAttribute('row-index');
            if (lastY === currY.y && lastRow === Number(currRow)) throw new Error('End of lines');

            await waitForPromises(200, 'test');
        }
    } catch (err) {
        throw new Error(`Expected row could not be found: "${rowNumber}".\nSelector: ${selectorToUse} \n ${err}`);
    }
};

export const isRowExists = async (): Promise<boolean> => {
    const row = await browser.$(`[row-index="0"]`);
    return row.isExisting();
};

/**
 * This function will scroll to the column and make sure it is visible.
 * This function is only used for the table component.
 * @param column the column to scroll to
 * @param lookupStrategy the lookup strategy to use for the column
 * @param floatingRow whether the row is floating or not
 */
export const scrollToTableColumn = async ({
    tableSelector,
    columnName,
    lookupStrategy,
    floatingRow = false,
}: {
    tableSelector: string;
    columnName: string;
    lookupStrategy: utils.LookupStrategy;
    floatingRow?: boolean;
}) => {
    const tableElement = await $(tableSelector);

    if ((await tableElement.isExisting()) && !(await tableElement.isDisplayedInViewport())) {
        await tableElement.scrollIntoView();
    }

    // await scrollTo(tableSelector);

    await waitForPromises(300, 'waiting for scroll to be processed');

    const container = await $(`${tableSelector} div.ag-header-container`);
    const containerWidth = (await container.getSize()).width;
    const scrollStep = 50;
    const viewPortSelector = `${tableSelector} div.ag-body-viewport div.ag-center-cols-viewport`;
    const headerSelector = `${tableSelector} ${SELECTORS.getColumnHeaderSelector(columnName, lookupStrategy)}`;
    let header = await $(headerSelector);
    const rowSelector = floatingRow
        ? '.ag-floating-top-container div[role="row"][row-index="t-0"]'
        : '.ag-center-cols-container div[role="row"][row-index="0"]';
    const cellSelector = `${tableSelector} ${rowSelector} ${utils.getTableNestedFieldSelector({ lookupStrategy, identifier: columnName })}`;
    const cell = await $(cellSelector);

    // If the column is already visible, return
    if (
        (await header.isExisting()) &&
        (await header.isDisplayed()) &&
        ((await header.isDisplayedInViewport()) || (await header.isClickable())) &&
        (await cell.isDisplayedInViewport())
    ) {
        return;
    }

    // await scrollToLeft(viewPortSelector, 0);
    await scroll({ selector: viewPortSelector, amount: -containerWidth, direction: 'left' });

    for (
        let scrollLeft = (await browser.getElementRect(container.elementId)).x;
        scrollLeft <= containerWidth;
        scrollLeft += scrollStep
    ) {
        header = await $(headerSelector);
        if (
            (await header.isExisting()) &&
            (await header.isDisplayed()) &&
            ((await header.isDisplayedInViewport()) || (await header.isClickable()))
        ) {
            scrollLeft =
                (await browser.getElementRect(header.elementId)).x -
                (await browser.getElementRect(container.elementId)).x;

            await scrollToLeft(viewPortSelector, scrollLeft);
            return;
        }

        await scrollToLeft(viewPortSelector, scrollLeft);
        await waitForPromises(300, 'waiting to show element');
    }
    throw new Error(`Expected column couldn't be found: ${columnName}. \nSelector: ${header.selector}`);
};

export const scrollTo = async (selector: string) => {
    await browser.execute(
        `const node = document.querySelector('${selector}'); node ? node.scrollIntoViewIfNeeded() : true;`,
    );
    const element = await $(selector);
    if (await element.isExisting()) {
        await browser.execute(async el => {
            await el.scrollIntoView({ block: 'center', inline: 'nearest' });
        }, element);
    }
};
