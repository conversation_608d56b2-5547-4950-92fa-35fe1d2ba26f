import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';

export class CheckBoxFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'checkbox', identifier, lookupStrategy, context });
    }

    override async click() {
        await this.expectToAppear({ cssSelector: 'input' });
        await super.jsClick({ cssSelector: 'input', ignoreContext: false, skipVisibilityCheck: true });
    }

    async setCheckboxToValue(state: 'ticks' | 'unticks') {
        const selectorToUse = this.getSelectorForOperation('input');

        const element = await browser.$(selectorToUse);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Checkbox does not exist.\nSelector: ${selectorToUse}`,
        });
        let result = String(await element.isSelected());

        if (result == null) {
            result = 'false';
        }

        if ((state === 'ticks' ? 'true' : 'false') !== result) {
            await this.click();
        }
    }
}
