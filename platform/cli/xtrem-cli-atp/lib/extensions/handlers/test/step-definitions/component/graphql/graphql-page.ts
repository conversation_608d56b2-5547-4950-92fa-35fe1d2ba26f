import * as fs from 'fs';
// import path from 'node:path';
import AbstractPageObject from '../abstract-page-object';
import { GraphQlEditor } from './graphql-editor';
import path = require('path');

export class GraphQLPage extends AbstractPageObject {
    constructor() {
        super('.graphiql-container');
    }

    graphQlEditor = new GraphQlEditor();

    async loadGraphQLFile(relativeFilePath: string): Promise<string> {
        return fs.readFileSync(await this.getRelativePath(relativeFilePath), {
            encoding: 'utf-8',
        });
    }

    async loadResponseFile(responseFile: string): Promise<string> {
        return fs.readFileSync(await this.getRelativePath(responseFile), {
            encoding: 'utf-8',
        });
    }

    // eslint-disable-next-line require-await, class-methods-use-this
    async getRelativePath(relativeFilePath: string): Promise<string> {
        const featurePath = path
            .resolve(process.env.FEATURE_PATH!)
            .replace(/\\/g, '/')
            .substring(0, path.resolve(process.env.FEATURE_PATH!).replace(/\\/g, '/').lastIndexOf('/'));

        const filePath = path.resolve(path.join(featurePath, relativeFilePath));

        console.log(`Loading file: ${filePath}`);

        if (!fs.existsSync(filePath)) {
            throw new Error(`Expected file could not be found: ${filePath}`);
        }

        return filePath;
    }

    static get graphQlEditor(): GraphQlEditor {
        return this.graphQlEditor;
    }
}
