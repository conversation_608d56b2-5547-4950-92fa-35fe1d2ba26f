// eslint-disable-next-line import/no-extraneous-dependencies
import AxeBuilder from '@axe-core/webdriverio';
import { Given, Then, When } from '@cucumber/cucumber';
import { addAttachment } from '@wdio/allure-reporter';
import { expect } from 'chai';
import * as fs from 'fs';
import { SupportedDevices, takeScreenshot, waitForElementToExist } from '../step-definitions-utils';
import { waitForPromises } from './field/wait-util';
import { GenericPageObject } from './generic-page-object';
import pageModel from './main-page';
import * as StaticStore from './static-store';
import { testGlobals } from './test-globals';
import path = require('path');

Given(
    /^the user is logged into the system in (ultrawide desktop|HD desktop|desktop|tablet|mobile) mode using the "([^"\n\r]*)" user name and "([^"\n\r]*)" password$/,
    async (device: SupportedDevices, username: string, password: string) => {
        await pageModel.waitForFinishLoading();
        await pageModel.logIn({ device, usernameParam: username, passwordParam: password });
    },
);

Given(/^the user logs out of the system$/, async () => {
    await pageModel.logOut();
});

Given(
    /^the user opens the application on a (ultrawide desktop|HD desktop|desktop|mobile|tablet)$/,
    async (device: SupportedDevices) => {
        testGlobals.device = device;
        await pageModel.openApp({ url: '', device });
    },
);

Given(
    /^the user opens the application on a (ultrawide desktop|HD desktop|desktop|mobile|tablet) using the following link: "(.*)"$/,
    async (device: SupportedDevices, url: string) => {
        testGlobals.device = device;
        try {
            await pageModel.openApp({ url, device });
        } catch (error) {
            throw new Error(error);
        }
    },
);

/**
 * This step should be used for intermediate navigation events within a scenario, but not for the initial page load.
 */
Given(
    /^the user (navigates to|opens a new tab and navigates to) the following link: "(.*)"$/,
    async (newTabAction: 'navigates to' | 'opens a new tab and navigates to', url: string) => {
        try {
            await pageModel.openTargetURL(url, newTabAction === 'opens a new tab and navigates to');
        } catch (error) {
            throw new Error(error);
        }
    },
);

When(/^the user waits (?:for )?(\d+) (?:second|seconds)$/, async (seconds: number) => {
    await pageModel.wait(seconds);
});

Then(/^the test id ("[a-z0-9- ]*") text content is "([^"\n\r]*)"$/, async (id: string, expectedValue: string) => {
    await pageModel.expectTextContent({ toBe: expectedValue, ignoreCase: true, cssSelector: `[data-testid=${id}]` });
});

Then(/^the test id ("[a-z0-9- ]*") not exists in the page$/, async (id: string) => {
    await pageModel.expectToNotExist(`[data-testid=${id}]`);
    expect(!(await (await $(`[data-testid=${id}]`)).isExisting()));
});

/** Generic visual regression step. */
Then(
    /^element with (test id|class|css selector) "(.*)" looks as before$/,
    async (selectorType: 'class' | 'test id' | 'css selector', selector: string) => {
        const genericPageObject = new GenericPageObject();
        const cssSelector = genericPageObject.getCssSelector(selectorType, selector);
        await waitForElementToExist({ name: 'element', selector: cssSelector });
        await genericPageObject.expectToMatchSnapshot(cssSelector);
    },
);

/**
 * Only to be used for debugging purposes
 */
Then('takes a screenshot', async () => {
    await takeScreenshot();
});

Then(
    /the page does not have any "(minor|moderate|serious|critical)" accessibility violations/,
    async (level: 'minor' | 'moderate' | 'serious' | 'critical') => {
        const result = await browser.executeAsync<any, []>(`
            const resolve = arguments[0];

            if (!window.axe) {
                const script = document.createElement('script');
                script.type = 'text/javascript';
                script.src = 'axe.min.js';
                document.head.appendChild(script);
            }
            const intervalRef = setInterval(() => {
                if (!window.axe) {
                    return;
                }
                clearInterval(intervalRef);
                resolve(window.axe.run().catch(console.log));
            }, 100);
            `);

        const filteredViolations = result.violations?.filter((v: any) => v.impact === level) || [];

        if (filteredViolations.length > 0) {
            const violationDetails = JSON.stringify(filteredViolations, null, 4);
            // eslint-disable-next-line no-console
            console.log(violationDetails);
            addAttachment('Accessibility test scan result', violationDetails, 'text/plain');
            throw new Error(
                `Accessibility test failed, ${filteredViolations.length} violations found, see details in attachment`,
            );
        }
    },
);

Then(/^the user executes an accessibility tests scan/, async () => {
    const axebuilder = new AxeBuilder({ client: browser });
    const result = await axebuilder.analyze();
    await waitForPromises(500, 'wait for analyze');
    await takeScreenshot();
    const browserURL = await browser.getUrl();
    const shortURL = browserURL.substring(browserURL.indexOf('@'), browserURL.length).split('/');
    const featureName = process.env.FEATURE_PATH?.substring(process.env.FEATURE_PATH?.lastIndexOf('/'))
        .replace('/', '')
        .replace('.feature', '');

    let packageName;
    let pageName;
    let violationDetails;

    if (shortURL[1] !== 'undefined') {
        packageName = shortURL[1].toString();
    }
    if (shortURL[2] !== 'undefined') {
        pageName = shortURL[2].toString();
    }

    violationDetails = JSON.stringify(result.violations, null, 4).replaceAll(
        '"help"',
        `"feature": "${featureName}",\n        "package": "${packageName}",\n        "page": "${pageName}",\n        "scenario": "${testGlobals._CURRENT_SCENARIO}",\n        "help"`,
    );

    if (violationDetails === '[]') {
        violationDetails = violationDetails.replace(
            '[]',
            `[\n    {\n        "feature": "${featureName}",\n        "package": "${packageName}",\n        "page": "${pageName}",\n        "scenario": "${testGlobals._CURRENT_SCENARIO}",\n        "help": "No violation found"\n    }\n]`,
        );
    }

    if (!fs.existsSync(testGlobals.accessibility_json_path!)) {
        fs.mkdirSync(testGlobals.accessibility_json_path!);
    }

    addAttachment('Accessibility test scan result', violationDetails, 'text/plain');

    const targetFileName = `accessibility-test-${new Date().getTime()}.json`;
    const targetPath = path.resolve(testGlobals.accessibility_json_path!, targetFileName);
    fs.writeFileSync(targetPath, violationDetails, 'utf-8');
});

Then(
    /^the user stores the generated value with length (\b([5-9]|1[0-3])\b) with the key "(.*)"$/,
    (valueLength: number, storeKey: string) => {
        const timeStamp = new Date().getTime().toString();
        const generatedValue = timeStamp.substring(13 - valueLength);
        StaticStore.storeObject(storeKey, generatedValue);
        // eslint-disable-next-line no-console
        console.log(`KEY: ${storeKey} - DATA STORED: ${generatedValue}`);
    },
);
