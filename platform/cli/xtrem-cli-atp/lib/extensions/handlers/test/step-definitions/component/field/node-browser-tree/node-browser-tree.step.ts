import { Then, When } from '@cucumber/cucumber';
import * as utils from '../../../step-definitions-utils';
import pageModel from '../../main-page';
import * as StaticStore from '../../static-store';
import { waitForPromises } from '../wait-util';
import { NodeBrowserTreeObject } from './node-browser-tree-object';

When(
    /^the user selects the "(.*)" (bound|labelled) node-browser-tree field on (the main page|a modal|a full width modal|the sidebar|the navigation panel)$/,
    async (identifier: string, lookupStrategy: utils.LookupStrategy, context: utils.ElementContext) => {
        const field = new NodeBrowserTreeObject({ identifier, lookupStrategy, context });
        await field.dismissAllNotification();
        await utils.waitForElementToBeFound({
            name: `"${identifier}" node-browser-tree field`,
            selector: field.cssSelector as string,
        });
        await StaticStore.storeField(utils.fieldTypes.nodeBrowserTree, field);
    },
);

Then(/^the user searches for "(.*)" in the node-browser-tree field$/, async (placeholder: string) => {
    const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(placeholder);
    await field.searchTreeElement(storeValue);
    await waitForPromises(500, 'Filtering table');
});

When(
    /^the user selects the tree-view element of level "([^"\n\r]*)" with text "([^"\n\r]*)" in the node-browser-tree field$/,
    async (nodeLevel: string, nodeElementText: string) => {
        const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(nodeElementText);
        const nodeElementID = await field.getNodeElement(nodeLevel, storeValue);
        StaticStore.storeObject<WebdriverIO.Element>(StaticStore.StoredKeys.NODE_BROWSER_TREE_ELEMENT, nodeElementID);
    },
);

Then(
    /^the user (expands|collapses) the tree-view element in the node-browser-tree field$/,
    async (toggle: utils.ExpandedOrCollapsed) => {
        const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
        const treeElement = StaticStore.getStoredObject<WebdriverIO.Element>(
            StaticStore.StoredKeys.NODE_BROWSER_TREE_ELEMENT,
        );
        await field.expandCollapse(toggle, treeElement);
    },
);

Then(/^the user (ticks|unticks) the tree-view element in the node-browser-tree field$/, async (toggleState: string) => {
    const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
    await pageModel.waitForFinishLoading();
    const treeElement = StaticStore.getStoredObject<WebdriverIO.Element>(
        StaticStore.StoredKeys.NODE_BROWSER_TREE_ELEMENT,
    );
    await field.selectNodeElementWithText(toggleState, treeElement);
});

Then(/^the node-browser-tree field is (enabled|disabled)$/, async (cssState: 'enabled' | 'disabled') => {
    const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
    await field.loseFocus();
    await field.expectToBeEnabledClass(field.cssSelector, cssState === 'disabled');
});

Then(/^the node-browser-tree field is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
    await field.loseFocus();
    await field.expectTitleToBeDisplayed(cssState === 'hidden');
});

Then(
    /^the helper text of the node-browser-tree field is (displayed|hidden)$/,
    async (cssState: 'displayed' | 'hidden') => {
        const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
        await field.loseFocus();
        await field.expectHelperTextToBeDisplayed(cssState === 'hidden');
    },
);

Then(/^the helper text of the node-browser-tree field is "(.*)"$/, async (value: string) => {
    const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
    await field.loseFocus();
    await field.expectHelperText(value);
});

Then(/^the title of the node-browser-tree field is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
    await field.loseFocus();
    await field.expectTitleToBeDisplayed(cssState === 'hidden');
});

Then(/^the title of the node-browser-tree field is "(.*)"$/, async (title: string) => {
    const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
    await field.loseFocus();
    await field.expectTitle(title);
});

Then(/^the node-browser-tree field is read-only$/, async () => {
    const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
    await field.loseFocus();
    await field.expectToBeReadOnly();
});

Then(/^the user clears the search field of the node-browser-tree field$/, async () => {
    const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
    await field.loseFocus();
    await field.clearTreeElement();
});

Then(/^the tree-view element of the node-browser-tree field is (ticked|unticked)$/, async (ticked: string) => {
    const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
    const nodeElements = StaticStore.getStoredObject<WebdriverIO.Element>(
        StaticStore.StoredKeys.NODE_BROWSER_TREE_ELEMENT,
    );
    await field.expectTickUntick(ticked, nodeElements);
});

Then(
    /^the tree-view element of the node-browser-tree field is (expanded|collapsed)$/,
    async (toggleStatus: 'expanded' | 'collapsed') => {
        const field = <NodeBrowserTreeObject>StaticStore.getStoredField(utils.fieldTypes.nodeBrowserTree);
        const nodeElements = StaticStore.getStoredObject<WebdriverIO.Element>(
            StaticStore.StoredKeys.NODE_BROWSER_TREE_ELEMENT,
        );
        await field.expectExpandedCollapsed(toggleStatus, nodeElements);
    },
);
