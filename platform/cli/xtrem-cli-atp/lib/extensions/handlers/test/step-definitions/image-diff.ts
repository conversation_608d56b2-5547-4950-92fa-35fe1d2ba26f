/* eslint-disable no-console */
import { addAttachment } from '@wdio/allure-reporter';
import * as fs from 'fs';
import * as gm from 'gm';
import * as looksSame from 'looks-same';

const diffOptions = {
    strict: false,
    tolerance: 5,
    antialiasingTolerance: 10,
    ignoreAntialiasing: true,
    ignoreCaret: true,
};

export const saveGmState = (state: gm.State, target: string) =>
    new Promise<void>((resolve, reject) => {
        state.write(target, err => {
            if (err) {
                console.log(err);
                reject(err);
            } else {
                resolve();
            }
        });
    });

export const compareImages = async (referencePath: string, currentPath: string) => {
    const diffOptionsWithRatio = { ...diffOptions };
    try {
        const result = await looksSame(currentPath, referencePath, diffOptionsWithRatio);
        if (!result.equal) {
            addAttachment('referencePath', fs.readFileSync(referencePath), 'image/png');
            addAttachment('currentPath', fs.readFileSync(currentPath), 'image/png');
            await looksSame
                .createDiff({
                    reference: referencePath,
                    current: currentPath,
                    highlightColor: '#ff00ff',
                    ...diffOptionsWithRatio,
                })
                .then(
                    buffer => {
                        addAttachment('buffer', buffer, 'image/png');
                        throw new Error("Element doesn't match snapshot.");
                    },
                    diffError => {
                        console.log('Failed to build comparing image');
                        throw diffError;
                    },
                );
        }
    } finally {
        fs.unlinkSync(currentPath);
    }
};
