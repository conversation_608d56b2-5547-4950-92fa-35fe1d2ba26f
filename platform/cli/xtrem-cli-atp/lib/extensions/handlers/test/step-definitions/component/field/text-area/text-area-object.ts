import { FieldObject } from '../field-object';

export class TextAreaFieldObject extends FieldObject {
    override async write({
        content,
        cssSelector = 'textarea',
        ignoreContext = false,
    }: {
        content: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        await super.write({ content, cssSelector, ignoreContext });
    }

    override async expectValue({
        toBe,
        cssSelector = 'textarea',
        ignoreContext = false,
        isContains = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
        isContains?: boolean;
    }) {
        if (isContains) {
            await super.expectValueContains({
                toBe,
                cssSelector,
                ignoreContext,
                multiLine: true,
                ignoreWhitespace: true,
            });
        } else {
            await super.expectValue({ toBe, cssSelector, ignoreContext, multiLine: true });
        }
    }
}
