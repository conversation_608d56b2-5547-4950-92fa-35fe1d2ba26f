import { Then, When } from '@cucumber/cucumber';
import { takeScreenshot } from '../../../step-definitions-utils';
import { ToastObject } from './toast-object';

Then(/^the value of the toast is "(.*)"$/, async (value: string) => {
    if (value.length < 128) await takeScreenshot();
    const messageObj = new ToastObject();
    await messageObj.expectSomeToastWithText(value);
    await messageObj.detectError();
});

Then(/^a toast with text "(.*)" is displayed$/, async (value: string) => {
    if (value.length < 128) await takeScreenshot();
    const messageObj = new ToastObject();
    await messageObj.expectSomeToastWithText(value);
    await messageObj.detectError();
});

Then(/^a validation error message is displayed with text$/, async (value: string) => {
    if (value.length < 128) await takeScreenshot();
    const messageObj = new ToastObject();
    await messageObj.errorSomeToastWithText(value);
});

Then(/^a toast containing text "(.*)" is displayed$/, async (value: string) => {
    if (value.length < 128) await takeScreenshot();
    const messageObj = new ToastObject();
    await messageObj.expectSomeToastWithTextContent(value);
    await messageObj.detectError();
});

Then(/^a validation error message is displayed containing text$/, async (value: string) => {
    if (value.length < 128) await takeScreenshot();
    const messageObj = new ToastObject();
    await messageObj.errorSomeToastWithTextContent(value);
});

When(/^the user dismisses all the toasts$/, async () => {
    const messageObj = new ToastObject();
    await messageObj.dismissAllToasts();
    await takeScreenshot();
});

When(/^the user dismisses the validation error message$/, async () => {
    const messageObj = new ToastObject();
    await messageObj.dismissAllToasts();
    await takeScreenshot();
});

Then(
    /^a (default|error|info|success|warning) toast with text "(.*)" is displayed$/,
    async (type: string, value: string) => {
        if (value.length < 128) await takeScreenshot();
        const messageObj = new ToastObject();
        await messageObj.expectSomeToastWithTypeAndText(type, value);
        if (type !== 'error') {
            await messageObj.detectError();
        }
    },
);

Then(
    /^a (default|error|info|success|warning) toast containing text "(.*)" is displayed$/,
    async (type: string, value: string) => {
        if (value.length < 128) await takeScreenshot();
        const messageObj = new ToastObject();
        await messageObj.expectSomeToastWithTypeAndTextContent(type, value);
        if (type !== 'error') {
            await messageObj.detectError();
        }
    },
);

Then(/^no error toast or validation error message is displayed$/, async () => {
    await takeScreenshot();
    const messageObj = new ToastObject();
    await messageObj.detectError();
});
