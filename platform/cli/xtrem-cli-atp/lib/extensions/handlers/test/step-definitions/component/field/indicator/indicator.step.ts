import { Then, When } from '@cucumber/cucumber';
import { ElementContext, FieldTypes, LookupStrategy } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { IndicatorFieldObject } from './indicator-field-object';

When(
    /^the user selects the "(.*)" (bound|labelled) tile (aggregate|calendar|checkbox|code editor|count|date|relative date|dropdown-list|filter select|graphiql editor|icon|label|link|multi dropdown|multi reference|numeric|progress|radio|reference|rich text|scan|select|switch|text|text area) field on (the main page|a modal|a full width modal|the detail panel|the sidebar)$/,
    async (identifier: string, lookupStrategy: LookupStrategy, fieldType: FieldTypes, context: ElementContext) => {
        const field = new IndicatorFieldObject({ identifier, lookupStrategy, fieldType, context });
        await field.dismissAllNotification();
        await (
            await field.get()
        ).waitForExist({
            timeoutMsg: `Expected element could not be found: "${identifier}". \nSelector: ${field.cssSelector}`,
        });
        await StaticStore.storeField(fieldType, field);
    },
);

Then(
    /^the value of the tile (aggregate|code editor|count|dropdown-list|relative date|date|filter select|graphiql editor|multi dropdown|multi reference|numeric|reference|select|text|text area) field is "(.*)"$/,
    async (fieldType: FieldTypes, value: string) => {
        const storefield = <IndicatorFieldObject>StaticStore.getStoredField(fieldType);
        await storefield.expectedValue(value);
    },
);

Then(
    /^the "([^"\n\r]*)" (bound|labelled) tile (aggregate|calendar|checkbox|code editor|count|date|relative date|dropdown-list|filter select|graphiql editor|icon|label|link|multi dropdown|multi reference|numeric|progress|radio|reference|rich text|scan|select|switch|text|text area) field is (displayed|hidden)$/,
    async (value: string, lookupStrategy: LookupStrategy, fieldType: FieldTypes, cssState: 'displayed' | 'hidden') => {
        const field = new IndicatorFieldObject({ identifier: value, lookupStrategy, fieldType });
        await (
            await field.get()
        ).waitForExist({
            timeoutMsg: `Expected element could not be found: "${value}" ${fieldType} tile field.\nSelector: ${field.cssSelector}`,
        });
        await field.expectTileToBeDisplayed(cssState);
    },
);

When(
    /^the user clicks in the tile (checkbox|count|date|relative date|dropdown-list|filter select|icon|label|link|multi dropdown|multi reference|numeric|progress|reference|rich text|scan|select|separator|text) field$/,
    async (fieldType: FieldTypes) => {
        await browser.pause(500);
        const field = <IndicatorFieldObject>StaticStore.getStoredField(fieldType);
        await field.click();
    },
);

Then(
    /^the tile (aggregate|calendar|checkbox|count|date|relative date|dropdown-list|filter select|multi dropdown|multi reference|numeric|reference|rich text|text|text area|select|switch) field is (enabled|disabled)$/,
    async (fieldType: FieldTypes, cssState: 'enabled' | 'disabled') => {
        const field = <IndicatorFieldObject>StaticStore.getStoredField(fieldType);
        await field.loseFocus();
        await field.expectToBeEnabledClass(field.cssSelector, cssState === 'disabled');
    },
);
