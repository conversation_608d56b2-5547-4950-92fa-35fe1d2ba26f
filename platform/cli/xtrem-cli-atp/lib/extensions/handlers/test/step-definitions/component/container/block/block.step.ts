import { Then } from '@cucumber/cucumber';
import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { BlockContainerObject } from './block-object';

Then(
    /^the title of the "([^"\n\r]*)" (bound|labelled) block container on (the main page|the detail list|a modal|a full width modal|the detail panel) is "(.*)"$/,
    async (identifier: string, lookupStrategy: LookupStrategy, context: ElementContext, title: string) => {
        const container = new BlockContainerObject({ identifier, lookupStrategy, context });
        await container.expectTitle(title);
    },
);
