import * as utils from '../../../step-definitions-utils';
import { getDataTestIdSelector, timeoutWaitFor } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { NestedGridObject } from '../nested-grid/nested-grid-object';
import { TableObject } from '../table/table-object';

export const expectImageStatus = async ({
    selector,
    identifier,
    status,
}: {
    selector: string;
    identifier: string;
    status: 'defined' | 'undefined';
}) => {
    const element = await $(selector);
    await element.waitForExist({
        timeout: timeoutWaitFor,
        timeoutMsg: `Expected element could not be found: "${identifier}" nested image.\nSelector: ${element.selector}`,
    });

    let selectorToUse = '';

    if (status === 'defined') {
        selectorToUse = '.e-image-field-content-wrapper img';
    } else if (status === 'undefined') {
        selectorToUse = getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-image-field-placeholder' });
    }

    try {
        await browser.waitUntil(
            async () => {
                const placeHolder = await element.$(selectorToUse);
                const isDisplayed = (await placeHolder.isExisting()) && (await placeHolder.isDisplayed());
                return isDisplayed;
            },
            { timeout: timeoutWaitFor },
        );
    } catch (error) {
        await browser.takeScreenshot();
        throw new Error(`Expected image field to be ${status}.\nSelector: ${element.selector} ${selectorToUse}`);
    }
};

export async function selectAndGetParent(parentType: 'table' | 'nested grid') {
    let parent;
    if (parentType === 'table') {
        const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
        await table.expect.waitForTableStopLoading();
        parent = table;
    } else {
        const nestedGrid = <NestedGridObject>StaticStore.getStoredField(utils.fieldTypes.nestedGrid);
        await nestedGrid.waitForTableFieldToLoad();
        parent = nestedGrid;
    }
    return parent;
}
