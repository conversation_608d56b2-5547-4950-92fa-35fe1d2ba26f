import { Then } from '@cucumber/cucumber';
import { ElementContext, LookupStrategy, NestedFieldTypes } from '../../../step-definitions-utils';
import { NavigationPanelNestedFieldObject } from './navigation-panel-nested-field-object';

Then(
    /^the value of the "([^"\n\r]*)" bound nested (numeric|text|label|reference|aggregate) field on the "([^"\n\r]*)" navigation panel's row is "(.*)"$/,
    async (identifier: string, fieldType: NestedFieldTypes, rowId: string, expectedContent: string) => {
        const field = new NavigationPanelNestedFieldObject({
            identifier,
            lookupStrategy: LookupStrategy.BIND,
            fieldType,
            rowId,
            context: ElementContext.NAVIGATION_PANEL,
        });
        await field.loseFocus();
        await field.expectTextContent({ toBe: expectedContent, ignoreCase: false, cssSelector: 'span' });
    },
);
