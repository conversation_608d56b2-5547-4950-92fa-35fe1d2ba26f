import { BaseFieldTypes, NonNestedFieldTypes } from './enums';

export type DomSelectorTypes =
    | ''
    | 'a'
    | 'button'
    | 'div'
    | 'h1'
    | 'h2'
    | 'h3'
    | 'h4'
    | 'span'
    | 'ul'
    | 'label'
    | 'input'
    | 'header'
    | 'footer'
    | 'nav'
    | 'aside'
    | 'main'
    | 'section'
    | 'th'
    | 'tr';

export type EnabledState = 'enabled' | 'disabled';

export type ExportType = 'csv' | 'excel';

export type FieldTypes = BaseFieldTypes | NonNestedFieldTypes;

export type NestedFieldTypes = BaseFieldTypes;

export type Page = 'next' | 'previous';

export type RowType = 'the row' | 'the floating row';

export type SupportedDevices = 'mobile' | 'tablet' | 'desktop' | 'HD desktop' | 'ultrawide desktop';

export type TableButton = 'First' | 'Next' | 'Previous' | 'Last';

export type TickStateType = 'ticks' | 'unticks';

export type TimeUnits = 'year' | 'month' | 'day' | null;

export type ViewMode = 'compact' | 'comfortable';
