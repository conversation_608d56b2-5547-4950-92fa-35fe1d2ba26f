import { Then } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { FilterEditorObject } from './filter-editor-object';

Then(/the user clicks the "([^"\n\r]*)" button of the filter editor field$/, async (value: string) => {
    const filterEditorObject = <FilterEditorObject>StaticStore.getStoredField(fieldTypes.filterEditor);
    await filterEditorObject.clickAddButton(value);
});

Then(
    /the "([^"\n\r]*)" (dropdown|text|switch) field of row "([^"\n\r]*)" in the filter editor field is (displayed|hidden)$/,
    async (name: string, fieldType: 'dropdown' | 'text' | 'date', rowId: number, cssState: 'displayed' | 'hidden') => {
        const filterEditorObject = <FilterEditorObject>StaticStore.getStoredField(fieldTypes.filterEditor);
        await filterEditorObject.expectRowFieldDisplayed({ name, rowId, reverse: cssState === 'hidden' });
    },
);

Then(
    /the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the filter editor field is (enabled|disabled)$/,
    async (name: string, fieldType: 'dropdown' | 'text' | 'date', rowId: number, cssState: 'enabled' | 'disabled') => {
        const filterEditorObject = <FilterEditorObject>StaticStore.getStoredField(fieldTypes.filterEditor);
        await filterEditorObject.expectRowFieldEnabled({ name, rowId, reverse: cssState === 'disabled' });
    },
);

Then(
    /the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the filter editor field$/,
    async (value: string, name: string, fieldType: 'dropdown' | 'text', rowId: number) => {
        const filterEditorObject = <FilterEditorObject>StaticStore.getStoredField(fieldTypes.filterEditor);
        await filterEditorObject.setFieldValue({ value, name, rowId });
    },
);

Then(
    /the user clears the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the filter editor field$/,
    async (name: string, fieldType: 'dropdown' | 'text', rowId: number) => {
        const filterEditorObject = <FilterEditorObject>StaticStore.getStoredField(fieldTypes.filterEditor);
        await filterEditorObject.clearRowFieldValue(name, rowId);
    },
);

Then(
    /the user clicks the "([^"\n\r]*)" switch of row "([^"\n\r]*)" in the filter editor field$/,
    async (name: string, rowId: number) => {
        const filterEditorObject = <FilterEditorObject>StaticStore.getStoredField(fieldTypes.filterEditor);
        await filterEditorObject.clickSwitch(name, rowId);
    },
);

Then(
    /the "([^"\n\r]*)" switch of row "([^"\n\r]*)" in the filter editor field is "([^"\n\r]*)"$/,
    async (name: string, rowId: number, value: string) => {
        const filterEditorObject = <FilterEditorObject>StaticStore.getStoredField(fieldTypes.filterEditor);
        await filterEditorObject.expectSwitchValue({ name, rowId, value });
    },
);

Then(
    /^the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the filter editor field is (displayed|hidden)$/,
    async (buttonName: string, rowId: number, cssState: 'displayed' | 'hidden') => {
        const filterEditorObject = <FilterEditorObject>StaticStore.getStoredField(fieldTypes.filterEditor);
        await filterEditorObject.expectEditorActionButtonDisplayed({
            buttonName,
            rowId,
            reverse: cssState === 'hidden',
        });
    },
);

Then(
    /^the user clicks the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the filter editor field$/,
    async (buttonName: string, rowId: number) => {
        const filterEditorObject = <FilterEditorObject>StaticStore.getStoredField(fieldTypes.filterEditor);
        await filterEditorObject.clickEditorActionButton(buttonName, rowId);
    },
);
