import { Key } from 'webdriverio';
import * as StaticStore from '../../static-store';
import { FieldObject } from '../field-object';

export class GraphiqlEditorFieldObject extends FieldObject {
    override async write({ content }: { content: string }) {
        const line = await this.find('.graphiql-container section.graphiql-query-editor');
        await line.click();
        await browser.keys(Key.Enter);
        await browser.keys(content);
    }

    async writeByLineNumber(content: string, lineNumber: number) {
        const line = await this.find(
            `.graphiql-container section.graphiql-query-editor div:nth-child(${lineNumber}) .CodeMirror-line`,
        );
        await line.click();
        await browser.keys(content);
    }

    async insertLine(lineNumber: number) {
        const line = await this.find(
            `.graphiql-container section.graphiql-query-editor div:nth-child(${lineNumber}) .CodeMirror-line`,
        );
        await line.click();
        // eslint-disable-next-line class-methods-use-this
        await browser.keys(Key.ArrowRight);
        await browser.keys(Key.Enter);
    }

    override async clearInput({ ignoreContext = false }: { ignoreContext?: boolean }) {
        const cssSelector = '.graphiql-container section.graphiql-query-editor .CodeMirror-line';
        const line = await this.find(cssSelector, ignoreContext);
        let lineCount = (await this.findAll(cssSelector, ignoreContext)).length;
        while (lineCount > 1 || (await line.getText()).replace(/(^[\s\u200b]*|[\s\u200b]*$)/g, '').trim() !== '') {
            await line.click();
            await browser.keys([Key.Ctrl, Key.Home]);
            await browser.pause(100);
            await browser.keys([Key.Shift, Key.Ctrl, Key.End]);
            await browser.pause(100);
            await browser.keys(Key.Delete);
            lineCount = (await this.findAll(cssSelector, ignoreContext)).length;
        }
    }

    override async expectValue({
        toBe,
        ignoreContext = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(
            '.graphiql-container section.graphiql-query-editor .CodeMirror-line',
            ignoreContext,
        );
        const element = await browser.$(selectorToUse);
        await element.waitForExist({ timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}` });
        await element.waitForDisplayed({
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
        });
        let textContent = '';
        try {
            await browser.waitUntil(
                async () => {
                    textContent = (await Promise.all((await browser.$$(selectorToUse)).map(e => e.getText())))
                        .map(t => t.trim())
                        .join(' ');
                    textContent = textContent
                        .replace(/(\r\n|\r|\n)/g, ' ')
                        .replace(/\u00A0/g, ' ')
                        .replace(/(^[\s\u200b]*|[\s\u200b]*$)/g, '')
                        .trim();
                    return (textContent || '') === (toBe || '').replace(/\\n/g, ' ');
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Expected value: "${toBe}", actual: "${textContent}".\nSelector: ${selectorToUse}`);
        }
    }

    override async setElementValueToStore({ keyValue }: { keyValue: string }) {
        const selectorToUse = this.getSelectorForOperation(
            '.graphiql-container section.graphiql-query-editor .CodeMirror-line',
            false,
        );
        const element = await browser.$(selectorToUse);
        await element.waitForExist({
            timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
            timeout: this.valueCheckTimeout,
        });
        await element.waitForDisplayed({
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
            timeout: this.valueCheckTimeout,
        });
        let textContent = '';
        try {
            textContent = (await Promise.all((await browser.$$(selectorToUse)).map(e => e.getText())))
                .map(t => t.trim())
                .join(' ');
            textContent = textContent
                .replace(/(\r\n|\r|\n)/g, ' ')
                .replace(/\u00A0/g, ' ')
                .replace(/(^[\s\u200b]*|[\s\u200b]*$)/g, '')
                .trim();
            StaticStore.storeObject(keyValue, textContent);
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Value not set".\nSelector: ${selectorToUse}`);
        }
    }
}
