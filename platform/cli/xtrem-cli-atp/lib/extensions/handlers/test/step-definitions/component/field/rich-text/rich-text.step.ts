import { Then } from '@cucumber/cucumber';
import { fieldTypes, getElementTypeSelector, takeScreenshot } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { RichTextFieldObject } from './rich-text-object';

// ----------
// Static store field steps
// ----------
Then(/^the value of the rich text field is "(.*)"$/, async (value: string) => {
    const field = <RichTextFieldObject>StaticStore.getStoredField(fieldTypes.richText);
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
    await field.loseFocus();
    await field.expectTextContent({
        toBe: storeValue || '',
        ignoreCase: false,
        cssSelector: getElementTypeSelector(fieldTypes.richText),
    });
});

Then(/^the user clicks in the "(.*)" button of the rich text field$/, async (buttonLabel: string) => {
    const field = <RichTextFieldObject>StaticStore.getStoredField(fieldTypes.richText);
    await field.clickFormattingButton(buttonLabel);
    await takeScreenshot();
});

Then(/^the user selects all the content of the rich text field$/, async () => {
    const field = <RichTextFieldObject>StaticStore.getStoredField(fieldTypes.richText);
    await field.selectAllContent();
});
