import * as utils from '../../../step-definitions-utils';
import { testGlobals } from '../../test-globals';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

export class FilterSelectFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        super({ fieldType: 'filter-select', identifier, lookupStrategy, context });
    }

    override async select(value: string) {
        if (testGlobals.device === 'mobile' || testGlobals.device === 'tablet') {
            await this.selectMobile(value);
        } else {
            await super.select(value);
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async selectMobile(value: string) {
        await utils.selectFromLookUpDialog(value);
    }

    async expectOptionsToContain({
        value,
        identifier,
        lookupStrategy,
    }: {
        value: string;
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
    }): Promise<void> {
        let options: string[] = [];
        if (testGlobals.device === 'mobile' || testGlobals.device === 'tablet') {
            // eslint-disable-next-line no-void
            void utils.expectElementToBeDisplayed({
                selector: `${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-lookup-dialog' })} [data-testid="e-card"]`,
            });
            await browser.waitUntil(
                async () => {
                    const items = await this.findAll(
                        `${utils.getDataTestIdSelector({ domSelector: '.e-lookup-dialog span' as any, dataTestIdValue: 'e-field-value' })}`,
                        true,
                    );
                    options = await Promise.all(items.map(i => i.getText()));
                    return items.length > 0;
                },
                {
                    timeoutMsg: `No lookup items were found for ${identifier} ${lookupStrategy} field.`,
                    timeout: this.timeoutWaitFor,
                },
            );
        } else {
            await browser.waitUntil(
                async () => {
                    const items = await this.findAll(
                        `${utils.getDataTestIdSelector({ domSelector: 'ul', dataTestIdValue: 'e-ui-select-dropdown' })} li`,
                    );
                    options = await Promise.all(items.map(i => i.getText()));
                    return items.length > 0 && options[0] !== 'Loading...';
                },
                {
                    timeoutMsg: `No dropdown items were found for ${identifier} ${lookupStrategy} field.`,
                    timeout: this.timeoutWaitFor,
                },
            );
        }
        const isOptionIncluded = options.includes(value);
        if (!isOptionIncluded) {
            throw new Error(`Could not find option '${value}' in [${options.join(', ')}]`);
        }
    }

    async clickOnFieldLookupButton() {
        await this.click('button[data-testid^="e-ui-select-lookup-button"]');
        await waitForPromises(500, 'open field lookup');
    }
}
