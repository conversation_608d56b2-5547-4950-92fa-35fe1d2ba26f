export interface PDFConfiguration {
    PageLayout: PageLayout;
    TextFormat: TextFormat;
}

export interface PageLayout {
    PageSize: string;
    LeftMargin: number;
    RightMargin: number;
    TopMargin: number;
    BottomMargin: number;
    LogoPath: string;
    LogoLocation: { Vertical: string; Horizontal: string };
    SignatureBoxLocation: { Vertical: string; Horizontal: string };
    UseExecutorAsTitle: boolean;
    NewPagePerScenario: boolean;
    SortFeaturesByRunOrder: boolean;
    DisplayTextAttachment: boolean;
}

export interface TextFormat {
    TitleFont: string;
    TitleSize: number;
    TitleFormatOptions: TextFormatOptions;
    HeadingFont: string;
    HeadingSize: number;
    HeadingFormatOptions: TextFormatOptions;
    EntryFont: string;
    EntrySize: number;
    EntryFormatOptions: TextFormatOptions;
    FeatureFont: string;
    FeatureSize: number;
    FeatureFormatOptions: TextFormatOptions;
    ScenarioFont: string;
    ScenarioSize: number;
    ScenarioFormatOptions: TextFormatOptions;
    ScenarioParametersFont: string;
    ScenarioParametersSize: number;
    ScenarioParametersFormatOptions: TextFormatOptions;
    StepFont: string;
    StepSize: number;
    StepFormatOptions: TextFormatOptions;
    ErrorFont: string;
    ErrorSize: number;
    ErrorFormatOptions: TextFormatOptions;
    TextAttachmentFont: string;
    TextAttachmentSize: number;
    TextAttachmentFormatOptions: TextFormatOptions;
}

export interface TextFormatOptions {
    Bold: boolean;
    Italic: boolean;
    Underline: boolean;
    Color: string;
}
