/* eslint-disable @typescript-eslint/naming-convention */
import { ElementContext, expectElementToBeDisplayed, KEY, LookupStrategy } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';

export interface IContentTableObject {
    identifier: string;
    lookupStrategy: LookupStrategy;
    context?: ElementContext;
    fieldType?: string;
}

export class ContentTableObject extends FieldObject {
    constructor({ identifier, lookupStrategy, context, fieldType }: IContentTableObject) {
        super({ fieldType: fieldType ?? 'content table', identifier, lookupStrategy, context });
    }

    async clickAddButton(value: string) {
        const addButtonSelector = 'button[data-testid="add-item-button"]';
        const addButton = await this.find(addButtonSelector);
        await this.expectTextContent({
            toBe: value,
            ignoreCase: true,
            cssSelector: addButtonSelector,
            ignoreContext: true,
        });
        await addButton.click();
    }

    async setFieldValue({ value, name, rowId }: { value: string; name: string; rowId: number }) {
        const cssSelector = `[data-testid~="e-widget-editor-${name}-${rowId}"]`;
        await this.write({ content: value, cssSelector, ignoreContext: true });
        await ContentTableObject.press(KEY.Enter);
    }

    async expectRowFieldDisplayed({ name, rowId, reverse = false }: { name: string; rowId: number; reverse: boolean }) {
        const element = await this.find(`[data-testid~="e-widget-editor-${name}-${rowId}"]`, true);
        await this.expectToBeDisplayed(`${element.selector.toString()}`, reverse);
    }

    async expectRowFieldEnabled({ name, rowId, reverse = false }: { name: string; rowId: number; reverse: boolean }) {
        const element = await this.find(`[data-testid~="e-widget-editor-${name}-${rowId}"]`, true);
        await this.expectToBeEnabled(`${element.selector.toString()}`, reverse);
    }

    async expectActionButtonDisplayed({
        buttonName,
        rowId,
        reverse = false,
    }: {
        buttonName: string;
        rowId: number;
        reverse: boolean;
    }) {
        const actionButton = await this.find(
            `[data-element="flat-table-row"][data-testid="${rowId}"] [data-testid="flat-table-${buttonName}-button"]`,
            true,
        );
        await expectElementToBeDisplayed({ selector: actionButton.selector.toString(), reverse });
    }

    async clickActionButton(buttonName: string, rowId: number) {
        const actionButton = await this.find(
            `[data-element="flat-table-row"][data-testid="${rowId}"] [data-testid="flat-table-${buttonName}-button"]`,
            true,
        );
        await actionButton.click();
    }

    async clearRowFieldValue(name: string, rowId: number) {
        const selectorToUse = `[data-testid~="e-widget-editor-${name}-${rowId}"]`;
        await this.clearInput({ cssSelector: selectorToUse });
    }

    async expectRowFieldValue({ fieldName, rowId, value }: { fieldName: string; rowId: number; value: string }) {
        const selectorToUse = `[data-testid="e-widget-editor-${fieldName}-${rowId}"]`;
        const rowField = await this.find(selectorToUse, true);
        await this.expectValue({ toBe: value, cssSelector: rowField.selector.toString() });
    }
}
