import * as utils from '../../../../step-definitions-utils';
import AbstractPageObject from '../../../abstract-page-object';
import { DropDownMenuObject } from '../../../dropdown-menu-object';
import * as StaticStore from '../../../static-store';
import { PodCollectionObject } from '../pod-collection/pod-collection-object';

const getFieldSelector = () => {
    const field = <PodCollectionObject>StaticStore.getStoredField(utils.fieldTypes.podCollection);
    return field.cssSelector;
};

export class PodCollectionItemObject extends AbstractPageObject {
    public readonly dropDownMenu: DropDownMenuObject;

    constructor(itemId: string) {
        super(
            `${getFieldSelector()} ${utils.getMultipleDataTestIdSelector('div', [
                'e-pod-collection-field',
                `e-pod-collection-item-${itemId}`,
            ])}`,
        );
        this.dropDownMenu = new DropDownMenuObject(this.cssSelector);
    }

    async selectPodCollectionIcon(name: string) {
        const iconBtn = await this.find(
            utils.getDataTestIdSelector({
                domSelector: 'button',
                dataTestIdValue: `e-pod-collection-${utils.formatString(name)}`,
            }),
        );
        await utils.waitForElementToBeDisplayed({
            name: `pod collection "${name}" icon`,
            selector: iconBtn.selector.toString(),
        });
        await iconBtn.click();
    }

    async selectMainCheckbox(action: 'selects' | 'unselects') {
        const element = await this.get();
        await element.scrollIntoView();
        await element.waitForDisplayed();

        const selectCheckBoxSelector = `${utils.getDataTestIdSelector({
            domSelector: 'span',
            dataTestIdValue: 'e-pod-collection-select',
        })} [data-component="checkbox"] input`;
        const selectCheckBox = await this.findOrFail(selectCheckBoxSelector);

        const selectionState = await selectCheckBox.isSelected();
        if ((action === 'selects' && selectionState) || (action === 'unselects' && !selectionState)) {
            return;
        }
        await selectCheckBox.click();
    }
}
