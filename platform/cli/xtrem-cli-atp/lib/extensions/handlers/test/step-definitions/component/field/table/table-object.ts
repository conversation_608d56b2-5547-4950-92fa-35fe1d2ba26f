/* eslint-disable @typescript-eslint/naming-convention */
import * as utils from '../../../step-definitions-utils';
import { FilterObject } from '../../filter-object';
import { FieldObject } from '../field-object';
import { TableAction } from './tableMethods/table-action';
import { TableClick } from './tableMethods/table-click';
import { TableColumns } from './tableMethods/table-columns';
import { TableExpect } from './tableMethods/table-expect';
import { TableRows } from './tableMethods/table-rows';
import { TableValidate } from './tableMethods/table-validate';

interface ITableObject {
    identifier: string;
    lookupStrategy: utils.LookupStrategy;
    context?: utils.ElementContext;
    subtype?: 'table' | 'table-summary' | 'tree';
}

export class TableObject extends FieldObject {
    public action: TableAction;

    public tableClick: TableClick;

    public columns: TableColumns;

    public expect: TableExpect;

    public filter: FilterObject;

    public rows: TableRows;

    public validate: TableValidate;

    constructor({ identifier, lookupStrategy, context, subtype = 'table' }: ITableObject) {
        super({ fieldType: subtype, identifier, lookupStrategy, context });
        this.action = new TableAction(this);
        this.tableClick = new TableClick(this);
        this.columns = new TableColumns(this);
        this.expect = new TableExpect(this);
        this.filter = new FilterObject(this);
        this.rows = new TableRows(this);
        this.validate = new TableValidate(this);
    }
}
