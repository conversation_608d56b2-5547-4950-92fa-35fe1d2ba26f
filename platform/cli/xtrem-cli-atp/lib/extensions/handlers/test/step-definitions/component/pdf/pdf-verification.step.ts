/* pdf steps definition file */
import { Then, When } from '@cucumber/cucumber';
import { atpEnv, LignAlignment, takeScreenshot } from '../../step-definitions-utils';
import { pdfUtility } from '../pdf-utility';
import * as StaticStore from '../static-store';
import { testGlobals } from '../test-globals';

When(/^the user reads the "(.*)" pdf file$/, async (pdfFile: string) => {
    try {
        if (atpEnv.downloadFolder)
            await pdfUtility.readPDF(atpEnv.downloadFolder, StaticStore.getUserdefinedKeyValueFromStore(pdfFile));
    } catch (error) {
        testGlobals._CURRENT_STEP_SCREENSHOT = true;
        throw new Error(error);
    }
});

When(
    /^the user stores the file name from the preview toolbar with the key "(.*)"$/,
    async (pdfFileVariable: string) => {
        const fileNameField = await $('[data-testid="e-preview-field-toolbar-file-name"]');
        await fileNameField.waitForExist();
        await browser.waitUntil(async () => {
            return (await fileNameField.getText()) !== '';
        });
        const str = await fileNameField.getText();
        await takeScreenshot();
        console.log('File name stored:', str);
        StaticStore.storeObject(pdfFileVariable, str);
    },
);
When(
    // eslint-disable-next-line @sage/redos/no-vulnerable
    /^the user renames the file "(.*)" with name containing "(.*)"$/,
    async (fileName: string, filePattern: string) => {
        try {
            await pdfUtility.renameFile(fileName, filePattern);
        } catch (error) {
            testGlobals._CURRENT_STEP_SCREENSHOT = true;
            throw new Error(error);
        }
    },
);

Then(/^the user sets the pdf threshold to "(.*)" pt$/, (threshold: number) => {
    pdfUtility.setThreshold(threshold);
});

Then(/^the user resets the pdf threshold$/, () => {
    pdfUtility.resetThreshold();
});

Then(/^the user sets the pdf vertical alignment to (top|center|bottom)$/, (alignment: LignAlignment) => {
    pdfUtility.setAlignment(alignment);
});

Then(/^the user resets the pdf vertical alignment$/, () => {
    pdfUtility.resetAlignment();
});

Then(/^the user verifies the pdf report contains$/, (requiredText: string) => {
    try {
        pdfUtility.assertTextInPDF(StaticStore.getUserdefinedKeyValueFromStore(requiredText)); // await Pages.emPage.replaceByStoredValues(requiredText));
    } catch (error) {
        testGlobals._CURRENT_STEP_SCREENSHOT = true;
        throw new Error(error);
    }
});

When(/^the user clicks the "(.*)" action in the preview toolbar$/, async (actionName: string) => {
    await pdfUtility.clickButton(actionName);
});
