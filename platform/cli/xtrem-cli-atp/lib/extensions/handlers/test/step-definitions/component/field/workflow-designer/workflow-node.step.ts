import { When } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { WorkflowDesignerFieldObject } from './workflow-designer-object';
import { WorkflowNodeObject } from './workflow-node-object';

When(
    /^the user selects the titled "(.*)" workflow node in the workflow designer field$/,
    async (workflowNodeName: string) => {
        const field = <WorkflowDesignerFieldObject>StaticStore.getStoredField(fieldTypes.workflowDesigner);
        const node = await field.getWorkflowNode(workflowNodeName);
        await StaticStore.storeField(fieldTypes.workflowNode, node);
    },
);

When(
    /^the user clicks the "(.*)" icon of the workflow node in the workflow designer field$/,
    async (iconName: string) => {
        const field = <WorkflowNodeObject>StaticStore.getStoredField(fieldTypes.workflowNode);
        await field.clickNodeIcon(iconName);
    },
);

When(
    /^the user clicks the "(.*)" action of the workflow node in the workflow designer field$/,
    async (actionName: string) => {
        const field = <WorkflowNodeObject>StaticStore.getStoredField(fieldTypes.workflowNode);
        await field.clickNodeAction(actionName);
    },
);

When(
    /^the user clicks the "(.*)" (left|right) button of the workflow node in the workflow designer field$/,
    async (buttonName: string, side: 'left' | 'right') => {
        const field = <WorkflowNodeObject>StaticStore.getStoredField(fieldTypes.workflowNode);
        await field.clickSideButton(buttonName, side);
    },
);
