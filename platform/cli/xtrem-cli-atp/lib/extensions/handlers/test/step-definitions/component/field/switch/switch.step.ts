import { Then, When } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { SwitchFieldObject } from './switch-object';

// ----------
// Static store field steps
// ----------
When(/^the user clicks in the switch field$/, async () => {
    const field = <SwitchFieldObject>StaticStore.getStoredField(fieldTypes.switch);
    await field.clickOnSwitch();
});

When(/^the user turns the switch field "(.*)"$/, async (value: string) => {
    const field = <SwitchFieldObject>StaticStore.getStoredField(fieldTypes.switch);
    await field.setSwitchToStatus(value);
});

Then(/^the switch field is set to "(.*)"$/, async (status: string) => {
    const field = <SwitchFieldObject>StaticStore.getStoredField(fieldTypes.switch);
    await field.loseFocus();
    await field.expectStatusToBe(status);
});

Then(/^the title help of the switch field is "(.*)"$/, async (toBe: string) => {
    const field = <SwitchFieldObject>StaticStore.getStoredField(fieldTypes.switch);
    await field.loseFocus();
    await field.expectTitleHelpToBe(toBe);
});

Then(/^the size of the switch field is large$/, async () => {
    const field = <SwitchFieldObject>StaticStore.getStoredField(fieldTypes.switch);
    await field.loseFocus();
    await field.expectLargeSize();
});
