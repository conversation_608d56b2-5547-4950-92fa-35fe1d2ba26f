import { Then, When } from '@cucumber/cucumber';
import {
    ElementContext,
    LookupStrategy,
    NestedFieldTypes,
    waitForElementToBeFound,
} from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { NestedFieldObject } from '../table/table-nested-field/table-nested-field-object';
import { SelectedRowDetails, rowIndexSelector } from '../table/table-nested-field/table-nested-field-utils';
import { TableObject } from '../table/table-object';
import { scrollToTableColumn, scrollToTableRow } from '../table/tableUtils';
import { waitForPromises } from '../wait-util';

When(
    /^the user selects the "(.*)" (bound|labelled) tree field on (the main page|a modal|a full width modal|the sidebar|the navigation panel|the detail panel)$/,
    async (identifier: string, lookupStrategy: LookupStrategy, context: ElementContext) => {
        const tree = new TableObject({
            identifier,
            lookupStrategy,
            context,
            subtype: 'tree',
        });
        await tree.dismissAllNotification();
        await (
            await tree.get()
        ).waitForExist({
            timeoutMsg: `Expected element could not be found: "${identifier}" tree field.\nSelector: ${tree.cssSelector}`,
        });
        StaticStore.storeObject(StaticStore.StoredKeys.TREE, tree);
    },
);

When(
    /^the user selects the (?:(?:row ([0-9]*))|(floating row)) of the tree field$/,
    async (rowNumber: number, floatingRow: string | null) => {
        const tree = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TREE);
        await tree.expect.waitForTableStopLoading();
        await scrollToTableRow(`${tree.cssSelector} ${rowIndexSelector(rowNumber, floatingRow !== null)}`, rowNumber);
        await waitForElementToBeFound({
            name: 'row to select',
            selector: `${tree.cssSelector} ${rowIndexSelector(rowNumber, floatingRow !== null)}`,
        });
        const selectedRow: SelectedRowDetails = {
            isFloatingRow: floatingRow !== null,
            rowNumber: rowNumber ?? 0, // Provide a default value for floating rows
        };
        StaticStore.storeObject<SelectedRowDetails>(StaticStore.StoredKeys.ROW, selectedRow);
    },
);

/* --------- Deprecated Steps ---------*/

// /**
//  * @deprecated the user selects the row $4 of the table field\nthe value of the "$1" $2 nested $3 field of the selected row in the tree field is "$6"
//  */
// Then(
//     /^the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of (?:(?:row ([0-9]*))|(the floating row)) in the tree field is "(.*)"$/,
//     async (
//         columnName: string,
//         nestedLookupStrategy: LookupStrategy,
//         fieldType: NestedFieldTypes,
//         rowNumber: number | null,
//         floatingRow: string | null,
//         expectedValue: string,
//     ) => {
//         //     const tree = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TREE);
//         //     const field = new NestedFieldObject({
//         //         tableSelector: tree.cssSelector,
//         //         columnName,
//         //         nestedLookupStrategy,
//         //         rowNumber: floatingRow != null ? 1 : rowNumber!,
//         //         isPinnedToTop: floatingRow != null,
//         //     });
//         //     await field.waitForTableStopLoading();
//         //     const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedValue);
//         //     if (!(await (await $(field.cssSelector)).isExisting()))
//         //         await scrollToTableColumn({
//         //             tableSelector: tree.cssSelector,
//         //             columnName,
//         //             lookupStrategy: nestedLookupStrategy,
//         //         });
//         //     await field.expectNestedValue({ toBe: storeValue, fieldType, columnName, rowNumber });
//     },
// );

// /**
//  * @deprecated the user selects the row $2 of the table field\nthe user $1 the selected row of the tree field
//  */
// When(
//     /^the user (expands|collapses) row ([0-9]*) of the tree field$/,
//     async (expandOrCollapse: 'expands' | 'collapses', rowNumber: number) => {
//         const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TREE);
//         await table.expect.waitForTableStopLoading();
//         if (expandOrCollapse === 'expands') {
//             await table.rows.expandRow(rowNumber);
//         } else {
//             await table.rows.collapseRow(rowNumber);
//         }
//         await waitForPromises(900, 'Wait for group');
//     },
// );

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of the selected row in the tree field is "(.*)"$/,
    async (
        columnName: string,
        nestedLookupStrategy: LookupStrategy,
        fieldType: NestedFieldTypes,
        expectedValue: string,
    ) => {
        const tree = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TREE);
        const { isFloatingRow, rowNumber }: SelectedRowDetails = StaticStore.getStoredObject(
            StaticStore.StoredKeys.ROW,
        );
        const field = new NestedFieldObject({
            tableSelector: tree.cssSelector,
            columnName,
            nestedLookupStrategy,
            rowNumber,
            isFloatingRow,
        });
        await field.waitForTableStopLoading();
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedValue);
        if (!(await (await $(field.cssSelector)).isExisting()))
            await scrollToTableColumn({
                tableSelector: tree.cssSelector,
                columnName,
                lookupStrategy: nestedLookupStrategy,
            });
        await field.expectNestedValue({ toBe: storeValue, fieldType, columnName, rowNumber });
    },
);

When(
    /^the user (expands|collapses) the selected row of the tree field$/,
    async (expandOrCollapse: 'expands' | 'collapses') => {
        const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TREE);
        await table.expect.waitForTableStopLoading();
        const { rowNumber }: SelectedRowDetails = StaticStore.getStoredObject(StaticStore.StoredKeys.ROW);
        if (expandOrCollapse === 'expands') {
            await table.rows.expandRow(rowNumber);
        } else {
            await table.rows.collapseRow(rowNumber);
        }
        await waitForPromises(900, 'Wait for group');
    },
);
