import * as moment from 'moment';
import { expectElementToBeDisplayed } from '../../step-definitions-utils';

export const DATE_FORMAT_MAP: { [key: string]: string } = {
    'pt-PT': 'DD/MM/YYYY',
    'fr-FR': 'DD/MM/YYYY',
    'es-ES': 'DD/MM/YYYY',
    'en-GB': 'DD/MM/YYYY',
    'pl-PL': 'DD.MM.YYYY',
    'de-DE': 'DD.MM.YYYY',
    'en-US': 'MM/DD/YYYY',
    'zh-CN': 'YYYY/MM/DD',
};

export async function getUserLocale(): Promise<string> {
    const localeFromState = await browser.execute<string, []>(`window?.__XTREM_STATE?.applicationContext?.locale`);
    if (localeFromState) {
        return localeFromState;
    }
    return browser.execute<string, []>(`window.document.documentElement.lang`);
}

export async function openDatePicker(obj: any) {
    const isDayPickerOpen = await (await obj.find('.rdp-root', true)).isExisting();
    if (!isDayPickerOpen) {
        const dateField = await obj.find('[data-component="date"] [data-element="input"]');
        await dateField.isExisting();
        await dateField.click();
        await expectElementToBeDisplayed({ selector: '.rdp-root', name: 'Date picker is not displayed' });
    }
}

export function formatDate(date: Date, lang: string): string {
    return date.toLocaleString(lang, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
    });
}

export function formatDateNth(date: string, lang: string): string {
    let dateField = date;
    if (date.match(/st|nd|rd|th/g)) {
        dateField = getNthDay(dateField, lang).toLocaleString();
    }
    return dateField;
}

export function getDateAbbreviation(lang: string, date: Date): string {
    let dayAbbreviation = date.toLocaleDateString(lang, { weekday: 'short' }).substring(0, 3);
    const day = date.getDate();
    const monthAbbreviation = date.toLocaleDateString(lang, { month: 'short' }).substring(0, 3);
    const year = date.getFullYear();
    const abbreviationMap: { [key: string]: { [key: string]: string } } = {
        'pl-PL': {
            'pt.': 'pią',
            'wt.': 'wto',
            'śr.': 'śro',
        },
    };

    if (lang in abbreviationMap && dayAbbreviation in abbreviationMap[lang]) {
        dayAbbreviation = abbreviationMap[lang][dayAbbreviation];
    }
    return `${dayAbbreviation} ${day} ${monthAbbreviation} ${year}`;
}

export function getNthDay(date: string, lang: string) {
    let formattedDate;
    const dynamicDateString = date.split('+');
    const dayPosition = date.match(/(\d+)/g);
    const strRegex = /[a-zA-Z]+/g;
    const result = strRegex.test(dynamicDateString[1]);

    if ((dynamicDateString.length > 1 && result === true) || !dayPosition) {
        throw new Error('Expected date format is incorrect.');
    }

    try {
        const dynamicDate = date.split(' ');
        const day = dynamicDate[1];
        const validDays = moment.weekdays();

        if (!validDays.includes(day)) {
            throw new Error(`Invalid day name: ${day}. Please provide a valid weekday.`);
        }

        const daysArray = [];
        let currentDay = moment().startOf('day');
        const nextYear = moment().add(1, 'year').year();

        while (currentDay.year() <= nextYear) {
            if (currentDay.format('dddd') === day) {
                daysArray.push(currentDay.clone());
            }
            currentDay = currentDay.add(1, 'day');
        }

        const formulaDate = daysArray[parseInt(dayPosition[0], 10) - 1];

        if (!formulaDate) {
            throw new Error(`Invalid position: ${dayPosition[0]}. Not enough days found.`);
        }

        if (date.includes('+') && dayPosition[1] !== undefined) {
            formattedDate = formulaDate.add(parseInt(dayPosition[1], 10), 'days');
        } else {
            formattedDate = formulaDate;
        }

        return formatDate(formattedDate.toDate(), lang);
    } catch (error) {
        throw new Error(`Error processing date: ${error.message}`);
    }
}

/**  Dynamic dates calculation based on the argument defined in parameter (e.g L, M/T/Y etc)
 * Refer to https://confluence.sage.com/x/gssrF for standalone date fields
 * Refer to https://confluence.sage.com/x/sBEAFg for nested date fields in table
 */
export function changeDateWithFormat(format: string, lang: string): string {
    const today: Date = new Date();
    let date: Date | null = null;

    switch (format) {
        case 'T':
            date = today;
            break;
        case 'L':
            date = new Date(today.getFullYear(), today.getMonth() + 1, 0); // Last day of the current month
            break;
        default:
            break;
    }

    if (!date) {
        if (format.match(/^T[+-][\d]+$/)) {
            let shift = 0;
            const partParts = format.match(/T([+-])([\d]+)/);
            if (partParts && partParts[1] && partParts[2]) {
                shift = parseInt(partParts[2], 10);
                if (partParts[1] === '-') {
                    shift *= -1;
                }
            }
            date = moment().add(shift, 'days').toDate();
        } else {
            const localeFormat = moment.localeData(lang).longDateFormat('L');
            const inputFormatParts = format.split(/[/.]/gi);
            const localeFormatParts = localeFormat.split(/[/.]/gi);
            let m = moment();
            let i = 0;
            let shiftToEndOfMonth = false;
            // eslint-disable-next-line no-restricted-syntax
            for (const inputFormatPart of inputFormatParts) {
                const localeFormatPart = localeFormatParts[i];

                let shift = 0;
                const partParts = inputFormatPart.match(/\([\w]+([+-])([\d]+)\)/);
                if (partParts && partParts[1] && partParts[2]) {
                    shift = parseInt(partParts[2], 10);
                    if (partParts[1] === '-') {
                        shift *= -1;
                    }
                }

                if (localeFormatPart === 'DD') {
                    if (inputFormatPart === 'L') {
                        shiftToEndOfMonth = true;
                    } else if (inputFormatPart.includes('+') || inputFormatPart.includes('-')) {
                        m = m.add(shift, 'days');
                    } else if (inputFormatPart !== 'T') {
                        m = m.date(parseInt(inputFormatPart, 10));
                    }
                }
                if (localeFormatPart === 'MM') {
                    if (inputFormatPart.includes('+') || inputFormatPart.includes('-')) {
                        m = m.add(shift, 'months');
                    } else if (inputFormatPart !== 'M') {
                        m = m.month(parseInt(inputFormatPart, 10) - 1);
                    }
                }
                if (localeFormatPart === 'YYYY') {
                    if (inputFormatPart.includes('+') || inputFormatPart.includes('-')) {
                        m = m.add(shift, 'years');
                    } else if (inputFormatPart !== 'Y') {
                        m = m.year(parseInt(inputFormatPart, 10));
                    }
                }

                i += 1;
            }
            if (shiftToEndOfMonth) {
                m = m.endOf('month');
            }
            date = m.toDate();
        }
    }

    const formattedDate: string = formatDate(date, lang);
    return formattedDate;
}

export function parseDateString(
    date: string,
    lang: string,
    withoutSeparators = false,
): { day: string; month: string; year: string } {
    let dateFormat = DATE_FORMAT_MAP[lang];

    if (!dateFormat) {
        throw new Error(`Expected language ${lang} not supported.`);
    }

    if (withoutSeparators) {
        const separator = /\.|\//g.exec(dateFormat);
        if (!separator) {
            throw new Error('Date separator not found');
        }
        dateFormat = dateFormat.replaceAll(separator[0], '');
    }

    const dateIndex = dateFormat.indexOf('DD');
    const monthIndex = dateFormat.indexOf('MM');
    const yearIndex = dateFormat.indexOf('YYYY');
    const day = date.slice(dateIndex, dateIndex + 2);
    const month = date.slice(monthIndex, monthIndex + 2);
    const year = date.slice(yearIndex, yearIndex + 4);

    return {
        day,
        month,
        year,
    };
}

export function formatDateToDateString({
    day,
    month,
    year,
    lang,
}: {
    day: string;
    month: string;
    year: string;
    lang: string;
}): string {
    const dateFormat = DATE_FORMAT_MAP[lang];

    if (!dateFormat) {
        throw new Error(`Expected language ${lang} not supported.`);
    }

    return dateFormat.replace('DD', day).replace('MM', month).replace('YYYY', year);
}
