import { camelCase, kebabCase } from 'lodash';
import { waitForElementToBeDisplayed } from '../../step-definitions-utils';
import AbstractPageObject from '../abstract-page-object';
import { DialogDisplayTypes, DialogObject } from '../container/dialog/dialog-object';

export class Dashboard extends AbstractPageObject {
    constructor() {
        super('.e-dashboard');
    }

    async expectDashboardToBeDisplayed() {
        const dashboardElement = await this.get();
        await waitForElementToBeDisplayed({ name: 'dashboardElement', selector: dashboardElement.selector.toString() });
    }

    async expectDashboardTitle(title: string, isEmpty: boolean = false) {
        const selector = `[data-testid="e-dashboard${isEmpty ? '-empty-no-dashboard' : ''}-title"]`;
        await this.expectTextContent({ toBe: title, ignoreCase: false, cssSelector: selector, ignoreContext: false });
    }

    async expectDashboardSubtitle(subtitle: string, isEmpty: boolean = false) {
        const selector = `[data-testid="e-dashboard${isEmpty ? '-empty-no-dashboard' : ''}-subtitle"]`;
        await this.expectTextContent({
            toBe: subtitle,
            ignoreCase: false,
            cssSelector: selector,
            ignoreContext: false,
        });
    }

    async expectBlankDashboardSubtitle(subtitle: string) {
        await this.expectTextContent({
            toBe: subtitle,
            ignoreCase: false,
            cssSelector: '[data-testid~="e-dashboard-empty-no-widgets-subtitle"]',
            ignoreContext: false,
        });
    }

    async clickCreateDashboard() {
        await this.click('[data-testid~="e-dashboard-create"]');
    }

    // eslint-disable-next-line class-methods-use-this
    async expectCreationDialogToAppear() {
        await browser.waitUntil(async () => {
            const dataState = await (await $('.e-dashboard-create-dialog')).getAttribute('data-state');
            const modalDone = await $(`.modal-background-enter-done`);
            const isExisting = await modalDone.isExisting();
            return dataState === 'open' && isExisting;
        });
    }

    async expectDialogDescription(description: string) {
        await this.expectTextContent({
            toBe: description,
            ignoreCase: false,
            cssSelector: '[data-testid~="e-dashboard-create-dialog-description"]',
            ignoreContext: true,
        });
    }

    async expectBlankDashboardTemplate(templateName: string) {
        await this.expectTextContent({
            toBe: templateName,
            ignoreCase: false,
            cssSelector: '[data-testid~="e-selection-card-title"]',
            ignoreContext: true,
        });
    }

    // deprecated

    async getTemplateImage(templateIndex: number) {
        const cardTemplateImages = await this.findAll('[data-testid="e-selection-card-image"]', true);
        return cardTemplateImages[templateIndex];
    }

    async getTemplateByIndex(templateIndex: number) {
        const cssSelector = '.e-selection-card';
        const cardTemplateImages = await this.findAll(cssSelector, true);
        const template = cardTemplateImages[templateIndex - 1];
        if (!template || !(await template.isExisting())) {
            throw new Error(
                `Expected element could not be found: "${templateIndex}" template index\ncssSelector: "${cssSelector}"`,
            );
        }
        return template;
    }

    async getTemplateSelected() {
        const cardTemplateSelected = await this.find('.e-selection-card-selected', true);
        return cardTemplateSelected;
    }

    async getTemplateByTitle(templateTitle: string) {
        const cssSelector = `[aria-label="${templateTitle}"]`;
        const template = await this.find(cssSelector, true);
        if (!(await template.isExisting())) {
            throw new Error(`Expected element could not be found: "${templateTitle}"\ncssSelector: "${cssSelector}"`);
        }

        return template;
    }

    async selectTemplate(templateIndex: number) {
        const template = await this.getTemplateByIndex(templateIndex);
        await template.waitForDisplayed();
        if (!(await this.isSelectedByIndex(templateIndex))) await template.click();
    }

    async selectTemplateByTitle(templateTitle: string) {
        const template = await this.getTemplateByTitle(templateTitle);
        await template.waitForExist();
        if (!(await this.isSelectedByTitle(templateTitle))) await template.click();
    }

    async isSelectedByIndex(templateIndex: number) {
        const template = await this.getTemplateByIndex(templateIndex);
        const isSelected = this.isSelected(template);

        return isSelected;
    }

    async isSelectedByTitle(templateTitle: string) {
        const template = await this.getTemplateByTitle(templateTitle);
        const isSelected = this.isSelected(template);

        return isSelected;
    }

    async isSelected(template: WebdriverIO.Element) {
        if (!(await (await this.getTemplateSelected()).isExisting())) return false;
        if (!(await template.isExisting())) return false;
        const testId = await template.getAttribute('data-testid');
        const testIdSelected = await (await this.getTemplateSelected()).getAttribute('data-testid');
        return testId === testIdSelected;
    }

    async expectTemplateSelected(templateIndex: number) {
        if (!(await this.isSelectedByIndex(templateIndex))) {
            throw new Error(`Expected index:${templateIndex} not selected`);
        }
    }

    async expectTemplateSelectedByTitle(templateTitle: string) {
        if (!(await this.isSelectedByTitle(templateTitle))) {
            throw new Error(`Expected title:${templateTitle} not selected`);
        }
    }

    expectCreationDialogButtonEnabled(buttonName: string, reverse: boolean = false): Promise<void> {
        switch (buttonName) {
            case 'next':
                return this.expectToBeEnabled('[data-testid="e-dashboard-dialog-create-next-button"]', reverse);
            case 'cancel':
                return this.expectToBeEnabled('[data-testid="e-dashboard-dialog-create-cancel-button"]', reverse);
            default:
                throw new Error(`Expected button name to be "next" or "cancel", received: "${buttonName}"`);
        }
    }

    async clickCreationDialogButton(buttonName: string) {
        await this.click(`[data-testid="e-dashboard-dialog-create-${kebabCase(buttonName)}-button"]`, true);
    }

    async expectTabToBeDisplayed(tabIdentifier: string, reverse: boolean = false) {
        await this.expectToBeDisplayed(`[data-testid~="e-xtrem-tab-${camelCase(tabIdentifier)}"]`, reverse);
    }

    async clickTab(tabIdentifier: string) {
        await this.expectTabToBeDisplayed(tabIdentifier);
        await this.scrollTo({ selector: `[data-testid~="e-xtrem-tab-${camelCase(tabIdentifier)}"]` });
        await this.click(`[data-testid~="e-xtrem-tab-${camelCase(tabIdentifier)}"]`);
    }

    async openActionMenu() {
        await browser.waitUntil(
            async () => {
                const actionMenu = await $(
                    '[data-testid="e-dashboard-actions"] [data-element="action-popover-button"]',
                );
                await actionMenu.waitForExist({
                    timeout: this.timeoutWaitFor,
                    timeoutMsg: `Element not displayed.\nSelector: ${actionMenu.selector}`,
                });
                await actionMenu.scrollIntoView();
                await actionMenu.moveTo();
                await actionMenu.click();
                await this.expectToAppear({ cssSelector: '[data-component="action-popover"]', ignoreContext: true });
                const isDisplayed = await (await $('[data-component="action-popover"]')).isDisplayed();
                return isDisplayed;
            },
            { timeout: this.timeoutWaitFor, timeoutMsg: 'Cannot open action menu' },
        );
    }

    async clickCrudButton(buttonName: string) {
        const selectorToUse = `[data-testid~="e-dashboard-action-${camelCase(buttonName)}"]`;
        await this.expectToAppear({ cssSelector: selectorToUse, ignoreContext: true });
        await this.click(selectorToUse, true);
    }

    async deleteAllDashboards() {
        await (await this.get()).waitForDisplayed();
        while (true) {
            const emptyDashboardIndicator = await this.find('[data-testid="e-dashboard-empty-no-dashboard"]');
            const isEmptyDashboard = await emptyDashboardIndicator.isExisting();
            if (isEmptyDashboard) {
                return;
            }

            await this.openActionMenu();
            await this.clickCrudButton('delete');
            const dialog = new DialogObject(DialogDisplayTypes.mainpage);
            const dialogButton = await dialog.getDialogButton('OK');
            await dialogButton.click();
            await browser.pause(1000);
        }
    }

    async expectAddedDashboardWidget(widgetName: string, reverse: boolean = false) {
        await this.expectToBeDisplayed(`[data-testid~="db-widget-container-${camelCase(widgetName)}"]`, reverse);
    }
}

export default Dashboard;
