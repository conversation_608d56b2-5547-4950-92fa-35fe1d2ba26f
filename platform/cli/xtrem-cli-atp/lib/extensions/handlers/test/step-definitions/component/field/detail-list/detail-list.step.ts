import { Then } from '@cucumber/cucumber';
import { ElementContext, LookupStrategy, NestedFieldTypes } from '../../../step-definitions-utils';
import { waitForPromises } from '../wait-util';
import { DetailListNestedFieldObject } from './detail-list-nested-field-object';
import { DetailListObject } from './detail-list-object';

Then(
    /^the title of the item on the row "([^"\n\r]*)" in the detail list is "([^"\n\r]*)"$/,
    async (rowId: string, title: string) => {
        const detailList = new DetailListObject();
        await detailList.expectTitle(rowId, title);
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested (date|label|numeric|text|reference|link) field of the row (\d+) in the detail list is "([^"\n\r]*)"$/,
    async (
        identifier: string,
        lookupStrategy: LookupStrategy,
        fieldType: NestedFieldTypes,
        rowNumber: number,
        toBe: string,
    ) => {
        const field = new DetailListNestedFieldObject({
            identifier,
            lookupStrategy,
            fieldType,
            rowNumber,
            context: ElementContext.DETAIL_LIST,
        });
        // Line below commented to fix XT-95664
        // await field.loseFocus();
        await field.expectDetailListNestedFieldTextContent(toBe);
    },
);

Then(
    /^the user selects the nested link field with the value "([^"\n\r]*)" of the row "([^"\n\r]*)" in the detail list$/,
    async (title: string, rowId: string) => {
        const detailList = new DetailListObject();
        await detailList.linkClick(rowId, title);
        await waitForPromises(200, 'wait for link to exist');
    },
);
