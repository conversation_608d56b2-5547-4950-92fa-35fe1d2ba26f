import { addAttachment } from '@wdio/allure-reporter';
import * as fs from 'fs';
import * as path from 'path';
import { waitForPromises } from '../../component/field/wait-util';
import { testGlobals } from '../../component/test-globals';
import { FieldTypes } from '../types';
import { getDataTestIdSelector } from './selector-functions';

export const getFieldType = (fieldType: FieldTypes) => {
    const fieldTypeMapping: { [key: string]: string } = {
        'text area': 'text-area',
        'multi reference': 'multi-reference',
        'multi dropdown': 'multi-dropdown',
        'summary table': 'table-summary',
        'content table': 'content-table',
        'filter editor': 'filter-editor',
    };
    return fieldTypeMapping[fieldType] || fieldType;
};

export const takeScreenshot = async () => {
    const puppeteerBrowser = await browser.getPuppeteer();

    const screenshotsDir = path.resolve(path.dirname(`${testGlobals._CURRENT_SCENARIO_PATH}`), 'screenshots');

    if (!fs.existsSync(screenshotsDir)) {
        fs.mkdirSync(screenshotsDir, { recursive: true });
    }
    const pages = await browser.call(async () => {
        const p = await puppeteerBrowser.pages();
        return p;
    });
    let currentPage = pages[0];
    const windowHandles = await browser.getWindowHandles();
    const currentHandle = await browser.getWindowHandle();

    for (let index = 0; index < windowHandles.length; index += 1) {
        const targetHandle = windowHandles[index];
        if (currentHandle === targetHandle) currentPage = pages[index];
    }

    testGlobals._CURRENT_STEP_SCREENSHOT = true;

    // For Allure Report
    const allureScreenshot = await currentPage.screenshot({
        captureBeyondViewport: false,
    });

    addAttachment('Screenshot', allureScreenshot, 'image/png');
};

export const formatString = (str: string) =>
    str
        .trim()
        .toLowerCase()
        .replace(/(\r\n|\n|\r| )/gm, '');

export const removeWhiteSpaces = (str: string) => str.trim().replace(/\s+/g, '');

export const addTrailingSlash = (targetUrl: string) => {
    if (targetUrl.slice(-1) !== '/') {
        return targetUrl + encodeURI('/');
    }
    return targetUrl;
};

export const selectFromLookUpDialog = async (expectedValue: string) => {
    await waitForPromises(500, 'open mobile lookup dialog');
    const elements = await $$(
        `${getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-lookup-dialog' })} ${getDataTestIdSelector({
            domSelector: 'div',
            dataTestIdValue: 'e-dialog-body',
        })} ${getDataTestIdSelector({
            domSelector: 'span',
            dataTestIdValue: 'e-field-value',
        })}`,
    );

    const values = await Promise.all(
        elements.map(async el => {
            const val = await el.getText();
            return val;
        }),
    );

    const index = values.findIndex(val => val === expectedValue);

    if (index < 0) {
        throw new Error(`Expected value to be selected could not be found: "${expectedValue}"`);
    }

    await elements[index].scrollIntoView({ block: 'center' });
    await waitForPromises(200, 'scroll item into view');
    await elements[index].moveTo();
    await elements[index].click();
    await waitForPromises(200, 'select item from mobile lookup dialog');
};

export const waitUntilWithScreenshot = async (
    conditionFn: () => boolean | Promise<boolean>,
    { timeout, interval, timeoutMsg }: { timeout: number; interval: number; timeoutMsg: string },
): Promise<true | void> => {
    const start = Date.now();
    let value;

    while (Date.now() - start < timeout) {
        value = await conditionFn();
        if (value) return value;
        await browser.pause(interval);
    }

    await takeScreenshot();

    throw new Error(timeoutMsg);
};

export const timeToSeconds = (time: string) => {
    const [hours, minutes, seconds] = time.split(':').map(Number);
    return hours * 3600 + minutes * 60 + seconds;
};
