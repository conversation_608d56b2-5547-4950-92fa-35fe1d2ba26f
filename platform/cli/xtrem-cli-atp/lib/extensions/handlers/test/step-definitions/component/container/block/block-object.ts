import * as utils from '../../../step-definitions-utils';
import { ContainerObject } from '../container-object';

export class BlockContainerObject extends ContainerObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        super({
            containerType: utils.ContainerElementTypes.BLOCK,
            identifier: utils.getLookupStrategySelector({ fieldType: 'block', lookupStrategy, identifier }),
            context,
        });
    }

    async expectTitle(expectedTitle: string) {
        await this.expectTextContent({
            toBe: expectedTitle,
            ignoreCase: false,
            cssSelector: utils.getDataTestIdSelector({ domSelector: 'h3', dataTestIdValue: 'e-block-title' }),
        });
    }

    async expectBlockToAppear(cssState: 'displayed' | 'hidden') {
        const element = await $(this.cssSelector);
        const elemExists = await element.isExisting();
        if (elemExists) {
            const parentElement = await element.parentElement();
            const elemAttribute = await parentElement.getAttribute('class');
            if (cssState === 'displayed' && elemAttribute.includes('e-hidden')) {
                await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
                throw new Error(`Expected Element to be displayed.\nSelector: ${this.cssSelector}`);
            }

            if (cssState === 'hidden' && !elemAttribute.includes('e-hidden')) {
                await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
                throw new Error(`Expected Element to be hidden.\nSelector: ${this.cssSelector}`);
            }
        } else {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(
                `Element expected to exists in ${this.valueCheckTimeout} ms: (selector: ${this.cssSelector})`,
            );
        }
    }
}
