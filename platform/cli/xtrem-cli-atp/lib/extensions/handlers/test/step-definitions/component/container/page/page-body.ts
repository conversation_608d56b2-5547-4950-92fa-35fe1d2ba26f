import { kebabCase } from 'lodash';
import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';

export class Body extends AbstractPageObject {
    constructor(parent: string) {
        super(`${parent} ${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-page-body' })}`);
    }

    private async scrollToSelector(baseSelector: string) {
        const selectorToUse = this.getSelectorForOperation(baseSelector);
        const element = await browser.$(selectorToUse);
        await element.waitForExist();
        await browser.execute(`document.querySelector('${selectorToUse}').scrollIntoView();`);
        await this.expectToAppear({ cssSelector: baseSelector });
    }

    async scrollToSection(identifier: string, lookupStrategy: utils.LookupStrategy) {
        const baseSelector = utils.getLookupStrategySelector({ fieldType: 'section', lookupStrategy, identifier });
        await this.scrollToSelector(baseSelector);
    }

    async scrollToBlock(identifier: string, lookupStrategy: utils.LookupStrategy) {
        const baseSelector = utils.getLookupStrategySelector({ fieldType: 'block', lookupStrategy, identifier });
        await this.scrollToSelector(baseSelector);
    }

    async scrollToField({
        identifier,
        lookupStrategy,
        fieldType,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        fieldType: utils.FieldTypes;
    }) {
        const baseSelector = utils.getLookupStrategySelector({
            fieldType: `${kebabCase(fieldType)}-field`,
            lookupStrategy,
            identifier,
        });
        await this.scrollToSelector(baseSelector);
    }
}
