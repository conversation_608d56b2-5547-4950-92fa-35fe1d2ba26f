import { Then, When } from '@cucumber/cucumber';
import * as utils from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { FieldObject } from '../field-object';
import { TableObject } from '../table/table-object';
import { scrollUntilClickable } from '../table/tableUtils';
import { waitForPromises } from '../wait-util';
import { CardNestedFieldObject } from './card-nested-field-object';
import { CardObject } from './card-object';
import { expectImageStatus, selectAndGetParent } from './card-utils';

When(/^the user clicks the card (\d+) in the table field?$/, async (cardNumber: number) => {
    const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
    await table.expect.waitForTableStopLoading();
    const card = new CardObject({ parent: table, cardNumber });
    await scrollUntilClickable(card.cssSelector);
    await waitForPromises(500, 'scrolling to card');
    await browser.execute(`document.querySelector('${card.cssSelector} button').click()`);

    await waitForPromises(500, 'clicking card');
});

When(
    /^the user (ticks|unticks) the main checkbox of the card (\d+) in the table field$/,
    async (selection: 'ticks' | 'unticks', cardNumber: number) => {
        const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
        await table.expect.waitForTableStopLoading();
        const card = new CardObject({ parent: table, cardNumber });
        await card.tickCheckbox(selection);
        await waitForPromises(500, 'main checkbox selection');
    },
);

When(
    /^the user (ticks|unticks) the main checkbox of the card with the text "(.*)" in the table field$/,
    async (selection: 'ticks' | 'unticks', label: string) => {
        const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
        await table.expect.waitForTableStopLoading();
        const card = new CardObject({ parent: table, cardNumber: undefined, label });
        await card.tickCheckbox(selection);
        await waitForPromises(500, 'main checkbox selection');
    },
);

When(/^all the cards in the table field are (selected|unselected)$/, async (selection: 'selected' | 'unselected') => {
    const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
    await table.expect.waitForTableStopLoading();
    await table.validateCardSelection(selection);
    await waitForPromises(500, 'get checkbox value');
});

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested (date|label|numeric|reference|select|text|link) field of the card (\d+) in the table field is "(.*)"$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.NestedFieldTypes,
        cardNumber: number,
        expectedContent: string,
    ) => {
        const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
        await table.expect.waitForTableStopLoading();
        const card = new CardObject({ parent: table, cardNumber });
        await (
            await $(card.cssSelector)
        ).waitForExist({
            timeout: utils.atpEnv.timeoutWaitFor,
            timeoutMsg: `Expected card "${cardNumber}" could not be found.\nSelector: ${card.cssSelector})`,
        });
        await card.scrollTo();
        const field = new CardNestedFieldObject({ parent: card, identifier, lookupStrategy, fieldType });
        await utils.waitForElementToBeFound({ name: `"${identifier}" nested field`, selector: field.cssSelector });
        // commented out because of this XT-81895
        // await field.loseFocus();
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedContent);
        await field.expectTextContent({
            toBe: storeValue,
            ignoreCase: false,
            cssSelector: fieldType === 'label' ? 'span span' : 'span',
        });
    },
);

When(
    /^the user stores the value of the "([^"\n\r]*)" (bound|labelled) nested (date|label|numeric|reference|select|text|link) field of the card (\d+) in the table field with the key "(.*)"$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        fieldType: utils.NestedFieldTypes,
        cardNumber: number,
        storeKey: string,
    ) => {
        const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
        await table.expect.waitForTableStopLoading();
        const card = new CardObject({ parent: table, cardNumber });
        await (
            await $(card.cssSelector)
        ).waitForExist({
            timeout: utils.atpEnv.timeoutWaitFor,
            timeoutMsg: `Expected card "${cardNumber}" could not be found.\nSelector: ${card.cssSelector})`,
        });
        await card.scrollTo();
        const field = new CardNestedFieldObject({ parent: card, identifier, lookupStrategy, fieldType });
        const element = await field.waitForDisplayedAndGetElement({
            ignoreContext: true,
            selector: field.cssSelector,
            timeout: utils.atpEnv.timeoutWaitFor,
            timeoutMsg: selector =>
                `Expected element could not be found: "${identifier}" nested field and card number ${cardNumber}.\nSelector: ${selector}`,
        });
        const elementValue = await element.getText();
        StaticStore.storeObject(storeKey, elementValue);
        await utils.takeScreenshot();
    },
);

When(
    /^the user clicks the "(.*)" inline action button of the card (\d+) in the (table|nested grid) field$/,
    async (actionName: string, cardNumber: number, parentType: 'table' | 'nested grid') => {
        const parent = await selectAndGetParent(parentType);
        const card = new CardObject({ parent, cardNumber });
        await card.clickButtonAction(actionName);
        await waitForPromises(500, 'button action selection');
    },
);

When(
    /^the "(.*)" inline action button of the card (\d+) in the table field is (displayed|hidden)$/,
    async (actionName: string, cardNumber: number, cssState: 'displayed' | 'hidden') => {
        const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
        await table.expect.waitForTableStopLoading();
        const card = new CardObject({ parent: table, cardNumber });
        await card.expectActionButtonIsDisplayed(actionName, cssState === 'hidden');
    },
);

When(
    /^the user clicks the "([^"\n\r]*)" dropdown action of the card (\d+) in the table field$/,
    async (actionName: string, cardNumber: number) => {
        const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
        await table.expect.waitForTableStopLoading();
        const card = new CardObject({ parent: table, cardNumber });
        await card.clickDropdownAction(actionName);
        await waitForPromises(500, 'dropdown action selection');
    },
);

Then(
    /^the "([^"\n\r]*)" dropdown action of the card (\d+) in the table field is (enabled|disabled)$/,
    async (actionName: string, cardNumber: any, expectedState: utils.EnabledState) => {
        const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
        await table.expect.waitForTableStopLoading();
        const card = new CardObject({ parent: table, cardNumber });
        await card.expectDropdownActionToBeEnabled(actionName, expectedState);
    },
);

When(
    /^the "([^"\n\r]*)" (bound|labelled) nested image field of the card (\d+) in the table field is (defined|undefined)$/,
    async (
        identifier: string,
        lookupStrategy: utils.LookupStrategy,
        cardNumber: number,
        status: 'defined' | 'undefined',
    ) => {
        const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
        await table.expect.waitForTableStopLoading();
        const card = new CardObject({ parent: table, cardNumber });
        await (
            await $(card.cssSelector)
        ).waitForExist({
            timeout: utils.atpEnv.timeoutWaitFor,
            timeoutMsg: `Expected card "${cardNumber}" could not be found.\nSelector: ${card.cssSelector})`,
        });
        const field = new CardNestedFieldObject({
            parent: card,
            identifier,
            lookupStrategy,
            fieldType: utils.nestedFieldTypes.image,
        });
        await field.loseFocus();
        await expectImageStatus({ selector: field.cssSelector, identifier, status });
    },
);

Then(
    /^the value of the "([^"\n\r]*)" bound nested (numeric|reference|text) field in the header card is "(.*)"$/,
    async (identifier: string, fieldType: utils.NestedFieldTypes, expectedContent: string) => {
        const field = new FieldObject({
            fieldType,
            identifier,
            lookupStrategy: utils.LookupStrategy.BIND,
            context: utils.ElementContext.HEADER_CARD,
        });
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedContent);
        await field.expectTextContent({ toBe: storeValue, ignoreCase: false, cssSelector: 'span' });
    },
);

When(
    /^the "(.*)" bound nested image field on the header card is (defined|undefined)$/,
    async (identifier: string, status: 'defined' | 'undefined') => {
        await expectImageStatus({
            selector: `header[data-testid="e-header"] div[data-testid~="e-image-field"][data-testid~="e-field-bind-${identifier}"]`,
            identifier,
            status,
        });
    },
);
