import { After, AfterAll, Before, BeforeAll, Status, When } from '@cucumber/cucumber';
import * as chalk from 'chalk';
import * as StaticStore from '../static-store';
import { activeFunction } from './active-function';

let currentFeaturePath = '';
let isFeatureRunning = false;
let scenariosCount = 0;
let executedScenariosCount = 0;
const isSingleInstance = () => {
    return Number(process.env.XTREM_TEST_MAX_INSTANCES ?? 1) <= 1;
};

BeforeAll(() => {
    isFeatureRunning = true;
});

Before(async (scenario: any) => {
    if (isSingleInstance()) return;
    currentFeaturePath = scenario.pickle.uri.split('\\').pop();
    await activeFunction.setLockEntryAfterRemoved(currentFeaturePath);
    scenariosCount += 1;
});

After(async (step: any) => {
    if (step.result.status !== Status.PASSED && !isSingleInstance()) {
        // eslint-disable-next-line no-console
        console.log(
            chalk.blue.bold(
                `\t ℹ️\t EXECUTION ERROR DETECTED, REMOVING LOCK ENTRIES FROM FEATURE: ${currentFeaturePath}`,
            ),
        );
        await activeFunction.deleteByFeaturePath(currentFeaturePath);
    }
    executedScenariosCount += 1;
});

AfterAll(async () => {
    if (isSingleInstance()) return;
    if (isFeatureRunning && executedScenariosCount === scenariosCount) {
        await activeFunction.deleteByFeaturePath(currentFeaturePath);
        isFeatureRunning = false;
        scenariosCount = 0;
        executedScenariosCount = 0;
    }
});

/* Active Functions */

When(/^the user adds the lock entry "(.*)"$/, async (lockEntryCode: string) => {
    if (isSingleInstance()) return;
    StaticStore.storeObject(StaticStore.StoredKeys.CURRENT_LOCKED_FUNCTION, lockEntryCode);
    // eslint-disable-next-line no-console
    console.log(chalk.blue.bold(`\t ℹ️\t LOCK ENTRY: ${lockEntryCode}`));
    await activeFunction.checkForActiveLockEntry(lockEntryCode);
});

When(/^the user removes the lock entry "(.*)"$/, async (lockEntryCode: string) => {
    if (isSingleInstance()) return;
    StaticStore.storeObject(StaticStore.StoredKeys.CURRENT_LOCKED_FUNCTION, '');
    await activeFunction.deleteByLockEntryCodeAndFeaturePath(lockEntryCode, activeFunction.getFeaturePath());
});
