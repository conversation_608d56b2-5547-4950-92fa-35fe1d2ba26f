import { Then } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { SelectionCardObject } from './selection-card-object';

Then(/^the user selects the card with "(.*)" value in the selection card$/, async (value: string) => {
    const selectionCardField = <SelectionCardObject>StaticStore.getStoredField(fieldTypes.selectionCard);
    await selectionCardField.clickCardToSelect(value);
});

Then(
    /^the card with "(.*)" value in the selection card is (selected|unselected)$/,
    async (cardName: string, cssState: 'selected' | 'unselected') => {
        const selectionCardField = <SelectionCardObject>StaticStore.getStoredField(fieldTypes.selectionCard);
        await selectionCardField.expectCardSelected(cardName, cssState === 'unselected');
    },
);
