import { Then, When } from '@cucumber/cucumber';
import { LookupActionIdentifier, LookupActionObject, LookupActionStatus } from './lookup-action-object';

When(/^the user clicks the (cancel|select) action on the lookup dialog$/, async (identifier: LookupActionIdentifier) => {
    const lookupAction = new LookupActionObject({ identifier });
    await lookupAction.click();
});

Then(
    /^the lookup dialogs (cancel|select) action is (disabled|enabled)$/,
    async (identifier: LookupActionIdentifier, status: LookupActionStatus) => {
        const lookupAction = new LookupActionObject({ identifier });
        try {
            await lookupAction.expectStatusToBe(status);
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error(error);
            throw error;
        }
    },
);
