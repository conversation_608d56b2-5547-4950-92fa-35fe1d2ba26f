import { camelCase } from 'lodash';
import * as utils from '../../../../step-definitions-utils';
import * as StaticStore from '../../../static-store';
import { PodCollectionItemObject } from '../pod-collection-item/pod-collection-item-object';
import { PodObject } from '../pod-field/pod-object';

export class PodCollectionObject extends PodObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        super({ fieldType: 'pod-collection', identifier, lookupStrategy, context });
    }

    // eslint-disable-next-line class-methods-use-this
    selectPodCollectionBtn = async (name: string) => {
        const addItemBtnSelector = utils.getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: 'e-pod-collection-add-new',
        });
        const addItemBtn = await this.find(addItemBtnSelector);
        await addItemBtn.waitForDisplayed({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element to be displayed: ${name}.\nSelector: ${addItemBtn.selector})`,
        });

        const addItemBtnTxt = await addItemBtn.getText();

        if (utils.formatString(addItemBtnTxt) === utils.formatString(name)) {
            await addItemBtn.click();
            return;
        }
        throw new Error(
            `The "${name}" button of the selected pod collection field could not be found.\nSelector: ${addItemBtnSelector}`,
        );
    };

    async expectPodCollectionValidity(expectedToBeValid: boolean) {
        const selectorToUse = this.getSelectorForOperation('span[type="error"]');
        await this.loseFocus();

        if (expectedToBeValid) {
            await this.expectToDisappear(selectorToUse, true);
        } else {
            await this.expectToAppear({ cssSelector: selectorToUse, ignoreContext: true });
        }
    }

    async storePodCollectionItem(identifier: string, lookupStrategy: 'id' | 'labelled') {
        await utils.waitForElementToBeDisplayed({ name: 'pod collection field', selector: this.cssSelector });
        await (await $('.e-pod-collection')).waitForExist();
        const items = await this.findAll('.e-pod-collection');
        // eslint-disable-next-line no-restricted-syntax
        for (const item of items) {
            // eslint-disable-next-line no-await-in-loop
            const classList = await item.getAttribute('data-testid');
            const classListArray = classList.split(' ');

            const labelSelector = classListArray.find(element => element.match(/e-field-label-/g));
            const idSelector = classListArray.find(element => element.match(/e-pod-collection-item-/g));

            const itemLabel = labelSelector?.replace('e-field-label-', '') as string;
            const itemId = idSelector?.replace('e-pod-collection-item-', '') as string;

            if (
                (lookupStrategy === 'labelled' && camelCase(identifier) === itemLabel) ||
                (lookupStrategy === 'id' && identifier === itemId)
            ) {
                const podCollectionItem = new PodCollectionItemObject(itemId);
                StaticStore.storeObject(StaticStore.StoredKeys.POD_COLLECTION_ITEM, podCollectionItem);
                return;
            }
        }
        throw new Error(`The selected "${identifier}" pod collection item could not be found.`);
    }

    override async expectPodTitle(value: string) {
        await this.expectTextContent({ toBe: value, ignoreCase: false, cssSelector: '.e-field-title' });
    }

    override async expectPodHelperText(value: string) {
        await this.expectTextContent({ toBe: value, ignoreCase: false, cssSelector: this.podHelperText });
    }
}
