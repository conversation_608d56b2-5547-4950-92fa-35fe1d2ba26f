import * as utils from '../../step-definitions-utils';
import AbstractPageObject from '../abstract-page-object';

export class MultiActions extends AbstractPageObject {
    constructor(context: utils.ElementContext) {
        super(`${utils.getContextSelector(context)} ${utils.getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: 'e-create-multi-action-button',
        })},
               ${utils.getContextSelector(context)} ${utils.getDataComponentSelector('div', 'split-button')} button[data-element="toggle-button"]`);
    }

    async clickMultiActionsButton(identifier: string, lookupStrategy: utils.LookupStrategy) {
        let selectorToUse = '';
        try {
            await this.click();
            selectorToUse = utils.getLookupStrategySelector({
                fieldType: 'business-action',
                lookupStrategy,
                identifier,
                domSelector: 'button',
            });

            const elementSelector = await browser.$(selectorToUse);

            if (!(await elementSelector.isExisting())) {
                throw new Error(
                    `Expected "${identifier}" multi-action button could not be found.\nSelector: ${this.cssSelector}${selectorToUse}`,
                );
            }
            // Dismiss all notifications before clicking the multi-action button in case a new notification is displayed
            await this.dismissAllNotification();
            await elementSelector.click();
        } catch (error) {
            throw new Error(error.message);
        }
    }
}
