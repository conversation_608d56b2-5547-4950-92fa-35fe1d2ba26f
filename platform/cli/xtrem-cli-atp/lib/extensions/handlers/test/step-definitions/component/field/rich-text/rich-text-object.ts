import { Key } from 'webdriverio';
import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

export class RichTextFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'rich-text', identifier, lookupStrategy, context });
    }

    override async write({ content }: { content: string }) {
        await this.expectToAppear({ cssSelector: '.ck-content' });
        await this.click('.ck-content');
        await browser.keys(content);
    }

    async clickFormattingButton(buttonLabel: string) {
        const selector = '.ck-toolbar button.ck-button';

        await this.expectToAppear();

        const rtButtons = await this.findAll(selector);
        let button;
        // eslint-disable-next-line no-restricted-syntax
        for (const buttonElement of rtButtons) {
            // eslint-disable-next-line no-await-in-loop
            if ((await (await buttonElement.$('.ck-button__label')).getText()) === buttonLabel) {
                button = buttonElement;
                break;
            }
        }

        if (!button) {
            throw new Error(
                `Expected element could not be found: rich text editor '${buttonLabel}' formatting button.\nSelector: ${this.cssSelector} ${selector}`,
            );
        }

        if ((await button.getAttribute('aria-disabled')) === 'true') {
            throw new Error(
                `Rich Text formatting button '${buttonLabel}' is disabled.\nSelector: ${this.cssSelector} ${selector}`,
            );
        }

        await button.click();
        await waitForPromises(0, 'rich text after click formatting');
    }

    async selectAllContent() {
        await this.expectToAppear({ cssSelector: '.ck-content' });
        await this.click('.ck-content');
        await browser.keys([Key.Ctrl, Key.Home]);
        await browser.pause(100);
        await browser.keys([Key.Shift, Key.Ctrl, Key.End]);
        await browser.pause(100);
    }

    override async clearInput() {
        const cssSelector = '.ck-content';
        await this.expectToAppear({ cssSelector, ignoreContext: false });
        // eslint-disable-next-line no-await-in-loop
        while ((await (await this.find(cssSelector)).getText()).trim() !== '') {
            // eslint-disable-next-line no-await-in-loop
            await this.click(cssSelector, false);
            // eslint-disable-next-line no-await-in-loop
            await browser.keys([Key.Ctrl, Key.Home]);
            // eslint-disable-next-line no-await-in-loop
            await browser.pause(100);
            // eslint-disable-next-line no-await-in-loop
            await browser.keys([Key.Shift, Key.Ctrl, Key.End]);
            // eslint-disable-next-line no-await-in-loop
            await browser.pause(100);
            // eslint-disable-next-line no-await-in-loop
            await browser.keys(Key.Backspace);
            // eslint-disable-next-line no-await-in-loop
        }
    }

    /**
     * This will check if element is enabled or not
     *
     * @param domElement Element to check if it is enabled or not in DOM
     * @param reverse if set to true, it will return check for disable element
     */
    override async expectToBeEnabled(selector: string, reverse = false) {
        const element = await browser.$(selector);
        await browser.waitUntil(
            async () => {
                const disabledAttribute = await element.getAttribute('contenteditable');
                return reverse
                    ? disabledAttribute === 'false'
                    : disabledAttribute == null || disabledAttribute !== 'false';
            },
            {
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected Element to be ${reverse ? 'disabled' : 'enabled'}.\nSelector: ${selector}`,
            },
        );
    }
}
