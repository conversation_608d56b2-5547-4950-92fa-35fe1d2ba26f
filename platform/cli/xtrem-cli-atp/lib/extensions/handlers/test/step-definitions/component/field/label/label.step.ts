import { Then } from '@cucumber/cucumber';
import { fieldTypes, getElementTypeSelector } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { LabelFieldObject } from './label-field-object';

// ----------
// Static store field steps
// ----------
Then(/^the value of the label field is "(.*)"$/, async (value: string) => {
    const field = <LabelFieldObject>StaticStore.getStoredField(fieldTypes.label);
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
    await field.loseFocus();
    await field.expectTextContent({
        toBe: storeValue,
        ignoreCase: false,
        cssSelector: getElementTypeSelector(fieldTypes.label),
    });
});

Then(/^the label field is (enabled|disabled)$/, async (cssState: 'enabled' | 'disabled') => {
    const field = <LabelFieldObject>StaticStore.getStoredField(fieldTypes.label);
    await field.loseFocus();
    await field.expectToBeEnabledClass(`${field.cssSelector}`, cssState === 'disabled');
});
