import { Then } from '@cucumber/cucumber';
import { fieldTypes } from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { ContentTableObject } from './content-table-object';

Then(/the user clicks the "([^"\n\r]*)" button of the content table field$/, async (value: string) => {
    const contentTableObject = <ContentTableObject>StaticStore.getStoredField(fieldTypes.contentTable);
    await contentTableObject.clickAddButton(value);
});

Then(
    /the "([^"\n\r]*)" (dropdown|text|date) field of row "([^"\n\r]*)" in the content table field is (displayed|hidden)$/,
    async (name: string, fieldType: 'dropdown' | 'text' | 'date', rowId: number, cssState: 'displayed' | 'hidden') => {
        const contentTableObject = <ContentTableObject>StaticStore.getStoredField(fieldTypes.contentTable);
        await contentTableObject.expectRowFieldDisplayed({ name, rowId, reverse: cssState === 'hidden' });
    },
);

Then(
    /the "([^"\n\r]*)" (dropdown|text|date) field of row "([^"\n\r]*)" in the content table field is (enabled|disabled)$/,
    async (name: string, fieldType: 'dropdown' | 'text' | 'date', rowId: number, cssState: 'enabled' | 'disabled') => {
        const contentTableObject = <ContentTableObject>StaticStore.getStoredField(fieldTypes.contentTable);
        await contentTableObject.expectRowFieldEnabled({ name, rowId, reverse: cssState === 'disabled' });
    },
);

Then(
    /the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the content table field$/,
    async (value: string, name: string, fieldType: 'dropdown' | 'text', rowId: number) => {
        const contentTableObject = <ContentTableObject>StaticStore.getStoredField(fieldTypes.contentTable);
        await contentTableObject.setFieldValue({ value, name, rowId });
    },
);

Then(
    /the user clears the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the content table field$/,
    async (name: string, fieldType: 'dropdown' | 'text', rowId: number) => {
        const contentTableObject = <ContentTableObject>StaticStore.getStoredField(fieldTypes.contentTable);
        await contentTableObject.clearRowFieldValue(name, rowId);
    },
);

Then(
    /^the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the content table field is (displayed|hidden)$/,
    async (buttonName: string, rowId: number, cssState: 'displayed' | 'hidden') => {
        const contentTableObject = <ContentTableObject>StaticStore.getStoredField(fieldTypes.contentTable);
        await contentTableObject.expectActionButtonDisplayed({ buttonName, rowId, reverse: cssState === 'hidden' });
    },
);

Then(
    /^the user clicks the "([^"\n\r]*)" action button of row "([^"\n\r]*)" in the content table field$/,
    async (buttonName: string, rowId: number) => {
        const contentTableObject = <ContentTableObject>StaticStore.getStoredField(fieldTypes.contentTable);
        await contentTableObject.clickActionButton(buttonName, rowId);
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (dropdown|text) field of row "([^"\n\r]*)" in the content table field is "([^"\n\r]*)"$/,
    async (fieldName: string, fieldType: 'dropdown' | 'text', rowId: number, value: string) => {
        const contentTableObject = <ContentTableObject>StaticStore.getStoredField(fieldTypes.contentTable);
        await contentTableObject.expectRowFieldValue({ fieldName, rowId, value });
    },
);
