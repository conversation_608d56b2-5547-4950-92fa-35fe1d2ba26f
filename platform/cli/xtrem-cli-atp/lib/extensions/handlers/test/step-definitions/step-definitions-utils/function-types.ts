import { LookupStrategy } from './enums';

export type AsyncStringReturnVoid = (input: string) => Promise<void>;
export type AsyncBooleanReturnVoid = (input: boolean) => Promise<void>;
export type AsyncNumberReturnVoid = (input: number) => Promise<void>;
export type AsyncStringBooleanReturnVoid = (input1: string, input2?: boolean) => Promise<void>;
export type AsyncStringNumberReturnVoid = (input1: string, input2: number) => Promise<void>;
export type AsyncStringLookupStrategyReturnVoid = (input1: string, input2: LookupStrategy) => Promise<void>;
export type AsyncStringLookupStrategyReturnString = (input1: string, input2: LookupStrategy) => Promise<string>;
export type AsyncStringLookupStrategyReturnElement = (
    input1: string,
    input2: LookupStrategy,
) => Promise<WebdriverIO.Element>;
export type SyncStringLookupStrategyReturnString = (input1: string, input2: LookupStrategy) => string;
export type AsyncObjectStringLookupStrategyReturnVoid = (input: {
    id: string;
    lookupStrategy: LookupStrategy;
    reverse?: boolean;
}) => Promise<void>;
export type AsyncObjectStringLookupStrategyBooleanReturnElement = (input: {
    id: string;
    lookupStrategy: LookupStrategy;
    reverse?: boolean;
}) => Promise<WebdriverIO.Element>;
export type AsyncObjectStringNumberBooleanReturnVoid = (input: {
    id: string;
    rowNumber: number;
    reverse?: boolean;
}) => Promise<void>;
export type AsyncObjectStringStringBooleanReturnVoid = (input: {
    toBe: string;
    cssSelector?: string;
    ignoreContext?: boolean;
}) => Promise<void>;
