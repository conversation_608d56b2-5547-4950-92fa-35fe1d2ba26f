import { Then, When } from '@cucumber/cucumber';
import { runtimeParameters } from '../../../../../../../parameters';
import * as utils from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { testGlobals } from '../../test-globals';
import { waitForPromises } from '../wait-util';
import { ReferenceFieldObject } from './reference-object';

When(/^the user writes "(.*)" in the reference field$/, async (value: string) => {
    const field = <ReferenceFieldObject>StaticStore.getStoredField(utils.fieldTypes.reference);
    const selection = runtimeParameters.getStringOrParameter(value);
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(selection);
    await field.expectToBeEnabled(`${field.cssSelector} ${utils.getElementTypeSelector(utils.fieldTypes.reference)}`);
    await field.expectToBeReadOnly({
        selector: field.cssSelector,
        FieldType: utils.fieldTypes.reference,
        reverse: true,
    });
    await field.write({ content: storeValue });
    await waitForPromises(500, 'write to field');
});

When(/^the user clicks the lookup button of the reference field$/, async () => {
    const field = <ReferenceFieldObject>StaticStore.getStoredField(utils.fieldTypes.reference);
    await field.clickLookupButton();
    await field.expectLookupDialogToAppear();
});

Then(/^the lookup button of the reference field is displayed$/, async () => {
    const field = <ReferenceFieldObject>StaticStore.getStoredField(utils.fieldTypes.reference);
    await field.expectToBeDisplayed(
        `${field.cssSelector} ${utils.getDataTestIdSelector({ domSelector: 'button', dataTestIdValue: 'e-ui-select-lookup-button' })}`,
    );
});

Then(/^a list of options is displayed for the reference field$/, async () => {
    if (testGlobals.device === 'mobile' || testGlobals.device === 'tablet') {
        await utils.expectElementToBeDisplayed({
            selector: 'div[data-component="dialog-full-screen"] [data-testid="e-card"]',
        });
    } else {
        const field = <ReferenceFieldObject>StaticStore.getStoredField(utils.fieldTypes.reference);
        const dropdownSelector = utils.getDataTestIdSelector({
            domSelector: 'ul',
            dataTestIdValue: 'e-ui-select-dropdown',
        });

        await browser.waitUntil(
            async () => {
                const items = await field.findAll(`${dropdownSelector} li`);
                const textContents = await Promise.all(items.map(item => item.getText()));

                const loadingOrNoItems = ['Loading...', 'No items found'];
                return !textContents.some(text => loadingOrNoItems.includes(text.trim()));
            },
            {
                timeoutMsg: 'No items were loaded for the Reference field.',
                timeout: utils.atpEnv.timeoutWaitFor,
            },
        );
    }
});

Then(/^the reference field tunnel link is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const field = <ReferenceFieldObject>StaticStore.getStoredField(utils.fieldTypes.reference);
    await field.expectTunnelLinkToBeDisplayed(cssState === 'hidden');
});

Then(/^the user clicks the reference field tunnel link$/, async () => {
    const field = <ReferenceFieldObject>StaticStore.getStoredField(utils.fieldTypes.reference);
    await field.clickReferenceTunnelLink();
});

Then(/^the user clicks the "(.*)" action link in the reference field lookup$/, async (name: string) => {
    const field = <ReferenceFieldObject>StaticStore.getStoredField(utils.fieldTypes.reference);
    await field.clickLookupActionLink(name);
});
