import { SupportedDevices } from '../step-definitions-utils';

export interface TestGlobals {
    _CURRENT_FEATURE?: string;
    _CURRENT_STEP_SCREENSHOT?: boolean;
    _CURRENT_SCENARIO?: string;
    _CURRENT_SCENARIO_PATH?: string;
    _CURRENT_STEP?: string;
    _CURRENT_STEP_ID?: number;
    _CURRENT_SCENARIO_STEPS?: string;
    cfgTimeout?: number;
    device?: SupportedDevices;
    language?: string;
    accessibility_json_path?: string;
    isLogged?: boolean;
}

export const testGlobals: TestGlobals = {};

export const isMobileDevice = () =>
    testGlobals.device === 'mobile' ||
    testGlobals.device === 'tablet' ||
    (process.env.TARGET_URL &&
        process.env.TARGET_URL.match(/^(http|https):\/\/([a-z0-9-]+\.)*[a-z0-9-]+\.[a-z]+\/handheld(\/$|$)/g));
