import AbstractPageObject from '../abstract-page-object';
import { DropDownMenuObject } from '../dropdown-menu-object';

export class MobileSidebar extends AbstractPageObject {
    public readonly dropDownMenu: DropDownMenuObject;

    constructor() {
        super('div[data-element="dialog-full-screen"]');
        this.dropDownMenu = new DropDownMenuObject(this.cssSelector);
    }

    async expectMobileSidebarTitle(title: string, reverse: boolean) {
        const mobileSidebarTitle = await this.find('.e-sidebar-title', true);
        await this.expectToBeDisplayed(mobileSidebarTitle.selector.toString(), reverse);
        await this.expectTextContent({
            toBe: title,
            ignoreCase: false,
            cssSelector: mobileSidebarTitle.selector.toString(),
        });
    }
}
