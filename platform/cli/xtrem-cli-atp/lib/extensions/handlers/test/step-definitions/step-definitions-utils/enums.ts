export enum BaseFieldTypes {
    aggregate = 'aggregate',
    checkbox = 'checkbox',
    count = 'count',
    date = 'date',
    dateTimeRange = 'date-time-range',
    dropdownList = 'dropdown-list',
    filterSelect = 'filter select',
    formDesigner = 'form designer',
    formDesigner_dataContainer = 'form designer - data container',
    formDesigner_table = 'form designer - table',
    formDesigner_tableCell = 'form designer - table cell',
    formDesigner_unbreakableContainer = 'form designer - unbreakable container',
    icon = 'icon',
    image = 'image',
    label = 'label',
    link = 'link',
    multiDropdown = 'multi dropdown',
    multiReference = 'multi reference',
    nestedGrid = 'nested grid',
    nodeBrowserTree = 'node-browser-tree',
    nodeStepTree = 'node step tree',
    numeric = 'numeric',
    pod = 'pod',
    dynamicPod = 'dynamic-pod',
    dynamicSelect = 'dynamic-select',
    podCollection = 'pod collection',
    podCollectionItem = 'pod collection item',
    progress = 'progress',
    radio = 'radio',
    reference = 'reference',
    relativeDate = 'relative date',
    select = 'select',
    staticContent = 'static-content',
    switch = 'switch',
    text = 'text',
    textAreaNested = 'text-area',
    vitalPod = 'vital pod',
    selectionCard = 'selection card',
    workflowDesigner = 'workflow designer',
    workflowNode = 'workflow node',
    time = 'time',
}

export enum ButtonName {
    CALENDAR = 'Calendar',
    COLUMN_DISPLAY = 'Columns display',
    VIEW_MODE = 'View mode',
    FILTER = 'Filter',
}

export enum CheckedOrUnchecked {
    CHECKED = 'checks',
    UNCHECKED = 'unchecks',
}

export enum ContainerElementTypes {
    SECTION = 'section',
    BLOCK = 'block',
}

export enum DisplayedOrHidden {
    DISPLAYED = 'displayed',
    HIDDEN = 'hidden',
}

export enum ElementContext {
    CARD_LIST = 'the card list',
    DETAIL_LIST = 'the detail list',
    HEADER_CARD = 'the header card',
    HELPER_PANEL = 'the detail panel',
    HELPER_PANEL_HEADER = 'the detail panel header',
    MAIN_PAGE = 'the main page',
    MAIN_PAGE_FOOTER = 'the main page footer',
    MAIN_PAGE_HEADER = 'the main page header',
    MODAL = 'a modal',
    MODAL_HEADER = 'a modal header',
    FULL_MODAL = 'a full width modal',
    NAVIGATION_PANEL = 'the navigation panel',
    SIDEBAR = 'the sidebar',
    MOBILE_SIDEBAR = 'the mobile sidebar',
}

export enum ExpandedOrCollapsed {
    EXPANDED = 'expands',
    COLLAPSED = 'collapses',
}

export enum FilterStatus {
    SHOW = 'Show',
    HIDE = 'Hide',
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export enum KEY {
    Alt = 'Alt',
    ArrowDown = 'ArrowDown',
    ArrowLeft = 'ArrowLeft',
    ArrowRight = 'ArrowRight',
    ArrowUp = 'ArrowUp',
    Backspace = 'Backspace',
    Cancel = 'Cancel',
    Clear = 'Clear',
    Comma = 'Comma',
    Control = 'Control',
    Delete = 'Delete',
    Dot = 'Dot',
    Eight = 'Eight',
    End = 'End',
    Enter = 'Enter',
    Equal = 'Equal',
    Escape = 'Escape',
    F1 = 'F1',
    F10 = 'F10',
    F11 = 'F11',
    F12 = 'F12',
    F2 = 'F2',
    F3 = 'F3',
    F4 = 'F4',
    F5 = 'F5',
    F6 = 'F6',
    F7 = 'F7',
    F8 = 'F8',
    F9 = 'F9',
    Five = 'Five',
    Four = 'Four',
    Help = 'Help',
    Home = 'Home',
    Insert = 'Insert',
    Meta = 'Meta',
    Minus = 'Minus',
    Multiply = 'Multiply',
    Nine = 'Nine',
    One = 'One',
    PageDown = 'PageDown',
    PageUp = 'PageUp',
    Pause = 'Pause',
    Plus = 'Plus',
    Return = 'Return',
    Semicolon = 'Semicolon',
    Seven = 'Seven',
    Shift = 'Shift',
    Six = 'Six',
    Slash = 'Slash',
    Space = 'Space',
    Tab = 'Tab',
    Three = 'Three',
    Two = 'Two',
    Unidentified = 'Unidentified',
    ZenkakuHankaku = 'ZenkakuHankaku',
    Zero = 'Zero',
}

export enum LookupStrategy {
    NONE = '',
    BIND = 'bound',
    LABEL = 'labelled',
}

export enum NonNestedFieldTypes {
    button = 'button',
    calendar = 'calendar',
    codeEditor = 'code editor',
    fileDeposit = 'file deposit',
    graphiqlEditor = 'graphiql editor',
    pdfViewer = 'pdf viewer',
    richText = 'rich text',
    scan = 'scan',
    separator = 'separator',
    table = 'table',
    textArea = 'text area',
    selectionCard = 'selection card',
    summaryTable = 'summary table',
    contentTable = 'content table',
    filterEditor = 'filter editor',
}

export enum TickState {
    TICKS = 'ticks',
    UNTICKS = 'unticks',
}

export enum LignAlignment {
    TOP = 'top',
    CENTER = 'center',
    BOTTOM = 'bottom',
}
