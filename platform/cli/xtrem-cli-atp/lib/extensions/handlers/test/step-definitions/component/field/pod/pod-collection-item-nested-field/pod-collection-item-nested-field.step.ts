import { Then, When } from '@cucumber/cucumber';
import { FieldTypes, LookupStrategy, fieldTypes } from '../../../../step-definitions-utils';
import { waitForPromises } from '../../wait-util';
import { PodCollectionItemNestedFieldObject } from './pod-collection-item-nested-field-object';

When(
    /^the user (selects|unselects) the "(.*)" (bound|labelled) nested checkbox field of the selected pod collection item$/,
    async (action: 'selects' | 'unselects', identifier: string, lookupStrategy: LookupStrategy) => {
        const nestedField = new PodCollectionItemNestedFieldObject({
            identifier,
            lookupStrategy,
            fieldType: fieldTypes.checkbox,
        });
        await nestedField.selectCheckBox(action === 'unselects', identifier);
    },
);

When(
    /^the user clicks the "(.*)" (bound|labelled) nested switch field of the selected pod collection item$/,
    async (identifier: string, lookupStrategy: LookupStrategy) => {
        const nestedField = new PodCollectionItemNestedFieldObject({
            identifier,
            lookupStrategy,
            fieldType: fieldTypes.switch,
        });
        await nestedField.selectSwitch(identifier);
    },
);

Then(
    /^the "(.*)" (bound|labelled) nested checkbox field of the selected pod collection item is (selected|unselected)$/,
    async (identifier: string, lookupStrategy: LookupStrategy, state: 'selected' | 'unselected') => {
        const nestedField = new PodCollectionItemNestedFieldObject({
            identifier,
            lookupStrategy,
            fieldType: fieldTypes.checkbox,
        });
        await nestedField.expectCheckBoxToBeSelected(state === 'unselected', identifier);
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested switch field of the selected pod collection item is set to "(.*)"$/,
    async (identifier: string, lookupStrategy: LookupStrategy, expectedValue: string) => {
        const nestedField = new PodCollectionItemNestedFieldObject({
            identifier,
            lookupStrategy,
            fieldType: fieldTypes.switch,
        });
        await nestedField.loseFocus();
        await nestedField.exceptSwitchValue(expectedValue, identifier);
    },
);

When(
    /^the user clicks in the "(.*)" (bound|labelled) nested (multi dropdown|multi reference) field of the selected pod collection item$/,
    async (identifier: string, lookupStrategy: LookupStrategy, fieldType: FieldTypes) => {
        const nestedField = new PodCollectionItemNestedFieldObject({ identifier, lookupStrategy, fieldType });
        await nestedField.clickMultiField(identifier);
    },
);

When(
    /^the user writes "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (multi dropdown|multi reference|reference|select|numeric|text area|text) field of the selected pod collection item$/,
    async (value: string, identifier: string, lookupStrategy: LookupStrategy, fieldType: FieldTypes) => {
        const nestedField = new PodCollectionItemNestedFieldObject({ identifier, lookupStrategy, fieldType });
        await nestedField.setNestedFieldValue({ value, fieldType, identifier });
        await waitForPromises(500, 'setNestedFieldValue');
    },
);

When(
    /^the user clears the "(.*)" (bound|labelled) nested (multi dropdown|multi reference) field of the selected pod collection item$/,
    async (identifier: string, lookupStrategy: LookupStrategy, fieldType: FieldTypes) => {
        const nestedField = new PodCollectionItemNestedFieldObject({ identifier, lookupStrategy, fieldType });
        // open the dropdown by clicking on arrow dropdown
        const selector = nestedField.cssSelector;
        const dropdownArrow = await browser.$(`${selector} .e-ui-select-inline-dropdown`);
        await dropdownArrow.scrollIntoView();
        await dropdownArrow.click();
        await nestedField.clearNestedField(fieldType);
        // close dropdown after clearing
        await nestedField.closeList();
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested (multi dropdown|multi reference|reference|select|numeric|progress|text area|label|text) field of the selected pod collection item is "(.*)"$/,
    async (identifier: string, lookupStrategy: LookupStrategy, fieldType: FieldTypes, expectedValue: string) => {
        const nestedField = new PodCollectionItemNestedFieldObject({ identifier, lookupStrategy, fieldType });

        await nestedField.expectNestedFieldValue({ expectedValue, fieldType, identifier });
    },
);

When(
    /^the user selects "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (multi dropdown|multi reference) field of the selected pod collection item$/,
    async (value: string, identifier: string, lookupStrategy: LookupStrategy, fieldType: FieldTypes) => {
        const nestedField = new PodCollectionItemNestedFieldObject({ identifier, lookupStrategy, fieldType });
        await PodCollectionItemNestedFieldObject.selectMultiOptionsInCollectionItemNestedField(nestedField, value);
    },
);

Then(
    /^at least the following list of options is displayed "([^"\n\r]*)" in the "([^"\n\r]*)" (bound|labelled) nested (multi dropdown|multi reference) field of the selected pod collection item$/,
    async (expectedOptions: string, identifier: string, lookupStrategy: LookupStrategy, fieldType: FieldTypes) => {
        const nestedField = new PodCollectionItemNestedFieldObject({ identifier, lookupStrategy, fieldType });
        await nestedField.selectDropDown.expectOptionsToBe(expectedOptions);
    },
);
