import { Then } from '@cucumber/cucumber';
import * as utils from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { FileDepositFieldObject } from './file-deposit-object';

// ----------
// File deposit field steps
// ----------

Then(/^the user adds the file "(.*)" to the file deposit field$/, async (relativeFilePath: string) => {
    const fileDepositField = <FileDepositFieldObject>StaticStore.getStoredField(utils.fieldTypes.fileDeposit);
    await fileDepositField.uploadFile(relativeFilePath);
});

Then(/^the user removes the file from the file deposit field$/, async () => {
    const fileDepositField = <FileDepositFieldObject>StaticStore.getStoredField(utils.fieldTypes.fileDeposit);
    await fileDepositField.clickDeleteFileButton();
});

Then(
    /^the user selects the "(.*)" (bound|labelled) file deposit field on (the main page|a modal|a full width modal|the sidebar|the navigation panel|the detail panel)$/,
    async (identifier: string, lookupStrategy: utils.LookupStrategy, context: utils.ElementContext) => {
        const fileDepositField = new FileDepositFieldObject({ identifier, lookupStrategy, context });
        await fileDepositField.dismissAllNotification();
        await StaticStore.storeField(utils.fieldTypes.fileDeposit, fileDepositField);
        await utils.waitForElementToExist({
            name: `Deposit field ${identifier}`,
            selector: fileDepositField.cssSelector,
        });
    },
);

Then(/^the user clicks the "(.*)" file in the file deposit field$/, async (identifier: string) => {
    const fileDepositField = <FileDepositFieldObject>StaticStore.getStoredField(utils.fieldTypes.fileDeposit);
    await fileDepositField.clickFileButton(identifier);
});
