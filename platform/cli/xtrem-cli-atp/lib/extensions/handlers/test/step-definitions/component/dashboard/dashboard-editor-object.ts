/* eslint-disable class-methods-use-this */
import { camelCase, kebabCase } from 'lodash';
import * as utils from '../../step-definitions-utils';
import AbstractPageObject from '../abstract-page-object';
import { getUserLocale, parseDateString } from '../field/date-utils';
import { waitForPromises } from '../field/wait-util';
import { testGlobals } from '../test-globals';

export class DashboardEditor extends AbstractPageObject {
    constructor() {
        super('.e-dashboard-editor');
    }

    async expectDashboardEditorTitle(title: string) {
        await this.expectTextContent({
            toBe: title,
            ignoreCase: false,
            cssSelector: '[data-testid="e-dashboard-editor-title"]',
            ignoreContext: false,
        });
    }

    expectDashboardEditorButtonEnabled(buttonName: string, reverse: boolean = false): Promise<void> {
        switch (buttonName) {
            case 'undo':
                return this.expectToBeEnabled('[data-testid="e-dashboard-editor-undo"]', reverse);
            case 'redo':
                return this.expectToBeEnabled('[data-testid="e-dashboard-editor-redo"]', reverse);
            default:
                throw new Error(`Expected button name to be "next" or "cancel", received: "${buttonName}"`);
        }
    }

    expectDashboardEditorFooterButtonEnabled(buttonName: string, reverse: boolean = false): Promise<void> {
        switch (buttonName) {
            case 'cancel':
                return this.expectToBeEnabled('[data-testid="e-dashboard-editor-dialog-cancel"]', reverse);
            case 'save':
                return this.expectToBeEnabled('[data-testid="e-dashboard-editor-dialog-save"]', reverse);
            default:
                throw new Error(`Expected button name to be "next" or "cancel", received: "${buttonName}"`);
        }
    }

    async clickTitleIcon() {
        await this.click('[data-testid~="e-dashboard-editor-title-edit"]');
    }

    async editDashboardTitle(value: string) {
        await this.write({ content: value, cssSelector: '[data-testid="e-dashboard-editor-title"]' });
    }

    async clickDashboardEditorButton(buttonName: string) {
        await this.click(`[data-testid="e-dashboard-editor-${kebabCase(buttonName)}"]`);
    }

    async clickDashboardEditorFooterButton(buttonName: string) {
        await this.click(`[data-testid="e-dashboard-editor-dialog-${kebabCase(buttonName)}"]`, true);
        await waitForPromises(500, 'Waiting for click');
    }

    async expectNavigationPanelTitle(title: string) {
        await this.expectTextContent({
            toBe: title,
            ignoreCase: false,
            cssSelector: '.e-page-navigation-panel-header-title',
            ignoreContext: false,
        });
    }

    async expectNavigationPanelCategory(name: string) {
        await this.expectToBeDisplayed(`[data-testid="e-mobile-table-separator-${camelCase(name)}"]`, false);
    }

    async expectDashboardWidget(name: string) {
        await this.expectToBeDisplayed(`[data-testid="db-widget-container-${camelCase(name)}"]`, false);
    }

    // async clickAdd(index: number) {
    //     const cards = await this.findAll('.e-card');
    //     const card = cards[index];
    //     if (!card) {
    //         throw new Error(`Could not find Add button at row ${index}`);
    //     }
    //     const button = await card.$('button');
    //     await button.click();
    // }

    async clickAdd(title: string) {
        const cards = await this.findAll('.e-card');
        let card;
        // eslint-disable-next-line no-restricted-syntax
        for (const c of cards) {
            const cardTitleElement = await c.$('.e-card-title');
            const cardTitle = await cardTitleElement.getText();
            if (cardTitle === title) {
                card = c;
            }
        }
        if (!card) {
            throw new Error(`Could not find Add button for row with title ${title}`);
        }
        const button = await card.$('a');
        await button.click();
    }

    async clickTabInDashboardEditor(tabIdentifier: string) {
        await this.click(`[data-testid~="e-xtrem-tab-${camelCase(tabIdentifier)}"]`);
    }

    async clickNavigationPanelButton(identifier: string, lookupStrategy: utils.LookupStrategy) {
        const selectorToUse = camelCase(identifier);
        const button =
            lookupStrategy === utils.LookupStrategy.BIND
                ? await this.find(`.e-business-action[data-testid~="e-field-bind-${selectorToUse}"]`)
                : await this.find(`.e-business-action[data-testid~="e-field-label-${selectorToUse}"]`);
        await utils.waitForElementToBeDisplayed({ name: 'button', selector: button.selector.toString() });
        await button.click();
    }

    async expectWidgetEditorDialogTitle(title: string) {
        const selector = '.e-widget-editor-dialog';
        await utils.waitForElementToBeDisplayed({ name: 'class', selector });
        const selectorToUse = `${selector} [data-component="heading"] h1`;
        await this.expectTextContent({
            toBe: title,
            ignoreCase: false,
            cssSelector: selectorToUse,
            ignoreContext: true,
        });
    }

    async expectWidgetEditorStepTitle(value: string) {
        const selectorToUse = '.e-widget-editor-dialog';
        await this.expectTextContains({
            toBe: value,
            ignoreCase: false,
            cssSelector: selectorToUse,
            ignoreContext: true,
        });
    }

    async expectWidgetEditorStepSubtitle(value: string) {
        const selectorToUse = '[data-testid="e-widget-editor-step-subtitle"]';
        await this.expectTextContent({
            toBe: value,
            ignoreCase: false,
            cssSelector: selectorToUse,
            ignoreContext: true,
        });
    }

    async editWidgetTitle(value: string, fieldName: string) {
        const cssSelector = `[data-testid="e-widget-editor-${fieldName}"]`;
        await utils.waitForElementToBeDisplayed({ name: 'widget title', selector: cssSelector });
        await this.write({ content: value, cssSelector, ignoreContext: true });
    }

    async setFieldValue({ value, name, rowId }: { value: string; name: string; rowId: number }) {
        const cssSelector = `[data-testid~="e-widget-editor-${name}-${rowId}"]`;
        await this.write({ content: value, cssSelector, ignoreContext: true });
    }

    async expectWidgetEditorFieldDisplayed(name: string, reverse: boolean = false) {
        const element = await this.find(
            `[data-testid~="e-widget-editor-${name}"]` || `[data-testid~="e-widget-editor-layout-page-${name}"]`,
            true,
        );
        await this.expectToBeDisplayed(`${element.selector.toString()}`, reverse);
    }

    async expectWidgetEditorFieldEnabled(name: string, reverse: boolean = false) {
        const element = await this.find(`[data-testid~="e-widget-editor-${name}"]`, true);
        await this.expectToBeEnabled(`${element.selector.toString()}`, reverse);
    }

    async expectRowFieldDisplayed({ name, rowId, reverse = false }: { name: string; rowId: number; reverse: boolean }) {
        const element = await this.find(`[data-testid~="e-widget-editor-${name}-${rowId}"]`, true);
        await this.expectToBeDisplayed(`${element.selector.toString()}`, reverse);
    }

    async expectRowFieldEnabled({ name, rowId, reverse = false }: { name: string; rowId: number; reverse: boolean }) {
        const element = await this.find(`[data-testid~="e-widget-editor-${name}-${rowId}"]`, true);
        await this.expectToBeEnabled(`${element.selector.toString()}`, reverse);
    }

    async selectWidget(widgetName: string) {
        const selector = `button[data-testid~="e-selection-card-${widgetName}"]`;
        const widgetCard = await this.find(selector, true);
        await widgetCard.scrollIntoView();
        await widgetCard.waitForClickable();
        await widgetCard.click();
    }

    async expectWidgetCardSelected(widgetName: string, reverse: boolean = false) {
        const selector = `button[data-testid~="e-selection-card-${widgetName}"].e-selection-card-selected`;
        const widgetCardSelected = await this.find(selector, true);
        await utils.waitForElementToBeDisplayed({
            name: 'widget selected',
            selector: widgetCardSelected.selector.toString(),
            reverse,
        });
    }

    async expectWidgetCardDisplayed(widgetName: string, reverse: boolean = false) {
        const selector = `[data-testid~="e-selection-card-${widgetName}"]`;
        const widgetCard = await this.find(selector, true);
        await utils.waitForElementToBeDisplayed({
            name: 'widget card',
            selector: widgetCard.selector.toString(),
            reverse,
        });
    }

    async expectButtonDisplayed(buttonName: string, reverse: boolean = false) {
        if (buttonName === 'update') {
            const selector = '[data-testid="e-widget-editor-dialog-add"]';
            const button = await this.waitForDisplayedAndGetElement({
                selector,
                ignoreContext: true,
            });
            const buttonText = await button.getText();
            if (buttonText !== 'Update') {
                throw new Error(`Expected button to be "update", received: "${buttonText}"`);
            }
            await this.expectToBeDisplayed(selector, reverse);
            return;
        }
        await this.expectToBeDisplayed(`[data-testid="e-widget-editor-dialog-${buttonName}"]`, reverse);
    }

    async expectButtonEnabled(buttonName: string, reverse: boolean = false) {
        if (buttonName === 'update') {
            const selector = '[data-testid="e-widget-editor-dialog-add"]';
            const button = await this.waitForDisplayedAndGetElement({
                selector,
                ignoreContext: true,
            });
            const buttonText = await button.getText();
            if (buttonText !== 'Update') {
                throw new Error(`Expected button to be "update", received: "${buttonText}"`);
            }
            await this.expectToBeEnabled(selector, reverse);
            return;
        }
        await this.expectToBeEnabled(`[data-testid="e-widget-editor-dialog-${buttonName}"]`, reverse);
    }

    async clickWidgetEditorButton(buttonName: string) {
        if (buttonName === 'update') {
            const selector = '[data-testid="e-widget-editor-dialog-add"]';
            const button = await this.waitForDisplayedAndGetElement({
                selector,
                ignoreContext: true,
            });
            const buttonText = await button.getText();
            if (buttonText !== 'Update') {
                throw new Error(`Expected button to be "update", received: "${buttonText}"`);
            }
            await browser.execute(`document.querySelector('${button.selector}').click()`);
            await waitForPromises(300, 'wait click to process');
            return;
        }
        await browser.execute(`document.querySelector('[data-testid="e-widget-editor-dialog-${buttonName}"]').click()`);
        await waitForPromises(300, 'wait click to process');
    }

    async selectUnselectWidgetData(name: string, state: string) {
        const selectorToUse = `[data-testid~="e-tree-view-checkbox-label-${camelCase(name)}"]`;
        const checkbox = await this.find(selectorToUse, true);
        await checkbox.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found. \nSelector: ${selectorToUse} `,
        });
        const select = state === 'selects';
        const selectionState = String(await checkbox.isSelected());
        if ((select && (!selectionState || selectionState === 'false')) || (!select && selectionState === 'true'))
            await checkbox.click();
    }

    async expectWidgetEditorTableButton(name: string, reverse: boolean = false) {
        const button = await this.find('[data-testid="add-item-button"', true);
        await utils.waitForElementToBeDisplayed({
            name: 'table button',
            selector: button.selector.toString(),
            reverse,
        });
        await this.expectTextContent({
            toBe: name,
            ignoreCase: false,
            cssSelector: button.selector.toString(),
            ignoreContext: true,
        });
    }

    async clickWidgetEditorTableButton(name: string) {
        const button = await this.find('[data-testid="add-item-button"', true);
        await this.expectTextContent({
            toBe: name,
            ignoreCase: false,
            cssSelector: button.selector.toString(),
            ignoreContext: true,
        });
        await button.click();
    }

    async clickWidgetEditorActionButton(name: string, rowId: number) {
        const actionButton = await this.find(
            `[data-element="flat-table-row"][data-testid="${rowId}"] [data-testid="flat-table-${name}-button"]`,
            true,
        );
        await actionButton.click();
    }

    async expectWidgetEditorActionButtonDisplayed({
        buttonName,
        rowId,
        reverse = false,
    }: {
        buttonName: string;
        rowId: number;
        reverse: boolean;
    }) {
        const actionButton = await this.find(
            `[data-element="flat-table-row"][data-testid="${rowId}"] [data-testid="flat-table-${buttonName}-button"]`,
            true,
        );
        await utils.expectElementToBeDisplayed({ selector: actionButton.selector.toString(), reverse });
    }

    async expectValidationError({ value, rowId, reverse = false }: { value: string; rowId: number; reverse: boolean }) {
        const selectorToUse = `[data-element="flat-table-row"][data-testid="${rowId}"] span[type="error"]`;
        await utils.waitForElementToBeDisplayed({ name: 'validation error', selector: selectorToUse, reverse });
        if (!reverse) {
            await this.click(selectorToUse, true);
            const portalEntrance = await this.find(
                `[data-element="flat-table-row"][data-testid="${rowId}"] [data-portal-entrance]`,
                true,
            );
            const portalId = await portalEntrance.getAttribute('data-portal-entrance');
            const portalExit = await this.find(`[data-portal-exit="${portalId}"]`, true);
            const actualValue = await portalExit.getText();
            if (actualValue.toLowerCase() !== value.toLowerCase()) {
                throw new Error(`Expected value: ${value}, actual: ${actualValue}.\nSelector: ${selectorToUse}`);
            }
        }
    }

    async expectDateFieldToBeDisplayed({
        name,
        rowId,
        reverse = false,
    }: {
        name: string;
        rowId: number;
        reverse: boolean;
    }) {
        const element = await this.find(
            `[data-element="flat-table-row"][data-testid="${rowId}"] [data-element="${name}-date"]`,
            true,
        );
        await utils.expectElementToBeDisplayed({ selector: element.selector.toString(), reverse, name: 'data field' });
    }

    async editDateField({ date, name, rowId }: { date: string; name: string; rowId: number }) {
        const lang = await getUserLocale();

        let dateField = await this.find(
            `[data-element="flat-table-row"][data-testid="${rowId}"] [data-element="${name}-date"]`,
            true,
        );

        if (!(await dateField.isExisting())) {
            dateField = await this.find(
                `[data-element="flat-table-row"][data-testid="${rowId}"] [data-testid="e-widget-editor-${name}-${rowId}"]`,
                true,
            );
        }

        await dateField.click();
        await dateField.clearValue();
        if (testGlobals.device !== 'desktop') {
            await dateField.setValue(date);
        } else {
            const arrValue = [...date];
            for (let i = 0; i < arrValue.length; i += 1) {
                await browser.keys(arrValue[i]);
            }
        }
        await waitForPromises(2000, 'wait for day picker');

        const isDayPickerOpen = await (await this.find('.rdp-root', true)).isExisting();

        if (isDayPickerOpen) {
            const { day, year, month } = parseDateString(date, lang);

            // click the selected date so the date picker to closes
            await (await this.find(`.rdp-root [data-day="${year}-${month}-${day}"]`, true)).click();
        }
    }

    async expectFieldContent({ name, rowId, value }: { name: string; rowId: number; value: string }) {
        await this.expectValue({
            toBe: value,
            cssSelector: `[data-testid="e-widget-editor-${name}-${rowId}"]`,
            ignoreContext: true,
        });
    }

    async expectPFieldContent({ value }: { name: string; rowId: number; value: string }) {
        await this.expectTextContains({
            toBe: value,
            ignoreCase: false,
            cssSelector: '.e-widget-editor-section',
            ignoreContext: true,
        });
    }

    async tickUntickCheckboxField(value: string, state: string) {
        const selectorToUse = `[data-testid="e-widget-editor-layout-isEnabled-${camelCase(value)}Action"]`;
        const selectedItem = await this.find(selectorToUse, true);

        await selectedItem.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found. \nSelector: ${selectorToUse} `,
        });
        const select = state === 'ticks';
        const selectionState = await selectedItem.isSelected();
        if ((select && !selectionState) || (!select && selectionState === true)) {
            await selectedItem.click();
        }
    }

    async expectWidgetEditorPreviewButton(buttonName: string, reverse: boolean) {
        const selector = `.e-widget-editor [data-testid="db-widget-call-to-actions-${camelCase(buttonName)}"]`;
        const previewButton = await this.find(selector, true);
        await utils.expectElementToBeDisplayed({ selector: previewButton.selector.toString(), reverse });
    }

    async expectWidgetFieldContent(name: string, value: string) {
        await this.expectValue({
            toBe: value,
            cssSelector: `[data-testid="e-widget-editor-${name}"]`,
            ignoreContext: true,
        });
    }

    async expectWidgetDataFound(label: string, reverse: boolean) {
        const selector = `[data-testid="e-tree-view-container-label-${camelCase(label)}"]`;
        await utils.waitForElementToExist({ name: 'data field', selector, reverse });
        await utils.waitForElementToBeDisplayed({ name: 'tree-view element', selector, reverse });
    }

    async expectWidgetDataSelected(label: string, reverse: boolean) {
        const selector = `[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-${camelCase(label)}"]`;
        const treeViewDataElement = await this.find(selector, true);
        const ariaChecked = String(await treeViewDataElement.isSelected());
        utils.expectAriaChecked({ toBe: ariaChecked, reverse, selector });
    }

    async expectFieldValueInRow({ name, rowNumber, value }: { name: string; rowNumber: number; value: string }) {
        const cssSelector = `[data-testid~="e-widget-editor-${name}-${rowNumber}"]`;
        await this.expectValue({ toBe: value, cssSelector, ignoreContext: true });
    }

    async expectCheckboxFieldChecked(fieldName: string, reverse: boolean) {
        const selector = `[data-testid="e-widget-editor-layout-isEnabled-${camelCase(fieldName)}Action"]`;
        const checkboxField = await this.find(selector, true);
        const ariaChecked = String(await checkboxField.isSelected());
        utils.expectAriaChecked({ toBe: ariaChecked, reverse, selector });
    }

    async searchTreeElement(value: string) {
        const searchInput = `${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-data-step-search' })} input`;
        await this.write({ content: value, cssSelector: searchInput, ignoreContext: true });
    }

    async clearSearch(fieldType: utils.FieldTypes = utils.fieldTypes.text) {
        const searchInput = `${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-data-step-search' })} input`;
        await this.clearInput({ ignoreContext: true, fieldType, cssSelector: searchInput });
    }
}

export default DashboardEditor;
