import allureReporter from '@wdio/allure-reporter';
import { runtimeParameters } from '../../../../../parameters';
import { FieldTypes } from '../step-definitions-utils';
import { FieldObject } from './field/field-object';
import { NodeStepTreeObject } from './field/form-designer/node-step-tree-object';
import { WorkflowNodeObject } from './field/workflow-designer/workflow-node-object';

export enum StoredKeys {
    GRAPHIQL_EDITOR = 'GRAPHIQL_EDITOR',
    NESTED_GRID_PHANTOM_ROW_SELECTOR = 'NESTED_GRID_PHANTOM_ROW_SELECTOR',
    NESTED_GRID_ROW_ATTRIBUTES = 'NESTED_GRID_ROW_ATTRIBUTES',
    NESTED_GRID_ROW_PARENT = 'NESTED_GRID_ROW_PARENT',
    MOBILE_NESTED_GRID_ROW_SELECTOR = 'MOBILE_NESTED_GRID_ROW_SELECTOR',
    NODE_BROWSER_TREE_ELEMENT = 'NODE_BROWSER_TREE_ELEMENT',
    NODE_STEP_TREE_ELEMENT = 'NODE_STEP_TREE_ELEMENT',
    POD_COLLECTION_ITEM = 'POD_COLLECTION_ITEM',
    POD_OBJECT = 'POD_OBJECT',
    DASHBOARD_WIDGET = 'DASHBOARD_WIDGET',
    DASHBOARD_WIDGET_TABLE_CARD = 'DASHBOARD_WIDGET_TABLE_CARD',
    DASHBOARD_WIDGET_TABLE_CARD_SWITCH_CARD_ROW = 'DASHBOARD_WIDGET_TABLE_CARD_SWITCH_CARD_ROW',
    STEPSEQUENCE = 'STEP_SEQUENCE',
    FILE_DEPOSIT = 'FILE_DEPOSIT',
    TABLE = 'TABLE',
    TABLE_NESTED_FIELD = 'TABLE_NESTED_FIELD',
    ROW = 'ROW',
    TREE = 'TREE',
    COLUMN = 'COLUMN',
    NOTIFICATION = 'NOTIFICATION',
    TABLE_EDITOR = 'TABLE_EDITOR',
    CURRENT_LOCKED_FUNCTION = 'CURRENT_LOCKED_FUNCTION',
}

export class StaticStore {
    constructor() {
        (global as any).staticStore = {};
    }

    // eslint-disable-next-line class-methods-use-this
    setValue = (key: string, value: any) => {
        const scope = global as any;
        scope.staticStore[key] = value;
    };

    // eslint-disable-next-line class-methods-use-this
    getObject = (key: string): any => {
        return (global as any).staticStore[key];
    };

    // eslint-disable-next-line class-methods-use-this
    getString = (key: string): string => {
        return (global as any).staticStore[key] as string;
    };

    // eslint-disable-next-line class-methods-use-this
    getUserdefinedKeyValueFromStore = (key: string): string => {
        return (global as any).staticStore[key] as string;
    };
}

export const staticStore = new StaticStore();

export const storeString = (key: StoredKeys, str: string) => {
    allureReporter.addAttachment(`Stored value: ${key}`, `${str}`, 'text/plain');
    staticStore.setValue(key, str);
};

export const getStoredString = (key: string) => {
    const storeValue = staticStore.getString(key);
    if (storeValue) allureReporter.addAttachment(`Stored value: ${key}`, `${storeValue}`, 'text/plain');
    return storeValue;
};

export const storeField = async (
    key: FieldTypes,
    fieldObject: FieldObject | NodeStepTreeObject | WorkflowNodeObject | WebdriverIO.Element,
) => {
    if (fieldObject instanceof FieldObject) await (await fieldObject.get()).waitForExist();
    staticStore.setValue(key, fieldObject);
};

export const getStoredField = (key: FieldTypes) => {
    return staticStore.getObject(key);
};

export const storeObject = <T extends Object>(key: StoredKeys | string, object: T): void => {
    if (!Object.values(StoredKeys).includes(key as StoredKeys)) {
        allureReporter.addAttachment(`Stored value: ${key}`, `${object.toString()}`, 'text/plain');
    }
    staticStore.setValue(key, object);
};

export const getStoredObject = <T>(key: StoredKeys): T => {
    const storeValue = staticStore.getObject(key) as T;
    if (!Object.values(StoredKeys).includes(key as StoredKeys)) {
        allureReporter.addAttachment(`Stored value: ${key}`, `${storeValue}`, 'text/plain');
    }
    return storeValue;
};

export const getUserdefinedKeyValueFromStore = (value: string) => {
    let newValue = value;
    let keyValue = '';
    const keyValues = value.split('[');
    if (value === 'null' || value == null) {
        return '';
    }
    keyValues.forEach(kV => {
        keyValue = kV.split(']')[0];
        const storeValue = getStoredString(`[${keyValue}]`);
        if (storeValue) newValue = newValue.replace(`[${keyValue}]`, storeValue);
    });
    if (value.indexOf('param:') > -1) {
        const param = runtimeParameters.getStringOrParameter(value);
        if (param) newValue = param;
    }

    return newValue;
};
