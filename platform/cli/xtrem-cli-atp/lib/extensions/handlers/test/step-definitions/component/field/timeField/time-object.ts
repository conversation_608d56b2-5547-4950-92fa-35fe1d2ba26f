import { Key } from 'webdriverio';
import * as utils from '../../../step-definitions-utils';
import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { getUserLocale } from '../date-utils';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

export class TimeFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'time', identifier, lookupStrategy, context });
    }

    async isMandatory() {
        const selectorToUse = utils.getDataTestIdSelector({
            domSelector: 'label',
            dataTestIdValue: `e-field-label`,
        });
        const isMandatoryElement = await $(`${this.cssSelector} ${selectorToUse}`);

        if (!(await isMandatoryElement.isExisting()) || !(await isMandatoryElement.getText()).includes('*')) {
            throw new Error(` Expected element to be mandatory.\nSelector: ${this.cssSelector} ${selectorToUse}`);
        }
    }

    async setHoursMin(targetTime: string) {
        await this.timeState(targetTime);
        const [hours, minutes] = await this.extractTime(targetTime);
        await this.setTimeField('hours', hours);
        await this.setTimeField('minutes', minutes);
        await waitForPromises(500, 'Waiting for popup to open');
    }

    async checkHoursMinSegment(segment: 'hours' | 'minutes', expectedValue: string) {
        const actualValue = await this.getTimeFieldValue(segment);
        if (actualValue !== expectedValue) {
            throw new Error(
                `Expected value for "${segment}": "${expectedValue}", actual: "${actualValue}".\nSelector: ${this.cssSelector}`,
            );
        }
    }

    async expectHoursMinToEqual(targetTime: string) {
        const [hours, minutes] = await this.extractTime(targetTime);
        const hourValue = await this.getTimeFieldValue('hours');
        const minutesValue = await this.getTimeFieldValue('minutes');
        if (hourValue !== hours || minutesValue !== minutes) {
            throw new Error(
                `Expected value: "${targetTime}", actual: "${hourValue}:${minutesValue}".\nSelector: ${this.cssSelector}`,
            );
        }
        await waitForPromises(500, 'Waiting for popup to open');
    }

    // eslint-disable-next-line class-methods-use-this
    private async extractTime(targetDate: string): Promise<[string, string]> {
        const regex = /(\d{1,2})[,:.](\d{2})/;
        const match = targetDate.match(regex);
        const lang = await getUserLocale();

        if (!match || !/^[^a-zA-Z]*$/.test(targetDate)) {
            throw new Error(`Invalid time format ${targetDate}. Expected HH:MM`);
        }

        const [hours, minutes] = [parseInt(match[1], 10), parseInt(match[2], 10)];

        const isEnUS = lang === 'en-US';
        // eslint-disable-next-line no-restricted-globals
        const isHourInvalid = isNaN(hours) || (isEnUS ? hours < 1 || hours > 12 : hours < 0 || hours > 23);

        if (isHourInvalid) {
            const hourRange = isEnUS ? '1 and 12' : '0 and 23';
            throw new Error(`Invalid hour: "${hours}". Must be between ${hourRange}.`);
        }

        // eslint-disable-next-line no-restricted-globals
        if (isNaN(minutes) || minutes < 0 || minutes > 59) {
            throw new Error(`Invalid minutes: "${minutes}". Must be between 0 and 59.`);
        }

        return [match[1], match[2]];
    }

    private async setTimeField(field: 'hours' | 'minutes', value: string) {
        let cssSelector = utils.getDataTestIdSelector({
            domSelector: 'input',
            dataTestIdValue: `e-field-bind-field-${field}`,
        });
        cssSelector = cssSelector.replace(/=/g, '~=');
        const inputField = await $(cssSelector);
        await inputField.waitForExist();
        await inputField.waitForDisplayed();
        await inputField.click();
        await inputField.clearValue();
        await this.write({ content: value.toString(), cssSelector, ignoreContext: true }); // e.g., "08"
        await browser.keys(Key.Tab);
    }

    // eslint-disable-next-line class-methods-use-this
    private async getTimeFieldValue(field: 'hours' | 'minutes') {
        let cssSelector = utils.getDataTestIdSelector({
            domSelector: 'input',
            dataTestIdValue: `e-field-bind-field-${field}`,
        });
        cssSelector = cssSelector.replace(/=/g, '~=');
        const inputField = await $(cssSelector);
        const timeValue = await inputField.getAttribute('value');
        return timeValue;
    }

    async toggleDateRangeButton(targetDate: string): Promise<void> {
        const selectorToUse = utils.getDataComponentSelector('div', 'button-toggle');
        const buttonSelector = `${selectorToUse} button[aria-label="${targetDate}"]`;
        const toggleButton = await $(buttonSelector);

        await this.timeState(targetDate);

        try {
            await waitForPromises(500, 'Waiting for popup to open');

            const checkboxValue = await toggleButton.getAttribute('aria-pressed');
            const isSelected = checkboxValue;

            if (isSelected === 'false') {
                await toggleButton.waitForExist();
                await toggleButton.waitForDisplayed();
                await toggleButton.scrollIntoView();
                await toggleButton.click();
                await waitForPromises(300, 'Waiting for checkbox click');
            }
        } catch (error) {
            throw new Error(`Expected element could not be found: "${targetDate}". \nSelector: ${selectorToUse}`);
        }
    }

    async timeState(identifier?: string): Promise<void> {
        const selectorToUse = await $(this.cssSelector);
        const label = identifier ? `"${identifier}" ` : '';
        const classAttr = await selectorToUse.getAttribute('class');

        if (classAttr.includes('e-hidden')) {
            throw new Error(`Expected element could not be found: ${label}.\n${selectorToUse.selector}`);
        }

        if (classAttr.includes('e-read-only') || classAttr.includes('e-disabled')) {
            throw new Error(`Expected element ${label} to be enabled.\n${selectorToUse.selector}`);
        }
    }
}
