import { camelCase } from 'lodash';
import { ElementContext, LookupStrategy } from '../enums';
import { fieldTypes } from '../index';
import { DomSelectorTypes, FieldTypes, NestedFieldTypes } from '../types';

export const getMultipleDataTestIdSelector = (domSelector: DomSelectorTypes, dataTestIdValues: string[]) =>
    dataTestIdValues.reduce(
        (currentValue, dataTestIdValue) => `${currentValue}[data-testid~="${dataTestIdValue}"]`,
        domSelector,
    );

export const getDataTestIdSelector = ({
    domSelector,
    dataTestIdValue,
    partialMatch = false,
}: {
    domSelector: DomSelectorTypes;
    dataTestIdValue: string;
    partialMatch?: boolean;
}) =>
    partialMatch
        ? `${domSelector}[data-testid~="${dataTestIdValue}"]`
        : `${domSelector}[data-testid="${dataTestIdValue}"]`;

export const getDataComponentSelector = (domSelector: DomSelectorTypes, dataComponentValue: string) =>
    `${domSelector}[data-component="${dataComponentValue}"]`;

export const getElementTypeSelector = (elementType: FieldTypes | NestedFieldTypes) => {
    switch (elementType) {
        case fieldTypes.button:
            return fieldTypes.button;
        case fieldTypes.calendar:
            return '';
        case fieldTypes.count:
        case fieldTypes.icon:
        case fieldTypes.dateTimeRange:
        case fieldTypes.time:
            return 'div';
        case fieldTypes.staticContent:
            return 'div';
        case fieldTypes.label:
            return 'span span';
        case fieldTypes.image:
        case fieldTypes.link:
        case fieldTypes.progress:
        case fieldTypes.relativeDate:
            return 'span';
        case fieldTypes.checkbox:
        case fieldTypes.date:
        case fieldTypes.dropdownList:
        case fieldTypes.filterSelect:
        case fieldTypes.numeric:
        case fieldTypes.reference:
        case fieldTypes.scan:
        case fieldTypes.select:
        case fieldTypes.text:
        case fieldTypes.vitalPod:
            return 'input';
        case fieldTypes.richText:
            return '.ck-content';
        case fieldTypes.multiDropdown:
        case fieldTypes.multiReference:
            return 'div[role="presentation"]';
        case fieldTypes.textArea:
            return 'textarea';
        case fieldTypes.codeEditor:
            return '.monaco-editor';
        case fieldTypes.graphiqlEditor:
            return '.graphiql-editor';
        case fieldTypes.pdfViewer:
            return 'pdfviewer';
        case fieldTypes.switch:
            return 'div[type]';
        default:
            throw new Error(`Invalid element type: ${elementType}`);
    }
};

export const getNestedElementTypeSelector = (elementType: NestedFieldTypes) => {
    switch (elementType) {
        case fieldTypes.checkbox:
            return 'input';
        case fieldTypes.count:
        case fieldTypes.date:
        case fieldTypes.dropdownList:
        case fieldTypes.numeric:
        case fieldTypes.radio:
        case fieldTypes.select:
        case fieldTypes.switch:
        case fieldTypes.text:
        case fieldTypes.filterSelect:
        case fieldTypes.staticContent:
            return '';
        case fieldTypes.icon:
        case fieldTypes.progress:
            return 'div.e-field-progress-progressbar';
        case fieldTypes.image:
            return 'img';
        case fieldTypes.label:
            return 'span span';
        case fieldTypes.link:
            return 'a';
        case fieldTypes.reference:
            return 'span';
        default:
            throw new Error(`Invalid element type: ${elementType}`);
    }
};

export const getContextSelector = (context: ElementContext) => {
    switch (context) {
        case ElementContext.CARD_LIST:
            return `${getMultipleDataTestIdSelector('div', ['e-table-field'])} .e-table-field-mobile`;
        case ElementContext.DETAIL_LIST:
            return '.e-detail-list .e-detail-list-container';
        case ElementContext.HEADER_CARD:
            return `div[id="root"] ${getDataTestIdSelector({ domSelector: 'header', dataTestIdValue: 'e-header' })} ${getDataTestIdSelector(
                {
                    domSelector: 'div',
                    dataTestIdValue: 'e-card',
                },
            )} .e-context-page-header`;
        case ElementContext.HELPER_PANEL:
            return `div[id="root"] ${getDataTestIdSelector({ domSelector: 'aside', dataTestIdValue: 'e-detail-panel' })}`;
        case ElementContext.HELPER_PANEL_HEADER:
            return `div[id="root"] ${getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-detail-panel-header' })}`;
        case ElementContext.MAIN_PAGE:
            return 'div[id="root"] main';
        case ElementContext.MAIN_PAGE_HEADER:
            return `div[id="root"] ${getDataTestIdSelector({ domSelector: 'header', dataTestIdValue: 'e-header' })}`;
        case ElementContext.MAIN_PAGE_FOOTER:
            return 'div[id="root"] .e-page-footer-container';
        case ElementContext.MODAL:
            return 'div[data-component="dialog"]';
        case ElementContext.FULL_MODAL:
            return 'div[data-component="dialog-full-screen"]';
        case ElementContext.NAVIGATION_PANEL:
            return '.e-page-navigation-panel';
        case ElementContext.SIDEBAR:
            return 'div[data-component="sidebar"]';
        case ElementContext.MOBILE_SIDEBAR:
            return 'div[data-element="dialog-full-screen"]';
        default:
            throw new Error(`Unexpected context: ${context}`);
    }
};

export const getLookupStrategySelectorwithNoDom = ({
    fieldType,
    lookupStrategy,
    identifier,
}: {
    fieldType: string;
    lookupStrategy: LookupStrategy;
    identifier: string;
}) => {
    return getMultipleDataTestIdSelector('', [
        `e-${fieldType}-field`,
        `e-field-${lookupStrategy === LookupStrategy.BIND ? `bind-${identifier}` : `label-${camelCase(identifier)}`}`,
    ]);
};

export const getLookupStrategySelector = ({
    fieldType,
    lookupStrategy,
    identifier,
    domSelector = 'div',
}: {
    fieldType: string;
    lookupStrategy: LookupStrategy;
    identifier: string;
    domSelector?: DomSelectorTypes;
}) => {
    return getMultipleDataTestIdSelector(domSelector, [
        `e-${fieldType}-field`,
        `e-field-${lookupStrategy === LookupStrategy.BIND ? `bind-${identifier}` : `label-${camelCase(identifier)}`}`,
    ]);
};

export const getNestedFieldType = (nestedFieldType: FieldTypes) => {
    return nestedFieldType === fieldTypes.dateTimeRange ? 'datetimerange' : nestedFieldType;
};

export const getTableNestedFieldSelector = ({
    lookupStrategy,
    identifier,
    domSelector = 'div',
    fieldType,
}: {
    lookupStrategy: LookupStrategy;
    identifier: string;
    domSelector?: DomSelectorTypes;
    fieldType?: FieldTypes;
}) => {
    return `${domSelector}.e-nested-cell-${
        lookupStrategy === LookupStrategy.BIND ? `bind-${identifier}` : `label-${camelCase(identifier)}`
    }${fieldType ? `.e-nested-cell-field-${getNestedFieldType(fieldType)}` : ''}`;
};

export const getHeaderLookupStrategySelector = ({
    fieldType,
    lookupStrategy,
    identifier,
    domSelector = 'div',
}: {
    fieldType: string;
    lookupStrategy: LookupStrategy;
    identifier: string;
    domSelector?: DomSelectorTypes;
}) => {
    return getMultipleDataTestIdSelector(domSelector, [
        `e-${fieldType}-${
            lookupStrategy === LookupStrategy.BIND ? `bind-${identifier}` : `label-${camelCase(identifier)}`
        }`,
    ]);
};

export const getPageBusinessActionSelector = ({
    fieldType,
    identifier,
    domSelector = 'span',
}: {
    fieldType: string;
    identifier: string;
    domSelector?: DomSelectorTypes;
}) => {
    return getMultipleDataTestIdSelector(domSelector, [`e-page-${fieldType}-${identifier}`]);
};

export const getPagingSelector = (level: number, parentId?: number) => {
    return level <= 0
        ? '.ag-theme-balham > div > .ag-root-wrapper > .ag-paging-panel'
        : `.nested-row-level-${level - 1}-${parentId} > .ag-details-row > .ag-details-grid > div > .ag-paging-panel`;
};

export const getLookupId = (s: LookupStrategy, id: string) =>
    s === LookupStrategy.BIND ? `bind-${id}` : `label-${camelCase(id)}`;
