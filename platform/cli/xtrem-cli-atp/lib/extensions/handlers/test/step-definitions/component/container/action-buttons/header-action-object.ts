import * as utils from '../../../step-definitions-utils';
import AbstractPageObject from '../../abstract-page-object';

export enum HeaderActionStatus {
    disabled = 'disabled',
    enabled = 'enabled',
}

export class HeaderActionObject extends AbstractPageObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        super(
            (context ? `${utils.getContextSelector(context)} ` : '') +
                utils.getHeaderLookupStrategySelector({
                    fieldType: 'header-action',
                    lookupStrategy,
                    identifier,
                    domSelector: 'button',
                }),
        );
    }

    async expectStatusToBe(status: HeaderActionStatus) {
        const selector =
            status === HeaderActionStatus.enabled || status === HeaderActionStatus.disabled
                ? `${this.cssSelector}`
                : this.cssSelector;
        switch (status) {
            case HeaderActionStatus.enabled:
                await this.expectToBeEnabled(selector);
                break;
            case HeaderActionStatus.disabled:
                await this.expectNotToBeEnabled(selector);
                break;
            default:
                throw new Error('STATUS NOT DEFINED');
        }
    }
}
