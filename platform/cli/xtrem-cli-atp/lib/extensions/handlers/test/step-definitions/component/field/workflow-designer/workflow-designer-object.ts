import * as utils from '../../../step-definitions-utils';
import { Sidebar } from '../../sidebar/sidebar-object';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';
import { WorkflowNodeObject } from './workflow-node-object';

export const SELECTORS = {
    workflowDesignerButton: 'button[data-component=button]',
    workflowDesignerTextContent: '.e-workflow-empty-content h3',
    workflowToolbarButtons: 'div[data-testid="rf__controls"] button',
    workflowNodes: 'div.react-flow__nodes div[data-testid~="e-workflow-node"]',
    workflowAddSelectionCards: 'div.e-workflow-add-dialog div.selection-card-container button.e-selection-card',
    workflowAddButtons: 'div[data-element="form-footer"] button',
};

export class WorkflowDesignerFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        super({ fieldType: 'workflow', identifier, lookupStrategy, context });
    }

    async getElementFromElementsAndClick({
        selectorToUse,
        elementType,
        elementValue,
        attributeOrText,
        attributeOrElementSelector,
        sideBar,
    }: {
        selectorToUse: string;
        elementType: string;
        elementValue: string;
        attributeOrText: 'Text' | 'Attribute';
        attributeOrElementSelector: string;
        sideBar?: Sidebar;
    }) {
        const fullSelector = `${sideBar ? sideBar.cssSelector : this.cssSelector} ${selectorToUse}`;
        const elementsToSearch = await this.findAll(fullSelector, true);

        let elementToFind;
        // eslint-disable-next-line no-restricted-syntax
        for (const searchElement of elementsToSearch) {
            let elementSearchValue = '';
            if (attributeOrText === 'Text') {
                elementSearchValue = await searchElement.$(attributeOrElementSelector).getText();
            } else {
                elementSearchValue = await searchElement.getAttribute(attributeOrElementSelector);
            }

            if (elementSearchValue.toLowerCase() === elementValue.toLowerCase()) {
                elementToFind = searchElement;
            }
        }

        if (elementToFind === undefined) {
            throw new Error(
                `Expected element could not be found: "${elementValue}" ${elementType}.\nSelector: ${this.cssSelector} ${selectorToUse}`,
            );
        }

        return elementToFind;
    }

    async clickWorkflowDesignerButton(buttonName: string) {
        const button = await this.getElementFromElementsAndClick({
            selectorToUse: SELECTORS.workflowDesignerButton,
            elementType: 'button',
            elementValue: buttonName,
            attributeOrText: 'Text',
            attributeOrElementSelector: 'span[data-element="main-text"]',
        });
        await button.click();

        await waitForPromises(200, `click workflow designer button`);
    }

    async checkWorkflowDesignerTextContent(expectedValue: string) {
        await this.expectTextContent({ toBe: expectedValue, cssSelector: `${SELECTORS.workflowDesignerTextContent}` });
    }

    async clickWorkflowToolbarAction(toolbarActionName: string) {
        const toolbarAction = await this.getElementFromElementsAndClick({
            selectorToUse: SELECTORS.workflowToolbarButtons,
            elementType: 'action',
            elementValue: toolbarActionName,
            attributeOrText: 'Attribute',
            attributeOrElementSelector: 'title',
        });

        await toolbarAction.scrollIntoView();

        await waitForPromises(200, `click workflow designer button`);
    }

    async getWorkflowNode(workflowNodeTitle: string) {
        const workflowNode = await this.getElementFromElementsAndClick({
            selectorToUse: SELECTORS.workflowNodes,
            elementType: 'field',
            elementValue: workflowNodeTitle,
            attributeOrText: 'Text',
            attributeOrElementSelector: 'div.e-workflow-node-title-label',
        });

        const nodeId = await workflowNode.getAttribute('data-nodeid');

        await this.scrollTo({
            selector: `${this.cssSelector} ${SELECTORS.workflowNodes}[data-nodeid="${nodeId}"]`,
            ignoreContext: true,
        });

        return new WorkflowNodeObject(`${this.cssSelector} ${SELECTORS.workflowNodes}[data-nodeid="${nodeId}"]`);
    }

    async selectCardInWorkflowAddDialog(cardTitle: string) {
        const sidebar = new Sidebar();

        const selectionCard = await this.getElementFromElementsAndClick({
            selectorToUse: SELECTORS.workflowAddSelectionCards,
            elementType: 'selection card',
            elementValue: cardTitle,
            attributeOrText: 'Text',
            attributeOrElementSelector: 'div[data-testid="e-selection-card-title"]',
            sideBar: sidebar,
        });

        if (!(await selectionCard.getAttribute('class')).includes('e-selection-card-selected')) {
            await selectionCard.click();
            await waitForPromises(200, 'click selection card');
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async searchCardInWorkflowAddDialog(cardTitle: string) {
        const sidebar = new Sidebar();
        const selectorToUse = utils.getDataTestIdSelector({
            domSelector: 'div',
            dataTestIdValue: 'e-selection-card-filter',
        });
        const inputElement = await $(`${sidebar.cssSelector} ${selectorToUse} input`);
        try {
            await inputElement.click();
            await inputElement.clearValue();
            await inputElement.addValue(cardTitle);
            await waitForPromises(200, `wait for write`);
        } catch (error) {
            throw new Error(`Expected element could not be found:\nSelector: ${inputElement}`);
        }
    }

    async clickWorkflowAddSidebarButton(buttonName: string) {
        const sidebar = new Sidebar();

        const sideBarButton = await this.getElementFromElementsAndClick({
            selectorToUse: SELECTORS.workflowAddButtons,
            elementType: 'button',
            elementValue: buttonName,
            attributeOrText: 'Text',
            attributeOrElementSelector: 'span[data-element="main-text"]',
            sideBar: sidebar,
        });

        await sideBarButton.click();

        await waitForPromises(200, `click workflow designer button`);
    }
}
