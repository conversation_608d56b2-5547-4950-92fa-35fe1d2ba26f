import { Then } from '@cucumber/cucumber';
import { ElementContext, LookupStrategy } from '../../step-definitions-utils';
import { MultiActions } from './multi-action-object';

Then(
    /^the user clicks the "(.*)" (bound|labelled) multi action button on (the main page|a modal|a modal header|a full width modal|the detail panel|the navigation panel)$/,
    async (buttonName: string, lookupStrategy: LookupStrategy, context: ElementContext) => {
        const Action = new MultiActions(context);
        await Action.dismissAllNotification();
        await Action.clickMultiActionsButton(buttonName, lookupStrategy);
    },
);
