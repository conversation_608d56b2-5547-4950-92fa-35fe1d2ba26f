import { Then, When } from '@cucumber/cucumber';
import { Key } from 'webdriverio';
import * as utils from '../../../../step-definitions-utils';
import pageModel from '../../../main-page';
import * as StaticStore from '../../../static-store';
import { PodCollectionObject } from './pod-collection-object';

When(
    /^the user selects the "(.*)" (bound|labelled) pod collection field on (the main page|a modal|a full width modal|the detail panel|the sidebar)$/,
    async (identifier: string, lookupStrategy: utils.LookupStrategy, context: utils.ElementContext) => {
        await pageModel.waitForFinishLoading();
        const field = new PodCollectionObject({ identifier, lookupStrategy, context });
        await field.dismissAllNotification();
        await browser.keys(Key.Tab);
        await utils.waitForElementToExist({ name: 'pod collection field', selector: field.cssSelector });
        await StaticStore.storeField(utils.fieldTypes.podCollection, field);
    },
);

When(/^the user clicks the "(.*)" button of the selected pod collection field$/, async (name: string) => {
    const field = <PodCollectionObject>StaticStore.getStoredField(utils.fieldTypes.podCollection);
    await field.loseFocus();
    await field.selectPodCollectionBtn(name);
});

Then(/^the selected pod collection field is (valid|invalid)$/, async (valid: 'valid' | 'invalid') => {
    const field = <PodCollectionObject>StaticStore.getStoredField(utils.fieldTypes.podCollection);
    await field.loseFocus();
    await field.expectPodCollectionValidity(valid === 'valid');
});

Then(
    /^the selected pod collection field is (enabled|disabled|read-only)$/,
    async (state: 'enabled' | 'disabled' | 'read-only') => {
        const field = <PodCollectionObject>StaticStore.getStoredField(utils.fieldTypes.podCollection);
        await field.loseFocus();
        if (state === 'read-only') {
            await field.expectPodToBeReadOnly();
        } else {
            await field.expectPodToBeEnabled(state === 'disabled');
        }
    },
);

Then(/^the title of the selected pod collection field is "(.*)"$/, async (value: string) => {
    const field = <PodCollectionObject>StaticStore.getStoredField(utils.fieldTypes.podCollection);
    await field.loseFocus();
    await field.expectPodTitle(value);
});

Then(/^the helper text of the selected pod collection field is "(.*)"$/, async (value: string) => {
    const field = <PodCollectionObject>StaticStore.getStoredField(utils.fieldTypes.podCollection);
    await field.loseFocus();
    await field.expectPodHelperText(value);
});
