import * as chalk from 'chalk';
import * as fs from 'fs';
import * as path from 'path';
import * as util from 'util';
import * as xml2js from 'xml2js';

const readFileAsync = util.promisify(fs.readFile);
const writeFileAsync = util.promisify(fs.writeFile);
const readdirAsync = util.promisify(fs.readdir);
const statAsync = util.promisify(fs.stat);

const contextSeparator = '--';

function logSuccess(message: string) {
    // eslint-disable-next-line no-console
    console.log(chalk.green.bold(`✔️ ${message}`));
}

function logError(message: string) {
    // eslint-disable-next-line no-console
    console.error(chalk.red.bold(`❌ ${message}`));
}

async function readXmlContent(filePath: string): Promise<string> {
    try {
        return await readFileAsync(filePath, 'utf-8');
    } catch (error) {
        throw new Error(`Error reading XML file: ${error.message}`);
    }
}

function parseXml(xmlContent: string): Promise<any> {
    return new Promise<any>((resolve, reject) => {
        xml2js.parseString(xmlContent, (err, result) => {
            if (err) {
                reject(new Error(`Error parsing XML: ${err}`));
            } else {
                resolve(result);
            }
        });
    });
}

function updateTestCaseNames(result: any): void {
    result.testsuites.testsuite.forEach((testsuite: any) => {
        const featureName = testsuite.properties[0].property.find((prop: any) => prop.$.name === 'featureName').$.value;
        testsuite.testcase.forEach((testcase: any) => {
            testcase.$.name = `${featureName} ${contextSeparator} ${testcase.$.name}`;
        });
    });
}

function convertJsonToXml(result: any): string {
    return new xml2js.Builder().buildObject(result);
}

async function writeUpdatedXmlToFile(filePath: string, updatedXml: string): Promise<void> {
    try {
        await writeFileAsync(filePath, updatedXml, 'utf-8');
        logSuccess(`JUnit XML updated successfully. Updated file: ${filePath}`);
    } catch (error) {
        throw new Error(`Error writing updated XML to file: ${error.message}`);
    }
}

export async function updateAllJUnitFilesInFolder(folderPath: string): Promise<void> {
    try {
        await processFolder(folderPath);
        logSuccess('All JUnit XML files updated successfully.');
    } catch (error) {
        logError(error);
    }
}

async function processFolder(this: any, folderPath: string): Promise<void> {
    const files = (await readdirAsync(folderPath)).filter(file => file.toLowerCase().endsWith('.xml'));

    // eslint-disable-next-line no-restricted-syntax
    for (const file of files) {
        const fullPath = path.join(folderPath, file);
        const stats = await statAsync(fullPath);
        if (!stats.isDirectory()) {
            await updateXmlFile(fullPath); // Update XML file
        }
    }
}

async function updateXmlFile(filePath: string): Promise<void> {
    // Step 1: Read XML content from the file
    const xmlContent = await readXmlContent(filePath);

    // Step 2: Parse XML
    const result = await parseXml(xmlContent);
    try {
        // Step 3: Update testcase names
        updateTestCaseNames(result);
    } catch (error) {
        logError(`Error updating XML file: ${error.message}`);
    }
    let updatedXml = '';
    try {
        // Step 4: Convert JSON back to XML
        updatedXml = convertJsonToXml(result);
    } catch (error) {
        logError(`Error converting XML file: ${error.message}`);
    }
    // Step 5: Write updated XML to the same file
    await writeUpdatedXmlToFile(filePath, updatedXml);
}
