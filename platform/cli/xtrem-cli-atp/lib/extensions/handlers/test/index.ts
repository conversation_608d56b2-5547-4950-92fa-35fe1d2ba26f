import { TestOptions } from '@sage/xtrem-cli-dev';
import { cliContext, quitWithError } from '@sage/xtrem-cli-lib';
import { executeIntegrationTest } from './integration-test';

export const test = async (options: TestOptions) => {
    const { executionMode, dir } = cliContext;
    try {
        const { integration: executingIntegrationTests, ci: isCi, serviceOptions, browser, pattern } = options;

        if (executingIntegrationTests) {
            // TODO: Add cucumber integration test support here.
            await executeIntegrationTest(executionMode, dir, isCi, browser, serviceOptions, pattern);
        }
    } catch (error) {
        quitWithError(executionMode, error);
    }
};
