import { LintOptions, TestOptions } from '@sage/xtrem-cli-dev';
import type { Argv } from 'yargs';
import { test } from './extensions/handlers/test';

export const plugin = (yargs: Argv): Argv => yargs;

export const builder = {
    test: (yargs: Argv) =>
        yargs
            .option('integration', {
                desc: 'Executes the BDD Cucumber integration tests of the current package',
                default: false,
                type: 'boolean',
            })
            .option('browser', {
                desc: 'Makes the browser visible when running the integration tests (not consider when using --ci flag',
                default: false,
                type: 'boolean',
            }),
};

export const handler = {
    test: (argv: any) => test(argv as TestOptions),
    lint: (argv: any) => {
        const options = argv as LintOptions;
        // Lazy loading of handlers to speed up the CLI loading
        // eslint-disable-next-line global-require
        return require('./extensions/handlers/lint').lint(options);
    },
};
