import allureReporter from '@wdio/allure-reporter';

export class ParameterHelper {
    validateEnvVarExists = (paramName: string) => {
        if (!this.checkEnvVarExists(paramName)) {
            throw new Error(`"${paramName}" is not declared as an environmental variable.`);
        }
    };

    // eslint-disable-next-line class-methods-use-this
    checkEnvVarExists = (paramName: string): boolean => {
        if (!(paramName in process.env && process.env[paramName])) {
            return false;
        }
        return true;
    };

    getStringOrParameter = (paramName: string): string => {
        if (paramName && paramName.startsWith('param:')) {
            const parsedParamName = paramName.split(':')[1];
            return this.getStringParameter(parsedParamName);
        }
        return !paramName ? '' : paramName;
    };

    // eslint-disable-next-line class-methods-use-this
    getParameter = (paramName: string): string => {
        let param = '';
        if (process.env[paramName] != null) {
            param = process.env[paramName] as string;
        }
        const paramNewName = paramName.toLowerCase();
        if (paramNewName.search('password') < 0) {
            allureReporter.addAttachment(`Static parameter value: ${paramName}`, `${param}`, 'text/plain');
        } else {
            allureReporter.addAttachment(`Static parameter value: ${paramName}`, `**************`, 'text/plain');
        }

        return param;
    };

    // eslint-disable-next-line class-methods-use-this
    setParameter = (paramName: string, value: any) => {
        if (paramName in process.env && process.env[paramName]) {
            process.env[paramName] = value;
        }
    };

    getStringParameter = (paramName: string): string => {
        const param = this.getParameter(paramName);
        if (param) {
            return param;
        }
        throw new Error(`Requested environment String variable does not exist: ${paramName}`);
    };

    getNumericParameter = (paramName: string): number => {
        const param = this.getParameter(paramName);
        if (param) {
            return parseInt(param, 10);
        }
        throw new Error(`Requested environment Numeric variable does not exist: ${paramName}`);
    };

    getBooleanParameter = (paramName: string): boolean => {
        const param = this.getParameter(paramName);
        if (param) {
            return param.toLowerCase() === 'true';
        }
        throw new Error(`Requested environment Boolean variable does not exist: ${paramName}`);
    };
}

export const runtimeParameters = new ParameterHelper();
