{"extends": "../../tsconfig-package.json", "include": ["index.ts", "generate-scenario-uuid.ts", "lib/**/*", "test/**/*", "wdio.conf.js", "resources/step-definitions"], "compilerOptions": {"baseUrl": ".", "outDir": "build", "rootDir": ".", "skipLibCheck": true, "types": ["node", "@wdio/globals/types", "@wdio/devtools-service", "@wdio/cucumber-framework"], "removeComments": false}, "references": [{"path": "../xtrem-cli-dev"}, {"path": "../xtrem-cli-lib"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../back-end/xtrem-dts-bundle"}, {"path": "../../back-end/xtrem-service"}, {"path": "../../front-end/xtrem-ui"}, {"path": "../../back-end/eslint-plugin-xtrem"}, {"path": "../../shared/xtrem-async-helper"}]}