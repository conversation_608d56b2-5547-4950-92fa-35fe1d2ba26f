import * as fs from 'fs';
import * as path from 'path';
import { v4 } from 'uuid';

function generateUUID() {
    const id: string = v4();
    return `UUID${id}`.replaceAll('-', '');
}

function addUUIDToScenario(filePath: string): { modifiedLines: number; matchedLines: number } {
    let modifiedScenarioName = 0;
    let modifiedUUID = 0;
    let totalModifiedLines = 0;
    let matchedLines = 0;
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    let modifiedContent = '';
    const lines = fileContent.split('\n');
    const lastLine = lines[lines.length - 1];
    const endsWithEmptyLine = lastLine.trim() === '';

    lines.forEach((line: string, index: number) => {
        const scenarioMarkers = ['Scenario:', 'Scenario Outline:'];
        let modifiedLine = line;

        scenarioMarkers.forEach((marker: string) => {
            if (line.includes(marker)) {
                matchedLines += 1;
                const spaceCount = line.search(/\S|$/);
                const scenarioParts = line.split(marker);
                if (scenarioParts.length === 2 && !scenarioParts[1].includes('UUID')) {
                    const scenarioName = scenarioParts[1].trim();
                    const uuid = generateUUID();
                    modifiedScenarioName += 1;
                    const hashIndex = line.indexOf('#');
                    if (hashIndex !== -1 && hashIndex === spaceCount) {
                        const spacesAfterHash = line.substring(hashIndex + 1).search(/\S|$/);
                        modifiedLine = `${' '.repeat(spaceCount)}#${' '.repeat(
                            spacesAfterHash,
                        )}${marker} ${uuid} - ${scenarioName}`;
                    } else {
                        modifiedLine = `${' '.repeat(spaceCount)}${marker} ${uuid} - ${scenarioName}`;
                    }
                    totalModifiedLines += 1;
                } else if (scenarioParts.length === 2 && scenarioParts[1].includes('UUID')) {
                    modifiedUUID += 1;
                }
            }
        });

        if (index !== lines.length - 1 || !endsWithEmptyLine) {
            modifiedContent += `${modifiedLine}\n`;
        } else {
            modifiedContent += modifiedLine;
        }
    });

    if (modifiedScenarioName > 0 || modifiedUUID > 0) {
        fs.writeFileSync(filePath, modifiedContent, 'utf-8');
    }

    return { modifiedLines: totalModifiedLines, matchedLines };
}

function generateScenarioUUID(dir: string) {
    const startTimer = process.hrtime();
    const featureFiles: string[] = [];

    const getFiles = (directoryPath: string) => {
        const files = fs
            .readdirSync(directoryPath)
            .filter(
                (file: string) =>
                    !file.startsWith('node_modules') && !file.startsWith('tmp') && !file.startsWith('functional-tests'),
            );

        files.forEach((file: string) => {
            const filePath = path.join(directoryPath, file);
            const stat = fs.statSync(filePath);
            if (stat.isDirectory()) {
                getFiles(filePath);
            } else if (filePath.endsWith('.feature')) {
                const containsNodeModules = filePath.includes('node_modules');
                const containsFunctionalTests = filePath.includes('functional-tests');
                if (!containsNodeModules && !containsFunctionalTests) {
                    featureFiles.push(filePath);
                } else {
                    console.log(`Skipping feature file: ${filePath} (in node_modules or functional-tests or tmp path)`);
                }
            }
        });
    };

    getFiles(dir);

    let totalModifiedFiles = 0;
    let totalModifiedLines = 0;
    let totalMatchedLines = 0;

    featureFiles.forEach((file: string) => {
        const { modifiedLines, matchedLines } = addUUIDToScenario(file);
        if (modifiedLines > 0) {
            totalModifiedFiles += 1;
            totalModifiedLines += modifiedLines;
            totalMatchedLines += matchedLines;
        }
    });

    if (totalModifiedFiles === 0) {
        console.log('All scenarios already have UUIDs');
    } else {
        console.log(`${totalModifiedFiles} modified files`);
        console.log(`${totalMatchedLines} scenarios found`);
        console.log(`${totalModifiedLines} UUIDs added`);
    }
    const endTimer = process.hrtime(startTimer);
    console.log(`Execution time: ${endTimer[0]}s ${endTimer[1] / 1000000}ms`);
}

generateScenarioUUID(process.cwd());
