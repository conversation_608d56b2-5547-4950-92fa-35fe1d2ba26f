import { getFolderCucumberSentences } from '@sage/cucumber-steps-parser';

import * as fs from 'fs';
import * as lodash from 'lodash';
import * as path from 'path';

const splitStepsRegex = /\(([a-zA-Z0-9\s\|]+)\)/g;

const steps = getFolderCucumberSentences(
    path.join(process.cwd(), 'lib', 'extensions', 'handlers', 'test', 'step-definitions'),
    true,
);

const lines = [
    'PATH: XTREEM/2+Integration+tests/3+Available+Cucumber+steps',
    '',
    '# Available Cucumber steps',
    '',
    'Currently the following Cucumber steps are available:',
    '',
    ...steps.map(s => `- ${s.text}`),
    '',
];

const targetDocFile = path.join(
    process.cwd(),
    '../../../documentation/platform/12-test-framework/2-integration-tests',
    '3-available-cucumber-steps.md',
);
fs.writeFileSync(targetDocFile, lines.join('\n'), 'utf-8');

let stepsFileContent = `
const { Then } = require('@cucumber/cucumber');
`;

const printStep = (stepText: string, isDeprecated = false, deprecationComment?: string) => {
    if (isDeprecated) {
        stepsFileContent = `${stepsFileContent}
/**
 * @deprecated${deprecationComment ? ' ' + deprecationComment : ''}
 */`;
    }

    stepsFileContent = `${stepsFileContent}
Then(/^${stepText}$/, function () {
    // This is just a stub function.
});
`;
};

const combine = (arrays: string[][]) => {
    const r = [],
        max = arrays.length - 1;
    function helper(arr, i) {
        for (let j = 0, l = arrays[i].length; j < l; j++) {
            const a = arr.slice(0);
            a.push(arrays[i][j]);
            if (i === max) r.push(a);
            else helper(a, i + 1);
        }
    }
    helper([], 0);
    return r;
};
steps.forEach(step => {
    const isDeprecated = step.tags.indexOf('deprecated') !== -1;
    printStep(step.text, isDeprecated, step.tagDetails.deprecated);

    const matchResult = step.text.match(splitStepsRegex);
    if (matchResult) {
        const formattedResult = matchResult.map(s => lodash.trimEnd(lodash.trimStart(s, '('), ')').split('|'));
        combine(formattedResult).forEach(combinedItems => {
            let formattedString = step.text;
            matchResult.forEach((match: string, index: number) => {
                formattedString = formattedString.replace(match, combinedItems[index]);
            });
            printStep(formattedString, isDeprecated);
        });
    } else {
        printStep(step.text, isDeprecated);
    }
});

const targetStepFile = path.join(process.cwd(), 'build', 'cucumber-steps.js');
fs.writeFileSync(targetStepFile, stepsFileContent, 'utf-8');
