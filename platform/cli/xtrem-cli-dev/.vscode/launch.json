{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "Run show case integration tests",
            "skipFiles": ["<node_internals>/**"],
            "program": "${workspaceFolder}/../xtrem-cli/bin/xtrem",
            "cwd": "${workspaceFolder}/../../show-case/xtrem-show-case",
            "args": ["test", "action-buttons", "--browser", "--integration"],
            "outFiles": ["${workspaceFolder}/build/**/*.js"],
            "env": {
                "TARGET_URL": "http://localhost:3000"
            }
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Run manufacturing integration tests",
            "skipFiles": ["<node_internals>/**"],
            "program": "${workspaceFolder}/../xtrem-cli/bin/xtrem",
            "cwd": "${workspaceFolder}/../../../services/applications/xtrem-manufacturing",
            "args": ["test", "smoke-test-pr-cd-work-order.feature", "--browser", "--integration"],
            "outFiles": ["${workspaceFolder}/build/**/*.js"],
        }
    ]
}
