{"extends": "../../tsconfig-package.json", "include": ["index.ts", "lib/**/*", "test/**/*"], "compilerOptions": {"baseUrl": ".", "outDir": "build", "rootDir": ".", "skipLibCheck": true, "types": ["node"], "removeComments": false}, "references": [{"path": "../xtrem-cli-compile"}, {"path": "../xtrem-cli-lib"}, {"path": "../xtrem-cli-main"}, {"path": "../xtrem-cli-transformers"}, {"path": "../../back-end/xtrem-core"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../back-end/xtrem-dts-bundle"}, {"path": "../../shared/xtrem-i18n"}, {"path": "../../back-end/xtrem-service"}, {"path": "../../shared/xtrem-shared"}, {"path": "../../back-end/xtrem-minify"}]}