// Must be adapted to postgres pool X3-197245

import { ExecutionMode } from '@sage/xtrem-cli-lib';
import { ConfigManager, getTestFixtures } from '@sage/xtrem-core';
import { assert, expect } from 'chai';
import 'chai-as-promised';
import * as path from 'path';
import { executeGraphqlTests } from '../lib/commands/handlers/test';
import { mochaErrors } from '../lib/commands/utils';

describe.skip('GraphQL framework tests', () => {
    const dirPath = path.join(__dirname, 'fixtures/x3-test');

    before(() => {
        getTestFixtures().updateContext();
        process.env.APPLICATION_DIR = dirPath;
        ConfigManager.load(path.join(__dirname, '..'), 'test');
    });

    it('passing tests', async () => {
        await executeGraphqlTests(ExecutionMode.INTEGRATED, dirPath, false, 'pass');
        assert.isEmpty(mochaErrors, 'Positive flow test should not return errors');
    });

    it('execution mode tests', async () => {
        await executeGraphqlTests(ExecutionMode.INTEGRATED, dirPath, false, 'execution-mode');
        assert.isEmpty(mochaErrors, 'Positive flow test should not return errors');
    });
    it('layers', async () => {
        await executeGraphqlTests(ExecutionMode.INTEGRATED, dirPath, false, 'layers');
        assert.isEmpty(mochaErrors, 'layer configuration should not throw any error');
    });
    it('failing tests', async () => {
        await assert.isRejected(executeGraphqlTests(ExecutionMode.INTEGRATED, dirPath, false, 'fail'));

        assert.isNotEmpty(mochaErrors, 'MochaErrors should contain error reports from failing tests.');
        expect(mochaErrors['fail-missing-request'][0]).to.match(
            /Request file is missing, you need to add either 'request\.graphql' or 'request\.graphql\.hbs' to.*fail-missing-request.*See https:\/\/confluence\.sage\.com\/display\/ETNA\/GraphQL\+test\+framework for a detailed documentation\./,
        );
        expect(mochaErrors['fail-empty-request-file'][0]).to.match(
            /'request\.graphql' under.*fail-empty-request-file.*is empty\. See https:\/\/confluence\.sage\.com\/display\/ETNA\/GraphQL\+test\+framework for a detailed documentation\./,
        );
        expect(mochaErrors['fail-empty-response-file'][0]).to.match(
            /'response.json' under.*fail-empty-response-file.*is empty\. See https:\/\/confluence\.sage\.com\/display\/ETNA\/GraphQL\+test\+framework for a detailed documentation\./,
        );
        expect(mochaErrors['fail-missing-response'][0]).to.match(
            /Response file is missing, you need to add either 'response\.json' or 'response\.json\.hbs' to.*fail-missing-response.*See https:\/\/confluence\.sage\.com\/display\/ETNA\/GraphQL\+test\+framework for a detailed documentation\./,
        );
        expect(mochaErrors['fail-missing-parameters-file'][0]).to.match(
            /Parameters file is missing: you need to add a file called 'parameters\.json' under.*fail-missing-parameters-file.*See https:\/\/confluence\.sage\.com\/display\/ETNA\/GraphQL\+test\+framework for a detailed documentation\./,
        );
        expect(mochaErrors['fail-missing-parameters'][0]).to.match(
            /You need to add the following variables as an object to 'filter by countryName' under the 'input' key: \["TestNodes\.countryName"\]\. See https:\/\/confluence\.sage\.com\/display\/ETNA\/GraphQL\+test\+framework for a detailed documentation\./,
        );
        expect(mochaErrors['fail-missing-parameters-output'][0]).to.match(
            /You need to add the following variables as an object to 'filter by countryName' under the 'output' key: \["code","countryName","description"\]\. See https:\/\/confluence\.sage.com\/display\/ETNA\/GraphQL\+test\+framework for a detailed documentation\./,
        );
        expect(mochaErrors['fail-invalid-mock'][0]).to.equal(
            "No plugin found for 'invalid' mock.: expected undefined to exist",
        );
        expect(mochaErrors['fail-without-hbs-with-env-parameters-variables'][0]).to.match(
            /'variables' property is only for handlebars tests \(with request\.graphql\.hbs and response\.json\.hbs\)\. See https:\/\/confluence\.sage.com\/display\/ETNA\/GraphQL\+test\+framework for a detailed documentation\./,
        );
        expect(mochaErrors['fail-without-hbs-with-env-parameters-and-output'][0]).to.match(
            /'output' property is only for handlebars tests \(with request\.graphql\.hbs and response\.json\.hbs\)\. See https:\/\/confluence\.sage.com\/display\/ETNA\/GraphQL\+test\+framework for a detailed documentation\./,
        );
        expect(mochaErrors['fail-without-hbs-with-env-parameters-and-input'][0]).to.match(
            /'input' property is only for handlebars tests \(with request\.graphql\.hbs and response\.json\.hbs\)\. See https:\/\/confluence\.sage.com\/display\/ETNA\/GraphQL\+test\+framework for a detailed documentation\./,
        );
    });

    // it('can query extension layer table', function() {
    //     Test.withContext(context => {
    //         const schema = context.getTableSchemaName('test_node_db');
    //         assert.isTrue(context.sysSqlPool.tableExists('test_node_db', schema));
    //         const tableName = PostgresPool.getFullTableName(schema, 'test_node_db');
    //         context.sqlPool.withConnection(cnx => {
    //             const data = context.sqlPool.execute(cnx, `SELECT code, company, is_active from ${tableName}`);
    //             assert.deepEqual(data, [
    //                 { code: 'C1', company: 'SAGE', is_active: 'Y' },
    //                 { code: 'C2', company: 'NOTSAGE', is_active: 'N' },
    //                 { code: 'C3', company: ' ', is_active: 'N' },
    //             ]);
    //         });
    //     });
    // });
});
