# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [24.0.8](https://github.com/compare/...@sage/x3-test@24.0.8) (2022-10-18)

### Bug Fixes


### Features


# [24.0.6](https://github.com/compare/...@sage/x3-test@24.0.6) (2022-10-16)

### Bug Fixes


### Features


# [24.0.5](https://github.com/compare/...@sage/x3-test@24.0.5) (2022-10-15)

### Bug Fixes


### Features


# [24.0.4](https://github.com/compare/...@sage/x3-test@24.0.4) (2022-10-15)

### Bug Fixes


### Features


# [24.0.3](https://github.com/compare/...@sage/x3-test@24.0.3) (2022-10-11)

### Bug Fixes


### Features


# [24.0.2](https://github.com/compare/...@sage/x3-test@24.0.2) (2022-10-11)

### Bug Fixes


### Features


# [24.0.1](https://github.com/compare/...@sage/x3-test@24.0.1) (2022-10-11)

### Bug Fixes


### Features


# [24.0.0](https://github.com/compare/...@sage/x3-test@24.0.0) (2022-09-29)

### Bug Fixes


### Features


# [23.0.48](https://github.com/compare/...@sage/x3-test@23.0.48) (2022-09-29)

### Bug Fixes


### Features


# [23.0.47](https://github.com/compare/...@sage/x3-test@23.0.47) (2022-09-29)

### Bug Fixes


### Features


# [23.0.46](https://github.com/compare/...@sage/x3-test@23.0.46) (2022-09-28)

### Bug Fixes


### Features


# [23.0.45](https://github.com/compare/...@sage/x3-test@23.0.45) (2022-09-28)

### Bug Fixes


### Features

* **cli:** move plugins to devDependencies (X3-283449) ([#9001](https://github.com/issues/9001))  ([61f93dc](https://github.com/commit/61f93dcbd42d74c8e13a098aae55e7a43de39e5e))

# [23.0.44](https://github.com/compare/...@sage/x3-test@23.0.44) (2022-09-26)

### Bug Fixes


### Features


# [23.0.43](https://github.com/compare/...@sage/x3-test@23.0.43) (2022-09-25)

### Bug Fixes


### Features


# [23.0.42](https://github.com/compare/...@sage/x3-test@23.0.42) (2022-09-24)

### Bug Fixes


### Features


# [23.0.41](https://github.com/compare/...@sage/x3-test@23.0.41) (2022-09-23)

### Bug Fixes


### Features


# [23.0.40](https://github.com/compare/...@sage/x3-test@23.0.40) (2022-09-22)

### Bug Fixes


### Features


# [23.0.39](https://github.com/compare/...@sage/x3-test@23.0.39) (2022-09-21)

### Bug Fixes


### Features


# [23.0.38](https://github.com/compare/...@sage/x3-test@23.0.38) (2022-09-21)

### Bug Fixes


### Features


# [23.0.37](https://github.com/compare/...@sage/x3-test@23.0.37) (2022-09-21)

### Bug Fixes


### Features


# [23.0.36](https://github.com/compare/...@sage/x3-test@23.0.36) (2022-09-19)

### Bug Fixes


### Features


# [23.0.35](https://github.com/compare/...@sage/x3-test@23.0.35) (2022-09-19)

### Bug Fixes


### Features


# [23.0.34](https://github.com/compare/...@sage/x3-test@23.0.34) (2022-09-19)

### Bug Fixes


### Features


# [23.0.33](https://github.com/compare/...@sage/x3-test@23.0.33) (2022-09-19)

### Bug Fixes


### Features


# [23.0.32](https://github.com/compare/...@sage/x3-test@23.0.32) (2022-09-18)

### Bug Fixes


### Features


# [23.0.31](https://github.com/compare/...@sage/x3-test@23.0.31) (2022-09-18)

### Bug Fixes


### Features


# [23.0.30](https://github.com/compare/...@sage/x3-test@23.0.30) (2022-09-18)

### Bug Fixes


### Features


# [23.0.29](https://github.com/compare/...@sage/x3-test@23.0.29) (2022-09-18)

### Bug Fixes


### Features


# [23.0.28](https://github.com/compare/...@sage/x3-test@23.0.28) (2022-09-17)

### Bug Fixes


### Features


# [23.0.27](https://github.com/compare/...@sage/x3-test@23.0.27) (2022-09-17)

### Bug Fixes


### Features


# [23.0.26](https://github.com/compare/...@sage/x3-test@23.0.26) (2022-09-16)

### Bug Fixes


### Features


# [23.0.25](https://github.com/compare/...@sage/x3-test@23.0.25) (2022-09-16)

### Bug Fixes


### Features


# [23.0.24](https://github.com/compare/...@sage/x3-test@23.0.24) (2022-09-16)

### Bug Fixes


### Features


# [23.0.23](https://github.com/compare/...@sage/x3-test@23.0.23) (2022-09-16)

### Bug Fixes


### Features


# [23.0.22](https://github.com/compare/...@sage/x3-test@23.0.22) (2022-09-14)

### Bug Fixes


### Features


# [23.0.21](https://github.com/compare/...@sage/x3-test@23.0.21) (2022-09-14)

### Bug Fixes


### Features


# [23.0.20](https://github.com/compare/...@sage/x3-test@23.0.20) (2022-09-12)

### Bug Fixes


### Features


# [23.0.19](https://github.com/compare/...@sage/x3-test@23.0.19) (2022-09-12)

### Bug Fixes


### Features


# [23.0.18](https://github.com/compare/...@sage/x3-test@23.0.18) (2022-09-11)

### Bug Fixes


### Features


# [23.0.17](https://github.com/compare/...@sage/x3-test@23.0.17) (2022-09-10)

### Bug Fixes


### Features


# [23.0.16](https://github.com/compare/...@sage/x3-test@23.0.16) (2022-09-09)

### Bug Fixes


### Features


# [23.0.15](https://github.com/compare/...@sage/x3-test@23.0.15) (2022-09-09)

### Bug Fixes


### Features

* **cli:** refactor cli - create xtrem-cli-lib (X3-283449) ([#8736](https://github.com/issues/8736))  ([c0b1e7a](https://github.com/commit/c0b1e7a6f6f0cc6078062f2425c156746e0f5223))

# [23.0.14](https://github.com/compare/...@sage/x3-test@23.0.14) (2022-09-07)

### Bug Fixes


### Features


# [23.0.13](https://github.com/compare/...@sage/x3-test@23.0.13) (2022-09-07)

### Bug Fixes


### Features


# [23.0.12](https://github.com/compare/...@sage/x3-test@23.0.12) (2022-09-06)

### Bug Fixes


### Features


# [23.0.11](https://github.com/compare/...@sage/x3-test@23.0.11) (2022-09-05)

### Bug Fixes


### Features


# [23.0.10](https://github.com/compare/...@sage/x3-test@23.0.10) (2022-09-05)

### Bug Fixes


### Features


# [23.0.9](https://github.com/compare/...@sage/x3-test@23.0.9) (2022-09-04)

### Bug Fixes


### Features


# [23.0.8](https://github.com/compare/...@sage/x3-test@23.0.8) (2022-09-03)

### Bug Fixes


### Features


# [23.0.7](https://github.com/compare/...@sage/x3-test@23.0.7) (2022-09-02)

### Bug Fixes


### Features


# [23.0.6](https://github.com/compare/...@sage/x3-test@23.0.6) (2022-09-01)

### Bug Fixes


### Features


# [23.0.5](https://github.com/compare/...@sage/x3-test@23.0.5) (2022-08-31)

### Bug Fixes


### Features


# [23.0.4](https://github.com/compare/...@sage/x3-test@23.0.4) (2022-08-30)

### Bug Fixes


### Features


# [23.0.3](https://github.com/compare/...@sage/x3-test@23.0.3) (2022-08-30)

### Bug Fixes


### Features


# [23.0.2](https://github.com/compare/...@sage/x3-test@23.0.2) (2022-08-29)

### Bug Fixes


### Features


# [23.0.1](https://github.com/compare/...@sage/x3-test@23.0.1) (2022-08-29)

### Bug Fixes


### Features


# [23.0.0](https://github.com/compare/...@sage/x3-test@23.0.0) (2022-08-26)

### Bug Fixes


### Features


# [22.0.25](https://github.com/compare/...@sage/x3-test@22.0.25) (2022-08-25)

### Bug Fixes


### Features


# [22.0.24](https://github.com/compare/...@sage/x3-test@22.0.24) (2022-08-24)

### Bug Fixes


### Features


# [22.0.23](https://github.com/compare/...@sage/x3-test@22.0.23) (2022-08-23)

### Bug Fixes


### Features


# [22.0.22](https://github.com/compare/...@sage/x3-test@22.0.22) (2022-08-22)

### Bug Fixes


### Features


# [22.0.21](https://github.com/compare/...@sage/x3-test@22.0.21) (2022-08-21)

### Bug Fixes


### Features


# [22.0.20](https://github.com/compare/...@sage/x3-test@22.0.20) (2022-08-19)

### Bug Fixes


### Features


# [22.0.19](https://github.com/compare/...@sage/x3-test@22.0.19) (2022-08-18)

### Bug Fixes


### Features


# [22.0.18](https://github.com/compare/...@sage/x3-test@22.0.18) (2022-08-17)

### Bug Fixes


### Features


# [22.0.17](https://github.com/compare/...@sage/x3-test@22.0.17) (2022-08-16)

### Bug Fixes


### Features


# [22.0.16](https://github.com/compare/...@sage/x3-test@22.0.16) (2022-08-15)

### Bug Fixes


### Features


# [22.0.15](https://github.com/compare/...@sage/x3-test@22.0.15) (2022-08-14)

### Bug Fixes


### Features


# [22.0.14](https://github.com/compare/...@sage/x3-test@22.0.14) (2022-08-13)

### Bug Fixes


### Features


# [22.0.13](https://github.com/compare/...@sage/x3-test@22.0.13) (2022-08-11)

### Bug Fixes


### Features


# [22.0.12](https://github.com/compare/...@sage/x3-test@22.0.12) (2022-08-11)

### Bug Fixes


### Features


# [22.0.11](https://github.com/compare/...@sage/x3-test@22.0.11) (2022-08-09)

### Bug Fixes


### Features


# [22.0.10](https://github.com/compare/...@sage/x3-test@22.0.10) (2022-08-08)

### Bug Fixes


### Features


# [22.0.9](https://github.com/compare/...@sage/x3-test@22.0.9) (2022-08-07)

### Bug Fixes


### Features


# [22.0.8](https://github.com/compare/...@sage/x3-test@22.0.8) (2022-08-06)

### Bug Fixes


### Features


# [22.0.7](https://github.com/compare/...@sage/x3-test@22.0.7) (2022-08-05)

### Bug Fixes


### Features


# [22.0.6](https://github.com/compare/...@sage/x3-test@22.0.6) (2022-08-04)

### Bug Fixes


### Features


# [22.0.5](https://github.com/compare/...@sage/x3-test@22.0.5) (2022-08-01)

### Bug Fixes


### Features


# [22.0.4](https://github.com/compare/...@sage/x3-test@22.0.4) (2022-07-31)

### Bug Fixes


### Features


# [22.0.3](https://github.com/compare/...@sage/x3-test@22.0.3) (2022-07-30)

### Bug Fixes


### Features


# [22.0.2](https://github.com/compare/...@sage/x3-test@22.0.2) (2022-07-29)

### Bug Fixes


### Features


# [22.0.1](https://github.com/compare/...@sage/x3-test@22.0.1) (2022-07-28)

### Bug Fixes


### Features


# [22.0.0](https://github.com/compare/...@sage/x3-test@22.0.0) (2022-07-28)

### Bug Fixes


### Features


# [21.0.36](https://github.com/compare/...@sage/x3-test@21.0.36) (2022-07-28)

### Bug Fixes


### Features


# [21.0.35](https://github.com/compare/...@sage/x3-test@21.0.35) (2022-07-27)

### Bug Fixes


### Features


# [21.0.34](https://github.com/compare/...@sage/x3-test@21.0.34) (2022-07-26)

### Bug Fixes


### Features


# [21.0.33](https://github.com/compare/...@sage/x3-test@21.0.33) (2022-07-25)

### Bug Fixes


### Features


# [21.0.32](https://github.com/compare/...@sage/x3-test@21.0.32) (2022-07-24)

### Bug Fixes


### Features


# [21.0.31](https://github.com/compare/...@sage/x3-test@21.0.31) (2022-07-23)

### Bug Fixes


### Features


# [21.0.30](https://github.com/compare/...@sage/x3-test@21.0.30) (2022-07-22)

### Bug Fixes


### Features


# [21.0.29](https://github.com/compare/...@sage/x3-test@21.0.29) (2022-07-21)

### Bug Fixes


### Features


# [21.0.28](https://github.com/compare/...@sage/x3-test@21.0.28) (2022-07-20)

### Bug Fixes


### Features


# [21.0.27](https://github.com/compare/...@sage/x3-test@21.0.27) (2022-07-19)

### Bug Fixes


### Features


# [21.0.26](https://github.com/compare/...@sage/x3-test@21.0.26) (2022-07-19)

### Bug Fixes


### Features


# [21.0.25](https://github.com/compare/...@sage/x3-test@21.0.25) (2022-07-18)

### Bug Fixes


### Features


# [21.0.24](https://github.com/compare/...@sage/x3-test@21.0.24) (2022-07-17)

### Bug Fixes


### Features


# [21.0.23](https://github.com/compare/...@sage/x3-test@21.0.23) (2022-07-16)

### Bug Fixes


### Features


# [21.0.22](https://github.com/compare/...@sage/x3-test@21.0.22) (2022-07-15)

### Bug Fixes


### Features


# [21.0.21](https://github.com/compare/...@sage/x3-test@21.0.21) (2022-07-14)

### Bug Fixes


### Features


# [21.0.20](https://github.com/compare/...@sage/x3-test@21.0.20) (2022-07-14)

### Bug Fixes


### Features


# [21.0.19](https://github.com/compare/...@sage/x3-test@21.0.19) (2022-07-13)

### Bug Fixes


### Features


# [21.0.18](https://github.com/compare/...@sage/x3-test@21.0.18) (2022-07-13)

### Bug Fixes


### Features


# [21.0.17](https://github.com/compare/...@sage/x3-test@21.0.17) (2022-07-10)

### Bug Fixes


### Features


# [21.0.16](https://github.com/compare/...@sage/x3-test@21.0.16) (2022-07-09)

### Bug Fixes


### Features


# [21.0.15](https://github.com/compare/...@sage/x3-test@21.0.15) (2022-07-08)

### Bug Fixes


### Features


# [21.0.14](https://github.com/compare/...@sage/x3-test@21.0.14) (2022-07-07)

### Bug Fixes


### Features


# [21.0.13](https://github.com/compare/...@sage/x3-test@21.0.13) (2022-07-06)

### Bug Fixes


### Features


# [21.0.12](https://github.com/compare/...@sage/x3-test@21.0.12) (2022-07-05)

### Bug Fixes


### Features


# [21.0.11](https://github.com/compare/...@sage/x3-test@21.0.11) (2022-07-04)

### Bug Fixes


### Features


# [21.0.10](https://github.com/compare/...@sage/x3-test@21.0.10) (2022-07-04)

### Bug Fixes


### Features


# [21.0.9](https://github.com/compare/...@sage/x3-test@21.0.9) (2022-07-02)

### Bug Fixes


### Features


# [21.0.8](https://github.com/compare/...@sage/x3-test@21.0.8) (2022-07-01)

### Bug Fixes


### Features


# [21.0.7](https://github.com/compare/...@sage/x3-test@21.0.7) (2022-06-30)

### Bug Fixes


### Features


# [21.0.6](https://github.com/compare/...@sage/x3-test@21.0.6) (2022-06-29)

### Bug Fixes


### Features


# [21.0.5](https://github.com/compare/...@sage/x3-test@21.0.5) (2022-06-28)

### Bug Fixes


### Features


# [21.0.4](https://github.com/compare/...@sage/x3-test@21.0.4) (2022-06-27)

### Bug Fixes


### Features


# [21.0.3](https://github.com/compare/...@sage/x3-test@21.0.3) (2022-06-26)

### Bug Fixes


### Features


# [21.0.2](https://github.com/compare/...@sage/x3-test@21.0.2) (2022-06-25)

### Bug Fixes


### Features


# [21.0.1](https://github.com/compare/...@sage/x3-test@21.0.1) (2022-06-24)

### Bug Fixes


### Features


# [21.0.0](https://github.com/compare/...@sage/x3-test@21.0.0) (2022-06-23)

### Bug Fixes


### Features


# [20.0.32](https://github.com/compare/...@sage/x3-test@20.0.32) (2022-06-23)

### Bug Fixes


### Features


# [20.0.31](https://github.com/compare/...@sage/x3-test@20.0.31) (2022-06-22)

### Bug Fixes


### Features


# [20.0.30](https://github.com/compare/...@sage/x3-test@20.0.30) (2022-06-21)

### Bug Fixes


### Features


# [20.0.29](https://github.com/compare/...@sage/x3-test@20.0.29) (2022-06-21)

### Bug Fixes


### Features


# [20.0.28](https://github.com/compare/...@sage/x3-test@20.0.28) (2022-06-20)

### Bug Fixes


### Features


# [20.0.27](https://github.com/compare/...@sage/x3-test@20.0.27) (2022-06-19)

### Bug Fixes


### Features


# [20.0.26](https://github.com/compare/...@sage/x3-test@20.0.26) (2022-06-18)

### Bug Fixes


### Features


# [20.0.25](https://github.com/compare/...@sage/x3-test@20.0.25) (2022-06-17)

### Bug Fixes


### Features


# [20.0.24](https://github.com/compare/...@sage/x3-test@20.0.24) (2022-06-16)

### Bug Fixes


### Features


# [20.0.23](https://github.com/compare/...@sage/x3-test@20.0.23) (2022-06-16)

### Bug Fixes


### Features


# [20.0.22](https://github.com/compare/...@sage/x3-test@20.0.22) (2022-06-14)

### Bug Fixes


### Features


# [20.0.21](https://github.com/compare/...@sage/x3-test@20.0.21) (2022-06-14)

### Bug Fixes


### Features


# [20.0.20](https://github.com/compare/...@sage/x3-test@20.0.20) (2022-06-13)

### Bug Fixes


### Features


# [20.0.19](https://github.com/compare/...@sage/x3-test@20.0.19) (2022-06-12)

### Bug Fixes


### Features


# [20.0.18](https://github.com/compare/...@sage/x3-test@20.0.18) (2022-06-11)

### Bug Fixes


### Features


# [20.0.17](https://github.com/compare/...@sage/x3-test@20.0.17) (2022-06-10)

### Bug Fixes


### Features


# [20.0.16](https://github.com/compare/...@sage/x3-test@20.0.16) (2022-06-10)

### Bug Fixes


### Features


# [20.0.15](https://github.com/compare/...@sage/x3-test@20.0.15) (2022-06-08)

### Bug Fixes


### Features


# [20.0.14](https://github.com/compare/...@sage/x3-test@20.0.14) (2022-06-07)

### Bug Fixes


### Features


# [20.0.13](https://github.com/compare/...@sage/x3-test@20.0.13) (2022-06-06)

### Bug Fixes


### Features


# [20.0.12](https://github.com/compare/...@sage/x3-test@20.0.12) (2022-06-05)

### Bug Fixes


### Features


# [20.0.11](https://github.com/compare/...@sage/x3-test@20.0.11) (2022-06-04)

### Bug Fixes


### Features


# [20.0.10](https://github.com/compare/...@sage/x3-test@20.0.10) (2022-06-03)

### Bug Fixes


### Features


# [20.0.9](https://github.com/compare/...@sage/x3-test@20.0.9) (2022-06-03)

### Bug Fixes


### Features


# [20.0.8](https://github.com/compare/...@sage/x3-test@20.0.8) (2022-06-02)

### Bug Fixes

* package version number fix ([#7344](https://github.com/issues/7344))  ([517e460](https://github.com/commit/517e460cde4848f3559f14bfd79a77835cd78223))

### Features


# [20.0.5](https://github.com/compare/...@sage/x3-test@20.0.5) (2022-06-02)

### Bug Fixes


### Features


# [20.0.4](https://github.com/compare/...@sage/x3-test@20.0.4) (2022-06-01)

### Bug Fixes


### Features


# [20.0.3](https://github.com/compare/...@sage/x3-test@20.0.3) (2022-05-29)

### Bug Fixes


### Features


# [20.0.2](https://github.com/compare/...@sage/x3-test@20.0.2) (2022-05-28)

### Bug Fixes


### Features


# [20.0.1](https://github.com/compare/...@sage/x3-test@20.0.1) (2022-05-27)

### Bug Fixes


### Features


# [20.0.0](https://github.com/compare/...@sage/x3-test@20.0.0) (2022-05-26)

### Bug Fixes


### Features


# [19.0.33](https://github.com/compare/...@sage/x3-test@19.0.33) (2022-05-26)

### Bug Fixes


### Features


# [19.0.32](https://github.com/compare/...@sage/x3-test@19.0.32) (2022-05-26)

### Bug Fixes


### Features


# [19.0.31](https://github.com/compare/...@sage/x3-test@19.0.31) (2022-05-24)

### Bug Fixes


### Features


# [19.0.30](https://github.com/compare/...@sage/x3-test@19.0.30) (2022-05-24)

### Bug Fixes


### Features


# [19.0.29](https://github.com/compare/...@sage/x3-test@19.0.29) (2022-05-23)

### Bug Fixes


### Features


# [19.0.28](https://github.com/compare/...@sage/x3-test@19.0.28) (2022-05-23)

### Bug Fixes


### Features


# [19.0.27](https://github.com/compare/...@sage/x3-test@19.0.27) (2022-05-22)

### Bug Fixes


### Features


# [19.0.26](https://github.com/compare/...@sage/x3-test@19.0.26) (2022-05-21)

### Bug Fixes


### Features


# [19.0.25](https://github.com/compare/...@sage/x3-test@19.0.25) (2022-05-20)

### Bug Fixes


### Features


# [19.0.24](https://github.com/compare/...@sage/x3-test@19.0.24) (2022-05-20)

### Bug Fixes


### Features


# [19.0.23](https://github.com/compare/...@sage/x3-test@19.0.23) (2022-05-18)

### Bug Fixes


### Features


# [19.0.22](https://github.com/compare/...@sage/x3-test@19.0.22) (2022-05-17)

### Bug Fixes


### Features


# [19.0.21](https://github.com/compare/...@sage/x3-test@19.0.21) (2022-05-16)

### Bug Fixes


### Features


# [19.0.20](https://github.com/compare/...@sage/x3-test@19.0.20) (2022-05-15)

### Bug Fixes


### Features


# [19.0.19](https://github.com/compare/...@sage/x3-test@19.0.19) (2022-05-14)

### Bug Fixes


### Features


# [19.0.18](https://github.com/compare/...@sage/x3-test@19.0.18) (2022-05-13)

### Bug Fixes


### Features


# [19.0.17](https://github.com/compare/...@sage/x3-test@19.0.17) (2022-05-13)

### Bug Fixes


### Features


# [19.0.16](https://github.com/compare/...@sage/x3-test@19.0.16) (2022-05-12)

### Bug Fixes


### Features


# [19.0.15](https://github.com/compare/...@sage/x3-test@19.0.15) (2022-05-11)

### Bug Fixes


### Features


# [19.0.14](https://github.com/compare/...@sage/x3-test@19.0.14) (2022-05-10)

### Bug Fixes


### Features


# [19.0.13](https://github.com/compare/...@sage/x3-test@19.0.13) (2022-05-10)

### Bug Fixes


### Features


# [19.0.12](https://github.com/compare/...@sage/x3-test@19.0.12) (2022-05-09)

### Bug Fixes


### Features


# [19.0.11](https://github.com/compare/...@sage/x3-test@19.0.11) (2022-05-08)

### Bug Fixes


### Features


# [19.0.10](https://github.com/compare/...@sage/x3-test@19.0.10) (2022-05-07)

### Bug Fixes


### Features


# [19.0.9](https://github.com/compare/...@sage/x3-test@19.0.9) (2022-05-06)

### Bug Fixes


### Features


# [19.0.8](https://github.com/compare/...@sage/x3-test@19.0.8) (2022-05-06)

### Bug Fixes


### Features


# [19.0.7](https://github.com/compare/...@sage/x3-test@19.0.7) (2022-05-04)

### Bug Fixes


### Features


# [19.0.6](https://github.com/compare/...@sage/x3-test@19.0.6) (2022-05-03)

### Bug Fixes


### Features


# [19.0.5](https://github.com/compare/...@sage/x3-test@19.0.5) (2022-05-02)

### Bug Fixes


### Features


# [19.0.4](https://github.com/compare/...@sage/x3-test@19.0.4) (2022-05-01)

### Bug Fixes


### Features


# [19.0.3](https://github.com/compare/...@sage/x3-test@19.0.3) (2022-04-30)

### Bug Fixes


### Features


# [19.0.2](https://github.com/compare/...@sage/x3-test@19.0.2) (2022-04-29)

### Bug Fixes

* remove npm npx rimraf (XT-24746) ([#6742](https://github.com/issues/6742))  ([6aa09dd](https://github.com/commit/6aa09dd305b2724eb472161396c820ca4c3f3d0a))

### Features


# [19.0.1](https://github.com/compare/...@sage/x3-test@19.0.1) (2022-04-28)

### Bug Fixes


### Features


# [19.0.0](https://github.com/compare/...@sage/x3-test@19.0.0) (2022-04-28)

### Bug Fixes


### Features


# [18.0.37](https://github.com/compare/...@sage/x3-test@18.0.37) (2022-04-28)

### Bug Fixes


### Features


# [18.0.36](https://github.com/compare/...@sage/x3-test@18.0.36) (2022-04-27)

### Bug Fixes


### Features


# [18.0.35](https://github.com/compare/...@sage/x3-test@18.0.35) (2022-04-27)

### Bug Fixes


### Features


# [18.0.34](https://github.com/compare/...@sage/x3-test@18.0.34) (2022-04-26)

### Bug Fixes


### Features


# [18.0.33](https://github.com/compare/...@sage/x3-test@18.0.33) (2022-04-26)

### Bug Fixes


### Features


# [18.0.32](https://github.com/compare/...@sage/x3-test@18.0.32) (2022-04-25)

### Bug Fixes


### Features


# [18.0.31](https://github.com/compare/...@sage/x3-test@18.0.31) (2022-04-25)

### Bug Fixes


### Features

* XT-20534 refactor tenantIds ([#6337](https://github.com/issues/6337))  ([6060c3b](https://github.com/commit/6060c3b607d8183cbba6effc5767b8222fa23f7a))

# [18.0.30](https://github.com/compare/...@sage/x3-test@18.0.30) (2022-04-21)

### Bug Fixes


### Features


# [18.0.29](https://github.com/compare/...@sage/x3-test@18.0.29) (2022-04-21)

### Bug Fixes


### Features


# [18.0.28](https://github.com/compare/...@sage/x3-test@18.0.28) (2022-04-20)

### Bug Fixes


### Features


# [18.0.27](https://github.com/compare/...@sage/x3-test@18.0.27) (2022-04-20)

### Bug Fixes


### Features


# [18.0.26](https://github.com/compare/...@sage/x3-test@18.0.26) (2022-04-18)

### Bug Fixes


### Features


# [18.0.25](https://github.com/compare/...@sage/x3-test@18.0.25) (2022-04-18)

### Bug Fixes


### Features


# [18.0.24](https://github.com/compare/...@sage/x3-test@18.0.24) (2022-04-16)

### Bug Fixes


### Features


# [18.0.23](https://github.com/compare/...@sage/x3-test@18.0.23) (2022-04-15)

### Bug Fixes


### Features


# [18.0.22](https://github.com/compare/...@sage/x3-test@18.0.22) (2022-04-14)

### Bug Fixes


### Features


# [18.0.21](https://github.com/compare/...@sage/x3-test@18.0.21) (2022-04-13)

### Bug Fixes


### Features


# [18.0.20](https://github.com/compare/...@sage/x3-test@18.0.20) (2022-04-12)

### Bug Fixes


### Features


# [18.0.19](https://github.com/compare/...@sage/x3-test@18.0.19) (2022-04-11)

### Bug Fixes


### Features


# [18.0.18](https://github.com/compare/...@sage/x3-test@18.0.18) (2022-04-10)

### Bug Fixes


### Features


# [18.0.17](https://github.com/compare/...@sage/x3-test@18.0.17) (2022-04-09)

### Bug Fixes


### Features


# [18.0.16](https://github.com/compare/...@sage/x3-test@18.0.16) (2022-04-08)

### Bug Fixes


### Features


# [18.0.15](https://github.com/compare/...@sage/x3-test@18.0.15) (2022-04-07)

### Bug Fixes


### Features


# [18.0.14](https://github.com/compare/...@sage/x3-test@18.0.14) (2022-04-06)

### Bug Fixes


### Features


# [18.0.13](https://github.com/compare/...@sage/x3-test@18.0.13) (2022-04-05)

### Bug Fixes


### Features


# [18.0.12](https://github.com/compare/...@sage/x3-test@18.0.12) (2022-04-04)

### Bug Fixes


### Features


# [18.0.11](https://github.com/compare/...@sage/x3-test@18.0.11) (2022-04-03)

### Bug Fixes


### Features


# [18.0.10](https://github.com/compare/...@sage/x3-test@18.0.10) (2022-04-02)

### Bug Fixes


### Features


# [18.0.9](https://github.com/compare/...@sage/x3-test@18.0.9) (2022-04-01)

### Bug Fixes


### Features


# [18.0.8](https://github.com/compare/...@sage/x3-test@18.0.8) (2022-03-31)

### Bug Fixes


### Features


# [18.0.7](https://github.com/compare/...@sage/x3-test@18.0.7) (2022-03-31)

### Bug Fixes


### Features


# [18.0.6](https://github.com/compare/...@sage/x3-test@18.0.6) (2022-03-30)

### Bug Fixes


### Features


# [18.0.5](https://github.com/compare/...@sage/x3-test@18.0.5) (2022-03-29)

### Bug Fixes


### Features


# [18.0.4](https://github.com/compare/...@sage/x3-test@18.0.4) (2022-03-28)

### Bug Fixes


### Features


# [18.0.3](https://github.com/compare/...@sage/x3-test@18.0.3) (2022-03-27)

### Bug Fixes


### Features


# [18.0.2](https://github.com/compare/...@sage/x3-test@18.0.2) (2022-03-26)

### Bug Fixes


### Features


# [18.0.1](https://github.com/compare/...@sage/x3-test@18.0.1) (2022-03-25)

### Bug Fixes


### Features


# [18.0.0](https://github.com/compare/...@sage/x3-test@18.0.0) (2022-03-24)

### Bug Fixes


### Features


# [17.0.29](https://github.com/compare/...@sage/x3-test@17.0.29) (2022-03-24)

### Bug Fixes


### Features


# [17.0.28](https://github.com/compare/...@sage/x3-test@17.0.28) (2022-03-24)

### Bug Fixes


### Features


# [17.0.27](https://github.com/compare/...@sage/x3-test@17.0.27) (2022-03-23)

### Bug Fixes


### Features


# [17.0.26](https://github.com/compare/...@sage/x3-test@17.0.26) (2022-03-22)

### Bug Fixes


### Features


# [17.0.25](https://github.com/compare/...@sage/x3-test@17.0.25) (2022-03-21)

### Bug Fixes


### Features


# [17.0.24](https://github.com/compare/...@sage/x3-test@17.0.24) (2022-03-20)

### Bug Fixes


### Features


# [17.0.23](https://github.com/compare/...@sage/x3-test@17.0.23) (2022-03-20)

### Bug Fixes


### Features


# [17.0.22](https://github.com/compare/...@sage/x3-test@17.0.22) (2022-03-19)

### Bug Fixes


### Features


# [17.0.21](https://github.com/compare/...@sage/x3-test@17.0.21) (2022-03-19)

### Bug Fixes


### Features


# [17.0.20](https://github.com/compare/...@sage/x3-test@17.0.20) (2022-03-18)

### Bug Fixes


### Features


# [17.0.19](https://github.com/compare/...@sage/x3-test@17.0.19) (2022-03-18)

### Bug Fixes


### Features


# [17.0.18](https://github.com/compare/...@sage/x3-test@17.0.18) (2022-03-18)

### Bug Fixes


### Features


# [17.0.17](https://github.com/compare/...@sage/x3-test@17.0.17) (2022-03-17)

### Bug Fixes


### Features


# [17.0.16](https://github.com/compare/...@sage/x3-test@17.0.16) (2022-03-17)

### Bug Fixes


### Features


# [17.0.15](https://github.com/compare/...@sage/x3-test@17.0.15) (2022-03-13)

### Bug Fixes


### Features


# [17.0.14](https://github.com/compare/...@sage/x3-test@17.0.14) (2022-03-10)

### Bug Fixes


### Features


# [17.0.13](https://github.com/compare/...@sage/x3-test@17.0.13) (2022-03-09)

### Bug Fixes


### Features


# [17.0.12](https://github.com/compare/...@sage/x3-test@17.0.12) (2022-03-09)

### Bug Fixes


### Features


# [17.0.11](https://github.com/compare/...@sage/x3-test@17.0.11) (2022-03-08)

### Bug Fixes


### Features


# [17.0.10](https://github.com/compare/...@sage/x3-test@17.0.10) (2022-03-07)

### Bug Fixes


### Features


# [17.0.9](https://github.com/compare/...@sage/x3-test@17.0.9) (2022-03-06)

### Bug Fixes


### Features


# [17.0.8](https://github.com/compare/...@sage/x3-test@17.0.8) (2022-03-05)

### Bug Fixes


### Features


# [17.0.7](https://github.com/compare/...@sage/x3-test@17.0.7) (2022-03-04)

### Bug Fixes


### Features


# [17.0.6](https://github.com/compare/...@sage/x3-test@17.0.6) (2022-03-03)

### Bug Fixes


### Features


# [17.0.5](https://github.com/compare/...@sage/x3-test@17.0.5) (2022-03-03)

### Bug Fixes


### Features


# [17.0.4](https://github.com/compare/...@sage/x3-test@17.0.4) (2022-03-01)

### Bug Fixes


### Features


# [17.0.3](https://github.com/compare/...@sage/x3-test@17.0.3) (2022-02-28)

### Bug Fixes


### Features


# [17.0.2](https://github.com/compare/...@sage/x3-test@17.0.2) (2022-02-27)

### Bug Fixes


### Features


# [17.0.1](https://github.com/compare/...@sage/x3-test@17.0.1) (2022-02-26)

### Bug Fixes


### Features


# [17.0.0](https://github.com/compare/...@sage/x3-test@17.0.0) (2022-02-25)

### Bug Fixes


### Features


# [16.0.29](https://github.com/compare/...@sage/x3-test@16.0.29) (2022-02-24)

### Bug Fixes


### Features


# [16.0.28](https://github.com/compare/...@sage/x3-test@16.0.28) (2022-02-24)

### Bug Fixes


### Features


# [16.0.27](https://github.com/compare/...@sage/x3-test@16.0.27) (2022-02-24)

### Bug Fixes


### Features


# [16.0.26](https://github.com/compare/...@sage/x3-test@16.0.26) (2022-02-23)

### Bug Fixes


### Features


# [16.0.25](https://github.com/compare/...@sage/x3-test@16.0.25) (2022-02-22)

### Bug Fixes


### Features


# [16.0.24](https://github.com/compare/...@sage/x3-test@16.0.24) (2022-02-21)

### Bug Fixes


### Features


# [16.0.23](https://github.com/compare/...@sage/x3-test@16.0.23) (2022-02-20)

### Bug Fixes


### Features


# [16.0.22](https://github.com/compare/...@sage/x3-test@16.0.22) (2022-02-19)

### Bug Fixes


### Features


# [16.0.21](https://github.com/compare/...@sage/x3-test@16.0.21) (2022-02-18)

### Bug Fixes


### Features


# [16.0.20](https://github.com/compare/...@sage/x3-test@16.0.20) (2022-02-17)

### Bug Fixes


### Features


# [16.0.19](https://github.com/compare/...@sage/x3-test@16.0.19) (2022-02-16)

### Bug Fixes


### Features


# [16.0.18](https://github.com/compare/...@sage/x3-test@16.0.18) (2022-02-15)

### Bug Fixes

* XT-18862 on tenant import verify package version on released version ([#5271](https://github.com/issues/5271))  ([42cb3b2](https://github.com/commit/42cb3b2dfd3c0a19a800d1cef3667d7543022513))

### Features


# [16.0.17](https://github.com/compare/...@sage/x3-test@16.0.17) (2022-02-13)

### Bug Fixes


### Features


# [16.0.16](https://github.com/compare/...@sage/x3-test@16.0.16) (2022-02-12)

### Bug Fixes


### Features


# [16.0.15](https://github.com/compare/...@sage/x3-test@16.0.15) (2022-02-11)

### Bug Fixes


### Features


# [16.0.14](https://github.com/compare/...@sage/x3-test@16.0.14) (2022-02-10)

### Bug Fixes


### Features


# [16.0.13](https://github.com/compare/...@sage/x3-test@16.0.13) (2022-02-10)

### Bug Fixes


### Features


# [16.0.12](https://github.com/compare/...@sage/x3-test@16.0.12) (2022-02-08)

### Bug Fixes


### Features


# [16.0.11](https://github.com/compare/...@sage/x3-test@16.0.11) (2022-02-07)

### Bug Fixes


### Features


# [16.0.10](https://github.com/compare/...@sage/x3-test@16.0.10) (2022-02-07)

### Bug Fixes


### Features


# [16.0.9](https://github.com/compare/...@sage/x3-test@16.0.9) (2022-02-06)

### Bug Fixes


### Features


# [16.0.8](https://github.com/compare/...@sage/x3-test@16.0.8) (2022-02-05)

### Bug Fixes


### Features


# [16.0.7](https://github.com/compare/...@sage/x3-test@16.0.7) (2022-02-04)

### Bug Fixes


### Features


# [16.0.6](https://github.com/compare/...@sage/x3-test@16.0.6) (2022-02-04)

### Bug Fixes


### Features


# [16.0.5](https://github.com/compare/...@sage/x3-test@16.0.5) (2022-02-02)

### Bug Fixes


### Features


# [16.0.4](https://github.com/compare/...@sage/x3-test@16.0.4) (2022-02-01)

### Bug Fixes


### Features


# [16.0.3](https://github.com/compare/...@sage/x3-test@16.0.3) (2022-01-31)

### Bug Fixes


### Features


# [16.0.2](https://github.com/compare/...@sage/x3-test@16.0.2) (2022-01-30)

### Bug Fixes


### Features


# [16.0.1](https://github.com/compare/...@sage/x3-test@16.0.1) (2022-01-29)

### Bug Fixes


### Features


# [16.0.0](https://github.com/compare/...@sage/x3-test@16.0.0) (2022-01-29)

### Bug Fixes


### Features


# [15.0.36](https://github.com/compare/...@sage/x3-test@15.0.36) (2022-01-28)

### Bug Fixes


### Features


# [15.0.35](https://github.com/compare/...@sage/x3-test@15.0.35) (2022-01-28)

### Bug Fixes


### Features


# [15.0.34](https://github.com/compare/...@sage/x3-test@15.0.34) (2022-01-28)

### Bug Fixes


### Features


# [15.0.33](https://github.com/compare/...@sage/x3-test@15.0.33) (2022-01-26)

### Bug Fixes


### Features


# [15.0.32](https://github.com/compare/...@sage/x3-test@15.0.32) (2022-01-26)

### Bug Fixes


### Features


# [15.0.31](https://github.com/compare/...@sage/x3-test@15.0.31) (2022-01-25)

### Bug Fixes


### Features


# [15.0.30](https://github.com/compare/...@sage/x3-test@15.0.30) (2022-01-25)

### Bug Fixes


### Features


# [15.0.29](https://github.com/compare/...@sage/x3-test@15.0.29) (2022-01-24)

### Bug Fixes


### Features


# [15.0.28](https://github.com/compare/...@sage/x3-test@15.0.28) (2022-01-24)

### Bug Fixes


### Features


# [15.0.27](https://github.com/compare/...@sage/x3-test@15.0.27) (2022-01-23)

### Bug Fixes


### Features


# [15.0.26](https://github.com/compare/...@sage/x3-test@15.0.26) (2022-01-23)

### Bug Fixes


### Features


# [15.0.25](https://github.com/compare/...@sage/x3-test@15.0.25) (2022-01-21)

### Bug Fixes


### Features


# [15.0.24](https://github.com/compare/...@sage/x3-test@15.0.24) (2022-01-18)

### Bug Fixes


### Features


# [15.0.23](https://github.com/compare/...@sage/x3-test@15.0.23) (2022-01-18)

### Bug Fixes


### Features


# [15.0.22](https://github.com/compare/...@sage/x3-test@15.0.22) (2022-01-17)

### Bug Fixes


### Features


# [15.0.21](https://github.com/compare/...@sage/x3-test@15.0.21) (2022-01-16)

### Bug Fixes


### Features


# [15.0.20](https://github.com/compare/...@sage/x3-test@15.0.20) (2022-01-15)

### Bug Fixes


### Features


# [15.0.19](https://github.com/compare/...@sage/x3-test@15.0.19) (2022-01-14)

### Bug Fixes


### Features


# [15.0.18](https://github.com/compare/...@sage/x3-test@15.0.18) (2022-01-14)

### Bug Fixes


### Features


# [15.0.17](https://github.com/compare/...@sage/x3-test@15.0.17) (2022-01-13)

### Bug Fixes


### Features


# [15.0.16](https://github.com/compare/...@sage/x3-test@15.0.16) (2022-01-12)

### Bug Fixes


### Features


# [15.0.15](https://github.com/compare/...@sage/x3-test@15.0.15) (2022-01-11)

### Bug Fixes


### Features


# [15.0.14](https://github.com/compare/...@sage/x3-test@15.0.14) (2022-01-11)

### Bug Fixes


### Features


# [15.0.13](https://github.com/compare/...@sage/x3-test@15.0.13) (2022-01-11)

### Bug Fixes


### Features


# [15.0.12](https://github.com/compare/...@sage/x3-test@15.0.12) (2022-01-10)

### Bug Fixes


### Features


# [15.0.11](https://github.com/compare/...@sage/x3-test@15.0.11) (2022-01-09)

### Bug Fixes


### Features


# [15.0.10](https://github.com/compare/...@sage/x3-test@15.0.10) (2022-01-08)

### Bug Fixes


### Features


# [15.0.9](https://github.com/compare/...@sage/x3-test@15.0.9) (2022-01-07)

### Bug Fixes


### Features


# [15.0.8](https://github.com/compare/...@sage/x3-test@15.0.8) (2022-01-07)

### Bug Fixes


### Features


# [15.0.7](https://github.com/compare/...@sage/x3-test@15.0.7) (2022-01-06)

### Bug Fixes


### Features


# [15.0.6](https://github.com/compare/...@sage/x3-test@15.0.6) (2022-01-05)

### Bug Fixes


### Features


# [15.0.5](https://github.com/compare/...@sage/x3-test@15.0.5) (2022-01-04)

### Bug Fixes


### Features


# [15.0.4](https://github.com/compare/...@sage/x3-test@15.0.4) (2022-01-03)

### Bug Fixes


### Features


# [15.0.3](https://github.com/compare/...@sage/x3-test@15.0.3) (2022-01-02)

### Bug Fixes


### Features


# [15.0.2](https://github.com/compare/...@sage/x3-test@15.0.2) (2022-01-01)

### Bug Fixes


### Features


# [15.0.1](https://github.com/compare/...@sage/x3-test@15.0.1) (2021-12-31)

### Bug Fixes


### Features


# [15.0.0](https://github.com/compare/...@sage/x3-test@15.0.0) (2021-12-30)

### Bug Fixes


### Features


# [14.0.29](https://github.com/compare/...@sage/x3-test@14.0.29) (2021-12-30)

### Bug Fixes


### Features


# [14.0.28](https://github.com/compare/...@sage/x3-test@14.0.28) (2021-12-29)

### Bug Fixes


### Features


# [14.0.27](https://github.com/compare/...@sage/x3-test@14.0.27) (2021-12-28)

### Bug Fixes


### Features


# [14.0.26](https://github.com/compare/...@sage/x3-test@14.0.26) (2021-12-27)

### Bug Fixes


### Features


# [14.0.25](https://github.com/compare/...@sage/x3-test@14.0.25) (2021-12-27)

### Bug Fixes


### Features


# [14.0.24](https://github.com/compare/...@sage/x3-test@14.0.24) (2021-12-22)

### Bug Fixes


### Features


# [14.0.23](https://github.com/compare/...@sage/x3-test@14.0.23) (2021-12-21)

### Bug Fixes


### Features


# [14.0.22](https://github.com/compare/...@sage/x3-test@14.0.22) (2021-12-15)

### Bug Fixes


### Features


# [14.0.21](https://github.com/compare/...@sage/x3-test@14.0.21) (2021-12-14)

### Bug Fixes


### Features


# [14.0.20](https://github.com/compare/...@sage/x3-test@14.0.20) (2021-12-13)

### Bug Fixes


### Features


# [14.0.19](https://github.com/compare/...@sage/x3-test@14.0.19) (2021-12-12)

### Bug Fixes


### Features


# [14.0.18](https://github.com/compare/...@sage/x3-test@14.0.18) (2021-12-11)

### Bug Fixes


### Features


# [14.0.17](https://github.com/compare/...@sage/x3-test@14.0.17) (2021-12-10)

### Bug Fixes


### Features


# [14.0.16](https://github.com/compare/...@sage/x3-test@14.0.16) (2021-12-09)

### Bug Fixes


### Features


# [14.0.15](https://github.com/compare/...@sage/x3-test@14.0.15) (2021-12-09)

### Bug Fixes


### Features


# [14.0.14](https://github.com/compare/...@sage/x3-test@14.0.14) (2021-12-08)

### Bug Fixes


### Features


# [14.0.13](https://github.com/compare/...@sage/x3-test@14.0.13) (2021-12-07)

### Bug Fixes


### Features


# [14.0.12](https://github.com/compare/...@sage/x3-test@14.0.12) (2021-12-07)

### Bug Fixes


### Features


# [14.0.11](https://github.com/compare/...@sage/x3-test@14.0.11) (2021-12-06)

### Bug Fixes


### Features


# [14.0.10](https://github.com/compare/...@sage/x3-test@14.0.10) (2021-12-05)

### Bug Fixes


### Features


# [14.0.9](https://github.com/compare/...@sage/x3-test@14.0.9) (2021-12-04)

### Bug Fixes


### Features


# [14.0.8](https://github.com/compare/...@sage/x3-test@14.0.8) (2021-12-03)

### Bug Fixes


### Features


# [14.0.7](https://github.com/compare/...@sage/x3-test@14.0.7) (2021-12-03)

### Bug Fixes


### Features


# [14.0.6](https://github.com/compare/...@sage/x3-test@14.0.6) (2021-12-02)

### Bug Fixes


### Features


# [14.0.5](https://github.com/compare/...@sage/x3-test@14.0.5) (2021-12-02)

### Bug Fixes


### Features


# [14.0.4](https://github.com/compare/...@sage/x3-test@14.0.4) (2021-12-02)

### Bug Fixes


### Features


# [14.0.3](https://github.com/compare/...@sage/x3-test@14.0.3) (2021-12-02)

### Bug Fixes


### Features


# [14.0.2](https://github.com/compare/...@sage/x3-test@14.0.2) (2021-12-01)

### Bug Fixes


### Features


# [14.0.1](https://github.com/compare/...@sage/x3-test@14.0.1) (2021-11-29)

### Bug Fixes


### Features


# [14.0.0](https://github.com/compare/...@sage/x3-test@14.0.0) (2021-11-29)

### Bug Fixes


### Features


# [13.0.28](https://github.com/compare/...@sage/x3-test@13.0.28) (2021-11-29)

### Bug Fixes


### Features


# [13.0.27](https://github.com/compare/...@sage/x3-test@13.0.27) (2021-11-28)

### Bug Fixes


### Features


# [13.0.26](https://github.com/compare/...@sage/x3-test@13.0.26) (2021-11-27)

### Bug Fixes


### Features


# [13.0.25](https://github.com/compare/...@sage/x3-test@13.0.25) (2021-11-26)

### Bug Fixes


### Features


# [13.0.24](https://github.com/compare/...@sage/x3-test@13.0.24) (2021-11-25)

### Bug Fixes


### Features


# [13.0.23](https://github.com/compare/...@sage/x3-test@13.0.23) (2021-11-24)

### Bug Fixes


### Features


# [13.0.22](https://github.com/compare/...@sage/x3-test@13.0.22) (2021-11-23)

### Bug Fixes


### Features


# [13.0.21](https://github.com/compare/...@sage/x3-test@13.0.21) (2021-11-22)

### Bug Fixes


### Features


# [13.0.20](https://github.com/compare/...@sage/x3-test@13.0.20) (2021-11-22)

### Bug Fixes


### Features


# [13.0.19](https://github.com/compare/...@sage/x3-test@13.0.19) (2021-11-19)

### Bug Fixes


### Features


# [13.0.18](https://github.com/compare/...@sage/x3-test@13.0.18) (2021-11-18)

### Bug Fixes


### Features


# [13.0.17](https://github.com/compare/...@sage/x3-test@13.0.17) (2021-11-18)

### Bug Fixes


### Features


# [13.0.16](https://github.com/compare/...@sage/x3-test@13.0.16) (2021-11-17)

### Bug Fixes


### Features


# [13.0.15](https://github.com/compare/...@sage/x3-test@13.0.15) (2021-11-17)

### Bug Fixes


### Features


# [13.0.14](https://github.com/compare/...@sage/x3-test@13.0.14) (2021-11-14)

### Bug Fixes


### Features


# [13.0.13](https://github.com/compare/...@sage/x3-test@13.0.13) (2021-11-13)

### Bug Fixes


### Features


# [13.0.12](https://github.com/compare/...@sage/x3-test@13.0.12) (2021-11-12)

### Bug Fixes


### Features


# [13.0.11](https://github.com/compare/...@sage/x3-test@13.0.11) (2021-11-11)

### Bug Fixes


### Features


# [13.0.10](https://github.com/compare/...@sage/x3-test@13.0.10) (2021-11-10)

### Bug Fixes


### Features


# [13.0.9](https://github.com/compare/...@sage/x3-test@13.0.9) (2021-11-09)

### Bug Fixes


### Features


# [13.0.8](https://github.com/compare/...@sage/x3-test@13.0.8) (2021-11-09)

### Bug Fixes


### Features


# [13.0.7](https://github.com/compare/...@sage/x3-test@13.0.7) (2021-11-09)

### Bug Fixes


### Features


# [13.0.6](https://github.com/compare/...@sage/x3-test@13.0.6) (2021-11-08)

### Bug Fixes


### Features


# [13.0.5](https://github.com/compare/...@sage/x3-test@13.0.5) (2021-11-07)

### Bug Fixes


### Features


# [13.0.4](https://github.com/compare/...@sage/x3-test@13.0.4) (2021-11-06)

### Bug Fixes


### Features


# [13.0.3](https://github.com/compare/...@sage/x3-test@13.0.3) (2021-11-05)

### Bug Fixes


### Features

* XT-4131 align versions across packages ([#3530](https://github.com/issues/3530))  ([f1f900a](https://github.com/commit/f1f900ae61a90a0da1164f4466926be22b20ba0a))

# [13.0.2](https://github.com/compare/...@sage/x3-test@13.0.2) (2021-11-04)

### Bug Fixes


### Features


# [13.0.1](https://github.com/compare/...@sage/x3-test@13.0.1) (2021-11-03)

### Bug Fixes


### Features


# [13.0.0](https://github.com/compare/...@sage/x3-test@13.0.0) (2021-11-03)

### Bug Fixes


### Features


# [12.0.35](https://github.com/compare/...@sage/x3-test@12.0.35) (2021-11-02)

### Bug Fixes


### Features


# [12.0.34](https://github.com/compare/...@sage/x3-test@12.0.34) (2021-10-31)

### Bug Fixes


### Features


# [12.0.33](https://github.com/compare/...@sage/x3-test@12.0.33) (2021-10-30)

### Bug Fixes


### Features


# [12.0.32](https://github.com/compare/...@sage/x3-test@12.0.32) (2021-10-29)

### Bug Fixes


### Features


# [12.0.31](https://github.com/compare/...@sage/x3-test@12.0.31) (2021-10-28)

### Bug Fixes


### Features


# [12.0.30](https://github.com/compare/...@sage/x3-test@12.0.30) (2021-10-28)

### Bug Fixes


### Features


# [12.0.29](https://github.com/compare/...@sage/x3-test@12.0.29) (2021-10-26)

### Bug Fixes


### Features


# [12.0.28](https://github.com/compare/...@sage/x3-test@12.0.28) (2021-10-26)

### Bug Fixes


### Features


# [12.0.27](https://github.com/compare/...@sage/x3-test@12.0.27) (2021-10-25)

### Bug Fixes


### Features


# [12.0.26](https://github.com/compare/...@sage/x3-test@12.0.26) (2021-10-25)

### Bug Fixes


### Features


# [12.0.25](https://github.com/compare/...@sage/x3-test@12.0.25) (2021-10-24)

### Bug Fixes


### Features


# [12.0.24](https://github.com/compare/...@sage/x3-test@12.0.24) (2021-10-24)

### Bug Fixes


### Features


# [12.0.23](https://github.com/compare/...@sage/x3-test@12.0.23) (2021-10-23)

### Bug Fixes


### Features


# [12.0.22](https://github.com/compare/...@sage/x3-test@12.0.22) (2021-10-22)

### Bug Fixes


### Features


# [12.0.21](https://github.com/compare/...@sage/x3-test@12.0.21) (2021-10-22)

### Bug Fixes


### Features


# [12.0.20](https://github.com/compare/...@sage/x3-test@12.0.20) (2021-10-20)

### Bug Fixes


### Features


# [12.0.19](https://github.com/compare/...@sage/x3-test@12.0.19) (2021-10-19)

### Bug Fixes


### Features


# [12.0.18](https://github.com/compare/...@sage/x3-test@12.0.18) (2021-10-18)

### Bug Fixes


### Features


# [12.0.17](https://github.com/compare/...@sage/x3-test@12.0.17) (2021-10-17)

### Bug Fixes


### Features


# [12.0.16](https://github.com/compare/...@sage/x3-test@12.0.16) (2021-10-17)

### Bug Fixes


### Features


# [12.0.15](https://github.com/compare/...@sage/x3-test@12.0.15) (2021-10-16)

### Bug Fixes


### Features


# [12.0.14](https://github.com/compare/...@sage/x3-test@12.0.14) (2021-10-15)

### Bug Fixes


### Features


# [12.0.13](https://github.com/compare/...@sage/x3-test@12.0.13) (2021-10-14)

### Bug Fixes


### Features


# [12.0.12](https://github.com/compare/...@sage/x3-test@12.0.12) (2021-10-13)

### Bug Fixes


### Features


# [12.0.11](https://github.com/compare/...@sage/x3-test@12.0.11) (2021-10-12)

### Bug Fixes


### Features


# [12.0.10](https://github.com/compare/...@sage/x3-test@12.0.10) (2021-10-12)

### Bug Fixes


### Features


# [12.0.9](https://github.com/compare/...@sage/x3-test@12.0.9) (2021-10-10)

### Bug Fixes


### Features


# [12.0.8](https://github.com/compare/...@sage/x3-test@12.0.8) (2021-10-09)

### Bug Fixes


### Features


# [12.0.7](https://github.com/compare/...@sage/x3-test@12.0.7) (2021-10-08)

### Bug Fixes


### Features


# [12.0.6](https://github.com/compare/...@sage/x3-test@12.0.6) (2021-10-08)

### Bug Fixes


### Features


# [12.0.5](https://github.com/compare/...@sage/x3-test@12.0.5) (2021-10-07)

### Bug Fixes


### Features


# [12.0.4](https://github.com/compare/...@sage/x3-test@12.0.4) (2021-10-06)

### Bug Fixes


### Features


# [12.0.3](https://github.com/compare/...@sage/x3-test@12.0.3) (2021-10-05)

### Bug Fixes

* version bump  ([be66252](https://github.com/commit/be66252a4428e87349f6af50ea31ac6909a8160f))
* XT-999999 fix package versions ([#1780](https://github.com/issues/1780))  ([4bd5d4a](https://github.com/commit/4bd5d4a02fc8959893403c22b7d702f664082be8))

### Features

* XT-4920 updating of enum types in nodes and api to union string ([#1149](https://github.com/issues/1149))  ([097909a](https://github.com/commit/097909a82182189967d3eb15b0cde0bf2392324d))
* XT-873 rename system tables ([#680](https://github.com/issues/680))  ([7accb6d](https://github.com/commit/7accb6d4f9f7a4afc78562f944f2d21470b5a307))
* XT-9309 and XT-4846 implement package released status ([#2427](https://github.com/issues/2427))  ([be00d7d](https://github.com/commit/be00d7da3fa80729ef521ae3b5b66f220a9d0e79))
* **svc-model:** XT-793 package activation ([#186](https://github.com/issues/186))  ([4e6e6b8](https://github.com/commit/4e6e6b81197f8981618f15a7619aebaf7aaf40fe))

# [11.0.26](https://github.com/Sage-ERP-X3/em-core/compare/...@sage/x3-test@11.0.26) (2021-09-22)

### Bug Fixes

* version bump  ([be66252](https://github.com/Sage-ERP-X3/em-core/commit/be66252a4428e87349f6af50ea31ac6909a8160f))
* XT-999999 fix package versions ([#1780](https://github.com/Sage-ERP-X3/em-core/issues/1780))  ([4bd5d4a](https://github.com/Sage-ERP-X3/em-core/commit/4bd5d4a02fc8959893403c22b7d702f664082be8))

### Features

* XT-4920 updating of enum types in nodes and api to union string ([#1149](https://github.com/Sage-ERP-X3/em-core/issues/1149))  ([097909a](https://github.com/Sage-ERP-X3/em-core/commit/097909a82182189967d3eb15b0cde0bf2392324d))
* XT-873 rename system tables ([#680](https://github.com/Sage-ERP-X3/em-core/issues/680))  ([7accb6d](https://github.com/Sage-ERP-X3/em-core/commit/7accb6d4f9f7a4afc78562f944f2d21470b5a307))
* XT-9309 and XT-4846 implement package released status ([#2427](https://github.com/Sage-ERP-X3/em-core/issues/2427))  ([be00d7d](https://github.com/Sage-ERP-X3/em-core/commit/be00d7da3fa80729ef521ae3b5b66f220a9d0e79))
* **svc-model:** XT-793 package activation ([#186](https://github.com/Sage-ERP-X3/em-core/issues/186))  ([4e6e6b8](https://github.com/Sage-ERP-X3/em-core/commit/4e6e6b81197f8981618f15a7619aebaf7aaf40fe))

