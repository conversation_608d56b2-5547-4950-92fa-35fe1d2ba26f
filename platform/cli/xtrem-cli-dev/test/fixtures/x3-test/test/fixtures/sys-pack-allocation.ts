/** @ignore */ /** */

import { decorators, EnumDataType, Node, Reference } from '@sage/xtrem-core';
import { SysPackVersion } from './sys-pack-version';

export enum PackAllocationStatusEnum {
    off = 1,
    preparing,
    on,
}

export type PackAllocationStatus = keyof typeof PackAllocationStatusEnum;

export const packAllocationStatusEnumDataType = new EnumDataType<PackAllocationStatus>({
    enum: PackAllocationStatusEnum,
    filename: __filename,
});

/** @internal */
@decorators.node<SysPackAllocation>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canDeleteMany: true,
    isPlatformNode: true,
    indexes: [
        {
            orderBy: { package: +1 },
            isUnique: true,
        },
    ],
})
export class SysPackAllocation extends Node {
    @decorators.referenceProperty<SysPackAllocation, 'package'>({
        isPublished: true,
        isStored: true,
        node: () => SysPackVersion,
    })
    readonly package: Reference<SysPackVersion>;

    @decorators.booleanProperty<SysPackAllocation, 'isActive'>({
        isPublished: true,
        isStored: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.enumProperty<SysPackAllocation, 'status'>({
        isPublished: true,
        isStored: true,
        dataType: () => packAllocationStatusEnumDataType,
    })
    readonly status: Promise<PackAllocationStatus>;

    @decorators.booleanProperty<SysPackAllocation, 'isActivable'>({
        isPublished: true,
        isStored: true,
    })
    readonly isActivable: Promise<boolean>;
}
