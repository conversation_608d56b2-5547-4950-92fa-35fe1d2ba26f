import { BusinessRuleError, Context, decorators, Logger, Node, StringDataType } from '@sage/xtrem-core';

const logger = Logger.getLogger(__filename, 'Bundles');

/** @internal */
@decorators.node<SysPackVersion>({
    isPublished: true,
    storage: 'sql',
    canCreate: true,
    canRead: true,
    canUpdate: true,
    isSharedByAllTenants: true,
    isSetupNode: true,
    indexes: [{ orderBy: { name: 1 }, isUnique: true, isNaturalKey: true }],
    isPlatformNode: true,
})
export class SysPackVersion extends Node {
    @decorators.stringProperty<SysPackVersion, 'name'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 100 }),
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<SysPackVersion, 'version'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 20 }),
    })
    readonly version: Promise<string>;

    @decorators.booleanProperty<SysPackVersion, 'isUpgradeBundle'>({
        isPublished: true,
        isStored: true,
    })
    readonly isUpgradeBundle: Promise<boolean>;

    @decorators.booleanProperty<SysPackVersion, 'isHidden'>({
        isPublished: true,
        isStored: true,
    })
    readonly isHidden: Promise<boolean>;

    @decorators.booleanProperty<SysPackVersion, 'isReleased'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly isReleased: Promise<boolean>;

    @decorators.stringProperty<SysPackVersion, 'sqlSchemaVersion'>({
        isPublished: true,
        isStored: true,
        dataType: () => new StringDataType({ maxLength: 20 }),
    })
    readonly sqlSchemaVersion: Promise<string>;

    /**
     * Activate the bundle for Xtrem services, if the bundle is already active it will deactivate it
     * @param context
     * @param bundleId
     */
    @decorators.mutation<typeof SysPackVersion, 'activate'>({
        isPublished: true,
        parameters: [{ name: 'package', type: 'string' }],
        return: 'boolean',
    })
    static activate(context: Context, bundleId: string): boolean {
        if (!context.tenantId) {
            logger.warn(`Cannot activate bundle ${bundleId}: tenantId is not set`);
            return false;
        }
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-system/error-packages-cannot-be-deactivated',
                'Packages cannot be deactivated',
            ),
        );
    }
}
