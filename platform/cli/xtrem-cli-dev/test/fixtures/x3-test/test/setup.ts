import { getTestFixtures } from '@sage/xtrem-core';
import { TestNode } from '../lib/nodes/_index';
import { testNodeData } from './fixtures/data';

export const setup = async () => {
    const fixtures = getTestFixtures();
    fixtures.updateContext();
    delete require.cache[require.resolve('./fixtures/data/test-node-data')];
    await fixtures.initTables([{ nodeConstructor: TestNode, data: testNodeData }]);
};
