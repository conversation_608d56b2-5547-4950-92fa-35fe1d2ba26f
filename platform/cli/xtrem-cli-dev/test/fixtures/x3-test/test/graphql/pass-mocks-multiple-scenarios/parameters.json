{"soap-test-1": {"input": {"code": "C1"}, "output": {"company": "A-SAGE", "wsResponse": "hello world", "now": "2019-10-21T17:23:07.000Z"}, "layers": ["a"], "envConfigs": {"mocks": ["axios"], "now": "2019-10-21T17:23:07.000Z"}}, "soap-test-2": {"input": {"code": "C1"}, "output": {"company": "B-SAGE", "wsResponse": "salut monde", "now": "2019-11-21T17:23:07.000Z"}, "layers": ["a", "b"], "envConfigs": {"mocks": ["axios"], "now": "2019-11-21T17:23:07.000Z"}}}