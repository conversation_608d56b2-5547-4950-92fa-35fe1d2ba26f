{"extends": "../../../../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": ".", "paths": {"@sage/*": ["../*/index.ts", "../*/lib", "../*/src"]}, "skipLibCheck": true}, "include": ["index.ts", "lib/**/*", "test/**/*"], "exclude": [], "references": [{"path": "../../.."}, {"path": "../../../../../back-end/xtrem-core"}, {"path": "../../../../../shared/xtrem-shared"}, {"path": "../../../../../cli/xtrem-cli-lib"}, {"path": "api"}]}