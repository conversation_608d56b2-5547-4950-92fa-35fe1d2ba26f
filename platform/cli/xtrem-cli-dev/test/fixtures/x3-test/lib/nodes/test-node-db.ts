import { date, datetime, decorators, Node } from '@sage/xtrem-core';
import { codeDataType, descriptionDataType } from '../data-types/data-types';
import { callWebservice } from '../functions/axios-call';

@decorators.node<TestNodeDB>({
    isPublished: true,
    storage: 'sql',
    package: 'x3Test',
    canRead: true,
    canSearch: true,
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestNodeDB extends Node {
    @decorators.stringProperty<TestNodeDB, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    /** 4GL code: CRYNAM */
    @decorators.stringProperty<TestNodeDB, 'isActive'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly isActive: Promise<string>;

    @decorators.stringProperty<TestNodeDB, 'company'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly company: Promise<string>;

    @decorators.stringProperty<TestNodeDB, 'wsResponse'>({
        isPublished: true,
        dataType: () => descriptionDataType,
        getValue() {
            return callWebservice();
        },
    })
    readonly wsResponse: Promise<string>;

    @decorators.dateProperty<TestNodeDB, 'today'>({
        isPublished: true,
        getValue() {
            return date.today();
        },
    })
    readonly today: Promise<date>;

    @decorators.datetimeProperty<TestNodeDB, 'now'>({
        isPublished: true,
        getValue() {
            return datetime.now();
        },
    })
    readonly now: Promise<datetime>;
}
