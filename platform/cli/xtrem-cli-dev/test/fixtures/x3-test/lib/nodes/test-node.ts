import { Context, date, datetime, decorators, Node } from '@sage/xtrem-core';
import { codeDataType, descriptionDataType } from '../data-types/data-types';

@decorators.node<TestNode>({
    isPublished: true,
    storage: 'sql',
    tableName: 'TestCliTestNode',
    indexes: [{ orderBy: { code: 1 }, isUnique: true }],
})
export class TestNode extends Node {
    @decorators.stringProperty<TestNode, 'code'>({
        isPublished: true,
        isStored: true,
        dataType: () => codeDataType,
    })
    readonly code: Promise<string>;

    /** 4GL code: CRYNAM */
    @decorators.stringProperty<TestNode, 'countryName'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly countryName: Promise<string>;

    @decorators.stringProperty<TestNode, 'description'>({
        isPublished: true,
        isStored: true,
        dataType: () => descriptionDataType,
    })
    readonly description: Promise<string>;

    @decorators.dateProperty<TestNode, 'today'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly today: Promise<date | null>;

    @decorators.datetimeProperty<TestNode, 'now'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
    })
    readonly now: Promise<datetime | null>;

    @decorators.query<typeof TestNode, 'languageCode'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'string',
        },
    })
    static languageCode(context: Context) {
        return context.currentLocaleLanguage;
    }

    @decorators.query<typeof TestNode, 'legislationCode'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'string',
        },
    })
    static legislationCode(context: Context) {
        return context.currentLegislationCode;
    }

    @decorators.query<typeof TestNode, 'user'>({
        isPublished: true,
        parameters: [],
        return: {
            type: 'string',
        },
    })
    static async user(context: Context): Promise<string | undefined> {
        return (await context.user)?.email;
    }
}
