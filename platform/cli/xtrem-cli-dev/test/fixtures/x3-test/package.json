{"name": "@sage/x3-test", "description": "X3 Test", "version": "37.0.14", "xtrem": {"isHidden": true}, "keywords": ["xtrem-application-package"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "files": ["build", "data"], "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-lib": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-shared": "workspace:*", "lodash": "^4.17.21"}, "devDependencies": {"@types/chai": "^4.3.6", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "axios": "^1.8.4", "c8": "^10.1.2", "chai": "^4.3.10", "mocha": "^10.2.0", "typescript": "~5.4.5"}, "scripts": {"build": "xtrem compile", "build:cache": "turbo run build", "build:references": "tsc -b -v .", "clean": "rm -rf build", "lint": "xtrem lint", "start": "xtrem start", "test": "exit 0 || xtrem test --integration", "test:ci": "echo 'no tests'", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}