import { ConfigManager, getTestFixtures, Test } from '@sage/xtrem-core';
import { Config } from '@sage/xtrem-shared';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as x3Test from './fixtures/x3-test/lib/index';

const dirname = __dirname.replace('/build/', '/');

describe.skip('Mocha layers tests', () => {
    let configLoadMock: sinon.SinonStubbedInstance<any>;
    let configCurrentMock: sinon.SinonStubbedInstance<any>;
    let xtremConfig: Config = {};

    before(async () => {
        getTestFixtures().updateContext();
        configLoadMock = (() => {
            const originalConfigLoad = ConfigManager.load.bind(ConfigManager);
            return sinon.stub(ConfigManager, 'load').callsFake((dir: string, source = 'start'): Config => {
                const config = originalConfigLoad(dir, source);
                const actualConfig = {
                    ...config,
                };
                if (!actualConfig.storage?.sql) throw new Error('missing storage.sql config key');
                (actualConfig.storage.sql as any).schemaName = 'xtrem_cli_test';
                xtremConfig = actualConfig;
                return xtremConfig;
            });
        })();

        configCurrentMock = sinon.stub(ConfigManager, 'current').get((): Config => {
            return xtremConfig;
        });

        const dir = `${dirname}/fixtures/x3-test/`;
        ConfigManager.load(dir, 'test');
        // "fixtures/x3-test/lib" for the buildDir of Application constructor because
        // for a real package we have @sage level and here we dont have that level
        const schemaName = Test.getTestSchemaName('x3-test');
        const buildDir = `${dir}/lib`;
        await Test.createTestApplication({ api: x3Test, buildDir, schemaName });
    });

    after(function () {
        configLoadMock.restore();
        configCurrentMock.restore();
    });

    it('layers a', async function () {
        await Test.withContext(
            async context => {
                const testNodeDb = await context.read(x3Test.nodes.TestNodeDB, {
                    code: 'C1',
                });
                assert.instanceOf(testNodeDb, x3Test.nodes.TestNodeDB);
                assert.equal(await testNodeDb.code, 'C1');
                assert.equal(await testNodeDb.company, 'A-SAGE');
            },
            { config: { layers: ['a'] }, skipMocks: true },
        );
    });

    it('layers b', async function () {
        await Test.withContext(
            async context => {
                const testNodeDb = await context.read(x3Test.nodes.TestNodeDB, {
                    code: 'C1',
                });
                assert.instanceOf(testNodeDb, x3Test.nodes.TestNodeDB);
                assert.equal(await testNodeDb.code, 'C1');
                assert.equal(await testNodeDb.company, 'B-SAGE');
            },
            { config: { layers: ['b'] }, skipMocks: true },
        );
    });

    it('layers a-b', async function () {
        await Test.withContext(
            async context => {
                const testNodeDb = await context.read(x3Test.nodes.TestNodeDB, {
                    code: 'C1',
                });
                assert.instanceOf(testNodeDb, x3Test.nodes.TestNodeDB);
                assert.equal(await testNodeDb.code, 'C1');
                assert.equal(await testNodeDb.company, 'B-SAGE');
            },
            { config: { layers: ['a', 'b'] }, skipMocks: true },
        );
    });

    it('layers b-a', async function () {
        await Test.withContext(
            async context => {
                const testNodeDb = await context.read(x3Test.nodes.TestNodeDB, {
                    code: 'C1',
                });
                assert.instanceOf(testNodeDb, x3Test.nodes.TestNodeDB);
                assert.equal(await testNodeDb.code, 'C1');
                assert.equal(await testNodeDb.company, 'A-SAGE');
            },
            { config: { layers: ['b', 'a'] }, skipMocks: true },
        );
    });
    // Test using the same layer twice in a test suite
    it('layers a-b again', async function () {
        await Test.withContext(
            async context => {
                const testNodeDb = await context.read(x3Test.nodes.TestNodeDB, {
                    code: 'C1',
                });
                assert.instanceOf(testNodeDb, x3Test.nodes.TestNodeDB);
                assert.equal(await testNodeDb.code, 'C1');
                assert.equal(await testNodeDb.company, 'B-SAGE');
            },
            { config: { layers: ['a', 'b'] }, skipMocks: true },
        );
    });
});
