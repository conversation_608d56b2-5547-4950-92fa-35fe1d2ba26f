import type { Argv } from 'yargs';
import { StartDevOptions, startHandler } from './extensions/start';

export * from './commands/utils';

export const plugin = (yargs: Argv): Argv =>
    yargs.commandDir('./commands', {
        extensions: ['js'],
    });

export const builder = {
    start: (yargs: Argv): Argv =>
        yargs
            .option('watch-client', {
                desc: 'Watches client side artifacts (pages and stickers) and recompiles them when changes detected',
            })
            .option('watch-server', {
                desc:
                    'Watches server side artifacts (pages and stickers) and recompiles them when changes detected ' +
                    'and then rebounces the server',
            })
            .option('watch-all', {
                desc: 'Both of the watch options combined',
                // default: false,
            })
            .option('socket-key', {
                desc: 'socket key of compilation event stream',
                type: 'string',
            })
            .option('prod-ui', {
                desc: 'start in prod-Ui mode',
                type: 'boolean',
            }),
};

export const handler = {
    start: (argv: any, commandContext?: any) => startHandler(argv as StartDevOptions, commandContext),
};
