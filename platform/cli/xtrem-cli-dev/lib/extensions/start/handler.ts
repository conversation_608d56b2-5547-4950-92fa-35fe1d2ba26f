import { startWatchServer, typescriptObserver, watchClientArtifacts, webpackObserver } from '@sage/xtrem-cli-compile';
import {
    cliContext,
    ExecutionMode,
    initiateSocketServer,
    PluggableHandlerResult,
    printInfo,
    printWarning,
    reloadApplication,
} from '@sage/xtrem-cli-lib';
import { StartContext, StartOptions, startServices } from '@sage/xtrem-cli-main';
import { startInProdUiMode } from '../../commands/handlers/prod-ui/start-prod-ui';

export interface StartDevOptions extends StartOptions {
    watchAll?: boolean;
    watchServer?: boolean;
    watchClient?: boolean;
    socketKey?: string;
    prodUi?: boolean;
}

export const startHandler = (options: StartDevOptions, startContext: StartContext): Promise<PluggableHandlerResult> => {
    return (async () => {
        const { executionMode } = cliContext;
        const watchClient = options.watchClient || options.watchAll;
        const watchServer = options.watchServer || options.watchAll;
        const autoReload = !options.socketKey;
        const { dir, deployedApp, startServerOptions } = startContext;

        if (options.prodUi) {
            printInfo(executionMode, 'Starting in prod-Ui mode...');
            startInProdUiMode();
            return { skipDefault: false };
        }

        if (options.socketKey) {
            await initiateSocketServer(options.socketKey, {
                onRebounce: () => {
                    reloadApplication(deployedApp, dir, startServerOptions, startServices, true).catch(err => {
                        printWarning(ExecutionMode.INTEGRATED, 'Failed to reload the application');
                        printWarning(ExecutionMode.INTEGRATED, err.message);
                    });
                },
            });
        }

        if (watchClient) {
            printInfo(executionMode, 'Watching client...');
            const webpackCompilationObserver = webpackObserver({
                executionMode,
                deployedApp,
                dir,
                autoReload,
                ...startServerOptions,
            });

            const webpackObservable = watchClientArtifacts(executionMode, dir);
            if (webpackObservable) {
                webpackObservable.subscribe(webpackCompilationObserver);
            }
        }

        if (watchServer) {
            printInfo(executionMode, 'Watching server...');
            const typescriptCompilationObserver = typescriptObserver(
                {
                    executionMode,
                    deployedApp,
                    dir,
                    autoReload,
                    ...startServerOptions,
                },
                startServices,
            );

            const typescriptObservable = startWatchServer(executionMode, dir, !!options.isUsingReferences);
            typescriptObservable.subscribe(typescriptCompilationObserver);
        }

        return { skipDefault: !!(options.socketKey || watchClient || watchServer) };
    })();
};
