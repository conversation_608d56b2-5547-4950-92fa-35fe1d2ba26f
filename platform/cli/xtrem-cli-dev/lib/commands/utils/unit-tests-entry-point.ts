import { Config<PERSON>anager, Logger } from '@sage/xtrem-core';
import { setupTestSuite } from './mocha';

// This is a empty test entry-point used by the Mocha runner to setup the test suite and especially to create the test application.

// Load config from current dir so that we get rootdir/x3/xtrem-config.yml instead of rootdir/xtrem-config.yml
// when executing x3 unit tests
ConfigManager.load(process.cwd(), 'test');

// disable logs if config file says so.
if (ConfigManager.current?.logs?.disabledForTests) {
    Logger.disable();
}

// Execute setupTestSuite before any other test
before(setupTestSuite);
