import {
    ConfigManager,
    Dict,
    Logger,
    Test,
    TestOptions,
    WorkflowMock,
    WorkflowMockOptions,
    WorkflowResult,
    WorkflowRunOptions,
    WorkflowStartEvent,
    assertDeepPartialMatch,
} from '@sage/xtrem-core';
import { datetime } from '@sage/xtrem-date-time';
import { LogicError, defaultStringCompare } from '@sage/xtrem-shared';
import { assert } from 'chai';
import * as fs from 'fs';
import * as glob from 'glob';
import { load } from 'js-yaml';
import { isObject, mapValues, omit, pick } from 'lodash';
import * as fsp from 'path';
import { setupTestSuite } from './mocha';

// This is the single entry-point used by the Mocha runner to execute all workflow tests
// for a given application and a given pattern. These variables are given at runtime and are dynamically set
// through environment variables.

// Load config from current dir so that we get rootdir/x3/xtrem-config.yml instead of rootdir/xtrem-config.yml
// when executing x3 unit tests
ConfigManager.load(process.cwd(), 'test');

// disable logs if config file says so.
if (ConfigManager.current?.logs?.disabledForTests) {
    Logger.disable();
}

const logger = Logger.getLogger(__filename, 'workflow-tests');

// Execute setupTestSuite before any other test
before(setupTestSuite);

type ExecutionModeType = 'only' | 'skip' | 'normal';

export interface ScenarioOptions {
    executionMode?: ExecutionModeType;
    envConfigs?: TestOptions;
    mocks?: WorkflowMockOptions[];
}

export interface Scenario extends ScenarioOptions {
    name: string;
    startEvent: WorkflowStartEvent;
    expectedResult: WorkflowResult;
}

export interface Suite extends ScenarioOptions {
    scenarios: Dict<Scenario>;
}

const helpMessage =
    'See https://confluence.sage.com/display/XTREEM/4+Workflow+unit+tests for a detailed documentation.';

function fixTestActiveServiceOptions(options: TestOptions): void {
    if (options.testActiveServiceOptions) {
        // Service options are strings in the parameters.json file. Convert them to ServiceOption instances
        options.testActiveServiceOptions = (options.testActiveServiceOptions as unknown as string[]).map(name =>
            Test.application.findServiceOption(name),
        );
    }
}

const parseFile = <T>(file: string, text: string): T => {
    try {
        return (file.endsWith('.json') ? JSON.parse : load)(text) as T;
    } catch (e) {
        return assert.fail(`Cannot parse ${file}: ${e.message}.`);
    }
};

export function validateScenario(file: string, scenario: Scenario): void {
    const { startEvent, expectedResult, name } = scenario;

    assert.exists(startEvent, `${file}: You need to specify 'startEvent' for scenario '${name}'. ${helpMessage}`);
    assert.exists(
        startEvent.topic,
        `${file}: You need to specify 'topic' for startEvent of scenario '${name}'. ${helpMessage}`,
    );
    assert.exists(
        startEvent.payload,
        `${file}: You need to specify 'payload' for startEvent of scenario '${name}'. ${helpMessage}`,
    );
    assert.exists(
        expectedResult,
        `${file}: You need to specify 'expectedResult' for scenario '${name}'. ${helpMessage}`,
    );
    assert.exists(
        expectedResult.status,
        `${file}: You need to specify expected status for 'expectedResult' of scenario '${name}'. ${helpMessage}`,
    );
}

const omitSystemProperties = (obj: any): any => {
    return mapValues(omit(obj, '_id', '_updateTick'), value => {
        if (Array.isArray(value)) return value.map(omitSystemProperties);
        if (isObject(value)) return omitSystemProperties(value);
        return value;
    });
};

/** Runs a test scenario */
export async function runScenario({
    file,
    scenario,
    scenarioOptions,
}: {
    file: string;
    scenario: Scenario;
    scenarioOptions: ScenarioOptions;
}): Promise<void> {
    validateScenario(file, scenario);
    const { startEvent, expectedResult, mocks } = scenario;
    const testOptions: TestOptions = { ...(scenarioOptions.envConfigs ?? {}) };

    fixTestActiveServiceOptions(testOptions);

    const runOptions: WorkflowRunOptions = {
        tenantId: testOptions.tenantId || Test.defaultTenantId,
        userEmail: testOptions.user?.email || Test.defaultEmail,
        loginEmail: testOptions.user?.email || Test.defaultEmail,
        locale: testOptions.locale,
    };

    const run = async (body: () => Promise<WorkflowResult>): Promise<WorkflowResult> => {
        const envConfigs = scenarioOptions.envConfigs;
        if (envConfigs && (envConfigs.today || envConfigs.now)) {
            // Use a date mock
            if (envConfigs.now && envConfigs.today) {
                throw new LogicError("specify 'now' or 'today' in envConfigs but not both");
            }
            if (envConfigs.now) datetime.overrideNow(envConfigs.now);
            else datetime.overrideNow(`${envConfigs.today}T12:00:00Z`);
            try {
                return await body();
            } finally {
                datetime.overrideNow(null);
            }
        }
        return body();
    };

    const result = await run(() =>
        WorkflowMock.run(mocks, () => Test.application.workflowManager.runTest(startEvent, runOptions)),
    );
    // Omit _id from the result as it may vary
    const actual = omitSystemProperties(result);
    logger.debug(() => `Actual workflow result : \n ${JSON.stringify(actual, null, 4)}`);

    // compare the results of query (actual) vs the result (expected)
    assertDeepPartialMatch(actual, expectedResult);
}

const getItOverride = (
    mode?: ExecutionModeType,
): Mocha.TestFunction | Mocha.ExclusiveTestFunction | Mocha.PendingTestFunction => {
    switch (mode) {
        case 'only':
            return it.only;
        case 'skip':
            return it.skip;
        default:
            return it;
    }
};

const runWorkflowTest = (file: string): void => {
    const suite = parseFile<Suite>(file, fs.readFileSync(file, 'utf8'));

    Object.entries(suite.scenarios).forEach(([name, scenario]) => {
        scenario.name = name;
        const scenarioOptions = {
            ...pick(suite, ['executionMode', 'envConfigs', 'mocks']),
            ...pick(scenario, ['executionMode', 'envConfigs', 'mocks']),
        };
        const overriddenIt = getItOverride(scenarioOptions.executionMode);

        overriddenIt(name, () =>
            runScenario({
                file,
                scenario,
                scenarioOptions,
            }),
        );
    });
};

/**
 * Execute mocha `decribe` call for all workflow tests.
 * @param {string} applicationDir
 * @param [string] pattern
 */
const describeWorkflowTests = (applicationDir: string, pattern = ''): void => {
    const workflowDir = fsp.join(applicationDir, 'test/workflow');
    if (fs.existsSync(workflowDir)) {
        before(() => Test.application.workflowManager.start());

        describe('Workflow tests', () => {
            const files = glob
                .sync(`./test/workflow/${pattern}*.{json,yml}`, {
                    cwd: applicationDir,
                    absolute: true,
                    realpath: true,
                })
                .sort(defaultStringCompare);
            files.forEach(file => {
                // extract the scenario name from the directory
                const message = file.substring(applicationDir.length + 1).replace(/\.(ts|yml)$/, '');
                describe(message, () => runWorkflowTest(file));
            });
        });
    } else {
        // eslint-disable-next-line no-console
        console.warn(`${workflowDir} is missing!`);
    }
};

describeWorkflowTests(process.env.APPLICATION_DIR ?? process.cwd(), process.env.WORKFLOW_TEST_PATTERN);
