import { CliHooks, ExecutionMode, printWarning } from '@sage/xtrem-cli-lib';
import { Application, ConfigManager } from '@sage/xtrem-core';

export { activatesContainerHeartbeatMonitor } from '@sage/xtrem-cli-lib';

export async function loadTestDatabase(
    executionMode: ExecutionMode,
    application: Application,
    source: string,
    options?: {
        layersAsString?: string;
        serviceOptionsAsString?: string;
        /**
         * Should the schema be reset before loading data ?
         */
        noSchemaReset?: boolean;
    },
): Promise<void> {
    const config = ConfigManager.load(application.dir, source);
    const layers = options?.layersAsString ? options.layersAsString?.split(',').map(layer => layer.trim()) : undefined;
    const defaultLayers = ['setup', 'test'];
    const contextOptions = {
        config,
        testMode: true,
        testLayers: layers || defaultLayers,
        testActiveServiceOptionNames: options?.serviceOptionsAsString
            ? options?.serviceOptionsAsString.split(',').map(serviceOption => serviceOption.trim())
            : undefined,
    };
    if (!options?.noSchemaReset) {
        try {
            await Application.createDbSchema(application.schemaName);
        } catch (e) {
            printWarning(
                executionMode,
                `cannot create or check if schema ${application.schemaName} exists: ${e.message}`,
            );
        }
    }
    await CliHooks.testManager.prepareLoadTestDatabase(application, contextOptions);
}
