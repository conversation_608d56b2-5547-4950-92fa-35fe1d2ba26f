import { cliContext, ExecutionMode, isDebugging, printWarning, withTiming } from '@sage/xtrem-cli-lib';
import { ApplicationCreateOptions, ConfigManager, dynamicImport, Test } from '@sage/xtrem-core';
import { use } from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as fs from 'fs';
import * as Mocha from 'mocha';
import * as path from 'path';
import { MochaTestReporter } from './mocha-reporter';

// TODO: remove any but compile fails on xtrem-avalara-gateway
use(chaiAsPromised as any);

// eslint-disable-next-line import/no-mutable-exports
export let mochaErrors: { [key: string]: string[] };

export const setupTestSuite = async (): Promise<void> => {
    if (Test.application != null) {
        printWarning(ExecutionMode.STANDALONE, 'Test application already set');
        return;
    }
    const dir = process.env.APPLICATION_DIR ?? process.cwd();
    // Load config first so that we can access it from code without dir
    ConfigManager.load(dir, 'test');

    const modules = await withTiming(ExecutionMode.STANDALONE, {
        // body: () => dynamicImport(path.join(dir, 'index.ts')),
        body: () => dynamicImport(path.join(dir, 'build/index.js')),
        success: () => `Required modules from ${dir}`,
    });
    const buildDir = path.join(path.join(dir, 'build'));

    const options: ApplicationCreateOptions = { api: modules, buildDir };

    process.env.XTREM_USE_TEST_APPLICATION = '1';
    Test.application = await withTiming(ExecutionMode.STANDALONE, {
        body: () => Test.createTestApplication(options),
        success: () => 'application created',
    });

    // Execute some code after the application is created but before the tests are executed
    // This is useful to stub some functions
    const beforeTest = path.join(dir, 'build/test/fixtures/before-test.js');
    if (fs.existsSync(beforeTest)) {
        const beforeTestContent = await dynamicImport(beforeTest);
        if (beforeTestContent.beforeTest) {
            await withTiming(ExecutionMode.STANDALONE, {
                body: () => beforeTestContent.beforeTest(),
                success: () => `running beforeTest from ${beforeTest}`,
            });
        }
    }

    const testOptions = process.env.XTREM_TEST_OPTIONS
        ? JSON.parse(process.env.XTREM_TEST_OPTIONS)
        : cliContext.options;

    if (testOptions.allServiceOptions) {
        Test.cliActiveServiceOptions = Object.values(Test.application.serviceOptionsByName);
    } else if (testOptions.serviceOptions) {
        Test.cliActiveServiceOptions = (testOptions.serviceOptions as string)
            .split(',')
            .map(name => Test.application.findServiceOption(name));
    }
};

export const createMochaRunner = (executionMode: ExecutionMode, dir: string, isCi: boolean) => {
    mochaErrors = {};
    const packageFileContent = JSON.parse(fs.readFileSync(path.resolve(dir, 'package.json'), 'utf-8'));
    let reporter;

    if (isCi) {
        reporter = 'mocha-junit-reporter';
    } else {
        reporter = 'spec';
    }

    if (process.env.REPORTER === 'custom') {
        reporter = MochaTestReporter;
    }

    const testOptions = process.env.XTREM_TEST_OPTIONS
        ? JSON.parse(process.env.XTREM_TEST_OPTIONS)
        : cliContext.options;

    const getTimeout = () => {
        if (testOptions.noTimeout) {
            printWarning(executionMode, '--noTimeout set : timeout is disabled.');
            return false;
        }
        if (testOptions.timeout) {
            printWarning(executionMode, `timeout forced to ${testOptions.timeout} ms`);
            return testOptions.timeout as number;
        }
        return false;
    };
    // TODO: remove any but compile fails on xtrem-avalara-gateway
    const mocha = new (Mocha as any)({
        color: true,
        ui: '' /* TODO: REMOVE */,
        ignoreLeaks: false,

        timeout: isDebugging ? false : getTimeout(),
        reporter,
        fullStackTrace: true,
        reporterOptions: isCi
            ? {
                  junit_report_name: packageFileContent.description,
                  junit_report_path: path.resolve(
                      dir,
                      `junit-report-${packageFileContent.name.replace('/', '-').replace('@', '')}.xml`,
                  ),
              }
            : undefined,
    } as any);

    return mocha;
};

export async function runMochaTests(mocha: Mocha, isEsm: boolean): Promise<boolean> {
    return !(await new Promise<number>(resolve => {
        if (isEsm) {
            mocha
                .loadFilesAsync()
                .then(() => mocha.run(resolve))
                .catch(() => resolve(1));
        } else {
            mocha.run(resolve);
        }
    }));
}
