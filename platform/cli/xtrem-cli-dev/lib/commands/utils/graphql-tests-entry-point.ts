import { <PERSON><PERSON><PERSON><PERSON>, ConfigManager, Dict, Logger, mapGraphQlResult, Test, TestOptions } from '@sage/xtrem-core';
import { defaultStringCompare, dotNotate, handleBars, tryJsonParse } from '@sage/xtrem-shared';
import { assert } from 'chai';
import * as fs from 'fs';
import * as glob from 'glob';
import { ExecutionResult, graphql, GraphQLSchema } from 'graphql';
import { load } from 'js-yaml';
import { difference } from 'lodash';
import * as fsp from 'path';
import { setupTestSuite } from './mocha';

// This is the single entry-point used by the Mocha runner to execute all Graphql tests
// for a given application and a given pattern. These variables are given at runtime and are dynamically set
// through environment variables.

// Load config from current dir so that we get rootdir/x3/xtrem-config.yml instead of rootdir/xtrem-config.yml
// when executing x3 unit tests
ConfigManager.load(process.cwd(), 'test');

// disable logs if config file says so.
if (ConfigManager.current?.logs?.disabledForTests) {
    Logger.disable();
}

const logger = Logger.getLogger(__filename, 'graphql-tests');

// Execute setupTestSuite before any other test
before(setupTestSuite);

type ExecutionModeType = 'only' | 'skip' | 'normal';

interface Parameters {
    [key: string]: {
        input?: object;
        output: object;
        variables?: Dict<AnyValue>;
        envConfigs?: TestOptions;
        layers: string[];
        mocks?: string[];
        executionMode?: ExecutionModeType;
    };
}
interface MinimalParameters {
    envConfigs?: TestOptions;
    layers: string[];
    // these properties cannot be used by developers
    // they are here for checking errors purpose
    input?: object;
    output: object;
    variables?: object;
    executionMode?: ExecutionModeType;
}
interface ApplicationTestParameters {
    directory: string;
    schema: GraphQLSchema;
}

interface TestScenarioInput {
    parameters: Parameters;
    scenario: string;
    requestHbs: string;
    schema: GraphQLSchema;
    responseHbs: string;
    directory: string;
}
const helpMessage = 'See https://confluence.sage.com/display/XTREEM/3+GraphQL+unit+tests for a detailed documentation.';

/**
 * Sets the layers property for the passed in TestOptions from parameters passed
 * @param opts
 * @param parameters
 */
function setLayersFromParameters(options: TestOptions, parameters: any): TestOptions {
    const opts = options || {};
    if (parameters.layers) {
        opts.config = opts.config || {};
        opts.config.layers = parameters.layers;
        opts.config.sql = opts.config.sql || {};
        opts.config.sql.layers = parameters.layers;
    }
    return opts;
}

function fixTestActiveServiceOptions(options: TestOptions): void {
    if (options.testActiveServiceOptions) {
        // Service options are strings in the parameters.json file. Convert them to ServiceOption instances
        options.testActiveServiceOptions = (options.testActiveServiceOptions as unknown as string[]).map(name =>
            Test.application.findServiceOption(name),
        );
    }
}

/**
 * Validate that required query files have the correct structure
 * @param directory
 * @param requestGraphqlPath
 * @param responseHbsPath
 * @param requestHbsPath
 * @param responseJsonPath
 * @returns {boolean} isExpectingParameters
 */
const validateQueryFileStructure = (
    isResponseHbs: boolean,
    isRequestHbs: boolean,
    isResponseJson: boolean,
    isRequestGraphql: boolean,
    isExpectingParameters: boolean,
    directory: string,
): void => {
    assert.isFalse(
        !isRequestGraphql && !isRequestHbs,
        `Request file is missing, you need to add either 'request.graphql' or 'request.graphql.hbs' to '${directory}'. ${helpMessage}`,
    );
    assert.isFalse(
        !isResponseJson && !isResponseHbs,
        `Response file is missing, you need to add either 'response.json' or 'response.json.hbs' to '${directory}'. ${helpMessage}`,
    );
    assert.isFalse(
        isRequestGraphql && isRequestHbs,
        `Use either 'request.graphql' or 'request.graphql.hbs' but not both. Please delete one of the two from '${directory}'. ${helpMessage}`,
    );
    assert.isFalse(
        isResponseJson && isResponseHbs,
        `Use either 'response.json' or 'response.json.hbs' but not both. Please delete one of the two from '${directory}'. ${helpMessage}`,
    );
    assert.isFalse(
        !isExpectingParameters && (!isRequestGraphql || !isResponseJson),
        `Parameters file is missing: you need to add a file called 'parameters.json' or 'parameters.yml' under '${directory}'. ${helpMessage}`,
    );
};

/**
 * Load parameters.json file and validate
 * @param parametersPath
 * @param directory
 * @returns
 */
const loadParameterFile = (parametersPath: string, directory: string): any => {
    const parametersString = fs.readFileSync(parametersPath, 'utf8');
    const parameters = parametersPath.endsWith('.json') ? tryJsonParse(parametersString) : load(parametersString);

    assert.exists(parameters, `'parameters.json' under '${directory}' is not a valid JSON file. ${helpMessage}`);
    return parameters;
};

/**
 * Runs a test scenario, i.e. any key defined in 'parameters.json'.
 *
 * @export
 * @param {TestScenarioInput} {
 *     parameters,
 *     scenario,
 *     applicationDir,
 *     requestHbs,
 *     schema,
 *     responseHbs,
 *     directory,
 * }
 */
export async function runScenario({
    parameters,
    scenario,
    requestHbs,
    schema,
    responseHbs,
    directory,
}: TestScenarioInput): Promise<void> {
    const { input, variables, envConfigs } = parameters[scenario];

    let options: TestOptions = envConfigs ?? {};

    // if mocks are provided in parameters.json then set the directory and scenario in the options object
    if (options?.mocks) {
        options.directory = directory;
        options.scenario = scenario;
    }
    assert.isFalse(
        input === undefined && variables === undefined,
        `You need to specify either 'input' or 'variables' for test scenario '${scenario}'. ${helpMessage}`,
    );
    assert.isUndefined(
        input && variables,
        `Use either 'input' or 'variables' for test scenario '${scenario}' but not both. ${helpMessage}`,
    );

    options = setLayersFromParameters(options, parameters[scenario]);
    fixTestActiveServiceOptions(options);

    options.source = 'graphql';

    let actual = {};
    if (input) {
        const { variables: requestTemplateVariables, result: request } = handleBars(requestHbs, input);
        logger.debug(() => request);

        /**
         * inputParameters is a key added on handlebar we don't need the properties inputParameters in the parameter.json file
         */
        if (requestTemplateVariables.includes('inputParameters')) {
            requestTemplateVariables.splice(
                requestTemplateVariables.findIndex(variable => variable === 'inputParameters'),
            );
        }

        /**
         * inputData is a key added on handlebar we don't need the properties inputData in the parameter.json file
         */
        if (requestTemplateVariables.includes('inputData')) {
            requestTemplateVariables.splice(requestTemplateVariables.findIndex(variable => variable === 'inputData'));
        }

        // if input is provided then check if all the required variables are provided vs the request template
        const missingRequestVariables = difference(requestTemplateVariables, Object.keys(dotNotate(input)));
        assert.isFalse(
            missingRequestVariables.length > 0,
            `You need to add the following variables as an object to '${scenario}' under the 'input' key: ${JSON.stringify(
                missingRequestVariables,
            )}. ${helpMessage}`,
        );
        actual = await Test.withContext(
            async context => {
                return mapGraphQlResult(
                    (await graphql({ schema, source: request, contextValue: context })) as ExecutionResult,
                );
            },
            { ...options, source: options.source ?? 'graphql' },
        );
    } else {
        actual = await Test.withContext(
            async context => {
                return mapGraphQlResult(
                    (await graphql({
                        schema,
                        source: requestHbs,
                        contextValue: context,
                        variableValues: variables,
                    })) as ExecutionResult,
                );
            },
            { ...options, source: options.source ?? 'graphql' },
        );
    }
    logger.debug(() => `Actual result of the query : \n ${JSON.stringify(actual, null, 4)}`);

    // fetch output from parameters.json
    const output = parameters[scenario].output;
    assert.isFalse(
        output === undefined,
        `You need to specify an 'output' property for test scenario '${scenario}'. ${helpMessage}`,
    );
    const isJsonOutput = responseHbs.indexOf('{{{output}}}') !== -1;
    // if the attribute of an output is an array, the handle bars resolution does not work well
    // so we check if the output attribute is an array and then stringfy it like {{{output}}}
    const handleBarData = Object.keys(output).reduce((r, k) => {
        const val = (output as any)[k];
        r[k] = Array.isArray(val) ? JSON.stringify(val) : val;
        return r;
    }, {} as Dict<any>);

    const { variables: responseTemplateVariables, result: responseJson } = isJsonOutput
        ? handleBars(responseHbs, {
              output: JSON.stringify(output),
          })
        : handleBars(responseHbs, handleBarData);
    if (!isJsonOutput) {
        const missingResponseVariables = difference(responseTemplateVariables, Object.keys(dotNotate(output)));
        assert.isFalse(
            missingResponseVariables.length > 0,
            `You need to add the following variables as an object to '${scenario}' under the 'output' key: ${JSON.stringify(
                missingResponseVariables,
            )}. ${helpMessage}`,
        );
    }

    const response = tryJsonParse(responseJson);
    assert.exists(response, `response file under '${directory}' could not be parsed. ${helpMessage}`);

    // compare the results of query (actual) vs the response (expected)
    assert.deepEqual(actual, response, `Scenario '${scenario}' has failed. All errors are displayed above.`);
}

const resolveSingleScenario = async (
    isResponseHbs: boolean,
    isRequestHbs: boolean,
    isResponseJson: boolean,
    isRequestGraphql: boolean,
    { schema, directory }: ApplicationTestParameters,
    requestGraphqlPath: string,
    responseJsonPath: string,
    parameters?: Parameters | MinimalParameters,
    scenario?: string,
): Promise<void> => {
    validateQueryFileStructure(isResponseHbs, isRequestHbs, isResponseJson, isRequestGraphql, !!parameters, directory);
    if (isResponseHbs && isRequestHbs) {
        const requestHbs = fs.readFileSync(requestGraphqlPath, 'utf8');
        assert.isNotEmpty(requestHbs, `'request.graphql.hbs' under '${directory}' is empty. ${helpMessage}`);
        const responseHbs = fs.readFileSync(responseJsonPath, 'utf8');
        assert.isNotEmpty(responseHbs, `'response.json.hbs' file is empty. ${helpMessage}`);
        await runScenario({
            parameters: parameters as Parameters,
            scenario: scenario || directory,
            requestHbs,
            schema,
            responseHbs,
            directory,
        });

        return;
    }
    let options: TestOptions = { source: 'graphql' };
    if (parameters) {
        const { input, variables, output } = parameters;
        if (!isRequestHbs) {
            assert.isFalse(
                input !== undefined,
                `'input' property is only for handlebars tests (with request.graphql.hbs and response.json.hbs). ${helpMessage}`,
            );

            assert.isFalse(
                output !== undefined,
                `'output' property is only for handlebars tests (with request.graphql.hbs and response.json.hbs). ${helpMessage}`,
            );

            assert.isFalse(
                variables !== undefined,
                `'variables' property is only for handlebars tests (with request.graphql.hbs and response.json.hbs). ${helpMessage}`,
            );
        }
        options = parameters.envConfigs ?? {};
        if (options?.mocks) options.directory = directory;

        options = setLayersFromParameters(options, parameters);
        fixTestActiveServiceOptions(options);
    }

    const requestGraphql = fs.readFileSync(requestGraphqlPath, 'utf8');
    assert.isNotEmpty(requestGraphql, `'request.graphql' under '${directory}' is empty. ${helpMessage}`);
    const responseJson = fs.readFileSync(responseJsonPath, 'utf8');
    assert.isNotEmpty(responseJson, `'response.json' under '${directory}' is empty. ${helpMessage}`);
    const response = tryJsonParse(responseJson);
    assert.exists(response, `'response.json' under '${directory}' is not a valid JSON file. ${helpMessage}`);

    logger.debug(() => requestGraphql);
    const actual = await Test.withContext(
        async context => {
            return mapGraphQlResult(
                (await graphql({ schema, source: requestGraphql, contextValue: context })) as ExecutionResult,
            );
        },
        { ...options, source: options.source ?? 'graphql' },
    );
    logger.debug(() => `Actual result of the query : \n ${JSON.stringify(actual, null, 4)}`);
    // as this ia single request in the it the resetting of the sql pool is handled in the before hook
    // in mocha.ts
    assert.deepEqual(actual, response, `Test scenario '${directory}' has failed.`);
};

const runGraphqlTest = (directory: string): void => {
    const requestGraphqlPath = fsp.join(directory, 'request.graphql');
    const responseJsonPath = fsp.join(directory, 'response.json');
    const requestHbsPath = fsp.join(directory, 'request.graphql.hbs');
    const responseHbsPath = fsp.join(directory, 'response.json.hbs');
    const parametersJsonPath = fsp.join(directory, 'parameters.json');
    const parametersYmlPath = fsp.join(directory, 'parameters.yml');

    const isExpectingJsonParameters = fs.existsSync(parametersJsonPath);
    const isExpectingYmlParameters = fs.existsSync(parametersYmlPath);
    const isExpectingParameters = isExpectingJsonParameters || isExpectingYmlParameters;
    const parametersPath = isExpectingJsonParameters ? parametersJsonPath : parametersYmlPath;

    const isResponseJson = fs.existsSync(responseJsonPath);
    const isRequestGraphql = fs.existsSync(requestGraphqlPath);
    const isResponseHbs = fs.existsSync(responseHbsPath);
    const isRequestHbs = fs.existsSync(requestHbsPath);

    if (!isExpectingParameters) {
        // don't expect parameter just normal request and response filed
        // case of without handlebars and without parameters.json
        it('default test scenario', async () => {
            assert.exists(Test.application, "Couldn't find a valid application object.");

            if (!schema) schema = await Test.application.getGraphQLSchema();
            await resolveSingleScenario(
                isResponseHbs,
                isRequestHbs,
                isResponseJson,
                isRequestGraphql,
                { schema, directory },
                requestGraphqlPath,
                responseJsonPath,
            );
        });
    } else {
        // parameters provided, so we expect to run 1 to many scenarios

        let parameters: Parameters | MinimalParameters = {};

        if (isResponseHbs && isRequestHbs) {
            parameters = loadParameterFile(parametersPath, directory) as Parameters;
        } else {
            parameters = loadParameterFile(parametersPath, directory) as MinimalParameters;
        }

        const getItOverride = (mode?: ExecutionModeType): any => {
            switch (mode) {
                case 'only':
                    return it.only;
                case 'skip':
                    return it.skip;
                default:
                    return it;
            }
        };

        if (isResponseHbs && isRequestHbs) {
            // handlebars case

            Object.keys(parameters).forEach(scenario => {
                const overriddenIt = getItOverride((parameters as Parameters)[scenario].executionMode);

                overriddenIt(scenario, async () => {
                    if (!schema) schema = await Test.application.getGraphQLSchema();
                    await resolveSingleScenario(
                        isResponseHbs,
                        isRequestHbs,
                        isResponseJson,
                        isRequestGraphql,
                        { schema, directory },
                        requestHbsPath,
                        responseHbsPath,
                        parameters as Parameters,
                        scenario,
                    );
                });
            });
        } else {
            // normal case with env variables and layers

            const overriddenIt = getItOverride((parameters as MinimalParameters).executionMode);

            overriddenIt('default test scenario', async () => {
                if (!schema) schema = await Test.application.getGraphQLSchema();
                await resolveSingleScenario(
                    isResponseHbs,
                    isRequestHbs,
                    isResponseJson,
                    isRequestGraphql,
                    { schema, directory },
                    requestGraphqlPath,
                    responseJsonPath,
                    parameters as MinimalParameters,
                );
            });
        }
    }
};

let schema: any;

/**
 * Initializes and runs the matching graphQL tests
 * @param {string} applicationDir
 * @param [string] pattern
 */
const describeGraphqlTests = (applicationDir: string, pattern = ''): void => {
    const graphqlDir = fsp.join(applicationDir, 'test/graphql');
    if (fs.existsSync(graphqlDir)) {
        describe('Graphql tests', () => {
            const directories = glob
                .sync(`./test/graphql/${pattern}*/`, {
                    cwd: applicationDir,
                    absolute: true,
                    realpath: true,
                })
                .sort(defaultStringCompare);
            directories.forEach(directory => {
                // extract the scenario name from the directory
                const message = directory.substring(applicationDir.length + 1);
                describe(message, () => {
                    runGraphqlTest(directory);
                });
            });
        });
    } else {
        // eslint-disable-next-line no-console
        console.warn(`${graphqlDir} is missing!`);
    }
};

describeGraphqlTests(process.env.APPLICATION_DIR ?? process.cwd(), process.env.GRAPHQL_TEST_PATTERN);
