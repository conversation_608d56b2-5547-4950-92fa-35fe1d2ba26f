import { URL } from 'node:url';

const localDevUrls: (string | undefined)[] = ['http://sdmo-xtrem.localhost.dev-sagextrem.com:8240', 'http://xtrem.localhost.dev-sagextrem.com:8240', 'http://localhost:8240'];

const targetUrl = process.env.TARGET_URL ? new URL(process.env.TARGET_URL) : undefined;
export const targetUrlOrigin = targetUrl?.origin;
export const targetBaseUrl = targetUrl?.pathname === '/unsecuredevlogin' ? targetUrlOrigin : targetUrl?.href;
export const isLocalTargetUrl = () => localDevUrls.includes(targetUrlOrigin);
export const isRemoteTargetUrl = () => targetUrlOrigin && !isLocalTargetUrl();
