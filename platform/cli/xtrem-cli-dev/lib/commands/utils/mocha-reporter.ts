/* eslint-disable no-console */
import * as <PERSON><PERSON> from 'mocha';
import * as Base from 'mocha/lib/reporters/base';
import { mochaErrors } from './mocha';

/**
 * Constructs a new `Spec` reporter instance.
 *
 * @public
 * @class
 * @memberof Mocha.reporters
 * @extends Mocha.reporters.Base
 * @param {Runner} runner - Instance triggers reporter actions.
 * @param {Object} [options] - runner options
 */
export function MochaTestReporter(this: any, runner: Mocha.Runner) {
    Base.call(this, runner);
    const self = this;
    self.indents = 0;
    self.indent = function indent() {
        return Array(self.indents).join('  ');
    };

    runner.on('start', () => {
        console.log('Runner on start');
    });
    runner.on('suite', (suite) => {
        self.indents += 1;
        console.log(Base.color('suite', '%s%s'), self.indent(), suite.title);
    });
    runner.on('suite end', () => {
        self.indents -= 1;
        if (self.indents === 1) {
            console.log();
        }
    });
    runner.on('pending', (test) => {
        const fmt = self.indent() + Base.color('pending', '  - %s');
        console.log(fmt, test.title);
    });
    runner.on('pass', (test) => {
        let fmt;
        if (test.speed === 'fast') {
            fmt = self.indent() + Base.color('checkmark', `  ${Base.symbols.ok}`) + Base.color('pass', ' %s');
            console.log(fmt, test.title);
        } else {
            fmt =
                self.indent() +
                Base.color('checkmark', `  ${Base.symbols.ok}`) +
                Base.color('pass', ' %s') +
                Base.color(test.speed, ' (%dms)');
            console.log(fmt, test.title, test.duration);
        }
    });
    runner.on('fail', (test: any, err: any) => {
        const key = getErrorKey(test.fullTitle());
        const value = err.message;
        if (mochaErrors[key]) {
            mochaErrors[key].push(value);
        } else {
            mochaErrors[key] = [value];
        }
        // let fmt = self.indent() + Base.color('fail', '  x %s');
        // console.log(fmt, test.title);
        // fmt = self.indent() + Base.color('fail', '  ERROR - %s');
        // console.log(value);
    });
    runner.once('end', () => {
        // failures
        // Print at at the end if there are a number of tests in package
        if (self.stats.failures) {
            self.failures.forEach((f: any) => {
                const {
                    title,
                    err: { message, stack },
                } = f;
                const fmt = self.indent() + Base.color('fail', '  FAILED - %s');
                console.log(fmt, `${title}: ${message}\n ${stack} \n`);
            });
        }
    });
}

// extracts the error key (the test-directory-name from a mocha error string generated by a graphql test
function getErrorKey(input: string) {
    // input is something like: 'Graphql test fixtures test-directory-name title of scenario'
    const describe1 = 'Graphql test fixtures ';
    if (!input.startsWith(describe1)) throw new Error(`Invalid error key: prefix not found: ${input}`);
    const spaceIndex = input.indexOf(' ', describe1.length);
    if (spaceIndex < 0) throw new Error(`Invalid error key: space not found: ${input}`);
    // return the test-directory-name substring
    return input.substring(describe1.length, spaceIndex);
}
