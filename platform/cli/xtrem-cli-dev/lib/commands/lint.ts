import { pluggableBuilder, pluggableHandler } from '@sage/xtrem-cli-lib';
import type { Argv } from 'yargs';
import { LintOptions } from './utils';

export const command = ['lint [path]', 'l'];
export const desc = 'Lints the source-code with ts-lint and prettier';

export const builder = (yargs: Argv) =>
    pluggableBuilder(
        command,
        yargs
            .version(false)
            .option('fix', {
                desc: 'Fixes auto-fixable issues',
                default: false,
                type: 'boolean',
            })
            .positional('path', {
                describe:
                    'Optional. Relative path of files inside the project to linted, it can be used with standard file matchers',
                type: 'string',
            })
            .example('$0 lint', 'Lint the current package'),
    );

export const handler = async (argv: LintOptions) => {
    const pluggableResult = await pluggableHandler(command, argv);
    if (!pluggableResult.skipDefault) {
        // Lazy loading of handlers to speed up the CLI loading
        // eslint-disable-next-line global-require
        require('./handlers/lint').lint(argv.fix, argv.path);
    }
};
