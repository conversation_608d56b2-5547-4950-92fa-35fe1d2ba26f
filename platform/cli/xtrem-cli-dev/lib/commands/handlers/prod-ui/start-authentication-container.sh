#!/usr/bin/env bash

docker run --rm \
-e CLIENT_ID="$CLIENT_ID" \
-e CLIENT_SECRET="$CLIENT_SECRET" \
-e ACCESS_TOKEN_LIFETIME=1500 \
-e CUSTOMER_CLIENT_ID=xx \
-e CUSTOMER_CLIENT_SECRET=xx \
-e AWS_REGION=eu-west-1 \
-e KNOWN_DOMAINS="*.dev-sagextrem.com" \
-e LOCAL_DEV=true \
-e XTREM_PORT=8240 \
-e LOCAL_SESSION=true \
-e MULTI_LOCAL_DEV=true \
-e LOCAL_SESSION=true \
-e LOCAL_TENANTS="777777777777777777777" \
-e BASE_URL=http://login.localhost.dev-sagextrem.com:8080 \
-e DEFAULT_LOCALE=en-US \
-e ONETRUST_FORCED_PREFERENCE_LOCAL_DEVa=7 \
-e KNOWN_DOMAINS="*.dev-sagextrem.com" \
-e ISSUER=connect.localhost.dev-sagextrem.com \
-p "8080:8080" --name xtrem_auth \
ghcr.io/sage-erp-x3/xtrem-deployment-authentication:master
