import { Config } from '@sage/xtrem-shared';
import { execSync } from 'child_process';
import * as fs from 'fs';
import { load } from 'js-yaml';
import * as os from 'os';
import * as fsp from 'path';

/**
 * The full filename of the configuration file.
 */
let configFullFilename: string | undefined;

/**
 * The full filename of the security file.
 */
let securityFullFilename: string | undefined;

function logSuccess(message: string) {
    console.log(`✅ ${message}`);
}

function exitWithFailure(message: string): never {
    console.log(`❌ ${message}`);
    process.exit(1);
}

function logWarning(message: string) {
    console.log(`⚠️ ${message}`);
}

/**
 * Start, if needed, the docker container for the authentication service
 */
function startAuthenticationContainer(config: Config): void {
    const authContainerName = 'xtrem_auth';
    // Check if the container is already running
    const isRunning =
        execSync(`docker ps --filter "name=${authContainerName}" --format "{{.Names}}"`).toString().trim() ===
        authContainerName;

    if (isRunning) {
        logSuccess('Authentication service container is running.');
        return;
    }
    console.log('\x1b[0;33m'); // change color to orange
    console.log(`****************************************************************************************`);
    console.log('***');
    console.log('***');
    console.log('*** The authentication service container has been started ...');
    console.log(
        `*** Please open a new console and re-run 'pnpm ${process.argv.slice(2).join(' ')}' to start the application.`,
    );
    console.log('***');
    console.log('***');
    console.log(`****************************************************************************************`);
    console.log('\x1b[0m'); // restore default color
    try {
        execSync(`${getSourceDirname()}/start-authentication-container.sh`, {
            stdio: 'inherit',
            env: {
                ...process.env,
                CLIENT_ID: config.authenticationContainer?.clientId,
                CLIENT_SECRET: config.authenticationContainer?.clientSecret,
            },
        });
    } catch (error) {
        if (error.signal === 'SIGINT') {
            // The user used Ctrl-C to stop the authentication service container
            logSuccess('The authentication service container has been stopped');
            console.log(
                `
*******************************************************************************************************************************************


        If you no longer need to run in prod-Ui mode, simply:

        \t- remove the 'prodUi: true' line from your configuration file (${configFullFilename})

        \t- delete the file ${securityFullFilename}


*******************************************************************************************************************************************
        `,
            );
            process.exit(0);
        } else throw error;
    }
}

/**
 * Loads a configuration file by searching the specified path and optionally its parent directories.
 *
 * @param filename - The name of the configuration file to load.
 * @param path - The starting directory to search for the file.
 * @param recurse - Whether to search parent directories if the file is not found in the starting directory. Defaults to true.
 * @returns An object containing the filename and its content if found, otherwise undefined.
 */
function loadConfigurationFile(
    filename: string,
    path: string,
    recurse = true,
): { filename: string; content: string } | undefined {
    const fullFilename = fsp.join(path, filename);
    if (!fs.existsSync(fullFilename)) {
        if (!recurse) return undefined;
        const parentFolder = fsp.join(path, '..');
        if (parentFolder === path) return undefined;
        return loadConfigurationFile(filename, parentFolder);
    }
    return {
        filename: fullFilename,
        content: fs.readFileSync(fullFilename, 'utf8'),
    };
}

/**
 * Remove the 'build' part the __dirname path
 */
function getSourceDirname(): string {
    return __dirname.replace('/build/lib/', '/lib/');
}

/**
 * Creates a security file in the specified configuration folder.
 *
 * @param xtremConfigFolder - The folder where the security file should be created.
 * @returns An object containing the filename and its content.
 */
function createSecurityFile(xtremConfigFolder: string): { filename: string; content: string } {
    const fullFilename = fsp.join(xtremConfigFolder, 'xtrem-security.yml');
    fs.copyFileSync(fsp.join(getSourceDirname(), 'xtrem-security.yml.template'), fullFilename);
    logWarning(`Security file created at ${fullFilename}.`);
    return {
        filename: fullFilename,
        content: fs.readFileSync(fullFilename, 'utf8'),
    };
}

/**
 * Returns the local IP address for WSL
 */
function getLocalIpAddressForWsl(): string {
    const addresses = os.networkInterfaces();
    const eth0 = addresses.eth0;
    if (eth0 == null) exitWithFailure(`No network interface found named 'eth0'`);
    const ipv4Address = eth0.find(address => address.family === 'IPv4' && !address.internal);
    if (ipv4Address == null) exitWithFailure(`No IPv4 address found for 'eth0'`);
    return ipv4Address.address;
}

/**
 * Checks the hosts file for WSL and ensures the correct redirections are set.
 */
function checkHostFileForWsl(appPrefix: string): void {
    const folder = '/mnt/c/Windows/System32/drivers/etc';
    const hostFile = loadConfigurationFile('hosts', folder, false);
    if (hostFile == null) exitWithFailure(`No hosts file found in ${folder}`);

    const ipAddress = getLocalIpAddressForWsl();

    const isValidIp = /^\d{1,3}(\.\d{1,3}){3}$/.test(ipAddress);
    if (!isValidIp) {
        exitWithFailure(`Invalid IP address retrieved: ${ipAddress}`);
    }

    const checkRedirection = (redirection: string) => {
        const regex = new RegExp(`^(\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}) ${redirection}$`, 'm');
        const content = hostFile.content.replace(/\r/g, '');
        const match = content.match(regex);
        if (!match) {
            exitWithFailure(
                `In Windows, edit the file 'C:\\Windows\\System32\\drivers\\etc\\hosts' and add \
the following line:\n\t${ipAddress} ${redirection}.`,
            );
        }
        if (match[1] === ipAddress) {
            logSuccess(`Redirection for ${redirection} is already set to ${ipAddress}.`);
            return;
        }
        exitWithFailure(
            `In Windows, edit the file 'C:\\Windows\\System32\\drivers\\etc\\hosts' and replace \
the following line:\n\t${match[1]} ${redirection}\nwith:\n\t${ipAddress} ${redirection}.`,
        );
    };
    checkRedirection('login.localhost.dev-sagextrem.com');
    checkRedirection('websocket.localhost.dev-sagextrem.com');
    checkRedirection('connect.localhost.dev-sagextrem.com');

    checkRedirection(`${appPrefix}xtrem.localhost.dev-sagextrem.com`);
}

/**
 * Checks the configuration and ensures all necessary files and settings are in place.
 * This includes verifying the presence of a configuration file, validating the prodUi setting,
 * and ensuring a security file exists.
 */
function checkConfiguration(): Config {
    // Load the configuration
    const configContent = loadConfigurationFile('xtrem-config.yml', process.cwd());
    if (!configContent) {
        exitWithFailure(`Configuration file not found (starting from ${process.cwd()}).`);
    }
    logSuccess(`Configuration file found at ${configContent.filename}.`);
    configFullFilename = configContent.filename;

    const config = load(configContent.content) as Config;
    if (!config) {
        exitWithFailure(`Configuration file is not valid YAML.`);
    }

    // Check prodUi setting
    if (config.prodUi !== true) {
        exitWithFailure(`You must add 'prodUi: true' to your configuration file ${configContent.filename}.`);
    }
    logSuccess('prodUi is enabled in the configuration file');

    if (
        config.authenticationContainer == null ||
        config.authenticationContainer.clientId == null ||
        config.authenticationContainer.clientSecret == null
    ) {
        exitWithFailure(`Your configuration file ${configContent.filename} must have a 'authenticationContainer' section with this format:\

authenticationContainer:
  clientId: <clientId>
  clientSecret: <clientSecret>

The values for <clientId> and <clientSecret> can be found in Keeper (Global XTreem/Authentication service/Authentication service)`);
    }

    // Ensure a security file exists
    let securityContent = loadConfigurationFile('xtrem-security.yml', process.cwd());
    if (!securityContent) {
        securityContent = createSecurityFile(fsp.dirname(configContent.filename));
    }
    logSuccess(`Security file found at ${securityContent.filename}.`);
    securityFullFilename = securityContent.filename;

    return config;
}

/**
 * Start the application in prodUi mode.
 */
export function startInProdUiMode(): void {
    const config = checkConfiguration();

    const appPrefix = config.app == null ? '' : `${config.app}-`;

    startAuthenticationContainer(config);

    checkHostFileForWsl(appPrefix);

    console.log('\x1b[0;33m'); // change color to orange
    console.log(`
*******************************************************************************************************************************************


        The application will now start in prod-Ui mode, you will be able to navigate to http://${appPrefix}xtrem.localhost.dev-sagextrem.com:8240/



        If you no longer need to run in prod-Ui mode, simply:

        \t- remove the 'prodUi: true' line from your configuration file (${configFullFilename})

        \t- delete the file ${securityFullFilename}


*******************************************************************************************************************************************
        `);
    console.log('\x1b[0m'); // restore default color
}
