import {
    cliContext,
    ExecutionMode,
    printError,
    printInfo,
    printSuccess,
    printWarning,
    quitWithError,
} from '@sage/xtrem-cli-lib';
import { dynamicImport } from '@sage/xtrem-core';
import * as chalk from 'chalk';
import { ESLint } from 'eslint';
import * as fs from 'fs';
import { glob } from 'glob';
import * as path from 'path';
import { Options as PrettierOptions } from 'prettier';
import { getResourcesDir } from '../../utils/file';

type Prettier = { format: (source: string, options?: PrettierOptions) => Promise<string> };

const getFiles = (
    executionMode: ExecutionMode,
    dir: string,
    isClientIncluded: boolean,
    providedPath?: string,
): string[] | undefined => {
    const globOptions = { cwd: dir, absolute: true, realpath: true };
    if (providedPath) {
        const absolutePath = path.isAbsolute(providedPath) ? providedPath : path.resolve(dir, providedPath);
        const stats = fs.statSync(absolutePath);
        if (stats.isFile()) {
            if (absolutePath.endsWith('.ts')) {
                if (fs.existsSync(absolutePath)) {
                    return [absolutePath];
                }
                quitWithError(executionMode, 'File is not found.');
                return undefined;
            }
            quitWithError(executionMode, `${absolutePath} is not a TypeScript file.`);
            return undefined;
        }
    } else {
        return glob.sync(['./lib/**/*.ts', './test/**/*.ts', './test/graphql/**/parameters.json'], {
            ...globOptions,
            ignore: isClientIncluded
                ? undefined
                : ['./lib/{pages,page-extensions,page-fragments,stickers,client-functions}/**/*.{c,m,}ts'],
        });
    }
    return glob.sync(
        ['./lib/**/*.ts', './lib-generated/**/*.ts', './test/**/*.ts', './test/graphql/**/parameters.json'],
        globOptions,
    );
};

async function getEslintBaseConfig(dir: string, fix: boolean, baseConfig: string): Promise<ESLint.Options> {
    let extension = '.cjs';
    if (!fs.existsSync(`${dir}/${baseConfig}${extension}`)) {
        extension = '.js';
    }
    const baseConfigMod = await dynamicImport(`${dir}/${baseConfig}${extension}`);
    return {
        baseConfig: baseConfigMod.default ?? baseConfigMod,
        fix,
    };
}

export const lint = async (fix = false, providedPath?: string) => {
    const { executionMode, dir } = cliContext;

    const prettierConfigPath = path.resolve(getResourcesDir(), '.prettierrc');
    const prettierConfig: PrettierOptions = JSON.parse(fs.readFileSync(prettierConfigPath, 'utf-8'));

    const eslintConfig = await getEslintBaseConfig(dir, fix, '.eslintrc');
    const parserOptions = (eslintConfig as any).baseConfig.parserOptions;
    const isClientIncluded =
        parserOptions.project instanceof Array && parserOptions.project.includes('tsconfig-artifacts.json');
    const linter = new ESLint(eslintConfig);
    const fileNameLinter = new ESLint(await getEslintBaseConfig(dir, fix, '.eslintrc-filename'));

    const files = getFiles(executionMode, dir, isClientIncluded, providedPath);
    const prettierErrors: string[] = [];
    if (!files) {
        return;
    }
    const result = await linter.lintFiles(files);
    if (fix) {
        // output fixes to disk
        printInfo(executionMode, 'Output fixes to disk');
        await ESLint.outputFixes(result);
    }

    const fileNameResult = await fileNameLinter.lintFiles(files);
    // Lazy load prettier to save 500ms when the app is started
    // eslint-disable-next-line global-require
    const prettier: Prettier = require('prettier');
    await Promise.all(
        files
            .filter(s => !/\.json$/.test(s))
            .map(async f => {
                const fileContent = await fs.promises.readFile(f, 'utf-8');
                const prettySource = await prettier.format(fileContent, { ...prettierConfig, filepath: f });
                const prettierChanged = prettySource !== fileContent;
                if (fix && prettierChanged) {
                    printInfo(executionMode, `${f} has been reformatted.`);
                    await fs.promises.writeFile(f, prettySource, 'utf-8');
                } else if (prettierChanged) {
                    prettierErrors.push(f);
                }
            }),
    );

    const sumResults = [...result, ...fileNameResult];
    const lintFailed = sumResults.some(r => r.messages.some(m => m.severity === 2));

    const failed = lintFailed || prettierErrors.length > 0;

    if (failed) {
        sumResults.forEach(r => {
            r.messages
                .filter(m => m.severity === 1 && m.ruleId)
                .forEach(m => {
                    printWarning(executionMode, `${r.filePath}:${m.line}:${m.column}: ${m.message} (${m.ruleId})`);
                });
            r.messages
                .filter(m => m.severity === 2)
                .forEach(m => {
                    printError(executionMode, `${r.filePath}:${m.line}:${m.column}: ${m.message} (${m.ruleId})`);
                });
        });

        prettierErrors.forEach(f => {
            printError(executionMode, `prettier (${f})`);
        });

        if (!fix) {
            printInfo(executionMode, `Run again with ${chalk.bold('--fix')} to try to fix the issues above.`);
        }

        quitWithError(executionMode, "Some files didn't pass the linter.");
    } else {
        sumResults.forEach(r => {
            r.messages
                .filter(m => m.severity === 1 && m.ruleId)
                .forEach(m => {
                    printWarning(executionMode, `${r.filePath}:${m.line}:${m.column}: ${m.message} (${m.ruleId})`);
                });
        });
        printSuccess(executionMode, 'All good!');
        if (executionMode === ExecutionMode.STANDALONE) {
            process.exit(0);
        }
    }
};
