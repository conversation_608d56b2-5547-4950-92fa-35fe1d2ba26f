/* eslint-disable no-console */
import { copyFolderRecursive, ExecutionMode, getNpmCommandName, printInfo, quitWithError } from '@sage/xtrem-cli-lib';
import { asyncArray } from '@sage/xtrem-core';
import * as childProcess from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { getResourcesDir } from '../../utils/file';
import { getLatestVersion } from './search-packages';

const mandatoryDependencies = ['@sage/xtrem-core', '@sage/xtrem-cli', '@sage/xtrem-ui'];

export interface DependencyWithVersion {
    name: string;
    version: string;
}

export const bootstrapPackage = async (
    executionMode: ExecutionMode,
    dir: string,
    vendorName: string,
    name: string,
    dependencies: DependencyWithVersion[],
) => {
    const fixtureDir = path.resolve(getResourcesDir(), 'empty-package');
    const targetDir = path.resolve(dir, name);

    copyFolderRecursive(fixtureDir, targetDir, true);

    const targetPackageFilePath = path.resolve(targetDir, 'package.json');
    const packageFileContent = fs.readFileSync(targetPackageFilePath, 'utf-8');
    const packageContentObject = JSON.parse(
        packageFileContent.split('[VENDORNAME]').join(vendorName).split('[PACKAGENAME]').join(name),
    );

    await asyncArray(mandatoryDependencies).forEach(async d => {
        packageContentObject.dependencies[d] = `^${await getLatestVersion(d)}`;
    });

    dependencies.forEach(d => {
        packageContentObject.dependencies[d.name] = `^${d.version}`;
    });

    fs.writeFileSync(targetPackageFilePath, JSON.stringify(packageContentObject, null, 4), 'utf-8');

    printInfo(executionMode, 'Installing package...');

    return new Promise<void>((resolve, reject) => {
        const deployedProcess = childProcess.spawn(getNpmCommandName(), ['install', '--verbose'], {
            env: process.env,
            cwd: targetDir,
        });

        deployedProcess.stdout.on('data', data => {
            console.log(data.toString());
        });

        deployedProcess.stderr.on('data', data => {
            console.log(data.toString());
        });

        deployedProcess.on('close', code => {
            if (code === 0) {
                resolve();
            } else {
                quitWithError(executionMode, 'Failed to install package');
                reject();
            }
        });
    });
};
