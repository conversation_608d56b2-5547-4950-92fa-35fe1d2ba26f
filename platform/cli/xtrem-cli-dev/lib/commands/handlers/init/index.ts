import { cliContext, ExecutionMode, printSuccess, quitWithError } from '@sage/xtrem-cli-lib';
import * as fs from 'fs';
import inquirer from 'inquirer';
import * as path from 'path';
import { bootstrapPackage } from './bootstrap-package';
import { PackageInfo, searchX3Dependencies } from './search-packages';

export { bootstrapPackage, PackageInfo, searchX3Dependencies };

const kebabCaseRegex = /^([a-z][a-z0-9]*)(-[a-z0-9]+)*$/;

export const init = () => {
    const { executionMode, dir } = cliContext;
    (async () => {
        const availableDependencies = await searchX3Dependencies(executionMode);
        const prompt = inquirer.createPromptModule();
        const userInput = await prompt<{
            vendorName: string;
            name: string;
            dependencies: string[];
            database: string;
        }>([
            {
                name: 'vendorName',
                type: 'input',
                message: 'Enter your vendor name (e.g. sage)',
                default: 'sage',
                validate: value => {
                    if (kebabCaseRegex.test(value.trim())) {
                        return true;
                    }
                    return 'Please enter your vendor name in kebab case format.';
                },
            },
            {
                name: 'name',
                type: 'input',
                default: 'sample-package',
                message: 'Enter your package name (e.g. my-xtrem-package)',
                validate: value => {
                    if (value && kebabCaseRegex.test(value.trim())) {
                        if (fs.existsSync(path.resolve(dir, value))) {
                            return 'This package already exists in the current directory. Please choose another name.';
                        }
                        return true;
                    }
                    return 'Please enter your vendor name in kebab case format.';
                },
            },
            {
                name: 'dependencies',
                type: 'checkbox',
                message: 'Select your dependencies',
                choices: availableDependencies.map(d => `${d.name} v${d.version}`),
            },
            {
                name: 'database',
                type: 'list',
                message: 'Select your database driver',
                default: 'none',
                choices: ['none', 'postgres'],
            },
        ]);

        const selectedDependencies = userInput.dependencies.map(d => {
            const parts = d.split(' v');
            return { name: parts[0], version: parts[1] };
        });

        if (userInput.database !== 'none') {
            selectedDependencies.push({ name: `@sage/xtrem-${userInput.database}`, version: '1.0.0' });
        }

        try {
            await bootstrapPackage(executionMode, dir, userInput.vendorName, userInput.name, selectedDependencies);
            printSuccess(executionMode, 'Your new package is now ready to roll!');
            if (executionMode === ExecutionMode.STANDALONE) {
                process.exit();
            }
        } catch (e) {
            quitWithError(executionMode, e);
        }
    })().catch(e => {
        quitWithError(executionMode, e);
    });
};
