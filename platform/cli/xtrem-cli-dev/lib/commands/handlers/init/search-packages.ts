import { ExecutionMode, getNpmCommandName, printInfo, printWarning } from '@sage/xtrem-cli-lib';
import { AnyRecord, AnyValue } from '@sage/xtrem-core';
import * as childProcess from 'child_process';

const runNpmCommand = <T extends AnyValue>(...parameters: string[]): Promise<T> => {
    return new Promise<T>(resolve => {
        const printedLines: string[] = [];
        printInfo(ExecutionMode.STANDALONE, `Running command: pnpm ${parameters.join(' ')}`);

        const deployedProcess = childProcess.spawn(getNpmCommandName(), parameters, {
            env: process.env,
        });

        deployedProcess.stderr.on('data', data => {
            printWarning(ExecutionMode.STANDALONE, data.toString());
        });

        deployedProcess.stdout.on('data', data => {
            printedLines.push(data.toString());
        });

        deployedProcess.on('exit', () => {
            resolve(JSON.parse(printedLines.join('')));
        });
    });
};

export interface PackageInfo {
    name: string;
    description: string;
    maintainers: string;
    version: string;
    date: string;
}

export const searchX3Dependencies = async (executionMode: ExecutionMode) => {
    // BL: We can later refactor this to a simple GET call, when the packages are published publicly.
    // For now, we stick to a subprocess as we don't want to deal with the authentication protocol.
    printInfo(executionMode, 'Gathering info, wait a second...');
    const config = await runNpmCommand('config', 'list', '--json');
    const registryUrl = (config as AnyRecord)['@sage:registry'];
    if (!registryUrl) {
        throw new Error('No @sage registry config found.');
    }
    return (
        await runNpmCommand<string[]>(
            'search',
            `--registry=${registryUrl}`,
            'xtrem-application-package',
            '--searchlimit=100',
            '--quality=null',
            '--popularity=null',
            '--maintenance=null',
            '--json',
        )
    ).map((r: any) => ({ ...r, maintainers: '' }));
};

export const getLatestVersion = async (packageName: string) => {
    const result = await runNpmCommand<{ version: string }>('view', `${packageName}@latest`, '--json');
    return result.version;
};
