/* eslint-disable no-console */
/* eslint-disable global-require, import/no-dynamic-require */
import {
    cliContext,
    ExecutionMode,
    isDebugging,
    printInfo,
    printSuccess,
    printWarning,
    quitWithError,
} from '@sage/xtrem-cli-lib';
import { ConfigManager, Dict } from '@sage/xtrem-core';
import { defaultStringCompare } from '@sage/xtrem-shared';
import * as childProcess from 'child_process';
import * as fs from 'fs';
import { glob } from 'glob';
import * as path from 'path';
import { TestOptions } from '../../utils';
import { createMochaRunner, runMochaTests } from '../../utils/mocha';

export const resolvePackageBin = (packageName: string) => {
    const jsonPath = require.resolve(`${packageName}/package.json`, { paths: [__dirname] });
    const packageRootDir = path.dirname(jsonPath);
    const packageFileContent = require(jsonPath);
    const executableRelativePath = packageFileContent.bin[packageName] || packageFileContent.bin;
    return path.resolve(packageRootDir, executableRelativePath);
};
/**
 * Execute Mocha unit tests
 *
 * TODO:
 * - Code instrumentation and coverage collection (Probably with an `after` transformer)
 *
 * @param executionMode
 * @param dir
 * @param isCi
 * @param pattern
 */
export const executeUnitTest = async (executionMode: ExecutionMode, dir: string) => {
    printInfo(executionMode, 'Running unit tests...');

    const { ci: isCi, pattern, serviceOptions } = cliContext.options as TestOptions;

    const packageFile = require(path.resolve(dir, 'package.json'));
    const localizedPattern = isCi || !pattern ? 'build/test/mocha/**/*' : `build/test/mocha/**/${pattern}*`;
    const graphqlTests = glob
        .sync(`./test/graphql/${pattern}*/`, { cwd: dir, absolute: true, realpath: true })
        .sort(defaultStringCompare);

    const files = glob
        .sync(`${dir}/${localizedPattern}`)
        .filter(f => f.endsWith('.js'))
        // additional checks to skip directories that do not have any real tests
        // exclude sample-test.ts
        .filter(f => !f.endsWith('sample-test.js'))
        // exclude ts files that do not contain describe
        .filter(f => fs.readFileSync(f, 'utf8').includes('describe'))
        .sort(defaultStringCompare);

    if ((!files || files.length === 0) && (!graphqlTests || graphqlTests.length === 0)) {
        printWarning(executionMode, 'No unit test files were found.');
        return;
    }

    if (isCi) {
        const graphqlEntryPointPath = path.join(__dirname, '../../utils/graphql-tests-entry-point');
        try {
            const hasPassed = await new Promise<boolean>(resolve => {
                let commandParts = [
                    resolvePackageBin('c8'),
                    '--include',
                    'build/lib/**/*.js',
                    '--extension',
                    '.js',
                    '--extension',
                    '.ts',
                ];

                // Add exclusions:
                [
                    ...[
                        'build/lib/{client-functions,pages,page-extensions,page-fragments,stickers,upgrades,widgets}/**/*.js',
                    ],
                    ...(packageFile?.c8?.exclude || []),
                ].forEach(exclude => {
                    commandParts.push('--exclude');
                    commandParts.push(exclude);
                });

                commandParts = [
                    ...commandParts,
                    ...[
                        // see https://stackoverflow.com/questions/44701991/getting-nyc-istanbul-coverage-report-to-work-with-typescript
                        '--exclude-after-remap',
                        'false',
                        '--reporter',
                        'html',
                        '--reporter',
                        'text',
                        '--reporter',
                        'json',
                        '--reporter',
                        'cobertura',
                        resolvePackageBin('mocha'),
                        '--max-old-space-size=3000',
                        '--exit',
                        '--ui',
                        require.resolve('./xtrem-interface'),
                        '--timeout',
                        '1200000',
                        '--require',
                        'source-map-support/register',
                        '--reporter',
                        'mocha-multi',
                        '--reporter-options',
                        'spec=-,mocha-junit-reporter=-',
                        '--recursive',
                        `${graphqlEntryPointPath}.js`,
                        `${localizedPattern}.js`,
                        `${serviceOptions ? `--service-options=${serviceOptions}` : ''}`,
                    ],
                ];

                const env = {
                    ...process.env,
                    MOCHA_FILE: path.resolve(
                        dir,
                        `junit-report-${packageFile.name.replace('/', '-').replace('@', '')}.xml`,
                    ),
                    XTREM_TEST_OPTIONS: JSON.stringify(cliContext.options),
                } as Dict<string>;
                // In Codespaces, for some reason the NODE_ENV can have the value 'undefined' instead of not being defined
                if (env.NODE_ENV === 'undefined') {
                    delete env.NODE_ENV;
                }
                const testProcess = childProcess.spawn('node', commandParts, {
                    env,
                    cwd: dir,
                    stdio: 'inherit',
                });

                testProcess.on('close', code => {
                    resolve(code === 0);
                });
            });

            if (hasPassed) {
                printSuccess(executionMode, 'Unit tests passed.');
            } else {
                quitWithError(executionMode, 'Unit tests failed.');
            }
        } catch (e) {
            console.log(e);
            printWarning(executionMode, e);
            quitWithError(executionMode, 'Unit tests failed.');
        }
    } else {
        if (isDebugging) {
            printWarning(executionMode, 'Running in debug mode, timeout is disabled.');
        }

        try {
            const mocha = createMochaRunner(executionMode, dir, isCi);

            // add a first test that only contain the setupTestSuite() call to initialize the test application
            // for this mocha runner
            const unitTestsEntryPointPath = path.join(__dirname, '../../utils/unit-tests-entry-point');
            mocha.addFile(unitTestsEntryPointPath);
            files.forEach(file => {
                mocha.addFile(file);
            });

            // Do not output the list of tests if logs are disabled
            if (!ConfigManager.current.logs?.disabled) {
                const filesExecuted = `The following unit test files will be executed:\n${[...graphqlTests, ...files]
                    .map(f => ` - ${f.replace(dir + path.sep, '')}`)
                    .join('\n')}`;

                printInfo(executionMode, filesExecuted);
            }

            const hasPassed = await runMochaTests(mocha, packageFile.type === 'module');
            if (hasPassed) {
                printSuccess(executionMode, 'Unit tests passed.');
            } else {
                quitWithError(executionMode, 'Unit tests failed.');
            }
        } catch (e) {
            console.log(e);
            printWarning(executionMode, e);
            quitWithError(executionMode, 'Unit tests failed.');
        }
    }
};
