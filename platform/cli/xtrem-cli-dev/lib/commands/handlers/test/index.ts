import { compileServer } from '@sage/xtrem-cli-compile';
import { cliContext, ExecutionMode, quitWithError } from '@sage/xtrem-cli-lib';
import { Application, ConfigManager, Test } from '@sage/xtrem-core';
import * as chalk from 'chalk';
import * as path from 'path';
import { isRemoteTargetUrl, TestOptions } from '../../utils';
import { executeGraphqlTests } from './graphql-tests';
import { executeUnitTest } from './unit-tests';
import { executeWorkflowTests } from './workflow-tests';

export { executeGraphqlTests } from './graphql-tests';

export const test = async (options: TestOptions) => {
    const { executionMode, dir } = cliContext;
    try {
        const {
            unit: executingUnitTests,
            integration: executingIntegrationTests,
            graphql: executingGraphqlTests,
            workflow: executingWorkflowTests,
            ci: isCi,
            pattern,
            skipServerCompile,
        } = options;

        let newDir;
        newDir = Test.getDirectory(dir, 'package.json');
        if (newDir === '') {
            newDir = Test.getDirectory(path.join(dir, '../'), 'package.json');
        }
        if (newDir === '') {
            newDir = Test.getDirectory(path.join(dir, '../../'), 'package.json');
        }

        const absoluteDir = path.resolve(newDir);

        if (!executingUnitTests && !executingIntegrationTests && !executingGraphqlTests && !executingWorkflowTests) {
            quitWithError(
                executionMode,
                'Please use at least one of the --unit, --integration, --graphql or --workflow test switches.',
            );
        }

        if (!ConfigManager.current.storage?.managedExternal && !isRemoteTargetUrl()) {
            const schemaName = Test.getTestSchemaName(absoluteDir);
            await Application.dropDbSchema(schemaName);
            await Application.createDbSchema(schemaName);
        }

        // TODO: skip this if files have not changed
        if (!options.integration && !isCi && !skipServerCompile) {
            // do not clobber routing.json file which was generated by build and which may aggregate other routing.json files
            compileServer(executionMode, newDir, { doNotGenerateRoutingFile: true });
        }

        if (executingUnitTests) {
            await executeUnitTest(executionMode, newDir);
        }

        if (executingGraphqlTests) {
            await executeGraphqlTests(executionMode, newDir, isCi, pattern);
        }

        if (executingWorkflowTests) {
            await executeWorkflowTests(executionMode, newDir, isCi, pattern);
        }

        if (executionMode === ExecutionMode.STANDALONE) {
            process.exit();
        }
    } catch (error) {
        quitWithError(executionMode, error);
    }
};

export const getHelp = (): string => `${chalk.bold.blue(
    'test [pattern] [--unit] [--integration] [--ci] [--graphql] [--browser]',
)}
    Executes tests.
        pattern:            Optional. Pattern which is used to filter the test files.
        --integration:      Executes the BDD Cucumber integration tests of the current package.
        --unit:             Executes the Mocha unit tests of the package in an f-stream context.
        --ci:               Provides CI context functionality: JUnit reports and code coverage support
        --graphql:          Executes the Graphql tests.
        --workflow:         Executes the Workflow tests.
        --browser:          Makes the browser visible when running the integration tests (not consider when using --ci flag).
        --service-options:  service options that should be active for unit and integration tests.
`;
