/* eslint-disable global-require, import/no-dynamic-require */
import { ExecutionMode, isDebugging, printInfo, printSuccess, printWarning, quitWithError } from '@sage/xtrem-cli-lib';
import * as path from 'path';
import { createMochaRunner, runMochaTests } from '../../utils/mocha';
/**
 * Execute Graphql unit tests
 *
 * @param executionMode STANDALONE or INTEGRATED
 * @param dir the em-core package directory
 * @param isCi wether tests are meant to be executed in ci mode or not
 * @param pattern a string that matches a single folder under <em-core-package>/test/graphql
 *              and temporary <em-core-package>/test/fixtures/graphql
 */
export const executeGraphqlTests = async (
    executionMode: ExecutionMode,
    dir: string,
    isCi: boolean,
    pattern: string = '',
) => {
    let transformedPattern = pattern;
    if (pattern !== '') {
        transformedPattern = pattern.split(path.sep).pop() || '';
        process.env.GRAPHQL_TEST_PATTERN = transformedPattern;
    }
    const isSingleTest = transformedPattern !== '';
    printInfo(
        executionMode,
        isSingleTest ? `Looking for '${transformedPattern}' Graphql test...` : 'Looking for Graphql tests...',
    );

    if (isDebugging) {
        printWarning(executionMode, 'Running in debug mode, timeout is disabled.');
    }

    const mocha = createMochaRunner(executionMode, dir, isCi);
    // This will be the entry-point for Mocha, which will execute the required test/tests according to the given path
    const entryPointPath = path.join(__dirname, '../../utils/graphql-tests-entry-point');
    // Clear require cache between tests
    delete require.cache[require.resolve(entryPointPath)];
    mocha.addFile(entryPointPath);
    printInfo(
        executionMode,
        isSingleTest ? `Executing the '${transformedPattern}' Graphql test...` : 'Executing all Graphql tests..',
    );

    try {
        const packageFile = require(path.resolve(dir, 'package.json'));
        const hasPassed = await runMochaTests(mocha, packageFile.type === 'module');
        if (hasPassed) {
            printSuccess(executionMode, 'All good.', false);
        } else {
            quitWithError(
                executionMode,
                isSingleTest
                    ? `'${transformedPattern}' test has failed. All errors are displayed above.`
                    : 'One or more Graphql tests have failed. All errors are displayed above.',
            );
        }
    } catch (e) {
        printWarning(executionMode, e === 1 ? `${e} failure.` : `${e} failures.`);
        quitWithError(
            executionMode,
            isSingleTest
                ? `'${transformedPattern}' test has failed. All errors are displayed above.`
                : 'One or more Graphql tests have failed. All errors are displayed above.',
        );
    }
};
