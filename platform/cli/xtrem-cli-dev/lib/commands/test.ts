import { cliContext, pluggableBuilder, pluggableHandler, printWarning } from '@sage/xtrem-cli-lib';
import type { Argv } from 'yargs';
import { TestOptions } from './utils';

export const command = ['test [pattern]', 't'];
export const desc = 'Executes tests';

export const builder = (yargs: Argv) =>
    pluggableBuilder(
        command,
        yargs
            .version(false)
            .positional('pattern', {
                desc: 'Optional. Pattern which is used to filter the test files',
                type: 'string',
            })
            .option('unit', {
                desc: 'Executes the Mocha unit tests of the package in an f-stream context',
                default: false,
                type: 'boolean',
            })
            .option('ci', {
                desc: 'Provides CI context functionality: JUnit reports and code coverage support',
                default: false,
                type: 'boolean',
            })
            .option('skip-server-compile', {
                desc: 'Skip server copmile',
                default: false,
                type: 'boolean',
            })
            .option('graphql', {
                desc: 'Executes the Graphql tests',
                default: false,
                type: 'boolean',
            })
            .option('workflow', {
                desc: 'Executes the workflow tests',
                default: false,
                type: 'boolean',
            })
            .option('service-options', {
                desc: 'service options that should be active for unit and integration tests',
                type: 'string',
            })
            .example('$0 test', ''),
    );

export const handler = async (argv: object) => {
    const pluggableResult = await pluggableHandler(command, argv);
    if (!pluggableResult.skipDefault) {
        cliContext.options = argv;
        // Lazy loading of handlers to speed up the CLI loading
        // eslint-disable-next-line global-require
        return require('./handlers/test').test(argv as TestOptions);
    }
    return printWarning(cliContext.executionMode, 'skip default main test command');
};
