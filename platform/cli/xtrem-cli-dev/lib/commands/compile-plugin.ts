import type { Argv } from 'yargs';

export const command = ['compile-plugin', 'build-plugin'];
export const desc = 'Compiles an Xtrem UI Plugin';

export const builder = (yargs: Argv) => yargs.version(false).example('$0 compile-plugin', 'Compile the UI plugin');

export const handler = () =>
    (() => {
        // Lazy loading of handlers to speed up the CLI loading
        // eslint-disable-next-line global-require
        require('@sage/xtrem-cli-compile').compilePlugin();
    })();
