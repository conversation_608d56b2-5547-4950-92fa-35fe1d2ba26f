import { CompileOptions } from '@sage/xtrem-cli-compile';
import type { Argv } from 'yargs';

export const command = ['compile', 'c', 'build', 'prepare'];
export const desc = 'Creates a ready-to-use Xtrem package';

// NOTE: see later to not have to use the 'is-*' aliases in the list of options below

export const builder = (yargs: Argv) =>
    yargs
        .version(false)
        .option('skip-client', {
            desc: 'Skips the compilation of client side artifacts',
        })
        .option('skip-cop', {
            desc: 'Skips code verification',
        })
        .option('skip-server', {
            desc: 'Skips the compilation of server side artifacts',
        })
        .option('skip-dts', {
            desc: 'Skips the creation of a single package TS declaration file',
        })
        .option('skip-api-client', {
            desc: 'Skips the generation of a single package API reference TS declaration package',
        })
        .option('only-api-client', {
            desc: 'Generations a single package API reference TS declaration package without building the rest',
            type: 'boolean',
        })
        .option('only-changed', {
            desc: 'Compiles only changed client artifacts, prevents the build folder from being cleared',
            type: 'boolean',
        })
        .option('skip-table-schema', {
            desc: 'Skips the generation of table schema declaration JSON files',
        })
        .option('skip-clean', {
            desc: 'Skips removal of existing build output',
        })
        .option('force', {
            desc: 'Force a full build',
        })
        .option('references', {
            desc: "Compiles all project's references",
            type: 'boolean',
        })
        .option('binary', {
            desc: 'Compiles the server side artifacts into a closed-source binary format',
            type: 'boolean',
        })
        .option('instrumented', {
            desc: 'Instruments the compiled codebase',
            type: 'boolean',
        })
        .option('prod', {
            desc: 'TBA',
            type: 'boolean',
        })
        .option('fast', {
            desc: 'Faster build of client artifacts, it can be useful for large packages',
        })
        .middleware((argv: any) => {
            // create internal aliases to be used by the command options
            argv.isOnlyApi = argv.onlyApiClient;
            argv.isOnlyChanged = argv.onlyChanged;
            argv.isUsingReferences = argv.references;
            argv.isBinary = argv.binary;
            argv.isInstrumented = argv.instrumented;
            argv.isProd = argv.prod;
            return argv;
        })
        .example('$0 compile', 'Compile the current package');

export const handler = (argv: any) =>
    // Lazy loading of handlers to speed up the CLI loading
    // eslint-disable-next-line global-require
    require('@sage/xtrem-cli-compile').compile(argv as CompileOptions) as Promise<void>;
