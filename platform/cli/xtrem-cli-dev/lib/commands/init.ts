import type { Argv } from 'yargs';

export const command = ['init', 'i', 'create'];
export const desc = 'Creates an empty xtrem package. It prompts for installation details';

export const builder = (yargs: Argv) => yargs.version(false).example('$0 init', 'Initialize a new package');

export const handler = () =>
    // Lazy loading of handlers to speed up the CLI loading
    // eslint-disable-next-line global-require
    require('./handlers/init').init();
