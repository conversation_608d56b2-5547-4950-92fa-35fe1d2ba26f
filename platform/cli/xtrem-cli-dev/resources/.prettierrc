{"singleQuote": true, "printWidth": 120, "tabWidth": 4, "useTabs": false, "semi": true, "trailingComma": "all", "arrowParens": "avoid", "endOfLine": "lf", "overrides": [{"files": ["*.json", ".prettier<PERSON>"], "options": {"parser": "json"}}, {"files": "*.ts", "options": {"parser": "typescript"}}, {"files": ["*.yml", "*.yaml"], "options": {"parser": "yaml", "tabWidth": 2}}, {"files": ["*.graphql.hbs", "*.json.hbs"], "options": {"parser": "graphql", "tabWidth": 2}}]}