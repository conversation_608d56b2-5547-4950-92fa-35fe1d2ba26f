import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<SamplePage>({
    authorizationCode: 'ANYCODE',
    module: 'any-module',
    title: 'Sample Page',
    isTransient: true,
})
export class SamplePage extends ui.Page {
    @ui.decorators.section<SamplePage>({
        title: 'Section',
    })
    sampleSection: ui.containers.Section;

    @ui.decorators.block<SamplePage>({
        parent() {
            return this.sampleSection;
        },
        title: 'Block',
    })
    sampleBlock: ui.containers.Block;

    @ui.decorators.textField<SamplePage>({
        parent() {
            return this.sampleBlock;
        },
        title: 'Just an empty field',
        helperText: 'This is just an empty field.',
    })
    sampleField: ui.fields.Text;
}
