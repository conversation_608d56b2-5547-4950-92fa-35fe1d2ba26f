{"compilerOptions": {"module": "CommonJS", "moduleResolution": "Node", "target": "es2022", "outDir": "build", "rootDir": ".", "baseUrl": ".", "skipLibCheck": true, "skipDefaultLibCheck": true, "noUnusedLocals": true, "noImplicitAny": true, "noImplicitThis": true, "strictNullChecks": true, "useDefineForClassFields": false, "experimentalDecorators": true}, "include": ["index.ts", "lib/**/*", "api/api.d.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png"]}