if (process.env.SKIP_CLIENT === '1') {
    console.warn('Skipping tests on SKIP_CLIENT env variable.');
    process.exit(0);
}

module.exports = {
    testTimeout: 20000,
    roots: ['<rootDir>/lib'],
    moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node', 'd.ts'],
    transform: {
        '^.+\\.tsx?$': [
            'ts-jest',
            {
                tsconfig: 'tsconfig.test.json',
            },
        ],
    },
    testMatch: ['<rootDir>/**/__tests__/**/*test.ts?(x)'],
    setupFilesAfterEnv: ['<rootDir>/lib/__tests__/setup-after-env.ts'],
    testEnvironment: '<rootDir>/lib/__tests__/environment.ts',
    globalSetup: '<rootDir>/lib/__tests__/global-setup.ts',
    globalTeardown: '<rootDir>/lib/__tests__/global-teardown.ts',
    verbose: false,
    reporters: ['default', 'jest-junit'],
    coverageProvider: 'v8',
    collectCoverageFrom: ['<rootDir>/lib/**/*.{ts,tsx}'],
    coverageReporters: ['json', 'lcov', 'text', 'clover', 'cobertura'],
    testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/coverage/', '<rootDir>/webpack/'],
    moduleDirectories: ['node_modules'],
    coveragePathIgnorePatterns: [
        '.*__tests__.*',
        '<rootDir>/node_modules/',
        '<rootDir>/coverage/',
        '<rootDir>/webpack/',
        '<rootDir>/lib/consumer-mock',
    ],
    transformIgnorePatterns: [
        '<rootDir>/node_modules/',
        '<rootDir>/coverage/',
        '<rootDir>/junit/',
        '<rootDir>/webpack/',
        'node_modules/(?!(carbon-react)/)',
    ],
    moduleNameMapper: {
        '\\.(css|scss|svg)$': 'identity-obj-proxy',
        '^d3-time-format$': 'd3-time-format/dist/d3-time-format.js',
        uuid: 'uuid/dist/index.js',
        'carbon-react/esm/(.*)': 'carbon-react/lib/$1',
    },
    snapshotFormat: {
        escapeString: true,
        printBasicPrototype: true,
    },
};
