'use strict';

const Webpack = require('webpack');
const path = require('path');
const merge = require('webpack-merge');
const TerserPlugin = require('terser-webpack-plugin');
const base = require('./base');
const rootDir = path.resolve(__dirname, '..');
const xtremi18n = require('@sage/xtrem-cli-transformers');

module.exports = merge.merge(base(), {
    devtool: false,
    mode: 'production',
    plugins: [
        new Webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('production'),
            DEV_MODE: false,
        }),
        {
            apply: compiler => {
                compiler.hooks.afterEmit.tap('AfterEmitPlugin', () => {
                    xtremi18n.mergeTranslationFiles(rootDir);
                });
            },
        },
    ],
    optimization: {
        minimize: true,
        minimizer: [
            new TerserPlugin({
                terserOptions: {
                    keep_classnames: true,
                    keep_fnames: true,
                },
            }),
        ],
    },
});
