'use strict';

const Webpack = require('webpack');
const merge = require('webpack-merge');
const base = require('./base');

module.exports = merge.merge(base(), {
    devtool: 'source-map',
    mode: 'development',
    target: 'web',
    plugins: [
        new Webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('development'),
        }),
    ],
    optimization: {
        minimize: false,
        moduleIds: 'deterministic',
    },
});
