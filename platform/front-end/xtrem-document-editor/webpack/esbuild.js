'use strict';

const Webpack = require('webpack');
const WebpackNotifierPlugin = require('webpack-notifier');
const merge = require('webpack-merge');
const base = require('./base');
const path = require('path');
const rootDir = path.resolve(__dirname, '..');
const buildDir = path.resolve(rootDir, 'build');

module.exports = merge.merge(base('esbuild'), {
    devtool: 'source-map',
    mode: 'development',
    target: 'web',
    devServer: {
        static: {
            directory: buildDir,
            watch: {
                ignored: /(i18n|node_modules)/,
            },
            publicPath: '/',
        },
        compress: true,
        port: 4000,
        host: '0.0.0.0',
    },
    cache: {
        type: 'filesystem',
        cacheDirectory: path.resolve(process.cwd(), '.temp_cache'),
    },
    plugins: [
        new WebpackNotifierPlugin({
            skipFirstNotification: true,
            alwaysNotify: true,
            excludeWarnings: true,
            sound: false,
        }),
        new Webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('development'),
        }),
    ],
    optimization: {
        minimize: false,
        moduleIds: 'deterministic',
    },
});
