import type PropertyPlugin from './plugins/property';
import type QueryTablePlugin from './plugins/query-table';
import type RecordContextPlugin from './plugins/record-context';
import type { ReportEditorConfig } from './report-editor-config-type';

declare module '@ckeditor/ckeditor5-core' {
    interface EditorConfig {
        reportEditorConfig?: ReportEditorConfig;
    }

    interface PluginsMap {
        [QueryTablePlugin.pluginName]: QueryTablePlugin;
        [RecordContextPlugin.pluginName]: RecordContextPlugin;
        [PropertyPlugin.pluginName]: PropertyPlugin;
    }
    interface CommandsMap {}
}
