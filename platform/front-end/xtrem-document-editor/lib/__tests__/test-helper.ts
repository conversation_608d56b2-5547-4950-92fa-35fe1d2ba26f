import type { Dict, TreeElement } from '@sage/xtrem-shared';
import type { Order } from '@sage/xtrem-ui-components';
import { isNil } from 'lodash';
import type * as puppeteer from 'puppeteer';
import format from 'xml-formatter';
import { COMMAND_INSERT_QUERY_TABLE } from '../constants';
import type { DataModelProperty } from '../context-provider';
import { GLOBAL_KEY_PAGE } from './test-constants';

export function selectEditorRoot(root: 'header' | 'body' | 'footer' = 'body'): Promise<void> {
    const page = getPage();
    return page.click(`#${root}`);
}

export function getPage(): puppeteer.Page {
    return globalThis[GLOBAL_KEY_PAGE] as puppeteer.Page;
}

export async function wait(ms: number): Promise<void> {
    return new Promise<void>(resolve => setTimeout(resolve, ms));
}

export async function setValue(value: string, root: 'header' | 'body' | 'footer' = 'body'): Promise<void> {
    const page = getPage();

    // eslint-disable-next-line prefer-template
    await page.evaluate('window.EDITOR.data.set({ ' + root + ': `' + value + '` })');
}
export async function getValue(root: 'header' | 'body' | 'footer' = 'body'): Promise<string> {
    const page = getPage();
    const value = (await page.evaluate(`window.EDITOR.getFullData()`)) as Promise<string>;
    return format(`<div>${value[root] || ''}</div>`, { indentation: '  ', lineSeparator: '\n' });
}

export async function getTextContent(selector: string): Promise<string> {
    const page = getPage();
    const element = await page.$(selector);
    if (element === null) {
        throw new Error(`Element not found: ${selector}`);
    }
    return ((await element.evaluate(el => el.textContent)) ?? '').trim();
}

export async function printDom(element: string | puppeteer.ElementHandle<Element>): Promise<void> {
    const page = getPage();

    if (typeof element === 'string') {
        const targetElement = await page.$(element);
        if (targetElement === null) {
            throw new Error(`Element not found: ${element}`);
        }
        // eslint-disable-next-line no-console
        console.log(await targetElement.asElement()?.evaluate(node => (node as any).innerHTML));
    } else {
        // eslint-disable-next-line no-console
        console.log(await element.asElement()?.evaluate(node => (node as any).innerHTML));
    }
}
export async function getElementValue(element: string | puppeteer.ElementHandle<HTMLInputElement>): Promise<string> {
    const page = getPage();
    let targetElement: puppeteer.ElementHandle<HTMLInputElement>;
    if (typeof element === 'string') {
        targetElement = (await page.$(element)) as puppeteer.ElementHandle<HTMLInputElement>;
    } else {
        targetElement = element;
    }

    return (await targetElement.getProperty('value')).jsonValue();
}

export async function getEditorHtmlContent(root: 'header' | 'body' | 'footer' = 'body'): Promise<string> {
    const page = getPage();
    const editor = await page.$(`.ck.ck-content.ck-editor__editable#${root}`);
    const asElement = editor?.asElement();
    if (isNil(asElement)) {
        throw new Error(`Element not found: .ck.ck-content.ck-editor__editable#${root}`);
    }
    return asElement.evaluate(node => (node as any).innerHTML);
}

export async function waitForSelectorToDisappear(selector: string): Promise<void> {
    const page = getPage();
    try {
        await page.waitForFunction(selector => !window.document.querySelector(selector), { timeout: 5000 }, selector);
    } catch (err: any) {
        throw new Error(`Selector doesn't disappear: ${selector}\n${err.message}`);
    }
}
export async function waitForSelectorToAppear(selector: string): Promise<void> {
    const page = getPage();
    try {
        await page.waitForSelector(selector, { timeout: 15000 });
    } catch (err: any) {
        await printDom('body');
        throw new Error(`Selector doesn't appear: ${selector}\n${err.message}`);
    }
}

export async function openRightPanel(): Promise<void> {
    const buttonSelector = '[data-cke-tooltip-text="Show/Hide formatting panel"]';
    const page = getPage();
    await waitForSelectorToAppear(buttonSelector);
    await page.click(buttonSelector);
    await waitForSelectorToAppear('.document-editor-right-panel');
}

export async function closeLeftPanel(): Promise<void> {
    const buttonSelector = '[data-cke-tooltip-text="Show/Hide fields panel"]';
    const page = getPage();
    await waitForSelectorToAppear(buttonSelector);
    await page.click(buttonSelector);
    await waitForSelectorToDisappear('.document-editor-left-panel');
}

export async function openDataPropertiesPanel(): Promise<void> {
    const buttonSelector = '[data-cke-tooltip-text="Show/Hide fields panel"]';
    const page = getPage();
    await waitForSelectorToAppear(buttonSelector);
    await page.click(buttonSelector);
    await waitForSelectorToAppear('.document-editor-left-panel');
}

export async function openGlobalPropertiesPanel(): Promise<void> {
    const buttonSelector = '[data-cke-tooltip-text="Show/Hide document properties panel"]';
    const page = getPage();
    await waitForSelectorToAppear(buttonSelector);
    await page.click(buttonSelector);
    await waitForSelectorToAppear('.document-editor-left-panel');
}

export async function closeRightPanel(): Promise<void> {
    const buttonSelector = '[data-cke-tooltip-text="Show/Hide formatting panel"]';
    const page = getPage();
    await waitForSelectorToAppear(buttonSelector);
    await page.click(buttonSelector);
    await waitForSelectorToDisappear('.document-editor-right-panel');
}

export async function getByTestId<ElementType extends Element>(
    testId: string,
): Promise<puppeteer.ElementHandle<ElementType>> {
    const page = getPage();
    const selector = `[data-testid="${testId}"]`;
    await waitForSelectorToAppear(selector);
    return page.$(selector) as Promise<puppeteer.ElementHandle<ElementType>>;
}

export async function getStyleProperty(
    element: puppeteer.ElementHandle<HTMLElement>,
    styleProperty: string,
): Promise<string | null> {
    return element.evaluate((node, p) => {
        return node.style.getPropertyValue(p) || null;
    }, styleProperty);
}

export async function executeEditorCommand(commandName: string, commandValue?: any): Promise<void> {
    const page = getPage();
    await page.evaluate(
        (cn: string, cv: any) => {
            const editor = (window as any).EDITOR;
            editor.execute(cn, cv);
        },
        commandName,
        commandValue,
    );
}

export async function isEditorCommandEnabled(commandName: string): Promise<boolean> {
    const page = getPage();

    return page.evaluate((cn: string): boolean => {
        const editor = (window as any).EDITOR;
        return editor.commands.get(cn).isEnabled;
    }, commandName);
}

export async function insertSimpleQueryTable({ orderBy }: { orderBy?: Dict<Order> } = {}): Promise<void> {
    const selectedItem: TreeElement<DataModelProperty> = {
        data: {
            name: 'salesInvoice',
            label: 'Sales Invoice',
            kind: 'LIST',
            type: 'SalesInvoice',
            canFilter: false,
            canSort: false,
            namespace: 'xtremSales',
            iconType: 'csv',
            isCustom: false,
            isOnInputType: true,
            isOnOutputType: true,
        } as any,
        id: 'salesInvoice',
        key: 'salesInvoice',
        labelKey: 'Sales Invoice',
        labelPath: 'Sales Invoice',
        label: 'Sales Invoice',
    };
    await executeEditorCommand(COMMAND_INSERT_QUERY_TABLE, {
        lookupResult: {
            filters: [],
            orderBy,
            selectedMode: 'list',
            selectedFields: {
                orderDate: { name: 'orderDate', label: 'Order Date', kind: 'SCALAR', type: 'Date' },
                totalWithoutTax: { name: 'totalWithoutTax', label: 'Net Total', kind: 'SCALAR', type: 'Float' },
                invoiceNumber: { name: 'invoiceNumber', label: 'Invoice Number', kind: 'SCALAR', type: 'String' },
                totalWithTax: { name: 'totalWithTax', label: 'Gross Total', kind: 'SCALAR', type: 'Float' },
            },
        },
        selectedItem,
        remapInfo: { path: 'xtremSales.salesInvoice.query.edges' },
    });

    await waitForSelectorToAppear('.query-table');
}

/**
 * Captures the editor value, sets a temporary value and the recovers the original value.
 * This is useful to check the downcast and upcast configuration
 */
export const restoreEditorValue = async (): Promise<void> => {
    const originalValue = await getValue();

    // We unset the value here
    await setValue('<div>EMPTY</div>');

    await expect(getValue()).resolves.toContain('EMPTY');

    // Recover the value with table and then expect it to be parsed to the same model
    await setValue(originalValue);
};
