import express from 'express';
import { mkdir, writeFile } from 'fs/promises';
import type * as http from 'http';
import * as path from 'path';
import * as puppeteer from 'puppeteer';
import { rimraf } from 'rimraf';
import {
    COVERAGE_TEMP_DIR,
    GLOBAL_KEY_BROWSER,
    GLOBAL_KEY_SERVER,
    TEMP_DIR,
    TEST_APP_PORT,
    WS_ENDPOINT_FILE,
} from './test-constants';

export default async function TestSetup(): Promise<void> {
    // The test can only execute if the project was previously build with `pnpm run build` because we can serve the consumer mock to puppeteer
    const server = await new Promise<http.Server>(resolve => {
        const app = express();
        const rootPath = path.resolve(process.cwd(), 'build');
        app.use('/', express.static(rootPath));
        const s = app.listen(TEST_APP_PORT, () => {
            resolve(s);
        });
    });

    const browser = await puppeteer.launch({
        headless: true,
        args: [
            '--disable-gpu',
            '--full-memory-crash-report',
            '--unlimited-storage',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--window-size=1920,1080',
        ],
    });

    globalThis[GLOBAL_KEY_BROWSER] = browser;
    globalThis[GLOBAL_KEY_SERVER] = server;

    await rimraf.rimraf(TEMP_DIR);
    await mkdir(TEMP_DIR, { recursive: true });
    await mkdir(COVERAGE_TEMP_DIR, { recursive: true });
    // use the file system to expose the wsEndpoint for TestEnvironments
    await writeFile(WS_ENDPOINT_FILE, browser.wsEndpoint());
}
