import * as path from 'path';
import * as os from 'os';

export const TEST_APP_PORT = 5678;
export const TEST_APP_URL = `http://localhost:${TEST_APP_PORT}/`;
export const TEMP_DIR = path.join(os.tmpdir(), 'jest_puppeteer_global_setup');
export const WS_ENDPOINT_FILE = path.join(TEMP_DIR, 'wsEndpoint');
export const COVERAGE_TEMP_DIR = path.join(TEMP_DIR, 'coverage');
export const GLOBAL_KEY_PAGE = '__PAGE_GLOBAL__';
export const GLOBAL_KEY_BROWSER = '__BROWSER_GLOBAL__';
export const GLOBAL_KEY_SERVER = '__SERVER_GLOBAL__';
