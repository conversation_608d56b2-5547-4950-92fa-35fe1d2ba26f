import { readFile, writeFile } from 'fs/promises';
import NodeEnvironment from 'jest-environment-node';
import * as path from 'path';
import * as puppeteer from 'puppeteer';
import uid from 'uid';
import type { Context } from 'vm';
import { COVERAGE_TEMP_DIR, GLOBAL_KEY_BROWSER, GLOBAL_KEY_PAGE, WS_ENDPOINT_FILE } from './test-constants';

export default class PuppeteerEnvironment extends NodeEnvironment {
    async setup(): Promise<void> {
        await super.setup();
        // get the wsEndpoint
        const wsEndpoint = await readFile(WS_ENDPOINT_FILE, 'utf8');
        if (!wsEndpoint) {
            throw new Error('wsEndpoint not found');
        }

        // connect to puppeteer
        const browser = await puppeteer.connect({
            browserWSEndpoint: wsEndpoint,
        });

        const page = await browser.newPage();
        await page.coverage.startJSCoverage({ resetOnNavigation: false, includeRawScriptCoverage: true });

        this.global[GLOBAL_KEY_BROWSER] = browser;
        this.global[GLOBAL_KEY_PAGE] = page;
    }

    async teardown(): Promise<void> {
        const browser = this.global[GLOBAL_KEY_BROWSER] as puppeteer.Browser;
        const page = this.global[GLOBAL_KEY_PAGE] as puppeteer.Page;

        if (page) {
            // We need to write the suite coverage data into a temp dir because the suite doesn't run on the same process as the global teardown where the results are summarised
            const coverageDetails = await page.coverage.stopJSCoverage();
            await writeFile(path.resolve(COVERAGE_TEMP_DIR, uid(6)), JSON.stringify(coverageDetails), 'utf8');
            await page.close();
        }

        if (browser) {
            browser.disconnect();
        }
        await super.teardown();
    }

    getVmContext(): Context | null {
        return super.getVmContext();
    }
}
