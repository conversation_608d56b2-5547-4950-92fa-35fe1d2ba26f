import { TEST_APP_URL } from './test-constants';
import { getPage, selectEditorRoot } from './test-helper';

beforeEach(async () => {
    const page = getPage();
    page.emulate({
        viewport: {
            width: 1792,
            height: 1024,
            deviceScaleFactor: 1,
            isMobile: false,
            hasTouch: false,
            isLandscape: true,
        },
        userAgent:
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36',
    });
    await page.goto(TEST_APP_URL);
    let loaded = false;
    while (!loaded) {
        loaded = (await page.evaluate('!!window.EDITOR;')) as boolean;
    }

    // Close CK editor's debugger
    await page.click('.ck-inspector-button.ck-inspector-navbox__navigation__toggle');

    await selectEditorRoot();
});

afterEach(async () => {
    const page = getPage();
    await page.evaluate(() => {
        // We need to reset the value because it is kept between sessions using local storage.
        window.localStorage.clear();
    });
    await page.reload();
});
