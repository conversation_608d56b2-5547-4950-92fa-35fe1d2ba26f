import { rimraf } from 'rimraf';
import { COVERAGE_TEMP_DIR, GLOBAL_KEY_BROWSER, GLOBAL_KEY_SERVER, TEMP_DIR, TEST_APP_URL } from './test-constants';
import type * as puppeteer from 'puppeteer';
import v8toIstanbul from 'v8-to-istanbul';
import type * as http from 'http';
import * as fs from 'fs';
import * as path from 'path';
import * as istanbulLibCoverage from 'istanbul-lib-coverage';
import * as istanbulLibReport from 'istanbul-lib-report';
import * as istanbulReports from 'istanbul-reports';

export default async function TestTeardown(): Promise<void> {
    const puppeteer: puppeteer.Browser = globalThis[GLOBAL_KEY_BROWSER];
    const server: http.Server = globalThis[GLOBAL_KEY_SERVER];

    // close the browser instance
    await puppeteer.close();

    if (server) {
        await new Promise<void>(resolve => server.close(() => resolve()));
    }

    const coverageMap = istanbulLibCoverage.createCoverageMap();

    // Load coverage for each suite from the temp dir
    const coverageFileNames = fs.readdirSync(COVERAGE_TEMP_DIR);
    for (const coverageFileName of coverageFileNames) {
        const coverageFilePath = path.resolve(COVERAGE_TEMP_DIR, coverageFileName);
        const coverageDetails = JSON.parse(fs.readFileSync(coverageFilePath, 'utf-8'));
        for (const v8ScriptCov of coverageDetails) {
            if (v8ScriptCov.url.indexOf(TEST_APP_URL) === 0 && v8ScriptCov.url.endsWith('main-bundle.js')) {
                const filePath = path.resolve(process.cwd(), 'build', v8ScriptCov.url.replace(TEST_APP_URL, ''));
                const converter = v8toIstanbul(filePath);
                await converter.load();
                converter.applyCoverage(v8ScriptCov.rawScriptCoverage.functions);
                coverageMap.merge(converter.toIstanbul());
            }
        }
    }

    // Filter out css files
    const filteredCoverageMap = istanbulLibCoverage.createCoverageMap();
    filteredCoverageMap.merge(
        Object.keys(coverageMap.data).reduce((prevValue, key): istanbulLibCoverage.CoverageMapData => {
            if (!key.endsWith('.css') && !key.endsWith('.scss')) {
                prevValue[key] = coverageMap.data[key];
            }
            return prevValue;
        }, {} as istanbulLibCoverage.CoverageMapData),
    );

    const reportContext = istanbulLibReport.createContext({
        coverageMap: filteredCoverageMap,
        defaultSummarizer: 'nested',
        dir: path.resolve(process.cwd(), 'coverage'),
        sourceFinder: (file: string) => {
            const mappedFile = path.join(process.cwd(), file);
            return fs.readFileSync(mappedFile, 'utf-8');
        },
    });

    (istanbulReports.create('lcov') as any).execute(reportContext);
    (istanbulReports.create('text') as any).execute(reportContext);
    (istanbulReports.create('json') as any).execute(reportContext);

    // clean-up the wsEndpoint file and coverage
    await rimraf.rimraf(TEMP_DIR);
}
