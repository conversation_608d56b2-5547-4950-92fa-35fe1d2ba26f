import Button from 'carbon-react/esm/components/button';
import Dialog from 'carbon-react/esm/components/dialog';
import Form from 'carbon-react/esm/components/form';
import Typography from 'carbon-react/esm/components/typography';
import React from 'react';
import type { DocumentContextProvider } from '../context-provider';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import { getContextObjectType } from '../plugins/utils';
import type { LocalizeEnumFunction, LocalizeFunction, LocalizeLocale, NodeDetails } from '@sage/xtrem-shared';
import type { ConditionEditorProperty, FilterParameter, CarbonLocaleType } from '@sage/xtrem-ui-components';
import { ConditionEditorTableComponent } from '@sage/xtrem-ui-components';

export interface ConditionDialogRequest {
    existingConditions?: ConditionEditorProperty[];
    onFinished: (newCondition: any) => void;
}

export interface ConditionDialogProps {
    conditionRequest: ConditionDialogRequest | null;
    contextProvider: DocumentContextProvider;
    editorRef: MultiRootEditor | null;
    locale: LocalizeLocale;
    localize: LocalizeFunction;
    localizeEnumMember: LocalizeEnumFunction;
    onClose: () => void;
    carbonLocale: CarbonLocaleType;
}

export function ConditionDialog({
    carbonLocale,
    conditionRequest,
    contextProvider,
    editorRef,
    locale,
    localize,
    localizeEnumMember,
    onClose,
}: ConditionDialogProps): React.ReactElement {
    const [conditions, setConditions] = React.useState<ConditionEditorProperty[]>(
        conditionRequest?.existingConditions ?? [],
    );
    const [contextObjectType, setContextObjectType] = React.useState<string | null>(null);
    const [parameters, setParameters] = React.useState<FilterParameter[] | null>(null);
    const [isValid, setValid] = React.useState<boolean>(false);

    const fetchedParameters = React.useMemo(() => contextProvider.getDocumentParameters(), [contextProvider]);

    React.useEffect(() => {
        const asyncEffect = async (): Promise<void> => {
            const params = await fetchedParameters;
            setParameters(params || []);
        };

        if (!conditionRequest) {
            setConditions([]);
            return;
        }

        const cot = getContextObjectType(editorRef?.model.document.selection.getFirstPosition()?.parent);
        setContextObjectType(cot);
        setConditions(conditionRequest?.existingConditions ?? []);
        asyncEffect();
    }, [conditionRequest, contextProvider, editorRef, fetchedParameters]);

    const isOpen = React.useMemo(() => Boolean(conditionRequest), [conditionRequest]);

    const onConfirmed = React.useCallback(() => {
        if (conditionRequest?.onFinished) {
            conditionRequest?.onFinished(conditions);
            onClose();
        }
    }, [conditionRequest, conditions, onClose]);

    const fetchItems = React.useCallback(
        (object: NodeDetails): Promise<NodeDetails[]> => {
            if (object?.node) {
                return contextProvider.getObjectDetails({
                    objectType: object?.node,
                    contextType: 'body',
                    canParentFilter: true,
                    canParentSort: true,
                });
            }
            return Promise.resolve([]);
        },
        [contextProvider],
    );

    return (
        <Dialog
            className="document-editor-condition-dialog"
            onCancel={onClose}
            open={isOpen}
            size="extra-large"
            title={localize('@sage/xtrem-document-editor/conditional-block-title', 'Conditional block')}
            height="600px"
        >
            <Form
                stickyFooter
                height="500px"
                leftSideButtons={
                    <Button onClick={onClose}>{localize('@sage/xtrem-document-editor/cancel', 'Cancel')}</Button>
                }
                saveButton={
                    <Button buttonType="primary" type="button" onClick={onConfirmed} disabled={!isValid}>
                        {localize('@sage/xtrem-document-editor/apply-button', 'Apply')}
                    </Button>
                }
            >
                <Typography>
                    {localize(
                        '@sage/xtrem-document-editor/conditional-block-description',
                        'A new block will be inserted and its content will be displayed only if all the following conditions are satisfied.',
                    )}
                </Typography>
                {contextObjectType && parameters && (
                    <ConditionEditorTableComponent
                        carbonLocale={carbonLocale}
                        fetchItems={fetchItems}
                        locale={locale}
                        localize={localize}
                        localizeEnumMember={localizeEnumMember}
                        node={contextObjectType}
                        onChange={setConditions}
                        onValidityChange={setValid}
                        parameters={parameters}
                        value={conditions}
                    />
                )}
            </Form>
        </Dialog>
    );
}
