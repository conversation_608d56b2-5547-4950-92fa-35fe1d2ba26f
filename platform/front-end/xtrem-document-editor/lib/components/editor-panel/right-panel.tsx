import {
    COMMAND_ALIGNMENT_QUERY_TABLE,
    COMMAND_BACKGROUND_QUERY_TABLE,
    COMMAND_BORDER_COLOR_QUERY_TABLE,
    COMMAND_BORDER_STYLE_QUERY_TABLE,
    COMMAND_BORDER_WIDTH_QUERY_TABLE,
    COMMAND_CELL_BACKGROUND_QUERY_TABLE,
    COMMAND_CELL_BORDER_COLOR_QUERY_TABLE,
    COMMAND_CELL_BORDER_STYLE_QUERY_TABLE,
    COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE,
    COMMAND_CELL_HEIGHT_QUERY_TABLE,
    COMMAND_CELL_HORIZONTAL_ALIGNMENT_QUERY_TABLE,
    COMMAND_CELL_VERTICAL_ALIGNMENT_QUERY_TABLE,
    COMMAND_CHANGE_PROPERTY_FORMAT,
    COMMAND_EDIT_CONDITION_BLOCK,
    COMMAND_EDIT_FILTER_QUERY_TABLE,
    COMMAND_EDIT_ORDER_QUERY_TABLE,
    COMMAND_INSERT_COLUMN_QUERY_COMMAND,
    COMMAND_INSERT_PAGE_BREAK_QUERY_TABLE,
    COMMAND_INSERT_TABLE_COLUMN_LEFT,
    COMMAND_REMOVE_COLUMN_QUERY_TABLE,
    COMMAND_REMOVE_TABLE_COLUMN,
    COMMAND_TABLE_CELL_BACKGROUND_COLOR,
    COMMAND_TABLE_CELL_BORDER_COLOR,
    COMMAND_TABLE_CELL_BORDER_STYLE,
    COMMAND_TABLE_CELL_BORDER_WIDTH,
    COMMAND_TABLE_CELL_HEIGHT,
    COMMAND_TABLE_CELL_HORIZONTAL_ALIGNMENT,
    COMMAND_TABLE_CELL_VERTICAL_ALIGNMENT,
} from '../../constants';
import * as React from 'react';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import { PanelHeader } from './panel-header';
import ToggleRightPanelUi from '../../plugins/toggle-right-panel/toggle-right-panel-ui';
import QueryTableUtils from '../../plugins/query-table/utils/query-table-utils';
import { TableUtils } from '@ckeditor/ckeditor5-table';
import { HorizontalCellAlignment } from '../horizontal-alignment';
import { VerticalCellAlignment } from '../vertical-alignment';
import type { CarbonLocaleType } from '@sage/xtrem-ui-components';
import { GridRow } from '@sage/xtrem-ui-components';
import { EditorActionButton } from '../editor-action-button';
import { PropertyFormatEditor } from '../property-format-editor';
import type { LocalizeEnumFunction, LocalizeFunction, LocalizeLocale } from '@sage/xtrem-shared';
import { EditOrderActionButton } from '../edit-order-action-button';
import { EditFilterActionButton } from '../edit-filter-action-button';
import type { DocumentContextProvider } from '../../context-provider';
import { useEditorCommandState } from '../use-editor-command-state';
import { NoFormatOptionsComponent } from '../no-format-options/no-format-options-component';
import _ from 'lodash';
import { PageBreak } from '../page-break';
import { MinimumCellHeight } from '../min-cell-height';
import { CellBorderWidthSelect } from '../border-width/cell-border-width-select';
import { TableBorderWidthSelect } from '../border-width/table-border-width-select';
import { TableBorderStyleSelect } from '../border-style/table-border-style-select';
import { CellBorderStyleSelect } from '../border-style/cell-border-style-select';
import { TableColorPicker } from '../color-picker/table-color-picker';
import { CellColorPicker } from '../color-picker/cell-color-picker';
import { EditConditionActionButton } from '../edit-condition-action-button';
import type { ConditionDialogRequest } from '../condition-dialog';

export interface RightPanelProps {
    carbonLocale: CarbonLocaleType;
    contextProvider: DocumentContextProvider;
    editorRef: MultiRootEditor | null;
    isOpen: boolean;
    locale: LocalizeLocale;
    localize: LocalizeFunction;
    localizeEnumMember: LocalizeEnumFunction;
    setConditionDialogRequest: (conditionDialogRequest: ConditionDialogRequest) => void;
}

type EnabledCommands = {
    [key: string]: boolean;
};

export function RightPanel({
    carbonLocale,
    contextProvider,
    editorRef,
    isOpen,
    locale,
    localize,
    localizeEnumMember,
    setConditionDialogRequest,
}: RightPanelProps): React.ReactElement | null {
    const enabledCommands: EnabledCommands = useEditorCommandState(editorRef);

    const allCommandsDisabled = (obj: EnabledCommands): boolean => {
        return _.every(obj, value => value === false);
    };

    if (!isOpen || !editorRef) {
        return null;
    }

    const noCommandsEnabled = allCommandsDisabled(enabledCommands);

    return (
        <div className="document-editor-right-panel">
            <PanelHeader
                editorRef={editorRef}
                title={localize('@sage/xtrem-document-editor/right-panel-title', 'Formatting')}
                actionUiPlugin={ToggleRightPanelUi}
                localize={localize}
            />
            <div className="document-editor-right-panel-body">
                {!noCommandsEnabled && (
                    <GridRow
                        columns={4}
                        gutter={16}
                        margin={0}
                        verticalMargin={16}
                        className="document-editor-right-panel-body-row"
                    >
                        <HorizontalCellAlignment
                            title={localize('@sage/xtrem-document-editor/table-alignment', 'Table alignment')}
                            editor={editorRef}
                            tableUtilsPluginConstructor={QueryTableUtils}
                            commandName={COMMAND_ALIGNMENT_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_ALIGNMENT_QUERY_TABLE]}
                            localize={localize}
                        />
                        <HorizontalCellAlignment
                            title={localize(
                                '@sage/xtrem-document-editor/cell-horizontal-alignment',
                                'Cell horizontal alignment',
                            )}
                            editor={editorRef}
                            tableUtilsPluginConstructor={QueryTableUtils}
                            commandName={COMMAND_CELL_HORIZONTAL_ALIGNMENT_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_CELL_HORIZONTAL_ALIGNMENT_QUERY_TABLE]}
                            localize={localize}
                        />
                        <HorizontalCellAlignment
                            title={localize(
                                '@sage/xtrem-document-editor/cell-horizontal-alignment',
                                'Cell horizontal alignment',
                            )}
                            editor={editorRef}
                            tableUtilsPluginConstructor={TableUtils}
                            commandName={COMMAND_TABLE_CELL_HORIZONTAL_ALIGNMENT}
                            isEnabled={enabledCommands[COMMAND_TABLE_CELL_HORIZONTAL_ALIGNMENT]}
                            localize={localize}
                        />
                        <VerticalCellAlignment
                            title={localize(
                                '@sage/xtrem-document-editor/cell-vertical-alignment',
                                'Cell vertical alignment',
                            )}
                            editor={editorRef}
                            tableUtilsPluginConstructor={QueryTableUtils}
                            commandName={COMMAND_CELL_VERTICAL_ALIGNMENT_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_CELL_VERTICAL_ALIGNMENT_QUERY_TABLE]}
                            localize={localize}
                        />
                        <VerticalCellAlignment
                            title={localize(
                                '@sage/xtrem-document-editor/cell-vertical-alignment',
                                'Cell vertical alignment',
                            )}
                            editor={editorRef}
                            tableUtilsPluginConstructor={TableUtils}
                            commandName={COMMAND_TABLE_CELL_VERTICAL_ALIGNMENT}
                            isEnabled={enabledCommands[COMMAND_TABLE_CELL_VERTICAL_ALIGNMENT]}
                            localize={localize}
                        />
                        <PageBreak
                            title={localize('@sage/xtrem-document-editor/insert-page-break', 'Insert page break')}
                            editor={editorRef}
                            tableUtilsPluginConstructor={QueryTableUtils}
                            commandName={COMMAND_INSERT_PAGE_BREAK_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_INSERT_PAGE_BREAK_QUERY_TABLE]}
                            localize={localize}
                        />
                        <CellColorPicker
                            editor={editorRef}
                            commandName={COMMAND_CELL_BACKGROUND_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_CELL_BACKGROUND_QUERY_TABLE]}
                            title={localize('@sage/xtrem-document-editor/background-color', 'Background color')}
                            tableUtilsPluginConstructor={QueryTableUtils}
                        />
                        <CellColorPicker
                            editor={editorRef}
                            commandName={COMMAND_TABLE_CELL_BACKGROUND_COLOR}
                            isEnabled={enabledCommands[COMMAND_TABLE_CELL_BACKGROUND_COLOR]}
                            title={localize('@sage/xtrem-document-editor/background-color', 'Background color')}
                            tableUtilsPluginConstructor={TableUtils}
                        />
                        <CellColorPicker
                            editor={editorRef}
                            commandName={COMMAND_CELL_BORDER_COLOR_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_CELL_BORDER_COLOR_QUERY_TABLE]}
                            title={localize('@sage/xtrem-document-editor/border-color', 'Border color')}
                            tableUtilsPluginConstructor={QueryTableUtils}
                        />
                        <CellColorPicker
                            editor={editorRef}
                            commandName={COMMAND_TABLE_CELL_BORDER_COLOR}
                            isEnabled={enabledCommands[COMMAND_TABLE_CELL_BORDER_COLOR]}
                            title={localize('@sage/xtrem-document-editor/border-color', 'Border color')}
                            tableUtilsPluginConstructor={TableUtils}
                        />
                        <CellBorderWidthSelect
                            editor={editorRef}
                            commandName={COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE]}
                            title={localize('@sage/xtrem-document-editor/border-width', 'Border width')}
                            tableUtilsPluginConstructor={QueryTableUtils}
                        />
                        <CellBorderWidthSelect
                            editor={editorRef}
                            commandName={COMMAND_TABLE_CELL_BORDER_WIDTH}
                            isEnabled={enabledCommands[COMMAND_TABLE_CELL_BORDER_WIDTH]}
                            title={localize('@sage/xtrem-document-editor/border-width', 'Border width')}
                            tableUtilsPluginConstructor={TableUtils}
                        />
                        <CellBorderStyleSelect
                            editor={editorRef}
                            commandName={COMMAND_CELL_BORDER_STYLE_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_CELL_BORDER_STYLE_QUERY_TABLE]}
                            title={localize('@sage/xtrem-document-editor/border-style', 'Border style')}
                            tableUtilsPluginConstructor={QueryTableUtils}
                        />
                        <CellBorderStyleSelect
                            editor={editorRef}
                            commandName={COMMAND_TABLE_CELL_BORDER_STYLE}
                            isEnabled={enabledCommands[COMMAND_TABLE_CELL_BORDER_STYLE]}
                            title={localize('@sage/xtrem-document-editor/border-style', 'Border style')}
                            tableUtilsPluginConstructor={TableUtils}
                        />
                        <TableColorPicker
                            editor={editorRef}
                            commandName={COMMAND_BACKGROUND_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_BACKGROUND_QUERY_TABLE]}
                            title={localize('@sage/xtrem-document-editor/background-color', 'Background color')}
                        />
                        <TableColorPicker
                            editor={editorRef}
                            commandName={COMMAND_BORDER_COLOR_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_BORDER_COLOR_QUERY_TABLE]}
                            title={localize('@sage/xtrem-document-editor/border-color', 'Border color')}
                        />
                        <TableBorderStyleSelect
                            editor={editorRef}
                            commandName={COMMAND_BORDER_STYLE_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_BORDER_STYLE_QUERY_TABLE]}
                            title={localize('@sage/xtrem-document-editor/border-style', 'Border style')}
                        />
                        <TableBorderWidthSelect
                            editor={editorRef}
                            commandName={COMMAND_BORDER_WIDTH_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_BORDER_WIDTH_QUERY_TABLE]}
                            title={localize('@sage/xtrem-document-editor/border-width', 'Border width')}
                        />
                        <MinimumCellHeight
                            title={localize('@sage/xtrem-document-editor/minimum-cell-height', 'Minimum cell height')}
                            editor={editorRef}
                            commandName={COMMAND_TABLE_CELL_HEIGHT}
                            tableUtilsPluginConstructor={QueryTableUtils}
                            isEnabled={enabledCommands[COMMAND_TABLE_CELL_HEIGHT]}
                        />
                        <MinimumCellHeight
                            title={localize('@sage/xtrem-document-editor/minimum-cell-height', 'Minimum cell height')}
                            editor={editorRef}
                            commandName={COMMAND_CELL_HEIGHT_QUERY_TABLE}
                            tableUtilsPluginConstructor={QueryTableUtils}
                            isEnabled={enabledCommands[COMMAND_CELL_HEIGHT_QUERY_TABLE]}
                        />
                        <EditorActionButton
                            editor={editorRef}
                            commandName={COMMAND_REMOVE_COLUMN_QUERY_TABLE}
                            isEnabled={enabledCommands[COMMAND_REMOVE_COLUMN_QUERY_TABLE]}
                            label={localize('@sage/xtrem-document-editor/remove-column', 'Remove column')}
                        />
                        <EditorActionButton
                            editor={editorRef}
                            commandName={COMMAND_INSERT_COLUMN_QUERY_COMMAND}
                            isEnabled={enabledCommands[COMMAND_INSERT_COLUMN_QUERY_COMMAND]}
                            label={localize('@sage/xtrem-document-editor/insert-column', 'Insert column')}
                        />
                        <EditorActionButton
                            editor={editorRef}
                            commandName={COMMAND_REMOVE_TABLE_COLUMN}
                            isEnabled={enabledCommands[COMMAND_REMOVE_TABLE_COLUMN]}
                            label={localize('@sage/xtrem-document-editor/remove-column', 'Remove column')}
                        />
                        <EditorActionButton
                            editor={editorRef}
                            commandName={COMMAND_INSERT_TABLE_COLUMN_LEFT}
                            isEnabled={enabledCommands[COMMAND_INSERT_TABLE_COLUMN_LEFT]}
                            label={localize('@sage/xtrem-document-editor/insert-column', 'Insert column')}
                        />
                        <PropertyFormatEditor
                            editor={editorRef}
                            localize={localize}
                            isEnabled={enabledCommands[COMMAND_CHANGE_PROPERTY_FORMAT]}
                        />
                        <EditOrderActionButton
                            contextProvider={contextProvider}
                            localize={localize}
                            editor={editorRef}
                            isEnabled={enabledCommands[COMMAND_EDIT_ORDER_QUERY_TABLE]}
                        />
                        <EditFilterActionButton
                            carbonLocale={carbonLocale}
                            contextProvider={contextProvider}
                            editor={editorRef}
                            locale={locale}
                            localize={localize}
                            localizeEnumMember={localizeEnumMember}
                            isEnabled={enabledCommands[COMMAND_EDIT_FILTER_QUERY_TABLE]}
                        />
                        <EditConditionActionButton
                            editor={editorRef}
                            localize={localize}
                            setConditionDialogRequest={setConditionDialogRequest}
                            isEnabled={enabledCommands[COMMAND_EDIT_CONDITION_BLOCK]}
                        />
                    </GridRow>
                )}
                {noCommandsEnabled && (
                    <GridRow
                        columns={1}
                        gutter={16}
                        margin={0}
                        verticalMargin={16}
                        className="document-editor-right-panel-body-row"
                    >
                        <NoFormatOptionsComponent
                            text={localize(
                                '@sage/xtrem-document-editor/formatting-empty-state-no-field-selected',
                                'Nothing to define.',
                            )}
                            subText={localize(
                                '@sage/xtrem-document-editor/formatting-empty-state-select-field',
                                'Select something else to display the relevant information.',
                            )}
                        />
                    </GridRow>
                )}
            </div>
        </div>
    );
}
