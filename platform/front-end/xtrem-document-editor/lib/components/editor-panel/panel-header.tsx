import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import IconButton from 'carbon-react/esm/components/icon-button';
import Icon from 'carbon-react/esm/components/icon';
import tokens from '@sage/design-tokens/js/base/es6';
import * as React from 'react';
import type { PluginClassConstructor } from '@ckeditor/ckeditor5-core/src/plugin';
import type TogglePluginUi from '../../plugins/toggle-plugin-ui';
import type { LocalizeFunction } from '@sage/xtrem-shared';

export interface PanelHeaderProps {
    title: string;
    actionUiPlugin: PluginClassConstructor;
    editorRef: MultiRootEditor;
    localize: LocalizeFunction;
}

export function PanelHeader({
    title,
    actionUiPlugin: action,
    editorRef,
    localize,
}: PanelHeaderProps): React.ReactElement {
    const onClose = React.useCallback(() => {
        const plugin = editorRef.plugins.get(action) as TogglePluginUi;
        plugin.togglePanel();
    }, [editorRef, action]);

    return (
        <div className="document-editor-panel-header">
            <span className="document-editor-panel-header-label">{title}</span>
            <IconButton onClick={onClose} aria-label={localize('@sage/xtrem-document-editor/close', 'Close')}>
                <Icon fontSize="large" type="cross" color={tokens.colorsActionMinor500} />
            </IconButton>
        </div>
    );
}
