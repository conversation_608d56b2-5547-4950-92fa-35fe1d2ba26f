import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import type { LocalizeFunction } from '@sage/xtrem-shared';
import * as React from 'react';
import {
    COMMAND_INSERT_PROPERTY,
    COMMAND_INSERT_QUERY_TABLE,
    COMMAND_INSERT_RECORD_CONTEXT,
    OBJECT_TYPE_GLOBAL_PROPERTIES,
    OBJECT_TYPE_ROOT,
} from '../../constants';
import type { ContextType, DocumentContextProvider } from '../../context-provider';
import ToggleDataPropertiesPanelUi from '../../plugins/toggle-data-properties-panel/toggle-data-properties-panel-ui';
import { getContextObjectType } from '../../plugins/utils';
import type {
    DocumentEditorInsertDialogResult,
    LookupRequest,
} from '../insert-list-property-dialog/insert-list-property-dialog';
import { NodeBrowserTree } from '../node-browser-tree/node-browser-tree';
import { PanelHeader } from './panel-header';
import type { TableContentWithGroupsProps } from '@sage/xtrem-ui-components';
import ToggleGlobalPropertiesPanelUi from '../../plugins/toggle-global-properties-panel/toggle-global-properties-panel-ui';

export interface LeftPanelProps {
    isOpen: boolean;
    contextProvider: DocumentContextProvider;
    editorRef: MultiRootEditor | null;
    localize: LocalizeFunction;
    onOpenLookupDialog: (lookupRequest: LookupRequest) => void;
    rootContext: string;
}

export function LeftPanel({
    isOpen,
    contextProvider: { getObjectDetails, onObjectInsert },
    editorRef,
    onOpenLookupDialog,
    localize,
    rootContext,
}: LeftPanelProps): React.ReactElement | null {
    const [recordContext, setRecordContext] = React.useState(rootContext);
    const [rootName, setSelectedNameRoot] = React.useState<ContextType>('body');

    const onItemsSelected = React.useCallback(
        async (selectedItems: NonNullable<TableContentWithGroupsProps['selectedItems']>): Promise<void> => {
            const selectedItem = Object.values(selectedItems)[0];
            // Inserting simple scalar property
            if (selectedItem.data.kind === 'SCALAR' || selectedItem.data.kind === 'ENUM') {
                const remapInfo = await onObjectInsert(selectedItem.key, selectedItem.data, 'property');
                editorRef?.execute(COMMAND_INSERT_PROPERTY, { selectedItem, remapInfo, recordContext });
            }

            // Inserting a list or record context
            if (selectedItem.data.kind === 'LIST') {
                const lookupRequest: LookupRequest = {
                    root: rootName,
                    contextObjectType: selectedItem.data.type,
                    onSelected: async (lookupResult: DocumentEditorInsertDialogResult) => {
                        const remapInfo = await onObjectInsert(
                            selectedItem.key,
                            selectedItem.data,
                            lookupResult.selectedMode,
                        );

                        editorRef?.execute(
                            lookupResult.selectedMode === 'list'
                                ? COMMAND_INSERT_QUERY_TABLE
                                : COMMAND_INSERT_RECORD_CONTEXT,
                            { lookupResult, selectedItem, remapInfo },
                        );
                    },
                };
                onOpenLookupDialog(lookupRequest);
            }
        },
        [editorRef, onObjectInsert, onOpenLookupDialog, recordContext, rootName],
    );

    React.useEffect(() => {
        const onChange = (): void => {
            if (rootContext === OBJECT_TYPE_ROOT) {
                setRecordContext(
                    getContextObjectType(editorRef?.model.document.selection.focus?.parent) || rootContext,
                );
            }
            const currentRootName: ContextType =
                (editorRef?.model.document.selection.getFirstRange()?.start.root.rootName as ContextType) || 'body';
            setSelectedNameRoot(currentRootName);
        };

        editorRef?.model.document.on('change', onChange);

        onChange();
        return (): void => {
            editorRef?.model.document.off('change', onChange);
        };
    }, [editorRef, isOpen, rootContext]);

    const getTitle = React.useCallback((): string => {
        if (recordContext === OBJECT_TYPE_ROOT) {
            return localize('@sage/xtrem-document-editor/global-context', 'Global context');
        }
        if (recordContext === OBJECT_TYPE_GLOBAL_PROPERTIES) {
            return localize('@sage/xtrem-document-editor/global-properties', 'Document properties');
        }

        return recordContext;
    }, [localize, recordContext]);

    const getNodesAndCollections = React.useCallback<React.ComponentProps<typeof NodeBrowserTree>['getObjectDetails']>(
        async getObjectDetailsArgs => {
            const objectDetails = await getObjectDetails({ ...getObjectDetailsArgs, contextType: rootName });
            const isRootContext = recordContext === OBJECT_TYPE_ROOT;
            return isRootContext ? objectDetails.map(o => ({ ...o, kind: 'LIST' })) : objectDetails;
        },
        [getObjectDetails, recordContext, rootName],
    );

    if (!isOpen || !editorRef) {
        return null;
    }

    return (
        <div className="document-editor-left-panel">
            <PanelHeader
                editorRef={editorRef}
                title={localize('@sage/xtrem-document-editor/left-panel-title', 'Fields')}
                actionUiPlugin={
                    rootContext === OBJECT_TYPE_ROOT ? ToggleDataPropertiesPanelUi : ToggleGlobalPropertiesPanelUi
                }
                localize={localize}
            />
            <div className="document-editor-panel-context-indicator">{getTitle()}</div>
            <div className="document-editor-right-panel-node-browser">
                <NodeBrowserTree
                    checkedItems={{}}
                    getObjectDetails={getNodesAndCollections}
                    localize={localize}
                    objectType={recordContext}
                    onCheckedItemsUpdated={onItemsSelected}
                    root={rootName}
                    selectionMode="button"
                />
            </div>
        </div>
    );
}
