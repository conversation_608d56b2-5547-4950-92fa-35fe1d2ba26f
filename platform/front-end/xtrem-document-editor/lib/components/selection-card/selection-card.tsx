import * as React from 'react';
import type { IconType } from 'carbon-react/esm/components/icon';
import Icon from 'carbon-react/esm/components/icon';

export interface SelectionCardProps {
    _id: string;
    title: string;
    description?: string;
    onClick: () => void;
    isSelected?: boolean;
    isReadOnly?: boolean;
    icon: IconType;
}

export const SelectionCard = React.memo(
    React.forwardRef<React.ElementRef<'button'>, SelectionCardProps>(
        ({ _id, title, description = '', icon, onClick, isSelected = false, isReadOnly = false }, ref) => {
            const onCardClick = React.useCallback(
                (ev: React.MouseEvent<HTMLButtonElement>) => {
                    ev.preventDefault();
                    onClick();
                },
                [onClick],
            );

            const baseClassName = isSelected ? 'e-selection-card e-selection-card-selected' : 'e-selection-card';

            return (
                <button
                    ref={ref}
                    type="button"
                    data-testid={`e-selection-card-${_id}`}
                    className={baseClassName}
                    onClick={onCardClick}
                    aria-label={title}
                    disabled={isReadOnly}
                >
                    <div data-testid="e-selection-card-image" className="e-selection-card-image">
                        {isSelected ? (
                            <Icon
                                data-testid="e-selection-card-image-selected"
                                fontSize="large"
                                type="tick_circle"
                                color="--colorsSemanticPositive500"
                            />
                        ) : (
                            <Icon data-testid="e-selection-card-image-selected" fontSize="large" type={icon} />
                        )}
                    </div>
                    <div data-testid="e-selection-card-content" className="e-selection-card-content">
                        <div data-testid="e-selection-card-title" className="e-selection-card-title">
                            {title}
                        </div>
                        <div data-testid="e-selection-card-description" className="e-selection-card-description">
                            {description}
                        </div>
                    </div>
                </button>
            );
        },
    ),
);

SelectionCard.displayName = 'SelectionCard';
