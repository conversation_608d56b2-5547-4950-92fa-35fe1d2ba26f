import type { Editor, Plugin } from '@ckeditor/ckeditor5-core';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import type { DocumentSelection, Element, Selection } from '@ckeditor/ckeditor5-engine';
import * as React from 'react';

interface TableUtilsPluginType extends Plugin {
    getSelectionAffectedTableRows: (selection: Selection | DocumentSelection) => Array<Element>;
}

export type TableUtilsPluginConstructorType = new (editor: Editor) => TableUtilsPluginType;

export const useQueryTableRowPropertyCommand = ({
    commandName,
    editor,
    tableUtilsPluginConstructor,
    defaultValue = false,
    attrName,
}: {
    commandName: string;
    editor: MultiRootEditor | null;
    tableUtilsPluginConstructor: TableUtilsPluginConstructorType;
    defaultValue?: any;
    attrName: string;
}): {
    value: boolean;
    onChange: (value: boolean) => void;
} => {
    const [value, setValue] = React.useState<boolean>(defaultValue);

    const onEditorChange = React.useCallback(() => {
        const tableUtils = editor?.plugins.get(tableUtilsPluginConstructor);
        if (editor) {
            const selectedTableRows = tableUtils?.getSelectionAffectedTableRows(editor.model.document.selection);
            if (selectedTableRows?.[0]) {
                const currentValue = selectedTableRows[0].getAttribute(attrName) as boolean;
                if (currentValue !== value) {
                    setValue(currentValue || defaultValue);
                }
            }
        }
    }, [editor, attrName, value, tableUtilsPluginConstructor, defaultValue]);

    React.useEffect(() => {
        onEditorChange();
        editor?.model.document.on('change', onEditorChange);
        return (): void => {
            editor?.model.document.off('change', onEditorChange);
        };
    }, [editor, commandName, onEditorChange]);

    const onChange = React.useCallback(
        (changeValue: boolean) => {
            setValue(changeValue);
            editor?.execute(commandName, { value: changeValue });
        },
        [editor, commandName],
    );

    return { value, onChange };
};
