import * as React from 'react';

export const NoFormatOptions = React.memo(() => (
    <svg xmlns="http://www.w3.org/2000/svg" width="96" height="96" viewBox="0 0 96 96" fill="none">
        <g clipPath="url(#clip0_1007_17508)">
            <path
                d="M91.1999 96H30.5439C29.2709 96 28.05 95.4943 27.1498 94.5941C26.2496 93.6939 25.7439 92.473 25.7439 91.2V4.8C25.7439 3.52696 26.2496 2.30606 27.1498 1.40588C28.05 0.505709 29.2709 0 30.5439 0H91.1999C92.4729 0 93.6938 0.505709 94.594 1.40588C95.4942 2.30606 95.9999 3.52696 95.9999 4.8V91.2C95.9999 92.473 95.4942 93.6939 94.594 94.5941C93.6938 95.4943 92.4729 96 91.1999 96Z"
                fill="#8D8D8D"
            />
            <path
                d="M42.1441 80.0001C35.1357 80.1547 28.3524 77.5223 23.2829 72.6808C18.2134 67.8393 15.2719 61.1841 15.104 54.1761C14.9624 47.1748 17.6032 40.4031 22.4472 35.3461C27.2913 30.2891 33.9432 27.3596 40.9441 27.2001C47.9605 27.0413 54.7528 29.6761 59.8267 34.525C64.9006 39.3738 67.8406 46.0396 68.0001 53.0561C68.133 60.0546 65.4863 66.8204 60.6397 71.8709C55.7931 76.9214 49.1421 79.8447 42.1441 80.0001ZM75.7441 12.8001C75.7441 11.9514 75.4069 11.1375 74.8068 10.5374C74.2067 9.93724 73.3927 9.6001 72.5441 9.6001H34.1441C33.2954 9.6001 32.4814 9.93724 31.8813 10.5374C31.2812 11.1375 30.9441 11.9514 30.9441 12.8001C30.9441 13.6488 31.2812 14.4627 31.8813 15.0628C32.4814 15.663 33.2954 16.0001 34.1441 16.0001H72.5441C73.3927 16.0001 74.2067 15.663 74.8068 15.0628C75.4069 14.4627 75.7441 13.6488 75.7441 12.8001ZM85.3441 28.8001H34.1441C33.2954 28.8001 32.4814 28.463 31.8813 27.8628C31.2812 27.2627 30.9441 26.4488 30.9441 25.6001C30.9441 24.7514 31.2812 23.9375 31.8813 23.3374C32.4814 22.7372 33.2954 22.4001 34.1441 22.4001H85.3441C86.1927 22.4001 87.0067 22.7372 87.6068 23.3374C88.2069 23.9375 88.5441 24.7514 88.5441 25.6001C88.5441 26.4488 88.2069 27.2627 87.6068 27.8628C87.0067 28.463 86.1927 28.8001 85.3441 28.8001ZM85.3441 41.6001H34.1441C33.2954 41.6001 32.4814 41.263 31.8813 40.6628C31.2812 40.0627 30.9441 39.2488 30.9441 38.4001C30.9441 37.5514 31.2812 36.7375 31.8813 36.1374C32.4814 35.5372 33.2954 35.2001 34.1441 35.2001H85.3441C86.1927 35.2001 87.0067 35.5372 87.6068 36.1374C88.2069 36.7375 88.5441 37.5514 88.5441 38.4001C88.5441 39.2488 88.2069 40.0627 87.6068 40.6628C87.0067 41.263 86.1927 41.6001 85.3441 41.6001ZM85.3441 54.4001H34.1441C33.2954 54.4001 32.4814 54.063 31.8813 53.4628C31.2812 52.8627 30.9441 52.0488 30.9441 51.2001C30.9441 50.3514 31.2812 49.5375 31.8813 48.9374C32.4814 48.3372 33.2954 48.0001 34.1441 48.0001H85.3441C86.1927 48.0001 87.0067 48.3372 87.6068 48.9374C88.2069 49.5375 88.5441 50.3514 88.5441 51.2001C88.5441 52.0488 88.2069 52.8627 87.6068 53.4628C87.0067 54.063 86.1927 54.4001 85.3441 54.4001ZM85.3441 80.0001H72.6881C71.8394 80.0001 71.0254 79.663 70.4253 79.0628C69.8252 78.4627 69.4881 77.6488 69.4881 76.8001C69.4881 75.9514 69.8252 75.1375 70.4253 74.5374C71.0254 73.9372 71.8394 73.6001 72.6881 73.6001H85.3441C86.1927 73.6001 87.0067 73.9372 87.6068 74.5374C88.2069 75.1375 88.5441 75.9514 88.5441 76.8001C88.5441 77.6488 88.2069 78.4627 87.6068 79.0628C87.0067 79.663 86.1927 80.0001 85.3441 80.0001Z"
                fill="white"
            />
            <g clipPath="url(#clip1_1007_17508)">
                <path
                    d="M52.8787 63.2986C52.2607 63.9214 51.4478 64.2336 50.635 64.2336C49.8283 64.2336 49.0202 63.9261 48.4035 63.3103L42.7742 57.6953L37.1741 63.3381C36.5561 63.9609 35.7432 64.2731 34.9304 64.2731C34.1237 64.2731 33.3156 63.9656 32.699 63.3498C31.4597 62.1136 31.4535 60.1051 32.6866 58.8627L38.287 53.2196L32.6588 47.6057C31.4195 46.3695 31.4133 44.361 32.6464 43.1186C33.878 41.8794 35.8837 41.8708 37.1214 43.107L42.7508 48.722L48.3509 43.0793C49.5824 41.8392 51.5882 41.8322 52.8259 43.0677C54.0652 44.3039 54.0713 46.3124 52.8382 47.5548L47.2378 53.1979L52.8661 58.8118C54.1054 60.048 54.1115 62.0565 52.8784 63.2989L52.8787 63.2986ZM42.1468 24.5332C41.9196 24.5332 41.6925 24.5356 41.4653 24.541C33.634 24.7207 26.3403 27.9476 20.9273 33.6267C15.5158 39.306 12.6355 46.7581 12.8146 54.61C12.967 61.2866 15.3628 67.3798 19.2304 72.2341L0.927095 90.584C-0.309032 91.8226 -0.309032 93.8318 0.927095 95.0703C1.54516 95.6899 2.35484 95.9997 3.16469 95.9997C3.97453 95.9997 4.78405 95.6899 5.40228 95.0703L5.45908 95.0133L5.54912 95.1036L23.4525 77.1573C23.5587 77.0509 23.6435 76.9321 23.7315 76.8152C28.7759 80.9006 35.1853 83.3423 42.127 83.3423C42.3543 83.3423 42.5813 83.3399 42.8085 83.3345C58.9766 82.9635 71.83 69.4742 71.4591 53.2645C71.0942 37.2828 58.0104 24.5332 42.1468 24.5332ZM42.663 76.9909C42.4837 76.9947 42.3076 76.9971 42.1299 76.9971C29.689 76.9971 19.4285 66.9973 19.1411 54.4646C19.0005 48.3075 21.2611 42.4633 25.5045 38.0101C29.7493 33.5563 35.4684 31.0258 41.6107 30.8847C41.79 30.8809 41.9661 30.8786 42.1438 30.8786C54.5847 30.8786 64.8452 40.8776 65.1326 53.4103C65.4215 66.122 55.3434 76.6997 42.663 76.9908V76.9909Z"
                    fill="#C6C6C6"
                />
            </g>
        </g>
        <defs>
            <clipPath id="clip0_1007_17508">
                <rect width="96" height="96" fill="white" />
            </clipPath>
            <clipPath id="clip1_1007_17508">
                <rect width="71.4667" height="71.4667" fill="white" transform="translate(0 24.5332)" />
            </clipPath>
        </defs>
    </svg>
));

NoFormatOptions.displayName = 'NoFormatOptionsSvg';
