import * as React from 'react';
import { ButtonToggle, ButtonToggleGroup } from 'carbon-react/esm/components/button-toggle';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import type { TableUtilsPluginConstructorType } from './use-table-cell-property-command';
import { useQueryTableCellPropertyCommand } from './use-table-cell-property-command';
import { GridColumn } from '@sage/xtrem-ui-components';
import type { LocalizeFunction } from '@sage/xtrem-shared';

export interface CellColorPickerProps {
    title: string;
    commandName: string;
    editor: MultiRootEditor | null;
    tableUtilsPluginConstructor: TableUtilsPluginConstructorType;
    localize: LocalizeFunction;
    isEnabled: boolean;
}

export function VerticalCellAlignment({
    title,
    editor,
    commandName,
    tableUtilsPluginConstructor,
    localize,
    isEnabled,
}: CellColorPickerProps): React.ReactElement | null {
    const { value, onChange } = useQueryTableCellPropertyCommand({
        commandName,
        editor,
        tableUtilsPluginConstructor,
        defaultValue: 'top',
    });

    const onSelectChange = React.useCallback(
        (_ev: React.MouseEvent<HTMLButtonElement>, alignment: string) => {
            onChange(alignment);
        },
        [onChange],
    );

    if (!isEnabled || !editor) {
        return null;
    }

    return (
        <GridColumn columnSpan={4}>
            <ButtonToggleGroup
                id="button-toggle-group-id"
                label={title}
                value={value}
                onChange={onSelectChange}
                data-testid={`control-vertical-alignment-${commandName}`}
            >
                <ButtonToggle value="top">
                    {localize('@sage/xtrem-document-editor/horizontal-vertical-top', 'Top')}
                </ButtonToggle>
                <ButtonToggle value="middle">
                    {localize('@sage/xtrem-document-editor/horizontal-vertical-middle', 'Middle')}
                </ButtonToggle>
                <ButtonToggle value="bottom">
                    {localize('@sage/xtrem-document-editor/horizontal-vertical-bottom', 'Bottom')}
                </ButtonToggle>
            </ButtonToggleGroup>
        </GridColumn>
    );
}
