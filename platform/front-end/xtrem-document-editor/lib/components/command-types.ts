import { type MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import { type TableUtilsPluginConstructorType } from './use-table-cell-property-command';

export interface BaseCommandProps {
    title: string;
    commandName: string;
    isEnabled: boolean;
    editor: MultiRootEditor | null;
}

export interface BaseSelectProps extends BaseCommandProps {
    value: string;
    onChange: (value: string) => void;
}

export interface CellCommandProps extends BaseCommandProps {
    tableUtilsPluginConstructor: TableUtilsPluginConstructorType;
}
