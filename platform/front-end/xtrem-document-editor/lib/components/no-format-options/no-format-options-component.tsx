import * as React from 'react';
import { NoFormatOptions } from '../svg/no-format-options';

interface NoRowsProps {
    text?: string;
    subText?: string;
}

export function NoFormatOptionsComponent({ text, subText }: NoRowsProps): React.ReactElement | null {
    const className = ['e-no-format-options'];
    return (
        <div className={className.join(' ')} data-testid="e-no-rows-found-component">
            <div className="e-no-format-options-content-block">
                <div className="e-no-format-image-container">
                    <div className="e-no-format-options-rows-image">
                        <NoFormatOptions />
                    </div>
                </div>
                <div className="e-no-format-text-container">
                    <div className="e-no-format-options-text">{text}</div>
                    <div className="e-no-format-options-subtext">{subText}</div>
                </div>
            </div>
        </div>
    );
}
