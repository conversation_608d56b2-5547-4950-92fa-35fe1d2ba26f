import type { LocalizeFunction } from '@sage/xtrem-shared';
import type { TableContentWithGroupsProps } from '@sage/xtrem-ui-components';
import type { ContextType, DataModelProperty, GetObjectDetailsFunction } from '../../context-provider';

export interface NodeBrowserTreeProps {
    root: ContextType;
    isDisabled?: boolean;
    isReadOnly?: boolean;
    checkedItems?: NonNullable<TableContentWithGroupsProps['selectedItems']>;
    getObjectDetails: GetObjectDetailsFunction;
    onCheckedItemsUpdated?: (checkedItems: NonNullable<TableContentWithGroupsProps['selectedItems']>) => void;
    objectType?: string;
    filter?: (nodes: DataModelProperty[]) => DataModelProperty[];
    selectionMode: 'multiple' | 'button';
    localize: LocalizeFunction;
}
