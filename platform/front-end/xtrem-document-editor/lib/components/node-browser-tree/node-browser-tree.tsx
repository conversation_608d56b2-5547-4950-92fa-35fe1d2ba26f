import * as React from 'react';
import { Tree } from '@sage/xtrem-ui-components';
import { type TreeElement, type GraphQLTypes, textStreams } from '@sage/xtrem-shared';
import IconButton from 'carbon-react/esm/components/icon-button';
import Icon from 'carbon-react/esm/components/icon';
import Textbox from 'carbon-react/esm/components/textbox';
import type { NodeBrowserTreeProps } from './node-browser-tree-type';
import type { DataModelProperty } from '../../context-provider';

export const NodeBrowserTree: React.FC<NodeBrowserTreeProps> = React.memo(
    ({
        checkedItems,
        filter: incomingFilter,
        getObjectDetails,
        isDisabled,
        isReadOnly,
        localize,
        objectType,
        onCheckedItemsUpdated,
        root,
        selectionMode,
    }) => {
        const filter = React.useMemo(
            () => incomingFilter ?? ((v: DataModelProperty[]): DataModelProperty[] => v),
            [incomingFilter],
        );

        const rootElement = React.useMemo(
            (): TreeElement<DataModelProperty> => ({
                key: '',
                id: '',
                labelKey: '',
                label: '',
                labelPath: '',
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    kind: 'OBJECT',
                    type: objectType!,
                    name: '',
                    label: '',
                    canFilter: true,
                    canSort: true,
                    enumType: null,
                    isCustom: false,
                    dataType: '',
                    targetNode: '',
                    isStored: false,
                    isOnInputType: false,
                    isOnOutputType: false,
                    isMutable: false,
                },
            }),
            [objectType],
        );

        const fetchItems = React.useCallback(
            async (treeNode: TreeElement<DataModelProperty>): Promise<TreeElement<DataModelProperty>[]> => {
                const allNodeInfo = await getObjectDetails({
                    objectType: treeNode.data.type,
                    canParentFilter: treeNode.data.canFilter,
                    canParentSort: treeNode.data.canSort,
                    contextType: root,
                });

                let filteredNodes = [...allNodeInfo];
                if (filter) {
                    filteredNodes = filter(filteredNodes);
                }

                return filteredNodes.map<TreeElement<DataModelProperty>>(nodeInfo => ({
                    data: {
                        ...nodeInfo,
                    },
                    id: treeNode.key ? `${treeNode.key}.${nodeInfo.name}` : nodeInfo.name,
                    key: treeNode.key ? `${treeNode.key}.${nodeInfo.name}` : nodeInfo.name,
                    labelPath: treeNode.key ? `${treeNode.key}.${nodeInfo.name}` : nodeInfo.name,
                    labelKey: treeNode.label ? `${treeNode.label}.${nodeInfo.label}` : nodeInfo.label,
                    label: nodeInfo.label,
                    canBeExpanded:
                        (nodeInfo.kind === 'OBJECT' && !textStreams.includes(nodeInfo.type as GraphQLTypes)) ||
                        nodeInfo.kind === 'INTERFACE',
                    canBeSelected:
                        (nodeInfo.kind !== 'OBJECT' || textStreams.includes(nodeInfo.type as GraphQLTypes)) &&
                        nodeInfo.kind !== 'INTERFACE',
                }));
            },
            [filter, getObjectDetails, root],
        );

        const [searchText, setSearchText] = React.useState<string>('');
        const inputRef = React.useRef<HTMLInputElement | null>(null);

        const [listOpenedItems, setListOpenedItems] = React.useState<{
            key: string;
            children: string[];
        }>({
            key: '',
            children: [],
        });
        const handleOpenChild = React.useCallback((key: string, children: string[]) => {
            setListOpenedItems(currentItems => ({
                ...currentItems,
                [key]: children,
            }));
        }, []);
        const handleCloseChild = React.useCallback(
            (key: string) => {
                const updatedOpenedItems = { ...listOpenedItems };
                delete updatedOpenedItems[key as keyof typeof listOpenedItems];
                setListOpenedItems(updatedOpenedItems);
            },
            [listOpenedItems],
        );
        const onClearButtonClicked = React.useCallback((): void => {
            setSearchText('');
            if (inputRef?.current) {
                inputRef.current.focus();
            }
        }, [setSearchText, inputRef]);

        const onSearchTextChanged = React.useCallback((ev: React.ChangeEvent<HTMLInputElement>) => {
            setSearchText(ev.target.value);
        }, []);

        const treeComponent = (
            <Tree
                checkedItems={checkedItems || {}}
                element={rootElement}
                fetchItems={fetchItems}
                isDisabled={isDisabled}
                level={0}
                listOpenedItems={listOpenedItems}
                localize={localize}
                onCheckedItemsUpdated={onCheckedItemsUpdated}
                onCloseChild={handleCloseChild}
                onOpenChild={handleOpenChild}
                searchText={searchText}
                isReadOnly={isReadOnly}
                selectionMode={selectionMode}
                isFolderIconHidden={selectionMode === 'button'}
            />
        );

        return (
            rootElement && (
                <>
                    <div data-testid="e-data-step-search">
                        <Textbox
                            data-testid="e-node-browser-filter"
                            ref={inputRef}
                            onKeyDown={(e: React.KeyboardEvent): void => {
                                // On hitting the enter key the component reloads the page for some reason
                                if (e.key === 'Enter') {
                                    e.preventDefault();
                                }
                            }}
                            disabled={isDisabled}
                            onChange={onSearchTextChanged}
                            placeholder="Search..."
                            aria-label="Search..."
                            leftChildren={<Icon type="search" bgSize="large" />}
                            value={searchText}
                            mb={1}
                        >
                            {!!searchText && (
                                <IconButton
                                    onClick={onClearButtonClicked}
                                    aria-label={localize(
                                        '@sage/xtrem-document-editor/clear-filter-text',
                                        'Clear filter text',
                                    )}
                                >
                                    <Icon type="cross" />
                                </IconButton>
                            )}
                        </Textbox>
                    </div>
                    <div className="e-data-node-step-tree-container" data-testid="e-data-step-tree-container">
                        <div className="e-data-node-step-tree-wrapper">{treeComponent}</div>
                    </div>
                </>
            )
        );
    },
);

NodeBrowserTree.displayName = 'NodeBrowserTree';
