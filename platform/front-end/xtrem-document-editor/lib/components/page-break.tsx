import * as React from 'react';
import { type TableUtilsPluginConstructorType } from './use-table-row-attr-command';
import { ATTR_BREAK_PAGE_AFTER, type LocalizeFunction } from '@sage/xtrem-shared';
import { GridColumn } from '@sage/xtrem-ui-components';
import Switch from 'carbon-react/esm/components/switch';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import { useQueryTableRowPropertyCommand } from './use-table-row-attr-command';

export interface PageBreakProps {
    title: string;
    commandName: string;
    editor: MultiRootEditor | null;
    tableUtilsPluginConstructor: TableUtilsPluginConstructorType;
    localize: LocalizeFunction;
    isEnabled: boolean;
}

export function PageBreak({
    title,
    editor,
    commandName,
    tableUtilsPluginConstructor,
    isEnabled,
}: PageBreakProps): React.ReactElement | null {
    const attrName = ATTR_BREAK_PAGE_AFTER;
    const { value, onChange } = useQueryTableRowPropertyCommand({
        commandName,
        editor,
        tableUtilsPluginConstructor,
        attrName,
    });

    const onSwitchChange = React.useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            onChange(event.target.checked);
        },
        [onChange],
    );

    if (!isEnabled || !editor) {
        return null;
    }

    return (
        <GridColumn columnSpan={4}>
            <label className="field-label">{title}</label>
            <Switch marginTop="12px" size="small" name="pageBreak" onChange={onSwitchChange} checked={value} />
        </GridColumn>
    );
}
