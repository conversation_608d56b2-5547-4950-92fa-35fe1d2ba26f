import * as React from 'react';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import { GridColumn } from '@sage/xtrem-ui-components';

export interface EditorActionButtonProps {
    commandName: string;
    editor: MultiRootEditor | null;
    label: string;
    isEnabled: boolean;
}

export function EditorActionButton({
    commandName,
    editor,
    label,
    isEnabled,
}: EditorActionButtonProps): React.ReactElement | null {
    const onButtonClick = React.useCallback(() => {
        editor?.execute(commandName);
    }, [editor, commandName]);

    if (!isEnabled) {
        return null;
    }
    return (
        <GridColumn columnSpan={2}>
            <ButtonMinor fullWidth={true} onClick={onButtonClick}>
                {label}
            </ButtonMinor>
        </GridColumn>
    );
}
