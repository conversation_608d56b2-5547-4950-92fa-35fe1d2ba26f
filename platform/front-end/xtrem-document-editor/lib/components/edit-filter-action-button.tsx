import * as React from 'react';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import type { CarbonLocaleType } from '@sage/xtrem-ui-components';
import { GridColumn } from '@sage/xtrem-ui-components';
import { COMMAND_EDIT_FILTER_QUERY_TABLE } from '../constants';
import Dialog from 'carbon-react/esm/components/dialog';
import Form from 'carbon-react/esm/components/form';
import Button from 'carbon-react/esm/components/button';
import type { FilterProperty, LocalizeEnumFunction, LocalizeFunction, LocalizeLocale } from '@sage/xtrem-shared';
import type { DataModelProperty, DocumentContextProvider } from '../context-provider';
import { getContextFilter, getContextObjectType, getSelectedItemsFromProperties } from '../plugins/utils';
import Loader from 'carbon-react/esm/components/loader';
import { FiltersComponent } from './filter/filters-component';
import { isEmpty } from 'lodash';
import { getDeepObjectDetails } from './utils';

export interface EditFilterActionButtonProps {
    carbonLocale: CarbonLocaleType;
    contextProvider: DocumentContextProvider;
    editor: MultiRootEditor | null;
    locale: LocalizeLocale;
    localize: LocalizeFunction;
    localizeEnumMember: LocalizeEnumFunction;
    isEnabled: boolean;
}

export function EditFilterActionButton({
    carbonLocale,
    contextProvider,
    editor,
    locale,
    localize,
    localizeEnumMember,
    isEnabled,
}: EditFilterActionButtonProps): React.ReactElement | null {
    const [isOpen, setOpen] = React.useState(false);
    const [properties, setProperties] = React.useState<DataModelProperty[] | null>(null);
    const [filters, setFilters] = React.useState<Array<FilterProperty>>([]);
    const [contextObjectType, setContextObjectType] = React.useState<string | null>(null);

    React.useEffect(() => {
        setProperties(null);
        if (editor && isOpen) {
            const focusedElement = editor.model.document.selection.focus?.parent;
            const objectType = getContextObjectType(focusedElement);
            setContextObjectType(objectType);
            setFilters(getContextFilter(focusedElement) || []);
        }
    }, [contextProvider, editor, isOpen]);

    React.useEffect(() => {
        if (editor && isEnabled && isOpen && contextObjectType) {
            const referenceIds = filters.filter(obj => obj.id && obj.id.includes('.')).map(obj => obj.id);
            getDeepObjectDetails(contextProvider, contextObjectType, referenceIds).then(dataModel => {
                setProperties(dataModel);
            });
        }
    }, [isEnabled, isOpen, contextProvider, editor, contextObjectType, filters]);

    const onSaveOrder = React.useCallback(() => {
        if (editor) {
            editor.execute(COMMAND_EDIT_FILTER_QUERY_TABLE, { filters });
        }
        setOpen(false);
    }, [editor, filters]);

    const selectedItems = React.useMemo(() => getSelectedItemsFromProperties(properties), [properties]);

    if (!isEnabled) {
        return null;
    }

    return (
        <>
            <Dialog
                open={isOpen}
                onCancel={(): void => setOpen(false)}
                title={localize('@sage/xtrem-document-editor/edit-filters-dialog-title', 'Edit Filters')}
                className="document-editor-edit-filter-dialog"
                size="extra-large"
                height="600px"
            >
                <Form
                    onSubmit={(ev: React.FormEvent<HTMLFormElement>): void => {
                        ev.preventDefault();
                    }}
                    stickyFooter
                    height="500px"
                    saveButton={
                        <Button buttonType="primary" type="submit" onClick={onSaveOrder}>
                            {localize('@sage/xtrem-document-editor/apply-button', 'Apply')}
                        </Button>
                    }
                >
                    {(isEmpty(properties) || !contextObjectType) && <Loader />}
                    {!isEmpty(properties) && properties && contextObjectType && (
                        <FiltersComponent
                            carbonLocale={carbonLocale}
                            contextProvider={contextProvider}
                            filters={filters}
                            locale={locale}
                            localize={localize}
                            localizeEnumMember={localizeEnumMember}
                            objectType={contextObjectType}
                            onChange={setFilters}
                            selectedItems={selectedItems}
                        />
                    )}
                </Form>
            </Dialog>

            <GridColumn columnSpan={2}>
                <ButtonMinor
                    fullWidth={true}
                    onClick={(): void => setOpen(true)}
                    data-testid="document-editor-edit-filter-button"
                >
                    {localize('@sage/xtrem-document-editor/edit-filters-button', 'Edit filters')}
                </ButtonMinor>
            </GridColumn>
        </>
    );
}
