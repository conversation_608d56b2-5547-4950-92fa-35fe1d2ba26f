import * as React from 'react';
import { ButtonToggle, ButtonToggleGroup } from 'carbon-react/esm/components/button-toggle';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import type { TableUtilsPluginConstructorType } from './use-table-cell-property-command';
import { useQueryTableCellPropertyCommand } from './use-table-cell-property-command';
import { GridColumn } from '@sage/xtrem-ui-components';
import type { LocalizeFunction } from '@sage/xtrem-shared';

export interface CellColorPickerProps {
    title: string;
    commandName: string;
    editor: MultiRootEditor | null;
    tableUtilsPluginConstructor: TableUtilsPluginConstructorType;
    localize: LocalizeFunction;
    isEnabled: boolean;
}

export function HorizontalCellAlignment({
    title,
    editor,
    commandName,
    tableUtilsPluginConstructor,
    localize,
    isEnabled,
}: CellColorPickerProps): React.ReactElement | null {
    const { value, onChange } = useQueryTableCellPropertyCommand({
        commandName,
        editor,
        tableUtilsPluginConstructor,
        defaultValue: 'left',
    });

    const onSelectChange = React.useCallback(
        (_ev: React.MouseEvent<HTMLButtonElement>, alignment: string) => {
            onChange(alignment);
        },
        [onChange],
    );

    if (!isEnabled || !editor) {
        return null;
    }

    return (
        <GridColumn columnSpan={4}>
            <ButtonToggleGroup id="button-toggle-group-id" label={title} value={value} onChange={onSelectChange}>
                <ButtonToggle value="left">
                    {localize('@sage/xtrem-document-editor/horizontal-alignment-left', 'Left')}
                </ButtonToggle>
                <ButtonToggle value="center">
                    {localize('@sage/xtrem-document-editor/horizontal-alignment-center', 'Center')}
                </ButtonToggle>
                <ButtonToggle value="right">
                    {localize('@sage/xtrem-document-editor/horizontal-alignment-right', 'Right')}
                </ButtonToggle>
            </ButtonToggleGroup>
        </GridColumn>
    );
}
