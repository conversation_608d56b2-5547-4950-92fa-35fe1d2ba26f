import * as React from 'react';
import Numeric from 'carbon-react/esm/components/number';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import type { TableUtilsPluginConstructorType } from './use-table-cell-property-command';
import { useQueryTableCellPropertyCommand } from './use-table-cell-property-command';
import { GridColumn } from '@sage/xtrem-ui-components';

export interface CellColorPickerProps {
    title: string;
    commandName: string;
    editor: MultiRootEditor | null;
    tableUtilsPluginConstructor: TableUtilsPluginConstructorType;
    isEnabled: boolean;
}

export function MinimumCellHeight({
    title,
    editor,
    commandName,
    tableUtilsPluginConstructor,
    isEnabled,
}: CellColorPickerProps): React.ReactElement | null {
    const { value, onChange } = useQueryTableCellPropertyCommand({
        commandName,
        editor,
        tableUtilsPluginConstructor,
    });

    const [inputValue, setInputValue] = React.useState<number | string | undefined>(value || '24 px');

    React.useEffect(() => {
        setInputValue(value || 24);
    }, [value]);

    const onSelectChange = React.useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        setInputValue(Number(event.target.value));
    }, []);

    const onBlur = React.useCallback(() => {
        onChange(String(inputValue));
    }, [onChange, inputValue]);

    if (!isEnabled || !editor) {
        return null;
    }

    return (
        <GridColumn columnSpan={4}>
            <Numeric
                onBlur={onBlur}
                id={commandName}
                label={title}
                value={String(inputValue)}
                onChange={onSelectChange}
            />
        </GridColumn>
    );
}
