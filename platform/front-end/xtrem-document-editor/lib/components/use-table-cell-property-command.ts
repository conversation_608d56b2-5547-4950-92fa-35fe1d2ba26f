import type { Editor, Plugin } from '@ckeditor/ckeditor5-core';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import type { DocumentSelection, Element, Selection } from '@ckeditor/ckeditor5-engine';
import * as React from 'react';

interface TableUtilsPluginType extends Plugin {
    getSelectionAffectedTableCells: (selection: Selection | DocumentSelection) => Array<Element>;
}

export type TableUtilsPluginConstructorType = new (editor: Editor) => TableUtilsPluginType;

export interface TableCellPropertyCommandProps {
    commandName: string;
    editor: MultiRootEditor | null;
    tableUtilsPluginConstructor: TableUtilsPluginConstructorType;
    defaultValue?: any;
}

export interface TableCellPropertyProps {
    commandName: string;
    editor: MultiRootEditor | null;
    tableUtilsPluginConstructor: TableUtilsPluginConstructorType;
    defaultValue?: any;
}

export const useQueryTableCellPropertyCommand = ({
    commandName,
    editor,
    tableUtilsPluginConstructor,
    defaultValue = '',
}: TableCellPropertyCommandProps): {
    value: string;
    onChange: (value: string) => void;
} => {
    const [value, setValue] = React.useState<string>(defaultValue);

    const onEditorChange = React.useCallback(() => {
        const tableUtils = editor?.plugins.get(tableUtilsPluginConstructor);
        if (editor) {
            const selectedTableCells = tableUtils?.getSelectionAffectedTableCells(editor.model.document.selection);
            if (selectedTableCells?.[0]) {
                const currentValue = selectedTableCells[0].getAttribute(commandName) as string;
                if (currentValue !== value) {
                    setValue(currentValue || defaultValue);
                }
            }
        }
    }, [editor, commandName, value, tableUtilsPluginConstructor, defaultValue]);

    React.useEffect(() => {
        onEditorChange();
        editor?.model.document.on('change', onEditorChange);
        return (): void => {
            editor?.model.document.off('change', onEditorChange);
        };
    }, [editor, commandName, onEditorChange]);

    const onChange = React.useCallback(
        (value: string) => {
            setValue(value);
            editor?.execute(commandName, { value });
        },
        [editor, commandName],
    );

    return { value, onChange };
};
