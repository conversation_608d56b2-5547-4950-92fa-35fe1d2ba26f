import * as React from 'react';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import { COMMAND_CHANGE_PROPERTY_FORMAT } from '../constants';
import { GridColumn } from '@sage/xtrem-ui-components';
import { usePropertyTypeAndFormat } from './use-property-type-and-format';
import { Select, Option } from 'carbon-react/esm/components/select';
import NumberInput from 'carbon-react/esm/components/number';
import { isNil } from 'lodash';
import type { LocalizeFunction } from '@sage/xtrem-shared';

export interface PropertyFormatEditorProps {
    editor: MultiRootEditor | null;
    localize: LocalizeFunction;
    isEnabled: boolean;
}

export function PropertyFormatEditor({
    editor,
    localize,
    isEnabled,
}: PropertyFormatEditorProps): React.ReactElement | null {
    const { propertyFormat, propertyType } = usePropertyTypeAndFormat(editor);

    const onDateFormatChange = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>): void => {
            editor?.execute(COMMAND_CHANGE_PROPERTY_FORMAT, { format: ev.target.value });
        },
        [editor],
    );

    const onScaleChange = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>): void => {
            const format = isNil(ev.target.value) || ev.target.value === '' ? '' : parseInt(ev.target.value, 10);
            editor?.execute(COMMAND_CHANGE_PROPERTY_FORMAT, { format });
        },
        [editor],
    );
    if (!isEnabled || !editor || !propertyType) {
        return null;
    }

    return (
        <GridColumn columnSpan={4}>
            {propertyType === 'Date' && (
                <Select
                    name="dateFormat"
                    label={localize('@sage/xtrem-document-editor/formatting-date-format', 'Date format')}
                    value={propertyFormat as string}
                    onChange={onDateFormatChange}
                    data-testid="control-vertical-property-format-date"
                >
                    <Option
                        key="FullDate"
                        value="FullDate"
                        text={localize('@sage/xtrem-document-editor/formatting-date-format-full-date', 'Full date')}
                    />
                    <Option
                        key="LongMonth"
                        value="LongMonth"
                        text={localize(
                            '@sage/xtrem-document-editor/formatting-date-format-full-month-name',
                            'Full month name',
                        )}
                    />
                    <Option
                        key="LongMonthYear"
                        value="LongMonthYear"
                        text={localize(
                            '@sage/xtrem-document-editor/formatting-date-format-full-month-name-with-year',
                            'Full month name with year',
                        )}
                    />
                    <Option
                        key="MonthDay"
                        value="MonthDay"
                        text={localize(
                            '@sage/xtrem-document-editor/formatting-date-format-month-number-and-day',
                            'Month number and day',
                        )}
                    />
                    <Option
                        key="MonthYear"
                        value="MonthYear"
                        text={localize(
                            '@sage/xtrem-document-editor/formatting-date-format-month-number-and-year',
                            'Month number and year',
                        )}
                    />
                    <Option
                        key="LongMonthDayYear"
                        value="LongMonthDayYear"
                        text={localize(
                            '@sage/xtrem-document-editor/formatting-date-format-year-month-day-number',
                            'Year, month number and day',
                        )}
                    />
                </Select>
            )}
            {(propertyType === 'Float' || propertyType === 'Decimal' || propertyType === 'Int') && (
                <NumberInput
                    name="scale"
                    label={localize('@sage/xtrem-document-editor/formatting-number-precision', 'Precision')}
                    value={propertyFormat ? String(propertyFormat) : ''}
                    onChange={onScaleChange}
                    data-testid="control-vertical-property-format-number"
                />
            )}
        </GridColumn>
    );
}
