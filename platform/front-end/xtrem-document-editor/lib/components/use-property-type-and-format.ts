import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import * as React from 'react';
import type { DataModelPropertyType } from '../context-provider';
import { getSelectedPropertyDataTypeAndFormat } from '../plugins/utils';

export const usePropertyTypeAndFormat = (
    editor: MultiRootEditor | null,
): {
    propertyType: DataModelPropertyType | null;
    propertyFormat: string | number | null;
} => {
    const [propertyType, setPropertyType] = React.useState<DataModelPropertyType | null>(null);
    const [propertyFormat, setPropertyFormat] = React.useState<string | number | null>(null);

    const onEditorChange = React.useCallback(() => {
        if (editor) {
            const dataTypeAndFormat = getSelectedPropertyDataTypeAndFormat(editor);
            setPropertyType(dataTypeAndFormat?.dataType || null);
            setPropertyFormat(dataTypeAndFormat?.format || null);
        }
    }, [editor]);

    React.useEffect(() => {
        onEditorChange();
        editor?.model.document.on('change', onEditorChange);
        return (): void => {
            editor?.model.document.off('change', onEditorChange);
        };
    }, [editor, onEditorChange]);

    return { propertyType, propertyFormat };
};
