import { cloneDeep } from 'lodash';
import type { DataModelProperty, DocumentContextProvider } from '../context-provider';

/**
 * This function will retrieve the Node Data for a context object type ex: 'Item'
 * and will also retrieve the Node Data for any reference objects ex: 'category.name'
 */
export const getDeepObjectDetails = async (
    contextProvider: DocumentContextProvider,
    contextObjectType: string,
    referenceIds: string[],
): Promise<DataModelProperty[]> => {
    const nodeData: DataModelProperty[] = await contextProvider.getObjectDetails({
        objectType: contextObjectType,
        contextType: 'body',
        canParentFilter: true,
        canParentSort: true,
    });
    let currContextObjectType: string | undefined = contextObjectType;
    if (referenceIds.length > 0) {
        let newData: DataModelProperty[] = nodeData;
        const mappedAsyncReferences = await Promise.all(
            referenceIds.map(async referenceId => {
                // Custom fields are deep bound but they are not references
                const propDetails = nodeData.find(node => node.name === referenceId);
                if (propDetails) {
                    return propDetails;
                }
                const referenceParts = referenceId.split('.');
                let clonedReferenceObject: DataModelProperty | undefined;

                for (let i = 0; i < referenceParts.length; i += 1) {
                    currContextObjectType = newData.find(node => node.name === referenceParts[i])?.type;

                    if (!currContextObjectType) {
                        throw new Error(`Reference type not found for ${referenceId}`);
                    }

                    const referenceObject = newData.find(node => node.name === referenceParts[i]);
                    if (i === referenceParts.length - 1) {
                        if (!referenceObject) throw new Error(`Reference object not found for ${referenceId}`);
                        clonedReferenceObject = cloneDeep(referenceObject);
                        clonedReferenceObject.name = referenceId;
                        clonedReferenceObject.node = currContextObjectType;
                        return clonedReferenceObject;
                    }
                    newData = await contextProvider.getObjectDetails({
                        objectType: currContextObjectType,
                        contextType: 'body',
                        canParentFilter: referenceObject?.canFilter || false,
                        canParentSort: referenceObject?.canSort || false,
                    });
                }

                return clonedReferenceObject;
            }),
        );

        const resolvedReferences = mappedAsyncReferences.filter(Boolean) as DataModelProperty[];

        return [...nodeData, ...resolvedReferences];
    }
    return nodeData;
};
