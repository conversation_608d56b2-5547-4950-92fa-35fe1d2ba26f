import React from 'react';
import { useQueryTablePropertyCommand } from '../use-table-property-command';
import { type BaseCommandProps } from '../command-types';
import { ColorPicker } from './color-picker';

export function TableColorPicker({
    title,
    commandName,
    editor,
    isEnabled,
}: BaseCommandProps): React.ReactElement | null {
    const { value, onChange } = useQueryTablePropertyCommand({
        commandName,
        editor,
    });

    return (
        <ColorPicker
            title={title}
            commandName={commandName}
            value={value}
            onChange={onChange}
            isEnabled={isEnabled}
            editor={editor}
        />
    );
}
