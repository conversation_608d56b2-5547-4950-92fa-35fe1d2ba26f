import * as React from 'react';
import { THEME_COLORS } from '../../constants';
import AdvancedColorPicker from 'carbon-react/esm/components/advanced-color-picker';
import { GridColumn } from '@sage/xtrem-ui-components';
import { type BaseSelectProps } from '../command-types';

export function ColorPicker({ title, value, onChange, isEnabled, editor }: BaseSelectProps): React.ReactElement | null {
    const [isOpen, setOpen] = React.useState(false);

    const onColorChange = React.useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            setOpen(false);
            onChange(event.target.value);
        },
        [onChange],
    );

    if (!isEnabled || !editor) {
        return null;
    }

    return (
        <GridColumn columnSpan={2}>
            <label className="field-label">{title}</label>
            <AdvancedColorPicker
                name="advancedPicker"
                availableColors={THEME_COLORS.map(c => ({ value: c, label: c }))}
                defaultColor={value}
                selectedColor={value}
                onChange={onColorChange}
                onOpen={(): void => {
                    setOpen(!isOpen);
                }}
                onClose={(): void => {
                    setOpen(false);
                }}
                open={isOpen}
            />
        </GridColumn>
    );
}
