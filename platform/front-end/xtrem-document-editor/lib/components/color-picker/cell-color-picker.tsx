import * as React from 'react';
import { useQueryTableCellPropertyCommand } from '../use-table-cell-property-command';
import { type CellCommandProps } from '../command-types';
import { ColorPicker } from './color-picker';

export function CellColorPicker({
    title,
    commandName,
    editor,
    isEnabled,
    tableUtilsPluginConstructor,
}: CellCommandProps): React.ReactElement | null {
    const { value, onChange } = useQueryTableCellPropertyCommand({
        commandName,
        editor,
        tableUtilsPluginConstructor,
    });

    return (
        <ColorPicker
            title={title}
            commandName={commandName}
            value={value}
            onChange={onChange}
            isEnabled={isEnabled}
            editor={editor}
        />
    );
}
