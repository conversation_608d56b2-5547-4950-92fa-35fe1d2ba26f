import type { Command } from '@ckeditor/ckeditor5-core';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import { useEffect, useState } from 'react';
import { FORMATTING_COMMANDS } from '../constants';

export const useEditorCommandState = (editorRef: MultiRootEditor | null): any => {
    const [commandStates, setCommandStates] = useState<Record<string, boolean>>({});

    useEffect(() => {
        const commands: Record<string, Command> = {};

        const updateCommandStates = (): void => {
            const newCommandStates: Record<string, boolean> = {};
            Object.keys(commands).forEach(commandName => {
                newCommandStates[commandName] = commands[commandName].isEnabled;
            });
            setCommandStates(newCommandStates);
        };

        if (editorRef) {
            const commandsList = FORMATTING_COMMANDS;

            commandsList.forEach(commandName => {
                const command = editorRef.commands.get(commandName) as Command;
                if (command) {
                    commands[commandName] = command;
                    command.on('change:isEnabled', updateCommandStates);
                }
            });

            updateCommandStates();
        }

        return (): void => {
            Object.keys(commands).forEach(commandName => {
                commands[commandName].off('change:isEnabled', updateCommandStates);
            });
        };
    }, [editorRef]);

    return commandStates;
};
