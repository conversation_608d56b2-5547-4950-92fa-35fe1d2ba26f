import type { AggregationOptions, Dict } from '@sage/xtrem-shared';
import type { Order, TableContentWithGroupsProps } from '@sage/xtrem-ui-components';
import type { DataModelProperty } from '../../context-provider';

export type TableContentValue = Omit<TableContentWithGroupsProps['value'][number], '_id'>[];

export function getAggregationsFromItems(tableContentValue: TableContentValue): Dict<AggregationOptions> {
    return tableContentValue.reduce<Dict<AggregationOptions>>((prevValue, row) => {
        prevValue[row.path] = {
            group: row.group || 0,
            operation: row.operation || 'NONE',
        };
        return prevValue;
    }, {});
}

export function getSelectedFieldsFromItems(tableContentValue: TableContentValue): Dict<DataModelProperty> {
    return tableContentValue.reduce<Dict<DataModelProperty>>((prevValue, row) => {
        if (row.property) {
            prevValue[row.path] = row.property.data;
        }
        return prevValue;
    }, {});
}

export function getOrderByFromItems(tableContentValue: TableContentValue): Dict<Order> {
    return tableContentValue
        .filter(i => Boolean(i.property?.data.canSort))
        .reduce<Dict<Order>>((prevValue, row) => {
            const sorting = row.sorting;
            if (sorting) {
                prevValue[row.path] = sorting;
            }
            return prevValue;
        }, {} as Dict<Order>);
}
