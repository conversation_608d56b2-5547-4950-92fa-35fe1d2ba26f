import Button from 'carbon-react/esm/components/button';
import Dialog from 'carbon-react/esm/components/dialog';
import Form from 'carbon-react/esm/components/form';
import * as React from 'react';
import { NodeBrowserTree } from '../node-browser-tree/node-browser-tree';
import type { DocumentContextProvider, DataModelProperty, ContextType } from '../../context-provider';
import {
    TableContentWithGroups,
    getMainPresentation,
    enforceContiguousOrder,
    isEnumField,
} from '@sage/xtrem-ui-components';
import type { TableContentWithGroupsProps, Order, CarbonLocaleType } from '@sage/xtrem-ui-components';
import { FiltersComponent, type FiltersComponentProps } from '../filter/filters-component';
import { GraphQLTypes, FieldKey, Sortings } from '@sage/xtrem-shared';
import type {
    Aggregations,
    Dict,
    FilterProperty,
    GridNestedFieldTypes,
    LocalizeEnumFunction,
    LocalizeFunction,
    LocalizeLocale,
} from '@sage/xtrem-shared';
import { StepSequence, StepSequenceItem } from 'carbon-react/esm/components/step-sequence';
import Typography from 'carbon-react/esm/components/typography/typography.component';
import { SelectionCard } from '../selection-card/selection-card';
import Loader from 'carbon-react/esm/components/loader';
import { SortingComponent } from '../sorting/sorting-component';
import { getSelectedItemsFromProperties } from '../../plugins/utils';
import { chain, isEmpty, isEqual, isNil, omit } from 'lodash';
import {
    type TableContentValue,
    getAggregationsFromItems,
    getOrderByFromItems,
    getSelectedFieldsFromItems,
} from './insert-list-property-dialog-utils';
import type { ToastProps } from 'carbon-react/esm/components/toast';

export type SelectionType = 'property' | 'recordContext' | 'list';

export interface GroupAggregation {
    group: number;
    operation: Aggregations | 'NONE';
}
export interface DocumentEditorInsertDialogResult {
    selectedFields?: Dict<DataModelProperty> | null;
    aggregations?: Dict<GroupAggregation>;
    filters?: FiltersComponentProps['filters'];
    orderBy?: Dict<Order>;
    collectionType?: Dict<DataModelProperty> | null;
    selectedMode: 'list' | 'recordContext';
}
export interface LookupRequest {
    root: ContextType;
    contextObjectType: string;
    onSelected: (result: DocumentEditorInsertDialogResult) => void;
}

const noListItems = (nodes: DataModelProperty[]): DataModelProperty[] => nodes.filter(n => n.kind !== 'LIST');

export interface InsertListPropertyDialogProps {
    carbonLocale: CarbonLocaleType;
    contextProvider: DocumentContextProvider;
    locale: LocalizeLocale;
    localize: LocalizeFunction;
    localizeEnumMember: LocalizeEnumFunction;
    lookupRequest?: LookupRequest | null;
    onClose: () => void;
    onDisplayNotification: (content: string, type: ToastProps['variant']) => void;
}

type Step = 'modeSelection' | 'contentDefinition' | 'filterDefinition' | 'orderDefinition' | 'propertySelection';

interface RequestSteps {
    [key: string]: Array<Step>;
}

const requestSteps: RequestSteps = {
    recordContext: ['modeSelection', 'filterDefinition', 'orderDefinition'],
    list: ['modeSelection', 'propertySelection', 'contentDefinition', 'filterDefinition'],
};

export function InsertListPropertyDialog({
    carbonLocale,
    contextProvider,
    locale,
    localize,
    localizeEnumMember,
    lookupRequest,
    onClose,
    onDisplayNotification,
}: InsertListPropertyDialogProps): React.ReactElement {
    const stepTranslations: Record<(typeof requestSteps)[keyof typeof requestSteps][number], string> = {
        contentDefinition: localize(
            '@sage/xtrem-document-editor/property-dialog-step-define-content',
            'Define content',
        ),
        filterDefinition: localize('@sage/xtrem-document-editor/property-dialog-step-define-filters', 'Define filters'),
        modeSelection: localize('@sage/xtrem-document-editor/property-dialog-step-insert-mode', 'Insert mode'),
        orderDefinition: localize(
            '@sage/xtrem-document-editor/property-dialog-step-define-sorting-order',
            'Define sorting order',
        ),
        propertySelection: localize(
            '@sage/xtrem-document-editor/property-dialog-step-column-selection',
            'Column selection',
        ),
    };

    const [properties, setProperties] = React.useState<DataModelProperty[] | null>(null);
    const [selectedItems, setSelectedItems] = React.useState<NonNullable<TableContentWithGroupsProps['selectedItems']>>(
        {},
    );
    const [filters, setFilters] = React.useState<Array<FilterProperty>>([]);
    const [orderBy, setOrderBy] = React.useState<Dict<Order>>({});
    const [groupOrderError, setGroupOrderError] = React.useState<boolean>(false);

    const onGroupOrderError = React.useCallback((value: boolean) => {
        setGroupOrderError(value);
    }, []);

    const [currentStep, setCurrentStep] = React.useState<number>(0);
    const [steps, setSteps] = React.useState<Step[]>(['modeSelection']);
    const [selectedMode, setSelectedMode] = React.useState<'recordContext' | 'list' | null>(null);

    const getTableContentFromSelectedItems = React.useCallback(
        (currentTableContentValue: TableContentValue) => {
            const basicTypes = Object.values(GraphQLTypes);
            const newTableContent = Object.keys(selectedItems).reduce<TableContentValue>((acc, curr) => {
                if (
                    basicTypes.includes(selectedItems[curr].data.type as GraphQLTypes) ||
                    isEnumField(selectedItems[curr].data)
                ) {
                    const property = selectedItems[curr];
                    acc.push({
                        presentation: isEnumField(selectedItems[curr].data)
                            ? FieldKey.Text
                            : (getMainPresentation({
                                  localize,
                                  type: property.data.type as GraphQLTypes,
                              }) as GridNestedFieldTypes),
                        title: property.title ?? property.label,
                        formatting: undefined,
                        divisor: undefined,
                        property,
                        path: property.id ?? property.path,
                        labelPath: property.id ?? property.path,
                        group: property.group ?? 0,
                        operation: property.operation,
                        sorting: property.sorting ?? Sortings.ascending,
                    });
                }
                return acc;
            }, []);
            const existingRows = currentTableContentValue.filter(v => newTableContent.find(t => t.path === v.path));
            const maxGroup = existingRows.reduce((max, curr) => (curr?.group > max ? curr.group : max), 0);

            const newTableContentRows = newTableContent
                .filter(t => existingRows.find(s => s.path === t.path) === undefined)
                .map(e => ({
                    ...e,
                    group: maxGroup,
                }));
            const orderedData = [...existingRows, ...newTableContentRows].map((e, idx) => ({
                ...e,
                _id: String(idx + 1),
            }));

            return enforceContiguousOrder({ orderedData, key: 'group' }).map(row => omit(row, ['_id']));
        },
        [localize, selectedItems],
    );

    const [tableContentValue, setTableContentValue] = React.useState(getTableContentFromSelectedItems([]));

    const isFormValid = React.useCallback((): boolean => {
        const currentStepName = steps[currentStep];
        if (groupOrderError) {
            return false;
        }
        if (currentStepName === 'modeSelection' && selectedMode === null) {
            // The user has to select the mode (record context or table) to proceed
            return false;
        }
        if (currentStepName === 'propertySelection' && isEmpty(selectedItems)) {
            // At least one property must be selected in order to insert a table.
            return false;
        }
        if (
            currentStepName === 'contentDefinition' &&
            !chain(tableContentValue)
                .groupBy(s => s.group)
                .toPairs()
                .map(s => s[1][0])
                .value()
                .every(s => Boolean(s.sorting))
        ) {
            // The first row of each group must be sortable
            return false;
        }
        return true;
    }, [steps, currentStep, groupOrderError, selectedMode, selectedItems, tableContentValue]);

    const onPrimaryButtonClicked = React.useCallback(() => {
        if (currentStep === steps.length - 1 && selectedMode) {
            lookupRequest?.onSelected({
                filters,
                orderBy: selectedMode === 'recordContext' ? orderBy : getOrderByFromItems(tableContentValue),
                selectedMode,
                aggregations: getAggregationsFromItems(tableContentValue),
                selectedFields: getSelectedFieldsFromItems(tableContentValue),
            });
            onClose();
        } else {
            setCurrentStep(currentStep + 1);
        }
    }, [currentStep, steps.length, selectedMode, lookupRequest, filters, orderBy, tableContentValue, onClose]);

    const onPreviousButtonClicked = React.useCallback(() => {
        setCurrentStep(currentStep - 1);
        setGroupOrderError(false);
    }, [currentStep]);

    const getStepStatus = React.useCallback(
        (index: number) => {
            if (currentStep < index) {
                return 'incomplete';
            }

            if (currentStep === index) {
                return 'current';
            }

            return 'complete';
        },
        [currentStep],
    );

    React.useEffect(() => {
        setTableContentValue(getTableContentFromSelectedItems);
    }, [getTableContentFromSelectedItems]);

    React.useEffect(() => {
        setSelectedItems({});
        setCurrentStep(0);
        setSelectedMode(null);
        setProperties(null);
        setFilters([]);
        setOrderBy({});
        if (lookupRequest?.contextObjectType) {
            contextProvider
                .getObjectDetails({
                    objectType: lookupRequest?.contextObjectType,
                    contextType: 'body',
                    canParentFilter: true,
                    canParentSort: true,
                })
                .then(setProperties);
        }
    }, [lookupRequest, contextProvider]);

    const selectAllNodeProperties = React.useCallback(() => {
        setSelectedItems(getSelectedItemsFromProperties(properties));
    }, [properties]);

    React.useEffect(() => {
        setSteps(selectedMode ? requestSteps[selectedMode] : ['modeSelection']);
        // prepopulate selected properties for data context so that filters can be added
        if (selectedMode === 'recordContext') {
            selectAllNodeProperties();
        }
    }, [selectAllNodeProperties, selectedMode]);

    const onContentChange = React.useCallback<TableContentWithGroupsProps['onChange']>(
        (newData): void => {
            const newTableContent = newData.filter(newItem => !isNil(newItem.property));

            if (!isEqual(newTableContent, tableContentValue)) {
                setTableContentValue(newTableContent);
            }

            // make sure filters are still compatible with selected items
            setFilters(currentFilters => currentFilters?.filter(f => newData.find(d => d._id === f.id)));
        },
        [tableContentValue],
    );

    const onTableModeSelected = React.useCallback((): void => {
        setSelectedItems({});
        setSelectedMode('list');
    }, []);

    const onDataContextSelected = React.useCallback((): void => {
        selectAllNodeProperties();
        setSelectedMode('recordContext');
    }, [selectAllNodeProperties]);

    const handleOnClose = React.useCallback(() => {
        setGroupOrderError(false);
        onClose();
    }, [onClose]);

    return (
        <Dialog
            open={!!lookupRequest}
            onCancel={handleOnClose}
            title={localize('@sage/xtrem-document-editor/insert-object-type-dialog-title', 'Insert an object type')}
            className="document-editor-property-selection-dialog"
            size="extra-large"
            height="600px"
        >
            <Form
                onSubmit={(ev: React.FormEvent<HTMLFormElement>): void => {
                    ev.preventDefault();
                }}
                stickyFooter
                height="500px"
                leftSideButtons={
                    currentStep !== 0 ? <Button onClick={onPreviousButtonClicked}>Previous</Button> : undefined
                }
                saveButton={
                    <Button
                        buttonType="primary"
                        type="submit"
                        onClick={onPrimaryButtonClicked}
                        disabled={!isFormValid()}
                    >
                        {steps.length > 1 && currentStep === steps.length - 1
                            ? localize('@sage/xtrem-document-editor/dialog-button-confirm', 'Confirm')
                            : localize('@sage/xtrem-document-editor/dialog-button-next', 'Next')}
                    </Button>
                }
            >
                {!properties && <Loader />}
                {properties && (
                    <>
                        <StepSequence pb="20px">
                            {steps.map((s: Step, index: number): React.ReactElement => {
                                const currentStepNumber = String(index + 1);
                                return (
                                    <StepSequenceItem
                                        key={s}
                                        aria-label={localize(
                                            '@sage/xtrem-document-editor/step-status-count',
                                            'Step {{currentStepNumber}} of {{totalStepCount}}',
                                            {
                                                currentStepNumber,
                                                totalStepCount: steps.length,
                                            },
                                        )}
                                        hiddenCompleteLabel={localize(
                                            '@sage/xtrem-document-editor/step-status-complete',
                                            'Complete',
                                        )}
                                        hiddenCurrentLabel={localize(
                                            '@sage/xtrem-document-editor/step-status-current',
                                            'Current',
                                        )}
                                        indicator={String(index + 1)}
                                        status={getStepStatus(index)}
                                    >
                                        {stepTranslations[s]}
                                    </StepSequenceItem>
                                );
                            })}
                        </StepSequence>
                        <Typography variant="h2" mb="16px">
                            <span>{currentStep + 1}</span>
                            <span>. </span>
                            <span>{stepTranslations[steps[currentStep]]}</span>
                        </Typography>
                        {steps[currentStep] === 'modeSelection' && (
                            <div className="document-editor-mode-selection-container">
                                <SelectionCard
                                    title={localize('@sage/xtrem-document-editor/list-insertion-mode-table', 'Table')}
                                    description={localize(
                                        '@sage/xtrem-document-editor/list-insertion-mode-table-description',
                                        'Inserts a table with configurable columns',
                                    )}
                                    _id="list"
                                    icon="csv"
                                    isSelected={selectedMode === 'list'}
                                    onClick={onTableModeSelected}
                                />
                                <SelectionCard
                                    title={localize(
                                        '@sage/xtrem-document-editor/list-insertion-mode-data-container',
                                        'Data container',
                                    )}
                                    description={localize(
                                        '@sage/xtrem-document-editor/list-insertion-mode-data-context-description',
                                        'Inserts a single record for detailed documents',
                                    )}
                                    _id="recordContext"
                                    icon="document_right_align"
                                    isSelected={selectedMode === 'recordContext'}
                                    onClick={onDataContextSelected}
                                />
                            </div>
                        )}
                        {steps[currentStep] === 'propertySelection' && lookupRequest && (
                            <NodeBrowserTree
                                getObjectDetails={contextProvider.getObjectDetails}
                                onCheckedItemsUpdated={setSelectedItems}
                                checkedItems={selectedItems}
                                filter={noListItems}
                                objectType={lookupRequest?.contextObjectType}
                                selectionMode="multiple"
                                localize={localize}
                                root={lookupRequest.root}
                            />
                        )}
                        {steps[currentStep] === 'contentDefinition' && (
                            <TableContentWithGroups
                                isAddButtonHidden={true}
                                localize={localize}
                                onChange={onContentChange}
                                onDisplayNotification={onDisplayNotification}
                                selectedItems={selectedItems}
                                value={tableContentValue}
                                isPropertySelectionDisabled={true}
                                setGroupOrderError={onGroupOrderError}
                            />
                        )}
                        {steps[currentStep] === 'filterDefinition' && (
                            <FiltersComponent
                                carbonLocale={carbonLocale}
                                contextProvider={contextProvider}
                                filters={filters}
                                locale={locale}
                                localize={localize}
                                localizeEnumMember={localizeEnumMember}
                                objectType={lookupRequest?.contextObjectType}
                                onChange={setFilters}
                                selectedItems={selectedItems}
                            />
                        )}
                        {steps[currentStep] === 'orderDefinition' && (
                            <SortingComponent
                                localize={localize}
                                value={orderBy}
                                properties={properties}
                                onChange={setOrderBy}
                            />
                        )}
                    </>
                )}
            </Form>
        </Dialog>
    );
}
