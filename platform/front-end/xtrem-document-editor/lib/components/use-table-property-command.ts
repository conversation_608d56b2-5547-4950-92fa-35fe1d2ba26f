import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import * as React from 'react';

export interface TablePropertyCommandProps {
    commandName: string;
    editor: MultiRootEditor | null;
    defaultValue?: any;
}

export const useQueryTablePropertyCommand = ({
    commandName,
    editor,
    defaultValue = '',
}: TablePropertyCommandProps): {
    value: string;
    onChange: (value: string) => void;
} => {
    const [value, setValue] = React.useState<string>(defaultValue);

    const onEditorChange = React.useCallback(() => {
        if (editor) {
            const selectedTable = editor.model.document.selection.getSelectedElement();
            if (selectedTable) {
                const currentValue = selectedTable.getAttribute(commandName) as string;
                if (currentValue !== value) {
                    setValue(currentValue || defaultValue);
                }
            }
        }
    }, [editor, commandName, value, defaultValue]);

    React.useEffect(() => {
        onEditorChange();
        editor?.model.document.on('change', onEditorChange);
        return (): void => {
            editor?.model.document.off('change', onEditorChange);
        };
    }, [editor, commandName, onEditorChange]);

    const onChange = React.useCallback(
        (changeValue: string) => {
            setValue(changeValue);
            editor?.execute(commandName, { value: changeValue });
        },
        [editor, commandName],
    );

    return { value, onChange };
};
