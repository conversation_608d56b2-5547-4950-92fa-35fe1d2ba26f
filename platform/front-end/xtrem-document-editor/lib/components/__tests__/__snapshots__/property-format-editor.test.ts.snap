// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`property format editor should edit number precision and downcast it to the view 1`] = `
"<div>
  <p>
    <span class=\\"property\\" data-property-display-label=\\"Net Total\\" data-property-data-type=\\"Float\\" data-property-name=\\"totalWithoutTax\\" data-property-data-format=\\"6\\" data-property-parent-context=\\"$root\\">
      {{formatNumber totalWithoutTax 6}}
    </span>
  </p>
</div>"
`;

exports[`property format editor should remove handlebar helper function if number formatting is removed 1`] = `
"<div>
  <p>
    <span class=\\"property\\" data-property-display-label=\\"Net Total\\" data-property-data-type=\\"Float\\" data-property-name=\\"totalWithoutTax\\" data-property-data-format=\\"\\" data-property-parent-context=\\"$root\\">
      {{totalWithoutTax}}
    </span>
  </p>
</div>"
`;
