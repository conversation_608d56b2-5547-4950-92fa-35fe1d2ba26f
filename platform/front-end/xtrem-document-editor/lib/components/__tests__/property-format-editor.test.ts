import {
    getByTestId,
    getPage,
    getValue,
    openRightPanel,
    setValue,
    waitForSelectorToAppear,
} from '../../__tests__/test-helper';

describe('property format editor', () => {
    it('should remove handlebar helper function if number formatting is removed', async () => {
        const page = getPage();
        setValue(`
        <span
            class="property"
            data-property-display-label="Net Total"
            data-property-data-type="Float"
            data-property-name="totalWithoutTax"
            data-property-data-format="3"
            data-property-parent-context="$root"
        >
            {{formatNumber totalWithoutTax 3}}
        </span>
        `);

        await page.click('.property');
        await openRightPanel();
        const precisionInput = await getByTestId<HTMLInputElement>('control-vertical-property-format-number');
        await precisionInput.click();
        await precisionInput.press('Backspace');
        await precisionInput.press('Backspace');

        await expect(getValue()).resolves.toMatchSnapshot();
        await expect(getValue()).resolves.not.toContain('{{formatNumber');
    });

    it('should edit number precision and downcast it to the view', async () => {
        const page = getPage();
        setValue(`
        <span
            class="property"
            data-property-display-label="Net Total"
            data-property-data-type="Float"
            data-property-name="totalWithoutTax"
            data-property-data-format="3"
            data-property-parent-context="$root"
        >
            {{formatNumber totalWithoutTax 3}}
        </span>
        `);

        await page.click('.property');
        await openRightPanel();
        const precisionInput = await getByTestId<HTMLInputElement>('control-vertical-property-format-number');
        await precisionInput.click();
        await precisionInput.press('Backspace');
        await precisionInput.type('6');

        await expect(getValue()).resolves.toMatchSnapshot();
        await expect(getValue()).resolves.toContain('{{formatNumber totalWithoutTax 6}}');
    });

    it('should show empty state if no element is selected', async () => {
        setValue(`
        <span
            class="property"
            data-property-display-label="Net Total"
            data-property-data-type="Float"
            data-property-name="totalWithoutTax"
            data-property-data-format="3"
            data-property-parent-context="$root"
        >
            {{formatNumber totalWithoutTax 3}}
        </span>
        `);
        await openRightPanel();
        await waitForSelectorToAppear('[data-testid="e-no-rows-found-component"]');
    });

    it('should show empty state if the property does not have formatting options', async () => {
        const page = getPage();
        await setValue(`
        <span class="property" data-property-display-label="Name" data-property-data-type="String" data-property-name="name" data-property-data-format="" data-property-parent-context="$root">
            {{name}}
        </span>
        `);
        await page.click('.property');
        await openRightPanel();
        await waitForSelectorToAppear('[data-testid="e-no-rows-found-component"]');
    });

    it('should not show empty state if the property have formatting options', async () => {
        const page = getPage();
        await setValue(`
        <span
            class="property"
            data-property-display-label="Net Total"
            data-property-data-type="Float"
            data-property-name="totalWithoutTax"
            data-property-data-format="3"
            data-property-parent-context="$root"
        >
            {{formatNumber totalWithoutTax 3}}
        </span>
        `);
        await page.click('.property');
        await openRightPanel();
        await expect(getValue()).resolves.not.toContain('[data-testid="e-no-rows-found-component"]');
    });
});
