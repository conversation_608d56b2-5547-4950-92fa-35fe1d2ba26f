import {
    getPage,
    openDataPropertiesPanel,
    waitForSelectorToAppear,
    getElementValue,
    getValue,
} from '../../__tests__/test-helper';

describe('content step', () => {
    it('should render the right columns', async () => {
        const page = getPage();
        await openDataPropertiesPanel();

        const contextSelector = '[data-testid="e-tree-view-container-label-salesInvoice"]';
        const propertySelector = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-grossTotal"]';
        const nextButtonSelector = '[data-element="form-summary"]';

        await waitForSelectorToAppear(contextSelector);
        await page.click(contextSelector);

        await waitForSelectorToAppear('.document-editor-mode-selection-container');
        await page.click('.e-selection-card-title');
        await page.click(nextButtonSelector);
        await waitForSelectorToAppear(propertySelector);
        await page.click(propertySelector);
        await page.click(nextButtonSelector);

        const columns = ['property', 'title', 'group', 'operation', 'sorting'];

        await Promise.all(columns.map(c => waitForSelectorToAppear(`[data-testid="e-widget-editor-content-${c}-1"]`)));
    });
    it('should update the title column', async () => {
        const page = getPage();
        await openDataPropertiesPanel();

        const contextSelector = '[data-testid="e-tree-view-container-label-salesInvoice"]';
        const propertySelector = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-grossTotal"]';
        const nextButtonSelector = '[data-element="form-summary"]';
        const titleColumnSelector = '[data-testid="e-widget-editor-content-title-1"]';
        const prevButtonSelector = '[data-element="form-footer"] button[type="button"]';

        await waitForSelectorToAppear(contextSelector);
        await page.click(contextSelector);

        await waitForSelectorToAppear('.document-editor-mode-selection-container');
        await page.click('.e-selection-card-title');
        await page.click(nextButtonSelector);
        await waitForSelectorToAppear(propertySelector);
        await page.click(propertySelector);
        await page.click(nextButtonSelector);

        await page.waitForSelector(titleColumnSelector);

        await page.focus(titleColumnSelector);
        await page.type(titleColumnSelector, 'Title Column Test', { delay: 10 });
        await page.click(nextButtonSelector);
        await page.click(prevButtonSelector);
        await expect(getElementValue(titleColumnSelector)).resolves.toEqual('Title Column Test');
        await page.click(nextButtonSelector);
        await page.click(nextButtonSelector);
        await expect(getValue()).resolves.toContain('Title Column Test');
    });
    describe('textstream properties', () => {
        it('should be in the tree', async () => {
            const page = getPage();
            await openDataPropertiesPanel();

            const contextSelector = '[data-testid="e-tree-view-container-label-salesInvoice"]';
            const propertySelector = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-notes"]';
            const nextButtonSelector = '[data-element="form-summary"]';

            await waitForSelectorToAppear(contextSelector);
            await page.click(contextSelector);

            await waitForSelectorToAppear('.document-editor-mode-selection-container');
            await page.click('.e-selection-card-title');
            await page.click(nextButtonSelector);
            await waitForSelectorToAppear(propertySelector);
        });

        it('should not have any operations or sorting', async () => {
            const page = getPage();
            await openDataPropertiesPanel();

            const contextSelector = '[data-testid="e-tree-view-container-label-salesInvoice"]';
            const propertySelector = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-notes"]';
            const nextButtonSelector = '[data-element="form-summary"]';
            const operationSelector = '[data-testid="e-widget-editor-content-operation-1"]';
            const orderSelector = '[data-testid="e-widget-editor-content-sorting-1"]';

            await waitForSelectorToAppear(contextSelector);
            await page.click(contextSelector);

            await waitForSelectorToAppear('.document-editor-mode-selection-container');
            await page.click('.e-selection-card-title');
            await page.click(nextButtonSelector);
            await waitForSelectorToAppear(propertySelector);
            await page.click(propertySelector);
            await page.click(nextButtonSelector);
            const operationElement = await page.$(operationSelector);
            expect(operationElement).toBeNull();
            const orderElement = await page.$(orderSelector);
            expect(orderElement).toBeNull();
        });
        it('should not be filterable', async () => {
            const page = getPage();
            await openDataPropertiesPanel();

            const contextSelector = '[data-testid="e-tree-view-container-label-salesInvoice"]';
            const notesSelector = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-notes"]';
            const orderDateSelector = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-orderDate"]';
            const nextButtonSelector = '[data-element="form-summary"]';
            const addItemSelector = '[data-testid="add-item-button"]';
            const filterPropertySelector = '[data-testid="e-widget-editor-filter-property-1"]';

            await waitForSelectorToAppear(contextSelector);
            await page.click(contextSelector);

            await waitForSelectorToAppear('.document-editor-mode-selection-container');
            await page.click('.e-selection-card-title');
            await page.click(nextButtonSelector);
            await waitForSelectorToAppear(notesSelector);
            await waitForSelectorToAppear(orderDateSelector);
            await page.click(orderDateSelector);
            await page.click(notesSelector);
            await page.click(nextButtonSelector);
            await page.click(nextButtonSelector);
            await waitForSelectorToAppear(addItemSelector);
            await page.click(addItemSelector);
            await waitForSelectorToAppear(filterPropertySelector);
            await expect(getValue()).resolves.not.toContain('Notes');
        });
        it('should not show "groups" select if all properties are non-sortable', async () => {
            const page = getPage();
            await openDataPropertiesPanel();

            const contextSelector = '[data-testid="e-tree-view-container-label-salesInvoice"]';
            const propertySelector = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-notes"]';
            const propertySelector2 = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-queryText"]';
            const propertySelector3 = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-testCustomField"]';
            const nextButtonSelector = '[data-element="form-summary"]';
            const errorMessageSelector = '[data-testid="e-widget-editor-group-order-error-message"]';

            await waitForSelectorToAppear(contextSelector);
            await page.click(contextSelector);

            await waitForSelectorToAppear('.document-editor-mode-selection-container');
            await page.click('.e-selection-card-title');
            await page.click(nextButtonSelector);
            await waitForSelectorToAppear(propertySelector);
            await waitForSelectorToAppear(propertySelector2);
            await waitForSelectorToAppear(propertySelector3);
            await page.click(propertySelector);
            await page.click(propertySelector2);
            await page.click(propertySelector3);

            await page.click(nextButtonSelector);

            const isButtonDisabled = await page.$eval(
                nextButtonSelector,
                button => (button as HTMLButtonElement).disabled == null,
            );
            expect(isButtonDisabled).toBe(true);

            const elementsWithGroupTestId = await page.$$('[data-testid^="e-widget-editor-content-group-"]');
            expect(elementsWithGroupTestId.length).toBe(0);

            const errorMessageExists = await page.$(errorMessageSelector);
            expect(errorMessageExists).not.toBeNull();
        });
        it('should show an error message if group selection not allowed', async () => {
            const page = getPage();
            await openDataPropertiesPanel();

            const contextSelector = '[data-testid="e-tree-view-container-label-salesInvoice"]';
            const notesSelector = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-notes"]';
            const orderDateSelector = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-orderDate"]';
            const orderDateGroupSelector = '[data-testid="e-widget-editor-content-group-1"]';
            const nextButtonSelector = '[data-element="form-summary"]';
            const optionSelector = '[data-element="select-list"] li[data-index="1"]';

            await waitForSelectorToAppear(contextSelector);
            await page.click(contextSelector);

            await waitForSelectorToAppear('.document-editor-mode-selection-container');
            await page.click('.e-selection-card-title');
            await page.click(nextButtonSelector);
            await waitForSelectorToAppear(orderDateSelector);
            await waitForSelectorToAppear(notesSelector);
            await page.click(orderDateSelector);
            await page.click(notesSelector);

            await page.click(nextButtonSelector);

            const isButtonEnabled = await page.$eval(nextButtonSelector, button => !button.hasAttribute('disabled'));
            expect(isButtonEnabled).toBe(true);

            await waitForSelectorToAppear(orderDateGroupSelector);
            await page.click(orderDateGroupSelector);
            await waitForSelectorToAppear(optionSelector);
            await page.click(optionSelector);

            await waitForSelectorToAppear('[data-testid="notification-content"]');
            const toastElement = await page.$('[data-testid="notification-content"]');
            expect(toastElement).toBeTruthy();
        });
        it('should show an error message if group selection not allowed several groups', async () => {
            const page = getPage();
            await openDataPropertiesPanel();

            const contextSelector = '[data-testid="e-tree-view-container-label-salesInvoice"]';
            const notesSelector = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-notes"]';
            const orderDateSelector = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-orderDate"]';
            const deliveryDateSelector = '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-deliveryDate"]';
            const nonSortablePropertySelector =
                '[data-testid="e-tree-view-checkbox e-tree-view-checkbox-label-testCustomField"]';
            const orderDateGroupSelector = '[data-testid="e-widget-editor-content-group-1"]';
            const deliveryDateGroupSelector = '[data-testid="e-widget-editor-content-group-2"]';
            const notesGroupSelector = '[data-testid="e-widget-editor-content-group-3"]';
            const nextButtonSelector = '[data-element="form-summary"]';
            const optionSelector = '[data-element="select-list"] li[data-index="1"]';
            const optionSelectorGroup3 = '[data-element="select-list"] li[data-index="2"]';

            await waitForSelectorToAppear(contextSelector);
            await page.click(contextSelector);

            await waitForSelectorToAppear('.document-editor-mode-selection-container');
            await page.click('.e-selection-card-title');
            await page.click(nextButtonSelector);
            await waitForSelectorToAppear(orderDateSelector);
            await waitForSelectorToAppear(deliveryDateSelector);
            await waitForSelectorToAppear(notesSelector);
            await waitForSelectorToAppear(nonSortablePropertySelector);

            await page.click(orderDateSelector);
            await page.click(deliveryDateSelector);
            await page.click(notesSelector);
            await page.click(nonSortablePropertySelector);

            await page.click(nextButtonSelector);

            const isButtonEnabled = await page.$eval(nextButtonSelector, button => !button.hasAttribute('disabled'));
            expect(isButtonEnabled).toBe(true);

            await waitForSelectorToAppear(orderDateGroupSelector);
            await page.click(orderDateGroupSelector);
            await waitForSelectorToAppear(optionSelector);
            await page.click(optionSelector);

            await waitForSelectorToAppear(notesGroupSelector);
            await page.click(notesGroupSelector);
            await waitForSelectorToAppear(optionSelector);
            await page.click(optionSelector);

            let toastElement = await page.$('[data-testid="notification-content"]');
            expect(toastElement).toBeNull();

            await waitForSelectorToAppear(orderDateGroupSelector);
            await page.click(orderDateGroupSelector);
            await waitForSelectorToAppear(optionSelectorGroup3);
            await page.click(optionSelectorGroup3);

            await waitForSelectorToAppear('[data-testid="notification-content"]');
            toastElement = await page.$('[data-testid="notification-content"]');
            expect(toastElement).toBeTruthy();

            await waitForSelectorToAppear(deliveryDateGroupSelector);
            await page.click(deliveryDateGroupSelector);
            await waitForSelectorToAppear(optionSelectorGroup3);
            await page.click(optionSelectorGroup3);

            await waitForSelectorToAppear('[data-testid="notification-content"]');
            toastElement = await page.$('[data-testid="notification-content"]');
            expect(toastElement).toBeTruthy();
        });
    });
});
