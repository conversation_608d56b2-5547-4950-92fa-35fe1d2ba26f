import {
    executeEditorCommand,
    waitForSelectorToAppear,
    openRightPanel,
    selectEditorRoot,
    getPage,
    waitForSelectorToDisappear,
    getValue,
    restoreEditorValue,
} from '../../__tests__/test-helper';
import { COMMAND_INSERT_QUERY_TABLE } from '../../constants';

describe('insert page break switch on the formatting panel', () => {
    let page;

    beforeAll(() => {
        page = getPage();
    });

    const setupEditorWithTable = async () => {
        await selectEditorRoot();
        await executeEditorCommand(COMMAND_INSERT_QUERY_TABLE, {
            lookupResult: {
                filters: [],
                orderBy: {
                    invoiceNumber: 'ascending',
                    totalWithoutTax: 'ascending',
                    orderDate: 'ascending',
                },
                selectedMode: 'list',
                aggregations: {
                    invoiceNumber: {
                        group: 0,
                        operation: 'NONE',
                    },
                    totalWithoutTax: {
                        group: 0,
                        operation: 'sum',
                    },
                    orderDate: {
                        group: 1,
                        operation: 'distinctCount',
                    },
                },
                selectedFields: {
                    invoiceNumber: {
                        name: 'invoiceNumber',
                        label: 'Invoice Number',
                        kind: 'SCALAR',
                        type: 'String',
                        canFilter: true,
                        canSort: true,
                        enumType: null,
                        isCustom: false,
                        dataType: '',
                        referenceNode: '',
                        isOnInputType: true,
                        isOnOutputType: true,
                    },
                    totalWithoutTax: {
                        name: 'totalWithoutTax',
                        label: 'Net Total',
                        kind: 'SCALAR',
                        type: 'Float',
                        canFilter: true,
                        canSort: true,
                        enumType: null,
                        isCustom: false,
                        dataType: '',
                        referenceNode: '',
                        isOnInputType: true,
                        isOnOutputType: true,
                    },
                    orderDate: {
                        name: 'orderDate',
                        label: 'Order Date',
                        kind: 'SCALAR',
                        type: 'Date',
                        canFilter: true,
                        canSort: true,
                        enumType: null,
                        isCustom: false,
                        dataType: '',
                        referenceNode: '',
                        isOnInputType: true,
                        isOnOutputType: true,
                    },
                },
            },
            selectedItem: {
                label: 'Sales Invoice',
                data: {
                    name: 'salesInvoice',
                    label: 'Sales Invoice',
                    kind: 'LIST',
                    type: 'SalesInvoice',
                    namespace: 'xtremSales',
                    iconType: 'csv',
                    canFilter: false,
                    canSort: false,
                    enumType: null,
                    isCustom: false,
                    dataType: 'salesInvoice',
                    referenceNode: '',
                    isOnInputType: true,
                    isOnOutputType: true,
                },
                id: 'salesInvoice',
                key: 'salesInvoice',
                labelKey: 'Sales Invoice',
                labelPath: 'Sales Invoice',
            },
            remapInfo: {
                path: 'xtremSales.salesInvoice.query.edges',
                subPath: 'node',
            },
        });
        await waitForSelectorToAppear('.query-table');
    };

    const clickOnFooterCell = async () => {
        await page.click('tr[data-footer-group*="0"] > td.query-table-cell');
    };

    beforeEach(async () => {
        await setupEditorWithTable();
    });

    it("if the selector in the table's group footer then the 'Insert page break' switch appears in the formatting panel", async () => {
        await clickOnFooterCell();
        await openRightPanel();

        const insertPageBreakSwitchSelector = '[name="pageBreak"]';
        await waitForSelectorToAppear(insertPageBreakSwitchSelector);
    });

    it("add [data-break-page-after] attr to the group's footer row when the user switches on the 'Insert page break' on the formatting panel", async () => {
        await clickOnFooterCell();
        await openRightPanel();

        const insertPageBreakSwitchSelector = '[name="pageBreak"]';
        await page.click(insertPageBreakSwitchSelector);

        await waitForSelectorToAppear('.query-table-row[data-break-page-after="true"]');
    });

    it("remove [data-break-page-after] attr from the group's footer row when the user switches off the 'Insert page break' on the formatting panel", async () => {
        await clickOnFooterCell();
        await openRightPanel();

        const insertPageBreakSwitchSelector = '[name="pageBreak"]';
        await page.click(insertPageBreakSwitchSelector);
        await page.click(insertPageBreakSwitchSelector);

        await waitForSelectorToDisappear('.query-table-row[data-break-page-after="true"]');
    });

    it("if the user switches on the 'Insert page break' then changes the focus of the selector then changes back to the group's footer then the value is kept", async () => {
        await clickOnFooterCell();
        await openRightPanel();

        const insertPageBreakSwitchSelector = '[name="pageBreak"]';
        await page.click(insertPageBreakSwitchSelector);
        await waitForSelectorToAppear('.query-table-row[data-break-page-after="true"]');

        const updatedEditorValueWithTable = await getValue();
        expect(updatedEditorValueWithTable).toContain(
            '<tr class="query-table-row" data-footer-group="0" data-break-page-after="true">',
        );

        await restoreEditorValue();
        const secondUpdatedEditorValueWithTable = await getValue();
        expect(secondUpdatedEditorValueWithTable).toContain(
            '<tr class="query-table-row" data-footer-group="0" data-break-page-after="true">',
        );
    });
});
