import {
    getByTestId,
    getElementValue,
    getPage,
    insertSimpleQueryTable,
    openRightPanel,
    setValue,
    waitForSelectorToAppear,
    waitForSelectorToDisappear,
} from '../../__tests__/test-helper';

describe('edit order button', () => {
    it('should be visible if the focus is within a table', async () => {
        const page = getPage();
        await openRightPanel();
        await insertSimpleQueryTable();

        await waitForSelectorToDisappear('[data-testid="document-editor-edit-sorting-button"]');
        await page.click('td.query-table-cell');
        await waitForSelectorToAppear('[data-testid="document-editor-edit-sorting-button"]');
        await setValue('<div>EMPTY</div>');
        await waitForSelectorToDisappear('[data-testid="document-editor-edit-sorting-button"]');
    });

    it('should open the filter editor dialog when clicked', async () => {
        const page = getPage();
        await openRightPanel();
        await insertSimpleQueryTable({ orderBy: { invoiceNumber: 'ascending' } });

        await page.click('td.query-table-cell');
        await waitForSelectorToAppear('[data-testid="document-editor-edit-sorting-button"]');
        await page.click('[data-testid="document-editor-edit-sorting-button"]', { offset: { x: 10, y: 10 } });
        await waitForSelectorToAppear('[data-testid="document-editor-sorting-step"]');
    });

    it('should load current sorting order into the dialog', async () => {
        const page = getPage();
        await openRightPanel();
        await insertSimpleQueryTable({ orderBy: { invoiceNumber: 'ascending' } });

        await page.click('td.query-table-cell');
        await page.click('[data-testid="document-editor-edit-sorting-button"]');
        await waitForSelectorToAppear('[data-testid="document-editor-sorting-step"]');

        const line1Property = await getByTestId<HTMLInputElement>('e-widget-editor-sorting-property-1');
        const line1Order = await getByTestId<HTMLInputElement>('e-widget-editor-sorting-order-1');
        await expect(getElementValue(line1Property)).resolves.toEqual('Invoice Number');
        await expect(getElementValue(line1Order)).resolves.toEqual('Ascending');
    });
});
