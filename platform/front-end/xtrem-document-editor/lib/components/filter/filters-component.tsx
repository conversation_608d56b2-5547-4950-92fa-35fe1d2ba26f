/* eslint-disable react/no-unstable-nested-components */

import * as React from 'react';
import type { FilterProperty, LocalizeEnumFunction, LocalizeFunction, LocalizeLocale } from '@sage/xtrem-shared';
import { GraphQLTypes } from '@sage/xtrem-shared';
import type {
    CarbonLocaleType,
    FilterParameter,
    FilterTableProps,
    TableContentWithGroupsProps,
    useFilterTable,
} from '@sage/xtrem-ui-components';
import { GridColumn, GridRow, FilterTableComponent, isEnumField } from '@sage/xtrem-ui-components';
import Loader from 'carbon-react/esm/components/loader';
import type { DataModelProperty, DocumentContextProvider } from '../../context-provider';

export interface FiltersComponentProps {
    carbonLocale: CarbonLocaleType;
    contextProvider: DocumentContextProvider;
    filters: Array<FilterProperty>;
    locale: LocalizeLocale;
    localize: LocalizeFunction;
    localizeEnumMember: LocalizeEnumFunction;
    objectType?: string;
    onChange: (newFilters: Array<FilterProperty>) => void;
    selectedItems: NonNullable<TableContentWithGroupsProps['selectedItems']>;
}

export function FiltersComponent({
    carbonLocale,
    contextProvider,
    filters,
    locale,
    localize,
    localizeEnumMember,
    objectType,
    onChange,
    selectedItems,
}: FiltersComponentProps): React.ReactElement {
    const [targetNodeDetails, setTargetNodeDetails] = React.useState<DataModelProperty[] | null>(null);
    const [parameters, setParameters] = React.useState<FilterParameter[] | null>(null);

    React.useEffect(() => {
        if (objectType) {
            contextProvider.getDocumentParameters().then(setParameters);
            contextProvider
                .getObjectDetails({ objectType, canParentFilter: true, canParentSort: true, contextType: 'body' })
                .then(setTargetNodeDetails);
        }
    }, [contextProvider, objectType]);

    const selectedProperties = React.useMemo(() => {
        const basicTypes = Object.values(GraphQLTypes);
        return Object.keys(selectedItems).reduce<
            NonNullable<Parameters<typeof useFilterTable>[0]['selectedProperties']>
        >((acc, curr) => {
            if (
                basicTypes.includes(selectedItems[curr].data.type as GraphQLTypes) ||
                isEnumField(selectedItems[curr].data)
            ) {
                acc[curr] = selectedItems[curr];
            }
            return acc;
        }, {});
    }, [selectedItems]);

    // TODO: XT-54885 pass node & nodeNames to component
    const nodeNames: FilterTableProps['nodeNames'] = {};
    const node: FilterTableProps['node'] = '';

    if (!targetNodeDetails || !parameters) {
        return <Loader />;
    }

    return (
        <GridRow columns={8} gutter={16} margin={0} verticalMargin={0}>
            <GridColumn columnSpan={8} className="document-editor-filter-editor">
                <FilterTableComponent
                    mode="table"
                    carbonLocale={carbonLocale}
                    value={filters}
                    locale={locale}
                    localize={localize}
                    parameterMode="usage"
                    parameters={parameters}
                    localizeEnumMember={localizeEnumMember}
                    node={node}
                    nodeNames={nodeNames}
                    onChange={onChange}
                    selectedProperties={selectedProperties}
                />
            </GridColumn>
        </GridRow>
    );
}
