import { type Dict, type LocalizeFunction, type TreeElement } from '@sage/xtrem-shared';
import type { Order } from '@sage/xtrem-ui-components';
import { SortConditionEditor } from '@sage/xtrem-ui-components';
import type { DataModelProperty } from '../../context-provider';
import * as React from 'react';
import { getSelectedItemsFromProperties } from '../../plugins/utils';

export interface SortingProps {
    canAddNewLines?: boolean;
    canDrag?: boolean;
    canRemoveLines?: boolean;
    localize: LocalizeFunction;
    onChange: (newOrder: Dict<Order>) => void;
    properties: DataModelProperty[];
    propertyReadOnly?: boolean;
    value: Dict<Order>;
}

export function SortingComponent({
    canAddNewLines = true,
    canDrag = true,
    canRemoveLines = true,
    localize,
    onChange,
    properties,
    propertyReadOnly = false,
    value,
}: SortingProps): React.ReactElement | null {
    const remappedProperties = getSelectedItemsFromProperties(properties);

    return (
        <div className="document-editor-sorting-step" data-testid="document-editor-sorting-step">
            <SortConditionEditor<TreeElement<DataModelProperty>>
                localize={localize}
                value={value}
                canAddNewLines={canAddNewLines}
                canRemoveLines={canRemoveLines}
                canDrag={canDrag}
                properties={remappedProperties}
                propertyReadOnly={propertyReadOnly}
                onChange={onChange}
                gridGutter={16}
            />
        </div>
    );
}
