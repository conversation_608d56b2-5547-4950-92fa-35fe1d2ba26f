import React from 'react';
import { useQueryTablePropertyCommand } from '../use-table-property-command';
import { BorderStyle } from './border-style-select';
import { type BaseCommandProps } from '../command-types';

export function TableBorderStyleSelect({
    title,
    commandName,
    editor,
    isEnabled,
}: BaseCommandProps): React.ReactElement | null {
    const { value, onChange } = useQueryTablePropertyCommand({
        commandN<PERSON>,
        editor,
    });

    return (
        <BorderStyle
            title={title}
            commandName={commandName}
            value={value}
            onChange={onChange}
            isEnabled={isEnabled}
            editor={editor}
        />
    );
}
