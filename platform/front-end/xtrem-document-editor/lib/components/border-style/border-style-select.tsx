import * as React from 'react';
import { Select, Option } from 'carbon-react/esm/components/select';
import { GridColumn } from '@sage/xtrem-ui-components';
import { BORDER_STYLE } from '../../constants';
import { type BaseSelectProps } from '../command-types';
import { type Property } from 'csstype';

export function BorderStyle({
    title,
    commandName,
    value,
    onChange,
    isEnabled,
    editor,
}: BaseSelectProps): React.ReactElement | null {
    const onSelectChange = React.useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            onChange(event.target.value);
        },
        [onChange],
    );

    if (!isEnabled || !editor) {
        return null;
    }

    return (
        <GridColumn columnSpan={2}>
            <Select name={commandName} label={title} value={value} onChange={onSelectChange}>
                {BORDER_STYLE.map(c => (
                    <Option key={c} value={c} text={c}>
                        {c}
                        <span
                            className="document-editor-border-style-preview"
                            style={{
                                borderTopStyle: c as Property.BorderTopStyle,
                            }}
                        />
                    </Option>
                ))}
            </Select>
        </GridColumn>
    );
}
