import * as React from 'react';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import { Element } from '@ckeditor/ckeditor5-engine';
import type { Order } from '@sage/xtrem-ui-components';
import { GridColumn } from '@sage/xtrem-ui-components';
import { COMMAND_EDIT_ORDER_QUERY_TABLE } from '../constants';
import Dialog from 'carbon-react/esm/components/dialog';
import Form from 'carbon-react/esm/components/form';
import Button from 'carbon-react/esm/components/button';
import type { Dict, LocalizeFunction } from '@sage/xtrem-shared';
import type { DataModelProperty, DocumentContextProvider } from '../context-provider';
import { getContextObjectType, getContextSortOrder, isTableElement } from '../plugins/utils';
import { SortingComponent } from './sorting/sorting-component';
import Loader from 'carbon-react/esm/components/loader';
import { isEmpty } from 'lodash';
import { getDeepObjectDetails } from './utils';

export interface EditOrderActionButtonProps {
    editor: MultiRootEditor | null;
    contextProvider: DocumentContextProvider;
    localize: LocalizeFunction;
    isEnabled: boolean;
}

export function EditOrderActionButton({
    editor,
    contextProvider,
    localize,
    isEnabled,
}: EditOrderActionButtonProps): React.ReactElement | null {
    const [isOpen, setOpen] = React.useState(false);
    const [properties, setProperties] = React.useState<DataModelProperty[] | null>(null);
    const [orderBy, setOrderBy] = React.useState<Dict<Order>>({});
    const [contextObjectType, setContextObjectType] = React.useState<string | null>(null);

    const focusElement = editor?.model.document.selection.focus?.parent;
    const [isTable, setIsTable] = React.useState(
        focusElement && focusElement instanceof Element ? isTableElement(focusElement) : false,
    );

    React.useEffect(() => {
        setProperties(null);
        if (editor && isOpen) {
            const focusedElement = editor.model.document.selection.focus?.parent;
            const objectType = getContextObjectType(focusedElement);
            setContextObjectType(objectType);
            setOrderBy(getContextSortOrder(focusedElement) || {});
            setIsTable(focusedElement && focusedElement instanceof Element ? isTableElement(focusedElement) : false);
        }
    }, [contextProvider, editor, isOpen]);

    React.useEffect(() => {
        if (editor && isEnabled && isOpen && contextObjectType) {
            const referenceIds = Object.keys(orderBy).filter(key => key.includes('.'));
            getDeepObjectDetails(contextProvider, contextObjectType, referenceIds).then(dataModel => {
                setProperties(dataModel);
            });
        }
    }, [isEnabled, isOpen, contextProvider, editor, contextObjectType, orderBy]);

    const onSaveOrder = React.useCallback(() => {
        if (editor) {
            editor.execute(COMMAND_EDIT_ORDER_QUERY_TABLE, { orderBy });
        }
        setOpen(false);
    }, [editor, orderBy]);

    if (!isEnabled) {
        return null;
    }

    return (
        <>
            <Dialog
                open={isOpen}
                onCancel={(): void => setOpen(false)}
                title={localize('@sage/xtrem-document-editor/edit-sorting-dialog-title', 'Edit sorting')}
                className="document-editor-edit-sorting-dialog"
                size="extra-large"
                height="600px"
            >
                <Form
                    onSubmit={(ev: React.FormEvent<HTMLFormElement>): void => {
                        ev.preventDefault();
                    }}
                    stickyFooter
                    height="500px"
                    saveButton={
                        <Button buttonType="primary" type="submit" onClick={onSaveOrder}>
                            {localize('@sage/xtrem-document-editor/apply-button', 'Apply')}
                        </Button>
                    }
                >
                    {isEmpty(properties) && <Loader />}
                    {!isEmpty(properties) && properties && (
                        <SortingComponent
                            canAddNewLines={!isTable}
                            canDrag={!isTable}
                            canRemoveLines={!isTable}
                            localize={localize}
                            onChange={setOrderBy}
                            properties={properties}
                            propertyReadOnly={isTable}
                            value={orderBy}
                        />
                    )}
                </Form>
            </Dialog>

            <GridColumn columnSpan={2}>
                <ButtonMinor
                    fullWidth={true}
                    onClick={(): void => setOpen(true)}
                    data-testid="document-editor-edit-sorting-button"
                >
                    {localize('@sage/xtrem-document-editor/edit-sorting-button', 'Edit sorting')}
                </ButtonMinor>
            </GridColumn>
        </>
    );
}
