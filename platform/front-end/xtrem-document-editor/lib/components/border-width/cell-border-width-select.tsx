import * as React from 'react';
import { BorderWidth } from './border-width-select';
import { useQueryTableCellPropertyCommand } from '../use-table-cell-property-command';
import { type CellCommandProps } from '../command-types';

export function CellBorderWidthSelect({
    title,
    commandName,
    editor,
    isEnabled,
    tableUtilsPluginConstructor,
}: CellCommandProps): React.ReactElement | null {
    const { value, onChange } = useQueryTableCellPropertyCommand({
        commandName,
        editor,
        tableUtilsPluginConstructor,
    });

    return (
        <BorderWidth
            title={title}
            commandName={commandName}
            value={value}
            onChange={onChange}
            isEnabled={isEnabled}
            editor={editor}
        />
    );
}
