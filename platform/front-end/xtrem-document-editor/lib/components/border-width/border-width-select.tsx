import * as React from 'react';
import { Select, Option } from 'carbon-react/esm/components/select';
import { GridColumn } from '@sage/xtrem-ui-components';
import { BORDER_WIDTH } from '../../constants';
import { type BaseSelectProps } from '../command-types';

export function BorderWidth({
    title,
    commandName,
    value,
    onChange,
    isEnabled,
    editor,
}: BaseSelectProps): React.ReactElement | null {
    const onSelectChange = React.useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            onChange(event.target.value);
        },
        [onChange],
    );

    if (!isEnabled || !editor) {
        return null;
    }

    return (
        <GridColumn columnSpan={2}>
            <Select name={commandName} label={title} value={value} onChange={onSelectChange}>
                {BORDER_WIDTH.map(c => (
                    <Option key={c} value={c} text={c} />
                ))}
            </Select>
        </GridColumn>
    );
}
