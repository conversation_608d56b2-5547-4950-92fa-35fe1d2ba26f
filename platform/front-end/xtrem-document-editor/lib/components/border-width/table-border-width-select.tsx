import React from 'react';
import { useQueryTablePropertyCommand } from '../use-table-property-command';
import { BorderWidth } from './border-width-select';
import { type BaseCommandProps } from '../command-types';

export function TableBorderWidthSelect({
    title,
    commandName,
    editor,
    isEnabled,
}: BaseCommandProps): React.ReactElement | null {
    const { value, onChange } = useQueryTablePropertyCommand({
        commandN<PERSON>,
        editor,
    });

    return (
        <BorderWidth
            title={title}
            commandName={commandName}
            value={value}
            onChange={onChange}
            isEnabled={isEnabled}
            editor={editor}
        />
    );
}
