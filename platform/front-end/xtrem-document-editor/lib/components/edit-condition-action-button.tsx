import * as React from 'react';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import type { ConditionEditorProperty } from '@sage/xtrem-ui-components';
import { GridColumn } from '@sage/xtrem-ui-components';
import type { LocalizeFunction } from '@sage/xtrem-shared';
import { getContextCondition } from '../plugins/utils';
import type { ConditionDialogRequest } from './condition-dialog';
import { COMMAND_EDIT_CONDITION_BLOCK } from '../constants';

export interface EditConditionActionButtonProps {
    editor: MultiRootEditor | null;
    localize: LocalizeFunction;
    isEnabled: boolean;
    setConditionDialogRequest: (conditionDialogRequest: ConditionDialogRequest) => void;
}

export function EditConditionActionButton({
    editor,
    localize,
    isEnabled,
    setConditionDialogRequest,
}: EditConditionActionButtonProps): React.ReactElement | null {
    const onClick = React.useCallback(() => {
        if (!editor) {
            return;
        }

        const focusedElement = editor.model.document.selection.focus?.parent;
        const conditionDialogRequest: ConditionDialogRequest = {
            existingConditions: getContextCondition(focusedElement) || [],
            onFinished: (conditions: ConditionEditorProperty[]) => {
                if (editor) {
                    editor.execute(COMMAND_EDIT_CONDITION_BLOCK, { conditions });
                }
            },
        };
        setConditionDialogRequest(conditionDialogRequest);
    }, [editor, setConditionDialogRequest]);

    if (!isEnabled) {
        return null;
    }

    return (
        <GridColumn columnSpan={2}>
            <ButtonMinor fullWidth={true} onClick={onClick} data-testid="document-editor-edit-condition-button">
                {localize('@sage/xtrem-document-editor/edit-conditions-button', 'Edit conditions')}
            </ButtonMinor>
        </GridColumn>
    );
}
