import type { NodeDetails } from '@sage/xtrem-shared';
import type { FilterParameter } from '@sage/xtrem-ui-components';
import type { IconType } from 'carbon-react/esm/components/icon';

export type { DocumentEditorInsertDialogResult } from './components/insert-list-property-dialog/insert-list-property-dialog';

export type DataModelProperty = Omit<NodeDetails, 'type'> & {
    namespace?: string;
    type: NodeDetails['type'];
    iconType?: IconType;
};

export type DataModelPropertyType = DataModelProperty['type'];

export interface ObjectInsertionDetails {
    // The path may depend on the insertion mode, for example if we insert a record context or an aggregate query, we may need to add additional path components
    path: string;
    // For example a subpath within a collection, e.g edges for a list query
    subPath?: string;
}

export type ContextType = 'body' | 'header' | 'footer';

export interface GetObjectDetailsFunctionArgs {
    objectType: string;
    canParentFilter: boolean;
    canParentSort: boolean;
    contextType: ContextType;
}

export type GetObjectDetailsFunction = (args: GetObjectDetailsFunctionArgs) => Promise<DataModelProperty[]>;
export type GetDocumentParameterFunction = () => Promise<FilterParameter[]>;
export type OnObjectInsertFunction = (
    path: string,
    property: DataModelProperty,
    insertMode: 'property' | 'recordContext' | 'list',
) => Promise<ObjectInsertionDetails>;

/**
 * The context provider is the bridge between the document editor's data model and the document editor.
 */
export interface DocumentContextProvider {
    /** Provide information about the consuming product's data structure, this information is used to place data tags into the document.
     * - "$root" is used for requesting the top level data context details
     * - "$globalProperties" is used for requesting global variables such as user, date, etc.
     * */
    getObjectDetails: GetObjectDetailsFunction;
    /** List of external parameters that the end user can use in filters  */
    getDocumentParameters: GetDocumentParameterFunction;
    /** This function allows to remap the insertion path before actually inserting the element to the document.
     * For example this is where we can generate the final query path for aggregations in the future.
     *  */
    onObjectInsert: OnObjectInsertFunction;
}
