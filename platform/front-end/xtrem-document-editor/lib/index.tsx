import { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import type { LookupRequest } from './components/insert-list-property-dialog/insert-list-property-dialog';
import { InsertListPropertyDialog } from './components/insert-list-property-dialog/insert-list-property-dialog';
import type { DocumentContextProvider } from './context-provider';
import * as React from 'react';
import { COMMAND_ZOOM_IN, COMMAND_ZOOM_OUT, OBJECT_TYPE_GLOBAL_PROPERTIES, OBJECT_TYPE_ROOT } from './constants';
import { LeftPanel } from './components/editor-panel/left-panel';
import { RightPanel } from './components/editor-panel/right-panel';
import type { ConditionDialogRequest } from './components/condition-dialog';
import { ConditionDialog } from './components/condition-dialog';
import type { LocalizeEnumFunction, LocalizeFunction, LocalizeLocale } from '@sage/xtrem-shared';
import type { ToastProps } from 'carbon-react/esm/components/toast';
import ToggleDataPropertiesPanelUi from './plugins/toggle-data-properties-panel/toggle-data-properties-panel-ui';
import ToggleRightPanelUi from './plugins/toggle-right-panel/toggle-right-panel-ui';
import type { Document } from '@ckeditor/ckeditor5-engine';
import { useMultiRootEditor } from '@ckeditor/ckeditor5-react';
import ToggleGlobalPropertiesPanelUi from './plugins/toggle-global-properties-panel/toggle-global-properties-panel-ui';
import type { EventInfo } from '@ckeditor/ckeditor5-utils';
import { getEditorAttributes, getEditorData, getStaticEditorConfig } from './utils';
import { round } from 'lodash';
import type { CarbonLocaleType } from '@sage/xtrem-ui-components';

export { OBJECT_TYPE_GLOBAL_PROPERTIES, OBJECT_TYPE_ROOT };
export * from './context-provider';
export * as constants from './constants';
export type { InsertQueryTableCommandArgument } from './plugins/query-table/commands/insert-query-table-command';

export type PaperSize = 'letter' | 'legal' | 'tabloid' | 'ledger' | 'a0' | 'a1' | 'a2' | 'a3' | 'a4' | 'a5' | 'a6';
export type PaperOrientation = 'landscape' | 'portrait' | 'fullScreen';

const bodyFragments = ['header', 'body', 'footer'];

export interface XtremDocumentEditorProps {
    carbonLocale: CarbonLocaleType;
    contextProvider: DocumentContextProvider;
    /** HTML value of the footer */
    footerValue?: string;
    /** HTML value of the header */
    headerValue?: string;
    isDisabled?: boolean;
    locale: LocalizeLocale;
    localize: LocalizeFunction;
    localizeEnumMember: LocalizeEnumFunction;
    marginBottom?: number;
    marginLeft?: number;
    marginRight?: number;
    marginTop?: number;
    onBlur?: () => void;
    /** Triggered when the page body changes */
    onChange: (newValue: string) => void;
    onDisplayNotification: (content: string, type: ToastProps['variant']) => void;
    onFocus?: () => void;
    /** Triggered when the footer changes */
    onFooterChange?: (newValue: string) => void;
    /** Triggered when the header changes */
    onHeaderChange?: (newValue: string) => void;
    /** Triggered when the editor instances are loaded */
    onReady?: (editor: MultiRootEditor) => void;
    /** Orientation of the paper displayed in the editor */
    paperOrientation?: PaperOrientation;
    /** Size of the paper displayed in the editor */
    paperSize?: PaperSize;
    /** HTML value of the page body */
    value: string;
}

export function XtremDocumentEditor({
    carbonLocale,
    contextProvider,
    footerValue = '',
    headerValue = '',
    isDisabled,
    locale,
    localize,
    localizeEnumMember,
    marginBottom = 2,
    marginLeft = 2,
    marginRight = 2,
    marginTop = 2,
    onBlur,
    onChange,
    onDisplayNotification,
    onFocus,
    onFooterChange,
    onHeaderChange,
    onReady,
    paperOrientation = 'portrait',
    paperSize = 'a4',
    value,
}: XtremDocumentEditorProps): React.ReactElement {
    const [zoomLevel, setZoomLevel] = React.useState<number>(
        parseFloat(window.localStorage.getItem('DOCUMENT_EDITOR_ZOOM_LEVEL_KEY') || '1'),
    );

    const [lookupRequest, setLookupRequest] = React.useState<LookupRequest | null>(null);
    const [conditionDialogRequest, setConditionDialogRequest] = React.useState<ConditionDialogRequest | null>(null);
    const [isRightPanelOpen, setRightPanelOpen] = React.useState<boolean>(false);
    const [isDataPropertiesPanelOpen, setDataPropertiesPanelOpen] = React.useState<boolean>(false);
    const [isGlobalPropertiesPanelOpen, setGlobalPropertiesPanelOpen] = React.useState<boolean>(false);

    React.useEffect(() => {
        window.localStorage.setItem('DOCUMENT_EDITOR_ZOOM_LEVEL_KEY', zoomLevel.toString());
    }, [zoomLevel]);

    const onGlobalPropertiesPanelOpenChange = React.useCallback((isOpen: boolean) => {
        if (isOpen) {
            setDataPropertiesPanelOpen(false);
        }
        setGlobalPropertiesPanelOpen(isOpen);
    }, []);

    const onDataPropertiesPanelOpenChange = React.useCallback((isOpen: boolean) => {
        if (isOpen) {
            setGlobalPropertiesPanelOpen(false);
        }
        setDataPropertiesPanelOpen(isOpen);
    }, []);

    const onZoomIn = React.useCallback((): void => {
        if (zoomLevel > 1.6) {
            onDisplayNotification(
                localize('@sage/xtrem-document-editor/max-zoom-level-reached', 'Maximum zoom level reached'),
                'warning',
            );
            return;
        }
        setZoomLevel(round(zoomLevel + 0.1, 2));
    }, [localize, onDisplayNotification, zoomLevel]);

    const onZoomOut = React.useCallback((): void => {
        if (zoomLevel < 0.5) {
            onDisplayNotification(
                localize('@sage/xtrem-document-editor/min-zoom-level-reached', 'Minimum zoom level reached'),
                'warning',
            );
            return;
        }
        setZoomLevel(round(zoomLevel - 0.1, 2));
    }, [localize, onDisplayNotification, zoomLevel]);

    const onEditorValueChange = React.useCallback(
        (event: EventInfo<string, unknown>, editor: MultiRootEditor): void => {
            const sourceDocument = event.source as Document;
            if (sourceDocument.differ.getChanges().length === 0) {
                return;
            }
            const rootName = sourceDocument.selection.getFirstRange()?.start.root.rootName;
            const newData = editor.getData({ rootName });

            if (rootName === 'header' && onHeaderChange) {
                onHeaderChange(newData);
            }

            if (rootName === 'footer' && onFooterChange) {
                onFooterChange(newData);
            }

            if (rootName === 'body') {
                onChange(newData);
            }
        },
        [onChange, onFooterChange, onHeaderChange],
    );

    const triggerAllRootChanges = React.useCallback(
        (editor: MultiRootEditor) => {
            const rootNames = editor.model.document.getRootNames();

            rootNames.forEach(rootName => {
                const newData = editor.getData({ rootName });

                if (rootName === 'header' && onHeaderChange) {
                    onHeaderChange(newData);
                }

                if (rootName === 'footer' && onFooterChange) {
                    onFooterChange(newData);
                }

                if (rootName === 'body') {
                    onChange(newData);
                }
            });
        },
        [onChange, onFooterChange, onHeaderChange],
    );

    const {
        data,
        editor: editorRef,
        toolbarElement,
        setData,
        setAttributes,
        editableElements,
    } = useMultiRootEditor({
        editor: MultiRootEditor,
        data: getEditorData({ paperOrientation, value, headerValue, footerValue }),
        onReady,
        onBlur,
        onFocus,
        rootsAttributes: getEditorAttributes({ paperOrientation, marginLeft, marginRight }),
        onChange: onEditorValueChange,
        config: {
            ...getStaticEditorConfig(locale),
            reportEditorConfig: {
                contextProvider,
                openLookup: setLookupRequest,
                onOpenConditionDialog: setConditionDialogRequest,
                onRightPanelOpenChange: setRightPanelOpen,
                onDataPropertiesPanelOpenChange,
                onGlobalPropertiesPanelOpenChange,
                onDisplayNotification,
                localize,
            },
        },
    });

    React.useEffect(() => {
        if (editorRef) {
            const editor = editorRef;

            const triggerChangeEvent = (): void => {
                const sourceDocument = editor.model.document;

                if (!sourceDocument) {
                    return;
                }

                triggerAllRootChanges(editor);
            };

            if (editor.model) {
                triggerChangeEvent();
            }
        }
    }, [editorRef, onChange, onFooterChange, onHeaderChange, triggerAllRootChanges]);

    React.useEffect(() => {
        if (editorRef) {
            const dataPropertiesPanelPlugin = editorRef.plugins.get(
                ToggleDataPropertiesPanelUi.pluginName,
            ) as ToggleDataPropertiesPanelUi;
            const globalPropertiesPanelPlugin = editorRef.plugins.get(
                ToggleGlobalPropertiesPanelUi.pluginName,
            ) as ToggleGlobalPropertiesPanelUi;
            const rightPanelPlugin = editorRef.plugins.get(ToggleRightPanelUi);
            dataPropertiesPanelPlugin.view.isEnabled = !isDisabled;
            globalPropertiesPanelPlugin.view.isEnabled = !isDisabled;
            rightPanelPlugin.view.isEnabled = !isDisabled;
            if (isDisabled) {
                setRightPanelOpen(false);
                setDataPropertiesPanelOpen(false);
                setGlobalPropertiesPanelOpen(false);
            }
        }
    }, [editorRef, isDisabled]);

    React.useEffect(() => {
        if (editorRef) {
            editorRef.on(COMMAND_ZOOM_IN, onZoomIn);
            editorRef.on(COMMAND_ZOOM_OUT, onZoomOut);
        }
        return (): void => {
            if (editorRef) {
                editorRef.off(COMMAND_ZOOM_IN, onZoomIn);
                editorRef.off(COMMAND_ZOOM_OUT, onZoomOut);
            }
        };
    }, [editorRef, zoomLevel, onZoomIn, onZoomOut]);

    React.useEffect(() => {
        // This hook should only trigger on external value updates, that's why the `data` is excluded from the dependency list
        if (value !== data.body || headerValue !== data.header || footerValue !== data.footer) {
            setData(getEditorData({ paperOrientation, value, headerValue, footerValue }));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [setData, footerValue, headerValue, value, paperOrientation]);

    React.useEffect(() => {
        setAttributes(getEditorAttributes({ paperOrientation, marginLeft, marginRight }));
    }, [setAttributes, marginLeft, marginRight, paperOrientation]);

    const footerLabel = React.useMemo(() => localize('@sage/xtrem-document-editor/footer-label', 'Footer'), [localize]);

    const containerStyle: React.CSSProperties = React.useMemo(() => {
        const tableBodyLabel = localize('@sage/xtrem-document-editor/table-body-label', 'Table body');
        const tableGroup1FooterLabel = localize(
            '@sage/xtrem-document-editor/table-group-footer-label',
            'Group {{level}} footer',
            { level: 1 },
        );
        const tableGroup2FooterLabel = localize(
            '@sage/xtrem-document-editor/table-group-footer-label',
            'Group {{level}} footer',
            { level: 2 },
        );
        const tableGroup3FooterLabel = localize(
            '@sage/xtrem-document-editor/table-group-footer-label',
            'Group {{level}} footer',
            { level: 3 },
        );
        const tableGroup4FooterLabel = localize(
            '@sage/xtrem-document-editor/table-group-footer-label',
            'Group {{level}} footer',
            { level: 4 },
        );
        const tableGroup5FooterLabel = localize(
            '@sage/xtrem-document-editor/table-group-footer-label',
            'Group {{level}} footer',
            { level: 5 },
        );
        const tableGroup6FooterLabel = localize(
            '@sage/xtrem-document-editor/table-group-footer-label',
            'Group {{level}} footer',
            { level: 6 },
        );
        const tableGroup7FooterLabel = localize(
            '@sage/xtrem-document-editor/table-group-footer-label',
            'Group {{level}} footer',
            { level: 7 },
        );
        const tableGroup8FooterLabel = localize(
            '@sage/xtrem-document-editor/table-group-footer-label',
            'Group {{level}} footer',
            { level: 8 },
        );
        const translationKeysStyle = {
            '--tableBodyLabel': `"${tableBodyLabel}"`,
            '--tableFooterLabel': `"${footerLabel}"`,
            '--tableGroup1FooterLabel': `"${tableGroup1FooterLabel}"`,
            '--tableGroup2FooterLabel': `"${tableGroup2FooterLabel}"`,
            '--tableGroup3FooterLabel': `"${tableGroup3FooterLabel}"`,
            '--tableGroup4FooterLabel': `"${tableGroup4FooterLabel}"`,
            '--tableGroup5FooterLabel': `"${tableGroup5FooterLabel}"`,
            '--tableGroup6FooterLabel': `"${tableGroup6FooterLabel}"`,
            '--tableGroup7FooterLabel': `"${tableGroup7FooterLabel}"`,
            '--tableGroup8FooterLabel': `"${tableGroup8FooterLabel}"`,
        };
        return zoomLevel === 1
            ? ({ ...translationKeysStyle } as React.CSSProperties)
            : ({
                  transform: `scale(${zoomLevel})`,
                  marginTop: `${(zoomLevel - 1) * 10}cm`,
                  marginBottom: `${(zoomLevel - 1) * 10}cm`,
                  ...translationKeysStyle,
              } as React.CSSProperties);
    }, [localize, footerLabel, zoomLevel]);

    return (
        <div className="document-editor">
            <InsertListPropertyDialog
                carbonLocale={carbonLocale}
                contextProvider={contextProvider}
                locale={locale}
                localize={localize}
                localizeEnumMember={localizeEnumMember}
                lookupRequest={lookupRequest}
                onDisplayNotification={onDisplayNotification}
                onClose={(): void => {
                    setLookupRequest(null);
                }}
            />
            <ConditionDialog
                carbonLocale={carbonLocale}
                conditionRequest={conditionDialogRequest}
                contextProvider={contextProvider}
                editorRef={editorRef}
                locale={locale}
                localize={localize}
                localizeEnumMember={localizeEnumMember}
                onClose={(): void => {
                    setConditionDialogRequest(null);
                }}
            />
            <div className="document-editor__toolbar">{toolbarElement}</div>
            <div
                className={`document-editor__editable-container document-editor__editable-container-${paperOrientation}`}
            >
                <LeftPanel
                    contextProvider={contextProvider}
                    editorRef={editorRef}
                    isOpen={isDataPropertiesPanelOpen}
                    localize={localize}
                    onOpenLookupDialog={setLookupRequest}
                    rootContext={OBJECT_TYPE_ROOT}
                />
                <LeftPanel
                    contextProvider={contextProvider}
                    editorRef={editorRef}
                    isOpen={isGlobalPropertiesPanelOpen}
                    localize={localize}
                    onOpenLookupDialog={setLookupRequest}
                    rootContext={OBJECT_TYPE_GLOBAL_PROPERTIES}
                />
                <RightPanel
                    carbonLocale={carbonLocale}
                    contextProvider={contextProvider}
                    editorRef={editorRef}
                    isOpen={isRightPanelOpen}
                    locale={locale}
                    localize={localize}
                    localizeEnumMember={localizeEnumMember}
                    setConditionDialogRequest={setConditionDialogRequest}
                />
                <div
                    style={containerStyle}
                    className={`ck-page-body ck-page-body-orientation-${paperOrientation} ck-page-body-size-${paperSize}`}
                >
                    {editableElements.map((e: JSX.Element, index) => {
                        const key = paperOrientation === 'fullScreen' ? 'body' : bodyFragments[index];
                        const style: React.CSSProperties = {};
                        if (key === 'header') {
                            style.height = `${marginTop}cm`;
                        }

                        if (key === 'footer') {
                            style.height = `${marginBottom}cm`;
                        }

                        return (
                            <div className={`ck-page-fragment ck-page-fragment-${key}`} style={style} key={key}>
                                {key === 'header' && (
                                    <div className="ck-page-fragment-label-header">
                                        {localize('@sage/xtrem-document-editor/header-label', 'Header')}
                                    </div>
                                )}

                                {e}
                                {key === 'footer' && <div className="ck-page-fragment-label-footer">{footerLabel}</div>}
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
}

export default XtremDocumentEditor;
