@import './ck-editor.scss';
@import './components/selection-card/selection-card.scss';
@import './components/node-browser-tree/node-browser-tree.scss';
@import './components/no-format-options/no-format-options.component.scss';

$fontCarbonIcons: 'CarbonIcons';

@font-face {
    font-family: $fontCarbonIcons;
    src: url('/fonts/carbon-icons-webfont.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* Sage UI font family */
@font-face {
    font-family: 'Sage UI';
    src:
        url('https://fonts.sage.com/Sage_UI-Regular.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_UI-Regular.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Sage UI';
    src:
        url('https://fonts.sage.com/Sage_UI-Medium.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_UI-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Sage UI';
    src:
        url('https://fonts.sage.com/Sage_UI-Medium.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_UI-Medium.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Sage UI';
    src:
        url('https://fonts.sage.com/Sage_UI-Bold.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_UI-Bold.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

iframe#webpack-dev-server-client-overlay {
    display: none !important;
}

.document-editor {
    --tableBodyLabel: 'Table body';
    --tableFooterLabel: 'Footer';
    --tableGroup1FooterLabel: 'Group 1 footer';
    --tableGroup2FooterLabel: 'Group 2 footer';
    --tableGroup3FooterLabel: 'Group 3 footer';
    --tableGroup4FooterLabel: 'Group 4 footer';
    --tableGroup5FooterLabel: 'Group 5 footer';
    --tableGroup6FooterLabel: 'Group 6 footer';
    --tableGroup7FooterLabel: 'Group 7 footer';
    --tableGroup8FooterLabel: 'Group 8 footer';

    border: 1px solid var(--ck-color-base-border);
    border-radius: var(--ck-border-radius);

    /* Set vertical boundaries for the document editor. */
    max-height: 700px;

    /* This element is a flex container for easier rendering. */
    display: flex;
    flex-flow: column nowrap;

    position: relative;
}

.document-editor-left-panel {
    left: 0;
    border-right: 1px solid var(--colorsUtilityMajor100);
}

.document-editor-right-panel {
    right: 0;
    border-left: 1px solid var(--colorsUtilityMajor100);
    display: flex;
    flex-direction: column;
}

.document-editor__toolbar {
    /* Make sure the toolbar container is always above the editable. */
    z-index: 11;

    /* Create the illusion of the toolbar floating over the editable. */
    box-shadow: 0 0 5px hsla(0, 0%, 0%, 0.2);

    /* Use the CKEditor CSS variables to keep the UI consistent. */
    border-bottom: 1px solid var(--ck-color-toolbar-border);
}

/* Adjust the look of the toolbar inside of the container. */
.document-editor__toolbar .ck-toolbar {
    border: 0;
    border-radius: 0;
}

/* Make the editable container look like the inside of a native word processor app. */
.document-editor .document-editor__editable-container {
    padding: calc(2 * var(--ck-spacing-large));
    background: var(--ck-color-base-foreground);

    /* Make it possible to scroll the "page" of the edited content. */
    overflow-y: scroll;

    &.document-editor__editable-container-fullScreen {
        padding: 0;

        .ck-page-fragment>.ck-content {
            padding: 8px;
        }
    }

    .ck-content {
        font-size: 10pt;
    }
}

.document-editor__editable-container>.ck-editor__editable {
    /* Set the dimensions of the "page". */
    width: 15.8cm;
    min-height: 21cm;

    /* Keep the "page" off the boundaries of the container. */
    padding: 1cm 2cm 2cm;

    border: 1px hsl(0, 0%, 82.7%) solid;
    border-radius: var(--ck-border-radius);
    background: white;

    /* The "page" should cast a slight shadow (3D illusion). */
    box-shadow: 0 0 5px hsla(0, 0%, 0%, 0.1);

    /* Center the "page". */
    margin: 0 auto;
}

.document-editor__editable-container>.ck-page-body {
    border: 1px hsl(0, 0%, 82.7%) solid;
    border-radius: var(--ck-border-radius);
    background: white;

    /* The "page" should cast a slight shadow (3D illusion). */
    box-shadow: 0 0 5px hsla(0, 0%, 0%, 0.1);

    /* Center the "page". */
    margin: 0 auto;

    &.ck-page-body-orientation-portrait.ck-page-body-size-a0 {
        width: 84.1cm;
        min-height: 118.8cm;
    }

    &.ck-page-body-orientation-landscape.ck-page-body-size-a0 {
        width: 118.8cm;
        min-height: 84.1cm;
    }

    &.ck-page-body-orientation-portrait.ck-page-body-size-a1 {
        width: 59.4cm;
        min-height: 84.1cm;
    }

    &.ck-page-body-orientation-landscape.ck-page-body-size-a1 {
        width: 84.1cm;
        min-height: 59.4cm;
    }

    &.ck-page-body-orientation-portrait.ck-page-body-size-a2 {
        width: 42cm;
        min-height: 59.4cm;
    }

    &.ck-page-body-orientation-landscape.ck-page-body-size-a2 {
        width: 59.4cm;
        min-height: 42cm;
    }

    &.ck-page-body-orientation-portrait.ck-page-body-size-a3 {
        width: 29.7cm;
        min-height: 42cm;
    }

    &.ck-page-body-orientation-landscape.ck-page-body-size-a3 {
        width: 42cm;
        min-height: 29.7cm;
    }

    &.ck-page-body-orientation-portrait.ck-page-body-size-a4 {
        width: 21cm;
        min-height: 29.7cm;
    }

    &.ck-page-body-orientation-landscape.ck-page-body-size-a4 {
        width: 29.7cm;
        min-height: 21cm;
    }

    &.ck-page-body-orientation-portrait.ck-page-body-size-a5 {
        width: 14.8cm;
        min-height: 21cm;
    }

    &.ck-page-body-orientation-landscape.ck-page-body-size-a5 {
        width: 21cm;
        min-height: 14.8cm;
    }

    &.ck-page-body-orientation-portrait.ck-page-body-size-a6 {
        width: 10.5cm;
        min-height: 14.8cm;
    }

    &.ck-page-body-orientation-landscape.ck-page-body-size-a6 {
        width: 14.8cm;
        min-height: 10.5cm;
    }

    &.ck-page-body-orientation-portrait.ck-page-body-size-letter {
        width: 21.59cm;
        min-height: 27.94cm;
    }

    &.ck-page-body-orientation-landscape.ck-page-body-size-letter {
        width: 27.94cm;
        min-height: 21.59cm;
    }

    &.ck-page-body-orientation-portrait.ck-page-body-size-legal {
        width: 21.59cm;
        min-height: 35.6cm;
    }

    &.ck-page-body-orientation-landscape.ck-page-body-size-legal {
        width: 27.94cm;
        min-height: 35.6cm;
    }

    &.ck-page-body-orientation-portrait.ck-page-body-size-ledger {
        width: 27.94cm;
        min-height: 43.18cm;
    }

    &.ck-page-body-orientation-landscape.ck-page-body-size-ledger {
        width: 43.18cm;
        min-height: 27.94cm;
    }

    &.ck-page-body-orientation-portrait.ck-page-body-size-tabloid {
        width: 27.94cm;
        min-height: 43.18cm;
    }

    &.ck-page-body-orientation-landscape.ck-page-body-size-tabloid {
        width: 43.18cm;
        min-height: 27.94cm;
    }

    .ck-page-fragment {
        position: relative;

        .ck-page-fragment-label-header {
            position: absolute;
            text-align: right;
            padding: 2px 10px;
            border: 2px dashed var(--colorsUtilityMajor100);
            color: var(--colorsUtilityYin090);
            background-color: var(--colorsUtilityYang100);
            font-weight: var(--fontWeights700);
            bottom: -13px;
            z-index: 9;
            text-transform: uppercase;
        }

        .ck-page-fragment-label-footer {
            position: absolute;
            text-align: right;
            padding: 2px 10px;
            border: 2px dashed var(--colorsUtilityMajor100);
            color: var(--colorsUtilityYin090);
            background-color: var(--colorsUtilityYang100);
            font-weight: var(--fontWeights700);
            top: -13px;
            z-index: 9;
            text-transform: uppercase;
        }
    }

    .ck-page-fragment-header {
        border-bottom: 2px dashed var(--colorsUtilityMajor100);

        .ck-content {
            height: 100%;
        }
    }

    .ck-page-fragment-footer {
        border-top: 2px dashed var(--colorsUtilityMajor100);

        .ck-content {
            height: 100%;
        }
    }

    &.ck-page-body-orientation-portrait,
    &.ck-page-body-orientation-landscape {
        display: flex;
        flex-direction: column;

        .ck-page-fragment.ck-page-fragment-body {
            flex: 1;
            display: flex;
            flex-direction: column;

            >.ck-content {
                flex: 1;
                overflow-x: hidden;
            }
        }
    }
}

.ck-page-fragment>.ck-content {
    padding: 0 2cm;
}

.ck-powered-by {
    display: none;
}

/* Override the page's width in the "Examples" section which is wider. */
.main__content-wide .document-editor__editable-container>.ck-editor__editable {
    width: 18cm;
}

/* Set the default font for the "page" of the content. */
.document-editor .ck-content,
.document-editor .ck-heading-dropdown .ck-list .ck-button__label {
    font:
        16px/1.6 'Sage UI',
        Helvetica,
        Arial,
        sans-serif;
}

/* Adjust the headings dropdown to host some larger heading styles. */
.document-editor .ck-heading-dropdown .ck-list .ck-button__label {
    line-height: calc(1.7 * var(--ck-line-height-base) * var(--ck-font-size-base));
    min-width: 6em;
}

/* Scale down all heading previews because they are way too big to be presented in the UI.
Preserve the relative scale, though. */
.document-editor .ck-heading-dropdown .ck-list .ck-heading_heading1 .ck-button__label,
.document-editor .ck-heading-dropdown .ck-list .ck-heading_heading2 .ck-button__label {
    transform: scale(0.8);
    transform-origin: left;
}

.document-editor .ck-heading-dropdown .ck-heading_heading2.ck-on .ck-button__label {
    color: var(--ck-color-list-button-on-text);
}

/* Make the block quoted text serif with some additional spacing. */
.document-editor .ck-content blockquote {
    font-family: Georgia, serif;
    margin-left: calc(2 * var(--ck-spacing-large));
    margin-right: calc(2 * var(--ck-spacing-large));
}

.property {
    border-radius: 4px;
    background: var(--colorsActionMajorYin065);
    color: var(--colorsUtilityYang100);
    box-shadow: var(--boxShadow100);
    padding: 2px;
    outline-offset: -2px;
    line-height: 1em;
    margin: 0 1px;
}

.query-table-cell.e-right-align {
    text-align: right;
}

.property::selection {
    display: none;
}

.document-editor__editable-container .query-table-body {
    tr.query-table-row {
        td {
            vertical-align: top;
        }

        td:first-child::before,
        td:last-child::before {
            padding-right: 8px;
            position: absolute;
            border-top: 3px dashed var(--colorsUtilityMajor100);
            border-bottom: 3px dashed var(--colorsUtilityMajor100);
            width: 100px;
            font-size: 10px;
            line-height: 24px;
        }

        td:first-child::before {
            content: var(--tableBodyLabel);
            left: -110px;
            text-align: right;
            padding-right: 8px;
        }

        td:last-child::before {
            content: var(--tableBodyLabel);
            right: -118px;
            text-align: left;
            padding-left: 8px;
        }
    }

    tr.query-table-row[data-footer-group='footer'] td:first-child::before,
    tr.query-table-row[data-footer-group='footer'] td:last-child::before {
        content: var(--tableFooterLabel);
    }

    tr.query-table-row[data-footer-group='0'] td:first-child::before,
    tr.query-table-row[data-footer-group='0'] td:last-child::before {
        content: var(--tableGroup1FooterLabel);
    }

    tr.query-table-row[data-footer-group='1'] td:first-child::before,
    tr.query-table-row[data-footer-group='1'] td:last-child::before {
        content: var(--tableGroup2FooterLabel);
    }

    tr.query-table-row[data-footer-group='2'] td:first-child::before,
    tr.query-table-row[data-footer-group='2'] td:last-child::before {
        content: var(--tableGroup3FooterLabel);
    }

    tr.query-table-row[data-footer-group='3'] td:first-child::before,
    tr.query-table-row[data-footer-group='3'] td:last-child::before {
        content: var(--tableGroup4FooterLabel);
    }

    tr.query-table-row[data-footer-group='4'] td:first-child::before,
    tr.query-table-row[data-footer-group='4'] td:last-child::before {
        content: var(--tableGroup5FooterLabel);
    }

    tr.query-table-row[data-footer-group='5'] td:first-child::before,
    tr.query-table-row[data-footer-group='5'] td:last-child::before {
        content: var(--tableGroup6FooterLabel);
    }

    tr.query-table-row[data-footer-group='6'] td:first-child::before,
    tr.query-table-row[data-footer-group='6'] td:last-child::before {
        content: var(--tableGroup7FooterLabel);
    }

    tr.query-table-row[data-footer-group='8'] td:first-child::before,
    tr.query-table-row[data-footer-group='8'] td:last-child::before {
        content: var(--tableGroup8FooterLabel);
    }
}

.document-editor-property-selection-dialog,
.document-editor-condition-dialog {
    .document-editor-mode-selection-container {
        grid-template-columns: repeat(1, 1fr);
        gap: 24px;
        padding: 0px;
        width: 100%;
        display: grid;
        box-sizing: border-box;
    }

    .document-editor-sorting-step,
    [data-component='flat-table-wrapper'] {
        --fieldSpacing: 0;
    }
}

.document-editor-sorting-step,
[data-component='flat-table-wrapper'] {
    --fieldSpacing: 0;
}

.document-editor-filter-editor {
    [data-component='flat-table-wrapper'] {
        --fieldSpacing: 0;
    }
}

.document-editor-border-style-preview {
    margin-left: 8px;
    border-top-width: 2px;
    border-color: var(--colorsUtilityYin090);
    width: 20px;
    height: 3px;
    display: inline-block;
}

.document-editor-left-panel,
.document-editor-right-panel {
    z-index: 10;
    background: var(--colorsUtilityYang100);
    position: absolute;
    top: 0;
    height: 100%;
    padding-top: 52px;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
    width: 320px;
    display: flex;
    flex-flow: column nowrap;

    [data-portal-entrance] {
        display: none;
    }
}

.document-editor-panel-header {
    display: flex;
    border-bottom: 1px solid var(--colorsUtilityMajor100);
    padding: 12px 4px;

    .document-editor-panel-header-label {
        flex: 1;
        font-size: var(--fontSizes400);
        font-weight: var(--fontWeights700);
        padding: 12px 16px;
        margin-bottom: 12px;
    }
}

.document-editor-right-panel-node-browser {
    padding: 12px;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    flex-flow: column nowrap;
}

.document-editor-right-panel-body {
    padding: 12px;
    overflow-y: auto;
    overflow-x: hidden;
}

.document-editor-right-panel-body-row {
    height: 100%;
}

.field-label {
    color: var(--colorsUtilityYin090);
    display: block;
    font-weight: 600;
    font-size: 14px;
}

.document-editor-panel-context-indicator {
    padding: 12px;
    font-size: 16px;
    font-weight: var(--fontWeights500);
}

.ck-property-tooltip {
    padding: 8px !important;
}