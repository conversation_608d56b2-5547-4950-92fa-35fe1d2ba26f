import type { FilterParameter } from '@sage/xtrem-ui-components';
import { OBJECT_TYPE_GLOBAL_PROPERTIES, OBJECT_TYPE_ROOT } from './constants';
import type {
    DataModelProperty,
    DocumentContextProvider,
    GetObjectDetailsFunctionArgs,
    ObjectInsertionDetails,
} from './context-provider';

export const fakeParameters: FilterParameter[] = [
    {
        type: 'String',
        name: 'invoiceNumber',
        label: 'Invoice Number',
    },
    {
        type: 'Int',
        name: 'invoiceNetTotal',
        label: 'Invoice Net Total',
    },
];

const additionalPropsCommonValues = {
    enumType: null,
    isCustom: false,
    dataType: '',
    dataTypeDetails: null,
    targetNode: '',
    targetNodeDetails: null,
    isStored: false,
    isOnInputType: true,
    isOnOutputType: true,
    isMutable: false,
};
const additionalPropsForId = {
    ...additionalPropsCommonValues,
    isOnInputType: false,
};

const additionalPropsForUser = {
    ...additionalPropsCommonValues,
    dataType: 'user',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-system/User',
    targetNodeDetails: {},
};

const additionalPropsForCountry = {
    ...additionalPropsCommonValues,
    dataType: 'country',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-system/Country',
    targetNodeDetails: {},
};

const additionalPropsForSalesInvoice = {
    ...additionalPropsCommonValues,
    dataType: 'salesInvoice',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-sales/SalesInvoice',
    targetNodeDetails: {},
};

const additionalPropsForCustomer = {
    ...additionalPropsCommonValues,
    dataType: 'customer',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-master-data/Customer',
    targetNodeDetails: {},
};

const additionalPropsForItem = {
    ...additionalPropsCommonValues,
    dataType: 'item',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-master-data/Item',
    targetNodeDetails: {},
};

const additionalPropsForProductCategory = {
    ...additionalPropsCommonValues,
    dataType: 'productCategoryEnumDataType',
    enumType: 'productCategoryEnumType',
};

const additionalPropsForBillingAddress = {
    ...additionalPropsCommonValues,
    dataType: 'billingAddress',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-master-data/BusinessEntityAddress',
    targetNodeDetails: {},
};

const additionalPropsForDeliveryAddress = {
    ...additionalPropsCommonValues,
    dataType: 'deliveryAddress',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-master-data/BusinessEntityAddress',
    targetNodeDetails: {},
};

const additionalPropsForCurrency = {
    ...additionalPropsCommonValues,
    dataType: 'currency',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-master-data/Currency',
    targetNodeDetails: {},
};

const additionalPropsForSalesInvoiceLine = {
    ...additionalPropsCommonValues,
    dataType: 'salesInvoiceLine',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-sales/SalesInvoiceLine',
    targetNodeDetails: {},
};

const mockGlobalProperties: DataModelProperty[] = [
    {
        name: '@root.currentDate',
        label: 'Current Date',
        kind: 'SCALAR',
        type: 'Date',
        canFilter: false,
        canSort: false,
        ...additionalPropsCommonValues,
    },
    {
        name: '@root.createdByUser',
        label: 'Name of user who created the document',
        kind: 'SCALAR',
        type: 'String',
        canFilter: false,
        canSort: false,
        ...additionalPropsCommonValues,
    },
    {
        name: '@root.updatedByUser',
        label: 'Name of user who updated the document',
        kind: 'SCALAR',
        type: 'String',
        canFilter: false,
        canSort: false,
        ...additionalPropsCommonValues,
    },
    {
        name: '@root.invoiceNumber',
        label: '"Invoice Number" Parameter',
        kind: 'SCALAR',
        type: 'String',
        canFilter: false,
        canSort: false,
        ...additionalPropsCommonValues,
    },
    {
        name: '@root.invoiceNetTotal',
        label: '"Invoice net total" Parameter',
        kind: 'SCALAR',
        type: 'String',
        canFilter: false,
        canSort: false,
        ...additionalPropsCommonValues,
    },
];
const mockGlobalPropertiesHeaderAndFooter: DataModelProperty[] = [
    {
        name: '#currentPageNumber',
        label: 'Current Page Number',
        kind: 'SCALAR',
        type: 'String',
        canFilter: false,
        canSort: false,
        ...additionalPropsCommonValues,
    },
    {
        name: '#pageCount',
        label: 'Total Page Count',
        kind: 'SCALAR',
        type: 'String',
        canFilter: false,
        canSort: false,
        ...additionalPropsCommonValues,
    },
];

const mockRootObject: DataModelProperty[] = [
    {
        name: '_id',
        label: 'ID',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'salesInvoice',
        label: 'Sales Invoice',
        kind: 'LIST',
        type: 'SalesInvoice',
        namespace: 'xtremSales',
        iconType: 'csv',
        canFilter: false,
        canSort: false,
        ...additionalPropsForSalesInvoice,
    },
    {
        name: 'salesInvoiceLine',
        label: 'Sales Invoice Line',
        kind: 'LIST',
        type: 'SalesInvoiceLine',
        namespace: 'xtremSales',
        iconType: 'csv',
        canFilter: false,
        canSort: false,
        ...additionalPropsForSalesInvoiceLine,
    },
    {
        name: 'user',
        label: 'User',
        kind: 'LIST',
        type: 'User',
        namespace: 'xtremSystem',
        iconType: 'csv',
        canFilter: false,
        canSort: false,
        ...additionalPropsForUser,
    },
    {
        name: 'customer',
        label: 'Customer',
        kind: 'LIST',
        type: 'Customer',
        namespace: 'xtremMasterData',
        iconType: 'csv',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCustomer,
    },
    {
        name: 'item',
        label: 'Item',
        kind: 'LIST',
        type: 'Item',
        namespace: 'xtremMasterData',
        iconType: 'csv',
        canFilter: false,
        canSort: false,
        ...additionalPropsForItem,
    },
];

const mockCountryObject: DataModelProperty[] = [
    {
        name: '_id',
        label: 'ID',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'name',
        label: 'Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'isoCode',
        label: 'ISO Code',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'phoneNumber',
        label: 'Country phone code',
        kind: 'SCALAR',
        type: 'Int',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
];

const mockCurrencyObject: DataModelProperty[] = [
    {
        name: '_id',
        label: 'ID',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'name',
        label: 'Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'symbol',
        label: 'Symbol',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'isoCode',
        label: 'ISO Code',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
];

const mockCustomerObject: DataModelProperty[] = [
    {
        name: '_id',
        label: 'ID',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'name',
        label: 'Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'taxNumber',
        label: 'Tax Code',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'taxCountry',
        label: 'Country',
        kind: 'OBJECT',
        type: 'Country',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCountry,
    },
];
const mockItemObject: DataModelProperty[] = [
    {
        name: '_id',
        label: 'ID',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'description',
        label: 'Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'isDangerous',
        label: 'Dangerous',
        kind: 'SCALAR',
        type: 'Boolean',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'upcCode',
        label: 'UPC code',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'originCountry',
        label: 'Manufactured in',
        kind: 'OBJECT',
        type: 'Country',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCountry,
    },
    {
        type: '@sage/xtrem-master-data/ProductCategory',
        kind: 'ENUM',
        enumValues: ['great', 'good', 'ok', 'notBad', 'awful'],
        isCollection: false,
        name: 'category',
        canFilter: true,
        canSort: true,
        label: 'Category',
        ...additionalPropsForProductCategory,
    },
];

const mockSalesInvoiceLineObject: DataModelProperty[] = [
    {
        name: 'description',
        label: 'Description',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'item',
        label: 'Item',
        kind: 'OBJECT',
        type: 'Item',
        canFilter: false,
        canSort: false,
        ...additionalPropsForItem,
    },
    {
        name: 'quantity',
        label: 'Quantity',
        kind: 'SCALAR',
        type: 'Float',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'netPrice',
        label: 'Net Price',
        kind: 'SCALAR',
        type: 'Float',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'grossPrice',
        label: 'Gross Price',
        kind: 'SCALAR',
        type: 'Float',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'computedLineTotal',
        label: 'Computed Line Total',
        kind: 'SCALAR',
        type: 'Float',
        canFilter: false,
        canSort: false,
        ...additionalPropsCommonValues,
    },
    {
        name: 'invoice',
        label: 'Invoice',
        kind: 'OBJECT',
        type: 'SalesInvoice',
        canFilter: true,
        canSort: true,
        ...additionalPropsForSalesInvoice,
    },
];

const mockSalesInvoiceObject: DataModelProperty[] = [
    {
        name: '_id',
        label: 'ID',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'invoiceNumber',
        label: 'Invoice Number',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: '_customData.testCustomField',
        label: 'testCustomField',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: false,
        isStored: true,
        isOnInputType: true,
        isOnOutputType: true,
        dataType: '',
        targetNode: '',
        enumType: null,
        isCustom: true,
        isMutable: false,
    },
    {
        name: 'totalWithoutTax',
        label: 'Net Total',
        kind: 'SCALAR',
        type: 'Float',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'totalWithTax',
        label: 'Gross Total',
        kind: 'SCALAR',
        type: 'Float',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'customer',
        label: 'Customer',
        kind: 'OBJECT',
        type: 'Customer',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCustomer,
    },
    {
        name: 'billingAddress',
        label: 'Billing address',
        kind: 'OBJECT',
        type: 'Address',
        canFilter: false,
        canSort: false,
        ...additionalPropsForBillingAddress,
    },
    {
        name: 'deliveryAddress',
        label: 'Delivery address',
        kind: 'OBJECT',
        type: 'Address',
        canFilter: false,
        canSort: false,
        ...additionalPropsForDeliveryAddress,
    },
    {
        name: 'phoneNumber',
        label: 'Country phone code',
        kind: 'SCALAR',
        type: 'Int',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'orderDate',
        label: 'Order Date',
        kind: 'SCALAR',
        type: 'Date',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'deliveryDate',
        label: 'Delivery Date',
        kind: 'SCALAR',
        type: 'Date',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'currency',
        label: 'Currency',
        kind: 'OBJECT',
        type: 'Currency',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCurrency,
    },
    {
        name: 'notes',
        label: 'Notes',
        kind: 'OBJECT',
        type: '_OutputTextStream',
        canFilter: true,
        canSort: false,
        ...additionalPropsCommonValues,
    },
    {
        name: 'queryText',
        label: 'Query Text',
        kind: 'OBJECT',
        type: '_OutputTextStream',
        canFilter: true,
        canSort: false,
        ...additionalPropsCommonValues,
    },
    {
        name: 'lines',
        label: 'Lines',
        kind: 'LIST',
        iconType: 'list_view',
        type: 'SalesInvoiceLine',
        canFilter: false,
        canSort: false,
        ...additionalPropsForSalesInvoiceLine,
    },
];

const mockAddressObject: DataModelProperty[] = [
    {
        name: '_id',
        label: 'ID',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'name',
        label: 'Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'town',
        label: 'Town',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'addressLine1',
        label: 'Address Line 1',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'addressLine2',
        label: 'Address Line 2',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'zipCode',
        label: 'Post code',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'country',
        label: 'Country',
        kind: 'OBJECT',
        type: 'Country',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCountry,
    },
];

const mockUserObject: DataModelProperty[] = [
    {
        name: '_id',
        label: 'ID',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'firstName',
        label: 'First Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'lastName',
        label: 'Last Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'country',
        label: 'Country of origin',
        kind: 'OBJECT',
        type: 'Country',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCountry,
    },
];

export const fakeContextProvider: DocumentContextProvider = {
    onObjectInsert: (
        path: string,
        property: DataModelProperty,
        insertMode: 'property' | 'recordContext' | 'list',
    ): Promise<ObjectInsertionDetails> =>
        new Promise<ObjectInsertionDetails>(resolve => {
            const rootPath = property.namespace ? `${property.namespace}.${path}` : path;
            if (insertMode === 'recordContext') {
                resolve({
                    path: `${rootPath}.query.edges.0.node`,
                });
            } else if (insertMode === 'list') {
                resolve({
                    path: `${rootPath}.query.edges`,
                    subPath: 'node',
                });
            }

            resolve({
                path: rootPath,
            });
        }),
    getDocumentParameters: (): Promise<FilterParameter[]> => {
        return Promise.resolve<FilterParameter[]>(fakeParameters);
    },
    getObjectDetails: ({ objectType, contextType }: GetObjectDetailsFunctionArgs): Promise<DataModelProperty[]> =>
        new Promise(resolve =>
            setTimeout(() => {
                if (objectType === OBJECT_TYPE_GLOBAL_PROPERTIES) {
                    if (contextType === 'body') {
                        resolve(mockGlobalProperties);
                    } else {
                        resolve([...mockGlobalProperties, ...mockGlobalPropertiesHeaderAndFooter]);
                    }
                    return;
                }

                if (objectType === OBJECT_TYPE_ROOT) {
                    resolve(mockRootObject);
                    return;
                }

                if (objectType === 'Country') {
                    resolve(mockCountryObject);
                    return;
                }

                if (objectType === 'Currency') {
                    resolve(mockCurrencyObject);
                    return;
                }

                if (objectType === 'Customer') {
                    resolve(mockCustomerObject);
                    return;
                }
                if (objectType === 'Item') {
                    resolve(mockItemObject);
                    return;
                }
                if (objectType === 'SalesInvoiceLine') {
                    resolve(mockSalesInvoiceLineObject);
                    return;
                }

                if (objectType === 'SalesInvoice') {
                    resolve(mockSalesInvoiceObject);
                    return;
                }

                if (objectType === 'Address') {
                    resolve(mockAddressObject);
                    return;
                }

                if (objectType === 'User') {
                    resolve(mockUserObject);
                }
            }, 500),
        ),
};
