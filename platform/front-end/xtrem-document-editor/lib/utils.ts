import { Essentials } from '@ckeditor/ckeditor5-essentials';
import { Bold, Code, Italic, Strikethrough, Subscript, Superscript, Underline } from '@ckeditor/ckeditor5-basic-styles';
import { Alignment } from '@ckeditor/ckeditor5-alignment';
import { HorizontalLine } from '@ckeditor/ckeditor5-horizontal-line';
import { BlockQuote } from '@ckeditor/ckeditor5-block-quote';
import { Font } from '@ckeditor/ckeditor5-font';
import { HtmlComment } from '@ckeditor/ckeditor5-html-support';
import { Indent } from '@ckeditor/ckeditor5-indent';
import { Clipboard } from '@ckeditor/ckeditor5-clipboard';
import { Link } from '@ckeditor/ckeditor5-link';
import { List } from '@ckeditor/ckeditor5-list';
import { Heading } from '@ckeditor/ckeditor5-heading';
import { PageBreak } from '@ckeditor/ckeditor5-page-break';
import { Paragraph } from '@ckeditor/ckeditor5-paragraph';
import { ImageInsert, Image, ImageStyle, ImageResize, ImageToolbar } from '@ckeditor/ckeditor5-image';
import {
    Table,
    TableCellProperties,
    TableProperties,
    TableToolbar,
    TableColumnResize,
} from '@ckeditor/ckeditor5-table';
import PropertyPlugin from './plugins/property';
import QueryTablePlugin from './plugins/query-table';
import RecordContextPlugin from './plugins/record-context';
import {
    COMMAND_TOGGLE_DATA_PROPERTIES,
    COMMAND_TOGGLE_GLOBAL_PROPERTIES,
    COMMAND_TOGGLE_RIGHT_PANEL,
    THEME_COLORS,
    COMMAND_ZOOM_IN,
    COMMAND_ZOOM_OUT,
    COMMAND_INSERT_CONDITIONAL_BLOCK,
} from './constants';
import UnbreakableBlock from './plugins/unbreakable-block';
import ToggleRightPanel from './plugins/toggle-right-panel';
import ToggleDataPropertiesPanel from './plugins/toggle-data-properties-panel';
import ToggleGlobalPropertiesPanel from './plugins/toggle-global-properties-panel';
import { Base64UploadAdapter } from '@ckeditor/ckeditor5-upload';
import ConditionalBlock from './plugins/conditional-block';
import RootStylePlugin from './plugins/root-style-plugin';
import type { LocalizeLocale } from '@sage/xtrem-shared';
import type { PaperOrientation } from './index';
import { memoize } from 'lodash';
import ZoomIn from './plugins/zoom-in';
import ZoomOut from './plugins/zoom-out';

export const getStaticEditorConfig = memoize((locale: LocalizeLocale): Record<string, unknown> => {
    return {
        language: locale.substring(0, 2),
        plugins: [
            Alignment,
            Base64UploadAdapter,
            BlockQuote,
            Bold,
            Clipboard,
            Code,
            ConditionalBlock,
            Essentials,
            Font,
            Heading,
            HorizontalLine,
            HtmlComment,
            Image,
            ImageInsert,
            ImageResize,
            ImageStyle,
            ImageToolbar,
            Indent,
            Italic,
            Link,
            List,
            PageBreak,
            Paragraph,
            PropertyPlugin,
            QueryTablePlugin,
            RecordContextPlugin,
            RootStylePlugin,
            Strikethrough,
            Subscript,
            Superscript,
            Table,
            TableCellProperties,
            TableColumnResize,
            TableProperties,
            TableToolbar,
            ToggleDataPropertiesPanel,
            ToggleGlobalPropertiesPanel,
            ToggleRightPanel,
            UnbreakableBlock,
            Underline,
            ZoomIn,
            ZoomOut,
        ],
        toolbar: [
            'undo',
            'redo',
            '|',
            'heading',
            '|',
            'numberedList',
            'bulletedList',
            '|',
            'fontSize',
            'fontFamily',
            'fontColor',
            'fontBackgroundColor',
            'alignment',
            '-',
            'bold',
            'italic',
            'underline',
            {
                label: 'Formatting',
                icon: 'text',
                items: ['strikethrough', 'subscript', 'superscript', 'code', '|', 'removeFormat'],
            },
            '|',
            'insertTable',
            'blockQuote',
            'outdent',
            'indent',
            'pageBreak',
            'uploadImage',
            '|',
            COMMAND_TOGGLE_DATA_PROPERTIES,
            COMMAND_TOGGLE_GLOBAL_PROPERTIES,
            COMMAND_TOGGLE_RIGHT_PANEL,
            COMMAND_INSERT_CONDITIONAL_BLOCK,
            'unbreakableBlock',
            '|',
            COMMAND_ZOOM_IN,
            COMMAND_ZOOM_OUT,
        ],
        image: {
            upload: { types: ['jpeg', 'png'] },
        },
        fontFamily: {
            options: [
                'default',
                'Arial, Helvetica, sans-serif',
                'Courier New, Courier, monospace',
                'Georgia, serif',
                'Lucida Sans Unicode, Lucida Grande, sans-serif',
                'Tahoma, Geneva, sans-serif',
                'Times New Roman, Times, serif',
                'Trebuchet MS, Helvetica, sans-serif',
                'Verdana, Geneva, sans-serif',
                'Sage UI, Geneva, sans-serif',
            ],
        },
        fontSize: {
            options: [
                {
                    title: '8',
                    model: '8pt',
                },
                {
                    title: '10',
                    model: '10pt',
                },
                {
                    title: '12',
                    model: '12pt',
                },
                {
                    title: 'default',
                    model: 'default',
                },
                {
                    title: '16',
                    model: '16pt',
                },
                {
                    title: '18',
                    model: '18pt',
                },
                {
                    title: '20',
                    model: '20pt',
                },
                {
                    title: '24',
                    model: '24pt',
                },
                {
                    title: '32',
                    model: '32pt',
                },
            ],
        },
        fontColor: {
            colors: THEME_COLORS,
        },
        fontBackgroundColor: {
            colors: THEME_COLORS,
        },
        heading: {
            options: [
                {
                    model: 'paragraph',
                    title: 'Paragraph',
                    class: '',
                },
                {
                    model: 'heading1',
                    view: 'h1',
                    title: 'Heading 1',
                    class: '',
                },
                {
                    model: 'heading2',
                    view: 'h2',
                    title: 'Heading 2',
                    class: '',
                },
                {
                    model: 'heading3',
                    view: 'h3',
                    title: 'Heading 3',
                    class: '',
                },
                {
                    model: 'heading4',
                    view: 'h4',
                    title: 'Heading 4',
                    class: '',
                },
                {
                    model: 'heading5',
                    view: 'h5',
                    title: 'Heading 5',
                    class: '',
                },
                {
                    model: 'heading6',
                    view: 'h6',
                    title: 'Heading 6',
                    class: '',
                },
            ],
        },
        table: {
            contentToolbar: [],
        },
    };
});

export function getEditorData({
    paperOrientation,
    value,
    headerValue,
    footerValue,
}: {
    paperOrientation: PaperOrientation;
    value: string;
    headerValue: string;
    footerValue: string;
}): Record<string, string> {
    return paperOrientation === 'fullScreen'
        ? { body: value }
        : { header: headerValue, body: value, footer: footerValue };
}

export function getEditorAttributes({
    paperOrientation,
    marginLeft,
    marginRight,
}: {
    paperOrientation: PaperOrientation;
    marginLeft: number;
    marginRight: number;
}): Record<string, Record<string, unknown>> {
    const pagePadding = { padding: { marginLeft, marginRight } };
    return paperOrientation === 'fullScreen'
        ? { body: {} }
        : { header: pagePadding, body: pagePadding, footer: pagePadding };
}
