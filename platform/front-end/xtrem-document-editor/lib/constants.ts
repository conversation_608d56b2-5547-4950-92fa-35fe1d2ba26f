import tokens from '@sage/design-tokens/js/base/common';

export const COMMAND_INSERT_RECORD_CONTEXT = 'insertRecordContext';
export const COMMAND_INSERT_PROPERTY = 'insertProperty';
export const COMMAND_CHANGE_PROPERTY_FORMAT = 'changePropertyFormat';
export const COMMAND_INSERT_QUERY_TABLE = 'queryTableInsert';
export const COMMAND_ALIGNMENT_QUERY_TABLE = 'queryTableAlignment';
export const COMMAND_BORDER_COLOR_QUERY_TABLE = 'queryTableBorderColor';
export const COMMAND_BORDER_STYLE_QUERY_TABLE = 'queryTableBorderStyle';
export const COMMAND_BORDER_WIDTH_QUERY_TABLE = 'queryTableBorderWidth';
export const COMMAND_BACKGROUND_QUERY_TABLE = 'queryTableBackgroundColor';
export const COMMAND_CELL_BORDER_COLOR_QUERY_TABLE = 'queryTableCellBorderColor';
export const COMMAND_CELL_BORDER_STYLE_QUERY_TABLE = 'queryTableCellBorderStyle';
export const COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE = 'queryTableCellBorderWidth';
export const COMMAND_CELL_BACKGROUND_QUERY_TABLE = 'queryTableCellBackgroundColor';
export const COMMAND_CELL_HEIGHT_QUERY_TABLE = 'queryTableCellHeight';
export const COMMAND_CELL_VERTICAL_ALIGNMENT_QUERY_TABLE = 'queryTableCellVerticalAlignment';
export const COMMAND_CELL_HORIZONTAL_ALIGNMENT_QUERY_TABLE = 'queryTableCellHorizontalAlignment';
export const COMMAND_CELL_QUERY_TABLE_PADDING = 'queryTableCellPadding';
export const COMMAND_INSERT_COLUMN_QUERY_COMMAND = 'queryTableInsertColumnCommand';
export const COMMAND_REMOVE_COLUMN_QUERY_TABLE = 'queryTableRemoveColumnCommand';
export const COMMAND_INSERT_CONDITIONAL_BLOCK = 'insertConditionalBlock';
export const COMMAND_EDIT_ORDER_QUERY_TABLE = 'queryTableEditOrderCommand';
export const COMMAND_EDIT_FILTER_QUERY_TABLE = 'queryTableEditFilterCommand';
export const COMMAND_EDIT_CONDITION_BLOCK = 'conditionalBlockEditCommand';
export const COMMAND_TABLE_CELL_BORDER_COLOR = 'tableCellBorderColor';
export const COMMAND_TABLE_CELL_HORIZONTAL_ALIGNMENT = 'tableCellHorizontalAlignment';
export const COMMAND_TABLE_CELL_VERTICAL_ALIGNMENT = 'tableCellVerticalAlignment';
export const COMMAND_TABLE_CELL_BACKGROUND_COLOR = 'tableCellBackgroundColor';
export const COMMAND_TABLE_CELL_BORDER_WIDTH = 'tableCellBorderWidth';
export const COMMAND_TABLE_CELL_BORDER_STYLE = 'tableCellBorderStyle';
export const COMMAND_REMOVE_TABLE_COLUMN = 'removeTableColumn';
export const COMMAND_INSERT_TABLE_COLUMN_LEFT = 'insertTableColumnLeft';
export const COMMAND_INSERT_PAGE_BREAK_QUERY_TABLE = 'queryTableInsertPageBreak';
export const COMMAND_TABLE_CELL_HEIGHT = 'tableCellHeight';
export const COMMAND_INSERT_UNBREAKABLE_BLOCK = 'insertUnbreakableBlock';

export const FORMATTING_COMMANDS = [
    COMMAND_ALIGNMENT_QUERY_TABLE,
    COMMAND_BACKGROUND_QUERY_TABLE,
    COMMAND_BORDER_COLOR_QUERY_TABLE,
    COMMAND_BORDER_STYLE_QUERY_TABLE,
    COMMAND_BORDER_WIDTH_QUERY_TABLE,
    COMMAND_CELL_BACKGROUND_QUERY_TABLE,
    COMMAND_CELL_BORDER_COLOR_QUERY_TABLE,
    COMMAND_CELL_BORDER_STYLE_QUERY_TABLE,
    COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE,
    COMMAND_CELL_HEIGHT_QUERY_TABLE,
    COMMAND_CELL_HORIZONTAL_ALIGNMENT_QUERY_TABLE,
    COMMAND_CELL_VERTICAL_ALIGNMENT_QUERY_TABLE,
    COMMAND_CHANGE_PROPERTY_FORMAT,
    COMMAND_EDIT_CONDITION_BLOCK,
    COMMAND_EDIT_FILTER_QUERY_TABLE,
    COMMAND_EDIT_ORDER_QUERY_TABLE,
    COMMAND_INSERT_COLUMN_QUERY_COMMAND,
    COMMAND_INSERT_PAGE_BREAK_QUERY_TABLE,
    COMMAND_INSERT_TABLE_COLUMN_LEFT,
    COMMAND_REMOVE_COLUMN_QUERY_TABLE,
    COMMAND_REMOVE_TABLE_COLUMN,
    COMMAND_TABLE_CELL_BACKGROUND_COLOR,
    COMMAND_TABLE_CELL_BORDER_COLOR,
    COMMAND_TABLE_CELL_BORDER_STYLE,
    COMMAND_TABLE_CELL_BORDER_WIDTH,
    COMMAND_TABLE_CELL_HEIGHT,
    COMMAND_TABLE_CELL_HORIZONTAL_ALIGNMENT,
];

export const DATA_FORMAT_ENUM = 'ENUM';
export const DATA_FORMAT_DECIMAL = '2';

export const COMMAND_TOGGLE_RIGHT_PANEL = 'toggleRightPanel';
export const COMMAND_ZOOM_IN = 'zoomIn';
export const COMMAND_ZOOM_OUT = 'zoomOut';
export const COMMAND_TOGGLE_DATA_PROPERTIES = 'toggleDataPropertiesPanel';
export const COMMAND_TOGGLE_GLOBAL_PROPERTIES = 'toggleGlobalPropertiesPanel';

export const LOCAL_STORAGE_KEY = 'DOCUMENT_EDITOR_CONSUMER_MOCK_VALUE';
export const LOCAL_STORAGE_HEADER_KEY = 'DOCUMENT_EDITOR_CONSUMER_MOCK_HEADER_VALUE';
export const LOCAL_STORAGE_FOOTER_KEY = 'DOCUMENT_EDITOR_CONSUMER_MOCK_FOOTER_VALUE';
export const LOCAL_STORAGE_ORIENTATION_KEY = 'DOCUMENT_EDITOR_CONSUMER_MOCK_ORIENTATION_VALUE';
export const LOCAL_STORAGE_SIZE_KEY = 'DOCUMENT_EDITOR_CONSUMER_MOCK_SIZE_VALUE';
export const LOCAL_STORAGE_MARGIN_TOP_KEY = 'LOCAL_STORAGE_MARGIN_TOP_KEY';
export const LOCAL_STORAGE_MARGIN_RIGHT_KEY = 'LOCAL_STORAGE_MARGIN_RIGHT_KEY';
export const LOCAL_STORAGE_MARGIN_BOTTOM_KEY = 'LOCAL_STORAGE_MARGIN_BOTTOM_KEY';
export const LOCAL_STORAGE_MARGIN_LEFT_KEY = 'LOCAL_STORAGE_MARGIN_LEFT_KEY';
export const DOCUMENT_EDITOR_ZOOM_LEVEL_KEY = 'DOCUMENT_EDITOR_ZOOM_LEVEL_KEY';

export const VALIGN_VALUES_REG_EXP = /^(top|middle|bottom)$/;
export const ALIGN_VALUES_REG_EXP = /^(left|center|right|justify)$/;

export const THEME_COLORS = [
    'transparent',
    tokens.colorsYang100.substring(0, 7).toUpperCase(),
    tokens.colorsYin090.substring(0, 7).toUpperCase(),
    '#dfdfdf',
    tokens.colorsUtilityMajor300.substring(0, 7).toUpperCase(),
    tokens.colorsUtilityMajor400.substring(0, 7).toUpperCase(),
    tokens.colorsUtilityMajor500.substring(0, 7).toUpperCase(),
    tokens.colorsActionMajor450.substring(0, 7).toUpperCase(),
    tokens.colorsActionMajor600.substring(0, 7).toUpperCase(),
    tokens.colorsActionMajor700.substring(0, 7).toUpperCase(),
    tokens.colorsSemanticFocus250.substring(0, 7).toUpperCase(),
    tokens.colorsSemanticFocus500.substring(0, 7).toUpperCase(),
    tokens.colorsSemanticNegative500.substring(0, 7).toUpperCase(),
    tokens.colorsSemanticNegative600.substring(0, 7).toUpperCase(),
    tokens.colorsSemanticCaution400.substring(0, 7).toUpperCase(),
    tokens.colorsSemanticCaution500.substring(0, 7).toUpperCase(),
    tokens.colorsSemanticCaution600.substring(0, 7).toUpperCase(),
    tokens.colorsSemanticInfo500.substring(0, 7).toUpperCase(),
    tokens.colorsSemanticInfo600.substring(0, 7).toUpperCase(),
];

export const BORDER_WIDTH = ['1px', '2px', '3px', '4px'];
export const BORDER_STYLE = ['solid', 'dotted', 'dashed'];

export const OBJECT_TYPE_ROOT = '$root';
export const OBJECT_TYPE_GLOBAL_PROPERTIES = '$globalProperties';

/**
 * The minimum column width given as a percentage value. Used in situations when the table is not yet rendered, so it is impossible to
 * calculate how many percentage of the table width would be {@link ~COLUMN_MIN_WIDTH_IN_PIXELS minimum column width in pixels}.
 */
export const COLUMN_MIN_WIDTH_AS_PERCENTAGE = 5;

/**
 * The minimum column width in pixels when the maximum table width is known.
 */
export const COLUMN_MIN_WIDTH_IN_PIXELS = 40;

/**
 * Determines how many digits after the decimal point are used to store the column width as a percentage value.
 */
export const COLUMN_WIDTH_PRECISION = 2;

export const COMMANDS_QUERY_TABLE = [
    COMMAND_BORDER_COLOR_QUERY_TABLE,
    COMMAND_BORDER_WIDTH_QUERY_TABLE,
    COMMAND_BACKGROUND_QUERY_TABLE,
    COMMAND_CELL_BORDER_COLOR_QUERY_TABLE,
    COMMAND_CELL_BORDER_STYLE_QUERY_TABLE,
    COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE,
    COMMAND_CELL_BACKGROUND_QUERY_TABLE,
    COMMAND_CELL_HEIGHT_QUERY_TABLE,
];
