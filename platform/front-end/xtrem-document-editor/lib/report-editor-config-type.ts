import type { ToastProps } from 'carbon-react/esm/components/toast';
import type { LookupRequest } from './components/insert-list-property-dialog/insert-list-property-dialog';
import type { DocumentContextProvider } from './context-provider';
import type { ConditionDialogRequest } from './components/condition-dialog';
import type { LocalizeFunction } from '@sage/xtrem-shared';

export interface ReportEditorConfig {
    contextProvider: DocumentContextProvider;
    openLookup: (lookupRequest: LookupRequest | null) => void;
    onOpenConditionDialog: (conditionDialogRequest: ConditionDialogRequest | null) => void;
    onRightPanelOpenChange: (isOpen: boolean) => void;
    onDataPropertiesPanelOpenChange: (isOpen: boolean) => void;
    onGlobalPropertiesPanelOpenChange: (isOpen: boolean) => void;
    onDisplayNotification: (content: string, type: ToastProps['variant']) => void;
    localize: LocalizeFunction;
}
