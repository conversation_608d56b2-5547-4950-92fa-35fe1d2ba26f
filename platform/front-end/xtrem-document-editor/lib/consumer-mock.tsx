import * as React from 'react';
import { createRoot } from 'react-dom/client';
import GlobalStyle from 'carbon-react/esm/style/global-style';
import CKEditorInspector from '@ckeditor/ckeditor5-inspector';
import type { PaperOrientation, PaperSize } from '.';
import XtremDocumentEditor from '.';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import CarbonProvider from 'carbon-react/esm/components/carbon-provider';
import sageTheme from 'carbon-react/esm/style/themes/sage';
import {
    LOCAL_STORAGE_FOOTER_KEY,
    LOCAL_STORAGE_HEADER_KEY,
    LOCAL_STORAGE_KEY,
    LOCAL_STORAGE_MARGIN_BOTTOM_KEY,
    LOCAL_STORAGE_MARGIN_LEFT_KEY,
    LOCAL_STORAGE_MARGIN_RIGHT_KEY,
    LOCAL_STORAGE_MARGIN_TOP_KEY,
    LOCAL_STORAGE_ORIENTATION_KEY,
    LOCAL_STORAGE_SIZE_KEY,
} from './constants';
import type { ToastProps } from 'carbon-react/esm/components/toast';
import Toast from 'carbon-react/esm/components/toast';
import Switch from 'carbon-react/esm/components/switch';
import type { LocalizeEnumFunction, LocalizeFunction } from '@sage/xtrem-shared';
import { compile } from 'handlebars';
import { GridRow, GridColumn } from '@sage/xtrem-ui-components';
import { Select, Option } from 'carbon-react/esm/components/select';
import NumberComponent from 'carbon-react/esm/components/number';

import 'carbon-react/esm/style/fonts.css';
import './style.scss';
import { fakeContextProvider } from './consumer-mock-static-definitions';

const fakeCarbonLocale = (): any => ({});

interface NotificationRequest {
    type: ToastProps['variant'];
    content: string;
}

function ConsumerMock(): React.ReactElement {
    const [value, setValue] = React.useState<string>(window.localStorage.getItem(LOCAL_STORAGE_KEY) || '');
    const [headerValue, setHeaderValue] = React.useState<string>(
        window.localStorage.getItem(LOCAL_STORAGE_HEADER_KEY) || '',
    );
    const [footerValue, setFooterValue] = React.useState<string>(
        window.localStorage.getItem(LOCAL_STORAGE_FOOTER_KEY) || '',
    );
    const [notificationRequest, setNotificationRequest] = React.useState<NotificationRequest | null>(null);
    const [isDisabled, setDisabled] = React.useState<boolean>(false);
    const [paperSize, setPaperSize] = React.useState<PaperSize>(
        (window.localStorage.getItem(LOCAL_STORAGE_SIZE_KEY) as PaperSize) || 'a4',
    );
    const [paperOrientation, setPaperOrientation] = React.useState<PaperOrientation>(
        (window.localStorage.getItem(LOCAL_STORAGE_ORIENTATION_KEY) as PaperOrientation) || 'portrait',
    );
    const [marginTop, setMarginTop] = React.useState<number>(
        Number((window.localStorage.getItem(LOCAL_STORAGE_MARGIN_TOP_KEY) as PaperOrientation) || '2'),
    );
    const [marginRight, setMarginRight] = React.useState<number>(
        Number((window.localStorage.getItem(LOCAL_STORAGE_MARGIN_RIGHT_KEY) as PaperOrientation) || '2'),
    );
    const [marginBottom, setMarginBottom] = React.useState<number>(
        Number((window.localStorage.getItem(LOCAL_STORAGE_MARGIN_BOTTOM_KEY) as PaperOrientation) || '2'),
    );
    const [marginLeft, setMarginLeft] = React.useState<number>(
        Number((window.localStorage.getItem(LOCAL_STORAGE_MARGIN_LEFT_KEY) as PaperOrientation) || '2'),
    );

    const onDisplayNotification = React.useCallback((content: string, type: ToastProps['variant']) => {
        setNotificationRequest({ content, type });
        setTimeout((): void => setNotificationRequest(null), 5000);
    }, []);

    const onReady = React.useCallback((editor: MultiRootEditor) => {
        (window as any).EDITOR = editor;
        CKEditorInspector.attach(editor);
    }, []);

    const onFocus = React.useCallback(() => {
        // eslint-disable-next-line no-console
        console.log('EDITOR FOCUSED');
    }, []);

    const onBlur = React.useCallback(() => {
        // eslint-disable-next-line no-console
        console.log('EDITOR BLURRED');
    }, []);

    const onMarginTopChanged = React.useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        window.localStorage.setItem(LOCAL_STORAGE_MARGIN_TOP_KEY, event.target.value);
        setMarginTop(Number(event.target.value || '0'));
    }, []);

    const onMarginBottomChanged = React.useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        window.localStorage.setItem(LOCAL_STORAGE_MARGIN_BOTTOM_KEY, event.target.value);
        setMarginBottom(Number(event.target.value || '0'));
    }, []);

    const onMarginLeftChanged = React.useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        window.localStorage.setItem(LOCAL_STORAGE_MARGIN_LEFT_KEY, event.target.value);
        setMarginLeft(Number(event.target.value || '0'));
    }, []);

    const onMarginRightChanged = React.useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        window.localStorage.setItem(LOCAL_STORAGE_MARGIN_RIGHT_KEY, event.target.value);
        setMarginRight(Number(event.target.value || '0'));
    }, []);

    const onPaperSizeChanged = React.useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        window.localStorage.setItem(LOCAL_STORAGE_SIZE_KEY, event.target.value);
        setPaperSize(event.target.value as PaperSize);
    }, []);

    const onPaperOrientationChanged = React.useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        window.localStorage.setItem(LOCAL_STORAGE_ORIENTATION_KEY, event.target.value);
        setPaperOrientation(event.target.value as PaperOrientation);
    }, []);

    const onChange = React.useCallback((newValue: string) => {
        window.localStorage.setItem(LOCAL_STORAGE_KEY, newValue);
        setValue(newValue);
        // eslint-disable-next-line no-console
        console.log('Body value changed', newValue);
    }, []);

    const onHeaderChange = React.useCallback((newValue: string) => {
        window.localStorage.setItem(LOCAL_STORAGE_HEADER_KEY, newValue);
        setHeaderValue(newValue);
        // eslint-disable-next-line no-console
        console.log('Header value changed', newValue);
    }, []);

    const onFooterChange = React.useCallback((newValue: string) => {
        window.localStorage.setItem(LOCAL_STORAGE_FOOTER_KEY, newValue);
        setFooterValue(newValue);
        // eslint-disable-next-line no-console
        console.log('Footer value changed', newValue);
    }, []);

    const localize = React.useCallback<LocalizeFunction>(
        (_, literal: string, context: any): string => compile(literal)(context),
        [],
    );

    const localizeEnumMember: LocalizeEnumFunction = () => 'FakeNode';

    return (
        <CarbonProvider theme={sageTheme}>
            <div className="App">
                <GlobalStyle />
                <Toast
                    open={!!notificationRequest}
                    onDismiss={(): void => setNotificationRequest(null)}
                    variant={notificationRequest?.type}
                >
                    <div data-testid="notification-content">{notificationRequest?.content}</div>
                </Toast>
                <div>
                    <GridRow columns={12} gutter={8} margin={12}>
                        <GridColumn columnSpan={12}>
                            <h2>Document editor</h2>
                            <XtremDocumentEditor
                                carbonLocale={fakeCarbonLocale}
                                contextProvider={fakeContextProvider}
                                footerValue={footerValue}
                                headerValue={headerValue}
                                isDisabled={isDisabled}
                                locale="en-US"
                                localize={localize}
                                localizeEnumMember={localizeEnumMember}
                                marginBottom={marginBottom}
                                marginLeft={marginLeft}
                                marginRight={marginRight}
                                marginTop={marginTop}
                                onBlur={onBlur}
                                onChange={onChange}
                                onDisplayNotification={onDisplayNotification}
                                onFocus={onFocus}
                                onFooterChange={onFooterChange}
                                onHeaderChange={onHeaderChange}
                                onReady={onReady}
                                paperOrientation={paperOrientation}
                                paperSize={paperSize}
                                value={value}
                            />
                        </GridColumn>

                        <GridColumn columnSpan={2}>
                            <Select
                                id="paperSize"
                                name="paperSize"
                                value={paperSize}
                                onChange={onPaperSizeChanged}
                                label="Paper size"
                            >
                                <Option text="A0" value="a0" />
                                <Option text="A1" value="a1" />
                                <Option text="A2" value="a2" />
                                <Option text="A3" value="a3" />
                                <Option text="A4" value="a4" />
                                <Option text="A5" value="a5" />
                                <Option text="A6" value="a6" />
                                <Option text="Letter" value="ledger" />
                                <Option text="Legal" value="legal" />
                                <Option text="Tabloid" value="tabloid" />
                                <Option text="Ledger" value="ledger" />
                            </Select>
                        </GridColumn>
                        <GridColumn columnSpan={2}>
                            <Select
                                id="paperOrientation"
                                name="paperOrientation"
                                value={paperOrientation}
                                onChange={onPaperOrientationChanged}
                                label="Paper Orientation"
                            >
                                <Option text="Portrait" value="portrait" />
                                <Option text="Landscape" value="landscape" />
                                <Option text="Full screen" value="fullScreen" />
                            </Select>
                        </GridColumn>
                        <GridColumn columnSpan={1}>
                            <NumberComponent
                                label="Margin top"
                                name="marginTop"
                                value={String(marginTop)}
                                onChange={onMarginTopChanged}
                            />
                        </GridColumn>
                        <GridColumn columnSpan={1}>
                            <NumberComponent
                                label="Margin right"
                                name="marginRight"
                                value={String(marginRight)}
                                onChange={onMarginRightChanged}
                            />
                        </GridColumn>
                        <GridColumn columnSpan={1}>
                            <NumberComponent
                                label="Margin bottom"
                                name="marginBottom"
                                value={String(marginBottom)}
                                onChange={onMarginBottomChanged}
                            />
                        </GridColumn>
                        <GridColumn columnSpan={1}>
                            <NumberComponent
                                label="Margin left"
                                name="marginLeft"
                                value={String(marginLeft)}
                                onChange={onMarginLeftChanged}
                            />
                        </GridColumn>
                        <GridColumn columnSpan={1}>
                            <Switch
                                label="Is disabled?"
                                name="isEditorDisabled"
                                onChange={(e): void => {
                                    setDisabled(e.target.checked);
                                }}
                                checked={isDisabled}
                            />
                        </GridColumn>
                        <GridColumn columnSpan={12}>
                            <h2>Body value</h2>
                            <textarea
                                id="bodyValue"
                                style={{ width: '100%', height: '600px' }}
                                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>): void => {
                                    onChange(e.target.value || '');
                                }}
                            >
                                {value}
                            </textarea>
                        </GridColumn>
                        <GridColumn columnSpan={12}>
                            <h2>Header value</h2>
                            <textarea
                                id="headerValue"
                                style={{ width: '100%', height: '600px' }}
                                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>): void => {
                                    onHeaderChange(e.target.value || '');
                                }}
                            >
                                {headerValue}
                            </textarea>
                        </GridColumn>
                        <GridColumn columnSpan={12}>
                            <h2>Footer value</h2>
                            <textarea
                                id="footerValue"
                                style={{ width: '100%', height: '600px' }}
                                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>): void => {
                                    onFooterChange(e.target.value || '');
                                }}
                            >
                                {footerValue}
                            </textarea>
                        </GridColumn>
                    </GridRow>
                </div>
            </div>
        </CarbonProvider>
    );
}

/* istanbul ignore next */
(window as any).start = (): void => {
    createRoot(window.document.getElementById('root') as HTMLElement).render(<ConsumerMock />);
};
