import * as React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import { ButtonView } from '@ckeditor/ckeditor5-ui';
import type { Locale } from '@ckeditor/ckeditor5-utils';
import { COMMAND_ZOOM_IN } from '../../constants';
import type { ReportEditorConfig } from '../../report-editor-config-type';
import { Plugin } from '@ckeditor/ckeditor5-core';

export default class ZoomInUi extends Plugin {
    private view: ButtonView;

    public static get pluginName(): string {
        return 'ZoomInUi' as const;
    }

    public zoomIn = (): void => {
        this.editor.fire(COMMAND_ZOOM_IN);
    };

    public init(): void {
        const editor = this.editor;
        // Add bold button to feature components.
        editor.ui.componentFactory.add(COMMAND_ZOOM_IN, (locale: Locale) => {
            this.view = new ButtonView(locale);
            const { localize } = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;

            this.view.set({
                label: localize('@sage/xtrem-document-editor/zoom-in', 'Zoom in'),
                icon: renderToStaticMarkup(
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M10 3C10.5523 3 11 3.44772 11 4V9H16C16.5523 9 17 9.44772 17 10C17 10.5523 16.5523 11 16 11H11V16C11 16.5523 10.5523 17 10 17C9.44772 17 9 16.5523 9 16V11H4C3.44772 11 3 10.5523 3 10C3 9.44772 3.44772 9 4 9H9V4C9 3.44772 9.44772 3 10 3Z"
                            fill="#000000e6"
                        />
                    </svg>,
                ),
                tooltip: true,
                isToggleable: true,
                isEnabled: true,
            });

            this.listenTo(this.view, 'execute', this.zoomIn);

            return this.view;
        });
    }
}
