import { Command } from '@ckeditor/ckeditor5-core';
import type { Editor } from '@ckeditor/ckeditor5-core';
import type { Element } from '@ckeditor/ckeditor5-engine';
import { Position } from '@ckeditor/ckeditor5-engine';
import type { HtmlComment } from '@ckeditor/ckeditor5-html-support';
import { ATTR_CONTEXT_CONDITION } from '@sage/xtrem-shared';
import { isNil } from 'lodash';
import type { ConditionDialogRequest } from '../../components/condition-dialog';
import { getContextObjectType } from '../utils';
import type { ReportEditorConfig } from '../../report-editor-config-type';
import type { ConditionEditorProperty, FilterParameter } from '@sage/xtrem-ui-components';
import { writeConditionComment } from './conditional-block-utils';

export default class ConditionalBlockInsertCommand extends Command {
    private parameters: FilterParameter[] = [];

    constructor(editor: Editor) {
        super(editor);
        const reportEditorConfig = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;
        reportEditorConfig.contextProvider.getDocumentParameters().then(parameters => {
            this.parameters = parameters || [];
        });
    }

    get htmlCommentPlugin(): HtmlComment {
        return this.editor.plugins.get('HtmlComment') as HtmlComment;
    }

    private readonly insertCondition = (conditions: ConditionEditorProperty[]): void => {
        if (isNil(conditions)) {
            return;
        }

        this.editor.model.change(writer => {
            const conditionalBlockElement = writer.createElement('conditionalBlock', {
                [ATTR_CONTEXT_CONDITION]: JSON.stringify(conditions),
            });
            const conditionalBlockBodyElement = writer.createElement('conditionalBlockBody');
            const conditionalBlockFooterElement = writer.createElement('conditionalBlockFooter');
            writer.append(conditionalBlockBodyElement, conditionalBlockElement);
            writer.append(conditionalBlockFooterElement, conditionalBlockElement);
            this.editor.model.insertObject(conditionalBlockElement);

            this.htmlCommentPlugin.createHtmlComment(
                new Position(
                    conditionalBlockElement.root as Element,
                    conditionalBlockFooterElement.getPath(),
                    'toNext',
                ),
                '{{/if}}',
            );

            writeConditionComment(this.htmlCommentPlugin, conditions, conditionalBlockBodyElement, this.parameters);
        });
    };

    execute(): void {
        const selection = this.editor.model.document.selection;
        const contextObjectType = getContextObjectType(selection.focus?.parent);
        if (!contextObjectType) {
            return;
        }
        const reportEditorConfig = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;
        const conditionDialogRequest: ConditionDialogRequest = {
            existingConditions: [],
            onFinished: this.insertCondition,
        };
        reportEditorConfig.onOpenConditionDialog(conditionDialogRequest);
    }

    refresh(): void {
        const model = this.editor.model;
        const selection = model.document.selection;
        const position = selection.getFirstPosition();
        if (position === null) {
            return;
        }
        const allowedIn = model.schema.findAllowedParent(position, 'conditionalBlock');
        const contextObjectType = getContextObjectType(selection.focus?.parent);

        this.isEnabled = allowedIn !== null && !!contextObjectType && contextObjectType !== '$root';
    }
}
