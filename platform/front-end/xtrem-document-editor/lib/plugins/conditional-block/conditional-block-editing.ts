import { Plugin } from '@ckeditor/ckeditor5-core';
import ConditionalBlockInsertCommand from './conditional-block-insert-command';
import { COMMAND_EDIT_CONDITION_BLOCK, COMMAND_INSERT_CONDITIONAL_BLOCK } from '../../constants';
import { ATTR_CONTEXT_CONDITION } from '@sage/xtrem-shared';
import { toWidget, toWidgetEditable } from '@ckeditor/ckeditor5-widget';
import ConditionalBlockEditCommand from './conditional-block-edit-command';

export default class ConditionalBlockEditing extends Plugin {
    init(): void {
        this._defineSchema();
        this._defineConverters();
        this.editor.commands.add(COMMAND_INSERT_CONDITIONAL_BLOCK, new ConditionalBlockInsertCommand(this.editor));
        this.editor.commands.add(COMMAND_EDIT_CONDITION_BLOCK, new ConditionalBlockEditCommand(this.editor));
    }

    _defineSchema(): void {
        const schema = this.editor.model.schema;
        schema.register('conditionalBlock', {
            inheritAllFrom: '$blockObject',
            allowContentOf: 'conditionalBody',
            allowAttributes: [ATTR_CONTEXT_CONDITION],
        });

        schema.register('conditionalBlockBody', {
            isLimit: true,
            allowIn: 'conditionalBlock',
            // Allow content which is allowed in blocks (i.e. text with attributes).
            allowContentOf: '$block',
        });
        schema.register('conditionalBlockFooter', {
            isLimit: true,
            allowIn: 'conditionalBlock',
            allowContentOf: [],
        });
    }

    _defineConverters(): void {
        const conversion = this.editor.conversion;

        conversion
            .for('upcast')
            .elementToElement({
                model: (viewElement, { writer }) => {
                    return writer.createElement('conditionalBlock', {
                        [ATTR_CONTEXT_CONDITION]: viewElement.getAttribute(ATTR_CONTEXT_CONDITION),
                    });
                },
                view: {
                    name: 'section',
                    classes: 'conditional-block',
                    attributes: [ATTR_CONTEXT_CONDITION],
                },
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_CONDITION,
                view: ATTR_CONTEXT_CONDITION,
            });

        conversion
            .for('dataDowncast')
            .elementToElement({
                model: 'conditionalBlock',
                view: {
                    name: 'section',
                    classes: 'conditional-block',
                },
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_CONDITION,
                view: ATTR_CONTEXT_CONDITION,
            });

        conversion
            .for('editingDowncast')
            .elementToElement({
                model: 'conditionalBlock',
                view: (_modelElement, { writer: viewWriter }) => {
                    const section = viewWriter.createContainerElement('section', { class: 'conditional-block' });
                    return toWidget(section, viewWriter);
                },
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_CONDITION,
                view: ATTR_CONTEXT_CONDITION,
            });

        conversion.for('upcast').elementToElement({
            model: 'conditionalBlockBody',
            view: {
                name: 'div',
                classes: 'conditional-block-body',
            },
        });

        conversion.for('dataDowncast').elementToElement({
            model: 'conditionalBlockBody',
            view: {
                name: 'div',
                classes: 'conditional-block-body',
            },
        });

        conversion.for('editingDowncast').elementToElement({
            model: 'conditionalBlockBody',
            view: (_modelElement, { writer: viewWriter }) => {
                const body = viewWriter.createEditableElement('div', { class: 'conditional-block-body' });
                return toWidgetEditable(body, viewWriter);
            },
        });

        conversion.elementToElement({
            model: 'conditionalBlockFooter',
            view: {
                name: 'div',
                classes: 'conditional-block-footer',
            },
        });
    }
}
