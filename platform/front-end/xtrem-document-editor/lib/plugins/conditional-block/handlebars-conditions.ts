import type { NodeDetails, TreeElement } from '@sage/xtrem-shared';
import { GraphQLTypes } from '@sage/xtrem-shared';
import type { ConditionEditorProperty, FilterParameter, ValueType } from '@sage/xtrem-ui-components';
import { isArray, isEmpty, isNumber, isString } from 'lodash';

function serializeLiteral(targetType: keyof typeof GraphQLTypes, value: any): string | number {
    switch (targetType) {
        case 'String':
        case 'Id':
        case 'IntReference':
        case 'DateTime':
            if (isString(value)) {
                return `"${value}"`;
            }
            return String(value);
        case 'Date':
            if (isString(value)) {
                return `"${value}"`;
            }
            if (isString(value.rawValue)) {
                return `"${value.rawValue}"`;
            }
            throw new Error(`Failed serialize date value: ${value}`);
        case 'Decimal':
        case 'Float':
            return `"${value}"`;
        case 'Int':
            if (isNumber(value)) {
                return value;
            }
            if (isString(value)) {
                return parseInt(value, 10);
            }
            throw new Error(`Invalid number value ${value}`);
        case 'Boolean':
            if (value === 'true' || value === 'false') {
                return value;
            }
            if (value === true || value === false) {
                return String(value);
            }
            throw new Error(`Invalid boolean value ${value}`);

        default:
            throw new Error(`Unknown target type ${targetType}`);
    }
}

function serializeConditionValue(
    value: any,
    valueType: ValueType,
    parameters?: FilterParameter[],
    value1?: any,
    valueType1?: ValueType,
): string | string[] | number {
    if (!valueType) {
        throw new Error('Value type is required for condition generation');
    }

    // Only value2 can be a constant, so we need to format the constant to the type of value1
    if (valueType === 'constant') {
        if (!valueType1) {
            throw new Error('Value type 1 is required for serializing value2 to a constant');
        }
        const targetType =
            valueType1 === 'parameter'
                ? parameters?.find(p => p.name === value1)?.type
                : (value1 as TreeElement<NodeDetails>).data.type;

        if (isArray(value)) {
            return value.map(v => serializeLiteral(GraphQLTypes.String, v)) as string[];
        }

        return serializeLiteral(targetType as keyof typeof GraphQLTypes, value);
    }

    if (valueType === 'parameter') {
        return `@root.${value}`;
    }

    if (valueType === 'property') {
        const propertyValue = value as TreeElement<NodeDetails>;
        return propertyValue.id;
    }

    throw new Error(`Unknown value type ${valueType}`);
}

export function createHandlebarsCondition(
    conditions: ConditionEditorProperty[],
    parameters: FilterParameter[],
): string {
    if (isEmpty(conditions)) {
        return '';
    }
    const buckets: ConditionEditorProperty[][] = [[]];
    // We split the conditions whenever there is an `or` conjunction
    conditions.forEach(condition => {
        if (condition.conjunction === 'or') {
            buckets.push([]);
        }

        buckets[buckets.length - 1].push(condition);
    });

    const mappedBuckets = buckets.map(bucket => {
        const mappedConditions = bucket
            .map((condition): string => {
                const isEmptyFilter = condition.operator === 'empty' || condition.operator === 'notEmpty';
                if (!condition.valueType1 || (!isEmptyFilter && !condition.valueType2)) {
                    throw new Error('Missing value type, cannot generate condition');
                }
                const value1 = serializeConditionValue(condition.value1, condition.valueType1, parameters);
                const value2 = !isEmptyFilter
                    ? serializeConditionValue(
                          condition.value2,
                          condition.valueType2!,
                          parameters,
                          condition.value1,
                          condition.valueType1,
                      )
                    : null;

                switch (condition.operator) {
                    case 'equals':
                        return `( eq ${value1} ${value2} )`;
                    case 'greaterThan':
                        return `( gt ${value1} ${value2} )`;
                    case 'greaterThanOrEqual':
                        return `( gte ${value1} ${value2} )`;
                    case 'lessThan':
                        return `( lt ${value1} ${value2} )`;
                    case 'lessThanOrEqual':
                        return `( lte ${value1} ${value2} )`;
                    case 'contains':
                        return `( containsString ${value2} ${value1} )`;
                    case 'startsWith':
                        return `( startsWithString ${value2} ${value1} )`;
                    case 'endsWith':
                        return `( endsWithString ${value2} ${value1} )`;
                    case 'notEqual':
                        return `( not ( eq ${value1} ${value2} ) )`;
                    case 'empty':
                        return `( or ( eq ${value1} null ) ( eq ${value1} "" ) ( eq ${value1} undefined ) )`;
                    case 'notEmpty':
                        return `( not ( or ( eq ${value1} null ) ( eq ${value1} "" ) ( eq ${value1} undefined ) ) )`;
                    case 'set':
                        if (condition.valueType2 === 'property' || condition.valueType2 === 'parameter') {
                            return `( eq ${value1} ${value2} )`;
                        }
                        if (isArray(value2)) {
                            const orConditions = value2.map(v => `( eq ${value1} ${v} )`).join(' ');
                            return `(or ${orConditions})`;
                        }
                        throw new Error('Invalid value type for set condition with constant value');
                    case 'multiNotEqual':
                        if (condition.valueType2 === 'property' || condition.valueType2 === 'parameter') {
                            return `( not ( eq ${value1} ${value2} ) )`;
                        }
                        if (isArray(value2)) {
                            const andConditions = value2.map(v => `( not ( eq ${value1} ${v} ) )`).join(' ');
                            return `(and ${andConditions})`;
                        }
                        throw new Error('Invalid value type for set condition with constant value');
                    // case 'multiNotEqual':
                    default:
                        throw new Error(`Unknown operator ${condition.operator}`);
                }
            })
            .join(' ');

        if (bucket.length === 1) {
            return mappedConditions;
        }

        return `( and ${mappedConditions} )`;
    });

    if (buckets.length === 1) {
        return mappedBuckets[0];
    }
    return `( or ${mappedBuckets.join(' ')} )`;
}
