import helpers from '@sage/handlebars-helpers';
import handlebars from 'handlebars';
import { fakeParameters } from '../../../consumer-mock-static-definitions';
import { createHandlebarsCondition } from '../handlebars-conditions';

const commonProperty = {
    canFilter: true,
    canSort: true,
    enumType: null,
    isCustom: false,
    dataType: '',
    dataTypeDetails: null,
    targetNode: '',
    targetNodeDetails: null,
    isOnInputType: true,
    isOnOutputType: true,
    isMutable: false,
    kind: 'SCALAR',
};

describe('handlebars conditions', () => {
    // We compile an if statement with the generated code to ensure that the generated code is handlebars-compliant
    const compileTemplate = (condition: string) =>
        handlebars.compile(`
        {{#if ${condition}}}
            <div>test</div>
        {{/if}}
    `)({
            _id: '132',
            invoiceNumber: '123',
            grossTotal: 3444,
            netTotal: 234,
            customer: {
                name: '<PERSON>',
                email: '<EMAIL>',
            },
        });

    beforeAll(() => {
        helpers({ handlebars });
        handlebars.registerHelper('startsWithString', jest.fn());
        handlebars.registerHelper('endsWithString', jest.fn());
        handlebars.registerHelper('containsString', jest.fn());
    });
    describe('createHandlebarsCondition', () => {
        describe('property -> property', () => {
            it('should generate simple condition', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'property',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: {
                                label: 'ID',
                                data: {
                                    name: '_id',
                                    label: 'ID',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: '_id',
                                key: '_id',
                                labelKey: 'ID',
                                labelPath: 'ID',
                            },
                            operator: 'equals',
                        },
                    ],
                    [],
                );

                expect(result).toEqual('( eq invoiceNumber _id )');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should generate simple condition for enums', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'property',
                            _id: '1',
                            value1: {
                                label: 'Credit Rating',
                                data: {
                                    ...commonProperty,
                                    type: '@sage/xtrem-master-data/CreditRating',
                                    kind: 'ENUM',
                                    enumValues: ['great', 'good', 'ok', 'notBad', 'awful'],
                                    isCollection: false,
                                    name: 'creditRating',
                                    canFilter: true,
                                    canSort: true,
                                    label: 'Credit Rating',
                                    enumType: 'productCategoryEnumType',
                                    isCustom: false,
                                    dataType: 'productCategoryEnumDataType',

                                    node: '@sage/xtrem-master-data/CreditRating',
                                },
                                id: 'customer.creditRating',
                                key: 'customer.creditRating',
                                labelKey: 'Credit Rating',
                                labelPath: 'Customer > Credit Rating',
                            },
                            value2: {
                                label: 'Acceptable Credit Rating',
                                data: {
                                    ...commonProperty,
                                    type: '@sage/xtrem-master-data/CreditRating',
                                    kind: 'ENUM',
                                    enumValues: ['great', 'good', 'ok', 'notBad', 'awful'],
                                    isCollection: false,
                                    name: 'acceptableCreditRating',
                                    canFilter: true,
                                    canSort: true,
                                    label: 'Acceptable Credit Rating',
                                    enumType: 'productCategoryEnumType',
                                    isCustom: false,
                                    dataType: 'productCategoryEnumDataType',

                                    node: '@sage/xtrem-master-data/CreditRating',
                                },
                                id: 'acceptableCreditRating',
                                key: 'acceptableCreditRating',
                                labelKey: 'Acceptable Credit Rating',
                                labelPath: 'Acceptable Credit Rating',
                            },
                            operator: 'set',
                        },
                    ],
                    [],
                );

                expect(result).toEqual('( eq customer.creditRating acceptableCreditRating )');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should generate simple not equal condition', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'property',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: {
                                label: 'ID',
                                data: {
                                    name: '_id',
                                    label: 'ID',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: '_id',
                                key: '_id',
                                labelKey: 'ID',
                                labelPath: 'ID',
                            },
                            operator: 'notEqual',
                        },
                    ],
                    [],
                );

                expect(result).toEqual('( not ( eq invoiceNumber _id ) )');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should generate condition with two lines with AND', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'property',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: {
                                label: 'ID',
                                data: {
                                    name: '_id',
                                    label: 'ID',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: '_id',
                                key: '_id',
                                labelKey: 'ID',
                                labelPath: 'ID',
                            },
                            operator: 'greaterThanOrEqual',
                        },
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'property',
                            _id: '1',
                            value1: {
                                label: 'Net Total',
                                data: {
                                    name: 'netTotal',
                                    label: 'Net Total',
                                    type: 'Int',
                                    node: 'Int',
                                    ...commonProperty,
                                },
                                id: 'netTotal',
                                key: 'netTotal',
                                labelKey: 'Net Total',
                                labelPath: 'Net Total',
                            },
                            value2: {
                                label: 'Gross Total',
                                data: {
                                    name: 'grossTotal',
                                    label: 'Gross Total',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'grossTotal',
                                key: 'grossTotal',
                                labelKey: 'Gross Total',
                                labelPath: 'Gross Total',
                            },
                            operator: 'lessThanOrEqual',
                        },
                    ],
                    [],
                );

                expect(result).toEqual('( and ( gte invoiceNumber _id ) ( lte netTotal grossTotal ) )');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should generate condition with two lines with OR conjunction', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'property',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: {
                                label: 'ID',
                                data: {
                                    name: '_id',
                                    label: 'ID',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: '_id',
                                key: '_id',
                                labelKey: 'ID',
                                labelPath: 'ID',
                            },
                            operator: 'greaterThan',
                        },
                        {
                            conjunction: 'or',
                            valueType1: 'property',
                            valueType2: 'property',
                            _id: '1',
                            value1: {
                                label: 'Net Total',
                                data: {
                                    name: 'netTotal',
                                    label: 'Net Total',
                                    type: 'Int',
                                    node: 'Int',
                                    ...commonProperty,
                                },
                                id: 'netTotal',
                                key: 'netTotal',
                                labelKey: 'Net Total',
                                labelPath: 'Net Total',
                            },
                            value2: {
                                label: 'Gross Total',
                                data: {
                                    name: 'grossTotal',
                                    label: 'Gross Total',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'grossTotal',
                                key: 'grossTotal',
                                labelKey: 'Gross Total',
                                labelPath: 'Gross Total',
                            },
                            operator: 'lessThan',
                        },
                    ],
                    [],
                );

                expect(result).toEqual('( or ( gt invoiceNumber _id ) ( lt netTotal grossTotal ) )');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should generate condition with four lines with 2-2 OR conjunction', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'property',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: {
                                label: 'ID',
                                data: {
                                    name: '_id',
                                    label: 'ID',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: '_id',
                                key: '_id',
                                labelKey: 'ID',
                                labelPath: 'ID',
                            },
                            operator: 'greaterThan',
                        },
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'property',
                            _id: '1',
                            value1: {
                                label: 'Net Total',
                                data: {
                                    name: 'netTotal',
                                    label: 'Net Total',
                                    type: 'Int',
                                    node: 'Int',
                                    ...commonProperty,
                                },
                                id: 'netTotal',
                                key: 'netTotal',
                                labelKey: 'Net Total',
                                labelPath: 'Net Total',
                            },
                            value2: {
                                label: 'Gross Total',
                                data: {
                                    name: 'grossTotal',
                                    label: 'Gross Total',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'grossTotal',
                                key: 'grossTotal',
                                labelKey: 'Gross Total',
                                labelPath: 'Gross Total',
                            },
                            operator: 'lessThan',
                        },
                        {
                            conjunction: 'or',
                            valueType1: 'property',
                            valueType2: 'property',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: {
                                label: 'ID',
                                data: {
                                    name: '_id',
                                    label: 'ID',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: '_id',
                                key: '_id',
                                labelKey: 'ID',
                                labelPath: 'ID',
                            },
                            operator: 'greaterThan',
                        },
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'property',
                            _id: '1',
                            value1: {
                                label: 'Net Total',
                                data: {
                                    name: 'netTotal',
                                    label: 'Net Total',
                                    type: 'Int',
                                    node: 'Int',
                                    ...commonProperty,
                                },
                                id: 'netTotal',
                                key: 'netTotal',
                                labelKey: 'Net Total',
                                labelPath: 'Net Total',
                            },
                            value2: {
                                label: 'Gross Total',
                                data: {
                                    name: 'grossTotal',
                                    label: 'Gross Total',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'grossTotal',
                                key: 'grossTotal',
                                labelKey: 'Gross Total',
                                labelPath: 'Gross Total',
                            },
                            operator: 'lessThan',
                        },
                    ],
                    [],
                );

                expect(result).toEqual(
                    '( or ( and ( gt invoiceNumber _id ) ( lt netTotal grossTotal ) ) ( and ( gt invoiceNumber _id ) ( lt netTotal grossTotal ) ) )',
                );
                expect(() => compileTemplate(result)).not.toThrow();
            });
        });

        describe('property - constant', () => {
            it('should generate simple condition', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: 'INV0123',
                            operator: 'equals',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual('( eq invoiceNumber "INV0123" )');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should generate simple startsWith condition', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: 'INV0',
                            operator: 'startsWith',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual('( startsWithString "INV0" invoiceNumber )');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should generate simple contains condition', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: 'V0',
                            operator: 'contains',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual('( containsString "V0" invoiceNumber )');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should generate simple endsWith condition', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: '1234',
                            operator: 'endsWith',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual('( endsWithString "1234" invoiceNumber )');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should generate simple condition for enums', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Credit Rating',
                                data: {
                                    ...commonProperty,
                                    type: '@sage/xtrem-master-data/CreditRating',
                                    kind: 'ENUM',
                                    enumValues: ['great', 'good', 'ok', 'notBad', 'awful'],
                                    isCollection: false,
                                    name: 'creditRating',
                                    canFilter: true,
                                    canSort: true,
                                    label: 'Credit Rating',
                                    enumType: 'productCategoryEnumType',
                                    isCustom: false,
                                    dataType: 'productCategoryEnumDataType',

                                    node: '@sage/xtrem-master-data/CreditRating',
                                },
                                id: 'customer.creditRating',
                                key: 'customer.creditRating',
                                labelKey: 'Credit Rating',
                                labelPath: 'Customer > Credit Rating',
                            },
                            value2: ['great', 'ok'],
                            operator: 'set',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual('(or ( eq customer.creditRating "great" ) ( eq customer.creditRating "ok" ))');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should generate AND-based negation for enum constants with multiNotEqual', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Credit Rating',
                                data: {
                                    ...commonProperty,
                                    type: '@sage/xtrem-master-data/CreditRating',
                                    kind: 'ENUM',
                                    enumValues: ['great', 'good', 'ok'],
                                    isCollection: false,
                                    name: 'creditRating',
                                    canFilter: true,
                                    canSort: true,
                                    label: 'Credit Rating',
                                    enumType: 'productCategoryEnumType',
                                    isCustom: false,
                                    dataType: 'productCategoryEnumDataType',
                                    node: '@sage/xtrem-master-data/CreditRating',
                                },
                                id: 'customer.creditRating',
                                key: 'customer.creditRating',
                                labelKey: 'Credit Rating',
                                labelPath: 'Customer > Credit Rating',
                            },
                            value2: ['great', 'ok'],
                            operator: 'multiNotEqual',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual(
                    '(and ( not ( eq customer.creditRating "great" ) ) ( not ( eq customer.creditRating "ok" ) ))',
                );
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should generate condition with two lines with AND', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: 'INV0456',
                            operator: 'greaterThanOrEqual',
                        },
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Net Total',
                                data: {
                                    name: 'netTotal',
                                    label: 'Net Total',
                                    type: 'Int',
                                    node: 'Int',
                                    ...commonProperty,
                                },
                                id: 'netTotal',
                                key: 'netTotal',
                                labelKey: 'Net Total',
                                labelPath: 'Net Total',
                            },
                            value2: '12345',
                            operator: 'lessThanOrEqual',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual('( and ( gte invoiceNumber "INV0456" ) ( lte netTotal 12345 ) )');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should serialize Int constant as raw number', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Net Total',
                                data: {
                                    name: 'netTotal',
                                    label: 'Net Total',
                                    type: 'Int',
                                    node: 'Int',
                                    ...commonProperty,
                                },
                                id: 'netTotal',
                                key: 'netTotal',
                                labelKey: 'Net Total',
                                labelPath: 'Net Total',
                            },
                            value2: '123',
                            operator: 'equals',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual('( eq netTotal 123 )');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should serialize Float constant as quoted string', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Gross Total',
                                data: {
                                    name: 'grossTotal',
                                    label: 'Gross Total',
                                    type: 'Float',
                                    node: 'Float',
                                    ...commonProperty,
                                },
                                id: 'grossTotal',
                                key: 'grossTotal',
                                labelKey: 'Gross Total',
                                labelPath: 'Gross Total',
                            },
                            value2: 123.45,
                            operator: 'equals',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual('( eq grossTotal "123.45" )');
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should serialize Decimal constant as quoted string', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Unit Price',
                                data: {
                                    name: 'unitPrice',
                                    label: 'Unit Price',
                                    type: 'Decimal',
                                    node: 'Decimal',
                                    ...commonProperty,
                                },
                                id: 'unitPrice',
                                key: 'unitPrice',
                                labelKey: 'Unit Price',
                                labelPath: 'Unit Price',
                            },
                            value2: 49.99,
                            operator: 'equals',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual('( eq unitPrice "49.99" )');
                expect(() => compileTemplate(result)).not.toThrow();
            });
        });

        describe('empty operator', () => {
            it('should create filter with empty property', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: null,
                            operator: 'empty',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual(
                    '( or ( eq invoiceNumber null ) ( eq invoiceNumber "" ) ( eq invoiceNumber undefined ) )',
                );
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should create filter with empty parameter', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'parameter',
                            valueType2: 'constant',
                            _id: '1',
                            value1: 'invoiceNetTotal',
                            value2: null,
                            operator: 'empty',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual(
                    '( or ( eq @root.invoiceNetTotal null ) ( eq @root.invoiceNetTotal "" ) ( eq @root.invoiceNetTotal undefined ) )',
                );
                expect(() => compileTemplate(result)).not.toThrow();
            });
        });

        describe('not empty operator', () => {
            it('should create filter with not empty property', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'property',
                            valueType2: 'constant',
                            _id: '1',
                            value1: {
                                label: 'Invoice Number',
                                data: {
                                    name: 'invoiceNumber',
                                    label: 'Invoice Number',
                                    type: 'String',
                                    node: 'String',
                                    ...commonProperty,
                                },
                                id: 'invoiceNumber',
                                key: 'invoiceNumber',
                                labelKey: 'Invoice Number',
                                labelPath: 'Invoice Number',
                            },
                            value2: null,
                            operator: 'notEmpty',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual(
                    '( not ( or ( eq invoiceNumber null ) ( eq invoiceNumber "" ) ( eq invoiceNumber undefined ) ) )',
                );
                expect(() => compileTemplate(result)).not.toThrow();
            });

            it('should create filter with not empty parameter', () => {
                const result = createHandlebarsCondition(
                    [
                        {
                            conjunction: 'and',
                            valueType1: 'parameter',
                            valueType2: 'constant',
                            _id: '1',
                            value1: 'invoiceNetTotal',
                            value2: null,
                            operator: 'notEmpty',
                        },
                    ],
                    fakeParameters,
                );

                expect(result).toEqual(
                    '( not ( or ( eq @root.invoiceNetTotal null ) ( eq @root.invoiceNetTotal "" ) ( eq @root.invoiceNetTotal undefined ) ) )',
                );
                expect(() => compileTemplate(result)).not.toThrow();
            });
        });
    });
});
