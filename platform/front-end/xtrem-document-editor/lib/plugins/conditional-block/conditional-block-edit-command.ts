import { Command } from '@ckeditor/ckeditor5-core';
import type { Editor } from '@ckeditor/ckeditor5-core';
import { ATTR_CONTEXT_CONDITION } from '@sage/xtrem-shared';
import type { ConditionEditorProperty, FilterParameter } from '@sage/xtrem-ui-components';
import { getContextCondition } from '../utils';
import type { HtmlComment } from '@ckeditor/ckeditor5-html-support';
import type { Element } from '@ckeditor/ckeditor5-engine';
import { Position, Range } from '@ckeditor/ckeditor5-engine';
import { writeConditionComment } from './conditional-block-utils';
import type { ReportEditorConfig } from 'lib/report-editor-config-type';

export default class ConditionalBlockEditCommand extends Command {
    private parameters: FilterParameter[] = [];

    constructor(editor: Editor) {
        super(editor);
        const reportEditorConfig = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;
        reportEditorConfig.contextProvider.getDocumentParameters().then(parameters => {
            this.parameters = parameters || [];
        });
    }

    get htmlCommentPlugin(): HtmlComment {
        return this.editor.plugins.get('HtmlComment') as HtmlComment;
    }

    execute({ conditions }: { conditions: ConditionEditorProperty[] }): void {
        const editor = this.editor;
        const model = this.editor.model;

        editor.model.change(writer => {
            const selection = model.document.selection;
            const firstPosition = selection.getFirstPosition();
            if (!firstPosition) {
                return;
            }
            const conditionalBlock = firstPosition.findAncestor('conditionalBlock');
            const root = conditionalBlock?.root as unknown as Element;
            if (!root || !conditionalBlock) {
                return;
            }

            const conditionalBlockBodyElement = conditionalBlock.getChild(0) as Element;
            const conditionalBlockFooterElement = conditionalBlock.getChild(1);

            if (!conditionalBlockBodyElement || !conditionalBlockFooterElement) {
                return;
            }

            // Update condition in the handlebars tag
            const startPath = conditionalBlock.getChild(0)?.getPath();
            const endPath = conditionalBlock.getChild(1)?.getPath();

            if (!startPath || !endPath) {
                return;
            }

            // We need to find the HTML comment containing the handlebars condition
            const range = new Range(new Position(root, startPath), new Position(root, endPath));
            const commendIds = this.htmlCommentPlugin.getHtmlCommentsInRange(range);
            if (commendIds.length === 0) {
                return;
            }

            // Remove the outdated condition
            commendIds.forEach(comment => {
                const commentData = this.htmlCommentPlugin.getHtmlCommentData(comment);
                if (commentData?.content.startsWith('{{#if')) {
                    this.htmlCommentPlugin.removeHtmlComment(comment);
                }
            });

            // Insert the new condition
            writeConditionComment(this.htmlCommentPlugin, conditions, conditionalBlockBodyElement, this.parameters);

            // Update condition JSON property
            writer.setAttribute(ATTR_CONTEXT_CONDITION, JSON.stringify(conditions), conditionalBlock);
        });
    }

    refresh(): void {
        this.isEnabled = getContextCondition(this.editor.model.document.selection.focus?.parent) !== null;
    }
}
