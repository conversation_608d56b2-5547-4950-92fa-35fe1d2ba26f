import { Plugin } from '@ckeditor/ckeditor5-core';
import { ButtonView } from '@ckeditor/ckeditor5-ui';
import { COMMAND_INSERT_CONDITIONAL_BLOCK } from '../../constants';
import type { ReportEditorConfig } from '../../report-editor-config-type';
import type { Locale } from '@ckeditor/ckeditor5-utils';
import { renderToStaticMarkup } from 'react-dom/server';
import * as React from 'react';

export default class ConditionalBlockUI extends Plugin {
    private view: ButtonView;

    init(): void {
        const editor = this.editor;

        editor.ui.componentFactory.add(COMMAND_INSERT_CONDITIONAL_BLOCK, (locale: Locale) => {
            // The state of the button will be bound to the widget command.
            const command = editor.commands.get(COMMAND_INSERT_CONDITIONAL_BLOCK);

            // The button will be an instance of ButtonView.
            this.view = new ButtonView(locale);
            const { localize } = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;
            this.view.set({
                label: localize('@sage/xtrem-document-editor/conditional-block-title', 'Conditional block'),
                icon: renderToStaticMarkup(
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path
                            d="M18.0919 0L20 1.90813L1.90813 20L0 18.0919L18.0919 0ZM17.9902 6.99618V18.9896H6.99618V16.9907H16.0352L15.9913 6.99618H17.9902ZM12.9929 0.999454V2.99836H3.95387L3.99782 12.9929H1.99891V0.999454H12.9929Z"
                            fill="#000000e6"
                        />
                    </svg>,
                ),
                tooltip: true,
                isEnabled: true,
            });

            // Bind the state of the button to the command.
            this.view.bind('isOn', 'isEnabled').to(command as any, 'value', 'isEnabled');

            // Execute the command when the button is clicked (executed).
            this.listenTo(this.view, 'execute', () => editor.execute(COMMAND_INSERT_CONDITIONAL_BLOCK));

            return this.view;
        });
    }
}
