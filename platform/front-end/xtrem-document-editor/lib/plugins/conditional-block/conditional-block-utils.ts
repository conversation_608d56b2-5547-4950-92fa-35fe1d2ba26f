import type { Element } from '@ckeditor/ckeditor5-engine';
import { Position } from '@ckeditor/ckeditor5-engine';
import type { HtmlComment } from '@ckeditor/ckeditor5-html-support';
import type { ConditionEditorProperty, FilterParameter } from '@sage/xtrem-ui-components';
import { createHandlebarsCondition } from './handlebars-conditions';

export function writeConditionComment(
    htmlCommentPlugin: HtmlComment,
    conditions: ConditionEditorProperty[],
    conditionalBlockBodyElement: Element,
    parameters: FilterParameter[],
): void {
    const commentContent = `{{#if ${createHandlebarsCondition(conditions, parameters)}}}`;
    const root = conditionalBlockBodyElement.root as Element;

    htmlCommentPlugin.createHtmlComment(
        new Position(root, conditionalBlockBodyElement.getPath(), 'toPrevious'),
        commentContent,
    );
}
