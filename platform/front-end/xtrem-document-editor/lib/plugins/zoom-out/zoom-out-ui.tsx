import * as React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import { ButtonView } from '@ckeditor/ckeditor5-ui';
import type { Locale } from '@ckeditor/ckeditor5-utils';
import { COMMAND_ZOOM_OUT } from '../../constants';
import type { ReportEditorConfig } from '../../report-editor-config-type';
import { Plugin } from '@ckeditor/ckeditor5-core';

export default class ZoomOutUi extends Plugin {
    private view: ButtonView;

    public static get pluginName(): string {
        return 'ZoomOutUi' as const;
    }

    public zoomOut = (): void => {
        this.editor.fire(COMMAND_ZOOM_OUT);
    };

    public init(): void {
        const editor = this.editor;
        // Add bold button to feature components.
        editor.ui.componentFactory.add(COMMAND_ZOOM_OUT, (locale: Locale) => {
            this.view = new ButtonView(locale);
            const { localize } = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;

            this.view.set({
                label: localize('@sage/xtrem-document-editor/zoom-out', 'Zoom out'),
                icon: renderToStaticMarkup(
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path
                            d="M16.3 11H3.7C3.28 11 3 10.6 3 10C3 9.4 3.28 9 3.7 9H16.3C16.72 9 17 9.4 17 10C17 10.6 16.72 11 16.3 11Z"
                            fill="#000000e6"
                        />
                    </svg>,
                ),
                tooltip: true,
                isToggleable: true,
                isEnabled: true,
            });

            this.listenTo(this.view, 'execute', this.zoomOut);

            return this.view;
        });
    }
}
