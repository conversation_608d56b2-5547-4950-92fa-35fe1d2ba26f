import { Plugin } from '@ckeditor/ckeditor5-core';
import ToggleDataPropertiesPanelUi from './toggle-data-properties-panel-ui';
import ToggleDataPropertiesPanelEditing from './toggle-data-properties-panel-editing';

export default class ToggleDataPropertiesPanel extends Plugin {
    static get requires(): Array<typeof Plugin> {
        return [ToggleDataPropertiesPanelUi, ToggleDataPropertiesPanelEditing];
    }
}
