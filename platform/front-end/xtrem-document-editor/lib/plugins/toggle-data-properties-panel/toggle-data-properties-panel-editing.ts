import { Plugin } from '@ckeditor/ckeditor5-core';
import { COMMAND_TOGGLE_DATA_PROPERTIES } from '../../constants';
import ToggleDataPropertiesPanelCommand from './toggle-data-properties-panel-command';

export default class ToggleDataPropertiesPanelEditing extends Plugin {
    public static get pluginName(): string {
        return 'ToggleDataPropertiesPanel';
    }

    public init(): void {
        this.editor.commands.add(COMMAND_TOGGLE_DATA_PROPERTIES, new ToggleDataPropertiesPanelCommand(this.editor));
    }
}
