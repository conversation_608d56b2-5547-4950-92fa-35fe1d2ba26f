import * as React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import { ButtonView } from '@ckeditor/ckeditor5-ui';
import type { Locale } from '@ckeditor/ckeditor5-utils';
import { COMMAND_TOGGLE_DATA_PROPERTIES } from '../../constants';
import TogglePluginUi from '../toggle-plugin-ui';
import type { ReportEditorConfig } from '../../report-editor-config-type';
import ToggleGlobalPropertiesPanelUi from '../toggle-global-properties-panel/toggle-global-properties-panel-ui';

export default class ToggleDataPropertiesPanelUi extends TogglePluginUi {
    public static get pluginName(): string {
        return 'ToggleDataPropertiesPanelUi' as const;
    }

    public togglePanel = (): void => {
        const newState = !this.view.isOn;
        this.view.set({ isOn: newState });
        const reportEditorConfig = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;
        reportEditorConfig.onDataPropertiesPanelOpenChange(newState);

        if (newState) {
            const globalPropertiesPlugin = this.editor.plugins.get(
                ToggleGlobalPropertiesPanelUi.pluginName,
            ) as ToggleGlobalPropertiesPanelUi;
            globalPropertiesPlugin.view.set({ isOn: false });
        }
    };

    public init(): void {
        const editor = this.editor;

        // Add bold button to feature components.
        editor.ui.componentFactory.add(COMMAND_TOGGLE_DATA_PROPERTIES, (locale: Locale) => {
            this.view = new ButtonView(locale);
            const { localize } = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;

            this.view.set({
                label: localize('@sage/xtrem-document-editor/toggle-data-properties-panel', 'Show/Hide fields panel'),
                icon: renderToStaticMarkup(
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M17.8938 4C19.0934 4 20.0931 5 19.9931 6.2V15.8C19.9931 17 19.0934 18 17.8938 18H2.19924C0.999655 18 0 17 0 15.8V9H6.49776C6.79765 9 6.99758 8.9 7.19752 8.7L11.1961 4H17.8938ZM6.49776 2C7.19752 2 7.79731 2.2 8.29714 2.6L9.09686 3.3L6.29783 6.7C6.0979 6.9 5.798 7 5.4981 7H0V4.2C0 3 0.999655 2 2.19924 2H6.49776Z"
                            fill="#000000e6"
                        />
                    </svg>,
                ),
                tooltip: true,
                isToggleable: true,
                isEnabled: true,
            });

            // Execute command.
            this.listenTo(this.view, 'execute', this.togglePanel);

            return this.view;
        });
    }
}
