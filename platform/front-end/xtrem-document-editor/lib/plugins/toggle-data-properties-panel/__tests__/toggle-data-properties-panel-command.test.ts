import {
    closeLeftPanel,
    getPage,
    getTextContent,
    openDataPropertiesPanel,
    waitForSelectorToDisappear,
} from '../../../__tests__/test-helper';

describe('toggle data properties panel command', () => {
    it('should toggle data properties panel', async () => {
        const page = getPage();
        await openDataPropertiesPanel();
        await page.waitForSelector('.document-editor-left-panel');
        const content = await getTextContent('.document-editor-panel-context-indicator');
        expect(content).toEqual('Global context');
        await closeLeftPanel();
        await waitForSelectorToDisappear('.document-editor-right-panel');
    });
});
