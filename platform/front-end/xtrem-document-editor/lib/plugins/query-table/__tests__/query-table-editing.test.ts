import {
    executeEditorCommand,
    getValue,
    insertSimpleQueryTable,
    restoreEditorValue,
    waitForSelectorToAppear,
} from '../../../__tests__/test-helper';
import { COMMAND_INSERT_QUERY_TABLE } from '../../../constants';

describe('query table editing', () => {
    it('should parse back query table', async () => {
        await insertSimpleQueryTable();

        await restoreEditorValue();

        const updatedEditorValueWithTable = await getValue();

        expect(updatedEditorValueWithTable).toContain(
            '<table class="query-table" data-context-object-type="SalesInvoice"',
        );

        expect(updatedEditorValueWithTable).toMatchSnapshot();

        // We check repeated on and off load to ensure that the value doesn't get corrupted
        await restoreEditorValue();

        const secondUpdatedEditorValueWithTable = await getValue();
        expect(updatedEditorValueWithTable).toEqual(secondUpdatedEditorValueWithTable);
    });

    it('should transform distinct counted date fields to integer in the grouping columns', async () => {
        await executeEditorCommand(COMMAND_INSERT_QUERY_TABLE, {
            lookupResult: {
                filters: [],
                orderBy: {
                    invoiceNumber: 'ascending',
                    totalWithoutTax: 'ascending',
                    orderDate: 'ascending',
                },
                selectedMode: 'list',
                aggregations: {
                    invoiceNumber: {
                        group: 0,
                        operation: 'NONE',
                    },
                    totalWithoutTax: {
                        group: 0,
                        operation: 'sum',
                    },
                    orderDate: {
                        group: 0,
                        operation: 'distinctCount',
                    },
                },
                selectedFields: {
                    invoiceNumber: {
                        name: 'invoiceNumber',
                        label: 'Invoice Number',
                        kind: 'SCALAR',
                        type: 'String',
                        canFilter: true,
                        canSort: true,
                        enumType: null,
                        isCustom: false,
                        dataType: 'string',
                        dataTypeDetails: null,
                        targetNode: '',
                        targetNodeDetails: null,
                        isOnInputType: true,
                        isOnOutputType: true,
                        isMutable: false,
                    },
                    totalWithoutTax: {
                        name: 'totalWithoutTax',
                        label: 'Net Total',
                        kind: 'SCALAR',
                        type: 'Float',
                        canFilter: true,
                        canSort: true,
                        enumType: null,
                        isCustom: false,
                        dataType: 'float',
                        dataTypeDetails: null,
                        targetNode: '',
                        targetNodeDetails: null,
                        isOnInputType: true,
                        isOnOutputType: true,
                        isMutable: false,
                    },
                    orderDate: {
                        name: 'orderDate',
                        label: 'Order Date',
                        kind: 'SCALAR',
                        type: 'Date',
                        canFilter: true,
                        canSort: true,
                        enumType: null,
                        isCustom: false,
                        dataType: 'date',
                        dataTypeDetails: null,
                        targetNode: '',
                        targetNodeDetails: null,
                        isOnInputType: true,
                        isOnOutputType: true,
                        isMutable: false,
                    },
                },
            },
            selectedItem: {
                label: 'Sales Invoice',
                data: {
                    name: 'salesInvoice',
                    label: 'Sales Invoice',
                    kind: 'LIST',
                    type: 'SalesInvoice',
                    namespace: 'xtremSales',
                    iconType: 'csv',
                    canFilter: false,
                    canSort: false,
                    enumType: null,
                    isCustom: false,
                    dataType: 'salesInvoice',
                    dataTypeDetails: {},
                    targetNode: '@sage/xtrem-sales/SalesInvoice',
                    targetNodeDetails: {},
                    isOnInputType: true,
                    isOnOutputType: true,
                    isMutable: false,
                },
                id: 'salesInvoice',
                key: 'salesInvoice',
                labelKey: 'Sales Invoice',
                labelPath: 'Sales Invoice',
            },
            remapInfo: {
                path: 'xtremSales.salesInvoice.query.edges',
                subPath: 'node',
            },
        });

        await waitForSelectorToAppear('.query-table');

        const updatedEditorValueWithTable = await getValue();

        // Normal date property in table body
        expect(updatedEditorValueWithTable).toContain(
            '<span class="property" data-property-display-label="Order Date" data-property-data-type="Date" data-property-name="orderDate" data-property-data-format="FullDate" data-property-parent-context="SalesInvoice">',
        );

        // Distinct count int property in table footer
        expect(updatedEditorValueWithTable).toContain(
            '<span class="property" data-property-display-label="Order Date" data-property-data-type="Int" data-property-name="_blockAggregatedData.orderDate.distinctCount" data-property-data-format="" data-property-parent-context="SalesInvoice">',
        );
    });
});
