import { Plugin } from '@ckeditor/ckeditor5-core';
import { addBackgroundRules, addBorderRules, addPaddingRules } from '@ckeditor/ckeditor5-engine';
import type { ElementAttributes } from '@ckeditor/ckeditor5-engine/src/view/element';
import { HtmlComment } from '@ckeditor/ckeditor5-html-support';
import { ContextualBalloon } from '@ckeditor/ckeditor5-ui';
import { Widget, toWidget, toWidgetEditable } from '@ckeditor/ckeditor5-widget';
import {
    ATTR_ALIAS,
    ATTR_BREAK_PAGE_AFTER,
    ATTR_CLASS,
    ATTR_CONTEXT_FILTER,
    ATTR_CONTEXT_LIST_ORDER,
    ATTR_CONTEXT_OBJECT_PATH,
    ATTR_CONTEXT_OBJECT_TYPE,
    ATTR_FOOTER_GROUP,
    ATTR_QUERY_TABLE_HIDDEN_ROW,
} from '@sage/xtrem-shared';
import { isNil } from 'lodash';
import {
    COMMAND_ALIGNMENT_QUERY_TABLE,
    COMMAND_BACKGROUND_QUERY_TABLE,
    COMMAND_BORDER_COLOR_QUERY_TABLE,
    COMMAND_BORDER_STYLE_QUERY_TABLE,
    COMMAND_BORDER_WIDTH_QUERY_TABLE,
    COMMAND_CELL_BACKGROUND_QUERY_TABLE,
    COMMAND_CELL_BORDER_COLOR_QUERY_TABLE,
    COMMAND_CELL_BORDER_STYLE_QUERY_TABLE,
    COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE,
    COMMAND_CELL_HEIGHT_QUERY_TABLE,
    COMMAND_CELL_HORIZONTAL_ALIGNMENT_QUERY_TABLE,
    COMMAND_CELL_QUERY_TABLE_PADDING,
    COMMAND_CELL_VERTICAL_ALIGNMENT_QUERY_TABLE,
    COMMAND_EDIT_FILTER_QUERY_TABLE,
    COMMAND_EDIT_ORDER_QUERY_TABLE,
    COMMAND_INSERT_COLUMN_QUERY_COMMAND,
    COMMAND_INSERT_PAGE_BREAK_QUERY_TABLE,
    COMMAND_INSERT_QUERY_TABLE,
    COMMAND_REMOVE_COLUMN_QUERY_TABLE,
} from '../../constants';
import InsertQueryTableCommand from './commands/insert-query-table-command';
import QueryTableSelectColumnCommand from './commands/query-select-column-command';
import QueryTableSelectRowCommand from './commands/query-select-row-command';
import QueryTableAlignmentCommand from './commands/query-table-alignment-command';
import QueryTableBackgroundColorCommand from './commands/query-table-background-color-command';
import QueryTableBorderColorCommand from './commands/query-table-border-color-command';
import QueryTableBorderStyleCommand from './commands/query-table-border-style-command';
import QueryTableBorderWidthCommand from './commands/query-table-border-width-command';
import QueryTableCellBackgroundColorCommand from './commands/query-table-cell-background-color-command';
import QueryTableCellBorderColorCommand from './commands/query-table-cell-border-color-command';
import QueryTableCellBorderStyleCommand from './commands/query-table-cell-border-style-command';
import QueryTableCellBorderWidthCommand from './commands/query-table-cell-border-width-command';
import QueryTableCellHeightCommand from './commands/query-table-cell-height-command';
import QueryTableCellHorizontalAlignmentCommand from './commands/query-table-cell-horizontal-alignment-command';
import QueryTableCellPaddingCommand from './commands/query-table-cell-padding-command';
import QueryTableCellVerticalAlignmentCommand from './commands/query-table-cell-vertical-alignment-command';
import QueryTableEditFilterCommand from './commands/query-table-edit-filter-command';
import QueryTableEditOrderCommand from './commands/query-table-edit-order-command';
import QueryTableInsertColumnCommand from './commands/query-table-insert-column-command';
import QueryTableInsertPageBreakCommand from './commands/query-table-insert-page-break-command';
import QueryTableRemoveColumnCommand from './commands/query-table-remove-column-command';
import {
    downcastTableBorderAndBackgroundAttributes,
    enableCellProperty,
    enableHorizontalAlignmentProperty,
    enableTableAlignmentProperty,
    enableTableBorderProperties,
    enableTableCellBorderProperties,
    enableTableProperty,
    enableVerticalAlignmentProperty,
} from './query-table-editing-utils';

export default class QueryTableEditing extends Plugin {
    static get requires(): Array<typeof Plugin> {
        return [Widget, HtmlComment, ContextualBalloon];
    }

    init(): void {
        this._defineSchema();
        this._defineConverters();

        const editor = this.editor;
        const schema = editor.model.schema;
        const conversion = editor.conversion;
        const commands = editor.commands;

        editor.data.addStyleProcessorRules(addBorderRules);
        editor.data.addStyleProcessorRules(addBackgroundRules);
        editor.data.addStyleProcessorRules(addPaddingRules);

        enableTableBorderProperties(schema, conversion);

        enableTableAlignmentProperty(schema, conversion, 'left');

        /*
        enableTableToFigureProperty(schema, conversion, {
            modelAttribute: 'tableWidth',
            styleName: 'width',
            defaultValue: defaultTableProperties.width,
        });
        editor.commands.add('tableWidth', new TableWidthCommand(editor, defaultTableProperties.width));

        enableTableToFigureProperty(schema, conversion, {
            modelAttribute: 'tableHeight',
            styleName: 'height',
            defaultValue: defaultTableProperties.height,
        });
        editor.commands.add('tableHeight', new TableHeightCommand(editor, defaultTableProperties.height));
*/

        enableTableProperty(schema, conversion, {
            modelAttribute: COMMAND_BACKGROUND_QUERY_TABLE,
            styleName: 'background-color',
            defaultValue: 'transparent',
        });

        enableCellProperty(schema, conversion, {
            modelAttribute: COMMAND_CELL_HEIGHT_QUERY_TABLE,
            styleName: 'height',
            defaultValue: '24px',
        });

        enableCellProperty(schema, conversion, {
            modelAttribute: COMMAND_CELL_QUERY_TABLE_PADDING,
            styleName: 'padding',
            // reduceBoxSides: true,
            defaultValue: '4px',
        });

        enableCellProperty(schema, conversion, {
            modelAttribute: COMMAND_CELL_BACKGROUND_QUERY_TABLE,
            styleName: 'background-color',
            defaultValue: 'transparent',
        });

        downcastTableBorderAndBackgroundAttributes(editor);

        enableTableCellBorderProperties(schema, conversion);

        enableHorizontalAlignmentProperty(schema, conversion, 'left');

        enableVerticalAlignmentProperty(schema, conversion, 'top');

        commands.add(COMMAND_INSERT_QUERY_TABLE, new InsertQueryTableCommand(this.editor));
        commands.add(COMMAND_BORDER_COLOR_QUERY_TABLE, new QueryTableBorderColorCommand(editor, '#FFFFFF'));
        commands.add(COMMAND_BORDER_STYLE_QUERY_TABLE, new QueryTableBorderStyleCommand(editor, 'solid'));
        commands.add(COMMAND_BORDER_WIDTH_QUERY_TABLE, new QueryTableBorderWidthCommand(editor, '1px'));
        commands.add(COMMAND_ALIGNMENT_QUERY_TABLE, new QueryTableAlignmentCommand(editor, 'left'));
        commands.add(COMMAND_BACKGROUND_QUERY_TABLE, new QueryTableBackgroundColorCommand(editor, 'transparent'));
        commands.add(COMMAND_CELL_HEIGHT_QUERY_TABLE, new QueryTableCellHeightCommand(editor, '24px'));
        commands.add(COMMAND_CELL_QUERY_TABLE_PADDING, new QueryTableCellPaddingCommand(editor, '4px'));
        commands.add(
            COMMAND_CELL_BACKGROUND_QUERY_TABLE,
            new QueryTableCellBackgroundColorCommand(editor, 'transparent'),
        );
        commands.add(COMMAND_CELL_BORDER_STYLE_QUERY_TABLE, new QueryTableCellBorderStyleCommand(editor, 'solid'));
        commands.add(COMMAND_CELL_BORDER_COLOR_QUERY_TABLE, new QueryTableCellBorderColorCommand(editor, '#FFFFFF'));
        commands.add(COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE, new QueryTableCellBorderWidthCommand(editor, '1px'));
        commands.add(
            COMMAND_CELL_HORIZONTAL_ALIGNMENT_QUERY_TABLE,
            new QueryTableCellHorizontalAlignmentCommand(editor, 'left'),
        );
        commands.add(
            COMMAND_CELL_VERTICAL_ALIGNMENT_QUERY_TABLE,
            new QueryTableCellVerticalAlignmentCommand(editor, 'top'),
        );
        commands.add('querySelectTableRow', new QueryTableSelectRowCommand(editor));
        commands.add('querySelectTableColumn', new QueryTableSelectColumnCommand(editor));
        commands.add(COMMAND_INSERT_COLUMN_QUERY_COMMAND, new QueryTableInsertColumnCommand(editor));
        commands.add(COMMAND_REMOVE_COLUMN_QUERY_TABLE, new QueryTableRemoveColumnCommand(editor));
        commands.add(COMMAND_EDIT_ORDER_QUERY_TABLE, new QueryTableEditOrderCommand(editor));
        commands.add(COMMAND_EDIT_FILTER_QUERY_TABLE, new QueryTableEditFilterCommand(editor));
        commands.add(COMMAND_INSERT_PAGE_BREAK_QUERY_TABLE, new QueryTableInsertPageBreakCommand(editor, ''));
    }

    _defineSchema(): void {
        const schema = this.editor.model.schema;

        schema.register('queryTable', {
            inheritAllFrom: '$blockObject',
            allowAttributes: [
                ATTR_CONTEXT_OBJECT_TYPE,
                ATTR_CONTEXT_OBJECT_PATH,
                ATTR_CONTEXT_LIST_ORDER,
                ATTR_CONTEXT_FILTER,
                ATTR_ALIAS,
            ],
        });

        schema.register('queryTableHead', {
            isLimit: true,
            allowIn: 'queryTable',
            allowContentOf: 'queryTableRow',
        });

        schema.register('queryTableRow', {
            isLimit: true,
            allowIn: ['queryTableHead', 'queryTableBody', 'queryTableFooter'],
            allowContentOf: ['queryTableCell'],
            allowAttributes: [ATTR_QUERY_TABLE_HIDDEN_ROW, ATTR_FOOTER_GROUP, ATTR_BREAK_PAGE_AFTER],
        });

        schema.register('queryTableCell', {
            isLimit: true,
            allowIn: 'queryTableRow',
            allowContentOf: '$container',
            isSelectable: true,
            allowAttributes: ['colspan', 'rowspan'],
        });

        schema.register('queryTableBody', {
            isLimit: true,
            allowIn: 'queryTable',
            allowContentOf: 'queryTableRow',
        });

        schema.register('queryTableFooter', {
            isLimit: true,
            allowIn: 'queryTable',
            allowContentOf: 'queryTableRow',
        });
    }

    _defineConverters(): void {
        const conversion = this.editor.conversion;

        // <queryTable> converters
        conversion
            .for('upcast')
            .elementToElement({
                converterPriority: 'highest',
                model: (viewElement, { writer }) => {
                    return writer.createElement('queryTable', {
                        [ATTR_CONTEXT_OBJECT_TYPE]: viewElement.getAttribute(ATTR_CONTEXT_OBJECT_TYPE),
                        [ATTR_CONTEXT_OBJECT_PATH]: viewElement.getAttribute(ATTR_CONTEXT_OBJECT_PATH),
                        [ATTR_CONTEXT_FILTER]: viewElement.getAttribute(ATTR_CONTEXT_FILTER),
                        [ATTR_CONTEXT_LIST_ORDER]: viewElement.getAttribute(ATTR_CONTEXT_LIST_ORDER),
                        [ATTR_ALIAS]: viewElement.getAttribute(ATTR_ALIAS),
                    });
                },
                view: {
                    name: 'table',
                    classes: 'query-table',
                    attributes: [
                        ATTR_CONTEXT_OBJECT_TYPE,
                        ATTR_CONTEXT_OBJECT_PATH,
                        ATTR_CONTEXT_FILTER,
                        ATTR_CONTEXT_LIST_ORDER,
                        ATTR_ALIAS,
                    ],
                },
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_OBJECT_TYPE,
                view: ATTR_CONTEXT_OBJECT_TYPE,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_OBJECT_TYPE,
                view: ATTR_CONTEXT_OBJECT_TYPE,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_FILTER,
                view: ATTR_CONTEXT_FILTER,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_LIST_ORDER,
                view: ATTR_CONTEXT_LIST_ORDER,
            })
            .attributeToAttribute({
                model: ATTR_ALIAS,
                view: ATTR_ALIAS,
            });

        conversion
            .for('dataDowncast')
            .elementToElement({
                converterPriority: 'highest',
                model: 'queryTable',
                view: {
                    name: 'table',
                    classes: 'query-table',
                },
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_OBJECT_TYPE,
                view: ATTR_CONTEXT_OBJECT_TYPE,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_OBJECT_TYPE,
                view: ATTR_CONTEXT_OBJECT_TYPE,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_FILTER,
                view: ATTR_CONTEXT_FILTER,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_LIST_ORDER,
                view: ATTR_CONTEXT_LIST_ORDER,
            })
            .attributeToAttribute({
                model: ATTR_ALIAS,
                view: ATTR_ALIAS,
            });

        conversion
            .for('editingDowncast')
            .elementToElement({
                converterPriority: 'highest',
                model: 'queryTable',
                view: (modelElement, { writer: viewWriter }) => {
                    const section = viewWriter.createContainerElement('table', {
                        class: 'query-table',
                    });

                    return toWidget(section, viewWriter);
                },
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_OBJECT_TYPE,
                view: ATTR_CONTEXT_OBJECT_TYPE,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_OBJECT_TYPE,
                view: ATTR_CONTEXT_OBJECT_TYPE,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_LIST_ORDER,
                view: ATTR_CONTEXT_LIST_ORDER,
            })
            .attributeToAttribute({
                model: ATTR_ALIAS,
                view: ATTR_ALIAS,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_FILTER,
                view: ATTR_CONTEXT_FILTER,
            });

        // <queryTableHead> converters
        conversion.for('upcast').elementToElement({
            converterPriority: 'highest',
            model: 'queryTableHead',
            view: {
                name: 'thead',
                classes: 'query-table-head',
            },
        });

        conversion.for('dataDowncast').elementToElement({
            converterPriority: 'highest',
            model: 'queryTableHead',
            view: {
                name: 'thead',
                classes: 'query-table-head',
            },
        });

        conversion.for('editingDowncast').elementToElement({
            converterPriority: 'highest',
            model: 'queryTableHead',
            view: (modelElement, { writer: viewWriter }) => {
                return viewWriter.createContainerElement('thead', {
                    class: 'query-table-head',
                });
            },
        });

        // <queryTableRow> converters
        conversion
            .for('upcast')
            .elementToElement({
                converterPriority: 'highest',
                model: 'queryTableRow',
                view: {
                    name: 'tr',
                    classes: 'query-table-row',
                },
            })
            .attributeToAttribute({
                model: ATTR_QUERY_TABLE_HIDDEN_ROW,
                view: ATTR_QUERY_TABLE_HIDDEN_ROW,
            })
            .attributeToAttribute({
                model: ATTR_FOOTER_GROUP,
                view: ATTR_FOOTER_GROUP,
            })
            .attributeToAttribute({
                model: ATTR_BREAK_PAGE_AFTER,
                view: ATTR_BREAK_PAGE_AFTER,
            });

        conversion
            .for('dataDowncast')
            .elementToElement({
                converterPriority: 'highest',
                model: 'queryTableRow',
                view: {
                    name: 'tr',
                    classes: 'query-table-row',
                },
            })
            .attributeToAttribute({
                model: ATTR_QUERY_TABLE_HIDDEN_ROW,
                view: ATTR_QUERY_TABLE_HIDDEN_ROW,
            })
            .attributeToAttribute({
                model: ATTR_FOOTER_GROUP,
                view: ATTR_FOOTER_GROUP,
            })
            .attributeToAttribute({
                model: ATTR_BREAK_PAGE_AFTER,
                view: ATTR_BREAK_PAGE_AFTER,
            });

        conversion
            .for('editingDowncast')
            .elementToElement({
                converterPriority: 'highest',
                model: 'queryTableRow',
                view: (modelElement, { writer: viewWriter }) => {
                    const elementAttributes: ElementAttributes = {
                        class: 'query-table-row',
                    };

                    if (modelElement.getAttribute(ATTR_QUERY_TABLE_HIDDEN_ROW)) {
                        elementAttributes[ATTR_QUERY_TABLE_HIDDEN_ROW] = 1;
                    }

                    if (!isNil(modelElement.getAttribute(ATTR_FOOTER_GROUP))) {
                        elementAttributes[ATTR_FOOTER_GROUP] = String(modelElement.getAttribute(ATTR_FOOTER_GROUP));
                    }

                    return viewWriter.createContainerElement('tr', elementAttributes);
                },
            })
            .attributeToAttribute({
                model: ATTR_QUERY_TABLE_HIDDEN_ROW,
                view: ATTR_QUERY_TABLE_HIDDEN_ROW,
            })
            .attributeToAttribute({
                model: ATTR_FOOTER_GROUP,
                view: ATTR_FOOTER_GROUP,
            })
            .attributeToAttribute({
                model: ATTR_BREAK_PAGE_AFTER,
                view: ATTR_BREAK_PAGE_AFTER,
            });

        // <queryTableBody> converters
        conversion.for('upcast').elementToElement({
            converterPriority: 'highest',
            model: 'queryTableBody',
            view: {
                name: 'tbody',
                classes: 'query-table-body',
            },
        });

        conversion.for('dataDowncast').elementToElement({
            converterPriority: 'high',
            model: 'queryTableBody',
            view: {
                name: 'tbody',
                classes: 'query-table-body',
            },
        });

        conversion.for('editingDowncast').elementToElement({
            converterPriority: 'highest',
            model: 'queryTableBody',
            view: (modelElement, { writer: viewWriter }) => {
                return viewWriter.createContainerElement('tbody', {
                    class: 'query-table-body',
                });
            },
        });

        conversion.for('upcast').elementToElement({
            converterPriority: 'highest',
            model: (viewElement, { writer }) => {
                const attributes: any = {};
                if (viewElement.hasClass('e-right-align')) {
                    attributes.class = 'e-right-align';
                }
                return writer.createElement('queryTableCell', attributes);
            },
            view: {
                name: 'td',
                classes: 'query-table-cell',
            },
        });

        conversion.for('dataDowncast').elementToElement({
            converterPriority: 'highest',
            model: 'queryTableCell',
            view: (modelElement, { writer }) => {
                const classes = ['query-table-cell'];
                const modelClass = modelElement.getAttribute(ATTR_CLASS);
                if (typeof modelClass === 'string' && modelClass.includes('e-right-align')) {
                    classes.push('e-right-align');
                }
                return writer.createContainerElement('td', {
                    class: classes.join(' '),
                });
            },
        });

        conversion.for('editingDowncast').elementToElement({
            converterPriority: 'highest',
            model: 'queryTableCell',
            view: (modelElement, { writer: viewWriter }) => {
                const classes = ['query-table-cell'];
                const modelClass = modelElement.getAttribute(ATTR_CLASS);
                if (typeof modelClass === 'string' && modelClass.includes('e-right-align')) {
                    classes.push('e-right-align');
                }
                const section = viewWriter.createEditableElement('td', {
                    class: classes.join(' '),
                });
                return toWidgetEditable(section, viewWriter);
            },
        });

        // <queryTableFooter> converters
        conversion.for('upcast').elementToElement({
            converterPriority: 'highest',
            model: 'queryTableFooter',
            view: {
                name: 'tfoot',
                classes: 'query-table-footer',
            },
        });

        conversion.for('dataDowncast').elementToElement({
            converterPriority: 'highest',
            model: 'queryTableFooter',
            view: {
                name: 'tfoot',
                classes: 'query-table-footer',
            },
        });

        conversion.for('editingDowncast').elementToElement({
            converterPriority: 'highest',
            model: 'queryTableFooter',
            view: (modelElement, { writer: viewWriter }) => {
                return viewWriter.createContainerElement('tfoot', {
                    class: 'query-table-footer',
                });
            },
        });
    }
}
