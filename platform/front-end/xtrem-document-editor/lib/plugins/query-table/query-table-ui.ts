import { Plugin } from '@ckeditor/ckeditor5-core';
import { createInsertQueryTableButton } from './query-table-factory';
import { ContextualBalloon } from '@ckeditor/ckeditor5-ui';

export default class QueryTableUi extends Plugin {
    static get requires(): Array<typeof Plugin> {
        return [ContextualBalloon];
    }

    init(): void {
        const editor = this.editor;
        editor.ui.componentFactory.add('queryTable', () => {
            return createInsertQueryTableButton(editor);
        });
    }
}
