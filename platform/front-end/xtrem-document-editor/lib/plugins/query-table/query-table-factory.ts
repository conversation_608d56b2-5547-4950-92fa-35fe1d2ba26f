import type { Command, Editor } from '@ckeditor/ckeditor5-core';
import { ButtonView } from '@ckeditor/ckeditor5-ui';
import type { LookupRequest } from '../../components/insert-list-property-dialog/insert-list-property-dialog';
import { COMMAND_INSERT_QUERY_TABLE } from '../../constants';
import { getContextObjectType } from '../utils';
import type { ContextType } from '../../context-provider';

const onInsertQueryTableButtonClick = (editor: Editor) => (): void => {
    const reportEditorConfig = editor.config.get('reportEditorConfig') as any;
    const selection = editor.model.document.selection;
    const contextObjectType = getContextObjectType(selection.focus?.parent);

    if (!contextObjectType) {
        return;
    }

    const currentRootName: ContextType =
        (editor.model.document.selection.getFirstRange()?.start.root.rootName as ContextType) || 'body';

    const lookupRequest: LookupRequest = {
        root: currentRootName,
        contextObjectType,
        onSelected: result => editor.execute(COMMAND_INSERT_QUERY_TABLE, result),
    };
    reportEditorConfig.openLookup(lookupRequest);
};

export const createInsertQueryTableButton = (editor: Editor): ButtonView => {
    const buttonView = new ButtonView(editor.locale);
    buttonView.set({
        label: 'Insert Query Table',
        tooltip: true,
        withText: true,
    });

    const command = editor.commands.get(COMMAND_INSERT_QUERY_TABLE) as Command;
    buttonView.bind('isEnabled').to(command);

    // Execute the command when the dropdown item is clicked (executed).
    editor.listenTo(buttonView, 'execute', onInsertQueryTableButtonClick(editor));

    return buttonView;
};
