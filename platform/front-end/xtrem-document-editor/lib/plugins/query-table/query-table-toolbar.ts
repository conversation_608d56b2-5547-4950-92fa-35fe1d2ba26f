import { Plugin } from '@ckeditor/ckeditor5-core';
import { WidgetToolbarRepository } from '@ckeditor/ckeditor5-widget';
import { getSelectedTableWidget, getTableWidgetAncestor } from './ui/widget';

/**
 * The table toolbar class. It creates toolbars for the table feature and its content (for now only for the table cell content).
 *
 * The table toolbar shows up when a table widget is selected. Its components (e.g. buttons) are created based on the
 * {@link module:table/tableconfig~TableConfig#tableToolbar `table.tableToolbar` configuration option}.
 *
 * Table content toolbar shows up when the selection is inside the content of a table. It creates its component based on the
 * {@link module:table/tableconfig~TableConfig#contentToolbar `table.contentToolbar` configuration option}.
 */
export default class TableToolbar extends Plugin {
    /**
     * @inheritDoc
     */
    public static get requires(): Array<typeof Plugin> {
        return [WidgetToolbarRepository];
    }

    /**
     * @inheritDoc
     */
    public static get pluginName(): string {
        return 'QueryTableToolbar';
    }

    /**
     * @inheritDoc
     */
    public afterInit(): void {
        const editor = this.editor;
        const t = editor.t;
        const widgetToolbarRepository = editor.plugins.get(WidgetToolbarRepository);

        const tableContentToolbarItems = editor.config.get('table.contentToolbar') as any;

        const tableToolbarItems = editor.config.get('table.tableToolbar') as any;

        if (tableContentToolbarItems) {
            widgetToolbarRepository.register('tableContent', {
                ariaLabel: t('Table toolbar'),
                items: tableContentToolbarItems,
                getRelatedElement: getTableWidgetAncestor,
            });
        }

        if (tableToolbarItems) {
            widgetToolbarRepository.register('table', {
                ariaLabel: t('Table toolbar'),
                items: tableToolbarItems,
                getRelatedElement: getSelectedTableWidget,
            });
        }
    }
}
