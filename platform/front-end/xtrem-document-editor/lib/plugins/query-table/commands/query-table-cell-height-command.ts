import type { Editor } from '@ckeditor/ckeditor5-core';
import { COMMAND_CELL_HEIGHT_QUERY_TABLE } from '../../../constants';
import { addDefaultUnitToNumericValue } from '../query-table-editing-utils';
import QueryTableCellPropertyCommand from './query-table-cell-property-command';

/**
 * The table cell height command.
 *
 * The command is registered by the {@link module:table/tablecellproperties/tablecellpropertiesediting~TableCellPropertiesEditing} as
 * the `'tableCellHeight'` editor command.
 *
 * To change the height of selected cells, execute the command:
 *
 * ```ts
 * editor.execute( 'tableCellHeight', {
 *   value: '50px'
 * } );
 * ```
 *
 * **Note**: This command adds the default `'px'` unit to numeric values. Executing:
 *
 * ```ts
 * editor.execute( 'tableCellHeight', {
 *   value: '50'
 * } );
 * ```
 *
 * will set the `height` attribute to `'50px'` in the model.
 */
export default class QueryTableCellHeightCommand extends QueryTableCellPropertyCommand {
    /**
     * Creates a new `TableCellHeightCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value of the attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, COMMAND_CELL_HEIGHT_QUERY_TABLE, defaultValue);
    }

    /**
     * @inheritDoc
     */
    protected override _getValueToSet(value: string | number | undefined): string | number | undefined {
        return addDefaultUnitToNumericValue(value, 'px');
    }
}
