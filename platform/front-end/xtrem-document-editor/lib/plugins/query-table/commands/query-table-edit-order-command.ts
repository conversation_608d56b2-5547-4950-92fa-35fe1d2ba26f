import { Command } from '@ckeditor/ckeditor5-core';
import type { Dict } from '@sage/xtrem-shared';
import { ATTR_CONTEXT_LIST_ORDER } from '@sage/xtrem-shared';
import type { Order } from '@sage/xtrem-ui-components';
import { getContextSortOrder } from '../../utils';

export default class QueryTableEditOrderCommand extends Command {
    execute({ orderBy }: { orderBy: Dict<Order> }): void {
        const editor = this.editor;
        const model = this.editor.model;

        editor.model.change(writer => {
            const selection = model.document.selection;
            const queryTableElement =
                selection.getFirstPosition()?.findAncestor('queryTable') ||
                selection.getFirstPosition()?.findAncestor('recordContext');

            if (queryTableElement) {
                writer.setAttribute(ATTR_CONTEXT_LIST_ORDER, JSON.stringify(orderBy), queryTableElement);
            }
        });
    }

    refresh(): void {
        this.isEnabled = getContextSortOrder(this.editor.model.document.selection.focus?.parent) !== null;
    }
}
