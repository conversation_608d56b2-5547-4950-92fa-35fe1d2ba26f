import type { Editor } from '@ckeditor/ckeditor5-core';
import type { Element } from '@ckeditor/ckeditor5-engine';
import { COMMAND_BORDER_STYLE_QUERY_TABLE } from '../../../constants';
import { getSingleValue } from '../query-table-editing-utils';
import QueryTablePropertyCommand from './query-table-property-command';

/**
 * The table style border command.
 *
 * The command is registered by the {@link module:table/tableproperties/tablepropertiesediting~TablePropertiesEditing} as
 * the `'tableBorderStyle'` editor command.
 *
 * To change the border style of the selected table, execute the command:
 *
 * ```ts
 * editor.execute( 'tableBorderStyle', {
 *   value: 'dashed'
 * } );
 * ```
 */
export default class QueryTableBorderStyleCommand extends QueryTablePropertyCommand {
    /**
     * Creates a new `TableBorderStyleCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value of the attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, COMMAND_BORDER_STYLE_QUERY_TABLE, defaultValue);
    }

    /**
     * @inheritDoc
     */
    protected override _getValue(table: Element): string | undefined {
        if (!table) {
            return undefined;
        }

        return getSingleValue(table.getAttribute(this.attributeName) as string);
    }
}
