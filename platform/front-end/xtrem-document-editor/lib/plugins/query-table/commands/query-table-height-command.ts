import { addDefaultUnitToNumericValue } from '../query-table-editing-utils';
import QueryTablePropertyCommand from './query-table-property-command';
import type { Editor } from '@ckeditor/ckeditor5-core';

/**
 * The table height command.
 *
 * The command is registered by the {@link module:table/tableproperties/tablepropertiesediting~TablePropertiesEditing} as
 * the `'tableHeight'` editor command.
 *
 * To change the height of the selected table, execute the command:
 *
 * ```ts
 * editor.execute( 'tableHeight', {
 *   value: '500px'
 * } );
 * ```
 *
 * **Note**: This command adds the default `'px'` unit to numeric values. Executing:
 *
 * ```ts
 * editor.execute( 'tableHeight', {
 *   value: '50'
 * } );
 * ```
 *
 * will set the `height` attribute to `'50px'` in the model.
 */
export default class QueryTableHeightCommand extends QueryTablePropertyCommand {
    /**
     * Creates a new `TableHeightCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value of the attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, 'queryTableHeight', defaultValue);
    }

    /**
     * @inheritDoc
     */
    protected override _getValueToSet(value: string | number | undefined): string | number | undefined {
        const v = addDefaultUnitToNumericValue(value, 'px');

        if (v === this._defaultValue) {
            return undefined;
        }

        return v;
    }
}
