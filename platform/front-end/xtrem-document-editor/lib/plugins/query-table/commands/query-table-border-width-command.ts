import type { Editor } from '@ckeditor/ckeditor5-core';
import type { Element } from '@ckeditor/ckeditor5-engine';
import { COMMAND_BORDER_WIDTH_QUERY_TABLE } from '../../../constants';
import { addDefaultUnitToNumericValue, getSingleValue } from '../query-table-editing-utils';
import QueryTablePropertyCommand from './query-table-property-command';

/**
 * The table width border command.
 *
 * The command is registered by the {@link module:table/tableproperties/tablepropertiesediting~TablePropertiesEditing} as
 * the `'tableBorderWidth'` editor command.
 *
 * To change the border width of the selected table, execute the command:
 *
 * ```ts
 * editor.execute( 'tableBorderWidth', {
 *   value: '5px'
 * } );
 * ```
 *
 * **Note**: This command adds the default `'px'` unit to numeric values. Executing:
 *
 * ```ts
 * editor.execute( 'tableBorderWidth', {
 *   value: '5'
 * } );
 * ```
 *
 * will set the `borderWidth` attribute to `'5px'` in the model.
 */
export default class QueryTableBorderWidthCommand extends QueryTablePropertyCommand {
    /**
     * Creates a new `TableBorderWidthCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value of the attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, COMMAND_BORDER_WIDTH_QUERY_TABLE, defaultValue);
    }

    /**
     * @inheritDoc
     */
    protected override _getValue(table: Element): string | undefined {
        if (!table) {
            return undefined;
        }

        return getSingleValue(table.getAttribute(this.attributeName) as string);
    }

    /**
     * @inheritDoc
     */
    protected override _getValueToSet(value: string | number | undefined): string | number | undefined {
        const newValue = addDefaultUnitToNumericValue(value, 'px');

        return newValue;
    }
}
