import type { Editor } from '@ckeditor/ckeditor5-core';
import { COMMAND_ALIGNMENT_QUERY_TABLE } from '../../../constants';
import QueryTablePropertyCommand from './query-table-property-command';

/**
 * The table alignment command.
 *
 * The command is registered by the {@link module:table/tableproperties/tablepropertiesediting~TablePropertiesEditing} as
 * the `'tableAlignment'` editor command.
 *
 * To change the alignment of the selected table, execute the command:
 *
 * ```ts
 * editor.execute( 'tableAlignment', {
 *   value: 'right'
 * } );
 * ```
 */
export default class QueryTableAlignmentCommand extends QueryTablePropertyCommand {
    /**
     * Creates a new `TableAlignmentCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value for the "alignment" attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, COMMAND_ALIGNMENT_QUERY_TABLE, defaultValue);
    }
}
