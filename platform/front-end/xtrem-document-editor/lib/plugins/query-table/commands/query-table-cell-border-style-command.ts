import type { Editor } from '@ckeditor/ckeditor5-core';
import type { Element } from '@ckeditor/ckeditor5-engine';
import { COMMAND_CELL_BORDER_STYLE_QUERY_TABLE } from '../../../constants';
import { getSingleValue } from '../query-table-editing-utils';
import QueryTableCellPropertyCommand from './query-table-cell-property-command';

/**
 * The table cell border style command.
 *
 * The command is registered by the {@link module:table/tablecellproperties/tablecellpropertiesediting~TableCellPropertiesEditing} as
 * the `'tableCellBorderStyle'` editor command.
 *
 * To change the border style of selected cells, execute the command:
 *
 * ```ts
 * editor.execute( 'tableCellBorderStyle', {
 *   value: 'dashed'
 * } );
 * ```
 */
export default class QueryTableCellBorderStyleCommand extends QueryTableCellPropertyCommand {
    /**
     * Creates a new `TableCellBorderStyleCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value of the attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, COMMAND_CELL_BORDER_STYLE_QUERY_TABLE, defaultValue);
    }

    /**
     * @inheritDoc
     */
    protected override _getAttribute(tableCell: Element): string | undefined {
        if (!tableCell) {
            return undefined;
        }

        return getSingleValue(tableCell.getAttribute(this.attributeName) as string | undefined);
    }
}
