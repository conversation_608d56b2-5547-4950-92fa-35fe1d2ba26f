import type { Editor } from '@ckeditor/ckeditor5-core';
import type { Element } from '@ckeditor/ckeditor5-engine';
import { COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE } from '../../../constants';
import { addDefaultUnitToNumericValue, getSingleValue } from '../query-table-editing-utils';
import QueryTableCellPropertyCommand from './query-table-cell-property-command';

/**
 * The table cell border width command.
 *
 * The command is registered by the {@link module:table/tablecellproperties/tablecellpropertiesediting~TableCellPropertiesEditing} as
 * the `'tableCellBorderWidth'` editor command.
 *
 * To change the border width of selected cells, execute the command:
 *
 * ```ts
 * editor.execute( 'tableCellBorderWidth', {
 *   value: '5px'
 * } );
 * ```
 *
 * **Note**: This command adds the default `'px'` unit to numeric values. Executing:
 *
 * ```ts
 * editor.execute( 'tableCellBorderWidth', {
 *   value: '5'
 * } );
 * ```
 *
 * will set the `borderWidth` attribute to `'5px'` in the model.
 */
export default class QueryTableCellBorderWidthCommand extends QueryTableCellPropertyCommand {
    /**
     * Creates a new `TableCellBorderWidthCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value of the attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE, defaultValue);
    }

    /**
     * @inheritDoc
     */
    protected override _getAttribute(tableCell: Element): string | undefined {
        if (!tableCell) {
            return undefined;
        }

        return getSingleValue(tableCell.getAttribute(this.attributeName) as string);
    }

    /**
     * @inheritDoc
     */
    protected override _getValueToSet(value: string | number | undefined): string | number | undefined {
        return addDefaultUnitToNumericValue(value, 'px');
    }
}
