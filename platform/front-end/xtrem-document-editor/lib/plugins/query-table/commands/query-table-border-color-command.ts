import type { Editor } from '@ckeditor/ckeditor5-core';
import type { Element } from '@ckeditor/ckeditor5-engine';
import { COMMAND_BORDER_COLOR_QUERY_TABLE } from '../../../constants';
import { getSingleValue } from '../query-table-editing-utils';
import QueryTablePropertyCommand from './query-table-property-command';

/**
 * The table border color command.
 *
 * The command is registered by the {@link module:table/tableproperties/tablepropertiesediting~TablePropertiesEditing} as
 * the `'tableBorderColor'` editor command.
 *
 * To change the border color of the selected table, execute the command:
 *
 * ```ts
 * editor.execute( 'tableBorderColor', {
 *   value: '#f00'
 * } );
 * ```
 */
export default class QueryTableBorderColorCommand extends QueryTablePropertyCommand {
    /**
     * Creates a new `TableBorderColorCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value of the attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, COMMAND_BORDER_COLOR_QUERY_TABLE, defaultValue);
    }

    /**
     * @inheritDoc
     */
    protected override _getValue(table: Element): string | undefined {
        if (!table) {
            return undefined;
        }

        return getSingleValue(table.getAttribute(this.attributeName) as string);
    }
}
