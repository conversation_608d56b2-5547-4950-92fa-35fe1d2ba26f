import { Command, type Editor } from '@ckeditor/ckeditor5-core';
import type { Range, Element } from '@ckeditor/ckeditor5-engine';
import QueryTableUtils from '../utils/query-table-utils';

/**
 * The select row command.
 *
 * The command is registered by {@link module:table/tableediting~TableEditing} as the `'selectTableRow'` editor command.
 *
 * To select the rows containing the selected cells, execute the command:
 *
 * ```ts
 * editor.execute( 'selectTableRow' );
 * ```
 */
export default class QueryTableSelectRowCommand extends Command {
    /**
     * @inheritDoc
     */
    constructor(editor: Editor) {
        super(editor);

        // It does not affect data so should be enabled in read-only mode.
        this.affectsData = false;
    }

    /**
     * @inheritDoc
     */
    public override refresh(): void {
        const tableUtils: QueryTableUtils = this.editor.plugins.get(QueryTableUtils);
        const selectedCells = tableUtils.getSelectionAffectedTableCells(this.editor.model.document.selection);

        this.isEnabled = selectedCells.length > 0;
    }

    /**
     * @inheritDoc
     */
    public override execute(): void {
        const model = this.editor.model;
        const tableUtils: QueryTableUtils = this.editor.plugins.get(QueryTableUtils);
        const referenceCells = tableUtils.getSelectionAffectedTableCells(model.document.selection);
        const rowIndexes = tableUtils.getRowIndexes(referenceCells);

        const table = referenceCells[0].findAncestor('queryTable')!;
        const rangesToSelect: Array<Range> = [];

        for (let rowIndex = rowIndexes.first; rowIndex <= rowIndexes.last; rowIndex += 1) {
            for (const cell of (table.getChild(rowIndex) as Element).getChildren()) {
                rangesToSelect.push(model.createRangeOn(cell));
            }
        }

        model.change(writer => {
            writer.setSelection(rangesToSelect);
        });
    }
}
