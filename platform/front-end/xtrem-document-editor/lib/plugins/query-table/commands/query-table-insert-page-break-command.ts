import type { Editor } from '@ckeditor/ckeditor5-core';
import QueryTableRowPropertyCommand from './query-table-row-property-command';
import { getFooterGroup } from '../../utils';
import { ATTR_BREAK_PAGE_AFTER } from '@sage/xtrem-shared';

export default class QueryTableInsertPageBreakCommand extends QueryTableRowPropertyCommand {
    /**
     * Creates a new `QueryTableRowPropertyCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value for the "page break" attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, ATTR_BREAK_PAGE_AFTER, defaultValue);
    }

    public override refresh(): void {
        const model = this.editor.model;
        const selection = model.document.selection;
        const parent = selection.focus?.parent;

        const footerGroupAttribute = getFooterGroup(parent);

        this.isEnabled = footerGroupAttribute !== null && footerGroupAttribute !== 'footer';
    }
}
