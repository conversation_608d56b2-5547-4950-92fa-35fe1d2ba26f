import { Command } from '@ckeditor/ckeditor5-core';
import QueryTableUtils from '../utils/query-table-utils';

/**
 * The insert column command.
 *
 * The command is registered by {@link module:table/tableediting~TableEditing} as the `'insertTableColumnLeft'` and
 * `'insertTableColumnRight'` editor commands.
 *
 * To insert a column to the left of the selected cell, execute the following command:
 *
 * ```ts
 * editor.execute( 'insertTableColumnLeft' );
 * ```
 *
 * To insert a column to the right of the selected cell, execute the following command:
 *
 * ```ts
 * editor.execute( 'insertTableColumnRight' );
 * ```
 */
export default class QueryTableInsertColumnCommand extends Command {
    /**
     * @inheritDoc
     */
    public override refresh(): void {
        const selection = this.editor.model.document.selection;
        const tableUtils: QueryTableUtils = this.editor.plugins.get(QueryTableUtils);
        const isAnyCellSelected = !!tableUtils.getSelectionAffectedTableCells(selection).length;

        this.isEnabled = isAnyCellSelected;
    }

    /**
     * Executes the command.
     *
     * Depending on the command's {@link #order} value, it inserts a column to the `'left'` or `'right'` of the column
     * in which the selection is set.
     *
     * @fires execute
     */
    public override execute({ order = 'left' }: { order?: 'left' | 'right' } = {}): void {
        const editor = this.editor;
        const selection = editor.model.document.selection;
        const queryTableUtils: QueryTableUtils = editor.plugins.get(QueryTableUtils);
        const insertBefore = order === 'left';

        const affectedTableCells = queryTableUtils.getSelectionAffectedTableCells(selection);
        const columnIndexes = queryTableUtils.getColumnIndexes(affectedTableCells);

        const column = insertBefore ? columnIndexes.first : columnIndexes.last;
        const table = affectedTableCells[0].findAncestor('queryTable')!;

        queryTableUtils.queryTableInsertColumns(table, { columns: 1, at: insertBefore ? column : column + 1 });
    }
}
