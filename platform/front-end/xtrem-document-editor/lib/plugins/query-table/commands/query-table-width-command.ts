import type { Editor } from '@ckeditor/ckeditor5-core';
import { addDefaultUnitToNumericValue } from '../query-table-editing-utils';
import QueryTablePropertyCommand from './query-table-property-command';

/**
 * The table width command.
 *
 * The command is registered by the {@link module:table/tableproperties/tablepropertiesediting~TablePropertiesEditing} as
 * the `'tableWidth'` editor command.
 *
 * To change the width of the selected table, execute the command:
 *
 * ```ts
 * editor.execute( 'tableWidth', {
 *   value: '400px'
 * } );
 * ```
 *
 * **Note**: This command adds the default `'px'` unit to numeric values. Executing:
 *
 * ```ts
 * editor.execute( 'tableWidth', {
 *   value: '50'
 * } );
 * ```
 *
 * will set the `width` attribute to `'50px'` in the model.
 */
export default class QueryTableWidthCommand extends QueryTablePropertyCommand {
    /**
     * Creates a new `TableWidthCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value of the attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, 'queryTableWidth', defaultValue);
    }

    /**
     * @inheritDoc
     */
    public override _getValueToSet(value: string | number | undefined): string | number | undefined {
        return addDefaultUnitToNumericValue(value, 'px');
    }
}
