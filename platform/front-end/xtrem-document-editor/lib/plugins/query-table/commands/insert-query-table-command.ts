import type { Editor } from '@ckeditor/ckeditor5-core';
import { Command } from '@ckeditor/ckeditor5-core';
import type { Element, Writer } from '@ckeditor/ckeditor5-engine';
import { Position } from '@ckeditor/ckeditor5-engine';
import type { HtmlComment } from '@ckeditor/ckeditor5-html-support';
import tokens from '@sage/design-tokens/js/base/common';
import type { Dict, NodeDetails, TreeElement } from '@sage/xtrem-shared';
import {
    ATTR_ALIAS,
    ATTR_CONTEXT_FILTER,
    ATTR_CONTEXT_LIST_ORDER,
    ATTR_CONTEXT_OBJECT_PATH,
    ATTR_CONTEXT_OBJECT_TYPE,
    ATTR_FOOTER_GROUP,
    ATTR_QUERY_TABLE_HIDDEN_ROW,
    Aggregations,
    BLOCK_AGGREGATION_PROPERTY_NAME,
} from '@sage/xtrem-shared';
import { cloneDeep, isEmpty, reverse, uniq } from 'lodash';
import type {
    DocumentEditorInsertDialogResult,
    GroupAggregation,
} from '../../../components/insert-list-property-dialog/insert-list-property-dialog';
import {
    COMMAND_CELL_BACKGROUND_QUERY_TABLE,
    COMMAND_CELL_BORDER_COLOR_QUERY_TABLE,
    COMMAND_CELL_BORDER_STYLE_QUERY_TABLE,
    COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE,
    COMMAND_CELL_QUERY_TABLE_PADDING,
    COMMAND_CELL_VERTICAL_ALIGNMENT_QUERY_TABLE,
} from '../../../constants';
import type { DataModelProperty, ObjectInsertionDetails } from '../../../context-provider';
import InsertPropertyCommand from '../../property/insert-property-command';
import { generateQueryAlias } from '../../utils';

export interface InsertQueryTableCommandArgument {
    lookupResult: DocumentEditorInsertDialogResult;
    selectedItem: TreeElement<DataModelProperty>;
    remapInfo: ObjectInsertionDetails;
}

export default class InsertQueryTableCommand extends Command {
    private readonly defaultCellAttributes = {
        [COMMAND_CELL_BORDER_COLOR_QUERY_TABLE]: tokens.colorsYin090.substring(0, 7).toUpperCase(),
        [COMMAND_CELL_BORDER_STYLE_QUERY_TABLE]: 'solid',
        [COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE]: '1px',
        [COMMAND_CELL_QUERY_TABLE_PADDING]: '2px',
        [COMMAND_CELL_VERTICAL_ALIGNMENT_QUERY_TABLE]: 'top',
    };

    constructor(editor: Editor) {
        super(editor);
        this.affectsData = false;
    }

    get htmlCommentPlugin(): HtmlComment {
        return this.editor.plugins.get('HtmlComment') as HtmlComment;
    }

    private createHiddenRow(writer: Writer, parentElement: Element, colSpan: number): Element {
        /**
         * This hidden row is needed because we need some anchor element to add the handlebars comments before.
         * We can't anchor behind elements, only before them.
         *  */
        const tableForEachClosingHiddenRow = writer.createElement('queryTableRow', {
            [ATTR_QUERY_TABLE_HIDDEN_ROW]: 1,
        });
        writer.append(tableForEachClosingHiddenRow, parentElement);
        const tableHiddenCell = writer.createElement('queryTableCell', {
            colspan: colSpan,
        });
        writer.append(tableHiddenCell, tableForEachClosingHiddenRow);
        const queryTableCellParagraph = writer.createElement('paragraph');
        writer.append(queryTableCellParagraph, tableHiddenCell);

        return tableForEachClosingHiddenRow;
    }

    private writeTableBodyRepeatingRow(
        writer: Writer,
        selectedFields: Dict<DataModelProperty>,
        contextDatatype: NodeDetails['type'],
        tableBodyElement: Element,
    ): Element {
        const queryTableBodyRow = writer.createElement('queryTableRow');
        writer.append(queryTableBodyRow, tableBodyElement);

        Object.keys(selectedFields).forEach(key => {
            const cellAttributes: any = { ...this.defaultCellAttributes };
            const dataType = selectedFields[key].type;
            const classes = (cellAttributes.class || '').split(' ');
            if (dataType === 'Float' || dataType === 'Decimal' || dataType === 'Int') {
                classes.push('e-right-align');
            }
            cellAttributes.class = classes.join(' ').trim();
            const queryTableCell = writer.createElement('queryTableCell', cellAttributes);
            const queryTableCellParagraph = writer.createElement('paragraph');

            writer.append(queryTableCell, queryTableBodyRow);
            const placeholder = InsertPropertyCommand.createPropertyElement(
                key,
                selectedFields[key],
                contextDatatype,
                writer,
            );
            writer.append(queryTableCellParagraph, queryTableCell);
            writer.append(placeholder, queryTableCellParagraph);
        });

        return queryTableBodyRow;
    }

    private writeTableHead(writer: Writer, queryTable: Element, selectedFields: Dict<DataModelProperty>): void {
        const queryTableHead = writer.createElement('queryTableHead');
        writer.append(queryTableHead, queryTable);
        const queryTableHeadRow = writer.createElement('queryTableRow');
        writer.append(queryTableHeadRow, queryTableHead);

        Object.keys(selectedFields).forEach(key => {
            const queryTableHeadCell = writer.createElement('queryTableCell', {
                ...this.defaultCellAttributes,
                [COMMAND_CELL_BACKGROUND_QUERY_TABLE]: '#0000001a',
            });
            const queryTableHeadCellParagraph = writer.createElement('paragraph');
            writer.append(queryTableHeadCell, queryTableHeadRow);
            const label = writer.createText(selectedFields[key].label);
            writer.append(queryTableHeadCellParagraph, queryTableHeadCell);
            writer.append(label, queryTableHeadCellParagraph);
        });
    }

    private writeTableFooterRows(
        writer: Writer,
        selectedFields: Dict<DataModelProperty>,
        aggregations?: Dict<GroupAggregation>,
        contextDatatype?: NodeDetails['type'],
        queryTableBody?: Element,
    ): void {
        if (!queryTableBody || !contextDatatype || !aggregations) {
            return;
        }

        /**
         * A group footer is printed for every document level
         */
        const groups = uniq(Object.values(aggregations).map(a => a.group));
        // If there is only a single group and the user doesn't apply any aggregation method, then no group footer is printed.
        const hasAggregations = Object.values(aggregations).find(o => o.operation !== 'NONE');

        if (groups.length === 1 && !hasAggregations) {
            return;
        }

        // Move the footer group to the end
        const orderedGroups = reverse(groups);
        const footer = orderedGroups.shift()!;
        orderedGroups.push(footer);

        orderedGroups.forEach(group => {
            if (!selectedFields || !aggregations) {
                return;
            }

            const isLastGroup = group === Math.max(...groups);
            // If this is the top level aka last group, we want to print a global footer at the end of the collection
            const helperFunction = isLastGroup ? 'printBreakIfLast' : 'printBreakIfPropertyWillChange';

            const groupRow = writer.createElement('queryTableRow', {
                [ATTR_FOOTER_GROUP]: isLastGroup ? 'footer' : String(group),
            });
            writer.append(groupRow, queryTableBody);

            // An empty, hidden row is needed to anchor the closing comment to
            const tableGroupClosingHiddenRow = this.createHiddenRow(
                writer,
                queryTableBody,
                Object.keys(selectedFields).length,
            );

            // The footer section is processed every time the break property changes value between iterations
            const breakProperty = Object.keys(aggregations).filter(k => aggregations![k].group === group)[0];

            /**
             * The aggregation function arguments consists of the break property, then in pairs the aggregated property and the
             * applied aggregation method.
             *  */
            const aggregationFunctionArguments = Object.keys(aggregations)
                .filter(key => aggregations?.[key].operation && aggregations[key].operation !== 'NONE')
                .reduce(
                    (prevValue: string, key: string) => {
                        return `${prevValue} '${key}' '${aggregations?.[key].operation}'`;
                    },
                    // In case of the end of table footer, no property is needed.
                    isLastGroup ? '' : `'${breakProperty}'`,
                );

            this.htmlCommentPlugin.createHtmlComment(
                new Position(
                    tableGroupClosingHiddenRow.root as any,
                    tableGroupClosingHiddenRow.getPath(),
                    'toPrevious',
                ),
                `{{/${helperFunction}}}`,
            );

            this.htmlCommentPlugin.createHtmlComment(
                new Position(groupRow.root as any, groupRow.getPath(), 'toNext'),
                `{{#${helperFunction} ${aggregationFunctionArguments}}}`,
            );
            const fieldKeys = Object.keys(selectedFields);
            fieldKeys.forEach(field => {
                const queryTableGroupCell = writer.createElement('queryTableCell', this.defaultCellAttributes);
                writer.append(queryTableGroupCell, groupRow);
                const queryTableGroupCellParagraph = writer.createElement('paragraph');
                writer.append(queryTableGroupCellParagraph, queryTableGroupCell);

                const aggregationOperation = aggregations?.[field]?.operation;
                if (aggregationOperation && aggregationOperation !== 'NONE') {
                    const propertyDetails = cloneDeep(selectedFields?.[field]);
                    if (isEmpty(propertyDetails)) {
                        return;
                    }

                    if (aggregationOperation === Aggregations.distinctCount) {
                        // If the distinct count is used, the type is always an integer
                        propertyDetails.type = 'Int';
                    }

                    const placeholder = InsertPropertyCommand.createPropertyElement(
                        `${BLOCK_AGGREGATION_PROPERTY_NAME}.${field}.${aggregationOperation}`,
                        propertyDetails,
                        contextDatatype,
                        writer,
                    );
                    writer.append(placeholder, queryTableGroupCellParagraph);
                }
            });
        });
    }

    execute({ lookupResult, selectedItem, remapInfo }: InsertQueryTableCommandArgument): void {
        this.editor.model.change(writer => {
            if (!lookupResult.selectedFields) {
                return;
            }

            const selectedFieldKeys = Object.keys(lookupResult.selectedFields);

            const alias = generateQueryAlias();
            // TODO: Move these attributes to the repeating queryTableRowElement
            const queryTable = writer.createElement('queryTable', {
                [ATTR_CONTEXT_OBJECT_TYPE]: selectedItem.data.type,
                [ATTR_CONTEXT_OBJECT_PATH]: remapInfo.path,
                [ATTR_CONTEXT_FILTER]: JSON.stringify(lookupResult.filters || {}),
                [ATTR_CONTEXT_LIST_ORDER]: JSON.stringify(lookupResult.orderBy || {}),
                [ATTR_ALIAS]: alias,
            });

            this.editor.model.insertObject(queryTable);

            this.writeTableHead(writer, queryTable, lookupResult.selectedFields);

            const queryTableBody = writer.createElement('queryTableBody');
            writer.append(queryTableBody, queryTable);

            const queryTableBodyRow = this.writeTableBodyRepeatingRow(
                writer,
                lookupResult.selectedFields,
                selectedItem.data.type,
                queryTableBody,
            );

            this.writeTableFooterRows(
                writer,
                lookupResult.selectedFields,
                lookupResult.aggregations,
                selectedItem.data.type,
                queryTableBody,
            );

            /**
             * This hidden row is needed because we need some anchor element to add the handlebars comments before.
             * We can't anchor behind elements, only before them.
             *  */
            const tableHiddenRow = writer.createElement('queryTableRow', {
                [ATTR_QUERY_TABLE_HIDDEN_ROW]: 1,
            });
            writer.append(tableHiddenRow, queryTableBody);
            const tableHiddenCell = writer.createElement('queryTableCell', {
                colspan: selectedFieldKeys.length,
            });
            writer.append(tableHiddenCell, tableHiddenRow);
            const tableHiddenCellParagraph = writer.createElement('paragraph');
            writer.append(tableHiddenCellParagraph, tableHiddenCell);

            const queryTableFooter = writer.createElement('queryTableFooter');
            writer.append(queryTableFooter, queryTable);
            const queryTableFooterRow = writer.createElement('queryTableRow');
            writer.append(queryTableFooterRow, queryTableFooter);

            Object.keys(lookupResult.selectedFields).forEach(() => {
                const queryTableFooterCell = writer.createElement('queryTableCell');
                const queryTableFooterCellParagraph = writer.createElement('paragraph');
                writer.append(queryTableFooterCell, queryTableFooterRow);
                writer.append(queryTableFooterCellParagraph, queryTableFooterCell);
            });
            const tableForEachClosingHiddenRow = this.createHiddenRow(writer, queryTableBody, selectedFieldKeys.length);

            const remapInfoWithAlias = remapInfo.path.split('.');
            remapInfoWithAlias[0] = alias;

            if (remapInfo.subPath) {
                // If a subpath if provided, we need to add a wrapping {{#with}} tag as well
                this.htmlCommentPlugin.createHtmlComment(
                    new Position(
                        tableForEachClosingHiddenRow.root as any,
                        tableForEachClosingHiddenRow.getPath(),
                        'toPrevious',
                    ),
                    '{{/with}}{{/each}}',
                );
                this.htmlCommentPlugin.createHtmlComment(
                    new Position(queryTableBodyRow.root as any, queryTableBodyRow.getPath(), 'toNext'),
                    `{{#each ${remapInfoWithAlias.join('.')}}}{{#with ${remapInfo.subPath}}}`,
                );
            } else {
                this.htmlCommentPlugin.createHtmlComment(
                    new Position(
                        tableForEachClosingHiddenRow.root as any,
                        tableForEachClosingHiddenRow.getPath(),
                        'toPrevious',
                    ),
                    '{{/each}}',
                );
                this.htmlCommentPlugin.createHtmlComment(
                    new Position(queryTableBodyRow.root as any, queryTableBodyRow.getPath(), 'toNext'),
                    `{{#each ${remapInfoWithAlias.join('.')}}}`,
                );
            }
        });
    }

    refresh(): void {
        const model = this.editor.model;
        const selection = model.document.selection;
        const allowedIn = model.schema.findAllowedParent(selection.getFirstPosition()!, 'queryTable');
        this.isEnabled = allowedIn !== null;
    }
}
