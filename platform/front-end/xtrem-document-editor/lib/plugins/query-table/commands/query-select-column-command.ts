import { Command, type Editor } from '@ckeditor/ckeditor5-core';
import type { Range } from '@ckeditor/ckeditor5-engine';
import QueryTableUtils from '../utils/query-table-utils';
import QueryTableWalker from '../utils/query-table-walker';

/**
 * The select column command.
 *
 * The command is registered by {@link module:table/tableediting~TableEditing} as the `'selectTableColumn'` editor command.
 *
 * To select the columns containing the selected cells, execute the command:
 *
 * ```ts
 * editor.execute( 'selectTableColumn' );
 * ```
 */
export default class QueryTableSelectColumnCommand extends Command {
    /**
     * @inheritDoc
     */
    constructor(editor: Editor) {
        super(editor);

        // It does not affect data so should be enabled in read-only mode.
        this.affectsData = false;
    }

    /**
     * @inheritDoc
     */
    public override refresh(): void {
        const tableUtils: QueryTableUtils = this.editor.plugins.get(QueryTableUtils);
        const selectedCells = tableUtils.getSelectionAffectedTableCells(this.editor.model.document.selection);

        this.isEnabled = selectedCells.length > 0;
    }

    /**
     * @inheritDoc
     */
    public override execute(): void {
        const tableUtils: QueryTableUtils = this.editor.plugins.get(QueryTableUtils);
        const model = this.editor.model;
        const referenceCells = tableUtils.getSelectionAffectedTableCells(model.document.selection);
        const firstCell = referenceCells[0];
        const lastCell = referenceCells.pop()!;
        const table = firstCell.findAncestor('table')!;

        const startLocation = tableUtils.getCellLocation(firstCell);
        const endLocation = tableUtils.getCellLocation(lastCell);

        const startColumn = Math.min(startLocation.column, endLocation.column);
        const endColumn = Math.max(startLocation.column, endLocation.column);

        const rangesToSelect: Array<Range> = [];

        for (const cellInfo of new QueryTableWalker(table, { startColumn, endColumn })) {
            rangesToSelect.push(model.createRangeOn(cellInfo.cell));
        }

        model.change(writer => {
            writer.setSelection(rangesToSelect);
        });
    }
}
