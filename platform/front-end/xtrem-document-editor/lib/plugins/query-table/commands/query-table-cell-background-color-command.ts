import type { Editor } from '@ckeditor/ckeditor5-core';
import { COMMAND_CELL_BACKGROUND_QUERY_TABLE } from '../../../constants';
import QueryTableCellPropertyCommand from './query-table-cell-property-command';

/**
 * The table cell background color command.
 *
 * The command is registered by the {@link module:table/tablecellproperties/tablecellpropertiesediting~TableCellPropertiesEditing} as
 * the `'tableCellBackgroundColor'` editor command.
 *
 * To change the background color of selected cells, execute the command:
 *
 * ```ts
 * editor.execute( 'tableCellBackgroundColor', {
 *   value: '#f00'
 * } );
 * ```
 */
export default class QueryTableCellBackgroundColorCommand extends QueryTableCellPropertyCommand {
    /**
     * Creates a new `TableCellBackgroundColorCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value of the attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, COMMAND_CELL_BACKGROUND_QUERY_TABLE, defaultValue);
    }
}
