import type { Editor } from '@ckeditor/ckeditor5-core';
import { COMMAND_BACKGROUND_QUERY_TABLE } from '../../../constants';
import QueryTablePropertyCommand from './query-table-property-command';

/**
 * The table background color command.
 *
 * The command is registered by the {@link module:table/tableproperties/tablepropertiesediting~TablePropertiesEditing} as
 * the `'tableBackgroundColor'` editor command.
 *
 * To change the background color of the selected table, execute the command:
 *
 * ```ts
 * editor.execute( 'tableBackgroundColor', {
 *   value: '#f00'
 * } );
 * ```
 */
export default class QueryTableBackgroundColorCommand extends QueryTablePropertyCommand {
    /**
     * Creates a new `TableBackgroundColorCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value of the attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, COMMAND_BACKGROUND_QUERY_TABLE, defaultValue);
    }
}
