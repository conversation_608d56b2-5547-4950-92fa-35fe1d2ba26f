import type { Batch, Element } from '@ckeditor/ckeditor5-engine';
import { Command, type Editor } from '@ckeditor/ckeditor5-core';

export interface TableRowPropertyCommandExecuteOptions {
    value?: string;
    batch?: Batch;
}

/**
 * The table row attribute command.
 *
 * This command is a base command for other table property commands.
 */
export default class QueryTableRowPropertyCommand extends Command {
    /**
     * The attribute that will be set by the command.
     */
    public readonly attributeName: string;

    /**
     * The default value for the attribute.
     */
    protected readonly _defaultValue: string | undefined;

    /**
     * Creates a new `TableRowPropertyCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param attributeName Table cell attribute name.
     * @param defaultValue The default value of the attribute.
     */
    constructor(editor: Editor, attributeName: string, defaultValue?: string) {
        super(editor);

        this.attributeName = attributeName;
        this._defaultValue = defaultValue;
    }

    /**
     * @inheritDoc
     */
    public override refresh(): void {
        const editor = this.editor;
        const selection = editor.model.document.selection;

        const queryTableRow = selection.getFirstPosition()?.findAncestor('queryTableRow');

        this.isEnabled = !!queryTableRow;
        this.value = queryTableRow ? this._getValue(queryTableRow) : undefined;
    }

    /**
     * Executes the command.
     *
     * @fires execute
     * @param options.value If set, the command will set the attribute on the selected table.
     * If not set, the command will remove the attribute from the selected table.
     * @param options.batch Pass the model batch instance to the command to aggregate changes,
     * for example, to allow a single undo step for multiple executions.
     */
    public override execute(options: TableRowPropertyCommandExecuteOptions = {}): void {
        const model = this.editor.model;
        const selection = model.document.selection;

        const { value, batch } = options;

        const queryTableRow = selection.getFirstPosition()?.findAncestor('queryTableRow');
        if (!queryTableRow) {
            return;
        }
        const valueToSet = this._getValueToSet(value);

        model.enqueueChange(batch, writer => {
            if (valueToSet) {
                writer.setAttribute(this.attributeName, valueToSet, queryTableRow);
            } else {
                writer.removeAttribute(this.attributeName, queryTableRow);
            }
        });
    }

    /**
     * Returns the attribute value for a table.
     */
    protected _getValue(tableRow: Element): unknown {
        if (!tableRow) {
            return undefined;
        }

        return tableRow.getAttribute(this.attributeName);
    }

    /**
     * Returns the proper model value. It can be used to add a default unit to numeric values.
     */
    protected _getValueToSet(value: string | number | undefined): string | number | undefined {
        return value;
    }
}
