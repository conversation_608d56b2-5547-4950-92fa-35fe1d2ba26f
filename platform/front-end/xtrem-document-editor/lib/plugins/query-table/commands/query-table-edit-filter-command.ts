import { Command } from '@ckeditor/ckeditor5-core';
import { getContextFilter } from '../../utils';
import type { FilterProperty } from '@sage/xtrem-shared';
import { ATTR_CONTEXT_FILTER } from '@sage/xtrem-shared';

export default class QueryTableEditFilterCommand extends Command {
    execute({ filters }: { filters: Array<FilterProperty> }): void {
        const editor = this.editor;
        const model = this.editor.model;

        editor.model.change(writer => {
            const selection = model.document.selection;
            const queryTableElement =
                selection.getFirstPosition()?.findAncestor('queryTable') ||
                selection.getFirstPosition()?.findAncestor('recordContext');
            if (queryTableElement) {
                writer.setAttribute(ATTR_CONTEXT_FILTER, JSON.stringify(filters), queryTableElement);
            }
        });
    }

    refresh(): void {
        this.isEnabled = getContextFilter(this.editor.model.document.selection.focus?.parent) !== null;
    }
}
