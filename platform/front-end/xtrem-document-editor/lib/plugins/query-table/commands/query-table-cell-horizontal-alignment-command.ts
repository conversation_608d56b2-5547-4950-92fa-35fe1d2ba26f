import type { Editor } from '@ckeditor/ckeditor5-core';
import { COMMAND_CELL_HORIZONTAL_ALIGNMENT_QUERY_TABLE } from '../../../constants';
import QueryTableCellPropertyCommand from './query-table-cell-property-command';

/**
 * The table cell horizontal alignment command.
 *
 * The command is registered by the {@link module:table/tablecellproperties/tablecellpropertiesediting~TableCellPropertiesEditing} as
 * the `'tableCellHorizontalAlignment'` editor command.
 *
 * To change the horizontal text alignment of selected cells, execute the command:
 *
 * ```ts
 * editor.execute( 'tableCellHorizontalAlignment', {
 *  value: 'right'
 * } );
 * ```
 */
export default class QueryTableCellHorizontalAlignmentCommand extends QueryTableCellPropertyCommand {
    /**
     * Creates a new `TableCellHorizontalAlignmentCommand` instance.
     *
     * @param editor An editor in which this command will be used.
     * @param defaultValue The default value for the "alignment" attribute.
     */
    constructor(editor: Editor, defaultValue: string) {
        super(editor, COMMAND_CELL_HORIZONTAL_ALIGNMENT_QUERY_TABLE, defaultValue);
    }
}
