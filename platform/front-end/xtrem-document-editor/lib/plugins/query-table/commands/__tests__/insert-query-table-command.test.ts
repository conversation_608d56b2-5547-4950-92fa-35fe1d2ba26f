import { executeEditorCommand, getValue, waitForSelectorToAppear } from '../../../../__tests__/test-helper';
import { COMMAND_INSERT_QUERY_TABLE } from '../../../../constants';

describe('insert query table command', () => {
    it('should insert basic query table', async () => {
        await executeEditorCommand(COMMAND_INSERT_QUERY_TABLE, {
            lookupResult: {
                filters: [],
                orderBy: [],
                selectedMode: 'list',
                selectedFields: {
                    orderDate: { name: 'orderDate', label: 'Order Date', kind: 'SCALAR', type: 'Date' },
                    totalWithoutTax: { name: 'totalWithoutTax', label: 'Net Total', kind: 'SCALAR', type: 'Float' },
                    invoiceNumber: { name: 'invoiceNumber', label: 'Invoice Number', kind: 'SCALAR', type: 'String' },
                    totalWithTax: { name: 'totalWithTax', label: 'Gross Total', kind: 'SCALAR', type: 'Float' },
                },
            },
            selectedItem: {
                label: 'Sales Invoice',
                data: {
                    name: 'salesInvoice',
                    label: 'Sales Invoice',
                    kind: 'LIST',
                    type: 'SalesInvoice',
                    namespace: 'xtremSales',
                    iconType: 'csv',
                },
                id: 'salesInvoice',
                key: 'salesInvoice',
                labelKey: 'Sales Invoice',
                labelPath: 'Sales Invoice',
            },
            remapInfo: { path: 'xtremSales.salesInvoice.query.edges' },
        });

        await waitForSelectorToAppear('.query-table');

        expect(getValue()).resolves.toMatchSnapshot();
    });

    it('should insert basic query table with subpath', async () => {
        await executeEditorCommand(COMMAND_INSERT_QUERY_TABLE, {
            lookupResult: {
                filters: [],
                orderBy: [],
                selectedMode: 'list',
                selectedFields: {
                    orderDate: { name: 'orderDate', label: 'Order Date', kind: 'SCALAR', type: 'Date' },
                    totalWithoutTax: { name: 'totalWithoutTax', label: 'Net Total', kind: 'SCALAR', type: 'Float' },
                    invoiceNumber: { name: 'invoiceNumber', label: 'Invoice Number', kind: 'SCALAR', type: 'String' },
                    totalWithTax: { name: 'totalWithTax', label: 'Gross Total', kind: 'SCALAR', type: 'Float' },
                },
            },
            selectedItem: {
                label: 'Sales Invoice',
                data: {
                    name: 'salesInvoice',
                    label: 'Sales Invoice',
                    kind: 'LIST',
                    type: 'SalesInvoice',
                    namespace: 'xtremSales',
                    iconType: 'csv',
                },
                id: 'salesInvoice',
                key: 'salesInvoice',
                labelKey: 'Sales Invoice',
                labelPath: 'Sales Invoice',
            },
            remapInfo: { path: 'xtremSales.salesInvoice.query.edges', subPath: 'node' },
        });

        await waitForSelectorToAppear('.query-table');

        expect(getValue()).resolves.toMatchSnapshot();
    });
});
