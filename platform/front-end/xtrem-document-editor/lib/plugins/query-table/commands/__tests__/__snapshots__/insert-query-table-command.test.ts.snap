// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`insert query table command should insert basic query table 1`] = `
"<div>
  <table class=\\"query-table\\" data-context-object-type=\\"SalesInvoice\\" data-context-object-path=\\"xtremSales.salesInvoice.query.edges\\" data-context-filter=\\"[]\\" data-context-list-order=\\"[]\\" data-alias=\\"TEST_ALIAS\\">
    <thead class=\\"query-table-head\\">
      <tr class=\\"query-table-row\\">
        <td class=\\"query-table-cell\\" style=\\"background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            Order Date
          </p>
        </td>
        <td class=\\"query-table-cell\\" style=\\"background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            Net Total
          </p>
        </td>
        <td class=\\"query-table-cell\\" style=\\"background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            Invoice Number
          </p>
        </td>
        <td class=\\"query-table-cell\\" style=\\"background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            Gross Total
          </p>
        </td>
      </tr>
    </thead>
    <tbody class=\\"query-table-body\\">
      <!--{{#each TEST_ALIAS.salesInvoice.query.edges}}-->
      <tr class=\\"query-table-row\\">
        <td class=\\"query-table-cell\\" style=\\"border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            <span class=\\"property\\" data-property-display-label=\\"Order Date\\" data-property-data-type=\\"Date\\" data-property-name=\\"orderDate\\" data-property-data-format=\\"FullDate\\" data-property-parent-context=\\"SalesInvoice\\">
              {{formatDate orderDate 'FullDate'}}
            </span>
          </p>
        </td>
        <td class=\\"query-table-cell e-right-align\\" style=\\"border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            <span class=\\"property\\" data-property-display-label=\\"Net Total\\" data-property-data-type=\\"Float\\" data-property-name=\\"totalWithoutTax\\" data-property-data-format=\\"2\\" data-property-parent-context=\\"SalesInvoice\\">
              {{formatNumber totalWithoutTax 2}}
            </span>
          </p>
        </td>
        <td class=\\"query-table-cell\\" style=\\"border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            <span class=\\"property\\" data-property-display-label=\\"Invoice Number\\" data-property-data-type=\\"String\\" data-property-name=\\"invoiceNumber\\" data-property-data-format=\\"\\" data-property-parent-context=\\"SalesInvoice\\">
              {{invoiceNumber}}
            </span>
          </p>
        </td>
        <td class=\\"query-table-cell e-right-align\\" style=\\"border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            <span class=\\"property\\" data-property-display-label=\\"Gross Total\\" data-property-data-type=\\"Float\\" data-property-name=\\"totalWithTax\\" data-property-data-format=\\"2\\" data-property-parent-context=\\"SalesInvoice\\">
              {{formatNumber totalWithTax 2}}
            </span>
          </p>
        </td>
      </tr>
      <tr class=\\"query-table-row\\" data-hidden=\\"1\\">
        <td class=\\"query-table-cell\\" colspan=\\"4\\">
          <p>
            &nbsp;
          </p>
        </td>
      </tr>
      <!--{{/each}}-->
      <tr class=\\"query-table-row\\" data-hidden=\\"1\\">
        <td class=\\"query-table-cell\\" colspan=\\"4\\">
          <p>
            &nbsp;
          </p>
        </td>
      </tr>
    </tbody>
    <tfoot class=\\"query-table-footer\\">
      <tr class=\\"query-table-row\\">
        <td class=\\"query-table-cell\\">
          <p>
            &nbsp;
          </p>
        </td>
        <td class=\\"query-table-cell\\">
          <p>
            &nbsp;
          </p>
        </td>
        <td class=\\"query-table-cell\\">
          <p>
            &nbsp;
          </p>
        </td>
        <td class=\\"query-table-cell\\">
          <p>
            &nbsp;
          </p>
        </td>
      </tr>
    </tfoot>
  </table>
</div>"
`;

exports[`insert query table command should insert basic query table with subpath 1`] = `
"<div>
  <table class=\\"query-table\\" data-context-object-type=\\"SalesInvoice\\" data-context-object-path=\\"xtremSales.salesInvoice.query.edges\\" data-context-filter=\\"[]\\" data-context-list-order=\\"[]\\" data-alias=\\"TEST_ALIAS\\">
    <thead class=\\"query-table-head\\">
      <tr class=\\"query-table-row\\">
        <td class=\\"query-table-cell\\" style=\\"background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            Order Date
          </p>
        </td>
        <td class=\\"query-table-cell\\" style=\\"background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            Net Total
          </p>
        </td>
        <td class=\\"query-table-cell\\" style=\\"background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            Invoice Number
          </p>
        </td>
        <td class=\\"query-table-cell\\" style=\\"background-color:#0000001a;border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            Gross Total
          </p>
        </td>
      </tr>
    </thead>
    <tbody class=\\"query-table-body\\">
      <!--{{#each TEST_ALIAS.salesInvoice.query.edges}}{{#with node}}-->
      <tr class=\\"query-table-row\\">
        <td class=\\"query-table-cell\\" style=\\"border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            <span class=\\"property\\" data-property-display-label=\\"Order Date\\" data-property-data-type=\\"Date\\" data-property-name=\\"orderDate\\" data-property-data-format=\\"FullDate\\" data-property-parent-context=\\"SalesInvoice\\">
              {{formatDate orderDate 'FullDate'}}
            </span>
          </p>
        </td>
        <td class=\\"query-table-cell e-right-align\\" style=\\"border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            <span class=\\"property\\" data-property-display-label=\\"Net Total\\" data-property-data-type=\\"Float\\" data-property-name=\\"totalWithoutTax\\" data-property-data-format=\\"2\\" data-property-parent-context=\\"SalesInvoice\\">
              {{formatNumber totalWithoutTax 2}}
            </span>
          </p>
        </td>
        <td class=\\"query-table-cell\\" style=\\"border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            <span class=\\"property\\" data-property-display-label=\\"Invoice Number\\" data-property-data-type=\\"String\\" data-property-name=\\"invoiceNumber\\" data-property-data-format=\\"\\" data-property-parent-context=\\"SalesInvoice\\">
              {{invoiceNumber}}
            </span>
          </p>
        </td>
        <td class=\\"query-table-cell e-right-align\\" style=\\"border:1px solid #000000;padding:2px;vertical-align:top;\\">
          <p>
            <span class=\\"property\\" data-property-display-label=\\"Gross Total\\" data-property-data-type=\\"Float\\" data-property-name=\\"totalWithTax\\" data-property-data-format=\\"2\\" data-property-parent-context=\\"SalesInvoice\\">
              {{formatNumber totalWithTax 2}}
            </span>
          </p>
        </td>
      </tr>
      <tr class=\\"query-table-row\\" data-hidden=\\"1\\">
        <td class=\\"query-table-cell\\" colspan=\\"4\\">
          <p>
            &nbsp;
          </p>
        </td>
      </tr>
      <!--{{/with}}{{/each}}-->
      <tr class=\\"query-table-row\\" data-hidden=\\"1\\">
        <td class=\\"query-table-cell\\" colspan=\\"4\\">
          <p>
            &nbsp;
          </p>
        </td>
      </tr>
    </tbody>
    <tfoot class=\\"query-table-footer\\">
      <tr class=\\"query-table-row\\">
        <td class=\\"query-table-cell\\">
          <p>
            &nbsp;
          </p>
        </td>
        <td class=\\"query-table-cell\\">
          <p>
            &nbsp;
          </p>
        </td>
        <td class=\\"query-table-cell\\">
          <p>
            &nbsp;
          </p>
        </td>
        <td class=\\"query-table-cell\\">
          <p>
            &nbsp;
          </p>
        </td>
      </tr>
    </tfoot>
  </table>
</div>"
`;
