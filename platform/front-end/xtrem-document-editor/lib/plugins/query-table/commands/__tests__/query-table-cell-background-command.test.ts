import {
    executeEditorCommand,
    getPage,
    getStyleProperty,
    insertSimpleQueryTable,
} from '../../../../__tests__/test-helper';
import { COMMAND_CELL_BACKGROUND_QUERY_TABLE } from '../../../../constants';

describe('query table cell background command', () => {
    it('should update the cell background color property', async () => {
        const page = getPage();
        await insertSimpleQueryTable();
        const headerCells = await page.$$('thead td.query-table-cell');
        await expect(getStyleProperty(headerCells[0], 'background-color')).resolves.toEqual('rgba(0, 0, 0, 0.1)');

        await headerCells[0].click();

        await executeEditorCommand(COMMAND_CELL_BACKGROUND_QUERY_TABLE, { value: '#123456' });

        // Only the first cell's background should change
        await expect(getStyleProperty(headerCells[0], 'background-color')).resolves.toEqual('rgb(18, 52, 86)');
        await expect(getStyleProperty(headerCells[1], 'background-color')).resolves.toEqual('rgba(0, 0, 0, 0.1)');
    });
});
