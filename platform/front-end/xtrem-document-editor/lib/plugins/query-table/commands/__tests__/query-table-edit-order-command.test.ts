import {
    executeEditorCommand,
    getPage,
    getValue,
    insertSimpleQueryTable,
    isEditorCommandEnabled,
    setValue,
} from '../../../../__tests__/test-helper';
import { COMMAND_EDIT_ORDER_QUERY_TABLE } from '../../../../constants';

describe('query table edit order', () => {
    it('should add modify the orderBy attribute on the table element', async () => {
        const page = getPage();
        await insertSimpleQueryTable();

        // Focus a cell in the table
        await page.click('td.query-table-cell');

        await executeEditorCommand(COMMAND_EDIT_ORDER_QUERY_TABLE, {
            orderBy: { name: 'ascending' },
        });

        await expect(getValue()).resolves.toContain(
            'data-context-list-order="{&quot;name&quot;:&quot;ascending&quot;}"',
        );

        await executeEditorCommand(COMMAND_EDIT_ORDER_QUERY_TABLE, {
            orderBy: { name: 'ascending', description: 'descending' },
        });

        await expect(getValue()).resolves.toContain(
            'data-context-list-order="{&quot;name&quot;:&quot;ascending&quot;,&quot;description&quot;:&quot;descending&quot;}"',
        );
    });

    it('should be enabled if the focus is within a table', async () => {
        const page = getPage();

        await insertSimpleQueryTable();

        await expect(isEditorCommandEnabled(COMMAND_EDIT_ORDER_QUERY_TABLE)).resolves.toEqual(false);

        await page.click('td.query-table-cell');

        await expect(isEditorCommandEnabled(COMMAND_EDIT_ORDER_QUERY_TABLE)).resolves.toEqual(true);

        await setValue('<div>EMPTY</div>');

        await expect(isEditorCommandEnabled(COMMAND_EDIT_ORDER_QUERY_TABLE)).resolves.toEqual(false);
    });
});
