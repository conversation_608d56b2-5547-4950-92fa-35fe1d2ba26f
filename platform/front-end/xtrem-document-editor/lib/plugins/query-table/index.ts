import QueryTableEditing from './query-table-editing';
import QueryTableUI from './query-table-ui';
import { Plugin } from '@ckeditor/ckeditor5-core';
import QueryTableUtils from './utils/query-table-utils';
import QueryTableSelection from './query-table-selection';
import QueryTableMouse from './query-table-mouse';
import { Widget } from '@ckeditor/ckeditor5-widget';

export default class QueryTablePlugin extends Plugin {
    static get requires(): Array<typeof Plugin> {
        return [QueryTableEditing, QueryTableUI, QueryTableUtils, QueryTableMouse, QueryTableSelection, Widget];
    }
}
