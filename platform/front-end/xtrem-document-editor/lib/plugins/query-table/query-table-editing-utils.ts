import type { Editor } from '@ckeditor/ckeditor5-core';
import type { Conversion, Schema, ViewElement, BoxSides, DowncastConversionApi } from '@ckeditor/ckeditor5-engine';
import {
    COMMAND_ALIGNMENT_QUERY_TABLE,
    COMMAND_BACKGROUND_QUERY_TABLE,
    COMMAND_BORDER_COLOR_QUERY_TABLE,
    COMMAND_BORDER_STYLE_QUERY_TABLE,
    COMMAND_BORDER_WIDTH_QUERY_TABLE,
    COMMAND_CELL_BORDER_COLOR_QUERY_TABLE,
    COMMAND_CELL_BORDER_STYLE_QUERY_TABLE,
    COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE,
    COMMAND_INSERT_PAGE_BREAK_QUERY_TABLE,
    VALIGN_VALUES_REG_EXP,
} from '../../constants';
import { isObject } from 'lodash';

const ALIGN_VALUES_REG_EXP = /^(left|center|right)$/;
const FLOAT_VALUES_REG_EXP = /^(left|none|right)$/;

/**
 * Conversion helper for upcasting attributes using normalized styles.
 *
 * @param options.modelAttribute The attribute to set.
 * @param options.styleName The style name to convert.
 * @param options.viewElement The view element name that should be converted.
 * @param options.defaultValue The default value for the specified `modelAttribute`.
 * @param options.shouldUpcast The function which returns `true` if style should be upcasted from this element.
 */
export function upcastStyleToAttribute(
    conversion: Conversion,
    options: {
        modelAttribute: string;
        styleName: string;
        viewElement: string | RegExp;
        defaultValue: string;
        reduceBoxSides?: boolean;
        shouldUpcast?: (viewElement: ViewElement) => boolean;
    },
): void {
    const {
        modelAttribute,
        styleName,
        viewElement,
        defaultValue,
        reduceBoxSides = false,
        shouldUpcast = (): boolean => true,
    } = options;

    conversion.for('upcast').attributeToAttribute({
        view: {
            name: viewElement,
            styles: {
                [styleName]: /[\s\S]+/,
            },
        },
        model: {
            key: modelAttribute,
            value: (viewElement: ViewElement): string | Record<Side, string> | undefined => {
                if (!shouldUpcast(viewElement)) {
                    return undefined;
                }

                const normalized = viewElement.getNormalizedStyle(styleName) as Record<Side, string>;
                const value = reduceBoxSides ? reduceBoxSidesValue(normalized) : normalized;

                if (defaultValue !== value) {
                    return value;
                }
                return undefined;
            },
        },
    });
}

export interface StyleValues {
    color: string;
    style: string;
    width: string;
}

/**
 * Conversion helper for upcasting border styles for view elements.
 *
 * @param defaultBorder The default border values.
 * @param defaultBorder.color The default `borderColor` value.
 * @param defaultBorder.style The default `borderStyle` value.
 * @param defaultBorder.width The default `borderWidth` value.
 */
export function upcastBorderStyles(
    conversion: Conversion,
    viewElementName: string,
    modelAttributes: StyleValues,
): void {
    conversion.for('upcast').add(dispatcher =>
        dispatcher.on(`element:${viewElementName}`, (evt, data, conversionApi) => {
            // If the element was not converted by element-to-element converter,
            // we should not try to convert the style. See #8393.
            if (!data.modelRange) {
                return;
            }

            // Check the most detailed properties. These will be always set directly or
            // when using the "group" properties like: `border-(top|right|bottom|left)` or `border`.
            const stylesToConsume = [
                'border-top-width',
                'border-top-color',
                'border-top-style',
                'border-bottom-width',
                'border-bottom-color',
                'border-bottom-style',
                'border-right-width',
                'border-right-color',
                'border-right-style',
                'border-left-width',
                'border-left-color',
                'border-left-style',
            ].filter(styleName => data.viewItem.hasStyle(styleName));

            if (!stylesToConsume.length) {
                return;
            }

            const matcherPattern = {
                styles: stylesToConsume,
            };

            // Try to consume appropriate values from consumable values list.
            if (!conversionApi.consumable.test(data.viewItem, matcherPattern)) {
                return;
            }

            const modelElement = [...data.modelRange.getItems({ shallow: true })].pop();

            conversionApi.consumable.consume(data.viewItem, matcherPattern);

            const normalizedBorder = {
                style: data.viewItem.getNormalizedStyle('border-style'),
                color: data.viewItem.getNormalizedStyle('border-color'),
                width: data.viewItem.getNormalizedStyle('border-width'),
            };

            const reducedBorder = {
                style: reduceBoxSidesValue(normalizedBorder.style),
                color: reduceBoxSidesValue(normalizedBorder.color),
                width: reduceBoxSidesValue(normalizedBorder.width),
            };

            if (reducedBorder.style) {
                conversionApi.writer.setAttribute(modelAttributes.style, reducedBorder.style, modelElement);
            }

            if (reducedBorder.color) {
                conversionApi.writer.setAttribute(modelAttributes.color, reducedBorder.color, modelElement);
            }

            if (reducedBorder.width) {
                conversionApi.writer.setAttribute(modelAttributes.width, reducedBorder.width, modelElement);
            }
        }),
    );
}

/**
 * Conversion helper for downcasting an attribute to a style.
 */
export function downcastAttributeToStyle(
    conversion: Conversion,
    options: {
        modelElement: string;
        modelAttribute: string;
        styleName: string;
    },
): void {
    const { modelElement, modelAttribute, styleName } = options;

    conversion.for('downcast').attributeToAttribute({
        model: {
            name: modelElement,
            key: modelAttribute,
        },
        view: modelAttributeValue => ({
            key: 'style',
            value: {
                [styleName]: modelAttributeValue,
            },
        }),
    });
}

/**
 * Conversion helper for downcasting attributes from the model table to a view table (not to `<figure>`).
 */
export function downcastTableAttribute(
    conversion: Conversion,
    options: {
        modelAttribute: string;
        styleName: string;
    },
): void {
    const { modelAttribute, styleName } = options;

    conversion.for('downcast').add(dispatcher =>
        dispatcher.on(`attribute:${modelAttribute}:queryTable`, (evt, data, conversionApi: DowncastConversionApi) => {
            const { item, attributeNewValue } = data;
            const { mapper, writer } = conversionApi;

            if (!conversionApi.consumable.consume(data.item, evt.name)) {
                return;
            }

            const currentViewElement = mapper.toViewElement(item);

            if (!currentViewElement) {
                return;
            }

            const table = currentViewElement.is('element', 'table')
                ? currentViewElement
                : ([...currentViewElement.getChildren()].find(child => child.is('element', 'table')) as ViewElement);

            if (attributeNewValue) {
                writer.setStyle(styleName, attributeNewValue, table);
            } else {
                writer.removeStyle(styleName, table);
            }
        }),
    );
}

type Side = 'top' | 'right' | 'bottom' | 'left';
type Style = Record<Side, string>;

/**
 * Reduces the full top, right, bottom, left object to a single string if all sides are equal.
 * Returns original style otherwise.
 */
function reduceBoxSidesValue(style?: Style): undefined | string | Style {
    if (!style) {
        return undefined;
    }
    const sides: Array<Side> = ['top', 'right', 'bottom', 'left'];
    const allSidesDefined = sides.every(side => style[side]);

    if (!allSidesDefined) {
        return style;
    }

    const topSideStyle = style.top;
    const allSidesEqual = sides.every(side => style[side] === topSideStyle);

    if (!allSidesEqual) {
        return style;
    }

    return topSideStyle;
}

export function enableTableBorderProperties(schema: Schema, conversion: Conversion): void {
    const modelAttributes = {
        width: COMMAND_BORDER_WIDTH_QUERY_TABLE,
        color: COMMAND_BORDER_COLOR_QUERY_TABLE,
        style: COMMAND_BORDER_STYLE_QUERY_TABLE,
    };

    schema.extend('queryTable', {
        allowAttributes: Object.values(modelAttributes),
    });

    upcastBorderStyles(conversion, 'queryTable', modelAttributes);

    downcastTableAttribute(conversion, { modelAttribute: modelAttributes.color, styleName: 'border-color' });
    downcastTableAttribute(conversion, { modelAttribute: modelAttributes.style, styleName: 'border-style' });
    downcastTableAttribute(conversion, { modelAttribute: modelAttributes.width, styleName: 'border-width' });
}

/**
 * Enables the `'tableCellBorderStyle'`, `'tableCellBorderColor'` and `'tableCellBorderWidth'` attributes for table cells.
 *
 * @param defaultBorder The default border values.
 * @param defaultBorder.color The default `tableCellBorderColor` value.
 * @param defaultBorder.style The default `tableCellBorderStyle` value.
 * @param defaultBorder.width The default `tableCellBorderWidth` value.
 */
export function enableTableCellBorderProperties(schema: Schema, conversion: Conversion): void {
    const modelAttributes = {
        width: COMMAND_CELL_BORDER_WIDTH_QUERY_TABLE,
        color: COMMAND_CELL_BORDER_COLOR_QUERY_TABLE,
        style: COMMAND_CELL_BORDER_STYLE_QUERY_TABLE,
    };

    schema.extend('queryTableCell', {
        allowAttributes: Object.values(modelAttributes),
    });

    upcastBorderStyles(conversion, 'td', modelAttributes);
    upcastBorderStyles(conversion, 'th', modelAttributes);
    downcastAttributeToStyle(conversion, {
        modelElement: 'queryTableCell',
        modelAttribute: modelAttributes.style,
        styleName: 'border-style',
    });
    downcastAttributeToStyle(conversion, {
        modelElement: 'queryTableCell',
        modelAttribute: modelAttributes.color,
        styleName: 'border-color',
    });
    downcastAttributeToStyle(conversion, {
        modelElement: 'queryTableCell',
        modelAttribute: modelAttributes.width,
        styleName: 'border-width',
    });
}

/**
 * Enables the `'tableCellHorizontalAlignment'` attribute for table cells.
 *
 * @param defaultValue The default horizontal alignment value.
 */
export function enableHorizontalAlignmentProperty(schema: Schema, conversion: Conversion, defaultValue: string): void {
    schema.extend('queryTableCell', {
        allowAttributes: ['queryTableCellHorizontalAlignment'],
    });

    conversion.for('downcast').attributeToAttribute({
        model: {
            name: 'queryTableCell',
            key: 'queryTableCellHorizontalAlignment',
        },
        view: alignment => ({
            key: 'style',
            value: {
                'text-align': alignment,
            },
        }),
    });

    conversion
        .for('upcast')
        // Support for the `text-align:*;` CSS definition for the table cell alignment.
        .attributeToAttribute({
            view: {
                name: /^(td|th)$/,
                styles: {
                    'text-align': ALIGN_VALUES_REG_EXP,
                },
            },
            model: {
                key: 'queryTableCellHorizontalAlignment',
                value: (viewElement: ViewElement) => {
                    const align = viewElement.getStyle('text-align');

                    return align === defaultValue ? null : align;
                },
            },
        })
        // Support for the `align` attribute as the backward compatibility while pasting from other sources.
        .attributeToAttribute({
            view: {
                name: /^(td|th)$/,
                attributes: {
                    align: ALIGN_VALUES_REG_EXP,
                },
            },
            model: {
                key: 'queryTableCellHorizontalAlignment',
                value: (viewElement: ViewElement) => {
                    const align = viewElement.getAttribute('align');

                    return align === defaultValue ? null : align;
                },
            },
        });
}

/**
 * Enables the `'verticalAlignment'` attribute for table cells.
 *
 * @param defaultValue The default vertical alignment value.
 */
export function enableVerticalAlignmentProperty(schema: Schema, conversion: Conversion, defaultValue: string): void {
    schema.extend('queryTableCell', {
        allowAttributes: ['queryTableCellVerticalAlignment'],
    });

    conversion.for('downcast').attributeToAttribute({
        model: {
            name: 'queryTableCell',
            key: 'queryTableCellVerticalAlignment',
        },
        view: alignment => ({
            key: 'style',
            value: {
                'vertical-align': alignment,
            },
        }),
    });

    conversion
        .for('upcast')
        // Support for the `vertical-align:*;` CSS definition for the table cell alignment.
        .attributeToAttribute({
            view: {
                name: /^(td|th)$/,
                styles: {
                    'vertical-align': VALIGN_VALUES_REG_EXP,
                },
            },
            model: {
                key: 'queryTableCellVerticalAlignment',
                value: (viewElement: ViewElement) => {
                    const align = viewElement.getStyle('vertical-align');

                    return align === defaultValue ? null : align;
                },
            },
        })
        // Support for the `align` attribute as the backward compatibility while pasting from other sources.
        .attributeToAttribute({
            view: {
                name: /^(td|th)$/,
                attributes: {
                    valign: VALIGN_VALUES_REG_EXP,
                },
            },
            model: {
                key: 'queryTableCellVerticalAlignment',
                value: (viewElement: ViewElement) => {
                    const valign = viewElement.getAttribute('valign');

                    return valign === defaultValue ? null : valign;
                },
            },
        });
}

/**
 * Enables conversion for an attribute for simple view-model mappings.
 *
 * @param options.defaultValue The default value for the specified `modelAttribute`.
 */
export function enableTableProperty(
    schema: Schema,
    conversion: Conversion,
    options: {
        modelAttribute: string;
        styleName: string;
        defaultValue: string;
    },
): void {
    const { modelAttribute } = options;

    schema.extend('queryTable', {
        allowAttributes: [modelAttribute],
    });
    upcastStyleToAttribute(conversion, { viewElement: 'queryTable', ...options });
    downcastTableAttribute(conversion, options);
}

/**
 * Enables conversion for an attribute for simple view-model mappings.
 *
 * @param options.defaultValue The default value for the specified `modelAttribute`.
 */
export function enableCellProperty(
    schema: Schema,
    conversion: Conversion,
    options: {
        modelAttribute: string;
        styleName: string;
        defaultValue: string;
        reduceBoxSides?: boolean;
    },
): void {
    const { modelAttribute } = options;

    schema.extend('queryTableCell', {
        allowAttributes: [modelAttribute],
    });

    upcastStyleToAttribute(conversion, { viewElement: /^(td|th)$/, ...options });
    downcastAttributeToStyle(conversion, { modelElement: 'queryTableCell', ...options });
}

/**
 * Returns a string if all four values of box sides are equal.
 *
 * If a string is passed, it is treated as a single value (pass-through).
 *
 * ```ts
 * // Returns 'foo':
 * getSingleValue( { top: 'foo', right: 'foo', bottom: 'foo', left: 'foo' } );
 * getSingleValue( 'foo' );
 *
 * // Returns undefined:
 * getSingleValue( { top: 'foo', right: 'foo', bottom: 'bar', left: 'foo' } );
 * getSingleValue( { top: 'foo', right: 'foo' } );
 * ```
 */
export function getSingleValue(objectOrString: BoxSides | string | undefined): string | undefined {
    if (!objectOrString || !isObject(objectOrString)) {
        return objectOrString;
    }

    const { top, right, bottom, left } = objectOrString;

    if (top === right && right === bottom && bottom === left) {
        return top!;
    }

    return undefined;
}

/**
 * Adds a unit to a value if the value is a number or a string representing a number.
 *
 * **Note**: It does nothing to non-numeric values.
 *
 * ```ts
 * getSingleValue( 25, 'px' ); // '25px'
 * getSingleValue( 25, 'em' ); // '25em'
 * getSingleValue( '25em', 'px' ); // '25em'
 * getSingleValue( 'foo', 'px' ); // 'foo'
 * ```
 *
 * @param defaultUnit A default unit added to a numeric value.
 */
export function addDefaultUnitToNumericValue(
    value: string | number | undefined,
    defaultUnit: string,
): string | number | undefined {
    const numericValue = parseFloat(value as any);

    if (Number.isNaN(numericValue)) {
        return value;
    }

    if (String(numericValue) !== String(value)) {
        return value;
    }

    return `${numericValue}${defaultUnit}`;
}

export interface NormalizedDefaultProperties {
    borderStyle: string;
    borderWidth: string;
    borderColor: string;
    backgroundColor: string;
    width: string;
    height: string;
    alignment?: string;
    padding?: string;
    verticalAlignment?: string;
    horizontalAlignment?: string;
}

/**
 * Returns the normalized configuration.
 *
 * @param options.includeAlignmentProperty Whether the "alignment" property should be added.
 * @param options.includePaddingProperty Whether the "padding" property should be added.
 * @param options.includeVerticalAlignmentProperty Whether the "verticalAlignment" property should be added.
 * @param options.includeHorizontalAlignmentProperty Whether the "horizontalAlignment" property should be added.
 * @param options.isRightToLeftContent Whether the content is right-to-left.
 */
export function getNormalizedDefaultProperties(
    config: Partial<NormalizedDefaultProperties> | undefined,
    options: {
        includeAlignmentProperty?: boolean;
        includePaddingProperty?: boolean;
        includeVerticalAlignmentProperty?: boolean;
        includeHorizontalAlignmentProperty?: boolean;
        isRightToLeftContent?: boolean;
    } = {},
): NormalizedDefaultProperties {
    const normalizedConfig: NormalizedDefaultProperties = {
        borderStyle: 'none',
        borderWidth: '',
        borderColor: '',
        backgroundColor: '',
        width: '',
        height: '',
        ...config,
    };

    if (options.includeAlignmentProperty && !normalizedConfig.alignment) {
        normalizedConfig.alignment = 'center';
    }

    if (options.includePaddingProperty && !normalizedConfig.padding) {
        normalizedConfig.padding = '';
    }

    if (options.includeVerticalAlignmentProperty && !normalizedConfig.verticalAlignment) {
        normalizedConfig.verticalAlignment = 'middle';
    }

    if (options.includeHorizontalAlignmentProperty && !normalizedConfig.horizontalAlignment) {
        normalizedConfig.horizontalAlignment = options.isRightToLeftContent ? 'right' : 'left';
    }

    return normalizedConfig;
}

/**
 * Enables the `'alignment'` attribute for table.
 *
 * @param defaultValue The default alignment value.
 */
export function enableTableAlignmentProperty(schema: Schema, conversion: Conversion, defaultValue: string): void {
    schema.extend('queryTable', {
        allowAttributes: [COMMAND_ALIGNMENT_QUERY_TABLE],
    });

    conversion.for('downcast').attributeToAttribute({
        model: {
            name: 'queryTable',
            key: COMMAND_ALIGNMENT_QUERY_TABLE,
        },
        view: alignment => ({
            key: 'style',
            value: {
                // Model: `alignment:center` => CSS: `float:none`.
                float: alignment === 'center' ? 'none' : alignment,
            },
        }),
        converterPriority: 'high',
    });

    conversion
        .for('upcast')
        // Support for the `float:*;` CSS definition for the table alignment.
        .attributeToAttribute({
            view: {
                name: 'queryTable',
                styles: {
                    float: FLOAT_VALUES_REG_EXP,
                },
            },
            model: {
                key: COMMAND_ALIGNMENT_QUERY_TABLE,
                value: (viewElement: ViewElement) => {
                    let align = viewElement.getStyle('float');

                    // CSS: `float:none` => Model: `alignment:center`.
                    if (align === 'none') {
                        align = 'center';
                    }

                    return align === defaultValue ? null : align;
                },
            },
        })
        // Support for the `align` attribute as the backward compatibility while pasting from other sources.
        .attributeToAttribute({
            view: {
                attributes: {
                    align: ALIGN_VALUES_REG_EXP,
                },
            },
            model: {
                name: 'queryTable',
                key: COMMAND_ALIGNMENT_QUERY_TABLE,
                value: (viewElement: ViewElement) => {
                    const align = viewElement.getAttribute('align');

                    return align === defaultValue ? null : align;
                },
            },
        });
}

/**
 * Register table border and background attributes converters.
 */
export function downcastTableBorderAndBackgroundAttributes(editor: Editor): void {
    const modelAttributes = {
        'border-width': COMMAND_BORDER_WIDTH_QUERY_TABLE,
        'border-color': COMMAND_BORDER_COLOR_QUERY_TABLE,
        'border-style': COMMAND_BORDER_STYLE_QUERY_TABLE,
        'background-color': COMMAND_BACKGROUND_QUERY_TABLE,
    };

    // eslint-disable-next-line no-restricted-syntax
    for (const [styleName, modelAttribute] of Object.entries(modelAttributes)) {
        editor.conversion.for('dataDowncast').add(dispatcher => {
            return dispatcher.on(
                `attribute:${modelAttribute}:queryTable`,
                (evt, data, conversionApi): void => {
                    const { item, attributeNewValue } = data;
                    const { mapper, writer } = conversionApi;

                    if (!conversionApi.consumable.consume(item, evt.name)) {
                        return;
                    }

                    const table = mapper.toViewElement(item);

                    if (attributeNewValue) {
                        writer.setStyle(styleName, attributeNewValue, table);
                    } else {
                        writer.removeStyle(styleName, table);
                    }
                },
                { priority: 'high' },
            );
        });
    }
}

/**
 * Enables the `'tableInsertPageBreak'` attribute for table row.
 *
 * @param defaultPageBreak The default page break value.
 */
export function enableTableRowProperties(schema: Schema, conversion: Conversion): void {
    schema.extend('queryTableRow', {
        allowAttributes: COMMAND_INSERT_PAGE_BREAK_QUERY_TABLE,
    });

    upcastPageBreak(conversion, 'tr', COMMAND_INSERT_PAGE_BREAK_QUERY_TABLE);

    downcastAttributeToStyle(conversion, {
        modelElement: 'queryTableRow',
        modelAttribute: COMMAND_INSERT_PAGE_BREAK_QUERY_TABLE,
        styleName: 'page-break-after',
    });
}

export function upcastPageBreak(conversion: Conversion, viewElementName: string, modelAttribute: string): void {
    conversion.for('upcast').add(dispatcher =>
        dispatcher.on(`element:${viewElementName}`, (_evt, data, conversionApi) => {
            if (!data.modelRange) {
                return;
            }

            const stylesToConsume = ['page-break-after'].filter(styleName => data.viewItem.hasStyle(styleName));

            if (!stylesToConsume.length) {
                return;
            }

            const matcherPattern = {
                styles: stylesToConsume,
            };

            if (!conversionApi.consumable.test(data.viewItem, matcherPattern)) {
                return;
            }

            const modelElement = [...data.modelRange.getItems({ shallow: true })].pop();

            conversionApi.consumable.consume(data.viewItem, matcherPattern);

            const normalizedPageBreak = data.viewItem.getNormalizedStyle('page-break-after');

            conversionApi.writer.setAttribute(modelAttribute, normalizedPageBreak, modelElement);
        }),
    );
}
