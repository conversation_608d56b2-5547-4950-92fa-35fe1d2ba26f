import * as React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import { ButtonView } from '@ckeditor/ckeditor5-ui';
import type { Locale } from '@ckeditor/ckeditor5-utils';
import { COMMAND_TOGGLE_RIGHT_PANEL } from '../../constants';
import TogglePluginUi from '../toggle-plugin-ui';
import type { ReportEditorConfig } from '../../report-editor-config-type';

export default class ToggleRightPanelUi extends TogglePluginUi {
    public static get pluginName(): string {
        return 'ToggleRightPanelUi' as const;
    }

    public togglePanel = (): void => {
        const newState = !this.view.isOn;
        this.view.set({ isOn: newState });
        const reportEditorConfig = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;
        reportEditorConfig.onRightPanelOpenChange(newState);
    };

    public init(): void {
        const editor = this.editor;

        // Add bold button to feature components.
        editor.ui.componentFactory.add(COMMAND_TOGGLE_RIGHT_PANEL, (locale: Locale) => {
            this.view = new ButtonView(locale);
            const { localize } = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;

            this.view.set({
                label: localize('@sage/xtrem-document-editor/toggle-formatting-panel', 'Show/Hide formatting panel'),
                icon: renderToStaticMarkup(
                    <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M0 7.5H9V13.5H0V7.5ZM0 17.4C0 19.1 1.4 20.5 3.1 20.5H6C7.6 20.5 9 19.1 9 17.4V14.5H0V17.4ZM5.9 0.5H3.1C1.4 0.5 0 1.9 0 3.6V6.5H9V3.6C9 1.9 7.6 0.5 5.9 0.5ZM11 17.4C11 19.1 12.3 20.5 14 20.5H16.9C18.6 20.5 20 19.1 20 17.4V14.5H11V17.4ZM16.9 0.5H14C12.4 0.5 11 1.9 11 3.6V6.5H20V3.6C20 1.9 18.6 0.5 16.9 0.5ZM11 7.5H20V13.5H11V7.5Z"
                            fill="#000000e6"
                        />
                    </svg>,
                ),
                tooltip: true,
                isToggleable: true,
                isEnabled: true,
            });

            // Execute command.
            this.listenTo(this.view, 'execute', this.togglePanel);

            return this.view;
        });
    }
}
