import { closeRightPanel, getPage, openRightPanel, waitForSelectorToDisappear } from '../../../__tests__/test-helper';

describe('toggle formatting panel command', () => {
    it('should toggle formatting panel', async () => {
        const page = getPage();
        await openRightPanel();
        await page.waitForSelector('.document-editor-right-panel');
        await closeRightPanel();
        await waitForSelectorToDisappear('.document-editor-right-panel');
    });
});
