import { Plugin } from '@ckeditor/ckeditor5-core';
import { COMMAND_TOGGLE_RIGHT_PANEL } from '../../constants';
import ToggleRightPanelCommand from './toggle-right-panel-command';

export default class ToggleRightPanelEditing extends Plugin {
    public static get pluginName(): string {
        return 'ToggleRightPanel';
    }

    public init(): void {
        this.editor.commands.add(COMMAND_TOGGLE_RIGHT_PANEL, new ToggleRightPanelCommand(this.editor));
    }
}
