import { Plugin } from '@ckeditor/ckeditor5-core';
import { Widget, toWidget, viewToModelPositionOutsideModelElement } from '@ckeditor/ckeditor5-widget';
import {
    ATTR_PROPERTY_DATA_FORMAT,
    ATTR_PROPERTY_DATA_TYPE,
    ATTR_PROPERTY_DISPLAY_LABEL,
    ATTR_PROPERTY_NAME,
    ATTR_PROPERTY_PARENT_CONTEXT_TYPE,
    BLOCK_AGGREGATION_PROPERTY_NAME,
    GraphQLTypes,
} from '@sage/xtrem-shared';
import {
    COMMAND_CHANGE_PROPERTY_FORMAT,
    COMMAND_INSERT_PROPERTY,
    DATA_FORMAT_ENUM,
    OBJECT_TYPE_GLOBAL_PROPERTIES,
} from '../../constants';
import type { DataModelPropertyType } from '../../context-provider';
import ChangePropertyFormatCommand from './change-property-format-command';
import InsertPropertyCommand from './insert-property-command';
import type { Batch, Element } from '@ckeditor/ckeditor5-engine';
import { findChildElementsByName, getContextObjectType } from '../utils';
import type { ReportEditorConfig } from '../../report-editor-config-type';
import { last } from 'lodash';
import { ContextualBalloon } from '@ckeditor/ckeditor5-ui'; // ADDED
import PropertyTooltipView from './property-tooltip-view';

export default class PropertyEditing extends Plugin {
    private _balloon: ContextualBalloon;

    private _balloonForm: PropertyTooltipView;

    static get requires(): Array<typeof Plugin> {
        return [Widget, ContextualBalloon];
    }

    init(): void {
        this._definePropertyConsistencyValidator();
        this._defineSchema();
        this._defineConverters();
        this._defineTooltip();

        this.editor.commands.add(COMMAND_INSERT_PROPERTY, new InsertPropertyCommand(this.editor));
        this.editor.commands.add(COMMAND_CHANGE_PROPERTY_FORMAT, new ChangePropertyFormatCommand(this.editor));

        this.editor.editing.mapper.on(
            'viewToModelPosition',
            viewToModelPositionOutsideModelElement(this.editor.model, viewElement => viewElement.hasClass('property')),
        );
    }

    private _defineTooltip(): void {
        // Based on https://ckeditor.com/docs/ckeditor5/latest/tutorials/abbreviation-plugin/abbreviation-plugin-level-2.html
        this._balloon = this.editor.plugins.get(ContextualBalloon);
        this._balloonForm = new PropertyTooltipView(this.editor.locale);
        this.listenTo(this.editor.editing.view.document, 'mouseup', evt => {
            const source: any = evt.source;
            const selectedElement = source?.selection.getSelectedElement();
            if (selectedElement && Array.from(selectedElement.getClassNames()).indexOf('property') !== -1) {
                this._showTooltip(selectedElement);
            } else {
                this._hideTooltip();
            }
        });
    }

    private _hideTooltip(): void {
        if (this._balloon.hasView(this._balloonForm)) {
            this._balloon.remove(this._balloonForm);

            // Focus the editing view after closing the form view.
            this.editor.editing.view.focus();
        }
    }

    private _showTooltip(selectedElement: Element): void {
        const { localize } = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;
        const path = selectedElement.getAttribute(ATTR_PROPERTY_NAME) as string;
        const type = selectedElement.getAttribute(ATTR_PROPERTY_DATA_TYPE) as string;
        const context = selectedElement.getAttribute(ATTR_PROPERTY_PARENT_CONTEXT_TYPE) as string;
        this._balloonForm.setPropertyInformation(localize, context, type, path);

        if (!this._balloon.hasView(this._balloonForm)) {
            this._balloon.add({
                view: this._balloonForm,
                position: this._getBalloonPositionData(),
            });
        } else {
            this._balloon.updatePosition(this._getBalloonPositionData());
        }
    }

    private _getBalloonPositionData(): { target: () => Range } {
        const view = this.editor.editing.view;
        const viewDocument = view.document;
        const target = (): Range => view.domConverter.viewRangeToDom(viewDocument.selection.getFirstRange()!);

        return {
            target,
        };
    }

    private _definePropertyConsistencyValidator(): void {
        const reportEditorConfig = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;

        this.editor.model.document.on('change', (_, batch: Batch) => {
            const roots = this.editor.model.document.getRoots();
            for (const root of roots) {
                if (batch.isUndoable && !batch.isUndo && root) {
                    const propertyElements = findChildElementsByName('property', root);
                    const invalidElement = propertyElements.find(p => {
                        const contextType = getContextObjectType(p);
                        const requiredContextType = p.getAttribute(ATTR_PROPERTY_PARENT_CONTEXT_TYPE);
                        return (
                            requiredContextType !== OBJECT_TYPE_GLOBAL_PROPERTIES && contextType !== requiredContextType
                        );
                    });
                    if (invalidElement) {
                        reportEditorConfig.onDisplayNotification(
                            'This property can only be used in the same context where it was defined',
                            'warning',
                        );
                        this.editor.execute('undo');
                    }
                }
            }
        });
    }

    _defineSchema(): void {
        const schema = this.editor.model.schema;

        schema.register('property', {
            // Behaves like a self-contained inline object (e.g. an inline image)
            // allowed in places where $text is allowed (e.g. in paragraphs).
            // The inline widget can have the same attributes as text (for example linkHref, bold).
            inheritAllFrom: '$inlineObject',

            // The placeholder can have many types, like date, name, surname, etc:
            allowAttributes: [
                ATTR_PROPERTY_NAME,
                ATTR_PROPERTY_DISPLAY_LABEL,
                ATTR_PROPERTY_DATA_TYPE,
                ATTR_PROPERTY_DATA_FORMAT,
                ATTR_PROPERTY_PARENT_CONTEXT_TYPE,
            ],
        });
    }

    _getEditorPropertyText(displayLabel: string, name: string): string {
        const nameComponents = name.split('.');
        if (nameComponents[0] === BLOCK_AGGREGATION_PROPERTY_NAME) {
            const method = last(nameComponents);

            return `${method}(${displayLabel})`;
        }

        return displayLabel;
    }

    /**
     * The property editor's dataDowncast and editingDowncast roles are different, in the editor we just display a simple placeholder,
     * but in the template we use handlebar moustache wrappers and depending on the formatting helper functions too.
     */
    _defineConverters(): void {
        const conversion = this.editor.conversion;

        conversion
            .for('upcast')
            .elementToElement({
                view: {
                    name: 'span',
                    classes: ['property'],
                },
                model: (viewElement, { writer: modelWriter }) => {
                    return modelWriter.createElement('property', {
                        [ATTR_PROPERTY_NAME]: viewElement.getAttribute(ATTR_PROPERTY_NAME),
                        [ATTR_PROPERTY_DISPLAY_LABEL]: viewElement.getAttribute(ATTR_PROPERTY_DISPLAY_LABEL),
                        [ATTR_PROPERTY_DATA_TYPE]: viewElement.getAttribute(ATTR_PROPERTY_DATA_TYPE),
                        [ATTR_PROPERTY_DATA_FORMAT]: viewElement.getAttribute(ATTR_PROPERTY_DATA_FORMAT),
                        [ATTR_PROPERTY_PARENT_CONTEXT_TYPE]: viewElement.getAttribute(
                            ATTR_PROPERTY_PARENT_CONTEXT_TYPE,
                        ),
                    });
                },
            })
            .attributeToAttribute({
                model: ATTR_PROPERTY_DISPLAY_LABEL,
                view: ATTR_PROPERTY_DISPLAY_LABEL,
            })
            .attributeToAttribute({
                model: ATTR_PROPERTY_DATA_TYPE,
                view: ATTR_PROPERTY_DATA_TYPE,
            })
            .attributeToAttribute({
                model: ATTR_PROPERTY_DATA_FORMAT,
                view: ATTR_PROPERTY_DATA_FORMAT,
            })
            .attributeToAttribute({
                model: ATTR_PROPERTY_PARENT_CONTEXT_TYPE,
                view: ATTR_PROPERTY_PARENT_CONTEXT_TYPE,
            });

        conversion.for('editingDowncast').elementToElement({
            model: 'property',
            view: (modelItem, { writer: viewWriter }) => {
                const name = modelItem.getAttribute(ATTR_PROPERTY_NAME) as string;
                const displayLabel = modelItem.getAttribute(ATTR_PROPERTY_DISPLAY_LABEL) as string;
                const dataType = modelItem.getAttribute(ATTR_PROPERTY_DATA_TYPE);
                const dataFormat = modelItem.getAttribute(ATTR_PROPERTY_DATA_FORMAT);
                const parentContextType = modelItem.getAttribute(ATTR_PROPERTY_PARENT_CONTEXT_TYPE);

                const placeholderView = viewWriter.createContainerElement('span', {
                    class: 'property',
                    [ATTR_PROPERTY_DISPLAY_LABEL]: displayLabel,
                    [ATTR_PROPERTY_DATA_TYPE]: dataType,
                    [ATTR_PROPERTY_NAME]: name,
                    [ATTR_PROPERTY_DATA_FORMAT]: dataFormat,
                    [ATTR_PROPERTY_PARENT_CONTEXT_TYPE]: parentContextType,
                });

                // Insert the placeholder name (as a text).
                const innerText = viewWriter.createText(this._getEditorPropertyText(displayLabel, name));
                viewWriter.insert(viewWriter.createPositionAt(placeholderView, 0), innerText);

                // Enable widget handling on a placeholder element inside the editing view.
                return toWidget(placeholderView, viewWriter);
            },
        });

        conversion
            .for('dataDowncast')
            .elementToElement({
                model: 'property',
                view: (modelItem, { writer: viewWriter }) => {
                    const name = modelItem.getAttribute(ATTR_PROPERTY_NAME);
                    const displayLabel = modelItem.getAttribute(ATTR_PROPERTY_DISPLAY_LABEL);
                    const dataType = modelItem.getAttribute(ATTR_PROPERTY_DATA_TYPE) as DataModelPropertyType;
                    const dataFormat = modelItem.getAttribute(ATTR_PROPERTY_DATA_FORMAT);
                    const parentContextType = modelItem.getAttribute(ATTR_PROPERTY_PARENT_CONTEXT_TYPE);

                    const placeholderView = viewWriter.createContainerElement('span', {
                        class: 'property',
                        [ATTR_PROPERTY_DISPLAY_LABEL]: displayLabel,
                        [ATTR_PROPERTY_DATA_TYPE]: dataType,
                        [ATTR_PROPERTY_NAME]: name,
                        [ATTR_PROPERTY_DATA_FORMAT]: dataFormat,
                        [ATTR_PROPERTY_PARENT_CONTEXT_TYPE]: parentContextType,
                    });

                    let innerTextContent = name;

                    // In the view we use special helper bars helper functions within the tags
                    if (dataFormat && dataType === 'Date') {
                        innerTextContent = `formatDate ${name} '${dataFormat}'`;
                    }
                    if (dataFormat && (dataType === 'Float' || dataType === 'Decimal' || dataType === 'Int')) {
                        innerTextContent = `formatNumber ${name} ${dataFormat}`;
                    }
                    if (dataFormat === DATA_FORMAT_ENUM) {
                        innerTextContent = `enumValue '${dataType}' ${name}`;
                    }

                    // Insert the placeholder name (as a text).
                    const innerText = viewWriter.createText(
                        dataType === GraphQLTypes._OutputTextStream || dataType === '_OutputTextStream'
                            ? `{{{${innerTextContent}}}}`
                            : `{{${innerTextContent}}}`,
                    );
                    viewWriter.insert(viewWriter.createPositionAt(placeholderView, 0), innerText);

                    return placeholderView;
                },
            })
            .attributeToAttribute({
                model: ATTR_PROPERTY_DISPLAY_LABEL,
                view: ATTR_PROPERTY_DISPLAY_LABEL,
            })
            .attributeToAttribute({
                model: ATTR_PROPERTY_DATA_TYPE,
                view: ATTR_PROPERTY_DATA_TYPE,
            })
            .attributeToAttribute({
                model: ATTR_PROPERTY_NAME,
                view: ATTR_PROPERTY_NAME,
            })
            .attributeToAttribute({
                model: ATTR_PROPERTY_DATA_FORMAT,
                view: ATTR_PROPERTY_DATA_FORMAT,
            })
            .attributeToAttribute({
                model: ATTR_PROPERTY_PARENT_CONTEXT_TYPE,
                view: ATTR_PROPERTY_PARENT_CONTEXT_TYPE,
            });
    }
}
