import {
    getByTestId,
    getPage,
    setValue,
    openRightPanel,
    getElementValue,
    getEditorHtmlContent,
} from '../../../__tests__/test-helper';

describe('property editing', () => {
    it('should upcast property element', async () => {
        await setValue(`
        <span class="property" data-property-display-label="Net Total" data-property-data-type="Float" data-property-name="totalWithoutTax" data-property-data-format="" data-property-parent-context="$root">
            {{totalWithoutTax}}
        </span>
        `);

        await expect(getEditorHtmlContent()).resolves.toMatchSnapshot();
    });

    it('should upcast property element with number data format option ', async () => {
        const page = getPage();
        setValue(`
        <span
            class="property"
            data-property-display-label="Net Total"
            data-property-data-type="Float"
            data-property-name="totalWithoutTax"
            data-property-data-format="3"
            data-property-parent-context="$root"
        >
            {{formatNumber totalWithoutTax 3}}
        </span>
        `);

        await page.click('.property');
        await openRightPanel();
        const precisionInput = await getByTestId<HTMLInputElement>('control-vertical-property-format-number');
        await expect(getElementValue(precisionInput)).resolves.toEqual('3');
    });

    it('should upcast property element with date data format option ', async () => {
        const page = getPage();
        setValue(`
        <span
            class="property"
            data-property-display-label="Order Date"
            data-property-data-type="Date"
            data-property-name="orderDate"
            data-property-data-format="LongMonthYear"
            data-property-parent-context="$root"
        >
            {{formatDate orderDate 3}}
        </span>
        `);

        await page.click('.property');
        await openRightPanel();
        const dateFormatInput = await getByTestId<HTMLInputElement>('control-vertical-property-format-date');
        await expect(getElementValue(dateFormatInput)).resolves.toEqual('Full month name with year');
    });
});
