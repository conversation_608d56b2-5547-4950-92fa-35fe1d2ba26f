import { executeEditorCommand, getByTestId, getPage, getValue, selectEditorRoot } from '../../../__tests__/test-helper';
import { COMMAND_INSERT_PROPERTY } from '../../../constants';

describe('insert property command', () => {
    it('should insert simple string property', async () => {
        await executeEditorCommand(COMMAND_INSERT_PROPERTY, {
            remapInfo: { path: 'invoiceNumber' },
            recordContext: '$root',
            selectedItem: {
                label: 'Invoice Number',
                data: { name: 'invoiceNumber', label: 'Invoice Number', kind: 'SCALAR', type: 'String' },
                id: 'invoiceNumber',
                key: 'invoiceNumber',
                labelKey: 'Invoice Number',
                labelPath: 'Invoice Number',
            },
        });

        const page = getPage();
        await page.waitForSelector('.property');

        await expect(getValue()).resolves.toContain(
            '<span class="property" data-property-display-label="Invoice Number" data-property-data-type="String" data-property-name="invoiceNumber" data-property-data-format="" data-property-parent-context="$root">',
        );
        await expect(getValue()).resolves.toContain('{{invoiceNumber}}');
    });

    it('should insert simple date property with default date format', async () => {
        await executeEditorCommand(COMMAND_INSERT_PROPERTY, {
            remapInfo: { path: 'deliveryDate' },
            recordContext: '$root',
            selectedItem: {
                label: 'Delivery Date',
                data: { name: 'deliveryDate', label: 'Delivery Date', kind: 'SCALAR', type: 'Date' },
                id: 'deliveryDate',
                key: 'deliveryDate',
                labelKey: 'Delivery Date',
                labelPath: 'Delivery Date',
            },
        });

        const page = getPage();
        await page.waitForSelector('.property');

        await expect(getValue()).resolves.toContain(
            '<span class="property" data-property-display-label="Delivery Date" data-property-data-type="Date" data-property-name="deliveryDate" data-property-data-format="FullDate" data-property-parent-context="$root">',
        );
        await expect(getValue()).resolves.toContain("{{formatDate deliveryDate 'FullDate'}}");
    });

    it('should insert simple decimal property with two decimal places format', async () => {
        await executeEditorCommand(COMMAND_INSERT_PROPERTY, {
            remapInfo: { path: 'netPrice' },
            recordContext: '$root',
            selectedItem: {
                label: 'Net Price',
                data: { name: 'netPrice', label: 'Net Price', kind: 'SCALAR', type: 'Decimal' },
                id: 'netPrice',
                key: 'netPrice',
                labelKey: 'Net Price',
                labelPath: 'Net Price',
            },
        });

        const page = getPage();
        await page.waitForSelector('.property');

        await expect(getValue()).resolves.toContain(
            '<span class="property" data-property-display-label="Net Price" data-property-data-type="Decimal" data-property-name="netPrice" data-property-data-format="2" data-property-parent-context="$root">',
        );
        await expect(getValue()).resolves.toContain('{{formatNumber netPrice 2}}');
    });

    it('should insert simple float property with two decimal places format', async () => {
        await executeEditorCommand(COMMAND_INSERT_PROPERTY, {
            remapInfo: { path: 'netWeight' },
            recordContext: '$root',
            selectedItem: {
                label: 'Net Weight',
                data: { name: 'netWeight', label: 'Net Weight', kind: 'SCALAR', type: 'Float' },
                id: 'netWeight',
                key: 'netWeight',
                labelKey: 'Net Weight',
                labelPath: 'Net Weight',
            },
        });

        const page = getPage();
        await page.waitForSelector('.property');

        await expect(getValue()).resolves.toContain(
            '<span class="property" data-property-display-label="Net Weight" data-property-data-type="Float" data-property-name="netWeight" data-property-data-format="2" data-property-parent-context="$root">',
        );
        await expect(getValue()).resolves.toContain('{{formatNumber netWeight 2}}');
    });

    it('should insert simple integer property with empty format', async () => {
        await executeEditorCommand(COMMAND_INSERT_PROPERTY, {
            remapInfo: { path: 'itemQuantity' },
            recordContext: '$root',
            selectedItem: {
                label: 'Item Quantity',
                data: { name: 'itemQuantity', label: 'Item Quantity', kind: 'SCALAR', type: 'Int' },
                id: 'itemQuantity',
                key: 'itemQuantity',
                labelKey: 'Item Quantity',
                labelPath: 'Item Quantity',
            },
        });

        const page = getPage();
        await page.waitForSelector('.property');

        await expect(getValue()).resolves.toContain(
            '<span class="property" data-property-display-label="Item Quantity" data-property-data-type="Int" data-property-name="itemQuantity" data-property-data-format="" data-property-parent-context="$root">',
        );
        await expect(getValue()).resolves.toContain('{{itemQuantity}}');
    });

    it('should insert enum property', async () => {
        await executeEditorCommand(COMMAND_INSERT_PROPERTY, {
            remapInfo: { path: 'category' },
            recordContext: '$root',
            selectedItem: {
                label: 'Category',
                data: { name: 'category', label: 'Category', kind: 'ENUM', type: '@sage/xtrem-test/TestCategory' },
                id: 'category',
                key: 'category',
                labelKey: 'Category',
                labelPath: 'Category',
            },
        });

        const page = getPage();
        await page.waitForSelector('.property');

        await expect(getValue()).resolves.toContain(
            '<span class="property" data-property-display-label="Category" data-property-data-type="@sage/xtrem-test/TestCategory" data-property-name="category" data-property-data-format="ENUM" data-property-parent-context="$root">',
        );
        await expect(getValue()).resolves.toContain("{{enumValue '@sage/xtrem-test/TestCategory' category}}");
    });

    it('should display a warning notification if the property is inserted to a context that it is not usable in', async () => {
        await executeEditorCommand(COMMAND_INSERT_PROPERTY, {
            remapInfo: { path: 'deliveryDate' },
            // The property will be inserted to the root context but it can only be used in the sales order context
            recordContext: 'SalesOrder',
            selectedItem: {
                label: 'Delivery Date',
                data: { name: 'deliveryDate', label: 'Delivery Date', kind: 'SCALAR', type: 'Date' },
                id: 'deliveryDate',
                key: 'deliveryDate',
                labelKey: 'Delivery Date',
                labelPath: 'Delivery Date',
            },
        });

        const notificationElement = await getByTestId<HTMLDivElement>('notification-content');
        const textContent = (await notificationElement.getProperty('textContent')).jsonValue();
        await expect(textContent).resolves.toEqual(
            'This property can only be used in the same context where it was defined',
        );
    });

    it('should insert simple string property to the header', async () => {
        await selectEditorRoot('header');

        await executeEditorCommand(COMMAND_INSERT_PROPERTY, {
            remapInfo: { path: 'invoiceNumber' },
            recordContext: '$root',
            selectedItem: {
                label: 'Invoice Number',
                data: { name: 'invoiceNumber', label: 'Invoice Number', kind: 'SCALAR', type: 'String' },
                id: 'invoiceNumber',
                key: 'invoiceNumber',
                labelKey: 'Invoice Number',
                labelPath: 'Invoice Number',
            },
        });

        const page = getPage();
        await page.waitForSelector('.property');

        await expect(getValue('header')).resolves.toContain(
            '<span class="property" data-property-display-label="Invoice Number" data-property-data-type="String" data-property-name="invoiceNumber" data-property-data-format="" data-property-parent-context="$root">',
        );
        await expect(getValue('header')).resolves.toContain('{{invoiceNumber}}');
    });

    it('should insert simple string property to the footer', async () => {
        await selectEditorRoot('footer');

        await executeEditorCommand(COMMAND_INSERT_PROPERTY, {
            remapInfo: { path: 'invoiceNumber' },
            recordContext: '$root',
            selectedItem: {
                label: 'Invoice Number',
                data: { name: 'invoiceNumber', label: 'Invoice Number', kind: 'SCALAR', type: 'String' },
                id: 'invoiceNumber',
                key: 'invoiceNumber',
                labelKey: 'Invoice Number',
                labelPath: 'Invoice Number',
            },
        });

        const page = getPage();
        await page.waitForSelector('.property');

        await expect(getValue('footer')).resolves.toContain(
            '<span class="property" data-property-display-label="Invoice Number" data-property-data-type="String" data-property-name="invoiceNumber" data-property-data-format="" data-property-parent-context="$root">',
        );
        await expect(getValue('footer')).resolves.toContain('{{invoiceNumber}}');
    });
});
