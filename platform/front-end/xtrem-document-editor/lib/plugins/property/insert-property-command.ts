import { Command } from '@ckeditor/ckeditor5-core';
import type { Element, Writer } from '@ckeditor/ckeditor5-engine';
import type { TreeElement } from '@sage/xtrem-shared';
import {
    ATTR_PROPERTY_DATA_FORMAT,
    ATTR_PROPERTY_DATA_TYPE,
    ATTR_PROPERTY_DISPLAY_LABEL,
    ATTR_PROPERTY_NAME,
    ATTR_PROPERTY_PARENT_CONTEXT_TYPE,
} from '@sage/xtrem-shared';
import { isEnumField } from '@sage/xtrem-ui-components';
import { DATA_FORMAT_DECIMAL, DATA_FORMAT_ENUM } from '../../constants';
import type { DataModelProperty, ObjectInsertionDetails } from '../../context-provider';
import { getContextObjectType } from '../utils';

export default class InsertPropertyCommand extends Command {
    private static readonly getDataFormat = (itemDetails: DataModelProperty): string => {
        if (itemDetails.type === 'Date') {
            return 'FullDate';
        }

        if (isEnumField(itemDetails)) {
            return DATA_FORMAT_ENUM;
        }

        if (itemDetails.type === 'Decimal' || itemDetails.type === 'Float') {
            return DATA_FORMAT_DECIMAL;
        }
        return '';
    };

    public static createPropertyElement = (
        propertyKey: string,
        itemDetails: DataModelProperty,
        recordContext: string,
        writer: Writer,
    ): Element => {
        return writer.createElement('property', {
            [ATTR_PROPERTY_NAME]: propertyKey,
            [ATTR_PROPERTY_DISPLAY_LABEL]: itemDetails.label,
            [ATTR_PROPERTY_DATA_TYPE]: itemDetails.type,
            [ATTR_PROPERTY_DATA_FORMAT]: InsertPropertyCommand.getDataFormat(itemDetails),
            [ATTR_PROPERTY_PARENT_CONTEXT_TYPE]: recordContext,
        });
    };

    execute({
        selectedItem,
        remapInfo,
        recordContext,
    }: {
        selectedItem: TreeElement<DataModelProperty>;
        remapInfo: ObjectInsertionDetails;
        recordContext: string;
    }): void {
        const editor = this.editor;

        editor.model.change(writer => {
            // Create a <placeholder> element with the "name" attribute (and all the selection attributes)...
            const placeholder = InsertPropertyCommand.createPropertyElement(
                remapInfo.path,
                selectedItem.data,
                recordContext,
                writer,
            );

            // ... and insert it into the document. Put the selection on the inserted element.
            editor.model.insertObject(placeholder);
        });
    }

    refresh(): void {
        const model = this.editor.model;
        const selection = model.document.selection;
        const isAllowed = model.schema.checkChild(selection.focus?.parent as any, 'property');
        const contextObjectType = getContextObjectType(selection.focus?.parent);
        this.isEnabled = isAllowed && !!contextObjectType;
    }
}
