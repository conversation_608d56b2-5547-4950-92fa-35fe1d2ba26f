import { Command } from '@ckeditor/ckeditor5-core';
import { ATTR_PROPERTY_DATA_FORMAT } from '@sage/xtrem-shared';
import type { DataModelPropertyType } from '../../context-provider';
import { getSelectedPropertyDataTypeAndFormat } from '../utils';

export default class ChangePropertyFormatCommand extends Command {
    execute({ format }: { format: DataModelPropertyType }): void {
        const editor = this.editor;

        editor.model.change(writer => {
            const model = this.editor.model;
            const selection = model.document.selection;
            writer.setAttribute(ATTR_PROPERTY_DATA_FORMAT, format, [...selection.getRanges()][0]);
        });
    }

    /**
     * The data format command is only available if a property is selected and it is a float or a date
     */
    refresh(): void {
        const dataTypeAndFormat = getSelectedPropertyDataTypeAndFormat(this.editor);
        this.isEnabled =
            dataTypeAndFormat?.dataType === 'Date' ||
            dataTypeAndFormat?.dataType === 'Float' ||
            dataTypeAndFormat?.dataType === 'Decimal' ||
            dataTypeAndFormat?.dataType === 'Int';
    }
}
