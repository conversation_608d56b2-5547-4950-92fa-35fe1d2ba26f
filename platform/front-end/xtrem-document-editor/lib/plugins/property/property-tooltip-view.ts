import type { ViewCollection } from '@ckeditor/ckeditor5-ui';
import { View, LabelView } from '@ckeditor/ckeditor5-ui';
import type { Locale } from '@ckeditor/ckeditor5-utils';
import { OBJECT_TYPE_GLOBAL_PROPERTIES } from '../../constants';
import { BLOCK_AGGREGATION_PROPERTY_NAME, type LocalizeFunction } from '@sage/xtrem-shared';

export default class PropertyTooltipView extends View {
    private readonly contextView: LabelView = new LabelView();

    private readonly typeView: LabelView = new LabelView();

    private readonly propertyPath: LabelView = new LabelView();

    private readonly childViews: ViewCollection<any>;

    constructor(locale?: Locale) {
        super(locale);
        this.typeView = new LabelView();

        this.childViews = this.createCollection([this.contextView, this.typeView, this.propertyPath]);

        this.setTemplate({
            tag: 'div',
            children: this.childViews,
            attributes: {
                class: 'ck-property-tooltip',
            },
        });
    }

    setPropertyInformation(localize: LocalizeFunction, context: string, type: string, propertyPath: string): void {
        const globalPropertiesLabel = localize('@sage/xtrem-document-editor/global-properties', 'Document properties');
        const isGlobalProperty = context === OBJECT_TYPE_GLOBAL_PROPERTIES;
        this.contextView.text = isGlobalProperty ? globalPropertiesLabel : context;
        this.typeView.text = localize('@sage/xtrem-document-editor/tooltip-data-type', 'Type: {{type}}', { type });
        this.propertyPath.text = localize('@sage/xtrem-document-editor/tooltip-data-path', 'Path: {{path}}', {
            path: propertyPath
                .replace('@root.', '')
                .replace('#', '')
                .replace(`${BLOCK_AGGREGATION_PROPERTY_NAME}.`, ''),
        });
    }
}
