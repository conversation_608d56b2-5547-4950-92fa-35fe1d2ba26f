import { Command } from '@ckeditor/ckeditor5-core';
import { Position } from '@ckeditor/ckeditor5-engine';
import type { HtmlComment } from '@ckeditor/ckeditor5-html-support';
import type { TreeElement } from '@sage/xtrem-shared';
import {
    ATTR_ALIAS,
    ATTR_CONTEXT_FILTER,
    ATTR_CONTEXT_LIST_ORDER,
    ATTR_CONTEXT_OBJECT_PATH,
    ATTR_CONTEXT_OBJECT_TYPE,
} from '@sage/xtrem-shared';
import type { DocumentEditorInsertDialogResult } from '../../components/insert-list-property-dialog/insert-list-property-dialog';
import type { DataModelProperty, ObjectInsertionDetails } from '../../context-provider';
import { generateQueryAlias } from '../utils';

export default class InsertRecordContext extends Command {
    get htmlCommentPlugin(): HtmlComment {
        return this.editor.plugins.get('HtmlComment') as HtmlComment;
    }

    execute({
        lookupResult,
        selectedItem,
        remapInfo,
    }: {
        lookupResult: DocumentEditorInsertDialogResult;
        selectedItem: TreeElement<DataModelProperty>;
        remapInfo: ObjectInsertionDetails;
    }): void {
        this.editor.model.change(writer => {
            const alias = generateQueryAlias();
            const recordContextElement = writer.createElement('recordContext', {
                [ATTR_CONTEXT_OBJECT_TYPE]: selectedItem.data.type,
                [ATTR_CONTEXT_OBJECT_PATH]: remapInfo.path,
                [ATTR_CONTEXT_FILTER]: JSON.stringify(lookupResult.filters || {}),
                [ATTR_CONTEXT_LIST_ORDER]: JSON.stringify(lookupResult.orderBy || {}),
                [ATTR_ALIAS]: alias,
            });

            this.editor.model.insertObject(recordContextElement);

            const recordContextBodyElement = writer.createElement('recordContextBody');

            writer.append(recordContextBodyElement, recordContextElement);
            const recordContextFooterElement = writer.createElement('recordContextFooter');
            writer.appendElement('paragraph', recordContextBodyElement);
            writer.append(recordContextFooterElement, recordContextElement);

            this.htmlCommentPlugin.createHtmlComment(
                new Position(recordContextFooterElement.root as any, recordContextFooterElement.getPath(), 'toNext'),
                '{{/with}}',
            );

            const remapInfoWithAlias = remapInfo.path.split('.');
            remapInfoWithAlias[0] = alias;
            this.htmlCommentPlugin.createHtmlComment(
                new Position(recordContextBodyElement.root as any, recordContextBodyElement.getPath(), 'toPrevious'),
                `{{#with ${remapInfoWithAlias.join('.')}}}`,
            );
        });
    }

    refresh(): void {
        const model = this.editor.model;
        const selection = model.document.selection;
        const allowedIn = model.schema.findAllowedParent(selection.getFirstPosition()!, 'recordContext');

        this.isEnabled = allowedIn !== null;
    }
}
