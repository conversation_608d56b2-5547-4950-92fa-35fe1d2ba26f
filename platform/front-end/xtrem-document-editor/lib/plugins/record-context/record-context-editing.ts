import { Plugin } from '@ckeditor/ckeditor5-core';
import { HtmlComment } from '@ckeditor/ckeditor5-html-support';
import { ContextualBalloon } from '@ckeditor/ckeditor5-ui';
import { Widget, toWidget, toWidgetEditable } from '@ckeditor/ckeditor5-widget';
import {
    ATTR_ALIAS,
    ATTR_CONTEXT_FILTER,
    ATTR_CONTEXT_LIST_ORDER,
    ATTR_CONTEXT_OBJECT_PATH,
    ATTR_CONTEXT_OBJECT_TYPE,
} from '@sage/xtrem-shared';
import { COMMAND_INSERT_RECORD_CONTEXT } from '../../constants';
import InsertRecordContext from './insert-record-context-command';

export default class RecordContextEditing extends Plugin {
    static get requires(): Array<typeof Plugin> {
        return [Widget, HtmlComment, ContextualBalloon];
    }

    init(): void {
        this._defineSchema();
        this._defineConverters();
        this.editor.commands.add(COMMAND_INSERT_RECORD_CONTEXT, new InsertRecordContext(this.editor));
    }

    _defineSchema(): void {
        const schema = this.editor.model.schema;

        schema.register('recordContext', {
            // Behaves like a self-contained block object (e.g. a block image)
            // allowed in places where other blocks are allowed (e.g. directly in the root).
            inheritAllFrom: '$blockObject',
            allowAttributes: [
                ATTR_CONTEXT_OBJECT_TYPE,
                ATTR_CONTEXT_OBJECT_PATH,
                ATTR_CONTEXT_FILTER,
                ATTR_CONTEXT_LIST_ORDER,
                ATTR_ALIAS,
            ],
        });

        schema.register('recordContextBody', {
            // Cannot be split or left by the caret.
            isLimit: true,

            allowIn: 'recordContext',

            // Allow content which is allowed in the root (e.g. paragraphs).
            allowContentOf: '$root',
        });

        schema.register('recordContextFooter', {
            // Cannot be split or left by the caret.
            isLimit: true,

            allowIn: 'recordContext',

            // Allow content which is allowed in the root (e.g. paragraphs).
            allowContentOf: [],
        });

        schema.addChildCheck((context, childDefinition): boolean | undefined => {
            if (context.endsWith('recordContextBody') && childDefinition.name === 'recordContext') {
                return true;
            }
            return undefined;
        });
    }

    _defineConverters(): void {
        const conversion = this.editor.conversion;

        conversion
            .for('upcast')
            .elementToElement({
                model: (viewElement, { writer }) => {
                    return writer.createElement('recordContext', {
                        [ATTR_CONTEXT_OBJECT_TYPE]: viewElement.getAttribute(ATTR_CONTEXT_OBJECT_TYPE),
                        [ATTR_CONTEXT_OBJECT_PATH]: viewElement.getAttribute(ATTR_CONTEXT_OBJECT_PATH),
                        [ATTR_CONTEXT_FILTER]: viewElement.getAttribute(ATTR_CONTEXT_FILTER),
                        [ATTR_CONTEXT_LIST_ORDER]: viewElement.getAttribute(ATTR_CONTEXT_LIST_ORDER),
                        [ATTR_ALIAS]: viewElement.getAttribute(ATTR_ALIAS),
                    });
                },
                view: {
                    name: 'section',
                    classes: 'record-context',
                    attributes: [
                        ATTR_CONTEXT_OBJECT_TYPE,
                        ATTR_CONTEXT_OBJECT_PATH,
                        ATTR_CONTEXT_FILTER,
                        ATTR_CONTEXT_LIST_ORDER,
                        ATTR_ALIAS,
                    ],
                },
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_OBJECT_TYPE,
                view: ATTR_CONTEXT_OBJECT_TYPE,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_OBJECT_TYPE,
                view: ATTR_CONTEXT_OBJECT_TYPE,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_FILTER,
                view: ATTR_CONTEXT_FILTER,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_LIST_ORDER,
                view: ATTR_CONTEXT_LIST_ORDER,
            })
            .attributeToAttribute({
                model: ATTR_ALIAS,
                view: ATTR_ALIAS,
            });

        conversion
            .for('dataDowncast')
            .elementToElement({
                model: 'recordContext',
                view: {
                    name: 'section',
                    classes: 'record-context',
                },
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_OBJECT_TYPE,
                view: ATTR_CONTEXT_OBJECT_TYPE,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_OBJECT_PATH,
                view: ATTR_CONTEXT_OBJECT_PATH,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_FILTER,
                view: ATTR_CONTEXT_FILTER,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_LIST_ORDER,
                view: ATTR_CONTEXT_LIST_ORDER,
            })
            .attributeToAttribute({
                model: ATTR_ALIAS,
                view: ATTR_ALIAS,
            });

        conversion
            .for('editingDowncast')
            .elementToElement({
                model: 'recordContext',
                view: (modelElement, { writer: viewWriter }) => {
                    const section = viewWriter.createContainerElement('section', {
                        class: 'record-context',
                    });
                    const element = toWidget(section, viewWriter, {});
                    return element;
                },
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_OBJECT_TYPE,
                view: ATTR_CONTEXT_OBJECT_TYPE,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_OBJECT_PATH,
                view: ATTR_CONTEXT_OBJECT_PATH,
            })

            .attributeToAttribute({
                model: ATTR_CONTEXT_FILTER,
                view: ATTR_CONTEXT_FILTER,
            })
            .attributeToAttribute({
                model: ATTR_CONTEXT_LIST_ORDER,
                view: ATTR_CONTEXT_LIST_ORDER,
            })
            .attributeToAttribute({
                model: ATTR_ALIAS,
                view: ATTR_ALIAS,
            });

        conversion.for('upcast').elementToElement({
            model: 'recordContextBody',
            view: {
                name: 'div',
                classes: 'report-context-body',
            },
        });
        conversion.for('dataDowncast').elementToElement({
            model: 'recordContextBody',
            view: {
                name: 'div',
                classes: 'report-context-body',
            },
        });
        conversion.for('editingDowncast').elementToElement({
            model: 'recordContextBody',
            view: (modelElement, { writer: viewWriter }) => {
                // Note: You use a more specialized createEditableElement() method here.
                const div = viewWriter.createEditableElement('div', {
                    class: 'report-context-body',
                });

                return toWidgetEditable(div, viewWriter);
            },
        });

        conversion.for('upcast').elementToElement({
            model: 'recordContextFooter',
            view: {
                name: 'span',
                classes: 'report-context-footer',
            },
        });
        conversion.for('dataDowncast').elementToElement({
            model: 'recordContextFooter',
            view: {
                name: 'span',
                classes: 'report-context-footer',
            },
        });
        conversion.for('editingDowncast').elementToElement({
            model: 'recordContextFooter',
            view: (modelElement, { writer: viewWriter }) => {
                const span = viewWriter.createEmptyElement('span', {
                    class: 'report-context-footer',
                });
                return span;
            },
        });
    }
}
