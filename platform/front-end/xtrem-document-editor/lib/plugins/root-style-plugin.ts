import { Plugin } from '@ckeditor/ckeditor5-core';

export default class RootStylePlugin extends Plugin {
    static get requires(): Array<typeof Plugin> {
        return [];
    }

    init(): void {
        const schema = this.editor.model.schema;
        schema.extend('$root', {
            allowAttributes: ['paddingLeft', 'paddingRight'],
        });

        this.editor.conversion.for('editingDowncast').attributeToAttribute({
            model: {
                name: '$root',
                key: 'paddingLeft',
            },
            view: modelAttributeValue => {
                return {
                    key: 'style',
                    value: {
                        'padding-left': modelAttributeValue,
                    },
                };
            },
        });
    }
}
