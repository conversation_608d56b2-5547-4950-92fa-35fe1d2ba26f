import type { Editor } from '@ckeditor/ckeditor5-core';
import type { DocumentFragment } from '@ckeditor/ckeditor5-engine';
import { Element } from '@ckeditor/ckeditor5-engine';
import type { Dict, FilterProperty } from '@sage/xtrem-shared';
import {
    ATTR_CONTEXT_CONDITION,
    ATTR_CONTEXT_FILTER,
    ATTR_CONTEXT_LIST_ORDER,
    ATTR_CONTEXT_OBJECT_TYPE,
    ATTR_FOOTER_GROUP,
    ATTR_PROPERTY_DATA_FORMAT,
    ATTR_PROPERTY_DATA_TYPE,
} from '@sage/xtrem-shared';
import type { ConditionEditorProperty, Order, TableContentWithGroupsProps } from '@sage/xtrem-ui-components';
import type { DataModelProperty, DataModelPropertyType } from '../context-provider';

export const getSelectedPropertyDataTypeAndFormat = (
    editor: Editor,
): { dataType: DataModelPropertyType | null; format: string | number | null } | null => {
    const model = editor.model;
    const selection = model.document.selection;
    const isProperty = model.schema.checkChild(selection.focus?.parent as any, 'property');
    if (!isProperty) {
        return null;
    }

    const selectedElement = [...selection.getRanges()][0]?.getContainedElement();
    if (!selectedElement) {
        return null;
    }

    const dataType = (selectedElement.getAttribute(ATTR_PROPERTY_DATA_TYPE) as DataModelPropertyType) || null;
    const format = (selectedElement.getAttribute(ATTR_PROPERTY_DATA_FORMAT) as DataModelPropertyType) || null;
    return { dataType, format };
};

export const getSelectedPropertyFormat = (editor: Editor): string | number | null => {
    const model = editor.model;
    const selection = model.document.selection;
    const isProperty = model.schema.checkChild(selection.focus?.parent as any, 'property');
    if (!isProperty) {
        return null;
    }

    const selectedElement = [...selection.getRanges()][0]?.getContainedElement();
    if (!selectedElement) {
        return null;
    }

    return (selectedElement.getAttribute(ATTR_PROPERTY_DATA_TYPE) as DataModelPropertyType) || null;
};

export function isTableElement(element: Element): boolean {
    const table = element.findAncestor('queryTable');
    return Boolean(table);
}

export function isDataContainer(element: Element): boolean {
    const recordContext = element.findAncestor('recordContext');
    return Boolean(recordContext);
}

export function getContextObjectType(element?: Element | DocumentFragment): string | null {
    const contextObjectType = (element as Element)?.getAttribute(ATTR_CONTEXT_OBJECT_TYPE);
    if (contextObjectType) {
        return String(contextObjectType);
    }
    if (element?.parent) {
        return getContextObjectType(element.parent);
    }

    return '$root';
}

export function getContextSortOrder(element?: Element | DocumentFragment): Dict<Order> | null {
    const orderByAttribute = (element as Element)?.getAttribute(ATTR_CONTEXT_LIST_ORDER);
    if (orderByAttribute) {
        return JSON.parse(String(orderByAttribute));
    }

    if (element?.parent) {
        return getContextSortOrder(element.parent);
    }

    return null;
}

export function getContextFilter(element?: Element | DocumentFragment): Array<FilterProperty> | null {
    const filtersAttribute = (element as Element)?.getAttribute(ATTR_CONTEXT_FILTER);
    if (filtersAttribute) {
        return JSON.parse(String(filtersAttribute));
    }

    if (element?.parent) {
        return getContextFilter(element.parent);
    }

    return null;
}

export function getContextCondition(element?: Element | DocumentFragment): ConditionEditorProperty[] | null {
    const conditionAttribute = (element as Element)?.getAttribute(ATTR_CONTEXT_CONDITION);
    if (conditionAttribute) {
        return JSON.parse(String(conditionAttribute));
    }

    if (element?.parent) {
        return getContextCondition(element.parent);
    }

    return null;
}

export function findChildElementsByName(
    elementName: string,
    parentElement: Element,
    collector: Element[] = [],
): Element[] {
    const children = [...(parentElement?.getChildren?.() || [])];
    children.forEach(c => {
        if (c instanceof Element) {
            if (c.is('element', elementName)) {
                collector.push(c);
            } else {
                findChildElementsByName(elementName, c, collector);
            }
        }
    });

    return collector;
}

export function getSelectedItemsFromProperties(
    properties: DataModelProperty[] | null,
): NonNullable<TableContentWithGroupsProps['selectedItems']> {
    return (properties ?? []).reduce<NonNullable<TableContentWithGroupsProps['selectedItems']>>((acc, item) => {
        acc[item.name] = {
            ...item,
            id: item.name,
            data: item,
            key: item.name,
            label: item.label,
            labelKey: item.name,
            labelPath: item.name,
            node: item.node,
        } as NonNullable<TableContentWithGroupsProps['selectedItems']>[number];
        return acc;
    }, {});
}

export function generateQueryAlias(): string {
    if (window.navigator.webdriver) {
        // In automation we need to hard code this so the generated alias is predictable
        return 'TEST_ALIAS';
    }
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < 8) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
        counter += 1;
    }
    return result;
}

export function getFooterGroup(element?: Element | DocumentFragment): any | null {
    const footerGroupAttribute = (element as Element)?.getAttribute(ATTR_FOOTER_GROUP);
    if (footerGroupAttribute) {
        return footerGroupAttribute;
    }

    if (element?.parent) {
        return getFooterGroup(element.parent);
    }

    return null;
}
