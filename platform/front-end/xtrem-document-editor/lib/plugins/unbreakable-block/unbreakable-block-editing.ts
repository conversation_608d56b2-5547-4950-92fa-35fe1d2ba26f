import { Plugin } from '@ckeditor/ckeditor5-core';
import { Widget, toWidget, toWidgetEditable } from '@ckeditor/ckeditor5-widget';
import { COMMAND_INSERT_UNBREAKABLE_BLOCK } from '../../constants';
import InsertUnbreakableBlockCommand from './insert-unbreakable-block-command';

export default class UnbreakableBlockEditing extends Plugin {
    static get requires(): Array<typeof Plugin> {
        return [Widget];
    }

    init(): void {
        this._defineSchema();
        this._defineConverters();
        this.editor.commands.add(COMMAND_INSERT_UNBREAKABLE_BLOCK, new InsertUnbreakableBlockCommand(this.editor));
    }

    _defineSchema(): void {
        const schema = this.editor.model.schema;
        schema.register('unbreakableBlock', {
            isLimit: true,
            inheritAllFrom: '$blockObject',
            allowContentOf: 'unbreakableBlockBody',
        });

        schema.register('unbreakableBlockBody', {
            isLimit: true,
            allowIn: 'unbreakableBlock',
            allowContentOf: '$root',
        });
    }

    _defineConverters(): void {
        const conversion = this.editor.conversion;

        conversion.for('upcast').elementToElement({
            model: 'unbreakableBlock',
            view: {
                name: 'section',
                classes: 'unbreakable-block',
            },
        });
        conversion.for('dataDowncast').elementToElement({
            model: 'unbreakableBlock',
            view: {
                name: 'section',
                classes: 'unbreakable-block',
            },
        });
        conversion.for('editingDowncast').elementToElement({
            model: 'unbreakableBlock',
            view: (_modelElement, { writer: viewWriter }) => {
                const section = viewWriter.createContainerElement('section', { class: 'unbreakable-block' });

                return toWidget(section, viewWriter, { label: 'unbreakable block widget' });
            },
        });

        conversion.for('upcast').elementToElement({
            model: 'unbreakableBlockBody',
            view: {
                name: 'div',
                classes: 'unbreakable-block-body',
            },
        });
        conversion.for('dataDowncast').elementToElement({
            model: 'unbreakableBlockBody',
            view: {
                name: 'div',
                classes: 'unbreakable-block-body',
            },
        });
        conversion.for('editingDowncast').elementToElement({
            model: 'unbreakableBlockBody',
            view: (_modelElement, { writer: viewWriter }) => {
                const div = viewWriter.createEditableElement('div', { class: 'unbreakable-block-body' });

                return toWidgetEditable(div, viewWriter);
            },
        });
    }
}
