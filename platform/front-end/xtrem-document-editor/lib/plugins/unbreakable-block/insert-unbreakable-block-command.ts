// unbreakablebox/insertunbreakableboxcommand.js

import { Command } from '@ckeditor/ckeditor5-core';
import type { Element, Writer } from '@ckeditor/ckeditor5-engine';

export default class InsertUnbreakableBlockCommand extends Command {
    execute(): void {
        this.editor.model.change(writer => {
            this.editor.model.insertObject(this._createUnbreakableBlock(writer));
        });
    }

    refresh(): void {
        const model = this.editor.model;
        const selection = model.document.selection;
        const position = selection.getFirstPosition();
        if (position === null) {
            return;
        }
        const allowedIn = model.schema.findAllowedParent(position, 'unbreakableBlock');

        this.isEnabled = allowedIn !== null;
    }

    private _createUnbreakableBlock(writer: Writer): Element {
        const unbreakableBlock = writer.createElement('unbreakableBlock');
        const unbreakableBlockBody = writer.createElement('unbreakableBlockBody');
        writer.append(unbreakableBlockBody, unbreakableBlock);
        writer.appendElement('paragraph', unbreakableBlockBody);

        return unbreakableBlock;
    }
}
