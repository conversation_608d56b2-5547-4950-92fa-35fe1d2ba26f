import * as React from 'react';
import { Plugin } from '@ckeditor/ckeditor5-core';
import { ButtonView } from '@ckeditor/ckeditor5-ui';
import { COMMAND_INSERT_UNBREAKABLE_BLOCK } from '../../constants';
import type { Locale } from '@ckeditor/ckeditor5-utils';
import type { ReportEditorConfig } from '../../report-editor-config-type';
import { renderToStaticMarkup } from 'react-dom/server';

export default class UnbreakableBlockUI extends Plugin {
    init(): void {
        const editor = this.editor;

        editor.ui.componentFactory.add('unbreakableBlock', (locale: Locale) => {
            // The state of the button will be bound to the widget command.
            const command = editor.commands.get(COMMAND_INSERT_UNBREAKABLE_BLOCK);

            // The button will be an instance of ButtonView.
            const buttonView = new ButtonView(locale);
            const { localize } = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;
            buttonView.set({
                label: localize(
                    '@sage/xtrem-document-editor/insert-unbreakable-container',
                    'Insert unbreakable container',
                ),
                icon: renderToStaticMarkup(
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M 15.066406 1.238281 C 15.773438 1.238281 16.433594 1.417969 17.042969 1.769531 C 17.648438 2.125 18.132812 2.601562 18.484375 3.210938 C 18.839844 3.816406 19.015625 4.472656 19.015625 5.179688 L 19.015625 15.058594 C 19.015625 15.78125 18.839844 16.441406 18.484375 17.039062 C 18.132812 17.640625 17.648438 18.121094 17.042969 18.480469 C 16.433594 18.839844 15.773438 19.019531 15.066406 19.019531 L 5.15625 19.019531 C 4.433594 19.019531 3.769531 18.839844 3.167969 18.480469 C 2.566406 18.121094 2.085938 17.640625 1.726562 17.039062 C 1.363281 16.441406 1.183594 15.78125 1.183594 15.058594 L 1.183594 5.179688 C 1.183594 4.472656 1.363281 3.816406 1.726562 3.210938 C 2.085938 2.601562 2.566406 2.125 3.167969 1.769531 C 3.769531 1.417969 4.433594 1.238281 5.15625 1.238281 Z M 10.109375 6.179688 C 9.386719 6.179688 8.722656 6.355469 8.113281 6.710938 C 7.503906 7.0625 7.023438 7.542969 6.671875 8.148438 C 6.316406 8.757812 6.136719 9.417969 6.136719 10.128906 C 6.136719 10.84375 6.316406 11.503906 6.671875 12.109375 C 7.023438 12.714844 7.503906 13.195312 8.113281 13.550781 C 8.722656 13.902344 9.386719 14.078125 10.101562 14.078125 C 10.816406 14.078125 11.476562 13.902344 12.085938 13.550781 C 12.695312 13.195312 13.175781 12.714844 13.53125 12.109375 C 13.886719 11.503906 14.0625 10.84375 14.0625 10.128906 C 14.0625 9.417969 13.886719 8.757812 13.53125 8.148438 C 13.175781 7.542969 12.695312 7.0625 12.085938 6.710938 C 11.476562 6.355469 10.820312 6.179688 10.109375 6.179688 Z M 10.109375 6.179688"
                            fill="#000000e6"
                        />
                    </svg>,
                ),
                isToggleable: true,
                tooltip: true,
            });

            // Bind the state of the button to the command.
            buttonView.bind('isOn', 'isEnabled').to(command as any, 'value', 'isEnabled');

            // Execute the command when the button is clicked (executed).
            this.listenTo(buttonView, 'execute', () => editor.execute(COMMAND_INSERT_UNBREAKABLE_BLOCK));

            return buttonView;
        });
    }
}
