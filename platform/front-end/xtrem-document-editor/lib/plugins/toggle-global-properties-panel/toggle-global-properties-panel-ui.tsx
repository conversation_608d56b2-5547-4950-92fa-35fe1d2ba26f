import * as React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import { ButtonView } from '@ckeditor/ckeditor5-ui';
import type { Locale } from '@ckeditor/ckeditor5-utils';
import { COMMAND_TOGGLE_GLOBAL_PROPERTIES } from '../../constants';
import TogglePluginUi from '../toggle-plugin-ui';
import type { ReportEditorConfig } from '../../report-editor-config-type';
import ToggleDataPropertiesPanelUi from '../toggle-data-properties-panel/toggle-data-properties-panel-ui';

export default class ToggleGlobalPropertiesPanelUi extends TogglePluginUi {
    public static get pluginName(): string {
        return 'ToggleGlobalPropertiesPanelUi' as const;
    }

    public togglePanel = (): void => {
        const newState = !this.view.isOn;
        this.view.set({ isOn: newState });
        const reportEditorConfig = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;
        reportEditorConfig.onGlobalPropertiesPanelOpenChange(newState);

        if (newState) {
            const globalPropertiesPlugin = this.editor.plugins.get(
                ToggleDataPropertiesPanelUi.pluginName,
            ) as ToggleDataPropertiesPanelUi;
            globalPropertiesPlugin.view.set({ isOn: false });
        }
    };

    public init(): void {
        const editor = this.editor;

        // Add bold button to feature components.
        editor.ui.componentFactory.add(COMMAND_TOGGLE_GLOBAL_PROPERTIES, (locale: Locale) => {
            this.view = new ButtonView(locale);
            const { localize } = this.editor.config.get('reportEditorConfig') as ReportEditorConfig;

            this.view.set({
                label: localize(
                    '@sage/xtrem-document-editor/toggle-global-properties-panel',
                    'Show/Hide document properties panel',
                ),
                icon: renderToStaticMarkup(
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M8.3 11H0.7C0.3 11 0 11.3 0 11.7V16.8C0 18 1 19 2.2 19H6.7C8 19 9 18 9 16.8V11.7C9 11.3 8.7 11 8.3 11ZM6 14H3C2.7 14 2.5 13.8 2.5 13.5C2.5 13.2 2.7 13 3 13H6C6.3 13 6.5 13.2 6.5 13.5C6.5 13.8 6.3 14 6 14ZM19.3 11H11.7C11.3 11 11 11.3 11 11.7V16.8C11 18 12 19 13.2 19H17.7C19 19 20 18 20 16.8V11.7C20 11.3 19.7 11 19.3 11ZM17 14H14C13.7 14 13.5 13.8 13.5 13.5C13.5 13.2 13.7 13 14 13H17C17.3 13 17.5 13.2 17.5 13.5C17.5 13.8 17.3 14 17 14ZM7.2 9H11.7C13 9 14 8 14 6.8V1.7C14 1.3 13.7 1 13.3 1H5.7C5.3 1 5 1.3 5 1.7V6.8C5 8 6 9 7.2 9ZM8 3H11C11.3 3 11.5 3.2 11.5 3.5C11.5 3.8 11.3 4 11 4H8C7.7 4 7.5 3.8 7.5 3.5C7.5 3.2 7.7 3 8 3Z"
                            fill="#000000e6"
                        />
                    </svg>,
                ),
                tooltip: true,
                isToggleable: true,
                isEnabled: true,
            });

            // Execute command.
            this.listenTo(this.view, 'execute', this.togglePanel);

            return this.view;
        });
    }
}
