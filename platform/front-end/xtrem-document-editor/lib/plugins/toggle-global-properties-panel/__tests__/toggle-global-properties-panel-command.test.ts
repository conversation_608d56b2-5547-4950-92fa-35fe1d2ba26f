import { getPage, getTextContent, openGlobalPropertiesPanel } from '../../../__tests__/test-helper';

describe('toggle global properties command', () => {
    it('should toggle global properties panel', async () => {
        const page = getPage();
        await openGlobalPropertiesPanel();
        await page.waitForSelector('.document-editor-left-panel');
        const content = await getTextContent('.document-editor-panel-context-indicator');
        expect(content).toEqual('Document properties');
    });
});
