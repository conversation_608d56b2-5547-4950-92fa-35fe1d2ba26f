import { Plugin } from '@ckeditor/ckeditor5-core';
import ToggleGlobalPropertiesPanelUi from './toggle-global-properties-panel-ui';
import ToggleGlobalPropertiesPanelEditing from './toggle-global-properties-panel-editing';

export default class ToggleGlobalPropertiesPanel extends Plugin {
    static get requires(): Array<typeof Plugin> {
        return [ToggleGlobalPropertiesPanelUi, ToggleGlobalPropertiesPanelEditing];
    }
}
