import { Plugin } from '@ckeditor/ckeditor5-core';
import { COMMAND_TOGGLE_GLOBAL_PROPERTIES } from '../../constants';
import ToggleGlobalPropertiesPanelCommand from './toggle-global-properties-panel-command';

export default class ToggleGlobalPropertiesPanelEditing extends Plugin {
    public static get pluginName(): string {
        return 'ToggleGlobalPropertiesPanel';
    }

    public init(): void {
        this.editor.commands.add(COMMAND_TOGGLE_GLOBAL_PROPERTIES, new ToggleGlobalPropertiesPanelCommand(this.editor));
    }
}
