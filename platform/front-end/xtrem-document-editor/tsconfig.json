{"extends": "../tsconfig", "compilerOptions": {"plugins": [{"import": "tsPatchMessageTransformer", "transform": "../../cli/xtrem-cli-transformers/build/lib/transformers/message-transformer.js", "before": true}], "baseUrl": ".", "outDir": "build", "rootDir": ".", "paths": {"timers": ["node_modules/timers-browserify"], "stream": ["node_modules/stream-browserify"]}}, "include": ["index.ts", "./lib", "jest-svg-transformer.js"], "exclude": ["./lib/**/*.test.ts", "./lib/**/*.test.tsx", "./lib/**/__tests__/**/*", "./lib/__tests__/**/*", "./lib/**/__mocks__/**/*", "./lib/__mocks__/**/*"], "references": [{"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-filter-utils"}, {"path": "../../shared/xtrem-shared"}, {"path": "../xtrem-ui-components"}, {"path": "../../shared/xtrem-i18n"}, {"path": "../xtrem-static-shared"}]}