const { resolve } = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');

export const copyStaticResources = (targetDir: string) =>
    new CopyWebpackPlugin({
        patterns: [
            {
                from: resolve(__dirname, '..', '..', 'static', 'fonts'),
                to: resolve(targetDir, 'fonts'),
            },
            {
                from: resolve(__dirname, '..', '..', 'static', 'images'),
                to: resolve(targetDir, 'images'),
            },
        ],
    });
