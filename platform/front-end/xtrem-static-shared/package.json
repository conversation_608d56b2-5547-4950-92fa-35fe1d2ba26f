{"name": "@sage/xtrem-static-shared", "version": "58.0.2", "description": "Sage static shared resources used by various front-end packages", "main": "build/lib/index.js", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "dependencies": {"copy-webpack-plugin": "^13.0.0"}, "scripts": {"build": "tsc -b -v .", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "test": "echo \"nothing to test here\"", "test:ci": "echo \"nothing to test here\""}, "author": "<EMAIL>", "license": "UNLICENSED"}