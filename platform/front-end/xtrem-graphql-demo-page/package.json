{"name": "@sage/xtrem-graphql-demo-page", "version": "58.0.2", "description": "Sage branded graphiql page", "main": "build/bundle.js", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "scripts": {"build": "tsc -b -v . && webpack --config ./webpack/prod.js", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "lint": "echo \"linting disabled on this package\"", "start": "webpack serve --config ./webpack/dev.js", "test": "echo \"Error: no test specified for xtrem-graphql-demo-page\" && exit 0", "test:ci": "echo \"Error: no test specified for xtrem-graphql-demo-page\" && exit 0"}, "author": "<EMAIL>", "license": "UNLICENSED", "dependencies": {"@codemirror/language": "6.11.2", "@sage/design-tokens": "4.35.0", "axios": "^1.11.0", "graphiql": "^3.0.6", "graphql": "16.1.0-experimental-stream-defer.6", "react": "^18.3.1", "react-dom": "^18.3.1", "url-search-params-polyfill": "^8.0.0"}, "devDependencies": {"@sage/xtrem-static-shared": "workspace:*", "@types/react": "^18.3.3", "@types/react-dom": "^18.0.0", "copy-webpack-plugin": "^13.0.0", "css-loader": "^6.8.1", "graphql-ws": "^6.0.0", "mini-css-extract-plugin": "^2.0.0", "style-loader": "^4.0.0", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.8.3", "webpack": "^5.95.0", "webpack-cli": "^6.0.0", "webpack-dev-server": "^5.0.0", "webpack-modules": "^1.0.0"}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}