{"compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": ".", "module": "esnext", "target": "es2022", "lib": ["es2022", "dom"], "sourceMap": true, "jsx": "react", "moduleResolution": "node", "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": true, "ignoreDeprecations": "5.0", "allowSyntheticDefaultImports": true, "noUnusedLocals": true, "declaration": true, "composite": true}, "include": ["lib", "lib/*.tsx"], "references": [{"path": "../xtrem-static-shared"}]}