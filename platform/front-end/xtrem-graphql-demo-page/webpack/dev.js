const baseConfig = require('./base.js');
const path = require('path');
const rootDir = path.resolve(__dirname, '..');
const buildDir = path.resolve(rootDir, 'build');
const Webpack = require('webpack');

baseConfig.mode = 'development';
baseConfig.devtool = 'source-map';
baseConfig.devServer = {
    contentBase: buildDir,
    compress: true,
    publicPath: '/',
    port: 4000,
    host: '0.0.0.0',
    historyApiFallback: {
        index: '/',
    },
    proxy: [
        {
            context: ['/explorer', '/api'],
            target: 'http://localhost:8240',
        },
    ],
};
baseConfig.devtool = 'source-map';

baseConfig.plugins.push(
    new Webpack.DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify('development'),
        DEV_MODE: true,
    }),
);

module.exports = baseConfig;
