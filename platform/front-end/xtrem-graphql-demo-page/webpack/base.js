const path = require('path');
const Webpack = require('webpack');
const rootDir = path.resolve(__dirname, '..');
const buildDir = path.resolve(rootDir, 'build');
const timestamp = new Date().toISOString();
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const WebpackModules = require('webpack-modules');
const { copyStaticResources } = require('@sage/xtrem-static-shared');
const CopyWebpackPlugin = require('copy-webpack-plugin');

console.log('Build target dir: ', buildDir);

module.exports = {
    target: 'web',
    entry: [path.join(rootDir, 'lib/index.tsx')],
    output: {
        filename: 'bundle.js',
        path: buildDir,
    },

    devServer: {
        contentBase: buildDir,
        compress: true,
        publicPath: '/',
    },
    resolve: {
        symlinks: true,
        extensions: ['.ts', '.tsx', '.js', '.json', '.css'],
    },
    plugins: [
        new WebpackModules(),
        new MiniCssExtractPlugin({
            filename: 'xtrem-graphiql-style.css',
        }),
        new Webpack.BannerPlugin({
            banner: 'Built on ' + timestamp,
        }),
        new Webpack.BannerPlugin({
            banner: `window.build = '${process.env.BUILD_NUMBER ? 'b ' + process.env.BUILD_NUMBER : 'dev'}';`,
            raw: true,
            include: 'bundle.js',
        }),
        new Webpack.ContextReplacementPlugin(
            /graphql-language-service-interface[\\/]dist$/,
            new RegExp(`^\\./.*\\.js$`),
        ),
        copyStaticResources(buildDir),
        new CopyWebpackPlugin({
            patterns: [
                {
                    from: path.resolve(rootDir + '/dev-resources'),
                    to: buildDir,
                },
            ],
        }),
    ],
    module: {
        rules: [
            {
                test: /\.m?js/,
                resolve: {
                    fullySpecified: false,
                },
            },
            {
                test: /\.tsx?$/,
                loader: 'ts-loader',
                options: {
                    transpileOnly: true,
                },
            },
            {
                test: /\.css$/,
                use: [
                    'style-loader',
                    {
                        loader: 'css-loader',
                        options: {
                            url: {
                                // Exclude URLs starting with '/absolute/path'
                                filter: url => !url.startsWith('/'),
                            },
                        },
                    },
                ],
            },
            {
                test: /\.(png|woff|woff2|eot|ttf|svg)$/,
                loader: 'url-loader',
                options: {
                    limit: 100000,
                },
            },
            // All output '.js' files will have any sourcemaps re-processed by 'source-map-loader'.
            {
                enforce: 'pre',
                exclude: [/node_modules/],
                test: /\.js$ /,
                loader: 'source-map-loader',
            },
        ],
    },
};
