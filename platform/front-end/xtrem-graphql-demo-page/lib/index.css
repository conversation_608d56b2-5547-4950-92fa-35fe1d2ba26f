@font-face {
    font-family: 'Sage UI';
    src:
        url('https://fonts.sage.com/Sage_UI-Regular.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_UI-Regular.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* Sage Text font family */
@font-face {
    font-family: 'Sage Text';
    src:
        url('https://fonts.sage.com/Sage_Text-Light.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_Text-Light.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Sage Text';
    src:
        url('https://fonts.sage.com/Sage_Text-Light_Italic.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_Text-Light_Italic.woff') format('woff');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Sage Text';
    src:
        url('https://fonts.sage.com/Sage_Text-Regular.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_Text-Regular.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Sage Text';
    src:
        url('https://fonts.sage.com/Sage_Text-Italic.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_Text-Italic.woff') format('woff');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Sage Text';
    src:
        url('https://fonts.sage.com/Sage_Text-Medium.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_Text-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Sage Text';
    src:
        url('https://fonts.sage.com/Sage_Text-Medium_Italic.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_Text-Medium_Italic.woff') format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Sage Text';
    src:
        url('https://fonts.sage.com/Sage_Text-Bold.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_Text-Bold.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Sage Text';
    src:
        url('https://fonts.sage.com/Sage_Text-Bold_Italic.woff2') format('woff2'),
        url('https://fonts.sage.com/Sage_Text-Bold_Italic.woff') format('woff');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'CarbonIcons';
    src: url('/fonts/carbon-icons-webfont.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

body,
html,
#app-body {
    padding: 0;
    margin: 0;
    font-family: 'Sage UI', sans-serif;
    background: #f2f5f6;
    height: 100%;
}

#app-body {
    width: 100%;
    height: 100%;
}

.graphiql-container .docExplorerWrap,
.graphiql-container .historyPaneWrap {
    background: #f2f5f6;
    border-right: 1px solid #ccd6db;
    border-left: 1px solid #ccd6db;
}

.graphiql-container .doc-explorer-contents,
.graphiql-container .history-contents {
    background: #f2f5f6;
}

.graphiql-container,
.graphiql-container button,
.graphiql-container input {
    font-family: 'Sage UI', sans-serif;
}

.graphiql-container .doc-explorer-title,
.graphiql-container .history-title {
    font-size: 18px;
    text-align: left;
    font-family: 'Sage UI', sans-serif;
    color: var(--colorsYang100);
}

.graphiql-container .variable-editor-title,
.graphiql-container .doc-category-title {
    font-variant: none;
    text-transform: capitalize;
    font-family: 'Sage UI', sans-serif;
    color: var(--colorsYin090);
}

.graphiql-container .doc-explorer-title-bar,
.graphiql-container .history-title-bar {
    background: var(--colorsYin090);
    color: var(--colorsYang100);
    height: 35px;
    z-index: 1;
}

.graphiql-container .docExplorerHide {
    color: var(--colorsYang100);
}

.graphiql-container .title {
    white-space: nowrap;
    height: 48px;
    vertical-align: middle;
}

.logo {
    padding-left: 40px;
    padding-top: 14px;
    padding-right: 24px;
    padding-bottom: 12px;
    width: 40px;
    vertical-align: middle;
}

.product-name {
    font-family: 'Sage Text', sans-serif;
    font-size: 18px;
    font-weight: 500;
    color: #3b4b68c2;
    padding: 4px 4px 4px 24px;
    display: inline-block;
    border-left: 1px solid var(--colorsUtilityMajor200);
}

.topBarWrap {
    background: #000000;
    color: var(--colorsActionMajor500);
    border: none;
}

.graphiql-container .topBar {
    background: transparent;
    border: none;
    padding: 0;
    height: 48px;
}

.graphiql-container .result-window .CodeMirror.cm-s-graphiql {
    background: var(--colorsActionMinor050);
}

.graphiql-container .CodeMirror.cm-s-graphiql {
    background: var(--colorsActionMinor025);
}

.graphiql-container .topBarWrap {
    padding: 0;
    box-shadow: 0px 1px 16px 0px rgba(0, 0, 0, 0.75);
    z-index: 20;
}

.graphiql-container .CodeMirror-gutters {
    border-right: 1px solid #ccd6db;
    padding-right: 2px;
    background: #e5eaec;
}

.graphiql-container .CodeMirror-foldgutter,
.graphiql-container .variable-editor-title {
    background: #e5eaec;
    border-color: #ccd6db;
}

.graphiql-container .CodeMirror-foldmarker {
    background: var(--colorsActionMajor500);
}

.graphiql-container .CodeMirror-linenumber {
    color: #003349;
}

.graphiql-container .cm-property {
    color: var(--colorsYin090);
}

.graphiql-container .arg-name {
    color: var(--colorsActionMinor550);
}

.graphiql-container .field-short-description {
    color: rgba(0, 0, 0, 0.3);
}

.graphiql-container .cm-punctuation {
    color: #99adb6;
}

.graphiql-container .doc-explorer-contents .field-name {
    color: var(--colorsActionMajor500);
}

.graphiql-container .doc-explorer-contents .field-name:hover {
    color: var(--colorsActionMajor600);
}

.graphiql-container .doc-explorer-contents .field-name:focus {
    outline: 2px solid var(--colorsSemanticFocus500);
}

.graphiql-container .cm-invalidchar,
.graphiql-container .cm-keyword {
    color: #c7384f;
    font-weight: bold;
}

.graphiql-container .cm-string {
    color: var(--colorsSemanticNegative600);
}

.graphiql-container .cm-number {
    color: var(--colorsActionMajor500);
    font-weight: bold;
}

.graphiql-container .CodeMirror-hint-active {
    background: var(--colorsActionMajor500);
}

.graphiql-container .CodeMirror-hint-information .infoType {
    color: var(--colorsSemanticFocus500);
    font-weight: bold;
}

.graphiql-container .toolbar {
    display: flex;
    flex-direction: row-reverse;
}

.graphiql-container .toolbar-button,
.graphiql-container .execute-button,
.graphiql-container .docExplorerShow {
    text-align: center;
    cursor: pointer;
    display: inline-block;
    font-family: 'Sage UI', sans-serif;
    font-size: 14px;
    font-weight: 600;
    height: 28px;
    line-height: 24px;
    padding: 0px 16px;
    background: var(--colorsActionMajor500);
    color: var(--colorsYang100);
    border-radius: 0;
    margin: 0 10px;
    box-shadow: none;
    border: var(--colorsActionMajor500);
}

.graphiql-container .toolbar-button.error,
.graphiql-container .execute-button.error {
    background: var(--colorsSemanticNegative500);
    color: var(--colorsYang100);
}

.graphiql-container .toolbar-button.error:hover,
.graphiql-container .execute-button.error:hover {
    background: var(--colorsSemanticNegative600);
    color: var(--colorsYang100);
}

.graphiql-container .toolbar-button:focus,
.graphiql-container .execute-button:focus,
.graphiql-container .docExplorerShow:focus {
    outline: 2px solid var(--colorsSemanticFocus500);
}

.graphiql-container .toolbar-button:hover,
.graphiql-container .execute-button:hover,
.graphiql-container .docExplorerShow:hover {
    background: var(--colorsActionMajor600);
}

.graphiql-container .execute-button,
.graphiql-container .docExplorerShow {
    width: 92px;
    height: 28px;
}

.graphiql-container .docExplorerShow {
    margin-top: 10px;
}

.graphiql-container .doc-explorer-back {
    color: var(--colorsYang100);
}

.graphiql-container .doc-explorer-back::before {
    border-color: var(--colorsYang100);
}

.graphiql-container .execute-button-wrap {
    margin-right: 0;
}

.graphiql-container .execute-button {
    background: var(--colorsActionMajor500);
    color: var(--colorsYang100);
    box-shadow: var(--boxShadow100);
    border-radius: 0;
    position: relative;
    padding-left: 36px;
    margin-top: 3px;
}

.graphiql-container .execute-button:hover {
    background: var(--colorsActionMajor600);
}

.graphiql-container .execute-button::before {
    content: 'Run';
}

.graphiql-container .execute-button::after {
    -webkit-font-smoothing: antialiased;
    font-family: CarbonIcons, sans-serif;
    content: '';
    font-size: 16px;
    font-style: normal;
    font-weight: normal;
    line-height: 28px;
    display: block;
    position: absolute;
    top: 0;
    left: 16px;
}

.graphiql-container .execute-button:focus {
    outline: none;
}

.graphiql-container .execute-button svg {
    display: none;
}

.graphiql-container .docExplorerShow::before {
    display: none;
}

.graphiql-container .execute-button path {
    fill: var(--colorsYang100);
}

.graphiql-container .history-contents li:hover {
    background: var(--colorsActionMajor600);
}

.graphiql-container .execute-button-wrap {
    flex: 1;
    text-align: right;
}

.graphiql-container .search-box {
    padding-left: 10px;
    z-index: 1;
    border: 1px solid #668592;
    outline: 0px;
    width: calc(100% - 50px);
    margin-top: 0;
    background: #fff;
}

.graphiql-container .cm-builtin,
.doc-category-item .type-name {
    color: var(--colorsSemanticCaution500);
    font-weight: bold;
}

.beta-sticker {
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 14px;
    font-weight: normal;
    color: #fff;
    line-height: 1;
    vertical-align: middle;
    white-space: nowrap;
    text-align: center;
    background-color: #bbb;
    border-radius: 10px;
    margin-left: 8px;
    margin-bottom: 1px;
}
