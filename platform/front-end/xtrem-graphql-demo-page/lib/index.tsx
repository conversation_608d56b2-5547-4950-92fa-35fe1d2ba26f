import axios from 'axios';
import GraphiQL from 'graphiql';
import * as React from 'react';
import { createRoot } from 'react-dom/client';

import 'graphiql/graphiql.css';
import 'url-search-params-polyfill';
import './index.css';
import '@sage/design-tokens/css/base.css';

const defaultQuery = `# GraphiQL is an in-browser integrated development environment (IDE) for
# writing, validating, and testing GraphQL queries.
#
# You can use it here to explore and interact with the Sage XTreeM GraphQL API
# and demo data provided.
#
# Type queries on this side of the screen. Typeahead support lists available
# choices based on the text entered.  GraphiQL also highlights syntax and
# validation errors in the code.
#
# GraphQL queries typically start with a { character.
# Comment lines starting with a # are ignored.
#
# Keyboard shortcuts:
#     Prettify a query:  Shift + Ctrl + P (or select the Prettify button above)
#     Run a query:  Ctrl + Enter (or select the Run button above)
#     Auto complete:  Ctrl + Space (or start typing)
#
`;

const graphQLFetcher = (graphQLParams: any) => {
    const targetApi = window.location.pathname.endsWith('/metadata/') ? 'metadata' : 'api';
    return axios
        .post(window.location.pathname + `/../../${targetApi}`, graphQLParams, {
            headers: { 'xtrem-graphql-fetcher': 'graphiql' },
        })
        .then(response => response.data)
        .catch(err => {
            if (err.response) {
                return err.response.data;
            } else {
                return 'The server seems to be down, please try again later.';
            }
        });
};

const urlParams = new URLSearchParams(window.location.search);
const currentQuery = urlParams.get('query');

createRoot(document.getElementById('app-body') as HTMLElement).render(
    <GraphiQL
        fetcher={graphQLFetcher}
        defaultQuery={defaultQuery}
        query={currentQuery || defaultQuery}
        variables={urlParams.get('variables') || undefined}
    >
        <GraphiQL.Logo>
            <img className="logo" src="images/sage-logo.svg" alt="Sage" />
            <span className="product-name">GraphQL API sandbox</span>
        </GraphiQL.Logo>
    </GraphiQL>,
);
