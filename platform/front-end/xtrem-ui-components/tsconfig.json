{"extends": "../tsconfig", "compilerOptions": {"plugins": [{"import": "tsPatchMessageTransformer", "transform": "../../cli/xtrem-cli-transformers/build/lib/transformers/message-transformer.js", "before": true}], "sourceMap": true, "module": "CommonJS", "baseUrl": ".", "outDir": "build", "rootDir": ".", "paths": {"timers": ["node_modules/timers-browserify"], "stream": ["node_modules/stream-browserify"]}}, "include": ["index.ts", "./lib"], "exclude": ["./lib/**/*.test.ts", "./lib/**/*.test.tsx", "./lib/**/__tests__/**/*", "./lib/__tests__/**/*", "./lib/**/__mocks__/**/*", "./lib/__mocks__/**/*"], "references": [{"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-shared"}, {"path": "../../cli/xtrem-cli-transformers"}, {"path": "../xtrem-static-shared"}]}