import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { TreeInput } from '../lib/tree-input/tree-input';
import { fakeFetchItems } from '../lib/__tests__/common';
import Box from 'carbon-react/esm/components/box';
import { NodeDetails, TreeElement } from '@sage/xtrem-shared';

const DemoComponent = ({
    isDisabled,
    value: externalValue,
}: {
    isDisabled: boolean;
    value?: TreeElement<NodeDetails> | null;
}) => {
    const [value, setValue] = React.useState<TreeElement<NodeDetails> | null>(externalValue);
    return (
        <div>
            <Box p={8}>
                <h4>Component</h4>
                <TreeInput
                    fetchItems={fakeFetchItems}
                    localize={(_, l) => l}
                    node="SalesInvoice"
                    onChange={setValue}
                    isDisabled={isDisabled}
                    value={value}
                />
            </Box>
            <Box p={8}>
                <h4>Value</h4>
                <pre style={{ background: '#FFF' }}>{JSON.stringify(value, null, 4)}</pre>
            </Box>
        </div>
    );
};
const meta = {
    title: 'TreeInput',
    component: DemoComponent,
    parameters: {
        layout: 'fullscreen',
    },
} satisfies Meta<typeof DemoComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const TreeInputStory: Story = {
    args: {
        isDisabled: false,
    },
} satisfies Story;

export const TreeInputStoryWithValue: Story = {
    args: {
        isDisabled: false,
        value: {
            label: 'Invoice Number',
            id: 'invoiceNumber',
            key: 'invoiceNumber',
            labelKey: 'Invoice Number',
            labelPath: 'Invoice Number',
            data: {
                name: 'invoiceNumber',
                label: 'Invoice Number',
                kind: 'SCALAR',
                type: 'String',
                canFilter: true,
                canSort: true,
                enumType: null,
                isCustom: false,
                dataType: '',
                targetNode: '',
                isOnInputType: true,
                isOnOutputType: true,
                isMutable: false,
                node: 'String',
            },
        },
    },
} satisfies Story;

export const TreeInputStoryWithDeepValue: Story = {
    args: {
        isDisabled: false,
        value: {
            label: 'Credit Rating',
            id: 'customer.creditRating',
            key: 'customer.creditRating',
            labelKey: 'Credit Rating',
            labelPath: 'Customer > Credit Rating',
            data: {
                type: '@sage/xtrem-master-data/CreditRating',
                kind: 'ENUM',
                enumValues: ['great', 'good', 'ok', 'notBad', 'awful'],
                isCollection: false,
                name: 'creditRating',
                canFilter: true,
                canSort: true,
                label: 'Credit Rating',
                enumType: 'productCategoryEnumType',
                isCustom: false,
                dataType: 'productCategoryEnumDataType',
                targetNode: '',
                isOnInputType: true,
                isOnOutputType: true,
                isMutable: false,
                node: '@sage/xtrem-master-data/CreditRating',
            },
        },
    },
} satisfies Story;
