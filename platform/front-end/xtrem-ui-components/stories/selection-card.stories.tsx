import type { Meta, StoryObj } from '@storybook/react';
import { SelectionCard } from '../lib/selection-card/selection-card';

const meta = {
    title: 'Selection Card',
    component: SelectionCard,
    parameters: {
        layout: 'fullscreen',
    },
} satisfies Meta<typeof SelectionCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const SelectionCardStory: Story = {
    args: {
        _id: '1',
        title: 'Title',
        description: 'Description',
        icon: '/images/favicon.svg',
        onClick: () => console.log('Clicked'),
        isReadOnly: false,
        isSelected: false,
    },
};
