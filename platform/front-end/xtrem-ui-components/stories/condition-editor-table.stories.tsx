import type { Meta, StoryObj } from '@storybook/react';
import { ConditionEditorTableComponent } from '../lib/condition-editor-table/condition-editor-table';
import { fakeFetchItems, fakeParameters } from '../lib/__tests__/common';
import { startCase } from 'lodash';

const meta = {
    title: 'Condition editor table',
    component: ConditionEditorTableComponent,
    parameters: {
        layout: 'fullscreen',
    },
} satisfies Meta<typeof ConditionEditorTableComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const ConditionEditorTableWithParameters: Story = {
    args: {
        carbonLocale: () => ({}),
        locale: 'en-US',
        localizeEnumMember: (_, member) => startCase(member),
        localize: (_, v) => v,
        onChange: condition => {
            console.log(condition);
        },
        value: [],
        node: 'SalesInvoice',
        parameters: fakeParameters,
        fetchItems: fakeFetchItems,
        onValidityChange: isValid => console.log(`is valid: ${isValid}`),
    },
};

export const ConditionEditorTableWithoutParameters: Story = {
    args: {
        carbonLocale: () => ({}),
        locale: 'en-US',
        localizeEnumMember: (_, member) => startCase(member),
        localize: (_, v) => v,
        onChange: condition => {
            console.log(condition);
        },
        value: [],
        node: 'SalesInvoice',
        parameters: [],
        fetchItems: fakeFetchItems,
        onValidityChange: isValid => console.log(`is valid: ${isValid}`),
    },
};

export const ConditionEditorTableWithValue: Story = {
    args: {
        carbonLocale: () => ({}),
        locale: 'en-US',
        localizeEnumMember: (_, member) => startCase(member),
        localize: (_, v) => v,
        onChange: condition => {
            console.log(condition);
        },
        value: [
            {
                conjunction: 'and',
                valueType1: 'property',
                valueType2: 'parameter',
                _id: '2',
                value1: {
                    label: 'Invoice Number',
                    data: {
                        name: 'invoiceNumber',
                        label: 'Invoice Number',
                        kind: 'SCALAR',
                        type: 'String',
                        canFilter: true,
                        canSort: true,
                        enumType: null,
                        isCustom: false,
                        dataType: '',
                        dataTypeDetails: null,
                        targetNode: '',
                        targetNodeDetails: null,
                        isOnInputType: true,
                        isOnOutputType: true,
                        isMutable: false,
                        node: 'String',
                    },
                    id: 'invoiceNumber',
                    key: 'invoiceNumber',
                    labelKey: 'Invoice Number',
                    labelPath: 'Invoice Number',
                },
                value2: 'invoiceNumber',
                operator: 'equals',
            },
            {
                conjunction: 'and',
                valueType1: 'property',
                valueType2: 'constant',
                _id: '1',
                value1: {
                    label: 'Delivery Date',
                    data: {
                        name: 'deliveryDate',
                        label: 'Delivery Date',
                        kind: 'SCALAR',
                        type: 'Date',
                        canFilter: true,
                        canSort: true,
                        enumType: null,
                        isCustom: false,
                        dataType: '',
                        dataTypeDetails: null,
                        targetNode: '',
                        targetNodeDetails: null,
                        isOnInputType: true,
                        isOnOutputType: true,
                        isMutable: false,
                        node: 'Date',
                    },
                    id: 'deliveryDate',
                    key: 'deliveryDate',
                    labelKey: 'Delivery Date',
                    labelPath: 'Delivery Date',
                },
                value2: {
                    formattedValue: '11/06/2024',
                    rawValue: '2024-06-11',
                },
                operator: 'equals',
            },
        ],
        node: 'SalesInvoice',
        parameters: fakeParameters,
        fetchItems: fakeFetchItems,
        onValidityChange: isValid => console.log(`is valid: ${isValid}`),
    },
};
