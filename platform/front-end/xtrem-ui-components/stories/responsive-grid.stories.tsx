import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';

import { GridColumn, GridRow } from '../lib/responsive-grid/responsive-grid';

const DemoComponent = ({
    totalNumberOfColumns = 12,
    gutter = 8,
    margin = 16,
    verticalMargin = 16,
}: {
    totalNumberOfColumns: number;
    gutter: number;
    margin: number;
    verticalMargin: number;
}) => (
    <GridRow columns={totalNumberOfColumns} gutter={gutter} verticalMargin={verticalMargin} margin={margin}>
        <GridColumn>Column 1</GridColumn>
        <GridColumn>Column 2</GridColumn>
        <GridColumn>Column 3</GridColumn>
        <GridColumn>Column 4</GridColumn>
        <GridColumn>Column 5</GridColumn>
        <GridColumn>Column 6</GridColumn>
    </GridRow>
);
const meta = {
    title: 'Responsive Grid',
    component: DemoComponent,
    parameters: {
        layout: 'fullscreen',
    },
} satisfies Meta<typeof DemoComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const ResponsiveGrid: Story = {
    args: {
        totalNumberOfColumns: 12,
        gutter: 8,
        margin: 16,
        verticalMargin: 16,
    },
};
