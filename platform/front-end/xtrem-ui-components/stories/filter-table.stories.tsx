import type { Meta, StoryObj } from '@storybook/react';
import { FilterTableComponent } from '../lib/filter-table/filter-table';
import { fakeParameters, mockSalesInvoiceObject } from '../lib/__tests__/common';
import { startCase } from 'lodash';

const meta = {
    title: 'Filter table',
    component: FilterTableComponent,
    parameters: {
        layout: 'fullscreen',
    },
} satisfies Meta<typeof FilterTableComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const FilterTable: Story = {
    args: {
        carbonLocale: () => ({}),
        locale: 'en-US',
        localizeEnumMember: (_, member) => startCase(member),
        localize: (_, v) => v,
        onChange: condition => {
            console.log(condition);
        },
        value: [],
        node: 'SalesInvoice',
        parameters: fakeParameters,
        selectedProperties: mockSalesInvoiceObject.reduce((acc, curr) => {
            acc[curr.name] = {
                label: curr.label,
                id: curr.name,
                key: curr.name,
                path: curr.name,
                labelPath: curr.label,
                data: curr,
            };
            return acc;
        }, {}),
        nodeNames: {
            SalesInvoice: 'Sales Invoice',
            SalesOrder: 'Sales Order',
        },
        automaticColumnsSpacing: true,
        isDisabled: false,
        parameterMode: 'usage',
    },
};

export const FilterTableNoFilterableProperty: Story = {
    args: {
        carbonLocale: () => ({}),
        locale: 'en-US',
        localizeEnumMember: (_, member) => startCase(member),
        localize: (_, v) => v,
        onChange: condition => {
            console.log(condition);
        },
        value: [],
        node: 'SalesInvoice',
        parameters: fakeParameters,
        selectedProperties: mockSalesInvoiceObject
            .filter(c => !c.canFilter)
            .reduce(
                (acc, curr) => {
                    acc[curr.name] = {
                        label: curr.label,
                        id: curr.name,
                        key: curr.name,
                        path: curr.name,
                        labelPath: curr.label,
                        data: curr,
                    };
                    return acc;
                },
                {
                    'notes.value': {
                        label: 'Notes',
                        id: 'notes.value',
                        key: 'notes.value',
                        labelKey: 'Notes',
                        labelPath: 'Notes',
                        data: {
                            name: 'notes.value',
                            title: 'Notes',
                            canSort: false,
                            canFilter: true,
                            type: '_OutputTextStream',
                            isCustom: false,
                            isMutable: false,
                            isStored: true,
                            isOnInputType: true,
                            isOnOutputType: true,
                            enumType: null,
                            dataType: '',
                            targetNode: '',
                            isCollection: false,
                            kind: 'SCALAR',
                            label: 'Notes',
                        },
                    },
                },
            ),
        nodeNames: {
            SalesInvoice: 'Sales Invoice',
            SalesOrder: 'Sales Order',
        },
        automaticColumnsSpacing: true,
        isDisabled: false,
        parameterMode: 'usage',
    },
};

export const FilterTableNoParentColumn: Story = {
    args: {
        carbonLocale: () => ({}),
        locale: 'en-US',
        localizeEnumMember: (_, member) => startCase(member),
        localize: (_, v) => v,
        onChange: condition => {
            console.log(condition);
        },
        value: [],
        node: 'SalesInvoice',
        parameters: fakeParameters,
        isParentColumnHidden: true,
        selectedProperties: mockSalesInvoiceObject.reduce((acc, curr) => {
            acc[curr.name] = {
                label: curr.label,
                id: curr.name,
                key: curr.name,
                path: curr.name,
                labelPath: curr.label,
                data: curr,
            };
            return acc;
        }, {}),
        nodeNames: {
            SalesInvoice: 'Sales Invoice',
            SalesOrder: 'Sales Order',
        },
        automaticColumnsSpacing: true,
        isDisabled: false,
        parameterMode: 'usage',
    },
};

export const FilterTablePodMode: Story = {
    args: {
        carbonLocale: () => ({}),
        locale: 'en-US',
        localizeEnumMember: (_, member) => startCase(member),
        localize: (_, v) => v,
        onChange: condition => {
            console.log(condition);
        },
        value: [],
        node: 'SalesInvoice',
        parameters: fakeParameters,
        selectedProperties: mockSalesInvoiceObject.reduce((acc, curr) => {
            acc[curr.name] = {
                label: curr.label,
                id: curr.name,
                key: curr.name,
                path: curr.name,
                labelPath: curr.label,
                data: curr,
            };
            return acc;
        }, {}),
        nodeNames: {
            SalesInvoice: 'Sales Invoice',
            SalesOrder: 'Sales Order',
        },
        automaticColumnsSpacing: true,
        isDisabled: false,
        parameterMode: 'usage',
        mode: 'pod',
    },
};
