import type { LocalizeFunction } from '@sage/xtrem-shared';
import * as React from 'react';

export interface PropertyTableHeaderProps {
    localize: LocalizeFunction;
    isParentColumnHidden?: boolean;
}

export const PropertyTableHeader = React.memo(({ localize, isParentColumnHidden }: PropertyTableHeaderProps) => {
    return (
        <tr>
            <th>
                <span className="e-filterable-select-header-content">
                    <span className="e-filterable-select-header-content-text">
                        {localize('@sage/xtrem-ui-components/name', 'Name')}
                    </span>
                </span>
            </th>
            {!isParentColumnHidden && (
                <th>
                    <span className="e-filterable-select-header-content">
                        <span className="e-filterable-select-header-content-text">
                            {localize('@sage/xtrem-ui-components/parent', 'Parent')}
                        </span>
                    </span>
                </th>
            )}
        </tr>
    );
});

PropertyTableHeader.displayName = 'PropertyTableHeader';
