import React from 'react';
import { render, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { PropertyTableHeader } from '../property-table-header';

describe('PropertyTableHeader', () => {
    const localizeMock = jest.fn((key, defaultValue) => defaultValue);

    it('should render with localized headers', async () => {
        const { getByText } = render(<PropertyTableHeader localize={localizeMock} />);

        await waitFor(() => {
            expect(getByText('Name')).toBeInTheDocument();
            expect(getByText('Parent')).toBeInTheDocument();
        });

        expect(localizeMock).toHaveBeenCalledWith('@sage/xtrem-ui-components/name', 'Name');
        expect(localizeMock).toHaveBeenCalledWith('@sage/xtrem-ui-components/parent', 'Parent');
    });
});
