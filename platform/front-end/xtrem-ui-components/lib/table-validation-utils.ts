import { isValidIsoDate } from '@sage/xtrem-date-time';
import { GraphQLTypes, RANGE_DIVIDER, type NodeDetails } from '@sage/xtrem-shared';
import { every, includes } from 'lodash';
import type { ConditionEditorTableProps } from './condition-editor-table/condition-editor-table-types';

function validateBoolean(value: any, localize: ConditionEditorTableProps['localize']): string | undefined {
    if (value !== 'true' && value !== 'false') {
        return localize('@sage/xtrem-ui-components/invalid-boolean', 'Invalid boolean');
    }
    return undefined;
}

function validateEnum(
    enumValues: string[] | undefined,
    localize: ConditionEditorTableProps['localize'],
): string | undefined {
    if (!every(enumValues?.map(item => enumValues.includes(item)))) {
        return localize('@sage/xtrem-ui-components/invalid-value', 'Invalid value');
    }
    return undefined;
}

function validateDate(value: any, localize: ConditionEditorTableProps['localize']): string | undefined {
    const invalidRange = localize('@sage/xtrem-ui-components/invalid-range', 'Invalid range');

    if (value && Array.isArray(value)) {
        const errors = value.map(v => validateScalarValue({ type: GraphQLTypes.Date, value: v, localize }) ?? '');
        if (errors.some(Boolean)) {
            return errors.join(RANGE_DIVIDER);
        }
        const [start, end] = value;
        if (start && end && Date.parse(end.rawValue) < Date.parse(start.rawValue)) {
            return `${RANGE_DIVIDER}${invalidRange}`;
        }
        return undefined;
    }
    if (typeof value === 'object' && (value.formattedValue.length !== 10 || !isValidIsoDate(value.rawValue))) {
        return localize('@sage/xtrem-ui-components/invalid-date', 'Invalid date');
    }
    return undefined;
}

function validateNumeric(
    value: any,
    type: NodeDetails['type'],
    localize: ConditionEditorTableProps['localize'],
): string | undefined {
    const invalidRange = localize('@sage/xtrem-ui-components/invalid-range', 'Invalid range');
    const invalidInteger = localize('@sage/xtrem-ui-components/invalid-integer', 'Invalid integer');
    const invalidNumber = localize('@sage/xtrem-ui-components/invalid-number', 'Invalid number');

    // IMPORTANT: Validation of ranges must be done prior to the validation of integer and float
    //            values. Otherwise the validation of ranges failes.
    if (value && value.includes(RANGE_DIVIDER)) {
        const values = String(value).split(RANGE_DIVIDER);
        const errors = values.map(v => validateScalarValue({ type, value: v, localize }) ?? '');
        if (errors.some(Boolean)) {
            return errors.join(RANGE_DIVIDER);
        }
        const [start, end] = values.map(Number);
        if (start && end && end < start) {
            return `${RANGE_DIVIDER}${invalidRange}`;
        }
        return undefined;
    }

    if (
        includes([GraphQLTypes.Int, GraphQLTypes.IntReference], type) &&
        (value === undefined || value === null || !value.match(/^[\d]+$/gu))
    ) {
        return invalidInteger;
    }

    if (
        includes([GraphQLTypes.Decimal, GraphQLTypes.Float], type) &&
        (value === undefined || value === null || Number.isNaN(Number.parseFloat(value)))
    ) {
        return invalidNumber;
    }

    return undefined;
}

export function validateScalarValue({
    type,
    value,
    enumValues,
    localize,
}: {
    type: NodeDetails['type'];
    value: any;
    enumValues?: string[];
    localize: ConditionEditorTableProps['localize'];
}): string | undefined {
    switch (type) {
        case GraphQLTypes.Boolean:
            return validateBoolean(value, localize);
        case GraphQLTypes.Enum:
            return validateEnum(enumValues, localize);
        case GraphQLTypes.Date:
        case GraphQLTypes.DateTime:
            return validateDate(value, localize);
        case GraphQLTypes.Decimal:
        case GraphQLTypes.Float:
        case GraphQLTypes.Int:
        case GraphQLTypes.IntReference:
            return validateNumeric(value, type, localize);
        case 'InputStream':
        case GraphQLTypes.Json:
        case GraphQLTypes.ExternalReference:
            // Unsupported
            return undefined;
        default:
            return undefined;
    }
}
