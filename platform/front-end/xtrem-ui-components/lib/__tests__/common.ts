import type { NodeDetails } from '@sage/xtrem-shared';

const additionalPropsCommonValues = {
    enumType: null,
    isCustom: false,
    dataType: '',
    dataTypeDetails: null,
    targetNode: '',
    targetNodeDetails: null,
    isStored: true,
    isOnInputType: true,
    isOnOutputType: true,
    isMutable: false,
};
const additionalPropsForId = {
    ...additionalPropsCommonValues,
    isOnInputType: false,
};

const additionalPropsForCountry = {
    ...additionalPropsCommonValues,
    dataType: 'country',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-system/Country',
    targetNodeDetails: {},
};

const additionalPropsForSalesInvoice = {
    ...additionalPropsCommonValues,
    dataType: 'salesInvoice',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-sales/SalesInvoice',
    targetNodeDetails: {},
};

const additionalPropsForCustomer = {
    ...additionalPropsCommonValues,
    dataType: 'customer',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-master-data/Customer',
    targetNodeDetails: {},
};

const additionalPropsForItem = {
    ...additionalPropsCommonValues,
    dataType: 'item',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-master-data/Item',
    targetNodeDetails: {},
};

const additionalPropsForProductCategory = {
    ...additionalPropsCommonValues,
    dataType: 'productCategoryEnumDataType',
    enumType: 'productCategoryEnumType',
};

const additionalPropsForBillingAddress = {
    ...additionalPropsCommonValues,
    dataType: 'billingAddress',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-master-data/BusinessEntityAddress',
    targetNodeDetails: {},
};

const additionalPropsForDeliveryAddress = {
    ...additionalPropsCommonValues,
    dataType: 'deliveryAddress',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-master-data/BusinessEntityAddress',
    targetNodeDetails: {},
};

const additionalPropsForCurrency = {
    ...additionalPropsCommonValues,
    dataType: 'currency',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-master-data/Currency',
    targetNodeDetails: {},
};

const additionalPropsForSalesInvoiceLine = {
    ...additionalPropsCommonValues,
    dataType: 'salesInvoiceLine',
    dataTypeDetails: {},
    targetNode: '@sage/xtrem-sales/SalesInvoiceLine',
    targetNodeDetails: {},
};

const mockCountryObject: NodeDetails[] = [
    {
        name: '_id',
        label: 'ID',
        kind: 'SCALAR',
        type: 'IntOrString',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'name',
        label: 'Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'isoCode',
        label: 'ISO Code',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'isEuMember',
        label: 'EU Member',
        kind: 'SCALAR',
        type: 'Boolean',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'phoneNumber',
        label: 'Country phone code',
        kind: 'SCALAR',
        type: 'Int',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'currency',
        label: 'Currency',
        kind: 'OBJECT',
        type: 'Currency',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCurrency,
    },
];

const mockCurrencyObject: NodeDetails[] = [
    {
        name: '_id',
        label: '_id',
        kind: 'SCALAR',
        type: 'IntOrString',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'name',
        label: 'Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'symbol',
        label: 'Symbol',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'isoCode',
        label: 'ISO Code',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'country',
        label: 'Country',
        kind: 'OBJECT',
        type: 'Country',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCountry,
    },
];

const mockCustomerObject: NodeDetails[] = [
    {
        name: '_id',
        label: '_id',
        kind: 'SCALAR',
        type: 'IntOrString',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'name',
        label: 'Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'taxNumber',
        label: 'Tax Code',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'taxCountry',
        label: 'Country',
        kind: 'OBJECT',
        type: 'Country',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCountry,
    },
    {
        type: '@sage/xtrem-master-data/CreditRating',
        kind: 'ENUM',
        enumValues: ['great', 'good', 'ok', 'notBad', 'awful'],
        isCollection: false,
        name: 'creditRating',
        canFilter: true,
        canSort: true,
        label: 'Credit Rating',
        ...additionalPropsForProductCategory,
    },
];
const mockItemObject: NodeDetails[] = [
    {
        name: '_id',
        label: 'ID',
        kind: 'SCALAR',
        type: 'IntOrString',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'description',
        label: 'Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'upcCode',
        label: 'UPC code',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'originCountry',
        label: 'Manufactured in',
        kind: 'OBJECT',
        type: 'Country',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCountry,
    },
    {
        type: '@sage/xtrem-master-data/ProductCategory',
        kind: 'ENUM',
        enumValues: ['great', 'good', 'ok', 'notBad', 'awful'],
        isCollection: false,
        name: 'category',
        canFilter: true,
        canSort: true,
        label: 'Category',
        ...additionalPropsForProductCategory,
    },
];

const mockSalesInvoiceLineObject: NodeDetails[] = [
    {
        name: 'description',
        label: 'Description',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'item',
        label: 'Item',
        kind: 'OBJECT',
        type: 'Item',
        canFilter: false,
        canSort: false,
        ...additionalPropsForItem,
    },
    {
        name: 'quantity',
        label: 'Quantity',
        kind: 'SCALAR',
        type: 'Float',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'isDiscountLine',
        label: 'Discount Line',
        kind: 'SCALAR',
        type: 'Boolean',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'netPrice',
        label: 'Net Price',
        kind: 'SCALAR',
        type: 'Float',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'grossPrice',
        label: 'Gross Price',
        kind: 'SCALAR',
        type: 'Float',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'computedLineTotal',
        label: 'Computed Line Total',
        kind: 'SCALAR',
        type: 'Float',
        canFilter: false,
        canSort: false,
        ...additionalPropsCommonValues,
    },
    {
        name: 'invoice',
        label: 'Invoice',
        kind: 'OBJECT',
        type: 'SalesInvoice',
        canFilter: true,
        canSort: true,
        ...additionalPropsForSalesInvoice,
    },
];

export const mockSalesInvoiceObject: NodeDetails[] = [
    {
        name: '_id',
        label: '_id',
        kind: 'SCALAR',
        type: 'IntOrString',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'invoiceNumber',
        label: 'Invoice Number',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: '_customData.testCustomField',
        label: 'testCustomField',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: false,
        isStored: true,
        isOnInputType: true,
        isOnOutputType: true,
        dataType: '',
        targetNode: '',
        enumType: null,
        isCustom: true,
        isMutable: false,
    },
    {
        name: 'totalWithoutTax',
        label: 'Net Total',
        kind: 'SCALAR',
        type: 'Float',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'totalWithTax',
        label: 'Gross Total',
        kind: 'SCALAR',
        type: 'Float',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'customer',
        label: 'Customer',
        kind: 'OBJECT',
        type: 'Customer',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCustomer,
    },
    {
        name: 'billingAddress',
        label: 'Billing address',
        kind: 'OBJECT',
        type: 'Address',
        canFilter: false,
        canSort: false,
        ...additionalPropsForBillingAddress,
    },
    {
        name: 'deliveryAddress',
        label: 'Delivery address',
        kind: 'OBJECT',
        type: 'Address',
        canFilter: false,
        canSort: false,
        ...additionalPropsForDeliveryAddress,
    },
    {
        name: 'phoneNumber',
        label: 'Country phone code',
        kind: 'SCALAR',
        type: 'Int',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'orderDate',
        label: 'Order Date',
        kind: 'SCALAR',
        type: 'Date',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'deliveryDate',
        label: 'Delivery Date',
        kind: 'SCALAR',
        type: 'Date',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'currency',
        label: 'Currency',
        kind: 'OBJECT',
        type: 'Currency',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCurrency,
    },
    {
        name: 'notes',
        label: 'Notes',
        kind: 'OBJECT',
        type: '_OutputTextStream',
        canFilter: true,
        canSort: false,
        ...additionalPropsCommonValues,
    },
    {
        name: 'queryText',
        label: 'Query Text',
        kind: 'OBJECT',
        type: '_OutputTextStream',
        canFilter: true,
        canSort: false,
        ...additionalPropsCommonValues,
    },
    {
        name: 'lines',
        label: 'Lines',
        kind: 'LIST',
        type: 'SalesInvoiceLine',
        canFilter: false,
        canSort: false,
        ...additionalPropsForSalesInvoiceLine,
    },
];

const mockAddressObject: NodeDetails[] = [
    {
        name: '_id',
        label: '_id',
        kind: 'SCALAR',
        type: 'IntOrString',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'name',
        label: 'Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'town',
        label: 'Town',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'addressLine1',
        label: 'Address Line 1',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'addressLine2',
        label: 'Address Line 2',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'zipCode',
        label: 'Post code',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'country',
        label: 'Country',
        kind: 'OBJECT',
        type: 'Country',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCountry,
    },
];

const mockUserObject: NodeDetails[] = [
    {
        name: '_id',
        label: 'ID',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsForId,
    },
    {
        name: 'firstName',
        label: 'First Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'lastName',
        label: 'Last Name',
        kind: 'SCALAR',
        type: 'String',
        canFilter: true,
        canSort: true,
        ...additionalPropsCommonValues,
    },
    {
        name: 'country',
        label: 'Country of origin',
        kind: 'OBJECT',
        type: 'Country',
        canFilter: false,
        canSort: false,
        ...additionalPropsForCountry,
    },
];

export const fakeFetchItems = (parent: NodeDetails): Promise<NodeDetails[]> =>
    new Promise(resolve =>
        setTimeout(() => {
            const objectName = parent.node;

            if (objectName === 'Country') {
                resolve(mockCountryObject);
                return;
            }

            if (objectName === 'Currency') {
                resolve(mockCurrencyObject);
                return;
            }

            if (objectName === 'Customer') {
                resolve(mockCustomerObject);
                return;
            }
            if (objectName === 'Item') {
                resolve(mockItemObject);
                return;
            }
            if (objectName === 'SalesInvoiceLine') {
                resolve(mockSalesInvoiceLineObject);
                return;
            }

            if (objectName === 'SalesInvoice') {
                resolve(mockSalesInvoiceObject);
                return;
            }

            if (objectName === 'Address') {
                resolve(mockAddressObject);
                return;
            }

            if (objectName === 'User') {
                resolve(mockUserObject);
            }
        }, 500),
    );

export const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect;

export function mockedGetBoundingClientRect(this: any): DOMRect {
    const dataElement = this.getAttribute('data-element');
    if (dataElement === 'select-list-wrapper') {
        return {
            height: 1080,
            width: 1920,
        } as any;
    }
    return originalGetBoundingClientRect.bind(this)();
}

export const fakeParameters = [
    {
        type: 'String',
        name: 'invoiceNumber',
        label: 'Invoice Number',
    },
    {
        type: 'Int',
        name: 'invoiceNetTotal',
        label: 'Invoice Net Total',
    },
    {
        type: 'Boolean',
        name: 'shouldPrintAdditionalDetails',
        label: 'Print Additional details',
    },
    {
        name: 'dateVar',
        label: 'Date var',
        type: 'Date',
    },
    {
        name: 'intVar',
        label: 'Int var',
        type: 'Float',
    },
    {
        name: 'dateVar2',
        label: 'Another date var',
        type: 'Date',
    },
    {
        name: 'salesInvoice',
        label: 'Sales invoice',
        type: 'reference',
    },
];

export const getTreeDropdown = (input: HTMLElement, baseElement: HTMLElement): HTMLElement | null => {
    const id = input.getAttribute('aria-controls');
    return baseElement.querySelector(`[id^="${id}"]`);
};
