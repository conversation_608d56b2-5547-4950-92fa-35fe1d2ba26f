import { getInternalPathFromExternal } from '../utils';

describe('getInternalPathFromExternal', () => {
    let mockedUrl: string;

    beforeEach(() => {
        mockedUrl = 'http://localhost:3000';
        Object.defineProperty(window, 'location', {
            value: {
                get origin() {
                    return 'http://localhost:3000';
                },
                get href() {
                    return mockedUrl;
                },
            },
        });
    });

    it('should return url with query parameters', () => {
        mockedUrl = 'http://localhost:3000/@sage/xtrem-test/TestPage/eyJfaWQiOiIkbmV3In0=';
        const result = getInternalPathFromExternal();
        expect(result).toEqual('@sage/xtrem-test/TestPage/eyJfaWQiOiIkbmV3In0=');
    });

    it('should return url without query parameters', () => {
        mockedUrl = 'http://localhost:3000/@sage/xtrem-test/TestPage';
        const result = getInternalPathFromExternal();
        expect(result).toEqual('@sage/xtrem-test/TestPage');
    });

    it('should parse 4th segment as an _id if not valid base64 json', () => {
        mockedUrl = 'http://localhost:3000/@sage/xtrem-test/TestPage/1245';
        const result = getInternalPathFromExternal();
        expect(result).toEqual('@sage/xtrem-test/TestPage/eyJfaWQiOiIxMjQ1In0=');
    });

    it('should mix query params with search parameters', () => {
        mockedUrl = 'http://localhost:3000/@sage/xtrem-test/TestPage/eyJfaWQiOiIkbmV3In0=?test=foo&test2=bar';
        const result = getInternalPathFromExternal();
        expect(result).toEqual('@sage/xtrem-test/TestPage/eyJ0ZXN0IjoiZm9vIiwidGVzdDIiOiJiYXIiLCJfaWQiOiIkbmV3In0=');
    });

    it('should parse 4th segment as an _id if not valid base64 json mixed with search parameters', () => {
        mockedUrl = 'http://localhost:3000/@sage/xtrem-test/TestPage/1245?test=foo&test2=bar';
        const result = getInternalPathFromExternal();
        expect(result).toEqual('@sage/xtrem-test/TestPage/eyJ0ZXN0IjoiZm9vIiwidGVzdDIiOiJiYXIiLCJfaWQiOiIxMjQ1In0=');
    });
});
