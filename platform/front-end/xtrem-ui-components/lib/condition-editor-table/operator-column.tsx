import React from 'react';
import type {
    FilterParameter,
    DefaultConditionEditorCols,
    OnCellChange,
    ConditionOperator,
} from './condition-editor-table-types';
import { filterTranslations } from './condition-editor-table-types';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import { GraphQLTypes, filterGraphqlMapping } from '@sage/xtrem-shared';
import type { FilterTypeValue, LocalizeFunction } from '@sage/xtrem-shared';
import { FilterableSelect, Option } from 'carbon-react/esm/components/select';
import { includes, without } from 'lodash';
import Box from 'carbon-react/esm/components/box';
import Typography from 'carbon-react/esm/components/typography';

export interface OperatorColumnProps<Cols extends ColDef[] = [], E = unknown> {
    isDisabled?: boolean;
    localize: LocalizeFunction;
    onCellChange: OnCellChange<Cols, E>;
    parameters?: FilterParameter[];
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['data'][number];
}

export function OperatorColumn<Cols extends ColDef[] = [], E = unknown>({
    isDisabled,
    localize,
    onCellChange,
    parameters,
    rowData,
}: OperatorColumnProps<Cols, E>): React.ReactElement {
    const isParameter = rowData.valueType1 === 'parameter';
    // If the value is a parameter, the type is the type of the parameter
    const type: GraphQLTypes = isParameter
        ? parameters?.find(p => p.name === rowData.value1)?.type
        : rowData.value1?.data?.type;
    // Parameters are always scalar types
    const kind = isParameter ? 'SCALAR' : rowData.value1?.data?.kind;

    let filterableValue: ConditionOperator[] = [];
    if (type !== undefined && filterGraphqlMapping[type] !== undefined) {
        filterableValue = [...filterGraphqlMapping[type]];
    } else if (kind !== undefined) {
        const foundKey = Object.keys(filterGraphqlMapping).find(
            key => key.toLowerCase() === kind.toLowerCase(),
        ) as GraphQLTypes;
        if (foundKey !== undefined) {
            filterableValue = [...(filterGraphqlMapping[foundKey] ?? [])];
        }
    }

    filterableValue = without([...filterableValue, 'empty', 'notEmpty'], 'inRange');

    if (
        type &&
        includes(
            [GraphQLTypes.Boolean, GraphQLTypes.Id, GraphQLTypes.IntOrString, GraphQLTypes.IntReference] as string[],
            type,
        )
    ) {
        return (
            <div data-testid={`e-widget-editor-filter-type-${rowData._id}`}>
                <Box display="flex" alignItems="center" justifyContent="flex-start">
                    <Typography variant="p" m={0} paddingLeft="8px">
                        {localize('@sage/xtrem-ui-components/equals', 'Equals')}
                    </Typography>
                </Box>
            </div>
        );
    }
    return (
        <FilterableSelect
            disabled={!rowData.value1 || isDisabled}
            openOnFocus={true}
            data-testid={`e-widget-editor-filter-type-${rowData._id}`}
            onChange={({ target: { value } }): void => {
                onCellChange({
                    columnId: 'operator',
                    rowId: rowData._id,
                    value: value as FilterTypeValue | undefined,
                    rowData,
                });
            }}
            placeholder={localize('@sage/xtrem-ui-components/select-operator', 'Select operator...')}
            size="small"
            value={rowData.operator ?? ''}
        >
            {filterableValue?.map((filterType: FilterTypeValue) => {
                return (
                    <Option
                        text={filterTranslations(localize)[filterType] ?? ''}
                        value={filterType ?? ''}
                        key={filterType ?? ''}
                    />
                );
            })}
        </FilterableSelect>
    );
}
