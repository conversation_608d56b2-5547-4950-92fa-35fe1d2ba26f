import * as React from 'react';
import type {
    DefaultConditionEditorCols,
    FilterParameter,
    OnCellChange,
    ValueType,
} from './condition-editor-table-types';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import { GraphQLTypes } from '@sage/xtrem-shared';
import type { LocalizeFunction, LocalizeLocale, NodeDetails } from '@sage/xtrem-shared';
import { includes } from 'lodash';
import { ParameterValueInput } from '../shared-table-column/parameter-value-input';
import { DateValueInput } from '../shared-table-column/date-value-input';
import type { CarbonLocaleType } from '../types';
import { BooleanValueInput } from '../shared-table-column/boolean-value-input';
import { EnumValueInput } from '../shared-table-column/enum-value-input';
import { TextValueInput } from '../shared-table-column/text-value-input';
import { PropertyColumn } from './property-column';
import { NUMERIC_TYPES } from '../utils';

const SUPPORTED_PARAMETER_TYPES: string[] = [
    GraphQLTypes.Int,
    GraphQLTypes.Float,
    GraphQLTypes.Decimal,
    GraphQLTypes.Boolean,
    GraphQLTypes.String,
    GraphQLTypes.Date,
];

export interface OperatorColumnProps<Cols extends ColDef[] = [], E = unknown> {
    localize: LocalizeFunction;
    onCellChange: OnCellChange<Cols, E>;
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['data'][number];
    fetchItems: (element: NodeDetails) => Promise<NodeDetails[]>;
    carbonLocale: CarbonLocaleType;
    isDisabled?: boolean;
    locale: LocalizeLocale;
    localizeEnumMember: (enumFullPathName: string, memberName: string) => string;
    errors?: Record<string, any>;
    node?: string;
    parameters?: FilterParameter[];
    typeRestriction?: NodeDetails['type'];
    valueType?: ValueType;
    columnId: 'value1' | 'value2';
    enumOptions?: string[];
    enumType?: string;
}

export function ValueColumn<Cols extends ColDef[] = [], E = unknown>({
    localize,
    onCellChange,
    rowData,
    locale,
    carbonLocale,
    localizeEnumMember,
    errors,
    fetchItems,
    node,
    parameters,
    typeRestriction,
    valueType,
    columnId,
    enumOptions: enumValues,
    enumType,
    ...rest
}: OperatorColumnProps<Cols, E>): React.ReactElement {
    const isDisabled = rest.isDisabled || !valueType || !valueType;

    const filteredFetchItems = React.useCallback(
        async (element: NodeDetails): Promise<NodeDetails[]> => {
            const items = await fetchItems(element);
            if (!typeRestriction) {
                return items;
            }

            // Numeric values can be compared against each other, regardless their actual type, so here we normalize all numeric types to Int
            const normalizedExpectedType = NUMERIC_TYPES.includes(typeRestriction) ? GraphQLTypes.Int : typeRestriction;

            return items.filter(i => {
                const normalizedCurrentType = NUMERIC_TYPES.includes(i.type) ? GraphQLTypes.Int : i.type;
                return normalizedCurrentType === normalizedExpectedType || i.kind === 'OBJECT';
            });
        },
        [fetchItems, typeRestriction],
    );

    const filteredParameters = React.useMemo(() => {
        if (!parameters) {
            return [];
        }

        if (!typeRestriction) {
            return parameters.filter(p => SUPPORTED_PARAMETER_TYPES.includes(p.type));
        }

        const normalizedExpectedType = NUMERIC_TYPES.includes(typeRestriction) ? GraphQLTypes.Int : typeRestriction;

        return parameters.filter(p => {
            const normalizedCurrentType = NUMERIC_TYPES.includes(p.type) ? GraphQLTypes.Int : p.type;
            return normalizedCurrentType === normalizedExpectedType;
        });
    }, [parameters, typeRestriction]);

    if (valueType === 'property') {
        return (
            <PropertyColumn
                localize={localize}
                isDisabled={isDisabled}
                onCellChange={onCellChange}
                rowData={rowData}
                columnId={columnId}
                fetchItems={filteredFetchItems}
                node={node}
                data-testid={`e-condition-table-${columnId}-${rowData._id}`}
            />
        );
    }
    // When the user enables the parameter value for the current row, we render a selection instead
    if (valueType === 'parameter') {
        return (
            <ParameterValueInput
                localize={localize}
                isDisabled={isDisabled}
                onCellChange={onCellChange}
                rowData={rowData}
                errors={errors}
                columnId={columnId}
                parameters={filteredParameters}
                noParameterFiltering={true}
                data-testid={`e-condition-table-${columnId}`}
            />
        );
    }

    // date & date-time
    if (
        valueType === 'constant' &&
        typeRestriction &&
        includes([GraphQLTypes.Date, GraphQLTypes.DateTime], typeRestriction)
    ) {
        return (
            <DateValueInput
                isDisabled={isDisabled}
                onCellChange={onCellChange}
                rowData={rowData}
                locale={locale}
                carbonLocale={carbonLocale}
                errors={errors}
                columnId={columnId}
                data-testid={`e-condition-table-${columnId}`}
            />
        );
    }

    if (valueType === 'constant' && typeRestriction === GraphQLTypes.Boolean) {
        return (
            <BooleanValueInput
                isDisabled={isDisabled}
                onCellChange={onCellChange}
                rowData={rowData}
                localize={localize}
                columnId={columnId}
                data-testid={`e-condition-table-${columnId}`}
            />
        );
    }

    // enums
    if (valueType === 'constant' && enumValues && enumType) {
        return (
            <EnumValueInput
                isDisabled={isDisabled}
                onCellChange={onCellChange}
                rowData={rowData}
                localize={localize}
                localizeEnumMember={localizeEnumMember}
                columnId={columnId}
                enumOptions={enumValues}
                enumType={enumType}
                data-testid={`e-condition-table-${columnId}`}
            />
        );
    }

    return (
        <TextValueInput
            isDisabled={isDisabled}
            onCellChange={onCellChange}
            rowData={rowData}
            localize={localize}
            errors={errors}
            columnId={columnId}
            data-testid={`e-condition-table-${columnId}`}
        />
    );
}
