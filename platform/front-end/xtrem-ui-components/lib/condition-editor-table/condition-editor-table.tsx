import { isEqual } from 'lodash';
import React from 'react';
import { FlatTable } from '../flat-table/flat-table';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import { usePrevious } from '../hooks/use-previous';
import { conditionEditorTableReducer, isCompleteRow, mapConditionToEditorRow } from './condition-editor-table-utils';
import { ConjunctionColumn } from './conjunction-column';
import { OperatorColumn } from './operator-column';
import { ValueColumn } from './value-column';
import { ValueTypeColumn } from './value-type-column';
import type {
    DefaultConditionEditorCols,
    ConditionEditorTableProps,
    OnCellChange,
    UseFilterTableHook,
    ConditionEditorTableValidations,
} from './condition-editor-table-types';
import { isEnumField } from '../utils';

export function useConditionEditorTable<
    Cols extends ColDef[] = [],
    E extends [ConditionEditorTableValidations, string] = [ConditionEditorTableValidations, string],
>({
    carbonLocale,
    isDisabled,
    localize,
    localizeEnumMember,
    node,
    onChange,
    onValidityChange,
    parameters = [],
    value = [],
    fetchItems,
    locale,
}: ConditionEditorTableProps): UseFilterTableHook<Cols, E> {
    const previousValue = usePrevious(value);

    const onCellChange = React.useCallback<OnCellChange<Cols, E>>(changes => {
        dispatch({ type: 'CELL_CHANGED', changes });
    }, []);

    const onRowAdded = React.useCallback<
        NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['onRowAdded']>
    >(() => {
        dispatch({ type: 'ROW_ADDED' });
    }, []);

    const onRowRemoved = React.useCallback<
        NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['onRowRemoved']>
    >((row): void => {
        dispatch({ type: 'ROW_REMOVED', row });
    }, []);

    const columns = React.useMemo<
        FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['columns']
    >(() => {
        return [
            {
                id: 'conjunction',
                header: {
                    name: localize('@sage/xtrem-ui-components/condition-editor', 'Conjunction'),
                    width: 120,
                },
                cellRenderer: ({ rowData, rowIndex }): React.ReactElement => (
                    <ConjunctionColumn<Cols, E>
                        isDisabled={isDisabled}
                        localize={localize}
                        onCellChange={onCellChange}
                        rowData={rowData}
                        rowIndex={rowIndex}
                    />
                ),
            },
            {
                id: 'valueType1',
                header: {
                    name: localize('@sage/xtrem-ui-components/condition-value-type-1', 'Value Type 1'),
                    width: 150,
                },
                cellRenderer: ({ rowData }): React.ReactElement => (
                    <ValueTypeColumn<Cols, E>
                        isDisabled={isDisabled}
                        localize={localize}
                        onCellChange={onCellChange}
                        rowData={rowData}
                        columnId="valueType1"
                        parameters={parameters}
                    />
                ),
            },
            {
                id: 'value1',
                header: {
                    name: localize('@sage/xtrem-ui-components/condition-table-value-1', 'Value 1'),
                },
                cellRenderer: ({ rowData, extraData }): React.ReactElement => (
                    <ValueColumn<Cols>
                        errors={extraData[0]}
                        isDisabled={isDisabled}
                        localize={localize}
                        onCellChange={onCellChange}
                        rowData={rowData}
                        carbonLocale={carbonLocale}
                        fetchItems={fetchItems}
                        locale={locale}
                        localizeEnumMember={localizeEnumMember}
                        node={extraData[1]}
                        parameters={parameters}
                        columnId="value1"
                        valueType={rowData.valueType1}
                    />
                ),
            },
            {
                id: 'operator',
                header: {
                    name: localize('@sage/xtrem-ui-components/operator', 'Operator'),
                    width: undefined,
                },
                cellRenderer: ({ rowData }): React.ReactElement => (
                    <OperatorColumn<Cols, E>
                        isDisabled={isDisabled}
                        localize={localize}
                        onCellChange={onCellChange}
                        rowData={rowData}
                        parameters={parameters}
                    />
                ),
            },
            {
                id: 'valueType2',
                header: {
                    name: localize('@sage/xtrem-ui-components/condition-value-type-2', 'Value Type 2'),
                    width: 150,
                },
                cellRenderer: ({ rowData }): React.ReactElement | null => {
                    if (rowData.operator === 'empty' || rowData.operator === 'notEmpty') {
                        return null;
                    }

                    return (
                        <ValueTypeColumn<Cols, E>
                            isDisabled={isDisabled || !rowData.valueType1 || !rowData.value1}
                            localize={localize}
                            onCellChange={onCellChange}
                            rowData={rowData}
                            columnId="valueType2"
                            parameters={parameters}
                        />
                    );
                },
            },
            {
                id: 'value2',
                header: {
                    name: localize('@sage/xtrem-ui-components/condition-table-value-2', 'Value 2'),
                },
                cellRenderer: ({ rowData, extraData: [validations, node] }): React.ReactElement | null => {
                    if (rowData.operator === 'empty' || rowData.operator === 'notEmpty') {
                        return null;
                    }
                    const isParameter = rowData.valueType1 === 'parameter';
                    const isEnum = !isParameter && isEnumField(rowData.value1?.data);
                    // If the value is a parameter, the type is the type of the parameter
                    const type = isParameter
                        ? parameters?.find(p => p.name === rowData.value1)?.type
                        : rowData.value1?.data?.type;
                    return (
                        <ValueColumn<Cols>
                            errors={validations}
                            isDisabled={isDisabled || !type || !rowData.valueType2}
                            localize={localize}
                            onCellChange={onCellChange}
                            rowData={rowData}
                            carbonLocale={carbonLocale}
                            fetchItems={fetchItems}
                            locale={locale}
                            localizeEnumMember={localizeEnumMember}
                            node={node}
                            parameters={parameters}
                            columnId="value2"
                            valueType={rowData.valueType2}
                            typeRestriction={type}
                            enumType={isEnum ? rowData.value1?.data?.type : undefined}
                            enumOptions={isEnum ? rowData.value1?.data?.enumValues : undefined}
                        />
                    );
                },
            },
            { id: 'key', isHidden: true, header: { name: 'Key' } },
        ];
    }, [carbonLocale, fetchItems, isDisabled, locale, localize, localizeEnumMember, onCellChange, parameters]);

    const onRowDrag = React.useCallback<
        NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['onRowDrag']>
    >(ids => {
        dispatch({ type: 'ROW_DRAGGED', ids });
    }, []);

    const addButtonText = React.useMemo<string>(
        () => localize('@sage/xtrem-ui-components/add-condition', 'Add condition'),
        [localize],
    );

    const data = React.useMemo<
        FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['data']
    >(() => {
        return mapConditionToEditorRow<Cols>({ value });
    }, [value]);

    const [tableDefinition, dispatch] = React.useReducer(conditionEditorTableReducer<Cols, E>()(localize, parameters), {
        addButtonText,
        canDrag: true,
        columns,
        counter: value.length,
        data,
        onRowAdded,
        onRowDrag,
        onRowRemoved,
        validations: {},
    });

    React.useEffect(() => {
        const completeRows = tableDefinition.data.filter(row => isCompleteRow(row, tableDefinition.validations));
        onChange(
            completeRows.map(({ conjunction, value1, value2, valueType1, valueType2, key, operator, _id, ...rest }) => {
                return {
                    // spread needed in case columns are extended
                    ...rest,
                    conjunction: conjunction!,
                    valueType1,
                    valueType2,
                    _id,
                    value1,
                    value2,
                    key,
                    operator,
                };
            }),
        );
        onValidityChange?.(completeRows.length === tableDefinition.data.length);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tableDefinition.data, tableDefinition.validations, onValidityChange]);

    React.useEffect(() => {
        const newValue = mapConditionToEditorRow({ value });
        if (!isEqual(previousValue, value) && !isEqual(newValue, tableDefinition.data)) {
            dispatch({ type: 'DATA_RESET', value });
        }
    }, [value, tableDefinition.data, previousValue]);

    return {
        ...tableDefinition,
        extraData: [tableDefinition.validations, node],
        canRemoveLines: true,
        emptyStateText: localize(
            '@sage/xtrem-ui-components/condition-empty-state',
            'Click the add button to start editing the conditions',
        ),
        actionsText: localize('@sage/xtrem-ui-components/actions', 'Actions'),
        isAddButtonHidden: false,
        onCellChange,
    } as unknown as UseFilterTableHook<Cols, E>;
}

export function ConditionEditorTableComponent(props: ConditionEditorTableProps): React.ReactElement {
    const {
        actionsText,
        addButtonText,
        canDrag,
        canRemoveLines,
        columns,
        data,
        emptyStateText,
        extraData,
        isAddButtonHidden,
        onRowAdded,
        onRowDrag,
        onRowRemoved,
    } = useConditionEditorTable(props);

    const memoizedExtraData = React.useMemo(() => {
        return [extraData?.[0], extraData?.[1] || props.node];
    }, [extraData, props.node]);

    return (
        <FlatTable
            actionsText={actionsText}
            addButtonText={addButtonText}
            canAddNewLines={!props.isDisabled}
            canDrag={canDrag}
            canRemoveLines={canRemoveLines}
            columns={columns}
            data={data}
            emptyStateText={emptyStateText}
            extraData={memoizedExtraData}
            isAddButtonHidden={isAddButtonHidden}
            maxLines={10}
            onRowAdded={onRowAdded}
            onRowDrag={onRowDrag}
            onRowRemoved={onRowRemoved}
        />
    );
}
