import React from 'react';
import type { DefaultConditionEditorCols, OnCellChange } from './condition-editor-table-types';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import type { LocalizeFunction, NodeDetails, TreeElement } from '@sage/xtrem-shared';
import { TreeInput } from '../tree-input/tree-input';

export interface PropertyColumnProps<Cols extends ColDef[] = [], E = unknown> {
    'data-testid'?: string;
    columnId?: 'value1' | 'value2';
    fetchItems: (element: NodeDetails) => Promise<NodeDetails[]>;
    isDisabled?: boolean;
    localize: LocalizeFunction;
    node?: string;
    onCellChange: OnCellChange<Cols, E>;
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['data'][number];
}

export function PropertyColumn<Cols extends ColDef[] = [], E = unknown>({
    'data-testid': dataTestId,
    columnId = 'value1',
    fetchItems,
    isDisabled,
    localize,
    node,
    onCellChange,
    rowData,
}: PropertyColumnProps<Cols, E>): React.ReactElement {
    const onChange = React.useCallback(
        (value: TreeElement<NodeDetails> | null) => {
            onCellChange({
                columnId,
                rowData,
                rowId: rowData._id,
                value: value || undefined,
            });
        },
        [columnId, onCellChange, rowData],
    );
    return (
        <TreeInput
            canSelectObjects={false}
            data-testid={dataTestId}
            fetchItems={fetchItems}
            isDisabled={isDisabled}
            localize={localize}
            node={node}
            onChange={onChange}
            value={rowData[columnId] ?? null}
        />
    );
}
