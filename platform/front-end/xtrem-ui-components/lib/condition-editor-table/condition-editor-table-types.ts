import type {
    FilterTypeValue,
    LocalizeEnumFunction,
    LocalizeFunction,
    LocalizeLocale,
    NodeDetails,
} from '@sage/xtrem-shared';
import { memoize } from 'lodash';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import type { BaseTableCols, CarbonLocaleType } from '../types';

export type ConjunctionType = 'and' | 'or';
export type ValueType = 'constant' | 'property' | 'parameter';

export interface ConditionEditorProperty {
    _id: string;
    conjunction: ConjunctionType;
    value1: any;
    value2: any;
    valueType1?: ValueType;
    valueType2?: ValueType;
    operator?: ConditionOperator;
}

export interface FilterParameter {
    name: string;
    label?: string;
    type: NodeDetails['type'];
}

export type DefaultConditionEditorCols = [
    ...BaseTableCols,
    { id: 'conjunction'; type?: ConjunctionType },
    { id: 'operator'; type?: any },
    { id: 'valueType1'; type?: ValueType },
    { id: 'valueType2'; type?: ValueType },
    { id: 'value1'; type?: any },
    { id: 'value2'; type?: any },
    { id: 'key'; type: string },
];

/**
 * - `creation` means that when the user selects checks the use parameter input, a new parameter should be created for that filter value
 * - `usage` means that the user can only select from a predefined set of parameter values
 */

export interface ConditionEditorTableProps {
    carbonLocale: CarbonLocaleType;
    isDisabled?: boolean;
    locale: LocalizeLocale;
    localize: LocalizeFunction;
    localizeEnumMember: LocalizeEnumFunction;
    node?: string;
    onChange: (filters: (ConditionEditorProperty & { _id: string })[]) => void;
    onValidityChange?: (isValid: boolean) => void;
    parameters?: FilterParameter[];
    value?: ConditionEditorProperty[];
    fetchItems: (element: NodeDetails) => Promise<NodeDetails[]>;
}

export const filterTranslations = memoize(
    (localize: LocalizeFunction): Record<FilterTypeValue | 'empty' | 'notEmpty' | 'timeFrame', string> => ({
        contains: localize('@sage/xtrem-ui-components/contains', 'Contains'),
        empty: localize('@sage/xtrem-ui-components/empty', 'Empty'),
        endsWith: localize('@sage/xtrem-ui-components/endsWith', 'Ends with'),
        equals: localize('@sage/xtrem-ui-components/equals', 'Equals'),
        greaterThan: localize('@sage/xtrem-ui-components/greaterThan', 'Greater than'),
        greaterThanOrEqual: localize('@sage/xtrem-ui-components/greaterThanOrEqual', 'Greater than or equal to'),
        inRange: localize('@sage/xtrem-ui-components/between', 'Between'),
        lessThan: localize('@sage/xtrem-ui-components/lessThan', 'Less than'),
        lessThanOrEqual: localize('@sage/xtrem-ui-components/lessThanOrEqual', 'Less than or equal to'),
        matches: localize('@sage/xtrem-ui-components/matches', 'Matches'),
        multiNotEqual: localize('@sage/xtrem-ui-components/notEqual', 'Does not equal'),
        notContains: localize('@sage/xtrem-ui-components/notContains', 'Does not contain'),
        notEmpty: localize('@sage/xtrem-ui-components/notEmpty', 'Not empty'),
        notEqual: localize('@sage/xtrem-ui-components/notEqual', 'Does not equal'),
        set: localize('@sage/xtrem-ui-components/set', 'Equals'),
        startsWith: localize('@sage/xtrem-ui-components/startsWith', 'Starts with'),
        timeFrame: localize('@sage/xtrem-ui-components/timeframe', 'Time frame'),
        multipleRange: localize('@sage/xtrem-ui-components/multipleRange', 'Multiple range'),
    }),
);

export type ConditionEditorTableValidations<Cols extends ColDef[] = []> = Record<
    string,
    Record<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>[number]['id'], string>
>;

export type ConditionEditorTableState<Cols extends ColDef[] = [], E = unknown> = {
    addButtonText: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['addButtonText'];
    canDrag: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['canDrag'];
    onRowDrag: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['onRowDrag'];
    onRowRemoved: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['onRowRemoved'];
    onRowAdded: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['onRowAdded'];
    columns: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['columns'];
    data: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['data'];
    counter: number;
    validations: ConditionEditorTableValidations<Cols>;
};

export type OnCellChange<Cols extends ColDef[] = [], E = unknown> = (
    args: {
        [P in UnrestrictedTableCols<DefaultConditionEditorCols, Cols>[number]['id']]: {
            columnId: P;
            rowId: string;
            value: Extract<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>[number], { id: P }>['type'];
            rowData: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['data'][number];
        };
    }[UnrestrictedTableCols<DefaultConditionEditorCols, Cols>[number]['id']],
) => void;

export type UnrestrictedOnCellChange<Cols extends ColDef[] = []> = (
    args: {
        [P in UnrestrictedTableCols<DefaultConditionEditorCols, Cols, false>[number]['id']]: {
            columnId: P;
            rowId: string;
            value: Extract<UnrestrictedTableCols<DefaultConditionEditorCols, Cols, false>[number], { id: P }>['type'];
            rowData: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols, false>>['data'][number];
        };
    }[UnrestrictedTableCols<DefaultConditionEditorCols, Cols, false>[number]['id']],
) => void;

type Changes<Cols extends ColDef[] = []> = Parameters<OnCellChange<Cols>>[0];

export type ConditionTableEditorAction<Cols extends ColDef[] = [], E = unknown> =
    | {
          type: 'DATA_RESET';
          value: NonNullable<ConditionEditorTableProps['value']>;
      }
    | {
          type: 'ROW_ADDED';
      }
    | {
          type: 'ROW_DRAGGED';
          ids: Parameters<
              NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['onRowDrag']>
          >[0];
      }
    | {
          type: 'CELL_CHANGED';
          changes: Changes<Cols>;
      }
    | {
          type: 'ROW_REMOVED';
          row: Parameters<
              NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['onRowRemoved']>
          >[0];
      };

export type ActionExtractor<A extends ConditionTableEditorAction['type']> = Extract<
    ConditionTableEditorAction,
    { type: A }
>;

export type UseFilterTableHook<Cols extends ColDef[] = [], E = unknown> = Omit<
    ConditionEditorTableState<Cols, E>,
    'columns' | 'data'
> & {
    data: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols, false>, E>['data'];
    columns: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols, false>, E>['columns'];
    extraData: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols, false>, E>['extraData'];
    canRemoveLines: boolean;
    emptyStateText: string | undefined;
    actionsText: string;
    isAddButtonHidden: boolean;
    onCellChange: UnrestrictedOnCellChange<Cols>;
};

export type ConditionOperator = FilterTypeValue | 'empty' | 'notEmpty';
