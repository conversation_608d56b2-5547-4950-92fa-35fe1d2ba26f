import React from 'react';
import type { ValueTypeColumnProps } from '../value-type-column';
import { ValueTypeColumn } from '../value-type-column';
import { render, waitFor } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import { fakeParameters, mockedGetBoundingClientRect, originalGetBoundingClientRect } from '../../__tests__/common';
import '@testing-library/jest-dom';

const setupResizeObserverMock = () => {
    if (!window) {
        return;
    }
    window.ResizeObserver =
        window.ResizeObserver ||
        jest.fn().mockImplementation((callback: ResizeObserverCallback) => {
            let hasCalledCallback = false;
            const observer: ResizeObserver = {
                disconnect: jest.fn(),
                // observe mock needs to actually call the callback straight away, as this is what a real ResizeObserver does
                // and this behaviour is needed for the FixedNavigationBarContextProvider to work properly.
                // Note that we must only call the callback once per ResizeObserver instance, to avoid stack overflows in
                // react-virtual.
                observe: jest.fn((target: Element) => {
                    if (!hasCalledCallback) {
                        hasCalledCallback = true;
                        callback([{ target } as ResizeObserverEntry], observer);
                    }
                }),
                unobserve: jest.fn(),
            };
            return observer;
        });
};

const mockDOMRect = (width: number, height: number, elementIdentifier: string) => {
    Element.prototype.getBoundingClientRect = jest.fn(function patata(this: HTMLElement) {
        if (this.getAttribute('data-component') === elementIdentifier) {
            return getDOMRect(width, height);
        }
        return getDOMRect(0, 0);
    });
};

const getDOMRect = (width: number, height: number): DOMRect => ({
    width,
    height,
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    x: 0,
    y: 0,
    toJSON: () => {},
});

describe('value type column', () => {
    let props: ValueTypeColumnProps;

    beforeAll(() => {
        Element.prototype.getBoundingClientRect = mockedGetBoundingClientRect;
        setupResizeObserverMock();
        mockDOMRect(40, 100, 'select-list-scrollable-container');
    });

    afterAll(() => {
        Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
    });

    beforeEach(() => {
        props = {
            localize: jest.fn().mockImplementation((_, v) => v),
            onCellChange: jest.fn(),
            rowData: { _id: '123', valueType1: 'property' } as any,
            isDisabled: false,
            columnId: 'valueType1',
            parameters: fakeParameters,
        };
    });

    describe('render', () => {
        it('should render with default value with two options if it is bound to valueType1', () => {
            const { baseElement } = render(<ValueTypeColumn {...props} />);
            const dropdownInput = baseElement.querySelector(
                'input[data-testid="e-condition-table-valueType1-column-123"]',
            )!;
            expect(dropdownInput).toHaveValue('Property');
            const options = baseElement.querySelectorAll('[data-component="option"]');
            expect(options).toHaveLength(1);
            expect(options[0]).toHaveTextContent('Property');
        });

        it('should be disabled if no parameters are available and it is bound to valueType1', () => {
            props.parameters = undefined;
            const { queryByTestId } = render(<ValueTypeColumn {...props} />);
            expect(queryByTestId('e-condition-table-valueType1-column-123')!).toHaveTextContent('Property');
        });

        it('should render with default value with three options if it is bound to valueType2', async () => {
            props.columnId = 'valueType2';
            console.log('props:', props);
            const { baseElement, findByRole } = render(<ValueTypeColumn {...props} />);
            const input = await findByRole('combobox');
            await userEvent.click(input);
            await waitFor(() => {
                const options = baseElement.querySelectorAll('[data-component="option"]');
                expect(options).toHaveLength(3);
                expect(options[0]).toHaveTextContent('Property');
                expect(options[1]).toHaveTextContent('Parameter');
                expect(options[2]).toHaveTextContent('Constant');
            });
        });
    });

    describe('interactions', () => {
        it('should call onCellChange on selection', async () => {
            const { baseElement } = render(<ValueTypeColumn {...props} />);
            const dropdownInput = baseElement.querySelector(
                'input[data-testid="e-condition-table-valueType1-column-123"]',
            )!;
            await userEvent.click(dropdownInput);
            await waitFor(() => {
                expect(baseElement.querySelectorAll('[data-component="option"]')).toHaveLength(2);
            });
            expect(props.onCellChange).not.toHaveBeenCalled();
            await userEvent.click(baseElement.querySelectorAll('[data-component="option"]')[1]);
            await waitFor(() => {
                expect(props.onCellChange).toHaveBeenCalledWith({
                    columnId: 'valueType1',
                    rowData: props.rowData,
                    rowId: '123',
                    value: 'parameter',
                });
            });
        });
    });
});
