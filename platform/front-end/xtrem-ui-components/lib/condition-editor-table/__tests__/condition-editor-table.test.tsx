import React from 'react';
import type { ConditionEditorTableProps } from '../condition-editor-table-types';
import { render, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { fakeFetchItems, fakeParameters, getTreeDropdown } from '../../__tests__/common';
import { startCase } from 'lodash';
import { ConditionEditorTableComponent } from '../condition-editor-table';
import '@testing-library/jest-dom';

describe('condition editor table', () => {
    let props: ConditionEditorTableProps;

    beforeEach(() => {
        props = {
            carbonLocale: () => ({}),
            locale: 'en-US',
            localizeEnumMember: (_, member) => startCase(member),
            localize: (_, v) => v,
            onChange: jest.fn(),
            value: [],
            node: 'SalesInvoice',
            parameters: fakeParameters,
            fetchItems: fakeFetchItems,
        };
    });

    describe('render', () => {
        it('should render empty condition editor table', () => {
            const { queryByTestId, baseElement } = render(<ConditionEditorTableComponent {...props} />);
            expect(queryByTestId('add-item-button')).toBeInTheDocument();
            const headers = baseElement.querySelectorAll('[data-element="flat-table-header"]');
            expect(headers).toHaveLength(6);
            expect(headers[0]).toHaveTextContent('Conjunction');
            expect(headers[1]).toHaveTextContent('Value Type 1');
            expect(headers[2]).toHaveTextContent('Value 1');
            expect(headers[3]).toHaveTextContent('Operator');
            expect(headers[4]).toHaveTextContent('Value Type 2');
            expect(headers[5]).toHaveTextContent('Value 2');
        });

        it('should render EnumValueInput as a MultiSelect', async () => {
            props.value = [
                {
                    _id: '1',
                    conjunction: 'and',
                    valueType1: 'property',
                    valueType2: 'constant',
                    operator: 'equals',
                    value1: {
                        label: 'Display Status',
                        data: {
                            name: 'displayStatus',
                            type: 'Enum',
                            kind: 'SCALAR',
                            enumValues: ['draft', 'posted'],
                        },
                        id: 'displayStatus',
                    },
                    value2: null,
                },
            ];

            const { getByTestId, container } = render(<ConditionEditorTableComponent {...props} />);
            const input = await waitFor(() => getByTestId('e-condition-table-value2-1'));
            expect(input).toBeInTheDocument();

            await userEvent.click(input);

            await waitFor(() => {
                const multiselect = container.querySelector('ul[role="listbox"][aria-multiselectable="true"]');
                expect(multiselect).toBeInTheDocument();
            });
        });
    });

    describe('interactions', () => {
        it('should add the first row with default values', async () => {
            const { queryByTestId, baseElement } = render(<ConditionEditorTableComponent {...props} />);
            const addButton = queryByTestId('add-item-button')!;
            expect(addButton).toBeInTheDocument();
            await userEvent.click(addButton);
            await waitFor(() => {
                expect(addButton).not.toBeInTheDocument();
            });

            const valueTypeColumn = baseElement.querySelector(
                'input[data-testid="e-condition-table-valueType1-column-1"]',
            )!;
            const conjunctionColumn = baseElement.querySelector(
                'input[data-testid="e-condition-table-conjunction-column-1"]',
            )!;
            expect(valueTypeColumn).toHaveValue('Property');
            expect(conjunctionColumn).toHaveValue('If');
            expect(conjunctionColumn).toBeDisabled();
            expect(queryByTestId('e-condition-table-value1-1')).not.toBeDisabled();
            expect(queryByTestId('e-condition-table-value2-1')).toBeDisabled();
        });

        it('should remove row', async () => {
            const { queryByTestId } = render(<ConditionEditorTableComponent {...props} />);
            const addButton = queryByTestId('add-item-button')!;
            expect(addButton).toBeInTheDocument();
            await userEvent.click(addButton);
            await waitFor(() => {
                expect(addButton).not.toBeInTheDocument();
            });
            const removeRowButton = queryByTestId('flat-table-remove-button')!;
            expect(removeRowButton).toBeInTheDocument();
            await userEvent.click(removeRowButton);
            await waitFor(() => {
                expect(queryByTestId('add-item-button')).toBeInTheDocument();
            });
        });

        it('should handle data entry to a row', async () => {
            const { queryByTestId, baseElement } = render(<ConditionEditorTableComponent {...props} />);
            const addButton = queryByTestId('add-item-button')!;
            expect(addButton).toBeInTheDocument();
            await userEvent.click(addButton);
            let value1: HTMLElement;
            await waitFor(() => {
                value1 = queryByTestId('e-condition-table-value1-1')!;
                expect(value1).toBeInTheDocument();
            });
            await userEvent.click(value1!);
            const value1dropdown = getTreeDropdown(value1!, baseElement)!;
            await waitFor(() => {
                expect(value1dropdown.querySelectorAll('li')).not.toHaveLength(0);
            });
            const valueType2 = baseElement.querySelector('input[data-testid="e-condition-table-valueType2-column-1"]')!;
            expect(queryByTestId('e-widget-editor-filter-type-1')).toBeDisabled();
            expect(valueType2).toBeDisabled();
            const grossTotal = queryByTestId('e-tree-view-container-label-grossTotal')?.querySelector('button')!;
            await userEvent.click(grossTotal);
            await waitFor(() => {
                expect(value1).toHaveValue('Gross Total');
                expect(queryByTestId('e-widget-editor-filter-type-1')).not.toBeDisabled();
                expect(valueType2).not.toBeDisabled();
            });
            expect(queryByTestId('e-widget-editor-filter-type-1')).toHaveValue('Equals');
            await userEvent.click(valueType2);
            //
        });
    });
});
