import React from 'react';
import type { ConjunctionColumnProps } from '../conjunction-column';
import { ConjunctionColumn } from '../conjunction-column';
import { render, waitFor } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { mockedGetBoundingClientRect, originalGetBoundingClientRect } from '../../__tests__/common';

const setupResizeObserverMock = () => {
    if (!window) {
        return;
    }
    window.ResizeObserver =
        window.ResizeObserver ||
        jest.fn().mockImplementation((callback: ResizeObserverCallback) => {
            let hasCalledCallback = false;
            const observer: ResizeObserver = {
                disconnect: jest.fn(),
                // observe mock needs to actually call the callback straight away, as this is what a real ResizeObserver does
                // and this behaviour is needed for the FixedNavigationBarContextProvider to work properly.
                // Note that we must only call the callback once per ResizeObserver instance, to avoid stack overflows in
                // react-virtual.
                observe: jest.fn((target: Element) => {
                    if (!hasCalledCallback) {
                        hasCalledCallback = true;
                        callback([{ target } as ResizeObserverEntry], observer);
                    }
                }),
                unobserve: jest.fn(),
            };
            return observer;
        });
};

const mockDOMRect = (width: number, height: number, elementIdentifier: string) => {
    Element.prototype.getBoundingClientRect = jest.fn(function patata(this: HTMLElement) {
        if (this.getAttribute('data-component') === elementIdentifier) {
            return getDOMRect(width, height);
        }
        return getDOMRect(0, 0);
    });
};

const getDOMRect = (width: number, height: number): DOMRect => ({
    width,
    height,
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    x: 0,
    y: 0,
    toJSON: () => {},
});

describe('conjunction column', () => {
    let props: ConjunctionColumnProps;

    beforeAll(() => {
        Element.prototype.getBoundingClientRect = mockedGetBoundingClientRect;
        setupResizeObserverMock();
        mockDOMRect(40, 100, 'select-list-scrollable-container');
    });

    afterAll(() => {
        Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
    });

    beforeEach(() => {
        props = {
            localize: jest.fn().mockImplementation((_, v) => v),
            onCellChange: jest.fn(),
            rowIndex: 4,
            rowData: { _id: '123', conjunction: 'and' } as any,
            isDisabled: false,
        };
    });

    describe('render', () => {
        it('should render with default value', () => {
            const { baseElement } = render(<ConjunctionColumn {...props} />);
            const dropdownInput = baseElement.querySelector(
                'input[data-testid="e-condition-table-conjunction-column-123"]',
            )!;
            expect(dropdownInput).toHaveValue('And');
        });

        it('should render with If in the first row', () => {
            props.rowIndex = 0;
            const { baseElement } = render(<ConjunctionColumn {...props} />);
            const dropdownInput = baseElement.querySelector(
                'input[data-testid="e-condition-table-conjunction-column-123"]',
            )!;
            expect(dropdownInput).toHaveValue('If');
        });

        it('should render with Or value', () => {
            props.rowData.conjunction = 'or';
            const { baseElement } = render(<ConjunctionColumn {...props} />);
            const dropdownInput = baseElement.querySelector(
                'input[data-testid="e-condition-table-conjunction-column-123"]',
            )!;
            expect(dropdownInput).toHaveValue('Or');
        });
    });

    describe('interactions', () => {
        it('should call onCellChange on selection', async () => {
            const { baseElement } = render(<ConjunctionColumn {...props} />);
            const dropdownInput = baseElement.querySelector(
                'input[data-testid="e-condition-table-conjunction-column-123"]',
            )!;
            await userEvent.click(dropdownInput);
            await waitFor(() => {
                expect(baseElement.querySelectorAll('[data-component="option"]')).toHaveLength(2);
            });
            expect(props.onCellChange).not.toHaveBeenCalled();
            await userEvent.click(baseElement.querySelectorAll('[data-component="option"]')[1]);
            await waitFor(() => {
                expect(props.onCellChange).toHaveBeenCalledWith({
                    columnId: 'conjunction',
                    rowData: props.rowData,
                    rowId: '123',
                    value: 'or',
                });
            });
        });
    });
});
