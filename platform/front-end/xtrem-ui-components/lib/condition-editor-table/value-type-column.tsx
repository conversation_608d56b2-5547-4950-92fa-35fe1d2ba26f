import React from 'react';
import type {
    ValueType,
    DefaultConditionEditorCols,
    OnCellChange,
    FilterParameter,
} from './condition-editor-table-types';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import { Select, Option } from 'carbon-react/esm/components/select';
import type { LocalizeFunction } from '@sage/xtrem-shared';
import Box from 'carbon-react/esm/components/box';
import Typography from 'carbon-react/esm/components/typography';

export interface ValueTypeColumnProps<Cols extends ColDef[] = [], E = unknown> {
    isDisabled?: boolean;
    onCellChange: OnCellChange<Cols, E>;
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['data'][number];
    localize: LocalizeFunction;
    columnId: 'valueType1' | 'valueType2';
    parameters?: FilterParameter[];
}

export function ValueTypeColumn<Cols extends ColDef[] = [], E = unknown>({
    isDisabled,
    onCellChange,
    rowData,
    localize,
    columnId,
    parameters,
}: ValueTypeColumnProps<Cols, E>): React.ReactElement {
    const propertyLiteral = localize('@sage/xtrem-ui-components/condition-value-type-property', 'Property');
    const hasParameters = parameters && parameters.length > 0;
    const hasConstant = columnId === 'valueType2';

    if (!hasParameters && !hasConstant) {
        return (
            <div className="e-condition-table-value-type-column">
                <div data-testid={`e-condition-table-${columnId}-column-${rowData._id}`}>
                    <Box display="flex" alignItems="center" justifyContent="flex-start">
                        <Typography variant="p" m={0} paddingLeft="8px">
                            {propertyLiteral}
                        </Typography>
                    </Box>
                </div>
            </div>
        );
    }

    return (
        <div className="e-condition-table-value-type-column">
            <Select
                disabled={isDisabled}
                openOnFocus={true}
                data-testid={`e-condition-table-${columnId}-column-${rowData._id}`}
                onChange={({ target: { value } }): void => {
                    onCellChange({
                        columnId,
                        rowId: rowData._id,
                        value: value as ValueType | undefined,
                        rowData,
                    });
                }}
                size="small"
                value={rowData[columnId] ?? ''}
            >
                <Option text={propertyLiteral} value="property" key="property" />
                {hasParameters && (
                    <Option
                        text={localize('@sage/xtrem-ui-components/condition-value-type-parameter', 'Parameter')}
                        value="parameter"
                        key="parameter"
                    />
                )}
                {hasConstant && (
                    <Option
                        text={localize('@sage/xtrem-ui-components/condition-value-type-constant', 'Constant')}
                        value="constant"
                        key="constant"
                    />
                )}
            </Select>
        </div>
    );
}
