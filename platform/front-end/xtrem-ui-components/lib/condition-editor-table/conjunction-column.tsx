import React from 'react';
import type { ConjunctionType, DefaultConditionEditorCols, OnCellChange } from './condition-editor-table-types';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import { Select, Option } from 'carbon-react/esm/components/select';
import type { LocalizeFunction } from '@sage/xtrem-shared';
import { noop } from 'lodash';

export interface ConjunctionColumnProps<Cols extends ColDef[] = [], E = unknown> {
    isDisabled?: boolean;
    onCellChange: OnCellChange<Cols, E>;
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['data'][number];
    localize: LocalizeFunction;
    rowIndex: number;
}

export function ConjunctionColumn<Cols extends ColDef[] = [], E = unknown>({
    isDisabled,
    onCellChange,
    rowData,
    localize,
    rowIndex,
}: ConjunctionColumnProps<Cols, E>): React.ReactElement {
    return (
        <div className="e-condition-table-conjunction-column">
            {rowIndex === 0 && (
                <Select
                    disabled={true}
                    openOnFocus={true}
                    data-testid={`e-condition-table-conjunction-column-${rowData._id}`}
                    size="small"
                    value="if"
                    onChange={noop}
                >
                    <Option
                        text={localize('@sage/xtrem-ui-components/condition-conjunction-if', 'If')}
                        value="if"
                        key="if"
                    />
                </Select>
            )}
            {rowIndex !== 0 && (
                <Select
                    disabled={isDisabled}
                    openOnFocus={true}
                    data-testid={`e-condition-table-conjunction-column-${rowData._id}`}
                    onChange={({ target: { value } }): void => {
                        onCellChange({
                            columnId: 'conjunction',
                            rowId: rowData._id,
                            value: value as ConjunctionType | undefined,
                            rowData,
                        });
                    }}
                    size="small"
                    value={rowData.conjunction ?? ''}
                >
                    <Option
                        text={localize('@sage/xtrem-ui-components/condition-conjunction-and', 'And')}
                        value="and"
                        key="and"
                    />
                    <Option
                        text={localize('@sage/xtrem-ui-components/condition-conjunction-or', 'Or')}
                        value="or"
                        key="or"
                    />
                </Select>
            )}
        </div>
    );
}
