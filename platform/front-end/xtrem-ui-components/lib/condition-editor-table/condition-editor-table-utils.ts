/* eslint-disable react/no-unstable-nested-components */
import type { LocalizeFunction } from '@sage/xtrem-shared';
import Box from 'carbon-react/esm/components/box';
import { isEqual, isNil, memoize, set, sortBy } from 'lodash';
import styled from 'styled-components';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import { validateScalarValue } from '../table-validation-utils';
import { isEnumField } from '../utils';
import type {
    ActionExtractor,
    ConditionEditorTableProps,
    ConditionEditorTableState,
    ConditionEditorTableValidations,
    ConditionTableEditorAction,
    DefaultConditionEditorCols,
    FilterParameter,
} from './condition-editor-table-types';

export const SplitBox = styled(Box)`
    & > div {
        flex: 1;
    }
`;
export const isCompleteRow = <Cols extends ColDef[] = [], E = unknown>(
    row: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['data'][number],
    validations: ConditionEditorTableValidations<Cols>,
): boolean =>
    row.conjunction != null &&
    row.operator != null &&
    row.value1 != null &&
    row.valueType1 != null &&
    (row.operator === 'empty' || row.operator === 'notEmpty' || (row.value2 != null && row.valueType2 != null)) &&
    !!Object.values(validations[row._id] ?? {}).every(v => v == null);

export const mapConditionToEditorRow = memoize(
    <Cols extends ColDef[] = []>({
        value,
    }: {
        value: NonNullable<ConditionEditorTableProps['value']>;
    }): FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>>['data'] => {
        return value.map(({ conjunction, operator, valueType1, valueType2, value1, value2, ...rest }, index) => {
            return {
                // spread needed in case columns are extended
                ...rest,
                key: String(index + 1),
                _id: String(index + 1),
                property: undefined,
                conjunction,
                operator,
                valueType1,
                valueType2,
                value1,
                value2,
            };
        });
    },
);

export function conditionEditorTableReducer<Cols extends ColDef[] = [], E = unknown>(): (
    localize: LocalizeFunction,
    parameters?: FilterParameter[],
) => (
    state: ConditionEditorTableState<Cols, E>,
    action: ConditionTableEditorAction<Cols, E>,
) => ConditionEditorTableState<Cols, E> {
    return memoize((localize: LocalizeFunction, parameters?: FilterParameter[]) => {
        return (
            state: ConditionEditorTableState<Cols, E>,
            action: ConditionTableEditorAction<Cols, E>,
        ): ConditionEditorTableState<Cols, E> => {
            switch (action.type) {
                case 'DATA_RESET':
                    return handleDataReset<Cols, E>(state, action);
                case 'ROW_ADDED':
                    return handleRowAdded<Cols, E>(state);
                case 'ROW_REMOVED':
                    return handleRowRemoved<Cols, E>(state, action);
                case 'ROW_DRAGGED':
                    return handleRowDragged<Cols, E>(state, action);
                case 'CELL_CHANGED':
                    return handleCellChanged<Cols, E>(state, action, localize, parameters);
                default:
                    return state;
            }
        };
    });
}

function handleRowAdded<Cols extends ColDef[] = [], E = unknown>(
    state: ConditionEditorTableState<Cols, E>,
): ConditionEditorTableState<Cols, E> {
    const newRow: FlatTableProps<UnrestrictedTableCols<DefaultConditionEditorCols, Cols>, E>['data'][number] =
        state.columns.reduce(
            (acc, curr) => {
                acc[curr.id] = undefined as any;
                return acc;
            },
            { _id: String(state.counter + 1) } as FlatTableProps<
                UnrestrictedTableCols<DefaultConditionEditorCols, Cols>,
                E
            >['data'][number],
        );

    newRow.conjunction = 'and';
    newRow.valueType1 = 'property';
    newRow.operator = 'equals';

    return {
        ...state,
        counter: state.counter + 1,
        data: [...state.data, newRow],
    };
}

function handleRowRemoved<Cols extends ColDef[] = [], E = unknown>(
    state: ConditionEditorTableState<Cols, E>,
    action: ActionExtractor<'ROW_REMOVED'>,
): ConditionEditorTableState<Cols, E> {
    return {
        ...state,
        data: state.data.filter(element => element._id !== action.row._id),
    };
}
function handleDataReset<Cols extends ColDef[] = [], E = unknown>(
    state: ConditionEditorTableState<Cols, E>,
    { value }: { value: NonNullable<ConditionEditorTableProps['value']> },
): ConditionEditorTableState<Cols, E> {
    const incompleteRows = state.data.filter(row => !isCompleteRow(row, state.validations));
    const completeRows = mapConditionToEditorRow({
        value,
    });
    const completeRowsLength = completeRows.length;
    const data = [
        ...completeRows,
        ...incompleteRows.map((incompleteRow, index) => ({
            ...incompleteRow,
            _id: String(index + completeRowsLength + 1),
        })),
    ];
    return {
        ...state,
        counter: data.length,
        data,
    };
}

function handleRowDragged<Cols extends ColDef[] = [], E = unknown>(
    state: ConditionEditorTableState<Cols, E>,
    action: ActionExtractor<'ROW_DRAGGED'>,
): ConditionEditorTableState<Cols, E> {
    const orderedRows = sortBy(state.data, item => action.ids.indexOf(item._id));
    return {
        ...state,
        data: orderedRows,
    };
}

function getActualValue<Cols extends ColDef[] = []>({
    columnId,
    value,
}: {
    columnId: UnrestrictedTableCols<DefaultConditionEditorCols, Cols>[number]['id'];
    value: any;
}): any {
    switch (columnId) {
        case '_id':
        case 'key':
        case 'value1':
        case 'value2':
        case 'valueType1':
        case 'valueType2':
            return isNil(value) ? undefined : value;
        case 'conjunction':
            return value || 'and';
        default:
            return value;
    }
}

function handleCellChanged<Cols extends ColDef[] = [], E = unknown>(
    state: ConditionEditorTableState<Cols, E>,
    action: ActionExtractor<'CELL_CHANGED'>,
    localize: ConditionEditorTableProps['localize'],
    parameters?: FilterParameter[],
): ConditionEditorTableState<Cols, E> {
    const {
        changes: { rowId, columnId, value, rowData },
    } = action;

    const isParameter = rowData.valueType1 === 'parameter';
    const isEnum = !isParameter && isEnumField(rowData.value1?.data);
    // If the value is a parameter, the type is the type of the parameter
    const type = isParameter ? parameters?.find(p => p.name === rowData.value1)?.type : rowData.value1?.data?.type;

    const validationMessage =
        rowData.valueType2 === 'constant' && (columnId === 'value2' || rowData.value2)
            ? validateScalarValue({
                  type,
                  value: columnId === 'value2' ? value : rowData.value2,
                  enumValues: isEnum ? rowData.value1.data.enumValues : [],
                  localize,
              })
            : undefined;

    const data = state.data.map(curr => {
        if (curr._id !== rowId || isEqual(curr[columnId], value)) {
            return curr;
        }

        // If the property or value type changes, reset the value
        if (columnId === 'valueType1') {
            curr.value1 = null;
            curr.operator = 'equals';
            curr.valueType2 = 'property';
            curr.value2 = null;
        }

        if (columnId === 'value1') {
            curr.operator = 'equals';
            curr.valueType2 = 'property';
            curr.value2 = null;
        }

        if (columnId === 'valueType2') {
            curr.value2 = null;
        }

        if (columnId === 'operator' && (value === 'empty' || value === 'notEmpty')) {
            curr.valueType2 = 'constant';
        }

        if (!(columnId === 'value1' && value === undefined)) {
            // set actual value
            set(curr, columnId, getActualValue({ columnId, value }));
        }
        if (!(columnId === 'value2' && value === undefined)) {
            // set actual value
            set(curr, columnId, getActualValue({ columnId, value }));
        }

        return curr;
    });

    const changedRow: any = data.find(d => d._id === rowData._id);
    const validationResets = Object.keys(changedRow ?? {}).reduce((acc, key) => {
        if (changedRow?.[key] === undefined) {
            set(acc, key, undefined);
        }
        return acc;
    }, {});

    const validations = {
        ...state.validations,
        [rowData._id]: {
            ...state.validations?.[rowData._id],
            [columnId]: validationMessage,
            ...validationResets,
        },
    };

    return {
        ...state,
        data,
        validations,
    };
}
