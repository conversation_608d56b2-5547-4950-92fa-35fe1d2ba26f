import { filterGraphqlMapping, GraphQLTypes, type Dict } from '@sage/xtrem-shared';
import { isEmpty, isNil, isObject } from 'lodash';

export const NUMERIC_TYPES: string[] = [GraphQLTypes.Int, GraphQLTypes.Float, GraphQLTypes.Decimal];

const isValidQueryParameter = (str: string): boolean => {
    try {
        const params = JSON.parse(atob(str));
        return isObject(params);
    } catch {
        return false;
    }
};

export const getInternalPathFromExternal = (href = window.location.href): string => {
    const url = new URL(href);
    let queryParams = Array.from(url.searchParams.keys())
        .filter(key => {
            // INFO: Query paremeters which should be excluded from base64 encoding, such as the
            //       pendo designer can be added here.
            const exceptions = ['pendo-designer'];
            return !exceptions.includes(key);
        })
        .reduce((prevValue, key) => {
            return { ...prevValue, [key]: url.searchParams.get(key) };
        }, {} as Dict<any>);

    const path = href
        .toString()
        .replace(`${window.location.origin}/`, '')
        .replace('index.html', '')
        .replace('#', '')
        .replace(/(\?[^?#]*)$/, '');

    const components = path.split('/');
    const queryParameterSegment = components[3] || '';

    if (isValidQueryParameter(queryParameterSegment)) {
        queryParams = {
            ...queryParams,
            ...JSON.parse(atob(queryParameterSegment)),
        };
    } else if (!isNil(queryParameterSegment) && !isEmpty(queryParameterSegment)) {
        queryParams._id = String(queryParameterSegment);
    }

    if (!isEmpty(queryParams)) {
        const encodedParams = btoa(JSON.stringify(queryParams));
        if (components.length === 3) {
            components.push(encodedParams);
        } else {
            components[3] = encodedParams;
        }
    }

    return components.join('/');
};

export const filterableGraphqlTypes = Object.entries(filterGraphqlMapping).reduce<(keyof typeof GraphQLTypes)[]>(
    (acc, [k, v]) => {
        if (v !== undefined) {
            acc.push(k as keyof typeof GraphQLTypes);
        }
        return acc;
    },
    [],
);

export const isEnumField = (data?: { type?: string; kind?: string; enumValues?: string[] }): boolean =>
    !!data &&
    (data.type?.toLowerCase() === 'enum' ||
        data.kind?.toLowerCase() === 'enum' ||
        (data.kind?.toLowerCase() === 'scalar' && Array.isArray(data.enumValues)));
