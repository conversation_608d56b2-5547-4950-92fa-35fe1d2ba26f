import type { Dict, TreeElement } from '@sage/xtrem-shared';
import { FlatTable } from '../flat-table/flat-table';
import { GridColumn, GridRow } from '../responsive-grid/responsive-grid';
import * as React from 'react';
import type { FlatTableProps } from '../flat-table/flat-table-types';
import { set, sortBy } from 'lodash';
import type { Order, SortConditionEditorProps, TableCols } from './sort-condition-editor-types';
import { OrderCellRenderer } from './order-cell-renderer';
import { getOrderByTranslations } from './sort-condition-utils';
import { PropertyCellRenderer } from './property-cell-renderer';

export function SortConditionEditor<ListPropertyItem extends TreeElement<any>>({
    canAddNewLines,
    canDrag = true,
    canRemoveLines = true,
    getPropertySubtitle,
    gridGutter,
    localize,
    onChange,
    properties,
    propertyReadOnly = false,
    value,
}: SortConditionEditorProps<ListPropertyItem>): React.ReactElement {
    const orderByTranslations = getOrderByTranslations(localize);

    const onCellChange = React.useCallback<
        <P extends FlatTableProps<TableCols<ListPropertyItem>>['columns'][number]['id']>(args: {
            columnId: P;
            rowId: string;
            value: FlatTableProps<TableCols<ListPropertyItem>>['data'][number][P];
        }) => void
    >(
        ({ columnId, rowId, value }) => {
            setTableDefinition(current => {
                return {
                    ...current,
                    data: current.data.reduce<FlatTableProps<TableCols<ListPropertyItem>>['data']>((acc, curr) => {
                        if (curr._id === rowId) {
                            set(curr, columnId, value || undefined);
                            // Set type and path for given property
                            if (curr.property?.id) {
                                const prop = properties?.[curr.property.id];
                                curr.path = prop?.id;
                                curr.labelPath = prop?.labelPath;
                            }
                        }
                        acc.push(curr);
                        return acc;
                    }, []),
                };
            });
        },
        [properties],
    );

    // Counter to keep track of table IDs
    const counterRef = React.useRef(Object.keys(value ?? {}).length);

    const onRowAdded = React.useCallback<NonNullable<FlatTableProps<TableCols<ListPropertyItem>>['onRowAdded']>>(() => {
        setTableDefinition(current => {
            counterRef.current += 1;
            const newRow = {
                _id: String(counterRef.current),
                property: undefined,
                order: undefined,
                type: undefined,
                path: undefined,
                labelPath: undefined,
                node: undefined,
            } as const;
            return {
                ...current,
                data: [...current.data, newRow],
            };
        });
    }, []);

    const onRowRemoved = React.useCallback<NonNullable<FlatTableProps<TableCols<ListPropertyItem>>['onRowRemoved']>>(
        (removedRow): void => {
            setTableDefinition(current => {
                return {
                    ...current,
                    data: current.data.filter(element => element._id !== removedRow._id),
                };
            });
        },
        [],
    );

    const onRowDrag = React.useCallback<NonNullable<FlatTableProps<TableCols<ListPropertyItem>>['onRowDrag']>>(ids => {
        setTableDefinition(current => {
            const orderedRows = sortBy(current.data, item => {
                return ids.indexOf(item._id);
            });
            return {
                ...current,
                data: orderedRows,
            };
        });
    }, []);

    const columns = React.useMemo<FlatTableProps<TableCols<ListPropertyItem>>['columns']>(() => {
        return [
            { id: 'path', isHidden: !propertyReadOnly, header: { name: 'Path' } },
            {
                id: 'property',
                header: { name: localize('@sage/xtrem-ui-components/property', 'Property'), width: 400 },
                // eslint-disable-next-line react/no-unstable-nested-components
                cellRenderer: ({ rowData, data: allData }): React.ReactElement => (
                    <PropertyCellRenderer
                        readOnly={propertyReadOnly}
                        rowData={rowData}
                        localize={localize}
                        onCellChange={onCellChange}
                        allData={allData}
                        properties={properties}
                        getPropertySubtitle={getPropertySubtitle}
                    />
                ),
            },
            {
                id: 'order',
                header: { name: localize('@sage/xtrem-ui-components/sort-order', 'Order'), width: 300 },
                // eslint-disable-next-line react/no-unstable-nested-components
                cellRenderer: ({ rowData }): React.ReactElement => (
                    <OrderCellRenderer
                        rowData={rowData}
                        localize={localize}
                        onCellChange={onCellChange}
                        orderByTranslations={orderByTranslations}
                    />
                ),
            },
            { id: 'labelPath', isHidden: true, header: { name: 'Label path' } },
        ];
    }, [localize, properties, onCellChange, orderByTranslations, getPropertySubtitle, propertyReadOnly]);

    const data = React.useMemo<FlatTableProps<TableCols<ListPropertyItem>>['data']>(() => {
        return Object.keys(value ?? []).map((id: string, index: number) => {
            const propertyDetails = properties[id];
            return {
                _id: String(index + 1),
                property: propertyDetails,
                order: value[id],
                type: propertyDetails.data.type,
                path: id,
                labelPath: propertyDetails.labelPath,
                node: propertyDetails.data.node,
            };
        });
    }, [value, properties]);

    const addButtonText = React.useMemo<string>(
        () => localize('@sage/xtrem-ui-components/add-sort-condition', 'Add a sort condition'),
        [localize],
    );

    const [tableDefinition, setTableDefinition] = React.useState<FlatTableProps<TableCols<ListPropertyItem>>>({
        addButtonText,
        isAddButtonHidden: Object.keys(properties).length === 0,
        emptyStateText:
            Object.keys(properties).length === 0
                ? localize(
                      '@sage/xtrem-ui-components/widget-editor-no-sortable-properties',
                      'You cannot sort the current values. You can select different data or continue without sorting.',
                  )
                : undefined,
        canDrag,
        onRowDrag,
        canAddNewLines,
        canRemoveLines,
        onRowRemoved,
        onRowAdded,
        columns,
        data,
    });

    React.useEffect(() => {
        onChange(
            tableDefinition.data
                .filter(p => p.property != null && p.order != null && p.path != null && p.labelPath != null)
                .reduce((prevValue: Dict<Order>, { order, path }: (typeof tableDefinition.data)[number]) => {
                    if (path && order) {
                        prevValue[path] = order;
                    }
                    return prevValue;
                }, {} as Dict<Order>),
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tableDefinition.data]);

    return (
        <GridRow columns={8} gutter={gridGutter} margin={0} verticalMargin={0}>
            <GridColumn columnSpan={8}>
                <FlatTable
                    {...tableDefinition}
                    canAddNewLines={canAddNewLines}
                    canRemoveLines={canRemoveLines}
                    actionsText={localize('@sage/xtrem-ui-components/actions', 'Actions')}
                />
            </GridColumn>
        </GridRow>
    );
}
