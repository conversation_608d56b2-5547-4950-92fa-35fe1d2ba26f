import * as React from 'react';
import { FilterableSelect, OptionRow } from 'carbon-react/esm/components/select';
import type { RowDataType, TableCols } from './sort-condition-editor-types';
import type { Dict, LocalizeFunction, TreeElement } from '@sage/xtrem-shared';
import type { FlatTableProps } from '../flat-table/flat-table-types';
import { PropertyTableHeader } from '../property-template-header/property-table-header';

export interface PropertyCellRendererProps<ListPropertyItem extends TreeElement> {
    localize: LocalizeFunction;
    onCellChange: <P extends FlatTableProps<TableCols<ListPropertyItem>>['columns'][number]['id']>(args: {
        columnId: P;
        rowId: string;
        value: FlatTableProps<TableCols<ListPropertyItem>>['data'][number][P];
    }) => void;
    rowData: RowDataType<ListPropertyItem>;
    allData: Array<RowDataType<ListPropertyItem>>;
    properties: Dict<ListPropertyItem>;
    getPropertySubtitle?: (property: ListPropertyItem) => string | null;
    readOnly?: boolean;
}

export function PropertyCellRenderer<ListPropertyItem extends TreeElement>({
    rowData,
    localize,
    onCellChange,
    properties,
    allData,
    getPropertySubtitle,
    readOnly = false,
}: PropertyCellRendererProps<ListPropertyItem>): React.ReactElement {
    return (
        <FilterableSelect
            readOnly={readOnly}
            multiColumn={true}
            tableHeader={<PropertyTableHeader localize={localize} />}
            openOnFocus={true}
            data-testid={`e-widget-editor-sorting-property-${rowData._id}`}
            onChange={({ target: { value } }: { target: { value: string } }): void => {
                onCellChange({ columnId: 'property', rowId: rowData._id, value: properties[value] || null });
            }}
            placeholder={localize('@sage/xtrem-ui-components/select-property', 'Select property...')}
            size="small"
            // @ts-expect-error value is nullable
            value={rowData.property?.id ?? null}
        >
            {Object.keys(properties)
                .filter(
                    p =>
                        !allData
                            .filter(item => item.property && item._id !== rowData._id)
                            .map(item => item.path)
                            .includes(p),
                )
                .map(p => {
                    return (
                        <OptionRow text={properties[p].label} value={p} key={p}>
                            <td
                                width="50%"
                                style={{
                                    overflow: 'hidden',
                                    whiteSpace: 'pre-line',
                                    maxWidth: 0,
                                }}
                            >
                                {properties[p].label}
                            </td>
                            {getPropertySubtitle && (
                                <td
                                    width="50%"
                                    style={{
                                        overflow: 'hidden',
                                        whiteSpace: 'pre-line',
                                        maxWidth: 0,
                                        textAlign: 'end',
                                    }}
                                >
                                    {getPropertySubtitle(properties[p])}
                                </td>
                            )}
                        </OptionRow>
                    );
                })}
        </FilterableSelect>
    );
}
