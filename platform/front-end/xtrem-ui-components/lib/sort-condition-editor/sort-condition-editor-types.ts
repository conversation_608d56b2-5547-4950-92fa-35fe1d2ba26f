import type { Dict, LocalizeFunction, TreeElement } from '@sage/xtrem-shared';

export type Order = 'ascending' | 'descending';

export type TableCols<ListPropertyItem extends TreeElement> = [
    { id: 'property'; type?: ListPropertyItem },
    { id: 'order'; type?: Order },
    { id: 'path'; type?: string },
    { id: 'labelPath'; type?: string },
];

export type RowDataType<ListPropertyItem extends TreeElement> = {
    _id: string;
    order?: Order;
    path?: string;
    labelPath?: string;
    property?: ListPropertyItem;
};

export interface SortConditionEditorProps<ListPropertyItem extends TreeElement> {
    canAddNewLines?: boolean;
    canDrag?: boolean;
    canRemoveLines?: boolean;
    getPropertySubtitle?: (property: ListPropertyItem) => string | null;
    gridGutter: number;
    localize: LocalizeFunction;
    onChange: (newValue: Dict<Order>) => void;
    properties: Dict<ListPropertyItem>;
    propertyReadOnly?: boolean;
    value: Dict<Order>;
}
