import * as React from 'react';
import { FilterableSelect, Option } from 'carbon-react/esm/components/select';
import type { Order, RowDataType, TableCols } from './sort-condition-editor-types';
import type { LocalizeFunction, TreeElement } from '@sage/xtrem-shared';
import type { FlatTableProps } from '../flat-table/flat-table-types';

export interface OrderCellRendererProps<ListPropertyItem extends TreeElement> {
    localize: LocalizeFunction;
    orderByTranslations: Record<Order, string>;
    onCellChange: <P extends FlatTableProps<TableCols<ListPropertyItem>>['columns'][number]['id']>(args: {
        columnId: P;
        rowId: string;
        value: FlatTableProps<TableCols<ListPropertyItem>>['data'][number][P];
    }) => void;
    rowData: RowDataType<ListPropertyItem>;
}

export function OrderCellRenderer<ListPropertyItem extends TreeElement>({
    rowData,
    localize,
    orderByTranslations,
    onCellChange,
}: OrderCellRendererProps<ListPropertyItem>): React.ReactElement {
    return (
        <FilterableSelect
            openOnFocus={true}
            data-testid={`e-widget-editor-sorting-order-${rowData._id}`}
            onChange={({ target: { value } }): void => {
                onCellChange({
                    columnId: 'order',
                    rowId: rowData._id,
                    value: value as Order | undefined,
                });
            }}
            placeholder={localize('@sage/xtrem-ui-components/select-sort-order', 'Select sort order...')}
            size="small"
            value={rowData.order ?? ''}
        >
            {Object.entries(orderByTranslations).map(([order, translatedOrder], index) => (
                <Option text={translatedOrder} value={order} key={order ?? index} />
            ))}
        </FilterableSelect>
    );
}
