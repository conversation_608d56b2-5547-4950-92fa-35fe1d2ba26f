import type { LocalizeFunction } from '@sage/xtrem-shared';
import type { Order } from './sort-condition-editor-types';
import { memoize } from 'lodash';

export const getOrderByTranslations = memoize(
    (localize: LocalizeFunction): Record<Order, string> => ({
        ascending: localize('@sage/xtrem-ui-components/ascending', 'Ascending'),
        descending: localize('@sage/xtrem-ui-components/descending', 'Descending'),
    }),
);
