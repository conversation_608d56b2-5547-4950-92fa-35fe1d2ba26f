import React from 'react';
import { render, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SortConditionEditor } from '../sort-condition-editor';
import '@testing-library/jest-dom';

const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect;

const setupResizeObserverMock = () => {
    if (!window) {
        return;
    }
    window.ResizeObserver =
        window.ResizeObserver ||
        jest.fn().mockImplementation((callback: ResizeObserverCallback) => {
            let hasCalledCallback = false;
            const observer: ResizeObserver = {
                disconnect: jest.fn(),
                // observe mock needs to actually call the callback straight away, as this is what a real ResizeObserver does
                // and this behaviour is needed for the FixedNavigationBarContextProvider to work properly.
                // Note that we must only call the callback once per ResizeObserver instance, to avoid stack overflows in
                // react-virtual.
                observe: jest.fn((target: Element) => {
                    if (!hasCalledCallback) {
                        hasCalledCallback = true;
                        callback([{ target } as ResizeObserverEntry], observer);
                    }
                }),
                unobserve: jest.fn(),
            };
            return observer;
        });
};

const mockDOMRect = (width: number, height: number, elementIdentifier: string) => {
    Element.prototype.getBoundingClientRect = jest.fn(function patata(this: HTMLElement) {
        if (this.getAttribute('data-component') === elementIdentifier) {
            return getDOMRect(width, height);
        }
        return getDOMRect(0, 0);
    });
};

const getDOMRect = (width: number, height: number): DOMRect => ({
    width,
    height,
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    x: 0,
    y: 0,
    toJSON: () => {},
});

function mockedGetBoundingClientRect(this: any) {
    const dataElement = this.getAttribute('data-element');
    if (dataElement === 'select-list-wrapper') {
        return {
            height: 1080,
            width: 1920,
        } as any;
    }
    return originalGetBoundingClientRect.bind(this)();
}

describe('SortConditionEditor', () => {
    const localize = jest.fn((key, defaultValue) => defaultValue);
    const onChange = jest.fn();

    beforeAll(() => {
        Element.prototype.getBoundingClientRect = mockedGetBoundingClientRect;
        setupResizeObserverMock();
        mockDOMRect(40, 100, 'select-list-scrollable-container');
    });

    afterAll(() => {
        Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
    });

    const properties = {
        '1': { id: '1', label: 'Property 1', data: { type: 'string', node: 'Node 1' }, labelPath: 'Label Path 1' },
        '2': { id: '2', label: 'Property 2', data: { type: 'number', node: 'Node 2' }, labelPath: 'Label Path 2' },
    };

    const defaultProps = {
        canAddNewLines: true,
        canDrag: true,
        canRemoveLines: true,
        getPropertySubtitle: jest.fn(property => `Subtitle for ${property.label}`),
        gridGutter: 10,
        localize,
        onChange,
        properties,
        propertyReadOnly: false,
        value: {},
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('render', () => {
        it('should render without any lines', async () => {
            const { getByText } = render(<SortConditionEditor {...defaultProps} />);

            await waitFor(() => {
                expect(getByText('Add a sort condition')).toBeInTheDocument();
            });
        });

        it('should render with lines', async () => {
            const { getByTestId } = render(<SortConditionEditor {...defaultProps} value={{ '1': 'ascending' }} />);

            await waitFor(() => {
                expect(getByTestId('e-widget-editor-sorting-property-1')).toHaveValue('Property 1');
                expect(getByTestId('e-widget-editor-sorting-order-1')).toHaveValue('Ascending');
            });
        });
    });

    describe('interactions', () => {
        it('should add a new row', async () => {
            const { getByText, getByTestId, queryByTestId } = render(<SortConditionEditor {...defaultProps} />);
            await userEvent.click(getByText('Add a sort condition'));

            await waitFor(() => {
                expect(getByTestId('e-widget-editor-sorting-property-1')).toBeInTheDocument();
                expect(getByTestId('e-widget-editor-sorting-order-1')).toBeInTheDocument();
            });

            expect(queryByTestId('e-widget-editor-sorting-property-1')).toHaveValue('');
            expect(queryByTestId('e-widget-editor-sorting-order-1')).toHaveValue('');
        });

        it('should remove a row', async () => {
            const { getByTestId, queryByTestId } = render(
                <SortConditionEditor {...defaultProps} value={{ '1': 'ascending' }} />,
            );

            await userEvent.click(getByTestId('flat-table-remove-button'));

            await waitFor(() => {
                expect(queryByTestId('e-widget-editor-sorting-property-1')).not.toBeInTheDocument();
            });
        });

        it('should handle property selection', async () => {
            const { getByTestId, baseElement } = render(
                <SortConditionEditor {...defaultProps} value={{ '1': 'ascending' }} />,
            );
            const propertySelect = getByTestId('e-widget-editor-sorting-property-1');

            await userEvent.click(propertySelect);
            const option = await waitFor(() => baseElement.closest('body')!.querySelector('tr[data-index="1"]'));
            await waitFor(() => {
                expect(baseElement.closest('body')!.querySelector('tr[data-index="1"]')).toBeInTheDocument();
            });
            await userEvent.click(option!);
            await waitFor(() => {
                expect(propertySelect).toHaveValue('Property 2');
                expect(onChange).toHaveBeenCalled();
            });
        });

        it('should handle order selection', async () => {
            const { findByTestId, baseElement } = render(
                <SortConditionEditor {...defaultProps} value={{ '1': 'descending' }} />,
            );
            const orderSelect = await findByTestId('e-widget-editor-sorting-order-1');

            await userEvent.click(orderSelect);

            const dropdownId = orderSelect.getAttribute('aria-controls');
            await waitFor(() => {
                expect(baseElement.querySelector(`[id="${dropdownId}"]`)).toBeInTheDocument();
            });
            await userEvent.click(baseElement.querySelector(`[id="${dropdownId}"] li[data-index="0"]`)!);

            await waitFor(() => {
                expect(orderSelect).toHaveValue('Ascending');
                expect(onChange).toHaveBeenCalled();
            });
        });
    });
});
