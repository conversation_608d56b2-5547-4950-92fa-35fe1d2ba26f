import type { DependenciesComparator } from './types';

export const basicDepsComparator: DependenciesComparator = (d1, d2) => {
    if (d1 === d2) return true;

    if (d1.length !== d2.length) return false;

    // eslint-disable-next-line no-restricted-syntax
    for (const [i, element] of d1.entries()) {
        if (element !== d2[i]) {
            return false;
        }
    }

    return true;
};

export const isBrowser =
    typeof window !== 'undefined' && typeof navigator !== 'undefined' && typeof document !== 'undefined';
