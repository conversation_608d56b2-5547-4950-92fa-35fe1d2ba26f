import type { DependencyList } from 'react';

export type DependenciesComparator<Deps extends DependencyList = DependencyList> = (a: Deps, b: Deps) => boolean;

export type EffectCallback = (...args: any[]) => any;

export type EffectHook<
    Callback extends EffectCallback = EffectCallback,
    De<PERSON> extends DependencyList | undefined = DependencyList | undefined,
    RestArgs extends any[] = any[],
> = ((...args: [Callback, Deps, ...RestArgs]) => void) | ((...args: [Callback, Deps]) => void);
