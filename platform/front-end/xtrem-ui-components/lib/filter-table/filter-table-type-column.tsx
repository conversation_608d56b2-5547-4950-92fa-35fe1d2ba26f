import React from 'react';
import { type DefaultFilterCols, type OnCellChange, filterTranslations } from './filter-table-types';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import { GraphQLTypes, filterGraphqlMapping } from '@sage/xtrem-shared';
import type { FilterTypeValue, LocalizeFunction } from '@sage/xtrem-shared';
import { FilterableSelect, Option } from 'carbon-react/esm/components/select';
import type { DefaultPropertyType } from '../types';
import Box from 'carbon-react/esm/components/box';
import Typography from 'carbon-react/esm/components/typography';

const isDateOrDateTimeProperty = (property?: DefaultPropertyType): boolean =>
    property?.data?.type === GraphQLTypes.Date || property?.data?.type === GraphQLTypes.DateTime;

export interface FilterTableTypeColumnProps<Cols extends ColDef[] = [], E = unknown> {
    isDisabled?: boolean;
    localize: LocalizeFunction;
    onCellChange: OnCellChange<Cols, E>;
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['data'][number];
}

export function FilterTableTypeColumn<Cols extends ColDef[] = [], E = unknown>({
    isDisabled,
    localize,
    onCellChange,
    rowData,
}: FilterTableTypeColumnProps<Cols, E>): React.ReactElement {
    const type = rowData.property?.data?.type as GraphQLTypes;
    const kind = rowData.property?.data?.kind;

    let filterableValue: FilterTypeValue[] | undefined;
    if (type !== undefined && filterGraphqlMapping[type] !== undefined) {
        filterableValue = [...filterGraphqlMapping[type]];
    } else if (kind !== undefined) {
        const foundKey = Object.keys(filterGraphqlMapping).find(
            key => key.toLowerCase() === kind.toLowerCase(),
        ) as GraphQLTypes;
        if (foundKey !== undefined) {
            filterableValue = [...(filterGraphqlMapping[foundKey] ?? [])];
        }
    }

    if (rowData.property?.data?.type && rowData.property.data.type === GraphQLTypes.Boolean) {
        return (
            <div data-testid={`e-widget-editor-filter-type-${rowData._id}`}>
                <Box display="flex" alignItems="center" justifyContent="flex-start">
                    <Typography variant="p" m={0} paddingLeft="8px">
                        {localize('@sage/xtrem-ui-components/equals', 'Equals')}
                    </Typography>
                </Box>
            </div>
        );
    }

    const renderOptions = (
        filterableValue: FilterTypeValue[] | undefined,
        localize: LocalizeFunction,
        property?: DefaultPropertyType,
    ): React.JSX.Element[] | undefined => {
        return (
            filterableValue &&
            filterableValue
                ?.map((filterType: FilterTypeValue) => {
                    return (
                        <Option
                            text={filterTranslations(localize)[filterType] ?? ''}
                            value={filterType ?? ''}
                            key={filterType ?? ''}
                        />
                    );
                })
                .concat(
                    isDateOrDateTimeProperty(property)
                        ? [<Option text={filterTranslations(localize).timeFrame} value="timeFrame" key="timeframe" />]
                        : [],
                )
        );
    };

    return (
        <FilterableSelect
            disabled={!rowData.property || isDisabled}
            openOnFocus={true}
            data-testid={`e-widget-editor-filter-type-${rowData._id}`}
            onChange={({ target: { value } }): void => {
                onCellChange({
                    columnId: 'filterType',
                    rowId: rowData._id,
                    value: value as FilterTypeValue | undefined,
                    rowData,
                });
            }}
            placeholder={localize('@sage/xtrem-ui-components/select-filter-type', 'Select filter type...')}
            size="small"
            value={rowData.filterType ?? ''}
        >
            {renderOptions(filterableValue, localize, rowData.property)}
        </FilterableSelect>
    );
}

export const FilterTableTypeColumnMemo = React.memo(FilterTableTypeColumn);
FilterTableTypeColumnMemo.displayName = 'FilterTableTypeColumn';
