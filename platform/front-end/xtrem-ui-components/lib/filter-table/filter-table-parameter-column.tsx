import React from 'react';
import { type DefaultFilterCols, type OnCellChange } from './filter-table-types';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import Switch from 'carbon-react/esm/components/switch';
import { Select, Option } from 'carbon-react/esm/components/select';
import type { LocalizeFunction } from '@sage/xtrem-shared';

export interface FilterTableParameterColumnProps<Cols extends ColDef[] = [], E = unknown> {
    isDisabled?: boolean;
    onCellChange: OnCellChange<Cols, E>;
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['data'][number];
    mode?: 'switch' | 'dropdown';
    localize: LocalizeFunction;
}

export function FilterTableParameterColumn<Cols extends ColDef[] = [], E = unknown>({
    isDisabled,
    onCellChange,
    mode,
    rowData,
    localize,
}: FilterTableParameterColumnProps<Cols, E>): React.ReactElement {
    if (mode === 'dropdown') {
        return (
            <div className="e-filter-table-parameter-dropdown">
                <Select
                    data-testid={`e-widget-editor-filter-parameter-${rowData._id}`}
                    size="small"
                    name="parameter"
                    disabled={isDisabled}
                    onChange={(e: any): void => {
                        onCellChange({
                            columnId: 'parameter',
                            rowData,
                            rowId: rowData._id,
                            value: e.target.value === 'parameter',
                        });
                        // reset filter value
                        onCellChange({
                            columnId: 'filterValue',
                            rowData,
                            rowId: rowData._id,
                            value: undefined,
                        });
                    }}
                    value={rowData.parameter ? 'parameter' : 'filterValue'}
                >
                    <Option
                        text={localize(
                            '@sage/xtrem-ui-components/filter-table-option-enter-manually',
                            'Enter manually',
                        )}
                        value="filterValue"
                    />
                    <Option
                        text={localize(
                            '@sage/xtrem-ui-components/filter-table-option-select-parameter',
                            'Select a parameter',
                        )}
                        value="parameter"
                    />
                </Select>
            </div>
        );
    }
    return (
        <div className="e-filter-table-parameter-switch">
            <Switch
                data-testid={`e-widget-editor-filter-parameter-${rowData._id}`}
                size="small"
                name="parameter"
                disabled={isDisabled}
                onChange={(e): void => {
                    onCellChange({
                        columnId: 'parameter',
                        rowData,
                        rowId: rowData._id,
                        value: e.target.checked,
                    });
                    // reset filter value
                    onCellChange({
                        columnId: 'filterValue',
                        rowData,
                        rowId: rowData._id,
                        value: undefined,
                    });
                }}
                checked={rowData.parameter || false}
            />
        </div>
    );
}

export const FilterTableParameterColumnMemo = React.memo(FilterTableParameterColumn);
FilterTableParameterColumnMemo.displayName = 'FilterTableParameterColumnMemo';
