.e-filter-table-parameter-switch {
    height: 32px;
    padding-top: 4px;
    --fieldSpacing: 24px;
}

.e-widget-editor-range-filter-parameter {
    display: flex;

    &>div {
        flex: 1;
    }
}

.e-filter-pod-container {
    .e-filter-pod-row {
        display: flex;
        flex-flow: row wrap;
        border-radius: 12px;
        justify-content: right;
        background-color: #FFFFFF;
        margin-bottom: 12px;
        > div {
            margin: 12px;
            margin-bottom: 0;
        }
        div[role="presentation"] {
            min-height: var(--sizing500);
        }
        .e-filter-pod-condition-label {
            align-items: flex-start;
            width: 100%;
            font-weight: bold;
            > div {
                margin: 0;
            }
        }
        .e-filter-pod-property {
            width: 100%;
            > div {
                margin: 0;
            }
        }
        .e-filter-pod-filter-parameter-container {
            width: 100%;
            display: flex;
            justify-content: space-between;
            .e-filter-pod-filter-type {
                width: 100%;
                margin-right: 12px;
                display: flex;
                align-items: center;
                > div {
                    margin: 0;
                    width: 100%;
                }
            }
            .e-filter-pod-parameter {
                width: 100%;
                .e-filter-table-parameter-dropdown {
                    > div {
                        margin: 0;
                    }
                }
                
            }
        }
        .e-filter-pod-filter-value {
            width: 100%;
            > div {
                margin: 0;
            }
            .e-widget-editor-range-filter-parameter {
                [data-component="filterable-select"]:first-of-type {
                    margin-right: 12px;
                }
            }
        }
        .e-filter-pod-remove {
            margin-bottom: 12px;
        }
        
    }
    .e-filter-pod-add {
        margin-left: 12px;
        > button {
            margin: 0;
        }

    }
}