import type {
    Dict,
    FilterTypeValue,
    LocalizeEnumFunction,
    LocalizeFunction,
    LocalizeLocale,
    NodeDetails,
} from '@sage/xtrem-shared';
import { memoize } from 'lodash';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import type { BaseTableCols, CarbonLocaleType, DefaultPropertyType } from '../types';

export type FilterProperty<P extends DefaultPropertyType = DefaultPropertyType> = P & {
    filterType: FilterTypeValue | 'timeFrame';
    filterValue: any;
    parameter?: boolean;
};

export interface FilterParameter {
    name: string;
    label?: string;
    type: NodeDetails['type'];
}

export type DefaultFilterCols<P extends DefaultPropertyType = DefaultPropertyType> = [
    ...BaseTableCols,
    { id: 'property'; type?: P },
    { id: 'path'; type: string },
    { id: 'labelPath'; type?: string },
    { id: 'filterType'; type?: FilterTypeValue | 'timeFrame' },
    { id: 'filterValue'; type?: any },
    { id: 'key'; type: string },
    { id: 'labelKey'; type: string },
    { id: 'parameter'; type?: boolean },
    { id: 'title'; type?: string },
    { id: 'formatting'; type?: any },
    { id: 'divisor'; type?: any },
];

/**
 * - `creation` means that when the user selects checks the use parameter input, a new parameter should be created for that filter value
 * - `usage` means that the user can only select from a predefined set of parameter values
 */
export type ParameterHandlingMode = 'creation' | 'usage';

export interface FilterTableProps<P extends DefaultPropertyType = DefaultPropertyType> {
    carbonLocale: CarbonLocaleType;
    isDisabled?: boolean;
    isParentColumnHidden?: boolean;
    locale: LocalizeLocale;
    localize: LocalizeFunction;
    localizeEnumMember: LocalizeEnumFunction;
    node?: string;
    nodeNames: Dict<string>;
    onChange: <Prop extends DefaultPropertyType = DefaultPropertyType>(
        filters: (FilterProperty<Prop> & { _id: string })[],
    ) => void;
    parameterMode?: ParameterHandlingMode;
    parameters?: FilterParameter[];
    selectedProperties?: Dict<P>;
    value?: FilterProperty<P>[];
    automaticColumnsSpacing?: boolean;
    mode: 'table' | 'pod';
    propertyAction?: () => void;
    propertyActionLabel?: string;
    filterValueAction?: () => void;
    filterValueActionLabel?: string;
}

export const filterTranslations = memoize(
    (localize: LocalizeFunction): Record<FilterTypeValue | 'timeFrame', string> => ({
        contains: localize('@sage/xtrem-ui-components/contains', 'Contains'),
        empty: localize('@sage/xtrem-ui-components/empty', 'Empty'),
        endsWith: localize('@sage/xtrem-ui-components/endsWith', 'Ends with'),
        equals: localize('@sage/xtrem-ui-components/equals', 'Equals'),
        greaterThan: localize('@sage/xtrem-ui-components/greaterThan', 'Greater than'),
        greaterThanOrEqual: localize('@sage/xtrem-ui-components/greaterThanOrEqual', 'Greater than or equal to'),
        inRange: localize('@sage/xtrem-ui-components/between', 'Between'),
        lessThan: localize('@sage/xtrem-ui-components/lessThan', 'Less than'),
        lessThanOrEqual: localize('@sage/xtrem-ui-components/lessThanOrEqual', 'Less than or equal to'),
        matches: localize('@sage/xtrem-ui-components/matches', 'Matches'),
        multiNotEqual: localize('@sage/xtrem-ui-components/notEqual', 'Does not equal'),
        notContains: localize('@sage/xtrem-ui-components/notContains', 'Does not contain'),
        notEmpty: localize('@sage/xtrem-ui-components/notEmpty', 'Not empty'),
        notEqual: localize('@sage/xtrem-ui-components/notEqual', 'Does not equal'),
        set: localize('@sage/xtrem-ui-components/set', 'Equals'),
        startsWith: localize('@sage/xtrem-ui-components/startsWith', 'Starts with'),
        timeFrame: localize('@sage/xtrem-ui-components/timeframe', 'Time frame'),
        multipleRange: localize('@sage/xtrem-ui-components/multipleRanges', 'Multiple ranges'),
    }),
);

export type Validations<Cols extends ColDef[] = []> = Record<
    string,
    Record<UnrestrictedTableCols<DefaultFilterCols, Cols>[number]['id'], string>
>;

export type FilterTableState<Cols extends ColDef[] = [], E = unknown> = {
    addButtonText: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['addButtonText'];
    canDrag: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['canDrag'];
    onRowDrag: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['onRowDrag'];
    canAddNewLines: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['canAddNewLines'];
    onRowRemoved: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['onRowRemoved'];
    onRowAdded: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['onRowAdded'];
    columns: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['columns'];
    data: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['data'];
    counter: number;
    validations: Validations<Cols>;
    parameterMode?: ParameterHandlingMode;
    filterTypeMode?: 'switch' | 'dropdown';
};

export type OnCellChange<Cols extends ColDef[] = [], E = unknown> = (
    args: {
        [P in UnrestrictedTableCols<DefaultFilterCols, Cols>[number]['id']]: {
            columnId: P;
            rowId: string;
            value: Extract<UnrestrictedTableCols<DefaultFilterCols, Cols>[number], { id: P }>['type'];
            rowData: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['data'][number];
        };
    }[UnrestrictedTableCols<DefaultFilterCols, Cols>[number]['id']],
) => void;

export type UnrestrictedOnCellChange<Cols extends ColDef[] = []> = (
    args: {
        [P in UnrestrictedTableCols<DefaultFilterCols, Cols, false>[number]['id']]: {
            columnId: P;
            rowId: string;
            value: Extract<UnrestrictedTableCols<DefaultFilterCols, Cols, false>[number], { id: P }>['type'];
            rowData: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols, false>>['data'][number];
        };
    }[UnrestrictedTableCols<DefaultFilterCols, Cols, false>[number]['id']],
) => void;

type Changes<Cols extends ColDef[] = []> = Parameters<OnCellChange<Cols>>[0];

export type FilterTableAction<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
> =
    | {
          type: 'DATA_RESET';
          value: NonNullable<FilterTableProps<P>['value']>;
          selectedProperties?: Dict<P>;
      }
    | {
          type: 'ROW_ADDED';
      }
    | {
          type: 'ROW_DRAGGED';
          ids: Parameters<
              NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['onRowDrag']>
          >[0];
      }
    | {
          type: 'COLUMNS_CHANGED';
          columns: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['columns'];
      }
    | {
          type: 'CELL_CHANGED';
          changes: Changes<Cols>;
          selectedProperties: Dict<P>;
      }
    | {
          type: 'ROW_REMOVED';
          row: Parameters<
              NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['onRowRemoved']>
          >[0];
      };

export type ActionExtractor<A extends FilterTableAction['type']> = Extract<FilterTableAction, { type: A }>;

export type UseFilterTableHook<Cols extends ColDef[] = [], E = unknown> = Omit<
    FilterTableState<Cols, E>,
    'columns' | 'data'
> & {
    data: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols, false>, E>['data'];
    columns: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols, false>, E>['columns'];
    extraData: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols, false>>['extraData'];
    canRemoveLines: boolean;
    emptyStateText: string | undefined;
    actionsText: string;
    isAddButtonHidden: boolean;
    onCellChange: UnrestrictedOnCellChange<Cols>;
};
