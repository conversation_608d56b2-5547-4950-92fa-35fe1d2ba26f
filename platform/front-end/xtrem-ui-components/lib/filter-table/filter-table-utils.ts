import {
    GraphQLTypes,
    MATCHES,
    RANGE,
    SET,
    filterGraphqlMapping,
    objectKeys,
    type Dict,
    type LocalizeFunction,
} from '@sage/xtrem-shared';
import Box from 'carbon-react/esm/components/box';
import { includes, isEqual, isNil, memoize, set, sortBy } from 'lodash';
import styled from 'styled-components';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import { validateScalarValue } from '../table-validation-utils';
import type { DefaultPropertyType } from '../types';
import { filterableGraphqlTypes } from '../utils';
import type {
    ActionExtractor,
    DefaultFilterCols,
    FilterTableAction,
    FilterTableProps,
    FilterTableState,
    ParameterHandlingMode,
    Validations,
} from './filter-table-types';

export const SplitBox = styled(Box)`
    & > div {
        flex: 1;
    }
`;
export const isCompleteRow = <Cols extends ColDef[] = [], E = unknown>(
    row: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['data'][number],
    validations: Validations<Cols>,
    parameterMode?: ParameterHandlingMode,
): boolean =>
    row.property != null &&
    row.filterType != null &&
    // In parameter creation mode, the empty filter value is actually expected given the value is a parameter
    (row.filterValue != null ||
        (!!row.parameter && parameterMode === 'creation') ||
        row.filterType === 'empty' ||
        row.filterType === 'notEmpty') &&
    row.path != null &&
    row.labelPath != null &&
    !!Object.values(validations[row._id] ?? {}).every(v => v == null);

export const mapFilterToEditorRow = memoize(
    <Cols extends ColDef[] = [], P extends DefaultPropertyType = DefaultPropertyType, E = unknown>({
        value,
        selectedProperties = {},
    }: {
        value: NonNullable<FilterTableProps['value']>;
        selectedProperties?: Dict<P>;
    }): FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['data'] => {
        return value.map(
            (
                {
                    filterType,
                    filterValue,
                    data: { type },
                    id,
                    labelPath,
                    parameter,
                    key,
                    labelKey,
                    label,
                    title: _title,
                    canBeExpanded: _canBeExpanded,
                    canBeSelected: _canBeSelected,
                    ...rest
                },
                index,
            ) => {
                return {
                    // spread needed in case columns are extended
                    ...rest,
                    _id: String(index + 1),
                    property: selectedProperties?.[id],
                    // legacy "filterType" for enums was "matches"
                    filterType: type === 'Enum' && filterType === 'matches' ? 'set' : filterType,
                    // legacy "filterValue" for enums could also not be an array
                    filterValue: type === 'Enum' && !Array.isArray(filterValue) ? [filterValue] : filterValue,
                    path: id,
                    labelPath,
                    key,
                    labelKey,
                    parameter,
                    label,
                } as any;
            },
        );
    },
);

export function filterTableReducer<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
>(): (
    localize: LocalizeFunction,
) => (state: FilterTableState<Cols, E>, action: FilterTableAction<Cols, P, E>) => FilterTableState<Cols, E> {
    return memoize((localize: LocalizeFunction) => {
        return (state: FilterTableState<Cols, E>, action: FilterTableAction<Cols, P, E>): FilterTableState<Cols, E> => {
            switch (action.type) {
                case 'DATA_RESET':
                    return handleDataReset<Cols, P, E>(state, action);
                case 'ROW_ADDED':
                    return handleRowAdded<Cols, E>(state);
                case 'ROW_REMOVED':
                    return handleRowRemoved<Cols, E>(state, action);
                case 'ROW_DRAGGED':
                    return handleRowDragged<Cols, E>(state, action);
                case 'COLUMNS_CHANGED':
                    return handleColumnsChanged<Cols, P, E>(state, action);
                case 'CELL_CHANGED':
                    return handleCellChanged<Cols, P, E>(state, action, localize);
                default:
                    return state;
            }
        };
    });
}

export function getPropertyParentNode({
    labelPath,
    nodeNames,
    node,
}: {
    labelPath?: string;
    nodeNames: Dict<string>;
    node?: string;
}): string {
    const split = labelPath?.split('.');
    return split && split.length > 1 ? split.slice(-2, -1)[0] : (nodeNames[node ?? ''] ?? '');
}

function handleRowAdded<Cols extends ColDef[] = [], E = unknown>(
    state: FilterTableState<Cols, E>,
): FilterTableState<Cols, E> {
    const newRow: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['data'][number] =
        state.columns.reduce(
            (acc, curr) => {
                acc[curr.id] = undefined as any;
                return acc;
            },
            { _id: String(state.counter + 1) } as FlatTableProps<
                UnrestrictedTableCols<DefaultFilterCols, Cols>,
                E
            >['data'][number],
        );
    return {
        ...state,
        counter: state.counter + 1,
        data: [...state.data, newRow],
    };
}

function handleRowRemoved<Cols extends ColDef[] = [], E = unknown>(
    state: FilterTableState<Cols, E>,
    action: ActionExtractor<'ROW_REMOVED'>,
): FilterTableState<Cols, E> {
    return {
        ...state,
        data: state.data.filter(element => element._id !== action.row._id),
    };
}
function handleDataReset<Cols extends ColDef[] = [], P extends DefaultPropertyType = DefaultPropertyType, E = unknown>(
    state: FilterTableState<Cols, E>,
    { value, selectedProperties }: { value: NonNullable<FilterTableProps['value']>; selectedProperties?: Dict<P> },
): FilterTableState<Cols, E> {
    const incompleteRows = state.data.filter(row => !isCompleteRow(row, state.validations, state.parameterMode));
    const completeRows = mapFilterToEditorRow({
        value,
        selectedProperties,
    });
    const completeRowsLength = completeRows.length;
    const data = [
        ...completeRows,
        ...incompleteRows.map((incompleteRow, index) => ({
            ...incompleteRow,
            _id: String(index + completeRowsLength + 1),
        })),
    ];
    return {
        ...state,
        counter: data.length,
        data,
    };
}

function handleColumnsChanged<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
>(
    state: FilterTableState<Cols, E>,
    { columns }: Extract<FilterTableAction<Cols, P, E>, { type: 'COLUMNS_CHANGED' }>,
): FilterTableState<Cols, E> {
    return {
        ...state,
        columns,
    };
}

function handleRowDragged<Cols extends ColDef[] = [], E = unknown>(
    state: FilterTableState<Cols, E>,
    action: ActionExtractor<'ROW_DRAGGED'>,
): FilterTableState<Cols, E> {
    const orderedRows = sortBy(state.data, item => action.ids.indexOf(item._id));
    return {
        ...state,
        data: orderedRows,
    };
}

function getActualValue<Cols extends ColDef[] = []>({
    columnId,
    value,
}: {
    columnId: UnrestrictedTableCols<DefaultFilterCols, Cols>[number]['id'];
    value: any;
}): any {
    switch (columnId) {
        case '_id':
        case 'key':
        case 'labelKey':
        case 'path':
        case 'labelPath':
        case 'parameter':
        case 'filterValue':
            return isNil(value) ? undefined : value;
        case 'property':
        case 'filterType':
            return value || undefined;
        default:
            return value;
    }
}

function handleCellChanged<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
>(
    state: FilterTableState<Cols, E>,
    action: ActionExtractor<'CELL_CHANGED'>,
    localize: FilterTableProps<P>['localize'],
): FilterTableState<Cols, E> {
    const {
        changes: { rowId, columnId, value, rowData },
        selectedProperties,
    } = action;

    const validationMessage =
        rowData.property?.data?.type && (columnId === 'filterValue' || rowData.filterValue) && !rowData.parameter
            ? validateScalarValue({
                  type: rowData.property.data.type,
                  value: columnId === 'filterValue' ? value : rowData.filterValue,
                  enumValues: rowData.property.data.enumValues,
                  localize,
              })
            : undefined;
    const data = state.data.map(c => {
        const curr = { ...c };
        if (curr._id !== rowId || isEqual(curr[columnId], value)) {
            return curr;
        }

        // reset filter value when setting filter type to type range
        // or filter type was range and is set to something else
        if (columnId === 'filterType' && (value === RANGE || curr.filterType === RANGE)) {
            curr.filterValue = undefined;
        }

        if (
            !(
                columnId === 'filterValue' &&
                value === undefined &&
                curr.parameter === true &&
                state.parameterMode === 'creation'
            )
        ) {
            // set actual value
            set(curr, columnId, getActualValue({ columnId, value }));
        }

        // set type and path for given property
        if (curr.property) {
            const prop = selectedProperties?.[curr.property.id];
            curr.path = prop?.id;
            curr.labelPath = prop?.labelPath;
        }

        // set filter type in case it cannot be chosen
        if (curr.property?.data?.type && [GraphQLTypes.Boolean].includes(curr.property.data.type as GraphQLTypes)) {
            curr.filterType = MATCHES;
        }

        const columnType = curr.property?.data.type as GraphQLTypes | undefined;

        // reset filter type & value if filter type is not compatible
        if (
            columnId === 'property' &&
            columnType &&
            // always reset value for enums & booleans
            (columnType === GraphQLTypes.Boolean ||
                columnType === GraphQLTypes.Enum ||
                !curr.filterType ||
                !(filterGraphqlMapping[columnType] as string[])?.includes(curr.filterType) ||
                (curr.filterType === 'timeFrame' &&
                    columnType !== GraphQLTypes.Date &&
                    columnType !== GraphQLTypes.DateTime))
        ) {
            curr.filterType = undefined;
            curr.filterValue = undefined;
        }

        // set default filter type
        if (
            columnId === 'property' &&
            curr.property &&
            !curr.filterType &&
            curr.property.data.type === GraphQLTypes.Enum
        ) {
            curr.filterType = SET;
        }

        // set filter value for parameter filter in creation mode
        if (columnId === 'parameter' && curr.property && state.parameterMode === 'creation') {
            curr.filterValue = value ? curr.property.id : undefined;
        }

        return curr;
    });

    const changedRow = data.find(d => d._id === rowData._id);
    const validationResets = changedRow
        ? objectKeys(changedRow).reduce((acc, key) => {
              if (changedRow?.[key] === undefined) {
                  set(acc, key, undefined);
              }
              return acc;
          }, {})
        : {};

    const validations = {
        ...state.validations,
        [rowData._id]: {
            ...state.validations?.[rowData._id],
            [columnId]: validationMessage,
            ...validationResets,
        },
    };

    return {
        ...state,
        data,
        validations,
    };
}

export const isPropertyFilterable = (p: DefaultPropertyType): boolean => {
    if (!p.data.canFilter) {
        return false;
    }

    const isTypeFilterable = p.data.type && includes(filterableGraphqlTypes, p.data.type);
    const isKindFilterable =
        p.data.kind &&
        includes(
            filterableGraphqlTypes.map(type => type.toLowerCase()),
            p.data.kind.toLowerCase(),
        );

    return isTypeFilterable || isKindFilterable;
};
