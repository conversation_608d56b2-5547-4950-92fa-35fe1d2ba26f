import React from 'react';
import { fireEvent, render, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { startCase } from 'lodash';
import { FilterTableComponent } from '../filter-table';
import { filterTranslations, type FilterTableProps } from '../filter-table-types';
import '@testing-library/jest-dom';
import { filterGraphqlMapping, GraphQLTypes, type Property } from '@sage/xtrem-shared';

const getDOMRect = (width: number, height: number): DOMRect => ({
    width,
    height,
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    x: 0,
    y: 0,
    toJSON: () => {},
});

const mockDOMRect = (width: number, height: number, elementIdentifier: string) => {
    Element.prototype.getBoundingClientRect = jest.fn(function mockBoundClientRect(this: HTMLElement) {
        if (this.getAttribute('data-component') === elementIdentifier) {
            return getDOMRect(width, height);
        }
        return getDOMRect(0, 0);
    });
};

export default mockDOMRect;

describe('FilterTableComponent', () => {
    let props: FilterTableProps;

    beforeAll(() => {
        mockDOMRect(200, 200, 'select-list-scrollable-container');
    });
    beforeEach(() => {
        props = {
            carbonLocale: () => ({}),
            locale: 'en-US',
            localizeEnumMember: (_, member) => startCase(member),
            localize: (_, v) => v,
            onChange: jest.fn(),
            mode: 'table',
            nodeNames: {
                SalesInvoice: 'Sales Invoice',
                SalesOrder: 'Sales Order',
            },
            value: [],
            node: 'SalesInvoice',
            parameters: [],
            selectedProperties: {
                SalesInvoice: {
                    id: 'SalesInvoice',
                    label: 'Sales Invoice',
                    data: {
                        canFilter: true,
                        canSort: true,
                        kind: 'OBJECT',
                        label: 'Sales Invoice',
                        name: 'SalesInvoice',
                        isStored: true,
                        isOnInputType: true,
                        isOnOutputType: true,
                        dataType: 'String',
                        targetNode: '',
                        enumType: null,
                        isCustom: false,
                        isMutable: true,
                        type: 'String',
                    },
                    labelKey: 'SalesInvoice',
                    labelPath: 'SalesInvoice',
                    key: 'SalesInvoice',
                    path: 'SalesInvoice',
                },
            },
        };
    });

    afterEach(() => {});
    describe('table mode', () => {
        describe('render', () => {
            it('should render empty filter table', () => {
                const { queryByTestId, baseElement } = render(<FilterTableComponent {...props} />);
                expect(queryByTestId('add-item-button')).toBeInTheDocument();
                const headers = baseElement.querySelectorAll('[data-element="flat-table-header"]');
                expect(headers).toHaveLength(3);
                expect(headers[0]).toHaveTextContent('Property');
                expect(headers[1]).toHaveTextContent('Filter type');
                expect(headers[2]).toHaveTextContent('Filter value');
            });
        });

        describe('interactions', () => {
            it('should add a row with default values', async () => {
                const { queryByTestId } = render(<FilterTableComponent {...props} />);
                const addButton = queryByTestId('add-item-button')!;
                expect(addButton).toBeInTheDocument();
                await userEvent.click(addButton);
                await waitFor(() => {
                    expect(queryByTestId('e-widget-editor-filter-property-1')).toBeInTheDocument();
                });
            });

            it('should remove a row', async () => {
                const { queryByTestId } = render(<FilterTableComponent {...props} />);
                const addButton = queryByTestId('add-item-button');
                expect(addButton).toBeTruthy();
                await userEvent.click(addButton!);
                await waitFor(() => {
                    expect(queryByTestId('e-widget-editor-filter-property-1')).toBeInTheDocument();
                });
                const removeButton = queryByTestId('flat-table-remove-button')!;
                expect(removeButton).toBeTruthy();
                await userEvent.click(removeButton);
                await waitFor(() => {
                    expect(queryByTestId('e-widget-editor-filter-property-1')).not.toBeInTheDocument();
                });
            });

            it('should handle data entry in a row', async () => {
                const { queryByTestId } = render(<FilterTableComponent {...props} />);
                const addButton = queryByTestId('add-item-button')!;
                expect(addButton).toBeTruthy();
                await userEvent.click(addButton);
                await waitFor(() => {
                    expect(queryByTestId('e-widget-editor-filter-property-1')).toBeInTheDocument();
                });
                const propertyCell = queryByTestId('e-widget-editor-filter-property-1')!;
                expect(propertyCell).toBeTruthy();
                await userEvent.click(propertyCell);
                await userEvent.type(propertyCell, 'prop1');
                await waitFor(() => {
                    expect(propertyCell).toHaveValue('prop1');
                });
            });

            it('should trigger onChange when data changes', async () => {
                const { queryByTestId } = render(<FilterTableComponent {...props} />);
                const addButton = queryByTestId('add-item-button')!;
                await userEvent.click(addButton);
                await waitFor(() => {
                    expect(queryByTestId('e-widget-editor-filter-property-1')).toBeInTheDocument();
                });
                const propertyCell = queryByTestId('e-widget-editor-filter-property-1')!;
                await userEvent.click(propertyCell);
                await userEvent.type(propertyCell, 'prop1');
                await waitFor(() => {
                    expect(props.onChange).toHaveBeenCalled();
                });
            });

            it('should not render the Add filter button if no filterable properties available', async () => {
                props.selectedProperties = {
                    Boolean: {
                        label: 'Boolean',
                        data: { type: GraphQLTypes.Boolean, canFilter: false, canSort: true }, // Because canFilter is false
                        id: 'Boolean',
                        labelPath: 'Boolean',
                    } as Property,
                    Date: {
                        label: 'Date',
                        data: { type: GraphQLTypes.Date, canFilter: false, canSort: true }, // Because canFilter is false
                        id: 'Date',
                        labelPath: 'Date',
                    } as Property,
                    DateTime: {
                        label: 'DateTime',
                        data: { type: GraphQLTypes.DateTime, canFilter: false, canSort: true }, // Because canFilter is false
                        id: 'DateTime',
                        labelPath: 'DateTime',
                    } as Property,
                };
                const { findByText, queryByText } = render(<FilterTableComponent {...props} />);

                await findByText(
                    'You cannot filter the current values. You can select different data or continue without filtering.',
                );

                expect(queryByText('Add filter')).not.toBeInTheDocument();
                expect(queryByText('No data to display')).not.toBeInTheDocument();
            });

            it('should render a table row after clicking on the "Add filter" button', async () => {
                const { findByText, findAllByPlaceholderText, queryByText, findAllByTestId, findByTestId } = render(
                    <FilterTableComponent {...props} />,
                );
                await findByText('No data to display');
                fireEvent.click(await findByTestId('add-item-button'));
                expect(queryByText('Add filter')).not.toBeInTheDocument();
                expect(queryByText('No data to display')).not.toBeInTheDocument();
                await findByText('Property', { selector: 'th > div > div > div' });
                await findByText('Filter type', { selector: 'th > div > div > div' });
                await findByText('Filter value', { selector: 'th > div > div > div' });
                await findByText('Actions', { selector: 'th > div' });
                expect((await findAllByPlaceholderText('Select property...')).length).toBe(1);
                expect(((await findByTestId('e-widget-editor-filter-type-1')) as HTMLInputElement).disabled).toBe(true);
                expect(((await findByTestId('e-widget-editor-filter-value-1')) as HTMLInputElement).disabled).toBe(
                    true,
                );
                expect((await findAllByTestId('flat-table-remove-button')).length).toBe(1);
                expect((await findAllByTestId('flat-table-add-button')).length).toBe(1);
            });

            it('should be able to add multiple rows', async () => {
                const { findAllByTestId, findByTestId } = render(<FilterTableComponent {...props} />);
                fireEvent.click(await findByTestId('add-item-button'));
                fireEvent.click(await findByTestId('flat-table-add-button'));
                expect((await findAllByTestId('flat-table-remove-button')).length).toBe(2);
                expect((await findAllByTestId('flat-table-add-button')).length).toBe(1);
            });

            it('should be able to delete rows', async () => {
                const { findAllByTestId, findByTestId, queryByTestId } = render(<FilterTableComponent {...props} />);
                fireEvent.click(await findByTestId('add-item-button'));
                fireEvent.click(await findByTestId('flat-table-add-button'));
                expect((await findAllByTestId('flat-table-remove-button')).length).toBe(2);
                expect((await findAllByTestId('flat-table-add-button')).length).toBe(1);
                fireEvent.click((await findAllByTestId('flat-table-remove-button'))[0]);
                expect((await findAllByTestId('flat-table-remove-button')).length).toBe(1);
                expect((await findAllByTestId('flat-table-add-button')).length).toBe(1);
                fireEvent.click(await findByTestId('flat-table-remove-button'));
                expect(queryByTestId('flat-table-add-button')).not.toBeInTheDocument();
                expect(queryByTestId('flat-table-remove-button')).not.toBeInTheDocument();
                await findByTestId('add-item-button');
            });

            describe('filter type', () => {
                it('Should render the right filter for Boolean', async () => {
                    props.selectedProperties = {
                        Boolean: {
                            label: 'Boolean',
                            data: { type: GraphQLTypes.Boolean, canFilter: true, canSort: true },
                            id: 'Boolean',
                            labelPath: 'Boolean',
                        } as Property,
                    };
                    const { queryByTestId } = render(<FilterTableComponent {...props} />);
                    const addItem = queryByTestId('add-item-button');
                    expect(addItem).toBeInTheDocument();
                    fireEvent.click(addItem!);
                    const filterTypeInput = queryByTestId('e-widget-editor-filter-property-1') as HTMLInputElement;
                    expect(filterTypeInput).toBeInTheDocument();
                    await userEvent.type(filterTypeInput, 'Boolean', {
                        initialSelectionStart: 0,
                        initialSelectionEnd: filterTypeInput.value.length,
                    });

                    await userEvent.tab();
                    const filterType = queryByTestId('e-widget-editor-filter-type-1');
                    expect(filterType).toBeInTheDocument();
                    expect(filterTypeInput?.getAttribute('aria-expanded')).toBe('false');
                    expect(filterType?.textContent).toBe('Equals');
                });

                it('Should render the right filter for ID', async () => {
                    props.selectedProperties = {
                        Id: {
                            label: 'Id',
                            data: { type: GraphQLTypes.Id, canFilter: true, canSort: true },
                            id: 'Id',
                            labelPath: 'Id',
                        } as Property,
                    };
                    props.nodeNames = {};
                    props.node = undefined;
                    const { queryByTestId, getByTestId } = render(<FilterTableComponent {...props} />);
                    const addItem = queryByTestId('add-item-button');
                    expect(addItem).toBeInTheDocument();
                    fireEvent.click(addItem!);
                    const filterTypeInput = queryByTestId('e-widget-editor-filter-property-1') as HTMLInputElement;
                    expect(filterTypeInput).toBeInTheDocument();
                    await userEvent.type(filterTypeInput, 'Id', {
                        initialSelectionStart: 0,
                        initialSelectionEnd: filterTypeInput.value.length,
                    });

                    const filterType = queryByTestId('e-widget-editor-filter-type-1');
                    expect(filterType).toBeInTheDocument();
                    await userEvent.tab();
                    fireEvent.focus(filterType!);
                    await waitFor(async () => {
                        const filterPropertyInput = getByTestId(
                            'e-widget-editor-filter-property-1',
                        ) as HTMLInputElement;
                        expect(filterPropertyInput?.getAttribute('aria-expanded')).toBe('false');
                    });
                    await waitFor(async () => {
                        const filterValueInput = getByTestId('e-widget-editor-filter-type-1') as HTMLInputElement;
                        expect(filterValueInput?.getAttribute('aria-expanded')).toBe('true');
                    });
                    await waitFor(() => {
                        const dropdowns = Array.from(document.querySelectorAll('[data-element="select-list-wrapper"]'));
                        const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                        const filters = Array.from(lastDropdown?.querySelectorAll('li') ?? []) as HTMLLIElement[];
                        const availableFilters = filters.map(method => method.textContent);
                        return expect(availableFilters).toEqual(
                            [...(filterGraphqlMapping.Id ?? [])].map(p => filterTranslations((_, s) => s)[p]),
                        );
                    });
                });

                it('Should render the right filter for Date', async () => {
                    props.selectedProperties = {
                        Date: {
                            label: 'Date',
                            data: { type: GraphQLTypes.Date, canFilter: true, canSort: true },
                            id: 'Date',
                            labelPath: 'Date',
                        } as Property,
                    };
                    const { queryByTestId, getByTestId } = render(<FilterTableComponent {...props} />);
                    const addItem = queryByTestId('add-item-button');
                    expect(addItem).toBeInTheDocument();
                    fireEvent.click(addItem!);
                    const filterTypeInput = queryByTestId('e-widget-editor-filter-property-1') as HTMLInputElement;
                    expect(filterTypeInput).toBeInTheDocument();
                    await userEvent.type(filterTypeInput, 'Date', {
                        initialSelectionStart: 0,
                        initialSelectionEnd: filterTypeInput.value.length,
                    });

                    const filterType = queryByTestId('e-widget-editor-filter-type-1');
                    expect(filterType).toBeInTheDocument();
                    await userEvent.tab();
                    fireEvent.focus(filterType!);
                    await waitFor(async () => {
                        const filterPropertyInput = getByTestId(
                            'e-widget-editor-filter-property-1',
                        ) as HTMLInputElement;
                        expect(filterPropertyInput?.getAttribute('aria-expanded')).toBe('false');
                    });
                    await waitFor(async () => {
                        const filterValueInput = getByTestId('e-widget-editor-filter-type-1') as HTMLInputElement;
                        expect(filterValueInput?.getAttribute('aria-expanded')).toBe('true');
                    });
                    await waitFor(() => {
                        const dropdowns = Array.from(document.querySelectorAll('[data-element="select-list-wrapper"]'));
                        const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                        const filters = Array.from(lastDropdown?.querySelectorAll('li') ?? []) as HTMLLIElement[];
                        const availableFilters = filters.map(method => method.textContent);
                        return expect(availableFilters).toEqual(
                            [...(filterGraphqlMapping.Date ?? [])]
                                .map(p => filterTranslations((_, s) => s)[p])
                                .concat('Time frame'),
                        );
                    });
                });

                it('Should render the right filter for DateTime', async () => {
                    props.selectedProperties = {
                        DateTime: {
                            label: 'DateTime',
                            data: { type: GraphQLTypes.DateTime, canFilter: true, canSort: true },
                            id: 'DateTime',
                            labelPath: 'DateTime',
                        } as Property,
                    };
                    const { queryByTestId, getByTestId } = render(<FilterTableComponent {...props} />);
                    const addItem = queryByTestId('add-item-button');
                    expect(addItem).toBeInTheDocument();
                    fireEvent.click(addItem!);
                    const filterTypeInput = queryByTestId('e-widget-editor-filter-property-1') as HTMLInputElement;
                    expect(filterTypeInput).toBeInTheDocument();
                    await userEvent.type(filterTypeInput, 'DateTime', {
                        initialSelectionStart: 0,
                        initialSelectionEnd: filterTypeInput.value.length,
                    });

                    const filterType = queryByTestId('e-widget-editor-filter-type-1');
                    expect(filterType).toBeInTheDocument();
                    await userEvent.tab();
                    fireEvent.focus(filterType!);
                    await waitFor(async () => {
                        const filterPropertyInput = getByTestId(
                            'e-widget-editor-filter-property-1',
                        ) as HTMLInputElement;
                        expect(filterPropertyInput?.getAttribute('aria-expanded')).toBe('false');
                    });
                    await waitFor(async () => {
                        const filterValueInput = getByTestId('e-widget-editor-filter-type-1') as HTMLInputElement;
                        expect(filterValueInput?.getAttribute('aria-expanded')).toBe('true');
                    });
                    await waitFor(() => {
                        const dropdowns = Array.from(document.querySelectorAll('[data-element="select-list-wrapper"]'));
                        const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                        const filters = Array.from(lastDropdown?.querySelectorAll('li') ?? []) as HTMLLIElement[];
                        const availableFilters = filters.map(method => method.textContent);
                        return expect(availableFilters).toEqual(
                            [...(filterGraphqlMapping.datetime ?? [])]
                                .map(p => filterTranslations((_, s) => s)[p])
                                .concat('Time frame'),
                        );
                    });
                });

                it('Should render the right filter for Decimal', async () => {
                    props.selectedProperties = {
                        Decimal: {
                            label: 'Decimal',
                            data: { type: GraphQLTypes.Decimal, canFilter: true, canSort: true },
                            id: 'Decimal',
                            labelPath: 'Decimal',
                        } as Property,
                    };
                    const { queryByTestId, getByTestId } = render(<FilterTableComponent {...props} />);
                    const addItem = queryByTestId('add-item-button');
                    expect(addItem).toBeInTheDocument();
                    fireEvent.click(addItem!);
                    const filterTypeInput = queryByTestId('e-widget-editor-filter-property-1') as HTMLInputElement;
                    expect(filterTypeInput).toBeInTheDocument();
                    await userEvent.type(filterTypeInput, 'Decimal', {
                        initialSelectionStart: 0,
                        initialSelectionEnd: filterTypeInput.value.length,
                    });

                    const filterType = queryByTestId('e-widget-editor-filter-type-1');
                    expect(filterType).toBeInTheDocument();
                    await userEvent.tab();
                    fireEvent.focus(filterType!);
                    await waitFor(async () => {
                        const filterPropertyInput = getByTestId(
                            'e-widget-editor-filter-property-1',
                        ) as HTMLInputElement;
                        expect(filterPropertyInput?.getAttribute('aria-expanded')).toBe('false');
                    });
                    await waitFor(async () => {
                        const filterValueInput = getByTestId('e-widget-editor-filter-type-1') as HTMLInputElement;
                        expect(filterValueInput?.getAttribute('aria-expanded')).toBe('true');
                    });
                    await waitFor(() => {
                        const dropdowns = Array.from(document.querySelectorAll('[data-element="select-list-wrapper"]'));
                        const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                        const filters = Array.from(lastDropdown?.querySelectorAll('li') ?? []) as HTMLLIElement[];
                        const availableFilters = filters.map(method => method.textContent);
                        return expect(availableFilters).toEqual(
                            [...(filterGraphqlMapping.Decimal ?? [])].map(p => filterTranslations((_, s) => s)[p]),
                        );
                    });
                });

                it('Should render the right filter for Enum', async () => {
                    props.selectedProperties = {
                        Enum: {
                            label: 'Enum',
                            data: { type: GraphQLTypes.Enum, canFilter: true, canSort: true },
                            id: 'Enum',
                            labelPath: 'Enum',
                        } as Property,
                    };
                    const { queryByTestId, getByTestId } = render(<FilterTableComponent {...props} />);
                    const addItem = queryByTestId('add-item-button');
                    expect(addItem).toBeInTheDocument();
                    fireEvent.click(addItem!);
                    const filterTypeInput = queryByTestId('e-widget-editor-filter-property-1') as HTMLInputElement;
                    expect(filterTypeInput).toBeInTheDocument();
                    await userEvent.type(filterTypeInput, 'Enum', {
                        initialSelectionStart: 0,
                        initialSelectionEnd: filterTypeInput.value.length,
                    });

                    const filterType = queryByTestId('e-widget-editor-filter-type-1');
                    expect(filterType).toBeInTheDocument();
                    await userEvent.tab();
                    fireEvent.focus(filterType!);
                    await waitFor(async () => {
                        const filterPropertyInput = getByTestId(
                            'e-widget-editor-filter-property-1',
                        ) as HTMLInputElement;
                        expect(filterPropertyInput?.getAttribute('aria-expanded')).toBe('false');
                    });
                    await waitFor(async () => {
                        const filterValueInput = getByTestId('e-widget-editor-filter-type-1') as HTMLInputElement;
                        expect(filterValueInput?.getAttribute('aria-expanded')).toBe('true');
                    });
                    await waitFor(() => {
                        const dropdowns = Array.from(document.querySelectorAll('[data-element="select-list-wrapper"]'));
                        const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                        const filters = Array.from(lastDropdown?.querySelectorAll('li') ?? []) as HTMLLIElement[];
                        const availableFilters = filters.map(method => method.textContent);
                        return expect(availableFilters).toEqual(
                            [...(filterGraphqlMapping.Enum ?? [])].map(p => filterTranslations((_, s) => s)[p]),
                        );
                    });
                });

                it('Should render the right filter for ExternalReference', async () => {
                    props.selectedProperties = {
                        ExternalReference: {
                            label: 'ExternalReference',
                            data: { type: GraphQLTypes.ExternalReference, canFilter: true, canSort: true },
                            id: 'ExternalReference',
                            labelPath: 'ExternalReference',
                        } as Property,
                    };
                    const { queryByText } = render(<FilterTableComponent {...props} />);

                    queryByText(
                        'You cannot filter the current values. You can select different data or continue without filtering.',
                    );
                });

                it('Should render the right filter for Float', async () => {
                    props.selectedProperties = {
                        Float: {
                            label: 'Float',
                            data: { type: GraphQLTypes.Float, canFilter: true, canSort: true },
                            id: 'Float',
                            labelPath: 'Float',
                        } as Property,
                    };
                    const { queryByTestId, getByTestId } = render(<FilterTableComponent {...props} />);
                    const addItem = queryByTestId('add-item-button');
                    expect(addItem).toBeInTheDocument();
                    fireEvent.click(addItem!);
                    const filterTypeInput = queryByTestId('e-widget-editor-filter-property-1') as HTMLInputElement;
                    expect(filterTypeInput).toBeInTheDocument();
                    await userEvent.type(filterTypeInput, 'Float', {
                        initialSelectionStart: 0,
                        initialSelectionEnd: filterTypeInput.value.length,
                    });

                    const filterType = queryByTestId('e-widget-editor-filter-type-1');
                    expect(filterType).toBeInTheDocument();
                    await userEvent.tab();
                    fireEvent.focus(filterType!);
                    await waitFor(async () => {
                        const filterPropertyInput = getByTestId(
                            'e-widget-editor-filter-property-1',
                        ) as HTMLInputElement;
                        expect(filterPropertyInput?.getAttribute('aria-expanded')).toBe('false');
                    });
                    await waitFor(async () => {
                        const filterValueInput = getByTestId('e-widget-editor-filter-type-1') as HTMLInputElement;
                        expect(filterValueInput?.getAttribute('aria-expanded')).toBe('true');
                    });
                    await waitFor(() => {
                        const dropdowns = Array.from(document.querySelectorAll('[data-element="select-list-wrapper"]'));
                        const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                        const filters = Array.from(lastDropdown?.querySelectorAll('li') ?? []) as HTMLLIElement[];
                        const availableFilters = filters.map(method => method.textContent);
                        return expect(availableFilters).toEqual(
                            [...(filterGraphqlMapping.Float ?? [])].map(p => filterTranslations((_, s) => s)[p]),
                        );
                    });
                });

                it('Should render the right filter for Int', async () => {
                    props.selectedProperties = {
                        Int: {
                            label: 'Int',
                            data: { type: GraphQLTypes.Int, canFilter: true, canSort: true },
                            id: 'Int',
                            labelPath: 'Int',
                        } as Property,
                    };
                    const { queryByTestId, getByTestId } = render(<FilterTableComponent {...props} />);
                    const addItem = queryByTestId('add-item-button');
                    expect(addItem).toBeInTheDocument();
                    fireEvent.click(addItem!);
                    const filterTypeInput = queryByTestId('e-widget-editor-filter-property-1') as HTMLInputElement;
                    expect(filterTypeInput).toBeInTheDocument();
                    await userEvent.type(filterTypeInput, 'Int', {
                        initialSelectionStart: 0,
                        initialSelectionEnd: filterTypeInput.value.length,
                    });

                    const filterType = queryByTestId('e-widget-editor-filter-type-1');
                    expect(filterType).toBeInTheDocument();
                    await userEvent.tab();
                    fireEvent.focus(filterType!);
                    await waitFor(async () => {
                        const filterPropertyInput = getByTestId(
                            'e-widget-editor-filter-property-1',
                        ) as HTMLInputElement;
                        expect(filterPropertyInput?.getAttribute('aria-expanded')).toBe('false');
                    });
                    await waitFor(async () => {
                        const filterValueInput = getByTestId('e-widget-editor-filter-type-1') as HTMLInputElement;
                        expect(filterValueInput?.getAttribute('aria-expanded')).toBe('true');
                    });
                    await waitFor(() => {
                        const dropdowns = Array.from(document.querySelectorAll('[data-element="select-list-wrapper"]'));
                        const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                        const filters = Array.from(lastDropdown?.querySelectorAll('li') ?? []) as HTMLLIElement[];
                        const availableFilters = filters.map(method => method.textContent);
                        return expect(availableFilters).toEqual(
                            [...(filterGraphqlMapping.Int ?? [])].map(p => filterTranslations((_, s) => s)[p]),
                        );
                    });
                });

                it('Should render the right filter for IntReference', async () => {
                    props.selectedProperties = {
                        IntReference: {
                            label: 'IntReference',
                            data: { type: GraphQLTypes.IntReference, canFilter: true, canSort: true },
                            id: 'IntReference',
                            labelPath: 'IntReference',
                        } as Property,
                    };
                    const { queryByTestId, getByTestId } = render(<FilterTableComponent {...props} />);
                    const addItem = queryByTestId('add-item-button');
                    expect(addItem).toBeInTheDocument();
                    fireEvent.click(addItem!);
                    const filterTypeInput = queryByTestId('e-widget-editor-filter-property-1') as HTMLInputElement;
                    expect(filterTypeInput).toBeInTheDocument();
                    await userEvent.type(filterTypeInput, 'IntReference', {
                        initialSelectionStart: 0,
                        initialSelectionEnd: filterTypeInput.value.length,
                    });

                    const filterType = queryByTestId('e-widget-editor-filter-type-1');
                    expect(filterType).toBeInTheDocument();
                    await userEvent.tab();
                    fireEvent.focus(filterType!);
                    await waitFor(async () => {
                        const filterPropertyInput = getByTestId(
                            'e-widget-editor-filter-property-1',
                        ) as HTMLInputElement;
                        expect(filterPropertyInput?.getAttribute('aria-expanded')).toBe('false');
                    });
                    await waitFor(async () => {
                        const filterValueInput = getByTestId('e-widget-editor-filter-type-1') as HTMLInputElement;
                        expect(filterValueInput?.getAttribute('aria-expanded')).toBe('true');
                    });
                    await waitFor(() => {
                        const dropdowns = Array.from(document.querySelectorAll('[data-element="select-list-wrapper"]'));
                        const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                        const filters = Array.from(lastDropdown?.querySelectorAll('li') ?? []) as HTMLLIElement[];
                        const availableFilters = filters.map(method => method.textContent);
                        return expect(availableFilters).toEqual(
                            [...(filterGraphqlMapping.IntReference ?? [])].map(p => filterTranslations((_, s) => s)[p]),
                        );
                    });
                });

                it('Should render the right filter for Json', async () => {
                    props.selectedProperties = {
                        Json: {
                            label: 'Json',
                            data: { type: GraphQLTypes.Json, canFilter: true, canSort: true },
                            id: 'Json',
                            labelPath: 'Json',
                        } as Property,
                    };
                    const { queryByText } = render(<FilterTableComponent {...props} />);
                    queryByText(
                        'You cannot filter the current values. You can select different data or continue without filtering.',
                    );
                });

                it('Should render the right filter for String', async () => {
                    props.selectedProperties = {
                        String: {
                            label: 'String',
                            data: { type: GraphQLTypes.String, canFilter: true, canSort: true },
                            id: 'String',
                            labelPath: 'String',
                        } as Property,
                    };
                    const { queryByTestId, getByTestId } = render(<FilterTableComponent {...props} />);
                    const addItem = queryByTestId('add-item-button');
                    expect(addItem).toBeInTheDocument();
                    fireEvent.click(addItem!);
                    const filterTypeInput = queryByTestId('e-widget-editor-filter-property-1') as HTMLInputElement;
                    expect(filterTypeInput).toBeInTheDocument();
                    await userEvent.type(filterTypeInput, 'String', {
                        initialSelectionStart: 0,
                        initialSelectionEnd: filterTypeInput.value.length,
                    });

                    const filterType = queryByTestId('e-widget-editor-filter-type-1');
                    expect(filterType).toBeInTheDocument();
                    await userEvent.tab();
                    fireEvent.focus(filterType!);
                    await waitFor(async () => {
                        const filterPropertyInput = getByTestId(
                            'e-widget-editor-filter-property-1',
                        ) as HTMLInputElement;
                        expect(filterPropertyInput?.getAttribute('aria-expanded')).toBe('false');
                    });
                    await waitFor(async () => {
                        const filterValueInput = getByTestId('e-widget-editor-filter-type-1') as HTMLInputElement;
                        expect(filterValueInput?.getAttribute('aria-expanded')).toBe('true');
                    });
                    await waitFor(() => {
                        const dropdowns = Array.from(document.querySelectorAll('[data-element="select-list-wrapper"]'));
                        const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                        const filters = Array.from(lastDropdown?.querySelectorAll('li') ?? []) as HTMLLIElement[];
                        const availableFilters = filters.map(method => method.textContent);
                        return expect(availableFilters).toEqual(
                            [...(filterGraphqlMapping.String ?? [])].map(p => filterTranslations((_, s) => s)[p]),
                        );
                    });
                });

                it('Should render the right filter for IntOrString', async () => {
                    props.selectedProperties = {
                        IntOrString: {
                            label: 'IntOrString',
                            data: { type: GraphQLTypes.IntOrString, canFilter: true, canSort: true },
                            id: 'IntOrString',
                            labelPath: 'IntOrString',
                        } as Property,
                    };
                    const { queryByTestId, getByTestId } = render(<FilterTableComponent {...props} />);
                    const addItem = queryByTestId('add-item-button');
                    expect(addItem).toBeInTheDocument();
                    fireEvent.click(addItem!);
                    const filterTypeInput = queryByTestId('e-widget-editor-filter-property-1') as HTMLInputElement;
                    expect(filterTypeInput).toBeInTheDocument();
                    await userEvent.type(filterTypeInput, 'IntOrString', {
                        initialSelectionStart: 0,
                        initialSelectionEnd: filterTypeInput.value.length,
                    });

                    const filterType = queryByTestId('e-widget-editor-filter-type-1');
                    expect(filterType).toBeInTheDocument();
                    await userEvent.tab();
                    fireEvent.focus(filterType!);
                    await waitFor(async () => {
                        const filterPropertyInput = getByTestId(
                            'e-widget-editor-filter-property-1',
                        ) as HTMLInputElement;
                        expect(filterPropertyInput?.getAttribute('aria-expanded')).toBe('false');
                    });
                    await waitFor(async () => {
                        const filterValueInput = getByTestId('e-widget-editor-filter-type-1') as HTMLInputElement;
                        expect(filterValueInput?.getAttribute('aria-expanded')).toBe('true');
                    });
                    await waitFor(() => {
                        const dropdowns = Array.from(document.querySelectorAll('[data-element="select-list-wrapper"]'));
                        const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                        const filters = Array.from(lastDropdown?.querySelectorAll('li') ?? []) as HTMLLIElement[];
                        const availableFilters = filters.map(method => method.textContent);
                        return expect(availableFilters).toEqual(
                            [...(filterGraphqlMapping.IntOrString ?? [])].map(p => filterTranslations((_, s) => s)[p]),
                        );
                    });
                });
            });
        });
    });

    describe('pod mode', () => {
        beforeEach(() => {
            props.mode = 'pod';
        });

        describe('render', () => {
            it('Should render', () => {
                const { queryByTestId, debug } = render(<FilterTableComponent {...props} />);
                debug();
                expect(queryByTestId('e-filter-pod-container')).toBeInTheDocument();
            });
        });
        describe('interactions', () => {
            it('Should add a pod after cliking on Add filter button', () => {
                const { queryByTestId } = render(<FilterTableComponent {...props} />);
                const addItem = queryByTestId('add-item-button');
                expect(addItem).toBeInTheDocument();
                fireEvent.click(addItem!);
                expect(queryByTestId('e-filter-pod-container')).toBeInTheDocument();
                expect(queryByTestId('e-filter-pod-row-1')).toBeInTheDocument();
            });
            it('Should remove a pod after clicking on Remove button', async () => {
                const { queryByTestId } = render(<FilterTableComponent {...props} />);
                const addItem = queryByTestId('add-item-button');
                expect(addItem).toBeInTheDocument();
                fireEvent.click(addItem!);
                expect(queryByTestId('e-filter-pod-row-1')).toBeInTheDocument();
                const removeButton = queryByTestId('e-filter-table-remove-row-button-1');
                expect(removeButton).toBeInTheDocument();
                fireEvent.click(removeButton!);
                expect(queryByTestId('e-filter-pod-row')).not.toBeInTheDocument();
            });
            it('Should render property, filterType and filterValue', () => {
                const { queryByTestId } = render(<FilterTableComponent {...props} />);
                const addItem = queryByTestId('add-item-button');
                expect(addItem).toBeInTheDocument();
                fireEvent.click(addItem!);
                expect(queryByTestId('e-filter-pod-container')).toBeInTheDocument();
                expect(queryByTestId('e-filter-pod-row-1')).toBeInTheDocument();
                expect(queryByTestId('e-filter-pod-property-1')).toBeInTheDocument();
                expect(queryByTestId('e-filter-pod-filter-type-1')).toBeInTheDocument();
                expect(queryByTestId('e-filter-pod-filter-value-1')).toBeInTheDocument();
            });
            it('Should render parameter if parameterMode is set and it has paramaters', async () => {
                props.parameterMode = 'usage';
                props.parameters = [
                    {
                        name: 'exampleParam',
                        type: 'String',
                    },
                ];
                const { queryByTestId } = render(<FilterTableComponent {...props} />);
                const addItem = queryByTestId('add-item-button');
                expect(addItem).toBeInTheDocument();
                fireEvent.click(addItem!);
                expect(queryByTestId('e-filter-pod-container')).toBeInTheDocument();
                expect(queryByTestId('e-filter-pod-row-1')).toBeInTheDocument();
                expect(queryByTestId('e-filter-pod-parameter-1')).toBeInTheDocument();
            });
            it('Should NOT render parameter if parameterMode is NOT set', async () => {
                const { queryByTestId } = render(<FilterTableComponent {...props} />);
                const addItem = queryByTestId('add-item-button');
                expect(addItem).toBeInTheDocument();
                fireEvent.click(addItem!);
                expect(queryByTestId('e-filter-pod-container')).toBeInTheDocument();
                expect(queryByTestId('e-filter-pod-row-1')).toBeInTheDocument();
                expect(queryByTestId('e-filter-pod-parameter-1')).not.toBeInTheDocument();
            });
        });
    });
});
