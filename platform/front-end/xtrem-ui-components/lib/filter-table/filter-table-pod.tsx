import React from 'react';
import type { UseFilterTableHook } from './filter-table-types';
import Box from 'carbon-react/esm/components/box';
import Typography from 'carbon-react/esm/components/typography';
import Button from 'carbon-react/esm/components/button';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import { noop } from 'lodash';
import type { LocalizeFunction } from '@sage/xtrem-shared';

function NoFilter({
    emptyStateText,
    isAddButtonHidden,
    onPodAdded,
    addButtonText,
}: {
    emptyStateText?: string;
    isAddButtonHidden: boolean;
    onPodAdded: UseFilterTableHook['onRowAdded'];
    addButtonText: string;
}): React.ReactElement {
    return (
        <Box paddingY={80} gap={1} display="flex" flexDirection="column" alignItems="center" justifyContent="center">
            <div className="e-flat-table-placeholder">
                <Typography color="--colorsUtilityMajor100" lineHeight="30px" variant="h3">
                    {emptyStateText}
                </Typography>
            </div>

            {!isAddButtonHidden && (
                <ButtonMinor mt={2} iconType="add" onClick={onPodAdded} data-testid="add-item-button">
                    {addButtonText}
                </ButtonMinor>
            )}
        </Box>
    );
}

export function FilterTablePod({
    data,
    extraData,
    columns,
    emptyStateText,
    localize,
    onPodAdded = noop,
    onPodRemoved = noop,
    isAddButtonHidden,
    addButtonText = 'Add condition',
}: {
    data: UseFilterTableHook['data'];
    extraData: UseFilterTableHook['extraData'];
    columns: UseFilterTableHook['columns'];
    emptyStateText?: UseFilterTableHook['emptyStateText'];
    onPodAdded?: UseFilterTableHook['onRowAdded'];
    onPodRemoved?: UseFilterTableHook['onRowRemoved'];
    isAddButtonHidden: UseFilterTableHook['isAddButtonHidden'];
    addButtonText: UseFilterTableHook['addButtonText'];
    localize: LocalizeFunction;
}): React.ReactElement | null {
    const propertyColumn = columns.find(column => column.id === 'property');
    const filterType = columns.find(column => column.id === 'filterType');
    const parameter = columns.find(column => column.id === 'parameter');
    const filterValue = columns.find(column => column.id === 'filterValue');

    // if some of the properties above are undefined return null
    if (!propertyColumn || !filterType || !filterValue) {
        return null;
    }

    return (
        <div data-testid="e-filter-pod-container" className="e-filter-pod-container">
            {data.length === 0 ? (
                <NoFilter
                    emptyStateText={emptyStateText}
                    isAddButtonHidden={isAddButtonHidden}
                    onPodAdded={onPodAdded}
                    addButtonText={addButtonText}
                />
            ) : (
                <>
                    {data.map((rowData, index) => {
                        const propertyElement = propertyColumn.cellRenderer?.({
                            rowData,
                            rowIndex: index,
                            data,
                            extraData,
                        });

                        const filterElement = filterType.cellRenderer?.({
                            rowData,
                            rowIndex: index,
                            data,
                            extraData,
                        });
                        const parameterElement = parameter?.cellRenderer?.({
                            rowData,
                            rowIndex: index,
                            data,
                            extraData,
                            filterTypeMode: 'dropdown',
                        });
                        const filterValueElement = filterValue.cellRenderer?.({
                            rowData,
                            rowIndex: index,
                            data,
                            extraData,
                        });

                        return (
                            <div
                                key={rowData._id}
                                data-testid={`e-filter-pod-row-${rowData._id}`}
                                className="e-filter-pod-row"
                            >
                                <div
                                    className="e-filter-pod-condition-label"
                                    data-testid={`e-filter-pod-condition-label-${rowData._id}`}
                                >
                                    <Typography marginBottom={0} fontWeight="500">
                                        {`${localize('@sage/xtrem-ui-components/pod-condition', 'Condition')} ${index + 1}`}
                                    </Typography>
                                </div>
                                <div
                                    className="e-filter-pod-property"
                                    data-testid={`e-filter-pod-property-${rowData._id}`}
                                >
                                    {propertyElement}
                                </div>
                                <div
                                    className="e-filter-pod-filter-parameter-container"
                                    data-testid={`e-filter-pod-filter-parameter-container-${rowData._id}`}
                                >
                                    <div
                                        className="e-filter-pod-filter-type"
                                        data-testid={`e-filter-pod-filter-type-${rowData._id}`}
                                    >
                                        {filterElement}
                                    </div>
                                    {parameter && (
                                        <div
                                            data-testid={`e-filter-pod-parameter-${rowData._id}`}
                                            className="e-filter-pod-parameter"
                                        >
                                            {parameterElement}
                                        </div>
                                    )}
                                </div>
                                <div
                                    className="e-filter-pod-filter-value"
                                    data-testid={`e-filter-pod-filter-value-${rowData._id}`}
                                >
                                    {filterValueElement}
                                </div>
                                <div className="e-filter-pod-remove" data-testid={`e-filter-pod-remove-${rowData._id}`}>
                                    <Button
                                        destructive
                                        iconType="remove"
                                        onClick={() => {
                                            onPodRemoved(rowData);
                                        }}
                                        data-testid={`e-filter-table-remove-row-button-${rowData._id}`}
                                    >
                                        {localize(
                                            '@sage/xtrem-ui-components/filter-pod-remove-condition',
                                            'Remove condition',
                                        )}
                                    </Button>
                                </div>
                            </div>
                        );
                    })}
                    {!isAddButtonHidden && (
                        <div className="e-filter-pod-add">
                            <Button
                                mt={2}
                                iconType="add"
                                onClick={onPodAdded}
                                data-testid="e-filter-table-add-row-button"
                            >
                                {localize('@sage/xtrem-ui-components/filter-pod-add-condition', 'Add condition')}
                            </Button>
                        </div>
                    )}
                </>
            )}
        </div>
    );
}
