import React from 'react';
import { PropertyTableHeader } from '../property-template-header/property-table-header';
import type { DefaultFilterCols, OnCellChange } from './filter-table-types';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import type { Dict, LocalizeFunction } from '@sage/xtrem-shared';
import { FilterableSelect, OptionRow } from 'carbon-react/esm/components/select';
import type { DefaultPropertyType } from '../types';
import { getPropertyParentNode } from './filter-table-utils';
import Button from 'carbon-react/esm/components/button';

export interface FilterTablePropertyColumnProps<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
> {
    filterableProperties: P[];
    isDisabled?: boolean;
    localize: LocalizeFunction;
    node?: string;
    nodeNames: Dict<string>;
    onCellChange: OnCellChange<Cols, E>;
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['data'][number];
    isParentColumnHidden?: boolean;
    propertyAction?: () => void;
    propertyActionLabel?: string;
}

export function FilterTablePropertyColumn<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
>({
    filterableProperties,
    isDisabled,
    isParentColumnHidden,
    localize,
    node,
    nodeNames,
    onCellChange,
    rowData,
    propertyAction,
    propertyActionLabel,
}: FilterTablePropertyColumnProps<Cols, P, E>): React.ReactElement {
    return (
        <FilterableSelect
            listActionButton={
                propertyAction && (
                    <Button iconType="add" iconPosition="after">
                        {propertyActionLabel ?? localize('@sage/xtrem-ui-components/add-property', 'Add property')}
                    </Button>
                )
            }
            onListAction={() => {
                if (propertyAction) {
                    propertyAction();
                }
            }}
            multiColumn={true}
            tableHeader={<PropertyTableHeader localize={localize} isParentColumnHidden={isParentColumnHidden} />}
            openOnFocus={true}
            disabled={isDisabled}
            data-testid={`e-widget-editor-filter-property-${rowData._id}`}
            // @ts-expect-error "onChange" is actually triggered with { target: { value: Property } }
            onChange={({ target: { value } }: { target: { value: Property } }): void => {
                onCellChange({ columnId: 'property', rowId: rowData._id, value, rowData });
            }}
            placeholder={localize('@sage/xtrem-ui-components/select-property', 'Select property...')}
            size="small"
            // @ts-expect-error value is of type Property
            value={rowData.property ?? {}}
        >
            {filterableProperties.map(p => {
                return (
                    // @ts-expect-error value is of type Property
                    <OptionRow text={p.label} value={p} key={p.id}>
                        <td
                            width={isParentColumnHidden ? '100%' : '50%'}
                            style={{
                                overflow: 'hidden',
                                whiteSpace: 'pre-line',
                                maxWidth: 0,
                            }}
                        >
                            {p.label}
                        </td>
                        {!isParentColumnHidden && (
                            <td
                                width="50%"
                                style={{
                                    overflow: 'hidden',
                                    whiteSpace: 'pre-line',
                                    maxWidth: 0,
                                    textAlign: 'end',
                                }}
                            >
                                {getPropertyParentNode({ nodeNames, labelPath: p.labelPath, node })}
                            </td>
                        )}
                    </OptionRow>
                );
            })}
        </FilterableSelect>
    );
}
