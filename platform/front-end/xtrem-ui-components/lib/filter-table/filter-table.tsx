import React from 'react';
import type {
    DefaultFilterCols,
    FilterTableProps,
    OnCellChange,
    UseFilterTableHook,
    Validations,
} from './filter-table-types';
import { filterTableReducer, isCompleteRow, isPropertyFilterable, mapFilterToEditorRow } from './filter-table-utils';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import type { DefaultPropertyType } from '../types';
import { FilterTablePropertyColumn } from './filter-table-property-column';
import { FilterTableTypeColumnMemo } from './filter-table-type-column';
import { FilterTableValueColumnMemo } from './filter-table-value-column';
import { FilterTableParameterColumnMemo } from './filter-table-parameter-column';
import { FlatTable } from '../flat-table/flat-table';
import { isEqual } from 'lodash';
import { usePrevious } from '../hooks/use-previous';
import { useDeepCompareEffect } from '../hooks';
import { FilterTablePod } from './filter-table-pod';

export function useFilterTable<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
>({
    carbonLocale,
    isDisabled,
    locale,
    localize,
    localizeEnumMember,
    node,
    nodeNames,
    onChange,
    parameterMode,
    parameters = [],
    selectedProperties,
    value = [],
    automaticColumnsSpacing = false,
    isParentColumnHidden = false,
    filterValueAction,
    filterValueActionLabel,
    propertyAction,
    propertyActionLabel,
}: FilterTableProps<P>): UseFilterTableHook<Cols> {
    const previousValue = usePrevious(value);
    const filterableProperties = React.useMemo(
        () => Object.values(selectedProperties ?? {}).filter(isPropertyFilterable),
        [selectedProperties],
    );

    const onCellChange = React.useCallback<OnCellChange<Cols, E>>(
        changes => {
            dispatch({ type: 'CELL_CHANGED', changes, selectedProperties: selectedProperties ?? {} });
        },
        [selectedProperties],
    );

    const onRowAdded = React.useCallback<
        NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['onRowAdded']>
    >(() => {
        dispatch({ type: 'ROW_ADDED' });
    }, []);

    const onRowRemoved = React.useCallback<
        NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['onRowRemoved']>
    >((row): void => {
        dispatch({ type: 'ROW_REMOVED', row });
    }, []);

    const columnProperty = React.useMemo(
        (): FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['columns'][number] => ({
            id: 'property',
            header: {
                name: localize('@sage/xtrem-ui-components/property', 'Property'),
                width: automaticColumnsSpacing ? undefined : 200,
            },
            cellRenderer: ({ rowData }) => (
                <FilterTablePropertyColumn<Cols, P, E>
                    filterableProperties={filterableProperties}
                    isDisabled={isDisabled}
                    localize={localize}
                    node={node}
                    nodeNames={nodeNames}
                    onCellChange={onCellChange}
                    rowData={rowData}
                    isParentColumnHidden={isParentColumnHidden}
                    propertyAction={propertyAction}
                    propertyActionLabel={propertyActionLabel}
                />
            ),
        }),
        [
            localize,
            automaticColumnsSpacing,
            filterableProperties,
            isDisabled,
            node,
            nodeNames,
            onCellChange,
            isParentColumnHidden,
            propertyAction,
            propertyActionLabel,
        ],
    );

    const columnFilterType = React.useMemo(
        (): FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['columns'][number] => ({
            id: 'filterType',
            header: {
                name: localize('@sage/xtrem-ui-components/filter-type', 'Filter type'),
                width: automaticColumnsSpacing ? undefined : 200,
            },
            cellRenderer: ({ rowData }) => (
                <FilterTableTypeColumnMemo
                    isDisabled={isDisabled}
                    localize={localize}
                    onCellChange={onCellChange}
                    rowData={rowData}
                />
            ),
        }),
        [localize, automaticColumnsSpacing, isDisabled, onCellChange],
    );
    const columnFilterValue = React.useMemo(
        (): FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['columns'][number] => ({
            id: 'filterValue',
            header: {
                name: localize('@sage/xtrem-ui-components/filter-value', 'Filter value'),
                width: automaticColumnsSpacing ? undefined : 250,
            },
            cellRenderer: ({ rowData, extraData: errors }): React.ReactElement => (
                <FilterTableValueColumnMemo
                    carbonLocale={carbonLocale}
                    errors={errors as Validations<Cols>}
                    isDisabled={isDisabled}
                    locale={locale}
                    localize={localize}
                    localizeEnumMember={localizeEnumMember}
                    node={node}
                    onCellChange={onCellChange}
                    parameterMode={parameterMode}
                    parameters={parameters}
                    rowData={rowData}
                    filterValueAction={filterValueAction}
                    filterValueActionLabel={filterValueActionLabel}
                />
            ),
        }),
        [
            localize,
            automaticColumnsSpacing,
            carbonLocale,
            isDisabled,
            locale,
            localizeEnumMember,
            node,
            onCellChange,
            parameterMode,
            parameters,
            filterValueAction,
            filterValueActionLabel,
        ],
    );

    const columnShouldUseParameter = React.useMemo(
        (): FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['columns'][number] => ({
            id: 'parameter',
            header: {
                name: localize('@sage/xtrem-ui-components/filter-use-parameter', 'Use parameter'),
                width: 125,
            },
            cellRenderer: ({ rowData, filterTypeMode }) => (
                <FilterTableParameterColumnMemo
                    isDisabled={isDisabled}
                    rowData={rowData}
                    onCellChange={onCellChange}
                    mode={filterTypeMode}
                    localize={localize}
                />
            ),
        }),
        [onCellChange, isDisabled, localize],
    );

    const columns = React.useMemo<
        FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['columns']
    >((): FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['columns'] => {
        return [
            columnProperty,
            columnFilterType,
            ...(parameterMode === 'creation' || (parameterMode === 'usage' && parameters.length > 0)
                ? [columnShouldUseParameter]
                : []),
            columnFilterValue,
            { id: 'path', isHidden: true, header: { name: 'Path' } },
            { id: 'labelPath', isHidden: true, header: { name: 'Label path' } },
            { id: 'key', isHidden: true, header: { name: 'Key' } },
            { id: 'labelKey', isHidden: true, header: { name: 'Label key' } },
        ];
    }, [
        columnProperty,
        columnFilterType,
        parameterMode,
        parameters.length,
        columnShouldUseParameter,
        columnFilterValue,
    ]);

    const onRowDrag = React.useCallback<
        NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['onRowDrag']>
    >(ids => {
        dispatch({ type: 'ROW_DRAGGED', ids });
    }, []);

    const addButtonText = React.useMemo<string>(
        () => localize('@sage/xtrem-ui-components/add-filter', 'Add filter'),
        [localize],
    );

    const data = React.useMemo<FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['data']>(() => {
        return mapFilterToEditorRow<Cols, P, E>({ value, selectedProperties });
    }, [value, selectedProperties]);

    const [tableDefinition, dispatch] = React.useReducer(filterTableReducer<Cols, P, E>()(localize), {
        addButtonText,
        canAddNewLines: true,
        canDrag: false,
        columns,
        counter: value.length,
        data,
        onRowAdded,
        onRowDrag,
        onRowRemoved,
        parameterMode,
        validations: {},
    });

    useDeepCompareEffect(() => {
        dispatch({ type: 'COLUMNS_CHANGED', columns });
    }, [selectedProperties, dispatch]);

    React.useEffect(() => {
        onChange(
            tableDefinition.data
                .filter(row => isCompleteRow(row, tableDefinition.validations, parameterMode))
                .map(({ property, filterType, filterValue, path, labelPath, key, labelKey, parameter, ...rest }) => {
                    return {
                        // spread needed in case columns are extended
                        ...rest,
                        label: property!.label,
                        filterType: filterType!,
                        filterValue,
                        data: property!.data,
                        id: path!,
                        labelPath: labelPath!,
                        property,
                        key,
                        labelKey,
                        parameter,
                    };
                }),
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tableDefinition.data, tableDefinition.parameterMode, tableDefinition.validations]);

    React.useEffect(() => {
        const newValue = mapFilterToEditorRow({ value, selectedProperties });
        if (!isEqual(previousValue, value) && !isEqual(newValue, tableDefinition.data)) {
            dispatch({ type: 'DATA_RESET', value, selectedProperties });
        }
    }, [value, selectedProperties, tableDefinition.data, previousValue]);

    return {
        ...tableDefinition,
        extraData: tableDefinition.validations,
        canRemoveLines: true,
        emptyStateText:
            filterableProperties.length === 0
                ? localize(
                      '@sage/xtrem-ui-components/widget-editor-no-filterable-properties',
                      'You cannot filter the current values. You can select different data or continue without filtering.',
                  )
                : undefined,
        actionsText: localize('@sage/xtrem-ui-components/actions', 'Actions'),
        isAddButtonHidden: filterableProperties.length === 0,
        onCellChange,
    } as unknown as UseFilterTableHook<Cols, E>;
}

export function FilterTableComponent(props: FilterTableProps): React.ReactElement {
    const {
        actionsText,
        addButtonText,
        canAddNewLines,
        canDrag,
        canRemoveLines,
        columns,
        data,
        emptyStateText,
        extraData,
        isAddButtonHidden,
        onRowAdded,
        onRowDrag,
        onRowRemoved,
    } = useFilterTable(props);

    if (props.mode === 'pod') {
        return (
            <FilterTablePod
                addButtonText={addButtonText}
                columns={columns}
                data={data}
                extraData={extraData}
                emptyStateText={emptyStateText}
                isAddButtonHidden={isAddButtonHidden}
                onPodAdded={onRowAdded}
                onPodRemoved={onRowRemoved}
                localize={props.localize}
            />
        );
    }
    return (
        <FlatTable
            actionsText={actionsText}
            addButtonText={addButtonText}
            canAddNewLines={!props.isDisabled && canAddNewLines}
            canDrag={canDrag}
            canRemoveLines={canRemoveLines}
            columns={columns}
            data={data}
            emptyStateText={emptyStateText}
            extraData={extraData}
            isAddButtonHidden={isAddButtonHidden}
            onRowAdded={onRowAdded}
            onRowDrag={onRowDrag}
            onRowRemoved={onRowRemoved}
        />
    );
}
