/* eslint-disable react/no-unused-prop-types */
import React from 'react';
import type {
    Validations,
    DefaultFilterCols,
    OnCellChange,
    FilterParameter,
    ParameterHandlingMode,
} from './filter-table-types';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import { GraphQLTypes, RANGE_DIVIDER } from '@sage/xtrem-shared';
import type { LocalizeLocale, FilterTypeValue, LocalizeFunction } from '@sage/xtrem-shared';
import { FilterableSelect, Option } from 'carbon-react/esm/components/select';
import { includes } from 'lodash';
import { SplitBox } from './filter-table-utils';
import I18nProvider from 'carbon-react/esm/components/i18n-provider';
import DateRange from 'carbon-react/esm/components/date-range';
import Textbox from 'carbon-react/esm/components/textbox';
import type { CarbonLocaleType } from '../types';
import { TextValueInput } from '../shared-table-column/text-value-input';
import { DateValueInput } from '../shared-table-column/date-value-input';
import { EnumValueInput } from '../shared-table-column/enum-value-input';
import { BooleanValueInput } from '../shared-table-column/boolean-value-input';
import { ParameterValueInput } from '../shared-table-column/parameter-value-input';
import { filterableGraphqlTypes } from '../utils';
import { ParameterRangeValueInput } from '../shared-table-column/parameter-range-value-input';

export interface FilterTableValueColumnProps<Cols extends ColDef[] = [], E = unknown> {
    carbonLocale: CarbonLocaleType;
    errors: Validations<Cols>;
    isDisabled?: boolean;
    locale: LocalizeLocale;
    localize: LocalizeFunction;
    localizeEnumMember: (enumFullPathName: string, memberName: string) => string;
    node?: string;
    onCellChange: OnCellChange<Cols, E>;
    parameterMode?: ParameterHandlingMode;
    parameters?: Array<FilterParameter>;
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultFilterCols, Cols>, E>['data'][number];
    filterValueAction?: () => void;
    filterValueActionLabel?: string;
}

function DynamicTimeFrameFilterValueInput<Cols extends ColDef[] = [], E = unknown>({
    isDisabled,
    localize,
    onCellChange,
    rowData,
}: FilterTableValueColumnProps<Cols, E>): React.ReactElement {
    return (
        <FilterableSelect
            disabled={isDisabled}
            openOnFocus={true}
            data-testid={`e-widget-editor-filter-value-timeframe-${rowData._id}`}
            onChange={({ target: { value } }): void => {
                onCellChange({
                    columnId: 'filterValue',
                    rowId: rowData._id,
                    value: value as FilterTypeValue | 'timeFrame' | undefined,
                    rowData,
                });
            }}
            size="small"
            value={rowData.filterValue ?? ''}
        >
            <Option
                text={localize('@sage/xtrem-ui-components/current-month', 'Current month')}
                value="same-month"
                key="same-month"
            />
            <Option
                text={localize('@sage/xtrem-ui-components/current-year', 'Current year')}
                value="same-year"
                key="same-year"
            />
            <Option
                text={localize('@sage/xtrem-ui-components/previous-month', 'Previous month')}
                value="previous-month"
                key="previous-month"
            />
            <Option
                text={localize('@sage/xtrem-ui-components/previous-year', 'Previous year')}
                value="previous-year"
                key="previous-year"
            />
            <Option
                text={localize('@sage/xtrem-ui-components/last-seven-days', 'Last 7 days')}
                value="last-7-days"
                key="last-7-days"
            />
            <Option
                text={localize('@sage/xtrem-ui-components/last-thirty-days', 'Last 30 days')}
                value="last-30-days"
                key="last-30-days"
            />
            <Option
                text={localize('@sage/xtrem-ui-components/previous-day', 'Previous day')}
                value="previous-day"
                key="previous-day"
            />
            <Option
                text={localize('@sage/xtrem-ui-components/current-day', 'Current day')}
                value="same-day"
                key="same-day"
            />
            <Option
                text={localize('@sage/xtrem-ui-components/previous-week', 'Previous week')}
                value="previous-week"
                key="previous-week"
            />
            <Option
                text={localize('@sage/xtrem-ui-components/current-week', 'Current week')}
                value="same-week"
                key="same-week"
            />
            <Option text={localize('@sage/xtrem-ui-components/next-day', 'Next day')} value="next-day" key="next-day" />
            <Option
                text={localize('@sage/xtrem-ui-components/next-week', 'Next week')}
                value="next-week"
                key="next-week"
            />
            <Option
                text={localize('@sage/xtrem-ui-components/next-month', 'Next month')}
                value="next-month"
                key="next-month"
            />
            <Option
                text={localize('@sage/xtrem-ui-components/next-year', 'Next year')}
                value="next-year"
                key="next-year"
            />
        </FilterableSelect>
    );
}

function DateRangeFilterValueInput<Cols extends ColDef[] = [], E = unknown>({
    carbonLocale,
    errors,
    isDisabled,
    locale,
    onCellChange,
    rowData,
}: FilterTableValueColumnProps<Cols, E>): React.ReactElement {
    return (
        <div className="e-widget-editor-flat-table-date">
            <I18nProvider locale={carbonLocale(locale || 'en-US')}>
                <DateRange
                    startDateProps={{
                        disabled: isDisabled || !rowData.property || !rowData.filterType,
                    }}
                    endDateProps={{
                        disabled: isDisabled || !rowData.property || !rowData.filterType,
                    }}
                    tooltipPosition="top"
                    startError={errors[rowData._id]?.filterValue?.split(RANGE_DIVIDER)?.[0]}
                    endError={errors[rowData._id]?.filterValue?.split(RANGE_DIVIDER)?.[1]}
                    value={(rowData.filterValue ?? [{}, {}]).map((v: any) => v?.formattedValue || v?.rawValue || '')}
                    onChange={({ target: { value: values } }): void => {
                        onCellChange({
                            columnId: 'filterValue',
                            rowId: rowData._id,
                            value: values,
                            rowData,
                        });
                    }}
                />
            </I18nProvider>
        </div>
    );
}

function NumberRangeFilterValueInput<Cols extends ColDef[] = [], E = unknown>({
    errors,
    isDisabled,
    localize,
    onCellChange,
    rowData,
}: FilterTableValueColumnProps<Cols, E>): React.ReactElement {
    return (
        <SplitBox display="flex" alignItems="center" justifyContent="center" gap={1}>
            <Textbox
                error={errors[rowData._id]?.filterValue?.split(RANGE_DIVIDER)?.[0]}
                tooltipPosition="top"
                disabled={isDisabled || !rowData.property || !rowData.filterType}
                data-testid={`e-widget-editor-filter-value-min-${rowData._id}`}
                onChange={({ target: { value } }: { target: { value: string } }): void => {
                    onCellChange({
                        columnId: 'filterValue',
                        rowId: rowData._id,
                        value: [value, rowData.filterValue?.split(RANGE_DIVIDER)[1] ?? ''].join(RANGE_DIVIDER),
                        rowData,
                    });
                }}
                placeholder={localize('@sage/xtrem-ui-components/select-filter-value-min', 'Minimum value')}
                size="small"
                value={rowData.filterValue?.split(RANGE_DIVIDER)[0] ?? ''}
            />
            <Textbox
                error={errors[rowData._id]?.filterValue?.split(RANGE_DIVIDER)?.[1]}
                tooltipPosition="top"
                disabled={isDisabled || !rowData.property || !rowData.filterType}
                data-testid={`e-widget-editor-filter-value-max-${rowData._id}`}
                onChange={({ target: { value } }: { target: { value: string } }): void => {
                    onCellChange({
                        columnId: 'filterValue',
                        rowId: rowData._id,
                        value: [rowData.filterValue?.split(RANGE_DIVIDER)[0] ?? '', value].join(RANGE_DIVIDER),
                        rowData,
                    });
                }}
                placeholder={localize('@sage/xtrem-ui-components/select-filter-value-max', 'Maximum value')}
                size="small"
                value={rowData.filterValue?.split(RANGE_DIVIDER)[1] ?? ''}
            />
        </SplitBox>
    );
}

export function FilterTableValueColumn<Cols extends ColDef[] = [], E = unknown>(
    props: FilterTableValueColumnProps<Cols, E>,
): React.ReactElement | null {
    const isDisabled = props.isDisabled || !props.rowData.property || !props.rowData.filterType;
    const { rowData } = props;

    // When the user enables the parameter value for the current row, we render a selection instead
    if (rowData.parameter) {
        /**
         * If the mode is `creation` a new parameter is created automatically when the user uses parameter for the field
         * and it is automatically set and cannot be changed by the user.
         *  */
        if (props.parameterMode === 'creation') {
            return null;
        }

        /**
         * In `usage` mode the user chooses from a predefined list of parameters which is supplied by the consuming component.
         */

        if (rowData.filterType === 'inRange') {
            return <ParameterRangeValueInput {...props} columnId="filterValue" />;
        }
        return <ParameterValueInput {...props} columnId="filterValue" isDisabled={isDisabled} />;
    }

    if (rowData.filterType === 'empty' || rowData.filterType === 'notEmpty') {
        return null;
    }

    // date & date-time
    if (
        rowData.property?.data?.type &&
        includes([GraphQLTypes.Date, GraphQLTypes.DateTime], rowData.property.data.type)
    ) {
        if (rowData.filterType === 'timeFrame') {
            return <DynamicTimeFrameFilterValueInput<Cols, E> {...props} isDisabled={isDisabled} />;
        }
        if (rowData.filterType === 'inRange') {
            return <DateRangeFilterValueInput<Cols, E> {...props} isDisabled={isDisabled} />;
        }
        return <DateValueInput {...props} columnId="filterValue" isDisabled={isDisabled} />;
    }

    // numeric ranges
    if (
        rowData.filterType === 'inRange' &&
        rowData.property?.data?.type &&
        includes(
            [GraphQLTypes.Decimal, GraphQLTypes.Float, GraphQLTypes.Int, GraphQLTypes.IntReference],
            rowData.property.data.type,
        )
    ) {
        return <NumberRangeFilterValueInput<Cols, E> {...props} isDisabled={isDisabled} />;
    }

    if (rowData.property?.data?.type === GraphQLTypes.Boolean) {
        // We can't use the common is disabled here because we need the filter type is not selectable for boolean values
        return (
            <BooleanValueInput
                {...props}
                columnId="filterValue"
                isDisabled={props.isDisabled || !props.rowData.property}
            />
        );
    }

    // enums
    if (
        (rowData.property?.data?.type === GraphQLTypes.Enum ||
            (rowData.property?.data.kind &&
                includes(
                    filterableGraphqlTypes.map(type => type.toLowerCase()),
                    rowData.property?.data.kind.toLowerCase(),
                ))) &&
        rowData.property?.data.node &&
        rowData.property?.data.enumValues
    ) {
        const enumType = rowData.property.data.node;
        const enumOptions = rowData.property.data.enumValues;
        return (
            <EnumValueInput
                {...props}
                columnId="filterValue"
                isDisabled={isDisabled}
                enumOptions={enumOptions}
                enumType={enumType}
            />
        );
    }

    return <TextValueInput {...props} columnId="filterValue" isDisabled={isDisabled} />;
}

export const FilterTableValueColumnMemo = React.memo(FilterTableValueColumn);
FilterTableValueColumnMemo.displayName = 'FilterTableValueColumnMemo';
