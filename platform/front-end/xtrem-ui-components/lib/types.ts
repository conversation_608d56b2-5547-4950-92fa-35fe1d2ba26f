import type { NodeDetails, TreeElement } from '@sage/xtrem-shared';
import type { I18nProviderProps } from 'carbon-react/esm/components/i18n-provider';
import type { IconType } from 'carbon-react/esm/components/icon';

export type DefaultPropertyType = TreeElement;

export type BaseTableCols = [{ id: '_id'; type: string }];

export type DataModelProperty = Omit<NodeDetails, 'type'> & {
    namespace?: string;
    type: NodeDetails['type'];
    iconType?: IconType;
};

export type CarbonLocaleType = (selectedLocale: string) => I18nProviderProps['locale'];
