$focus-shadow: 0 0 0 2px var(--colorsSemanticFocus500);

.e-selection-card {
    border-radius: var(--borderRadius100);
    text-align: left;
    box-sizing: content-box;
    padding: 0;
    width: 100%;
    display: flex;
    background-color: var(--colorsUtilityYang100);
    min-height: 80px;
    border: 1px solid var(--colorsUtilityMajor100);
    cursor: pointer;

    &-selected {
        background-color: var(--colorsUtilityMajor100);
    }

    &:focus {
        box-shadow: $focus-shadow;
        outline: none;
    }

    .e-selection-card-image {
        height: 40px;
        width: 40px;
        padding: 16px;

        img {
            height: 40px;
            width: 40px;
        }
    }

    .e-selection-card-content {
        padding-top: 16px;
        padding-bottom: 16px;
        padding-right: 16px;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;

        .e-selection-card-title {
            font-family: var(--fontFamiliesDefault);
            font-style: normal;
            font-weight: 500;
            font-size: var(--fontSizes200);
            line-height: 150%;
        }

        .e-selection-card-description {
            font-family: var(--fontFamiliesDefault);
            font-style: normal;
            font-weight: 400;
            font-size: var(--fontSizes100);
            line-height: 150%;
        }
    }
}
