import * as React from 'react';
import { render, fireEvent } from '@testing-library/react';
import type { SelectionCardProps } from '../selection-card';
import { SelectionCard } from '../selection-card';

describe('selection card', () => {
    let props: SelectionCardProps;

    beforeEach(() => {
        props = {
            title: 'Test title',
            description: 'Test description',
            _id: 'testId',
            isSelected: false,
            onClick: jest.fn(),
            icon: 'http://example.com/test.png',
        };
    });

    it('should render basic details', () => {
        const { queryByTestId } = render(<SelectionCard {...props} />);
        expect(queryByTestId('e-selection-card-title')!.textContent).toEqual('Test title');
        expect(queryByTestId('e-selection-card-description')!.textContent).toEqual('Test description');
    });

    it('should trigger onClick prop when clicked', () => {
        const { queryByTestId } = render(<SelectionCard {...props} />);
        expect(props.onClick).not.toHaveBeenCalled();
        expect(fireEvent.click(queryByTestId('e-selection-card-testId')!));
        expect(props.onClick).toHaveBeenCalled();
    });

    it('should render unselected', () => {
        const { baseElement } = render(<SelectionCard {...props} />);
        expect(baseElement.querySelector('[data-component="icon"]')).toBeNull();
    });

    it('should render selected', () => {
        const { baseElement } = render(<SelectionCard {...props} isSelected={true} />);
        expect(baseElement.querySelector('[data-component="icon"]')).not.toBeNull();
    });
});
