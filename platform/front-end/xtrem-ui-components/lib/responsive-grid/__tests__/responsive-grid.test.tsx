import React from 'react';
import { render, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { GridColumn, GridRow } from '../responsive-grid';

describe('Grid Components', () => {
    describe('GridRow', () => {
        it('should apply custom className', async () => {
            const { container } = render(
                <GridRow columns={2} gutter={5} margin={10} className="custom-class">
                    <div>Child 1</div>
                    <div>Child 2</div>
                </GridRow>,
            );

            await waitFor(() => {
                const rowElement = container.firstChild;
                expect(rowElement).toHaveClass('custom-class');
                expect(rowElement).toHaveClass('e-grid-row-2');
            });
        });
    });

    describe('GridColumn', () => {
        it('should render with default styles', async () => {
            const { container } = render(
                <GridColumn columnSpan={2}>
                    <div>Column Child</div>
                </GridColumn>,
            );

            await waitFor(() => {
                const columnElement = container.firstChild;
                expect(columnElement).toHaveStyle({
                    gridColumn: 'span 2',
                });
            });
        });

        it('should render with custom columnStart', async () => {
            const { container } = render(
                <GridColumn columnSpan={3} columnStart={1}>
                    <div>Column Child</div>
                </GridColumn>,
            );

            await waitFor(() => {
                const columnElement = container.firstChild;
                expect(columnElement).toHaveStyle({
                    gridColumn: '1 / span 3',
                });
            });
        });

        it('should apply custom className', async () => {
            const { container } = render(
                <GridColumn columnSpan={1} className="custom-column-class">
                    <div>Column Child</div>
                </GridColumn>,
            );

            await waitFor(() => {
                const columnElement = container.firstChild;
                expect(columnElement).toHaveClass('custom-column-class');
                expect(columnElement).toHaveClass('e-grid-column-1');
            });
        });
    });
});
