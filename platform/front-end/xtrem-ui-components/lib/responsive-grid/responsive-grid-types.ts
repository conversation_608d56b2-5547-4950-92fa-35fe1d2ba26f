import type { KeyboardEvent<PERSON><PERSON><PERSON>, UIEventHandler } from 'react';

export interface RowProps {
    'data-testid'?: string;
    children: React.ReactNode;
    className?: string;
    columns: number;
    gutter: number;
    margin: number;
    onKeyDown?: KeyboardEventHandler<HTMLDivElement>;
    onScroll?: UIEventHandler<HTMLDivElement>;
    verticalMargin?: number;
}

export interface ColumnProps {
    columnSpan?: number;
    columnStart?: number;
    children: React.ReactNode;
    className?: string;
}
