import React from 'react';
import type { ColumnProps, RowProps } from './responsive-grid-types';

export function GridRow(props: RowProps): React.ReactElement {
    const verticalMargin = props.verticalMargin ?? 16;
    return (
        <div
            className={`e-grid-row e-grid-row-${props.columns} ${props.className || ''}`}
            onScroll={props.onScroll}
            style={{
                gridTemplateColumns: `repeat(${props.columns}, 1fr)`,
                gridGap: props.gutter,
                paddingLeft: props.margin,
                paddingRight: props.margin,
                paddingTop: verticalMargin,
                paddingBottom: verticalMargin,
            }}
            data-testid={props['data-testid']}
        >
            {props.children}
        </div>
    );
}

const getGridColumnStyle = (columnStart?: number, columnSpan?: number): string => {
    return columnStart ? `${columnStart} / span ${columnSpan || 1}` : `span ${columnSpan || 1}`;
};

export function GridColumn(props: React.PropsWithChildren<ColumnProps>): React.ReactElement {
    return (
        <div
            className={`e-grid-column e-grid-column-${props.columnSpan} ${props.className || ''}`}
            style={{ gridColumn: getGridColumnStyle(props.columnStart, props.columnSpan) }}
        >
            {props.children}
        </div>
    );
}
