import { numericFields } from '@sage/xtrem-shared';
import Textbox from 'carbon-react/esm/components/textbox';
import { includes, isEmpty, isEqual, isNil, omit } from 'lodash';
import * as React from 'react';
import { FlatTable } from '../flat-table/flat-table';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import type { DefaultPropertyType } from '../types';
import { ContentTablePresentationColumn } from './content-table-presentation-column';
import { ContentTablePropertyColumn } from './content-table-property-column';
import type {
    ContentTableProps,
    DefaultContentTableCols,
    OnCellChange,
    UseContentTableHook,
} from './content-table-types';
import { isContentRowValid, tableWidgetReducer } from './content-table-utils';
import { usePrevious } from '../hooks/use-previous';
import { useDeepCompareEffect, useDeepCompareMemo } from '../hooks';
import { ContentTableTitleColumn } from './content-table-title-column';
import type { Validations } from '../filter-table/filter-table-types';

export function useContentTable<
    Cols extends ColDef[] = [],
    E = unknown,
    P extends DefaultPropertyType = DefaultPropertyType,
>({
    value,
    contentType,
    isDisabled,
    localize,
    node,
    nodeNames,
    onChange,
    selectedProperties,
    isPropertySelectionDisabled,
}: ContentTableProps<Cols, P, E>): UseContentTableHook<Cols, E> {
    const previousValue = usePrevious(value);

    const onCellChange = React.useCallback<OnCellChange<Cols, E>>(
        changes => {
            dispatch({ type: 'CELL_CHANGED', changes, selectedProperties: selectedProperties ?? {} });
        },
        [selectedProperties],
    );

    const addButtonText = React.useMemo<string>(
        () => localize('@sage/xtrem-ui-components/add-column', 'Add column'),
        [localize],
    );

    const onRowDrag = React.useCallback<
        NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['onRowDrag']>
    >(ids => {
        dispatch({ type: 'ROW_DRAGGED', ids });
    }, []);

    const onRowAdded = React.useCallback<
        NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['onRowAdded']>
    >(dataOverride => {
        dispatch({ type: 'ROW_ADDED', data: dataOverride });
    }, []);

    const onRowRemoved = React.useCallback<
        NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['onRowRemoved']>
    >((row): void => {
        dispatch({ type: 'ROW_REMOVED', row });
    }, []);

    const valueToDataMapper = React.useCallback<
        (
            arg: (typeof value)[number],
            i: number,
        ) => FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'][number]
    >(
        ({ presentation, formatting, labelPath, title, divisor, path, property, ...rest }, index) => {
            return {
                // spread needed in case columns are extended
                ...rest,
                _id: String(index + 1),
                property: selectedProperties?.[path],
                presentation,
                formatting: isNil(formatting) ? undefined : String(formatting),
                type: property?.data?.type,
                path,
                labelPath,
                title,
                divisor: isNil(divisor) ? undefined : String(divisor),
            };
        },
        [selectedProperties],
    );

    const columns = React.useMemo<FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['columns']>(
        () => [
            {
                id: 'property',
                header: { name: localize('@sage/xtrem-ui-components/property', 'Property'), width: 250 },
                cellRenderer: ({ rowData, data: allData }): React.ReactElement => (
                    <ContentTablePropertyColumn
                        allData={allData}
                        isDisabled={isDisabled}
                        isPropertySelectionDisabled={isPropertySelectionDisabled}
                        localize={localize}
                        node={node}
                        nodeNames={nodeNames}
                        onCellChange={onCellChange}
                        rowData={rowData}
                        selectedProperties={selectedProperties}
                    />
                ),
            },
            {
                id: 'title',
                header: { name: localize('@sage/xtrem-ui-components/title', 'Title'), width: 150 },
                cellRenderer: ({ rowData, extraData }): React.ReactElement => {
                    const errors = extraData as Validations<Cols>;

                    return (
                        <ContentTableTitleColumn
                            rowData={rowData}
                            errors={errors}
                            onCellChange={onCellChange}
                            localize={localize}
                            value={rowData.title}
                        />
                    );
                },
            },
            {
                id: 'presentation',
                header: { name: localize('@sage/xtrem-ui-components/presentation', 'Presentation'), width: 180 },
                cellRenderer: ({ rowData }): React.ReactElement => {
                    return (
                        <ContentTablePresentationColumn
                            isDisabled={isDisabled}
                            localize={localize}
                            onCellChange={onCellChange}
                            rowData={rowData}
                            contentType={contentType}
                        />
                    );
                },
            },
            {
                id: 'formatting',
                header: { name: localize('@sage/xtrem-ui-components/decimal-digits', 'Decimal places'), width: 130 },
                cellRenderer: ({ rowData, extraData }): React.ReactElement | null => {
                    if (rowData.property?.data?.type && !includes(numericFields, rowData.property.data.type)) {
                        return null;
                    }

                    const errors = extraData as Validations<Cols>;
                    return (
                        <Textbox
                            error={errors[rowData._id]?.formatting}
                            inputMode="numeric"
                            tooltipPosition="top"
                            data-testid={`e-widget-editor-content-formatting-${rowData._id}`}
                            onChange={({ target: { value: formattingValue } }): void => {
                                onCellChange({
                                    columnId: 'formatting',
                                    rowId: rowData._id,
                                    value: formattingValue,
                                    rowData,
                                });
                            }}
                            placeholder={localize(
                                '@sage/xtrem-ui-components/widget-editor-content-formatting',
                                'Decimal digits',
                            )}
                            size="small"
                            value={rowData.formatting ?? ''}
                        />
                    );
                },
            },
            {
                id: 'divisor',
                header: { name: localize('@sage/xtrem-ui-components/divisor', 'Divisor'), width: 130 },
                cellRenderer: ({ rowData, extraData }): React.ReactElement | null => {
                    if (rowData.property?.data?.type && !includes(numericFields, rowData.property.data.type)) {
                        return null;
                    }
                    const errors = extraData as Validations<Cols>;

                    return (
                        <Textbox
                            error={errors[rowData._id]?.divisor}
                            inputMode="numeric"
                            tooltipPosition="top"
                            data-testid={`e-widget-editor-content-divisor-${rowData._id}`}
                            onChange={({ target: { value: divisorValue } }): void => {
                                onCellChange({
                                    columnId: 'divisor',
                                    rowId: rowData._id,
                                    value: divisorValue,
                                    rowData,
                                });
                            }}
                            placeholder={localize('@sage/xtrem-ui-components/divisor', 'Divisor')}
                            size="small"
                            value={rowData.divisor ?? ''}
                        />
                    );
                },
            },
            { id: 'path', isHidden: true, header: { name: 'Path' } },
            { id: 'labelPath', isHidden: true, header: { name: 'Label path' } },
        ],
        [
            contentType,
            isDisabled,
            isPropertySelectionDisabled,
            localize,
            node,
            nodeNames,
            onCellChange,
            selectedProperties,
        ],
    );

    const [tableDefinition, dispatch] = React.useReducer(tableWidgetReducer<Cols, P, E>()(localize), {
        addButtonText,
        canDrag: true,
        onRowDrag,
        onRowRemoved,
        onRowAdded,
        columns,
        data: value.map(valueToDataMapper),
        counter: (value ?? []).length,
        validations: {},
    });

    const currentIds = useDeepCompareMemo(() => tableDefinition.data.map(d => d._id), [tableDefinition.data]);

    const setOrderedIds = React.useCallback<UseContentTableHook<Cols, E>['setOrderedIds']>(
        ({ newOrderedIds, id, enforceContiguous }) => {
            if (!isEqual(currentIds, newOrderedIds)) {
                dispatch({ type: 'ORDER_CHANGED', orderedIds: newOrderedIds, id, enforceContiguous });
            }
        },
        [currentIds],
    );

    const canAddNewLines = React.useMemo<boolean>(() => {
        return Boolean(
            !isEmpty(selectedProperties) &&
                tableDefinition.data.length < 100 &&
                tableDefinition.data.length < Object.keys(selectedProperties).length,
        );
    }, [tableDefinition.data.length, selectedProperties]);

    useDeepCompareEffect(() => {
        dispatch({ type: 'COLUMNS_CHANGED', columns });
    }, [selectedProperties, dispatch]);

    React.useEffect(() => {
        if (
            !isEqual(previousValue, value) &&
            !isEqual(
                value.map(v => omit(v, ['_id'])),
                tableDefinition.data.map(d => omit(d, ['_id'])),
            )
        ) {
            dispatch({
                type: 'DATA_RESET',
                data: value.map((v, idx) => ({ ...v, _id: String(idx + 1) })),
            });
            return;
        }
        if (isEqual(value, tableDefinition.data)) {
            return;
        }
        (onChange as ContentTableProps<[], P, E>['onChange'])(
            tableDefinition.data
                .filter(row => isContentRowValid({ row, validations: tableDefinition.validations }))
                .map(({ presentation, property, title, formatting, divisor, path, labelPath, ...rest }) => {
                    const result: Parameters<ContentTableProps<[], P, E>['onChange']>[0][number] = {
                        // spread needed in case columns are extended
                        ...rest,
                        presentation: presentation!,
                        title: title!,
                        labelPath: labelPath!,
                        formatting,
                        divisor,
                        property,
                        path,
                    };

                    return result;
                }),
        );
    }, [tableDefinition.data, tableDefinition.validations, onChange, previousValue, value]);

    const setData = React.useCallback(
        (data: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data']) => {
            dispatch({ type: 'DATA_RESET', data });
        },
        [],
    );

    const hook = React.useMemo(
        () =>
            ({
                ...tableDefinition,
                canAddNewLines,
                onCellChange,
                setOrderedIds,
                setData,
            }) as unknown as UseContentTableHook<Cols, E>,
        [canAddNewLines, onCellChange, tableDefinition, setOrderedIds, setData],
    );

    return hook;
}

export function ContentTableComponent(props: ContentTableProps): React.ReactElement {
    const { addButtonText, canAddNewLines, canDrag, columns, data, onRowAdded, onRowDrag, onRowRemoved, validations } =
        useContentTable(props);

    return (
        <FlatTable
            addButtonText={addButtonText}
            canAddNewLines={!props.isDisabled && canAddNewLines}
            canDrag={canDrag}
            columns={columns}
            data={data}
            onRowAdded={onRowAdded}
            onRowDrag={onRowDrag}
            onRowRemoved={onRowRemoved}
            extraData={validations}
        />
    );
}
