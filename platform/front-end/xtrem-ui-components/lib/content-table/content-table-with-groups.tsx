/* eslint-disable react/no-unstable-nested-components */

import * as React from 'react';

import type {
    AggregationOptions,
    Aggregations,
    Dict,
    KeysOfValue,
    LocalizeFunction,
    SortingOptions,
} from '@sage/xtrem-shared';
import {
    aggregationsGraphqlMapping,
    aggregationTranslations,
    GraphQLTypes,
    sortingGraphqlMapping,
    Sortings,
    sortingTranslations,
} from '@sage/xtrem-shared';
import Message from 'carbon-react/esm/components/message';
import { FilterableSelect, Option } from 'carbon-react/esm/components/select';
import type { ToastProps } from 'carbon-react/esm/components/toast';
import { findIndex, isNil, sortBy } from 'lodash';
import type { FilterTableProps } from '../filter-table/filter-table-types';
import { FlatTable } from '../flat-table/flat-table';
import type { FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import { useDeepCompareEffect } from '../hooks';
import { GridColumn, GridRow } from '../responsive-grid/responsive-grid';
import { useContentTable } from './content-table';
import { ContentTableGroupColumn } from './content-table-group-column';
import type {
    AdditionalContentColumns,
    ContentTableProps,
    ContentTableValidations,
    DefaultContentTableCols,
    GroupOrderValidationResult,
    TreeElementWithGroups,
} from './content-table-types';
import { CONTENT_TYPES } from './content-table-types';
import { checkValidGroupOrder, enforceContiguousOrder } from './content-table-utils';
import { isEnumField } from '../utils';

export interface TableContentWithGroupsProps<P extends TreeElementWithGroups = TreeElementWithGroups, E = unknown> {
    isAddButtonHidden?: boolean;
    isDisabled?: boolean;
    isPropertySelectionDisabled?: boolean;
    localize: LocalizeFunction;
    onChange: ContentTableProps<AdditionalContentColumns, P, E>['onChange'];
    selectedItems?: Dict<P>;
    value: Omit<
        FlatTableProps<
            UnrestrictedTableCols<DefaultContentTableCols, AdditionalContentColumns, false>,
            E
        >['data'][number],
        '_id'
    >[];
    onDisplayNotification?: (content: string, type: ToastProps['variant']) => void;
    setGroupOrderError?: (value: boolean) => void;
}

export function TableContentWithGroups<P extends TreeElementWithGroups = TreeElementWithGroups, E = unknown>({
    selectedItems = {},
    onChange,
    localize,
    value,
    isDisabled = false,
    isAddButtonHidden = false,
    isPropertySelectionDisabled = false,
    onDisplayNotification,
    setGroupOrderError,
}: TableContentWithGroupsProps<P, E>): React.ReactElement {
    // TODO: XT-54876 pass node & nodeNames to component
    const nodeNames: FilterTableProps['nodeNames'] = React.useMemo(() => ({}), []);
    const node: FilterTableProps['node'] = React.useMemo(() => '', []);
    const [groupOrderValidation, setGroupOrderValidation] = React.useState<GroupOrderValidationResult>({
        isValid: true,
        invalidFields: [],
    });

    const selectedProperties = React.useMemo(() => {
        const basicTypes = Object.values(GraphQLTypes);
        return Object.keys(selectedItems).reduce<
            NonNullable<Parameters<typeof useContentTable>[0]['selectedProperties']>
        >((acc, curr) => {
            if (
                basicTypes.includes(selectedItems[curr].data?.type as GraphQLTypes) ||
                isEnumField(selectedItems[curr].data)
            ) {
                acc[curr] = selectedItems[curr];
            }
            return acc;
        }, {});
    }, [selectedItems]);

    const displayGroupErrorMessage = React.useCallback(() => {
        if (onDisplayNotification) {
            onDisplayNotification(
                localize(
                    '@sage/xtrem-ui-components/disallowed-group-assignment',
                    'Non-sortable properties cannot be the first items in the group. Select other properties.',
                ),
                'error',
            );
        }
    }, [localize, onDisplayNotification]);

    const {
        addButtonText,
        canAddNewLines,
        canDrag,
        columns,
        counter,
        data,
        onCellChange,
        onRowAdded,
        onRowDrag: defaultOnRowDrag,
        onRowRemoved,
        validations,
        setOrderedIds,
    } = useContentTable<
        AdditionalContentColumns,
        {
            validations: ContentTableValidations<
                [
                    { id: 'group'; type: number },
                    { id: 'operation'; type?: Aggregations | 'NONE' },
                    { id: 'sorting'; type?: SortingOptions },
                ]
            >;
            orderedIds: string[];
        }
    >({
        isPropertySelectionDisabled,
        value,
        localize,
        node,
        nodeNames,
        onChange,
        selectedProperties,
        contentType: CONTENT_TYPES.DOCUMENT_EDITOR,
    });

    const groupColumn = React.useMemo<(typeof columns)[number]>(() => {
        return {
            id: 'group',
            header: {
                name: localize('@sage/xtrem-ui-components/group', 'Group'),
                width: 150,
            },
            // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
            cellRenderer: params => {
                return (
                    <ContentTableGroupColumn
                        data={params.data}
                        rowData={params.rowData}
                        rowIndex={params.rowIndex}
                        localize={localize}
                        setOrderedIds={setOrderedIds}
                        setGroupOrderValidation={setGroupOrderValidation}
                        setGroupOrderError={setGroupOrderError}
                        displayGroupErrorMessage={displayGroupErrorMessage}
                        onCellChange={onCellChange}
                    />
                );
            },
        };
    }, [localize, onCellChange, setOrderedIds, setGroupOrderError, displayGroupErrorMessage]);

    const operationColumn = React.useMemo<(typeof columns)[number]>(
        () => ({
            id: 'operation',
            header: {
                name: localize('@sage/xtrem-ui-components/operation', 'Operation'),
                width: 200,
            },
            // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
            cellRenderer: params => {
                const columnType = params.rowData.property?.data.type as GraphQLTypes | undefined;

                const availableAggregations = columnType ? (aggregationsGraphqlMapping[columnType] ?? []) : [];
                return availableAggregations.length === 0 ? null : (
                    <FilterableSelect
                        openOnFocus={true}
                        data-testid={`e-widget-editor-content-operation-${params.rowData._id}`}
                        onChange={({ target: { value: operationValue } }): void => {
                            const newOperationValue: AggregationOptions['operation'] =
                                operationValue === '' ? 'NONE' : (operationValue as Aggregations);
                            onCellChange({
                                columnId: 'operation',
                                rowId: params.rowData._id,
                                value: newOperationValue,
                                rowData: params.rowData,
                            });
                            if (isNil(params.rowData.sorting)) {
                                onCellChange({
                                    columnId: 'sorting',
                                    rowId: params.rowData._id,
                                    value: Sortings.ascending,
                                    rowData: params.rowData,
                                });
                            }
                        }}
                        placeholder={localize('@sage/xtrem-ui-components/select-value-placeholder', 'Select value...')}
                        size="small"
                        value={isNil(params.rowData.operation) ? '' : String(params.rowData.operation)}
                    >
                        {availableAggregations.map((aggregation: Aggregations) => {
                            const text = aggregationTranslations(localize)[aggregation];
                            return (
                                <Option key={aggregation} text={text} value={aggregation}>
                                    {text}
                                </Option>
                            );
                        })}
                    </FilterableSelect>
                );
            },
        }),
        [onCellChange, localize],
    );

    const sortingColumn = React.useMemo<(typeof columns)[number]>(
        () => ({
            id: 'sorting',
            header: {
                name: localize('@sage/xtrem-ui-components/sorting', 'Sorting'),
                width: 150,
            },
            // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
            cellRenderer: params => {
                const columnType = params.rowData.property?.data.type as GraphQLTypes | undefined;

                const availableSortings = columnType ? (sortingGraphqlMapping[columnType] ?? []) : [];

                if (
                    !params.rowData.path ||
                    !selectedItems[params.rowData.path]?.data?.canFilter ||
                    availableSortings.length === 0
                ) {
                    return null;
                }

                return (
                    <FilterableSelect
                        openOnFocus={true}
                        data-testid={`e-widget-editor-content-sorting-${params.rowData._id}`}
                        onChange={({ target: { value: sortingValue } }): void => {
                            const newSortingValue: TreeElementWithGroups['sorting'] =
                                sortingValue === '' ? Sortings.ascending : (sortingValue as SortingOptions);
                            onCellChange({
                                columnId: 'sorting',
                                rowId: params.rowData._id,
                                value: newSortingValue,
                                rowData: params.rowData,
                            });
                        }}
                        placeholder={localize('@sage/xtrem-ui-components/select-value-placeholder', 'Select value...')}
                        size="small"
                        value={isNil(params.rowData.sorting) ? '' : String(params.rowData.sorting)}
                    >
                        {availableSortings.map((sorting: SortingOptions) => {
                            const text = sortingTranslations(localize)[sorting];
                            return (
                                <Option key={sorting} text={text} value={sorting}>
                                    {text}
                                </Option>
                            );
                        })}
                    </FilterableSelect>
                );
            },
        }),
        [localize, selectedItems, onCellChange],
    );

    const onRowDrag = React.useCallback<NonNullable<typeof defaultOnRowDrag>>(
        newOrderedIds => {
            const orderedData = sortBy(data, d => findIndex(newOrderedIds, element => d._id === element));
            const newData = enforceContiguousOrder({
                orderedData,
                key: 'group' as KeysOfValue<(typeof orderedData)[number], number>,
            });

            const { isValid } = checkValidGroupOrder({ data: newData, enforceContiguous: 'group' });

            if (isValid) {
                setOrderedIds({
                    newOrderedIds: newData.map(d => d.path),
                    id: 'path',
                    enforceContiguous: 'group',
                });
                setGroupOrderValidation({ isValid });
                if (setGroupOrderError) {
                    setGroupOrderError(false);
                }
            } else {
                setOrderedIds({
                    newOrderedIds: data.map(d => d.path),
                    id: 'path',
                    enforceContiguous: 'group',
                });
                displayGroupErrorMessage();
            }
        },
        [data, setGroupOrderError, setOrderedIds, displayGroupErrorMessage],
    );

    const getRowProps = React.useCallback<NonNullable<React.ComponentProps<typeof FlatTable>['getRowProps']>>(
        ({ row, data: tableData, rowIndex }) => {
            const isEven = (row.group ?? 0) % 2 === 0;
            return {
                bgColor: isEven ? undefined : '--colorsUtilityMajor100',
                horizontalBorderColor: isEven ? undefined : '--colorsUtilityYang100',
                ...((tableData[rowIndex + 1]?.group ?? row.group) !== row.group
                    ? {
                          horizontalBorderSize: 'medium',
                          horizontalBorderColor: '--colorsUtilityMajor400',
                      }
                    : {}),
            };
        },
        [],
    );

    const propertyColumn = React.useMemo<(typeof columns)[number]>(() => columns[0], [columns]);
    const titleColumn = React.useMemo<(typeof columns)[number]>(() => columns[1], [columns]);
    const pathColumn = React.useMemo<(typeof columns)[number]>(() => columns[5], [columns]);

    const flatTableColumns = React.useMemo<typeof columns>(
        () => [propertyColumn, titleColumn, groupColumn, operationColumn, sortingColumn, pathColumn],
        [groupColumn, operationColumn, pathColumn, propertyColumn, sortingColumn, titleColumn],
    );

    const hasDefaultTitles = React.useRef(false);

    useDeepCompareEffect(() => {
        if (hasDefaultTitles.current || !data.length) {
            return;
        }
        hasDefaultTitles.current = true;
        data.forEach(row => {
            onCellChange({ columnId: 'title', rowData: row, rowId: row._id, value: row.title });
        });
    }, [data, onCellChange]);

    useDeepCompareEffect(() => {
        const { isValid, invalidFields } = checkValidGroupOrder({ data, enforceContiguous: 'group' });
        setGroupOrderValidation({ isValid, invalidFields });
        if (setGroupOrderError) {
            setGroupOrderError(!isValid);
        }
    }, [data, setGroupOrderError]);

    const extraData = React.useMemo(() => ({ validations }), [validations]);
    const actionsText = React.useMemo(() => localize('@sage/xtrem-ui-components/actions', 'Actions'), [localize]);
    const emptyStateText = React.useMemo(
        () =>
            Object.keys(selectedProperties).length === 0
                ? localize(
                      '@sage/xtrem-ui-components/widget-editor-no-selectable-properties',
                      'Select a property to add a value.',
                  )
                : undefined,
        [localize, selectedProperties],
    );

    const handleRowAdded = React.useCallback(() => {
        // add row to the last group
        onRowAdded?.({ group: data[data.length - 1]?.group ?? 0 });
        setOrderedIds({
            newOrderedIds: [...data.map(d => d._id), String(counter + 1)],
            id: '_id',
            enforceContiguous: 'group',
        });
    }, [onRowAdded, data, counter, setOrderedIds]);

    return (
        <GridRow columns={8} gutter={16} margin={0} verticalMargin={0} className="document-editor-content-table">
            <GridColumn columnSpan={8}>
                {!groupOrderValidation.isValid && (
                    <div data-testid="e-widget-editor-group-order-error-message">
                        <Message variant="error" mb="16px">
                            {!groupOrderValidation.isValid && (
                                <div>
                                    {localize(
                                        '@sage/xtrem-ui-components/disallowed-group-assignment-list-properties',
                                        `The following properties: ${
                                            groupOrderValidation.invalidFields ||
                                            [].map(fieldName => `"${fieldName}"`).join(', ')
                                        }, are non-sortable and cannot be the first items in the group.`,
                                    )}
                                </div>
                            )}
                        </Message>
                    </div>
                )}
                <FlatTable
                    actionsText={actionsText}
                    addButtonText={addButtonText}
                    canAddNewLines={!isDisabled && canAddNewLines}
                    canDrag={canDrag}
                    canRemoveLines={!isDisabled}
                    columns={flatTableColumns}
                    data={data}
                    emptyStateText={emptyStateText}
                    extraData={extraData}
                    isAddButtonHidden={isAddButtonHidden}
                    getRowProps={getRowProps}
                    onRowAdded={handleRowAdded}
                    onRowDrag={onRowDrag}
                    onRowRemoved={onRowRemoved}
                />
            </GridColumn>
        </GridRow>
    );
}
