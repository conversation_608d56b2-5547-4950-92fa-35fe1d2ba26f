import type {
    AggregationOptions,
    Dict,
    GridNestedFieldTypes,
    LocalizeFunction,
    presentationEditorGraphqlMapping,
    presentationGraphqlMapping,
    SortingOptions,
    TreeElement,
} from '@sage/xtrem-shared';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import type { BaseTableCols, DataModelProperty, DefaultPropertyType } from '../types';

export type ContentTableValidations<Cols extends ColDef[] = []> = Record<
    string,
    Record<UnrestrictedTableCols<DefaultContentTableCols, Cols>[number]['id'], string>
>;

export enum CONTENT_TYPES {
    DOCUMENT_EDITOR = 'documentEditor',
}

export type DefaultContentTableCols<P extends DefaultPropertyType = DefaultPropertyType> = [
    ...BaseTableCols,
    { id: 'presentation'; type?: GridNestedFieldTypes },
    { id: 'title'; type?: string },
    { id: 'formatting'; type?: string },
    { id: 'divisor'; type?: string },
    { id: 'property'; type?: P },
    { id: 'path'; type: string },
    { id: 'labelPath'; type?: string },
];

export type OnCellChange<Cols extends ColDef[] = [], E = unknown> = (
    args: {
        [P in UnrestrictedTableCols<DefaultContentTableCols, Cols>[number]['id']]: {
            columnId: P;
            rowId: string;
            value: Extract<UnrestrictedTableCols<DefaultContentTableCols, Cols>[number], { id: P }>['type'];
            rowData: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'][number];
        };
    }[UnrestrictedTableCols<DefaultContentTableCols, Cols>[number]['id']],
) => void;

export type WidgetContentState<Cols extends ColDef[], E = unknown> = {
    addButtonText: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['addButtonText'];
    canDrag: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['canDrag'];
    onRowDrag: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['onRowDrag'];
    onRowRemoved: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['onRowRemoved'];
    onRowAdded: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['onRowAdded'];
    columns: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['columns'];
    data: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'];
    counter: number;
    validations: ContentTableValidations<Cols>;
};

export interface GroupOrderValidationResult {
    isValid: boolean;
    invalidFields?: string[];
}

export type TableProperty = DefaultPropertyType & {
    presentation: GridNestedFieldTypes;
    formatting?: number;
    title: string;
    divisor?: number;
};
export type ContentActionExtractor<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
    A extends ContentAction<Cols, P, E>['type'] = any,
> = Extract<ContentAction<Cols, P, E>, { type: A }>;

export type UnrestrictedOnCellChange<Cols extends ColDef[] = [], E = unknown> = (
    args: {
        [P in UnrestrictedTableCols<DefaultContentTableCols, Cols, false>[number]['id']]: {
            columnId: P;
            rowId: string;
            value: Extract<UnrestrictedTableCols<DefaultContentTableCols, Cols, false>[number], { id: P }>['type'];
            rowData: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols, false>, E>['data'][number];
        };
    }[UnrestrictedTableCols<DefaultContentTableCols, Cols, false>[number]['id']],
) => void;

type Changes<Cols extends ColDef[] = [], E = unknown> = Parameters<OnCellChange<Cols, E>>[0];

export type ContentAction<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
> =
    | {
          type: 'ORDER_CHANGED';
          orderedIds: string[];
          id: UnrestrictedTableCols<DefaultContentTableCols, Cols, false>[number]['id'];
          enforceContiguous?: Extract<
              UnrestrictedTableCols<DefaultContentTableCols, Cols, false>[number],
              { type: number }
          >['id'];
      }
    | {
          type: 'DATA_RESET';
          data: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'];
      }
    | {
          type: 'GROUPS_ERROR_RESET';
      }
    | {
          type: 'ROW_ADDED';
          data?: any;
      }
    | {
          type: 'ROW_DRAGGED';
          ids: Parameters<
              NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['onRowDrag']>
          >[0];
      }
    | {
          type: 'COLUMNS_CHANGED';
          columns: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['columns'];
      }
    | {
          type: 'CELL_CHANGED';
          changes: Changes<Cols, E>;
          selectedProperties: Dict<P>;
      }
    | {
          type: 'ROW_REMOVED';
          row: Parameters<
              NonNullable<FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['onRowRemoved']>
          >[0];
      };

export type UseContentTableHook<Cols extends ColDef[] = [], E = unknown> = Omit<
    WidgetContentState<Cols, E>,
    'columns' | 'data'
> & {
    data: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols, false>, E>['data'];
    columns: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols, false>, E>['columns'];
    canAddNewLines: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols, false>, E>['canAddNewLines'];
    onCellChange: UnrestrictedOnCellChange<Cols, E>;
    setOrderedIds: (args: {
        newOrderedIds: string[];
        id: UnrestrictedTableCols<DefaultContentTableCols, Cols, false>[number]['id'];
        enforceContiguous?: Extract<
            UnrestrictedTableCols<DefaultContentTableCols, Cols, false>[number],
            { type: number }
        >['id'];
    }) => void;
    setData: (data: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols, false>, E>['data']) => void;
};

export type ContentTableProps<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
> = {
    contentType?: CONTENT_TYPES;
    isDisabled?: boolean;
    isPropertySelectionDisabled?: boolean;
    localize: LocalizeFunction;
    node: string | undefined;
    nodeNames: Dict<string>;
    onChange: (newData: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols, false>, E>['data']) => void;
    selectedProperties: Dict<P> | undefined;
    value: Omit<FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'][number], '_id'>[];
};

export type PresentationMapping = typeof presentationGraphqlMapping | typeof presentationEditorGraphqlMapping;

export interface TreeElementWithGroups<T extends any = DataModelProperty> extends TreeElement<T> {
    group?: number;
    operation?: AggregationOptions['operation'];
    sorting?: SortingOptions;
    title?: string;
}

export type AdditionalContentColumns = [
    { id: 'group'; type: number },
    { id: 'operation'; type?: AggregationOptions['operation'] },
    { id: 'sorting'; type?: SortingOptions },
];
