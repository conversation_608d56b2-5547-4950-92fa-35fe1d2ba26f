import type { LocalizeFunction } from '@sage/xtrem-shared';
import React from 'react';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import { type DefaultContentTableCols, type OnCellChange } from './content-table-types';
import Textbox from 'carbon-react/esm/components/textbox';
import type { Validations } from '../filter-table/filter-table-types';

export interface ContentTableTitleColumnProps<Cols extends ColDef[] = [], E = Validations<Cols>> {
    errors: E;
    localize: LocalizeFunction;
    value?: string;
    onCellChange: OnCellChange<Cols, E>;
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'][number];
}

export function ContentTableTitleColumn<Cols extends ColDef[] = [], E extends Validations<Cols> = Validations<Cols>>({
    rowData,
    errors,
    onCellChange,
    localize,
    value: propValue,
}: ContentTableTitleColumnProps<Cols, E>): React.ReactElement {
    const [titleValue, setTitleValue] = React.useState(propValue);
    React.useEffect(() => {
        setTitleValue(propValue);
    }, [propValue]);
    const onChange = React.useCallback<NonNullable<React.ComponentProps<typeof Textbox>['onChange']>>(
        ({ target: { value } }): void => {
            setTitleValue(value);
        },
        [],
    );
    const onBlur = React.useCallback<NonNullable<React.ComponentProps<typeof Textbox>['onBlur']>>((): void => {
        if (titleValue === propValue) {
            return;
        }
        onCellChange({
            columnId: 'title',
            rowId: rowData._id,
            value: titleValue,
            rowData,
        });
    }, [titleValue, propValue, onCellChange, rowData]);

    return (
        <Textbox
            error={errors[rowData._id]?.title}
            disabled={!rowData.property}
            inputMode="text"
            tooltipPosition="top"
            data-testid={`e-widget-editor-content-title-${rowData._id}`}
            onChange={onChange}
            onBlur={onBlur}
            placeholder={localize('@sage/xtrem-ui-components/title', 'Title')}
            size="small"
            value={titleValue ?? ''}
        />
    );
}
