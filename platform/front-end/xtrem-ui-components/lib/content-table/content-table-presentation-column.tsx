import type { FieldKey, LocalizeFunction, GridNestedFieldTypes, GraphQLTypes } from '@sage/xtrem-shared';
import { presentationEditorGraphqlMapping, presentationGraphqlMapping } from '@sage/xtrem-shared';
import { FilterableSelect, Option } from 'carbon-react/esm/components/select';
import Typography from 'carbon-react/esm/components/typography';
import React from 'react';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import {
    CONTENT_TYPES,
    type DefaultContentTableCols,
    type OnCellChange,
    type PresentationMapping,
} from './content-table-types';
import { presentationTranslations } from './content-table-utils';

export interface ContentTablePresentationColumnProps<Cols extends ColDef[] = [], E = unknown>
    extends Partial<React.ComponentProps<typeof FilterableSelect>> {
    isDisabled?: boolean;
    localize: LocalizeFunction;
    onCellChange: OnCellChange<Cols, E>;
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'][number];
    contentType?: CONTENT_TYPES;
}

export function ContentTablePresentationColumn<Cols extends ColDef[] = [], E = unknown>({
    isDisabled,
    localize,
    onCellChange,
    rowData,
    contentType,
    ...rest
}: ContentTablePresentationColumnProps<Cols, E>): React.ReactElement {
    const presentationMapping = React.useMemo<PresentationMapping>(
        () =>
            contentType === CONTENT_TYPES.DOCUMENT_EDITOR
                ? presentationEditorGraphqlMapping
                : presentationGraphqlMapping,
        [contentType],
    );
    const columnType = rowData.property?.data.type as GraphQLTypes | undefined;

    if (columnType && presentationMapping[columnType].length === 1) {
        return (
            <div data-testid={`e-widget-editor-content-presentation-${rowData._id}`}>
                <Typography variant="p" m={0} paddingLeft="8px">
                    {presentationTranslations(localize)[presentationMapping[columnType][0]]}
                </Typography>
            </div>
        );
    }

    return (
        <FilterableSelect
            disabled={!rowData.property || isDisabled}
            openOnFocus={true}
            data-testid={`e-widget-editor-content-presentation-${rowData._id}`}
            // @ts-expect-error "onChange" is actually triggered with { target: { value: GridNestedFieldTypes } }
            onChange={({ target: { value } }: { target: { value: GridNestedFieldTypes } }): void => {
                onCellChange({ columnId: 'presentation', rowId: rowData._id, value, rowData });
            }}
            placeholder={localize('@sage/xtrem-ui-components/select', 'Select...')}
            size="small"
            value={rowData.presentation ?? ''}
            {...rest}
        >
            {(columnType ? presentationMapping[columnType] : []).map((p: FieldKey) => (
                <Option text={presentationTranslations(localize)[p]} value={p} key={p} />
            ))}
        </FilterableSelect>
    );
}
