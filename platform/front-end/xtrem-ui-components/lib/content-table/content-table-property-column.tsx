import React from 'react';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import type { Dict, LocalizeFunction } from '@sage/xtrem-shared';
import type { DefaultContentTableCols, OnCellChange } from './content-table-types';
import { PropertyTableHeader } from '../property-template-header/property-table-header';
import { getPropertyParentNode } from '../filter-table/filter-table-utils';
import { FilterableSelect, OptionRow } from 'carbon-react/esm/components/select';
import type { DefaultPropertyType } from '../types';
import { useDeepCompareMemo } from '../hooks/use-deep-compare-memo';

export interface ContentTablePropertyColumnProps<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
> extends Partial<React.ComponentProps<typeof FilterableSelect>> {
    allData: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'];
    isDisabled?: boolean;
    isPropertySelectionDisabled?: boolean;
    localize: LocalizeFunction;
    node?: string;
    nodeNames: Dict<string>;
    onCellChange: OnCellChange<Cols, E>;
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'][number];
    selectedProperties?: Dict<P>;
}

export function ContentTablePropertyColumn<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
>({
    allData,
    isDisabled,
    isPropertySelectionDisabled,
    localize,
    node,
    nodeNames,
    onCellChange,
    rowData,
    selectedProperties,
    ...rest
}: ContentTablePropertyColumnProps<Cols, P, E>): React.ReactElement {
    const options = useDeepCompareMemo(
        () =>
            Object.values(selectedProperties ?? {}).map(s => {
                const { id, label, labelPath, path, data } = s;
                return { id: id ?? path, label, labelPath, path, data };
            }),
        [selectedProperties],
    );

    const value = useDeepCompareMemo(() => {
        const { id, label, labelPath, path, data } = rowData.property ?? {};
        return { id: id ?? path, label, labelPath, path, data };
    }, [rowData.property]);

    const onKeyDown = React.useCallback<NonNullable<React.ComponentProps<typeof FilterableSelect>['onKeyDown']>>(
        e => {
            if (isPropertySelectionDisabled && e.key !== 'Tab') {
                e.preventDefault();
            }
        },
        [isPropertySelectionDisabled],
    );
    return (
        <FilterableSelect
            disabled={isDisabled}
            multiColumn={true}
            tableHeader={<PropertyTableHeader localize={localize} />}
            openOnFocus={true}
            data-testid={`e-widget-editor-content-property-${rowData._id}`}
            // @ts-expect-error "onChange" is actually triggered with { target: { value: Property } }
            onChange={({ target: { value } }: { target: { value: Property } }): void => {
                onCellChange({ columnId: 'property', rowId: rowData._id, value, rowData });
            }}
            onKeyDown={onKeyDown}
            placeholder={localize('@sage/xtrem-ui-components/select-property', 'Select property...')}
            size="small"
            value={value}
            {...rest}
        >
            {options
                .filter(
                    p =>
                        !allData
                            .filter(item => item.property && item._id !== rowData._id)
                            .map(item => item.path)
                            .includes(p.id) && p.data.type,
                )
                .map(p => {
                    return (
                        <OptionRow text={p.label} value={p} key={p.id ?? p.path}>
                            <td
                                width="50%"
                                style={{
                                    overflow: 'hidden',
                                    whiteSpace: 'pre-line',
                                    maxWidth: 0,
                                }}
                            >
                                {p.label}
                            </td>
                            <td
                                width="50%"
                                style={{
                                    overflow: 'hidden',
                                    whiteSpace: 'pre-line',
                                    maxWidth: 0,
                                    textAlign: 'end',
                                }}
                            >
                                {getPropertyParentNode({ nodeNames, labelPath: p.labelPath, node })}
                            </td>
                        </OptionRow>
                    );
                })}
        </FilterableSelect>
    );
}
