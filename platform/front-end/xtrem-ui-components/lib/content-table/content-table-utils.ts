import type { Dict, GraphQLTypes, GridNestedFieldTypes, KeysOfValue, LocalizeFunction } from '@sage/xtrem-shared';
import {
    FieldKey,
    arrayOrderContiguous,
    numericFields,
    objectKeys,
    presentationGraphqlMapping,
} from '@sage/xtrem-shared';
import { findIndex, get, groupBy, includes, isEqual, isNil, memoize, set, sortBy } from 'lodash';
import type { ColDef, FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import type { DefaultPropertyType } from '../types';
import type {
    ContentAction,
    ContentActionExtractor,
    ContentTableValidations,
    DefaultContentTableCols,
    GroupOrderValidationResult,
    WidgetContentState,
} from './content-table-types';

export const presentationTranslations = memoize(
    (localize: LocalizeFunction): Record<FieldKey, string> => ({
        Checkbox: localize('@sage/xtrem-ui-components/Checkbox', 'Checkbox'),
        Date: localize('@sage/xtrem-ui-components/Date', 'Date'),
        Label: localize('@sage/xtrem-ui-components/Label', 'Label'),
        Numeric: localize('@sage/xtrem-ui-components/Numeric', 'Numeric'),
        Progress: localize('@sage/xtrem-ui-components/Progress', 'Progress'),
        Text: localize('@sage/xtrem-ui-components/Text', 'Text'),
        Aggregate: localize('@sage/xtrem-ui-components/Aggregate', 'Aggregate'),
        Button: localize('@sage/xtrem-ui-components/Button', 'Button'),
        Calendar: localize('@sage/xtrem-ui-components/Calendar', 'Calendar'),
        Chart: localize('@sage/xtrem-ui-components/Chart', 'Chart'),
        Card: localize('@sage/xtrem-ui-components/Card', 'Card'),
        ContentTable: localize('@sage/xtrem-ui-components/ContentTable', 'Content table'),
        Count: localize('@sage/xtrem-ui-components/Count', 'Count'),
        Datetime: localize('@sage/xtrem-ui-components/Datetime', 'Datetime'),
        DatetimeRange: localize('@sage/xtrem-ui-components/DatetimeRange', 'Datetime range'),
        DetailList: localize('@sage/xtrem-ui-components/DetailList', 'Detail list'),
        DropdownList: localize('@sage/xtrem-ui-components/DropdownList', 'Dropdown list'),
        File: localize('@sage/xtrem-ui-components/File', 'File'),
        DynamicPod: localize('@sage/xtrem-ui-components/DynamicPod', 'Dynamic pod'),
        DynamicSelect: localize('@sage/xtrem-ui-components/DynamicSelect', 'Dynamic select'),
        FileDeposit: localize('@sage/xtrem-ui-components/FileDeposit', 'File deposit'),
        FilterEditor: localize('@sage/xtrem-ui-components/FilterEditor', 'Filter editor'),
        FilterSelect: localize('@sage/xtrem-ui-components/FilterSelect', 'Filter select'),
        FormDesigner: localize('@sage/xtrem-ui-components/FormDesigner', 'Form designer'),
        Icon: localize('@sage/xtrem-ui-components/Icon', 'Icon'),
        Image: localize('@sage/xtrem-ui-components/Image', 'Image'),
        Link: localize('@sage/xtrem-ui-components/Link', 'Link'),
        Message: localize('@sage/xtrem-ui-components/Message', 'Message'),
        MultiDropdown: localize('@sage/xtrem-ui-components/MultiDropdown', 'Multi dropdown'),
        MultiFileDeposit: localize('@sage/xtrem-ui-components/MultiFileDeposit', 'Multi file deposit'),
        MultiReference: localize('@sage/xtrem-ui-components/MultiReference', 'Multi reference'),
        NestedGrid: localize('@sage/xtrem-ui-components/NestedGrid', 'Nested grid'),
        NodeBrowserTree: localize('@sage/xtrem-ui-components/NodeBrowserTree', 'Node browser tree'),
        Plugin: localize('@sage/xtrem-ui-components/Plugin', 'Plugin'),
        Pod: localize('@sage/xtrem-ui-components/Pod', 'Pod'),
        Reference: localize('@sage/xtrem-ui-components/Reference', 'Reference'),
        RichText: localize('@sage/xtrem-ui-components/RichText', 'Rich text'),
        PodCollection: localize('@sage/xtrem-ui-components/PodCollection', 'Pod collection'),
        Preview: localize('@sage/xtrem-ui-components/Preview', 'Preview'),
        Radio: localize('@sage/xtrem-ui-components/Radio', 'Radio'),
        RelativeDate: localize('@sage/xtrem-ui-components/RelativeDate', 'Relative date'),
        Select: localize('@sage/xtrem-ui-components/Select', 'Select'),
        SelectionCard: localize('@sage/xtrem-ui-components/SelectionCard', 'Selection card'),
        Separator: localize('@sage/xtrem-ui-components/Separator', 'Separator'),
        StaticContent: localize('@sage/xtrem-ui-components/StaticContent', 'Static content'),
        Switch: localize('@sage/xtrem-ui-components/Switch', 'Switch'),
        Table: localize('@sage/xtrem-ui-components/Table', 'Table'),
        StepSequence: localize('@sage/xtrem-ui-components/StepSequence', 'Step sequence'),
        TableSummary: localize('@sage/xtrem-ui-components/TableSummary', 'Table summary'),
        Technical: localize('@sage/xtrem-ui-components/Technical', 'Technical'),
        TechnicalJson: localize('@sage/xtrem-ui-components/TechnicalJson', 'Technical JSON'),
        TextArea: localize('@sage/xtrem-ui-components/TextArea', 'Text area'),
        Time: localize('@sage/xtrem-ui-components/Time', 'Time'),
        ToggleButton: localize('@sage/xtrem-ui-components/ToggleButton', 'Toggle button'),
        Tree: localize('@sage/xtrem-ui-components/Tree', 'Tree'),
        VisualProcess: localize('@sage/xtrem-ui-components/VisualProcess', 'Visual process'),
        VitalPod: localize('@sage/xtrem-ui-components/VitalPod', 'Vital pod'),
        Workflow: localize('@sage/xtrem-ui-components/Workflow', 'Workflow'),
    }),
);

export const getMainPresentation = memoize(
    ({ localize, type }: { localize: LocalizeFunction; type: GraphQLTypes }) =>
        presentationTranslations(localize)[presentationGraphqlMapping[type][0]] as string,
);

export function tableWidgetReducer<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
>(): (
    localize: LocalizeFunction,
) => (state: WidgetContentState<Cols, E>, action: ContentAction<Cols, P, E>) => WidgetContentState<Cols, E> {
    return memoize((localize: LocalizeFunction) => {
        return (state: WidgetContentState<Cols, E>, action: ContentAction<Cols, P, E>): WidgetContentState<Cols, E> => {
            switch (action.type) {
                case 'ORDER_CHANGED':
                    return handleOrderChange<Cols, P, E>(state, action);
                case 'DATA_RESET':
                    return handleDataReset<Cols, P, E>(state, action);
                case 'ROW_ADDED':
                    return handleRowAdded<Cols, E>(state, action.data);
                case 'ROW_REMOVED':
                    return handleRowRemoved<Cols, E>(state, action.row._id);
                case 'ROW_DRAGGED':
                    return handleRowDragged<Cols, E>(state, action.ids);
                case 'CELL_CHANGED':
                    return handleCellChanged<Cols, P, E>(state, action, action.selectedProperties, localize);
                case 'COLUMNS_CHANGED':
                    return handleColumnsChanged<Cols, P, E>(state, action);
                default:
                    return state;
            }
        };
    });
}

export function enforceContiguousOrder<R extends { _id: string }, K extends KeysOfValue<R, number>>({
    orderedData,
    key,
}: {
    orderedData: R[];
    key: K;
}): R[] {
    const changes = arrayOrderContiguous({
        orderedData,
        key,
    });
    return changes.reduce((acc, { rowId, columnId, value }) => {
        const element = acc.find(d => d._id === rowId);
        if (element !== undefined) {
            element[columnId] = value;
        }
        return acc;
    }, orderedData);
}
export function areAllPropertiesNonSortable<Cols extends ColDef[] = [], E = unknown>(
    data: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'],
): boolean {
    return data.every(row => {
        const { canSort } = row.property?.data ?? {};
        return typeof canSort === 'boolean' && !canSort;
    });
}

export function checkValidGroupOrder<Cols extends ColDef[] = [], E = unknown>({
    data,
    enforceContiguous,
}: {
    data: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'];
    enforceContiguous?: any;
}): GroupOrderValidationResult {
    const groups = groupBy(data, enforceContiguous);
    const invalidFields: string[] = [];
    let isValid = true;

    Object.keys(groups).forEach(key => {
        const group = groups[key];
        const element = group[0] as any;
        const name = element?.property?.data?.label;
        const canSort = element?.property?.data?.canSort;
        const isValidGroup = typeof canSort === 'undefined' || canSort;
        if (!isValidGroup) {
            isValid = false;
            if (name) {
                invalidFields.push(name);
            }
        }
    });

    return { isValid, invalidFields };
}

function handleOrderChange<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
>(
    state: WidgetContentState<Cols, E>,
    { orderedIds, id, enforceContiguous }: ContentActionExtractor<Cols, P, E, 'ORDER_CHANGED'>,
): WidgetContentState<Cols, E> {
    const orderedData = sortBy(state.data, d => findIndex(orderedIds, element => (d as any)[id] === element));
    const data =
        enforceContiguous !== undefined
            ? enforceContiguousOrder({
                  orderedData,
                  key: enforceContiguous as KeysOfValue<(typeof orderedData)[number], number>,
              })
            : orderedData;

    return {
        ...state,
        data,
    };
}

function handleDataReset<Cols extends ColDef[] = [], P extends DefaultPropertyType = DefaultPropertyType, E = unknown>(
    state: WidgetContentState<Cols, E>,
    { data }: ContentActionExtractor<Cols, P, E, 'DATA_RESET'>,
): WidgetContentState<Cols, E> {
    const dataLength = data.length;
    return {
        ...state,
        counter: dataLength,
        data: [
            ...data,
            ...state.data
                .filter(row => !isContentRowValid({ row, validations: state.validations }))
                .map((row, index) => ({ ...row, _id: String(index + dataLength + 1) })),
        ],
    };
}

function handleRowAdded<Cols extends ColDef[] = [], E = unknown>(
    state: WidgetContentState<Cols, E>,
    data?: any,
): WidgetContentState<Cols, E> {
    const maxId = state.data.reduce((max, row) => {
        const rowId = Number(row._id);
        return rowId > max ? rowId : max;
    }, 0);

    const newRowId = String(maxId + 1);

    const newRow = {
        _id: newRowId,
        presentation: undefined,
        title: undefined,
        formatting: undefined,
        divisor: undefined,
        path: '',
        labelPath: undefined,
        property: undefined,
    };

    const newData = [...state.data, { ...newRow, ...data }];

    return {
        ...state,
        counter: newData.length,
        data: newData,
    };
}

function handleColumnsChanged<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
>(
    state: WidgetContentState<Cols, E>,
    { columns }: ContentActionExtractor<Cols, P, E, 'COLUMNS_CHANGED'>,
): WidgetContentState<Cols, E> {
    return {
        ...state,
        columns,
    };
}

function handleRowRemoved<Cols extends ColDef[] = [], E = unknown>(
    state: WidgetContentState<Cols, E>,
    rowId: string,
): WidgetContentState<Cols, E> {
    const newData = state.data.filter(element => element._id !== rowId);
    return {
        ...state,
        counter: newData.length,
        data: newData,
    };
}

function handleRowDragged<Cols extends ColDef[] = [], E = unknown>(
    state: WidgetContentState<Cols, E>,
    ids: string[],
): WidgetContentState<Cols, E> {
    const orderedRows = sortBy(state.data, item => {
        return ids.indexOf(item._id);
    });
    return {
        ...state,
        data: orderedRows,
    };
}

function getActualValue<Cols extends ColDef[] = []>({
    columnId,
    value,
}: {
    columnId: UnrestrictedTableCols<DefaultContentTableCols, Cols>[number]['id'];
    value: any;
}): any {
    if (columnId === 'property') {
        return value || undefined;
    }
    return isNil(value) ? undefined : value;
}

export function isContentRowValid({
    row,
    validations,
}: {
    row: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols>>['data'][number];
    validations: ContentTableValidations;
}): boolean {
    return (
        row.property != null &&
        row.presentation != null &&
        row.property?.data?.type != null &&
        row.path != null &&
        row.labelPath != null &&
        row.title != null &&
        Object.values(validations[row._id] ?? {}).every(v => v == null)
    );
}

function handleCellChanged<
    Cols extends ColDef[] = [],
    P extends DefaultPropertyType = DefaultPropertyType,
    E = unknown,
>(
    state: WidgetContentState<Cols, E>,
    action: ContentActionExtractor<Cols, P, E, 'CELL_CHANGED'>,
    selectedProperties: Dict<DefaultPropertyType>,
    localize: LocalizeFunction,
): WidgetContentState<Cols, E> {
    const { rowId, columnId, value, rowData } = action.changes;

    const validationMessage = validateCellChange<Cols, E>(rowData, columnId, value, localize);

    const data = state.data.map(row => {
        if (row._id === rowId && !isEqual(get(row, columnId), value)) {
            // set actual value
            set(row, columnId, getActualValue({ columnId, value }));
            // set type and path for given property
            if (row.property) {
                const idOrPath = row.property.id ?? row.property.path;
                const prop = selectedProperties?.[idOrPath];
                row.path = idOrPath;
                row.labelPath = prop?.labelPath;
            }

            if (row.property && row.property.data && columnId === 'title') {
                row.property.data.label = value ?? '';
            }

            const columnType = row.property?.data.type as GraphQLTypes | undefined;

            // set presentation type in case it cannot be chosen
            if (columnType && presentationGraphqlMapping[columnType]?.length === 1) {
                row.presentation = presentationGraphqlMapping[columnType][0] as GridNestedFieldTypes;
            }

            if (columnId === 'property') {
                // reset presentation & formatting if property is not compatible
                row.presentation = undefined;
                row.formatting = undefined;
                // always set default title upon property change
                row.title = row.property?.label;
                // always reset divisor upon property change
                row.divisor =
                    row.property?.data?.type && includes(numericFields, row.property.data.type) ? '1' : undefined;
            }

            if (columnId === 'presentation') {
                row.formatting = undefined;
            }

            if (!row.presentation && columnType) {
                row.presentation = presentationGraphqlMapping[columnType][0] as GridNestedFieldTypes;
            }

            if (
                columnId !== 'formatting' &&
                !row.formatting &&
                row.property?.data?.type &&
                includes(numericFields, row.property.data.type) &&
                row.presentation === FieldKey.Numeric
            ) {
                row.formatting = '0';
            }
        }

        return row;
    });

    const changedRow = data.find(d => d._id === rowData._id);
    const validationResets = changedRow
        ? objectKeys(changedRow).reduce((acc, key) => {
              if (changedRow?.[key] === undefined) {
                  set(acc, key, undefined);
              }
              return acc;
          }, {})
        : {};
    const validations = {
        ...state.validations,
        [rowData._id]: {
            ...state.validations?.[rowData._id],
            [columnId]: validationMessage,
            ...validationResets,
        },
    };

    return {
        ...state,
        data,
        validations,
    };
}

function validateCellChange<Cols extends ColDef[] = [], E = unknown>(
    rowData: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'][number],
    columnId: keyof FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, Cols>, E>['data'][number],
    value: any,
    localize: LocalizeFunction,
): string | undefined {
    if (
        rowData.property?.data?.type &&
        (columnId === 'divisor' || columnId === 'formatting') &&
        includes(numericFields, rowData.property.data.type)
    ) {
        const parsedNumber = Number.parseInt(String(value), 10);
        if (Number.isNaN(parsedNumber)) {
            return localize('@sage/xtrem-ui-components/invalid-number', 'Invalid number');
        }
        if (columnId === 'formatting' && (parsedNumber < 0 || parsedNumber > 4)) {
            return localize(
                '@sage/xtrem-ui-components/must-be-between-zero-and-four',
                'This value needs to be between 0 and 4.',
            );
        }
        if (columnId === 'divisor' && parsedNumber <= 0) {
            return localize(
                '@sage/xtrem-ui-components/must-be-greater-than-zero',
                'This value needs to be greater than 0.',
            );
        }
    }
    return undefined;
}
