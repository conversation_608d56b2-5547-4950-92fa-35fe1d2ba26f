import type { LocalizeFunction } from '@sage/xtrem-shared';
import { arrayMovePreservingOrder } from '@sage/xtrem-shared';
import { FilterableSelect, Option } from 'carbon-react/esm/components/select';
import { isNil, max, range } from 'lodash';
import React from 'react';
import type { FlatTableProps, UnrestrictedTableCols } from '../flat-table/flat-table-types';
import { useDeepCompareMemo } from '../hooks';
import type {
    AdditionalContentColumns,
    DefaultContentTableCols,
    GroupOrderValidationResult,
    UnrestrictedOnCellChange,
    UseContentTableHook,
} from './content-table-types';
import { areAllPropertiesNonSortable } from './content-table-utils';
import type { TableContentWithGroupsProps } from './content-table-with-groups';

export interface ContentTableGroupColumnProp {
    localize: LocalizeFunction;
    onCellChange: UnrestrictedOnCellChange<AdditionalContentColumns, any>;
    data: FlatTableProps<UnrestrictedTableCols<DefaultContentTableCols, AdditionalContentColumns, false>>['data'];
    rowData: FlatTableProps<
        UnrestrictedTableCols<DefaultContentTableCols, AdditionalContentColumns, false>
    >['data'][number];
    rowIndex: number;
    setOrderedIds: UseContentTableHook<AdditionalContentColumns>['setOrderedIds'];
    setGroupOrderValidation: React.Dispatch<React.SetStateAction<GroupOrderValidationResult>>;
    setGroupOrderError: TableContentWithGroupsProps['setGroupOrderError'];
    displayGroupErrorMessage: () => void;
}

export function ContentTableGroupColumn({
    data,
    rowIndex,
    rowData,
    onCellChange,
    localize,
    setOrderedIds,
    setGroupOrderValidation,
    setGroupOrderError,
    displayGroupErrorMessage,
}: ContentTableGroupColumnProp): React.ReactNode {
    const onGroupChange = useDeepCompareMemo<NonNullable<React.ComponentProps<typeof FilterableSelect>['onChange']>>(
        () =>
            ({ target: { value: groupValue } }): void => {
                const newValue = Number(groupValue);
                const { success, data: orderedData } = arrayMovePreservingOrder({
                    data,
                    fromIndex: rowIndex,
                    toValue: newValue,
                    orderByKey: 'group',
                });

                if (success) {
                    onCellChange({
                        columnId: 'group',
                        rowId: rowData._id,
                        value: newValue,
                        rowData: { ...rowData, group: newValue },
                    });
                    setOrderedIds({
                        newOrderedIds: orderedData.map(d => d.path),
                        id: 'path',
                        enforceContiguous: 'group',
                    });
                    setGroupOrderValidation({ isValid: true });
                    if (setGroupOrderError) {
                        setGroupOrderError(false);
                    }
                } else {
                    displayGroupErrorMessage();
                }
            },
        [data, rowIndex, rowData],
    );

    const options = useDeepCompareMemo(() => range(0, (max(data.map(d => d.group)) ?? 0) + 2), [data]);

    const value = React.useMemo(() => (isNil(rowData.group) ? '0' : String(rowData.group)), [rowData.group]);

    if (areAllPropertiesNonSortable(data)) {
        return null;
    }

    return (
        <FilterableSelect
            openOnFocus={true}
            data-testid={`e-widget-editor-content-group-${rowData._id}`}
            onChange={onGroupChange}
            placeholder={localize('@sage/xtrem-ui-components/select-value-placeholder', 'Select value...')}
            size="small"
            value={value}
        >
            {options.map(option => {
                const text = localize('@sage/xtrem-ui-components/group-number', 'Group {{groupNumber}}', {
                    groupNumber: option + 1,
                });
                return (
                    <Option key={option} text={text} value={String(option)}>
                        {text}
                    </Option>
                );
            })}
        </FilterableSelect>
    );
}
