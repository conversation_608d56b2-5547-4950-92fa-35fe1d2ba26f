import Textbox from 'carbon-react/esm/components/textbox';
import { Tree } from '../tree/tree';
import React from 'react';
import type { Dict, LocalizeFunction, NodeDetails, TreeElement } from '@sage/xtrem-shared';
import { textStreams, GraphQLTypes } from '@sage/xtrem-shared';
import Popover from 'carbon-react/esm/__internal__/popover/popover.component';
import uid from 'uid';

const ROOT_TOKEN = '$root';

export interface TreeInputProps {
    fetchItems: (element: NodeDetails) => Promise<NodeDetails[]>;
    localize: LocalizeFunction;
    node?: string;
    isDisabled?: boolean;
    onChange: (value: TreeElement<NodeDetails> | null) => void;
    value?: TreeElement<NodeDetails> | null;
    canSelectObjects?: boolean;
    'data-testid'?: string;
}
export function TreeInput({
    fetchItems,
    localize,
    node,
    onChange,
    isDisabled,
    canSelectObjects,
    value,
    'data-testid': dataTestId = 'e-tree-input',
}: TreeInputProps): React.ReactElement {
    const [isOpened, setIsOpen] = React.useState(false);
    const [inputValue, setInputValue] = React.useState(value?.labelPath || '');
    const [selectedItems, setSelectedItems] = React.useState<Dict<TreeElement<NodeDetails>>>(
        value ? { [value.id]: value } : {},
    );
    const elementRef = React.useRef<HTMLDivElement>(null);
    const listContainerRef = React.useRef<HTMLDivElement>(null);

    // Element id cannot start with a number
    const id = React.useRef(`x${uid()}`);

    const onClick = React.useCallback(
        (e: MouseEvent) => {
            let element: HTMLElement | null = e.target as HTMLElement;
            if (e.target === window.document.body) {
                return;
            }
            while (!element || element?.tagName !== 'HTML') {
                if (element === elementRef.current || element === listContainerRef.current) {
                    return;
                }
                element = element?.parentElement || null;
            }
            setIsOpen(false);
            window.removeEventListener('click', onClick);
        },
        [elementRef],
    );

    const closeDropDown = React.useCallback(() => {
        setIsOpen(false);
        window.removeEventListener('click', onClick);
    }, [onClick]);

    const openDropDown = React.useCallback(() => {
        if (!isDisabled) {
            setIsOpen(true);
            window.addEventListener('click', onClick);
        }
    }, [isDisabled, onClick]);

    const onFocus = React.useCallback(() => {
        const inputElement = elementRef.current?.querySelector('input');
        if (!isDisabled && inputElement && inputElement.value.length > 0) {
            inputElement.setSelectionRange(0, inputElement.value.length);
        }
    }, [isDisabled]);

    const onCheckedItemsUpdated = React.useCallback(
        (e: Dict<TreeElement>) => {
            const keys = Object.keys(e);
            if (keys.length > 0) {
                const key = keys[0];
                const item = e[key];
                setInputValue(item.labelPath);
                closeDropDown();
                onChange(item);
            } else {
                onChange(null);
            }
            setSelectedItems(e as any);
            elementRef.current?.querySelector('input')?.focus();
            setTimeout(() => {
                setIsOpen(false);
            }, 0);
        },
        [closeDropDown, onChange, elementRef],
    );

    const onKeyDown = React.useCallback(
        (ev: React.KeyboardEvent) => {
            if (ev.key === 'ArrowDown') {
                openDropDown();
                setTimeout(() => {
                    listContainerRef.current?.querySelector('button')?.focus();
                }, 0);
            }

            if (ev.key === 'Escape') {
                closeDropDown();
                ev.preventDefault();
                ev.stopPropagation();
            }

            if (ev.key === 'Backspace' || ev.key === 'Delete') {
                setSelectedItems({});
                setInputValue('');
            }

            if (ev.key === 'Tab') {
                setTimeout(closeDropDown, 10);
            }

            if (ev.key.startsWith('Key') || ev.key.startsWith('Digit') || ev.key.startsWith('Numpad')) {
                // Prevent the user from typing in the input field
                ev.preventDefault();
                ev.stopPropagation();
            }
        },
        [closeDropDown, openDropDown],
    );

    const onTreeSelfClose = React.useCallback(() => {
        elementRef.current?.querySelector('input')?.focus();

        setTimeout(() => {
            setIsOpen(false);
        }, 0);
    }, [elementRef]);

    const internalFetchItems = React.useCallback(
        async (parent: TreeElement<NodeDetails>): Promise<TreeElement<NodeDetails>[]> => {
            const fetchedItems = await fetchItems(parent.data);
            return fetchedItems
                .filter(
                    data =>
                        !textStreams.includes(data.type as GraphQLTypes) &&
                        data.kind !== 'LIST' &&
                        data.type !== GraphQLTypes._InputStream,
                )
                .map((data: NodeDetails): TreeElement<NodeDetails> => {
                    return {
                        data: { ...data, node: data.type },
                        id: `${parent.id}.${data.name}`.replace(/^\$root\./, ''),
                        key: `${parent.key}.${data.name}`.replace(/^\$root\./, ''),
                        label: data.label || data.name,
                        labelKey: data.label || data.name,
                        labelPath: `${parent.labelPath} > ${data.label}`.replace(/^\s>\s/, ''),
                        canBeExpanded: data.kind === 'OBJECT',
                        canBeSelected: canSelectObjects
                            ? data.kind === 'OBJECT' || data.kind === 'SCALAR'
                            : data.kind === 'SCALAR',
                    };
                });
        },
        [canSelectObjects, fetchItems],
    );

    return (
        <div className="e-tree-input" ref={elementRef}>
            <Textbox
                onFocus={onFocus}
                onKeyDown={onKeyDown}
                inputIcon="caret_down"
                value={inputValue}
                disabled={isDisabled}
                size="small"
                data-testid={dataTestId}
                aria-expanded={isOpened}
                aria-controls={id.current}
                onClick={openDropDown}
            />
            <Popover
                placement="bottom"
                disablePortal={false}
                reference={elementRef}
                isOpen={isOpened}
                disableBackgroundUI
                animationFrame
            >
                <div
                    ref={listContainerRef}
                    id={id.current}
                    className="e-tree-input-dropdown"
                    data-testid="e-tree-input-dropdown"
                    style={{
                        top: (elementRef.current?.offsetHeight || 0) + (elementRef.current?.offsetTop || 0),
                        left: elementRef.current?.offsetLeft,
                        width: elementRef.current?.offsetWidth,
                        zIndex: 2,
                    }}
                >
                    <Tree
                        checkedItems={selectedItems}
                        fetchItems={internalFetchItems}
                        isFolderIconHidden={true}
                        level={0}
                        localize={localize}
                        onCheckedItemsUpdated={onCheckedItemsUpdated}
                        onClose={onTreeSelfClose}
                        selectionMode="button"
                        element={{
                            id: ROOT_TOKEN,
                            key: ROOT_TOKEN,
                            label: 'Root',
                            labelKey: 'Root',
                            labelPath: '',
                            canBeExpanded: true,
                            canBeSelected: false,
                            data: { name: 'Root', node, canSort: false, canFilter: false } as any,
                        }}
                    />
                </div>
            </Popover>
        </div>
    );
}
