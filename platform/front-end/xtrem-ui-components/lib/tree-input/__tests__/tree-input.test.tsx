import React from 'react';
import type { TreeInputProps } from '../tree-input';
import { TreeInput } from '../tree-input';
import { render, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { fakeFetchItems, getTreeDropdown } from '../../__tests__/common';
import '@testing-library/jest-dom';

describe('tree input', () => {
    let props: TreeInputProps;
    beforeEach(() => {
        props = {
            fetchItems: jest.fn(fakeFetchItems),
            localize: jest.fn().mockImplementation((_, v) => v),
            onChange: jest.fn(),
            canSelectObjects: false,
            isDisabled: false,
            node: 'SalesInvoice',
        };
    });

    describe('render', () => {
        it('should render simple tree input', () => {
            const { queryByTestId } = render(<TreeInput {...props} />);
            expect(queryByTestId('e-tree-input')!).toBeInTheDocument();
        });
    });

    describe('interactions', () => {
        it('should open dropdown on input click', async () => {
            const { queryByTestId, baseElement } = render(<TreeInput {...props} />);
            const input = queryByTestId('e-tree-input')!;
            expect(input).toHaveAttribute('aria-expanded', 'false');
            await userEvent.click(input);
            await waitFor(() => {
                expect(input).toHaveAttribute('aria-expanded', 'true');
            });
            const dropdown = getTreeDropdown(input, baseElement)!;
            expect(dropdown).toBeInTheDocument();
        });

        it('should trigger on change on deep level selection', async () => {
            const { queryByTestId, baseElement } = render(<TreeInput {...props} />);
            const input = queryByTestId('e-tree-input')!;
            expect(input).toHaveAttribute('aria-expanded', 'false');
            await userEvent.click(input);
            await waitFor(() => {
                expect(input).toHaveAttribute('aria-expanded', 'true');
            });
            const dropdown = getTreeDropdown(input, baseElement)!;
            await waitFor(() => {
                expect(dropdown.querySelectorAll('li')).not.toHaveLength(0);
            });

            const grossTotal = queryByTestId('e-tree-view-container-label-grossTotal')?.querySelector('button')!;
            expect(props.onChange).not.toHaveBeenCalled();
            await userEvent.click(grossTotal);
            await waitFor(() => {
                expect(props.onChange).toHaveBeenCalledWith({
                    data: {
                        canFilter: true,
                        canSort: true,
                        dataType: '',
                        dataTypeDetails: null,
                        enumType: null,
                        isCustom: false,
                        isMutable: false,
                        isStored: true,
                        isOnInputType: true,
                        isOnOutputType: true,
                        kind: 'SCALAR',
                        label: 'Gross Total',
                        name: 'totalWithTax',
                        node: 'Float',
                        targetNode: '',
                        targetNodeDetails: null,
                        type: 'Float',
                    },
                    id: 'totalWithTax',
                    key: 'totalWithTax',
                    label: 'Gross Total',
                    labelKey: 'Gross Total',
                    labelPath: 'Gross Total',
                });
            });
            expect(input).toHaveValue('Gross Total');
        });
    });
});
