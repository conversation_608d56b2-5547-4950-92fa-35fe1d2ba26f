import React from 'react';
import type { FlatTableProps as CarbonFlatTableProps } from 'carbon-react/esm/components/flat-table';
import {
    FlatTable as CarbonFlatTable,
    FlatTableRow,
    FlatTableHead,
    FlatTableHeader,
    FlatTableBodyDraggable,
    FlatTableCell,
    FlatTableBody,
    FlatTableRowHeader,
} from 'carbon-react/esm/components/flat-table';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import Box from 'carbon-react/esm/components/box';
import Typography from 'carbon-react/esm/components/typography';
import { get, noop } from 'lodash';
import type { ColDef, FlatTableProps } from './flat-table-types';

// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
export function FlatTable<T extends ColDef[] = [], E = unknown>({
    actionsText = 'Actions',
    addButtonText = 'Add item',
    canAddNewLines = false,
    canDrag = false,
    canRemoveLines = false,
    columns,
    data,
    emptyStateText = 'No data to display',
    extraData,
    getRowProps,
    isAddButtonHidden = false,
    maxLines,
    onRowAdded = noop,
    onRowDrag = noop,
    onRowRemoved = noop,
    ...flatTableProps
}: FlatTableProps<T, E>) {
    const colDefs = React.useMemo(() => {
        return columns.filter(column => !column.isHidden);
    }, [columns]);

    const renderRows = React.useCallback(() => {
        return data.length === 0 ? (
            <FlatTableRow>
                <FlatTableCell colspan={colDefs.length}>
                    <Box
                        paddingY={80}
                        gap={1}
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                        justifyContent="center"
                    >
                        <div className="e-flat-table-placeholder">
                            <Typography color="--colorsUtilityMajor100" lineHeight="30px" variant="h3">
                                {emptyStateText}
                            </Typography>
                        </div>

                        {!isAddButtonHidden && (
                            <ButtonMinor mt={2} iconType="add" onClick={onRowAdded} data-testid="add-item-button">
                                {addButtonText}
                            </ButtonMinor>
                        )}
                    </Box>
                </FlatTableCell>
            </FlatTableRow>
        ) : (
            data.map((row, rowIndex, allData) => {
                const rowWithId = row as { _id: string };
                const cols = colDefs.map(({ header, id, cellRenderer: CellRenderer, isHidden: _, ...cellProps }) => {
                    const renderedItem: any = CellRenderer ? (
                        <CellRenderer
                            rowData={row}
                            rowIndex={rowIndex}
                            data={allData}
                            extraData={extraData as NonNullable<E>}
                        />
                    ) : (
                        get(row, id, '')
                    );
                    const CellComponent = header.isSticky ? FlatTableRowHeader : FlatTableCell;
                    return (
                        <CellComponent
                            key={`${rowWithId._id}-${id}`}
                            width={header.width}
                            truncate
                            py="var(--spacing075)"
                            {...cellProps}
                        >
                            {renderedItem}
                        </CellComponent>
                    );
                });
                const rowProps = getRowProps?.({ row, data: allData, rowIndex });
                return (
                    <FlatTableRow key={rowWithId._id} id={rowWithId._id} data-testid={rowWithId._id} {...rowProps}>
                        {(canAddNewLines || canRemoveLines) && data.length > 0 ? (
                            <>
                                {cols}
                                <FlatTableCell key="__actions">
                                    <div className="e-flat-table-actions-container">
                                        <ButtonMinor
                                            // @ts-expect-error tabIndex is not a valid prop for ButtonMinor
                                            tabIndex={0}
                                            buttonType="secondary"
                                            iconType="remove"
                                            aria-label="Remove"
                                            size="small"
                                            marginRight={2}
                                            onClick={(): void => onRowRemoved(row)}
                                            data-testid="flat-table-remove-button"
                                        />
                                        {canAddNewLines &&
                                            rowIndex === data.length - 1 &&
                                            (!maxLines || data.length < maxLines) && (
                                                <ButtonMinor
                                                    // @ts-expect-error tabIndex is not a valid prop for ButtonMinor
                                                    tabIndex={0}
                                                    buttonType="secondary"
                                                    iconType="add"
                                                    aria-label="Add"
                                                    size="small"
                                                    onClick={onRowAdded}
                                                    data-testid="flat-table-add-button"
                                                />
                                            )}
                                    </div>
                                </FlatTableCell>
                            </>
                        ) : (
                            cols
                        )}
                    </FlatTableRow>
                );
            })
        );
    }, [
        addButtonText,
        canAddNewLines,
        canRemoveLines,
        colDefs,
        data,
        emptyStateText,
        extraData,
        getRowProps,
        isAddButtonHidden,
        maxLines,
        onRowAdded,
        onRowRemoved,
    ]);

    const hasDrag = React.useMemo(() => canDrag && data.length > 0, [canDrag, data.length]);
    const hasData = data.length !== 0;

    const tableProps: Omit<CarbonFlatTableProps, 'children'> & { className?: string } = { ...flatTableProps };
    tableProps.className = hasData ? 'e-flat-table-has-data' : 'e-flat-table-empty';
    return (
        <CarbonFlatTable overflowX="auto" width="100%" {...tableProps}>
            <FlatTableHead>
                <FlatTableRow>
                    {hasDrag && <FlatTableHeader width={40} />}
                    {colDefs.map(({ header: { name: headername, isSticky, ...headerProps }, id: columnId }) => {
                        const HeaderComponent = isSticky ? FlatTableRowHeader : FlatTableHeader;
                        return (
                            <HeaderComponent key={columnId} {...headerProps} truncate>
                                <div className="e-flat-table-content">
                                    <div className="e-flat-table-content-text">{headername}</div>
                                </div>
                            </HeaderComponent>
                        );
                    })}
                    {(canAddNewLines || canRemoveLines) && data.length > 0 && (
                        <FlatTableHeader key="__actions" width={120}>
                            {actionsText}
                        </FlatTableHeader>
                    )}
                </FlatTableRow>
            </FlatTableHead>
            {hasDrag ? (
                // @ts-expect-error IDs can also be strings
                <FlatTableBodyDraggable getOrder={onRowDrag}>{renderRows()}</FlatTableBodyDraggable>
            ) : (
                <FlatTableBody>{renderRows()}</FlatTableBody>
            )}
        </CarbonFlatTable>
    );
}

FlatTable.displayName = 'FlatTable';
