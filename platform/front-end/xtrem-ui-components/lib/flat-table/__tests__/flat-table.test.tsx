import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { FlatTable } from '../flat-table';

describe('FlatTable Component', () => {
    const columns: any = [
        {
            id: 'name',
            header: { name: 'Name' },
        },
        {
            id: 'age',
            header: { name: 'Age' },
        },
    ];

    const data = [
        { _id: '1', name: '<PERSON>', age: 30 },
        { _id: '2', name: '<PERSON>', age: 25 },
    ];

    it('should render the table with data', () => {
        render(<FlatTable columns={columns} data={data} />);

        expect(screen.getByText('Name')).toBeInTheDocument();
        expect(screen.getByText('Age')).toBeInTheDocument();
        expect(screen.getByText('<PERSON>')).toBeInTheDocument();
        expect(screen.getByText('<PERSON>')).toBeInTheDocument();
        expect(screen.getByText('30')).toBeInTheDocument();
        expect(screen.getByText('25')).toBeInTheDocument();
    });

    it('should display empty state text when no data is provided', () => {
        render(<FlatTable columns={columns} data={[]} emptyStateText="No data available" />);

        expect(screen.getByText('No data available')).toBeInTheDocument();
    });

    it('should render the add button & empty table default text', async () => {
        const { findByTestId, findByText } = render(<FlatTable columns={columns} data={[]} />);
        await findByTestId('add-item-button');
        await findByText('No data to display');
    });

    it('should call onRowAdded when Add item button is clicked', () => {
        const onRowAdded = jest.fn();
        render(<FlatTable columns={columns} data={[]} onRowAdded={onRowAdded} />);

        fireEvent.click(screen.getByTestId('add-item-button'));

        expect(onRowAdded).toHaveBeenCalled();
    });

    it('should call onRowRemoved when Remove button is clicked', () => {
        const onRowRemoved = jest.fn();
        render(<FlatTable columns={columns} data={data} canRemoveLines onRowRemoved={onRowRemoved} />);

        fireEvent.click(screen.getAllByTestId('flat-table-remove-button')[0]);

        expect(onRowRemoved).toHaveBeenCalledWith(data[0]);
    });

    it('should hide the Add item button when isAddButtonHidden is true', () => {
        render(<FlatTable columns={columns} data={[]} isAddButtonHidden />);

        expect(screen.queryByTestId('add-item-button')).toBeNull();
    });

    it('should render the Actions column when canAddNewLines or canRemoveLines is true', () => {
        render(<FlatTable columns={columns} data={data} canAddNewLines />);

        expect(screen.getByText('Actions')).toBeInTheDocument();
    });
});
