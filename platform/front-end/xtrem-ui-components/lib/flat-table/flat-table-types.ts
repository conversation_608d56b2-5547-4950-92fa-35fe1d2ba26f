import type {
    FlatTableProps as CarbonFlatTableProps,
    FlatTableCellProps,
    FlatTableHeader,
    FlatTableRowProps,
} from 'carbon-react/esm/components/flat-table';

export type ColDef = {
    id: string;
    type?: any;
};

export type FlatTableColumnDef<Cols extends ColDef[] = [], E = unknown> = FlatTableCellProps & {
    id: Cols[number]['id'];
    isHidden?: boolean;
    header: {
        name: string;
        isSticky?: boolean;
    } & React.ComponentProps<typeof FlatTableHeader>;
    cellRenderer?: React.FC<{
        rowData: FlatTableProps<Cols, E>['data'][number];
        rowIndex: number;
        data: FlatTableProps<Cols, E>['data'];
        extraData: E;
        filterTypeMode?: 'switch' | 'dropdown';
    }>;
};

type ColsWithId<Cols extends ColDef[]> = [...Cols, { id: '_id'; type: string }];

export type FlatTableProps<Cols extends ColDef[] = [], E = unknown> = Omit<CarbonFlatTableProps, 'children'> & {
    addButtonText?: string;
    actionsText?: string;
    canAddNewLines?: boolean;
    canDrag?: boolean;
    canRemoveLines?: boolean;
    maxLines?: number;
    columns: FlatTableColumnDef<Cols, E>[];
    data: {
        [ColId in ColsWithId<Cols>[number]['id']]: Extract<ColsWithId<Cols>[number], { id: ColId }>['type'];
    }[];
    emptyStateText?: string;
    extraData?: E;
    getRowProps?: (args: {
        rowIndex: number;
        row: FlatTableProps<Cols, E>['data'][number];
        data: FlatTableProps<Cols, E>['data'];
    }) => Omit<FlatTableRowProps, 'children'>;
    isAddButtonHidden?: boolean;
    onRowAdded?: (data?: any) => void;
    onRowDrag?: (orderedIds: string[]) => void;
    onRowRemoved?: (row: FlatTableProps<Cols, E>['data'][number]) => void;
};

export type UnrestrictedTableCols<
    DefaultCols extends ColDef[] = [],
    Cols extends ColDef[] = [],
    Strict extends boolean = true,
> = Strict extends true
    ? (DefaultCols[number] | (Cols[number] & { id?: never }))[]
    : (DefaultCols[number] | Cols[number])[];
