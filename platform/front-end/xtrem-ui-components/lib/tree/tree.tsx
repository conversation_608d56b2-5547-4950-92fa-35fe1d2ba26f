import { Checkbox } from 'carbon-react/esm/components/checkbox';
import type { IconType } from 'carbon-react/esm/components/icon';
import Icon from 'carbon-react/esm/components/icon';
import IconButton from 'carbon-react/esm/components/icon-button';
import * as React from 'react';
import * as tokens from '@sage/design-tokens/js/base/common';
import { camelCase, filter, flatMap, includes } from 'lodash';
import type { TreeProps } from './tree-types';
import Link from 'carbon-react/esm/components/link';
import Loader from 'carbon-react/esm/components/loader';
import type { Dict, NodeDetails, TreeElement } from '@sage/xtrem-shared';

const computeUnderlinedContent = (label: string, searchText: string): React.ReactChild[] => {
    if (!searchText) {
        return [label];
    }

    const regex = new RegExp(`(${searchText})`, 'gi');
    const parts = label.split(regex);
    const underlinedLabel: React.ReactChild[] = [];

    for (let i = 0; i < parts.length; i += 1) {
        const part = parts[i];
        if (part.toLowerCase() === searchText.toLowerCase()) {
            underlinedLabel.push(
                <span key={i} className="e-tree-view-underlined">
                    {part}
                </span>,
            );
        } else {
            underlinedLabel.push(part);
        }
    }

    return underlinedLabel;
};

export const Tree = React.memo(
    <T extends NodeDetails & { iconType?: IconType } = NodeDetails>({
        checkedItems,
        element,
        fetchItems,
        isDisabled,
        isReadOnly,
        level,
        listOpenedItems,
        localize,
        onCheckedItemsUpdated,
        onCloseChild,
        onOpenChild,
        selectionMode = 'multiple',
        searchText = '',
        isFolderIconHidden = false,
        onClose,
    }: TreeProps<T>) => {
        const bodyRef = React.useRef<HTMLUListElement>(null);
        const [listItems, setListItems] = React.useState<TreeElement[] | null>(null);
        const [isOpen, setOpen] = React.useState<boolean>(() => {
            if (level === 0) {
                return true;
            }
            if (listOpenedItems && element.key !== 'key' && element.key in listOpenedItems) {
                return true;
            }
            return false;
        });

        const onTopTreeKeyDown = React.useCallback(
            (ev: React.KeyboardEvent) => {
                if (ev.key === 'Tab' && onClose) {
                    onClose();
                    return;
                }
                const bodyElement = bodyRef.current;
                if (!bodyElement) {
                    return;
                }
                const activeElement = document.activeElement as HTMLButtonElement | null;
                if (!activeElement) {
                    return;
                }

                const buttons = bodyElement.querySelectorAll('button');
                const currentIndex = Array.from(buttons).indexOf(activeElement);
                if (ev.key === 'ArrowDown' && currentIndex !== -1) {
                    buttons.item(currentIndex + 1)?.focus();
                    ev.preventDefault();
                    return;
                }

                if (ev.key === 'ArrowUp' && currentIndex === 0) {
                    onClose?.();
                    return;
                }
                if (ev.key === 'ArrowUp' && currentIndex !== -1) {
                    buttons.item(currentIndex - 1)?.focus();
                    ev.preventDefault();
                }
            },
            [onClose],
        );

        const onOpen = React.useCallback(
            (ev: React.MouseEvent<HTMLButtonElement, MouseEvent> | React.KeyboardEvent<HTMLButtonElement>) => {
                ev.preventDefault();
                setOpen(!isOpen);
                if (isOpen && onCloseChild) {
                    onCloseChild(element.key);
                }
            },
            [element.key, isOpen, onCloseChild],
        );

        const previousListItemsRef = React.useRef(listItems);

        React.useEffect(() => {
            if (isOpen) {
                fetchItems(element).then(elements => {
                    const sortedElements = [...elements];
                    sortedElements.sort((a, b) => a.label.localeCompare(b.label));
                    setListItems(sortedElements);
                });
            }
        }, [isOpen, element, fetchItems]);

        React.useEffect(() => {
            if (level !== 0 && onOpenChild && listItems && listItems !== previousListItemsRef.current) {
                onOpenChild(
                    element.key,
                    listItems.map(item => item.label),
                );
            }

            previousListItemsRef.current = listItems;
        }, [listItems, level, onOpenChild, element.key]);

        function findValuesByPartialPath(obj: any, partialPath: string): string[] {
            return flatMap(filter(obj, (_value, key) => includes(key, partialPath)));
        }

        const findSearchText = React.useCallback(
            (item: any) => {
                const result = findValuesByPartialPath(listOpenedItems, item.key);
                return (
                    item.label.toLowerCase().includes(searchText.toLowerCase()) ||
                    result.findIndex(ele => ele.toLowerCase().includes(searchText.toLowerCase())) !== -1
                );
            },
            [listOpenedItems, searchText],
        );

        const renderBody = React.useCallback((): React.ReactElement => {
            const ulProps: React.DetailedHTMLProps<React.HTMLAttributes<HTMLUListElement>, HTMLUListElement> = {
                className: `e-tree-view-element-body e-tree-view-element-body-level-${level}`,
                ref: bodyRef,
            };
            if (level === 0) {
                ulProps.role = 'tree';
                ulProps['aria-multiselectable'] = selectionMode === 'multiple';
                ulProps.onKeyDown = onTopTreeKeyDown;
            }
            return (
                <ul {...ulProps}>
                    {listItems
                        ?.filter(item => findSearchText(item))
                        .map(childElement => (
                            <Tree
                                checkedItems={checkedItems}
                                element={childElement}
                                fetchItems={fetchItems}
                                isDisabled={isDisabled}
                                isReadOnly={isReadOnly}
                                isFolderIconHidden={isFolderIconHidden}
                                key={childElement.key}
                                level={level + 1}
                                listOpenedItems={listOpenedItems}
                                localize={localize}
                                onCheckedItemsUpdated={onCheckedItemsUpdated}
                                onCloseChild={onCloseChild}
                                onOpenChild={onOpenChild}
                                searchText={searchText}
                                selectionMode={selectionMode}
                            />
                        ))}
                </ul>
            );
        }, [
            level,
            listItems,
            selectionMode,
            onTopTreeKeyDown,
            findSearchText,
            checkedItems,
            fetchItems,
            isDisabled,
            isReadOnly,
            isFolderIconHidden,
            listOpenedItems,
            localize,
            onCheckedItemsUpdated,
            onCloseChild,
            onOpenChild,
            searchText,
        ]);

        const onChecked = React.useCallback(
            (ev: React.ChangeEvent<HTMLInputElement>) => {
                const newCheckedItems = { ...(checkedItems || {}) };
                if (ev.target.checked) {
                    newCheckedItems[element.key] = {
                        label: element.label,
                        data: element.data,
                        id: element.key,
                        key: element.key,
                        labelKey: element.labelKey,
                        labelPath: element.labelPath,
                    };
                } else {
                    delete newCheckedItems[element.key];
                }
                if (onCheckedItemsUpdated) {
                    onCheckedItemsUpdated(newCheckedItems);
                }
            },
            [checkedItems, element, onCheckedItemsUpdated],
        );

        const onButtonClick = React.useCallback(() => {
            const newCheckedItems: Dict<TreeElement<T>> = {};
            newCheckedItems[element.key] = {
                label: element.label,
                data: element.data,
                id: element.key,
                key: element.key,
                labelKey: element.labelKey,
                labelPath: element.labelPath,
            };
            if (onCheckedItemsUpdated) {
                onCheckedItemsUpdated(newCheckedItems);
            }
        }, [element, onCheckedItemsUpdated]);

        if (level === 0) {
            return renderBody();
        }

        const indentations: React.ReactNode[] = [];
        for (let i = 1; i < level; i += 1) {
            indentations.push(
                <span
                    key={i}
                    className={`e-tree-view-element-tree-branch e-tree-view-element-tree-branch-level-${i}`}
                />,
            );
        }

        const isChecked = !!checkedItems[element.key];

        return (
            <li
                className={`e-tree-view-element e-tree-view-element-level-${level} ${isChecked ? ' checked' : ''}`}
                role="treeitem"
                aria-selected={isChecked}
                aria-expanded={element.canBeExpanded ? (isOpen ? 'true' : 'false') : undefined}
            >
                <div className={`e-tree-view-element-container e-tree-view-element-container-${selectionMode}`}>
                    {indentations.length > 0 && <span className="e-tree-view-indent-container">{indentations}</span>}
                    {level > 1 && <span className="e-tree-view-line" />}
                    {element.canBeSelected && !isReadOnly && selectionMode !== 'button' && (
                        <div className="e-tree-view-checkbox-container">
                            {selectionMode === 'multiple' && (
                                <Checkbox
                                    aria-label={element.label}
                                    checked={!!checkedItems[element.key]}
                                    data-testid={`e-tree-view-checkbox e-tree-view-checkbox-label-${camelCase(
                                        element.label,
                                    )}`}
                                    disabled={isDisabled}
                                    mb="0px"
                                    onChange={onChecked}
                                />
                            )}
                        </div>
                    )}
                    {element.canBeSelected && isReadOnly && (
                        <Icon
                            className="e-tree-view-switcher-icon"
                            type={checkedItems[element.key] ? 'tick' : 'cross'}
                            color={tokens.colorsYin090}
                        />
                    )}
                    {element.canBeExpanded && (
                        <div className="e-tree-view-element-node-container">
                            <span
                                className={`e-tree-view-switcher-container${
                                    isOpen ? ' e-tree-view-switcher-container-open' : ''
                                }${isFolderIconHidden ? ' e-tree-view-switcher-container-hidden-folder' : ''}`}
                            >
                                <IconButton
                                    data-testid={`e-tree-view-switcher-icon-container e-tree-view-switcher-label-${camelCase(
                                        element.label,
                                    )}`}
                                    onClick={onOpen}
                                    disabled={isDisabled}
                                >
                                    <>
                                        <Icon
                                            className="e-tree-view-switcher-icon"
                                            type={!isOpen ? 'plus' : 'minus'}
                                            color={tokens.colorsActionMinor400}
                                        />
                                        <span className="e-tree-view-switcher-label e-screen-reader-only">
                                            {isOpen
                                                ? localize('@sage/xtrem-ui-components/collapse', 'Collapse')
                                                : localize('@sage/xtrem-ui-components/expand', 'Expand')}
                                        </span>
                                    </>
                                </IconButton>
                            </span>
                            {!isFolderIconHidden && <Icon type="folder" className="e-tree-view-element-icon" />}
                            {(!element.canBeSelected || selectionMode !== 'button') && (
                                <label>{computeUnderlinedContent(element.label, searchText)}</label>
                            )}
                            {selectionMode === 'button' && element.canBeSelected && (
                                <Link
                                    data-testid={`e-tree-view-checkbox e-tree-view-checkbox-label-${camelCase(
                                        element.label,
                                    )}`}
                                    onClick={onButtonClick}
                                    disabled={isDisabled}
                                    iconAlign="left"
                                    variant="neutral"
                                >
                                    {computeUnderlinedContent(element.label, searchText)}
                                </Link>
                            )}
                        </div>
                    )}
                    {!element.canBeExpanded && (
                        <div
                            className="e-tree-view-icon-container"
                            data-testid={`e-tree-view-container-label-${camelCase(element.label)}`}
                        >
                            {selectionMode === 'button' && (
                                <Link
                                    data-testid={`e-tree-view-checkbox e-tree-view-checkbox-label-${camelCase(
                                        element.label,
                                    )}`}
                                    onClick={onButtonClick}
                                    disabled={isDisabled}
                                    icon={element.data.iconType || 'file_generic'}
                                    iconAlign="left"
                                    variant="neutral"
                                >
                                    {computeUnderlinedContent(element.label, searchText)}
                                </Link>
                            )}
                            {selectionMode !== 'button' && (
                                <>
                                    <Icon
                                        type={element.data.iconType || 'file_generic'}
                                        className="e-tree-view-element-icon"
                                    />
                                    <label>{computeUnderlinedContent(element.label, searchText)}</label>
                                </>
                            )}
                        </div>
                    )}
                </div>
                {isOpen && listItems !== null && renderBody()}
                {isOpen && listItems === null && <Loader size="small" />}
            </li>
        );
    },
);

Tree.displayName = 'Tree';
