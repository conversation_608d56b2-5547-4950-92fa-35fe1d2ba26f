.e-tree-view-element {
    list-style: none;
    white-space: nowrap;
    padding-left: 0;

    &.e-tree-view-element-level-1 {
        border: none;

        &>.e-tree-view-element-container>.e-tree-view-element-node-container,
        &>.e-tree-view-element-container>.e-tree-view-checkbox-container {
            padding-left: 6px;
        }
    }
}

.e-tree-view-element-tree-branch {
    border-left: 2px solid #CCD6DB;
    display: inline-block;
    height: 100%;
    margin-left: 24px;


    &.e-tree-view-element-tree-branch-level-1 {
        margin-left: 12px;
    }
}

.e-tree-view-element.checked {
    background: #EDF1F2;
}

.e-tree-view-element-container {
    height: 24px;
    display: flex;
    flex-direction: row;
    align-items: center;

    & label span.e-tree-view-underlined {
        font-weight: bold;
        text-decoration: underline;
        display: inline;
    }
}

.e-tree-view-element-node-container {
    display: inline-block;

    .e-tree-view-element-icon {
        padding: 0 2px;
    }
}

.e-tree-view-container {
    .e-tree-view-element-body:first-child {
        padding-left: 2px;
    }
}

.e-tree-view-element-body {
    margin: 0;
    padding-left: 0;

    label {
        font-size: 14px;
    }
}

.e-tree-view-line {
    width: 16px;
    min-width: 16px;
    height: 2px;
    background: #CCD6DB;
    display: inline-block;
}

.e-tree-view-switcher-container-open {
    position: relative;

    &::after {
        content: ' ';
        position: absolute;
        display: block;
        width: 2px;
        height: 6px;
        background-color: #CCD6DB;
        bottom: -5px;
        left: 8px;

    }
}

.e-tree-view-checkbox-container {
    padding-top: 4px;
    box-sizing: border-box;
}

.e-tree-view-checkbox-container,
.e-tree-view-icon-container {
    display: inline-block;
    vertical-align: middle;
    height: 24px;
}

.e-tree-view-element-container-single .e-tree-view-icon-container span,
.e-tree-view-element-container-multiple .e-tree-view-icon-container span {
    display: inline-block;
    width: 18px;
}

.e-tree-view-icon-container {
    padding-left: 4px;
}

.e-tree-view-icon-container label {
    padding-left: 4px;
}

.e-tree-view-switcher-container button {
    border: 1px solid #668494;
    background: #fff;
    width: 16px;
    height: 16px;
}

.e-tree-view-switcher-container.e-tree-view-switcher-container-hidden-folder button {
    margin-right: var(--spacing100);
}

.e-tree-view-switcher-container button span {
    width: 0;
    height: 0;
    padding-bottom: 2px;
}

.e-tree-view-switcher-container button span::before {
    padding-bottom: 2px;
}

.e-tree-view-element-icon {
    vertical-align: sub;
}

.e-tree-view-element-icon::before {
    font-size: 18px;
    line-height: 24px;
}

.e-tree-view-element-body>li:last-child>.e-tree-view-element-container>.e-tree-view-indent-container>.e-tree-view-element-tree-branch:last-child {
    margin-bottom: 11px;
    height: 13px;
}

.e-tree-view-indent-container {
    display: inline-block;
    height: 24px;
}


.e-tree-view-element-container-button .e-tree-view-icon-container {
    width: 100%;
    flex: 1;
    box-sizing: border-box;

    button {
        display: flex;
        width: 100%;
        justify-content: left;
        text-decoration: none;

        &>span:last-of-type {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-decoration: underline;
        }
    }
}


.e-screen-reader-only{
    display: none;
}