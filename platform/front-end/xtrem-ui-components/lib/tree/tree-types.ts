import type { Dict, LocalizeFunction, TreeElement } from '@sage/xtrem-shared';

export interface TreeProps<T extends unknown = any> {
    checkedItems: Dict<TreeElement<T>>;
    element: TreeElement<T>;
    fetchItems: (element: TreeElement<T>) => Promise<TreeElement<T>[]>;
    isDisabled?: boolean;
    isReadOnly?: boolean;
    isRootNode?: boolean;
    level: number;
    listOpenedItems?: { key: string; children: string[] };
    localize: LocalizeFunction;
    onCheckedItemsUpdated?: (checkedItems: Dict<TreeElement<T>>) => void;
    onCloseChild?: (key: string) => void;
    onOpenChild?: (key: string, children: string[]) => void;
    onClose?: () => void;
    searchText?: string;
    selectionMode?: 'multiple' | 'button';
    isFolderIconHidden?: boolean;
}
