import { fireEvent, render, waitFor } from '@testing-library/react';
import type { NodeTypeKind } from '@sage/xtrem-shared';
import React from 'react';
import type { TreeProps } from '../tree-types';
import { Tree } from '../tree';
import '@testing-library/jest-dom';

describe('Tree component', () => {
    let props: TreeProps;

    it('Should render a Tree component if data is provided', async () => {
        props = {
            element: {
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    kind: 'OBJECT',
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                },
                key: '',
                labelKey: '',
                label: '',
                id: '',
                labelPath: '',
            },
            checkedItems: {},
            onCheckedItemsUpdated: jest.fn(),
            fetchItems: jest.fn().mockResolvedValue([
                {
                    key: 'item1',
                    label: 'Item 1',
                    canBeExpanded: true,
                    canBeSelected: false,
                    data: {
                        type: 'String',
                    },
                },
            ]),
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
        };
        const component = render(<Tree {...props} />);

        await waitFor(() => {
            expect(component.baseElement.querySelectorAll('li').length).not.toEqual(0);
        });

        expect(component.container.querySelector('.e-tree-view-element-body')!.className).toEqual(
            'e-tree-view-element-body e-tree-view-element-body-level-0',
        );

        expect(component.container.querySelector('.e-tree-view-element')!.className).toEqual(
            'e-tree-view-element e-tree-view-element-level-1 ',
        );
    });

    it('Should render a Tree component with a selectable item', async () => {
        props = {
            element: {
                canBeExpanded: false,
                canBeSelected: true,
                data: {
                    kind: 'OBJECT',
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                },
                key: '',
                labelKey: '',
                label: '',
                id: '',
                labelPath: '',
            },
            checkedItems: {},
            onCheckedItemsUpdated: jest.fn(),
            fetchItems: jest.fn().mockResolvedValue([
                {
                    key: 'item1',
                    label: 'Item 1',
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        type: 'String',
                    },
                },
            ]),
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
        };
        const component = render(<Tree {...props} />);

        await waitFor(() => {
            expect(component.baseElement.querySelectorAll('li').length).not.toEqual(0);
        });

        expect(component.container.querySelector('.e-tree-view-checkbox-container')?.className).toEqual(
            'e-tree-view-checkbox-container',
        );
    });

    it('Should check the selected elements', async () => {
        props = {
            element: {
                canBeExpanded: false,
                canBeSelected: true,
                data: {
                    kind: 'OBJECT',
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                },
                key: '',
                labelKey: '',
                label: '',
                id: '',
                labelPath: '',
            },
            checkedItems: {
                item2: {
                    label: 'Item 2',
                    id: 'item2',
                    key: 'item2',
                    labelPath: 'Item 2',
                    data: { type: 'String' },
                    labelKey: '',
                },
                item3: {
                    label: 'Item 3',
                    id: 'item3',
                    key: 'item3',
                    labelPath: 'Item 3',
                    data: { type: 'String' },
                    labelKey: '',
                },
            },
            onCheckedItemsUpdated: jest.fn(),
            fetchItems: jest.fn().mockResolvedValue([
                {
                    key: 'item1',
                    labelKey: 'Item 1',
                    label: 'Item 1',
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        type: 'String',
                    },
                },
                {
                    key: 'item2',
                    labelKey: 'Item 2',
                    label: 'Item 2',
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        type: 'String',
                    },
                },
                {
                    key: 'item3',
                    labelKey: 'Item 3',
                    label: 'Item 3',
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        type: 'String',
                    },
                },
            ]),
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
        };
        const component = render(<Tree {...props} />);

        await waitFor(() => {
            expect(component.baseElement.querySelectorAll('li').length).not.toEqual(0);
            expect(component.baseElement.querySelector('.e-tree-view-checkbox-container')).toBeInTheDocument();
        });

        expect(props.onCheckedItemsUpdated).not.toHaveBeenCalled();
        fireEvent.click(component.getAllByTestId('e-tree-view-checkbox', { exact: false })[0]);
        expect(props.onCheckedItemsUpdated).toHaveBeenCalledWith({
            item1: {
                label: 'Item 1',
                id: 'item1',
                key: 'item1',
                labelPath: undefined,
                labelKey: 'Item 1',
                data: { type: 'String' },
            },
            item2: {
                label: 'Item 2',
                id: 'item2',
                key: 'item2',
                labelPath: 'Item 2',
                labelKey: '',
                data: { type: 'String' },
            },
            item3: {
                label: 'Item 3',
                id: 'item3',
                key: 'item3',
                labelPath: 'Item 3',
                labelKey: '',
                data: { type: 'String' },
            },
        });
    });

    it('Should update consumer component if checkbox is selected', async () => {
        props = {
            element: {
                canBeExpanded: false,
                canBeSelected: true,
                data: {
                    kind: 'OBJECT',
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                },
                key: '',
                labelKey: '',
                label: '',
                id: '',
                labelPath: '',
            },
            checkedItems: {
                item2: {
                    label: 'Item 2',
                    id: 'item2',
                    labelPath: 'item2',
                    data: { type: 'String' },
                    key: '',
                    labelKey: '',
                },
                item3: {
                    label: 'Item 3',
                    id: 'item3',
                    labelPath: 'item3',
                    data: { type: 'String' },
                    key: '',
                    labelKey: '',
                },
            },
            onCheckedItemsUpdated: jest.fn(),
            fetchItems: jest.fn().mockResolvedValue([
                {
                    key: 'item1',
                    label: 'Item 1',
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        type: 'String',
                    },
                },
                {
                    key: 'item2',
                    label: 'Item 2',
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        type: 'String',
                    },
                },
                {
                    key: 'item3',
                    label: 'Item 3',
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        type: 'String',
                    },
                },
            ]),
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
        };
        const component = render(<Tree {...props} />);

        await waitFor(() => {
            expect(component.baseElement.querySelectorAll('li').length).not.toEqual(0);
            expect(component.baseElement.querySelector('.e-tree-view-checkbox-container')).toBeInTheDocument();
        });

        const checkboxes = component.queryAllByTestId('e-tree-view-checkbox', { exact: false });
        expect(checkboxes[0].attributes.getNamedItem('checked')).toBeNull();
        expect(checkboxes[1].attributes.getNamedItem('checked')).not.toBeNull();
        expect(checkboxes[2].attributes.getNamedItem('checked')).not.toBeNull();
    });

    it('Should display the correct switcher icon if the item is expandable and it is closed', async () => {
        props = {
            element: {
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    kind: 'OBJECT',
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                },
                key: '',
                labelKey: '',
                id: '',
                labelPath: '',
                label: '',
            },
            checkedItems: {
                item2: {
                    label: 'Item 2',
                    id: 'item2',
                    labelPath: 'item2',
                    data: { type: 'String' },
                    key: '',
                    labelKey: '',
                },
                item3: {
                    label: 'Item 3',
                    id: 'item3',
                    labelPath: 'item3',
                    data: { type: 'String' },
                    key: '',
                    labelKey: '',
                },
            },
            onCheckedItemsUpdated: jest.fn(),
            fetchItems: jest.fn().mockResolvedValue([
                {
                    key: 'item1',
                    label: 'Item 1',
                    canBeExpanded: true,
                    canBeSelected: false,
                    data: {
                        type: 'String',
                    },
                },
                {
                    key: 'item2',
                    label: 'Item 2',
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        type: 'String',
                    },
                },
            ]),
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
        };
        const component = render(<Tree {...props} />);

        await waitFor(() => {
            expect(component.baseElement.querySelectorAll('li').length).not.toEqual(0);
        });

        const icons = component.baseElement.querySelectorAll('.e-tree-view-switcher-icon');
        expect(icons[0].getAttribute('type')).toEqual('plus');
    });

    it('Should display the correct switcher icon if the item is expandable and it is opened', async () => {
        props = {
            element: {
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    kind: 'OBJECT',
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                },
                key: '',
                labelKey: '',
                label: '',
                id: '',
                labelPath: '',
            },
            checkedItems: {
                item2: {
                    label: 'Item 2',
                    id: 'item2',
                    labelPath: 'item2',
                    data: { type: 'String' },
                    key: '',
                    labelKey: '',
                },
                item3: {
                    label: 'Item 3',
                    id: 'item3',
                    labelPath: 'item3',
                    data: { type: 'String' },
                    key: '',
                    labelKey: '',
                },
            },
            onCheckedItemsUpdated: jest.fn(),
            fetchItems: jest.fn().mockResolvedValue([
                {
                    key: 'item1',
                    label: 'Item 1',
                    canBeExpanded: true,
                    canBeSelected: false,
                    data: {
                        type: 'String',
                    },
                },
                {
                    key: 'item2',
                    label: 'Item 2',
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        type: 'String',
                    },
                },
            ]),
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
        };
        const component = render(<Tree {...props} />);

        await waitFor(() => {
            expect(component.baseElement.querySelectorAll('li').length).not.toEqual(0);
        });

        const switcherButton = component.getAllByTestId('e-tree-view-switcher-icon-container', { exact: false })[0];
        fireEvent.click(switcherButton);

        const icons = component.baseElement.querySelectorAll('.e-tree-view-switcher-icon');
        expect(icons[0].getAttribute('type')).toEqual('minus');
    });

    it('Should display the correct icon if the item is not expandable', async () => {
        props = {
            element: {
                canBeExpanded: false,
                canBeSelected: true,
                data: {
                    kind: 'OBJECT',
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                },
                key: '',
                labelKey: '',
                label: '',
                id: '',
                labelPath: '',
            },
            checkedItems: {
                item2: {
                    label: 'Item 2',
                    id: 'item2',
                    labelPath: 'item2',
                    data: { type: 'String' },
                    key: '',
                    labelKey: '',
                },
                item3: {
                    label: 'Item 3',
                    id: 'item3',
                    labelPath: 'item3',
                    data: { type: 'String' },
                    key: '',
                    labelKey: '',
                },
            },
            onCheckedItemsUpdated: jest.fn(),
            fetchItems: jest.fn().mockResolvedValue([
                {
                    key: 'item1',
                    label: 'Item 1',
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        type: 'String',
                    },
                },
                {
                    key: 'item2',
                    label: 'Item 2',
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        type: 'String',
                    },
                },
            ]),
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
        };
        const component = render(<Tree {...props} />);

        await waitFor(() => {
            expect(component.baseElement.querySelectorAll('li').length).not.toEqual(0);
        });

        const icons = component.baseElement.querySelectorAll('.e-tree-view-element-icon');
        expect(icons[0].getAttribute('type')).toEqual('file_generic');
    });

    it('Should call fetch items when the switcher open icon is clicked', async () => {
        props = {
            element: {
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    kind: 'OBJECT',
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                },
                key: '',
                labelKey: '',
                label: '',
                id: '',
                labelPath: '',
            },
            checkedItems: {
                item2: {
                    label: 'Item 2',
                    id: 'item2',
                    labelPath: 'item2',
                    data: { type: 'String' },
                    key: '',
                    labelKey: '',
                },
                item3: {
                    label: 'Item 3',
                    id: 'item3',
                    labelPath: 'item3',
                    data: { type: 'String' },
                    key: '',
                    labelKey: '',
                },
            },
            onCheckedItemsUpdated: jest.fn(),
            fetchItems: jest.fn().mockResolvedValue([
                {
                    key: 'item1',
                    label: 'Item 1',
                    canBeExpanded: true,
                    canBeSelected: false,
                    data: {
                        type: 'String',
                    },
                },
                {
                    key: 'item2',
                    label: 'Item 2',
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        type: 'String',
                    },
                },
            ]),
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
        };
        const component = render(<Tree {...props} />);

        await waitFor(() => {
            expect(component.baseElement.querySelectorAll('li').length).not.toEqual(0);
            expect(component.baseElement.querySelector('.e-tree-view-checkbox-container')).toBeInTheDocument();
        });

        const switcherButton = component.getAllByTestId('e-tree-view-switcher-icon-container', { exact: false })[0];
        fireEvent.click(switcherButton);

        expect(props.fetchItems).toHaveBeenCalled();
        expect(props.fetchItems).toHaveBeenCalledWith({
            key: 'item1',
            label: 'Item 1',
            canBeExpanded: true,
            canBeSelected: false,
            data: {
                type: 'String',
            },
        });
    });

    it('Should display only the item that matched searchText', async () => {
        props = {
            element: {
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    kind: 'OBJECT',
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                },
                key: '',
                labelKey: '',
                label: 'test',
                id: '',
                labelPath: '',
            },
            checkedItems: {},
            onCheckedItemsUpdated: jest.fn(),
            fetchItems: jest.fn().mockResolvedValue([
                {
                    key: 'item1',
                    label: 'Item 1',
                    canBeExpanded: true,
                    canBeSelected: false,
                    data: {
                        type: 'String',
                    },
                },
                {
                    key: 'item2',
                    label: 'Item 2',
                    canBeExpanded: true,
                    canBeSelected: false,
                    data: {
                        type: 'String',
                    },
                },
            ]),
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
            searchText: '2',
        };
        const component = render(<Tree {...props} />);

        await waitFor(() => {
            expect(component.baseElement.querySelectorAll('li').length).toEqual(1);
        });

        expect(component.container.querySelector('.e-tree-view-element-body')!.className).toEqual(
            'e-tree-view-element-body e-tree-view-element-body-level-0',
        );

        expect(component.container.querySelector('.e-tree-view-element')!.className).toEqual(
            'e-tree-view-element e-tree-view-element-level-1 ',
        );
    });

    it('Should be empty if no item matched searchText', async () => {
        props = {
            element: {
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    kind: 'OBJECT',
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                },
                key: '',
                labelKey: '',
                label: 'test',
                id: '',
                labelPath: '',
            },
            checkedItems: {},
            onCheckedItemsUpdated: jest.fn(),
            fetchItems: jest.fn().mockResolvedValue([
                {
                    key: 'item1',
                    label: 'Item 1',
                    canBeExpanded: true,
                    canBeSelected: false,
                    data: {
                        type: 'String',
                    },
                },
                {
                    key: 'item2',
                    label: 'Item 2',
                    canBeExpanded: true,
                    canBeSelected: false,
                    data: {
                        type: 'String',
                    },
                },
            ]),
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
            searchText: 'aa',
        };
        const component = render(<Tree {...props} />);

        await waitFor(() => {
            expect(component.baseElement.querySelectorAll('li').length).toEqual(0);
        });

        expect(component.container.querySelector('.e-tree-view-element-body')!.className).toEqual(
            'e-tree-view-element-body e-tree-view-element-body-level-0',
        );
    });

    it('Should underline the matched searchText', async () => {
        props = {
            element: {
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    kind: 'OBJECT',
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                },
                key: '',
                labelKey: '',
                label: 'test',
                id: '',
                labelPath: '',
            },
            checkedItems: {},
            onCheckedItemsUpdated: jest.fn(),
            fetchItems: jest.fn().mockResolvedValue([
                {
                    key: 'item1',
                    label: 'Item 1',
                    canBeExpanded: true,
                    canBeSelected: false,
                    data: {
                        type: 'String',
                    },
                },
                {
                    key: 'item2',
                    label: 'Item 2',
                    canBeExpanded: true,
                    canBeSelected: false,
                    data: {
                        type: 'String',
                    },
                },
            ]),
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
            searchText: '2',
        };
        const component = render(<Tree {...props} />);

        await waitFor(() => {
            expect(component.container.querySelector('.e-tree-view-element-level-1 label span')!.className).toEqual(
                'e-tree-view-underlined',
            );
        });
    });

    it('Should display only the item that matched searchText in nested level with underlined searchText', async () => {
        const fetchItems = jest.fn();
        fetchItems.mockResolvedValueOnce([
            {
                key: 'item1',
                label: 'Item 1',
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    type: 'String',
                },
            },
            {
                key: 'item2',
                label: 'Item 2',
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    type: 'String',
                },
            },
        ]);
        fetchItems.mockResolvedValueOnce([
            {
                key: 'item3',
                label: 'Item 3',
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    type: 'String',
                },
            },
            {
                key: 'item4',
                label: 'Item 42',
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    type: 'String',
                },
            },
        ]);
        const props = {
            element: {
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    kind: 'OBJECT' as NodeTypeKind,
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                    canFilter: false,
                    canSort: false,
                    isStored: true,
                    isOnInputType: true,
                    isOnOutputType: true,
                    isCustom: false,
                    isMutable: true,
                    dataType: '',
                    targetNode: '',
                    enumType: null,
                },
                key: '',
                labelKey: '',
                label: 'test',
                id: '',
                labelPath: '',
            },
            checkedItems: {},
            onCheckedItemsUpdated: jest.fn(),
            fetchItems,
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
            searchText: '2',
            listOpenedItems: { key: '', children: [] },
            onOpenChild: jest.fn(),
            onCloseChild: jest.fn(),
        };
        const { getByTestId, getByText, container } = render(<Tree {...props} />);

        await waitFor(() => {
            expect(container.querySelectorAll('li').length).toEqual(1);
        });

        const switcherContainer = getByTestId('e-tree-view-switcher-icon-container', { exact: false });
        fireEvent.click(switcherContainer);

        await waitFor(() => {
            expect(fetchItems).toHaveBeenCalledWith(props.element);
            expect(fetchItems).toHaveBeenCalledTimes(2);
        });

        await waitFor(() => {
            expect(container.querySelectorAll('li.e-tree-view-element.e-tree-view-element-level-2').length).toEqual(1);
            expect(
                getByText(props.searchText, {
                    selector: 'li.e-tree-view-element.e-tree-view-element-level-2 span.e-tree-view-underlined',
                }),
            ).toBeInTheDocument();
        });
    });
    it('Should call onOpenChild callback', async () => {
        const fetchItems = jest.fn();
        const levelOneReturnedItems = [
            {
                key: 'item3',
                label: 'Item 3',
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    type: 'String',
                },
            },
            {
                key: 'item4',
                label: 'Item 42',
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    type: 'String',
                },
            },
        ];
        fetchItems.mockResolvedValueOnce([
            {
                key: 'item1',
                label: 'Item 1',
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    type: 'String',
                },
            },
            {
                key: 'item2',
                label: 'Item 2',
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    type: 'String',
                },
            },
        ]);
        fetchItems.mockResolvedValueOnce(levelOneReturnedItems);
        const props = {
            element: {
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    kind: 'OBJECT' as NodeTypeKind,
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                    canFilter: false,
                    canSort: false,
                    isStored: true,
                    isOnInputType: true,
                    isOnOutputType: true,
                    isCustom: false,
                    isMutable: true,
                    dataType: '',
                    targetNode: '',
                    enumType: null,
                },
                key: '',
                labelKey: '',
                label: 'test',
                id: '',
                labelPath: '',
            },
            checkedItems: {},
            onCheckedItemsUpdated: jest.fn(),
            fetchItems,
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
            searchText: '2',
            listOpenedItems: { key: '', children: [] },
            onOpenChild: jest.fn(),
            onCloseChild: jest.fn(),
        };
        const { getByTestId, container } = render(<Tree {...props} />);

        await waitFor(() => {
            expect(container.querySelectorAll('li').length).toEqual(1);
        });

        const switcherContainer = getByTestId('e-tree-view-switcher-icon-container', { exact: false });
        fireEvent.click(switcherContainer);

        await waitFor(() => {
            expect(props.onOpenChild).toHaveBeenCalledWith(
                'item2',
                levelOneReturnedItems.map(item => item.label),
            );
        });
    });
    it('Should call onCloseChild callback', async () => {
        props = {
            element: {
                canBeExpanded: true,
                canBeSelected: false,
                data: {
                    kind: 'OBJECT',
                    label: '',
                    name: '',
                    type: 'ShowCaseProduct',
                },
                key: '',
                labelKey: '',
                label: 'test',
                id: '',
                labelPath: '',
            },
            checkedItems: {},
            onCheckedItemsUpdated: jest.fn(),
            fetchItems: jest.fn().mockResolvedValue([
                {
                    key: 'item1',
                    label: 'Item 1',
                    canBeExpanded: true,
                    canBeSelected: false,
                    data: {
                        type: 'String',
                    },
                },
                {
                    key: 'item2',
                    label: 'Item 2',
                    canBeExpanded: true,
                    canBeSelected: false,
                    data: {
                        type: 'String',
                    },
                },
            ]),
            isRootNode: true,
            level: 0,
            localize: jest.fn().mockImplementation((value: string) => value),
            searchText: '2',
            listOpenedItems: { key: '', children: [] },
            onOpenChild: jest.fn(),
            onCloseChild: jest.fn(),
        };
        const { getByTestId, container } = render(<Tree {...props} />);

        await waitFor(() => {
            expect(container.querySelectorAll('li').length).toEqual(1);
        });

        const switcherContainer = getByTestId('e-tree-view-switcher-icon-container', { exact: false });
        fireEvent.click(switcherContainer);
        fireEvent.click(switcherContainer);

        await waitFor(() => {
            expect(props.onCloseChild).toHaveBeenCalledTimes(1);
        });
    });
});
