import React from 'react';
import type { TextValueInputProps } from '../text-value-input';
import { TextValueInput } from '../text-value-input';
import { render, waitFor } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { mockedGetBoundingClientRect, originalGetBoundingClientRect } from '../../__tests__/common';

describe('Text column value input', () => {
    let props: TextValueInputProps;

    beforeAll(() => {
        Element.prototype.getBoundingClientRect = mockedGetBoundingClientRect;
    });

    afterAll(() => {
        Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
    });

    beforeEach(() => {
        props = {
            columnId: 'value',
            localize: jest.fn().mockImplementation((_, v) => v),
            onCellChange: jest.fn(),
            rowData: { _id: '123' },
            isDisabled: false,
        };
    });

    describe('render', () => {
        it('should render with empty value', () => {
            const { queryByTestId } = render(<TextValueInput {...props} />);

            expect(queryByTestId('e-widget-editor-filter-value-123')!).toHaveValue('');
        });

        it('should render with a value', () => {
            props.rowData.value = 'test';
            const { queryByTestId } = render(<TextValueInput {...props} />);

            expect(queryByTestId('e-widget-editor-filter-value-123')!).toHaveValue('test');
        });
    });

    describe('interactions', () => {
        it('should update the value', async () => {
            const { queryByTestId } = render(<TextValueInput {...props} />);
            const input = queryByTestId('e-widget-editor-filter-value-123') as HTMLInputElement;
            await userEvent.type(input, 'X');
            await waitFor(() => {
                expect(props.onCellChange).toHaveBeenCalledWith({
                    columnId: 'value',
                    rowId: '123',
                    value: 'X',
                    rowData: { _id: '123' },
                });
            });
        });
    });
});
