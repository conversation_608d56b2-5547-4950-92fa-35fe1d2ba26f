import React from 'react';
import type { BooleanValueInputProps } from '../boolean-value-input';
import { BooleanValueInput } from '../boolean-value-input';
import { render, waitFor } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { mockedGetBoundingClientRect, originalGetBoundingClientRect } from '../../__tests__/common';

describe('Boolean column value input', () => {
    let props: BooleanValueInputProps;

    beforeAll(() => {
        Element.prototype.getBoundingClientRect = mockedGetBoundingClientRect;
    });

    afterAll(() => {
        Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
    });

    beforeEach(() => {
        props = {
            columnId: 'value',
            localize: jest.fn().mockImplementation((_, v) => v),
            onCellChange: jest.fn(),
            rowData: { _id: '123' },
            isDisabled: false,
        };
    });

    describe('render', () => {
        it('should render with empty value', () => {
            const { queryByTestId } = render(<BooleanValueInput {...props} />);

            expect(queryByTestId('e-widget-editor-filter-value-123')!).toHaveValue('');
        });

        it('should render with true value', () => {
            props.rowData.value = true;
            const { queryByTestId } = render(<BooleanValueInput {...props} />);

            expect(queryByTestId('e-widget-editor-filter-value-123')!).toHaveValue('True');
        });

        it('should render with false value', () => {
            props.rowData.value = false;
            const { queryByTestId } = render(<BooleanValueInput {...props} />);

            expect(queryByTestId('e-widget-editor-filter-value-123')!).toHaveValue('False');
        });
    });

    describe('interactions', () => {
        it('should update the value to true', async () => {
            const { baseElement, queryByTestId } = render(<BooleanValueInput {...props} />);
            const input = queryByTestId('e-widget-editor-filter-value-123') as HTMLInputElement;
            await userEvent.type(input, 'True');
            await userEvent.click(baseElement.querySelector('li')!);
            await waitFor(() => {
                expect(props.onCellChange).toHaveBeenCalledWith({
                    columnId: 'value',
                    rowId: '123',
                    value: 'true',
                    rowData: { _id: '123' },
                });
            });
        });

        it('should update the value to false', async () => {
            const { baseElement, queryByTestId } = render(<BooleanValueInput {...props} />);
            const input = queryByTestId('e-widget-editor-filter-value-123') as HTMLInputElement;
            await userEvent.type(input, 'False');
            await userEvent.click(baseElement.querySelector('li')!);
            await waitFor(() => {
                expect(props.onCellChange).toHaveBeenCalledWith({
                    columnId: 'value',
                    rowId: '123',
                    value: 'false',
                    rowData: { _id: '123' },
                });
            });
        });
    });
});
