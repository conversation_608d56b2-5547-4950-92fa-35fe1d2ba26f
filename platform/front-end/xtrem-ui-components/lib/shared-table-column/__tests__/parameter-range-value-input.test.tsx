import React from 'react';
import type { ParameterRangeValueInputProps } from '../parameter-range-value-input';
import { ParameterRangeValueInput } from '../parameter-range-value-input';
import { render, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

describe('parameter range value input', () => {
    let props: ParameterRangeValueInputProps;

    beforeEach(() => {
        props = {
            isDisabled: false,
            localize: jest.fn(),
            onCellChange: jest.fn(),
            rowData: {
                _id: '1',
                filterType: 'inRange',
                filterValue: 'testDataTypes.date2~testDataTypes.date3',
                key: 'testDataTypes.date1',
                label: 'Date 1',
                labelKey: 'Date 1',
                labelPath: 'Date 1',
                parameter: true,
                path: 'testDataTypes.date1',
                property: {
                    canBeExpanded: false,
                    canBeSelected: true,
                    data: {
                        canFilter: true,
                        canSort: false,
                        kind: 'SCALAR',
                        label: 'Date 1',
                        name: 'testDataTypes.date1',
                        node: undefined,
                        type: 'Date',
                    },
                    id: 'testDataTypes.date1',
                    key: 'testDataTypes.date1',
                    label: 'Date 1',
                    labelKey: 'Date 1',
                    labelPath: 'Date 1',
                    title: 'Date 1',
                },
            },
            parameters: [
                {
                    name: 'testDataTypes._id',
                    type: 'IntReference',
                    label: 'Test data types id',
                },
                {
                    name: 'testDataTypes.id',
                    type: 'String',
                    label: 'Id',
                },
                {
                    name: 'testDataTypes.date1',
                    type: 'Date',
                    label: 'Date 1',
                },
                {
                    name: 'testDataTypes.date2',
                    type: 'Date',
                    label: 'Date 2',
                },
                {
                    name: 'testDataTypes.date3',
                    type: 'Date',
                    label: 'Date 3',
                },
            ],
            columnId: 'filterValue',
            'data-testid': 'filterValue',
        };
    });

    it('should render with date range', async () => {
        const { queryByTestId } = render(<ParameterRangeValueInput {...props} />);
        await waitFor(() => {
            expect(queryByTestId('e-widget-editor-filter-value-from-1')).toHaveValue('Date 2');
            expect(queryByTestId('e-widget-editor-filter-value-to-1')).toHaveValue('Date 3');
        });
    });
});
