import React from 'react';
import type { EnumValueInputProps } from '../enum-value-input';
import { EnumValueInput } from '../enum-value-input';
import { render, waitFor } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { mockedGetBoundingClientRect, originalGetBoundingClientRect } from '../../__tests__/common';
import { startCase } from 'lodash';

const setupResizeObserverMock = () => {
    if (!window) {
        return;
    }
    window.ResizeObserver =
        window.ResizeObserver ||
        jest.fn().mockImplementation((callback: ResizeObserverCallback) => {
            let hasCalledCallback = false;
            const observer: ResizeObserver = {
                disconnect: jest.fn(),
                // observe mock needs to actually call the callback straight away, as this is what a real ResizeObserver does
                // and this behaviour is needed for the FixedNavigationBarContextProvider to work properly.
                // Note that we must only call the callback once per ResizeObserver instance, to avoid stack overflows in
                // react-virtual.
                observe: jest.fn((target: Element) => {
                    if (!hasCalledCallback) {
                        hasCalledCallback = true;
                        callback([{ target } as ResizeObserverEntry], observer);
                    }
                }),
                unobserve: jest.fn(),
            };
            return observer;
        });
};

const mockDOMRect = (width: number, height: number, elementIdentifier: string) => {
    Element.prototype.getBoundingClientRect = jest.fn(function patata(this: HTMLElement) {
        if (this.getAttribute('data-component') === elementIdentifier) {
            return getDOMRect(width, height);
        }
        return getDOMRect(0, 0);
    });
};

const getDOMRect = (width: number, height: number): DOMRect => ({
    width,
    height,
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    x: 0,
    y: 0,
    toJSON: () => {},
});

describe('Enum column value input', () => {
    let props: EnumValueInputProps;

    beforeAll(() => {
        Element.prototype.getBoundingClientRect = mockedGetBoundingClientRect;
        setupResizeObserverMock();
        mockDOMRect(40, 100, 'select-list-scrollable-container');
    });

    afterAll(() => {
        Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
    });

    beforeEach(() => {
        props = {
            enumOptions: ['bad', 'notBad', 'great', 'good'],
            localizeEnumMember: jest.fn().mockImplementation((_, v) => startCase(v)),
            enumType: '@sage/xtrem-test/MyTestEnum',
            columnId: 'value',
            localize: jest.fn().mockImplementation((_, v) => v),
            onCellChange: jest.fn(),
            rowData: { _id: '123' },
            isDisabled: false,
        };
    });

    describe('render', () => {
        it('should render with empty value', () => {
            const { queryByTestId } = render(<EnumValueInput {...props} />);
            expect(queryByTestId('e-widget-editor-filter-value-123')!).toHaveValue('');
        });

        it('should render with a single value', async () => {
            props.rowData.value = ['good'];
            const { baseElement, findByRole } = render(<EnumValueInput {...props} />);
            const input = await findByRole('combobox');
            await userEvent.click(input);
            const selectedElements = baseElement.querySelectorAll('[aria-selected="true"]');
            expect(selectedElements).toHaveLength(1);
            expect(selectedElements[0]).toHaveTextContent('Good');
        });

        it('should render with multiple values', async () => {
            props.rowData.value = ['good', 'notBad'];
            const { baseElement, findByRole } = render(<EnumValueInput {...props} />);
            const input = await findByRole('combobox');
            await userEvent.click(input);
            const selectedElements = baseElement.querySelectorAll('[aria-selected="true"]');
            expect(selectedElements).toHaveLength(2);
            expect(selectedElements[0]).toHaveTextContent('Not Bad');
            expect(selectedElements[1]).toHaveTextContent('Good');
        });
    });

    describe('interactions', () => {
        it('should update the value to true', async () => {
            const { baseElement, findByRole } = render(<EnumValueInput {...props} />);
            const input = await findByRole('combobox');
            await userEvent.click(input);
            const options = baseElement.querySelectorAll('[data-component="option"]');
            await userEvent.click(options[2]);
            await waitFor(() => {
                expect(props.onCellChange).toHaveBeenCalledWith({
                    columnId: 'value',
                    rowId: '123',
                    value: ['great'],
                    rowData: { _id: '123' },
                });
            });
        });
    });
});
