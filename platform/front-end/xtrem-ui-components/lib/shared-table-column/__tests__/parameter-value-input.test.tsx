import React from 'react';
import type { ParameterValueInputProps } from '../parameter-value-input';
import { ParameterValueInput } from '../parameter-value-input';
import { render, waitFor } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { fakeParameters, mockedGetBoundingClientRect, originalGetBoundingClientRect } from '../../__tests__/common';

describe('Parameter column value input', () => {
    let props: ParameterValueInputProps;

    beforeAll(() => {
        Element.prototype.getBoundingClientRect = mockedGetBoundingClientRect;
    });

    afterAll(() => {
        Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
    });

    beforeEach(() => {
        props = {
            columnId: 'value',
            localize: jest.fn().mockImplementation((_, v) => v),
            onCellChange: jest.fn(),
            rowData: { _id: '123', property: { data: { type: 'Boolean' } } },
            isDisabled: false,
            parameters: fakeParameters,
        };
    });

    describe('render', () => {
        it('should render with empty value', () => {
            const { queryByTestId } = render(<ParameterValueInput {...props} />);
            expect(queryByTestId('e-widget-editor-filter-value-123')!).toHaveValue('');
        });

        it('should render with a value', () => {
            props.rowData = { _id: '123', value: 'invoiceNetTotal', property: { data: { type: 'Int' } } };
            const { queryByTestId } = render(<ParameterValueInput {...props} />);
            expect(queryByTestId('e-widget-editor-filter-value-123')!).toHaveValue('Invoice Net Total');
        });

        it('should render with an enum value with SET method', () => {
            props.parameters = [
                {
                    name: 'testDataTypes._id',
                    type: 'IntReference',
                    label: 'Test data types id',
                },
                {
                    name: 'testDataTypes.id',
                    type: 'String',
                    label: 'Id',
                },
                {
                    name: 'testDataTypes.enum1',
                    type: 'Enum',
                    label: 'Enum 1',
                },
                {
                    name: 'testDataTypes.enum2',
                    type: 'Enum',
                    label: 'Enum 2',
                },
            ];
            props.rowData = {
                _id: '123',
                path: 'testDataTypes.enum1',
                property: {
                    id: 'testDataTypes.enum1',
                    key: 'testDataTypes.enum1',
                    label: 'Enum 1',
                    title: 'Enum 1',
                    canBeExpanded: false,
                    canBeSelected: true,
                    labelKey: 'Enum 1',
                    labelPath: 'Enum 1',
                    data: {
                        type: 'Enum',
                        node: '@sage/xtrem-workflow/TestEnum',
                        canFilter: true,
                        canSort: false,
                        kind: 'SCALAR',
                        label: 'Enum 1',
                        name: 'testDataTypes.enum1',
                    },
                },
                filterType: 'set',
                filterValue: ['testDataTypes.enum2'],
                labelPath: 'Enum 1',
                key: 'testDataTypes.enum1',
                labelKey: 'Enum 1',
                parameter: true,
                label: 'Enum 1',
            };
            props.columnId = 'filterValue';
            const { queryByTestId } = render(<ParameterValueInput {...props} />);
            expect(queryByTestId('e-widget-editor-filter-value-123')!).toHaveValue('Enum 2');
        });
    });

    describe('interactions', () => {
        it('should update the value the value', async () => {
            const { baseElement, queryByTestId } = render(<ParameterValueInput {...props} />);
            const input = queryByTestId('e-widget-editor-filter-value-123') as HTMLInputElement;
            await userEvent.type(input, 'Print Additional details');
            await userEvent.click(baseElement.querySelector('li')!);
            await waitFor(() => {
                expect(props.onCellChange).toHaveBeenCalledWith({
                    columnId: 'value',
                    rowId: '123',
                    value: 'shouldPrintAdditionalDetails',
                    rowData: { _id: '123', property: { data: { type: 'Boolean' } } },
                });
            });
        });
    });
});
