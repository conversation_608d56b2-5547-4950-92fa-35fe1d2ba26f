import type { LocalizeFunction } from '@sage/xtrem-shared';
import Textbox from 'carbon-react/esm/components/textbox';
import React from 'react';

export interface TextValueInputProps {
    errors?: Record<string, any>;
    isDisabled?: boolean;
    localize: LocalizeFunction;
    onCellChange: (arg: { columnId: string; rowId: string; value: any; rowData: Record<string, any> }) => void;
    rowData: Record<string, any>;
    columnId: string;
    'data-testid'?: string;
}

export function TextValueInput({
    errors,
    isDisabled,
    localize,
    onCellChange,
    rowData,
    columnId,
    'data-testid': dataTestId = 'e-widget-editor-filter-value',
}: TextValueInputProps): React.ReactElement {
    return (
        <Textbox
            error={errors?.[rowData._id]?.[columnId]}
            tooltipPosition="top"
            disabled={isDisabled}
            data-testid={`${dataTestId}-${rowData._id}`}
            onChange={({ target: { value } }: { target: { value: string } }): void => {
                onCellChange({
                    columnId,
                    rowId: rowData._id,
                    value,
                    rowData,
                });
            }}
            placeholder={localize('@sage/xtrem-ui-components/filter-value', 'Filter value')}
            size="small"
            value={rowData[columnId] ?? ''}
        />
    );
}
