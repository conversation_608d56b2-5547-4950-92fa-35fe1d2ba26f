import type { LocalizeFunction } from '@sage/xtrem-shared';
import React from 'react';
import { FilterableSelect, Option } from 'carbon-react/esm/components/select';

export interface BooleanValueInputProps {
    isDisabled?: boolean;
    localize: LocalizeFunction;
    onCellChange: (arg: { columnId: string; rowId: string; value: any; rowData: Record<string, any> }) => void;
    rowData: Record<string, any>;
    columnId: string;
    'data-testid'?: string;
}

export function BooleanValueInput({
    isDisabled,
    localize,
    onCellChange,
    rowData,
    columnId,
    'data-testid': dataTestId = 'e-widget-editor-filter-value',
}: BooleanValueInputProps): React.ReactElement {
    return (
        <FilterableSelect
            disabled={isDisabled}
            openOnFocus={true}
            data-testid={`${dataTestId}-${rowData._id}`}
            onChange={({ target: { value } }): void => {
                onCellChange({
                    columnId,
                    rowId: rowData._id,
                    value: value as string | undefined,
                    rowData,
                });
            }}
            placeholder={localize('@sage/xtrem-ui-components/select-filter-value-boolean', 'Select value...')}
            size="small"
            value={String(rowData[columnId] ?? '')}
        >
            <Option text={localize('@sage/xtrem-ui-components/true', 'True')} value="true" />
            <Option text={localize('@sage/xtrem-ui-components/false', 'False')} value="false" />
        </FilterableSelect>
    );
}
