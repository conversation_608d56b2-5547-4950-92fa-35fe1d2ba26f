import type { LocalizeLocale } from '@sage/xtrem-shared';
import React from 'react';
import type { DateChangeEvent } from 'carbon-react/esm/components/date';
import DateInput from 'carbon-react/esm/components/date';
import type { CarbonLocaleType } from '../types';
import I18nProvider from 'carbon-react/esm/components/i18n-provider';

export interface DateValueInputProps {
    errors?: Record<string, any>;
    isDisabled?: boolean;
    onCellChange: (arg: { columnId: string; rowId: string; value: any; rowData: Record<string, any> }) => void;
    rowData: Record<string, any>;
    carbonLocale: CarbonLocaleType;
    locale: LocalizeLocale;
    columnId: string;
    'data-testid'?: string;
}

export function DateValueInput({
    carbonLocale,
    errors,
    isDisabled,
    locale,
    onCellChange,
    rowData,
    columnId,
    'data-testid': dataTestId = 'e-widget-editor-filter-value',
}: DateValueInputProps): React.ReactElement {
    return (
        <div className="e-widget-editor-flat-table-date">
            <I18nProvider locale={carbonLocale(locale || 'en-US')}>
                <DateInput
                    data-testid={`${dataTestId}-${rowData._id}`}
                    error={errors?.[rowData._id]?.filterValue}
                    disabled={isDisabled}
                    size="small"
                    readOnly={isDisabled}
                    tooltipPosition="top"
                    allowEmptyValue={true}
                    value={rowData[columnId]?.formattedValue || rowData[columnId]?.rawValue || ''}
                    onChange={(ev: DateChangeEvent): void => {
                        onCellChange({
                            columnId,
                            rowId: rowData._id,
                            value: ev.target.value,
                            rowData,
                        });
                    }}
                />
            </I18nProvider>
        </div>
    );
}
