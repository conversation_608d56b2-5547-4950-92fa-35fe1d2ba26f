import type { Dict, LocalizeFunction } from '@sage/xtrem-shared';
import React from 'react';
import { MultiSelect, Option } from 'carbon-react/esm/components/select';

export interface EnumValueInputProps {
    isDisabled?: boolean;
    onCellChange: (arg: { columnId: string; rowId: string; value: any; rowData: Record<string, any> }) => void;
    rowData: Record<string, any>;
    localizeEnumMember: (enumFullPathName: string, memberName: string) => string;
    localize: LocalizeFunction;
    columnId: string;
    enumType: string;
    enumOptions: string[];
    'data-testid'?: string;
}

export function EnumValueInput({
    isDisabled,
    onCellChange,
    rowData,
    localizeEnumMember,
    localize,
    columnId,
    enumType,
    enumOptions,
    'data-testid': dataTestId = 'e-widget-editor-filter-value',
}: EnumValueInputProps): React.ReactElement | null {
    if (!enumType) {
        return null;
    }

    const localizedOptions = enumOptions.reduce((value: Dict<string>, key: string) => {
        value[key] = localizeEnumMember(enumType, key);
        return value;
    }, {} as Dict<string>);
    return (
        <MultiSelect
            disabled={isDisabled}
            openOnFocus={true}
            data-testid={`${dataTestId}-${rowData._id}`}
            onChange={({ target: { value } }): void => {
                onCellChange({
                    columnId,
                    rowId: rowData._id,
                    value,
                    rowData,
                });
            }}
            placeholder={localize('@sage/xtrem-ui-components/select-filter-value-enum', 'Select value...')}
            size="small"
            value={rowData[columnId] ?? []}
        >
            {Object.keys(localizedOptions).map(option => {
                return <Option key={option} text={localizedOptions[option]} value={option} />;
            })}
        </MultiSelect>
    );
}
