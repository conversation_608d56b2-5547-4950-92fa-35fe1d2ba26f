import type { LocalizeFunction } from '@sage/xtrem-shared';
import type { FilterParameter } from '../filter-table/filter-table-types';
import React from 'react';
import { ParameterValueInput } from './parameter-value-input';

export interface ParameterRangeValueInputProps {
    errors?: Record<string, any>;
    isDisabled?: boolean;
    localize: LocalizeFunction;
    onCellChange: (arg: { columnId: string; rowId: string; value: any; rowData: Record<string, any> }) => void;
    rowData: Record<string, any>;
    parameters?: Array<FilterParameter>;
    columnId: string;
    'data-testid'?: string;
}

export function ParameterRangeValueInput(props: ParameterRangeValueInputProps): React.ReactElement | null {
    const { onCellChange, rowData, columnId } = props;
    const valueComponents = rowData[columnId]?.split('~');

    const [fromValue, setFromValue] = React.useState<string | null>(valueComponents?.[0] || null);
    const [toValue, setToValue] = React.useState<string | null>(valueComponents?.[1] || null);

    const onFromChange = React.useCallback(({ value }: { value: string }) => {
        setFromValue(value);
    }, []);

    const onToChange = React.useCallback(({ value }: { value: string }) => {
        setToValue(value);
    }, []);

    React.useEffect(() => {
        const value = rowData[columnId] as string;
        if (!value) {
            setFromValue(null);
            setToValue(null);
            return;
        }
        const components = value.split('~');
        setFromValue(components[0] || null);
        setToValue(components[1] || null);
    }, [rowData, columnId]);

    React.useEffect(() => {
        const currentValue = rowData[columnId] as string;
        if (!fromValue || !toValue) {
            if (currentValue) {
                onCellChange({
                    columnId,
                    rowId: rowData._id,
                    value: null,
                    rowData,
                });
            }
            return;
        }

        const newValue = `${fromValue}~${toValue}`;
        if (currentValue !== newValue) {
            onCellChange({
                columnId,
                rowId: rowData._id,
                value: newValue,
                rowData,
            });
        }
    }, [fromValue, toValue, onCellChange, columnId, rowData]);

    return (
        <div className="e-widget-editor-range-filter-parameter">
            <ParameterValueInput
                key="from"
                {...props}
                controlledValue={fromValue}
                onCellChange={onFromChange}
                data-testid="e-widget-editor-filter-value-from"
            />
            <ParameterValueInput
                key="to"
                {...props}
                controlledValue={toValue}
                onCellChange={onToChange}
                data-testid="e-widget-editor-filter-value-to"
            />
        </div>
    );
}
