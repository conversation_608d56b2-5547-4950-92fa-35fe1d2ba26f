import { GraphQLTypes, type LocalizeFunction } from '@sage/xtrem-shared';
import type { FilterParameter } from '../filter-table/filter-table-types';
import React from 'react';
import { FilterableSelect, Option } from 'carbon-react/esm/components/select';
import { NUMERIC_TYPES } from '../utils';
import type { DefaultPropertyType } from '../types';
import { isArray } from 'lodash';
import Button from 'carbon-react/esm/components/button';

function getFilteredParameters(
    noParameterFiltering: boolean,
    property?: DefaultPropertyType,
    parameters: Array<FilterParameter> = [],
): Array<FilterParameter> {
    if (noParameterFiltering) {
        return parameters;
    }

    if (!property || !parameters) {
        return [];
    }
    const normalizedExpectedType = NUMERIC_TYPES.includes(property.data.type) ? GraphQLTypes.Int : property.data.type;
    return parameters.filter(i => {
        const normalizedCurrentType = NUMERIC_TYPES.includes(i.type) ? GraphQLTypes.Int : i.type;
        return (
            normalizedCurrentType === normalizedExpectedType || (property.data.name === '_id' && i.type === 'reference')
        );
    });
}

export interface ParameterValueInputProps {
    errors?: Record<string, any>;
    isDisabled?: boolean;
    localize: LocalizeFunction;
    onCellChange: (arg: { columnId: string; rowId: string; value: any; rowData: Record<string, any> }) => void;
    rowData: Record<string, any>;
    parameters?: Array<FilterParameter>;
    columnId: string;
    controlledValue?: string | null;
    noParameterFiltering?: boolean;
    'data-testid'?: string;
    filterValueAction?: () => void;
    filterValueActionLabel?: string;
}

export function ParameterValueInput({
    isDisabled,
    localize,
    onCellChange,
    rowData,
    parameters = [],
    columnId,
    controlledValue,
    noParameterFiltering = false,
    'data-testid': dataTestId = 'e-widget-editor-filter-value',
    filterValueAction,
    filterValueActionLabel,
}: ParameterValueInputProps): React.ReactElement | null {
    const filteredParameters = getFilteredParameters(noParameterFiltering, rowData?.property, parameters);
    const externalValue = isArray(rowData[columnId]) ? rowData[columnId][0] : rowData[columnId];
    return (
        <FilterableSelect
            listActionButton={
                filterValueAction && (
                    <Button iconType="add" iconPosition="after">
                        {filterValueActionLabel ?? localize('@sage/xtrem-ui-components/add-property', 'Add property')}
                    </Button>
                )
            }
            onListAction={() => {
                if (filterValueAction) {
                    filterValueAction();
                }
            }}
            disabled={isDisabled}
            openOnFocus={true}
            data-testid={`${dataTestId}-${rowData._id}`}
            onChange={({ target: { value } }): void => {
                onCellChange({
                    columnId,
                    rowId: rowData._id,
                    value: value as string | undefined,
                    rowData,
                });
            }}
            placeholder={localize('@sage/xtrem-ui-components/select-value-placeholder', 'Select value...')}
            size="small"
            value={controlledValue !== undefined ? controlledValue : (externalValue ?? '')}
        >
            {filteredParameters.map(v => (
                <Option key={v.label || v.name} text={v.label || v.name} value={v.name}>
                    {v.label}
                </Option>
            ))}
        </FilterableSelect>
    );
}
