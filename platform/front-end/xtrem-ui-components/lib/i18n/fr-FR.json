{"@sage/xtrem-ui-components/actions": "Actions", "@sage/xtrem-ui-components/add-column": "Ajouter colonne", "@sage/xtrem-ui-components/add-condition": "Ajouter une condition", "@sage/xtrem-ui-components/add-filter": "Ajouter filtre", "@sage/xtrem-ui-components/add-property": "<PERSON><PERSON><PERSON> propri<PERSON>", "@sage/xtrem-ui-components/add-sort-condition": "Ajjouter condition de tri", "@sage/xtrem-ui-components/Aggregate": "Agrégat", "@sage/xtrem-ui-components/ascending": "Croissant", "@sage/xtrem-ui-components/between": "<PERSON><PERSON>", "@sage/xtrem-ui-components/Button": "Bouton", "@sage/xtrem-ui-components/Calendar": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui-components/Card": "Fiche", "@sage/xtrem-ui-components/Chart": "Graphique", "@sage/xtrem-ui-components/Checkbox": "Case à cocher", "@sage/xtrem-ui-components/collapse": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui-components/condition-conjunction-and": "Et", "@sage/xtrem-ui-components/condition-conjunction-if": "Si", "@sage/xtrem-ui-components/condition-conjunction-or": "Ou", "@sage/xtrem-ui-components/condition-editor": "Conjonction", "@sage/xtrem-ui-components/condition-empty-state": "Sélectionner le bouton d'ajout pour commencer à modifier les conditions", "@sage/xtrem-ui-components/condition-table-value-1": "Valeur 1", "@sage/xtrem-ui-components/condition-table-value-2": "Valeur 2", "@sage/xtrem-ui-components/condition-value-type-1": "Type de valeur 1", "@sage/xtrem-ui-components/condition-value-type-2": "Type de valeur 2", "@sage/xtrem-ui-components/condition-value-type-constant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui-components/condition-value-type-parameter": "Paramètre", "@sage/xtrem-ui-components/condition-value-type-property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui-components/contains": "Contient", "@sage/xtrem-ui-components/ContentTable": "Table de contenu", "@sage/xtrem-ui-components/Count": "Comptage", "@sage/xtrem-ui-components/current-day": "Jour en cours", "@sage/xtrem-ui-components/current-month": "<PERSON><PERSON> actuel", "@sage/xtrem-ui-components/current-week": "<PERSON><PERSON><PERSON> en cours", "@sage/xtrem-ui-components/current-year": "Année en cours", "@sage/xtrem-ui-components/Date": "Date", "@sage/xtrem-ui-components/Datetime": "Date / Heure", "@sage/xtrem-ui-components/DatetimeRange": "Borne date / heure", "@sage/xtrem-ui-components/decimal-digits": "Décimales", "@sage/xtrem-ui-components/descending": "Décroissant", "@sage/xtrem-ui-components/DetailList": "Liste de détail", "@sage/xtrem-ui-components/disallowed-group-assignment": "Les propriétés non triables ne peuvent pas être les premiers éléments du groupe. Sélectionnez une autre propriété.", "@sage/xtrem-ui-components/divisor": "Diviseur", "@sage/xtrem-ui-components/DropdownList": "Liste déroulante", "@sage/xtrem-ui-components/DynamicPod": "Pod dynamique", "@sage/xtrem-ui-components/DynamicSelect": "Sélection dynamique", "@sage/xtrem-ui-components/empty": "Vide", "@sage/xtrem-ui-components/endsWith": "Se termine par", "@sage/xtrem-ui-components/equals": "Est égal à", "@sage/xtrem-ui-components/expand": "Développer", "@sage/xtrem-ui-components/false": "Faux", "@sage/xtrem-ui-components/File": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui-components/FileDeposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui-components/filter-pod-add-condition": "Ajouter une condition", "@sage/xtrem-ui-components/filter-pod-remove-condition": "Re<PERSON>rer la condition", "@sage/xtrem-ui-components/filter-table-option-enter-manually": "<PERSON><PERSON><PERSON><PERSON> man<PERSON>", "@sage/xtrem-ui-components/filter-table-option-select-parameter": "Sélectionner un paramètre", "@sage/xtrem-ui-components/filter-type": "Type de filtre", "@sage/xtrem-ui-components/filter-use-parameter": "Paramètre d'utilisation", "@sage/xtrem-ui-components/filter-value": "<PERSON>ur de filtre", "@sage/xtrem-ui-components/FilterEditor": "É<PERSON>eur de filtre", "@sage/xtrem-ui-components/FilterSelect": "Sélection de filtre", "@sage/xtrem-ui-components/FormDesigner": "Designer de formulaire", "@sage/xtrem-ui-components/greaterThan": "Sup<PERSON>ur à", "@sage/xtrem-ui-components/greaterThanOrEqual": "Su<PERSON><PERSON><PERSON> ou égal à", "@sage/xtrem-ui-components/group": "Groupe", "@sage/xtrem-ui-components/group-number": "Groupe {{groupNumber}}", "@sage/xtrem-ui-components/Icon": "Icône", "@sage/xtrem-ui-components/Image": "Image", "@sage/xtrem-ui-components/invalid-boolean": "Bool<PERSON><PERSON> invalide", "@sage/xtrem-ui-components/invalid-date": "Date invalide", "@sage/xtrem-ui-components/invalid-integer": "<PERSON>tier invalide", "@sage/xtrem-ui-components/invalid-number": "Numéro invalide", "@sage/xtrem-ui-components/invalid-range": "<PERSON><PERSON> invalide", "@sage/xtrem-ui-components/invalid-value": "<PERSON><PERSON> invalide", "@sage/xtrem-ui-components/Label": "Libellé", "@sage/xtrem-ui-components/last-seven-days": "Les 7 derniers jours", "@sage/xtrem-ui-components/last-thirty-days": "Les 30 derniers jours", "@sage/xtrem-ui-components/lessThan": "Inférieur à", "@sage/xtrem-ui-components/lessThanOrEqual": "Inférieur ou égal à", "@sage/xtrem-ui-components/Link": "<PERSON><PERSON>", "@sage/xtrem-ui-components/matches": "Correspond à", "@sage/xtrem-ui-components/Message": "Message", "@sage/xtrem-ui-components/MultiDropdown": "Liste déroulante multiple", "@sage/xtrem-ui-components/MultiFileDeposit": "Dépôt de fichiers multiples", "@sage/xtrem-ui-components/multipleRange": "Borne multiple", "@sage/xtrem-ui-components/multipleRanges": "Bornes multiples", "@sage/xtrem-ui-components/MultiReference": "Référence multiple", "@sage/xtrem-ui-components/must-be-between-zero-and-four": "Cette valeur doit se situer entre 0 et 4.", "@sage/xtrem-ui-components/must-be-greater-than-zero": "Cette valeur doit être supérieure à 0.", "@sage/xtrem-ui-components/name": "Nom", "@sage/xtrem-ui-components/NestedGrid": "Grille imbriquée", "@sage/xtrem-ui-components/next-day": "<PERSON><PERSON> suivant", "@sage/xtrem-ui-components/next-month": "<PERSON><PERSON> prochain", "@sage/xtrem-ui-components/next-week": "Semaine prochaine", "@sage/xtrem-ui-components/next-year": "<PERSON><PERSON>", "@sage/xtrem-ui-components/NodeBrowserTree": "Arborescence de navigateur de node", "@sage/xtrem-ui-components/notContains": "Ne contient pas", "@sage/xtrem-ui-components/notEmpty": "Non vide", "@sage/xtrem-ui-components/notEqual": "N'est pas égal à", "@sage/xtrem-ui-components/Numeric": "Numérique", "@sage/xtrem-ui-components/operation": "Opération", "@sage/xtrem-ui-components/operator": "Opérateur", "@sage/xtrem-ui-components/parent": "Parent", "@sage/xtrem-ui-components/Plugin": "Plugin", "@sage/xtrem-ui-components/Pod": "Pod", "@sage/xtrem-ui-components/pod-condition": "Condition", "@sage/xtrem-ui-components/PodCollection": "Collection de pods", "@sage/xtrem-ui-components/presentation": "Présentation", "@sage/xtrem-ui-components/Preview": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui-components/previous-day": "<PERSON><PERSON>", "@sage/xtrem-ui-components/previous-month": "<PERSON><PERSON>", "@sage/xtrem-ui-components/previous-week": "<PERSON><PERSON><PERSON>", "@sage/xtrem-ui-components/previous-year": "<PERSON><PERSON>", "@sage/xtrem-ui-components/Progress": "Avancement", "@sage/xtrem-ui-components/property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui-components/Radio": "Radio", "@sage/xtrem-ui-components/Reference": "Référence", "@sage/xtrem-ui-components/RelativeDate": "Date relative", "@sage/xtrem-ui-components/RichText": "Texte enrichi", "@sage/xtrem-ui-components/select": "Sélectionner...", "@sage/xtrem-ui-components/Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-ui-components/select-filter-type": "Sélectionner le type de filtre...", "@sage/xtrem-ui-components/select-filter-value-boolean": "Sélectionner la valeur...", "@sage/xtrem-ui-components/select-filter-value-enum": "Sélectionner la valeur...", "@sage/xtrem-ui-components/select-filter-value-max": "Valeur maximum", "@sage/xtrem-ui-components/select-filter-value-min": "Valeur minimum", "@sage/xtrem-ui-components/select-operator": "Sélectionner un opérateur...", "@sage/xtrem-ui-components/select-property": "Sélectionner la propriété...", "@sage/xtrem-ui-components/select-sort-order": "Sélectionner l'ordre de tri...", "@sage/xtrem-ui-components/select-value-placeholder": "Sélectionner la valeur...", "@sage/xtrem-ui-components/SelectionCard": "Carte de sélection", "@sage/xtrem-ui-components/Separator": "Séparateur", "@sage/xtrem-ui-components/set": "Est égal à", "@sage/xtrem-ui-components/sort-order": "Commande", "@sage/xtrem-ui-components/sorting": "Tri", "@sage/xtrem-ui-components/startsWith": "Commence par", "@sage/xtrem-ui-components/StaticContent": "Contenu statique", "@sage/xtrem-ui-components/StepSequence": "Séquence d'étape", "@sage/xtrem-ui-components/Switch": "Bascule", "@sage/xtrem-ui-components/Table": "Table", "@sage/xtrem-ui-components/TableSummary": "Récapitulatif de table", "@sage/xtrem-ui-components/Technical": "Technique", "@sage/xtrem-ui-components/TechnicalJson": "JSON technique", "@sage/xtrem-ui-components/Text": "Texte", "@sage/xtrem-ui-components/TextArea": "Zone de texte", "@sage/xtrem-ui-components/Time": "<PERSON><PERSON>", "@sage/xtrem-ui-components/timeframe": "Période de temps", "@sage/xtrem-ui-components/title": "Titre", "@sage/xtrem-ui-components/ToggleButton": "Bouton bascule", "@sage/xtrem-ui-components/Tree": "Arborescence", "@sage/xtrem-ui-components/true": "Vrai", "@sage/xtrem-ui-components/VisualProcess": "Processus visuel", "@sage/xtrem-ui-components/VitalPod": "Pod vital", "@sage/xtrem-ui-components/widget-editor-content-formatting": "Décimales", "@sage/xtrem-ui-components/widget-editor-no-filterable-properties": "Vous ne pouvez pas filtrer les valeurs actuelles. Vous pouvez filtrer des données différentes ou continuer sans filtre.", "@sage/xtrem-ui-components/widget-editor-no-selectable-properties": "Sé<PERSON><PERSON>ner une propriété pour ajouter une valeur.", "@sage/xtrem-ui-components/widget-editor-no-sortable-properties": "Vous ne pouvez pas trier les valeurs actuelles. Vous pouvez sélectionner des données différentes ou continuer sans tri.", "@sage/xtrem-ui-components/Workflow": "Workflow"}