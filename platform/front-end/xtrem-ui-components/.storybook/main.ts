import type { StorybookConfig } from '@storybook/react-webpack5';
import { join, dirname } from 'path';
const { copyStaticResources } = require('@sage/xtrem-static-shared');
const path = require('path');
const rootDir = path.resolve(__dirname, '..');
const Webpack = require('webpack');

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value: string): any {
    return dirname(require.resolve(join(value, 'package.json')));
}
const config: StorybookConfig = {
    stories: ['../stories/**/*.mdx', '../stories/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
    addons: [
        getAbsolutePath('@storybook/addon-webpack5-compiler-swc'),
        getAbsolutePath('@storybook/addon-onboarding'),
        getAbsolutePath('@storybook/addon-links'),
        getAbsolutePath('@storybook/addon-styling-webpack'),
    ],
    framework: {
        name: getAbsolutePath('@storybook/react-webpack5'),
        options: {},
    },
    webpackFinal: async (cfg) => {
        cfg.mode = 'development';
        cfg.plugins.push(copyStaticResources(cfg.output.path));
        cfg.resolve.extensions.push('.scss');
        cfg.resolve.alias = {
            ...cfg.resolve.alias,
            timers: getAbsolutePath('timers-browserify'),
            stream: getAbsolutePath('stream-browserify'),
        };
        cfg.plugins.push(
            new Webpack.NormalModuleReplacementPlugin(/@sage\/xtrem-i18n/, path.resolve(rootDir, 'empty-module.js')),
        );
        cfg.module.rules.push(
            {
                test: /\.scss$/,
                use: [
                    {
                        loader: 'style-loader', // 4. Creates `style` nodes from JS strings
                    },
                    {
                        loader: 'css-loader', // 3. Translates CSS into CommonJS
                        options: {
                            url: {
                                filter: (url, resourcePath) => {
                                    // Exclude URLs starting with '/absolute/path'
                                    if (url.startsWith('/')) {
                                        return false;
                                    }
                                    return true;
                                }
                            },
                        }
                    },
                    {
                        loader: 'resolve-url-loader', // 2. Resolves relative paths in url() statements based on the original source file
                    },
                    {
                        loader: 'sass-loader', // 1. compiles Sass to CSS
                        options: {
                            sourceMap: true, // needed for 'resolve-url-loader' to work
                            sassOptions: {
                                exclude: [/node_modules/],
                                sourceMapContents: false, // needed for 'resolve-url-loader' to work
                            },
                        },
                    },
                ]
            })
        return cfg;
    },
};
export default config;
