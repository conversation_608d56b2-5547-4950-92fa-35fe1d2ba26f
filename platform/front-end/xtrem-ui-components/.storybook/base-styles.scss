$fontCarbonIcons: 'CarbonIcons';
$fontAdelle: 'Adelle Sans SAGE';

@font-face {
    font-family: $fontAdelle;
    src: url('/fonts/AdelleSansSAGE-Bold.eot');
    src: url('/fonts/AdelleSansSAGE-Bold.eot?#iefix') format('embedded-opentype'), url('/fonts/AdelleSansSAGE-Bold.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: $fontCarbonIcons;
    src: url('/fonts/carbon-icons-webfont.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: "Sage UI";
    src: url('https://fonts.sage.com/Sage_UI-Regular.woff2') format("woff2"), url('https://fonts.sage.com/Sage_UI-Regular.woff') format("woff");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Sage UI";
    src: url('https://fonts.sage.com/Sage_UI-Medium.woff2') format("woff2"), url('https://fonts.sage.com/Sage_UI-Medium.woff') format("woff");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Sage UI";
    src: url('https://fonts.sage.com/Sage_UI-Medium.woff2') format("woff2"), url('https://fonts.sage.com/Sage_UI-Medium.woff') format("woff");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Sage UI";
    src: url('https://fonts.sage.com/Sage_UI-Bold.woff2') format("woff2"), url('https://fonts.sage.com/Sage_UI-Bold.woff') format("woff");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}
