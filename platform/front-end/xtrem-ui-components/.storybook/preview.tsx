import type { Preview } from '@storybook/react';
import React from 'react';
import CarbonProvider from 'carbon-react/esm/components/carbon-provider';
import sageTheme from 'carbon-react/esm/style/themes/sage';
import GlobalStyle from 'carbon-react/esm/style/global-style';
import '../lib/index.scss';
import './base-styles.scss';
import "carbon-react/lib/style/fonts.css";

const preview: Preview = {
    decorators: [
        Story => {
            return (
                <CarbonProvider theme={sageTheme}>
                    <GlobalStyle />
                    <Story />
                </CarbonProvider>
            );
        },
    ],
};

export default preview;
