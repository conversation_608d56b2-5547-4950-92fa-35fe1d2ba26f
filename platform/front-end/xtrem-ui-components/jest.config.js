if (process.env.SKIP_CLIENT === '1') {
    console.warn('Skipping tests on SKIP_CLIENT env variable.');
    process.exit(0);
}

const esmOnlyPackages = [
    '@ag-grid-community',
    '@ag-grid-enterprise',
    '@react-dnd',
    'carbon-react',
    'core-dnd',
    'dnd-core',
    'react-dnd-html5-backend',
    'react-dnd',
];

module.exports = {
    roots: ['<rootDir>/lib'],
    testTimeout: 15000,
    moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node', 'd.ts'],
    transform: {
        '^.+\\.(ts|tsx|js|jsx|mjs)$': [
            'ts-jest',
            {
                tsconfig: 'tsconfig.test.json',
            },
        ],
    },
    setupFilesAfterEnv: ['<rootDir>/lib/__tests__/jest-setup.ts'],
    testMatch: ['<rootDir>/**/__tests__/**/*test.ts?(x)'],
    testEnvironment: 'jsdom',
    runtime: '@side/jest-runtime',
    verbose: false,
    setupFiles: ['jest-canvas-mock', 'fake-indexeddb/auto'],
    reporters: ['default', 'jest-junit'],
    coverageProvider: 'v8',
    collectCoverageFrom: ['<rootDir>/lib/**/*.{ts,tsx}'],
    moduleDirectories: ['node_modules', 'lib', __dirname, '../xtrem-ui-components'], // <--- HERE!!!!

    coverageReporters: ['json', 'lcov', 'text', 'clover', 'cobertura'],
    testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/coverage/', '<rootDir>/webpack/'],
    coveragePathIgnorePatterns: [
        '.*__tests__.*',
        '<rootDir>/node_modules/',
        '<rootDir>/coverage/',
        '<rootDir>/webpack/',
    ],
    transformIgnorePatterns: [
        '<rootDir>/node_modules/',
        '<rootDir>/coverage/',
        '<rootDir>/junit/',
        '<rootDir>/webpack/',
        `node_modules/(?!.pnpm|${esmOnlyPackages.join('|')})`,
    ],
    moduleNameMapper: {
        '\\.(css|scss|svg)$': 'identity-obj-proxy',
        'carbon-react/esm/(.*)': '<rootDir>/node_modules/carbon-react/lib/$1',
    },
    snapshotFormat: {
        escapeString: true,
        printBasicPrototype: true,
    },
};
