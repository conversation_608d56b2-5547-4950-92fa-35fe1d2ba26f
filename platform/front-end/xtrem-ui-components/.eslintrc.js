module.exports = {
    plugins: ['jsx-a11y', 'react'],
    extends: ['../../.eslintrc-base.js', 'plugin:react/recommended', 'plugin:jsx-a11y/strict', 'plugin:react-hooks/recommended', 'plugin:storybook/recommended'],
    ignorePatterns: ['lib/__tests__/test-helpers'],
    parserOptions: {
        tsconfigRootDir: __dirname,
        project: ['tsconfig.json', 'tsconfig.test.json'],
    },
    overrides: [
        {
            files: ['lib/**/*','stories/**/*'],
            rules: {
                '@typescript-eslint/consistent-type-imports': 'error',
                '@typescript-eslint/no-unused-expressions': 'error',
                '@typescript-eslint/explicit-function-return-type': 'error',
                'no-console': 'error',
                'func-names': 'error',
                'react/jsx-props-no-spreading': 'off',
                'react/jsx-boolean-value': 'off',
                'react/require-default-props': 'off',
                'react/jsx-indent-props': ['error', 4],
                'react/jsx-indent': ['error', 4],
                'react/destructuring-assignment': 'off',
                'react/prop-types': 'off',
                'class-methods-use-this': 'off',
                'no-case-declarations': 'off',
                'jsx-a11y/no-static-element-interactions': 'off',
                'jsx-a11y/click-events-have-key-events': 'off',
                'jsx-a11y/alt-text': 'off',
                'jsx-a11y/label-has-for': 'off',
                'jsx-a11y/label-has-associated-control': 'off',
                'jsx-a11y/tabindex-no-positive': 'off',
                'jsx-a11y/no-noninteractive-tabindex': 'off',
                'import/no-extraneous-dependencies': 'warn',
                'react/sort-comp': 'warn',
                'react-hooks/exhaustive-deps': 'error',
                '@typescript-eslint/ban-ts-comment': 'error',
                '@typescript-eslint/naming-convention': [
                    'error',
                    {
                        selector: 'variable',
                        format: ['camelCase', 'UPPER_CASE', 'PascalCase'],
                        filter: {
                            regex: '^_id|__collectionItem|__value$',
                            match: false,
                        },
                    },
                ],
                'react/no-access-state-in-setstate': 'error',
                'default-case': 'warn',
                'react/no-children-prop': 'warn',
                'no-useless-escape': 'error',
                'jsx-a11y/no-noninteractive-element-interactions': 'warn',
                'jsx-a11y/anchor-is-valid': 'warn',
                'import/no-named-as-default': 'off',
                'new-cap': 'warn',
                'react/display-name': 'warn',
                'max-params': ['warn', 5],
                'react/no-unused-prop-types': 'warn',
                'react/jsx-wrap-multilines': ['error', { arrow: true, return: true, declaration: true }],
                // promise stuff
                '@typescript-eslint/no-floating-promises': 'off',
                '@typescript-eslint/no-misused-promises': 'off',
                '@typescript-eslint/await-thenable': 'off',
                'require-await': 'off',
                'no-promise-executor-return': 'off',
                // TODO: these rules have been added here and disabled after tslint upgrade - REVIEW
                '@typescript-eslint/no-shadow': 'off',

                // TO BE REVIEWED by front-end team
                'react/function-component-definition': 'error',
                'react/no-unused-class-component-methods': 'warn',
                'react/no-unstable-nested-components': 'warn',
                'react/jsx-no-useless-fragment': 'warn',
                '@typescript-eslint/default-param-last': 'warn',
            },
        },
        {
            files: ['lib/redux/reducer/**/*'],
            rules: {
                '@typescript-eslint/default-param-last': 'off',
            },
        },
        {
            files: ['lib/component/types-test.ts'],
            rules: {
                '@typescript-eslint/ban-ts-comment': 'off',
            },
        },
        {
            files: ['*.test.ts', '*.test.tsx', '**/__mocks__/**'],
            rules: {
                '@typescript-eslint/no-unused-expressions': 'error',
                '@typescript-eslint/explicit-function-return-type': 'off',
                '@typescript-eslint/ban-ts-comment': 'error',
                '@typescript-eslint/no-shadow': 'off',
                'no-console': 'off',
                'func-names': 'error',
                'import/first': 'off',
                'react/jsx-props-no-spreading': 'off',
                'react/jsx-boolean-value': 'off',
                'react/require-default-props': 'off',
                'react/jsx-indent-props': ['error', 4],
                'react/jsx-indent': ['warn', 4],
                'react/destructuring-assignment': 'off',
                'react/prop-types': 'off',
                'class-methods-use-this': 'off',
                'no-case-declarations': 'off',
                'jsx-a11y/no-static-element-interactions': 'off',
                'jsx-a11y/click-events-have-key-events': 'off',
                'jsx-a11y/alt-text': 'off',
                'jsx-a11y/label-has-for': 'off',
                'jsx-a11y/label-has-associated-control': 'off',
                'jsx-a11y/tabindex-no-positive': 'off',
                'jsx-a11y/no-noninteractive-tabindex': 'off',
                'import/no-extraneous-dependencies': 'off',
                'react/sort-comp': 'warn',
                '@typescript-eslint/naming-convention': [
                    'error',
                    {
                        selector: 'variable',
                        format: ['camelCase', 'UPPER_CASE', 'PascalCase'],
                        filter: {
                            regex: '^_id|__collectionItem|__value$',
                            match: false,
                        },
                    },
                ],
                'react/no-access-state-in-setstate': 'error',
                'react/display-name': 'off',
                'default-case': 'warn',
                'react/no-children-prop': 'warn',
                'no-useless-escape': 'error',
                'jsx-a11y/no-noninteractive-element-interactions': 'warn',
                'jsx-a11y/anchor-is-valid': 'warn',
                'import/no-named-as-default': 'off',
                'new-cap': 'warn',
                'no-new': 'off',
                'max-params': ['warn', 5],
                'no-multi-assign': 'off',
                'global-require': 'off',
                'no-plusplus': 'off',
            },
        },
    ],
};
