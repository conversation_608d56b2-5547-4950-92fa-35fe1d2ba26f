import { Graph<PERSON><PERSON> } from '@sage/example-sales-api';
import { edgesSelector, Graph } from '@sage/xtrem-client';
import axios from 'axios';

const url = 'https://apidemo.sagex3.com/demo/service/X3CLOUDV2_SEED/graphql/';

const graph = new Graph<GraphApi>({
    fetcher: async query => (await axios.post(url, { query })).data,
});

async function queryOrders() {
    const salesOrders = graph.node('@sage/x3-sales/SalesOrder');
    const result = await salesOrders
        .query(
            edgesSelector(
                { id: true, orderDate: true, billToCustomer: { code: true } },
                { first: 5, orderBy: { orderDate: 1 } },
            ),
        )
        .execute();

    result.edges.map(edge => edge.node).forEach(node => console.log(node));
}

queryOrders().catch(err => console.error(err.stack));
