{"name": "@sage/xtrem-client-sales-order-query-example", "version": "37.0.14", "description": "Example of client API code", "license": "UNLICENSED", "author": "Sage", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/sales-order-query.js", "dependencies": {"@sage/example-sales-api": "^1.0.0", "@sage/xtrem-client": "workspace:*", "axios": "^1.8.4"}, "devDependencies": {"typescript": "~5.4.5"}, "scripts": {"build": "tsc -b .", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "reset": "rm -rf pnpm-lock.yaml node_modules build", "start": "node ."}}