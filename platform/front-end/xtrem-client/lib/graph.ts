import { camelCase } from 'lodash';
import { Config } from './config';
import { aggregateQueries } from './node-aggregate-queries';
import { nodeMutations } from './node-mutations';
import { nodeQueries } from './node-queries';
import type { Mutations } from './types';

function splitPath(nodeName: string) {
    // remove '@sage' scope
    return nodeName
        .replace(/^@\w*\//, '')
        .split('/')
        .map(camelCase);
}

export class Graph<ApiT> {
    constructor(private config: Config) {
        if (!config.fetcher) throw new Error('Invalid configuration: fetcher missing');
    }

    node<K extends keyof ApiT>(nodePath: K): ApiT[K] {
        const config = this.config;
        if (typeof nodePath !== 'string') throw new Error(`bad node key: ${typeof nodePath}`);
        const path = splitPath(nodePath);
        return {
            ...nodeQueries(config, path),
            ...aggregateQueries(config, path),
            ...nodeMutations(config, path),
        } as any;
    }
}

export class GraphMutation<ApiT> {
    constructor(private readonly config: Config) {
        if (!config.fetcher) throw new Error('Invalid configuration: fetcher missing');
    }

    node(nodePath: string): Pick<ApiT[keyof ApiT], Mutations & keyof ApiT[keyof ApiT]> {
        const config = this.config;
        if (typeof nodePath !== 'string') throw new Error(`bad node key: ${typeof nodePath}`);
        const path = splitPath(nodePath);

        return {
            ...nodeMutations(config, path),
        } as any;
    }
}
