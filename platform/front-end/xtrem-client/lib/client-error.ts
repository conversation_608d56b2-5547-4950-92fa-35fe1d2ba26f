/** Diagnose severities */
export enum ClientDiagnoseSeverity {
    test,
    info,
    warn,
    error,
    exception,
}

/** Interface for diagnoses returned in the `extensions` property of an error detail */
export interface ClientDiagnose {
    /** The severity of the diagnose */
    severity: ClientDiagnoseSeverity;

    /** The diagnose message */
    message: string;

    /** The path of the diagnose (in the node payload, not in the entire graphql request) */
    path: string[];
}

/**
 * Interface for error details contained in a client error
 *
 * `message`, `locations` and `path` are standard properties of GraphQL errors.
 * `extensions` contains the XTreeM diagnoses.
 */
export interface ErrorDetail {
    /** The error message */
    message: string;
    /** The locations in the graphql request */
    locations: { line: number; column: number }[];
    /** The path in the graphql request */
    path: string[];
    /** extension for diagnoses */
    extensions: {
        code: string;
        diagnoses: ClientDiagnose[];
    };
}

function formatError(error: ErrorDetail) {
    return `${error.path && error.path.join('.')}: ${error.message}`;
}
function formatErrors(errors: ErrorDetail[]) {
    return [`${errors.length} error(s):`, ...errors.map(formatError)].join('\n  ');
}

/**
 * Class for errors returned by the client library.
 *
 * this.message is a summary of the errors.
 * this.errors contains the full details, as an array of ErrorDetail objects
 *
 */
export class ClientError extends Error {
    constructor(readonly errors: ErrorDetail[]) {
        super(formatErrors(errors));
    }
}
