import { Config } from './config';
import { nodeIdOperation, nodeOperation, Operation } from './operations';
import { Edges, EdgesSelector, PagingOptions } from './paging';
import { Request } from './request';
import { fullSelector, OnlySelected, Selector } from './selector';

export type ReadOperation<OutputT> = Operation<string, OutputT>;

export type GetDuplicateOperation<OutputT> = Operation<string, OutputT>;

export type QueryOperation<T> = <SelectorT extends Selector<T>>(
    nodeSelector: EdgesSelector<SelectorT>,
    args?: PagingOptions<T>,
) => Request<Edges<OnlySelected<T, SelectorT>>>;

const queryOperation =
    <T>(config: Config, path: string[]) =>
    <SelectorT extends Selector<T>>(selector: SelectorT): Request<Edges<OnlySelected<T, SelectorT>>> => {
        // const full = queryOperationSelector(path, selector, false);
        const full = fullSelector([...path, 'query'], selector, false);
        return new Request(config, [...path, 'query'], '', full, false);
    };

export type GetDefaultsOperation<T> = <SelectorT extends Selector<T>>(
    nodeSelector: SelectorT,
    args?: { data?: object },
) => Request<OnlySelected<T, SelectorT>>;

/** @internal */
export const lookupsOperation =
    <T>(config: Config, path: string[], dataOrId: string | { data: Partial<T> }, propertyName: string) =>
    <SelectorT extends Selector<T>>(
        propertySelector: EdgesSelector<SelectorT>,
    ): Request<Edges<OnlySelected<T, SelectorT>>> => {
        const lookupsArgs = typeof dataOrId === 'string' ? { _id: dataOrId } : dataOrId;
        const full = fullSelector(path, { lookups: { __args: lookupsArgs, [propertyName]: propertySelector } }, false);
        return new Request(config, [...path, 'lookups'], propertyName, full, false);
    };

/** @internal */
export const nodeQueries = (config: Config, path: string[]) => {
    return {
        // built-in queries
        query: queryOperation(config, path) as any,
        read: nodeIdOperation<any, any>(config, path, 'read', false),
        getDuplicate: nodeIdOperation<any, any>(config, path, 'getDuplicate', false),
        getDefaults: nodeOperation<any, any>(config, path, 'getDefaults', false),
        lookups: (dataOrId: string | { data: {} }) => {
            return new Proxy(
                {},
                {
                    get(dummy, propertyName) {
                        if (typeof propertyName !== 'string')
                            throw new Error(`${path.join('/')}: bad proxy request: ${typeof propertyName}`);
                        return lookupsOperation(config, path, dataOrId, propertyName);
                    },
                },
            );
        },
        // custom queries
        queries: new Proxy(
            {},
            {
                get(dummy, name) {
                    if (typeof name !== 'string')
                        throw new Error(`${path.join('/')}: bad proxy request: ${typeof name}`);
                    return nodeOperation<any, any>(config, path, name, false);
                },
            },
        ),
    };
};
