import { EnumType } from 'json-to-graphql-query';
import { ClientCollection } from './client-node';
import { AggregateEdgesSelector, AggregateGroupSelector, AggregateValuesSelector } from './node-aggregate-queries';
import { EdgesSelector } from './paging';

// T with only keys enabled by SelectorT
export type OnlySelected<T, SelectorT> =
    T extends Array<infer U>
        ? OnlySelected<U, SelectorT>[]
        : T extends object
          ? {
                [K in keyof T & keyof SelectorT]: T[K] extends (infer U)[] | null
                    ? OnlySelected<U, SelectorT[K]>[]
                    : OnlySelected<T[K], SelectorT[K]>;
            }
          : T;

export type Selector<T> = T extends object
    ? {
          [K in keyof T]?: T[K] extends ClientCollection<infer C>
              ? CollectionSelector<C>
              : T[K] extends (infer U)[] | null | undefined
                ? Selector<U>
                : Selector<T[K]>;
      }
    : boolean; // should be true but then we get problem when ts promotes true to boolean

export type CollectionSelector<T, SelectorT = Selector<T>> = {
    query?: EdgesSelector<SelectorT>;
    queryAggregate?: AggregateEdgesSelector<T, AggregateGroupSelector<T>, AggregateValuesSelector<T>>;
    readAggregate?: AggregateValuesSelector<T>;
};

function wrap(path: string[], data: any, isMutation: boolean) {
    const p = [isMutation ? 'mutation' : 'query'].concat(path);
    return p.reduceRight((r, k) => ({ [k]: r }), data);
}

/** @internal */
export function fullSelector(path: string[], selector: any, isMutation: boolean, transform?: (child: any) => void) {
    const replace = (val: any): any => {
        if (typeof val !== 'object') return val;
        if (val == null) return val;
        if (Array.isArray(val)) return val.map(replace);
        const child = Object.keys(val).reduce((r, k) => {
            if (k === '_by') {
                // special case for group by selector.
                // prop: { _by: 'month' } must be converted to prop(by: 'month')
                r.__args = { by: new EnumType(val[k]) };
            } else {
                r[k] = replace(val[k]);
            }
            return r;
        }, {} as any);
        return transform ? transform(child) : child;
    };
    // replace using a wrapped clone of the original selector
    return replace(wrap(path, { ...selector }, isMutation));
}

/** @internal */
export function queryOperationSelector(path: string[], selector: any, isMutation: boolean) {
    return fullSelector(path, selector, isMutation, (child: any) => {
        // convert selector from { document { edges { } } } to { document { query { edges { } } } }
        if (child.edges && child.edges.node) {
            return { query: child };
        }
        return child;
    });
}

/** @internal */
export function queryAggregateSelector(path: string[], selector: any, isMutation: boolean) {
    return fullSelector(path, selector, isMutation, (child: any) => {
        // convert selector from { document { edges { } } } to { document { queryAggregate { edges { } } } }
        if (child.edges && child.edges.node) {
            return { queryAggregate: child };
        }
        return child;
    });
}
