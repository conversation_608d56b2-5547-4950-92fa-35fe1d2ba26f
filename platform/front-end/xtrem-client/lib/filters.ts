import { C<PERSON>Collection, ClientNode } from './client-node';

export type ExtendNumber<T> = T extends number | null ? T | string : T;
export type ExtendEnum<T> = T extends string ? (string extends T ? T : T | number) : T;

export type FilterValue<T> = ExtendEnum<ExtendNumber<T>>;

// We need these types below for array types (integerArray, enumArray, referenceArray, stringArray ... )
export type ExtendNumberArray<T> = T extends number[] | null ? T | string[] : T;
export type ExtendEnumArray<T> = T extends string[] ? (string[] extends T ? T : T | number[]) : T;

export type FilterValueArray<T> = ExtendEnumArray<ExtendNumberArray<T>>;

// We use a custom rule for enums in arrays.
// Type matching fails when the array contains several valid string literals because TypeScript narrows the type
// on individual array elements instead of narrowing globally over all array entries.
// To solve it we use `(string | number)[]` instead of `(V | number)[]`.
// This is not ideal because it is too permissive but this is the best we can do for now.
export type ExtendEnumInArray<T> = T extends string ? (string extends T ? T : string | number) : T;
export type FilterArrayValue<T> = ExtendEnumInArray<ExtendNumber<T>>[];

// We need these types below for array types (integerArray, enumArray, referenceArray, stringArray, ... )
export type ExtendEnumArrayInArray<T> = T extends string[] ? (string[] extends T ? T : string[] | number[]) : T;
export type FilterArrayOfArrayValue<T> = ExtendEnumArrayInArray<ExtendNumberArray<T>>[];

export interface FilterOperatorEquality<V> {
    _eq?: FilterValue<V> | null;
    _ne?: FilterValue<V> | null;
    _in?: FilterArrayValue<V>;
    _nin?: FilterArrayValue<V>;
}

export interface FilterOperatorInclusion<V> {
    _contains?: FilterValue<V>;
    _containsRange?: FilterValue<V>;
    _containedBy?: FilterValue<V>;
    start?: FilterOperatorEquality<V> | FilterOperatorComparable<V>;
    end?: FilterOperatorEquality<V> | FilterOperatorComparable<V>;
}

export interface FilterOperatorArray<V> {
    _eq?: FilterValueArray<V> | null;
    _ne?: FilterValueArray<V> | null;
    _in?: FilterArrayOfArrayValue<V>;
    _nin?: FilterArrayOfArrayValue<V>;
    _contains?: FilterValue<V> extends (infer ElementT)[] ? FilterValue<ElementT> : never;
}

export interface FilterOperatorComparable<V> extends FilterOperatorEquality<V> {
    _gt?: FilterValue<V>;
    _gte?: FilterValue<V>;
    _lt?: FilterValue<V>;
    _lte?: FilterValue<V>;
}

export type FilterOperator<V> = V extends string
    ? (FilterOperatorComparable<V> | FilterOperatorInclusion<V>) & {
          _mod?: never;
          _regex?: string;
          _options?: string;
      }
    : V extends number
      ? FilterOperatorComparable<V> & { _mod?: number[]; _regex?: string; _options?: string }
      : V extends any[]
        ? FilterOperatorArray<V>
        : FilterOperatorEquality<V>;

export interface Quantifiers {
    _atLeast?: number;
    _atMost?: number;
    _every?: true;
    _none?: true;
}

export interface Fn {
    _fn?: string;
}

export interface Logical<T extends ClientNode> {
    _and?: Filter<T>[];
    _or?: Filter<T>[];
    _not?: Filter<T>;
}

// Date and time properties are exposed as strings in api.d.ts files.
// We cannot distinguish them from other strings so we add this MmeberFilters type to all string properties.
export interface MemberFilters {
    value?: FilterValue<number>;
    epoch?: FilterValue<number>;
    year?: FilterValue<number>;
    month?: FilterValue<number>;
    day?: FilterValue<number>;
    week?: FilterValue<number>;
    weekDay?: FilterValue<number>;
    yearDay?: FilterValue<number>;
}

export type NodeFilter<T extends ClientNode> = {
    [K in keyof T]?: T[K] extends ClientCollection<infer ElementT>
        ? Logical<T> & Filter<ElementT> & Quantifiers & Fn
        : T[K] extends ClientNode
          ? Filter<T[K]> | string // assumes key is string
          : Filter<T[K]> | MemberFilters;
};

export type Filter<T> = T extends ClientNode ? Logical<T> & NodeFilter<T> & Fn : FilterValue<T> | FilterOperator<T>;
