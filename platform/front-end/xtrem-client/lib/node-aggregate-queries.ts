import { Config } from './config';
import { Filter } from './filters';
import { convertPagingOptions, Edges, PagingOptions, PagingOptionsSelector } from './paging';
import { Request } from './request';
import { fullSelector, OnlySelected } from './selector';
import { integer } from './types';

export type AggregateValuesSelector<T> = T extends object
    ? { [K in keyof T]?: T[K] extends (infer U)[] ? AggregateValuesSelector<U> : AggregateValuesSelector<T[K]> }
    : {
          min?: boolean;
          max?: boolean;
          sum?: boolean;
          avg?: boolean;
          distinctCount?: boolean;
      };

export type AggregateGroupSelector<T> = T extends object
    ? { [K in keyof T]?: T[K] extends (infer U)[] ? AggregateGroupSelector<U> : AggregateGroupSelector<T[K]> }
    : {
          _by: 'value' | 'day' | 'month' | 'year';
      };

export interface AggregateReadOptions<T> {
    filter?: Filter<T extends infer V ? V : never>;
}

export type AggregateQueryOptions<T> = PagingOptions<T>;

export type AggregateReadOperation<T> = <SelectorT extends AggregateValuesSelector<T>>(
    selector: SelectorT,
    args?: AggregateReadOptions<T>,
) => Request<OnlySelected<AggregateResultValues<T>, SelectorT>>;

// For now we allow all aggregation functions for all data types because
// client side date and decimal types are just string.
// This will be improved later.
export type AggregatePropertyValues<T> = {
    min: T;
    max: T;
    avg: T;
    sum: T;
    distinctCount: integer;
};

export type AggregateResultValues<T> = T extends object
    ? { [K in keyof T]?: T[K] extends (infer U)[] ? AggregateResultValues<U> : AggregateResultValues<T[K]> }
    : AggregatePropertyValues<T>;

// eslint-disable-next-line @typescript-eslint/comma-dangle
const nodeAggregateReadOperation =
    <ArgT, T>(config: Config, path: string[]) =>
    <SelectorT extends AggregateValuesSelector<T>>(
        selector: SelectorT,
        args: AggregateReadOptions<ArgT>,
    ): Request<OnlySelected<AggregateResultValues<T>, SelectorT>> => {
        const full = fullSelector(
            [...path, 'readAggregate'],
            {
                __args: { filter: JSON.stringify((args && args.filter) || {}) },
                ...selector,
            },
            false,
        );
        return new Request(config, [...path, 'readAggregate'], '', full, false);
    };

// eslint-disable-next-line @typescript-eslint/comma-dangle
export interface AggregateQuerySelector<
    T,
    GroupT extends AggregateGroupSelector<T> = AggregateGroupSelector<T>,
    ValuesT extends AggregateValuesSelector<T> = AggregateValuesSelector<T>,
> {
    group: GroupT;
    values: ValuesT;
}

export type AggregateEdges<T> = Edges<{ group: T; values: AggregateResultValues<T> }>;

// eslint-disable-next-line @typescript-eslint/comma-dangle
export interface AggregateEdgesSelector<
    T,
    GroupT extends AggregateGroupSelector<T> = AggregateGroupSelector<T>,
    ValuesT extends AggregateValuesSelector<T> = AggregateValuesSelector<T>,
> {
    __args?: PagingOptionsSelector;
    edges: { node: { group: GroupT; values?: ValuesT }; cursor?: boolean };
    pageInfo?: { hasNextPage?: boolean; endCursor?: boolean; hasPreviousPage?: boolean; startCursor?: boolean };
    totalCount?: boolean;
}

// eslint-disable-next-line @typescript-eslint/comma-dangle
export function aggregateEdgesSelector<
    T,
    GroupT extends AggregateGroupSelector<T> = AggregateGroupSelector<T>,
    ValuesT extends AggregateValuesSelector<T> = AggregateValuesSelector<T>,
>(
    selector: AggregateQuerySelector<T, GroupT, ValuesT>,
    args?: AggregateQueryOptions<T>,
): AggregateEdgesSelector<T, GroupT, ValuesT> {
    return {
        __args: convertPagingOptions(args || {}),
        edges: { node: selector, cursor: true },
        pageInfo: { hasNextPage: true, endCursor: true, hasPreviousPage: true, startCursor: true },
    };
}

// eslint-disable-next-line @typescript-eslint/comma-dangle
export type AggregateQueryOperation<T> = <
    GroupT extends AggregateGroupSelector<T>,
    ValuesT extends AggregateValuesSelector<T>,
>(
    selector: AggregateEdgesSelector<T, GroupT, ValuesT>,
    args?: AggregateQueryOptions<T>,
) => Request<Edges<{ group: OnlySelected<T, GroupT>; values: OnlySelected<AggregateResultValues<T>, ValuesT> }>>;

// eslint-disable-next-line @typescript-eslint/comma-dangle
const nodeAggregateQueryOperation =
    <T>(config: Config, path: string[]) =>
    <GroupT extends AggregateGroupSelector<T>, ValuesT extends AggregateValuesSelector<T>>(
        selector: AggregateEdgesSelector<T, GroupT, ValuesT>,
    ): Request<Edges<{ group: OnlySelected<T, GroupT>; values: OnlySelected<T, ValuesT> }>> => {
        const full = fullSelector([...path, 'queryAggregate'], selector, false);
        return new Request(config, [...path, 'queryAggregate'], '', full, false);
    };

/** @internal */
export const aggregateQueries = (config: Config, path: string[]) => {
    return {
        aggregate: {
            read: nodeAggregateReadOperation<any, any>(config, path),
            query: nodeAggregateQueryOperation(config, path),
        },
    };
};
