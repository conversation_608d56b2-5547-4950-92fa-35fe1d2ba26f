import { Config } from './config';
import {
    nodeIdOperation,
    nodeOperation,
    Operation,
    runToCompletionOperation,
    scalarIdOperation,
    scalarOperation,
} from './operations';
import { Request } from './request';

export type CreateOperation<InputT, OutputT> = Operation<{ data: InputT }, OutputT>;
export type DuplicateOperation<InputT, OutputT> = Operation<{ _id: string; data?: InputT }, OutputT>;
export type UpdateOperation<InputT, OutputT> = Operation<{ data: InputT }, OutputT>;
export type UpdateByIdOperation<InputT, OutputT> = Operation<{ _id: string; data: InputT }, OutputT>;
export type DeleteOperation<KeyT> = (args: KeyT) => Request<number>;

/** @internal */
export const nodeMutations = (config: Config, path: string[]) => {
    return {
        // built-in mutations
        create: nodeOperation<any, any>(config, path, 'create', true),
        duplicate: nodeOperation<any, any>(config, path, 'duplicate', true),
        update: nodeOperation<any, any>(config, path, 'update', true),
        updateById: nodeIdOperation<any, any>(config, path, 'updateById', true),
        delete: scalarOperation<any, any>(config, path, 'delete', true),
        deleteById: scalarIdOperation<any>(config, path, 'deleteById', true),
        // custom mutations
        mutations: new Proxy({} as any, {
            get(_dummy, name) {
                if (typeof name !== 'string') throw new Error(`${path.join('/')}: bad proxy request: ${typeof name}`);
                return nodeOperation<any, any>(config, path, name, true);
            },
        }),
        asyncOperations: new Proxy({} as any, {
            get(dummy, name) {
                if (typeof name !== 'string') throw new Error(`${path.join('/')}: bad proxy request: ${typeof name}`);
                return {
                    runToCompletion: runToCompletionOperation(config, [...path, name]),
                    start: nodeOperation<any, any>(config, [...path, name], 'start', true),
                    stop: nodeOperation<any, any>(config, [...path, name], 'stop', true),
                    track: nodeOperation<any, any>(config, [...path, name], 'track', false),
                    requestUserNotification: nodeOperation<any, any>(config, [...path, name], 'requestUserNotification', true),
                };
            },
        }),
    };
};
