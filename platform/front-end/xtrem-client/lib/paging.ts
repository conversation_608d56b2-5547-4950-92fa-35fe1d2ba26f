import { Filter } from './filters';
import { OrderBy } from './order-by';
import { OnlySelected, Selector } from './selector';

// Array<{edges: { node: T }}> => T[]
export type WithoutEdges<T> = {
    [K in keyof T]: T[K] extends object
        ? T[K] extends EdgesMatcher<infer U>
            ? WithoutEdges<U>[]
            : WithoutEdges<T[K]>
        : T[K];
};

/**
 * {
 *     query: Array<{edges: { node: T }}>;
 *     readAggregate: AggregateResultValues<T>;     =>      T[]
 *     queryAggregate: AggregateEdges<T>;
 * }
 */
export type ExtractEdges<T> = {
    [K in keyof T]: T[K] extends object
        ? T[K] extends EdgesMatcher<infer U> | ClientCollectionMatcher<infer U>
            ? ExtractEdges<U>[]
            : ExtractEdges<T[K]>
        : T[K];
};

// Same as "ExtractEdges<T>" only with all properties optional
export type ExtractEdgesPartial<T> = {
    [K in keyof T]?: T[K] extends object
        ? T[K] extends EdgesMatcher<infer U> | ClientCollectionMatcher<infer U>
            ? ExtractEdgesPartial<U>[]
            : ExtractEdgesPartial<T[K]>
        : T[K];
};

export interface Query<T> {
    query: Edges<T>;
}

// helper to infer T from a partial Edges<T>
export interface EdgesMatcher<T> {
    edges: { node: T }[];
}
// helper to infer T from a partial ClientCollection<T>
export interface ClientCollectionMatcher<T> {
    query: EdgesMatcher<T>;
}

export interface BinaryStreamMatcher {
    value: string;
}

export interface Edges<T> {
    edges: { node: T; cursor: string | null }[];
    pageInfo: { hasNextPage: boolean; endCursor: string | null; hasPreviousPage: boolean; startCursor: string | null };
    totalCount?: number;
}

export interface PagingOptions<T> {
    first?: number;
    after?: string;
    last?: number;
    before?: string;
    orderBy?: OrderBy<T extends infer U ? U : never>;
    filter?: Filter<T extends infer V ? V : never>;
}

export interface PagingOptionsSelector {
    first?: number;
    after?: string;
    last?: number;
    before?: string;
    orderBy?: string;
    filter?: string;
}

export type WithoutSelectedEdges<T, SelectorT extends Selector<T>> = WithoutEdges<OnlySelected<T, SelectorT>>;

/**
 * Recursively transforms "edges":
 *
 * Array<{edges: { node: T }}> => T[]
 *
 * "query", "queryAggregate" & "readAggregate" are PRESERVED.
 *
 * @export
 * @template T
 * @param {Edges<T>} edges
 * @returns {WithoutEdges<T>[]}
 */
export function withoutEdges<T>(edges: Edges<T>): WithoutEdges<T>[] {
    const transform = (val: any): any => {
        if (!val || typeof val !== 'object') return val;
        // if (val.query) return transform(val.query);
        if (Array.isArray(val)) return val.map(transform);
        if (Array.isArray(val.edges)) return val.edges.map((v: any) => transform(v.node));
        return Object.keys(val).reduce((r, k) => {
            r[k] = transform(val[k]);
            return r;
        }, {} as any);
    };
    return transform(edges);
}

/**
 * Recursively extract "edges" from queries:
 *
 * * {
 *     query: Array<{edges: { node: T }}>;
 *     readAggregate: AggregateResultValues<T>;     =>      T[]
 *     queryAggregate: AggregateEdges<T>;
 * }
 *
 * "query", "queryAggregate" & "readAggregate" are REMOVED.
 * Use "withoutEdges" if you want to keep them.
 * @export
 * @template T
 * @param {Edges<T>} edges
 * @returns {ExtractEdges<T>[]}
 */
export function extractEdges<T>(edges: Edges<T>): ExtractEdges<T>[] {
    const transform = (val: any): any => {
        if (!val || typeof val !== 'object') return val;
        if (val.query) return transform(val.query);
        if (Array.isArray(val)) return val.map(transform);
        if (Array.isArray(val.edges)) return val.edges.map((v: any) => transform(v.node));
        return Object.keys(val).reduce((r, k) => {
            r[k] = transform(val[k]);
            return r;
        }, {} as any);
    };
    return transform(edges);
}

/** @internal */
export function convertPagingOptions(args: PagingOptions<any>) {
    const converted = {} as PagingOptionsSelector;
    if (args.first) converted.first = args.first;
    if (args.after) converted.after = args.after;

    if (args.last) converted.last = args.last;
    if (args.before) converted.before = args.before;
    if (args.filter) converted.filter = JSON.stringify(args.filter);
    if (args.orderBy) converted.orderBy = JSON.stringify(args.orderBy);

    return Object.keys(converted).length > 0 ? converted : undefined;
}

export interface EdgesSelector<SelectorT> {
    __args?: PagingOptionsSelector;
    edges: { node: SelectorT; cursor?: boolean };
    pageInfo?: { hasNextPage?: boolean; endCursor?: boolean; hasPreviousPage?: boolean; startCursor?: boolean };
    totalCount?: boolean;
}

export function edgesSelector<T, SelectorT = Selector<T>>(
    selector: SelectorT,
    args?: PagingOptions<T>,
): EdgesSelector<SelectorT> {
    return {
        __args: convertPagingOptions(args || {}),
        edges: { node: selector, cursor: true },
        pageInfo: { hasNextPage: true, endCursor: true, hasPreviousPage: true, startCursor: true },
    };
}

export interface QuerySelector<T, SelectorT = Selector<T>> {
    query: EdgesSelector<SelectorT>;
}

export function querySelector<T, SelectorT = Selector<T>>(
    selector: SelectorT,
    args?: PagingOptions<T>,
): QuerySelector<T, SelectorT> {
    return {
        query: {
            __args: convertPagingOptions(args || {}),
            edges: { node: selector, cursor: true },
            pageInfo: { hasNextPage: true, endCursor: true, hasPreviousPage: true, startCursor: true },
        },
    };
}
