/* eslint-disable @typescript-eslint/naming-convention */
export type integer = number;
export type short = number;
export type decimal = number;

export type Mutations =
    | 'create'
    | 'update'
    | 'updateById'
    | 'delete'
    | 'deleteById'
    | 'mutations'
    | 'duplicate'
    | 'asyncOperations';

export class TextStream {
    value: string;
}

export class BinaryStream {
    value: string; // base64 encoded
}
