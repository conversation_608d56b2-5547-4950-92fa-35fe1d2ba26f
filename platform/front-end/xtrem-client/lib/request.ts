import { jsonToGraphQLQuery } from 'json-to-graphql-query';
import { ClientError } from './client-error';
import { Config } from './config';

function unwrapPath(path: string[], data: any) {
    return path.reduce((r, k) => r[k], data);
}

function unwrapResult(obj: any): any {
    if (!obj || typeof obj !== 'object') return obj;
    if (Array.isArray(obj)) return obj.map(unwrapResult);
    return Object.keys(obj).reduce((r, k) => {
        r[k] = unwrapResult(obj[k]);
        return r;
    }, {} as any);
}

function unwrap(path: string[], data: any) {
    if (!data.data) throw new Error(`request failed: ${JSON.stringify(data.errors)}`);
    return unwrapPath(path, data.data);
}

export class Request<ResultT> {
    constructor(
        public config: Config,
        public path: string[],
        public name: string,
        public selector: any,
        public isMutation: boolean,
    ) { }

    async execute(): Promise<ResultT> {
        const query = jsonToGraphQLQuery(this.selector, { pretty: true });
        if (this.config.log) this.config.log('GRAPHQL REQUEST', query);
        const result = await this.config.fetcher(query, this.isMutation, this.selector);
        if (result.errors) throw new ClientError(result.errors);
        if (this.config.log) this.config.log('GRAPHQL RESULT', JSON.stringify(result, null, 2));
        const unwrapped = unwrap(this.path, unwrapResult(result));
        return this.name ? unwrapped[this.name] : unwrapped;
    }

    /** @internal */
    unwrapBatchResult(data: any) {
        const unwrapped = unwrapPath(this.path.slice(1), data);
        return this.name ? unwrapped[this.name] : unwrapped;
    }
}
