import { AggregateEdges, AggregateResultValues } from './node-aggregate-queries';
import { Edges } from './paging';
import { integer } from './types';
import { Dict } from './utils';

export interface Node {
    queries: Dict<any>;
    mutations: Dict<any>;
}

export class ClientNode {
    _id: string;

    _createStamp?: string;

    _updateStamp?: string;

    _updateTick?: integer;

    _customData?: string;

    /**
     * access restrictions on node
     */
    _access?: { name: string; status: string }[];

    /**
     * entity tag used for opmitistic concurreny
     */
    _etag?: string;
}

export class VitalClientNode extends ClientNode {
    _sortValue: integer;
}

export interface ClientNodeInput {
    _id?: string;
}

export type UpdateAction = 'create' | 'update' | 'delete';

export interface VitalClientNodeInput extends ClientNodeInput {
    _sortValue?: integer;
    _action?: UpdateAction;
}

export type InputData<T> = {
    [K in keyof T]?: T[K] extends Edges<infer U>
        ? InputData<U>[]
        : T[K] extends object
          ? InputData<T[K]> | string
          : T[K];
};

export type OutputData<T> = { [K in keyof T]: T[K] };

// TODO: improve KeyData
export type KeyData<T> = InputData<T>;

export interface ClientCollection<T> {
    query: Edges<T>;
    readAggregate: AggregateResultValues<T>;
    queryAggregate: AggregateEdges<T>;
}
