import { Config } from './config';
import { Request } from './request';
import { fullSelector, OnlySelected, Selector } from './selector';

export type Operation<ArgT, T> = <SelectorT extends Selector<T extends (infer U)[] ? U : T>>(
    selector: SelectorT,
    args: ArgT,
) => Request<OnlySelected<T, SelectorT>>;

export type RunToCompletionOperation<ArgT, T> = <SelectorT extends Selector<T extends (infer U)[] ? U : T>>(
    selector: SelectorT,
    args: ArgT,
    options?: { callback?: (arg: { pollCount: number; elapsedMillis: number; status: string }) => boolean },
) => Request<OnlySelected<T, SelectorT>>;

/**
 * Represents an asynchronous operation with methods to start, track, stop, and run the operation to completion.
 *
 * @template ArgT - The type of the arguments passed to the operation.
 * @template T - The type of the result returned by the operation.
 *
 * @property {Operation<ArgT, { trackingId: string }>} start - Initiates the operation and returns a tracking ID.
 * @property {Operation<{ trackingId: string }, T>} track - Tracks the progress of the operation using a tracking ID.
 * @property {Operation<{ trackingId: string; reason?: string }, boolean>} stop - Stops the operation, optionally providing a reason.
 * @property {Operation<ArgT, T>} runToCompletion - Runs the operation to completion and returns the final result.
 */

export interface AsyncTrackReturn<T> {
    status: string;
    result: T;
    errorMessage: string;
    logMessages: {
        level: string;
        message: string;
    }[];
}

export interface AsyncOperation<ArgT, T> {
    start: Operation<ArgT, { trackingId: string }>;
    track: Operation<{ trackingId: string }, AsyncTrackReturn<T>>;
    stop: Operation<{ trackingId: string; reason?: string }, boolean>;
    requestUserNotification: Operation<{ trackingId: string }, boolean>;
    runToCompletion: RunToCompletionOperation<ArgT, T>;
}

/** Extract the type of the result of the operation */
export type OperationResultType<Op> = Op extends Operation<any, infer T> ? T : never;

/** Extract the type of the result of the operation */
export type OperationParamType<Op> = Op extends Operation<infer T, any> ? T : never;

export type ExtractArray<T> = T extends Array<infer U> ? U : T;

/** @internal */
export const nodeOperation = <ArgT, T>(config: Config, path: string[], name: string, isMutation: boolean) => {
    return <SelectorT extends Selector<T>>(selector: SelectorT, args: ArgT): Request<OnlySelected<T, SelectorT>> => {
        const full = fullSelector(
            path,
            { [name]: args && Object.keys(args).length ? { __args: args, ...(selector as {}) } : (selector as {}) },
            isMutation,
        );
        return new Request(config, path, name, full, isMutation);
    };
};

/** @internal */
export const nodeIdOperation =
    <ArgT, T>(config: Config, path: string[], name: string, isMutation: boolean) =>
        <SelectorT extends Selector<T>>(
            selector: SelectorT,
            args: { _id: string; data?: ArgT } | string,
        ): Request<OnlySelected<T, SelectorT>> => {
            const argsObj = typeof args === 'string' ? { _id: args } : args;
            const full = fullSelector(path, { [name]: { __args: argsObj, ...(selector as {}) } }, isMutation);
            return new Request(config, path, name, full, isMutation);
        };

/** @internal */
export const scalarOperation =
    <ArgT, ResultT>(config: Config, path: string[], name: string, isMutation: boolean) =>
        (args: ArgT): Request<ResultT> => {
            const full = fullSelector(path, { [name]: { __args: args } }, isMutation);
            return new Request(config, path, name, full, isMutation);
        };

/** @internal */
export const scalarIdOperation =
    <ResultT>(config: Config, path: string[], name: string, isMutation: boolean) =>
        (args: string): Request<ResultT> => {
            const full = fullSelector(path, { [name]: { __args: { _id: args } } }, isMutation);
            return new Request(config, path, name, full, isMutation);
        };

// Defined in xtrem-communication
export type AsyncMutationStatus = 'pending' | 'running' | 'success' | 'error';

export interface PollingOptions {
    callback?(arg: { pollCount: number; elapsedMillis: number; status: AsyncMutationStatus }): boolean;
}

export interface AsyncMutationResponse<T> {
    status: AsyncMutationStatus;
    result: T;
    errorMessage?: string;
}

const sleepMillis = (millis: number) =>
    new Promise(resolve => {
        setTimeout(resolve, millis);
    });

export const runToCompletionOperation = <ArgT, T>(config: Config, path: string[]) => {
    return <SelectorT extends Selector<T>>(
        selector: SelectorT,
        args: ArgT,
        options?: PollingOptions,
    ): { execute: () => Promise<OnlySelected<T, SelectorT>> } => {
        const startMutation = nodeOperation<ArgT, { trackingId: string }>(config, path, 'start', true);
        const trackMutation = nodeOperation<{ trackingId: string }, AsyncMutationResponse<OnlySelected<T, SelectorT>>>(
            config,
            path,
            'track',
            false,
        );
        return {
            async execute() {
                const trackingId = await startMutation({ trackingId: true }, args).execute();
                let running = true;
                const startMillis = Date.now();
                let pollCount = 1;
                while (running) {
                    const response = await trackMutation(
                        { status: true, result: selector as any, errorMessage: true },
                        trackingId,
                    ).execute();
                    if (options?.callback)
                        running = options.callback({
                            pollCount,
                            elapsedMillis: Date.now() - startMillis,
                            status: response.status,
                        });
                    switch (response.status) {
                        case 'pending':
                        case 'running':
                            break;
                        case 'success':
                            return response.result as OnlySelected<T, SelectorT>;
                        case 'error':
                            throw new Error(response.errorMessage || '<unknown error>');
                        default:
                            throw new Error(`invalid status: ${response.status}`);
                    }

                    // Ramp up sleep time from 100 to 1000 and then remain at 1000.
                    await sleepMillis(Math.min(pollCount * 100, 1000));
                    pollCount += 1;
                }
                throw new Error('polling cancelled');
            },
        };
    };
};
