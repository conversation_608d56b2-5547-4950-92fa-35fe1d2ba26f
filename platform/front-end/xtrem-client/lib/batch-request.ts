import { jsonToGraphQLQuery } from 'json-to-graphql-query';
import { Config } from './config';
import { Request } from './request';
import { Dict } from './utils';

export type BatchResult<T extends Dict<Request<any>>> = {
    [K in keyof T]: T[K] extends Request<infer ResultT> ? ResultT : never;
};

export class BatchRequest<T extends Dict<Request<any>>> {
    private config: Config;

    constructor(private requests: T) {
        const keys = Object.keys(this.requests);
        if (keys.length > 0) this.config = this.requests[keys[0]].config;
        if (
            keys.length > 1 &&
            keys.slice(1).some(k => this.requests[k].isMutation !== this.requests[keys[0]].isMutation)
        ) {
            throw new Error('invalid batch request: cannot mix queries and mutations in the same request');
        }
    }

    /** @internal */
    private isEmpty() {
        return Object.keys(this.requests).length === 0;
    }

    /** @internal */
    private mergeSelectors() {
        return Object.keys(this.requests).reduce((r, k) => {
            const request = this.requests[k];
            const typeKey = request.isMutation ? 'mutation' : 'query';
            r[typeKey] = r[typeKey] || {};
            const subKey = Object.keys(request.selector[typeKey])[0];
            r[typeKey][k] = {
                ...request.selector[typeKey][subKey],
                __aliasFor: subKey,
            };
            return r;
        }, {} as Dict<any>);
    }

    /** @internal */
    private unwrapResult(results: any) {
        return Object.keys(this.requests).reduce((r, k) => {
            const request = this.requests[k];
            const result = results.data[k];
            r[k] = request.unwrapBatchResult(result);
            return r;
        }, {} as Dict<any>);
    }

    async execute(): Promise<BatchResult<T>> {
        if (this.isEmpty()) return {} as any;
        const selector = this.mergeSelectors();
        const query = jsonToGraphQLQuery(selector, { pretty: true });
        if (this.config.log) this.config.log('BATCH QUERY', query);
        const results = await this.config.fetcher(query, !!selector.mutation, selector);
        if (this.config.log) this.config.log('BATCH RESULT', results);
        return this.unwrapResult(results) as any;
    }
}
