PATH: XTREEM/Client+Framework/GraphQL+Client+Library

# GraphQL Client Library

## What is it?

The GraphQL client library is a helper library for developers who call xtrem GraphQL APIs from TypeScript. Main features are:

-   Simple TypeScript API to execute queries and mutations
-   Strongly typed API. The requests are composed from typed object literals rather than raw text. The responses are also statically typed.
-   Generic library for all xtrem GraphQL APIs (patent pending). The typing information is provided by typescript definition files. No need to generate and compile typescript code for every service, just provide a .d.ts file.
-   Full support for all standard queries and mutations, including aggregate queries, custom queries and mutations.
-   Batch requests.
-   Usable both browser-side and server-side (node.js).

## Basic query

```ts
// Import library definitions
import { edgesSelector, Graph, withoutEdges } from '@sage/xtrem-client';
// Import type definitions for the service we are calling, here @sage/x3-sales
import { GraphApi } from '@sage/example-sales-api';
// Import the http client library - axios works both client side and server side.
import axios from 'axios';

// The service URL
const serviceUrl = 'https://apidemo.sagex3.com/api/';

// Instantiate `graph`, our proxy to the service
const graph = new Graph<GraphApi>({
    fetcher: async query => (await axios.post(serviceUrl, { query })).data,
});

// Our example function which executes a query on the SalesOrder node
async function queryOrders() {
    // We obtain a handle to a proxy for the SalesOrder node API
    const salesOrderProxy = graph.node('@sage/x3-sales/SalesOrder');
    // Now we can execute a query
    const result = await salesOrderProxy
        .query(
            edgesSelector(
                // The properties that we want to select
                { id: true, orderDate: true, customer: { code: true } },
                // Paging, filtering, sorting options
                { first: 5, orderBy: { orderDate: 1 } },
            ),
        )
        .execute();

    // Iterate though the results and log them to the console.
    withoutEdges(result).forEach(order => {
        console.log(`order id=${order.id} date=${order.orderDate}, customer=${order.customer.code}`);
    });
}

// Execute our query and catch eventual errors.
queryOrders().catch(err => console.error(err.stack));
```

## Creating the API proxy

To access an XTREM graphql API you have to get a proxy for the entire graph:

```ts
const graph = new Graph<GraphApi>({
    fetcher: async query => (await axios.post(serviceUrl, { query })).data,
});
```

The API proxy is an instance of the Graph class.

The `GraphAPI` type parameter provides the static typing information to the Graph proxy.
`GraphAPI` is a TypeScript `interface` which gives access to all the interface definitions of the GraphQL schema.

The `fetcher` is the function that sends the request to the service.
In this example we use axios but you can replace it with your favorite http client library and configure options like proxy settings.

## Getting a node API proxy

Xtrem GraphQL APIs are structured around _nodes_.
Once you have a graph proxy you can obtain a specific node API proxy with:

```ts
const salesOrder = graph.node('@sage/x3-sales/SalesOrder');
```

Note: the node name (`@sage/x3-sales/SalesOrder`) is case sensitive.

## Node API

The `salesOrder` node proxy gives you access to the queries and mutations available on sales orders:

Queries:

-   `salesOrder.query(edgesSelector(selector, options))`: query nodes with the standard GraphQL pagination protocol.
-   `salesOrder.read(selector, _id)`: read a single node, given its \_id returned by a previous query.
-   `salesOrder.aggregate.query(aggregateEdgesSelector(selector, options))`: execute an aggregation _group by_ query. The result is paged.
-   `salesOrder.aggregate.read(selector)`: execute a global aggregation query, without any _group by_.
-   `salesOrder.queries.myQuery(args)`: execute a custom query exposed by the node.

Mutations:

-   `salesOrder.create(selector, { data })`: create a node. The payload is passed in the data member.
-   `salesOrder.update(selector, { data })`: update a node. The payload is passed in the data member.
-   `salesOrder.updateById(selector, { _id, data })`: update a node, with its `_id` returned by a previous request.
-   `salesOrder.delete(selector, key)`: delete a node. The key is an object literal.
-   `salesOrder.deleteById(selector, _id)`: delete a node, with its `_id` returned by a previous request.
-   `salesOrder.mutations.myMutation(args)`: execute a custom mutation exposed by the node.

This is the _maximal_ API that a node _may_ expose. The _actual_ API of a given node will be a subset which depends on the node flags (`canRead`, `canUpdate`, `canDelete`) and on whether or not the node publishes custom queries or mutations.

All these methods return a `Request` instance. You can execute these requests individually, with `request.execute()`, or batch them.

## Node query

-   `salesOrder.query(edgesSelector(selector, options))`: query nodes with the standard GraphQL pagination protocol

Xtrem queries use the standard _connection_ (paging) protocol of graphql, with edges, node and cursor elements. See https://graphql.org/learn/pagination/.

The `edgesSelector` utility generates the standard `{ edges { node { ... }, cursor { ... } }, pageInfo { ... } }` _connection_ wrapper. It takes two parameters:

-   `selector`: an object that defines which properties will be returned by the query. The properties may be properties of the node itself, properties in the nodes that it references (`customer` in the example below), or properties of one of its collections.
-   `options`: the paging, filtering and sorting options for the query.

The query returns a `Request` object. You will usually call the `execute()` method of this request to get the results but you may also create a batch with several requests and send them together to the service. See batching below.

The result is an `{ edges: [{ node: { ... }, cursor: ... }, ...], pageInfo { ... } }` wrapper around the data that you selected. You can eliminate the wrapper and get the raw data with the `withoutEdges(result)` utility.

Here is an example:

```ts
const result = await salesOrder
    .query(
        // Add the connection wrapper
        edgesSelector(
            // The properties that we are selecting
            { id: true, orderDate: true, customer: { code: true } },
            // The size of the page and the sort order.
            { first: 5, orderBy: { orderDate: 1 } },
        ),
    )
    .execute();

// Remove the connection wrapper and iterate though the results.
withoutEdges(result).forEach(order => {
    console.log(`order id=${order.id} date=${order.orderDate}, customer=${order.customer.code}`);
});
```

`withoutEdges(result)` returns the nodes data as an array of objects that only contain the properties that we selected.
The paging information is available through `result.pageInfo`.

## Query Filters

You can filter a query with a `filter` option. For example:

```ts
const result = await salesOrder
    .query(
        edgesSelector(
            { id: true, orderDate: true, customer: { code: true } },
            // The size of the page and the sort order.
            {
                filter: {
                    orderDate: { _gte: '2020-03-01', _lte: '2020-03-31' },
                    customer: { code: { _nin: ['C0001', 'C0002'] } },
                },
            },
        ),
    )
    .execute();
```

This query will return the sales orders with an `orderDate` in March 2020 and a `customer.code` different from C0001 and C0002.

As the example shows, the filter option may combine a filter on a property of the primary node (`orderDate: { _gte: '2020-03-01', _lte: '2020-03-31' }`) and a filter on a property of another node referenced by one of its properties (`customer: { code: { _nin: ['C0001', 'C0002'] } }`).

The following operators may be used:

| Operator  | Description                                           |
| --------- | ----------------------------------------------------- |
| \_eq      | equal to                                              |
| \_ne      | not equal to                                          |
| \_lt      | less than                                             |
| \_lte     | less than or equal                                    |
| \_gt      | greater than                                          |
| \_gte     | less than or equal                                    |
| \_in      | in (an array of values)                               |
| \_nin     | not in (an array of values)                           |
| \_regex   | matches a regular expression                          |
| \_options | regular expression options ('i' for case independent) |
| \_mod     | arithmetic modulo (value % args[0] === args[1])       |
| \_and     | logical and of an array of filters                    |
| \_not     | logical or of an array of filters                     |
| \_not     | logical not of a filter                               |

## Query Sort

You can sort a query with an `orderBy` option. For example:

```ts
const result = await salesOrder
    .query(
        edgesSelector(
            { id: true, orderDate: true, customer: { code: true } },
            // The size of the page and the sort order.
            {
                orderBy: {
                    customer: { name: +1 },
                    orderDate: -1,
                },
            },
        ),
    )
    .execute();
```

This query will return the sales orders ordered by increasing `customer.name` and then decreasing `orderDate`.

The leaf properties of the `orderBy` object can take two values: `+1` (ascending) or `-1` (descending).
For properties that reference another node, you can specify an order on one or more properties of the referenced node (`customer: { name: +1 }` in our example).

## Node read

If you have the primary key value(s) for a node, you have a simpler API to read the node:

```ts
const order = await salesOrder
    .read(
        {
            id: 'S003',
        },
        {
            orderDate: true,
            customer: { code: true },
        },
    )
    .execute();

console.log(`order id=${order.id} date=${order.orderDate}, customer=${order.customer.code})
```

This API saves you the overhead of the paging protocol, edges, filters, etc.

You may also use the special \_id property to read a node.
...

## Update mutation

...

## Delete mutation

...

## Aggregate queries

...

## Custom queries and mutations

...

## Batching

...

## Advanced filters

Filters on collections ...

## Static Typing

The client library allows you to query the data in a type-safe way.
...
