import { assert } from 'chai';
import * as utils from '../lib/utils';

describe('utils', () => {
    describe('serializeToGraphQL', () => {
        it('transforms object to GraphQL friendly JSON', () => {
            assert.strictEqual(
                utils.serializeToGraphQL({
                    property1: 'a string',
                    property2: 43,
                    property3: { property5: false, property6: 'stuff' },
                }),
                `{
    property1: "a string",
    property2: 43,
    property3: {
        property5: false,
        property6: "stuff"
    }
}`,
            );
        });
    });
});
