import { assert } from 'chai';
import { BatchRequest, edgesSelector, Graph, querySelector, withoutEdges } from '../lib/index';
import { data, DocumentInput, setup, TestApi } from './fixtures';

setup();

interface CurrentRequest {
    query: string;
    response: object;
}

interface TestData {
    selector: object;
    pagingOptions?: object;
    currentRequest: CurrentRequest;
    expectedResult: object;
}
const mockFetcherFactory = (currentRequest: CurrentRequest) => {
    // eslint-disable-next-line require-await
    return async (query: string): Promise<any> => {
        assert.equal(query, currentRequest.query);
        return currentRequest.response;
    };
};

const queryTest = async (testData: TestData) => {
    const graph = new Graph<TestApi>({
        fetcher: mockFetcherFactory(testData.currentRequest),
    });
    const documentNode = graph.node('@sage/xtrem-client/document');
    const page = testData.pagingOptions
        ? await documentNode.query(edgesSelector(testData.selector, testData.pagingOptions)).execute()
        : await documentNode.query(edgesSelector(testData.selector)).execute();
    assert.deepEqual(page, testData.expectedResult);
    assert.isArray(page.edges);
};

describe('graphql client', () => {
    it('can query a page', async () => {
        await queryTest(data['can query a page']);
    });

    it('can iterate on pages', async () => {
        await queryTest(data['can iterate on pages - 1']);
        await queryTest(data['can iterate on pages - 2']);
    });

    it('can iterate on pages backward', async () => {
        await queryTest(data['can iterate on pages backward - 1']);
        await queryTest(data['can iterate on pages backward - 2']);
    });

    it('can filter pages', async () => {
        await queryTest(data['can filter pages']);
    });

    it('can order pages', async () => {
        await queryTest(data['can order pages']);
    });

    it('can order pages with last', async () => {
        await queryTest(data['can order pages with last']);
    });

    it('can filter and order pages simultaneously', async () => {
        await queryTest(data['can filter and order pages simultaneously']);
    });

    it('can query a page containing another connection', async () => {
        const testData = data['can query a page containing another connection'];
        const graph = new Graph<TestApi>({
            fetcher: mockFetcherFactory(testData.currentRequest),
        });
        const documentNode = graph.node('@sage/xtrem-client/document');
        const page = await documentNode
            .query(edgesSelector({ id: true, lines: querySelector({ item: true }) }))
            .execute();
        const documents = withoutEdges(page);
        assert.deepEqual(documents, testData.expectedResult as any);
        assert.isArray(documents);
    });

    it('can execute a custom query', async () => {
        const testData = data['can execute a custom query'];
        const graph = new Graph<TestApi>({
            fetcher: mockFetcherFactory(testData.currentRequest),
        });
        const documentNode = graph.node('@sage/xtrem-client/document');
        const result = await documentNode.queries.testQuery(testData.selector, testData.otherOptions).execute();
        assert.deepEqual(result, testData.expectedResult);
    });

    it('can execute a custom mutation', async () => {
        const testData = data['can execute a custom mutation'];
        const graph = new Graph<TestApi>({
            fetcher: mockFetcherFactory(testData.currentRequest),
        });
        const documentNode = graph.node('@sage/xtrem-client/document');
        const result = await documentNode.mutations.testMutation(testData.selector, testData.otherOptions).execute();
        assert.deepEqual(result, testData.expectedResult);
    });

    it('can start a bulk mutation', async () => {
        const testData = data['can start a bulk mutation'];
        const graph = new Graph<TestApi>({
            fetcher: mockFetcherFactory(testData.currentRequest),
        });
        const documentNode = graph.node('@sage/xtrem-client/document');
        const result = await documentNode.asyncOperations.testBulkMutation
            .start({ trackingId: true }, { filter: '{}' })
            .execute();
        assert.deepEqual(result, testData.expectedResult);
    });

    it('can stop a bulk mutation', async () => {
        const testData = data['can stop a bulk mutation'];
        const graph = new Graph<TestApi>({
            fetcher: mockFetcherFactory(testData.currentRequest),
        });
        const documentNode = graph.node('@sage/xtrem-client/document');
        const result = await documentNode.asyncOperations.testBulkMutation
            .stop(true, { trackingId: 'abcd', reason: 'testing' })
            .execute();
        assert.deepEqual(result, testData.expectedResult);
    });

    it('can execute a create mutation', async () => {
        const testData = data['can execute a create mutation'];
        const graph = new Graph<TestApi>({
            fetcher: mockFetcherFactory(testData.currentRequest),
        });
        const documentNode = graph.node('@sage/xtrem-client/document');
        const selector = {
            id: true,
            amount: true,
            status: true,
            address: { street: true, city: true },
            lines: querySelector({ item: true, quantity: true }),
        };
        const result = await documentNode.create(selector, { data: testData.data as DocumentInput }).execute();
        assert.deepEqual(result, testData.expectedResult as any);
    });

    it('can batch queries', async () => {
        const testData = data['can batch queries'];
        const graph = new Graph<TestApi>({
            fetcher: mockFetcherFactory(testData.currentRequest),
        });
        const documentNode = graph.node('@sage/xtrem-client/document');
        const batch = new BatchRequest({
            document1: documentNode.queries.testQuery(testData.arguments[0][0] as any, testData.arguments[0][1] as any),
            document2: documentNode.queries.testQuery(testData.arguments[1][0] as any, testData.arguments[1][1] as any),
        });
        const result = await batch.execute();
        assert.deepEqual(result, testData.expectedResult as any);
    });

    it('throws if batch mixes query and mutation', () => {
        const testData = data['can batch queries'];
        const graph = new Graph<TestApi>({
            fetcher: mockFetcherFactory(testData.currentRequest),
        });
        const documentNode = graph.node('@sage/xtrem-client/document');
        assert.throws(() => {
            return new BatchRequest({
                document1: documentNode.mutations.testMutation({ mutatedDocument: { id: true } }, { id: 'doc1' }),
                document2: documentNode.queries.testQuery({ queriedDocument: { id: true } }, { id: 'doc2' }),
            });
        }, /invalid batch request: cannot mix queries and mutations in the same request/);
    });

    it('can execute lookups queries', async () => {
        let testData = data['can execute lookups queries - 1'];
        let graph = new Graph<TestApi>({
            fetcher: mockFetcherFactory(testData.currentRequest),
        });
        let documentNode = graph.node('@sage/xtrem-client/document');
        const resultWithId = await documentNode
            .lookups('100')
            .address(edgesSelector(testData.selector, testData.pagingOptions))
            .execute();
        assert.deepEqual(resultWithId, testData.expectedResult);

        testData = data['can execute lookups queries - 2'];
        graph = new Graph<TestApi>({
            fetcher: mockFetcherFactory(testData.currentRequest),
        });
        documentNode = graph.node('@sage/xtrem-client/document');
        const resultWithData = await documentNode
            .lookups({ data: { amount: 100 } })
            .address(edgesSelector(testData.selector, testData.pagingOptions))
            .execute();
        assert.deepEqual(resultWithData, testData.expectedResult);
    });

    it('can execute get default values', async () => {
        let testData = data['can execute getDefaults queries - 1'];
        let graph = new Graph<TestApi>({
            fetcher: mockFetcherFactory(testData.currentRequest),
        });
        let documentNode = graph.node('@sage/xtrem-client/document');
        const result = await documentNode.getDefaults(testData.selector).execute();
        assert.deepEqual(result, testData.expectedResult as any);

        testData = data['can execute getDefaults queries - 2'];
        graph = new Graph<TestApi>({
            fetcher: mockFetcherFactory(testData.currentRequest),
        });
        documentNode = graph.node('@sage/xtrem-client/document');
        const resultWithArgs = await documentNode
            .getDefaults(testData.selector, { data: { id: 'Test', amount: 1000, status: 'draft' } })
            .execute();
        assert.deepEqual(resultWithArgs, testData.expectedResult as any);
    });
});
