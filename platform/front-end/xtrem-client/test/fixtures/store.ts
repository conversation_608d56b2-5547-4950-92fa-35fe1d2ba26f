const basicQuery = `query {
    xtremClient {
        document {
            query %{
                edges {
                    node {
                        id
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}`;

const queryWithAmount = `query {
    xtremClient {
        document {
            query %{
                edges {
                    node {
                        id
                        amount
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}`;

const queryWithLines = `query {
    xtremClient {
        document {
            query {
                edges {
                    node {
                        id
                        lines {
                            query {
                                edges {
                                    node {
                                        item
                                    }
                                    cursor
                                }
                                pageInfo {
                                    hasNextPage
                                    endCursor
                                    hasPreviousPage
                                    startCursor
                                }
                            }
                        }
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}`;

const customQuery = `query {
    xtremClient {
        document {
            testQuery (id: "doc2", text: "hello") {
                queriedDocument {
                    id
                    amount
                }
                text
            }
        }
    }
}`;

const customMutation = `mutation {
    xtremClient {
        document {
            testMutation (id: "doc2", text: "hello") {
                mutatedDocument {
                    id
                    amount
                }
                text
            }
        }
    }
}`;

const startBulkMutation = `mutation {
    xtremClient {
        document {
            testBulkMutation {
                start (filter: "{}") {
                    trackingId
                }
            }
        }
    }
}`;

const stopBulkMutation = `mutation {
    xtremClient {
        document {
            testBulkMutation {
                stop (trackingId: "abcd", reason: "testing")
            }
        }
    }
}`;

const basicResult = {
    edges: [
        { node: { id: 'doc1' }, cursor: null },
        { node: { id: 'doc2' }, cursor: null },
        { node: { id: 'doc3' }, cursor: null },
    ],
    pageInfo: {
        hasNextPage: false,
        endCursor: 'doc3',
        hasPreviousPage: false,
        startCursor: 'doc1',
    },
};

const basicResponse = {
    data: {
        xtremClient: {
            document: {
                query: basicResult,
            },
        },
    },
};

const resultWithAmount = {
    edges: [
        { node: { id: 'doc1', amount: 100 }, cursor: null },
        { node: { id: 'doc2', amount: 80 }, cursor: null },
        { node: { id: 'doc3', amount: 50 }, cursor: null },
    ],
    pageInfo: { hasNextPage: false, endCursor: 'doc3', hasPreviousPage: false, startCursor: 'doc1' },
};

const responseWithAmount = {
    data: {
        xtremClient: {
            document: {
                query: resultWithAmount,
            },
        },
    },
};

const listOfLines = [
    { node: { id: 'doc1', lines: [{ item: 'item1' }, { item: 'item2' }, { item: 'item3' }] } },
    { node: { id: 'doc2', lines: [{ item: 'item5' }, { item: 'item1' }] } },
    { node: { id: 'doc3', lines: [{ item: 'item2' }, { item: 'item8' }] } },
];

const dataToCreate = {
    id: 'doc2',
    amount: 10,
    status: 'accepted',
    address: {
        street: '156 University Avenue',
        city: 'Palo Alto',
    },
    lines: [
        { item: 'item4', quantity: 1 },
        { item: 'item8', quantity: 3 },
    ],
};

const createdData = {
    ...dataToCreate,
    status: 'draft',
    lines: {
        edges: dataToCreate.lines.map(line => ({ cursor: null, node: line })),
        pageInfo: {
            hasNextPage: false,
            endCursor: null,
            hasPreviousPage: false,
            startCursor: null,
        },
    },
};

const stringifiedCreatedData =
    '{id: "doc2", amount: 10, status: "accepted", address: {street: "156 University Avenue", city: "Palo Alto"}, lines: [{item: "item4", quantity: 1}, {item: "item8", quantity: 3}]}';

const createMutation = `mutation {
    xtremClient {
        document {
            create (data: ${stringifiedCreatedData}) {
                id
                amount
                status
                address {
                    street
                    city
                }
                lines {
                    query {
                        edges {
                            node {
                                item
                                quantity
                            }
                            cursor
                        }
                        pageInfo {
                            hasNextPage
                            endCursor
                            hasPreviousPage
                            startCursor
                        }
                    }
                }
            }
        }
    }
}`;

const batchQuery = `query {
    document1: xtremClient {
        document {
            testQuery (id: "doc1", text: "hello") {
                queriedDocument {
                    id
                    amount
                }
                text
            }
        }
    }
    document2: xtremClient {
        document {
            testQuery (id: "doc2", text: "world") {
                queriedDocument {
                    id
                }
            }
        }
    }
}`;

const batchResult = {
    document1: { queriedDocument: { id: 'doc1', amount: 100 }, text: 'special hello' },
    document2: { queriedDocument: { id: 'doc2' } },
};

const batchResponse = {
    document1: { document: { testQuery: { queriedDocument: { id: 'doc1', amount: 100 }, text: 'special hello' } } },
    document2: { document: { testQuery: { queriedDocument: { id: 'doc2' } } } },
};

const lookupsQuery = `query {
    xtremClient {
        document {
            lookups (%) {
                address (first: 2) {
                    edges {
                        node {
                            street
                            city
                        }
                        cursor
                    }
                    pageInfo {
                        hasNextPage
                        endCursor
                        hasPreviousPage
                        startCursor
                    }
                }
            }
        }
    }
}`;

const lookupsResult = {
    edges: [
        { node: { street: '10 rue Fructidor', city: 'Paris' }, cursor: null },
        { node: { street: '18 rue Brumaire', city: 'Paris' }, cursor: null },
    ],
    pageInfo: { endCursor: null, hasNextPage: false, startCursor: null, hasPreviousPage: false },
};

const getDefaultsQuery = `query {
    xtremClient {
        document {
            getDefaults %{
                id
                amount
                status
            }
        }
    }
}`;

const getDefaultsResult = [
    {
        getDefaults: {
            id: 'doc1',
            amount: 0,
            status: 'accepted',
        },
    },
    {
        getDefaults: {
            id: 'Test',
            amount: 1000,
            status: 'draft',
        },
    },
];

/** @internal */
export const data = {
    'can query a page': {
        selector: { id: true },
        currentRequest: {
            query: basicQuery.replace('%', ''),
            response: basicResponse,
        },
        expectedResult: basicResult,
    },
    'can iterate on pages - 1': {
        selector: { id: true },
        pagingOptions: { first: 2 },
        currentRequest: {
            query: basicQuery.replace('%', '(first: 2) '),
            response: basicResponse,
        },
        expectedResult: basicResult,
    },
    'can iterate on pages - 2': {
        selector: { id: true },
        pagingOptions: { first: 2, after: 'doc2' },
        currentRequest: {
            query: basicQuery.replace('%', '(first: 2, after: "doc2") '),
            response: basicResponse,
        },
        expectedResult: basicResult,
    },

    'can iterate on pages backward - 1': {
        selector: { id: true },
        pagingOptions: { last: 2 },
        currentRequest: {
            query: basicQuery.replace('%', '(last: 2) '),
            response: basicResponse,
        },
        expectedResult: basicResult,
    },
    'can iterate on pages backward - 2': {
        selector: { id: true },
        pagingOptions: { last: 2, before: 'doc2' },
        currentRequest: {
            query: basicQuery.replace('%', '(last: 2, before: "doc2") '),
            response: basicResponse,
        },
        expectedResult: basicResult,
    },
    'can filter pages': {
        selector: {
            id: true,
            amount: true,
        },
        pagingOptions: {
            filter: {
                amount: {
                    _lte: 75,
                },
            },
        },
        currentRequest: {
            query: queryWithAmount.replace('%', '(filter: "{\\"amount\\":{\\"_lte\\":75}}") '),
            response: responseWithAmount,
        },
        expectedResult: resultWithAmount,
    },
    'can order pages': {
        selector: {
            id: true,
            amount: true,
        },
        pagingOptions: {
            orderBy: {
                amount: -1,
            },
        },
        currentRequest: {
            query: queryWithAmount.replace('%', '(orderBy: "{\\"amount\\":-1}") '),
            response: responseWithAmount,
        },
        expectedResult: resultWithAmount,
    },
    'can order pages with last': {
        selector: {
            id: true,
            amount: true,
        },
        pagingOptions: {
            orderBy: {
                amount: -1,
            },
            last: 2,
        },
        currentRequest: {
            query: queryWithAmount.replace('%', '(last: 2, orderBy: "{\\"amount\\":-1}") '),
            response: responseWithAmount,
        },
        expectedResult: resultWithAmount,
    },
    'can filter and order pages simultaneously': {
        selector: {
            id: true,
            amount: true,
        },
        pagingOptions: {
            filter: {
                address: {
                    city: {
                        _eq: 'Paris',
                    },
                },
            },
            orderBy: {
                amount: +1,
            },
        },
        currentRequest: {
            query: queryWithAmount.replace(
                '%',
                '(filter: "{\\"address\\":{\\"city\\":{\\"_eq\\":\\"Paris\\"}}}", orderBy: "{\\"amount\\":1}") ',
            ),
            response: responseWithAmount,
        },
        expectedResult: resultWithAmount,
    },
    'can query a page containing another connection': {
        currentRequest: {
            query: queryWithLines,
            response: {
                data: {
                    xtremClient: {
                        document: {
                            query: {
                                edges: listOfLines,
                                pageInfo: {
                                    hasNextPage: false,
                                    endCursor: 'doc3',
                                    hasPreviousPage: false,
                                    startCursor: 'doc1',
                                },
                            },
                        },
                    },
                },
            },
        },
        expectedResult: listOfLines.map(line => line.node),
    },
    'can execute a custom query': {
        selector: { queriedDocument: { id: true, amount: true }, text: true },
        otherOptions: { id: 'doc2', text: 'hello' },
        currentRequest: {
            query: customQuery,
            response: {
                data: {
                    xtremClient: {
                        document: {
                            testQuery: {
                                queriedDocument: { id: 'doc2', amount: 80 },
                                text: 'special hello',
                            },
                        },
                    },
                },
            },
        },
        expectedResult: { queriedDocument: { id: 'doc2', amount: 80 }, text: 'special hello' },
    },
    'can execute a custom mutation': {
        selector: { mutatedDocument: { id: true, amount: true }, text: true },
        otherOptions: { id: 'doc2', text: 'hello' },
        currentRequest: {
            query: customMutation,
            response: {
                data: {
                    xtremClient: {
                        document: {
                            testMutation: {
                                mutatedDocument: { id: 'doc2', amount: 80 },
                                text: 'special hello',
                            },
                        },
                    },
                },
            },
        },
        expectedResult: { mutatedDocument: { id: 'doc2', amount: 80 }, text: 'special hello' },
    },
    'can execute a create mutation': {
        data: dataToCreate,
        currentRequest: {
            query: createMutation,
            response: {
                data: {
                    xtremClient: {
                        document: {
                            create: createdData,
                        },
                    },
                },
            },
        },
        expectedResult: createdData,
    },
    'can batch queries': {
        arguments: [
            [
                { queriedDocument: { id: true, amount: true }, text: true },
                { id: 'doc1', text: 'hello' },
            ],
            [{ queriedDocument: { id: true } }, { id: 'doc2', text: 'world' }],
        ],
        currentRequest: {
            query: batchQuery,
            response: {
                data: batchResponse,
            },
        },
        expectedResult: batchResult,
    },
    'can execute lookups queries - 1': {
        selector: { street: true, city: true },
        pagingOptions: { first: 2 },
        currentRequest: {
            query: lookupsQuery.replace('%', '_id: "100"'),
            response: {
                data: {
                    xtremClient: { document: { lookups: { address: lookupsResult } } },
                },
            },
        },
        expectedResult: lookupsResult,
    },
    'can execute lookups queries - 2': {
        selector: { street: true, city: true },
        pagingOptions: { first: 2 },
        currentRequest: {
            query: lookupsQuery.replace('%', 'data: {amount: 100}'),
            response: {
                data: {
                    xtremClient: { document: { lookups: { address: lookupsResult } } },
                },
            },
        },
        expectedResult: lookupsResult,
    },
    'can execute getDefaults queries - 1': {
        selector: { id: true, amount: true, status: true },
        currentRequest: {
            query: getDefaultsQuery.replace('%', ''),
            response: {
                data: {
                    xtremClient: {
                        document: {
                            getDefaults: getDefaultsResult[0],
                        },
                    },
                },
            },
        },
        expectedResult: getDefaultsResult[0],
    },
    'can execute getDefaults queries - 2': {
        selector: { id: true, amount: true, status: true },
        currentRequest: {
            query: getDefaultsQuery.replace('%', '(data: {id: "Test", amount: 1000, status: "draft"}) '),
            response: {
                data: {
                    xtremClient: {
                        document: {
                            getDefaults: getDefaultsResult[1],
                        },
                    },
                },
            },
        },
        expectedResult: getDefaultsResult[1],
    },

    'can start a bulk mutation': {
        data: dataToCreate,
        currentRequest: {
            query: startBulkMutation,
            response: {
                data: {
                    xtremClient: {
                        document: {
                            testBulkMutation: { start: { trackingId: 'abcd' } },
                        },
                    },
                },
            },
        },
        expectedResult: { trackingId: 'abcd' },
    },

    'can stop a bulk mutation': {
        data: dataToCreate,
        currentRequest: {
            query: stopBulkMutation,
            response: {
                data: {
                    xtremClient: {
                        document: {
                            testBulkMutation: { stop: true },
                        },
                    },
                },
            },
        },
        expectedResult: true,
    },
};
