/* eslint-disable @typescript-eslint/no-unused-vars */
export type NotAny<T> = T[] extends true[] ? T : T[] extends false[] ? T : never;

// inspired by https://2ality.com/2019/07/testing-static-types.html
export type AssertEqual<T, Expected> = NotAny<T extends Expected ? (Expected extends T ? true : false) : false>;
export type AssertMatch<T, Expected> = NotAny<T extends Expected ? true : false>;

// inspired by https://fettblog.eu/typescript-match-the-exact-object-shape/
class ExtraPropertiesError<T> {
    protected never!: T;
}
export type Exact<T1, T2> = T1 extends T2
    ? Exclude<keyof T1, keyof T2> extends never
        ? T1
        : ExtraPropertiesError<T2>
    : never;

export function match<T, Expected>(arg: AssertMatch<T, Expected>) {}
export function strictMatch<T, Expected>(arg: AssertMatch<T, Exact<T, Expected>>) {}
export function equal<T, Expected>(arg: AssertEqual<T, Expected>) {}

export function checkTypeAssertions() {
    // sanity check on our type assertions
    equal<{ s: string; n: number }, { s: string; n: number }>(true);
    equal<{ s: string; n: number }, { s: string }>(false);
    equal<{ s: string; n: number }, { s: string; n: string }>(false);
    match<{ s: string; n: number }, { s: string; n: number }>(true);
    match<{ s: string; n: number }, { s: string }>(true);
    match<{ s: string; n: number }, {}>(true);
    match<{ s: string }, { s: string; n: number }>(false);
    match<{}, { s: string; n: number }>(false);

    match<{ s: string }, { s?: string; n?: number }>(true);
    match<{ s: string; n: number }, { s?: string; n?: number }>(true);
    match<{ s: string; n: number; z: string }, { s?: string; n?: number }>(true);
    strictMatch<{ s: string }, { s?: string; n?: number }>(true);
    strictMatch<{ s: string; n: number }, { s?: string; n?: number }>(true);
    strictMatch<{ s: string; n: number; z: string }, { s?: string; n?: number }>(false);

    // check that any is rejected
    const never: never = undefined as any as never;
    equal<any, string>(never);
    equal<string, any>(never);
    equal<any, any>(never);
}
