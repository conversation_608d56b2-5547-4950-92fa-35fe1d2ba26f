import {
    ClientCollection,
    ClientNode,
    CreateOperation,
    GetDefaultsOperation,
    GetDuplicateOperation,
    Operation,
    QueryOperation,
} from '../../lib/index';

/** @internal */
export type DocumentStatus = 'draft' | 'reviewed' | 'accepted';

/** @internal */
export interface Document extends ClientNode {
    id: string;
    status: DocumentStatus;
    amount: number;
    address: Address;
    lines: ClientCollection<DocumentLine>;
    // extra properties for type assertions only
    nullableString: string | null;
    nullableNumber: number | null;
    nullableAddress: Address | null;
}

/** @internal */
export interface DocumentInput {
    id: string;
    status?: DocumentStatus;
    amount?: number;
    address?: AddressInput;
    lines?: DocumentLineInput[];
}

/** @internal */
export interface Address extends ClientNode {
    street: string;
    city: string;
    zip: string;
    country: string;
}

/** @internal */
export interface AddressInput {
    street?: string;
    city?: string;
    zip?: string;
    country?: string;
}

/** @internal */
export interface DocumentLine extends ClientNode {
    item: string;
    quantity: number;
}

/** @internal */
export interface DocumentLineInput {
    item?: string;
    quantity?: number;
}

/** @internal */
export interface TestQueryResult {
    queriedDocument: Document;
    text: string;
}

/** @internal */
export interface TestMutationResult {
    mutatedDocument: Document;
    text: string;
}

/** @internal */
export interface TestApi {
    '@sage/xtrem-client/document': {
        query: QueryOperation<Document>;
        queries: {
            testQuery: Operation<{ id: string; text?: string }, TestQueryResult>;
        };
        create: CreateOperation<DocumentInput, Document>;
        mutations: {
            testMutation: Operation<{ id: string; text?: string }, TestMutationResult>;
        };
        asyncOperations: {
            testBulkMutation: {
                track: Operation<{ trackingId: string }, boolean>;
                start: Operation<{ filter?: string }, { trackingId: string }>;
                stop: Operation<{ trackingId: string; reason?: string }, boolean>;
            };
        };
        lookups: (dataOrId: string | { data: Partial<Document> }) => {
            address: QueryOperation<Address>;
        };
        getDefaults: GetDefaultsOperation<Document>;
        getDuplicate: GetDuplicateOperation<Document>;
    };
}
