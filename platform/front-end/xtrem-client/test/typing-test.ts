import { CollectionSelector, EdgesSelector, Filter, OrderBy, Selector } from '../lib/index';
import { Document, DocumentStatus } from './fixtures';
import { match } from './fixtures/type-assertions';

export function checkSelectors() {
    match<'b', Exclude<'a' | 'b' | string, keyof { a: boolean }>>(true);
    // Selector values must be boolean
    match<{ id: true; amount: true }, Selector<Document>>(true);
    match<{ id: true; amount: false }, Selector<Document>>(true);
    match<{ id: true; amount: 1 }, Selector<Document>>(false);
    // Invalid properties are rejected if alone
    match<{ foo: true }, Selector<Document>>(false);
    // Unfortunately, invalid properties are only rejected if alone
    // This is due to the lack of exact types in TypeScript
    // See https://github.com/microsoft/TypeScript/issues/12936
    // We include these false positives here, to document the limits of what we can do
    match<{ foo: true; amount: true }, Selector<Document>>(true);

    // Properties of a reference can be selected
    match<{ address: { street: true; city: true } }, Selector<Document>>(true);
    // But we cannot select the reference as a whole (GraphQL does not allow it)
    match<{ address: true }, Selector<Document>>(false);
    // Invalid reference properties are rejected but only if alone
    match<{ address: { foo: true } }, Selector<Document>>(false);
    match<{ address: { foo: true; city: true } }, Selector<Document>>(true);

    // Properties of a collection can also be selected, properly wrapped with edges/node/...
    match<{ lines: CollectionSelector<{ item: true; quantity: true }> }, Selector<Document>>(true);
    // The edges/node wrapper is mandatory
    match<{ lines: { item: true; quantity: true } }, Selector<Document>>(false);
    // Invalid collection properties are rejected but only if alone
    match<{ lines: EdgesSelector<{ foo: true }> }, Selector<Document>>(false);
    match<{ lines: CollectionSelector<{ foo: true; quantity: true }> }, Selector<Document>>(true);
}

export function checkFilters() {
    match<{ id: string; amount: number }, Filter<Document>>(true);
    // Numbers can be passed as strings
    match<{ id: string; amount: string }, Filter<Document>>(true);
    match<{ amount: string }, Filter<Document>>(true);
    // But strings cannot be passed as number
    match<{ id: number }, Filter<Document>>(false);
    // Non nullable properties cannot be filtered as null
    match<{ id: null; amount: number }, Filter<Document>>(false);
    match<{ id: null }, Filter<Document>>(false);
    match<{ id: string; amount: null }, Filter<Document>>(false);
    match<{ amount: null }, Filter<Document>>(false);
    // Nullable properties can be filtered as null or not
    match<{ nullableString: string }, Filter<Document>>(true);
    match<{ nullableString: null }, Filter<Document>>(true);
    match<{ nullableString: string | null }, Filter<Document>>(true);
    match<{ nullableNumber: number }, Filter<Document>>(true);
    match<{ nullableNumber: null }, Filter<Document>>(true);
    match<{ nullableNumber: number | null }, Filter<Document>>(true);
    // Invalid properties are rejected but only if alone
    match<{ foo: string }, Filter<Document>>(false);
    match<{ id: string; foo: string }, Filter<Document>>(true);

    // check string operators
    match<{ id: { _eq: string } }, Filter<Document>>(true);
    match<{ id: { _ne: string } }, Filter<Document>>(true);
    match<{ id: { _lt: string } }, Filter<Document>>(true);
    match<{ id: { _gt: string } }, Filter<Document>>(true);
    match<{ id: { _lte: string } }, Filter<Document>>(true);
    match<{ id: { _gte: string } }, Filter<Document>>(true);
    match<{ id: { _in: string[] } }, Filter<Document>>(true);
    match<{ id: { _nin: string[] } }, Filter<Document>>(true);
    match<{ id: { _regex: string; _options: string } }, Filter<Document>>(true);
    match<{ id: { _nin: string[] } }, Filter<Document>>(true);
    match<{ id: { _eq: number } }, Filter<Document>>(false);
    match<{ id: { _ne: number } }, Filter<Document>>(false);
    match<{ id: { _lt: number } }, Filter<Document>>(false);
    match<{ id: { _gt: number } }, Filter<Document>>(false);
    match<{ id: { _lte: number } }, Filter<Document>>(false);
    match<{ id: { _gte: number } }, Filter<Document>>(false);
    match<{ id: { _in: number[] } }, Filter<Document>>(false);
    match<{ id: { _nin: number[] } }, Filter<Document>>(false);
    match<{ id: { _regex: number; _options: string } }, Filter<Document>>(false);
    match<{ id: { _regex: string; _options: number } }, Filter<Document>>(false);
    match<{ id: { _mod: number } }, Filter<Document>>(false);

    // Numeric properties can be filtered either as number or string, but not other types (like boolean)
    match<{ amount: number }, Filter<Document>>(true);
    match<{ amount: string }, Filter<Document>>(true);
    match<{ amount: boolean }, Filter<Document>>(false);
    // With equality operators too
    match<{ amount: { _eq: number } }, Filter<Document>>(true);
    match<{ amount: { _eq: string } }, Filter<Document>>(true);
    match<{ amount: { _eq: boolean } }, Filter<Document>>(false);

    match<{ amount: { _lt: number } }, Filter<Document>>(true);
    match<{ amount: { _lt: string } }, Filter<Document>>(true);
    match<{ amount: { _lt: boolean } }, Filter<Document>>(false);

    match<{ amount: { _in: number[] } }, Filter<Document>>(true);
    match<{ amount: { _in: string[] } }, Filter<Document>>(true);

    match<{ amount: { _regex: string; _options: string } }, Filter<Document>>(true);
    match<{ amount: { _regex: number; _options: string } }, Filter<Document>>(false);
    match<{ amount: { _mod: number } }, Filter<Document>>(false);

    // Enum properties can be filtered by their key, but not any string
    match<{ status: DocumentStatus }, Filter<Document>>(true);
    match<{ status: 'draft' }, Filter<Document>>(true);
    match<{ status: number }, Filter<Document>>(true);
    match<{ status: 'foo' }, Filter<Document>>(false);
    match<{ status: string }, Filter<Document>>(false);

    // Reference
    match<{ address: string }, Filter<Document>>(true);
    match<{ address: null }, Filter<Document>>(false);
    match<{ address: { street: string } }, Filter<Document>>(true);
    match<{ address: { street: { _eq: string } } }, Filter<Document>>(true);
    match<{ address: { street: number } }, Filter<Document>>(false);
    match<{ address: { street: { _eq: number } } }, Filter<Document>>(false);
    // Invalid properties are rejected in reference filters, but only if alone
    match<{ address: { foo: number } }, Filter<Document>>(false);
    match<{ address: { foo: number; street: string } }, Filter<Document>>(true);

    // Nullable reference
    match<{ nullableAddress: string }, Filter<Document>>(true);
    match<{ nullableAddress: null }, Filter<Document>>(true);
    match<{ nullableAddress: string | null }, Filter<Document>>(true);
    match<{ nullableAddress: { street: string } }, Filter<Document>>(true);

    // Collection
    match<{ lines: { item: string } }, Filter<Document>>(true);
    match<{ lines: { quantity: number } }, Filter<Document>>(true);
    match<{ lines: { quantity: string } }, Filter<Document>>(true);
    match<{ lines: { item: string; _atLeast: number } }, Filter<Document>>(true);
    match<{ lines: { item: string; _atMost: number } }, Filter<Document>>(true);
    match<{ lines: { item: string; _every: true } }, Filter<Document>>(true);
    match<{ lines: { item: string; _none: true } }, Filter<Document>>(true);
    // Invalid quantifier type
    match<{ lines: { _atLeast: string } }, Filter<Document>>(false);
    match<{ lines: { item: string; _atLeast: string } }, Filter<Document>>(false);
    match<{ lines: { item: string; _every: boolean } }, Filter<Document>>(false);
    // Invalid properties are rejected in collection filters, but only if alone
    match<{ lines: { foo: string } }, Filter<Document>>(false);
    match<{ lines: { foo: string; item: string } }, Filter<Document>>(true);
    match<{ lines: { foo: string; _atLeast: 5 } }, Filter<Document>>(true);
}

export function checkOrderBy() {
    match<{ id: 1; quantity: -1 }, OrderBy<Document>>(true);
    // But values have to be 1 or -1
    match<{ id: 0; quantity: -1 }, OrderBy<Document>>(false);
    // Rejects invalid property
    match<{ foo: 1 }, OrderBy<Document>>(false);
    // False positives when invalid property is not alone
    match<{ id: 1; foo: 1 }, OrderBy<Document>>(true);

    // Reference
    match<{ address: { street: 1 } }, OrderBy<Document>>(true);
    match<{ address: { foo: 1 } }, OrderBy<Document>>(false);
    // False positives when invalid property is not alone
    match<{ address: { foo: 1; street: 1 } }, OrderBy<Document>>(true);

    // Cannot order by collection
    match<{ lines: { item: 1 } }, OrderBy<Document>>(false);
}
