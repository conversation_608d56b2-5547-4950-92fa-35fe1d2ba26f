{"name": "@sage/xtrem-ui-plugin-graphiql", "description": "GraphiQL IDE plugin for the Xtrem UI framework.", "version": "58.0.2", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "files": ["build", "README.md", "CHANGELOG.md"], "main": "build/index.js", "types": "build/index.d.ts", "xtremPlugin": true, "dependencies": {"@codemirror/language": "6.11.2", "@sage/xtrem-ui": "workspace:*", "axios": "^1.11.0", "graphiql": "^3.0.6", "html-webpack-plugin": "^5.1.0", "react": "^18.3.1"}, "devDependencies": {"@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-dev": "workspace:*", "@types/react": "^18.3.3", "eslint": "^8.49.0"}, "scripts": {"build": "xtrem build-plugin", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "lint": "eslint -c .eslintrc.js --ext .ts --ext .tsx lib"}}