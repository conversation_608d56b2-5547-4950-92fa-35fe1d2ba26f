const HtmlWebpackPlugin = require('html-webpack-plugin');
const packageFile = require('./package.json');

module.exports = {
    mode: 'development',
    optimization: {
        minimize: false,
    },
    devtool: 'eval-source-map',
    plugins: [
        new HtmlWebpackPlugin({
            publicPath: `/plugins/${Buffer.from(packageFile.name).toString('base64')}/build/`,
        }),
    ],
    resolve: {
        modules: ['../../node_modules/', 'node_modules/'],
        symlinks: true,
    },
    module: {
        rules: [
            {
                test: /\.m?js/,
                resolve: {
                    fullySpecified: false
                }
            }
        ],
    },
};
