/* eslint-disable react/jsx-indent */
/* eslint-disable react/jsx-indent-props */

import * as ui from '@sage/xtrem-ui';
import { GraphiQL } from 'graphiql';
import * as React from 'react';
import 'graphiql/graphiql.css';
import './styles.css';

export interface GraphiqlPluginProperties {
    /** Height of the editor in pixels. */
    height?: number;
    /** Whether the top toolbar is hidden */
    isToolbarHidden?: boolean;
}

type GraphiqlQueryProps = ui.plugin.XtremUiPluginComponentProps<GraphiqlPluginProperties, string | null>;

interface GraphiqlQueryState {
    value: string | null;
    prevValue: string | null;
    isActive: boolean;
}

class GraphiqlQueryField extends React.Component<GraphiqlQueryProps, GraphiqlQueryState> {
    private element = React.createRef<HTMLDivElement>();

    static getDerivedStateFromProps(props: GraphiqlQueryProps, state: GraphiqlQueryState): GraphiqlQueryState {
        if (!state || props.value !== state.prevValue) {
            return { ...(state || { isActive: false }), value: props.value || '', prevValue: props.value };
        }

        return state;
    }

    override componentDidMount() {
        const graphiqlElement = this.element.current?.querySelector('.graphiql-query-editor .graphiql-editor');
        if (graphiqlElement) {
            graphiqlElement.addEventListener('focus', this.onFocus, true);
            graphiqlElement.addEventListener('blur', this.onBlur, true);
        }
    }

    onChange = (value: string) => {
        this.setState({ value });
    };

    onFocus = () => this.setState({ isActive: true });

    onBlur = () => {
        const { value } = this.state;
        const { setFieldValue } = this.props;
        setFieldValue(value);
        this.setState({ isActive: false });
    };

    // eslint-disable-next-line react/destructuring-assignment
    onFetchApi = (graphQLParams: { query: string }) => this.props.executeQuery(graphQLParams.query);

    override render() {
        const { value, isActive } = this.state;
        const { fieldProperties, browser, value: propValue, screenId, fixedHeight } = this.props;

        if (browser && browser.lessThan.xs) {
            return (
                <div>
                    {ui.localize(
                        '@sage/xtrem-ui-plugin-graphiql/mobile-not-supported',
                        'Code editing on mobile devices is not supported.',
                    )}
                </div>
            );
        }

        const classNames = ['e-graphiql-plugin'];
        // The readOnly property on GraphiQL is only applied during the first rendering so we
        // have to work around it to allow the state to be changed dynamically.
        if (
            ui.plugin.isFieldReadOnly(screenId, fieldProperties, propValue, null) ||
            ui.plugin.isFieldDisabled(screenId, fieldProperties, propValue, null)
        ) {
            classNames.push('e-graphiql-plugin-disabled');
        }

        if (fieldProperties.isToolbarHidden) {
            classNames.push('e-graphiql-toolbar-hidden');
        }

        if (isActive) {
            classNames.push('e-graphiql-active');
        }

        let height = fieldProperties.height || fixedHeight || 400;

        if (fieldProperties.helperText) {
            height -= 22;
        }

        return (
            <div ref={this.element} className={classNames.join(' ')} style={{ height }}>
                <GraphiQL fetcher={this.onFetchApi} query={value || ''} onEditQuery={this.onChange} />
            </div>
        );
    }
}

export default {
    name: 'graphiql',
    component: GraphiqlQueryField,
    createFieldQuery: (): ui.plugin.QueryProperty => {
        return { value: true };
    },
    transformFromGraphValue: (rawValueFromQueryResult: any) => {
        // eslint-disable-next-line react/destructuring-assignment
        if (rawValueFromQueryResult && rawValueFromQueryResult.value) {
            return rawValueFromQueryResult.value;
        }
        return null;
    },
    transformToGraphValue: (value: any) => {
        return value ? { value } : null;
    },
} as ui.plugin.XtremUiPlugin<GraphiqlPluginProperties, string>;
