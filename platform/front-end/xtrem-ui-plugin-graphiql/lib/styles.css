.e-graphiql-plugin,
.e-graphiql-plugin .graphiql-container .cm-s-graphiql,
.e-graphiql-plugin .graphiql-container {
    --editor-background: var(--colorsYang100);
    --color-base: var(--colorsYang100);
    --container-height: 400px;
    --container-outline: 1px solid #668592;
    --container-pointer-events: auto;
    --container-background-color: #fff;
    --color-cm-invalidchar: #f00;
    --color-cm-comment: #999;
    --color-cm-punctuation: #555;
    --color-cm-keyword: #b11a04;
    --color-cm-def: #d2054e;
    --color-cm-property: #1f61a0;
    --color-cm-qualifier: #1c92a9;
    --color-cm-attribute: #8b2bb9;
    --color-cm-number: #2882f9;
    --color-cm-string: #d64292;
    --color-cm-builtin: #d47509;
    --color-cm-string-2: #0b7fc7;
    --color-cm-variable: #397d13;
    --color-cm-meta: #b33086;
    --color-cm-atom: #ca9800;
}

.e-graphiql-plugin.e-graphiql-plugin-disabled,
.e-graphiql-plugin.e-graphiql-plugin-disabled .graphiql-container {
    --container-pointer-events: none;
    --container-background-color: rgb(242, 245, 246);
    --color-cm-invalidchar: rgba(0, 0, 0, 0.55);
    --color-cm-comment: rgba(0, 0, 0, 0.55);
    --color-cm-punctuation: rgba(0, 0, 0, 0.55);
    --color-cm-keyword: rgba(0, 0, 0, 0.55);
    --color-cm-def: rgba(0, 0, 0, 0.55);
    --color-cm-property: rgba(0, 0, 0, 0.55);
    --color-cm-qualifier: rgba(0, 0, 0, 0.55);
    --color-cm-attribute: rgba(0, 0, 0, 0.55);
    --color-cm-number: rgba(0, 0, 0, 0.55);
    --color-cm-string: rgba(0, 0, 0, 0.55);
    --color-cm-builtin: rgba(0, 0, 0, 0.55);
    --color-cm-string-2: rgba(0, 0, 0, 0.55);
    --color-cm-variable: rgba(0, 0, 0, 0.55);
    --color-cm-meta: rgba(0, 0, 0, 0.55);
    --color-cm-atom: rgba(0, 0, 0, 0.55);
}


.e-graphiql-plugin .graphiql-container .graphiql-editors.full-height {
    margin-top: 0;
}

.e-graphiql-plugin .graphiql-container .graphiql-query-editor {
    padding: 0;
    border: 1px solid var(--colorsUtilityMajor300);
    border-radius: var(--borderRadius050);
}

.e-graphiql-plugin .graphiql-container .graphiql-session {
    padding: 0;
}

.e-graphiql-plugin .graphiql-container .graphiql-session > div:last-child {
    display: none !important;
}

.e-graphiql-plugin .graphiql-container .graphiql-sidebar {
    display: none;
}

.e-graphiql-plugin .graphiql-container .graphiql-editor-tools {
    display: none;
}
.e-graphiql-plugin .graphiql-container .graphiql-main .graphiql-session-header {
    display: none;
}

.e-graphiql-plugin .graphiql-response {
    display: none;
}
.e-graphiql-plugin .graphiql-horizontal-drag-bar {
    display: none !important;
}

.e-graphiql-plugin .graphiql-container .graphiql-sessions {
    margin: 0;
    padding: 0;
    border-radius: 0;
    background: var(--colorsYang100);
}
.e-graphiql-plugin .graphiql-query-editor .graphiql-toolbar {
    display: none;
}

.e-graphiql-plugin .graphiql-container .CodeMirror.cm-s-graphiql {
    border-radius: calc(var(--borderRadius050) + 1px);
}

/* Invalid character */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-invalidchar {
    color: var(--color-cm-invalidchar);
}

/* Comment */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-comment {
    color: var(--color-cm-comment);
}

/* Punctuation */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-punctuation {
    color: var(--color-cm-punctuation);
}

/* Keyword */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-keyword {
    color: var(--color-cm-keyword);
}

/* OperationName, FragmentName */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-def {
    color: var(--color-cm-def);
}

/* FieldName */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-property {
    color: var(--color-cm-property);
}

/* FieldAlias */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-qualifier {
    color: var(--color-cm-qualifier);
}

/* ArgumentName and ObjectFieldName */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-attribute {
    color: var(--color-cm-attribute);
}

/* Number */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-number {
    color: var(--color-cm-number);
}

/* String */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-string {
    color: var(--color-cm-string);
}

/* Boolean */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-builtin {
    color: var(--color-cm-builtin);
}

/* EnumValue */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-string-2 {
    color: var(--color-cm-string-2);
}

/* Variable */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-variable {
    color: var(--color-cm-variable);
}

/* Directive */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-meta {
    color: var(--color-cm-meta);
}

/* Type */
.e-graphiql-plugin .graphiql-container .CodeMirror .cm-atom {
    color: var(--color-cm-atom);
}

/* Line number */
.e-graphiql-plugin .graphiql-container .cm-s-graphiql .CodeMirror-linenumber {
    color: var(--colorsYin090);
}

.e-graphiql-plugin li.CodeMirror-hint {
    color: var(--colorsYin090);
    background: var(--colorsYang100);
}
