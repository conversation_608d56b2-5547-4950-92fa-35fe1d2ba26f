# Monaco Editor field plugin for Xtrem Framework

This plugin integrates [Monaco editor](https://microsoft.github.io/monaco-editor/) into the Xtrem UI framework. This package is also meant to be used as a reference implementation on Xtrem UI plugins.

This field type only functions well in desktop browsers due to limitations of Monaco editor. If the field is loaded into a mobile-sized browser, a warning message is displayed instead.

## Binding
As the usual content of this field type is source code text, it was designed to be bound with a `TextStream` node property. It cannot be used with simple string fields.

## Usage

The Monaco editor plugin can be used as a plugin field on xtrem pages. It is important to _only_ import the type of the properties and not the implementation of the plugin.

```typescript
import type { MonacoPluginProperties } from '@sage/xtrem-ui-plugin-monaco';

    ...

    @ui.decorators.pluginField<MyDemoPage, MonacoPluginProperties>({
        parent() {
            return this.block;
        },
        title: 'Code',
        helperText: 'Source code of something',
        isFullWidth: true,
        pluginPackage: '@sage/xtrem-ui-plugin-monaco',
        language: 'typescript',
        height: 400,
    })
    notes: ui.fields.Plugin<MonacoPluginProperties>;

    ...
```

## Decorator properties

-   **language**: Programming or markup language of the editor content, it is used for syntax highlighting and validation.
-   **size**: Height of the editor field body in pixels.
