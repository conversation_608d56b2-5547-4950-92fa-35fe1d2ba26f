import * as monacoEditor from 'monaco-editor';
import { languages } from 'monaco-editor';
import { EditorSuggestion } from './index';

type Monaco = typeof monacoEditor;

const handlebarsHelpersSuggestions: EditorSuggestion[] = [
    {
        label: 'after',
        insertText: 'after',
        documentation: {
            value: 'Returns the elements of the given array from the given index to the end of the array.',
        },
        range: null,
    },
    {
        label: 'arrayify',
        insertText: 'arrayify',
        documentation: {
            value: 'Cast the given value to an array.',
        },
    },
    {
        label: 'before',
        insertText: 'before',
        documentation: {
            value: 'Returns the elements of the given array before the given index.',
        },
    },
    {
        label: 'eachIndex',
        insertText: '#eachIndex',
        documentation: {
            value: '',
        },
    },
    {
        label: 'filter',
        insertText: '#filter',
        documentation: {
            value: 'Block helper that filters the given array and returns a block for elements that evaluate to `true`, otherwise the else block is returned.',
        },
    },
    {
        label: 'first',
        insertText: 'first',
        documentation: {
            value: 'Returns the first element, or first n elements of the given array.',
        },
    },
    {
        label: 'forEach',
        insertText: '#forEach',
        documentation: {
            value: 'Iterates over each element in the given array and exposes the current element as context to the inner block. In addition to the current array element, the helper exposes the following variables to the inner block:  \n• index  \n• total  \n• isFirst  \n• isLast  \n\nAlso, `@index` is exposed as a private variable, and additional private variables may be defined as hash arguments.',
        },
    },
    {
        label: 'inArray',
        insertText: '#inArray',
        description: '{{#inArray <array> <value>}} <block> [{{else}} <inverse block>] {{/inArray}}',
        documentation: {
            value: "Block helper that renders the block if the given array contains the given value. Optionally, specify an inverse block to render when the array does not contain the given value.\n\n```handlebars\n<!-- array: ['a', 'b', 'c'] -->\n{{#inArray array \"d\"}}\n  foo\n{{else}}\n  bar\n{{/inArray}}\n<!-- results in: 'bar' -->\n```",
        },
    },
    {
        label: 'isArray',
        insertText: 'isArray',
        documentation: {
            value: 'Returns true if the given value is an es5 array.',
        },
    },
    {
        label: 'itemAt',
        insertText: 'itemAt',
        documentation: {
            value: 'Returns the element from the given array at the given index.',
        },
    },
    {
        label: 'join',
        insertText: 'join',
        documentation: {
            value: 'Join all elements of the given array into a string, optionally using a given separator.',
        },
    },
    {
        label: 'lengthEqual',
        insertText: '#lengthEqual',
        documentation: {
            value: 'Returns true if the the length of the given value is equal to the given length. Can be used as a block or inline helper.',
        },
    },
    {
        label: 'last',
        insertText: 'last',
        documentation: {
            value: 'Returns the last element, or last n items, of the given array or string.',
        },
    },
    {
        label: 'length',
        insertText: 'length',
        documentation: {
            value: 'Returns the length of the given array or string.',
        },
    },
    {
        label: 'map',
        insertText: 'map',
        documentation: {
            value: 'Returns a new array, created by calling a function on each element of the given array.',
        },
    },
    {
        label: 'pluck',
        insertText: 'pluck',
        documentation: {
            value: 'Map over the given object, or array of objects, and create an array of values from the given `prop`. Dot-notation may be used (as a string) to get nested properties.',
        },
    },
    {
        label: 'reverse',
        insertText: 'reverse',
        documentation: {
            value: 'Reverse the elements in the given array, or the characters in the given string.',
        },
    },
    {
        label: 'some',
        insertText: '#some',
        documentation: {
            value: 'Block helper that returns the block if the callback returns true for some value in the given array.',
        },
    },
    {
        label: 'sort',
        insertText: 'sort',
        documentation: {
            value: 'Sort the given array. If an array of objects is passed, you may optionally pass a `key` to sort on as the second argument. You may alternatively pass a sorting function as the second argument.',
        },
    },
    {
        label: 'sortBy',
        insertText: 'sortBy',
        documentation: {
            value: 'Sort an `array`. If an array of objects is passed, you may optionally pass a `key` to sort on as the second argument. You may alternatively pass a sorting function as the second argument.',
        },
    },
    {
        label: 'withAfter',
        insertText: '#withAfter',
        documentation: {
            value: 'Use the elements in the array after the specified index as context inside a block.',
        },
    },
    {
        label: 'withBefore',
        insertText: '#withBefore',
        documentation: {
            value: 'Use the elements in the array before the specified index as context inside a block.',
        },
    },
    {
        label: 'withFirst',
        insertText: '#withFirst',
        documentation: {
            value: 'Use the first element in an array inside a handlebars block expression.',
        },
    },
    {
        label: 'withGroup',
        insertText: '#withGroup',
        documentation: {
            value: 'Block helper that groups array elements by given group `size`.',
        },
    },
    {
        label: 'withLast',
        insertText: '#withLast',
        documentation: {
            value: 'Use the last item or `n` items in an array as context inside a block.',
        },
    },
    {
        label: 'withSort',
        insertText: '#withSort',
        documentation: {
            value: 'Block helper that sorts a collection and exposes the sorted collection as context inside the block.',
        },
    },
    {
        label: 'unique',
        insertText: '#unique',
        documentation: {
            value: 'Block helper that return an array with all duplicate values removed. Best used along with an #each helper.',
        },
    },
    {
        label: 'embed',
        insertText: 'embed',
        documentation: {
            value: 'Embed code from an external file as preformatted text.',
        },
    },
    {
        label: 'gist',
        insertText: 'gist',
        documentation: {
            value: 'Embed a GitHub Gist using only the id of the Gist.',
        },
    },
    {
        label: 'jsfiddle',
        insertText: 'jsfiddle',
        documentation: {
            value: 'Generate the HTML for a jsFiddle link with the given `params`.',
        },
    },
    {
        label: 'isEmpty',
        insertText: 'isEmpty',
        documentation: {
            value: 'Inline, subexpression, or block helper that returns true (or the block) if the given collection is empty, or false (or the inverse block, if supplied) if the colleciton is not empty.',
        },
    },
    {
        label: 'iterate',
        insertText: '#iterate',
        documentation: {
            value: 'Block helper that iterates over an array or object. If an array is given, `.forEach` is called, or if an object is given, `.forOwn` is called, otherwise the inverse block is returned.',
        },
    },
    {
        label: 'and',
        insertText: '#and',
        documentation: {
            value: 'Helper that renders the block if **both** of the given values are truthy. If an inverse block is specified it will be rendered when falsy. Works as a block helper, inline helper or subexpression.',
        },
    },
    {
        label: 'compare',
        insertText: '#compare',
        documentation: {
            value: 'Render a block when a comparison of the first and third arguments returns true. The second argument is the operator to use. You may also optionally specify an inverse block to render when falsy.',
        },
    },
    {
        label: 'contains',
        insertText: '#contains',
        documentation: {
            value: 'Block helper that renders the block if `collection` has the given `value`, using strict equality (`===`) for comparison, otherwise the inverse block is rendered (if specified). If a `startIndex` is specified and is negative, it is used as the offset from the end of the collection.',
        },
    },
    {
        label: 'default',
        insertText: 'default',
        documentation: {
            value: 'Returns the first value that is not undefined, otherwise the "default" value is returned.',
        },
    },
    {
        label: 'eq',
        insertText: '#eq',
        documentation: {
            value: 'Block helper that renders a block if `a` is **equal to** `b`. If an inverse block is specified it will be rendered when falsy. You may optionally use the `compare=""` hash argument for the second value.',
        },
    },
    {
        label: 'gt',
        insertText: '#gt',
        documentation: {
            value: 'Block helper that renders a block if `a` is **greater than** `b`. If an inverse block is specified it will be rendered when falsy. You may optionally use the `compare=""` hash argument for the second value.',
        },
    },
    {
        label: 'gte',
        insertText: '#gte',
        documentation: {
            value: 'Block helper that renders a block if `a` is **greater than or equal to** `b`. If an inverse block is specified it will be rendered when falsy. You may optionally use the `compare=""` hash argument for the second value.',
        },
    },
    {
        label: 'has',
        insertText: '#has',
        documentation: {
            value: 'Block helper that renders a block if `value` has `pattern`. If an inverse block is specified it will be rendered when falsy.',
        },
    },
    {
        label: 'isfalsy',
        insertText: 'isfalsy',
        documentation: {
            value: 'Returns true if the given `value` is falsy. Uses the falsy library for comparisons. Please see that library for more information or to report bugs with this helper.',
        },
    },
    {
        label: 'isTruthy',
        insertText: 'isTruthy',
        documentation: {
            value: 'Returns true if the given `value` is truthy. Uses the falsy library for comparisons. Please see that library for more information or to report bugs with this helper.',
        },
    },
    {
        label: 'ifEven',
        insertText: '#ifEven',
        documentation: {
            value: 'Return true if the given value is an even number.',
        },
    },
    {
        label: 'ifNth',
        insertText: '#ifNth',
        documentation: {
            value: 'Conditionally renders a block if the remainder is zero when `a` operand is divided by `b`. If an inverse block is specified it will be rendered when the remainder is **not zero**.',
        },
    },
    {
        label: 'ifOdd',
        insertText: '#ifOdd',
        documentation: {
            value: 'Block helper that renders a block if `value` is **an odd number**. If an inverse block is specified it will be rendered when falsy.',
        },
    },
    {
        label: 'is',
        insertText: '#is',
        documentation: {
            value: 'Block helper that renders a block if `a` is **equal to** `b`. If an inverse block is specified it will be rendered when falsy. Similar to [eq](#eq) but does not do strict equality.',
        },
    },
    {
        label: 'isnt',
        insertText: '#isnt',
        documentation: {
            value: 'Block helper that renders a block if `a` is **not equal to** `b`. If an inverse block is specified it will be rendered when falsy. Similar to [unlessEq](#unlesseq) but does not use strict equality for comparisons.',
        },
    },
    {
        label: 'lt',
        insertText: 'lt',
        documentation: {
            value: 'Block helper that renders a block if `a` is **less than** `b`. If an inverse block is specified it will be rendered when falsy. You may optionally use the `compare=""` hash argument for the second value.',
        },
    },
    {
        label: 'lte',
        insertText: '#lte',
        documentation: {
            value: 'Block helper that renders a block if `a` is **less than or equal to** `b`. If an inverse block is specified it will be rendered when falsy. You may optionally use the `compare=""` hash argument for the second value.',
        },
    },
    {
        label: 'neither',
        insertText: '#neither',
        documentation: {
            value: 'Block helper that renders a block if **neither of** the given values are truthy. If an inverse block is specified it will be rendered when falsy.',
        },
    },
    {
        label: 'not',
        insertText: '#not',
        documentation: {
            value: 'Returns true if `val` is falsy. Works as a block or inline helper.',
        },
    },
    {
        label: 'or',
        insertText: '#or',
        documentation: {
            value: 'Block helper that renders a block if **any of** the given values is truthy. If an inverse block is specified it will be rendered when falsy.',
        },
    },
    {
        label: 'unlessEq',
        insertText: '#unlessEq',
        documentation: {
            value: 'Block helper that always renders the inverse block **unless `a` is equal to `b`**.',
        },
    },
    {
        label: 'unlessGt',
        insertText: '#unlessGt',
        documentation: {
            value: 'Block helper that always renders the inverse block **unless `a` is is greater than `b`**.',
        },
    },
    {
        label: 'unlessLt',
        insertText: '#unlessLt',
        documentation: {
            value: 'Block helper that always renders the inverse block **unless `a` is is less than `b`**.',
        },
    },
    {
        label: 'unlessGteq',
        insertText: '#unlessGteq',
        documentation: {
            value: 'Block helper that always renders the inverse block **unless `a` is is greater than or equal to `b`**.',
        },
    },
    {
        label: 'unlessLteq',
        insertText: '#unlessLteq',
        documentation: {
            value: 'Block helper that always renders the inverse block **unless `a` is is less than or equal to `b`**.',
        },
    },
    {
        label: 'year',
        insertText: 'year',
        documentation: {
            value: 'Get the current year.',
        },
    },
    {
        label: 'moment',
        insertText: 'moment',
        documentation: {
            value: 'Use [moment][] as a helper. See [helper-date][] for more details.',
        },
    },
    {
        label: 'read',
        insertText: 'read',
        documentation: {
            value: 'Read a file from the file system. This is useful in composing "include"-style helpers using sub-expressions.',
        },
    },
    {
        label: 'readdir',
        insertText: 'readdir',
        documentation: {
            value: 'Return an array of files from the given directory.',
        },
    },
    {
        label: 'attr',
        insertText: 'attr',
        documentation: {
            value: 'Stringify attributes on the options `hash`.',
        },
    },
    {
        label: 'css',
        insertText: 'css',
        documentation: {
            value: 'Add an array of `<link>` tags. Automatically resolves relative paths to `options.assets` if passed on the context.',
        },
    },
    {
        label: 'js',
        insertText: 'js',
        documentation: {
            value: 'Generate one or more `<script></script>` tags with paths/urls to javascript or coffeescript files.',
        },
    },
    {
        label: 'sanitize',
        insertText: 'sanitize',
        documentation: {
            value: 'Strip HTML tags from a string, so that only the text nodes are preserved.',
        },
    },
    {
        label: 'ul',
        insertText: '#ul',
        documentation: {
            value: 'Block helper for creating unordered lists (`<ul></ul>`)',
        },
    },
    {
        label: 'ol',
        insertText: '#ol',
        documentation: {
            value: 'Block helper for creating ordered lists  (`<ol></ol>`)',
        },
    },
    {
        label: 'thumbnailImage',
        insertText: 'thumbnailImage',
        documentation: {
            value: 'Returns a `<figure>` with a thumbnail linked to a full picture',
        },
    },
    {
        label: 'i18n',
        insertText: '#i18n',
        documentation: {
            value: 'i18n helper. See [button-i18n](https://github.com/assemble/buttons) for a working example.',
        },
    },
    {
        label: 'inflect',
        insertText: 'inflect',
        documentation: {
            value: 'Returns either the `singular` or `plural` inflection of a word based on the given `count`.',
        },
    },
    {
        label: 'ordinalize',
        insertText: 'ordinalize',
        documentation: {
            value: 'Returns an ordinalized number as a string.',
        },
    },
    {
        label: 'log',
        insertText: 'log',
        documentation: {
            value: 'Helper for logging an unstyled message to the terminal.',
        },
    },
    {
        label: 'ok',
        insertText: 'ok',
        documentation: {
            value: 'Helper for logging a green colored "ok" message preceded by a checkmark to the terminal.',
        },
    },
    {
        label: 'success',
        insertText: 'success',
        documentation: {
            value: 'Helper for logging a green colored "success" message to the terminal.',
        },
    },
    {
        label: 'info',
        insertText: 'info',
        documentation: {
            value: 'Helper for logging a cyan colored "information" message to the terminal.',
        },
    },
    {
        label: 'warn',
        insertText: 'warn',
        documentation: {
            value: 'Helper for logging a yellow colored "warning" message to the terminal.',
        },
    },
    {
        label: 'error',
        insertText: 'error',
        documentation: {
            value: 'Helper for logging a red colored "error" message to the terminal.',
        },
    },
    {
        label: 'danger',
        insertText: 'danger',
        documentation: {
            value: 'Helper for logging a red colored "danger" message to the terminal.',
        },
    },
    {
        label: 'bold',
        insertText: 'bold',
        documentation: {
            value: 'Helper for logging a bolded message to the terminal.',
        },
    },
    {
        label: '_debug',
        insertText: '_debug',
        documentation: {
            value: 'Outputs a debug statement with the current context, and/or `val`',
        },
    },
    {
        label: '_inspect',
        insertText: '_inspect',
        documentation: {
            value: 'Returns stringified JSON, wrapped in a gfm codeblock, html `<pre>` tags, or unchanged, based on the `type` passed on the context.',
        },
    },
    {
        label: 'match',
        insertText: 'match',
        documentation: {
            value: 'Returns an array of strings that match the given glob pattern(s). Options may be passed on the options hash or locals.',
        },
    },
    {
        label: 'isMatch',
        insertText: 'isMatch',
        documentation: {
            value: ' Returns true if a filepath contains the given pattern. Options may be passed on the options hash or locals.',
        },
    },
    {
        label: 'abs',
        insertText: 'abs',
        documentation: {
            value: 'Return the magnitude of `a`.',
        },
    },
    {
        label: 'add',
        insertText: 'add',
        documentation: {
            value: 'Return the sum of `a` plus `b`.',
        },
    },
    {
        label: 'avg',
        insertText: 'avg',
        documentation: {
            value: 'Returns the average of all numbers in the given array.',
        },
    },
    {
        label: 'ceil',
        insertText: 'ceil',
        documentation: {
            value: 'Get the `Math.ceil()` of the given value.',
        },
    },
    {
        label: 'divide',
        insertText: 'divide',
        documentation: {
            value: 'Divide `a` by `b`',
        },
    },
    {
        label: 'minus',
        insertText: 'minus',
        documentation: {
            value: 'Return the difference of `a` minus `b`.',
        },
    },
    {
        label: 'floor',
        insertText: 'floor',
        documentation: {
            value: 'Get the `Math.floor()` of the given value.',
        },
    },
    {
        label: 'modulo',
        insertText: 'modulo',
        documentation: {
            value: 'Get the remainder of a division operation.',
        },
    },
    {
        label: 'multiply',
        insertText: 'multiply',
        documentation: {
            value: 'Return the product of `a` times `b`.',
        },
    },
    {
        label: 'plus',
        insertText: 'plus',
        documentation: {
            value: 'Add `a` by `b`.',
        },
    },
    {
        label: 'random',
        insertText: 'random',
        documentation: {
            value: 'Generate a random number between two values',
        },
    },
    {
        label: 'remainder',
        insertText: 'remainder',
        documentation: {
            value: '',
        },
    },
    {
        label: 'round',
        insertText: 'round',
        documentation: {
            value: 'Round the given number.',
        },
    },
    {
        label: 'subtract',
        insertText: 'subtract',
        documentation: {
            value: 'Return the product of `a` minus `b`.',
        },
    },
    {
        label: 'sum',
        insertText: 'sum',
        documentation: {
            value: 'Returns the sum of all numbers in the given array.',
        },
    },
    {
        label: 'times',
        insertText: 'times',
        documentation: {
            value: 'Multiply number `a` by number `b`.',
        },
    },
    {
        label: 'frame',
        insertText: 'frame',
        documentation: {
            value: 'Block helper for exposing private `@` variables on the context',
        },
    },
    {
        label: 'option',
        insertText: 'option',
        documentation: {
            value: 'Return the given value of `prop` from `this.options`.',
        },
    },
    {
        label: 'noop',
        insertText: '#noop',
        documentation: {
            value: 'Block helper that renders the block without taking any arguments.',
        },
    },
    {
        label: 'typeOf',
        insertText: 'typeOf',
        documentation: {
            value: 'Get the native type of the given `value`',
        },
    },
    {
        label: 'withHash',
        insertText: '#withHash',
        documentation: {
            value: 'Block helper that builds the context for the block from the options hash.',
        },
    },
    {
        label: 'bytes',
        insertText: 'bytes',
        documentation: {
            value: 'Format a number to its equivalent in bytes. If a string is passed, its length will be formatted and returned.',
        },
    },
    {
        label: 'addCommas',
        insertText: 'addCommas',
        documentation: {
            value: 'Add commas to numbers',
        },
    },
    {
        label: 'phoneNumber',
        insertText: 'phoneNumber',
        documentation: {
            value: 'Convert a string or number to a formatted phone number.',
        },
    },
    {
        label: 'toAbbr',
        insertText: 'toAbbr',
        documentation: {
            value: 'Abbreviate numbers to the given number of `precision`. This is for general numbers, not size in bytes.',
        },
    },
    {
        label: 'toExponential',
        insertText: 'toExponential',
        documentation: {
            value: 'Returns a string representing the given number in exponential notation.',
        },
    },
    {
        label: 'toFixed',
        insertText: 'toFixed',
        documentation: {
            value: 'Formats the given number using fixed-point notation.',
        },
    },
    {
        label: 'toFloat',
        insertText: 'toFloat',
        documentation: {
            value: '',
        },
    },
    {
        label: 'toInt',
        insertText: 'toInt',
        documentation: {
            value: '',
        },
    },
    {
        label: 'toPrecision',
        insertText: 'toPrecision',
        documentation: {
            value: 'Returns a string representing the `Number` object to the specified precision.',
        },
    },
    {
        label: 'extend',
        insertText: 'extend',
        documentation: {
            value: 'Extend the context with the properties of other objects. A shallow merge is performed to avoid mutating the context.',
        },
    },
    {
        label: 'forIn',
        insertText: '#forIn',
        documentation: {
            value: 'Block helper that iterates over the properties of an object, exposing each key and value on the context.',
        },
    },
    {
        label: 'forOwn',
        insertText: '#forOwn',
        documentation: {
            value: 'Block helper that iterates over the **own** properties of an object, exposing each key and value on the context.',
        },
    },
    {
        label: 'toPath',
        insertText: 'toPath',
        documentation: {
            value: 'Take arguments and, if they are string or number, convert them to a dot-delineated object property path.',
        },
    },
    {
        label: 'get',
        insertText: '#get',
        documentation: {
            value: ' Use property paths (`a.b.c`) to get a value or nested value from the context. Works as a regular helper or block helper.',
        },
    },
    {
        label: 'getObject',
        insertText: 'getObject',
        documentation: {
            value: 'Use property paths (`a.b.c`) to get an object from the context. Differs from the `get` helper in that this helper will return the actual object, including the given property key. Also, this helper does not work as a block helper.',
        },
    },
    {
        label: 'hasOwn',
        insertText: 'hasOwn',
        documentation: {
            value: 'Return true if `key` is an own, enumerable property of the given `context` object.',
        },
    },
    {
        label: 'isObject',
        insertText: 'isObject',
        documentation: {
            value: 'Return true if `value` is an object.',
        },
    },
    {
        label: 'JSONparse',
        insertText: 'JSONparse',
        description: '{{JSONparse string}}',
        documentation: {
            value: 'Parses the given string using `JSON.parse`.\n```handlebars\n<!-- string: \'{"foo": "bar"}\' -->\n{{JSONparse string}}\n<!-- results in: { foo: \'bar\' } -->\n```',
        },
    },
    {
        label: 'JSONstringify',
        insertText: 'JSONstringify',
        documentation: {
            value: 'Stringify an object using `JSON.stringify`.',
        },
    },
    {
        label: 'merge',
        insertText: 'merge',
        documentation: {
            value: 'Deeply merge the properties of the given `objects` with the context object.',
        },
    },
    {
        label: 'pick',
        insertText: 'pick',
        documentation: {
            value: 'Pick properties from the context object.',
        },
    },
    {
        label: 'absolute',
        insertText: 'absolute',
        documentation: {
            value: 'Get the directory path segment from the given `filepath`.',
        },
    },

    {
        label: 'dirname',
        insertText: 'dirname',
        documentation: {
            value: 'Get the directory path segment from the given `filepath`.',
        },
    },
    {
        label: 'relative',
        insertText: 'relative',
        documentation: {
            value: 'Get the relative filepath from `a` to `b`.',
        },
    },
    {
        label: 'basename',
        insertText: 'basename',
        documentation: {
            value: 'Get the file extension from the given `filepath`.',
        },
    },
    {
        label: 'stem',
        insertText: 'stem',
        documentation: {
            value: ' Get the "stem" from the given `filepath`.',
        },
    },
    {
        label: 'extname',
        insertText: 'extname',
        documentation: {
            value: 'Get the file extension from the given `filepath`.',
        },
    },
    {
        label: 'resolve',
        insertText: 'resolve',
        documentation: {
            value: 'Resolve an absolute path from the given `filepath`.',
        },
    },
    {
        label: 'segments',
        insertText: 'segments',
        documentation: {
            value: 'Get specific (joined) segments of a file path by passing a range of array indices.',
        },
    },
    {
        label: 'toRegex',
        insertText: 'toRegex',
        documentation: {
            value: 'Convert the given string to a regular expression.',
        },
    },
    {
        label: 'test',
        insertText: 'test',
        documentation: {
            value: 'Returns true if the given `str` matches the given regex. A regex can be passed on the context, or using the [toRegex](#toregex) helper as a subexpression.',
        },
    },
    {
        label: 'append',
        insertText: 'append',
        documentation: {
            value: 'Append the specified `suffix` to the given string.',
        },
    },
    {
        label: 'camelcase',
        insertText: 'camelcase',
        documentation: {
            value: 'camelCase the characters in the given `string`.',
        },
    },
    {
        label: 'capitalize',
        insertText: 'capitalize',
        documentation: {
            value: 'Capitalize the first word in a sentence.',
        },
    },
    {
        label: 'capitalizeAll',
        insertText: 'capitalizeAll',
        documentation: {
            value: 'Capitalize all words in a string.',
        },
    },
    {
        label: 'center',
        insertText: 'center',
        documentation: {
            value: 'Center a string using non-breaking spaces',
        },
    },
    {
        label: 'chop',
        insertText: 'chop',
        documentation: {
            value: 'Like trim, but removes both extraneous whitespace **and non-word characters** from the beginning and end of a string.',
        },
    },
    {
        label: 'dashcase',
        insertText: 'dashcase',
        documentation: {
            value: 'dash-case the characters in `string`. Replaces non-word characters and periods with hyphens.',
        },
    },
    {
        label: 'dotcase',
        insertText: 'dotcase',
        documentation: {
            value: 'dot.case the characters in `string`.',
        },
    },
    {
        label: 'ellipsis',
        insertText: 'ellipsis',
        documentation: {
            value: 'Truncates a string to the specified `length`, and appends it with an elipsis, `…`.',
        },
    },
    {
        label: 'hyphenate',
        insertText: 'hyphenate',
        documentation: {
            value: 'Replace spaces in a string with hyphens.',
        },
    },
    {
        label: 'isString',
        insertText: 'isString',
        documentation: {
            value: 'Return true if `value` is a string.',
        },
    },
    {
        label: 'lowercase',
        insertText: 'lowercase',
        documentation: {
            value: 'Lowercase all characters in the given string.',
        },
    },
    {
        label: 'occurrences',
        insertText: 'occurrences',
        documentation: {
            value: 'Return the number of occurrences of `substring` within the given `string`.',
        },
    },
    {
        label: 'pascalcase',
        insertText: 'pascalcase',
        documentation: {
            value: 'PascalCase the characters in `string`.',
        },
    },
    {
        label: 'pathcase',
        insertText: 'pathcase',
        documentation: {
            value: 'path/case the characters in `string`.',
        },
    },
    {
        label: 'plusify',
        insertText: 'plusify',
        documentation: {
            value: 'Replace spaces in the given string with pluses.',
        },
    },
    {
        label: 'prepend',
        insertText: 'prepend',
        documentation: {
            value: 'Prepends the given `string` with the specified `prefix`.',
        },
    },
    {
        label: 'raw',
        insertText: 'raw',
        documentation: {
            value: 'Render a block without processing mustache templates inside the block.',
        },
    },
    {
        label: 'remove',
        insertText: 'remove',
        documentation: {
            value: 'Remove all occurrences of `substring` from the given `str`.',
        },
    },
    {
        label: 'removeFirst',
        insertText: 'removeFirst',
        documentation: {
            value: 'Remove the first occurrence of `substring` from the given `str`.',
        },
    },
    {
        label: 'replace',
        insertText: 'replace',
        documentation: {
            value: 'Replace all occurrences of substring `a` with substring `b`.',
        },
    },
    {
        label: 'replaceFirst',
        insertText: 'replaceFirst',
        documentation: {
            value: '',
        },
    },
    {
        label: 'reverse',
        insertText: 'reverse',
        documentation: {
            value: 'Reverse a string.',
        },
    },
    {
        label: 'sentence',
        insertText: 'sentence',
        documentation: {
            value: 'Sentence case the given string',
        },
    },
    {
        label: 'snakecase',
        insertText: 'snakecase',
        documentation: {
            value: 'snake_case the characters in the given `string`.',
        },
    },
    {
        label: 'split',
        insertText: 'split',
        documentation: {
            value: 'Split `string` by the given `character`.',
        },
    },
    {
        label: 'startsWith',
        insertText: '#startsWith',
        documentation: {
            value: 'Tests whether a string begins with the given prefix.',
        },
    },
    {
        label: 'titleize',
        insertText: 'titleize',
        documentation: {
            value: 'Title case the given string.',
        },
    },
    {
        label: 'trim',
        insertText: 'trim',
        documentation: {
            value: 'Removes extraneous whitespace from the beginning and end of a string.',
        },
    },
    {
        label: 'trimLeft',
        insertText: 'trimLeft',
        documentation: {
            value: 'Removes extraneous whitespace from the beginning of a string.',
        },
    },
    {
        label: 'trimRight',
        insertText: 'trimRight',
        documentation: {
            value: 'Removes extraneous whitespace from the end of a string.',
        },
    },
    {
        label: 'truncate',
        insertText: 'truncate',
        documentation: {
            value: 'Truncate a string to the specified `length`. Also see [ellipsis](#ellipsis).',
        },
    },
    {
        label: 'truncateWords',
        insertText: 'truncateWords',
        documentation: {
            value: 'Truncate a string to have the specified number of words. Also see [truncate](#truncate).',
        },
    },
    {
        label: 'uppercase',
        insertText: 'uppercase',
        documentation: {
            value: 'Uppercase all of the characters in the given string. If used as a block helper it will uppercase the entire block. This helper does not support inverse blocks.',
        },
    },
    {
        label: 'encodeURI',
        insertText: 'encodeURI',
        documentation: {
            value: 'Encodes a Uniform Resource Identifier (URI) component by replacing each instance of certain characters by one, two, three, or four escape sequences representing the UTF-8 encoding of the character.',
        },
    },
    {
        label: 'escape',
        insertText: 'escape',
        documentation: {
            value: 'Escape the given string by replacing characters with escape sequences. Useful for allowing the string to be used in a URL, etc.',
        },
    },
    {
        label: 'decodeURI',
        insertText: 'decodeURI',
        documentation: {
            value: 'Decode a Uniform Resource Identifier (URI) component.',
        },
    },
    {
        label: 'urlResolve',
        insertText: 'urlResolve',
        documentation: {
            value: 'Take a base URL, and a href URL, and resolve them as a browser would for an anchor tag.',
        },
    },
    {
        label: 'urlParse',
        insertText: 'urlParse',
        documentation: {
            value: 'Parses a `url` string into an object.',
        },
    },
    {
        label: 'stripQuerystring',
        insertText: 'stripQuerystring',
        documentation: {
            value: 'Strip the query string from the given `url`.',
        },
    },
    {
        label: 'stripProtocol',
        insertText: 'stripProtocol',
        documentation: {
            value: 'Strip protocol from a `url`. Useful for displaying media that may have an http protocol on secure connections.',
        },
    },
] as EditorSuggestion[];

/**
 * Builds a CompletionItem from a Handlebars-Helpers EditorSuggestion to be used by the CompletionItemProvider.
 *
 * @param instance An instance of the editor to provide enum values.
 * @param suggestion An Editor Suggestion object.
 *
 * @return A CompletionItem object built from the suggestion.
 */
const createCompletionItem = (instance: Monaco, suggestion: EditorSuggestion): languages.CompletionItem => {
    return {
        label: suggestion.label,
        kind: instance.languages.CompletionItemKind.Function,
        detail: suggestion.description,
        documentation: {
            value: `${suggestion.documentation?.value}\n\n[Handlebars-Helpers Reference](https://github.com/helpers/handlebars-helpers/blob/master/README.md#helpers)`,
        },
        insertText: suggestion.insertText,
        range: null as any,
        sortText: 'helpers',
    } as languages.CompletionItem;
};

export const buildHelpersCompletionItems = (instance: Monaco): languages.CompletionItem[] => {
    const suggestions = handlebarsHelpersSuggestions;
    return suggestions.map(s => createCompletionItem(instance, s)) as languages.CompletionItem[];
};
