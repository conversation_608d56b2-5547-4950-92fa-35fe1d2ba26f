import * as monacoEditor from 'monaco-editor';
import { editor, languages, Position } from 'monaco-editor';
import { buildHelpersCompletionItems } from './handlebars-helpers';
import { EditorSuggestion } from './index';

type Monaco = typeof monacoEditor;
type TagPositions = {
    start: Position;
    end: Position;
};
type TagObject = {
    name: string;
    text: string;
    type: string;
    position: Position;
};

/**
 * Finds the start and end positions of the closest previous Handlebars tag (opening or closing) to the current position in the model.
 * The search stops at the beginning of the model.
 *
 * @param model An ITextModel object containing the text of the editor.
 * @param startingPosition A Position object indicating the current position of the cursor in the model.
 * @param tagRegExp A RegExp describing the beginning and ending tags to find.
 *
 * @return A TagPositions object, which contains the start and end positions, or null.
 */
function findPreviousTagPositions(
    model: editor.ITextModel,
    startingPosition: Position,
    tagRegExp: string,
): TagPositions | null {
    const found = model.findPreviousMatch(tagRegExp, startingPosition, true, false, null, true);

    /**
     * The findPreviousMatch method will loop past the beginning of the text in the model so
     * we must test the location of the match to make sure it is before the starting position
     */
    if (
        !found ||
        startingPosition.isBeforeOrEqual({
            lineNumber: found.range.startLineNumber,
            column: found.range.startColumn,
        } as Position)
    ) {
        return null;
    }

    return {
        start: new Position(found.range.startLineNumber, found.range.startColumn),
        end: new Position(found.range.endLineNumber, found.range.endColumn),
    };
}

/**
 * Finds the closest previous Handlebars tag (opening or closing) to the current position in the model and return a TagObject describing it.
 *
 * @param model An ITextModel object containing the text of the editor.
 * @param currentPosition A Position object indicating the current position of the cursor in the model.
 * @param tags A string array of tag names for which to search.
 *
 * @return A TagObject object or null.
 */
function getPreviousTag(model: editor.ITextModel, currentPosition: Position, tags: string[]): TagObject | null {
    const tagsString = tags.join('|');
    const foundTag = findPreviousTagPositions(
        model,
        currentPosition,
        `\\{\\{\\s*#(${tagsString})\\s*[\\w\\.\\[\\]]+\\}\\}|\\{\\{\\s*\\/(${tagsString})\\s*\\}\\}`,
    );
    if (!foundTag || currentPosition.isBefore(foundTag?.end)) {
        return null;
    }

    const tagText = model
        .getValueInRange({
            startLineNumber: foundTag.start.lineNumber,
            startColumn: foundTag.start.column + 2,
            endLineNumber: foundTag.end.lineNumber,
            endColumn: foundTag.end.column - 2,
        })
        .trim();

    const tagObject = {} as TagObject;
    tagObject.type = tagText.charAt(0);
    tagObject.name = tagText.match(/\w+/)?.[0] || '';
    tagObject.text = tagText.replace(`${tagObject.type + tagObject.name}`, '').trim();
    tagObject.position = new Position(foundTag.start.lineNumber, foundTag.start.column);

    return tagObject;
}

/**
 * Builds a concatenated string of the properties included in opening Handlebars context tags.
 * Any opening context tag that does not have a corresponding closing tag before the current position is included.
 * *
 * @param model An ITextModel object containing the text of the editor.
 * @param currentPosition A Position object indicating the current position of the cursor in the model.
 *
 * @return A dot-case string of the context properties. For example, 'xtremPurchasing.purchaseOrder.query'.
 */
const getContext = (model: editor.ITextModel, currentPosition: Position): string | null => {
    const context: string[] = [];
    const closers: string[] = [];

    const contextTags = ['with', 'each'];

    let position = currentPosition;
    const modelStart = new Position(0, 0);
    while (modelStart.isBefore(position)) {
        const tag = getPreviousTag(model, position, contextTags);
        if (tag) {
            if (tag.type === '/') {
                closers.push(tag.name);
            } else if (tag.name === closers[closers.length - 1]) {
                closers.pop();
            } else {
                context.splice(0, 0, tag.text);
            }
            position = tag.position;
        } else {
            position = new Position(0, 0);
        }
    }
    const result = context.join('.');

    return result.length ? `${result}.` : null;
};

/**
 * Builds a CompletionItem from an EditorSuggestion to be used by the CompletionItemProvider.
 *
 * @param instance An instance of the editor to provide enum values.
 * @param suggestion An Editor Suggestion object.
 *
 * @return A CompletionItem object built from the suggestion.
 */
function createCompletionItem(instance: Monaco, suggestion: EditorSuggestion): languages.CompletionItem {
    return {
        label: suggestion.label,
        kind: suggestion.kind
            ? instance.languages.CompletionItemKind[suggestion.kind]
            : instance.languages.CompletionItemKind.Keyword,
        detail: suggestion.description,
        documentation: suggestion.documentation,
        insertTextRules: suggestion.insertTextRules || null,
        insertText: suggestion.insertText,
        range: null as any,
        sortText: suggestion.sortText || '!',
    } as languages.CompletionItem;
}

function processSegments(text: string, suggestions: EditorSuggestion[], isContext?: boolean): EditorSuggestion[] {
    const segments = text.split('.').filter(s => s.trim() !== '');
    const segmentCount = segments.length - 1;
    let found;
    let children: EditorSuggestion[] = suggestions;

    const dotEnd = text[text.length - 1] === '.';

    segments.forEach((segment, index, segmentArray) => {
        const isLast = index === segmentCount;

        // Is this segment an array value? It can have square brackets or not.
        if (/(^\[\d+\]$|^\d+$)/.test(segment)) {
            found = children![0];
        } else {
            found = children?.find(c => c.insertText === segment);
        }

        if (isLast) {
            if (found && dotEnd) {
                if (found.isArray && !isContext) {
                    // Return suggestion of '[0]' and select number inside brackets
                    children = [
                        {
                            label: '[]',
                            // eslint-disable-next-line no-template-curly-in-string
                            insertText: '[${0:0}]',
                            insertTextRules: languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        },
                    ];
                } else {
                    children = found.children || [];
                }
            } else {
                children = children?.filter(s => s.insertText.includes(segment));
            }
        } else if (found) {
            if (found.isArray && /(^\[\d+\]$|^\d+$)/.test(segmentArray[index + 1])) {
                // Insert dummy suggestion to be children[0] for the array value in the next split
                children = [
                    {
                        label: '[0]',
                        insertText: '[0]',
                        children: found.children,
                    },
                ];
            } else {
                children = found.children || [];
            }
        } else {
            children = [];
        }
    });

    return children;
}

/**
 * Builds an array of CompletionItems based on given EditorSuggestions and the current position within the model.
 *
 * @param instance An instance of the editor to provide enum values.
 * @param suggestions An array of EditorSuggestion types. These suggestions will be converted into completion items for the editor.
 * @param model An ITextModel object containing the text of the editor.
 * @param position A Position object indicating the current position of the cursor in the model.
 *
 * @return An array of CompletionItems that can be consumed by the editor and displayed.
 */
export const buildCompletionItems = (
    instance: Monaco,
    suggestions: EditorSuggestion[],
    model: editor.ITextModel,
    position: Position,
): languages.CompletionItem[] => {
    let results: EditorSuggestion[] = [];
    let itemList: languages.CompletionItem[] = [];
    let includeHandlebarsHelpers = false;

    // Find the previous opening braces
    const word = model.getWordUntilPosition(position);
    let text = word.word;

    const prevOpenBraces = model.findPreviousMatch(
        '{{',
        {
            lineNumber: position.lineNumber,
            column: position.column,
        },
        false,
        false,
        null,
        true,
    );

    // If no opening braces then leave
    if (!prevOpenBraces) {
        return [];
    }

    // Check for closing braces within the text from the opening braces to the current position
    text = model.getValueInRange({
        ...prevOpenBraces.range,
        endLineNumber: position.lineNumber,
        endColumn: position.column,
    });
    // If closing braces were found then we are not inside a handlebars expression, so leave
    if (text.indexOf('}}') !== -1) {
        return [];
    }
    // Trim off the leading braces and tag name
    text = text.replace(/^{+/, '').trimStart();
    text = text.replace(/[#/]\w+\s*/, '');

    // Determine if we are in a specific context
    const context = getContext(model, position);

    // If we are in the first segment of the path then include all suggestions.
    if ((context + text).indexOf('.') === -1) {
        results = suggestions;
        includeHandlebarsHelpers = true;
    } else if (text[text.length - 1] === ' ') {
        // If there a space just before the position then there is no appropriate suggestion.
        results = [];
    } else {
        // If context exists then get its suggestions before looking for the text's suggestions
        const contextResults = context ? processSegments(context, suggestions, true) : suggestions;
        results = processSegments(text, contextResults);
    }

    // Convert suggestions into completion items
    itemList = results.map(s => createCompletionItem(instance, s)) as languages.CompletionItem[];
    if (includeHandlebarsHelpers) {
        itemList.push(...buildHelpersCompletionItems(instance));
    }

    return itemList;
};
