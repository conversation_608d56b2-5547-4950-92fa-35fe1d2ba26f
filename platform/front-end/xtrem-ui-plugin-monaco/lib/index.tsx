/* eslint-disable react/jsx-indent-props */
/* eslint-disable react/jsx-indent */
import * as ui from '@sage/xtrem-ui';
import * as React from 'react';
import MonacoEditor from 'react-monaco-editor';
import * as monacoEditor from 'monaco-editor';
import './styles.css';
import { buildCompletionItems } from './handlebars-utils';
// @ts-expect-error - This is the monaco editor private interface for the internal tabFocus implementation
import { TabFocus } from 'monaco-editor/esm/vs/editor/browser/config/tabFocus';

export interface EditorSuggestion {
    label: string;
    insertText: string;
    description?: string;
    documentation?: monacoEditor.IMarkdownString;
    children?: EditorSuggestion[];
    sortText?: string;
    kind?: keyof typeof monacoEditor.languages.CompletionItemKind;
    isArray?: boolean;
    insertTextRules?: monacoEditor.languages.CompletionItemInsertTextRule;
}

export interface MonacoPluginProperties {
    /**
     * Programming/markup language of the editor content. The editor validates the content against the syntax and rules of the selected
     * language.
     **/
    language:
    | 'css'
    | 'graphql'
    | 'handlebars'
    | 'html'
    | 'javascript'
    | 'json'
    | 'markdown'
    | 'scss'
    | 'typescript'
    | 'text'
    | 'xml'
    | 'yaml';

    /** Height of the editor in pixels. */
    height?: number;
    /** Property name suggestions to be displayed when using the handlebars language */
    handlebarsSuggestions?: EditorSuggestion[] | null;
}

type MonacoEditorProps = ui.plugin.XtremUiPluginComponentProps<MonacoPluginProperties, string | null>;

interface MonacoEditorState {
    value: string | null;
}

class MonacoEditorField extends React.Component<MonacoEditorProps, MonacoEditorState> {
    private handlebarsCompletionItemProvider: monacoEditor.IDisposable;

    constructor(props: MonacoEditorProps) {
        super(props);
        this.state = { value: props.value || null };
    }

    /**
     *  Update the `value` in the state if new props are sent and the value is not the same as the component is aware of.
     **/
    override UNSAFE_componentWillReceiveProps(nextProps: MonacoEditorProps) {
        this.setState(currentState => {
            const stateValue = this.state && currentState.value ? currentState.value : null;
            const propsValue = nextProps.value || null;
            return { value: propsValue !== stateValue ? propsValue : stateValue };
        });
    }

    override componentWillUnmount() {
        this.handlebarsCompletionItemProvider.dispose();
    }

    editorDidMount = (editor: monacoEditor.editor.IStandaloneCodeEditor) => {
        const { setFieldValue } = this.props;

        editor.addAction({
            id: 'breakTrap',
            label: '',
            run: ()=> {
                TabFocus.setTabFocusMode(true);
            }
        });

        editor.addAction({
            id: 'restoreTrap',
            label: '',
            run: ()=> {
                TabFocus.setTabFocusMode(false);
            }
        });

        // user can use "Esc" key to get out of the focus trap, without overriding widget usages preconditions
        editor.addAction({
            id: 'onEscape',
            label: '',
            keybindings: [monacoEditor.KeyCode.Escape],
            precondition: 'editorTextFocus && !suggestWidgetVisible',
            run: (ed) => {
                ed.getAction('breakTrap')?.run();
            },
        });

        editor.onDidChangeModelContent(() => {
           editor.getAction('restoreTrap')?.run();
        });

        editor.onDidBlurEditorWidget(() => {
            const { value } = this.state;
            setFieldValue(value);
            editor.getAction('restoreTrap')?.run();
        });

        this.loadHandlebarsSuggestions();
    };

    onChange = (value: string) => {
        this.setState({ value });
    };

    loadHandlebarsSuggestions() {
        const { fieldProperties } = this.props;

        this.handlebarsCompletionItemProvider = monacoEditor.languages.registerCompletionItemProvider('handlebars', {
            triggerCharacters: ['{', '.'],
            provideCompletionItems: (model, position) => {
                const inboundItems = buildCompletionItems(
                    monacoEditor,
                    fieldProperties.handlebarsSuggestions || [],
                    model,
                    position,
                );

                return {
                    suggestions: inboundItems,
                } as monacoEditor.languages.ProviderResult<monacoEditor.languages.CompletionList>;
            },
        });
    }

    override render() {
        const { value } = this.state;
        const { fieldProperties, browser, value: propValue, screenId, fixedHeight } = this.props;

        if (browser && browser.lessThan.xs) {
            return (
                <div>
                    {ui.localize(
                        '@sage/xtrem-ui-plugin-monaco/mobile-not-supported',
                        'Code editing on mobile devices is not supported.',
                    )}
                </div>
            );
        }

        const isReadOnly =
            ui.plugin.isFieldReadOnly(screenId, fieldProperties, propValue, null) ||
            ui.plugin.isFieldDisabled(screenId, fieldProperties, propValue, null);

        monacoEditor.editor.defineTheme('readOnly', {
            base: 'vs',
            inherit: true,
            colors: { 'editor.background': ui.tokens.colorsUtilityMajor025 },
            rules: [],
        });

        const options: monacoEditor.editor.IStandaloneEditorConstructionOptions = {
            selectOnLineNumbers: true,
            automaticLayout: true,
            minimap: { enabled: false },
            readOnly: isReadOnly,
        };
        if (this.handlebarsCompletionItemProvider) {
            this.handlebarsCompletionItemProvider.dispose();
            this.loadHandlebarsSuggestions();
        }

        return (
            <div>
                <MonacoEditor
                    height={fieldProperties.height || fixedHeight || 300}
                    language={fieldProperties.language || 'typescript'}
                    theme={isReadOnly ? 'readOnly' : 'vs'}
                    value={value || ''}
                    options={options}
                    onChange={this.onChange}
                    editorDidMount={this.editorDidMount}
                />
            </div>
        );
    }
}

export default {
    name: 'monaco',
    component: MonacoEditorField,
    createFieldQuery: (): ui.plugin.QueryProperty => {
        return { value: true };
    },
    transformFromGraphValue: (rawValueFromQueryResult: any) => {
        // eslint-disable-next-line react/destructuring-assignment
        if (rawValueFromQueryResult && rawValueFromQueryResult.value) {
            return rawValueFromQueryResult.value;
        }
        return null;
    },
    transformToGraphValue: (value: any) => {
        return value ? { value } : null;
    },
} as ui.plugin.XtremUiPlugin<MonacoPluginProperties, string>;
