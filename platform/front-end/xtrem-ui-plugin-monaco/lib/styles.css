@font-face {
    font-family: codicon;
    src: url('/plugins/QHNhZ2UveHRyZW0tdWktcGx1Z2luLW1vbmFjbw==/build/codicon.ttf');
    font-weight: normal;
    font-style: normal;
}

.react-monaco-editor-container .monaco-editor {
    border: 1px solid #668592;
}
.react-monaco-editor-container .monaco-editor.focused {
    outline: 3px solid #ffb500;
}

.e-monaco-plugin-disabled {
    pointer-events: none;
}

.e-monaco-plugin-disabled .monaco-editor,
.e-monaco-plugin-disabled .monaco-editor-background,
.e-monaco-plugin-disabled .monaco-editor .margin,
.e-monaco-plugin-disabled .monaco-editor .inputarea.ime-input {
    background-color: rgb(242, 245, 246);
}

.e-monaco-plugin-disabled .monaco-editor .lines-content {
    opacity: 0.6;
}
