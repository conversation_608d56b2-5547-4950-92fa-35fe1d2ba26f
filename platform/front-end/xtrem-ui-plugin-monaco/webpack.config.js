const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');
const packageFile = require('./package.json');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const path = require('path');

const iconFontFile = path.resolve(
    path.dirname(require.resolve('@vscode/codicons/package.json')),
    'dist',
    'codicon.ttf',
);
const buildDir = path.resolve(__dirname, 'build');
const pluginNameHash = Buffer.from(packageFile.name).toString('base64');

module.exports = {
    mode: 'development',
    optimization: {
        minimize: false,
    },
    devtool: 'eval-source-map',
    module: {
        rules: [
            {
                test: /\.m?js/,
                resolve: {
                    fullySpecified: false,
                },
            },
        ],
    },
    resolve: {
        symlinks: true,
    },
    plugins: [
        new MonacoWebpackPlugin({
            monacoEditorPath: path.resolve(__dirname, 'node_modules/monaco-editor'),
            publicPath: `/plugins/${pluginNameHash}/build/`,
            languages: [
                'css',
                'graphql',
                'handlebars',
                'html',
                'javascript',
                'json',
                'markdown',
                'scss',
                'typescript',
                'xml',
                'yaml',
            ],
        }),
        new CopyWebpackPlugin({
            patterns: [
                {
                    from: iconFontFile,
                    to: buildDir,
                },
            ],
        }),
    ],
};
