import * as accepts from 'accepts';
import { Hand<PERSON>, Request, static as statc } from 'express';
import * as fsp from 'path';

function isStaticResource(request: Request) {
    return accepts(request).types(['html', 'json']) === 'html';
}

const staticPath = fsp.join(fsp.dirname(require.resolve('@sage/xtrem-graphql-demo-page/package.json')), 'build');
const staticApp = statc(staticPath);

export const graphiqlMiddleware: Handler = (req, res, next) => {
    return isStaticResource(req) ? staticApp(req, res, next) : next();
};
