{"extends": "../tsconfig", "compilerOptions": {"baseUrl": ".", "outDir": "build", "rootDir": ".", "target": "es2022", "composite": true, "paths": {"timers": ["node_modules/timers-browserify"], "stream": ["node_modules/stream-browserify"]}}, "include": ["index.ts", "./lib"], "exclude": ["./lib/**/*.test.ts", "./lib/**/*.test.tsx"], "references": [{"path": "../xtrem-client"}, {"path": "../../shared/xtrem-date-time"}, {"path": "../../shared/xtrem-decimal"}, {"path": "../../shared/xtrem-shared"}, {"path": "../xtrem-ui"}, {"path": "../xtrem-ui-components"}, {"path": "../../cli/xtrem-cli-transformers"}, {"path": "../xtrem-static-shared"}]}