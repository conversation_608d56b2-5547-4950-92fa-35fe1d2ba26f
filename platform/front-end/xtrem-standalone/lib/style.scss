@import '~@sage/xtrem-ui/build/vendor-xtrem-ui-style.css';
@import '~@sage/xtrem-ui/build/main-xtrem-ui-style.css';
@import '~@sage/design-tokens/css/base.css';

@import './mixins.scss';
@import './variables.scss';

@font-face {
  font-family: $fontCarbonIcons;
  src: url('/fonts/carbon-icons-webfont.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: $fontAdelle;
  src: url('/fonts/AdelleSansSAGE-Bold.eot');
  src: url('/fonts/AdelleSansSAGE-Bold.eot?#iefix') format('embedded-opentype'),
    url('/fonts/AdelleSansSAGE-Bold.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: $fontAdelle;
  src: url('/fonts/AdelleSansSAGE-Bold.eot');
  src: url('/fonts/AdelleSansSAGE-Bold.eot?#iefix') format('embedded-opentype'), url('/fonts/AdelleSansSAGE-Bold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Sage UI font family */
@font-face {
  font-family: 'Sage UI';
  src: url('https://fonts.sage.com/Sage_UI-Regular.woff2') format('woff2'),
    url('https://fonts.sage.com/Sage_UI-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* Sage Headline font family */
@font-face {
  font-family: "Sage Headline";
  src: url('https://fonts.sage.com/Sage_Headline-Black.woff2') format("woff2"), url('https://fonts.sage.com/Sage_Headline-Black.woff') format("woff");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Sage Text font family */
@font-face {
  font-family: "Sage Text";
  src: url('https://fonts.sage.com/Sage_Text-Light.woff2') format("woff2"), url('https://fonts.sage.com/Sage_Text-Light.woff') format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Sage Text";
  src: url('https://fonts.sage.com/Sage_Text-Light_Italic.woff2') format("woff2"), url('https://fonts.sage.com/Sage_Text-Light_Italic.woff') format("woff");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Sage Text";
  src: url('https://fonts.sage.com/Sage_Text-Regular.woff2') format("woff2"), url('https://fonts.sage.com/Sage_Text-Regular.woff') format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Sage Text";
  src: url('https://fonts.sage.com/Sage_Text-Italic.woff2') format("woff2"), url('https://fonts.sage.com/Sage_Text-Italic.woff') format("woff");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Sage Text";
  src: url('https://fonts.sage.com/Sage_Text-Medium.woff2') format("woff2"), url('https://fonts.sage.com/Sage_Text-Medium.woff') format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Sage Text";
  src: url('https://fonts.sage.com/Sage_Text-Medium_Italic.woff2') format("woff2"), url('https://fonts.sage.com/Sage_Text-Medium_Italic.woff') format("woff");
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Sage Text";
  src: url('https://fonts.sage.com/Sage_Text-Bold.woff2') format("woff2"), url('https://fonts.sage.com/Sage_Text-Bold.woff') format("woff");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Sage Text";
  src: url('https://fonts.sage.com/Sage_Text-Bold_Italic.woff2') format("woff2"), url('https://fonts.sage.com/Sage_Text-Bold_Italic.woff') format("woff");
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Sage UI';
  src: url('https://fonts.sage.com/Sage_UI-Medium.woff2') format('woff2'),
    url('https://fonts.sage.com/Sage_UI-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Sage UI';
  src: url('https://fonts.sage.com/Sage_UI-Medium.woff2') format('woff2'),
    url('https://fonts.sage.com/Sage_UI-Medium.woff') format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Sage UI';
  src: url('https://fonts.sage.com/Sage_UI-Bold.woff2') format('woff2'),
    url('https://fonts.sage.com/Sage_UI-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

#root {
  overflow: hidden;

  // set a max height for carbon's popover
  div[role="menu"][data-element="additional-buttons"] {
    max-height: 80%;
    overflow-y: auto;
  }
}

html,
body {
  color: var(--colorsYin090);
  font-size: 12px;
  font-family: var(--fontFamiliesDefault);
}

.xe-body {
  display: flex;
  height: 100%;
  background: var(--colorsUtilityMajor025);
}

.xe-app-container {
  flex: 1;
  height: 100vh;
  overflow-y: auto;
  padding-top: 48px;
  box-sizing: border-box;
  background: var(--colorsUtilityMajor025);

  .e-xtrem-controller {
    margin-top: 0;
    height: 100%;
    background: var(--colorsUtilityMajor025);
    border-left: 1px solid var(--colorsUtilityMajor100);
    box-sizing: border-box;
  }
}
