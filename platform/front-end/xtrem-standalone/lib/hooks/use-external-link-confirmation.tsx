import { useCallback, useState } from 'react';
import type { ExternalLinkConfirmationDialogProps } from '../component/external-link-confirmation/exterrnal-link-confirmation-dialog';
import type { Resolver } from './create-promise';
import { createPromise } from './create-promise';
import { useSkipExternalLinkConfirmation } from './use-local-storage';

export interface UseExternalLinkConfirmationOptions {
    onCancel: ExternalLinkConfirmationDialogProps['onCancel'];
    onConfirm: ExternalLinkConfirmationDialogProps['onConfirm'];
}

export interface UseExternalLinkConfirmationResult {
    getConfirmation: () => Promise<void>;
    isOpen: boolean;
    onCancel: ExternalLinkConfirmationDialogProps['onCancel'];
    onConfirm: ExternalLinkConfirmationDialogProps['onConfirm'];
}

export const useExternalLinkConfirmation = ({
    onCancel: onControlledCancel,
    onConfirm: onControlledConfirm,
}: UseExternalLinkConfirmationOptions): UseExternalLinkConfirmationResult => {
    const [confirmationResolver, setConfirmationResolver] = useState<Resolver<void>>();
    const [isOpen, setOpen] = useState<boolean>(false);
    const [isSkipExternalLinkConfirmation, setSkipExternalLinkConfirmation] = useSkipExternalLinkConfirmation();

    const storeSkipExternalLinkConfirmation = useCallback(
        (isSkipConfirmationChecked: boolean) => {
            if (isSkipConfirmationChecked && !isSkipExternalLinkConfirmation) {
                setSkipExternalLinkConfirmation(true);
            }
        },
        [isSkipExternalLinkConfirmation, setSkipExternalLinkConfirmation],
    );

    const getConfirmation = useCallback(async () => {
        setOpen(true);
        const [promise, resolver] = createPromise<void>();
        setConfirmationResolver(resolver);
        return promise;
    }, [setConfirmationResolver]);

    const onCancel = useCallback(
        async (isSkipConfirmationChecked: boolean) => {
            storeSkipExternalLinkConfirmation(isSkipConfirmationChecked);
            setOpen(false);
            onControlledCancel(isSkipConfirmationChecked);
            confirmationResolver?.reject();
        },
        [storeSkipExternalLinkConfirmation, onControlledCancel, confirmationResolver],
    );

    const onConfirm = useCallback(
        async (isSkipConfirmationChecked: boolean) => {
            storeSkipExternalLinkConfirmation(isSkipConfirmationChecked);
            setOpen(false);
            onControlledConfirm(isSkipConfirmationChecked);
            confirmationResolver?.resolve();
        },
        [storeSkipExternalLinkConfirmation, onControlledConfirm, confirmationResolver],
    );

    return { getConfirmation, isOpen, onCancel, onConfirm };
};
