import { useEffect, useState } from 'react';

export const useLocalStorage = <T = any>(key: string, initValue: T): [T, React.Dispatch<React.SetStateAction<T>>] => {
    const [value, setValue] = useState<T>(() => {
        let currValue: T;

        try {
            currValue = JSON.parse(localStorage.getItem(key) || String(initValue));
        } catch (error) {
            currValue = initValue;
        }

        return currValue;
    });

    useEffect(() => {
        localStorage.setItem(key, JSON.stringify(value));
    }, [key, value]);

    return [value, setValue];
};

export const SKIP_EXTERNAL_LINK_CONFIRMATION_KEY = 'xe-notifications-skip-external-link-confirmation-key';
export const useSkipExternalLinkConfirmation = () => {
    const [value, setValue] = useState<boolean>(() => {
        return isSkipExternalLinkConfirmation();
    });

    useEffect(() => {
        localStorage.setItem(SKIP_EXTERNAL_LINK_CONFIRMATION_KEY, JSON.stringify(value));
    }, [value]);

    return [value, setValue] as const;
};

export const isSkipExternalLinkConfirmation = () => {
    let currValue: boolean;
    try {
        currValue = JSON.parse(localStorage.getItem(SKIP_EXTERNAL_LINK_CONFIRMATION_KEY) || String(false));
    } catch (error) {
        currValue = false;
    }

    return currValue;
};
