export interface Resolver<T = any> {
    resolve: (value: T | PromiseLike<T>) => void;
    reject: (reason?: any) => void;
}

export type CreatePromiseResult<T = any> = [Promise<T>, Resolver<T> | undefined];

export const createPromise = <T = any>(): CreatePromiseResult<T> => {
    let resolver: Resolver<T> | undefined;
    return [
        new Promise<T>((resolve, reject) => {
            resolver = { resolve, reject };
        }),
        resolver,
    ];
};
