import { renderHook } from '@testing-library/react';
import { createPromise, type Resolver } from '../create-promise';

describe('createPromise', () => {
    it('should return array with the created promise and resolver', () => {
        const { result } = renderHook(() => createPromise());
        const [promise, resolver] = result.current;
        expect(promise).toBeInstanceOf(Promise);
        expect(resolver).toBeDefined();
        expect(Object.keys(resolver as Resolver<any>)).toEqual(['resolve', 'reject']);
    });
});
