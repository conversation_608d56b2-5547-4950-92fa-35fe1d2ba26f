import '@testing-library/jest-dom';
import { renderHook, waitFor } from '@testing-library/react';
import { useExternalLinkConfirmation } from '../use-external-link-confirmation';

describe('useExternalLinkConfirmation', () => {
    afterEach(() => {
        if (localStorage.getItem('xe-notifications-skip-external-link-confirmation-key')) {
            localStorage.removeItem('xe-notifications-skip-external-link-confirmation-key');
        }

        jest.restoreAllMocks();
    });

    // TODO: Flake test excluded
    xit('should return expected dialog props', async () => {
        const { result, rerender } = renderHook(() =>
            useExternalLinkConfirmation({
                onCancel: jest.fn(),
                onConfirm: jest.fn(),
            }),
        );
        const { getConfirmation, isOpen, onConfirm } = result.current;

        getConfirmation();
        rerender();

        waitFor(() => {
            expect(isOpen).toEqual(true);
        });

        onConfirm(false);
    });

    // TODO: Flake test excluded
    xit('should invoke provided onCancel when called', () => {
        const onCancelMock = jest.fn();
        const { result } = renderHook(() =>
            useExternalLinkConfirmation({
                onCancel: onCancelMock,
                onConfirm: jest.fn(),
            }),
        );
        const props = result.current;

        props.getConfirmation();
        props.onCancel(false);

        waitFor(() => {
            expect(onCancelMock).toHaveBeenCalledWith(false);
        });
    });
});
