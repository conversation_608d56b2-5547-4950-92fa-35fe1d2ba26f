import { renderHook, waitFor } from '@testing-library/react';
import { isSkipExternalLinkConfirmation, useLocalStorage, useSkipExternalLinkConfirmation } from '../use-local-storage';

describe('useLocalStorage', () => {
    beforeEach(() => {
        localStorage.setItem('real-key', JSON.stringify('real-value'));
    });

    it('should return provided init value and value setter given unused key', () => {
        const { result } = renderHook(() => useLocalStorage('fake-key', 'init-value'));
        const [value, setValue] = result.current;
        expect(value).toEqual('init-value');
        expect(setValue).toBeInstanceOf(Function);
    });

    it('should return stored value and value setter given used key', async () => {
        const { result } = renderHook(() => useLocalStorage('real-key', 'init-value'));
        const [value, setValue] = result.current;

        await waitFor(() => {
            expect(value).toEqual('real-value');
            expect(setValue).toBeInstanceOf(Function);
        });
    });

    it('should store the underlying value when calling setValue with a new value', async () => {
        const { result } = renderHook(() => useLocalStorage('fake-key', 'init-value'));
        expect(result.current[0]).toEqual('init-value');
        result.current[1]('real-value');

        await waitFor(() => {
            result.current[0] = 'real-value';
        });
    });
});

describe('isSkipExternalLinkConfirmation', () => {
    afterEach(() => {
        if (localStorage.getItem('xe-notifications-skip-external-link-confirmation-key')) {
            localStorage.removeItem('xe-notifications-skip-external-link-confirmation-key');
        }
    });

    it('should return false by default', () => {
        const { result } = renderHook(() => isSkipExternalLinkConfirmation());
        expect(result.current).toEqual(false);
    });

    it('should return true if skip external link confirmation key is set', () => {
        localStorage.setItem('xe-notifications-skip-external-link-confirmation-key', JSON.stringify(true));
        const { result } = renderHook(() => isSkipExternalLinkConfirmation());
        expect(result.current).toEqual(true);
    });
});

describe('useSkipExternalLinkConfirmation', () => {
    afterEach(() => {
        if (localStorage.getItem('xe-notifications-skip-external-link-confirmation-key')) {
            localStorage.removeItem('xe-notifications-skip-external-link-confirmation-key');
        }
    });

    it('should return expected value and value setter', () => {
        const { result } = renderHook(() => useSkipExternalLinkConfirmation());
        const [value, setValue] = result.current;
        expect(value).toEqual(false);
        expect(setValue).toBeInstanceOf(Function);
    });

    it('should return expected value and value setter if skip external link confirmation key is set', () => {
        localStorage.setItem('xe-notifications-skip-external-link-confirmation-key', JSON.stringify(true));
        const { result } = renderHook(() => useSkipExternalLinkConfirmation());
        const [value, setValue] = result.current;
        expect(value).toEqual(true);
        expect(setValue).toBeInstanceOf(Function);
    });

    it('should set the value correctly when using the value setter', async () => {
        const { result } = renderHook(() => useSkipExternalLinkConfirmation());
        expect(result.current[0]).toEqual(false);
        result.current[1](true);

        await waitFor(() => {
            expect(result.current[0]).toEqual(true);
        });
    });
});
