import type { RootMenuItem, SitemapEntry } from '@sage/xtrem-shared';
import { sortBy } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import type * as actionTypes from '../standalone-redux/action-types';
import * as actions from '../standalone-redux/actions';
import type { XtremStandaloneState } from '../standalone-redux/state';
import './mobile-navigation.scss';
import { SitemapSubMenu } from './sitemap-submenu';

export interface XtremNavigationProps {
    sitemap: (RootMenuItem & SitemapEntry)[];
    isNavigationOpen: boolean;
    isExtraSmall: boolean;
    path: string;
    setNavigationOpen: (isOpen: boolean) => void;
    onInternalNavigationChange: (path: string) => void;
}

export function MobileNavigation(props: XtremNavigationProps) {
    if (!props.isNavigationOpen || !props.isExtraSmall) {
        return null;
    }

    const onSelectLink = (path: string) => (event: React.MouseEvent) => {
        event.preventDefault();
        props.setNavigationOpen(false);
        props.onInternalNavigationChange(path);
    };

    return (
        <nav className="xe-navigation-mobile" aria-label="Navigation menu" data-testid="xe-navigation-menu">
            <ul className="xe-navigation-secondary-body-list">
                {sortBy(props.sitemap, ['priority', 'title']).map(p => (
                    <SitemapSubMenu
                        key={p.id}
                        sitemapEntry={p}
                        level={1}
                        onSelectLink={onSelectLink}
                        path={props.path}
                    />
                ))}
            </ul>
        </nav>
    );
}

const mapStateToProps = (state: XtremStandaloneState): XtremNavigationProps => ({
    path: state.path,
    sitemap: state.sitemap as any,
    isNavigationOpen: state.isNavigationOpen,
    isExtraSmall: state.browser.is.xs,
    onInternalNavigationChange: actions.actionStub,
    setNavigationOpen: actions.actionStub,
});

const mapDispatchToProps = (dispatch: actionTypes.AppThunkDispatch) => ({
    onInternalNavigationChange: (path: string) => dispatch(actions.onInternalNavigationChange(path, true)),
    setNavigationOpen: (isOpen: boolean) => dispatch(actions.setNavigationOpen(isOpen)),
});

// tslint:disable-next-line:variable-name
export const ConnectedMobileNavigation = connect(mapStateToProps, mapDispatchToProps)(MobileNavigation);
