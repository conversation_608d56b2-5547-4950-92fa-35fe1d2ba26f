import { localize } from '../../service/standalone-i18n-service';
import * as React from 'react';
import type { UserDetails } from '../../service/auth-service';
import type { Dict } from '@sage/xtrem-shared';
import Icon from 'carbon-react/esm/components/icon';
import type { TenantItem } from '../../service/tenants-service';
import Link from 'carbon-react/esm/components/link';
import { getProfileMenuItems, ProfileSettingsTab } from './get-profile-menu-items';
import { ConnectedLanguageSelector } from './language-selector';
import { MyProfile } from './my-profile';
import './profile-settings.scss';

export interface ProfileSettingsProps {
    user: UserDetails | null;
    localeOnChange: (locale: string | null) => void;
    translations: Dict<string>;
    loginService: string | null;
    tenantItem: TenantItem;
}

const renderProfileContent = (
    tab: ProfileSettingsTab,
    user: ProfileSettingsProps['user'],
    translations: ProfileSettingsProps['translations'],
    localeOnChange: (locale: string | null) => void,
): React.ReactNode => {
    switch (tab) {
        case ProfileSettingsTab.MyProfileTab:
            return (
                <div className="xe-profile-my-profile-content">
                    <MyProfile user={user} translations={translations} />
                </div>
            );
        case ProfileSettingsTab.LanguageTab:
            return (
                <div className="xe-profile-language-content">
                    <ConnectedLanguageSelector
                        user={user}
                        translations={translations}
                        localeOnChange={localeOnChange}
                    />
                </div>
            );
        default:
            return null;
    }
};

export function ProfileSettingsPanel(props: ProfileSettingsProps): React.JSX.Element {
    const [currentTab, setTab] = React.useState(ProfileSettingsTab.MyProfileTab);

    return (
        <div className="xe-profile-container">
            <div className="xe-profile-header">
                <span>
                    {localize(
                        '@sage/xtrem-standalone/personal-settings',
                        'Personal settings',
                        {},
                        props.translations,
                        props.user?.locale ?? undefined,
                    )}
                </span>
            </div>
            <div className="xe-profile-body">
                <div className="xe-profile-menu">
                    <div className="xe-profile-menu-items-container">
                        {getProfileMenuItems(props.user, props.translations).map(item => (
                            <button
                                type="button"
                                key={item.key}
                                onClick={() => setTab(item.key)}
                                className={
                                    item.key === currentTab
                                        ? 'xe-profile-menu-item xe-profile-menu-item-selected'
                                        : 'xe-profile-menu-item'
                                }
                            >
                                <span>{item.text}</span>
                            </button>
                        ))}
                    </div>
                    <div className="xe-profile-menu-logout">
                        <Icon color="#FFFF" type="logout" />
                        <a
                            data-component="link"
                            href={`${props.loginService}/logout?fromUrl=${document.location.href}`}
                            className="xe-profile-menu-logout-text"
                        >
                            {localize(
                                '@sage/xtrem-standalone/logout',
                                'Logout',
                                {},
                                props.translations,
                                props.user?.locale ?? undefined,
                            )}
                        </a>
                    </div>
                </div>
                <div className="xe-profile-content">
                    {renderProfileContent(currentTab, props.user, props.translations, props.localeOnChange)}
                    <div className="xe-profile-links">
                        <div className="xe-profile-menu-privacy-preferences-link">
                            <Link href={props.tenantItem.oneTrustMagicLink}>
                                {localize(
                                    '@sage/xtrem-standalone/privacy-preferences',
                                    'Privacy Preferences',
                                    {},
                                    props.translations,
                                    props.user?.locale ?? undefined,
                                )}
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

ProfileSettingsPanel.displayName = 'ProfileSettingsPanel';
