import type { Dict } from '@sage/xtrem-shared';
import * as React from 'react';
import type { UserDetails } from '../../service/auth-service';
import { localize } from '../../service/standalone-i18n-service';

export interface MyProfileProps {
    user: UserDetails | null;
    translations: Dict<string>;
}

export function MyProfile({ user, translations }: MyProfileProps): React.JSX.Element {
    return (
        <>
            <div className="xe-profile-photo-container">
                {user?.photo ? (
                    <img src={`data:image;base64,${user.photo}`} className="xe-profile-photo" />
                ) : (
                    <div className="xe-profile-picture-large xe-profile-picture-empty" />
                )}
            </div>
            <div className="xe-profile-text-container">
                <div className="xe-profile-text-hello">
                    {localize('@sage/xtrem-standalone/hello', 'Hello', {}, translations, user?.locale ?? undefined)}
                </div>
                <div className="xe-profile-text-name">{user?.firstName ?? null}</div>
                <div className="xe-profile-text-name">{user?.lastName ?? null}</div>
                <div className="xe-profile-text-email">{user?.email ?? null}</div>
            </div>
        </>
    );
}

MyProfile.displayName = 'MyProfile';
