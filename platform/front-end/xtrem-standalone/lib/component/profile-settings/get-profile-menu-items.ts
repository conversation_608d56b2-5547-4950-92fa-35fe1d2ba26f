import type { Dict } from '@sage/xtrem-shared';
import type { UserDetails } from '../../service/auth-service';
import { localize } from '../../service/standalone-i18n-service';

export enum ProfileSettingsTab {
    MyProfileTab = 'MyProfile',
    LanguageTab = 'Language',
}

export interface ProfileMenuItemDescription {
    key: ProfileSettingsTab;
    text: string;
}

export const getProfileMenuItems = (
    user: UserDetails | null,
    translations: Dict<string>,
): ProfileMenuItemDescription[] => {
    return [
        {
            key: ProfileSettingsTab.MyProfileTab,
            text: localize(
                '@sage/xtrem-standalone/my-profile',
                'My profile',
                {},
                translations,
                user?.locale ?? undefined,
            ),
        },
        {
            key: ProfileSettingsTab.LanguageTab,
            text: localize('@sage/xtrem-standalone/language', 'Language', {}, translations, user?.locale ?? undefined),
        },
    ];
};
