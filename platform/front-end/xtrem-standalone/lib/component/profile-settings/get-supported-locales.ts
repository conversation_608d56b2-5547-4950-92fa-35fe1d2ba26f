import type { Dict } from '@sage/xtrem-shared';
import type { UserDetails } from '../../service/auth-service';
import { localize } from '../../service/standalone-i18n-service';

export interface LocaleDescription {
    key: string;
    text: string;
}

export const getSupportedLocales = ({
    user,
    translations,
    locales,
}: {
    user: UserDetails | null;
    translations: Dict<string>;
    locales: string[];
}): LocaleDescription[] => {
    const languages = [
        {
            key: 'en-US',
            text: localize(
                '@sage/xtrem-standalone/language-en-us',
                'English (United States)',
                {},
                translations,
                user?.locale ?? undefined,
            ),
        },
        {
            key: 'en-GB',
            text: localize(
                '@sage/xtrem-standalone/language-en-gb',
                'English (United Kingdom)',
                {},
                translations,
                user?.locale ?? undefined,
            ),
        },
        {
            key: 'de-DE',
            text: localize(
                '@sage/xtrem-standalone/language-de-de',
                'German (Germany)',
                {},
                translations,
                user?.locale ?? undefined,
            ),
        },
        {
            key: 'fr-FR',
            text: localize(
                '@sage/xtrem-standalone/language-fr-fr',
                'French (France)',
                {},
                translations,
                user?.locale ?? undefined,
            ),
        },
        {
            key: 'es-ES',
            text: localize(
                '@sage/xtrem-standalone/language-es-es',
                'Spanish (Spain)',
                {},
                translations,
                user?.locale ?? undefined,
            ),
        },
        {
            key: 'pt-PT',
            text: localize(
                '@sage/xtrem-standalone/language-pt-pt',
                'Portuguese (Portugal)',
                {},
                translations,
                user?.locale ?? undefined,
            ),
        },
        {
            key: 'pt-BR',
            text: localize(
                '@sage/xtrem-standalone/language-pt-br',
                'Portuguese (Brazil)',
                {},
                translations,
                user?.locale ?? undefined,
            ),
        },
        {
            key: 'zh-CN',
            text: localize(
                '@sage/xtrem-standalone/language-zh-cn',
                'Chinese (Simplified)',
                {},
                translations,
                user?.locale ?? undefined,
            ),
        },
        {
            key: 'ar-SA',
            text: localize(
                '@sage/xtrem-standalone/language-ar-sa',
                'Arabic (Saudi Arabia)',
                {},
                translations,
                user?.locale ?? undefined,
            ),
        },
        {
            key: 'it-IT',
            text: localize(
                '@sage/xtrem-standalone/language-it-it',
                'Italian (Italy)',
                {},
                translations,
                user?.locale ?? undefined,
            ),
        },
        {
            key: 'pl-PL',
            text: localize(
                '@sage/xtrem-standalone/language-pl-pl',
                'Polish (Poland)',
                {},
                translations,
                user?.locale ?? undefined,
            ),
        },
    ];
    return (locales.length > 0 ? languages.filter(locale => locales.includes(locale.key)) : languages).sort((a, b) =>
        a.text.localeCompare(b.text),
    );
};
