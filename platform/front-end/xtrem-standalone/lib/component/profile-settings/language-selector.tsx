import type { Dict } from '@sage/xtrem-shared';
import Button from 'carbon-react/esm/components/button';
import { RadioButton, RadioButtonGroup } from 'carbon-react/esm/components/radio-button';
import * as React from 'react';
import type { UserDetails } from '../../service/auth-service';
import { localize } from '../../service/standalone-i18n-service';
import { getSupportedLocales } from './get-supported-locales';
import type { XtremStandaloneState } from '../../standalone-redux/state';
import { connect } from 'react-redux';

interface LanguageSelectorExternalProps {
    locales: string[];
}
export interface LanguageSelectorProps extends LanguageSelectorExternalProps {
    user: UserDetails | null;
    localeOnChange: (locale: string | null) => void;
    translations: Dict<string>;
}

export function LanguageSelector({
    user,
    translations,
    localeOnChange,
    locales,
}: LanguageSelectorProps): React.JSX.Element {
    const [selectedLocale, setSelectedLocale] = React.useState<string | null>(user?.locale || null);

    return (
        <>
            <div
                className="xe-profile-language-selector-radio-button-container"
                data-testid="xe-profile-language-selector"
            >
                <RadioButtonGroup
                    data-testid="xe-profile-language-selector-locale-selection"
                    name="locales"
                    onChange={e => setSelectedLocale(e.target.value as string)}
                    value={selectedLocale ?? undefined}
                >
                    {getSupportedLocales({ user, translations, locales }).map(locale => (
                        <RadioButton
                            color="colorsYang100"
                            className="xe-profile-language-selector-radio-button"
                            data-testid={`xe-profile-language-selector-locale-selection-${locale.key}`}
                            fieldHelp={locale.key}
                            fieldHelpInline={true}
                            key={locale.key}
                            label={locale.text}
                            value={locale.key}
                        />
                    ))}
                </RadioButtonGroup>
            </div>
            <div className="xe-profile-language-apply">
                <Button
                    buttonType="primary"
                    data-testid="xe-profile-language-selector-apply"
                    onClick={() => localeOnChange(selectedLocale)}
                >
                    {localize(
                        '@sage/xtrem-standalone/apply-locale-selection',
                        'Apply',
                        {},
                        translations,
                        user?.locale || undefined,
                    )}
                </Button>
            </div>
        </>
    );
}

const mapStateToProps = (state: XtremStandaloneState): LanguageSelectorExternalProps => ({
    locales: state.config?.locales ?? [],
});

export const ConnectedLanguageSelector = connect(mapStateToProps)(LanguageSelector);

LanguageSelector.displayName = 'LanguageSelector';
