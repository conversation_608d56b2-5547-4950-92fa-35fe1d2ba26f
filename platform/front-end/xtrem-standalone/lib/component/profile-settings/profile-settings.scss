.xe-profile-container {
    width: 675px;
    height: 412px;
    background-color: var(--colorsGray1000);
    left: auto;
    right: 0;
    margin-top: 0;
    opacity: 0;
    position: absolute;
    transform: translate(0, 10px);
    transition: all 0.2s ease;
    visibility: hidden;

    .xe-profile-header {
        padding: 17px 24px;
        color: var(--colorsYang100);
        height: 60px;
        box-sizing: border-box;
        font-family: var(--fontFamiliesDefault);
        font-style: normal;
        font-weight: 700;
        font-size: 16px;
        line-height: 24px;
        border-bottom: 1px solid var(--colorsYang100);
    }

    .xe-profile-body {
        display: flex;
        height: 352px;

        .xe-profile-menu {
            width: 150px;
            display: flex;
            flex-flow: row wrap;
            border-right: 2px solid var(--colorsYang100);
            box-sizing: border-box;
            height: 352px;

            .xe-profile-menu-items-container {
                display: flex;
                flex-flow: column nowrap;
                width: 100%;
                align-items: flex-end;

                .xe-profile-menu-item {
                    border: none;
                    background: none;
                    height: 24px;
                    color: var(--colorsYang100);
                    font-family: var(--fontFamiliesDefault);
                    font-style: normal;
                    font-weight: var(--fontWeights700);
                    font-size: 14px;
                    line-height: 24px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    margin-bottom: 10px;
                    padding-right: 12px;
                    border-right: 4px solid transparent;

                    &:first-child {
                        margin-top: 20px;
                    }

                    &.xe-profile-menu-item-selected {
                        border-right: 4px solid var(--colorsActionMajor500);
                    }

                    &:hover {
                        border-right: 4px solid var(--colorsActionMajor500);
                    }
                }
            }

            .xe-profile-menu-logout {
                display: flex;
                align-self: flex-end;
                margin-bottom: 36px;
                margin-left: 16px;

                .xe-profile-menu-logout-text {
                    font-family: var(--fontFamiliesDefault);
                    margin-left: 4px;
                    color: var(--colorsYang100);
                    font-weight: var(--fontWeights500);
                    font-size: 16px;
                    line-height: 24px;
                    text-decoration-line: underline;

                    &:focus {
                        outline: 3px solid var(--colorsSemanticFocus500);
                    }
                }
            }
        }

        .xe-profile-content {
            display: flex;
            flex: 1;
            margin-left: 24px;
            margin-right: 24px;
            margin-top: 24px;
            flex-direction: column;
            justify-content: space-between;

            .xe-profile-links {
                display: flex;
                align-self: flex-end;

                .xe-profile-menu-privacy-preferences-link {
                    margin: 16px 0;
                    font-family: var(--fontFamiliesDefault);
                    font-weight: var(--fontWeights500);
                    font-size: 16px;
                    line-height: 24px;
                    text-decoration-line: underline;

                    &:focus {
                        outline: 3px solid var(--colorsSemanticFocus500);
                    }
                }

                .xe-profile-menu-privacy-preferences-link a {
                    color: var(--colorsYang100);
                    background-color: transparent;
                }
            }

            .xe-profile-my-profile-content {
                display: flex;
                align-self: stretch;

                .xe-profile-photo-container .xe-profile-photo {
                    width: 120px;
                    height: 120px;
                }

                .xe-profile-text-container {
                    margin-left: 24px;
                    color: var(--colorsYang100);

                    .xe-profile-text-hello {
                        font-family: var(--fontFamiliesDefault);
                        font-weight: var(--fontWeights700);
                        font-size: 18px;
                        line-height: 24px;
                        margin-bottom: 4px;
                    }

                    .xe-profile-text-name {
                        font-family: var(--fontFamiliesDefault);
                        font-weight: var(--fontWeights700);
                        font-size: 32px;
                        line-height: 42px;
                    }

                    .xe-profile-text-email {
                        font-family: var(--fontFamiliesDefault);
                        font-weight: var(--fontWeights500);
                        font-size: 16px;
                        line-height: 19px;
                        margin-top: 16px;
                    }
                }
            }

            .xe-profile-language-content {
                width: 100%;

                .xe-profile-language-selector-radio-button-container {
                    overflow-y: auto;
                    height: 198px;
                    padding-left: 3px;
                    padding-top: 3px;

                    [data-component='radio-button'] {
                        label {
                            color: var(--colorsYang100);
                            font-family: var(--fontFamiliesDefault);
                            font-weight: var(--fontWeights500);
                            font-size: 14px;
                            line-height: 150%;
                        }

                        [data-element='help'] {
                            color: var(--colorsYang100);
                            font-family: var(--fontFamiliesDefault);
                            font-weight: var(--fontWeights500);
                            font-size: 14px;
                            line-height: 150%;
                            text-align: right;
                        }
                    }
                }

                .xe-profile-language-apply {
                    margin-top: 24px;
                    display: flex;
                    justify-content: flex-end;

                    button {
                        color: var(--colorsYang100);
                        font-family: var(--fontFamiliesDefault);
                        font-weight: var(--fontWeights500);
                        font-size: 14px;
                        line-height: 150%;
                    }
                }
            }
        }
    }
}