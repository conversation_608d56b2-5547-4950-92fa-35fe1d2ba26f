import { render } from '@testing-library/react';
import * as React from 'react';
import type { UserDetails } from '../../../service/auth-service';
import { MyProfile, MyProfileProps } from '../my-profile';

const user: UserDetails = {
    clientEncryptionKey: '',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    id: '1',
    lastName: 'Doe',
    locale: 'en-US',
    photo: null,
    pref: 0,
    tenantId: '1',
    uniqueTenantId: '1',
    uniqueUserId: '1',
    userCode: 'JD-001',
    uuid: '50b5a250-2b1b-435b-be9d-c99d22151f66',
    isOperator: false,
};

const translations = {
    'en-US': {
        '@sage/xtrem-standalone/hello': 'Hello',
    },
    'de-DE': {
        '@sage/xtrem-standalone/hello': 'Hallo',
    },
} as const;

const getProps = (locale?: 'en-US' | 'de-DE'): MyProfileProps => ({
    user: { ...user, locale: locale || null },
    translations: locale ? translations[locale] : {},
});

describe('MyProfile', () => {
    it('should render as expected in English (en-US)', () => {
        const props = getProps('en-US');
        const { container } = render(<MyProfile {...props} />);
        expect(container).toMatchSnapshot();
    });

    it('should render as expected in German (de-DE)', () => {
        const props = getProps('de-DE');
        const { container } = render(<MyProfile {...props} />);
        expect(container).toMatchSnapshot();
    });

    it('should render as expected in English (fallback)', () => {
        const props = getProps();
        const { container } = render(<MyProfile {...props} />);
        expect(container).toMatchSnapshot();
    });
});
