Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // deprecated
        removeListener: jest.fn(), // deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
    })),
});

jest.mock('carbon-react/esm/__internal__/utils/helpers/guid/index', () => (): string => {
    return 'testcarb-onco-mpon-ents-uniqguidmock';
});

import { fireEvent, render, waitFor } from '@testing-library/react';
import * as React from 'react';
import type { UserDetails } from '../../../service/auth-service';
import { LanguageSelector } from '../language-selector';

const user: UserDetails = {
    clientEncryptionKey: '',
    email: '<EMAIL>',
    firstName: 'John',
    id: '1',
    lastName: 'Doe',
    locale: 'en-US',
    photo: null,
    pref: 0,
    tenantId: '1',
    uniqueTenantId: '1',
    uniqueUserId: '1',
    userCode: 'JD-001',
    uuid: '50b5a250-2b1b-435b-be9d-c99d22151f66',
    isOperator: false,
};

const translations = {
    'en-US': {
        '@sage/xtrem-standalone/hello': 'Hello',
        '@sage/xtrem-standalone/personal-settings': 'Personal settings',
        '@sage/xtrem-standalone/apply-locale-selection': 'Apply',
    },
    'de-DE': {
        '@sage/xtrem-standalone/hello': 'Hallo',
        '@sage/xtrem-standalone/personal-settings': 'Persönliche Einstellungen',
        '@sage/xtrem-standalone/apply-locale-selection': 'Anwenden',
    },
} as const;

const locales = ['en-US', 'en-GB', 'de-DE', 'fr-FR', 'es-ES'];

const getProps = (locale?: 'en-US' | 'de-DE') => ({
    localeOnChange: jest.fn(),
    translations: locale ? translations[locale] : {},
    user: { ...user, locale: locale ?? null },
});

describe('LanguageSelector', () => {
    it('should render as expected in English (en-US)', () => {
        const props = getProps('en-US');
        const { container } = render(<LanguageSelector {...props} locales={locales} />);
        expect(container).toMatchSnapshot();
    });

    it('should render as expected in German (de-DE)', () => {
        const props = getProps('de-DE');
        const { container } = render(<LanguageSelector {...props} locales={locales} />);
        expect(container).toMatchSnapshot();
    });

    it('should render as expected in English (fallback)', () => {
        const props = getProps();
        const { container } = render(<LanguageSelector {...props} locales={locales} />);
        expect(container).toMatchSnapshot();
    });

    it('should call localeOnChange when language change is applied', async () => {
        const props = getProps('en-US');
        const { getByTestId } = render(<LanguageSelector {...props} locales={locales} />);

        const languageButton = getByTestId('xe-profile-language-selector-locale-selection-de-DE');
        fireEvent.click(languageButton);

        const applyButton = getByTestId('xe-profile-language-selector-apply');
        fireEvent.click(applyButton);

        await waitFor(() => {
            expect(props.localeOnChange).toHaveBeenCalledWith('de-DE');
        });
    });
});
