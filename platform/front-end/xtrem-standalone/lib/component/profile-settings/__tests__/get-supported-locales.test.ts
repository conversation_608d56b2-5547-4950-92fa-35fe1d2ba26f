import { Dict } from '@sage/xtrem-shared';
import type { UserDetails } from '../../../service/auth-service';
import { getSupportedLocales } from '../get-supported-locales';

const mockUser: UserDetails = {
    clientEncryptionKey: '',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    id: '1',
    lastName: 'Doe',
    locale: 'en-US',
    photo: null,
    pref: 0,
    tenantId: '1',
    uniqueTenantId: '1',
    uniqueUserId: '1',
    userCode: 'JD-001',
    uuid: '50b5a250-2b1b-435b-be9d-c99d22151f66',
    isOperator: false,
};

const locales = [
    'ar-SA',
    'de-DE',
    'en-GB',
    'en-US',
    'es-ES',
    'fr-FR',
    'it-IT',
    'pl-PL',
    'pt-BR',
    'pt-PT',
    'zh-CN',
] as const;

type MappedLocales = {
    [K in (typeof locales)[number]]: {
        [L in (typeof locales)[number] as `@sage/xtrem-standalone/language-${Lowercase<L>}`]: string;
    };
};

const translations: Partial<MappedLocales> = {
    'en-US': {
        '@sage/xtrem-standalone/language-en-us': 'English (United States)',
        '@sage/xtrem-standalone/language-en-gb': 'English (United Kingdom)',
        '@sage/xtrem-standalone/language-de-de': 'German (Germany)',
        '@sage/xtrem-standalone/language-fr-fr': 'French (France)',
        '@sage/xtrem-standalone/language-es-es': 'Spanish (Spain)',
        '@sage/xtrem-standalone/language-it-it': 'Italian (Italy)',
        '@sage/xtrem-standalone/language-pl-pl': 'Polish (Poland)',
        '@sage/xtrem-standalone/language-pt-br': 'Portuguese (Brazil)',
        '@sage/xtrem-standalone/language-pt-pt': 'Portuguese (Portugal)',
        '@sage/xtrem-standalone/language-zh-cn': 'Chinese (Simplified)',
        '@sage/xtrem-standalone/language-ar-sa': 'Arabic (Saudi Arabia)',
    },
    'de-DE': {
        '@sage/xtrem-standalone/language-en-us': 'Englisch (Vereinigte Staaten)',
        '@sage/xtrem-standalone/language-en-gb': 'Englisch (Vereinigtes Königreich)',
        '@sage/xtrem-standalone/language-de-de': 'Deutsch (Deutschland)',
        '@sage/xtrem-standalone/language-fr-fr': 'Französisch (Frankreich)',
        '@sage/xtrem-standalone/language-es-es': 'Spanisch (Spanien)',
        '@sage/xtrem-standalone/language-it-it': 'Italienisch (Italien)',
        '@sage/xtrem-standalone/language-pl-pl': 'Polnisch (Polen)',
        '@sage/xtrem-standalone/language-pt-br': 'Portugiesisch (Brasilien)',
        '@sage/xtrem-standalone/language-pt-pt': 'Portugiesisch (Portugal)',
        '@sage/xtrem-standalone/language-zh-cn': 'Chinesisch (Vereinfacht)',
        '@sage/xtrem-standalone/language-ar-sa': 'Arabisch (Saudi-Arabien)',
    },
} as const;

describe('getSupportedLocales', () => {
    it('should return all languages when locales array is empty', () => {
        const user: UserDetails | null = { ...mockUser, locale: 'en-US' };
        expect(
            getSupportedLocales({
                user,
                translations: translations[user.locale as 'en-US'] as Dict<string>,
                locales: [],
            }),
        ).toEqual([
            { key: 'ar-SA', text: 'Arabic (Saudi Arabia)' },
            { key: 'zh-CN', text: 'Chinese (Simplified)' },
            { key: 'en-GB', text: 'English (United Kingdom)' },
            { key: 'en-US', text: 'English (United States)' },
            { key: 'fr-FR', text: 'French (France)' },
            { key: 'de-DE', text: 'German (Germany)' },
            { key: 'it-IT', text: 'Italian (Italy)' },
            { key: 'pl-PL', text: 'Polish (Poland)' },
            { key: 'pt-BR', text: 'Portuguese (Brazil)' },
            { key: 'pt-PT', text: 'Portuguese (Portugal)' },
            { key: 'es-ES', text: 'Spanish (Spain)' },
        ]);
    });
    it('should return only French locale when locales array contains only "fr-FR"', () => {
        const user: UserDetails | null = { ...mockUser, locale: 'en-US' };
        expect(
            getSupportedLocales({
                user,
                translations: translations[user.locale as 'en-US'] as Dict<string>,
                locales: ['fr-FR'],
            }),
        ).toEqual([{ key: 'fr-FR', text: 'French (France)' }]);
    });
    it('should return expected supported locales in English (en-US)', () => {
        const user: UserDetails | null = { ...mockUser, locale: 'en-US' };
        expect(
            getSupportedLocales({
                user,
                translations: translations[user.locale as 'en-US'] as Dict<string>,
                locales: locales as any,
            }),
        ).toEqual([
            { key: 'ar-SA', text: 'Arabic (Saudi Arabia)' },
            { key: 'zh-CN', text: 'Chinese (Simplified)' },
            { key: 'en-GB', text: 'English (United Kingdom)' },
            { key: 'en-US', text: 'English (United States)' },
            { key: 'fr-FR', text: 'French (France)' },
            { key: 'de-DE', text: 'German (Germany)' },
            { key: 'it-IT', text: 'Italian (Italy)' },
            { key: 'pl-PL', text: 'Polish (Poland)' },
            { key: 'pt-BR', text: 'Portuguese (Brazil)' },
            { key: 'pt-PT', text: 'Portuguese (Portugal)' },
            { key: 'es-ES', text: 'Spanish (Spain)' },
        ]);
    });

    it('should return expected supported locales in German (de-DE)', () => {
        const user: UserDetails | null = { ...mockUser, locale: 'de-DE' };
        expect(
            getSupportedLocales({
                user,
                translations: translations[user.locale as 'de-DE'] as Dict<string>,
                locales: locales as any,
            }),
        ).toEqual([
            { key: 'ar-SA', text: 'Arabisch (Saudi-Arabien)' },
            { key: 'zh-CN', text: 'Chinesisch (Vereinfacht)' },
            { key: 'de-DE', text: 'Deutsch (Deutschland)' },
            { key: 'en-US', text: 'Englisch (Vereinigte Staaten)' },
            { key: 'en-GB', text: 'Englisch (Vereinigtes Königreich)' },
            { key: 'fr-FR', text: 'Französisch (Frankreich)' },
            { key: 'it-IT', text: 'Italienisch (Italien)' },
            { key: 'pl-PL', text: 'Polnisch (Polen)' },
            { key: 'pt-BR', text: 'Portugiesisch (Brasilien)' },
            { key: 'pt-PT', text: 'Portugiesisch (Portugal)' },
            { key: 'es-ES', text: 'Spanisch (Spanien)' },
        ]);
    });

    it('should return expected supported locales in English (fallback) given user with null as locale', () => {
        const user: UserDetails | null = { ...mockUser, locale: null };
        expect(getSupportedLocales({ user, translations: {}, locales: locales as any })).toEqual([
            { key: 'ar-SA', text: 'Arabic (Saudi Arabia)' },
            { key: 'zh-CN', text: 'Chinese (Simplified)' },
            { key: 'en-GB', text: 'English (United Kingdom)' },
            { key: 'en-US', text: 'English (United States)' },
            { key: 'fr-FR', text: 'French (France)' },
            { key: 'de-DE', text: 'German (Germany)' },
            { key: 'it-IT', text: 'Italian (Italy)' },
            { key: 'pl-PL', text: 'Polish (Poland)' },
            { key: 'pt-BR', text: 'Portuguese (Brazil)' },
            { key: 'pt-PT', text: 'Portuguese (Portugal)' },
            { key: 'es-ES', text: 'Spanish (Spain)' },
        ]);
    });

    it('should return expected supported locales in English (fallback) given null as user', () => {
        expect(getSupportedLocales({ user: null, translations: {}, locales: locales as any })).toEqual([
            { key: 'ar-SA', text: 'Arabic (Saudi Arabia)' },
            { key: 'zh-CN', text: 'Chinese (Simplified)' },
            { key: 'en-GB', text: 'English (United Kingdom)' },
            { key: 'en-US', text: 'English (United States)' },
            { key: 'fr-FR', text: 'French (France)' },
            { key: 'de-DE', text: 'German (Germany)' },
            { key: 'it-IT', text: 'Italian (Italy)' },
            { key: 'pl-PL', text: 'Polish (Poland)' },
            { key: 'pt-BR', text: 'Portuguese (Brazil)' },
            { key: 'pt-PT', text: 'Portuguese (Portugal)' },
            { key: 'es-ES', text: 'Spanish (Spain)' },
        ]);
    });
});
