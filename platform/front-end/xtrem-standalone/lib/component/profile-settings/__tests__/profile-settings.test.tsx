import { render } from '@testing-library/react';
import * as React from 'react';
import { ProfileSettingsPanel, ProfileSettingsProps } from '../index';
import { UserDetails } from '../../../service/auth-service';

const user: UserDetails = {
    clientEncryptionKey: '',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    id: '1',
    lastName: 'Doe',
    locale: 'en-US',
    photo: null,
    pref: 0,
    tenantId: '1',
    uniqueTenantId: '1',
    uniqueUserId: '1',
    userCode: 'JD-001',
    uuid: '50b5a250-2b1b-435b-be9d-c99d22151f66',
    isOperator: false,
};

const translations = {
    'en-US': {
        '@sage/xtrem-standalone/apply-locale-selection': 'Apply',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-action-cancel': 'Cancel',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-action-confirm': 'Continue',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-content-line-1':
            'This link takes you to an external website and opens in a new browser tab.',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-content-line-2':
            'Sage is not responsible for the content or privacy policies on that page.',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-skip-confirmation-label':
            "Don't show this message again",
        '@sage/xtrem-standalone/external-link-confirmation-dialog-subtitle': 'Proceed with caution.',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-title': 'External link',
        '@sage/xtrem-standalone/hello': 'Hello',
        '@sage/xtrem-standalone/help-menu': 'Help',
        '@sage/xtrem-standalone/home': 'Home',
        '@sage/xtrem-standalone/language': 'Language',
        '@sage/xtrem-standalone/language-de-de': 'German (Germany)',
        '@sage/xtrem-standalone/language-en-gb': 'English (United Kingdom)',
        '@sage/xtrem-standalone/language-en-us': 'English (United States)',
        '@sage/xtrem-standalone/language-es-es': 'Spanish (Spain)',
        '@sage/xtrem-standalone/language-fr-fr': 'French (France)',
        '@sage/xtrem-standalone/logout': 'Logout',
        '@sage/xtrem-standalone/maximize': 'Maximize',
        '@sage/xtrem-standalone/minimize': 'Minimize',
        '@sage/xtrem-standalone/my-profile': 'My profile',
        '@sage/xtrem-standalone/no-notification-description': "You'll get a notification when there's something new.",
        '@sage/xtrem-standalone/no-notification-title': "You're up to date.",
        '@sage/xtrem-standalone/notification-action-close': 'Close',
        '@sage/xtrem-standalone/notification-action-delete': 'Delete',
        '@sage/xtrem-standalone/notification-action-mark-read': 'Mark as read',
        '@sage/xtrem-standalone/notification-action-mark-unread': 'Mark as unread',
        '@sage/xtrem-standalone/notification-center': 'Notification center',
        '@sage/xtrem-standalone/notification-menu': 'Notifications',
        '@sage/xtrem-standalone/personal-settings': 'Personal settings',
        '@sage/xtrem-standalone/privacy-preferences': 'Privacy preferences',
        '@sage/xtrem-standalone/profile-settings-close': 'Close profile settings',
        '@sage/xtrem-standalone/profile-settings-open': 'Open profile settings',
        '@sage/xtrem-standalone/sage-copilot': 'Sage Copilot',
        '@sage/xtrem-standalone/skip-to-main-content': 'Skip to main content',
    },
    'de-DE': {
        '@sage/xtrem-standalone/apply-locale-selection': 'Anwenden',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-action-cancel': 'Abbrechen',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-action-confirm': 'Fortfahren',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-content-line-1':
            'Dieser Link führt Sie zu einer externen Website und wird in einem neuen Browser-Tab geöffnet.',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-content-line-2':
            'Sage ist nicht verantwortlich für den Inhalt oder die Datenschutzrichtlinien auf dieser Seite.',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-skip-confirmation-label':
            'Diese Meldung nicht mehr anzeigen.',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-subtitle':
            'Lesen Sie diese Meldung, bevor Sie fortfahren.',
        '@sage/xtrem-standalone/external-link-confirmation-dialog-title': 'Externer Link',
        '@sage/xtrem-standalone/hello': 'Hallo',
        '@sage/xtrem-standalone/help-menu': 'Hilfe',
        '@sage/xtrem-standalone/home': 'Start',
        '@sage/xtrem-standalone/language': 'Sprache',
        '@sage/xtrem-standalone/language-de-de': 'Deutsch (Deutschland)',
        '@sage/xtrem-standalone/language-en-gb': 'Englisch (Großbritannien)',
        '@sage/xtrem-standalone/language-en-us': 'Englisch (USA)',
        '@sage/xtrem-standalone/language-es-es': 'Spanisch (Spanien)',
        '@sage/xtrem-standalone/language-fr-fr': 'Französisch (Frankreich)',
        '@sage/xtrem-standalone/logout': 'Abmelden',
        '@sage/xtrem-standalone/maximize': 'Maximieren',
        '@sage/xtrem-standalone/minimize': 'Minimieren',
        '@sage/xtrem-standalone/my-profile': 'Mein Profil',
        '@sage/xtrem-standalone/no-notification-description':
            'Sie erhalten eine Benachrichtigung, wenn es etwas Neues gibt.',
        '@sage/xtrem-standalone/no-notification-title': 'Sie sind auf dem neusten Stand.',
        '@sage/xtrem-standalone/notification-action-close': 'Schließen',
        '@sage/xtrem-standalone/notification-action-delete': 'Löschen',
        '@sage/xtrem-standalone/notification-action-mark-read': 'Als gelesen markieren',
        '@sage/xtrem-standalone/notification-action-mark-unread': 'Als ungelesen markieren',
        '@sage/xtrem-standalone/notification-center': 'Benachrichtigungs-Center',
        '@sage/xtrem-standalone/notification-menu': 'Benachrichtigungen',
        '@sage/xtrem-standalone/personal-settings': 'Persönliche Einstellungen',
        '@sage/xtrem-standalone/privacy-preferences': 'Datenschutzeinstellungen',
        '@sage/xtrem-standalone/profile-settings': '{{state}} Profileinstellungen',
        '@sage/xtrem-standalone/profile-settings-close': 'Profileinstellungen schließen',
        '@sage/xtrem-standalone/profile-settings-open': 'Profileinstellungen öffnen',
        '@sage/xtrem-standalone/recent': 'Aktuell',
        '@sage/xtrem-standalone/sage-copilot': 'Sage Copilot',
        '@sage/xtrem-standalone/skip-to-main-content': 'Zum Hauptinhalt wechseln',
    },
} as const;

const getProps = (locale?: 'en-US' | 'de-DE'): ProfileSettingsProps => ({
    user: { ...user, locale: locale ?? null },
    translations: locale ? translations[locale] : {},
    localeOnChange: jest.fn(),
    loginService: 'http://login.service.com',
    tenantItem: { oneTrustMagicLink: 'http://one.trust.magic.link', tenantList: [] },
});

describe('ProfileSettingsPanel', () => {
    it('should render the component in English (en-US)', () => {
        const props = getProps('en-US');
        const { container } = render(<ProfileSettingsPanel {...props} />);
        expect(container).toMatchSnapshot();
    });

    it('should render the component in German (de-DE)', () => {
        const props = getProps('de-DE');
        const { container } = render(<ProfileSettingsPanel {...props} />);
        expect(container).toMatchSnapshot();
    });

    it('should render the component in English (en-US)', () => {
        const props = getProps();
        const { container } = render(<ProfileSettingsPanel {...props} />);
        expect(container).toMatchSnapshot();
    });
});
