// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LanguageSelector should render as expected in English (en-US) 1`] = `
<div>
  <div
    class="xe-profile-language-selector-radio-button-container"
    data-testid="xe-profile-language-selector"
  >
    <fieldset
      class="sc-fUnMCh lkegts"
      data-component="radiogroup"
    >
      <p
        class="sc-bXCLTC fwYniy"
        color="blackOpacity90"
      />
      <div
        class="sc-jsJBEP bcRIji"
        data-component="radio-button-group"
        role="radiogroup"
      >
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-en-GB"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="en-GB"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    English (United Kingdom)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  en-GB
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    checked=""
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-en-US"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="en-US"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    English (United States)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  en-US
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-fr-FR"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="fr-FR"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    French (France)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  fr-FR
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-de-DE"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="de-DE"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    German (Germany)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  de-DE
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-es-ES"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="es-ES"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    Spanish (Spain)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  es-ES
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </fieldset>
  </div>
  <div
    class="xe-profile-language-apply"
  >
    <button
      class="sc-imWYAI kIbAGq"
      data-component="button"
      data-testid="xe-profile-language-selector-apply"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="sc-dhKdcB egdbsU"
          data-element="main-text"
        >
          Apply
        </span>
      </span>
    </button>
  </div>
</div>
`;

exports[`LanguageSelector should render as expected in English (fallback) 1`] = `
<div>
  <div
    class="xe-profile-language-selector-radio-button-container"
    data-testid="xe-profile-language-selector"
  >
    <fieldset
      class="sc-fUnMCh lkegts"
      data-component="radiogroup"
    >
      <p
        class="sc-bXCLTC fwYniy"
        color="blackOpacity90"
      />
      <div
        class="sc-jsJBEP bcRIji"
        data-component="radio-button-group"
        role="radiogroup"
      >
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-en-GB"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="en-GB"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    English (United Kingdom)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  en-GB
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-en-US"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="en-US"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    English (United States)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  en-US
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-fr-FR"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="fr-FR"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    French (France)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  fr-FR
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-de-DE"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="de-DE"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    German (Germany)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  de-DE
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-es-ES"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="es-ES"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    Spanish (Spain)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  es-ES
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </fieldset>
  </div>
  <div
    class="xe-profile-language-apply"
  >
    <button
      class="sc-imWYAI kIbAGq"
      data-component="button"
      data-testid="xe-profile-language-selector-apply"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="sc-dhKdcB egdbsU"
          data-element="main-text"
        >
          Apply
        </span>
      </span>
    </button>
  </div>
</div>
`;

exports[`LanguageSelector should render as expected in German (de-DE) 1`] = `
<div>
  <div
    class="xe-profile-language-selector-radio-button-container"
    data-testid="xe-profile-language-selector"
  >
    <fieldset
      class="sc-fUnMCh lkegts"
      data-component="radiogroup"
    >
      <p
        class="sc-bXCLTC fwYniy"
        color="blackOpacity90"
      />
      <div
        class="sc-jsJBEP bcRIji"
        data-component="radio-button-group"
        role="radiogroup"
      >
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-en-GB"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="en-GB"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    English (United Kingdom)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  en-GB
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-en-US"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="en-US"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    English (United States)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  en-US
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-fr-FR"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="fr-FR"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    French (France)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  fr-FR
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    checked=""
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-de-DE"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="de-DE"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    German (Germany)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  de-DE
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="sc-ikkxIA sc-dAbbOL jrlJBt gSjygP"
          data-component="radio-button"
        >
          <div
            class="sc-fPXMVe eSVSjJ"
          >
            <div
              class="sc-dAlyuH ljmGCF"
            >
              <div
                class="sc-jlZhew hEounP"
                data-role="field-line"
              >
                <div
                  class="sc-eldPxv MXtZb"
                  data-role="checkable-input"
                >
                  <input
                    aria-describedby="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                    aria-invalid="false"
                    class="sc-eDPEul itgXLo xe-profile-language-selector-radio-button"
                    color="colorsYang100"
                    data-testid="xe-profile-language-selector-locale-selection-es-ES"
                    id="testcarb-onco-mpon-ents-uniqguidmock"
                    name="locales"
                    role="radio"
                    type="radio"
                    value="es-ES"
                  />
                  <div
                    class="sc-gFqAkR"
                  >
                    <svg
                      data-role="radio-svg"
                      focusable="false"
                      viewBox="0 0 15 15"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        stroke="none"
                        stroke-width="1"
                      >
                        <circle
                          class="radio-button-check"
                          cx="7.5"
                          cy="7.5"
                          fill="#FFFFFF"
                          r="5"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <div
                  class="sc-jEACwC dCavOa"
                  data-role="label-container"
                  id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                  width="30"
                >
                  <label
                    class="sc-cwHptR hwrDEM"
                    data-element="label"
                    for="testcarb-onco-mpon-ents-uniqguidmock"
                    id="testcarb-onco-mpon-ents-uniqguidmock-label"
                  >
                    Spanish (Spain)
                  </label>
                </div>
                <span
                  class="sc-kpDqfm gNGTCC"
                  data-element="help"
                  id="testcarb-onco-mpon-ents-uniqguidmock-field-help"
                >
                  es-ES
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </fieldset>
  </div>
  <div
    class="xe-profile-language-apply"
  >
    <button
      class="sc-imWYAI kIbAGq"
      data-component="button"
      data-testid="xe-profile-language-selector-apply"
      draggable="false"
      type="button"
    >
      <span>
        <span
          class="sc-dhKdcB egdbsU"
          data-element="main-text"
        >
          Anwenden
        </span>
      </span>
    </button>
  </div>
</div>
`;
