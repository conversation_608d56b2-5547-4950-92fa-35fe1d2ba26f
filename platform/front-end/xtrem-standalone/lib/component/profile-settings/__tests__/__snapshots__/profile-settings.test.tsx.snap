// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ProfileSettingsPanel should render the component in English (en-US) 1`] = `
<div>
  <div
    class="xe-profile-container"
  >
    <div
      class="xe-profile-header"
    >
      <span>
        Personal settings
      </span>
    </div>
    <div
      class="xe-profile-body"
    >
      <div
        class="xe-profile-menu"
      >
        <div
          class="xe-profile-menu-items-container"
        >
          <button
            class="xe-profile-menu-item xe-profile-menu-item-selected"
            type="button"
          >
            <span>
              My profile
            </span>
          </button>
          <button
            class="xe-profile-menu-item"
            type="button"
          >
            <span>
              Language
            </span>
          </button>
        </div>
        <div
          class="xe-profile-menu-logout"
        >
          <span
            class="sc-iGgWBj iWpVbl"
            color="#FFFF"
            data-component="icon"
            data-element="logout"
            data-role="icon"
            font-size="small"
            type="logout"
          />
          <a
            class="xe-profile-menu-logout-text"
            data-component="link"
            href="http://login.service.com/logout?fromUrl=http://localhost/"
          >
            Logout
          </a>
        </div>
      </div>
      <div
        class="xe-profile-content"
      >
        <div
          class="xe-profile-my-profile-content"
        >
          <div
            class="xe-profile-photo-container"
          >
            <div
              class="xe-profile-picture-large xe-profile-picture-empty"
            />
          </div>
          <div
            class="xe-profile-text-container"
          >
            <div
              class="xe-profile-text-hello"
            >
              Hello
            </div>
            <div
              class="xe-profile-text-name"
            >
              John
            </div>
            <div
              class="xe-profile-text-name"
            >
              Doe
            </div>
            <div
              class="xe-profile-text-email"
            >
              <EMAIL>
            </div>
          </div>
        </div>
        <div
          class="xe-profile-links"
        >
          <div
            class="xe-profile-menu-privacy-preferences-link"
          >
            <span
              class="sc-kpDqfm htmBFh"
              data-component="link"
            >
              <a
                data-role="link-anchor"
                href="http://one.trust.magic.link"
              >
                <span
                  class="sc-dAlyuH"
                >
                  Privacy preferences
                </span>
              </a>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`ProfileSettingsPanel should render the component in English (en-US) 2`] = `
<div>
  <div
    class="xe-profile-container"
  >
    <div
      class="xe-profile-header"
    >
      <span>
        Personal settings
      </span>
    </div>
    <div
      class="xe-profile-body"
    >
      <div
        class="xe-profile-menu"
      >
        <div
          class="xe-profile-menu-items-container"
        >
          <button
            class="xe-profile-menu-item xe-profile-menu-item-selected"
            type="button"
          >
            <span>
              My profile
            </span>
          </button>
          <button
            class="xe-profile-menu-item"
            type="button"
          >
            <span>
              Language
            </span>
          </button>
        </div>
        <div
          class="xe-profile-menu-logout"
        >
          <span
            class="sc-iGgWBj iWpVbl"
            color="#FFFF"
            data-component="icon"
            data-element="logout"
            data-role="icon"
            font-size="small"
            type="logout"
          />
          <a
            class="xe-profile-menu-logout-text"
            data-component="link"
            href="http://login.service.com/logout?fromUrl=http://localhost/"
          >
            Logout
          </a>
        </div>
      </div>
      <div
        class="xe-profile-content"
      >
        <div
          class="xe-profile-my-profile-content"
        >
          <div
            class="xe-profile-photo-container"
          >
            <div
              class="xe-profile-picture-large xe-profile-picture-empty"
            />
          </div>
          <div
            class="xe-profile-text-container"
          >
            <div
              class="xe-profile-text-hello"
            >
              Hello
            </div>
            <div
              class="xe-profile-text-name"
            >
              John
            </div>
            <div
              class="xe-profile-text-name"
            >
              Doe
            </div>
            <div
              class="xe-profile-text-email"
            >
              <EMAIL>
            </div>
          </div>
        </div>
        <div
          class="xe-profile-links"
        >
          <div
            class="xe-profile-menu-privacy-preferences-link"
          >
            <span
              class="sc-kpDqfm htmBFh"
              data-component="link"
            >
              <a
                data-role="link-anchor"
                href="http://one.trust.magic.link"
              >
                <span
                  class="sc-dAlyuH"
                >
                  Privacy Preferences
                </span>
              </a>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`ProfileSettingsPanel should render the component in German (de-DE) 1`] = `
<div>
  <div
    class="xe-profile-container"
  >
    <div
      class="xe-profile-header"
    >
      <span>
        Persönliche Einstellungen
      </span>
    </div>
    <div
      class="xe-profile-body"
    >
      <div
        class="xe-profile-menu"
      >
        <div
          class="xe-profile-menu-items-container"
        >
          <button
            class="xe-profile-menu-item xe-profile-menu-item-selected"
            type="button"
          >
            <span>
              Mein Profil
            </span>
          </button>
          <button
            class="xe-profile-menu-item"
            type="button"
          >
            <span>
              Sprache
            </span>
          </button>
        </div>
        <div
          class="xe-profile-menu-logout"
        >
          <span
            class="sc-iGgWBj iWpVbl"
            color="#FFFF"
            data-component="icon"
            data-element="logout"
            data-role="icon"
            font-size="small"
            type="logout"
          />
          <a
            class="xe-profile-menu-logout-text"
            data-component="link"
            href="http://login.service.com/logout?fromUrl=http://localhost/"
          >
            Abmelden
          </a>
        </div>
      </div>
      <div
        class="xe-profile-content"
      >
        <div
          class="xe-profile-my-profile-content"
        >
          <div
            class="xe-profile-photo-container"
          >
            <div
              class="xe-profile-picture-large xe-profile-picture-empty"
            />
          </div>
          <div
            class="xe-profile-text-container"
          >
            <div
              class="xe-profile-text-hello"
            >
              Hallo
            </div>
            <div
              class="xe-profile-text-name"
            >
              John
            </div>
            <div
              class="xe-profile-text-name"
            >
              Doe
            </div>
            <div
              class="xe-profile-text-email"
            >
              <EMAIL>
            </div>
          </div>
        </div>
        <div
          class="xe-profile-links"
        >
          <div
            class="xe-profile-menu-privacy-preferences-link"
          >
            <span
              class="sc-kpDqfm htmBFh"
              data-component="link"
            >
              <a
                data-role="link-anchor"
                href="http://one.trust.magic.link"
              >
                <span
                  class="sc-dAlyuH"
                >
                  Datenschutzeinstellungen
                </span>
              </a>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
