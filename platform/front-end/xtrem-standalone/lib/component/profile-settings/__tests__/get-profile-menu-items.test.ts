import { Dict } from '@sage/xtrem-shared';
import type { UserDetails } from '../../../service/auth-service';
import { getProfileMenuItems } from '../get-profile-menu-items';

const mockUser: UserDetails = {
    clientEncryptionKey: '',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    id: '1',
    lastName: 'Doe',
    locale: 'en-US',
    photo: null,
    pref: 0,
    tenantId: '1',
    uniqueTenantId: '1',
    uniqueUserId: '1',
    userCode: 'JD-001',
    uuid: '50b5a250-2b1b-435b-be9d-c99d22151f66',
    isOperator: false,
};

const translations: Dict<Dict<string>> = {
    'en-US': {
        '@sage/xtrem-standalone/my-profile': 'My profile',
        '@sage/xtrem-standalone/language': 'Language',
    },
    'de-DE': {
        '@sage/xtrem-standalone/my-profile': 'Mein Profil',
        '@sage/xtrem-standalone/language': 'Sprache',
    },
} as const;

describe('getProfileMenuItems', () => {
    it('should return expected profile menu items in English (en-US)', () => {
        const user: UserDetails | null = { ...mockUser, locale: 'en-US' };
        expect(getProfileMenuItems(user, translations[user.locale as 'en-US'])).toEqual([
            { key: 'MyProfile', text: 'My profile' },
            { key: 'Language', text: 'Language' },
        ]);
    });

    it('should return expected profile menu items in German (de-DE)', () => {
        const user: UserDetails | null = { ...mockUser, locale: 'de-DE' };
        expect(getProfileMenuItems(user, translations[user.locale as 'de-DE'])).toEqual([
            { key: 'MyProfile', text: 'Mein Profil' },
            { key: 'Language', text: 'Sprache' },
        ]);
    });

    it('should return expected profile menu items in English (fallback) given user with null as locale', () => {
        const user: UserDetails | null = { ...mockUser, locale: null };
        expect(getProfileMenuItems(user, {})).toEqual([
            { key: 'MyProfile', text: 'My profile' },
            { key: 'Language', text: 'Language' },
        ]);
    });

    it('should return expected profile menu items in English (fallback) given null as user', () => {
        expect(getProfileMenuItems(null, {})).toEqual([
            { key: 'MyProfile', text: 'My profile' },
            { key: 'Language', text: 'Language' },
        ]);
    });
});
