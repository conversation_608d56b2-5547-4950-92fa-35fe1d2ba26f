jest.mock('@sage/xtrem-ui', () => ({
    integration: {
        breakpoints: {},
        DashboardRootComponent: () => <div data-testid="bms-dashboard">BMS Dashboard Placeholder</div>,
        XtremUiIndex: () => <div data-testid="xtrem-ui">Xtrem-UI Placeholder</div>,
    },
}));

import { render, screen, within } from '@testing-library/react';
import React from 'react';
import { NotificationPreviewCard } from '../notification-preview-card';
import type { NotificationPreview } from '../notification-types';
import '@testing-library/jest-dom';
import { XtremStandaloneState } from '../../../standalone-redux/state';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { Provider } from 'react-redux';

const previews: Array<NotificationPreview> = [
    {
        _createStamp: new Date('2023-01-01T00:00:00.000Z'),
        _id: '1',
        actions: [],
        description: 'This is an regular notification.',
        icon: 'info',
        isRead: false,
        isStale: false,
        level: 'info',
        shouldDisplayToast: false,
        title: 'Notification #1',
    },
    {
        _createStamp: new Date('2023-01-01T01:00:00.000Z'),
        _id: '2',
        actions: [],
        description: 'This is an urgent notification that has already been previewed.',
        icon: 'info',
        isRead: false,
        isStale: true,
        level: 'info',
        shouldDisplayToast: true,
        title: 'Notification #2',
    },
    {
        _createStamp: new Date('2023-01-01T02:00:00.000Z'),
        _id: '3',
        actions: [],
        description: 'This is an urgent notification that has yet to be previewed.',
        icon: 'info',
        isRead: false,
        isStale: false,
        level: 'info',
        shouldDisplayToast: true,
        title: 'Notification #3',
    },
    {
        _createStamp: new Date('2023-01-01T03:00:00.000Z'),
        _id: '4',
        actions: [
            {
                _id: '1',
                link: '@sage/xtrem-test/TestPage',
                style: 'tertiary',
                title: 'Internal',
            },
        ],
        description: 'This is an urgent notification that has yet to be previewed.',
        icon: 'info',
        isRead: false,
        isStale: false,
        level: 'info',
        shouldDisplayToast: true,
        title: 'Notification #4',
    },
    {
        _createStamp: new Date('2023-01-01T04:00:00.000Z'),
        _id: '5',
        actions: [
            {
                _id: '1',
                link: '@sage/xtrem-test/TestPage',
                style: 'tertiary',
                title: 'Internal',
            },
            {
                _id: '2',
                link: 'https://www.google.com/',
                style: 'tertiary',
                title: 'External',
            },
        ],
        description: 'This is an urgent notification that has yet to be previewed.',
        icon: 'info',
        isRead: false,
        isStale: false,
        level: 'info',
        shouldDisplayToast: true,
        title: 'Notification #5',
    },
];

describe('NotificationPreviewCard', () => {
    let mockState: XtremStandaloneState;

    const getMockStore = () => {
        const mockStoreCreator = configureMockStore<XtremStandaloneState>([thunk]);
        return mockStoreCreator(() => mockState);
    };

    const markNotificationAsReadMock = jest.fn();
    const markNotificationAsUnreadMock = jest.fn();
    const onCloseMock = jest.fn();
    const onInternalNavigationChange = jest.fn();

    beforeEach(() => {
        markNotificationAsReadMock.mockReset();
        markNotificationAsUnreadMock.mockReset();
        onCloseMock.mockReset();
        onInternalNavigationChange.mockReset();

        mockState = {
            config: {
                agGridLicenceKey: 'licenceKey',
                chatbotBackendUrl: 'http://chatbot.end.point',
                productName: 'Sage Distribution and Manufacturing Operations',
            },
            chatbotConfig: {
                aiToken: 'someToken',
                accessCode:
                    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30',
                accessCodeExpiration: 1516239022,
            },
            pageContext: null,
            isApplicationDirty: false,
            menuItems: [],
            sitemap: [],
            isChatbotOpen: true,
            isNavigationOpen: false,
            isNotificationCenterOpen: false,
            notifications: previews,
            pageTitle: 'title',
            preNavigationConfirmation: jest.fn(),
            tenantsList: {} as any,
            browser: {} as any,
            loginService: 'http://login.service.com',
            path: '',
            user: {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                id: '<EMAIL>',
                userCode: 'john.doe',
                locale: 'en-US',
                tenantId: 'tenant1',
                pref: 5,
                uniqueTenantId: 'teant1id',
                uniqueUserId: 'johnDoeId',
                clientEncryptionKey: 'test',
                uuid: '**********',
                photo: null,
                isOperator: false,
            },
            translations: {},
        };
    });

    it('should render notification preview with correct title', () => {
        const preview = previews.find((preview: NotificationPreview) => {
            return preview._id === '3';
        }) as NotificationPreview;
        render(
            <Provider store={getMockStore()}>
                <NotificationPreviewCard onClose={onCloseMock} preview={preview} />
            </Provider>,
        );

        const element = screen.getByTestId('xe-notification-title');
        expect(element).toHaveTextContent(preview.title);
    });

    it('should render notification preview with correct description', () => {
        const preview = previews.find((preview: NotificationPreview) => {
            return preview._id === '3';
        }) as NotificationPreview;
        render(
            <Provider store={getMockStore()}>
                <NotificationPreviewCard onClose={onCloseMock} preview={preview} />
            </Provider>,
        );

        const element = screen.getByTestId('xe-notification-description');
        expect(element).toHaveTextContent(preview.description);

        const iconElement = screen.getByRole('img');
        expect(iconElement).toHaveBeenCalled;
    });

    it('should render notification preview with correct title', () => {
        const preview = previews.find((preview: NotificationPreview) => {
            return preview._id === '3';
        }) as NotificationPreview;
        render(
            <Provider store={getMockStore()}>
                <NotificationPreviewCard onClose={onCloseMock} preview={preview} />
            </Provider>,
        );

        const element = screen.getByRole('img');
        expect(element).toHaveAttribute('data-element', 'info');
    });

    it('should render notification preview with close icon button', () => {
        const preview = previews.find((preview: NotificationPreview) => {
            return preview._id === '3';
        }) as NotificationPreview;
        render(
            <Provider store={getMockStore()}>
                <NotificationPreviewCard onClose={onCloseMock} preview={preview} />
            </Provider>,
        );

        const header = screen.getByTestId('xe-notification-body-header');
        expect(header).toBeInTheDocument();

        const action = within(header).getByTestId('xe-notification-action-close');
        expect(action).toBeInTheDocument();
        expect(action).toHaveAttribute('aria-label', 'Close');
    });

    it('should return null for non-urgent notifications', () => {
        const preview = previews.find((preview: NotificationPreview) => {
            return preview._id === '1';
        }) as NotificationPreview;
        render(
            <Provider store={getMockStore()}>
                <NotificationPreviewCard onClose={onCloseMock} preview={preview} />
            </Provider>,
        );

        const element = screen.queryByTestId('xe-notification-card-preview');
        expect(element).not.toBeInTheDocument();
    });

    it('should return null for already toasted notifications', () => {
        const preview = previews.find((preview: NotificationPreview) => {
            return preview._id === '2';
        }) as NotificationPreview;
        render(
            <Provider store={getMockStore()}>
                <NotificationPreviewCard onClose={onCloseMock} preview={preview} />
            </Provider>,
        );

        const element = screen.queryByTestId('xe-notification-card-preview');
        expect(element).not.toBeInTheDocument();
    });

    describe('given notification preview with no actions', () => {
        it('should render "Mark as read" button action', () => {
            const preview = previews.find((preview: NotificationPreview) => {
                return preview._id === '3';
            }) as NotificationPreview;
            render(
                <Provider store={getMockStore()}>
                    <NotificationPreviewCard onClose={onCloseMock} preview={preview} />
                </Provider>,
            );

            const actions = screen.getByTestId('xe-notification-actions');
            expect(actions).toBeInTheDocument();
            expect(actions.childElementCount).toEqual(1);
            expect(actions.children[0]).toHaveTextContent('Mark as read');
        });
    });

    describe('given notification preview with one action', () => {
        it('should render defined action as button action', () => {
            const preview = previews.find((preview: NotificationPreview) => {
                return preview._id === '4';
            }) as NotificationPreview;
            render(
                <Provider store={getMockStore()}>
                    <NotificationPreviewCard onClose={onCloseMock} preview={preview} />
                </Provider>,
            );

            const actions = screen.getByTestId('xe-notification-actions');
            expect(actions).toBeInTheDocument();
            expect(actions.childElementCount).toEqual(2);
            expect(actions.children[0]).toHaveTextContent(preview.actions[0].title);
            expect(actions.children[1]).toHaveTextContent('Mark as read');
        });
    });

    describe('given notification with two or more actions', () => {
        it('should render defined actions as button actions', () => {
            const preview = previews.find((preview: NotificationPreview) => {
                return preview._id === '5';
            }) as NotificationPreview;
            render(
                <Provider store={getMockStore()}>
                    <NotificationPreviewCard onClose={onCloseMock} preview={preview} />
                </Provider>,
            );

            const actions = screen.getByTestId('xe-notification-actions');
            expect(actions).toBeInTheDocument();
            expect(actions.childElementCount).toEqual(2);
            expect(actions.children[0]).toHaveTextContent(preview.actions[0].title);
            expect(actions.children[1]).toHaveTextContent(preview.actions[1].title);
        });
    });
});
