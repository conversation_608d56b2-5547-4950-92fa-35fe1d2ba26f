jest.mock('@sage/xtrem-ui', () => ({
    integration: {
        XtremUiIndex: () => <div data-testid="xtrem-ui">Xtrem-UI placeholder</div>,
        DashboardRootComponent: () => <div data-testid="bms-dashboard">BMS dashboard placeholder</div>,
        breakpoints: {},
    },
}));
jest.mock('../notification-center-item', () => ({
    ConnectedNotificationCenterItem: () => <div data-testid="xe-notification-card" />,
}));

import { Notification } from '@sage/xtrem-shared';
import React from 'react';
import { NotificationCenter } from '../notification-center';

const { render, screen } = require('@testing-library/react');

const mockTranslations = { '@sage/xtrem-standalone/notification-center': 'Notification Center' };

const mockNotifications: Array<Notification> = [
    {
        _createStamp: new Date('2022-01-01T00:00:00.000Z'),
        icon: 'check_all',
        level: 'success',
        title: 'Notification 1',
        shouldDisplayToast: false,
        description: 'some desc',
        _id: '1',
        isRead: false,
        actions: [],
    },
    {
        _createStamp: new Date('2022-01-02T00:00:00.000Z'),
        icon: 'warning',
        level: 'warning',
        title: 'Notification 2',
        shouldDisplayToast: true,
        description: 'some desc',
        _id: '2',
        isRead: true,
        actions: [],
    },
];

describe('NotificationCenter', () => {
    it('renders the header with the correct title', () => {
        const locale = 'en-US';
        render(
            <NotificationCenter
                translations={mockTranslations}
                locale={locale}
                markAllNotificationsAsRead={jest.fn()}
                notifications={[]}
                open={true}
                setNotificationCenterOpen={jest.fn()}
            />,
        );
        const headerTitle = screen.getByText('Notification Center');
        expect(headerTitle).toBeInTheDocument();
    });

    it('calls the setNotificationCenterOpen function when the sidebar is closed', () => {
        const locale = 'en-US';
        const setNotificationCenterOpen = jest.fn();
        render(
            <NotificationCenter
                translations={mockTranslations}
                locale={locale}
                markAllNotificationsAsRead={jest.fn()}
                notifications={[]}
                open={true}
                setNotificationCenterOpen={setNotificationCenterOpen}
            />,
        );
        const closeButton = screen.getByRole('button', { name: 'Close' });
        closeButton.click();
        expect(setNotificationCenterOpen).toHaveBeenCalled();
    });

    it('should not render the component content because open is false', () => {
        const translations = { 'Notification center': 'Notification Center' };
        const locale = 'en-US';
        render(
            <NotificationCenter
                translations={translations}
                locale={locale}
                markAllNotificationsAsRead={jest.fn()}
                notifications={[]}
                open={false}
                setNotificationCenterOpen={jest.fn()}
            />,
        );
        expect(screen.queryByTestId('xe-notification-content')).not.toBeInTheDocument();
    });

    it('should render the component because open is true', () => {
        const translations = { 'Notification center': 'Notification Center' };
        const locale = 'en-US';
        render(
            <NotificationCenter
                translations={translations}
                locale={locale}
                markAllNotificationsAsRead={jest.fn()}
                notifications={[]}
                open={true}
                setNotificationCenterOpen={jest.fn()}
            />,
        );
        const notificationCenter = screen.queryByTestId('xe-notification-content');
        expect(notificationCenter).toBeInTheDocument();
    });

    it('should render the component with 2 notifications', () => {
        const translations = { 'Notification center': 'Notification Center' };
        const locale = 'en-US';
        render(
            <NotificationCenter
                translations={translations}
                locale={locale}
                markAllNotificationsAsRead={jest.fn()}
                notifications={mockNotifications}
                open={true}
                setNotificationCenterOpen={jest.fn()}
            />,
        );
        const notificationCenter = screen.queryByTestId('xe-notification-content');
        expect(notificationCenter).toBeInTheDocument();
        expect(screen.queryAllByTestId('xe-notification-card').length).toBe(2);
    });

    it('should render the component in blank because no notifications', () => {
        const translations = { 'Notification center': 'Notification Center' };
        const locale = 'en-US';
        render(
            <NotificationCenter
                translations={translations}
                locale={locale}
                markAllNotificationsAsRead={jest.fn()}
                notifications={[]}
                open={true}
                setNotificationCenterOpen={jest.fn()}
            />,
        );
        const notificationCenter = screen.queryByTestId('xe-notification-content');
        expect(notificationCenter).toBeInTheDocument();
        expect(screen.queryAllByTestId('xe-notification-card').length).toBe(0);
    });
});
