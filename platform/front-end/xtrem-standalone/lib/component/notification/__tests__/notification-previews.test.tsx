jest.mock('@sage/xtrem-ui', () => ({
    integration: {
        breakpoints: {},
        DashboardRootComponent: () => <div data-testid="bms-dashboard">BMS Dashboard Placeholder</div>,
        XtremUiIndex: () => <div data-testid="xtrem-ui">Xtrem-UI Placeholder</div>,
    },
}));

import type { Notification } from '@sage/xtrem-shared';
import '@testing-library/jest-dom';
import { render, waitFor } from '@testing-library/react';
import React from 'react';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { XtremStandaloneState } from '../../../standalone-redux/state';
import { NotificationPreviews } from '../notification-previews';

const notifications: Array<Notification> = [
    {
        _createStamp: new Date('2023-01-01T00:00:00.000Z'),
        _id: '1',
        actions: [],
        description: 'This is a regular notification.',
        icon: 'info',
        isRead: false,
        level: 'info',
        shouldDisplayToast: false,
        title: 'Notification #1',
    },
    {
        _createStamp: new Date('2023-01-02T00:00:00.000Z'),
        _id: '2',
        actions: [],
        description: 'This is an urgent notification.',
        icon: 'info',
        isRead: false,
        level: 'info',
        shouldDisplayToast: true,
        title: 'Notification #2',
    },
    {
        _createStamp: new Date('2023-01-03T00:00:00.000Z'),
        _id: '3',
        actions: [],
        description: 'This is an urgent notification.',
        icon: 'info',
        isRead: false,
        level: 'info',
        shouldDisplayToast: true,
        title: 'Notification #3',
    },
];

describe('notification previews', () => {
    let mockState: XtremStandaloneState;

    const getMockStore = () => {
        const mockStoreCreator = configureMockStore<XtremStandaloneState>([thunk]);
        return mockStoreCreator(() => mockState);
    };

    beforeEach(() => {
        mockState = {
            pageContext: null,
            config: {
                agGridLicenceKey: 'licenceKey',
                chatbotBackendUrl: 'http://chatbot.end.point',
                productName: 'Sage Distribution and Manufacturing Operations',
            },
            chatbotConfig: {
                aiToken: 'someToken',
                accessCode:
                    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30',
                accessCodeExpiration: 1516239022,
            },
            isApplicationDirty: false,
            menuItems: [],
            sitemap: [],
            isChatbotOpen: true,
            isNavigationOpen: false,
            isNotificationCenterOpen: false,
            notifications,
            pageTitle: 'title',
            preNavigationConfirmation: jest.fn(),
            tenantsList: {} as any,
            browser: {} as any,
            loginService: 'http://login.service.com',
            path: '',
            user: {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                id: '<EMAIL>',
                userCode: 'john.doe',
                locale: 'en-US',
                tenantId: 'tenant1',
                pref: 5,
                uniqueTenantId: 'teant1id',
                uniqueUserId: 'johnDoeId',
                clientEncryptionKey: 'test',
                uuid: '**********',
                photo: null,
                isOperator: false,
            },
            translations: {},
        };
    });

    it('should render notification previews for urgent notifications', async () => {
        const { queryAllByTestId } = render(
            <Provider store={getMockStore()}>
                <NotificationPreviews notifications={notifications} />
            </Provider>,
        );

        await waitFor(() => {
            const cards = queryAllByTestId('xe-notification-card-preview');
            expect(cards.length).toEqual(2);
        });
    });

    it('should not render notification previews for non-urgent notifications', async () => {
        const { queryAllByTestId } = render(
            <Provider store={getMockStore()}>
                <NotificationPreviews notifications={[notifications[0]]} />
            </Provider>,
        );

        await waitFor(() => {
            const cards = queryAllByTestId('xe-notification-card-preview');
            expect(cards.length).toEqual(0);
        });
    });
});
