import type { Dict, NotificationAction } from '@sage/xtrem-shared';
import { getNotificationActions } from '../get-notification-actions';

const TEST_ACTIONS: Dict<NotificationAction> = {
    ANNOUNCE: {
        _id: '1',
        link: '@sage/xtrem-test/RocketLaunch/12345/announce',
        style: 'tertiary',
        title: 'Announce',
    },
    ABORT: {
        _id: '2',
        link: '@sage/xtrem-test/RocketLaunch/12345/abort',
        style: 'secondary',
        title: 'Abort',
    },
    START: {
        _id: '3',
        link: '@sage/xtrem-test/RocketLaunch/12345/start',
        style: 'primary',
        title: 'Start',
    },
};

describe('getNotificationActions(isNotificationRead: boolean, actions?: NotificationAction[])', () => {
    let isNotificationRead = false;
    let actions: NotificationAction[] = [];
    const markReadTitle = 'Mark as read';
    const markUnreadTitle = 'Mark as unread';

    describe('given unread notification', () => {
        describe('given notification with no actions', () => {
            it('should return "mark as read" button action', () => {
                const result = getNotificationActions(isNotificationRead, actions, markReadTitle, markUnreadTitle);
                expect(result.buttons[0]).toEqual(expect.objectContaining({ title: 'Mark as read' }));
            });

            it('should have no other button actions', () => {
                const result = getNotificationActions(isNotificationRead, actions, markReadTitle, markUnreadTitle);
                expect(result.buttons.length).toEqual(1);
            });

            it('should have no other menu actions', () => {
                const result = getNotificationActions(isNotificationRead, actions, markReadTitle, markUnreadTitle);
                expect(result.menu.length).toEqual(0);
            });
        });

        describe('given notification with one action', () => {
            beforeEach(() => {
                actions = [TEST_ACTIONS.ANNOUNCE];
            });

            it('should return provided actions as first button action', () => {
                const result = getNotificationActions(isNotificationRead, actions, markReadTitle, markUnreadTitle);
                expect(result.buttons[0]).toEqual(expect.objectContaining({ title: 'Announce' }));
            });

            it('should return "mark as read" button action', () => {
                const result = getNotificationActions(isNotificationRead, actions, markReadTitle, markUnreadTitle);
                expect(result.buttons[1]).toEqual(expect.objectContaining({ title: 'Mark as read' }));
            });

            it('should have no other menu actions', () => {
                const result = getNotificationActions(isNotificationRead, actions, markReadTitle, markUnreadTitle);
                expect(result.menu.length).toEqual(0);
            });
        });

        describe('given notification with two actions', () => {
            beforeEach(() => {
                actions = [TEST_ACTIONS.ANNOUNCE, TEST_ACTIONS.ABORT];
            });

            it('should return provided actions as button actions', () => {
                const result = getNotificationActions(isNotificationRead, actions, markReadTitle, markUnreadTitle);
                expect(result.buttons[0]).toEqual(expect.objectContaining({ title: 'Announce' }));
                expect(result.buttons[1]).toEqual(expect.objectContaining({ title: 'Abort' }));
            });

            it('should return "mark as read" menu action', () => {
                const result = getNotificationActions(isNotificationRead, actions, markReadTitle, markUnreadTitle);
                expect(result.menu[0]).toEqual(expect.objectContaining({ title: 'Mark as read' }));
            });
        });

        describe('given notification with more than two actions', () => {
            beforeEach(() => {
                actions = [TEST_ACTIONS.ANNOUNCE, TEST_ACTIONS.ABORT, TEST_ACTIONS.START];
            });

            it('should return the first two provided actions as button actions', () => {
                const result = getNotificationActions(isNotificationRead, actions, markReadTitle, markUnreadTitle);
                expect(result.buttons[0]).toEqual(expect.objectContaining({ title: 'Announce' }));
                expect(result.buttons[1]).toEqual(expect.objectContaining({ title: 'Abort' }));
            });

            it('should return the remaining provided actions as menu actions', () => {
                const result = getNotificationActions(isNotificationRead, actions, markReadTitle, markUnreadTitle);
                expect(result.menu[0]).toEqual(expect.objectContaining({ title: 'Start' }));
            });

            it('should return "mark as read" menu action at the end', () => {
                const result = getNotificationActions(isNotificationRead, actions, markReadTitle, markUnreadTitle);
                expect(result.menu[1]).toEqual(expect.objectContaining({ title: 'Mark as read' }));
            });
        });
    });
});
