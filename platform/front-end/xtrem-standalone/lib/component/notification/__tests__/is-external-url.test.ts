import { isExternalUrl } from '../is-external-url';

let mockedUrl = 'https://www.sage.com';
describe('isExternalUrl(url: string): boolean', () => {
    beforeEach(() => {
        mockedUrl = 'https://www.sage.com';
        Object.defineProperty(window, 'location', {
            value: {
                get origin() {
                    return mockedUrl;
                },
            },
        });
    });

    it('should return true given absolute url with different origin', () => {
        expect(isExternalUrl('https://www.google.com')).toEqual(true);
        expect(isExternalUrl('https://www.google.co.uk')).toEqual(true);
        expect(isExternalUrl('https://www.google.com')).toEqual(true);
        expect(isExternalUrl('https://www.google.com/foo/bar')).toEqual(true);
        expect(isExternalUrl('https://www.google.com/foo/bar?q=baz')).toEqual(true);
        expect(isExternalUrl('https://www.google.com/foo/bar?q=baz#buzz')).toEqual(true);
    });

    it('should return true given alternative protocol', () => {
        expect(isExternalUrl('http://www.sage.com')).toEqual(true);
    });

    it('should return true given alternative subdomain', () => {
        expect(isExternalUrl('https://xtrem.sage.com')).toEqual(true);
    });

    it('should return false given absolute url with same origin', () => {
        expect(isExternalUrl('https://www.sage.com')).toEqual(false);
        expect(isExternalUrl('https://www.sage.com/foo/bar')).toEqual(false);
        expect(isExternalUrl('https://www.sage.com/foo/bar?q=baz')).toEqual(false);
        expect(isExternalUrl('https://www.sage.com/foo/bar?q=baz#buzz')).toEqual(false);
    });

    it('should return false given relative url', () => {
        expect(isExternalUrl('/foo/bar')).toEqual(false);
        expect(isExternalUrl('@sage/FooBar/12345')).toEqual(false);
    });

    it('should return false given invalid url', () => {
        expect(isExternalUrl(undefined as any)).toEqual(false);
        expect(isExternalUrl('')).toEqual(false);
        expect(isExternalUrl('Hello World!')).toEqual(false);
    });

    describe('with a port number', () => {
        beforeEach(() => {
            mockedUrl = 'http://sdmo-xtrem.localhost.dev-sagextrem.com:8240';
        });

        it('should return false given invalid url', () => {
            expect(
                isExternalUrl(
                    'http://sdmo-xtrem.localhost.dev-sagextrem.com:8240/download?t=yHeyoR84YRmuzF8Qp8Ykag%3D%3D.8kPCPRtIpcyKhYNOCWqc3o2EALd%2Bh7aDeULLpupoSmn2MIo%3D.q3m26fzZWHZj6gHOmPqXYw%3D%3D',
                ),
            ).toEqual(false);
        });
    });
});
