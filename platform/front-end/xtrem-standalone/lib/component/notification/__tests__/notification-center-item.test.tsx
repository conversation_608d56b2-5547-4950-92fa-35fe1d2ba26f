jest.mock('@sage/xtrem-ui', () => ({
    integration: {
        XtremUiIndex: () => <div data-testid="xtrem-ui">Xtrem-UI placeholder</div>,
        DashboardRootComponent: () => <div data-testid="bms-dashboard">BMS dashboard placeholder</div>,
        breakpoints: {},
    },
}));

import { render, screen, waitFor, within } from '@testing-library/react';
import React from 'react';
import type { Notification } from '@sage/xtrem-shared';
import { NotificationCenterItem } from '../notification-center-item';

const locale = 'en-US';

const translations = {
    '@sage/xtrem-standalone/notification-action-mark-read': 'Mark as read',
    '@sage/xtrem-standalone/notification-action-mark-unread': 'Mark as unread',
    '@sage/xtrem-standalone/notification-action-delete': 'Delete',
};

const notifications: Array<Notification> = [
    {
        _createStamp: new Date('2023-05-03T00:00:00.000Z'),
        _id: '5',
        actions: [
            {
                _id: '3',
                style: 'tertiary',
                link: '@sage/xtrem-test/Widget/12345?opertation=retry',
                title: 'Retry',
            },
        ],
        description: 'This is an error notification.',
        icon: 'error',
        isRead: false,
        level: 'error',
        shouldDisplayToast: false,
        title: 'Error: Lorem Ipsum',
    },
    {
        _createStamp: new Date('2023-04-01T00:00:00.000Z'),
        _id: '4',
        actions: [],
        description: 'This is a warning notification.',
        icon: 'warning',
        isRead: false,
        level: 'warning',
        shouldDisplayToast: false,
        title: 'Warning: Lorem Ipsum',
    },
    {
        _createStamp: new Date('2023-03-01T00:00:00.000Z'),
        _id: '3',
        actions: [],
        description: 'This is a success notification.',
        icon: 'check_all',
        isRead: false,
        level: 'success',
        shouldDisplayToast: false,
        title: 'Success: Lorem Ipsum',
    },
    {
        _createStamp: new Date('2023-02-01T00:00:00.000Z'),
        _id: '2',
        actions: [],
        description: 'This is an info notification.',
        icon: 'info',
        isRead: false,
        level: 'info',
        shouldDisplayToast: false,
        title: 'Info: Lorem Ipsum',
    },
    {
        _createStamp: new Date('2023-01-01T00:00:00.000Z'),
        _id: '1',
        actions: [
            {
                _id: '1',
                icon: 'tick',
                link: '@sage/xtrem-test/Widget/12345?operation=approve',
                style: 'secondary',
                title: 'Approve',
            },
            {
                _id: '2',
                icon: 'tick',
                link: '@sage/xtrem-test/Widget/12345?operation=reject',
                style: 'secondary',
                title: 'Reject',
            },
        ],
        description: 'This is an approval notification',
        icon: 'question',
        isRead: false,
        level: 'approval',
        shouldDisplayToast: false,
        title: 'Approval: Lorem Ipsum',
    },
];

describe('NotificationCenterItem', () => {
    const mockDeleteNotification = jest.fn();
    const mockMarkNotificationAsRead = jest.fn();
    const mockMarkNotificationAsUnread = jest.fn();
    const mockOnInternalNavigationChange = jest.fn();
    const mockSetNotificationCenterOpen = jest.fn();

    beforeEach(() => {
        mockDeleteNotification.mockReset();
        mockMarkNotificationAsRead.mockReset();
        mockMarkNotificationAsUnread.mockReset();
        mockOnInternalNavigationChange.mockReset();
        mockSetNotificationCenterOpen.mockReset();
    });

    it('should render notification with correct title', () => {
        const notification = notifications.find(notification => notification._id === '2') as Notification;
        render(
            <NotificationCenterItem
                id={notification._id}
                deleteNotification={mockDeleteNotification}
                locale={locale}
                markNotificationAsRead={mockMarkNotificationAsRead}
                markNotificationAsUnread={mockMarkNotificationAsUnread}
                notification={notification}
                onInternalNavigationChange={mockOnInternalNavigationChange}
                setNotificationCenterOpen={mockSetNotificationCenterOpen}
                translations={translations}
            />,
        );

        const element = screen.getByTestId('xe-notification-title');
        expect(element).toBeInTheDocument();
        expect(element).toHaveTextContent(notification.title);
    });

    it('should render notification with correct description', () => {
        const notification = notifications.find(notification => notification._id === '2') as Notification;
        render(
            <NotificationCenterItem
                id={notification._id}
                deleteNotification={mockDeleteNotification}
                locale={locale}
                markNotificationAsRead={mockMarkNotificationAsRead}
                markNotificationAsUnread={mockMarkNotificationAsUnread}
                notification={notification}
                onInternalNavigationChange={mockOnInternalNavigationChange}
                setNotificationCenterOpen={mockSetNotificationCenterOpen}
                translations={translations}
            />,
        );

        const element = screen.getByTestId('xe-notification-description');
        expect(element).toHaveTextContent(notification.description);
    });

    it('should render notification with correct icon', () => {
        const notification = notifications.find(notification => notification._id === '2') as Notification;
        render(
            <NotificationCenterItem
                id={notification._id}
                deleteNotification={mockDeleteNotification}
                locale={locale}
                markNotificationAsRead={mockMarkNotificationAsRead}
                markNotificationAsUnread={mockMarkNotificationAsUnread}
                notification={notification}
                onInternalNavigationChange={mockOnInternalNavigationChange}
                setNotificationCenterOpen={mockSetNotificationCenterOpen}
                translations={translations}
            />,
        );

        const element = screen.getByRole('img');
        expect(element).toHaveAttribute('data-element', 'info');
    });

    describe('given notification with no actions', () => {
        it('should render "Mark as read" button action if notification is unread', () => {
            const notification = notifications.find(notification => notification._id === '2') as Notification;
            render(
                <NotificationCenterItem
                    id={notification._id}
                    deleteNotification={mockDeleteNotification}
                    locale={locale}
                    markNotificationAsRead={mockMarkNotificationAsRead}
                    markNotificationAsUnread={mockMarkNotificationAsUnread}
                    notification={notification}
                    onInternalNavigationChange={mockOnInternalNavigationChange}
                    setNotificationCenterOpen={mockSetNotificationCenterOpen}
                    translations={translations}
                />,
            );

            const actions = screen.getByTestId('xe-notification-actions');
            expect(actions).toBeInTheDocument();
            expect(actions.childElementCount).toEqual(1);
            expect(actions.children[0]).toHaveTextContent('Mark as read');
        });

        it('should render "Mark as unread" button action if notification is read', () => {
            const notification: Notification = {
                ...(notifications.find(notification => notification._id === '2') as Notification),
                isRead: true,
            };
            render(
                <NotificationCenterItem
                    id={notification._id}
                    deleteNotification={mockDeleteNotification}
                    locale={locale}
                    markNotificationAsRead={mockMarkNotificationAsRead}
                    markNotificationAsUnread={mockMarkNotificationAsUnread}
                    notification={notification}
                    onInternalNavigationChange={mockOnInternalNavigationChange}
                    setNotificationCenterOpen={mockSetNotificationCenterOpen}
                    translations={translations}
                />,
            );

            const actions = screen.getByTestId('xe-notification-actions');
            expect(actions).toBeInTheDocument();
            expect(actions.childElementCount).toEqual(1);
            expect(actions.children[0]).toHaveTextContent('Mark as unread');
        });

        it('should render "Delete" menu action as icon button', () => {
            const notification = notifications.find(notification => notification._id === '2') as Notification;
            render(
                <NotificationCenterItem
                    id={notification._id}
                    deleteNotification={mockDeleteNotification}
                    locale={locale}
                    markNotificationAsRead={mockMarkNotificationAsRead}
                    markNotificationAsUnread={mockMarkNotificationAsUnread}
                    notification={notification}
                    onInternalNavigationChange={mockOnInternalNavigationChange}
                    setNotificationCenterOpen={mockSetNotificationCenterOpen}
                    translations={translations}
                />,
            );

            const header = screen.getByTestId('xe-notification-body-header');
            expect(header).toBeInTheDocument();

            const action = within(header).getByTestId('xe-notification-action-delete');
            expect(action).toBeInTheDocument();
            expect(action).toHaveAttribute('aria-label', 'Delete');
        });
    });

    describe('given notification with one action', () => {
        it('should render defined action and "mark as read" action as button actions', () => {
            const notification = notifications.find(notification => notification._id === '5') as Notification;
            render(
                <NotificationCenterItem
                    id={notification._id}
                    deleteNotification={mockDeleteNotification}
                    locale={locale}
                    markNotificationAsRead={mockMarkNotificationAsRead}
                    markNotificationAsUnread={mockMarkNotificationAsUnread}
                    notification={notification}
                    onInternalNavigationChange={mockOnInternalNavigationChange}
                    setNotificationCenterOpen={mockSetNotificationCenterOpen}
                    translations={translations}
                />,
            );

            const actions = screen.getByTestId('xe-notification-actions');
            expect(actions).toBeInTheDocument();
            expect(actions.childElementCount).toEqual(2);
            expect(actions.children[0]).toHaveTextContent('Retry');
            expect(actions.children[1]).toHaveTextContent('Mark as read');
        });

        it('should render "Delete" action as icon button', () => {
            const notification = notifications.find(notification => notification._id === '2') as Notification;
            render(
                <NotificationCenterItem
                    id={notification._id}
                    deleteNotification={mockDeleteNotification}
                    locale={locale}
                    markNotificationAsRead={mockMarkNotificationAsRead}
                    markNotificationAsUnread={mockMarkNotificationAsUnread}
                    notification={notification}
                    onInternalNavigationChange={mockOnInternalNavigationChange}
                    setNotificationCenterOpen={mockSetNotificationCenterOpen}
                    translations={translations}
                />,
            );

            const header = screen.getByTestId('xe-notification-body-header');
            expect(header).toBeInTheDocument();

            const action = within(header).getByTestId('xe-notification-action-delete');
            expect(action).toBeInTheDocument();
            expect(action).toHaveAttribute('aria-label', 'Delete');
        });
    });

    describe('given notification with two actions', () => {
        it('should render defined actions as button actions', () => {
            const notification = notifications.find(notification => notification._id === '1') as Notification;
            render(
                <NotificationCenterItem
                    id={notification._id}
                    deleteNotification={mockDeleteNotification}
                    locale={locale}
                    markNotificationAsRead={mockMarkNotificationAsRead}
                    markNotificationAsUnread={mockMarkNotificationAsUnread}
                    notification={notification}
                    onInternalNavigationChange={mockOnInternalNavigationChange}
                    setNotificationCenterOpen={mockSetNotificationCenterOpen}
                    translations={translations}
                />,
            );

            const actions = screen.getByTestId('xe-notification-actions');
            expect(actions).toBeInTheDocument();
            expect(actions.childElementCount).toEqual(2);
            expect(actions.children[0]).toHaveTextContent('Approve');
            expect(actions.children[1]).toHaveTextContent('Reject');
        });

        it('should render "mark as read" and "delete" actions as menu action', async () => {
            window.ResizeObserver = class ResizeObserver {
                observe(): void {
                    jest.fn();
                }

                unobserve(): void {
                    jest.fn();
                }

                disconnect(): void {
                    jest.fn();
                }
            };
            const notification = notifications.find(notification => notification._id === '1') as Notification;
            render(
                <NotificationCenterItem
                    id={notification._id}
                    deleteNotification={mockDeleteNotification}
                    locale={locale}
                    markNotificationAsRead={mockMarkNotificationAsRead}
                    markNotificationAsUnread={mockMarkNotificationAsUnread}
                    notification={notification}
                    onInternalNavigationChange={mockOnInternalNavigationChange}
                    setNotificationCenterOpen={mockSetNotificationCenterOpen}
                    translations={translations}
                />,
            );

            const header = screen.getByTestId('xe-notification-body-header');
            expect(header).toBeInTheDocument();

            const menu = within(header).getByRole('button');
            expect(menu).toBeInTheDocument();
            menu.click();

            await waitFor(() => {
                const portal = document.querySelector('[data-component="action-popover"]');
                expect(portal).toBeInTheDocument();
            });

            const actions = within(
                document.querySelector('[data-component="action-popover"]') as HTMLDivElement,
            ).getAllByRole('presentation');
            expect(actions).toHaveLength(2);
            expect(actions[0]).toHaveTextContent('Mark as read');
            expect(actions[1]).toHaveTextContent('Delete');
        });
    });
});
