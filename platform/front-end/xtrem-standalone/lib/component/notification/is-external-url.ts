/**
 * @param {string} urlString - The URL to be evaluated
 * @returns {boolean} Returns true for absolute link that begins with a different origin from the
 * current window. Otherwise return false (even if the provided url is invalid).
 */
export const isExternalUrl = (urlString: string): boolean => {
    try {
        const url = new URL(urlString);
        return url.origin !== window.location.origin;
    } catch {
        return false;
    }
};

export const isDownloadUrl = (urlString: string): boolean => {
    return urlString.startsWith(`${window.location.origin}/download?`);
};
