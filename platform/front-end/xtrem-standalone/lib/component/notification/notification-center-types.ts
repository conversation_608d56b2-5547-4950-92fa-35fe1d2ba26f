import type { Dict, Notification } from '@sage/xtrem-shared';

export interface NotificationCenterHeaderProps {
    translations: NotificationCenterProps['translations'];
    locale: string;
}

export interface NotificationCenterExternalProps {
    open: boolean;
}

export interface NotificationCenterProps extends NotificationCenterExternalProps {
    locale: string;
    notifications: Notification[];
    markAllNotificationsAsRead: () => Promise<void>;
    setNotificationCenterOpen: (isOpen: boolean) => void;
    translations: Dict<string>;
}

export interface NotificationCenterItemExternalProps {
    id: string;
    onClose?: () => void;
}

export interface NotificationCenterItemProps extends NotificationCenterItemExternalProps {
    locale: string;
    notification: Notification;
    deleteNotification: (id: string) => Promise<void>;
    markNotificationAsRead: (id: string) => Promise<void>;
    markNotificationAsUnread: (id: string) => Promise<void>;
    onInternalNavigationChange: (path: string) => void;
    setNotificationCenterOpen: (isOpen: boolean) => void;
    translations: Dict<string>;
}
