import * as tokens from '@sage/design-tokens/js/base/common';
import type { Locale as XtremLocale, Notification, NotificationAction, NotificationLevel } from '@sage/xtrem-shared';
import { ActionPopover, ActionPopoverDivider, ActionPopoverItem } from 'carbon-react/esm/components/action-popover';
import ButtonMajor from 'carbon-react/esm/components/button';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import type { IconType } from 'carbon-react/esm/components/icon';
import Icon from 'carbon-react/esm/components/icon';
import IconButton from 'carbon-react/esm/components/icon-button';
import type { Locale as DateFnsLocale } from 'date-fns';
import { formatDistance } from 'date-fns/formatDistance';
import { arSA } from 'date-fns/locale/ar-SA';
import { de } from 'date-fns/locale/de';
import { enGB } from 'date-fns/locale/en-GB';
import { enUS } from 'date-fns/locale/en-US';
import { es } from 'date-fns/locale/es';
import { fr } from 'date-fns/locale/fr';
import { it } from 'date-fns/locale/it';
import { pl } from 'date-fns/locale/pl';
import { pt } from 'date-fns/locale/pt';
import { ptBR } from 'date-fns/locale/pt-BR';
import { zhCN } from 'date-fns/locale/zh-CN';
import { kebabCase, noop } from 'lodash';
import React from 'react';
import type { KeyboardEvent, MouseEvent } from 'react';
import { connect } from 'react-redux';
import { useExternalLinkConfirmation } from '../../hooks/use-external-link-confirmation';
import { isSkipExternalLinkConfirmation } from '../../hooks/use-local-storage';
import { localize } from '../../service/standalone-i18n-service';
import type * as actionTypes from '../../standalone-redux/action-types';
import * as actions from '../../standalone-redux/actions';
import type { XtremStandaloneState } from '../../standalone-redux/state';
import { getNotificationActions } from './get-notification-actions';
import { isDownloadUrl, isExternalUrl } from './is-external-url';
import type { NotificationCenterItemExternalProps, NotificationCenterItemProps } from './notification-center-types';
import './notification-center.scss';
import { ExternalLinkConfirmationDialog } from '../external-link-confirmation/exterrnal-link-confirmation-dialog';

const dateLocales: Record<XtremLocale, DateFnsLocale> = {
    'en-GB': enGB,
    'en-US': enUS,
    'es-ES': es,
    'pl-PL': pl,
    'zh-CN': zhCN,
    'fr-FR': fr,
    'pt-PT': pt,
    'pt-BR': ptBR,
    'ar-SA': arSA,
    'de-DE': de,
    'it-IT': it,
};

const levels: Record<NotificationLevel, string> = {
    success: tokens.colorsSemanticPositive500,
    warning: tokens.colorsSemanticCaution400,
    error: tokens.colorsSemanticNegative450,
    info: tokens.colorsSemanticInfo500,
    approval: tokens.colorsSemanticFocus500,
};

export function NotificationCenterItem({
    deleteNotification,
    locale,
    markNotificationAsRead,
    markNotificationAsUnread,
    notification,
    onClose,
    onInternalNavigationChange,
    setNotificationCenterOpen,
    translations,
}: NotificationCenterItemProps): React.ReactElement {
    const { getConfirmation, isOpen, onCancel, onConfirm } = useExternalLinkConfirmation({
        onCancel: noop,
        onConfirm: noop,
    });

    const startDate =
        typeof notification._createStamp === 'string'
            ? new Date(parseInt(notification._createStamp, 10))
            : notification._createStamp;

    const fnsLocale = locale as keyof typeof dateLocales;
    const distance = formatDistance(startDate, new Date(), {
        addSuffix: true,
        locale: dateLocales[fnsLocale] ? dateLocales[fnsLocale] : enUS,
    });

    const getNotificationActionTestId = (title: string): string => {
        return `xe-notification-action-${kebabCase(title)}`;
    };

    const notificationActions = getNotificationActions(
        notification.isRead,
        notification.actions,
        localize('@sage/xtrem-standalone/notification-action-mark-read', 'Mark as read', {}, translations, locale),
        localize('@sage/xtrem-standalone/notification-action-mark-unread', 'Mark as unread', {}, translations, locale),
    );

    const markUnreadNotificationAsRead = async () => {
        if (!notification.isRead) {
            await markNotificationAsRead(notification._id);
        }
    };

    const onDeleteNotification = async () => {
        await deleteNotification(notification._id);
    };

    const onHandleButtonAction = (action: NotificationAction) => async () => {
        if (action._id === 'notification-action-mark-read') {
            await markNotificationAsRead(notification._id);
            return;
        }

        if (action._id === 'notification-action-mark-unread') {
            await markNotificationAsUnread(notification._id);
        }
    };

    const renderTopRightButton = () => {
        if (onClose) {
            return (
                <IconButton
                    aria-label={localize(
                        '@sage/xtrem-standalone/notification-action-close',
                        'Close',
                        {},
                        translations,
                        locale,
                    )}
                    data-testid="xe-notification-action-close"
                    onClick={onClose}
                >
                    <Icon
                        tooltipMessage={localize(
                            '@sage/xtrem-standalone/notification-action-close',
                            'Close',
                            {},
                            translations,
                            locale,
                        )}
                        type="close"
                    />
                </IconButton>
            );
        }

        return (
            <IconButton
                aria-label={localize(
                    '@sage/xtrem-standalone/notification-action-delete',
                    'Delete',
                    {},
                    translations,
                    locale,
                )}
                data-testid="xe-notification-action-delete"
                onClick={onDeleteNotification}
            >
                <Icon
                    color={tokens.colorsSemanticNegative500}
                    tooltipMessage={localize(
                        '@sage/xtrem-standalone/notification-action-delete',
                        'Delete',
                        {},
                        translations,
                        locale,
                    )}
                    type="delete"
                />
            </IconButton>
        );
    };

    const onHandleAnchorAction =
        (action: NotificationAction, isExternalLink: boolean, isDownloadLink: boolean, href: string) =>
        async (event: MouseEvent<HTMLElement> | KeyboardEvent<HTMLElement>) => {
            if (event.ctrlKey || event.metaKey) {
                await markUnreadNotificationAsRead();
                return;
            }

            event.preventDefault();

            if (isExternalLink || isDownloadLink) {
                await markUnreadNotificationAsRead();
                try {
                    if (!isSkipExternalLinkConfirmation() && !isDownloadLink) {
                        await getConfirmation();
                    }
                    window.open(href, '_blank', 'noreferrer');
                } catch {
                    // eslint-disable-next-line no-console
                    console.warn(`Navigation to external link "${href}" cancelled by user.`);
                }
                return;
            }

            await markUnreadNotificationAsRead();
            onInternalNavigationChange(action.link);
            setNotificationCenterOpen(false);
        };

    return (
        <div
            className={`xe-notification-card xe-notification-card--${notification.isRead ? 'read' : 'unread'}`}
            data-testid="xe-notification-card"
            data-status={notification.isRead ? 'read' : 'unread'}
        >
            <div className="xe-notification-icon-container">
                <Icon
                    className="xe-notification-icon"
                    data-testid="xe-notification-icon"
                    type={notification.icon}
                    bg={levels[notification.level]}
                    color={tokens.colorsYang100}
                    role="img"
                />
            </div>
            <div className="xe-notification-body-container">
                <div className="xe-notification-body-header" data-testid="xe-notification-body-header">
                    <span data-testid="xe-notification-time-ago" className="xe-notification-time-ago">
                        {distance}
                    </span>
                    {!onClose && notificationActions.menu.length ? (
                        <ActionPopover data-testid="xe-notification-menu-actions">
                            {notificationActions.menu.map((action: NotificationAction) => {
                                if (
                                    action._id === 'notification-action-mark-read' ||
                                    action._id === 'notification-action-mark-unread'
                                ) {
                                    return (
                                        <ActionPopoverItem
                                            data-testid={getNotificationActionTestId(action.title)}
                                            icon={(action.icon as IconType) || 'none'}
                                            key={action._id}
                                            onClick={onHandleButtonAction(action)}
                                        >
                                            {action.title}
                                        </ActionPopoverItem>
                                    );
                                }

                                const isExternalLink = isExternalUrl(action.link);
                                const isDownloadLink = isDownloadUrl(action.link);
                                return (
                                    <ActionPopoverItem
                                        data-testid={getNotificationActionTestId(action.title)}
                                        href={action.link}
                                        icon={(action.icon as IconType) || 'none'}
                                        key={action._id}
                                        onClick={onHandleAnchorAction(
                                            action,
                                            isExternalLink,
                                            isDownloadLink,
                                            action.link,
                                        )}
                                    >
                                        {action.title}
                                    </ActionPopoverItem>
                                );
                            })}
                            {notificationActions.menu.length ? <ActionPopoverDivider /> : null}
                            <ActionPopoverItem
                                data-testid="xe-notification-action-delete"
                                data-is-destructive="true"
                                icon="delete"
                                onClick={onDeleteNotification}
                            >
                                {localize(
                                    '@sage/xtrem-standalone/notification-action-delete',
                                    'Delete',
                                    {},
                                    translations,
                                    locale,
                                )}
                            </ActionPopoverItem>
                        </ActionPopover>
                    ) : (
                        renderTopRightButton()
                    )}
                </div>
                <div className="xe-notification-body-content">
                    <h1 className="xe-notification-body-title" data-testid="xe-notification-title">
                        {notification.title}
                    </h1>
                    <p className="xe-notification-body-description" data-testid="xe-notification-description">
                        {notification.description}
                    </p>
                    <div className="xe-notification-body-actions" data-testid="xe-notification-actions">
                        {notificationActions.buttons.map((action: NotificationAction) => {
                            if (
                                action._id === 'notification-action-mark-read' ||
                                action._id === 'notification-action-mark-unread'
                            ) {
                                // Rendering mark as read/unread as buttons to avoid re-renders.
                                return (
                                    <ButtonMinor
                                        buttonType="tertiary"
                                        data-testid={getNotificationActionTestId(action.title)}
                                        key={action._id}
                                        onClick={onHandleButtonAction(action)}
                                    >
                                        {action.title}
                                    </ButtonMinor>
                                );
                            }

                            const isExternalLink = isExternalUrl(action.link);
                            const isDownloadLink = isDownloadUrl(action.link);

                            const buttonProps = {
                                key: action._id,
                                'data-testid': getNotificationActionTestId(action.title),
                                href: action.link,
                                target: isExternalLink || isDownloadLink ? '_blank' : '_self',
                                onClick: onHandleAnchorAction(action, isExternalLink, isDownloadLink, action.link),
                                children: (
                                    <>
                                        {action.title}
                                        {isExternalLink && <Icon type="link" />}
                                        {isDownloadLink && <Icon type="download" />}
                                    </>
                                ),
                            };

                            return action.style === 'primary' || action.style === 'secondary' ? (
                                <ButtonMajor buttonType={action.style} {...buttonProps} />
                            ) : (
                                <ButtonMinor buttonType="tertiary" {...buttonProps} />
                            );
                        })}
                    </div>
                </div>
            </div>
            {isSkipExternalLinkConfirmation() ? null : (
                <ExternalLinkConfirmationDialog
                    isOpen={isOpen}
                    locale={locale}
                    onCancel={onCancel}
                    onConfirm={onConfirm}
                    translations={translations}
                />
            )}
        </div>
    );
}

const mapStateToProps = (
    state: XtremStandaloneState,
    props: NotificationCenterItemExternalProps,
): NotificationCenterItemProps => ({
    ...props,
    deleteNotification: actions.actionStub,
    locale: state.user?.locale || 'en-US',
    notification: state.notifications.find(
        (notification: Notification) => notification._id === props.id,
    ) as Notification,
    markNotificationAsRead: actions.actionStub,
    markNotificationAsUnread: actions.actionStub,
    onInternalNavigationChange: actions.actionStub,
    setNotificationCenterOpen: actions.actionStub,
    translations: state.translations,
});

const mapDispatchToProps = (dispatch: actionTypes.AppThunkDispatch) => ({
    deleteNotification: (id: string) => dispatch(actions.deleteNotification(id)),
    markNotificationAsRead: (id: string) => dispatch(actions.markNotificationAsRead(id)),
    markNotificationAsUnread: (id: string) => dispatch(actions.markNotificationAsUnread(id)),
    onInternalNavigationChange: (path: string) => dispatch(actions.onInternalNavigationChange(path, true)),
    setNotificationCenterOpen: (isOpen: boolean) => dispatch(actions.setNotificationCenterOpen(isOpen)),
});

export const ConnectedNotificationCenterItem = connect(mapStateToProps, mapDispatchToProps)(NotificationCenterItem);
