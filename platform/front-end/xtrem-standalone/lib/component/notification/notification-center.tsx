import Sidebar from 'carbon-react/esm/components/sidebar/sidebar.component';
import React from 'react';
import { connect } from 'react-redux';
import { localize } from '../../service/standalone-i18n-service';
import type * as actionTypes from '../../standalone-redux/action-types';
import * as actions from '../../standalone-redux/actions';
import type { XtremStandaloneState } from '../../standalone-redux/state';
import './notification-center.scss';
import { NotificationCenterHeader } from './notification-center-header';
import { ConnectedNotificationCenterItem } from './notification-center-item';
import type { NotificationCenterExternalProps, NotificationCenterProps } from './notification-center-types';
import { EmptyNotification } from './empty-notification';

export function NotificationCenter(props: NotificationCenterProps): React.ReactElement {
    const onCloseSidebar = () => {
        props.setNotificationCenterOpen(false);
    };

    return (
        <Sidebar
            data-element="notification-center"
            header={<NotificationCenterHeader locale={props.locale} translations={props.translations} />}
            open={props.open}
            onCancel={onCloseSidebar}
        >
            <div className="xe-notification-center">
                <div data-testid="xe-notification-content" className="xe-notification-content">
                    {props.notifications.length > 0 ? (
                        props.notifications.map(notification => (
                            <ConnectedNotificationCenterItem key={notification._id} id={notification._id} />
                        ))
                    ) : (
                        <div className="xe-notification-empty">
                            <EmptyNotification />
                            <span className="xe-notification-empty-title">
                                {localize(
                                    '@sage/xtrem-standalone/no-notification-title',
                                    "You're up to date.",
                                    {},
                                    props.translations,
                                    props.locale,
                                )}
                            </span>
                            <span className="xe-notification-empty-description">
                                {localize(
                                    '@sage/xtrem-standalone/no-notification-description',
                                    "You'll get a notification when there's something new.",
                                    {},
                                    props.translations,
                                    props.locale,
                                )}
                            </span>
                        </div>
                    )}
                </div>
            </div>
        </Sidebar>
    );
}

const mapStateToProps = (
    state: XtremStandaloneState,
    ownProps: NotificationCenterExternalProps,
): NotificationCenterProps => ({
    ...ownProps,
    locale: state.user?.locale || 'en-US',
    notifications: state.notifications,
    markAllNotificationsAsRead: actions.actionStub,
    setNotificationCenterOpen: actions.actionStub,
    translations: state.translations,
});

const mapDispatchToProps = (dispatch: actionTypes.AppThunkDispatch) => ({
    markAllNotificationsAsReaad: () => dispatch(actions.markAllNotificationsAsRead()),
    setNotificationCenterOpen: (isOpen: boolean) => dispatch(actions.setNotificationCenterOpen(isOpen)),
});

// tslint:disable-next-line:variable-name
export const ConnectedNotificationCenter = connect(mapStateToProps, mapDispatchToProps)(NotificationCenter);
