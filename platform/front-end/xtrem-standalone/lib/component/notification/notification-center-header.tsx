import React from 'react';
import { localize } from '../../service/standalone-i18n-service';
import type { NotificationCenterHeaderProps } from './notification-center-types';

export function NotificationCenterHeader({ translations, locale }: NotificationCenterHeaderProps): React.ReactElement {
    return (
        <div className="xe-notification-center-header">
            <span className="xe-notification-center-header-title">
                {localize(
                    '@sage/xtrem-standalone/notification-center',
                    'Notification center',
                    {},
                    translations,
                    locale,
                )}
            </span>
        </div>
    );
}
