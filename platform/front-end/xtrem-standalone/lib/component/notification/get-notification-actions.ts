import type { NotificationAction } from '@sage/xtrem-shared';
import { isEmpty } from 'lodash';

export interface GetNotificationActionsResult {
    buttons: NotificationAction[];
    menu: NotificationAction[];
}

export const READ_NOTIFICATION_ACTION: NotificationAction = {
    _id: 'notification-action-mark-read',
    icon: 'hide',
    link: '',
    style: 'tertiary',
    title: '',
};

export const UNREAD_NOTIFICATION_ACTION: NotificationAction = {
    _id: 'notification-action-mark-unread',
    icon: 'view',
    link: '',
    style: 'tertiary',
    title: '',
};

const getReadOrUnreadNotificationAction = (
    isNotificationRead: boolean,
    markReadTitle: string,
    markUnreadTitle: string,
): NotificationAction => {
    return isNotificationRead
        ? { ...UNREAD_NOTIFICATION_ACTION, title: markUnreadTitle }
        : { ...READ_NOTIFICATION_ACTION, title: markReadTitle };
};

export const getNotificationActions = (
    isNotificationRead: boolean,
    actions: NotificationAction[],
    markReadTitle: string,
    markUnreadTitle: string,
): GetNotificationActionsResult => {
    const buttons: NotificationAction[] = [];
    const menu: NotificationAction[] = [];

    if (isEmpty(actions)) {
        buttons.push(getReadOrUnreadNotificationAction(isNotificationRead, markReadTitle, markUnreadTitle));
    } else if (actions.length === 1) {
        buttons.push(actions[0], getReadOrUnreadNotificationAction(isNotificationRead, markReadTitle, markUnreadTitle));
    } else {
        buttons.push(actions[0], actions[1]);

        if (actions.length > 2) {
            menu.push(...actions.slice(2));
        }

        menu.push(getReadOrUnreadNotificationAction(isNotificationRead, markReadTitle, markUnreadTitle));
    }

    return { buttons, menu };
};
