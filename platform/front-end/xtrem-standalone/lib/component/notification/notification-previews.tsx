import type { Notification } from '@sage/xtrem-shared';
import React, { useEffect, useState } from 'react';
import type { ReactElement } from 'react';
import { connect } from 'react-redux';
import { NotificationPreviewCard } from './notification-preview-card';
import type { NotificationPreview } from './notification-types';
import type { XtremStandaloneState } from '../../standalone-redux/state';
import './notification-center.scss';

export interface NotificationPreviewProps {
    notifications: Notification[];
}

export function NotificationPreviews({ notifications }: NotificationPreviewProps): ReactElement {
    const [previews, setPreviews] = useState<NotificationPreview[]>(() => {
        const initPreviews: NotificationPreview[] = notifications
            .filter((notification: Notification) => {
                return notification.shouldDisplayToast === true;
            })
            .map((notification: Notification) => {
                // INFO: This is a hack to ensure that the notification can be tested.
                return { ...notification, isStale: process.env.NODE_ENV !== 'test' } as NotificationPreview;
            });
        return initPreviews;
    });

    const onClosePreview = (id: string) => () => {
        setPreviews((state: NotificationPreview[]) => {
            return state.map((preview: NotificationPreview) => {
                return preview._id === id ? { ...preview, isStale: true } : preview;
            });
        });
    };

    useEffect(() => {
        setPreviews((prevPreviews: NotificationPreview[]) => {
            const currPreviews: NotificationPreview[] = notifications
                .filter((notification: Notification) => {
                    return notification.shouldDisplayToast === true;
                })
                .map((notification: Notification) => {
                    const match = prevPreviews.find((preview: NotificationPreview) => {
                        return preview._id === notification._id;
                    });

                    return typeof match === 'undefined' ? { ...notification, isStale: false } : match;
                });

            return currPreviews;
        });
    }, [notifications]);

    return (
        <div className="xe-notification-preview-wrapper" data-testid="xe-notification-preview-wrapper">
            {previews.map((preview: NotificationPreview) => (
                <NotificationPreviewCard key={preview._id} preview={preview} onClose={onClosePreview(preview._id)} />
            ))}
        </div>
    );
}

const mapStateToProps = (state: XtremStandaloneState): NotificationPreviewProps => ({
    notifications: state.notifications,
});

export const ConnectedNotificationPreviews = connect(mapStateToProps)(NotificationPreviews);
