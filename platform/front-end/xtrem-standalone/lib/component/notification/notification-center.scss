[data-element='notification-center'],
.xe-notification-preview-wrapper {
    .xe-notification-center-header {
        .xe-notification-center-header-title {
            color: var(--colorsUtilityYin090);
            font: var(--typographySidebarTitleM);
        }
    }

    [data-element='sidebar-content'] {
        width: 100%;
        padding: 0;
    }
}

[data-component='action-popover'] {
    [data-is-destructive='true'] {
        [data-element='action-popover-menu-item-icon'][type='delete'] {
            color: var(--colorsSemanticNegative500);
        }

        [data-element='delete'] {
            color: var(--colorsSemanticNegative500);
        }
    }
}

.xe-notification-center {
    position: relative;
    height: 100%;

    .xe-notification-content {
        background-color: var(--colorsUtilityYang100);
        min-height: calc(100% - 32px);
        padding: 16px;

        .xe-notification-card {
            display: flex;
            padding: 16px 24px;
            border-radius: 8px;
            background: var(--colorsUtilityYang100);
            box-shadow: 0px -1px 0px 0px var(--colorsUtilityMajor100) inset;
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
            }

            &.xe-notification-card--read {
                background: var(--colorsUtilityMajor040);
            }

            .xe-notification-icon-container {
                display: flex;

                .xe-notification-icon {
                    padding: 12px;
                    width: 16px;
                    height: 16px;
                    border-radius: 4px;

                    &::before {
                        font-size: 16px;
                    }
                }
            }

            .xe-notification-body-container {
                display: flex;
                flex-flow: column;
                margin-left: 18px;
                padding: 6px;
                width: 100%;
                overflow: hidden;

                .xe-notification-body-header {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;

                    .xe-notification-time-ago {
                        color: var(--colorsUtilityYin055);
                        font: var(--typographyCardSelectSubtitleM);
                    }

                    [data-component='action-popover-wrapper'] {
                        margin: 0;
                    }
                }

                .xe-notification-body-content {
                    .xe-notification-body-title {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        font: var(--typographyMessageHeadingL);
                        color: var(--colorsUtilityYin090);
                        margin: 0;
                    }

                    .xe-notification-body-description {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        font: var(--typographyMessageTextM);
                        color: var(--colorsUtilityYin090);
                    }

                    .xe-notification-body-actions {
                        display: flex;
                        flex-direction: row;
                        justify-content: flex-start;
                        align-items: center;

                        > button,
                        a {
                            [data-element='main-text'] {
                                display: flex;
                                flex-direction: row;
                                justify-content: flex-start;
                                align-items: center;
                            }

                            [data-component='icon'] {
                                margin: 0 0 0 var(--spacing100);
                            }
                        }

                        > button:not(:last-child),
                        a:not(:last-child) {
                            margin-right: var(--spacing200);
                        }
                    }
                }
            }
        }

        .xe-notification-empty {
            display: flex;
            flex-flow: column wrap;
            justify-content: center;
            align-content: center;
            align-items: center;
            padding-top: 100px;

            .xe-notification-empty-title {
                font: var(--typographySidebarTitleL);
                color: var(--colorsUtilityYin090);
                margin: 19px 0px;
            }

            .xe-notification-empty-description {
                text-align: center;
                font: var(--typographySidebarParagraphM);
                color: var(--colorsUtilityYin090);
                width: 288px;
            }
        }
    }
}

.xe-notification-preview-wrapper {
    position: absolute;
    top: 48px;
    right: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    width: 360px;
    max-height: 100%;

    .xe-notification-card {
        display: flex;
        padding: 16px 24px;
        margin-bottom: 4px;
        border-radius: 8px;
        background: var(--colorsUtilityYang100);
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

        &.xe-notification-card--read {
            background: var(--colorsUtilityMajor040);
        }

        [data-is-destructive='true'] {
            [data-element='action-popover-menu-item-icon'][type='delete'] {
                color: var(--colorsSemanticNegative500);
            }

            [data-element='delete'] {
                color: var(--colorsSemanticNegative500);
            }
        }

        .xe-notification-icon-container {
            display: flex;

            .xe-notification-icon {
                padding: 12px;
                width: 16px;
                height: 16px;
                border-radius: 4px;

                &::before {
                    font-size: 16px;
                }
            }
        }

        .xe-notification-body-container {
            display: flex;
            flex-flow: column;
            margin-left: 18px;
            padding: 6px;
            width: 100%;
            overflow: hidden;

            .xe-notification-body-header {
                width: 100%;
                display: flex;
                justify-content: space-between;

                .xe-notification-time-ago {
                    color: var(--colorsUtilityYin055);
                    font: var(--typographyCardSelectSubtitleM);
                }

                [data-component='action-popover-wrapper'] {
                    margin: 0;
                }
            }

            .xe-notification-body-content {
                .xe-notification-body-title {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font: var(--typographyMessageHeadingL);
                    color: var(--colorsUtilityYin090);
                    margin: 0;
                }

                .xe-notification-body-description {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font: var(--typographyMessageTextM);
                    color: var(--colorsUtilityYin090);
                }

                .xe-notification-body-actions {
                    display: flex;
                    flex-direction: row;
                    justify-content: flex-start;
                    align-items: center;

                    > button,
                    a {
                        [data-element='main-text'] {
                            display: flex;
                            flex-direction: row;
                            justify-content: flex-start;
                            align-items: center;
                        }

                        [data-component='icon'] {
                            margin: 0 0 0 var(--spacing100);
                        }
                    }

                    > button:not(:last-child),
                    a:not(:last-child) {
                        margin-right: var(--spacing200);
                    }
                }
            }
        }
    }
}

[data-element='notification-center'] [data-element='sidebar'] {
    max-width: 100vw;
}
