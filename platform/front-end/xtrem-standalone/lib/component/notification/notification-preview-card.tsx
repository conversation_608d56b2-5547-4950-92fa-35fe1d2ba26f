import React, { useEffect } from 'react';
import type { ReactElement } from 'react';
import type { NotificationPreview } from './notification-types';
import { ConnectedNotificationCenterItem } from './notification-center-item';

export const NOTIFICATION_PREVIEW_DURATAION = 7_000;

export interface NotificationPreviewCardProps {
    onClose: () => void;
    preview: NotificationPreview;
}

export function NotificationPreviewCard({
    onClose,
    preview,
}: NotificationPreviewCardProps): ReactElement<any, any> | null {
    useEffect(() => {
        let timeoutId: any;

        if (!preview.isStale) {
            timeoutId = setTimeout(() => {
                onClose();
            }, NOTIFICATION_PREVIEW_DURATAION);
        }

        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };
        // INFO: Dependencies are ommitted on purpose to ensure notification timers don't interfere with
        //       each others.
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return !preview.shouldDisplayToast || preview.isStale ? null : (
        <div
            aria-live="polite"
            aria-relevant="additions"
            className="xe-notification-card-preview"
            data-testid="xe-notification-card-preview"
            role="status"
        >
            <ConnectedNotificationCenterItem id={preview._id} onClose={onClose} />
        </div>
    );
}
