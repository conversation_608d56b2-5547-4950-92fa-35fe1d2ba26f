@import '../variables.scss';
@import '../mixins.scss';

@mixin header_list_item {
    cursor: default;
    display: flex;
    line-height: 41px;
    margin-right: 8px;
    padding: 0 16px;
    position: relative;

    &:active {
        outline: none !important;
    }
}

.xe-header {
    display: flex;
    flex-flow: row nowrap;
    height: 48px;
    position: fixed;
    transition: box-shadow 0.2s ease;
    justify-content: space-between;
    width: 100vw;
    z-index: 2012;
    background: #000;

    .xe-skip-link a:focus {
        border: 3px solid var(--colorsSemanticFocus500);
        outline: none;
        box-shadow: none;
        font-size: 14px;
        color: var(--colorsYin055);
        top: 6px;
    }

    .xe-logo-container {
        order: 1;
        width: 100%;
        display: flex;
        text-decoration: none;
        outline: none;

        @include focusable;

        .xe-text-sage-logo {
            height: 32px;
            width: 58px;
            margin-top: 8px;
            margin-bottom: 8px;
            margin-left: 24px;
            padding-right: 16px;
            border-right: 1px solid #5e5e5e;
        }

        &:hover {
            text-decoration: none;
        }

        .xe-sage-logo {
            height: 26px;
            position: relative;
            top: 8px;
        }

        .xe-text-sage {
            height: 48px;
            position: relative;
        }

        .xe-text-logo-main-name {
            color: var(--colorsYang100);
            font-family: 'Sage Text', sans-serif;
            font-weight: 500;
            font-size: 18px;
            line-height: 18px;
            margin: 14px 12px 10px 16px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .xe-text-logo-sub-name {
            border-left: 1px solid #b2b2b2;
            color: var(--colorsYang100);
            font-family: var(--fontFamiliesDefault);
            font-size: 20px;
            height: 24px;
            line-height: 24px;
            margin-left: 0;
            margin-top: 12px;
            margin-bottom: 12px;
            padding-left: 12px;
            white-space: nowrap;
        }
    }

    // align Pendo's badge to the top right
    .pendo-resource-center-badge-notification-bubble {
        transform: translate(37px, 7px) scale(0.428568, 0.46152);

        // hide Pendo's badge animation
        &::before {
            display: none;
        }
    }

    // hide Pendo's unread count
    .pendo-notification-bubble-unread-count {
        display: none;
    }

    .xe-actions {
        display: flex;
        justify-content: flex-end;
        padding-right: 24px;
        order: 2;
        margin: 0;
        padding-left: 0;

        button {
            cursor: pointer;
        }

        li {
            list-style: none;
        }

        .xe-ai-assistant-action .xe-profile-action-icon-button {
            padding-top: 4px;
        }

        .xe-profile-action {
            display: flex;

            &.xe-profile-action-disabled .xe-icon-button:hover {
                background: #000;
            }

            .xe-icon-button-link-notification {
                position: relative;

                .xe-icon-button-link-notification-badge {
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    background-color: var(--colorsSemanticFocus500);
                    position: absolute;
                    right: 16px;
                    top: 14px;
                    border: 1px solid transparent;
                }
            }

            .xe-profile-action-button {
                appearance: none;
                background: transparent;
                border: none;
            }
        }

        .xe-profile-action.xe-notification-action {
            position: relative;
        }

        //.xe-profile-action,
        .xe-tenants-action {
            @include header_list_item;
            padding: 0;

            // Remove position absolute on mobile
            @include extra_small {
                position: inherit;
            }

            .xe-tenants-action-button {
                padding: 0 6px;
                appearance: none;
                background: transparent;
                border: none;
                @include focusable;
            }

            .xe-tenants-current-tenant-wrapper {
                padding: 0 16px;
            }

            &.xe-profile-action-icon-button {
                position: relative;
                padding-right: 0;
                padding-left: 0;
                cursor: pointer;
            }

            .xe-icon-label {
                position: absolute;
                top: -4px;
                right: 0px;
            }
        }

        .xe-icon-button {
            padding-right: 16px;
            padding-left: 16px;
            text-decoration: none;
            color: var(--colorsYang100);
            display: block;
            height: 48px;
            line-height: 48px;
            @include focusable;

            &:hover {
                background: var(--colorsComponentsMenuSpringParent600);
            }
        }

        @mixin header_tenant_list_item_text {
            color: var(--colorsYang100);
            font-size: 16px;
            line-height: 48px;
            font-weight: 400;
            max-width: 256px;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: right;
            white-space: nowrap;
        }

        .xe-tenants-current-tenant-wrapper {
            display: flex;
            align-items: center;
            gap: 8px;

            .xe-tenants-current-tenant {
                @include header_tenant_list_item_text;
            }

            .xe-tenants-current-pill {
                display: flex;
                align-items: center;
                text-transform: capitalize;
            }

            >span {
                height: 48px;
            }
        }

        .xe-tenants-no-action {
            @include header_list_item;

            .xe-tenants-action-button {
                padding: 0 6px;
                appearance: none;
                background: transparent;
                border: none;
            }

            &:focus {
                outline: none;
            }

            .xe-tenants-current-tenant {
                @include header_tenant_list_item_text;
            }
        }

        .xe-tenants-action {
            @include focusable;

            .xe-tenants-current-tenant-wrapper {
                &:hover {
                    background: var(--colorsComponentsMenuSpringParent600);

                    & span[data-component="pill"] {
                        border-color: var(--colorsYang100);
                    }
                }
            }

            .xe-tenants-list-wrapper {

                @include extra_small {
                    transform: translate(0, 1px);
                    left: 0;
                    width: 100%;
                }

                left: auto;
                right: 0;
                opacity: 1;
                position: absolute;
                transform: translate(0, 0);
                transition: all 0.2s ease;
                visibility: visible;
                width: 350px;
            }

            .xe-tenants-list-container {
                background: #000;
                box-shadow: var(--boxShadow200);
                max-height: calc(100dvh - 48px);
                overflow-y: auto;
                border-bottom-right-radius: var(--borderRadius100);
                border-bottom-left-radius: var(--borderRadius100);
                
                @include extra_small {
                    border-bottom-right-radius: 0;
                    border-bottom-left-radius: 0;
                }
            }

            .xe-tenants-list-content {
                font-size: 14px;
                line-height: 21px;

                .xe-tenant {

                    @include extra_small {
                        padding: 12px 24px;
                    }
                    font-family: var(--fontFamiliesDefault);
                    font-weight: var(--fontWeights500);
                    padding: 16px 24px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    text-decoration: none;
                    color: var(--colorsYang100);

                    .xe-tenant-name {
                        text-align: left;
                        min-width: 0;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .xe-tenant-kind {
                        flex: 0 0 auto;
                        margin-right: auto;
                        text-transform: capitalize;
                    }

                    .xe-tenant-icon {
                        flex: 0 0 auto;
                    }

                    &:hover,
                    &:focus {
                        background: var(--colorsActionMajor600);
                        color: var(--colorsYang100);
                        outline: none;

                        &>span {
                            color: var(--colorsYang100);
                        }

                        & span[data-component="pill"] {
                            border-color: var(--colorsYang100);
                        }

                    }

                    &:visited {
                        color: var(--colorsYang100);

                        &:hover,
                        &:focus {
                            color: var(--colorsYang100);
                        }
                    }
                }
            }
        }
    }

    .xe-profile-profile {
        &:hover {
            background: var(--colorsComponentsMenuSpringParent600);
        }

        &.xe-opened {
            .xe-profile-container {
                opacity: 1;
                transform: translate(0, 0);
                visibility: visible;
                border-bottom-right-radius: var(--borderRadius100);
                border-bottom-left-radius: var(--borderRadius100);
            }
        }

        .xe-profile-wrapper {
            width: 56px;
        }
    }

    .xe-profile-picture {
        width: 100%;
        height: 100%;
        background-color: transparent;
        border: 0;
        padding: 0;

        &:focus {
            outline: 3px solid var(--colorsSemanticFocus500);
        }
    }

    .xe-profile-picture-empty {
        padding: 0;

        &::before {
            font-family: $fontCarbonIcons;
            content: '';
            font-size: 16px;
            display: block;
            height: 20px;
            width: 20px;
            text-align: center;
            background-color: rgb(242, 245, 246);
            line-height: 24px;
            color: rgb(128, 153, 164);
            border-radius: var(--borderRadiusCircle);
            border: 2px solid var(--colorsYang100);
            margin: 0 auto;
        }
    }

    .xe-profile-picture-large {
        border: 1px solid #b3c2c8;
        height: 120px;
        width: 120px;
        box-sizing: border-box;
        position: relative;
        background-size: cover;

        &.xe-profile-picture-empty {
            border: 2px solid var(--colorsYang100);
            border-radius: var(--borderRadiusCircle);

            &::before {
                font-family: $fontCarbonIcons;
                content: '';
                font-size: 64px;
                display: block;
                height: 100%;
                width: 100%;
                text-align: center;
                background-color: rgb(242, 245, 246);
                line-height: 120px;
                color: rgb(128, 153, 164);
            }
        }
    }

    .xe-profile-header {
        display: flex;
        background: #000;
        line-height: 21px;
        padding: 16px 24px;
    }

    .xe-profile-details {
        color: var(--colorsYang100);
        box-shadow:
            0 10px 20px 0 rgba(0, 20, 29, 0.2),
            0 20px 40px 0 rgba(0, 20, 29, 0.1);
    }

    .xe-tenants-action>span {
        line-height: 41px;
        height: 41px;
    }

    .xe-profile-actions {
        width: 198px;
        margin: auto 0;
        padding-left: 24px;

        .xe-profile-email {
            color: var(--colorsYang100);
            font-size: 14px;
            font-weight: 400;
            margin-bottom: 8px;
        }

        .xe-profile-name {
            font-size: 16px;
            font-weight: var(--fontWeights700);
            line-height: 24px;
            color: var(--colorsYang100);
        }
    }
}
