import Button from 'carbon-react/esm/components/button';
import { Checkbox } from 'carbon-react/esm/components/checkbox';
import Dialog from 'carbon-react/esm/components/dialog';
import Form from 'carbon-react/esm/components/form';
import Typography from 'carbon-react/esm/components/typography';
import React, { useState } from 'react';
import type { ChangeEvent, ReactElement } from 'react';
import type { Dict } from '@sage/xtrem-shared';
import { localize } from '../../service/standalone-i18n-service';
import './external-link-confirmation-dialog.scss';

export interface ExternalLinkConfirmationDialogProps {
    isOpen: boolean;
    onCancel: (isSkipConfirmationChecked: boolean) => void;
    onConfirm: (isSkipConfirmationChecked: boolean) => void;
    locale: string;
    translations: Dict<string>;
}

export function ExternalLinkConfirmationDialog({
    isOpen,
    locale,
    onCancel,
    onConfirm,
    translations,
}: ExternalLinkConfirmationDialogProps): ReactElement {
    const [isSkipConfirmationChecked, setSkipConfirmationChecked] = useState<boolean>(false);

    const onChangeSkipConfirmationCheckbox = (event: ChangeEvent<HTMLInputElement>) => {
        setSkipConfirmationChecked(event.target.checked);
    };

    const onCancelButtonClick = () => {
        onCancel(isSkipConfirmationChecked);
    };

    const onConfirmButtonClick = () => {
        onConfirm(isSkipConfirmationChecked);
    };

    return (
        <Dialog
            data-element="xe-external-link-confirmation-dialog"
            data-testid="xe-external-link-confirmation-dialog"
            open={isOpen}
            subtitle={localize(
                '@sage/xtrem-standalone/external-link-confirmation-dialog-subtitle',
                'Proceed with caution.',
                {},
                translations,
                locale,
            )}
            title={localize(
                '@sage/xtrem-standalone/external-link-confirmation-dialog-title',
                'External link',
                {},
                translations,
                locale,
            )}
        >
            <Form
                leftSideButtons={
                    <div className="xe-external-link-confirmation-dialog-footer">
                        <div className="xe-external-link-confirmation-dialog-skip-confirmation">
                            <Checkbox
                                data-testid="xe-external-link-confirmation-dialog-skip-confirmation-checkbox"
                                label={localize(
                                    '@sage/xtrem-standalone/external-link-confirmation-dialog-skip-confirmation-label',
                                    `Don't show this message again`,
                                    {},
                                    translations,
                                    locale,
                                )}
                                name="xe-external-link-confirmation-dialog-skip-confirmation-checkbox"
                                onChange={onChangeSkipConfirmationCheckbox}
                            />
                        </div>
                        <div className="xe-external-link-confirmation-dialog-actions">
                            <div className="xe-external-link-confirmation-dialog-action-cancel">
                                <Button
                                    buttonType="secondary"
                                    data-testid="xe-external-link-confirmation-dialog-action-cancel-button"
                                    name="xe-external-link-confirmation-dialog-action-cancel-button"
                                    onClick={onCancelButtonClick}
                                >
                                    {localize(
                                        '@sage/xtrem-standalone/external-link-confirmation-dialog-action-cancel',
                                        'Cancel',
                                        {},
                                        translations,
                                        locale,
                                    )}
                                </Button>
                            </div>
                            <div className="xe-external-link-confirmation-dialog-action-confirm">
                                <Button
                                    buttonType="secondary"
                                    data-testid="xe-external-link-confirmation-dialog-action-confirm-button"
                                    name="xe-external-link-confirmation-dialog-action-confirm-button"
                                    onClick={onConfirmButtonClick}
                                >
                                    {localize(
                                        '@sage/xtrem-standalone/external-link-confirmation-dialog-action-confirm',
                                        'Continue',
                                        {},
                                        translations,
                                        locale,
                                    )}
                                </Button>
                            </div>
                        </div>
                    </div>
                }
                stickyFooter
            >
                <Typography>
                    <span data-testid="xe-external-link-confirmation-dialog-content-line-1">
                        {localize(
                            '@sage/xtrem-standalone/external-link-confirmation-dialog-content-line-1',
                            'This link takes you to an external website and opens in a new browser tab.',
                            {},
                            translations,
                            locale,
                        )}
                    </span>
                    <br />
                    <span data-testid="xe-external-link-confirmation-dialog-content-line-2">
                        {localize(
                            '@sage/xtrem-standalone/external-link-confirmation-dialog-content-line-2',
                            'Sage is not responsible for the content or privacy policies on that page.',
                            {},
                            translations,
                            locale,
                        )}
                    </span>
                </Typography>
            </Form>
        </Dialog>
    );
}
