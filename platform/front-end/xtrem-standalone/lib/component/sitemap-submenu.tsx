import type { SitemapEntry } from '@sage/xtrem-shared';
import * as React from 'react';
import Icon from 'carbon-react/esm/components/icon';
import { camelCase, sortBy } from 'lodash';

export interface SitemapSubMenuProps {
    level: number;
    sitemapEntry: SitemapEntry;
    onSelectLink: (path: string) => (event: React.MouseEvent) => void;
    bookmarksDisabled?: boolean;
    forwardRef?: React.MutableRefObject<HTMLAnchorElement | null>;
    path: string | null;
}

export function SitemapSubMenu(props: SitemapSubMenuProps) {
    const { sitemapEntry } = props;
    const [isOpen, setIsOpen] = React.useState(false);
    const firstElementRef = React.useRef<HTMLAnchorElement | null>(null);
    const toggleChildrenElements = () => {
        if (isOpen) {
            setIsOpen(false);
        } else {
            setIsOpen(true);
            setTimeout(() => {
                if (firstElementRef.current) {
                    firstElementRef.current.focus();
                }
            }, 10);
        }
    };

    const hasChildren = sitemapEntry.children && !props.sitemapEntry.isPage;
    const onItemClicked = (event: React.MouseEvent<HTMLAnchorElement>) => {
        if (props.sitemapEntry.isPage) {
            if (!event.ctrlKey && !event.metaKey) {
                event.preventDefault();
                props.onSelectLink(props.sitemapEntry.id!)(event);
            }
        } else {
            event.preventDefault();
            toggleChildrenElements();
        }
    };

    const headerClasses = ['xe-navigation-sub-menu-title-line'];
    if (isOpen) {
        headerClasses.push('xe-navigation-sub-menu-title-line-open');
    }

    const containerClasses = ['xe-navigation-sub-menu', `xe-navigation-sub-menu-level-${props.level}`];
    if (!hasChildren) {
        containerClasses.push('xe-navigation-sub-menu-page');
    }

    if (sitemapEntry.isPage && props.path === sitemapEntry.id) {
        containerClasses.push('xe-navigation-sub-menu-selected');
    }

    const linkClassNames = ['xe-navigation-sub-menu-title', `xe-navigation-sub-menu-${camelCase(sitemapEntry.id)}`];

    return (
        <li className={containerClasses.join(' ')} data-testid={`xe-navigation-sub-menu-${camelCase(sitemapEntry.id)}`}>
            <div className={headerClasses.join(' ')}>
                <a
                    className={linkClassNames.join(' ')}
                    data-testid={`xe-navigation-sub-menu-link-${sitemapEntry.id}`}
                    data-menu-item={`secondary-menu-item-${sitemapEntry.id}`}
                    href={sitemapEntry.isPage ? `${document.location.origin}/${sitemapEntry.id}` : '#'}
                    onClick={onItemClicked}
                    ref={props.forwardRef}
                >
                    <span className="xe-navigation-sub-menu-title-label">{sitemapEntry.title}</span>
                    {hasChildren && <Icon type={isOpen ? 'chevron_up' : 'chevron_down'} mt="4px" mb="4px" mr={1} />}
                </a>
            </div>
            {hasChildren && isOpen && (
                <ul className="xe-navigation-sub-menu-body">
                    {sitemapEntry.children &&
                        sortBy(sitemapEntry.children, ['priority', 'title']).map((c: SitemapEntry, i: number) => (
                            <SitemapSubMenu
                                key={c.id}
                                level={props.level + 1}
                                sitemapEntry={c}
                                onSelectLink={props.onSelectLink}
                                bookmarksDisabled={props.bookmarksDisabled}
                                forwardRef={i === 0 ? firstElementRef : undefined}
                                path={props.path}
                            />
                        ))}
                </ul>
            )}
        </li>
    );
}
