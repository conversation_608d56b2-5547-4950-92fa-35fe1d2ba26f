import type { Dict, RootMenuItem, SitemapEntry } from '@sage/xtrem-shared';
import Icon from 'carbon-react/esm/components/icon';
import * as React from 'react';
import { connect } from 'react-redux';
import type * as actionTypes from '../standalone-redux/action-types';
import * as actions from '../standalone-redux/actions';
import type { XtremStandaloneState } from '../standalone-redux/state';
import { SitemapSubMenu } from './sitemap-submenu';

import { camelCase, sortBy } from 'lodash';
import type { UserDetails } from '../service/auth-service';
import { localize } from '../service/standalone-i18n-service';
import './navigation.scss';

export interface XtremNavigationProps {
    sitemap: RootMenuItem[];
    user: UserDetails | null;
    path: string;
    onInternalNavigationChange: (path: string) => void;
    setNavigationOpen: (isOpen: boolean) => void;
    isNavigationOpen: boolean;
    translations: Dict<string>;
    goHome: () => void;
}

export interface XtremNavigationState {
    openSitemapRootMenuItem?: SitemapEntry;
    oldSecondaryOpenGroup?: string;
}

export class XtremNavigation extends React.Component<XtremNavigationProps, XtremNavigationState> {
    private firstElementRef: React.MutableRefObject<HTMLAnchorElement | null>;

    constructor(props: XtremNavigationProps) {
        super(props);
        this.state = {};
        this.firstElementRef = React.createRef();
    }

    onToggleMenu = (event: React.MouseEvent) => {
        event.preventDefault();
        this.props.setNavigationOpen(!this.props.isNavigationOpen);
    };

    onOpenSecondaryMenu = (secondaryOpenGroup?: string) => (event: React.MouseEvent) => {
        event.preventDefault();
        if (this.state.oldSecondaryOpenGroup === secondaryOpenGroup) {
            this.setState({ oldSecondaryOpenGroup: undefined, openSitemapRootMenuItem: undefined });
            this.props.setNavigationOpen(false);
        } else {
            this.setState({ oldSecondaryOpenGroup: secondaryOpenGroup, openSitemapRootMenuItem: undefined });
        }
    };

    onOpenSitemapRootCategory = (rootItem: RootMenuItem) => (event: React.MouseEvent) => {
        event.preventDefault();
        if (this.state.openSitemapRootMenuItem === rootItem) {
            this.setState({ openSitemapRootMenuItem: undefined, oldSecondaryOpenGroup: undefined });
            this.props.setNavigationOpen(false);
        } else {
            this.setState({ openSitemapRootMenuItem: rootItem as SitemapEntry, oldSecondaryOpenGroup: undefined });
            this.props.setNavigationOpen(true);
            setTimeout(() => {
                if (this.firstElementRef.current) {
                    this.firstElementRef.current.focus();
                }
            }, 10);
        }
    };

    onSelectLink = (path: string) => (event: React.MouseEvent) => {
        event.preventDefault();
        this.props.setNavigationOpen(false);
        this.setState({ oldSecondaryOpenGroup: undefined, openSitemapRootMenuItem: undefined }, () => {
            this.props.onInternalNavigationChange(path);
        });
    };

    onSelectHomeLink = (event: React.MouseEvent) => {
        event.preventDefault();
        this.props.setNavigationOpen(false);
        this.setState({ openSitemapRootMenuItem: undefined, oldSecondaryOpenGroup: undefined }, this.props.goHome);
    };

    render() {
        const navigationPrimaryClasses = ['xe-navigation'];
        if (!this.props.isNavigationOpen) {
            navigationPrimaryClasses.push('xe-navigation-primary-closed');
        }

        const backdropClasses = ['xe-navigation-backdrop'];
        if (this.state.oldSecondaryOpenGroup || this.state.openSitemapRootMenuItem) {
            navigationPrimaryClasses.push('xe-navigation-menu-open');
            backdropClasses.push('xe-navigation-backdrop-open');
        }

        const containsCurrentOpenPage = (siteMapEntry: SitemapEntry): boolean | undefined => {
            const screenId = siteMapEntry.id;
            if (screenId && this.props.path.includes(screenId)) {
                return true;
            }
            return Boolean(siteMapEntry?.children?.find(containsCurrentOpenPage));
        };

        const homeClasses: string[] = ['xe-navigation-menu-item'];

        if (!this.props.path) {
            homeClasses.push('xe-navigation-is-active');
        }
        const homeTranslatedText = localize(
            '@sage/xtrem-standalone/home',
            'Home',
            {},
            this.props.translations,
            this.props.user?.locale || 'en-US',
        );
        return (
            <nav
                className={navigationPrimaryClasses.join(' ')}
                aria-label="Navigation menu"
                data-testid="xe-navigation-menu"
            >
                <div className="xe-navigation-primary xe-skip-link">
                    <div className="xe-navigation-menu">
                        <a
                            className={homeClasses.join(' ')}
                            href="#"
                            onClick={this.onSelectHomeLink}
                            data-testid="xe-navigation-home"
                            aria-label={homeTranslatedText}
                        >
                            <span className="xe-navigation-menu-item-icon">
                                <Icon type="home" color="#FFF" m={2} />
                            </span>
                            <span className="xe-navigation-menu-item-title">{homeTranslatedText}</span>
                        </a>
                        {(this.props.sitemap || []).map(rootItem => {
                            const classNames = [
                                'xe-navigation-menu-item',
                                `xe-navigation-menu-item-${camelCase(rootItem.id)}`,
                            ];
                            if (this.state.openSitemapRootMenuItem?.id === rootItem.id) {
                                classNames.push('xe-navigation-menu-item-selected');
                            }

                            if (containsCurrentOpenPage(rootItem as SitemapEntry)) {
                                classNames.push('xe-navigation-is-active');
                            }

                            return (
                                <a
                                    key={rootItem.id}
                                    href="#"
                                    className={classNames.join(' ')}
                                    onClick={this.onOpenSitemapRootCategory(rootItem)}
                                    data-menu-item={`primary-menu-item-${rootItem.id}`}
                                    data-testid={`xe-navigation-primary-menu-item-${rootItem.id}`}
                                    aria-label={rootItem.title}
                                >
                                    <span className="xe-navigation-menu-item-icon">
                                        <Icon type={rootItem.icon} color="#FFF" m={2} />
                                    </span>
                                    <span className="xe-navigation-menu-item-title">{rootItem.title}</span>
                                </a>
                            );
                        })}
                    </div>
                    <div className="xe-navigation-toggle">
                        <a
                            className="xe-navigation-menu-item"
                            href="#"
                            onClick={this.onToggleMenu}
                            data-testid="xe-navigation-toggle"
                        >
                            <span className="xe-navigation-menu-item-icon"></span>
                            <span className="xe-navigation-menu-item-title">
                                {this.props.isNavigationOpen
                                    ? localize(
                                          '@sage/xtrem-standalone/minimize',
                                          'Minimize',
                                          {},
                                          this.props.translations,
                                          this.props.user?.locale || 'en-US',
                                      )
                                    : localize(
                                          '@sage/xtrem-standalone/maximize',
                                          'Maximize',
                                          {},
                                          this.props.translations,
                                          this.props.user?.locale || 'en-US',
                                      )}
                            </span>
                        </a>
                    </div>
                </div>
                {this.state.openSitemapRootMenuItem && (
                    <div className="xe-navigation-secondary">
                        {!this.props.isNavigationOpen && (
                            <div className="xe-navigation-secondary-title" data-testid="xe-navigation-secondary-title">
                                <div className="xe-navigation-secondary-title-label">
                                    {this.state.openSitemapRootMenuItem.title}
                                </div>
                            </div>
                        )}
                        <div className="xe-navigation-secondary-body">
                            <ul className="xe-navigation-secondary-body-list">
                                {this.state.openSitemapRootMenuItem.children &&
                                    sortBy(this.state.openSitemapRootMenuItem.children, ['priority', 'title']).map(
                                        (p: SitemapEntry, i: number) => (
                                            <SitemapSubMenu
                                                key={p.id}
                                                sitemapEntry={p}
                                                level={1}
                                                onSelectLink={this.onSelectLink}
                                                forwardRef={i === 0 ? this.firstElementRef : undefined}
                                                path={this.props.path}
                                            />
                                        ),
                                    )}
                            </ul>
                        </div>
                    </div>
                )}
                {this.state.oldSecondaryOpenGroup ||
                    (this.state.openSitemapRootMenuItem && (
                        <div className={backdropClasses.join(' ')} onClick={this.onOpenSecondaryMenu()} />
                    ))}
            </nav>
        );
    }
}

const mapStateToProps = (state: XtremStandaloneState): XtremNavigationProps => ({
    sitemap: state.sitemap,
    user: state.user,
    isNavigationOpen: state.isNavigationOpen,
    translations: state.translations,
    path: state.path,
    onInternalNavigationChange: actions.actionStub,
    setNavigationOpen: actions.actionStub,
    goHome: actions.actionStub,
});

const mapDispatchToProps = (dispatch: actionTypes.AppThunkDispatch) => ({
    onInternalNavigationChange: (path: string) => dispatch(actions.onInternalNavigationChange(path, true)),
    setNavigationOpen: (isOpen: boolean) => dispatch(actions.setNavigationOpen(isOpen)),
    goHome: () => dispatch(actions.goHome()),
});

// tslint:disable-next-line:variable-name
export const ConnectedXtremNavigation = connect(mapStateToProps, mapDispatchToProps)(XtremNavigation);
