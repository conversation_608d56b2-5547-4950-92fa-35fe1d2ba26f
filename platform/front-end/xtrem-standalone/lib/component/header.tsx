import * as tokens from '@sage/design-tokens/js/base/common';
import type { Dict, Notification } from '@sage/xtrem-shared';
import type { integration } from '@sage/xtrem-ui';
import Button from 'carbon-react/esm/components/button';
import Icon from 'carbon-react/esm/components/icon';
import Link from 'carbon-react/esm/components/link';
import Tooltip from 'carbon-react/esm/components/tooltip';
import Pill from 'carbon-react/esm/components/pill';
import * as React from 'react';
import { connect } from 'react-redux';
import type { UserDetails } from '../service/auth-service';
import { localize } from '../service/standalone-i18n-service';
import type { TenantDetails, TenantItem } from '../service/tenants-service';
import type * as actionTypes from '../standalone-redux/action-types';
import * as actions from '../standalone-redux/actions';
import type { XtremStandaloneState } from '../standalone-redux/state';
import './header.scss';
import { ConnectedNotificationCenter } from './notification/notification-center';
import { ConnectedNotificationPreviews } from './notification/notification-previews';
import { ProfileSettingsPanel } from './profile-settings';
import type { ChatbotConfiguration } from '../service/chatbot-service';
import { CHATBOT_SUPPORTED_PAGES } from '../service/chatbot-service';
import { getPendoApi } from '../service/telemetry-service';
import { ChatbotIcon } from './chatbot-icon';

export interface XtremHeaderProps extends XtremExternalHeaderProps {
    chatbotConfig: ChatbotConfiguration | null;
    chatbotEnabled: boolean;
    goHome: () => void;
    isExtraSmall: boolean;
    isNavigationOpen: boolean;
    isNotificationCenterOpen: boolean;
    loginService: string | null;
    menuItems: integration.Menu[];
    notifications: Notification[];
    path: string;
    productName: string | null;
    setNavigationOpen: (isOpen: boolean) => void;
    setNotificationCenterOpen: (isOpen: boolean) => void;
    tenantItem: TenantItem;
    translations: Dict<string>;
    user: UserDetails | null;
}

export interface XtremExternalHeaderProps {
    onUserChangeLocale: (locale: string | null) => void;
}

export function XtremHeader(props: XtremHeaderProps) {
    const profileStyles: React.CSSProperties = {};
    const hasPhoto = props.user && props.user.photo;
    const hasUnreadNotifications = props.notifications.some(n => !n.isRead);
    const selectableTenants = props.tenantItem.tenantList.reduce((value, tenant) => {
        if (!tenant.current) {
            value.push(tenant.tenantId);
        }
        return value;
    }, [] as string[]);

    const [isTenantsListContainerOpen, setIsTenantsListContainerOpen] = React.useState(false);
    const [focusedTenant, setFocusedTenant] = React.useState(-1);
    const [isProfileOpenedByKeyboard, setProfileOpenedByKeyboard] = React.useState(false);
    const toggleProfilePanelOpenByKeyboard = (): void => setProfileOpenedByKeyboard(!isProfileOpenedByKeyboard);

    const tenantsActionsRef = React.useRef<HTMLButtonElement>(null);
    const tenantsContentRef = React.useRef<HTMLDivElement>(null);
    const getTenantsAnchor = (tenantId: string): HTMLAnchorElement =>
        tenantsContentRef.current?.querySelector(`a[id="${tenantId}"]`) as HTMLAnchorElement;

    const onTenantsActionEvent = () => {
        setIsTenantsListContainerOpen(!isTenantsListContainerOpen);
        if (!isTenantsListContainerOpen) {
            setFocusedTenant(-1);
        }
    };

    const onTenantsActionKeyDown = <ElementT extends Element>(e: React.KeyboardEvent<ElementT>) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onTenantsActionEvent();
        }

        if (e.key === 'ArrowDown') {
            getTenantsAnchor(selectableTenants[0])?.focus();
            setFocusedTenant(0);
        }
    };

    const onTenantsContentKeyDown = <ElementT extends Element>(e: React.KeyboardEvent<ElementT>) => {
        e.stopPropagation();
        if (e.key === ' ') {
            getTenantsAnchor(selectableTenants[focusedTenant]).click();
        }

        if (e.key === 'Escape') {
            onTenantsActionEvent();
            tenantsActionsRef.current?.focus();
        }

        if (e.key === 'ArrowDown' && focusedTenant < selectableTenants.length - 1) {
            getTenantsAnchor(selectableTenants[focusedTenant + 1]).focus();
            setFocusedTenant(focusedTenant + 1);
        }

        if (e.key === 'ArrowUp' && focusedTenant > 0) {
            getTenantsAnchor(selectableTenants[focusedTenant - 1]).focus();
            setFocusedTenant(focusedTenant - 1);
        }
    };

    if (hasPhoto) {
        profileStyles.backgroundImage = `url(data:image;base64,${props.user!.photo})`;
    }

    const goHome = (event: React.MouseEvent) => {
        event.preventDefault();
        props.setNavigationOpen(false);
        setProfileOpenedByKeyboard(false);
        props.goHome();
        getPendoApi()?.track('goHomeButtonClicked');
    };

    const currentTenant = props.tenantItem.tenantList.find(tenant => tenant.current);
    const hasTenants = props.tenantItem.tenantList?.length > 0 && !props.user?.isOperator;
    const canSwitchTenant = props.tenantItem.tenantList?.length > 1;
    const logoContainerClasses = ['xe-logo-container'];
    if (props.isExtraSmall) {
        logoContainerClasses.push('xe-logo-container-no-margin');
    }

    const profileClassNames = ['xe-profile-action', 'xe-profile-profile'];
    if (isProfileOpenedByKeyboard) {
        profileClassNames.push('xe-opened');
    }

    const onSkipLink = (event: React.MouseEvent | React.KeyboardEvent): void => {
        event.preventDefault();
        const pageBodyElements = Array.from(document.querySelectorAll('main'));

        if (pageBodyElements.length > 1) {
            const focusableElements = Array.from(
                pageBodyElements[pageBodyElements.length - 1].querySelectorAll(
                    'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])',
                ),
            ).filter(
                el =>
                    !el.hasAttribute('disabled') &&
                    !el.getAttribute('aria-hidden') &&
                    !el.classList.contains('e-navigation-panel-toggle'),
            );

            const firstFocusableElement = focusableElements[0] as HTMLButtonElement;

            firstFocusableElement.focus();
        } else {
            const focusableElements = Array.from(
                pageBodyElements[0].querySelectorAll(
                    'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])',
                ),
            ).filter(
                el =>
                    !el.hasAttribute('disabled') &&
                    !el.getAttribute('aria-hidden') &&
                    !el.classList.contains('e-navigation-panel-toggle'),
            );

            const firstFocusableElement = focusableElements[0] as HTMLButtonElement;

            firstFocusableElement.focus();
        }
    };

    const openChatbot = React.useCallback(() => {
        window.GmsChatUi?.setDisplayState('popup-right');
    }, []);

    const getTenantHref = (tenant: TenantDetails): string => {
        return tenant.current
            ? ''
            : `${props.loginService}${tenant.directLoginUrl.substring(tenant.directLoginUrl.indexOf('/'))}`;
    };

    const profileAriaLabelText = isProfileOpenedByKeyboard
        ? localize(
              '@sage/xtrem-standalone/profile-settings-close',
              'Close profile settings',
              {},
              props.translations,
              props.user?.locale ?? undefined,
          )
        : localize(
              '@sage/xtrem-standalone/profile-settings-open',
              'Open profile settings',
              {},
              props.translations,
              props.user?.locale ?? undefined,
          );

    React.useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                toggleProfilePanelOpenByKeyboard();
            }
        };

        if (isProfileOpenedByKeyboard) {
            document.addEventListener('keydown', handleKeyDown);
        }

        return () => {
            if (isProfileOpenedByKeyboard) {
                document.removeEventListener('keydown', handleKeyDown);
            }
        };
    }, [isProfileOpenedByKeyboard, toggleProfilePanelOpenByKeyboard]);

    const isChatbotButtonHidden = !CHATBOT_SUPPORTED_PAGES.find(
        p => props.path === p || props.path.startsWith(`${p}/`),
    );

    const getTenantPill = (kind?: string) => {
        if (!kind) return null;
        switch (kind) {
            case 'production':
                return (
                    <Pill isDarkBackground={true} borderColor={tokens.colorsSemanticPositive500} size="S">
                        {kind}
                    </Pill>
                );
            case 'demo':
                return (
                    <Pill isDarkBackground={true} borderColor={tokens.colorsSemanticInfo500} size="S">
                        {kind}
                    </Pill>
                );
            default:
                return null;
        }
    };

    return (
        <header className="xe-header">
            <div className="xe-skip-link" data-testid="xe-skip-link">
                {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
                <Link isSkipLink={true} onClick={onSkipLink} href="#" />
            </div>
            {props.isExtraSmall && (
                <Button
                    buttonType="tertiary"
                    ml={2}
                    iconType={props.isNavigationOpen ? 'close' : 'list_view'}
                    onClick={() => props.setNavigationOpen(!props.isNavigationOpen)}
                />
            )}
            <a href="/" className={logoContainerClasses.join(' ')} onClick={goHome}>
                <img className="xe-text-sage-logo" src="/images/sage-logo.svg" alt="Sage" />
                {!props.isExtraSmall && <span className="xe-text-logo-main-name">{props.productName}</span>}
            </a>
            <ul className="xe-actions">
                {hasTenants && (
                    <li
                        className={`${canSwitchTenant ? 'xe-tenants-action' : 'xe-tenants-no-action'}`}
                        data-testid={`${canSwitchTenant ? 'xe-tenants-action' : 'xe-tenants-no-action'}`}
                    >
                        <button
                            className="xe-tenants-action-button"
                            disabled={!canSwitchTenant}
                            onClick={canSwitchTenant ? onTenantsActionEvent : undefined}
                            onKeyDown={canSwitchTenant ? onTenantsActionKeyDown : undefined}
                            ref={tenantsActionsRef}
                            type="button"
                        >
                            <div className="xe-tenants-current-tenant-wrapper">
                                {!props.isExtraSmall && (
                                    <>
                                        <span
                                            className="xe-tenants-current-tenant"
                                            data-testid="xe-tenants-current-tenant"
                                            title={currentTenant?.tenantName}
                                        >
                                            {currentTenant?.tenantName}
                                        </span>
                                        <span className="xe-tenants-current-pill">
                                            {getTenantPill(currentTenant?.kind)}
                                        </span>
                                    </>
                                )}
                                {canSwitchTenant && (
                                    <Icon
                                        color={tokens.colorsYang100}
                                        data-testid="xe-tenants-icon-dropdown"
                                        tabIndex={-1}
                                        type="dropdown"
                                    />
                                )}
                            </div>
                            {isTenantsListContainerOpen && (
                                <div className="xe-tenants-list-wrapper" data-testid="xe-tenants-list-wrapper">
                                    <div className="xe-tenants-list-container" data-testid="xe-tenants-list-container">
                                        <div
                                            className="xe-tenants-list-content"
                                            data-testid="xe-tenants-list-content"
                                            ref={tenantsContentRef}
                                            onKeyDown={onTenantsContentKeyDown}
                                        >
                                            <div>
                                                {(props.tenantItem.tenantList || []).map(tenant => (
                                                    <a
                                                        className="xe-tenant"
                                                        id={tenant.tenantId}
                                                        key={tenant.tenantId}
                                                        href={getTenantHref(tenant)}
                                                    >
                                                        <span className="xe-tenant-name">{tenant.tenantName}</span>
                                                        <span className="xe-tenant-kind">
                                                            {getTenantPill(tenant.kind)}
                                                        </span>
                                                        {tenant.current && (
                                                            <Icon
                                                                color={tokens.colorsYang100}
                                                                data-testid="xe-tenant-icon"
                                                                className="xe-tenant-icon"
                                                                tabIndex={-1}
                                                                type="tick"
                                                            />
                                                        )}
                                                    </a>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </button>
                    </li>
                )}
                {props.menuItems?.map(m => {
                    return (
                        <li key={m.id} className="xe-profile-action xe-profile-action-icon-button">
                            <button
                                aria-label={m.icon as any}
                                className="xe-profile-action-button xe-icon-button xe-icon-button-link"
                                data-testid={m.id}
                                onClick={e => {
                                    e.preventDefault();
                                    m.onClick();
                                }}
                                type="button"
                            >
                                <Icon
                                    type={m.icon as any}
                                    tooltipMessage={m.title}
                                    color={tokens.colorsYang100}
                                    tabIndex={-1}
                                />
                            </button>
                            {m.badgeContent && (
                                <span className="xe-icon-label">
                                    <Pill colorVariant="neutral" fill={true} pillRole="status" size="S">
                                        {String(m.badgeContent)}
                                    </Pill>
                                </span>
                            )}
                        </li>
                    );
                })}
                {props.chatbotEnabled && props.chatbotConfig && props.user?.locale && !isChatbotButtonHidden && (
                    <li className="xe-profile-action xe-ai-assistant-action" data-testid="xe-ai-assistant-action">
                        <button
                            disabled={isChatbotButtonHidden}
                            aria-label={localize(
                                '@sage/xtrem-standalone/sage-copilot',
                                'Sage Copilot',
                                {},
                                props.translations,
                                props.user.locale,
                            )}
                            className="xe-profile-action-button xe-icon-button xe-profile-action-icon-button"
                            onClick={openChatbot}
                            type="button"
                            data-testid="xe-ai-assistant-action-button"
                        >
                            <Tooltip
                                message={localize(
                                    '@sage/xtrem-standalone/sage-copilot',
                                    'Sage Copilot',
                                    {},
                                    props.translations,
                                    props.user.locale,
                                )}
                            >
                                <ChatbotIcon />
                            </Tooltip>
                        </button>
                    </li>
                )}
                {props.user?.locale ? (
                    <>
                        <li className="xe-profile-action xe-notification-action" data-testid="xe-notification-action">
                            <button
                                aria-label={localize(
                                    '@sage/xtrem-standalone/notification-menu',
                                    'Notifications',
                                    {},
                                    props.translations,
                                    props.user.locale,
                                )}
                                className="xe-profile-action-button xe-icon-button xe-profile-action-icon-button xe-icon-button-link-notification"
                                data-testid={
                                    hasUnreadNotifications ? 'xe-notification-unread' : 'xe-notification-normal'
                                }
                                onClick={ev => {
                                    ev.preventDefault();
                                    props.setNotificationCenterOpen(!props.isNotificationCenterOpen);
                                }}
                                type="button"
                            >
                                <Icon
                                    type="alert"
                                    color={tokens.colorsYang100}
                                    tabIndex={-1}
                                    tooltipMessage={localize(
                                        '@sage/xtrem-standalone/notification-menu',
                                        'Notifications',
                                        {},
                                        props.translations,
                                        props.user.locale,
                                    )}
                                />
                                {hasUnreadNotifications && <span className="xe-icon-button-link-notification-badge" />}
                            </button>
                            <ConnectedNotificationPreviews />
                            <ConnectedNotificationCenter open={props.isNotificationCenterOpen} />
                        </li>
                        <li className="xe-profile-action xe-profile-action-icon-button">
                            {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
                            <a
                                // Don't reuse this className as it uses by Pendo to show the help dialog
                                className="xe-icon-button xe-icon-button-link"
                                id="pendo-help"
                                href="#"
                                target="_blank"
                                onClick={ev => {
                                    ev.preventDefault();
                                    getPendoApi()?.track('helpButtonClicked');
                                }}
                                aria-label={localize(
                                    '@sage/xtrem-standalone/help-menu',
                                    'Help',
                                    {},
                                    props.translations,
                                    props.user.locale,
                                )}
                            >
                                <Icon
                                    color={tokens.colorsYang100}
                                    tabIndex={-1}
                                    tooltipMessage={localize(
                                        '@sage/xtrem-standalone/help-menu',
                                        'Help',
                                        {},
                                        props.translations,
                                        props.user.locale,
                                    )}
                                    type="help"
                                />
                            </a>
                        </li>
                    </>
                ) : null}
                <li className={profileClassNames.join(' ')}>
                    <div className="xe-profile-wrapper">
                        {hasPhoto ? (
                            <button
                                type="button"
                                className="xe-profile-picture"
                                style={profileStyles}
                                onClick={toggleProfilePanelOpenByKeyboard}
                                aria-label={profileAriaLabelText}
                            />
                        ) : (
                            <button
                                type="button"
                                className="xe-profile-picture xe-profile-picture-empty"
                                onClick={toggleProfilePanelOpenByKeyboard}
                                aria-label={profileAriaLabelText}
                            />
                        )}
                        <ProfileSettingsPanel
                            user={props.user}
                            translations={props.translations}
                            loginService={props.loginService}
                            localeOnChange={locale => props.onUserChangeLocale(locale)}
                            tenantItem={props.tenantItem}
                        />
                    </div>
                </li>
            </ul>
        </header>
    );
}

XtremHeader.displayName = 'XtremHeader';

const mapStateToProps = (state: XtremStandaloneState, ownProps: XtremExternalHeaderProps): XtremHeaderProps => ({
    chatbotConfig: state.chatbotConfig,
    chatbotEnabled: !!state.config?.chatbotBackendUrl,
    goHome: actions.actionStub,
    isExtraSmall: state.browser.is.xs,
    isNavigationOpen: state.isNavigationOpen,
    isNotificationCenterOpen: state.isNotificationCenterOpen,
    loginService: state.loginService,
    menuItems: state.menuItems.filter(m => m.category === 'sticker'),
    notifications: state.notifications,
    onUserChangeLocale: ownProps.onUserChangeLocale,
    path: state.path,
    productName: state.config?.productName || null,
    setNavigationOpen: actions.actionStub,
    setNotificationCenterOpen: actions.actionStub,
    tenantItem: state.tenantsList,
    translations: state.translations,
    user: state.user,
});

const mapDispatchToProps = (dispatch: actionTypes.AppThunkDispatch) => ({
    goHome: () => dispatch(actions.goHome()),
    setNavigationOpen: (isOpen: boolean) => dispatch(actions.setNavigationOpen(isOpen)),
    setNotificationCenterOpen: (isOpen: boolean) => dispatch(actions.setNotificationCenterOpen(isOpen)),
});

export const ConnectedXtremHeader = connect(mapStateToProps, mapDispatchToProps)(XtremHeader);
