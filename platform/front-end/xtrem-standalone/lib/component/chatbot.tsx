import type { XtremStandaloneState } from '../standalone-redux/state';
import * as React from 'react';
import { useSelector } from 'react-redux';
import type { ChatbotConfiguration } from '../service/chatbot-service';
import './chatbot.scss';
import { GmsChatWindow } from '@sageai/gms-chat-ui-react';
import type { UserDetails } from '../service/auth-service';
import type { integration } from '@sage/xtrem-ui';
import { date } from '@sage/xtrem-date-time';

export function Chatbot() {
    const userCurrentDate = React.useMemo(
        () => date.today(Intl.DateTimeFormat().resolvedOptions().timeZone).toString(),
        [],
    );
    const chatbotConfig = useSelector<XtremStandaloneState, ChatbotConfiguration | null>(s => s.chatbotConfig);

    const chatbotBackendUrl = useSelector<XtremStandaloneState, string | null>(
        s => s.config?.chatbotBackendUrl || null,
    );
    const chatbotGmsClient = useSelector<XtremStandaloneState, any>(s => s.config?.chatbotGmsClient ?? 'sdmo_v1');
    const app = useSelector<XtremStandaloneState, string>(s => s.config?.app ?? 'sdmo');
    const user = useSelector<XtremStandaloneState, UserDetails | null>(state => state.user);
    const pageContext = useSelector<XtremStandaloneState, integration.PageContext | null>(state => state.pageContext);

    if (!chatbotConfig || !chatbotBackendUrl || !user?.uuid || !user?.locale) {
        return null;
    }

    const authToken = chatbotConfig.aiToken?.token ?? '';

    const accessCode = chatbotConfig.accessCode?.code;

    return (
        <GmsChatWindow
            data-testid="gms-chat-window"
            backendUrl={chatbotBackendUrl}
            enableInsights={true}
            enableChat={false}
            authToken={authToken} // (string) the token that is used to authenticate the client to the backend service
            userId={user.uuid} // (UUID) a unique UUID to identify the user within the backend service
            gmsClient={chatbotGmsClient} // specifies which GMS client to communicate with: `base_v2`, `excel_v1`, `sdmo_v1`, `sip_v1`, `sna_v1`
            showSidebar={false} // ("true"|"false") specifies whether the sidebar is visible (default: "true")
            showSuggestions={false} // ("true"|"false") specifies whether the suggested questions are fetched and displayed (default: "true")
            showConversationStarter={true} // ("true"|"false") specifies whether the conversation starter is fetched and displayed (default: "true")
            fetchHistory={true} // ("true"|"false") specifies whether the chat history is fetched (default: "true")
            showHeading={true} // ("true"|"false") specifies whether the chat window heading is visible (default "true")
            roundedCorners={true} // ("true"|"false") specifies whether the chat window has rounded corners (default "true")
            initialDisplayState="hidden"
            disableShadowDom={true}
            chatContext={{
                tenantId: user?.tenantId ?? undefined, // (string) the tenant id of the user
                userLocale: user?.locale ?? undefined, // (string) the locale of the user
                userCurrentDate, // (string) the current date in the user's timezone
                nodeName: pageContext?.nodeName, // (string) the name of the current node
                recordFilter: pageContext?.recordFilter, // (string) the filter to get the current record
                screenId: pageContext?.screenId, // (string) the id of the current screen
                screenTitle: pageContext?.screenTitle, // (string) the title of the current screen
                app: app ?? undefined, // (string) the app name that is used to identify the current app
                apiAccessCode: accessCode ?? undefined, // (string) the access code that is used to authenticate the ai backend service
            }}
        />
    );
}
