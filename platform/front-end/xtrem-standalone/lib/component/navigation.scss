@import '../variables.scss';

.xe-navigation {
    height: 100vh;
    position: absolute;
    display: block;
    width: auto;
    top: 0;
    left: 0;
    z-index: 2000;
    box-shadow: var(--boxShadow300);

    &.xe-navigation-menu-open {
        width: 100vw;
        display: flex;

        .xe-navigation-primary .xe-navigation-menu-item.xe-navigation-is-active {
            box-shadow: none;
        }
    }

    &.xe-navigation-primary-closed {
        box-shadow: none;

        .xe-navigation-primary {
            width: 64px;

            .xe-navigation-menu-item-title {
                display: none;
            }

            .xe-navigation-toggle .xe-navigation-menu-item-icon {
                transform: rotate(180deg);
            }

            .xe-navigation-menu-item:hover {
                width: 246px;

                .xe-navigation-menu-item-title {
                    display: inline-block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }

        .xe-navigation-secondary .xe-navigation-secondary-body {
            height: calc(100% - 65px);
        }
    }

    .xe-navigation-backdrop {
        background: rgba(0, 20, 29, 0.6);
        height: 100vh;
        transition: all 0.2s ease;
        z-index: 2004;
        opacity: 0;
        display: none;

        &.xe-navigation-backdrop-open {
            width: auto;
            opacity: 1;
            display: inline-block;
            flex: 1;
        }
    }

    .xe-navigation-primary {
        background-color: var(--colorsYang100);
        display: flex;
        flex-direction: column;
        width: 246px;
        height: 100vh;

        transition-duration: 0.2s;
        transition-property: width;

        .xe-navigation-toggle .xe-navigation-menu-item:hover {
            background-color: var(--colorsActionMajor600);
            border-color: var(--colorsActionMajor600);
            color: var(--colorsYang100);

            span::before,
            .xe-navigation-menu-item-icon {
                color: var(--colorsYang100);
            }
        }

        .xe-navigation-menu {
            padding-top: 48px;
            flex: 1;
        }

        .xe-navigation-menu-item {
            line-height: 64px;
            height: 64px;
            padding-right: 14px;
            font-size: 16px;
            background-color: var(--colorsYang100);
            color: var(--colorsYin090);
            display: flex;
            overflow: hidden;
            position: relative;
            text-decoration: none;
            text-overflow: ellipsis;
            transition: width 0.2s;
            border: none;

            &:focus {
                margin: 0;
                box-shadow: inset 0px 0px 0px 3px var(--colorsSemanticFocus500);
                outline: none;
            }

            span::before {
                color: var(--colorsYin090);
            }

            &.xe-navigation-menu-item-selected {
                background-color: var(--colorsComponentsMenuSpringStandard500);
                color: var(--colorsYin090);

                span::before {
                    color: var(--colorsYin090);
                }
            }

            &.xe-navigation-is-active {
                background-color: var(--colorsComponentsMenuSpringStandard500);
                box-shadow: inset 0px 0px 0px 1px var(--colorsComponentsMenuSpringChild400);
                color: var(--colorsYin090);

                span::before {
                    color: var(--colorsYin090);
                }

                &:focus {
                    box-shadow: inset 0px 0px 0px 3px var(--colorsSemanticFocus500);
                }

                &:hover {
                    background-color: var(--colorsActionMajor500);
                    color: var(--colorsYang100);

                    span::before {
                        color: var(--colorsYang100);
                    }
                }
            }

            &:hover {
                background-color: var(--colorsActionMajor500);
                border-color: var(--colorsActionMajor500);
                color: var(--colorsYang100);

                span::before,
                .xe-navigation-menu-item-icon {
                    color: var(--colorsYang100);
                }
            }
        }

        .xe-navigation-menu-item-title {
            font-family: var(--fontFamiliesDefault);
            font-weight: var(--fontWeights500);
            font-size: var(--fontSizes200);
        }

        .xe-navigation-menu-item-icon {
            font-family: $fontCarbonIcons;
            display: inline-block;
            margin-right: 14px;
            text-align: center;
            width: 64px;
            transition-duration: 0.2s;
            transition-property: transform;
            color: var(--colorsYin090);

            span::before {
                color: var(--colorsYin090);
            }
        }
    }

    .xe-navigation-secondary {
        width: 265px;
        padding-top: 48px;
        height: 100vh;
        background-color: var(--colorsComponentsMenuSpringStandard500);
        left: 246px;
        transition: left 0.2s;
        display: inline-block;
        flex-direction: column;
        z-index: 2004;
        box-sizing: border-box;

        .xe-navigation-secondary-title {
            background-color: var(--colorsActionMinor500);
            box-shadow: 0 2px 4px 0 rgba(0, 20, 29, 0.15), 0 3px 3px 0 rgba(0, 20, 29, 0.2);
            color: var(--colorsYang100);
            padding: 16px 0;
            white-space: normal;
            width: 100%;
            z-index: 2001;
            font-family: var(--fontFamiliesDefault);
            font-size: 16px;

            .xe-navigation-secondary-title-label {
                line-height: 25px;
                margin: 0 20px;
                font-family: var(--fontFamiliesDefault);
                font-weight: var(--fontWeights500);
            }
        }

        .xe-navigation-secondary-body {
            overflow-y: auto;
            flex: 1;
            height: 100%;
        }

        .xe-navigation-secondary-item {
            display: flex;
            margin: 16px 0 16px 16px;
            font-size: 16px;
            font-family: var(--fontFamiliesDefault);

            .xe-navigation-secondary-item-link {
                text-decoration: none;
                color: #000;
            }
        }
    }
}

.xe-navigation.xe-navigation-primary-closed .xe-navigation-toggle .xe-navigation-menu-item-icon {
    margin-right: 0;
}

.xe-navigation.xe-navigation-primary-closed .xe-navigation-toggle .xe-navigation-menu-item {
    padding-right: 0;
}

.xe-navigation-sub-menu {
    text-decoration: none;
    list-style: none;
    background: transparent;

    &.xe-navigation-sub-menu-selected {
        box-shadow: inset 0px 0px 0px 1px var(--colorsComponentsMenuSpringChild400);
    }
}

.xe-navigation-sub-menu-body,
.xe-navigation-secondary-body-list {
    padding: 0;
    margin: 0;
}

.xe-navigation-sub-menu-title-line {
    display: flex;

    .xe-navigation-sub-menu-title {
        flex: 1;
        text-decoration: none;
        color: #000;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;

        .xe-navigation-sub-menu-title-label {
            font-family: var(--fontFamiliesDefault);
            font-weight: var(--fontWeights500);
            font-size: var(--fontSizes100);
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            line-height: 34px;
        }
    }

    &>span {
        height: 34px;
        line-height: 34px;
    }
}

.xe-navigation-sub-menu-level-1 {
    color: var(--colorsYin090);

    .xe-navigation-sub-menu-title-line:hover {
        background-color: var(--colorsActionMajor500);
        color: var(--colorsYang100);
        cursor: pointer;

        span::before,
        .xe-navigation-sub-menu-title {
            color: var(--colorsYang100);
        }
    }

    padding-left: 0;

    >.xe-navigation-sub-menu-title-line-open>.xe-navigation-sub-menu-title {
        font-size: 16px;
    }

    .xe-navigation-sub-menu-title {
        padding-left: 13px;
        padding-top: 5px;
        padding-bottom: 5px;
        padding-right: 5px;
        margin-right: 3px;
        margin-left: 3px;
        margin-top: 3px;
        margin-bottom: 3px;

        &:focus {
            margin-left: 0;
            margin-top: 0;
            margin-bottom: 0;
            margin-right: 0;
            border: 3px solid var(--colorsSemanticFocus500);
            outline: none;
        }
    }
}

.xe-navigation-sub-menu-level-2 {
    .xe-navigation-sub-menu-title {
        padding-left: 29px;
        font-size: 14px;

        >span {
            height: 34px;
            line-height: 34px;
            margin-top: 0;
            margin-bottom: 0;
        }
    }

    >.xe-navigation-sub-menu-title-line>.xe-navigation-sub-menu-title,
    >.xe-navigation-sub-menu-title-line>span {
        height: 34px;
        line-height: 34px;
    }
}

.xe-navigation-sub-menu-level-3 {
    position: relative;
    min-height: 50px;

    &::after {
        display: block;
        width: 4px;
        height: 4px;
        content: ' ';
        border-radius: 2px;
        position: absolute;
        left: 32px;
        top: 26px;
        background-color: var(--colorsYin090);
    }

    &:hover::after {
        background-color: var(--colorsYang100);
    }

    .xe-navigation-sub-menu-title {
        padding-left: 45px;
        font-size: 14px;

        >span {
            height: 34px;
            line-height: 34px;
        }
    }

    >.xe-navigation-sub-menu-title-line>.xe-navigation-sub-menu-title,
    >.xe-navigation-sub-menu-title-line>span {
        padding-top: 8px;
        padding-bottom: 8px;
        height: 34px;
        line-height: 34px;
    }
}

.xe-navigation-sub-menu-level-4 {
    font-size: 14px;

    .xe-navigation-sub-menu-title {
        padding-left: 54px;

        .xe-navigation-sub-menu-title {
            padding-top: 8px;
            padding-bottom: 8px;
        }
    }
}