// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MobileNavigation should render as expected 1`] = `
<nav
  aria-label="Navigation menu"
  class="xe-navigation-mobile"
  data-testid="xe-navigation-menu"
>
  <ul
    class="xe-navigation-secondary-body-list"
  >
    <li
      class="xe-navigation-sub-menu xe-navigation-sub-menu-level-1 xe-navigation-sub-menu-page"
      data-testid="xe-navigation-sub-menu-1"
    >
      <div
        class="xe-navigation-sub-menu-title-line"
      >
        <a
          class="xe-navigation-sub-menu-title xe-navigation-sub-menu-1"
          data-menu-item="secondary-menu-item-1"
          data-testid="xe-navigation-sub-menu-link-1"
          href="http://localhost/1"
        >
          <span
            class="xe-navigation-sub-menu-title-label"
          >
            Test Page #1
          </span>
        </a>
      </div>
    </li>
    <li
      class="xe-navigation-sub-menu xe-navigation-sub-menu-level-1 xe-navigation-sub-menu-page"
      data-testid="xe-navigation-sub-menu-2"
    >
      <div
        class="xe-navigation-sub-menu-title-line"
      >
        <a
          class="xe-navigation-sub-menu-title xe-navigation-sub-menu-2"
          data-menu-item="secondary-menu-item-2"
          data-testid="xe-navigation-sub-menu-link-2"
          href="http://localhost/2"
        >
          <span
            class="xe-navigation-sub-menu-title-label"
          >
            Test Page #2
          </span>
        </a>
      </div>
    </li>
    <li
      class="xe-navigation-sub-menu xe-navigation-sub-menu-level-1 xe-navigation-sub-menu-page"
      data-testid="xe-navigation-sub-menu-3"
    >
      <div
        class="xe-navigation-sub-menu-title-line"
      >
        <a
          class="xe-navigation-sub-menu-title xe-navigation-sub-menu-3"
          data-menu-item="secondary-menu-item-3"
          data-testid="xe-navigation-sub-menu-link-3"
          href="http://localhost/3"
        >
          <span
            class="xe-navigation-sub-menu-title-label"
          >
            Test Page #3
          </span>
        </a>
      </div>
    </li>
  </ul>
</nav>
`;
