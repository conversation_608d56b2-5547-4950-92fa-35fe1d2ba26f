// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`XtremNavigation should render as expected 1`] = `
<nav
  aria-label="Navigation menu"
  class="xe-navigation xe-navigation-primary-closed"
  data-testid="xe-navigation-menu"
>
  <div
    class="xe-navigation-primary xe-skip-link"
  >
    <div
      class="xe-navigation-menu"
    >
      <a
        aria-label="Home"
        class="xe-navigation-menu-item"
        data-testid="xe-navigation-home"
        href="#"
      >
        <span
          class="xe-navigation-menu-item-icon"
        >
          <span
            class="sc-iGgWBj jBJWUf"
            color="#FFF"
            data-component="icon"
            data-element="home"
            data-role="icon"
            font-size="small"
            type="home"
          />
        </span>
        <span
          class="xe-navigation-menu-item-title"
        >
          Home
        </span>
      </a>
      <a
        aria-label="Pages"
        class="xe-navigation-menu-item xe-navigation-menu-item-pages"
        data-menu-item="primary-menu-item-pages"
        data-testid="xe-navigation-primary-menu-item-pages"
        href="#"
      >
        <span
          class="xe-navigation-menu-item-icon"
        >
          <span
            class="sc-iGgWBj gaXKbF"
            color="#FFF"
            data-component="icon"
            data-element="folder"
            data-role="icon"
            font-size="small"
            type="folder"
          />
        </span>
        <span
          class="xe-navigation-menu-item-title"
        >
          Pages
        </span>
      </a>
    </div>
    <div
      class="xe-navigation-toggle"
    >
      <a
        class="xe-navigation-menu-item"
        data-testid="xe-navigation-toggle"
        href="#"
      >
        <span
          class="xe-navigation-menu-item-icon"
        >
          
        </span>
        <span
          class="xe-navigation-menu-item-title"
        >
          Maximize
        </span>
      </a>
    </div>
  </div>
</nav>
`;
