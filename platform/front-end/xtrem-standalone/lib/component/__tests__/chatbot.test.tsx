import * as React from 'react';
const chatbotComponentMock = jest.fn();

jest.mock('@sageai/gms-chat-ui-react', () => ({
    GmsChatWindow: (props: any) => {
        chatbotComponentMock(props);
        return <div data-testid="gms-chat-window">GmsChatWindow</div>;
    },
}));

jest.mock('@sage/xtrem-ui', () => ({
    integration: {
        XtremUiIndex: () => <div data-testid="xtrem-ui">Xtrem-UI placeholder</div>,
        DashboardRootComponent: () => <div data-testid="bms-dashboard">BMS dashboard placeholder</div>,
    },
}));

import configureMockStore from 'redux-mock-store';
import { render } from '@testing-library/react';
import { Chatbot } from '../chatbot';
import { Provider } from 'react-redux';
import { XtremStandaloneState } from '../../standalone-redux/state';
import thunk from 'redux-thunk';
import * as actions from '../../standalone-redux/actions';

describe('chatbot', () => {
    let mockState: XtremStandaloneState;

    const getMockStore = () => {
        const mockStoreCreator = configureMockStore<XtremStandaloneState>([thunk]);
        return mockStoreCreator(() => mockState);
    };

    beforeEach(() => {
        chatbotComponentMock.mockReset();
        jest.spyOn(actions, 'setChatbotOpen');

        mockState = {
            pageContext: null,
            config: {
                agGridLicenceKey: 'licenceKey',
                chatbotBackendUrl: 'http://chatbot.end.point',
                productName: 'Sage Distribution and Manufacturing Operations',
            },
            chatbotConfig: {
                aiToken: { token: 'someToken', expiration: ********** },
                accessCode: {
                    code: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30',
                    expiration: **********,
                },
            },
            isApplicationDirty: false,
            menuItems: [],
            sitemap: [],
            isChatbotOpen: true,
            isNavigationOpen: false,
            isNotificationCenterOpen: false,
            notifications: [],
            pageTitle: 'title',
            preNavigationConfirmation: jest.fn(),
            tenantsList: {} as any,
            browser: {} as any,
            loginService: 'http://login.service.com',
            path: '',
            user: {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                id: '<EMAIL>',
                userCode: 'john.doe',
                locale: 'en-US',
                tenantId: 'tenant1',
                pref: 5,
                uniqueTenantId: 'teant1id',
                uniqueUserId: 'johnDoeId',
                clientEncryptionKey: 'test',
                uuid: '**********',
                photo: null,
                isOperator: false,
            },
            translations: {},
        };
    });

    it('should render the chatbot with the right properties', () => {
        expect(chatbotComponentMock).not.toHaveBeenCalled();
        const { queryByTestId } = render(
            <Provider store={getMockStore()}>
                <Chatbot />
            </Provider>,
        );

        expect(queryByTestId('gms-chat-window')!).not.toBeNull();
        expect(chatbotComponentMock).toHaveBeenCalledWith({
            authToken: 'someToken',
            backendUrl: 'http://chatbot.end.point',
            chatContext: {
                nodeName: undefined,
                recordFilter: undefined,
                screenId: undefined,
                screenTitle: undefined,
                tenantId: 'tenant1',
                userCurrentDate: expect.any(String),
                userLocale: 'en-US',
                apiAccessCode:
                    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30',
                app: 'sdmo',
            },
            'data-testid': 'gms-chat-window',
            disableShadowDom: true,
            enableChat: false,
            enableInsights: true,
            fetchHistory: true,
            gmsClient: 'sdmo_v1',
            initialDisplayState: 'hidden',
            roundedCorners: true,
            showConversationStarter: true,
            showHeading: true,
            showSidebar: false,
            showSuggestions: false,
            userId: '**********',
        });
    });

    it('should not render the chatbot if the chatbot config is missing', () => {
        mockState.chatbotConfig = null;
        expect(chatbotComponentMock).not.toHaveBeenCalled();
        const { queryByTestId } = render(
            <Provider store={getMockStore()}>
                <Chatbot />
            </Provider>,
        );

        expect(queryByTestId('gms-chat-window')!).toBeNull();
        expect(chatbotComponentMock).not.toHaveBeenCalled();
    });

    it('should not render the chatbot if the config is missing', () => {
        mockState.config = null;
        expect(chatbotComponentMock).not.toHaveBeenCalled();
        const { queryByTestId } = render(
            <Provider store={getMockStore()}>
                <Chatbot />
            </Provider>,
        );

        expect(queryByTestId('gms-chat-window')!).toBeNull();
        expect(chatbotComponentMock).not.toHaveBeenCalled();
    });

    it('should not render the chatbot if the configs backendUrl is missing', () => {
        (mockState.config as any).chatbotBackendUrl = null;
        expect(chatbotComponentMock).not.toHaveBeenCalled();
        const { queryByTestId } = render(
            <Provider store={getMockStore()}>
                <Chatbot />
            </Provider>,
        );

        expect(queryByTestId('gms-chat-window')!).toBeNull();
        expect(chatbotComponentMock).not.toHaveBeenCalled();
    });

    it('should not render the chatbot if the user info is missing', () => {
        mockState.user = null;
        expect(chatbotComponentMock).not.toHaveBeenCalled();
        const { queryByTestId } = render(
            <Provider store={getMockStore()}>
                <Chatbot />
            </Provider>,
        );

        expect(queryByTestId('gms-chat-window')!).toBeNull();
        expect(chatbotComponentMock).not.toHaveBeenCalled();
    });

    it('should not render the chatbot if the user uuid is not defined', () => {
        (mockState.user as any).uuid = undefined;

        expect(chatbotComponentMock).not.toHaveBeenCalled();
        const { queryByTestId } = render(
            <Provider store={getMockStore()}>
                <Chatbot />
            </Provider>,
        );

        expect(queryByTestId('gms-chat-window')!).toBeNull();
        expect(chatbotComponentMock).not.toHaveBeenCalled();
    });

    it('should not render the chatbot if the user locale is not defined', () => {
        (mockState.user as any).locale = undefined;

        expect(chatbotComponentMock).not.toHaveBeenCalled();
        const { queryByTestId } = render(
            <Provider store={getMockStore()}>
                <Chatbot />
            </Provider>,
        );

        expect(queryByTestId('gms-chat-window')!).toBeNull();
        expect(chatbotComponentMock).not.toHaveBeenCalled();
    });

    it('should not render the chatbot if the chat backend url is missing', () => {
        (mockState.config as any).chatbotBackendUrl = undefined;
        expect(chatbotComponentMock).not.toHaveBeenCalled();
        const { queryByTestId } = render(
            <Provider store={getMockStore()}>
                <Chatbot />
            </Provider>,
        );

        expect(queryByTestId('gms-chat-window')!).toBeNull();
        expect(chatbotComponentMock).not.toHaveBeenCalled();
    });
});
