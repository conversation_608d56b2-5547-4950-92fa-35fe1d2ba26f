import { render, waitFor } from '@testing-library/react';
import * as React from 'react';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { XtremStandaloneState } from '../../standalone-redux/state';
import { ConnectedMobileNavigation, MobileNavigation, type XtremNavigationProps } from '../mobile-navigation';

jest.mock('@sage/xtrem-ui', () => ({
    integration: {
        XtremUiIndex: () => <div data-testid="xtrem-ui">Xtrem-UI placeholder</div>,
        DashboardRootComponent: () => <div data-testid="bms-dashboard">BMS dashboard placeholder</div>,
        breakpoints: {},
    },
}));

const mockProps: XtremNavigationProps = {
    isExtraSmall: true,
    isNavigationOpen: true,
    onInternalNavigationChange: jest.fn(),
    path: '@sage/xtrem-standalone-test/TestPage',
    setNavigationOpen: jest.fn(),
    sitemap: [
        {
            icon: 'print',
            id: '1',
            isPage: true,
            packageName: '@sage/xtrem-standalone-test',
            priority: 100,
            title: 'Test Page #1',
        },
        {
            icon: 'print',
            id: '2',
            isPage: true,
            packageName: '@sage/xtrem-standalone-test',
            priority: 100,
            title: 'Test Page #2',
        },
        {
            icon: 'print',
            id: '3',
            isPage: true,
            packageName: '@sage/xtrem-standalone-test',
            priority: 100,
            title: 'Test Page #3',
        },
    ],
};

describe('MobileNavigation', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    it('should not render if isNavigationOpen is false', () => {
        const props: XtremNavigationProps = { ...mockProps, isNavigationOpen: false };
        const { queryByTestId } = render(<MobileNavigation {...props} />);
        expect(queryByTestId('xe-navigation-menu')).toBeNull();
    });

    it('should not render if isExtraSmall is false', () => {
        const props: XtremNavigationProps = { ...mockProps, isExtraSmall: false };
        const { queryByTestId } = render(<MobileNavigation {...props} />);
        expect(queryByTestId('xe-navigation-menu')).toBeNull();
    });

    it('should render as expected', () => {
        const props: XtremNavigationProps = { ...mockProps };
        const { getByTestId } = render(<MobileNavigation {...props} />);
        expect(getByTestId('xe-navigation-menu')).toBeInTheDocument();
        expect(getByTestId('xe-navigation-menu')).toMatchSnapshot();
    });

    it('should close navigation when selecting a link', () => {
        const props: XtremNavigationProps = { ...mockProps };
        const { getByTestId } = render(<MobileNavigation {...props} />);
        const link = getByTestId('xe-navigation-sub-menu-link-1');
        link.click();
        expect(props.setNavigationOpen).toHaveBeenCalledWith(false);
    });

    it('should call onInternalNavigationChange with path', () => {
        const props: XtremNavigationProps = { ...mockProps };
        const { getByTestId } = render(<MobileNavigation {...props} />);
        const link = getByTestId('xe-navigation-sub-menu-link-1');
        link.click();
        expect(props.onInternalNavigationChange).toHaveBeenCalledWith('1');
    });

    describe('ConnectedMobileNavigation', () => {
        let mockState: XtremStandaloneState = {
            pageContext: null,
            browser: { is: { xs: true } } as any,
            config: {
                agGridLicenceKey: 'licenceKey',
                chatbotBackendUrl: 'http://chatbot.end.point',
                productName: 'Sage Distribution and Manufacturing Operations',
            },
            chatbotConfig: {
                aiToken: 'someToken',
                accessCode:
                    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30',
                accessCodeExpiration: 1516239022,
            },
            isApplicationDirty: false,
            menuItems: [],
            sitemap: mockProps.sitemap,
            isChatbotOpen: true,
            isNavigationOpen: true,
            isNotificationCenterOpen: false,
            notifications: [],
            pageTitle: 'title',
            preNavigationConfirmation: jest.fn(),
            tenantsList: {} as any,
            loginService: 'http://login.service.com',
            path: mockProps.path,
            user: {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                id: '<EMAIL>',
                userCode: 'john.doe',
                locale: 'en-US',
                tenantId: 'tenant1',
                pref: 5,
                uniqueTenantId: 'teant1id',
                uniqueUserId: 'johnDoeId',
                clientEncryptionKey: 'test',
                uuid: '**********',
                photo: null,
                isOperator: false,
            },
            translations: {},
        };

        const getMockStore = () => {
            const mockStoreCreator = configureMockStore<XtremStandaloneState>([thunk]);
            return mockStoreCreator(() => mockState);
        };

        it('should render connected component as expected', async () => {
            const store = getMockStore();
            const { queryByTestId } = render(
                <Provider store={store}>
                    <ConnectedMobileNavigation />
                </Provider>,
            );
            await waitFor(() => {
                const menu = queryByTestId('xe-navigation-menu');
                expect(menu).toBeInTheDocument();
            });
        });
    });
});
