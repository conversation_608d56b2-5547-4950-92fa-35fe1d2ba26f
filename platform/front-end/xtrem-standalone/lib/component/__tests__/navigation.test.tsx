import { render, waitFor } from '@testing-library/react';
import * as React from 'react';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import type { XtremStandaloneState } from '../../standalone-redux/state';
import { ConnectedXtremNavigation, XtremNavigation, type XtremNavigationProps } from '../navigation';

jest.mock('@sage/xtrem-ui', () => ({
    integration: {
        XtremUiIndex: () => <div data-testid="xtrem-ui">Xtrem-UI placeholder</div>,
        DashboardRootComponent: () => <div data-testid="bms-dashboard">BMS dashboard placeholder</div>,
        breakpoints: {},
    },
}));

const mockState: XtremStandaloneState = {
    browser: { is: { xs: false } } as any,
    pageContext: null,
    config: {
        agGridLicenceKey: 'licenceKey',
        chatbotBackendUrl: 'http://chatbot.end.point',
        productName: 'Sage Distribution and Manufacturing Operations',
    },
    chatbotConfig: {
        aiToken: 'someToken',
        accessCode:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30',
        accessCodeExpiration: 1516239022,
    },
    isApplicationDirty: false,
    menuItems: [],
    sitemap: [
        {
            icon: 'folder',
            id: 'pages',
            packageName: '@sage/xtrem-standalone-test',
            priority: 100,
            title: 'Pages',
            children: [
                {
                    icon: 'print',
                    id: '1',
                    isPage: true,
                    packageName: '@sage/xtrem-standalone-test',
                    priority: 100,
                    title: 'Test Page #1',
                },
                {
                    icon: 'print',
                    id: '2',
                    isPage: true,
                    packageName: '@sage/xtrem-standalone-test',
                    priority: 100,
                    title: 'Test Page #2',
                },
                {
                    icon: 'print',
                    id: '3',
                    isPage: true,
                    packageName: '@sage/xtrem-standalone-test',
                    priority: 100,
                    title: 'Test Page #3',
                },
            ],
        },
    ] as any,
    isChatbotOpen: false,
    isNavigationOpen: false,
    isNotificationCenterOpen: false,
    notifications: [],
    pageTitle: 'title',
    preNavigationConfirmation: jest.fn(),
    tenantsList: {} as any,
    loginService: 'http://login.service.com',
    path: '@sage/xtrem-standalone-test/TestPage',
    user: {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        id: '<EMAIL>',
        userCode: 'john.doe',
        locale: 'en-US',
        tenantId: 'tenant1',
        pref: 5,
        uniqueTenantId: 'teant1id',
        uniqueUserId: 'johnDoeId',
        clientEncryptionKey: 'test',
        uuid: '1234567890',
        photo: null,
        isOperator: false,
    },
    translations: {},
};

const mockProps: XtremNavigationProps = {
    goHome: jest.fn(),
    isNavigationOpen: mockState.isNavigationOpen,
    onInternalNavigationChange: jest.fn(),
    path: mockState.path,
    setNavigationOpen: jest.fn(),
    sitemap: mockState.sitemap,
    translations: {},
    user: mockState.user,
};

describe('XtremNavigation', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    it('should render as expected', () => {
        const props: XtremNavigationProps = { ...mockProps };
        const { queryByTestId } = render(<XtremNavigation {...props} />);
        expect(queryByTestId('xe-navigation-menu')).toBeInTheDocument();
        expect(queryByTestId('xe-navigation-menu')).toMatchSnapshot();
    });

    it('should close navigation panel when home link is clicked', async () => {
        const props: XtremNavigationProps = { ...mockProps };
        const { getByTestId } = render(<XtremNavigation {...props} />);
        const link = getByTestId('xe-navigation-home');
        link.click();
        await waitFor(() => {
            expect(props.setNavigationOpen).toHaveBeenCalledWith(false);
        });
    });

    it('should call goHome when home link is clicked', async () => {
        const props: XtremNavigationProps = { ...mockProps };
        const { getByTestId } = render(<XtremNavigation {...props} />);
        const link = getByTestId('xe-navigation-home');
        link.click();
        await waitFor(() => {
            expect(props.goHome).toHaveBeenCalled();
        });
    });

    it('should call setNavigationOpen root menu is opened', async () => {
        const props: XtremNavigationProps = { ...mockProps };
        const { getByTestId } = render(<XtremNavigation {...props} />);
        const menu = getByTestId('xe-navigation-primary-menu-item-pages');
        menu.click();
        await waitFor(() => {
            expect(props.setNavigationOpen).toHaveBeenCalledWith(true);
        });
    });

    it('should close navigation panel when site link is clicked', async () => {
        const props: XtremNavigationProps = { ...mockProps };
        const { getByTestId } = render(<XtremNavigation {...props} />);
        const menu = getByTestId('xe-navigation-primary-menu-item-pages');
        menu.click();

        await waitFor(() => {
            const link = getByTestId('xe-navigation-sub-menu-link-1');
            link.click();
            expect(props.setNavigationOpen).toHaveBeenCalledWith(false);
        });
    });

    it('should call onInternalNavigationChange when site link is clicked', async () => {
        const props: XtremNavigationProps = { ...mockProps };
        const { getByTestId } = render(<XtremNavigation {...props} />);
        const menu = getByTestId('xe-navigation-primary-menu-item-pages');
        menu.click();

        await waitFor(() => {
            expect(props.setNavigationOpen).toHaveBeenCalledWith(true);
            const link = getByTestId('xe-navigation-sub-menu-link-1');
            link.click();
        });

        await waitFor(() => {
            expect(props.onInternalNavigationChange).toHaveBeenCalledWith('1');
        });
    });

    describe('ConnectedXtremNavigation', () => {
        const getMockStore = () => {
            const mockStoreCreator = configureMockStore<XtremStandaloneState>([thunk]);
            return mockStoreCreator(() => mockState);
        };

        it('should render connected component as expected', async () => {
            const store = getMockStore();
            const { queryByTestId } = render(
                <Provider store={store}>
                    <ConnectedXtremNavigation />
                </Provider>,
            );
            await waitFor(() => {
                const menu = queryByTestId('xe-navigation-menu');
                expect(menu).toBeInTheDocument();
            });
        });
    });
});
