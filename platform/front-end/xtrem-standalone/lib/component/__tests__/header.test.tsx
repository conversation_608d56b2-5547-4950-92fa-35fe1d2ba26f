jest.mock('@sage/xtrem-ui', () => ({
    integration: {
        XtremUiIndex: () => <div data-testid="xtrem-ui">Xtrem-UI placeholder</div>,
        DashboardRootComponent: () => <div data-testid="bms-dashboard">BMS dashboard placeholder</div>,
        breakpoints: {},
    },
}));

jest.mock('../notification/notification-center', () => ({
    ConnectedNotificationCenter: () => <div data-testid="xe-notification-center" />,
}));

jest.mock('../notification/notification-previews', () => ({
    ConnectedNotificationPreviews: () => <div data-testid="xe-notification-previews" />,
}));

import { Notification } from '@sage/xtrem-shared';
import { integration } from '@sage/xtrem-ui';
import * as React from 'react';
import { UserDetails } from '../../service/auth-service';
import { XtremHeader } from '../header';
import { render, screen, fireEvent } from '@testing-library/react';

describe('header', () => {
    const user: UserDetails = {
        email: '<EMAIL>',
        tenantId: '000000000',
        firstName: 'John',
        lastName: 'Doe',
        id: '<EMAIL>',
        userCode: '<EMAIL>',
        locale: 'en-GB',
        uniqueUserId: 'asdf',
        uniqueTenantId: 'asdf',
        clientEncryptionKey: 'test',
        photo: null,
        pref: 5,
        uuid: null,
        isOperator: false,
    };

    const tenantItem = {
        oneTrustMagicLink:
            'https://privacyportaluatde.onetrust.com/ui/#/multipage/************************************/preferences',
        tenantList: [
            {
                current: true,
                directLoginUrl: 'connect.localhost.dev-sagextrem.com/login/tenant/000000000000000000000',
                kind: 'demo',
                lastLogin: '2020-12-09T11:50:53.297Z',
                tenantId: '000000000000000000000',
                tenantName: 'Acme-000000000000000000000',
                countryCode: 'ES',
                subscriptionType: 'internal',
            },
            {
                current: false,
                directLoginUrl: 'connect.localhost.dev-sagextrem.com/login/tenant/111111111111111111111',
                kind: 'demo',
                lastLogin: '2020-11-09T08:20:55.227Z',
                tenantId: '111111111111111111111',
                tenantName: 'Acme-111111111111111111111',
                countryCode: 'ES',
                subscriptionType: 'internal',
            },
            {
                current: false,
                directLoginUrl: 'connect.localhost.dev-sagextrem.com/login/tenant/222222222222222222222',
                kind: 'demo',
                lastLogin: '2020-12-06T10:58:33.297Z',
                tenantId: '222222222222222222222',
                tenantName: 'Acme-222222222222222222222',
                countryCode: 'ES',
                subscriptionType: 'internal',
            },
        ],
    };

    const tenantSingleItem = {
        oneTrustMagicLink:
            'https://privacyportaluatde.onetrust.com/ui/#/multipage/************************************/preferences',
        tenantList: [
            {
                current: true,
                directLoginUrl: 'connect.localhost.dev-sagextrem.com/login/tenant/000000000000000000000',
                kind: 'demo',
                lastLogin: '2020-12-09T11:50:53.297Z',
                tenantId: '000000000000000000000',
                tenantName: 'Acme-000000000000000000000',
                countryCode: 'ES',
                subscriptionType: 'internal',
            },
        ],
    };

    it('should render the header without exploding', () => {
        render(
            <XtremHeader
                onUserChangeLocale={jest.fn()}
                productName="Test Product Title"
                menuItems={[]}
                notifications={[]}
                loginService="http://example.com"
                tenantItem={tenantItem}
                user={user}
                goHome={jest.fn()}
                isExtraSmall={false}
                isNavigationOpen={false}
                setNavigationOpen={jest.fn()}
                translations={{}}
                isNotificationCenterOpen={false}
                setNotificationCenterOpen={jest.fn()}
                path=""
                chatbotEnabled={false}
                chatbotConfig={null}
            />,
        );
        expect(screen.queryAllByText('Test Product Title')).toHaveLength(1);

        expect((screen.getByTestId('xe-tenants-current-tenant') as HTMLDivElement).textContent).toBe(
            'Acme-000000000000000000000',
        );
    });

    it('should be possible to click in the tenant name and display all the tenants', async () => {
        render(
            <XtremHeader
                onUserChangeLocale={jest.fn()}
                productName="DMO"
                menuItems={[]}
                notifications={[]}
                loginService="http://example.com"
                tenantItem={tenantItem}
                user={user}
                goHome={jest.fn()}
                isExtraSmall={false}
                isNavigationOpen={false}
                setNavigationOpen={jest.fn()}
                translations={{}}
                isNotificationCenterOpen={false}
                setNotificationCenterOpen={jest.fn()}
                path=""
                chatbotEnabled={false}
                chatbotConfig={null}
            />,
        );
        fireEvent.click(screen.getByTestId('xe-tenants-current-tenant'));
        expect(screen.queryAllByText('Acme-000000000000000000000')).toHaveLength(2);
        expect(screen.queryAllByText('Acme-111111111111111111111')).toHaveLength(1);
        expect(screen.queryAllByText('Acme-222222222222222222222')).toHaveLength(1);
    });

    it('should be possible to see the only tenant but not opening a dropdown', async () => {
        render(
            <XtremHeader
                onUserChangeLocale={jest.fn()}
                productName="DMO"
                menuItems={[]}
                notifications={[]}
                loginService="http://example.com"
                tenantItem={tenantSingleItem}
                user={user}
                goHome={jest.fn()}
                setNavigationOpen={jest.fn()}
                isExtraSmall={false}
                translations={{}}
                isNavigationOpen={false}
                isNotificationCenterOpen={false}
                setNotificationCenterOpen={jest.fn()}
                path=""
                chatbotEnabled={false}
                chatbotConfig={null}
            />,
        );

        fireEvent.click(screen.getByTestId('xe-tenants-current-tenant'));
        expect(screen.queryAllByText('Acme-000000000000000000000')).toHaveLength(1);
        expect(screen.queryAllByText('Acme-111111111111111111111')).toHaveLength(0);
        expect(screen.queryAllByText('Acme-222222222222222222222')).toHaveLength(0);
    });

    it('should render the available menu items', () => {
        const menuItems: integration.Menu[] = [
            { icon: 'home', id: 'ActiveSticker', title: 'Printer destination', category: 'sticker', onClick: () => {} },
            { icon: 'person', id: 'ProfileSticker', title: 'User Profile', category: 'sticker', onClick: () => {} },
        ];
        render(
            <XtremHeader
                onUserChangeLocale={jest.fn()}
                productName="DMO"
                menuItems={menuItems}
                notifications={[]}
                loginService="http://example.com"
                tenantItem={tenantItem}
                user={user}
                goHome={jest.fn()}
                setNavigationOpen={jest.fn()}
                isExtraSmall={false}
                translations={{}}
                isNavigationOpen={false}
                isNotificationCenterOpen={false}
                setNotificationCenterOpen={jest.fn()}
                path=""
                chatbotEnabled={false}
                chatbotConfig={null}
            />,
        );
        expect(screen.getByTestId('ActiveSticker')).toBeTruthy();
        expect(screen.getByTestId('ProfileSticker')).toBeTruthy();
    });

    it('should render isSkipLink link', () => {
        render(
            <XtremHeader
                onUserChangeLocale={jest.fn()}
                productName="DMO"
                menuItems={[]}
                notifications={[]}
                loginService="http://example.com"
                tenantItem={tenantItem}
                user={user}
                goHome={jest.fn()}
                setNavigationOpen={jest.fn()}
                isExtraSmall={false}
                translations={{}}
                isNavigationOpen={false}
                isNotificationCenterOpen={false}
                setNotificationCenterOpen={jest.fn()}
                path=""
                chatbotEnabled={false}
                chatbotConfig={null}
            />,
        );
        expect(screen.getByTestId('xe-skip-link')).toBeTruthy();
    });

    it('should not render tenant selector to operator users', () => {
        const operatorUser: UserDetails = {
            ...user,
            isOperator: true,
        };
        render(
            <XtremHeader
                onUserChangeLocale={jest.fn()}
                productName="DMO"
                menuItems={[]}
                notifications={[]}
                loginService="http://example.com"
                tenantItem={tenantSingleItem}
                user={operatorUser}
                goHome={jest.fn()}
                setNavigationOpen={jest.fn()}
                isExtraSmall={false}
                translations={{}}
                isNavigationOpen={false}
                isNotificationCenterOpen={false}
                setNotificationCenterOpen={jest.fn()}
                path=""
                chatbotEnabled={false}
                chatbotConfig={null}
            />,
        );
        expect(screen.queryAllByText('Acme-000000000000000000000')).toHaveLength(0);
    });

    describe('Menu item', () => {
        it('should display the badge content', () => {
            const menuItems: integration.Menu[] = [
                {
                    icon: 'home',
                    id: 'ActiveSticker',
                    title: 'Printer destination',
                    category: 'sticker',
                    badgeContent: 'Badge Test Content',
                    onClick: () => {},
                },
            ];
            render(
                <XtremHeader
                    onUserChangeLocale={jest.fn()}
                    productName="DMO"
                    menuItems={menuItems}
                    notifications={[]}
                    loginService="http://example.com"
                    tenantItem={tenantItem}
                    user={user}
                    goHome={jest.fn()}
                    setNavigationOpen={jest.fn()}
                    isExtraSmall={false}
                    translations={{}}
                    isNavigationOpen={false}
                    isNotificationCenterOpen={false}
                    setNotificationCenterOpen={jest.fn()}
                    path=""
                    chatbotEnabled={false}
                    chatbotConfig={null}
                />,
            );
            expect(screen.queryAllByText('Badge Test Content')).toHaveLength(1);
        });

        it('should call menu callback onClick', () => {
            const menuOnClick = jest.fn(() => {});
            const menuItems: integration.Menu[] = [
                {
                    icon: 'home',
                    id: 'ActiveSticker',
                    title: 'Printer destination',
                    category: 'sticker',
                    onClick: menuOnClick,
                },
            ];
            render(
                <XtremHeader
                    onUserChangeLocale={jest.fn()}
                    productName="DMO"
                    menuItems={menuItems}
                    notifications={[]}
                    loginService="http://example.com"
                    tenantItem={tenantItem}
                    user={user}
                    goHome={jest.fn()}
                    setNavigationOpen={jest.fn()}
                    isExtraSmall={false}
                    translations={{}}
                    isNavigationOpen={false}
                    isNotificationCenterOpen={false}
                    setNotificationCenterOpen={jest.fn()}
                    path=""
                    chatbotEnabled={false}
                    chatbotConfig={null}
                />,
            );
            fireEvent.click(screen.getByTestId('ActiveSticker'));
            expect(menuOnClick).toHaveBeenCalledTimes(1);
        });

        it('should render the notification with the badge because unread notifications', () => {
            const notifications: Notification[] = [
                {
                    _id: '1',
                    _createStamp: new Date('2023-08-31T06:08:30.000Z'),
                    level: 'info',
                    shouldDisplayToast: false,
                    isRead: false,
                    icon: 'person_info',
                    title: 'Lorem ipsum dolor',
                    description:
                        'Morbi ut maximus nibh. Aenean in auctor nunc, ut sagittis sapien. Praesent at maximus magna, non egestas quam.',
                    actions: [],
                },
            ];
            render(
                <XtremHeader
                    onUserChangeLocale={jest.fn()}
                    productName="Intacct Manufacturing"
                    menuItems={[]}
                    notifications={notifications}
                    loginService="http://example.com"
                    tenantItem={tenantItem}
                    user={user}
                    goHome={jest.fn()}
                    setNavigationOpen={jest.fn()}
                    isExtraSmall={false}
                    translations={{}}
                    isNavigationOpen={false}
                    isNotificationCenterOpen={false}
                    setNotificationCenterOpen={jest.fn()}
                    path=""
                    chatbotEnabled={false}
                    chatbotConfig={null}
                />,
            );
            const unreadNotificationButton = screen.getByTestId('xe-notification-unread');
            expect(unreadNotificationButton).toBeInTheDocument();

            const notificationBadge = unreadNotificationButton.querySelector(
                'span.xe-icon-button-link-notification-badge',
            );
            expect(notificationBadge).toBeInTheDocument();
        });

        it('should render the notification with the icon "alert" because all the notifications are read', () => {
            const notifications: Notification[] = [
                {
                    _id: '1',
                    _createStamp: new Date('2023-08-31T06:08:30.000Z'),
                    level: 'info',
                    shouldDisplayToast: false,
                    isRead: true,
                    icon: 'person_info',
                    title: 'Lorem ipsum dolor',
                    description:
                        'Morbi ut maximus nibh. Aenean in auctor nunc, ut sagittis sapien. Praesent at maximus magna, non egestas quam.',
                    actions: [],
                },
            ];
            render(
                <XtremHeader
                    onUserChangeLocale={jest.fn()}
                    productName="Intacct Manufacturing"
                    menuItems={[]}
                    notifications={notifications}
                    loginService="http://example.com"
                    tenantItem={tenantItem}
                    user={user}
                    goHome={jest.fn()}
                    setNavigationOpen={jest.fn()}
                    isExtraSmall={false}
                    translations={{}}
                    isNavigationOpen={false}
                    isNotificationCenterOpen={false}
                    setNotificationCenterOpen={jest.fn()}
                    path=""
                    chatbotEnabled={false}
                    chatbotConfig={null}
                />,
            );
            const unreadNotificationButton = screen.getByTestId('xe-notification-normal');
            expect(unreadNotificationButton).toBeTruthy();
            expect(unreadNotificationButton.children[0]).toBeTruthy();
            expect(unreadNotificationButton.children[0].getAttribute('data-element')).toBe('alert');
        });

        it('should render the notification with the icon "alert" because no notifications', () => {
            const notifications: Notification[] = [];
            render(
                <XtremHeader
                    onUserChangeLocale={jest.fn()}
                    productName="Intacct Manufacturing"
                    menuItems={[]}
                    notifications={notifications}
                    loginService="http://example.com"
                    tenantItem={tenantItem}
                    user={user}
                    goHome={jest.fn()}
                    setNavigationOpen={jest.fn()}
                    isExtraSmall={false}
                    translations={{}}
                    isNavigationOpen={false}
                    isNotificationCenterOpen={false}
                    setNotificationCenterOpen={jest.fn()}
                    path=""
                    chatbotEnabled={false}
                    chatbotConfig={null}
                />,
            );
            const unreadNotificationButton = screen.getByTestId('xe-notification-normal');
            expect(unreadNotificationButton).toBeTruthy();
            expect(unreadNotificationButton.children[0]).toBeTruthy();
            expect(unreadNotificationButton.children[0].getAttribute('data-element')).toBe('alert');
        });
    });

    describe('chatbot button', () => {
        it('should not render the button if the chatbot is disabled', () => {
            const notifications: Notification[] = [];
            const { queryByTestId } = render(
                <XtremHeader
                    onUserChangeLocale={jest.fn()}
                    productName="Intacct Manufacturing"
                    menuItems={[]}
                    notifications={notifications}
                    loginService="http://example.com"
                    tenantItem={tenantItem}
                    user={user}
                    goHome={jest.fn()}
                    setNavigationOpen={jest.fn()}
                    isExtraSmall={false}
                    translations={{}}
                    isNavigationOpen={false}
                    isNotificationCenterOpen={false}
                    setNotificationCenterOpen={jest.fn()}
                    path=""
                    chatbotEnabled={false}
                    chatbotConfig={{
                        aiToken: 'someToken',
                        accessCode:
                            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWUsImlhdCI6MTUxNjIzOTAyMn0.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30',
                        accessCodeExpiration: 1516239022,
                    }}
                />,
            );

            expect(queryByTestId('xe-ai-assistant-action')).toBeNull();
        });

        it('should display the chatbot button if the user is on a supported page', () => {
            const notifications: Notification[] = [];
            const { queryByTestId } = render(
                <XtremHeader
                    onUserChangeLocale={jest.fn()}
                    productName="Intacct Manufacturing"
                    menuItems={[]}
                    notifications={notifications}
                    loginService="http://example.com"
                    tenantItem={tenantItem}
                    user={user}
                    goHome={jest.fn()}
                    setNavigationOpen={jest.fn()}
                    isExtraSmall={false}
                    translations={{}}
                    isNavigationOpen={false}
                    isNotificationCenterOpen={false}
                    setNotificationCenterOpen={jest.fn()}
                    path="@sage/xtrem-show-case/ShowCaseProduct"
                    chatbotEnabled={true}
                    chatbotConfig={{
                        aiToken: 'someToken',
                        accessCode:
                            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWUsImlhdCI6MTUxNjIzOTAyMn0.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30',
                        accessCodeExpiration: 1516239022,
                    }}
                />,
            );

            expect(queryByTestId('xe-ai-assistant-action-button')).not.toBeNull();
        });
        it('should display the chatbot button if the user is on a supported page with query params', () => {
            const notifications: Notification[] = [];
            const { queryByTestId } = render(
                <XtremHeader
                    onUserChangeLocale={jest.fn()}
                    productName="Intacct Manufacturing"
                    menuItems={[]}
                    notifications={notifications}
                    loginService="http://example.com"
                    tenantItem={tenantItem}
                    user={user}
                    goHome={jest.fn()}
                    setNavigationOpen={jest.fn()}
                    isExtraSmall={false}
                    translations={{}}
                    isNavigationOpen={false}
                    isNotificationCenterOpen={false}
                    setNotificationCenterOpen={jest.fn()}
                    path="@sage/xtrem-show-case/ShowCaseProduct/someParams"
                    chatbotEnabled={true}
                    chatbotConfig={{
                        aiToken: 'someToken',
                        accessCode:
                            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWUsImlhdCI6MTUxNjIzOTAyMn0.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30',
                        accessCodeExpiration: 1516239022,
                    }}
                />,
            );

            expect(queryByTestId('xe-ai-assistant-action-button')).not.toBeNull();
        });

        it('should not render the chatbot button if the user is not on a supported page', () => {
            const notifications: Notification[] = [];
            const { queryByTestId } = render(
                <XtremHeader
                    onUserChangeLocale={jest.fn()}
                    productName="Intacct Manufacturing"
                    menuItems={[]}
                    notifications={notifications}
                    loginService="http://example.com"
                    tenantItem={tenantItem}
                    user={user}
                    goHome={jest.fn()}
                    setNavigationOpen={jest.fn()}
                    isExtraSmall={false}
                    translations={{}}
                    isNavigationOpen={false}
                    isNotificationCenterOpen={false}
                    setNotificationCenterOpen={jest.fn()}
                    path="@sage/xtrem-show-case/SomeRandomPage"
                    chatbotEnabled={true}
                    chatbotConfig={{
                        aiToken: 'someToken',
                        accessCode:
                            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWUsImlhdCI6MTUxNjIzOTAyMn0.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30',
                        accessCodeExpiration: 1516239022,
                    }}
                />,
            );

            expect(queryByTestId('xe-ai-assistant-action-button')).toBeNull();
        });
    });
});
