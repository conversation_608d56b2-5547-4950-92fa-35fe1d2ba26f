const { fireEvent, render, waitFor } = require('@testing-library/react');
import * as React from 'react';
import { SitemapSubMenu, SitemapSubMenuProps } from '../sitemap-submenu';

describe('sitemap sub menu', () => {
    beforeEach(() => {
        jest.restoreAllMocks();
    });

    const defaultProps: SitemapSubMenuProps = {
        level: 1,
        onSelectLink: jest.fn(() => jest.fn()),
        path: null,
        sitemapEntry: {
            children: [
                { id: '@sage/test/TestRecentPage2', isPage: true, priority: 322323, title: 'Title 2' },
                { id: '@sage/test/TestRecentPage1', isPage: true, priority: 12, title: 'Title 1' },
            ],
            isPage: false,
            id: '@sage/test/AnotherCategory',
            priority: 2,
            title: 'Category 1',
        },
    };

    const renderSitemapSubMenu = (props: Partial<SitemapSubMenuProps> = {}) => {
        return render(<SitemapSubMenu {...defaultProps} {...props} />);
    };

    it('should render closed by default', () => {
        const container = renderSitemapSubMenu();
        const menuHeader = container.getByTestId('xe-navigation-sub-menu-link-@sage/test/AnotherCategory');
        expect(menuHeader.textContent).toEqual('Category 1');
    });

    it('should toggle dropdown when category header clicked', async () => {
        const container = renderSitemapSubMenu();
        const menuHeader = container.getByTestId('xe-navigation-sub-menu-link-@sage/test/AnotherCategory');
        expect(container.baseElement.querySelectorAll('.xe-navigation-sub-menu-level-2')).toHaveLength(0);

        fireEvent.click(menuHeader);
        await waitFor(() => {
            expect(container.baseElement.querySelectorAll('.xe-navigation-sub-menu-level-2')).toHaveLength(2);
        });

        fireEvent.click(menuHeader);
        await waitFor(() => {
            expect(container.baseElement.querySelectorAll('.xe-navigation-sub-menu-level-2')).toHaveLength(0);
        });
    });

    it('should trigger navigation callback when page element clicked', async () => {
        const container = renderSitemapSubMenu();
        const menuHeader = container.getByTestId('xe-navigation-sub-menu-link-@sage/test/AnotherCategory');
        expect(defaultProps.onSelectLink).not.toHaveBeenCalled();
        fireEvent.click(menuHeader);

        await waitFor(() => {
            const submenuHeader = container.getByTestId('xe-navigation-sub-menu-link-@sage/test/TestRecentPage2');
            fireEvent.click(submenuHeader);
        });

        await waitFor(() => {
            expect(defaultProps.onSelectLink).toHaveBeenCalledWith('@sage/test/TestRecentPage2');
        });
    });
});
