@import '../variables.scss';

.xe-chatbot {
    height: 60vh;
    position: absolute;
    display: flex;
    flex-direction: column;
    bottom: 0;
    right: 40px;
    z-index: 4000;
    width: 400px;

    &.xe-chatbot-collapsed {
        height: 56px;
    }

    .xe-chatbot-header {
        padding: 12px 16px;
        align-items: center;
        color: var(--colorsActionMajorYang100);
        border-top-left-radius: var(--borderRadius200);
        border-top-right-radius: var(--borderRadius200);
        background: var(--colorsGray1000);
        display: flex;

        >svg {
            height: 32px;
            width: 32px;
        }

        .xe-chatbot-header-label {
            font-family: $fontAdelle;
            font-size: var(--fontSizes300);
            padding-left: 8px;
            flex: 1;
        }
    }

    .xe-chatbot-body {
        display: flex;
        flex: 1;

        >div {
            border-radius: 0;
            box-shadow: var(--boxShadow100);
        }
    }
}