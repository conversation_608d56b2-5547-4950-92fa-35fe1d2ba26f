import { getNew<PERSON>elic<PERSON><PERSON>, getPendo<PERSON>pi } from './service/telemetry-service';
import type { ErrorInfo, ReactNode } from 'react';
import React, { Component } from 'react';

interface ErrorBoundaryProps {
    children: ReactNode;
}

interface ErrorBoundaryState {
    error?: Error;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = {};
    }

    public static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        return { error };
    }

    public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        getPendoApi()?.track('unhandledEvent', {
            message: error.message,
            stack: error.stack!,
        });
        getNewRelicApi()?.noticeError(error);
        getPendoApi()?.track('unhandledEvent', {
            message: error.message,
            stack: String(errorInfo.componentStack),
        });
        // eslint-disable-next-line no-console
        console.warn(errorInfo);
    }

    public render() {
        if (this.state.error) {
            return (
                <div>
                    <h4>An error occurred while loading the page. Try again.</h4>
                    <pre>{this.state.error?.message}</pre>
                    <pre>{this.state.error?.stack}</pre>
                </div>
            );
        }

        return this.props.children;
    }
}
