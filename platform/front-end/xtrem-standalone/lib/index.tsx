import { integration } from '@sage/xtrem-ui';
import type { QueryParameters } from '@sage/xtrem-ui';
import * as React from 'react';
import { createRoot } from 'react-dom/client';
import { connect, Provider } from 'react-redux';
import { ConnectedXtremHeader } from './component/header';
import { ConnectedXtremNavigation } from './component/navigation';
import type * as actionTypes from './standalone-redux/action-types';
import * as actions from './standalone-redux/actions';
import type { XtremStandaloneState } from './standalone-redux/state';
import { getStore } from './standalone-redux/store';
import type { UserDetails } from './service/auth-service';
import { lockRenewal, shouldRenewToken, unlockRenewal } from './service/auth-service';
import { ConnectedMobileNavigation } from './component/mobile-navigation';
import sageTheme from 'carbon-react/esm/style/themes/sage';
import Loader from 'carbon-react/esm/components/loader';
import { localize } from './service/standalone-i18n-service';
import I18nProvider from 'carbon-react/esm/components/i18n-provider';
import CarbonProvider from 'carbon-react/esm/components/carbon-provider';
import type { Dict, StandaloneConfig } from '@sage/xtrem-shared';
import { clone, set } from 'lodash';
import { getNewRelicApi, getPendoApi } from './service/telemetry-service';
import { ErrorBoundary } from './error-boundary';
import { Chatbot } from './component/chatbot';
import { initiateSocketConnection } from './service/socket-service';

import './style.scss';

const LAST_USER_INTERACTION = 'LAST_USER_INTERACTION';
const IDLE_LIMIT = 60 * 60000;
const customizedTheme = set(clone(sageTheme), 'zIndex.popover', 5000);

export interface XtremStandaloneProps {
    path: string;
    user: UserDetails | null;
    loginService: string | null;
    updateNavStateBasedOnLocation: () => void;
    init: () => void;
    onInternalNavigationChange: (path: string, doDirtyCheck: boolean) => Promise<void>;
    setPageTitle: (newTitle: string) => void;
    setMenuItems: (menuItems: integration.Menu[]) => void;
    setPageContext: (pageContext: integration.PageContext | null) => void;
    onDirtyStatusChange: (isApplicationDirty: boolean, preNavigationConfirmation: () => Promise<void>) => void;
    config: StandaloneConfig | null;
    isExtraSmall: boolean;
    translations: Dict<string>;
    isNavigationOpen: boolean;
}

export class XtremStandalone extends React.Component<XtremStandaloneProps, { removeXtremUi?: boolean }> {
    private intervalRef: number | null = null;

    private readonly iframeRef = React.createRef<HTMLIFrameElement>();

    private readonly xtremUiRef = React.createRef<integration.XtremUiIndex>();

    private readonly appContext: integration.ApplicationContext = {
        handleNavigation: (path: string, _queryParameters?: QueryParameters, doDirtyCheck = false) =>
            this.props.onInternalNavigationChange(path, doDirtyCheck),
        updateMenu: this.props.setMenuItems,
        onPageTitleChange: this.props.setPageTitle,
        onPageContextChange: this.props.setPageContext,
    };

    private hasInitializedWebsocket = false;

    constructor(props: XtremStandaloneProps) {
        super(props);
        this.onUserUpdate();
        this.checkAuthTokenState(props.user?.locale!, false);
        this.state = {
            removeXtremUi: false,
        };
    }

    componentDidMount() {
        // Start listening on browser navigation changes
        window.addEventListener('popstate', this.props.updateNavStateBasedOnLocation);

        this.props.init();

        // Checks every 20 seconds if the session needs to be renewed
        this.intervalRef = setInterval(this.checkAuthTokenState, 20000) as unknown as number;
    }

    componentDidUpdate() {
        if (this.props.isNavigationOpen && this.xtremUiRef.current) {
            this.xtremUiRef.current.cleanToasts();
        }

        if (!this.hasInitializedWebsocket && this.xtremUiRef.current && this.props.config) {
            initiateSocketConnection({
                protocols: this.props.config.app,
                onWebSocketMessage: this.xtremUiRef.current.onWebSocketMessage,
            });

            this.hasInitializedWebsocket = true;
        }
    }

    componentWillUnmount() {
        if (this.intervalRef) {
            clearInterval(this.intervalRef);
            this.intervalRef = null;
        }
    }

    private readonly checkAuthTokenState = async (locale: string, forceRenewToken: boolean): Promise<void> =>
        new Promise((resolve, reject) => {
            const lastInteraction = window.sessionStorage.getItem(LAST_USER_INTERACTION);
            const idleTimeLimit = new Date().getTime() - IDLE_LIMIT;
            if (lastInteraction && Number(lastInteraction) < idleTimeLimit) {
                getPendoApi()?.track('userIdleTimeout');
                // redirects user to login page if LAST_USER_INTERACTION was more than 5 min ago
                this.onUserLogout();
            }

            if ((shouldRenewToken() || forceRenewToken) && this.props.loginService && this.iframeRef.current) {
                // It locks the renewal process so other tabs will not try to renew the session at the same time.
                lockRenewal();
                this.iframeRef.current.onerror = () => {
                    // Check if still need to renew the token because due to race condition could be updated by other tab
                    if (shouldRenewToken()) {
                        getPendoApi()?.track('renewTokenError');
                        // If the renewal fails, it retries after 1 second just in case the new token is not placed yet by other tab
                        setTimeout(() => {
                            if (shouldRenewToken()) {
                                getPendoApi()?.track('renewTokenErrorRetry');
                                this.onUserLogout();
                                reject();
                            } else {
                                resolve();
                            }
                        }, 2000);
                    } else {
                        resolve();
                    }
                };

                // Once the page is successfully loaded, it removed the lock
                this.iframeRef.current.onload = () => {
                    unlockRenewal();
                    resolve();
                };

                this.iframeRef.current.src = `${this.props.loginService}/renew?locale=${locale}`;
            }
        });

    private readonly onTelemetryEvent = (event: string, data: any) => {
        if (event === 'unhandledError') {
            const error = data as Error;
            getPendoApi()?.track(event, {
                message: error.message,
                stack: error.stack!,
            });
            getNewRelicApi()?.noticeError(error);
        } else {
            getPendoApi()?.track(event, data);
        }
    };

    private readonly onUserLogout = (): any => {
        window.location.replace(`${this.props.loginService}/logout?fromUrl=${document.location.href}`);
    };

    private readonly onUserUpdate = (): void => {
        window.sessionStorage.setItem(LAST_USER_INTERACTION, String(new Date().getTime()));
    };

    private readonly onUserChangeLocale = async (locale: string): Promise<void> => {
        try {
            await this.props.onInternalNavigationChange(this.props.path, true);
            await this.checkAuthTokenState(locale, true);
            this.setState({ removeXtremUi: true }, () => document.location.reload());
        } catch (e) {
            // ignore
        }
    };

    render() {
        if (this.props.user && this.props.config) {
            const locale = this.props.user.locale || 'en-US';
            this.appContext.locale = locale;
            this.appContext.login = this.props.user.id!;
            this.appContext.userCode = this.props.user.userCode || this.appContext.login;
            this.appContext.cacheEncryptionKey = this.props.user.clientEncryptionKey;
            this.appContext.agGridLicenceKey = this.props.config?.agGridLicenceKey!;
            this.appContext.onDirtyStatusChange = this.props.onDirtyStatusChange;
            this.appContext.onTelemetryEvent = this.onTelemetryEvent;
            this.appContext.onApiRequestError = ({ status }) => {
                if (status === 401) {
                    this.onUserLogout();
                }
            };

            const carbonLocale: any = {
                link: {
                    skipLinkLabel: (): string =>
                        localize(
                            '@sage/xtrem-standalone/skip-to-main-content',
                            'Skip to main content',
                            {},
                            this.props.translations,
                            locale,
                        ),
                },
            };

            const hasPage = !!this.props.path;
            return (
                <ErrorBoundary>
                    <I18nProvider locale={carbonLocale}>
                        <ConnectedXtremHeader onUserChangeLocale={this.onUserChangeLocale} />
                        <ConnectedMobileNavigation />
                        <Chatbot />
                        <div className="xe-body" onKeyDown={this.onUserUpdate} onMouseMove={this.onUserUpdate}>
                            {!this.props.isExtraSmall && <ConnectedXtremNavigation />}
                            <div
                                className="xe-app-container"
                                style={{
                                    height: hasPage ? '100vh' : 0,
                                    paddingLeft: this.props.isExtraSmall ? 0 : '64px',
                                }}
                            >
                                <ErrorBoundary>
                                    {!this.state?.removeXtremUi && (
                                        <integration.XtremUiIndex
                                            path={this.props.path}
                                            applicationContext={this.appContext}
                                            ref={this.xtremUiRef}
                                        />
                                    )}
                                </ErrorBoundary>
                            </div>
                            {!hasPage && (
                                <integration.DashboardRootComponent applicationContext={this.appContext} group="home" />
                            )}
                        </div>
                        <iframe ref={this.iframeRef} style={{ display: 'none' }} title="auth iframe" />
                    </I18nProvider>
                </ErrorBoundary>
            );
        }
        return (
            <div className="initial-loader-wrapper">
                <div className="initial-loader">
                    <Loader size="large" />
                </div>
            </div>
        );
    }
}

const mapStateToProps = (state: XtremStandaloneState): XtremStandaloneProps => ({
    config: state.config,
    isExtraSmall: state.browser.is.xs,
    isNavigationOpen: state.isNavigationOpen,
    loginService: state.loginService,
    path: state.path,
    translations: state.translations,
    user: state.user,
    init: actions.actionStub,
    onDirtyStatusChange: actions.actionStub,
    onInternalNavigationChange: actions.actionStub,
    setPageTitle: actions.actionStub,
    setMenuItems: actions.actionStub,
    setPageContext: actions.actionStub,
    updateNavStateBasedOnLocation: actions.actionStub,
});

const mapDispatchToProps = (dispatch: actionTypes.AppThunkDispatch) => ({
    updateNavStateBasedOnLocation: () => dispatch(actions.updateNavStateBasedOnLocation()),
    onDirtyStatusChange: (isApplicationDirty: boolean, preNavigationConfirmation: () => Promise<void>) =>
        dispatch(actions.onDirtyStatusChange(isApplicationDirty, preNavigationConfirmation)),
    init: () => dispatch(actions.init()),
    onInternalNavigationChange: (path: string, doDirtyCheck = false) =>
        dispatch(actions.onInternalNavigationChange(path, doDirtyCheck)),
    setPageTitle: (title: string): void => {
        dispatch(actions.setPageTitle(title));
    },
    setPageContext: (pageContext: integration.PageContext | null): void => {
        dispatch(actions.setPageContext(pageContext));
    },
    setMenuItems: (menuItems: integration.Menu[]) => dispatch(actions.setMenuItems(menuItems)),
});

// tslint:disable-next-line:variable-name
export const ConnectedXtremStandaloneComponent = connect(mapStateToProps, mapDispatchToProps)(XtremStandalone);

(window as any).start = () => {
    createRoot(window.document.getElementById('root') as HTMLElement).render(
        <Provider store={getStore()}>
            <CarbonProvider theme={customizedTheme}>
                <ConnectedXtremStandaloneComponent />
            </CarbonProvider>
        </Provider>,
    );
};
