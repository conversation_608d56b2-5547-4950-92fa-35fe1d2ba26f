import axios from 'axios';
import { reverse, sortBy } from 'lodash';

export interface TenantDetails {
    current: boolean;
    directLoginUrl: string;
    kind: string;
    lastLogin: string;
    tenantId: string;
    tenantName: string;
    countryCode: string;
    subscriptionType: string;
}

export interface TenantItem {
    oneTrustMagicLink: string;
    tenantList: TenantDetails[];
}

export const fetchTenants = async (loginServiceUrl: string) => {
    const response = await axios.get(`${loginServiceUrl}/api/user/tenants`, { withCredentials: true });

    const tenantItem = {
        oneTrustMagicLink: response.data?.oneTrustMagicLink,
        tenantList: reverse(sortBy(response.data?.tenants ?? [], 'lastLogin')),
    };

    return tenantItem as TenantItem;
};
