import type { Dict } from '@sage/xtrem-shared';
import { format } from '@sage/xtrem-shared';

/**
 * This function will be either injected by the 'decoratorTransformer' (which wraps a specific set of decorators
 * properties) or can be used in any page or sticker for any string that has to be translated.
 * The translations will be fetched by the 'screen-loader-service' through the metadata endpoint based on
 * the browser's locale settings. Hence the 'localize' function simply relies on the state to fetch all translations.
 *
 * @export
 * @param {string} key the translation key
 * @param {string} value the translation default value
 * @returns {string} the translated string
 */
export function localize(
    key: string,
    value: string,
    data: object | any[],
    translations: Dict<string>,
    locale = 'en-US',
): string {
    const literal = translations[key] || value;
    return format(literal, locale, data);
}
