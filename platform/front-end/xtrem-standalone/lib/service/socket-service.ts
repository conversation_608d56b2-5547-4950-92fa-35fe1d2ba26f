import type * as actionTypes from '../standalone-redux/action-types';
import { refreshNotifications } from '../standalone-redux/actions';
import { getStore } from '../standalone-redux/store';

/**
 * websocket url always the same for all clusters, replace cluster name by websocket and keep rest of the domain
 */
export const extractWebsocketUrlFromCurrentDomain = () => {
    const hostnameComponents = document.location.hostname.split('.');
    const portNumber = document.location.port;
    return `${window.location.protocol === 'https:' ? 'wss' : 'ws'}://websocket.${hostnameComponents
        .filter((_, index) => index > 0)
        .join('.')}:${portNumber}`;
};

async function fetchNotifications() {
    const store = getStore();
    const dispatch = store.dispatch as actionTypes.AppThunkDispatch;
    await dispatch(refreshNotifications());
}

export const initiateSocketConnection = ({
    protocols,
    onWebSocketMessage,
}: {
    protocols?: string;
    onWebSocketMessage: (event: MessageEvent) => void;
}) => {
    let keepAliveIntervalRef: NodeJS.Timeout | null;
    let refreshNotificationsIntervalRef: NodeJS.Timeout | null;
    const webSocket = new WebSocket(extractWebsocketUrlFromCurrentDomain(), protocols);

    const sendKeepAliveMessage = () => {
        try {
            webSocket?.send(JSON.stringify({ category: 'keepAlive' }));
        } catch (err) {
            // eslint-disable-next-line no-console
            console.warn(err);
        }
    };

    const clearKeepAliveInterval = (): void => {
        if (keepAliveIntervalRef) {
            clearInterval(keepAliveIntervalRef);
            keepAliveIntervalRef = null;
        }
    };
    const clearRefreshNotificationsInterval = (): void => {
        if (refreshNotificationsIntervalRef) {
            clearInterval(refreshNotificationsIntervalRef);
            refreshNotificationsIntervalRef = null;
        }
    };
    webSocket.onclose = () => {
        clearKeepAliveInterval();
        clearRefreshNotificationsInterval();
        setTimeout(() => {
            initiateSocketConnection({ protocols, onWebSocketMessage });
        }, 30000);
    };

    webSocket.onopen = () => {
        clearKeepAliveInterval();
        clearRefreshNotificationsInterval();
        keepAliveIntervalRef = setInterval(sendKeepAliveMessage, 25000);
        refreshNotificationsIntervalRef = setInterval(fetchNotifications, 60000);
    };

    webSocket.onmessage = async event => {
        let content: any = {};
        try {
            content = JSON.parse(event.data);
        } catch {
            // Intentionally left empty
        }

        if (content.category === 'userNotification') {
            const dispatch = getStore().dispatch as actionTypes.AppThunkDispatch;
            await dispatch(refreshNotifications());
        }
        onWebSocketMessage(event);
    };
};
