// These APIs are traditionally loaded in by snippets in the document head so we need to double check if they are defined.
type NewRelicApi = typeof newrelic;

declare const NREUM: NewRelicApi;

export const getPendoApi = () => {
    try {
        if (pendo) {
            return pendo;
        }
    } catch (e) {
        // Intentionally empty
    }
    return null;
};
export const getNewRelicApi = (): NewRelicApi | null => {
    try {
        if (NREUM) {
            return NREUM;
        }
    } catch (e) {
        // Intentionally empty
    }
    return null;
};
