import type { Dict } from '@sage/xtrem-shared';
import { jwtDecode } from 'jwt-decode';

export const RENEW_LOCK_KEY = 'RENEW_LOCK_KEY';

export interface DecodedToken {
    auth0?: string;
    jti?: string;
    cluster?: string;
    sub: string;
    tenantId: string;
    mfaEnforced?: boolean;
    locale: string;
    pref: number;
    supportUserEmail?: string;
    supportUserName?: string;
    aud: string;
    iss: string;
    iat: number;
    exp: number;
}

export interface UserDetails {
    id: string | null;
    userCode: string | null;
    locale: string | null;
    email: string | null;
    firstName: string | null;
    lastName: string | null;
    photo: string | null;
    tenantId: string | null;
    uniqueTenantId: string | null;
    uniqueUserId: string | null;
    clientEncryptionKey: string;
    pref: number;
    uuid: string | null;
    isOperator: boolean | null;
}

/** Parses cookies to a string indexed dictionary */
export const getCookies = () =>
    window.document.cookie.split(';').reduce((prevValue, currentValue) => {
        const separatorIndex = currentValue.indexOf('=');
        prevValue[currentValue.substr(0, separatorIndex).trim()] = currentValue.substr(separatorIndex + 1).trim();
        return prevValue;
    }, {} as Dict<string>);

/** Decodes the access_token if it exists */
export const getDecodedToken = (): DecodedToken | null => {
    const cookies = getCookies();
    if (cookies.access_token) {
        return jwtDecode<DecodedToken>(cookies.access_token);
    }
    return null;
};

/** Extracts the user details from the access token */
export const getUserDetails = (): Partial<UserDetails> => {
    const decodedToken = getDecodedToken();
    if (decodedToken) {
        const pipeIndex = decodedToken.auth0?.indexOf('|') || 0;
        return {
            id: decodedToken.sub,
            locale: decodedToken.locale,
            email: decodedToken.sub,
            tenantId: decodedToken.tenantId,
            pref: decodedToken.pref,
            uuid: decodedToken.auth0?.substring(pipeIndex + 1),
            isOperator: !!decodedToken.auth0?.startsWith('device'),
        };
    }

    return {
        id: null,
        locale: null,
        email: null,
        uuid: null,
        isOperator: null,
    };
};

/**
 * Removes the renewal lock.
 */
export const unlockRenewal = () => {
    window.sessionStorage.removeItem(RENEW_LOCK_KEY);
};

/**
 * Locks the renewal so the session is not renewed concurrently if many tabs are open. It sets a timestamp of 10 seconds
 * in the future so if something fails or the user closes the tab while the session is getting renewed, it will not get
 * jammed and future sessions or other tabs can renew the token.
 */
export const lockRenewal = () => {
    window.sessionStorage.setItem(RENEW_LOCK_KEY, String(new Date().getTime() + 10000));
    setTimeout(unlockRenewal, 10000);
};

/**
 * Checks if the token should be renewed. It should only be renewed if the access token expires in the next two minutes
 * and there isn't an active lock in place already.
 */
export const shouldRenewToken = (): boolean => {
    const decodedToken = getDecodedToken();
    return (
        !!decodedToken &&
        new Date((decodedToken.exp - 120) * 1000) < new Date() &&
        (!window.sessionStorage.getItem(RENEW_LOCK_KEY) ||
            new Date(Number(window.sessionStorage.getItem(RENEW_LOCK_KEY))) < new Date())
    );
};
