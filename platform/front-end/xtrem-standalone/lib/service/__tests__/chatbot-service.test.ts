import axios from 'axios';
import { fetchChatbotConfiguration } from '../chatbot-service';

describe('fetchChatbotConfiguration', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should get to login service', async () => {
        const get = jest.fn(() => Promise.reject('TestError: Endpoint not implemented.'));
        const post = jest.fn(() =>
            Promise.resolve({ data: { data: { serviceSettings: { copilot: { token: 'someToken' } } } } }),
        );
        jest.spyOn(axios, 'get').mockImplementation(get);
        jest.spyOn(axios, 'post').mockImplementation(post);
        await fetchChatbotConfiguration('http://sso.xtrem-login-service.com');
        expect(get).toHaveBeenCalledWith('http://sso.xtrem-login-service.com/copilot', {
            withCredentials: true,
        });
    });

    it('should return null given invalid login service response', async () => {
        const get = jest.fn(() => Promise.resolve({ data: {} }));
        const post = jest.fn(() =>
            Promise.resolve({ data: { data: { serviceSettings: { copilot: { token: 'someToken' } } } } }),
        );
        jest.spyOn(axios, 'get').mockImplementation(get);
        jest.spyOn(axios, 'post').mockImplementation(post);
        const configuration = await fetchChatbotConfiguration('http://sso.xtrem-login-service.com');
        expect(configuration).toBeNull();
    });

    it('should return null given errored login service response', async () => {
        const get = jest.fn(() => Promise.reject('TestError: Endpoint not implemented.'));
        const post = jest.fn(() =>
            Promise.resolve({ data: { data: { serviceSettings: { copilot: { token: 'someToken' } } } } }),
        );
        jest.spyOn(axios, 'get').mockImplementation(get);
        jest.spyOn(axios, 'post').mockImplementation(post);
        const configuration = await fetchChatbotConfiguration('http://sso.xtrem-login-service.com');
        expect(configuration).toBeNull();
    });

    it('should return aiToken and graphQL from response', async () => {
        const get = jest.fn(() =>
            Promise.resolve({
                data: {
                    aiToken: 'someToken',
                    accessCode:
                        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWUsImlhdCI6MTUxNjIzOTAyMn0.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30',
                    accessCodeExpiration: 1516239022,
                },
            }),
        );
        const post = jest.fn(() =>
            Promise.resolve({
                data: { data: { serviceSettings: { copilot: { token: 'someToken', expiration: 0, enabled: true } } } },
            }),
        );
        jest.spyOn(axios, 'post').mockImplementation(post);
        jest.spyOn(axios, 'get').mockImplementation(get);
        const configuration = await fetchChatbotConfiguration('http://sso.xtrem-login-service.com', 60);
        expect(configuration).toEqual({
            aiToken: {
                token: 'someToken',
                expiration: expect.any(Number),
                enabled: true,
            },
            accessCode: {
                code: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWUsImlhdCI6MTUxNjIzOTAyMn0.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30',
                expiration: expect.any(Number),
            },
            chatbotAccessCodeLifeTimeInMinutes: 60,
        });
    });
});
