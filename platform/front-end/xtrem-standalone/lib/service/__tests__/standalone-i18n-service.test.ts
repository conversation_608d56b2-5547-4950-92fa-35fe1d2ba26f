import { format } from '@sage/xtrem-shared';

jest.mock('@sage/xtrem-shared', () => ({
    format: jest.fn(),
}));

import { localize } from '../standalone-i18n-service';

describe('StandaloneI18nService', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it(`should wrap xtrem shared's "format" function with translation key`, () => {
        const key = '@sage/xtrem-standalone-test/key1';
        const value = 'Lorem ipsum dolor sit amet';
        const translations = {
            [key]: 'The quick brown fox jumps over the lazy dog',
        };
        const data = { foo: 'foo', bar: 'bar' };
        const locale = 'en-US';
        localize(key, value, data, translations, locale);
        expect(format).toHaveBeenCalledWith(translations[key], locale, data);
    });

    it(`should wrap xtrem shared's "format" function with fallback value`, () => {
        const key = '@sage/xtrem-standalone-test/key1';
        const value = 'Lorem ipsum dolor sit amet';
        const translations = {
            '@sage/xtrem-standalone-test/quick-fox': 'The quick brown fox jumps over the lazy dog',
        };
        const data = { foo: 'foo', bar: 'bar' };
        const locale = 'en-US';
        localize(key, value, data, translations, locale);
        expect(format).toHaveBeenCalledWith(value, locale, data);
    });
});
