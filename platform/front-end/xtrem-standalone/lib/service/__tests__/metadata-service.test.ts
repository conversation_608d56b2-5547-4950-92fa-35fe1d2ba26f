import axios from 'axios';
import * as metadataService from '../metadata-service';

describe('metadata service', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('fetchLoginServiceUrl', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should throw given invalid login service response', async () => {
            const get = jest.fn(() => Promise.reject('TestError: Endpoint not implemented.'));
            jest.spyOn(axios, 'get').mockImplementation(get);
            try {
                await metadataService.fetchLoginServiceUrl();
                fail('Expected to throw');
            } catch (error: any) {
                expect(error).toEqual('TestError: Endpoint not implemented.');
            }
        });

        it('should return login service data', async () => {
            const get = jest.fn(() => Promise.resolve({ data: 'test-login-service-response' }));
            jest.spyOn(axios, 'get').mockImplementation(get);
            expect(await metadataService.fetchLoginServiceUrl()).toEqual('test-login-service-response');
            expect(get).toHaveBeenCalledWith('/login-service');
        });
    });

    describe('fetchStandaloneConfig', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should throw given invalid standalone config response', async () => {
            const get = jest.fn(() => Promise.reject('TestError: Endpoint not implemented.'));
            jest.spyOn(axios, 'get').mockImplementation(get);
            try {
                await metadataService.fetchStandaloneConfig({});
                fail('Expected to throw');
            } catch (error: any) {
                expect(error).toEqual('TestError: Endpoint not implemented.');
            }
        });

        it('should fetch standalone config data', async () => {
            const get = jest.fn(() => Promise.resolve({ data: 'test-login-service-response' }));
            jest.spyOn(axios, 'get').mockImplementation(get);
            expect(await metadataService.fetchStandaloneConfig({})).toEqual('test-login-service-response');
            expect(get).toHaveBeenCalledWith('/standalone-config');
        });

        it('should return standalone config given invalid user details', async () => {
            const get = jest.fn(() => Promise.resolve({ data: { agGridLicenceKey: 'dGVzdC1saXNlbmNlLWtleQ==' } }));
            jest.spyOn(axios, 'get').mockImplementation(get);
            expect(await metadataService.fetchStandaloneConfig({})).toEqual({
                agGridLicenceKey: 'dGVzdC1saXNlbmNlLWtleQ==',
            });
        });

        it('should return standalone config with personalized ag grid license key given user details', async () => {
            const get = jest.fn(() => Promise.resolve({ data: { agGridLicenceKey: 'dGVzdC1saXNlbmNlLWtleQ==' } }));
            jest.spyOn(axios, 'get').mockImplementation(get);
            expect(
                await metadataService.fetchStandaloneConfig({ email: '<EMAIL>', tenantId: '1' }),
            ).toEqual({ agGridLicenceKey: 'test-lisence-key' });
        });
    });

    describe('fetchMataData', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should return expected metadata result', async () => {
            const post = jest.fn(() =>
                Promise.resolve({
                    data: {
                        data: {
                            userInfo: {
                                id: '1',
                                email: '<EMAIL>',
                            },
                            sitemap: {},
                            notifications: [],
                            strings: [
                                { key: '@sage/xtrem-standalone-test/key1', content: 'test-value-1' },
                                { key: '@sage/xtrem-standalone-test/key2', content: 'test-value-2' },
                            ],
                        },
                    },
                }),
            );
            jest.spyOn(axios, 'post').mockImplementation(post);
            const expected = {
                userInfo: { id: '1', email: '<EMAIL>' },
                sitemap: {},
                notifications: [] as any[],
                translations: {
                    '@sage/xtrem-standalone-test/key1': 'test-value-1',
                    '@sage/xtrem-standalone-test/key2': 'test-value-2',
                },
            };
            expect(await metadataService.fetchMetaData('en-US')).toEqual(expected);
        });
    });

    describe('fetchNotifications', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should return expected notifications result', async () => {
            const notifications = [
                {
                    _id: '1',
                    _createStamp: '2024-01-01T00:00:00.000Z',
                    actions: {
                        _id: '1',
                        link: '/test-link',
                        style: 'primary',
                        title: 'Test',
                    },
                    descriptions: 'This is the description for a test notification.',
                    icon: 'test-icon',
                    isRead: true,
                    level: 'info',
                    shouldDisplayToast: false,
                },
                {
                    _id: '2',
                    _createStamp: '2024-01-02T00:00:00.000Z',
                    actions: {
                        _id: '1',
                        link: '/test-link',
                        style: 'primary',
                        title: 'Test',
                    },
                    descriptions: 'This is the description for a test notification.',
                    icon: 'test-icon',
                    isRead: false,
                    level: 'warning',
                    shouldDisplayToast: true,
                },
            ];
            const post = jest.fn(() => Promise.resolve({ data: { data: { notifications } } }));
            jest.spyOn(axios, 'post').mockImplementation(post);
            expect(await metadataService.fetchNotifications('en-US')).toEqual(notifications);
        });
    });

    describe('notificationMarkRead', () => {
        it('should call matadata endpoint with expected data', async () => {
            const notificationId = '1';
            const locale = 'en-US';
            const post = jest.fn(() => Promise.resolve({ data: { data: { notifications: { markRead: true } } } }));
            jest.spyOn(axios, 'post').mockImplementation(post);
            await metadataService.notificationMarkRead(notificationId, locale);
            const data = { query: `mutation{notifications{markRead(_id:"1")}}` };
            const config = { headers: { 'Accept-Language': locale } };
            expect(post).toHaveBeenCalledWith('/metadata', data, config);
        });
    });

    describe('notificationMarkAllRead', () => {
        it('should call metadata endpoint with expected data', async () => {
            const locale = 'en-US';
            const post = jest.fn(() => Promise.resolve({ data: { data: { notifications: { markAllRead: true } } } }));
            jest.spyOn(axios, 'post').mockImplementation(post);
            await metadataService.notificationMarkAllRead(locale);
            const data = { query: `mutation{notifications{markAllRead()}}` };
            const config = { headers: { 'Accept-Language': locale } };
            expect(post).toHaveBeenCalledWith('/metadata', data, config);
        });
    });

    describe('notificationMarkUnread', () => {
        it('should call metadata endpoint with expected data', async () => {
            const notificationId = '1';
            const locale = 'en-US';
            const post = jest.fn(() => Promise.resolve({ data: { data: { notifications: { markUnread: true } } } }));
            jest.spyOn(axios, 'post').mockImplementation(post);
            await metadataService.notificationMarkUnread(notificationId, locale);
            const data = { query: `mutation{notifications{markUnread(_id:"1")}}` };
            const config = { headers: { 'Accept-Language': locale } };
            expect(post).toHaveBeenCalledWith('/metadata', data, config);
        });
    });

    describe('notificationDelete', () => {
        it('should call metadata endpoint with expected data', async () => {
            const notificationId = '1';
            const locale = 'en-US';
            const post = jest.fn(() => Promise.resolve({ data: { data: { notifications: { delete: true } } } }));
            jest.spyOn(axios, 'post').mockImplementation(post);
            await metadataService.notificationDelete(notificationId, locale);
            const data = { query: `mutation{notifications{delete(_id:"1")}}` };
            const config = { headers: { 'Accept-Language': locale } };
            expect(post).toHaveBeenCalledWith('/metadata', data, config);
        });
    });

    it('logPageVisit should call the server with the link that is being openned', async () => {
        const postMock = jest.spyOn(axios, 'post').mockResolvedValue({
            data: {
                data: {
                    xtremSystem: {
                        user: { logPageVisit: ['@sage/my-package/NewPageVisit', '@sage/my-package/PreviousPageVisit'] },
                    },
                },
            },
        });
        expect(postMock).not.toHaveBeenCalled();
        expect(await metadataService.logPageVisit('@sage/my-package/NewPageVisit')).toEqual([
            '@sage/my-package/NewPageVisit',
            '@sage/my-package/PreviousPageVisit',
        ]);
        expect(postMock).toHaveBeenCalledWith('/api', {
            query: 'mutation{xtremSystem{user{logPageVisit(path:"@sage/my-package/NewPageVisit")}}}',
        });
    });

    it('fetchAgGridLicence should get the licence and decode it if it is available', async () => {
        const getMock = jest.spyOn(axios, 'get').mockResolvedValue({
            data: { agGridLicenceKey: 'bXlsaWNlbmNldGVzdA==' },
        });
        expect(getMock).not.toHaveBeenCalled();
        expect(
            await metadataService.fetchStandaloneConfig({ email: '<EMAIL>', id: '223', tenantId: '1234' }),
        ).toEqual({ agGridLicenceKey: 'mylicencetest' });
        expect(getMock).toHaveBeenCalledWith('/standalone-config');
    });

    it('fetchAgGridLicence should get the licence and return null if it is not configured', async () => {
        const getMock = jest.spyOn(axios, 'get').mockResolvedValue({
            data: null,
        });
        expect(getMock).not.toHaveBeenCalled();
        expect(
            await metadataService.fetchStandaloneConfig({ email: '<EMAIL>', id: '223', tenantId: '1234' }),
        ).toEqual(null);
    });
});
