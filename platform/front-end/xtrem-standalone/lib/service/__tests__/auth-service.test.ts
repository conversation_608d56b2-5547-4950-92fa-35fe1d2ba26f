import { waitFor } from '@testing-library/dom';
import { Dict } from '@sage/xtrem-shared';
import {
    getCookies,
    getDecodedToken,
    getUserDetails,
    lockRenewal,
    RENEW_LOCK_KEY,
    shouldRenewToken,
    unlockRenewal,
} from '../auth-service';

const getMockCookie = (values: Dict<string>): string => {
    return Object.keys(values).reduce((prevValue, currentValue) => {
        return prevValue
            ? `${prevValue}; ${currentValue}=${values[currentValue]}`
            : `${currentValue}=${values[currentValue]}`;
    }, '');
};

describe('AuthService', () => {
    describe('getCookies', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should return a dictionary of values set in the cookie', () => {
            jest.spyOn(document, 'cookie', 'get').mockReturnValue(getMockCookie({ key1: 'value1', key2: 'value2' }));
            const cookies = getCookies();
            expect(cookies).toEqual({ key1: 'value1', key2: 'value2' });
        });
    });

    describe('getDecodedToken', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should decode JWT access token from cookies', () => {
            jest.spyOn(document, 'cookie', 'get').mockReturnValue(
                getMockCookie({
                    access_token:
                        'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SrJaP3yUA2PBytgwkbuBGjcqf6uckAjBMDe_WehlJRhof3KR5DiDFSLVa5lBAZRvJpsDpmWc3Ad0PO_6ij8LFQ',
                }),
            );
            const token = getDecodedToken();
            expect(token).toEqual({
                auth0: 'test-auth0|00000000-0000-4000-8000-000000000000',
                jti: 'test-jti',
                cluster: 'test-cluster',
                sub: '<EMAIL>',
                tenantId: '0123456789',
                mfaEnforced: true,
                locale: 'en-US',
                pref: 1,
                supportUserEmail: '<EMAIL>',
                supportUserName: 'John Doe',
                aud: 'https://sage-xtrem.com',
                iss: 'https://sso.sage-xtrem.com',
                iat: 0,
                exp: 9999999999999,
            });
        });
    });

    describe('getUserDetails', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should return user details from JWT access token', () => {
            jest.spyOn(document, 'cookie', 'get').mockReturnValue(
                getMockCookie({
                    access_token:
                        'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SrJaP3yUA2PBytgwkbuBGjcqf6uckAjBMDe_WehlJRhof3KR5DiDFSLVa5lBAZRvJpsDpmWc3Ad0PO_6ij8LFQ',
                }),
            );
            const userDetails = getUserDetails();
            expect(userDetails).toEqual({
                id: '<EMAIL>',
                locale: 'en-US',
                email: '<EMAIL>',
                tenantId: '0123456789',
                pref: 1,
                uuid: '00000000-0000-4000-8000-000000000000',
                isOperator: false,
            });
        });

        it('should return nulled user if JWT access token is falsy', () => {
            const userDetails = getUserDetails();
            expect(userDetails).toEqual({
                id: null,
                locale: null,
                email: null,
                uuid: null,
                isOperator: null,
            });
        });
    });

    describe('unlockRenewal', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it(`should call session storage's removeItem with expected key`, () => {
            const spy = jest.spyOn(Storage.prototype, 'removeItem');
            unlockRenewal();
            expect(spy).toHaveBeenCalledWith(RENEW_LOCK_KEY);
        });
    });

    describe('lockRenewal', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it(`should call session storage's setItem with expected key`, async () => {
            const setSpy = jest.spyOn(Storage.prototype, 'setItem');
            const removeSpy = jest.spyOn(Storage.prototype, 'removeItem');
            lockRenewal();
            expect(setSpy).toHaveBeenCalledWith(RENEW_LOCK_KEY, expect.any(String));

            await waitFor(
                () => {
                    expect(removeSpy).toHaveBeenCalledWith(RENEW_LOCK_KEY);
                },
                { timeout: 12000, interval: 1000 },
            );
        });
    });

    describe('shouldRenewToken', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should return true given access token expires within the next 2 minutes', () => {
            jest.spyOn(document, 'cookie', 'get').mockReturnValue(
                getMockCookie({
                    access_token:
                        'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Z25-iYP-y_Q11e68GU7YJmF-n86IPchnePhpnfYjIx98p3llWScIeDZ6WfvJXyl9Emev9xrKZeHQa8QVgqnZ3w',
                }),
            );
            expect(shouldRenewToken()).toBe(true);
        });

        it('should not renew token if access token is valid for more than 2 minutes', () => {
            jest.spyOn(document, 'cookie', 'get').mockReturnValue(
                getMockCookie({
                    access_token:
                        'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SrJaP3yUA2PBytgwkbuBGjcqf6uckAjBMDe_WehlJRhof3KR5DiDFSLVa5lBAZRvJpsDpmWc3Ad0PO_6ij8LFQ',
                }),
            );
            expect(shouldRenewToken()).toBe(false);
        });

        it('should not renew token if access token is falsy', () => {
            jest.spyOn(document, 'cookie', 'get').mockReturnValue(getMockCookie({ access_token: '' }));
            expect(shouldRenewToken()).toBe(false);
        });

        it('should not renew token if there is an active lock in place', () => {
            jest.spyOn(Storage.prototype, 'getItem').mockImplementation((key: string) => {
                if (key === RENEW_LOCK_KEY) {
                    return String(new Date().getTime() + 60_000);
                }
                return null;
            });
            jest.spyOn(document, 'cookie', 'get').mockReturnValue(
                getMockCookie({
                    access_token:
                        'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Z25-iYP-y_Q11e68GU7YJmF-n86IPchnePhpnfYjIx98p3llWScIeDZ6WfvJXyl9Emev9xrKZeHQa8QVgqnZ3w',
                }),
            );
            expect(shouldRenewToken()).toBe(false);
        });
    });
});
