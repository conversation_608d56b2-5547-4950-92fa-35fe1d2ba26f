import type { Dict, StandaloneConfig } from '@sage/xtrem-shared';
import axios from 'axios';
import type { UserDetails } from './auth-service';

export interface NavigationCategory {
    icon: string;
    title: string;
}

export interface XtremPageReference {
    key: string;
    title: string;
    category?: string;
}

export const fetchLoginServiceUrl = async (): Promise<string> => {
    return (await axios.get('/login-service')).data;
};

export const fetchStandaloneConfig = async (userDetails: Partial<UserDetails>): Promise<StandaloneConfig> => {
    const config = (await axios.get('/standalone-config')).data as StandaloneConfig;
    if (config?.agGridLicenceKey && userDetails.email && userDetails.tenantId) {
        config.agGridLicenceKey = atob(config.agGridLicenceKey);
    }

    return config;
};

export const logPageVisit = async (historyItem: string) => {
    const response = await axios.post('/api', {
        query: `mutation{xtremSystem{user{logPageVisit(path:"${historyItem}")}}}`,
    });

    return response.data.data.xtremSystem.user.logPageVisit;
};

export const fetchMetaData = async (locale: string) => {
    const response = await axios.post(
        '/metadata',
        {
            query: `{pages{key,title,category},userInfo{ email, userCode, firstName,lastName,photo,bookmarks,history,uniqueUserId,uniqueTenantId,clientEncryptionKey},
                strings(filter:{packageOrPage:"@sage/xtrem-standalone"}){
                    key
                    content
                }
                notifications {
                    _id
                    _createStamp
                    actions {
                        _id
                        link
                        style
                        title
                    }
                    description
                    icon
                    isRead
                    level
                    shouldDisplayToast
                    title
               }
               sitemap {
                  id
                  title
                  isPage
                  icon
                  priority
                  children {
                    id
                    title
                    isPage
                    priority
                    children {
                      title
                      id
                      isPage
                      priority
                      children {
                        title
                        id
                        isPage
                        priority
                        children {
                          title
                          id
                          isPage
                          priority
                          children {
                            title
                            id
                            isPage
                            priority
                            children {
                              title
                              id
                              isPage
                              priority
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }`,
        },
        {
            headers: {
                'Accept-Language': locale,
            },
        },
    );

    return {
        userInfo: response.data.data.userInfo,
        sitemap: response.data.data.sitemap,
        notifications: response.data.data.notifications,
        translations: response.data.data.strings.reduce(
            (prevValue: Dict<string>, currentValue: { key: string; content: string }) => {
                prevValue[currentValue.key] = currentValue.content;
                return prevValue;
            },
            {},
        ),
    };
};

export const fetchNotifications = async (locale: string) => {
    const response = await axios.post(
        '/metadata',
        {
            query: `{
                notifications {
                    _id
                    _createStamp
                    level
                    shouldDisplayToast
                    isRead
                    icon
                    title
                    description
                    actions {
                    _id
                    title
                    link
                    style
                    }
                }
            }`,
        },
        {
            headers: {
                'Accept-Language': locale,
            },
        },
    );

    return response.data.data.notifications;
};

export const notificationMarkRead = async (notificationId: string, locale: string): Promise<boolean> => {
    const response = await axios.post(
        '/metadata',
        {
            query: `mutation{notifications{markRead(_id:"${notificationId}")}}`,
        },
        {
            headers: {
                'Accept-Language': locale,
            },
        },
    );

    return response.data.data.notifications.markRead;
};

export const notificationMarkAllRead = async (locale: string): Promise<boolean> => {
    const response = await axios.post(
        '/metadata',
        {
            query: 'mutation{notifications{markAllRead()}}',
        },
        {
            headers: {
                'Accept-Language': locale,
            },
        },
    );

    return response.data.data.notifications.markAllRead;
};

export const notificationMarkUnread = async (notificationId: string, locale: string): Promise<boolean> => {
    const response = await axios.post(
        '/metadata',
        {
            query: `mutation{notifications{markUnread(_id:"${notificationId}")}}`,
        },
        {
            headers: {
                'Accept-Language': locale,
            },
        },
    );

    return response.data.data.notifications.markUnread;
};

export const notificationDelete = async (notificationId: string, locale: string): Promise<boolean> => {
    const response = await axios.post(
        '/metadata',
        {
            query: `mutation{notifications{delete(_id:"${notificationId}")}}`,
        },
        {
            headers: {
                'Accept-Language': locale,
            },
        },
    );

    return response.data.data.notifications.delete;
};
