import axios from 'axios';

/**
 * List of pages where the chatbot should be activated
 */
export const CHATBOT_SUPPORTED_PAGES = [
    '@sage/xtrem-master-data/Customer',
    '@sage/xtrem-show-case/ShowCaseProduct',
    '@sage/xtrem-sales/SalesOrder',
];

export interface ChatbotConfiguration {
    aiToken: { token: string; expiration: number } | null;
    accessCode: { code: string; expiration: number } | null;
    chatbotAccessCodeLifeTimeInMinutes?: number;
}

export const getAccessCode = async (
    loginServiceUrl: string,
    chatbotAccessCodeLifeTimeInMinutes: number,
): Promise<{ code: string; expiration: number } | null> => {
    const response = await axios.get(`${loginServiceUrl}/copilot`, { withCredentials: true });

    if (!response.data?.accessCode) {
        // eslint-disable-next-line no-console
        console.warn('Invalid chatbot access code received.');
        // eslint-disable-next-line no-console
        console.warn(response.data);
        return null;
    }

    const accessCodeExpiration = Date.now() + chatbotAccessCodeLifeTimeInMinutes * 60 * 1000;
    return {
        code: response.data.accessCode,
        expiration: accessCodeExpiration,
    };
};

export const fetchAiToken = async (): Promise<{ token: string; expiration: number; enabled: boolean } | null> => {
    // TODO: do we need to cache this to minimize the number of requests?
    const response = await axios.post('/metadata', {
        query: `{
                serviceSettings {
                    copilot {
                        token
                        expiration
                        enabled
                    }
                }
              }`,
    });

    if (!response.data?.data?.serviceSettings?.copilot?.token) {
        // eslint-disable-next-line no-console
        console.warn('Invalid gms token received.');
        // eslint-disable-next-line no-console
        console.warn(response.data);
        return null;
    }

    return {
        token: response.data.data.serviceSettings.copilot.token,
        expiration: Number(response.data.data.serviceSettings.copilot.expiration),
        enabled: response.data.data.serviceSettings.copilot.enabled,
    };
};

export async function fetchChatbotConfiguration(
    loginServiceUrl: string,
    chatbotAccessCodeLifeTimeInMinutes = 60,
): Promise<ChatbotConfiguration | null> {
    try {
        const aiToken = await fetchAiToken();
        if (!aiToken) {
            // eslint-disable-next-line no-console
            console.warn('Invalid chatbot token received.');
        }

        const accessCode = await getAccessCode(loginServiceUrl, chatbotAccessCodeLifeTimeInMinutes);

        if (!accessCode) {
            // eslint-disable-next-line no-console
            console.warn('Invalid chatbot access code received.');
            return null;
        }

        return {
            accessCode,
            aiToken,
            chatbotAccessCodeLifeTimeInMinutes,
        };
    } catch (err) {
        // eslint-disable-next-line no-console
        console.warn('Failed to fetch chatbot config');
        // eslint-disable-next-line no-console
        console.warn(err);
        return null;
    }
}
