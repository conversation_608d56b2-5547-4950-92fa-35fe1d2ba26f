import * as React from 'react';

import type { MockStoreEnhanced } from 'redux-mock-store';
import configureMockStore from 'redux-mock-store';
import reduxThunk from 'redux-thunk';
import * as actions from '../standalone-redux/actions';
import type { XtremStandaloneState } from '../standalone-redux/state';

jest.mock('@sage/xtrem-ui', () => ({ integration: { XtremUiIndex: jest.fn(() => <div>Xtrem UI Placeholder</div>) } }));

const mockReduxActions = () => {
    return Object.keys(actions).map(actionKey =>
        jest
            .spyOn(actions, actionKey as keyof typeof actions)
            .mockImplementation((): any => ({ type: 'TEST_REDUX_ACTION', value: undefined })),
    );
};

export const getMockState = (partialState: Partial<XtremStandaloneState> = {}): XtremStandaloneState => ({
    browser: { is: { xs: false } } as any,
    chatbotConfig: null,
    config: null,
    isApplicationDirty: false,
    isChatbotOpen: false,
    isNavigationOpen: false,
    isNotificationCenterOpen: false,
    loginService: null,
    menuItems: [],
    notifications: [],
    pageContext: null,
    pageTitle: null,
    path: '',
    preNavigationConfirmation: () => Promise.resolve(),
    sitemap: [],
    tenantsList: { oneTrustMagicLink: '', tenantList: [] },
    translations: {},
    user: null,
    ...partialState,
});

export const getMockStore = (
    storeState: XtremStandaloneState = getMockState(),
    shouldMockReduxActions = true,
): MockStoreEnhanced<XtremStandaloneState> => {
    const mockStoreCreator = configureMockStore<XtremStandaloneState>([reduxThunk]);
    if (shouldMockReduxActions) {
        mockReduxActions();
    }
    return mockStoreCreator(storeState);
};
