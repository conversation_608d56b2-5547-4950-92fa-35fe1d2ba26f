import { render } from '@testing-library/react';
import React from 'react';
import { ErrorBoundary } from '../error-boundary';

describe('ErrorBoundry', () => {
    it('should render children if no error is present', () => {
        const { getByText } = render(<ErrorBoundary>Hello World!</ErrorBoundary>);
        expect(getByText('Hello World!')).toBeInTheDocument();
    });

    it('should render error message if error is present', () => {
        const ErroredComponent = () => {
            throw new Error(`I'm a teapot - I can't brew coffee.`);
        };

        const { getByText } = render(
            <ErrorBoundary>
                <ErroredComponent />
            </ErrorBoundary>,
        );

        expect(getByText(`An error occurred while loading the page. Try again.`)).toBeInTheDocument();
        expect(getByText(`I'm a teapot - I can't brew coffee.`)).toBeInTheDocument();
    });
});
