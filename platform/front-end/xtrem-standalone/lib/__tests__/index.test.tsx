jest.mock('../component/header', () => ({
    ConnectedXtremHeader: () => <div data-testid="xtrem-header">Header placeholder</div>,
}));
jest.mock('../component/header', () => ({
    ConnectedXtremHeader: () => <div data-testid="xtrem-header">Header placeholder</div>,
}));
jest.mock('../component/mobile-navigation', () => ({
    ConnectedMobileNavigation: () => <div data-testid="xtrem-mobile-navigation">Mobile navigation placeholder</div>,
}));
jest.mock('../component/navigation', () => ({
    ConnectedXtremNavigation: () => <div data-testid="xtrem-mobile-navigation">Navigation placeholder</div>,
}));
jest.mock('@sage/xtrem-ui', () => ({
    integration: {
        XtremUiIndex: () => <div data-testid="xtrem-ui">Xtrem-UI placeholder</div>,
        DashboardRootComponent: () => <div data-testid="bms-dashboard">BMS dashboard placeholder</div>,
    },
}));
jest.mock('@sageai/gms-chat-ui-react', () => ({
    GmsChatWindow: (props: any) => <div data-testid="gms-chat-window">GmsChatWindow</div>,
}));
jest.mock('carbon-react/esm/hooks/useMediaQuery', () => ({ __esModule: true, default: (): boolean => false }));

import { render, cleanup, fireEvent } from '@testing-library/react';
import * as React from 'react';
import { Provider } from 'react-redux';
import { XtremStandalone, XtremStandaloneProps } from '../index';
import { getStore } from '../standalone-redux/store';

const windowLocationMock = jest.fn();

Object.defineProperty(window, 'location', {
    value: {
        replace: windowLocationMock,
    },
});
Object.defineProperty(window, 'matchMedia', {
    value: () => ({ matches: false, addEventListener: jest.fn() }),
});

const MINUTE = 60000;

describe('Index component', () => {
    let props: XtremStandaloneProps;
    beforeEach(() => {
        jest.useFakeTimers();
        windowLocationMock.mockReset();
        props = {
            config: {
                agGridLicenceKey: 'licenceKey',
                productName: 'Sage Distribution and Manufacturing Operations',
            },
            init: jest.fn(),
            isExtraSmall: false,
            loginService: 'http://login.service.com',
            onDirtyStatusChange: jest.fn(),
            onInternalNavigationChange: jest.fn(),
            setMenuItems: jest.fn(),
            updateNavStateBasedOnLocation: jest.fn(),
            setPageTitle: jest.fn(),
            setPageContext: jest.fn(),
            isNavigationOpen: false,
            path: '',
            user: {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                id: 'john.doe',
                userCode: 'john.doe',
                locale: 'en-US',
                tenantId: 'tenant1',
                pref: 5,
                uniqueTenantId: 'teant1id',
                uniqueUserId: 'johnDoeId',
                clientEncryptionKey: 'test',
                photo: null,
                uuid: null,
                isOperator: false,
            },
            translations: {},
        };
    });

    afterEach(() => {
        cleanup();
    });

    it('should render the component in loading mode', async () => {
        props.user = null;
        const { baseElement } = render(
            <Provider store={getStore()}>
                <XtremStandalone {...props} />
            </Provider>,
        );
        expect(baseElement.querySelector('.initial-loader-wrapper')).not.toBeNull();
        expect(baseElement.querySelector('.xe-body')).toBeNull();
    });

    it('should render the dashboard component if the user details are set but the path is empty', () => {
        const { baseElement, queryByTestId } = render(
            <Provider store={getStore()}>
                <XtremStandalone {...props} />
            </Provider>,
        );
        expect(baseElement.querySelector('.initial-loader-wrapper')).toBeNull();
        expect(baseElement.querySelector('.xe-body')).not.toBeNull();
        expect(queryByTestId('bms-dashboard')).not.toBeNull();
    });

    it('should not render the dashboard component if the user details and the path is set', () => {
        props.path = '@sage/xtrem-test/SomePage';
        const { baseElement, queryByTestId } = render(
            <Provider store={getStore()}>
                <XtremStandalone {...props} />
            </Provider>,
        );
        expect(baseElement.querySelector('.initial-loader-wrapper')).toBeNull();
        expect(baseElement.querySelector('.xe-body')).not.toBeNull();
        expect(queryByTestId('bms-dashboard')).toBeNull();
    });

    describe('idle user', () => {
        it('should log the user out after 60 minutes of inactivity', () => {
            render(
                <Provider store={getStore()}>
                    <XtremStandalone {...props} />
                </Provider>,
            );
            expect(windowLocationMock).not.toHaveBeenCalled();
            jest.advanceTimersByTime(59 * MINUTE);
            expect(windowLocationMock).not.toHaveBeenCalled();
            jest.advanceTimersByTime(2 * MINUTE);
            expect(windowLocationMock).toHaveBeenCalledWith(
                'http://login.service.com/logout?fromUrl=http://localhost/',
            );
        });

        it('should restart the timer when a key is pushed', () => {
            const { queryByTestId } = render(
                <Provider store={getStore()}>
                    <XtremStandalone {...props} />
                </Provider>,
            );
            const xtremUiElement = queryByTestId('xtrem-ui')!;
            expect(windowLocationMock).not.toHaveBeenCalled();
            fireEvent.keyDown(xtremUiElement);
            jest.advanceTimersByTime(45 * MINUTE);
            expect(windowLocationMock).not.toHaveBeenCalled();
            fireEvent.keyDown(xtremUiElement);
            jest.advanceTimersByTime(59 * MINUTE);
            expect(windowLocationMock).not.toHaveBeenCalled();
            jest.advanceTimersByTime(2 * MINUTE);
            expect(windowLocationMock).toHaveBeenCalledWith(
                'http://login.service.com/logout?fromUrl=http://localhost/',
            );
        });

        it('should restart the timer when the mouse is moved', () => {
            const { queryByTestId } = render(
                <Provider store={getStore()}>
                    <XtremStandalone {...props} />
                </Provider>,
            );
            const xtremUiElement = queryByTestId('xtrem-ui')!;
            expect(windowLocationMock).not.toHaveBeenCalled();
            fireEvent.mouseMove(xtremUiElement);
            jest.advanceTimersByTime(45 * MINUTE);
            expect(windowLocationMock).not.toHaveBeenCalled();
            fireEvent.mouseMove(xtremUiElement);
            jest.advanceTimersByTime(59 * MINUTE);
            expect(windowLocationMock).not.toHaveBeenCalled();
            jest.advanceTimersByTime(2 * MINUTE);
            expect(windowLocationMock).toHaveBeenCalledWith(
                'http://login.service.com/logout?fromUrl=http://localhost/',
            );
        });
    });
});
