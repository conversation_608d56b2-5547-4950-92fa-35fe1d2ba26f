import type { RootMenuItem } from '@sage/xtrem-shared';
import type { integration } from '@sage/xtrem-ui';
import { getInternalPathFromExternal } from '@sage/xtrem-ui-components';
import axios from 'axios';
import { getUserDetails } from '../service/auth-service';
import type { ChatbotConfiguration } from '../service/chatbot-service';
import { fetchAiToken, fetchChatbotConfiguration, getAccessCode } from '../service/chatbot-service';
import {
    fetchMetaData,
    fetchNotifications,
    fetchStandaloneConfig,
    notificationDelete,
    notificationMarkAllRead,
    notificationMarkRead,
    notificationMarkUnread,
} from '../service/metadata-service';
import { getNewRelicApi, getPendoApi } from '../service/telemetry-service';
import type { TenantDetails } from '../service/tenants-service';
import { fetchTenants } from '../service/tenants-service';
import type { AppThunkDispatch } from './action-types';
import { ActionType } from './action-types';
import type { XtremStandaloneState } from './state';
import { isDevMode } from './store';

export interface GmsNavigationEvent extends Event {
    detail: {
        target: string;
    };
}

export const setPath = (path: string) => ({ type: ActionType.SetPath, value: path });

export const setSitemap = (sitemap: RootMenuItem[]) => ({ type: ActionType.SetSitemap, value: sitemap });

export const setNavigationOpen = (isOpen: boolean) => ({ type: ActionType.SetNavigationOpen, value: isOpen });

export const setChatbotOpen = (isOpen: boolean) => {
    getPendoApi()?.track(isOpen ? 'chatbotOpened' : 'chatbotClosed');
    window.GmsChatUi?.setDisplayState(isOpen ? 'popup-right' : 'hidden');
    return { type: ActionType.SetChatbotOpen, value: isOpen };
};

export const setNotificationCenterOpen = (isOpen: boolean) => ({
    type: ActionType.SetNotificationCenterOpen,
    value: isOpen,
});

export const setMenuItems = (menuItems: integration.Menu[]) => ({ type: ActionType.SetMenuItems, value: menuItems });

export const setTenantsList = (tenantsList: TenantDetails[]) => ({
    type: ActionType.SetTenantsList,
    value: tenantsList,
});

export const actionStub = () => {
    throw new Error('Action stub called, did you forget to override the the action?');
};

export const onDirtyStatusChange = (isApplicationDirty: boolean, preNavigationConfirmation: () => Promise<void>) => ({
    type: ActionType.SetDirtyState,
    value: {
        isApplicationDirty,
        preNavigationConfirmation,
    },
});

export const onInternalNavigationChange =
    (path: string, doDirtyCheck = false) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremStandaloneState) => {
        const state = getState();
        if (doDirtyCheck && state.isApplicationDirty && state.preNavigationConfirmation) {
            try {
                await state.preNavigationConfirmation();
                dispatch({ type: ActionType.SetDirtyState, value: null });
            } catch {
                return Promise.reject();
            }
        }

        const pathDifferent = path !== state.path;

        if (pathDifferent) {
            const nextUrl = `/${path}`;
            window.history.pushState({ id: path }, `XTreeM ${path}`, nextUrl);
            dispatch(setPath(path));
            if (path) {
                dispatch(setSitemap(state.sitemap));
            }
        }
        return Promise.resolve();
    };

export const goHome = () => async (dispatch: AppThunkDispatch, getState: () => XtremStandaloneState) => {
    const path = '';
    const pathDifferent = path !== getState().path;
    window.document.title = `Sage ${getState().config?.productName || ''}`;
    if (pathDifferent) {
        await dispatch(onInternalNavigationChange('', true));
    }
};

export const updateNavStateBasedOnLocation =
    () => (dispatch: AppThunkDispatch, getState: () => XtremStandaloneState) => {
        const path = getInternalPathFromExternal();
        const pathDifferent = path !== getState().path;
        if (pathDifferent) {
            dispatch(setPath(path));
        }
    };

export const refreshAiToken = () => async (dispatch: AppThunkDispatch, getState: () => XtremStandaloneState) => {
    const result = await fetchAiToken();
    if (result) {
        const chatbotConfig: ChatbotConfiguration = { ...getState().chatbotConfig!, aiToken: result };
        dispatch({ type: ActionType.SetChatbotConfig, value: chatbotConfig });
        dispatch(scheduleRefreshAiToken());
    }
};

export const scheduleRefreshAiToken = () => (dispatch: AppThunkDispatch, getState: () => XtremStandaloneState) => {
    const state = getState();
    const aiTokenExpiration = state.chatbotConfig?.aiToken?.expiration;
    if (aiTokenExpiration) {
        setTimeout(() => {
            dispatch(refreshAiToken()).catch(() => {
                if (isDevMode()) {
                    // eslint-disable-next-line no-console
                    console.warn('Failed to refresh chatbot ai token');
                }
            });
        }, aiTokenExpiration - Date.now());
    }
};

export const refreshChatbotAccessToken =
    () => async (dispatch: AppThunkDispatch, getState: () => XtremStandaloneState) => {
        const result = await getAccessCode(
            getState().loginService!,
            getState().chatbotConfig?.chatbotAccessCodeLifeTimeInMinutes!,
        );
        if (result) {
            const chatbotConfig: ChatbotConfiguration = { ...getState().chatbotConfig!, accessCode: result };
            dispatch({ type: ActionType.SetChatbotConfig, value: chatbotConfig });
            dispatch(scheduleRefreshAiToken());
        }
    };

export const scheduleRefreshChatbotAccessToken =
    () => (dispatch: AppThunkDispatch, getState: () => XtremStandaloneState) => {
        const state = getState();
        const aiAccessTokenExpiration = state.chatbotConfig?.accessCode?.expiration;
        if (aiAccessTokenExpiration) {
            setTimeout(() => {
                dispatch(refreshChatbotAccessToken()).catch(() => {
                    if (isDevMode()) {
                        // eslint-disable-next-line no-console
                        console.warn('Failed to refresh chatbot access token');
                    }
                });
            }, aiAccessTokenExpiration - Date.now());
        }
    };

export const init = () => async (dispatch: AppThunkDispatch) => {
    let loginServiceUrl = '';
    try {
        const response = await axios.get('/login-service');
        loginServiceUrl = response.data;
        const userDetailsFromToken = getUserDetails();

        if (!userDetailsFromToken.id && !loginServiceUrl) {
            // eslint-disable-next-line no-console
            console.warn('Security config is missing, user context could not be identified.');
            // eslint-disable-next-line no-console
            console.warn('Please add a security config file for your deployment in order to access this page.');
        } else {
            if (userDetailsFromToken.locale) {
                document.documentElement.setAttribute('lang', userDetailsFromToken.locale);
            }
            const standaloneConfig = await fetchStandaloneConfig(userDetailsFromToken);
            dispatch({ type: ActionType.SetConfig, value: standaloneConfig });
            dispatch({ type: ActionType.SetLoginService, value: loginServiceUrl });
            const metadata = await fetchMetaData(userDetailsFromToken.locale!);
            const tenantsList = await fetchTenants(loginServiceUrl);
            // TODO: use standaloneConfig.chatbotAccessCodeLifeTimeInMinutes or chatbotConfig.accessCodeExpiration to setTimeout to refresh accessCode and accessCodeExpiration
            const chatbotConfig = await fetchChatbotConfiguration(
                loginServiceUrl,
                standaloneConfig.chatbotAccessCodeLifeTimeInMinutes,
            );
            if (isDevMode()) {
                // eslint-disable-next-line no-console
                console.log('Chatbot config:');
                // eslint-disable-next-line no-console
                console.log(chatbotConfig);
            }
            // Bitwise variable, convert it to binary so individual parts can be
            const consentOptions = Number(userDetailsFromToken.pref || 0)
                .toString(2)
                .padStart(3, '0');

            const {
                kind = '',
                countryCode = '',
                subscriptionType = '',
                tenantId = '',
                tenantName = '',
            } = tenantsList.tenantList.find(tenant => tenant.current === true) || {};

            let pendoHelpOnClickOutsideHandler: ((e: any) => void) | null = null;

            getPendoApi()?.initialize({
                visitor: {
                    hasanalyticsconsent: !!Number(consentOptions.charAt(1)),
                    hasmarketingconsent: !!Number(consentOptions.charAt(2)),
                    id: metadata.userInfo.uniqueUserId,
                    localeCode: userDetailsFromToken.locale!,
                },
                account: {
                    countryCode,
                    kind,
                    id: metadata.userInfo.uniqueTenantId,
                    pendoClusterTag: standaloneConfig.pendoClusterTag || null,
                    subscriptionType,
                    tenantId,
                    tenantName,
                },
                guides: {
                    // @ts-expect-error: @types/pendo-io-browser typings are incomplete
                    globalScripts: [
                        {
                            script(_: any, guide: any) {
                                pendoHelpOnClickOutsideHandler = e => {
                                    if (
                                        e?.target &&
                                        !e.target.closest('.xe-header #pendo-help') &&
                                        !e.target.closest('#pendo-resource-center-container')
                                    ) {
                                        getPendoApi()?.onGuideDismissed(guide.id);
                                    }
                                };
                                // @ts-expect-error: @types/pendo-io-browser typings are incomplete
                                getPendoApi()?.attachEvent(document, 'click', pendoHelpOnClickOutsideHandler);
                            },
                            unmounted() {
                                // @ts-expect-error: @types/pendo-io-browser typings are incomplete
                                getPendoApi()?.detachEvent(document, 'click', pendoHelpOnClickOutsideHandler);
                                pendoHelpOnClickOutsideHandler = null;
                            },
                            test(step: any) {
                                return step?.element?.matches('.xe-header #pendo-help');
                            },
                        },
                    ],
                },
            });

            getPendoApi()?.track('userViewportMetrics', {
                innerWidth: window.innerWidth,
                innerHeight: window.innerHeight,
                screenWidth: window.screen.width,
                screenHeight: window.screen.height,
                pixelRatio: window.devicePixelRatio || 1,
            });

            dispatch({
                type: ActionType.SetMetadata,
                value: {
                    translations: metadata.translations,
                    notifications: metadata.notifications ? metadata.notifications : [],
                    sitemap: metadata.sitemap,
                    userDetails: { ...userDetailsFromToken, ...metadata.userInfo },
                    tenantsList,
                    chatbotConfig,
                },
            });

            dispatch(scheduleRefreshAiToken());
            dispatch(scheduleRefreshChatbotAccessToken());
            document.addEventListener('gms-chat-navigate', (event: GmsNavigationEvent) => {
                const targetUrl = event.detail.target;
                const isInternalLink = targetUrl.startsWith(window.location.origin);
                if (isInternalLink) {
                    const internalPath = getInternalPathFromExternal(targetUrl);
                    // eslint-disable-next-line @typescript-eslint/no-floating-promises
                    dispatch(onInternalNavigationChange(internalPath));
                } else {
                    window.open(targetUrl, '_blank');
                }
            });
        }
    } catch (e) {
        // eslint-disable-next-line no-console
        console.error(e);
        getNewRelicApi()?.noticeError(e);
        if (loginServiceUrl) {
            window.location.replace(`${loginServiceUrl}/login?fromUrl=${document.location.href}`);
        }
    }
};

export const setPageTitle =
    (title: string | null) => (dispatch: AppThunkDispatch, getState: () => XtremStandaloneState) =>
        dispatch({ type: ActionType.SetPageTitle, value: title || `Sage ${getState().config?.productName || ''}` });

export const setPageContext = (pageContext: integration.PageContext | null) => (dispatch: AppThunkDispatch) => {
    dispatch({ type: ActionType.SetPageContext, value: pageContext });
    if (!pageContext) {
        return;
    }
    setTimeout(async () => {
        const result = await window.GmsChatUi?.fetchInsights('first-page');
        const insightCount = Array.isArray(result) ? result.length : result?.insights.length;
        pageContext?.onInsightCountUpdated?.(insightCount ?? 0);
    }, 2000);
};

export const refreshNotifications = () => async (dispatch: AppThunkDispatch, getState: () => XtremStandaloneState) => {
    const locale = getState()?.user?.locale || 'en-US';
    const notifications = await fetchNotifications(locale);
    dispatch({ type: ActionType.SetNotifications, value: notifications });
};

export const markNotificationAsRead =
    (notificationId: string) => async (_: AppThunkDispatch, getState: () => XtremStandaloneState) => {
        getPendoApi()?.track('markSingleNotificationsRead');

        try {
            const locale = getState()?.user?.locale || 'en-US';
            await notificationMarkRead(notificationId, locale);
        } catch (e) {
            // eslint-disable-next-line no-console
            console.error(e);
            getNewRelicApi()?.noticeError(e);
            return Promise.reject();
        }

        return Promise.resolve();
    };

export const markAllNotificationsAsRead = () => async (_: AppThunkDispatch, getState: () => XtremStandaloneState) => {
    try {
        getPendoApi()?.track('markAllNotificationsRead');
        const locale = getState()?.user?.locale || 'en-US';
        await notificationMarkAllRead(locale);
    } catch (e) {
        // eslint-disable-next-line no-console
        console.error(e);
        getNewRelicApi()?.noticeError(e);
        return Promise.reject();
    }

    return Promise.resolve();
};

export const markNotificationAsUnread =
    (notificationId: string) => async (_: AppThunkDispatch, getState: () => XtremStandaloneState) => {
        try {
            getPendoApi()?.track('markAllNotificationsUnread');
            const locale = getState()?.user?.locale || 'en-US';
            await notificationMarkUnread(notificationId, locale);
        } catch (e) {
            // eslint-disable-next-line no-console
            console.error(e);
            getNewRelicApi()?.noticeError(e);
            return Promise.reject();
        }

        return Promise.resolve();
    };

export const deleteNotification =
    (notificationId: string) => async (_: AppThunkDispatch, getState: () => XtremStandaloneState) => {
        try {
            getPendoApi()?.track('deleteNotification');
            const locale = getState()?.user?.locale || document.documentElement.getAttribute('lang') || 'en-US';
            await notificationDelete(notificationId, locale);
        } catch (e) {
            // eslint-disable-next-line no-console
            console.error(e);
            getNewRelicApi()?.noticeError(e);
            return Promise.reject();
        }

        return Promise.resolve();
    };
