import type { Dict, RootMenuItem, StandaloneConfig, Notification } from '@sage/xtrem-shared';
import type { integration } from '@sage/xtrem-ui';
import type { UserDetails } from '../service/auth-service';
import type { TenantItem } from '../service/tenants-service';
import type { ChatbotConfiguration } from '../service/chatbot-service';

export interface ReduxResponsive {
    is: ResponsiveTypes;
    greaterThan: ResponsiveTypes;
    lessThan: ResponsiveTypes;
    mediaType: string;
    orientation: string;
}

export interface ResponsiveTypes {
    xs: boolean;
    s: boolean;
    m: boolean;
    l: boolean;
}

export interface XtremStandaloneState {
    browser: ReduxResponsive;
    chatbotConfig: ChatbotConfiguration | null;
    config: StandaloneConfig | null;
    isApplicationDirty: boolean;
    isChatbotOpen: boolean;
    isNavigationOpen: boolean;
    isNotificationCenterOpen: boolean;
    loginService: string | null;
    menuItems: integration.Menu[];
    notifications: Notification[];
    pageContext: integration.PageContext | null;
    pageTitle: string | null;
    path: string;
    preNavigationConfirmation: (() => Promise<void>) | null;
    sitemap: RootMenuItem[];
    tenantsList: TenantItem;
    translations: Dict<string>;
    user: UserDetails | null;
}
