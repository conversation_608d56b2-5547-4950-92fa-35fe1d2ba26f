import type { ThunkAction, ThunkDispatch } from 'redux-thunk';
import type { XtremStandaloneState } from './state';

export interface AppThunkAction extends ThunkAction<void, XtremStandaloneState, void, AppAction> {}

export interface AppThunkDispatch extends ThunkDispatch<XtremStandaloneState, void, AppAction> {}

export type AppAction = { type: ActionType; value?: any };

export enum ActionType {
    SetChatbotConfig = 'SetChatbotConfig',
    SetChatbotOpen = 'SetChatbotOpen',
    SetConfig = 'SetConfig',
    SetDirtyState = 'SetDirtyState',
    SetLoginService = 'SetLoginService',
    SetMenuItems = 'SetMenuItems',
    SetMetadata = 'SetMetadata',
    SetNavigationOpen = 'SetNavigationOpen',
    SetNotificationCenterOpen = 'SetNotificationCenterOpen',
    SetNotifications = 'SetNotifications',
    SetPageContext = 'SetPageContext',
    SetPageTitle = 'SetPageTitle',
    SetPath = 'SetPath',
    SetProfile = 'SetProfile',
    SetSitemap = 'SetSitemap',
    SetTenantsList = 'SetTenantsList',
    SetUserDetails = 'SetUserDetails',
}
