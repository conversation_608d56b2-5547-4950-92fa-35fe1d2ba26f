jest.mock('@sage/xtrem-ui-components', () => ({
    getInternalPathFromExternal: jest.fn(() => 'browser/path'),
}));

jest.mock('../../service/metadata-service');

import { integration } from '@sage/xtrem-ui';
import { getMockState } from '../../__tests__/test-utils';
import * as metadataService from '../../service/metadata-service';
import * as actions from '../actions';

const notifications = [
    {
        _id: '1',
        _createStamp: '2024-01-01T00:00:00.000Z',
        actions: {
            _id: '1',
            link: '/test-link',
            style: 'primary',
            title: 'Test',
        },
        descriptions: 'This is the description for a test notification.',
        icon: 'test-icon',
        isRead: true,
        level: 'info',
        shouldDisplayToast: false,
    },
    {
        _id: '2',
        _createStamp: '2024-01-02T00:00:00.000Z',
        actions: {
            _id: '1',
            link: '/test-link',
            style: 'primary',
            title: 'Test',
        },
        descriptions: 'This is the description for a test notification.',
        icon: 'test-icon',
        isRead: false,
        level: 'warning',
        shouldDisplayToast: true,
    },
];

describe('actions', () => {
    beforeEach(() => {
        jest.restoreAllMocks();
    });

    it('should throw when  `actionStub` is called', () => {
        expect(actions.actionStub).toThrow('Action stub called, did you forget to override the the action?');
    });

    it('should create `setPath` action with a value', () => {
        const path = '/my/test/pathValue';
        expect(actions.setPath(path)).toEqual({ type: 'SetPath', value: path });
    });

    it('should create `setMenuItems` action with a value', () => {
        const newMenuItems = [{ id: 'id', title: 'Title' }] as integration.Menu[];
        expect(actions.setMenuItems(newMenuItems)).toEqual({ type: 'SetMenuItems', value: newMenuItems });
    });

    it('should create `SetTenantsList` action with a value', () => {
        const newTenantsList = [
            {
                current: true,
                directLoginUrl: 'connect.localhost.dev-sagextrem.com/login/tenant/777777777777777777777',
                kind: 'demo',
                lastLogin: '2020-12-09T11:50:53.297Z',
                tenantId: '777777777777777777777',
                tenantName: 'Acme-777777777777777777777',
                countryCode: 'ES',
                subscriptionType: 'internal',
            },
        ];
        expect(actions.setTenantsList(newTenantsList)).toEqual({ type: 'SetTenantsList', value: newTenantsList });
    });

    describe('goHome', () => {
        it('should set the path to an empty string when goHome is called', () => {
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(() => getMockState({ path: 'some/path' }));
            const onInternalNavigationChangeSpy = jest.spyOn(actions, 'onInternalNavigationChange');
            expect(onInternalNavigationChangeSpy).not.toHaveBeenCalled();
            actions.goHome()(dispatchMock, getStateMock);
            expect(onInternalNavigationChangeSpy).toHaveBeenCalledWith('', true);
        });

        it('should not set the path when goHome is called and already on the home page', () => {
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(getMockState);

            expect(dispatchMock).not.toHaveBeenCalled();
            actions.goHome()(dispatchMock, getStateMock);
            expect(dispatchMock).not.toHaveBeenCalled();
        });
    });

    describe('onInternalNavigationChange', () => {
        it('should update the path on onInternalNavigationChange', () => {
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(getMockState);
            const pushStateSpy = jest.spyOn(window.history, 'pushState');

            expect(dispatchMock).not.toHaveBeenCalled();
            actions.onInternalNavigationChange('the/new/path')(dispatchMock, getStateMock);
            expect(dispatchMock).toHaveBeenCalledWith({ type: 'SetPath', value: 'the/new/path' });
            expect(pushStateSpy).toHaveBeenCalled();
        });

        it('should not set the path when the navigation event targets the current path', () => {
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(() => getMockState({ path: 'some/path' }));
            const pushStateSpy = jest.spyOn(window.history, 'pushState');

            expect(dispatchMock).not.toHaveBeenCalled();
            actions.onInternalNavigationChange('some/path')(dispatchMock, getStateMock);
            expect(dispatchMock).not.toHaveBeenCalled();
            expect(pushStateSpy).not.toHaveBeenCalled();
        });
    });

    describe('updateNavStateBasedOnLocation', () => {
        it("should update the path when the browser's path is not the same as the state path", () => {
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(getMockState);

            expect(dispatchMock).not.toHaveBeenCalled();
            actions.updateNavStateBasedOnLocation()(dispatchMock, getStateMock);
            expect(dispatchMock).toHaveBeenCalledWith({ type: 'SetPath', value: 'browser/path' });
        });

        it("should dispatch any actions if the browser's path is the same as the state path", () => {
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(() => getMockState({ path: 'browser/path' }));

            expect(dispatchMock).not.toHaveBeenCalled();
            actions.updateNavStateBasedOnLocation()(dispatchMock, getStateMock);
            expect(dispatchMock).not.toHaveBeenCalled();
        });
    });

    describe('setPageTitle', () => {
        it('should dispatch SetPageTitle action type with provided title', () => {
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(
                () =>
                    ({
                        config: { productName: 'XtremStandalone' },
                    }) as any,
            );

            expect(dispatchMock).not.toHaveBeenCalled();
            actions.setPageTitle('Xtrem Standalone Test')(dispatchMock, getStateMock);
            expect(dispatchMock).toHaveBeenCalledWith({ type: 'SetPageTitle', value: 'Xtrem Standalone Test' });
        });

        it('should dispatch SetPageTitle action type with product name', () => {
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(
                () =>
                    ({
                        config: { productName: 'XtremStandalone' },
                    }) as any,
            );

            expect(dispatchMock).not.toHaveBeenCalled();
            actions.setPageTitle(null)(dispatchMock, getStateMock);
            expect(dispatchMock).toHaveBeenCalledWith({ type: 'SetPageTitle', value: 'Sage XtremStandalone' });
        });
    });

    describe('refreshNotifications', () => {
        afterEach(() => {
            jest.resetAllMocks();
        });

        it('should call fetch notifications from metadata service', async () => {
            const fetchNotificationsMock = jest.fn(() => Promise.resolve(notifications));
            jest.spyOn(metadataService, 'fetchNotifications').mockImplementation(fetchNotificationsMock);
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(
                () =>
                    ({
                        user: { locale: 'en-US' },
                    }) as any,
            );

            await actions.refreshNotifications()(dispatchMock, getStateMock);
            expect(fetchNotificationsMock).toHaveBeenCalledWith('en-US');
            expect(dispatchMock).toHaveBeenCalledWith({ type: 'SetNotifications', value: notifications });
        });
    });

    describe('markNotificationAsRead', () => {
        afterEach(() => {
            jest.resetAllMocks();
        });

        it(`should call metadata service's notificationMarkRead with expected data`, async () => {
            const notificationId = '1';
            const locale = 'en-US';
            const notificationMarkReadMock = jest.fn(() => Promise.resolve(true));
            jest.spyOn(metadataService, 'notificationMarkRead').mockImplementation(notificationMarkReadMock);
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(
                () =>
                    ({
                        user: { locale },
                    }) as any,
            );

            await actions.markNotificationAsRead(notificationId)(dispatchMock, getStateMock);
            expect(notificationMarkReadMock).toHaveBeenCalledWith(notificationId, locale);
        });

        it('should throw given invalid/failed call to notificationMarkRead', async () => {
            const notificationId = '1';
            const locale = 'en-US';
            const notificationMarkReadMock = jest.fn(() => Promise.reject());
            jest.spyOn(metadataService, 'notificationMarkRead').mockImplementation(notificationMarkReadMock);
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(
                () =>
                    ({
                        user: { locale },
                    }) as any,
            );

            try {
                await actions.markNotificationAsRead(notificationId)(dispatchMock, getStateMock);
                fail('Expected an error to be thrown');
            } catch {
                expect(notificationMarkReadMock).toHaveBeenCalledWith(notificationId, locale);
            }
        });
    });

    describe('markAllNotificationsAsRead', () => {
        afterEach(() => {
            jest.resetAllMocks();
        });

        it(`should call metadata service's notificationMarkAllRead with expected data`, async () => {
            const locale = 'en-US';
            const notificationMarkAllReadMock = jest.fn(() => Promise.resolve(true));
            jest.spyOn(metadataService, 'notificationMarkAllRead').mockImplementation(notificationMarkAllReadMock);
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(
                () =>
                    ({
                        user: { locale },
                    }) as any,
            );

            await actions.markAllNotificationsAsRead()(dispatchMock, getStateMock);
            expect(notificationMarkAllReadMock).toHaveBeenCalledWith(locale);
        });

        it('should throw given invalid/failed call to notificationMarkAllRead', async () => {
            const locale = 'en-US';
            const notificationMarkAllReadMock = jest.fn(() => Promise.reject());
            jest.spyOn(metadataService, 'notificationMarkAllRead').mockImplementation(notificationMarkAllReadMock);
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(
                () =>
                    ({
                        user: { locale },
                    }) as any,
            );

            try {
                await actions.markAllNotificationsAsRead()(dispatchMock, getStateMock);
                fail('Expected an error to be thrown');
            } catch {
                expect(notificationMarkAllReadMock).toHaveBeenCalledWith(locale);
            }
        });
    });

    describe('markNotificationAsUnread', () => {
        afterEach(() => {
            jest.resetAllMocks();
        });

        it(`should call metadata service's notificationMarkUnread with expected data`, async () => {
            const notificationId = '1';
            const locale = 'en-US';
            const notificationMarkUnreadMock = jest.fn(() => Promise.resolve(true));
            jest.spyOn(metadataService, 'notificationMarkUnread').mockImplementation(notificationMarkUnreadMock);
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(
                () =>
                    ({
                        user: { locale },
                    }) as any,
            );

            await actions.markNotificationAsUnread(notificationId)(dispatchMock, getStateMock);
            expect(notificationMarkUnreadMock).toHaveBeenCalledWith(notificationId, locale);
        });

        it('should throw given invalid/failed call to notificationMarkUnread', async () => {
            const notificationId = '1';
            const locale = 'en-US';
            const notificationMarkUnreadMock = jest.fn(() => Promise.reject());
            jest.spyOn(metadataService, 'notificationMarkUnread').mockImplementation(notificationMarkUnreadMock);
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(
                () =>
                    ({
                        user: { locale },
                    }) as any,
            );

            try {
                await actions.markNotificationAsUnread(notificationId)(dispatchMock, getStateMock);
                fail('Expected an error to be thrown');
            } catch {
                expect(notificationMarkUnreadMock).toHaveBeenCalledWith(notificationId, locale);
            }
        });
    });

    describe('deleteNotification', () => {
        afterEach(() => {
            jest.resetAllMocks();
        });

        it(`should call metadata service's deleteNotification with expected data`, async () => {
            const notificationId = '1';
            const locale = 'en-US';
            const notificationDeleteMock = jest.fn(() => Promise.resolve(true));
            jest.spyOn(metadataService, 'notificationDelete').mockImplementation(notificationDeleteMock);
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(
                () =>
                    ({
                        user: { locale },
                    }) as any,
            );

            await actions.deleteNotification(notificationId)(dispatchMock, getStateMock);
            expect(notificationDeleteMock).toHaveBeenCalledWith(notificationId, locale);
        });

        it('should throw given invalid/failed call to notificationMarkUnread', async () => {
            const notificationId = '1';
            const locale = 'en-US';
            const notificationDeleteMock = jest.fn(() => Promise.resolve(true));
            jest.spyOn(metadataService, 'notificationDelete').mockImplementation(notificationDeleteMock);
            const dispatchMock = jest.fn();
            const getStateMock = jest.fn(
                () =>
                    ({
                        user: { locale },
                    }) as any,
            );

            try {
                await actions.deleteNotification(notificationId)(dispatchMock, getStateMock);
                fail('Expected an error to be thrown');
            } catch {
                expect(notificationDeleteMock).toHaveBeenCalledWith(notificationId, locale);
            }
        });
    });
});
