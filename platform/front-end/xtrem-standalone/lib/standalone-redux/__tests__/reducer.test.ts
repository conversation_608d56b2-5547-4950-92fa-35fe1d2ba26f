import { integration } from '@sage/xtrem-ui';
import '../../__tests__/test-utils';
import { ActionType } from '../action-types';
import * as reducer from '../reducer';

const getActionsWithoutItem = (filterActions: ActionType[]) =>
    Object.keys(ActionType).filter(e => filterActions.indexOf(e as ActionType) === -1);

describe('reducer', () => {
    beforeEach(() => {
        jest.restoreAllMocks();
    });

    afterEach(() => {
        window.document.title = '';
    });

    describe('path', () => {
        it('should return the value of the setPath action', () => {
            expect(reducer.path(undefined, { type: ActionType.SetPath, value: 'my/new/path' })).toEqual('my/new/path');
        });

        it('should return the default path the profile is unset', () => {
            expect(reducer.path(undefined, { type: ActionType.SetProfile, value: null })).toEqual('');
        });

        it('should only trigger on the setPath event', () => {
            getActionsWithoutItem([ActionType.SetPath]).forEach(action => {
                const newState = reducer.path('my/current/path', {
                    type: action as ActionType,
                    value: 'my/updated/path',
                });
                expect(newState).toEqual('my/current/path');
            });
        });
    });

    describe('menuItems', () => {
        const nextMenuItems = [{ id: 'next', title: 'Next Title' }] as integration.Menu[];
        const currentMenuItems = [{ id: 'current', title: 'Current Title' }] as integration.Menu[];

        it('should return the value of the setMenuItems action', () => {
            expect(
                reducer.menuItems(currentMenuItems, { type: ActionType.SetMenuItems, value: nextMenuItems }),
            ).toEqual(nextMenuItems);
        });

        it('should return an empty array on the setProfile action', () => {
            expect(reducer.menuItems(currentMenuItems, { type: ActionType.SetProfile, value: nextMenuItems })).toEqual(
                [],
            );
        });

        it('should only trigger on the setMenuItems and setProfile event', () => {
            getActionsWithoutItem([ActionType.SetMenuItems, ActionType.SetProfile]).forEach(action => {
                const newState = reducer.menuItems(currentMenuItems, {
                    type: action as ActionType,
                    value: nextMenuItems,
                });
                expect(newState).toEqual(currentMenuItems);
            });
        });
    });

    describe('pageTitle', () => {
        it('should return the value of the SetPageTitle action', () => {
            expect(reducer.pageTitle('current title', { type: ActionType.SetPageTitle, value: 'next title' })).toEqual(
                'next title',
            );
        });

        it('should update the window title to contain the page title', () => {
            expect(window.document.title).toBeFalsy();
            reducer.pageTitle('current title', { type: ActionType.SetPageTitle, value: 'next title' });
            expect(window.document.title).toEqual('next title');
        });

        it('should reset the window title when the page title is emptied', () => {
            window.document.title = 'Some random title';
            reducer.pageTitle('current title', { type: ActionType.SetPageTitle, value: '' });
            expect(window.document.title).toEqual('');
        });

        it('should return the current state when navigating to another page', () => {
            expect(reducer.pageTitle('current title', { type: ActionType.SetPath, value: 'new/xtrem/page' })).toEqual(
                'current title',
            );
        });

        it('should only trigger on the setPath and setPageTitle events', () => {
            getActionsWithoutItem([ActionType.SetPath, ActionType.SetPageTitle]).forEach(action => {
                const newState = reducer.pageTitle('current title', {
                    type: action as ActionType,
                    value: 'next value',
                });
                expect(newState).toEqual('current title');
            });
        });
    });

    describe('tenants', () => {
        const newTenantsList = [
            {
                current: true,
                directLoginUrl: 'connect.localhost.dev-sagextrem.com/login/tenant/777777777777777777777',
                kind: 'demo',
                lastLogin: '2020-12-09T11:50:53.297Z',
                tenantId: '777777777777777777777',
                tenantName: 'Acme-777777777777777777777',
            },
        ];

        it('should return the value of the setTenantsList action', () => {
            expect(reducer.tenantsList(undefined, { type: ActionType.SetTenantsList, value: newTenantsList })).toEqual(
                newTenantsList,
            );
        });

        it('should return the value of the SetMetadata action', () => {
            expect(
                reducer.tenantsList(undefined, {
                    type: ActionType.SetMetadata,
                    value: { tenantsList: newTenantsList },
                }),
            ).toEqual(newTenantsList);
        });

        it('should return an empty array when tenants list is null', () => {
            expect(reducer.tenantsList(undefined, { type: ActionType.SetTenantsList, value: null }).tenantList).toEqual(
                [],
            );
        });
    });

    describe('translations', () => {
        const newTranslations = [
            {
                '@sage/demo/test-string': 'Hi',
                '@sage/demo/test-string-another': 'Stuff',
            },
        ];

        it('should return the value of the translations of setMetadata action', () => {
            expect(
                reducer.translations(undefined, {
                    type: ActionType.SetMetadata,
                    value: { translations: newTranslations },
                }),
            ).toEqual(newTranslations);
        });
    });

    describe('sitemap', () => {
        const fixtureSitemap = [
            {
                children: [
                    { id: '@sage/test/TestRecentPage2', isPage: true, priority: 23, title: 'Title 1' },
                    {
                        children: [
                            { id: '@sage/test/TestRecentPage1', isPage: true, priority: 322323, title: 'Title 2' },
                        ],
                        id: '@sage/test/AnotherCategory',
                        priority: 2,
                        title: 'Category 1',
                    },
                ],
                icon: 'analysis',
                id: '@sage/test/DemoCategory',
                priority: 32,
                title: 'Demo Category',
            },
        ];

        it('should return the value of the sitemap of setMetadata action', () => {
            expect(
                reducer.sitemap(undefined, { type: ActionType.SetMetadata, value: { sitemap: fixtureSitemap } }),
            ).toEqual(fixtureSitemap);
        });

        it('should return the value of the sitemap  action', () => {
            expect(reducer.sitemap(undefined, { type: ActionType.SetSitemap, value: fixtureSitemap })).toEqual(
                fixtureSitemap,
            );
        });
    });
});
