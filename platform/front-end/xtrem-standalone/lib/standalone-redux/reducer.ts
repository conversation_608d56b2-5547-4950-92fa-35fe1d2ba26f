/* eslint-disable @typescript-eslint/default-param-last */
import type { Notification, RootMenuItem, StandaloneConfig } from '@sage/xtrem-shared';
import { integration } from '@sage/xtrem-ui';
import { getInternalPathFromExternal } from '@sage/xtrem-ui-components';
import type { Reducer } from 'redux';
import { combineReducers } from 'redux';
import type { IBrowser } from 'redux-responsive';
import { createResponsiveStateReducer } from 'redux-responsive';
import type { UserDetails } from '../service/auth-service';
import type { ChatbotConfiguration } from '../service/chatbot-service';
import type { TenantItem } from '../service/tenants-service';
import type { AppAction } from './action-types';
import { ActionType } from './action-types';
import type { XtremStandaloneState } from './state';

export const path = (state: string = getInternalPathFromExternal(), action: AppAction) => {
    switch (action.type) {
        case ActionType.SetPath:
            return action.value;
        case ActionType.SetProfile:
            return action.value ? state : '';
        default:
            return state;
    }
};

export const config = (state: StandaloneConfig | null = null, action: AppAction) => {
    if (action.type === ActionType.SetConfig) {
        return action.value;
    }
    return state;
};

export const pageTitle = (state: string | null = null, action: AppAction) => {
    switch (action.type) {
        case ActionType.SetPageTitle:
            window.document.title = action.value;
            return action.value;
        default:
            return state;
    }
};

export const menuItems = (state: integration.Menu[] = [], action: AppAction) => {
    switch (action.type) {
        case ActionType.SetMenuItems:
            return action.value;
        case ActionType.SetProfile:
            return [];
        default:
            return state;
    }
};

export const user = (state: UserDetails | null = null, action: AppAction) => {
    if (action.type === ActionType.SetUserDetails) {
        return action.value;
    }
    if (action.type === ActionType.SetMetadata) {
        return action.value.userDetails;
    }
    return state;
};

export const notifications = (state: Notification[] = [], action: AppAction) => {
    if (action.type === ActionType.SetMetadata) {
        return action.value.notifications;
    }
    if (action.type === ActionType.SetNotifications) {
        return action.value;
    }
    return state;
};

export const loginService = (state: string | null = null, action: AppAction) => {
    if (action.type === ActionType.SetLoginService) {
        return action.value;
    }
    return state;
};

export const isNavigationOpen = (state = false, action: AppAction) => {
    if (action.type === ActionType.SetNavigationOpen) {
        return action.value;
    }
    return state;
};

export const isChatbotOpen = (state = false, action: AppAction) => {
    if (action.type === ActionType.SetChatbotOpen) {
        return action.value;
    }
    return state;
};

export const isNotificationCenterOpen = (state = false, action: AppAction) => {
    if (action.type === ActionType.SetNotificationCenterOpen) {
        return action.value;
    }
    return state;
};

export const isApplicationDirty = (state = false, action: AppAction) => {
    if (action.type === ActionType.SetDirtyState) {
        return action.value?.isApplicationDirty || false;
    }
    return state;
};

export const preNavigationConfirmation = (state: (() => Promise<void>) | null = null, action: AppAction) => {
    if (action.type === ActionType.SetDirtyState) {
        return action.value?.preNavigationConfirmation || null;
    }
    return state;
};

export const tenantsList = (state: TenantItem = { oneTrustMagicLink: '', tenantList: [] }, action: AppAction) => {
    if (action.type === ActionType.SetTenantsList) {
        return action.value ?? state;
    }
    if (action.type === ActionType.SetMetadata) {
        return action.value.tenantsList;
    }
    return state;
};

export const chatbotConfig = (state: ChatbotConfiguration | null = null, action: AppAction) => {
    if (action.type === ActionType.SetMetadata) {
        return action.value.chatbotConfig;
    }

    if (action.type === ActionType.SetChatbotConfig) {
        return action.value;
    }
    return state;
};

export const sitemap = (state: RootMenuItem[] = [], action: AppAction) => {
    if (action.type === ActionType.SetSitemap) {
        return action.value ?? state;
    }
    if (action.type === ActionType.SetMetadata) {
        return action.value.sitemap;
    }
    return state;
};

export const translations = (state = {}, action: AppAction) => {
    if (action.type === ActionType.SetMetadata) {
        return action.value.translations;
    }
    return state;
};

export const pageContext = (
    state: integration.PageContext | null = null,
    action: AppAction,
): integration.PageContext | null => {
    if (action.type === ActionType.SetPageContext) {
        return action.value;
    }
    return state;
};

export const browser: Reducer<IBrowser<integration.Breakpoints>> = createResponsiveStateReducer({
    ...integration.breakpoints,
});

export const getRootReducer = (): Reducer<XtremStandaloneState, AppAction> => {
    return combineReducers<XtremStandaloneState, AppAction>({
        browser,
        chatbotConfig,
        config,
        isApplicationDirty,
        isChatbotOpen,
        isNavigationOpen,
        isNotificationCenterOpen,
        loginService,
        menuItems,
        notifications,
        pageContext,
        pageTitle,
        path,
        preNavigationConfirmation,
        sitemap,
        tenantsList,
        translations,
        user,
    });
};
