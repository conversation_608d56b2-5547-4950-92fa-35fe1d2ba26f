import type { AnyAction, Dispatch, Middleware, MiddlewareAPI, Store } from 'redux';
import { applyMiddleware, compose, createStore } from 'redux';
import { responsiveStoreEnhancer } from 'redux-responsive';
import thunk from 'redux-thunk';
import type { AppAction } from './action-types';
import { getRootReducer } from './reducer';
import type { XtremStandaloneState } from './state';

export const isDevMode = () =>
    window.location.hostname === 'sdmo-xtrem.localhost.dev-sagextrem.com' || (window as any).DEBUGGING_XTREM;

const consoleFormat = 'color: green; font-weight: bold;';

const loggingMiddleWare: Middleware =
    ({ getState }: MiddlewareAPI<Dispatch<AnyAction>>) =>
    (next: Dispatch<AnyAction>) =>
    (action: AnyAction): AnyAction => {
        if (isDevMode()) {
            const id = Date.now();
            const state = getState();
            // eslint-disable-next-line no-console
            console.log('%cDispatching', consoleFormat, id, action);
            // eslint-disable-next-line no-console
            console.log('%cState before dispatch', consoleFormat, id, state);
            const returnValue = next(action);
            const updatedState = getState();
            // eslint-disable-next-line no-console
            console.log('%cState after dispatch', consoleFormat, id, updatedState);
            (window as any).__XTREM_STANDALONE_STATE = updatedState;
            return returnValue;
        }

        return next(action);
    };

export const composeEnhancers =
    (isDevMode() && window && (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__) || compose;

function configureStore(initialState: Partial<XtremStandaloneState> = {}) {
    const middleWares: Middleware[] = [thunk];

    middleWares.push(loggingMiddleWare);

    const enhancer = composeEnhancers(responsiveStoreEnhancer, applyMiddleware(...middleWares));
    return createStore<XtremStandaloneState, AppAction, {}, {}>(getRootReducer(), initialState as any, enhancer);
}

const store = configureStore();

export const getStore = (): Store<XtremStandaloneState, AppAction> => store;
