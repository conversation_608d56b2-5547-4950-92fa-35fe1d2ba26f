module.exports = {
    plugins: ['jsx-a11y', 'react'],
    extends: [
        '../../.eslintrc-base.js',
        'plugin:react/recommended',
        'plugin:jsx-a11y/strict',
        'plugin:react-hooks/recommended',
    ],
    ignorePatterns: ['*.test.ts', '*.test.tsx', 'lib/__tests__/test-helpers'],
    parserOptions: {
        tsconfigRootDir: __dirname,
        project: ['tsconfig.json', 'tsconfig.test.json'],
    },
    overrides: [
        {
            files: ['lib/**/*'],
            rules: {
                'no-console': 'error',
                '@typescript-eslint/consistent-type-imports': 'error',
                '@typescript-eslint/no-unused-expressions': 'error',
                'func-names': 'error',
                'react/jsx-props-no-spreading': 'off',
                'react/jsx-boolean-value': 'off',
                'react/require-default-props': 'off',
                'react/jsx-indent-props': ['error', 4],
                'react/jsx-indent': ['error', 4],
                'react/destructuring-assignment': 'off',
                'react/prop-types': 'off',
                'class-methods-use-this': 'off',
                'no-case-declarations': 'off',
                'jsx-a11y/no-static-element-interactions': 'off',
                'jsx-a11y/click-events-have-key-events': 'off',
                'jsx-a11y/alt-text': 'off',
                'jsx-a11y/label-has-for': 'off',
                'jsx-a11y/label-has-associated-control': 'off',
                'jsx-a11y/tabindex-no-positive': 'off',
                'jsx-a11y/no-noninteractive-tabindex': 'off',
                'react/sort-comp': 'warn',
                '@typescript-eslint/naming-convention': [
                    'error',
                    {
                        selector: 'variable',
                        format: ['camelCase', 'UPPER_CASE', 'PascalCase'],
                        filter: {
                            regex: '^_id|__collectionItem|__value$',
                            match: false,
                        },
                    },
                ],
                'react/no-access-state-in-setstate': 'error',
                'default-case': 'warn',
                'react/no-children-prop': 'warn',
                'no-useless-escape': 'error',
                'jsx-a11y/no-noninteractive-element-interactions': 'warn',
                'jsx-a11y/anchor-is-valid': 'warn',
                'import/no-named-as-default': 'off',
                'new-cap': 'warn',
                'react/display-name': 'warn',
                '@typescript-eslint/no-floating-promises': 'warn',

                // TO BE REVIEWED by front-end team
                'react/function-component-definition': 'warn',
                '@typescript-eslint/default-param-last': 'warn',
            },
        },
    ],
};
