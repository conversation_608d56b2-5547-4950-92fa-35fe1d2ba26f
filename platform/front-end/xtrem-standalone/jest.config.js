const esmOnlyPackages = [
    '@ag-grid-community',
    '@ag-grid-enterprise',
    '@react-dnd',
    'carbon-react',
    'core-dnd',
    'dnd-core',
    'react-dnd-html5-backend',
    'react-dnd',
];

module.exports = {
    roots: ['<rootDir>/lib'],
    moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
    transform: {
        '^.+\\.(ts|tsx|js|jsx|mjs)$': [
            'ts-jest',
            {
                diagnostics: true,
                tsconfig: 'tsconfig.test.json',
            },
        ],
    },
    testRegex: '(__tests__)(.*).test.ts[x]*$',
    verbose: true,
    testEnvironment: 'jsdom',
    reporters: ['default'],
    coverageProvider: 'v8',
    setupFilesAfterEnv: ['<rootDir>/lib/__tests__/jest-setup.ts'],
    collectCoverageFrom: ['<rootDir>/lib/**/*.{ts,tsx}'],
    testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/dev-resources/', '<rootDir>/webpack/'],
    coveragePathIgnorePatterns: [
        '.*__tests__.*',
        '.d.ts',
        '.-types.ts',
        '<rootDir>/node_modules/',
        '<rootDir>/dev-resources/',
        '<rootDir>/webpack/',
    ],
    transformIgnorePatterns: [
        '<rootDir>/node_modules/',
        '<rootDir>/dev-resources/',
        '<rootDir>/webpack/',
        `node_modules/(?!.pnpm|${esmOnlyPackages.join('|')})`,
    ],
    moduleDirectories: ['node_modules', '<rootDir>/../xtrem-ui-components'],
    moduleNameMapper: {
        '\\.(css|scss|svg)$': 'identity-obj-proxy',
        'carbon-react/esm/(.*)': '<rootDir>/node_modules/carbon-react/lib/$1',
    },
    fakeTimers: {
        enableGlobally: true,
    },
};
