'use strict';

const Webpack = require('webpack');
const WebpackNotifierPlugin = require('webpack-notifier');
const merge = require('webpack-merge');
const base = require('./base');
const path = require('path');
const rootDir = path.resolve(__dirname, '..');
const buildDir = path.resolve(rootDir, 'build');

module.exports = merge.merge(base, {
    devtool: 'source-map',
    mode: 'development',
    target: 'web',
    watchOptions: {
        ignored: /(i18n|node_modules)/,
    },
    cache: {
        type: 'filesystem',
        cacheDirectory: path.resolve(process.cwd(), '.temp_cache'),
    },
    plugins: [
        new WebpackNotifierPlugin({
            skipFirstNotification: true,
            alwaysNotify: true,
            sound: false,
        }),
        new Webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('development'),
            DEV_MODE: true,
            FEATURE_USER_CLIENT_CUSTOMIZATION: JSON.stringify(false),
        }),
    ],
});
