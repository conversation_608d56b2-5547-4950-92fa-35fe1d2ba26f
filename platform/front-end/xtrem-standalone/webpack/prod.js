'use strict';

const Webpack = require('webpack');
const merge = require('webpack-merge');
const TerserPlugin = require('terser-webpack-plugin');
const base = require('./base');

module.exports = merge.merge(base, {
    devtool: false,
    mode: 'production',
    plugins: [
        new Webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('production'),
            DEV_MODE: false,
            FEATURE_USER_CLIENT_CUSTOMIZATION: JSON.stringify(true),
        }),
        new Webpack.BannerPlugin({
            banner: `Copyright (c) 2020-${new Date().getFullYear()} The Sage Group plc or its licensors. Sage, Sage logos, and Sage product and service names mentioned herein are the trademarks of Sage Global Services Limited or its licensors. All other trademarks are the property of their respective owners. */`,
        }),
    ],
    optimization: {
        minimize: true,
        minimizer: [
            new TerserPlugin({
                extractComments: false,
                terserOptions: {
                    keep_classnames: true,
                    keep_fnames: true,
                },
            }),
        ],
    },
});
