const path = require('path');
const rootDir = path.resolve(__dirname, '..');
const buildDir = path.resolve(rootDir, 'build');
const xtremUiDir = path.resolve(rootDir, './node_modules/@sage/xtrem-ui/build');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const Webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const templateFn = require('adjust-sourcemap-loader').moduleFilenameTemplate({
    format: 'projectRelative',
});
const timestamp = new Date().toISOString();
const WebpackBar = require('webpackbar');
const xtremi18n = require('@sage/xtrem-cli-transformers');
const { copyStaticResources } = require('@sage/xtrem-static-shared');
const HtmlWebpackPlugin = require('html-webpack-plugin');

if (process.env.SKIP_CLIENT) {
    console.log('SKIP_CLIENT environment variable detected, stopping webpack bundle build.');
    process.exit(0);
}

console.log('Build target dir:', buildDir);
console.log('xtrem-ui build dir:', xtremUiDir);

const cMapsDir = path.resolve(xtremUiDir, 'cmaps');
const standardFontsDir = path.resolve(xtremUiDir, 'standard_fonts');
const pdfWorker = path.resolve(xtremUiDir, 'pdf.worker.min.mjs');

const WEBPACK_NONCE = '{{nonce}}';

module.exports = {
    entry: './lib/index.tsx',
    context: rootDir,
    target: 'web',
    ignoreWarnings: [/export .* was not found in|Can't resolve 'fs'/],
    output: {
        filename: 'xtrem.[contenthash].js',
        path: buildDir,
        pathinfo: true,
        publicPath: '/',
        devtoolModuleFilenameTemplate: templateFn,
        devtoolFallbackModuleFilenameTemplate: templateFn,
    },
    resolve: {
        symlinks: true,
        alias: {
            'carbon-react': path.resolve(rootDir, 'node_modules/carbon-react/'),
            react: path.resolve(rootDir, 'node_modules/react/'),
            handlebars: 'handlebars/dist/handlebars.min.js',
            timers: 'timers-browserify',
            stream: 'stream-browserify',
            crypto: 'crypto-browserify',
            'react-dom/server': path.resolve(rootDir, 'node_modules/react-dom/server.js'),
        },
        extensions: ['.ts', '.tsx', '.js', '.json', '.scss', '.css'],
        fallback: {
            path: false,
            buffer: false,
            events: false,
        },
    },
    plugins: [
        new Webpack.NormalModuleReplacementPlugin(
            /[/\\]theme[/\\].+\.css$/,
            path.resolve(path.join(rootDir, 'webpack', 'empty.css')),
        ),
        new Webpack.DefinePlugin({
            'global.__webpack_nonce__': JSON.stringify(WEBPACK_NONCE),
        }),
        new HtmlWebpackPlugin({
            template: path.resolve(rootDir + '/lib/index.html'),
            cache: false,
            inject: false,
            hash: true,
        }),
        new Webpack.NormalModuleReplacementPlugin(/@sage\/xtrem-i18n/, path.resolve(rootDir, 'empty-module.js')),
        // shows progress bar
        new WebpackBar(),
        new MiniCssExtractPlugin({
            filename: 'xtrem.[contenthash].css',
        }),
        new Webpack.BannerPlugin({
            banner: 'Built on ' + timestamp,
        }),
        new CopyWebpackPlugin({
            patterns: [
                {
                    from: cMapsDir,
                    to: path.resolve(buildDir, 'cmaps')
                },
                {
                    from: standardFontsDir,
                    to: path.resolve(buildDir, 'standard_fonts')
                },
                {
                    from: pdfWorker,
                    to: path.resolve(buildDir, 'pdf.worker.min.mjs')
                },
                {
                    from: path.resolve(rootDir, 'lib/i18n'),
                    to: path.resolve(buildDir, 'lib/i18n'),
                },
                {
                    from: path.join(xtremUiDir, 'images'),
                    to: path.resolve(buildDir, 'images'),
                },
                {
                    from: path.join(xtremUiDir, 'success.wav'),
                    to: path.resolve(buildDir),
                },
                {
                    from: path.join(xtremUiDir, 'error.wav'),
                    to: path.resolve(buildDir),
                },
                {
                    from: path.join(xtremUiDir, 'async-loader-animation.webm'),
                    to: path.resolve(buildDir),
                },
                {
                    from: require.resolve('axe-core/axe.min.js'),
                    to: path.resolve(buildDir + '/axe.min.js'),
                },
            ],
        }),
        copyStaticResources(buildDir),
        new MiniCssExtractPlugin({
            filename: '[name].[contenthash].css',
        }),
        {
            apply: compiler => {
                compiler.hooks.afterEmit.tap('AfterEmitPlugin', () => {
                    xtremi18n.mergeTranslationFiles(rootDir);
                });
            },
        },
    ],
    module: {
        rules: [
            {
                test: /\.svg$/,
                use: [
                    {
                        loader: 'svg-inline-loader',
                        options: {
                            removeTags: true,
                            removingTags: ['title', 'desc'],
                            removeSVGTagAttrs: false,
                        },
                    },
                ],
            },
            {
                test: /\.scss$/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader,
                    },
                    {
                        loader: 'css-loader', // 3. Translates CSS into CommonJS
                        options: {
                            url: {
                                filter: (url, resourcePath) => {
                                    // Exclude URLs starting with '/absolute/path'
                                    if (url.startsWith('/')) {
                                        return false;
                                    }
                                    return true;
                                },
                            },
                        },
                    },
                    {
                        loader: 'resolve-url-loader', // 2. Resolves relative paths in url() statements based on the original source file
                    },
                    {
                        loader: 'sass-loader', // 1. compiles Sass to CSS
                        options: {
                            sourceMap: true, // needed for 'resolve-url-loader' to work
                            sassOptions: {
                                exclude: [/node_modules/],
                                sourceMapContents: false, // needed for 'resolve-url-loader' to work
                            },
                        },
                    },
                ],
            },
            {
                test: /\.(png|woff|woff2|eot|ttf)$/,
                loader: 'url-loader',
                options: {
                    limit: 100000,
                },
            },
            {
                test: /\.tsx?$/,
                loader: 'ts-loader',
                options: {
                    transpileOnly: false,
                    getCustomTransformers: () => ({
                        before: [xtremi18n.messageTransformer],
                    }),
                },
            },
            {
                test: /\.css$/,
                use: ['style-loader', 'css-loader'],
            },
            // All output '.js' files will have any sourcemaps re-processed by 'source-map-loader'.
            {
                enforce: 'pre',
                exclude: [/node_modules/],
                test: /\.js$ /,
                loader: 'source-map-loader',
            },
        ],
    },
};
