PATH: XTREEM/Development+documentation/Client+Framework

# xtrem-ui

**Welcome to the official rendering engine of the Xtrem project!** :tada::rocket:

xtrem-ui is meant to define Xtrem screens layout (which, where and using what data fields get rendered) as well as the behavior of elements (what happens when clicking a button, changing a text, etc.). See the following picture to get a better idea:

![Example screen](./example-screen.png)

And here is what the previous screen definition looks like. We know, it feels overwhelming the first time. Keep reading this document and you will be comfortable with the framework in a blink of an eye!

```typescript
import * as ui from '@sage/xtrem-ui';
import { GraphApi } from '@sage/x3-products-api';

@ui.decorators.page<Product>({
    authorizationCode: 'ESOPRO',
    title: 'Products',
    node: '@sage/x3-products/Product',
    navigationPanel: {
        listItem: {
            image: ui.nestedFields.image({ bind: 'image' }),
            titleLine: ui.nestedFields.text({ bind: 'description1' }),
            line2: ui.nestedFields.text({ bind: 'code' }),
        },
    },
})
export class Product extends ui.Page<GraphApi> {
    @ui.decorators.section<Product>({
        title: 'Order Details',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<Product>({
        parent() {
            return this.mainSection;
        },
    })
    orderDetailsBlock: ui.containers.Block;

    @ui.decorators.block<Product>({
        title: 'Product Image',
        parent() {
            return this.mainSection;
        },
    })
    imageBlock: ui.containers.Block;

    @ui.decorators.textField<Product>({
        title: 'Product Code',
        isReadOnly: true,
        parent() {
            return this.orderDetailsBlock;
        },
    })
    code: ui.fields.Text;

    @ui.decorators.textField<Product>({
        title: 'Description',
        isReadOnly: true,
        parent() {
            return this.orderDetailsBlock;
        },
    })
    description1: ui.fields.Text;

    @ui.decorators.textField<Product>({
        title: 'Accounting Code',
        isReadOnly: true,
        parent() {
            return this.orderDetailsBlock;
        },
    })
    accountingCode: ui.fields.Text;

    @ui.decorators.imageField<Product>({
        width: '200px',
        parent() {
            return this.imageBlock;
        },
    })
    image: ui.fields.Image;

    @ui.decorators.barCodeField<Product>({
        parent() {
            return this.orderDetailsBlock;
        },
    })
    upc: ui.fields.BarCode;
}
```

## Setting up the project

If you are about to use xtrem-ui is very likely that you are developing for X3 (former Enterprise Management, thus [em-core](https://github.com/Sage-ERP-X3/em-core) repository). If that is the case then you are lucky :sunglasses: Most of the stuff has already been sorted out for you. We built the [xtrem-cli](https://github.com/Sage-ERP-X3/etna/tree/master/%40sage/xtrem-cli) command line utility to get you covered. These are the most common things you will be doing, but take a closer look at xtrem-cli to know what provides out of the box.

-   Create a new module: Run `xtrem init` if you need to add a new module to the em-core repository
-   Create a new page: There is no xtrem-cli need for that. Just go ahead and create a new file in the pages folder, which you will then export from the `index.ts` file. Take a look at [Pages structure](#pages-structure) section for more details
-   Compile a page: xtrem-cli compiles all the artifacts in a module by running `xtrem compile`. You can speed up the compilation by skipping some of the artifacts (e.g. providing the `--skip-server` argument if you are only working on client pages)
-   Start a module: Run `xtrem start` to get your module deployed at port 8240 (you will find your pages at a URL similar to http://localhost/@sage/x3-show-case/TextField). You can tell xtrem-cli to watch for changes in the pages by using the `--watch-client` argument
-   Test a page: xtrem-cli runs the tests for a whole module through `xtrem test`. You can also run the cucumber integration tests by passing the `--integration` argument (you will need to install imagemagik to run the visual regression tests; https://sourceforge.net/projects/graphicsmagick/files for Windows, `TODO` for Mac)

If you are about to work in a **non-X3 project** there is a few more things you will have to care about. This readme file does not cover them now, but don't give up yet! Contact us and we will be happy to assist you (and add the corresponding information to this file afterwards).

## Pages structure

xtrem-ui uses two ECMAScript 2015 features to define screens: classes and import/export statements. The first thing you need to do when creating a new page is to export a class from your page file. Make sure to name the class using [Upper camel case](https://en.wikipedia.org/wiki/Camel_case) and the file using [Kebab case](https://en.wikipedia.org/wiki/Kebab_case):

_your-page.ts_

```typescript
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<YourPage>({
    authorizationCode: 'CODE',
})
export class YourPage extends ui.Page {}
```

Notice the `extends ui.Page` statement? Your page needs to extend the ui.Page base class for the framework to understand it actually is an screen. Also, it will allow you to access page utilities such as the developer API (the \$ object). Finally, before start adding fields to your page you will need to mark your class as an xtrem-ui page using [typescript decorators](https://www.typescriptlang.org/docs/handbook/decorators.html).

Decorators are a mechanism to modify class declarations and xtrem-ui uses them to initialize pages and fields with specific values so you don't need to worry about it. All xtrem-ui decorators are available through `ui.decorators` and they are always used in the same way: they receive the class name as the generic type (the `<YourPage>` part) and a properties object between parenthesis. For more details about the decorator properties check the xtrem-ui [API documentation](http://ptf-ci-master2.sagefr.adinternal.com:8080/job/etna/job/xtrem-master/job/master/lastSuccessfulBuild/Etna_20UI_20API_20Reference/globals.html) (for instance, [page decorator properties](http://ptf-ci-master2.sagefr.adinternal.com:8080/job/etna/job/xtrem-master/job/master/lastSuccessfulBuild/Etna_20UI_20API_20Reference/interfaces/root.pagedecoratorproperties.html)).

The last step in order to make the screen accessible from the rest of the project is to export it from the `lib/pages/index.ts` file.

_index.ts_

```typescript
export { YourPage } from './your-field';
```

At this point you can view the newly added empty page from a browser by compiling and starting the module and browsing https://localhost:8240/@sage/(module-name)/YourPage:

```bash
# This pnpm scripts are defined on any em-core module
# You can use the equivalent xtrem-cli commands on other projects

pnpm run build   # xtrem compile
pnpm start       # xtrem start
```

An empty page is not very useful, so let's add some components into it! To render any component in a screen we only need to add a property to the class, give it a type and mark it with the corresponding decorator. This is how we would add a button to your screen:

```typescript
// ...
export class YourPage extends ui.Page {
    @ui.decorators.buttonField<YourPage>({})
    yourButton: ui.fields.Button;
}
```

_Trobuleshooting: The type of a property must match the type of the property decorator. Using `ui.containers.Block` type along with `ui.decorators.Tile` decorator for a property named `whatever` will turn into an error message similar to `the "whatever" property is misconfigured because the decorator type "ui.decorators.Tile" doesn't match the "ui.containers.Block" property type`._

If you go and try that out, you will see no button in the screen. This is due to the way xtrem-ui pages are structured: fields must be rendered inside blocks, and blocks must be rendered inside sections. Sections are the white boxes you see in the browser and all the sections in a page will be rendered in order of declaration (first section in the browser will be the first property of type section declared in the class).

The same logic applies for blocks and fields: the upper they are placed in the class file, the sooner they will be rendered inside their parent. The difference between sections and blocks/fields is that the later must specify a parent element in ordered to be rendered inside a container. Take a look at the following example to get a better understanding. Notice that all decorators can receive properties to customize the screen components.

_your-page.ts_

```typescript
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<YourPage>({
    authorizationCode: 'CODE',
})
export class YourPage extends ui.Page {
    @ui.decorators.section<YourPage>({
        title: 'First section',
    })
    firstSection: ui.containers.Section;

    @ui.decorators.block<YourPage>({
        title: 'First block',
        parent() {
            return this.firstSection;
        },
    })
    firstBlock: ui.containers.Block;

    @ui.decorators.labelField<YourPage>({
        parent() {
            return this.firstBlock;
        },
        map(value) {
            return 'Hardcoded value';
        },
    })
    labelField: ui.fields.Label;

    @ui.decorators.block<YourPage>({
        parent() {
            return this.firstSection;
        },
    })
    secondBlock: ui.containers.Block;

    @ui.decorators.section<YourPage>({
        title: 'Second section',
    })
    secondSection: ui.containers.Section;

    @ui.decorators.block<YourPage>({
        title: 'Third block',
        parent() {
            return this.secondSection;
        },
    })
    thirdBlock: ui.containers.Block;
}
```

![Example screen](./screen-layout.png)

You can mix sections, blocks and fields attributes as you like within the class. The order applies only between properties of the same category. If you choose not to provide a parent (or if you forget to), the field won't be rendered in the screen but still will be available programmatically.

You are now ready to start creating pages :champagne: See the next section to discover how to fill pages with data.

## Filling the fields with data

xtrem-ui supports two scenarios when it comes to representing data: pages that represent a database record and pages that do not map to a database record (e.g. dashboards, computed values, administration screens, etc.). We name the later **transient pages** to differentiate them from the former (we put a specific name to the second type because the first type of page is more common in a ERP software, so those are the regular pages or **non-transient pages**).

### Filling non-transient pages

Non-transient pages represent a database record (also a GraphQL node in the xtrem context). For xtrem-ui to know which node the page is mapped to, non-transient pages must specify a node property in their decorators. Make sure the provided value follows the `'(vendorName)/(moduleName)/(nodeName)'` convention given that xtrem-ui relays on it to resolve the corresponding GraphQL node:

```typescript
@ui.decorators.page<YourPage>({
    authorizationCode: 'CODE',
    node: '@sage/x3-products/Product',
})
export class YourPage extends ui.Page {}
```

Once you have set a node for your page, xtrem-ui will automatically try to fetch the value for each field of the page every time the page is loaded, using the name of that field as the property of the GraphQL node. For example, we can display the product code in a text field by creating a class attribute named `code`:

```typescript
@ui.decorators.page<YourPage>({
    authorizationCode: 'CODE',
    node: '@sage/x3-products/Product',
})
export class YourPage extends ui.Page {
    // Define the sections and blocks you need

    @ui.decorators.textField<Product>({
        title: 'Product code',
        parent() {
            // Specify a parent container where to render the code field
        },
    })
    code: ui.fields.Text;
}
```

_Troubleshooting: If you name a class attribute with anything that is not a valid property on the GraphQL node, the automatic GraphQL query launched in the page load will fail, causing the page to break. The failed network request comes with a very useful message in this cases (Cannot query field \"XXXX\" on type \"YYYY\". Did you mean \"ZZZZ\" or \"QQQQ\"?)._

There are a couple of properties that come handy when dealing with fields data fetching:

-   isTransient: If you mark a field as transient it will not be requested in the screen GraphQL initial query. You can use transient fields for computed values or to display data which is not connected to the page GraphQL node
-   bind: The value provided as the bind property will be used instead of the class attribute name in the GraphQL queries. When using this property, any name is valid for the class attribute

### Filling transient pages

Transient pages fields must be filled manually. See the [Querying the GraphQL API](#Querying-the-GraphQL-API) section to get more details on how get data from the server. **A page is marked as transient setting the** `isTransient` **decorator property to** `true`.

## Querying the GraphQL API

xtrem-ui pages come with some utilities to interact with the framework: you will find them all in the `this.$` object available in the pages scope. One of these utilities is the `graph` object, which provides methods to query the GraphQL API as well as posting data back to the server (called mutations in the GraphQL terminology).

The following example gets the fields \_id, code, localizedDescription1 and productCategory of the first ten products sorted by ascending stock volume:

```typescript
    @ui.decorators.buttonField<YourPage>({
        parent() {
            // Specify a parent container where to render the code field
        },
        isTransient: true,
        onClick() {
            this.$.graph
                .node('@sage/x3-products/Product')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            code: true,
                            localizedDescription1: true,
                            productCategory: true,
                        },
                        {
                            first: 10,
                            orderBy: {
                                stockVolume: 1
                            },
                        },
                    ),
                )
                .execute()
                .then(response => {
                    // The response will contain an array of objects called edges
                    // Each of the these objects contains a node property representing the queried node
                    const firstProduct = response.edges[0].node;
                    // Here you can use the first product information to update the screen fields
                })
                .catch((error: any) => {
                    // Take care of this unlucky error. E.g. Show a toast to the user
                    this.$.showToast('Something went wrong');
                });
        },
    })
    addLineReference: ui.fields.Reference;
```

When it comes to mutations xtrem provides a set of default operations that should have you covered in most of the cases. Those ones can only be used from **non-transient** pages though, because they assume that all the fields in page belong to the same database record. Using them in **transient pages will raise an exception**. There are three operations: create, update and delete.

```typescript
@ui.decorators.buttonField<YourPage>({
    async onClick() {
        await this.$.graph.create();
    },
})
createAction: ui.field.Button;

@ui.decorators.buttonField<YourPage>({
    async onClick() {
        await this.$.graph.update();
    },
})
updateAction: ui.field.Button;

@ui.decorators.buttonField<YourPage>({
    async onClick() {
        await this.$.graph.delete();
    },
})
deleteAction: ui.field.Button;
```

Raw query: TODO

### Type-safety on Graphql API queries

TODO

## Mocking data for development

Alasql

## Passing parameters between screens

queryParameters

## Adding a Navigation panel

navigationPanel property

## Stickers, what are those?

TODO
