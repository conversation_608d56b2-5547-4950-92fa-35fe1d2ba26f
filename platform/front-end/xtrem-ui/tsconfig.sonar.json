{"compilerOptions": {"outDir": "build", "module": "esnext", "target": "es2022", "lib": ["es2022", "dom"], "sourceMap": true, "jsx": "react", "moduleResolution": "node", "rootDir": "lib", "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": true, "ignoreDeprecations": "5.0", "allowSyntheticDefaultImports": true, "noUnusedLocals": true, "declaration": true}, "include": ["./lib/index.ts", "./lib/xtrem-ui-index.tsx", "./lib/consumer-mock/consumer-mock.tsx", "./lib/module-types.d.ts", "./lib/component/container/container-properties.ts", "./lib/component/field/rich-text/rtf-parser.ts", "./lib/utils/validator.ts"], "exclude": ["node_modules", "**/*.test.tsx?", "**/__mocks__/*", "**/__snapshots__/*"]}