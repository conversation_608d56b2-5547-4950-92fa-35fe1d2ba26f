.e-dialog-normal>div>[data-element="dialog"] {
    max-height: calc(100vh - 76px);
}

.e-dialog-content-loading {
    height: 100%;
    text-align: center;

    &>div {
        padding-top: 32px;
        padding-bottom: 32px;
        display: inline-block;
        text-align: center;
    }
}

.e-dialog-warn,
.e-dialog-info,
.e-dialog-success,
.e-dialog-error {
    border-top: 2px solid var(--colorsUtilityMajor100);
    background: var(--colorsUtilityMajor025);

    h2 {
        font-size: 2rem;
        font-family: $fontAdelle;
    }
}

.e-dialog-warn {
    border-color: var(--colorsSemanticCaution500);
}

.e-dialog-info {
    border-color: var(--colorsSemanticInfo500);
}

.e-dialog-success {
    border-color: var(--colorsSemanticPositive500);
}

.e-dialog-type-error {
    border-color: var(--colorsSemanticNegative500);

    .e-dialog-error-header {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
    }

    .e-dialog-error-content {
        padding: 0 16px;
    }

    .e-dialog-error-copy {
        cursor: pointer;
        border: none;
        width: 24px;

        span {
            margin: 0;
        }

        span::before {
            color: var(--colorsYin090);
            opacity: 0.7;
        }
    }

    .e-dialog-error-copy:hover {
        background: none;

        span::before {
            color: var(--colorsYin090);
        }
    }

    .e-dialog-error-stack {
        background-color: var(--colorsUtilityMajor050);
        max-height: 100px;
        padding: 5px 10px;
        overflow: auto;
    }
}

.e-page-footer-container .e-dialog-business-actions {
    flex: 1
}

.e-dialog-content {
    flex: 1;
    height: 100%;

    &.e-dialog-type-message {

        h1,
        h2,
        h3,
        h4,
        h5 {
            margin-top: 0;
            margin-bottom: 0;
        }
    }

    .e-dialog-business-actions-more>div {
        display: inline-block;
    }

    .e-page-crud-button-container {
        padding: 0;
    }

    @include extra_small {
        overflow: auto;
    }

    .e-dialog-body {
        flex: 1;
        height: 100%;

        @include extra_small {
            [data-element='form-content'] {
                margin-bottom: 64px;
                overflow: unset;
            }
        }

        [data-element="form-footer"] {
            @include extra_small {
                position: fixed;
                box-shadow: var(--boxShadow200);
                width: 100vw;
                background: var(--colorsYang100);
                padding: 12px;
                bottom: 0;
                right: 0;
                margin-left: 0;
                margin-right: 0;
                box-sizing: border-box;
                height: auto;

                >div {
                    flex: 1;

                    button {
                        flex: 1;
                    }

                    &:empty {
                        display: none;
                    }
                }
            }

            @media screen and (max-width: 356px) {
                flex-wrap: wrap;
                height: auto;
            }
        }

        .e-page-navigation-panel.e-page-navigation-panel-full-width {
            padding: 0;
            margin: 0;
            width: 100%;
        }

        .e-page-navigation-panel {
            margin-left: 0;
        }

        .e-dialog-custom-content {
            overflow: visible;
            display: flex;

            .carbon-row {
                width: 100%;
            }

            .e-block-parent {
                .e-block {
                    box-shadow: none;
                }
            }

            .e-section-header {
                display: none;
            }

            .e-dialog-custom-content-header {
                border-bottom: 1px solid var(--colorsUtilityMajor100);
                position: relative;
                display: flex;
                justify-content: space-between;

                .e-dialog-custom-content-tab-container {
                    @include extra_small {
                        white-space: nowrap;
                        overflow: auto;
                    }

                    .e-dialog-custom-content-tab {
                        font-family: $fontAdelle;
                        font-size: 1.167rem;
                        display: inline-block;
                        padding: 12px 16px 16px 16px;
                        color: var(--colorsUtilityMajor200);
                        cursor: pointer;
                        transition: color 0.5s;

                        &.e-dialog-custom-content-tab-active {
                            color: var(--colorsYin090);
                        }
                    }
                }
            }
        }

        .e-page {
            height: 100%;
            overflow: visible;

            .e-page-body-container {
                min-height: 250px;
            }

            @include extra_small {
                .e-page-main-section {
                    max-height: calc(100dvh - 48px);
                }

                .e-page-body-container {
                    overflow-y: auto;
                }
            }

            &.e-page-has-footer {
                @include extra_small {
                    .e-page-main-section {
                        max-height: calc(100dvh - 120px);
                    }
                }
            }
        }
    }
}

.e-dialog-footer {

    &.e-dialog-type-page {
        display: flex;
        flex: 1;
        flex-direction: row-reverse;
    }

    .e-business-action {
        padding-left: 24px;
    }

    @include extra_small {
        flex: 1;

        .e-dialog-business-actions {
            display: flex;
            flex: 1;
        }

        .e-business-action {
            padding-right: 24px;
            padding-left: 0;
            flex: 1;

            button {
                width: 100%;
            }

            &:last-child {
                padding-right: 0;
            }
        }
    }
}

.e-dialog-text-content {
    font-size: 14px;
    display: block;

    h6 {
        font-size: 14px;
        font-weight: bold;
    }

    ul {
        padding-inline-start: 1.5rem;
    }

    li {
        margin-bottom: 6px;
    }

    @include extra_small {
        padding: 16px 24px;
    }
}

.e-dialog-sidebar {
    .e-dialog-content {
        height: calc(100%);
    }
}

.e-dialog-body .e-section:nth-last-child(2) {
    padding-bottom: 0;
}

[data-element='dialog-full-screen'] {
    button[data-element="close"] {
        @include extra_small {
            right: 12px;
            top: 12px;
        }
    }
}

[data-element="sidebar-content"] .e-filters-dialog {
    margin-left: calc(-1 * var(--spacing400));
    margin-top: calc(-1 * var(--spacing300));

    .e-dialog-body {
        padding-left: var(--spacing400);
    }
}

.e-dialog-sidebar,
[data-element='dialog-full-screen'] {
    [data-element='content'] {
        height: unset;

        @include extra_small {
            padding: 0;
        }
    }

    .e-dialog-content {
        /*
        &.e-dialog-content-with-page-actions {
            height: calc(100vh - 144px);

            @include extra_small {
                height: calc(100% - 112px);
            }
        }*/

        @include extra_small {
            height: calc(100vh - 52px);
        }

        &.e-filters-dialog {
            overflow-y: hidden;
            height: 100%;
            display: flex;
            flex-flow: column;

            @include small {
                height: calc(100vh - 78px);
            }

            .e-dialog-body {
                overflow-y: auto;
            }

            .e-dialog-sidebar-footer {
                position: static;
                height: 64px;

                @include extra_small {
                    position: fixed;
                    bottom: 0;
                    z-index: 10;
                }
            }
        }
    }

}

.e-dialog-sidebar {
    .e-dialog-content.e-dialog-content-with-page-actions {
        height: calc(100vh - 156px);
    }

    .e-page-footer {
        display: flex;

        .e-business-action {
            flex: 1;

            button {
                width: 100%;
            }
        }
    }
}

[data-element='dialog-full-screen'] {
    [data-element='content'] {
        flex: 1;
        height: 100%;

        @include extra_small {
            overflow-y: auto;

            [data-element="form-content"] {
                padding: 0 !important;
            }
        }

    }

    @include extra_small {
        .e-dialog-body {
            padding: 0;
        }
    }

    [data-component='full-screen-heading'] {

        >div,
        .header-container {
            flex-flow: column;
            width: 100%;
            margin-left: -40px;
            margin-right: -40px;
            padding-left: 40px;
            padding-right: 40px;

            .e-header-title-label {
                display: inline-block;
                margin-left: 24px;
                vertical-align: top;
            }
        }

        @include medium_and_below {
            background: var(--colorsYang100);
            padding: 0 40px;
        }

        @include extra_small {
            padding: 12px 20px 12px;
            max-height: 100%;

            .e-dialog-body {
                padding: 0;
            }

            >button {
                margin-right: 12px;
                margin-top: 12px;
            }

            [data-component='heading'] {
                margin-bottom: 0;

                .e-header-container {
                    margin: 0;

                    h1 {
                        line-height: 16px;
                        font-size: 16px;
                    }

                    div {
                        padding: 16px;
                        height: 48px;
                        box-sizing: border-box;

                        h1 {
                            line-height: 16px;
                            font-size: 16px;
                        }
                    }
                }
            }
        }
    }
}

.e-dialog-sidebar {
    div[data-element='sidebar'] {
        overflow-x: hidden;

        >button {
            top: 31px;
        }

        div[data-element='sidebar-content'] {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: stretch;
            overflow: hidden;
            padding: 0;
            height: 100%;
        }
    }
}

div[data-element='sidebar-content'] {
    .e-page-body-container {
        margin-top: 16px;
    }
}

.carbon-portal {
    [data-component="sidebar-header"] {
        flex-direction: column;
        padding: 24px;
    }
}

.e-dialog-sidebar-heading {
    margin-bottom: -24px;
    width: 100%;

    [data-element="header-container"]>div {
        width: 100%;

        >h1 {
            width: 100%;
        }
    }
}

.e-dialog-sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    background: var(--colorsYang100);
    border-top: 1px solid var(--colorsUtilityMajor100);

    .e-dialog-sidebar-footer-content {
        display: flex;
        padding: 12px 34px;
        justify-content: space-between;
        width: 100%;

        @include extra_small {
            padding: 12px 8px;
        }

        div:last-child {
            margin-left: auto;

            button:last-child {
                margin-left: 16px;
                margin-right: 0;
            }
        }
    }
}

.e-toast-content {
    white-space: normal;

    ul {
        padding-inline-start: 16px;
    }

    p,
    ul,
    li {
        margin: 0;
    }
}

@include extra_small {
    [data-element='dialog-full-screen'] {
        .e-dialog-type-page {
            [data-element='form-content'] {
                padding: 0 !important;
            }
        }
    }
}

[data-element='dialog'] {
    .e-header-wizard-steps {
        margin-left: -32px;
        padding-left: 32px;
        margin-right: -85px;
        padding-right: 32px;
    }
}


.e-dialog-title-back-button-wrapper {
    vertical-align: text-bottom;
}

[data-component="dialog"] [data-element="subtitle"] {
    font-size: 16px;
    font-family: var(--fontFamiliesDefault);
    font-weight: var(--fontWeights400);
}

.e-dialog-type-table-sidebar {
    [data-element="form-footer"] {
        margin-top: 0;
        display: flex;
        justify-content: flex-end;
        height: 72px;
        background: var(--colorsYang100);
        box-shadow: inset 0px 1px 0px var(--colorsActionMinor200);
        padding: 16px;
        box-sizing: border-box;
        position: absolute;
        bottom: 0;
        width: 100%;
        text-align: right;
        margin-left: -32px;
    }
}
.e-xtrem-tabs--blank {
    height: 30px;
}

