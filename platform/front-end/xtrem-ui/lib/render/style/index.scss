@import './mixins';
@import './variables';

@font-face {
    font-family: $fontCarbonIcons;
    src: url('/fonts/carbon-icons-webfont.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: $fontAdelle;
    src: url('/fonts/AdelleSansSAGE-Bold.eot');
    src: url('/fonts/AdelleSansSAGE-Bold.eot?#iefix') format('embedded-opentype'), url('/fonts/AdelleSansSAGE-Bold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

/* Sage UI font family */
@font-face {
    font-family: "Sage UI";
    src: url('https://fonts.sage.com/Sage_UI-Regular.woff2') format("woff2"), url('https://fonts.sage.com/Sage_UI-Regular.woff') format("woff");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Sage UI";
    src: url('https://fonts.sage.com/Sage_UI-Medium.woff2') format("woff2"), url('https://fonts.sage.com/Sage_UI-Medium.woff') format("woff");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Sage UI";
    src: url('https://fonts.sage.com/Sage_UI-Medium.woff2') format("woff2"), url('https://fonts.sage.com/Sage_UI-Medium.woff') format("woff");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Sage UI";
    src: url('https://fonts.sage.com/Sage_UI-Bold.woff2') format("woff2"), url('https://fonts.sage.com/Sage_UI-Bold.woff') format("woff");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

// In order to convert the number to px

.e-anim-bounce {
    animation: bounce-animation 0.8s ease-out;
}

@keyframes bounce-animation {

    0%,
    to {
        transform: rotate(0);
    }

    10% {
        transform: scale(1.1);
    }

    20% {
        transform: rotate(4deg) translateY(-12px);
    }

    40% {
        transform: rotate(2deg) translateY(0);
    }

    60% {
        transform: rotate(-2deg) translateY(-8px);
    }

    80% {
        transform: rotate(-1deg) translateY(0);
    }
}

.e-carbon-icon {
    @include icon;
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
}

.carbon-button {
    font-family: $fontAdelle;
}

.carbon-portal.e-loading-portal {
    z-index: 2001;
}


body {
    font-family: var(--fontFamiliesDefault), sans-serif;

    @include extra_small {
        width: 100%;
    }
}

svg {
    color: var(--colorsUtilityMajor300);

    &:hover {
        color: var(--colorsUtilityMajor500);
    }
}

@import './icons';
@import './fields';
@import './ui';
@import './containers';
@import './dialogs';
@import '../../dashboard/dashboard-component';
@import '../../dashboard/dashboard-create-dialog';
@import '../../dashboard/dashboard-editor-dialog';
@import '../../dashboard/dashboard-placeholder';
@import '../../dashboard/widget-editor/widget-editor';

.e-hidden {
    display: none !important;
}

.e-full-width {
    width: 100%;
    display: block;
    box-sizing: border-box;

    .common-input {
        width: 100%;
    }
}

.screen-centered {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    font-weight: normal;
}

body,
input,
select,
textarea {
    font-family: var(--fontFamiliesDefault);
}

.e-screen-reader-only {
    width: 0;
    height: 0;
    overflow: hidden;
    display: block;
}
