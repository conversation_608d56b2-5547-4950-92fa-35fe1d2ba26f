.e-field {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    height: 100%;
    vertical-align: top;
    text-align: left;
    width: 100%;

    /* data-label is set only when field has a title */
    &:not([data-label]):not(.e-card-field):not(.e-separator-field):not(.e-context-page-header):not(.e-file-field):not(.e-detail-list):not([data-nested]):not(.e-pod-field):not(.e-vital-pod-field):not(.e-dynamic-pod-field):not(.e-label-field):not(.e-table-field):not(.e-nested-grid-field):not(.e-static-content-field):not(.e-message-field):not(.e-file-deposit-field):not(.e-form-designer-field):not(.e-node-browser-tree):not(.e-filter-editor-field):not(.e-content-table-field):not(.e-plugin-field):not(.e-multi-file-deposit-field):not(.e-step-sequence-field):not(.e-time-field):not(.e-preview-field):not(.e-table-field) {
        margin-top: 27px;
    }

    @media print {
        page-break-inside: avoid;
    }

    &.e-disabled {

        *[data-element="label"],
        .common-input__label,
        .common-input__help-text {
            color: var(--colorsYin055);
        }
    }

    .e-field-nested-no-input {
        border: none;
        height: 18px;
        line-height: 18px;
        min-width: auto;
    }

    &.e-helper-text-hidden {

        span[data-element="help"],
        .common-input__help-text {
            display: none;
        }
    }

    &.e-title-hidden {
        @include e-field-label-hidden;
    }

    .e-field-prefix {
        font-family: var(--fontFamiliesDefault);
        line-height: 38px;
        color: var(--colorsYin055);
        font-size: 14px;
        padding-right: 4px;
        padding-top: 1px;
        box-sizing: border-box;
    }

    .e-field-postfix {
        font-family: var(--fontFamiliesDefault);
        line-height: 38px;
        color: var(--colorsYin055);
        font-size: 14px;
        padding-left: 4px;
        padding-top: 1px;
        box-sizing: border-box;
    }

    .common-input__label {
        @include e-field-field-label;

        &.e-nested-read-only-label {
            margin-bottom: 0;
        }
    }

    .common-input__help-text,
    span[data-element="help"] {
        display: block;
        margin-top: 8px;
        margin-left: 0;
    }

    [data-element="label"],
    legend {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        @include e-field-label-font;
    }

    .carbon-help {
        .carbon-icon {
            font-size: 10px;
        }
    }

    .e-field-error-message-carbon {
        .common-input__input {
            border-color: var(--colorsSemanticNegative500);
        }
    }

    .e-field-right-icon {
        font-size: 24px;
    }

    &.e-in-tile {
        padding: 0px 8px;
        padding-right: 40px;
        margin-bottom: 0;
        border-right: 1px solid var(--colorsUtilityMajor100);
        margin-right: 10px;

        .carbon-heading__title {
            height: 64px;
            line-height: 64px;

            .carbon-icon:before {
                height: 64px;
                font-size: 32px;
                line-height: 64px;
            }
        }

        &:last-of-type {
            border-right: none;
            margin-right: 0;
        }
    }
}

.e-field .e-field-error-message {
    color: var(--colorsSemanticNegative500);
}

.e-field-input {
    @include extra_small {
        width: 100%;
    }
}

.e-field-input-adornment {
    p {
        font-size: 1.1667rem;
        line-height: 1.1667rem;
    }
}

.e-field-read-only {
    display: block;
    font-size: 1.1667rem;
    padding: 10px 4px;
    height: 40px;
    border-bottom: 1px solid var(--colorsUtilityMajor100);
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.e-icon-clickable {
    cursor: pointer;
}

/* Label */
.e-page-body .e-label-field {
    & [data-component="pill"] {
        margin-top: 8px;
        text-overflow: ellipsis;
        max-width: 100%;
        overflow: hidden;
        white-space: nowrap;
        display: inline-block;
        box-sizing: border-box;
    }
}

/* Link */
.e-link-field {
    .carbon-link__anchor {
        line-height: 31px;
    }
}

/* Scan */
.e-scan-field {
    .e-field-postfix {
        display: flex;
        align-items: center;
    }

    .e-scan-field-icon>div {
        height: 100%;

        &>span {
            height: 100%;
        }
    }
}

.e-button-field {
    button {
        margin-right: 0;
        width: 100%;
    }
}

/* Reference */
.e-reference-field {
    display: flex;

    .e-portrait {
        padding-right: 8px;
        display: inline-block;
        vertical-align: top;
    }

    >div {
        width: 100%;
    }

    &.e-reference-inline-picture {
        display: flex;
        flex-flow: row;

        .e-portrait {
            width: unset;
        }

        .e-reference-field-body {
            flex: 1;
        }
    }

    &.e-reference-field-lookup {
        [type="dropdown"] {
            display: none;
        }
    }

    .e-reference-field-lookup-button-mobile {
        padding: 12px;
        margin-right: 0;
        margin-right: -10px;
        width: 40px;
        border-left: 1px solid var(--colorsUtilityMajor300);
        border-top: none;
        border-right: none;
        border-bottom: none;

        &[disabled] {
            border-left: 1px solid var(--colorsUtilityMajor100);
        }

        span {
            margin-right: 0;
            color: var(--colorsYin065);
        }

        &.e-reference-field-lookup-button-mobile-small {
            padding-top: 8px;
        }

        &.e-reference-field-lookup-button-mobile-large {
            padding-top: 16px;
        }
    }
}

.e-reference-field,
.e-multi-reference-field {
    span>span[data-component="icon"]:not([role="tooltip"]) {
        order: 1;
        margin-right: 8px;
        padding-top: 3px;
        padding-bottom: 3px;
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
    }
}

.e-reference-suggestion-info {
    font-style: italic;
}


.e-block-context-detail-panel {
    .e-date-field {
        [data-popper-placement="bottom-start"] {
            width: 287px;
        }

        [data-popper-placement="top-start"] {
            width: 287px;
        }
    }
}

.e-date-field,
.e-widget-editor-flat-table-date {
    div[role="presentation"] {
        width: 100% !important;
    }

    div[data-component="date-range"] {
        display: flex;
        gap: 10px;

        div[role="presentation"] {
            min-height: 32px !important;
        }
    }

    .carbon-date .common-input__field {
        width: 100% !important;
    }

    &.e-full-width .carbon-date .common-input__field {
        width: 100% !important;
    }
}

.rdp-root {
    width: 328px;
    font-family: var(--fontFamiliesDefault);

    @include small_and_below {
        top: 48px !important;
        bottom: 0 !important;
        left: 0 !important;
        width: 100vw;
        padding: 0px 0 0 !important;
        margin: 0 !important;
        position: fixed !important;

        .rdp-day_button {
            display: inline-block;
        }
    }
}

/* Numeric */
.e-numeric-field,
.e-aggregate-field {
    .e-field-read-only {
        text-align: right;

        @include small_and_below {
            text-align: left;
        }
    }
}

.e-numeric-field {
    div[role='presentation'] {
        padding: 0px 12px;

        input {
            padding: 0;
        }
    }
}

.e-text-field {
    div[role='presentation'] {
        padding: 0px 12px;

        input {
            padding: 0;
        }
    }

    .e-text-field-autocomplete-value {
        position: absolute;
        display: flex;
        justify-items: center;
        line-height: 38px;
        color: var(--colorsActionMinor400);
        font-size: 14px;
    }
}

.e-text-area-field {
    textarea {
        height: auto !important;
    }

    .e-field-read-only {
        white-space: pre;
        height: auto;
        min-height: 40px;

        @include extra_small {
            width: 90%;
            white-space: normal;
        }
    }

    >div>div>div {
        width: 100%;
    }

    .carbon-textarea .common-input__label {
        margin-top: 0;
    }

    .e-text-area-field-input>textarea {
        margin: 0 2px;
    }
}

.e-rich-text-field {
    @include extra_small {
        overflow-x: auto;
        width: 100vw;
    }

    display: flex;
    flex-direction: column;
    padding: 0;

    .rdw-editor-wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid var(--colorsUtilityMajor200);

        .rdw-editor-toolbar {
            height: 40px;
        }

        .rdw-editor-main {
            flex: 1;
            min-height: 200px;
        }
    }

    &>.e-field-development-rtf-download {
        display: none;
    }
}

.e-rich-text-field.e-rich-text-field-disabled {
    .rdw-editor-toolbar {
        opacity: 0.3;
        pointer-events: none;
    }

    .rdw-editor-main {
        pointer-events: none;
    }
}

.e-filter-label-wrapper {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    padding: 8px;

    .e-filter-label {
        margin-right: 20px;
        margin-bottom: 5px;

        >span {
            >button {
                top: 0 !important;
                right: 0 !important;
            }
        }
    }
}

.e-filter-label-wrapper--navigation-panel {
    @extend .e-filter-label-wrapper;
    background-color: var(--colorsUtilityMajor025) !important;

    .e-filter-label {
        max-width: 100%;
        margin-right: 10px;
        margin-bottom: 10px;

        span[data-component="pill"] {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

/* Table */
.e-table-field,
.e-nested-grid-field {
    width: 100%;
    display: flex;
    flex-direction: column;
    border: none;
    position: relative;
    box-sizing: border-box;

    .e-table-field-header-column-error {
        border: 1px solid var(--colorsSemanticNegative500);
        box-shadow: inset 1px 1px 0 var(--colorsSemanticNegative500), inset -1px -1px 0 var(--colorsSemanticNegative500);
    }

    .ag-theme-balham {
        font-family: var(--fontFamiliesDefault);

        .ag-simple-filter-body-wrapper .ag-filter-condition {
            display: none;
        }

        [ref="btFirst"],
        [ref="btLast"] {
            display: none;
        }
    }

    .e-field-title {
        padding: 8px 8px 8px 0;

        @include small_and_below {
            padding: 8px 0;
        }

        .common-input__label {
            margin-bottom: 0;
        }
    }

    .e-field-header {
        padding: 0 0 8px 0;
    }

    .e-table-field-actions-separator {
        margin: 4px 4px 0 8px;
        display: inline-block;
        vertical-align: middle;
        background-color: var(--colorsUtilityMajor050);
        width: 2px;
        height: 20px;
    }

    .ag-root-wrapper {
        border-radius: 0;
        overflow: visible;
    }

    .e-field-nested {
        flex: 1;
        max-width: 100%;
    }

    .e-table-field-selection-checkbox {
        padding: 0;

        svg {
            left: 0;
        }
    }

    .e-table-field-wrapper {
        overflow-x: auto;
    }

    .e-table-field-row-select {
        height: 24px;
    }

    .e-table-field-select-row-disabled {
        cursor: not-allowed;
        pointer-events: none;

        .ag-checkbox {
            opacity: 0.2;
        }
    }

    .e-table-columns-display-menu {
        background: var(--colorsYang100);
        box-shadow: var(--boxShadow100);
        margin-right: 40px;
        max-width: 352px;
        padding: 8px 0;
        position: absolute;
        right: 0;
        top: 40px;
        z-index: 10;
    }

    .e-table-columns-display-menu-title {
        color: var(--colorsUtilityYin090);
        font-family: $fontAdelle;
        font-size: 18px;
        line-height: 27px;
        margin-left: 24px;
        margin-top: 16px;
        margin-bottom: 12px;
        width: 199px;
        text-align: left;
        overflow-y: scroll;
    }

    .e-table-columns-display-menu-items {
        width: 100%;
        margin-left: 8px;
        margin-right: 16px;
        padding: 0;
    }

    .e-table-columns-display-menu-item {
        label {
            color: var(--colorsUtilityYin090);
            ;
            margin-top: 0;
        }

        display: inline-block;
        font-size: 12px;
        height: 40px;
        line-height: 18px;
        padding-left: 12px;
        text-align: left;
        width: 45%;
    }

    .carbon-checkbox {
        padding-top: 0;
    }

    .e-table-field-header-no-sort {
        cursor: not-allowed !important;

        &:focus {
            outline: none !important;
        }
    }

    .e-table-field-header {
        .e-table-field-header-row {
            height: 32px;
        }

        .e-table-field-header-label {
            font-size: 1rem;

            &.e-table-field-header-label-numeric {
                padding-right: 12px;
            }
        }

        th:first-child {
            padding-left: 8px;
        }

        th:last-child {
            padding-right: 8px;
        }
    }

    .e-numeric-cell-editor {
        box-sizing: border-box;
        text-align: right;
        outline: none;
    }

    .e-nested-header-numeric {
        .ag-cell-label-container {
            flex-direction: row;
        }

        .ag-header-cell-label {
            flex-direction: row-reverse;
        }
    }

    .e-nested-cell-field-link a {
        color: var(--colorsActionMajor500);

        &:hover {
            color: var(--colorsActionMajor600);
        }
    }

    .e-nested-cell-field-numeric {
        text-align: right;
    }

    .e-field {
        .e-field-read-only {
            min-width: auto;
        }

        width: 100%;
        padding: initial;
        white-space: nowrap;
        text-overflow: ellipsis;

        .e-field-input {
            width: 100%;
            border: none;
            background-color: transparent;
        }
    }
}

.e-load-more-button-container {
    padding: 16px 0;
    text-align: center;

    .e-load-more-button-loader {
        padding: 30px 0;
    }
}

.e-table-pagination {
    display: flex;
    flex-flow: row;
    justify-content: center;
    align-items: center;

    @include print-hidden();

    div[data-component="multi-action-button"] {
        background-color: var(--colorsUtilityMajor025);

        button {
            font-size: 12px;
            color: var(--colorsYin090);
            height: 20px;

            &:first-of-type {
                font-weight: normal;
            }
        }

        span[data-component="icon"] {
            display: none;
        }

        div[data-element="additional-buttons"] {
            background-color: var(--colorsYang100);
            width: 100px;
            color: var(--colorsYin090);
            box-shadow: 0px 0px 34px 4px rgba(0, 0, 0, 0.23);
            padding: 8px 0px;

            button[role="menu-item"] {
                background-color: var(--colorsYang100);
                border: unset;

                &:hover {
                    background-color: #f2f4f5;
                }
            }
        }
    }

    .e-table-pagination-pages {
        display: flex;
    }

    .e-table-pagination-button {
        font-weight: bold;
        width: 32px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 4px;
        border: none;

        &:hover {
            cursor: pointer;
        }

        &:focus {
            span {
                color: var(--colorsUtilityMajor400);
            }
        }

        &:disabled {
            &:hover {
                cursor: not-allowed;
            }

            span {
                color: var(--colorsUtilityMajor050);
            }
        }

        span {
            color: var(--colorsUtilityMajor300);
        }
    }

    .e-table-pagination-button.active {
        color: var(--colorsUtilityMajor500) !important;
        background-color: var(--colorsUtilityMajor050) !important;
    }
}

.e-chart-field {
    max-width: 100%;
    height: inherit;

    @include extra_small {
        @include full_width_mobile_field;
    }
}

/* AG GRID cell styles */
div[role="gridcell"] {

    /* Date field */
    &.e-nested-cell-field-date {
        input {
            font-family: var(--fontFamiliesDefault);
            font-size: 12px;
            height: 26px;
        }

        span[data-component="icon"] {
            width: 26px;
            height: 26px;
        }
    }

    /* Image field */
    &.e-nested-cell-field-image {
        height: 100%;
        display: flex;
        align-items: center;

        img {
            height: 100%;
            object-fit: scale-down;
        }
    }

    /* Progress field */
    &.e-nested-cell-field-progress {
        .e-field-progress-progressbar {
            padding-left: 2px;
            padding-right: 2px;
        }
    }
}

.e-field-header {
    display: flex;
    align-items: center;

    .e-table-pagination {
        .e-table-pagination-select-pre-text {
            margin-right: 8px;
        }

        .e-table-pagination-select-post-text {
            margin-left: 8px;
        }

        .e-table-pagination-select {
            border: none;
            padding: 0;
            width: 30px;
            height: 20px;
            background-color: transparent;
            color: var(--colorsYin090);

            &:hover {
                background-color: transparent;
                border: none;
                color: var(--colorsYin090);
            }
        }

        .e-table-pagination-select-label {
            font-family: var(--fontFamiliesDefault);
            font-weight: var(--fontWeights500);
        }

        .e-table-pagination-select-option {
            font-family: var(--fontFamiliesDefault);
            font-weight: var(--fontWeights500);
        }

        .e-table-pagination-counter {
            margin-left: 16px;
            padding-left: 16px;
            font-family: var(--fontFamiliesDefault);
            font-weight: var(--fontWeights500);
            padding-right: 16px;
            border: 1px solid var(--colorsUtilityMajor050);
            border-width: 0 0 0 2px;
            color: var(--colorsUtilityMajor300);

            .e-table-pagination-counter-total {
                white-space: pre;
            }
        }

        .e-table-pagination-button {
            margin: 4px;
            padding: 0;
            width: 16px;

            >span {
                margin-right: 0;
            }
        }
    }

    .e-field-actions-wrapper {
        text-align: right;
        margin-right: 4px;
        flex: 1;

        .e-component-actions,
        .e-field-header-actions,
        .e-component-actions>div,
        .e-field-header-actions>div {
            display: inline-block;
            position: relative;
        }
    }
}

.e-page-validation-error-list {
    font-family: var(--fontFamiliesDefault);
    font-size: 14px;
    line-height: 14px;
    padding-top: 10px;

    &>h6 {
        font-family: var(--fontFamiliesDefault);
        font-weight: var(--fontWeights500);
        margin: 0;
        padding: 0;
        font-size: 18px;
        line-height: 18px;
    }

    &>ul {
        padding-inline-start: 1.8rem;
        margin: 16px 0;

        &>li:not(:last-child) {
            margin-bottom: 1rem;
        }

        &>li {
            &>p {
                font-family: var(--fontFamiliesDefault);
                display: inline;
                margin: 0;
                padding: 0;
                font-size: 14px;
                line-height: 14px;
            }
        }
    }

    p {
        margin: 0;
        padding: 0;
    }

    p {
        margin: 0;
        padding: 0;
    }
}

.e-step-sequence-field {
    position: relative;

    ol {
        padding: 0;
    }
}

@import "../../component/field/calendar/calendar-component.scss";
@import "../../component/field/card/card-component.scss";
@import "../../component/field/checkbox/checkbox-component.scss";
@import "../../component/field/datetime-range/datetime-range-component.scss";
@import "../../component/field/detail-list/detail-list-component.scss";
@import "../../component/field/dropdown-list/dropdown-list-component.scss";
@import "../../component/field/file-deposit/file-deposit-component.scss";
@import "../../component/field/file/file-component.scss";
@import "../../component/field/filter-editor/filter-editor-component.scss";
@import "../../component/field/filter-select/filter-select-component.scss";
@import "../../component/field/form-designer/form-designer-component.scss";
@import "../../component/field/image/image-component.scss";
@import "../../component/field/link/link-component.scss";
@import "../../component/field/message/message-component.scss";
@import "../../component/field/multi-dropdown/multi-dropdown-component.scss";
@import "../../component/field/multi-file-deposit/multi-file-deposit-component.scss";
@import "../../component/field/nested-grid/mobile-nested-grid.scss";
@import "../../component/field/nested-grid/nested-grid-component.scss";
@import "../../component/field/numeric/numeric-component.scss";
@import "../../component/field/pod-collection/pod-collection-component.scss";
@import "../../component/field/preview/preview-component.scss";
@import "../../component/field/progress/progress-component.scss";
@import "../../component/field/radio/radio-component.scss";
@import "../../component/field/relative-date/relative-date-component.scss";
@import "../../component/field/rich-text/rich-text-component.scss";
@import "../../component/field/selection-card/selection-card-component.scss";
@import "../../component/field/separator/separator-component.scss";
@import "../../component/field/static-content/static-content-component.scss";
@import "../../component/field/step-sequence/step-sequence-component.scss";
@import "../../component/field/switch/switch-component.scss";
@import "../../component/field/table-summary/table-summary-component.scss";
@import "../../component/field/table/desktop-table-component.scss";
@import "../../component/field/table/mobile-table-component.scss";
@import "../../component/field/time/time-component.scss";
@import "../../component/field/toggle/toggle-component.scss";
@import "../../component/field/visual-process/visual-process-component.scss";
@import "../../component/field/workflow/workflow-component.scss";
@import "../../component/table-sidebar/table-sidebar.scss";
@import "../../component/field/dynamic-select/dynamic-select-component.scss"