.e-xtrem-controller {
    margin-top: 49px;
    display: flex;
    height: calc(100% - 49px);
    width: 100%;
    flex-direction: column;
    overflow-y: auto;
    position: relative;
    font-family: var(--fontFamiliesDefault);

    @media print {
        height: fit-content;
        overflow: unset;
    }
}

.e-horizontal-separator {
    font-size: 1rem;
    background: var(--colorsUtilityMajor025);
    border-bottom: 1px solid var(--colorsUtilityMajor100);
    padding: 4px 16px;
    color: var(--colorsYin055);
    font-family: $fontAdelle;
    height: 24px;
    max-height: 24px;
    box-sizing: border-box;
}

.e-footer-container {
    @include page_responsive_container;
    bottom: 0;
    position: sticky;
    z-index: 9;

    .e-footer {
        padding: 12px 16px;
        background: var(--colorsYang100);
        box-shadow: var(--boxShadow200);

        .e-footer-button-next {
            margin: 0;
            float: right;
        }
    }
}

.e-sticker-wrapper .e-sticker-icon {
    position: absolute;
    top: 70px;
    z-index: 100;
    right: 100;
}

.e-tooltip-wrapper {
    display: block;
    position: relative;

    .e-tooltip {
        position: fixed;
        z-index: 10;
    }
}

@import '../../component/container/block/block-component.scss';
@import '../../component/container/dialog/loading-dialog.scss';
@import '../../component/container/dialog/body/async-loader.scss';
@import '../../component/container/dialog/body/lookup-dialog.scss';
@import '../../component/container/detail-panel/detail-panel-component.scss';
@import '../../component/container/navigation-panel/navigation-panel-component.scss';
@import '../../component/container/page/xtrem-header';
@import '../../component/container/page/page-component.scss';
@import '../../component/container/page-footer/page-footer-component.scss';
@import '../../component/container/section/section-component.scss';
@import '../../component/container/sticker/sticker-dialog.scss';
@import '../../component/container/tile/tile-component.scss';