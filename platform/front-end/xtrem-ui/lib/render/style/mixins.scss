$xs: 703;
$s: 959;
$m: 1259;
$l: 1920;


@function number($value) {
    @if type-of($value)=='number' {
        @return $value;
    }

    @else if type-of($value) !='string' {
        $_: log('Value for `to-number` should be a number or a string.');
    }

    $result: 0;
    $digits: 0;
    $minus: str-slice($value, 1, 1)=='-';
    $numbers: (
        '0': 0,
        '1': 1,
        '2': 2,
        '3': 3,
        '4': 4,
        '5': 5,
        '6': 6,
        '7': 7,
        '8': 8,
        '9': 9,
    );

@for $i from if($minus, 2, 1) through str-length($value) {
    $character: str-slice($value, $i, $i);

    @if not(index(map-keys($numbers), $character) or $character=='.') {
        @return to-length(if($minus, -$result, $result), str-slice($value, $i));
    }

    @if $character=='.' {
        $digits: 1;
    }

    @else if $digits==0 {
        $result: $result * 10(+map-get($numbers, $character));
    }

    @else {
        $digits: $digits * 10;
        $result: $result (+(map-get($numbers, $character))) / $digits;
    }
}

@return if($minus, -$result, $result);
}

@mixin print-hidden {
    @media print {
        display: none !important;
    }
}

@mixin extra_small {
    @media (max-width: #{$xs}px) {
        @content;
    }
}

@mixin small_and_below {
    @media (max-width: #{$s}px) {
        @content;
    }
}

@mixin small {
    @media (min-width: #{$xs+1px}) and (max-width: #{$s}px) {
        @content;
    }
}

@mixin medium_and_below {
    @media (max-width: #{$m}px) {
        @content;
    }
}

@mixin medium {
    @media (min-width: #{$s+1px}) and (max-width: #{$l}px) {
        @content;
    }
}

@mixin page_responsive_container {
    box-sizing: border-box;

}

@mixin icon {
    font-family: $fontCarbonIcons;
}

@mixin s_section_shadow {
    -webkit-box-shadow: 0 3px 3px 0 rgba(0, 20, 29, 0.2), 0 2px 4px 0 rgba(0, 20, 29, 0.15);
    -moz-box-shadow: 0 3px 3px 0 rgba(0, 20, 29, 0.2), 0 2px 4px 0 rgba(0, 20, 29, 0.15);
    box-shadow: 0 3px 3px 0 rgba(0, 20, 29, 0.2), 0 2px 4px 0 rgba(0, 20, 29, 0.15);

    @media print {
        box-shadow: none !important;
    }
}

@mixin full_width_mobile_field {
    width: 100vw !important;
    margin: 0 -16px;
}

@mixin e-field-label-font {
    font-family: var(--fontFamiliesDefault);
    font-weight: 600;
    font-size: 14px;
    color: var(--colorsYin090);
}

@mixin e-field-field-label {
    min-height: 17px;
    display: block;
    padding: 0;
    line-height: unset;
    cursor: default;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 8px;
    @include e-field-label-font;
}

@mixin e-field-label-hidden {

    [data-element='legend'],
    legend {
        display: none;
    }

    .e-pill-wrapper {
        padding-top: 0;
    }
}

@mixin isFirefox {
    @supports (-moz-appearance:none) {
        @content;
    }
}

@mixin isNotFirefox {
    @supports not(-moz-appearance:none) {
        @content;
    }
}

@mixin e-block-container-style {
    border-radius: var(--borderRadius100);
    background: var(--colorsUtilityMajor010);
    height: 100%;

    @include extra_small {
        background: var(--colorsUtilityMajor025);
    }
}

@mixin e-block-title-container-style {
    background: transparent;
    padding: 0;

    @include extra_small {
        background: var(--colorsUtilityMajor025);
        border-top: 1px solid var(--colorsUtilityMajor100);
        border-bottom: 1px solid var(--colorsUtilityMajor100);
        padding: 0 16px;
        margin-bottom: 0;
    }
}

@mixin e-block-title-style {
    font-family: $fontAdelle;
    font-size: 1.333rem;
    padding: 16px 16px 4px 16px;
    color: var(--colorsYin090);
    min-height: 20px;
    line-height: 20px;
    margin: 0;

    @include extra_small {
        padding: 0 0 8px 0;
        height: 24px;
        line-height: 24px;
    }

    @include extra_small {
        padding: 4px 0;
        color: var(--colorsYin055);
        font-family: var(--fontFamiliesDefault);
        font-weight: var(--fontWeights700);
        font-size: 1rem;
    }
}

@mixin e-pod-title-style {

    @include e-block-title-style;
    height: 24px;
    line-height: 24px;

    @include extra_small {
        padding: 0 16px;
        height: 40px;
        line-height: 40px;
    }
}
