@import "../../component/ui/calendar-body/calendar-body.scss";
@import "../../component/ui/card/card-component.scss";
@import "../../component/ui/dnd-files/drag-and-drop-file.scss";
@import "../../component/ui/filter/filter-manager.scss";
@import "../../component/ui/input-field-skeleton.scss";
@import "../../component/ui/no-rows-found/no-rows-found.component.scss";
@import "../../component/ui/node-browser-tree/node-browser-tree.scss";
@import "../../component/ui/pod/pod-component.scss";
@import "../../component/ui/select/select.scss";
@import "../../component/ui/table-shared/ag-theme-balham--curation.scss";
@import "../../component/ui/table-shared/filters/date/date-filter.scss";
@import "../../component/ui/table-shared/filters/numeric/numeric-filter.scss";
@import "../../component/ui/table-shared/filters/reference/reference-filter.scss";
@import "../../component/ui/table-shared/table-configuration-dialog/table-configuration-dialog.scss";
@import "../../component/ui/table-shared/table-dropdown-actions/table-dropdown-actions.scss";
@import "../../component/ui/table-shared/table-view-selector.scss";
@import "../../component/ui/tabs/xtrem-tabs.scss";
@import "../../component/ui/time/time-component.scss";
@import "../../component/ui/xtrem-action-popover.scss";
@import '~@sage/xtrem-ui-components/build/xtrem-ui-components.css';
@import '~@sage/xtrem-document-editor/build/xtrem-document-editor.css';
@import './ag-grid-icon-overrides.scss';

.bold {

    & input,
    p {
        font-weight: bold !important;
    }
}

.ag-theme-balham .ag-cell,
.ag-theme-balham .ag-full-width-row {
    padding: 0 !important;
}

.ag-theme-balham .ag-cell.e-nested-cell-editable,
.ag-theme-balham .ag-full-width-row.e-nested-cell-editable {
    padding: 5px 3px 5px 0 !important;
}

div[role="gridcell"] .ag-react-container {
    height: 100% !important;
}

.error-list {
    list-style: none;
    padding: 0;
    font-size: 13px;

    & li {
        margin: 0;
        margin-top: 6px;

        :before {
            content: "\274C";
            /* cross mark icon*/
            display: inline-block;
            margin-right: 10px;
            width: 1.3em;
            font-size: 1em;
            color: transparent;
            text-shadow: 0 0 0 var(--colorsSemanticNegative500);
            margin-inline-start: -2px;
        }
    }
}

.error-list--inside-tooltip {
    margin-bottom: 0;
    margin-top: 2px;
}

.error-list--bullets {
    padding: 0;
    padding-inline-start: 1.3em;

    strong {
        font-family: var(--fontFamiliesDefault);
        font-weight: var(--fontWeights500);
    }
}

.e-ui-table-tooltip-content {
    background: var(--colorsYang100);
    color: var(--colorsYin090);
    display: block;
    padding: 8px 16px;
    margin-left: -11px;
    margin-right: -11px;
    margin-top: -7px;
    border-top-right-radius: calc(var(--borderRadius050) - 1px);
    border-top-left-radius: calc(var(--borderRadius050) - 1px);
    text-align: left;
}

.e-flat-table-placeholder {
    text-align: center;
}