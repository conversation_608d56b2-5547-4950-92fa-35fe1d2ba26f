@import './variables';

.ag-theme-balham,
.ag-theme-balham-dark,
.ag-theme-balham-auto-dark {

    --ag-icon-font-code-filter: "";
    --ag-icon-font-family-filter: #{$fontCarbonIcons};

    --ag-icon-font-code-menu-alt: "";
    --ag-icon-font-family-menu-alt: #{$fontCarbonIcons};

    --ag-icon-font-code-pin: "";
    --ag-icon-font-family-pin: #{$fontCarbonIcons};

    --ag-icon-font-code-asc: "";
    --ag-icon-font-family-asc: #{$fontCarbonIcons};

    --ag-icon-font-code-desc: "";
    --ag-icon-font-family-desc: #{$fontCarbonIcons};

    --ag-icon-font-code-tree-closed: "";
    --ag-icon-font-family-tree-closed: #{$fontCarbonIcons};

    --ag-icon-font-code-tree-open: "";
    --ag-icon-font-family-tree-open: #{$fontCarbonIcons};

    --ag-checkbox-checked-color: var(--colorsYin090);

    --ag-icon-font-code-checkbox-checked: "";
    --ag-icon-font-family-checkbox-checked: #{$fontCarbonIcons};

    --ag-icon-font-code-checkbox-unchecked: "";
    --ag-icon-font-family-checkbox-unchecked: #{$fontCarbonIcons};

    --ag-icon-font-code-checkbox-indeterminate: "\E968";
    --ag-icon-font-family-checkbox-unchecked: #{$fontCarbonIcons};
    --ag-checkbox-indeterminate-color: var(--colorsYin090);
}

.ag-checkbox-input-wrapper.ag-partial::after {
    content: var(--ag-icon-font-code-checkbox-indeterminate);
    line-height: 14px;
}