/**
 * @packageDocumentation
 * @module root
 * */

import type { AccessStatus } from '@sage/xtrem-shared';
import * as React from 'react';
import { connect } from 'react-redux';
import type { BaseControlObject } from '../component/base-control-object';
import type { FieldWrapperExternalProps } from '../component/field/field-base-component-types';
import type { ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import type { ContainerWidth, FieldWidth } from '../component/types';
import { FieldKey } from '../component/types';
import { GridColumn } from '@sage/xtrem-ui-components';
import * as xtremRedux from '../redux';
import type { ResponsiveTypes } from '../redux/state';
import type { PageDefinition } from '../service/page-definition';
import { xtremConsole } from '../utils/console';
import { resolveByValue } from '../utils/resolve-value-utils';
import { calculateContainerWidth, calculateFieldWidth } from '../utils/responsive-utils';
import { getPagePropertiesFromPageDefinition } from '../utils/state-utils';
import { isDevMode } from '../utils/window';
import { getElementAccessStatus } from '../utils/access-utils';
import { AsyncConnectedVisualProcessComponent } from '../component/field/visual-process/async-visual-process-component';
import { AsyncConnectedCalendarComponent } from '../component/field/calendar/async-calendar-component';
import { AsyncConnectedRichTextComponent } from '../component/field/rich-text/async-rich-text-component';
import { AsyncConnectedChartComponent } from '../component/field/chart/async-chart-component';
import { AsyncConnectedTableSummaryComponent } from '../component/field/table-summary/async-table-summary-component';
import { AsyncConnectedPodCollectionComponent } from '../component/field/pod-collection/async-pod-collection-component';
import { AsyncConnectedDetailListComponent } from '../component/field/detail-list/async-detail-list-component';
import { AsyncConnectedReferenceComponent } from '../component/field/reference/async-reference-component';
import { AsyncConnectedMessageComponent } from '../component/field/message/async-message-component';
import { AsyncConnectedMultiReferenceComponent } from '../component/field/multi-reference/async-multi-reference-component';
import { AsyncConnectedMultiDropdownComponent } from '../component/field/multi-dropdown/async-multi-dropdown-component';
import { AsyncConnectedSelectComponent } from '../component/field/select/async-select-component';
import { AsyncConnectedDropdownListComponent } from '../component/field/dropdown-list/async-dropdown-list-component';
import { AsyncConnectedFilterEditorComponent } from '../component/field/filter-editor/async-filter-editor-component';
import { AsyncConnectedFilterSelectComponent } from '../component/field/filter-select/async-filter-select-component';
import { AsyncConnectedAggregateComponent } from '../component/field/aggregate/async-aggregate-component';
import { AsyncConnectedButtonComponent } from '../component/field/button/async-button-component';
import { AsyncConnectedCardComponent } from '../component/field/card/async-card-component';
import { AsyncConnectedCheckboxComponent } from '../component/field/checkbox/async-checkbox-component';
import { AsyncConnectedCountComponent } from '../component/field/count/async-count-component';
import { AsyncConnectedDateComponent } from '../component/field/date/async-date-component';
import { AsyncConnectedFileComponent } from '../component/field/file/async-file-component';
import { AsyncConnectedFileDepositComponent } from '../component/field/file-deposit/async-file-deposit-component';
import { AsyncConnectedFormDesignerComponent } from '../component/field/form-designer/async-form-designer-component';
import { AsyncConnectedIconComponent } from '../component/field/icon/async-icon-component';
import { AsyncConnectedImageComponent } from '../component/field/image/async-image-component';
import { AsyncConnectedLabelComponent } from '../component/field/label/async-label-component';
import { AsyncConnectedPluginComponent } from '../component/field/plugin/async-plugin-component';
import { AsyncConnectedPodComponent } from '../component/field/pod/async-pod-component';
import { AsyncConnectedProgressComponent } from '../component/field/progress/async-progress-component';
import { AsyncConnectedRadioComponent } from '../component/field/radio/async-radio-component';
import { AsyncConnectedSeparatorComponent } from '../component/field/separator/async-separator-component';
import { AsyncConnectedStepSequenceComponent } from '../component/field/step-sequence/async-step-sequence-component';
import { AsyncConnectedSwitchComponent } from '../component/field/switch/async-switch-component';
import { AsyncConnectedTextAreaComponent } from '../component/field/text-area/async-text-area-component';
import { AsyncConnectedTextComponent } from '../component/field/text/async-text-component';
import { AsyncConnectedToggleComponent } from '../component/field/toggle/async-toggle-component';
import { AsyncConnectedVitalPodComponent } from '../component/field/vital-pod/async-vital-pod-component';
import { AsyncConnectedLinkComponent } from '../component/field/link/async-link-component';
import { AsyncConnectedNumericComponent } from '../component/field/numeric/async-numeric-component';
import { AsyncConnectedPreviewComponent } from '../component/field/preview/async-preview-component';
import { AsyncConnectedStaticContentComponent } from '../component/field/static-content/async-static-content-component';
import { AsyncConnectedTreeComponent } from '../component/field/tree/async-tree-component';
import { AsyncConnectedTableComponent } from '../component/field/table/async-table-component';
import { AsyncConnectedNestedGridComponent } from '../component/field/nested-grid/async-nested-grid-component';
import { AsyncConnectedRelativeDateComponent } from '../component/field/relative-date/async-relative-date-component';
import { AsyncConnectedNodeBrowserTreeComponent } from '../component/field/node-browser-tree/async-node-browser-tree-component';
import { AsyncConnectedSelectionCardComponent } from '../component/field/selection-card/async-selection-card-component';
import { AsyncConnectedDynamicPodComponent } from '../component/field/dynamic-pod/async-dynamic-pod-component';
import { AsyncConnectedWorkflowComponent } from '../component/field/workflow/async-workflow-component';
import { AsyncConnectedContentTableComponent } from '../component/field/content-table/async-content-table-component';
import { AsyncConnectedMultiFileDepositComponent } from '../component/field/multi-file-deposit/async-multi-file-deposit-component';
import { AsyncConnectedTimeComponent } from '../component/field/time/async-time-component';
import { AsyncConnectedDatetimeRangeComponent } from '../component/field/datetime-range/async-datetime-range-component';
import { AsyncConnectedDynamicSelectComponent } from '../component/field/dynamic-select/async-dynamic-select-component';
import { AsyncConnectedDatetimeComponent } from '../component/field/datetime/async-datetime-component';

export interface FieldWrapperProps extends FieldWrapperExternalProps {
    fieldController: BaseControlObject<any, any>;
    browserIs?: ResponsiveTypes;
    isHidden?: boolean;
    accessRule?: AccessStatus;
    onFocus: (screenId: string, elementId: string, row: string, nestedField: string) => void;
    fixedHeight?: number;
    isUsingInfiniteScroll?: boolean;
}

/**
 * Main field type router. Depending on the field type, it renders the corresponding component to the DOM
 */
export class FieldWrapper extends React.Component<FieldWrapperProps> {
    /** A field is hidden if it is hidden by its `isHidden` decorator property or if the field is unavailable for
     * the current user based on the access rights computation.
     **/
    private readonly isHidden = (): boolean =>
        this.props.isParentHidden || this.props.isHidden || this.props.accessRule === 'unavailable';

    private readonly renderField = (): JSX.Element => {
        const props = {
            contextType: this.props.contextType,
            elementId: this.props.item!.$bind,
            item: this.props.item!,
            nestedReadOnlyField: this.props.nestedReadOnlyField,
            screenId: this.props.screenId,
            availableColumns: this.props.availableColumns,
            isParentDisabled:
                this.isHidden() || this.props.isParentDisabled || this.props.accessRule === 'unauthorized',
            fixedHeight: this.props.fixedHeight,
            isUsingInfiniteScroll: this.props.isUsingInfiniteScroll,
            onFocus: this.props.onFocus,
        };

        switch (this.props.fieldController.componentType) {
            case FieldKey.Aggregate:
                return <AsyncConnectedAggregateComponent {...props} />;
            case FieldKey.Button:
                return <AsyncConnectedButtonComponent {...props} />;
            case FieldKey.Card:
                return <AsyncConnectedCardComponent {...props} />;
            case FieldKey.Calendar:
                return <AsyncConnectedCalendarComponent {...props} />;
            case FieldKey.Chart:
                return <AsyncConnectedChartComponent {...props} />;
            case FieldKey.Checkbox:
                return <AsyncConnectedCheckboxComponent {...props} />;
            case FieldKey.ContentTable:
                return <AsyncConnectedContentTableComponent {...props} />;
            case FieldKey.Count:
                return <AsyncConnectedCountComponent {...props} />;
            case FieldKey.DetailList:
                return <AsyncConnectedDetailListComponent {...props} />;
            case FieldKey.Date:
                return <AsyncConnectedDateComponent {...props} />;
            case FieldKey.DynamicPod:
                return <AsyncConnectedDynamicPodComponent {...props} />;
            case FieldKey.DynamicSelect:
                return <AsyncConnectedDynamicSelectComponent {...props} />;
            case FieldKey.DropdownList:
                return <AsyncConnectedDropdownListComponent {...props} />;
            case FieldKey.File:
                return <AsyncConnectedFileComponent {...props} />;
            case FieldKey.FileDeposit:
                return <AsyncConnectedFileDepositComponent {...props} />;
            case FieldKey.FilterEditor:
                return <AsyncConnectedFilterEditorComponent {...props} />;
            case FieldKey.FilterSelect:
                return <AsyncConnectedFilterSelectComponent {...props} />;
            case FieldKey.FormDesigner:
                return <AsyncConnectedFormDesignerComponent {...props} />;
            case FieldKey.Icon:
                return <AsyncConnectedIconComponent {...props} />;
            case FieldKey.Image:
                return <AsyncConnectedImageComponent {...props} />;
            case FieldKey.Label:
                return <AsyncConnectedLabelComponent {...props} />;
            case FieldKey.Link:
                return <AsyncConnectedLinkComponent {...props} />;
            case FieldKey.Message:
                return <AsyncConnectedMessageComponent {...props} />;
            case FieldKey.MultiDropdown:
                return <AsyncConnectedMultiDropdownComponent {...props} />;
            case FieldKey.MultiFileDeposit:
                return <AsyncConnectedMultiFileDepositComponent {...props} />;
            case FieldKey.MultiReference:
                return <AsyncConnectedMultiReferenceComponent {...props} />;
            case FieldKey.NestedGrid:
                return <AsyncConnectedNestedGridComponent {...props} />;
            case FieldKey.Numeric:
                return <AsyncConnectedNumericComponent {...props} />;
            case FieldKey.Preview:
                return <AsyncConnectedPreviewComponent {...props} />;
            case FieldKey.Pod:
                return <AsyncConnectedPodComponent {...props} />;
            case FieldKey.Plugin:
                return <AsyncConnectedPluginComponent {...props} />;
            case FieldKey.Progress:
                return <AsyncConnectedProgressComponent {...props} />;
            case FieldKey.Radio:
                return <AsyncConnectedRadioComponent {...props} />;
            case FieldKey.Reference:
                return <AsyncConnectedReferenceComponent {...props} />;
            case FieldKey.RelativeDate:
                return <AsyncConnectedRelativeDateComponent {...props} />;
            case FieldKey.RichText:
                return <AsyncConnectedRichTextComponent {...props} />;
            case FieldKey.PodCollection:
                return <AsyncConnectedPodCollectionComponent {...props} />;
            case FieldKey.Select:
                return <AsyncConnectedSelectComponent {...props} />;
            case FieldKey.SelectionCard:
                return <AsyncConnectedSelectionCardComponent {...props} />;
            case FieldKey.Separator:
                return <AsyncConnectedSeparatorComponent {...props} />;
            case FieldKey.StaticContent:
                return <AsyncConnectedStaticContentComponent {...props} />;
            case FieldKey.StepSequence:
                return <AsyncConnectedStepSequenceComponent {...props} />;
            case FieldKey.Switch:
                return <AsyncConnectedSwitchComponent {...props} />;
            case FieldKey.Table:
                return <AsyncConnectedTableComponent {...props} />;
            case FieldKey.TableSummary:
                return <AsyncConnectedTableSummaryComponent {...props} />;
            case FieldKey.Text:
                return <AsyncConnectedTextComponent {...props} />;
            case FieldKey.TextArea:
                return <AsyncConnectedTextAreaComponent {...props} />;
            case FieldKey.Time:
                return <AsyncConnectedTimeComponent {...props} />;
            case FieldKey.Datetime:
                return <AsyncConnectedDatetimeComponent {...props} />;
            case FieldKey.DatetimeRange:
                return <AsyncConnectedDatetimeRangeComponent {...props} />;
            case FieldKey.Toggle:
                return <AsyncConnectedToggleComponent {...props} />;
            case FieldKey.Tree:
                return <AsyncConnectedTreeComponent {...props} />;
            case FieldKey.NodeBrowserTree:
                return <AsyncConnectedNodeBrowserTreeComponent {...props} />;
            case FieldKey.VisualProcess:
                return <AsyncConnectedVisualProcessComponent {...props} />;
            case FieldKey.VitalPod:
                return <AsyncConnectedVitalPodComponent {...props} />;
            case FieldKey.Workflow:
                return <AsyncConnectedWorkflowComponent {...props} />;
            default:
                return (
                    <div>
                        Unhandled field:
                        {this.props.fieldController.componentType}
                    </div>
                );
        }
    };

    render(): React.ReactNode {
        // In case of a Technical Field we don't render anything in the UI.
        if (this.props.fieldController.componentType === FieldKey.TechnicalJson) {
            return null;
        }
        if (this.props.nestedReadOnlyField) {
            return this.renderField();
        }
        if (this.props.browserIs && this.props.item) {
            const columnSpan =
                this.props.item.$containerType === 'section'
                    ? calculateContainerWidth(
                          this.props.browserIs,
                          this.props.availableColumns || 12,
                          this.props.item.$columnWidth as ContainerWidth,
                      )
                    : calculateFieldWidth(
                          this.props.browserIs,
                          this.props.fieldController.componentType,
                          this.props.availableColumns,
                          this.props.item.$isFullWidth,
                          this.props.item.$columnWidth as FieldWidth,
                      );

            const gridColumnClasses = ['e-field-grid-column'];

            if (this.isHidden()) {
                /**
                 * Even if a field is hidden, we still need to render it to the DOM because some (especially reference field types) may render
                 * a lookup dialog which should be rendered if opened even if the field body is hidden.
                 **/
                gridColumnClasses.push('e-field-grid-column-hidden');
            }

            return (
                <GridColumn className={gridColumnClasses.join(' ')} columnSpan={columnSpan}>
                    {this.renderField()}
                </GridColumn>
            );
        }
        if (isDevMode()) {
            xtremConsole.warn("You need to provide the state's responsive browser object to the field wrapper");
        }
        return null;
    }
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: FieldWrapperExternalProps): FieldWrapperProps => {
    const elementId = props.item!.$bind;
    const screenDefinition = state.screenDefinitions[props.screenId];
    const fieldProperties = screenDefinition.metadata.uiComponentProperties[elementId] as ReadonlyFieldProperties;
    const pageProperties = getPagePropertiesFromPageDefinition(screenDefinition as PageDefinition);
    const accessBindings = (screenDefinition as PageDefinition).accessBindings || {};
    const accessRule = getElementAccessStatus({
        accessBindings,
        bind: elementId,
        elementProperties: fieldProperties,
        contextNode: pageProperties?.node,
        nodeTypes: state.nodeTypes,
        dataTypes: state.dataTypes,
    });

    return {
        ...props,
        browserIs: state.browser.is,
        accessRule,
        isHidden: resolveByValue({
            screenId: props.screenId,
            skipHexFormat: true,
            propertyValue: fieldProperties.isHidden,
            rowValue: null, // Nested fields don't use the field wrapper, they are rendered via the NestedFieldWrapper component
        }),
        fieldController: screenDefinition.metadata.controlObjects[elementId] as BaseControlObject<any, any>,
        onFocus: xtremRedux.actions.actionStub,
    };
};

const mapDispatchToProps = (dispatch: xtremRedux.AppThunkDispatch): Partial<FieldWrapperProps> => ({
    onFocus: (screenId: string, elementId: string, row: string, nestedField: string): any =>
        dispatch(xtremRedux.actions.setFocusPosition(screenId, elementId, row, nestedField)),
});

export default connect(mapStateToProps, mapDispatchToProps)(FieldWrapper);
