import * as React from 'react';
import { ConnectedBlockComponent } from '../component/container/block/block-component';
import { AsyncGridRowBlockComponent } from '../component/container/grid-row-block/async-grid-row-block';
import { ConnectedSectionComponent } from '../component/container/section/section-component';
import { ConnectedTileComponent } from '../component/container/tile/tile-component';
import type { PageArticleItem } from '../service/layout-types';
import type { ContextType } from '../types';
// eslint-disable-next-line import/no-named-as-default
import FieldWrapper from './field-wrapper';

export class RenderingRouterProps {
    availableColumns: number;

    // eslint-disable-next-line react/static-property-placement
    contextType?: ContextType;

    item: Partial<PageArticleItem>;

    screenId: string;

    isParentDisabled?: boolean;

    /**
     * Indicates if any of the parents in the layout structure is hidden, it is required so we can cascade
     * down the hidden status and mark the hidden inputs not focusable
     * */
    isParentHidden?: boolean;

    fixedHeight?: number;

    hasFooter?: boolean;

    isUsingInfiniteScroll?: boolean;
}

export const RenderingRouter: React.FC<RenderingRouterProps> = React.memo(
    (props: RenderingRouterProps): JSX.Element => {
        if (props.item.$bind) {
            return (
                <FieldWrapper
                    screenId={props.screenId}
                    item={props.item as PageArticleItem}
                    contextType={props.contextType}
                    availableColumns={props.availableColumns}
                    isParentDisabled={props.isParentDisabled}
                    isParentHidden={props.isParentHidden}
                    fixedHeight={props.fixedHeight}
                    isUsingInfiniteScroll={props.isUsingInfiniteScroll}
                />
            );
        }
        if (props.item.$layout) {
            switch (props.item.$category) {
                case 'section':
                    return (
                        <ConnectedSectionComponent
                            availableColumns={props.availableColumns}
                            contextType={props.contextType}
                            fixedHeight={props.fixedHeight}
                            hasFooter={props.hasFooter}
                            isParentDisabled={props.isParentDisabled}
                            isParentHidden={props.isParentHidden}
                            item={props.item}
                            screenId={props.screenId}
                            isUsingInfiniteScroll={props.isUsingInfiniteScroll}
                        />
                    );
                case 'block':
                    return (
                        <ConnectedBlockComponent
                            screenId={props.screenId}
                            item={props.item}
                            contextType={props.contextType}
                            availableColumns={props.availableColumns}
                            isParentDisabled={props.isParentDisabled}
                            isParentHidden={props.isParentHidden}
                        />
                    );
                case 'tile':
                    return (
                        <ConnectedTileComponent
                            screenId={props.screenId}
                            item={props.item}
                            contextType={props.contextType}
                            availableColumns={props.availableColumns}
                            isParentDisabled={props.isParentDisabled}
                            isParentHidden={props.isParentHidden}
                        />
                    );
                case 'grid-row-block':
                    return (
                        <AsyncGridRowBlockComponent
                            screenId={props.screenId}
                            item={props.item}
                            contextType={props.contextType}
                            availableColumns={props.availableColumns}
                            isParentDisabled={props.isParentDisabled}
                            isParentHidden={props.isParentHidden}
                        />
                    );
                default:
                    return (
                        <div>
                            Unhandled container:
                            {props.item.$category}
                        </div>
                    );
            }
        } else {
            throw new Error(`Invalid article item:${JSON.stringify(props.item, null, 4)}`);
        }
    },
);
RenderingRouter.displayName = 'Accordion';
