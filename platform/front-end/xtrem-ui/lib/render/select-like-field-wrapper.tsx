import type { EditableFieldProperties } from '../component/editable-field-control-object';
import type {
    BaseEditableComponentProperties,
    NestedFieldsAdditionalProperties,
} from '../component/field/field-base-component-types';
import * as React from 'react';
import { connect } from 'react-redux';
import type { Dict } from '@sage/xtrem-shared';
import type * as xtremRedux from '../redux';
import { addOptionsAndLocalizationToProps } from '../utils/transformers';
import type { HasOptionType } from '../component/field/traits';
import { FieldKey } from '../component/types';
import { AsyncSelectComponent } from '../component/field/select/async-select-component';
import { AsyncDropdownListComponent } from '../component/field/dropdown-list/async-dropdown-list-component';
import { AsyncRadioComponent } from '../component/field/radio/async-radio-component';
import { AsyncMultiDropdownComponent } from '../component/field/multi-dropdown/async-multi-dropdown-component';

type fieldType = FieldKey.Select | FieldKey.DropdownList | FieldKey.Radio | FieldKey.MultiDropdown;
interface SelectLikeFieldWrapperExternalProps
    extends BaseEditableComponentProperties<
        EditableFieldProperties & HasOptionType,
        any,
        NestedFieldsAdditionalProperties
    > {
    fieldType: fieldType;
}
export interface SelectLikeFieldWrapperProps extends SelectLikeFieldWrapperExternalProps {
    localizedOptions?: Dict<string>;
    enumOptions?: string[];
}

export function SelectLikeFieldWrapper(props: SelectLikeFieldWrapperProps): React.ReactElement {
    switch (props.fieldType) {
        case FieldKey.Select:
            return <AsyncSelectComponent {...props} />;
        case FieldKey.DropdownList:
            return <AsyncDropdownListComponent {...props} />;
        case FieldKey.Radio:
            return <AsyncRadioComponent {...props} />;
        case FieldKey.MultiDropdown:
            return <AsyncMultiDropdownComponent {...props} />;
        default:
            return (
                <div>
                    Unsupported type:
                    {props.fieldType}
                </div>
            );
    }
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: SelectLikeFieldWrapperExternalProps,
): SelectLikeFieldWrapperProps => {
    return addOptionsAndLocalizationToProps(state, props);
};

export const ConnectedSelectLikeFieldWrapper = connect(mapStateToProps)(SelectLikeFieldWrapper);
