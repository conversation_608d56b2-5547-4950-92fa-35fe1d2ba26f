import type { AccessStatus, LocalizeLocale } from '@sage/xtrem-shared';
import * as React from 'react';
import { connect } from 'react-redux';
import type { AggregateProperties, DynamicSelectProperties } from '../component/control-objects';
import type { EditableFieldProperties } from '../component/editable-field-control-object';
import { AsyncAggregateComponent } from '../component/field/aggregate/async-aggregate-component';
import { AsyncCheckboxComponent } from '../component/field/checkbox/async-checkbox-component';
import { AsyncCountComponent } from '../component/field/count/async-count-component';
import { AsyncDateComponent } from '../component/field/date/async-date-component';
import type {
    BaseEditableComponentProperties,
    BaseReadonlyComponentProperties,
    NestedFieldsAdditionalProperties,
} from '../component/field/field-base-component-types';
import type { MultiReferenceComponentProps } from '../component/field/multi-reference/multi-reference-types';
import { AsyncProgressComponent } from '../component/field/progress/async-progress-component';
import type { ReferenceComponentProps } from '../component/field/reference/reference-types';
import type { NestedField, NestedFieldHandlersArguments, NestedFieldTypes } from '../component/nested-fields';
import type { ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import * as xtremRedux from '../redux';
import type { FocusPosition } from '../redux/state';
import type { ScreenBase } from '../service/screen-base';
import type { ValidationResult } from '../service/screen-base-definition';
import { executeValidationRulesOnField } from '../service/validation-service';
import type { ContextType, NestedRecordId, NodePropertyType } from '../types';
import { getElementAccessStatus } from '../utils/access-utils';
import { getPageDefinitionFromState } from '../utils/state-utils';
import { AsyncReferenceComponent } from '../component/field/reference/async-reference-component';
import { AsyncTextAreaComponent } from '../component/field/text-area/async-text-area-component';
import { AsyncTextComponent } from '../component/field/text/async-text-component';
import { AsyncSwitchComponent } from '../component/field/switch/async-switch-component';
import { AsyncLinkComponent } from '../component/field/link/async-link-component';
import { AsyncNumericComponent } from '../component/field/numeric/async-numeric-component';
import { AsyncIconComponent } from '../component/field/icon/async-icon-component';
import { AsyncLabelComponent } from '../component/field/label/async-label-component';
import { AsyncImageComponent } from '../component/field/image/async-image-component';
import { AsyncMultiReferenceComponent } from '../component/field/multi-reference/async-multi-reference-component';
import { AsyncRelativeDateComponent } from '../component/field/relative-date/async-relative-date-component';
import { FieldKey } from '../component/types';
import { normalizeUnderscoreBind } from '../utils/abstract-fields-utils';
import { ConnectedSelectLikeFieldWrapper } from './select-like-field-wrapper';
import { AsyncFilterSelectComponent } from '../component/field/filter-select/async-filter-select-component';
import type { FilterSelectComponentProps } from '../component/field/filter-select/filter-select-types';
import type { QueryParameters } from '../utils/types';
import { AsyncDatetimeRangeComponent } from '../component/field/datetime-range/async-datetime-range-component';
import { AsyncDynamicSelectComponent } from '../component/field/dynamic-select/async-dynamic-select-component';
import { AsyncDatetimeComponent } from '../component/field/datetime/async-datetime-component';

export interface NestedFieldWrapperContextProps {
    _id: NestedRecordId;
    contextType?: ContextType;
    contextNode?: NodePropertyType;
    handlersArguments?: NestedFieldHandlersArguments;
    screenId: string;
    parentElementId: string;
    setFieldValue: (bind: string, value: any) => Promise<void>;
    recordContext?: any;
}

export interface NestedFieldWrapperExternalProps extends NestedFieldWrapperContextProps {
    columnDefinition: NestedField<ScreenBase, NestedFieldTypes>;
    columnName: string;
    columnProperties: ReadonlyFieldProperties;
    focusPosition?: FocusPosition | null;
    isParentReadOnly?: boolean;
    nestedReadOnlyField?: boolean;
    shouldRenderLabelInNestedReadOnlyMode?: boolean;
    validate?: (columnName: string, value: any) => Promise<ValidationResult[]>;
    validationErrors?: ValidationResult[];
    value: any;
}

export interface NestedFieldWrapperProps extends NestedFieldWrapperExternalProps {
    focusPosition?: FocusPosition | null;
    accessRule?: AccessStatus;
    locale: LocalizeLocale;
    onFocus: (screenId: string, elementId: string, row: string, nestedField: string) => void;
    navigate: (path: string, queryParameters?: QueryParameters) => void;
    updateNestedFieldValidationErrors: (
        screenId: string,
        elementId: string,
        errors: ValidationResult[],
        columnName: string,
        recordId?: string,
    ) => void;
}

export class NestedFieldWrapper extends React.Component<NestedFieldWrapperProps> {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    removeNonNestedErrors = (_bind: string): undefined => {
        return undefined;
    };

    validate = async (columnName: string, value: any): Promise<ValidationResult[]> => {
        if (this.props.validate) {
            return this.props.validate(columnName, value);
        }

        const result = await executeValidationRulesOnField({
            screenId: this.props.screenId,
            elementId: this.props.parentElementId,
            value,
            columnId: columnName,
            fieldProperties: this.props.columnDefinition.properties as any,
            recordId: this.props._id,
            rowValue: this.props.recordContext,
        });

        const filteredResult = result.filter(v => !!v.message);

        this.props.updateNestedFieldValidationErrors(
            this.props.screenId,
            this.props.parentElementId,
            filteredResult,
            columnName,
            this.props._id,
        );

        return filteredResult;
    };

    renderElement(): JSX.Element | undefined {
        const readOnlyProperties: BaseReadonlyComponentProperties<
            ReadonlyFieldProperties,
            any,
            NestedFieldsAdditionalProperties
        > = {
            contextType: this.props.contextType,
            elementId: this.props.columnName,
            columnDefinition: this.props.columnDefinition,
            fieldProperties: this.props.columnProperties,
            handlersArguments: this.props.handlersArguments,
            screenId: this.props.screenId,
            nestedReadOnlyField: this.props.nestedReadOnlyField,
            shouldRenderLabelInNestedReadOnlyMode: this.props.shouldRenderLabelInNestedReadOnlyMode,
            value: this.props.value,
            locale: this.props.locale,
            availableColumns: 1,
            parentElementId: this.props.parentElementId,
            // CC: This was never being called at the time of development (X3-180847)
            onFocus: () => {
                this.props.onFocus(
                    this.props.screenId,
                    this.props.parentElementId,
                    this.props._id,
                    this.props.columnName,
                );
            },
            isInFocus:
                !!this.props.focusPosition &&
                this.props.screenId === this.props.focusPosition.screenId &&
                this.props.parentElementId === this.props.focusPosition.elementId &&
                this.props.columnName === this.props.focusPosition.nestedField &&
                this.props._id === this.props.focusPosition.row,
            isNested: true,
            isParentDisabled: this.props.accessRule === 'unauthorized',
        };

        const editableProperties: BaseEditableComponentProperties<
            EditableFieldProperties,
            any,
            NestedFieldsAdditionalProperties
        > = {
            ...readOnlyProperties,
            setFieldValue: this.props.setFieldValue,
            removeNonNestedErrors: this.removeNonNestedErrors,
            validate: this.validate,
            recordContext: this.props.recordContext,
            validationErrors:
                this.props.validationErrors?.filter(
                    e => (e.columnId || e.recordId) === normalizeUnderscoreBind(this.props.columnName),
                ) || [],
            isParentReadOnly: this.props.isParentReadOnly,
        };

        switch (this.props.columnDefinition.type) {
            case FieldKey.Aggregate:
                const aggregateProperties = {
                    ...readOnlyProperties,
                    fieldProperties: readOnlyProperties.fieldProperties as AggregateProperties<any>,
                };
                return <AsyncAggregateComponent {...aggregateProperties} />;
            case FieldKey.Checkbox:
                return <AsyncCheckboxComponent {...editableProperties} />;
            case FieldKey.Count:
                return <AsyncCountComponent {...readOnlyProperties} />;
            case FieldKey.Date:
                return <AsyncDateComponent {...editableProperties} />;
            case FieldKey.DatetimeRange:
                return <AsyncDatetimeRangeComponent {...editableProperties} />;
            case FieldKey.DynamicSelect:
                const dynamicSelectProperties = {
                    ...editableProperties,
                    fieldProperties: readOnlyProperties.fieldProperties as DynamicSelectProperties<any>,
                };
                return <AsyncDynamicSelectComponent {...dynamicSelectProperties} />;
            case FieldKey.Progress:
                return <AsyncProgressComponent {...readOnlyProperties} />;
            case FieldKey.Icon:
                return <AsyncIconComponent {...readOnlyProperties} />;
            case FieldKey.Image:
                return <AsyncImageComponent {...editableProperties} />;
            case FieldKey.Label:
                return <AsyncLabelComponent {...readOnlyProperties} />;
            case FieldKey.Link:
                return <AsyncLinkComponent {...readOnlyProperties} />;
            case FieldKey.MultiReference:
                return (
                    <AsyncMultiReferenceComponent
                        {...(editableProperties as MultiReferenceComponentProps)}
                        contextNode={this.props.contextNode}
                    />
                );
            case FieldKey.Numeric:
                return <AsyncNumericComponent {...editableProperties} />;
            case FieldKey.Reference:
                return (
                    <AsyncReferenceComponent
                        {...(editableProperties as ReferenceComponentProps)}
                        contextNode={this.props.contextNode}
                    />
                );
            case FieldKey.Select:
            case FieldKey.MultiDropdown:
            case FieldKey.DropdownList:
                return (
                    <ConnectedSelectLikeFieldWrapper
                        {...editableProperties}
                        fieldType={this.props.columnDefinition.type}
                    />
                );
            case FieldKey.Datetime:
                return <AsyncDatetimeComponent {...editableProperties} />;
            case FieldKey.Switch:
                return <AsyncSwitchComponent {...editableProperties} />;
            case FieldKey.Text:
                return <AsyncTextComponent {...editableProperties} />;
            case FieldKey.TextArea:
                return <AsyncTextAreaComponent {...editableProperties} />;
            case FieldKey.RelativeDate:
                return <AsyncRelativeDateComponent {...readOnlyProperties} />;
            case FieldKey.FilterSelect:
                return (
                    <AsyncFilterSelectComponent
                        {...(editableProperties as FilterSelectComponentProps)}
                        recordContext={this.props.recordContext}
                    />
                );
            default:
                return (
                    <div>
                        Unhandled field:
                        {this.props.columnDefinition.type}
                    </div>
                );
        }
    }

    render(): React.ReactNode {
        if (this.props.accessRule === 'unavailable') {
            return null;
        }

        return (
            <div className={`e-field-nested e-field-nested-${this.props._id}-${this.props.columnName}`}>
                {this.renderElement()}
            </div>
        );
    }
}
const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: NestedFieldWrapperExternalProps,
): NestedFieldWrapperProps => {
    const accessBindings = getPageDefinitionFromState(props.screenId, state).accessBindings || {};
    const accessRule = getElementAccessStatus({
        accessBindings,
        bind: props.columnProperties.bind!,
        elementProperties: props.columnProperties,
        contextNode: props.contextNode,
        nodeTypes: state.nodeTypes,
        dataTypes: state.dataTypes,
    });

    return {
        ...props,
        accessRule,
        focusPosition: props.focusPosition !== undefined ? props.focusPosition : state.focusPosition,
        locale: (state.applicationContext?.locale as LocalizeLocale) || 'base',
        updateNestedFieldValidationErrors: xtremRedux.actions.actionStub,
        navigate: xtremRedux.actions.actionStub,
        onFocus: xtremRedux.actions.actionStub,
    };
};
const mapDispatchToProps = (dispatch: xtremRedux.AppThunkDispatch): Partial<NestedFieldWrapperProps> => ({
    updateNestedFieldValidationErrors: (
        screenId: string,
        elementId: string,
        errors: ValidationResult[],
        columnName: string,
        recordId?: string,
    ): void => {
        dispatch(
            xtremRedux.actions.updateNestedFieldValidationErrors(screenId, elementId, errors, columnName, recordId),
        );
    },
    navigate: (path: string, queryParameters: QueryParameters = {}): Promise<void> =>
        dispatch(xtremRedux.actions.navigate(path, queryParameters)),
    onFocus: (screenId: string, elementId: string, row: string, nestedField: string): any =>
        dispatch(xtremRedux.actions.setFocusPosition(screenId, elementId, row, nestedField)),
});

export const ConnectedNestedFieldWrapper = connect(mapStateToProps, mapDispatchToProps)(NestedFieldWrapper);
