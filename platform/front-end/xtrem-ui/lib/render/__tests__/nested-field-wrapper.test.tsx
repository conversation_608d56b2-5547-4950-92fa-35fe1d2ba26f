jest.mock('../../component/field/text/async-text-component');
jest.mock('../../component/field/button/async-button-component');
jest.mock('../../component/field/numeric/async-numeric-component');
jest.mock('../../component/field/progress/async-progress-component');
jest.mock('../../component/field/text-area/async-text-area-component');
jest.mock('../../component/field/select/async-select-component');
jest.mock('../../component/field/reference/async-reference-component');
jest.mock('../../component/field/separator/async-separator-component');
jest.mock('../../component/field/select/async-select-component');
jest.mock('../../component/field/link/async-link-component');
jest.mock('../../component/field/image/async-image-component');
jest.mock('../../component/field/icon/async-icon-component');
jest.mock('../../component/field/date/async-date-component');
jest.mock('../../component/field/checkbox/async-checkbox-component');
jest.mock('../../component/field/label/async-label-component');

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { Page } from '../../service/page';
import { getMockPageDefinition, getMockState, getMockStore } from '../../__tests__/test-helpers';
import { ImageComponent } from '../../component/field/image/image-component';
import type { ImageValue } from '../../component/field/image/image-types';
import type {
    NestedField,
    NestedFieldHandlersArguments,
    NestedFieldsProperties,
    NestedFieldTypesWithoutTechnical,
} from '../../component/nested-fields';
import type { XtremAppState } from '../../redux';
import * as graphqlService from '../../service/graphql-service';
import * as events from '../../utils/events';
import { ConnectedNestedFieldWrapper } from '../nested-field-wrapper';
import type { PageDefinition } from '../../service/page-definition';
import { IconComponent } from '../../component/field/icon/icon-component';
import { LinkComponent } from '../../component/field/link/link-component';
import { NumericComponent } from '../../component/field/numeric/numeric-component';
import { ProgressComponent } from '../../component/field/progress/progress-component';
import { ReferenceComponent } from '../../component/field/reference/reference-component';
import { SelectComponent } from '../../component/field/select/select-component';
import { TextComponent } from '../../component/field/text/text-component';
import { LabelComponent } from '../../component/ui/label/label-component';
import { FieldKey } from '../../component/types';
import { DateComponent } from '../../component/field/date/date-component';
import { fireEvent, render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { GraphQLTypes } from '@sage/xtrem-shared';
import { GraphQLKind } from '../../types';

describe('Nested Field wrapper', () => {
    type FieldValueTypes = string | number | ImageValue;
    const screenId = 'TestPage';
    let state: XtremAppState;
    let store: MockStoreEnhanced<XtremAppState>;
    const fieldId = 'test-id';
    const testColumnId = 'test-column';
    const parentElementId = 'parent-element-id';
    const handlersArguments: NestedFieldHandlersArguments = {
        onClick: ['sample-argument'],
    };
    const nestedProperties: NestedFieldsProperties<NestedFieldTypesWithoutTechnical, Page> = {
        bind: testColumnId,
        title: 'Column 1',
        onClick: jest.fn(),
    };
    const imageValue: ImageValue = {
        value: `/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDADIiJSwlHzIsKSw4NTI7S31RS0VFS5ltc1p9tZ++u7Kfr6zI4f/zyNT/
        16yv+v/9////////wfD/////////////2wBDATU4OEtCS5NRUZP/zq/O////////////////////////////////////////////////////////////////
        ///wAARCAAYAEADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAAAQMAAgQF/8QAJRABAAIBBAEEAgMAAAAAAAAAAQIRAAMSITEEEyJBgTORUWFx/8QAA
        EBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AOgM52xQDrjvAV5Xv0vfKUALlTQfeBm0HThMNHXkL0Lw/swN5g
        A8yT4MCS1OEOJV8mBz9Z05yfW8iSx7p4j+jA1aD6Wj7ZMzstsfvAas4UyRHvjrAkC9KhpLMClQntlqFc2X1gUj4viwVObKrddH9YDoHvuujAEuNV+bLwFS8x
        dSr+Cq3Vf+4F5RgQl6ZR2p1eAzU/HX80YBYyJLCuexwJCO2O1bwCRidAfWBSctswbI12GAJT3yiwFR7+MBjGK2g/WAJR3FdF84E2rK5VR0YH/9k=`,
    };
    const emptySetFieldValue = () => Promise.resolve();

    const getNestedFieldWrapperComponent = (
        value: FieldValueTypes,
        type: NestedFieldTypesWithoutTechnical,
        properties: NestedFieldsProperties<NestedFieldTypesWithoutTechnical, Page>,
    ): React.ReactElement<any, any> => {
        const columnDefinition: NestedField<Page, NestedFieldTypesWithoutTechnical> = {
            defaultUiProperties: { ...properties },
            properties,
            type,
        };
        return (
            <Provider store={store}>
                <ConnectedNestedFieldWrapper
                    _id={fieldId}
                    columnDefinition={columnDefinition as any}
                    columnName={properties.bind as string}
                    columnProperties={properties as any}
                    handlersArguments={handlersArguments}
                    screenId={screenId}
                    setFieldValue={emptySetFieldValue}
                    value={value}
                    parentElementId={parentElementId}
                    contextNode="@sage/xtrem-test/MyNode"
                />
            </Provider>
        );
    };

    const renderNestedField = (
        value: FieldValueTypes,
        fieldType: NestedFieldTypesWithoutTechnical,
        properties: NestedFieldsProperties<NestedFieldTypesWithoutTechnical, Page>,
    ) => {
        return render(getNestedFieldWrapperComponent(value, fieldType, properties));
    };

    beforeEach(() => {
        state = getMockState();
        state.nodeTypes = {
            MyNode: {
                name: 'MyNode',
                title: 'MyNode',
                packageName: '@sage/xtrem-test',
                properties: {
                    [testColumnId]: {
                        name: testColumnId,
                        type: GraphQLTypes.String,
                        kind: GraphQLKind.Scalar,
                    },
                },
                mutations: {},
            },
        };
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        store = getMockStore(state);
        jest.spyOn(graphqlService, 'fetchReferenceFieldSuggestions').mockImplementation(() => Promise.resolve([]));
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    const defaultFieldSelector = (componentName: string) =>
        `div[data-testid~="e-${componentName}-field"][data-testid~="e-field-bind-test-column"]`;
    const nestedComponentSelectors = {
        date: { fieldType: FieldKey.Date, value: '2020-02-06', componentClass: DateComponent },
        icon: {
            selector: `${defaultFieldSelector('icon')} span[data-component="icon"]`,
            fieldType: FieldKey.Icon,
            value: 'icon',
            componentClass: IconComponent,
        },
        image: {
            selector: `${defaultFieldSelector('image')} div span`,
            fieldType: FieldKey.Image,
            value: imageValue,
            componentClass: ImageComponent,
        },
        label: {
            selector: `${defaultFieldSelector('label')} span span`,
            fieldType: FieldKey.Label,
            value: 'label',
            componentClass: LabelComponent,
        },
        link: { fieldType: FieldKey.Link, value: 'link', componentClass: LinkComponent },
        numeric: {
            selector: `${defaultFieldSelector('numeric')} input`,
            fieldType: FieldKey.Numeric,
            value: '100',
            componentClass: NumericComponent,
        },
        progress: {
            selector: `${defaultFieldSelector('progress')} div`,
            fieldType: FieldKey.Progress,
            value: 1,
            componentClass: ProgressComponent,
        },
        reference: {
            selector: `${defaultFieldSelector('reference')} input`,
            fieldType: FieldKey.Reference,
            value: 'reference',
            componentClass: ReferenceComponent,
        },
        select: {
            // TODO: selector property commented as this way, for select field, we can not test triggerEventHandler call.
            // Find a way to test this call as the rest of the fields
            // selector: `${defaultFieldSelector('select')} input`,
            fieldType: FieldKey.Select,
            value: 'select',
            componentClass: SelectComponent,
        },
        text: {
            selector: `${defaultFieldSelector('text')} input`,
            fieldType: FieldKey.Text,
            value: 'text',
            componentClass: TextComponent,
        },
    };

    describe('snapshots', () => {
        const executeTest = (value: FieldValueTypes, fieldType: NestedFieldTypesWithoutTechnical): void => {
            const { container } = renderNestedField(value, fieldType, nestedProperties);
            expect(container).toMatchSnapshot();
        };
        Object.keys(nestedComponentSelectors).forEach(element => {
            it(`should render a ${element} field`, () => {
                const { fieldType, value } = nestedComponentSelectors[element];
                executeTest(value, fieldType);
            });
        });
    });

    describe('interactions', () => {
        describe('nested fields - should trigger default onClick', () => {
            Object.keys(nestedComponentSelectors)
                .filter(key => nestedComponentSelectors[key].selector)
                .forEach(key => {
                    it(key, () => {
                        const { selector, fieldType, value } = nestedComponentSelectors[key];
                        const wrapper = renderNestedField(value, fieldType, nestedProperties);
                        expect(events.triggerNestedFieldEvent).not.toHaveBeenCalled();

                        const inputColumnOne = wrapper.container.querySelector(selector);

                        fireEvent.click(inputColumnOne);

                        expect(events.triggerNestedFieldEvent).toHaveBeenCalledWith(
                            screenId,
                            parentElementId,
                            nestedProperties,
                            'onClick',
                            'sample-argument',
                        );
                    });
                });
        });
    });

    describe('authorization', () => {
        beforeEach(() => {
            const pageDefinition = getMockPageDefinition(screenId) as PageDefinition;
            state.screenDefinitions[screenId] = pageDefinition;
            store = getMockStore(state);
        });

        it('should render if no authorization rights are applied', () => {
            const { fieldType, value, selector } = nestedComponentSelectors.text;
            const wrapper = renderNestedField(value, fieldType as NestedFieldTypesWithoutTechnical, nestedProperties);
            const input = wrapper.container.querySelector(selector);
            expect(input).not.toBeDisabled();
        });

        it('should be disabled if the field is unauthorized', () => {
            const pageDefinition = getMockPageDefinition(screenId, {
                accessBindings: {
                    MyNode: {
                        [testColumnId]: 'unauthorized',
                    },
                },
            }) as PageDefinition;
            state.screenDefinitions[screenId] = pageDefinition;
            store = getMockStore(state);
            const { fieldType, value, selector } = nestedComponentSelectors.text;
            const wrapper = renderNestedField(value, fieldType as NestedFieldTypesWithoutTechnical, nestedProperties);
            const input = wrapper.container.querySelector(selector);
            expect(input).toBeDisabled();
        });

        it('should not render if the field is unavailable', () => {
            const pageDefinition = getMockPageDefinition(screenId, {
                accessBindings: {
                    MyNode: {
                        [testColumnId]: 'unavailable',
                    },
                },
            }) as PageDefinition;
            state.screenDefinitions[screenId] = pageDefinition;
            store = getMockStore(state);
            const { fieldType, value } = nestedComponentSelectors.text;
            const { container } = renderNestedField(
                value,
                fieldType as NestedFieldTypesWithoutTechnical,
                nestedProperties,
            );
            expect(container.childElementCount).toBe(0);
        });
    });
});
