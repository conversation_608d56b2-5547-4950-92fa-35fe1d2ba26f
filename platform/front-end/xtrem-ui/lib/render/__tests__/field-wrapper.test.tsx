jest.mock('../../component/field/text/async-text-component');
jest.mock('../../component/field/button/async-button-component');
jest.mock('../../component/field/numeric/async-numeric-component');
jest.mock('../../component/field/progress/async-progress-component');
jest.mock('../../component/field/text-area/async-text-area-component');
jest.mock('../../component/field/select/async-select-component');
jest.mock('../../component/field/reference/async-reference-component');
jest.mock('../../component/field/separator/async-separator-component');
jest.mock('../../component/field/select/async-select-component');
jest.mock('../../component/field/link/async-link-component');
jest.mock('../../component/field/image/async-image-component');
jest.mock('../../component/field/icon/async-icon-component');
jest.mock('../../component/field/date/async-date-component');
jest.mock('../../component/field/checkbox/async-checkbox-component');
jest.mock('../../component/field/label/async-label-component');
jest.mock('../../component/field/chart/async-chart-component');

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../__tests__/test-helpers';
import { ChartTypes } from '../../component/chart-types';
import * as fieldControls from '../../component/control-objects';
import type { ChartDecoratorProperties, LinkDecoratorProperties } from '../../component/decorators';
import { FieldKey } from '../../component/types';
import type { XtremAppState } from '../../redux';
import type { ScreenBase } from '../../service/screen-base';
// eslint-disable-next-line import/no-named-as-default
import FieldWrapper from '../field-wrapper';
import type { FieldWrapperExternalProps } from '../../component/field/field-base-component-types';
import type { PageProperties } from '../../component/control-objects';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { GraphQLKind } from '../../types';

describe('Field wrapper', () => {
    const screenId = 'TestPage';
    let state: XtremAppState;
    let store: MockStoreEnhanced<XtremAppState>;
    const fieldId = 'test-field';
    beforeEach(() => {
        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        state.nodeTypes = {
            MyNode: {
                name: 'MyNode',
                title: 'MyNode',
                packageName: '@sage/xtrem-test',
                properties: {
                    [fieldId]: {
                        name: fieldId,
                        type: 'String',
                        kind: GraphQLKind.Scalar,
                        isOnInputType: true,
                        canFilter: true,
                    },
                    someOtherBind: {
                        name: 'someOtherBind',
                        type: 'String',
                        kind: GraphQLKind.Scalar,
                        isOnInputType: true,
                        canFilter: true,
                    },
                },
                mutations: {},
            },
            AnotherNode: {
                name: 'AnotherNode',
                title: 'AnotherNode',
                packageName: '@sage/xtrem-test',
                properties: {
                    [fieldId]: {
                        name: fieldId,
                        type: 'String',
                        kind: GraphQLKind.Scalar,
                        isOnInputType: true,
                        canFilter: true,
                    },
                    someOtherBind: {
                        name: 'someOtherBind',
                        type: 'String',
                        kind: GraphQLKind.Scalar,
                        isOnInputType: true,
                        canFilter: true,
                    },
                },
                mutations: {},
            },
        };
        store = getMockStore(state);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    const renderComponent = (item: { $bind: string }, props: Partial<FieldWrapperExternalProps> = {}) => {
        return render(
            <Provider store={store}>
                <FieldWrapper screenId={screenId} item={item} {...props} />
            </Provider>,
        );
    };

    it('should not render a hidden field', () => {
        const item = addFieldToState(FieldKey.Button, state, screenId, fieldId, {
            isHidden: true,
        });

        const wrapper = renderComponent(item);
        expect(
            wrapper.container.querySelector('.e-grid-column')!.classList.contains('e-field-grid-column-hidden'),
        ).toEqual(true);
    });

    it('should render the field if it is not hidden', () => {
        const item = addFieldToState(FieldKey.Button, state, screenId, fieldId, {
            isHidden: false,
        });

        const wrapper = renderComponent(item);
        expect(
            wrapper.container.querySelector('.e-grid-column')!.classList.contains('e-field-grid-column-hidden'),
        ).toEqual(false);
    });

    it('should render a button field', () => {
        const item = addFieldToState(FieldKey.Button, state, screenId, fieldId, {});
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelectorAll('.e-button-field')).toHaveLength(1);
    });

    it('should render a chart field', () => {
        const props: ChartDecoratorProperties<ScreenBase> = {
            chart: {
                type: ChartTypes.Bar,
                series: [],
                xAxis: {
                    defaultUiProperties: { ...fieldControls.TextControlObject.defaultUiProperties, bind: 'anything' },
                    properties: { bind: 'anything' },
                    type: FieldKey.Text,
                },
            },
        };
        const item = addFieldToState(FieldKey.Chart, state, screenId, fieldId, props, {
            data: [1, 2, 3],
        });
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-chart-field')).not.toBeNull();
    });

    it('should render a checkbox field', () => {
        const item = addFieldToState(FieldKey.Checkbox, state, screenId, fieldId, {}, true);
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-checkbox-field')).not.toBeNull();
    });

    it('should render a date field', () => {
        const item = addFieldToState(FieldKey.Date, state, screenId, fieldId, {}, '2020-02-06');
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-date-field')).not.toBeNull();
    });

    it('should render a icon field', () => {
        const item = addFieldToState(FieldKey.Icon, state, screenId, fieldId, {});
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-icon-field')).not.toBeNull();
    });

    it('should render a image field', () => {
        const imageValue = {
            value:
                '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDADIiJSwlHzIsKSw4NTI7S31RS0VFS5ltc1p9tZ++u7Kfr6zI4f/zyNT/' +
                '16yv+v/9////////wfD/////////////2wBDATU4OEtCS5NRUZP/zq/O/////////////////////////////////////////////////////////////////' +
                '///wAARCAAYAEADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAAAQMAAgQF/8QAJRABAAIBBAEEAgMAAAAAAAAAAQIRAAMSITEEEyJBgTORUWFx/8QAFA' +
                'EBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AOgM52xQDrjvAV5Xv0vfKUALlTQfeBm0HThMNHXkL0Lw/swN5qg' +
                'A8yT4MCS1OEOJV8mBz9Z05yfW8iSx7p4j+jA1aD6Wj7ZMzstsfvAas4UyRHvjrAkC9KhpLMClQntlqFc2X1gUj4viwVObKrddH9YDoHvuujAEuNV+bLwFS8Xx' +
                'dSr+Cq3Vf+4F5RgQl6ZR2p1eAzU/HX80YBYyJLCuexwJCO2O1bwCRidAfWBSctswbI12GAJT3yiwFR7+MBjGK2g/WAJR3FdF84E2rK5VR0YH/9k=',
        };
        const item = addFieldToState(FieldKey.Image, state, screenId, fieldId, {}, imageValue);
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-image-field')).not.toBeNull();
    });

    it('should render a label field', () => {
        const item = addFieldToState(FieldKey.Label, state, screenId, fieldId, {});
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-label-field')).not.toBeNull();
    });

    it('should render a link field', () => {
        const value: LinkDecoratorProperties<ScreenBase> = {
            page: 'TestPage',
        };
        const item = addFieldToState(FieldKey.Link, state, screenId, fieldId, value);
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-link-field')).not.toBeNull();
    });

    it('should render a numeric field', () => {
        const item = addFieldToState(FieldKey.Numeric, state, screenId, fieldId, {}, 1);
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-numeric-field')).not.toBeNull();
    });

    it('should render a progress field', () => {
        const item = addFieldToState(FieldKey.Progress, state, screenId, fieldId, {}, 1);
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-progress-field')).not.toBeNull();
    });

    it('should render a reference field', () => {
        const item = addFieldToState<FieldKey.Reference>(FieldKey.Reference, state, screenId, fieldId, {
            node: '@sage/xtrem-ui-test/test-property',
            valueField: 'name',
        });
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-reference-field')).not.toBeNull();
    });

    it('should render a select field', () => {
        const item = addFieldToState(
            FieldKey.Select,
            state,
            screenId,
            fieldId,
            {
                title: 'Select a value',
                options: ['Option1', 'Option2', 'Option3'],
            },
            '',
        );
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-select-field')).not.toBeNull();
    });

    it('should render a separator field', () => {
        const item = addFieldToState(FieldKey.Separator, state, screenId, fieldId, {});
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-separator-field')).not.toBeNull();
    });

    it('should render a text field', () => {
        const item = addFieldToState(FieldKey.Text, state, screenId, fieldId, {});
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelector('.e-text-field')).not.toBeNull();
    });

    it('should not disable the field if it is authorized', () => {
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
            accessBindings: {
                MyNode: {
                    [fieldId]: 'authorized',
                },
            },
        });
        (state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] as PageProperties<any>) = {
            node: '@sage/xtrem-test/MyNode',
        };
        getMockStore(state);
        const item = addFieldToState(FieldKey.Text, state, screenId, fieldId, {});
        const wrapper = renderComponent(item);
        expect(wrapper.queryByTestId('e-text-field-input')).not.toBeDisabled();
    });

    it('should disable the field if edit is unauthorized', () => {
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
            accessBindings: {
                MyNode: {
                    [fieldId]: 'unauthorized',
                },
            },
        });
        (state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] as PageProperties<any>) = {
            node: '@sage/xtrem-test/MyNode',
        };
        getMockStore(state);
        const item = addFieldToState(FieldKey.Text, state, screenId, fieldId, {});
        const wrapper = renderComponent(item);
        expect(wrapper.queryByTestId('e-text-field-input')).toBeDisabled();
    });

    it('should disable the field if edit is unauthorized by access node', () => {
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
            accessBindings: {
                MyNode: {
                    [fieldId]: 'authorized',
                },
                AnotherNode: {
                    someOtherBind: 'unauthorized',
                },
            },
        });
        (state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] as PageProperties<any>) = {
            node: '@sage/xtrem-test/MyNode',
        };

        getMockStore(state);
        const item = addFieldToState(FieldKey.Text, state, screenId, fieldId, {
            access: {
                node: '@sage/xtrem-test/AnotherNode',
                bind: 'someOtherBind',
            },
        });
        const wrapper = renderComponent(item);
        expect(wrapper.queryByTestId('e-text-field-input')).toBeDisabled();
    });

    it('should not disable the field if edit is authorized by access node', () => {
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
            accessBindings: {
                MyNode: {
                    [fieldId]: 'unauthorized',
                },
                AnotherNode: {
                    someOtherBind: 'authorized',
                },
            },
        });
        (state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] as PageProperties<any>) = {
            node: '@sage/xtrem-test/MyNode',
        };

        getMockStore(state);
        const item = addFieldToState(FieldKey.Text, state, screenId, fieldId, {
            access: {
                node: '@sage/xtrem-test/AnotherNode',
                bind: 'someOtherBind',
            },
        });
        const wrapper = renderComponent(item);
        expect(wrapper.queryByTestId('e-text-field-input')).not.toBeDisabled();
    });

    it('should check access rights for the filed by its bind value rather than element id', () => {
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
            accessBindings: {
                MyNode: {
                    [fieldId]: 'authorized',
                    someOtherBind: 'unauthorized',
                },
            },
        });
        (state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] as PageProperties<any>) = {
            node: '@sage/xtrem-test/MyNode',
        };

        getMockStore(state);
        const item = addFieldToState(FieldKey.Text, state, screenId, fieldId, {
            bind: 'someOtherBind',
        });
        const wrapper = renderComponent(item);
        expect(wrapper.queryByTestId('e-text-field-input')).toBeDisabled();
    });

    it('should hide the field if it is unavailable', () => {
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
            accessBindings: {
                MyNode: {
                    [fieldId]: 'unavailable',
                },
            },
        });
        (state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] as PageProperties<any>) = {
            node: '@sage/xtrem-test/MyNode',
        };
        getMockStore(state);
        const item = addFieldToState(FieldKey.Text, state, screenId, fieldId, {});
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelectorAll('.e-field-grid-column-hidden')).toHaveLength(1);
    });

    it('should render a text area field', () => {
        const item = addFieldToState(FieldKey.TextArea, state, screenId, fieldId, {});
        const wrapper = renderComponent(item);
        expect(wrapper.container.querySelectorAll('.e-text-area-field')).toHaveLength(1);
    });

    describe('responsive grid', () => {
        it('should wrap page fields into the responsive grid', () => {
            const item = addFieldToState(FieldKey.Text, state, screenId, fieldId, {
                width: 'small',
            });
            const wrapper = renderComponent(item, { nestedReadOnlyField: false });
            expect(wrapper.container.querySelectorAll('.e-grid-column')).toHaveLength(1);
            expect(wrapper.container.querySelectorAll('.e-text-field')).toHaveLength(1);
        });

        it('should not wrap nested read-only fields to columns', () => {
            const item = addFieldToState(FieldKey.Text, state, screenId, fieldId, {
                width: 'medium',
            });
            const wrapper = renderComponent(item, { availableColumns: 12, nestedReadOnlyField: true });
            expect(wrapper.container.querySelectorAll('.e-grid-column')).toHaveLength(0);
            expect(wrapper.container.querySelectorAll('.e-text-field')).toHaveLength(1);
        });
    });
});
