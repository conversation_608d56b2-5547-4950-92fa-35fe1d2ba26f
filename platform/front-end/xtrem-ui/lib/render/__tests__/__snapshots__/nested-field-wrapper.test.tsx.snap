// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Nested Field wrapper snapshots should render a date field 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin: var(--spacing000);
}

.c3 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e90e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c8 .c9 {
  padding: 0 var(--spacing150);
  padding-right: 0;
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c7 {
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
  width: 135px;
}

.c0 .c7 .c9 {
  margin-right: -8px;
}

.c11 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: var(--sizing500);
}

.c11:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

<div>
  <div
    class="e-field-nested e-field-nested-test-id-test-column"
  >
    <div
      class="e-field e-date-field"
      data-label="Column 1"
      data-nested="true"
      data-testid="e-date-field e-field-label-column1 e-field-bind-test-column"
    >
      <div
        class="c0"
        data-component="date"
        role="presentation"
      >
        <div
          class="c1 c2"
          data-component="date-input"
        >
          <div
            class="c3"
            data-role="field-line"
          >
            <div
              class="c4"
              data-role="label-container"
              id="label-container-TestPage-parent-element-id-test-column-label"
              width="30"
            >
              <label
                class="c5"
                data-element="label"
                for="TestPage-parent-element-id-test-column"
                id="TestPage-parent-element-id-test-column-label"
              >
                Column 1
              </label>
            </div>
            <div
              class="c6"
              data-role="input-presentation-container"
            >
              <div
                class="c7 c8"
                role="presentation"
              >
                <input
                  aria-invalid="false"
                  autocomplete="off"
                  class="c9 c10"
                  data-element="input"
                  data-label="Column 1"
                  id="TestPage-parent-element-id-test-column"
                  name="test-column"
                  type="text"
                  value="06/02/2020"
                />
                <span
                  aria-hidden="true"
                  class="c11"
                  data-element="input-icon-toggle"
                  data-role="input-icon-toggle"
                  tabindex="-1"
                >
                  <span
                    class="c12"
                    data-component="icon"
                    data-element="calendar"
                    data-role="icon"
                    font-size="small"
                    type="calendar"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Nested Field wrapper snapshots should render a icon field 1`] = `
.c0 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c0:hover {
  color: var(--colorsYin090);
  background-color: transparent;
}

.c0::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

<div>
  <div
    class="e-field-nested e-field-nested-test-id-test-column"
  >
    <div
      class="e-field e-icon-field"
      data-label="Column 1"
      data-nested="true"
      data-testid="e-icon-field e-field-label-column1 e-field-bind-test-column"
    >
      <div>
        <span
          class="c0 e-field-value e-icon-clickable"
          data-component="icon"
          data-element="icon"
          data-role="icon"
          font-size="small"
          tabindex="0"
          type="icon"
        />
      </div>
      <span
        class="common-input__help-text"
        data-element="help"
        data-testid="e-field-helper-text"
      >
         
      </span>
    </div>
  </div>
</div>
`;

exports[`Nested Field wrapper snapshots should render a image field 1`] = `
.c5 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c5:hover {
  color: #006437;
  background-color: transparent;
}

.c5::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c2 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: transparent;
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
  padding: 0px;
  width: 40px;
  min-height: 40px;
}

.c2:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c2 .c4 {
  color: var(--colorsActionMajor500);
}

.c2:hover {
  background: var(--colorsActionMajor600);
  color: var(--colorsActionMajorYang100);
}

.c2:hover .c4 {
  color: var(--colorsActionMajorYang100);
}

.c2 .c4 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c2 .c4 svg {
  margin-top: 0;
}

.c1 {
  height: inherit;
  min-width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  max-width: 40px;
  min-width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: 0px;
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

.c3 .c4 {
  color: #99adb7ff;
  font-size: 1.6667rem;
  height: 24px;
  min-height: 24px;
}

.c3:hover {
  color: #000000a6;
}

.c3:hover .c4 {
  color: #000000a6;
}

.c3:disabled .c4 {
  color: #ccd6dbff;
}

.c3:disabled:hover {
  background-color: transparent;
}

.c3:disabled:hover .c4 {
  color: #ccd6dbff;
}

<div>
  <div
    class="e-field-nested e-field-nested-test-id-test-column"
  >
    <div
      class="e-field e-image-field"
      data-label="Column 1"
      data-nested="true"
      data-testid="e-image-field e-field-label-column1 e-field-bind-test-column"
    >
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
      >
        Column 1
      </label>
      <div
        class="e-image-field-content-wrapper"
      >
        <div>
          <div
            class="e-portrait"
          >
            <div
              class="c0"
              data-component="portrait"
              shape="square"
            >
              <img
                alt="Column 1"
                class="c1"
                data-element="user-image"
                src="data:image;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDADIiJSwlHzIsKSw4NTI7S31RS0VFS5ltc1p9tZ++u7Kfr6zI4f/zyNT/
        16yv+v/9////////wfD/////////////2wBDATU4OEtCS5NRUZP/zq/O////////////////////////////////////////////////////////////////
        ///wAARCAAYAEADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAAAQMAAgQF/8QAJRABAAIBBAEEAgMAAAAAAAAAAQIRAAMSITEEEyJBgTORUWFx/8QAA
        EBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AOgM52xQDrjvAV5Xv0vfKUALlTQfeBm0HThMNHXkL0Lw/swN5g
        A8yT4MCS1OEOJV8mBz9Z05yfW8iSx7p4j+jA1aD6Wj7ZMzstsfvAas4UyRHvjrAkC9KhpLMClQntlqFc2X1gUj4viwVObKrddH9YDoHvuujAEuNV+bLwFS8x
        dSr+Cq3Vf+4F5RgQl6ZR2p1eAzU/HX80YBYyJLCuexwJCO2O1bwCRidAfWBSctswbI12GAJT3yiwFR7+MBjGK2g/WAJR3FdF84E2rK5VR0YH/9k="
              />
            </div>
          </div>
        </div>
        <div
          class="e-image-field-remove"
        >
          <button
            aria-label="Remove"
            class="c2 c3"
            data-component="button"
            data-testid="e-image-field-remove"
            draggable="false"
            type="button"
          >
            <span
              aria-hidden="true"
              class="c4 c5"
              color="--colorsActionMajor500"
              data-component="icon"
              data-element="cross"
              data-role="icon"
              font-size="small"
              type="cross"
            />
          </button>
        </div>
      </div>
      <span
        class="common-input__help-text"
        data-element="help"
        data-testid="e-field-helper-text"
      >
         
      </span>
    </div>
  </div>
</div>
`;

exports[`Nested Field wrapper snapshots should render a label field 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field-nested e-field-nested-test-id-test-column"
  >
    <div
      class="e-field e-label-field"
      data-label="Column 1"
      data-nested="true"
      data-testid="e-label-field e-field-label-column1 e-field-bind-test-column"
    >
      <span
        class="e-pill-wrapper"
      >
        <span
          class="c0"
          data-component="pill"
          style="cursor: pointer;"
        >
          label
        </span>
      </span>
      <span
        class="common-input__help-text"
        data-element="help"
        data-testid="e-field-helper-text"
      >
         
      </span>
    </div>
  </div>
</div>
`;

exports[`Nested Field wrapper snapshots should render a link field 1`] = `
.c0 > a,
.c0 > button {
  font-size: var(--fontSizes100);
  color: var(--colorsActionMajor500);
}

.c0 > a:hover,
.c0 > button:hover {
  color: var(--colorsActionMajor600);
}

.c0 > a:focus,
.c0 > button:focus {
  background-color: var(--colorsSemanticFocus250);
  border-radius: var(--borderRadius025);
}

.c0 > a:any-link:hover,
.c0 > button:hover {
  cursor: pointer;
}

.c0 > a,
.c0 > button {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.c0 > a:focus,
.c0 > button:focus {
  color: var(--colorsActionMajorYin090);
  outline: none;
}

<div>
  <div
    class="e-field-nested e-field-nested-test-id-test-column"
  >
    <div
      class="e-field e-link-field"
      data-label="Column 1"
      data-nested="true"
      data-testid="e-link-field e-field-label-column1 e-field-bind-test-column"
    >
      <span
        class="c0"
        data-component="link"
      >
        <a
          data-role="link-anchor"
          href="#"
        >
          <span
            class=""
          >
            link
          </span>
        </a>
      </span>
    </div>
  </div>
</div>
`;

exports[`Nested Field wrapper snapshots should render a numeric field 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field-nested e-field-nested-test-id-test-column"
  >
    <div
      class="e-field e-numeric-field"
      data-label="Column 1"
      data-nested="true"
      data-testid="e-numeric-field e-field-label-column1 e-field-bind-test-column"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-parent-element-id-test-column-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-parent-element-id-test-column"
              id="TestPage-parent-element-id-test-column-label"
            >
              Column 1
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c7 c8"
                data-element="input"
                data-label="Column 1"
                data-testid="e-ui-decimal-input"
                id="TestPage-parent-element-id-test-column"
                name="test-column"
                type="text"
                value="100"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Nested Field wrapper snapshots should render a progress field 1`] = `
.c0 {
  text-align: center;
  white-space: nowrap;
  width: 100%;
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  background-color: var(--colorsSemanticNeutral200);
  border: 1px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius400);
  overflow-x: hidden;
  width: 100%;
  min-height: -webkit-fit-content;
  min-height: -moz-fit-content;
  min-height: fit-content;
  box-sizing: border-box;
}

.c2 {
  display: inline-block;
  font-weight: 500;
}

.c1 {
  text-align: start;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 4px;
  font-size: var(--fontSizes100);
  margin-bottom: var(--spacing100);
}

.c4 {
  position: relative;
  left: 0;
  background-color: var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius400);
  width: 1%;
  min-width: 2px;
  height: var(--sizing100);
}

<div>
  <div
    class="e-field-nested e-field-nested-test-id-test-column"
  >
    <div
      class="e-field e-progress-field"
      data-label="Column 1"
      data-nested="true"
      data-testid="e-progress-field e-field-label-column1 e-field-bind-test-column"
    >
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
      >
        Column 1
      </label>
      <div
        class="c0"
        data-component="progress-bar"
      >
        <span
          class="c1"
          data-role="values-label"
        >
          <span
            class="c2"
            data-element="current-progress-label"
          >
            1%
          </span>
          <span
            data-element="custom-preposition"
          >
            of
          </span>
          <span
            class="c2"
            data-element="max-progress-label"
          >
            100%
          </span>
        </span>
        <span
          aria-hidden="true"
          class="c3"
          data-role="progress-bar"
        >
          <span
            class="c4"
            data-element="inner-bar"
            data-role="inner-bar"
          />
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Nested Field wrapper snapshots should render a reference field 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c13::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: hidden;
}

<div>
  <div
    class="e-field-nested e-field-nested-test-id-test-column"
  >
    <div
      class="e-field e-reference-field"
      data-label="Column 1"
      data-nested="true"
      data-testid="e-reference-field e-field-label-column1 e-field-bind-test-column"
    >
      <div
        class="e-reference-field-body"
      >
        <div
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="downshift-0-menu"
          class="e-ui-select-input-wrapper"
          role="combobox"
        >
          <div
            class="c0 c1"
          >
            <div
              class="c2"
              data-role="field-line"
            >
              <div
                class="c3"
                data-role="label-container"
                id="label-container-TestPage-parent-element-id-test-column-label"
                width="30"
              >
                <label
                  class="c4"
                  data-element="label"
                  for="TestPage-parent-element-id-test-column"
                  id="TestPage-parent-element-id-test-column-label"
                >
                  Column 1
                </label>
              </div>
              <div
                class="c5"
                data-role="input-presentation-container"
              >
                <div
                  class="c6"
                  role="presentation"
                  style="padding-right: 20px; flex-wrap: nowrap;"
                >
                  <div
                    class="e-ui-select-input-left-children"
                  />
                  <input
                    aria-autocomplete="list"
                    aria-controls="downshift-0-menu"
                    aria-invalid="false"
                    aria-label="Column 1"
                    autocomplete="off"
                    class="c7 c8 e-field-select-input-text"
                    data-element="input"
                    data-testid="e-reference-field-lookup-input-uniqguidmock"
                    id="TestPage-parent-element-id-test-column"
                    name="testcarb-onco-mpon-ents-uniqguidmock"
                    placeholder="Please Select..."
                    style="text-overflow: ellipsis; min-width: 0;"
                    type="text"
                    value=""
                  />
                  <div
                    class="e-ui-select-close"
                  >
                    <span
                      class="e-ui-select-close-icon"
                    >
                      <button
                        aria-label="Clear"
                        class="c9 c10"
                        data-component="icon-button"
                        data-testid="e-ui-select-close"
                        hidden=""
                        tabindex="-1"
                        type="button"
                      >
                        <div>
                          <span
                            class="c11 c12"
                            data-component="icon"
                            data-element="cross_circle"
                            data-role="icon"
                            font-size="small"
                            type="cross_circle"
                          />
                        </div>
                      </button>
                    </span>
                  </div>
                  <div
                    class="e-ui-select-inline-dropdown"
                    id="downshift-0-toggle-button"
                    tabindex="-1"
                  >
                    <div>
                      <span
                        class="c11 c13"
                        data-component="icon"
                        data-element="dropdown"
                        data-role="icon"
                        font-size="small"
                        type="dropdown"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          aria-busy="true"
          aria-labelledby="downshift-0-label"
          class="e-ui-select-dropdown"
          id="downshift-0-menu"
          style="position: relative; width: 100%;"
        >
          <ul
            aria-labelledby="TestPage-parent-element-id-test-column"
            data-testid="e-ui-select-dropdown"
            role="listbox"
            style="max-height: 18px; border-radius: 4px;"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Nested Field wrapper snapshots should render a select field 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field-nested e-field-nested-test-id-test-column"
  >
    <div
      class="e-field e-select-field"
      data-label="Column 1"
      data-nested="true"
      data-testid="e-select-field e-field-label-column1 e-field-bind-test-column"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-parent-element-id-test-column-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-parent-element-id-test-column"
                id="TestPage-parent-element-id-test-column-label"
              >
                Column 1
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Column 1"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-select-field-input"
                  id="TestPage-parent-element-id-test-column"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="select"
                />
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c9"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-parent-element-id-test-column"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Nested Field wrapper snapshots should render a text field 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field-nested e-field-nested-test-id-test-column"
  >
    <div
      class="e-field e-text-field"
      data-label="Column 1"
      data-nested="true"
      data-testid="e-text-field e-field-label-column1 e-field-bind-test-column"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-parent-element-id-test-column-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-parent-element-id-test-column"
              id="TestPage-parent-element-id-test-column-label"
            >
              Column 1
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c7 c8"
                data-element="input"
                data-label="Column 1"
                data-testid="e-text-field-input"
                id="TestPage-parent-element-id-test-column"
                name="test-column"
                type="text"
                value="text"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
