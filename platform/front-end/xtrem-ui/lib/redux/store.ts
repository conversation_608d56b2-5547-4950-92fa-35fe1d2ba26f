import type { Middleware, Store } from 'redux';
import { applyMiddleware, compose, createStore } from 'redux';
import { responsiveStoreEnhancer } from 'redux-responsive';
import reduxThunkMiddleware from 'redux-thunk';
import { isDevMode } from '../utils/window';
import type { AppAction } from './action-types';
import { actionSubscriptionMiddleware } from './middleware/action-subscription-middleware';
import { dashboardWidgetsLoadMiddleware } from './middleware/dashboard-widgets-load-middleware';
import { loggingMiddleware } from './middleware/logging-middleware';
import { websocketCallbackMiddleware } from './middleware/websocket-middleware';
import rootReducer from './reducer';
import type { XtremAppState } from './state';

const composeEnhancers = (isDevMode() && window && (window as any).__REDUX_DEVTOOLS_EXTENSION_COMPOSE__) || compose;

const configureStore = (): Store<XtremAppState, AppAction> => {
    const middleWares: Middleware[] = [
        reduxThunkMiddleware,
        loggingMiddleware,
        actionSubscriptionMiddleware,
        dashboardWidgetsLoadMiddleware,
        websocketCallbackMiddleware,
    ];

    const enhancer = composeEnhancers(responsiveStoreEnhancer, applyMiddleware(...middleWares));
    return createStore<XtremAppState, AppAction, {}, {}>(rootReducer, undefined, enhancer);
};

const store: Store<XtremAppState, AppAction> = configureStore();

export const getStore = (): Store<XtremAppState, AppAction> => store;
