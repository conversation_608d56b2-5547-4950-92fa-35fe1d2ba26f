import type { Dashboard, Dict, WorkflowNode } from '@sage/xtrem-shared';
import type { ThunkAction, ThunkDispatch } from 'redux-thunk';
import type { OptionsMenuItem, PageControlObject } from '../component/control-objects';
import type { TableViewGrouping, TableViewSortedColumn } from '../component/field/table/table-component-types';
import type { WidgetDefinition } from '../dashboard/widgets/abstract-widget';
import type { CollectionValue } from '../service/collection-data-service';
import type { InitialMetadataQueryResult, ProcessedNodeDetails } from '../service/metadata-service';
import type { DataTypeDetails, FormattedNodeDetails } from '../service/metadata-types';
import type { XtremUiPlugin } from '../service/plugin-service';
import type { ScreenBaseDefinition, ValidationResult } from '../service/screen-base-definition';
import type { Toast } from '../service/toast-service';
import type { XtremUiTransaction } from '../service/transactions-service';
import type { DialogDescription } from '../types/dialogs';
import type { QueryParameters } from '../utils/types';
import type { EventCallback } from './reducer/websocket';
import type {
    ApplicationContext,
    DashboardListItem,
    FocusPosition,
    Menu,
    UiComponentUserSettings,
    UserWidgetDefinition,
    XtremAppState,
} from './state';

export enum ActionType {
    AddDashboard = 'AddDashboard',
    AddDataTypes = 'AddDataTypes',
    AddEnumType = 'AddEnumType',
    AddInternalError = 'AddInternalError',
    AddMenuItem = 'AddMenuItem',
    AddNavigationPanelItems = 'AddNavigationPanelItems',
    AddNodeTypes = 'AddNodeTypes',
    AddPlugin = 'AddPlugin',
    AddSchemaInfo = 'AddSchemaInfo',
    AddScreenDefinition = 'AddScreenDefinition',
    AddTranslations = 'AddTranslations',
    AddWidgetData = 'AddWidgetData',
    AddWidgets = 'AddWidgets',
    AddWindow = 'AddWindow',
    ApplyCurrentViewNameAndDescription = 'ApplyCurrentViewNameAndDescription',
    ClearAllSubscriptions = 'ClearAllSubscriptions',
    ClearHistory = 'ClearHistory',
    ClearNavigationPanelSearchText = 'ClearNavigationPanelSearchText',
    ClearNestedFieldsProperties = 'ClearNestedFieldsProperties',
    ClearWidgetOptions = 'ClearWidgetOptions',
    CloseDialog = 'CloseDialog',
    CloseLookupDialog = 'CloseLookupDialog',
    CommitTransaction = 'CommitTransaction',
    ExecuteFormDesignCommand = 'ExecuteFormDesignCommand',
    FinishScreenLoading = 'FinishScreenLoading',
    HandleBackNavigation = 'HandleBackNavigation',
    InitTableViews = 'InitTableViews',
    OpenDashboardEditor = 'OpenDashboardEditor',
    OpenDialog = 'OpenDialog',
    OpenLookupDialog = 'OpenLookupDialog',
    OpenNestedGridLevel = 'OpenNestedGridLevel',
    PopFromHistory = 'PopFromHistory',
    PushToHistory = 'PushToHistory',
    RedrawComponent = 'RedrawComponent',
    ReduxInitialAction = 'ReduxInitialAction',
    RemoveDashboard = 'RemoveDashboard',
    RemoveDashboardGroup = 'RemoveDashboardGroup',
    RemoveInternalError = 'RemoveInternalError',
    RemoveMenuItem = 'RemoveMenuItem',
    RemoveNestedFieldErrors = 'RemoveNestedFieldErrors',
    RemoveNonNestedError = 'RemoveNonNestedError',
    RemovePageServerErrors = 'RemovePageServerErrors',
    RemovePhantomError = 'RemovePhantomError',
    RemoveScreenDefinition = 'RemoveScreenDefinition',
    RemoveToast = 'RemoveToast',
    RemoveToasts = 'RemoveAllToasts',
    RemoveWindow = 'RemoveWindow',
    ReplaceLastInHistory = 'ReplaceLastInHistory',
    ResetScreenUiComponentProperties = 'ResetScreenUiComponentProperties',
    ResetWidgetLoadingTimeout = 'ResetWidgetLoadingTimeout',
    Set360ViewState = 'Set360ViewState',
    SetActiveSection = 'SetActiveSection',
    SetApplicationContext = 'SetApplicationContext',
    SetBackNavigation = 'SetBackNavigation',
    SetComponentLoading = 'SetComponentLoading',
    SetDashboardEditorDialogOpen = 'SetDashboardEditorDialogOpen',
    SetElementUserSettings = 'SetElementUserSettings',
    SetErrors = 'SetErrors',
    SetFieldCleanState = 'SetFieldCleanState',
    SetFieldDirtyState = 'SetFieldDirtyState',
    SetFieldValue = 'SetFieldValue',
    SetFilterPanel = 'SetFilterPanel',
    SetFocusPosition = 'SetFocusPosition',
    SetGlobalLoading = 'SetGlobalLoading',
    SetInitialDashboardInformation = 'SetInitialDashboardInformation',
    SetInitialMetaData = 'SetInitialMetaData',
    SetKeyboardShortcutsEnabled = 'SetKeyboardShortcutsEnabled',
    SetLoadingDashboards = 'SetLoadingDashboards',
    SetNavigationPanelIsHeaderHidden = 'SetNavigationPanelIsHeaderHidden',
    SetNavigationPanelIsHidden = 'SetNavigationPanelIsHidden',
    SetNavigationPanelIsOpened = 'SetNavigationPanelIsOpened',
    SetNavigationPanelIsRefreshing = 'SetNavigationPanelIsRefreshing',
    SetNavigationPanelValue = 'SetNavigationPanelValue',
    SetNodeNames = 'SetNodeNames',
    SetPageClean = 'SetPageClean',
    SetPageInsightCount = 'SetPageInsightCount',
    SetPath = 'SetPath',
    SetQueryParameter = 'SetQueryParameter',
    SetScreenDefinitionDialogId = 'SetScreenDefinitionDialogId',
    SetScreenDefinitionReady = 'SetScreenDefinitionReady',
    SetSectionReady = 'SetSectionReady',
    SetSectionValues = 'SetSectionValues',
    SetSelectedDashboard = 'SetSelectedDashboard',
    SetStickerDefinitionDialogId = 'SetStickerDefinitionDialogId',
    SetTableViewColumnHidden = 'SetTableViewColumnHidden',
    SetTableViewColumnOrder = 'SetTableViewColumnOrder',
    SetTableViewFilter = 'SetTableViewFilter',
    SetTableViewGrouping = 'SetTableViewGrouping',
    SetTableViewOptionsMenuItem = 'SetTableViewOptionMenuItem',
    SetTableViewOptionsMenuItemAndViewFilter = 'SetTableViewOptionsMenuItemAndViewFilter',
    SetTableViewSearchText = 'SetTableViewSearchText',
    SetTableViewSortOrder = 'SetTableViewSortOrder',
    SetUiComponentProperties = 'SetUiComponentProperties',
    SetUserCustomizationSaved = 'SetUserCustomizationSaved',
    SetValues = 'SetValues',
    SetWidgetCategories = 'SetWidgetCategories',
    SetWidgetData = 'SetWidgetData',
    SetWidgetEditorOpen = 'SetWidgetEditorOpen',
    SetWidgetLoading = 'SetWidgetLoading',
    SetWidgetLoadingTimeoutId = 'SetWidgetLoadingTimeoutId',
    SetWidgetOptions = 'SetWidgetOptions',
    SetWidgetProperties = 'SetWidgetProperties',
    SetWorkflowNodes = 'SetWorkflowNodes',
    ShowToast = 'ShowToast',
    SubscribeToEvent = 'SubscribeToEvent',
    TriggerCategoryCallbacks = 'TriggerCategoryCallbacks',
    UnsubscribeFromEvent = 'UnsubscribeFromEvent',
    UpdateDashboardEditorFromHistory = 'UpdateDashboardEditorFromHistory',
    UpdateDashboardEditorWithHistory = 'UpdateDashboardEditorWithHistory',
    UpdateErrors = 'UpdateErrors',
    UpdateMenuItem = 'UpdateMenuItem',
    UpdateNestedFieldErrors = 'UpdateNestedFieldErrors',
    UpdateNestedFieldRecordErrors = 'UpdateNestedFieldRecordErrors',
    UpdateTableSidebarDialogTarget = 'UpdateTableSidebarDialogTarget',
    UpdateUserCustomization = 'UpdateUserCustomization',
    UpdateUserWidgetDefinition = 'UpdateUserWidgetDefinition',
}

export type AppAction =
    | {
          type: ActionType.SetInitialDashboardInformation;
          value: { availableDashboards: DashboardListItem[]; canEditDashboards: boolean; group: string };
      }
    | {
          type: ActionType.AddDashboard;
          value: { dashboard: Dashboard; group: string };
      }
    | {
          type: ActionType.SetSelectedDashboard;
          value: { _id: string; title: string; group: string };
      }
    | {
          type: ActionType.AddPlugin;
          value: {
              pluginPackage: string;
              pluginDefinition: XtremUiPlugin<any, any>;
          };
      }
    | {
          type: ActionType.UpdateUserWidgetDefinition;
          value: { widget: UserWidgetDefinition; group: string };
      }
    | {
          type: ActionType.SetErrors;
          value: {
              screenId: string;
              elementId: string;
              errors: ValidationResult[];
          };
      }
    | {
          type: ActionType.Set360ViewState;
          value: {
              screenId: string;
              state: boolean;
          };
      }
    | {
          type: ActionType.UpdateNestedFieldErrors;
          value: {
              screenId: string;
              elementId: string;
              validationErrors: ValidationResult[];
              columnId: string;
              recordId?: string;
          };
      }
    | {
          type: ActionType.SetNodeNames;
          value: Dict<string>;
      }
    | {
          type: ActionType.SetWidgetCategories;
          value: { categories: Dict<string> };
      }
    | {
          type: ActionType.UpdateNestedFieldRecordErrors;
          value: {
              screenId: string;
              elementId: string;
              validationErrors: ValidationResult[];
              recordId?: string;
              isUncommitted?: boolean;
              level?: number;
          };
      }
    | {
          type: ActionType.AddInternalError;
          value: {
              elementId: string;
              errorMessage: ValidationResult;
              screenId: string;
          };
      }
    | {
          type: ActionType.AddNavigationPanelItems;
          value: {
              navigationPanelCursor?: string;
              navigationPanelItems: any[];
              screenId: string;
              hasNextPage: boolean;
          };
      }
    | {
          type: ActionType.AddMenuItem;
          value: Menu;
      }
    | {
          type: ActionType.AddEnumType;
          value: { enums: Dict<{ values: string[]; name: string; translations: string[] }>; locale: string };
      }
    | {
          type: ActionType.AddDataTypes;
          value: Dict<DataTypeDetails>;
      }
    | {
          type: ActionType.AddNodeTypes;
          value: Dict<FormattedNodeDetails>;
      }
    | {
          type: ActionType.AddScreenDefinition;
          value: ScreenBaseDefinition;
      }
    | {
          type: ActionType.AddTranslations;
          value: {
              locale: string;
              literals: Dict<string>;
          };
      }
    | {
          type: ActionType.AddWidgets;
          value: {
              widgets: WidgetDefinition[];
              group: string;
          };
      }
    | {
          type: ActionType.AddWidgetData;
          value: {
              widgetId: string;
              group: string;
              data: any;
          };
      }
    | {
          type: ActionType.AddWindow;
          value: {
              sectionId: string;
              window: Window;
          };
      }
    | {
          type: ActionType.ClearNestedFieldsProperties;
          value: string;
      }
    | {
          type: ActionType.ClearWidgetOptions;
          value: null;
      }
    | {
          type: ActionType.CloseDialog;
          value: number;
      }
    | {
          type: ActionType.CloseLookupDialog;
          value: null;
      }
    | {
          type: ActionType.CommitTransaction;
          value: {
              screenId: string;
              transaction: XtremUiTransaction;
          };
      }
    | {
          type: ActionType.OpenDialog;
          value: {
              dialogId: number;
              dialog: DialogDescription;
          };
      }
    | {
          type: ActionType.RedrawComponent;
          value: {
              elementId: string;
              screenId: string;
              columnBind?: string;
          };
      }
    | {
          type: ActionType.OpenNestedGridLevel;
          value: {
              elementId: string;
              screenId: string;
              recordPath?: string[];
          };
      }
    | {
          type: ActionType.ExecuteFormDesignCommand;
          value: {
              elementId: string;
              screenId: string;
              command: string;
              args?: string[];
          };
      }
    | {
          type: ActionType.FinishScreenLoading;
          value: {
              pageDefinition: ScreenBaseDefinition;
              locale: string;
          } & ProcessedNodeDetails;
      }
    | {
          type: ActionType.AddSchemaInfo;
          value: {
              locale: string;
          } & ProcessedNodeDetails;
      }
    | {
          type: ActionType.OpenLookupDialog;
          value: FocusPosition;
      }
    | {
          type: ActionType.RemoveToasts;
          value: null;
      }
    | {
          type: ActionType.RemoveDashboard;
          value: {
              dashboardId: string;
              group: string;
          };
      }
    | {
          type: ActionType.RemoveDashboardGroup;
          value: string;
      }
    | {
          type: ActionType.RemoveNonNestedError;
          value: {
              elementId: string;
              screenId: string;
              columnId?: string;
          };
      }
    | {
          type: ActionType.RemoveInternalError;
          value: {
              elementId: string;
              screenId: string;
          };
      }
    | {
          type: ActionType.RemovePhantomError;
          value: {
              elementId: string;
              screenId: string;
          };
      }
    | {
          type: ActionType.RemoveMenuItem;
          value: string;
      }
    | {
          type: ActionType.RemoveToast;
          value: number;
      }
    | {
          type: ActionType.RemovePageServerErrors;
          value: {
              screenId: string;
          };
      }
    | {
          type: ActionType.RemoveScreenDefinition;
          value: string;
      }
    | {
          type: ActionType.ResetScreenUiComponentProperties;
          value: {
              screenId: string;
              activeSection?: string | null;
          };
      }
    | {
          type: ActionType.RemoveWindow;
          value: string;
      }
    | {
          type: ActionType.SetActiveSection;
          value: {
              activeSection: string | null;
              screenId: string;
              isLoading?: boolean;
          };
      }
    | {
          type: ActionType.SetNavigationPanelValue;
          value: {
              value: CollectionValue;
              screenId: string;
          };
      }
    | {
          type: ActionType.SetNavigationPanelIsRefreshing;
          value: {
              isRefreshing: boolean;
              screenId: string;
          };
      }
    | {
          type: ActionType.SetApplicationContext;
          value: ApplicationContext;
      }
    | {
          type: ActionType.SetInitialMetaData;
          value: InitialMetadataQueryResult &
              ProcessedNodeDetails & {
                  locale: string;
              };
      }
    | {
          type: ActionType.SetSectionValues;
          value: {
              screenId: string;
              sectionId: string;
              values: Dict<any>;
          };
      }
    | {
          type: ActionType.SetSectionReady;
          value: {
              screenId: string;
              sectionId: string;
          };
      }
    | {
          type: ActionType.SetComponentLoading;
          value: {
              screenId: string;
              elementId: string;
              isLoading: boolean;
          };
      }
    | {
          type: ActionType.ApplyCurrentViewNameAndDescription;
          value: {
              screenId: string;
              elementId: string;
          };
      }
    | {
          type: ActionType.SetElementUserSettings;
          value: {
              screenId: string;
              elementId: string;
              userSettings: Partial<UiComponentUserSettings> | null;
          };
      }
    | {
          type: ActionType.SetDashboardEditorDialogOpen;
          value: { isOpen: boolean; group: string };
      }
    | {
          type: ActionType.SetFieldDirtyState;
          value: {
              screenId: string;
              elementId: string;
          };
      }
    | {
          type: ActionType.SetFieldCleanState;
          value: {
              screenId: string;
              elementId: string;
          };
      }
    | {
          type: ActionType.SetFieldValue;
          value: {
              elementId: string;
              fieldValue: any;
              screenId: string;
              isOrganicChange: boolean;
          };
      }
    | {
          type: ActionType.SetFilterPanel;
          value: boolean;
      }
    | {
          type: ActionType.SetFocusPosition;
          value: FocusPosition | null;
      }
    | {
          type: ActionType.SetGlobalLoading;
          value: boolean;
      }
    | {
          type: ActionType.SetLoadingDashboards;
          value: { isLoading: boolean; group: string };
      }
    | {
          type: ActionType.SetKeyboardShortcutsEnabled;
          value: boolean;
      }
    | {
          type: ActionType.SetNavigationPanelIsHeaderHidden;
          value: {
              screenId: string;
              isHeaderHidden: boolean;
          };
      }
    | {
          type: ActionType.SetNavigationPanelIsHidden;
          value: {
              screenId: string;
              isHidden: boolean;
          };
      }
    | {
          type: ActionType.SetNavigationPanelIsOpened;
          value: {
              screenId: string;
              isOpened: boolean;
          };
      }
    | {
          type: ActionType.SetPageClean;
          /** The screenId */
          value: string;
      }
    | {
          type: ActionType.SetPageInsightCount;
          /** The screenId */
          value: { screenId: string; count: number };
      }
    | {
          type: ActionType.SetPath;
          value: string;
      }
    | {
          type: ActionType.SetScreenDefinitionDialogId;
          value: {
              screenId: string;
              dialogId: number;
              pageControlObject: PageControlObject;
              title: string;
              subtitle: string;
              forceSetTitle?: boolean;
          };
      }
    | {
          type: ActionType.SetUserCustomizationSaved;
          value: {
              screenId: string;
              elementId: string;
              viewId?: string;
          };
      }
    | {
          type: ActionType.SetScreenDefinitionReady;
          value: string;
      }
    | {
          type: ActionType.SetStickerDefinitionDialogId;
          value: {
              screenId: string;
              dialogId: number;
              onFinish?: (values: any) => void;
          };
      }
    | {
          type: ActionType.SetUiComponentProperties;
          value: {
              elementId: string;
              fieldProperties: any;
              screenId: string;
          };
      }
    | {
          type: ActionType.SetQueryParameter;
          value: {
              parameterName: string;
              value: string | number;
              screenId: string;
          };
      }
    | {
          type: ActionType.SetValues;
          value: {
              screenId: string;
              values: Dict<any>;
              preserveValidationState?: boolean;
          };
      }
    | {
          type: ActionType.SetWidgetData;
          value: {
              widgetId: string;
              group: string;
              data: any;
          };
      }
    | {
          type: ActionType.SetWidgetProperties;
          value: {
              widgetId: string;
              group: string;
              properties: any;
          };
      }
    | {
          type: ActionType.SetWidgetOptions;
          value: {
              widgetId: string;
              dashboardId: string;
              group: string;
              options: any;
          };
      }
    | {
          type: ActionType.ShowToast;
          value: Toast;
      }
    | {
          type: ActionType.UpdateDashboardEditorFromHistory;
          value: {
              historyIndex: number;
              group: string;
              dashboard: Dashboard;
          };
      }
    | {
          type: ActionType.UpdateDashboardEditorWithHistory;
          value: {
              dashboard: Dashboard;
              group: string;
              history: Dashboard[];
          };
      }
    | {
          type: ActionType.UpdateErrors;
          value: {
              screenId: string;
              validationErrors: Dict<ValidationResult[] | undefined>;
          };
      }
    | {
          type: ActionType.UpdateMenuItem;
          value: {
              id: string;
              badgeContent?: string;
          };
      }
    | {
          type: ActionType.OpenDashboardEditor;
          value: {
              dashboard: Dashboard;
              group: string;
          };
      }
    | {
          type: ActionType.SetWidgetEditorOpen;
          value: {
              isOpen: boolean;
              widgetId?: string;
              group: string;
              widgetDefinition?: UserWidgetDefinition;
          };
      }
    | {
          type: ActionType.ReduxInitialAction;
      }
    | {
          type: ActionType.InitTableViews;
          value: {
              tableViews: Dict<Dict<UiComponentUserSettings>>;
          };
      }
    | {
          type: ActionType.SetTableViewColumnHidden;
          value: {
              columnHidden?: Dict<boolean>;
              elementId: string;
              level: number;
              screenId: string;
          };
      }
    | {
          type: ActionType.SetTableViewColumnOrder;
          value: {
              columnOrder?: string[];
              elementId: string;
              level: number;
              screenId: string;
          };
      }
    | {
          type: ActionType.SetTableViewFilter;
          value: {
              elementId: string;
              filter: any;
              level: number;
              screenId: string;
          };
      }
    | {
          type: ActionType.SetTableViewGrouping;
          value: {
              elementId: string;
              grouping?: TableViewGrouping;
              level: number;
              screenId: string;
          };
      }
    | {
          type: ActionType.SetTableViewOptionsMenuItem;
          value: {
              elementId: string;
              level: number;
              optionsMenuItem?: OptionsMenuItem;
              screenId: string;
          };
      }
    | {
          type: ActionType.SetTableViewOptionsMenuItemAndViewFilter;
          value: {
              elementId: string;
              level: number;
              optionsMenuItem?: OptionsMenuItem;
              screenId: string;
              filter: any;
          };
      }
    | {
          type: ActionType.SetTableViewSearchText;
          value: {
              screenId: string;
              elementId: string;
              level: number;
              searchText: string;
          };
      }
    | {
          type: ActionType.ClearNavigationPanelSearchText;
          value: {
              screenId: string;
          };
      }
    | {
          type: ActionType.SetWorkflowNodes;
          value: WorkflowNode[];
      }
    | {
          type: ActionType.SetWidgetLoading;
          value: {
              widgetId: string;
              isLoading: boolean;
          };
      }
    | {
          type: ActionType.SetWidgetLoadingTimeoutId;
          value: {
              widgetId: string;
              timeoutId: number | null;
          };
      }
    | {
          type: ActionType.ResetWidgetLoadingTimeout;
          value: {
              widgetId: string;
          };
      }
    | {
          type: ActionType.UpdateTableSidebarDialogTarget;
          value: {
              dialogId: number;
              recordId?: string;
              nextRecordId?: string;
              prevRecordId?: string;
              isNewRecord: boolean;
              title: string;
          };
      }
    | {
          type: ActionType.SetTableViewSortOrder;
          value: {
              elementId: string;
              level: number;
              sortOrder?: TableViewSortedColumn[];
              screenId: string;
          };
      }
    | { type: ActionType.PushToHistory; value: { path: string; queryParams: QueryParameters } }
    | { type: ActionType.PopFromHistory }
    | { type: ActionType.ClearHistory }
    | { type: ActionType.HandleBackNavigation }
    | { type: ActionType.SetBackNavigation; value: boolean }
    | { type: ActionType.ReplaceLastInHistory; value: { path: string; queryParams: QueryParameters } }
    | {
          type: ActionType.SubscribeToEvent;
          value: {
              category: string;
              callback: EventCallback;
          };
      }
    | {
          type: ActionType.UnsubscribeFromEvent;
          value: {
              category: string;
              callback: EventCallback;
          };
      }
    | {
          type: ActionType.ClearAllSubscriptions;
      }
    | {
          type: ActionType.TriggerCategoryCallbacks;
          value: {
              category: string;
              args: any[];
          };
      };

export interface AppThunkAction extends ThunkAction<void, XtremAppState, void, AppAction> {}

export interface AppThunkDispatch extends ThunkDispatch<XtremAppState, void, AppAction> {}
