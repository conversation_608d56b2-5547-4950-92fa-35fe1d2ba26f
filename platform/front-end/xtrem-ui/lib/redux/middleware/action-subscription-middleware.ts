import type { AnyAction, Dispatch, Middleware } from 'redux';
import { xtremConsole } from '../../utils/console';
import { isDevMode } from '../../utils/window';
import type { AppAction } from '../action-types';

export type ActionSubscription = (action: AppAction) => void;
export type Unsubscribe = () => void;

const subscriptions: ActionSubscription[] = [];

export const getSubscriptions = (): ActionSubscription[] => [...subscriptions];

export const subscribeToActions = (subscription: ActionSubscription): Unsubscribe => {
    subscriptions.push(subscription);
    return (): void => {
        const index = subscriptions.indexOf(subscription);
        if (index !== -1) {
            subscriptions.splice(index, 1);
        }
    };
};

export const actionSubscriptionMiddleware: Middleware =
    () =>
    (next: Dispatch<AnyAction>) =>
    (action: AnyAction): AnyAction => {
        subscriptions.forEach(s => {
            try {
                s(action);
            } catch (e) {
                if (isDevMode()) {
                    xtremConsole.error(e);
                }
            }
        });
        return next(action);
    };
