import type { AnyAction, Dispatch, Store } from 'redux';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import { resetWidgetLoadingTimeout, setWidgetLoadingTimeoutId } from '../actions';
import type { XtremAppState } from '../state';
import { DASHBOARD_WIDGETS_MIN_LOADING_TIME } from '../../utils/constants';

export const dashboardWidgetsLoadMiddleware =
    (store: Store<XtremAppState, AppAction>) =>
    (next: Dispatch<AnyAction>) =>
    (action: AppAction): AppAction => {
        const result = next(action);

        if (action.type === ActionType.SetWidgetLoading) {
            const state = store.getState().loading.widgets[action.value.widgetId];

            if (!state.isActualLoading && state.isVisibleLoading && state.loadingStartTime !== null) {
                const elapsedTime = Date.now() - state.loadingStartTime;

                if (elapsedTime < DASHBOARD_WIDGETS_MIN_LOADING_TIME) {
                    const remainingTime = DASHBOARD_WIDGETS_MIN_LOADING_TIME - elapsedTime;

                    const timeoutId = window.setTimeout(() => {
                        store.dispatch(resetWidgetLoadingTimeout(action.value.widgetId));
                    }, remainingTime);

                    store.dispatch(setWidgetLoadingTimeoutId(action.value.widgetId, timeoutId));
                }
            }
        }

        return result;
    };
