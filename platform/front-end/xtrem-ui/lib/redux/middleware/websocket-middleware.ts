import type { AnyAction, Dispatch, Store } from 'redux';
import { xtremConsole } from '../../utils/console';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { XtremAppState } from '../state';

export const websocketCallbackMiddleware =
    (store: Store<XtremAppState, AppAction>) =>
    (next: Dispatch<AnyAction>) =>
    (action: AppAction): AppAction => {
        if (action.type === ActionType.TriggerCategoryCallbacks) {
            const { category, args } = action.value;
            const state = store.getState();
            const callbacks = state.websocket.subscriptions.activeSubscriptions.get(category);
            if (callbacks) {
                callbacks.forEach(callback => {
                    try {
                        callback(...args);
                    } catch (error) {
                        xtremConsole.error(`Error executing event callback for category ${category}:`, error);
                    }
                });
            }
        }
        return next(action);
    };
