/* eslint-disable no-console */
import type { AnyAction, Dispatch, Middleware, MiddlewareAPI } from 'redux';
import { xtremConsole } from '../../utils/console';
import { hasStateLogging } from '../../utils/window';

const consoleFormat = 'color: green; font-weight: bold;';

export const loggingMiddleware: Middleware =
    ({ getState }: MiddlewareAPI<Dispatch<AnyAction>>) =>
    (next: Dispatch<AnyAction>) =>
    (action: AnyAction): AnyAction => {
        if (hasStateLogging()) {
            const id = Date.now();
            const state = getState();
            if (hasStateLogging()) {
                xtremConsole.log('%cDispatching', consoleFormat, id, action);
                xtremConsole.log('%cState before dispatch', consoleFormat, id, state);
            }
            const returnValue = next(action);
            const updatedState = getState();
            if (hasStateLogging()) {
                xtremConsole.log('%cState after dispatch', consoleFormat, id, updatedState);
            }
            (window as any).__XTREM_STATE = updatedState;
            return returnValue;
        }

        return next(action);
    };
