import { objectKeys, type Dashboard, type DashboardItem, type Dict } from '@sage/xtrem-shared';
import { cloneDeepWith, isEmpty, uniq } from 'lodash';
import { getImageUrlFromValue } from '../../component/field/image/image-utils';
import type { DashboardContextVariables } from '../../dashboard/dashboard-types';
import { GENERIC_PLATFORM_WIDGET_PREFIX, getGenericWidgetDefinition } from '../../dashboard/generic-widgets';
import type { AbstractWidget, WidgetDefinition } from '../../dashboard/widgets/abstract-widget';
import { WidgetType } from '../../dashboard/widgets/abstract-widget';
import type { TableWidgetProperties } from '../../dashboard/widgets/table-widget-decorator';
import * as dashboardService from '../../service/dashboard-service';
import { resolveWidgetProperty, type LoadMoreRowsParams } from '../../service/dashboard-service';
import { fetchEnumTranslations } from '../../service/metadata-service';
import * as screenLoaderService from '../../service/screen-loader-service';
import type { Constructible } from '../../types';
import { arePlatformLiteralsMissing } from '../../utils/state-utils';
import { getArtifactDescription } from '../../utils/transformers';
import type { AppAction, AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { XtremAppState } from '../state';
import { addTranslations } from './common-actions';

/**
 * Constructs the widget object depending its type. It checks if the widget is generic user configured or functional code provided, then constructs it.
 */
export const createWidgetObject = ({
    contextVariables,
    dashboardId,
    group,
    settings,
    type,
    widgetConstructors,
    widgetId,
}: {
    contextVariables?: DashboardContextVariables;
    dashboardId: string;
    group: string;
    settings?: any;
    type: string;
    widgetConstructors: Dict<Constructible<AbstractWidget>>;
    widgetId: string;
}): AbstractWidget => {
    const isGenericWidget = type.startsWith(GENERIC_PLATFORM_WIDGET_PREFIX);
    if (isGenericWidget) {
        if (!settings) {
            throw new Error('Generic widgets must have a settings object');
        }
        return getGenericWidgetDefinition(settings, dashboardId, widgetId, group, contextVariables);
    }

    const WidgetConstructor = widgetConstructors[type];
    if (!WidgetConstructor) {
        throw new Error(`No constructor found for widget: ${type}`);
    }
    WidgetConstructor.prototype.__id = widgetId;
    WidgetConstructor.prototype.__group = group;
    WidgetConstructor.prototype.__dashboardId = dashboardId;
    WidgetConstructor.prototype.__contextVariables = contextVariables;
    return new WidgetConstructor();
};

const loadDashboard = async (
    dashboard: Dashboard | null,
    group: string,
    contextVariables: DashboardContextVariables | undefined,
    forceRefetchData: boolean,
    dispatch: AppThunkDispatch,
    getState: () => XtremAppState,
): Promise<void> => {
    const loadedWidgets = objectKeys(getState().dashboard.dashboardGroups[group].widgets);
    const filterWidgetsToLoad = (c: DashboardItem): boolean => forceRefetchData || loadedWidgets.indexOf(c._id) === -1;
    // We should not load widgets that we already have and generic user defined widgets which have the `@sage/xtrem-ui` prefix
    const filterWidgetsToFetch = (c: DashboardItem): boolean =>
        filterWidgetsToLoad(c) && !c.type.startsWith(GENERIC_PLATFORM_WIDGET_PREFIX);
    const widgetsNeedLoading = dashboard ? uniq(dashboard.children.filter(filterWidgetsToFetch).map(c => c.type)) : [];
    const widgetConstructors = await screenLoaderService.fetchWidgetDefinitions(getState, dispatch, widgetsNeedLoading);
    if (!widgetConstructors) {
        return;
    }

    const enumsToLoad: string[] =
        dashboard?.children?.reduce((prevValue, currentValue) => {
            return uniq([...prevValue, ...(currentValue.settings?.usedEnums || [])]);
        }, [] as string[]) || [];

    if (!isEmpty(enumsToLoad)) {
        const locale = getState().applicationContext?.locale || 'en-US';
        const enumTranslations = await fetchEnumTranslations(enumsToLoad, locale);
        dispatch(addTranslations(locale, enumTranslations));
    }

    const widgets =
        dashboard?.children.filter(filterWidgetsToLoad).map<WidgetDefinition>((item: DashboardItem) => {
            const widgetId = String(item._id);
            const dashboardId = String(dashboard._id);
            const widgetObject = createWidgetObject({
                widgetConstructors,
                type: item.type,
                widgetId,
                dashboardId,
                group,
                contextVariables,
                settings: item.settings,
            });
            return {
                artifactName: item.type,
                _id: String(item._id),
                widgetObject,
                properties: widgetObject.constructor.prototype.__properties,
                widgetType: widgetObject.constructor.prototype.__type,
                runtimeSettings: {},
            };
        }) || [];
    dispatch({ type: ActionType.AddWidgets, value: { widgets, group } });
    if (dashboard) {
        dispatch({ type: ActionType.AddDashboard, value: { dashboard, group } });
    }

    const widgetPromises = widgets.map(w => {
        return dispatch(loadWidgetData({ widgetId: w._id, group }));
    });

    Promise.all(widgetPromises).then(() => {
        dispatch({ type: ActionType.SetLoadingDashboards, value: { isLoading: false, group } });
    });
};

export const refreshCurrentDashboard =
    (group: string, contextVariables?: DashboardContextVariables) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const dashboard = Object.values(state.dashboard.dashboardGroups[group]?.dashboards || {}).find(
            d => d.isSelected,
        );
        if (dashboard) {
            await loadDashboard(dashboard, group, contextVariables, true, dispatch, getState);
        }
    };

export const fetchUserDashboardDefinition =
    (group: string, contextVariables?: DashboardContextVariables) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        dispatch({ type: ActionType.SetLoadingDashboards, value: { isLoading: true, group } });
        const state = getState();
        const locale = state.applicationContext?.locale || 'en-US';

        const shouldFetchPlatformLiterals = arePlatformLiteralsMissing(getState(), locale);
        const { dashboard, stringLiterals, availableDashboards, canEditDashboards } =
            await dashboardService.fetchCurrentDashboardDefinition(group, shouldFetchPlatformLiterals);

        if (shouldFetchPlatformLiterals) {
            dispatch(addTranslations(locale, stringLiterals));
        }
        await loadDashboard(dashboard, group, contextVariables, false, dispatch, getState);

        dispatch({
            type: ActionType.SetInitialDashboardInformation,
            value: { availableDashboards: availableDashboards || [], canEditDashboards, group },
        });
    };

export const setSelectedDashboard =
    (id: string, group: string, contextVariables?: DashboardContextVariables) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        getState().applicationContext?.onTelemetryEvent?.('dashboardTabSelected', { group });
        dispatch({ type: ActionType.SetLoadingDashboards, value: { isLoading: true, group } });
        const dashboard = await dashboardService.setSelectedDashboard(id, group);
        await loadDashboard(dashboard, group, contextVariables, false, dispatch, getState);
    };

/**
 * Clone a dashboard based on its ID, then marks it automatically selected
 * @param idToClone
 * @returns
 */
export const cloneDashboard =
    (idToClone: string, group: string, contextVariables?: DashboardContextVariables) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        getState().applicationContext?.onTelemetryEvent?.('dashboardCloned', { group });
        const { _id } = await dashboardService.cloneDashboard(idToClone);
        const dashboard = await dashboardService.setSelectedDashboard(_id, group);
        await loadDashboard(dashboard, group, contextVariables, false, dispatch, getState);
    };

/**
 * Creates an empty dashboard, then marks it automatically selected
 * @param idToClone
 * @returns
 */
export const createEmptyDashboard =
    (group: string, contextVariables?: DashboardContextVariables) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        getState().applicationContext?.onTelemetryEvent?.('emptyDashboardCreated', { group });
        const { _id } = await dashboardService.createEmptyDashboard(group);
        const dashboard = await dashboardService.setSelectedDashboard(_id, group);
        await loadDashboard(dashboard, group, contextVariables, false, dispatch, getState);
    };

export const deleteDashboard =
    (dashboardId: string, group: string, contextVariables?: DashboardContextVariables) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        getState().applicationContext?.onTelemetryEvent?.('dashboardDeleted', { group });
        await dashboardService.deleteDashboard(dashboardId);
        dispatch({ type: ActionType.RemoveDashboard, value: { dashboardId, group } });
        dispatch(fetchUserDashboardDefinition(group, contextVariables));
    };

export function getNewTableData({
    widgetDefinition,
    data,
}: {
    widgetDefinition: WidgetDefinition;
    data: any;
    group: string;
}): any {
    const tableProperties = widgetDefinition.properties as TableWidgetProperties<any>;
    const widgetObject = cloneDeepWith(widgetDefinition.widgetObject, (_value, key, object): any | undefined => {
        // Here we override the developer API so the new data can be accessed
        if (key === '_$' && object === widgetDefinition.widgetObject) {
            return { ...widgetDefinition.widgetObject.$, data };
        }
        return undefined;
    });
    const tableContent =
        typeof tableProperties.content === 'function'
            ? tableProperties.content.apply(widgetObject)
            : tableProperties.content;
    return (tableContent ?? []).map((r: any) => ({
        ...r,
        i: r._id,
        image: r.image ? getImageUrlFromValue(r.image) : undefined,
    }));
}
export const loadWidgetData =
    ({
        widgetId,
        group,
        forceRefetch = false,
        queryArgs,
        dataMode = 'SET',
        skipUnset = false,
    }: {
        widgetId: string;
        group: string;
        forceRefetch?: boolean;
        queryArgs?: LoadMoreRowsParams;
        dataMode?: 'SET' | 'ADD';
        skipUnset?: boolean;
    }) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<any[]> => {
        const state = getState();
        const widgetDefinition = state.dashboard.dashboardGroups[group].widgets[widgetId];
        const isTableWidget = widgetDefinition.widgetType === WidgetType.table;
        if (!skipUnset && (dataMode === 'SET' || !isTableWidget)) {
            dispatch({ type: ActionType.SetWidgetData, value: { widgetId, data: null, group } });
        }
        if (!widgetDefinition) {
            throw new Error(`Widget ${widgetId} is not in the state.`);
        }
        const artifactDescription = getArtifactDescription(widgetDefinition.artifactName);
        const pck = `${artifactDescription.vendor}/${artifactDescription.package}`;
        const version = state.applicationPackages![pck]!;
        if (dataMode !== 'ADD' && !skipUnset) {
            dispatch(SetWidgetLoading(widgetId, true));
        }
        const data = await dashboardService.fetchWidgetData({
            widgetDefinition,
            version,
            locale: state.applicationContext?.locale,
            forceRefetch,
            queryArgs,
            group,
        });
        if (dataMode !== 'ADD' && !skipUnset) {
            dispatch(SetWidgetLoading(widgetId, false));
        }
        if (widgetDefinition.widgetType === WidgetType.table) {
            const businessIcon = resolveWidgetProperty(
                (widgetDefinition.properties as TableWidgetProperties<any>).businessIcon,
                group,
                widgetDefinition.widgetObject || widgetDefinition._id,
            );
            dispatch({
                type: ActionType.SetWidgetProperties,
                value: { widgetId, properties: { businessIcon }, group },
            });
        }
        if (dataMode === 'SET' || !isTableWidget) {
            dispatch({ type: ActionType.SetWidgetData, value: { widgetId, data, group } });
        } else {
            dispatch({ type: ActionType.AddWidgetData, value: { widgetId, data, group } });
        }

        if (isTableWidget) {
            return getNewTableData({ widgetDefinition, data, group });
        }
        return data;
    };

export const setWidgetOptions = (dashboardId: string, widgetId: string, group: string, options: any): AppAction => ({
    type: ActionType.SetWidgetOptions,
    value: {
        dashboardId,
        widgetId,
        options,
        group,
    },
});

export const SetWidgetLoading = (widgetId: string, isLoading: boolean): AppAction => ({
    type: ActionType.SetWidgetLoading,
    value: { widgetId, isLoading },
});

export const setWidgetLoadingTimeoutId = (widgetId: string, timeoutId: number | null): AppAction => ({
    type: ActionType.SetWidgetLoadingTimeoutId,
    value: { widgetId, timeoutId },
});

export const resetWidgetLoadingTimeout = (widgetId: string): AppAction => ({
    type: ActionType.ResetWidgetLoadingTimeout,
    value: { widgetId },
});

export const removeDashboardGroup = (screenId: string): AppAction => ({
    type: ActionType.RemoveDashboardGroup,
    value: screenId,
});
