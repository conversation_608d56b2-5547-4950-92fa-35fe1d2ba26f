import { objectKeys } from '@sage/xtrem-shared';
import { fetchWidgetCategories } from '../../service/dashboard-service';
import { fetchNodeNames } from '../../service/node-information-service';
import { GraphQLTypes } from '../../types';
import { deepFindPropertyValues } from '../../utils/common-util';
import type { AppAction, AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { UserWidgetDefinition, XtremAppState } from '../state';

export const openWidgetEditorDialog =
    (group: string, widgetId?: string, widgetDefinition?: UserWidgetDefinition) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();

        if (objectKeys(state.dashboard.nodeNames).length === 0) {
            const nodeNames = await fetchNodeNames();
            dispatch({ type: ActionType.SetNodeNames, value: nodeNames });
        }
        if (objectKeys(state.dashboard.widgetCategories).length === 0) {
            const categories = await fetchWidgetCategories();
            dispatch({ type: ActionType.SetWidgetCategories, value: { categories } });
        }

        dispatch({
            type: ActionType.SetWidgetEditorOpen,
            value: {
                isOpen: true,
                widgetId,
                widgetDefinition,
                group,
            },
        });
    };

export const closeWidgetEditorDialog = (group: string): AppAction => ({
    type: ActionType.SetWidgetEditorOpen,
    value: { isOpen: false, group },
});

export const updateUserWidgetDefinition = (widgetDefinition: UserWidgetDefinition, group: string): AppAction => {
    const copiedWidgetDefinition = { ...widgetDefinition };
    // Extract list of used enums so we can load them next time the dashboard is accessed.
    copiedWidgetDefinition.usedEnums = deepFindPropertyValues(
        'node',
        widgetDefinition,
        (v: any) => v.type === GraphQLTypes.Enum,
    );
    return {
        type: ActionType.UpdateUserWidgetDefinition,
        value: { widget: copiedWidgetDefinition, group },
    };
};
