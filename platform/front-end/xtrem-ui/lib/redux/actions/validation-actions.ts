import type { ValidationResult } from '../../service/screen-base-definition';
import type { Dict } from '@sage/xtrem-shared';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';

export const setFieldValidationErrors = (
    screenId: string,
    elementId: string,
    errors: ValidationResult[] | ValidationResult,
): AppAction => ({
    type: ActionType.SetErrors,
    value: { screenId, elementId, errors: Array.isArray(errors) ? errors : [errors] },
});

export const removeNonNestedErrors = (screenId: string, elementId: string, columnId?: string): AppAction => ({
    type: ActionType.RemoveNonNestedError,
    value: { screenId, elementId, columnId },
});

export const updateNestedFieldValidationErrors = (
    screenId: string,
    elementId: string,
    validationErrors: ValidationResult[],
    columnId: string,
    recordId?: string,
): AppAction => ({
    type: ActionType.UpdateNestedFieldErrors,
    value: { screenId, elementId, validationErrors, columnId, recordId },
});

export const updateNestedFieldValidationErrorsForRecord = ({
    screenId,
    elementId,
    validationErrors,
    recordId,
    isUncommitted = false,
    level,
}: {
    screenId: string;
    elementId: string;
    validationErrors: ValidationResult[];
    recordId: string;
    isUncommitted: boolean;
    level?: number;
}): AppAction => ({
    type: ActionType.UpdateNestedFieldRecordErrors,
    value: { screenId, elementId, validationErrors, recordId, level, isUncommitted },
});

export const setPageValidationErrors = (
    screenId: string,
    validationErrors: Dict<ValidationResult[] | undefined>,
): AppAction => ({ type: ActionType.UpdateErrors, value: { screenId, validationErrors } });

export const addInternalError = (screenId: string, elementId: string, errorMessage: ValidationResult): AppAction => ({
    type: ActionType.AddInternalError,
    value: { screenId, elementId, errorMessage },
});

export const removeInternalError = (screenId: string, elementId: string): AppAction => ({
    type: ActionType.RemoveInternalError,
    value: { screenId, elementId },
});

export const removePageServerErrors = (screenId: string): AppAction => ({
    type: ActionType.RemovePageServerErrors,
    value: { screenId },
});

export const removePhantomErrors = (screenId: string, elementId: string): AppAction => ({
    type: ActionType.RemovePhantomError,
    value: { screenId, elementId },
});
