import { isEmpty, isNil } from 'lodash';
import type { NestedField } from '../../component/nested-fields';
import type { SidebarDefinitionDecorator } from '../../component/table-sidebar/table-sidebar-types';
import type { CardDefinition } from '../../component/ui/card/card-component';
import type { CollectionValue } from '../../service/collection-data-service';
import * as dialogService from '../../service/dialog-service';
import { openDirtyPageConfirmationDialog } from '../../service/dirty-state-service';
import { localize } from '../../service/i18n-service';
import type { PageDefinition } from '../../service/page-definition';
import { commitTransaction } from '../../service/transactions-service';
import type { TableSidebarDialogContent } from '../../types/dialogs';
import { resolveByValue } from '../../utils/resolve-value-utils';
import {
    getPageDefinitionFromState,
    getResolvedSidebarLayout,
    getSidebarNestedFields,
    getSidebarTableProperties,
    isSidebarDirty,
} from '../../utils/state-utils';
import { cleanMetadataFromRecord } from '../../utils/transformers';
import type { AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { XtremAppState } from '../state';

const handleOnSidebarOpenEvent = async ({
    sidebarDefinition,
    screenId,
    pageDefinition,
    recordValue,
    level,
}: {
    sidebarDefinition: SidebarDefinitionDecorator;
    screenId: string;
    pageDefinition: PageDefinition;
    recordValue: any;
    level?: number;
}): Promise<void> => {
    if (sidebarDefinition?.onRecordOpened) {
        // Trigger on record open event if the functional developer declared one
        await sidebarDefinition.onRecordOpened.apply(pageDefinition.page, [
            recordValue._id,
            cleanMetadataFromRecord(recordValue),
            level ?? 0,
        ]);
        commitTransaction(screenId);
    }
};

export const closeTableSidebar =
    (dialogId: number, suppressDiscardEvent = false) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const dialog = state.activeDialogs[dialogId];

        if (!dialog) {
            // Dialog was already closed by another action
            return;
        }

        const dialogContent = dialog.content as TableSidebarDialogContent;

        if (!dialog.screenId) {
            throw new Error('Dialog screenId is required for table-sidebar dialog');
        }

        const isDirty = isSidebarDirty(state);
        if (!suppressDiscardEvent) {
            if (isDirty) {
                try {
                    await openDirtyPageConfirmationDialog(dialog.screenId);
                } catch {
                    // If the user rejects the dialog, we won't continue
                    return;
                }
            }

            const pageDefinition = getPageDefinitionFromState(dialog.screenId, state);
            const tableProperties = getSidebarTableProperties({
                pageDefinition,
                elementId: dialogContent.elementId,
                level: dialogContent.level,
            });

            if (tableProperties.sidebar?.onRecordDiscarded) {
                await tableProperties.sidebar.onRecordDiscarded.apply(pageDefinition.page);
                commitTransaction(dialog.screenId);
            }

            const tableValue = pageDefinition.values[dialogContent.elementId] as CollectionValue;
            tableValue.cancelRecordTransaction({
                recordId: dialogContent.recordId || '-1',
                recordLevel: dialogContent.level,
            });
        }

        dialogService.closeDialog({ dialogId, suppressDiscardEvent: suppressDiscardEvent || !isDirty });
    };

export const openTableSidebar =
    ({
        cardDefinition,
        columns,
        dialogId,
        elementId,
        level,
        parentId,
        recordId,
        screenId,
        sidebarDefinition,
    }: {
        cardDefinition?: CardDefinition;
        columns: Array<NestedField<any, any>>;
        dialogId?: number;
        elementId: string;
        level?: number;
        parentId?: string;
        recordId?: string;
        screenId: string;
        sidebarDefinition?: SidebarDefinitionDecorator;
    }) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        if (!sidebarDefinition) {
            throw new Error(`No sidebar layout is defined for ${elementId}`);
        }

        const applicationContext = getState().applicationContext;

        let dialogControl: dialogService.DialogControl;

        if (dialogId) {
            dialogControl = getState().activeDialogs[dialogId].dialogControl;
            dispatch({
                type: ActionType.UpdateTableSidebarDialogTarget,
                value: {
                    dialogId,
                    nextRecordId: undefined,
                    prevRecordId: undefined,
                    recordId: undefined,
                    isNewRecord: false,
                    title: localize('@sage/xtrem-ui/dialog-loading', 'Loading...'),
                },
            });
        } else {
            dialogControl = dialogService.openTableSidebarDialog({
                screenId,
                elementId,
                sidebarDefinition,
                cardDefinition,
                level,
                columns,
            });
            dialogControl.catch(() => {
                dispatch(closeTableSidebar(dialogControl.id, true));
            });
        }

        let pageDefinition = getPageDefinitionFromState(screenId, getState());
        const tableProperties = getSidebarTableProperties({ pageDefinition, elementId, level });
        const tableValue = pageDefinition.values[elementId] as CollectionValue;

        const recordValue = recordId
            ? tableValue.getRecordByIdAndLevel({ id: recordId, level }) // Modify the current record
            : await tableValue.createNewPhantomRow({ isUncommitted: true, level, parentId }); // Create a new record

        // We need fresh values here in case of the callback made any changes
        pageDefinition = getPageDefinitionFromState(screenId, getState());
        let prevRecord;
        let nextRecord;
        if (recordId) {
            // We don't need to start a record transaction for new record because the createNewPhantomRow is already called with the isUncommitted flag.
            tableValue.startRecordTransaction({ recordId, recordLevel: level });

            prevRecord = await tableValue.getPreviousRecord({ recordId, recordLevel: level });
            nextRecord = await tableValue.getNextRecord({ recordId, tableProperties, recordLevel: level });
        }

        applicationContext?.onTelemetryEvent?.(`tableSidebarOpened-${elementId}-${level || 0}`, {
            elementId,
            screenId,
            level,
        });

        await handleOnSidebarOpenEvent({
            sidebarDefinition,
            screenId,
            pageDefinition,
            recordValue,
            level,
        });

        const sidebarTitle = resolveByValue({
            propertyValue: sidebarDefinition.title,
            fieldValue: recordValue?._id,
            rowValue: recordValue,
            screenId,
            skipHexFormat: true,
        });

        const fieldTitle = resolveByValue({
            propertyValue: tableProperties?.title,
            screenId,
            rowValue: null,
            fieldValue: null,
            skipHexFormat: true,
        });

        const title = sidebarTitle || fieldTitle || elementId;
        applicationContext?.onPageTitleChange?.(title, null);
        await dispatch({
            type: ActionType.UpdateTableSidebarDialogTarget,
            value: {
                title,
                dialogId: dialogControl.id,
                nextRecordId: nextRecord?._id,
                prevRecordId: prevRecord?._id,
                recordId: recordId ?? recordValue._id,
                isNewRecord: isNil(recordId),
            },
        });

        const updatedState = getState();
        updatedState.applicationContext?.updateMenu(updatedState.menuItems);
    };

export const confirmTableSidebar =
    (dialogId: number, addNewRecord = false) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const dialog = state.activeDialogs[dialogId];
        const dialogContent = dialog.content as TableSidebarDialogContent;
        const { level, elementId, recordId, sidebarDefinition } = dialogContent;

        if (!dialog.screenId) {
            throw new Error('Dialog screenId is required for table-sidebar dialog');
        }

        const pageDefinition = getPageDefinitionFromState(dialog.screenId, state);
        const tableProperties = getSidebarTableProperties({
            elementId,
            pageDefinition,
            level,
        });

        const collectionValue = pageDefinition.values[dialogContent.elementId] as CollectionValue;

        if (recordId) {
            const rawRecord = collectionValue.getRawRecord({
                id: recordId,
                isUncommitted: true,
                level,
                cleanMetadata: false,
            });

            const sidebarLayoutDefinition = getResolvedSidebarLayout({
                layout: sidebarDefinition.layout,
                elementId,
                nodeTypes: state.nodeTypes,
                level,
                screenDefinition: pageDefinition,
                value: collectionValue,
            });

            const result = await collectionValue.runValidationOnRecord({
                recordData: rawRecord,
                columnsToRevalidate: getSidebarNestedFields(sidebarLayoutDefinition),
                isUncommitted: true,
            });

            if (!isEmpty(result)) {
                // If validations fail, we won't proceed
                return;
            }

            const updateRecord = collectionValue.commitRecord({ recordId, recordLevel: dialogContent.level });
            if (tableProperties.sidebar?.onRecordConfirmed || sidebarDefinition?.onRecordConfirmed) {
                const onRecordConfirmed =
                    tableProperties.sidebar?.onRecordConfirmed || (sidebarDefinition?.onRecordConfirmed as any);
                await onRecordConfirmed.apply(pageDefinition.page, [recordId, cleanMetadataFromRecord(updateRecord)]);
            }
        }

        getState().applicationContext?.onTelemetryEvent?.(
            `tableSidebarConfirmed-${dialogContent.elementId}-${dialogContent.level || 0}`,
            {
                elementId: dialogContent.elementId,
                screenId: dialog.screenId,
                level: dialogContent.level,
            },
        );

        if (!addNewRecord) {
            dispatch(closeTableSidebar(dialogId, true));
        } else {
            dispatch(
                openTableSidebar({
                    ...dialogContent,
                    recordId: undefined,
                    dialogId,
                    screenId: dialog.screenId,
                }),
            );
        }
    };

const sidebarNextOrPrevRecord = async ({
    dispatch,
    getState,
    isNext,
    dialogId,
}: {
    dispatch: AppThunkDispatch;
    getState: () => XtremAppState;
    isNext: boolean;
    dialogId: number;
}): Promise<void> => {
    const state = getState();
    const dialog = state.activeDialogs[dialogId];
    const dialogContent = dialog.content as TableSidebarDialogContent;

    if (!dialog.screenId) {
        throw new Error('Dialog screenId is required for table-sidebar dialog');
    }

    const pageDefinition = getPageDefinitionFromState(dialog.screenId, state);

    const tableProperties = getSidebarTableProperties({
        pageDefinition,
        elementId: dialogContent.elementId,
        level: dialogContent.level,
    });

    const recordId = dialogContent.recordId;
    if (!recordId) {
        return;
    }

    const isDirty = isSidebarDirty(state);
    if (isDirty) {
        try {
            await openDirtyPageConfirmationDialog(dialog.screenId);
        } catch {
            // If the user rejects the dialog, we won't continue
            return;
        }
    }

    const collectionValue = pageDefinition.values[dialogContent.elementId] as CollectionValue;
    if (isNext) {
        state.applicationContext?.onTelemetryEvent?.(
            `tableSidebarNextRecordButtonClicked-${dialogContent.elementId}-${dialogContent.level || 0}`,
            {
                elementId: dialogContent.elementId,
                screenId: dialog.screenId,
                level: dialogContent.level,
            },
        );
        const nextRecord = await collectionValue.getNextRecord({
            recordId,
            tableProperties,
            recordLevel: dialogContent.level,
        });
        if (nextRecord) {
            collectionValue.cancelRecordTransaction({ recordId, recordLevel: dialogContent.level });
            await dispatch(
                openTableSidebar({
                    ...dialogContent,
                    screenId: dialog.screenId,
                    recordId: nextRecord._id,
                    dialogId,
                }),
            );
        }
    } else {
        state.applicationContext?.onTelemetryEvent?.(
            `tableSidebarPreviousRecordButtonClicked-${dialogContent.elementId}-${dialogContent.level || 0}`,
            {
                elementId: dialogContent.elementId,
                screenId: dialog.screenId,
                level: dialogContent.level,
            },
        );
        const previousRecord = await collectionValue.getPreviousRecord({
            recordId,
            recordLevel: dialogContent.level,
        });
        if (previousRecord) {
            collectionValue.cancelRecordTransaction({ recordId, recordLevel: dialogContent.level });
            await dispatch(
                openTableSidebar({
                    ...dialogContent,
                    screenId: dialog.screenId,
                    recordId: previousRecord._id,
                    dialogId,
                }),
            );
        }
    }
};

export const selectSidebarNextRecord =
    (dialogId: number) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        return sidebarNextOrPrevRecord({ dispatch, getState, isNext: true, dialogId });
    };

export const selectSidebarPreviousRecord =
    (dialogId: number) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        return sidebarNextOrPrevRecord({ dispatch, getState, isNext: false, dialogId });
    };
