import type { WorkflowNode } from '@sage/xtrem-shared';
import * as xtremRedux from '..';
import { fetchWorkflowNodes } from '../../service/graphql-service';

export const loadWorkflowNodes =
    () =>
    async (dispatch: xtremRedux.AppThunkDispatch, getState: () => xtremRedux.XtremAppState): Promise<void> => {
        const state = getState();
        if (state.workflowNodes) {
            return;
        }

        const workflowNodes = await fetchWorkflowNodes();

        await dispatch(setWorkflowNodes(workflowNodes));
    };

export const setWorkflowNodes = (workflowNodes: WorkflowNode[]): xtremRedux.AppAction => ({
    type: xtremRedux.ActionType.SetWorkflowNodes,
    value: workflowNodes,
});
