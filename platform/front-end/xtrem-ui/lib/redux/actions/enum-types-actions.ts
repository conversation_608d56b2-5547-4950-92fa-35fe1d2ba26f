import { objectKeys } from '@sage/xtrem-shared';
import { uniq, without } from 'lodash';
import type { XtremAppState } from '..';
import { fetchEnumDefinitions } from '../../service/metadata-service';
import type { AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';

export const loadEnumType =
    (enumNames: string[], locale: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const enumsToFetch = without(uniq(enumNames), ...objectKeys(getState().enumTypes));
        const enumDefinitions = await fetchEnumDefinitions(enumsToFetch);

        dispatch({ type: ActionType.AddEnumType, value: { enums: enumDefinitions, locale } });
    };
