PATH: XTREEM/Client+Framework/Validation+API

## Introduction

This page summarizes how validation works in general in the framework. For field type specific validation methods please check the corresponding field documentation page. Validations are triggered when the user leaves the validated field, but they can triggered programmatically as well.

## Defining general validation rules

All editable field can be validated by defining the `validation` decorator property. This property can be a function that returns a `string` or a function that returns a `promise` which resolves to a `string`. If the returned value is empty or undefined, the validation is considered to be successful. If a truly string is returned, it is used as a validation error message that is displayed to the user.

In addition to that, the `validation` decorator property can be defined as a regular expression. In this case the value is matched against the regular expression and if it is not met, a validation error message is displayed to the user.

## Various validation properties

There is a number of other validation related decorator properties provided, but they are only available to a certain set of fields:

- `isMandatory`
- `min`
- `max`
- `minLength`
- `maxLength`
- `minDate`
- `maxDate`
- `isNotZero`

When these validation decorator properties are used, the validation error message is provided by the framework. It briefly explains the problem and refers to the field using its label or element ID.

## Validation rules when the field disabled or hidden

When a field is hidden or disabled, it is excluded from validation and always considered to be valid regardless to its value. This rule also applies if the parent container (or the parent's parent and so on) is hidden. When a container is hidden all of its children fields are considered to be valid.

## Triggering validations

Validations can be triggered programmatically in event callbacks, for example to validate the content of a page before executing a save operation. The validations can be triggered on field or container level using the `validate()` method. For fields it returns a promise that resolves to a string, for containers it returns a promise that resolves to a string array. This array holds the validation error messages of various fields. If the array is empty, the container can be considered to be valid.

When the validation is triggered programmatically, the user interface is updated too.
