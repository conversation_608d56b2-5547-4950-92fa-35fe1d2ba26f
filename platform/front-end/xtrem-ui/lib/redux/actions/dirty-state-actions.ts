import type { PageDecoratorProperties } from '../../component/container/page/page-decorator';
import { notifyConsumerAboutDirtyStatus } from '../../service/dirty-state-service';
import { xtremConsole } from '../../utils/console';
import { triggerScreenEvent } from '../../utils/events';
import { hasAnyDirtyScreenDefinitions, isScreenDefinitionDirty } from '../../utils/state-utils';
import type { AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { XtremAppState } from '../state';

export const setFieldDirty =
    ({ screenId, elementId }: { screenId: string; elementId: string }) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        const state = getState();
        const screenDefinition = state.screenDefinitions[screenId];
        if (!screenDefinition) {
            xtremConsole.warn(`Could not find screen definition with id '${screenId}'`);
            return;
        }

        if (
            !(screenDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<any>).skipDirtyCheck
        ) {
            const originalPageDirtyState = isScreenDefinitionDirty(screenDefinition);
            const originalAppDirtyState = hasAnyDirtyScreenDefinitions(state);

            // If it is a user initiated, organic change and the page is currently clean, we tell the screen listener that the screen has just become dirty.
            if (!originalPageDirtyState) {
                triggerScreenEvent(screenId, 'onDirtyStateUpdated', true);
            }

            // If it is a user initiated, organic change and the application is currently clean, we tell the consumer listener that the application has just become dirty.
            if (!originalAppDirtyState) {
                notifyConsumerAboutDirtyStatus(state, true);
            }
        }

        dispatch({
            type: ActionType.SetFieldDirtyState,
            value: {
                screenId,
                elementId,
            },
        });
    };

export const setFieldClean =
    ({ screenId, elementId }: { screenId: string; elementId: string }) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        const state = getState();
        const screenDefinition = state.screenDefinitions[screenId];
        if (!screenDefinition) {
            xtremConsole.warn(`Could not find screen definition with id '${screenId}'`);
            return;
        }

        const originalPageDirtyState = isScreenDefinitionDirty(screenDefinition);
        const originalAppDirtyState = hasAnyDirtyScreenDefinitions(state);

        dispatch({
            type: ActionType.SetFieldCleanState,
            value: {
                screenId,
                elementId,
            },
        });

        if (
            !(screenDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<any>).skipDirtyCheck
        ) {
            const updatedState = getState();
            const currentPageDirtyState = isScreenDefinitionDirty(updatedState.screenDefinitions[screenId]);
            const currentAppDirtyState = hasAnyDirtyScreenDefinitions(updatedState);

            // If it is a user initiated, organic change and the page is currently clean, we tell the screen listener that the screen has just become dirty.
            if (originalPageDirtyState !== currentPageDirtyState) {
                triggerScreenEvent(screenId, 'onDirtyStateUpdated', currentPageDirtyState);
            }

            // If it is a user initiated, organic change and the application is currently clean, we tell the consumer listener that the application has just become dirty.
            if (originalAppDirtyState !== currentAppDirtyState) {
                notifyConsumerAboutDirtyStatus(state, currentAppDirtyState);
            }
        }
    };
