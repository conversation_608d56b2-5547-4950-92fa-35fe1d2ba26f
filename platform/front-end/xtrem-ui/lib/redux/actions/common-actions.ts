import type { Dict } from '@sage/xtrem-shared';
import type { UiComponentProperties as dirtyStateActions } from '../../component/abstract-ui-control-object';
import type { InternalSectionProperties } from '../../component/container/section/section-types';
import type { PageControlObject, PageProperties } from '../../component/control-objects';
import { closeDialog } from '../../service/dialog-service';
import { dispatchContainerValidation } from '../../service/dispatch-service';
import { fetchRecordData } from '../../service/graphql-service';
import type { ProcessedNodeDetails } from '../../service/metadata-service';
import { processNodeDetailsResponse, queryInitialMetadata, queryMetadata } from '../../service/metadata-service';
import type { DataTypeDetails, FormattedNodeDetails } from '../../service/metadata-types';
import type { PageDefinition } from '../../service/page-definition';
import type { XtremUiPlugin } from '../../service/plugin-service';
import type { ScreenBaseDefinition } from '../../service/screen-base-definition';
import { showContainerValidationToast } from '../../service/toast-service';
import { formatScreenValues } from '../../service/value-formatter-service';
import { filterFields } from '../../utils/abstract-fields-utils';
import {
    clearOutOfDateEntries,
    getArtifactCacheDatabase,
    getCachedRawNodeDetails,
} from '../../utils/artifact-cache-utils';
import { WIZARD_FINISHED } from '../../utils/constants';
import { triggerFieldEvent, triggerScreenEvent } from '../../utils/events';
import { getPageDefinitionFromState, getVisibleSectionsFromPage } from '../../utils/state-utils';
import { parseScreenValues } from '../../utils/transformers';
import { isDevMode } from '../../utils/window';
import type { AppAction, AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { ApplicationContext, XtremAppState } from '../state';
import { removeDashboardGroup } from './dashboard-actions';

export const setPath = (path: string): AppAction => ({
    type: ActionType.SetPath,
    value: path,
});

export const setComponentLoading = (screenId: string, elementId: string, isLoading: boolean): AppAction => ({
    type: ActionType.SetComponentLoading,
    value: { screenId, elementId, isLoading },
});

export const setApplicationContext = (applicationContext: ApplicationContext): AppAction => ({
    type: ActionType.SetApplicationContext,
    value: applicationContext,
});

export const finishScreenLoading = (
    pageDefinition: ScreenBaseDefinition,
    nodeTypes: Dict<FormattedNodeDetails>,
    dataTypes: Dict<DataTypeDetails>,
    enumTypes: Dict<{ name: string; values: string[]; translations: string[] }>,
    locale: string,
): AppAction => ({
    type: ActionType.FinishScreenLoading,
    value: { pageDefinition, nodeTypes, dataTypes, enumTypes, locale },
});

export const addSchemaInfo = (
    nodeTypes: Dict<FormattedNodeDetails>,
    dataTypes: Dict<DataTypeDetails>,
    enumTypes: Dict<{ name: string; values: string[]; translations: string[] }>,
    locale: string,
): AppAction => ({
    type: ActionType.AddSchemaInfo,
    value: { nodeTypes, dataTypes, enumTypes, locale },
});

export const addScreenDefinition = (pageDefinition: ScreenBaseDefinition): AppAction => ({
    type: ActionType.AddScreenDefinition,
    value: pageDefinition,
});

export const addPlugin = (pluginPackage: string, pluginDefinition: XtremUiPlugin<any, any>): AppAction => ({
    type: ActionType.AddPlugin,
    value: {
        pluginPackage,
        pluginDefinition,
    },
});

export const setScreenDefinitionReady = (screenId: string): AppAction => ({
    type: ActionType.SetScreenDefinitionReady,
    value: screenId,
});

export const setScreenDefinitionDialogId = (
    screenId: string,
    dialogId: number,
    pageControlObject: PageControlObject,
    title: string,
    subtitle: string,
    forceSetTitle?: boolean,
): AppAction => ({
    type: ActionType.SetScreenDefinitionDialogId,
    value: { screenId, dialogId, pageControlObject, title, subtitle, forceSetTitle },
});

export const removeScreenDefinition = (screenId: string): AppAction => ({
    type: ActionType.RemoveScreenDefinition,
    value: screenId,
});

export const setFieldProperties = (
    screenId: string,
    elementId: string,
    fieldProperties: dirtyStateActions,
): AppAction => ({
    type: ActionType.SetUiComponentProperties,
    value: {
        screenId,
        elementId,
        fieldProperties,
    },
});

export const setGlobalLoading = (loaderState: boolean): AppAction => ({
    type: ActionType.SetGlobalLoading,
    value: loaderState,
});

export const actionStub = (): any => {
    throw new Error('Action stub called, did you forget to override the the action?');
};

export const fetchTranslationByPackage =
    (packageKey: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const applicationContext = state.applicationContext;

        const result = await queryMetadata({
            metadataType: 'strings',
            metadataProperties: ['key', 'content'],
            path: packageKey,
        });

        if (result && result.strings) {
            const literals = result.strings.reduce((acc: Dict<string>, item: { key: string; content: string }) => {
                acc[item.key] = item.content;
                return acc;
            }, {});

            dispatch({
                type: ActionType.AddTranslations,
                value: {
                    locale: applicationContext?.locale || 'en-US',
                    literals,
                },
            });
        }
    };

export const addTranslations = (locale: string, literals: Dict<string>): AppAction => ({
    type: ActionType.AddTranslations,
    value: {
        locale,
        literals,
    },
});

export const setInitialMetaData =
    (applicationContext: ApplicationContext) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        if (!state.applicationPackages) {
            let processedNodeDetails: ProcessedNodeDetails | null = null;
            const initialMetaData = await queryInitialMetadata();

            if (!isDevMode()) {
                const db = await getArtifactCacheDatabase();
                const rawNodeDetails = await getCachedRawNodeDetails({
                    db,
                    installedPackages: initialMetaData.installedPackages,
                    passphrase: applicationContext.cacheEncryptionKey,
                });
                processedNodeDetails = processNodeDetailsResponse(rawNodeDetails);

                // Clear up the out of date artifacts from the cache
                await clearOutOfDateEntries({ db, installedPackages: initialMetaData.installedPackages });
            }

            dispatch({
                type: ActionType.SetInitialMetaData,
                value: {
                    ...initialMetaData,
                    nodeTypes: processedNodeDetails?.nodeTypes || {},
                    dataTypes: processedNodeDetails?.dataTypes || {},
                    enumTypes: processedNodeDetails?.enumTypes || {},
                    locale: applicationContext.locale || 'en-US',
                },
            });
        }
    };

export const clearWidgetOptions = (): AppAction => ({
    type: ActionType.ClearWidgetOptions,
    value: null,
});

export const set360ViewState =
    (screenId: string, state: boolean, isUsingButton = false) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        triggerScreenEvent(screenId, 'on360ViewSwitched', state);
        if (isUsingButton) {
            getState().applicationContext?.onTelemetryEvent?.('360viewSwitched', { screenId, state });
        }
        if (!state) {
            dispatch(removeDashboardGroup(`${screenId}-360`));
        }

        dispatch({
            type: ActionType.Set360ViewState,
            value: { screenId, state },
        });
    };
/**
 * Sets the active section in the state
 * @param screenId
 * @param activeSection the ID of the active section OR `WIZARD_FINISHED` token if the user is stepping to close the widget.
 * @param isInitialLoad
 * @returns
 */
export const setActiveSection =
    (screenId: string, activeSection: string | typeof WIZARD_FINISHED | null, isInitialLoad = false) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const pageDefinition = getPageDefinitionFromState(screenId, state);
        const sections = getVisibleSectionsFromPage(screenId, pageDefinition);

        getState().applicationContext?.onTelemetryEvent?.(`sectionActivated-${activeSection}`, {
            screenId,
            activeSection,
        });

        const currentlyActiveSection =
            pageDefinition.activeSection || getVisibleSectionsFromPage(screenId, pageDefinition)[0]?.id;

        if (currentlyActiveSection === activeSection) {
            // If the current active section is the same that we are about to change to, we should not proceed
            return;
        }

        if (currentlyActiveSection && pageDefinition.page.$.mode === 'wizard') {
            // In case of wizard mode we need to run the validations before we can change the tabs

            // We need to check if we are stepping forward or backwards in the wizard, we only validate on forward steps
            const indexOfCurrentSection = sections.findIndex(s => s.id === pageDefinition.activeSection);
            const indexOfNextSection = sections.findIndex(s => s.id === activeSection);

            if (indexOfCurrentSection < indexOfNextSection) {
                const result = await dispatchContainerValidation(
                    pageDefinition.page._pageMetadata.screenId,
                    currentlyActiveSection,
                );

                // If the validation fails, we display the page error dialog

                if (result.blockingErrors.length > 0) {
                    showContainerValidationToast(pageDefinition.page._pageMetadata, result);
                    return;
                }
            }
        }

        if (!isInitialLoad && currentlyActiveSection) {
            await triggerFieldEvent(screenId, currentlyActiveSection, 'onInactive');
        }

        if (activeSection === WIZARD_FINISHED) {
            if (pageDefinition.dialogId) {
                await closeDialog({
                    dialogId: pageDefinition.dialogId,
                    result: parseScreenValues(screenId, true, false, state),
                    isWizardCompleted: true,
                });
            } else {
                triggerScreenEvent(screenId, 'onClose', true);
            }
            return;
        }

        const sectionProperties = activeSection
            ? (pageDefinition.metadata.uiComponentProperties[activeSection] as InternalSectionProperties)
            : null;

        const shouldLoadLazyLoadedSection =
            !!activeSection && !!sectionProperties?.isLazyLoaded && !sectionProperties.isLoaded;

        await dispatch({
            type: ActionType.SetActiveSection,
            value: { activeSection, screenId, isLoading: shouldLoadLazyLoadedSection },
        });

        if (shouldLoadLazyLoadedSection) {
            // If the section is lazy loaded, we need to load it before we can set it as active

            const values = await fetchRecordData({
                screenId,
                recordId: pageDefinition.selectedRecordId ? String(pageDefinition.selectedRecordId) : undefined,
                screenDefinition: pageDefinition,
                nodeTypes: state.nodeTypes,
                requiredSections: [activeSection],
                skipHeaderFields: true,
            });

            const formattedValues = formatScreenValues({
                controlObjects: filterFields(pageDefinition.metadata.controlObjects, [activeSection], pageDefinition),
                nodeTypes: state.nodeTypes,
                parentNode: String((sectionProperties as PageProperties).node),
                plugins: state.plugins,
                screenId,
                values,
                userSettings: pageDefinition.userSettings,
            });

            await dispatch({
                type: ActionType.SetSectionValues,
                value: {
                    screenId,
                    values: formattedValues,
                    sectionId: activeSection,
                },
            });
        }

        if (!isInitialLoad && activeSection) {
            await triggerFieldEvent(screenId, activeSection, 'onActive');
        }

        if (activeSection) {
            await dispatch({
                type: ActionType.SetSectionReady,
                value: {
                    screenId,
                    sectionId: activeSection,
                },
            });
        }
    };

export const stepOneSection =
    (screenId: string, direction: 1 | -1) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const screenDefinitions = state.screenDefinitions;
        // This function is only called for wizard page dialogs, so here we don't need to handle the case of custom dialogs
        const sections = getVisibleSectionsFromPage(screenId, screenDefinitions[screenId]);
        const activeSection = (screenDefinitions[screenId] as PageDefinition).activeSection || sections[0]?.id;
        const currentIndex = sections.findIndex(s => screenId && s.id === activeSection);
        const nextIndex = currentIndex + direction;
        if (nextIndex === sections.length) {
            // If we step out of the range of sections, that means that the wizard process is now complete.
            return dispatch(setActiveSection(screenId, WIZARD_FINISHED));
        }
        return dispatch(setActiveSection(screenId, sections[nextIndex].id));
    };
