import type { Dict } from '@sage/xtrem-shared';
import type { OptionsMenuItem } from '../../component/control-objects';
import type {
    TableUserSettings,
    TableViewGrouping,
    TableViewSortedColumn,
} from '../../component/field/table/table-component-types';
import { createUserClientSettings, updateUserClientSettings } from '../../service/metadata-service';
import type { AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { UiComponentUserSettings, XtremAppState } from '../state';
import { navigationPanelId } from '../../component/container/navigation-panel/navigation-panel-types';
import { pageDialog } from '../../service/dialog-service';
import {
    fetchUserClientSettingsForElement,
    selectView,
    unselectView,
} from '../../service/user-client-settings-service';
import { localize } from '../../service/i18n-service';
import {
    getNavigationPanelTablePropertiesFromPageDefinition,
    getPageDefinitionFromState,
} from '../../utils/state-utils';
import { createNavigationPanelDefaultView } from '../../service/navigation-panel-service';
import { DEFAULT_VIEW_ID } from '../../utils/constants';

export const storeUserClientSettings =
    (screenId: string, elementId: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const view = state.screenDefinitions[screenId].userSettings[elementId]?.$current;
        await updateUserClientSettings(view);
        dispatch({
            type: ActionType.SetUserCustomizationSaved,
            value: {
                screenId,
                elementId,
                viewId: view._id,
            },
        });
    };

export const saveTableUserClientSettingsForPageElement =
    (screenId: string, elementId: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        // We do not want to autosave the navigation panel settings as it has its own view management system
        if (elementId === navigationPanelId) {
            return;
        }

        const state = getState();
        const view = state.screenDefinitions[screenId].userSettings[elementId]?.$current as TableUserSettings;
        let viewId = view._id;
        const viewWithoutFilters: TableUserSettings = {
            ...view,
            content: [
                {
                    ...view.content[0],
                    filter: undefined,
                    optionsMenuItem: undefined,
                    searchText: undefined,
                    grouping: undefined,
                },
            ],
        };

        if (viewId && viewId !== DEFAULT_VIEW_ID) {
            await updateUserClientSettings(viewWithoutFilters);
        } else {
            viewId = await createUserClientSettings(screenId, elementId, viewWithoutFilters);
        }

        dispatch({
            type: ActionType.SetUserCustomizationSaved,
            value: {
                screenId,
                elementId,
                viewId,
            },
        });
    };

export const setTableViewColumnHidden =
    (screenId: string, elementId: string, level: number, columnHidden?: Dict<boolean>) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        getState().applicationContext?.onTelemetryEvent?.('tableVisibilityChange', {
            screenId,
            elementId,
        });
        dispatch({
            type: ActionType.SetTableViewColumnHidden,
            value: { screenId, elementId, level, columnHidden },
        });
        dispatch(saveTableUserClientSettingsForPageElement(screenId, elementId));
    };

export const setTableViewColumnOrder =
    (screenId: string, elementId: string, level: number, columnOrder?: string[]) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        getState().applicationContext?.onTelemetryEvent?.('tableColumnOrderChange', {
            screenId,
            elementId,
        });
        dispatch({
            type: ActionType.SetTableViewColumnOrder,
            value: { screenId, elementId, level, columnOrder },
        });
        dispatch(saveTableUserClientSettingsForPageElement(screenId, elementId));
    };

export const setTableViewFilter =
    (screenId: string, elementId: string, level: number, filter?: any) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        getState().applicationContext?.onTelemetryEvent?.('tableFiltered', {
            elementId,
            screenId,
        });
        dispatch({
            type: ActionType.SetTableViewFilter,
            value: { screenId, elementId, level, filter },
        });
    };

export const setTableViewGrouping =
    (screenId: string, elementId: string, level: number, grouping?: TableViewGrouping) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        getState().applicationContext?.onTelemetryEvent?.('tableGroupingChange', {
            elementId,
            screenId,
            item: grouping?.key,
        });
        dispatch({
            type: ActionType.SetTableViewGrouping,
            value: { screenId, elementId, level, grouping },
        });
    };

export const setTableViewOptionMenuItem =
    (screenId: string, elementId: string, level: number, optionsMenuItem?: OptionsMenuItem) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        getState().applicationContext?.onTelemetryEvent?.('tableOptionsMenuChange', {
            elementId,
            item: optionsMenuItem?.title,
            screenId,
        });

        dispatch({
            type: ActionType.SetTableViewOptionsMenuItem,
            value: { screenId, elementId, level, optionsMenuItem },
        });
    };

export const setTableViewOptionsMenuItemAndViewFilter =
    (screenId: string, elementId: string, level: number, optionsMenuItem?: OptionsMenuItem, filter?: any) =>
    (dispatch: AppThunkDispatch): void => {
        dispatch({
            type: ActionType.SetTableViewOptionsMenuItem,
            value: { screenId, elementId, level, optionsMenuItem, filter },
        });
    };

export const setTableViewSearchText =
    (screenId: string, elementId: string, level: number, searchText: string) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        getState().applicationContext?.onTelemetryEvent?.('tableQuickTextFiltered', {
            elementId,
            screenId,
        });
        dispatch({
            type: ActionType.SetTableViewSearchText,
            value: { screenId, elementId, level, searchText },
        });
    };

export const clearNavigationPanelSearchText =
    (screenId: string) =>
    (dispatch: AppThunkDispatch): void => {
        dispatch({
            type: ActionType.ClearNavigationPanelSearchText,
            value: { screenId },
        });
    };

export const setTableViewSortOrder =
    (screenId: string, elementId: string, level: number, sortOrder?: TableViewSortedColumn[]) =>
    (dispatch: AppThunkDispatch): void => {
        dispatch({
            type: ActionType.SetTableViewSortOrder,
            value: { screenId, elementId, level, sortOrder },
        });
        dispatch(saveTableUserClientSettingsForPageElement(screenId, elementId));
    };

export const selectMainListView =
    (screenId: string, view: UiComponentUserSettings) =>
    async (dispatch: AppThunkDispatch): Promise<void> => {
        if (view?._id) {
            await dispatch({
                type: ActionType.SetElementUserSettings,
                value: { screenId, elementId: navigationPanelId, userSettings: view },
            });

            await selectView(screenId, navigationPanelId, view._id);
        }
    };
export const unselectMainListView =
    (screenId: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const pageDefinition = getPageDefinitionFromState(screenId, state);
        const defaultTableProperties = getNavigationPanelTablePropertiesFromPageDefinition(pageDefinition, true);
        await dispatch({
            type: ActionType.SetElementUserSettings,
            value: {
                screenId,
                elementId: navigationPanelId,
                userSettings: createNavigationPanelDefaultView({
                    screenId,
                    defaultTableProperties,
                    accessBindings: pageDefinition.accessBindings,
                    dataTypes: state.dataTypes,
                    nodeTypes: state.nodeTypes,
                }),
            },
        });

        await unselectView(screenId, navigationPanelId);
    };

export const openCreateNewViewDialog =
    (screenId: string, elementId: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<string | null> => {
        const state = getState();
        if (!state.clientUserSettingsEditPage) {
            return null;
        }

        const view = state.screenDefinitions[screenId]?.userSettings[navigationPanelId]?.$current;
        const isDefaultView = !view || view._id === DEFAULT_VIEW_ID;
        const result = await pageDialog<{ _id?: string }>(
            { dispatch, getState },
            state.clientUserSettingsEditPage,
            { screenId, elementId },
            {
                hasGreyBackground: true,
                resolveOnCancel: true,
                title: !isDefaultView
                    ? localize('@sage/xtrem-ui/save-new-view-as', 'Save view as')
                    : localize('@sage/xtrem-ui/create-a-view', 'Create a view'),
                subtitle: !isDefaultView
                    ? localize('@sage/xtrem-ui/create-a-new-view', 'Create a new view')
                    : localize('@sage/xtrem-ui/default-view-cannot-be-edited', 'Default view cannot be updated.'),
                values: {
                    content: view?.content ? JSON.stringify(view.content) : '{}',
                    screenId,
                    elementId,
                },
            },
        );

        if (!result?._id) {
            return null;
        }

        await selectView(screenId, elementId, result._id);

        return result._id;
    };

export const openManageViewsDialog =
    (screenId: string, elementId: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<UiComponentUserSettings[]> => {
        const state = getState();
        if (state.clientUserSettingsListPage) {
            await pageDialog(
                { dispatch, getState },
                state.clientUserSettingsListPage,
                { screenId, elementId },
                {
                    resolveOnCancel: true,
                    hasGreyBackground: true,
                },
            );
        }

        return fetchUserClientSettingsForElement(screenId, navigationPanelId);
    };
