import type { AbstractResponsiveWidget } from '@sage/bms-dashboard';
import { objectKeys, type Dashboard, type DashboardItem, type Dict } from '@sage/xtrem-shared';
import { cloneDeep, isEqual, last, noop, uniqBy } from 'lodash';
import { navigationPanelId } from '../../component/container/navigation-panel/navigation-panel-types';
import type {
    OptionsMenuItem,
    PageActionProperties,
    PageNavigationPanel,
    PageProperties,
    TableProperties,
} from '../../component/control-objects';
import { PageActionControlObject } from '../../component/control-objects';
import * as nestedFields from '../../component/nested-fields';
import { FieldKey } from '../../component/types';
import type { CardDefinition } from '../../component/ui/card/card-component';
import type { DashboardBreakpoint, DashboardContextVariables } from '../../dashboard/dashboard-types';
import {
    GENERIC_PLATFORM_WIDGET_PREFIX,
    getGenericWidgetArtifactName,
    getGenericWidgetDefinition,
} from '../../dashboard/generic-widgets';
import { getWidgetDefaultSize } from '../../dashboard/widget-editor/widget-editor-utils';
import { CollectionValue } from '../../service/collection-data-service';
import * as dashboardService from '../../service/dashboard-service';
import { localize } from '../../service/i18n-service';
import type { FormattedNodeDetails } from '../../service/metadata-types';
import type { PageDefinition } from '../../service/page-definition';
import { getNewPageDefinition } from '../../service/page-definition';
import { getNewPageMetadata } from '../../service/page-metadata';
import * as screenLoaderService from '../../service/screen-loader-service';
import { DASHBOARD_SCREEN_ID } from '../../utils/constants';
import type { AppAction, AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { UserWidgetDefinition, XtremAppState } from '../state';
import { createWidgetObject, fetchUserDashboardDefinition, loadWidgetData } from './dashboard-actions';
import { closeWidgetEditorDialog, openWidgetEditorDialog } from './widget-editor-actions';

/** Incremental negative IDs for new widgets */
let nextWidgetId = 0;

export const resetNextWidgetIdOnlyToBeUsedInTests = (): void => {
    nextWidgetId = 0;
};

export const getWidgetListCardDefinition = (
    dispatch: AppThunkDispatch,
    group: string,
    contextVariables?: DashboardContextVariables,
): CardDefinition<any> => {
    const title = nestedFields.text<any, dashboardService.XtremUiWidget>({
        bind: 'title',
    });

    const line2 = nestedFields.text<any, dashboardService.XtremUiWidget>({
        bind: 'description',
    });

    const category = nestedFields.reference<
        any,
        dashboardService.XtremUiWidget,
        dashboardService.XtremUiWidgetCategory
    >({
        bind: 'category',
        valueField: 'title',
        helperTextField: 'sortValue',
        node: '@sage/xtrem-ui/XtremUiWidgetCategory',
        isHidden: true,
    });

    const line3Right = nestedFields.link<any, dashboardService.XtremUiWidget>({
        bind: 'type',
        isTransient: true,
        icon: 'add',
        map() {
            return localize('@sage/xtrem-ui/dashboard-editor-add-widget', 'Add');
        },
        onClick(_value, widget: dashboardService.XtremUiWidget) {
            dispatch(addFunctionalDeveloperDefinedWidget(widget, group, contextVariables));
        },
    });

    const image = nestedFields.icon<any, dashboardService.XtremUiWidget>({
        bind: 'type',
        // iconSize: 'medium', enable this line once https://github.com/Sage/carbon/issues/5658 is fixed
        backgroundSize: 'large',
        map: value => {
            switch (value) {
                case 'table':
                    return 'csv';
                case 'indicatorTile':
                    return 'copy';
                case 'lineChart':
                    return 'chart_line';
                case 'barChart':
                    return 'chart_bar';
                case 'gauge':
                    return 'computer_clock';
                case 'visualProcess':
                    return 'arrow_left_right_small';
                default:
                    return 'copy';
            }
        },
    });

    return {
        image,
        title,
        line2,
        line3Right,
        category,
    };
};

export const saveDashboardEditorState =
    (group: string, contextVariables?: DashboardContextVariables) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const { widgets, dashboardEditor } = getState().dashboard.dashboardGroups[group];
        const dashboardDefinition = dashboardEditor.currentDashboardDefinition;
        const dashboard = await dashboardService.updateDashboard(dashboardDefinition);
        // The new widgets get new IDs on the server so we need to ensure that those are loaded in the client state
        await Promise.all(
            dashboard.children.map(async (dashboardItem: DashboardItem): Promise<void> => {
                if (!widgets[dashboardItem._id]) {
                    await dispatch(
                        loadNewWidget(
                            dashboardItem.type,
                            dashboardItem._id,
                            dashboard._id,
                            group,
                            contextVariables,
                            dashboardItem.settings,
                        ),
                    );
                }
            }),
        );
        dispatch(fetchUserDashboardDefinition(group, contextVariables));
    };

export const createDashboardPageDefinition = (
    dispatch: AppThunkDispatch,
    group: string,
    widgets: dashboardService.XtremUiWidget[],
    categories: dashboardService.XtremUiWidgetCategory[],
    contextVariables?: DashboardContextVariables,
    nodeTypes?: Dict<FormattedNodeDetails>,
    locale?: string,
): PageDefinition => {
    const navigationPanelCard = getWidgetListCardDefinition(dispatch, group, contextVariables);
    const columns = Object.values(navigationPanelCard) as nestedFields.NestedField<
        any,
        nestedFields.NestedFieldTypes
    >[];

    const createWidgetActionId = 'createWidget';

    const orderBy = {
        category: { sortValue: 1 },
        title: 1,
    };

    const optionsMenu: OptionsMenuItem[] = [
        { title: localize('@sage/xtrem-ui/dashboard-editor-all-widgets', 'All widgets'), graphQLFilter: {} },
        ...categories.map<OptionsMenuItem>(c => ({
            graphQLFilter: { category: { _id: c._id } },
            title: c.title,
        })),
    ];

    const widgetListCollectionValue = new CollectionValue<dashboardService.XtremUiWidget>({
        columnDefinitions: [columns],
        elementId: navigationPanelId,
        hasNextPage: false,
        initialValues: widgets,
        isNoServerLookups: true,
        isTransient: true,
        locale,
        nodes: ['@sage/xtrem-ui/XtremUiWidget'],
        nodeTypes,
        orderBy: [orderBy as any],
        screenId: DASHBOARD_SCREEN_ID,
        activeOptionsMenuItem: optionsMenu?.[0],
    });

    const createActionProperties: PageActionProperties<any> = {
        icon: 'add',
        title: localize('@sage/xtrem-ui/dashboard-editor-widget-list-add', 'Create a widget'),
        onClick: () => dispatch(openWidgetEditorDialog(group)),
    };

    const createAction = new PageActionControlObject({
        screenId: DASHBOARD_SCREEN_ID,
        elementId: createWidgetActionId,
        setUiComponentProperties: noop,
        getUiComponentProperties: (): PageActionProperties<any> => ({
            icon: 'add',
            title: localize('@sage/xtrem-ui/dashboard-editor-widget-list-add', 'Create a widget'),
        }),
    });

    return {
        ...(getNewPageDefinition() as PageDefinition),
        values: { _id: '-1' },
        type: 'page',
        page: { $: {} } as any,
        navigationPanel: {
            groupByField: 'category',
            isHeaderHidden: false,
            isHidden: false,
            isOpened: true,
            isRefreshing: false,
            value: widgetListCollectionValue,
        },
        userSettings: {
            [navigationPanelId]: {
                $current: {
                    _id: '$current',
                    title: '$current',
                    description: '$current',
                    content: [{}],
                },
            },
        },
        metadata: {
            ...getNewPageMetadata(DASHBOARD_SCREEN_ID),
            uiComponentProperties: {
                [DASHBOARD_SCREEN_ID]: {
                    title: localize('@sage/xtrem-ui/dashboard-editor-widget-list-title', 'Widgets'),
                    navigationPanel: {} as PageNavigationPanel<any>,
                    // TODO: Remove this condition once we can add custom widgets to 360 views too
                    ...(group === 'home' ? { createAction: () => createAction } : {}),
                } as PageProperties,
                [createWidgetActionId]: createActionProperties,
                [navigationPanelId]: {
                    _controlObjectType: FieldKey.Table,
                    columns,
                    orderBy,
                    optionsMenu,
                    hasSearchBoxMobile: true,
                    isTransient: true,
                    mobileCard: navigationPanelCard,
                } as TableProperties,
            },
        },
    };
};

export const openDashboardEditorDialog =
    (group: string, contextVariables?: DashboardContextVariables) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const { widgets, categories } = await dashboardService.getWidgetList(group);

        const dashboardPageDefinition = createDashboardPageDefinition(
            dispatch,
            group,
            widgets,
            categories,
            contextVariables,
            state.nodeTypes,
            state.applicationContext?.locale,
        );
        dispatch({
            type: ActionType.AddScreenDefinition,
            value: dashboardPageDefinition,
        });

        /** We open the dashboard editor with a specific dashboard definition */
        const activeDashboard = objectKeys(state.dashboard.dashboardGroups[group].dashboards).find(
            d => state.dashboard.dashboardGroups[group].dashboards[d].isSelected,
        );
        if (!activeDashboard) {
            return;
        }

        dispatch({
            type: ActionType.OpenDashboardEditor,
            value: { dashboard: state.dashboard.dashboardGroups[group].dashboards[activeDashboard], group },
        });
    };

export const closeDashboardEditorDialog = (group: string): AppAction => ({
    type: ActionType.SetDashboardEditorDialogOpen,
    value: { isOpen: false, group },
});

export const undoDashboardEditor =
    (group: string) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        const state = getState();
        state.applicationContext?.onTelemetryEvent?.('dashboardEditorUndo', { group });
        const { dashboardEditor } = state.dashboard.dashboardGroups[group];
        dispatch({
            type: ActionType.UpdateDashboardEditorFromHistory,
            value: {
                historyIndex: dashboardEditor.currentHistoryIndex - 1,
                dashboard: dashboardEditor.history[dashboardEditor.currentHistoryIndex - 1],
                group,
            },
        });
    };

export const redoDashboardEditor =
    (group: string) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        const state = getState();
        state.applicationContext?.onTelemetryEvent?.('dashboardEditorRedo', { group });
        const { dashboardEditor } = state.dashboard.dashboardGroups[group];
        dispatch({
            type: ActionType.UpdateDashboardEditorFromHistory,
            value: {
                historyIndex: dashboardEditor.currentHistoryIndex + 1,
                dashboard: dashboardEditor.history[dashboardEditor.currentHistoryIndex + 1],
                group,
            },
        });
    };

export const updateDashboardEditorStateWithHistory =
    (newDashboardDefinition: Dashboard, group: string) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        const { dashboardEditor } = getState().dashboard.dashboardGroups[group];

        const lastDashboardDefinition = cloneDeep(dashboardEditor.currentDashboardDefinition);
        if (!isEqual(newDashboardDefinition, lastDashboardDefinition)) {
            const history = [
                ...dashboardEditor.history.slice(0, dashboardEditor.currentHistoryIndex + 1),
                newDashboardDefinition,
            ];
            const dashboard = last(history) as Dashboard;
            dispatch({
                type: ActionType.UpdateDashboardEditorWithHistory,
                value: {
                    history,
                    dashboard,
                    group,
                },
            });
        }
    };

export const removeDashboardEditorWidget =
    (widgetId: string, group: string) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        const state = getState();
        const { dashboardEditor } = state.dashboard.dashboardGroups[group];

        const newDashboardDefinition = cloneDeep(dashboardEditor.currentDashboardDefinition);
        const widgetIndex = newDashboardDefinition.children.findIndex(w => w._id === widgetId);

        state.applicationContext?.onTelemetryEvent?.('dashboardWidgetRemoved', {
            type: newDashboardDefinition.children[widgetIndex].type,
        });

        newDashboardDefinition.children.splice(widgetIndex, 1);
        dispatch(updateDashboardEditorStateWithHistory(newDashboardDefinition, group));
    };

export const updateDashboardTitleInEditor =
    (newTitle: string, group: string) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        const { dashboardEditor } = getState().dashboard.dashboardGroups[group];
        const newDashboardDefinition = cloneDeep(dashboardEditor.currentDashboardDefinition);
        newDashboardDefinition.title = newTitle;
        dispatch(updateDashboardEditorStateWithHistory(newDashboardDefinition, group));
    };

/**
 * Fetches widget source code from the server. It should be called when a new widget is added to the dashboard which has not been previously
 * loaded into the state from the server.
 *
 */
export const loadNewWidget =
    (
        widgetType: string,
        widgetId: string,
        dashboardId: string,
        group: string,
        contextVariables?: DashboardContextVariables,
        settings?: any,
    ) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const widgetConstructors = widgetType.startsWith(GENERIC_PLATFORM_WIDGET_PREFIX)
            ? {}
            : await screenLoaderService.fetchWidgetDefinitions(getState, dispatch, [widgetType]);
        if (widgetConstructors) {
            const widgetObject = createWidgetObject({
                widgetConstructors,
                type: widgetType,
                widgetId,
                dashboardId,
                settings,
                group,
                contextVariables,
            });
            const widgets = [
                {
                    artifactName: widgetType,
                    _id: widgetId,
                    widgetObject,
                    properties: widgetObject.constructor.prototype.__properties,
                    widgetType: widgetObject.constructor.prototype.__type,
                    runtimeSettings: {},
                },
            ];

            dispatch({ type: ActionType.AddWidgets, value: { widgets, group } });
            await dispatch(loadWidgetData({ widgetId, group }));
        }
    };

export const updateDashboardEditorWidgetPositions =
    ({
        widgets,
        group,
        contextVariables,
        breakpoint,
    }: {
        widgets: AbstractResponsiveWidget[];
        group: string;
        contextVariables?: DashboardContextVariables;
        breakpoint: DashboardBreakpoint;
    }) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const { dashboardEditor } = getState().dashboard.dashboardGroups[group];

        const newDashboardDefinition = cloneDeep(dashboardEditor.currentDashboardDefinition);
        const newWidgetsToBeLoaded: DashboardItem[] = [];

        newDashboardDefinition.children = widgets.map<DashboardItem>(w => {
            const details = dashboardEditor.currentDashboardDefinition.children.find(
                i => Number(i._id) === Number(w.i),
            );
            if (!details) {
                nextWidgetId -= 1;
                const widgetId = String(nextWidgetId);
                const widgetType = (w as any as dashboardService.XtremUiWidget)._id;
                const newWidget: DashboardItem = {
                    _id: widgetId,
                    type: widgetType,
                    positions: [
                        { x: w.xxs.x, y: w.xxs.y, w: w.xxs.w, h: w.xxs.h, breakpoint: 'xxs' },
                        { x: w.xs.x, y: w.xs.y, w: w.xs.w, h: w.xs.h, breakpoint: 'xs' },
                        { x: w.sm.x, y: w.sm.y, w: w.sm.w, h: w.sm.h, breakpoint: 'sm' },
                        { x: w.md.x, y: w.md.y, w: w.md.w, h: w.md.h, breakpoint: 'md' },
                        { x: w.lg.x, y: w.lg.y, w: w.lg.w, h: w.lg.h, breakpoint: 'lg' },
                    ],
                    settings: {},
                };
                newWidgetsToBeLoaded.push(newWidget);
                return newWidget;
            }
            return {
                ...details,
                positions: uniqBy(
                    [
                        ...details.positions,
                        { x: w[breakpoint].x, y: w[breakpoint].y, breakpoint, w: w[breakpoint].w, h: w[breakpoint].h },
                    ],
                    i => i.breakpoint,
                ).map(p =>
                    p.breakpoint === breakpoint
                        ? { x: w[breakpoint].x, y: w[breakpoint].y, breakpoint, w: w[breakpoint].w, h: w[breakpoint].h }
                        : p,
                ),
            } satisfies DashboardItem;
        });

        await Promise.all(
            newWidgetsToBeLoaded.map<Promise<void>>(widget =>
                dispatch(
                    loadNewWidget(
                        widget.type,
                        widget._id,
                        newDashboardDefinition._id,
                        group,
                        contextVariables,
                        widget.settings,
                    ),
                ),
            ),
        );

        dispatch(updateDashboardEditorStateWithHistory(newDashboardDefinition, group));
    };

export const addNewWidgetToDashboardLayout =
    (widgetId: string, artifactName: string, group: string, uwd: UserWidgetDefinition = {}) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        const { dashboardEditor } = getState().dashboard.dashboardGroups[group];
        // Calculate the first available position by adding the widget height and its Y position
        const nextYSlots = (['xxs', 'xs', 'sm', 'md', 'lg'] as const).reduce(
            (acc, b) => {
                acc[b] = Math.max(
                    ...dashboardEditor.currentDashboardDefinition.children.map(c => {
                        const position = c.positions.find(p => p.breakpoint === b);
                        return Math.max(position?.y ?? 0, 0) + (position?.h ?? 1);
                    }),
                );
                return acc;
            },
            {} as {
                xxs: number;
                xs: number;
                sm: number;
                md: number;
                lg: number;
            },
        );
        const defaultSize = getWidgetDefaultSize(uwd);
        const newWidget: DashboardItem = {
            _id: widgetId,
            type: artifactName,
            positions: [
                { ...defaultSize, x: 0, y: nextYSlots.xxs, breakpoint: 'xxs' },
                { ...defaultSize, x: 0, y: nextYSlots.xs, breakpoint: 'xs' },
                { ...defaultSize, x: 0, y: nextYSlots.sm, breakpoint: 'sm' },
                { ...defaultSize, x: 0, y: nextYSlots.md, breakpoint: 'md' },
                { ...defaultSize, x: 0, y: nextYSlots.lg, breakpoint: 'lg' },
            ],
            settings: uwd,
        };

        const children = [...dashboardEditor.currentDashboardDefinition.children, newWidget];
        dispatch(
            updateDashboardEditorStateWithHistory({ ...dashboardEditor.currentDashboardDefinition, children }, group),
        );
    };

export const updateWidgetSettings =
    (widgetId: string, group: string, settings: any = {}) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const { dashboardEditor } = getState().dashboard.dashboardGroups[group];
        const children = cloneDeep(dashboardEditor.currentDashboardDefinition.children);
        const widgetIndex = children.findIndex(i => i._id === widgetId);
        children[widgetIndex].settings = settings;
        dispatch(
            updateDashboardEditorStateWithHistory({ ...dashboardEditor.currentDashboardDefinition, children }, group),
        );
        await dispatch(loadWidgetData({ forceRefetch: true, widgetId, group }));
    };

export const addFunctionalDeveloperDefinedWidget =
    (widget: dashboardService.XtremUiWidget, group: string, contextVariables?: DashboardContextVariables) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const { dashboardEditor } = getState().dashboard.dashboardGroups[group];
        const artifactName = widget._id; // The ID is the artifact ID
        nextWidgetId -= 1;
        const widgetId = String(nextWidgetId);
        getState().applicationContext?.onTelemetryEvent?.('dashboardFunctionalWidgetAdded', {
            type: artifactName,
        });
        await dispatch(
            loadNewWidget(
                artifactName,
                widgetId,
                dashboardEditor.currentDashboardDefinition._id,
                group,
                contextVariables,
                {},
            ),
        );
        await dispatch(addNewWidgetToDashboardLayout(widgetId, artifactName, group));
    };

/**
 * Takes the current working copy of from the widget editor and adds it to the dashboard definition.
 * @returns
 */
export const finishEditingUserDefinedWidget =
    (group: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const dashboardState = getState().dashboard.dashboardGroups[group];
        const dashboardId = dashboardState.dashboardEditor.currentDashboardDefinition._id;
        const userWidgetDefinition = dashboardState.widgetEditor.widgetDefinition;
        const isNewWidget = !dashboardState.widgetEditor.widgetId;
        if (isNewWidget) {
            nextWidgetId -= 1;
        }
        const widgetId = isNewWidget ? String(nextWidgetId) : String(dashboardState.widgetEditor.widgetId);
        const widgetObject = getGenericWidgetDefinition(userWidgetDefinition, dashboardId, widgetId, group);

        const artifactName = getGenericWidgetArtifactName(userWidgetDefinition);

        const widgets = [
            {
                artifactName,
                _id: widgetId,
                widgetObject,
                properties: widgetObject.constructor.prototype.__properties,
                widgetType: widgetObject.constructor.prototype.__type,
                runtimeSettings: {},
            },
        ];
        getState().applicationContext?.onTelemetryEvent?.('dashboardCustomWidgetAdded', {
            type: userWidgetDefinition.type,
            group,
        });

        dispatch({ type: ActionType.AddWidgets, value: { widgets, group } });

        await dispatch(loadWidgetData({ widgetId, group }));
        await dispatch(closeWidgetEditorDialog(group));

        if (isNewWidget) {
            await dispatch(addNewWidgetToDashboardLayout(widgetId, artifactName, group, userWidgetDefinition));
        } else {
            await dispatch(updateWidgetSettings(widgetId, group, userWidgetDefinition));
        }
    };
