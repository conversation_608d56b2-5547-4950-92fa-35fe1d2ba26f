import { getMockState, getMockStore } from '../../../__tests__/test-helpers';

import * as dashboardService from '../../../service/dashboard-service';
import * as nodeInformationService from '../../../service/node-information-service';
import * as widgetEditorActions from '../widget-editor-actions';
import { ActionType } from '../../action-types';
import type { TableProperty, UserWidgetDefinition, XtremAppState } from '../../state';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { GraphQLTypes } from '../../../types';
import { FieldKey } from '../../../component/types';

const fixtureNodeNames = {
    '@sage/xtrem-test/ATestNode': 'ZZZZZZ',
    '@sage/xtrem-test/AnotherTestNode': 'AAAA',
    '@sage/xtrem-test/ThirdTestNode': 'BBBB',
};

const fixtureCategories = {
    SOME_CATEGORY: 'Some Category',
    ANOTHER_CATEGORY: 'Another Category',
};

const testDashboardGroup = 'home';

describe('widget editor actions', () => {
    let mockState: XtremAppState;
    let mockStore: MockStoreEnhanced<XtremAppState>;

    beforeEach(() => {
        jest.spyOn(nodeInformationService, 'fetchNodeNames').mockResolvedValue(fixtureNodeNames);

        jest.spyOn(dashboardService, 'fetchWidgetCategories').mockResolvedValue(fixtureCategories);

        mockState = getMockState({
            dashboard: {
                dashboardGroups: {
                    home: {
                        dashboards: {
                            '1234': {
                                isSelected: true,
                                _id: '1234',
                                title: 'dashboard',
                                children: [
                                    {
                                        _id: '123',
                                        type: '@sage/xtrem-test/FakeWidget',
                                        positions: [
                                            { x: 1, y: 1, breakpoint: 'xxs', w: 1, h: 1 },
                                            { x: 1, y: 1, breakpoint: 'xs', w: 1, h: 1 },
                                            { x: 1, y: 1, breakpoint: 'sm', w: 1, h: 1 },
                                            { x: 1, y: 1, breakpoint: 'md', w: 1, h: 1 },
                                            { x: 1, y: 1, breakpoint: 'lg', w: 1, h: 1 },
                                        ],
                                        settings: {},
                                    },
                                ],
                            },
                        },
                        widgets: {},
                        dashboardEditor: {
                            currentDashboardDefinition: {
                                _id: '',
                                title: '',
                                children: [],
                            },
                            currentHistoryIndex: 0,
                            history: [],
                            isDirty: false,
                            isOpen: true,
                        },
                        widgetEditor: {
                            isOpen: false,
                            isDirty: false,
                            widgetDefinition: {},
                        },
                        availableDashboards: [],
                    },
                },
                canEditDashboards: true,
                widgetCategories: fixtureCategories,
                nodeNames: fixtureNodeNames,
            },
        });

        mockStore = getMockStore(mockState);
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('openWidgetEditorDialog', () => {
        it('should not fetch the widget categories and the list of nodes if they are already cached in the state', async () => {
            expect(nodeInformationService.fetchNodeNames).not.toHaveBeenCalled();
            expect(dashboardService.fetchWidgetCategories).not.toHaveBeenCalled();
            await widgetEditorActions.openWidgetEditorDialog('home', '')(mockStore.dispatch, () => mockState);
            expect(nodeInformationService.fetchNodeNames).not.toHaveBeenCalled();
            expect(dashboardService.fetchWidgetCategories).not.toHaveBeenCalled();

            const actions = mockStore.getActions();
            expect(actions).toHaveLength(1);
            expect(actions[0].type).toEqual(ActionType.SetWidgetEditorOpen);
            expect(actions[0].value).toEqual({ isOpen: true, group: 'home', widgetId: '' });
        });

        it('should fetch the widget categories and the list of nodes and cache them to the state if they were not previously cached', async () => {
            mockState.dashboard.nodeNames = {};
            mockState.dashboard.widgetCategories = {};
            mockStore = getMockStore(mockState);

            expect(nodeInformationService.fetchNodeNames).not.toHaveBeenCalled();
            expect(dashboardService.fetchWidgetCategories).not.toHaveBeenCalled();
            await widgetEditorActions.openWidgetEditorDialog(testDashboardGroup)(mockStore.dispatch, () => mockState);
            expect(nodeInformationService.fetchNodeNames).toHaveBeenCalled();
            expect(dashboardService.fetchWidgetCategories).toHaveBeenCalled();

            const actions = mockStore.getActions();
            expect(actions).toHaveLength(3);
            expect(actions[0].type).toEqual(ActionType.SetNodeNames);
            expect(actions[0].value).toEqual(fixtureNodeNames);
            expect(actions[1].type).toEqual(ActionType.SetWidgetCategories);
            expect(actions[1].value).toEqual({ categories: fixtureCategories });
            expect(actions[2].type).toEqual(ActionType.SetWidgetEditorOpen);
            expect(actions[2].value).toEqual({ isOpen: true, group: 'home' });
        });
    });

    describe('closeWidgetEditorDialog', () => {
        it('should return the close action', () => {
            const result = widgetEditorActions.closeWidgetEditorDialog(testDashboardGroup);
            expect(result.type).toEqual(ActionType.SetWidgetEditorOpen);
            expect((result as any).value).toEqual({ isOpen: false, group: 'home' });
        });
    });

    describe('updateUserWidgetDefinition', () => {
        it('should return the action with the updated value', () => {
            const userWidgetDefinition = {
                category: fixtureCategories[0],
                node: fixtureNodeNames[0],
                title: 'Test title',
            };
            const result = widgetEditorActions.updateUserWidgetDefinition(userWidgetDefinition, testDashboardGroup);
            expect(result.type).toEqual(ActionType.UpdateUserWidgetDefinition);
            expect((result as any).value).toEqual({
                widget: { ...userWidgetDefinition, usedEnums: [] },
                group: 'home',
            });
        });

        it('should extract the list of used enums', () => {
            const userWidgetDefinition: UserWidgetDefinition = {
                category: fixtureCategories[0],
                node: fixtureNodeNames[0],
                title: 'Test title',
                columns: [
                    {
                        label: 'Test',
                        data: { node: 'TestEnum', type: GraphQLTypes.Enum },
                        id: 'test',
                        labelPath: 'tst',
                        title: 'Test',
                        presentation: FieldKey.Date,
                    } as TableProperty,
                    {
                        label: 'Test',
                        data: { node: 'TestNode', type: GraphQLTypes.IntReference },
                        id: 'test',
                        labelPath: 'tst',
                        title: 'Test',
                        presentation: FieldKey.Date,
                    } as TableProperty,
                ],
            };
            const result = widgetEditorActions.updateUserWidgetDefinition(userWidgetDefinition, testDashboardGroup);
            expect(result.type).toEqual(ActionType.UpdateUserWidgetDefinition);
            expect((result as any).value).toEqual({
                widget: { ...userWidgetDefinition, usedEnums: ['TestEnum'] },
                group: 'home',
            });
        });
    });
});
