import { loadEnumType } from '../enum-types-actions';
import * as metadataService from '../../../service/metadata-service';
import { getMockStore } from '../../../__tests__/test-helpers/mock-store-helpers';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../state';

const enumGraphQLTypes = {
    OrderCityEnum: {
        name: '@sage/xtrem-test/OrderCityEnum',
        values: ['BCN', 'Madrid', 'Valencia', 'Paris'],
        translations: [],
    },
    OrderFoodEnum: {
        name: '@sage/xtrem-test/OrderFoodEnum',
        values: ['PatatasBravas', 'Churros', 'Paella', 'Croissant'],
        translations: [],
    },
    OrderDrinkEnum: {
        name: '@sage/xtrem-test/OrderDrinkEnum',
        values: ['Vermute', 'TintoVerano', 'Orxata', 'Champagne'],
        translations: [],
    },
};

describe('Enum Types Actions', () => {
    let mockStore: MockStoreEnhanced<XtremAppState>;

    beforeEach(() => {
        jest.spyOn(metadataService, 'fetchEnumDefinitions').mockResolvedValue(enumGraphQLTypes);
        mockStore = getMockStore();
    });
    it('should transform GraphQL type info to a simple string array dictionary', async () => {
        await loadEnumType(
            ['@sage/xtrem-test/OrderCityEnum', '@sage/xtrem-test/OrderFoodEnum', '@sage/xtrem-test/OrderDrinkEnum'],
            'en-US',
        );
        expect(mockStore.getActions()).toEqual([]);
    });
});
