import * as reduxStore from '../../store';
import { nestedFields } from '../../..';
import { getMockState, getMockStore } from '../../../__tests__/test-helpers';
import type { TableProperties } from '../../../component/control-objects';
import type { TableSidebarDialogContent } from '../../../types/dialogs';
import * as stateUtils from '../../../utils/state-utils';
import { ActionType } from '../../action-types';
import type { XtremAppState } from '../../state';
import {
    closeTableSidebar,
    confirmTableSidebar,
    openTableSidebar,
    selectSidebarNextRecord,
    selectSidebarPreviousRecord,
} from '../table-sidebar-actions';
import type { TableDecoratorProperties } from '../../../component/decorator-properties';

describe('Table Sidebar Actions', () => {
    let state: Partial<XtremAppState>;
    const mockedCancelRecordTransaction = jest.fn();
    const mockedStartRecordTransaction = jest.fn();
    let mockedGetPrevRecord: jest.Mock;
    let mockedGetNextRecord: jest.Mock;
    let mockCreateNewPhantomRow: jest.Mock;
    let mockGetRecordByIdAndLevel: jest.Mock;
    let mockCommitRecord: jest.Mock;
    let mockGetRawRecord: jest.Mock;
    let mockRunValidationOnRecord: jest.Mock;
    let mockOnRecordOpen: jest.Mock;

    beforeEach(() => {
        mockCreateNewPhantomRow = jest.fn().mockResolvedValue({ _id: '-1', value: 'whatever' });
        mockGetRawRecord = jest.fn().mockReturnValue({ _id: '-1', value: 'whatever' });
        mockedGetPrevRecord = jest.fn().mockResolvedValue({ _id: 'recordId0', value: 'whatever' });
        mockedGetNextRecord = jest.fn().mockResolvedValue({ _id: 'recordId2', value: 'whatever' });
        mockGetRecordByIdAndLevel = jest.fn(({ id }: { id: string }) => ({ _id: id, value: 'whatever' }));
        mockCommitRecord = jest.fn().mockResolvedValue({ _id: 'recordId', value: 'whatever' });
        mockRunValidationOnRecord = jest.fn().mockResolvedValue({});
        mockOnRecordOpen = jest.fn();
        jest.spyOn(stateUtils, 'isSidebarDirty').mockReturnValue(false);
        jest.spyOn(stateUtils, 'isScreenDefinitionDirty').mockReturnValue(true);

        const sidebarDefinition = {
            onRecordOpened: mockOnRecordOpen,
            layout: {
                mainSection: {
                    title: 'Section title',
                    blocks: {
                        mainBlock: {
                            fields: ['field1, field2'],
                            title: 'Test block',
                        },
                    },
                },
            },
        };
        const columns = [
            nestedFields.text<any>({
                bind: 'field1',
                title: 'Test text field',
                isMandatory: true,
            }),
            nestedFields.numeric<any>({
                bind: 'field2',
                title: 'Test numeric field',
                scale: 2,
            }),
            nestedFields.date<any>({
                bind: { nested: { binding: true } },
                title: 'Test date field',
            }),
        ];

        state = {
            activeDialogs: {
                1: {
                    isSticker: false,
                    buttons: {},
                    level: 'info',
                    options: {},
                    dialogControl: {
                        id: 1,
                        resolve: jest.fn(),
                        reject: jest.fn(),
                    } as any,
                    dialogId: 1,
                    screenId: 'screenId',
                    content: {
                        elementId: 'elementId',
                        recordId: 'recordId',
                        nextRecordId: '2',
                        prevRecordId: '3',
                        sidebarDefinition,
                        columns,
                    } as TableSidebarDialogContent,
                },
            },
            screenDefinitions: {
                screenId: {
                    metadata: {
                        uiComponentProperties: {
                            elementId: {
                                sidebar: sidebarDefinition,
                                columns,
                            } as TableProperties,
                        },
                    } as any,
                    values: {
                        elementId: {
                            cancelRecordTransaction: mockedCancelRecordTransaction,
                            getRecordByIdAndLevel: mockGetRecordByIdAndLevel,
                            startRecordTransaction: mockedStartRecordTransaction,
                            getNextRecord: mockedGetNextRecord,
                            getPreviousRecord: mockedGetPrevRecord,
                            createNewPhantomRow: mockCreateNewPhantomRow,
                            commitRecord: mockCommitRecord,
                            getRawRecord: mockGetRawRecord,
                            runValidationOnRecord: mockRunValidationOnRecord,
                            getColumnDefinitions: jest.fn(() => columns),
                        },
                    },
                } as any,
            },
        };
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('closeTableSidebar', () => {
        it('should dispatch an action to close the table sidebar', async () => {
            const mockState = getMockState(state);
            const mockStore = getMockStore(mockState);
            jest.spyOn(reduxStore, 'getStore').mockReturnValue(mockStore);

            await closeTableSidebar(1)(mockStore.dispatch, mockStore.getState);

            expect(mockStore.getActions()).toEqual([
                {
                    type: ActionType.RemoveMenuItem,
                    value: 'CloseDialogMenuItem',
                },
                {
                    type: ActionType.CloseDialog,
                    value: 1,
                },
            ]);
            expect(mockedCancelRecordTransaction).toHaveBeenCalledWith({ recordId: 'recordId' });
        });

        it('should only dispatch the CloseTableSidebar action as suppressDiscardEvent is true', async () => {
            const mockState = getMockState(state);
            const mockStore = getMockStore(mockState);
            jest.spyOn(reduxStore, 'getStore').mockReturnValue(mockStore);

            await closeTableSidebar(1, true)(mockStore.dispatch, mockStore.getState);

            expect(mockStore.getActions()).toEqual([
                {
                    type: ActionType.RemoveMenuItem,
                    value: 'CloseDialogMenuItem',
                },
                {
                    type: ActionType.CloseDialog,
                    value: 1,
                },
            ]);
            expect(mockedCancelRecordTransaction).not.toHaveBeenCalled();
        });
    });

    describe('openTableSidebar', () => {
        it('should dispatch an action to open the table sidebar', async () => {
            const mockState = getMockState(state);
            const mockStore = getMockStore(mockState);
            jest.spyOn(reduxStore, 'getStore').mockReturnValue(mockStore);
            const tableProperties = mockState.screenDefinitions.screenId.metadata.uiComponentProperties
                .elementId as TableDecoratorProperties;
            await openTableSidebar({
                screenId: 'screenId',
                elementId: 'elementId',
                recordId: 'recordId',
                columns: tableProperties.columns || [],
                sidebarDefinition: tableProperties.sidebar,
            })(mockStore.dispatch, mockStore.getState);
            const expected = [
                {
                    type: ActionType.AddMenuItem,
                    value: expect.objectContaining({
                        alignedLeft: true,
                        category: 'dialog',
                        dialogId: 1,
                        icon: 'cross',
                        id: 'CloseDialogMenuItem',
                        isDialogSticker: false,
                        title: 'Loading...',
                    }),
                },
                {
                    type: ActionType.OpenDialog,
                    value: expect.objectContaining({
                        dialog: expect.objectContaining({
                            dialogControl: expect.any(Object),
                            content: expect.objectContaining({
                                columns: [
                                    expect.objectContaining({
                                        defaultUiProperties: expect.anything(),
                                        properties: {
                                            _controlObjectType: 'Text',
                                            bind: 'field1',
                                            isMandatory: true,
                                            title: 'Test text field',
                                        },
                                        type: 'Text',
                                    }),
                                    expect.objectContaining({
                                        defaultUiProperties: expect.anything(),
                                        properties: {
                                            _controlObjectType: 'Numeric',
                                            bind: 'field2',
                                            scale: 2,
                                            title: 'Test numeric field',
                                        },
                                        type: 'Numeric',
                                    }),
                                    expect.objectContaining({
                                        defaultUiProperties: expect.anything(),
                                        properties: {
                                            _controlObjectType: 'Date',
                                            bind: {
                                                nested: {
                                                    binding: true,
                                                },
                                            },
                                            title: 'Test date field',
                                        },
                                        type: 'Date',
                                    }),
                                ],
                                elementId: 'elementId',
                                screenId: 'screenId',
                                sidebarDefinition: {
                                    layout: {
                                        mainSection: {
                                            blocks: {
                                                mainBlock: {
                                                    fields: ['field1, field2'],
                                                    title: 'Test block',
                                                },
                                            },
                                            title: 'Section title',
                                        },
                                    },
                                    onRecordOpened: mockOnRecordOpen,
                                },
                            }),
                        }),
                    }),
                },
                {
                    type: ActionType.UpdateTableSidebarDialogTarget,
                    value: {
                        dialogId: 1,
                        isNewRecord: false,
                        nextRecordId: 'recordId2',
                        prevRecordId: 'recordId0',
                        recordId: 'recordId',
                        title: 'elementId',
                    },
                },
            ];
            expect(mockedGetPrevRecord).toHaveBeenCalled();
            expect(mockedGetNextRecord).toHaveBeenCalled();
            expect(mockOnRecordOpen).toHaveBeenCalledWith('recordId', { _id: 'recordId', value: 'whatever' }, 0);
            expect(mockGetRecordByIdAndLevel).toHaveBeenCalled();
            expect(mockStore.getActions()).toEqual(expected);
        });

        it('should next and prev record to be false because is a new record', async () => {
            const mockState = getMockState(state);
            const mockStore = getMockStore(mockState);
            jest.spyOn(reduxStore, 'getStore').mockReturnValue(mockStore);

            const tableProperties = mockState.screenDefinitions.screenId.metadata.uiComponentProperties
                .elementId as TableDecoratorProperties;
            await openTableSidebar({
                screenId: 'screenId',
                elementId: 'elementId',
                columns: tableProperties.columns || [],
                sidebarDefinition: tableProperties.sidebar,
            })(mockStore.dispatch, mockStore.getState);

            const expected = [
                {
                    type: ActionType.AddMenuItem,
                    value: expect.objectContaining({
                        alignedLeft: true,
                        category: 'dialog',
                        dialogId: 2,
                        icon: 'cross',
                        id: 'CloseDialogMenuItem',
                        isDialogSticker: false,
                        title: 'Loading...',
                    }),
                },
                {
                    type: ActionType.OpenDialog,
                    value: expect.objectContaining({
                        dialog: expect.objectContaining({
                            dialogControl: expect.any(Object),
                            content: expect.objectContaining({
                                columns: [
                                    expect.objectContaining({
                                        defaultUiProperties: expect.anything(),
                                        properties: {
                                            _controlObjectType: 'Text',
                                            bind: 'field1',
                                            isMandatory: true,
                                            title: 'Test text field',
                                        },
                                        type: 'Text',
                                    }),
                                    expect.objectContaining({
                                        defaultUiProperties: expect.anything(),
                                        properties: {
                                            _controlObjectType: 'Numeric',
                                            bind: 'field2',
                                            scale: 2,
                                            title: 'Test numeric field',
                                        },
                                        type: 'Numeric',
                                    }),
                                    expect.objectContaining({
                                        defaultUiProperties: expect.anything(),
                                        properties: {
                                            _controlObjectType: 'Date',
                                            bind: {
                                                nested: {
                                                    binding: true,
                                                },
                                            },
                                            title: 'Test date field',
                                        },
                                        type: 'Date',
                                    }),
                                ],
                                elementId: 'elementId',
                                screenId: 'screenId',
                                sidebarDefinition: {
                                    layout: {
                                        mainSection: {
                                            blocks: {
                                                mainBlock: {
                                                    fields: ['field1, field2'],
                                                    title: 'Test block',
                                                },
                                            },
                                            title: 'Section title',
                                        },
                                    },
                                    onRecordOpened: mockOnRecordOpen,
                                },
                            }),
                        }),
                    }),
                },
                {
                    type: ActionType.UpdateTableSidebarDialogTarget,
                    value: {
                        dialogId: 2,
                        isNewRecord: true,
                        nextRecordId: undefined,
                        prevRecordId: undefined,
                        recordId: '-1',
                        title: 'elementId',
                    },
                },
            ];
            expect(mockCreateNewPhantomRow).toHaveBeenCalled();
            expect(mockedGetPrevRecord).not.toHaveBeenCalled();
            expect(mockedGetNextRecord).not.toHaveBeenCalled();
            expect(mockStore.getActions()).toEqual(expected);
        });
    });

    describe('confirmTableSidebar', () => {
        it('Should commit the record and dispatch a action to close the sidebar', async () => {
            const mockState = getMockState(state);
            const mockStore = getMockStore(mockState);
            jest.spyOn(reduxStore, 'getStore').mockReturnValue(mockStore);

            await confirmTableSidebar(1)(mockStore.dispatch, mockStore.getState);
            const expected = [
                {
                    type: ActionType.RemoveMenuItem,
                    value: 'CloseDialogMenuItem',
                },
                {
                    type: ActionType.CloseDialog,
                    value: 1,
                },
            ];
            expect(mockCommitRecord).toHaveBeenCalled();
            expect(mockStore.getActions()).toEqual(expected);
        });

        it('Should not commit the sidebar if there are validation errors', async () => {
            mockRunValidationOnRecord.mockResolvedValue({
                field1: {
                    screenId: 'Test',
                    elementId: 'field1',
                    message: 'validation error',
                },
            });
            const mockState = getMockState(state);
            const mockStore = getMockStore(mockState);
            jest.spyOn(reduxStore, 'getStore').mockReturnValue(mockStore);

            await confirmTableSidebar(1)(mockStore.dispatch, mockStore.getState);
            expect(mockCommitRecord).not.toHaveBeenCalled();
            expect(mockStore.getActions()).toEqual([]);
        });
    });

    describe('selectSidebarNextRecord', () => {
        it('should dispatch an action to select the next record', async () => {
            const mockState = getMockState(state);
            const mockStore = getMockStore(mockState);
            jest.spyOn(reduxStore, 'getStore').mockReturnValue(mockStore);

            await selectSidebarNextRecord(1)(mockStore.dispatch, mockStore.getState);
            const expected = [
                {
                    type: ActionType.UpdateTableSidebarDialogTarget,
                    value: {
                        dialogId: 1,
                        isNewRecord: false,
                        nextRecordId: undefined,
                        prevRecordId: undefined,
                        recordId: undefined,
                        title: 'Loading...',
                    },
                },
                {
                    type: ActionType.UpdateTableSidebarDialogTarget,
                    value: {
                        dialogId: 1,
                        isNewRecord: false,
                        nextRecordId: 'recordId2',
                        prevRecordId: 'recordId0',
                        recordId: 'recordId2',
                        title: 'elementId',
                    },
                },
            ];
            expect(mockedGetNextRecord).toHaveBeenCalled();
            expect(mockedCancelRecordTransaction).toHaveBeenCalledWith({ recordId: 'recordId' });
            expect(mockedStartRecordTransaction).toHaveBeenCalledWith({ recordId: 'recordId2' });
            expect(mockOnRecordOpen).toHaveBeenCalledWith('recordId2', { _id: 'recordId2', value: 'whatever' }, 0);
            expect(mockStore.getActions()).toEqual(expected);
        });
    });

    describe('selectSidebarPreviousRecord', () => {
        it('should dispatch an action to select the previous record', async () => {
            const mockState = getMockState(state);
            const mockStore = getMockStore(mockState);
            jest.spyOn(reduxStore, 'getStore').mockReturnValue(mockStore);

            await selectSidebarPreviousRecord(1)(mockStore.dispatch, mockStore.getState);
            const expected = [
                {
                    type: ActionType.UpdateTableSidebarDialogTarget,
                    value: {
                        dialogId: 1,
                        isNewRecord: false,
                        nextRecordId: undefined,
                        prevRecordId: undefined,
                        recordId: undefined,
                        title: 'Loading...',
                    },
                },
                {
                    type: ActionType.UpdateTableSidebarDialogTarget,
                    value: {
                        dialogId: 1,
                        isNewRecord: false,
                        nextRecordId: 'recordId2',
                        prevRecordId: 'recordId0',
                        recordId: 'recordId0',
                        title: 'elementId',
                    },
                },
            ];
            expect(mockedGetPrevRecord).toHaveBeenCalled();
            expect(mockedCancelRecordTransaction).toHaveBeenCalledWith({ recordId: 'recordId' });
            expect(mockedStartRecordTransaction).toHaveBeenCalledWith({ recordId: 'recordId0' });
            expect(mockStore.getActions()).toEqual(expected);
        });
    });
});
