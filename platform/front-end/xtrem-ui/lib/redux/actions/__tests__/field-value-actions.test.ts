import type { MockStoreEnhanced } from 'redux-mock-store';
import {
    getMockPageDefinition,
    getMockPageMetadata,
    getMockState,
    getMockStore,
} from '../../../__tests__/test-helpers';
import { ActionType } from '../../action-types';
import * as fieldValueActions from '../field-value-actions';
import type * as xtremRedux from '../../index';
import type { XtremAppState } from '../../state';
import { waitFor } from '@testing-library/dom';

describe('Field value redux actions', () => {
    const screenId = 'TestPage';
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let commitValueAndPropertyChangesMock: jest.MockInstance<any, any>;
    let isTransactionInProgressMock: jest.MockInstance<any, any>;

    beforeEach(() => {
        const mockState = getMockState({});
        commitValueAndPropertyChangesMock = jest.fn().mockResolvedValue(null);
        isTransactionInProgressMock = jest.fn().mockResolvedValue(false);
        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
            page: {
                $: {
                    commitValueAndPropertyChanges: commitValueAndPropertyChangesMock,
                    isTransactionInProgress: isTransactionInProgressMock,
                },
            } as any,
            metadata: getMockPageMetadata(screenId, {
                uiComponentProperties: {
                    [screenId]: {},
                    bind01: {},
                },
            }),
        });
        mockStore = getMockStore(mockState);
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('setFieldValue', () => {
        it('Should dispatch a SetFieldValue action with the bind & fieldValue', async () => {
            fieldValueActions.setFieldValue(
                screenId,
                'bind01',
                'value01',
                true,
            )(mockStore.dispatch, mockStore.getState);
            const expected: xtremRedux.AppAction[] = [
                {
                    type: ActionType.SetFieldValue,
                    value: { screenId, elementId: 'bind01', fieldValue: 'value01', isOrganicChange: true },
                },
            ];

            await waitFor(() => mockStore.getActions().length === 1);
            expect(mockStore.getActions()).toEqual(expected);
        });

        it('Should not commit transactions before dispatching the value changed action if no transaction is in progress', async () => {
            expect(commitValueAndPropertyChangesMock).not.toHaveBeenCalled();
            isTransactionInProgressMock.mockReturnValue(false);
            fieldValueActions.setFieldValue(
                screenId,
                'bind01',
                'value01',
                true,
            )(mockStore.dispatch, mockStore.getState);
            expect(commitValueAndPropertyChangesMock).not.toHaveBeenCalled();
        });

        it('Should commit transactions before dispatching the value changed action if transaction is in progress', async () => {
            expect(commitValueAndPropertyChangesMock).not.toHaveBeenCalled();
            fieldValueActions.setFieldValue(
                screenId,
                'bind01',
                'value01',
                true,
            )(mockStore.dispatch, mockStore.getState);
            isTransactionInProgressMock.mockReturnValue(true);
            const expected: xtremRedux.AppAction[] = [
                {
                    type: ActionType.SetFieldValue,
                    value: { screenId, elementId: 'bind01', fieldValue: 'value01', isOrganicChange: true },
                },
            ];
            expect(commitValueAndPropertyChangesMock).toHaveBeenCalledTimes(1);

            expect(mockStore.getActions()).toEqual([]);
            await waitFor(() => mockStore.getActions().length === 1);
            expect(mockStore.getActions()).toEqual(expected);
        });
    });
});
