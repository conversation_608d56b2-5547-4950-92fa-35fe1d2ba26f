import type { MockStoreEnhanced } from 'redux-mock-store';
import {
    addBlockToState,
    addFieldToState,
    addPageControlObject,
    addSectionToState,
    getMockPageDefinition,
    getMockPageMetadata,
    getMockState,
    getMockStore,
} from '../../../__tests__/test-helpers';
import type {
    BlockControlObject,
    InternalSectionProperties,
    PageControlObject,
    SectionControlObject,
} from '../../../component/control-objects';
import * as events from '../../../utils/events';
import { ActionType } from '../../action-types';
import type * as xtremRedux from '../../index';
import type { ApplicationContext, XtremAppState } from '../../state';
import * as commonActions from '../common-actions';
import * as dashboardActions from '../dashboard-actions';
import { FieldKey } from '@sage/xtrem-shared';
import type { PageDefinition } from '../../../service/page-definition';
import * as graphqlService from '../../../service/graphql-service';

describe('Common redux actions', () => {
    const screenId = 'TestPage';
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let commitValueAndPropertyChangesMock: jest.MockInstance<any, any>;
    let isTransactionInProgressMock: jest.MockInstance<any, any>;

    beforeEach(() => {
        const mockState = getMockState({});
        commitValueAndPropertyChangesMock = jest.fn().mockResolvedValue(null);
        isTransactionInProgressMock = jest.fn().mockResolvedValue(false);
        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
            page: {
                $: {
                    commitValueAndPropertyChanges: commitValueAndPropertyChangesMock,
                    isTransactionInProgress: isTransactionInProgressMock,
                },
            } as any,
            metadata: getMockPageMetadata(screenId, {
                uiComponentProperties: {
                    [screenId]: {},
                    bind01: {},
                },
            }),
        });
        mockStore = getMockStore(mockState);
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('setApplicationContext', () => {
        it('Should dispatch a SetApplicationContext action with the appContext value', () => {
            mockStore.dispatch(commonActions.setApplicationContext({} as ApplicationContext));
            expect(mockStore.getActions()).toEqual([{ type: ActionType.SetApplicationContext, value: {} }]);
        });
    });

    describe('setFieldProperties', () => {
        it('Should return an action SetUiComponentProperties with value object bind & fieldProperties', () => {
            const expected: xtremRedux.AppAction = {
                type: ActionType.SetUiComponentProperties,
                value: { screenId, elementId: 'bind01', fieldProperties: { title: '01' } },
            };

            const action = commonActions.setFieldProperties(screenId, 'bind01', { title: '01' });

            expect(action).toEqual(expected);
        });
    });

    describe('setGlobalLoading', () => {
        it('Should return an action SetGlobalLoading with the same input value', () => {
            const action = commonActions.setGlobalLoading(true);
            const expected: xtremRedux.AppAction = {
                type: ActionType.SetGlobalLoading,
                value: true,
            };
            expect(action).toEqual(expected);
        });
    });

    describe('actionStub', () => {
        it('Should throw an error because this is used to be override by actions', () => {
            try {
                commonActions.actionStub();
            } catch (error) {
                expect(error).toBeDefined();
            }
        });
    });

    describe('setScreenDefinitionReady', () => {
        it('Should return a SetScreenDefinitionReady action type', () => {
            const action = commonActions.setScreenDefinitionReady(screenId);
            const expected: xtremRedux.AppAction = {
                type: ActionType.SetScreenDefinitionReady,
                value: screenId,
            };
            expect(action).toEqual(expected);
        });
    });

    describe('setScreenDefinitionDialogId', () => {
        it('Should return a SetScreenDefinitionDialogId action type', () => {
            const dialogId = 1;
            const pageDefinition = getMockPageDefinition(screenId);
            const pageControlObject = pageDefinition.metadata.controlObjects[screenId] as PageControlObject;
            const action = commonActions.setScreenDefinitionDialogId(
                screenId,
                dialogId,
                pageControlObject,
                'Test',
                'Subtitle',
            );
            const expected: xtremRedux.AppAction = {
                type: ActionType.SetScreenDefinitionDialogId,
                value: { screenId, dialogId, pageControlObject, title: 'Test', subtitle: 'Subtitle' },
            };
            expect(action).toEqual(expected);
        });
    });

    describe('clearWidgetOptions', () => {
        it('should return a ClearWidgetOptionsn action type', () => {
            const action = commonActions.clearWidgetOptions();
            const expected: xtremRedux.AppAction = {
                type: ActionType.ClearWidgetOptions,
                value: null,
            };
            expect(action).toEqual(expected);
        });
    });

    describe('set360ViewState', () => {
        let mockStore: MockStoreEnhanced<XtremAppState>;

        beforeEach(() => {
            mockStore = getMockStore();
        });

        it('should return a Set360ViewState action type', () => {
            commonActions.set360ViewState(screenId, true)(mockStore.dispatch, mockStore.getState);
            expect(mockStore.getActions()).toEqual([
                {
                    type: ActionType.Set360ViewState,
                    value: { screenId, state: true },
                },
            ]);
        });

        it('should trigger the on360ViewSwitched page event', () => {
            expect(events.triggerScreenEvent).not.toHaveBeenCalled();
            commonActions.set360ViewState(screenId, false)(mockStore.dispatch, mockStore.getState);
            expect(events.triggerScreenEvent).toHaveBeenCalledWith(screenId, 'on360ViewSwitched', false);
            expect(mockStore.getActions()).toEqual([
                {
                    type: ActionType.RemoveDashboardGroup,
                    value: `${screenId}-360`,
                },
                {
                    type: ActionType.Set360ViewState,
                    value: { screenId, state: false },
                },
            ]);
        });

        it('should not call removeDashboardGroup when state is true', () => {
            const removeDashboardGroupSpy = jest.spyOn(dashboardActions, 'removeDashboardGroup');
            commonActions.set360ViewState(screenId, true);
            expect(removeDashboardGroupSpy).not.toHaveBeenCalled();
        });
    });

    describe('setActiveSection', () => {
        beforeEach(() => {
            const mockState = getMockState({});

            const pageDefinition = getMockPageDefinition(screenId);
            mockState.screenDefinitions[screenId] = pageDefinition;
            addPageControlObject(mockState, screenId, {
                node: '@sage/xtrem-test/TestNode',
            });

            const field1 = addFieldToState(FieldKey.Text, mockState, screenId, 'field1', {
                parent() {
                    return mockState.screenDefinitions[screenId].metadata.controlObjects
                        .block1 as BlockControlObject<any>;
                },
            });

            const field2 = addFieldToState(FieldKey.Text, mockState, screenId, 'field2', {
                parent() {
                    return mockState.screenDefinitions[screenId].metadata.controlObjects
                        .block2 as BlockControlObject<any>;
                },
            });

            addFieldToState(FieldKey.Text, mockState, screenId, 'field3', {});

            const block1 = addBlockToState(
                mockState,
                screenId,
                'block1',
                {
                    title: 'Block 1',
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects
                            .testSection1 as SectionControlObject<any>;
                    },
                },
                [field1],
            );

            addSectionToState(
                mockState,
                screenId,
                'testSection1',
                {
                    title: 'My Section',
                },
                [block1],
            );

            const block2 = addBlockToState(
                mockState,
                screenId,
                'block2',
                {
                    title: 'Block 2',
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects
                            .testSection2 as SectionControlObject<any>;
                    },
                },
                [field2],
            );

            addSectionToState(
                mockState,
                screenId,
                'testSection2',
                {
                    title: 'My Other Section',
                },
                [block2],
            );
            (mockState.screenDefinitions[screenId] as PageDefinition).selectedRecordId = '12345';

            mockStore = getMockStore(mockState);

            jest.spyOn(graphqlService, 'fetchRecordData').mockResolvedValue({ field2: 'Hi' });
        });

        it('Should not dispatch anything if no section is currently selected and the action selects the first section', async () => {
            await commonActions.setActiveSection(screenId, 'testSection1')(mockStore.dispatch, mockStore.getState);
            expect(mockStore.getActions()).toEqual([]);
            expect(events.triggerFieldEvent).not.toHaveBeenCalled();
        });

        it('Should not dispatch anything if the section is already selected', async () => {
            const mockState = mockStore.getState();
            (mockState.screenDefinitions[screenId] as PageDefinition).activeSection = 'testSection2';
            mockStore = getMockStore(mockState);

            await commonActions.setActiveSection(screenId, 'testSection2')(mockStore.dispatch, mockStore.getState);
            expect(mockStore.getActions()).toEqual([]);
            expect(events.triggerFieldEvent).not.toHaveBeenCalled();
        });

        it('Should dispatch the SetActiveSection action', async () => {
            await commonActions.setActiveSection(screenId, 'testSection2')(mockStore.dispatch, mockStore.getState);
            expect(mockStore.getActions()).toEqual([
                {
                    type: ActionType.SetActiveSection,
                    value: {
                        activeSection: 'testSection2',
                        isLoading: false,
                        screenId,
                    },
                },
                {
                    type: ActionType.SetSectionReady,
                    value: {
                        screenId: 'TestPage',
                        sectionId: 'testSection2',
                    },
                },
            ]);
        });

        it('Should trigger onInactive on currently active section and onActive on the activated section', async () => {
            expect(events.triggerFieldEvent).not.toHaveBeenCalled();
            await commonActions.setActiveSection(screenId, 'testSection2')(mockStore.dispatch, mockStore.getState);
            expect(events.triggerFieldEvent).toHaveBeenCalledWith(screenId, 'testSection1', 'onInactive');
            expect(events.triggerFieldEvent).toHaveBeenCalledWith(screenId, 'testSection2', 'onActive');
        });

        it('Should start loading fields in lazy sections', async () => {
            const mockState = mockStore.getState();
            (
                (mockState.screenDefinitions[screenId] as PageDefinition).metadata.uiComponentProperties
                    .testSection2 as InternalSectionProperties
            ).isLazyLoaded = true;
            mockStore = getMockStore(mockState);

            await commonActions.setActiveSection(screenId, 'testSection2')(mockStore.dispatch, mockStore.getState);
            expect(graphqlService.fetchRecordData).toHaveBeenCalledWith({
                screenId,
                recordId: '12345',
                nodeTypes: {},
                requiredSections: ['testSection2'],
                skipHeaderFields: true,
                screenDefinition: expect.any(Object),
            });
            expect(mockStore.getActions()).toEqual([
                {
                    type: ActionType.SetActiveSection,
                    value: {
                        activeSection: 'testSection2',
                        isLoading: true,
                        screenId,
                    },
                },
                {
                    type: 'SetSectionValues',
                    value: {
                        screenId,
                        sectionId: 'testSection2',
                        values: {
                            field2: 'Hi',
                        },
                    },
                },
                {
                    type: ActionType.SetSectionReady,
                    value: {
                        screenId: 'TestPage',
                        sectionId: 'testSection2',
                    },
                },
            ]);
        });
    });
});
