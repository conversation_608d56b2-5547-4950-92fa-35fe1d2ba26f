import { applyActionMocks, getMockPageDefinition, getMockState, getMockStore } from '../../../__tests__/test-helpers';

import type { BlockProperties, NumericProperties, TextProperties } from '../../../component/control-objects';
import { BlockControlObject, NumericControlObject, TextControlObject } from '../../../component/control-objects';
import type { HasParent } from '../../../component/field/traits';
import { runAndDispatchFieldValidation } from '../../../service/dispatch-service';
import type { PageArticleItem } from '../../../service/layout-types';
import { FieldKey } from '../../../component/types';
import { GraphQLKind, GraphQLTypes } from '../../../types';

describe('Validation redux actions', () => {
    const screenId = 'ScreenId';
    const elementId = 'fieldId';
    let mockState: any;
    beforeEach(() => {
        mockState = getMockState();
        mockState.nodeTypes = {
            MyTestNode: {
                name: 'MyTestNode',
                title: 'MyTestNode',
                properties: {
                    [elementId]: {
                        name: elementId,
                        kind: GraphQLKind.Scalar,
                        type: GraphQLTypes.String,
                    },
                    otherProperty: {
                        name: 'otherProperty',
                        kind: GraphQLKind.Scalar,
                        type: GraphQLTypes.String,
                    },
                },
            },
        };
        getMockStore(mockState, false);
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    describe('validate', () => {
        it('should validate mandatory field', async () => {
            const fieldValue = '';
            const fieldProperties: NumericProperties = { isMandatory: true };
            mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            const controlObject = new TextControlObject({
                componentKey: FieldKey.Text,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            mockState.screenDefinitions[screenId].metadata.controlObjects[elementId] = controlObject;
            mockState.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] = fieldProperties;
            mockState.screenDefinitions[screenId].values[elementId] = fieldValue;

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe('You need to enter a value.');
        });

        it('should not validate mandatory field if it is unauthorized', async () => {
            const fieldValue = '';
            const fieldProperties: NumericProperties = { isMandatory: true };
            const pageDefinition = getMockPageDefinition(screenId);
            const controlObject = new TextControlObject({
                componentKey: FieldKey.Text,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            pageDefinition.metadata.controlObjects[elementId] = controlObject;
            pageDefinition.metadata.uiComponentProperties[elementId] = fieldProperties;
            pageDefinition.metadata.uiComponentProperties[screenId] = {
                node: '@sage/xtrem-test/MyTestNode',
            } as any;
            pageDefinition.accessBindings = {
                MyTestNode: {
                    [elementId]: 'unauthorized',
                },
            };
            pageDefinition.values[elementId] = fieldValue;
            mockState.screenDefinitions[screenId] = pageDefinition;
            getMockStore(mockState, false);

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe(undefined);
        });

        it('should not validate mandatory field if it is unavailable', async () => {
            const fieldValue = '';
            const fieldProperties: NumericProperties = { isMandatory: true };
            const pageDefinition = getMockPageDefinition(screenId);
            const controlObject = new TextControlObject({
                componentKey: FieldKey.Text,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            pageDefinition.metadata.controlObjects[elementId] = controlObject;
            pageDefinition.metadata.uiComponentProperties[elementId] = fieldProperties;
            pageDefinition.metadata.uiComponentProperties[screenId] = {
                node: '@sage/xtrem-test/MyTestNode',
            } as any;
            pageDefinition.accessBindings = {
                MyTestNode: {
                    [elementId]: 'unavailable',
                },
            };
            pageDefinition.values[elementId] = fieldValue;
            mockState.screenDefinitions[screenId] = pageDefinition;
            getMockStore(mockState, false);

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe(undefined);
        });

        it('should not validate mandatory field if its access configuration points to an unavailable property', async () => {
            const fieldValue = '';
            const fieldProperties: NumericProperties = {
                isMandatory: true,
                access: {
                    node: '@sage/xtrem-test/MyTestNode',
                    bind: 'otherProperty',
                },
            };
            const pageDefinition = getMockPageDefinition(screenId);
            const controlObject = new TextControlObject({
                componentKey: FieldKey.Text,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            pageDefinition.metadata.controlObjects[elementId] = controlObject;
            pageDefinition.metadata.uiComponentProperties[elementId] = fieldProperties;
            pageDefinition.metadata.uiComponentProperties[screenId] = {
                node: '@sage/xtrem-test/MyTestNode',
            } as any;
            pageDefinition.accessBindings = {
                MyTestNode: {
                    [elementId]: 'authorized',
                    otherProperty: 'unauthorized',
                },
            };
            pageDefinition.values[elementId] = fieldValue;
            mockState.screenDefinitions[screenId] = pageDefinition;
            getMockStore(mockState, false);

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe(undefined);
        });

        it('should validate mandatory field if it is authorized', async () => {
            const fieldValue = '';
            const fieldProperties: NumericProperties = { isMandatory: true };
            const pageDefinition = getMockPageDefinition(screenId);
            const controlObject = new TextControlObject({
                componentKey: FieldKey.Text,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            pageDefinition.metadata.controlObjects[elementId] = controlObject;
            pageDefinition.metadata.uiComponentProperties[elementId] = fieldProperties;
            pageDefinition.metadata.uiComponentProperties[screenId] = {
                node: '@sage/xtrem-test/MyTestNode',
            } as any;
            pageDefinition.accessBindings = {
                MyTestNode: {
                    [elementId]: 'authorized',
                },
            };
            pageDefinition.values[elementId] = fieldValue;
            mockState.screenDefinitions[screenId] = pageDefinition;
            getMockStore(mockState, false);

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe('You need to enter a value.');
        });

        it('should validate isMandatory if the field is empty', async () => {
            const fieldValue = '';
            const fieldProperties: NumericProperties = { isMandatory: true };
            mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            const controlObject = new TextControlObject({
                componentKey: FieldKey.Text,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            mockState.screenDefinitions[screenId].metadata.controlObjects[elementId] = controlObject;
            mockState.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] = fieldProperties;
            mockState.screenDefinitions[screenId].values[elementId] = fieldValue;

            const errorMessages = await controlObject.validate();
            expect(errorMessages).toBe('You need to enter a value.');
        });

        it('should validate non zero field with integer', async () => {
            const fieldValue = '0';
            const fieldProperties: NumericProperties = { isNotZero: true };
            mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            const controlObject = new TextControlObject({
                componentKey: FieldKey.Text,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            mockState.screenDefinitions[screenId].metadata.controlObjects[elementId] = controlObject;
            mockState.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] = fieldProperties;
            mockState.screenDefinitions[screenId].values[elementId] = fieldValue;

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe('fieldId cannot be zero');
        });

        it('should validate non zero field with decimal', async () => {
            const fieldValue = '0.00';
            const fieldProperties: NumericProperties = { isNotZero: true };
            mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            const controlObject = new TextControlObject({
                componentKey: FieldKey.Text,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            mockState.screenDefinitions[screenId].metadata.controlObjects[elementId] = controlObject;
            mockState.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] = fieldProperties;
            mockState.screenDefinitions[screenId].values[elementId] = fieldValue;

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe('fieldId cannot be zero');
        });

        it('should validate pass fields with a non zero value if the isNotZero validation is applied', async () => {
            const fieldValue = '0.01';
            const fieldProperties: NumericProperties = { isNotZero: true };
            mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            const controlObject = new TextControlObject({
                componentKey: FieldKey.Text,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            mockState.screenDefinitions[screenId].metadata.controlObjects[elementId] = controlObject;
            mockState.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] = fieldProperties;
            mockState.screenDefinitions[screenId].values[elementId] = fieldValue;

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe(undefined);
        });

        it('should exclude hidden fields from validation', async () => {
            const fieldValue = '';
            const fieldProperties: TextProperties = { isMandatory: true, isHidden: true };
            mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            const controlObject = new TextControlObject({
                componentKey: FieldKey.Text,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            mockState.screenDefinitions[screenId].metadata.controlObjects[elementId] = controlObject;
            mockState.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] = fieldProperties;
            mockState.screenDefinitions[screenId].values[elementId] = fieldValue;

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe(undefined);
        });

        it('should exclude disabled fields from validation', async () => {
            const fieldValue = '';
            const fieldProperties: TextProperties = { isMandatory: true, isDisabled: true };
            mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            const controlObject = new TextControlObject({
                componentKey: FieldKey.Text,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            mockState.screenDefinitions[screenId].metadata.controlObjects[elementId] = controlObject;
            mockState.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] = fieldProperties;
            mockState.screenDefinitions[screenId].values[elementId] = fieldValue;

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe(undefined);
        });

        it('should exclude fields with hidden parents from validation', async () => {
            const fieldValue = '';
            const blockProperties: BlockProperties = { isHidden: true };

            mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);

            const blockId = 'myBlockId';
            const blockControlObject = new BlockControlObject({
                elementId: blockId,
                dispatchBlockValidation: jest.fn(),
                getUiComponentProperties: jest.fn().mockReturnValue(blockProperties),
                layout: {} as PageArticleItem,
                screenId,
            });

            const fieldProperties: TextProperties & HasParent<any, any> = {
                isMandatory: true,
                parent() {
                    return blockControlObject;
                },
            };

            const controlObject = new TextControlObject({
                componentKey: FieldKey.Text,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });

            mockState.screenDefinitions[screenId].metadata.controlObjects[elementId] = controlObject;
            mockState.screenDefinitions[screenId].metadata.controlObjects[blockId] = blockControlObject;
            mockState.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] = fieldProperties;
            mockState.screenDefinitions[screenId].metadata.uiComponentProperties[blockId] = blockProperties;
            mockState.screenDefinitions[screenId].values[elementId] = fieldValue;

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe(undefined);
        });

        it('should return an error message for mandatory numeric field without value', async () => {
            const fieldValue = undefined;
            const fieldProperties: NumericProperties = { isMandatory: true };
            mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            const controlObject = new NumericControlObject({
                componentKey: FieldKey.Numeric,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            mockState.screenDefinitions[screenId].metadata.controlObjects[elementId] = controlObject;
            mockState.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] = fieldProperties;
            mockState.screenDefinitions[screenId].values[elementId] = fieldValue;

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe('You need to enter a value.');
        });

        it('should return NO error message for mandatory numeric field with value 0', async () => {
            const fieldValue = 0;
            const fieldProperties: NumericProperties = { isMandatory: true };
            mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            const controlObject = new NumericControlObject({
                componentKey: FieldKey.Numeric,
                elementId,
                dispatchValidation: runAndDispatchFieldValidation,
                getValue: jest.fn().mockReturnValue(fieldValue),
                getUiComponentProperties: jest.fn().mockReturnValue(fieldProperties),
                layout: {} as PageArticleItem,
                screenId,
                setUiComponentProperties: jest.fn(),
                setValue: jest.fn(),
                refresh: jest.fn(),
                focus: jest.fn(),
                isFieldDirty: jest.fn(),
                setFieldDirty: jest.fn(),
                setFieldClean: jest.fn(),
                isFieldInFocus: jest.fn(),
            });
            mockState.screenDefinitions[screenId].metadata.controlObjects[elementId] = controlObject;
            mockState.screenDefinitions[screenId].metadata.uiComponentProperties[elementId] = fieldProperties;
            mockState.screenDefinitions[screenId].values[elementId] = fieldValue;

            const errorMessage = await controlObject.validate();
            expect(errorMessage).toBe(undefined);
        });
    });
});
