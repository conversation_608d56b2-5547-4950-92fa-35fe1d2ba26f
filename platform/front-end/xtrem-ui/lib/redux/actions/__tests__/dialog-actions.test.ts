import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    getMockStickerDefinition,
    getMockPageMetadata,
} from '../../../__tests__/test-helpers';

import type { MockStoreEnhanced } from 'redux-mock-store';
import { StickerControlObject } from '../../../component/control-objects';
import { FieldKey } from '../../../component/types';
import type { PageDefinition } from '../../../service/page-definition';
import * as screenLoaderService from '../../../service/screen-loader-service';
import * as transactionService from '../../../service/transactions-service';
import type { DialogDescription } from '../../../types/dialogs';
import * as events from '../../../utils/events';
import { ActionType } from '../../action-types';
import type { StickerDecoratorProperties } from '../../../component/decorators';
import type { PageArticleItem } from '../../../service/layout-types';
import type { Sticker } from '../../../service/sticker';
import * as menuItems from '../menu-items';
import type * as xtremRedux from '../..';
import * as dialogActions from '../dialog-actions';

export const getStickerDefinition = (stickerId: string, isActive = true) => {
    return getMockStickerDefinition(stickerId, {
        metadata: getMockPageMetadata(stickerId, {
            uiComponentProperties: {
                [stickerId]: {
                    icon: 'home',
                    isActive: () => isActive,
                } as StickerDecoratorProperties<Sticker>,
            },
            controlObjects: {
                [stickerId]: new StickerControlObject({
                    dispatchPageValidation: jest.fn(),
                    getUiComponentProperties: jest.fn().mockReturnValue({}),
                    layout: {} as Partial<PageArticleItem>,
                    screenId: stickerId,
                    setUiComponentProperties: jest.fn(),
                    updateMenuItem: jest.fn(),
                }),
            },
        }),
    });
};

describe('Dialog redux actions', () => {
    let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;
    let mockState: xtremRedux.XtremAppState;
    const dialog = {} as DialogDescription;
    const dialogId = 0;
    let setUiComponentPropertiesMock: jest.MockInstance<void, any> | null = null;

    beforeEach(() => {
        setUiComponentPropertiesMock = jest
            .spyOn(transactionService, 'setUiComponentProperties')
            .mockImplementation(jest.fn());
        mockState = getMockState({
            screenDefinitions: {
                StickerOne: getStickerDefinition('StickerOne'),
                NonSticker: getStickerDefinition('NonSticker'),
            },
            menuItems: [
                {
                    id: menuItems.navigationPanelMenuItemId,
                    onClick: () => {},
                    title: 'Test icon',
                    category: 'dialog',
                },
            ],
            activeDialogs: {
                '2': {
                    isSticker: false,
                    screenId: 'NonSticker',
                    dialogId: 2,
                    level: 'info',
                    options: {},
                    title: 'Non Sticker Dialog',
                    buttons: {},
                    content: undefined,
                    type: 'page',
                    dialogControl: undefined,
                } as any,
                '3': {
                    isSticker: true,
                    screenId: 'StickerOne',
                    dialogId: 3,
                    level: 'info',
                    options: {},
                    title: 'A sticker',
                    buttons: {},
                    content: undefined,
                    dialogControl: undefined,
                } as any,
            },
        });
        addFieldToState(
            FieldKey.Reference,
            mockState,
            'StickerOne',
            'aReferenceField',
            {
                isReferenceDialogOpen: true,
            } as any,
            'Option1',
        );
        mockStore = getMockStore(mockState, true);
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('closeDialog', () => {
        it('Should dispatch RemoveMenuItem and CloseDialog actions', async () => {
            await dialogActions.closeDialog(2)(mockStore.dispatch, mockStore.getState);
            const expected = [
                {
                    type: ActionType.RemoveMenuItem,
                    value: menuItems.closeDialogMenuItemId,
                },
                {
                    type: ActionType.CloseDialog,
                    value: 2,
                },
                {
                    type: ActionType.RemoveScreenDefinition,
                    value: 'NonSticker',
                },
            ];
            expect(mockStore.getActions()).toEqual(expected);
        });

        it('should trigger onClose event of the page dialog', async () => {
            expect(events.triggerScreenEvent).not.toHaveBeenCalled();
            await dialogActions.closeDialog(2)(mockStore.dispatch, mockStore.getState);
            expect(events.triggerScreenEvent).toHaveBeenCalledWith('NonSticker', 'onClose');
        });

        it('Should dispatch RemoveScreenDefinition when closing non sticker dialog', async () => {
            const screenId = 'NonSticker';
            const dialogId = 2;
            await dialogActions.closeDialog(dialogId)(mockStore.dispatch, mockStore.getState);
            const expected = [
                {
                    type: ActionType.RemoveMenuItem,
                    value: menuItems.closeDialogMenuItemId,
                },
                {
                    type: ActionType.CloseDialog,
                    value: dialogId,
                },
                {
                    type: ActionType.RemoveScreenDefinition,
                    value: screenId,
                },
            ];
            expect(mockStore.getActions()).toEqual(expected);
        });

        it('should close the reference field lookup dialog when closing a sticker', async () => {
            expect(setUiComponentPropertiesMock).not.toHaveBeenCalled();
            await dialogActions.closeDialog(3)(mockStore.dispatch, mockStore.getState);
            expect(setUiComponentPropertiesMock).toHaveBeenCalledTimes(1);
            expect(setUiComponentPropertiesMock).toHaveBeenCalledWith('StickerOne', 'aReferenceField', {
                isReferenceDialogOpen: false,
                _controlObjectType: 'Reference',
            });
        });
    });

    describe('openDialog', () => {
        it('Should dispatch AddMenuItem and OpenDialog actions', async () => {
            const menuItem: xtremRedux.Menu = { id: 'id', title: 'title', onClick: () => {}, category: 'dialog' };
            jest.spyOn(menuItems, 'getCloseDialogMenuItem').mockReturnValue(menuItem);
            dialogActions.openDialog(dialogId, dialog)(mockStore.dispatch, mockStore.getState);
            const expected = [
                {
                    type: ActionType.AddMenuItem,
                    value: menuItem,
                },
                {
                    type: ActionType.OpenDialog,
                    value: {
                        dialogId,
                        dialog,
                    },
                },
            ];
            expect(mockStore.getActions()).toEqual(expected);
        });
    });

    describe('loadPageDialogContent', () => {
        it('Should dispatch addScreenDefinition and setScreenDefinitionReady actions', async () => {
            const $standardDeleteActionMock = {};
            Object.defineProperty($standardDeleteActionMock, 'isDisabled', {
                get: jest.fn(),
                set: jest.fn(),
            });
            const $standardDuplicateActionMock = {};
            Object.defineProperty($standardDuplicateActionMock, 'isDisabled', {
                get: jest.fn(),
                set: jest.fn(),
            });

            const screenId = 'setScreenDefinitionReadyID';
            const onLoadMock = jest.fn();
            const pageDefinition: PageDefinition = getMockPageDefinition(
                screenId,
                {
                    page: {
                        $standardDeleteAction: $standardDeleteActionMock as any,
                        $standardDuplicateAction: $standardDuplicateActionMock as any,
                        onLoad: onLoadMock,
                    } as any,
                },
                {
                    uiComponentProperties: {
                        [screenId]: {},
                    },
                },
            );
            jest.spyOn(screenLoaderService, 'fetchPageDefinition').mockReturnValue(Promise.resolve(pageDefinition));
            jest.spyOn(events, 'triggerScreenEvent');
            await dialogActions.loadPageDialogContent('setScreenDefinitionReadyPath', {})(
                mockStore.dispatch,
                mockStore.getState,
            );
            const expected = [
                {
                    type: ActionType.AddScreenDefinition,
                    value: pageDefinition,
                },
                {
                    type: ActionType.SetScreenDefinitionReady,
                    value: screenId,
                },
            ];
            expect(mockStore.getActions()).toEqual(expected);
            expect(events.triggerScreenEvent).toHaveBeenCalledWith(screenId, 'onLoad');
        });

        it('Should not dispatch any actions', async () => {
            const pageDefinition = null;
            jest.spyOn(screenLoaderService, 'fetchPageDefinition').mockReturnValue(Promise.resolve(pageDefinition));
            await dialogActions.loadPageDialogContent('setScreenDefinitionReadyPath', {})(
                mockStore.dispatch,
                mockStore.getState,
            );
            expect(mockStore.getActions()).toEqual([]);
        });
    });
});
