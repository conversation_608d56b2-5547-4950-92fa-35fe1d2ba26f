import {
    addFieldToState,
    addPageControlObject,
    getMockPageDefinition,
    getMockPageMetadata,
    getMockState,
    getMockStore,
} from '../../../__tests__/test-helpers';

import type { MockStoreEnhanced } from 'redux-mock-store';
import type { AppAction, XtremAppState } from '../..';
import type { PageProperties } from '../../../component/container/container-properties';
import { PageControlObject, TextControlObject } from '../../../component/control-objects';
import type { PageDecoratorProperties } from '../../../component/decorators';
import { FieldKey } from '../../../component/types';
import { DialogControl } from '../../../service/dialog-service';
import * as graphqlService from '../../../service/graphql-service';
import { purgeMetaDataCache } from '../../../service/metadata-service';
import type { Page } from '../../../service/page';
import type { PageDefinition } from '../../../service/page-definition';
import * as screenLoaderService from '../../../service/screen-loader-service';
import { NEW_PAGE } from '../../../utils/constants';
import * as events from '../../../utils/events';
import * as windowUtil from '../../../utils/window';
import { ActionType } from '../../action-types';
import * as dialogActions from '../dialog-actions';
import * as navigationPanelActions from '../navigation-panel-actions';
import { goBack, navigate, selectRecord, setIdToQueryParameters, update360ViewInPath } from '../router-actions';

jest.mock('carbon-react/esm/components/textarea');
jest.mock('carbon-react/esm/components/date');
jest.mock('carbon-react/esm/components/date-range');
jest.mock('carbon-react/esm/components/checkbox');
jest.mock('carbon-react/esm/components/radio-button');
jest.mock('carbon-react/esm/components/action-popover');
jest.mock('carbon-react/esm/components/button-toggle/button-toggle-group');
jest.mock('carbon-react/esm/components/button-toggle/button-toggle.component');
jest.mock('carbon-react/esm/components/dialog');
jest.mock('carbon-react/esm/components/dialog-full-screen');
jest.mock('carbon-react/esm/components/portrait');
jest.mock('carbon-react/esm/components/select');
jest.mock('carbon-react/esm/components/sidebar');
jest.mock('carbon-react/esm/components/step-sequence');
jest.mock('carbon-react/esm/components/toast');
jest.mock('../../../service/dialog-service.ts');

describe('Router Redux actions', () => {
    const pageId = 'TestPage';
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let setDeleteCrudActionDisabledMock: jest.Mock;
    let setDuplicateCrudActionDisabledMock: jest.Mock;

    describe('navigate', () => {
        let state: XtremAppState;
        let pageDefinition: PageDefinition;

        beforeEach(async () => {
            setDeleteCrudActionDisabledMock = jest.fn();
            setDuplicateCrudActionDisabledMock = jest.fn();
            await purgeMetaDataCache();
            state = getMockState();
            state.browser = {
                greaterThan: {
                    l: false,
                    m: false,
                    s: false,
                    xs: false,
                },
                is: {
                    l: false,
                    m: false,
                    s: false,
                    xs: false,
                },
                lessThan: {
                    l: false,
                    m: false,
                    s: false,
                    xs: false,
                },
                mediaType: 'desktop',
                orientation: 'landscape',
            };
            state.nodeTypes = {};
            const $standardDeleteActionMock = {};
            Object.defineProperty($standardDeleteActionMock, 'isDisabled', {
                get: jest.fn(),
                set: setDeleteCrudActionDisabledMock,
            });
            const $standardDuplicateActionMock = {};
            Object.defineProperty($standardDuplicateActionMock, 'isDisabled', {
                get: jest.fn(),
                set: setDuplicateCrudActionDisabledMock,
            });

            pageDefinition = getMockPageDefinition(pageId, {
                page: {
                    $standardDeleteAction: $standardDeleteActionMock,
                    $standardDuplicateAction: $standardDuplicateActionMock,
                } as any,
                metadata: getMockPageMetadata(pageId, {
                    controlObjects: {
                        [pageId]: new PageControlObject({
                            screenId: pageId,
                            getUiComponentProperties: jest.fn().mockReturnValue({}),
                            setUiComponentProperties: jest.fn(),
                            dispatchPageValidation: jest.fn(),
                            layout: null,
                            getFocussedField: jest.fn(),
                        }),
                    },
                    uiComponentProperties: {
                        option1: {
                            title: '1',
                        },
                        option2: {
                            title: '2',
                        },
                        [pageId]: {
                            title: 'Test page',
                            objectTypePlural: 'Products',
                            objectTypeSingular: 'Product',
                            onLoad: jest.fn(),
                        } as Partial<PageDecoratorProperties<Page>>,
                    },
                }),
            });

            mockStore = getMockStore(state);
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        const mockLocalStorage = () => {
            jest.spyOn(windowUtil, 'getLocalStorage').mockImplementation(() => {
                return {
                    getItem() {
                        return JSON.stringify({
                            id1: {
                                option1: {
                                    bar: 'foo',
                                },
                                option2: {
                                    name: 'fuu',
                                },
                            },
                        });
                    },
                } as unknown as Storage;
            });
        };

        it('Should call onLoad callback when defined and when navigating to a new page', async () => {
            jest.spyOn(screenLoaderService, 'fetchPageDefinition').mockReturnValue(Promise.resolve(pageDefinition));
            mockLocalStorage();

            await navigate('@sage/module/page/itemId')(mockStore.dispatch, mockStore.getState);

            expect(events.triggerScreenEvent).toHaveBeenCalledTimes(1);
            expect(events.triggerScreenEvent).toHaveBeenCalledWith(pageId, 'onLoad');
        });

        it('Should call onLoad extension callbacks when defined and when navigating to a new page', async () => {
            jest.spyOn(screenLoaderService, 'fetchPageDefinition').mockReturnValue(Promise.resolve(pageDefinition));
            mockLocalStorage();

            await navigate('@sage/module/page/itemId')(mockStore.dispatch, mockStore.getState);

            expect(events.triggerScreenEvent).toHaveBeenCalledTimes(1);
        });

        it('Should dispatch corresponding actions when navigating to a new page', async () => {
            jest.spyOn(screenLoaderService, 'fetchPageDefinition').mockReturnValue(Promise.resolve(pageDefinition));
            mockLocalStorage();
            await navigate('@sage/module/page/itemId')(mockStore.dispatch, mockStore.getState);

            const expectedActions = [
                {
                    type: ActionType.SetGlobalLoading,
                    value: true,
                },
                {
                    type: ActionType.PushToHistory,
                    value: { path: '/test/path', queryParams: {} },
                },
                {
                    type: ActionType.SetPath,
                    value: '@sage/module/page/eyJfaWQiOiJpdGVtSWQifQ==',
                },
                {
                    type: ActionType.AddScreenDefinition,
                    value: pageDefinition,
                },
                {
                    type: ActionType.SetNavigationPanelIsOpened,
                    value: {
                        isOpened: false,
                        screenId: 'TestPage',
                    },
                },
                { type: ActionType.SetScreenDefinitionReady, value: 'TestPage' },
                { type: ActionType.SetGlobalLoading, value: false },
            ];

            expect(mockStore.getActions()).toEqual(expectedActions);
        });

        it('Should dispatch corresponding actions when navigating to a new page with Navigation panel', async () => {
            (pageDefinition.metadata.uiComponentProperties[pageId] as PageDecoratorProperties<Page>).navigationPanel = {
                listItem: {
                    title: {
                        defaultUiProperties: { ...TextControlObject.defaultUiProperties, bind: 'text' },
                        properties: { bind: 'text' },
                        type: FieldKey.Text,
                    },
                },
            };
            jest.spyOn(screenLoaderService, 'fetchPageDefinition').mockReturnValue(Promise.resolve(pageDefinition));
            mockLocalStorage();
            await navigate('@sage/module/page/itemId')(mockStore.dispatch, mockStore.getState);

            const expectedActions = [
                {
                    type: ActionType.SetGlobalLoading,
                    value: true,
                },
                {
                    type: ActionType.PushToHistory,
                    value: { path: '/test/path', queryParams: {} },
                },
                {
                    type: ActionType.SetPath,
                    value: '@sage/module/page/eyJfaWQiOiJpdGVtSWQifQ==',
                },
                {
                    type: ActionType.AddScreenDefinition,
                    value: pageDefinition,
                },
                {
                    type: ActionType.SetNavigationPanelIsOpened,
                    value: {
                        isOpened: true,
                        screenId: 'TestPage',
                    },
                },
                { type: ActionType.SetScreenDefinitionReady, value: 'TestPage' },
                { type: ActionType.SetGlobalLoading, value: false },
            ];

            expect(mockStore.getActions()).toEqual(expectedActions);
        });

        it('Should dispatch corresponding actions when navigating to a new page but pageDefinition is null', async () => {
            jest.spyOn(screenLoaderService, 'fetchPageDefinition').mockReturnValue(Promise.resolve(null));
            await navigate('@sage/module/page/itemId')(mockStore.dispatch, mockStore.getState);

            const expectedActions = [
                {
                    type: ActionType.SetGlobalLoading,
                    value: true,
                },
                {
                    type: ActionType.PushToHistory,
                    value: { path: '/test/path', queryParams: {} },
                },
                {
                    type: ActionType.SetPath,
                    value: '@sage/module/page/eyJfaWQiOiJpdGVtSWQifQ==',
                },
                { type: ActionType.SetGlobalLoading, value: false },
            ];

            expect(mockStore.getActions()).toEqual(expectedActions);
        });

        it('Should dispatch close dialog actions when navigating to a new page but pageDefinition is null', async () => {
            state.activeDialogs = {
                1: {
                    isSticker: false,
                    dialogId: 1,
                    buttons: {
                        accept: {
                            text: 'Ok',
                            onClick: jest.fn(),
                        },
                        cancel: {
                            text: 'Cancel',
                            onClick: jest.fn(),
                        },
                    },
                    content: 'Dialog text 1',
                    level: 'info',
                    title: 'Dialog title 1',
                    screenId: pageId,
                    dialogControl: new DialogControl({
                        dialogId: 1,
                        screenId: 'screenId',
                        level: 'info',
                        title: 'Title 1',
                        content: 'content 1',
                        getButtons: jest.fn(),
                        isSticker: false,
                        isDirtyCheck: false,
                    }),
                    options: {},
                },
                2: {
                    isSticker: false,
                    dialogId: 2,
                    buttons: {
                        accept: {
                            text: 'Ok',
                            onClick: jest.fn(),
                        },
                        cancel: {
                            text: 'Cancel',
                            onClick: jest.fn(),
                        },
                    },
                    content: 'Dialog text 2',
                    level: 'info',
                    title: 'Dialog title 2',
                    screenId: pageId,
                    dialogControl: new DialogControl({
                        dialogId: 2,
                        screenId: 'screenId',
                        level: 'info',
                        title: 'Title 2',
                        content: 'content 2',
                        getButtons: jest.fn(),
                        isDirtyCheck: false,
                    }),
                    options: {},
                },
            };

            const closeDialogMock =
                (dialogId: number) =>
                async (dispatch = mockStore.dispatch): Promise<void> => {
                    dispatch({ type: ActionType.CloseDialog, value: dialogId });
                    return Promise.resolve();
                };

            jest.spyOn(dialogActions, 'closeDialog').mockImplementation(closeDialogMock);
            jest.spyOn(screenLoaderService, 'fetchPageDefinition').mockReturnValue(Promise.resolve(null));
            await navigate('@sage/module/page/itemId')(mockStore.dispatch, mockStore.getState);

            const expectedActions = [
                {
                    type: ActionType.SetGlobalLoading,
                    value: true,
                },
                {
                    type: ActionType.PushToHistory,
                    value: { path: '/test/path', queryParams: {} },
                },
                {
                    type: ActionType.CloseDialog,
                    value: 1,
                },
                {
                    type: ActionType.CloseDialog,
                    value: 2,
                },
                {
                    type: ActionType.SetPath,
                    value: '@sage/module/page/eyJfaWQiOiJpdGVtSWQifQ==',
                },
                { type: ActionType.SetGlobalLoading, value: false },
            ];

            expect(mockStore.getActions()).toEqual(expectedActions);
        });

        it('Should dispatch corresponding actions when navigating to an empty page', async () => {
            jest.spyOn(screenLoaderService, 'fetchPageDefinition').mockReturnValue(Promise.resolve(pageDefinition));
            jest.spyOn(windowUtil, 'getLocalStorage').mockImplementation(() => {
                return {
                    getItem() {
                        return JSON.stringify({
                            id1: {
                                option1: {
                                    bar: 'foo',
                                },
                                option2: {
                                    name: 'fuu',
                                },
                            },
                        });
                    },
                } as unknown as Storage;
            });

            const extensionOnClose1 = jest.fn();
            const extensionOnClose2 = jest.fn();

            state = {
                ...state,
                screenDefinitions: {
                    myLoadedPage: {
                        accessBindings: {},
                        values: {},
                        errors: {},
                        internalErrors: {},
                        userSettings: {},
                        type: 'page',
                        dirtyStates: {},
                        metadata: {
                            uiComponentProperties: {
                                myLoadedPage: { onClose: jest.fn() },
                            } as any,
                            extensionsOnClose: [extensionOnClose1, extensionOnClose2],
                        } as any,
                    },
                    myLoadedSticker: {
                        accessBindings: {},
                        values: {},
                        errors: {},
                        internalErrors: {},
                        userSettings: {},
                        type: 'sticker',
                        dirtyStates: {},
                        metadata: {} as any,
                    },
                },
            };
            mockStore = getMockStore(state);

            mockStore.clearActions();
            expect(events.triggerScreenEvent).not.toHaveBeenCalled();
            await navigate('')(mockStore.dispatch, mockStore.getState);
            expect(events.triggerScreenEvent).toHaveBeenCalledTimes(1);
            expect(events.triggerScreenEvent).toHaveBeenNthCalledWith(1, 'myLoadedPage', 'onClose');
            expect(mockStore.getActions()).toEqual([
                {
                    type: ActionType.SetGlobalLoading,
                    value: true,
                },
                {
                    type: ActionType.PushToHistory,
                    value: { path: '/test/path', queryParams: {} },
                },
                {
                    type: ActionType.RemoveScreenDefinition,
                    value: 'myLoadedPage',
                },
                {
                    type: ActionType.SetPath,
                    value: '',
                },
                {
                    type: ActionType.SetGlobalLoading,
                    value: false,
                },
            ]);
        });

        it('should set the page title when navigating to a new page', async () => {
            jest.spyOn(screenLoaderService, 'fetchPageDefinition').mockReturnValue(Promise.resolve(pageDefinition));
            jest.spyOn(windowUtil, 'getLocalStorage').mockImplementation(() => {
                return {
                    getItem() {
                        return JSON.stringify({
                            id1: {
                                option1: {
                                    bar: 'foo',
                                },
                                option2: {
                                    name: 'fuu',
                                },
                            },
                        });
                    },
                } as unknown as Storage;
            });

            expect(state.applicationContext?.onPageTitleChange).not.toHaveBeenCalled();
            await navigate(NEW_PAGE)(mockStore.dispatch, mockStore.getState);
            expect(state.applicationContext?.onPageTitleChange).toHaveBeenCalledWith('Test page', null);
        });

        describe('updateBackArrowNavigationHistory', () => {
            it('should clear history if navigating from menu', async () => {
                const queryParameters = btoa(JSON.stringify({ _id: '1234' }));
                state = {
                    ...state,
                    navigation: {
                        history: [
                            {
                                path: `@sage/module1/page1/${queryParameters}`,
                                queryParams: {},
                            },
                        ],
                        isBackNavigation: false,
                    },
                    path: `@sage/module2/page1/${queryParameters}`,
                };
                mockStore = getMockStore(state);

                const targetUrl = `@sage/module3/page1/${queryParameters}`;

                await navigate(targetUrl, {}, true)(mockStore.dispatch, mockStore.getState);

                const actions = mockStore.getActions();
                expect(actions).toContainEqual({ type: ActionType.ClearHistory });
                expect(actions).not.toContainEqual(expect.objectContaining({ type: ActionType.PushToHistory }));
                expect(actions).not.toContainEqual(expect.objectContaining({ type: ActionType.PopFromHistory }));
            });

            it('should push previous path to history if the page changes', async () => {
                const queryParameters = btoa(JSON.stringify({ _id: '1234' }));
                state = {
                    ...state,
                    navigation: {
                        history: [{ path: `@sage/module1/page1/${queryParameters}`, queryParams: {} }],
                        isBackNavigation: false,
                    },
                    path: `@sage/module2/page1/${queryParameters}`,
                };
                mockStore = getMockStore(state);

                await navigate(`@sage/module2/page2/${queryParameters}`, {})(mockStore.dispatch, mockStore.getState);
                const actions = mockStore.getActions();
                expect(actions).toContainEqual({
                    type: ActionType.PushToHistory,
                    value: { path: `@sage/module2/page1/${queryParameters}`, queryParams: {} },
                });
            });

            it('should not push to history navigating in page context', async () => {
                const queryParameters = btoa(JSON.stringify({ _id: '1234' }));
                state = {
                    ...state,
                    navigation: {
                        history: [{ path: `@sage/module1/page1/${queryParameters}`, queryParams: {} }],
                        isBackNavigation: false,
                    },
                    path: `@sage/module2/page1/${queryParameters}`,
                };
                mockStore = getMockStore(state);

                const newQueryParameters = btoa(JSON.stringify({ _id: 'asdf' }));
                await navigate(`@sage/module2/page1/${newQueryParameters}`, {})(mockStore.dispatch, mockStore.getState);
                const actions = mockStore.getActions();
                expect(actions).not.toContainEqual(expect.objectContaining({ type: ActionType.PushToHistory }));
            });

            it('should detect back navigation when isBackNavigation flag is true', async () => {
                const queryParameters = btoa(JSON.stringify({ _id: '1234' }));
                state = {
                    ...state,
                    navigation: {
                        history: [
                            { path: `@sage/module2/page1/${queryParameters}`, queryParams: {} },
                            { path: `@sage/module1/page1/${queryParameters}`, queryParams: {} },
                        ],
                        isBackNavigation: true,
                    },
                    path: `@sage/module3/page1/${queryParameters}`,
                };
                mockStore = getMockStore(state);

                navigate(`@sage/module1/page1/${queryParameters}`, {}, false)(mockStore.dispatch, mockStore.getState);

                const actions = mockStore.getActions();
                expect(actions).not.toContainEqual(expect.objectContaining({ type: ActionType.PushToHistory }));
                expect(actions).toContainEqual(expect.objectContaining({ type: ActionType.HandleBackNavigation }));
            });

            it('should add "/" to history when navigating from an empty path', async () => {
                const queryParameters = btoa(JSON.stringify({ _id: '1234' }));
                state = { ...state, navigation: { history: [], isBackNavigation: false }, path: '' };
                mockStore = getMockStore(state);

                navigate(`@sage/module1/page1/${queryParameters}`, {})(mockStore.dispatch, mockStore.getState);

                const actions = mockStore.getActions();
                expect(actions).toContainEqual({
                    type: ActionType.PushToHistory,
                    value: { path: '/', queryParams: {} },
                });
            });
        });
    });

    describe('goBack action', () => {
        let mockStore: any;
        let state: any;
        const goToMock = jest.fn();

        beforeEach(() => {
            state = getMockState();
            state.applicationContext = { handleNavigation: jest.fn() };
            state.path = '@sage/module1/page1/item1';
            jest.spyOn(require('../../../service/router'), 'getRouter').mockReturnValue({ goTo: goToMock });
            mockStore = getMockStore(state);
        });

        afterEach(() => {
            jest.resetAllMocks();
        });

        it('should do nothing if history is empty', async () => {
            await goBack()(mockStore.dispatch, mockStore.getState);
            expect(goToMock).not.toHaveBeenCalled();
        });

        it('should navigate to the last entry in history', async () => {
            state = {
                ...state,
                navigation: {
                    history: [
                        { path: '@sage/module/page1/item1', queryParams: {} },
                        { path: '@sage/module/page2/item1', queryParams: {} },
                    ],
                    isBackNavigation: false,
                },
            };
            mockStore = getMockStore(state);

            await goBack()(mockStore.dispatch, mockStore.getState);

            expect(state.applicationContext.handleNavigation).toHaveBeenCalledWith(
                '@sage/module/page2/item1',
                {},
                true,
            );
        });

        it('should navigate to home if the last history entry is "/"', async () => {
            state = { ...state, navigation: { history: [{ path: '/', queryParams: {} }] } };
            mockStore = getMockStore(state);

            await goBack()(mockStore.dispatch, mockStore.getState);

            expect(state.applicationContext.handleNavigation).toHaveBeenCalledWith('', {}, true);
        });
    });

    describe('selectRecord', () => {
        const onLoad = () => Promise.resolve();

        let state: XtremAppState;

        beforeEach(() => {
            const $standardDeleteActionMock = {};
            Object.defineProperty($standardDeleteActionMock, 'isDisabled', {
                get: jest.fn(),
                set: setDeleteCrudActionDisabledMock,
            });
            const $standardDuplicateActionMock = {};
            Object.defineProperty($standardDuplicateActionMock, 'isDisabled', {
                get: jest.fn(),
                set: setDuplicateCrudActionDisabledMock,
            });
            state = getMockState();
            state.screenDefinitions[pageId] = getMockPageDefinition(pageId, {
                page: {
                    $standardDeleteAction: $standardDeleteActionMock as any,
                    $standardDuplicateAction: $standardDuplicateActionMock as any,
                } as any,
            });
            addPageControlObject(state, pageId, { onLoad, node: '@sage/xtrem-test/MyTestNode' });
            addFieldToState(FieldKey.Text, state, pageId, 'val', {});
            addFieldToState(FieldKey.Text, state, pageId, 'val02', {});
            mockStore = getMockStore(state);
        });

        afterEach(() => {
            jest.resetAllMocks();
        });

        it('should dispatch SetGlobalLoading, SetValues and triggerAction actions and call setNavigationPanel on mobile', async () => {
            state.browser.lessThan.m = true;
            jest.spyOn(graphqlService, 'fetchRecordData').mockImplementation(
                async ({ screenId: _pageId, recordId }) => {
                    expect(_pageId).toBe(pageId);
                    expect(recordId).toBe('1');
                    return { val: '01', val02: '02' };
                },
            );

            const navigationPanelSpy = jest
                .spyOn(navigationPanelActions, 'setNavigationPanelIsOpened')
                .mockReturnValue(() => {});

            await selectRecord(pageId, '1')(mockStore.dispatch, mockStore.getState);
            const expected: AppAction[] = [
                { type: ActionType.SetGlobalLoading, value: true },
                {
                    type: ActionType.SetPath,
                    value: '/test/path/eyJfaWQiOiIxIn0=',
                },
                {
                    type: ActionType.ResetScreenUiComponentProperties,
                    value: {
                        screenId: pageId,
                    },
                },
                {
                    type: ActionType.SetValues,
                    value: {
                        screenId: pageId,
                        values: {
                            val: '01',
                            val02: '02',
                        },
                    },
                },
                {
                    type: ActionType.ClearNavigationPanelSearchText,
                    value: {
                        screenId: pageId,
                    },
                },
            ];
            expect(mockStore.getActions()).toContainEqual(expected[0]);
            expect(mockStore.getActions()).toContainEqual(expected[1]);

            expect(events.triggerScreenEvent).toHaveBeenCalledTimes(1);

            expect(navigationPanelSpy).toHaveBeenCalledTimes(1);
            navigationPanelSpy.mockRestore();
        });

        it('should dispatch SetGlobalLoading, SetValues and triggerAction actions and call setNavigationPanel', async () => {
            jest.spyOn(graphqlService, 'fetchRecordData').mockImplementation(
                async ({ screenId: _pageId, recordId }) => {
                    expect(_pageId).toBe(pageId);
                    expect(recordId).toBe('1');
                    return { val: '01', val02: '02' };
                },
            );

            const navigationPanelSpy = jest
                .spyOn(navigationPanelActions, 'setNavigationPanelIsOpened')
                .mockReturnValue(() => {});

            await selectRecord(pageId, '1')(mockStore.dispatch, mockStore.getState);
            const expected: AppAction[] = [
                { type: ActionType.SetGlobalLoading, value: true },
                {
                    type: ActionType.SetPath,
                    value: '/test/path/eyJfaWQiOiIxIn0=',
                },
                {
                    type: ActionType.ResetScreenUiComponentProperties,
                    value: {
                        screenId: pageId,
                        activeSection: null,
                    },
                },
                {
                    type: ActionType.SetValues,
                    value: {
                        screenId: pageId,
                        values: {
                            val: '01',
                            val02: '02',
                        },
                    },
                },
                {
                    type: ActionType.ClearNavigationPanelSearchText,
                    value: {
                        screenId: pageId,
                    },
                },
            ];
            expect(mockStore.getActions()).toEqual(expected);
            expect(events.triggerScreenEvent).toHaveBeenCalledTimes(1);

            expect(navigationPanelSpy).toHaveBeenCalledTimes(0);
            navigationPanelSpy.mockRestore();
        });

        it('should enable the standard delete action if the record has an ID ', async () => {
            jest.spyOn(graphqlService, 'fetchRecordData').mockResolvedValue({ val: '01', val02: '02' });

            await selectRecord(pageId, '1')(mockStore.dispatch, mockStore.getState);

            expect(setDeleteCrudActionDisabledMock).toHaveBeenCalledWith(false);
        });

        it('should disable the standard delete action if the user navigates to an empty record', async () => {
            jest.spyOn(graphqlService, 'fetchDefaultValues').mockResolvedValue({});
            await selectRecord(pageId, null)(mockStore.dispatch, mockStore.getState);

            expect(setDeleteCrudActionDisabledMock).toHaveBeenCalledWith(true);
        });

        it('should not call fetchDefaultValues or fetchRecordData if the page is transient', async () => {
            state.screenDefinitions[pageId].metadata.uiComponentProperties[pageId].isTransient = true;
            jest.spyOn(graphqlService, 'fetchDefaultValues').mockResolvedValue({});
            expect(graphqlService.fetchDefaultValues).not.toHaveBeenCalled();
            expect(graphqlService.fetchRecordData).not.toHaveBeenCalled();
            await selectRecord(pageId, null)(mockStore.dispatch, mockStore.getState);
            expect(graphqlService.fetchDefaultValues).not.toHaveBeenCalled();
            expect(graphqlService.fetchRecordData).not.toHaveBeenCalled();
        });

        it('should not call fetchDefaultValues or fetchRecordData if the page has no node', async () => {
            (state.screenDefinitions[pageId].metadata.uiComponentProperties[pageId] as PageProperties<any>).node =
                undefined;
            jest.spyOn(graphqlService, 'fetchDefaultValues').mockResolvedValue({});
            expect(graphqlService.fetchDefaultValues).not.toHaveBeenCalled();
            expect(graphqlService.fetchRecordData).not.toHaveBeenCalled();
            await selectRecord(pageId, null)(mockStore.dispatch, mockStore.getState);
            expect(graphqlService.fetchDefaultValues).not.toHaveBeenCalled();
            expect(graphqlService.fetchRecordData).not.toHaveBeenCalled();
        });

        it('should dispatch a SetGlobalLoading to true and handle errors from fetchRecordData promise', async () => {
            jest.spyOn(graphqlService, 'fetchRecordData').mockImplementation(() => {
                // eslint-disable-next-line prefer-promise-reject-errors
                return Promise.reject('boom');
            });
            const mockFunc = jest.fn();
            jest.spyOn(console, 'error').mockImplementationOnce(mockFunc);
            await selectRecord(pageId, '1')(mockStore.dispatch, mockStore.getState);
            expect(mockStore.getActions()).toContainEqual({ type: ActionType.SetGlobalLoading, value: true });
            expect(mockFunc.call.length).toBe(1);
        });
    });

    describe('update360ViewInPath', () => {
        let mockStore: any;
        let mockDispatch: jest.Mock;
        let mockGetState: jest.Mock;
        let state: any;

        beforeEach(() => {
            state = getMockState();
            state.path = `@sage/module1/page1/${btoa(JSON.stringify({ _id: '1234' }))}`;
            state.applicationContext = { handleNavigation: jest.fn() };

            mockStore = getMockStore(state, false);
            mockDispatch = mockStore.dispatch;
            mockGetState = jest.fn(() => state);
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should add view=360 to the path when enable360 is true', () => {
            update360ViewInPath(true)(mockDispatch, mockGetState);

            const expectedNewPath = `@sage/module1/page1/${btoa(JSON.stringify({ _id: '1234', view: '360' }))}`;

            expect(mockGetState).toHaveBeenCalled();
            expect(state.applicationContext.handleNavigation).toHaveBeenCalledWith(expectedNewPath, {});
        });

        it('should remove view=360 from the path when enable360 is false', () => {
            state.path = `@sage/module1/page1/${btoa(JSON.stringify({ _id: '1234', view: '360' }))}`;

            update360ViewInPath(false)(mockDispatch, mockGetState);

            const expectedNewPath = `@sage/module1/page1/${btoa(JSON.stringify({ _id: '1234' }))}`;

            expect(mockGetState).toHaveBeenCalled();
            expect(state.applicationContext.handleNavigation).toHaveBeenCalledWith(expectedNewPath, {});
        });

        it('should not call handleNavigation if path is empty', () => {
            state.path = '';

            update360ViewInPath(true)(mockDispatch, mockGetState);

            expect(mockGetState).toHaveBeenCalled();
            expect(state.applicationContext.handleNavigation).not.toHaveBeenCalled();
        });
    });

    it('setIdToQueryParameters should create a SetQueryParameter action', () => {
        expect(setIdToQueryParameters('MyTestScreen', '1234')).toEqual({
            type: 'SetQueryParameter',
            value: {
                parameterName: '_id',
                value: '1234',
                screenId: 'MyTestScreen',
            },
        });
    });
});
