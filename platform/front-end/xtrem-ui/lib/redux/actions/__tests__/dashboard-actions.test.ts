import { getMockState, getMockStore } from '../../../__tests__/test-helpers';

import { waitFor } from '@testing-library/react';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { WidgetType } from '../../../dashboard/widgets/abstract-widget';
import * as dashboardService from '../../../service/dashboard-service';
import * as metadataService from '../../../service/metadata-service';
import * as screenLoaderService from '../../../service/screen-loader-service';
import { ActionType } from '../../action-types';
import type { XtremAppState } from '../../state';
import * as dashboardActions from '../dashboard-actions';
import { FakeWidget } from './fake-widget';

const testDashboardGroup = 'home';

describe('Dashboard actions', () => {
    let mockState: XtremAppState;
    let mockStore: MockStoreEnhanced<XtremAppState>;
    beforeEach(() => {
        mockState = getMockState({
            applicationContext: {
                locale: 'en-US',
            } as any,
            dashboard: {
                dashboardGroups: {
                    home: {
                        dashboards: {},
                        widgets: {
                            '123': {
                                _id: '123',
                                artifactName: '@sage/xtrem-test/FakeWidget',
                                properties: { title: 'Fake Widget', getQuery: () => ({}) },
                                widgetObject: {} as any,
                                widgetType: WidgetType.indicatorTile,
                            },
                        },
                        widgetEditor: {
                            isOpen: false,
                            isDirty: false,
                            widgetDefinition: {},
                        },
                        dashboardEditor: {
                            isOpen: false,
                            isDirty: false,
                            history: [],
                            currentHistoryIndex: 0,
                            currentDashboardDefinition: {
                                _id: '-1',
                                children: [],
                                title: '',
                            },
                        },
                        availableDashboards: [],
                    },
                },
                nodeNames: {},
                widgetCategories: {},
                canEditDashboards: true,
            },
        });
        mockStore = getMockStore(mockState);
        jest.spyOn(screenLoaderService, 'fetchWidgetDefinitions').mockResolvedValue({
            '@sage/xtrem-test/FakeWidget': FakeWidget,
        });
        jest.spyOn(metadataService, 'fetchEnumTranslations').mockResolvedValue({
            '@sage/xtrem-show-case/enums__show_case_product_category__awful': 'Awful',
            '@sage/xtrem-show-case/enums__show_case_product_category__good': 'Good',
            '@sage/xtrem-show-case/enums__show_case_product_category__great': 'Great',
            '@sage/xtrem-show-case/enums__show_case_product_category__notBad': 'Not bad',
            '@sage/xtrem-show-case/enums__show_case_product_category__ok': 'Ok',
        });
        jest.spyOn(dashboardService, 'fetchWidgetData').mockResolvedValue({});
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('fetchUserDashboardDefinition', () => {
        beforeEach(() => {
            jest.spyOn(dashboardService, 'fetchCurrentDashboardDefinition').mockResolvedValue({
                dashboard: {
                    _id: '324324',
                    title: 'New Dashboard',
                    children: [
                        {
                            _id: '123',
                            settings: {},
                            type: '@sage/xtrem-test/FakeWidget',
                            positions: [
                                { x: 1, y: 5, breakpoint: 'xxs', w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'xs', w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'sm', w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'md', w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'lg', w: 2, h: 1 },
                            ],
                        },
                    ],
                },
                stringLiterals: {},
                availableDashboards: [
                    { _id: '1', title: 'My dashboard' },
                    { _id: '324324', title: 'New Dashboard' },
                ],
                canEditDashboards: true,
            });
        });

        it('should fetch enum translation details if the widget uses enum', async () => {
            jest.spyOn(dashboardService, 'fetchCurrentDashboardDefinition').mockResolvedValue({
                dashboard: {
                    _id: '324324',
                    title: 'New Dashboard',
                    children: [
                        {
                            _id: '123',
                            settings: { usedEnums: ['MyFakeEnum', 'MyOtherFakeEnum'] },
                            type: '@sage/xtrem-test/FakeWidget',
                            positions: [
                                { x: 1, y: 5, breakpoint: 'xxs', w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'xs', w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'sm', w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'md', w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'lg', w: 2, h: 1 },
                            ],
                        },
                    ],
                },
                stringLiterals: {},
                availableDashboards: [
                    { _id: '1', title: 'My dashboard' },
                    { _id: '324324', title: 'New Dashboard' },
                ],
                canEditDashboards: true,
            });
            await dashboardActions.fetchUserDashboardDefinition(testDashboardGroup)(
                mockStore.dispatch,
                () => mockState,
            );
            await waitFor(() => {
                expect(metadataService.fetchEnumTranslations).toHaveBeenCalledWith(
                    ['MyFakeEnum', 'MyOtherFakeEnum'],
                    'en-US',
                );
                expect(mockStore.getActions()).toEqual([
                    {
                        type: ActionType.SetLoadingDashboards,
                        value: {
                            group: 'home',
                            isLoading: true,
                        },
                    },
                    {
                        type: ActionType.AddTranslations,
                        value: {
                            literals: {},
                            locale: 'en-US',
                        },
                    },
                    {
                        type: ActionType.AddTranslations,
                        value: {
                            literals: {
                                '@sage/xtrem-show-case/enums__show_case_product_category__awful': 'Awful',
                                '@sage/xtrem-show-case/enums__show_case_product_category__good': 'Good',
                                '@sage/xtrem-show-case/enums__show_case_product_category__great': 'Great',
                                '@sage/xtrem-show-case/enums__show_case_product_category__notBad': 'Not bad',
                                '@sage/xtrem-show-case/enums__show_case_product_category__ok': 'Ok',
                            },
                            locale: 'en-US',
                        },
                    },
                    // Loads widgets
                    {
                        type: ActionType.AddWidgets,
                        value: { widgets: expect.any(Array), group: 'home' },
                    },
                    // Add dashboard details
                    {
                        type: ActionType.AddDashboard,
                        value: {
                            dashboard: {
                                _id: '324324',
                                children: expect.any(Array),
                                title: 'New Dashboard',
                            },
                            group: 'home',
                        },
                    },
                    {
                        type: ActionType.SetLoadingDashboards,
                        value: {
                            group: 'home',
                            isLoading: false,
                        },
                    },
                    // Sets dashboard list
                    {
                        type: ActionType.SetInitialDashboardInformation,
                        value: {
                            availableDashboards: [
                                { _id: '1', title: 'My dashboard' },
                                { _id: '324324', title: 'New Dashboard' },
                            ],
                            canEditDashboards: true,
                            group: 'home',
                        },
                    },
                ]);
            });
        });
        it('should load the selected dashboard with details and the dashboard list', async () => {
            await dashboardActions.fetchUserDashboardDefinition(testDashboardGroup)(
                mockStore.dispatch,
                () => mockState,
            );
            await waitFor(() => {
                expect(mockStore.getActions()).toEqual([
                    {
                        type: ActionType.SetLoadingDashboards,
                        value: {
                            group: 'home',
                            isLoading: true,
                        },
                    },
                    {
                        type: ActionType.AddTranslations,
                        value: {
                            literals: {},
                            locale: 'en-US',
                        },
                    },
                    // Loads widgets
                    {
                        type: ActionType.AddWidgets,
                        value: { widgets: [], group: 'home' },
                    },
                    // Add dashboard details
                    {
                        type: ActionType.AddDashboard,
                        value: {
                            group: 'home',
                            dashboard: {
                                _id: '324324',
                                children: [
                                    {
                                        _id: '123',
                                        settings: {},
                                        type: '@sage/xtrem-test/FakeWidget',
                                        positions: [
                                            { x: 1, y: 5, breakpoint: 'xxs', w: 2, h: 1 },
                                            { x: 1, y: 5, breakpoint: 'xs', w: 2, h: 1 },
                                            { x: 1, y: 5, breakpoint: 'sm', w: 2, h: 1 },
                                            { x: 1, y: 5, breakpoint: 'md', w: 2, h: 1 },
                                            { x: 1, y: 5, breakpoint: 'lg', w: 2, h: 1 },
                                        ],
                                    },
                                ],
                                title: 'New Dashboard',
                            },
                        },
                    },
                    {
                        type: ActionType.SetLoadingDashboards,
                        value: {
                            group: 'home',
                            isLoading: false,
                        },
                    },
                    // Sets dashboard list
                    {
                        type: ActionType.SetInitialDashboardInformation,
                        value: {
                            availableDashboards: [
                                { _id: '1', title: 'My dashboard' },
                                { _id: '324324', title: 'New Dashboard' },
                            ],
                            canEditDashboards: true,
                            group: 'home',
                        },
                    },
                ]);
            });
        });
    });

    describe('createEmptyDashboard', () => {
        beforeEach(() => {
            jest.spyOn(dashboardService, 'createEmptyDashboard').mockResolvedValue({
                _id: '324324',
                title: 'New Dashboard',
                children: [],
            });
            jest.spyOn(dashboardService, 'setSelectedDashboard').mockResolvedValue({
                _id: '324324',
                title: 'New Dashboard',
                children: [],
            });
        });

        it('should create a new dashboard and mark it as selected', async () => {
            expect(dashboardService.setSelectedDashboard).not.toHaveBeenCalled();
            await dashboardActions.createEmptyDashboard(testDashboardGroup)(mockStore.dispatch, () => mockState);
            expect(dashboardService.createEmptyDashboard).toHaveBeenCalled();
            expect(dashboardService.setSelectedDashboard).toHaveBeenCalledWith('324324', 'home');
            await waitFor(() => {
                expect(mockStore.getActions()).toEqual([
                    {
                        type: ActionType.AddWidgets,
                        value: { widgets: [], group: 'home' },
                    },
                    {
                        type: ActionType.AddDashboard,
                        value: {
                            dashboard: {
                                _id: '324324',
                                children: [],
                                title: 'New Dashboard',
                            },
                            group: 'home',
                        },
                    },
                    {
                        type: ActionType.SetLoadingDashboards,
                        value: { isLoading: false, group: 'home' },
                    },
                ]);
            });
        });
    });

    describe('cloneDashboard', () => {
        beforeEach(() => {
            jest.spyOn(dashboardService, 'cloneDashboard').mockResolvedValue({
                _id: '324324',
                title: 'Cloned Dashboard',
                children: [
                    {
                        _id: '123',
                        settings: {},
                        type: '@sage/xtrem-test/FakeWidget',
                        positions: [
                            { x: 1, y: 5, breakpoint: 'xxs', w: 2, h: 1 },
                            { x: 1, y: 5, breakpoint: 'xs', w: 2, h: 1 },
                            { x: 1, y: 5, breakpoint: 'sm', w: 2, h: 1 },
                            { x: 1, y: 5, breakpoint: 'md', w: 2, h: 1 },
                            { x: 1, y: 5, breakpoint: 'lg', w: 2, h: 1 },
                        ],
                    },
                ],
            });

            jest.spyOn(dashboardService, 'setSelectedDashboard').mockResolvedValue({
                _id: '324324',
                title: 'Cloned Dashboard',
                children: [
                    {
                        _id: '123',
                        settings: {},
                        type: '@sage/xtrem-test/FakeWidget',
                        positions: [
                            { x: 1, y: 5, breakpoint: 'xxs', w: 2, h: 1 },
                            { x: 1, y: 5, breakpoint: 'xs', w: 2, h: 1 },
                            { x: 1, y: 5, breakpoint: 'sm', w: 2, h: 1 },
                            { x: 1, y: 5, breakpoint: 'md', w: 2, h: 1 },
                            { x: 1, y: 5, breakpoint: 'lg', w: 2, h: 1 },
                        ],
                    },
                ],
            });
        });

        it('should clone the dashboard and mark it as selected', async () => {
            expect(dashboardService.setSelectedDashboard).not.toHaveBeenCalled();
            await dashboardActions.cloneDashboard('32', testDashboardGroup)(mockStore.dispatch, () => mockState);
            expect(dashboardService.cloneDashboard).toHaveBeenCalledWith('32');
            expect(dashboardService.setSelectedDashboard).toHaveBeenCalledWith('324324', 'home');
            await waitFor(() => {
                expect(mockStore.getActions()).toEqual([
                    {
                        type: ActionType.AddWidgets,
                        value: { widgets: [], group: 'home' },
                    },
                    {
                        type: ActionType.AddDashboard,
                        value: {
                            dashboard: {
                                _id: '324324',
                                children: [
                                    {
                                        _id: '123',
                                        settings: {},
                                        type: '@sage/xtrem-test/FakeWidget',
                                        positions: [
                                            { x: 1, y: 5, breakpoint: 'xxs', w: 2, h: 1 },
                                            { x: 1, y: 5, breakpoint: 'xs', w: 2, h: 1 },
                                            { x: 1, y: 5, breakpoint: 'sm', w: 2, h: 1 },
                                            { x: 1, y: 5, breakpoint: 'md', w: 2, h: 1 },
                                            { x: 1, y: 5, breakpoint: 'lg', w: 2, h: 1 },
                                        ],
                                    },
                                ],
                                title: 'Cloned Dashboard',
                            },
                            group: 'home',
                        },
                    },
                    {
                        type: ActionType.SetLoadingDashboards,
                        value: {
                            isLoading: false,
                            group: 'home',
                        },
                    },
                ]);
            });
        });
    });
});
