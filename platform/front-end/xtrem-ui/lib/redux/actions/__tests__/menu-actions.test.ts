import { getMockState, getMockStore } from '../../../__tests__/test-helpers';

import { ActionType } from '../../action-types';
import * as menuActions from '../menu-actions';
import type { XtremAppState, Menu } from '../../state';
import type { MockStoreEnhanced } from 'redux-mock-store';

jest.mock('../menu-items');

describe('Menu actions', () => {
    let mockState: XtremAppState;
    let mockStore: MockStoreEnhanced<XtremAppState>;
    beforeEach(() => {
        mockState = getMockState();
        mockStore = getMockStore(mockState);
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('addMenuItem', () => {
        it('Should return an AddMenuItem action with the same input value', async () => {
            const menuItem = { id: 'TestMenuItem' } as Menu;
            await menuActions.addMenuItem(menuItem)(mockStore.dispatch, mockStore.getState);

            expect(mockStore.getActions()).toEqual([
                {
                    type: ActionType.AddMenuItem,
                    value: menuItem,
                },
            ]);
        });
    });

    describe('removeMenuItem', () => {
        it('Should return a RemoveMenuItem action with the same input value', async () => {
            const menuItemId = 'TestMenuItem';
            await menuActions.removeMenuItem(menuItemId)(mockStore.dispatch, mockStore.getState);

            expect(mockStore.getActions()).toEqual([
                {
                    type: ActionType.RemoveMenuItem,
                    value: menuItemId,
                },
            ]);
        });
    });
});
