import { getMockState, getMockStore } from '../../../__tests__/test-helpers';

import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as actions from '..';
import * as dialogService from '../../../service/dialog-service';
import * as eventUtils from '../../../utils/events';
import type { XtremAppState } from '../../state';
import * as menuItems from '../menu-items';
import '@testing-library/jest-dom';

jest.mock('carbon-react/esm/components/textarea');
jest.mock('carbon-react/esm/components/date');
jest.mock('carbon-react/esm/components/date-range');
jest.mock('carbon-react/esm/components/checkbox');
jest.mock('carbon-react/esm/components/radio-button');
jest.mock('carbon-react/esm/components/action-popover');
jest.mock('carbon-react/esm/components/button-toggle/button-toggle-group');
jest.mock('carbon-react/esm/components/button-toggle/button-toggle.component');
jest.mock('carbon-react/esm/components/dialog');
jest.mock('carbon-react/esm/components/dialog-full-screen');
jest.mock('carbon-react/esm/components/portrait');
jest.mock('carbon-react/esm/components/select');
jest.mock('carbon-react/esm/components/sidebar');
jest.mock('carbon-react/esm/components/step-sequence');
jest.mock('carbon-react/esm/components/toast');
jest.mock('../../../service/dialog-service');

describe('Menu items redux actions', () => {
    let mockStore: MockStoreEnhanced<XtremAppState>;
    const dialogId = 1;

    beforeEach(() => {
        mockStore = getMockStore();
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('getCloseDialogMenuItem', () => {
        it('Should return a close dialog menu item', () => {
            const menuItem = menuItems.getCloseDialogMenuItem(dialogId, false);
            expect(menuItem.alignedLeft).toBe(true);
            expect(menuItem.icon).toBe('cross');
            expect(menuItem.id).toBe(menuItems.closeDialogMenuItemId);
            expect(menuItem.onClick).toBeDefined();
            expect(menuItem.title).toBe('Close dialog');
        });

        it('Should dispatch closeDialog action on close dialog menu item click', () => {
            mockStore = getMockStore(undefined, false);
            const closeDialogSpy = jest
                .spyOn(actions, 'closeDialog')
                .mockReturnValue(() => Promise.resolve({ type: 'CLOSE' }) as any);
            const dispatchSpy = jest.spyOn(mockStore, 'dispatch');
            const menuItem = menuItems.getCloseDialogMenuItem(dialogId, false);
            menuItem.onClick();
            expect(closeDialogSpy).toHaveBeenCalledTimes(1);
            expect(dispatchSpy).toHaveBeenCalledTimes(1);
        });
    });

    describe('getStickerMenuItem', () => {
        const stickerId = 'stickerId';
        const icon: IconType = 'analysis';
        const sections: any[] = [];

        it('should return a sticker menu item', () => {
            const menuItem = menuItems.getStickerMenuItem(stickerId, icon, sections);
            expect(menuItem.icon).toBe(icon);
            expect(menuItem.id).toBe(stickerId);
            expect(menuItem.onClick).toBeDefined();
            expect(menuItem.title).toBeDefined();
        });

        it('should create a dialog on sticker menu item click', () => {
            const menuItem = menuItems.getStickerMenuItem(stickerId, icon, sections);
            const createStickerDialogSpy = jest.spyOn(dialogService, 'createStickerDialog');
            menuItem.onClick();
            expect(createStickerDialogSpy).toHaveBeenCalledTimes(1);
        });

        it('should not duplicate active dialogs on sticker menu item click', () => {
            const menuItem = menuItems.getStickerMenuItem('stickerId', icon, sections);
            const mockState = getMockState();
            mockState.activeDialogs = {
                [dialogId]: {
                    buttons: {},
                    content: '',
                    dialogControl: dialogService.createStickerDialog(mockStore, 'stickerId', 'info', '', '' as any, {}),
                    dialogId,
                    isSticker: true,
                    level: 'info',
                    screenId: 'stickerId',
                    title: '',
                    options: {},
                },
            };
            mockStore = getMockStore(mockState);
            const createStickerDialogSpy = jest.spyOn(dialogService, 'createStickerDialog');
            menuItem.onClick();
            expect(createStickerDialogSpy).toHaveBeenCalledTimes(1);
        });

        it('should trigger the `onOpen` event when the menu item is clicked', () => {
            const menuItem = menuItems.getStickerMenuItem(stickerId, icon, sections);
            const triggerEventListenerSpy = jest.spyOn(eventUtils, 'triggerScreenEvent');
            menuItem.onClick();
            expect(triggerEventListenerSpy).toHaveBeenCalledTimes(1);
            expect(triggerEventListenerSpy).toHaveBeenCalledWith(stickerId, 'onOpen');
        });

        it('should set the `category` property when it is not declared by the functional developer', () => {
            const menuItem = menuItems.getStickerMenuItem(stickerId, icon, sections);
            expect(menuItem.category).toEqual('sticker');
        });

        it('should set the `category` property when it is declared by the functional developer', () => {
            const menuItem = menuItems.getStickerMenuItem(
                stickerId,
                icon,
                sections,
                undefined,
                undefined,
                'custom-category',
            );
            expect(menuItem.category).toEqual('sticker/custom-category');
        });
    });
});
