import axios from 'axios';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../..';
import {
    getMockPageMetadata,
    getMockState,
    getMockStickerDefinition,
    getMockStore,
} from '../../../__tests__/test-helpers';
import { StickerControlObject } from '../../../component/control-objects';
import type { StickerDecoratorProperties } from '../../../component/decorators';
import type { MetadataType } from '../../../service/metadata-types';
import type { PageArticleItem } from '../../../service/layout-types';
import * as screenLoaderService from '../../../service/screen-loader-service';
import type { Sticker } from '../../../service/sticker';
import type { StickerDefinition } from '../../../service/sticker-definition';
import * as menuItems from '../menu-items';
import { discoverStickers } from '../sticker-actions';
import * as events from '../../../utils/events';

export const getStickerDefinition = (stickerId: string, isActive = true) => {
    return getMockStickerDefinition(stickerId, {
        metadata: getMockPageMetadata(stickerId, {
            uiComponentProperties: {
                [stickerId]: {
                    icon: 'home',
                    isActive: () => isActive,
                } as StickerDecoratorProperties<Sticker>,
            },
            controlObjects: {
                [stickerId]: new StickerControlObject({
                    dispatchPageValidation: jest.fn(),
                    getUiComponentProperties: jest.fn().mockReturnValue({}),
                    layout: {} as Partial<PageArticleItem>,
                    screenId: stickerId,
                    setUiComponentProperties: jest.fn(),
                    updateMenuItem: jest.fn(),
                }),
            },
        }),
    });
};

describe('Sticker actions', () => {
    const stickerMenuItem: xtremRedux.Menu = {
        id: 'menuId',
        title: 'menuTitle',
        onClick: () => {},
        category: 'sticker',
    };
    const sticker1Id = 'testModule1/testSticker1';
    const sticker2Id = 'testModule2/testSticker2';
    const sticker3Id = 'testModule2/testSticker3';

    let state: xtremRedux.XtremAppState;
    let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;
    let sticker1Definition: StickerDefinition;
    let sticker2Definition: StickerDefinition;
    let sticker3Definition: StickerDefinition;

    beforeEach(() => {
        state = getMockState();
        sticker1Definition = getStickerDefinition(sticker1Id);
        sticker2Definition = getStickerDefinition(sticker2Id);
        sticker3Definition = getStickerDefinition(sticker3Id, false);

        mockStore = getMockStore(state);

        jest.spyOn(axios, 'post').mockImplementation(() =>
            Promise.resolve({
                data: {
                    data: {
                        stickers: [{ key: sticker1Id }, { key: sticker2Id }, { key: sticker3Id }] as MetadataType[],
                    },
                },
                status: 201,
                statusText: 'CREATED',
                headers: {},
                config: {},
            }),
        );
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('Should dispatch SetGlobalLoading, SetPath, SetPageDefinition when navigate to a new page', async () => {
        const fetchStickerMock = jest
            .spyOn(screenLoaderService, 'fetchStickerDefinition')
            .mockReturnValueOnce(Promise.resolve(sticker1Definition));
        jest.spyOn(screenLoaderService, 'fetchStickerDefinition').mockReturnValueOnce(
            Promise.resolve(sticker2Definition),
        );
        jest.spyOn(screenLoaderService, 'fetchStickerDefinition').mockReturnValueOnce(
            Promise.resolve(sticker3Definition),
        );
        const getMenuItemMock = jest.spyOn(menuItems, 'getStickerMenuItem').mockReturnValue(stickerMenuItem);

        await discoverStickers()(mockStore.dispatch, mockStore.getState);

        expect(fetchStickerMock).toHaveBeenCalledTimes(3);
        expect(fetchStickerMock.mock.calls[0][2]).toEqual(sticker1Id);
        expect(fetchStickerMock.mock.calls[1][2]).toEqual(sticker2Id);
        expect(fetchStickerMock.mock.calls[2][2]).toEqual(sticker3Id);

        expect(getMenuItemMock).toHaveBeenCalledTimes(2);

        const addMenuItemAction = {
            type: xtremRedux.ActionType.AddMenuItem,
            value: stickerMenuItem,
        };
        const expectedActions = [
            {
                type: xtremRedux.ActionType.AddScreenDefinition,
                value: sticker1Definition,
            },
            {
                type: xtremRedux.ActionType.AddScreenDefinition,
                value: sticker2Definition,
            },
            {
                type: xtremRedux.ActionType.AddScreenDefinition,
                value: sticker3Definition,
            },
            addMenuItemAction,
            addMenuItemAction,
        ];

        expect(mockStore.getActions()).toEqual(expectedActions);
        expect(events.triggerScreenEvent).toHaveBeenCalledTimes(2);
        expect(events.triggerScreenEvent).toHaveBeenNthCalledWith(1, sticker1Id, 'onLoad');
        expect(events.triggerScreenEvent).toHaveBeenNthCalledWith(2, sticker2Id, 'onLoad');
    });
});
