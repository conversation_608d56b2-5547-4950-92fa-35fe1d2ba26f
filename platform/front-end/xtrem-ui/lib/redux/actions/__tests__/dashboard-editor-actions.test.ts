import { getMockState, getMockStore } from '../../../__tests__/test-helpers';

import type { MockStoreEnhanced } from 'redux-mock-store';
import { WidgetType } from '../../../dashboard/widgets/abstract-widget';
import { CollectionValue } from '../../../service/collection-data-service';
import * as dashboardService from '../../../service/dashboard-service';
import * as screenLoaderService from '../../../service/screen-loader-service';
import { DASHBOARD_SCREEN_ID } from '../../../utils/constants';
import { ActionType } from '../../action-types';
import type { UserWidgetDefinition, XtremAppState } from '../../state';
import * as dashboardEditorActions from '../dashboard-editor-actions';
import { FakeWidget } from './fake-widget';

const testDashboardGroup = 'home';

describe('dashboard editor actions', () => {
    let mockState: XtremAppState;
    let mockStore: MockStoreEnhanced<XtremAppState>;

    beforeEach(() => {
        dashboardEditorActions.resetNextWidgetIdOnlyToBeUsedInTests();
        mockState = getMockState({
            dashboard: {
                canEditDashboards: false,
                dashboardGroups: {
                    home: {
                        dashboards: {
                            '1234': {
                                isSelected: true,
                                _id: '1234',
                                title: 'dashboard',
                                children: [
                                    {
                                        _id: '123',
                                        type: '@sage/xtrem-test/FakeWidget',
                                        positions: [
                                            { x: 1, y: 1, breakpoint: 'xxs', w: 1, h: 1 },
                                            { x: 1, y: 1, breakpoint: 'xs', w: 1, h: 1 },
                                            { x: 1, y: 1, breakpoint: 'sm', w: 1, h: 1 },
                                            { x: 1, y: 1, breakpoint: 'md', w: 1, h: 1 },
                                            { x: 1, y: 1, breakpoint: 'lg', w: 1, h: 1 },
                                        ],
                                        settings: {},
                                    },
                                ],
                            },
                        },
                        widgets: {
                            '123': {
                                _id: '123',
                                artifactName: '@sage/xtrem-test/FakeWidget',
                                properties: { title: 'Fake Widget', getQuery: () => ({}) },
                                widgetObject: {} as any,
                                widgetType: WidgetType.indicatorTile,
                            },
                        },
                        dashboardEditor: {
                            currentDashboardDefinition: {
                                _id: '456',
                                title: '',
                                children: [],
                            },
                            currentHistoryIndex: 0,
                            history: [],
                            isDirty: false,
                            isOpen: true,
                        },
                        widgetEditor: {
                            isOpen: false,
                            isDirty: false,
                            widgetDefinition: {},
                        },
                        availableDashboards: [],
                    },
                },
                widgetCategories: {},
                nodeNames: {},
            },
        });

        mockStore = getMockStore(mockState);

        jest.spyOn(screenLoaderService, 'fetchWidgetDefinitions').mockResolvedValue({
            '@sage/xtrem-test/FakeWidget': FakeWidget,
        });
        jest.spyOn(dashboardService, 'fetchWidgetData').mockResolvedValue({});
        jest.spyOn(dashboardService, 'getWidgetList').mockResolvedValue({
            categories: [],
            widgets: [
                {
                    category: { _id: '_OTHERS', title: 'Others', sortValue: 9007199254740991 },
                    description: '',
                    title: 'Ratio of administrators',
                    type: 'gauge',
                    _id: '@sage/xtrem-show-case/AdministratorGauge',
                },
                {
                    category: { _id: 'DEMO_CATEGORY', title: 'My demo category', sortValue: 1 },
                    description: 'Detailed list about the current users',
                    title: 'Users',
                    type: 'table',
                    _id: '@sage/xtrem-show-case/ListOfUsersTable',
                },
            ],
        });
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('openDashboardEditorDialog', () => {
        it('should fetch widget list from the server if it has not been loaded yet', async () => {
            await dashboardEditorActions.openDashboardEditorDialog(testDashboardGroup)(
                mockStore.dispatch,
                () => mockState,
            );
            const actions = mockStore.getActions();
            expect(actions).toHaveLength(2);
            expect(actions[0].type).toEqual(ActionType.AddScreenDefinition);
            expect(actions[0].value).toEqual(
                expect.objectContaining({
                    metadata: expect.objectContaining({
                        screenId: DASHBOARD_SCREEN_ID,
                    }),
                    navigationPanel: {
                        groupByField: 'category',
                        isHeaderHidden: false,
                        isHidden: false,
                        isOpened: true,
                        isRefreshing: false,
                        value: expect.any(CollectionValue),
                    },
                }),
            );
            expect(actions[1].type).toEqual(ActionType.OpenDashboardEditor);
        });
    });

    describe('updateDashboardTitleInEditor', () => {
        it('should update widget title and dispatch update', () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.currentDashboardDefinition = {
                _id: '456',
                title: 'old title',
                children: [],
            };

            dashboardEditorActions.updateDashboardTitleInEditor('new title', testDashboardGroup)(
                mockStore.dispatch,
                () => mockState,
            );
            const actions = mockStore.getActions();
            expect(actions).toHaveLength(1);
            expect(actions[0].type).toEqual(ActionType.UpdateDashboardEditorWithHistory);
            expect(actions[0].value.dashboard).toEqual({
                _id: '456',
                title: 'new title',
                children: [],
            });
        });

        it('should not dispatch an update if the new title identical to the old one', () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.currentDashboardDefinition = {
                _id: '456',
                title: 'old title',
                children: [],
            };

            dashboardEditorActions.updateDashboardTitleInEditor('old title', testDashboardGroup)(
                mockStore.dispatch,
                () => mockState,
            );
            const actions = mockStore.getActions();
            expect(actions).toHaveLength(0);
        });
    });

    describe('removeDashboardEditorWidget', () => {
        it('should remove widget from the dashboard layout definition', () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.currentDashboardDefinition = {
                _id: '123',
                title: 'title',
                children: [
                    {
                        _id: '4',
                        positions: [
                            { x: 1, y: 1, breakpoint: 'xxs', w: 1, h: 1 },
                            { x: 1, y: 1, breakpoint: 'xs', w: 1, h: 1 },
                            { x: 1, y: 1, breakpoint: 'sm', w: 1, h: 1 },
                            { x: 1, y: 1, breakpoint: 'md', w: 1, h: 1 },
                            { x: 1, y: 1, breakpoint: 'lg', w: 1, h: 1 },
                        ],
                        settings: {},
                        type: '@sage/xtrem-test/myWidget',
                    },
                    {
                        _id: '5',
                        positions: [
                            { x: 1, y: 1, breakpoint: 'xxs', w: 1, h: 1 },
                            { x: 1, y: 1, breakpoint: 'xs', w: 1, h: 1 },
                            { x: 1, y: 1, breakpoint: 'sm', w: 1, h: 1 },
                            { x: 1, y: 1, breakpoint: 'md', w: 1, h: 1 },
                            { x: 1, y: 1, breakpoint: 'lg', w: 1, h: 1 },
                        ],
                        settings: {},
                        type: '@sage/xtrem-test/myWidget',
                    },
                ],
            };

            dashboardEditorActions.removeDashboardEditorWidget('5', testDashboardGroup)(
                mockStore.dispatch,
                () => mockState,
            );
            const actions = mockStore.getActions();
            expect(actions).toHaveLength(1);
            expect(actions[0].type).toEqual(ActionType.UpdateDashboardEditorWithHistory);
            expect(actions[0].value.dashboard).toEqual({
                _id: '123',
                title: 'title',
                children: [
                    {
                        _id: '4',
                        positions: [
                            { x: 1, y: 1, breakpoint: 'xxs', w: 1, h: 1 },
                            { x: 1, y: 1, breakpoint: 'xs', w: 1, h: 1 },
                            { x: 1, y: 1, breakpoint: 'sm', w: 1, h: 1 },
                            { x: 1, y: 1, breakpoint: 'md', w: 1, h: 1 },
                            { x: 1, y: 1, breakpoint: 'lg', w: 1, h: 1 },
                        ],
                        settings: {},
                        type: '@sage/xtrem-test/myWidget',
                    },
                ],
            });
        });
    });

    describe('updateDashboardEditorStateWithHistory', () => {
        it('should add the new dashboard state to the history', () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.history = [
                {
                    _id: '123',
                    title: 'older state',
                    children: [],
                },
            ];
            dashboardEditorActions.updateDashboardEditorStateWithHistory(
                {
                    _id: '123',
                    title: 'title',
                    children: [],
                },
                testDashboardGroup,
            )(mockStore.dispatch, () => mockState);

            const actions = mockStore.getActions();
            expect(actions[0].type).toEqual(ActionType.UpdateDashboardEditorWithHistory);
            expect(actions[0].value.dashboard).toEqual({
                _id: '123',
                title: 'title',
                children: [],
            });
            expect(actions[0].value.history).toEqual([
                {
                    _id: '123',
                    title: 'older state',
                    children: [],
                },
                {
                    _id: '123',
                    title: 'title',
                    children: [],
                },
            ]);
        });
    });

    describe('loadNewWidget', () => {
        const dataFixture = { fake: { widget: 'data' } };
        beforeEach(() => {
            jest.spyOn(dashboardService, 'fetchWidgetData').mockResolvedValue(dataFixture);
        });

        it('should load an application developer defined widget', async () => {
            expect(screenLoaderService.fetchWidgetDefinitions).not.toHaveBeenCalled();
            await dashboardEditorActions.loadNewWidget(
                '@sage/xtrem-test/FakeWidget',
                '123',
                '456',
                testDashboardGroup,
            )(mockStore.dispatch, () => mockState);
            expect(screenLoaderService.fetchWidgetDefinitions).toHaveBeenCalledTimes(1);
            const actions = mockStore.getActions();
            expect(actions).toHaveLength(5);
            expect(actions[0].type).toEqual(ActionType.AddWidgets);
            expect(actions[0].value).toEqual({
                group: 'home',
                widgets: [
                    expect.objectContaining({
                        _id: '123',
                        artifactName: '@sage/xtrem-test/FakeWidget',
                        widgetObject: expect.any(FakeWidget),
                    }),
                ],
            });
            expect(actions[1].type).toEqual(ActionType.SetWidgetData);
            expect(actions[1].value).toEqual(expect.objectContaining({ data: null, widgetId: '123' }));
            expect(actions[4].type).toEqual(ActionType.SetWidgetData);
            expect(actions[4].value).toEqual(expect.objectContaining({ data: dataFixture, widgetId: '123' }));
        });

        it('should set the widget id and dashboard id to the widget prototype of an application developer defined widget', async () => {
            await dashboardEditorActions.loadNewWidget(
                '@sage/xtrem-test/FakeWidget',
                '123',
                '456',
                testDashboardGroup,
            )(mockStore.dispatch, () => mockState);
            const actions = mockStore.getActions();
            expect(actions[0].type).toEqual(ActionType.AddWidgets);
            expect(actions[0].value.widgets[0].widgetObject.constructor.prototype.__id).toEqual('123');
            expect(actions[0].value.widgets[0].widgetObject.constructor.prototype.__dashboardId).toEqual('456');
        });

        it('should load a user defined widget', async () => {
            expect(screenLoaderService.fetchWidgetDefinitions).not.toHaveBeenCalled();
            await dashboardEditorActions.loadNewWidget(
                '@sage/xtrem-ui/GenericTableWidget',
                '123',
                '456',
                testDashboardGroup,
                undefined,
                {
                    type: 'TABLE',
                    node: '@sage/xtrem-test/SomeTestNode',
                    title: 'Test widget',
                } as UserWidgetDefinition,
            )(mockStore.dispatch, () => mockState);
            expect(screenLoaderService.fetchWidgetDefinitions).not.toHaveBeenCalled();
            const actions = mockStore.getActions();
            expect(actions).toHaveLength(5);
            expect(actions[0].type).toEqual(ActionType.AddWidgets);
            expect(actions[0].value.widgets).toEqual([
                expect.objectContaining({ _id: '123', artifactName: '@sage/xtrem-ui/GenericTableWidget' }),
            ]);

            expect(actions[0].value.widgets[0].widgetObject.constructor.name).toEqual('GenericTableWidget');
            expect(actions[1].type).toEqual(ActionType.SetWidgetData);
            expect(actions[1].value).toEqual(expect.objectContaining({ data: null, widgetId: '123' }));
            expect(actions[4].type).toEqual(ActionType.SetWidgetData);
            expect(actions[4].value).toEqual(expect.objectContaining({ data: dataFixture, widgetId: '123' }));
        });

        it('should set the widget id and dashboard id to the widget prototype of a user defined widget', async () => {
            await dashboardEditorActions.loadNewWidget(
                '@sage/xtrem-ui/GenericTableWidget',
                '123',
                '456',
                testDashboardGroup,
                undefined,
                {
                    type: 'TABLE',
                    node: '@sage/xtrem-test/SomeTestNode',
                    title: 'Test widget',
                } as UserWidgetDefinition,
            )(mockStore.dispatch, () => mockState);
            const actions = mockStore.getActions();
            expect(actions[0].type).toEqual(ActionType.AddWidgets);
            expect(actions[0].value.widgets[0].widgetObject.constructor.prototype.__id).toEqual('123');
            expect(actions[0].value.widgets[0].widgetObject.constructor.prototype.__dashboardId).toEqual('456');
        });

        it('should throw an error if no settings provided for a user defined widget', async () => {
            await expect(() =>
                dashboardEditorActions.loadNewWidget(
                    '@sage/xtrem-ui/GenericTableWidget',
                    '123',
                    '456',
                    testDashboardGroup,
                )(mockStore.dispatch, () => mockState),
            ).rejects.toThrow('Generic widgets must have a settings object');
        });
    });

    describe('finishEditingUserDefinedWidget', () => {
        const dataFixture = { fake: { widget: 'data' } };
        beforeEach(() => {
            jest.spyOn(dashboardService, 'fetchWidgetData').mockResolvedValue(dataFixture);
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetDefinition = {
                type: 'TABLE',
                node: '@sage/xtrem-test/SomeTestNode',
                title: 'Test widget',
            };
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets['-1'] = {
                artifactName: '@sage/xtrem-ui/GenericTableWidget',
                widgetType: WidgetType.table,
                widgetObject: {} as any,
                properties: {} as any,
                data: {},
                _id: '-1',
            };
        });

        it('should dispatch actions to load the user defined widget', async () => {
            await dashboardEditorActions.finishEditingUserDefinedWidget(testDashboardGroup)(
                mockStore.dispatch,
                () => mockState,
            );
            const actions = mockStore.getActions();

            expect(actions.length).toEqual(8);
            expect(actions[0].type).toEqual(ActionType.AddWidgets);
            expect(actions[0].value.widgets).toEqual([
                expect.objectContaining({ _id: '-1', artifactName: '@sage/xtrem-ui/GenericTableWidget' }),
            ]);

            expect(actions[0].value.widgets[0].widgetObject.constructor.name).toEqual('GenericTableWidget');
            expect(actions[1].type).toEqual(ActionType.SetWidgetData);
            expect(actions[1].value).toEqual(expect.objectContaining({ data: null, widgetId: '-1' }));
            expect(actions[5].type).toEqual(ActionType.SetWidgetData);
            expect(actions[5].value).toEqual(expect.objectContaining({ data: dataFixture, widgetId: '-1' }));
        });

        it('should dispatch actions to close the widget editor', async () => {
            await dashboardEditorActions.finishEditingUserDefinedWidget(testDashboardGroup)(
                mockStore.dispatch,
                () => mockState,
            );
            const actions = mockStore.getActions();
            expect(actions[6]).toEqual({
                type: ActionType.SetWidgetEditorOpen,
                value: { isOpen: false, group: 'home' },
            });
        });

        it('should dispatch actions to add the widget to the dashboard editor state', async () => {
            await dashboardEditorActions.finishEditingUserDefinedWidget(testDashboardGroup)(
                mockStore.dispatch,
                () => mockState,
            );
            const actions = mockStore.getActions();
            expect(actions[7]).toEqual({
                type: ActionType.UpdateDashboardEditorWithHistory,
                value: {
                    group: 'home',
                    history: [
                        expect.objectContaining({
                            _id: '456',
                            children: [
                                expect.objectContaining({
                                    _id: '-1',
                                    settings: {
                                        node: '@sage/xtrem-test/SomeTestNode',
                                        title: 'Test widget',
                                        type: 'TABLE',
                                    },
                                    type: '@sage/xtrem-ui/GenericTableWidget',
                                    positions: [
                                        { x: 0, y: -Infinity, breakpoint: 'xxs', w: 2, h: 2 },
                                        { x: 0, y: -Infinity, breakpoint: 'xs', w: 2, h: 2 },
                                        { x: 0, y: -Infinity, breakpoint: 'sm', w: 2, h: 2 },
                                        { x: 0, y: -Infinity, breakpoint: 'md', w: 2, h: 2 },
                                        { x: 0, y: -Infinity, breakpoint: 'lg', w: 2, h: 2 },
                                    ],
                                }),
                            ],
                            title: '',
                        }),
                    ],
                    dashboard: {
                        _id: '456',
                        children: [
                            expect.objectContaining({
                                _id: '-1',
                                settings: {
                                    node: '@sage/xtrem-test/SomeTestNode',
                                    title: 'Test widget',
                                    type: 'TABLE',
                                },
                                type: '@sage/xtrem-ui/GenericTableWidget',
                                positions: [
                                    { x: 0, y: -Infinity, breakpoint: 'xxs', w: 2, h: 2 },
                                    { x: 0, y: -Infinity, breakpoint: 'xs', w: 2, h: 2 },
                                    { x: 0, y: -Infinity, breakpoint: 'sm', w: 2, h: 2 },
                                    { x: 0, y: -Infinity, breakpoint: 'md', w: 2, h: 2 },
                                    { x: 0, y: -Infinity, breakpoint: 'lg', w: 2, h: 2 },
                                ],
                            }),
                        ],
                        title: '',
                    },
                },
            });
        });

        it('should dispatch actions to update the widget in dashboard editor state', async () => {
            mockState.dashboard.dashboardGroups[
                testDashboardGroup
            ].dashboardEditor.currentDashboardDefinition.children = [
                {
                    _id: '123',
                    type: '@sage/xtrem-test/FakeWidget',
                    positions: [
                        { x: 1, y: 1, breakpoint: 'xxs', w: 1, h: 1 },
                        { x: 1, y: 1, breakpoint: 'xs', w: 1, h: 1 },
                        { x: 1, y: 1, breakpoint: 'sm', w: 1, h: 1 },
                        { x: 1, y: 1, breakpoint: 'md', w: 1, h: 1 },
                        { x: 1, y: 1, breakpoint: 'lg', w: 1, h: 1 },
                    ],
                    settings: {},
                },
            ];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetId = '123';
            await dashboardEditorActions.finishEditingUserDefinedWidget(testDashboardGroup)(
                mockStore.dispatch,
                () => mockState,
            );
            const actions = mockStore.getActions();
            expect(actions[6]).toEqual({
                type: ActionType.UpdateDashboardEditorWithHistory,
                value: {
                    group: 'home',
                    history: [
                        expect.objectContaining({
                            _id: '456',
                            children: [
                                expect.objectContaining({
                                    _id: '123',
                                    settings: {
                                        node: '@sage/xtrem-test/SomeTestNode',
                                        title: 'Test widget',
                                        type: 'TABLE',
                                    },
                                    type: '@sage/xtrem-test/FakeWidget',
                                }),
                            ],
                            title: '',
                        }),
                    ],
                    dashboard: {
                        _id: '456',
                        children: [
                            expect.objectContaining({
                                _id: '123',
                                settings: {
                                    node: '@sage/xtrem-test/SomeTestNode',
                                    title: 'Test widget',
                                    type: 'TABLE',
                                },
                                type: '@sage/xtrem-test/FakeWidget',
                            }),
                        ],
                        title: '',
                    },
                },
            });
        });
    });
});
