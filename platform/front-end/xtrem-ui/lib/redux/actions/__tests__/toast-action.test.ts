import type { Toast } from '../../../service/toast-service';
import { ActionType } from '../../action-types';
import * as toastActions from '../toast-actions';

describe('Toast actions', () => {
    it('should return a ShowToast action with the same input value', () => {
        const toastDetails: Toast = {
            content: 'Some content',
            timeout: 1234,
            type: 'error',
            id: 1234,
            isDismissed: false,
        };
        const action = toastActions.showToast(toastDetails);
        const expected = {
            type: ActionType.ShowToast,
            value: toastDetails,
        };
        expect(action).toEqual(expected);
    });

    it('should return a RemoveToast action with the same toast id', () => {
        const toastId = 2345;
        const action = toastActions.removeToast(toastId);
        const expected = {
            type: ActionType.RemoveToast,
            value: toastId,
        };
        expect(action).toEqual(expected);
    });

    it('should return a RemoveToasts action with a null value', () => {
        const action = toastActions.removeToasts();
        const expected = {
            type: ActionType.RemoveToasts,
            value: null,
        };
        expect(action).toEqual(expected);
    });
});
