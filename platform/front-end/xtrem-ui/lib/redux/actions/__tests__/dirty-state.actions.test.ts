import * as stateUtils from '../../../utils/state-utils';
import * as events from '../../../utils/events';
import type { MockStoreEnhanced } from 'redux-mock-store';
import {
    getMockPageDefinition,
    getMockPageMetadata,
    getMockState,
    getMockStore,
} from '../../../__tests__/test-helpers';
import { ActionType } from '../../action-types';
import * as dirtyStateActions from '../dirty-state-actions';
import type * as xtremRedux from '../../index';
import type { XtremAppState } from '../../state';
import type { PageDecoratorProperties } from '../../../component/decorators';

describe('Dirty state redux actions', () => {
    const screenId = 'TestPage';
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let commitValueAndPropertyChangesMock: jest.MockInstance<any, any>;
    let isTransactionInProgressMock: jest.MockInstance<any, any>;

    beforeEach(() => {
        const mockState = getMockState({});
        commitValueAndPropertyChangesMock = jest.fn().mockResolvedValue(null);
        isTransactionInProgressMock = jest.fn().mockResolvedValue(false);
        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
            page: {
                $: {
                    commitValueAndPropertyChanges: commitValueAndPropertyChangesMock,
                    isTransactionInProgress: isTransactionInProgressMock,
                },
            } as any,
            metadata: getMockPageMetadata(screenId, {
                uiComponentProperties: {
                    [screenId]: {},
                    bind01: {},
                },
            }),
        });
        mockStore = getMockStore(mockState);
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('setFieldDirty ', () => {
        const elementId = 'bind01';

        let pageProperties: PageDecoratorProperties<any>;
        const onDirtyStatusChangeMock = jest.fn();
        const onDirtyStateUpdatedMock = jest.fn();
        const onErrorMock = jest.fn();
        const isScreenDefinitionDirtySpy = jest.spyOn(stateUtils, 'isScreenDefinitionDirty');
        const hasAnyDirtyScreenDefinitionsSpy = jest.spyOn(stateUtils, 'hasAnyDirtyScreenDefinitions');
        const callSetFieldDirty = () => {
            dirtyStateActions.setFieldDirty({ screenId, elementId })(mockStore.dispatch, mockStore.getState);
        };

        const checkExpectedAction = () => {
            const expected: xtremRedux.AppAction[] = [
                {
                    type: ActionType.SetFieldDirtyState,
                    value: { screenId, elementId },
                },
            ];
            expect(mockStore.getActions()).toEqual(expected);
        };

        beforeEach(() => {
            const mockState = mockStore.getState();
            mockState.applicationContext = {
                updateMenu: jest.fn(),
                handleNavigation: jest.fn(),
                onPageTitleChange: jest.fn(),
                onDirtyStatusChange: onDirtyStatusChangeMock,
            };
            pageProperties = mockState.screenDefinitions[screenId].metadata.uiComponentProperties[
                screenId
            ] as PageDecoratorProperties<any>;
            pageProperties.onDirtyStateUpdated = onDirtyStateUpdatedMock;
            pageProperties.onError = onErrorMock;
        });

        it('Should dispatch a SetFieldDirtyState action with the screenId, elementID', () => {
            isScreenDefinitionDirtySpy.mockReturnValue(true);
            hasAnyDirtyScreenDefinitionsSpy.mockReturnValue(true);

            callSetFieldDirty();

            expect(events.triggerFieldEvent).not.toHaveBeenCalled();
            expect(onDirtyStatusChangeMock).not.toHaveBeenCalled();

            checkExpectedAction();
        });

        it('Should dispatch a SetFieldDirtyState action with the screenId, elementID', () => {
            isScreenDefinitionDirtySpy.mockReturnValue(false);
            hasAnyDirtyScreenDefinitionsSpy.mockReturnValue(false);

            callSetFieldDirty();

            expect(events.triggerScreenEvent).toHaveBeenCalled();
            expect(onDirtyStatusChangeMock).toHaveBeenCalledWith(true, expect.any(Function));
            expect(events.triggerScreenEvent).toHaveBeenCalledWith(screenId, 'onDirtyStateUpdated', true);

            checkExpectedAction();
        });

        it("Should not call the consumer's dirty status listener if the page dirty state is disabled", () => {
            pageProperties.skipDirtyCheck = true;
            isScreenDefinitionDirtySpy.mockReturnValue(false);
            hasAnyDirtyScreenDefinitionsSpy.mockReturnValue(false);

            callSetFieldDirty();

            expect(events.triggerFieldEvent).not.toHaveBeenCalled();
            expect(onDirtyStatusChangeMock).not.toHaveBeenCalled();

            checkExpectedAction();
        });
    });
});
