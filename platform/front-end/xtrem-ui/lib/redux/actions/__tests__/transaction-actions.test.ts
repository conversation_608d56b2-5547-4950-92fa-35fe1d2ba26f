import type { XtremUiTransactionEntry } from '../../../service/transactions-service';
import { ActionType } from '../../action-types';
import * as transactionActions from '../transaction-actions';

describe('Transaction actions', () => {
    const screenId = 'TestPage';

    it('Should return a CommitTransaction action type', () => {
        const transaction = { uiComponentProperties: { elementId: {} as XtremUiTransactionEntry }, values: {} };
        const expectedAction = {
            type: ActionType.CommitTransaction,
            value: { screenId, transaction },
        };

        const action = transactionActions.commitTransaction(screenId, transaction);

        expect(action).toEqual(expectedAction);
    });
});
