import { waitFor } from '@testing-library/dom';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { PageProperties } from '../../../component/container/container-properties';
import { navigationPanelId } from '../../../component/container/navigation-panel/navigation-panel-types';
import { FieldKey } from '../../../component/types';
import * as graphqlService from '../../../service/graphql-service';
import { createNavigationPanelValue, createNavigationTableProperties } from '../../../service/navigation-panel-service';
import type { Page } from '../../../service/page';
import type { PageDefinition } from '../../../service/page-definition';
import * as routerService from '../../../service/router';
import { getMockPageDefinition, getMockState, getMockStore } from '../../../__tests__/test-helpers';
import { ActionType } from '../../action-types';
import type * as xtremRedux from '../../index';
import type { XtremAppState } from '../../state';
import * as navigationPanelActions from '../navigation-panel-actions';

describe('Navigation panel actions', () => {
    const screenId = 'TestPage';
    let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;
    let state: XtremAppState;

    beforeEach(() => {
        state = getMockState();
        mockStore = getMockStore(state);
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('fetchNavigationPanelData', () => {
        let selectRecordMock: jest.MockInstance<any, any>;

        beforeEach(() => {
            jest.spyOn(graphqlService, 'fetchNavigationPanelData').mockResolvedValue([
                {
                    _id: '6',
                    rightField: 3.23,
                    field1: 'aaaaa',
                    field2: 'ddddd',
                    field3: {
                        code: 'code1',
                        value: 'value1',
                        _id: '5',
                    },
                },
            ]);

            selectRecordMock = jest.fn();

            jest.spyOn(routerService, 'getRouter').mockReturnValue({
                selectRecord: selectRecordMock,
            } as any);
        });

        it('Dispatches the correct action and payload', async () => {
            const expectedActions: xtremRedux.AppAction[] = [
                {
                    type: ActionType.SetNavigationPanelIsRefreshing,
                    value: { isRefreshing: true, screenId: 'TestPage' },
                },
                {
                    type: ActionType.SetNavigationPanelValue,
                    value: {
                        value: expect.objectContaining({
                            initialValues: [
                                {
                                    _id: '6',
                                    field1: 'aaaaa',
                                    field2: 'ddddd',
                                    field3: {
                                        _id: '5',
                                        code: 'code1',
                                        value: 'value1',
                                    },
                                    rightField: 3.23,
                                },
                            ],
                        }),
                        screenId,
                    },
                },
            ];

            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            const pageProperties: PageProperties<Page> = ((state.screenDefinitions[screenId].metadata
                .uiComponentProperties[screenId] as PageProperties<Page>) = {});
            pageProperties.navigationPanel = {
                listItem: {
                    title: {
                        type: FieldKey.Text,
                        properties: { bind: 'field1' },
                        defaultUiProperties: { bind: 'field1' },
                    },
                    titleRight: {
                        type: FieldKey.Numeric,
                        properties: { bind: 'rightField' },
                        defaultUiProperties: { bind: 'rightField' },
                    },
                    line2: {
                        type: FieldKey.Text,
                        properties: { bind: 'field2' },
                        defaultUiProperties: { bind: 'field2' },
                    },
                    line3: {
                        type: FieldKey.Reference,
                        properties: { bind: 'field3', valueField: 'valueField' },
                        defaultUiProperties: { bind: 'field3' },
                    },
                },
            };
            pageProperties.node = '@sage/xtrem-sales/SalesOrder';
            const tableProperties = await createNavigationTableProperties(
                screenId,
                '@sage/xtrem-sales/SalesOrder',
                state.screenDefinitions[screenId] as PageDefinition,
                {},
                {},
                state.path || undefined,
            );
            state.screenDefinitions[screenId].metadata.uiComponentProperties[navigationPanelId] = tableProperties;
            state.screenDefinitions[screenId].navigationPanel = {
                isHeaderHidden: false,
                isOpened: true,
                isHidden: false,
                isRefreshing: false,
                value: createNavigationPanelValue(screenId, tableProperties, [], {}, 'en-US'),
            };
            mockStore = getMockStore(state);

            await navigationPanelActions.refreshNavigationPanel(screenId)(mockStore.dispatch, mockStore.getState);

            await waitFor(() => {
                expect(mockStore.getActions()).toEqual(expectedActions);
            });
        });
    });

    describe('setNavigationPanelIsOpened', () => {
        it('Should dispatch SetNavigationPanelIsOpened action', async () => {
            await navigationPanelActions.setNavigationPanelIsOpened(false, 'pageId')(mockStore.dispatch);
            const expected = [
                {
                    type: ActionType.SetNavigationPanelIsOpened,
                    value: {
                        isOpened: false,
                        screenId: 'pageId',
                    },
                },
            ];
            expect(mockStore.getActions()).toEqual(expected);
        });
    });

    describe('setNavigationPanelIsHeaderHidden', () => {
        it('Should dispatch setNavigationPanelIsHeaderHidden action', async () => {
            await navigationPanelActions.setNavigationPanelIsHeaderHidden(false, 'pageId')(mockStore.dispatch);
            const expected = [
                {
                    type: ActionType.SetNavigationPanelIsHeaderHidden,
                    value: {
                        isHeaderHidden: false,
                        screenId: 'pageId',
                    },
                },
            ];
            expect(mockStore.getActions()).toEqual(expected);
        });
    });
});
