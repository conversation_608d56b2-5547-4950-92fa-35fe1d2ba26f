import type { Dict } from '@sage/xtrem-shared';
import { addNodeTypes } from '../node-type-actions';
import type { FormattedNodeDetails } from '../../../service/metadata-types';
import { ActionType } from '../..';
import { GraphQLKind } from '../../../types';

const typeInfoFixture: Dict<FormattedNodeDetails> = {
    AnyNode: {
        title: 'AnyNode',
        name: 'AnyNode',
        packageName: '@sage/xtrem-test',
        properties: {
            _id: { kind: GraphQLKind.Scalar, type: 'Id' },
            reference: { kind: 'OBJECT', type: 'AnotherType' },
            checkbox: { kind: GraphQLKind.Scalar, type: 'Boolean' },
            date: { kind: GraphQLKind.Scalar, type: 'Date' },
            label: { kind: GraphQLKind.Scalar, type: 'String' },
            select: { kind: GraphQLKind.Scalar, type: 'String' },
            text: { kind: GraphQLKind.Scalar, type: 'String' },
            link: { kind: GraphQLKind.Scalar, type: 'String' },
            numeric: { kind: GraphQLKind.Scalar, type: 'Float' },
            progress: { kind: GraphQLKind.Scalar, type: 'Int' },
        },
        mutations: {},
    },
    AnotherType: {
        title: 'AnyNode',
        name: 'AnyNode',
        packageName: '@sage/xtrem-test',
        properties: {
            _id: { kind: GraphQLKind.Scalar, type: 'Id' },
            status: { kind: GraphQLKind.Scalar, type: 'String' },
            title: { kind: GraphQLKind.Scalar, type: 'String' },
            code: { kind: GraphQLKind.Scalar, type: 'String' },
        },
        mutations: {},
    },
};

describe('Node Type Actions', () => {
    it('should return the action', () => {
        const action = addNodeTypes(typeInfoFixture);
        expect(action.type).toEqual(ActionType.AddNodeTypes);
        expect(action.value).toEqual(typeInfoFixture);
    });
});
