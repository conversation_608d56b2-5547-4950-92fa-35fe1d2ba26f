import { AbstractWidget } from '../../../dashboard/widgets/abstract-widget';
import { indicatorTile } from '../../../dashboard/widgets/indicator-tile-widget-decorator';

indicatorTile<FakeWidget>({
    title: 'Fake Widget',
    description: 'A fake test dummy',
    color: '#005500',
    icon: 'sync',
    value() {
        return '5.0.0';
    },
    getQuery() {
        return {
            xtremSystem: {
                sysPackVersion: {
                    query: {
                        __args: { filter: "{name:'@sage/xtrem-system'}" },
                        edges: {
                            node: {
                                version: true,
                            },
                        },
                    },
                },
            },
        };
    },
});
export class FakeWidget extends AbstractWidget {}
