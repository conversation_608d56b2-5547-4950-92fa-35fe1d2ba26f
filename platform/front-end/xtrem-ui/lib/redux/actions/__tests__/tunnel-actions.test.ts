import {
    addFieldToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../__tests__/test-helpers';
import { <PERSON>Key } from '@sage/xtrem-shared';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as actions from '..';
import { PageControlObject } from '../../../component/control-objects';
import type { ReferenceDecoratorProperties } from '../../../component/decorators';
import * as dialogService from '../../../service/dialog-service';
import * as dirtyStateService from '../../../service/dirty-state-service';
import * as grapqhlService from '../../../service/graphql-service';
import type { PageDefinition } from '../../../service/page-definition';
import { NEW_PAGE, QUERY_PARAM_TUNNEL_SEGMENTS, SHOULD_REFRESH_DIALOG_RESULT } from '../../../utils/constants';
import type { XtremAppState } from '../../state';
import type { TunnelSegment } from '../tunnel-actions';
import * as tunnelActions from '../tunnel-actions';

describe('Tunnel actions', () => {
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let mockState: XtremAppState;

    let pageDialogMock: jest.MockInstance<Promise<unknown>, any>;

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('opening a tunnel', () => {
        const screenId = 'TestPage';
        const fieldId = 'testField';
        const fieldNoTunnelId = 'testFieldNoTunnel';
        const fieldInvalidTunnelId = 'testFieldInvalidTunnel';
        const fieldSamePageTunnelId = 'testFieldInvalidTunnel';

        const mockReferenceFieldProperties: ReferenceDecoratorProperties = {
            node: '@sage/xtrem-test/TestNode',
            valueField: 'test',
            tunnelPage: '@sage/xtrem-test/TunnelPage',
            title: 'A reference field with a tunnel',
        };
        const mockReferenceFieldNoTunnelProperties: ReferenceDecoratorProperties = {
            node: '@sage/xtrem-test/TestNode',
            valueField: 'test',
        };

        const mockReferenceFieldInvalidTunnelProperties: ReferenceDecoratorProperties = {
            node: '@sage/xtrem-test/TestNode',
            valueField: 'test',
            tunnelPage: '@sage/TunnelPage',
        };
        const mockReferenceFieldSamePageTunnelProperties: ReferenceDecoratorProperties = {
            node: '@sage/xtrem-test/TestNode',
            valueField: 'test',
            tunnelPage: '@sage/xtrem-test/TestPage',
        };

        const mockReferenceFieldValue = {
            _id: '1232',
            test: 'Hi there',
        };

        beforeEach(() => {
            pageDialogMock = jest.spyOn(dialogService, 'openPageDialog').mockResolvedValue({});
            jest.spyOn(dialogService, 'messageDialog').mockResolvedValue({});
            jest.spyOn(grapqhlService, 'fetchReferenceFieldDataById').mockResolvedValue({
                _id: '123',
                name: 'updatedValue',
            });

            mockState = getMockState({});
            mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState<FieldKey.Reference, any>(
                FieldKey.Reference,
                mockState,
                screenId,
                fieldId,
                mockReferenceFieldProperties,
                mockReferenceFieldValue,
            );
            addFieldToState<FieldKey.Reference, any>(
                FieldKey.Reference,
                mockState,
                screenId,
                fieldNoTunnelId,
                mockReferenceFieldNoTunnelProperties,
                mockReferenceFieldValue,
            );
            addFieldToState<FieldKey.Reference, any>(
                FieldKey.Reference,
                mockState,
                screenId,
                fieldInvalidTunnelId,
                mockReferenceFieldInvalidTunnelProperties,
                mockReferenceFieldValue,
            );
            addFieldToState<FieldKey.Reference, any>(
                FieldKey.Reference,
                mockState,
                screenId,
                fieldSamePageTunnelId,
                mockReferenceFieldSamePageTunnelProperties,
                mockReferenceFieldValue,
            );
            mockStore = getMockStore(mockState);
        });

        it('should throw an error if the field does not have a tunnel page defined', async () => {
            await expect(async () =>
                tunnelActions.openTunnel({
                    screenId,
                    elementId: fieldNoTunnelId,
                    fieldProperties: mockReferenceFieldNoTunnelProperties,
                    value: mockReferenceFieldValue,
                })(mockStore.dispatch, mockStore.getState),
            ).rejects.toThrow('No tunnel page is defined.');
        });

        it('should throw an error if the tunnel link is invalid', async () => {
            await expect(async () =>
                tunnelActions.openTunnel({
                    screenId,
                    elementId: fieldInvalidTunnelId,
                    fieldProperties: mockReferenceFieldInvalidTunnelProperties,
                    value: mockReferenceFieldValue,
                })(mockStore.dispatch, mockStore.getState),
            ).rejects.toThrow('The tunnel page format is invalid.');
        });

        it('should open the page dialog for the tunnel and construct the breadcrumbs', async () => {
            expect(dialogService.openPageDialog).not.toHaveBeenCalled();
            await tunnelActions.openTunnel({
                screenId,
                elementId: fieldId,
                fieldProperties: mockReferenceFieldProperties,
                value: mockReferenceFieldValue,
            })(mockStore.dispatch, mockStore.getState);

            expect(dialogService.openPageDialog).toHaveBeenCalledWith(
                '@sage/xtrem-test/TunnelPage',
                {
                    [QUERY_PARAM_TUNNEL_SEGMENTS]: [
                        { label: 'test', screenId },
                        { label: 'A reference field with a tunnel - Hi there', screenId: 'TunnelPage' },
                    ],
                    _id: '1232',
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        });

        it('should open the page dialog for the tunnel even if the field has no value and construct the breadcrumbs using the new placeholder', async () => {
            expect(dialogService.openPageDialog).not.toHaveBeenCalled();
            await tunnelActions.openTunnel({
                screenId,
                fieldProperties: mockReferenceFieldProperties,
                elementId: fieldId,
                value: null,
            })(mockStore.dispatch, mockStore.getState);

            expect(dialogService.openPageDialog).toHaveBeenCalledWith(
                '@sage/xtrem-test/TunnelPage',
                {
                    [QUERY_PARAM_TUNNEL_SEGMENTS]: [
                        { label: 'test', screenId },
                        { label: 'A reference field with a tunnel - New', screenId: 'TunnelPage' },
                    ],
                    _id: NEW_PAGE,
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        });

        it('should display a message dialog if the user would open a circular tunnel', async () => {
            expect(dialogService.messageDialog).not.toHaveBeenCalled();
            expect(dialogService.openPageDialog).not.toHaveBeenCalled();

            await tunnelActions.openTunnel({
                screenId,
                fieldProperties: mockReferenceFieldSamePageTunnelProperties,
                value: mockReferenceFieldValue,
                elementId: fieldSamePageTunnelId,
            })(mockStore.dispatch, mockStore.getState);

            expect(dialogService.openPageDialog).not.toHaveBeenCalled();
            expect(dialogService.messageDialog).toHaveBeenCalledWith(
                screenId,
                'warn',
                'This page is already open.',
                'This page is already used below, please close that one first to be able to open this link, or open it on a new tab.',
            );
        });

        it('should fetch the updated element details from the server if the tunnel successfully returns an item', async () => {
            pageDialogMock.mockResolvedValue({
                [SHOULD_REFRESH_DIALOG_RESULT]: true,
                _id: '123',
            });
            expect(dialogService.messageDialog).not.toHaveBeenCalled();
            expect(dialogService.openPageDialog).not.toHaveBeenCalled();

            const tunnelResult = await tunnelActions.openTunnel({
                screenId,
                elementId: fieldId,
                fieldProperties: mockReferenceFieldProperties,
                value: mockReferenceFieldValue,
            })(mockStore.dispatch, mockStore.getState);

            expect(dialogService.openPageDialog).toHaveBeenCalledWith(
                '@sage/xtrem-test/TunnelPage',
                {
                    [QUERY_PARAM_TUNNEL_SEGMENTS]: [
                        { label: 'test', screenId: 'TestPage' },
                        { label: 'A reference field with a tunnel - Hi there', screenId: 'TunnelPage' },
                    ],
                    _id: '1232',
                },
                { fullScreen: true, resolveOnCancel: true },
            );
            expect(grapqhlService.fetchReferenceFieldDataById).toHaveBeenCalledWith({
                _id: '123',
                contextNode: undefined,
                elementId: fieldId,
                fieldProperties: {
                    _controlObjectType: 'Reference',
                    ...mockReferenceFieldProperties,
                },
                level: undefined,
                parentElementId: undefined,
                recordContext: undefined,
                screenId,
                valueField: 'test',
            });
            expect(tunnelResult).toEqual({
                _id: '123',
                name: 'updatedValue',
            });
        });
        it('should not try to fetch the updated element details from the server if the tunnel was cancelled', async () => {
            pageDialogMock.mockResolvedValue({});
            expect(dialogService.messageDialog).not.toHaveBeenCalled();
            expect(dialogService.openPageDialog).not.toHaveBeenCalled();

            const tunnelResult = await tunnelActions.openTunnel({
                screenId,
                elementId: fieldId,
                fieldProperties: mockReferenceFieldProperties,
                value: mockReferenceFieldValue,
            })(mockStore.dispatch, mockStore.getState);

            expect(dialogService.openPageDialog).toHaveBeenCalledWith(
                '@sage/xtrem-test/TunnelPage',
                {
                    [QUERY_PARAM_TUNNEL_SEGMENTS]: [
                        { label: 'test', screenId: 'TestPage' },
                        { label: 'A reference field with a tunnel - Hi there', screenId: 'TunnelPage' },
                    ],
                    _id: '1232',
                },
                { fullScreen: true, resolveOnCancel: true },
            );
            expect(grapqhlService.fetchReferenceFieldDataById).not.toHaveBeenCalled();
            expect(tunnelResult).toEqual(null);
        });
    });

    describe('closing tunnels using the breadcrumb links', () => {
        const mainPageId = 'MainPage';
        const tunnelPageA = 'TunnelPageA';
        const tunnelPageB = 'TunnelPageB';
        const tunnelPageC = 'TunnelPageC';
        const tunnelPageD = 'TunnelPageD';

        beforeEach(() => {
            jest.spyOn(dirtyStateService, 'openDirtyPageConfirmationDialog').mockResolvedValue({});
            mockState = getMockState({});
            const mainPage = getMockPageDefinition(mainPageId);
            mainPage.metadata.controlObjects[mainPageId] = new PageControlObject({
                screenId: mainPageId,
                dispatchPageValidation: jest.fn(),
                getFocussedField: jest.fn(),
                layout: null,
                getUiComponentProperties: jest.fn(),
                setUiComponentProperties: jest.fn(),
                getValidationState: jest.fn(),
                insertBefore: jest.fn(),
            });
            mockState.screenDefinitions[mainPageId] = mainPage;

            const pageA = getMockPageDefinition(tunnelPageA);
            const aControlObject = new PageControlObject({
                screenId: tunnelPageA,
                dispatchPageValidation: jest.fn(),
                getFocussedField: jest.fn(),
                layout: null,
                getUiComponentProperties: jest.fn(),
                setUiComponentProperties: jest.fn(),
                getValidationState: jest.fn(),
                insertBefore: jest.fn(),
            });
            pageA.metadata.controlObjects[tunnelPageA] = aControlObject;
            mockState.screenDefinitions[tunnelPageA] = pageA;

            const pageB = getMockPageDefinition(tunnelPageB);
            const bControlObject = new PageControlObject({
                screenId: tunnelPageB,
                dispatchPageValidation: jest.fn(),
                getFocussedField: jest.fn(),
                layout: null,
                getUiComponentProperties: jest.fn(),
                setUiComponentProperties: jest.fn(),
                getValidationState: jest.fn(),
                insertBefore: jest.fn(),
            });
            pageB.metadata.controlObjects[tunnelPageB] = bControlObject;
            mockState.screenDefinitions[tunnelPageB] = pageB;

            const pageC = getMockPageDefinition(tunnelPageC);
            const cControlObject = new PageControlObject({
                screenId: tunnelPageC,
                dispatchPageValidation: jest.fn(),
                getFocussedField: jest.fn(),
                layout: null,
                getUiComponentProperties: jest.fn(),
                setUiComponentProperties: jest.fn(),
                getValidationState: jest.fn(),
                insertBefore: jest.fn(),
            });
            pageC.metadata.controlObjects[tunnelPageC] = cControlObject;
            mockState.screenDefinitions[tunnelPageC] = pageC;

            const pageD = getMockPageDefinition(tunnelPageD);
            const dControlObject = new PageControlObject({
                screenId: tunnelPageD,
                dispatchPageValidation: jest.fn(),
                getFocussedField: jest.fn(),
                layout: null,
                getUiComponentProperties: jest.fn(),
                setUiComponentProperties: jest.fn(),
                getValidationState: jest.fn(),
                insertBefore: jest.fn(),
            });
            pageD.metadata.controlObjects[tunnelPageD] = dControlObject;
            pageD.queryParameters[QUERY_PARAM_TUNNEL_SEGMENTS] = [
                { label: 'Main Page', screenId: mainPageId },
                { label: 'Page A', screenId: tunnelPageA },
                { label: 'Page B', screenId: tunnelPageB },
                { label: 'Page C', screenId: tunnelPageC },
                { label: 'Page D', screenId: tunnelPageD },
            ] as Array<TunnelSegment> as any;
            mockState.screenDefinitions[tunnelPageD] = pageD;

            mockState.activeDialogs = {
                '1': {
                    type: 'page',
                    buttons: {},
                    dialogControl: {} as any,
                    dialogId: 1,
                    screenId: tunnelPageA,
                    level: 'info',
                    isSticker: false,
                    options: {},
                    content: aControlObject,
                },
                '2': {
                    type: 'page',
                    buttons: {},
                    dialogControl: {} as any,
                    dialogId: 2,
                    screenId: tunnelPageB,
                    level: 'info',
                    isSticker: false,
                    options: {},
                    content: bControlObject,
                },
                '3': {
                    type: 'page',
                    buttons: {},
                    dialogControl: {} as any,
                    dialogId: 3,
                    screenId: tunnelPageC,
                    level: 'info',
                    isSticker: false,
                    options: {},
                    content: cControlObject,
                },
                '4': {
                    type: 'page',
                    buttons: {},
                    dialogControl: {} as any,
                    dialogId: 4,
                    screenId: tunnelPageD,
                    level: 'info',
                    isSticker: false,
                    options: {},
                    content: dControlObject,
                },
            };
            mockStore = getMockStore(mockState);
        });

        afterEach(() => {
            applyActionMocks();
        });

        it('should close all tunnel pages when the user clicks the link of the main page', async () => {
            expect(actions.closeDialog).not.toHaveBeenCalled();
            await tunnelActions.closeTunnelByBreadcrumbLink(mainPageId, tunnelPageD)(
                mockStore.dispatch,
                mockStore.getState,
            );
            expect(actions.closeDialog).toHaveBeenCalledTimes(4);
            expect(actions.closeDialog).toHaveBeenNthCalledWith(1, 4);
            expect(actions.closeDialog).toHaveBeenNthCalledWith(2, 3);
            expect(actions.closeDialog).toHaveBeenNthCalledWith(3, 2);
            expect(actions.closeDialog).toHaveBeenNthCalledWith(4, 1);

            expect(dirtyStateService.openDirtyPageConfirmationDialog).not.toHaveBeenCalled();
        });

        it('should intermediate tunnel pages when the user clicks the link of the main page', async () => {
            expect(actions.closeDialog).not.toHaveBeenCalled();
            await tunnelActions.closeTunnelByBreadcrumbLink(tunnelPageB, tunnelPageD)(
                mockStore.dispatch,
                mockStore.getState,
            );
            expect(actions.closeDialog).toHaveBeenCalledTimes(2);
            expect(actions.closeDialog).toHaveBeenNthCalledWith(1, 4);
            expect(actions.closeDialog).toHaveBeenNthCalledWith(2, 3);

            expect(dirtyStateService.openDirtyPageConfirmationDialog).not.toHaveBeenCalled();
        });

        it('should display the dirty page confirmation if the top tunnel page is dirty', async () => {
            (mockState.screenDefinitions[tunnelPageD] as PageDefinition).dirtyStates = {
                [tunnelPageD]: true,
            };
            mockStore = getMockStore(mockState);
            expect(dirtyStateService.openDirtyPageConfirmationDialog).not.toHaveBeenCalled();

            await tunnelActions.closeTunnelByBreadcrumbLink(mainPageId, tunnelPageD)(
                mockStore.dispatch,
                mockStore.getState,
            );
            expect(dirtyStateService.openDirtyPageConfirmationDialog).toHaveBeenCalled();
        });
        it('should display the dirty page confirmation if the top tunnel page is dirty', async () => {
            (mockState.screenDefinitions[tunnelPageD] as PageDefinition).dirtyStates = {
                [tunnelPageD]: true,
            };
            mockStore = getMockStore(mockState);
            expect(dirtyStateService.openDirtyPageConfirmationDialog).not.toHaveBeenCalled();

            await tunnelActions.closeTunnelByBreadcrumbLink(mainPageId, tunnelPageD)(
                mockStore.dispatch,
                mockStore.getState,
            );
            expect(dirtyStateService.openDirtyPageConfirmationDialog).toHaveBeenCalled();
        });
    });
});
