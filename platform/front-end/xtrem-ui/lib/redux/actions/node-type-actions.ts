import type { Dict } from '@sage/xtrem-shared';
import type { AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { FormattedNodeDetails } from '../../service/metadata-types';
import { fetchNodeDetails } from '../../service/metadata-service';
import { addSchemaInfo } from './common-actions';

export const addNodeTypes = (
    types: Dict<any>,
): {
    type: ActionType.AddNodeTypes;
    value: Dict<FormattedNodeDetails>;
} => {
    return { type: ActionType.AddNodeTypes, value: types };
};

export const fetchNodeTypes =
    (nodeName: string[], locale: string) =>
    async (dispatch: AppThunkDispatch): Promise<void> => {
        const result = await fetchNodeDetails(nodeName);
        dispatch(addSchemaInfo(result.nodeTypes, result.dataTypes, result.enumTypes, locale));
    };
