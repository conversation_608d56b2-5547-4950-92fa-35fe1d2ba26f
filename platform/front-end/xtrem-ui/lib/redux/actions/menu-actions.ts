import type { AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { Menu, XtremAppState } from '../state';

export const addMenuItem =
    (menuItem: Menu) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        await dispatch({ type: ActionType.AddMenuItem, value: menuItem });

        const updatedState = getState();
        updatedState.applicationContext?.updateMenu(updatedState.menuItems);
    };

export const updateMenuItem =
    (menuItemId: string, badgeContent?: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        await dispatch({ type: ActionType.UpdateMenuItem, value: { id: menuItemId, badgeContent } });

        const updatedState = getState();
        updatedState.applicationContext?.updateMenu(updatedState.menuItems);
    };

export const removeMenuItem =
    (menuItemId: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        await dispatch({ type: ActionType.RemoveMenuItem, value: menuItemId });

        const updatedState = getState();
        updatedState.applicationContext?.updateMenu(updatedState.menuItems);
    };
