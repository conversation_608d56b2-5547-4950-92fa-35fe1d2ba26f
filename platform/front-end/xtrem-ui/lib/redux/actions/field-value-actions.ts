import type { Changeable as fieldValueActions } from '../../component/field/traits';
import { fetchCollectionData, fetchDefaultValues } from '../../service/graphql-service';
import type { PageDefinition } from '../../service/page-definition';
import type { XtremUiPlugin } from '../../service/plugin-service';
import type { ScreenBaseDefinition } from '../../service/screen-base-definition';
import { getScreenElement } from '../../service/screen-base-definition';
import { triggerScreenEvent } from '../../utils/events';
import type { AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { XtremAppState } from '../state';
import {
    getPageDefinitionFromState,
    getPagePropertiesFromPageDefinition,
    getPagePropertiesFromState,
    hasAnyDirtyScreenDefinitions,
    isScreenDefinitionDirty,
} from '../../utils/state-utils';
import { formatScreenValues } from '../../service/value-formatter-service';
import { getStore } from '../store';
import { notifyConsumerAboutDirtyStatus, onPageDirtyChange } from '../../service/dirty-state-service';
import { type Dict, objectKeys } from '@sage/xtrem-shared';
import type { Page } from '../../service/page';
import type { PageDecoratorProperties } from '../../component/decorator-properties';
import type { NestedField, NestedFieldTypes } from '../../component/nested-fields';
import { setComponentLoading } from './common-actions';
import { xtremConsole } from '../../utils/console';
import type { QueryArguments } from '../../service/graphql-utils';
import type { ScreenBase } from '../../service/screen-base';
import { NEW_PAGE } from '../../utils/constants';
import type { PropertyValueType } from '../../component/field/reference/reference-types';
import { FieldKey } from '../../component/types';
import type { FormattedNodeDetails } from '../../service/metadata-types';
import type { ReadonlyFieldControlObject } from '../../component/readonly-field-control-object';

export const setFieldValue =
    (screenId: string, elementId: string, fieldValue: any, isOrganicChange = false) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        let state = getState();
        let screenDefinition = state.screenDefinitions[screenId];

        const originalPageDirtyState = isScreenDefinitionDirty(screenDefinition);
        const originalAppDirtyState = hasAnyDirtyScreenDefinitions(state);

        const page = getScreenElement(screenDefinition) as Page;
        if (page.$.isTransactionInProgress()) {
            /**
             * If the dirty event listeners set properties or values, it can cause a conflict with the SetValueField below. The internal set value field action
             * does not use the transactions, so it can happen that the values are overwritten by the transaction which is committed after the values are set.
             *  */
            await page.$.commitValueAndPropertyChanges();
        }

        // If it is a user initiated, organic change and the application is currently clean, we tell the consumer listener that the application has just become dirty.
        if (
            isOrganicChange &&
            !originalAppDirtyState &&
            !(screenDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<any>).skipDirtyCheck
        ) {
            notifyConsumerAboutDirtyStatus(state, true);
        }

        dispatch({
            type: ActionType.SetFieldValue,
            value: {
                screenId,
                elementId,
                fieldValue,
                isOrganicChange,
            },
        });

        if (isOrganicChange) {
            state = getState();
            state.applicationContext?.onTelemetryEvent?.(`fieldValueModified-${elementId}`, { screenId, elementId });
            screenDefinition = state.screenDefinitions[screenId];
            const fieldProperties = screenDefinition.metadata.uiComponentProperties[
                elementId
            ] as fieldValueActions<any>;
            await applyDefaultValues({
                dispatch,
                screenDefinition,
                fieldProperties,
                plugins: state.plugins,
                nodeTypes: state.nodeTypes,
            });
        }
        // If it is a user initiated, organic change and the page is currently clean, we tell the screen listener that the screen has just become dirty.
        if (isOrganicChange && !originalPageDirtyState) {
            onPageDirtyChange(state, screenId, true);
            triggerScreenEvent(screenId, 'onDirtyStateUpdated', true);
        }
    };

export interface ApplyDefaultValuesArgs {
    dispatch: AppThunkDispatch;
    screenDefinition: ScreenBaseDefinition<any>;
    fieldProperties?: fieldValueActions<any>;
    plugins: Dict<XtremUiPlugin>;
    nodeTypes: Dict<FormattedNodeDetails>;
    requestedFieldIds?: string[];
    skipDispatch?: boolean;
}

export const applyDefaultValues = async ({
    dispatch,
    screenDefinition,
    fieldProperties,
    plugins,
    nodeTypes,
    requestedFieldIds,
    skipDispatch = false,
}: ApplyDefaultValuesArgs): Promise<any> => {
    const screenProperties = getPagePropertiesFromPageDefinition(screenDefinition as PageDefinition);
    const queryParameters = (screenDefinition as PageDefinition)?.queryParameters;

    // This block will check for tables with phantom rows and apply default values to it.
    if (
        (requestedFieldIds || fieldProperties?.fetchesDefaults) &&
        screenProperties.node &&
        !screenProperties.isTransient &&
        screenDefinition.type === 'page'
    ) {
        // Function that should retrieve tables with phantomRow from uiComponentProperties
        const getPhantomRowTableIds = (uiComponentProperties: Dict<any>): string[] => {
            return objectKeys(uiComponentProperties).filter(
                value =>
                    uiComponentProperties[value]._controlObjectType === FieldKey.Table &&
                    !uiComponentProperties[value].isTransient &&
                    uiComponentProperties[value].canAddNewLine,
            );
        };
        getPhantomRowTableIds(screenDefinition.metadata.uiComponentProperties).forEach(tableId => {
            screenDefinition.values[tableId].resetPhantomRowToDefault();
        });
    }

    // This block will be executed only if the field has fetchesDefaults set to true AND is a new record after that will update other non dirty fields.
    if (
        (requestedFieldIds || fieldProperties?.fetchesDefaults) &&
        screenProperties.node &&
        !screenProperties.isTransient &&
        screenDefinition.type === 'page' &&
        (requestedFieldIds || !queryParameters?._id || queryParameters?._id === NEW_PAGE) &&
        !queryParameters.__duplicate // We don't want to apply default values to duplicate pages
    ) {
        const defaultValues = await fetchDefaultValues(
            screenDefinition,
            String(screenProperties.node),
            requestedFieldIds,
            false,
            nodeTypes,
        );
        dispatchDefaultValuesSet({ screenDefinition, plugins, nodeTypes, defaultValues, dispatch, skipDispatch });
        return defaultValues;
    }

    return {};
};
export interface DispatchDefaultValuesSetArgs {
    screenDefinition: ScreenBaseDefinition<any>;
    plugins: Dict<XtremUiPlugin>;
    nodeTypes: Dict<FormattedNodeDetails>;
    defaultValues: Dict<any>;
    dispatch?: AppThunkDispatch;
    skipDispatch?: boolean;
}
export const dispatchDefaultValuesSet = ({
    screenDefinition,
    plugins,
    nodeTypes,
    defaultValues,
    dispatch = getStore().dispatch,
    skipDispatch = false,
}: DispatchDefaultValuesSetArgs): any => {
    const keys: string[] = objectKeys(defaultValues);
    const screenId = screenDefinition.metadata.screenId;

    const formattedDefaultValues = formatScreenValues({
        screenId,
        controlObjects: screenDefinition.metadata.controlObjects as Dict<ReadonlyFieldControlObject<any, any, any>>,
        plugins,
        nodeTypes,
        values: defaultValues,
        parentNode: undefined,
        onlyElementIds: keys,
        userSettings: screenDefinition.userSettings,
    });

    delete formattedDefaultValues._id;

    if (!skipDispatch) {
        dispatch({
            type: ActionType.SetValues,
            value: {
                screenId: screenDefinition.metadata.screenId,
                values: { ...screenDefinition.values, ...formattedDefaultValues },
                preserveValidationState: true,
            },
        });
    }

    return formattedDefaultValues;
};

export const loadCollectionData =
    (
        screenId: string,
        elementId: string,
        nestedFields: NestedField<ScreenBase, NestedFieldTypes>[],
        queryArguments: QueryArguments,
        bind?: PropertyValueType,
        keepAllRecords = false,
    ) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        // eslint-disable-next-line no-async-promise-executor
        return new Promise<any>(async (res, rej) => {
            try {
                const state = getState();
                const pageDefinition = getPageDefinitionFromState(screenId, state);
                const pageProperties = getPagePropertiesFromState(screenId, state);
                const rootNode = pageProperties.node!;
                const selectedRecordId = pageDefinition.selectedRecordId || '';
                dispatch(setComponentLoading(screenId, elementId, true));
                const result = await fetchCollectionData({
                    screenDefinition: pageDefinition,
                    rootNode,
                    rootNodeId: selectedRecordId,
                    elementId,
                    nestedFields,
                    queryArguments,
                    bind,
                });
                dispatch(setComponentLoading(screenId, elementId, false));
                const currentValue = pageDefinition.values[elementId];
                const updatedValue = keepAllRecords
                    ? { ...result, data: currentValue.data.concat(result.data) }
                    : result;
                await dispatch(setFieldValue(screenId, elementId, updatedValue, false));
                res(updatedValue);
            } catch (err) {
                dispatch(setComponentLoading(screenId, elementId, false));
                xtremConsole.error('Error filtering collection items', err);
                rej(err);
            }
        });
    };
