import { get } from 'lodash';
import { getFieldTitle } from '../../component/field/carbon-helpers';
import * as xtremRedux from '..';
import { NEW_PAGE, SHOULD_REFRESH_DIALOG_RESULT, QUERY_PARAM_TUNNEL_SEGMENTS } from '../../utils/constants';
import { getPageDefinitionFromState, isScreenDefinitionDirty } from '../../utils/state-utils';
import * as dialogService from '../../service/dialog-service';
import { convertDeepBindToPathNotNull } from '../../utils/nested-field-utils';
import { localize } from '../../service/i18n-service';
import type { PageControlObject, UiComponentProperties } from '../../component/control-objects';
import { getPageTitlesFromPageDefinition } from '../../utils/page-utils';
import { openDirtyPageConfirmationDialog } from '../../service/dirty-state-service';
import { fetchReferenceFieldDataById } from '../../service/graphql-service';
import type { Dict } from '@sage/xtrem-shared';
import type { NodePropertyType } from '../../types';
import { getReferenceTunnelLinkId } from '../../component/field/reference/reference-utils';
import type { HasTunnel, HasValueField } from '../../component/field/traits';

export interface TunnelSegment {
    screenId: string;
    label: string;
}

export interface OpenTunnelArgs {
    screenId: string;
    elementId: string;
    fieldProperties: HasTunnel<any> & HasValueField<any> & UiComponentProperties<any>;
    value: any;
    parentElementId?: string;
    recordContext?: Dict<any>;
    level?: number;
    contextNode?: NodePropertyType;
}

export const openTunnel =
    ({
        screenId,
        elementId,
        fieldProperties,
        value,
        parentElementId,
        recordContext,
        level,
        contextNode,
    }: OpenTunnelArgs) =>
    async (_dispatch: xtremRedux.AppThunkDispatch, getState: () => xtremRedux.XtremAppState): Promise<any> => {
        const { tunnelPage, valueField } = fieldProperties;
        if (!tunnelPage) {
            throw new Error('No tunnel page is defined.');
        }

        const pathParts = tunnelPage.split('/');

        if (pathParts.length < 3) {
            throw new Error('The tunnel page format is invalid.');
        }

        const state = getState();

        const tunnelScreenId = pathParts[2];
        if (state.screenDefinitions[tunnelScreenId]) {
            dialogService.messageDialog(
                screenId,
                'warn',
                localize('@sage/xtrem-ui/tunnel-already-open-title', 'This page is already open.'),
                localize(
                    '@sage/xtrem-ui/tunnel-already-open-description',
                    'This page is already used below, please close that one first to be able to open this link, or open it on a new tab.',
                ),
            );
            return null;
        }

        const locale = state.applicationContext?.locale || 'en-US';

        const pageDefinition = getPageDefinitionFromState(screenId, state);
        const fieldTitle = getFieldTitle(screenId, fieldProperties, null);
        const fieldDisplayValue =
            get(value, convertDeepBindToPathNotNull(valueField)) || localize('@sage/xtrem-ui/new', 'New');

        const _id = getReferenceTunnelLinkId({ fieldProperties, value }) || NEW_PAGE;

        const currentTunnelSegments: TunnelSegment[] = (pageDefinition.queryParameters[
            QUERY_PARAM_TUNNEL_SEGMENTS
        ] as unknown as TunnelSegment[]) || [
            { label: getPageTitlesFromPageDefinition(pageDefinition, locale).title, screenId },
        ];

        getState().applicationContext?.onTelemetryEvent?.(`tunnelOpened-${elementId}`, {
            elementId,
            screenId,
            tunnelPage,
            parentElementId,
        });

        const result = await dialogService.openPageDialog<{ [SHOULD_REFRESH_DIALOG_RESULT]: boolean; _id?: string }>(
            tunnelPage,
            {
                _id,
                [QUERY_PARAM_TUNNEL_SEGMENTS]: [
                    ...currentTunnelSegments,
                    {
                        screenId: tunnelScreenId,
                        label: `${fieldTitle} - ${fieldDisplayValue}`,
                    },
                ] as any,
            },
            { fullScreen: true, resolveOnCancel: true },
        );

        if (!result?.[SHOULD_REFRESH_DIALOG_RESULT] || !result?._id) {
            getState().applicationContext?.onTelemetryEvent?.(`tunnelClosed-${elementId}`, {
                elementId,
                screenId,
                tunnelPage,
                parentElementId,
            });
            // eslint-disable-next-line consistent-return
            return null;
        }

        getState().applicationContext?.onTelemetryEvent?.(`tunnelClosedWithModification-${elementId}`, {
            elementId,
            screenId,
            tunnelPage,
            parentElementId,
        });

        if (fieldProperties.tunnelPageIdField || parentElementId) {
            // In this case we won't refresh as the tunnel page is bound to a different node
            return null;
        }

        // eslint-disable-next-line consistent-return
        const queryResult = await fetchReferenceFieldDataById({
            _id: result?._id,
            elementId,
            screenId,
            fieldProperties,
            valueField: fieldProperties.valueField,
            parentElementId,
            contextNode,
            level,
            recordContext,
        });

        if (!queryResult) {
            /**
             * If we don't get a matching result with the ID, it means that the record that was created does not
             * compile with the client's filter decorator or the server's lookup filter decorator restrictions
             */
            await dialogService.messageDialog(
                screenId,
                'warn',
                localize('@sage/xtrem-ui/tunnel-record-not-suitable-title', 'New record not available'),
                localize(
                    '@sage/xtrem-ui/tunnel-record-not-suitable-description',
                    'The record created cannot be used in this context.',
                ),
            );
        }

        return queryResult;
    };

export const closeTunnelByBreadcrumbLink =
    (screenId: string, triggerScreenId: string) =>
    async (dispatch: xtremRedux.AppThunkDispatch, getState: () => xtremRedux.XtremAppState): Promise<void> => {
        const state = getState();
        const pageDefinition = getPageDefinitionFromState(triggerScreenId, state);
        const currentTunnelPages: string[] = (
            (pageDefinition.queryParameters[QUERY_PARAM_TUNNEL_SEGMENTS] as unknown as TunnelSegment[]) || []
        ).map(s => s.screenId);

        const dialogsToClose = currentTunnelPages
            .slice(currentTunnelPages.findIndex(s => s === screenId) + 1)
            .map(s =>
                Object.values(state.activeDialogs).find(
                    d => d.type === 'page' && (d.content as PageControlObject).id === s,
                ),
            )
            // We want to close the dialogs in a top to bottom order
            .reverse();

        const hasAnyDirtyTunnels = !!dialogsToClose.find(
            d => d?.screenId && isScreenDefinitionDirty(state.screenDefinitions[d?.screenId]),
        );

        if (hasAnyDirtyTunnels) {
            try {
                await openDirtyPageConfirmationDialog(currentTunnelPages[0]);
            } catch {
                return;
            }
        }

        // eslint-disable-next-line no-restricted-syntax
        for (const dialogToClose of dialogsToClose) {
            if (dialogToClose) {
                await dispatch(xtremRedux.actions.closeDialog(dialogToClose?.dialogId));
            }
        }
    };
