import { objectKeys, type Dict } from '@sage/xtrem-shared';
import type { SectionDecoratorProperties } from '../../component/decorator-properties';
import { getFieldTitle, getPageSubtitle } from '../../component/field/carbon-helpers';
import { errorDialog } from '../../service/dialog-service';
import { notifyConsumerAboutDirtyStatus } from '../../service/dirty-state-service';
import { fetchDefaultValues, fetchRecordData } from '../../service/graphql-service';
import { destroyScreenCollections } from '../../service/loki';
import type { FormattedNodeDetails } from '../../service/metadata-types';
import type { PageDefinition } from '../../service/page-definition';
import { fetchPageDefinition } from '../../service/screen-loader-service';
import { notifyConsumerOnError } from '../../service/telemetry-service';
import { rollbackTransaction } from '../../service/transactions-service';
import { formatScreenValues } from '../../service/value-formatter-service';
import * as wrapperService from '../../service/wrapper-service';
import { asyncForEach } from '../../utils/async';
import { getAttachmentCount, getAttachmentInformation } from '../../utils/attachment-utils';
import { xtremConsole } from '../../utils/console';
import { ATTACHMENT_SECTION_ID, DASHBOARD_SCREEN_ID, NEW_PAGE } from '../../utils/constants';
import { triggerFieldEvent, triggerScreenEvent } from '../../utils/events';
import { getNonLazySectionsFromScreenDefinition, getPageTitlesFromPageDefinition } from '../../utils/page-utils';
import {
    getNavigationPanelState,
    getNavigationPanelTablePropertiesFromPageDefinition,
    getPageDefinitionFromState,
    getPagePropertiesFromPageDefinition,
    getPagePropertiesFromState,
} from '../../utils/state-utils';
import type { QueryParameters } from '../../utils/types';
import type { AppAction, AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import * as xtremRedux from '../index';
import type { XtremAppState } from '../state';
import {
    addScreenDefinition,
    clearWidgetOptions,
    removeScreenDefinition,
    setFieldProperties,
    setGlobalLoading,
    setPath,
    setScreenDefinitionReady,
} from './common-actions';
import { closeDialog } from './dialog-actions';
import { setNavigationPanelIsOpened } from './navigation-panel-actions';
import { isEmpty } from 'lodash';
import type { ReadonlyFieldControlObject } from '../../component/readonly-field-control-object';

export const addQueryParametersToPath = (path: string, queryParameters: QueryParameters): string => {
    const params = objectKeys(queryParameters).length > 0 ? `/${btoa(JSON.stringify(queryParameters))}` : '';
    return path.split('/').slice(0, 3).join('/') + params;
};

export const getQueryParametersFromPath = (urlPart: string): any => {
    try {
        return JSON.parse(atob(urlPart));
    } catch {
        // If we provide a deep link (e.g. /@sage/em-sales/SalesOrder/SalesOrder:QTEFR0110040) the atob call will fail
        // In order to support accessing resources in a REST fashion, we set the last part of the url pathname as _id by default
        return {
            _id: urlPart,
        };
    }
};

export interface GetPageValuesParams {
    clean?: boolean;
    nodeTypes: Dict<FormattedNodeDetails>;
    recordId: string | null;
    requiredSections?: string[];
    screenDefinition: PageDefinition;
}

export const getPageValues = async ({
    clean = false,
    nodeTypes,
    recordId,
    requiredSections = [],
    screenDefinition,
}: GetPageValuesParams): Promise<any> => {
    const screenId = screenDefinition.metadata.screenId;
    const pageProperties = getPagePropertiesFromPageDefinition(screenDefinition);

    if (pageProperties.isTransient || !pageProperties.node) {
        return {};
    }

    if (recordId && recordId !== NEW_PAGE) {
        return fetchRecordData({
            screenId,
            recordId,
            screenDefinition,
            nodeTypes,
            requiredSections,
            includeFieldsWithNoParent: true,
        });
    }
    return fetchDefaultValues(screenDefinition, String(pageProperties.node), undefined, clean, nodeTypes);
};

export const triggerPostNavigationToasts = (
    state: XtremAppState,
    path: string,
    queryParameters: QueryParameters,
): Promise<void> => {
    if (state.applicationContext?.handleNavigation) {
        state.applicationContext?.handleNavigation(path, queryParameters);
    }

    return notifyConsumerAboutDirtyStatus(state);
};

const getBasePath = (path: string): string => {
    const parts = path.split('/').filter(Boolean);
    return parts.length <= 3 ? path : parts.slice(0, 3).join('/');
};

const handleNavigationPanelError =
    (dispatch: AppThunkDispatch, screenId: string) =>
    (error: any): void => {
        dispatch(setGlobalLoading(false));
        errorDialog(screenId, 'Error', error);
        notifyConsumerOnError(error);
        xtremConsole.error('Error on loading record', error);
    };

const closeCurrentPage =
    () =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();

        objectKeys(state.activeDialogs).forEach(dialogId => {
            dispatch(closeDialog(parseInt(dialogId, 10)));
        });
        const currentScreenDefinitions = state.screenDefinitions;

        const closeScreenDefinition = async (screenId: string): Promise<void> => {
            /**
             * We need to check if the screen definition is still on the state. It could be that the `closeDialog` event closed and removed a page dialog
             * so trying to close it again would throw an exception
             *  */
            if (getState().screenDefinitions[screenId]) {
                await triggerScreenEvent(screenId, 'onClose');
                wrapperService.onBlur();
                dispatch(removeScreenDefinition(screenId));
            }
        };

        const screenIds = objectKeys(state.screenDefinitions).filter(
            screenId => currentScreenDefinitions[screenId].type === 'page' && screenId !== DASHBOARD_SCREEN_ID,
        );

        await asyncForEach(screenIds, closeScreenDefinition);
    };

const updateBackArrowNavigationHistory = (
    dispatch: AppThunkDispatch,
    getState: () => XtremAppState,
    path: string,
    navigatingFromMenu: boolean,
    isBackNavigation: boolean,
): void => {
    const state = getState();

    if (isBackNavigation) {
        dispatch({ type: ActionType.HandleBackNavigation });
        return;
    }

    if (navigatingFromMenu) {
        dispatch({ type: ActionType.ClearHistory });
        return;
    }

    if (state.path && getBasePath(state.path) !== getBasePath(path)) {
        dispatch({ type: ActionType.PushToHistory, value: { path: state.path, queryParams: {} } });
    }

    if (!state.path) {
        dispatch({ type: ActionType.PushToHistory, value: { path: '/', queryParams: {} } });
    }
};

/**
 * Be aware that base64 encoded parameters in the path will override the queryParameters object
 * @param path Be
 * @param queryParameters
 */
export const navigate =
    (path: string, queryParameters: QueryParameters = {}, navigatingFromMenu = false) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        dispatch(setGlobalLoading(true));
        const pathParts = path.split('/');

        if (pathParts.length > 3) {
            // eslint-disable-next-line no-param-reassign
            queryParameters = getQueryParametersFromPath(pathParts[3]);
        }

        const state = getState();

        updateBackArrowNavigationHistory(
            dispatch,
            getState,
            path,
            navigatingFromMenu,
            state.navigation.isBackNavigation,
        );

        await dispatch(closeCurrentPage());

        if (path) {
            try {
                const pageDefinition: PageDefinition | null = await fetchPageDefinition({
                    getState,
                    dispatch,
                    path,
                    isMainPage: true,
                    queryParameters,
                });

                if (objectKeys(queryParameters).length > 0) {
                    // The path needs to be updated here because fetchPageDefinition might modify queryParameters
                    // eslint-disable-next-line no-param-reassign
                    path = addQueryParametersToPath(path, queryParameters);
                }

                dispatch(setPath(path));

                // It has to be called with a "fresh" state so the previous actions are already applied to the state value.
                await triggerPostNavigationToasts(getState(), path, queryParameters);

                if (pageDefinition) {
                    const screenId = pageDefinition.metadata.screenId;

                    dispatch(addScreenDefinition(pageDefinition));

                    const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);
                    const pageTitle = getFieldTitle(screenId, pageProperties, null);
                    state.applicationContext?.onPageTitleChange?.(
                        pageTitle || null,
                        getPageSubtitle(screenId, pageProperties, null),
                    );
                    state.applicationContext?.onPageContextChange?.({
                        nodeName: pageProperties.node ? String(pageProperties.node) : undefined,
                        onInsightCountUpdated: (count: number) =>
                            dispatch({ type: ActionType.SetPageInsightCount, value: { screenId, count } }),
                        recordFilter:
                            queryParameters._id && queryParameters._id !== NEW_PAGE
                                ? { _id: queryParameters._id }
                                : undefined,
                        screenId,
                        screenTitle: pageTitle,
                    });

                    const hasNavigationPanel = !!pageProperties.navigationPanel;
                    if (hasNavigationPanel && !pageDefinition.selectedRecordId) {
                        dispatch(setNavigationPanelIsOpened(true, screenId));
                    } else {
                        dispatch(setNavigationPanelIsOpened(false, screenId));
                    }
                    try {
                        pageDefinition.page.$standardDeleteAction.isDisabled =
                            !queryParameters._id || queryParameters._id === NEW_PAGE;
                        pageDefinition.page.$standardDuplicateAction.isDisabled =
                            !queryParameters._id || queryParameters._id === NEW_PAGE;
                        await triggerScreenEvent(screenId, 'onLoad');
                    } catch (error) {
                        handleNavigationPanelError(dispatch, screenId)(error);
                    }

                    dispatch(setScreenDefinitionReady(screenId));
                }
            } catch (error) {
                errorDialog(pathParts[2], 'Error', error);
                notifyConsumerOnError(error);
                xtremConsole.error('Error on navigating', error);
            }
        } else {
            state.applicationContext?.onPageTitleChange?.(null, null);
            state.applicationContext?.onPageContextChange?.(null);
            dispatch(setPath(path));
        }
        dispatch(setGlobalLoading(false));
    };

export const selectRecord =
    (screenId: string, recordId: string | null, clean = false) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        rollbackTransaction(screenId);
        dispatch(setGlobalLoading(true));
        destroyScreenCollections(screenId, true);
        const state = getState();
        const pageDefinition = getPageDefinitionFromState(screenId, state);
        const pageProperties = getPagePropertiesFromState(screenId, state);

        if (!pageDefinition || !pageProperties) {
            return;
        }

        if (recordId) {
            pageDefinition.queryParameters._id = recordId;
            pageDefinition.selectedRecordId = recordId;
        } else {
            delete pageDefinition.queryParameters._id;
            pageDefinition.selectedRecordId = recordId;
        }
        const path = addQueryParametersToPath(state.path!, pageDefinition.queryParameters);

        dispatch(setPath(path));

        // It has to be called with a "fresh" state so the previous actions are already applied to the state value.
        await triggerPostNavigationToasts(getState(), path, pageDefinition.queryParameters);

        try {
            const nonLazySections = getNonLazySectionsFromScreenDefinition(pageDefinition);
            if (!isEmpty(nonLazySections) && pageDefinition.activeSection) {
                // We load the non lazy sections and the active section
                nonLazySections.push(pageDefinition.activeSection);
            }

            const values = await getPageValues({
                screenDefinition: pageDefinition,
                recordId,
                nodeTypes: state.nodeTypes,
                clean,
                requiredSections: nonLazySections,
            });

            const formattedValues = formatScreenValues({
                controlObjects: pageDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                nodeTypes: state.nodeTypes,
                parentNode: String(pageProperties.node),
                plugins: state.plugins,
                screenId,
                values,
                userSettings: pageDefinition.userSettings,
            });

            dispatch({
                type: ActionType.ResetScreenUiComponentProperties,
                value: { screenId, activeSection: pageDefinition.activeSection },
            });
            dispatch({ type: ActionType.SetValues, value: { screenId, values: formattedValues } });
            dispatch({ type: ActionType.ClearNavigationPanelSearchText, value: { screenId } });
            if (pageDefinition.metadata.rootNode && values) {
                const attachmentInformation = getAttachmentInformation(
                    pageDefinition.metadata.rootNode,
                    state.nodeTypes,
                );
                if (attachmentInformation) {
                    dispatch(
                        setFieldProperties(screenId, ATTACHMENT_SECTION_ID, {
                            ...pageDefinition.metadata.uiComponentProperties[ATTACHMENT_SECTION_ID],
                            indicatorContent: getAttachmentCount(values),
                        } as SectionDecoratorProperties),
                    );
                }
            }

            pageDefinition.page.$standardDeleteAction.isDisabled =
                !pageDefinition.queryParameters._id || pageDefinition.queryParameters._id === NEW_PAGE;
            pageDefinition.page.$standardDuplicateAction.isDisabled =
                !pageDefinition.queryParameters._id || pageDefinition.queryParameters._id === NEW_PAGE;

            await triggerScreenEvent(screenId, 'onLoad');
            if (pageDefinition.activeSection) {
                await triggerFieldEvent(screenId, pageDefinition.activeSection, 'onActive');
                await dispatch({
                    type: ActionType.SetSectionReady,
                    value: {
                        screenId,
                        sectionId: pageDefinition.activeSection,
                    },
                });
            }

            if (!recordId && state.browser.lessThan.m) {
                dispatch(setNavigationPanelIsOpened(true, screenId));
            } else if (!recordId || state.browser.lessThan.m) {
                dispatch(setNavigationPanelIsOpened(!state.browser.lessThan.m, screenId));
            }

            if (!recordId || recordId === NEW_PAGE) {
                dispatch(xtremRedux.actions.set360ViewState(screenId, false));
            }
            const { title, subtitle } = getPageTitlesFromPageDefinition(
                pageDefinition,
                state.applicationContext?.locale || 'en-US',
            );
            state.applicationContext?.onPageTitleChange?.(title, subtitle || null);
            state.applicationContext?.onPageContextChange?.({
                nodeName: pageProperties.node ? String(pageProperties.node) : undefined,
                onInsightCountUpdated: (count: number) =>
                    dispatch({ type: ActionType.SetPageInsightCount, value: { screenId, count } }),
                recordFilter: recordId && recordId !== NEW_PAGE ? { _id: recordId } : undefined,
                screenId,
                screenTitle: title,
            });
        } catch (error) {
            handleNavigationPanelError(dispatch, screenId)(error);
        }
    };

export const findNavPanelRecordLocation = async (
    screenId: string,
    state: XtremAppState,
    offset: 1 | -1,
): Promise<string | null> => {
    const navigationPanelState = getNavigationPanelState(screenId, state);
    const pageDefinition = getPageDefinitionFromState(screenId, state);

    // If the nav panel state OR the current record ID is not found, cannot identify the current position
    if (!navigationPanelState || !pageDefinition || !pageDefinition.queryParameters._id) {
        return null;
    }

    let navigationPanelItems = navigationPanelState.value.getData({
        cleanMetadata: false,
        limit: Number.MAX_SAFE_INTEGER,
    });

    let pageNumber = 0;
    do {
        pageNumber += 1;
        let currentItemIndex = navigationPanelItems.findIndex(
            (e: { _id: string }) => e._id === pageDefinition.queryParameters._id,
        );
        const isCurrentLastItem = currentItemIndex === navigationPanelItems.length - 1;
        const isCurrentNotInTheSet = currentItemIndex === -1;
        // If the current item is the last one in the set OR not found at all, try to fetch next page
        if ((isCurrentLastItem || isCurrentNotInTheSet) && navigationPanelState.value.hasNextPage) {
            // eslint-disable-next-line no-await-in-loop
            const result = await navigationPanelState.value.getPageWithCurrentQueryArguments({
                tableFieldProperties: getNavigationPanelTablePropertiesFromPageDefinition(pageDefinition),
                pageNumber,
                pageSize: 20,
                cursor: [...navigationPanelItems].reverse()[0].__cursor,
                cleanMetadata: false,
            });
            navigationPanelItems = [...navigationPanelItems, ...result];
            currentItemIndex = navigationPanelItems.findIndex(
                (e: { _id: string }) => e._id === pageDefinition.queryParameters._id,
            );
        }
        if (currentItemIndex > -1) {
            return navigationPanelItems[currentItemIndex + offset]._id;
        }
    } while (pageDefinition.queryParameters._id !== NEW_PAGE && navigationPanelState.value.hasNextPage);
    // We try to find the current item until we read the full collection and no more pages are left

    return null;
};

export const selectNextRecord =
    (screenId: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        getState().applicationContext?.onTelemetryEvent?.('pageNextRecordButtonClicked', { screenId });
        try {
            const nextRecordId = await findNavPanelRecordLocation(screenId, getState(), 1);
            if (nextRecordId) {
                await dispatch(selectRecord(screenId, nextRecordId));
            }
        } catch {
            handleNavigationPanelError(dispatch, screenId);
        }
    };

export const selectFirstRecord =
    (screenId: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const pageDefinition = getPageDefinitionFromState(screenId, state);
        const navigationPanelState = getNavigationPanelState(screenId, state);

        if (!pageDefinition || !navigationPanelState) {
            throw new Error(`No navigation panel definition was found for ${screenId}`);
        }
        const navigationPanelItems = navigationPanelState.value.getData({
            cleanMetadata: false,
            limit: Number.MAX_SAFE_INTEGER,
        });
        if (navigationPanelItems.length === 0) {
            throw new Error(`The navigation panel does not have any items for ${screenId}`);
        }

        await dispatch(selectRecord(screenId, navigationPanelItems[0]._id));
    };

export const selectPreviousRecord =
    (screenId: string) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        getState().applicationContext?.onTelemetryEvent?.('pageNextRecordButtonClicked', { screenId });

        try {
            const previousRecordId = await findNavPanelRecordLocation(screenId, getState(), -1);
            if (previousRecordId) {
                await dispatch(selectRecord(screenId, previousRecordId));
            }
        } catch {
            handleNavigationPanelError(dispatch, screenId);
        }
    };

export const goHome =
    () =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        dispatch(clearWidgetOptions());
        await dispatch(closeCurrentPage());

        getState().applicationContext?.handleNavigation('', {});
    };

export const setIdToQueryParameters = (screenId: string, _id: string): AppAction => ({
    type: ActionType.SetQueryParameter,
    value: { parameterName: '_id', screenId, value: _id },
});

export const update360ViewInPath =
    (enable360: boolean) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        const state = getState();
        const path = state.path;
        if (!path) return;

        const pathParts = path.split('/');
        const queryParameters: QueryParameters =
            pathParts.length > 3 ? getQueryParametersFromPath(pathParts[3]) || {} : {};

        if (enable360) {
            queryParameters.view = '360';
        } else {
            delete queryParameters.view;
        }

        const newPath = addQueryParametersToPath(path, queryParameters);
        dispatch(setPath(newPath));
        state.applicationContext?.handleNavigation(newPath, {});
    };

export const goBack =
    () =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const { history } = state.navigation;

        if (history.length > 0) {
            const previousEntry = history[history.length - 1];
            const path = previousEntry.path === '/' ? '' : previousEntry.path;
            dispatch({ type: ActionType.SetBackNavigation, value: true });
            state.applicationContext?.handleNavigation(path, {}, true);
        }
    };
