import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';

export const setFocusPosition = (
    screenId: string,
    elementId: string,
    row?: string,
    nestedField?: string,
): AppAction => {
    return {
        type: ActionType.SetFocusPosition,
        value: { elementId, screenId, row, nestedField },
    };
};

export const emptyFocusPosition = (): AppAction => {
    return {
        type: ActionType.SetFocusPosition,
        value: null,
    };
};
