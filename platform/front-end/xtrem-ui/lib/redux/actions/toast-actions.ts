import type { Toast } from '../../service/toast-service';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';

export const showToast = (toast: Toast): AppAction => ({
    type: ActionType.ShowToast,
    value: toast,
});

export const removeToast = (toastId: number): AppAction => ({
    type: ActionType.RemoveToast,
    value: toastId,
});

export const removeToasts = (): AppAction => ({
    type: ActionType.RemoveToasts,
    value: null,
});
