import { objectKeys } from '@sage/xtrem-shared';
import { SectionControlObject } from '../../component/control-objects';
import type { StickerDecoratorProperties } from '../../component/decorators';
import { getFieldTitle } from '../../component/field/carbon-helpers';
import { messageDialog } from '../../service/dialog-service';
import { localize } from '../../service/i18n-service';
import { queryMetadata } from '../../service/metadata-service';
import type { MetadataResponse } from '../../service/metadata-types';
import { fetchStickerDefinition } from '../../service/screen-loader-service';
import type { Sticker } from '../../service/sticker';
import { showToast } from '../../service/toast-service';
import { xtremConsole } from '../../utils/console';
import { triggerScreenEvent } from '../../utils/events';
import type { AppAction, AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { XtremAppState } from '../state';
import { addScreenDefinition } from './common-actions';
import { addMenuItem } from './menu-actions';
import { getStickerMenuItem } from './menu-items';

export const discoverStickers =
    () =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void | undefined[]> => {
        try {
            const artifactsMetadata: MetadataResponse = await queryMetadata({
                applicationPackages: getState().applicationPackages!,
                metadataType: 'stickers',
                metadataProperties: ['key'],
                locale: getState().applicationContext?.locale,
            });

            if (!artifactsMetadata || !artifactsMetadata.stickers) {
                throw new Error(localize('@sage/xtrem-ui/no-stickers-found', 'Could not find any stickers'));
            }
            const stickerMap = artifactsMetadata.stickers.map(sticker => sticker.key!);
            const stickerDefinitions = await Promise.all(
                stickerMap.map(path => fetchStickerDefinition(getState, dispatch, path)),
            );

            return await Promise.all(
                stickerDefinitions.map(async stickerDefinition => {
                    try {
                        if (!stickerDefinition) {
                            return undefined;
                        }
                        dispatch(addScreenDefinition(stickerDefinition));
                        const sections = objectKeys(stickerDefinition.metadata.controlObjects)
                            .filter(
                                key => stickerDefinition.metadata.controlObjects[key] instanceof SectionControlObject,
                            )
                            .map(key => stickerDefinition.metadata.controlObjects[key] as SectionControlObject);

                        const isActiveCallback = (
                            stickerDefinition.metadata.uiComponentProperties[stickerDefinition.metadata.screenId] as any
                        ).isActive;
                        const isActivePromise = Promise.resolve(isActiveCallback.apply(stickerDefinition.sticker));

                        const isActive = await isActivePromise;
                        if (isActive) {
                            const screenId = stickerDefinition.metadata.screenId;
                            const stickerProperties = stickerDefinition.metadata.uiComponentProperties[
                                screenId
                            ] as StickerDecoratorProperties<Sticker>;

                            const menuItem = getStickerMenuItem(
                                screenId,
                                stickerProperties.icon,
                                sections,
                                undefined,
                                getFieldTitle(screenId, stickerProperties, null),
                                stickerProperties.category,
                            );

                            dispatch(addMenuItem(menuItem));
                            if (stickerProperties) {
                                try {
                                    await triggerScreenEvent(screenId, 'onLoad');
                                } catch {
                                    messageDialog(
                                        screenId,
                                        'error',
                                        'Error',
                                        localize(
                                            '@sage/xtrem-ui/sticker-actions-onload-unhandled-error',
                                            'An unhandled error ocurred',
                                        ),
                                    );
                                }
                            }
                        }
                        return undefined;
                    } catch (error) {
                        xtremConsole.error(
                            `An error ocurred while running the ${stickerDefinition?.metadata.screenId} sticker isActive callback`,
                            error,
                        );
                        return Promise.reject(error);
                    }
                }),
            );
        } catch (error) {
            return showToast(
                localize('@sage/xtrem-ui/error-loading-stickers', 'An error ocurred while loading the stickers'),
                { type: 'error' },
            );
        }
    };

export const setStickerDialogId = (
    screenId: string,
    dialogId: number,
    onFinish?: (values?: any) => void,
): AppAction => ({
    type: ActionType.SetStickerDefinitionDialogId,
    value: { screenId, dialogId, onFinish },
});
