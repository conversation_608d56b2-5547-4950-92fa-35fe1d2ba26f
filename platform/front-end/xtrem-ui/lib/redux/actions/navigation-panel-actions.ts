import { type LocalizeLocale, objectKeys } from '@sage/xtrem-shared';
import { navigationPanelId } from '../../component/container/navigation-panel/navigation-panel-types';
import type { OptionsMenuItem } from '../../component/control-objects';
import type { TableDecoratorProperties } from '../../component/field/table/table-component-types';
import type { CollectionValue } from '../../service/collection-data-service';
import { fetchNavigationPanelData } from '../../service/graphql-service';
import { createNavigationPanelValue } from '../../service/navigation-panel-service';
import {
    getNavigationPanelDefinitionFromState,
    getNavigationPanelState,
    getPageDefinitionFromState,
    getPagePropertiesFromState,
} from '../../utils/state-utils';
import type { AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { XtremAppState } from '../state';
import { getOrderByFromSortModel } from '../../utils/table-component-utils';
import { getGraphQLFilter, getTypedNestedFields } from '../../service/filter-service';
import { getNestedFieldsFromProperties } from '../../utils/nested-field-utils';
import { mapAgGridFilterToXtremFilters } from '../../utils/ag-grid/ag-grid-table-utils';
import { resolveByValue } from '../../utils/resolve-value-utils';
import { showToast } from '../../service/toast-service';
import { localize } from '../../service/i18n-service';

export const refreshNavigationPanel =
    (screenId: string, recordAdded = false) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const pageDefinition = getPageDefinitionFromState(screenId, state);

        if (!pageDefinition) {
            throw new Error(`No page definition found for ${screenId} page.`);
        }

        const pageProperties = getPagePropertiesFromState(screenId, state);
        if (!pageProperties) {
            throw new Error(`No page properties found for ${screenId} page.`);
        }

        const navigationPanelDef = getNavigationPanelDefinitionFromState(screenId, state);
        if (!navigationPanelDef) {
            throw new Error(`Navigation panel couldn't be refreshed as it is not defined for ${screenId} page`);
        }

        const navigationPanelState = getNavigationPanelState(screenId, state);
        if (!navigationPanelState) {
            throw new Error(`Navigation panel couldn't be refreshed as it is not defined for ${screenId} page`);
        }

        const rootNode = pageProperties.node;
        if (!rootNode) {
            throw new Error(
                `Navigation panel couldn't be refreshed because no root node was found on ${screenId} page.`,
            );
        }

        try {
            dispatch({ type: ActionType.SetNavigationPanelIsRefreshing, value: { isRefreshing: true, screenId } });

            const navigationPanelProperties = pageDefinition.metadata.uiComponentProperties[
                navigationPanelId
            ] as TableDecoratorProperties;

            const currentRecords = navigationPanelState.value.getData({
                cleanMetadata: true,
                limit: Number.MAX_SAFE_INTEGER,
            });

            const pageSize = Math.max(30, recordAdded ? currentRecords.length + 1 : currentRecords.length);

            const navigationPanelTableViews = pageDefinition.userSettings[navigationPanelId];
            const currentView = navigationPanelTableViews?.$current?.content[0];

            const selectedOptionsMenuItem: OptionsMenuItem | undefined =
                currentView?.optionsMenuItem ||
                resolveByValue({
                    screenId,
                    fieldValue: null,
                    rowValue: null,
                    propertyValue: navigationPanelProperties.optionsMenu,
                    skipHexFormat: true,
                })?.[0];

            const orderBy = currentView?.sortOrder
                ? getOrderByFromSortModel(currentView?.sortOrder, navigationPanelProperties.columns || [])
                : navigationPanelProperties.orderBy;

            const filter = currentView?.filter
                ? getGraphQLFilter(
                      objectKeys(currentView.filter).map(mapAgGridFilterToXtremFilters(currentView.filter)),
                      getTypedNestedFields(
                          screenId,
                          rootNode,
                          getNestedFieldsFromProperties(navigationPanelProperties),
                          state.nodeTypes,
                      ),
                  )
                : {};

            const navigationPanelData = await fetchNavigationPanelData(
                screenId,
                pageDefinition,
                navigationPanelProperties,
                orderBy,
                filter,
                pageSize,
                state.nodeTypes,
                selectedOptionsMenuItem,
            );

            const value = createNavigationPanelValue(
                screenId,
                navigationPanelProperties,
                navigationPanelData,
                state.nodeTypes,
                state.applicationContext?.locale as LocalizeLocale,
                selectedOptionsMenuItem,
                orderBy,
            );

            dispatch(setNavigationPanelValue(screenId, value));
        } catch {
            dispatch({ type: ActionType.SetNavigationPanelIsRefreshing, value: { isRefreshing: false, screenId } });
            showToast(
                localize('@sage/xtrem-ui/failed-to-refresh-navigation-panel', 'Failed to refresh navigation panel.'),
                { type: 'error' },
            );
        }
    };

const setNavigationPanelValue =
    (screenId: string, value: CollectionValue) =>
    (dispatch: AppThunkDispatch): void => {
        dispatch({
            type: ActionType.SetNavigationPanelValue,
            value: { screenId, value },
        });
    };

export const setNavigationPanelIsHidden =
    (isHidden: boolean, screenId: string) =>
    (dispatch: AppThunkDispatch): void => {
        dispatch({ type: ActionType.SetNavigationPanelIsHidden, value: { isHidden, screenId } });
    };

export const setNavigationPanelIsOpened =
    (isOpened: boolean, screenId: string) =>
    (dispatch: AppThunkDispatch): void => {
        dispatch({ type: ActionType.SetNavigationPanelIsOpened, value: { isOpened, screenId } });
    };

export const setNavigationPanelIsHeaderHidden =
    (isHeaderHidden: boolean, screenId: string) =>
    (dispatch: AppThunkDispatch): void => {
        dispatch({ type: ActionType.SetNavigationPanelIsHeaderHidden, value: { isHeaderHidden, screenId } });
    };
