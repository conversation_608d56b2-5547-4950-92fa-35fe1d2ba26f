import { FieldKey, objectKeys, type Dict } from '@sage/xtrem-shared';
import type {
    FormDesignerControlObject,
    ReferenceProperties,
    TableControlObject,
    WorkflowControlObject,
} from '../../component/control-objects';
import { getFieldTitle, getPageSubtitle } from '../../component/field/carbon-helpers';
import type { DialogControl } from '../../service/dialog-service';
import { customDialog, messageDialog } from '../../service/dialog-service';
import { notifyConsumerAboutDirtyStatus } from '../../service/dirty-state-service';
import { localize } from '../../service/i18n-service';
import type { PageDefinition } from '../../service/page-definition';
import type { ScreenBaseDefinition } from '../../service/screen-base-definition';
import { fetchPageDefinition } from '../../service/screen-loader-service';
import { commitTransaction, setUiComponentProperties } from '../../service/transactions-service';
import type { DialogDescription, LookupDialogContent } from '../../types/dialogs';
import { xtremConsole } from '../../utils/console';
import { NEW_PAGE } from '../../utils/constants';
import { triggerFieldEvent, triggerScreenEvent } from '../../utils/events';
import { getPageTitlesFromPageDefinition } from '../../utils/page-utils';
import { getMainPageDefinitionFromState, getPageDefinition, getPagePropertiesFromState } from '../../utils/state-utils';
import type { QueryParameters } from '../../utils/types';
import type { AppThunkDispatch } from '../action-types';
import { ActionType } from '../action-types';
import type { XtremAppState } from '../state';
import { addScreenDefinition, removeScreenDefinition, setScreenDefinitionReady } from './common-actions';
import { addMenuItem, removeMenuItem } from './menu-actions';
import { closeDialogMenuItemId, getCloseDialogMenuItem } from './menu-items';
import { closeTableSidebar } from './table-sidebar-actions';

const closeOpenedDialogs = (screenDefinitions: Dict<ScreenBaseDefinition>, screenId: string): void => {
    objectKeys(screenDefinitions[screenId].metadata.uiComponentProperties).forEach(fieldId => {
        if (
            (screenDefinitions[screenId].metadata.uiComponentProperties[fieldId] as ReferenceProperties<any>)
                .isReferenceDialogOpen === true
        ) {
            setUiComponentProperties(screenId, fieldId, {
                ...screenDefinitions[screenId].metadata.uiComponentProperties[fieldId],
                isReferenceDialogOpen: false,
            });
        }
    });
};

export const closeDialog =
    (dialogId: number) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<void> => {
        const state = getState();
        const activeDialogs = state.activeDialogs;
        const dialogDescription = activeDialogs[dialogId];
        const screenDefinitions = state.screenDefinitions;
        if (dialogDescription && dialogDescription.isSticker && dialogDescription.screenId) {
            const screenId = dialogDescription.screenId;
            // If the dialog we close is a sticker, then we need to close all reference look up dialogs on the sticker
            closeOpenedDialogs(screenDefinitions, screenId);
        }

        dispatch(removeMenuItem(closeDialogMenuItemId));
        dispatch({ type: ActionType.CloseDialog, value: dialogId });
        if (dialogDescription?.type === 'lookup') {
            dispatch({ type: ActionType.CloseLookupDialog, value: null });
            const content = dialogDescription.content as LookupDialogContent;
            if (dialogDescription?.screenId) {
                if (!content.parentElementId) {
                    triggerFieldEvent(dialogDescription.screenId, content.fieldId, 'onCloseLookupDialog');
                }
            }
        }
        if (dialogDescription?.type === 'table-sidebar') {
            dispatch(closeTableSidebar(dialogId));
        }
        if (
            dialogDescription &&
            dialogDescription.screenId &&
            !dialogDescription.isSticker &&
            dialogDescription.type === 'page'
        ) {
            // We need to call the onClose event of the page dialog before destroying the page definition
            await triggerScreenEvent(dialogDescription.screenId, 'onClose');

            dispatch(removeScreenDefinition(dialogDescription.screenId));
        }
        const applicationContext = getState().applicationContext;
        if (applicationContext?.onPageTitleChange) {
            const mainPage = getMainPageDefinitionFromState(state);
            if (mainPage) {
                const screenId = mainPage.metadata.screenId;
                const pageProperties = getPagePropertiesFromState(screenId, state);
                const pageTitle = pageProperties && getFieldTitle(screenId, pageProperties, null);
                const pageSubtitle = pageProperties && getPageSubtitle(screenId, pageProperties, null);
                applicationContext.onPageTitleChange(pageTitle || null, pageSubtitle || null);
            } else {
                applicationContext.onPageTitleChange(null, null);
            }
        }

        // If we already in a dirtyCheck dialog we will not call again the notifyConsumerAboutDirtyStatus
        if (!dialogDescription?.isDirtyCheck) {
            // We need a fresh copy of the state here.
            notifyConsumerAboutDirtyStatus(getState());
        }
    };

export const openDialog =
    (dialogId: number, dialog: DialogDescription) =>
    (dispatch: AppThunkDispatch, getState: () => XtremAppState): void => {
        dispatch(addMenuItem(getCloseDialogMenuItem(dialogId, dialog.isSticker, dialog.title, dialog.type)));
        dispatch({ type: ActionType.OpenDialog, value: { dialog, dialogId } });
        if (dialog.type === 'lookup' && dialog.screenId) {
            const content = dialog.content as LookupDialogContent;
            const elementId = content.parentElementId || content?.fieldId;
            dispatch({
                type: ActionType.OpenLookupDialog,
                value: {
                    elementId,
                    screenId: dialog.screenId,
                    nestedField: content.parentElementId ? content.fieldId : undefined,
                },
            });
            if (!content.parentElementId) {
                triggerFieldEvent(dialog.screenId, content.fieldId, 'onOpenLookupDialog');
            }
            const updatedState = getState();
            updatedState.applicationContext?.updateMenu(updatedState.menuItems);
            updatedState.applicationContext?.onTelemetryEvent?.(`lookupOpened-${elementId}`, {
                elementId,
            });
        }
        const applicationContext = getState().applicationContext;
        if (applicationContext && applicationContext.onPageTitleChange) {
            applicationContext.onPageTitleChange(dialog.title || null, dialog.subtitle || null);
        }
    };

export const loadPageDialogContent =
    (
        path: string,
        queryParameters: QueryParameters = {},
        /**
         * If the values are provided, they are not fetched from the server.
         * This feature is used to pre-populate page dialogs with data.
         *  */
        values?: Dict<any>,
        onFinish?: (values?: Dict<any>) => void,
        isDuplicate = false,
    ) =>
    async (dispatch: AppThunkDispatch, getState: () => XtremAppState): Promise<string | null> => {
        const pageDefinition: PageDefinition | null = await fetchPageDefinition({
            getState,
            dispatch,
            path,
            isMainPage: false,
            queryParameters,
            values,
            onFinish,
            isDuplicate,
        });

        if (!pageDefinition) {
            return null;
        }
        pageDefinition.isInDialog = true;
        dispatch(addScreenDefinition(pageDefinition));

        const screenId = pageDefinition.metadata.screenId;
        try {
            pageDefinition.page.$standardDeleteAction.isDisabled =
                !queryParameters._id || queryParameters._id === NEW_PAGE;
            pageDefinition.page.$standardDuplicateAction.isDisabled =
                !queryParameters._id || queryParameters._id === NEW_PAGE;
            await triggerScreenEvent(screenId, 'onLoad');
        } catch (e) {
            xtremConsole.error(e);
            messageDialog(
                screenId,
                'error',
                'Error',
                localize('@sage/xtrem-ui/dialog-actions-onload-unhandled-error', 'An unhandled error ocurred'),
            );
        }
        commitTransaction(screenId);
        dispatch(setScreenDefinitionReady(screenId));
        const state = getState();
        const { title, subtitle } = getPageTitlesFromPageDefinition(
            pageDefinition,
            state.applicationContext?.locale || 'en-US',
        );
        state.applicationContext?.onPageTitleChange?.(title, subtitle || null);

        return screenId;
    };

export const openFieldInDialog =
    (screenId: string, elementId: string) =>
    (_dispatch: AppThunkDispatch, getState: () => XtremAppState): DialogControl => {
        const state = getState();
        const pageDefinition = getPageDefinition(screenId, state);
        if (!pageDefinition) {
            throw new Error(`Page definition not found for screenId: ${screenId}`);
        }
        const controlObject = pageDefinition.metadata.controlObjects[elementId] as
            | TableControlObject
            | WorkflowControlObject
            | FormDesignerControlObject;
        if (
            controlObject.componentType !== FieldKey.Table &&
            controlObject.componentType !== FieldKey.Workflow &&
            controlObject.componentType !== FieldKey.FormDesigner
        ) {
            throw new Error(`Field ${controlObject.componentType} is not supported as a dialog.`);
        }
        const pageTitle = getPageTitlesFromPageDefinition(pageDefinition, state.applicationContext?.locale || 'en-US');
        return customDialog(screenId, 'info', controlObject, {
            dialogTitle: `${pageTitle.title} - ${controlObject.title}`,
            fullScreen: true,
            acceptButton: { isHidden: true },
            cancelButton: { isHidden: true },
            isPaddingRemoved: true,
            resolveOnCancel: true,
        });
    };
