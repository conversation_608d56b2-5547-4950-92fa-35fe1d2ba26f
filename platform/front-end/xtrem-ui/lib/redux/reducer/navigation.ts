import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { NavigationState } from '../state';

const initialState: NavigationState = {
    history: [],
    isBackNavigation: false,
};

export const navigation = (state: NavigationState = initialState, action: AppAction): NavigationState => {
    switch (action.type) {
        case ActionType.PushToHistory: {
            const lastEntry = state.history[state.history.length - 1];
            if (
                lastEntry &&
                lastEntry.path === action.value.path &&
                JSON.stringify(lastEntry.queryParams) === JSON.stringify(action.value.queryParams)
            ) {
                return state;
            }

            return {
                ...state,
                history: [...state.history, action.value],
            };
        }

        case ActionType.PopFromHistory:
            return {
                ...state,
                history: state.history.length > 0 ? state.history.slice(0, -1) : [],
            };

        case ActionType.ClearHistory:
            return {
                ...state,
                history: [],
            };

        case ActionType.SetBackNavigation:
            return {
                ...state,
                isBackNavigation: action.value,
            };

        case ActionType.ReplaceLastInHistory: {
            if (state.history.length === 0) {
                return {
                    ...state,
                    history: [action.value],
                };
            }
            return {
                ...state,
                history: [...state.history.slice(0, -1), action.value],
            };
        }

        case ActionType.HandleBackNavigation:
            return {
                ...state,
                isBackNavigation: false,
                history: state.history.length > 0 ? state.history.slice(0, -1) : [],
            };

        default:
            return state;
    }
};
