import { objectKeys, type Dict } from '@sage/xtrem-shared';
import produce from 'immer';
import { cloneDeep, isEqual, isNil } from 'lodash';
import { navigationPanelId } from '../../component/container/navigation-panel/navigation-panel-types';
import type { PageDefinition } from '../../service/page-definition';
import type { ScreenBaseDefinition, ValidationResult } from '../../service/screen-base-definition';
import { DEFAULT_VIEW_ID, SERVER_VALIDATION_RULE_PREFIX } from '../../utils/constants';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { UiComponentUserSettings } from '../state';
import type { InternalSectionProperties } from '../../component/container/section/section-types';

const DEFAULT_TABLE_VIEW = (): Dict<UiComponentUserSettings> => ({
    $current: { _id: DEFAULT_VIEW_ID, title: 'default', content: [], isDefault: true },
});

export const screenDefinitions = (
    state: Dict<ScreenBaseDefinition> = {},
    action: AppAction,
): Dict<ScreenBaseDefinition> => {
    return produce(state, (nextState: Dict<ScreenBaseDefinition | undefined>) => {
        let screenDefinition: ScreenBaseDefinition | undefined;
        let pageDefinition: PageDefinition;

        switch (action.type) {
            case ActionType.SetErrors:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition?.errors) {
                    const uiProps = screenDefinition?.metadata?.uiComponentProperties?.[action.value.elementId];
                    /**
                     * TODO: this isn't ideal but since error messages are displayed by Carbon only if a title is set
                     * then just ensure that an empty title is visible so that the tooltip is shown.
                     */
                    if (uiProps && !uiProps.title) {
                        uiProps.title = ' ';
                        uiProps.isTitleHidden = false;
                    }
                    screenDefinition.errors[action.value.elementId] = action.value.errors;
                }
                break;
            case ActionType.UpdateErrors:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const errors = action.value.validationErrors;
                    screenDefinition.errors = objectKeys(errors)
                        .filter(e => Boolean(errors[e]))
                        .reduce<Dict<ValidationResult[]>>((prevValue, elementId) => {
                            prevValue[elementId] = errors[elementId]!;
                            return prevValue;
                        }, {});
                }
                break;
            case ActionType.UpdateNestedFieldErrors:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const existingErrors = screenDefinition.errors[action.value.elementId] || [];
                    const filteredErrors = existingErrors.filter(
                        e => !(e.columnId === action.value.columnId && e.recordId === action.value.recordId),
                    );

                    screenDefinition.errors = {
                        ...screenDefinition.errors,
                        [action.value.elementId]: [...filteredErrors, ...action.value.validationErrors],
                    };
                }
                break;
            case ActionType.UpdateNestedFieldRecordErrors:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const existingErrors = screenDefinition.errors[action.value.elementId] || [];
                    const filteredErrors = existingErrors.filter(
                        e => !(e.recordId === action.value.recordId && (e.level || 0) === (action.value.level || 0)),
                    );

                    screenDefinition.errors = {
                        ...screenDefinition.errors,
                        [action.value.elementId]: [...filteredErrors, ...action.value.validationErrors],
                    };
                }
                break;
            case ActionType.AddInternalError:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const previousErrors = screenDefinition.internalErrors || {};
                    const currentErrors = {
                        ...previousErrors,
                        [action.value.elementId]: action.value.errorMessage,
                    };
                    screenDefinition.internalErrors = currentErrors;
                }
                break;
            case ActionType.RemoveInternalError:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const previousErrors = screenDefinition.internalErrors || {};
                    const currentErrors = objectKeys(previousErrors)
                        .filter(elementId => {
                            return elementId !== action.value.elementId;
                        })
                        .reduce((reduction: Dict<ValidationResult>, elementId: string) => {
                            return {
                                ...reduction,
                                [elementId]: previousErrors[elementId],
                            } as Dict<ValidationResult>;
                        }, {});
                    screenDefinition.internalErrors = currentErrors;
                }
                break;
            case ActionType.AddScreenDefinition:
                const addScreenValue = action.value;
                // eslint-disable-next-line no-param-reassign
                nextState = { ...nextState, [addScreenValue.metadata.screenId]: addScreenValue };
                if (addScreenValue.type === 'page') {
                    (addScreenValue as PageDefinition).selectedRecordId = addScreenValue.values._id
                        ? addScreenValue.values._id
                        : null;
                }
                break;
            case ActionType.FinishScreenLoading:
                const finishAddScreenValue = action.value.pageDefinition;
                // eslint-disable-next-line no-param-reassign
                nextState = { ...nextState, [finishAddScreenValue.metadata.screenId]: finishAddScreenValue };
                if (finishAddScreenValue.type === 'page') {
                    (finishAddScreenValue as PageDefinition).selectedRecordId = finishAddScreenValue.values._id
                        ? finishAddScreenValue.values._id
                        : null;
                }
                break;
            case ActionType.CommitTransaction:
                screenDefinition = nextState[action.value.screenId];

                objectKeys(action.value.transaction.values)
                    .filter(key => action.value.transaction.values[key].hasChangedInTransaction)
                    .forEach(key => {
                        if (screenDefinition?.values) {
                            screenDefinition.values[key] = action.value.transaction.values[key].value;
                        }
                    });

                objectKeys(action.value.transaction.uiComponentProperties)
                    .filter(key => action.value.transaction.uiComponentProperties[key].hasChangedInTransaction)
                    .forEach(key => {
                        if (screenDefinition?.metadata?.uiComponentProperties) {
                            screenDefinition.metadata.uiComponentProperties[key] = {
                                ...screenDefinition.metadata.uiComponentProperties[key],
                                ...action.value.transaction.uiComponentProperties[key].value,
                            };
                        }
                    });
                break;
            case ActionType.SetNavigationPanelValue:
                pageDefinition = nextState[action.value.screenId] as PageDefinition;
                if (pageDefinition.navigationPanel) {
                    pageDefinition.navigationPanel.value = action.value.value;
                    pageDefinition.navigationPanel.isRefreshing = false;
                }
                break;
            case ActionType.SetNavigationPanelIsRefreshing:
                pageDefinition = nextState[action.value.screenId] as PageDefinition;
                if (pageDefinition.navigationPanel) {
                    pageDefinition.navigationPanel.isRefreshing = action.value.isRefreshing;
                }
                break;
            case ActionType.SetPageInsightCount:
                pageDefinition = nextState[action.value.screenId] as PageDefinition;
                if (pageDefinition.navigationPanel) {
                    pageDefinition.insightCount = action.value.count;
                }
                break;
            case ActionType.RemoveNonNestedError:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    // If columnId is not provided, delete all errors related to the elementId
                    if (isNil(action.value.columnId)) {
                        delete screenDefinition.errors[action.value.elementId];
                    } else {
                        // If columnId is provided, keep only errors that have different columnId
                        const existingErrors = (screenDefinition.errors[action.value.elementId] || []).filter(
                            (e: ValidationResult) => !!e.columnId && e.columnId !== action.value.columnId,
                        );

                        if (existingErrors.length === 0) {
                            delete screenDefinition.errors[action.value.elementId];
                        } else {
                            screenDefinition.errors = {
                                ...screenDefinition.errors,
                                [action.value.elementId]: [...existingErrors],
                            };
                        }
                    }
                }
                break;
            case ActionType.RemovePhantomError:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const existingErrors = screenDefinition.errors[action.value.elementId] || [];
                    screenDefinition.errors[action.value.elementId] = existingErrors.filter(
                        (e: ValidationResult) => e.validationRule !== 'dirtyPhantomRow',
                    );
                }

                break;
            case ActionType.RemoveScreenDefinition:
                delete nextState[action.value];
                break;
            case ActionType.SetFieldDirtyState:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition && !screenDefinition.dirtyStates[action.value.elementId]) {
                    screenDefinition.dirtyStates[action.value.elementId] = true;
                }
                break;
            case ActionType.SetFieldCleanState:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition && screenDefinition.dirtyStates[action.value.elementId]) {
                    screenDefinition.dirtyStates[action.value.elementId] = false;
                }
                break;
            case ActionType.SetFieldValue:
                screenDefinition = nextState[action.value.screenId];
                if (
                    screenDefinition &&
                    action.value.isOrganicChange &&
                    !screenDefinition.dirtyStates[action.value.elementId]
                ) {
                    screenDefinition.dirtyStates[action.value.elementId] = true;
                }

                if (screenDefinition?.values) {
                    screenDefinition.values[action.value.elementId] = action.value.fieldValue;
                }
                break;
            // TODO Should we use it? Why not starting a transaction?
            case ActionType.SetUiComponentProperties:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition?.metadata?.uiComponentProperties) {
                    screenDefinition.metadata.uiComponentProperties[action.value.elementId] =
                        action.value.fieldProperties;
                }
                break;
            case ActionType.SetScreenDefinitionDialogId:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    screenDefinition.dialogId = action.value.dialogId;
                }
                break;
            case ActionType.SetStickerDefinitionDialogId:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    screenDefinition.dialogId = action.value.dialogId;
                    screenDefinition.onFinish = action.value.onFinish;
                }
                break;
            case ActionType.SetScreenDefinitionReady:
                pageDefinition = nextState[action.value] as PageDefinition;
                if (pageDefinition) {
                    pageDefinition.isReady = true;
                }
                break;
            case ActionType.SetValues:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    screenDefinition.values = action.value.values;

                    if (!action.value.preserveValidationState) {
                        screenDefinition.errors = {};
                    }

                    if (screenDefinition.type === 'page') {
                        (screenDefinition as PageDefinition).selectedRecordId = action.value.values._id
                            ? action.value.values._id
                            : null;
                    }
                }
                break;
            case ActionType.ResetScreenUiComponentProperties:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition?.metadata?.uiComponentProperties) {
                    screenDefinition.metadata.uiComponentProperties = {
                        ...screenDefinition?.metadata?.defaultUiComponentProperties,
                    };
                }
                if (screenDefinition && action.value.activeSection) {
                    (
                        screenDefinition.metadata.uiComponentProperties[
                            action.value.activeSection
                        ] as InternalSectionProperties
                    ).isLoaded = true;
                }
                if ((screenDefinition as PageDefinition)?.insightCount) {
                    (screenDefinition as PageDefinition).insightCount = 0;
                }
                break;
            case ActionType.SetNavigationPanelIsHidden:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition?.navigationPanel) {
                    screenDefinition.navigationPanel.isHidden = action.value.isHidden;
                }
                break;
            case ActionType.SetNavigationPanelIsOpened:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition?.navigationPanel) {
                    screenDefinition.navigationPanel.isOpened = action.value.isOpened;
                }
                break;
            case ActionType.SetNavigationPanelIsHeaderHidden:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition?.navigationPanel) {
                    screenDefinition.navigationPanel.isHeaderHidden = action.value.isHeaderHidden;
                }
                break;
            case ActionType.SetPageClean:
                screenDefinition = nextState[action.value];
                if (screenDefinition) {
                    screenDefinition.dirtyStates = {};
                }
                break;
            case ActionType.SetPath:
                const screenId = objectKeys(nextState).find(s => (nextState[s] as PageDefinition).isMainPage);
                screenDefinition = screenId ? nextState[screenId] : undefined;
                if (screenDefinition) {
                    screenDefinition.dirtyStates = {};
                }
                break;
            case ActionType.SetQueryParameter:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    pageDefinition = screenDefinition as PageDefinition;

                    pageDefinition.queryParameters = {
                        ...pageDefinition.queryParameters,
                        [action.value.parameterName]: action.value.value,
                    };
                    if (action.value.parameterName === '_id') {
                        pageDefinition.selectedRecordId = action.value.value ? String(action.value.value) : null;
                    }
                }
                break;
            case ActionType.RemovePageServerErrors:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    screenDefinition.errors = objectKeys(screenDefinition.errors).reduce<Dict<ValidationResult[]>>(
                        (prevValue, elementId) => {
                            const errors = (screenDefinition?.errors[elementId] || []).filter(
                                e => !e.validationRule.startsWith(SERVER_VALIDATION_RULE_PREFIX),
                            );

                            prevValue[elementId] = errors.length > 0 ? errors : [];
                            return prevValue;
                        },
                        {},
                    );
                }
                break;
            case ActionType.SetActiveSection:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const elementId = action.value.activeSection;
                    const pageDefinition = screenDefinition as PageDefinition;
                    pageDefinition.activeSection = elementId;
                    if (action.value.activeSection && action.value.isLoading)
                        if (pageDefinition?.metadata?.uiComponentProperties) {
                            (
                                pageDefinition.metadata.uiComponentProperties[
                                    action.value.activeSection
                                ] as InternalSectionProperties
                            ).isLoading = true;
                        }
                }
                break;
            case ActionType.SetSectionValues:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const elementId = action.value.sectionId;
                    const pageDefinition = screenDefinition as PageDefinition;
                    if (elementId) {
                        (
                            pageDefinition.metadata.uiComponentProperties[elementId] as InternalSectionProperties
                        ).isLoading = false;
                        (
                            pageDefinition.metadata.uiComponentProperties[elementId] as InternalSectionProperties
                        ).isLoaded = true;
                    }

                    screenDefinition.values = { ...pageDefinition.values, ...action.value.values };
                }
                break;
            case ActionType.SetSectionReady:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const elementId = action.value.sectionId;
                    const pageDefinition = screenDefinition as PageDefinition;
                    if (elementId) {
                        (
                            pageDefinition.metadata.uiComponentProperties[elementId] as InternalSectionProperties
                        ).isReady = true;
                    }
                }
                break;
            case ActionType.SetElementUserSettings:
                screenDefinition = nextState[action.value.screenId] as PageDefinition | undefined;
                if (screenDefinition?.userSettings) {
                    const tableViews =
                        screenDefinition.userSettings[action.value.elementId] || cloneDeep(DEFAULT_TABLE_VIEW());
                    if (action.value.userSettings) {
                        tableViews.$current = {
                            ...action.value.userSettings,
                            _id: action.value.userSettings._id || DEFAULT_VIEW_ID,
                            title: action.value.userSettings.title || 'default',
                            content: action.value.userSettings.content || [],
                            isDirty: false,
                        };
                    } else {
                        delete tableViews.$current;
                    }
                    screenDefinition.userSettings[action.value.elementId] = tableViews;
                }
                break;
            case ActionType.SetTableViewColumnHidden:
                screenDefinition = nextState[action.value.screenId] as PageDefinition | undefined;
                if (screenDefinition?.userSettings) {
                    const tableViews =
                        screenDefinition.userSettings[action.value.elementId] || cloneDeep(DEFAULT_TABLE_VIEW());

                    const currentColumnHidden = tableViews.$current?.content?.[action.value.level]?.columnHidden;
                    if (!isEqual(currentColumnHidden, action.value.columnHidden)) {
                        tableViews.$current.content[action.value.level] = {
                            ...(tableViews.$current?.content?.[action.value.level] || {}),
                            columnHidden: action.value.columnHidden,
                        };

                        tableViews.$current.isDirty = true;
                    }

                    screenDefinition.userSettings[action.value.elementId] = tableViews;
                }
                break;
            case ActionType.SetTableViewColumnOrder:
                screenDefinition = nextState[action.value.screenId] as PageDefinition | undefined;
                if (screenDefinition) {
                    const tableViews =
                        screenDefinition.userSettings[action.value.elementId] || cloneDeep(DEFAULT_TABLE_VIEW());
                    const currentColumnOrder = tableViews.$current?.content?.[action.value.level]?.columnOrder;

                    if (!isEqual(currentColumnOrder, action.value.columnOrder)) {
                        tableViews.$current.content[action.value.level] = {
                            ...(tableViews.$current?.content?.[action.value.level] || {}),
                            columnOrder: action.value.columnOrder,
                        };

                        tableViews.$current.isDirty = true;
                    }

                    screenDefinition.userSettings[action.value.elementId] = tableViews;
                }
                break;
            case ActionType.SetTableViewFilter:
                screenDefinition = nextState[action.value.screenId] as PageDefinition | undefined;
                if (screenDefinition) {
                    const tableViews =
                        screenDefinition.userSettings[action.value.elementId] || cloneDeep(DEFAULT_TABLE_VIEW());
                    const currentFilter = tableViews.$current?.content?.[action.value.level]?.filter || {};
                    if (!isEqual(currentFilter, action.value.filter)) {
                        tableViews.$current.content[action.value.level] = {
                            ...(tableViews.$current?.content?.[action.value.level] || {}),
                            filter: action.value.filter,
                        };

                        tableViews.$current.isDirty = true;
                        screenDefinition.userSettings[action.value.elementId] = tableViews;
                    }
                }
                break;
            case ActionType.SetTableViewGrouping:
                screenDefinition = nextState[action.value.screenId] as PageDefinition | undefined;
                if (screenDefinition) {
                    const tableViews =
                        screenDefinition.userSettings[action.value.elementId] || cloneDeep(DEFAULT_TABLE_VIEW());

                    const currentTableViewGrouping = tableViews.$current?.content?.[action.value.level]?.grouping;

                    if (!isEqual(currentTableViewGrouping, action.value.grouping)) {
                        tableViews.$current.content[action.value.level] = {
                            ...(tableViews.$current?.content?.[action.value.level] || {}),
                            grouping: action.value.grouping,
                        };

                        tableViews.$current.isDirty = true;
                    }

                    screenDefinition.userSettings[action.value.elementId] = tableViews;
                }
                break;
            case ActionType.SetTableViewOptionsMenuItem:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const tableViews =
                        screenDefinition.userSettings[action.value.elementId] || cloneDeep(DEFAULT_TABLE_VIEW());

                    const currentOptionsMenuItem = tableViews.$current?.content?.[action.value.level]?.optionsMenuItem;

                    if (!isEqual(currentOptionsMenuItem, action.value.optionsMenuItem)) {
                        tableViews.$current.content[action.value.level] = {
                            ...(tableViews.$current?.content?.[action.value.level] || {}),
                            optionsMenuItem: action.value.optionsMenuItem,
                        };

                        tableViews.$current.isDirty = true;
                    }

                    screenDefinition.userSettings[action.value.elementId] = tableViews;
                }
                break;
            case ActionType.SetTableViewOptionsMenuItemAndViewFilter:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const tableViews =
                        screenDefinition.userSettings[action.value.elementId] || cloneDeep(DEFAULT_TABLE_VIEW());

                    const currentOptionsMenuItem = tableViews.$current?.content?.[action.value.level]?.optionsMenuItem;
                    const currentFilter = tableViews.$current?.content?.[action.value.level]?.filter || {};

                    if (
                        !isEqual(currentOptionsMenuItem, action.value.optionsMenuItem) ||
                        !isEqual(currentFilter, action.value.filter)
                    ) {
                        tableViews.$current.content[action.value.level] = {
                            ...(tableViews.$current?.content?.[action.value.level] || {}),
                            filter: action.value.filter,
                            optionsMenuItem: action.value.optionsMenuItem,
                        };

                        tableViews.$current.isDirty = true;
                    }

                    screenDefinition.userSettings[action.value.elementId] = tableViews;
                }
                break;
            case ActionType.SetUserCustomizationSaved:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const tableViews = screenDefinition.userSettings?.[action.value.elementId];
                    tableViews.$current._id = action.value.viewId;
                    tableViews.$current.isDirty = false;
                }
                break;
            case ActionType.SetTableViewSearchText:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const tableViews =
                        screenDefinition.userSettings[action.value.elementId] || cloneDeep(DEFAULT_TABLE_VIEW());
                    tableViews.$current.content[action.value.level] = {
                        ...(tableViews.$current?.content?.[action.value.level] || {}),
                        searchText: action.value.searchText,
                    };

                    screenDefinition.userSettings[action.value.elementId] = tableViews;
                }
                break;
            case ActionType.ClearNavigationPanelSearchText:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const tableViews =
                        screenDefinition.userSettings[navigationPanelId] || cloneDeep(DEFAULT_TABLE_VIEW());
                    tableViews.$current.content[0] = {
                        ...(tableViews.$current?.content?.[0] || {}),
                        searchText: '',
                    };

                    screenDefinition.userSettings[navigationPanelId] = tableViews;
                }
                break;
            case ActionType.SetTableViewSortOrder:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    const tableViews =
                        screenDefinition.userSettings[action.value.elementId] || cloneDeep(DEFAULT_TABLE_VIEW());
                    tableViews.$current.content[action.value.level] = {
                        ...(tableViews.$current?.content?.[action.value.level] || {}),
                        sortOrder: action.value.sortOrder,
                    };
                    tableViews.$current.isDirty = true;

                    screenDefinition.userSettings[action.value.elementId] = tableViews;
                }
                break;
            case ActionType.Set360ViewState:
                screenDefinition = nextState[action.value.screenId];
                if (screenDefinition) {
                    (screenDefinition as PageDefinition).is360ViewOn = action.value.state;
                }
                break;
            default:
            // Intentionally left empty.
        }
        return nextState as Dict<ScreenBaseDefinition>;
    });
};
