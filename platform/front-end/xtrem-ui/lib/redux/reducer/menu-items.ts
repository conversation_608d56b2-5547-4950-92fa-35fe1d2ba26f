import produce from 'immer';
import { xtremConsole } from '../../utils/console';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { Menu } from '../state';

export const formatBadgeContent = (badgeContent?: string): string => {
    return badgeContent || '';
};

export const menuItems = (state: Menu[] = [], action: AppAction): Menu[] =>
    produce(state, (nextState: Menu[]) => {
        const addMenuItem = (menuItemToAdd: Menu): void => {
            const menuItemIndex = state.findIndex(m => m.id === menuItemToAdd.id);
            if (menuItemIndex === -1) {
                // eslint-disable-next-line no-param-reassign
                nextState = state.concat(menuItemToAdd);
            } else {
                nextState[menuItemIndex] = { ...menuItemToAdd };
            }
        };

        switch (action.type) {
            case ActionType.AddMenuItem:
                addMenuItem(action.value);
                break;
            case ActionType.UpdateMenuItem:
                const menuItemUpdate = nextState.find(m => m.id === action.value.id);
                if (menuItemUpdate) {
                    menuItemUpdate.badgeContent = formatBadgeContent(action.value.badgeContent);
                } else {
                    xtremConsole.warn(`No menu item was found for ${action.value.id}`);
                }
                break;
            case ActionType.RemoveMenuItem:
                // eslint-disable-next-line no-param-reassign
                nextState = state.filter(m => m.id !== action.value);
                break;
            case ActionType.SetScreenDefinitionDialogId:
                const index = nextState.findIndex(m => m.dialogId === action.value.dialogId);
                nextState[index] = { ...nextState[index], title: action.value.title };
                break;
            case ActionType.UpdateTableSidebarDialogTarget:
                const idx = nextState.findIndex(m => m.dialogId === action.value.dialogId);
                if (idx >= 0 && nextState[idx].dialogType === 'table-sidebar') {
                    nextState[idx].title = action.value.title;
                }
                break;
            default:
                break;
        }

        return nextState;
    });
