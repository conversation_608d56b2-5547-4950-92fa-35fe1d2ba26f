import type { XtremUiPlugin } from '../../service/plugin-service';
import type { Dict } from '@sage/xtrem-shared';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';

export const plugins = (
    state: Dict<XtremUiPlugin<any, any>> = {},
    action: AppAction,
): Dict<XtremUiPlugin<any, any>> => {
    if (action.type === ActionType.AddPlugin) {
        return { ...state, [action.value.pluginPackage]: action.value.pluginDefinition };
    }
    return state;
};
