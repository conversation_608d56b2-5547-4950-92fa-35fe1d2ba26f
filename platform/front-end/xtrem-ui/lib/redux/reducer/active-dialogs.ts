import type { Dict } from '@sage/xtrem-shared';
import produce from 'immer';
import type { DialogDescription, TableSidebarDialogContent } from '../../types/dialogs';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';

export const activeDialogs = (state: Dict<DialogDescription> = {}, action: AppAction): Dict<DialogDescription> =>
    produce(state, (nextState: Dict<DialogDescription>) => {
        switch (action.type) {
            case ActionType.OpenDialog:
                nextState[action.value.dialogId] = action.value.dialog;
                break;
            case ActionType.CloseDialog:
                delete nextState[action.value];
                break;
            case ActionType.SetScreenDefinitionDialogId:
                nextState[action.value.dialogId] = {
                    ...nextState[action.value.dialogId],
                    screenId: action.value.screenId,
                    content: action.value.pageControlObject,
                    title: action.value.forceSetTitle ? action.value.title : undefined,
                    subtitle: action.value.subtitle,
                };
                break;
            case ActionType.UpdateTableSidebarDialogTarget:
                if (!nextState[action.value.dialogId]) {
                    throw new Error(`Dialog with id ${action.value.dialogId} not found`);
                }

                if (nextState[action.value.dialogId].type !== 'table-sidebar') {
                    throw new Error(`Dialog with id ${action.value.dialogId} is not a table-sidebar dialog`);
                }

                const dialogContent = nextState[action.value.dialogId].content as TableSidebarDialogContent;
                nextState[action.value.dialogId] = {
                    ...nextState[action.value.dialogId],
                    content: {
                        ...dialogContent,
                        recordId: action.value.recordId,
                        isNewRecord: action.value.isNewRecord,
                        prevRecordId: action.value.prevRecordId,
                        nextRecordId: action.value.nextRecordId,
                    },
                    title: action.value.title,
                };
                break;
            default:
                break;
        }

        return nextState;
    });
