import type { AppAction } from '../../action-types';
import { ActionType } from '../../action-types';
import type { LoadingState } from '../../state';
import { loading } from '../loading';

describe('Reducer', () => {
    describe('loading', () => {
        it('Should return globalLoading as false and pages as empty object for the initial state', () => {
            const initialState = undefined;
            const action: AppAction = { type: ActionType.ReduxInitialAction };
            const expectedState: LoadingState = {
                globalLoading: false,
                pages: {},
                loadingDashboards: false,
                widgets: {},
            };

            const nextState = loading(initialState, action);
            expect(nextState).toStrictEqual(expectedState);
        });

        it('Should handle SetGlobalLoading action and set globalLoading in the state to true', () => {
            const initialState = undefined;
            const action: AppAction = {
                type: ActionType.SetGlobalLoading,
                value: true,
            };
            const expectedState: LoadingState = {
                globalLoading: true,
                pages: {},
                loadingDashboards: false,
                widgets: {},
            };

            const nextState = loading(initialState, action);
            expect(nextState).toStrictEqual(expectedState);
        });

        it('Should handle SetValues action and set globalLoading in the state to false', () => {
            const initialState = undefined;
            const action: AppAction = {
                type: ActionType.SetValues,
                value: {
                    screenId: 'anything-will-do',
                    values: {},
                },
            };
            const expectedState: LoadingState = {
                globalLoading: false,
                pages: {},
                loadingDashboards: false,
                widgets: {},
            };

            const nextState = loading(initialState, action);
            expect(nextState).toStrictEqual(expectedState);
        });

        it('Should handle SetComponentLoading action and set page1 field1 component loading to true', () => {
            const initialState = undefined;
            const action: AppAction = {
                type: ActionType.SetComponentLoading,
                value: {
                    elementId: 'field1',
                    screenId: 'page1',
                    isLoading: true,
                },
            };
            const expectedState: LoadingState = {
                globalLoading: false,
                pages: {
                    page1: { field1: true },
                },
                loadingDashboards: false,
                widgets: {},
            };
            const nextState = loading(initialState, action);
            expect(nextState).toStrictEqual(expectedState);
        });
        it('Should handle SetComponentLoading action and set page2 field1 component loading to true', () => {
            const initialState = {
                globalLoading: false,
                pages: {
                    page1: { field1: false },
                },
                loadingDashboards: false,
                widgets: {},
            };
            const action: AppAction = {
                type: ActionType.SetComponentLoading,
                value: {
                    elementId: 'field1',
                    screenId: 'page2',
                    isLoading: true,
                },
            };
            const expectedState: LoadingState = {
                globalLoading: false,
                pages: {
                    page1: { field1: false },
                    page2: { field1: true },
                },
                loadingDashboards: false,
                widgets: {},
            };
            const nextState = loading(initialState, action);
            expect(nextState).toStrictEqual(expectedState);
        });
        it('Should handle SetComponentLoading action and set page1 field2 component loading to true', () => {
            const initialState = {
                globalLoading: false,
                pages: {
                    page1: { field1: false },
                },
                loadingDashboards: false,
                widgets: {},
            };
            const action: AppAction = {
                type: ActionType.SetComponentLoading,
                value: {
                    elementId: 'field2',
                    screenId: 'page1',
                    isLoading: true,
                },
            };
            const expectedState: LoadingState = {
                globalLoading: false,
                pages: {
                    page1: { field1: false, field2: true },
                },
                loadingDashboards: false,
                widgets: {},
            };
            const nextState = loading(initialState, action);
            expect(nextState).toStrictEqual(expectedState);
        });
    });
});
