import { ActionType } from '../../action-types';
import { nodeNames, widgetCategories } from '../dashboard';

describe('widget editor reducer', () => {
    describe('nodeNames', () => {
        it('should return empty object by default', () => {
            const result = nodeNames(undefined, { type: ActionType.ReduxInitialAction });
            expect(result).toEqual({});
        });

        it('should return value of the SetNodeNames action', () => {
            const result = nodeNames(undefined, {
                type: ActionType.SetNodeNames,
                value: { '@sage/xtrem-test/MyTestNode': 'My test node' },
            });
            expect(result).toEqual({ '@sage/xtrem-test/MyTestNode': 'My test node' });
        });
    });

    describe('widgetCategories', () => {
        it('should return empty object by default', () => {
            const result = widgetCategories(undefined, { type: ActionType.ReduxInitialAction });
            expect(result).toEqual({});
        });

        it('should return value of the SetWidgetCategories action', () => {
            const result = widgetCategories(undefined, {
                type: ActionType.SetWidgetCategories,
                value: { categories: { TEST_CATEGORY: 'test' } },
            });
            expect(result).toEqual({ TEST_CATEGORY: 'test' });
        });
    });
});
