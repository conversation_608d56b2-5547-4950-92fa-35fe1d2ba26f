import { ActionType } from '../../action-types';
import { websocketCallbackMiddleware } from '../../middleware/websocket-middleware';

describe('websocketCallbackMiddleware', () => {
    it('should execute all callbacks for the given category', () => {
        const callback1 = jest.fn();
        const callback2 = jest.fn();
        const args = ['foo', 42];
        const activeSubscriptions = new Map([['test', new Set([callback1, callback2])]]);
        const getState = jest.fn(() => ({
            websocket: {
                subscriptions: { activeSubscriptions },
            },
        }));
        const next = jest.fn();
        const store = { getState, dispatch: jest.fn() } as any;
        const action = {
            type: ActionType.TriggerCategoryCallbacks,
            value: { category: 'test', args },
        } as any;

        websocketCallbackMiddleware(store)(next)(action);

        expect(callback1).toHaveBeenCalledWith(...args);
        expect(callback2).toHaveBeenCalledWith(...args);
        expect(next).toHaveBeenCalledWith(action);
    });

    it('should handle callback errors gracefully', () => {
        const errorCallback = jest.fn(() => {
            throw new Error('fail');
        });
        const normalCallback = jest.fn();
        const activeSubscriptions = new Map([['test', new Set([errorCallback, normalCallback])]]);
        const getState = jest.fn(() => ({
            websocket: {
                subscriptions: { activeSubscriptions },
            },
        }));
        const next = jest.fn();
        const store = { getState, dispatch: jest.fn() } as any;
        const action = {
            type: ActionType.TriggerCategoryCallbacks,
            value: { category: 'test', args: [] },
        } as any;

        // Silence error output for this test
        const originalError = console.error;
        console.error = jest.fn();

        websocketCallbackMiddleware(store)(next)(action);

        expect(errorCallback).toHaveBeenCalled();
        expect(normalCallback).toHaveBeenCalled();
        expect(next).toHaveBeenCalledWith(action);

        console.error = originalError;
    });

    it('should do nothing if category does not exist', () => {
        const getState = jest.fn(() => ({
            websocket: {
                subscriptions: { activeSubscriptions: new Map() },
            },
        }));
        const next = jest.fn();
        const store = { getState, dispatch: jest.fn() } as any;
        const action = {
            type: ActionType.TriggerCategoryCallbacks,
            value: { category: 'non-existent', args: [] },
        } as any;

        websocketCallbackMiddleware(store)(next)(action);
        expect(next).toHaveBeenCalledWith(action);
    });

    it('should pass through unrelated actions', () => {
        const getState = jest.fn(() => ({
            websocket: {
                subscriptions: { activeSubscriptions: new Map() },
            },
        }));
        const next = jest.fn();
        const store = { getState, dispatch: jest.fn() } as any;
        const action = { type: 'UNRELATED_ACTION' } as any;

        websocketCallbackMiddleware(store)(next)(action);
        expect(next).toHaveBeenCalledWith(action);
    });
});
