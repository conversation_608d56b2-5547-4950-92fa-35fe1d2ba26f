import { ActionType } from '../../action-types';
import { path } from '../path';

describe('Reducer', () => {
    describe('path', () => {
        it('Should return null as initial state', () => {
            expect(path(undefined, { type: ActionType.ReduxInitialAction })).toBeNull();
        });

        it('Should handle SetPath and set value as state', () => {
            expect(
                path(undefined, {
                    type: ActionType.SetPath,
                    value: '01',
                }),
            ).toBe('01');
        });
    });
});
