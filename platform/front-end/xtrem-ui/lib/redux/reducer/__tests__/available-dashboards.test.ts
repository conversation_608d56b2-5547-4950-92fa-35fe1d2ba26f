import type { AppAction } from '../../action-types';
import { ActionType } from '../../action-types';
import { availableDashboards } from '../dashboard-group';

describe('availableDashboard reducer', () => {
    it('Should return an empty array as initial state', () => {
        const initialState = undefined;
        const action: AppAction = { type: ActionType.ReduxInitialAction };
        const expectedState = [];

        const nextState = availableDashboards(initialState, action);
        expect(nextState).toEqual(expectedState);
    });

    it('Should handle SetInitialDashboardInformation and add its value to the existing list', () => {
        const initialState = [];
        const action: AppAction = {
            type: ActionType.SetInitialDashboardInformation,
            value: {
                group: 'home',
                availableDashboards: [
                    { _id: '2', title: 'dashboard 2' },
                    { _id: '3', title: 'dashboard 3' },
                ],
                canEditDashboards: true,
            },
        };

        const expectedState = [
            { _id: '2', title: 'dashboard 2' },
            { _id: '3', title: 'dashboard 3' },
        ];

        const nextState = availableDashboards(initialState, action);
        expect(nextState).toEqual(expectedState);
    });

    it('Should handle RemoveDashboard and remove the dashboard from the list', () => {
        const initialState = [
            { _id: '1', title: 'dashboard 1' },
            { _id: '2', title: 'dashboard 2' },
            { _id: '3', title: 'dashboard 3' },
        ];

        const action: AppAction = {
            type: ActionType.RemoveDashboard,
            value: {
                dashboardId: '2',
                group: 'home',
            },
        };

        const expectedState = [
            { _id: '1', title: 'dashboard 1' },
            { _id: '3', title: 'dashboard 3' },
        ];

        const nextState = availableDashboards(initialState, action);
        expect(nextState).toEqual(expectedState);
    });

    describe('AddDashboard', () => {
        const initialState = [
            { _id: '1', title: 'dashboard 1' },
            { _id: '2', title: 'dashboard 2' },
            { _id: '3', title: 'dashboard 3' },
        ];

        it('should add the dashboard list entry to the list if it is not there yet', () => {
            const action: AppAction = {
                type: ActionType.AddDashboard,
                value: {
                    dashboard: {
                        _id: '4',
                        title: 'dashboard 4',
                        children: [],
                    },
                    group: 'home',
                },
            };

            const expectedState = [
                { _id: '1', title: 'dashboard 1' },
                { _id: '2', title: 'dashboard 2' },
                { _id: '3', title: 'dashboard 3' },
                { _id: '4', title: 'dashboard 4' },
            ];

            const nextState = availableDashboards(initialState, action);
            expect(nextState).toEqual(expectedState);
        });

        it('should update the dashboard list entry if it is already in the list', () => {
            const action: AppAction = {
                type: ActionType.AddDashboard,
                value: {
                    dashboard: {
                        _id: '2',
                        title: 'new title',
                        children: [],
                    },
                    group: 'home',
                },
            };

            const expectedState = [
                { _id: '1', title: 'dashboard 1' },
                { _id: '2', title: 'new title' },
                { _id: '3', title: 'dashboard 3' },
            ];

            const nextState = availableDashboards(initialState, action);
            expect(nextState).toEqual(expectedState);
        });
    });
});
