import { ActionType } from '../../action-types';
import { subscriptions, websocket } from '../websocket';

describe('websocket reducer', () => {
    describe('subscriptions reducer', () => {
        it('should initialize with empty subscriptions', () => {
            const state = subscriptions(undefined, { type: ActionType.ReduxInitialAction });
            expect(state.activeSubscriptions.size).toBe(0);
        });

        describe('SubscribeToEvent', () => {
            it('should add a new subscription', () => {
                const callback = jest.fn();
                const state = subscriptions(undefined, {
                    type: ActionType.SubscribeToEvent,
                    value: { category: 'test', callback },
                });

                expect(state.activeSubscriptions.get('test')?.size).toBe(1);
                expect(state.activeSubscriptions.get('test')?.has(callback)).toBe(true);
            });

            it('should add multiple callbacks to the same category', () => {
                const callback1 = jest.fn();
                const callback2 = jest.fn();

                let state = subscriptions(undefined, {
                    type: ActionType.SubscribeToEvent,
                    value: { category: 'test', callback: callback1 },
                });

                state = subscriptions(state, {
                    type: ActionType.SubscribeToEvent,
                    value: { category: 'test', callback: callback2 },
                });

                expect(state.activeSubscriptions.get('test')?.size).toBe(2);
                expect(state.activeSubscriptions.get('test')?.has(callback1)).toBe(true);
                expect(state.activeSubscriptions.get('test')?.has(callback2)).toBe(true);
            });

            it('should not add the same callback twice', () => {
                const callback = jest.fn();

                let state = subscriptions(undefined, {
                    type: ActionType.SubscribeToEvent,
                    value: { category: 'test', callback },
                });

                state = subscriptions(state, {
                    type: ActionType.SubscribeToEvent,
                    value: { category: 'test', callback },
                });

                expect(state.activeSubscriptions.get('test')?.size).toBe(1);
            });
        });

        describe('UnsubscribeFromEvent', () => {
            it('should remove a subscription', () => {
                const callback = jest.fn();

                let state = subscriptions(undefined, {
                    type: ActionType.SubscribeToEvent,
                    value: { category: 'test', callback },
                });

                state = subscriptions(state, {
                    type: ActionType.UnsubscribeFromEvent,
                    value: { category: 'test', callback },
                });

                expect(state.activeSubscriptions.get('test')?.size).toBe(undefined);
            });

            it('should remove only the specified callback', () => {
                const callback1 = jest.fn();
                const callback2 = jest.fn();

                let state = subscriptions(undefined, {
                    type: ActionType.SubscribeToEvent,
                    value: { category: 'test', callback: callback1 },
                });

                state = subscriptions(state, {
                    type: ActionType.SubscribeToEvent,
                    value: { category: 'test', callback: callback2 },
                });

                state = subscriptions(state, {
                    type: ActionType.UnsubscribeFromEvent,
                    value: { category: 'test', callback: callback1 },
                });

                expect(state.activeSubscriptions.get('test')?.size).toBe(1);
                expect(state.activeSubscriptions.get('test')?.has(callback1)).toBe(false);
                expect(state.activeSubscriptions.get('test')?.has(callback2)).toBe(true);
            });

            it('should remove the category when no callbacks remain', () => {
                const callback = jest.fn();

                let state = subscriptions(undefined, {
                    type: ActionType.SubscribeToEvent,
                    value: { category: 'test', callback },
                });

                state = subscriptions(state, {
                    type: ActionType.UnsubscribeFromEvent,
                    value: { category: 'test', callback },
                });

                expect(state.activeSubscriptions.has('test')).toBe(false);
            });
        });

        describe('TriggerCategoryCallbacks', () => {
            it('should do nothing for non-existent category', () => {
                const state = subscriptions(undefined, {
                    type: ActionType.TriggerCategoryCallbacks,
                    value: { category: 'non-existent', args: [] },
                });

                expect(state.activeSubscriptions.size).toBe(0);
            });
        });

        describe('ClearAllSubscriptions', () => {
            it('should remove all subscriptions', () => {
                const callback1 = jest.fn();
                const callback2 = jest.fn();

                let state = subscriptions(undefined, {
                    type: ActionType.SubscribeToEvent,
                    value: { category: 'test1', callback: callback1 },
                });

                state = subscriptions(state, {
                    type: ActionType.SubscribeToEvent,
                    value: { category: 'test2', callback: callback2 },
                });

                state = subscriptions(state, {
                    type: ActionType.ClearAllSubscriptions,
                });

                expect(state.activeSubscriptions.size).toBe(0);
            });
        });
    });

    describe('websocket reducer', () => {
        it('should combine subscriptions reducer', () => {
            const state = websocket(undefined, { type: ActionType.ReduxInitialAction });
            expect(state).toHaveProperty('subscriptions');
            expect(state.subscriptions.activeSubscriptions.size).toBe(0);
        });
    });
});
