import { ActionType } from '../../action-types';
import { focusPosition } from '../focus-position';

describe('focus position reducer', () => {
    it('should return null as initial state', () => {
        expect(focusPosition(undefined, { type: ActionType.ReduxInitialAction })).toBeNull();
    });

    it('should set the focus position when SetFocusPosition action is dispatched', () => {
        expect(
            focusPosition(undefined, {
                type: ActionType.SetFocusPosition,
                value: {
                    elementId: 'testElementId',
                    screenId: 'testScreenId',
                    nestedField: 'testColumnBin',
                    row: '12',
                },
            }),
        ).toEqual({
            elementId: 'testElementId',
            screenId: 'testScreenId',
            nestedField: 'testColumnBin',
            row: '12',
        });
    });

    it('should unset the focus position when a new dialog is opened', () => {
        expect(
            focusPosition(undefined, {
                type: ActionType.OpenDialog,
                value: {
                    dialog: {} as any,
                    dialogId: 89,
                },
            }),
        ).toEqual(null);
    });
});
