import type { PageControlObject } from '../../../component/control-objects';
import type { PageDefinition } from '../../../service/page-definition';
import { getMockPageDefinition, getMockPageMetadata } from '../../../__tests__/test-helpers';
import type { AppAction } from '../../action-types';
import { ActionType } from '../../action-types';
import { screenDefinitions } from '../screen-definitions';

describe('Reducer', () => {
    const pageId = 'TestPage';

    // TODO replace getMockPageDefinition by getMockScreenDefinition where possible
    describe('Screen definitions', () => {
        it('Should return an empty object as initial state', () => {
            const state = undefined;
            const action: AppAction = { type: ActionType.ReduxInitialAction };
            const expected = {};

            const nextState = screenDefinitions(state, action);

            expect(nextState).toEqual(expected);
        });

        it('Should handle SetPageDefinition action', () => {
            const state = {};
            const action: AppAction = {
                type: ActionType.AddScreenDefinition,
                value: getMockPageDefinition(pageId),
            };
            const expected = { [pageId]: action.value };

            const nextState = screenDefinitions(state, action);

            expect(nextState).toEqual(expected);
        });

        it('Should handle SetValues action', () => {
            const state = { [pageId]: getMockPageDefinition(pageId) };
            const action: AppAction = {
                type: ActionType.SetValues,
                value: {
                    screenId: pageId,
                    values: {
                        _id: 'whatever',
                    },
                },
            };
            const expected = {
                [pageId]: getMockPageDefinition(pageId, {
                    values: action.value.values,
                    selectedRecordId: action.value.values._id,
                }),
            };

            const nexState = screenDefinitions(state, action);

            expect(nexState).toEqual(expected);
        });

        it('should remove any existing validation errors', () => {
            const screenDefinition = getMockPageDefinition(pageId);
            screenDefinition.errors = {
                someField: [
                    {
                        screenId: pageId,
                        elementId: 'someField',
                        validationRule: 'validation',
                        message: 'Some real error!',
                    },
                ],
                someOtherField: [
                    {
                        screenId: pageId,
                        elementId: 'someOtherField',
                        validationRule: 'validation',
                        message: 'Some other serious error.',
                    },
                ],
            };
            const state = { [pageId]: getMockPageDefinition(pageId) };
            const action: AppAction = {
                type: ActionType.SetValues,
                value: {
                    screenId: pageId,
                    values: {
                        _id: 'whatever',
                    },
                },
            };
            const expected = {
                [pageId]: getMockPageDefinition(pageId, {
                    values: action.value.values,
                    selectedRecordId: action.value.values._id,
                    errors: {},
                }),
            };

            const nexState = screenDefinitions(state, action);

            expect(nexState).toEqual(expected);
        });

        it('Should handle SetFieldValue action and add the value to the state as a dict', () => {
            const state = {
                [pageId]: getMockPageDefinition(pageId, {
                    values: {
                        '01': 'test01',
                    },
                }),
            };
            const action: AppAction = {
                type: ActionType.SetFieldValue,
                value: {
                    screenId: pageId,
                    elementId: '02',
                    fieldValue: 'test02',
                    isOrganicChange: false,
                },
            };
            const expected = {
                [pageId]: getMockPageDefinition(pageId, {
                    values: {
                        '01': 'test01',
                        '02': 'test02',
                    },
                }),
            };

            const nextState = screenDefinitions(state, action);

            expect(nextState).toEqual(expected);
        });

        it('Should handle SetUiComponentProperties action and add value.fieldProperties to the state as a dict', () => {
            const state = {
                [pageId]: getMockPageDefinition(pageId, {
                    metadata: getMockPageMetadata(pageId, {
                        uiComponentProperties: {
                            '01': {
                                title: 'Test title',
                            },
                        },
                    }),
                }),
            };
            const action: AppAction = {
                type: ActionType.SetUiComponentProperties,
                value: {
                    screenId: pageId,
                    elementId: '02',
                    fieldProperties: {
                        title: 'Yet another title',
                    },
                },
            };
            const expected = {
                [pageId]: getMockPageDefinition(pageId, {
                    metadata: getMockPageMetadata(pageId, {
                        uiComponentProperties: {
                            '01': {
                                title: 'Test title',
                            },
                            '02': {
                                title: 'Yet another title',
                            },
                        },
                    }),
                }),
            };

            const nextState = screenDefinitions(state, action);

            expect(nextState).toEqual(expected);
        });

        it('Should handle CommitTransaction action and add dirty values to the state dict', () => {
            const state = {
                [pageId]: getMockPageDefinition(pageId, {
                    metadata: getMockPageMetadata(pageId, {
                        uiComponentProperties: {
                            '01': {
                                title: 'First title',
                            },
                            '02': {
                                title: 'Second title',
                            },
                        },
                    }),
                    values: {
                        '01': 'First value',
                        '02': 'Second value',
                    },
                }),
            };

            const action: AppAction = {
                type: ActionType.CommitTransaction,
                value: {
                    screenId: pageId,
                    transaction: {
                        values: {
                            '01': {
                                value: 'Should modify value',
                                hasChangedInTransaction: true,
                            },
                            '02': {
                                value: 'Should not modify value',
                                hasChangedInTransaction: false,
                            },
                            '03': {
                                value: 'Should add value',
                                hasChangedInTransaction: true,
                            },
                            '04': {
                                value: 'Should not add value',
                                hasChangedInTransaction: false,
                            },
                        },
                        uiComponentProperties: {
                            '01': {
                                value: {
                                    title: 'Should modify title',
                                },
                                hasChangedInTransaction: true,
                            },
                            '02': {
                                value: {
                                    title: 'Should not modify title',
                                },
                                hasChangedInTransaction: false,
                            },
                            '03': {
                                value: {
                                    title: 'Should add title',
                                },
                                hasChangedInTransaction: true,
                            },
                            '04': {
                                value: {
                                    title: 'Should not add title',
                                },
                                hasChangedInTransaction: false,
                            },
                        },
                    },
                },
            };

            const expected = {
                [pageId]: getMockPageDefinition(pageId, {
                    metadata: getMockPageMetadata(pageId, {
                        uiComponentProperties: {
                            '01': {
                                title: 'Should modify title',
                            },
                            '02': {
                                title: 'Second title',
                            },
                            '03': {
                                title: 'Should add title',
                            },
                        },
                    }),
                    values: {
                        '01': 'Should modify value',
                        '02': 'Second value',
                        '03': 'Should add value',
                    },
                }),
            };

            const nextState = screenDefinitions(state, action);

            expect(nextState).toEqual(expected);
        });

        it('Should handle SetErrors and add value to the state dict', () => {
            const state = {
                [pageId]: getMockPageDefinition(pageId, {
                    errors: {
                        '02': [
                            {
                                elementId: '02',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                    },
                }),
            };
            const action: AppAction = {
                type: ActionType.SetErrors,
                value: {
                    screenId: pageId,
                    elementId: '01',
                    errors: [
                        {
                            elementId: '01',
                            screenId: pageId,
                            validationRule: 'validation',
                            message: 'error-message',
                        },
                    ],
                },
            };
            const expected = {
                [pageId]: getMockPageDefinition(pageId, {
                    errors: {
                        '01': [
                            {
                                elementId: '01',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                        '02': [
                            {
                                elementId: '02',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                    },
                }),
            };

            const nextState = screenDefinitions(state, action);

            expect(nextState).toEqual(expected);
        });

        it('Should handle RemoveError and remove the value from the state dict', () => {
            const state = {
                [pageId]: getMockPageDefinition(pageId, {
                    errors: {
                        '01': [
                            {
                                elementId: '01',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                        '02': [
                            {
                                elementId: '02',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                    },
                }),
            };
            const action: AppAction = {
                type: ActionType.RemoveNonNestedError,
                value: {
                    screenId: pageId,
                    elementId: '01',
                },
            };
            const expected = {
                [pageId]: getMockPageDefinition(pageId, {
                    errors: {
                        '02': [
                            {
                                elementId: '02',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                    },
                }),
            };

            const nextState = screenDefinitions(state, action);

            expect(nextState).toEqual(expected);
        });

        it('Should handle UpdateErrors and apply it to the state', () => {
            const state = {
                [pageId]: getMockPageDefinition(pageId, {
                    errors: {
                        '01': [
                            {
                                elementId: '01',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                        '02': [
                            {
                                elementId: '02',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                        '03': [
                            {
                                elementId: '03',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                    },
                }),
            };
            const action: AppAction = {
                type: ActionType.UpdateErrors,
                value: {
                    screenId: pageId,
                    validationErrors: {
                        '01': [
                            {
                                elementId: '01',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                        '02': undefined,
                        '03': undefined,
                        '04': [
                            {
                                elementId: '04',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                    },
                },
            };
            const expected = {
                [pageId]: getMockPageDefinition(pageId, {
                    errors: {
                        '01': [
                            {
                                elementId: '01',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                        '04': [
                            {
                                elementId: '04',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                    },
                }),
            };

            const nextState = screenDefinitions(state, action);

            expect(nextState).toEqual(expected);
        });

        it('Should remove a screen definition on RemoveScreenDefinition', () => {
            const initialState = {
                [pageId]: getMockPageDefinition(pageId),
            };
            const action: AppAction = {
                type: ActionType.RemoveScreenDefinition,
                value: pageId,
            };
            const expectedState = {};

            const nextState = screenDefinitions(initialState, action);

            expect(nextState).toEqual(expectedState);
        });

        it('Should handle SetScreenDefinitionReady action', () => {
            const initialState = {
                [pageId]: getMockPageDefinition(pageId, { isReady: false }),
            };
            const action: AppAction = {
                type: ActionType.SetScreenDefinitionReady,
                value: pageId,
            };
            const expected = { [pageId]: getMockPageDefinition(pageId) };

            const nextState = screenDefinitions(initialState, action);

            expect(nextState).toEqual(expected);
        });

        it('Should handle SetScreenDefinitionDialogId action', () => {
            const pageDefinition = getMockPageDefinition(pageId);
            const initialState = {
                [pageId]: pageDefinition,
            };
            const pageControlObject = pageDefinition.metadata.controlObjects[pageId] as PageControlObject;
            const action: AppAction = {
                type: ActionType.SetScreenDefinitionDialogId,
                value: { screenId: pageId, dialogId: 1, pageControlObject, title: 'Test', subtitle: 'test subtitle' },
            };
            const expected = { [pageId]: getMockPageDefinition(pageId, { dialogId: 1 }) };

            const nextState = screenDefinitions(initialState, action);

            expect(nextState).toEqual(expected);
        });

        it('Should handle set query parameter on the SetQueryParameter action', () => {
            const initialState = {
                [pageId]: getMockPageDefinition(pageId),
            };
            const action: AppAction = {
                type: ActionType.SetQueryParameter,
                value: { screenId: pageId, parameterName: 'test', value: '12345' },
            };

            const nextState = screenDefinitions(initialState, action);
            expect((nextState[pageId] as PageDefinition).queryParameters).toEqual({ test: '12345' });
        });

        it('Should handle dirty states for a given element on the SetFieldDirtyState action', () => {
            const initialState = {
                [pageId]: getMockPageDefinition(pageId),
            };
            const action: AppAction = {
                type: ActionType.SetFieldDirtyState,
                value: { screenId: pageId, elementId: 'elementId' },
            };

            const nextState = screenDefinitions(initialState, action);
            expect((nextState[pageId] as PageDefinition).dirtyStates).toEqual({
                elementId: true,
            });
        });
        it('Should handle RemovePageServerErrors and remove the value of server errors from the state dict', () => {
            const state = {
                [pageId]: getMockPageDefinition(pageId, {
                    errors: {
                        '01': [
                            {
                                elementId: '01',
                                screenId: pageId,
                                validationRule: 'server-validation',
                                message: 'error-message',
                            },
                        ],
                        '02': [
                            {
                                elementId: '02',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                    },
                }),
            };
            const action: AppAction = {
                type: ActionType.RemovePageServerErrors,
                value: {
                    screenId: pageId,
                },
            };
            const expected = {
                [pageId]: getMockPageDefinition(pageId, {
                    errors: {
                        '01': [],
                        '02': [
                            {
                                elementId: '02',
                                screenId: pageId,
                                validationRule: 'validation',
                                message: 'error-message',
                            },
                        ],
                    },
                }),
            };

            const nextState = screenDefinitions(state, action);

            expect(nextState).toEqual(expected);
        });
    });
});
