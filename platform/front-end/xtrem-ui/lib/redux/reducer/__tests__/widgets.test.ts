import { AbstractWidget, WidgetType } from '../../../dashboard/widgets/abstract-widget';
import type { AppAction } from '../../action-types';
import { ActionType } from '../../action-types';
import { widgets as reducer } from '../dashboard-group';

class TestWidget extends AbstractWidget {}

describe('Reducer', () => {
    describe('widgets', () => {
        it('should return initial state', () => {
            expect(reducer(undefined, { type: ActionType.ReduxInitialAction })).toEqual({});
        });

        it('should clear selected options when ClearWidgetOptions is triggered', () => {
            const state = {
                '1337': {
                    _id: '1337',
                    artifactName: 'test-widget',
                    data: { foo: 'foo', bar: 'bar' },
                    options: {
                        selectedItems: ['2'],
                        dataOptions: {
                            '1': 'Option #1',
                            '2': 'Option #2',
                        },
                    },
                    properties: {
                        title: 'Widget Title',
                        getQuery() {
                            return 'Query goes here...';
                        },
                    },
                    widgetObject: TestWidget,
                    widgetType: WidgetType.staticContent,
                },
            };

            const action: AppAction = { type: ActionType.ClearWidgetOptions, value: null };

            const expected = {
                '1337': { ...state['1337'], options: undefined },
            };

            expect(reducer(state as any, action)).toEqual(expected);
        });
    });
});
