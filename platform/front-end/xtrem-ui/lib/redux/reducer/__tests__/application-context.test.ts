import { getMockState } from '../../../__tests__/test-helpers';
import type { AppAction } from '../../action-types';
import { ActionType } from '../../action-types';
import { applicationContext } from '../application-context';

describe('Reducer', () => {
    describe('applicationContext', () => {
        it('Should return null as initial state', () => {
            const initialState = undefined;
            const action: AppAction = { type: ActionType.ReduxInitialAction };
            const expectedState = null;

            const nextState = applicationContext(initialState, action);
            expect(nextState).toBe(expectedState);
        });

        it('Should handle SetApplicationContext and set value as state', () => {
            const initialState = undefined;
            const action: AppAction = {
                type: ActionType.SetApplicationContext,
                value: getMockState().applicationContext!,
            };
            const expectedState = action.value;

            const nextState = applicationContext(initialState, action);
            expect(nextState).toEqual(expectedState);
        });
    });
});
