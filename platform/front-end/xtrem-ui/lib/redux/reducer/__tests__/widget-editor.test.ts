import { ActionType } from '../../action-types';
import { isDirty, isOpen, widgetDefinition } from '../widget-editor';

describe('widget editor reducer', () => {
    describe('isOpen', () => {
        it('should return false by default', () => {
            const result = isOpen(undefined, { type: ActionType.ReduxInitialAction });
            expect(result).toEqual(false);
        });

        it('should return the action value when called with SetWidgetEditorOpen', () => {
            let result = isOpen(undefined, {
                type: ActionType.SetWidgetEditorOpen,
                value: { isOpen: true, group: 'home' },
            });
            expect(result).toEqual(true);
            result = isOpen(undefined, {
                type: ActionType.SetWidgetEditorOpen,
                value: { isOpen: false, group: 'home' },
            });
            expect(result).toEqual(false);
        });
    });

    describe('isDirty', () => {
        it('should be clean by default', () => {
            const result = isDirty(undefined, { type: ActionType.ReduxInitialAction });
            expect(result).toEqual(false);
        });

        it('should be dirty when user changes are pushed to the state using UpdateUserWidgetDefinition', () => {
            const result = isDirty(undefined, {
                type: ActionType.UpdateUserWidgetDefinition,
                value: { widget: { title: 'Test' }, group: 'test' },
            });
            expect(result).toEqual(true);
        });

        it('should be clean when the dashboard editor is closed', () => {
            const result = isDirty(undefined, {
                type: ActionType.SetWidgetEditorOpen,
                value: { isOpen: false, group: 'home' },
            });
            expect(result).toEqual(false);
        });
    });

    describe('widgetDefinition', () => {
        it('should return empty object by default', () => {
            const result = widgetDefinition(undefined, { type: ActionType.ReduxInitialAction });
            expect(result).toEqual({});
        });

        it('should reset the value when the editor dialog is closed', () => {
            const result = widgetDefinition(
                { title: 'some widget' },
                { type: ActionType.SetWidgetEditorOpen, value: { isOpen: false, group: 'home' } },
            );
            expect(result).toEqual({});
        });

        it('should return value of the UpdateUserWidgetDefinition action', () => {
            const result = widgetDefinition(undefined, {
                type: ActionType.UpdateUserWidgetDefinition,
                value: { widget: { title: 'test' }, group: 'test' },
            });
            expect(result).toEqual({ title: 'test' });
        });
    });
});
