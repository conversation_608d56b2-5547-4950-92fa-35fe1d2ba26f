import { ActionType } from '../../action-types';
import { enumTypes } from '../enum-types';

describe('enum type reducer', () => {
    it('should default to an empty object', () => {
        const result = enumTypes(undefined, { type: ActionType.AddEnumType, value: { enums: {}, locale: 'en-US' } });
        expect(result).toEqual({});
    });

    it('should add enum definitions to the state', () => {
        const result = enumTypes(
            {},
            {
                type: ActionType.AddEnumType,
                value: {
                    locale: 'en-US',
                    enums: {
                        MyNewEnum: {
                            name: '@sage/xtrem-test/MyNewEnum',
                            values: ['property1', 'property2'],
                            translations: [],
                        },
                        AnotherNewEnum: {
                            name: '@sage/xtrem-test/AnotherNewEnum',
                            values: ['property3', 'property4'],
                            translations: [],
                        },
                    },
                },
            },
        );

        expect(result).toEqual({
            MyNewEnum: ['property1', 'property2'],
            AnotherNewEnum: ['property3', 'property4'],
        });
    });

    it('should only add new enum definitions', () => {
        const result = enumTypes(
            {
                MyNewEnum: ['property1', 'property2'],
            },
            {
                type: ActionType.AddEnumType,
                value: {
                    enums: {
                        AnotherNewEnum: {
                            name: '@sage/xtrem-test/AnotherNewEnum',
                            values: ['property3', 'property4'],
                            translations: [],
                        },
                    },
                    locale: 'en-US',
                },
            },
        );

        expect(result).toEqual({
            MyNewEnum: ['property1', 'property2'],
            AnotherNewEnum: ['property3', 'property4'],
        });
    });
});
