import type { Dashboard } from '@sage/xtrem-shared';
import { ActionType } from '../../action-types';
import { currentDashboardDefinition, currentHistoryIndex, history, isDirty, isOpen } from '../dashboard-editor';

describe('dashboard editor reducer', () => {
    describe('isOpen', () => {
        it('should return false by default', () => {
            const result = isOpen(undefined, { type: ActionType.ReduxInitialAction });
            expect(result).toEqual(false);
        });

        it('should return true when the dashboard editor is opened with a document', () => {
            const result = isOpen(undefined, {
                type: ActionType.OpenDashboardEditor,
                value: { dashboard: { _id: '-3', title: 'test', children: [] }, group: 'home' },
            });
            expect(result).toEqual(true);
        });

        it('should return the action value when called with SetDashboardEditorDialogOpen', () => {
            let result = isOpen(undefined, {
                type: ActionType.SetDashboardEditorDialogOpen,
                value: { isOpen: true, group: 'home' },
            });
            expect(result).toEqual(true);
            result = isOpen(undefined, {
                type: ActionType.SetDashboardEditorDialogOpen,
                value: { isOpen: false, group: 'home' },
            });
            expect(result).toEqual(false);
        });
    });

    describe('history', () => {
        it('should return an empty array by default', () => {
            const result = history(undefined, { type: ActionType.ReduxInitialAction });
            expect(result).toEqual([]);
        });

        it('should reset the history when the editor is opened with a document', () => {
            const previousState: Dashboard[] = [
                { _id: '-3', title: 'test 3', children: [] },
                { _id: '-2', title: 'test 2', children: [] },
            ];
            const result = history(previousState, {
                type: ActionType.OpenDashboardEditor,
                value: { dashboard: { _id: '-3', title: 'test', children: [] }, group: 'home' },
            });
            expect(result).toEqual([{ _id: '-3', title: 'test', children: [] }]);
        });

        it('should use the history property from the UpdateDashboardEditorWithHistory action', () => {
            const previousState: Dashboard[] = [{ _id: '-2', title: 'test 2', children: [] }];
            const result = history(previousState, {
                type: ActionType.UpdateDashboardEditorWithHistory,
                value: {
                    group: 'home',
                    dashboard: { _id: '-3', title: 'test', children: [] },
                    history: [
                        { _id: '-3', title: 'test 3', children: [] },
                        { _id: '-2', title: 'test 2', children: [] },
                    ],
                },
            });

            expect(result).toEqual([
                { _id: '-3', title: 'test 3', children: [] },
                { _id: '-2', title: 'test 2', children: [] },
            ]);
        });
    });

    describe('currentHistoryIndex', () => {
        it('should initiate with zero', () => {
            const result = currentHistoryIndex(undefined, { type: ActionType.ReduxInitialAction });
            expect(result).toEqual(0);
        });

        it('should increment the index by one on UpdateDashboardEditorWithHistory', () => {
            const result = currentHistoryIndex(1, {
                type: ActionType.UpdateDashboardEditorWithHistory,
                value: {
                    group: 'home',
                    dashboard: { _id: '-3', title: 'test', children: [] },
                    history: [
                        { _id: '-3', title: 'test 3', children: [] },
                        { _id: '-2', title: 'test 2', children: [] },
                    ],
                },
            });

            expect(result).toEqual(2);
        });

        it('should the index from the action value when it is update from history', () => {
            const result = currentHistoryIndex(1, {
                type: ActionType.UpdateDashboardEditorFromHistory,
                value: {
                    group: 'home',
                    historyIndex: 3,
                    dashboard: { _id: '-3', title: 'test', children: [] },
                },
            });

            expect(result).toEqual(3);
        });
    });

    describe('currentDashboardDefinition', () => {
        it('should initialize with a fake dashboard', () => {
            const result = currentDashboardDefinition(undefined, { type: ActionType.ReduxInitialAction });
            expect(result).toEqual({
                _id: '-1',
                children: [],
                title: '',
            });
        });

        it('should take the value from the action when the dashboard editor is opened', () => {
            const dashboard: Dashboard = {
                _id: '-5',
                children: [],
                title: 'My new dashboard',
            };

            const result = currentDashboardDefinition(undefined, {
                type: ActionType.OpenDashboardEditor,
                value: { dashboard, group: 'home' },
            });

            expect(result).toEqual(dashboard);
        });

        it('should take the dashboard property value when called with UpdateDashboardEditorFromHistory', () => {
            const dashboard: Dashboard = {
                _id: '-5',
                children: [],
                title: 'My new dashboard',
            };

            const result = currentDashboardDefinition(undefined, {
                type: ActionType.UpdateDashboardEditorFromHistory,
                value: { dashboard, historyIndex: 1, group: 'home' },
            });

            expect(result).toEqual(dashboard);
        });

        it('should take the dashboard property value when called with UpdateDashboardEditorWithHistory', () => {
            const dashboard: Dashboard = {
                _id: '-5',
                children: [],
                title: 'My new dashboard',
            };

            const result = currentDashboardDefinition(undefined, {
                type: ActionType.UpdateDashboardEditorWithHistory,
                value: { dashboard, history: [], group: 'home' },
            });

            expect(result).toEqual(dashboard);
        });

        it('should take the dashboard property value when called with AddDashboard', () => {
            const dashboard: Dashboard = {
                _id: '-5',
                children: [],
                title: 'My new dashboard',
            };

            const result = currentDashboardDefinition(undefined, {
                type: ActionType.AddDashboard,
                value: { dashboard, group: 'home' },
            });

            expect(result).toEqual(dashboard);
        });
    });

    describe('isDirty', () => {
        it('should be clean by default', () => {
            const result = isDirty(undefined, { type: ActionType.ReduxInitialAction });
            expect(result).toEqual(false);
        });

        it('should be clean up when the dashboard editor is getting closed SetDashboardEditorDialogOpen', () => {
            const result = isDirty(undefined, {
                type: ActionType.SetDashboardEditorDialogOpen,
                value: { isOpen: false, group: 'home' },
            });
            expect(result).toEqual(false);
        });

        it('should be dirty when user changes are pushed to the state using UpdateDashboardEditorWithHistory', () => {
            const dashboard: Dashboard = {
                _id: '-5',
                children: [],
                title: 'My new dashboard',
            };

            const result = isDirty(undefined, {
                type: ActionType.UpdateDashboardEditorWithHistory,
                value: { dashboard, history: [], group: 'home' },
            });
            expect(result).toEqual(true);
        });

        it('should be clean when a new dashboard is added to the state', () => {
            const dashboard: Dashboard = {
                _id: '-5',
                children: [],
                title: 'My new dashboard',
            };

            const result = isDirty(undefined, { type: ActionType.AddDashboard, value: { dashboard, group: 'home' } });
            expect(result).toEqual(false);
        });
    });
});
