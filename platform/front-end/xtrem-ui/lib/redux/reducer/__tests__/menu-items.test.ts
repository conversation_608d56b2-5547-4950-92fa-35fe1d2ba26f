import type { AppAction } from '../../action-types';
import { ActionType } from '../../action-types';
import type { Menu } from '../../state';
import { menuItems } from '../menu-items';

describe('Reducer', () => {
    describe('menuItems', () => {
        let initialState: Menu[] = [];
        const menuItemId = 'TestMenuItem';

        it('Should return [] as initial state', () => {
            expect(menuItems(undefined, { type: ActionType.ReduxInitialAction })).toEqual([]);
        });

        it('Should handle AddMenuItem and add the action value to the state', () => {
            const newMenuItem: Menu = {
                id: menuItemId,
                title: 'Whatever',
                onClick: () => {},
                category: 'any',
            };
            const expectedState = [newMenuItem];

            const nextState: Menu[] = menuItems(initialState, {
                type: ActionType.AddMenuItem,
                value: newMenuItem,
            });

            expect(nextState).toEqual(expectedState);
        });

        describe('UpdateMenuItem action', () => {
            beforeEach(() => {
                initialState = [{ id: menuItemId, badgeContent: 'A' } as Menu];
            });

            const runUpdateMenuItemTest = (badgeContentValue: string, badgeContentExpectedValue: string) => {
                const action: AppAction = {
                    type: ActionType.UpdateMenuItem,
                    value: {
                        id: menuItemId,
                        badgeContent: badgeContentValue,
                    },
                };
                const expectedState: Menu[] = [{ id: menuItemId, badgeContent: badgeContentExpectedValue } as Menu];
                const nextState: Menu[] = menuItems(initialState, action);
                expect(nextState).toEqual(expectedState);
            };

            it('Should update the menu icon badge content', () => {
                runUpdateMenuItemTest('B', 'B');
            });

            it('Should update the menu icon badge content - Number less than 100', () => {
                runUpdateMenuItemTest('99', '99');
            });

            it('Should update the menu icon badge content - Number greater or equal than 100', () => {
                runUpdateMenuItemTest('100', '100');
            });

            it('Should update the menu icon badge content - empty value payload', () => {
                const action: AppAction = {
                    type: ActionType.UpdateMenuItem,
                    value: {
                        id: menuItemId,
                    },
                };
                const expectedState: Menu[] = [{ id: menuItemId, badgeContent: '' } as Menu];
                const nextState: Menu[] = menuItems(initialState, action);
                expect(nextState).toEqual(expectedState);
            });
        });

        it('Should handle RemoveMenuItem and remove the action value from the state', () => {
            const menuItem = { id: menuItemId } as Menu;
            initialState = [menuItem];
            const expectedState: Menu[] = [];

            const nextState: Menu[] = menuItems(initialState, {
                type: ActionType.RemoveMenuItem,
                value: menuItemId,
            });

            expect(nextState).toEqual(expectedState);
        });

        it('Should update title on page dialog after it is loaded', () => {
            const menuItem = { id: menuItemId, dialogId: 3, title: 'Loading...' } as Menu;
            initialState = [menuItem];

            const nextState: Menu[] = menuItems(initialState, {
                type: ActionType.SetScreenDefinitionDialogId,
                value: {
                    dialogId: 3,
                    title: 'My New Title',
                    screenId: 'MyScreen',
                } as any,
            });

            expect(nextState[0].title).toEqual('My New Title');
        });
    });
});
