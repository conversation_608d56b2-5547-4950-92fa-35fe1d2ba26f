import { PageControlObject } from '../../../component/control-objects';
import type { DialogDescription } from '../../../types/dialogs';
import type { Dict } from '@sage/xtrem-shared';
import type { AppAction } from '../../action-types';
import { ActionType } from '../../action-types';
import { activeDialogs } from '../active-dialogs';

describe('Reducer', () => {
    describe('Active dialogs', () => {
        it('Should return empty object as initial state', () => {
            const state = undefined;
            const action: AppAction = { type: ActionType.ReduxInitialAction };
            const expected = {};

            const nextState = activeDialogs(state, action);
            expect(nextState).toEqual(expected);
        });

        it('Should handle OpenDialog and add value as a dict', () => {
            const state: Dict<DialogDescription> = {
                1: {
                    title: 'First dialog',
                } as DialogDescription,
            };
            const actionValue = {
                dialogId: 2,
                dialog: {
                    title: 'Second dialog',
                } as DialogDescription,
            };
            const expected = {
                1: {
                    title: 'First dialog',
                } as DialogDescription,
                2: {
                    title: 'Second dialog',
                } as DialogDescription,
            };
            expect(
                activeDialogs(state, {
                    type: ActionType.OpenDialog,
                    value: actionValue,
                }),
            ).toEqual(expected);
        });

        it('Should handle CloseDialog and remove the value from the dict', () => {
            const state: Dict<DialogDescription> = {
                1: {
                    title: 'First dialog',
                } as DialogDescription,
                2: {
                    title: 'Second dialog',
                } as DialogDescription,
            };
            const expected: Dict<DialogDescription> = {
                2: {
                    title: 'Second dialog',
                } as DialogDescription,
            };
            expect(
                activeDialogs(state, {
                    type: ActionType.CloseDialog,
                    value: 1,
                }),
            ).toEqual(expected);
        });

        it('Should handle SetScreenDefinitionDialogId and assign the screen definition to the corresponding dialog', () => {
            const screenId = 'TestPage';
            const state: Dict<DialogDescription> = {
                1: {
                    title: 'First dialog',
                } as DialogDescription,
                2: {
                    title: 'Second dialog',
                } as DialogDescription,
            };

            const pageControlObject = new PageControlObject({
                screenId,
                getUiComponentProperties: jest.fn().mockReturnValue({ title: 'Test title', subtitle: 'My Subtitle' }),
                setUiComponentProperties: jest.fn(),
                dispatchPageValidation: jest.fn(),
                layout: null,
                getFocussedField: jest.fn(),
            });

            expect(
                activeDialogs(state, {
                    type: ActionType.SetScreenDefinitionDialogId,
                    value: {
                        dialogId: 2,
                        screenId: 'TestPage',
                        pageControlObject,
                        title: 'Test title',
                        subtitle: 'My Subtitle',
                    },
                }),
            ).toEqual({
                1: {
                    title: 'First dialog',
                } as DialogDescription,
                2: {
                    title: undefined,
                    subtitle: 'My Subtitle',
                    content: pageControlObject,
                    screenId,
                } as DialogDescription,
            });
        });
    });
});
