import type { Toast } from '../../../service/toast-service';
import type { AppAction } from '../../action-types';
import { ActionType } from '../../action-types';
import { toasts } from '../toasts';

describe('toasts', () => {
    it('Should return an empty array as initial state', () => {
        const currentState = undefined;
        const action: AppAction = { type: ActionType.ReduxInitialAction };
        const expectedState: Toast[] = [];

        const nextState = toasts(currentState, action);

        expect(nextState).toEqual(expectedState);
    });

    it('Should add a toast to the state on ShowToast', () => {
        const currentState: Toast[] = [];
        const action: AppAction = {
            type: ActionType.ShowToast,
            value: {
                content: 'Toast message',
                id: 1,
                isDismissed: false,
                timeout: 4000,
                type: 'success',
            },
        };
        const expectedState: Toast[] = [action.value];

        const nextState = toasts(currentState, action);

        expect(nextState).toEqual(expectedState);
    });

    it('Should remove a toast from the state on RemoveToast if all current toasts are dismissed', () => {
        const currentState: Toast[] = [
            {
                content: 'Toast message',
                id: 1,
                isDismissed: false,
                timeout: 4000,
                type: 'success',
            },
            {
                content: 'Toast message',
                id: 2,
                isDismissed: true,
                timeout: 4000,
                type: 'success',
            },
        ];
        const action: AppAction = {
            type: ActionType.RemoveToast,
            value: currentState[0].id,
        };
        const expectedState: Toast[] = [];

        const nextState = toasts(currentState, action);

        expect(nextState).toEqual(expectedState);
    });

    it('Should update the isDismissed from the state on RemoveToast if not all toasts are dismissed', () => {
        const currentState: Toast[] = [
            {
                content: 'Toast message',
                id: 1,
                isDismissed: false,
                timeout: 4000,
                type: 'success',
            },
            {
                content: 'Toast message',
                id: 2,
                isDismissed: false,
                timeout: 4000,
                type: 'success',
            },
        ];
        const action: AppAction = {
            type: ActionType.RemoveToast,
            value: currentState[0].id,
        };
        const expectedState: Toast[] = [
            {
                content: 'Toast message',
                id: 1,
                isDismissed: true,
                timeout: 4000,
                type: 'success',
            },
            {
                content: 'Toast message',
                id: 2,
                isDismissed: false,
                timeout: 4000,
                type: 'success',
            },
        ];

        const nextState = toasts(currentState, action);

        expect(nextState).toEqual(expectedState);
    });

    it('should remove all toasts on RemoveToasts', () => {
        const currentState: Toast[] = [
            {
                content: 'Toast message',
                id: 1,
                timeout: 4000,
                isDismissed: true,
                type: 'success',
            },
            {
                content: 'Toast message 4',
                id: 2,
                timeout: 4000,
                isDismissed: false,
                type: 'warning',
            },
            {
                content: 'Toast message 2',
                id: 143,
                timeout: 4000,
                isDismissed: false,
                type: 'info',
            },
        ];
        const action: AppAction = {
            type: ActionType.RemoveToasts,
            value: null,
        };

        const nextState = toasts(currentState, action);

        expect(nextState).toEqual([]);
    });
});
