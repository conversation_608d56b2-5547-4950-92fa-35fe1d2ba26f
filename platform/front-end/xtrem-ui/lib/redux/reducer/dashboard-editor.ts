import type { Dashboard } from '@sage/xtrem-shared';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { DashboardEditorState } from '../state';
import type { Reducer } from 'redux';
import { combineReducers } from 'redux';
import { cloneDeep } from 'lodash';

/**
 * In order to prevent a number of potential null compiler warnings (`!`), here a fake dashboard definition is set.
 * When the editor opens, a real dashboard definition is loaded anyways.
 *  */
const FAKE_DASHBOARD: Dashboard = {
    _id: '-1',
    children: [],
    title: '',
};

export const isOpen = (state = false, action: AppAction): boolean => {
    if (action.type === ActionType.OpenDashboardEditor) {
        return true;
    }

    if (action.type === ActionType.SetDashboardEditorDialogOpen) {
        return action.value.isOpen;
    }

    return state;
};

export const history = (state: Dashboard[] = [], action: AppAction): Dashboard[] => {
    if (action.type === ActionType.OpenDashboardEditor) {
        return [cloneDeep(action.value.dashboard)];
    }
    if (action.type === ActionType.UpdateDashboardEditorWithHistory) {
        return action.value.history;
    }

    return state;
};

export const currentHistoryIndex = (state = 0, action: AppAction): number => {
    if (action.type === ActionType.SetDashboardEditorDialogOpen && !action.value) {
        return 0;
    }

    if (action.type === ActionType.OpenDashboardEditor) {
        return 0;
    }

    if (action.type === ActionType.UpdateDashboardEditorFromHistory) {
        return action.value.historyIndex;
    }

    if (action.type === ActionType.UpdateDashboardEditorWithHistory) {
        return state + 1;
    }

    return state;
};

export const currentDashboardDefinition = (state = FAKE_DASHBOARD, action: AppAction): Dashboard => {
    // The add dashboard action is triggered when the user selects a different tab, so it should be loaded into the editor
    if (action.type === ActionType.AddDashboard) {
        return cloneDeep(action.value.dashboard);
    }

    if (action.type === ActionType.OpenDashboardEditor) {
        return cloneDeep(action.value.dashboard);
    }

    if (action.type === ActionType.UpdateDashboardEditorFromHistory) {
        return cloneDeep(action.value.dashboard);
    }

    if (action.type === ActionType.UpdateDashboardEditorWithHistory) {
        return cloneDeep(action.value.dashboard);
    }

    return state;
};

export const isDirty = (state = false, action: AppAction): boolean => {
    // When the dashboard editor is closed, the dirty state is cleaned
    if (action.type === ActionType.SetDashboardEditorDialogOpen && !action.value) {
        return false;
    }

    // When a new change pushed from the user, we should mark the editor dirty
    if (
        action.type === ActionType.UpdateDashboardEditorWithHistory ||
        action.type === ActionType.UpdateDashboardEditorFromHistory
    ) {
        return true;
    }

    // It is triggered when the dashboard is saved
    if (action.type === ActionType.AddDashboard) {
        return false;
    }

    return state;
};

export const dashboardEditor: Reducer<DashboardEditorState, AppAction> = combineReducers<
    DashboardEditorState,
    AppAction
>({
    currentDashboardDefinition,
    currentHistoryIndex,
    history,
    isDirty,
    isOpen,
});
