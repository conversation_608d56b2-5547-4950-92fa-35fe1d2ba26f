import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';

export const isKeyboardShortcutsEnabled = (state = true, action: AppAction): boolean => {
    switch (action.type) {
        case ActionType.SetGlobalLoading:
            return !action.value;
        case ActionType.SetValues:
            return true;
        case ActionType.SetKeyboardShortcutsEnabled:
            return !!action.value;
        default:
            return state;
    }
};
