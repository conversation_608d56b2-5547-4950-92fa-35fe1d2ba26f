import type { Dict } from '@sage/xtrem-shared';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';

export const applicationPackages = (state: Dict<string> | null = null, action: AppAction): Dict<string> | null => {
    if (action.type === ActionType.SetInitialMetaData) {
        return action.value.installedPackages.reduce((prevValue, currentValue) => {
            prevValue[currentValue.name] = currentValue.version;
            return prevValue;
        }, {} as Dict<string>);
    }
    return state;
};
