import { activeDialogs } from './active-dialogs';
import { activeLookupDialog } from './current-lookup-dialog';
import { applicationContext } from './application-context';
import { applicationPackages } from './application-packages';
import { browser } from './browser';
import { combineReducers } from 'redux';
import { customizationWizardPage } from './customization-wizard-page';
import { dashboard } from './dashboard';
import { dataTypes } from './data-types';
import { enumTypes } from './enum-types';
import { exportConfigurationPage } from './export-configuration-page';
import { focusPosition } from './focus-position';
import { isKeyboardShortcutsEnabled } from './is-keyboard-shortcuts-enabled';
import { loading } from './loading';
import { menuItems } from './menu-items';
import { nodeTypes } from './node-type';
import { path } from './path';
import { plugins } from './plugins';
import { printingSettings } from './printing-settings';
import { screenDefinitions } from './screen-definitions';
import { serviceOptions } from './service-options';
import { toasts } from './toasts';
import { translations } from './translations';
import { workflowNodes } from './workflow-nodes';
import type { AppAction } from '../action-types';
import type { Reducer } from 'redux';
import type { XtremAppState } from '../state';
import { clientUserSettingsListPage } from './client-user-settings-edit-list';
import { clientUserSettingsEditPage } from './client-user-settings-edit-page';
import { navigation } from './navigation';
import { websocket } from './websocket';

const rootReducer: Reducer<XtremAppState, AppAction> = combineReducers<XtremAppState, AppAction>({
    activeDialogs,
    activeLookupDialog,
    applicationContext,
    applicationPackages,
    browser,
    clientUserSettingsEditPage,
    clientUserSettingsListPage,
    customizationWizardPage,
    dashboard,
    dataTypes,
    enumTypes,
    exportConfigurationPage,
    focusPosition,
    isKeyboardShortcutsEnabled,
    loading,
    menuItems,
    navigation,
    nodeTypes,
    path,
    plugins,
    printingSettings,
    screenDefinitions,
    serviceOptions,
    toasts,
    translations,
    websocket,
    workflowNodes,
});

export default rootReducer;
