import type { Dict } from '@sage/xtrem-shared';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';

export const serviceOptions = (state: Dict<boolean> = {}, action: AppAction): Dict<boolean> => {
    if (action.type === ActionType.SetInitialMetaData) {
        return action.value.serviceOptions.reduce((acc, serviceOption): Dict<boolean> => {
            acc[serviceOption.name] = serviceOption.isActive;
            return acc;
        }, {} as Dict<boolean>);
    }
    return state;
};
