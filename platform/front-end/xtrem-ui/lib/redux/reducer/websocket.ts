import type { Reducer } from 'redux';
import { combineReducers } from 'redux';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';

export type EventCallback = (...args: any[]) => void;

interface SubscriptionState {
    activeSubscriptions: Map<string, Set<EventCallback>>;
}

const initialState: SubscriptionState = {
    activeSubscriptions: new Map(),
};

export const subscriptions = (state: SubscriptionState = initialState, action: AppAction): SubscriptionState => {
    switch (action.type) {
        case ActionType.SubscribeToEvent: {
            const { category, callback } = action.value;
            const newSubscriptions = new Map(state.activeSubscriptions);
            const callbacks = newSubscriptions.get(category) || new Set();
            callbacks.add(callback);
            newSubscriptions.set(category, callbacks);
            return {
                ...state,
                activeSubscriptions: newSubscriptions,
            };
        }

        case ActionType.UnsubscribeFromEvent: {
            const { category, callback } = action.value;
            const newSubscriptions = new Map(state.activeSubscriptions);
            const callbacks = newSubscriptions.get(category);

            if (callbacks) {
                callbacks.delete(callback);
                if (callbacks.size === 0) {
                    newSubscriptions.delete(category);
                } else {
                    newSubscriptions.set(category, callbacks);
                }
            }

            return {
                ...state,
                activeSubscriptions: newSubscriptions,
            };
        }

        case ActionType.ClearAllSubscriptions:
            return {
                ...state,
                activeSubscriptions: new Map(),
            };

        default:
            return state;
    }
};

export const websocket: Reducer<{ subscriptions: SubscriptionState }, AppAction> = combineReducers({
    subscriptions,
});
