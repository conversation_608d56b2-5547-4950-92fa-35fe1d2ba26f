import type { Toast } from '../../service/toast-service';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';

export const toasts = (state: Toast[] = [], action: AppAction): Toast[] => {
    if (action.type === ActionType.ShowToast) {
        return [...state, action.value];
    }
    if (action.type === ActionType.RemoveToast) {
        const newState = [...state];
        const toastIndex = newState.findIndex(n => n.id === action.value);
        newState[toastIndex] = { ...newState[toastIndex], isDismissed: true };
        if (newState.find(n => !n.isDismissed)) {
            return newState;
        }
        return [];
    }
    if (action.type === ActionType.RemoveToasts) {
        return [];
    }
    return state;
};
