import { type Dict, objectKeys } from '@sage/xtrem-shared';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import { snakeCase } from 'lodash';
import { getArtifactDescription } from '../../utils/transformers';
import { clearCarbonLocaleCache } from '../../utils/carbon-locale';

const extractEnumTranslations = (
    enumValues: Dict<{ values: string[]; name: string; translations: string[] }>,
): Dict<string> =>
    objectKeys(enumValues).reduce((prevValue: Dict<string>, key: string) => {
        const enumDefinition = enumValues[key];
        const { vendor, package: pck, name } = getArtifactDescription(enumDefinition.name);
        enumDefinition.values.forEach((memberName: string, index: number) => {
            const translationKey = `${vendor}/${pck}/enums__${snakeCase(name)}__${memberName}`;
            if (enumDefinition.translations?.[index]) {
                prevValue[translationKey] = enumDefinition.translations[index];
            }
        });
        return prevValue;
    }, {} as Dict<string>);

export const translations = (state: Dict<Dict<string>> = {}, action: AppAction): Dict<Dict<string>> => {
    if (action.type === ActionType.AddTranslations) {
        clearCarbonLocaleCache();
        return { ...state, [action.value.locale]: { ...state[action.value.locale], ...action.value.literals } };
    }

    if (action.type === ActionType.AddSchemaInfo) {
        clearCarbonLocaleCache();
        return {
            ...state,
            [action.value.locale]: {
                ...state[action.value.locale],
                ...extractEnumTranslations(action.value.enumTypes),
            },
        };
    }

    if (action.type === ActionType.FinishScreenLoading) {
        clearCarbonLocaleCache();
        return {
            ...state,
            [action.value.locale]: {
                ...state[action.value.locale],
                ...extractEnumTranslations(action.value.enumTypes),
            },
        };
    }

    if (action.type === ActionType.AddEnumType) {
        clearCarbonLocaleCache();
        return {
            ...state,
            [action.value.locale]: {
                ...state[action.value.locale],
                ...extractEnumTranslations(action.value.enums),
            },
        };
    }
    return state;
};
