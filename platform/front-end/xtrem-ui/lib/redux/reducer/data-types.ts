import type { Dict } from '@sage/xtrem-shared';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { DataTypeDetails } from '../../service/metadata-types';

export const dataTypes = (state: Dict<DataTypeDetails> = {}, action: AppAction): Dict<DataTypeDetails> => {
    if (action.type === ActionType.AddDataTypes) {
        return { ...state, ...action.value };
    }

    if (action.type === ActionType.FinishScreenLoading) {
        return { ...state, ...action.value.dataTypes };
    }
    if (action.type === ActionType.SetInitialMetaData) {
        return { ...state, ...action.value.dataTypes };
    }

    return state;
};
