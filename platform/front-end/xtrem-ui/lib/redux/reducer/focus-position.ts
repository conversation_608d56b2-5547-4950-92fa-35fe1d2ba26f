import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { FocusPosition } from '../state';

export const focusPosition = (state: FocusPosition | null = null, action: AppAction): FocusPosition | null => {
    if (action.type === ActionType.OpenDialog) {
        return null;
    }

    if (action.type === ActionType.SetFocusPosition) {
        return action.value;
    }
    return state;
};
