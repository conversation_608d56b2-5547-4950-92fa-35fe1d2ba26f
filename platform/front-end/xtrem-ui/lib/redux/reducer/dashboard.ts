import { objectKeys, type Dict } from '@sage/xtrem-shared';
import type { Reducer } from 'redux';
import { combineReducers } from 'redux';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { DashboardGroupState, DashboardState } from '../state';
import { dashboardEditor } from './dashboard-editor';
import { availableDashboards, dashboards, widgets } from './dashboard-group';
import { widgetEditor } from './widget-editor';

export const nodeNames = (state = {}, action: AppAction): Dict<string> => {
    if (action.type === ActionType.SetNodeNames) {
        return action.value;
    }
    return state;
};

export const widgetCategories = (state = {}, action: AppAction): Dict<string> => {
    if (action.type === ActionType.SetWidgetCategories) {
        return action.value.categories;
    }
    return state;
};

export const canEditDashboards = (state = false, action: AppAction): boolean => {
    if (action.type === ActionType.SetInitialDashboardInformation) {
        return action.value.canEditDashboards;
    }
    return state;
};

const dashboardGroup: Reducer<DashboardGroupState, AppAction> = combineReducers<DashboardGroupState, AppAction>({
    availableDashboards,
    dashboardEditor,
    dashboards,
    widgetEditor,
    widgets,
});

export const dashboardGroups = (
    state: Dict<DashboardGroupState> = {},
    action: AppAction,
): Dict<DashboardGroupState> => {
    const group = (action as any)?.value?.group;

    if (group) {
        return {
            ...state,
            [group]: dashboardGroup(state[group], action),
        };
    }

    // The clear widget option action is a special case where we need to clear all widgets in all groups
    if (action.type === ActionType.ClearWidgetOptions) {
        return objectKeys(state).reduce((prevValue, currentValue) => {
            return { ...prevValue, [currentValue]: dashboardGroup(state[currentValue], action) };
        }, {} as Dict<DashboardGroupState>);
    }

    if (action.type === ActionType.RemoveDashboardGroup) {
        const { [action.value]: unused, ...newState } = state;
        return newState;
    }

    return state;
};

export const dashboard: Reducer<DashboardState, AppAction> = combineReducers<DashboardState, AppAction>({
    nodeNames,
    widgetCategories,
    dashboardGroups,
    canEditDashboards,
});
