import { DASHBOARD_WIDGETS_MIN_LOADING_TIME } from '../../utils/constants';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { LoadingState } from '../state';

export const loading = (
    state: LoadingState = { globalLoading: false, pages: {}, widgets: {}, loadingDashboards: false },
    action: AppAction,
): LoadingState => {
    switch (action.type) {
        case ActionType.SetGlobalLoading:
            return { ...state, globalLoading: action.value };
        case ActionType.SetLoadingDashboards:
            return { ...state, loadingDashboards: action.value.isLoading };
        case ActionType.SetValues:
            return { ...state, globalLoading: false };
        case ActionType.SetComponentLoading:
            return {
                ...state,
                pages: {
                    ...state.pages,
                    [action.value.screenId]: state.pages[action.value.screenId]
                        ? { ...state.pages[action.value.screenId], [action.value.elementId]: action.value.isLoading }
                        : { [action.value.elementId]: action.value.isLoading },
                },
            };
        case ActionType.SetWidgetLoading:
            const widgetState = { ...state.widgets[action.value.widgetId] };
            if (action.value.isLoading) {
                widgetState.isActualLoading = true;
                widgetState.isVisibleLoading = true;
                widgetState.loadingStartTime = Date.now();

                if (widgetState.loadingTimeoutId !== null) {
                    window.clearTimeout(widgetState.loadingTimeoutId);
                    widgetState.loadingTimeoutId = null;
                }
            } else {
                widgetState.isActualLoading = false;
                if (widgetState.loadingStartTime !== null) {
                    const elapsedTime = Date.now() - widgetState.loadingStartTime;

                    if (elapsedTime >= DASHBOARD_WIDGETS_MIN_LOADING_TIME) {
                        widgetState.isVisibleLoading = false;
                        widgetState.loadingStartTime = null;
                    }
                }
            }
            return {
                ...state,
                widgets: {
                    ...state.widgets,
                    [action.value.widgetId]: widgetState,
                },
            };
        case ActionType.SetWidgetLoadingTimeoutId:
            return {
                ...state,
                widgets: {
                    ...state.widgets,
                    [action.value.widgetId]: {
                        ...state.widgets[action.value.widgetId],
                        loadingTimeoutId: action.value.timeoutId,
                    },
                },
            };
        case ActionType.ResetWidgetLoadingTimeout:
            return {
                ...state,
                widgets: {
                    ...state.widgets,
                    [action.value.widgetId]: {
                        ...state.widgets[action.value.widgetId],
                        isVisibleLoading: false,
                        loadingStartTime: null,
                        loadingTimeoutId: null,
                    },
                },
            };
        default:
            return state;
    }
};
