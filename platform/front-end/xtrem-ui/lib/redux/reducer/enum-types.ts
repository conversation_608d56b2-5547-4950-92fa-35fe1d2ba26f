import { objectKeys, type Dict } from '@sage/xtrem-shared';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';

const remapEnumValues = (enumValues: Dict<{ values: string[]; name: string }>): Dict<string[]> =>
    objectKeys(enumValues).reduce(
        (prevValue: Dict<string[]>, key: string) => ({ ...prevValue, [key]: enumValues[key].values }),
        {} as Dict<string[]>,
    );

export const enumTypes = (state: Dict<string[]> = {}, action: AppAction): Dict<string[]> => {
    if (action.type === ActionType.AddEnumType) {
        return { ...state, ...remapEnumValues(action.value.enums) };
    }
    if (action.type === ActionType.FinishScreenLoading) {
        return { ...state, ...remapEnumValues(action.value.enumTypes) };
    }
    if (action.type === ActionType.AddSchemaInfo) {
        return { ...state, ...remapEnumValues(action.value.enumTypes) };
    }
    if (action.type === ActionType.SetInitialMetaData) {
        return { ...state, ...remapEnumValues(action.value.enumTypes) };
    }
    return state;
};
