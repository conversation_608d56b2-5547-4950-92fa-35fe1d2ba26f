import type { Dict } from '@sage/xtrem-shared';
import { GraphQLKind, GraphQLTypes } from '../../types';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { FormattedNodeDetails } from '../../service/metadata-types';

export const nodeTypes = (
    state: Dict<FormattedNodeDetails> = {
        XtremUiWidget: {
            name: 'XtremUiWidget',
            title: 'XtremUiWidget',
            packageName: '@sage/xtrem-ui',
            properties: {
                _id: { type: GraphQLTypes.ID, kind: GraphQLKind.Scalar, name: '_id' },
                title: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, name: 'title' },
                description: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, name: 'description' },
                type: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, name: 'type' },
                category: { type: 'XtremUiWidgetCategory', kind: GraphQLKind.Object, name: 'category' },
            },
            mutations: {},
        },
        XtremUiWidgetCategory: {
            name: 'XtremUiWidgetCategory',
            title: 'XtremUiWidgetCategory',
            packageName: '@sage/xtrem-ui',
            properties: {
                _id: { type: GraphQLTypes.ID, kind: GraphQLKind.Scalar, name: '_id' },
                title: {
                    type: GraphQLTypes.String,
                    kind: GraphQLKind.Scalar,
                    name: 'title',
                },
                sortValue: {
                    type: GraphQLTypes.Int,
                    kind: GraphQLKind.Scalar,
                    canSort: true,
                    canFilter: false,
                    name: 'sortValue',
                },
            },
            mutations: {},
        },
    },
    action: AppAction,
): Dict<FormattedNodeDetails> => {
    if (action.type === ActionType.AddNodeTypes) {
        return { ...state, ...action.value };
    }

    if (action.type === ActionType.FinishScreenLoading) {
        return { ...state, ...action.value.nodeTypes };
    }

    if (action.type === ActionType.SetInitialMetaData) {
        return { ...state, ...action.value.nodeTypes };
    }

    return state;
};
