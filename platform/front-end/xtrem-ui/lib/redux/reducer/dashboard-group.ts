import { deepMerge, objectKeys, type Dict } from '@sage/xtrem-shared';
import type { UiDashboard } from '../../dashboard/dashboard-types';
import type { WidgetDefinition } from '../../dashboard/widgets/abstract-widget';
import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { DashboardListItem } from '../state';

export const dashboards = (state: Dict<UiDashboard> = {}, action: AppAction): Dict<UiDashboard> => {
    if (action.type === ActionType.AddDashboard) {
        // Setting current dashboards unselected
        const newState = objectKeys(state).reduce((prevValue, currentValue) => {
            return { ...prevValue, [currentValue]: { ...state[currentValue], isSelected: false } };
        }, {} as Dict<UiDashboard>);

        return { ...newState, [action.value.dashboard._id]: { ...action.value.dashboard, isSelected: true } };
    }

    if (action.type === ActionType.RemoveDashboard) {
        const newState = { ...state };
        const itemToRemove = newState[action.value.dashboardId];
        delete newState[action.value.dashboardId];

        // If the selected dashboard is deleted we mark the first dashboard selected
        const remainingKeys = objectKeys(newState);
        if (itemToRemove.isSelected && remainingKeys.length > 0) {
            newState[remainingKeys[0]].isSelected = true;
        }
        return newState;
    }

    return state;
};

export const widgets = (state: Dict<WidgetDefinition> = {}, action: AppAction): Dict<WidgetDefinition> => {
    if (action.type === ActionType.AddWidgets) {
        const widgetDefinitions = action.value.widgets.reduce(
            (prevValue: Dict<WidgetDefinition>, currentValue: WidgetDefinition) => ({
                ...prevValue,
                [currentValue._id]: currentValue,
            }),
            {} as Dict<WidgetDefinition>,
        );
        return { ...state, ...widgetDefinitions };
    }

    if (action.type === ActionType.ClearWidgetOptions) {
        return objectKeys(state)
            .map((widgetId: string) => {
                return { ...state[widgetId], options: undefined };
            })
            .reduce((accu: Dict<WidgetDefinition>, next: WidgetDefinition) => {
                return { ...accu, [next._id]: next };
            }, {} as Dict<WidgetDefinition>);
    }

    if (action.type === ActionType.SetWidgetData) {
        const widgetDefinition = state[action.value.widgetId];
        return { ...state, [action.value.widgetId]: { ...widgetDefinition, data: action.value.data } };
    }

    if (action.type === ActionType.SetWidgetProperties) {
        const widgetDefinition = state[action.value.widgetId];
        return {
            ...state,
            [action.value.widgetId]: {
                ...widgetDefinition,
                properties: { ...widgetDefinition.properties, ...action.value.properties },
            },
        };
    }

    if (action.type === ActionType.AddWidgetData) {
        const widgetDefinition = state[action.value.widgetId];
        return {
            ...state,
            [action.value.widgetId]: { ...widgetDefinition, data: deepMerge(widgetDefinition.data, action.value.data) },
        };
    }

    if (action.type === ActionType.SetWidgetOptions) {
        const widgetDefinition = state[action.value.widgetId];
        return { ...state, [action.value.widgetId]: { ...widgetDefinition, options: action.value.options } };
    }

    return state;
};

export const availableDashboards = (state: DashboardListItem[] = [], action: AppAction): DashboardListItem[] => {
    const newState = [...state];
    switch (action.type) {
        case ActionType.SetInitialDashboardInformation:
            return action.value.availableDashboards;
        case ActionType.RemoveDashboard:
            const indexToRemove = newState.findIndex(d => String(d._id) === action.value.dashboardId);
            if (indexToRemove !== -1) {
                newState.splice(indexToRemove, 1);
            }
            return newState;
        case ActionType.AddDashboard:
            const indexToUpdate = newState.findIndex(d => String(d._id) === action.value.dashboard._id);
            const newEntry = { _id: action.value.dashboard._id, title: action.value.dashboard.title };
            if (indexToUpdate === -1) {
                return [...newState, newEntry];
            }
            newState[indexToUpdate] = newEntry;
            return newState;
        default:
            // We have to return the original state, not the cloned object so we don't trigger unessential updates
            return state;
    }
};
