import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { FocusPosition } from '../state';

export const activeLookupDialog = (state: FocusPosition | null = null, action: AppAction): FocusPosition | null => {
    if (action.type === ActionType.OpenLookupDialog) {
        return action.value;
    }

    if (action.type === ActionType.CloseLookupDialog) {
        return null;
    }

    return state;
};
