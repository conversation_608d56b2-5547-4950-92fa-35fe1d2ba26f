import type { Reducer } from 'redux';
import type { <PERSON><PERSON><PERSON><PERSON> } from 'redux-responsive';
import { createResponsiveStateReducer } from 'redux-responsive';
import type { Breakpoints } from '../../render/responsive-breakpoints';
import { breakpoints } from '../../render/responsive-breakpoints';

export const browser: Reducer<IBrowser<Breakpoints>> = createResponsiveStateReducer({
    ...breakpoints,
});
