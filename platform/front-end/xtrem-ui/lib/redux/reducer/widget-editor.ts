import type { AppAction } from '../action-types';
import { ActionType } from '../action-types';
import type { UserWidgetDefinition, WidgetEditorState } from '../state';
import type { Reducer } from 'redux';
import { combineReducers } from 'redux';

export const isOpen = (state = false, action: AppAction): boolean => {
    if (action.type === ActionType.SetWidgetEditorOpen) {
        return action.value.isOpen;
    }

    return state;
};

export const isDirty = (state = false, action: AppAction): boolean => {
    if (action.type === ActionType.UpdateUserWidgetDefinition) {
        // TODO: Revise this logic when we load previously created widgets for editing.
        return true;
    }

    if (action.type === ActionType.SetWidgetEditorOpen) {
        // Reset the dirty state when the dialog is being closed
        return false;
    }
    return state;
};

export const widgetDefinition = (state = {}, action: AppAction): UserWidgetDefinition => {
    if (action.type === ActionType.UpdateUserWidgetDefinition) {
        return action.value.widget;
    }

    if (action.type === ActionType.SetWidgetEditorOpen && !action.value.isOpen) {
        // Reset the value of the document when the editor is closed
        return {};
    }

    if (action.type === ActionType.SetWidgetEditorOpen && action.value.widgetDefinition) {
        // Sets the widget definition
        return action.value.widgetDefinition;
    }
    return state;
};

export const widgetId = (state = '', action: AppAction): string => {
    if (action.type === ActionType.SetWidgetEditorOpen && !action.value.isOpen) {
        return '';
    }

    if (action.type === ActionType.SetWidgetEditorOpen && action.value.widgetId) {
        return action.value.widgetId;
    }

    return state;
};

export const widgetEditor: Reducer<WidgetEditorState, AppAction> = combineReducers<WidgetEditorState, AppAction>({
    isDirty,
    isOpen,
    widgetDefinition,
    widgetId,
});
