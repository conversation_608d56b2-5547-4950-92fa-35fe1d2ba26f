import type { ClientNode, Filter } from '@sage/xtrem-client';
import type { Aggregations, Dashboard, Dict, FilterProperty, Property, WorkflowNode } from '@sage/xtrem-shared';
import type { Order } from '@sage/xtrem-ui-components';
import type { AxiosResponse } from 'axios';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { PropertyValueType } from '../component/field/reference/reference-types';
import type { GridNestedFieldTypes } from '../component/nested-fields';
import type { UiDashboard } from '../dashboard/dashboard-types';
import type { WidgetDefinition } from '../dashboard/widgets/abstract-widget';
import type { IndicatorTileGroupContent } from '../dashboard/widgets/indicator-tile-group-widget-decorator';
import type { CollectionValue } from '../service/collection-data-service';
import type { DataTypeDetails, FormattedNodeDetails } from '../service/metadata-types';
import type { XtremUiPlugin } from '../service/plugin-service';
import type { ScreenBaseDefinition } from '../service/screen-base-definition';
import type { Toast } from '../service/toast-service';
import type { DialogDescription, DialogType } from '../types/dialogs';
import type { QueryParameters } from '../utils/types';
import type { EventCallback } from './reducer/websocket';

export interface FocusPosition {
    screenId: string;
    elementId: string;
    row?: string;
    nestedField?: string;
}
/**
 * Represent a menu object that is rendered by the consumer in its navigation panel.
 */
export interface Menu {
    /** To be used with top level items, if true, the menu item should be rendered left to the logo */
    alignedLeft?: boolean;
    /** Child menu items, should be rendered as a pop down menu by the consumer */
    children?: Menu[];
    /** Whether the menu item should be hidden or not */
    hidden?: boolean;
    /** Sage DLS Icon, if defined, the icon should be rendered instead of the title, the title should become a tooltip. */
    icon?: IconType;
    /** Menu item ID, unique in the menu tree */
    id: string;
    /** Whether the menu item is hidden or not in mobile devices */
    isHiddenMobile?: boolean;
    /** Whether the menu item is hidden or not in desktop devices */
    isHiddenDesktop?: boolean;
    /** Value to be displayed inside a badge along with the menu icon */
    badgeContent?: string;
    /** Trigger the menu's action */
    onClick: () => void;
    /** Menu item associated text */
    title: string;
    /** Metadata field for organizing menu items */
    category: string;
    /** Indicate whether the menu item is related to a sticker dialog */
    isDialogSticker?: boolean;
    /** In case of a dialog reference, the internal ID of the dialog */
    dialogId?: number;
    /** In case of a dialog reference, the type of dialog */
    dialogType?: DialogType;
}

export type OnTelemetryEventFunction = (eventType: string, data: any) => void;

/**
 * The context that the UI application is integrated to. It requires some basic information and provides
 * callbacks to notifier the consumer around various events.
 */

export interface PageContext {
    nodeName?: string;
    recordFilter?: Filter<any>;
    screenTitle?: string;
    screenId?: string;
    onInsightCountUpdated?: (count: number) => void;
}

export interface ApplicationContext {
    login?: string;

    userCode?: string;

    locale?: string;

    /* Navigation callback, it is called when an internal navigation event occurs that requires a URL change in the browser */
    handleNavigation: (newPath: string, queryParameters: QueryParameters, doDirtyCheck?: boolean) => void;

    /* Menu change callback, it is called when a new menu object need to be rendered in the navigation panel of the consumer application */
    updateMenu: (newMenuItems: Menu[]) => void;

    /* Title change callback, it is called when the page title changes.
    This can be caused by a navigation event, or by assigning a new value to the title field of the page control object. */
    onPageTitleChange?: (newTitle: string | null, newSubtitle: string | null) => void;

    onPageContextChange?: (newPageContext: PageContext | null) => void;

    onApiRequestError?: (response: AxiosResponse) => void;

    /**
     * Triggered when the data forms within the application becomes dirty or clean. If the user has multiple pages open,
     * the application is considered clean only if all of those pages are clean.
     * */
    onDirtyStatusChange?: (isApplicationDirty: boolean, preNavigationConfirmation: () => Promise<void>) => void;

    /**
     * Triggered when on various kinds of telemetry events which might be used for further analysis.
     * */
    onTelemetryEvent?: OnTelemetryEventFunction;

    /* Headers that will be added to the GraphQL and etna-ui pages network requests */
    requestHeaders?: Dict<string>;

    /** Path prefix for API calls */
    path?: string;

    /** Licence key for ag-grid enterprise */
    agGridLicenceKey?: string;

    /** Encryption key that is used to encrypt transactional data in the browser cache */
    cacheEncryptionKey?: string;

    /** Wheter the playSound API object is enabled or not. */
    isPlaySoundEnabled?: boolean;

    /** Disables the node cache, it should be used in systems that don't support websocket notifications */
    isNodeCacheDisabled?: boolean;
}

export interface UiComponentUserSettings<ContentType extends any = any> {
    _id?: string;
    title: string;
    description?: string;
    content: ContentType;
    isDirty?: boolean;
    isDefault?: boolean;
}

export interface NavigationPanelState<T extends ClientNode = any> {
    /** It can be used to group items in the mobile card view */
    groupByField?: PropertyValueType;
    isHeaderHidden: boolean;
    isHidden: boolean;
    isOpened: boolean;
    isRefreshing: boolean;
    value: CollectionValue<T>;
}

export interface LoadingState {
    globalLoading: boolean;
    loadingDashboards: boolean;
    widgets: Dict<{
        isActualLoading: boolean;
        isVisibleLoading: boolean;
        loadingStartTime: number | null;
        loadingTimeoutId: number | null;
    }>;
    pages: Dict<Dict<boolean>>;
}

export interface DashboardListItem {
    _id: string;
    title: string;
    description?: string;
    listIcon?: string;
}

/**
 * The dashboard editor state, we have to have it as part of the global state because some actions from the navigation panel adds
 * elements to the `currentDashboardDefinition`
 */
export interface DashboardEditorState {
    /** The working document of the dashboard editor */
    currentDashboardDefinition: Dashboard;
    /**
     * In case the user steps back in the history, this property determines at which history state we are on so relative to that we
     * can either step backwards or forward
     */
    currentHistoryIndex: number;
    /** Snapshots of the working document states */
    history: Dashboard[];
    /** Whether the editor has unsaved changes */
    isDirty: boolean;
    /** Whether the dashboard dialog is open */
    isOpen: boolean;
}

export type UserCustomizableWidget = 'TABLE' | 'INDICATOR_TILE' | 'BAR_CHART' | 'LINE_CHART';

export type OrderByProperty = Property & { order: Order };

export type TableProperty = Property & {
    presentation: GridNestedFieldTypes;
    formatting?: number;
    title: string;
    divisor?: number;
};
export type AggregationProperty = Property & {
    formatting?: number;
    title: string;
    decimalDigits?: number;
    groupingMethod: Aggregations;
    color?: string;
    divisor?: number;
};

export type DateAggregation = 'year' | 'month' | 'day';

type XAxis = {
    property: Property;
    groupBy?: DateAggregation;
    decimalDigits?: number;
};

export interface UserWidgetDefinitionAction {
    isEnabled: boolean;
    title?: string;
    page?: string;
}
/** This object contains all information regarding a user defined widget. */
export interface UserWidgetDefinition {
    node?: string;
    type?: UserCustomizableWidget;
    /** Widget category setup ID */
    category?: string;
    /** Path from the root node to a property */
    selectedProperties?: Dict<Property>;
    /** User provided, displayed title of the widget */
    title?: string;
    /** User provided filters */
    filters?: FilterProperty[];
    /** User provided sort conditions */
    orderBy?: OrderByProperty[];
    /** User provided group-by for indicator tile */
    groupBy?: {
        divisor?: number;
        property: Property;
        method: Aggregations;
    };
    /** User provided columns for table widget */
    columns?: TableProperty[];
    /** Aggregation values displayed on the Y axis for chart widgets */
    aggregations?: AggregationProperty[];
    /** Number of aggregation values displayed on the Y axis for chart widgets */
    aggregationsCount?: number;
    horizontalAxisLabel?: string;
    verticalAxisLabel?: string;
    /** X axis for chart widgets */
    xAxis?: XAxis;
    /** Icon for the indicator tile */
    icon?: string;
    /** List of enums used in the custom widget */
    usedEnums?: string[];

    decimalDigits?: number;

    subtitle?: string;

    seeAllAction?: UserWidgetDefinitionAction;
    createAction?: UserWidgetDefinitionAction;
    content?: IndicatorTileGroupContent<any>[];
}

export interface WidgetEditorState {
    widgetId?: any;
    /** Whether the widget editor has unsaved changes */
    isDirty: boolean;
    /** Whether the widget dialog is open */
    isOpen: boolean;

    widgetDefinition: UserWidgetDefinition;
}

export interface DashboardGroupState {
    availableDashboards: DashboardListItem[];
    dashboards: Dict<UiDashboard>;
    dashboardEditor: DashboardEditorState;
    widgetEditor: WidgetEditorState;
    widgets: Dict<WidgetDefinition>;
}

export interface DashboardState {
    dashboardGroups: Dict<DashboardGroupState>;
    canEditDashboards: boolean;
    nodeNames: Dict<string>;
    widgetCategories: Dict<string>;
}

export interface PrintingSettings {
    printingAssignmentDialogUrl: string;
    canAccessPrintingAssignmentDialog: boolean;
    listPrintingWizardUrl: string;
    canAccessListPrintingWizard: boolean;
    recordPrintingWizardUrl: string;
    canAccessRecordPrintingWizard: boolean;
    listPrintingGlobalMutationConfigPage: string | null;
    recordPrintingGlobalBulkMutationName: string | null;
}

export interface XtremAppState {
    activeDialogs: Dict<DialogDescription>;
    activeLookupDialog: FocusPosition | null;
    applicationContext: ApplicationContext | null;
    applicationPackages: Dict<string> | null;
    browser: ReduxResponsive;
    customizationWizardPage: string | null;
    dashboard: DashboardState;
    dataTypes: Dict<DataTypeDetails>;
    enumTypes: Dict<string[]>;
    exportConfigurationPage: string | null;
    clientUserSettingsEditPage: string | null;
    clientUserSettingsListPage: string | null;
    focusPosition: FocusPosition | null;
    isKeyboardShortcutsEnabled: boolean;
    loading: LoadingState;
    menuItems: Menu[];
    nodeTypes: Dict<FormattedNodeDetails>;
    path: string | null;
    plugins: Dict<XtremUiPlugin<any, any>>;
    printingSettings: PrintingSettings | null;
    screenDefinitions: Dict<ScreenBaseDefinition>;
    serviceOptions: Dict<boolean>;
    toasts: Toast[];
    translations: Dict<Dict<string>>;
    workflowNodes: WorkflowNode[] | null;
    navigation: NavigationState;
    websocket: {
        subscriptions: {
            activeSubscriptions: Map<string, Set<EventCallback>>;
        };
    };
}

export interface ReduxResponsive {
    is: ResponsiveTypes;
    greaterThan: ResponsiveTypes;
    lessThan: ResponsiveTypes;
    mediaType: string;
    orientation: string;
}

export interface ResponsiveTypes {
    xs: boolean;
    s: boolean;
    m: boolean;
    l: boolean;
}

export interface NavigationState {
    history: { path: string; queryParams: QueryParameters }[];
    isBackNavigation: boolean;
}
