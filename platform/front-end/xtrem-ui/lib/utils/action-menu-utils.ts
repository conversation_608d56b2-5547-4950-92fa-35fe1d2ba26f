import { camelCase } from 'lodash';
import type {
    DropdownActionItem,
    DropdownActionItemOrMenuSeparator,
    ErrorHandlerFunction,
} from '../component/field/traits';
import type {
    CollectionItemAction,
    CollectionItemActionGroup,
    CollectionItemActionOrMenuSeparator,
    NestedCollectionItemAction,
    NestedCollectionItemActionGroup,
    NestedCollectionItemActionOrMenuSeparator,
} from '../component/ui/table-shared/table-dropdown-actions/table-dropdown-action-types';
import type {
    XtremActionPopoverItem,
    XtremActionPopoverItemOrMenuSeparator,
} from '../component/ui/xtrem-action-popover';
import type { AccessBindings } from '../service/page-definition';
import { getElementAccessStatusWithoutId } from './access-utils';
import { resolveByValue } from './resolve-value-utils';
import { splitValueToMergedValue } from './transformers';

export type ActionType =
    | 'pod-action'
    | 'pod-collection-action'
    | 'table-dropdown-action'
    | 'table-inline-action'
    | 'table-sidebar-header-dropdown-action'
    | 'table-sidebar-header-quick-action';

// SonarCloud: Refactor this union type to have less than 3 elements.
// Union types should not have too many elements typescript:S4622
type ActionMenuLike =
    | DropdownActionItem<any>
    | CollectionItemAction<any>
    | CollectionItemActionGroup<any>
    | NestedCollectionItemAction<any>
    | NestedCollectionItemActionGroup<any>;

// SonarCloud: Refactor this union type to have less than 3 elements.
// Union types should not have too many elements typescript:S4622
type ActionMenuOrSeparatorLike =
    | DropdownActionItemOrMenuSeparator<any>
    | CollectionItemActionOrMenuSeparator<any>
    | CollectionItemActionGroup<any>
    | NestedCollectionItemActionOrMenuSeparator<any>
    | NestedCollectionItemActionGroup<any>;

export interface CalculateActionMenuPropsWithSeparator {
    accessBindings: AccessBindings;
    actions?: Array<ActionMenuOrSeparatorLike>;
    actionType: ActionType;
    onTriggerMenuItem: (
        context: { id?: string; uniqueId: string },
        eventHandler: (this: any, recordId: string, rowItem: any, level?: number, parentIds?: string[]) => void,
        errorHandler?: ErrorHandlerFunction<any>,
    ) => void;
    pendoId?: string;
    rowValue?: any;
    screenId: string;
}

export interface CalculateActionMenuProps {
    accessBindings: AccessBindings;
    actions?: Array<ActionMenuLike>;
    actionType: ActionType;
    onTriggerMenuItem: (
        context: { id?: string; uniqueId: string },
        eventHandler: (this: any, recordId: string, rowItem: any, level?: number, parentIds?: string[]) => void,
        errorHandler?: ErrorHandlerFunction<any>,
    ) => void;
    pendoId?: string;
    rowValue?: any;
    screenId: string;
}

const getUniqueId = (title: string, id?: string): string => {
    return camelCase(id || title);
};

const getActionMenuTestId = (actionType: ActionType, title: string, id?: string): string => {
    const uniqueId = getUniqueId(title, id);

    switch (actionType) {
        case 'pod-action':
            return `e-pod-action e-pod-action--${uniqueId} e-action e-action--${uniqueId}`;
        case 'pod-collection-action':
            return `e-pod-collection-action e-pod-collection-action--${uniqueId} e-action e-action--${uniqueId}`;
        case 'table-dropdown-action':
            return `e-table-dropdown-action e-dropdown-action e-dropdown-action--${uniqueId} e-action e-action--${uniqueId}`;
        case 'table-inline-action':
            return `e-table-inline-action e-inline-action e-inline-action--${uniqueId} e-action e-action--${uniqueId}`;
        case 'table-sidebar-header-dropdown-action':
            return `e-table-sidebar-header-dropdown-action e-action e-action--${uniqueId}`;
        case 'table-sidebar-header-quick-action':
            return `e-table-sidebar-header-quick-action e-action e-action--${uniqueId}`;
        default:
            return `e-action e-action--${uniqueId}`;
    }
};

export function calculateActionMenu(args: CalculateActionMenuProps): Array<XtremActionPopoverItem> {
    return calculateActionMenuWithSeparator(args) as Array<XtremActionPopoverItem>;
}

export function calculateActionMenuWithSeparator(
    args: CalculateActionMenuPropsWithSeparator,
): Array<XtremActionPopoverItemOrMenuSeparator>;
export function calculateActionMenuWithSeparator({
    accessBindings = {},
    actions,
    actionType = 'table-dropdown-action',
    onTriggerMenuItem,
    rowValue,
    screenId,
    pendoId,
}: CalculateActionMenuPropsWithSeparator): Array<XtremActionPopoverItemOrMenuSeparator> {
    if (!actions) {
        return [];
    }

    const recordId = rowValue?._id;
    let idx = 0;

    const filterFn = (a: NestedCollectionItemAction<any> | NestedCollectionItemActionGroup<any>): boolean => {
        if (a.isMenuSeparator) {
            return true;
        }
        const accessRule = getElementAccessStatusWithoutId(accessBindings, a.access);
        return accessRule === 'authorized';
    };

    const mapFn = (
        a:
            | NestedCollectionItemActionOrMenuSeparator<any>
            | NestedCollectionItemActionGroup<any>
            | CollectionItemActionOrMenuSeparator<any>,
    ): XtremActionPopoverItemOrMenuSeparator & { priority: number; isDisplayed: boolean } => {
        const isDisabled =
            resolveByValue({
                screenId,
                propertyValue: a.isDisabled,
                rowValue: splitValueToMergedValue(rowValue),
                fieldValue: recordId,
                skipHexFormat: true,
            }) || false;

        const isHidden =
            resolveByValue({
                screenId,
                propertyValue: a.isHidden,
                rowValue: splitValueToMergedValue(rowValue),
                fieldValue: recordId,
                skipHexFormat: true,
            }) || false;

        const isDisplayed = resolveByValue({
            screenId,
            propertyValue: (a as CollectionItemActionOrMenuSeparator<any>).isDisplayed,
            rowValue,
            fieldValue: recordId,
            skipHexFormat: true,
        });

        const { children: hasChildren } = a as NestedCollectionItemActionGroup<any>;
        const children: any = hasChildren ? hasChildren.filter(filterFn).map(mapFn) : [];
        const { isDestructive, onClick } = a as CollectionItemAction<any>;
        const context: { id?: string; uniqueId: string } = {
            id: camelCase((a as CollectionItemAction<any>).id),
            uniqueId: camelCase((a as CollectionItemAction<any>).id || (a as CollectionItemAction<any>).title),
        };
        idx += 1;

        if (a.isMenuSeparator) {
            const key: string = `${idx}_${a.id}` || `${idx}_menu_separator`;
            return {
                isDisplayed,
                isHidden,
                isMenuSeparator: true,
                key,
                priority: idx,
                testId: getActionMenuTestId(actionType, key, a.id),
            };
        }

        return {
            childrenProp: children,
            icon: a.icon,
            isDisabled,
            isDisplayed,
            isHidden,
            isDestructive,
            isMenuSeparator: false,
            key: `${idx}_${a.title}`,
            onClick: (): void => (isDisabled ? undefined : onTriggerMenuItem(context, onClick, a.onError)),
            priority: idx,
            testId: getActionMenuTestId(actionType, a.title, (a as CollectionItemAction<any>).id),
            title: a.title,
            pendoId: pendoId && context.id ? `${pendoId}-${context.id}` : undefined,
        };
    };

    return actions.filter(filterFn).map<XtremActionPopoverItemOrMenuSeparator>(mapFn);
}

export interface MenuSeparatorOptions {
    id?: string;
    insertAfter?: string;
    insertBefore?: string;
}

export interface MenuSeparatorResult {
    id?: string;
    insertAfter?: string;
    insertBefore?: string;
    isMenuSeparator: true;
}

export function menuSeparator({ id, insertAfter, insertBefore }: MenuSeparatorOptions = {}): MenuSeparatorResult {
    return {
        id,
        insertAfter,
        insertBefore,
        isMenuSeparator: true,
    };
}
