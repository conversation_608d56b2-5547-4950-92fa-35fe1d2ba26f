export const asyncForEach = async (array: any[], callback: Function): Promise<void> => {
    for (let index = 0; index < array.length; index += 1) {
        // eslint-disable-next-line no-await-in-loop
        await callback(array[index], index, array);
    }
};

/**
 *  Makes a promise cancelable/abortable without libraries or subclassing
 */
export const cancelable: <T extends any = any>(promise: Promise<T>) => [() => void, Promise<T>] = <T extends any = any>(
    promise: Promise<T>,
) => {
    const controller = new AbortController();
    const signal = controller.signal;
    let handler: (this: AbortSignal, ev: Event) => void;
    return [
        (): void => {
            controller.abort();
        },
        Promise.race([
            new Promise<T>((_, reject) => {
                handler = (): void => {
                    reject(new DOMException('AbortError', 'AbortError'));
                };
                signal.addEventListener('abort', handler);
            }),
            promise,
        ]).finally(() => signal.removeEventListener('abort', handler)),
    ];
};

/**
 *  Takes only last promise call by cancelling the previous ones, avoids race conditions.
 */

export function takeLatest<T extends any = any>() {
    let cancel: any = null;
    let latestPromise: Promise<T> | null = null;
    // eslint-disable-next-line func-names
    return function (promise: Promise<T>): Promise<T> {
        if (latestPromise && cancel) {
            cancel();
        }
        [cancel, latestPromise] = cancelable<T>(promise);
        return latestPromise;
    };
}
