import { objectKeys, type Dict } from '@sage/xtrem-shared';
import { get, isEmpty, set, uniq } from 'lodash';
import type { AbstractContainer } from '../component/abstract-container';
import type { BaseControlObject, ComponentProperties } from '../component/base-control-object';
import type { BlockControlObject, SectionControlObject } from '../component/control-objects';
import type { NestedReferenceProperties, ReferenceProperties } from '../component/field/reference/reference-types';
import { getReferencePath, getReferenceValueField } from '../component/field/reference/reference-utils';
import type { HasParent } from '../component/field/traits';
import type { NestedField, NestedFieldTypes } from '../component/nested-fields';
import { ReadonlyFieldControlObject } from '../component/readonly-field-control-object';
import type { ComponentKey, FieldDecoratorProps } from '../component/types';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FieldKey } from '../component/types';
import type { FilterManagerField } from '../component/ui/filter/filter-manager';
import type { FormattedNodeDetails } from '../service/metadata-types';
import type { PageDefinition } from '../service/page-definition';
import type { ScreenBase } from '../service/screen-base';
import { getScreenElement, type ScreenBaseDefinition, type ValidationResult } from '../service/screen-base-definition';
import { GraphQLTypes } from '../types';
import { xtremConsole } from './console';
import { getLayoutChildren } from './layout-utils';
import { convertDeepBindToPath, convertDeepBindToPathNotNull } from './nested-field-utils';
import { isDevMode } from './window';

/**
 * This function will retrieve an elementId for nestedFields.
 * @param field
 */
export function getNestedFieldElementId(args: undefined): null;
export function getNestedFieldElementId(args?: NestedField<ScreenBase, NestedFieldTypes> | FilterManagerField): string;
export function getNestedFieldElementId(args?: NestedField<ScreenBase, NestedFieldTypes> | FilterManagerField): string;
export function getNestedFieldElementId(
    field?: NestedField<ScreenBase, NestedFieldTypes> | FilterManagerField,
): null | string {
    if (!field) {
        return null;
    }
    if (field.type === FieldKey.Reference || field.type === FieldKey.MultiReference) {
        const fieldProperties = field.properties as Omit<NestedReferenceProperties, 'onChange'>;
        const valueField = getReferenceValueField(fieldProperties)?.split('.').join('__');
        const helperTextField = fieldProperties.helperTextField;
        if (!fieldProperties.bind || (!valueField && !helperTextField)) {
            throw new Error(`Missing information for the following reference field: ${JSON.stringify(field)}`);
        }
        return `${convertDeepBindToPathNotNull(fieldProperties.bind)}__${convertDeepBindToPathNotNull(
            valueField || String(helperTextField),
        )}`;
    }
    return convertDeepBindToPath(field.properties.bind) ?? null;
}

export const filterFields = (
    controlObjects: Dict<BaseControlObject<any, any>>,
    requiredSections: string[] = [],
    pageDefinition?: PageDefinition,
    includeFieldsWithNoParent = false,
): Dict<ReadonlyFieldControlObject<any, any, any>> => {
    let requiredFields: string[] = [];
    const hasLazyLoadedFields = !isEmpty(requiredSections) && pageDefinition;
    if (!isEmpty(requiredSections) && pageDefinition?.metadata.layout) {
        requiredFields = pageDefinition.metadata.layout.$items
            .filter(c => requiredSections.indexOf(String(c.$containerId)) !== -1)
            .flatMap(i => getLayoutChildren(i));

        if (includeFieldsWithNoParent) {
            objectKeys(controlObjects).forEach(key => {
                if (
                    controlObjects[key] instanceof ReadonlyFieldControlObject &&
                    !getParentSection(pageDefinition, key)
                ) {
                    requiredFields.push(key);
                }
            });
        }
        requiredFields = uniq(requiredFields);
    }

    return objectKeys(controlObjects)
        .filter(
            key =>
                controlObjects[key] instanceof ReadonlyFieldControlObject &&
                (!hasLazyLoadedFields || requiredFields.includes(key)),
        )
        .reduce(
            (result, key) => ({ ...result, [key]: controlObjects[key] as ReadonlyFieldControlObject<any, any, any> }),
            {} as Dict<ReadonlyFieldControlObject<any, any, any>>,
        );
};

export function getValueTypes(
    controlObjects: Dict<BaseControlObject<any, any>>,
    uiComponentProperties: Dict<FieldDecoratorProps<any>>,
): Dict<ReadonlyFieldControlObject<any, any, any>> {
    return objectKeys(controlObjects).reduce<Dict<ReadonlyFieldControlObject<any, any, any>>>((result, key) => {
        if (controlObjects[key] instanceof ReadonlyFieldControlObject) {
            result[(uiComponentProperties?.[key] as any)?.bind ?? key] = controlObjects[
                key
            ] as ReadonlyFieldControlObject<any, any, any>;
        }
        return result;
    }, {});
}

export const setDefaultProperties = <T extends ComponentProperties<any>>(
    decoratorProperties: T,
    defaultProperties: Partial<T>,
    controlObjectType?: ComponentKey,
    extensionPackageName?: string,
): T => {
    objectKeys(defaultProperties)
        .filter(key => get(decoratorProperties, key) === undefined)
        .forEach(key => {
            set(decoratorProperties, key, get(defaultProperties, key));
        });
    decoratorProperties._controlObjectType = controlObjectType || decoratorProperties._controlObjectType;
    if (!decoratorProperties._declaredInExtension) {
        decoratorProperties._declaredInExtension = extensionPackageName;
    }
    return decoratorProperties;
};

const hasParentInHierarchy = (
    screen: ScreenBase,
    controlObject: BaseControlObject<any, any>,
    elementId: string,
): boolean => {
    const parentGetter = (controlObject as HasParent<ScreenBase, AbstractContainer<any, any, any>>).parent;
    if (parentGetter) {
        const parent = parentGetter.apply(screen);
        if (parent?.id === elementId) {
            return true;
        }
        if (parent?.parent) {
            return hasParentInHierarchy(screen, parent, elementId);
        }
    }
    return false;
};

export const getContainerChildFields = (
    screen: ScreenBase,
    controlObjects: BaseControlObject<any, any>[],
    elementId: string,
): BaseControlObject<any, any>[] =>
    controlObjects.filter(
        controlObject => controlObject.id === elementId || hasParentInHierarchy(screen, controlObject, elementId),
    );

export const getAllParents = (
    screen: ScreenBase,
    fields: BaseControlObject<any, any>[],
): BaseControlObject<any, any>[] => {
    return fields.reduce<BaseControlObject<any, any>[]>((reduced, field) => {
        const parentGetter = (field as any).parent;
        if (parentGetter) {
            const parent = parentGetter.apply(screen);
            return reduced.concat([parent, ...getAllParents(screen, [parent])]);
        }
        return reduced;
    }, []);
};

export function getParentSection(screenDefinition: ScreenBaseDefinition, elementId: string): string | null {
    const elementProperties = screenDefinition.metadata.uiComponentProperties[elementId];
    if (!elementProperties) {
        return null;
    }

    if (elementProperties._controlObjectType === ContainerKey.Section) {
        return elementId;
    }

    const parentElement: BlockControlObject | SectionControlObject = (
        elementProperties as HasParent<any, BlockControlObject | SectionControlObject>
    ).parent?.apply(getScreenElement(screenDefinition));
    if (parentElement?.id) {
        return getParentSection(screenDefinition, parentElement.id);
    }

    return null;
}

export const getPropertyScalarType = (
    nodeTypes: Dict<FormattedNodeDetails>,
    propertyType: string,
    fieldType: NestedFieldTypes,
    valueField?: ReferenceProperties<any>['valueField'], // valueField will be received only for referenceFields
): GraphQLTypes | null => {
    // If we can't find a scalar type for the field, we prevent it from being displayed in the filter manager
    const selectFieldScalarType = fieldType === FieldKey.Select ? GraphQLTypes.Enum : null;
    const matchingScalarType = objectKeys(GraphQLTypes).find(typeKey => GraphQLTypes[typeKey] === propertyType) as
        | GraphQLTypes
        | undefined;
    const referenceFieldPropertyType =
        valueField && nodeTypes[propertyType]
            ? (get(nodeTypes[propertyType].properties.type, getReferencePath<any>(valueField)) as GraphQLTypes)
            : null;
    return selectFieldScalarType || matchingScalarType || referenceFieldPropertyType;
};

export function handleChange<V>(
    bind: string,
    value: V, // TODO Type this properly
    setFieldValue: (bind: string, value: V) => Promise<void>,
    validate?: (bind: string, value: V) => Promise<ValidationResult[] | undefined>,
    onChange?: (newValue: V) => void,
): void {
    setFieldValue(bind, value)
        .then(() => {
            let validationPromise: Promise<ValidationResult[] | undefined> = Promise.resolve(undefined);
            if (validate) {
                validationPromise = validate(bind, value);
            } else if (isDevMode()) {
                xtremConsole.info(
                    `Validation is being skipped on ${bind} since it does not provide a validate handler`,
                );
            }

            if (onChange && validationPromise) {
                // eslint-disable-next-line no-console
                validationPromise.then(() => onChange(value)).catch(xtremConsole.error);
            } else if (isDevMode()) {
                xtremConsole.info(
                    `Change event cannot be triggered on ${bind} since it does not provide a change handler`,
                );
            }
        })
        .catch(err => {
            xtremConsole.error(`Failed to set the value of ${bind}`, err);
        });
}

export const normalizeUnderscoreBind = (bind: string): string => bind.split('__')[0];

export const findColumnDefinitionByBind = <T extends NestedField<ScreenBase, NestedFieldTypes> | FilterManagerField>(
    columns: T[],
    bind: string,
): T | undefined =>
    columns.find(c => getNestedFieldElementId(c) === bind || getNestedFieldElementId(c).startsWith(`${bind}__`));

export const filterColumnDefinitionByBind = <T extends NestedField<ScreenBase, NestedFieldTypes> | FilterManagerField>(
    columns: T[],
    bind: string,
): T[] =>
    columns.filter(c => getNestedFieldElementId(c) === bind || getNestedFieldElementId(c).startsWith(`${bind}__`));
