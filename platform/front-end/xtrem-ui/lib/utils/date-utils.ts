import { CalendarDate } from '@internationalized/date';
import { DateValue, date } from '@sage/xtrem-date-time';
import type { AggFunc } from '../service/collection-data-utils';
import type { DatePropertyValue } from './types';

// Date part type constants
export const DATE_PART_YEAR = 'year';
export const DATE_PART_MONTH = 'month';
export const DATE_PART_DAY = 'day';

export function isRowValueInDateGroup(rowValue: string, groupValue: string, aggFunc: AggFunc): boolean {
    const dateValue = date.parse(rowValue);
    const groupDate = date.parse(groupValue);
    const isSameYear = (): boolean => dateValue.year === groupDate.year;
    const isSameMonth = (): boolean => dateValue.month === groupDate.month;
    const isSameDay = (): boolean => dateValue.day === groupDate.day;
    if (aggFunc === DATE_PART_YEAR) {
        return isSameYear();
    }
    if (aggFunc === DATE_PART_MONTH) {
        return isSameYear() && isSameMonth();
    }
    if (aggFunc === DATE_PART_DAY) {
        return isSameYear() && isSameMonth() && isSameDay();
    }
    return false;
}

export function getTimezoneUtcOffsetByCity(timeZone: string): string {
    const formatter = new Intl.DateTimeFormat([], { timeZoneName: 'longOffset', timeZone });
    const dummyDate = formatter.format(new Date());
    const parts = dummyDate.split(', GMT');
    return `GMT${parts[1] || ''}`;
}

export function datePropertyValueToCalendarDate(value: DatePropertyValue | undefined): CalendarDate | undefined {
    if (!value) return undefined;
    if (value instanceof CalendarDate) return value;
    if (DateValue.isDate(value)) {
        return new CalendarDate(value.year, value.month, value.day);
    }
    if (value instanceof Date) {
        return new CalendarDate(value.getFullYear(), value.getMonth() + 1, value.getDate());
    }
    if (typeof value === 'string') {
        try {
            const dateValue = DateValue.parse(value);
            return new CalendarDate(dateValue.year, dateValue.month, dateValue.day);
        } catch {
            const date = new Date(value);
            if (!Number.isNaN(date.getTime())) {
                return new CalendarDate(date.getFullYear(), date.getMonth() + 1, date.getDate());
            }
            return undefined;
        }
    }
    return undefined;
}
