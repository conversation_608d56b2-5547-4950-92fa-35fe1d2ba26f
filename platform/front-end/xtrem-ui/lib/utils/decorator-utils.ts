import type { <PERSON><PERSON>N<PERSON>, Dict } from '@sage/xtrem-client';
import type { AbstractFieldDecorator } from '../component/abstract-field-decorator';
import type { ComponentKey, DecoratorTarget, FieldKey, LayoutContent, MetadataProps } from '../component/types';
// eslint-disable-next-line @typescript-eslint/consistent-type-imports
import { DecoratorProperties } from '../component/types';
import type { Extend } from '../service/page-extension';
import { getPageMetadata } from '../service/page-metadata';
import { getDeclarationPackage, getXtremArtifactVariable } from '../service/screen-loader-service';
import type { ScreenExtension } from '../types';
import { checkFilterAndNode } from './warnings';
import { convertDeepBindToPath } from './nested-field-utils';
import type { ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import type { DataTypeDetails, FormattedNodeDetails } from '../service/metadata-types';
import type { FragmentFieldsDecoratorProperties } from '../component/decorator-properties';
import type { HasParent, VoidPromise } from '../component/field/traits';
import { objectKeys } from '@sage/xtrem-shared';

export type OverrideDecoratorProperties<DecoratorProperties> = Partial<
    Omit<
        DecoratorProperties,
        | 'aggregateOn'
        | 'aggregationMethod'
        | 'bind'
        | 'boundTo'
        | 'chart'
        | 'columns'
        | 'endDateField'
        | 'eventCard'
        | 'fields'
        | 'helperTextField'
        | 'insertAfter'
        | 'insertBefore'
        | 'isTransient'
        | 'levels'
        | 'mobileCard'
        | 'node'
        | 'optionType'
        | 'parent'
        | 'pluginPackage'
        | 'startDateField'
        | 'valueField'
    >
>;

export type OverrideNestedDecoratorProperties<DecoratorProperties> = Partial<
    Omit<
        DecoratorProperties,
        | 'aggregateOn'
        | 'aggregationMethod'
        | 'chart'
        | 'columns'
        | 'endDateField'
        | 'eventCard'
        | 'fields'
        | 'helperTextField'
        | 'insertAfter'
        | 'insertBefore'
        | 'isTransient'
        | 'levels'
        | 'mobileCard'
        | 'node'
        | 'optionType'
        | 'parent'
        | 'pluginPackage'
        | 'startDateField'
    >
>;

export type ClickableOverrideDecoratorProperties<DecoratorProperties, CT> = {
    onClickAfter?: (this: CT) => VoidPromise;
} & OverrideDecoratorProperties<DecoratorProperties>;

export type ChangeableOverrideDecoratorProperties<DecoratorProperties, CT> = {
    onChangeAfter?: (this: CT) => VoidPromise;
} & ClickableOverrideDecoratorProperties<Omit<DecoratorProperties, '_controlObjectType'>, CT>;

export type ClickableNestedOverrideDecoratorProperties<DecoratorProperties, CT, C extends ClientNode = any> = {
    onClickAfter?: (this: CT, id: string, rowValue: C) => VoidPromise;
} & OverrideDecoratorProperties<DecoratorProperties>;

export type ChangeableNestedOverrideDecoratorProperties<DecoratorProperties, CT, C extends ClientNode = any> = {
    onChangeAfter?: (this: CT, id: string, rowValue: C) => VoidPromise;
} & ClickableOverrideDecoratorProperties<DecoratorProperties, CT>;

/**
 * Checks if the constructor passed in is a page or an extension. If an extension, it returns the page constructor
 * that is being extended. For more information, see the screen-loader-service's evaluateJavascriptResponse method
 * @param constructor
 */
export const getTargetPrototype = (constructor: Function): Function => {
    /**
     * The screen loader services adds the `xtremExports` function to the "window" object as it is accessible from
     * any scope.
     *  */
    const xtremArtifact = getXtremArtifactVariable();
    if (constructor.name.endsWith('Extension') && xtremArtifact) {
        const keys = objectKeys(xtremArtifact);
        if (keys.length < 1) {
            throw new Error('Invalid base prototype found when trying to load page extension.');
        }
        return xtremArtifact[constructor.name.replace('Extension', '')];
    }
    if (constructor.prototype.___isPageFragment) {
        const artifacts = objectKeys(xtremArtifact);
        return xtremArtifact[artifacts[0]];
    }
    return constructor;
};

/**
 * Standard decorator implementation. It implements the basic builder behavior which can be used by most of the fields
 * and containers.
 *
 * It creates a thunk function and appends it to the `pageMetadata` object so it can be executed once all information
 * is gathered from ALL decorator properties. See the screen loader service for further details.
 */
export function standardDecoratorImplementation<
    T extends ScreenExtension<T>,
    D extends FieldKey,
    N extends ClientNode = any,
>(
    properties: DecoratorProperties<D, Extend<T>, N>,
    decoratorClass: new (
        target: DecoratorTarget<T>,
        name: string,
        componentType: D,
        nodeTypes: Dict<FormattedNodeDetails>,
        dataTypes: Dict<DataTypeDetails>,
        metadataProps: MetadataProps<D>,
    ) => AbstractFieldDecorator<D>,
    componentType: D,
    checkForNode = false,
): (target: T, name: string) => void {
    // eslint-disable-next-line func-names
    return function (target: T, elementId: string): void {
        const extensionPackageName = getDeclarationPackage();
        if (checkForNode) {
            checkFilterAndNode(elementId, properties);
        }

        const pageMetadata = getPageMetadata(getTargetPrototype(target.constructor), target);

        // If target is a PageFragment we need to modify the decorator
        if (target.constructor.prototype.___isPageFragment) {
            // We load the fragment fields thunks
            objectKeys(pageMetadata.fragmentFieldsThunks).forEach(fft => pageMetadata.fragmentFieldsThunks[fft]());
            const fragmentKey = objectKeys(pageMetadata.uiComponentProperties).find(key => {
                return (
                    (pageMetadata.uiComponentProperties[key] as FragmentFieldsDecoratorProperties).fragment.split(
                        '/',
                    )[2] === target.constructor.name
                );
            });
            if (fragmentKey) {
                // The field coming from PageFragment needs to have the parent of the fragmentsfields
                (properties as HasParent<any, any>).parent = (
                    pageMetadata.uiComponentProperties[fragmentKey] as HasParent<any, any>
                ).parent;
                const index = pageMetadata.definitionOrder.indexOf(fragmentKey);
                // We need to insert the field after the fragment fields
                pageMetadata.definitionOrder.splice(index, 0, elementId);
                if (!pageMetadata.fragmentFields[target.constructor.name]) {
                    pageMetadata.fragmentFields[target.constructor.name] = [];
                }
                pageMetadata.fragmentFields[target.constructor.name].push(elementId);
            }
        } else {
            pageMetadata.definitionOrder.push(elementId);
        }

        pageMetadata.fieldBindings[elementId] =
            convertDeepBindToPath((properties as ReadonlyFieldProperties).bind) || elementId;
        pageMetadata.fieldThunks[elementId] = (
            nodeTypes: Dict<FormattedNodeDetails>,
            dataTypes: Dict<DataTypeDetails>,
        ): LayoutContent<D> =>
            // eslint-disable-next-line new-cap
            new decoratorClass(pageMetadata.target! as any, elementId, componentType, nodeTypes, dataTypes, {
                pageMetadata,
                properties,
                extensionPackageName,
            }).build().layout;
    };
}

/**
 * Standard extension decorator implementation.
 *
 */
export function standardExtensionDecoratorImplementation<
    CT extends ScreenExtension<CT>,
    D extends ComponentKey,
    N extends ClientNode = any,
>(properties: OverrideDecoratorProperties<DecoratorProperties<D, Extend<CT>, N>>): (target: CT, name: string) => void {
    // eslint-disable-next-line func-names
    return function (target: CT, name: string): void {
        const extensionPackageName = getDeclarationPackage();
        const isExtension = target.constructor.name.endsWith('Extension');
        if (!isExtension) {
            throw new Error(
                'Property override decorators (those that ends with Extension) can only be used in extension screens.',
            );
        }
        const proto = getTargetPrototype(target.constructor);
        if (!proto) {
            const baseClassName = target.constructor.name.replace('Extension', '');
            throw new Error(
                `The base class of your extension (${baseClassName}) was not found. Make sure to follow the extension naming conventions.`,
            );
        }
        const metadata = getPageMetadata(proto);
        if (
            !metadata.fieldThunks[name] &&
            !metadata.pageActionThunks[name] &&
            !metadata.sectionThunks[name] &&
            !metadata.blockThunks[name]
        ) {
            throw new Error(
                `The targeted screen member does not exist on the screen that you are about to extend. (${target.constructor.name}: ${name})`,
            );
        }

        const extensionOverrideThunks = metadata.extensionOverrideThunks[name] || [];
        extensionOverrideThunks.push(() => ({ ...properties, _declaredInExtension: extensionPackageName }));
        metadata.extensionOverrideThunks[name] = extensionOverrideThunks;
    };
}
