import type { ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import type { PageArticleItem } from '../service/layout-types';
import type { PageDefinition } from '../service/page-definition';
import type { PageMetadata } from '../service/page-metadata';
import { convertDeepBindToPath } from './nested-field-utils';
import { section } from '../component/container/section/section-decorator';
import { block } from '../component/container/block/block-decorator';
import type { SectionControlObject } from '../component/container/section/section-control-object';
import { objectKeys } from '@sage/xtrem-shared';

const mapDuplicateKeyToLayoutProperty =
    (pageDefinition: PageDefinition) =>
    (bind: string): PageArticleItem => {
        // Find the corresponding field property name by searching for the bind decorator property
        const $bind = objectKeys(pageDefinition.metadata.uiComponentProperties).find((key: string) => {
            const fieldProperties = pageDefinition.metadata.uiComponentProperties[key] as ReadonlyFieldProperties;
            return convertDeepBindToPath(fieldProperties.bind) === bind || (!fieldProperties.bind && key === bind);
        });

        if (!$bind) {
            throw new Error(`Could not find duplicate field definition for ${bind}`);
        }

        return {
            $bind,
            $containerType: 'block',
            $isFullWidth: false,
        };
    };

/**
 * Replaces the page's business actions with the duplicate actions
 * It mutates the pageDefinition object
 * */
export function applyDuplicationBusinessActions(pageDefinition: PageDefinition): void {
    pageDefinition.metadata.businessActionsThunk = (): any => {
        return [
            pageDefinition.metadata.controlObjects.$standardCancelAction,
            pageDefinition.metadata.controlObjects.$standardExecuteDuplicationAction,
        ];
    };
}
/**
 * Replaces the page's layout with a single section that contains only the fields which are required for duplication
 * It mutates the pageDefinition object
 * */
export function applyDuplicationLayoutStructure(pageDefinition: PageDefinition): void {
    const duplicateBindings = pageDefinition.metadata.duplicateBindings || [];

    pageDefinition.metadata.layout = {
        $items: [
            {
                $containerId: '$duplicateSection', // TODO: MAKE THIS A CONSTANT
                $isHiddenMobile: false,
                $isHiddenDesktop: false,
                $category: 'section',
                $layout: {
                    $items: [
                        {
                            $containerId: '$duplicateBlock', // TODO: MAKE THIS A CONSTANT
                            $isHiddenMobile: false,
                            $isHiddenDesktop: false,
                            $category: 'block',
                            $layout: {
                                $items: duplicateBindings.map(mapDuplicateKeyToLayoutProperty(pageDefinition)),
                            },
                        },
                    ],
                },
            },
        ],
    };
}

/**
 * Add a section and a block control object to the page definition
 * @param pageMetadata
 * @returns
 */
export function addDuplicationLayoutContainers(pageMetadata: PageMetadata): void {
    if (!pageMetadata?.target) {
        return;
    }

    section({ isTitleHidden: true })(pageMetadata.target, '$duplicateSection');
    block({
        isTitleHidden: true,
        parent(this: any) {
            return this._duplicateSection as SectionControlObject;
        },
    })(pageMetadata.target, '$duplicateBlock');
}
