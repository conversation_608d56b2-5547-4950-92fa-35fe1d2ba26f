import type { AccessStatus, Dict } from '@sage/xtrem-shared';
import type { CollectionActionAccessConfiguration, HasAccessRights } from '../component/field/traits';
import type { ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import type { AccessBindings } from '../service/page-definition';
import type { NodePropertyType } from '../types';
import type { DataTypeDetails, FormattedNodeDetails } from '../service/metadata-types';
import { isDevMode } from './window';
import { get } from 'lodash';
import { findDeepPropertyDetails } from './node-utils';
import { schemaTypeNameFromNodeName } from './transformers';

export interface AccessStatusProps {
    accessBindings: AccessBindings;
    // TODO: handle nested access rights
    bind: any;
    elementProperties: ReadonlyFieldProperties | HasAccessRights;
    contextNode?: NodePropertyType;
    nodeTypes: Dict<FormattedNodeDetails>;
    dataTypes: Dict<DataTypeDetails>;
}
export const getElementAccessStatus = ({
    accessBindings,
    // TODO: handle nested access rights
    bind,
    elementProperties,
    contextNode,
    nodeTypes,
}: AccessStatusProps): AccessStatus | undefined => {
    const accessNode = elementProperties.access?.node || contextNode;
    if (!accessNode) {
        return undefined;
    }

    const accessBind = elementProperties.access?.bind || (elementProperties as ReadonlyFieldProperties).bind || bind;

    // Actions are not on the node types, so we need to handle them separately
    if (accessBind[0] === '$') {
        return accessBindings[schemaTypeNameFromNodeName(String(accessNode))]?.[accessBind] || undefined;
    }

    const propertyDetails = findDeepPropertyDetails(
        schemaTypeNameFromNodeName(String(accessNode)),
        accessBind,
        nodeTypes,
    );

    if (!propertyDetails || !propertyDetails.name) {
        return undefined;
    }

    return accessBindings[propertyDetails.parentNode]?.[propertyDetails.name] || undefined;
};

export const getElementAccessStatusWithoutId = (
    accessBindings: AccessBindings,
    access?: CollectionActionAccessConfiguration,
): AccessStatus => {
    if (!access) {
        return 'authorized';
    }

    if (!access.bind || !access.node) {
        if (isDevMode()) {
            throw new Error('Invalid access definition, you must define both bind and node properties.');
        }
        return 'authorized';
    }

    return get(
        accessBindings,
        `${schemaTypeNameFromNodeName(access.node)}.${access.bind}`,
        'unauthorized',
    ) as AccessStatus;
};
