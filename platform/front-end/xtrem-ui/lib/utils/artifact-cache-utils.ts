import * as idb from 'idb';
import { ARTIFACT_CACHE_DEFAULT_PASSPHRASE, ARTIFACT_CACHE_STORE, ARTIFACT_DATABASE_NAME } from './constants';
import type { GraphqlCacheSettings } from '../service/graphql-utils';
import { decryptJsonDocument, encryptJsonDocument } from '../service/encryption-service';
import { get } from 'lodash';
import type { RawNodeDetails } from '../service/metadata-types';
import type { InstalledPackageList } from '../service/metadata-service';

const isUpToDateArtifact =
    (installedPackages: InstalledPackageList) =>
    (key: string[]): boolean => {
        const [cachePath, cachedVersion] = key;
        const cachePathSegments = cachePath.split('/');
        const cachedPackageName = `${cachePathSegments[1]}/${cachePathSegments[2]}`;
        const expectedVersion = installedPackages.find(pkg => pkg.name === cachedPackageName)?.version;
        return expectedVersion === cachedVersion;
    };

export interface ArtifactCacheEntry {
    key: string;
    version: string;
    locale: string;
    shouldFetchPlatformLiterals: string;
    /** Encrypted cache data */
    data: any;
    /** Cached timestamp */
    cachedAt: number;
}

export function getArtifactCacheDatabase(): Promise<idb.IDBPDatabase> {
    return idb.openDB(ARTIFACT_DATABASE_NAME, 1, {
        async upgrade(database) {
            // The records are tracked by the key and the version number of the corresponding package.
            database.createObjectStore(ARTIFACT_CACHE_STORE, {
                keyPath: ['key', 'version', 'locale', 'shouldFetchPlatformLiterals'],
            });
        },
    });
}

export async function getArtifactCachedEntry({
    db,
    cacheSettings,
    passphrase = ARTIFACT_CACHE_DEFAULT_PASSPHRASE,
}: {
    db: idb.IDBPDatabase;
    cacheSettings: GraphqlCacheSettings;
    passphrase?: string;
}): Promise<any | null> {
    const cacheKey = [
        cacheSettings.key,
        cacheSettings.version,
        cacheSettings.locale,
        String(cacheSettings.shouldFetchPlatformLiterals),
    ];

    const value = await db.get(ARTIFACT_CACHE_STORE, cacheKey);

    if (!value) {
        return null;
    }

    const decryptedData = decryptJsonDocument(value.data, passphrase);

    if (!decryptedData) {
        // If the decryption fails, we delete the cached entry
        const tx = db.transaction(ARTIFACT_CACHE_STORE, 'readwrite');
        await tx.store.delete(cacheKey);
        await tx.done;
        return null;
    }

    return {
        ...decryptedData,
        key: value.key,
        version: value.version,
        locale: value.locale,
        shouldFetchPlatformLiterals: value.shouldFetchPlatformLiterals,
        cachedAt: value.cachedAt,
    };
}

export async function cacheArtifact({
    db,
    cacheSettings,
    data,
    passphrase = ARTIFACT_CACHE_DEFAULT_PASSPHRASE,
}: {
    db: idb.IDBPDatabase;
    cacheSettings: GraphqlCacheSettings;
    data: any;
    passphrase?: string;
}): Promise<void> {
    const tx = db.transaction(ARTIFACT_CACHE_STORE, 'readwrite');
    const encryptedData = encryptJsonDocument(data, passphrase);
    if (encryptedData) {
        // Add cache settings to the object which contain the key fields.
        const cacheEntry: ArtifactCacheEntry = {
            data: encryptedData,
            key: cacheSettings.key,
            version: cacheSettings.version,
            locale: cacheSettings.locale,
            shouldFetchPlatformLiterals: String(cacheSettings.shouldFetchPlatformLiterals),
            cachedAt: Date.now(),
        };
        await tx.store.put(cacheEntry);
        await tx.done;
    }
}

export async function getCachedRawNodeDetails({
    db,
    installedPackages,
    passphrase = ARTIFACT_CACHE_DEFAULT_PASSPHRASE,
}: {
    db: idb.IDBPDatabase;
    installedPackages: InstalledPackageList;
    passphrase?: string;
}): Promise<RawNodeDetails[]> {
    const keys = await db.getAllKeys(ARTIFACT_CACHE_STORE);
    const keysToFetch = keys.filter(isUpToDateArtifact(installedPackages));
    const nodeDetails: RawNodeDetails[] = [];
    // eslint-disable-next-line no-restricted-syntax
    for (const key of keysToFetch) {
        const entry = await db.get(ARTIFACT_CACHE_STORE, key);
        const decodedDocument = decryptJsonDocument(entry.data, passphrase);
        if (decodedDocument && Array.isArray(key)) {
            const artifactType = String(key[0]).split('/')[0];
            nodeDetails.push(...get<RawNodeDetails[]>(decodedDocument, `data.${artifactType}.0.nodeDetails`, []));
        }
    }

    return nodeDetails;
}

/**
 * Clear out of date entries from the cache
 */
export async function clearOutOfDateEntries({
    db,
    installedPackages,
}: {
    db: idb.IDBPDatabase;
    installedPackages: InstalledPackageList;
}): Promise<void> {
    const keys = await db.getAllKeys(ARTIFACT_CACHE_STORE);
    const outOfDateEntries = keys.filter(k => !isUpToDateArtifact(installedPackages)(k as string[]));
    const tx = db.transaction(ARTIFACT_CACHE_STORE, 'readwrite');
    // eslint-disable-next-line no-restricted-syntax
    for (const key of outOfDateEntries) {
        await tx.store.delete(key);
    }

    await tx.done;
}
