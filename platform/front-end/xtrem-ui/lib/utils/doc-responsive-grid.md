PATH: XTREEM/Client+Framework/Responsive+Grid

This document lays out the framework's responsive grid and the options that the framework provides to developers.

## Introduction

The foundation for the grid system is the Sage Design Language System's [grid proposal](https://zeroheight.com/2ccf2b601/p/58ef5d-grid-system/b/01243e). Some slight changes were applied to better cater to the medium segment customers and users.

## Breakpoints

Frameworks responsive system identifies 5 distinct viewports which change the application behavior:

| Breakpoint | Screen width |
| ---------- | ------------ |
| XL         | > 1920px     |
| L          | < 1920px     |
| M          | < 1260px     |
| S          | < 960px      |
| XS         | < 704px      |

## Containers

To define the content of a screen, the developers can add various containers to the screen. Moreover, developers can define the width of these containers. The width is defined using T-Shirt sizes, which map to various column sizes depending on the current breakpoint. The following T-Shirt sizes are available for containers: `extra-large`, `large`, `medium`, `small` and `half`.

The table below defines how these size categories map to columns depending on the screen width:

| Breakpoint | XL container | L container | M container | S container |
| ---------- | ------------ | ----------- | ----------- | ----------- |
| XL         | 12           | 8           | 6           | 4           |
| L          | 12           | 8           | 6           | 4           |
| M          | 8            | 8           | 4           | 4           |
| S          | 8            | 8           | 4           | 4           |
| XS         | 4            | 4           | 4           | 4           |
| Half       | 6            | 6           | 4           | 4           |

The calculated container width is inherited by the container, which means if a container's calculated width is 6 columns, its content is also organized into 6 columns.
If the container width is not defined by the developer, all containers are set to `extra-large` by the framework.

## Fields

Fields work very similarly to containers, field width also can be defined using T-Shirt sizes. There are only 4 sizes for fields: `large`, `medium`, `medium-small`, `small` and `half`.

| Breakpoint | L field | M field | SM field | S field | Half field |
| ---------- | ------- | ------- | -------- | ------- | ---------- |
| XL         | 4       | 3       | 3        | 2       | 6          |
| L          | 4       | 3       | 3        | 2       | 6          |
| M          | 4       | 4       | 2        | 2       | 4          |
| S          | 4       | 4       | 2        | 2       | 4          |
| XS         | 4       | 4       | 2        | 2       | 2          |

If the field width is not defined by the developer, all fields are set to `small` by the framework. If the `isFullWith` property is defined on the field with `true` value, it takes precedence and the container's width is applied to the field.
Non-text field-based fields are exceptions from this rule, these fields are the following:

- Calendar
- DetailList
- FormDesigner
- Image
- Message
- MultiFileDeposit
- NestedGrid
- NodeBrowserTree
- PodCollection
- Preview
- RichText
- SelectionCard
- StepSequence
- Table
- TableSummary
- Tree
- VisualProcess

## Context

Containers and fields can be used in various contexts across the pages, for example on the page body, in dialogs or on the details panel. The context determines the number of the available of available columns. Any columns greater than the available space will be capped.

| Context                                        | Maximum number of columns |
| ---------------------------------------------- | ------------------------- |
| Page body on large screens                     | 12                        |
| Page body on medium and small screens          | 8                         |
| Page body on extra small screens               | 4                         |
| Standard dialogs                               | 4                         |
| Full-screen dialog on large screens            | 12                        |
| Full-screen dialog on medium and small screens | 8                         |
| Full-screen dialog on extra small screens      | 4                         |
| Details panel                                  | 4                         |

## Dashboards

Dashboards make use of the following breakpoints.
The active breakpoint defines how many columns are available.

| Breakpoint | Screen width | Columns      |
| ---------- | ------------ | ------------ |
| L          | > 1920px     | 12           |
| M          | < 1260px     | 8            |
| S          | < 960px      | 6            |
| XS         | < 704px      | 4            |
| XXS        | < 350px      | 2            |
