import { type Dict, objectKeys } from '@sage/xtrem-shared';
import type { UiComponentProperties } from '../component/abstract-ui-control-object';
import { SectionControlObject } from '../component/control-objects';
import { isFieldDisabled } from '../component/field/carbon-helpers';
import type { CanBeReadOnly, HasParent } from '../component/field/traits';
import type { ReadonlyFieldControlObject, ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import { getStore } from '../redux/store';
import type { PageArticleItem, PageArticleLayout } from '../service/layout-types';
import type { AccessBindings, PageDefinition } from '../service/page-definition';
import type { ScreenBase } from '../service/screen-base';
import type { DialogDescription } from '../types/dialogs';
import { getElementAccessStatus } from './access-utils';
import { resolveByValue } from './resolve-value-utils';
import { getPagePropertiesFromPageDefinition } from './state-utils';
import type { NodePropertyType } from '../types';
import type { DataTypeDetails, FormattedNodeDetails } from '../service/metadata-types';
import type { SectionProperties } from '../component/container/section/section-types';

export const getFieldOrderAndFocusability = (
    screenId: string,
    articleItem: PageArticleLayout,
    uiComponentProperties: Dict<UiComponentProperties<ScreenBase>>,
    isParentFocusable: boolean,
    collector: Dict<boolean> = {},
): Dict<boolean> => {
    if (articleItem.$items) {
        articleItem.$items.forEach(i => {
            if (i.$layout && i.$containerId) {
                const isContainerHidden = resolveByValue({
                    screenId,
                    skipHexFormat: true,
                    propertyValue: uiComponentProperties[i.$containerId].isHidden,
                    rowValue: null, // Nested fields are not supported yet
                });
                getFieldOrderAndFocusability(
                    screenId,
                    i.$layout,
                    uiComponentProperties,
                    isParentFocusable && !isContainerHidden,
                    collector,
                );
            } else if (i.$bind) {
                const isFieldHidden = resolveByValue({
                    screenId,
                    skipHexFormat: true,
                    propertyValue: uiComponentProperties[i.$bind].isHidden,
                    rowValue: null, // Nested fields are not supported yet
                });
                const isFieldCurrentlyDisabled = resolveByValue({
                    screenId,
                    skipHexFormat: true,
                    propertyValue: uiComponentProperties[i.$bind].isDisabled,
                    rowValue: null, // Nested fields are not supported yet
                });
                const isFieldReadOnly = resolveByValue({
                    screenId,
                    skipHexFormat: true,
                    propertyValue: (uiComponentProperties[i.$bind] as CanBeReadOnly<any, any>).isReadOnly,
                    rowValue: null, // Nested fields are not supported yet
                });
                collector[i.$bind] =
                    isParentFocusable && !isFieldHidden && !isFieldCurrentlyDisabled && !isFieldReadOnly;
            }
        });
    }
    return collector;
};

export const findNextField = (
    screenId: string,
    currentElementId?: string,
    isFocusable = false,
): ReadonlyFieldControlObject<any, any, any, any> | null => {
    const state = getStore().getState();
    const screenDefinition = state.screenDefinitions[screenId];
    const layout = screenDefinition.metadata.layout;
    const fieldOrder = getFieldOrderAndFocusability(
        screenId,
        layout,
        screenDefinition.metadata.uiComponentProperties,
        true,
    );
    const keys = objectKeys(fieldOrder);
    const currentPosition = keys.indexOf(currentElementId || '');
    const followingKeys = keys.slice(currentPosition + 1);

    if (followingKeys.length === 0) {
        return null;
    }

    if (isFocusable) {
        const focusableKey = followingKeys.find(k => fieldOrder[k]);
        return focusableKey
            ? (screenDefinition.metadata.controlObjects[focusableKey] as ReadonlyFieldControlObject<any, any, any, any>)
            : null;
    }

    return screenDefinition.metadata.controlObjects[followingKeys[0]] as ReadonlyFieldControlObject<any, any, any, any>;
};

export interface IsHiddenOrDisabledInLayoutArgs {
    componentProperties: Dict<UiComponentProperties>;
    screenId: string;
    elementId: string;
    accessBindings: AccessBindings;
    contextNode?: NodePropertyType;
    nodeTypes: Dict<FormattedNodeDetails>;
    dataTypes: Dict<DataTypeDetails>;
}

export const isHiddenOrDisabledInLayout = ({
    componentProperties,
    screenId,
    elementId,
    accessBindings,
    contextNode,
    nodeTypes,
    dataTypes,
}: IsHiddenOrDisabledInLayoutArgs): boolean => {
    const properties = componentProperties[elementId] as UiComponentProperties<any> &
        HasParent<any, any> &
        ReadonlyFieldProperties<any>;
    const isDisabled = isFieldDisabled(screenId, properties, null, null);

    // TODO: Nested validations should pass in context with value and handler args
    const isHidden = resolveByValue<boolean>({
        screenId,
        propertyValue: properties.isHidden,
        skipHexFormat: true,
        rowValue: null,
    });

    // If the user doesn't have access to a container, then it will be disabled or hidden.
    const accessStatus = getElementAccessStatus({
        accessBindings,
        bind: elementId,
        elementProperties: properties,
        contextNode,
        nodeTypes,
        dataTypes,
    });
    // TODO: Nested validations should pass in context with value and handler args
    if (isDisabled || isHidden || accessStatus === 'unauthorized' || accessStatus === 'unavailable') {
        return true;
    }

    if (properties.parent) {
        const parentElement = resolveByValue({
            screenId,
            propertyValue: properties.parent,
            skipHexFormat: true,
            rowValue: null,
        });
        if (parentElement) {
            return isHiddenOrDisabledInLayout({
                componentProperties,
                screenId,
                elementId: parentElement.elementId,
                accessBindings,
                contextNode,
                dataTypes,
                nodeTypes,
            });
        }
    }

    return false;
};

export const isSectionUsedInAnyDialogs = (
    activeDialogs: Dict<DialogDescription>,
    sectionContainerId: string,
): boolean => {
    const dialogIds = objectKeys(activeDialogs);
    // eslint-disable-next-line no-restricted-syntax
    for (const dialogId of dialogIds) {
        const dialog = activeDialogs[dialogId];
        if (dialog.content instanceof SectionControlObject && dialog.content.id === sectionContainerId) {
            return true;
        }

        if (
            dialog.content instanceof Array &&
            dialog.content.find(c => c instanceof SectionControlObject && c.id === sectionContainerId)
        ) {
            return true;
        }
    }
    return false;
};

export const getHeaderSection = (
    pageDefinition: PageDefinition,
    isSmallScreen = false,
): SectionControlObject | null => {
    if (isSmallScreen) {
        // The header section functionality is disabled on mobile, so if we run on a small screen we assume that it is just not defined.
        return null;
    }
    const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);
    return pageProperties.headerSection?.apply(pageDefinition.page) || null;
};

export const getVisibleSections = (
    pageDefinition: PageDefinition,
    activeDialogs: Dict<DialogDescription>,
    isSmallScreen = false,
): Partial<PageArticleItem>[] => {
    const headerSection = getHeaderSection(pageDefinition, isSmallScreen);
    const sectionIds: string[] = pageDefinition.metadata.layout.$items.map(section => section.$containerId!);
    const sectionsProperties = objectKeys(pageDefinition.metadata.uiComponentProperties)
        .filter(property => sectionIds.indexOf(property) > -1)
        .reduce(
            (properties, nextKey) => ({
                ...properties,
                [nextKey]: pageDefinition.metadata.uiComponentProperties[nextKey],
            }),
            {} as Dict<SectionProperties>,
        );

    return pageDefinition.metadata.layout.$items.filter(s => {
        const isInDialog = pageDefinition.isInDialog;
        const isHeaderSection = headerSection && s.$containerId === headerSection.id;
        if (!isInDialog && isHeaderSection) {
            return false;
        }

        if (isInDialog && isHeaderSection && isSmallScreen) {
            return false;
        }

        return (
            s.$containerId &&
            // Section is not hidden
            !resolveByValue({
                screenId: pageDefinition.metadata.screenId,
                skipHexFormat: true,
                propertyValue: sectionsProperties[s.$containerId].isHidden,
                rowValue: null, // Nested fields are not supported yet
            }) &&
            // Section is not used in any custom dialogs
            !isSectionUsedInAnyDialogs(activeDialogs, s.$containerId)
        );
    });
};

export const focusFirstElement = (selector: string, hasDialogActionButtons = false): void => {
    const elements = Array.from(document.querySelectorAll(selector));
    let firstFocusableElement;
    if (elements.length) {
        const focusableElements = Array.from(
            elements[elements.length - 1].querySelectorAll(
                'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])',
            ),
        ).filter(
            el =>
                !el.hasAttribute('disabled') &&
                !el.getAttribute('aria-hidden') &&
                !el.classList.contains('e-navigation-panel-toggle'),
        );
        if (focusableElements.length) {
            firstFocusableElement = focusableElements[
                !hasDialogActionButtons ? 0 : focusableElements.length - 1
            ] as HTMLButtonElement;
        }
        firstFocusableElement?.focus();
    }
};

export function getLayoutChildren(item: Partial<PageArticleItem>, collector: string[] = []): string[] {
    if (item.$layout?.$items) {
        item.$layout?.$items.forEach(i => {
            if (i.$layout && i.$containerId) {
                collector.push(i.$containerId);
                getLayoutChildren(i, collector);
            }
            if (i.$bind) {
                collector.push(i.$bind);
            }
        });
    }
    return collector;
}
