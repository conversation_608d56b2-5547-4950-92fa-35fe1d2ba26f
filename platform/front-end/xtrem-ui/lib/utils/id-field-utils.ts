import { formatDateToCurrentLocale } from '@sage/xtrem-date-time';
import type { LocalizeLocale } from '@sage/xtrem-shared';
import { get } from 'lodash';
import type { IdFieldType, PageHeaderFields } from '../component/container/page/page-types';
import {
    AggregateControlObject,
    CountControlObject,
    DateControlObject,
    DropdownListControlObject,
    LabelControlObject,
    NumericControlObject,
    ReferenceControlObject,
    SelectControlObject,
    TextControlObject,
} from '../component/control-objects';
import type { ReferenceDecoratorProperties, SelectDecoratorProperties } from '../component/decorators';
import { getReferenceValueField } from '../component/field/reference/reference-utils';
import { localizeEnumMember } from '../service/i18n-service';
import type { PageDefinition } from '../service/page-definition';
import { getScreenElement } from '../service/screen-base-definition';
import { formatNumericValue, getScalePrefixPostfixFromUnit } from './formatters';
import { getPagePropertiesFromPageDefinition } from './state-utils';

export const getFieldDisplayValue = (
    pageDefinition: PageDefinition,
    field: PageHeaderFields,
    locale?: string,
): string | null => {
    const value = field.value;
    if (value === null || value === '' || value === undefined) {
        return null;
    }

    const properties = pageDefinition.metadata.uiComponentProperties[field.id];
    if (field instanceof TextControlObject) {
        return value as string;
    }

    if (field instanceof CountControlObject) {
        return formatNumericValue({ screenId: pageDefinition.metadata.screenId, value, scale: 0, locale });
    }
    if (field instanceof NumericControlObject || field instanceof AggregateControlObject) {
        const computedUnitProperties = getScalePrefixPostfixFromUnit(
            pageDefinition.metadata.screenId,
            locale as LocalizeLocale,
            { unit: field.unit, unitMode: field.unitMode },
        );
        return formatNumericValue({
            screenId: pageDefinition.metadata.screenId,
            value,
            scale: computedUnitProperties?.scale ?? field.scale,
            locale,
        });
    }

    if (field instanceof ReferenceControlObject) {
        return get(value, getReferenceValueField(properties as ReferenceDecoratorProperties));
    }

    if (field instanceof DateControlObject) {
        return formatDateToCurrentLocale(value as string, locale as LocalizeLocale);
    }

    const selectProperties = properties as SelectDecoratorProperties;
    if (
        (field instanceof SelectControlObject ||
            field instanceof DropdownListControlObject ||
            field instanceof LabelControlObject) &&
        selectProperties.optionType
    ) {
        return localizeEnumMember(selectProperties.optionType, String(value));
    }

    return String(value);
};

export const getIdFieldValue = (pageDefinition: PageDefinition, locale?: string): string | null => {
    const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);
    if (!pageProperties.idField) {
        return null;
    }

    const idField: IdFieldType = pageProperties.idField.apply(getScreenElement(pageDefinition));
    if (!idField) {
        return null;
    }

    if (typeof idField === 'string') {
        return idField;
    }

    const idFieldArray = idField instanceof Array ? idField : [idField];
    const value = idFieldArray
        .map(f => getFieldDisplayValue(pageDefinition, f, locale))
        .filter(f => !!f)
        .join(' ')
        .trim();

    return value || null;
};
