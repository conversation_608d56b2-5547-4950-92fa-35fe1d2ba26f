export const SERVER_VALIDATION_RULE_PREFIX = 'server-';
export const NEW_PAGE = '$new';
export const VISUAL_PROCESS_FAKE_NODE_TYPE_PAGE = '@sage/xtrem-ui/VisualProcessPageNode';
export const REG_EXP_URL_PATTERN =
    // eslint-disable-next-line @sage/redos/no-vulnerable
    /(ftp|ftps|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-/]))?/;
// eslint-disable-next-line no-useless-escape
export const REG_EXP_INTERNAL_URL_PATTERN = /^@[a-zA-Z-]+\/[a-z\-]+\/[a-zA-Z]+/;
export const DUPLICATE_INDICATOR_QUERY_EDITOR_PARAM = '__duplicate';
export const DEEP_BIND_QUERY_ALIAS_GLUE = '__';

/**
 * This token is used to signal that the user is stepping over the last step of the wizard
 */
export const WIZARD_FINISHED = 'WIZARD_FINISHED';
export const PREVIEW_WIDGET_ID = 'PREVIEW_WIDGET';
export const PREVIEW_DASHBOARD_ID = 'PREVIEW_DASHBOARD';

export const QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER = '_filter';
export const QUERY_PARAM_TUNNEL_SEGMENTS = '_TUNNEL_SEGMENTS';
export const QUERY_PARAM_PRINTING_NODE_TYPE = '_PRINTING_SOURCE_NODE_TYPE';
export const QUERY_PARAM_PRINTING_SOURCE_PAGE = '_PRINTING_SOURCE_PAGE';
export const QUERY_PARAM_PRINTING_SOURCE_TYPE = '_PRINTING_SOURCE_TYPE';
export const QUERY_PARAM_PRINTING_RECORD_ID = '_PRINTING_RECORD_ID';
export const QUERY_PARAM_SELECTED_SECTION_ID = '_SELECTED_SECTION_ID';
export const SHOULD_REFRESH_DIALOG_RESULT = '_SHOULD_REFRESH';

export const TUNNEL_LINK_CLASS = 'e-tunnel-link';

export const BULK_ACTION_ASYNC_EXPORT = 'asyncExport';
export const ACTION_POPOVER_SUBMENUS_COMPAT_CSS_CLASS = 'e-action-popover-submenus-compat';
export const TABLE_INLINE_ACTION_CSS_CLASS = 'e-table-inline-action';
export const SINGLE_INLINE_ACTION_CSS_CLASS = 'e-popover-action-single-button';

export const PLUGIN_CACHE = 'xtrem-plugin-cache';
export const ARTIFACT_DATABASE_NAME = 'xtrem-ui';
export const ARTIFACT_CACHE_STORE = 'artifact-cache';
export const ARTIFACT_CACHE_DEFAULT_PASSPHRASE = 'NO_KEY';

export const QUERY_ALIAS_NAV_PANEL = 'navigationPanelItems';
export const QUERY_ALIAS_PAGE_DATA = 'rootNode';

export const ATTACHMENTS_PROPERTY_NAME = '_attachments';
export const ATTACHMENTS_ELEMENT_ID = '__attachmentsList';
export const ATTACHMENTS_PREVIEW_ELEMENT_ID = '__attachmentsPreview';
export const ATTACHMENT_SECTION_ID = '__attachmentSection';
export const DASHBOARD_SCREEN_ID = '$dashboard';
export const DEFAULT_VIEW_ID = '__default';

export const HEADER_IMAGE = '_headerImage';
export const HEADER_TITLE = '_headerTitle';
export const DASHBOARD_WIDGETS_MIN_LOADING_TIME = 500;

export const ELEMENT_ID_APPLICATION_CODE_LOOKUP = '$applicationCodeLookup';
