import type { Dict } from '@sage/xtrem-shared';

const parameters: Dict<string> = {};
window.location.search
    .substring(1)
    .split('&')
    .forEach(s => {
        const parts = s.split('=');
        if (parts[0]) {
            parameters[parts[0]] = parts[1];
        }
    });

export const getWindowParameters = (): Dict<string> => {
    return Object.seal(parameters);
};

export const getLocalStorage = (): Storage => window.localStorage;

export const isNoConsole = (): boolean => {
    try {
        return !!process?.env?.NO_CONSOLE;
    } catch {
        return false;
    }
};

export const isDevMode = (): boolean =>
    window.location?.hostname === 'localhost' ||
    window.location?.hostname === '127.0.0.1' ||
    (window as any).DEBUGGING_XTREM;

export const hasQueryLogging = (): boolean => isDevMode() && !(window as any).SKIP_QUERY_LOGGING && !isNoConsole();

export const hasNodeCacheLogging = (): boolean =>
    isDevMode() && !(window as any).SKIP_NODE_CACHE_LOGGING && !isNoConsole();

export const hasStateLogging = (): boolean => isDevMode() && !(window as any).SKIP_STATE_LOGGING && !isNoConsole();

export const hasAgGridLogging = (): boolean => isDevMode() && !(window as any).SKIP_AG_GRID_LOGGING && !isNoConsole();
