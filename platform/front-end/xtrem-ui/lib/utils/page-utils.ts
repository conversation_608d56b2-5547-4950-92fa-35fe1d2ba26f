import { set } from 'lodash';
import type { RuntimePageMetadata } from '../component/container/page/page-extension-decorator';
import type { PageDecoratorProperties, SectionDecoratorProperties } from '../component/decorator-properties';
import type { PageDefinition } from '../service/page-definition';
import { getIdFieldValue } from './id-field-utils';
import { resolveByRecordValue } from './resolve-value-utils';
import type { PageEventType } from '../component/container/page/page-types';
import { objectKeys } from '@sage/xtrem-shared';
import type { ScreenBaseDefinition } from '../service/screen-base-definition';

export interface HeaderTitles {
    subtitle: string | undefined;
    title: string;
}

export function getHeaderTitles({
    idFieldValue,
    isRecordCreationPage,
    objectTypePlural,
    objectTypeSingular,
    subtitle,
    title,
}: {
    idFieldValue?: string | null;
    isRecordCreationPage?: boolean;
    objectTypePlural?: string | null;
    objectTypeSingular?: string | null;
    subtitle?: string;
    title: string;
}): HeaderTitles {
    const isObjectTypeDefined = !!objectTypePlural && !!objectTypeSingular;

    if (isObjectTypeDefined) {
        if (isRecordCreationPage) {
            return { subtitle: undefined, title: objectTypeSingular };
        }

        return idFieldValue
            ? { subtitle: undefined, title: `${objectTypeSingular} ${idFieldValue}` }
            : { subtitle: undefined, title: `${objectTypePlural}` };
    }

    return { subtitle, title };
}

export const getPageTitlesFromPageDefinition = (pageDefinition: PageDefinition, locale: string): HeaderTitles => {
    const idFieldValue = getIdFieldValue(pageDefinition, locale);

    const screenId = pageDefinition.metadata.screenId;
    const pageDecoratorProps = pageDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<any>;
    return getHeaderTitles({
        title: resolveByRecordValue({
            propertyValue: pageDecoratorProps.title,
            screenId,
            rowValue: null,
            skipHexFormat: true,
        }),
        subtitle: resolveByRecordValue({
            propertyValue: pageDecoratorProps.subtitle,
            screenId,
            rowValue: null,
            skipHexFormat: true,
        }),
        idFieldValue,
        isRecordCreationPage: false,
        objectTypePlural: resolveByRecordValue({
            propertyValue: pageDecoratorProps.objectTypePlural,
            screenId,
            rowValue: null,
            skipHexFormat: true,
        }),
        objectTypeSingular: resolveByRecordValue({
            propertyValue: pageDecoratorProps.objectTypeSingular,
            screenId,
            rowValue: null,
            skipHexFormat: true,
        }),
    });
};

/**
 * Mutates PageMetadata to inject the provided onLoad function to be executed after the page fragment or page extension has been loaded
 */
export const injectOnloadAfterIntoPageMetadata = (
    pageMetadata: RuntimePageMetadata,
    onLoad: PageEventType<any>,
): void => {
    if (pageMetadata.uiComponentProperties?.[pageMetadata.screenId]?.onLoadAfter) {
        pageMetadata.uiComponentProperties[pageMetadata.screenId].onLoadAfter.push(onLoad);
        pageMetadata.defaultUiComponentProperties[pageMetadata.screenId].onLoadAfter.push(onLoad);
    } else {
        set(pageMetadata, ['uiComponentProperties', pageMetadata.screenId, 'onLoadAfter'], [onLoad]);
        set(pageMetadata, ['defaultUiComponentProperties', pageMetadata.screenId, 'onLoadAfter'], [onLoad]);
    }
};

export function getNonLazySectionsFromScreenDefinition(screenDefinition: ScreenBaseDefinition): string[] {
    return objectKeys(screenDefinition.metadata.sectionThunks).filter(
        sectionId =>
            !(screenDefinition.metadata.uiComponentProperties[sectionId] as SectionDecoratorProperties).isLazyLoaded,
    );
}
