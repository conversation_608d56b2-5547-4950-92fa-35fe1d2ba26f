import { line } from '../../component/chart-types';
import { ChartControlObject } from '../../component/control-objects';
import * as nestedFields from '../../component/nested-fields';
import type { GraphqlCollection } from '../../component/types';
import { FieldKey } from '../../component/types';
import { createFieldControlObject } from '../../__tests__/test-helpers/mock-store-helpers';
import { getValueTypes } from '../abstract-fields-utils';

describe('Abstract Field Utils', () => {
    it('"getValueTypes" should return a map with element ID as key', () => {
        const screenId = 'screenId';
        const elementId = 'fieldId';

        const fieldProps = {
            title: 'Test Chart Title',
            chart: line<any, any>({
                series: [
                    nestedFields.numeric({
                        bind: 'productA',
                        prefix: '€',
                        title: 'Product A',
                    }),
                    nestedFields.numeric({
                        bind: 'productB',
                        prefix: '€',
                        title: 'Product B',
                    }),
                ],
                xAxis: nestedFields.text<any, any>({ bind: 'date', title: 'Quantity' }),
            }),
        };

        const fieldValue: GraphqlCollection = {
            data: [
                {
                    _id: 'F0D04985-8F9B-BAAA-EED7-85F80D839FB6',
                    date: '2019-07-13',
                    productA: 4,
                    productB: 3,
                },
                {
                    _id: '2D1429EA-CF35-87D6-8478-BFD494AF80E7',
                    date: '2019-10-17',
                    productA: 4.2,
                    productB: 3.5,
                },
                {
                    _id: '1E9C324E-063E-1063-E784-7D03D26D4FDE',
                    date: '2019-02-14',
                    productA: 4.6,
                    productB: 3.3,
                },
            ],
            pageInfo: {},
        };

        const uiComponentProps = {
            [elementId]: fieldProps,
        };

        const controlObject = createFieldControlObject(
            FieldKey.Chart,
            screenId,
            ChartControlObject,
            elementId,
            fieldValue,
            fieldProps,
        );
        const controlObjects = {
            [elementId]: controlObject,
        };

        expect(getValueTypes(controlObjects, uiComponentProps)).toStrictEqual({
            fieldId: controlObject,
        });
    });

    it('"getValueTypes" should return a map with element bind as key', () => {
        const screenId = 'screenId';
        const elementId = 'fieldId';

        const fieldProps = {
            bind: 'myChart',
            title: 'Test Chart Title',
            chart: line<any, any>({
                series: [
                    nestedFields.numeric({
                        bind: 'productA',
                        prefix: '€',
                        title: 'Product A',
                    }),
                    nestedFields.numeric({
                        bind: 'productB',
                        prefix: '€',
                        title: 'Product B',
                    }),
                ],
                xAxis: nestedFields.text<any, any>({ bind: 'date', title: 'Quantity' }),
            }),
        };

        const fieldValue: GraphqlCollection = {
            data: [
                {
                    _id: 'F0D04985-8F9B-BAAA-EED7-85F80D839FB6',
                    date: '2019-07-13',
                    productA: 4,
                    productB: 3,
                },
                {
                    _id: '2D1429EA-CF35-87D6-8478-BFD494AF80E7',
                    date: '2019-10-17',
                    productA: 4.2,
                    productB: 3.5,
                },
                {
                    _id: '1E9C324E-063E-1063-E784-7D03D26D4FDE',
                    date: '2019-02-14',
                    productA: 4.6,
                    productB: 3.3,
                },
            ],
            pageInfo: {},
        };

        const uiComponentProps = {
            [elementId]: fieldProps,
        };

        const controlObject = createFieldControlObject(
            FieldKey.Chart,
            screenId,
            ChartControlObject,
            elementId,
            fieldValue,
            fieldProps,
        );
        const controlObjects = {
            [elementId]: controlObject,
        };

        expect(getValueTypes(controlObjects, uiComponentProps)).toStrictEqual({
            myChart: controlObject,
        });
    });
});
