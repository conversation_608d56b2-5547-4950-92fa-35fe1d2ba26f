import { nestedFields } from '../..';
import type { TableProperties } from '../../component/control-objects';
import { getNestedFieldsFromProperties } from '../nested-field-utils';

describe('nested field utils', () => {
    describe('getNestedFieldsFromProperties', () => {
        it('should extract simple table columns', () => {
            const tableProperties: TableProperties = {
                columns: [
                    nestedFields.text({ bind: 'someField' }),
                    nestedFields.numeric({ bind: { some: { otherField: true } } }),
                ],
            };
            const result = getNestedFieldsFromProperties(tableProperties);
            expect(result).toHaveLength(2);
            expect(result[0].properties.bind).toEqual('someField');
            expect(result[1].properties.bind).toEqual({ some: { otherField: true } });
        });

        it('should extract simple table columns and also the mobile card properties', () => {
            const tableProperties: TableProperties = {
                columns: [
                    nestedFields.text({ bind: 'someField' }),
                    nestedFields.numeric({ bind: { some: { otherField: true } } }),
                ],
                mobileCard: {
                    title: nestedFields.text({ bind: 'titleFieldUsedOnMobileCard' }),
                },
            };
            const result = getNestedFieldsFromProperties(tableProperties);
            expect(result).toHaveLength(3);
            expect(result[0].properties.bind).toEqual('someField');
            expect(result[1].properties.bind).toEqual({ some: { otherField: true } });
            expect(result[2].properties.bind).toEqual('titleFieldUsedOnMobileCard');
        });
    });
});
