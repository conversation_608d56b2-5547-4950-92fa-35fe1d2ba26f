import { FieldKey } from '../../component/types';
import type { ResponsiveTypes } from '../../redux/state';
import * as utils from '../responsive-utils';

const getResponsiveTypes = (viewport: 'xs' | 's' | 'm' | 'l' | 'infinity') => {
    return ['xs', 's', 'm', 'l', 'infinity'].reduce((prevVal: ResponsiveTypes, currentVal: string) => {
        prevVal[currentVal] = currentVal === viewport;
        return prevVal;
    }, {} as ResponsiveTypes);
};

describe('Responsive Utils', () => {
    describe('field width', () => {
        it('should determine the field width based on the screen and field T-Shirt size', () => {
            let result: number;
            result = utils.calculateFieldWidth(getResponsiveTypes('l'), FieldKey.Text, 12, false, 'large');
            expect(result).toBe(4);
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.Text, 12, false, 'large');
            expect(result).toBe(4);
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.Text, 12, false, 'half');
            expect(result).toBe(4);
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.Text, 12, false, 'small-medium');
            expect(result).toBe(2);
            result = utils.calculateFieldWidth(getResponsiveTypes('xs'), FieldKey.Text, 12, false, 'large');
            expect(result).toBe(4);
            result = utils.calculateFieldWidth(getResponsiveTypes('l'), FieldKey.Text, 12, false, 'medium');
            expect(result).toBe(3);
            result = utils.calculateFieldWidth(getResponsiveTypes('l'), FieldKey.Text, 12, false, 'small-medium');
            expect(result).toBe(3);
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.Text, 12, false, 'medium');
            expect(result).toBe(4);
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.Text, 12, false, 'half');
            expect(result).toBe(4);
            result = utils.calculateFieldWidth(getResponsiveTypes('xs'), FieldKey.Text, 12, false, 'medium');
            expect(result).toBe(4);
            result = utils.calculateFieldWidth(getResponsiveTypes('l'), FieldKey.Text, 12, false, 'small');
            expect(result).toBe(2);
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.Text, 12, false, 'small');
            expect(result).toBe(2);
            result = utils.calculateFieldWidth(getResponsiveTypes('xs'), FieldKey.Text, 12, false, 'small');
            expect(result).toBe(2);
            result = utils.calculateFieldWidth(getResponsiveTypes('xs'), FieldKey.Text, 12, false, 'small-medium');
            expect(result).toBe(2);
        });

        it('should return the number of available columns if the field is set to full width', () => {
            const result = utils.calculateFieldWidth(getResponsiveTypes('l'), FieldKey.Text, 12, true, 'small');
            expect(result).toBe(12);
        });

        it('should return the small field size if the it is not set by the developer', () => {
            const result = utils.calculateFieldWidth(getResponsiveTypes('l'), FieldKey.Text, 12);
            expect(result).toBe(2);
        });

        it('should cap calculated field size at the width of container', () => {
            const result = utils.calculateFieldWidth(getResponsiveTypes('l'), FieldKey.Text, 4, false, 'large');
            expect(result).toBe(4);
        });

        it('should return the container width if the field is a block type', () => {
            let result: number;
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.Calendar, 8);
            expect(result).toBe(8);
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.Table, 8);
            expect(result).toBe(8);
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.DetailList, 8);
            expect(result).toBe(8);
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.Image, 8);
            expect(result).toBe(8);
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.RichText, 8);
            expect(result).toBe(8);
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.MultiFileDeposit, 8);
            expect(result).toBe(8);
            result = utils.calculateFieldWidth(getResponsiveTypes('m'), FieldKey.FormDesigner, 8);
            expect(result).toBe(8);
        });
    });

    describe('container width', () => {
        it('should calculate the width of a container based the screen and T-Shirt size', () => {
            let result: number;
            result = utils.calculateContainerWidth(getResponsiveTypes('infinity'), 12, 'large');
            expect(result).toBe(8);
            result = utils.calculateContainerWidth(getResponsiveTypes('l'), 12, 'large');
            expect(result).toBe(8);
            result = utils.calculateContainerWidth(getResponsiveTypes('l'), 12, 'medium');
            expect(result).toBe(6);
            result = utils.calculateContainerWidth(getResponsiveTypes('l'), 12, 'half');
            expect(result).toBe(6);
            result = utils.calculateContainerWidth(getResponsiveTypes('l'), 12, 'small');
            expect(result).toBe(4);
            result = utils.calculateContainerWidth(getResponsiveTypes('m'), 12, 'large');
            expect(result).toBe(8);
            result = utils.calculateContainerWidth(getResponsiveTypes('m'), 12, 'medium');
            expect(result).toBe(6);
            result = utils.calculateContainerWidth(getResponsiveTypes('m'), 12, 'half');
            expect(result).toBe(4);
            result = utils.calculateContainerWidth(getResponsiveTypes('m'), 12, 'small');
            expect(result).toBe(4);
            result = utils.calculateContainerWidth(getResponsiveTypes('s'), 12, 'half');
            expect(result).toBe(4);
            result = utils.calculateContainerWidth(getResponsiveTypes('xs'), 12, 'large');
            expect(result).toBe(4);
            result = utils.calculateContainerWidth(getResponsiveTypes('xs'), 12, 'medium');
            expect(result).toBe(4);
            result = utils.calculateContainerWidth(getResponsiveTypes('xs'), 12, 'small');
            expect(result).toBe(4);
        });

        it('should cap the number of columns at the available column number', () => {
            const result = utils.calculateContainerWidth(getResponsiveTypes('l'), 4, 'large');
            expect(result).toBe(4);
        });

        it('should set the container size to extra large if it is not defined by the developer', () => {
            const result = utils.calculateContainerWidth(getResponsiveTypes('l'), 12);
            expect(result).toBe(12);
        });
    });
});
