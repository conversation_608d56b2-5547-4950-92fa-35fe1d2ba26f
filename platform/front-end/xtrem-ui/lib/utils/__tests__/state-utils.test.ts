import { getMockPageDefinition, getMockPageMetadata, getMockState } from '../../__tests__/test-helpers';
import { hasAnyDirtyScreenDefinitions } from '../state-utils';

describe('state utils', () => {
    describe('hasAnyDirtyScreenDefinitions', () => {
        const testDashboardGroup = 'home';

        it('should return true if has a a dirty screen definition in the state', () => {
            const state = getMockState();
            const screenId1 = 'ScreenIdFirst';
            state.screenDefinitions[screenId1] = getMockPageDefinition(screenId1);
            const screenId2 = 'ScreenIdSecond';
            state.screenDefinitions[screenId2] = getMockPageDefinition(screenId2, { dirtyStates: { testField: true } });

            expect(hasAnyDirtyScreenDefinitions(state)).toEqual(true);
        });

        it('should return false if all screens and the dashboard editor are clear', () => {
            const state = getMockState();
            state.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.isDirty = false;
            state.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.isDirty = false;
            const screenId1 = 'ScreenIdFirst';
            state.screenDefinitions[screenId1] = getMockPageDefinition(screenId1);
            const screenId2 = 'ScreenIdSecond';
            state.screenDefinitions[screenId2] = getMockPageDefinition(screenId2);

            expect(hasAnyDirtyScreenDefinitions(state)).toEqual(false);
        });

        it('should exclude "evergreen" pages from the dirty state calculation', () => {
            const state = getMockState();
            const screenId1 = 'ScreenIdFirst';
            state.screenDefinitions[screenId1] = getMockPageDefinition(screenId1);
            const screenId2 = 'ScreenIdSecond';
            state.screenDefinitions[screenId2] = getMockPageDefinition(screenId2, {
                dirtyStates: { testField: true },
                metadata: getMockPageMetadata(screenId2, {
                    uiComponentProperties: {
                        [screenId2]: {
                            skipDirtyCheck: true,
                        } as any,
                    },
                }),
            });

            expect(hasAnyDirtyScreenDefinitions(state)).toEqual(false);
        });

        it('should include the dashboard editor state into the dirty checks', () => {
            const state = getMockState();
            state.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.isDirty = true;
            state.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.isDirty = false;
            const screenId1 = 'ScreenIdFirst';
            state.screenDefinitions[screenId1] = getMockPageDefinition(screenId1);
            const screenId2 = 'ScreenIdSecond';
            state.screenDefinitions[screenId2] = getMockPageDefinition(screenId2);

            expect(hasAnyDirtyScreenDefinitions(state)).toEqual(true);
        });

        it('should include the widget editor state into the dirty checks', () => {
            const state = getMockState();
            state.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.isDirty = false;
            state.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.isDirty = true;
            const screenId1 = 'ScreenIdFirst';
            state.screenDefinitions[screenId1] = getMockPageDefinition(screenId1);
            const screenId2 = 'ScreenIdSecond';
            state.screenDefinitions[screenId2] = getMockPageDefinition(screenId2);

            expect(hasAnyDirtyScreenDefinitions(state)).toEqual(true);
        });
    });
});
