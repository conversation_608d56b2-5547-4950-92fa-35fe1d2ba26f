import type { EditableFieldProperties } from '../../component/editable-field-control-object';
import type { HasGenericErrorHandler } from '../../component/field/traits';
import * as nestedFields from '../../component/nested-fields';
import { <PERSON>Key } from '../../component/types';
import * as xtremRedux from '../../redux';
import type { XtremAppState } from '../../redux';
import * as dialogs from '../../service/dialog-service';
import * as toasts from '../../service/toast-service';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../__tests__/test-helpers';
import * as events from '../events';
import type { NestedFieldsProperties } from '../../component/nested-fields';
import * as stateUtils from '../state-utils';

const errorDialogMock = jest.fn();

describe('event handlers', () => {
    const screenId = 'TestPage';
    let pageOnError: jest.Mock<any, any>;
    let pageOnLoad: jest.Mock<any, any>;

    const field1id = 'field1id';
    let field1onClick: jest.Mock<any, any>;
    let field1onChange: jest.Mock<any, any>;
    let field1onError: jest.Mock<any, any>;

    const field2id = 'field2id';
    let field2onClick: jest.Mock<any, any>;

    const field3id = 'field3id';
    const field3idColumn1 = 'field3idColumn1';
    let field3onClick: jest.Mock<any, any>;
    let field3onError: jest.Mock<any, any>;
    let field3column1OnClick: jest.Mock<any, any>;
    let field3column1ColumnDef: NestedFieldsProperties<FieldKey.Text>;
    let field3column1OnError: jest.Mock<any, any>;
    let field3column2OnClick: jest.Mock<any, any>;
    let field3column2ColumnDef: NestedFieldsProperties<FieldKey.Text>;
    const field3idColumn2 = 'field3idColumn2';

    let state: XtremAppState;

    beforeEach(() => {
        pageOnError = jest.fn();
        pageOnLoad = jest.fn();

        state = getMockState();

        state.screenDefinitions[screenId] = getMockPageDefinition(
            screenId,
            {},
            {
                uiComponentProperties: {
                    [screenId]: {
                        onError: pageOnError,
                        onLoad: pageOnLoad,
                    } as any,
                },
            },
        );

        field1onClick = jest.fn();
        field1onError = jest.fn();
        field1onChange = jest.fn();

        addFieldToState(FieldKey.Text, state, screenId, field1id, {
            onClick: field1onClick,
            onChange: field1onChange,
            onError: field1onError,
        });

        field2onClick = jest.fn();

        addFieldToState(FieldKey.Numeric, state, screenId, field2id, {
            onClick: field2onClick,
        });

        field3onClick = jest.fn();
        field3column1OnClick = jest.fn();
        field3column1OnError = jest.fn();

        field3column1ColumnDef = {
            bind: field3idColumn1,
            onClick: field3column1OnClick,
            onError: field3column1OnError,
        };
        field3column2OnClick = jest.fn();
        field3column2ColumnDef = {
            bind: field3idColumn2,
            onClick: field3column2OnClick,
        };

        field3onError = jest.fn();

        addFieldToState(FieldKey.Table, state, screenId, field3id, {
            onClick: field3onClick,
            onError: field3onError,
            columns: [nestedFields.text(field3column1ColumnDef), nestedFields.text(field3column2ColumnDef)],
        });

        const store = getMockStore(state, false);

        jest.spyOn(xtremRedux, 'getStore').mockReturnValue(store);
        jest.spyOn(dialogs, 'errorDialog').mockImplementation(errorDialogMock);
        jest.spyOn(toasts, 'showToast').mockImplementation(jest.fn());
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    /* eslint-disable @typescript-eslint/no-throw-literal */
    describe('triggerEventHandler', () => {
        it('should call the corresponding event handler of a page', async () => {
            expect(pageOnLoad).not.toHaveBeenCalled();
            await events.triggerScreenEvent(screenId, 'onLoad');
            expect(pageOnLoad).toHaveBeenCalled();
        });

        it('should call the corresponding event handler of a field', async () => {
            expect(field3column1OnClick).not.toHaveBeenCalled();
            await events.triggerNestedFieldEvent(screenId, field1id, field3column1ColumnDef, 'onClick', 'arg1', {});
            expect(field3column1OnClick).toHaveBeenCalled();
            expect(field3column1OnClick).toHaveBeenCalledWith('arg1', {});
        });

        it('should call the onError function if an error occurs on field', async () => {
            field1onClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            expect(field1onError).not.toHaveBeenCalled();
            await events.triggerFieldEvent(screenId, field1id, 'onClick', 'arg1', {});
            expect(field1onError).toHaveBeenCalled();
            expect(field1onError).toHaveBeenCalledWith('OMG an error', screenId, field1id);
        });

        it('should call the onError function and display a toast if it returns a string', async () => {
            field1onClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            field1onError.mockReturnValue('An error occurred');
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
            expect(toasts.showToast).not.toHaveBeenCalled();
            expect(field1onError).not.toHaveBeenCalled();
            await events.triggerFieldEvent(screenId, field1id, 'onClick', 'arg1', {});
            expect(toasts.showToast).toHaveBeenCalledWith('An error occurred', {
                type: 'error',
                timeout: 10000,
                language: 'markdown',
            });
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
        });

        it('should call the onError function and display a toast if it returns a string promise', async () => {
            field1onClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            field1onError.mockReturnValue(Promise.resolve('An error occurred'));
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
            expect(toasts.showToast).not.toHaveBeenCalled();
            expect(field1onError).not.toHaveBeenCalled();
            await events.triggerFieldEvent(screenId, field1id, 'onClick', 'arg1', {});
            expect(toasts.showToast).toHaveBeenCalledWith('An error occurred', {
                type: 'error',
                timeout: 10000,
                language: 'markdown',
            });
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
        });

        it('should swallow the error if the error handler returns a falsy value', async () => {
            field1onClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            field1onError.mockReturnValue(undefined);
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
            expect(toasts.showToast).not.toHaveBeenCalled();
            expect(field1onError).not.toHaveBeenCalled();
            await events.triggerFieldEvent(screenId, field1id, 'onClick');
            expect(toasts.showToast).not.toHaveBeenCalled();
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
        });

        it('should display an error message when an unhandled error occurs', async () => {
            field2onClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            (
                state.screenDefinitions[screenId].metadata.uiComponentProperties[
                    screenId
                ] as HasGenericErrorHandler<any>
            ).onError = undefined;

            expect(dialogs.errorDialog).not.toHaveBeenCalled();
            expect(toasts.showToast).not.toHaveBeenCalled();
            await events.triggerFieldEvent(screenId, field2id, 'onClick', 'arg1', {});
            expect(toasts.showToast).not.toHaveBeenCalled();
            expect(dialogs.errorDialog).toHaveBeenCalledWith('TestPage', 'Error', 'OMG an error');
        });

        it('should call the onError function of the page if an error occurs on field that has no error handler', async () => {
            field2onClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            expect(pageOnError).not.toHaveBeenCalled();
            await events.triggerFieldEvent(screenId, field2id, 'onClick', 'arg1', {});
            expect(pageOnError).toHaveBeenCalled();
            expect(pageOnError).toHaveBeenCalledWith('OMG an error', screenId, field2id);
        });

        it('should not do anything if an invalid screen id is used', async () => {
            expect(field3column2OnClick).not.toHaveBeenCalled();
            await expect(
                events.triggerNestedFieldEvent(
                    'InvalidScreenId',
                    field3id,
                    field3column2ColumnDef,
                    'onClick',
                    'arg1',
                    {},
                ),
            ).rejects.toThrow("events: InvalidScreenId screen definition doesn't exist in current state");
            expect(toasts.showToast).not.toHaveBeenCalled();
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
        });
    });

    describe('triggerEventListener', () => {
        it('should call the corresponding event handler of a page', async () => {
            expect(pageOnLoad).not.toHaveBeenCalled();
            await events.triggerScreenEvent(screenId, 'onLoad');
            expect(pageOnLoad).toHaveBeenCalled();
        });

        it('should call the onError function if an error occurs on field', async () => {
            field1onClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            expect(field1onError).not.toHaveBeenCalled();
            await events.triggerFieldEvent(screenId, field1id, 'onClick', 'arg1', {});
            expect(field1onError).toHaveBeenCalled();
            expect(field1onError).toHaveBeenCalledWith('OMG an error', screenId, field1id);
        });

        it('should call the onError function and display a toast if it returns a string', async () => {
            field1onClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            field1onError.mockReturnValue('An error occurred');
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
            expect(toasts.showToast).not.toHaveBeenCalled();
            expect(field1onError).not.toHaveBeenCalled();
            await events.triggerFieldEvent(screenId, field1id, 'onClick', 'arg1', {});
            expect(toasts.showToast).toHaveBeenCalledWith('An error occurred', {
                type: 'error',
                timeout: 10000,
                language: 'markdown',
            });
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
        });

        it('should call the onError function and display a toast if it returns a string promise', async () => {
            field1onClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            field1onError.mockReturnValue(Promise.resolve('An error occurred'));
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
            expect(toasts.showToast).not.toHaveBeenCalled();
            expect(field1onError).not.toHaveBeenCalled();
            await events.triggerFieldEvent(screenId, field1id, 'onClick', 'arg1', {});
            expect(toasts.showToast).toHaveBeenCalledWith('An error occurred', {
                type: 'error',
                timeout: 10000,
                language: 'markdown',
            });
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
        });

        it('should swallow the error if the error handler returns a falsy value', async () => {
            field1onClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            field1onError.mockReturnValue(undefined);
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
            expect(toasts.showToast).not.toHaveBeenCalled();
            expect(field1onError).not.toHaveBeenCalled();
            await events.triggerFieldEvent(screenId, field1id, 'onClick', 'arg1', {});
            expect(toasts.showToast).not.toHaveBeenCalled();
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
        });

        it('should not do anything if an invalid screen id is used', async () => {
            await expect(events.triggerFieldEvent('InvalidScreenId', field1id, 'onClick', 'arg1', {})).rejects.toThrow(
                "events: InvalidScreenId screen definition doesn't exist in current state",
            );
            expect(toasts.showToast).not.toHaveBeenCalled();
            expect(dialogs.errorDialog).not.toHaveBeenCalled();
        });

        it('should display an error message when an unhandled error occurs', async () => {
            field2onClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            (
                state.screenDefinitions[screenId].metadata.uiComponentProperties[
                    screenId
                ] as HasGenericErrorHandler<any>
            ).onError = undefined;

            expect(dialogs.errorDialog).not.toHaveBeenCalled();
            expect(toasts.showToast).not.toHaveBeenCalled();
            await events.triggerFieldEvent(screenId, field2id, 'onClick', 'arg1', {});
            expect(toasts.showToast).not.toHaveBeenCalled();
            expect(dialogs.errorDialog).toHaveBeenCalledWith('TestPage', 'Error', 'OMG an error');
        });

        it('should call the onError function of the page if an error occurs on field that has no error handler', async () => {
            field2onClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            expect(pageOnError).not.toHaveBeenCalled();
            await events.triggerFieldEvent(screenId, field2id, 'onClick', 'arg1', {});
            expect(pageOnError).toHaveBeenCalled();
            expect(pageOnError).toHaveBeenCalledWith('OMG an error', screenId, field2id);
        });

        it('should not call the corresponding event handler of a field if it is disabled', async () => {
            expect(field1onClick).not.toHaveBeenCalled();
            (
                state.screenDefinitions[screenId].metadata.uiComponentProperties[
                    field1id
                ] as EditableFieldProperties<any>
            ).isDisabled = true;

            await events.triggerFieldEvent(screenId, field1id, 'onClick', 'arg1', {});
            expect(field1onClick).not.toHaveBeenCalled();
        });

        it('should not call the corresponding event handler of a field if it is disabled by a callback', async () => {
            jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() =>
                getMockPageDefinition('TestPage'),
            );
            const isDisabledMock = jest.fn(() => true);
            expect(field1onClick).not.toHaveBeenCalled();

            (
                state.screenDefinitions[screenId].metadata.uiComponentProperties[
                    field1id
                ] as EditableFieldProperties<any>
            ).isDisabled = isDisabledMock;

            await events.triggerFieldEvent(screenId, field1id, 'onClick', 'arg1', {});
            expect(field1onClick).not.toHaveBeenCalled();
            expect(isDisabledMock).toHaveBeenCalled();
            expect(isDisabledMock).toHaveBeenCalledWith('Test value', null);
        });

        it('should call the corresponding event handler of a field if it is enabled by a callback', async () => {
            jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() =>
                getMockPageDefinition('TestPage'),
            );
            const isDisabledMock = jest.fn(() => false);
            expect(field1onClick).not.toHaveBeenCalled();
            (
                state.screenDefinitions[screenId].metadata.uiComponentProperties[
                    field1id
                ] as EditableFieldProperties<any>
            ).isDisabled = isDisabledMock;

            await events.triggerFieldEvent(screenId, field1id, 'onClick', 'arg1', {});
            expect(field1onClick).toHaveBeenCalled();
            expect(isDisabledMock).toHaveBeenCalledWith('Test value', null);
        });
    });

    describe('nested use', () => {
        it('should call the corresponding event handler of a table column', async () => {
            expect(field3column1OnClick).not.toHaveBeenCalled();
            await events.triggerNestedFieldEvent(screenId, field3id, field3column1ColumnDef, 'onClick', 'arg1', {});
            expect(field3column1OnClick).toHaveBeenCalled();
            expect(field3column1OnClick).toHaveBeenCalledWith('arg1', {});
        });

        it('should handle call the error handler in case the event throws', async () => {
            field3column1OnClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            expect(field3column1OnError).not.toHaveBeenCalled();
            await events.triggerNestedFieldEvent(screenId, field3id, field3column1ColumnDef, 'onClick', 'arg1', {});
            expect(field3column1OnError).toHaveBeenCalled();
            expect(field3column1OnError).toHaveBeenCalledWith('OMG an error', 'TestPage', 'field3id');
        });

        it("should handle call the error table field's handler in case the event throws and no error handler found", async () => {
            field3column2OnClick.mockImplementation(() => {
                throw 'OMG an error';
            });

            expect(field3column2OnClick).not.toHaveBeenCalled();
            await events.triggerNestedFieldEvent(screenId, field3id, field3column2ColumnDef, 'onClick', 'arg1', {});
            expect(field3column2OnClick).toHaveBeenCalled();
            expect(field3onError).toHaveBeenCalledWith('OMG an error', 'TestPage', 'field3id');
        });
    });

    describe('isBulkChange', () => {
        it('should return false if only one character changes', () => {
            expect(events.isBulkChange(undefined as any, 'a')).toBe(false);
            expect(events.isBulkChange(null as any, 'a')).toBe(false);
            expect(events.isBulkChange('a', '')).toBe(false);
            expect(events.isBulkChange(4 as any, '')).toBe(false);
            expect(events.isBulkChange(4 as any, 31 as any)).toBe(false);
            expect(events.isBulkChange('ab', 'abc')).toBe(false);
            expect(events.isBulkChange('a', null as any)).toBe(false);
            expect(events.isBulkChange('0', '12')).toBe(false);
            expect(events.isBulkChange(0 as any, '12')).toBe(false);
            expect(events.isBulkChange(1234 as any, '123')).toBe(false);
        });

        it('should return true if more than one character changes', () => {
            expect(events.isBulkChange('', 12 as any)).toBe(true);
            expect(events.isBulkChange('aasd', null as any)).toBe(true);
            expect(events.isBulkChange('ab', 'abcd')).toBe(true);
            expect(events.isBulkChange(12332 as any, null as any)).toBe(true);
            expect(events.isBulkChange(12332 as any, '123')).toBe(true);
            expect(events.isBulkChange(12332 as any, '')).toBe(true);
            expect(events.isBulkChange('', 12332 as any)).toBe(true);
        });
    });
});
