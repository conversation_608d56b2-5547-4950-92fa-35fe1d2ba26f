import type { PageDecoratorProperties } from '../../component/container/page/page-decorator';
import type { PageHeaderFields } from '../../component/container/page/page-types';
import { FieldKey } from '../../component/types';
import type { XtremAppState } from '../../redux';
import type { PageDefinition } from '../../service/page-definition';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../__tests__/test-helpers';
import { getIdFieldValue, getFieldDisplayValue } from '../id-field-utils';

describe('id field utils', () => {
    const screenId = 'MyTestPage';
    const labelElementId = 'myTestLabelField';
    const numericElementId = 'myTestNumericField';
    const numericWithZeroElementId = 'myTestZeroNumericField';
    const textElementId = 'myTestTextField';
    const referenceElementId = 'myTestReferenceField';
    const referenceNestedElementId = 'myTestReferenceNestedField';
    const selectOptionTypeElementId = 'myTestSelectOptionTypeField';
    const dropdownOptionTypeElementId = 'myTestDropdownOptionTypeField';
    const selectElementId = 'myTestSelectField';
    const dropdownElementId = 'myTestDropdownField';
    const dateElementId = 'myTestDateField';

    let mockState: XtremAppState;
    let pageDefinition: PageDefinition;

    beforeEach(() => {
        mockState = getMockState();
        mockState.translations = {
            'en-US': { '@sage/xtrem-test/enums__my_test_enum__good': 'Localized Good' },
        };
        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        addFieldToState(FieldKey.Label, mockState, screenId, labelElementId, {}, 'Test Value');
        addFieldToState(FieldKey.Numeric, mockState, screenId, numericElementId, { scale: 3 }, 1234.6789);
        addFieldToState(FieldKey.Numeric, mockState, screenId, numericWithZeroElementId, { scale: 3 }, 0);
        addFieldToState(FieldKey.Text, mockState, screenId, textElementId, {}, 'Test Value');
        addFieldToState<FieldKey.Reference, any>(
            FieldKey.Reference,
            mockState,
            screenId,
            referenceElementId,
            { valueField: 'name' as any, node: '@sage/xtrem-test/MyTestNode' },
            { name: 'John Doe' },
        );
        addFieldToState<FieldKey.Reference, any>(
            FieldKey.Reference,
            mockState,
            screenId,
            referenceNestedElementId,
            { valueField: { user: { name: true } } as any, node: '@sage/xtrem-test/MyTestNode' },
            { user: { name: 'Joanna Doe' } },
        );
        addFieldToState(
            FieldKey.Select,
            mockState,
            screenId,
            selectOptionTypeElementId,
            { optionType: '@sage/xtrem-test/MyTestEnum' },
            'good',
        );
        addFieldToState(FieldKey.Select, mockState, screenId, selectElementId, {}, 'good');
        addFieldToState(
            FieldKey.DropdownList,
            mockState,
            screenId,
            dropdownOptionTypeElementId,
            { optionType: '@sage/xtrem-test/MyTestEnum' },
            'good',
        );
        addFieldToState(FieldKey.DropdownList, mockState, screenId, dropdownElementId, {}, 'good');
        addFieldToState(FieldKey.Date, mockState, screenId, dateElementId, {}, '2022-07-18');
        getMockStore(mockState);
        pageDefinition = mockState.screenDefinitions[screenId] as PageDefinition;
    });

    describe('getFieldDisplayValue', () => {
        it('should format numeric field value', () => {
            expect(
                getFieldDisplayValue(
                    pageDefinition,
                    pageDefinition.metadata.controlObjects[numericElementId] as PageHeaderFields,
                    'en-US',
                ),
            ).toEqual('1,234.679');
        });
        it('should format numeric field value with zero', () => {
            expect(
                getFieldDisplayValue(
                    pageDefinition,
                    pageDefinition.metadata.controlObjects[numericWithZeroElementId] as PageHeaderFields,
                    'en-US',
                ),
            ).toEqual('0.000');
        });

        it('should format text field value', () => {
            expect(
                getFieldDisplayValue(
                    pageDefinition,
                    pageDefinition.metadata.controlObjects[textElementId] as PageHeaderFields,
                    'en-US',
                ),
            ).toEqual('Test Value');
        });

        it('should format label field value', () => {
            expect(
                getFieldDisplayValue(
                    pageDefinition,
                    pageDefinition.metadata.controlObjects[labelElementId] as PageHeaderFields,
                    'en-US',
                ),
            ).toEqual('Test Value');
        });

        it('should format reference field value', () => {
            expect(
                getFieldDisplayValue(
                    pageDefinition,
                    pageDefinition.metadata.controlObjects[referenceElementId] as PageHeaderFields,
                    'en-US',
                ),
            ).toEqual('John Doe');
        });

        it('should format reference field value with nested value', () => {
            expect(
                getFieldDisplayValue(
                    pageDefinition,
                    pageDefinition.metadata.controlObjects[referenceNestedElementId] as PageHeaderFields,
                    'en-US',
                ),
            ).toEqual('Joanna Doe');
        });

        it('should format select field value', () => {
            expect(
                getFieldDisplayValue(
                    pageDefinition,
                    pageDefinition.metadata.controlObjects[selectElementId] as PageHeaderFields,
                    'en-US',
                ),
            ).toEqual('good');
        });

        it('should format select field value with option type', () => {
            expect(
                getFieldDisplayValue(
                    pageDefinition,
                    pageDefinition.metadata.controlObjects[selectOptionTypeElementId] as PageHeaderFields,
                    'en-US',
                ),
            ).toEqual('Localized Good');
        });

        it('should format dropdown field value', () => {
            expect(
                getFieldDisplayValue(
                    pageDefinition,
                    pageDefinition.metadata.controlObjects[dropdownElementId] as PageHeaderFields,
                    'en-US',
                ),
            ).toEqual('good');
        });

        it('should format dropdown field value with option type', () => {
            expect(
                getFieldDisplayValue(
                    pageDefinition,
                    pageDefinition.metadata.controlObjects[dropdownOptionTypeElementId] as PageHeaderFields,
                    'en-US',
                ),
            ).toEqual('Localized Good');
        });

        it('should format date field value', () => {
            expect(
                getFieldDisplayValue(
                    pageDefinition,
                    pageDefinition.metadata.controlObjects[dateElementId] as PageHeaderFields,
                    'en-US',
                ),
            ).toEqual('18/07/2022');
        });
    });

    describe('getIdFieldValue', () => {
        let pageProperties: PageDecoratorProperties<any>;

        beforeEach(() => {
            pageProperties = pageDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<any>;
        });

        it('should handle when idField is not defined', () => {
            pageProperties.idField = undefined;

            expect(getIdFieldValue(pageDefinition, 'en-US')).toEqual(null);
        });

        it('should handle when idField returns null', () => {
            pageProperties.idField = function TestFunction() {
                return null;
            };

            expect(getIdFieldValue(pageDefinition, 'en-US')).toEqual(null);
        });

        it('should handle when idField returns a string', () => {
            pageProperties.idField = function TestFunction() {
                return 'Some string value';
            };

            expect(getIdFieldValue(pageDefinition, 'en-US')).toEqual('Some string value');
        });

        it('should handle when idField returns a field control object', () => {
            pageProperties.idField = function TestFunction() {
                return pageDefinition.metadata.controlObjects[dropdownOptionTypeElementId] as PageHeaderFields;
            };

            expect(getIdFieldValue(pageDefinition, 'en-US')).toEqual('Localized Good');
        });

        it('should handle when idField returns an array of field control object with a single item', () => {
            pageProperties.idField = function TestFunction() {
                return [pageDefinition.metadata.controlObjects[dropdownOptionTypeElementId] as PageHeaderFields];
            };

            expect(getIdFieldValue(pageDefinition, 'en-US')).toEqual('Localized Good');
        });

        it('should handle when idField returns an array of field control object with a multiple item', () => {
            pageProperties.idField = function TestFunction() {
                return [
                    pageDefinition.metadata.controlObjects[dropdownOptionTypeElementId] as PageHeaderFields,
                    pageDefinition.metadata.controlObjects[referenceNestedElementId] as PageHeaderFields,
                ];
            };

            expect(getIdFieldValue(pageDefinition, 'en-US')).toEqual('Localized Good Joanna Doe');
        });
    });
});
