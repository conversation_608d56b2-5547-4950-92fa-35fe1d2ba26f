import type { PageDefinition } from '../../service/page-definition';
import { applyDuplicationBusinessActions, applyDuplicationLayoutStructure } from '../duplication-utils';

describe('duplication-utils', () => {
    describe('applyDuplicationBusinessActions', () => {
        it("should replace the page's business actions with the duplicate actions", () => {
            const spy = jest.fn();
            const pageDefinition = {
                metadata: {
                    businessActionsThunk: spy,
                    controlObjects: {
                        $standardCancelAction: { title: 'Cancel' },
                        $standardExecuteDuplicationAction: { title: 'Duplicate' },
                    },
                    businessActionsExtensionsThunk: [],
                },
            } as unknown as PageDefinition;
            applyDuplicationBusinessActions(pageDefinition);
            expect(spy).not.toHaveBeenCalled();
            expect(pageDefinition.metadata.businessActionsThunk!({} as any)).toEqual([
                { title: 'Cancel' },
                { title: 'Duplicate' },
            ]);
        });
    });
    describe('applyDuplicationLayoutStructure', () => {
        it("should replace the page's layout with a single section that contains only the fields which are required for duplication", () => {
            const pageDefinition = {
                metadata: {
                    duplicateBindings: ['bind1', 'bind3'],
                    uiComponentProperties: {
                        bind1: { bind: 'bind1', label: 'label1' },
                        bind2: { bind: 'bind2', label: 'label2' },
                        bind3: { bind: 'bind3', label: 'label3' },
                    },
                    layout: {},
                },
            } as unknown as PageDefinition;
            applyDuplicationLayoutStructure(pageDefinition);
            expect(pageDefinition.metadata.layout).toEqual({
                $items: [
                    {
                        $containerId: '$duplicateSection',
                        $isHiddenMobile: false,
                        $isHiddenDesktop: false,
                        $category: 'section',
                        $layout: {
                            $items: [
                                {
                                    $containerId: '$duplicateBlock',
                                    $isHiddenMobile: false,
                                    $isHiddenDesktop: false,
                                    $category: 'block',
                                    $layout: {
                                        $items: [
                                            { $bind: 'bind1', $containerType: 'block', $isFullWidth: false },
                                            { $bind: 'bind3', $containerType: 'block', $isFullWidth: false },
                                        ],
                                    },
                                },
                            ],
                        },
                    },
                ],
            });
        });
    });
});
