import * as reduxStore from '../../redux/store';
import { SectionControlObject } from '../../component/control-objects';
import type { ContainerKey } from '../../component/types';
import { FieldKey } from '../../component/types';
import { findNextField, isSectionUsedInAnyDialogs } from '../layout-utils';
import {
    getMockState,
    getMockStore,
    getMockPageDefinition,
    addFieldToState,
    addBlockToState,
    addSectionToState,
    addSectionElementToState,
} from '../../__tests__/test-helpers';
import type { XtremAppState } from '../../redux';

describe('layout utils', () => {
    const screenId = 'TestScreen';

    beforeEach(() => {
        const state = getMockState();
        const layout = {
            $items: [
                {
                    $containerId: 'section1',
                    $layout: {
                        $items: [
                            {
                                $containerId: 'block1',
                                $layout: {
                                    $items: [
                                        { $bind: 'field1' },
                                        { $bind: 'field2' },
                                        { $bind: 'field3' },
                                        { $bind: 'field4' },
                                    ],
                                },
                            },
                            {
                                $containerId: 'block2',
                                $layout: {
                                    $items: [{ $bind: 'field5' }, { $bind: 'field6' }],
                                },
                            },
                            {
                                $containerId: 'block3',
                                $layout: {
                                    $items: [{ $bind: 'field7' }, { $bind: 'field8' }],
                                },
                            },
                        ],
                    },
                },
                {
                    $containerId: 'section2',
                    $layout: {
                        $items: [
                            {
                                $containerId: 'block4',
                                $layout: {
                                    $items: [{ $bind: 'field9' }, { $bind: 'field10' }],
                                },
                            },
                            {
                                $containerId: 'block5',
                                $layout: {
                                    $items: [{ $bind: 'field11' }, { $bind: 'field12' }],
                                },
                            },
                        ],
                    },
                },
                {
                    $containerId: 'section3',
                    $layout: {
                        $items: [
                            {
                                $containerId: 'block6',
                                $layout: {
                                    $items: [{ $bind: 'field13' }, { $bind: 'field14' }],
                                },
                            },
                            {
                                $containerId: 'block7',
                                $layout: {
                                    $items: [{ $bind: 'field15' }, { $bind: 'field16' }],
                                },
                            },
                        ],
                    },
                },
            ],
        };

        state.screenDefinitions[screenId] = getMockPageDefinition();
        state.screenDefinitions[screenId].metadata.layout = layout;

        addSectionToState(state, screenId, 'section1', { isHidden: () => false });
        addSectionToState(state, screenId, 'section2', { isHidden: true });
        addSectionToState(state, screenId, 'section3');
        addBlockToState(state, screenId, 'block1', {});
        addBlockToState(state, screenId, 'block2', { isHidden: true });
        addBlockToState(state, screenId, 'block3', {});
        addBlockToState(state, screenId, 'block4', {});
        addBlockToState(state, screenId, 'block5', {});
        addBlockToState(state, screenId, 'block6', {});
        addBlockToState(state, screenId, 'block7', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field1', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field2', {
            isReadOnly: true,
        });
        addFieldToState(FieldKey.Text, state, screenId, 'field3', { isHidden: true });
        addFieldToState(FieldKey.Text, state, screenId, 'field4', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field5', {
            isHidden: () => true,
        });
        addFieldToState(FieldKey.Text, state, screenId, 'field6', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field7', {
            isHidden: () => true,
        });
        addFieldToState(FieldKey.Text, state, screenId, 'field8', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field9', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field10', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field11', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field12', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field13', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field14', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field15', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field16', {
            isDisabled: true,
        });

        const store = getMockStore(state);
        jest.spyOn(reduxStore, 'getStore').mockReturnValue(store);
    });

    describe('findNextField', () => {
        it('should find the next field within same block', () => {
            const result = findNextField(screenId, 'field1');
            expect(result).not.toBeNull();
            expect(result!.id).toEqual('field2');
        });

        it('should find the next visible and enabled field if focusable field is requested', () => {
            const result = findNextField(screenId, 'field1', true);
            expect(result).not.toBeNull();
            expect(result!.id).toEqual('field4');
        });

        it('should find fields in the next block if there are no others left in the current one', () => {
            const result = findNextField(screenId, 'field4');
            expect(result).not.toBeNull();
            expect(result!.id).toEqual('field5');
        });

        it('should skip fields in hidden blocks if focusable field is requested', () => {
            const result = findNextField(screenId, 'field4', true);
            expect(result).not.toBeNull();
            expect(result!.id).toEqual('field8');
        });

        it('should skip fields in hidden sections if focusable field is requested', () => {
            const result = findNextField(screenId, 'field8', true);
            expect(result).not.toBeNull();
            expect(result!.id).toEqual('field13');
        });

        it('should return null when no fields left', () => {
            const result = findNextField(screenId, 'field16');
            expect(result).toBeNull();
        });
        it('should return null when no focusable fields left', () => {
            const result = findNextField(screenId, 'field15', true);
            expect(result).toBeNull();
        });
    });

    describe('isSectionUsedInAnyDialogs', () => {
        let state: XtremAppState;

        beforeEach(() => {
            state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        });

        it('should return true if section is used in any dialogs with a non array content', () => {
            addSectionElementToState<ContainerKey.Section>(
                state,
                screenId,
                SectionControlObject as any,
                'section',
                'testSection1',
                {},
            );
            const section = state.screenDefinitions[screenId].metadata.controlObjects.testSection1;
            expect(isSectionUsedInAnyDialogs({ 3: { content: section } } as any, 'testSection1')).toBe(true);
        });

        it('should return true if section is used in any dialogs with an array content', () => {
            addSectionElementToState<ContainerKey.Section>(
                state,
                screenId,
                SectionControlObject as any,
                'section',
                'testSection2',
                {},
            );
            const section = state.screenDefinitions[screenId].metadata.controlObjects.testSection2;
            expect(isSectionUsedInAnyDialogs({ 3: { content: [section] } } as any, 'testSection2')).toBe(true);
        });

        it('should return false if section is not used in any dialogs', () => {
            addSectionElementToState<ContainerKey.Section>(
                state,
                screenId,
                SectionControlObject as any,
                'section',
                'testSection2',
                {},
            );
            expect(isSectionUsedInAnyDialogs({ 3: { content: [] } } as any, 'testSection2')).toBe(false);
        });
    });
});
