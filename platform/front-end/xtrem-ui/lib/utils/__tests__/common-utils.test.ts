import { containsValueDeep, deepFindPropertyValues } from '../common-util';

describe('common utils', () => {
    describe('deepFindPropertyValues', () => {
        it('should find value by key in nested object', () => {
            const expected1 = [1, 2, 3];
            const expected2 = { someSpecialValue: 5 };
            const expected3 = 'wow what a value';
            const fixture = {
                key1: [
                    {
                        key2: [
                            1,
                            '43',
                            {
                                key3: {
                                    mySpecialKey: expected1,
                                    key4: [3, 21, null, '43', undefined],
                                },
                                key5: [
                                    'some string',
                                    {
                                        mySpecialKey: expected2,
                                    },
                                    false,
                                    true,
                                    {
                                        key15: [
                                            {
                                                mySpecialKey: expected2,
                                            },
                                        ],
                                    },
                                ],
                                key13: null,
                            },
                        ],
                        key6: {
                            key7: '1',
                            key8: 8,
                            key9: {
                                key10: '12',
                                key11: '3223',
                                mySpecialKey: expected3,
                                key12: 12,
                            },
                        },
                    },
                ],
                mySpecialKey: expected3,
            };

            const result = deepFindPropertyValues('mySpecialKey', fixture);
            expect(result).toHaveLength(3);
            expect(result[0]).toEqual(expected1);
            expect(result[1]).toEqual(expected2);
            expect(result[2]).toEqual(expected3);
        });
        it('should find value by key in nested object only if parent object passes callback', () => {
            const expected1 = [1, 2, 3];
            const expected2 = { someSpecialValue: 5 };
            const expected3 = 'wow what a value';
            const fixture = {
                key1: [
                    {
                        key2: [
                            1,
                            '43',
                            {
                                key3: {
                                    mySpecialKey: expected1,
                                    key4: [3, 21, null, '43', undefined],
                                },
                                key5: [
                                    'some string',
                                    {
                                        mySpecialKey: expected2,
                                    },
                                    false,
                                    true,
                                    {
                                        key15: [
                                            {
                                                mySpecialKey: expected2,
                                            },
                                        ],
                                    },
                                ],
                                key13: null,
                            },
                        ],
                        key6: {
                            key7: '1',
                            key8: 8,
                            key9: {
                                key10: '12',
                                key11: '3223',
                                mySpecialKey: expected3,
                                key12: 12,
                            },
                        },
                    },
                ],
                mySpecialKey: expected3,
            };

            const result = deepFindPropertyValues('mySpecialKey', fixture, (p: any) => p.key12 === 12);
            expect(result).toHaveLength(1);
            expect(result[0]).toEqual(expected3);
        });
    });

    describe('containsValueDeep', () => {
        it('should find value in array in strict mode', () => {
            const target = [1, null, 'test'];
            expect(containsValueDeep(target, '1')).toBe(false);
            expect(containsValueDeep(target, 1)).toBe(true);
        });

        it('should find value in array in non-strict mode', () => {
            const target = [1, null, 'test'];
            expect(containsValueDeep(target, '1', false)).toBe(true);
            expect(containsValueDeep(target, 1, false)).toBe(true);
        });

        it('should find value in object in strict mode', () => {
            const target = { someKey: 1, someOtherKey: null, aThridKey: 'test' };
            expect(containsValueDeep(target, '1')).toBe(false);
            expect(containsValueDeep(target, 1)).toBe(true);
        });

        it('should find value in object in strict mode', () => {
            const target = { someKey: 1, someOtherKey: null, aThridKey: 'test' };
            expect(containsValueDeep(target, '1', false)).toBe(true);
            expect(containsValueDeep(target, 1, false)).toBe(true);
        });

        it('should find value in deep object in strict mode', () => {
            const target = {
                someKey: 1,
                someOtherKey: null,
                aThridKey: 'test',
                someOtherStuff: [
                    {
                        asd: '32',
                        anotherArray: [
                            {
                                withSomeValue: {
                                    thatHasAnother: { object: { with: [{ the: { result: [1234, 'qwery'] } }] } },
                                },
                            },
                        ],
                    },
                    [3, 5],
                ],
            };
            expect(containsValueDeep(target, '1234')).toBe(false);
            expect(containsValueDeep(target, 1234)).toBe(true);
            expect(containsValueDeep(target, 12345)).toBe(false);
            expect(containsValueDeep(target, 'qwery')).toBe(true);
            expect(containsValueDeep(target, 'test')).toBe(true);
            expect(containsValueDeep(target, 'test1')).toBe(false);
        });
    });
});
