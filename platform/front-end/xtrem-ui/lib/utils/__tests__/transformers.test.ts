import { parseLocalizedNumberStringToNumber } from '../formatters';
import * as transformers from '../transformers';
import { mergedValueToSplitValue, schemaTypeNameFromNodeName, splitValueToMergedValue } from '../transformers';

const getNormalizedChangedRecordsMock = jest.fn(value => value);

jest.mock('../../service/collection-data-service', () => {
    return {
        CollectionValue: class CollectionValue {
            value: any;

            constructor(value) {
                this.value = value;
            }

            getNormalizedChangedRecords() {
                return getNormalizedChangedRecordsMock(this.value);
            }
        },
    };
});

describe('Transformers utils', () => {
    describe('parseLocalizedNumberStringToNumber', () => {
        it('should convert zero to zero', () => {
            expect(parseLocalizedNumberStringToNumber('0', '.')).toEqual(0);
        });

        it('should not convert numbers', () => {
            expect(parseLocalizedNumberStringToNumber(1.2345, ',')).toEqual(1.2345);
        });

        it('should convert a dash to zero', () => {
            expect(parseLocalizedNumberStringToNumber('-', '.')).toEqual(0);
        });

        it('should convert an empty string to null', () => {
            expect(parseLocalizedNumberStringToNumber('', '.')).toEqual(null);
        });

        it('should convert an undefined to null', () => {
            expect(parseLocalizedNumberStringToNumber(undefined, '.')).toEqual(null);
        });

        it('should convert decimal values', () => {
            expect(parseLocalizedNumberStringToNumber('1 231 234.5678', '.')).toEqual(1231234.5678);
            expect(parseLocalizedNumberStringToNumber('1.231.234,5678', ',')).toEqual(1231234.5678);
            expect(parseLocalizedNumberStringToNumber('0.5678', '.')).toEqual(0.5678);
            expect(parseLocalizedNumberStringToNumber('0,5678', ',')).toEqual(0.5678);
            expect(parseLocalizedNumberStringToNumber('3', '.')).toEqual(3);
            expect(parseLocalizedNumberStringToNumber('12343223', ',')).toEqual(12343223);
        });

        it('should convert negative decimal values', () => {
            expect(parseLocalizedNumberStringToNumber(' -1 231 234.5678 ', '.')).toEqual(-1231234.5678);
            expect(parseLocalizedNumberStringToNumber('- 1.231.234,5678 ', ',')).toEqual(-1231234.5678);
        });

        it('should handle missing whole number parts', () => {
            expect(parseLocalizedNumberStringToNumber('.1234', '.')).toEqual(0.1234);
            expect(parseLocalizedNumberStringToNumber(',5678', ',')).toEqual(0.5678);
        });

        it('should handle negative numbers with missing whole number parts', () => {
            expect(parseLocalizedNumberStringToNumber('-.1234', '.')).toEqual(-0.1234);
            expect(parseLocalizedNumberStringToNumber('-,5678', ',')).toEqual(-0.5678);
        });

        it('should handle missing decimal part', () => {
            expect(parseLocalizedNumberStringToNumber('1234.', '.')).toEqual(1234);
            expect(parseLocalizedNumberStringToNumber('5678,', ',')).toEqual(5678);
        });

        it('should handle negative numbers with missing decimal number part', () => {
            expect(parseLocalizedNumberStringToNumber('-1234.', '.')).toEqual(-1234);
            expect(parseLocalizedNumberStringToNumber('-5678,', ',')).toEqual(-5678);
        });

        it('should keep leading zeros', () => {
            expect(parseLocalizedNumberStringToNumber('0.0010000000', '.')).toEqual(0.001);
        });
    });

    describe('mergedValueToSplitValue', () => {
        it('Should transform a merged value (graphql dataset) to a split value (redux dataset)', () => {
            const mergedValue = {
                _id: 1,
                code: 20,
                lines: [
                    { _id: 1, code: 1 },
                    { _id: 2, code: 2 },
                ],
                user: {
                    _id: 121,
                    name: 'Popeye',
                    age: 99,
                },
                product: {
                    _id: 12,
                    name: 'Spinach',
                },
            };
            const expectedSplitValue = {
                _id: 1,
                code: 20,
                lines: [
                    { _id: 1, code: 1 },
                    { _id: 2, code: 2 },
                ],
                user__name: { _id: 121, name: 'Popeye' },
                user__age: { _id: 121, age: 99 },
                product__name: { _id: 12, name: 'Spinach' },
            };
            const splitValue = mergedValueToSplitValue(mergedValue);
            expect(splitValue).toEqual(expectedSplitValue);
        });
    });
    describe('splitValueToMergedValue', () => {
        it('Should transform a split value (redux dataset) to a merged value (graphQL dataset)', () => {
            const splitValue = {
                _id: 1,
                code: 20,
                lines: [
                    { _id: 1, code: 1 },
                    { _id: 2, code: 2 },
                ],
                user__name: { _id: 121, name: 'Popeye' },
                user__age: { _id: 121, age: 99 },
                product__name: { _id: 12, name: 'Spinach' },
            };
            const expectedMergedValue = {
                _id: 1,
                code: 20,
                lines: [
                    { _id: 1, code: 1 },
                    { _id: 2, code: 2 },
                ],
                user: {
                    _id: 121,
                    name: 'Popeye',
                    age: 99,
                },
                product: {
                    _id: 12,
                    name: 'Spinach',
                },
            };
            const mergedValue = splitValueToMergedValue(splitValue);
            expect(mergedValue).toEqual(expectedMergedValue);
        });

        it('should transform a split value deeply to a merged value (graphQL dataset)', () => {
            const splitValue = {
                site__id: {
                    legalCompany__id: {
                        currency__symbol: {
                            symbol: '£',
                            _id: '3',
                        },
                        id: 'GB20',
                        _id: '2',
                    },
                    id: 'GB022',
                    _id: '4',
                },
                site__name: {
                    name: 'Chem. London',
                    _id: '4',
                },
                item__id: {
                    stockUnit__id: {
                        decimalDigits: 3,
                        id: 'l',
                        _id: '9',
                    },
                    isManufactured: true,
                    id: '17890',
                    _id: '17',
                },
                item__name: {
                    name: 'Hydro-alcoholic gel for hand antisepsis',
                    _id: '17',
                },
                indirectCostSection__id: null,
                economicQuantity: '400',
                id: '17890/GB022',
                _id: '16',
            };
            const expectedMergedValue = {
                id: '17890/GB022',
                _id: '16',
                site: {
                    _id: '4',
                    id: 'GB022',
                    name: 'Chem. London',
                    legalCompany: {
                        currency: {
                            symbol: '£',
                            _id: '3',
                        },
                        id: 'GB20',
                        _id: '2',
                    },
                },
                item: {
                    _id: '17',
                    id: '17890',
                    name: 'Hydro-alcoholic gel for hand antisepsis',
                    stockUnit: {
                        decimalDigits: 3,
                        id: 'l',
                        _id: '9',
                    },
                    isManufactured: true,
                },
                indirectCostSection: null,
                economicQuantity: '400',
            };
            const mergedValue = splitValueToMergedValue(splitValue);
            expect(mergedValue).toEqual(expectedMergedValue);
        });

        it('should replace empty objects into nulls', () => {
            const splitValue = {
                site__id: {
                    legalCompany__id: {},
                    id: 'GB022',
                    _id: '4',
                },
                site__name: {
                    name: 'Chem. London',
                    _id: '4',
                },
                item__id: {},
                indirectCostSection__id: null,
                economicQuantity: '400',
                id: '17890/GB022',
                _id: '16',
                customer: {},
            };
            const expectedMergedValue = {
                id: '17890/GB022',
                _id: '16',
                site: {
                    _id: '4',
                    id: 'GB022',
                    name: 'Chem. London',
                    legalCompany: null,
                },
                item: null,
                indirectCostSection: null,
                economicQuantity: '400',
                customer: null,
            };
            const mergedValue = splitValueToMergedValue(splitValue);
            expect(mergedValue).toEqual(expectedMergedValue);
        });
    });

    describe('schemaTypeNameFromNodeName', () => {
        it('should transform name normal schema name', () => {
            expect(schemaTypeNameFromNodeName('@sage/x3-show-case/SimpleNode')).toEqual('SimpleNode');
            expect(schemaTypeNameFromNodeName('@sage/x3-show-case/simpleNode')).toEqual('SimpleNode');
        });
    });

    it('removeNullOnlyLeaves', () => {
        const fixture = {
            _action: 'update',
            _id: '1',
            charge: 0,
            discount: 0,
            document: {
                _id: '1',
                currency: {
                    _id: '2',
                    decimalDigits: 2,
                    id: 'EUR',
                    name: 'Euro',
                    rounding: 0,
                    symbol: '€',
                },
                number: 'PI220001',
            },
            grossPrice: 0,
            item: {
                _id: '33',
                description: 'Screw driver',
                id: 'SCW-276',
                lotManagement: 'notManaged',
                name: 'Screw driver',
                stockUnit: { _id: '1', id: 'each', name: 'each' },
                type: 'good',
                emptyArrayToKeep: [],
            },
            lineAmountExcludingTax: 0,
            lineAmountExcludingTaxInCompanyCurrency: 0,
            lineAmountIncludingTax: 0,
            lineAmountIncludingTaxInCompanyCurrency: 0,
            matchingStatus: 'variance',
            netPrice: 0,
            priceOrigin: null,
            purchaseOrderLine: {
                invoicedQuantity: null,
                purchaseOrderLine: { lineAmountExcludingTax: null },
            },
            purchaseReceiptLine: {
                purchaseReceiptLine: { lineAmountExcludingTax: null },
            },
            purchaseUnit: {
                _id: '1',
                decimalDigits: 0,
                id: 'each',
                name: 'each',
            },
            purchaseUnitToStockUnitConversionFactor: 1,
            quantity: 2,
            quantityInStockUnit: 2,
            recipientSite: {
                _id: '8',
                country: {
                    _id: '3',
                    id: 'FR',
                    name: 'France',
                    regionLabel: 'department',
                    zipLabel: 'postalCode',
                },
                id: 'DEP1-S01',
                legalCompany: { _id: '4' },
                name: 'Entrepot de  Saint  Denis',
            },
            stockUnit: {
                _id: '1',
                decimalDigits: 0,
                id: 'each',
                name: 'each',
            },
            storedAttributes: null,
            storedDimensions: null,
            taxAmount: 0,
            taxAmountAdjusted: 0,
            taxDate: '2022-08-15',
            uiTaxes:
                '{"taxes":[{"_id":2,"taxCategoryReference":{"_id":"1","name":"Value added tax"},"taxCategory":"Value added tax","taxRate":"20","taxAmount":"0","nonTaxableAmount":"0","exemptAmount":"0","taxableAmount":"0","currency":{"_id":"2","name":"Euro"},"deductibleTaxRate":"100","deductibleTaxAmount":"0","isReverseCharge":false,"taxAmountAdjusted":"0","isTaxMandatory":true,"isSubjectToGlTaxExcludedAmount":true,"taxReference":{"_id":"8","name":"Normal rate deductible on debits"},"tax":"Normal rate deductible on debits","_sortValue":10}],"taxEngine":"genericTaxCalculation"}',
            varianceApprover: { displayName: null },
            testProperty: { someStuff: null, toKeep: 3 },
        };

        transformers.removeNullOnlyLeaves(fixture);
        expect(fixture).toEqual({
            _action: 'update',
            _id: '1',
            charge: 0,
            discount: 0,
            document: {
                _id: '1',
                currency: {
                    _id: '2',
                    decimalDigits: 2,
                    id: 'EUR',
                    name: 'Euro',
                    rounding: 0,
                    symbol: '€',
                },
                number: 'PI220001',
            },
            grossPrice: 0,
            item: {
                _id: '33',
                description: 'Screw driver',
                id: 'SCW-276',
                lotManagement: 'notManaged',
                name: 'Screw driver',
                stockUnit: { _id: '1', id: 'each', name: 'each' },
                type: 'good',
                emptyArrayToKeep: [],
            },
            lineAmountExcludingTax: 0,
            lineAmountExcludingTaxInCompanyCurrency: 0,
            lineAmountIncludingTax: 0,
            lineAmountIncludingTaxInCompanyCurrency: 0,
            matchingStatus: 'variance',
            netPrice: 0,
            priceOrigin: null,
            purchaseUnit: {
                _id: '1',
                decimalDigits: 0,
                id: 'each',
                name: 'each',
            },
            purchaseUnitToStockUnitConversionFactor: 1,
            quantity: 2,
            quantityInStockUnit: 2,
            recipientSite: {
                _id: '8',
                country: {
                    _id: '3',
                    id: 'FR',
                    name: 'France',
                    regionLabel: 'department',
                    zipLabel: 'postalCode',
                },
                id: 'DEP1-S01',
                legalCompany: { _id: '4' },
                name: 'Entrepot de  Saint  Denis',
            },
            stockUnit: {
                _id: '1',
                decimalDigits: 0,
                id: 'each',
                name: 'each',
            },
            storedAttributes: null,
            storedDimensions: null,
            taxAmount: 0,
            taxAmountAdjusted: 0,
            taxDate: '2022-08-15',
            uiTaxes:
                '{"taxes":[{"_id":2,"taxCategoryReference":{"_id":"1","name":"Value added tax"},"taxCategory":"Value added tax","taxRate":"20","taxAmount":"0","nonTaxableAmount":"0","exemptAmount":"0","taxableAmount":"0","currency":{"_id":"2","name":"Euro"},"deductibleTaxRate":"100","deductibleTaxAmount":"0","isReverseCharge":false,"taxAmountAdjusted":"0","isTaxMandatory":true,"isSubjectToGlTaxExcludedAmount":true,"taxReference":{"_id":"8","name":"Normal rate deductible on debits"},"tax":"Normal rate deductible on debits","_sortValue":10}],"taxEngine":"genericTaxCalculation"}',
            testProperty: { someStuff: null, toKeep: 3 },
        });
    });
});
