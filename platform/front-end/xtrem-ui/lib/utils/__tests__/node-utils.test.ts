import { GraphQLTypes, type Dict } from '@sage/xtrem-shared';
import { findDeepPropertyDetails } from '../node-utils';
import type { FormattedNodeDetails } from '../../service/metadata-types';
import { GraphQLKind } from '../../types';

describe('node utils', () => {
    describe('findDeepPropertyType', () => {
        const nodeTypes: Dict<FormattedNodeDetails> = {
            SalesOrder: {
                name: 'SalesOrder',
                title: 'SalesOrder',
                packageName: '@sage/xtrem-test',
                properties: {
                    _id: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
                    textSimpleField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    bind02: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    orderCity: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    aDateField: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar },
                    aDecimalField: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
                    aFloatField: { type: GraphQLTypes.Float, kind: GraphQLKind.Scalar },
                    anotherIntegerField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    aTransientIntegerField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    aTableField: { type: 'SalesOrderLine', kind: 'LIST' },
                    customer: { type: 'Customer', kind: 'OBJECT' },
                },
                mutations: {},
            },
            SalesOrderLine: {
                name: 'SalesOrderLine',
                title: 'SalesOrderLine',
                packageName: '@sage/xtrem-test',
                properties: {
                    aStringField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    aNumericField: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
                    anotherNumericField: { type: GraphQLTypes.Float, kind: GraphQLKind.Scalar },
                    anIntegerField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                },
                mutations: {},
            },
            Customer: {
                name: 'Customer',
                title: 'Customer',
                packageName: '@sage/xtrem-test',
                properties: {
                    name: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    contactEmail: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    deliveryAddress: { type: 'Address', kind: 'OBJECT' },
                },
                mutations: {},
            },
            Address: {
                name: 'Address',
                title: 'Address',
                packageName: '@sage/xtrem-test',
                properties: {
                    line1: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    line2: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    town: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    houseNumber: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    country: { type: 'Country', kind: 'OBJECT' },
                },
                mutations: {},
            },
            WorkOrder: {
                name: 'WorkOrder',
                title: 'WorkOrder',
                packageName: '@sage/xtrem-test',
                properties: {
                    _id: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
                    status: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    foo: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    bar: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                },
                mutations: {
                    createWorkOrder: {
                        name: 'createWorkOrder',
                        title: 'Create Work Order',
                        parameters: [{ name: 'data', title: 'Work Order Data' }],
                    },
                    updateWorkOrder: {
                        name: 'updateWorkOrder',
                        title: 'Update Work Order',
                        parameters: [
                            { name: 'id', title: 'Id of Work Order' },
                            { name: 'data', title: 'Work Order Data' },
                        ],
                    },
                    removeWorkOrder: {
                        name: 'removeWorkOrder',
                        title: 'Remove Work Order',
                        parameters: [{ name: 'id', title: 'Id of Work Order' }],
                    },
                },
            },
        };

        it('should find a simple property', () => {
            expect(findDeepPropertyDetails('SalesOrder', 'textSimpleField', nodeTypes)).toEqual({
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                parentNode: 'SalesOrder',
            });
        });

        it('should return null if property definition not is not found', () => {
            expect(findDeepPropertyDetails('SalesOrder', 'unknownProperty', nodeTypes)).toEqual(null);
        });

        it('should return null if node definition not is not found', () => {
            expect(findDeepPropertyDetails('PurchaseOrder', 'unknownProperty', nodeTypes)).toEqual(null);
        });

        it('should find a nested property', () => {
            expect(
                findDeepPropertyDetails(
                    'SalesOrder',
                    {
                        customer: {
                            deliveryAddress: {
                                houseNumber: true,
                            },
                        },
                    },
                    nodeTypes,
                ),
            ).toEqual({ type: GraphQLTypes.Int, kind: GraphQLKind.Scalar, parentNode: 'Address' });
        });

        it('should find a nested property for input if it is for a search query', () => {
            expect(
                findDeepPropertyDetails(
                    'SalesOrder',
                    {
                        customer: {
                            deliveryAddress: {
                                houseNumber: true,
                            },
                        },
                    },
                    nodeTypes,
                    true,
                ),
            ).toEqual({ type: GraphQLTypes.Int, kind: GraphQLKind.Scalar, parentNode: 'Address' });
        });

        it('should return null if a nested node definition not is not found', () => {
            expect(
                findDeepPropertyDetails(
                    'SalesOrder',
                    {
                        customer: {
                            deliveryAddress: {
                                country: {
                                    code: true,
                                },
                            },
                        },
                    },
                    nodeTypes,
                ),
            ).toEqual(null);
        });

        it('should find properties listed under mutations', () => {
            expect(findDeepPropertyDetails('WorkOrder', 'updateWorkOrder', nodeTypes)).toEqual({
                name: 'updateWorkOrder',
                parameters: [
                    { name: 'id', title: 'Id of Work Order' },
                    { name: 'data', title: 'Work Order Data' },
                ],
                parentNode: 'WorkOrder',
                title: 'Update Work Order',
            });
        });
    });
});
