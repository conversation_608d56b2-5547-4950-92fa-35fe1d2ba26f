import * as xtremRedux from '../../redux';
import { formatNumericValue, getNumberComponentsFromLocalizedNumberString } from '../formatters';
import { getMockState, getMockStore } from '../../__tests__/test-helpers/mock-store-helpers';

describe('Formatter utils', () => {
    const screenId = 'TestScreen';

    describe('formatNumericValue', () => {
        it('should return empty string if value is null', () => {
            expect(formatNumericValue({ screenId, value: null })).toBe('');
        });

        it('should return empty string if value is undefined', () => {
            expect(formatNumericValue({ screenId, value: undefined })).toBe('');
        });

        it('should return empty string if value is -', () => {
            expect(formatNumericValue({ screenId, value: '-' })).toBe('');
        });

        it('should return empty string if value is .', () => {
            expect(formatNumericValue({ screenId, value: '.' })).toBe('');
        });

        it('should return empty string if value is empty string', () => {
            expect(formatNumericValue({ screenId, value: '' })).toBe('');
        });

        it('should return empty string if value is not a number', () => {
            expect(formatNumericValue({ screenId, value: 'test' })).toBe('');
        });

        it('should send value converted to string ignoring the locale', () => {
            expect(formatNumericValue({ screenId, value: 1.2345, ignoreLocale: true })).toBe('1.235');
        });

        it('should send value converted to string when input is integer', () => {
            expect(formatNumericValue({ screenId, value: 1 })).toBe('1');
        });

        it('should send value converted to string when input is decimal', () => {
            expect(formatNumericValue({ screenId, value: 1.2345 })).toBe('1.235');
        });

        it('should send value converted to string when input is string', () => {
            expect(formatNumericValue({ screenId, value: '1.2345' })).toBe('1.235');
        });

        describe('locales', () => {
            let mockState: any;

            beforeEach(() => {
                mockState = getMockState({
                    applicationContext: {
                        updateMenu: jest.fn(),
                        handleNavigation: jest.fn(),
                        onPageTitleChange: jest.fn(),
                        locale: 'es-ES',
                    },
                });
                const mockStore = getMockStore(mockState);
                jest.spyOn(xtremRedux, 'getStore').mockReturnValue(mockStore);
            });

            afterEach(() => {
                jest.resetAllMocks();
            });

            it('should return value converted to string according with the spanish locale', () => {
                expect(formatNumericValue({ screenId, value: '1.2345' })).toBe('1,235');
            });

            it('should return value converted to string with the scale provided', () => {
                expect(formatNumericValue({ screenId, value: '1', scale: 2 })).toBe('1,00');
            });

            it('should return value converted to string with the scale provided and ignoring the locale', () => {
                expect(formatNumericValue({ screenId, value: '1', ignoreLocale: true, scale: 2 })).toBe('1.00');
            });

            it('should return value converted to string with the scale provided and ignoring the locale', () => {
                expect(formatNumericValue({ screenId, value: 1, ignoreLocale: true, scale: 2 })).toBe('1.00');
            });

            it('should return value converted to string with the scale provided', () => {
                expect(formatNumericValue({ screenId, value: '1.2345', scale: 2 })).toBe('1,23');
            });

            it('should return value converted to string with the scale provided and ignoring the locale', () => {
                expect(formatNumericValue({ screenId, value: '1.2345', ignoreLocale: true, scale: 2 })).toBe('1.23');
            });

            it('should return value converted to string according with the spanish locale', () => {
                expect(formatNumericValue({ screenId, value: 1.2345 })).toBe('1,235');
            });

            it('should return value converted to string according with no locale in the state', () => {
                mockState.applicationContext.locale = undefined;
                expect(formatNumericValue({ screenId, value: 1.2345 })).toBe('1.235');
            });
        });
    });

    describe('getNumberComponentsFromLocalizedNumberString', () => {
        it('should return default numbers component object given undefined value', () => {
            const expected = { integer: '', decimal: '', separator: '.', sign: '+' };
            const result = getNumberComponentsFromLocalizedNumberString(undefined, '.');
            expect(result).toEqual(expected);
        });

        it('should return default numbers component object given null value', () => {
            const expected = { integer: '', decimal: '', separator: '.', sign: '+' };
            const result = getNumberComponentsFromLocalizedNumberString(null, '.');
            expect(result).toEqual(expected);
        });

        it('should return correct components for integer value', () => {
            const expected = { integer: '1337', decimal: '', separator: '.', sign: '+' };
            const result = getNumberComponentsFromLocalizedNumberString('1337', '.');
            expect(result).toEqual(expected);
        });

        it('should return correct components for decimal value', () => {
            const expected = { integer: '13', decimal: '37', separator: '.', sign: '+' };
            const result = getNumberComponentsFromLocalizedNumberString('13.37', '.');
            expect(result).toEqual(expected);
        });

        it('should return correct components for positive value', () => {
            const expected = { integer: '13', decimal: '37', separator: '.', sign: '+' };
            const result = getNumberComponentsFromLocalizedNumberString('+13.37', '.');
            expect(result).toEqual(expected);
        });

        it('should return correct components for negative value', () => {
            const expected = { integer: '13', decimal: '37', separator: '.', sign: '-' };
            const result = getNumberComponentsFromLocalizedNumberString('-13.37', '.');
            expect(result).toEqual(expected);
        });

        it('should return correct components for number value', () => {
            const expected = { integer: '13', decimal: '37', separator: '.', sign: '+' };
            const result = getNumberComponentsFromLocalizedNumberString(13.37, '.');
            expect(result).toEqual(expected);
        });
    });
});
