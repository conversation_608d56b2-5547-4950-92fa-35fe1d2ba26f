import loki from '@sage/lokijs';
import { extendLokiOperators } from '../../service/loki';
import { xtremConsole } from '../console';
import { transformToLokiJsFilter } from '../lokijs-filter-transformer';

describe('transformToLokiJsFilter. legacy tests.', () => {
    it('should remap simple filter without operator', () => {
        expect(
            transformToLokiJsFilter({
                product: 'te',
            }),
        ).toEqual({
            product: 'te',
        });
    });

    it('should remap simple filter object', () => {
        expect(
            transformToLokiJsFilter({
                description: {
                    _eq: '1234',
                },
            }),
        ).toEqual({
            description: { $eq: '1234' },
        });
    });

    describe('regex', () => {
        it('should transform a regex filter with options', () => {
            expect(transformToLokiJsFilter({ anyField: { _regex: 'testpattern', _options: 'i' } })).toEqual({
                anyField: { $regex: ['testpattern', 'i'] },
            });
        });

        it('should transform a regex filter without options', () => {
            expect(transformToLokiJsFilter({ anyField: { _regex: 'testpattern' } })).toEqual({
                anyField: { $regex: 'testpattern' },
            });
        });

        it('should transform a nested regex statement', () => {
            const nestedFilterFixture = [
                {
                    _id: {
                        _eq: '12345',
                    },
                },
                {
                    provider: {
                        textField: {
                            _regex: 'express',
                            _options: 'i',
                        },
                    },
                },
            ];

            expect(transformToLokiJsFilter(nestedFilterFixture)).toEqual({
                _id: { $eq: '12345' },
                'provider.textField': {
                    $regex: ['express', 'i'],
                },
            });
        });
    });

    describe('ranges', () => {
        it('should transform inclusive number range', () => {
            expect(transformToLokiJsFilter({ anyField: { _gte: 15, _lte: 19 } })).toEqual({
                anyField: { $and: [{ $gte: 15 }, { $lte: 19 }] },
            });
        });

        it('should transform exclusive number range', () => {
            expect(transformToLokiJsFilter({ anyField: { _gt: 15, _lt: 19 } })).toEqual({
                anyField: { $and: [{ $gt: 15 }, { $lt: 19 }] },
            });
        });

        it('should transform inclusive date range', () => {
            expect(transformToLokiJsFilter({ anyField: { _gte: '2020-02-12', _lte: '2020-05-23' } })).toEqual({
                anyField: {
                    $and: [{ $gte: '2020-02-12' }, { $lte: '2020-05-23' }],
                },
            });
        });

        it('should transform exclusive date range', () => {
            expect(transformToLokiJsFilter({ anyField: { _gt: '2020-02-12', _lt: '2020-05-23' } })).toEqual({
                anyField: { $and: [{ $gt: '2020-02-12' }, { $lt: '2020-05-23' }] },
            });
        });

        it('should transform nested range filters', () => {
            const nestedFilterFixtureWithRange = [
                {
                    product: 'test',
                },
                {
                    provider: {
                        textField: {
                            _gt: '1',
                            _lt: '9',
                        },
                    },
                },
            ];

            expect(transformToLokiJsFilter(nestedFilterFixtureWithRange)).toEqual({
                product: 'test',
                'provider.textField': {
                    $and: [{ $gt: '1' }, { $lt: '9' }],
                },
            });
        });
    });

    describe('conjunction', () => {
        it('should remap complex filter structure with OR conjunction', () => {
            const nestedFilterFixtureWithOr = {
                _or: [
                    {
                        _id: {
                            _eq: '1234',
                        },
                    },
                    {
                        description: {
                            _regex: 'te',
                            _options: 'i',
                        },
                    },
                    {
                        provider: {
                            textField: {
                                _regex: 'te',
                                _options: 'i',
                            },
                        },
                    },
                ],
            };

            expect(transformToLokiJsFilter(nestedFilterFixtureWithOr)).toEqual({
                $or: [
                    { _id: { $eq: '1234' } },
                    { description: { $regex: ['te', 'i'] } },
                    { 'provider.textField': { $regex: ['te', 'i'] } },
                ],
            });
        });

        it('should remap complex filter structure with AND conjunction', () => {
            const nestedFilterFixtureWithAnd = {
                _and: [
                    {
                        _id: {
                            _eq: '1234',
                        },
                    },
                    {
                        description: {
                            _regex: 'te',
                            _options: 'i',
                        },
                    },
                    {
                        provider: {
                            textField: {
                                _regex: 'te',
                                _options: 'i',
                            },
                        },
                    },
                ],
            };

            expect(transformToLokiJsFilter(nestedFilterFixtureWithAnd)).toEqual({
                $and: [
                    {
                        _id: { $eq: '1234' },
                    },
                    { description: { $regex: ['te', 'i'] } },
                    { 'provider.textField': { $regex: ['te', 'i'] } },
                ],
            });
        });

        it('should remap complex filter structure without conjunction option', () => {
            const nestedFilterFixtureWithAnd = [
                {
                    _id: {
                        _eq: '1234',
                    },
                },
                {
                    description: {
                        _regex: 'te',
                        _options: 'i',
                    },
                },
                {
                    provider: {
                        textField: {
                            _regex: 'te',
                            _options: 'i',
                        },
                    },
                },
            ];

            expect(transformToLokiJsFilter(nestedFilterFixtureWithAnd)).toEqual({
                _id: { $eq: '1234' },
                description: { $regex: ['te', 'i'] },
                'provider.textField': { $regex: ['te', 'i'] },
            });
        });
    });
    describe('null values', () => {
        it('should transform nulls at node', () => {
            const filter = {
                $or: [{ location: { code: null } }, { status: 1 }],
            };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual({ $or: [{ 'location.code': { $eq: null } }, { status: 1 }] });
        });

        it('should transform null at _eq filter', () => {
            const filter = {
                anyField: { _eq: null },
            };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual({ anyField: { $eq: null } });
        });

        it('should transform null at _ne filter', () => {
            const filter = {
                anyField: { _ne: null },
            };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual({ anyField: { $ne: null } });
        });
    });
    describe('complex examples', () => {
        it('should remap complex example with nested OR conjunction', () => {
            const complexExample = {
                _and: [
                    {
                        isClosed: { _eq: false },
                        signatureStatus: { _in: [3, 4, 5] },
                        receiptStatus: { _in: [1, 2] },
                        receiptSite: { _in: ['UST10', null] },
                        purchaseSite: { legalCompany: { _eq: 'UST' } },
                        purchaseOrderQuantityLine: {
                            isClosed: false,
                            receiptSite: 'UST10',
                            sourceRequest: { _ne: 2 },
                            workInProgressStatus: { _eq: 1 },
                            productType: { _ne: 2 },
                            lineType: { _eq: 1 },
                        },
                    },
                    {
                        _or: [{ id: { _regex: '00001', _options: 'i' } }],
                    },
                ],
            };
            const result = transformToLokiJsFilter(complexExample);
            expect(result).toEqual({
                $and: [
                    {
                        isClosed: {
                            $eq: false,
                        },
                        'purchaseOrderQuantityLine.isClosed': false,
                        'purchaseOrderQuantityLine.lineType': {
                            $eq: 1,
                        },
                        'purchaseOrderQuantityLine.productType': {
                            $ne: 2,
                        },
                        'purchaseOrderQuantityLine.receiptSite': 'UST10',
                        'purchaseOrderQuantityLine.sourceRequest': {
                            $ne: 2,
                        },
                        'purchaseOrderQuantityLine.workInProgressStatus': {
                            $eq: 1,
                        },
                        'purchaseSite.legalCompany': {
                            $eq: 'UST',
                        },
                        receiptSite: {
                            $in: ['UST10', null],
                        },
                        receiptStatus: {
                            $in: [1, 2],
                        },
                        signatureStatus: {
                            $in: [3, 4, 5],
                        },
                    },
                    {
                        $or: [
                            {
                                id: {
                                    $regex: ['00001', 'i'],
                                },
                            },
                        ],
                    },
                ],
            });
        });

        it('should transform complex filter statement with nested $and statements', () => {
            const complexExample = {
                _and: [
                    {
                        _and: [
                            {
                                product: {
                                    _regex: 'beef',
                                    _options: 'i',
                                },
                            },
                            {
                                provider: {
                                    textField: {
                                        _regex: 'amazon',
                                        _options: 'i',
                                    },
                                },
                            },
                        ],
                    },
                    {
                        _or: [
                            {
                                description: {
                                    _regex: 'tongue',
                                    _options: 'i',
                                },
                            },
                            {
                                product: {
                                    _regex: 'tongue',
                                    _options: 'i',
                                },
                            },
                        ],
                    },
                ],
            };

            const result = transformToLokiJsFilter(complexExample);
            expect(result).toEqual({
                $and: [
                    {
                        $and: [
                            {
                                product: {
                                    $regex: ['beef', 'i'],
                                },
                            },
                            {
                                'provider.textField': {
                                    $regex: ['amazon', 'i'],
                                },
                            },
                        ],
                    },
                    {
                        $or: [
                            {
                                description: {
                                    $regex: ['tongue', 'i'],
                                },
                            },
                            {
                                product: {
                                    $regex: ['tongue', 'i'],
                                },
                            },
                        ],
                    },
                ],
            });
        });
    });

    describe('transformation of qualifier filter statements', () => {
        it('should transform a query with _atMost qualifier filter', () => {
            const complexExample = {
                isClosed: { _eq: false },
                purchaseOrderQuantityLines: {
                    _atMost: 10,
                    isClosed: false,
                    sourceRequest: { _ne: 2 },
                    workInProgressStatus: { _eq: 1 },
                    productType: { _ne: 2 },
                    lineType: { _eq: 1 },
                },
            };
            const result = transformToLokiJsFilter(complexExample);

            expect(result).toEqual({
                isClosed: {
                    $eq: false,
                },
                '__restricted_PurchaseOrderQuantityLines.query.totalCount': {
                    $lte: 10,
                },
            });
        });

        it('should transform a query with _atLeast qualifier filter', () => {
            const complexExample = {
                isClosed: { _eq: false },
                purchaseOrderQuantityLines: {
                    _atLeast: 4,
                    isClosed: false,
                    sourceRequest: { _ne: 2 },
                    workInProgressStatus: { _eq: 1 },
                    productType: { _ne: 2 },
                    lineType: { _eq: 1 },
                },
            };
            const result = transformToLokiJsFilter(complexExample);

            expect(result).toEqual({
                isClosed: {
                    $eq: false,
                },
                '__restricted_PurchaseOrderQuantityLines.query.totalCount': {
                    $gte: 4,
                },
            });
        });

        it('should transform a query with _none qualifier filter with true value and always expect a zero', () => {
            const complexExample = {
                isClosed: { _eq: false },
                purchaseOrderQuantityLines: {
                    _none: true,
                    isClosed: false,
                    sourceRequest: { _ne: 2 },
                    workInProgressStatus: { _eq: 1 },
                    productType: { _ne: 2 },
                    lineType: { _eq: 1 },
                },
            };
            const result = transformToLokiJsFilter(complexExample);

            expect(result).toEqual({
                isClosed: {
                    $eq: false,
                },
                '__restricted_PurchaseOrderQuantityLines.query.totalCount': {
                    $eq: 0,
                },
            });
        });

        it('should exclude _none qualifier filter with false value', () => {
            const complexExample = {
                isClosed: { _eq: false },
                purchaseOrderQuantityLines: {
                    _none: false,
                    isClosed: false,
                    sourceRequest: { _ne: 2 },
                    workInProgressStatus: { _eq: 1 },
                    productType: { _ne: 2 },
                    lineType: { _eq: 1 },
                },
            };
            const result = transformToLokiJsFilter(complexExample);

            expect(result).toEqual({
                isClosed: {
                    $eq: false,
                },
                'purchaseOrderQuantityLines.isClosed': false,
                'purchaseOrderQuantityLines.sourceRequest': {
                    $ne: 2,
                },
                'purchaseOrderQuantityLines.workInProgressStatus': {
                    $eq: 1,
                },
                'purchaseOrderQuantityLines.productType': {
                    $ne: 2,
                },
                'purchaseOrderQuantityLines.lineType': {
                    $eq: 1,
                },
            });
        });

        it('should transform a query with _every qualifier filter with true value and use a relative filter', () => {
            const complexExample = {
                isClosed: { _eq: false },
                purchaseOrderQuantityLines: {
                    _every: true,
                    isClosed: false,
                    sourceRequest: { _ne: 2 },
                    workInProgressStatus: { _eq: 1 },
                    productType: { _ne: 2 },
                    lineType: { _eq: 1 },
                },
            };
            const result = transformToLokiJsFilter(complexExample);

            expect(result).toEqual({
                isClosed: {
                    $eq: false,
                },
                '__restricted_PurchaseOrderQuantityLines.query.totalCount': {
                    $$gte: '__all_PurchaseOrderQuantityLines.query.totalCount',
                },
            });
        });

        it('should exclude _every qualifier filter with false', () => {
            const complexExample = {
                isClosed: { _eq: false },
                purchaseOrderQuantityLines: {
                    _every: false,
                    isClosed: false,
                    sourceRequest: { _ne: 2 },
                    workInProgressStatus: { _eq: 1 },
                    productType: { _ne: 2 },
                    lineType: { _eq: 1 },
                },
            };
            const result = transformToLokiJsFilter(complexExample);

            expect(result).toEqual({
                isClosed: {
                    $eq: false,
                },
                'purchaseOrderQuantityLines.isClosed': false,
                'purchaseOrderQuantityLines.sourceRequest': {
                    $ne: 2,
                },
                'purchaseOrderQuantityLines.workInProgressStatus': {
                    $eq: 1,
                },
                'purchaseOrderQuantityLines.productType': {
                    $ne: 2,
                },
                'purchaseOrderQuantityLines.lineType': {
                    $eq: 1,
                },
            });
        });
    });
});

describe('transformToLokiJsFilter. reimplementation tests.', () => {
    const data = require('./data/data.json');
    let db: any;
    let collection: any;
    beforeAll(() => {
        extendLokiOperators();
        db = new loki('db');
        collection = db.addCollection('collection');
        data.forEach((record: any) => {
            collection.insert(record as any);
        });
    });
    describe('base', () => {
        it('simple equal (no operator)', () => {
            const filter = { code: 'LPN-000003' };
            const expected = { code: 'LPN-000003' };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(1);
            expect(find[0].$loki).toBe(3);
        });

        it('simple statement', () => {
            const filter = { status: { _eq: 'inStock' } };
            const expected = { status: { $eq: 'inStock' } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(51);
            expect(find[0].$loki).toBe(3);
        });
    });

    describe('regex', () => {
        it('regex with options', () => {
            const filter = { status: { _regex: 'INSTOCK', _options: 'i' } };
            const expected = { status: { $regex: ['INSTOCK', 'i'] } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(51);
            expect(find[0].$loki).toBe(3);
        });

        it('regex without options', () => {
            const filter = { status: { _regex: 'inStoc' } };
            const expected = { status: { $regex: 'inStoc' } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(51);
            expect(find[0].$loki).toBe(3);
        });

        it('nested regex', () => {
            const filter = { code: 'LPN-000004', location: { code: { _regex: 'p010', _options: 'i' } } };
            const expected = { code: 'LPN-000004', 'location.code': { $regex: ['p010', 'i'] } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(1);
            expect(find[0].$loki).toBe(4);
        });
    });

    describe('ranges', () => {
        it('inclusive number range', () => {
            const filter = { rndNumber: { _gte: 15, _lte: 19 } };
            const expected = { rndNumber: { $and: [{ $gte: 15 }, { $lte: 19 }] } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(44);
            expect(find[0].$loki).toBe(3);
        });

        it('exclusive number range', () => {
            const filter = { rndNumber: { _gt: 15, _lt: 19 } };
            const expected = { rndNumber: { $and: [{ $gt: 15 }, { $lt: 19 }] } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(24);
            expect(find[0].$loki).toBe(3);
        });

        it('inclusive date range', () => {
            const filter = { rndDate: { _gte: '2020-02-12', _lte: '2020-05-23' } };
            const expected = { rndDate: { $and: [{ $gte: '2020-02-12' }, { $lte: '2020-05-23' }] } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(20);
            expect(find[0].$loki).toBe(1);
        });

        it('exclusive date range', () => {
            const filter = { rndDate: { _gt: '2020-02-12', _lt: '2020-05-23' } };
            const expected = { rndDate: { $and: [{ $gt: '2020-02-12' }, { $lt: '2020-05-23' }] } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(19);
            expect(find[0].$loki).toBe(12);
        });

        it('nested range filters', () => {
            const filter = {
                rndDate: { _gt: '2020-02-12', _lt: '2020-05-23' },
                container: { nestedRndNumber: { _gt: 1, _lt: 15 } },
            };
            const expected = {
                'container.nestedRndNumber': { $and: [{ $gt: 1 }, { $lt: 15 }] },
                rndDate: { $and: [{ $gt: '2020-02-12' }, { $lt: '2020-05-23' }] },
            };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(9);
            expect(find[0].$loki).toBe(21);
        });
    });

    describe('conjunctions', () => {
        it('standalone _or (multiple objects)', () => {
            const filter = {
                _or: [
                    { _id: { _eq: 'LPN-000003' } },
                    { code: { _regex: '000002', _options: 'i' } },
                    { container: { nestedRndNumber: 11 } },
                ],
            };
            const expected = {
                $or: [
                    { _id: { $eq: 'LPN-000003' } },
                    { code: { $regex: ['000002', 'i'] } },
                    { 'container.nestedRndNumber': 11 },
                ],
            };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(15);
            expect(find[0].$loki).toBe(3);
        });

        it('standalone _and (top level object)', () => {
            const filter = {
                status: {
                    _eq: 'free',
                },
                container: {
                    code: { _eq: 'ADC_CONTAINER_01' },
                },
                location: {
                    code: 'LP007',
                },
            };
            const expected = {
                status: { $eq: 'free' },
                'container.code': { $eq: 'ADC_CONTAINER_01' },
                'location.code': 'LP007',
            };
            // TODO: const expected = {$and:[{status:{$eq:'free'}},{'container.code':{$eq:'ADC_CONTAINER_01'}},{'location.code':'LP007'}]};
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(5);
            expect(find[0].$loki).toBe(80);
            expect(find[0].status).toBe('free');
            expect(find[0].container.code).toBe('ADC_CONTAINER_01');
            expect(find[0].location.code).toBe('LP007');
        });

        it('standalone _and (implicit, single object)', () => {
            const filter = [
                {
                    status: {
                        _eq: 'free',
                    },
                    container: {
                        code: { _eq: 'ADC_CONTAINER_01' },
                    },
                    location: {
                        code: 'LP007',
                    },
                },
            ];
            const expected = {
                status: { $eq: 'free' },
                'container.code': { $eq: 'ADC_CONTAINER_01' },
                'location.code': 'LP007',
            };
            // TODO: const expected = {$and:[{status:{$eq:'free'}},{'container.code':{$eq:'ADC_CONTAINER_01'}},{'location.code':'LP007'}]};
            const result = transformToLokiJsFilter(filter);
            xtremConsole.log(JSON.stringify(result));
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(5);
            expect(find[0].$loki).toBe(80);
            expect(find[0].status).toBe('free');
            expect(find[0].container.code).toBe('ADC_CONTAINER_01');
            expect(find[0].location.code).toBe('LP007');
        });

        it('standalone _and (implicit, multiple objects)', () => {
            const filter = [
                {
                    status: {
                        _eq: 'free',
                    },
                },
                {
                    container: {
                        code: { _eq: 'ADC_CONTAINER_01' },
                    },
                },
                {
                    location: {
                        code: 'LP007',
                    },
                },
            ];
            // TODO: const expected = {$and:[{status:{$eq:'free'}},{'container.code':{$eq:'ADC_CONTAINER_01'}},{'location.code':'LP007'}]};
            const expected = {
                status: { $eq: 'free' },
                'container.code': { $eq: 'ADC_CONTAINER_01' },
                'location.code': 'LP007',
            };
            // const expected = [{status:{$eq:'free'}},{'container.code':{$eq:'ADC_CONTAINER_01'}},{'location.code':'LP007'}];
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(5);
            expect(find[0].$loki).toBe(80);
            expect(find[0].status).toBe('free');
            expect(find[0].container.code).toBe('ADC_CONTAINER_01');
            expect(find[0].location.code).toBe('LP007');
        });

        it('standalone _and (single object)', () => {
            const filter = {
                _and: [
                    {
                        status: {
                            _eq: 'free',
                        },
                        container: {
                            code: { _eq: 'ADC_CONTAINER_01' },
                        },
                        location: {
                            code: 'LP007',
                        },
                    },
                ],
            };
            const expected = {
                $and: [
                    {
                        status: { $eq: 'free' },
                        'container.code': { $eq: 'ADC_CONTAINER_01' },
                        'location.code': 'LP007',
                    },
                ],
            };
            // TODO: const expected = {$and:[{status:{$eq:'free'}},{'container.code':{$eq:'ADC_CONTAINER_01'}},{'location.code':'LP007'}]};
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(5);
            expect(find[0].$loki).toBe(80);
            expect(find[0].status).toBe('free');
            expect(find[0].container.code).toBe('ADC_CONTAINER_01');
            expect(find[0].location.code).toBe('LP007');
        });

        it('standalone _and (multiple objects)', () => {
            const filter = {
                _and: [
                    {
                        status: {
                            _eq: 'free',
                        },
                    },
                    {
                        container: {
                            code: { _eq: 'ADC_CONTAINER_01' },
                        },
                    },
                    {
                        location: {
                            code: 'LP007',
                        },
                    },
                ],
            };
            const result = transformToLokiJsFilter(filter);
            const expected = {
                $and: [
                    { status: { $eq: 'free' } },
                    { 'container.code': { $eq: 'ADC_CONTAINER_01' } },
                    { 'location.code': 'LP007' },
                ],
            };
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(5);
            expect(find[0].$loki).toBe(80);
            expect(find[0].status).toBe('free');
            expect(find[0].container.code).toBe('ADC_CONTAINER_01');
            expect(find[0].location.code).toBe('LP007');
        });
    });

    describe('nulls', () => {
        it('null', () => {
            const filter = { status: null };
            const expected = { status: { $eq: null } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
        });

        it('null with operator', () => {
            const filter = { status: { _eq: null } };
            const expected = { status: { $eq: null } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
        });

        it('not null', () => {
            const filter = { status: { _ne: null } };
            const expected = { status: { $ne: null } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
        });

        it('null nested', () => {
            const filter = { location: { code: null } };
            const expected = { 'location.code': { $eq: null } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
        });

        it('null nested with operator _eq', () => {
            const filter = { location: { code: { _eq: null } } };
            const expected = { 'location.code': { $eq: null } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
        });

        it('null nested with operator _ne', () => {
            const filter = { location: { code: { _ne: null } } };
            const expected = { 'location.code': { $ne: null } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
        });

        it('null nested (_id)', () => {
            const filter = { location: { _id: null } };
            const expected = { location: { $empty: true } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
        });

        it('null nested (_id) with operator', () => {
            const filter = { location: { _id: { _eq: null } } };
            const expected = { location: { $empty: true } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
        });

        it('null in id _in', () => {
            const filter = { location: { _id: { _in: ['NA011~LP007', null] } } };
            const expected = { $or: [{ 'location._id': { $in: ['NA011~LP007'] } }, { location: { $empty: true } }] };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(8);
        });
    });

    describe('quantifiers', () => {
        it('_atMost', () => {
            const filter = { stock: { _atMost: 2 } };
            const expected = { '__restricted_Stock.query.totalCount': { $lte: 2 } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(47);
        });

        it('_atLeast', () => {
            const filter = { stock: { _atLeast: 90 } };
            const expected = { '__restricted_Stock.query.totalCount': { $gte: 90 } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(9);
        });

        it('_none is true', () => {
            const filter = { stock: { _none: true } };
            const expected = { '__restricted_Stock.query.totalCount': { $eq: 0 } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(34);
        });

        it('should exclude _none quantifier  with false value', () => {
            const filter = { stock: { _none: false } };
            const expected = {};
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(85);
        });

        it('should transform a query with _every qualifier filter with true value and use a relative filter', () => {
            const filter = { stock: { _every: true } };
            const expected = { '__restricted_Stock.query.totalCount': { $$gte: '__all_Stock.query.totalCount' } };
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(85);
        });

        it('should exclude _every qualifier filter with false', () => {
            const filter = { stock: { _every: false } };
            const expected = {};
            const result = transformToLokiJsFilter(filter);
            expect(result).toEqual(expected);
            const find = collection.find(result);
            expect(find.length).toBe(85);
        });
    });
});
