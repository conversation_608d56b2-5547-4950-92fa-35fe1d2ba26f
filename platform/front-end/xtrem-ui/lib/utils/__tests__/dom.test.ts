import type { ResponsiveProperties } from '../../component/abstract-ui-control-object';
import type { EditableFieldProperties } from '../../component/editable-field-control-object';
import type { ReduxResponsive } from '../../redux/state';
import type { PageArticleItem } from '../../service/layout-types';
import { getFieldClass, isHidden } from '../dom';

interface ITestCase {
    hiddenProperties?: Partial<PageArticleItem> | ResponsiveProperties;
    browser?: Partial<ReduxResponsive>;
    expectedValue: boolean;
}
interface IScenario {
    name: string;
    testCases: ITestCase[];
}
describe('DOM Utils', () => {
    const scenarios: IScenario[] = [
        {
            name: 'PageArticleItem',
            testCases: [
                {
                    hiddenProperties: { $isHiddenDesktop: true },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    hiddenProperties: { $isHiddenDesktop: false },
                    browser: {
                        is: { xs: false, s: false, m: true, l: false },
                        greaterThan: { xs: true, s: true, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    hiddenProperties: { $isHiddenMobile: true },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: true,
                },
                {
                    hiddenProperties: { $isHiddenMobile: false },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    // tested
                    hiddenProperties: { $isHiddenDesktop: true, $isHiddenMobile: true },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: true,
                },
                {
                    hiddenProperties: { $isHiddenDesktop: true, $isHiddenMobile: true },
                    browser: {
                        is: { xs: false, s: false, m: true, l: false },
                        greaterThan: { xs: true, s: true, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: true,
                },
                {
                    // tested
                    hiddenProperties: { $isHiddenDesktop: true, $isHiddenMobile: false },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    hiddenProperties: { $isHiddenDesktop: true, $isHiddenMobile: false },
                    browser: {
                        is: { xs: false, s: false, m: true, l: false },
                        greaterThan: { xs: true, s: true, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: true,
                },
                {
                    hiddenProperties: { $isHiddenDesktop: false, $isHiddenMobile: true },
                    browser: {
                        is: { xs: false, s: false, m: true, l: false },
                        greaterThan: { xs: true, s: true, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    hiddenProperties: { $isHiddenDesktop: false, $isHiddenMobile: true },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: true,
                },
                {
                    hiddenProperties: { $isHiddenDesktop: false, $isHiddenMobile: false },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    hiddenProperties: { $isHiddenDesktop: false, $isHiddenMobile: false },
                    browser: {
                        is: { xs: false, s: false, m: true, l: false },
                        greaterThan: { xs: true, s: true, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    hiddenProperties: { $isHiddenMobile: false },
                    expectedValue: false,
                },
                {
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
            ],
        },
        {
            name: 'ResponsiveProperties',
            testCases: [
                {
                    hiddenProperties: { isHiddenDesktop: true },
                    browser: {
                        is: { xs: false, s: false, m: true, l: false },
                        greaterThan: { xs: true, s: true, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: true,
                },
                {
                    hiddenProperties: { isHiddenDesktop: false },
                    browser: {
                        is: { xs: false, s: false, m: true, l: false },
                        greaterThan: { xs: true, s: true, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    hiddenProperties: { isHiddenMobile: true },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: true,
                },
                {
                    hiddenProperties: { isHiddenMobile: false },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    hiddenProperties: { isHiddenDesktop: true, isHiddenMobile: true },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: true,
                },
                {
                    hiddenProperties: { isHiddenDesktop: true, isHiddenMobile: true },
                    browser: {
                        is: { xs: false, s: false, m: true, l: false },
                        greaterThan: { xs: true, s: true, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: true,
                },
                {
                    hiddenProperties: { isHiddenDesktop: true, isHiddenMobile: false },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    hiddenProperties: { isHiddenDesktop: true, isHiddenMobile: false },
                    browser: {
                        is: { xs: false, s: false, m: true, l: false },
                        greaterThan: { xs: true, s: true, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: true,
                },
                {
                    hiddenProperties: { isHiddenDesktop: false, isHiddenMobile: true },
                    browser: {
                        is: { xs: false, s: false, m: true, l: false },
                        greaterThan: { xs: true, s: true, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    hiddenProperties: { isHiddenDesktop: false, isHiddenMobile: true },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: true,
                },
                {
                    hiddenProperties: { isHiddenDesktop: false, isHiddenMobile: false },
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    hiddenProperties: { isHiddenDesktop: false, isHiddenMobile: false },
                    browser: {
                        is: { xs: false, s: false, m: true, l: false },
                        greaterThan: { xs: true, s: true, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
                {
                    hiddenProperties: { isHiddenMobile: false },
                    expectedValue: false,
                },
                {
                    browser: {
                        is: { xs: true, s: false, m: false, l: false },
                        greaterThan: { xs: false, s: false, m: false, l: false },
                    } as ReduxResponsive,
                    expectedValue: false,
                },
            ],
        },
    ];

    const getTestDescriptionMessage = (testCase: ITestCase): string => {
        const hiddenPropertiesValues = () => {
            let message = '';

            const hiddenProperties = testCase && testCase.hiddenProperties;
            if (typeof hiddenProperties === 'object') {
                Object.keys(hiddenProperties).forEach(key => {
                    const value = hiddenProperties[key];
                    if (typeof value !== 'undefined') {
                        message += `and ${key} is ${value} `;
                    }
                });
            }

            return message;
        };

        return `It should return ${testCase.expectedValue} when ${
            testCase.hiddenProperties
                ? testCase.browser
                    ? `browser is ${testCase.browser!.is!.xs ? 'mobile ' : 'desktop '}${hiddenPropertiesValues()}`
                    : 'no browser is provided'
                : 'no properties are provided'
        }`;
    };

    /* eslint-disable no-restricted-syntax, no-prototype-builtins, @typescript-eslint/no-loop-func */
    for (const scenario of scenarios) {
        describe(scenario.name, () => {
            for (const testCase of scenario.testCases) {
                it(getTestDescriptionMessage(testCase), () => {
                    const result = isHidden(testCase.hiddenProperties, testCase.browser as ReduxResponsive);
                    expect(result).toBe(testCase.expectedValue);
                });
            }
        });
    }

    it('adds e-helper-text-hidden class to field with isHelperTextHidden property', () => {
        const fieldProperties: EditableFieldProperties = {
            isHelperTextHidden: true,
        };
        const className = getFieldClass('TestPage', null, '', fieldProperties);
        expect(className).toContain('e-helper-text-hidden');
    });

    it('adds e-title-hidden class to field with isTitleHidden property', () => {
        const fieldProperties: EditableFieldProperties = {
            isTitleHidden: true,
        };
        const className = getFieldClass('TestPage', null, '', fieldProperties);
        expect(className).toContain('e-title-hidden');
    });
});
