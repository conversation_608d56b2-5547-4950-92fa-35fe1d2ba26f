import { getMockPageMetadata } from '../../__tests__/test-helpers';
import { BlockControlObject } from '../../component/control-objects';
import { FieldKey } from '../../component/types';
import { standardDecoratorImplementation } from '../decorator-utils';
import * as pageMetadataImport from '../../service/page-metadata';
import * as targetPrototypeImport from '../decorator-utils';

describe('standardDecoratorImplementation', () => {
    let pageMetadata;
    class TestPage {}

    const pageName = TestPage.name;

    beforeEach(() => {
        pageMetadata = getMockPageMetadata(pageName);
        jest.spyOn(pageMetadataImport, 'getPageMetadata').mockImplementation(() => pageMetadata);
        jest.spyOn(targetPrototypeImport, 'getTargetPrototype').mockImplementation(ctor => ctor);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should return a function', () => {
        const properties = {};
        class DecoratorClass {}

        const result = standardDecoratorImplementation(properties, DecoratorClass as any, FieldKey.Numeric);

        expect(typeof result).toBe('function');
    });

    it('should push a function to pageMetadata.fieldThunks', () => {
        const properties = {};
        class DecoratorClass {}

        const func = standardDecoratorImplementation(properties, DecoratorClass as any, FieldKey.Numeric);
        func(TestPage as any, 'fieldId');
        expect(Object.keys(pageMetadata.fieldThunks).length).toBe(1);
        expect(typeof pageMetadata.fieldThunks.fieldId).toBe('function');
    });

    it('If Target page is a PageFragment we need to modify the decorator properties and set the same parent as fragmentFields', () => {
        const blockControlObject = new BlockControlObject({
            dispatchBlockValidation: jest.fn(),
            elementId: 'blockId',
            layout: {},
            screenId: 'screenId',
        });
        const properties = {} as any;
        class DecoratorClass {}
        pageMetadata.uiComponentProperties = {
            fragmentKey: {
                parent: blockControlObject,
            },
        };
        pageMetadata.definitionOrder = ['fragmentKey'];
        pageMetadata.fragmentFields = {
            [TestPage.name]: ['fragmentKey'],
        };

        const func = standardDecoratorImplementation(properties, DecoratorClass as any, FieldKey.Numeric);

        class PageFragment {}
        func(PageFragment as any, 'fieldId');
        expect(Object.keys(pageMetadata.fieldThunks).length).toBe(1);
        expect(typeof pageMetadata.fieldThunks.fieldId).toBe('function');
        expect(pageMetadata.definitionOrder).toEqual(['fragmentKey', 'fieldId']);
        expect(properties.parent).toBe(blockControlObject.parent);
    });
});
