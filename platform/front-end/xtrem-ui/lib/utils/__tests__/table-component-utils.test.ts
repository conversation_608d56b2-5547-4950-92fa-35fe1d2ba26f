import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../__tests__/test-helpers';
import type { SortOrder } from '../../component/field/table/table-component-types';
import { FieldKey } from '../../component/types';
import type { ColumnCompareType } from '../table-component-utils';
import { getOrderByFromSortModel, isColumnBindVariant, resolveCompareFunction } from '../table-component-utils';

describe('table component utils', () => {
    describe('isColumnBindVariant', () => {
        it('should match exact match', () => {
            expect(isColumnBindVariant('test', 'test')).toBe(true);
        });

        it('should not match different bind values', () => {
            expect(isColumnBindVariant('test', 'tset')).toBe(false);
        });

        it('should match bind matches with number postfixes', () => {
            expect(isColumnBindVariant('test', 'test_1')).toBe(true);
        });

        it('should match reference bind values', () => {
            expect(isColumnBindVariant('test', 'test__textField')).toBe(true);
        });

        it('should match reference bind values with number', () => {
            expect(isColumnBindVariant('test', 'test__textField_3')).toBe(true);
        });
    });
    describe('resolveCompareFunction', () => {
        const screenId = 'page';
        const elementId = '_id';
        let sortColumnsMock: jest.Mock;

        beforeEach(async () => {
            sortColumnsMock = jest.fn(() => 0);
            const mockState = getMockState();
            mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Table, mockState, screenId, elementId, {
                sortColumns: sortColumnsMock,
                columns: [],
            });
            getMockStore(mockState, true);
        });

        it('should sort columns based on sorting function', () => {
            const mockColumn1: ColumnCompareType = { bind: '_id', valueField: '_id' };
            const mockColumn2: ColumnCompareType = { bind: '_id', valueField: '_id' };
            sortColumnsMock.mockReturnValue(1);

            expect(resolveCompareFunction(screenId, sortColumnsMock, mockColumn1, mockColumn2)).toBe(1);
        });

        it('should call sorting function within the pages scope', () => {
            const mockColumn1: ColumnCompareType = { bind: '_id', valueField: '_id' };
            const mockColumn2: ColumnCompareType = { bind: '_id', valueField: '_id' };

            sortColumnsMock.mockImplementation(function Test(this: any) {
                expect(this).not.toBeNull();
                return 0;
            });

            expect(resolveCompareFunction(screenId, sortColumnsMock, mockColumn1, mockColumn2)).toBe(0);
            expect(sortColumnsMock).toHaveBeenCalled();
        });
    });

    describe('getOrderByFromSortModel', () => {
        it('Should return a object with 1 orderBy key for asc', () => {
            const sortModel = [{ colId: '_id', sort: 'asc' as SortOrder }];
            const orderBy = getOrderByFromSortModel(sortModel, []);
            expect(orderBy).toEqual({ _id: 1 });
        });
        it('Should return a object with -1 orderBy key for desc', () => {
            const sortModel = [{ colId: '_id', sort: 'desc' as SortOrder }];
            const orderBy = getOrderByFromSortModel(sortModel, []);
            expect(orderBy).toEqual({ _id: -1 });
        });
        it('Should remove underscore for colId', () => {
            const sortModel = [{ colId: 'product_1', sort: 'asc' as SortOrder }];
            const orderBy = getOrderByFromSortModel(sortModel, []);
            expect(orderBy).toEqual({ product: 1 });
        });
        it('Should manage duplication', () => {
            const sortModel = [
                { colId: 'product', sort: 'asc' as SortOrder },
                { colId: 'product', sort: 'asc' as SortOrder },
                { colId: 'product_1', sort: 'asc' as SortOrder },
            ];
            const orderBy = getOrderByFromSortModel(sortModel, []);
            expect(orderBy).toEqual({ product: 1 });
        });
        it('Should generate a reference orderBy', () => {
            const sortModel = [{ colId: 'product', sort: 'asc' as SortOrder }];
            const columns = [{ type: FieldKey.Reference, properties: { bind: 'product', valueField: 'textField' } }];
            const orderBy = getOrderByFromSortModel(sortModel, columns);
            expect(orderBy).toEqual({ product: { textField: 1 } });
        });
        it('Should manage duplication with reference orderBy', () => {
            const sortModel = [{ colId: 'product_1', sort: 'desc' as SortOrder }];
            const columns = [
                { type: FieldKey.Text, properties: { bind: 'product' } },
                { type: FieldKey.Reference, properties: { bind: 'product', valueField: 'textField' } },
                { type: FieldKey.Numeric, properties: { bind: 'product' } },
            ];
            const orderBy = getOrderByFromSortModel(sortModel, columns);
            expect(orderBy).toEqual({ product: { textField: -1 } });
        });
    });
});
