/* eslint-disable react/jsx-indent */
import { addFieldToState, getMockState, getMockStore } from '../../__tests__/test-helpers/mock-store-helpers';

import React from 'react';
import { ClientError } from '@sage/xtrem-client';
import { getMockPageDefinition } from '../../__tests__/test-helpers';
import { FieldKey } from '../../component/types';
import type { ScreenBase } from '../../service/screen-base';
import * as dispatchService from '../../service/dispatch-service';
import { transformServerErrorToToast } from '../server-error-transformer';
import type { XtremAppState } from '../../redux';
import { CollectionValue } from '../../service/collection-data-service';
import { nestedFields } from '../..';
import { CollectionFieldTypes } from '../../service/collection-data-types';
import { GraphQLKind, GraphQLTypes } from '../../types';
import { Provider } from 'react-redux';
import type { Store } from 'redux';
import { render } from '@testing-library/react';

describe('server error transformer', () => {
    const screenId = 'TestScreen';
    let screenBase: ScreenBase;
    let state: XtremAppState;
    let mockStore: Store;
    let dispatchFieldValidationMock: jest.SpyInstance;

    beforeEach(() => {
        state = getMockState();
        state.nodeTypes = {
            AnyNode: {
                name: 'AnyNode',
                title: 'Any Node',
                packageName: '@sage/xtrem-test',
                properties: {
                    _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                    _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                    _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                    anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    field1: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    field2: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    field3: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    field4: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    field5: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    someOtherBind: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    anotherField: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
                    child: { type: 'ChildNode', kind: 'LIST' },
                    tableField: { type: 'NestedGridExampleLevelOne', kind: 'LIST' },
                    nestedGridField: { type: 'NestedGridExampleLevelOne', kind: 'LIST' },
                },
                mutations: {},
            },
            ChildNode: {
                name: 'AnyNode',
                title: 'Any Node',
                packageName: '@sage/xtrem-test',
                properties: {
                    _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                    _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                    _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                    someSecondLevelField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                },
                mutations: {},
            },
            NestedGridExampleLevelOne: {
                name: 'AnyNode',
                title: 'Any Node',
                packageName: '@sage/xtrem-test',
                properties: {
                    _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                    _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                    _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                    anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    anotherField: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
                    secondLevelItems: { type: 'NestedGridExampleLevelTwo', kind: 'LIST' },
                },
                mutations: {},
            },
            NestedGridExampleLevelTwo: {
                name: 'AnyNode',
                title: 'Any Node',
                packageName: '@sage/xtrem-test',
                properties: {
                    _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                    _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                    _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                    someSecondLevelField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                },
                mutations: {},
            },
        };
        dispatchFieldValidationMock = jest.spyOn(dispatchService, 'dispatchFieldValidation');
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] = {
            node: '@sage/xtrem-test/AnyNode',
        } as any;
        addFieldToState(FieldKey.Text, state, screenId, 'field1', {
            title: 'Field Nr 1',
        });
        addFieldToState(FieldKey.Text, state, screenId, 'field2', {
            title: 'Field Nr 2',
        });
        addFieldToState(FieldKey.Text, state, screenId, 'field3', {
            title: 'Field Nr 3',
            bind: 'someOtherBind',
        });
        addFieldToState(FieldKey.Text, state, screenId, 'field4', {
            title: () => 'Field Nr 4',
        });
        addFieldToState(FieldKey.Text, state, screenId, 'field5', {});
        addFieldToState(FieldKey.Text, state, screenId, 'field6', {
            title: 'Deep bound field title',
            bind: { originAddress: { addressName: true } },
        });
        mockStore = getMockStore(state);
        screenBase = {
            _pageMetadata: state.screenDefinitions[screenId].metadata,
        } as any;
    });

    afterEach(() => {
        dispatchFieldValidationMock.mockClear();
    });

    it('should transform basic server side error with no field errors', async () => {
        const response = {
            errors: [
                {
                    message: 'Validation failed',
                    locations: [{ line: 4, column: 13 }],
                    path: ['xtremShowCase', 'showCaseProduct', 'update'],
                    extensions: {
                        code: 'business-rule-error',
                        diagnoses: [
                            { severity: 1, path: [], message: 'product validation successful' },
                            { severity: 3, path: [], message: 'Some global error' },
                        ],
                    },
                },
            ],
            data: { xtremShowCase: { showCaseProduct: { update: null } } },
            extensions: {
                diagnoses: [
                    { severity: 1, path: [], message: 'product validation successful' },
                    { severity: 3, path: [], message: 'Some global error' },
                ],
            },
        };
        const error = new ClientError(response.errors);
        const result = await transformServerErrorToToast(error, screenBase);
        expect(
            render(<Provider store={mockStore}>{(result.globalError as any)?.toastContent}</Provider>).container
                .innerHTML,
        ).toEqual(
            render(
                <div className="e-page-validation-error-list">
                    <h6>Validation Errors</h6>
                    <ul>
                        <li>Some global error</li>
                    </ul>
                </div>,
            ).container.innerHTML,
        );
        expect(Object.keys(result.validationErrors)).toHaveLength(0);
    });

    it('should map error back to a field', async () => {
        const response = {
            errors: [
                {
                    message: 'Validation failed',
                    locations: [{ line: 4, column: 13 }],
                    path: ['xtremShowCase', 'showCaseProduct', 'update'],
                    extensions: {
                        code: 'business-rule-error',
                        diagnoses: [
                            { severity: 3, path: ['field1'], message: 'Value must not be negative.' },
                            { severity: 1, path: [], message: 'product validation successful' },
                        ],
                    },
                },
            ],
            data: { xtremShowCase: { showCaseProduct: { update: null } } },
            extensions: {
                diagnoses: [
                    { severity: 3, path: ['field1'], message: 'Value must not be negative.' },
                    { severity: 1, path: [], message: 'product validation successful' },
                ],
            },
        };
        const error = new ClientError(response.errors);
        const result = await transformServerErrorToToast(error, screenBase);
        expect(
            render(<Provider store={mockStore}>{(result.globalError as any)?.toastContent}</Provider>).container
                .innerHTML,
        ).toEqual(
            render(
                <div className="e-page-validation-error-list">
                    <h6>Validation Errors</h6>
                    <ul>
                        <li>
                            <p>
                                <strong>Field Nr 1:</strong>
                                &#32;Value must not be negative.
                            </p>
                        </li>
                    </ul>
                </div>,
            ).container.innerHTML,
        );
        expect(Object.keys(result.validationErrors)).toEqual(['field1']);
        expect(result.validationErrors.field1).toContainEqual({
            screenId,
            elementId: 'field1',
            message: 'Value must not be negative.',
            validationRule: 'server-business-rule-error',
        });
    });

    it('should map errors back to multiple fields', async () => {
        const response = {
            errors: [
                {
                    message: 'Validation failed',
                    locations: [{ line: 4, column: 13 }],
                    path: ['xtremShowCase', 'showCaseProduct', 'update'],
                    extensions: {
                        code: 'business-rule-error',
                        diagnoses: [
                            { severity: 3, path: ['field2'], message: 'Value must not be less than 2020-08-03.' },
                            { severity: 3, path: ['field1'], message: 'Value must not be negative.' },
                            { severity: 1, path: [], message: 'product validation successful' },
                        ],
                    },
                },
            ],
            data: { xtremShowCase: { showCaseProduct: { update: null } } },
            extensions: {
                diagnoses: [
                    { severity: 3, path: ['field2'], message: 'Value must not be less than 2020-08-03.' },
                    { severity: 3, path: ['field1'], message: 'Value must not be negative.' },
                    { severity: 1, path: [], message: 'product validation successful' },
                ],
            },
        };
        const error = new ClientError(response.errors);
        const result = await transformServerErrorToToast(error, screenBase);
        expect(
            render(<Provider store={mockStore}>{(result.globalError as any)?.toastContent}</Provider>).container
                .innerHTML,
        ).toEqual(
            render(
                <div className="e-page-validation-error-list">
                    <h6>Validation Errors</h6>
                    <ul>
                        <li>
                            <p>
                                <strong>Field Nr 2:</strong>
                                &#32;Value must not be less than 2020-08-03.
                            </p>
                        </li>
                        <li>
                            <p>
                                <strong>Field Nr 1:</strong>
                                &#32;Value must not be negative.
                            </p>
                        </li>
                    </ul>
                </div>,
            ).container.innerHTML,
        );
        expect(Object.keys(result.validationErrors)).toEqual(['field2', 'field1']);
        expect(result.validationErrors.field1).toContainEqual({
            screenId,
            elementId: 'field1',
            message: 'Value must not be negative.',
            validationRule: 'server-business-rule-error',
        });
        expect(result.validationErrors.field2).toContainEqual({
            screenId,
            elementId: 'field2',
            message: 'Value must not be less than 2020-08-03.',
            validationRule: 'server-business-rule-error',
        });
    });

    it('should map error back to a field using callback title function', async () => {
        const response = {
            errors: [
                {
                    message: 'Validation failed',
                    locations: [{ line: 4, column: 13 }],
                    path: ['xtremShowCase', 'showCaseProduct', 'update'],
                    extensions: {
                        code: 'business-rule-error',
                        diagnoses: [
                            { severity: 3, path: ['field4'], message: 'Value must not be negative.' },
                            { severity: 1, path: [], message: 'product validation successful' },
                        ],
                    },
                },
            ],
            data: { xtremShowCase: { showCaseProduct: { update: null } } },
            extensions: {
                diagnoses: [
                    { severity: 3, path: ['field4'], message: 'Value must not be negative.' },
                    { severity: 1, path: [], message: 'product validation successful' },
                ],
            },
        };
        const error = new ClientError(response.errors);
        const result = await transformServerErrorToToast(error, screenBase);
        expect(
            render(<Provider store={mockStore}>{(result.globalError as any)?.toastContent}</Provider>).container
                .innerHTML,
        ).toEqual(
            render(
                <div className="e-page-validation-error-list">
                    <h6>Validation Errors</h6>
                    <ul>
                        <li>
                            <p>
                                <strong>Field Nr 4:</strong>
                                &#32;Value must not be negative.
                            </p>
                        </li>
                    </ul>
                </div>,
            ).container.innerHTML,
        );
        expect(Object.keys(result.validationErrors)).toEqual(['field4']);
        expect(result.validationErrors.field4).toContainEqual({
            screenId,
            elementId: 'field4',
            message: 'Value must not be negative.',
            validationRule: 'server-business-rule-error',
        });
    });

    it('should map error back to a field based on the bind property', async () => {
        const response = {
            errors: [
                {
                    message: 'Validation failed',
                    locations: [{ line: 4, column: 13 }],
                    path: ['xtremShowCase', 'showCaseProduct', 'update'],
                    extensions: {
                        code: 'business-rule-error',
                        diagnoses: [
                            { severity: 3, path: ['someOtherBind'], message: 'Value must not be negative.' },
                            { severity: 1, path: [], message: 'product validation successful' },
                        ],
                    },
                },
            ],
            data: { xtremShowCase: { showCaseProduct: { update: null } } },
            extensions: {
                diagnoses: [
                    { severity: 3, path: ['someOtherBind'], message: 'Value must not be negative.' },
                    { severity: 1, path: [], message: 'product validation successful' },
                ],
            },
        };
        const error = new ClientError(response.errors);
        const result = await transformServerErrorToToast(error, screenBase);
        expect(
            render(<Provider store={mockStore}>{(result.globalError as any)?.toastContent}</Provider>).container
                .innerHTML,
        ).toEqual(
            render(
                <div className="e-page-validation-error-list">
                    <h6>Validation Errors</h6>
                    <ul>
                        <li>
                            <p>
                                <strong>Field Nr 3:</strong>
                                &#32;Value must not be negative.
                            </p>
                        </li>
                    </ul>
                </div>,
            ).container.innerHTML,
        );
        expect(Object.keys(result.validationErrors)).toEqual(['field3']);
        expect(result.validationErrors.field3).toContainEqual({
            screenId,
            elementId: 'field3',
            message: 'Value must not be negative.',
            validationRule: 'server-business-rule-error',
        });
    });

    it('should map error back to a field when there are deep-bound properties', async () => {
        const response = {
            errors: [
                {
                    message: 'Validation failed',
                    locations: [{ line: 4, column: 13 }],
                    path: ['xtremShowCase', 'showCaseProduct', 'update'],
                    extensions: {
                        code: 'business-rule-error',
                        diagnoses: [
                            {
                                severity: 3,
                                path: ['field6', 'name'],
                                message: 'Enter a value greater than 5 in the field.',
                            },
                        ],
                    },
                },
            ],
            data: { xtremShowCase: { showCaseProduct: { update: null } } },
            extensions: {
                diagnoses: [
                    { severity: 3, path: ['field6', 'name'], message: 'Enter a value greater than 5 in the field.' },
                ],
            },
        };
        const error = new ClientError(response.errors);
        const result = await transformServerErrorToToast(error, screenBase);
        expect(
            render(<Provider store={mockStore}>{(result.globalError as any)?.toastContent}</Provider>).container
                .innerHTML,
        ).toEqual(
            render(
                <div className="e-page-validation-error-list">
                    <h6>Validation Errors</h6>
                    <ul>
                        <li>
                            <p>
                                <strong>Deep bound field title:</strong>
                                &#32;Enter a value greater than 5 in the field.
                            </p>
                        </li>
                    </ul>
                </div>,
            ).container.innerHTML,
        );
        expect(Object.keys(result.validationErrors)).toEqual(['field6']);
        expect(result.validationErrors.field6).toContainEqual({
            screenId,
            elementId: 'field6',
            message: 'Enter a value greater than 5 in the field.',
            validationRule: 'server-business-rule-error',
        });
    });

    it('should use the elementId when no label is available', async () => {
        const response = {
            errors: [
                {
                    message: 'Validation failed',
                    locations: [{ line: 4, column: 13 }],
                    path: ['xtremShowCase', 'showCaseProduct', 'update'],
                    extensions: {
                        code: 'business-rule-error',
                        diagnoses: [
                            { severity: 3, path: ['field5'], message: 'Value must not be negative.' },
                            { severity: 1, path: [], message: 'product validation successful' },
                        ],
                    },
                },
            ],
            data: { xtremShowCase: { showCaseProduct: { update: null } } },
            extensions: {
                diagnoses: [
                    { severity: 3, path: ['field5'], message: 'Value must not be negative.' },
                    { severity: 1, path: [], message: 'product validation successful' },
                ],
            },
        };
        const error = new ClientError(response.errors);
        const result = await transformServerErrorToToast(error, screenBase);
        expect(
            render(<Provider store={mockStore}>{(result.globalError as any)?.toastContent}</Provider>).container
                .innerHTML,
        ).toEqual(
            render(
                <div className="e-page-validation-error-list">
                    <h6>Validation Errors</h6>
                    <ul>
                        <li>
                            <p>
                                <strong>field5:</strong>
                                &#32;Value must not be negative.
                            </p>
                        </li>
                    </ul>
                </div>,
            ).container.innerHTML,
        );
        expect(Object.keys(result.validationErrors)).toEqual(['field5']);
        expect(result.validationErrors.field5).toContainEqual({
            screenId,
            elementId: 'field5',
            message: 'Value must not be negative.',
            validationRule: 'server-business-rule-error',
        });
    });

    it('should add errors that are not related to any field', async () => {
        const response = {
            errors: [
                {
                    message: 'Validation failed',
                    locations: [{ line: 4, column: 13 }],
                    path: ['xtremShowCase', 'showCaseProduct', 'update'],
                    extensions: {
                        code: 'business-rule-error',
                        diagnoses: [
                            { severity: 3, path: ['random'], message: 'Value must not be negative.' },
                            { severity: 1, path: [], message: 'product validation successful' },
                        ],
                    },
                },
            ],
            data: { xtremShowCase: { showCaseProduct: { update: null } } },
            extensions: {
                diagnoses: [
                    { severity: 3, path: ['random'], message: 'Value must not be negative.' },
                    { severity: 1, path: [], message: 'product validation successful' },
                ],
            },
        };
        const error = new ClientError(response.errors);
        const result = await transformServerErrorToToast(error, screenBase);
        expect(
            render(<Provider store={mockStore}>{(result.globalError as any)?.toastContent}</Provider>).container
                .innerHTML,
        ).toEqual(
            render(
                <div className="e-page-validation-error-list">
                    <h6>Validation Errors</h6>
                    <ul>
                        <li>Value must not be negative.</li>
                    </ul>
                </div>,
            ).container.innerHTML,
        );
        expect(Object.keys(result.validationErrors)).toEqual([]);
    });

    describe('collection nested validation errors - nested grid', () => {
        let collectionValue: CollectionValue;
        const elementId = 'nestedGridField';
        beforeEach(() => {
            const columns1 = [
                nestedFields.text<any, any>({ bind: '_id' }),
                nestedFields.text<any, any>({ bind: 'anyField', title: 'Field title' }),
                nestedFields.numeric<any, any>({ bind: 'someOtherField', title: () => 'Field title with callback' }),
            ];
            const columns2 = [
                nestedFields.text<any, any>({ bind: '_id' }),
                nestedFields.text<any, any>({ bind: 'someSecondLevelField', title: 'Second level title' }),
            ];

            getMockStore(state);

            collectionValue = new CollectionValue({
                screenId,
                elementId,
                isTransient: false,
                hasNextPage: true,
                orderBy: [{ anyField: 1 }],
                columnDefinitions: [columns1, columns2],
                nodeTypes: state.nodeTypes,
                nodes: ['@sage/xtrem-test/NestedGridExampleLevelOne', '@sage/xtrem-test/NestedGridExampleLevelTwo'],
                filter: [undefined],
                initialValues: [
                    { _id: '1', anyField: 'test', someOtherField: 4 },
                    { _id: '2', anyField: 'test string 2', someOtherField: -5 },
                    { _id: '3', anyField: 'test aasd', someOtherField: 32432 },
                ],
                fieldType: CollectionFieldTypes.DESKTOP_TABLE,
            });

            addFieldToState(
                FieldKey.NestedGrid,
                state,
                screenId,
                elementId,
                {
                    levels: [
                        {
                            columns: columns1,
                            node: '@sage/xtrem-test/NestedGridExampleLevelOne',
                            childProperty: 'secondLevelItems',
                        },
                        { columns: columns2, node: '@sage/xtrem-test/NestedGridExampleLevelTwo' },
                    ],
                    title: 'My Test table',
                },
                collectionValue,
            );
        });

        it('should add nested errors to collection value for the first level', async () => {
            const addErrorsSpy = jest.spyOn(collectionValue, 'addValidationErrors');

            const response = {
                errors: [
                    {
                        message: 'Validation failed',
                        locations: [{ line: 4, column: 13 }],
                        path: ['xtremTest', 'anyNode', 'update'],
                        extensions: {
                            code: 'business-rule-error',
                            diagnoses: [
                                {
                                    severity: 3,
                                    path: ['nestedGridField', '1', 'anyField'],
                                    message: 'Value must be greater than 1.',
                                },
                                {
                                    severity: 3,
                                    path: ['nestedGridField', '2', 'someOtherField'],
                                    message: 'Stuff.',
                                },
                                {
                                    severity: 3,
                                    path: ['nestedGridField', '3', 'anotherField'],
                                    message: 'Some exception',
                                },
                            ],
                        },
                    },
                ],
                data: { xtremTest: { anyNode: { update: null } } },
                extensions: { diagnoses: [] },
            };
            const error = new ClientError(response.errors);
            expect(dispatchFieldValidationMock).not.toHaveBeenCalled();
            expect(addErrorsSpy).not.toHaveBeenCalled();
            const result = await transformServerErrorToToast(error, screenBase);
            expect(addErrorsSpy).toHaveBeenCalledWith({
                validationErrors: [
                    {
                        bind: 'nestedGridField',
                        level: undefined,
                        columnId: 'anyField',
                        elementId: 'nestedGridField',
                        message: 'Value must be greater than 1.',
                        recordId: '1',
                        screenId: 'TestScreen',
                        validationRule: 'server-business-rule-error',
                    },
                    {
                        bind: 'nestedGridField',
                        level: undefined,
                        columnId: 'someOtherField',
                        elementId: 'nestedGridField',
                        message: 'Stuff.',
                        recordId: '2',
                        screenId: 'TestScreen',
                        validationRule: 'server-business-rule-error',
                    },
                    {
                        bind: 'nestedGridField',
                        level: undefined,
                        columnId: 'anotherField',
                        elementId: 'nestedGridField',
                        message: 'Some exception',
                        recordId: '3',
                        screenId: 'TestScreen',
                        validationRule: 'server-business-rule-error',
                    },
                ],
                shouldNotifySubscribers: true,
            });
            expect(result.validationErrors).toEqual({});
            expect(dispatchFieldValidationMock).toHaveBeenCalledWith(screenId, 'nestedGridField', [
                {
                    columnId: 'anyField',
                    elementId: 'nestedGridField',
                    message: 'Value must be greater than 1.',
                    messagePrefix: '_id: 1 - Field title',
                    recordId: '1',
                    screenId,
                    validationRule: 'server-business-rule-error',
                },
                {
                    columnId: 'anotherField',
                    elementId: 'nestedGridField',
                    message: 'Some exception',
                    messagePrefix: '_id: 3 - anotherField',
                    recordId: '3',
                    screenId,
                    validationRule: 'server-business-rule-error',
                },
                {
                    columnId: 'someOtherField',
                    elementId: 'nestedGridField',
                    message: 'Stuff.',
                    messagePrefix: '_id: 2 - Field title with callback',
                    recordId: '2',
                    screenId,
                    validationRule: 'server-business-rule-error',
                },
            ]);
        });

        it('should add nested errors to collection value for the second level', async () => {
            const addErrorsSpy = jest.spyOn(collectionValue, 'addValidationErrors');

            const response = {
                errors: [
                    {
                        message: 'Validation failed',
                        locations: [{ line: 4, column: 13 }],
                        path: ['xtremTest', 'anyNode', 'update'],
                        extensions: {
                            code: 'business-rule-error',
                            diagnoses: [
                                {
                                    severity: 3,
                                    path: ['nestedGridField', '1', 'secondLevelItems', '2', 'someSecondLevelField'],
                                    message: 'Value must not be less than 0.',
                                },
                            ],
                        },
                    },
                ],
                data: { xtremShowCase: { showCaseCustomer: { update: null } } },
                extensions: { diagnoses: [] },
            };
            const error = new ClientError(response.errors);
            expect(addErrorsSpy).not.toHaveBeenCalled();
            expect(dispatchFieldValidationMock).not.toHaveBeenCalled();
            const result = await transformServerErrorToToast(error, screenBase);
            expect(addErrorsSpy).toHaveBeenCalledWith({
                validationErrors: [
                    {
                        bind: 'nestedGridField',
                        columnId: 'someSecondLevelField',
                        elementId: 'nestedGridField',
                        message: 'Value must not be less than 0.',
                        recordId: '2',
                        level: 1,
                        screenId: 'TestScreen',
                        validationRule: 'server-business-rule-error',
                    },
                ],
                shouldNotifySubscribers: true,
            });
            expect(result.validationErrors).toEqual({});
            expect(dispatchFieldValidationMock).toHaveBeenCalledWith(screenId, 'nestedGridField', [
                {
                    columnId: 'someSecondLevelField',
                    elementId: 'nestedGridField',
                    level: 1,
                    message: 'Value must not be less than 0.',
                    messagePrefix: '_id: 2 - someSecondLevelField',
                    recordId: '2',
                    screenId,
                    validationRule: 'server-business-rule-error',
                },
            ]);
        });
    });

    describe('collection nested validation errors - table', () => {
        let collectionValue: CollectionValue;
        const elementId = 'tableField';
        beforeEach(() => {
            const columns = [
                nestedFields.text<any, any>({ bind: '_id' }),
                nestedFields.text<any, any>({ bind: 'anyField', title: 'Field title' }),
                nestedFields.numeric<any, any>({ bind: 'someOtherField', title: () => 'Field title with callback' }),
            ];

            mockStore = getMockStore(state);

            collectionValue = new CollectionValue({
                screenId,
                elementId,
                isTransient: false,
                hasNextPage: true,
                orderBy: [{ anyField: 1 }],
                columnDefinitions: [columns],
                nodeTypes: state.nodeTypes,
                nodes: ['@sage/xtrem-test/AnyNode'],
                filter: [undefined],
                initialValues: [
                    { _id: '1', anyField: 'test', someOtherField: 4 },
                    { _id: '2', anyField: 'test string 2', someOtherField: -5 },
                    { _id: '3', anyField: 'test aasd', someOtherField: 32432 },
                ],
                fieldType: CollectionFieldTypes.DESKTOP_TABLE,
            });

            addFieldToState(
                FieldKey.Table,
                state,
                screenId,
                elementId,
                { columns, title: 'My Test table' },
                collectionValue,
            );
        });

        it('should add nested errors to collection value', async () => {
            const addErrorsSpy = jest.spyOn(collectionValue, 'addValidationErrors');

            const response = {
                errors: [
                    {
                        message: 'Validation failed',
                        locations: [{ line: 4, column: 13 }],
                        path: ['xtremTest', 'anyNode', 'update'],
                        extensions: {
                            code: 'business-rule-error',
                            diagnoses: [
                                {
                                    severity: 3,
                                    path: ['tableField', '1', 'anyField'],
                                    message: 'Value must be greater than 1.',
                                },
                                {
                                    severity: 3,
                                    path: ['tableField', '2', 'someOtherField'],
                                    message: 'Stuff.',
                                },
                                {
                                    severity: 3,
                                    path: ['tableField', '3', 'anotherField'],
                                    message: 'Some exception',
                                },
                            ],
                        },
                    },
                ],
                data: { xtremTest: { anyNode: { update: null } } },
                extensions: { diagnoses: [] },
            };
            const error = new ClientError(response.errors);
            expect(addErrorsSpy).not.toHaveBeenCalled();
            expect(dispatchFieldValidationMock).not.toHaveBeenCalled();
            const result = await transformServerErrorToToast(error, screenBase);
            expect(addErrorsSpy).toHaveBeenCalledWith({
                validationErrors: [
                    {
                        bind: 'tableField',
                        level: undefined,
                        columnId: 'anyField',
                        elementId: 'tableField',
                        message: 'Value must be greater than 1.',
                        recordId: '1',
                        screenId: 'TestScreen',
                        validationRule: 'server-business-rule-error',
                    },
                    {
                        bind: 'tableField',
                        level: undefined,
                        columnId: 'someOtherField',
                        elementId: 'tableField',
                        message: 'Stuff.',
                        recordId: '2',
                        screenId: 'TestScreen',
                        validationRule: 'server-business-rule-error',
                    },
                    {
                        bind: 'tableField',
                        level: undefined,
                        columnId: 'anotherField',
                        elementId: 'tableField',
                        message: 'Some exception',
                        recordId: '3',
                        screenId: 'TestScreen',
                        validationRule: 'server-business-rule-error',
                    },
                ],
                shouldNotifySubscribers: true,
            });
            expect(result.validationErrors).toEqual({});
            expect(dispatchFieldValidationMock).toHaveBeenCalled();
            expect(dispatchFieldValidationMock).toHaveBeenCalledWith(screenId, 'tableField', [
                {
                    columnId: 'anyField',
                    elementId: 'tableField',
                    message: 'Value must be greater than 1.',
                    messagePrefix: '_id: 1 - Field title',
                    recordId: '1',
                    screenId,
                    validationRule: 'server-business-rule-error',
                },
                {
                    columnId: 'anotherField',
                    elementId: 'tableField',
                    message: 'Some exception',
                    messagePrefix: '_id: 3 - anotherField',
                    recordId: '3',
                    screenId,
                    validationRule: 'server-business-rule-error',
                },
                {
                    columnId: 'someOtherField',
                    elementId: 'tableField',
                    message: 'Stuff.',
                    messagePrefix: '_id: 2 - Field title with callback',
                    recordId: '2',
                    screenId,
                    validationRule: 'server-business-rule-error',
                },
            ]);
        });
    });
});
