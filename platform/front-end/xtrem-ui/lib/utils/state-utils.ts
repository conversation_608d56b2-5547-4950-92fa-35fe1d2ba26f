import { objectKeys, type Dict } from '@sage/xtrem-shared';
import { FieldKey } from '@sage/xtrem-shared';
import { navigationPanelId } from '../component/container/navigation-panel/navigation-panel-types';
import type { PageNavigationPanel, PageProperties } from '../component/container/page/page-types';
import type {
    InternalSectionProperties,
    NestedGridProperties,
    PageControlObject,
    TableProperties,
} from '../component/control-objects';
import { AbstractUiControlObject, SectionControlObject } from '../component/control-objects';
import type { PageDecoratorProperties, TableDecoratorProperties } from '../component/decorator-properties';
import * as xtremRedux from '../redux';
import type { NavigationPanelState, XtremAppState } from '../redux/state';
import type { CollectionValue } from '../service/collection-data-service';
import type { Page } from '../service/page';
import type { PageDefinition } from '../service/page-definition';
import type { ScreenBaseDefinition } from '../service/screen-base-definition';
import { xtremConsole } from './console';
import { convertDeepBindToPathNotNull } from './nested-field-utils';
import { resolveByValue } from './resolve-value-utils';
import type { MakeRequired, ValueOrCallback } from './types';
import type { TableSidebarDialogContent } from '../types/dialogs';
import type { SidebarSectionDefinition } from '../component/table-sidebar/table-sidebar-types';
import { getCustomFields } from '../service/customization-service';
import { mergeIntoSidebarLayout } from '../component/abstract-decorator-utils';
import type { FormattedNodeDetails } from '../service/metadata-types';
import { getParentSection } from './abstract-fields-utils';
import { isEqual } from 'lodash';

export const isScreenDefinitionDirty = (screenDefinition: ScreenBaseDefinition): boolean =>
    !(
        screenDefinition.metadata.uiComponentProperties[
            screenDefinition.metadata.screenId
        ] as PageDecoratorProperties<any>
    ).skipDirtyCheck &&
    !!objectKeys(screenDefinition.dirtyStates).find(key => screenDefinition.dirtyStates[key] === true) &&
    screenDefinition.type !== 'sticker';

export const hasAnyDirtyScreenDefinitions = (state: XtremAppState): boolean =>
    !!objectKeys(state.screenDefinitions).find(screenId =>
        isScreenDefinitionDirty(state.screenDefinitions[screenId]),
    ) ||
    !!Object.values(state.dashboard.dashboardGroups).find(
        group => group.dashboardEditor.isDirty || group.widgetEditor.isDirty,
    ) ||
    isSidebarDirty(state);

export const getPageDefinitionFromState = (
    screenId: string,
    state = xtremRedux.getStore().getState(),
): PageDefinition => {
    const pageDefinition = getPageDefinition(screenId, state);
    if (!pageDefinition) {
        throw new Error(`No page definition found: ${screenId}`);
    }
    return pageDefinition;
};

export const getPageDefinition = (
    screenId: string,
    state = xtremRedux.getStore().getState(),
): PageDefinition | undefined => {
    return state.screenDefinitions[screenId] as PageDefinition | undefined;
};

export const getMainPageDefinitionFromState = (state = xtremRedux.getStore().getState()): PageDefinition | undefined =>
    objectKeys(state.screenDefinitions)
        .filter(key => state.screenDefinitions[key].type === 'page')
        .map(key => state.screenDefinitions[key] as PageDefinition)
        .find(definition => definition.isMainPage);

export const getPagePropertiesFromState = (
    screenId: string,
    state = xtremRedux.getStore().getState(),
): PageProperties & PageDecoratorProperties<Page> => {
    const properties = getPageDefinitionFromState(screenId, state).metadata.uiComponentProperties[
        screenId
    ] as PageProperties & PageDecoratorProperties<Page>;

    if (!properties) {
        xtremConsole.error(getPageDefinitionFromState(screenId, state));
        throw new Error(`No properties found for page: ${screenId}`);
    }

    return properties;
};

export const getPageControlObjectFromState = (
    screenId: string,
    state = xtremRedux.getStore().getState(),
): PageControlObject | null =>
    (getPageDefinitionFromState(screenId, state).metadata.controlObjects[screenId] as PageControlObject) || null;

export const getPagePropertiesFromPageDefinition = (
    pageDefinition: PageDefinition,
): PageProperties & PageDecoratorProperties<Page> =>
    pageDefinition.metadata.uiComponentProperties[pageDefinition.metadata.screenId] as PageProperties &
        PageDecoratorProperties<Page>;

export const getNavigationPanelState = (
    screenId: string,
    state = xtremRedux.getStore().getState(),
): NavigationPanelState | null => (state.screenDefinitions[screenId] as PageDefinition)?.navigationPanel || null;

export const getNavigationPanelDefinitionFromState = (
    screenId: string,
    state = xtremRedux.getStore().getState(),
): PageNavigationPanel<any> | null => getPagePropertiesFromState(screenId, state).navigationPanel || null;

export const getNavigationPanelTablePropertiesFromPageDefinition = (
    pageDefinition: PageDefinition,
    isDefaultProps = false,
): TableDecoratorProperties =>
    pageDefinition.metadata[isDefaultProps ? 'defaultUiComponentProperties' : 'uiComponentProperties'][
        navigationPanelId
    ] as TableDecoratorProperties;

export const checkIfPageIsLoaded = (screenId: string, state = xtremRedux.getStore().getState()): void => {
    if (!getPageDefinitionFromState(screenId, state)) {
        throw new Error(`${screenId} page has already been destroyed, further operations are not allowed.`);
    }
};

export function isFieldDataLoaded(pageDefinition: ScreenBaseDefinition, elementId: string): boolean {
    const parentSection = getParentSection(pageDefinition, elementId);
    if (parentSection) {
        const sectionProperties = pageDefinition.metadata.uiComponentProperties[
            parentSection
        ] as InternalSectionProperties;
        return !sectionProperties.isLazyLoaded || !!sectionProperties.isLoaded;
    }
    return true;
}

export const arePlatformLiteralsMissing = (
    state = xtremRedux.getStore().getState(),
    locale = state.applicationContext?.locale || 'en-US',
): boolean =>
    !state.translations ||
    !state.translations[locale] ||
    !objectKeys(state.translations[locale]).find(key => key.startsWith('@sage/xtrem-ui'));

export const getSidebarTableProperties = ({
    elementId,
    pageDefinition,
    level = 0,
}: {
    pageDefinition: PageDefinition;
    level?: number;
    elementId: string;
}): MakeRequired<TableProperties, 'sidebar'> => {
    const tableProperties =
        pageDefinition.metadata.uiComponentProperties[elementId]._controlObjectType === FieldKey.NestedGrid
            ? (pageDefinition.metadata.uiComponentProperties[elementId] as NestedGridProperties).levels[level]
            : (pageDefinition.metadata.uiComponentProperties[elementId] as TableProperties);

    // We make a exception for MultifileDeposit because we hardcode the sidebar from our side so it's not defined in the page definition
    if (
        !tableProperties.sidebar &&
        pageDefinition.metadata.uiComponentProperties[elementId]._controlObjectType !== FieldKey.MultiFileDeposit
    ) {
        throw new Error(`No sidebar is defined for element with ID ${elementId}`);
    }
    return tableProperties as MakeRequired<TableProperties, 'sidebar'>;
};

export const getSidebarNormalFields = (layoutDefinition: Dict<SidebarSectionDefinition>): AbstractUiControlObject[] => {
    const collector: AbstractUiControlObject[] = [];
    objectKeys(layoutDefinition || {}).forEach(sectionKey => {
        objectKeys(layoutDefinition[sectionKey].blocks || []).forEach(blockKey => {
            (layoutDefinition[sectionKey].blocks[blockKey].fields || []).forEach(f => {
                if (f instanceof AbstractUiControlObject) {
                    collector.push(f);
                }
            });
        });
    });

    return collector;
};

export const getSidebarNestedFields = (layoutDefinition: Dict<SidebarSectionDefinition>): string[] => {
    const collector: string[] = [];
    objectKeys(layoutDefinition || {}).forEach(sectionKey => {
        objectKeys(layoutDefinition[sectionKey].blocks || []).forEach(blockKey => {
            (layoutDefinition[sectionKey].blocks[blockKey].fields || []).forEach(f => {
                if (!(f instanceof AbstractUiControlObject)) {
                    collector.push(convertDeepBindToPathNotNull(f));
                }
            });
        });
    });

    return collector;
};

export function getResolvedSidebarLayout({
    value,
    screenDefinition,
    level = 0,
    nodeTypes,
    elementId,
    layout,
}: {
    layout: ValueOrCallback<any, Dict<SidebarSectionDefinition>>;
    level?: number;
    value: CollectionValue;
    screenDefinition: ScreenBaseDefinition;
    elementId: string;
    nodeTypes: Dict<FormattedNodeDetails>;
}): Dict<SidebarSectionDefinition> {
    const customFields = getCustomFields(
        value.contextNode || value.nodes?.[level],
        nodeTypes,
        value.getColumnDefinitions(level),
        elementId,
        FieldKey.Table,
        screenDefinition.metadata.customizations,
    );

    return mergeIntoSidebarLayout(
        resolveByValue<Dict<SidebarSectionDefinition>>({
            screenId: screenDefinition.metadata.screenId,
            propertyValue: layout,
            rowValue: null,
            fieldValue: null,
            skipHexFormat: true,
        }),
        customFields,
    );
}

export const isSidebarDirty = (state = xtremRedux.getStore().getState()): boolean => {
    return Object.values(state.activeDialogs).some(dialog => {
        if (dialog.type !== 'table-sidebar') {
            return false;
        }

        const screenId = dialog.screenId;
        if (!screenId) {
            throw new Error('No screenId found in dialog');
        }

        const dialogContent = dialog.content as TableSidebarDialogContent;

        const tableValue = state.screenDefinitions[screenId].values[dialogContent.elementId] as CollectionValue;

        const committedRecords = tableValue.getData({
            isUncommitted: false,
            includePhantom: true,
            cleanMetadata: false,
            ...(dialogContent.recordId ? { level: dialogContent.level } : {}),
            parentId: null,
            where: {
                ...(dialogContent.recordId ? { _id: dialogContent.recordId } : { __phantom: true }),
            },
        });

        const records = tableValue.getData({
            isUncommitted: true,
            includePhantom: true,
            cleanMetadata: false,
            ...(dialogContent.recordId ? { level: dialogContent.level } : {}),
            parentId: null,
            where: {
                ...(dialogContent.recordId ? { _id: dialogContent.recordId } : { __phantom: true }),
            },
        });

        if (records?.[0]?.__dirtyColumns && (records?.[0]?.__dirtyColumns as Set<any>).size > 0) {
            const committedRecord = committedRecords?.[0];
            if (!committedRecord) {
                return true;
            }

            // Check if any dirty column is different from the committed record
            const isDifferent = Array.from(records[0].__dirtyColumns).some(
                (key: string) => !isEqual(committedRecord[key], records[0][key]),
            );
            if (isDifferent) {
                return true;
            }
        }

        const sidebarLayout = getResolvedSidebarLayout({
            value: tableValue,
            screenDefinition: state.screenDefinitions[screenId],
            level: dialogContent.level,
            nodeTypes: state.nodeTypes,
            elementId: dialogContent.elementId,
            layout: dialogContent.sidebarDefinition.layout,
        });

        const normalFields = getSidebarNormalFields(sidebarLayout);

        return !!normalFields.find(n => state.screenDefinitions[screenId].dirtyStates[n.id]);
    });
};

export const getVisibleSectionsFromPage = (
    screenId: string,
    screenDefinition: ScreenBaseDefinition = xtremRedux.getStore().getState().screenDefinitions[screenId],
): SectionControlObject[] => {
    const sectionOrder = screenDefinition.metadata.layout.$items.map(i => i.$containerId);
    const sections = Object.values(screenDefinition.metadata.controlObjects).filter(
        controlObject =>
            controlObject instanceof SectionControlObject &&
            !resolveByValue({
                screenId,
                skipHexFormat: true,
                propertyValue: controlObject.properties.isHidden,
                rowValue: null,
                fieldValue: null,
            }),
    ) as SectionControlObject[];

    return sections.sort((s1, s2) => sectionOrder.indexOf(s1.id) - sectionOrder.indexOf(s2.id));
};
