import type { Dict } from '@sage/xtrem-shared';
import { camelCase } from 'lodash';
import type { PropertyValueType } from '../component/field/reference/reference-types';
import { getStore } from '../redux/store';
import { GraphQLTypes } from '../types';
import { xtremConsole } from './console';
import { convertDeepBindToPathNotNull } from './nested-field-utils';
import { getArtifactDescription } from './transformers';
import type { FormattedNodeDetails, FormattedNodeDetailsProperty } from '../service/metadata-types';

/**
 * Finds a node property type details from the list of known node types by doing an deep search in the graph structure.
 * @param targetNode
 * @param targetProperty
 * @param nodeTypes
 * @returns
 */
export const findDeepPropertyDetails = (
    targetNode?: string,
    targetProperty?: PropertyValueType,
    nodeTypes: Dict<FormattedNodeDetails> = getStore().getState().nodeTypes,
    forFiltering = false,
): (FormattedNodeDetailsProperty & { parentNode: string }) | null => {
    if (!targetNode) {
        xtremConsole.warn('Missing target node.');
        return null;
    }
    if (!targetProperty) {
        xtremConsole.warn('Missing target property.');
        return null;
    }
    let currentTarget: FormattedNodeDetails = nodeTypes[targetNode];
    let previousTargetNode = targetNode;
    if (!currentTarget) {
        xtremConsole.warn(`Unknown node type: ${targetNode}.`);
        return null;
    }

    const path = convertDeepBindToPathNotNull(targetProperty).split('.');
    for (let i = 0; i < path.length; i += 1) {
        const propertyDefinition = currentTarget.properties[path[i]] || currentTarget.mutations[path[i]];
        if (!propertyDefinition) {
            xtremConsole.warn(`Unknown property: ${path[i]}.`);
            return null;
        }

        // If the path segment is JSON, the children are considered to be JSON objects too.
        if (propertyDefinition.type === GraphQLTypes.Json) {
            return { ...propertyDefinition, parentNode: currentTarget.name };
        }

        if (i === path.length - 1) {
            return { ...propertyDefinition, parentNode: currentTarget.name };
        }

        let nextType = propertyDefinition.type;
        /**
         * If the type is IntReference or ExternalReference, we need to look the query type (aka non-input type) to identify what sort of object it refers to
         * so we can continue deep searching.
         */
        if (
            (propertyDefinition.type === GraphQLTypes.IntReference ||
                propertyDefinition.type === GraphQLTypes.ExternalReference) &&
            forFiltering
        ) {
            const outputDefinition = nodeTypes[previousTargetNode].properties?.[path[i]]?.type;
            if (outputDefinition) {
                nextType = `${outputDefinition}`;
            }
        }
        previousTargetNode = nextType;
        currentTarget = nodeTypes[nextType];
        if (!currentTarget) {
            xtremConsole.warn(`Unknown property: ${nextType}.`);
            return null;
        }
    }
    xtremConsole.warn(`Failed to lookup type definition for ${targetNode} ${JSON.stringify(targetProperty)}.`);
    return null;
};

/**
 * Finds a node property type from the list of known node types by doing an deep search in the graph structure
 * @param targetNode
 * @param targetProperty
 * @param nodeTypes
 * @param defaultValue optionally can be called with a defaultValue which is returned if no type definition is found
 * @returns
 */
export function findDeepPropertyType(
    targetNode: string | undefined,
    targetProperty: PropertyValueType | undefined,
    nodeTypes: Dict<FormattedNodeDetails>,
): FormattedNodeDetailsProperty | null;
export function findDeepPropertyType(
    targetNode: string | undefined,
    targetProperty: PropertyValueType | undefined,
    nodeTypes: Dict<FormattedNodeDetails>,
    forFiltering?: boolean,
): FormattedNodeDetailsProperty | null;
export function findDeepPropertyType(
    targetNode: string | undefined,
    targetProperty: PropertyValueType | undefined,
    nodeTypes: Dict<FormattedNodeDetails>,
    forFiltering = false,
): FormattedNodeDetailsProperty | null {
    return findDeepPropertyDetails(targetNode, targetProperty, nodeTypes, forFiltering);
}

export const getDataTypeName = (node: string): string => {
    return camelCase(getArtifactDescription(node).name);
};
