import type { DecoratorProperties, FieldKey } from '../component/types';
import { xtremConsole } from './console';

export const checkFilterAndNode = <D extends FieldKey>(elementId: string, properties: DecoratorProperties<D>): void => {
    const castedProps = properties as { canFilter?: boolean; node?: string };
    if ((castedProps.canFilter || castedProps.canFilter === undefined) && !castedProps.node) {
        xtremConsole.warn(`${elementId}: Please set also the node property to enable filtering.`);
    }
};
