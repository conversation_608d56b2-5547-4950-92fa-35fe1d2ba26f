import React from 'react';
import type { ClientError, ErrorDetail } from '@sage/xtrem-client';
import { ClientDiagnoseSeverity } from '@sage/xtrem-client';
import { objectKeys, type Dict } from '@sage/xtrem-shared';
import type { PodCollectionDecoratorProperties, TableProperties } from '../component/control-objects';
import type {
    DetailListDecoratorProperties,
    NestedGridDecoratorProperties,
    PodDecoratorProperties,
    TableDecoratorProperties,
    VitalPodDecoratorProperties,
} from '../component/decorators';
import { withoutNestedTechnical } from '../component/nested-fields';
import type { ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import { getStore } from '../redux';
import { CollectionValue } from '../service/collection-data-service';
import type { ScreenBase } from '../service/screen-base';
import type { ValidationResult } from '../service/screen-base-definition';
import { SERVER_VALIDATION_RULE_PREFIX } from './constants';
import { resolveByValue } from './resolve-value-utils';
import { localize } from '../service/i18n-service';
import { omit, partition } from 'lodash';
import { ContainerValidationErrorsComponent } from '../component/container/page/container-validation-errors';
import { getPagePropertiesFromState } from './state-utils';
import { findDeepPropertyDetails, findDeepPropertyType } from './node-utils';
import { schemaTypeNameFromNodeName } from './transformers';
import { findColumnDefinitionByBind } from './abstract-fields-utils';
import { nonSavableFieldType } from '../service/graphql-api';
import { FieldKey } from '../component/types';
import { convertDeepBindToPath } from './nested-field-utils';
import type { FormattedNodeDetailsProperty } from '../service/metadata-types';

export interface TransformedServerError {
    validationErrors: Dict<ValidationResult[]>;
    globalError?: Error;
}

type NestedFieldDecoratorProperties =
    | TableDecoratorProperties
    | NestedGridDecoratorProperties
    | PodCollectionDecoratorProperties
    | PodDecoratorProperties<any, any>
    | VitalPodDecoratorProperties<any, any>
    | DetailListDecoratorProperties;

export const findNestedFieldProperties = (
    screenId: string,
    columnId: string,
    fieldProperties: NestedFieldDecoratorProperties,
    level = 0,
): string => {
    const nestedFields = withoutNestedTechnical(
        (fieldProperties as TableDecoratorProperties).columns ||
            (fieldProperties as DetailListDecoratorProperties).fields ||
            (fieldProperties as NestedGridDecoratorProperties).levels?.[level]?.columns ||
            [],
    );
    const nestedField = findColumnDefinitionByBind(nestedFields, columnId);
    if (nestedField) {
        return (
            resolveByValue({
                screenId,
                skipHexFormat: true,
                propertyValue: nestedField.properties.title,
                rowValue: undefined,
            }) || columnId
        );
    }
    return columnId;
};

const getEditableElementIdsFromBind = (screenBase: ScreenBase, bind: string): string[] =>
    objectKeys(screenBase._pageMetadata.uiComponentProperties).filter(
        k =>
            !nonSavableFieldType.includes(
                screenBase._pageMetadata.uiComponentProperties[k]._controlObjectType as FieldKey,
            ) &&
            (k === bind ||
                convertDeepBindToPath(
                    (screenBase._pageMetadata.uiComponentProperties[k] as ReadonlyFieldProperties).bind,
                ) === bind),
    )!;

export const transformServerErrorToToast = async (
    error: ClientError,
    screenBase: ScreenBase,
): Promise<TransformedServerError> => {
    const screenId = screenBase._pageMetadata.screenId;
    const serverErrorMessages: ErrorDetail[] = error.errors;
    const validationErrors: (ValidationResult & { bind: string })[] = [];
    const globalErrors: string[] = [];
    const state = getStore().getState();

    serverErrorMessages.forEach(e => {
        if (e.extensions?.diagnoses) {
            e.extensions.diagnoses.forEach(d => {
                if (d.severity !== ClientDiagnoseSeverity.error && d.severity !== ClientDiagnoseSeverity.exception) {
                    return;
                }

                const validationError: Partial<ValidationResult & { bind: string }> = {
                    screenId,
                    message: d.message,
                    // We use the server prefix to indicate that the error code is sent by the server
                    validationRule: `${SERVER_VALIDATION_RULE_PREFIX}${e.extensions.code}`,
                };

                let contextNode: string | undefined = getPagePropertiesFromState(screenId, state).node as string;
                if (!contextNode) {
                    globalErrors.push(d.message);
                    return;
                }
                contextNode = schemaTypeNameFromNodeName(contextNode);
                let isCollection = false;

                let propertyInfo: FormattedNodeDetailsProperty | null = null;

                for (let i = 0; i < d.path.length; i += 1) {
                    const currentProperty = d.path[i];

                    if (!isCollection) {
                        propertyInfo = findDeepPropertyDetails(contextNode, currentProperty, state.nodeTypes);
                    }

                    if (!propertyInfo) {
                        // If the error is unmappable we only use the basic details from it.
                        break;
                    }

                    if (propertyInfo.kind === 'SCALAR') {
                        // Scalar will only be the last element in the chain as it cannot have child fields
                        // We only need to preserve the level information if it is greater than 1.
                        // TODO: CHECK IF NESTED GRID LEVELS START WITH 0 OR 1;
                        // DON'T FORGET TO CHECK!!!!!!!!!!!
                        const level = Math.floor((i - 1) / 2);
                        if (level >= 1) {
                            validationError.level = level;
                        }

                        // If it's not on the top level we need to set the columnId
                        if (i > 0) {
                            validationError.columnId = currentProperty;
                        }
                    } else if (propertyInfo.kind === 'INPUT_OBJECT' || propertyInfo.kind === 'OBJECT') {
                        contextNode = findDeepPropertyType(contextNode, currentProperty, state.nodeTypes)!.type;
                        validationError.columnId = [...d.path].splice(i, d.path.length).join('.');
                        if (!validationError.elementId || !validationError.columnId) {
                            // eslint-disable-next-line no-continue
                            continue;
                        }
                        const fieldProps =
                            state.screenDefinitions[screenId].metadata.uiComponentProperties[validationError.elementId];
                        if (
                            fieldProps._controlObjectType === FieldKey.Table &&
                            findColumnDefinitionByBind(
                                (fieldProps as TableProperties).columns || [],
                                validationError.columnId,
                            )
                        ) {
                            break;
                        }
                    } else if (propertyInfo.kind === 'LIST') {
                        if (isCollection) {
                            isCollection = false;
                            validationError.recordId = currentProperty;
                        } else {
                            isCollection = true;
                            contextNode = findDeepPropertyType(contextNode, currentProperty, state.nodeTypes)!.type;
                        }
                    } else {
                        throw new Error(`Unknown GraphQL object kind: ${propertyInfo.kind}`);
                    }
                }

                let elementIds = getEditableElementIdsFromBind(screenBase, d.path[0]);
                if (!elementIds.length) {
                    elementIds = getEditableElementIdsFromBind(screenBase, [...d.path].splice(0, 1).join('.'));
                }

                elementIds.forEach(elementId => {
                    const newValidationError = { ...validationError };
                    newValidationError.elementId = elementId;
                    newValidationError.bind = d.path[0];
                    validationErrors.push(newValidationError as ValidationResult & { bind: string });
                });

                if (!elementIds.length) {
                    globalErrors.push(d.message);
                }
            });
        } else {
            globalErrors.push(
                e.message || localize('@sage/xtrem-ui/validation-errors-unknown', 'Unknown validation error.'),
            );
        }
    });

    const [nestedValidationErrors, nonNestedValidationErrors] = partition(validationErrors, e => e.recordId);
    // Capture non nested errors
    const returnValue: TransformedServerError = {
        validationErrors: nonNestedValidationErrors.reduce(
            (prevValue, v) => {
                if (!prevValue[v.elementId]) {
                    prevValue[v.elementId] = [omit(v, ['bind'])];
                } else {
                    prevValue[v.elementId].push(omit(v, ['bind']));
                }
                return prevValue;
            },
            {} as Dict<ValidationResult[]>,
        ),
    };

    // Capture nested errors and sort them into buckets based on their element id
    const nestedErrors = nestedValidationErrors.reduce(
        (prevValue, v) => {
            prevValue[v.elementId] = [...(prevValue[v.elementId] || []), v];
            return prevValue;
        },
        {} as Dict<ValidationResult[]>,
    );

    const validationErrorPromises: Promise<void>[] = [];
    const collectionValues: CollectionValue[] = [];
    objectKeys(nestedErrors).forEach(e => {
        const screenDefinition = state.screenDefinitions[screenId];
        const valueNestedErrors = nestedErrors[e];
        const value = screenDefinition.values[e];
        if (
            value instanceof CollectionValue &&
            valueNestedErrors.some(ne => ne.recordId && value.getRawRecord({ id: ne.recordId }))
        ) {
            // TODO nested grid and levels.
            validationErrorPromises.push(
                value.addValidationErrors({ validationErrors: nestedErrors[e], shouldNotifySubscribers: true }),
            );
            collectionValues.push(value);
        }

        if (screenDefinition.metadata.uiComponentProperties[e] && valueNestedErrors.length > 0) {
            nonNestedValidationErrors.push(...valueNestedErrors.map(err => ({ ...err, bind: e })));
        }

        // TODO: Non-collection nested records such as pods
    });
    await Promise.all(validationErrorPromises);

    const nestedErrs = collectionValues.flatMap(cv => {
        const fieldProperties = screenBase._pageMetadata.uiComponentProperties[cv.elementId] as any;
        return cv.getErrorList(
            cv.getErrorMessages({
                fieldProperties,
            }),
        );
    });
    const allErrors = [...nonNestedValidationErrors, ...nestedErrs, ...globalErrors];
    const pageValidationErrors = [...nonNestedValidationErrors, ...nestedErrs];
    if (allErrors.length > 0) {
        returnValue.globalError = new ServerError(
            localize('@sage/xtrem-ui/validation-errors', 'Validation Errors'),
            (
                <ContainerValidationErrorsComponent
                    pageMetadata={screenBase._pageMetadata}
                    validationResults={pageValidationErrors}
                    screenId={screenId}
                >
                    {globalErrors.map(e => (
                        <li key={e}>{e}</li>
                    ))}
                </ContainerValidationErrorsComponent>
            ),
            serverErrorMessages,
        );
    }
    return returnValue;
};

export class ServerError extends Error {
    constructor(
        message: string,
        public readonly toastContent: React.ReactNode,
        public readonly errors: ErrorDetail[],
    ) {
        super(message);
        // TODO: review this as we use es2020 now
        // needed as target is es5
        Object.setPrototypeOf(this, ServerError.prototype);
        this.name = 'ServerError';
    }
}
