PATH: XTREEM/Client+Framework/Auto+Configuration

## Introduction

Some page artifact settings are automatically derived from node property configuration and the corresponding datatype. The automatic client property configuration works on non-transient pages (see Limitations section below) which have a `node` decorator property. The framework maps the server side property definition to the field it is bound to on the page artifact and identifies the default values.

## Automatically configured properties and components
This section lists the automatically set properties and their calculation methods.

### Main list / navigation panel (`navigationPanel.listItem`)

The main list definition is determined by the node's default reference datatype. The columns list is computed from the `imageFieldPath`, `valuePath`, `helperTextPath` then the rest of the `columns`. The navigation panel definition is the following, `imageField` set by `imageFieldPath`, `title` set by `valuePath` and `line2` is set by `helperTextPath`.

The main list definition can be disabled by setting `navigationPanel: undefined` on the page decorator.

### `title` (all field types)
Field titles are by the bound properties translated name.

### `isDisabled` (all editable field types)

Fields are set to be disabled when the bound server side property is not on the node's input type. This means that all computed properties are automatically set to disabled, given the user's input wouldn't be saved anyway.

### `maxLength` (text, textArea, filterSelect fields)

The max length validation rule is determined by the bound property's string datatype's `maxLength` property.

### `scale` (numeric field)

In case of decimal properties, the scale decorator property is determined by the bound property's numeric datatype's `scale` property.

If the field is bound to an integer property, it's always set to `0`.

### `max` and `min` (numeric field)

The max and min validation rules are determined by the bound property's numeric datatype's precision and scale. Its computed as `(10^(precision - scale) - 10^(-scale))`

### `node` (table, summaryTable, reference, multiReference, podCollection, pod, vitalPod)

The node property is set by the reference type of the server side property definition.

### `optionType` (dropdownList, label, multiDropdown, radio, select, stepSequence)

The option type is determined by the bound property's enum data type

### `columns` (reference, multiReference, filterSelect, podCollection, table, tableSummary)

The columns property are set by the `lookup.columnPaths` of the corresponding reference data type.

### `valueField` (reference, multiReference, filterSelect)

The columns property are set by the `lookup.valuePath` of the corresponding reference data type.

### `helperTextField` (reference)

The columns property are set by the `lookup.helperTextPath` of the corresponding reference data type.

### `imageField` (reference)

The columns property are set by the `lookup.imageFieldPath` of the corresponding reference data type.

### `mobileCard` (table)

The columns property are set by the `lookup` of the corresponding reference data type. The mobile card definition is the following, `imageField` set by `imageFieldPath`, `title` set by `valuePath` and `line2` is set by `helperTextPath`.

### `tunnelPage` (reference)

The columns property are set by the `lookup.tunnelPage` of the corresponding reference data type.


## Overriding

The automatically are the default. If there is a property set in the client artifact, that takes precedence over the server provided default values.

If there is no need to the server defined value, the default value can be overridden by setting the given property to `undefined`. For example `mobileCard: undefined`.


## Limitation

Automatic configuration works only on non-transient pages and non-transient fields and columns, with the exception of fields with a `node` property. In that case, the framework tries to work out default values based on the default data type of the specified node. Therefore it is possible to set up transient reference fields only with a `node` property (and a `bind` property in case of nested fields).
