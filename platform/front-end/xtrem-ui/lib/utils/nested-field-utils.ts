import type { ClientNode } from '@sage/xtrem-client';
import { get, isObject } from 'lodash';
import type {
    CalendarProperties,
    PodCollectionDecoratorProperties,
    TableProperties,
} from '../component/control-objects';
import type { PropertyValueType } from '../component/field/reference/reference-types';
import { getReferenceValueField } from '../component/field/reference/reference-utils';
import type { InternalTableProperties } from '../component/field/table/table-component-types';
import type { NestedField, NestedFieldTypes } from '../component/nested-fields';
import type { NestedReferenceProperties } from '../component/nested-fields-properties';
import { FieldKey } from '../component/types';
import type { GridLodable } from '../service/collection-data-service';
import type { ScreenBase } from '../service/screen-base';
import { objectKeys } from '@sage/xtrem-shared';

export const isNestedField = (maybeNestedField: any): boolean =>
    !!maybeNestedField && typeof maybeNestedField === 'object' && maybeNestedField.properties && maybeNestedField.type;

export type NestedFieldPropertyType =
    | GridLodable
    | TableProperties
    | InternalTableProperties
    | CalendarProperties
    | PodCollectionDecoratorProperties;

export const getNestedFieldsFromProperties = <T extends ClientNode = any>(
    properties: NestedFieldPropertyType,
): NestedField<any, any, T>[] => {
    let columns: NestedField<any, any>[] = [];
    if ((properties as TableProperties).columns) {
        columns = [...((properties as TableProperties).columns || [])];
    }

    const mobileCardDefinition = (properties as TableProperties).mobileCard;
    if (mobileCardDefinition && isObject(mobileCardDefinition)) {
        columns = [...columns, ...(Object.values(mobileCardDefinition).filter(v => !!v) as NestedField<any, any>[])];
    }

    const calendarProperties = properties as CalendarProperties;
    if (calendarProperties.eventCard) {
        columns = [
            ...columns,
            ...(Object.values(calendarProperties.eventCard).filter(v => !!v) as NestedField<any, any>[]),
        ];
    }

    if (calendarProperties.startDateField) {
        columns.push({
            type: FieldKey.Date,
            defaultUiProperties: {
                bind: convertDeepBindToPathNotNull(calendarProperties.startDateField),
            },
            properties: {
                bind: convertDeepBindToPathNotNull(calendarProperties.startDateField),
            },
        });
    }
    if (calendarProperties.endDateField) {
        columns.push({
            type: FieldKey.Date,
            defaultUiProperties: {
                bind: convertDeepBindToPathNotNull(calendarProperties.endDateField),
            },
            properties: {
                bind: convertDeepBindToPathNotNull(calendarProperties.endDateField),
            },
        });
    }

    if (isNestedField((properties as PodCollectionDecoratorProperties).recordTitle)) {
        columns.push((properties as PodCollectionDecoratorProperties).recordTitle as NestedField<any, any>);
    }

    const headerLabel = (properties as PodCollectionDecoratorProperties).headerLabel;
    if (headerLabel) {
        columns.push(headerLabel);
    }

    return columns as NestedField<any, any, T>[];
};

/**
 * Resolve title field from a card definition to a display string value.
 */
export const getImagePlaceholderValue = (
    record: any,
    titleField: NestedField<ScreenBase, NestedFieldTypes>,
): string | undefined => {
    if (!record) {
        return undefined;
    }
    const properties = titleField.properties as NestedReferenceProperties;
    const fieldValuePath = convertDeepBindToPath(titleField.properties.bind);

    if (fieldValuePath && titleField.type === FieldKey.Reference) {
        const valueField = getReferenceValueField(properties);
        const value = get(record, `${fieldValuePath}.${valueField}`);
        if (value) {
            return String(value);
        }
        return '_';
    }

    if (fieldValuePath) {
        const fieldValue = get(record, fieldValuePath);
        if (fieldValue) {
            return String(fieldValue);
        }
    }
    return '_';
};

/**
 * Turn nested bind property reference object to dot-notion reference. For example, it converts `{this:{is:{a:{nested:{bind:true}}}}}`
 * to `this.is.a.nested.bind`.
 * @param path string or deep bind object
 * @returns dot notion deep reference string
 */
export const convertDeepBindToPath = (path?: string | PropertyValueType | boolean): string | null => {
    if (path === null || path === undefined || typeof path === 'boolean') {
        return null;
    }
    if (typeof path === 'string') {
        return path;
    }

    if (typeof path === 'object' && objectKeys(path).length === 1) {
        const childKeys = objectKeys(path);
        const childPath = convertDeepBindToPath(path[childKeys[0]] as any);
        if (childPath) {
            return `${childKeys[0]}.${childPath}`;
        }
        return childKeys[0];
    }

    throw new Error(`Unsupported bind value: ${JSON.stringify(path)}`);
};

/**
 * Same as convertDeepBindToPath, but throws an error if the value is null or undefined.
 * @param path
 */
export const convertDeepBindToPathNotNull = (path?: string | PropertyValueType | boolean): string => {
    const result = convertDeepBindToPath(path);
    if (!result) {
        throw new Error('Bind cannot be falsy.');
    }
    return result;
};

/**
 * Returns to first component of a nested bind property For example, it converts `hi`, `hi.there` and `{hi:{there:true}}` to `hi`.
 * @param path
 * @returns top level property name
 */
export const getTopLevelBindFromNestedBindNotNull = (path?: string | PropertyValueType): string => {
    if (!path) {
        throw new Error('Invalid bind, cannot be empty.');
    }

    return getTopLevelBindFromNestedBind(path)!;
};

/**
 * Returns to first component of a nested bind property For example, it converts `hi`, `hi.there` and `{hi:{there:true}}` to `hi`.
 * @param path
 * @returns top level property name
 */
export function getTopLevelBindFromNestedBind(path: string | PropertyValueType): string;
export function getTopLevelBindFromNestedBind(path: undefined | PropertyValueType): undefined;
export function getTopLevelBindFromNestedBind(path?: string | PropertyValueType): string | undefined {
    if (typeof path === 'string') {
        return path.split('.')[0];
    }

    if (typeof path === 'object' && objectKeys(path).length === 1) {
        return objectKeys(path)[0];
    }

    if (path === undefined) {
        return path;
    }

    throw new Error(`Unsupported bind value: ${JSON.stringify(path)}`);
}
