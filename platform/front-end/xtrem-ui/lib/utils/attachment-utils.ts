import { section } from '../component/container/section/section-decorator';
import type { PageMetadata } from '../service/page-metadata';
import { multiFileDepositField } from '../component/field/multi-file-deposit/multi-file-deposit-decorator';
import type { AttachmentInformation } from '../service/node-information-service';
import type { Page } from '../service/page';
import type {
    MultiFileDepositControlObject,
    SectionControlObject,
    SectionProperties,
} from '../component/control-objects';
import { attachmentsMimeTypes, attachmentsMimeTypesByExtention, type Dict } from '@sage/xtrem-shared';
import { schemaTypeNameFromNodeName } from './transformers';
import { findDeepPropertyType } from './node-utils';
import type { FormattedNodeDetails } from '../service/metadata-types';
import { ATTACHMENT_SECTION_ID, ATTACHMENTS_ELEMENT_ID, ATTACHMENTS_PROPERTY_NAME } from './constants';
import { localize } from '../service/i18n-service';
import type { ScreenBaseDefinition } from '../service/screen-base-definition';

const TWENTY_PLUS = '20+';

const documentMimeTypes = [
    ...attachmentsMimeTypesByExtention.pdf,
    ...attachmentsMimeTypesByExtention.pptx,
    ...attachmentsMimeTypesByExtention.docx,
    ...attachmentsMimeTypesByExtention.xlsx,
];
const imageMimeTypes = [
    ...attachmentsMimeTypesByExtention.bmp,
    ...attachmentsMimeTypesByExtention.gif,
    ...attachmentsMimeTypesByExtention.heic,
    ...attachmentsMimeTypesByExtention.heif,
    ...attachmentsMimeTypesByExtention.jfif,
    ...attachmentsMimeTypesByExtention.jpeg,
    ...attachmentsMimeTypesByExtention.jpg,
    ...attachmentsMimeTypesByExtention.png,
    ...attachmentsMimeTypesByExtention.svg,
    ...attachmentsMimeTypesByExtention.tif,
    ...attachmentsMimeTypesByExtention.tiff,
];

export const getAttachmentInformation = (
    nodeName: string,
    nodeTypes: Dict<FormattedNodeDetails>,
): AttachmentInformation | null => {
    const node = schemaTypeNameFromNodeName(nodeName);
    if (!nodeTypes[node]?.hasAttachments) {
        return null;
    }
    const attachmentAssociationNode = findDeepPropertyType(node, ATTACHMENTS_PROPERTY_NAME, nodeTypes)?.targetNode;
    const attachmentFileNode = findDeepPropertyType(
        node,
        `${ATTACHMENTS_PROPERTY_NAME}.attachment`,
        nodeTypes,
    )?.targetNode;
    if (attachmentAssociationNode && attachmentFileNode) {
        return {
            attachmentAssociationNode,
            attachmentFileNode,
        };
    }
    return null;
};

export interface PageWithAttachmentElements extends Page {
    [ATTACHMENT_SECTION_ID]: SectionControlObject;
    [ATTACHMENTS_ELEMENT_ID]: MultiFileDepositControlObject;
}

export function getAttachmentCount(values: ScreenBaseDefinition['values']): string | undefined {
    if (values[ATTACHMENTS_ELEMENT_ID]?.pageInfo && values[ATTACHMENTS_ELEMENT_ID]?.data) {
        return values[ATTACHMENTS_ELEMENT_ID].pageInfo?.hasNextPage
            ? TWENTY_PLUS
            : String(values[ATTACHMENTS_ELEMENT_ID].data.length);
    }
    return undefined;
}

export function setAttachmentCountOnMetadata(pageMetadata: PageMetadata, values: ScreenBaseDefinition['values']): void {
    (pageMetadata.uiComponentProperties[ATTACHMENT_SECTION_ID] as SectionProperties).indicatorContent =
        getAttachmentCount(values);
}

export function addAttachmentElements(pageMetadata: PageMetadata, attachmentInformation: AttachmentInformation): void {
    if (!pageMetadata?.target) {
        return;
    }

    section({
        title: localize('@sage/xtrem-ui/attachments', 'Attachments'),
    })(pageMetadata.target, ATTACHMENT_SECTION_ID);

    multiFileDepositField({
        parent(this: PageWithAttachmentElements) {
            return this[ATTACHMENT_SECTION_ID];
        },
        isTitleHidden: true,
        bind: ATTACHMENTS_PROPERTY_NAME,
        title: localize('@sage/xtrem-ui/attachments', 'Attachments'),
        node: attachmentInformation.attachmentAssociationNode,
        attachmentNode: attachmentInformation.attachmentFileNode,
        canUserHideColumns: true,
        fileTypes: attachmentsMimeTypes.join(', '),
        kind: 'attachment',
        optionsMenu: [
            {
                id: 'all',
                title: localize('@sage/xtrem-ui/attachment-options-menu-all', 'All file types'),
                graphQLFilter: {},
            },
            {
                id: 'documents',
                title: localize('@sage/xtrem-ui/attachment-options-menu-documents', 'Documents'),
                graphQLFilter: { attachment: { mimeType: { _in: documentMimeTypes } } },
            },
            {
                id: 'images',
                title: localize('@sage/xtrem-ui/attachment-options-menu-images', 'Images'),
                graphQLFilter: { attachment: { mimeType: { _in: imageMimeTypes } } },
            },
            {
                id: 'others',
                title: localize('@sage/xtrem-ui/attachment-options-menu-others', 'Others'),
                graphQLFilter: { attachment: { mimeType: { _nin: [...imageMimeTypes, ...documentMimeTypes] } } },
            },
        ],
        onChange(this: PageWithAttachmentElements) {
            const totalCount = this[ATTACHMENTS_ELEMENT_ID].value.length;
            this[ATTACHMENT_SECTION_ID].indicatorContent = totalCount > 20 ? TWENTY_PLUS : String(totalCount);
        },
        isFullWidth: true,
    })(pageMetadata.target, ATTACHMENTS_ELEMENT_ID);
}
