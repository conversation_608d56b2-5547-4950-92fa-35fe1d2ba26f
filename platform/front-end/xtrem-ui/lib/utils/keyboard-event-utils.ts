import type { KeyboardEvent } from 'react';

export const KEY_BACKSPACE = 8;
export const KEY_SPACE_BAR = 32;
export const KEY_DELETE = 46;
export const KEY_F2 = 113;
export const KEY_ENTER = 13;
export const KEY_TAB = 9;
export const KEY_ESC = 27;
export const SHIFT_TAB = 'Shift+Tab';
export const TAB = 'Tab';
export const ENTER = 'Enter';
export const ESCAPE = 'Escape';
export const BACKSPACE = 'Backspace';
export const DELETE = 'Delete';
export const F2 = 'F2';
export const SPACEBAR = ' ';

export function getCharCodeFromEvent(event: KeyboardEvent): number {
    const instance = event || window.event;
    return typeof instance.which === 'undefined' ? instance.keyCode : instance.which;
}

export const isModifierKeyPressed = (event: KeyboardEvent): boolean => {
    return event.altKey || event.ctrlKey || event.metaKey;
};

export const isUnmodifiedAlphabeticalChar = (event: KeyboardEvent): boolean => {
    return !isModifierKeyPressed(event) && /^[a-z]$/i.test(event.key);
};

export function isEnter(event: KeyboardEvent): boolean {
    return KEY_ENTER === getCharCodeFromEvent(event) || event.key === ENTER;
}
export function isTab(event: KeyboardEvent): boolean {
    return KEY_TAB === getCharCodeFromEvent(event) || event.key === TAB;
}

export function isEsc(event: KeyboardEvent): boolean {
    return KEY_ESC === getCharCodeFromEvent(event) || event.key === ESCAPE;
}

export function isDeleteOrBackspace(event: KeyboardEvent): boolean {
    return [KEY_DELETE, KEY_BACKSPACE].indexOf(event.keyCode) > -1 || [DELETE, BACKSPACE].includes(event.key);
}

export function isBackspace(event: KeyboardEvent): boolean {
    return [KEY_BACKSPACE].indexOf(event.keyCode) > -1 || event.key === BACKSPACE;
}

export function isSpaceBar(event: KeyboardEvent): boolean {
    return [KEY_SPACE_BAR].indexOf(event.keyCode) > -1 || event.key === SPACEBAR;
}

export function isForwardTab(event: KeyboardEvent): boolean {
    return isTab(event) && !event.shiftKey;
}

export function isBackwardTab(event: KeyboardEvent): boolean {
    return isTab(event) && event.shiftKey;
}
