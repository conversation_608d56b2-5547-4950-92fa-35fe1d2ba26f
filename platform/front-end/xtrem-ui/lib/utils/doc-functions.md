PATH: XTREEM/Client+Framework/Shared+Functions

# Shared Functions

The framework allows code to be shared across packages. Any function that is meant to be shared across UI pages must be places either inside `<pkg>/lib/client-functions` or `<pkg>/lib/shared-functions`. All exported functions will be then compiled by Typescript and included in the package build.

The way they can be imported inside client-side code is the following:

```ts
import * as xtremSystemClientFunctions from '@sage/xtrem-system/build/lib/client-functions';
import * as xtremSystemSharedFunctions from '@sage/xtrem-system/build/lib/shared-functions';
```
see [this page](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/FunctionsPage) for a more comprehensive example.


## Limitations

-   Functions must be imported from the `build` folder: until Typescript officially [adds support for package exports](https://github.com/microsoft/TypeScript/issues/33079) unfortunately there are no clean alternatives.
-   Please note that shared functions **are not supposed to use any Node exclusive features** because client-side code is ultimately run on the browser.
-   Given the previous point **any import from `@sage/xtrem-core` is not allowed as it will fail at runtime**.
-   The `localize` function string extraction only works in the main artifact files. The strings are not extracted from files located in the `client-function` and the `shared-functions` folder.
