import { useRef } from 'react';

export interface Timer {
    abort: () => void;
    isRunning: () => boolean;
    start: (callback: () => void, delay: number) => void;
}

export const useTimer = (): Timer => {
    const timeout = useRef<number | null>(null);

    /**
     * This function indicates whether or not a timer is currently running.
     */
    const isRunning = (): boolean => {
        return typeof timeout.current === 'number';
    };

    /**
     * This function aborts the currently running timer.
     */
    const abort = (): void => {
        if (timeout.current) {
            clearTimeout(timeout.current);
        }
    };

    /**
     * This function aborts the currently running timer (if applicable) and
     * starts/restarts a new timer with the given callback and delay.
     *
     * @param {function():void} callback
     * @param {number} delay
     */
    const start = (callback: () => void, delay: number): void => {
        abort();
        timeout.current = window.setTimeout(callback, delay);
    };

    return { abort, isRunning, start };
};
