import { usePrevious } from '@sage/xtrem-ui-components';
import { useEffect } from 'react';

export const useFocus = (componentRef: any, isInFocus = false, selector?: string): void => {
    const prevIsInFocus = usePrevious(isInFocus);
    useEffect(() => {
        if (!prevIsInFocus && isInFocus && componentRef.current) {
            const elementInFocus = document.activeElement;
            const element = selector ? getFocusableElement(componentRef.current, selector) : componentRef.current;
            if (element && elementInFocus !== element) {
                element.focus();
            }
        }
    });
};

const getFocusableElement = (fieldContainer: HTMLElement, selector: string): HTMLElement | null => {
    return fieldContainer.querySelector(selector);
};
