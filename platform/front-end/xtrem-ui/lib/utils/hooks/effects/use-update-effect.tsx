import { type DependencyList, useEffect } from 'react';
import { useFirstMountState } from './use-first-mount-state';
import { type EffectCallback, useDeepCompareEffect } from '@sage/xtrem-ui-components';

const useUpdateEffect: typeof useEffect = (effect, deps) => {
    const isFirstMount = useFirstMountState();

    // eslint-disable-next-line consistent-return
    useEffect(() => {
        if (!isFirstMount) {
            return effect();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [deps, isFirstMount]);
};

function useDeepCompareUpdateEffect<
    Callback extends EffectCallback = EffectCallback,
    Deps extends DependencyList = DependencyList,
>(effect: Callback, deps: Deps): void {
    const isFirstMount = useFirstMountState();

    // eslint-disable-next-line consistent-return
    useDeepCompareEffect(() => {
        if (!isFirstMount) {
            return effect();
        }
    }, deps);
}

export { useUpdateEffect, useDeepCompareUpdateEffect };
