import { useSelector } from 'react-redux';
import type * as xtremRedux from '../../../redux';

export const useFieldValue = <T extends any>(screenId?: string, elementId?: string): T | null => {
    return useSelector<xtremRedux.XtremAppState, any>(s => {
        if (!screenId || !elementId) {
            return null;
        }

        return s.screenDefinitions[screenId]?.values[elementId] || null;
    });
};
