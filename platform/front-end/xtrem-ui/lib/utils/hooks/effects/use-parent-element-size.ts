import { useCallback, useEffect, useState } from 'react';
import { useEventListener, useIsomorphicLayoutEffect } from 'usehooks-ts';

interface Size {
    width: number;
    height: number;
}

export function useParentElementSize<T extends HTMLElement = HTMLDivElement>(): [(node: T | null) => void, Size] {
    // Mutable values like 'ref.current' aren't valid dependencies
    // because mutating them doesn't re-render the component.
    // Instead, we use a state as a ref to be reactive.
    const [ref, setRef] = useState<T | null>(null);
    const [size, setSize] = useState<Size>({
        width: 0,
        height: 0,
    });

    // Prevent too many rendering using useCallback
    const handleSize = useCallback(() => {
        setSize({
            width: ref?.parentElement?.offsetWidth || 0,
            height: ref?.parentElement?.offsetHeight || 0,
        });
    }, [setSize, ref?.parentElement?.offsetHeight, ref?.parentElement?.offsetWidth]);

    useEffect(() => {
        /**
         * If the size of the elements outside of the parent element changes, that can force the parent element to resize.
         * The 'resize' event is only triggered when the window is resized, and not when only the element.
         * */
        const interval = setInterval(() => {
            if (size.width !== ref?.parentElement?.offsetWidth || size.height !== ref?.parentElement?.offsetHeight) {
                handleSize();
            }
        }, 1000);
        return (): void => clearInterval(interval);
    }, [size, handleSize, ref?.parentElement?.offsetHeight, ref?.parentElement?.offsetWidth]);

    useEventListener('resize', handleSize);

    useIsomorphicLayoutEffect(() => {
        handleSize();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [ref?.parentElement?.offsetHeight, ref?.parentElement?.offsetWidth]);

    return [setRef, size];
}
