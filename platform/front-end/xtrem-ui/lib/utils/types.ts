import type { ClientNode } from '@sage/xtrem-client';
import type { Datetime, DateValue } from '@sage/xtrem-date-time';
import type { Dict } from '@sage/xtrem-shared';
import type { Primitive, UnionToIntersection } from 'ts-essentials';
import type { ScreenBase } from '../service/screen-base';

export type UnionType<T extends any[]> = T[number];
export declare type DatePropertyValue = Date | string | DateValue;
export declare type DateTimePropertyValue = Date | string | Datetime;

export type QueryParameters = Dict<number | string | boolean>;

/** Useful to make OR condition between properties in our interface. If we put the two properties Typescript returns an a type error.
 * E.G RequireOnlyOne<interface, 'key1' | 'key2'> */
export declare type RequireOnlyOne<T, Keys extends keyof T = keyof T & string> = Pick<T, Exclude<keyof T, Keys>> &
    { [K in Keys]-?: Required<Pick<T, K>> & Partial<Record<Exclude<Keys, K>, undefined>> }[Keys];
export type Merge<M, N> = Omit<M, Extract<keyof M, keyof N>> & N;

export type ValueOrCallbackWithFieldValue<CT extends ScreenBase, ReturnType, ValueType = any, RecordType = Dict<any>> =
    | ((this: CT, value: ValueType, rowValue?: RecordType) => ReturnType | undefined)
    | ReturnType
    | undefined;

export type ValueOrCallbackWitRecordValue<CT extends ScreenBase, ReturnType, RecordType = Dict<any>> =
    | ((this: CT, rowValue?: RecordType) => ReturnType | undefined)
    | ReturnType
    | undefined;

export type ValueOrCallback<CT extends ScreenBase, ReturnType> = ((this: CT) => ReturnType | undefined) | ReturnType;

// Ensures object has at least and only one key
export type SingleKeyed<T> =
    T extends Array<any> ? never : T extends object ? RequireOnlyOne<{ [P in keyof T]?: T[P] }> : T;

export type WithAtLeastOneKey<T, U = { [K in keyof T]: Required<Pick<T, K>> }> = Partial<T> & U[keyof U];

export type Simplify<T> = { [KeyType in keyof T]: T[KeyType] };
export type PickOwnProperties<T extends ClientNode, P> = Simplify<
    Pick<
        T,
        {
            [K in keyof T]: T[K] extends P
                ? UnionToIntersection<T[K]> extends Array<P>
                    ? P & UnionToIntersection<P> extends infer Z
                        ? P extends Z
                            ? never
                            : K
                        : K
                    : K
                : P extends Array<infer U>
                  ? T[K] extends U
                      ? UnionToIntersection<T[K]> extends Array<U>
                          ? K
                          : never
                      : T[K] extends U
                        ? K
                        : never
                  : P extends T[K]
                    ? K
                    : never;
        }[keyof T]
    >
>;

export type IsEqual<T, U> = (<G>() => G extends T ? 1 : 2) extends <G>() => G extends U ? 1 : 2 ? true : false;
type Filter<KeyType, ExcludeType> =
    IsEqual<KeyType, ExcludeType> extends true ? never : KeyType extends ExcludeType ? never : KeyType;

export type Except<ObjectType, KeysType extends keyof ObjectType> = {
    [KeyType in keyof ObjectType as Filter<KeyType, KeysType>]: ObjectType[KeyType];
};
export type SetRequired<BaseType, Keys extends keyof BaseType> = Simplify<
    // Pick just the keys that are optional from the base type.
    Except<BaseType, Keys> &
        // Pick the keys that should be required from the base type and make them required.
        Required<Pick<BaseType, Keys>>
>;

type ValueOrCallbackKeys<Base> = NonNullable<
    {
        [Key in keyof Base]: Base[Key] extends ValueOrCallback<any, any>
            ? Base[Key] extends UnionToIntersection<Base[Key]>
                ? never
                : Key
            : never;
    }[keyof Base]
>;

type ConditionalKeys<Base, Condition> = NonNullable<
    {
        [Key in keyof Base]: Base[Key] extends Condition ? Key : never;
    }[keyof Base]
>;

export type ValueOrCallbackKeysExtractor<Base> = ValueOrCallbackKeys<Omit<Base, ConditionalKeys<Base, Primitive>>>;

type ValueOrCallbackPick<Base> = Pick<Base, ValueOrCallbackKeys<Base>>;
type ValueOrCallbackReturnExtractorInternal<Base> = ValueOrCallbackPick<Omit<Base, ConditionalKeys<Base, Primitive>>>;
export type ValueOrCallbackReturnExtractor<Base, K extends keyof ValueOrCallbackReturnExtractorInternal<Base>> =
    ValueOrCallbackReturnExtractorInternal<Base>[K] extends ValueOrCallback<any, infer R> ? R : never;

export type MakeRequired<T, K extends keyof T & string> = Omit<T, K> & Required<Pick<T, K>>;

export type CarbonLinkEvent =
    | React.MouseEvent<HTMLAnchorElement>
    | React.MouseEvent<HTMLButtonElement>
    | React.KeyboardEvent<HTMLAnchorElement>
    | React.KeyboardEvent<HTMLButtonElement>;

export type Writeable<T> = { -readonly [P in keyof T]: T[P] };
export type RecordValue<T> = T extends Record<any, infer U> ? U : never;

export interface JsonSerializableObject {
    [member: string]: JsonSerializableValue;
}
export interface JsonSerializableArray extends Array<JsonSerializableValue> {}

export type JsonSerializableValue = Primitive | JsonSerializableObject | JsonSerializableArray;

type _TupleOf<T, N extends number, R extends unknown[]> = R['length'] extends N ? R : _TupleOf<T, N, [T, ...R]>;
export type FixedSizeTuple<T, N extends number> = N extends N ? (number extends N ? T[] : _TupleOf<T, N, []>) : never;
