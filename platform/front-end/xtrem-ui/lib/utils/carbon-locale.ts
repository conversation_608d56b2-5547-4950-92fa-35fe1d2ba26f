import type { Dict } from '@sage/xtrem-shared';
import type { I18nProviderProps } from 'carbon-react/esm/components/i18n-provider';
import type { Locale as DateFnsLocale } from 'date-fns';
import { arSA } from 'date-fns/locale/ar-SA';
import { de } from 'date-fns/locale/de';
import { enGB } from 'date-fns/locale/en-GB';
import { enUS } from 'date-fns/locale/en-US';
import { es } from 'date-fns/locale/es';
import { fr } from 'date-fns/locale/fr';
import { it } from 'date-fns/locale/it';
import { pl } from 'date-fns/locale/pl';
import { pt } from 'date-fns/locale/pt';
import { ptBR } from 'date-fns/locale/pt-BR';
import { zhCN } from 'date-fns/locale/zh-CN';
import { memoize } from 'lodash';
import { localize } from '../service/i18n-service';

const dateLocales: Dict<DateFnsLocale> = {
    'en-GB': enGB,
    'en-US': enUS,
    'es-ES': es,
    'pl-PL': pl,
    'zh-CN': zhCN,
    'fr-FR': fr,
    'pt-PT': pt,
    'pt-BR': ptBR,
    'ar-SA': arSA,
    'de-DE': de,
    'it-IT': it,
};

export const isSingular = (count: number | string): boolean => parseInt(count as string, 10) === 1;

export const carbonLocale = memoize((selectedLocale: string): I18nProviderProps['locale'] => ({
    locale: (): string => selectedLocale,
    actions: {
        edit: (): string => localize('@sage/xtrem-ui/action-edit', 'Edit'),
        delete: (): string => localize('@sage/xtrem-ui/action-delete', 'Delete'),
    },
    actionPopover: {
        ariaLabel: (): string => 'actions',
    },
    batchSelection: {
        selected: (count: number): string =>
            localize('@sage/xtrem-ui/action-tool-bar-items-selected', 'Items selected: {{count}}', { count }),
    },
    confirm: {
        no: (): string => localize('@sage/xtrem-ui/generic-no', 'No'),
        yes: (): string => localize('@sage/xtrem-ui/generic-yes', 'Yes'),
    },
    date: {
        dateFnsLocale: (): DateFnsLocale => dateLocales[selectedLocale] || dateLocales['en-US'],
        ariaLabels: {
            previousMonthButton: () => localize('@sage/xtrem-ui/previous-month', 'Previous month'),
            nextMonthButton: () => localize('@sage/xtrem-ui/next-month', 'Next month'),
        },
        dateFormatOverride: localize('@sage/xtrem-ui/carbon-date-format', 'MM/dd/yyyy'),
    },
    errors: {
        messages: {
            formSummary: (): null => null,
        },
    },
    message: {
        ai: (): string => localize('@sage/xtrem-ui/message-type-ai', 'AI'),
        error: (): string => localize('@sage/xtrem-ui/message-type-error', 'Error'),
        info: (): string => localize('@sage/xtrem-ui/message-type-info', 'Information'),
        success: (): string => localize('@sage/xtrem-ui/message-type-success', 'Success'),
        warning: (): string => localize('@sage/xtrem-ui/message-type-warning', 'Warning'),
        neutral: (): string => localize('@sage/xtrem-ui/message-type-information', 'Information'),
        closeButtonAriaLabel: (): string => localize('@sage/xtrem-ui/action-close', 'Close'),
    },
    numeralDate: {
        validation: {
            day: (): string =>
                localize('@sage/xtrem-ui/date-error-day-range', 'Day should be a number within a 1-31 range.'),
            month: (): string =>
                localize('@sage/xtrem-ui/date-error-month-range', 'Month should be a number within a 1-12 range.'),
            year: (): string =>
                localize('@sage/xtrem-ui/date-error-year-range', 'Year should be a number within a 1800-2200 range.'),
        },
        labels: {
            day: (): string => localize('@sage/xtrem-ui/calendar-view-day', 'Day'),
            month: (): string => localize('@sage/xtrem-ui/calendar-view-month', 'Month'),
            year: (): string => localize('@sage/xtrem-ui/calendar-view-year', 'Year'),
        },
    },
    pager: {
        show: (): string => localize('@sage/xtrem-ui/table-show', 'Show'),
        records: (count: number, showNumber = true): string => {
            const noun = isSingular(count) ? 'item' : 'items';
            return showNumber ? `${count} ${noun}` : noun;
        },
        first: (): string => localize('@sage/xtrem-ui/table-first', 'First'),
        last: (): string => localize('@sage/xtrem-ui/table-last', 'Last'),
        next: (): string => localize('@sage/xtrem-ui/table-next', 'Next'),
        previous: (): string => localize('@sage/xtrem-ui/table-previous', 'Previous'),
        pageX: (): string => 'Page',
        ofY: (count: number): string => `of ${count}`,
    },
    select: {
        actionButtonText: (): string => 'Add an item',
        placeholder: (): string => localize('@sage/xtrem-ui/please-select-placeholder', 'Please Select...'),
        noResultsForTerm: (term: string): string => `No results for "${term}"`,
    },
    switch: {
        on: (): string => localize('@sage/xtrem-ui/switch-on-caps', 'ON'),
        off: (): string => localize('@sage/xtrem-ui/switch-off-caps', 'OFF'),
    },
    textEditor: {
        boldAria: (): string => localize('@sage/xtrem-ui/text-format-capital-bold', 'Bold'),
        italicAria: (): string => localize('@sage/xtrem-ui/text-format-capital-italic', 'Italic'),
        unorderedListAria: (): string => localize('@sage/xtrem-ui/text-format-capital-bullet-list', 'Bullet List'),
        orderedListAria: (): string => localize('@sage/xtrem-ui/text-format-capital-number-list', 'Number List'),
        cancelButton: (): string => localize('@sage/xtrem-ui/text-editor-cancel', 'Cancel'),
        cancelButtonAria: (): string => localize('@sage/xtrem-ui/text-editor-cancel-button', 'Cancel'),
        characterCounter: (): string => localize('@sage/xtrem-ui/text-editor-character-counter', 'Character count'),
        characterLimit: (): string => localize('@sage/xtrem-ui/text-editor-character-limit', 'Character limit'),
        contentEditorAria: (): string => localize('@sage/xtrem-ui/text-editor-content-editor', 'Content editor'),
        saveButton: (): string => localize('@sage/xtrem-ui/text-editor-save', 'Save'),
        saveButtonAria: (): string => localize('@sage/xtrem-ui/text-editor-save-button', 'Save'),
        toolbarAriaLabel: (): string => localize('@sage/xtrem-ui/text-editor-toolbar', 'Toolbar'),
    },
}));

export function clearCarbonLocaleCache(): void {
    carbonLocale.cache?.clear?.();
}
