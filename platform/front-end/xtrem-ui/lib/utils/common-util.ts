import { objectKeys } from '@sage/xtrem-shared';
import { get, isEqual, isObject, set, transform } from 'lodash';

export function deepFreeze<T extends object>(object: T): Readonly<T> {
    if (isObject(object) && Object.isFrozen(object)) {
        return object;
    }
    // Retrieve the property names defined on object
    const propNames = Object.getOwnPropertyNames(object);

    // Freeze properties before freezing self
    // eslint-disable-next-line no-restricted-syntax
    for (const name of propNames) {
        const value = get(object, name);

        set(object, name, value && typeof value === 'object' ? deepFreeze(value) : value);
    }

    return Object.freeze(object);
}

export function containsValueDeep(object: any, value: any, strict = true): boolean {
    // eslint-disable-next-line eqeqeq
    if (!strict && object == value) {
        return true;
    }

    if (object === value) {
        return true;
    }

    if (!object) {
        return false;
    }

    if (object instanceof Array) {
        return object.findIndex(k => containsValueDeep(k, value, strict)) !== -1;
    }

    if (typeof object === 'object') {
        return objectKeys(object).findIndex(k => containsValueDeep(object[k], value, strict)) !== -1;
    }

    return false;
}

export function differenceBetweenObjects<T extends object>(object: T, base: T): Partial<T> {
    return transform(object, (result: any, value, key) => {
        if (!isEqual(value, base[key])) {
            result[key] =
                isObject(value) && isObject(base[key]) ? differenceBetweenObjects(value, base[key] as any) : value;
        }
    });
}

/** Deep search an object structure by a property name. It returns a unique set of results. */
export const deepFindPropertyValues = <T = any>(
    propertyName: string,
    target: Object | Array<any>,
    callback: (target: any) => boolean = (): boolean => true,
): T[] => {
    const result: T[] = [];
    const deepFind = (currentTarget: Object | Array<any>): void => {
        if (Array.isArray(currentTarget)) {
            currentTarget.forEach(deepFind);
        } else if (typeof currentTarget === 'object' && currentTarget !== null) {
            objectKeys(currentTarget).forEach(currentKey => {
                if (currentKey === propertyName) {
                    if (callback(currentTarget)) {
                        result.push(get(currentTarget, currentKey) as T);
                    }
                } else {
                    deepFind(currentTarget[currentKey]);
                }
            });
        }
    };

    deepFind(target);
    return Array.from(new Set(result));
};
