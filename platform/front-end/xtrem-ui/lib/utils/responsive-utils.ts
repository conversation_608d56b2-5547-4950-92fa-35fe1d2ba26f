import type { DialogSizes } from 'carbon-react/esm/components/dialog/dialog.config';
import type { ComponentKey, ContainerWidth, FieldWidth } from '../component/types';
import { FieldKey } from '../component/types';
import type { ReduxResponsive, ResponsiveTypes } from '../redux/state';
import type { Breakpoints } from '../render/responsive-breakpoints';
import { objectKeys } from '@sage/xtrem-shared';

export interface ScreenSize extends Breakpoints {
    infinity: number;
}
export const DEFAULT_FIELD_SIZE: number = 2;

export const COLUMN_COUNT_HELPER_PANEL: number = 4;

export const COLUMN_COUNT_PAGE_BODY: ScreenSize = {
    infinity: 12,
    l: 12,
    m: 8,
    s: 8,
    xs: 4,
};

export const MARGIN_SIZE_PAGE_BODY: ScreenSize = {
    infinity: 40,
    l: 40,
    m: 32,
    s: 24,
    xs: 0,
};

export const GUTTER_SIZE_PAGE_BODY: ScreenSize = {
    infinity: 24,
    l: 24,
    m: 24,
    s: 16,
    xs: 16,
};

// Y Axis: field sizes; X Axis: screen sizes; Values: the calculated column size
export const FIELD_SIZE: {
    'extra-large': ScreenSize;
    large: ScreenSize;
    medium: ScreenSize;
    'small-medium': ScreenSize;
    small: ScreenSize;
    half: ScreenSize;
} = {
    'extra-large': { infinity: 6, l: 6, m: 6, s: 4, xs: 4 },
    large: { infinity: 4, l: 4, m: 4, s: 4, xs: 4 },
    half: { infinity: 6, l: 6, m: 4, s: 4, xs: 4 },
    medium: { infinity: 3, l: 3, m: 4, s: 4, xs: 4 },
    'small-medium': { infinity: 3, l: 3, m: 2, s: 2, xs: 2 },
    small: { infinity: 2, l: 2, m: 2, s: 2, xs: 2 },
};

export enum CONTAINER_SIZE_NAMING {
    extraLarge = 'extra-large',
    large = 'large',
    medium = 'medium',
    half = 'half',
    small = 'small',
    extraSmall = 'extra-small',
}

// Y Axis: field sizes; X Axis: screen sizes; Values: the calculated field size
export const CONTAINER_SIZE: {
    [key in CONTAINER_SIZE_NAMING]: ScreenSize;
} = {
    [CONTAINER_SIZE_NAMING.extraLarge]: { infinity: 12, l: 12, m: 8, s: 8, xs: 4 },
    [CONTAINER_SIZE_NAMING.large]: { infinity: 8, l: 8, m: 8, s: 8, xs: 4 },
    [CONTAINER_SIZE_NAMING.half]: { infinity: 6, l: 6, m: 4, s: 4, xs: 4 },
    [CONTAINER_SIZE_NAMING.medium]: { infinity: 6, l: 6, m: 6, s: 4, xs: 4 },
    [CONTAINER_SIZE_NAMING.small]: { infinity: 4, l: 4, m: 4, s: 4, xs: 4 },
    [CONTAINER_SIZE_NAMING.extraSmall]: { infinity: 2, l: 2, m: 2, s: 4, xs: 4 },
};

const pick = (is: ResponsiveTypes, bps: Breakpoints): any => bps[objectKeys(bps).find(bp => is[bp])!];

export const getPageBodyColumnCount = (is: ResponsiveTypes): number => pick(is, COLUMN_COUNT_PAGE_BODY);
export const getPageBodyMarginSize = (is: ResponsiveTypes): number => pick(is, MARGIN_SIZE_PAGE_BODY);
export const getGutterSize = (is: ResponsiveTypes): number => pick(is, GUTTER_SIZE_PAGE_BODY);

export const calculateContainerWidth = (
    is: ResponsiveTypes,
    availableColumnNum: number,
    setContainerWidth?: ContainerWidth,
): any => {
    const applicableWidth = pick(is, CONTAINER_SIZE[setContainerWidth || CONTAINER_SIZE_NAMING.extraLarge]);
    if (applicableWidth < availableColumnNum) {
        return applicableWidth;
    }
    return availableColumnNum;
};

export const calculateFieldWidth = (
    is: ResponsiveTypes,
    fieldType: ComponentKey,
    availableColumnNum = DEFAULT_FIELD_SIZE,
    isFieldFullWidth = false,
    setFieldNum?: FieldWidth,
): number => {
    const applicableWidth = pick(is, FIELD_SIZE[setFieldNum || CONTAINER_SIZE_NAMING.small]);

    if (isFieldFullWidth) {
        return availableColumnNum;
    }
    if (setFieldNum) {
        return Math.min(availableColumnNum, applicableWidth);
    }
    switch (fieldType) {
        // These field types require full width by default
        case FieldKey.Calendar:
        case FieldKey.DetailList:
        case FieldKey.FormDesigner:
        case FieldKey.Image:
        case FieldKey.Message:
        case FieldKey.MultiFileDeposit:
        case FieldKey.NestedGrid:
        case FieldKey.NodeBrowserTree:
        case FieldKey.PodCollection:
        case FieldKey.Preview:
        case FieldKey.RichText:
        case FieldKey.SelectionCard:
        case FieldKey.StepSequence:
        case FieldKey.Table:
        case FieldKey.TableSummary:
        case FieldKey.Tree:
        case FieldKey.VisualProcess:
            return availableColumnNum;
        default:
            return applicableWidth;
    }
};

export const getMaxDialogSize = (browser: ReduxResponsive): DialogSizes => {
    if (browser.greaterThan.m) {
        return CONTAINER_SIZE_NAMING.extraLarge;
    }
    if (browser.greaterThan.s) {
        return CONTAINER_SIZE_NAMING.large;
    }
    if (browser.greaterThan.xs) {
        return CONTAINER_SIZE_NAMING.medium;
    }
    return CONTAINER_SIZE_NAMING.small;
};
