import { get, noop } from 'lodash';
import { isDevMode, isNoConsole } from './window';

const consoleHistory: any[] = [];

const fakeConsoleLog =
    (originalMethod: Function) =>
    (...args: any[]): void => {
        consoleHistory.push(`${new Date().toISOString()} - ${originalMethod.name.toUpperCase()} -\n`);
        consoleHistory.push(...args);
        originalMethod.apply(window.console, args);
    };

if (isDevMode()) {
    (window as any).__PRINT_CONSOLE_HISTORY = (): string => {
        const history = consoleHistory
            .map(e => {
                try {
                    return JSON.stringify(e, null, 4);
                } catch {
                    return String(e);
                }
            })
            .join('\n\n-----------------------------------------------------------\n\n');

        consoleHistory.splice(0, consoleHistory.length);
        return history;
    };
}

export const xtremConsole: Console = new Proxy(window.console, {
    get: (target: Console, prop: string): any => {
        try {
            if (isNoConsole() || !isDevMode()) {
                return noop;
            }
            return fakeConsoleLog(get(target, prop));
        } catch (err) {
            return get(target, prop);
        }
    },
});
