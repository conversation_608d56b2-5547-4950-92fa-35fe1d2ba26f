import { camelCase } from 'lodash';
import type { ResponsiveProperties } from '../component/abstract-ui-control-object';
import type { EditableFieldProperties } from '../component/editable-field-control-object';
import type { BaseEditableComponentProperties } from '../component/field/field-base-component-types';
import type { ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import type { FieldKey, FieldValue } from '../component/types';
import type { ReduxResponsive } from '../redux/state';
import type { PageArticleItem } from '../service/layout-types';
import type { ContextType } from '../types';
import { normalizeUnderscoreBind } from './abstract-fields-utils';
import { resolveByValue } from './resolve-value-utils';
import { splitValueToMergedValue } from './transformers';

export function scrollWithAnimationTo(element: HTMLElement, container: HTMLElement, offset?: number): void {
    const startTime = performance.now();
    const elementTop = element ? element.getBoundingClientRect().top : 0;
    const targetY = Math.max(0, container.scrollTop + elementTop - (offset || 0));
    const startPoint = container.scrollTop;
    const distance = targetY - startPoint;
    const scrollDuration = Math.min(Math.max(300, Math.abs(distance)), 900);
    const animationStep = (newTimestamp: number): void => {
        if (Math.round(container.scrollTop) !== targetY && newTimestamp < startTime + scrollDuration) {
            const sinPercent = Math.sin((Math.PI / 2) * ((newTimestamp - startTime) / scrollDuration));
            const scrollValue = startPoint + sinPercent * distance;
            container.scrollTop = scrollValue;
            window.requestAnimationFrame(animationStep);
        }
    };
    window.requestAnimationFrame(animationStep);
}

export function isHidden(
    hiddenProperties?: Partial<PageArticleItem> | ResponsiveProperties,
    browser?: ReduxResponsive,
): boolean {
    if (!browser || !hiddenProperties) return false;

    const isHiddenDesktop =
        (hiddenProperties as PageArticleItem).$isHiddenDesktop ||
        (hiddenProperties as ResponsiveProperties).isHiddenDesktop ||
        false;

    const isHiddenMobile =
        (hiddenProperties as PageArticleItem).$isHiddenMobile ||
        (hiddenProperties as ResponsiveProperties).isHiddenMobile ||
        false;

    return (isHiddenDesktop && browser.greaterThan.s) || (isHiddenMobile && !browser.greaterThan.s);
}

export const getDataTestIdAttribute = (
    fieldType: string,
    label?: string,
    // TODO: XT-27998 `elementId` should always be a string, remove this workaround
    // when `valueField` gets added to nested image fields.
    elementId?: string | object,
    prefix = 'e-field',
): string => {
    return `e-${fieldType}-field${label ? ` ${prefix}-label-${camelCase(label)}` : ''}${
        elementId && typeof elementId === 'string' ? ` ${prefix}-bind-${normalizeUnderscoreBind(elementId)}` : ''
    }`;
};

export const getComponentClass = (
    props: Partial<BaseEditableComponentProperties<ReadonlyFieldProperties, FieldValue<FieldKey>>>,
    specificClassNames: string,
    rowValue?: any,
): string => {
    const isHiddenValue = resolveByValue({
        screenId: props.screenId!,
        fieldValue: props.value,
        propertyValue: props.fieldProperties!.isHidden,
        rowValue: splitValueToMergedValue(rowValue),
        skipHexFormat: true,
    });
    const isFieldHidden = isHidden({ ...props.item, ...props.fieldProperties }, props.browser) || isHiddenValue;
    const hasValidationErrors = props.validationErrors && props.validationErrors.length > 0;

    return getFieldClass(
        props.screenId!,
        props.value,
        `e-field ${specificClassNames}`,
        props.fieldProperties!,
        hasValidationErrors,
        isFieldHidden,
        rowValue,
        props.contextType,
    );
};

export const getFieldClass = (
    screenId: string,
    fieldValue: any,
    classNames: string,
    fieldProperties: EditableFieldProperties,
    hasValidationErrors = false,
    isFieldHidden = false,
    rowValue = {},
    contextType?: ContextType,
): string => {
    let classes = classNames;

    if (isFieldHidden) {
        classes += ' e-hidden';
    }

    if (hasValidationErrors) {
        classes += ' e-invalid';
    }

    if (fieldProperties.isFullWidth) {
        classes += ' e-full-width';
    }

    if (fieldProperties.isHelperTextHidden) {
        classes += ' e-helper-text-hidden';
    }

    if (fieldProperties.isTitleHidden) {
        classes += ' e-title-hidden';
    }
    if (contextType) {
        classes += ` e-context-${contextType}`;
    }

    if (
        resolveByValue({
            screenId,
            fieldValue,
            propertyValue: fieldProperties.isReadOnly,
            rowValue: splitValueToMergedValue(rowValue),
            skipHexFormat: true,
        })
    ) {
        classes += ' e-read-only';
    }

    if (
        resolveByValue({
            screenId,
            fieldValue,
            propertyValue: fieldProperties.isDisabled,
            rowValue: splitValueToMergedValue(rowValue),
            skipHexFormat: true,
        })
    ) {
        classes += ' e-disabled';
    }

    return classes;
};

export const copyToClipboard = (content: string): void => {
    const selection = document && document.getSelection();
    if (selection) {
        const el = document.createElement('textarea');
        el.value = content;
        el.setAttribute('readonly', '');
        el.style.position = 'absolute';
        el.style.left = '-9999px';
        document.body.appendChild(el);
        const selected = selection.rangeCount > 0 ? selection.getRangeAt(0) : false;
        el.select();
        document.execCommand('copy');
        document.body.removeChild(el);
        if (selected) {
            selection.removeAllRanges();
            selection.addRange(selected);
        }
    }
};

export function focusTopPage(): void {
    setTimeout(() => {
        const pages = document.querySelectorAll('.e-page-body');
        if (pages && pages.length > 0) {
            const targetPage = pages[pages.length - 1];
            if (targetPage instanceof HTMLElement) {
                targetPage.focus({
                    preventScroll: true,
                });
            }
        }
    });
}

export const focusPageContent = (): void => {
    const mainElements = Array.from(document.querySelectorAll('main'));

    if (mainElements.length) {
        const selector = 'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])';
        const focusableElements = Array.from(mainElements[mainElements.length - 1].querySelectorAll(selector)).filter(
            element => {
                return (
                    !element.hasAttribute('disabled') &&
                    !element.hasAttribute('aria-hidden') &&
                    !element.classList.contains('e-navigation-panel-toggle')
                );
            },
        );

        if (focusableElements.length) {
            (focusableElements[0] as HTMLElement).focus();
        }
    }
};

/** For mocking purposes */
export const getNewFileReader = (): FileReader => new FileReader();

export const getTabControlElement = (): HTMLElement | null =>
    document.querySelector<HTMLElement>('.e-header-nav li[tabindex="0"]');

export const isScrolledToTheEnd = (element: HTMLElement): boolean => {
    const sh = element.scrollWidth;
    const st = element.scrollLeft;
    const ht = element.offsetWidth;
    if (ht === 0 || st === sh - ht) {
        return true;
    }
    return false;
};

export const isChildOfElementWithClass = (element: Element | Document, className: string): boolean => {
    if (element === window.document) {
        return false;
    }

    if ((element as Element)?.classList.contains(className)) {
        return true;
    }

    if (!element.parentElement) {
        return false;
    }

    return isChildOfElementWithClass(element.parentElement, className);
};

export const isChildOfElementWithAttributeValue = (
    element: Element | Document,
    attributeName: string,
    attributeValue: string,
): boolean => {
    if (element === window.document) {
        return false;
    }

    if ((element as Element)?.getAttribute(attributeName) === attributeValue) {
        return true;
    }

    if (!element.parentElement) {
        return false;
    }

    return isChildOfElementWithAttributeValue(element.parentElement, attributeName, attributeValue);
};

export const findAncestorDatasetProperty = (element: HTMLElement, datasetPropertyName: string): string | null => {
    if ((element as unknown as Document) === window.document) {
        return null;
    }

    const datasetPropertyValue = (element as HTMLElement)?.dataset[datasetPropertyName];
    if (datasetPropertyValue) {
        return datasetPropertyValue;
    }

    if (!element?.parentElement) {
        return null;
    }

    return findAncestorDatasetProperty(element.parentElement, datasetPropertyName);
};
