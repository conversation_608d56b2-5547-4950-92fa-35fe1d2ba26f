import type { CreateOperation, UpdateOperation } from '@sage/xtrem-client';
import { attachmentsMimeTypesByExtention } from '@sage/xtrem-shared';
import type { CancelTokenSource } from 'axios';
import axios from 'axios';
import { cloneDeep, get, isArray } from 'lodash';
import type { FileDepositValue } from '../component/types';
import { localize } from '../service/i18n-service';
import { showToast } from '../service/toast-service';

export const STATUS_CREATED = 'created';
export const STATUS_UPLOADED = 'uploaded';
export const STATUS_UPLOAD_FAILED = 'uploadFailed';
export const STATUS_VERIFIED = 'verified';
export const STATUS_CANCELLED = 'cancelled';
export const STATUS_UPLOADING = 'uploading';
export const STATUS_REJECTED = 'rejected';

function normalizeDevUrl(url: string): string {
    // This replacement is needed so we can use the upload URL in watch mode locally
    if (window.origin === 'http://localhost:3000') {
        return url.replace('localhost:8240', 'localhost:3000');
    }

    // This replacement is needed so we can use the upload URL in integration tests
    if (window.origin === 'http://127.0.0.1:8240') {
        return url.replace('http://localhost', 'http://127.0.0.1');
    }

    return url;
}

export function isDisallowedMimeType(allowedTypes: string | string[], fileType: string, fileName: string): boolean {
    const normalizedAllowedTypes = isArray(allowedTypes) ? allowedTypes : allowedTypes.split(', ');
    // Firefox on Windows uses the registry to determine the file type, which can be incorrect for CSV files
    // https://support.mozilla.org/en-US/questions/1401889
    const normalizedFileType =
        window.navigator.userAgent.toLowerCase().includes('firefox') &&
        fileType === 'application/vnd.ms-excel' &&
        fileName.endsWith('.csv')
            ? 'text/csv'
            : fileType;
    // Wild card check for fileTypes like "audio/*", "video/*", "image/*"
    const wildcardType = `${normalizedFileType.split('/')[0]}/*`;
    return !normalizedAllowedTypes.includes(normalizedFileType) && !normalizedAllowedTypes.includes(wildcardType);
}

export interface FileDepositProgress {
    isInProgress: boolean;
    progress: number;
    fieldValue: Partial<FileDepositValue>;
    cancelTokenSource: CancelTokenSource;
}

export interface DepositFileArguments<K extends FileDepositValue['kind'] = FileDepositValue['kind']> {
    file: File;
    targetNodeMutations: {
        create: CreateOperation<any, any>;
        update: UpdateOperation<any, any>;
    };
    onUploadProgress?: (progressEvent: FileDepositProgress) => void;
    kind: K;
}

export async function depositFile({
    file,
    targetNodeMutations,
    onUploadProgress,
    kind,
}: DepositFileArguments): Promise<FileDepositValue> {
    const fieldValue: Partial<FileDepositValue> = {
        filename: file.name,
        mimeType: file.type || getMimeTypeFromExtension(file.name),
        lastModified: new Date(file.lastModified).toISOString(),
        contentLength: file.size,
        status: STATUS_CREATED,
        kind,
    };
    const cancelTokenSource = axios.CancelToken.source();

    onUploadProgress?.({ isInProgress: false, progress: 0, fieldValue: cloneDeep(fieldValue), cancelTokenSource });

    try {
        const createResponse = await targetNodeMutations
            .create(
                { _id: true, uploadUrl: true, _createStamp: true, _createUser: { displayName: true } },
                {
                    data: fieldValue,
                },
            )
            .execute();

        fieldValue._id = createResponse._id;
        fieldValue.uploadUrl = normalizeDevUrl(createResponse.uploadUrl || '');
        fieldValue._createStamp = createResponse._createStamp;
        fieldValue._createUser = createResponse._createUser;
    } catch (err) {
        fieldValue.status = STATUS_UPLOAD_FAILED;
        onUploadProgress?.({ isInProgress: false, progress: 0, fieldValue: cloneDeep(fieldValue), cancelTokenSource });
        throw err;
    }

    onUploadProgress?.({ isInProgress: true, progress: 0, fieldValue: cloneDeep(fieldValue), cancelTokenSource });

    if (!fieldValue.uploadUrl) {
        fieldValue.status = STATUS_UPLOAD_FAILED;
        onUploadProgress?.({ isInProgress: false, progress: 0, fieldValue: cloneDeep(fieldValue), cancelTokenSource });
        throw new Error('No upload URL returned from the server.');
    }

    try {
        await axios.put(fieldValue.uploadUrl, file, {
            onUploadProgress: ev => {
                const computedPercentage = Math.round((ev.loaded / (ev.total ?? 1)) * 100);
                onUploadProgress?.({
                    isInProgress: true,
                    progress: computedPercentage,
                    fieldValue: cloneDeep(fieldValue),
                    cancelTokenSource,
                });
            },
            cancelToken: cancelTokenSource.token,
        });
        fieldValue.status = STATUS_UPLOADED;
    } catch (err) {
        if (axios.isCancel(err)) {
            fieldValue.status = STATUS_CANCELLED;
            onUploadProgress?.({
                isInProgress: false,
                progress: 0,
                fieldValue: cloneDeep(fieldValue),
                cancelTokenSource,
            });

            return fieldValue as FileDepositValue;
        }
        fieldValue.status = STATUS_UPLOAD_FAILED;
        onUploadProgress?.({
            isInProgress: false,
            progress: 0,
            fieldValue: cloneDeep(fieldValue),
            cancelTokenSource,
        });
        throw err;
    }

    onUploadProgress?.({ isInProgress: true, progress: 100, fieldValue: cloneDeep(fieldValue), cancelTokenSource });

    delete fieldValue.uploadUrl;
    const fieldValueCopy = cloneDeep(fieldValue);
    delete fieldValueCopy._createStamp;
    delete fieldValueCopy._createUser;
    const updateResponse = await targetNodeMutations
        .update({ _id: true, downloadUrl: true, status: true }, { data: fieldValueCopy })
        .execute();

    fieldValue.downloadUrl = normalizeDevUrl(updateResponse.downloadUrl || '');
    fieldValue.status = updateResponse.status;
    onUploadProgress?.({ isInProgress: false, progress: 100, fieldValue: cloneDeep(fieldValue), cancelTokenSource });

    return fieldValue as FileDepositValue;
}

export function showNotAllowedTypeToast(type: string): void {
    const friendlyNameType = getMimeTypeUserFriendlyName(type);
    showToast(
        localize('@sage/xtrem-ui/invalid-file-type-message', '{{0}} is not allowed for security reasons.', [
            friendlyNameType,
        ]),
        { type: 'error' },
    );
}

export function getMimeTypeFromExtension(filename: string): string {
    const extension = filename.split('.').pop();
    if (extension && attachmentsMimeTypesByExtention[extension]) {
        return attachmentsMimeTypesByExtention[extension][0];
    }
    return '';
}

export const getMimeTypeUserFriendlyName = (mimeType: string): string => {
    const dict = {
        'application/atom+xml': localize('@sage/xtrem-ui/mime-type/application/atom+xml', 'Atom XML Feed'),
        'application/ecmascript': localize('@sage/xtrem-ui/mime-type/application/ecmascript', 'ECMAScript'),
        'application/font-woff': localize('@sage/xtrem-ui/mime-type/application/font-woff', 'Web Open Font Format'),
        'application/font-woff2': localize('@sage/xtrem-ui/mime-type/application/font-woff2', 'Web Open Font Format 2'),
        'application/graphql': localize('@sage/xtrem-ui/mime-type/application/graphql', 'GraphQL Query'),
        'application/java-archive': localize('@sage/xtrem-ui/mime-type/application/java-archive', 'Java Archive'),
        'application/javascript': localize('@sage/xtrem-ui/mime-type/application/javascript', 'JavaScript'),
        'application/json': localize('@sage/xtrem-ui/mime-type/application/json', 'JSON'),
        'application/ld+json': localize('@sage/xtrem-ui/mime-type/application/ld+json', 'JSON-LD Document'),
        'application/mac-binhex40': localize('@sage/xtrem-ui/mime-type/application/mac-binhex40', 'BinHex Archive'),
        'application/mathml+xml': localize('@sage/xtrem-ui/mime-type/application/mathml+xml', 'MathML Document'),
        'application/msword': localize('@sage/xtrem-ui/mime-type/application/msword', 'MS Word Document'),
        'application/octet-stream': localize('@sage/xtrem-ui/mime-type/application/octet-stream', 'Binary Data'),
        'application/pdf': localize('@sage/xtrem-ui/mime-type/application/pdf', 'PDF Document'),
        'application/postscript': localize('@sage/xtrem-ui/mime-type/application/postscript', 'PostScript'),
        'application/rdf+xml': localize('@sage/xtrem-ui/mime-type/application/rdf+xml', 'RDF Document'),
        'application/rss+xml': localize('@sage/xtrem-ui/mime-type/application/rss+xml', 'RSS XML Feed'),
        'application/rtf': localize('@sage/xtrem-ui/mime-type/application/rtf', 'Rich Text Format'),
        'application/sql': localize('@sage/xtrem-ui/mime-type/application/sql', 'SQL Database'),
        'application/vnd.amazon.ebook': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.amazon.ebook',
            'Amazon Kindle eBook',
        ),
        'application/vnd.android.package-archive': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.android.package-archive',
            'Android APK',
        ),
        'application/vnd.apple.installer+xml': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.apple.installer+xml',
            'Apple Installer Package',
        ),
        'application/vnd.apple.keynote': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.apple.keynote',
            'Apple Keynote',
        ),
        'application/vnd.apple.numbers': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.apple.numbers',
            'Apple Numbers',
        ),
        'application/vnd.apple.pages': localize('@sage/xtrem-ui/mime-type/application/vnd.apple.pages', 'Apple Pages'),
        'application/vnd.google-earth.kml+xml': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.google-earth.kml+xml',
            'Google Earth KML',
        ),
        'application/vnd.google-earth.kmz': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.google-earth.kmz',
            'Google Earth KMZ',
        ),
        'application/vnd.mozilla.xul+xml': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.mozilla.xul+xml',
            'Mozilla XUL',
        ),
        'application/vnd.ms-access': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.ms-access',
            'Microsoft Access Database',
        ),
        'application/vnd.ms-excel': localize('@sage/xtrem-ui/mime-type/application/vnd.ms-excel', 'MS Excel Document'),
        'application/vnd.ms-fontobject': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.ms-fontobject',
            'Embedded OpenType Font',
        ),
        'application/vnd.ms-powerpoint': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.ms-powerpoint',
            'MS PowerPoint Document',
        ),
        'application/vnd.ms-project': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.ms-project',
            'Microsoft Project Document',
        ),
        'application/vnd.ms-visio.drawing': localize(
            '@sage/xtrem-ui/mime-type/application/ms-visio',
            'MS Visio document',
        ),
        'application/vnd.ms-visio': localize('@sage/xtrem-ui/mime-type/application/ms-visio', 'MS Visio document'),
        'application/vnd.ms-visio.stencil': localize(
            '@sage/xtrem-ui/mime-type/application/ms-visio',
            'MS Visio document',
        ),
        'application/vnd.ms-visio.stencil.macroEnabled.12': localize(
            '@sage/xtrem-ui/mime-type/application/ms-visio.stencil.macroEnabled.12',
            'MS Visio document with macros',
        ),
        'application/vnd.ms-visio.template': localize(
            '@sage/xtrem-ui/mime-type/application/ms-visio.template',
            'MS Visio template',
        ),
        'application/vnd.oasis.opendocument.graphics': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.graphics',
            'OpenDocument Graphics',
        ),
        'application/vnd.oasis.opendocument.presentation': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.presentation',
            'OpenDocument Presentation',
        ),
        'application/vnd.oasis.opendocument.spreadsheet': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.spreadsheet',
            'OpenDocument Spreadsheet',
        ),
        'application/vnd.oasis.opendocument.text': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.oasis.opendocument.text',
            'OpenDocument Text',
        ),
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'MS PowerPoint Document',
        ),
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'MS Excel Document',
        ),
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': localize(
            '@sage/xtrem-ui/mime-type/application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'MS Word Document',
        ),
        'application/vnd.rn-realmedia': localize('@sage/xtrem-ui/mime-type/application/vnd.rn-realmedia', 'RealMedia'),
        'application/vnd.wap.wmlc': localize('@sage/xtrem-ui/mime-type/application/vnd.wap.wmlc', 'WMLC Document'),
        'application/x-7z-compressed': localize('@sage/xtrem-ui/mime-type/application/x-7z-compressed', '7z Archive'),
        'application/x-abiword': localize('@sage/xtrem-ui/mime-type/application/x-abiword', 'AbiWord Document'),
        'application/x-bzip': localize('@sage/xtrem-ui/mime-type/application/x-bzip', 'Bzip Archive'),
        'application/x-bzip2': localize('@sage/xtrem-ui/mime-type/application/x-bzip2', 'Bzip2 Archive'),
        'application/x-cd-image': localize('@sage/xtrem-ui/mime-type/application/x-cd-image', 'CD Image'),
        'application/x-chrome-extension': localize(
            '@sage/xtrem-ui/mime-type/application/x-chrome-extension',
            'Chrome Extension',
        ),
        'application/x-cocoa': localize('@sage/xtrem-ui/mime-type/application/x-cocoa', 'Cocoa Archive'),
        'application/x-csh': localize('@sage/xtrem-ui/mime-type/application/x-csh', 'C Shell Script'),
        'application/x-deb': localize('@sage/xtrem-ui/mime-type/application/x-deb', 'Debian Package'),
        'application/x-dvi': localize('@sage/xtrem-ui/mime-type/application/x-dvi', 'DVI Document'),
        'application/x-font-opentype': localize(
            '@sage/xtrem-ui/mime-type/application/x-font-opentype',
            'OpenType Font',
        ),
        'application/x-font-otf': localize('@sage/xtrem-ui/mime-type/application/x-font-otf', 'OpenType Font'),
        'application/x-font-ttf': localize('@sage/xtrem-ui/mime-type/application/x-font-ttf', 'TrueType Font'),
        'application/x-font-woff': localize('@sage/xtrem-ui/mime-type/application/x-font-woff', 'Web Open Font Format'),
        'application/x-font-woff2': localize(
            '@sage/xtrem-ui/mime-type/application/x-font-woff2',
            'Web Open Font Format 2',
        ),
        'application/x-gtar': localize('@sage/xtrem-ui/mime-type/application/x-gtar', 'GNU Tar Archive'),
        'application/x-gzip': localize('@sage/xtrem-ui/mime-type/application/x-gzip', 'GZIP Archive'),
        'application/x-hdf': localize('@sage/xtrem-ui/mime-type/application/x-hdf', 'Hierarchical Data Format (HDF)'),
        'application/x-httpd-php-source': localize(
            '@sage/xtrem-ui/mime-type/application/x-httpd-php-source',
            'PHP Source Code',
        ),
        'application/x-httpd-php': localize('@sage/xtrem-ui/mime-type/application/x-httpd-php', 'PHP Script'),
        'application/x-java-applet': localize('@sage/xtrem-ui/mime-type/application/x-java-applet', 'Java Applet'),
        'application/x-java-archive-diff': localize(
            '@sage/xtrem-ui/mime-type/application/x-java-archive-diff',
            'Java Archive Diff',
        ),
        'application/x-java-archive': localize(
            '@sage/xtrem-ui/mime-type/application/x-java-archive',
            'Java Archive (JAR)',
        ),
        'application/x-java-jnlp-file': localize('@sage/xtrem-ui/mime-type/application/x-java-jnlp-file', 'JNLP File'),
        'application/x-javascript': localize('@sage/xtrem-ui/mime-type/application/x-javascript', 'Old JavaScript'),
        'application/x-latex': localize('@sage/xtrem-ui/mime-type/application/x-latex', 'LaTeX Document'),
        'application/x-lzh-compressed': localize(
            '@sage/xtrem-ui/mime-type/application/x-lzh-compressed',
            'LZH Archive',
        ),
        'application/x-makeself': localize('@sage/xtrem-ui/mime-type/application/x-makeself', 'Makeself Archive'),
        'application/x-mif': localize('@sage/xtrem-ui/mime-type/application/x-mif', 'FrameMaker Interchange Format'),
        'application/x-msaccess': localize('@sage/xtrem-ui/mime-type/application/x-msaccess', 'MS Access Database'),
        'application/x-msdownload': localize('@sage/xtrem-ui/mime-type/application/x-msdownload', 'MS Download'),
        'application/x-msmetafile': localize('@sage/xtrem-ui/mime-type/application/x-msmetafile', 'Windows Metafile'),
        'application/x-msmoney': localize('@sage/xtrem-ui/mime-type/application/x-msmoney', 'MS Money'),
        'application/x-perl': localize('@sage/xtrem-ui/mime-type/application/x-perl', 'Perl Script'),
        'application/x-pilot': localize('@sage/xtrem-ui/mime-type/application/x-pilot', 'Pilot Archive'),
        'application/x-rar-compressed': localize(
            '@sage/xtrem-ui/mime-type/application/x-rar-compressed',
            'RAR Archive',
        ),
        'application/x-redhat-package-manager': localize(
            '@sage/xtrem-ui/mime-type/application/x-redhat-package-manager',
            'RedHat Package',
        ),
        'application/x-rpm': localize('@sage/xtrem-ui/mime-type/application/x-rpm', 'RPM Package'),
        'application/x-sea': localize('@sage/xtrem-ui/mime-type/application/x-sea', 'Sea Archive'),
        'application/x-sh': localize('@sage/xtrem-ui/mime-type/application/x-sh', 'Bash Shell Script'),
        'application/x-shockwave-flash': localize(
            '@sage/xtrem-ui/mime-type/application/x-shockwave-flash',
            'Flash Animation',
        ),
        'application/x-sql': localize('@sage/xtrem-ui/mime-type/application/x-sql', 'SQL Database'),
        'application/x-stuffit': localize('@sage/xtrem-ui/mime-type/application/x-stuffit', 'StuffIt Archive'),
        'application/x-tar': localize('@sage/xtrem-ui/mime-type/application/x-tar', 'TAR Archive'),
        'application/x-tcl': localize('@sage/xtrem-ui/mime-type/application/x-tcl', 'Tcl Script'),
        'application/x-tex': localize('@sage/xtrem-ui/mime-type/application/x-tex', 'TeX Document'),
        'application/x-texinfo': localize('@sage/xtrem-ui/mime-type/application/x-texinfo', 'Texinfo Document'),
        'application/x-troff': localize('@sage/xtrem-ui/mime-type/application/x-troff', 'Troff Document'),
        'application/x-vrml': localize('@sage/xtrem-ui/mime-type/application/x-vrml', 'VRML'),
        'application/x-www-form-urlencoded': localize(
            '@sage/xtrem-ui/mime-type/application/x-www-form-urlencoded',
            'Form URL Encoded Data',
        ),
        'application/x-x509-ca-cert': localize(
            '@sage/xtrem-ui/mime-type/application/x-x509-ca-cert',
            'X.509 Certificate',
        ),
        'application/x-xpinstall': localize('@sage/xtrem-ui/mime-type/application/x-xpinstall', 'Mozilla XPI Install'),
        'application/xhtml+xml': localize('@sage/xtrem-ui/mime-type/application/xhtml+xml', 'XHTML'),
        'application/xml': localize('@sage/xtrem-ui/mime-type/application/xml', 'XML'),
        'application/xslt+xml': localize('@sage/xtrem-ui/mime-type/application/xslt+xml', 'XSLT'),
        'application/zip': localize('@sage/xtrem-ui/mime-type/application/zip', 'ZIP Archive'),
        'audio/aac': localize('@sage/xtrem-ui/mime-type/audio/aac', 'AAC Audio'),
        'audio/amr': localize('@sage/xtrem-ui/mime-type/audio/amr', 'AMR Audio'),
        'audio/midi': localize('@sage/xtrem-ui/mime-type/audio/midi', 'MIDI Audio'),
        'audio/mpeg': localize('@sage/xtrem-ui/mime-type/audio/mpeg', 'MP3 Audio'),
        'audio/ogg': localize('@sage/xtrem-ui/mime-type/audio/ogg', 'Ogg Audio'),
        'audio/vnd.rn-realaudio': localize('@sage/xtrem-ui/mime-type/audio/vnd.rn-realaudio', 'RealAudio'),
        'audio/wav': localize('@sage/xtrem-ui/mime-type/audio/wav', 'WAV Audio'),
        'audio/x-m4a': localize('@sage/xtrem-ui/mime-type/audio/x-m4a', 'M4A Audio'),
        'audio/x-matroska': localize('@sage/xtrem-ui/mime-type/audio/x-matroska', 'Matroska Audio'),
        'audio/x-mpegurl': localize('@sage/xtrem-ui/mime-type/audio/x-mpegurl', 'MPEG URL Stream'),
        'audio/x-ms-wax': localize('@sage/xtrem-ui/mime-type/audio/x-ms-wax', 'WMA Audio Playlist'),
        'audio/x-ms-wma': localize('@sage/xtrem-ui/mime-type/audio/x-ms-wma', 'Windows Media Audio'),
        'audio/x-pn-realaudio-plugin': localize(
            '@sage/xtrem-ui/mime-type/audio/x-pn-realaudio-plugin',
            'RealAudio Plugin',
        ),
        'audio/x-pn-realaudio': localize('@sage/xtrem-ui/mime-type/audio/x-pn-realaudio', 'RealAudio'),
        'audio/x-realaudio': localize('@sage/xtrem-ui/mime-type/audio/x-realaudio', 'RealAudio'),
        'audio/x-wav': localize('@sage/xtrem-ui/mime-type/audio/x-wav', 'WAV Audio'),
        'chemical/x-pdb': localize('@sage/xtrem-ui/mime-type/chemical/x-pdb', 'Protein Data Bank'),
        'image/bmp': localize('@sage/xtrem-ui/mime-type/image/bmp', 'Bitmap Image'),
        'image/cgm': localize('@sage/xtrem-ui/mime-type/image/cgm', 'Computer Graphics Metafile'),
        'image/gif': localize('@sage/xtrem-ui/mime-type/image/gif', 'GIF Image'),
        'image/jpeg': localize('@sage/xtrem-ui/mime-type/image/jpeg', 'JPEG Image'),
        'image/png': localize('@sage/xtrem-ui/mime-type/image/png', 'PNG Image'),
        'image/svg+xml': localize('@sage/xtrem-ui/mime-type/image/svg+xml', 'SVG Image'),
        'image/tiff': localize('@sage/xtrem-ui/mime-type/image/tiff', 'TIFF Image'),
        'image/vnd.microsoft.icon': localize('@sage/xtrem-ui/mime-type/image/vnd.microsoft.icon', 'ICO Image'),
        'image/vnd.wap.wbmp': localize('@sage/xtrem-ui/mime-type/image/vnd.wap.wbmp', 'WBMP Image'),
        'image/webp': localize('@sage/xtrem-ui/mime-type/image/webp', 'WebP Image'),
        'image/x-cmu-raster': localize('@sage/xtrem-ui/mime-type/image/x-cmu-raster', 'CMU Image'),
        'image/x-icon': localize('@sage/xtrem-ui/mime-type/image/x-icon', 'Icon Image'),
        'image/x-jng': localize('@sage/xtrem-ui/mime-type/image/x-jng', 'JNG Image'),
        'image/x-ms-bmp': localize('@sage/xtrem-ui/mime-type/image/x-ms-bmp', 'Bitmap Image'),
        'image/x-portable-anymap': localize(
            '@sage/xtrem-ui/mime-type/image/x-portable-anymap',
            'Portable AnyMap Image',
        ),
        'image/x-portable-bitmap': localize(
            '@sage/xtrem-ui/mime-type/image/x-portable-bitmap',
            'Portable Bitmap Image',
        ),
        'image/x-portable-graymap': localize(
            '@sage/xtrem-ui/mime-type/image/x-portable-graymap',
            'Portable Graymap Image',
        ),
        'image/x-portable-pixmap': localize(
            '@sage/xtrem-ui/mime-type/image/x-portable-pixmap',
            'Portable Pixmap Image',
        ),
        'image/x-rgb': localize('@sage/xtrem-ui/mime-type/image/x-rgb', 'RGB Image'),
        'image/x-xbitmap': localize('@sage/xtrem-ui/mime-type/image/x-xbitmap', 'X Bitmap Image'),
        'image/x-xpixmap': localize('@sage/xtrem-ui/mime-type/image/x-xpixmap', 'X Pixmap Image'),
        'image/x-xwindowdump': localize('@sage/xtrem-ui/mime-type/image/x-xwindowdump', 'X Window Dump Image'),
        'model/vrml': localize('@sage/xtrem-ui/mime-type/model/vrml', 'VRML Model'),
        'multipart/form-data': localize('@sage/xtrem-ui/mime-type/multipart/form-data', 'Multipart Form Data'),
        'text/cache-manifest': localize('@sage/xtrem-ui/mime-type/text/cache-manifest', 'Cache Manifest'),
        'text/calendar': localize('@sage/xtrem-ui/mime-type/text/calendar', 'iCalendar'),
        'text/css': localize('@sage/xtrem-ui/mime-type/text/css', 'CSS'),
        'text/csv': localize('@sage/xtrem-ui/mime-type/text/csv', 'CSV (Comma-Separated Values)'),
        'text/ecmascript': localize('@sage/xtrem-ui/mime-type/text/ecmascript', 'ECMAScript'),
        'text/html': localize('@sage/xtrem-ui/mime-type/text/html', 'HTML'),
        'text/javascript': localize('@sage/xtrem-ui/mime-type/text/javascript', 'JavaScript'),
        'text/markdown': localize('@sage/xtrem-ui/mime-type/text/markdown', 'Markdown Document'),
        'text/mathml': localize('@sage/xtrem-ui/mime-type/text/mathml', 'MathML'),
        'text/plain': localize('@sage/xtrem-ui/mime-type/text/plain', 'Plain Text'),
        'text/richtext': localize('@sage/xtrem-ui/mime-type/text/richtext', 'Rich Text'),
        'text/rtf': localize('@sage/xtrem-ui/mime-type/text/rtf', 'Rich Text Format'),
        'text/tab-separated-values': localize(
            '@sage/xtrem-ui/mime-type/text/tab-separated-values',
            'Tab-Separated Values',
        ),
        'text/vnd.sun.j2me.app-descriptor': localize(
            '@sage/xtrem-ui/mime-type/text/vnd.sun.j2me.app-descriptor',
            'J2ME App Descriptor',
        ),
        'text/vnd.wap.wml': localize('@sage/xtrem-ui/mime-type/text/vnd.wap.wml', 'WML'),
        'text/vnd.wap.wmlscript': localize('@sage/xtrem-ui/mime-type/text/vnd.wap.wmlscript', 'WMLScript'),
        'text/vtt': localize('@sage/xtrem-ui/mime-type/text/vtt', 'WebVTT'),
        'text/x-component': localize('@sage/xtrem-ui/mime-type/text/x-component', 'HTC Component'),
        'text/x-cross-domain-policy': localize(
            '@sage/xtrem-ui/mime-type/text/x-cross-domain-policy',
            'Cross-domain Policy',
        ),
        'text/x-fortran': localize('@sage/xtrem-ui/mime-type/text/x-fortran', 'Fortran Source Code'),
        'text/xml': localize('@sage/xtrem-ui/mime-type/text/xml', 'XML Document'),
        'video/3gpp': localize('@sage/xtrem-ui/mime-type/video/3gpp', '3GPP Video'),
        'video/mp2t': localize('@sage/xtrem-ui/mime-type/video/mp2t', 'MPEG-2 TS Video'),
        'video/mp4': localize('@sage/xtrem-ui/mime-type/video/mp4', 'MP4 Video'),
        'video/mpeg': localize('@sage/xtrem-ui/mime-type/video/mpeg', 'MPEG Video'),
        'video/ogg': localize('@sage/xtrem-ui/mime-type/video/ogg', 'Ogg Video'),
        'video/quicktime': localize('@sage/xtrem-ui/mime-type/video/quicktime', 'QuickTime Video'),
        'video/vnd.rn-realvideo': localize('@sage/xtrem-ui/mime-type/video/vnd.rn-realvideo', 'RealVideo'),
        'video/webm': localize('@sage/xtrem-ui/mime-type/video/webm', 'WebM Video'),
        'video/x-flv': localize('@sage/xtrem-ui/mime-type/video/x-flv', 'FLV Video'),
        'video/x-m4v': localize('@sage/xtrem-ui/mime-type/video/x-m4v', 'M4V Video'),
        'video/x-matroska': localize('@sage/xtrem-ui/mime-type/video/x-matroska', 'Matroska Video'),
        'video/x-mng': localize('@sage/xtrem-ui/mime-type/video/x-mng', 'MNG Video'),
        'video/x-ms-asf': localize('@sage/xtrem-ui/mime-type/video/x-ms-asf', 'ASF Video'),
        'video/x-ms-wmv': localize('@sage/xtrem-ui/mime-type/video/x-ms-wmv', 'WMV Video'),
        'video/x-ms-wvx': localize('@sage/xtrem-ui/mime-type/video/x-ms-wvx', 'WMV Video Playlist'),
        'video/x-msvideo': localize('@sage/xtrem-ui/mime-type/video/x-msvideo', 'AVI Video'),
    };

    return get(dict, mimeType) || mimeType;
};
