import {
    DateValue,
    Datetime,
    datePropertyValueToDateString,
    isStringDate,
    date as xtremDate,
} from '@sage/xtrem-date-time';
import * as xtremDecimal from '@sage/xtrem-decimal';
import type { LocalizeLocale, Unit } from '@sage/xtrem-shared';
import { isNil } from 'lodash';
import type { HasUnit, NestedHasUnit } from '../component/field/traits';
import { getStore } from '../redux';
import type { ScreenBase } from '../service/screen-base';
import { resolveByValue } from './resolve-value-utils';
import { splitValueToMergedValue } from './transformers';
import type { DatePropertyValue, DateTimePropertyValue, ValueOrCallbackWithFieldValue } from './types';

const defaultFormat = 'YYYY-MM-DD';

export const getIsoDate = (date: DatePropertyValue | undefined | null): string | undefined => {
    if (date) {
        if (date instanceof Date) {
            return xtremDate.fromJsDate(date).format('YYYY-MM-DD');
        }
        if (typeof date === 'string' && date.match(/[1-2][0-9]{3}-[0-1][0-9]-[0-3][0-9]/)) {
            return date;
        }

        throw new Error(`Invalid date: ${date}`);
    }

    return undefined;
};

export const isInvalidJsDate = (jsDate: Date): boolean => Number.isNaN(jsDate.getTime());

export const dateToString = (date: Date, dateFieldFormat: string = defaultFormat): string => {
    return DateValue.parse(datePropertyValueToDateString(date)).format(dateFieldFormat);
};

export const isDateOrDateTime = (value: DatePropertyValue | DateTimePropertyValue): 'date' | 'datetime' | null => {
    if (typeof value === 'string' && isStringDate(value)) {
        return value.length === 10 ? 'date' : 'datetime';
    }

    if (value instanceof DateValue) {
        return 'date';
    }

    if (value instanceof Datetime) {
        return 'datetime';
    }

    if (value instanceof Date) {
        return 'datetime';
    }

    return null;
};

export const getFirstDayOfMonth = (date: DateValue): DateValue => date.begOfMonth();

export const getLastDayOfMonth = (date: DateValue): DateValue => date.endOfMonth();

export interface FormatNumericValueArgs {
    screenId: string;
    value: any;
    ignoreLocale?: boolean;
    scale?: ValueOrCallbackWithFieldValue<ScreenBase, number | null, string>;
    rowValue?: any;
    locale?: string;
    unitScale?: number;
}

export function getScalePrefixPostfixFromUnit(
    screenId: string,
    locale: LocalizeLocale,
    fieldProperties: HasUnit<any> | NestedHasUnit<any>,
    rowValue: any = {},
): { scale?: number; prefix?: string; postfix?: string } | null {
    if (!fieldProperties.unit) {
        return null;
    }

    const resolvedUnitMode = fieldProperties.unitMode ?? 'currency';

    const resolvedUnit = resolveByValue<Unit>({
        fieldValue: rowValue?._id,
        propertyValue: fieldProperties.unit,
        rowValue: splitValueToMergedValue(rowValue),
        skipHexFormat: true,
        screenId,
    });

    if (!resolvedUnit) {
        return null;
    }

    if (resolvedUnitMode === 'unitOfMeasure') {
        return {
            scale: resolvedUnit.decimalDigits ?? 2,
            postfix: resolvedUnit.symbol,
        };
    }

    switch (locale) {
        case 'en-US':
        case 'en-GB':
            return {
                scale: resolvedUnit.decimalDigits ?? 2,
                prefix: resolvedUnit.symbol,
            };
        default:
            return {
                scale: resolvedUnit.decimalDigits ?? 2,
                postfix: resolvedUnit.symbol,
            };
    }
}

export const formatNumericValue = ({
    screenId,
    value,
    ignoreLocale = false,
    scale,
    rowValue,
    locale,
    unitScale,
}: FormatNumericValueArgs): string => {
    const localeToUse = locale || getStore().getState()?.applicationContext?.locale || 'en-US';
    const tempValue = parseFloat(value);
    if (Number.isNaN(Number(tempValue))) {
        return '';
    }

    if (!isNil(scale) || !isNil(unitScale)) {
        const resolvedScale =
            resolveByValue({
                fieldValue: value,
                propertyValue: scale,
                rowValue: splitValueToMergedValue(rowValue),
                skipHexFormat: true,
                screenId,
            }) ?? unitScale;

        const formatOptions = {
            minimumFractionDigits: resolvedScale,
            maximumFractionDigits: resolvedScale,
            ...(ignoreLocale && { useGrouping: false }),
        };

        return new Intl.NumberFormat(ignoreLocale ? 'en-US' : localeToUse || 'en-US', formatOptions).format(tempValue);
    }

    return new Intl.NumberFormat(ignoreLocale ? 'en-US' : localeToUse, {
        ...(ignoreLocale && { useGrouping: false }),
    }).format(tempValue);
};

export const parseLocalizedNumberStringToNumber = (
    value: string | null | undefined | number,
    separator: string,
): number | null => {
    if (typeof value === 'number') {
        return value;
    }

    if (value === undefined || value === null || String(value.trim()).length === 0) {
        return null;
    }

    if (value.trim() === '-') {
        return 0;
    }

    const isNegative = value.trim().charAt(0) === '-';

    const parts = value.trim().split(separator);
    const decimalPart = parts.length === 1 ? '0' : parts[1].trim().replace(/\D/g, '');
    const wholeNumberPart = parts[0].replace(/\D/g, '');

    return xtremDecimal.Decimal.make(
        `${isNegative ? '-' : ''}${wholeNumberPart || '0'}.${decimalPart || '0'}`,
    ).toNumber();
};

interface NumberComponents {
    integer: string;
    decimal: string;
    separator: string;
    sign: '+' | '-';
}

export const getNumberComponentsFromLocalizedNumberString = (
    value: string | null | undefined | number,
    separator: string,
): NumberComponents => {
    let internal = typeof value === 'string' ? value : typeof value === 'number' ? `${value}` : '';

    let sign: '+' | '-' = '+';
    if (internal[0] === '-' || internal[0] === '+') {
        sign = internal[0];
        internal = internal.substring(1);
    }

    const [integer, decimal] = internal.split(separator);
    return { integer: integer || '', decimal: decimal || '', separator, sign };
};

export function humanFileSize(size: number): string {
    const i = size === 0 ? 0 : Math.floor(Math.log(size) / Math.log(1024));
    return `${+(size / 1024 ** i).toFixed(2) * 1} ${['B', 'kB', 'MB', 'GB', 'TB'][i]}`;
}
