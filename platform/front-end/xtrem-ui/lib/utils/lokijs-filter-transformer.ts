/* eslint-disable no-param-reassign */
import type { Filter } from '@sage/xtrem-client';
import { flow, has, includes, reduceRight, set, without } from 'lodash';
import type { FilterReservedWord } from '../service/graphql-query-builder';
import {
    filterReservedWords,
    RESTRICTED_COLUMN_ALL_PREFIX,
    RESTRICTED_COLUMN_PREFIX,
} from '../service/graphql-query-builder';
import { pascalCase } from './transformers';
import { reduceDict } from './type-utils';
import type { Dict } from '@sage/xtrem-shared';

/* KEY IDENTITY */

const isGraphQLFilterReservedWord = (key: any): boolean =>
    typeof key === 'string' && filterReservedWords.includes(key as string as FilterReservedWord);
const isTransformerReservedWord = (key: any): boolean =>
    typeof key === 'string' && ['$and', '$$gte', '$empty'].includes(key);
const isInternalReservedWord = (key: any): boolean => typeof key === 'string' && key.startsWith('__');
const isId = (key: any): boolean => typeof key === 'string' && key === '_id';
const isApplication = (key: any): boolean =>
    typeof key === 'string' &&
    !isGraphQLFilterReservedWord(key) &&
    !isTransformerReservedWord(key) &&
    !isInternalReservedWord(key);

/* VALUE REDUCTORS */

const normalizeRoot = ([value, path]: [any, (string | number)[]]): [any, (string | number)[]] => {
    // TODO: Refactor into andNormalization reductor
    if (typeof path[0] === 'number') {
        return [value, path.slice(1)];
    }
    return [value, path];
};

const nullNormalization = ([value, path]: [any, (string | number)[]]): [any, (string | number)[]] => {
    if (value === null) {
        const [key] = path.slice(-1);
        if (includes(['_eq', '_ne'], key)) {
            const [parentKey] = path.slice(-2);
            if (isId(parentKey)) {
                // TODO _ne is ignored for this case, as is the default behavior, maybe throw error
                return [true, [...path.slice(0, -2), '$empty']];
            }
        } else {
            if (isId(key)) {
                return [true, [...path.slice(0, -1), '$empty']];
            }
            if (isApplication(key)) {
                return [value, [...path, '_eq']];
            }
        }
    }
    return [value, path];
};

const dotdotConvention = ([value, path]: [any, (string | number)[]]): [any, (string | number)[]] => {
    const reducer = reduceRight(
        path,
        (acc: { result: (string | number)[]; dotdotGroup: string[] }, val, index, source) => {
            if (!isApplication(val)) {
                acc = { result: [val, ...acc.result], dotdotGroup: [] };
            } else {
                const lookBehind = source[index - 1];
                if (lookBehind && isApplication(lookBehind) && typeof val === 'string') {
                    acc = { result: acc.result, dotdotGroup: [val, ...acc.dotdotGroup] };
                } else {
                    const dotdotGroup = [val, ...acc.dotdotGroup];
                    acc = { result: [dotdotGroup.join('.'), ...acc.result], dotdotGroup: [] };
                }
            }
            return acc;
        },
        { result: [], dotdotGroup: [] },
    );
    const remap = reducer.result;
    return [value, remap];
};

const dollarReprefix = ([value, path]: [any, (string | number)[]]): [any, (string | number)[]] => {
    const remap = path.map(key => {
        if (typeof key === 'string') {
            const shouldTransformKey =
                !isId(key) && !isInternalReservedWord(key) && isGraphQLFilterReservedWord(key) && key.startsWith('_');
            return shouldTransformKey ? `$${key.slice(1)}` : key;
        }
        return key;
    });
    return [value, remap];
};

/* OBJECT REDUCTORS */

const regexCompactation = ([value, path]: [Dict<any>, (string | number)[]]): [Dict<any>, (string | number)[]] => {
    if (has(value, '_regex') && has(value, '_options')) {
        value._regex = [value._regex, value._options];
        delete value._options;
    }
    return [value, path];
};

const rangeCompactation = ([value, path]: [Dict<any>, (string | number)[]]): [Dict<any>, (string | number)[]] => {
    if (has(value, '_gte') && has(value, '_lte')) {
        value.$and = [{ _gte: value._gte }, { _lte: value._lte }];
        delete value._gte;
        delete value._lte;
    }
    if (has(value, '_gt') && has(value, '_lt')) {
        value.$and = [{ _gt: value._gt }, { _lt: value._lt }];
        delete value._gt;
        delete value._lt;
    }
    return [value, path];
};

const handleQuantifiers = ([value, path]: [Dict<any>, (string | number)[]]): [Dict<any>, (string | number)[]] => {
    if (has(value, '_atLeast')) {
        const key = path.pop();
        path = [...path, `${RESTRICTED_COLUMN_PREFIX}${pascalCase(key as string)}.query.totalCount`];
        value = {
            _gte: value._atLeast,
        };
        return [value, path];
    }
    if (has(value, '_atMost')) {
        const key = path.pop();
        path = [...path, `${RESTRICTED_COLUMN_PREFIX}${pascalCase(key as string)}.query.totalCount`];
        value = {
            _lte: value._atMost,
        };
        return [value, path];
    }
    if (has(value, '_none')) {
        if (value._none === true) {
            const key = path.pop();
            path = [...path, `${RESTRICTED_COLUMN_PREFIX}${pascalCase(key as string)}.query.totalCount`];
            value = { _eq: 0 };
        } else {
            delete value._none;
        }
        return [value, path];
    }
    if (has(value, '_every')) {
        if (value._every === true) {
            const key = path.pop();
            path = [...path, `${RESTRICTED_COLUMN_PREFIX}${pascalCase(key as string)}.query.totalCount`];
            value = { $$gte: `${RESTRICTED_COLUMN_ALL_PREFIX}${pascalCase(key as string)}.query.totalCount` };
        } else {
            delete value._every;
        }
        return [value, path];
    }
    return [value, path];
};

/* ARRAY REDUCTORS */

const inNormalization = ([value, path]: [Array<any>, (string | number)[]]): [Array<any>, (string | number)[]] => {
    const [key] = path.slice(-1);
    const [parentKey] = path.slice(-2);
    const [grandparentKey] = path.slice(-3);
    if (key === '_in' && isId(parentKey) && isApplication(grandparentKey) && includes(value, null)) {
        const newValue = [
            { [grandparentKey]: { _id: { _in: without([...value], null) } } },
            { [grandparentKey]: { $empty: true } },
        ];
        const newPath = [...path.slice(0, -3), '_or'];
        return [newValue, newPath];
    }
    return [value, path];
};

export const transformToLokiJsFilter = (target: Filter<any>): Filter<any> =>
    reduceDict<Filter<any>, LokiQuery<any>>()(
        target,
        {},
        (value, acc, key, path) => {
            const [remappedValue, remappedPath] = flow(
                normalizeRoot,
                nullNormalization,
                dotdotConvention,
                dollarReprefix,
            )([value, path]);
            set(acc, remappedPath, remappedValue);
            return acc;
        },
        (value, acc, key, path) => {
            const [remappedValue, remappedPath] = flow(
                regexCompactation,
                rangeCompactation,
                handleQuantifiers,
            )([value, path]);
            return [remappedValue, remappedPath];
        },
        (value, acc, key, path) => {
            const [remappedValue, remappedPath] = flow(inNormalization)([value, path]);
            return [remappedValue, remappedPath];
        },
    );
