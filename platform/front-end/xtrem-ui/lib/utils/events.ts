import { objectKeys, type Dict } from '@sage/xtrem-shared';
import { get } from 'lodash';
import type { UiComponentProperties } from '../component/abstract-ui-control-object';
import type { NestedGridProperties } from '../component/control-objects';
import type { EditableFieldControlObject } from '../component/editable-field-control-object';
import type { HasGenericErrorHandler, VoidPromise } from '../component/field/traits';
import type { NestedFieldsProperties } from '../component/nested-fields';
import type { Events, FieldDecoratorProps } from '../component/types';
import { getStore } from '../redux';
import { setGlobalLoading } from '../redux/actions';
import { errorDialog } from '../service/dialog-service';
import { PromiseTracker } from '../service/promise-tracker';
import type { ScreenBase } from '../service/screen-base';
import { getScreenElement } from '../service/screen-base-definition';
import { notifyConsumerOnError } from '../service/telemetry-service';
import { showToast } from '../service/toast-service';
import { xtremConsole } from './console';
import { resolveByValue } from './resolve-value-utils';
import { ServerError } from './server-error-transformer';
import { isDevMode } from './window';

type EventsAfter = `${Events}After`;
type ScreenUiComponentProperties = UiComponentProperties & HasGenericErrorHandler<any>;
type ElementUiComponentProperties = FieldDecoratorProps<any> & HasGenericErrorHandler<any>;
export type NestedFieldComponentProperties = NestedFieldsProperties<any> & HasGenericErrorHandler<any>;
type HandledEventContainer =
    | {
          onClick: Function;
          onError?: Function;
          onClickAfter?: Array<Function>;
          delegateErrorToCaller?: boolean;
      }
    | {
          onChange: Function;
          onError?: Function;
          delegateErrorToCaller?: boolean;
      };

const handleError = (screenId: string, error: Error): void => {
    getStore().dispatch(setGlobalLoading(false));
    errorDialog(screenId, 'Error', error);
    notifyConsumerOnError(error);
};

/**
 * Executes event and event-after callbacks with the screen scope. The event handler and arguments should be passed in to the function.
 * In case of an error occurs in the during the call back execution, the onError function is called.
 * */
const executeEventHandlerPrivate = async ({
    eventName,
    screenProperties,
    screenId,
    elementId,
    ctx,
    elementDef,
    targetDef,
    args,
}: {
    eventName: Events | EventsAfter;
    screenId: string;
    elementId: string;
    ctx: ScreenBase;
    screenProperties: ScreenUiComponentProperties;
    elementDef: ElementUiComponentProperties | ScreenUiComponentProperties;
    targetDef:
        | HandledEventContainer
        | NestedFieldComponentProperties
        | ElementUiComponentProperties
        | ScreenUiComponentProperties;
    args: any[];
}): Promise<void> => {
    const eventHandler =
        args && args[0]?.level !== undefined
            ? get(targetDef as NestedGridProperties, `levels.${args[0]}.level.${eventName}`)
            : get(targetDef, eventName);
    const errorHandler = targetDef.onError || elementDef.onError || screenProperties.onError;
    try {
        if (eventHandler) {
            if (typeof eventHandler === 'function') {
                await eventHandler.apply(ctx, args);
            } else if (typeof eventHandler === 'object') {
                // eslint-disable-next-line no-restricted-syntax
                for (const fn of eventHandler) {
                    // eslint-disable-next-line no-await-in-loop
                    await fn.apply(ctx, args);
                }
            }
        }
        const afterEvents = get(targetDef, `${eventName}After`);
        if (afterEvents) {
            await executeEventHandler(
                `${eventName}After` as EventsAfter,
                screenId,
                elementId,
                ctx,
                screenProperties,
                elementDef,
                targetDef,
                ...args,
            );
        }
    } catch (error) {
        xtremConsole.error(error);
        if ((targetDef as HandledEventContainer).delegateErrorToCaller) {
            throw error;
        } else if (errorHandler) {
            const rawResult = errorHandler.apply(ctx, [error, screenId, elementId]);
            const result = await (rawResult instanceof Promise ? rawResult : Promise.resolve(rawResult));
            if (result) {
                const isServerError = error instanceof ServerError;
                showToast(isServerError ? error.toastContent : result, {
                    type: 'error',
                    timeout: 10000,
                    language: isServerError ? 'jsx' : 'markdown',
                });
            } else if (isDevMode()) {
                xtremConsole.warn(`Silent error on ${screenId} / ${elementId}, you should handle this error.`);
                xtremConsole.warn(error);
            }
        } else {
            handleError(screenId, error);
        }
    }
};

const executeEventHandler = async (
    eventName: Events | EventsAfter,
    screenId: string,
    elementId: string,
    ctx: ScreenBase,
    screenDef: ScreenUiComponentProperties,
    elementDef: ElementUiComponentProperties | ScreenUiComponentProperties,
    targetDef:
        | HandledEventContainer
        | NestedFieldComponentProperties
        | ElementUiComponentProperties
        | ScreenUiComponentProperties,
    ...args: any[]
): Promise<void> => {
    return PromiseTracker.withTracker(() =>
        executeEventHandlerPrivate({
            eventName,
            screenId,
            elementId,
            ctx,
            screenProperties: screenDef,
            elementDef,
            targetDef,
            args,
        }),
    );
};
export const executeEventHandlerWithExternalHandler = async ({
    screenId,
    elementId,
    args,
    eventHandler,
}: {
    eventHandler: () => VoidPromise;
    screenId: string;
    elementId: string;
    args: any[];
}): Promise<void> => {
    const state = getStore().getState();

    const screenDef = state.screenDefinitions[screenId];
    if (!screenDef) {
        throw Error(`events: ${screenId} screen definition doesn't exist in current state`);
    }
    const ctx = getScreenElement(screenDef) as any;
    if (!ctx) {
        throw Error("events: screen context couldn't be built");
    }
    const screenProperties =
        screenDef.metadata.uiComponentProperties[screenId] ||
        (elementId ? screenDef.metadata.pageActions[elementId] : false);
    if (!screenProperties) {
        throw Error('events: screen or action unreachable');
    }
    const element = (elementId && screenDef.metadata.uiComponentProperties[elementId]) || screenProperties;

    return PromiseTracker.withTracker(() =>
        executeEventHandlerPrivate({
            eventName: 'onClick',
            screenId,
            elementId,
            ctx,
            screenProperties,
            targetDef: { onClick: eventHandler },
            elementDef: element,
            args,
        }),
    );
};

type ForScreen<Rest extends unknown[]> = (screenId: string, ...args: Rest) => Promise<void>;
type ForField<Rest extends unknown[]> = (screenId: string, elementId: string, ...args: Rest) => Promise<void>;
type ForNestedField<Rest extends unknown[]> = (
    screenId: string,
    elementId: string,
    nestedDef: NestedFieldComponentProperties,
    ...args: Rest
) => Promise<void>;

type TriggerEventForScreen = ForScreen<
    | [eventName: Extract<Events, 'onLoad' | 'onOpen'>]
    | [eventName: Extract<Events, 'onClose'>, isWizardFinished?: boolean]
    | [eventName: Extract<Events, 'onDirtyStateUpdated' | 'on360ViewSwitched'>, state: boolean]
>;
type TriggerEventForField = ForField<
    | [eventName: Extract<Events, 'onActive' | 'onInactive' | 'onAllDataLoaded' | 'onDataLoaded'>]
    | [eventName: Extract<Events, 'onChange'>]
    | [eventName: Extract<Events, 'onClick'>]
    | [eventName: Extract<Events, 'onReady'>]
    | [eventName: Extract<Events, 'onHeaderValueChanged' | 'onFooterValueChanged'>, newValue?: string]
    | [eventName: Extract<Events, 'onEmptyStateLinkClick'>]
    | [eventName: Extract<Events, 'onEmptyStateLinkClick'>, parentId?: string, level?: number]
    | [eventName: Extract<Events, 'onClick'>, rowId: string, rowData: Dict<any>, level?: number, ancestorIds?: string[]] // dropdown action click
    | [
          eventName: Extract<
              Events,
              | 'onChange'
              | 'onRecordAdded'
              | 'onRecordClick'
              | 'onRecordRemoved'
              | 'onRowAdded'
              | 'onRowActivated'
              | 'onRowClick'
              | 'onRowDeactivated'
              | 'onRowSelected'
              | 'onRowUnselected'
          >,
          rowId: string,
          rowData: Dict<any>,
          level?: number,
          ancestorIds?: string[],
      ]
    | [eventName: Extract<Events, 'onRecordClick'>, rowData: Dict<any>]
    | [
          eventName: Extract<Events, 'onEventClick'>,
          eventId: string,
          event: Dict<any> | null,
          isModifierKeyPushed: boolean,
      ]
    | [eventName: Extract<Events, 'onDayClick'>, date: Date]
    | [eventName: Extract<Events, 'onInputValueChange'>, inputValue: string]
    | [eventName: Extract<Events, 'onFileInfo'>, mimetype: string, length: number, fileName: string]
    | [eventName: Extract<Events, 'onCloseLookupDialog' | 'onOpenLookupDialog'>]
>;
type TriggerEventForNestedField = ForNestedField<
    | [eventName: Extract<Events, 'onClick'>, rowId: string, rowData: Dict<any>, level?: number, ancestorIds?: string[]]
    | [eventName: Extract<Events, 'onLevelExpanded'>, parentId: string, children: Dict<any>[]]
    | [
          eventName: Extract<Events, 'onChange'>,
          columnId: string,
          rowData: Dict<any>,
          level?: number,
          ancestorIds?: string[],
      ]
>;

type ForHandledEventContainer<Rest extends unknown[]> = (
    screenId: string,
    elementId: string,
    handledEventContainer: HandledEventContainer,
    ...args: Rest
) => Promise<void>;
type TriggerHandledEvent = ForHandledEventContainer<
    [] | [rowId: string, rowData: Dict<any>] | [rowId: string, rowData: Dict<any>, level: number, ancestorIds: string[]]
>;

const isDisabled = (screenId: string, elementId: string): boolean => {
    const state = getStore().getState();
    const fieldValue = (
        state.screenDefinitions[screenId].metadata.controlObjects[elementId] as EditableFieldControlObject<
            any,
            any,
            any
        >
    )?.value;
    const element = state.screenDefinitions[screenId].metadata.uiComponentProperties[elementId];
    return (
        element &&
        resolveByValue({
            propertyValue: element.isDisabled,
            skipHexFormat: true,
            fieldValue,
            screenId,
            rowValue: null,
        })
    );
};

const produceExecutionArguments: (
    eventName: Events,
    screenId: string,
    elementId?: string,
    explicitDef?: NestedFieldComponentProperties | HandledEventContainer,
) =>
    | false
    | [
          eventName: Events,
          screenId: string,
          elementId: string,
          screenCtx: ScreenBase,
          screen: ScreenUiComponentProperties,
          element: ElementUiComponentProperties | ScreenUiComponentProperties,
          targetDef:
              | HandledEventContainer
              | NestedFieldComponentProperties
              | ElementUiComponentProperties
              | ScreenUiComponentProperties,
      ] = (eventName, screenId, elementId, explicitDef) => {
    const state = getStore().getState();
    const screenDef = state.screenDefinitions[screenId];
    if (!screenDef) {
        throw Error(`events: ${screenId} screen definition doesn't exist in current state`);
    }
    const screenCtx = getScreenElement(screenDef);
    if (!screenCtx) {
        throw Error("events: screen context couldn't be built");
    }
    const screen =
        screenDef.metadata.uiComponentProperties[screenId] ||
        (elementId ? screenDef.metadata.pageActions[elementId] : false);
    if (!screen) {
        throw Error('events: screen or action unreachable');
    }
    const element = (elementId && screenDef.metadata.uiComponentProperties[elementId]) || screen;
    const targetDef = explicitDef || element;
    return [eventName, screenId, elementId || screenId, screenCtx, screen, element, targetDef];
};

export const triggerScreenEvent: TriggerEventForScreen = async (screenId, eventName, ...rest) => {
    const args = produceExecutionArguments(eventName, screenId);
    if (args) {
        await executeEventHandler(...args, ...rest);
    }
};

export const triggerFieldEvent: TriggerEventForField = async (screenId, elementId, eventName, ...rest) => {
    const args = produceExecutionArguments(eventName, screenId, elementId);
    if (args && !isDisabled(screenId, elementId)) {
        await executeEventHandler(...args, ...rest);
    }
};

export const triggerNestedFieldEvent: TriggerEventForNestedField = async (
    screenId,
    elementId,
    columnDefinition,
    eventName,
    ...rest
) => {
    const args = produceExecutionArguments(eventName, screenId, elementId, columnDefinition);
    if (args) {
        await executeEventHandler(...args, ...rest);
    }
};

export const triggerHandledEvent: TriggerHandledEvent = async (screenId, elementId, handledEventContainer, ...rest) => {
    const eventName = objectKeys(handledEventContainer).find(e => e !== 'onError' && !e.endsWith('After'));
    if (!eventName) {
        return;
    }

    const args = produceExecutionArguments(eventName as Events, screenId, elementId, handledEventContainer);
    if (!args) {
        return;
    }

    await executeEventHandler(...args, ...rest);
};

export const isBulkChange = (s1: string, s2: string): boolean => {
    const string1 = String(s1 === null || s1 === undefined ? '' : s1);
    const string2 = String(s2 === null || s2 === undefined ? '' : s2);

    if (Math.abs(string1.length - string2.length) > 1) {
        return true;
    }

    let diffCount = 0;
    Array.from(string1).forEach((character, index) => {
        if (character !== string2.charAt(index)) {
            diffCount += 1;
        }
    });

    return diffCount > 1;
};

export const executeCallbackInScreenContext = (callback: Function, screenId: string, ...args: any[]): void => {
    const state = getStore().getState();
    const screenDef = state.screenDefinitions[screenId];
    if (!screenDef) {
        throw Error(`events: ${screenId} screen definition doesn't exist in current state`);
    }
    const screenCtx = getScreenElement(screenDef);
    if (!screenCtx) {
        throw Error("events: screen context couldn't be built");
    }
    callback.apply(screenCtx, args);
};
