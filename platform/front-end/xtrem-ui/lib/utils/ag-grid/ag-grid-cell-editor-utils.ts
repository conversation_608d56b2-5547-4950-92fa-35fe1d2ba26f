import type { Column, Row<PERSON>lassParams, SuppressKeyboardEventParams } from '@ag-grid-community/core';
import * as tokens from '@sage/design-tokens/js/base/common';
import { memoize, set } from 'lodash';
import type { NestedField, NestedFieldTypes } from '../../component/nested-fields';
import { FieldKey } from '../../component/types';
import { localize } from '../../service/i18n-service';
import type { ScreenBase } from '../../service/screen-base';
import {
    BACKSPACE,
    DELETE,
    ENTER,
    F2,
    KEY_ENTER,
    KEY_TAB,
    getCharCodeFromEvent,
    isBackwardTab,
    isForwardTab,
    isTab,
} from '../keyboard-event-utils';
import { COLUMN_ID_ROW_ACTIONS, COLUMN_ID_ROW_SELECTION, COLUMN_ID_VALIDATIONS } from './ag-grid-column-config';

export function isLeftOrRight(event: React.KeyboardEvent): boolean {
    return [37, 39].indexOf(event.keyCode) > -1;
}

export function isCharNumeric(charStr: string): boolean {
    return (
        !!/\d/.test(charStr) || charStr === localize('@sage/xtrem-ui/number-format-separator', '.') || charStr === '-'
    );
}

export function isAllowedDateCharacter(charStr: string): boolean {
    return !!/\d/.test(charStr) || charStr === localize('@sage/xtrem-ui/date-format-separator', '/');
}

export function isValidUrl(text: string): boolean {
    return Boolean(
        // eslint-disable-next-line no-useless-escape
        /(ftp|http|https):\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()!@:%_\+.~#?&\/\/=]*)/.test(
            text,
        ),
    );
}

export function getInitialCellEditorState<T>({
    eventKey,
    initialValue,
    isNumeric = false,
}: {
    eventKey: string | null;
    initialValue: T;
    isNumeric?: boolean;
}): {
    value: T | string;
    highlightOnFocus: boolean;
} {
    let startValue: T | string;
    let highlightOnFocus = true;

    if (eventKey === BACKSPACE || eventKey === DELETE) {
        // if backspace or delete pressed, we clear the cell
        startValue = '';
    } else if (eventKey != null && eventKey !== ENTER && eventKey !== F2 && (!isNumeric || isCharNumeric(eventKey))) {
        // if a letter was pressed, we start with the letter
        startValue = eventKey;
        highlightOnFocus = false;
    } else {
        // otherwise we start with the current value
        startValue = initialValue;
        if (eventKey === F2) {
            highlightOnFocus = false;
        }
    }

    return {
        value: startValue,
        highlightOnFocus,
    };
}

export function isKeyPressedNumeric(event: React.KeyboardEvent): boolean {
    const charCode = getCharCodeFromEvent(event);
    const charStr = event.key ? event.key : String.fromCharCode(charCode);
    return isCharNumeric(charStr);
}

export function isKeyPressedDateComponent(event: React.KeyboardEvent): boolean {
    const charCode = getCharCodeFromEvent(event);
    const charStr = event.key ? event.key : String.fromCharCode(charCode);
    return isAllowedDateCharacter(charStr);
}

export function isFinishedEditingPressed(event: React.KeyboardEvent): boolean {
    const charCode = getCharCodeFromEvent(event);
    return charCode === KEY_ENTER || charCode === KEY_TAB;
}

export const defaultCellRendederStyles: React.CSSProperties = {
    lineHeight: '24px',
    fontSize: '14px',
    fontFamily: tokens.fontFamiliesDefault,
};

export const defaultCellEditorStyles: React.CSSProperties = {
    width: '100%',
    height: '100%',
    border: 'none',
    outline: 'none',
    background: 'transparent',
    ...defaultCellRendederStyles,
};

export const setDefaultAgGridInputStyles = (input: HTMLInputElement): void => {
    Object.entries(defaultCellRendederStyles).forEach(([key, value]) => {
        set(input.style, key, value);
    });
};

export const getColumnsToExport = (columns: Column[]): Column[] =>
    columns.filter(
        column =>
            !(
                column.getColId() === COLUMN_ID_ROW_SELECTION ||
                column.getColId() === COLUMN_ID_VALIDATIONS ||
                column.getColId() === COLUMN_ID_ROW_ACTIONS ||
                column.getColDef().type === FieldKey.Image
            ),
    );

export const cellExportFormatter = (
    column: NestedField<ScreenBase, NestedFieldTypes, any>,
    value?: string | number | null,
): string => {
    if (value === undefined || value === null || value === '') return '';
    if (column.type === FieldKey.Numeric) return Number(value).toString(10);
    if (column.type === FieldKey.Progress) return `${value}%`;
    return String(value);
};

export interface GetRowClassRulesOptions {
    canSelect: boolean;
    openedRecordId?: string | number | null;
    isChangeIndicatorDisabled?: boolean;
}

export const getRowClassRules = memoize(
    ({ canSelect, openedRecordId, isChangeIndicatorDisabled }: GetRowClassRulesOptions): any => ({
        'e-row-is-edited-with-select': memoize((params: RowClassParams) => {
            return params.data?.__dirty === true && canSelect;
        }),
        'e-row-is-edited-no-select': memoize((params: RowClassParams) => {
            return params.data?.__dirty === true && !isChangeIndicatorDisabled && !canSelect;
        }),
        'e-row-is-open-in-sidebar': memoize((params: RowClassParams) => {
            return Boolean(openedRecordId) && params.data?._id === openedRecordId;
        }),
    }),
);

export const isBackwardTabOnInput = (keyboardEvent: any): boolean =>
    isBackwardTab(keyboardEvent as React.KeyboardEvent) &&
    (keyboardEvent.target as HTMLInputElement)?.tagName === 'INPUT';

export const isForwardTabOnButton = (keyboardEvent: any): boolean =>
    isForwardTab(keyboardEvent as React.KeyboardEvent) &&
    (keyboardEvent.target as HTMLButtonElement)?.tagName === 'BUTTON';

export const suppressKeyboardEventForReferenceCellEditor = (params: SuppressKeyboardEventParams): boolean => {
    const event = params.event;
    if ((isTab(event as any) && isBackwardTabOnInput(event)) || isForwardTabOnButton(event)) {
        return false;
    }
    return params.editing;
};

export const suppressKeyboardEventForContainedTarget = (params: SuppressKeyboardEventParams): boolean => {
    if (!params.editing) {
        return false;
    }
    const focusableElements = Array.from(
        (params.event.currentTarget as HTMLElement | null)?.querySelectorAll?.(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
        ) ?? [],
    );
    return !(
        (isBackwardTab(params.event as any) && params.event.target === focusableElements[0]) ||
        (isForwardTab(params.event as any) && params.event.target === focusableElements[focusableElements.length - 1])
    );
};

export const isBackwardTabOnFirstAction = (keyboardEvent: any, index: number): boolean => {
    return isTab(keyboardEvent) && isBackwardTab(keyboardEvent) && index === 0;
};

export const isForwardTabOnLastAction = (keyboardEvent: any, index: number, length: number): boolean => {
    return isTab(keyboardEvent) && isForwardTab(keyboardEvent) && index === length - 1;
};

export const suppressKeyboardEventForActionCellEditor = (params: SuppressKeyboardEventParams): boolean => {
    const event = params.event as any;
    if (isTab(event) && params.event.target) {
        const isPhantomRow = params.data?.__phantom;
        const isPhantomRowDirty = params.data?.__dirty === true;

        const container = (params.event.target as HTMLElement)?.closest('.e-table-field-actions-container');
        if (
            !container ||
            (params.editing &&
                (params.event.target as HTMLElement)?.classList.contains('e-table-field-actions-container'))
        ) {
            if (!isBackwardTab(event)) {
                return true;
            }
            const focusableElements = Array.from(
                (params.event.target as HTMLElement).querySelectorAll(
                    'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])',
                ),
            );
            const last = focusableElements.at(-1);
            if (last instanceof HTMLElement) {
                event.preventDefault();
                event.stopPropagation();
                last.focus();
                return true;
            }
            return false;
        }
        const focusableElements = Array.from(
            container.querySelectorAll(
                'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])',
            ),
        );
        if (isPhantomRow && isForwardTab(event) && isPhantomRowDirty) {
            return true;
        }
        const index = focusableElements.indexOf(params.event.target as HTMLElement);
        if (
            isBackwardTabOnFirstAction(event, index) ||
            isForwardTabOnLastAction(event, index, focusableElements.length)
        ) {
            return false;
        }
        return true;
    }
    return true;
};
