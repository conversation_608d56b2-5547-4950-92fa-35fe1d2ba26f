import type { Dict, LocalizeLocale } from '@sage/xtrem-shared';
import { CUSTOM_DATA_PROPERTY, FieldKey, flat, objectKeys } from '@sage/xtrem-shared';
import { camelCase, get, memoize } from 'lodash';
import type { NestedGridProperties } from '../../component/control-objects';
import type { EditableFieldProperties } from '../../component/editable-field-control-object';
import { getFieldTitle, isFieldDisabled, isFieldReadOnly } from '../../component/field/carbon-helpers';
import type {
    ColumnsData,
    InternalTableProperties,
    TableViewLevel,
    TableViewSortedColumn,
} from '../../component/field/table/table-component-types';
import type { NestedField, NestedFieldTypes } from '../../component/nested-fields';
import { isNestedTechnical } from '../../component/nested-fields';
import type { NestedReferenceProperties, NestedTextProperties } from '../../component/nested-fields-properties';
import { CellWrapper } from '../../component/ui/table-shared/cell/cell-wrapper';
import type { CollectionValue } from '../../service/collection-data-service';
import { isCustomFieldReadOnly } from '../../service/customization-service';
import type { DataTypeDetails, FormattedNodeDetails } from '../../service/metadata-types';
import type { AccessBindings } from '../../service/page-definition';
import type { ScreenBase } from '../../service/screen-base';
import type { NodePropertyType } from '../../types';
import { normalizeUnderscoreBind } from '../abstract-fields-utils';
import { getElementAccessStatus } from '../access-utils';
import { convertDeepBindToPathNotNull } from '../nested-field-utils';
import { findDeepPropertyType } from '../node-utils';
import { resolveByValue } from '../resolve-value-utils';
import type { ColumnCompareType } from '../table-component-utils';
import { resolveCompareFunction } from '../table-component-utils';
import { cleanMetadataFromRecord, schemaTypeNameFromNodeName, splitValueToMergedValue } from '../transformers';
import type { ValueOrCallbackWithFieldValue } from '../types';
import type { ColumnConfigurationProperties } from './ag-grid-column-config';
import {
    addStaticColumns,
    defaultValueGetter,
    defaultValueSetter,
    getAggregateFieldColumnConfiguration,
    getCheckboxFieldColumnConfiguration,
    getCountFieldColumnConfiguration,
    getDateFieldColumnConfiguration,
    getDatetimeFieldColumnConfiguration,
    getDatetimeRangeFieldColumnConfiguration,
    getDropdownListFieldColumnConfiguration,
    getFilterSelectFieldColumnConfiguration,
    getIconFieldColumnConfiguration,
    getImageFieldColumnConfiguration,
    getLabelFieldColumnConfiguration,
    getLinkFieldColumnConfiguration,
    getNumericFieldColumnConfiguration,
    getProgressFieldColumnConfiguration,
    getReferenceFieldColumnConfiguration,
    getRelativeDateFieldColumnConfiguration,
    getSelectFieldColumnConfiguration,
    getSwitchFieldColumnConfiguration,
    getTextFieldColumnConfiguration,
} from './ag-grid-column-config';
import { onCellClickHandler, onCellValueChangedHandler } from './ag-grid-event-handlers';
import type { AgGridColumnConfigWithScreenIdAndColDef } from './ag-grid-utility-types';

export const shouldDisplayColumnInPanel = memoize((columnDefinition?: NestedField<ScreenBase, any>): boolean => {
    if (!columnDefinition || isNestedTechnical(columnDefinition) || !columnDefinition.type) {
        return false;
    }
    return !!(columnDefinition.properties as NestedTextProperties).title;
});

export function isColumnAvailable({
    screenId,
    columnDefinition,
    nodeTypes,
    dataTypes,
    contextNode,
    accessBindings,
}: {
    screenId: string;
    columnDefinition: NestedField<ScreenBase, Exclude<NestedFieldTypes, FieldKey.Technical>, any>;
    nodeTypes: Dict<FormattedNodeDetails>;
    dataTypes: Dict<DataTypeDetails>;
    contextNode: string;
    accessBindings: AccessBindings;
}): boolean {
    return (
        getElementAccessStatus({
            accessBindings,
            bind: columnDefinition.properties.bind!,
            elementProperties: columnDefinition.properties,
            contextNode,
            nodeTypes,
            dataTypes,
        }) !== 'unavailable' &&
        !excludedColumnTypes.includes(columnDefinition.type) &&
        !columnDefinition.properties.isExcludedFromMainField &&
        resolveByValue({
            screenId,
            skipHexFormat: true,
            propertyValue: columnDefinition.properties.isHidden,
            rowValue: null, // Whether a column is hidden or not is evaluated on a column level, so no row context can be provided.
        }) !== true &&
        resolveByValue({
            screenId,
            skipHexFormat: true,
            propertyValue: columnDefinition.properties.isHiddenDesktop,
            rowValue: null, // Whether a column is hidden or not is evaluated on a column level, so no row context can be provided.
        }) !== true
    );
}

const shouldDisplayCursor = (columnDefinitionType: NestedFieldTypes): boolean => {
    const cursorFields: NestedFieldTypes[] = [
        FieldKey.Date,
        FieldKey.DatetimeRange,
        FieldKey.DropdownList,
        FieldKey.FilterSelect,
        FieldKey.MultiDropdown,
        FieldKey.MultiReference,
        FieldKey.Numeric,
        FieldKey.Reference,
        FieldKey.Select,
        FieldKey.Text,
        FieldKey.TextArea,
    ];

    return cursorFields.includes(columnDefinitionType);
};

export const getCellClass = (
    isEditable: boolean,
    columnId: string,
    columnDefinition: NestedField<ScreenBase, NestedFieldTypes>,
    title: string,
    columnDefinitionType: NestedFieldTypes,
): string[] => {
    const cellClasses = [
        // Classes used by the xtrem-cli for finding the columns for testing
        `e-nested-cell-bind-${columnId}`,
        `e-nested-cell-label-${camelCase(title)}`,
        // Class name used for styling the cell
        `e-nested-cell-field-${columnDefinition.type.toLowerCase()}`,
    ];
    return isEditable && shouldDisplayCursor(columnDefinitionType)
        ? [...cellClasses, 'e-nested-cell-editable']
        : cellClasses;
};

export function withoutNestedTechnicalColumns(
    argument: ColumnsData[],
): ColumnsData<Exclude<NestedFieldTypes, FieldKey.Technical>>[] {
    return argument.filter(i => !isNestedTechnical(i.columnDefinition)) as ColumnsData<
        Exclude<NestedFieldTypes, FieldKey.Technical>
    >[];
}
const excludedColumnTypes = [FieldKey.TextArea, FieldKey.MultiDropdown, FieldKey.MultiReference];

const getNodeFromProperties = (level: number, properties: Omit<InternalTableProperties, 'selectedRecords'>): string => {
    if (level) {
        const nestedGridProperties = properties as unknown as NestedGridProperties;
        return String(nestedGridProperties.levels[level].node);
    }

    return String(properties.node);
};

/**
 * Local function to sort columns by the order defined in the table view or by the order defined in the field properties
 */
const sortColumnFunc =
    ({
        currentTableView,
        screenId,
        columnsData,
        sortColumns,
    }: {
        currentTableView?: TableViewLevel;
        screenId: string;
        columnsData: ColumnsData[];
        sortColumns?: (this: ScreenBase, firstColumn: ColumnCompareType, secondColumn: ColumnCompareType) => number;
    }) =>
    (column1: any, column2: any): number => {
        if (currentTableView?.columnOrder) {
            const column1Index = currentTableView.columnOrder.indexOf(
                convertDeepBindToPathNotNull(column1.columnDefinition.properties.bind),
            );
            const column2Index = currentTableView.columnOrder.indexOf(
                convertDeepBindToPathNotNull(column2.columnDefinition.properties.bind),
            );

            if (column1Index === -1) {
                return 1;
            }

            return column1Index - column2Index;
        }

        if (sortColumns) {
            return resolveCompareFunction(
                screenId,
                sortColumns,
                {
                    bind: convertDeepBindToPathNotNull(column1.columnDefinition.properties.bind),
                    valueField: (column1.columnDefinition.properties as NestedReferenceProperties).valueField as string,
                },
                {
                    bind: convertDeepBindToPathNotNull(column2.columnDefinition.properties.bind),
                    valueField: (column2.columnDefinition.properties as NestedReferenceProperties).valueField as string,
                },
            );
        }

        return (
            columnsData.indexOf(column1 as ColumnsData<NestedFieldTypes>) -
            columnsData.indexOf(column2 as ColumnsData<NestedFieldTypes>)
        );
    };

export const getColumns = ({
    accessBindings,
    columnsData,
    dataTypes,
    currentTableView,
    elementId: tableElementId,
    enumTypes,
    fieldProperties,
    isDisabled,
    isFilteringDisabled,
    groupBy,
    hasFloatingFilters = false,
    isParentDisabled,
    isReadOnly,
    level,
    locale,
    lookupSelectionMode,
    nodeTypes,
    pageNode,
    screenId,
    isSortingDisabled,
    value,
}: {
    accessBindings: AccessBindings;
    columnsData: ColumnsData[];
    currentTableView?: TableViewLevel;
    elementId: string;
    enumTypes: Dict<string[]>;
    /**
     * Ag-grid seems to cache some of these values in the column configuration, so in order to ensure that we always
     * have an up-to-date version of the properties, we pass them in as a callback
     * */
    fieldProperties: () => Omit<InternalTableProperties, 'selectedRecords'>;
    isDisabled?: boolean;
    isFilteringDisabled?: boolean;
    groupBy?: string;
    hasFloatingFilters?: boolean;
    level: number;
    locale: LocalizeLocale;
    lookupSelectionMode?: 'single' | 'multiple';
    isParentDisabled: () => boolean;
    isReadOnly?: ValueOrCallbackWithFieldValue<ScreenBase, boolean>;
    nodeTypes: Dict<FormattedNodeDetails>;
    pageNode?: NodePropertyType;
    screenId: string;
    isSortingDisabled?: boolean;
    customizedOrderBy?: TableViewSortedColumn[];
    dataTypes: Dict<DataTypeDetails>;
    /**
     * Ag-grid seems to cache some of these values in the column configuration, so in order to ensure that we always
     * have an up-to-date version of the value, we pass them in as a callback
     * */
    value: () => CollectionValue;
}): AgGridColumnConfigWithScreenIdAndColDef[] => {
    const fieldProps = fieldProperties();
    const { canFilter, canResizeColumns, node: tableNode, orderBy, sortColumns } = fieldProps;
    const isParentFieldDisabled: boolean = isParentDisabled() || isDisabled || false;
    const orderByKeys = objectKeys(flat(orderBy));
    const nodeType = schemaTypeNameFromNodeName(tableNode);

    const columns: AgGridColumnConfigWithScreenIdAndColDef[] = withoutNestedTechnicalColumns(columnsData)
        .filter(({ columnDefinition }) =>
            isColumnAvailable({
                contextNode: getNodeFromProperties(level, fieldProps),
                screenId,
                columnDefinition,
                nodeTypes,
                dataTypes,
                accessBindings,
            }),
        )
        .sort(sortColumnFunc({ currentTableView, screenId, columnsData, sortColumns }))
        .map(({ columnDefinition, elementId: columnId }) => {
            const columnProperties = columnDefinition.properties;
            const title = getFieldTitle(screenId, columnProperties, null); // The column title is evaluated on a column level, so no row context can be provided.
            const bind = convertDeepBindToPathNotNull(columnProperties.bind);
            const isEditableCell = (data: any): boolean => {
                const rowValue = splitValueToMergedValue(cleanMetadataFromRecord(data));
                const cellValue = get(data, bind);
                const accessRule = getElementAccessStatus({
                    accessBindings,
                    bind: columnDefinition.properties.bind!,
                    elementProperties: columnDefinition.properties,
                    contextNode: getNodeFromProperties(level, fieldProps),
                    nodeTypes,
                    dataTypes,
                });
                // We check if is a custom field and if it is, we check if it is read only
                if (bind.includes(CUSTOM_DATA_PROPERTY)) {
                    return !isCustomFieldReadOnly(
                        nodeTypes,
                        schemaTypeNameFromNodeName(pageNode) || '',
                        fieldProps.bind || tableElementId,
                    );
                }

                return (
                    fieldProps._controlObjectType !== FieldKey.Tree &&
                    accessRule !== 'unavailable' &&
                    accessRule !== 'unauthorized' &&
                    !isParentDisabled() &&
                    !isFieldDisabled(screenId, fieldProps, value(), undefined) &&
                    !isFieldDisabled(screenId, columnProperties, cellValue, rowValue) &&
                    !isFieldReadOnly(
                        screenId,
                        { isReadOnly: 'isReadOnly' in columnProperties ? columnProperties.isReadOnly : undefined },
                        cellValue,
                        rowValue,
                    ) &&
                    !isFieldReadOnly(screenId, fieldProps, value(), undefined)
                );
            };
            const gridColumnDefinition: AgGridColumnConfigWithScreenIdAndColDef = {
                context: {
                    columnDefinition,
                    columnId,
                    screenId,
                    isEditable: isEditableCell,
                },
                field: normalizeUnderscoreBind(columnId),
                headerName: title,
                minWidth: 100,
                editable: params => isEditableCell(params.data),
                sortable: columnProperties.canFilter !== false,
                type: columnDefinition.type,
                suppressMovable: true,
                cellClass: (params): string[] =>
                    getCellClass(isEditableCell(params.data), columnId, columnDefinition, title, columnDefinition.type),
                headerClass: () => {
                    const isMandatory = (columnProperties as EditableFieldProperties).isMandatory ?? false;

                    return [
                        `e-nested-header-bind-${columnId}`,
                        `e-nested-header-label-${camelCase(title)}`,
                        `e-nested-header-${columnDefinition.type.toLowerCase()}`,
                        isMandatory ? 'e-table-field-mandatory' : '',
                    ];
                },
                suppressHeaderMenuButton: true,
                onCellClicked: onCellClickHandler(screenId, tableElementId, columnProperties, isParentFieldDisabled),
                onCellValueChanged: onCellValueChangedHandler(
                    screenId,
                    tableElementId,
                    columnProperties,
                    value,
                    !!lookupSelectionMode,
                ),
                valueSetter: defaultValueSetter,
                valueGetter: params => defaultValueGetter(params, screenId, columnProperties)(),
                // Hide column we are grouping by as it will already be displayed as the leftmost column
                hide: columnId === groupBy || columnProperties.isHiddenOnMainField,
                flex: 1,
                resizable: Boolean(canResizeColumns),
            };
            const orderByKey = columnId.replace(/__/g, '.');

            if (currentTableView?.sortOrder) {
                const sortOption = currentTableView.sortOrder.find(c => c.colId === orderByKey);
                gridColumnDefinition.sort = sortOption?.sort;
                gridColumnDefinition.sortIndex = sortOption?.sortIndex;
            } else {
                const sortIndex = orderByKeys.indexOf(orderByKey);
                if (sortIndex !== -1) {
                    gridColumnDefinition.sort = get(orderBy, orderByKey) === 1 ? 'asc' : 'desc';
                    gridColumnDefinition.sortIndex = sortIndex + 1;
                }
            }

            const propertyGraphType = !columnProperties.isTransient
                ? findDeepPropertyType(nodeType, columnProperties.bind, nodeTypes, true)
                : null;

            const columnConfigProperties: ColumnConfigurationProperties<any> = {
                screenId,
                tableElementId,
                collectionValue: value,
                columnId,
                columnProperties: {
                    ...columnProperties,
                    wrapper: CellWrapper,
                },
                propertyGraphType,
                tableProperties: fieldProps,
                enumTypes,
                nodeTypes,
                isParentFieldDisabled,
                hasFloatingFilters,
                level,
                locale,
                isReadOnly,
            };

            switch (columnDefinition.type) {
                case FieldKey.Aggregate:
                    return {
                        ...gridColumnDefinition,
                        ...getAggregateFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Reference:
                    return {
                        ...gridColumnDefinition,
                        ...getReferenceFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Image:
                    return {
                        ...gridColumnDefinition,
                        ...getImageFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Label:
                    return {
                        ...gridColumnDefinition,
                        ...getLabelFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Icon:
                    return {
                        ...gridColumnDefinition,
                        ...getIconFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Checkbox:
                    return {
                        ...gridColumnDefinition,
                        ...getCheckboxFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Date:
                    return {
                        ...gridColumnDefinition,
                        ...getDateFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Datetime:
                    return {
                        ...gridColumnDefinition,
                        ...getDatetimeFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.DatetimeRange:
                    return {
                        ...gridColumnDefinition,
                        ...getDatetimeRangeFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Text:
                    return {
                        ...gridColumnDefinition,
                        ...getTextFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Link:
                    return {
                        ...gridColumnDefinition,
                        ...getLinkFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Progress:
                    return {
                        ...gridColumnDefinition,
                        ...getProgressFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Count:
                    return {
                        ...gridColumnDefinition,
                        ...getCountFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Numeric:
                    return {
                        ...gridColumnDefinition,
                        ...getNumericFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Select:
                    return {
                        ...gridColumnDefinition,
                        ...getSelectFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.Switch:
                    return {
                        ...gridColumnDefinition,
                        ...getSwitchFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.FilterSelect:
                    return {
                        ...gridColumnDefinition,
                        ...getFilterSelectFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.DropdownList:
                    return {
                        ...gridColumnDefinition,
                        ...getDropdownListFieldColumnConfiguration(columnConfigProperties),
                    };
                case FieldKey.RelativeDate:
                    return {
                        ...gridColumnDefinition,
                        ...getRelativeDateFieldColumnConfiguration(columnConfigProperties),
                    };
                default:
                    throw new Error(`Unexpected nested field type ${columnDefinition.type}`);
            }
        });

    if (canFilter === false || isFilteringDisabled || isParentFieldDisabled) {
        columns.forEach(c => {
            c.filter = false;
        });
    }

    if (canFilter === false || isSortingDisabled || isParentFieldDisabled) {
        columns.forEach(c => {
            c.sortable = false;
        });
    }

    addStaticColumns({
        accessBindings,
        columns,
        fieldProperties: fieldProps,
        hasFloatingFilters,
        isParentFieldDisabled,
        level,
        screenId,
        tableElementId,
        collectionValue: value,
    });

    if (isParentFieldDisabled) {
        columns.forEach(c => {
            c.suppressColumnsToolPanel = true;
            c.suppressMovable = true;
            c.suppressFiltersToolPanel = true;
            c.suppressFillHandle = true;
            c.resizable = false;
            c.suppressHeaderMenuButton = true;
        });
    }

    return columns.map((c, xtremSortIndex) => {
        const copy = { ...c };
        copy.context.xtremSortIndex = xtremSortIndex;
        return c;
    });
};
