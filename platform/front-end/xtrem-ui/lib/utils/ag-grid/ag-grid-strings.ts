import type { Dict } from '@sage/xtrem-shared';
import { memoize } from 'lodash';
import { localize } from '../../service/i18n-service';

export const localeText = memoize(
    (): Dict<string> => ({
        // Set Filter
        selectAll: localize('@sage/xtrem-ui/table-selectAll', 'Select All'),
        selectAllSearchResults: localize('@sage/xtrem-ui/table-selectAllSearchResults', 'Select All Search Results'),
        searchOoo: localize('@sage/xtrem-ui/table-searchOoo', 'Search...'),
        blanks: localize('@sage/xtrem-ui/table-blanks', 'Blanks'),
        noMatches: localize('@sage/xtrem-ui/table-noMatches', 'No matches.'),
        addCurrentSelectionToFilter: localize(
            '@sage/xtrem-ui/table-addCurrentSelectionToFilter',
            'Add current selection to filter',
        ),

        // Number Filter & Text Filter
        filterOoo: localize('@sage/xtrem-ui/table-filterOoo', 'Filter...'),
        equals: localize('@sage/xtrem-ui/table-equals', 'Equals'),
        notEqual: localize('@sage/xtrem-ui/table-notEqual', 'Not equal'),
        blank: localize('@sage/xtrem-ui/table-blank', 'Blank'),
        notBlank: localize('@sage/xtrem-ui/table-notBlank', 'Not blank'),
        empty: localize('@sage/xtrem-ui/table-empty', 'Choose one'),

        // Number Filter
        lessThan: localize('@sage/xtrem-ui/table-lessThan', 'Less than'),
        greaterThan: localize('@sage/xtrem-ui/table-greaterThan', 'Greater than'),
        lessThanOrEqual: localize('@sage/xtrem-ui/table-lessThanOrEqual', 'Less than or equal'),
        greaterThanOrEqual: localize('@sage/xtrem-ui/table-greaterThanOrEqual', 'Greater than or equal'),
        inRange: localize('@sage/xtrem-ui/table-inRange', 'In range'),
        inRangeStart: localize('@sage/xtrem-ui/table-inRangeStart', 'From'),
        inRangeEnd: localize('@sage/xtrem-ui/table-inRangeEnd', 'To'),

        // Text Filter
        contains: localize('@sage/xtrem-ui/table-contains', 'Contains'),
        notContains: localize('@sage/xtrem-ui/table-notContains', 'Not contains'),
        startsWith: localize('@sage/xtrem-ui/table-startsWith', 'Starts with'),
        endsWith: localize('@sage/xtrem-ui/table-endsWith', 'Ends with'),

        // Date Filter
        dateFormatOoo: localize('@sage/xtrem-ui/table-dateFormatOoo', 'YYYY-MM-DD'),
        before: localize('@sage/xtrem-ui/table-before', 'Before'),
        after: localize('@sage/xtrem-ui/table-after', 'After'),

        // Filter Conditions
        andCondition: localize('@sage/xtrem-ui/table-andCondition', 'AND'),
        orCondition: localize('@sage/xtrem-ui/table-orCondition', 'OR'),

        // Filter Buttons
        applyFilter: localize('@sage/xtrem-ui/table-applyFilter', 'Apply'),
        resetFilter: localize('@sage/xtrem-ui/table-resetFilter', 'Reset'),
        clearFilter: localize('@sage/xtrem-ui/table-clearFilter', 'Clear'),
        cancelFilter: localize('@sage/xtrem-ui/table-cancelFilter', 'Cancel'),

        // Filter Titles
        textFilter: localize('@sage/xtrem-ui/table-textFilter', 'Text Filter'),
        numberFilter: localize('@sage/xtrem-ui/table-numberFilter', 'Number Filter'),
        dateFilter: localize('@sage/xtrem-ui/table-dateFilter', 'Date Filter'),
        setFilter: localize('@sage/xtrem-ui/table-setFilter', 'Set Filter'),

        // Group Column Filter
        groupFilterSelect: localize('@sage/xtrem-ui/table-cancelFgroupFilterSelectilter', 'Select field:'),

        // Advanced Filter
        advancedFilterContains: localize('@sage/xtrem-ui/table-advancedFilterContains', 'contains'),
        advancedFilterNotContains: localize('@sage/xtrem-ui/table-advancedFilterNotContains', 'does not contain'),
        advancedFilterTextEquals: localize('@sage/xtrem-ui/table-advancedFilterTextEquals', 'equals'),
        advancedFilterTextNotEqual: localize('@sage/xtrem-ui/table-advancedFilterTextNotEqual', 'does not equal'),
        advancedFilterStartsWith: localize('@sage/xtrem-ui/table-advancedFilterStartsWith', 'begins with'),
        advancedFilterEndsWith: localize('@sage/xtrem-ui/table-advancedFilterEndsWith', 'ends with'),
        advancedFilterBlank: localize('@sage/xtrem-ui/table-advancedFilterBlank', 'is blank'),
        advancedFilterNotBlank: localize('@sage/xtrem-ui/table-advancedFilterNotBlank', 'is not blank'),
        advancedFilterEquals: localize('@sage/xtrem-ui/table-advancedFilterEquals', '='),
        advancedFilterNotEqual: localize('@sage/xtrem-ui/table-advancedFilterNotEqual', '!='),
        advancedFilterGreaterThan: localize('@sage/xtrem-ui/table-advancedFilterGreaterThan', '>'),
        advancedFilterGreaterThanOrEqual: localize('@sage/xtrem-ui/table-advancedFilterGreaterThanOrEqual', '>='),
        advancedFilterLessThan: localize('@sage/xtrem-ui/table-advancedFilterLessThan', '<'),
        advancedFilterLessThanOrEqual: localize('@sage/xtrem-ui/table-advancedFilterLessThanOrEqual', '<='),
        advancedFilterTrue: localize('@sage/xtrem-ui/table-advancedFilterTrue', 'is true'),
        advancedFilterFalse: localize('@sage/xtrem-ui/table-advancedFilterFalse', 'is false'),
        advancedFilterAnd: localize('@sage/xtrem-ui/table-advancedFilterAnd', 'AND'),
        advancedFilterOr: localize('@sage/xtrem-ui/table-advancedFilterOr', 'OR'),
        advancedFilterApply: localize('@sage/xtrem-ui/table-advancedFilterApply', 'Apply'),
        advancedFilterBuilder: localize('@sage/xtrem-ui/table-advancedFilterBuilder', 'Builder'),
        advancedFilterValidationMissingColumn: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationMissingColumn',
            'Column is missing',
        ),
        advancedFilterValidationMissingOption: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationMissingOption',
            'Option is missing',
        ),
        advancedFilterValidationMissingValue: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationMissingValue',
            'Value is missing',
        ),
        advancedFilterValidationInvalidColumn: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationInvalidColumn',
            'Column not found',
        ),
        advancedFilterValidationInvalidOption: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationInvalidOption',
            'Option not found',
        ),
        advancedFilterValidationMissingQuote: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationMissingQuote',
            'Value is missing an end quote',
        ),
        advancedFilterValidationNotANumber: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationNotANumber',
            'Value is not a number',
        ),
        advancedFilterValidationInvalidDate: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationInvalidDate',
            'Value is not a valid date',
        ),
        advancedFilterValidationMissingCondition: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationMissingCondition',
            'Condition is missing',
        ),
        advancedFilterValidationJoinOperatorMismatch: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationJoinOperatorMismatch',
            'Join operators within a condition must be the same',
        ),
        advancedFilterValidationInvalidJoinOperator: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationInvalidJoinOperator',
            'Join operator not found',
        ),
        advancedFilterValidationMissingEndBracket: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationMissingEndBracket',
            'Missing end bracket',
        ),
        advancedFilterValidationExtraEndBracket: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationExtraEndBracket',
            'Too many end brackets',
        ),
        advancedFilterValidationMessage: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationMessage',
            'Expression has an error. **{{error}}** - **{{validation}}**.',
        ),
        advancedFilterValidationMessageAtEnd: localize(
            '@sage/xtrem-ui/table-advancedFilterValidationMessageAtEnd',
            'Expression has an error. **{{validation}}** at end of expression.',
        ),
        advancedFilterBuilderTitle: localize('@sage/xtrem-ui/table-advancedFilterBuilderTitle', 'Advanced Filter'),
        advancedFilterBuilderApply: localize('@sage/xtrem-ui/table-advancedFilterBuilderApply', 'Apply'),
        advancedFilterBuilderCancel: localize('@sage/xtrem-ui/table-advancedFilterBuilderCancel', 'Cancel'),
        advancedFilterBuilderAddButtonTooltip: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderAddButtonTooltip',
            'Add Filter or Group',
        ),
        advancedFilterBuilderRemoveButtonTooltip: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderRemoveButtonTooltip',
            'Remove',
        ),
        advancedFilterBuilderMoveUpButtonTooltip: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderMoveUpButtonTooltip',
            'Move Up',
        ),
        advancedFilterBuilderMoveDownButtonTooltip: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderMoveDownButtonTooltip',
            'Move Down',
        ),
        advancedFilterBuilderAddJoin: localize('@sage/xtrem-ui/table-advancedFilterBuilderAddJoin', 'Add Group'),
        advancedFilterBuilderAddCondition: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderAddCondition',
            'Add Filter',
        ),
        advancedFilterBuilderSelectColumn: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderSelectColumn',
            'Select a column',
        ),
        advancedFilterBuilderSelectOption: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderSelectOption',
            'Select an option',
        ),
        advancedFilterBuilderEnterValue: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderEnterValue',
            'Enter a value...',
        ),
        advancedFilterBuilderValidationAlreadyApplied: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderValidationAlreadyApplied',
            'Current filter already applied.',
        ),
        advancedFilterBuilderValidationIncomplete: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderValidationIncomplete',
            'Not all conditions are complete.',
        ),
        advancedFilterBuilderValidationSelectColumn: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderValidationSelectColumn',
            'Must select a column.',
        ),
        advancedFilterBuilderValidationSelectOption: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderValidationSelectOption',
            'Must select an option.',
        ),
        advancedFilterBuilderValidationEnterValue: localize(
            '@sage/xtrem-ui/table-advancedFilterBuilderValidationEnterValue',
            'Must enter a value.',
        ),

        // Side Bar
        columns: localize('@sage/xtrem-ui/table-columns', 'Columns'),
        filters: localize('@sage/xtrem-ui/table-filters', 'Filters'),

        // columns tool panel
        pivotMode: localize('@sage/xtrem-ui/table-pivotMode', 'Pivot Mode'),
        groups: localize('@sage/xtrem-ui/table-groups', 'Row Groups'),
        rowGroupColumnsEmptyMessage: localize(
            '@sage/xtrem-ui/table-rowGroupColumnsEmptyMessage',
            'Drag here to set row groups',
        ),
        values: localize('@sage/xtrem-ui/table-values', 'Values'),
        valueColumnsEmptyMessage: localize('@sage/xtrem-ui/table-valueColumnsEmptyMessage', 'Drag here to aggregate'),
        pivots: localize('@sage/xtrem-ui/table-pivots', 'Column Labels'),
        pivotColumnsEmptyMessage: localize(
            '@sage/xtrem-ui/table-pivotColumnsEmptyMessage',
            'Drag here to set column labels',
        ),

        // Header of the Default Group Column
        group: localize('@sage/xtrem-ui/table-group', 'Group'),

        // Row Drag
        rowDragRow: localize('@sage/xtrem-ui/table-rowDragRow', 'row'),
        rowDragRows: localize('@sage/xtrem-ui/table-rowDragRows', 'rows'),

        // Other
        loadingOoo: localize('@sage/xtrem-ui/table-loadingOoo', 'Loading...'),
        noRowsToShow: localize('@sage/xtrem-ui/table-noRowsToShow', 'No Rows To Show'),
        enabled: localize('@sage/xtrem-ui/table-enabled', 'Enabled'),
        loadingError: localize('@sage/xtrem-ui/table-loadingError', 'ERR'),

        // Menu
        pinColumn: localize('@sage/xtrem-ui/table-pinColumn', 'Pin Column'),
        pinLeft: localize('@sage/xtrem-ui/table-pinLeft', 'Pin Left'),
        pinRight: localize('@sage/xtrem-ui/table-pinRight', 'Pin Right'),
        noPin: localize('@sage/xtrem-ui/table-noPin', 'No Pin'),
        valueAggregation: localize('@sage/xtrem-ui/table-valueAggregation', 'Value Aggregation'),
        autosizeThisColumn: localize('@sage/xtrem-ui/table-autosizeThiscolumn', 'Autosize This Column'),
        autosizeAllColumns: localize('@sage/xtrem-ui/table-autosizeAllColumns', 'Autosize All Columns'),
        groupBy: localize('@sage/xtrem-ui/table-groupBy', 'Group by'),
        ungroupBy: localize('@sage/xtrem-ui/table-ungroupBy', 'Un-Group by'),
        resetColumns: localize('@sage/xtrem-ui/table-resetColumns', 'Reset Columns'),
        expandAll: localize('@sage/xtrem-ui/table-expandAll', 'Expand All'),
        collapseAll: localize('@sage/xtrem-ui/table-collapseAll', 'Close All'),
        copy: localize('@sage/xtrem-ui/table-copy', 'Copy'),
        ctrlC: localize('@sage/xtrem-ui/table-ctrlC', 'Ctrl+C'),
        copyWithHeaders: localize('@sage/xtrem-ui/table-copyWithHeaders', 'Copy With Headers'),
        paste: localize('@sage/xtrem-ui/table-paste', 'Paste'),
        ctrlV: localize('@sage/xtrem-ui/table-ctrlV', 'Ctrl+V'),
        export: localize('@sage/xtrem-ui/table-export', 'Export'),
        csvExport: localize('@sage/xtrem-ui/table-csvExport', 'CSV Export'),
        excelExport: localize('@sage/xtrem-ui/table-excelExport', 'Excel Export (.xlsx)'),
        excelXmlExport: localize('@sage/xtrem-ui/table-excelXmlExport', 'Excel Export (.xml)'),
        noAggregation: localize('@sage/xtrem-ui/table-noAggregation', 'None'),
        ungroupAll: localize('@sage/xtrem-ui/table-ungroupAll', 'Un-Group All'),
        addToValues: localize('@sage/xtrem-ui/table-addToValues', 'Add **{{value}}** to values'),
        removeFromValues: localize('@sage/xtrem-ui/table-removeFromValues', 'Remove **{{value}}** from values'),
        addToLabels: localize('@sage/xtrem-ui/table-addToLabels', 'Add **{{value}}** to labels'),
        removeFromLabels: localize('@sage/xtrem-ui/table-removeFromLabels', 'Remove **{{value}}** from labels'),
        ctrlX: localize('@sage/xtrem-ui/table-ctrlX', 'Ctrl+X'),
        copyWithGroupHeaders: localize('@sage/xtrem-ui/table-copyWithGroupHeaders', 'Copy with Group Headers'),
        cut: localize('@sage/xtrem-ui/table-cut', 'Cut'),
        columnFilter: localize('@sage/xtrem-ui/table-columnFilter', 'Column Filter'),
        columnChooser: localize('@sage/xtrem-ui/table-columnChooser', 'Choose Columns'),
        chooseColumns: localize('@sage/xtrem-ui/table-chooseColumns', 'Choose Columns'),
        sortAscending: localize('@sage/xtrem-ui/table-sortAscending', 'Sort Ascending'),
        sortDescending: localize('@sage/xtrem-ui/table-sortDescending', 'Sort Descending'),
        sortUnSort: localize('@sage/xtrem-ui/table-sortUnSort', 'Clear Sort'),

        // Enterprise Menu Aggregation and Status Bar
        sum: localize('@sage/xtrem-ui/table-sum', 'Sum'),
        min: localize('@sage/xtrem-ui/table-min', 'Min'),
        max: localize('@sage/xtrem-ui/table-max', 'Max'),
        none: localize('@sage/xtrem-ui/table-none', 'None'),
        count: localize('@sage/xtrem-ui/table-count', 'Count'),
        avg: localize('@sage/xtrem-ui/table-avg', 'Average'),
        filteredRows: localize('@sage/xtrem-ui/table-filteredRows', 'Filtered'),
        selectedRows: localize('@sage/xtrem-ui/table-selectedRows', 'Selected'),
        totalRows: localize('@sage/xtrem-ui/table-totalRows', 'Total Rows'),
        totalAndFilteredRows: localize('@sage/xtrem-ui/table-totalAndFilteredRows', 'Rows'),
        page: localize('@sage/xtrem-ui/table-page', 'Page'),
        more: localize('@sage/xtrem-ui/table-more', 'More'),
        to: localize('@sage/xtrem-ui/table-to', 'To'),
        of: localize('@sage/xtrem-ui/table-of', 'Of'),
        next: localize('@sage/xtrem-ui/table-next', 'Next'),
        last: localize('@sage/xtrem-ui/table-last', 'Last'),
        first: localize('@sage/xtrem-ui/table-first', 'First'),
        previous: localize('@sage/xtrem-ui/table-previous', 'Previous'),
        pageLastRowUnknown: localize('@sage/xtrem-ui/table-pageLastRowUnknown', '?'),
        nextPage: localize('@sage/xtrem-ui/table-nextPage', 'Next Page'),
        lastPage: localize('@sage/xtrem-ui/table-lastPage', 'Last Page'),
        firstPage: localize('@sage/xtrem-ui/table-firstPage', 'First Page'),
        previousPage: localize('@sage/xtrem-ui/table-previousPage', 'Previous Page'),
        pageSizeSelectorLabel: localize('@sage/xtrem-ui/table-pageSizeSelectorLabel', 'Page Size:'),
        footerTotal: localize('@sage/xtrem-ui/table-footerTotal', 'Total'),

        // Pivoting
        pivotColumnGroupTotals: localize('@sage/xtrem-ui/table-pivotColumnGroupTotals', 'Total'),

        // Enterprise Menu (Charts)
        pivotChartAndPivotMode: localize('@sage/xtrem-ui/table-pivotChartAndPivotMode', 'Pivot Chart & Pivot Mode'),
        pivotChart: localize('@sage/xtrem-ui/table-pivotChart', 'Pivot Chart'),
        chartRange: localize('@sage/xtrem-ui/table-chartRange', 'Chart Range'),
        columnChart: localize('@sage/xtrem-ui/table-columnChart', 'Column'),
        groupedColumn: localize('@sage/xtrem-ui/table-groupedColumn', 'Grouped'),
        stackedColumn: localize('@sage/xtrem-ui/table-stackedColumn', 'Stacked'),
        normalizedColumn: localize('@sage/xtrem-ui/table-normalizedColumn', '100% Stacked'),
        barChart: localize('@sage/xtrem-ui/table-barChart', 'Bar'),
        groupedBar: localize('@sage/xtrem-ui/table-groupedBar', 'Grouped'),
        stackedBar: localize('@sage/xtrem-ui/table-stackedBar', 'Stacked'),
        normalizedBar: localize('@sage/xtrem-ui/table-normalizedBar', '100% Stacked'),
        pieChart: localize('@sage/xtrem-ui/table-pieChart', 'Pie'),
        pie: localize('@sage/xtrem-ui/table-pie', 'Pie'),
        doughnut: localize('@sage/xtrem-ui/table-doughnut', 'Doughnut'),
        line: localize('@sage/xtrem-ui/table-line', 'Line'),
        xyChart: localize('@sage/xtrem-ui/table-xyChart', 'X Y (Scatter)'),
        scatter: localize('@sage/xtrem-ui/table-scatter', 'Scatter'),
        bubble: localize('@sage/xtrem-ui/table-bubble', 'Bubble'),
        areaChart: localize('@sage/xtrem-ui/table-areaChart', 'Area'),
        area: localize('@sage/xtrem-ui/table-area', 'Area'),
        stackedArea: localize('@sage/xtrem-ui/table-stackedArea', 'Stacked'),
        normalizedArea: localize('@sage/xtrem-ui/table-normalizedArea', '100% Stacked'),
        histogramChart: localize('@sage/xtrem-ui/table-histogramChart', 'Histogram'),
        donut: localize('@sage/xtrem-ui/table-donut', 'Donut'),
        lineChart: localize('@sage/xtrem-ui/table-lineChart', 'Line'),
        stackedLine: localize('@sage/xtrem-ui/table-stackedLine', 'Stacked'),
        normalizedLine: localize('@sage/xtrem-ui/table-normalizedLine', '100% Stacked'),
        polarChart: localize('@sage/xtrem-ui/table-polarChart', 'Polar'),
        radarLine: localize('@sage/xtrem-ui/table-radarLine', 'Radar Line'),
        radarArea: localize('@sage/xtrem-ui/table-radarArea', 'Radar Area'),
        nightingale: localize('@sage/xtrem-ui/table-nightingale', 'Nightingale'),
        radialColumn: localize('@sage/xtrem-ui/table-radialColumn', 'Radial Column'),
        radialBar: localize('@sage/xtrem-ui/table-radialBar', 'Radial Bar'),
        statisticalChart: localize('@sage/xtrem-ui/table-statisticalChart', 'Statistical'),
        boxPlot: localize('@sage/xtrem-ui/table-boxPlot', 'Box Plot'),
        rangeBar: localize('@sage/xtrem-ui/table-rangeBar', 'Range Bar'),
        rangeArea: localize('@sage/xtrem-ui/table-rangeArea', 'Range Area'),
        hierarchicalChart: localize('@sage/xtrem-ui/table-hierarchicalChart', 'Hierarchical'),
        treemap: localize('@sage/xtrem-ui/table-treemap', 'Treemap'),
        sunburst: localize('@sage/xtrem-ui/table-sunburst', 'Sunburst'),
        specializedChart: localize('@sage/xtrem-ui/table-specializedChart', 'Specialized'),
        waterfall: localize('@sage/xtrem-ui/table-waterfall', 'Waterfall'),
        heatmap: localize('@sage/xtrem-ui/table-heatmap', 'Heatmap'),
        combinationChart: localize('@sage/xtrem-ui/table-combinationChart', 'Combination'),
        columnLineCombo: localize('@sage/xtrem-ui/table-columnLineCombo', 'Column & Line'),
        AreaColumnCombo: localize('@sage/xtrem-ui/table-AreaColumnCombo', 'Area & Column'),

        // Charts
        pivotChartTitle: localize('@sage/xtrem-ui/table-pivotChartTitle', 'Pivot Chart'),
        rangeChartTitle: localize('@sage/xtrem-ui/table-rangeChartTitle', 'Range Chart'),
        settings: localize('@sage/xtrem-ui/table-settings', 'Settings'),
        data: localize('@sage/xtrem-ui/table-data', 'Data'),
        format: localize('@sage/xtrem-ui/table-format', 'Format'),
        categories: localize('@sage/xtrem-ui/table-categories', 'Categories'),
        defaultCategory: localize('@sage/xtrem-ui/table-defaultCategory', '(None)'),
        series: localize('@sage/xtrem-ui/table-series', 'Series'),
        xyValues: localize('@sage/xtrem-ui/table-xyValues', 'X Y Values'),
        paired: localize('@sage/xtrem-ui/table-paired', 'Paired Mode'),
        axis: localize('@sage/xtrem-ui/table-axis', 'Axis'),
        navigator: localize('@sage/xtrem-ui/table-navigator', 'Navigator'),
        color: localize('@sage/xtrem-ui/table-color', 'Color'),
        thickness: localize('@sage/xtrem-ui/table-thickness', 'Thickness'),
        xType: localize('@sage/xtrem-ui/table-xType', 'X Type'),
        automatic: localize('@sage/xtrem-ui/table-automatic', 'Automatic'),
        category: localize('@sage/xtrem-ui/table-category', 'Category'),
        number: localize('@sage/xtrem-ui/table-number', 'Number'),
        time: localize('@sage/xtrem-ui/table-time', 'Time'),
        xRotation: localize('@sage/xtrem-ui/table-xRotation', 'X Rotation'),
        yRotation: localize('@sage/xtrem-ui/table-yRotation', 'Y Rotation'),
        ticks: localize('@sage/xtrem-ui/table-ticks', 'Ticks'),
        width: localize('@sage/xtrem-ui/table-width', 'Width'),
        height: localize('@sage/xtrem-ui/table-height', 'Height'),
        length: localize('@sage/xtrem-ui/table-length', 'Length'),
        padding: localize('@sage/xtrem-ui/table-padding', 'Padding'),
        spacing: localize('@sage/xtrem-ui/table-spacing', 'Spacing'),
        chart: localize('@sage/xtrem-ui/table-chart', 'Chart'),
        title: localize('@sage/xtrem-ui/table-title', 'Title'),
        titlePlaceholder: localize('@sage/xtrem-ui/table-titlePlaceholder', 'Chart title - double click to edit'),
        background: localize('@sage/xtrem-ui/table-background', 'Background'),
        font: localize('@sage/xtrem-ui/table-font', 'Font'),
        top: localize('@sage/xtrem-ui/table-top', 'Top'),
        right: localize('@sage/xtrem-ui/table-right', 'Right'),
        bottom: localize('@sage/xtrem-ui/table-bottom', 'Bottom'),
        left: localize('@sage/xtrem-ui/table-left', 'Left'),
        labels: localize('@sage/xtrem-ui/table-labels', 'Labels'),
        size: localize('@sage/xtrem-ui/table-size', 'Size'),
        minSize: localize('@sage/xtrem-ui/table-minSize', 'Minimum Size'),
        maxSize: localize('@sage/xtrem-ui/table-maxSize', 'Maximum Size'),
        legend: localize('@sage/xtrem-ui/table-legend', 'Legend'),
        position: localize('@sage/xtrem-ui/table-position', 'Position'),
        markerSize: localize('@sage/xtrem-ui/table-markerSize', 'Marker Size'),
        markerStroke: localize('@sage/xtrem-ui/table-markerStroke', 'Marker Stroke'),
        markerPadding: localize('@sage/xtrem-ui/table-markerPadding', 'Marker Padding'),
        itemSpacing: localize('@sage/xtrem-ui/table-itemSpacing', 'Item Spacing'),
        itemPaddingX: localize('@sage/xtrem-ui/table-itemPaddingX', 'Item Padding X'),
        itemPaddingY: localize('@sage/xtrem-ui/table-itemPaddingY', 'Item Padding Y'),
        layoutHorizontalSpacing: localize('@sage/xtrem-ui/table-layoutHorizontalSpacing', 'Horizontal Spacing'),
        layoutVerticalSpacing: localize('@sage/xtrem-ui/table-layoutVerticalSpacing', 'Vertical Spacing'),
        strokeWidth: localize('@sage/xtrem-ui/table-strokeWidth', 'Stroke Width'),
        offset: localize('@sage/xtrem-ui/table-offset', 'Offset'),
        offsets: localize('@sage/xtrem-ui/table-offsets', 'Offsets'),
        tooltips: localize('@sage/xtrem-ui/table-tooltips', 'Tooltips'),
        callout: localize('@sage/xtrem-ui/table-callout', 'Callout'),
        markers: localize('@sage/xtrem-ui/table-markers', 'Markers'),
        shadow: localize('@sage/xtrem-ui/table-shadow', 'Shadow'),
        blur: localize('@sage/xtrem-ui/table-blur', 'Blur'),
        xOffset: localize('@sage/xtrem-ui/table-xOffset', 'X Offset'),
        yOffset: localize('@sage/xtrem-ui/table-yOffset', 'Y Offset'),
        lineWidth: localize('@sage/xtrem-ui/table-lineWidth', 'Line Width'),
        normal: localize('@sage/xtrem-ui/table-normal', 'Normal'),
        bold: localize('@sage/xtrem-ui/table-bold', 'Bold'),
        italic: localize('@sage/xtrem-ui/table-italic', 'Italic'),
        boldItalic: localize('@sage/xtrem-ui/table-boldItalic', 'Bold Italic'),
        predefined: localize('@sage/xtrem-ui/table-predefined', 'Predefined'),
        fillOpacity: localize('@sage/xtrem-ui/table-fillOpacity', 'Fill Opacity'),
        strokeOpacity: localize('@sage/xtrem-ui/table-strokeOpacity', 'Line Opacity'),
        histogramBinCount: localize('@sage/xtrem-ui/table-histogramBinCount', 'Bin count'),
        columnGroup: localize('@sage/xtrem-ui/table-columnGroup', 'Column'),
        barGroup: localize('@sage/xtrem-ui/table-barGroup', 'Bar'),
        pieGroup: localize('@sage/xtrem-ui/table-pieGroup', 'Pie'),
        lineGroup: localize('@sage/xtrem-ui/table-lineGroup', 'Line'),
        scatterGroup: localize('@sage/xtrem-ui/table-scatterGroup', 'X Y (Scatter)'),
        areaGroup: localize('@sage/xtrem-ui/table-areaGroup', 'Area'),
        histogramGroup: localize('@sage/xtrem-ui/table-histogramGroup', 'Histogram'),
        groupedColumnTooltip: localize('@sage/xtrem-ui/table-groupedColumnTooltip', 'Grouped'),
        stackedColumnTooltip: localize('@sage/xtrem-ui/table-stackedColumnTooltip', 'Stacked'),
        normalizedColumnTooltip: localize('@sage/xtrem-ui/table-normalizedColumnTooltip', '100% Stacked'),
        groupedBarTooltip: localize('@sage/xtrem-ui/table-groupedBarTooltip', 'Grouped'),
        stackedBarTooltip: localize('@sage/xtrem-ui/table-stackedBarTooltip', 'Stacked'),
        normalizedBarTooltip: localize('@sage/xtrem-ui/table-normalizedBarTooltip', '100% Stacked'),
        pieTooltip: localize('@sage/xtrem-ui/table-pieTooltip', 'Pie'),
        doughnutTooltip: localize('@sage/xtrem-ui/table-doughnutTooltip', 'Doughnut'),
        lineTooltip: localize('@sage/xtrem-ui/table-lineTooltip', 'Line'),
        groupedAreaTooltip: localize('@sage/xtrem-ui/table-groupedAreaTooltip', 'Area'),
        stackedAreaTooltip: localize('@sage/xtrem-ui/table-stackedAreaTooltip', 'Stacked'),
        normalizedAreaTooltip: localize('@sage/xtrem-ui/table-normalizedAreaTooltip', '100% Stacked'),
        scatterTooltip: localize('@sage/xtrem-ui/table-scatterTooltip', 'Scatter'),
        bubbleTooltip: localize('@sage/xtrem-ui/table-bubbleTooltip', 'Bubble'),
        histogramTooltip: localize('@sage/xtrem-ui/table-histogramTooltip', 'Histogram'),
        noDataToChart: localize('@sage/xtrem-ui/table-noDataToChart', 'No data available to be charted.'),
        pivotChartRequiresPivotMode: localize(
            '@sage/xtrem-ui/table-pivotChartRequiresPivotMode',
            'Pivot Chart requires Pivot Mode enabled.',
        ),
        switchCategorySeries: localize('@sage/xtrem-ui/table-switchCategorySeries', 'Switch Category / Series'),
        categoryValues: localize('@sage/xtrem-ui/table-categoryValues', 'Category Values'),
        seriesLabels: localize('@sage/xtrem-ui/table-seriesLabels', 'Series Labels'),
        aggregate: localize('@sage/xtrem-ui/table-aggregate', 'Aggregate'),
        xAxis: localize('@sage/xtrem-ui/table-xAxis', 'Horizontal Axis'),
        yAxis: localize('@sage/xtrem-ui/table-yAxis', 'Vertical Axis'),
        polarAxis: localize('@sage/xtrem-ui/table-polarAxis', 'Polar Axis'),
        radiusAxis: localize('@sage/xtrem-ui/table-radiusAxis', 'Radius Axis'),
        zoom: localize('@sage/xtrem-ui/table-zoom', 'Zoom'),
        animation: localize('@sage/xtrem-ui/table-animation', 'Animation'),
        crosshair: localize('@sage/xtrem-ui/table-crosshair', 'Crosshair'),
        preferredLength: localize('@sage/xtrem-ui/table-preferredLength', 'Preferred Length'),
        axisType: localize('@sage/xtrem-ui/table-axisType', 'Axis Type'),
        timeFormat: localize('@sage/xtrem-ui/table-timeFormat', 'Time Format'),
        autoRotate: localize('@sage/xtrem-ui/table-autoRotate', 'Auto Rotate'),
        labelRotation: localize('@sage/xtrem-ui/table-labelRotation', 'Rotation'),
        circle: localize('@sage/xtrem-ui/table-circle', 'Circle'),
        polygon: localize('@sage/xtrem-ui/table-polygon', 'Polygon'),
        square: localize('@sage/xtrem-ui/table-square', 'Square'),
        cross: localize('@sage/xtrem-ui/table-cross', 'Cross'),
        diamond: localize('@sage/xtrem-ui/table-diamond', 'Diamond'),
        plus: localize('@sage/xtrem-ui/table-plus', 'Plus'),
        triangle: localize('@sage/xtrem-ui/table-triangle', 'Triangle'),
        heart: localize('@sage/xtrem-ui/table-heart', 'Heart'),
        orientation: localize('@sage/xtrem-ui/table-orientation', 'Orientation'),
        fixed: localize('@sage/xtrem-ui/table-fixed', 'Fixed'),
        parallel: localize('@sage/xtrem-ui/table-parallel', 'Parallel'),
        perpendicular: localize('@sage/xtrem-ui/table-perpendicular', 'Perpendicular'),
        radiusAxisPosition: localize('@sage/xtrem-ui/table-radiusAxisPosition', 'Position'),
        gridLines: localize('@sage/xtrem-ui/table-gridLines', 'Grid Lines'),
        chartStyle: localize('@sage/xtrem-ui/table-chartStyle', 'Chart Style'),
        chartTitles: localize('@sage/xtrem-ui/table-chartTitles', 'Titles'),
        chartTitle: localize('@sage/xtrem-ui/table-chartTitle', 'Chart Title'),
        chartSubtitle: localize('@sage/xtrem-ui/table-chartSubtitle', 'Subtitle'),
        horizontalAxisTitle: localize('@sage/xtrem-ui/table-horizontalAxisTitle', 'Horizontal Axis Title'),
        verticalAxisTitle: localize('@sage/xtrem-ui/table-verticalAxisTitle', 'Vertical Axis Title'),
        polarAxisTitle: localize('@sage/xtrem-ui/table-polarAxisTitle', 'Polar Axis Title'),
        weight: localize('@sage/xtrem-ui/table-weight', 'Weight'),
        calloutLabels: localize('@sage/xtrem-ui/table-calloutLabels', 'Callout Labels'),
        sectorLabels: localize('@sage/xtrem-ui/table-sectorLabels', 'Sector Labels'),
        positionRatio: localize('@sage/xtrem-ui/table-positionRatio', 'Position Ratio'),
        shape: localize('@sage/xtrem-ui/table-shape', 'Shape'),
        lineDash: localize('@sage/xtrem-ui/table-lineDash', 'Line Dash'),
        lineDashOffset: localize('@sage/xtrem-ui/table-lineDashOffset', 'Dash Offset'),
        scrollingZoom: localize('@sage/xtrem-ui/table-scrollingZoom', 'Scrolling'),
        scrollingStep: localize('@sage/xtrem-ui/table-scrollingStep', 'Scrolling Step'),
        selectingZoom: localize('@sage/xtrem-ui/table-selectingZoom', 'Selecting'),
        durationMillis: localize('@sage/xtrem-ui/table-durationMillis', 'Duration (ms)'),
        crosshairLabel: localize('@sage/xtrem-ui/table-crosshairLabel', 'Label'),
        crosshairSnap: localize('@sage/xtrem-ui/table-crosshairSnap', 'Snap to Node'),
        strokeColor: localize('@sage/xtrem-ui/table-strokeColor', 'Line Color'),
        miniChart: localize('@sage/xtrem-ui/table-miniChart', 'Mini-Chart'),
        connectorLine: localize('@sage/xtrem-ui/table-connectorLine', 'Connector Line'),
        seriesItems: localize('@sage/xtrem-ui/table-seriesItems', 'Series Items'),
        seriesItemType: localize('@sage/xtrem-ui/table-seriesItemType', 'Item Type'),
        seriesItemPositive: localize('@sage/xtrem-ui/table-seriesItemPositive', 'Positive'),
        seriesItemNegative: localize('@sage/xtrem-ui/table-seriesItemNegative', 'Negative'),
        seriesItemLabels: localize('@sage/xtrem-ui/table-seriesItemLabels', 'Item Labels'),
        polarGroup: localize('@sage/xtrem-ui/table-polarGroup', 'Polar'),
        statisticalGroup: localize('@sage/xtrem-ui/table-statisticalGroup', 'Statistical'),
        hierarchicalGroup: localize('@sage/xtrem-ui/table-hierarchicalGroup', 'Hierarchical'),
        specializedGroup: localize('@sage/xtrem-ui/table-specializedGroup', 'Specialized'),
        combinationGroup: localize('@sage/xtrem-ui/table-combinationGroup', 'Combination'),
        donutTooltip: localize('@sage/xtrem-ui/table-donutTooltip', 'Donut'),
        stackedLineTooltip: localize('@sage/xtrem-ui/table-stackedLineTooltip', 'Stacked'),
        normalizedLineTooltip: localize('@sage/xtrem-ui/table-normalizedLineTooltip', '100% Stacked'),
        radialColumnTooltip: localize('@sage/xtrem-ui/table-radialColumnTooltip', 'Radial Column'),
        radialBarTooltip: localize('@sage/xtrem-ui/table-radialBarTooltip', 'Radial Bar'),
        radarLineTooltip: localize('@sage/xtrem-ui/table-radarLineTooltip', 'Radar Line'),
        radarAreaTooltip: localize('@sage/xtrem-ui/table-radarAreaTooltip', 'Radar Area'),
        nightingaleTooltip: localize('@sage/xtrem-ui/table-nightingaleTooltip', 'Nightingale'),
        rangeBarTooltip: localize('@sage/xtrem-ui/table-rangeBarTooltip', 'Range Bar'),
        rangeAreaTooltip: localize('@sage/xtrem-ui/table-rangeAreaTooltip', 'Range Area'),
        boxPlotTooltip: localize('@sage/xtrem-ui/table-boxPlotTooltip', 'Box Plot'),
        treemapTooltip: localize('@sage/xtrem-ui/table-treemapTooltip', 'Treemap'),
        sunburstTooltip: localize('@sage/xtrem-ui/table-sunburstTooltip', 'Sunburst'),
        waterfallTooltip: localize('@sage/xtrem-ui/table-waterfallTooltip', 'Waterfall'),
        heatmapTooltip: localize('@sage/xtrem-ui/table-heatmapTooltip', 'Heatmap'),
        columnLineComboTooltip: localize('@sage/xtrem-ui/table-columnLineComboTooltip', 'Column & Line'),
        areaColumnComboTooltip: localize('@sage/xtrem-ui/table-areaColumnComboTooltip', 'Area & Column'),
        customComboTooltip: localize('@sage/xtrem-ui/table-customComboTooltip', 'Custom Combination'),
        innerRadius: localize('@sage/xtrem-ui/table-innerRadius', 'Inner Radius'),
        startAngle: localize('@sage/xtrem-ui/table-startAngle', 'Start Angle'),
        endAngle: localize('@sage/xtrem-ui/table-endAngle', 'End Angle'),
        reverseDirection: localize('@sage/xtrem-ui/table-reverseDirection', 'Reverse Direction'),
        groupPadding: localize('@sage/xtrem-ui/table-groupPadding', 'Group Padding'),
        seriesPadding: localize('@sage/xtrem-ui/table-seriesPadding', 'Series Padding'),
        tile: localize('@sage/xtrem-ui/table-tile', 'Tile'),
        whisker: localize('@sage/xtrem-ui/table-whisker', 'Whisker'),
        cap: localize('@sage/xtrem-ui/table-cap', 'Cap'),
        capLengthRatio: localize('@sage/xtrem-ui/table-capLengthRatio', 'Length Ratio'),
        labelPlacement: localize('@sage/xtrem-ui/table-labelPlacement', 'Placement'),
        inside: localize('@sage/xtrem-ui/table-inside', 'Inside'),
        outside: localize('@sage/xtrem-ui/table-outside', 'Outside'),
        chartSettingsToolbarTooltip: localize('@sage/xtrem-ui/table-chartSettingsToolbarTooltip', 'Menu'),
        chartLinkToolbarTooltip: localize('@sage/xtrem-ui/table-chartLinkToolbarTooltip', 'Linked to Grid'),
        chartUnlinkToolbarTooltip: localize('@sage/xtrem-ui/table-chartUnlinkToolbarTooltip', 'Unlinked from Grid'),
        chartDownloadToolbarTooltip: localize('@sage/xtrem-ui/table-chartDownloadToolbarTooltip', 'Download Chart'),
        chartMenuToolbarTooltip: localize('@sage/xtrem-ui/table-chartMenuToolbarTooltip', 'Menu'),
        chartEdit: localize('@sage/xtrem-ui/table-chartEdit', 'Edit Chart'),
        chartAdvancedSettings: localize('@sage/xtrem-ui/table-chartAdvancedSettings', 'Advanced Settings'),
        chartLink: localize('@sage/xtrem-ui/table-chartLink', 'Link to Grid'),
        chartUnlink: localize('@sage/xtrem-ui/table-chartUnlink', 'Unlink from Grid'),
        chartDownload: localize('@sage/xtrem-ui/table-chartDownload', 'Download Chart'),
        histogramFrequency: localize('@sage/xtrem-ui/table-histogramFrequency', 'Frequency'),
        seriesChartType: localize('@sage/xtrem-ui/table-seriesChartType', 'Series Chart Type'),
        seriesType: localize('@sage/xtrem-ui/table-seriesType', 'Series Type'),
        secondaryAxis: localize('@sage/xtrem-ui/table-secondaryAxis', 'Secondary Axis'),
        seriesAdd: localize('@sage/xtrem-ui/table-seriesAdd', 'Add a series'),
        categoryAdd: localize('@sage/xtrem-ui/table-categoryAdd', 'Add a category'),
        bar: localize('@sage/xtrem-ui/table-bar', 'Bar'),
        column: localize('@sage/xtrem-ui/table-column', 'Column'),
        histogram: localize('@sage/xtrem-ui/table-histogram', 'Histogram'),
        advancedSettings: localize('@sage/xtrem-ui/table-advancedSettings', 'Advanced Settings'),
        direction: localize('@sage/xtrem-ui/table-direction', 'Direction'),
        horizontal: localize('@sage/xtrem-ui/table-horizontal', 'Horizontal'),
        vertical: localize('@sage/xtrem-ui/table-vertical', 'Vertical'),
        seriesGroupType: localize('@sage/xtrem-ui/table-seriesGroupType', 'Group Type'),
        groupedSeriesGroupType: localize('@sage/xtrem-ui/table-groupedSeriesGroupType', 'Grouped'),
        stackedSeriesGroupType: localize('@sage/xtrem-ui/table-stackedSeriesGroupType', 'Stacked'),
        normalizedSeriesGroupType: localize('@sage/xtrem-ui/table-normalizedSeriesGroupType', '100% Stacked'),
        legendEnabled: localize('@sage/xtrem-ui/table-legendEnabled', 'Enabled'),
        invalidColor: localize('@sage/xtrem-ui/table-invalidColor', 'Color value is invalid'),
        groupedColumnFull: localize('@sage/xtrem-ui/table-groupedColumnFull', 'Grouped Column'),
        stackedColumnFull: localize('@sage/xtrem-ui/table-stackedColumnFull', 'Stacked Column'),
        normalizedColumnFull: localize('@sage/xtrem-ui/table-normalizedColumnFull', '100% Stacked Column'),
        groupedBarFull: localize('@sage/xtrem-ui/table-groupedBarFull', 'Grouped Bar'),
        stackedBarFull: localize('@sage/xtrem-ui/table-stackedBarFull', 'Stacked Bar'),
        normalizedBarFull: localize('@sage/xtrem-ui/table-normalizedBarFull', '100% Stacked Bar'),
        stackedAreaFull: localize('@sage/xtrem-ui/table-stackedAreaFull', 'Stacked Area'),
        normalizedAreaFull: localize('@sage/xtrem-ui/table-normalizedAreaFull', '100% Stacked Area'),
        customCombo: localize('@sage/xtrem-ui/table-customCombo', 'Custom Combination'),

        // ARIA
        ariaAdvancedFilterBuilderItem: localize(
            '@sage/xtrem-ui/table-ariaAdvancedFilterBuilderItem',
            '**{{aria}}**. Level **{{level}}**. Press ENTER to edit',
        ),
        ariaAdvancedFilterBuilderItemValidation: localize(
            '@sage/xtrem-ui/table-ariaAdvancedFilterBuilderItemValidation',
            '**{{aria}}**. Level **{{level}}**. **{{subLevel}}** Press ENTER to edit',
        ),
        ariaAdvancedFilterBuilderList: localize(
            '@sage/xtrem-ui/table-ariaAdvancedFilterBuilderList',
            'Advanced Filter Builder List',
        ),
        ariaAdvancedFilterBuilderFilterItem: localize(
            '@sage/xtrem-ui/table-ariaAdvancedFilterBuilderFilterItem',
            'Filter Condition',
        ),
        ariaAdvancedFilterBuilderGroupItem: localize(
            '@sage/xtrem-ui/table-ariaAdvancedFilterBuilderGroupItem',
            'Filter Group',
        ),
        ariaAdvancedFilterBuilderColumn: localize('@sage/xtrem-ui/table-ariaAdvancedFilterBuilderColumn', 'Column'),
        ariaAdvancedFilterBuilderOption: localize('@sage/xtrem-ui/table-ariaAdvancedFilterBuilderOption', 'Option'),
        ariaAdvancedFilterBuilderValueP: localize('@sage/xtrem-ui/table-ariaAdvancedFilterBuilderValueP', 'Value'),
        ariaAdvancedFilterBuilderJoinOperator: localize(
            '@sage/xtrem-ui/table-ariaAdvancedFilterBuilderJoinOperator',
            'Join Operator',
        ),
        ariaAdvancedFilterInput: localize('@sage/xtrem-ui/table-ariaAdvancedFilterInput', 'Advanced Filter Input'),
        ariaChecked: localize('@sage/xtrem-ui/table-ariaChecked', 'checked'),
        ariaColumn: localize('@sage/xtrem-ui/table-ariaColumn', 'Column'),
        ariaColumnGroup: localize('@sage/xtrem-ui/table-ariaColumnGroup', 'Column Group'),
        ariaColumnFiltered: localize('@sage/xtrem-ui/table-ariaColumnFiltered', 'Column Filtered'),
        ariaColumnSelectAll: localize('@sage/xtrem-ui/table-ariaColumnSelectAll', 'Toggle All Columns Visibility'),
        ariaDateFilterInput: localize('@sage/xtrem-ui/table-ariaDateFilterInput', 'Date Filter Input'),
        ariaDefaultListName: localize('@sage/xtrem-ui/table-ariaDefaultListName', 'List'),
        ariaFilterColumnsInput: localize('@sage/xtrem-ui/table-ariaFilterColumnsInput', 'Filter Columns Input'),
        ariaFilterFromValue: localize('@sage/xtrem-ui/table-ariaFilterFromValue', 'Filter from value'),
        ariaFilterInput: localize('@sage/xtrem-ui/table-ariaFilterInput', 'Filter Input'),
        ariaFilterList: localize('@sage/xtrem-ui/table-ariaFilterList', 'Filter List'),
        ariaFilterToValue: localize('@sage/xtrem-ui/table-ariaFilterToValue', 'Filter to value'),
        ariaFilterValue: localize('@sage/xtrem-ui/table-ariaFilterValue', 'Filter Value'),
        ariaFilterMenuOpen: localize('@sage/xtrem-ui/table-ariaFilterMenuOpen', 'Open Filter Menu'),
        ariaFilteringOperator: localize('@sage/xtrem-ui/table-ariaFilteringOperator', 'Filtering Operator'),
        ariaHidden: localize('@sage/xtrem-ui/table-ariaHidden', 'hidden'),
        ariaIndeterminate: localize('@sage/xtrem-ui/table-ariaIndeterminate', 'indeterminate'),
        ariaInputEditor: localize('@sage/xtrem-ui/table-ariaInputEditor', 'Input Editor'),
        ariaMenuColumn: localize('@sage/xtrem-ui/table-ariaMenuColumn', 'Press ALT DOWN to open column menu'),
        ariaFilterColumn: localize('@sage/xtrem-ui/table-ariaFilterColumn', 'Press CTRL ENTER to open filter'),
        ariaRowDeselect: localize('@sage/xtrem-ui/table-ariaRowDeselect', 'Press SPACE to deselect this row'),
        ariaHeaderSelection: localize('@sage/xtrem-ui/table-ariaHeaderSelection', 'Column with Header Selection'),
        ariaRowSelectAll: localize('@sage/xtrem-ui/table-ariaRowSelectAll', 'Press Space to toggle all rows selection'),
        ariaRowToggleSelection: localize(
            '@sage/xtrem-ui/table-ariaRowToggleSelection',
            'Press Space to toggle row selection',
        ),
        ariaRowSelect: localize('@sage/xtrem-ui/table-ariaRowSelect', 'Press SPACE to select this row'),
        ariaRowSelectionDisabled: localize(
            '@sage/xtrem-ui/table-ariaRowSelectionDisabled',
            'Row Selection is disabled for this row',
        ),
        ariaSearch: localize('@sage/xtrem-ui/table-ariaSearch', 'Search'),
        ariaSortableColumn: localize('@sage/xtrem-ui/table-ariaSortableColumn', 'Press ENTER to sort'),
        ariaToggleVisibility: localize('@sage/xtrem-ui/table-ariaToggleVisibility', 'Press SPACE to toggle visibility'),
        ariaToggleCellValue: localize('@sage/xtrem-ui/table-ariaToggleCellValue', 'Press SPACE to toggle cell value'),
        ariaUnchecked: localize('@sage/xtrem-ui/table-ariaUnchecked', 'unchecked'),
        ariaVisible: localize('@sage/xtrem-ui/table-ariaVisible', 'visible'),
        ariaSearchFilterValues: localize('@sage/xtrem-ui/table-ariaSearchFilterValues', 'Search filter values'),
        ariaPageSizeSelectorLabel: localize('@sage/xtrem-ui/table-ariaPageSizeSelectorLabel', 'Page Size'),
        ariaChartMenuClose: localize('@sage/xtrem-ui/table-ariaChartMenuClose', 'Close Chart Edit Menu'),
        ariaChartSelected: localize('@sage/xtrem-ui/table-ariaChartSelected', 'Selected'),
        ariaSkeletonCellLoadingFailed: localize(
            '@sage/xtrem-ui/table-ariaSkeletonCellLoadingFailed',
            'Row failed to load',
        ),
        ariaSkeletonCellLoading: localize('@sage/xtrem-ui/table-ariaSkeletonCellLoading', 'Row data is loading'),

        // ARIA Labels for Drop Zones
        ariaRowGroupDropZonePanelLabel: localize('@sage/xtrem-ui/table-ariaRowGroupDropZonePanelLabel', 'Row Groups'),
        ariaValuesDropZonePanelLabel: localize('@sage/xtrem-ui/table-ariaValuesDropZonePanelLabel', 'Values'),
        ariaPivotDropZonePanelLabel: localize('@sage/xtrem-ui/table-ariaPivotDropZonePanelLabel', 'Column Labels'),
        ariaDropZoneColumnComponentDescription: localize(
            '@sage/xtrem-ui/table-ariaDropZoneColumnComponentDescription',
            'Press DELETE to remove',
        ),
        ariaDropZoneColumnValueItemDescription: localize(
            '@sage/xtrem-ui/table-ariaDropZoneColumnValueItemDescription',
            'Press ENTER to change the aggregation type',
        ),
        ariaDropZoneColumnGroupItemDescription: localize(
            '@sage/xtrem-ui/table-ariaDropZoneColumnGroupItemDescription',
            'Press ENTER to sort',
        ),
        ariaDropZoneColumnComponentAggFuncSeparator: localize(
            '@sage/xtrem-ui/table-ariaDropZoneColumnComponentAggFuncSeparator',
            ' of ',
        ),
        ariaDropZoneColumnComponentSortAscending: localize(
            '@sage/xtrem-ui/table-ariaDropZoneColumnComponentSortAscending',
            'ascending',
        ),
        ariaDropZoneColumnComponentSortDescending: localize(
            '@sage/xtrem-ui/table-ariaDropZoneColumnComponentSortDescending',
            'descending',
        ),

        ariaLabelDialog: localize('@sage/xtrem-ui/table-ariaLabelDialog', 'Dialog'),

        ariaLabelColumnMenu: localize('@sage/xtrem-ui/table-ariaLabelColumnMenu', 'Column Menu'),
        ariaLabelColumnFilter: localize('@sage/xtrem-ui/table-ariaLabelColumnFilter', 'Column Filter'),

        ariaLabelCellEditor: localize('@sage/xtrem-ui/table-ariaLabelCellEditor', 'Cell Editor'),
        ariaLabelSelectField: localize('@sage/xtrem-ui/table-ariaLabelSelectField', 'Select Field'),

        // aria labels for rich select
        ariaLabelRichSelectField: localize('@sage/xtrem-ui/table-ariaLabelRichSelectField', 'Rich Select Field'),
        ariaLabelRichSelectToggleSelection: localize(
            '@sage/xtrem-ui/table-ariaLabelRichSelectToggleSelection',
            'Press SPACE to toggle selection',
        ),
        ariaLabelRichSelectDeselectAllItems: localize(
            '@sage/xtrem-ui/table-ariaLabelRichSelectDeselectAllItems',
            'Press DELETE to deselect all items',
        ),
        ariaLabelRichSelectDeleteSelection: localize(
            '@sage/xtrem-ui/table-ariaLabelRichSelectDeleteSelection',
            'Press DELETE to deselect item',
        ),

        ariaLabelTooltip: localize('@sage/xtrem-ui/table-ariaLabelTooltip', 'Tooltip'),
        ariaLabelContextMenu: localize('@sage/xtrem-ui/table-ariaLabelContextMenu', 'Context Menu'),
        ariaLabelSubMenu: localize('@sage/xtrem-ui/table-ariaLabelSubMenu', 'SubMenu'),
        ariaLabelAggregationFunction: localize(
            '@sage/xtrem-ui/table-ariaLabelAggregationFunction',
            'Aggregation Function',
        ),
        ariaLabelAdvancedFilterAutocomplete: localize(
            '@sage/xtrem-ui/table-ariaLabelAdvancedFilterAutocomplete',
            'Advanced Filter Autocomplete',
        ),
        ariaLabelAdvancedFilterBuilderAddField: localize(
            '@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderAddField',
            'Advanced Filter Builder Add Field',
        ),
        ariaLabelAdvancedFilterBuilderColumnSelectField: localize(
            '@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderColumnSelectField',
            'Advanced Filter Builder Column Select Field',
        ),
        ariaLabelAdvancedFilterBuilderOptionSelectField: localize(
            '@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderOptionSelectField',
            'Advanced Filter Builder Option Select Field',
        ),
        ariaLabelAdvancedFilterBuilderJoinSelectField: localize(
            '@sage/xtrem-ui/table-ariaLabelAdvancedFilterBuilderJoinSelectField',
            'Advanced Filter Builder Join Operator Select Field',
        ),

        // ARIA Labels for the Side Bar
        ariaColumnPanelList: localize('@sage/xtrem-ui/table-ariaColumnPanelList', 'Column List'),
        ariaFilterPanelList: localize('@sage/xtrem-ui/table-ariaFilterPanelList', 'Filter List'),

        // Number Format (Status Bar, Pagination Panel)
        thousandSeparator: localize('@sage/xtrem-ui/table-thousandSeparator', ','),
        decimalSeparator: localize('@sage/xtrem-ui/table-decimalSeparator', '.'),

        // Data types
        true: localize('@sage/xtrem-ui/table-true', 'True'),
        false: localize('@sage/xtrem-ui/table-false', 'False'),
        invalidDate: localize('@sage/xtrem-ui/table-invalidDate', 'Invalid Date'),
        invalidNumber: localize('@sage/xtrem-ui/table-invalidNumber', 'Invalid Number'),
        january: localize('@sage/xtrem-ui/table-january', 'January'),
        february: localize('@sage/xtrem-ui/table-february', 'February'),
        march: localize('@sage/xtrem-ui/table-march', 'March'),
        april: localize('@sage/xtrem-ui/table-april', 'April'),
        may: localize('@sage/xtrem-ui/table-may', 'May'),
        june: localize('@sage/xtrem-ui/table-june', 'June'),
        july: localize('@sage/xtrem-ui/table-july', 'July'),
        august: localize('@sage/xtrem-ui/table-august', 'August'),
        september: localize('@sage/xtrem-ui/table-september', 'September'),
        october: localize('@sage/xtrem-ui/table-october', 'October'),
        november: localize('@sage/xtrem-ui/table-november', 'November'),
        december: localize('@sage/xtrem-ui/table-december', 'December'),

        // Time formats
        timeFormatSlashesDDMMYYYY: localize('@sage/xtrem-ui/table-timeFormatSlashesDDMMYYYY', 'DD/MM/YYYY'),
        timeFormatSlashesMMDDYYYY: localize('@sage/xtrem-ui/table-timeFormatSlashesMMDDYYYY', 'MM/DD/YYYY'),
        timeFormatSlashesDDMMYY: localize('@sage/xtrem-ui/table-timeFormatSlashesDDMMYY', 'DD/MM/YY'),
        timeFormatSlashesMMDDYY: localize('@sage/xtrem-ui/table-timeFormatSlashesMMDDYY', 'MM/DD/YY'),
        timeFormatDotsDDMYY: localize('@sage/xtrem-ui/table-timeFormatDotsDDMYY', 'DD.M.YY'),
        timeFormatDotsMDDYY: localize('@sage/xtrem-ui/table-timeFormatDotsMDDYY', 'M.DD.YY'),
        timeFormatDashesYYYYMMDD: localize('@sage/xtrem-ui/table-timeFormatDashesYYYYMMDD', 'YYYY-MM-DD'),
        timeFormatSpacesDDMMMMYYYY: localize('@sage/xtrem-ui/table-timeFormatSpacesDDMMMMYYYY', 'DD MMMM YYYY'),
        timeFormatHHMMSS: localize('@sage/xtrem-ui/table-timeFormatHHMMSS', 'HH:MM:SS'),
        timeFormatHHMMSSAmPm: localize('@sage/xtrem-ui/table-timeFormatHHMMSSAmPm', 'HH:MM:SS AM/PM'),
    }),
);

export default localeText;
