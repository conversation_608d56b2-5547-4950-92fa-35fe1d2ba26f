import type { <PERSON>ClickedEvent, CellKeyDownEvent, RowClickedEvent } from '@ag-grid-community/core';
import { get, merge, set, unset } from 'lodash';
import type { OnRowClickFunction } from '../../component/field/table/table-component-types';
import type { NestedFieldsProperties, NestedFieldTypesWithoutTechnical } from '../../component/nested-fields';
import { FieldKey } from '../../component/types';
import { getStore } from '../../redux';
import { dispatchDefaultValuesSet } from '../../redux/actions';
import type { CollectionValue } from '../../service/collection-data-service';
import { RecordActionType } from '../../service/collection-data-types';
import { fetchNestedDefaultValues } from '../../service/graphql-service';
import type { ScreenBase } from '../../service/screen-base';
import { SINGLE_INLINE_ACTION_CSS_CLASS, TABLE_INLINE_ACTION_CSS_CLASS, TUNNEL_LINK_CLASS } from '../constants';
import { isChildOfElementWithAttributeValue, isChildOfElementWithClass } from '../dom';
import { triggerFieldEvent, triggerNestedFieldEvent } from '../events';
import { cleanMetadataFromRecord, splitValueToMergedValue } from '../transformers';
import { COLUMN_ID_ROW_ACTIONS, COLUMN_ID_ROW_SELECTION } from './ag-grid-column-config';
import type { AgGridColumnConfigWithScreenIdAndColDef } from './ag-grid-utility-types';

export const onCellClickHandler =
    (
        screenId: string,
        tableElementId: string,
        nestedFieldProperties: NestedFieldsProperties<NestedFieldTypesWithoutTechnical, ScreenBase>,
        isParentFieldDisabled: boolean,
    ) =>
    (event: CellClickedEvent): void => {
        if (!isParentFieldDisabled) {
            const row = splitValueToMergedValue(event.data);
            triggerNestedFieldEvent(
                screenId,
                tableElementId,
                nestedFieldProperties,
                'onClick',
                event.data._id,
                row,
                event.data.__level ?? 0,
            );
        }
    };

export const onCellValueChangedHandler =
    (
        screenId: string,
        tableElementId: string,
        nestedFieldProperties: NestedFieldsProperties<NestedFieldTypesWithoutTechnical, ScreenBase>,
        value: () => CollectionValue,
        isInLookupDialog?: boolean,
    ): ((params: any) => Promise<void>) =>
    async (params: any): Promise<void> => {
        if (typeof params.newValue !== 'object' && String(params.newValue) === String(params.oldValue)) {
            return;
        }

        const columnId: string = params.colDef.field;
        const recordId: string = params.data._id;
        const cellValue = get(params.data, params.colDef.field, null);
        let eventData;

        let updatedRow = await value().setCellValue({
            recordId,
            columnId,
            value: cellValue,
            level: params.data.__level,
            isOrganicChange: true,
            addToDirty: params.data.__phantom || columnId.includes('.') ? [columnId] : undefined,
        });

        if (updatedRow.__action === RecordActionType.ADDED) {
            const level = updatedRow.__level;
            const state = getStore().getState();
            const defaultValues = state.screenDefinitions[screenId].metadata.uiComponentProperties[tableElementId]
                .isTransient
                ? {}
                : await fetchNestedDefaultValues({
                      screenId,
                      elementId: tableElementId,
                      isNewRow: Boolean(updatedRow.__phantom),
                      recordId,
                      nestedElementId: params.colDef.field,
                      level,
                  });
            delete defaultValues?.nestedDefaults?._id;
            // We delete the default values for the columns that are already dirty, so we won't overwrite the user's input
            Array.from(updatedRow.__dirtyColumns || []).forEach((k: string) => {
                unset(defaultValues.nestedDefaults, k);
            });
            const updatedRecord = { _id: recordId };
            set(updatedRecord, columnId, cellValue);
            value().setRecordValue({
                recordData: merge(updatedRecord, defaultValues.nestedDefaults),
                level,
                toBeMarkedAsDirty: [columnId],
            });
            if (defaultValues.pageDefaults) {
                dispatchDefaultValuesSet({
                    screenDefinition: state.screenDefinitions[screenId],
                    plugins: state.plugins,
                    nodeTypes: state.nodeTypes,
                    defaultValues: defaultValues.pageDefaults,
                });
            }
            updatedRow = value().getRawRecord({ id: recordId, level, cleanMetadata: false });
            eventData = splitValueToMergedValue(cleanMetadataFromRecord(updatedRow));
        } else {
            eventData = splitValueToMergedValue(cleanMetadataFromRecord({ ...params.data, ...updatedRow }));
        }

        params.node.setData(updatedRow);

        if (!isInLookupDialog) {
            // trigger "onChange" of nested field
            await triggerNestedFieldEvent(
                screenId,
                tableElementId,
                nestedFieldProperties,
                'onChange',
                columnId,
                eventData,
                params.data.__level ?? 0,
            );
            // trigger "onChange" of table field
            await triggerFieldEvent(
                screenId,
                tableElementId,
                'onChange',
                columnId,
                eventData,
                params.data.__level ?? 0,
            );
        }
    };

export const onCellKeyDown = (event: CellKeyDownEvent): void => {
    const nativeEvent = event.event as KeyboardEvent;
    if (nativeEvent.key === 'Enter') {
        if (
            (event.colDef as AgGridColumnConfigWithScreenIdAndColDef)?.context.isEditable?.(event.data) &&
            (event.colDef.type === FieldKey.Switch || event.colDef.type === FieldKey.Checkbox)
        ) {
            event.node.setDataValue(event.colDef.field!, !event.value);
            return;
        }

        if (event.colDef.type === COLUMN_ID_ROW_SELECTION) {
            event.node.setSelected(true, false);
        }
    }
};

/** Event listener, triggered when the user clicks any part of the row apart from the dropdown actions */
export const onRowClick =
    (onRowClickCallback?: OnRowClickFunction) =>
    (event: RowClickedEvent): void => {
        const jsEvent = event.event as KeyboardEvent | MouseEvent;
        // Filtering out dropdown action clicks
        const target = event.event?.target as HTMLElement;

        const isModifierKeyPushed = jsEvent?.ctrlKey || jsEvent?.metaKey;
        const isPhantomXButton = event.rowPinned === 'top' && target.getAttribute('data-element') === 'cross_circle';
        const isDropdownAction =
            target.getAttribute('col-id') === COLUMN_ID_ROW_ACTIONS ||
            target.getAttribute('data-element') === 'ellipsis_vertical' ||
            isPhantomXButton;
        const isInlineAction =
            Array.from(target.parentElement?.classList || []).indexOf(TABLE_INLINE_ACTION_CSS_CLASS) !== -1;
        const isSingleAction =
            Array.from(target.parentElement?.classList || []).indexOf(SINGLE_INLINE_ACTION_CSS_CLASS) !== -1;
        const isTunnelLink = isChildOfElementWithClass(target, TUNNEL_LINK_CLASS);
        const isSelectColumn =
            isChildOfElementWithAttributeValue(target, 'col-id', COLUMN_ID_ROW_SELECTION) ||
            isChildOfElementWithClass(target, 'ag-selection-checkbox') ||
            (target.childElementCount === 1 &&
                isChildOfElementWithAttributeValue(target.firstChild as Element, 'col-id', COLUMN_ID_ROW_SELECTION));

        // Row action is only triggered if the row has data, is not a group and was not triggered by clicking on the dropdown actions menu
        if (
            onRowClickCallback &&
            event.data &&
            !event.data.__isGroup &&
            !isDropdownAction &&
            !isSingleAction &&
            !isSelectColumn &&
            !isTunnelLink &&
            !isInlineAction
        ) {
            onRowClickCallback(event.data._id, undefined, isModifierKeyPushed)();
        }
    };
