import type {
    <PERSON>Def,
    Column,
    ICellRendererParams,
    RowNode,
    SelectionColumnDef,
    ValueFormatterParams,
    ValueGetterParams,
    ValueParserParams,
    ValueSetterParams,
} from '@ag-grid-community/core';
import { formatDateToCurrentLocale, isValidIsoDate, type Datetime } from '@sage/xtrem-date-time';
import { CUSTOM_DATA_PROPERTY, type Dict, type LocalizeLocale } from '@sage/xtrem-shared';
import Loader from 'carbon-react/esm/components/loader';
import { get, isArray, isNil, isNumber, isObject, isPlainObject, isString, set } from 'lodash';
import type { MarkRequired } from 'ts-essentials';
import { navigationPanelId } from '../../component/container/navigation-panel/navigation-panel-types';
import type {
    NestedCheckboxProperties,
    NestedDateProperties,
    NestedDropdownListProperties,
    NestedFilterSelectProperties,
    NestedIconProperties,
    NestedImageProperties,
    NestedLinkProperties,
    NestedProgressProperties,
    NestedReferenceProperties,
    NestedRelativeDateProperties,
    NestedSelectProperties,
    NestedSwitchProperties,
    SelectDecoratorProperties,
} from '../../component/decorators';
import { CheckboxCellRenderer } from '../../component/field/checkbox/checkbox-cell-renderer';
import { CountCellRenderer } from '../../component/field/count/count-cell-renderer';
import type { NestedCountProperties } from '../../component/field/count/count-types';
import DateCellEditor from '../../component/field/date/date-cell-editor';
import { DatetimeRangeCellEditor } from '../../component/field/datetime-range/datetime-range-cell-editor';
import { DatetimeRangeCellRenderer } from '../../component/field/datetime-range/datetime-range-cell-renderer';
import type { NestedDatetimeRangeProperties } from '../../component/field/datetime-range/datetime-range-types';
import { DatetimeCellEditor } from '../../component/field/datetime/datetime-cell-editor';
import { DatetimeCellRenderer } from '../../component/field/datetime/datetime-cell-renderer';
import { DropdownListCellEditor } from '../../component/field/dropdown-list/dropdown-list-cell-editor';
import { DropdownListCellRenderer } from '../../component/field/dropdown-list/dropdown-list-cell-renderer';
import type { InternalNestedGridProperties } from '../../component/field/field-control-objects';
import FilterSelectCellEditor from '../../component/field/filter-select/filter-select-cell-editor';
import { FilterSelectRenderer } from '../../component/field/filter-select/filter-select-cell-renderer';
import { IconCellRenderer } from '../../component/field/icon/icon-cell-renderer';
import { ImageCellRenderer } from '../../component/field/image/image-cell-renderer';
import { LabelCellRenderer } from '../../component/field/label/label-cell-renderer';
import type { NestedLabelProperties } from '../../component/field/label/label-decorator';
import { LinkCellRenderer } from '../../component/field/link/link-cell-renderer';
import NumericEditor from '../../component/field/numeric/numeric-cell-editor';
import type { NestedNumericProperties } from '../../component/field/numeric/numeric-types';
import { ProgressCellRenderer } from '../../component/field/progress/progress-cell-renderer';
import ReferenceCellEditor from '../../component/field/reference/reference-cell-editor';
import ReferenceRenderer from '../../component/field/reference/reference-cell-renderer';
import { getReferenceValueField } from '../../component/field/reference/reference-utils';
import { RelativeDateCellRenderer } from '../../component/field/relative-date/relative-date-cell-renderer';
import { SelectCellEditor } from '../../component/field/select/select-cell-editor';
import { SelectRenderer } from '../../component/field/select/select-cell-renderer';
import { SwitchRenderer } from '../../component/field/switch/switch-cell-renderer';
import type { SwitchCellRendererProps } from '../../component/field/switch/switch-types';
import type { InternalTableProperties } from '../../component/field/table/table-component-types';
import type { NestedTextProperties } from '../../component/field/text/text-types';
import type { HasOptionType, Mappable, Nested } from '../../component/field/traits';
import type {
    NestedField,
    NestedFieldTypesWithoutTechnical,
    NestedFieldsProperties,
} from '../../component/nested-fields';
import type { GridRowActionOrMenuSeparatorType, GridRowActionType } from '../../component/types';
import { FieldKey } from '../../component/types';
import { formatDatetime } from '../../component/ui/datetime/datetime-utils';
import {
    DesktopNestedGridEmptyComponent,
    DesktopTableEmptyComponent,
} from '../../component/ui/no-rows-found/no-rows-found-component';
import type { CellWrapperProps } from '../../component/ui/table-shared/cell/cell-wrapper';
import { CellWrapper } from '../../component/ui/table-shared/cell/cell-wrapper';
import { CheckboxCell } from '../../component/ui/table-shared/cell/checkbox-cell';
import { DefaultCellEditor } from '../../component/ui/table-shared/cell/default-cell-editor';
import { DefaultCellRenderer } from '../../component/ui/table-shared/cell/default-cell-renderer';
import { TableDropdownActionsCell } from '../../component/ui/table-shared/cell/table-dropdown-action-cell';
import { ValidationCellRenderer } from '../../component/ui/table-shared/cell/table-validation-cell-renderer';
import { BooleanFilter } from '../../component/ui/table-shared/filters/boolean/boolean-filter';
import ClearFloatingFilter from '../../component/ui/table-shared/filters/clear/clear-floating-filter';
import DateFilter from '../../component/ui/table-shared/filters/date/date-filter';
import DateFloatingFilter from '../../component/ui/table-shared/filters/date/date-floating-filter';
import DatetimeFilter from '../../component/ui/table-shared/filters/date/datetime-filter';
import NumericFilter from '../../component/ui/table-shared/filters/numeric/numeric-filter';
import NumericFloatingFilter from '../../component/ui/table-shared/filters/numeric/numeric-floating-filter';
import ReferenceFilter from '../../component/ui/table-shared/filters/reference/reference-filter';
import ReferenceFloatingFilter from '../../component/ui/table-shared/filters/reference/reference-floating-filter';
import type { CollectionValue } from '../../service/collection-data-service';
import { calculateDeepPaths } from '../../service/customization-service';
import { localize, localizeEnumMember } from '../../service/i18n-service';
import type { FormattedNodeDetails, FormattedNodeDetailsProperty } from '../../service/metadata-types';
import type { AccessBindings } from '../../service/page-definition';
import type { ScreenBase } from '../../service/screen-base';
import type { ScreenBaseGenericType } from '../../types';
import { ContextType, GraphQLKind, GraphQLTypes } from '../../types';
import { getElementAccessStatusWithoutId } from '../access-utils';
import { xtremConsole } from '../console';
import { formatNumericValue, getScalePrefixPostfixFromUnit } from '../formatters';
import { convertDeepBindToPathNotNull } from '../nested-field-utils';
import { findDeepPropertyType } from '../node-utils';
import { resolveByValue } from '../resolve-value-utils';
import { cleanMetadataFromRecord, schemaTypeNameFromNodeName, splitValueToMergedValue } from '../transformers';
import type { Merge, SetRequired, ValueOrCallbackWithFieldValue } from '../types';
import {
    suppressKeyboardEventForActionCellEditor,
    suppressKeyboardEventForContainedTarget,
    suppressKeyboardEventForReferenceCellEditor,
} from './ag-grid-cell-editor-utils';
import type {
    AgGridColumnConfigWithAllColumns,
    AgGridColumnConfigWithScreenIdAndColDef,
} from './ag-grid-utility-types';

function getFilterableScalarPropertyType(type: GraphQLTypes): FormattedNodeDetailsProperty {
    return {
        kind: GraphQLKind.Scalar,
        type,
        name: '',
        canFilter: true,
    };
}

const textFilterOptions = (): Array<
    | string
    | {
          displayKey: string;
          displayName: string;
          predicate: (filterValues: string[], cellValue: any) => boolean;
          numberOfInputs?: number;
      }
> => [
    'contains',
    'notContains',
    'startsWith',
    'endsWith',
    'equals',
    'notEqual',
    {
        displayKey: 'greaterThanOrEqual',
        displayName: localize('@sage/xtrem-ui/greaterThanOrEqual', 'Greater than or equal to'),
        predicate: ([filterValue]: string[], cellValue: string): boolean =>
            String(cellValue).localeCompare(String(filterValue)) > 0 ||
            String(filterValue).toLowerCase() === String(cellValue).toLowerCase(),
    },
    {
        displayKey: 'lessThanOrEqual',
        displayName: localize('@sage/xtrem-ui/table-lessThanOrEqual', 'Less than or equal'),
        predicate: ([filterValue]: string[], cellValue: string): boolean =>
            String(cellValue).localeCompare(String(filterValue)) < 0 ||
            String(filterValue).toLowerCase() === String(cellValue).toLowerCase(),
    },
    {
        displayKey: 'inRange',
        displayName: localize('@sage/xtrem-ui/table-inRange', 'In range'),
        predicate: ([filterValue1, filterValue2]: string[], cellValue: string): boolean =>
            String(cellValue).localeCompare(String(filterValue1)) < 0 &&
            String(cellValue).localeCompare(String(filterValue2)) > 0,
        numberOfInputs: 2,
    },
]; // ['equals', 'notEqual', 'contains', 'notContains', 'startsWith', 'endsWith']
/**
 * ['equals', 'notEqual', 'contains', 'notContains', 'startsWith', 'endsWith', 'lessThan', 'lessThanOrEqual', 'greaterThan', 'greaterThanOrEqual', 'inRange', 'empty']
 */
const numberFilterOptions = ['equals', 'notEqual', 'lessThanOrEqual', 'greaterThanOrEqual', 'inRange'];

interface CellEditorProps extends Omit<ICellRendererParams, 'value'> {
    eventKey: string | null;
    initialValue: any;
    onValueChange: (value: any) => void;
    stopEditing: (suppressNavigateAfterEdit?: boolean) => void;
    value: string;
}
export interface CellParams<T extends Nested<any> = Nested, V = string>
    extends Omit<CellEditorProps, 'colDef' | 'value'>,
        CellProps<T> {
    colDef: MarkRequired<Omit<AgGridColumnConfigWithScreenIdAndColDef, 'cellRendererParams'>, 'field'> & {
        cellRendererParams: CellProps<T>;
    };
    value: V;
    node: RowNode;
    column: Column;
}
export interface CellProps<T> {
    columnId: string;
    contextNode?: keyof ScreenBaseGenericType<ScreenBase>;
    elementId: string;
    fieldProperties: WithNestedWrapper<T>;
    isEditing?: boolean;
    isParentFieldDisabled: boolean;
    isTableReadOnly?: ValueOrCallbackWithFieldValue<ScreenBase, boolean>;
    isTree: boolean;
    locale: LocalizeLocale;
    screenId: string;
    tableElementId: string;
    collectionValue: () => CollectionValue;
}

type ICellParams<T extends Nested<any>> = CellProps<T> | ((p: any) => CellProps<T>);
type ColumnProps<T extends Nested<any>> = Merge<
    SetRequired<ColDef, 'cellRenderer'>,
    {
        cellRendererParams: ICellParams<T>;
        cellEditorParams?: ICellParams<T>;
    }
>;

export interface ColumnConfigurationProperties<T extends Nested<any>> {
    collectionValue: () => CollectionValue;
    columnId: string;
    columnProperties: T;
    enumTypes: Dict<string[]>;
    hasFloatingFilters: boolean;
    isParentFieldDisabled: boolean;
    isReadOnly: ValueOrCallbackWithFieldValue<ScreenBase, boolean> | undefined;
    level: number;
    locale: LocalizeLocale;
    nodeTypes: Dict<FormattedNodeDetails>;
    propertyGraphType: FormattedNodeDetailsProperty | null;
    screenId: string;
    tableElementId: string;
    tableProperties: InternalTableProperties<ScreenBase> | InternalNestedGridProperties<ScreenBase>;
}

export type NestedProperties =
    | NestedReferenceProperties
    | NestedLabelProperties
    | NestedCheckboxProperties
    | NestedDateProperties
    | NestedTextProperties
    | NestedProgressProperties
    | NestedNumericProperties
    | NestedFilterSelectProperties
    | NestedDropdownListProperties
    | NestedSelectProperties
    | NestedSwitchProperties;

export const defaultValueGetter =
    (
        params: Pick<ValueGetterParams, 'colDef' | 'data'>,
        screenId: string,
        columnProperties: NestedFieldsProperties<NestedFieldTypesWithoutTechnical, ScreenBase>,
    ) =>
    (mapper: (value: any) => any = (v: any): any => v): any => {
        if (!params.colDef.field) {
            return null;
        }

        const map = (columnProperties as Mappable<ScreenBase>).map;
        if (!params.data) {
            return null;
        }

        const bind = convertDeepBindToPathNotNull(columnProperties.bind);
        // Do not display _id column for groups. Convention for groups is id = "__group-{groupValue}"
        const cellValue = params.data?.__isGroup && bind === '_id' ? '' : get(params.data, bind, null);

        if (map) {
            return resolveByValue({
                skipHexFormat: true,
                propertyValue: map,
                screenId,
                fieldValue: cellValue,
                rowValue: splitValueToMergedValue(cleanMetadataFromRecord(params.data)),
            });
        }
        return mapper(cellValue);
    };

function stringToNumber(value: string): number | string {
    const numberValue = Number(value);
    return Number.isNaN(numberValue) ? value : numberValue;
}
function stringToBoolean(value: string): boolean | string {
    return value === 'true' || value === 'false' ? value === 'true' : value;
}

export const defaultValueSetter = (
    params: ValueSetterParams,
    stringToValueConverter?: (value: string) => any,
): boolean => {
    const bind = params.colDef.field || params.column.getColId();
    const currentValue = get(params.data, bind, null);
    const value =
        stringToValueConverter && isString(params.newValue) ? stringToValueConverter(params.newValue) : params.newValue;
    const newValue = !isNil(value) ? value : null;
    if (currentValue === newValue) {
        return false;
    }

    set(params.data, bind, newValue);
    return true;
};
/**
 * Check the node type and colum properties to find the appropriate filter type
 * @param columnProperties
 * @param graphqlType
 * @param hasFloatingFilters
 * @returns
 */
const getFilterTypeFromGraphqlType = (props: ColumnConfigurationProperties<NestedProperties>): ColDef => {
    const properties: ColDef = {};

    // Check only whether the "_id" property of the reference node type is filterable for reference fields
    const graphqlType =
        props.columnProperties._controlObjectType === FieldKey.Reference
            ? findDeepPropertyType(props.propertyGraphType?.type, '_id', props.nodeTypes, true)
            : props.propertyGraphType;

    const parentNodeType = props.propertyGraphType?.parentNode
        ? props.nodeTypes[props.propertyGraphType.parentNode]
        : null;

    const isCustomField =
        props.propertyGraphType?.type === GraphQLTypes.Json && props.propertyGraphType.name === CUSTOM_DATA_PROPERTY;
    const { selectorSegments } = calculateDeepPaths(props.columnId);

    const subfieldFieldType = get(parentNodeType?.properties, selectorSegments.join('.'));

    if (
        !props.propertyGraphType?.type ||
        props.columnProperties.isTransient ||
        (props.columnProperties as Nested).canFilter === false ||
        graphqlType?.canSort === false
    ) {
        properties.sortable = false;
    }
    // If the column is transient, filtering is not allowed.
    if (
        !props.propertyGraphType?.type ||
        props.columnProperties.isTransient ||
        (props.columnProperties as Nested).canFilter === false ||
        graphqlType?.canFilter === false
    ) {
        properties.floatingFilter = false;
        properties.filter = false;
        return properties;
    }

    properties.floatingFilter = props.hasFloatingFilters;
    properties.filter = true;

    if (
        props.propertyGraphType?.type === GraphQLTypes.Enum ||
        (isCustomField && subfieldFieldType && (props.columnProperties as SelectDecoratorProperties).options)
    ) {
        const enumType = props.propertyGraphType?.enumType || (props.columnProperties as HasOptionType).optionType;
        const formattedEnumType = schemaTypeNameFromNodeName(enumType!);
        applyEnumFilterOptions(
            properties,
            props.screenId,
            props.enumTypes[formattedEnumType],
            enumType,
            props.hasFloatingFilters,
            (props.columnProperties as SelectDecoratorProperties).options,
            (props.columnProperties as SelectDecoratorProperties).map,
        );
    } else if (
        props.propertyGraphType?.type === GraphQLTypes.String ||
        props.propertyGraphType?.type === GraphQLTypes.ID ||
        (props.propertyGraphType.type === GraphQLTypes.IntOrString && props.propertyGraphType.name === '_id')
    ) {
        applyTextFilterOptions(properties, props.hasFloatingFilters);
    } else if (props.propertyGraphType?.type === GraphQLTypes.Date) {
        applyDateFilterOptions({ properties, hasFloatingFilters: props.hasFloatingFilters, locale: props.locale });
    } else if (props.propertyGraphType?.type === GraphQLTypes.DateTime) {
        applyDatetimeFilterOptions({ properties, hasFloatingFilters: props.hasFloatingFilters, locale: props.locale });
    } else if (
        props.propertyGraphType?.type === GraphQLTypes.Int ||
        props.propertyGraphType?.type === GraphQLTypes.IntOrString ||
        props.propertyGraphType?.type === GraphQLTypes.Float ||
        props.propertyGraphType?.type === GraphQLTypes.Decimal
    ) {
        applyNumericFilterOptions(properties, props.hasFloatingFilters);
    } else if (props.propertyGraphType?.type && props.propertyGraphType?.kind === GraphQLKind.Object) {
        applyReferenceFilterOptions(properties, props);
    } else if (props.propertyGraphType?.type === GraphQLTypes.Boolean) {
        applyBooleanFilterOptions(properties, props);
    } else if (props.propertyGraphType?.type === GraphQLTypes.Json && props.columnId.split('.').length > 1) {
        // If the field is a deeply nested JSON column we apply the filters based on the column definition type because there is not a real server side data type
        switch (props.columnProperties._controlObjectType) {
            case FieldKey.Numeric:
                applyNumericFilterOptions(properties, props.hasFloatingFilters);
                break;
            case FieldKey.Text:
                applyTextFilterOptions(properties, props.hasFloatingFilters);
                break;
            case FieldKey.Date:
                applyDateFilterOptions({
                    properties,
                    hasFloatingFilters: props.hasFloatingFilters,
                    locale: props.locale,
                });
                break;
            // TODO: enum and enum array fields
            default:
                properties.floatingFilter = false;
                properties.filter = false;
        }
    } else {
        properties.floatingFilter = false;
        properties.filter = false;
    }

    return properties;
};

export const getReferenceFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedReferenceProperties>,
): ColumnProps<NestedReferenceProperties> => {
    const { columnProperties, screenId } = props;
    const cellProps = getCellProps(props);

    const valueGetter = (params: Pick<ValueGetterParams, 'colDef' | 'data'>): string =>
        defaultValueGetter(
            params,
            screenId,
            columnProperties,
        )(v => {
            if (
                v &&
                columnProperties.valueField &&
                getReferenceValueField(columnProperties) !== undefined &&
                get(v, getReferenceValueField(columnProperties))
            ) {
                return get(v, getReferenceValueField(columnProperties));
            }
            return '';
        });
    return {
        enableRowGroup: props.tableElementId === navigationPanelId,
        allowedAggFuncs: ['count'],
        cellEditor: FieldKey.Reference,
        filter: columnProperties.canFilter !== false,
        cellEditorParams: cellProps,
        cellRenderer: 'ReferenceRenderer',
        cellRendererParams: cellProps,
        minWidth: 100 + (columnProperties.columns ? 24 : 0) + (columnProperties.imageField ? 24 : 0),
        suppressKeyboardEvent: suppressKeyboardEventForReferenceCellEditor,
        valueGetter,
        valueParser: (params: ValueParserParams): any => {
            const newData = { ...params.data };
            set(newData, params.colDef.field!, params.newValue);
            return valueGetter({ colDef: params.colDef, data: newData });
        },
        valueFormatter: (params: ValueFormatterParams): string => {
            return params.data?.__isGroup &&
                params.data?.__groupKey === `${convertDeepBindToPathNotNull(columnProperties.bind)}._id`
                ? `${params.value || localize('@sage/xtrem-ui/no-value', 'No value')} (${params.data.__groupCount})`
                : params?.value;
        },
        cellEditorPopup: true,
        valueSetter: (params: ValueSetterParams): boolean => {
            if (!isNil(params.newValue) && !isPlainObject(params.newValue)) {
                return false;
            }
            return defaultValueSetter(params);
        },
        ...getFilterTypeFromGraphqlType({
            ...props,
            ...(props.propertyGraphType == null && props.tableProperties?.isTransient && props.columnProperties.node
                ? {
                      propertyGraphType: {
                          name: props.columnProperties.bind!,
                          type: props.columnProperties.node as string,
                          kind: GraphQLKind.Object,
                          targetNode: props.columnProperties.node as string,
                          canFilter: true,
                      },
                  }
                : {}),
        }),
    };
};

export const getImageFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedImageProperties>,
): ColumnProps<NestedImageProperties> => ({
    editable: false,
    sortable: false,
    filter: false,
    cellRendererParams: getCellProps(props),
    cellRenderer: FieldKey.Image,
    valueGetter: (params: Pick<ValueGetterParams, 'colDef' | 'data'>): string =>
        defaultValueGetter(
            params,
            props.screenId,
            props.columnProperties,
        )(v => {
            return v?.value;
        }),
    valueSetter: (params: ValueSetterParams): boolean => {
        if (!isNil(params.newValue) && !isPlainObject(params.newValue)) {
            return false;
        }
        return defaultValueSetter(params);
    },
});

export const getSelectionColumnDef = ({
    contextType,
    fieldProperties,
}: {
    contextType?: ContextType;
    fieldProperties: Pick<ColumnConfigurationProperties<NestedProperties>['columnProperties'], '_controlObjectType'>;
}): SelectionColumnDef => ({
    pinned: 'left',
    lockPinned: true,
    headerClass: (): string | undefined => {
        if (
            contextType === ContextType.navigationPanel ||
            (contextType === ContextType.dialog &&
                (fieldProperties?._controlObjectType === FieldKey.MultiReference ||
                    fieldProperties?._controlObjectType === undefined))
        ) {
            return undefined;
        }
        // hide select all checkbox when not in navigation panel
        return 'hidden';
    },
});

export const getLabelFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedLabelProperties>,
): ColumnProps<NestedLabelProperties> => {
    const { columnProperties, enumTypes } = props;
    const enumOptions = columnProperties.optionType
        ? enumTypes[schemaTypeNameFromNodeName(columnProperties.optionType)]
        : undefined;

    const localizedOptions = enumOptions
        ? enumOptions.reduce((value: Dict<string>, key: string) => {
              value[key] = localizeEnumMember(columnProperties.optionType!, key);
              return value;
          }, {} as Dict<string>)
        : undefined;

    const cellProps = { ...getCellProps(props), localizedOptions, enumOptions };

    const properties: ColumnProps<NestedLabelProperties> = {
        editable: false,
        cellRenderer: FieldKey.Label,
        cellRendererParams: cellProps,
    };

    if (
        props.columnProperties.optionType &&
        props.columnProperties.canFilter !== false &&
        !props.columnProperties.isTransient
    ) {
        properties.filter = 'agSetColumnFilter';
        properties.filterParams = {
            values: enumOptions,
            defaultToNothingSelected: true,
            defaultJoinOperator: 'OR',
            valueFormatter: (v: any): string => {
                return localizedOptions?.[v.value] || v.value;
            },
        };
        properties.suppressHeaderMenuButton = false;
    }

    return {
        ...properties,
        ...getFilterTypeFromGraphqlType({
            ...props,
            // If the table is transient we just handle the label type as a string
            ...(props.tableProperties?.isTransient && !props.propertyGraphType
                ? getFilterableScalarPropertyType(GraphQLTypes.String)
                : {}),
        }),
        enableRowGroup: props.tableElementId === navigationPanelId,
        valueGetter: (params: ValueGetterParams): string =>
            defaultValueGetter(
                params,
                props.screenId,
                columnProperties,
            )(v => {
                return v ?? '';
            }),
        valueSetter: (params: ValueSetterParams): boolean => {
            if (localizedOptions && !localizedOptions[params.newValue]) {
                return false;
            }
            return defaultValueSetter(params);
        },
    };
};

export const getIconFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedIconProperties>,
): ColumnProps<NestedIconProperties> => ({
    editable: false,
    filter: false,
    sortable: false,
    cellRenderer: FieldKey.Icon,
    cellRendererParams: getCellProps(props),
    valueSetter: (params: ValueSetterParams): boolean => {
        if (!isNil(params.newValue) && !isPlainObject(params.newValue)) {
            return false;
        }
        return defaultValueSetter(params);
    },
    ...getFilterTypeFromGraphqlType(props),
});

export const getCountFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedCountProperties>,
): ColumnProps<NestedCountProperties> => ({
    editable: false,
    filter: false,
    sortable: false,
    cellRenderer: FieldKey.Count,
    cellRendererParams: getCellProps(props),
    valueSetter: (params: ValueSetterParams): boolean => {
        return defaultValueSetter(params, stringToNumber);
    },
});

export const getCheckboxFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedCheckboxProperties>,
): ColumnProps<NestedCheckboxProperties> => {
    // If the table is transient we just handle the checkbox type as a boolean
    if (props.tableProperties?.isTransient && !props.propertyGraphType?.type) {
        props.propertyGraphType = getFilterableScalarPropertyType(GraphQLTypes.Boolean);
    }

    const cellParams = getCellProps(props);
    return {
        editable: true,
        cellRendererParams: cellParams,
        cellEditorParams: { ...cellParams, isEditing: true },
        cellRenderer: FieldKey.Checkbox,
        cellEditor: FieldKey.Checkbox,
        minWidth: 44,
        ...getFilterTypeFromGraphqlType(props),
        valueSetter: (params: ValueSetterParams): boolean => {
            return defaultValueSetter(params, stringToBoolean);
        },
    };
};

export const getDateFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedDateProperties>,
): ColumnProps<NestedDateProperties> => {
    const cellProps = getCellProps(props);
    // If the table is transient we just handle the date type as a date
    if (props.tableProperties?.isTransient && !props.propertyGraphType?.type) {
        props.propertyGraphType = getFilterableScalarPropertyType(GraphQLTypes.Date);
    }

    return {
        cellRenderer: DefaultCellRenderer,
        cellRendererParams: cellProps,
        cellEditor: FieldKey.Date,
        valueFormatter: (params: ValueFormatterParams): string => {
            if (params.value !== undefined && params.value !== null && isValidIsoDate(params.value)) {
                return formatDateToCurrentLocale(params.value, props.locale, 'FullDate');
            }
            return '';
        },
        cellEditorParams: cellProps,
        enableRowGroup: props.tableElementId === navigationPanelId,
        valueGetter: (params: ValueGetterParams): string =>
            defaultValueGetter(
                params,
                props.screenId,
                props.columnProperties,
            )(v => {
                return v ?? '';
            }),
        valueSetter: (params: ValueSetterParams): boolean => {
            if (!isNil(params.newValue) && !isValidIsoDate(params.newValue)) {
                return false;
            }
            return defaultValueSetter(params);
        },
        ...getFilterTypeFromGraphqlType(props),
    };
};

export const getDatetimeRangeFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedDateProperties>,
): ColumnProps<NestedDatetimeRangeProperties> => {
    const cellProps = getCellProps(props);
    // If the table is transient we just handle the datetimerange type as a datetime range
    if (props.tableProperties?.isTransient && !props.propertyGraphType?.type) {
        props.propertyGraphType = getFilterableScalarPropertyType(GraphQLTypes.DatetimeRange);
    }

    return {
        cellRenderer: DatetimeRangeCellRenderer,
        cellRendererParams: cellProps,
        cellEditor: FieldKey.DatetimeRange,
        valueFormatter: (params: ValueFormatterParams): string => {
            const start = (params.value?.start as Datetime)
                ? formatDatetime({ date: params.value.start as Datetime, locale: props.locale, separator: ' ' })
                : '';
            const end = (params.value?.end as Datetime)
                ? formatDatetime({ date: params.value.end as Datetime, locale: props.locale, separator: ' ' })
                : '';

            return start === '' && end === '' ? '' : [start, end].join(' - ');
        },
        cellEditorParams: { ...cellProps, isEditing: true },
        suppressKeyboardEvent: suppressKeyboardEventForContainedTarget,
        cellEditorPopup: true,
        filter: false,
        sortable: false,
        minWidth: 350,
        valueSetter: (params: ValueSetterParams): boolean => {
            if (!isNil(params.newValue) && !isPlainObject(params.newValue)) {
                return false;
            }
            return defaultValueSetter(params);
        },
        ...getFilterTypeFromGraphqlType(props),
    };
};

export const getDatetimeFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedDateProperties>,
): ColumnProps<NestedDatetimeRangeProperties> => {
    const cellProps = getCellProps(props);
    // If the table is transient we just handle the datetime type as a datetime
    if (props.tableProperties?.isTransient && !props.propertyGraphType?.type) {
        props.propertyGraphType = getFilterableScalarPropertyType(GraphQLTypes.DateTime);
    }

    return {
        cellRenderer: DatetimeCellRenderer,
        cellRendererParams: cellProps,
        cellEditor: FieldKey.Datetime,
        valueFormatter: (params: ValueFormatterParams): string => {
            return (params.value as Datetime)
                ? formatDatetime({ date: params.value as Datetime, locale: props.locale, separator: ' ' })
                : '';
        },
        cellEditorParams: { ...cellProps, isEditing: true },
        suppressKeyboardEvent: suppressKeyboardEventForContainedTarget,
        cellEditorPopup: true,
        minWidth: 175,
        ...getFilterTypeFromGraphqlType(props),
    };
};

const getNumericValueFormatter =
    (screenId: string, columnProperties: NestedNumericProperties, locale: LocalizeLocale) =>
    (params: ValueFormatterParams): string => {
        const computedUnitProperties = getScalePrefixPostfixFromUnit(screenId, locale, columnProperties, params.data);

        let value = formatNumericValue({
            screenId,
            value: params.value,
            scale: columnProperties.scale,
            rowValue: params.data,
            unitScale: computedUnitProperties?.scale,
            locale,
        });

        value = resolveTextPrefix(
            params.value,
            value,
            params.data,
            screenId,
            columnProperties.prefix ?? computedUnitProperties?.prefix,
        );

        return resolveTextPostfix(
            params.value,
            value,
            params.data,
            screenId,
            columnProperties.postfix ?? computedUnitProperties?.postfix,
        );
    };

const resolveTextPrefix = (
    initialValue: any,
    currentValue: any,
    data: any,
    screenId: string,
    prefix?: ValueOrCallbackWithFieldValue<ScreenBase, string>,
): string => {
    const textPrefix = resolveByValue({
        fieldValue: initialValue,
        propertyValue: prefix,
        rowValue: splitValueToMergedValue(cleanMetadataFromRecord(data)),
        screenId,
        skipHexFormat: true,
    });

    return textPrefix ? `${textPrefix} ${currentValue ?? ''}` : currentValue;
};

const resolveTextPostfix = (
    initialValue: any,
    currentValue: any,
    data: any,
    screenId: string,
    postfix?: ValueOrCallbackWithFieldValue<ScreenBase, string>,
): string => {
    const textPostfix = resolveByValue({
        fieldValue: initialValue,
        propertyValue: postfix,
        rowValue: splitValueToMergedValue(cleanMetadataFromRecord(data)),
        skipHexFormat: true,
        screenId,
    });

    return textPostfix ? `${currentValue ?? ''} ${textPostfix}` : currentValue;
};

export const getValueFormatter =
    (screenId: string, columnProperties: NestedTextProperties | NestedNumericProperties) =>
    (params: ValueFormatterParams): string => {
        const value = resolveTextPrefix(params.value, params.value, params.data, screenId, columnProperties.prefix);
        return resolveTextPostfix(params.value, value, params.data, screenId, columnProperties.postfix);
    };

const applyTextFilterOptions = (properties: ColDef, hasFloatingFilters = false): void => {
    properties.suppressHeaderMenuButton = false;
    properties.filter = 'agTextColumnFilter';
    properties.menuTabs = hasFloatingFilters ? ['generalMenuTab'] : ['filterMenuTab', 'generalMenuTab'];
    properties.filterParams = {
        ...properties.filterParams,
        filterOptions: textFilterOptions(),
        defaultJoinOperator: 'OR',
    };
};

const applyEnumFilterOptions = (
    properties: ColDef,
    screenId: string,
    enumDefinition?: string[],
    optionType?: string,
    hasFloatingFilters = false,
    options?: ValueOrCallbackWithFieldValue<ScreenBase, string[]>,
    map?: (value?: any, rowValue?: any) => string,
): void => {
    properties.menuTabs = hasFloatingFilters ? ['generalMenuTab'] : ['filterMenuTab', 'generalMenuTab'];
    // If no enum definition was found for the enum type, we cannot filter on it.
    if ((!enumDefinition || !optionType) && (!options || !map)) {
        return;
    }

    const localizedOptions = enumDefinition?.reduce((value: Dict<string>, key: string) => {
        value[key] = optionType ? localizeEnumMember(optionType, key) : key;
        return value;
    }, {} as Dict<string>);

    properties.filter = 'agSetColumnFilter';
    properties.filterParams = {
        values: enumDefinition || options,
        defaultToNothingSelected: true,
        defaultJoinOperator: 'OR',
        valueFormatter: (v: any): string => {
            if (localizedOptions) {
                return localizedOptions?.[v.value];
            }

            if (map) {
                return resolveByValue({
                    propertyValue: map,
                    screenId,
                    skipHexFormat: true,
                    rowValue: null,
                    fieldValue: v.value,
                });
            }

            return v.value;
        },
    };
    properties.suppressHeaderMenuButton = false;
};

export const getTextFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedTextProperties>,
): ColumnProps<NestedTextProperties> => {
    const { columnProperties, screenId } = props;
    const properties: ColumnProps<NestedTextProperties> = {
        cellRenderer: DefaultCellRenderer,
        cellRendererParams: getCellProps(props),
        cellEditor: 'agTextCellEditor',
        valueSetter: (params: ValueSetterParams): boolean => {
            if (!isNil(params.newValue) && !isString(params.newValue)) {
                return false;
            }
            return defaultValueSetter(params);
        },
        valueGetter: (params: ValueGetterParams) =>
            defaultValueGetter(
                params,
                screenId,
                columnProperties,
            )((value: any) => {
                // Filter out functional developer introduced faulty values.
                if (isObject(value) || isArray(value)) {
                    xtremConsole.warn(`Invalid type set to table text field in ${params.colDef.field}`, value);
                    return null;
                }
                return value;
            }),
    };

    if (columnProperties.prefix || columnProperties.postfix) {
        properties.valueFormatter = getValueFormatter(screenId, columnProperties);
    }

    return {
        ...properties,
        ...getFilterTypeFromGraphqlType({
            ...props,
            // If the table is transient we just handle the label type as a string
            ...(props.tableProperties?.isTransient && !props.propertyGraphType?.type
                ? { propertyGraphType: getFilterableScalarPropertyType(GraphQLTypes.String) }
                : {}),
        }),
    };
};

export const getLinkFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedLinkProperties>,
): ColumnProps<NestedLinkProperties> => ({
    editable: false,
    cellRendererParams: getCellProps(props),
    cellRenderer: FieldKey.Link,
    onCellClicked: undefined,
    valueSetter: (params: ValueSetterParams): boolean => {
        if (!isNil(params.newValue) && !isString(params.newValue)) {
            return false;
        }
        return defaultValueSetter(params);
    },
    ...getFilterTypeFromGraphqlType({
        ...props,
        // If the table is transient we just handle the label type as a string
        ...(props.tableProperties?.isTransient && !props.propertyGraphType?.type
            ? { propertyGraphType: getFilterableScalarPropertyType(GraphQLTypes.String) }
            : {}),
    }),
});

export const getRelativeDateFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedRelativeDateProperties>,
): ColumnProps<NestedRelativeDateProperties> => ({
    editable: false,
    cellRendererParams: {
        ...getCellProps(props),
        relativeDateScope: props.propertyGraphType?.type === 'Date' ? 'date' : 'datetime',
    },
    cellRenderer: FieldKey.RelativeDate,
    onCellClicked: undefined,
    valueSetter: (params: ValueSetterParams): boolean => {
        if (!isNil(params.newValue) && !isString(params.newValue)) {
            return false;
        }
        return defaultValueSetter(params);
    },
    ...getFilterTypeFromGraphqlType(props),
});

export const getProgressFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedProgressProperties>,
): ColumnProps<NestedLinkProperties> => {
    return {
        editable: false,
        cellRenderer: FieldKey.Progress,
        cellRendererParams: getCellProps(props),
        valueSetter: (params: ValueSetterParams): boolean => {
            if (!isNumber(params.newValue) && !isNil(params.newValue)) {
                return false;
            }
            return defaultValueSetter(params);
        },
        ...getFilterTypeFromGraphqlType({
            ...props,
            // If the table is transient we just handle the progress type as a decimal
            ...(props.tableProperties?.isTransient && !props.propertyGraphType?.type
                ? { propertyGraphType: getFilterableScalarPropertyType(GraphQLTypes.Decimal) }
                : {}),
        }),
    };
};

const applyNumericFilterOptions = (properties: ColDef, hasFloatingFilters = false): void => {
    properties.suppressHeaderMenuButton = false;
    properties.filter = 'agNumberColumnFilter';
    properties.menuTabs = hasFloatingFilters ? ['generalMenuTab'] : ['filterMenuTab', 'generalMenuTab'];
    properties.floatingFilterComponent = NumericFloatingFilter;
    properties.filter = NumericFilter;
    properties.filterParams = {
        ...properties.filterParams,
        defaultJoinOperator: 'OR',
        filterOptions: numberFilterOptions,
    };
};

const applyBooleanFilterOptions = (
    properties: ColDef,
    props: ColumnConfigurationProperties<NestedCheckboxProperties | NestedSwitchProperties>,
): void => {
    properties.suppressHeaderMenuButton = false;
    properties.filter = BooleanFilter;
    properties.filterParams = {
        ...properties.filterParams,
        defaultJoinOperator: 'OR',
        ...{
            values: [true, false],
            controlObjectType: props.columnProperties._controlObjectType,
        },
    };
    properties.suppressFiltersToolPanel = true;
};

const applyDateFilterOptions = ({
    properties,
    hasFloatingFilters = false,
    locale,
}: {
    properties: ColDef;
    hasFloatingFilters?: boolean;
    locale: LocalizeLocale;
}): void => {
    properties.suppressHeaderMenuButton = false;

    properties.filter = DateFilter;

    // We need to set a special floating filter to render the current filter in the header
    if (hasFloatingFilters) {
        properties.floatingFilterComponent = DateFloatingFilter;
    }
    properties.menuTabs = hasFloatingFilters ? ['generalMenuTab'] : ['filterMenuTab', 'generalMenuTab'];
    properties.floatingFilterComponentParams = {
        ...properties.floatingFilterComponentParams,
        locale,
    };
    properties.filterParams = {
        ...properties.filterParams,
        locale,
        defaultJoinOperator: 'OR',
    };
};

const applyDatetimeFilterOptions = ({
    properties,
    hasFloatingFilters = false,
    locale,
}: {
    properties: ColDef;
    hasFloatingFilters?: boolean;
    locale: LocalizeLocale;
}): void => {
    properties.suppressHeaderMenuButton = false;

    properties.filter = DatetimeFilter;

    // We need to set a special floating filter to render the current filter in the header
    if (hasFloatingFilters) {
        properties.floatingFilterComponent = DateFloatingFilter;
    }
    properties.menuTabs = hasFloatingFilters ? ['generalMenuTab'] : ['filterMenuTab', 'generalMenuTab'];
    properties.floatingFilterComponentParams = {
        ...properties.floatingFilterComponentParams,
        locale,
    };
    properties.filterParams = {
        ...properties.filterParams,
        locale,
        defaultJoinOperator: 'OR',
    };
};

const applyReferenceFilterOptions = (
    properties: ColDef,
    props: ColumnConfigurationProperties<NestedProperties>,
): void => {
    properties.suppressHeaderMenuButton = false;

    // Custom filter component is used here to provide a scrollable, paginated selection experience
    properties.filter = ReferenceFilter;

    // We need to set a special floating filter to render the current filter in the header
    if (props.hasFloatingFilters) {
        properties.floatingFilterComponent = ReferenceFloatingFilter;
    }

    properties.menuTabs = props.hasFloatingFilters ? ['generalMenuTab'] : ['filterMenuTab', 'generalMenuTab'];
    properties.filterParams = {
        ...properties.filterParams,
        defaultJoinOperator: 'OR',
        filterOptions: props.columnProperties,
    };
};

export const getNumericFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedNumericProperties>,
): ColumnProps<NestedNumericProperties> => {
    const cellProps = getCellProps(props);

    // If the table is transient we just handle the number type as a decimal
    if (props.tableProperties?.isTransient && !props.propertyGraphType?.type) {
        props.propertyGraphType = getFilterableScalarPropertyType(GraphQLTypes.Decimal);
    }

    return {
        type: 'rightAligned',
        cellRenderer: DefaultCellRenderer,
        cellRendererParams: cellProps,
        cellEditor: FieldKey.Numeric,
        cellEditorParams: cellProps,
        valueFormatter: getNumericValueFormatter(props.screenId, props.columnProperties, props.locale),
        valueSetter: (params: ValueSetterParams): boolean => {
            return defaultValueSetter(params, stringToNumber);
        },
        ...getFilterTypeFromGraphqlType({
            ...props,
            // If the table is transient we just handle the numeric field type as a decimal
            ...(props.tableProperties?.isTransient && !props.propertyGraphType?.type
                ? { propertyGraphType: getFilterableScalarPropertyType(GraphQLTypes.Decimal) }
                : {}),
        }),
    };
};

export const getAggregateFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedNumericProperties>,
): ColumnProps<NestedNumericProperties> => {
    const cellProps = getCellProps(props);
    const properties: ColumnProps<NestedNumericProperties> = {
        type: 'rightAligned',
        cellRenderer: DefaultCellRenderer,
        cellRendererParams: cellProps,
        valueFormatter: getNumericValueFormatter(props.screenId, props.columnProperties, props.locale),
        filter: false,
        sortable: false,
        editable: false,
        valueSetter: (params: ValueSetterParams): boolean => {
            return defaultValueSetter(params, stringToNumber);
        },
    };

    return properties;
};

export const getFilterSelectFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedFilterSelectProperties>,
): ColumnProps<NestedFilterSelectProperties> => {
    const cellProps = getCellProps(props);

    // If the table is transient we just handle the filter select type as a string
    if (props.tableProperties?.isTransient && !props.propertyGraphType?.type) {
        props.propertyGraphType = getFilterableScalarPropertyType(GraphQLTypes.String);
    }

    return {
        cellEditorParams: cellProps,
        cellEditor: FieldKey.FilterSelect,
        cellRenderer: FilterSelectRenderer,
        cellRendererParams: cellProps,
        suppressKeyboardEvent: suppressKeyboardEventForReferenceCellEditor,
        valueGetter: (params: ValueGetterParams): string | null =>
            defaultValueGetter(
                params,
                props.screenId,
                props.columnProperties,
            )((value: any): string | null => {
                // Filter out functional developer introduced faulty values.
                if (isObject(value) || isArray(value)) {
                    xtremConsole.warn(`Invalid type set to table text field in ${params.colDef.field}`, value);
                    return null;
                }
                return value;
            }),
        cellEditorPopup: true,
        valueSetter: (params: ValueSetterParams): boolean => {
            if (!isNil(params.newValue) && !isString(params.newValue)) {
                return false;
            }
            return defaultValueSetter(params);
        },
        ...getFilterTypeFromGraphqlType(props),
    };
};

export const getDropdownListFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedDropdownListProperties>,
): ColumnProps<NestedDropdownListProperties> => {
    const { columnProperties, enumTypes } = props;
    const enumOptions = columnProperties.optionType
        ? enumTypes[schemaTypeNameFromNodeName(columnProperties.optionType)]
        : undefined;
    const localizedOptions = enumOptions
        ? enumOptions.reduce((value: Dict<string>, key: string) => {
              value[key] = localizeEnumMember(columnProperties.optionType!, key);
              return value;
          }, {} as Dict<string>)
        : undefined;
    const cellProps = { ...getCellProps(props), localizedOptions, enumOptions };

    // If the table is transient we just handle the drop down list type as a string
    if (props.tableProperties?.isTransient && !props.propertyGraphType?.type) {
        props.propertyGraphType = getFilterableScalarPropertyType(GraphQLTypes.String);
    }

    return {
        cellEditor: FieldKey.DropdownList,
        cellEditorParams: cellProps,
        cellRenderer: DropdownListCellRenderer,
        cellRendererParams: cellProps,
        suppressKeyboardEvent: suppressKeyboardEventForReferenceCellEditor,
        cellEditorPopup: true,
        valueSetter: (params: ValueSetterParams): boolean => {
            if (localizedOptions && !localizedOptions[params.newValue]) {
                return false;
            }
            return defaultValueSetter(params);
        },
        ...getFilterTypeFromGraphqlType(props),
    } as ColumnProps<NestedDropdownListProperties>;
};

export const getSelectFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedSelectProperties>,
): ColumnProps<NestedSelectProperties> => {
    const { columnProperties, enumTypes } = props;
    const enumType = columnProperties.optionType || props.propertyGraphType?.enumType;
    const enumOptions = enumType ? enumTypes[schemaTypeNameFromNodeName(enumType)] : undefined;
    const localizedOptions =
        enumOptions && enumType
            ? enumOptions.reduce((value: Dict<string>, key: string) => {
                  value[key] = localizeEnumMember(enumType, key);
                  return value;
              }, {} as Dict<string>)
            : undefined;
    const cellProps = { ...getCellProps(props), localizedOptions, enumOptions };

    // If the table is transient we just handle the select type as a string
    if (props.tableProperties?.isTransient && !props.propertyGraphType?.type) {
        props.propertyGraphType = getFilterableScalarPropertyType(GraphQLTypes.String);
    }

    return {
        cellEditor: FieldKey.Select,
        cellEditorParams: cellProps,
        cellRenderer: SelectRenderer,
        cellRendererParams: cellProps,
        valueSetter: (params: ValueSetterParams): boolean => {
            if (localizedOptions && !localizedOptions[params.newValue]) {
                return false;
            }
            return defaultValueSetter(params);
        },
        suppressKeyboardEvent: suppressKeyboardEventForReferenceCellEditor,
        cellEditorPopup: true,
        ...getFilterTypeFromGraphqlType(props),
    };
};

export const getSwitchFieldColumnConfiguration = (
    props: ColumnConfigurationProperties<NestedSwitchProperties>,
): ColumnProps<NestedSwitchProperties> => {
    // If the table is transient we just handle the switch type as a boolean
    if (props.tableProperties?.isTransient && !props.propertyGraphType?.type) {
        props.propertyGraphType = getFilterableScalarPropertyType(GraphQLTypes.Boolean);
    }

    const cellParams = (params: any): SwitchCellRendererProps => {
        const onChange = (value: boolean): void => {
            if (typeof params?.node?.setDataValue === 'function') {
                params.node.setDataValue(props.columnId, value);
            }
        };

        return { ...getCellProps(props), onChange };
    };

    return {
        editable: true,
        cellRenderer: SwitchRenderer,
        cellEditor: SwitchRenderer,
        minWidth: 80,
        cellRendererParams: cellParams,
        cellEditorParams: (params): SwitchCellRendererProps => {
            return { ...cellParams(params), isEditing: true };
        },
        valueSetter: (params: ValueSetterParams): boolean => {
            return defaultValueSetter(params, stringToBoolean);
        },
        ...getFilterTypeFromGraphqlType(props),
    };
};

export const COLUMN_ID_LINE_NUMBER = '__line_number';

export const getLineNumberColumnConfiguration = (screenId: string): AgGridColumnConfigWithScreenIdAndColDef => ({
    pinned: 'left',
    checkboxSelection: false,
    field: COLUMN_ID_LINE_NUMBER,
    suppressColumnsToolPanel: true,
    suppressMovable: true,
    headerName: '',
    editable: false,
    sortable: false,
    filter: false,
    suppressHeaderMenuButton: true,
    cellClass: 'e-table-field-line-number',
    minWidth: 44,
    maxWidth: 44,
    width: 44,
    context: {
        columnId: COLUMN_ID_LINE_NUMBER,
        screenId,
        isEditable: () => false,
    },
    suppressSizeToFit: true,
    suppressAutoSize: false,
    valueGetter: (params: ValueGetterParams): string => {
        if (params.node?.rowPinned) {
            return '';
        }
        return String((params.node?.rowIndex || 0) + 1);
    },
});

export const COLUMN_ID_VALIDATIONS = '__validation';

export const getValidationColumnConfiguration = (
    screenId: string,
    getColumns: () => NestedField<any, any>[],
): AgGridColumnConfigWithAllColumns => ({
    cellRenderer: ValidationCellRenderer.name,
    pinned: 'left',
    editable: false,
    field: COLUMN_ID_VALIDATIONS,
    suppressColumnsToolPanel: true,
    filter: false,
    headerName: '',
    initialHide: true,
    hide: true,
    maxWidth: 25,
    minWidth: 25,
    context: {
        columnId: COLUMN_ID_VALIDATIONS,
        screenId,
        isEditable: (): boolean => false,
        getColumns,
    },
    sortable: false,
    suppressAutoSize: true,
    suppressHeaderMenuButton: true,
    suppressSizeToFit: true,
    headerClass: 'e-table-field-validation-summary-header',
    cellClass: 'e-table-field-validation-summary',
    width: 25,
});

export const COLUMN_ID_AUTO_COLUMN = 'ag-Grid-AutoColumn';
export const COLUMN_ID_ROW_ACTIONS = '__actions';
export const COLUMN_CELLCLASS_ROW_ACTIONS = 'e-table-field-dropdown-actions-cell';
export const FOCUSED_BUT_BORDERLESS = 'e-ag-cell-focus--no-border';
export const getDropdownActionsColumnConfiguration = (
    screenId: string,
    tableElementId: string,
    level: number,
    wrapper: WithNestedWrapper<any>['wrapper'],
    isParentFieldDisabled: boolean,
    inlineActions: GridRowActionType,
    dropdownActions: GridRowActionOrMenuSeparatorType,
    hasFloatingFilters: boolean,
    collectionValue: () => CollectionValue | undefined,
): AgGridColumnConfigWithScreenIdAndColDef => {
    const cellParams = {
        elementId: tableElementId,
        screenId,
        level,
        wrapper,
        isParentFieldDisabled,
        inlineActions,
        dropdownActions,
        collectionValue,
    };
    return {
        pinned: 'right',
        field: COLUMN_ID_ROW_ACTIONS,
        suppressColumnsToolPanel: true,
        cellRenderer: TableDropdownActionsCell.name,
        cellEditor: TableDropdownActionsCell.name,
        editable: true,
        sortable: false,
        cellEditorPopup: true,
        suppressHeaderMenuButton: true,
        singleClickEdit: false,
        cellClass: COLUMN_CELLCLASS_ROW_ACTIONS,
        headerClass: 'e-table-field-dropdown-actions-header',
        minWidth: Math.max(44, 44 * inlineActions.length + 44 * Math.sign(dropdownActions.length)),
        maxWidth: Math.max(44, 44 * inlineActions.length + 44 * Math.sign(dropdownActions.length)),
        width: Math.max(44, 44 * inlineActions.length + 44 * Math.sign(dropdownActions.length)),
        context: {
            columnId: COLUMN_ID_ROW_ACTIONS,
            screenId,
            isEditable: (): boolean => false,
        },
        suppressSizeToFit: true,
        suppressAutoSize: true,
        suppressKeyboardEvent: suppressKeyboardEventForActionCellEditor,
        cellRendererParams: cellParams,
        cellEditorParams: {
            ...cellParams,
            isEditing: true,
        },
        headerName: '',
        filter: hasFloatingFilters,
        suppressHeaderFilterButton: true,
        suppressFloatingFilterButton: true,
        floatingFilter: hasFloatingFilters,
        floatingFilterComponent: hasFloatingFilters ? ClearFloatingFilter : null,
        floatingFilterComponentParams: { elementId: tableElementId },
    };
};

export const frameworkComponents: Dict<any> = {
    [FieldKey.Checkbox]: CheckboxCellRenderer,
    [FieldKey.Count]: CountCellRenderer,
    [FieldKey.Date]: DateCellEditor,
    [FieldKey.Datetime]: DatetimeCellEditor,
    [FieldKey.DatetimeRange]: DatetimeRangeCellEditor,
    [FieldKey.DropdownList]: DropdownListCellEditor,
    [FieldKey.FilterSelect]: FilterSelectCellEditor,
    [FieldKey.Icon]: IconCellRenderer,
    [FieldKey.Image]: ImageCellRenderer,
    [FieldKey.Label]: LabelCellRenderer,
    [FieldKey.Link]: LinkCellRenderer,
    [FieldKey.Numeric]: NumericEditor,
    [FieldKey.Progress]: ProgressCellRenderer,
    [FieldKey.Reference]: ReferenceCellEditor,
    [FieldKey.RelativeDate]: RelativeDateCellRenderer,
    [FieldKey.Select]: SelectCellEditor,
    [ValidationCellRenderer.name]: ValidationCellRenderer,
    [TableDropdownActionsCell.name]: TableDropdownActionsCell,
    DefaultCellEditor,
    DefaultCellRenderer,
    DropdownListCellRenderer,
    FilterSelectRenderer,
    Loader,
    ReferenceRenderer,
    SelectRenderer,
    SwitchRenderer,
    CheckboxCell,
    DesktopTableEmptyComponent,
    DesktopNestedGridEmptyComponent,
};

export type WithNestedWrapper<T> = T & {
    wrapper: React.FC<CellWrapperProps>;
};

export function getCellErrorMessage(props: CellParams<any, any>): string | undefined {
    const columnBind = props.colDef.field;
    return Object.prototype.hasOwnProperty.call(props.data?.__validationState || {}, columnBind)
        ? props.data?.__validationState[columnBind].message
        : undefined;
}

export function getCellWarningMessage({ data, fieldProperties, screenId }: CellParams<any, any>): string | undefined {
    return resolveByValue<string>({
        fieldValue: data._id,
        propertyValue: fieldProperties.warningMessage,
        rowValue: data,
        screenId,
        skipHexFormat: true,
    });
}
export function getCellInfoMessage({ data, fieldProperties, screenId }: CellParams<any, any>): string | undefined {
    return resolveByValue<string>({
        fieldValue: data._id,
        propertyValue: fieldProperties.infoMessage,
        rowValue: data,
        screenId,
        skipHexFormat: true,
    });
}

export function getTextAlignment(props: CellParams<any>): 'left' | 'right' | 'center' {
    const type = props.colDef.type;
    if (type === 'rightAligned') {
        return 'right';
    }
    if (type === 'leftAligned') {
        return 'left';
    }
    if (type === 'centerAligned') {
        return 'center';
    }
    return 'left';
}

export function getCellProps<T extends Nested<any>>({
    collectionValue,
    columnId,
    columnProperties,
    isParentFieldDisabled,
    isReadOnly,
    level,
    locale,
    screenId,
    tableElementId,
    tableProperties,
}: ColumnConfigurationProperties<T>): CellProps<T> {
    const levels = (tableProperties as InternalNestedGridProperties<ScreenBase>).levels;
    const contextNode = levels ? levels[level].node : tableProperties.node;
    const isTableReadOnly = isReadOnly;
    return {
        collectionValue,
        columnId,
        contextNode,
        elementId: columnId,
        fieldProperties: { ...columnProperties, wrapper: CellWrapper },
        isParentFieldDisabled,
        isTableReadOnly,
        isTree: tableProperties._controlObjectType === FieldKey.Tree,
        locale,
        screenId,
        tableElementId,
    };
}

export interface AddStaticColumnsArgs {
    screenId: string;
    fieldProperties: Omit<InternalTableProperties, 'selectedRecords'>;
    columns?: AgGridColumnConfigWithScreenIdAndColDef[];
    tableElementId: string;
    level: number;
    isParentFieldDisabled: boolean;
    hasFloatingFilters: boolean;
    accessBindings: AccessBindings;
    collectionValue: () => CollectionValue | undefined;
}

export const addStaticColumns = ({
    accessBindings,
    columns = [],
    fieldProperties,
    hasFloatingFilters,
    isParentFieldDisabled,
    level,
    screenId,
    tableElementId,
    collectionValue,
}: AddStaticColumnsArgs): void => {
    columns.unshift(getValidationColumnConfiguration(screenId, () => fieldProperties.columns || []));

    if (fieldProperties.hasLineNumbers) {
        columns.unshift(getLineNumberColumnConfiguration(screenId));
    }

    const inlineActions = (fieldProperties.inlineActions ?? []).filter(a => {
        const accessRule = getElementAccessStatusWithoutId(accessBindings || {}, a.access);
        return accessRule === 'authorized';
    });
    const dropdownActions = (fieldProperties.dropdownActions ?? []).filter(a => {
        if (a.isMenuSeparator) {
            return true;
        }
        const accessRule = getElementAccessStatusWithoutId(accessBindings || {}, a.access);
        return accessRule === 'authorized';
    });
    if (
        inlineActions.length > 0 ||
        dropdownActions.length > 0 ||
        fieldProperties.canAddNewLine === true ||
        hasFloatingFilters
    ) {
        columns.push(
            getDropdownActionsColumnConfiguration(
                screenId,
                tableElementId,
                level,
                CellWrapper,
                isParentFieldDisabled,
                inlineActions,
                dropdownActions,
                hasFloatingFilters,
                collectionValue,
            ),
        );
    }
};

export const COLUMN_ID_ROW_SELECTION = 'ag-Grid-ControlsColumn';

export const INTERNAL_COLUMN_IDS = [
    COLUMN_ID_ROW_SELECTION,
    COLUMN_ID_LINE_NUMBER,
    COLUMN_ID_VALIDATIONS,
    COLUMN_ID_AUTO_COLUMN,
    COLUMN_ID_ROW_ACTIONS,
];
