import type {
    ColDef,
    ColumnState,
    DateFilterModel,
    EditableCallbackParams,
    GridApi,
    ISimpleFilterModel,
    NumberFilterModel,
    SetFilterModel,
    TextFilterModel,
} from '@ag-grid-community/core';
import { FieldKey, objectKeys, type Dict, type FilterTypeValue } from '@sage/xtrem-shared';
import type { EnumType } from 'json-to-graphql-query';
import { isArray, isFunction } from 'lodash';
import type { PickProperties } from 'ts-essentials';
import { fetchReferenceItems } from '../../component/field/reference/reference-utils';
import type { NestedTextProperties } from '../../component/nested-fields-properties';
import { RANGE, RANGE_DIVIDER, SET } from '../../component/types';
import type { CollectionValue } from '../../service/collection-data-service';
import type { Filter } from '../../service/filter-service';
import { localize } from '../../service/i18n-service';
import type { ValidationResult } from '../../service/screen-base-definition';
import { triggerFieldEvent } from '../events';
import { resolveByValue } from '../resolve-value-utils';
import { AUTO_COLUMN_ID, getGraphFilter, removeNumericPostfix, setTableContext } from '../table-component-utils';
import { cleanMetadataFromRecord, splitValueToMergedValue } from '../transformers';
import { COLUMN_ID_LINE_NUMBER, COLUMN_ID_ROW_SELECTION } from './ag-grid-column-config';
import { shouldDisplayColumnInPanel } from './ag-grid-service';
import type {
    AgGridColumnConfigWithScreenIdAndColDef,
    ColumnPanelColumnState,
    FilterValue,
} from './ag-grid-utility-types';

import type { OptionsMenuItem, TableProperties } from '../../component/control-objects';
import { type TableDecoratorProperties } from '../../component/field/table/table-component-types';
import { getGroupFilterValue, type AggFunc } from '../../service/collection-data-utils';
import { fetchCollectionDataCount } from '../../service/graphql-service';
import type { NodePropertyType } from '../../types';

export type FilterModel = DateFilterModel | NumberFilterModel | TextFilterModel | SetFilterModel;

export const ROW_HEIGHT = 40;
export const GROUP_ROW_HEIGHT = ROW_HEIGHT * 1.25;

export const getFilterValueFromAgGridFilterStatement = (
    filterBase: FilterModel & {
        filterById?: boolean;
    },
    filterKey: string,
): FilterValue => {
    const filterType = filterBase.filterType;
    const type = (filterBase as ISimpleFilterModel).type as FilterTypeValue;
    const lookup1 =
        filterType === 'date'
            ? (filterBase as DateFilterModel).dateFrom
            : (filterBase as NumberFilterModel | TextFilterModel).filter;
    const lookup2 =
        filterType === 'date'
            ? (filterBase as DateFilterModel).dateTo
            : filterType === 'number'
              ? (filterBase as NumberFilterModel).filterTo
              : filterType === 'text'
                ? (filterBase as TextFilterModel).filterTo
                : '';

    const filterValue = type === RANGE ? `${lookup1}${RANGE_DIVIDER}${lookup2}` : lookup1;

    if (filterType === SET) {
        return {
            filterValue: (filterBase as SetFilterModel).values,
            filterType: {
                text: `${filterKey} in list`,
                value: SET,
            },
            filterById: filterBase.filterById,
        };
    }
    return {
        filterValue,
        filterType: { text: `${filterKey} ${type} ${filterValue}`, value: type },
    };
};

export function callGridMethod<M extends keyof PickProperties<GridApi, (...args: any[]) => any>>(
    gridApi: GridApi | undefined | null,
    method: M,
    ...args: ArgsType<GridApi[M]>
): ReturnType<GridApi[M]> | undefined {
    if (!gridApi || gridApi?.isDestroyed()) {
        return undefined;
    }
    return (gridApi[method] as any)?.(...args) as ReturnType<GridApi[M]>;
}

export function getSafeGridApiContext<M extends keyof PickProperties<GridApi, (...args: any[]) => any>, R>(
    cb: (arg: ReturnType<GridApi[M]> extends void ? undefined : ReturnType<GridApi[M]>) => R,
    gridApi: GridApi | undefined | null,
    method: M,
    ...args: ArgsType<GridApi[M]>
): R | undefined {
    if (!gridApi || gridApi?.isDestroyed()) {
        return undefined;
    }
    const result = (gridApi[method] as any)?.(...args) as ReturnType<GridApi[M]>;

    return cb(result);
}

export type FilterTypes = string | number | boolean | EnumType;

type ArgsType<T> = T extends (...args: infer A) => any ? A : never;

export const mapAgGridFilterToXtremFilters =
    (filterModel: Dict<any>) =>
    (filterKey: any): Filter<FilterTypes> => {
        const filterBase = filterModel[filterKey];
        const operator = filterBase.operator;
        const formattedFilterKey = removeNumericPostfix(filterKey);
        if (operator && isArray(filterBase.conditions)) {
            // Combined Filter
            return {
                // When the table value is reset the column names are getting an _{NUMBER} postfix, we remove that
                id: formattedFilterKey,
                value: filterBase.conditions.map(
                    (c: any): FilterValue => getFilterValueFromAgGridFilterStatement(c, filterKey),
                ),
            };
        }

        // Single Filter
        return {
            // When the table value is reset the column names are getting an _{NUMBER} postfix, we remove that
            id: formattedFilterKey,
            value: [getFilterValueFromAgGridFilterStatement(filterBase, filterKey)],
        };
    };

export const tryToCommitPhantomRow = async ({
    api,
    screenId,
    value,
    elementId,
}: {
    api: GridApi;
    screenId: string;
    value: CollectionValue;
    elementId: string;
}): Promise<Dict<ValidationResult> | null> => {
    const phantomRow = api.getPinnedTopRow(0);
    if (!phantomRow) {
        return null;
    }
    const dirtyColumns = new Set(api.getColumnDefs()?.map(c => (c as ColDef).field!));

    value.setRecordValue({
        recordData: { _id: phantomRow.data._id },
        level: phantomRow.data.__level,
        toBeMarkedAsDirty: Array.from(dirtyColumns),
        isOrganicChange: true,
    });

    const recordData = value.getRawRecord({
        id: phantomRow.data._id,
        level: phantomRow.data.__level,
        cleanMetadata: false,
    });

    const validationResult = await value.runValidationOnRecord({ recordData });

    if (objectKeys(validationResult || {}).length === 0) {
        api.flashCells({ rowNodes: [phantomRow] });
        const { __level: level, __parentId: parentId } = recordData;
        value.commitPhantomRow({
            id: recordData._id,
            level,
            parentId,
        });
        setTimeout(() => {
            value.createNewPhantomRow({ level, parentId });
            triggerFieldEvent(
                screenId,
                elementId,
                'onRowAdded',
                recordData._id,
                splitValueToMergedValue(cleanMetadataFromRecord(recordData)),
            );
        }, 500);
        return null;
    }
    return validationResult;
};

export const getFirstEditableColumn = (api: GridApi, data: any = {}, includeSelect = false): string | undefined => {
    return (api.getColumnDefs() ?? [])
        .filter(
            (colDef: ColDef) =>
                !colDef.hide &&
                ((includeSelect && colDef.colId === COLUMN_ID_ROW_SELECTION) ||
                    (colDef.colId !== COLUMN_ID_ROW_SELECTION &&
                        !!colDef.cellEditor &&
                        (colDef.editable === true ||
                            (isFunction(colDef.editable) &&
                                colDef.editable({
                                    data,
                                    api,
                                    colDef,
                                } as EditableCallbackParams))))),
        )
        .map(c => (c as ColDef).colId)[0];
};

export const getFilterModel = (api?: GridApi | null, groupByColumnField?: string): any | undefined => {
    const filterModel = api?.getFilterModel();
    return filterModel == null
        ? undefined
        : objectKeys(filterModel).reduce((acc, curr) => {
              const key = curr === AUTO_COLUMN_ID ? groupByColumnField : curr;
              if (key != null) {
                  acc[key] = filterModel[curr];
              }
              return acc;
          }, {} as any);
};

export function getSelectionFilter({
    groupByColumn,
    gridApi,
    screenId,
    isSelectAllChecked,
    tableFieldProperties,
    activeOptionsMenuItem,
    mode = 'server',
}: {
    groupByColumn?: AgGridColumnConfigWithScreenIdAndColDef;
    gridApi: GridApi;
    screenId: string;
    isSelectAllChecked: boolean;
    tableFieldProperties: TableProperties;
    activeOptionsMenuItem?: OptionsMenuItem;
    mode?: 'server' | 'client';
}): any {
    const groupFilter: any[] = [];
    if (groupByColumn) {
        gridApi.forEachNode(node => {
            if (node.group && node.data && node.isSelected()) {
                groupFilter.push(
                    getGroupFilterValue({
                        group: {
                            key: node.data.__groupKey,
                            value: node.data._id.split('__group-')[1],
                            aggFunc: (groupByColumn.aggFunc as AggFunc | null | undefined) ?? undefined,
                            type: groupByColumn.type as FieldKey,
                        },
                        mode,
                    }),
                );
            }
        });
    }
    return {
        ...(isSelectAllChecked &&
            getGraphFilter({
                filterModel: getFilterModel(gridApi, groupByColumn?.field),
                screenId,
                tableFieldProperties,
                activeOptionsMenuItem,
            })),
        _id:
            isSelectAllChecked || groupFilter.length > 0
                ? {
                      _nin: getAllUnselectedIds(gridApi),
                  }
                : {
                      _in: gridApi
                          .getSelectedNodes()
                          .filter(r => !r.group)
                          .map(row => row.data._id as string),
                  },
        ...(groupFilter.length > 0 && { _or: groupFilter }),
    };
}

export const getAllUnselectedIds = (gridApi: GridApi): string[] => {
    const unselected: string[] = [];
    gridApi.forEachNode(node => {
        if (node.data && !node.isSelected() && !node.data.__isGroup) {
            unselected.push(node.data._id);
        }
    });
    return unselected;
};

export const getColumnStatesForColumnPanel = (
    screenId: string,
    columnDefinitions?: AgGridColumnConfigWithScreenIdAndColDef[],
    columnStates?: ColumnState[],
): ColumnPanelColumnState[] => {
    if (!columnDefinitions) {
        return [];
    }

    const columns: ColumnPanelColumnState[] = [];

    if (columnDefinitions[0]?.context.columnId === COLUMN_ID_LINE_NUMBER) {
        const columnState = (columnStates || []).find(
            state => COLUMN_ID_LINE_NUMBER && state.colId && state.colId === COLUMN_ID_LINE_NUMBER,
        );

        columns.push({
            colId: COLUMN_ID_LINE_NUMBER,
            isHidden: columnState?.hide === true,
            isMandatory: false,
            isSpecialColumn: true,
            title: localize('@sage/xtrem-ui/table-line-number', 'Line number'),
        });
    }

    const columnsToDisplayInPanel = [
        ...columnDefinitions.filter(c => !!c.colId && shouldDisplayColumnInPanel(c.context.columnDefinition)),
    ];

    columns.push(
        ...columnsToDisplayInPanel
            .sort((a, b) => {
                if (columnStates) {
                    const aColumnStateIndex = columnStates.findIndex(acs => acs.colId === a.colId);
                    const bColumnStateIndex = columnStates.findIndex(bcs => bcs.colId === b.colId);
                    return aColumnStateIndex - bColumnStateIndex;
                }
                const aColumnDefinitionIndex = columnDefinitions.findIndex(acs => acs.colId === a.colId);
                const bColumnDefinitionIndex = columnDefinitions.findIndex(bcs => bcs.colId === b.colId);

                return aColumnDefinitionIndex - bColumnDefinitionIndex;
            })
            .map<ColumnPanelColumnState>(c => {
                const columnState = (columnStates || []).find(
                    state => c.colId && state.colId && state.colId === c.colId,
                );
                const properties = c.context.columnDefinition?.properties as NestedTextProperties;
                const title = resolveByValue({
                    propertyValue: properties.title,
                    rowValue: null,
                    fieldValue: null,
                    skipHexFormat: true,
                    screenId,
                });
                return {
                    colId: String(c.colId),
                    title,
                    isMandatory: !!properties.isMandatory || c.cellRenderer === 'agGroupCellRenderer',
                    isHidden: columnState ? columnState.hide === true : !!properties.isHiddenOnMainField,
                };
            }),
    );

    return columns;
};

export async function pasteToSelectedCell(api: GridApi): Promise<void> {
    if (!api) {
        return;
    }
    const focusedCell = api.getFocusedCell();
    if (!focusedCell) {
        return;
    }
    const colDef = focusedCell.column?.getColDef();
    const editable = colDef?.editable;
    if (typeof editable === 'boolean' && !editable) {
        return;
    }
    const isKeyboardWriteEnabled = await navigator.permissions.query({ name: 'clipboard-write' as PermissionName });
    if (isKeyboardWriteEnabled.state !== 'granted') {
        return;
    }
    const text = await navigator.clipboard.readText();
    const rowNode = api.getDisplayedRowAtIndex(focusedCell.rowIndex);
    if (!rowNode) {
        return;
    }
    if (typeof editable === 'function' && !editable(rowNode.data)) {
        return;
    }
    const userColDef = focusedCell.column.getUserProvidedColDef();
    if (userColDef?.type === FieldKey.Reference) {
        const items = await fetchReferenceItems(text, userColDef as AgGridColumnConfigWithScreenIdAndColDef);
        if (items.length === 1) {
            rowNode.setDataValue(focusedCell.column.getColId(), items[0].__collectionItem);
        }
        return;
    }
    rowNode.setDataValue(focusedCell.column.getColId(), text);
}

export async function copySelectedCellValue(cellValue: string): Promise<void> {
    const isKeyboardWriteEnabled = await navigator.permissions.query({ name: 'clipboard-write' as PermissionName });
    if (isKeyboardWriteEnabled.state !== 'granted') {
        return;
    }
    await navigator.clipboard.writeText(cellValue);
}

export const getTotalRecordCount = async ({
    api,
    fieldProperties,
    activeOptionsMenuItem,
    screenId,
    node,
    groupByColumnField,
}: {
    api: GridApi;
    fieldProperties: TableDecoratorProperties;
    activeOptionsMenuItem?: OptionsMenuItem;
    screenId: string;
    node: NodePropertyType;
    groupByColumnField?: string;
}): Promise<number> => {
    const filter = getGraphFilter({
        filterModel: getFilterModel(api, groupByColumnField),
        screenId,
        tableFieldProperties: fieldProperties,
        activeOptionsMenuItem,
    });
    return new Promise<number>(resolve => {
        if (fieldProperties._controlObjectType !== FieldKey.MultiReference) {
            fetchCollectionDataCount({ rootNode: node, filter: JSON.stringify(filter) }).then(totalRowCount => {
                resolve(totalRowCount);
            });
        } else {
            resolve(0);
        }
    }).then(totalRowCount => {
        setTableContext(api, c => {
            c.totalRowCount = totalRowCount;
        });
        return totalRowCount;
    });
};
