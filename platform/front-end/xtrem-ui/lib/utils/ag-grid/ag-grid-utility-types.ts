import type { ColDef } from '@ag-grid-community/core';
import type { FilterTypeValue } from '@sage/xtrem-shared';
import type { NestedField, NestedFieldTypes } from '../../component/nested-fields';
import type { ScreenBase } from '../../service/screen-base';

export interface AgGridColumnConfigWithScreenIdAndColDef<T extends any = {}> extends ColDef {
    context: {
        columnId: string;
        xtremSortIndex?: number;
        screenId: string;
        columnDefinition?: NestedField<ScreenBase, NestedFieldTypes>;
        isEditable: (data: any) => boolean;
    } & T;
    field?: string;
}

export interface AgGridColumnConfigWithAllColumns
    extends AgGridColumnConfigWithScreenIdAndColDef<{
        getColumns: () => NestedField<any, any>[];
    }> {}

export interface ColumnPanelColumnState {
    colId: string;
    isHidden: boolean;
    isMandatory: boolean;
    isSpecialColumn?: boolean;
    title: string;
}

export interface FilterValue {
    filterType: {
        text: string;
        value: FilterTypeValue;
    };
    filterValue: any;

    /** Indicates that the filter should be applied against the ID field, it is used with nested objects (e.g reference columns) */
    filterById?: boolean;
}
