import { GraphQLTypes, type LocalizeLocale } from '@sage/xtrem-shared';
import * as nestedFields from '../../../component/nested-fields';
import { FieldKey } from '../../../component/types';
import { CellWrapper } from '../../../component/ui/table-shared/cell/cell-wrapper';
import { CollectionValue } from '../../../service/collection-data-service';
import { CollectionFieldTypes } from '../../../service/collection-data-types';
import { GraphQLKind } from '../../../types';
import type { ColumnConfigurationProperties } from '../ag-grid-column-config';
import { getCellProps, getNumericFieldColumnConfiguration, getValueFormatter } from '../ag-grid-column-config';

describe('ag-grid-column-config tests', () => {
    const screenId = 'TestPageId';
    let collectionValue: () => CollectionValue;

    beforeEach(() => {
        collectionValue = () =>
            new CollectionValue({
                screenId,
                elementId: 'testField',
                isTransient: false,
                hasNextPage: false,
                orderBy: [{}],
                columnDefinitions: [
                    [nestedFields.text<any, any>({ bind: '_id' }), nestedFields.text<any, any>({ bind: 'anyField' })],
                ],
                nodeTypes: {},
                nodes: ['@sage/xtrem-test/AnyNode'],
                filter: [undefined],
                initialValues: [],
                fieldType: CollectionFieldTypes.DESKTOP_TABLE,
            });
    });

    describe('getValueFormatter', () => {
        it('should return the value as is if no pre or postfix applied', () => {
            expect(getValueFormatter(screenId, { bind: 'anyField' })({ value: 'the value' } as any)).toEqual(
                'the value',
            );
        });

        it('should return the value with postfix applied', () => {
            expect(
                getValueFormatter(screenId, { bind: 'anyField', postfix: '%' })({ value: 'the value' } as any),
            ).toEqual('the value %');
        });

        it('should return the value with prefix applied', () => {
            expect(
                getValueFormatter(screenId, { bind: 'anyField', prefix: '%' })({ value: 'the value' } as any),
            ).toEqual('% the value');
        });

        it('should return the value with prefix and postfix applied', () => {
            expect(
                getValueFormatter(screenId, { bind: 'anyField', prefix: '%', postfix: '£' })({
                    value: 'the value',
                } as any),
            ).toEqual('% the value £');
        });
    });

    describe('getNumericFieldColumnConfiguration', () => {
        const screenId = 'TestScreenId';
        const tableElementId = 'numeric-test';
        const columnId = 'testColumn';
        const partialExpectedConfig = {
            type: 'rightAligned',
            cellEditor: FieldKey.Numeric,
            cellEditorParams: {
                columnId: 'testColumn',
                contextNode: undefined,
                elementId: 'testColumn',
                fieldProperties: {
                    bind: 'testColumn',
                    wrapper: CellWrapper,
                },
                screenId,
                tableElementId,
                isParentFieldDisabled: undefined,
                isTableReadOnly: undefined,
                isTree: false,
            },
        };

        const getDefaultConfigurationProps = () =>
            ({
                tableElementId,
                screenId,
                columnProperties: { bind: columnId },
                columnId,
                enumTypes: {},
                nodeTypes: {},
                tableProperties: {},
            }) as ColumnConfigurationProperties<any>;

        it('should return numeric value with no pre or postfix applied', () => {
            const props = getNumericFieldColumnConfiguration(getDefaultConfigurationProps());
            expect(props.type).toBe(partialExpectedConfig.type);
            expect(props.cellEditor).toBe(partialExpectedConfig.cellEditor);
            expect(props.cellEditorParams).toEqual(partialExpectedConfig.cellEditorParams);
            const numericValue = (props.valueFormatter as (params: any) => string)({ value: '1' });
            expect(numericValue).toEqual('1');
        });

        it('should return numeric value with no pre or postfix applied', () => {
            const props = getNumericFieldColumnConfiguration(getDefaultConfigurationProps());
            const numericValue = (props.valueFormatter as (params: any) => string)({ value: 1 });
            expect(numericValue).toEqual('1');
        });

        it('should return numeric value with postfix applied', () => {
            const props = getNumericFieldColumnConfiguration({
                ...getDefaultConfigurationProps(),
                columnProperties: { bind: columnId, postfix: '%' },
            });
            const numericValue = (props.valueFormatter as (params: any) => string)({ value: 1 });
            expect(numericValue).toEqual('1 %');
        });

        it('should return numeric value with prefix applied', () => {
            const props = getNumericFieldColumnConfiguration({
                ...getDefaultConfigurationProps(),
                columnProperties: { bind: columnId, prefix: '%' },
            });
            const numericValue = (props.valueFormatter as (params: any) => string)({ value: 1 });
            expect(numericValue).toEqual('% 1');
        });

        it('should return numeric value with prefix and postfix applied', () => {
            const props = getNumericFieldColumnConfiguration({
                ...getDefaultConfigurationProps(),
                columnProperties: { bind: columnId, postfix: '%', prefix: '%' },
            });
            const numericValue = (props.valueFormatter as (params: any) => string)({ value: 1 });
            expect(numericValue).toEqual('% 1 %');
        });

        it('should return the correct filter type for custom fields', () => {
            const columnId = '_customData.testColumn';
            const mockMap = jest.fn(() => {});
            const columnProps = {
                ...getDefaultConfigurationProps(),
                columnId,
                columnProperties: { bind: columnId, options: [], map: mockMap },
                propertyGraphType: {
                    type: GraphQLTypes.Json,
                    name: '_customData',
                    kind: GraphQLKind.Scalar,
                    parentNode: 'SalesOrder',
                },
                nodeTypes: {
                    SalesOrder: {
                        name: 'SalesOrder',
                        title: 'Sales Order',
                        packageName: '@sage/xtrem-test',
                        mutations: {},
                        properties: {
                            testColumn: { type: 'string', kind: GraphQLKind.Scalar },
                        },
                    },
                },
                tableProperties: { isTransient: false },
            };

            const columnConfig = getNumericFieldColumnConfiguration(columnProps);

            expect(columnConfig.filter).toBe('agSetColumnFilter');
            expect(columnConfig.filterParams).toBeDefined();
            expect(columnConfig.filterParams?.values).toBeDefined();
        });
    });

    describe('getCellProps', () => {
        const tableElementId = 'numeric-test';
        const columnId = 'testColumn';
        const getDefaultConfigurationProps = () =>
            ({
                tableElementId,
                screenId,
                columnProperties: { bind: columnId },
                columnId,
                enumTypes: {},
                nodeTypes: {},
                tableProperties: {},
            }) as ColumnConfigurationProperties<any>;

        it('should return the cell props object', () => {
            expect(getCellProps(getDefaultConfigurationProps())).toMatchSnapshot();
        });

        it('should map the context node correctly for a table field', () => {
            expect(
                getCellProps({
                    tableElementId,
                    screenId,
                    columnProperties: { bind: columnId },
                    columnId,
                    level: 0,
                    isParentFieldDisabled: false,
                    enumTypes: {},
                    nodeTypes: {},
                    locale: 'en-US',
                    tableProperties: {
                        node: '@sage/my-test-node',
                    } as any,
                    hasFloatingFilters: false,
                    isReadOnly: false,
                    propertyGraphType: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    collectionValue,
                }).contextNode,
            ).toEqual('@sage/my-test-node');
        });

        it('should map the context node correctly for a nested grid field with levels', () => {
            const props = {
                tableElementId,
                screenId,
                columnProperties: { bind: columnId },
                columnId,
                level: 0,
                isParentFieldDisabled: false,
                enumTypes: {},
                nodeTypes: {},
                locale: 'en-US' as LocalizeLocale,
                propertyGraphType: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                tableProperties: {
                    levels: [
                        { node: '@sage/my-test-node' },
                        { node: '@sage/my-other-test-node' },
                        { node: '@sage/another-test-node' },
                    ],
                } as any,
            };
            expect(
                getCellProps({
                    ...props,
                    level: 0,
                    hasFloatingFilters: false,
                    isReadOnly: false,
                    collectionValue,
                }).contextNode,
            ).toEqual('@sage/my-test-node');
            expect(
                getCellProps({
                    ...props,
                    collectionValue,
                    level: 1,
                    hasFloatingFilters: false,
                    isReadOnly: false,
                }).contextNode,
            ).toEqual('@sage/my-other-test-node');
            expect(
                getCellProps({
                    ...props,
                    collectionValue,
                    level: 2,
                    hasFloatingFilters: false,
                    isReadOnly: false,
                }).contextNode,
            ).toEqual('@sage/another-test-node');
        });
    });
});
