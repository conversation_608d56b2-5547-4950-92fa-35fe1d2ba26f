import type { Column } from '@ag-grid-community/core';
import type { ScreenBase } from '../../../service/screen-base';
import { getMockState, getMockStore } from '../../../__tests__/test-helpers';
import type { NestedField, NestedFieldTypes } from '../../../component/nested-fields';
import { FieldKey } from '../../../component/types';
import { COLUMN_ID_ROW_ACTIONS, COLUMN_ID_ROW_SELECTION } from '../ag-grid-column-config';
import { cellExportFormatter, getColumnsToExport } from '../ag-grid-cell-editor-utils';

describe('ag-grid-editor-utils', () => {
    describe('getColumnsToExportExcel function', () => {
        it('Should filter array of columns and return without __select', () => {
            const columns = [
                {
                    colId: COLUMN_ID_ROW_SELECTION,
                    getColDef: () => {},
                    getColId: () => COLUMN_ID_ROW_SELECTION,
                },
                {
                    colId: 'product',
                    getColDef: () => ({
                        type: FieldKey.Text,
                    }),
                    getColId: () => 'product',
                },
            ] as unknown as Column[];
            const expectedColumns = [
                {
                    colId: 'product',
                    getColDef: () => ({
                        type: FieldKey.Text,
                    }),
                    getColId: () => 'product',
                },
            ];
            const resultColumns = getColumnsToExport(columns);
            expect(JSON.stringify(resultColumns)).toBe(JSON.stringify(expectedColumns));
        });
        it('Should filter array of columns and return without __actions', () => {
            const columns = [
                {
                    colId: COLUMN_ID_ROW_ACTIONS,
                    getColDef: () => {},
                    getColId: () => COLUMN_ID_ROW_ACTIONS,
                },
                {
                    colId: 'product',
                    getColDef: () => ({
                        type: FieldKey.Text,
                    }),
                    getColId: () => 'product',
                },
            ] as unknown as Column[];
            const expectedColumns = [
                {
                    colId: 'product',
                    getColDef: () => ({
                        type: FieldKey.Text,
                    }),
                    getColId: () => 'product',
                },
            ];
            const resultColumns = getColumnsToExport(columns);
            expect(JSON.stringify(resultColumns)).toBe(JSON.stringify(expectedColumns));
        });
        it('Should filter array of columns and return without type Image', () => {
            const columns = [
                {
                    colId: 'coolImage',
                    getColDef: () => ({
                        type: FieldKey.Image,
                    }),
                    getColId: () => 'coolImage',
                },
                {
                    colId: 'product',
                    getColDef: () => ({
                        type: FieldKey.Text,
                    }),
                    getColId: () => 'product',
                },
            ] as unknown as Column[];
            const expectedColumns = [
                {
                    colId: 'product',
                    getColDef: () => ({
                        type: FieldKey.Text,
                    }),
                    getColId: () => 'product',
                },
            ];
            const resultColumns = getColumnsToExport(columns);
            expect(JSON.stringify(resultColumns)).toBe(JSON.stringify(expectedColumns));
        });
        it('Should return a empty array if we pass a empty array', () => {
            const columns = [] as Column[];
            const expectedColumns = [];
            const resultColumns = getColumnsToExport(columns);
            expect(resultColumns).toEqual(expectedColumns);
        });
    });
    describe('cellExcelFormatter function', () => {
        beforeEach(() => {
            const mockState = getMockState({
                applicationContext: {
                    updateMenu: jest.fn(),
                    handleNavigation: jest.fn(),
                    onPageTitleChange: jest.fn(),
                    locale: 'en-US',
                },
            });
            getMockStore(mockState);
        });

        afterEach(() => {
            jest.resetAllMocks();
        });
        it('Should add a % to value with type Progress', () => {
            const column = {
                type: FieldKey.Progress,
            } as NestedField<ScreenBase, NestedFieldTypes, any>;
            const value = 50;
            const expectValue = '50%';
            const resultValue = cellExportFormatter(column, value);
            expect(resultValue).toEqual(expectValue);
        });
        it('Should return the same value', () => {
            const column = {
                type: FieldKey.Text,
            } as NestedField<ScreenBase, NestedFieldTypes, any>;
            const value = 'patata';
            const expectValue = 'patata';
            const resultValue = cellExportFormatter(column, value);
            expect(resultValue).toEqual(expectValue);
        });
        it('Should return empty string with input empty string', () => {
            const column = {
                type: FieldKey.Text,
            } as NestedField<ScreenBase, NestedFieldTypes, any>;
            const value = '';
            const expectValue = '';
            const resultValue = cellExportFormatter(column, value);
            expect(resultValue).toEqual(expectValue);
        });
        it('Should return empty string with null', () => {
            const column = {
                type: FieldKey.Text,
            } as NestedField<ScreenBase, NestedFieldTypes, any>;
            const value = null;
            const expectValue = '';
            const resultValue = cellExportFormatter(column, value);
            expect(resultValue).toEqual(expectValue);
        });
        it('Should return empty string with input undefined', () => {
            const column = {
                type: FieldKey.Text,
            } as NestedField<ScreenBase, NestedFieldTypes, any>;
            const value = undefined;
            const expectValue = '';
            const resultValue = cellExportFormatter(column, value);
            expect(resultValue).toEqual(expectValue);
        });
        it('Should convert number to string', () => {
            const column = {
                type: FieldKey.Text,
            } as NestedField<ScreenBase, NestedFieldTypes, any>;
            const value = 10;
            const expectValue = '10';
            const resultValue = cellExportFormatter(column, value);
            expect(resultValue).toEqual(expectValue);
        });
    });
});
