import { <PERSON><PERSON><PERSON>, type Dict } from '@sage/xtrem-shared';
import { has, isNil, uniqBy } from 'lodash';
import type { ComponentProperties } from '../component/base-control-object';
import type {
    NumericDecoratorProperties,
    ReferenceDecoratorProperties,
    SelectDecoratorProperties,
    TextDecoratorProperties,
} from '../component/decorator-properties';
import type {
    HasColumns,
    HasHelperTextField,
    HasImageField,
    HasMaxMin,
    HasMaxMinLength,
    HasNode,
    HasOptionType,
    HasScale,
    HasValueField,
} from '../component/field/traits';
import * as nestedFields from '../component/nested-fields';
import type {
    DataTypeDetails,
    DataTypeProperty,
    FormattedNodeDetails,
    NodeDetailsProperty,
} from '../service/metadata-types';
import { findDeepPropertyType } from './node-utils';
import { schemaTypeNameFromNodeName } from './transformers';
import type { ValueOrCallbackWithFieldValue } from './types';
import { convertDeepBindToPath, convertDeepBindToPathNotNull } from './nested-field-utils';
import type { NestedFieldTypes } from '../component/nested-fields';
import type { CardDefinition } from '../component/ui/card/card-component';
import type { ReferenceProperties, TableProperties } from '../component/field/field-properties';
import type { MarkOptional } from 'ts-essentials';

export interface AutomaticFieldConfigurationArgs<T> {
    propertyDetails: NodeDetailsProperty;
    dataType: DataTypeDetails;
    properties: T;
}
export interface AutomaticFieldConfigurationArgsWithSchemaInfo<T> extends AutomaticFieldConfigurationArgs<T> {
    nodeTypes: Dict<FormattedNodeDetails>;
    dataTypes: Dict<DataTypeDetails>;
}

export function getCardDefinitionFromReferenceDataType({
    dataType,
    dataTypes,
    nodeTypes,
    contextNode,
}: {
    nodeTypes: Dict<FormattedNodeDetails>;
    dataTypes: Dict<DataTypeDetails>;
    dataType: DataTypeDetails;
    contextNode: string;
}): CardDefinition<any> {
    const namedCardProperties = [
        convertDeepBindToPath(dataType.imageField?.bind),
        convertDeepBindToPath(dataType.value?.bind),
        convertDeepBindToPath(dataType.helperText?.bind),
    ];

    const columns = (dataType.columns || [])
        .map(c => createNestedFieldFromDataTypeProperty(c, nodeTypes, dataTypes, contextNode))
        .reduce<Dict<nestedFields.NestedField<any, NestedFieldTypes>>>((prevValue, curr) => {
            const normalizedBind = convertDeepBindToPathNotNull(curr.properties.bind);
            const propertyDetails = findDeepPropertyType(
                schemaTypeNameFromNodeName(contextNode),
                normalizedBind,
                nodeTypes,
            );

            if (!namedCardProperties.includes(normalizedBind) && propertyDetails?.isOnOutputType) {
                prevValue[normalizedBind.split('.').join('__')] = curr;
            }
            return prevValue;
        }, {});

    return {
        image: dataType.imageField
            ? createNestedFieldFromDataTypeProperty(dataType.imageField, nodeTypes, dataTypes, contextNode)
            : undefined,
        title: createNestedFieldFromDataTypeProperty(dataType.value, nodeTypes, dataTypes, contextNode),
        line2: dataType.helperText
            ? createNestedFieldFromDataTypeProperty(dataType.helperText, nodeTypes, dataTypes, contextNode)
            : undefined,
        ...columns,
    };
}

export function applyDefaultValuesOnNestedField(
    nodeTypes: Dict<FormattedNodeDetails>,
    dataTypes: Dict<DataTypeDetails>,
    field?: nestedFields.NestedField<any, NestedFieldTypes>,
    contextNode?: string,
): void {
    if (!field || !contextNode) {
        return;
    }
    const node = schemaTypeNameFromNodeName(contextNode);
    const propertyDetails = findDeepPropertyType(node, field.properties.bind, nodeTypes);
    const dataType = propertyDetails?.dataType ? dataTypes[propertyDetails?.dataType] : null;

    if (!propertyDetails) {
        return;
    }

    const { properties, defaultUiProperties, type } = field;

    addTitleToProperties({
        propertyDetails,
        properties: properties as { title?: ValueOrCallbackWithFieldValue<any, string> },
    });
    addTitleToProperties({
        propertyDetails,
        properties: defaultUiProperties as { title?: ValueOrCallbackWithFieldValue<any, string> },
    });
    if (!dataType) {
        return;
    }

    switch (type) {
        case FieldKey.Reference:
            addDisabledToProperties({ propertyDetails, dataType, properties });
            addDisabledToProperties({ propertyDetails, dataType, properties: defaultUiProperties });
            addValueFieldToProperties({
                dataType,
                properties: properties as HasValueField<any>,
                propertyDetails,
            });
            addValueFieldToProperties({
                dataType,
                properties: defaultUiProperties as HasValueField<any>,
                propertyDetails,
            });
            addHelperTextFieldToProperties({
                dataType,
                properties: properties as HasHelperTextField<any>,
                propertyDetails,
            });
            addHelperTextFieldToProperties({
                dataType,
                properties: defaultUiProperties as HasHelperTextField<any>,
                propertyDetails,
            });
            addImageFieldToProperties({
                dataType,
                properties: properties as HasImageField<any>,
                propertyDetails,
            });
            addImageFieldToProperties({
                dataType,
                properties: defaultUiProperties as HasImageField<any>,
                propertyDetails,
            });
            addNodeToProperties({
                dataType,
                propertyDetails,
                properties: properties as HasNode<any>,
            });
            addNodeToProperties({
                dataType,
                propertyDetails,
                properties: defaultUiProperties as HasNode<any>,
            });
            addColumnsToProperties(
                {
                    nodeTypes,
                    dataTypes,
                    dataType,
                    properties: properties as HasColumns<any, any, NestedFieldTypes>,
                    propertyDetails,
                },
                true,
            );
            addColumnsToProperties(
                {
                    nodeTypes,
                    dataTypes,
                    dataType,
                    properties: defaultUiProperties as HasColumns<any, any, NestedFieldTypes>,
                    propertyDetails,
                },
                true,
            );
            const columns = (properties as ReferenceProperties).columns;
            const subnode = (properties as ReferenceProperties).node;
            if (columns && subnode) {
                columns.forEach(c => {
                    applyDefaultValuesOnNestedField(nodeTypes, dataTypes, c, String(subnode));
                });
            }
            break;

        case FieldKey.Numeric:
            addDisabledToProperties({ propertyDetails, dataType, properties });
            addDisabledToProperties({ propertyDetails, dataType, properties: defaultUiProperties });
            addMaxMinToProperties({
                dataType,
                propertyDetails,
                properties: properties as HasMaxMin<any>,
            });
            addMaxMinToProperties({
                dataType,
                propertyDetails,
                properties: defaultUiProperties as HasMaxMin<any>,
            });
            break;
        case FieldKey.Text:
            addDisabledToProperties({ propertyDetails, dataType, properties });
            addDisabledToProperties({ propertyDetails, dataType, properties: defaultUiProperties });
            addMaxLengthToProperties({
                dataType,
                propertyDetails,
                properties: properties as HasMaxMinLength<any>,
            });
            addMaxLengthToProperties({
                dataType,
                propertyDetails,
                properties: defaultUiProperties as HasMaxMinLength<any>,
            });
            break;
        case FieldKey.Select:
        case FieldKey.DropdownList:
            addDisabledToProperties({ propertyDetails, dataType, properties });
            addDisabledToProperties({ propertyDetails, dataType, properties: defaultUiProperties });
            addOptionTypeToProperties({
                dataType,
                propertyDetails,
                properties: properties as HasOptionType,
            });
            addOptionTypeToProperties({
                dataType,
                propertyDetails,
                properties: defaultUiProperties as HasOptionType,
            });
            break;
        case FieldKey.Label:
            addOptionTypeToProperties({
                dataType,
                propertyDetails,
                properties: properties as HasOptionType,
            });
            addOptionTypeToProperties({
                dataType,
                propertyDetails,
                properties: defaultUiProperties as HasOptionType,
            });
            break;
        default:
        // Intentionally empty
    }
}

export function createNestedFieldFromDataTypeProperty(
    prop: DataTypeProperty,
    nodeTypes: Dict<FormattedNodeDetails>,
    dataTypes: Dict<DataTypeDetails>,
    contextNode: string,
): nestedFields.NestedField<any, any> {
    const node = schemaTypeNameFromNodeName(contextNode);
    const propertyDetails = findDeepPropertyType(node, prop.bind, nodeTypes) || undefined;
    const dataType = propertyDetails?.dataType ? dataTypes[propertyDetails?.dataType] : undefined;
    const propCopy = { ...prop };

    if (propertyDetails) {
        addTitleToProperties({ propertyDetails, properties: propCopy });
    }

    switch (prop.type) {
        case 'boolean':
            addDisabledToProperties({ propertyDetails, dataType, properties: propCopy as ComponentProperties });
            return nestedFields.checkbox(propCopy);
        case 'date':
            addDisabledToProperties({ propertyDetails, dataType, properties: propCopy as ComponentProperties });
            return nestedFields.date(propCopy);
        case 'integer':
            (propCopy as NumericDecoratorProperties).scale = 0;
            addDisabledToProperties({ propertyDetails, dataType, properties: propCopy as ComponentProperties });
            return nestedFields.numeric(propCopy);
        case 'decimal':
        case 'double':
        case 'short':
            if (dataType && propertyDetails) {
                addMaxMinToProperties({
                    dataType,
                    propertyDetails,
                    properties: propCopy as NumericDecoratorProperties,
                });
                addScaleToProperties({
                    dataType,
                    propertyDetails,
                    properties: propCopy as NumericDecoratorProperties,
                });
            }
            addDisabledToProperties({ propertyDetails, dataType, properties: propCopy as ComponentProperties });
            return nestedFields.numeric(propCopy);
        case 'string':
            if (dataType && propertyDetails) {
                addMaxLengthToProperties({
                    dataType,
                    propertyDetails,
                    properties: propCopy as TextDecoratorProperties,
                });
            }
            addDisabledToProperties({ propertyDetails, dataType, properties: propCopy as ComponentProperties });
            return nestedFields.text(propCopy);
        case 'json':
            return nestedFields.text(propCopy);
        case 'binaryStream':
            return nestedFields.image(propCopy);
        case 'enum':
            if (dataType && propertyDetails) {
                addOptionTypeToProperties({
                    dataType,
                    propertyDetails,
                    properties: propCopy as SelectDecoratorProperties,
                });
            }
            addDisabledToProperties({ propertyDetails, dataType, properties: propCopy as ComponentProperties });
            return nestedFields.select(propCopy);
        default:
            addDisabledToProperties({ propertyDetails, dataType, properties: propCopy as ComponentProperties });
            return nestedFields.text(propCopy);
    }
}

export function addTitleToProperties({
    propertyDetails,
    properties,
}: Omit<AutomaticFieldConfigurationArgs<{ title?: ValueOrCallbackWithFieldValue<any, string> }>, 'dataType'>): void {
    if (propertyDetails.title && !has(properties, 'title')) {
        properties.title = propertyDetails.title;
    }
}
export function addIsTransientInput({
    propertyDetails,
    properties,
}: AutomaticFieldConfigurationArgs<{ isTransientInput?: boolean }>): void {
    if (!propertyDetails.isOnOutputType && !has(properties, 'isTransientInput')) {
        properties.isTransientInput = true;
    }
}

export function addMaxLengthToProperties({
    dataType,
    properties,
}: AutomaticFieldConfigurationArgs<HasMaxMinLength<any>>): void {
    if (dataType.maxLength && !has(properties, 'maxLength')) {
        properties.maxLength = dataType.maxLength;
    }
}

export function addOptionTypeToProperties({
    propertyDetails,
    properties,
}: AutomaticFieldConfigurationArgs<HasOptionType>): void {
    if (propertyDetails.enumType && !has(properties, 'optionType')) {
        properties.optionType = propertyDetails.enumType;
    }
}

export function addScaleToProperties({
    dataType,
    properties,
}: AutomaticFieldConfigurationArgs<HasScale<any, any>>): void {
    if (!isNil(dataType.scale) && !has(properties, 'scale')) {
        properties.scale = dataType.scale;
    }

    if (dataType.type === 'integer') {
        properties.scale = 0;
    }
}

export function addMaxMinToProperties({ dataType, properties }: AutomaticFieldConfigurationArgs<HasMaxMin<any>>): void {
    if (!isNil(dataType.scale) && !isNil(dataType.precision) && !has(properties, 'min') && !has(properties, 'max')) {
        const max = 10 ** (dataType.precision - dataType.scale) - 10 ** -dataType.scale;
        properties.max = max;
        properties.min = -max;
    }
}

export function addNodeToProperties({
    propertyDetails,
    properties,
}: AutomaticFieldConfigurationArgs<HasNode<any>>): void {
    if (propertyDetails.targetNode && !has(properties, 'node')) {
        properties.node = propertyDetails.targetNode;
    }
}
export function addTunnelPageToProperties({
    dataType,
    properties,
}: AutomaticFieldConfigurationArgs<{ tunnelPage?: string }>): void {
    if (dataType.tunnelPage && !has(properties, 'tunnelPage')) {
        properties.tunnelPage = dataType.tunnelPage;
    }
}

export function addDisabledToProperties({
    propertyDetails,
    properties,
}: MarkOptional<AutomaticFieldConfigurationArgs<ComponentProperties<any>>, 'propertyDetails' | 'dataType'>): void {
    if (propertyDetails && propertyDetails.isOnInputType === false && !has(properties, 'isDisabled')) {
        properties.isDisabled = true;
    }
}

export function getDefaultColumnsFromDataType({
    ignoreCardDefinition = false,
    nodeTypes,
    dataTypes,
    dataType,
    targetNode,
}: {
    ignoreCardDefinition?: boolean;
    nodeTypes: Dict<FormattedNodeDetails>;
    dataTypes: Dict<DataTypeDetails>;
    dataType: DataTypeDetails;
    targetNode: string;
}): nestedFields.NestedField<any, nestedFields.NestedFieldTypes>[] {
    if (ignoreCardDefinition && targetNode) {
        return (dataType.columns || [])
            .filter(c => {
                const details = findDeepPropertyType(
                    schemaTypeNameFromNodeName(targetNode),
                    convertDeepBindToPathNotNull(c.bind),
                    nodeTypes,
                );
                return details?.isOnInputType;
            })
            .map(c => createNestedFieldFromDataTypeProperty(c, nodeTypes, dataTypes, targetNode));
    }
    return uniqBy(
        Object.values(
            getCardDefinitionFromReferenceDataType({
                nodeTypes,
                dataTypes,
                dataType,
                contextNode: targetNode,
            }),
        ).filter(c => !!c) as nestedFields.NestedField<any, nestedFields.NestedFieldTypes>[],
        f => convertDeepBindToPathNotNull(f.properties.bind),
    );
}

export function addColumnsToProperties(
    {
        dataType,
        propertyDetails,
        properties,
        nodeTypes,
        dataTypes,
    }: AutomaticFieldConfigurationArgsWithSchemaInfo<Partial<HasColumns<any, any, nestedFields.NestedFieldTypes>>>,
    ignoreCardDefinition = false,
): void {
    if (dataType.columns && propertyDetails.targetNode && !has(properties, 'columns')) {
        properties.columns = getDefaultColumnsFromDataType({
            dataType,
            dataTypes,
            ignoreCardDefinition,
            nodeTypes,
            targetNode: propertyDetails.targetNode!,
        });
    }
}

export function addMobileCardDefinitionToProperties({
    dataType,
    propertyDetails,
    properties,
    nodeTypes,
    dataTypes,
}: AutomaticFieldConfigurationArgsWithSchemaInfo<Partial<TableProperties<any>>>): void {
    if (dataType.columns && propertyDetails.targetNode && !has(properties, 'mobileCard')) {
        properties.mobileCard = getCardDefinitionFromReferenceDataType({
            nodeTypes,
            dataTypes,
            dataType,
            contextNode: propertyDetails.targetNode,
        });
    }
}

export function addValueFieldToProperties({
    dataType,
    propertyDetails,
    properties,
}: AutomaticFieldConfigurationArgs<Partial<HasValueField<any>>>): void {
    if (dataType.value?.bind && propertyDetails.targetNode && !has(properties, 'valueField')) {
        properties.valueField = dataType.value.bind;
    }
}

export function addHelperTextFieldToProperties({
    dataType,
    propertyDetails,
    properties,
}: AutomaticFieldConfigurationArgs<Partial<HasHelperTextField<any>>>): void {
    if (dataType.helperText?.bind && propertyDetails.targetNode && !has(properties, 'helperTextField')) {
        properties.helperTextField = dataType.helperText.bind;
    }
}
export function addImageFieldToProperties({
    dataType,
    propertyDetails,
    properties,
}: AutomaticFieldConfigurationArgs<Partial<HasImageField<any>>>): void {
    if (dataType.imageField?.bind && propertyDetails.targetNode && !has(properties, 'imageField')) {
        properties.imageField = dataType.imageField.bind;
    }
}

export function createDynamicPodReferenceField({
    column,
    nodeTypes,
    dataTypes,
}: {
    column: nestedFields.NestedField<any, any>;
    nodeTypes: Dict<FormattedNodeDetails>;
    dataTypes: Dict<DataTypeDetails>;
}): nestedFields.NestedField<any, any> {
    const node = (column.properties as ReferenceDecoratorProperties).node;
    const contextNode = schemaTypeNameFromNodeName((column.properties as ReferenceDecoratorProperties).node);
    if (!contextNode) {
        return column;
    }
    const nodeInfo = nodeTypes[contextNode];
    if (nodeInfo?.defaultDataType) {
        const propertyDetails: NodeDetailsProperty = {
            targetNode: String(node),
            type: nodeInfo.name,
            title: nodeInfo.title,
        };
        const dataType = dataTypes[nodeInfo?.defaultDataType];
        if (dataType && propertyDetails) {
            const propCopy = { ...column.properties } as ReferenceDecoratorProperties;
            addTitleToProperties({ propertyDetails, properties: propCopy });
            addColumnsToProperties({
                nodeTypes,
                dataTypes,
                dataType,
                properties: propCopy,
                propertyDetails,
            });
            addValueFieldToProperties({
                dataType,
                properties: propCopy,
                propertyDetails,
            });
            addHelperTextFieldToProperties({
                dataType,
                properties: propCopy,
                propertyDetails,
            });

            return {
                ...column,
                properties: propCopy,
                defaultProperties: propCopy,
            } as nestedFields.NestedField<any, any>;
        }
    }

    return column;
}
