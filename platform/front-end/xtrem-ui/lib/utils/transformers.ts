import { objectKeys, type Dict } from '@sage/xtrem-shared';
import { camelCase, cloneDeep, get, groupBy, isArray, isEmpty, isPlainObject, set } from 'lodash';
import type { PageProperties } from '../component/container/page/page-types';
import type { PluginProperties } from '../component/control-objects';
import {
    MultiReferenceControlObject,
    NodeBrowserTreeControlObject,
    VitalPodControlObject,
} from '../component/control-objects';
import type { EditableFieldProperties } from '../component/editable-field-control-object';
import type {
    EditableFieldComponentProperties,
    FieldComponentExternalProperties,
} from '../component/field/field-base-component-types';
import type { HasOptionType, HasOptions } from '../component/field/traits';
import type { NestedField, NestedFieldTypes } from '../component/nested-fields';
import type { NestedExtensionField } from '../component/nested-fields-extensions';
import type { ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import type { FieldKey } from '../component/types';
import type { CardExtensionDefinition } from '../component/ui/card/card-component';
import * as xtremRedux from '../redux';
import { CollectionValue } from '../service/collection-data-service';
import { nonSavableFieldType } from '../service/graphql-api';
import { localizeEnumMember } from '../service/i18n-service';
import type { Page } from '../service/page';
import type { ScreenBase } from '../service/screen-base';
import { serializePageData } from '../service/value-serializer-service';
import type { NodePropertyType } from '../types';
import type { ArtifactDescription } from '../types/artifact-description';
import { normalizeUnderscoreBind } from './abstract-fields-utils';
import { convertDeepBindToPathNotNull } from './nested-field-utils';

const isReferenceFieldValue = (fieldValue: any): boolean =>
    fieldValue && !Array.isArray(fieldValue) && isPlainObject(fieldValue);

/**
 * Converts object to have unique ids using fieldname__key
 */
export function mergedValueToSplitValue<T extends Object>(mergedValue: T): T {
    return splitReferenceFieldsProperties(mergedValue);
}

const splitReferenceFieldsProperties = <T extends object>(mergedValue: T): T => {
    if (!isReferenceFieldValue(mergedValue)) {
        return mergedValue;
    }
    const parsedValues = objectKeys(mergedValue)
        // We filter the reference fields with the following criteria
        .filter(key => !key.startsWith('__') && isReferenceFieldValue(mergedValue[key]))
        .reduce((_parsedValues, key) => {
            const fieldName = key;
            const fieldValue = mergedValue[key] as any;
            const splitProperties = objectKeys(fieldValue)
                .filter(_key => _key !== '_id')
                .reduce(
                    (_parsedValue, _key) => ({
                        ..._parsedValue,
                        [`${fieldName}__${_key}`]: { [_key]: fieldValue[_key], _id: fieldValue._id },
                    }),
                    {} as any,
                );
            delete mergedValue[key]; // Remove the original reference field; e.g. user { name: , age: }
            return { ..._parsedValues, ...splitProperties };
        }, {} as any);
    return { ...parsedValues, ...mergedValue };
};

/**
 * Converts the object to a normal normal value without __ (see mergedValueToSplitValue)
 */
export function splitValueToMergedValue<T extends Object>(splitValue: T): T {
    return mergeReferenceFieldsProperties(splitValue);
}

function mergeReferenceFieldsProperties<T extends Object>(splitValue: T): T {
    if (!isReferenceFieldValue(splitValue)) {
        return splitValue;
    }

    const groupedKeys = groupBy(objectKeys(splitValue), normalizeUnderscoreBind);
    const objectWithRemappedKeys = objectKeys(groupedKeys).reduce((mergedValue, groupKey) => {
        groupedKeys[groupKey].forEach(key => {
            if (mergedValue[groupKey]) {
                mergedValue[groupKey] = {
                    ...underscoreObjectCleaner(get(splitValue, key)),
                    ...mergedValue[groupKey],
                };
            } else {
                mergedValue[groupKey] = underscoreObjectCleaner(get(splitValue, key));
            }
        });
        return mergedValue;
    }, {} as any);

    return objectKeys(objectWithRemappedKeys).reduce((previousValue: any, key: string) => {
        if (key) {
            const value = isPlainObject(objectWithRemappedKeys[key])
                ? mergeReferenceFieldsProperties(objectWithRemappedKeys[key])
                : objectWithRemappedKeys[key];

            // Empty objects should be replaced with nulls
            previousValue[key] = isPlainObject(value) && !isArray(value) && isEmpty(value) ? null : value;
        }

        return previousValue;
    }, {} as any);
}

function underscoreObjectCleaner<T extends Object>(value: T): T {
    if (isReferenceFieldValue(value)) {
        return objectKeys(value).reduce(
            (cleanValue, key) => ({ ...cleanValue, [normalizeUnderscoreBind(key)]: value[key] }),
            {} as any,
        );
    }
    return value;
}

export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?([a-f\d]{2})$/i.exec(hex);
    return result
        ? {
              r: parseInt(result[1], 16),
              g: parseInt(result[2], 16),
              b: parseInt(result[3], 16),
          }
        : null;
};

export const serializeDataForTransientPage = (
    screenId: string,
    useBindKeys = true,
    isRequestingDefaults = false,
): any => {
    const state = xtremRedux.getStore().getState();
    const screenDefinition = state.screenDefinitions[screenId];

    let screenValues: Dict<any> = {};
    if (screenDefinition?.values && screenDefinition?.metadata?.uiComponentProperties) {
        const values = screenDefinition.values;
        const uiProperties = screenDefinition.metadata.uiComponentProperties as Dict<ReadonlyFieldProperties>;
        const addValue = (reduced: Dict<any>, key: string, value: any): Dict<any> => {
            const keyToSet = useBindKeys ? convertDeepBindToPathNotNull(uiProperties[key].bind || key) : key;
            const target = cloneDeep(reduced);
            set(target, keyToSet, value);
            return target;
        };

        screenValues = objectKeys(values)
            .filter(k => objectKeys(uiProperties).includes(k))
            .filter(key => uiProperties[key] && values[key] !== undefined)
            .reduce<Dict<any>>((reduced, key) => {
                const value = values[key];
                const uiProps = uiProperties[key];

                if ((uiProps as PluginProperties).pluginPackage) {
                    const plugin = state.plugins[(uiProps as PluginProperties).pluginPackage];
                    return addValue(
                        reduced,
                        key,
                        plugin.transformToGraphValue ? plugin.transformToGraphValue(value) : value,
                    );
                }
                if (value instanceof Array) {
                    if (screenDefinition.metadata.controlObjects[key] instanceof MultiReferenceControlObject) {
                        const referenceIds = value
                            .map(v => v._id)
                            .filter(Boolean)
                            .map(v => String(v));
                        return addValue(reduced, key, referenceIds);
                    }
                    return addValue(reduced, key, value);
                }
                if (value instanceof CollectionValue) {
                    if (Object.prototype.hasOwnProperty.call(uiProps, 'levels')) {
                        // nested grid
                        const changeSet = value.getChangedRecordsAsTree();
                        if (changeSet.length > 0) {
                            return addValue(reduced, key, changeSet);
                        }
                    } else {
                        const changeSet = value.getNormalizedChangedRecords(isRequestingDefaults);
                        const cleanChangeSet = changeSet.map(element =>
                            mergeReferenceFieldsProperties(cleanMetadataFromRecord(element)),
                        );
                        if (changeSet.length > 0) {
                            return addValue(reduced, key, cleanChangeSet);
                        }
                    }
                    // If no items are in the changeset, we leave it as and don't push it to the server
                    return reduced;
                }
                if (isPlainObject(value)) {
                    const controlObject = screenDefinition.metadata.controlObjects[key];
                    if (controlObject instanceof VitalPodControlObject) {
                        const updatedValue = { ...value };

                        return addValue(
                            reduced,
                            key,
                            objectKeys(updatedValue).reduce((prevValue: any, prop: any) => {
                                if (isPlainObject(updatedValue[prop]) && updatedValue[prop]._id) {
                                    prevValue[prop] = `${updatedValue[prop]._id}`;
                                } else {
                                    prevValue[prop] = updatedValue[prop];
                                }

                                return prevValue;
                            }, {} as any),
                        );
                    }

                    if (controlObject instanceof NodeBrowserTreeControlObject) {
                        return addValue(reduced, key, value);
                    }

                    if ('data' in value && Array.isArray(value.data)) {
                        return addValue(reduced, key, value.data);
                    }
                    if ('node' in uiProps) {
                        // TODO: This logic above is not very bright, let's add the field type to the uiProperties
                        // Check for reference fields
                        return addValue(reduced, key, value && value._id ? `_id:${value._id}` : null);
                    }
                    return addValue(reduced, key, value);
                }
                return addValue(reduced, key, value);
            }, {});
    }

    return screenValues;
};

export const parseScreenValues = (
    screenId: string,
    useBindKeys = true,
    isRequestingDefaults = false,
    state = xtremRedux.getStore().getState(),
): Dict<any> => {
    const screenDefinition = state.screenDefinitions[screenId];

    let screenValues: Dict<any> = {};
    if (screenDefinition?.values && screenDefinition?.metadata?.uiComponentProperties) {
        const uiComponentProperties = screenDefinition.metadata.uiComponentProperties as Dict<ReadonlyFieldProperties>;
        const screenProperties = uiComponentProperties[screenId] as PageProperties<any>;

        screenValues =
            screenProperties.node && state.nodeTypes[schemaTypeNameFromNodeName(screenProperties.node)]
                ? serializePageData({
                      isRequestingDefaults,
                      nodeTypes: state.nodeTypes,
                      screenDefinition,
                      targetNode: schemaTypeNameFromNodeName(screenProperties.node),
                  })
                : serializeDataForTransientPage(screenId, useBindKeys, isRequestingDefaults);
    }
    return screenValues || {};
};

export const getScreenValues = (screenId: string, useBindKeys = true): Dict<any> => {
    const state = xtremRedux.getStore().getState();
    const screenDefinition = state.screenDefinitions[screenId];

    let screenValues = {};
    if (screenDefinition?.values && screenDefinition?.metadata?.uiComponentProperties) {
        const values = screenDefinition.values;
        const uiProperties = screenDefinition.metadata.uiComponentProperties as Dict<ReadonlyFieldProperties>;

        screenValues = objectKeys(values)
            .filter(
                key =>
                    objectKeys(uiProperties).includes(key) &&
                    uiProperties[key] &&
                    !uiProperties[key].isTransient &&
                    uiProperties[key]._controlObjectType &&
                    !nonSavableFieldType.includes(uiProperties[key]._controlObjectType as FieldKey) &&
                    values[key] !== undefined,
            )
            .reduce((previousValue: Dict<any>, key: string) => {
                const keyToSet =
                    useBindKeys && uiProperties[key].bind ? convertDeepBindToPathNotNull(uiProperties[key].bind) : key;
                set(previousValue, keyToSet, values[key]);
                return previousValue;
            }, {} as Dict<any>);
    }

    return screenValues;
};

export const capitalize = (s: string): string => s[0].toUpperCase() + s.substring(1);

export const pascalCase = (s: string): string => capitalize(camelCase(s));

export function schemaTypeNameFromNodeName(name: NodePropertyType): string;
export function schemaTypeNameFromNodeName(name?: NodePropertyType): undefined | string;
export function schemaTypeNameFromNodeName(name?: NodePropertyType): string | undefined {
    if (!name) {
        return undefined;
    }
    const parts = String(name).split('/').map(pascalCase);
    return parts[parts.length - 1];
}

export const getArtifactDescription = (path: string): ArtifactDescription => {
    const pathElements = path.split('/');
    const name = pathElements[2] || 'index'; // E.g. CheckboxField (if no artifact name is provided, all the module artifacts are loaded through index.js)
    return {
        name,
        package: pathElements[1], // E.g. xtrem-show-case
        vendor: pathElements[0], // E.g. @sage
    };
};

export const cleanMetadataFromRecord = (record: any, removeAction = true, removeSortValue = false): any => {
    if (record === undefined || record === null || typeof record !== 'object') {
        return record;
    }
    const updatedRecord = cloneDeep(record);
    objectKeys(updatedRecord).forEach(key => {
        if (key === '_sortValue' && removeSortValue) {
            delete updatedRecord[key];
            return;
        }

        if (key === '__dirtyColumns') {
            delete updatedRecord[key];
            return;
        }

        if (key === '__validationState') {
            delete updatedRecord[key];
            return;
        }

        if (key === '__uncommitted') {
            delete updatedRecord[key];
            return;
        }

        if (key.startsWith('__')) {
            if (key === '__action' && !removeAction) {
                return;
            }
            delete updatedRecord[key];
        }
    });
    delete updatedRecord.$loki;
    delete updatedRecord[''];
    return updatedRecord;
};

export const cleanMetadataAndNonPersistentIdFromRecord = (record: any): any => {
    const updatedRecord = cleanMetadataFromRecord(record);
    if (updatedRecord._id && Number(updatedRecord._id) < 0) {
        delete updatedRecord._id;
    }

    return updatedRecord;
};

export interface FieldPropsWithOptions
    extends EditableFieldComponentProperties<EditableFieldProperties & HasOptionType & HasOptions<any>, any>,
        FieldComponentExternalProperties {}

export const addOptionsAndLocalizationToProps = <T extends FieldPropsWithOptions>(
    state: xtremRedux.XtremAppState,
    baseProps: T,
): T & { localizedOptions?: Dict<string>; enumOptions?: string[] } => {
    if (baseProps.fieldProperties.optionType) {
        const optionTypes = state.enumTypes[schemaTypeNameFromNodeName(baseProps.fieldProperties.optionType)] || [];
        const enumOptions = [...optionTypes];
        const localizedOptions = enumOptions.reduce((value: Dict<string>, key: string) => {
            return { ...value, [key]: localizeEnumMember(baseProps.fieldProperties.optionType!, key) };
        }, {} as Dict<string>);
        return { ...baseProps, enumOptions, localizedOptions };
    }
    return baseProps;
};

export const getNestedFieldArrayFromCardDefinition = <T extends NestedFieldTypes>(
    cardDefinition: Dict<NestedField<any, T> | undefined>,
): NestedField<Page, T>[] => Object.values(cardDefinition).filter(v => !!v) as NestedField<Page, T>[];

export const getNestedFieldArrayFromCardExtensionDefinition = <T extends NestedFieldTypes>(
    cardDefinition: CardExtensionDefinition,
): NestedExtensionField<Page, T>[] => Object.values(cardDefinition).filter(v => !!v) as NestedExtensionField<Page, T>[];

export const arrayMoveMutate = (array: any[], from: number, to: number): void => {
    const startIndex = from < 0 ? array.length + from : from;
    if (startIndex >= 0 && startIndex < array.length) {
        const endIndex = to < 0 ? array.length + to : to;
        const [item] = array.splice(from, 1);
        array.splice(endIndex, 0, item);
    }
};

// TODO: REVIEW THESE SIGNATURES
export function forceReadOnlyModeOnNestedField<T extends NestedField<ScreenBase, NestedFieldTypes> | undefined>(
    nestedField: T,
): T;
export function forceReadOnlyModeOnNestedField(nestedField: any): any {
    if (!nestedField) {
        return undefined;
    }
    return {
        ...nestedField,
        defaultUiProperties: {
            ...nestedField.defaultUiProperties,
            isReadOnly: true,
        },
        properties: {
            ...nestedField.properties,
            isReadOnly: true,
        },
    };
}

/**
 * Not a pure function, mutates the value. Deep clean is advised before calling.
 *
 * Removes nested structures that only contain keys with null values.
 * For example: { _id: 3, code: "Test", site: { country: null, site: { name: null } } } becomes just { _id: 3, code: "Test" }
 * See XT-30693 for more details.
 * @param data
 * @returns
 */
export const removeNullOnlyLeaves = (data: Object): boolean => {
    if (data === null) {
        return true;
    }
    if (Array.isArray(data)) {
        return false;
    }

    if (typeof data !== 'object') {
        return false;
    }

    const keys = objectKeys(data);
    const keysToRemove = keys.filter(key => {
        return removeNullOnlyLeaves(data[key]);
    });

    // If all children keys of the property are null, then we delete key.
    if (keysToRemove.length === keys.length) {
        keysToRemove.forEach(k => {
            delete data[k];
        });
        return true;
    }

    keys.forEach(k => {
        // If the object is empty after the null cleanup, we delete the object
        if (
            typeof data[k] === 'object' &&
            data[k] !== null &&
            !Array.isArray(data[k]) &&
            objectKeys(data[k]).length === 0
        ) {
            delete data[k];
        }
    });

    return false;
};
