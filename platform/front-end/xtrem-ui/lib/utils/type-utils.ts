import type { Dict } from '@sage/xtrem-shared';
import { cloneDeep, isArray, isPlainObject, reduce } from 'lodash';

export const isNumber = (value?: any): value is number => {
    return typeof value !== 'undefined' && value !== null && !Number.isNaN(Number(value));
};

type PathType = Array<string | number>;
type CollectionCallback<ValueType, AccumulatorType, SourceType> = (
    value: ValueType,
    acc: AccumulatorType,
    key: string | number,
    path: PathType,
    source: SourceType,
) => [ValueType, PathType];
type ValueCallback<ValueType, AccumulatorType, SourceType> = (
    value: ValueType,
    acc: AccumulatorType,
    key: string | number,
    path: PathType,
    source: SourceType,
) => AccumulatorType;
type onArray<AccumulatorType, SourceType> = CollectionCallback<Array<any>, AccumulatorType, SourceType>;
type onObject<AccumulatorType, SourceType> = CollectionCallback<Dict<any>, AccumulatorType, SourceType>;
type onValue<AccumulatorType, SourceType> = ValueCallback<any, AccumulatorType, SourceType>;

export const reduceDict =
    <SourceType, AccumulatorType>() =>
    (
        source: SourceType,
        initial: AccumulatorType,
        onValueFn: onValue<AccumulatorType, SourceType>,
        onObjectFn?: onObject<AccumulatorType, SourceType>,
        onArrayFn?: onArray<AccumulatorType, SourceType>,
    ): AccumulatorType => {
        const reduceTree = (
            tree: Dict<any>,
            accumulator: AccumulatorType,
            path: PathType = [],
            root: boolean = false,
        ): AccumulatorType => {
            if (root && isArray(tree)) {
                if (onArrayFn) {
                    const [remappedValue, remappedPath] = onArrayFn(tree, initial, '', path, source);
                    return reduceTree(remappedValue, initial, remappedPath);
                }
            }
            if (root && isPlainObject(tree)) {
                if (onObjectFn) {
                    const [remappedValue, remappedPath] = onObjectFn(tree, initial, '', path, source);
                    return reduceTree(remappedValue, initial, remappedPath);
                }
            }
            return reduce(
                tree,
                (acc, value, key) => {
                    const updatedPath = [...path, key];
                    if (isArray(value)) {
                        if (onArrayFn) {
                            const [remappedValue, remappedPath] = onArrayFn(value, acc, key, updatedPath, source);
                            return reduceTree(remappedValue, acc, remappedPath);
                        }
                        return reduceTree(value, acc, updatedPath);
                    }
                    if (isPlainObject(value)) {
                        if (onObjectFn) {
                            const [remappedValue, remappedPath] = onObjectFn(value, acc, key, updatedPath, source);
                            return reduceTree(remappedValue, acc, remappedPath);
                        }
                        return reduceTree(value, acc, updatedPath);
                    }
                    return onValueFn(value, acc, key, updatedPath, source);
                },
                accumulator,
            );
        };
        return reduceTree(cloneDeep(source) as Dict<any>, initial, [], true);
    };

export function AllAndOnly<T>() {
    // eslint-disable-next-line func-names
    return function <U extends T[]>(
        array: U & ([T] extends [U[number]] ? unknown : never),
    ): U & ([T] extends [U[number]] ? unknown : never) {
        return array;
    };
}
