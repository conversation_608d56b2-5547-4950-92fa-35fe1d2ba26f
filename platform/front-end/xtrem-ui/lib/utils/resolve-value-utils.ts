import { isFunction } from 'lodash';
import type { ScreenBase } from '../service/screen-base';
import { getScreenElement } from '../service/screen-base-definition';
import { executeInReadOnlyTransaction } from '../service/transactions-service';
import { xtremConsole } from './console';
import { getPageDefinitionFromState } from './state-utils';
import { isDevMode } from './window';

export interface ResolveByRecordValueParams {
    rowValue: any | (() => any);
    skipHexFormat?: boolean;
    propertyValue: ((fieldValue: any, entireRowRecordValue?: any) => any) | any;
    screenId: string;
}
export const resolveByRecordValue = <T = any>(params: ResolveByRecordValueParams): T =>
    resolveByValue<T>({ ...params, fieldValue: params.rowValue, rowValue: null });

export interface ResolveByValueParams {
    fieldValue?: any;
    rowValue: any | (() => any);
    skipHexFormat?: boolean;
    propertyValue: ((fieldValue: any, entireRowRecordValue?: any) => any) | any;
    screenId?: string;
}

export const resolveByValue = <T = any>(params: ResolveByValueParams): T => {
    if (!params.propertyValue) {
        return params.propertyValue;
    }

    let parsedValue: any;

    switch (typeof params.propertyValue) {
        case 'boolean':
        case 'number':
        case 'object':
        case 'string':
            parsedValue = params.propertyValue;
            break;
        case 'function':
            try {
                let context: ScreenBase | null = null;

                if (params.screenId) {
                    const screenDefinition = getPageDefinitionFromState(params.screenId);
                    if (screenDefinition) {
                        const screenElement = getScreenElement(screenDefinition);
                        if (screenElement) {
                            context = screenElement;
                        }
                    }
                }
                executeInReadOnlyTransaction(() => {
                    const rowValue = isFunction(params.rowValue) ? params.rowValue() : params.rowValue;
                    parsedValue = params.propertyValue.apply(context, [params.fieldValue, rowValue]);
                });
            } catch (e) {
                if (isDevMode()) {
                    xtremConsole.warn(e);
                }
            }
            break;
        default:
            break;
    }

    if (parsedValue && !params.skipHexFormat) {
        parsedValue = parsedValue.startsWith('#') ? parsedValue : `#${parsedValue}`;
    }
    return parsedValue;
};
