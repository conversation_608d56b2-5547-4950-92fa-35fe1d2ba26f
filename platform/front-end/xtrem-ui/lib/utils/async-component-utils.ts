import type { FieldComponentExternalProperties } from '../component/field/field-base-component-types';
import { ContextType } from '../types';

export const hasConnectedSkeletonFieldTitle = (props: FieldComponentExternalProperties): boolean => {
    return (
        (props.contextType === ContextType.page ||
            props.contextType === ContextType.header ||
            props.contextType === ContextType.detailPanel) &&
        !props.nestedReadOnlyField
    );
};
