PATH: XTREEM/Client+Framework/Error+Handlers

## Introduction

Any field type that have any event handler also has an `onError` error handler callback which is called by the front-end framework whenever an unexpected event occurs during the execution of the related event handler. This article discusses how this callback works.

## Example

```typescript
@ui.decorators.textField<ErrorHandlers>({
    title: 'Text with error handler',
    parent() {
        return this.block;
    },
    onChange() {
        throw new Error("An error occurred")
    },
    onError(error: any, sourceScreenId: string, sourceElementId: string) {
        return 'An error!';
    },
    helperText: 'Error is thrown on change and it is handled.',
})
field1: ui.fields.Text;
```

## `onError` decorator property

The `onError` callback function is available as a decorator property to all field types, nested field types, stickers and pages.
The function takes the following arguments:

- **error**: the error that was thrown from the event handler.
- **sourceScreenId**: the screen ID of the page or sticker where the error occurred.
- **sourceElementId**: the ID element where the error occurred.

The error handler function has access to the `this` scope of the page or sticker and can be synchronous or asynchronous. This allows calling the graph API or validation rules etc to recover from the error.

The function can be either void or return a string value. If it returns a non-empty string, the framework displays an error toast using the returned value as the toast message. When no or falsy value is returned, the framework silently swallows the error.

If an error is thrown and no error handler decorator implementation is found on the element and its parents, then an error dialog is displayed with the error details.

## Execution order

The error handler is called whenever an error is thrown in any of its event handlers or in the children components of the event handlers. The order of execution is the following:

1. Column or other nested field level
2. Field level
3. Page or sticker level

If an error handler is found on any of these levels it is executed and the event propagation is stopped, meaning that if an event handler found it is called and no other handlers are executed in the chain.
For example, if an error is thrown from a table column event handler, first the framework tries to find an error handler on the column declaration. If it does not exist then it checks on the field declaration. If that does not exist either, it checks on the page or sticker level. Finally if no error handler found on any of these levels a message dialog is displayed on screen with the error details including the error message and stack trace.

```
