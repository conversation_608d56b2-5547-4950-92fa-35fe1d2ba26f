import type { ColDef, <PERSON>ridA<PERSON> } from '@ag-grid-community/core';
import { CUSTOM_DATA_PROPERTY, objectKeys, type Dict } from '@sage/xtrem-shared';
import produce from 'immer';
import { includes, isEmpty, isEqual, isNil, set, without } from 'lodash';
import memoizeOne from 'memoize-one';
import type { OptionsMenuItem } from '../component/control-objects';
import { getReferenceValueFieldPath } from '../component/field/reference/reference-utils';
import type {
    TableDecoratorProperties,
    TableProperties,
    TableUserSettings,
    TableViewGrouping,
    TableViewSortedColumn,
} from '../component/field/table/table-component-types';
import { withoutNestedTechnical } from '../component/nested-fields';
import type { NestedReferenceProperties } from '../component/nested-fields-properties';
import type { OrderByType } from '../component/types';
import { FieldKey } from '../component/types';
import type { CardDefinition } from '../component/ui/card/card-component';
import { getStore } from '../redux';
import { getGraphQLFilter, getTypedNestedFields, mergeGraphQLFilters } from '../service/filter-service';
import type { GraphQLFilter } from '../service/graphql-utils';
import { getScreenElement } from '../service/screen-base-definition';
import { getNestedFieldElementId } from './abstract-fields-utils';
import { mapAgGridFilterToXtremFilters } from './ag-grid/ag-grid-table-utils';
import { convertDeepBindToPathNotNull, getNestedFieldsFromProperties } from './nested-field-utils';
import { resolveByValue } from './resolve-value-utils';
import { getPageDefinitionFromState } from './state-utils';
import type { ValueOrCallback } from './types';

export const AUTO_COLUMN_ID = 'ag-Grid-AutoColumn';

export const getGraphFilter = ({
    filterModel,
    screenId,
    tableFieldProperties,
    activeOptionsMenuItem,
}: {
    filterModel: any;
    screenId: string;
    tableFieldProperties: TableProperties;
    activeOptionsMenuItem?: OptionsMenuItem;
}): any => {
    const xtremFilter = objectKeys(filterModel).map(mapAgGridFilterToXtremFilters(filterModel));
    const agGridFilters = getGraphQLFilter(
        xtremFilter,
        getTypedNestedFields(
            screenId,
            tableFieldProperties.node as string,
            getNestedFieldsFromProperties(tableFieldProperties),
            getStore().getState().nodeTypes,
        ),
    );
    let tableFilter = tableFieldProperties.filter;
    if (typeof tableFieldProperties.filter === 'function') {
        tableFilter = tableFieldProperties.filter.apply(
            getPageDefinitionFromState(screenId, getStore().getState()).page,
            [],
        );
    }
    const applicableFilters: GraphQLFilter[] = !isEmpty(tableFilter) ? [agGridFilters, tableFilter] : [agGridFilters];
    if (activeOptionsMenuItem) {
        applicableFilters.push(activeOptionsMenuItem.graphQLFilter);
    }
    return mergeGraphQLFilters(applicableFilters) || ({} as GraphQLFilter);
};

export const isColumnBindVariant = (colBind: string, colId: string): boolean => {
    if (colBind === colId) {
        return true;
    }
    if (colId.includes(colBind)) {
        const multiple = new RegExp(`^${colBind}(__[0-9a-zA-Z]*)?(_[0-9]*)?$`);
        if (multiple.test(colId)) {
            return true;
        }
    }
    return false;
};

export interface ColumnCompareType {
    bind: string;
    valueField?: string;
}

export const resolveCompareFunction = (
    screenId: string,
    compareFunc: Function,
    arg1: ColumnCompareType,
    arg2: ColumnCompareType,
): number => {
    const screenDefinition = getPageDefinitionFromState(screenId);
    const screenElement = getScreenElement(screenDefinition);
    return compareFunc.apply(screenElement, [arg1, arg2]);
};

export const getOrderByKey = (columnId: string): string =>
    removeNumericPostfix(columnId)
        .replace(/([a-zA-Z0-9])_+([a-zA-Z0-9])/g, '$1.$2')
        // ._customData is wrongly replaced as ..customData, so we correct it here
        .replace('..customData', `.${CUSTOM_DATA_PROPERTY}`);

// eslint-disable-next-line @sage/redos/no-vulnerable
const postfixRegex = /(.*)_[1-9]+/g;

// When the nestedGrid value is reset the column names are getting an _{NUMBER} postfix, we remove that
export const removeNumericPostfix = (colId: string): string => postfixRegex.exec(colId)?.[1] ?? colId;

export const removeTableNumericPostfix = removeNumericPostfix;

/** Converts Ag-grid filter statements to Xtrem's GraphQL-friendly filter object structure */
export const getOrderByFromSortModel = (
    sortModel: TableViewSortedColumn[],
    columns: any[],
    autoGroupColumnDef?: ColDef,
): OrderByType =>
    sortModel
        .map(curr => {
            if (curr.colId === AUTO_COLUMN_ID && isNil(autoGroupColumnDef)) {
                throw new Error('Cannot find grouping column');
            }
            const colId = curr.colId === AUTO_COLUMN_ID && autoGroupColumnDef ? autoGroupColumnDef.field! : curr.colId;
            return { ...curr, colId };
        })
        .reduce<OrderByType>((prevValue: Dict<1 | -1>, currentValue: TableViewSortedColumn) => {
            if (!currentValue.sort) {
                return prevValue;
            }
            const prevObjKeys = objectKeys(prevValue);
            let colId: string;
            let colIndex: string | undefined;
            if (currentValue.colId.split('.').includes(CUSTOM_DATA_PROPERTY)) {
                colId = currentValue.colId;
            } else {
                const segments = currentValue.colId.split('_');
                colId = segments[0];
                colIndex = segments[1];
            }
            const columnDef = columns.filter(column => {
                const nestedFieldColumnId = getNestedFieldElementId(column) || '';
                return nestedFieldColumnId === colId || nestedFieldColumnId.startsWith(`${colId}_`);
            })[Number(colIndex) || 0];

            /**
             * As we use bind for rowIds it can be duplicated, Ag-grid adds a _1 _2 for every recordId duplication to
             * make it unique. So we have to clean up this _1 _2 values
             **/
            if (prevObjKeys.find(key => getOrderByKey(currentValue.colId) === key)) {
                return prevValue;
            }
            // Replace any '_' with '.' except from the beginning & end of 'currentValue.colId'
            const orderByKey =
                columnDef && columnDef.type === FieldKey.Reference
                    ? getReferenceValueFieldPath(columnDef.properties)
                    : getOrderByKey(currentValue.colId);
            return set(prevValue, orderByKey, currentValue.sort === 'asc' ? 1 : -1);
        }, {} as OrderByType);

interface ShouldRenderDropdownAboveProps {
    isPhantomRow: boolean;
    pageSize: number;
    rowIndex: number;
}

export const shouldRenderDropdownAbove = ({
    isPhantomRow,
    pageSize,
    rowIndex,
}: ShouldRenderDropdownAboveProps): boolean => {
    if (isPhantomRow) {
        // In the phantom row we always render on the bottom
        return false;
    }

    const indexOnCurrentPage = rowIndex % pageSize;

    // We need this in case of endless scroll, if the row number is less than 4 we should display below.
    if (indexOnCurrentPage < 4) {
        return false;
    }

    // We render above if there are less than 4 records left in the current page
    if (indexOnCurrentPage > pageSize - 4) {
        return true;
    }

    return false;
};

export const calculateHiddenColumns = memoizeOne(
    (hiddenColumns: string[], actualColumns: (string | undefined | null)[]) => {
        return without(actualColumns, undefined, null).filter((colId: string) =>
            hiddenColumns.find(colBind => isColumnBindVariant(colBind, colId)),
        );
    },
    isEqual,
);

export interface GetCardDefinitionFromColumnsArgs {
    screenId: string;
    columns: TableDecoratorProperties['columns'];
    isGreaterThanSmall: boolean;
    hiddenColumns?: string[];
    sortColumns?: (this: any, firstColumn: ColumnCompareType, secondColumn: ColumnCompareType) => number;
    mobileCard?: TableDecoratorProperties['mobileCard'];
}

export const getCardDefinitionFromColumns = ({
    screenId,
    columns,
    isGreaterThanSmall,
    hiddenColumns = [],
    sortColumns,
    mobileCard,
}: GetCardDefinitionFromColumnsArgs): CardDefinition => {
    let sortedColumns = [...(columns || [])];
    if (sortColumns) {
        sortedColumns = [...(columns || [])].sort((column1, column2) =>
            resolveCompareFunction(
                screenId,
                sortColumns,
                {
                    bind: convertDeepBindToPathNotNull(column1.properties.bind),
                    valueField: (column1.properties as NestedReferenceProperties).valueField as string,
                },
                {
                    bind: convertDeepBindToPathNotNull(column2.properties.bind),
                    valueField: (column2.properties as NestedReferenceProperties).valueField as string,
                },
            ),
        );
    }
    const columnBinds = sortedColumns.map(getNestedFieldElementId);
    const hiddenFields = calculateHiddenColumns(hiddenColumns ?? [], columnBinds) as string[];
    const visibleColumns = withoutNestedTechnical(sortedColumns).filter(
        f =>
            !includes(hiddenFields, f.properties.bind) &&
            !resolveByValue({
                fieldValue: null,
                propertyValue: f.properties.isHidden,
                skipHexFormat: true,
                rowValue: null,
                screenId,
            }) &&
            !(f.properties.isHiddenMobile && !isGreaterThanSmall),
    );

    return (
        mobileCard || {
            title: visibleColumns[0],
            titleRight: visibleColumns[1],
            line2: visibleColumns[2],
            line2Right: visibleColumns[3],
            line3: visibleColumns[4],
            line3Right: visibleColumns[5],
            line4: visibleColumns[6],
            line4Right: visibleColumns[7],
            line5: visibleColumns[8],
            line5Right: visibleColumns[9],
        }
    );
};

export const getTableViewColumnHidden = (views: Dict<TableUserSettings> = {}, level = 0): Dict<boolean> | undefined => {
    return views.$current?.content?.[level]?.columnHidden;
};

export function setGridContext<T = any>(gridApi: GridApi, contextProducer: (c: T) => void): void {
    const currentContext = gridApi.getGridOption('context') ?? {};
    const newContext = produce(currentContext, contextProducer);
    gridApi.setGridOption('context', newContext);
}

type TableContext = {
    headerClasses: Dict<boolean>;
    totalRowCount: number;
    isSelectAllEnabled: boolean;
};

export function setTableContext(gridApi: GridApi, contextProducer: (c: TableContext) => void): void {
    setGridContext<TableContext>(gridApi, contextProducer);
}

export function getGridApiContext<T = any>(gridApi: GridApi, defaultValue: T): T {
    const ctx = gridApi.getGridOption('context');
    return isEmpty(ctx) ? defaultValue : (ctx as T);
}

export function getTableContext(gridApi: GridApi): TableContext {
    return getGridApiContext<TableContext>(gridApi, {
        headerClasses: {},
        totalRowCount: 0,
        isSelectAllEnabled: false,
    });
}

type NestedGridContext = {
    parentId?: string;
    level?: number;
    parentApi?: GridApi;
    headerClasses: Dict<boolean>;
};

export function getNestedGridContext(gridApi: GridApi): NestedGridContext {
    return getGridApiContext<NestedGridContext>(gridApi, {
        parentId: undefined,
        level: undefined,
        parentApi: undefined,
        headerClasses: {},
    });
}

export function setNestedGridContext(gridApi: GridApi, contextProducer: (c: NestedGridContext) => void): void {
    setGridContext<NestedGridContext>(gridApi, contextProducer);
}

export const getTableViewFilter = (views: Dict<TableUserSettings> = {}, level = 0): Dict<any> => {
    const filters = views.$current?.content?.[level]?.filter || {};
    return objectKeys(filters).reduce((acc, curr) => {
        if (views.$current?.content?.[level]?.grouping?.key === curr) {
            acc[AUTO_COLUMN_ID] = filters[curr];
        } else {
            acc[curr] = filters[curr];
        }
        return acc;
    }, {} as any);
};

export const getTableViewGrouping = (views: Dict<TableUserSettings> = {}, level = 0): TableViewGrouping | undefined => {
    return views.$current?.content?.[level]?.grouping;
};

export const getTableViewOptionsMenuItem = (
    views: Dict<TableUserSettings> = {},
    level = 0,
): OptionsMenuItem | undefined => {
    return views.$current?.content?.[level]?.optionsMenuItem;
};

export const getTableViewSortOrder = (
    views: Dict<TableUserSettings> = {},
    level = 0,
): TableViewSortedColumn[] | undefined => {
    return views.$current?.content?.[level]?.sortOrder;
};

export const getTableViewColumnOrder = (views: Dict<TableUserSettings> = {}, level = 0): string[] | undefined => {
    return views.$current?.content?.[level]?.columnOrder;
};

export const getActiveOptionsMenu = (
    screenId: string,
    views: Dict<TableUserSettings> = {},
    optionsMenu?: ValueOrCallback<any, OptionsMenuItem[]>,
): OptionsMenuItem | undefined => {
    const resolvedOptionsMenu = resolveByValue({
        propertyValue: optionsMenu,
        rowValue: null,
        screenId,
        fieldValue: null,
        skipHexFormat: true,
    });

    if (!resolvedOptionsMenu) {
        return undefined;
    }

    const title = getTableViewOptionsMenuItem(views)?.title;
    if (!title) {
        return resolvedOptionsMenu[0];
    }

    return resolvedOptionsMenu.find((optionsMenuItem: OptionsMenuItem) => {
        return optionsMenuItem.title === title;
    });
};
