import * as tokens from '@sage/design-tokens/js/base/common';
import type * as VP from '@sage/visual-process-editor';
import { BatchRequest, ClientNode, edgesSelector, serializeToGraphQL } from '@sage/xtrem-client';
import type { DefaultPropertyType, FilterParameter } from '@sage/xtrem-ui-components';
import * as charts from './component/chart-types';
import * as containerProperties from './component/container/container-properties';
import { PageAction, containers, fields } from './component/control-objects';
import * as decoratorProperties from './component/decorator-properties';
import * as decorators from './component/decorators';
import * as fieldProperties from './component/field/field-properties';
import { RichTextEditorCapabilities } from './component/field/rich-text/rich-text-types';
import { StepSequenceStatus } from './component/field/step-sequence/step-sequence-types';
import { PropertyValueType } from './component/field/reference/reference-types';
import * as nestedFields from './component/nested-fields';
import * as nestedFieldExtensions from './component/nested-fields-extensions';
import * as nestedFieldOverrides from './component/nested-fields-overrides';
import type { ReadonlyFieldProperties } from './component/readonly-field-control-object';
import { SidebarFieldDefinition } from './component/table-sidebar/table-sidebar-types';
import type {
    FieldDecoratorProps,
    PageCategory,
    PartialCollectionValue,
    PartialCollectionValueWithIds,
} from './component/types';
import { ContainerKey, FieldKey } from './component/types';
import * as widgets from './dashboard/widgets';
import * as integration from './integration';
import * as plugin from './plugin';
import { GraphQLApi, ReadOnlyGraphQLApi, GraphQLMutationApi } from './service/graphql-api';
import {
    formatDateToCurrentLocale,
    formatNumberToCurrentLocale,
    localize,
    localizeEnumMember,
} from './service/i18n-service';
import { PageArticleItem, PageArticleLayout } from './service/layout-types';
import {
    fetchNodeDetails,
    fetchNodePackageName,
    mapNodeDetailsToTreeProperty,
    rawNodeDetailsToTreeProperty,
} from './service/node-information-service';
import { Page, PageFragment } from './service/page';
import { PageExtension } from './service/page-extension';
import { ValidationResult } from './service/screen-base-definition';
import { Sticker } from './service/sticker';
import type { ToastOptions, ToastType } from './service/toast-service';
import { ScreenBaseGenericType as PageNode, PageWithAccessToNodes } from './types';
import * as dialogs from './types/dialogs';
import { xtremConsole as console } from './utils/console';
import {
    NEW_PAGE,
    QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER,
    QUERY_PARAM_PRINTING_RECORD_ID,
    QUERY_PARAM_PRINTING_SOURCE_PAGE,
    QUERY_PARAM_PRINTING_NODE_TYPE,
    QUERY_PARAM_SELECTED_SECTION_ID,
    QUERY_PARAM_TUNNEL_SEGMENTS,
    SHOULD_REFRESH_DIALOG_RESULT,
} from './utils/constants';
import { menuSeparator } from './utils/action-menu-utils';
import type { QueryParameters } from './utils/types';
import { LabelFieldStyle } from './component/field/label/label-types';

export { DateFilterPeriodType } from '@sage/bms-dashboard';

const queryUtils = {
    BatchRequest,
    edgesSelector,
    serializeToGraphQL,
};

type PartialNode<T> = PartialCollectionValue<T>;
type PartialNodeWithId<T> = PartialCollectionValueWithIds<T>;

/* This is what we expose for the functional developers and for xtrem-studio */
export {
    charts,
    ClientNode,
    console,
    ContainerKey,
    containerProperties,
    containers,
    decoratorProperties,
    decorators,
    DefaultPropertyType,
    dialogs,
    fetchNodeDetails,
    fetchNodePackageName,
    FieldDecoratorProps,
    FieldKey,
    fieldProperties,
    fields,
    FilterParameter,
    formatDateToCurrentLocale,
    formatNumberToCurrentLocale,
    GraphQLApi,
    GraphQLMutationApi,
    integration,
    LabelFieldStyle,
    localize,
    localizeEnumMember,
    mapNodeDetailsToTreeProperty,
    menuSeparator,
    nestedFieldExtensions,
    nestedFieldOverrides,
    nestedFields,
    NEW_PAGE,
    Page,
    PageAction,
    PageArticleItem,
    PageArticleLayout,
    PageCategory,
    PageExtension,
    PageFragment,
    PageNode,
    PageWithAccessToNodes,
    PartialCollectionValue,
    PartialNode,
    PartialNodeWithId,
    PropertyValueType,
    plugin,
    QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER,
    QUERY_PARAM_PRINTING_NODE_TYPE,
    QUERY_PARAM_PRINTING_RECORD_ID,
    QUERY_PARAM_PRINTING_SOURCE_PAGE,
    QUERY_PARAM_SELECTED_SECTION_ID,
    QUERY_PARAM_TUNNEL_SEGMENTS,
    QueryParameters,
    queryUtils,
    rawNodeDetailsToTreeProperty,
    ReadonlyFieldProperties,
    ReadOnlyGraphQLApi,
    RichTextEditorCapabilities,
    SHOULD_REFRESH_DIALOG_RESULT,
    SidebarFieldDefinition,
    StepSequenceStatus,
    Sticker,
    ToastOptions,
    ToastType,
    tokens,
    ValidationResult,
    VP,
    widgets,
};
