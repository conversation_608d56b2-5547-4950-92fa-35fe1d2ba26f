import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../redux';
import { objectKeys, type Dashboard, type Dict, type Locale } from '@sage/xtrem-shared';
import DialogFullScreen from 'carbon-react/esm/components/dialog-full-screen';
import { getWidgetsFromDashboardItems } from '../service/dashboard-service';
import { DASHBOARD_SCREEN_ID } from '../utils/constants';
import localeText from './dashboard-strings';
import AsyncDragDropCanvasWrapper from './async-drag-drop-canvas-wrapper';
import { localize } from '../service/i18n-service';
import Button from 'carbon-react/esm/components/button';
import Form from 'carbon-react/esm/components/form';
import { DashboardTitleEditor } from './dashboard-title-editor';
import { openDirtyPageConfirmationDialog } from '../service/dirty-state-service';
import { showToast } from '../service/toast-service';
import { ConnectedDashboardSelectionTabs } from './dashboard-selection-tabs';
import { DashboardPlaceholder } from './dashboard-placeholder';
import NavigationPanelComponent from '../component/container/navigation-panel/navigation-panel-component';
import { getNavigationPanelState } from '../utils/state-utils';
import ToggleNavigationPanelButton from '../component/container/navigation-panel/toggle-navigation-panel-button';
import { useElementSize } from 'usehooks-ts';
import { AsyncConnectedWidgetEditorDialog } from './widget-editor/async-widget-editor-dialog';
import type { DashboardGroupState } from '../redux/state';
import type {
    DashboardBreakpoint,
    DashboardContextVariables,
    WidgetDefinitionWithLoadingState,
} from './dashboard-types';
import type { AbstractResponsiveWidget, DragDropCanvasProps, ResponsiveDragDropCanvasProps } from '@sage/bms-dashboard';
import {
    DASHBOARD_BREAKPOINTS,
    DASHBOARD_COLS,
    DASHBOARD_EDITOR_SIDEBAR_WIDTH,
    DASHBOARD_EDITOR_WITH_SIDEBAR_BREAKPOINTS,
    getWidgetsWithLoadingState,
} from './dashboard-utils';
import { isEqual, isNil } from 'lodash';

export interface DashboardEditorDialogExternalProps {
    contextVariables?: DashboardContextVariables;
    group: string;
}

export interface DashboardEditorDialogProps extends DashboardEditorDialogExternalProps {
    canEditDashboards: boolean;
    currentDashboardDefinition: Dashboard;
    dashboardDefinition: Dashboard;
    isDirty: boolean;
    isNavigationPanelOpened: boolean;
    isOpen?: boolean;
    isRedoDisabled: boolean;
    isUndoDisabled: boolean;
    loadingDashboards: boolean;
    locale: Locale;
    onClose: () => void;
    redoDashboardEditor: () => void;
    removeDashboardEditorWidget: (widgetId: string) => void;
    saveDashboardEditorState: () => Promise<void>;
    setNavigationPanelIsOpened: (newState: boolean) => void;
    undoDashboardEditor: () => void;
    updateDashboardEditorWidgetPositions: (
        widgets: AbstractResponsiveWidget[],
        breakpoint: DashboardBreakpoint,
    ) => void;
    updateDashboardTitleInEditor: (newTitle: string) => void;
    widgets: Dict<WidgetDefinitionWithLoadingState>;
}

export function DashboardEditorDialog({
    canEditDashboards,
    contextVariables,
    currentDashboardDefinition,
    dashboardDefinition,
    group,
    isDirty,
    isNavigationPanelOpened,
    isOpen,
    isRedoDisabled,
    isUndoDisabled,
    loadingDashboards,
    locale,
    onClose,
    redoDashboardEditor,
    removeDashboardEditorWidget,
    saveDashboardEditorState,
    setNavigationPanelIsOpened,
    undoDashboardEditor,
    updateDashboardEditorWidgetPositions,
    updateDashboardTitleInEditor,
    widgets,
}: DashboardEditorDialogProps): React.ReactElement {
    const [dashboardCanvasContainerRef, { width = 0 }] = useElementSize();
    const [breakpoint, setBreakpoint] = React.useState<DashboardBreakpoint | null>(null);

    const currentWidgets = React.useMemo(
        () =>
            isNil(breakpoint)
                ? []
                : getWidgetsFromDashboardItems({
                      dashboardId: String(dashboardDefinition._id),
                      group,
                      children: currentDashboardDefinition.children,
                      widgets,
                      isEditing: true,
                      onCloseCallback: removeDashboardEditorWidget,
                      canEditDashboards,
                      breakpoint,
                  }),
        [
            breakpoint,
            canEditDashboards,
            currentDashboardDefinition.children,
            dashboardDefinition._id,
            group,
            removeDashboardEditorWidget,
            widgets,
        ],
    );

    const onDataChange = React.useCallback<NonNullable<ResponsiveDragDropCanvasProps['onDataChange']>>(
        ({ widgets: updatedWidgets, breakpoint: b }): void => {
            if (!isEqual(currentWidgets, updatedWidgets)) {
                updateDashboardEditorWidgetPositions(updatedWidgets, b);
            }
        },
        [updateDashboardEditorWidgetPositions, currentWidgets],
    );

    const onDialogClose = React.useCallback((): void => {
        // If the dashboard is dirty, first the confirmation dialog needs to be displayed
        if (isDirty) {
            openDirtyPageConfirmationDialog(DASHBOARD_SCREEN_ID)
                .then(onClose)
                .catch(() => {
                    // Intentionally left empty, the user decided not to leave.
                });
        } else {
            onClose();
        }
    }, [onClose, isDirty]);

    const onChangeTabs = React.useCallback(async (): Promise<void> => {
        // If the dashboard is dirty, first the confirmation dialog needs to be displayed
        if (isDirty) {
            await openDirtyPageConfirmationDialog(DASHBOARD_SCREEN_ID);
        }
    }, [isDirty]);

    const onSave = React.useCallback(
        async (ev: React.MouseEvent<HTMLButtonElement>): Promise<void> => {
            ev.preventDefault();
            try {
                await saveDashboardEditorState();
                showToast(localize('@sage/xtrem-ui/dashboard-editor-saved-successfully', 'Dashboard saved.'), {
                    type: 'success',
                });
                onClose();
            } catch (err) {
                showToast(localize('@sage/xtrem-ui/dashboard-editor-save-error', 'Failed to save dashboard.'), {
                    type: 'error',
                });
            }
        },
        [saveDashboardEditorState, onClose],
    );

    const onBreakpointChange = React.useCallback((newBreakpoint: NonNullable<typeof breakpoint>) => {
        setBreakpoint(newBreakpoint);
    }, []);

    const args = React.useMemo(
        () =>
            ({
                type: 'responsive',
                isHeaderHidden: true,
                locale,
                isEditMode: true,
                onBreakpointChange,
                layout: {
                    cols: DASHBOARD_COLS,
                    breakpoints: isNavigationPanelOpened
                        ? DASHBOARD_EDITOR_WITH_SIDEBAR_BREAKPOINTS
                        : DASHBOARD_BREAKPOINTS,
                    rowHeight: 120,
                    width: isNavigationPanelOpened ? width - DASHBOARD_EDITOR_SIDEBAR_WIDTH : width,
                    isDraggable: true,
                },
                widgets: currentWidgets,
                onDataChange,
                stringLiterals: localeText(),
            }) satisfies DragDropCanvasProps,
        [currentWidgets, isNavigationPanelOpened, locale, onBreakpointChange, onDataChange, width],
    );

    return (
        <DialogFullScreen
            className="e-dashboard-editor-dialog"
            data-testid="e-dashboard-editor-dialog"
            title={localize('@sage/xtrem-ui/dashboard-editor-title', 'Dashboard editor')}
            open={!!isOpen}
            onCancel={onDialogClose}
        >
            <Form
                stickyFooter={true}
                leftSideButtons={
                    <Button data-testid="e-dashboard-editor-dialog-cancel" onClick={onDialogClose}>
                        {localize('@sage/xtrem-ui/dashboard-editor-cancel-edit', 'Cancel')}
                    </Button>
                }
                saveButton={
                    <Button
                        buttonType="primary"
                        data-testid="e-dashboard-editor-dialog-save"
                        disabled={!isDirty}
                        onClick={onSave}
                        type="submit"
                    >
                        {localize('@sage/xtrem-ui/dashboard-editor-save', 'Save')}
                    </Button>
                }
                onSubmit={ev => {
                    ev.preventDefault();
                }}
            >
                <div className="e-dashboard-editor">
                    {!isNavigationPanelOpened && (
                        <ToggleNavigationPanelButton
                            setNavigationPanelIsOpened={setNavigationPanelIsOpened}
                            isNavigationPanelOpened={isNavigationPanelOpened}
                        />
                    )}
                    {isNavigationPanelOpened && (
                        <NavigationPanelComponent screenId={DASHBOARD_SCREEN_ID} selectedRecordId="-1" />
                    )}
                    <div className="e-dashboard-editor-main">
                        <div className="e-dashboard-editor-tab-container">
                            <ConnectedDashboardSelectionTabs canChangeTabs={onChangeTabs} group={group} />
                        </div>
                        {loadingDashboards && <DashboardPlaceholder />}
                        {!loadingDashboards && (
                            <>
                                <div className="e-dashboard-editor-title-line">
                                    <DashboardTitleEditor
                                        title={currentDashboardDefinition.title}
                                        onChange={updateDashboardTitleInEditor}
                                    />
                                    <div className="e-dashboard-editor-history">
                                        <Button
                                            data-testid="e-dashboard-editor-undo"
                                            iconType="u_turn_left"
                                            buttonType="tertiary"
                                            onClick={undoDashboardEditor}
                                            disabled={isUndoDisabled}
                                        >
                                            {localize('@sage/xtrem-ui/dashboard-editor-undo', 'Undo')}
                                        </Button>
                                        <Button
                                            data-testid="e-dashboard-editor-redo"
                                            iconType="u_turn_right"
                                            buttonType="tertiary"
                                            onClick={redoDashboardEditor}
                                            disabled={isRedoDisabled}
                                        >
                                            {localize('@sage/xtrem-ui/dashboard-editor-redo', 'Redo')}
                                        </Button>
                                    </div>
                                </div>
                                <div
                                    className="e-dashboard-editor-body"
                                    data-testid="e-dashboard-editor-body"
                                    ref={dashboardCanvasContainerRef}
                                >
                                    <AsyncDragDropCanvasWrapper json={args} />
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </Form>
            {/** Here we lazy load the widget editor dialog, it is only loaded if the parent dialog is opened. */}
            {isOpen && <AsyncConnectedWidgetEditorDialog group={group} contextVariables={contextVariables} />}
        </DialogFullScreen>
    );
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: DashboardEditorDialogExternalProps,
): DashboardEditorDialogProps => {
    const dashboardGroup: DashboardGroupState = state.dashboard.dashboardGroups[props.group];
    const { dashboardEditor, dashboards, widgets } = dashboardGroup;

    const activeDashboard = objectKeys(dashboards).find(d => dashboards[d].isSelected);
    const navigationPanelState = getNavigationPanelState(DASHBOARD_SCREEN_ID, state);
    if (!activeDashboard) {
        throw new Error('The editor cannot be used if no active dashboard definition is loaded.');
    }

    return {
        ...props,
        canEditDashboards: state.dashboard?.canEditDashboards || false,
        currentDashboardDefinition: dashboardEditor.currentDashboardDefinition,
        dashboardDefinition: dashboards[activeDashboard],
        isDirty: dashboardEditor.isDirty,
        isNavigationPanelOpened: navigationPanelState?.isOpened || false,
        isOpen: dashboardEditor.isOpen,
        isRedoDisabled: dashboardEditor.currentHistoryIndex === dashboardEditor.history.length - 1,
        isUndoDisabled: dashboardEditor.currentHistoryIndex === 0,
        loadingDashboards: state.loading.loadingDashboards,
        locale: (state.applicationContext?.locale || 'en-US') as Locale,
        onClose: xtremRedux.actions.actionStub,
        redoDashboardEditor: xtremRedux.actions.actionStub,
        removeDashboardEditorWidget: xtremRedux.actions.actionStub,
        saveDashboardEditorState: xtremRedux.actions.actionStub,
        setNavigationPanelIsOpened: xtremRedux.actions.actionStub,
        undoDashboardEditor: xtremRedux.actions.actionStub,
        updateDashboardEditorWidgetPositions: xtremRedux.actions.actionStub,
        updateDashboardTitleInEditor: xtremRedux.actions.actionStub,
        widgets: getWidgetsWithLoadingState({ widgetState: state.loading.widgets, widgets: widgets ?? {} }),
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: DashboardEditorDialogExternalProps,
): Partial<DashboardEditorDialogProps> => {
    return {
        saveDashboardEditorState: (): Promise<void> =>
            dispatch(xtremRedux.actions.saveDashboardEditorState(props.group, props.contextVariables)),
        onClose: (): void => {
            dispatch(xtremRedux.actions.closeDashboardEditorDialog(props.group));
        },
        setNavigationPanelIsOpened: (isOpened: boolean): void =>
            dispatch(xtremRedux.actions.setNavigationPanelIsOpened(isOpened, DASHBOARD_SCREEN_ID)),
        undoDashboardEditor: (): void => {
            dispatch(xtremRedux.actions.undoDashboardEditor(props.group));
        },
        redoDashboardEditor: (): void => {
            dispatch(xtremRedux.actions.redoDashboardEditor(props.group));
        },
        removeDashboardEditorWidget: (widgetId: string): void => {
            dispatch(xtremRedux.actions.removeDashboardEditorWidget(widgetId, props.group));
        },
        updateDashboardEditorWidgetPositions: (
            widgets: AbstractResponsiveWidget[],
            breakpoint: DashboardBreakpoint,
        ): void => {
            dispatch(
                xtremRedux.actions.updateDashboardEditorWidgetPositions({
                    widgets,
                    group: props.group,
                    contextVariables: props.contextVariables,
                    breakpoint,
                }),
            );
        },
        updateDashboardTitleInEditor: (newTitle: string): void => {
            dispatch(xtremRedux.actions.updateDashboardTitleInEditor(newTitle, props.group));
        },
    };
};

export const ConnectedDashboardEditorDialog = connect(mapStateToProps, mapDispatchToProps)(DashboardEditorDialog);
