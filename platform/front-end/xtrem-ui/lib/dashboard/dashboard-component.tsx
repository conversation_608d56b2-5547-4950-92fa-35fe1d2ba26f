import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../redux';
import { objectKeys, type Dashboard, type Dict, type Locale } from '@sage/xtrem-shared';
import { getWidgetsFromDashboardItems } from '../service/dashboard-service';
import { DASHBOARD_SCREEN_ID } from '../utils/constants';
import Typography from 'carbon-react/esm/components/typography';
import localeText from './dashboard-strings';
import AsyncDragDropCanvasWrapper from './async-drag-drop-canvas-wrapper';
import Heading from 'carbon-react/esm/components/heading';
import Button from 'carbon-react/esm/components/button';
import { ActionPopover, ActionPopoverDivider, ActionPopoverItem } from 'carbon-react/esm/components/action-popover';
import { localize } from '../service/i18n-service';
import { ConnectedDashboardEditorDialog } from './dashboard-editor-dialog';
import { ConnectedDashboardSelectionTabs } from './dashboard-selection-tabs';
import { ConnectedDashboardCreateDialog } from './dashboard-create-dialog';
import { confirmationDialog } from '../service/dialog-service';
import { xtremConsole } from '../utils/console';
import { showToast } from '../service/toast-service';
import { DashboardPlaceholder } from './dashboard-placeholder';
import type { AbstractResponsiveWidget } from '@sage/bms-dashboard';
import type { DashboardGroupState } from '../redux/state';
import type {
    DashboardBreakpoint,
    DashboardContextVariables,
    OnBreakpointChange,
    WidgetDefinitionWithLoadingState,
} from './dashboard-types';
import { isEqual } from 'lodash';
import { DASHBOARD_BREAKPOINTS, DASHBOARD_COLS, getWidgetsWithLoadingState } from './dashboard-utils';
import Icon from 'carbon-react/esm/components/icon';
import * as tokens from '@sage/design-tokens/js/base/common';

export interface DashboardComponentExternalProps {
    group: string;
    dashboardContextVariables?: DashboardContextVariables;
    hasTabs?: boolean;
}

export interface DashboardComponentProps extends DashboardComponentExternalProps {
    canEditDashboards: boolean;
    cloneDashboard: (dashboardId: string) => Promise<void>;
    dashboardDefinition?: Dashboard;
    deleteDashboard: (dashboardId: string) => Promise<void>;
    fetchUserDashboardDefinition: () => void;
    globalLoading: boolean;
    hasInstalledPackagesLoaded: boolean;
    loadingDashboards: boolean;
    locale: Locale;
    openDashboardEditorDialog: () => void;
    refreshCurrentDashboard: () => void;
    widgets: Dict<WidgetDefinitionWithLoadingState>;
}

type DIALOG_MODE_OBJ = {
    CREATE_DASHBOARD: 'CREATE_DASHBOARD';
    EDIT_DASHBOARD: 'EDIT_DASHBOARD';
    NONE: 'NONE';
};

export type DIALOG_MODE = DIALOG_MODE_OBJ[keyof DIALOG_MODE_OBJ];

export interface DashboardComponentState {
    dialogMode: DIALOG_MODE;
    startedFetching: boolean;
    // We need to wait for the initial render to take place because otherwise the dashboard component will not calculate with the full page width
    verticalSpaceReady: boolean;
    breakpoint: DashboardBreakpoint;
}

export class DashboardComponent extends React.Component<DashboardComponentProps, DashboardComponentState> {
    constructor(props: DashboardComponentProps) {
        super(props);

        this.state = {
            dialogMode: 'NONE',
            startedFetching: this.props.dashboardDefinition !== undefined,
            verticalSpaceReady: false,
            breakpoint: 'md',
        };
    }

    componentDidMount(): void {
        this.fetchDashboards();
        setTimeout(() => {
            this.setState({ verticalSpaceReady: true });
        }, 100);
    }

    componentDidUpdate(prevProps: DashboardComponentProps): void {
        this.fetchDashboards();

        if (
            prevProps.group !== this.props.group ||
            !isEqual(prevProps.dashboardContextVariables, this.props.dashboardContextVariables)
        ) {
            this.props.refreshCurrentDashboard();
        }
    }

    private readonly fetchDashboards = (): void => {
        if (!this.props.dashboardDefinition && this.props.hasInstalledPackagesLoaded && !this.state.startedFetching) {
            this.setState({ startedFetching: true }, this.props.fetchUserDashboardDefinition);
        }
    };

    private readonly onCreateDialogOpen = (): void => this.setState({ dialogMode: 'CREATE_DASHBOARD' });

    private readonly onCreateDialogClose = (success = false): void => {
        this.setState({ dialogMode: 'NONE' });
        if (success) {
            this.props.openDashboardEditorDialog();
        }
    };

    private readonly onDeleteDashboard = async (): Promise<void> => {
        if (this.props.dashboardDefinition) {
            try {
                await confirmationDialog(
                    DASHBOARD_SCREEN_ID,
                    'warn',
                    localize('@sage/xtrem-ui/dashboard-delete-dashboard-dialog-title', 'Delete dashboard'),
                    localize(
                        '@sage/xtrem-ui/dashboard-delete-dashboard-dialog-message',
                        'Are you sure you want to delete this dashboard?',
                    ),
                );
                try {
                    await this.props.deleteDashboard(String(this.props.dashboardDefinition._id));
                    showToast(localize('@sage/xtrem-ui/dashboard-deleted', 'Dashboard deleted.'), {
                        type: 'success',
                    });
                } catch (err) {
                    xtremConsole.error(err);
                    showToast(
                        localize('@sage/xtrem-ui/dashboard-failed-to-delete', 'The dashboard could not be deleted.'),
                        { type: 'error' },
                    );
                }
            } catch {
                // Do nothing
            }
        }
    };

    private readonly onDuplicateDashboard = async (): Promise<void> => {
        if (this.props.dashboardDefinition) {
            try {
                await this.props.cloneDashboard(String(this.props.dashboardDefinition._id));
                showToast(localize('@sage/xtrem-ui/dashboard-duplicated', 'Dashboard duplicated.'), {
                    type: 'success',
                });
            } catch (err) {
                xtremConsole.error(err);
                showToast(
                    localize('@sage/xtrem-ui/dashboard-failed-to-duplicate', 'The dashboard could not be duplicated.'),
                    { type: 'error' },
                );
            }
        }
    };

    private readonly renderNoDashboards = (): React.ReactNode => {
        return (
            <div className="e-dashboard-empty" data-testid="e-dashboard-empty-no-dashboard">
                <Heading
                    data-testid="e-dashboard-empty-no-dashboard-title"
                    divider={false}
                    title={localize('@sage/xtrem-ui/no-dashboard-title', 'Drive your business')}
                />
                <div className="e-dashboard-empty-center-wrapper">
                    <img width={300} height={300} src="/images/dashboard-create.svg" alt="Create dashboard logo" />
                    <div className="e-dashboard-empty-right-wrapper">
                        <Heading
                            data-testid="e-dashboard-empty-no-dashboard-heading"
                            divider={false}
                            title={localize('@sage/xtrem-ui/no-dashboard-heading', 'This page has no dashboard.')}
                        />
                        <span
                            className="e-dashboard-empty-right-subtitle"
                            data-testid="e-dashboard-empty-no-dashboard-subtitle"
                        >
                            {localize('@sage/xtrem-ui/no-dashboard-subtitle', 'Create a dashboard to get started.')}
                        </span>
                        {this.props.canEditDashboards && (
                            <Button
                                data-testid="e-dashboard-create"
                                iconType="plus"
                                onClick={this.onCreateDialogOpen}
                                buttonType="primary"
                            >
                                {localize('@sage/xtrem-ui/dashboard-create-dashboard', 'Create dashboard')}
                            </Button>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    private readonly renderEmptyDashboard = (): React.ReactNode => {
        return (
            <div className="e-dashboard-empty" data-testid="e-dashboard-empty-no-widgets">
                <div className="e-dashboard-empty-center-wrapper">
                    <img width={300} height={300} src="/images/dashboard-create.svg" alt="Create dashboard logo" />
                    <div className="e-dashboard-empty-right-wrapper">
                        <Heading
                            data-testid="e-dashboard-empty-no-widgets-heading"
                            divider={false}
                            title={localize('@sage/xtrem-ui/dashboard-empty-heading', 'This dashboard is empty.')}
                        />
                        <span
                            className="e-dashboard-empty-right-subtitle"
                            data-testid="e-dashboard-empty-no-widgets-subtitle"
                        >
                            {localize(
                                '@sage/xtrem-ui/dashboard-empty-subtitle',
                                'Add a widget to customize your dashboard.',
                            )}
                        </span>
                        {this.props.canEditDashboards && (
                            <Button
                                data-testid="e-dashboard-create"
                                iconType="plus"
                                onClick={this.props.openDashboardEditorDialog}
                                buttonType="primary"
                            >
                                {localize('@sage/xtrem-ui/dashboard-add-widget', 'Add widget')}
                            </Button>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    private readonly renderActionButton = (): React.ReactNode => {
        return (
            <div data-component="action-popover-button" data-element="action-popover-button">
                <Icon
                    type="ellipsis_vertical"
                    tooltipMessage={localize('@sage/xtrem-ui/dashboard-more_options', 'More options')}
                    color={tokens.colorsUtilityMajor450}
                />
            </div>
        );
    };

    private readonly renderKebabMenu = (): React.ReactNode => {
        if (!this.props.dashboardDefinition) {
            return null;
        }
        const destructiveIconClassName: any = {
            className: 'e-action-popover-item-destructive',
        };

        return (
            <Typography variant="h1" mt={3} mr={3} ml={3} mb={0}>
                <span className="e-dashboard-title" data-testid="e-dashboard-title">
                    {this.props.dashboardDefinition.title}
                </span>
                {this.props.canEditDashboards && (
                    <span className="e-dashboard-title-menu">
                        <ActionPopover data-testid="e-dashboard-actions" renderButton={this.renderActionButton}>
                            <ActionPopoverItem
                                icon="edit"
                                onClick={this.props.openDashboardEditorDialog}
                                data-testid="e-dashboard-action-edit"
                            >
                                {localize('@sage/xtrem-ui/dashboard-edit', 'Edit')}
                            </ActionPopoverItem>
                            <ActionPopoverItem
                                icon="duplicate"
                                onClick={this.onDuplicateDashboard}
                                data-testid="e-dashboard-action-duplication"
                            >
                                {localize('@sage/xtrem-ui/dashboard-duplicate', 'Duplicate')}
                            </ActionPopoverItem>
                            <ActionPopoverItem
                                icon="plus_large"
                                onClick={this.onCreateDialogOpen}
                                data-testid="e-dashboard-action-create"
                            >
                                {localize('@sage/xtrem-ui/dashboard-create', 'Create')}
                            </ActionPopoverItem>
                            {!this.props.dashboardDefinition?.isTemplate && <ActionPopoverDivider />}
                            {!this.props.dashboardDefinition?.isTemplate && (
                                <ActionPopoverItem
                                    icon="delete"
                                    onClick={this.onDeleteDashboard}
                                    data-testid="e-dashboard-action-delete"
                                    {...destructiveIconClassName}
                                >
                                    {localize('@sage/xtrem-ui/dashboard-delete', 'Delete')}
                                </ActionPopoverItem>
                            )}
                        </ActionPopover>
                    </span>
                )}
            </Typography>
        );
    };

    private readonly getWidgets = (): AbstractResponsiveWidget[] => {
        return this.props.dashboardDefinition
            ? getWidgetsFromDashboardItems({
                  dashboardId: String(this.props.dashboardDefinition._id),
                  group: this.props.group,
                  children: this.props.dashboardDefinition.children,
                  widgets: this.props.widgets,
                  isEditing: undefined,
                  onCloseCallback: undefined,
                  canEditDashboards: this.props.canEditDashboards,
                  breakpoint: this.state.breakpoint,
              })
            : [];
    };

    private readonly onBreakpointChange: OnBreakpointChange = newBreakpoint => {
        this.setState({ breakpoint: newBreakpoint });
    };

    private readonly renderDashboard = (): React.ReactNode => {
        const widgets = this.getWidgets();
        const isLoading = !this.state.startedFetching || !this.state.verticalSpaceReady;
        const isEmptyDashboard =
            !isLoading &&
            !this.props.loadingDashboards &&
            !this.props.globalLoading &&
            this.state.verticalSpaceReady &&
            this.props.dashboardDefinition !== undefined &&
            widgets.length === 0;
        return (
            <>
                {!this.props.loadingDashboards && this.renderKebabMenu()}
                {!this.props.loadingDashboards &&
                    (isEmptyDashboard ? (
                        this.renderEmptyDashboard()
                    ) : (
                        <AsyncDragDropCanvasWrapper
                            json={{
                                onBreakpointChange: this.onBreakpointChange,
                                type: 'responsive',
                                isHeaderHidden: true,
                                isEditMode: false,
                                locale: this.props.locale,
                                layout: {
                                    cols: DASHBOARD_COLS,
                                    breakpoints: DASHBOARD_BREAKPOINTS,
                                    rowHeight: 120,
                                    width: 1200,
                                    isDraggable: false,
                                },
                                widgets,
                                stringLiterals: localeText(),
                            }}
                        />
                    ))}
                {this.props.loadingDashboards && <DashboardPlaceholder />}
                <ConnectedDashboardEditorDialog
                    group={this.props.group}
                    contextVariables={this.props.dashboardContextVariables}
                />
            </>
        );
    };

    render(): React.ReactNode {
        const isLoading = !this.state.startedFetching || !this.state.verticalSpaceReady;
        const hasNoDashboards =
            !isLoading &&
            !this.props.loadingDashboards &&
            !this.props.globalLoading &&
            this.state.verticalSpaceReady &&
            this.props.dashboardDefinition === undefined;

        return (
            <main className="e-dashboard">
                {this.props.hasTabs && (
                    <ConnectedDashboardSelectionTabs
                        onCreateDialogOpen={this.onCreateDialogOpen}
                        group={this.props.group}
                        contextVariables={this.props.dashboardContextVariables}
                    />
                )}
                {hasNoDashboards && this.renderNoDashboards()}
                {this.props.dashboardDefinition !== undefined &&
                    this.state.verticalSpaceReady &&
                    this.renderDashboard()}
                {this.props.canEditDashboards && (
                    <ConnectedDashboardCreateDialog
                        group={this.props.group}
                        isOpen={this.state.dialogMode === 'CREATE_DASHBOARD'}
                        onClose={this.onCreateDialogClose}
                        onNextClicked={this.onCreateDialogClose}
                        contextVariables={this.props.dashboardContextVariables}
                    />
                )}
            </main>
        );
    }
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: DashboardComponentExternalProps,
): DashboardComponentProps => {
    const dashboard: DashboardGroupState = state.dashboard.dashboardGroups[props.group];
    const { dashboards, widgets } = dashboard || {};

    const activeDashboard = objectKeys(dashboards || {}).find(d => dashboards[d].isSelected);
    return {
        ...props,
        canEditDashboards: state.dashboard.canEditDashboards,
        cloneDashboard: xtremRedux.actions.actionStub,
        dashboardDefinition: activeDashboard ? dashboards[activeDashboard] : undefined,
        deleteDashboard: xtremRedux.actions.actionStub,
        fetchUserDashboardDefinition: xtremRedux.actions.actionStub,
        globalLoading: state.loading.globalLoading,
        hasInstalledPackagesLoaded: !!state.applicationPackages,
        loadingDashboards: state.loading.loadingDashboards,
        locale: (state.applicationContext?.locale || 'en-US') as Locale,
        openDashboardEditorDialog: xtremRedux.actions.actionStub,
        refreshCurrentDashboard: xtremRedux.actions.actionStub,
        widgets: getWidgetsWithLoadingState({ widgetState: state.loading.widgets, widgets: widgets ?? {} }),
        hasTabs: (dashboard?.availableDashboards ?? []).length > 1,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: DashboardComponentExternalProps,
): Partial<DashboardComponentProps> => {
    return {
        fetchUserDashboardDefinition: (): void => {
            dispatch(xtremRedux.actions.fetchUserDashboardDefinition(props.group, props.dashboardContextVariables));
        },
        cloneDashboard: (dashboardId: string): Promise<void> =>
            dispatch(xtremRedux.actions.cloneDashboard(dashboardId, props.group, props.dashboardContextVariables)),
        deleteDashboard: (dashboardId: string): Promise<void> =>
            dispatch(xtremRedux.actions.deleteDashboard(dashboardId, props.group, props.dashboardContextVariables)),
        openDashboardEditorDialog: (): void => {
            dispatch(xtremRedux.actions.openDashboardEditorDialog(props.group, props.dashboardContextVariables));
        },
        refreshCurrentDashboard: (): void => {
            dispatch(xtremRedux.actions.refreshCurrentDashboard(props.group, props.dashboardContextVariables));
        },
    };
};

export const ConnectedDashboardComponent = connect(mapStateToProps, mapDispatchToProps)(DashboardComponent);
