import Button from 'carbon-react/esm/components/button';
import Form from 'carbon-react/esm/components/form';
import type { DialogSizes } from 'carbon-react/esm/components/dialog';
import CarbonDialog from 'carbon-react/esm/components/dialog';
import * as React from 'react';
import { localize } from '../service/i18n-service';
import { fetchDashboardTemplates } from '../service/dashboard-service';
import type { DashboardListItem } from '../redux/state';
import { resolveDetailedIcon } from '../utils/detailed-icons-utils';
import { connect } from 'react-redux';
import * as xtremRedux from '../redux';
import { showToast } from '../service/toast-service';
import { xtremConsole } from '../utils/console';
import { getMaxDialogSize } from '../utils/responsive-utils';
import { SelectionCard } from '@sage/xtrem-ui-components';
import type { DashboardContextVariables } from './dashboard-types';

export interface DashboardCreateDialogExternalProps {
    // eslint-disable-next-line react/no-unused-prop-types
    group: string;
    isOpen: boolean;
    // eslint-disable-next-line react/no-unused-prop-types
    contextVariables?: DashboardContextVariables;
    onClose: () => void;
    onNextClicked: (success: boolean) => void;
}

export interface DashboardCreateDialogProps extends DashboardCreateDialogExternalProps {
    cloneDashboard: (dashboardId: string) => Promise<void>;
    createDashboard: () => Promise<void>;
    dialogSize: DialogSizes;
}

export function DashboardCreateDialog({
    cloneDashboard,
    createDashboard,
    dialogSize,
    group,
    isOpen,
    onClose,
    onNextClicked,
}: DashboardCreateDialogProps): React.ReactElement {
    const onNextButtonClicked = async (ev: React.MouseEvent<HTMLButtonElement>): Promise<void> => {
        ev.preventDefault();
        if (selectedTemplate) {
            try {
                if (selectedTemplate._id === '_blank') {
                    await createDashboard();
                } else {
                    await cloneDashboard(selectedTemplate._id);
                }
                onNextClicked(true);
            } catch (err) {
                showToast(localize('@sage/xtrem-ui/dashboard-failed-to-create', 'Failed to create a new dashboard'), {
                    type: 'error',
                });
            }
        }
    };

    const [templateList, setTemplateList] = React.useState<DashboardListItem[]>([]);
    const [selectedTemplate, setSelectedTemplate] = React.useState<DashboardListItem | null>(null);

    React.useEffect(() => {
        (async (): Promise<void> => {
            const dashboardTemplates = await fetchDashboardTemplates(group);

            if (!Array.isArray(dashboardTemplates)) {
                xtremConsole.warn('Unable to set dashboard templates.', dashboardTemplates);
                return;
            }

            setTemplateList(dashboardTemplates);
        })();
    }, [group]);

    React.useEffect(() => {
        setSelectedTemplate(null);
    }, [isOpen]);

    const blankTemplate: DashboardListItem = {
        _id: '_blank',
        title: localize('@sage/xtrem-ui/dashboard-editor-create-dialog-blank-title', 'Blank template'),
        description: localize(
            '@sage/xtrem-ui/dashboard-editor-create-dialog-blank-description',
            'Start with a blank template and add the widgets you need',
        ),
        listIcon: '/images/detailed-icons/90x90_blank-template_green-on-transparent_icon.svg',
    };

    return (
        <CarbonDialog
            className="e-dashboard-create-dialog"
            data-testid="e-dashboard-create-dialog"
            open={isOpen}
            onCancel={(): void => onClose()}
            title={localize('@sage/xtrem-ui/dashboard-editor-create-dialog-title', 'Select a dashboard template.')}
            size={dialogSize}
        >
            <Form
                stickyFooter={true}
                leftSideButtons={
                    <Button data-testid="e-dashboard-dialog-create-cancel-button" onClick={(): void => onClose()}>
                        Cancel
                    </Button>
                }
                saveButton={
                    <Button
                        data-testid="e-dashboard-dialog-create-next-button"
                        disabled={!selectedTemplate}
                        buttonType="primary"
                        type="submit"
                        onClick={onNextButtonClicked}
                    >
                        Next
                    </Button>
                }
            >
                <div data-testid="e-dashboard-dialog-create-container" className="e-dashboard-dialog-create-container">
                    <div className="e-dashboard-welcome-image">
                        <img src="/images/welcome_dashboard.svg" alt="Welcome dashboard" />
                    </div>
                    <div className="e-dashboard-template-selection-list">
                        <span
                            className="e-dashboard-create-dialog-description"
                            data-testid="e-dashboard-create-dialog-description"
                        >
                            {localize(
                                '@sage/xtrem-ui/dashboard-editor-create-dialog-description',
                                'Select a template to get started or build your own dashboard. You can customize any dashboard by adding or removing widgets.',
                            )}
                        </span>
                        <div data-testid="e-dashboard-dialog-blank-template" className="e-dashboard-blank-template">
                            <SelectionCard
                                _id={blankTemplate._id}
                                title={blankTemplate.title}
                                description={blankTemplate.description}
                                icon={blankTemplate.listIcon}
                                onClick={(): void =>
                                    selectedTemplate?._id === blankTemplate._id
                                        ? setSelectedTemplate(null)
                                        : setSelectedTemplate(blankTemplate)
                                }
                                isSelected={selectedTemplate?._id === blankTemplate._id}
                            />
                        </div>
                        <div data-testid="e-dashboard-template-list" className="e-dashboard-template-list">
                            {templateList.map(template => (
                                <SelectionCard
                                    key={template._id}
                                    _id={template._id}
                                    title={template.title}
                                    description={template.description}
                                    icon={template.listIcon ? resolveDetailedIcon(template.listIcon) : ''}
                                    onClick={(): void =>
                                        selectedTemplate?._id === template._id
                                            ? setSelectedTemplate(null)
                                            : setSelectedTemplate(template)
                                    }
                                    isSelected={selectedTemplate?._id === template._id}
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </Form>
        </CarbonDialog>
    );
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: DashboardCreateDialogExternalProps,
): DashboardCreateDialogProps => ({
    ...props,
    dialogSize: getMaxDialogSize(state.browser),
    cloneDashboard: xtremRedux.actions.actionStub,
    createDashboard: xtremRedux.actions.actionStub,
});

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: DashboardCreateDialogExternalProps,
): Partial<DashboardCreateDialogProps> => {
    return {
        cloneDashboard: (dashboardId: string): Promise<void> =>
            dispatch(xtremRedux.actions.cloneDashboard(dashboardId, props.group, props.contextVariables)),
        createDashboard: (): Promise<void> =>
            dispatch(xtremRedux.actions.createEmptyDashboard(props.group, props.contextVariables)),
    };
};

export const ConnectedDashboardCreateDialog = connect(mapStateToProps, mapDispatchToProps)(DashboardCreateDialog);
