import GlobalStyle from 'carbon-react/esm/style/global-style';
import * as React from 'react';
import { Provider } from 'react-redux';
import * as xtremRedux from '../redux';
import I18nProvider from 'carbon-react/esm/components/i18n-provider';
import { carbonLocale } from '../utils/carbon-locale';
import type { ApplicationContext } from '../redux/state';
import { ConnectedDashboardComponent } from './dashboard-component';

export interface DashboardRootComponentProps {
    applicationContext: ApplicationContext;
    group: string;
}

export interface DashboardRootComponentState {
    error?: Error;
}

export class DashboardRootComponent extends React.Component<DashboardRootComponentProps, DashboardRootComponentState> {
    async componentDidMount(): Promise<void> {
        const thunkDispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
        await thunkDispatch(xtremRedux.actions.setApplicationContext(this.props.applicationContext));
    }

    componentDidCatch(error: Error): void {
        this.setState({ error });
    }

    render(): React.ReactNode {
        if (this.state?.error) {
            return (
                <div>
                    <h2>Failed to load dashboards.</h2>
                    <h4>{this.state.error.message}</h4>
                    <pre>{this.state.error.stack}</pre>
                </div>
            );
        }

        return (
            <Provider store={xtremRedux.getStore()}>
                <GlobalStyle />
                <I18nProvider locale={carbonLocale(this.props.applicationContext?.locale ?? 'en-US')}>
                    <ConnectedDashboardComponent group={this.props.group} />
                </I18nProvider>
            </Provider>
        );
    }
}
