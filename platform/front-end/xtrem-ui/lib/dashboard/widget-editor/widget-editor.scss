.e-widget-editor-indicator-container {
    height: 50px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    margin-bottom: 40px;
}

.e-widget-editor-label {
    font-family: var(--fontFamiliesDefault);
    font-weight: var(--fontWeights500);
    font-size: 16px;
    line-height: 20px;
    color: var(--colorsUtilityYin090);
    margin-bottom: 20px;
}

.e-widget-editor-step-subtitle {
    display: block;
    padding-top: 24px;
    padding-bottom: 8px;
    font-size: 14px;
    font-weight: var(--fontWeights500);
}

.e-widget-editor-section {
    padding-top: 16px;

    [data-component="filterable-select"][disabled] label::after {
        color: var(--colorsUtilityYin030);
    }

    & [data-component="date-range"] {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: var(--spacing050);

        & [role="presentation"] {
            width: 120px;
            min-height: 32px;

            & input {
                padding-left: 8px;

                &~span {
                    width: var(--sizing375)
                }
            }
        }
    }

    --fieldSpacing: 0;
}

.e-widget-editor-layout-screen {
    flex: 1;
    display: flex;
    flex-direction: column;

    & .e-widget-editor-layout-section {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        & #dashboardWrapper {
            flex: 1;
        }
    }
}

.e-widget-editor {
    height: 550px;
    display: flex;
    flex-direction: column;

    .e-data-step-tree {

        .e-data-step-tree-container {
            border: 1px solid #afb0b0;
            overflow-y: scroll;
            height: 336px;
            border-radius: var(--borderRadius100);
        }

        .e-data-step-tree-wrapper {
            background: repeating-linear-gradient(to bottom, var(--colorsUtilityMajor010) 0px, var(--colorsUtilityMajor010) 24px, #FFFFFF 24px, #FFFFFF 48px);
        }
    }

    .e-layout-step {
        display: flex;
        flex-direction: row;
        flex: 1;



        .e-layout-separator {

            height: 1px;
            margin-top: 24px;
            margin-bottom: 24px;
            background: var(--colorsActionMinorYin090);
        }

        .e-layout-settings-container {
            width: 300px;
            margin-right: 20px;
            padding-left: var(--spacing075);
            padding-right: var(--spacing075);
            margin-top: 8px;
            overflow-y: auto;
            max-height: 400px;

            >div:last-child {
                padding-bottom: 24px;
            }
        }

        .e-layout-preview-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .e-layout-preview-dashboard-wrapper {
            border: 1px solid var(--colorsActionMinor200);
            border-radius: var(--borderRadius100);
            flex: 1;
            --fieldSpacing: 24px;

        }

        .e-layout-preview-preview-label {
            font-family: var(--fontFamiliesDefault);
            font-style: normal;
            font-weight: var(--fontWeights500);
            font-size: 14px;
            line-height: 150%;
            margin-bottom: 8px;
        }
    }
}