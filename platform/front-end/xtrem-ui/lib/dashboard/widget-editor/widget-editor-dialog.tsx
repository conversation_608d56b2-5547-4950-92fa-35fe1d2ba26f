import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../redux';
import { objectKeys, type Dict, type Locale } from '@sage/xtrem-shared';
import Dialog from 'carbon-react/esm/components/dialog';
import { DASHBOARD_SCREEN_ID } from '../../utils/constants';
import { localize } from '../../service/i18n-service';
import Button from 'carbon-react/esm/components/button';
import Form from 'carbon-react/esm/components/form';
import { openDirtyPageConfirmationDialog } from '../../service/dirty-state-service';
import { ConnectedBasicsStep } from './basics-step';
import type { UserWidgetDefinition } from '../../redux/state';
import { StepSequence, StepSequenceItem } from 'carbon-react/esm/components/step-sequence';
import type { WidgetEditorStepProps, WidgetEditorStepType } from './widget-editor-utils';
import { getWidgetEditorSteps, stepsForWidgetTypes, getStepStatus } from './widget-editor-utils';
import { DataStep } from './data-step';
import { ConnectedContentStep } from './content-step';
import { FilterStep } from './filters-step';
import { ConnectedSortingStep } from './sorting-step';
import { LayoutStep } from './layout-step';
import { isEqual } from 'lodash';
import type { DashboardContextVariables } from '../dashboard-types';
import { flushSync } from 'react-dom';

export interface WidgetEditorDialogExternalProps {
    group: string;
    contextVariables?: DashboardContextVariables;
}

export interface WidgetEditorDialogProps extends WidgetEditorDialogExternalProps {
    isDirty: boolean;
    isNewWidget: boolean;
    isOpen: boolean;
    locale: Locale;
    nodeNames: Dict<string>;
    onClose: () => void;
    onFinish: () => void;
    onUpdateWidgetSettings: (widgetDefinition: UserWidgetDefinition) => void;
    onWidgetDefinitionUpdated: (widgetDefinition: UserWidgetDefinition) => void;
    widgetDefinition: UserWidgetDefinition;
}

export function WidgetEditorDialog({
    contextVariables,
    group,
    isDirty,
    isNewWidget,
    isOpen,
    locale,
    nodeNames,
    onClose,
    onFinish,
    onWidgetDefinitionUpdated,
    widgetDefinition,
}: WidgetEditorDialogProps): React.ReactElement {
    const widgetEditorSteps = React.useMemo(() => getWidgetEditorSteps(), []);

    const onWidgetDefinitionUpdatedCallback = React.useCallback(
        (newWidgetDefinition: UserWidgetDefinition) => {
            if (!isEqual(newWidgetDefinition, widgetDefinition)) {
                onWidgetDefinitionUpdated(newWidgetDefinition);
            }
        },
        [onWidgetDefinitionUpdated, widgetDefinition],
    );

    const [activeStep, setActiveStep] = React.useState<WidgetEditorStepType>('BASIC_INFO');
    const [availableSteps, setAvailableSteps] = React.useState<WidgetEditorStepType[]>([]);

    const isLastStep = React.useMemo(
        () => availableSteps.length > 0 && availableSteps[availableSteps.length - 1] === activeStep,
        [activeStep, availableSteps],
    );

    React.useEffect(() => {
        if (widgetDefinition.type) {
            setAvailableSteps(stepsForWidgetTypes[widgetDefinition.type]);
        } else {
            setAvailableSteps([]);
        }
    }, [widgetDefinition.type, setAvailableSteps]);

    React.useEffect(() => {
        // Reset active step when the dialog is closed
        setActiveStep('BASIC_INFO');
    }, [isOpen]);

    const onDialogClose = React.useCallback((): void => {
        // If the dashboard is dirty, first the confirmation dialog needs to be displayed
        if (isDirty) {
            openDirtyPageConfirmationDialog(DASHBOARD_SCREEN_ID)
                .then(onClose)
                .catch(() => {
                    // Intentionally left empty, the user decided not to leave.
                });
        } else {
            onClose();
        }
    }, [onClose, isDirty]);

    const onNextClick = React.useCallback(
        (event: React.MouseEvent<HTMLButtonElement>): void => {
            event.preventDefault();
            if (!isLastStep) {
                const activeStepPosition = availableSteps.indexOf(activeStep);
                const newActiveStep = availableSteps[activeStepPosition + 1];
                if (availableSteps[activeStepPosition] === 'DATA') {
                    const checkedItemKeys = objectKeys(widgetDefinition.selectedProperties ?? {});
                    flushSync(() => {
                        onWidgetDefinitionUpdatedCallback({
                            ...widgetDefinition,
                            aggregations: widgetDefinition.aggregations
                                ? widgetDefinition.aggregations.filter(a => checkedItemKeys.includes(a.id ?? a.path))
                                : undefined,
                            xAxis:
                                widgetDefinition.xAxis &&
                                checkedItemKeys.includes(
                                    widgetDefinition.xAxis.property.id ?? widgetDefinition.xAxis.property.path,
                                )
                                    ? widgetDefinition.xAxis
                                    : undefined,
                            groupBy:
                                widgetDefinition.groupBy &&
                                checkedItemKeys.includes(
                                    widgetDefinition.groupBy.property.id ?? widgetDefinition.groupBy.property.path,
                                )
                                    ? widgetDefinition.groupBy
                                    : undefined,
                            columns: widgetDefinition.columns
                                ? widgetDefinition.columns.filter(c => checkedItemKeys.includes(c.id ?? c.path))
                                : undefined,
                            filters: widgetDefinition.filters
                                ? widgetDefinition.filters.filter(f => checkedItemKeys.includes(f.id ?? f.path))
                                : undefined,
                            orderBy: widgetDefinition.orderBy
                                ? widgetDefinition.orderBy.filter(o => checkedItemKeys.includes(o.id ?? o.path))
                                : undefined,
                        });
                    });
                }
                setActiveStep(newActiveStep);
            } else {
                onFinish();
            }
        },
        [
            activeStep,
            setActiveStep,
            availableSteps,
            isLastStep,
            onFinish,
            widgetDefinition,
            onWidgetDefinitionUpdatedCallback,
        ],
    );

    const onPreviousClick = React.useCallback((): void => {
        const activeStepPosition = availableSteps.indexOf(activeStep);
        setActiveStep(availableSteps[activeStepPosition - 1]);
    }, [activeStep, setActiveStep, availableSteps]);

    const shouldRenderPreviousButton = React.useMemo(
        () => availableSteps.length > 0 && activeStep !== availableSteps[0],
        [activeStep, availableSteps],
    );

    const stepProperties = React.useMemo<WidgetEditorStepProps>(
        () => ({
            group,
            locale,
            nodeNames,
            onWidgetDefinitionUpdated: onWidgetDefinitionUpdatedCallback,
            stepIndex: availableSteps.length > 0 ? availableSteps.indexOf(activeStep) + 1 : 1,
            widgetDefinition,
            contextVariables,
        }),
        [
            activeStep,
            availableSteps,
            contextVariables,
            group,
            locale,
            nodeNames,
            onWidgetDefinitionUpdatedCallback,
            widgetDefinition,
        ],
    );

    const getCtaLabel = React.useCallback((): string => {
        if (!isLastStep) {
            return localize('@sage/xtrem-ui/wizard-next', 'Next');
        }

        if (isNewWidget) {
            return localize('@sage/xtrem-ui/widget-editor-add', 'Add');
        }

        return localize('@sage/xtrem-ui/widget-editor-update', 'Update');
    }, [isLastStep, isNewWidget]);

    const widgetTitle = React.useMemo(
        () =>
            isNewWidget
                ? localize('@sage/xtrem-ui/widget-editor-title-new', 'New widget')
                : localize('@sage/xtrem-ui/widget-editor-title-edit', 'Edit widget'),
        [isNewWidget],
    );

    return (
        <Dialog
            className="e-widget-editor-dialog"
            data-testid="e-widget-editor-dialog"
            title={widgetTitle}
            open={!!isOpen}
            size="extra-large"
            onCancel={onDialogClose}
        >
            <Form
                stickyFooter={true}
                leftSideButtons={
                    shouldRenderPreviousButton ? (
                        <Button data-testid="e-widget-editor-dialog-previous" onClick={onPreviousClick}>
                            {localize('@sage/xtrem-ui/wizard-previous', 'Previous')}
                        </Button>
                    ) : undefined
                }
                rightSideButtons={
                    <>
                        <Button data-testid="e-widget-editor-dialog-cancel" onClick={onDialogClose} mr="16px">
                            {localize('@sage/xtrem-ui/widget-editor-cancel-edit', 'Cancel')}
                        </Button>
                        <Button
                            buttonType="primary"
                            data-testid={`e-widget-editor-dialog-${isLastStep ? 'add' : 'next'}`}
                            disabled={!activeStep || !widgetEditorSteps[activeStep]?.isValid(widgetDefinition)}
                            onClick={onNextClick}
                        >
                            {getCtaLabel()}
                        </Button>
                    </>
                }
            >
                <div className="e-widget-editor">
                    <div
                        className="e-widget-editor-indicator-container"
                        data-testid="e-widget-editor-indicator-container"
                    >
                        {widgetDefinition.type && (
                            <StepSequence mt={0} mb={0}>
                                {availableSteps.map(s => (
                                    <StepSequenceItem
                                        key={s}
                                        indicator={String(availableSteps.indexOf(s) + 1)}
                                        status={getStepStatus(availableSteps, activeStep, s)}
                                    >
                                        {widgetEditorSteps[s].title}
                                    </StepSequenceItem>
                                ))}
                            </StepSequence>
                        )}
                    </div>
                    {activeStep === 'BASIC_INFO' && <ConnectedBasicsStep {...stepProperties} />}
                    {activeStep === 'DATA' && <DataStep {...stepProperties} />}
                    {activeStep === 'CONTENT' && <ConnectedContentStep {...stepProperties} />}
                    {activeStep === 'FILTERS' && <FilterStep {...stepProperties} />}
                    {activeStep === 'SORTING' && <ConnectedSortingStep {...stepProperties} />}
                    {activeStep === 'LAYOUT' && <LayoutStep {...stepProperties} />}
                </div>
            </Form>
        </Dialog>
    );
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: WidgetEditorDialogExternalProps,
): WidgetEditorDialogProps => {
    const { widgetEditor } = state.dashboard.dashboardGroups[props.group];
    return {
        ...props,
        isDirty: widgetEditor.isDirty,
        isNewWidget: !widgetEditor.widgetId,
        isOpen: widgetEditor.isOpen,
        locale: (state.applicationContext?.locale || 'en-US') as Locale,
        nodeNames: state.dashboard.nodeNames,
        onClose: xtremRedux.actions.actionStub,
        onFinish: xtremRedux.actions.actionStub,
        onUpdateWidgetSettings: xtremRedux.actions.actionStub,
        onWidgetDefinitionUpdated: xtremRedux.actions.actionStub,
        widgetDefinition: widgetEditor.widgetDefinition,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: WidgetEditorDialogExternalProps,
): Partial<WidgetEditorDialogProps> => {
    return {
        onWidgetDefinitionUpdated: (widgetDefinition: UserWidgetDefinition): void => {
            dispatch(xtremRedux.actions.updateUserWidgetDefinition(widgetDefinition, props.group));
        },
        onClose: (): void => {
            dispatch(xtremRedux.actions.closeWidgetEditorDialog(props.group));
        },
        onFinish: (): void => {
            dispatch(xtremRedux.actions.finishEditingUserDefinedWidget(props.group));
        },
    };
};

export const ConnectedWidgetEditorDialog = connect(mapStateToProps, mapDispatchToProps)(WidgetEditorDialog);
