import type { UserWidgetDefinition, UserWidgetDefinitionAction } from '../../redux/state';
import * as React from 'react';
import type { PickKeys } from 'ts-essentials';
import { set, cloneDeep } from 'lodash';
import { Checkbox } from 'carbon-react/esm/components/checkbox';
import { localize } from '../../service/i18n-service';
import Textbox from 'carbon-react/esm/components/textbox';
import { queryPagesByNodeType } from '../../service/metadata-service';
import { FilterableSelect, OptionRow } from 'carbon-react/esm/components/select';

export interface WidgetActionConfigurationProps {
    actionKey: PickKeys<Required<UserWidgetDefinition>, UserWidgetDefinitionAction>;
    actionName: string;
    helperText: string;
    onWidgetDefinitionUpdated: (newData: UserWidgetDefinition) => void;
    widgetDefinition: UserWidgetDefinition;
}

export function WidgetActionConfiguration({
    actionKey,
    actionName,
    helperText,
    onWidgetDefinitionUpdated,
    widgetDefinition,
}: WidgetActionConfigurationProps): React.ReactElement {
    const [title, setTitle] = React.useState<string>(widgetDefinition[actionKey]?.title || '');
    const [pages, setPages] = React.useState<Array<{ key: string; title: string }>>([]);
    const [titleValidationMessage, setTitleValidationMessage] = React.useState<string | undefined>(undefined);

    React.useEffect(() => {
        if (widgetDefinition.node) {
            queryPagesByNodeType(widgetDefinition.node).then(setPages);
        }
    }, [widgetDefinition.node, setPages]);

    const onActionEnabledUpdated = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>) => {
            const isChecked = ev.target.checked;
            const newWidgetDefinition = cloneDeep(widgetDefinition);
            set(newWidgetDefinition, `${actionKey}.isEnabled`, isChecked);
            if (isChecked && !newWidgetDefinition[actionKey]?.title) {
                set(newWidgetDefinition, `${actionKey}.title`, actionName);
                setTitle(actionName);
            }
            onWidgetDefinitionUpdated(newWidgetDefinition);
            if (!isChecked) {
                setTitleValidationMessage(undefined);
            }
        },
        [onWidgetDefinitionUpdated, actionKey, actionName, widgetDefinition, setTitleValidationMessage],
    );

    const onTitleChanged = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>) => {
            setTitle(ev.target.value);
            if (!ev.target.value) {
                setTitleValidationMessage(
                    localize('@sage/xtrem-ui/widget-editor-action-label-mandatory', 'You need to enter a title.'),
                );
            } else {
                setTitleValidationMessage(undefined);
            }
        },
        [setTitle, setTitleValidationMessage],
    );

    const onTitleBlur = React.useCallback(() => {
        onWidgetDefinitionUpdated({ ...widgetDefinition, [actionKey]: { ...widgetDefinition[actionKey], title } });
    }, [title, widgetDefinition, actionKey, onWidgetDefinitionUpdated]);

    const onPageChanged = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>) => {
            onWidgetDefinitionUpdated({
                ...widgetDefinition,
                [actionKey]: { ...widgetDefinition[actionKey], page: ev.target.value },
            });
        },
        [onWidgetDefinitionUpdated, actionKey, widgetDefinition],
    );

    return (
        <div>
            <Checkbox
                mt="16px"
                label={actionName}
                data-testid={`e-widget-editor-layout-isEnabled-${actionKey}`}
                name={`action-enabled-${actionKey}`}
                id={`action-enabled-${actionKey}`}
                checked={widgetDefinition[actionKey]?.isEnabled}
                onChange={onActionEnabledUpdated}
                fieldHelp={helperText}
            />
            <Textbox
                mt="16px"
                name={`action-title-${actionKey}`}
                id={`action-title-${actionKey}`}
                data-testid={`e-widget-editor-layout-title-${actionKey}`}
                label={localize('@sage/xtrem-ui/widget-editor-action-page-title', 'Title')}
                value={title}
                disabled={!widgetDefinition[actionKey]?.isEnabled}
                onChange={onTitleChanged}
                error={titleValidationMessage}
                onBlur={onTitleBlur}
                validationOnLabel={true}
                required={true}
            />
            <FilterableSelect
                mt="16px"
                mb="24px"
                data-testid={`e-widget-editor-layout-page-${actionKey}`}
                required={true}
                tableHeader={
                    <tr>
                        <th>{localize('@sage/xtrem-ui/widget-editor-action-page-title', 'Title')}</th>
                        <th>{localize('@sage/xtrem-ui/widget-editor-action-page-path', 'Path')}</th>
                    </tr>
                }
                multiColumn={true}
                openOnFocus={true}
                validationOnLabel={true}
                onChange={onPageChanged}
                name={`action-page-${actionKey}`}
                id={`action-page-${actionKey}`}
                disabled={!widgetDefinition[actionKey]?.isEnabled}
                label={localize('@sage/xtrem-ui/widget-editor-action-goes-to', 'Goes to')}
                value={widgetDefinition[actionKey]?.page || ''}
            >
                {pages.map(k => (
                    <OptionRow text={k.title} value={k.key} key={k.key} id={k.key}>
                        <td
                            width="50%"
                            style={{
                                overflow: 'hidden',
                                whiteSpace: 'pre-line',
                                maxWidth: 0,
                            }}
                        >
                            {k.title}
                        </td>
                        <td
                            width="50%"
                            style={{
                                overflow: 'hidden',
                                whiteSpace: 'pre-line',
                                maxWidth: 0,
                                textAlign: 'end',
                            }}
                        >
                            {k.key}
                        </td>
                    </OptionRow>
                ))}
            </FilterableSelect>
        </div>
    );
}
