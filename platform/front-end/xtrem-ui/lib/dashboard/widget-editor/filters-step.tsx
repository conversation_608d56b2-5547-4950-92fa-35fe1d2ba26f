import type { FilterProperty, LocalizeLocale } from '@sage/xtrem-shared';
import { FilterTableComponent, GridColumn, GridRow } from '@sage/xtrem-ui-components';
import Typography from 'carbon-react/esm/components/typography';
import * as React from 'react';
import { localize, localizeEnumMember } from '../../service/i18n-service';
import { carbonLocale } from '../../utils/carbon-locale';
import type { WidgetEditorStepProps } from './widget-editor-utils';
import { isEqual } from 'lodash';

export function FilterStep({
    stepIndex,
    widgetDefinition,
    onWidgetDefinitionUpdated,
    locale,
    nodeNames,
}: WidgetEditorStepProps): React.ReactElement {
    const { selectedProperties } = widgetDefinition;

    const onFilterTableUpdate = React.useCallback(
        (filters: Array<FilterProperty> = []) => {
            const currentFilters = widgetDefinition.filters || [];
            if (!isEqual(currentFilters, filters)) {
                onWidgetDefinitionUpdated({ ...widgetDefinition, filters });
            }
        },
        [onWidgetDefinitionUpdated, widgetDefinition],
    );

    return (
        <div>
            <Typography variant="h2" data-testid="e-widget-editor-step-title">
                {localize('@sage/xtrem-ui/widget-editor-filter-step-title', '{{stepIndex}}. Add your filters', {
                    stepIndex,
                })}
            </Typography>
            <div className="e-widget-editor-section">
                <GridRow columns={8} gutter={24} margin={0} verticalMargin={0}>
                    <GridColumn columnSpan={8}>
                        <FilterTableComponent
                            mode="table"
                            onChange={onFilterTableUpdate}
                            carbonLocale={carbonLocale}
                            locale={locale as LocalizeLocale}
                            localize={localize}
                            localizeEnumMember={localizeEnumMember}
                            node={widgetDefinition.node}
                            nodeNames={nodeNames}
                            selectedProperties={selectedProperties}
                            value={widgetDefinition.filters || []}
                        />
                    </GridColumn>
                </GridRow>
            </div>
        </div>
    );
}
