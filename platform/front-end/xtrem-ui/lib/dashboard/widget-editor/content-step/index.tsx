import Typography from 'carbon-react/esm/components/typography';
import * as React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import { localize } from '../../../service/i18n-service';
import type { WidgetEditorStepProps } from '../widget-editor-utils';
import { ChartWidgetContent } from './chart-widget-content';
import type { ContentStepProps } from './content-utils';
import { IndicatorWidgetContent } from './indicator-tile-widget-content';
import { TableWidgetContent } from './table-widget-content';

export function ContentStep({
    browserIs,
    nodeNames,
    onWidgetDefinitionUpdated,
    stepIndex,
    widgetDefinition,
}: ContentStepProps): React.ReactElement {
    return (
        <div>
            <Typography variant="h2" data-testid="e-widget-editor-step-title">
                {localize('@sage/xtrem-ui/widget-editor-content-step-title', '{{stepIndex}}. Add your content', {
                    stepIndex,
                })}
            </Typography>
            <div className="e-widget-editor-section">
                {widgetDefinition.type === 'INDICATOR_TILE' && (
                    <IndicatorWidgetContent
                        browserIs={browserIs}
                        nodeNames={nodeNames}
                        onWidgetDefinitionUpdated={onWidgetDefinitionUpdated}
                        widgetDefinition={widgetDefinition}
                    />
                )}
                {widgetDefinition.type === 'TABLE' && (
                    <TableWidgetContent
                        browserIs={browserIs}
                        nodeNames={nodeNames}
                        onWidgetDefinitionUpdated={onWidgetDefinitionUpdated}
                        widgetDefinition={widgetDefinition}
                    />
                )}
                {(widgetDefinition.type === 'BAR_CHART' || widgetDefinition.type === 'LINE_CHART') && (
                    <ChartWidgetContent
                        browserIs={browserIs}
                        nodeNames={nodeNames}
                        onWidgetDefinitionUpdated={onWidgetDefinitionUpdated}
                        widgetDefinition={widgetDefinition}
                    />
                )}
            </div>
        </div>
    );
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: WidgetEditorStepProps): ContentStepProps => {
    return {
        ...props,
        browserIs: state.browser.is,
    };
};

export const ConnectedContentStep = connect(mapStateToProps)(ContentStep);
