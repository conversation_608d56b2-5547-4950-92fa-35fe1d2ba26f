import { FilterableSelect, Option, OptionRow } from 'carbon-react/esm/components/select';
import * as React from 'react';
import { aggregatableGraphqlTypes } from '../../../component/types';
import { GridColumn, GridRow } from '@sage/xtrem-ui-components';
import { localize } from '../../../service/i18n-service';
import { getGutterSize } from '../../../utils/responsive-utils';
import type { UserWidgetDefinition } from '../../../redux/state';
import { get, includes, memoize } from 'lodash';
import type { ContentStepProps } from './content-utils';
import { PropertyTableHeader, getPropertyParentNode } from '../widget-editor-utils';
import Numeric from 'carbon-react/esm/components/number';
import Textbox from 'carbon-react/esm/components/textbox';
import type { STRING_AGGREGATIONS } from '@sage/xtrem-shared';
import { Aggregations, aggregationsGraphqlMapping, numericFields } from '@sage/xtrem-shared';

type IndicatorWidgetContentState = {
    divisor?: string;
    groupingMethod?: (typeof STRING_AGGREGATIONS)[number];
    groupingMethods: Aggregations[];
    groupingProperty?: NonNullable<UserWidgetDefinition['selectedProperties']>[number];
    groupingProperties: NonNullable<UserWidgetDefinition['selectedProperties']>[number][];
};

type IndicatorWidgetContentAction =
    | {
          type: 'SET_GROUPING_METHOD';
          groupingMethod: IndicatorWidgetContentState['groupingMethod'];
      }
    | {
          type: 'SET_DIVISOR';
          divisor: IndicatorWidgetContentState['divisor'];
      }
    | {
          type: 'SET_GROUPING_PROPERTY';
          groupingProperty: IndicatorWidgetContentState['groupingProperty'];
      };

function indicatorWidgetContentReducer(
    state: IndicatorWidgetContentState,
    action: IndicatorWidgetContentAction,
): IndicatorWidgetContentState {
    switch (action.type) {
        case 'SET_GROUPING_METHOD':
            return {
                ...state,
                groupingMethod: action.groupingMethod,
            };
        case 'SET_DIVISOR':
            return {
                ...state,
                divisor: action.divisor,
            };
        case 'SET_GROUPING_PROPERTY':
            if (action.groupingProperty === state.groupingProperty) {
                return state;
            }
            if (
                action.groupingProperty === undefined ||
                action.groupingProperty.data.type === undefined ||
                !includes(aggregatableGraphqlTypes, action.groupingProperty.data.type)
            ) {
                return {
                    ...state,
                    groupingMethods: [],
                    groupingMethod: undefined,
                    groupingProperty: undefined,
                    divisor: '1',
                };
            }

            const groupingMethods = get(aggregationsGraphqlMapping, action.groupingProperty.data.type) ?? [];
            return {
                ...state,
                groupingProperty: action.groupingProperty,
                groupingMethods,
                groupingMethod: groupingMethods.length === 1 ? groupingMethods[0] : undefined,
                divisor: '1',
            };
        default:
            return state;
    }
}

export const aggregationTranslations = memoize(
    (): Record<Aggregations, string> => ({
        [Aggregations.min]: localize('@sage/xtrem-ui/minimum', 'Minimum'),
        [Aggregations.max]: localize('@sage/xtrem-ui/maximum', 'Maximum'),
        [Aggregations.sum]: localize('@sage/xtrem-ui/sum', 'Sum'),
        [Aggregations.avg]: localize('@sage/xtrem-ui/average', 'Average'),
        [Aggregations.distinctCount]: localize('@sage/xtrem-ui/distinct-count', 'Distinct count'),
    }),
);

export function IndicatorWidgetContent({
    browserIs,
    onWidgetDefinitionUpdated,
    widgetDefinition,
    nodeNames,
}: {
    browserIs: ContentStepProps['browserIs'];
    onWidgetDefinitionUpdated: ContentStepProps['onWidgetDefinitionUpdated'];
    widgetDefinition: ContentStepProps['widgetDefinition'];
    nodeNames: ContentStepProps['nodeNames'];
}): React.ReactElement {
    const [state, dispatch] = React.useReducer(indicatorWidgetContentReducer, {
        groupingMethod: widgetDefinition.groupBy?.method,
        groupingMethods:
            widgetDefinition.groupBy?.property && widgetDefinition.groupBy.property.data.type
                ? (get(aggregationsGraphqlMapping, widgetDefinition.groupBy.property.data.type) ?? [])
                : [],
        groupingProperties: Object.values(widgetDefinition.selectedProperties ?? {}),
        groupingProperty: widgetDefinition.groupBy?.property,
        divisor: widgetDefinition.groupBy?.divisor ? String(widgetDefinition.groupBy.divisor) : '1',
    });

    React.useEffect(() => {
        if (
            state.groupingMethod !== undefined &&
            state.groupingProperty !== undefined &&
            state.divisor !== undefined &&
            !Number.isNaN(Number(state.divisor))
        ) {
            onWidgetDefinitionUpdated({
                ...widgetDefinition,
                groupBy: {
                    method: state.groupingMethod,
                    property: state.groupingProperty,
                    divisor: Number(state.divisor),
                },
            });
        } else {
            onWidgetDefinitionUpdated({ ...widgetDefinition, groupBy: undefined });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [state.groupingMethod, state.groupingProperty, state.divisor]);

    const onGroupingMethodChanged = React.useCallback((evt: React.ChangeEvent<HTMLInputElement>) => {
        dispatch({
            type: 'SET_GROUPING_METHOD',
            groupingMethod: (evt.target.value || undefined) as Aggregations | undefined,
        });
    }, []);

    const onDivisorChanged = React.useCallback((evt: React.ChangeEvent<HTMLInputElement>) => {
        dispatch({
            type: 'SET_DIVISOR',
            divisor: evt.target.value,
        });
    }, []);

    const divisorError = React.useMemo(() => {
        const value = Number(state.divisor);
        if (Number.isNaN(value)) {
            return localize('@sage/xtrem-ui/must-be-a-number', 'You need to enter a number');
        }
        if (value <= 0) {
            return localize('@sage/xtrem-ui/must-be-greater-than-zero', 'This value needs to be greater than 0.');
        }
        return undefined;
    }, [state.divisor]);

    const onGroupingPropertyChanged = React.useCallback(
        ({
            target: { value: groupingProperty },
        }: {
            target: { value: NonNullable<ContentStepProps['widgetDefinition']['selectedProperties']>[number] };
        }) => {
            dispatch({
                type: 'SET_GROUPING_PROPERTY',
                groupingProperty: groupingProperty || undefined,
            });
        },
        [],
    );

    const gridGutter = getGutterSize(browserIs);

    const canBeAggregated = (canFilter = false, canSort = false): boolean => canFilter && canSort;

    return (
        <GridRow columns={8} gutter={gridGutter} margin={0} verticalMargin={0}>
            <GridColumn columnSpan={4}>
                <FilterableSelect
                    required={true}
                    multiColumn={true}
                    tableHeader={<PropertyTableHeader />}
                    openOnFocus={true}
                    data-testid="e-widget-editor-grouping-property"
                    label={localize('@sage/xtrem-ui/widget-editor-grouping-property', 'Grouping property')}
                    // @ts-expect-error "onChange" is actually triggered with { target: { value: Property } }
                    onChange={onGroupingPropertyChanged}
                    // @ts-expect-error value is of type Property
                    value={state.groupingProperty ?? {}}
                >
                    {state.groupingProperties
                        .filter(
                            p =>
                                p.data.type &&
                                includes(aggregatableGraphqlTypes, p.data.type) &&
                                canBeAggregated(p.data.canFilter, p.data.canSort),
                        )
                        .map(p => (
                            // @ts-expect-error value is of type Property
                            <OptionRow text={p.label} value={p} key={p.id}>
                                <td
                                    width="50%"
                                    style={{
                                        overflow: 'hidden',
                                        whiteSpace: 'pre-line',
                                        maxWidth: 0,
                                    }}
                                >
                                    {p.label}
                                </td>
                                <td
                                    width="50%"
                                    style={{
                                        overflow: 'hidden',
                                        whiteSpace: 'pre-line',
                                        maxWidth: 0,
                                        textAlign: 'end',
                                    }}
                                >
                                    {getPropertyParentNode({ nodeNames, property: p, widgetDefinition })}
                                </td>
                            </OptionRow>
                        ))}
                </FilterableSelect>
            </GridColumn>
            <GridColumn columnSpan={4}>
                {state.groupingMethods.length > 1 && (
                    <FilterableSelect
                        required={true}
                        openOnFocus={true}
                        data-testid="e-widget-editor-grouping-method"
                        label={localize('@sage/xtrem-ui/widget-editor-grouping-method', 'Grouping method')}
                        onChange={onGroupingMethodChanged}
                        value={state.groupingMethod ?? ''}
                        disabled={state.groupingProperty == null}
                    >
                        {state.groupingMethods.map(aggregationMethod => (
                            <Option
                                text={aggregationTranslations()[aggregationMethod]}
                                value={aggregationMethod}
                                key={aggregationMethod}
                            />
                        ))}
                    </FilterableSelect>
                )}
                {state.groupingMethods.length <= 1 && (
                    <Textbox
                        data-testid="e-widget-editor-grouping-method"
                        required={true}
                        disabled={state.groupingProperty == null}
                        readOnly={true}
                        label={localize('@sage/xtrem-ui/widget-editor-grouping-method', 'Grouping method')}
                        value={aggregationTranslations()[state.groupingMethods[0]]}
                    />
                )}
            </GridColumn>
            {state.groupingProperty?.data?.type && includes(numericFields, state.groupingProperty.data.type) && (
                <GridColumn columnSpan={4}>
                    <Numeric
                        data-testid="e-widget-editor-content-divisor"
                        label={localize('@sage/xtrem-ui/divisor', 'Divisor')}
                        inputMode="numeric"
                        tooltipPosition="top"
                        onChange={onDivisorChanged}
                        value={state.divisor ?? ''}
                        disabled={state.groupingProperty == null}
                        error={divisorError}
                        fieldHelp={localize(
                            '@sage/xtrem-ui/divisor-helper-text',
                            'Reduce large numbers by entering a divisor.',
                        )}
                    />
                </GridColumn>
            )}
        </GridRow>
    );
}
