/* eslint-disable react/no-unstable-nested-components */
import {
    type Dict,
    numericFields,
    type Property,
    Aggregations,
    aggregationsGraphqlMapping,
    objectKeys,
} from '@sage/xtrem-shared';
import Box from 'carbon-react/esm/components/box';
import { FilterableSelect, Option, OptionRow } from 'carbon-react/esm/components/select';
import Textbox from 'carbon-react/esm/components/textbox';
import Typography from 'carbon-react/esm/components/typography';
import { get, includes, isEmpty, isEqual, isNil, set, sortBy, upperFirst } from 'lodash';
import * as React from 'react';
import type { FlatTableProps } from '@sage/xtrem-ui-components';
import { GridColumn, GridRow, FlatTable } from '@sage/xtrem-ui-components';
import type { AggregationProperty, DateAggregation, UserWidgetDefinition } from '../../../redux/state';
import { localize } from '../../../service/i18n-service';
import { getGutterSize } from '../../../utils/responsive-utils';
import { DEFAULT_CHART_COLORS, PropertyTableHeader, getPropertyParentNode } from '../widget-editor-utils';
import type { ContentAction, ContentStepProps } from './content-utils';
import { aggregationTranslations } from './indicator-tile-widget-content';
import { SplitBox } from '../../widgets/widget-utils';
import { GraphQLTypes } from '../../../types';

type ChartCols = [
    { id: 'property'; type?: Property },
    { id: 'title'; type?: string },
    { id: 'decimalDigits'; type?: number },
    { id: 'divisor'; type?: string },
    { id: 'groupingMethod'; type?: Aggregations },
    { id: 'path'; type?: string },
    { id: 'labelPath'; type?: string },
];
type Validations<T extends { id: string }[]> = Record<string, Record<T[number]['id'], string>>;
type ChartWidgetValidations = Validations<ChartCols>;
type ChartWidgetGrid = FlatTableProps<
    ChartCols,
    { validations: ChartWidgetValidations; tableSelectableProps: Property[]; numericTypes: typeof numericFields }
>;

type WidgetContentState<FT extends FlatTableProps<ChartCols, any>> = {
    addButtonText: NonNullable<FT>['addButtonText'];
    canDrag: NonNullable<FT>['canDrag'];
    onRowDrag: NonNullable<FT>['onRowDrag'];
    onRowRemoved: NonNullable<FT>['onRowRemoved'];
    onRowAdded: NonNullable<FT>['onRowAdded'];
    columns: NonNullable<FT>['columns'];
    data: NonNullable<FT>['data'];
    initialData: NonNullable<FT>['data'];
    counter: number;
    validations: ChartWidgetValidations;
};

type OnChartWidgetCellChange = <P extends ChartWidgetGrid['columns'][number]['id']>(args: {
    columnId: P;
    rowId: string;
    value: ChartWidgetGrid['data'][number][P] | string;
    rowData: ChartWidgetGrid['data'][number];
}) => void;

type ChartWidgetChanges = Parameters<OnChartWidgetCellChange>[0];

type ChartState = WidgetContentState<ChartWidgetGrid> & {
    xAxisGroupBy?: DateAggregation;
    initialxAxisGroupBy?: DateAggregation;
    groupingMethods: Aggregations[];
    xAxisProperty?: NonNullable<UserWidgetDefinition['xAxis']>['property'];
    initialxAxisProperty?: NonNullable<UserWidgetDefinition['xAxis']>['property'];
    xDecimalDigitsError?: string;
    xDecimalDigits?: NonNullable<UserWidgetDefinition['xAxis']>['decimalDigits'];
    initialxDecimalDigits?: NonNullable<UserWidgetDefinition['xAxis']>['decimalDigits'];
    xDecimalDigitsValue: string;
    groupingProperties: NonNullable<UserWidgetDefinition['selectedProperties']>[number][];
};
type ChartActions =
    | ContentAction<ChartWidgetGrid, ChartWidgetChanges>
    | {
          type: 'SET_X_AXIS_PROPERTY';
          value?: Property;
      }
    | {
          type: 'SET_X_DECIMAL_DIGITS';
          value: string;
      }
    | {
          type: 'SET_X_AXIS_GROUP_BY';
          value?: DateAggregation;
      };

function chartWidgetReducer(state: ChartState, action: ChartActions): ChartState {
    switch (action.type) {
        case 'ROW_ADDED':
            const newRow: ChartWidgetGrid['data'][number] = {
                _id: String(state.counter + 1),
                title: undefined,
                labelPath: undefined,
                path: undefined,
                property: undefined,
                decimalDigits: undefined,
                divisor: undefined,
                groupingMethod: undefined,
            };
            return {
                ...state,
                counter: state.counter + 1,
                data: [...state.data, newRow],
            };
        case 'ROW_REMOVED':
            return {
                ...state,
                data: state.data.filter(element => element._id !== action.row._id),
            };
        case 'ROW_DRAGGED':
            const orderedRows = sortBy(state.data, item => {
                return action.ids.indexOf(item._id);
            });
            return {
                ...state,
                data: orderedRows,
            };
        case 'CELL_CHANGED':
            const {
                changes: { rowId, columnId, value, rowData },
                selectedProperties,
            } = action;

            let validationMessage: string | undefined;
            if (
                rowData.property?.data?.type &&
                (columnId === 'divisor' || columnId === 'decimalDigits') &&
                includes(numericFields, rowData.property.data.type)
            ) {
                const parsedNumber = Number.parseInt(String(value), 10);
                if (Number.isNaN(parsedNumber)) {
                    validationMessage = localize('@sage/xtrem-ui/invalid-number', 'Invalid number');
                } else if (columnId === 'decimalDigits' && (parsedNumber < 0 || parsedNumber > 4)) {
                    validationMessage = localize(
                        '@sage/xtrem-ui/must-be-between-zero-and-four',
                        'This value needs to be between 0 and 4.',
                    );
                } else if (columnId === 'divisor' && parsedNumber <= 0) {
                    validationMessage = localize(
                        '@sage/xtrem-ui/must-be-greater-than-zero',
                        'This value needs to be greater than 0.',
                    );
                }
            }

            const data = state.data.reduce<ChartWidgetGrid['data']>((acc, curr) => {
                if (curr._id === rowId && !isEqual(get(curr, columnId), value)) {
                    // set actual value
                    set(curr, columnId, value || undefined);
                    // set type and path for given property
                    if (curr.property) {
                        const prop = selectedProperties?.[curr.property.id];
                        curr.path = prop?.id;
                        curr.labelPath = prop?.labelPath;
                    }

                    if (columnId === 'property') {
                        curr.groupingMethod = undefined;
                    }

                    // set aggregation method in case it cannot be chosen
                    if (
                        curr.property?.data?.type &&
                        (!includes(numericFields, curr.property?.data?.type) ||
                            curr.property?.data?.type === GraphQLTypes.IntReference)
                    ) {
                        curr.groupingMethod = Aggregations.distinctCount;
                    }

                    // set default grouping method (sum) for numeric fields
                    if (
                        !curr.groupingMethod &&
                        curr.property?.data?.type &&
                        includes(numericFields, curr.property?.data?.type)
                    ) {
                        const availableGroupingMethods = getAvailableGroupingMethods(state.data, rowData);
                        curr.groupingMethod = availableGroupingMethods.includes(Aggregations.sum)
                            ? Aggregations.sum
                            : availableGroupingMethods[0];
                    }

                    if (columnId === 'property') {
                        // always set default title upon property change
                        curr.title = curr.property?.label;
                        // always reset divisor upon property change
                        curr.divisor =
                            curr.property?.data?.type && includes(numericFields, curr.property.data.type)
                                ? '1'
                                : undefined;
                    }
                }

                acc.push(curr);
                return acc;
            }, []);
            const changedRow = data.find(d => d._id === rowData._id);
            const validationResets = objectKeys(changedRow ?? {}).reduce((acc, key) => {
                if (changedRow?.[key] === undefined) {
                    set(acc, key, undefined);
                }
                return acc;
            }, {});
            const validations = {
                ...state.validations,
                [rowData._id]: {
                    ...state.validations?.[rowData._id],
                    [columnId]: validationMessage,
                    ...validationResets,
                },
            };
            return {
                ...state,
                data,
                validations,
            };
        case 'SET_X_AXIS_PROPERTY':
            const shouldSetDecimalDigitsToTwo =
                (action.value?.data?.type === 'Decimal' || action.value?.data?.type === 'Float') &&
                action.value !== state.xAxisProperty;
            const shouldSetDecimalDigitsToZero =
                (action.value?.data?.type === 'Int' || action.value?.data?.type === 'IntReference') &&
                action.value !== state.xAxisProperty;
            let xDecimalDigits = state.xDecimalDigits;
            if (shouldSetDecimalDigitsToTwo) {
                xDecimalDigits = 2;
            } else if (shouldSetDecimalDigitsToZero) {
                xDecimalDigits = 0;
            }
            let xDecimalDigitsValue = state.xDecimalDigitsValue;
            if (shouldSetDecimalDigitsToTwo) {
                xDecimalDigitsValue = '2';
            } else if (shouldSetDecimalDigitsToZero) {
                xDecimalDigitsValue = '0';
            }
            return {
                ...state,
                xAxisProperty: action.value,
                xDecimalDigits,
                xDecimalDigitsValue,
            };
        case 'SET_X_DECIMAL_DIGITS':
            if (action.value && action.value.match(/^-?\d+$/)) {
                const num = Number(action.value);
                if (num < 0 || num > 4) {
                    return {
                        ...state,
                        xDecimalDigitsValue: action.value,
                        xDecimalDigitsError: localize(
                            '@sage/xtrem-ui/must-be-between-zero-and-four',
                            'This value needs to be between 0 and 4.',
                        ),
                    };
                }
                return {
                    ...state,
                    xDecimalDigitsValue: action.value,
                    xDecimalDigits: num,
                    xDecimalDigitsError: undefined,
                };
            }
            return {
                ...state,
                xDecimalDigitsValue: action.value,
                xDecimalDigitsError: localize('@sage/xtrem-ui/must-be-a-number', 'You need to enter a number'),
            };
        case 'SET_X_AXIS_GROUP_BY':
            return {
                ...state,
                xAxisGroupBy: action.value,
            };
        default:
            return state;
    }
}

function getAvailableGroupingMethods<FT extends FlatTableProps<ChartCols, any>>(
    allData: NonNullable<FT>['data'],
    rowData: ChartWidgetGrid['data'][number],
): Aggregations[] {
    const groupingMethods = rowData.property?.data?.type
        ? (get(aggregationsGraphqlMapping, rowData.property?.data?.type) ?? [])
        : [];
    return groupingMethods.filter(
        (p: Aggregations) =>
            !allData
                .filter(
                    item =>
                        item.property &&
                        item.property.id === rowData.property?.id &&
                        item.groupingMethod &&
                        item._id !== rowData._id,
                )
                .map(item => item.groupingMethod)
                .includes(p),
    );
}

export function ChartWidgetContent({
    browserIs,
    onWidgetDefinitionUpdated,
    widgetDefinition,
    nodeNames,
}: {
    browserIs: ContentStepProps['browserIs'];
    nodeNames: Dict<string>;
    onWidgetDefinitionUpdated: ContentStepProps['onWidgetDefinitionUpdated'];
    widgetDefinition: ContentStepProps['widgetDefinition'];
}): React.ReactElement {
    const onCellChange = React.useCallback<OnChartWidgetCellChange>(
        changes => {
            dispatch({ type: 'CELL_CHANGED', changes, selectedProperties: widgetDefinition.selectedProperties });
        },
        [widgetDefinition.selectedProperties],
    );

    // Exclude prop used on the X axis and those than cannot be sorted or filtered
    const tableSelectableProps = React.useMemo(
        () =>
            Object.values(widgetDefinition.selectedProperties ?? {}).filter(
                p => p.data.type && p.id !== widgetDefinition.xAxis?.property.id && p.data.canSort && p.data.canFilter,
            ),
        [widgetDefinition.selectedProperties, widgetDefinition.xAxis?.property.id],
    );

    const DECIMAL_DIGITS = React.useMemo(
        () => localize('@sage/xtrem-ui/widget-editor-content-formatting', 'Decimal places'),
        [],
    );

    const columns = React.useMemo<ChartWidgetGrid['columns']>(() => {
        return [
            {
                id: 'property',
                header: { name: localize('@sage/xtrem-ui/property', 'Property'), width: 250 },

                cellRenderer: ({
                    rowData,
                    data: allData,
                    extraData: { tableSelectableProps: availableYProps },
                }): React.ReactElement => {
                    return (
                        <FilterableSelect
                            multiColumn={true}
                            tableHeader={<PropertyTableHeader />}
                            openOnFocus={true}
                            data-testid={`e-widget-editor-content-property-${rowData._id}`}
                            // @ts-expect-error "onChange" is actually triggered with { target: { value: Property } }
                            onChange={({ target: { value } }: { target: { value: Property } }): void => {
                                onCellChange({ columnId: 'property', rowId: rowData._id, value, rowData });
                            }}
                            placeholder={localize('@sage/xtrem-ui/select-property', 'Select property...')}
                            size="small"
                            // @ts-expect-error value is of type Property
                            value={rowData.property ?? {}}
                        >
                            {availableYProps
                                .filter(
                                    p =>
                                        allData.filter(item => item.property && item._id === rowData._id).length <=
                                        (get(aggregationsGraphqlMapping, p.data.type ?? '') ?? []).length,
                                )
                                .map(p => {
                                    return (
                                        // @ts-expect-error value is of type Property
                                        <OptionRow text={p.label} value={p} key={p.id}>
                                            <td
                                                width="50%"
                                                style={{
                                                    overflow: 'hidden',
                                                    whiteSpace: 'pre-line',
                                                    maxWidth: 0,
                                                }}
                                            >
                                                {p.label}
                                            </td>
                                            <td
                                                width="50%"
                                                style={{
                                                    overflow: 'hidden',
                                                    whiteSpace: 'pre-line',
                                                    maxWidth: 0,
                                                    textAlign: 'end',
                                                }}
                                            >
                                                {getPropertyParentNode({ nodeNames, property: p, widgetDefinition })}
                                            </td>
                                        </OptionRow>
                                    );
                                })}
                        </FilterableSelect>
                    );
                },
            },
            {
                id: 'title',
                header: { name: localize('@sage/xtrem-ui/Label', 'Label'), width: 150 },
                cellRenderer: ({ rowData, extraData: { validations: errors } }): React.ReactElement => {
                    return (
                        <Textbox
                            error={errors[rowData._id]?.title}
                            disabled={!rowData.property}
                            inputMode="text"
                            tooltipPosition="top"
                            data-testid={`e-widget-editor-content-label-${rowData._id}`}
                            onChange={({ target: { value } }): void => {
                                onCellChange({
                                    columnId: 'title',
                                    rowId: rowData._id,
                                    value,
                                    rowData,
                                });
                            }}
                            placeholder={localize('@sage/xtrem-ui/Label', 'Label')}
                            size="small"
                            value={rowData.title ?? ''}
                        />
                    );
                },
            },
            {
                id: 'decimalDigits',
                header: {
                    name: localize('@sage/xtrem-ui/widget-editor-content-formatting', 'Decimal places'),
                    width: 130,
                },
                cellRenderer: ({ rowData, extraData: { validations: errors } }): React.ReactElement | null => {
                    if (rowData.property?.data?.type && !includes(numericFields, rowData.property.data.type)) {
                        return null;
                    }
                    return (
                        <Textbox
                            error={errors[rowData._id]?.decimalDigits}
                            inputMode="numeric"
                            tooltipPosition="top"
                            data-testid={`e-widget-editor-content-formatting-${rowData._id}`}
                            onChange={({ target: { value } }): void => {
                                onCellChange({
                                    columnId: 'decimalDigits',
                                    rowId: rowData._id,
                                    value,
                                    rowData,
                                });
                            }}
                            placeholder={DECIMAL_DIGITS}
                            size="small"
                            value={rowData.decimalDigits ?? ''}
                        />
                    );
                },
            },
            {
                id: 'divisor',
                header: { name: localize('@sage/xtrem-ui/divisor', 'Divisor'), width: 130 },
                cellRenderer: ({ rowData, extraData: { validations: errors } }): React.ReactElement | null => {
                    if (rowData.property?.data?.type && !includes(numericFields, rowData.property.data.type)) {
                        return null;
                    }
                    return (
                        <Textbox
                            error={errors[rowData._id]?.divisor}
                            inputMode="numeric"
                            tooltipPosition="top"
                            data-testid={`e-widget-editor-content-divisor-${rowData._id}`}
                            onChange={({ target: { value } }): void => {
                                onCellChange({
                                    columnId: 'divisor',
                                    rowId: rowData._id,
                                    value,
                                    rowData,
                                });
                            }}
                            placeholder={localize('@sage/xtrem-ui/divisor', 'Divisor')}
                            size="small"
                            value={rowData.divisor ?? ''}
                        />
                    );
                },
            },
            {
                id: 'groupingMethod',
                header: { name: localize('@sage/xtrem-ui/aggregation-method', 'Aggregation method'), width: 200 },
                cellRenderer: ({ rowData, data: allData, extraData: { numericTypes } }): React.ReactElement => {
                    if (
                        rowData.property?.data?.type &&
                        rowData.groupingMethod &&
                        !includes(numericTypes, rowData.property.data.type)
                    ) {
                        return (
                            <div data-testid={`e-widget-editor-grouping-method-${rowData._id}`}>
                                <Typography variant="p" m={0} paddingLeft="8px">
                                    {aggregationTranslations()[rowData.groupingMethod]}
                                </Typography>
                            </div>
                        );
                    }

                    return (
                        <FilterableSelect
                            required={true}
                            openOnFocus={true}
                            size="small"
                            data-testid={`e-widget-editor-grouping-method-${rowData._id}`}
                            onChange={({ target: { value } }): void => {
                                onCellChange({
                                    columnId: 'groupingMethod',
                                    rowId: rowData._id,
                                    value,
                                    rowData,
                                });
                            }}
                            value={rowData.groupingMethod ?? ''}
                            disabled={rowData.property == null}
                        >
                            {rowData.property?.data?.type &&
                                getAvailableGroupingMethods(allData, rowData).map(aggregationMethod => (
                                    <Option
                                        text={aggregationTranslations()[aggregationMethod]}
                                        value={aggregationMethod}
                                        key={aggregationMethod}
                                    />
                                ))}
                        </FilterableSelect>
                    );
                },
            },
            { id: 'path', isHidden: true, header: { name: 'Path' } },
            { id: 'labelPath', isHidden: true, header: { name: 'Label path' } },
        ];
    }, [onCellChange, nodeNames, widgetDefinition, DECIMAL_DIGITS]);

    const gridGutter = getGutterSize(browserIs);

    const addButtonText = React.useMemo<string>(() => localize('@sage/xtrem-ui/add-value', 'Add value'), []);

    const onRowDrag = React.useCallback<NonNullable<ChartWidgetGrid['onRowDrag']>>(ids => {
        dispatch({ type: 'ROW_DRAGGED', ids });
    }, []);

    const onRowAdded = React.useCallback<NonNullable<ChartWidgetGrid['onRowAdded']>>(() => {
        dispatch({ type: 'ROW_ADDED' });
    }, []);

    const onRowRemoved = React.useCallback<NonNullable<ChartWidgetGrid['onRowRemoved']>>((row): void => {
        dispatch({ type: 'ROW_REMOVED', row });
    }, []);

    const data = React.useMemo<ChartWidgetGrid['data']>(() => {
        return (widgetDefinition.aggregations ?? []).map(
            (
                { formatting, data: { type }, id, labelPath, decimalDigits, groupingMethod, title, divisor, path },
                index,
            ) => {
                return {
                    _id: String(index + 1),
                    property: widgetDefinition.selectedProperties?.[id ?? path],
                    formatting: isNil(formatting) ? undefined : String(formatting),
                    type,
                    path: id ?? path,
                    labelPath,
                    decimalDigits,
                    divisor: isNil(divisor) ? undefined : String(divisor),
                    groupingMethod,
                    title,
                };
            },
        );
    }, [widgetDefinition]);

    const [state, dispatch] = React.useReducer(chartWidgetReducer, {
        addButtonText,
        canDrag: widgetDefinition.type === 'BAR_CHART',
        onRowDrag,
        onRowRemoved,
        onRowAdded,
        columns,
        data,
        initialData: data,
        counter: (widgetDefinition.aggregations ?? []).length,
        validations: {},
        xAxisGroupBy: widgetDefinition.xAxis?.groupBy,
        initialxAxisGroupBy: widgetDefinition.xAxis?.groupBy,
        xAxisProperty: widgetDefinition.xAxis?.property,
        initialxAxisProperty: widgetDefinition.xAxis?.property,
        xDecimalDigits: widgetDefinition.xAxis?.decimalDigits,
        initialxDecimalDigits: widgetDefinition.xAxis?.decimalDigits,
        xDecimalDigitsValue: String(widgetDefinition.xAxis?.decimalDigits ?? 0),
        groupingMethods:
            widgetDefinition.groupBy?.property && widgetDefinition.groupBy.property.data.type
                ? (get(aggregationsGraphqlMapping, widgetDefinition.groupBy.property.data.type) ?? [])
                : [],
        groupingProperties: Object.values(widgetDefinition.selectedProperties ?? {}).filter(
            p => p.data.canSort && p.data.canFilter,
        ),
    });

    const horizontalAxisSelectableProps = React.useMemo(
        () =>
            state.groupingProperties.filter(p => p.data.canSort && !state.data.map(d => d.property?.id).includes(p.id)),
        [state.data, state.groupingProperties],
    );

    React.useEffect(() => {
        if (!state.xAxisProperty) {
            return;
        }
        onWidgetDefinitionUpdated({
            ...widgetDefinition,
            ...((state.initialxAxisProperty !== state.xAxisProperty ||
                state.initialxDecimalDigits !== state.xDecimalDigits) && {
                xAxis: {
                    ...widgetDefinition.xAxis,
                    property: state.xAxisProperty,
                    decimalDigits: state.xDecimalDigits,
                },
                orderBy: [],
            }),
        });

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [widgetDefinition, state.xAxisProperty, state.xDecimalDigits]);

    React.useEffect(() => {
        onWidgetDefinitionUpdated({
            ...widgetDefinition,
            ...((state.initialxAxisProperty !== state.xAxisProperty ||
                state.initialxAxisGroupBy !== state.xAxisGroupBy) && {
                xAxis: state.xAxisProperty
                    ? {
                          ...widgetDefinition.xAxis,
                          property: state.xAxisProperty,
                          groupBy:
                              state.xAxisProperty?.data?.type === 'Date' ||
                              state.xAxisProperty?.data?.type === 'DateTime'
                                  ? state.xAxisGroupBy
                                  : undefined,
                      }
                    : undefined,
            }),
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [widgetDefinition, state.xAxisProperty, state.xAxisGroupBy]);

    React.useEffect(() => {
        onWidgetDefinitionUpdated({
            ...widgetDefinition,
            ...((state.initialData !== state.data || !isEmpty(state.validations)) && {
                aggregations: state.data
                    .filter(
                        row =>
                            row.property != null &&
                            row.property?.data?.type != null &&
                            row.path != null &&
                            row.labelPath != null &&
                            row.groupingMethod != null &&
                            row.title != null &&
                            Object.values(state.validations[row._id] ?? {}).every(v => v == null),
                    )
                    .map(({ property, decimalDigits, groupingMethod, path, labelPath, title, divisor }, index) => {
                        const result: AggregationProperty = {
                            ...(property as Property),
                            id: path!,
                            labelPath: labelPath!,
                            groupingMethod: groupingMethod!,
                            decimalDigits,
                            title: title!,
                            color: widgetDefinition.aggregations?.[index]?.color ?? DEFAULT_CHART_COLORS[index].value,
                        };

                        if (divisor) {
                            result.divisor = Number(divisor);
                        }

                        return result;
                    }),
            }),
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [state.data, state.validations]);

    const canAddNewLines = React.useMemo(() => {
        return state.data.length < 5;
    }, [state.data.length]);

    const onHorizontalAxisChange = React.useCallback<(args: { target: { value: Property | undefined } }) => void>(
        ({ target: { value } }) => {
            dispatch({
                type: 'SET_X_AXIS_PROPERTY',
                value: value || undefined,
            });

            const row = state.data.find(d => d.path === value?.id);
            if (row) {
                dispatch({
                    type: 'ROW_REMOVED',
                    row,
                });
            }
        },
        [state.data],
    );

    const isXAxisDate = state.xAxisProperty?.data?.type === 'Date' || state.xAxisProperty?.data?.type === 'DateTime';
    const isXAxisNumeric = state.xAxisProperty?.data?.type && includes(numericFields, state.xAxisProperty.data?.type);
    return (
        <GridRow columns={8} gutter={gridGutter} margin={0} verticalMargin={0}>
            <GridColumn columnSpan={8}>
                <Box display="flex" flexDirection="column" justifyContent="center" gap={2}>
                    <SplitBox display="flex" alignItems="center" justifyContent="center" gap={2}>
                        <FilterableSelect
                            required={true}
                            openOnFocus={true}
                            multiColumn={true}
                            tableHeader={<PropertyTableHeader />}
                            data-testid="e-widget-editor-horizontal-axis"
                            label={localize('@sage/xtrem-ui/widget-editor-horizontal-axis', 'Horizontal axis')}
                            // @ts-expect-error "onChange" is actually triggered with { target: { value: Property } }
                            onChange={onHorizontalAxisChange}
                            // @ts-expect-error value is of type Property
                            value={state.xAxisProperty ?? {}}
                        >
                            {horizontalAxisSelectableProps.map(p => {
                                return (
                                    // @ts-expect-error value is of type Property
                                    <OptionRow text={p.label} value={p} key={p.id}>
                                        <td
                                            width="50%"
                                            style={{
                                                overflow: 'hidden',
                                                whiteSpace: 'pre-line',
                                                maxWidth: 0,
                                            }}
                                        >
                                            {p.label}
                                        </td>
                                        <td
                                            width="50%"
                                            style={{
                                                overflow: 'hidden',
                                                whiteSpace: 'pre-line',
                                                maxWidth: 0,
                                                textAlign: 'end',
                                            }}
                                        >
                                            {getPropertyParentNode({ nodeNames, property: p, widgetDefinition })}
                                        </td>
                                    </OptionRow>
                                );
                            })}
                        </FilterableSelect>
                        {isXAxisDate && (
                            <FilterableSelect
                                required={true}
                                openOnFocus={true}
                                data-testid="e-widget-editor-group-by"
                                label={localize('@sage/xtrem-ui/group-by', 'Group by')}
                                onChange={(ev: React.ChangeEvent<HTMLInputElement>): void => {
                                    const value = (ev.target.value || undefined) as DateAggregation | undefined;
                                    dispatch({
                                        type: 'SET_X_AXIS_GROUP_BY',
                                        value,
                                    });
                                }}
                                value={state.xAxisGroupBy ?? ''}
                            >
                                {['year', 'month', 'day'].map(aggregation => (
                                    <Option
                                        text={localize(`@sage/xtrem-ui/${aggregation}`, upperFirst(aggregation))}
                                        value={aggregation}
                                        key={aggregation}
                                    />
                                ))}
                            </FilterableSelect>
                        )}
                        {isXAxisNumeric && (
                            <Textbox
                                inputMode="numeric"
                                tooltipPosition="top"
                                data-testid="e-widget-editor-decimal-digits"
                                label={DECIMAL_DIGITS}
                                onChange={({ target: { value } }): void => {
                                    dispatch({
                                        type: 'SET_X_DECIMAL_DIGITS',
                                        value: value || '',
                                    });
                                }}
                                error={state.xDecimalDigitsError}
                                placeholder={DECIMAL_DIGITS}
                                value={state.xDecimalDigitsValue}
                            />
                        )}
                        {!isXAxisDate && !isXAxisNumeric && <Box width={432} />}
                    </SplitBox>
                    <Typography variant="h4">
                        {localize('@sage/xtrem-ui/widget-editor-content-step-vertical-axes', 'Vertical axes')}
                    </Typography>
                    <FlatTable
                        {...state}
                        extraData={{
                            validations: state.validations,
                            tableSelectableProps,
                            numericTypes: numericFields,
                        }}
                        canAddNewLines={canAddNewLines}
                        canRemoveLines={true}
                        emptyStateText={localize('@sage/xtrem-ui/no-data', 'No data to display')}
                        actionsText={localize('@sage/xtrem-ui/actions', 'Actions')}
                    />
                </Box>
            </GridColumn>
        </GridRow>
    );
}
