import type { FlatTableProps } from '@sage/xtrem-ui-components';
import type { ResponsiveTypes } from '../../../redux/state';
import type { WidgetEditorStepProps } from '../widget-editor-utils';

export interface ContentStepProps extends WidgetEditorStepProps {
    browserIs: ResponsiveTypes;
}

export type ContentAction<FT extends FlatTableProps<any, any>, Changes> =
    | {
          type: 'ROW_ADDED';
      }
    | {
          type: 'ROW_DRAGGED';
          ids: Parameters<NonNullable<FT['onRowDrag']>>[0];
      }
    | {
          type: 'CELL_CHANGED';
          changes: Changes;
          selectedProperties: ContentStepProps['widgetDefinition']['selectedProperties'];
      }
    | {
          type: 'ROW_REMOVED';
          row: Parameters<NonNullable<FT['onRowRemoved']>>[0];
      };
