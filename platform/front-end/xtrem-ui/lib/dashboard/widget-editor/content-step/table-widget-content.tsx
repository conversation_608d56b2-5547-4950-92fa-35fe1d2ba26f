import type { Dict } from '@sage/xtrem-shared';
import * as React from 'react';
import { localize } from '../../../service/i18n-service';
import { getGutterSize } from '../../../utils/responsive-utils';
import type { ContentStepProps } from './content-utils';
import { FlatTable, GridColumn, GridRow, useContentTable, useDeepCompareMemo } from '@sage/xtrem-ui-components';
import type { ContentTableProps } from '@sage/xtrem-ui-components';
import { isNil } from 'lodash';

export function TableWidgetContent({
    browserIs,
    onWidgetDefinitionUpdated,
    widgetDefinition,
    nodeNames,
}: {
    browserIs: ContentStepProps['browserIs'];
    nodeNames: Dict<string>;
    onWidgetDefinitionUpdated: ContentStepProps['onWidgetDefinitionUpdated'];
    widgetDefinition: ContentStepProps['widgetDefinition'];
}): React.ReactElement {
    const gridGutter = getGutterSize(browserIs);

    const onChange = React.useCallback<ContentTableProps['onChange']>(
        newColumns => {
            onWidgetDefinitionUpdated({
                ...widgetDefinition,
                columns: newColumns
                    .filter(c => !isNil(c.property) && !isNil(c.presentation) && !isNil(c.title))
                    .map(({ divisor, formatting, path, presentation, property, title }) => {
                        const { key, label, labelKey, data, labelPath } = property!;
                        return {
                            id: path,
                            divisor: isNil(divisor) ? 1 : Number(divisor),
                            formatting: isNil(formatting) ? 0 : Number(formatting),
                            labelPath,
                            path,
                            presentation: presentation!,
                            property,
                            title: title!,
                            key,
                            labelKey,
                            label,
                            data,
                        };
                    }),
            });
        },
        [onWidgetDefinitionUpdated, widgetDefinition],
    );

    const selectedProperties = useDeepCompareMemo(() => {
        return widgetDefinition.selectedProperties;
    }, [widgetDefinition.selectedProperties]);

    const value = useDeepCompareMemo(() => {
        return (widgetDefinition.columns ?? []).map(
            (
                {
                    label,
                    labelKey,
                    labelPath,
                    key,
                    path,
                    id,
                    canBeExpanded,
                    canBeSelected,
                    data,
                    formatting,
                    presentation,
                    title,
                    divisor,
                },
                index,
            ) => {
                return {
                    property: { label, labelKey, labelPath, id, key, path, canBeExpanded, canBeSelected, data },
                    formatting: !isNil(formatting) ? String(formatting) : undefined,
                    divisor: !isNil(divisor) ? String(divisor) : undefined,
                    presentation,
                    title,
                    _id: String(index + 1),
                    labelPath,
                    path: id ?? path,
                };
            },
        );
    }, [widgetDefinition.columns]);

    const { addButtonText, canAddNewLines, canDrag, columns, data, onRowAdded, onRowDrag, onRowRemoved, validations } =
        useContentTable({
            value,
            localize,
            node: widgetDefinition.node,
            nodeNames,
            onChange,
            selectedProperties,
        });

    return (
        <GridRow columns={8} gutter={gridGutter} margin={0} verticalMargin={0}>
            <GridColumn columnSpan={8}>
                <FlatTable
                    addButtonText={addButtonText}
                    canAddNewLines={canAddNewLines}
                    canDrag={canDrag}
                    columns={columns}
                    data={data}
                    onRowAdded={onRowAdded}
                    onRowDrag={onRowDrag}
                    onRowRemoved={onRowRemoved}
                    extraData={validations}
                    canRemoveLines={true}
                    actionsText={localize('@sage/xtrem-ui/actions', 'Actions')}
                />
            </GridColumn>
        </GridRow>
    );
}
