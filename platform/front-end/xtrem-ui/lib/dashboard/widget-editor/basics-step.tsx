import * as React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../redux';
import { objectKeys, type Dict } from '@sage/xtrem-shared';
import type { WidgetEditorStepProps } from './widget-editor-utils';
import { isChart } from './widget-editor-utils';
import { FilterableSelect, Option } from 'carbon-react/esm/components/select';
import { GridColumn, GridRow, SelectionCard } from '@sage/xtrem-ui-components';
import type { ResponsiveTypes, UserCustomizableWidget } from '../../redux/state';
import { getGutterSize } from '../../utils/responsive-utils';
import Textbox from 'carbon-react/esm/components/textbox';
import { localize } from '../../service/i18n-service';
import Typography from 'carbon-react/esm/components/typography';
import { resolveDetailedIcon } from '../../utils/detailed-icons-utils';
import { isEmpty, memoize } from 'lodash';

const widgetTypes = memoize(
    (): Dict<{ title: string; icon: string; description: string }> => ({
        INDICATOR_TILE: {
            title: localize('@sage/xtrem-ui/widget-editor-type-indicator-tile', 'Indicator'),
            description: localize(
                '@sage/xtrem-ui/widget-editor-type-indicator-tile-description',
                'Display major record updates.',
            ),
            icon: 'card',
        },
        TABLE: {
            title: localize('@sage/xtrem-ui/widget-editor-type-table', 'Table'),
            description: localize(
                '@sage/xtrem-ui/widget-editor-type-table-description',
                'Display data in rows and columns.',
            ),
            icon: 'table',
        },
        BAR_CHART: {
            title: localize('@sage/xtrem-ui/widget-editor-type-bar-chart', 'Bar chart'),
            description: localize(
                '@sage/xtrem-ui/widget-editor-type-bar-chart-description',
                'Display data visually with bars.',
            ),
            icon: 'chart-bar',
        },
        LINE_CHART: {
            title: localize('@sage/xtrem-ui/widget-editor-type-line-chart', 'Line chart'),
            description: localize(
                '@sage/xtrem-ui/widget-editor-type-line-chart-description',
                'Display data visually with lines.',
            ),
            icon: 'financials',
        },
    }),
);

export interface BasicsStepProps extends WidgetEditorStepProps {
    widgetCategories: Dict<string>;
    browserIs: ResponsiveTypes;
    isNewWidget: boolean;
}

export function BasicsStep({
    nodeNames,
    widgetCategories,
    onWidgetDefinitionUpdated,
    widgetDefinition,
    browserIs,
    isNewWidget,
    stepIndex,
}: BasicsStepProps): React.ReactElement {
    const [title, setTitle] = React.useState(widgetDefinition.title || '');
    const [titleValidationMessage, setTitleValidationMessage] = React.useState<string | undefined>(undefined);
    const [nodeValidationMessage, setNodeValidationMessage] = React.useState<string | undefined>(undefined);
    const hasWidgetSelection = React.useMemo(
        () => isNewWidget || isChart(widgetDefinition),
        [isNewWidget, widgetDefinition],
    );

    React.useEffect(() => {
        setTitle(widgetDefinition.title || '');
    }, [widgetDefinition.title, setTitle]);

    const selectableWidgetsKeys = React.useMemo(
        () => (!isNewWidget && isChart(widgetDefinition) ? ['BAR_CHART', 'LINE_CHART'] : objectKeys(widgetTypes())),
        [widgetDefinition, isNewWidget],
    );

    const gridGutter = getGutterSize(browserIs);

    const onNodeChanged = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>) => {
            const node = ev.target.value;
            const isDifferentNode = node !== widgetDefinition.node;
            if (isDifferentNode) {
                // Only keep a few basic properties because if the node type changes, we reset all other settings
                onWidgetDefinitionUpdated({
                    node,
                    title: widgetDefinition.title,
                    category: widgetDefinition.category,
                    type: widgetDefinition.type,
                });
            }
            if (!node) {
                setNodeValidationMessage(
                    localize('@sage/xtrem-ui/widget-editor-basic-step-missing-node', 'You need to add a node'),
                );
            } else {
                setNodeValidationMessage(undefined);
            }
        },
        [onWidgetDefinitionUpdated, widgetDefinition, setNodeValidationMessage],
    );

    const onCategoryChanged = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>) => {
            onWidgetDefinitionUpdated({ ...widgetDefinition, category: ev.target.value });
        },
        [onWidgetDefinitionUpdated, widgetDefinition],
    );

    const onTypeSelected = React.useCallback(
        (type: UserCustomizableWidget) => {
            // Only keep a few basic properties because if the widget type changes, we reset all other settings
            onWidgetDefinitionUpdated({
                type,
                title: widgetDefinition.title,
                category: widgetDefinition.category,
                node: widgetDefinition.node,
            });
        },
        [onWidgetDefinitionUpdated, widgetDefinition],
    );

    const onTitleChanged = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>) => {
            setTitle(ev.target.value);
        },
        [setTitle],
    );

    const onWidgetTitleBlur = React.useCallback(() => {
        onWidgetDefinitionUpdated({ ...widgetDefinition, title });
        if (!title) {
            setTitleValidationMessage(
                localize('@sage/xtrem-ui/widget-editor-basic-step-missing-title', 'You need to add a title'),
            );
        } else {
            setTitleValidationMessage(undefined);
        }
    }, [title, widgetDefinition, onWidgetDefinitionUpdated, setTitleValidationMessage]);

    const resolvedWidgetTypes = widgetTypes();
    const availableColumns = browserIs.xs ? 4 : 8;
    return (
        <div>
            <Typography variant="h2" data-testid="e-widget-editor-step-title">
                {localize(
                    '@sage/xtrem-ui/widget-editor-basic-step-title',
                    '{{stepIndex}}. Select a widget to get started',
                    { stepIndex },
                )}
            </Typography>
            <div className="e-widget-editor-section">
                <GridRow columns={availableColumns} gutter={gridGutter} margin={0} verticalMargin={0}>
                    <GridColumn columnSpan={4}>
                        <Textbox
                            data-testid="e-widget-editor-basic-title"
                            required={true}
                            validationOnLabel={true}
                            error={titleValidationMessage}
                            label={localize('@sage/xtrem-ui/widget-editor-widget-title', 'Widget title')}
                            value={title}
                            onChange={onTitleChanged}
                            onBlur={onWidgetTitleBlur}
                        />
                    </GridColumn>
                    {!isEmpty(widgetCategories) && (
                        <GridColumn columnSpan={4}>
                            <FilterableSelect
                                data-testid="e-widget-editor-basic-category"
                                name="widgetCategory"
                                id="widgetCategory"
                                label={localize('@sage/xtrem-ui/widget-editor-widget-category', 'Widget category')}
                                onChange={onCategoryChanged}
                                value={widgetDefinition.category || ''}
                            >
                                {objectKeys(widgetCategories).map(k => (
                                    <Option text={widgetCategories[k]} value={k} key={k} />
                                ))}
                            </FilterableSelect>
                        </GridColumn>
                    )}
                    {isNewWidget && (
                        <GridColumn columnSpan={4}>
                            <FilterableSelect
                                data-testid="e-widget-editor-basic-node"
                                required={true}
                                validationOnLabel={true}
                                error={nodeValidationMessage}
                                name="node"
                                id="node"
                                label={localize('@sage/xtrem-ui/widget-editor-entry-node', 'Entry module')}
                                onChange={onNodeChanged}
                                value={widgetDefinition.node || ''}
                            >
                                {objectKeys(nodeNames).map(k => (
                                    <Option text={nodeNames[k]} value={k} key={k} />
                                ))}
                            </FilterableSelect>
                        </GridColumn>
                    )}
                </GridRow>
            </div>

            {hasWidgetSelection && (
                <>
                    <Typography variant="segment-header-small" mt="16px">
                        Widgets
                    </Typography>

                    <div className="e-widget-editor-section">
                        <GridRow columns={availableColumns} gutter={gridGutter} margin={0} verticalMargin={0}>
                            {selectableWidgetsKeys.map(key => (
                                <GridColumn columnSpan={4} key={key}>
                                    <SelectionCard
                                        _id={key}
                                        isSelected={widgetDefinition.type === key}
                                        title={resolvedWidgetTypes[key].title}
                                        icon={resolveDetailedIcon(resolvedWidgetTypes[key].icon)}
                                        description={resolvedWidgetTypes[key].description}
                                        onClick={(): void => onTypeSelected(key as UserCustomizableWidget)}
                                    />
                                </GridColumn>
                            ))}
                        </GridRow>
                    </div>
                </>
            )}
        </div>
    );
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: WidgetEditorStepProps): BasicsStepProps => {
    const { widgetEditor } = state.dashboard.dashboardGroups[props.group];

    return {
        ...props,
        browserIs: state.browser.is,
        isNewWidget: !widgetEditor.widgetId,
        widgetCategories: state.dashboard.widgetCategories,
    };
};

export const ConnectedBasicsStep = connect(mapStateToProps)(BasicsStep);
