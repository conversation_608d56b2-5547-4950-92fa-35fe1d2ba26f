import type { AbstractResponsiveWidget, WidgetCollectionItemDefinition } from '@sage/bms-dashboard';
import { FilterableSelect, Option } from 'carbon-react/esm/components/select';
import AdvancedColorPicker from 'carbon-react/esm/components/advanced-color-picker';
import Box from 'carbon-react/esm/components/box';
import NumberComponent from 'carbon-react/esm/components/number';
import Typography from 'carbon-react/esm/components/typography';
import * as React from 'react';
import type { LoadMoreRowsParams } from '../../service/dashboard-service';
import { fetchWidgetData, getWidgetsFromDashboardItems } from '../../service/dashboard-service';
import { localize } from '../../service/i18n-service';
import AsyncDragDropCanvasWrapper from '../async-drag-drop-canvas-wrapper';
import localeText from '../dashboard-strings';
import { getGenericWidgetArtifactName, getGenericWidgetDefinition } from '../generic-widgets';
import type { WidgetDefinition } from '../widgets/abstract-widget';
import { WidgetType } from '../widgets/abstract-widget';
import type { TableWidgetProperties } from '../widgets/table-widget-decorator';
import { indicatorTileIconsSortedArray } from './indicator-tile-icons';
import type { WidgetEditorStepProps } from './widget-editor-utils';
import { isChart, DEFAULT_CHART_COLORS } from './widget-editor-utils';
import Textbox from 'carbon-react/esm/components/textbox';
import { isEmpty, isNil, memoize } from 'lodash';
import { useDebounce } from 'usehooks-ts';
import { WidgetActionConfiguration } from './widget-action-configuration';
import { deepMerge, objectKeys, type Dict } from '@sage/xtrem-shared';
import type { UserWidgetDefinition } from '../../redux/state';
import { queryPagesByNodeType } from '../../service/metadata-service';
import { PREVIEW_DASHBOARD_ID, PREVIEW_WIDGET_ID } from '../../utils/constants';
import { getNewTableData } from '../../redux/actions/dashboard-actions';
import { DASHBOARD_BREAKPOINTS } from '../dashboard-utils';
import type { DashboardBreakpoint, OnBreakpointChange } from '../dashboard-types';

type PreviewDashboardConfigurationContentProps = {
    widgetDefinition: UserWidgetDefinition;
    previewDashboardConfiguration: AbstractResponsiveWidget[];
    onBreakpointChange: OnBreakpointChange;
};

function PreviewDashboardConfigurationContent({
    widgetDefinition,
    previewDashboardConfiguration,
    onBreakpointChange,
}: PreviewDashboardConfigurationContentProps): React.ReactElement {
    const json = React.useMemo(
        () =>
            ({
                type: 'responsive',
                isHeaderHidden: true,
                isEditMode: false,
                locale: 'en-US',
                layout: {
                    cols: { xxs: 1, xs: 1, sm: 1, md: 1, lg: 1 },
                    breakpoints: DASHBOARD_BREAKPOINTS,
                    rowHeight: widgetDefinition.type === 'INDICATOR_TILE' ? 120 : 400,
                    width: 400,
                    isDraggable: false,
                },
                widgets: previewDashboardConfiguration,
                stringLiterals: localeText(),
                onBreakpointChange,
            }) as const,
        [onBreakpointChange, previewDashboardConfiguration, widgetDefinition.type],
    );
    return (
        <div className="e-layout-preview-dashboard-wrapper">
            <AsyncDragDropCanvasWrapper json={json} />
        </div>
    );
}

export function LayoutStep({
    contextVariables,
    group,
    onWidgetDefinitionUpdated,
    stepIndex,
    widgetDefinition,
}: WidgetEditorStepProps): React.ReactElement {
    const [previewDashboardConfiguration, setPreviewDashboardConfiguration] = React.useState<
        AbstractResponsiveWidget[] | null
    >(null);
    const [maxNumAllowedValue, setMaxNumAllowedValue] = React.useState<string>(
        String(widgetDefinition.aggregationsCount ?? 20),
    );

    const [breakpoint, setBreakpoint] = React.useState<DashboardBreakpoint>('md');
    const [arePagesAvailableForNode, setPagesAvailableForNode] = React.useState(false);
    const [verticalAxisLabel, setVerticalAxisLabel] = React.useState(widgetDefinition.verticalAxisLabel);
    const [horizontalAxisLabel, setHorizontalAxisLabel] = React.useState(widgetDefinition.horizontalAxisLabel);

    const [maxNumAllowed, setMaxNumAllowed] = React.useState<number>(widgetDefinition.aggregationsCount ?? 20);
    const first = useDebounce(maxNumAllowed, 500);
    const vAxisLabel = useDebounce(verticalAxisLabel, 500);
    const hAxisLabel = useDebounce(horizontalAxisLabel, 500);
    const [maxNumError, setMaxNumError] = React.useState('');
    const [chartColors, setChartColors] = React.useState<Dict<{ color: string; isOpen: boolean }>>(
        (widgetDefinition.aggregations ?? []).reduce(
            (acc, aggregation, index) => {
                acc[`${aggregation.id}-${aggregation.groupingMethod}`] = {
                    color: aggregation.color ?? DEFAULT_CHART_COLORS[index].value,
                    isOpen: false,
                };
                return acc;
            },
            {} as Dict<{ color: string; isOpen: boolean }>,
        ),
    );
    const [subtitle, setSubtitle] = React.useState<string>(widgetDefinition.subtitle || '');

    const icons = indicatorTileIconsSortedArray();

    React.useEffect(() => {
        if (widgetDefinition.node) {
            queryPagesByNodeType(widgetDefinition.node).then(r => setPagesAvailableForNode(!isEmpty(r)));
        }
    }, [widgetDefinition.node]);

    React.useEffect(() => {
        if (isChart(widgetDefinition)) {
            onWidgetDefinitionUpdated({
                ...widgetDefinition,
                aggregationsCount: maxNumAllowed,
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [first, widgetDefinition]);

    React.useEffect(() => {
        if (isChart(widgetDefinition)) {
            onWidgetDefinitionUpdated({
                ...widgetDefinition,
                horizontalAxisLabel: hAxisLabel,
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [hAxisLabel, widgetDefinition]);

    React.useEffect(() => {
        if (isChart(widgetDefinition)) {
            onWidgetDefinitionUpdated({
                ...widgetDefinition,
                verticalAxisLabel: vAxisLabel,
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [vAxisLabel, widgetDefinition]);

    const [decimalDigitsValidationMessage, setDecimalDigitsValidationMessage] = React.useState<string | undefined>(
        undefined,
    );

    React.useEffect(() => {
        const widgetObject = getGenericWidgetDefinition(
            widgetDefinition,
            PREVIEW_DASHBOARD_ID,
            PREVIEW_WIDGET_ID,
            group,
            contextVariables,
            true,
        );

        const dashboardWidgetDefinition: WidgetDefinition = {
            _id: PREVIEW_WIDGET_ID,
            artifactName: getGenericWidgetArtifactName(widgetDefinition),
            properties: widgetObject.constructor.prototype.__properties,
            widgetObject,
            widgetType: widgetObject.constructor.prototype.__type,
            options: {},
        };

        fetchWidgetData({
            group,
            widgetDefinition: dashboardWidgetDefinition,
            queryArgs: isChart(widgetDefinition) ? { first } : undefined,
        }).then(data => {
            dashboardWidgetDefinition.widgetObject.constructor.prototype.__data = data;
            const isTableWidget = dashboardWidgetDefinition.widgetType === WidgetType.table;
            const w =
                dashboardWidgetDefinition.widgetType === WidgetType.table
                    ? Math.max(
                          Math.floor(
                              objectKeys(
                                  (dashboardWidgetDefinition.properties as TableWidgetProperties<any>).rowDefinition,
                              ).length / 2,
                          ),
                          1,
                      )
                    : 1;
            const widgets = getWidgetsFromDashboardItems({
                dashboardId: PREVIEW_DASHBOARD_ID,
                group,
                children: [
                    {
                        _id: PREVIEW_WIDGET_ID,
                        settings: {},
                        positions: [
                            { x: 0, y: 0, breakpoint: 'xxs', h: 1, w },
                            { x: 0, y: 0, breakpoint: 'xs', h: 1, w },
                            { x: 0, y: 0, breakpoint: 'sm', h: 1, w },
                            { x: 0, y: 0, breakpoint: 'md', h: 1, w },
                            { x: 0, y: 0, breakpoint: 'lg', h: 1, w },
                        ],
                        type: getGenericWidgetArtifactName(widgetDefinition),
                    },
                ],
                widgets: {
                    [PREVIEW_WIDGET_ID]: { ...dashboardWidgetDefinition, data, isLoading: false },
                },
                breakpoint,
            });
            setPreviewDashboardConfiguration(
                isTableWidget
                    ? widgets.map(w => ({
                          ...w,
                          data: {
                              ...w.data,
                              loadMoreRows: memoize(
                                  async (
                                      loadMoreParams: LoadMoreRowsParams = {
                                          startIndex: 0,
                                          stopIndex: 20,
                                          pageCount: 1,
                                          pageSize: 20,
                                          first: 20,
                                      },
                                  ): Promise<WidgetCollectionItemDefinition[]> => {
                                      const newPage = await fetchWidgetData({
                                          widgetDefinition: dashboardWidgetDefinition,
                                          version: undefined,
                                          queryArgs: loadMoreParams,
                                          group,
                                      });

                                      const newData = getNewTableData({
                                          widgetDefinition: dashboardWidgetDefinition,
                                          data: newPage,
                                          group,
                                      });
                                      const mergedData = deepMerge(
                                          dashboardWidgetDefinition.widgetObject.constructor.prototype.__data,
                                          newPage,
                                      );
                                      dashboardWidgetDefinition.widgetObject.constructor.prototype.__data = mergedData;
                                      setPreviewDashboardConfiguration(currentConfig => {
                                          return currentConfig
                                              ? [
                                                    {
                                                        ...currentConfig[0],
                                                        data: {
                                                            ...currentConfig[0].data,
                                                            data: [...currentConfig[0].data.data, ...newData],
                                                        },
                                                    },
                                                ]
                                              : currentConfig;
                                      });
                                      return newData;
                                  },
                              ),
                          },
                      }))
                    : widgets,
            );
        });
    }, [widgetDefinition, setPreviewDashboardConfiguration, first, group, contextVariables, breakpoint]);

    const onIconChanged = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>) => {
            onWidgetDefinitionUpdated({ ...widgetDefinition, icon: ev.target.value });
        },
        [onWidgetDefinitionUpdated, widgetDefinition],
    );

    const onSubtitleChanged = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>) => {
            setSubtitle(ev.target.value);
        },
        [setSubtitle],
    );

    const onBreakpointChange = React.useCallback<OnBreakpointChange>(newBreakpoint => {
        setBreakpoint(newBreakpoint);
    }, []);

    const onSubtitleBlur = React.useCallback(() => {
        onWidgetDefinitionUpdated({ ...widgetDefinition, subtitle });
    }, [subtitle, widgetDefinition, onWidgetDefinitionUpdated]);

    const onDecimalDigitsChanged = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>) => {
            if (!ev.target.value) {
                setDecimalDigitsValidationMessage(
                    localize(
                        '@sage/xtrem-ui/widget-editor-action-decimal-digits-mandatory',
                        'You need to enter a number.',
                    ),
                );
            } else {
                setDecimalDigitsValidationMessage(undefined);
            }
            const decimalDigits = parseInt(ev.target.value, 10);
            onWidgetDefinitionUpdated({
                ...widgetDefinition,
                decimalDigits: !Number.isNaN(decimalDigits) ? decimalDigits : undefined,
            });
        },
        [onWidgetDefinitionUpdated, widgetDefinition, setDecimalDigitsValidationMessage],
    );

    const shouldRenderConfigColumn = arePagesAvailableForNode || widgetDefinition.type !== 'TABLE';

    return (
        <div className="e-widget-editor-layout-screen">
            <Typography variant="h2" data-testid="e-widget-editor-step-title">
                {localize('@sage/xtrem-ui/widget-editor-layout-step-title', '{{stepIndex}}. Create your layout', {
                    stepIndex,
                })}
            </Typography>
            <div className="e-layout-step e-widget-editor-section">
                {shouldRenderConfigColumn && (
                    <div className="e-layout-settings-container" data-testid="e-layout-settings-container">
                        {isChart(widgetDefinition) && (
                            <>
                                <div style={{ paddingBottom: '24px' }}>
                                    <Typography
                                        mb="16px"
                                        variant="h4"
                                        fontWeight="600"
                                        fontSize="16px"
                                        lineHeight="150%"
                                        p={0}
                                    >
                                        {localize('@sage/xtrem-ui/series-options', 'Series options')}
                                    </Typography>
                                    {(widgetDefinition.aggregations ?? []).map((aggregation, index) => {
                                        return (
                                            <Box
                                                key={aggregation.label}
                                                display="flex"
                                                alignItems="center"
                                                justifyContent="space-between"
                                                paddingY={1}
                                                boxShadow="boxShadow010"
                                            >
                                                <Typography
                                                    variant="p"
                                                    fontWeight="400"
                                                    fontSize="14px"
                                                    lineHeight="150%"
                                                    m={0}
                                                    p={0}
                                                >
                                                    {aggregation.label}
                                                </Typography>
                                                <AdvancedColorPicker
                                                    margin={0}
                                                    name={aggregation.label}
                                                    availableColors={DEFAULT_CHART_COLORS}
                                                    defaultColor={DEFAULT_CHART_COLORS[index]?.value}
                                                    selectedColor={
                                                        chartColors[`${aggregation.id}-${aggregation.groupingMethod}`]
                                                            .color
                                                    }
                                                    onChange={(evt: React.ChangeEvent<HTMLInputElement>): void => {
                                                        const color = (evt.target as any).value;
                                                        setChartColors(c => {
                                                            return {
                                                                ...c,
                                                                [`${aggregation.id}-${aggregation.groupingMethod}`]: {
                                                                    isOpen: false,
                                                                    color,
                                                                },
                                                            };
                                                        });
                                                        onWidgetDefinitionUpdated({
                                                            ...widgetDefinition,
                                                            aggregations: (widgetDefinition.aggregations ?? []).map(
                                                                a => {
                                                                    if (a.id === aggregation.id) {
                                                                        return { ...a, color };
                                                                    }
                                                                    return a;
                                                                },
                                                            ),
                                                        });
                                                    }}
                                                    onOpen={(): void => {
                                                        setChartColors(c => {
                                                            return {
                                                                ...c,
                                                                [`${aggregation.id}-${aggregation.groupingMethod}`]: {
                                                                    ...c[
                                                                        `${aggregation.id}-${aggregation.groupingMethod}`
                                                                    ],
                                                                    isOpen: true,
                                                                },
                                                            };
                                                        });
                                                    }}
                                                    onClose={(): void => {
                                                        setChartColors(c => {
                                                            return {
                                                                ...c,
                                                                [`${aggregation.id}-${aggregation.groupingMethod}`]: {
                                                                    ...c[
                                                                        `${aggregation.id}-${aggregation.groupingMethod}`
                                                                    ],
                                                                    isOpen: false,
                                                                },
                                                            };
                                                        });
                                                    }}
                                                    open={
                                                        chartColors[`${aggregation.id}-${aggregation.groupingMethod}`]
                                                            .isOpen
                                                    }
                                                />
                                            </Box>
                                        );
                                    })}
                                    <Textbox
                                        my="16px"
                                        name="max-num-allowed"
                                        data-testid="e-widget-editor-layout-max-num-allowed"
                                        label={localize(
                                            '@sage/xtrem-ui/widget-editor-max-num-of-values',
                                            'Maximum number of values',
                                        )}
                                        value={maxNumAllowedValue}
                                        error={maxNumError || undefined}
                                        onChange={({ target: { value } }): void => {
                                            setMaxNumAllowedValue(value);
                                            if (value.match(/^-?\d+$/)) {
                                                const num = Number(value);
                                                if (num < 1 || num > 365) {
                                                    setMaxNumError(
                                                        localize(
                                                            '@sage/xtrem-ui/must-be-between-1-and-365',
                                                            'Enter a number between 1 and 365',
                                                        ),
                                                    );
                                                } else {
                                                    setMaxNumAllowed(num);
                                                    setMaxNumError('');
                                                }
                                            } else {
                                                setMaxNumError(
                                                    localize(
                                                        '@sage/xtrem-ui/must-be-a-number',
                                                        'You need to enter a number',
                                                    ),
                                                );
                                            }
                                        }}
                                    />
                                </div>
                                <div style={{ paddingBottom: '24px' }}>
                                    <Typography
                                        variant="h4"
                                        fontWeight="600"
                                        fontSize="16px"
                                        lineHeight="150%"
                                        m={0}
                                        p={0}
                                    >
                                        {localize('@sage/xtrem-ui/axes', 'Axes')}
                                    </Typography>
                                    <Textbox
                                        mt="16px"
                                        maxLength={50}
                                        name="vertical-axis-label"
                                        data-testid="e-widget-editor-layout-vertical-axis-label"
                                        label={localize(
                                            '@sage/xtrem-ui/widget-editor-vertical-axis-label',
                                            'Vertical axis label',
                                        )}
                                        value={verticalAxisLabel}
                                        onChange={({ target: { value } }): void => {
                                            setVerticalAxisLabel(value);
                                        }}
                                    />
                                    <Textbox
                                        mt="16px"
                                        maxLength={50}
                                        name="horizontal-axis-label"
                                        data-testid="e-widget-editor-layout-horizontal-axis-label"
                                        label={localize(
                                            '@sage/xtrem-ui/widget-editor-horizontal-axis-label',
                                            'Horizontal axis label',
                                        )}
                                        value={horizontalAxisLabel}
                                        onChange={({ target: { value } }): void => {
                                            setHorizontalAxisLabel(value);
                                        }}
                                    />
                                </div>
                            </>
                        )}
                        {widgetDefinition.type === 'INDICATOR_TILE' && (
                            <>
                                <FilterableSelect
                                    data-testid="e-widget-editor-layout-icon"
                                    required={false}
                                    validationOnLabel={true}
                                    name="icon"
                                    id="icon"
                                    label={localize('@sage/xtrem-ui/widget-editor-icon', 'Icon')}
                                    onChange={onIconChanged}
                                    value={widgetDefinition.icon || ''}
                                >
                                    {icons.map(k => (
                                        <Option text={k.label} value={k.value} key={k.value} />
                                    ))}
                                </FilterableSelect>
                                <NumberComponent
                                    mt="16px"
                                    data-testid="e-widget-editor-layout-decimal-digits"
                                    required={true}
                                    validationOnLabel={true}
                                    name="decimal-digits"
                                    id="decimal-digits"
                                    disabled={
                                        widgetDefinition.groupBy?.property.data.type === 'String' ||
                                        widgetDefinition.groupBy?.method === 'distinctCount'
                                    }
                                    label={localize(
                                        '@sage/xtrem-ui/widget-editor-content-formatting',
                                        'Decimal places',
                                    )}
                                    onChange={onDecimalDigitsChanged}
                                    value={
                                        !isNil(widgetDefinition.decimalDigits)
                                            ? String(widgetDefinition.decimalDigits)
                                            : ''
                                    }
                                    error={decimalDigitsValidationMessage}
                                />
                                <Textbox
                                    mt="16px"
                                    name="subtitle"
                                    id="subtitle"
                                    data-testid="e-widget-editor-layout-subtitle"
                                    label={localize('@sage/xtrem-ui/widget-editor-subtitle', 'Subtitle')}
                                    value={subtitle}
                                    onChange={onSubtitleChanged}
                                    onBlur={onSubtitleBlur}
                                />
                                {arePagesAvailableForNode && <div className="e-layout-separator" />}
                            </>
                        )}
                        {arePagesAvailableForNode && (
                            <>
                                <div className="e-widget-editor-label">
                                    {localize('@sage/xtrem-ui/widget-actions', 'Actions')}
                                </div>
                                <WidgetActionConfiguration
                                    actionKey="seeAllAction"
                                    actionName={localize('@sage/xtrem-ui/widget-action-see-all', 'See all')}
                                    helperText={localize(
                                        '@sage/xtrem-ui/widget-action-see-all-help',
                                        'View records based on your data selection',
                                    )}
                                    widgetDefinition={widgetDefinition}
                                    onWidgetDefinitionUpdated={onWidgetDefinitionUpdated}
                                />
                                <WidgetActionConfiguration
                                    actionKey="createAction"
                                    actionName={localize('@sage/xtrem-ui/widget-action-create', 'Create')}
                                    helperText={localize(
                                        '@sage/xtrem-ui/widget-action-create-help',
                                        'Add a shortcut to create a new record',
                                    )}
                                    widgetDefinition={widgetDefinition}
                                    onWidgetDefinitionUpdated={onWidgetDefinitionUpdated}
                                />
                            </>
                        )}
                    </div>
                )}

                <div className="e-layout-preview-container">
                    <div className="e-layout-preview-preview-label">
                        {localize('@sage/xtrem-ui/widget-preview-label', 'Preview')}
                    </div>

                    {previewDashboardConfiguration && (
                        <PreviewDashboardConfigurationContent
                            widgetDefinition={widgetDefinition}
                            previewDashboardConfiguration={previewDashboardConfiguration}
                            onBreakpointChange={onBreakpointChange}
                        />
                    )}
                </div>
            </div>
        </div>
    );
}
