import { applyActionMocks, getMockState, getMockStore, userEvent } from '../../../__tests__/test-helpers';

import * as React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react';
import * as i18n from '../../../service/i18n-service';
import * as nodeInformationService from '../../../service/node-information-service';
import { Provider } from 'react-redux';
import { FilterStep } from '../filters-step';
import { GraphQLTypes } from '../../../types';
import { ConnectedWidgetEditorDialog } from '../widget-editor-dialog';
import type { XtremAppState } from '../../../redux';
import { updateUserWidgetDefinition } from '../../../redux/actions';
import { FieldKey, type Property } from '@sage/xtrem-shared';
import type { WidgetEditorStepProps } from '../widget-editor-utils';
import '@testing-library/jest-dom';

jest.useFakeTimers();

const setupResizeObserverMock = () => {
    if (!window) {
        return;
    }
    window.ResizeObserver =
        window.ResizeObserver ||
        jest.fn().mockImplementation((callback: ResizeObserverCallback) => {
            let hasCalledCallback = false;
            const observer: ResizeObserver = {
                disconnect: jest.fn(),
                // observe mock needs to actually call the callback straight away, as this is what a real ResizeObserver does
                // and this behaviour is needed for the FixedNavigationBarContextProvider to work properly.
                // Note that we must only call the callback once per ResizeObserver instance, to avoid stack overflows in
                // react-virtual.
                observe: jest.fn((target: Element) => {
                    if (!hasCalledCallback) {
                        hasCalledCallback = true;
                        callback([{ target } as ResizeObserverEntry], observer);
                    }
                }),
                unobserve: jest.fn(),
            };
            return observer;
        });
};

const testDashboardGroup = 'home';

const mockDOMRect = (width: number, height: number, elementIdentifier: string) => {
    Element.prototype.getBoundingClientRect = jest.fn(function patata(this: HTMLElement) {
        if (this.getAttribute('data-component') === elementIdentifier) {
            return getDOMRect(width, height);
        }
        return getDOMRect(0, 0);
    });
};
const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect;
function mockedGetBoundingClientRect(this: any) {
    const dataElement = this.getAttribute('data-element');
    if (dataElement === 'select-list-wrapper') {
        return {
            height: 1080,
            width: 1920,
        } as any;
    }
    return originalGetBoundingClientRect.bind(this)();
}

const getDOMRect = (width: number, height: number): DOMRect => ({
    width,
    height,
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    x: 0,
    y: 0,
    toJSON: () => {},
});

describe('filters widget editor step', () => {
    let props: WidgetEditorStepProps;

    beforeAll(() => {
        Element.prototype.getBoundingClientRect = mockedGetBoundingClientRect;
        setupResizeObserverMock();
        mockDOMRect(40, 100, 'select-list-scrollable-container');
    });

    afterAll(() => {
        Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
    });

    const selectedProperties = {
        Enum: {
            label: 'Enum',
            data: { type: GraphQLTypes.Enum, canFilter: true, canSort: true },
            id: 'Enum',
            labelPath: 'Enum',
        },
        Id: {
            label: 'Id',
            data: { type: GraphQLTypes.ID, canFilter: true, canSort: true },
            id: 'Id',
            labelPath: 'Id',
        },
        Boolean: {
            label: 'Boolean',
            data: { type: GraphQLTypes.Boolean, canFilter: true, canSort: true },
            id: 'Boolean',
            labelPath: 'Boolean',
        },
        Date: {
            label: 'Date',
            data: { type: GraphQLTypes.Date, canFilter: true, canSort: true },
            id: 'Date',
            labelPath: 'Date',
        },
        DateTime: {
            label: 'DateTime',
            data: { type: GraphQLTypes.DateTime, canFilter: true, canSort: true },
            id: 'DateTime',
            labelPath: 'DateTime',
        },
        Decimal: {
            label: 'Decimal',
            data: { type: GraphQLTypes.Decimal, canFilter: true, canSort: true },
            id: 'Decimal',
            labelPath: 'Decimal',
        },
        ExternalReference: {
            label: 'ExternalReference',
            data: { type: GraphQLTypes.ExternalReference, canFilter: true, canSort: true },
            id: 'ExternalReference',
            labelPath: 'ExternalReference',
        },
        Float: {
            label: 'Float',
            data: { type: GraphQLTypes.Float, canFilter: true, canSort: true },
            id: 'Float',
            labelPath: 'Float',
        },
        _InputStream: {
            label: '_InputStream',
            data: { type: '_InputStream', canFilter: true, canSort: true },
            id: '_InputStream',
            labelPath: '_InputStream',
        },
        Int: {
            label: 'Int',
            data: { type: GraphQLTypes.Int, canFilter: true, canSort: true },
            id: 'Int',
            labelPath: 'Int',
        },
        IntReference: {
            label: 'IntReference',
            data: { type: GraphQLTypes.IntReference, canFilter: true, canSort: true },
            id: 'IntReference',
            labelPath: 'IntReference',
        },
        Json: {
            label: 'Json',
            data: { type: GraphQLTypes.Json, canFilter: true, canSort: true },
            id: 'Json',
            labelPath: 'Json',
        },
        String: {
            label: 'String',
            data: { type: GraphQLTypes.String, canFilter: true, canSort: true },
            id: 'String',
            labelPath: 'String',
        },
        IntOrString: {
            label: 'IntOrString',
            data: { type: GraphQLTypes.IntOrString, canFilter: true, canSort: true },
            id: 'IntOrString',
            labelPath: 'IntOrString',
        },
    };
    beforeEach(() => {
        jest.useFakeTimers();
        jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);

        props = {
            group: testDashboardGroup,
            nodeNames: {},
            locale: 'en-US',
            onWidgetDefinitionUpdated: jest.fn(),
            stepIndex: 4,
            widgetDefinition: {
                type: 'TABLE',
                selectedProperties: selectedProperties as any,
            },
        };
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    it('should render the title', () => {
        const { getByText } = render(<FilterStep {...props} />);
        expect(getByText('{{stepIndex}}. Add your filters')).toBeInTheDocument();
    });

    describe('integration', () => {
        let mockStore;
        let mockState: XtremAppState;

        beforeAll(() => {
            Element.prototype.getBoundingClientRect = mockedGetBoundingClientRect;
            setupResizeObserverMock();
            mockDOMRect(40, 100, 'select-list-scrollable-container');
        });

        afterAll(() => {
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
        });

        beforeEach(() => {
            jest.useFakeTimers();

            jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);
            jest.spyOn(nodeInformationService, 'fetchNodeDetails').mockImplementation(() => Promise.resolve({}));

            mockState = getMockState();
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.isOpen = true;
            mockState.dashboard.nodeNames = {
                '@sage/xtrem-test/ATestNode': 'ZZZZZZ',
                '@sage/xtrem-test/AnotherTestNode': 'AAAA',
                '@sage/xtrem-test/ThirdTestNode': 'BBBB',
            };
            mockState.dashboard.widgetCategories = {
                SOME_CATEGORY: 'Some Category',
                ANOTHER_CATEGORY: 'Another Category',
            };
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetDefinition = {
                category: 'SOME_CATEGORY',
                title: 'Test title',
                type: 'TABLE',
                node: '@sage/xtrem-test/ATestNode',
                groupBy: undefined,
                columns: [
                    {
                        data: { type: GraphQLTypes.Boolean, canFilter: true, canSort: true },
                        formatting: undefined,
                        label: 'Boolean',
                        title: 'Boolean title',
                        id: 'Boolean',
                        labelPath: 'Boolean',
                        presentation: FieldKey.Checkbox,
                    } as any,
                ],
                selectedProperties: {
                    Boolean: {
                        label: 'Boolean',
                        data: { type: GraphQLTypes.Boolean, canFilter: true, canSort: true },
                        id: 'Boolean',
                        labelPath: 'Boolean',
                    } as Property,
                    Date: {
                        label: 'Date',
                        data: { type: GraphQLTypes.Date },
                        id: 'Date',
                        labelPath: 'Date',
                    } as Property,
                    DateTime: {
                        label: 'DateTime',
                        data: { type: GraphQLTypes.DateTime, canFilter: true, canSort: true },
                        id: 'DateTime',
                        labelPath: 'DateTime',
                    } as Property,
                    Decimal: {
                        label: 'Decimal',
                        data: { type: GraphQLTypes.Decimal, canFilter: true, canSort: true },
                        id: 'Decimal',
                        labelPath: 'Decimal',
                    } as Property,
                    Enum: {
                        label: 'Enum',
                        data: { type: GraphQLTypes.Enum, canFilter: true, canSort: true },
                        id: 'Enum',
                        labelPath: 'Enum',
                    } as Property,
                    ExternalReference: {
                        label: 'ExternalReference',
                        data: { type: GraphQLTypes.ExternalReference, canFilter: true, canSort: true },
                        id: 'ExternalReference',
                        labelPath: 'ExternalReference',
                    } as Property,
                    Float: {
                        label: 'Float',
                        data: { type: GraphQLTypes.Float, canFilter: true, canSort: true },
                        id: 'Float',
                        labelPath: 'Float',
                    } as Property,
                    Id: { label: 'Id', data: { type: 'ID' }, id: 'Id', labelPath: 'Id' } as Property,
                    _InputStream: {
                        label: '_InputStream',
                        data: { type: 'InputStream' },
                        id: '_InputStream',
                        labelPath: '_InputStream',
                    } as Property,
                    Int: {
                        label: 'Int',
                        data: { type: GraphQLTypes.Int, canFilter: true, canSort: true },
                        id: 'Int',
                        labelPath: 'Int',
                    } as Property,
                    IntReference: {
                        label: 'IntReference',
                        data: { type: GraphQLTypes.IntReference, canFilter: true, canSort: true },
                        id: 'IntReference',
                        labelPath: 'IntReference',
                    } as Property,
                    Json: {
                        label: 'Json',
                        data: { type: GraphQLTypes.Json, canFilter: true, canSort: true },
                        id: 'Json',
                        labelPath: 'Json',
                    } as Property,
                    String: {
                        label: 'String',
                        data: { type: GraphQLTypes.String, canFilter: true, canSort: true },
                        id: 'String',
                        labelPath: 'String',
                    } as Property,
                    IntOrString: {
                        label: 'IntOrString',
                        data: { type: GraphQLTypes.IntOrString, canFilter: true, canSort: true },
                        id: 'IntOrString',
                        labelPath: 'IntOrString',
                    } as Property,
                },
            };
            mockStore = getMockStore(mockState);
        });

        afterEach(() => {
            jest.useRealTimers();
            jest.resetAllMocks();
            applyActionMocks();
        });

        const setup = (storeCustomizer?: (state: XtremAppState) => void) => {
            if (storeCustomizer) {
                storeCustomizer(mockState);
                mockStore = getMockStore(mockState);
            }
            return render(
                <Provider store={mockStore}>
                    <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
        };

        it('should not be able to get to the filters screen without selecting any column in the content screen', async () => {
            const { findByText, findByTestId } = setup(state => {
                state.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetDefinition.columns = [];
            });
            fireEvent.click(await findByTestId('e-widget-editor-dialog-next'));
            fireEvent.click(await findByText('Select the data to add to your widget', { exact: false }));
            fireEvent.click(await findByTestId('e-widget-editor-dialog-next'));
            fireEvent.click(await findByText('Add your content', { exact: false }));
            expect(((await findByTestId('e-widget-editor-dialog-next')) as HTMLButtonElement).disabled).toBe(true);
        });

        it('should be able to set filters in global state', async () => {
            mockDOMRect(40, 100, 'select-list-scrollable-container');
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
            const { findByText, findByTestId } = setup();
            // go to filter tab
            fireEvent.click(await findByTestId('e-widget-editor-dialog-next'));
            fireEvent.click(await findByText('Select the data to add to your widget', { exact: false }));
            fireEvent.click(await findByTestId('e-widget-editor-dialog-next'));
            fireEvent.click(await findByText('Add your content', { exact: false }));
            fireEvent.click(await findByTestId('e-widget-editor-dialog-next'));
            fireEvent.click(await findByText('Add your filters', { exact: false }));
            // click on add filter
            fireEvent.click(await findByTestId('add-item-button'));
            // set first filter to 'String' | 'Starts with' | 'lookup'
            let filterPropertyInput = (await findByTestId('e-widget-editor-filter-property-1')) as HTMLInputElement;
            await userEvent.type(filterPropertyInput, 'String{Enter}', {
                initialSelectionStart: 0,
                initialSelectionEnd: filterPropertyInput.value.length,
            });
            jest.runOnlyPendingTimers();

            let filterTypeInput = (await findByTestId('e-widget-editor-filter-type-1')) as HTMLInputElement;
            await userEvent.type(filterTypeInput, 'Starts with{Enter}', {
                initialSelectionStart: 0,
                initialSelectionEnd: filterTypeInput.value.length,
            });
            jest.runOnlyPendingTimers();

            let filterValueInput = (await findByTestId('e-widget-editor-filter-value-1')) as HTMLInputElement;
            await userEvent.type(filterValueInput, 'lookup', {
                initialSelectionStart: 0,
                initialSelectionEnd: filterValueInput.value.length,
            });

            await waitFor(() => {
                expect(updateUserWidgetDefinition).toHaveBeenLastCalledWith(
                    expect.objectContaining({
                        filters: [
                            {
                                _id: '1',
                                data: { canFilter: true, canSort: true, type: 'String' },
                                filterType: 'startsWith',
                                filterValue: 'lookup',
                                id: 'String',
                                key: undefined,
                                label: 'String',
                                labelKey: undefined,
                                labelPath: 'String',
                                property: {
                                    data: { canFilter: true, canSort: true, type: 'String' },
                                    id: 'String',
                                    label: 'String',
                                    labelPath: 'String',
                                },
                            },
                        ],
                    }),
                    'home',
                );
            });
            // add new line
            fireEvent.click(await findByTestId('flat-table-add-button'));
            // set second filter to 'Decimal' | 'Greater than' | 'a'
            filterPropertyInput = (await findByTestId('e-widget-editor-filter-property-2')) as HTMLInputElement;
            await userEvent.type(filterPropertyInput, 'Decimal{Enter}', {
                initialSelectionStart: 0,
                initialSelectionEnd: filterPropertyInput.value.length,
            });
            jest.runOnlyPendingTimers();

            filterTypeInput = (await findByTestId('e-widget-editor-filter-type-2')) as HTMLInputElement;
            await userEvent.type(filterTypeInput, 'Greater than{Enter}', {
                initialSelectionStart: 0,
                initialSelectionEnd: filterTypeInput.value.length,
            });
            jest.runOnlyPendingTimers();

            filterValueInput = (await findByTestId('e-widget-editor-filter-value-2')) as HTMLInputElement;
            await userEvent.type(filterValueInput, 'a', {
                initialSelectionStart: 0,
                initialSelectionEnd: filterValueInput.value.length,
            });

            // 'a' is invalid so value is not set
            expect(updateUserWidgetDefinition).toHaveBeenLastCalledWith(
                expect.objectContaining({
                    filters: [
                        {
                            _id: '1',
                            data: { canFilter: true, canSort: true, type: 'String' },
                            filterType: 'startsWith',
                            filterValue: 'lookup',
                            id: 'String',
                            key: undefined,
                            label: 'String',
                            labelKey: undefined,
                            labelPath: 'String',
                            property: {
                                data: { canFilter: true, canSort: true, type: 'String' },
                                id: 'String',
                                label: 'String',
                                labelPath: 'String',
                            },
                        },
                    ],
                }),
                'home',
            );

            // change value to => '1'
            await userEvent.type(filterValueInput, '1', {
                initialSelectionStart: 0,
                initialSelectionEnd: filterValueInput.value.length,
            });

            // test that both lines have been added
            expect(updateUserWidgetDefinition).toHaveBeenLastCalledWith(
                expect.objectContaining({
                    filters: [
                        {
                            _id: '1',
                            data: { canFilter: true, canSort: true, type: 'String' },
                            filterType: 'startsWith',
                            filterValue: 'lookup',
                            id: 'String',
                            key: undefined,
                            label: 'String',
                            labelKey: undefined,
                            labelPath: 'String',
                            property: {
                                data: { canFilter: true, canSort: true, type: 'String' },
                                id: 'String',
                                label: 'String',
                                labelPath: 'String',
                            },
                        },
                        {
                            _id: '2',
                            data: { canFilter: true, canSort: true, type: 'Decimal' },
                            filterType: 'greaterThan',
                            filterValue: '1',
                            id: 'Decimal',
                            key: undefined,
                            label: 'Decimal',
                            labelKey: undefined,
                            labelPath: 'Decimal',
                            property: {
                                data: { canFilter: true, canSort: true, type: 'Decimal' },
                                id: 'Decimal',
                                label: 'Decimal',
                                labelPath: 'Decimal',
                            },
                        },
                    ],
                }),
                'home',
            );
            // change first line to 'Boolean'
            filterPropertyInput = (await findByTestId('e-widget-editor-filter-property-1')) as HTMLInputElement;
            await userEvent.type(filterPropertyInput, 'Boolean{Enter}', {
                initialSelectionStart: 0,
                initialSelectionEnd: filterTypeInput.value.length,
            });
            jest.runOnlyPendingTimers();

            // test that filter type is set to 'Equals' & value is reset
            await waitFor(
                async () => {
                    expect(
                        ((await findByTestId('e-widget-editor-filter-type-1')) as HTMLParagraphElement).textContent,
                    ).toBe('Equals');
                    expect(((await findByTestId('e-widget-editor-filter-value-1')) as HTMLInputElement).value).toBe('');
                    expect(updateUserWidgetDefinition).toHaveBeenLastCalledWith(
                        expect.objectContaining({
                            filters: [
                                {
                                    _id: '2',
                                    data: { canFilter: true, canSort: true, type: 'Decimal' },
                                    filterType: 'greaterThan',
                                    filterValue: '1',
                                    id: 'Decimal',
                                    key: undefined,
                                    label: 'Decimal',
                                    labelKey: undefined,
                                    labelPath: 'Decimal',
                                    property: {
                                        data: { canFilter: true, canSort: true, type: 'Decimal' },
                                        id: 'Decimal',
                                        label: 'Decimal',
                                        labelPath: 'Decimal',
                                    },
                                },
                            ],
                        }),
                        'home',
                    );
                },
                { timeout: 5000, interval: 100 },
            );
        }, 90000);
    });
});
