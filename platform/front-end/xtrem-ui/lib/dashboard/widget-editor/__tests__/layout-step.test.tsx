const dashboardComponentMock = jest.fn();
jest.mock('../../async-drag-drop-canvas-wrapper', () => ({
    __esModule: true,
    default: (props: any) => {
        dashboardComponentMock(props);
        return <div data-testid="bms-dashboard">BMS dashboard placeholder</div>;
    },
}));
const goToMock = jest.fn();
jest.mock('../../../service/router', () => ({
    getRouter: () => ({
        goTo: goToMock,
    }),
    // eslint-disable-next-line func-names, object-shorthand
    Router: function () {},
}));

import * as React from 'react';
import { fireEvent, render, waitFor } from '@testing-library/react';
import type { WidgetEditorStepProps } from '../widget-editor-utils';
import { LayoutStep } from '../layout-step';
import * as dashboardService from '../../../service/dashboard-service';
import * as metadataService from '../../../service/metadata-service';
import * as toastService from '../../../service/toast-service';
import { Aggregations, type Property } from '@sage/xtrem-shared';
import '@testing-library/jest-dom';

describe('layout widget editor step', () => {
    let props: WidgetEditorStepProps;

    beforeEach(() => {
        dashboardComponentMock.mockClear();
        goToMock.mockClear();
        jest.spyOn(dashboardService, 'fetchWidgetData').mockResolvedValue({});
        jest.spyOn(metadataService, 'queryPagesByNodeType').mockResolvedValue([
            { key: '@sage/xtrem-test/MyTestPage', title: 'Test page' },
        ]);
        jest.spyOn(toastService, 'showToast').mockImplementation(jest.fn());
        props = {
            group: 'home',
            locale: 'en-US',
            nodeNames: {},
            onWidgetDefinitionUpdated: jest.fn(),
            stepIndex: 1,
            widgetDefinition: {
                type: 'INDICATOR_TILE',
                decimalDigits: 2,
                icon: 'add',
                title: 'Test widget',
                node: '@sage/xtrem-test/TestNode',
            },
        };
    });

    it('should render the title', () => {
        const { getByText } = render(<LayoutStep {...props} />);
        expect(getByText('1. Create your layout')).toBeInTheDocument();
    });

    describe('table widget', () => {
        beforeEach(() => {
            props.widgetDefinition.type = 'TABLE';
        });

        it('should not render tile specific fields', () => {
            const { queryByTestId } = render(<LayoutStep {...props} />);
            expect(queryByTestId('e-widget-editor-layout-icon')).toEqual(null);
            expect(queryByTestId('e-widget-editor-layout-subtitle')).toEqual(null);
        });

        it('should render the preview using the dashboard component', async () => {
            expect(dashboardComponentMock).not.toHaveBeenCalled();
            const { findByTestId } = render(<LayoutStep {...props} />);
            await findByTestId('bms-dashboard');
            expect(dashboardComponentMock).toHaveBeenCalledWith({
                json: expect.objectContaining({
                    widgets: [
                        expect.objectContaining({
                            data: expect.objectContaining({ mode: 'table' }),
                            i: 'PREVIEW_WIDGET',
                            title: 'Test widget',
                            type: 'table',
                            xxs: { x: 0, y: 0, w: 1, h: 1 },
                            xs: { x: 0, y: 0, w: 1, h: 1 },
                            sm: { x: 0, y: 0, w: 1, h: 1 },
                            md: { x: 0, y: 0, w: 1, h: 1 },
                            lg: { x: 0, y: 0, w: 1, h: 1 },
                        }),
                    ],
                }),
            });
        });
    });

    describe('indicator tile', () => {
        beforeEach(() => {
            props.widgetDefinition.type = 'INDICATOR_TILE';
        });

        it('should render the settings container', () => {
            props.widgetDefinition.type = 'INDICATOR_TILE';
            const { queryByTestId } = render(<LayoutStep {...props} />);
            expect(queryByTestId('e-layout-settings-container')).not.toEqual(null);
        });

        it('should render the preview using the dashboard component', async () => {
            expect(dashboardComponentMock).not.toHaveBeenCalled();
            const { findByTestId } = render(<LayoutStep {...props} />);
            await findByTestId('bms-dashboard');
            expect(dashboardComponentMock).toHaveBeenCalledWith({
                json: expect.objectContaining({
                    widgets: [
                        expect.objectContaining({
                            data: expect.objectContaining({
                                icon: {
                                    color: undefined,
                                    url: '/images/detailed-icons/90x90_add_green-on-transparent_icon.svg',
                                },
                            }),
                            i: 'PREVIEW_WIDGET',
                            type: 'simple-indicator',
                            xxs: { x: 0, y: 0, w: 1, h: 1 },
                            xs: { x: 0, y: 0, w: 1, h: 1 },
                            sm: { x: 0, y: 0, w: 1, h: 1 },
                            md: { x: 0, y: 0, w: 1, h: 1 },
                            lg: { x: 0, y: 0, w: 1, h: 1 },
                        }),
                    ],
                }),
            });
        });

        it('should be able to update the icon of the widget', () => {
            const { queryByTestId } = render(<LayoutStep {...props} />);
            expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
            fireEvent.change(queryByTestId('e-widget-editor-layout-icon') as HTMLInputElement, {
                target: { value: 'Asset management' },
            });
            fireEvent.blur(queryByTestId('e-widget-editor-layout-icon') as HTMLInputElement);
            expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledWith(
                expect.objectContaining({ icon: 'asset_mgt' }),
            );
        });

        it('should set the subtitle', () => {
            const { queryByTestId } = render(<LayoutStep {...props} />);
            expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
            fireEvent.change(queryByTestId('e-widget-editor-layout-subtitle') as HTMLInputElement, {
                target: { value: 'New subtitle' },
            });
            expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
            fireEvent.blur(queryByTestId('e-widget-editor-layout-subtitle') as HTMLInputElement, {
                target: { value: 'New subtitle' },
            });
            expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledWith(
                expect.objectContaining({ subtitle: 'New subtitle' }),
            );
        });

        it('should enable decimal digits by default', () => {
            const { queryByTestId } = render(<LayoutStep {...props} />);

            expect(queryByTestId('e-widget-editor-layout-decimal-digits')!.attributes.getNamedItem('disabled')).toBe(
                null,
            );
        });

        it('should disable the decimal digits if distinct count is used', () => {
            props.widgetDefinition.groupBy = {
                method: Aggregations.distinctCount,
                property: {
                    label: 'Test label',
                    data: { type: 'Int' },
                    id: 'test',
                    labelPath: 'test',
                } as Property,
            };
            const { queryByTestId } = render(<LayoutStep {...props} />);

            expect(
                queryByTestId('e-widget-editor-layout-decimal-digits')!.attributes.getNamedItem('disabled'),
            ).not.toBe(null);
        });

        it('should disable the decimal digits if the aggregation is on a string property', () => {
            props.widgetDefinition.groupBy = {
                method: Aggregations.distinctCount,
                property: {
                    label: 'Test label',
                    data: { type: 'String' },
                    id: 'test',
                    labelPath: 'test',
                } as Property,
            };
            const { queryByTestId } = render(<LayoutStep {...props} />);

            expect(
                queryByTestId('e-widget-editor-layout-decimal-digits')!.attributes.getNamedItem('disabled'),
            ).not.toBe(null);
        });
    });

    describe('actions', () => {
        it('should disable the action details if the action is disabled', async () => {
            const { queryByTestId } = render(<LayoutStep {...props} />);

            await waitFor(() => {
                expect(queryByTestId('e-widget-editor-layout-title-seeAllAction')).not.toBeNull();
            });

            expect(queryByTestId('e-widget-editor-layout-title-seeAllAction')!).toHaveAttribute('disabled');
            expect(queryByTestId('e-widget-editor-layout-page-seeAllAction')!).toHaveAttribute('disabled');
            expect(queryByTestId('e-widget-editor-layout-title-createAction')!).toHaveAttribute('disabled');
            expect(queryByTestId('e-widget-editor-layout-page-createAction')!).toHaveAttribute('disabled');
        });

        it('should disable the action details if the action is disabled', async () => {
            props.widgetDefinition.createAction = { isEnabled: true };
            const { queryByTestId } = render(<LayoutStep {...props} />);

            await waitFor(() => {
                expect(queryByTestId('e-widget-editor-layout-title-seeAllAction')).not.toBeNull();
            });

            expect(queryByTestId('e-widget-editor-layout-title-seeAllAction')!).toHaveAttribute('disabled');
            expect(queryByTestId('e-widget-editor-layout-page-seeAllAction')!).toHaveAttribute('disabled');
            expect(queryByTestId('e-widget-editor-layout-title-createAction')!).not.toHaveAttribute('disabled');
            expect(queryByTestId('e-widget-editor-layout-page-createAction')!).not.toHaveAttribute('disabled');
        });

        it('should set default title when action gets enabled', async () => {
            const { queryByTestId } = render(<LayoutStep {...props} />);

            await waitFor(() => {
                expect(queryByTestId('e-widget-editor-layout-title-seeAllAction')).not.toBeNull();
            });

            expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
            fireEvent.click(queryByTestId('e-widget-editor-layout-isEnabled-createAction')!);
            expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledWith({
                createAction: {
                    isEnabled: true,
                    title: 'Create',
                },
                decimalDigits: 2,
                icon: 'add',
                node: '@sage/xtrem-test/TestNode',
                title: 'Test widget',
                type: 'INDICATOR_TILE',
            });

            expect((queryByTestId('e-widget-editor-layout-title-createAction')! as HTMLInputElement).value).toEqual(
                'Create',
            );
        });

        it('should set default title when action gets enabled', async () => {
            const { queryByTestId } = render(<LayoutStep {...props} />);

            await waitFor(() => {
                expect(queryByTestId('e-widget-editor-layout-title-seeAllAction')).not.toBeNull();
            });

            expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
            fireEvent.click(queryByTestId('e-widget-editor-layout-isEnabled-createAction')!);
            expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledWith({
                createAction: {
                    isEnabled: true,
                    title: 'Create',
                },
                decimalDigits: 2,
                icon: 'add',
                node: '@sage/xtrem-test/TestNode',
                title: 'Test widget',
                type: 'INDICATOR_TILE',
            });

            expect((queryByTestId('e-widget-editor-layout-title-createAction')! as HTMLInputElement).value).toEqual(
                'Create',
            );
        });

        it('should set the the page title', async () => {
            props.widgetDefinition.createAction = { isEnabled: true };
            const { queryByTestId } = render(<LayoutStep {...props} />);

            await waitFor(() => {
                expect(queryByTestId('e-widget-editor-layout-title-seeAllAction')).not.toBeNull();
            });

            expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
            fireEvent.change(queryByTestId('e-widget-editor-layout-title-createAction') as HTMLInputElement, {
                target: { value: 'New page title' },
            });
            expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
            fireEvent.blur(queryByTestId('e-widget-editor-layout-title-createAction') as HTMLInputElement, {
                target: { value: 'New page title' },
            });
            expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledWith(
                expect.objectContaining({ createAction: { isEnabled: true, title: 'New page title' } }),
            );
        });

        it('should render the preview and add create action', async () => {
            props.widgetDefinition.createAction = {
                isEnabled: true,
                title: 'CREATE',
                page: '@sage/xtrem-test/MyTestPage',
            };
            expect(dashboardComponentMock).not.toHaveBeenCalled();
            const { findByTestId, queryByTestId } = render(<LayoutStep {...props} />);

            await waitFor(() => {
                expect(queryByTestId('e-widget-editor-layout-title-seeAllAction')).not.toBeNull();
            });

            await findByTestId('bms-dashboard');
            expect(dashboardComponentMock).toHaveBeenCalledWith({
                json: expect.objectContaining({
                    widgets: [
                        expect.objectContaining({
                            callToActions: {
                                createAction: {
                                    isDisabled: false,
                                    isHidden: false,
                                    onClick: expect.any(Function),
                                    title: 'CREATE',
                                },
                            },
                        }),
                    ],
                }),
            });
        });

        it('should the CTA listener should display a toast if triggered from the preview', async () => {
            props.widgetDefinition.createAction = {
                isEnabled: true,
                title: 'CREATE',
                page: '@sage/xtrem-test/MyTestPage',
            };
            expect(dashboardComponentMock).not.toHaveBeenCalled();
            const { findByTestId } = render(<LayoutStep {...props} />);
            await findByTestId('bms-dashboard');
            expect(toastService.showToast).not.toHaveBeenCalled();
            expect(toastService.showToast).not.toHaveBeenCalled();
            dashboardComponentMock.mock.calls[0][0].json.widgets[0].callToActions.createAction.onClick();
            expect(toastService.showToast).toHaveBeenCalledWith(
                'Actions do not work in the preview. They work when you add the widget to your dashboard.',
                { type: 'info' },
            );
        });

        it('should not render the CTA config options if no page is available for the selected node', async () => {
            const { queryByTestId } = render(<LayoutStep {...props} />);

            await waitFor(() => {
                expect(queryByTestId('e-widget-editor-layout-subtitle')).not.toBeNull();
                expect(queryByTestId('e-widget-editor-layout-title-seeAllAction')).toBeNull();
                expect(queryByTestId('e-widget-editor-layout-title-create')).toBeNull();
            });
        });
    });
});
