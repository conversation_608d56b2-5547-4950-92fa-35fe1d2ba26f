import * as React from 'react';
import { render } from '@testing-library/react';
import type { WidgetEditorStepProps } from '../widget-editor-utils';
import { DataStep } from '../data-step';
import '@testing-library/jest-dom';

describe('data widget editor step', () => {
    let props: WidgetEditorStepProps;

    beforeEach(() => {
        props = {
            group: 'home',
            locale: 'en-US',
            nodeNames: {},
            onWidgetDefinitionUpdated: jest.fn(),
            stepIndex: 1,
            widgetDefinition: { node: '@sage/xtrem-show-case/ShowCaseProduct' },
        };
    });

    it('should render the title', () => {
        const { getByText } = render(<DataStep {...props} />);
        expect(getByText('1. Select the data to add to your widget')).toBeInTheDocument();
    });
});
