import * as React from 'react';
import { render, cleanup, fireEvent } from '@testing-library/react';
import type { SortingStepProps } from '../sorting-step';
import { GraphQLTypes } from '../../../types';
import { SortingStep } from '../sorting-step';
import * as i18n from '../../../service/i18n-service';
import type { Property } from '@sage/xtrem-shared';
import '@testing-library/jest-dom';

describe('sorting widget editor step', () => {
    let props: SortingStepProps;

    beforeEach(() => {
        cleanup();
        jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);

        props = {
            group: 'home',
            locale: 'en-US',
            nodeNames: {},
            browserIs: {
                l: true,
                m: false,
                s: false,
                xs: false,
            },
            onWidgetDefinitionUpdated: jest.fn(),
            stepIndex: 5,
            widgetDefinition: {
                type: 'TABLE',
                selectedProperties: {
                    Boolean: {
                        label: 'Boolean',
                        data: { type: GraphQLTypes.Boolean, canFilter: true, canSort: true },
                        id: 'Boolean',
                        labelPath: 'Boolean',
                    } as Property,
                    Date: {
                        label: 'Date',
                        data: { type: GraphQLTypes.Date },
                        id: 'Date',
                        labelPath: 'Date',
                    } as Property,
                    DateTime: {
                        label: 'DateTime',
                        data: { type: GraphQLTypes.DateTime, canFilter: true, canSort: true },
                        id: 'DateTime',
                        labelPath: 'DateTime',
                    } as Property,
                    Decimal: {
                        label: 'Decimal',
                        data: { type: GraphQLTypes.Decimal, canFilter: true, canSort: true },
                        id: 'Decimal',
                        labelPath: 'Decimal',
                    } as Property,
                    Enum: {
                        label: 'Enum',
                        data: { type: GraphQLTypes.Enum },
                        id: 'Enum',
                        labelPath: 'Enum',
                    } as Property,
                    ExternalReference: {
                        label: 'ExternalReference',
                        data: { type: GraphQLTypes.ExternalReference, canFilter: true, canSort: true },
                        id: 'ExternalReference',
                        labelPath: 'ExternalReference',
                    } as Property,
                    Float: {
                        label: 'Float',
                        data: { type: GraphQLTypes.Float },
                        id: 'Float',
                        labelPath: 'Float',
                    } as Property,
                    Id: { label: 'Id', data: { type: 'ID' }, id: 'Id', labelPath: 'Id' } as Property,
                    _InputStream: {
                        label: '_InputStream',
                        data: { type: 'InputStream', canFilter: true, canSort: true },
                        id: '_InputStream',
                        labelPath: '_InputStream',
                    } as Property,
                    Int: { label: 'Int', data: { type: GraphQLTypes.Int }, id: 'Int', labelPath: 'Int' } as Property,
                    IntReference: {
                        label: 'IntReference',
                        data: { type: GraphQLTypes.IntReference, canFilter: true, canSort: true },
                        id: 'IntReference',
                        labelPath: 'IntReference',
                    } as Property,
                    Json: {
                        label: 'Json',
                        data: { type: GraphQLTypes.Json },
                        id: 'Json',
                        labelPath: 'Json',
                    } as Property,
                    String: {
                        label: 'String',
                        data: { type: GraphQLTypes.String, canFilter: true, canSort: true },
                        id: 'String',
                        labelPath: 'String',
                    } as Property,
                    IntOrString: {
                        label: 'IntOrString',
                        data: { type: GraphQLTypes.IntOrString, canFilter: true, canSort: true },
                        id: 'IntOrString',
                        labelPath: 'IntOrString',
                    } as Property,
                },
            },
        };
    });

    it('should render the title', async () => {
        const { getByText } = render(<SortingStep {...props} />);
        expect(getByText('Define sorting', { exact: false })).toBeInTheDocument();
    });

    it('should not render if widget is not a table', async () => {
        props.widgetDefinition.type = 'INDICATOR_TILE';
        const { queryByTestId } = render(<SortingStep {...props} />);
        expect(queryByTestId('e-widget-editor-step-title')).not.toBeInTheDocument();
    });

    it('should render the add button & empty table text', async () => {
        const { findByRole, findByText } = render(<SortingStep {...props} />);
        await findByRole('button', { name: 'Add a sort condition' });
        await findByText('No data to display');
    });

    it('should not render the add button & empty table text if no sortable properties available', async () => {
        props.widgetDefinition.selectedProperties = {
            String: {
                label: 'String',
                data: { type: GraphQLTypes.String, canFilter: true, canSort: false },
                id: 'String',
                labelPath: 'String',
            } as Property,
            IntOrString: {
                label: 'IntOrString',
                data: { type: GraphQLTypes.IntOrString, canFilter: true, canSort: false },
                id: 'IntOrString',
                labelPath: 'IntOrString',
            } as Property,
        };
        const { findByText, queryByText } = render(<SortingStep {...props} />);

        await findByText(
            'You cannot sort the current values. You can select different data or continue without sorting.',
        );

        expect(queryByText('Add a sort condition')).not.toBeInTheDocument();
        expect(queryByText('No data to display')).not.toBeInTheDocument();
    });

    it('should render a table row after clicking on the "Add sort condition" button', async () => {
        const { findByRole, findByText, findAllByPlaceholderText, queryByText, findAllByTestId } = render(
            <SortingStep {...props} />,
        );
        await findByText('No data to display');
        fireEvent.click(await findByRole('button', { name: 'Add a sort condition' }));
        expect(queryByText('Add a sort condition')).not.toBeInTheDocument();
        expect(queryByText('No data to display')).not.toBeInTheDocument();
        await findByText('Property', { selector: 'th > div > div > div' });
        await findByText('Order', { selector: 'th > div > div > div' });
        await findByText('Actions', { selector: 'th > div' });
        expect((await findAllByPlaceholderText('Select property...')).length).toBe(1);
        expect((await findAllByPlaceholderText('Select sort order...')).length).toBe(1);
        expect((await findAllByTestId('flat-table-remove-button')).length).toBe(1);
        expect((await findAllByTestId('flat-table-add-button')).length).toBe(1);
    });

    it('should be able to add multiple rows', async () => {
        const { findByRole, findAllByTestId, findByTestId } = render(<SortingStep {...props} />);
        fireEvent.click(await findByRole('button', { name: 'Add a sort condition' }));
        fireEvent.click(await findByTestId('flat-table-add-button'));
        expect((await findAllByTestId('flat-table-remove-button')).length).toBe(2);
        expect((await findAllByTestId('flat-table-add-button')).length).toBe(1);
    });

    it('should be able to delete rows', async () => {
        const { findByRole, findAllByTestId, findByTestId, queryByTestId } = render(<SortingStep {...props} />);
        fireEvent.click(await findByRole('button', { name: 'Add a sort condition' }));
        fireEvent.click(await findByTestId('flat-table-add-button'));
        expect((await findAllByTestId('flat-table-remove-button')).length).toBe(2);
        expect((await findAllByTestId('flat-table-add-button')).length).toBe(1);
        fireEvent.click((await findAllByTestId('flat-table-remove-button'))[0]);
        expect((await findAllByTestId('flat-table-remove-button')).length).toBe(1);
        expect((await findAllByTestId('flat-table-add-button')).length).toBe(1);
        fireEvent.click(await findByTestId('flat-table-remove-button'));
        expect(queryByTestId('flat-table-add-button')).not.toBeInTheDocument();
        expect(queryByTestId('flat-table-remove-button')).not.toBeInTheDocument();
        await findByRole('button', { name: 'Add a sort condition' });
    });
});
