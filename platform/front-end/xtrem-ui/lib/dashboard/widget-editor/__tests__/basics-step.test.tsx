import * as React from 'react';
import { render, fireEvent, cleanup, waitFor } from '@testing-library/react';
import type { BasicsStepProps } from '../basics-step';
import { BasicsStep } from '../basics-step';
import type { OrderByProperty } from '../../../redux/state';
import '@testing-library/jest-dom';

const testDashboardGroup = 'home';

describe('basics widget editor step', () => {
    let props: BasicsStepProps;
    beforeEach(() => {
        cleanup();
        props = {
            group: testDashboardGroup,
            isNewWidget: true,
            locale: 'en-US',
            browserIs: {
                l: true,
                m: false,
                s: false,
                xs: false,
            },
            nodeNames: {
                '@sage/xtrem-test/ATestNode': 'A Test Node',
                '@sage/xtrem-test/AnotherTestNode': 'Another test node',
                '@sage/xtrem-test/ThirdTestNode': 'Third test node',
            },
            onWidgetDefinitionUpdated: jest.fn(),
            stepIndex: 1,
            widgetCategories: {
                SOME_CATEGORY: 'Some Category',
                ANOTHER_CATEGORY: 'Another Category',
            },
            widgetDefinition: {},
        };
    });

    it('should render the title', () => {
        const { getByText } = render(<BasicsStep {...props} />);
        expect(getByText('1. Select a widget to get started')).toBeInTheDocument();
    });

    it('should render without data', () => {
        const { queryByTestId } = render(<BasicsStep {...props} />);
        expect((queryByTestId('e-widget-editor-basic-title') as HTMLInputElement).value).toEqual('');
        expect((queryByTestId('e-widget-editor-basic-category') as HTMLInputElement).value).toEqual('');
        expect((queryByTestId('e-widget-editor-basic-node') as HTMLInputElement).value).toEqual('');
    });

    it('should render with data', () => {
        props.widgetDefinition = {
            ...props.widgetDefinition,
            title: 'Test title',
            node: '@sage/xtrem-test/AnotherTestNode',
            category: 'SOME_CATEGORY',
        };
        const { queryByTestId } = render(<BasicsStep {...props} />);
        expect((queryByTestId('e-widget-editor-basic-title') as HTMLInputElement).value).toEqual('Test title');
        expect((queryByTestId('e-widget-editor-basic-category') as HTMLInputElement).value).toEqual('Some Category');
        expect((queryByTestId('e-widget-editor-basic-node') as HTMLInputElement).value).toEqual('Another test node');
    });

    it('should set the title', () => {
        const { queryByTestId } = render(<BasicsStep {...props} />);
        expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
        fireEvent.change(queryByTestId('e-widget-editor-basic-title') as HTMLInputElement, {
            target: { value: 'New title' },
        });
        expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
        fireEvent.blur(queryByTestId('e-widget-editor-basic-title') as HTMLInputElement, {
            target: { value: 'New title' },
        });
        expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledWith({ title: 'New title' });
    });

    it('should validate the title', async () => {
        const { queryByTestId, baseElement } = render(<BasicsStep {...props} />);
        fireEvent.change(queryByTestId('e-widget-editor-basic-title') as HTMLInputElement, { target: { value: '' } });
        expect(baseElement.querySelector('[data-element="error"]')).toEqual(null);
        fireEvent.blur(queryByTestId('e-widget-editor-basic-title') as HTMLInputElement, { target: { value: '' } });
        expect(baseElement.querySelector('[data-element="error"]')).not.toEqual(null);
        fireEvent.mouseEnter(baseElement.querySelector('[data-element="error"]')!);
        await waitFor(() => {
            expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('You need to add a title');
        });
    });

    it('should set the category', () => {
        const { queryByTestId } = render(<BasicsStep {...props} />);
        expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
        fireEvent.change(queryByTestId('e-widget-editor-basic-category') as HTMLInputElement, {
            target: { value: 'Another Category' },
        });
        fireEvent.blur(queryByTestId('e-widget-editor-basic-category') as HTMLInputElement);
        expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledWith({ category: 'ANOTHER_CATEGORY' });
    });

    it('should set the node', () => {
        const { queryByTestId } = render(<BasicsStep {...props} />);
        expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
        fireEvent.change(queryByTestId('e-widget-editor-basic-node') as HTMLInputElement, {
            target: { value: 'Another test node' },
        });
        fireEvent.blur(queryByTestId('e-widget-editor-basic-node') as HTMLInputElement);
        expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledWith({ node: '@sage/xtrem-test/AnotherTestNode' });
    });

    it('should reset any non base setting when the node changes', () => {
        props.widgetDefinition.title = 'Test';
        props.widgetDefinition.type = 'INDICATOR_TILE';
        props.widgetDefinition.subtitle = 'subtitle';
        props.widgetDefinition.orderBy = [
            { data: {}, id: 'test', label: 'Test', labelPath: 'test.path', order: 'ascending' } as OrderByProperty,
        ];
        const { queryByTestId } = render(<BasicsStep {...props} />);
        expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
        fireEvent.change(queryByTestId('e-widget-editor-basic-node') as HTMLInputElement, {
            target: { value: 'Another test node' },
        });
        fireEvent.blur(queryByTestId('e-widget-editor-basic-node') as HTMLInputElement);
        expect((props.onWidgetDefinitionUpdated as unknown as jest.SpyInstance<any, any>).mock.calls[0][0]).toEqual({
            node: '@sage/xtrem-test/AnotherTestNode',
            title: 'Test',
            type: 'INDICATOR_TILE',
        });
    });

    it('should not render the node input if it is an existing widget', () => {
        const { queryByTestId } = render(<BasicsStep {...props} isNewWidget={false} />);
        expect(queryByTestId('e-widget-editor-basic-node')).toBeNull();
    });

    it('should set the widget type', () => {
        const { queryByTestId } = render(<BasicsStep {...props} />);
        expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();

        fireEvent.click(queryByTestId('e-selection-card-TABLE')!);
        expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledWith({ type: 'TABLE' });
    });

    it('should reset any non base setting when the type changes', () => {
        props.widgetDefinition.title = 'Test';
        props.widgetDefinition.subtitle = 'subtitle';
        props.widgetDefinition.node = '@sage/xtrem-test/AnotherTestNode';
        props.widgetDefinition.orderBy = [
            { data: {}, id: 'test', label: 'Test', labelPath: 'test.path', order: 'ascending' } as OrderByProperty,
        ];
        const { queryByTestId } = render(<BasicsStep {...props} />);
        expect(props.onWidgetDefinitionUpdated).not.toHaveBeenCalled();
        fireEvent.click(queryByTestId('e-selection-card-TABLE')!);
        expect((props.onWidgetDefinitionUpdated as unknown as jest.SpyInstance<any, any>).mock.calls[0][0]).toEqual({
            type: 'TABLE',
            title: 'Test',
            node: '@sage/xtrem-test/AnotherTestNode',
        });
    });

    it('should render the current widget type button selected ', () => {
        props.widgetDefinition.type = 'TABLE';
        const { queryByTestId } = render(<BasicsStep {...props} />);
        expect(queryByTestId('e-selection-card-TABLE')!.classList.contains('e-selection-card-selected')).toBe(true);
        expect(queryByTestId('e-selection-card-INDICATOR_TILE')!.classList.contains('e-selection-card-selected')).toBe(
            false,
        );
    });

    it('should not render the widget type selection cardst if it is an existing widget', () => {
        const { queryByTestId } = render(<BasicsStep {...props} isNewWidget={false} />);
        expect(queryByTestId('e-selection-card-TABLE')).toBeNull();
        expect(queryByTestId('e-selection-card-INDICATOR_TILE')).toBeNull();
    });
});
