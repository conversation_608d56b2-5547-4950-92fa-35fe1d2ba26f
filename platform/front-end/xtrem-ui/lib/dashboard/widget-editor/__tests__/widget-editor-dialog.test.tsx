const dashboardComponentMock = jest.fn();
jest.mock('../../async-drag-drop-canvas-wrapper', () => ({
    __esModule: true,
    default: (props: any) => {
        dashboardComponentMock(props);
        return <div data-testid="bms-dashboard">BMS dashboard placeholder</div>;
    },
}));

import { render, fireEvent, waitFor } from '@testing-library/react';
import { applyActionMocks, getMockState, getMockStore } from '../../../__tests__/test-helpers';
import React from 'react';
import type { XtremAppState } from '../../../redux';
import { Provider } from 'react-redux';
import * as i18n from '../../../service/i18n-service';

jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);

import * as xtremRedux from '../../../redux';
import { ConnectedWidgetEditorDialog } from '../widget-editor-dialog';
import * as nodeInformationService from '../../../service/node-information-service';
import { FieldKey } from '../../../component/types';
import type { AggregationProperty, OrderByProperty, TableProperty } from '../../../redux/state';
import { Aggregations, GraphQLTypes, type FilterProperty, type Property } from '@sage/xtrem-shared';
import { GraphQLKind } from '../../../types';
import '@testing-library/jest-dom';

const testDashboardGroup = 'home';

describe('widget editor dialog', () => {
    let mockStore;
    let mockState: XtremAppState;

    beforeEach(() => {
        jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);
        jest.spyOn(nodeInformationService, 'fetchNodeDetails').mockResolvedValue({});
        mockState = getMockState();
        mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.isOpen = true;
        mockState.dashboard.nodeNames = {
            '@sage/xtrem-test/ATestNode': 'ZZZZZZ',
            '@sage/xtrem-test/AnotherTestNode': 'AAAA',
            '@sage/xtrem-test/ThirdTestNode': 'BBBB',
        };
        mockState.dashboard.widgetCategories = {
            SOME_CATEGORY: 'Some Category',
            ANOTHER_CATEGORY: 'Another Category',
        };
        mockStore = getMockStore(mockState);
    });

    afterEach(() => {
        jest.clearAllMocks();
        applyActionMocks();
    });

    describe('cancel / close button', () => {
        it('should close the dialog if cancel button is clicked', async () => {
            expect(xtremRedux.actions.closeWidgetEditorDialog).not.toHaveBeenCalled();
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
            await waitFor(() => {
                expect(queryByTestId('e-widget-editor-dialog-cancel')).not.toBeNull();
            });
            fireEvent.click(queryByTestId('e-widget-editor-dialog-cancel')!);
            await waitFor(() => {
                expect(xtremRedux.actions.closeWidgetEditorDialog).toHaveBeenCalled();
            });
        });

        it('should close the dialog if close button is clicked', async () => {
            expect(xtremRedux.actions.closeWidgetEditorDialog).not.toHaveBeenCalled();
            const { baseElement } = render(
                <Provider store={mockStore}>
                    <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
            await waitFor(() => {
                expect(baseElement.querySelector('[data-element="close"]')).not.toBeNull();
            });
            fireEvent.click(baseElement.querySelector('[data-element="close"]')!);
            await waitFor(() => {
                expect(xtremRedux.actions.closeWidgetEditorDialog).toHaveBeenCalled();
            });
        });
    });

    describe('next button / previous', () => {
        it('should be disabled if the current step is invalid', () => {
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                </Provider>,
            );

            expect(queryByTestId('e-widget-editor-dialog-next')!.attributes.getNamedItem('disabled')).not.toBeNull();
        });

        it('should be enabled if the current step is valid', () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetDefinition = {
                category: 'SOME_CATEGORY',
                title: 'Test title',
                type: 'INDICATOR_TILE',
                node: '@sage/xtrem-test/ATestNode',
            };
            mockStore = getMockStore(mockState);
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                </Provider>,
            );

            expect(queryByTestId('e-widget-editor-dialog-next')!.attributes.getNamedItem('disabled')).toBeNull();
        });

        it('should accept old properties format', async () => {
            const columns: any = [
                {
                    data: {
                        canFilter: false,
                        canSort: false,
                        isCollection: false,
                        kind: GraphQLKind.Scalar,
                        label: '_id',
                        name: '_id',
                        type: 'Id',
                    },
                    label: '_id',
                    labelPath: '_id',
                    title: 'id',
                    path: '_id',
                    presentation: FieldKey.Text,
                },
                {
                    data: {
                        canFilter: false,
                        canSort: false,
                        isCollection: false,
                        kind: GraphQLKind.Scalar,
                        label: 'numericField',
                        name: 'numericField',
                        type: GraphQLTypes.Int,
                    },
                    label: 'Numeric',
                    labelPath: 'numericField',
                    title: 'Numeric',
                    path: 'numericField',
                    presentation: FieldKey.Numeric,
                },
                {
                    data: {
                        canFilter: false,
                        canSort: false,
                        isCollection: false,
                        kind: GraphQLKind.Scalar,
                        label: 'textField',
                        name: 'textField',
                        type: GraphQLTypes.String,
                    },
                    label: 'Text',
                    labelPath: 'textField',
                    title: 'Text',
                    path: 'textField',
                    presentation: FieldKey.Text,
                },
            ];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetDefinition = {
                category: 'SOME_CATEGORY',
                columns,
                node: '@sage/xtrem-test/ATestNode',
                selectedProperties: columns.reduce((accu, next) => {
                    return { ...accu, [next.path]: next };
                }, {}),
                title: 'Test title',
                type: 'TABLE',
            };
            mockStore = getMockStore(mockState);
            const { queryByTestId, queryByText, findByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                </Provider>,
            );

            fireEvent.click(queryByTestId('e-widget-editor-dialog-next')!);
            await waitFor(async () => {
                expect(queryByText('Data')).not.toBe(null);
            });
            fireEvent.click(queryByTestId('e-widget-editor-dialog-next')!);
            await waitFor(async () => {
                expect(queryByText('Content')).not.toBe(null);
            });
            await waitFor(() => {
                expect(xtremRedux.actions.updateUserWidgetDefinition).toHaveBeenCalledTimes(1);
                expect(xtremRedux.actions.updateUserWidgetDefinition).toHaveBeenNthCalledWith(
                    1,
                    expect.objectContaining({ columns }),
                    'home',
                );
            });
            expect(((await findByTestId('e-widget-editor-content-property-1')) as HTMLInputElement).value).toBe('_id');
            expect(((await findByTestId('e-widget-editor-content-property-2')) as HTMLInputElement).value).toBe(
                'Numeric',
            );
            expect(((await findByTestId('e-widget-editor-content-property-3')) as HTMLInputElement).value).toBe('Text');
        });

        it('should clear invalid properties upon data selection', async () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetDefinition = {
                category: 'SOME_CATEGORY',
                title: 'Test title',
                type: 'TABLE',
                node: '@sage/xtrem-test/ATestNode',
                selectedProperties: {
                    numericField: {
                        data: { canFilter: true, canSort: true, type: 'Int' },
                        label: 'Numeric',
                        id: 'numericField',
                        labelPath: 'numericField',
                    } as Property,
                },
                columns: [
                    {
                        data: { canFilter: true, canSort: true, type: 'String' },
                        label: 'Text',
                        id: 'textField',
                        title: 'Text',
                        presentation: FieldKey.Text,
                        labelPath: 'textField',
                    } as TableProperty,
                ],
                filters: [
                    {
                        data: { canFilter: true, canSort: true, type: 'String' },
                        label: 'Text',
                        id: 'textField',
                        labelPath: 'textField',
                        filterType: 'contains',
                        filterValue: 'patata',
                    } as FilterProperty,
                ],
                orderBy: [
                    {
                        data: { canFilter: true, canSort: true, type: 'String' },
                        label: 'Text',
                        id: 'textField',
                        labelPath: 'textField',
                        order: 'ascending',
                    } as OrderByProperty,
                ],
                aggregations: [
                    {
                        data: { canFilter: true, canSort: true, type: 'String' },
                        label: 'Text',
                        id: 'textField',
                        title: 'Text',
                        labelPath: 'textField',
                        groupingMethod: Aggregations.min,
                    } as AggregationProperty,
                ],
                xAxis: {
                    property: {
                        data: { canFilter: true, canSort: true, type: 'String' },
                        label: 'Text',
                        id: 'textField',
                        labelPath: 'textField',
                    } as Property,
                },
            };
            mockStore = getMockStore(mockState);
            const { queryByTestId, queryByText } = render(
                <Provider store={mockStore}>
                    <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                </Provider>,
            );

            fireEvent.click(queryByTestId('e-widget-editor-dialog-next')!);
            await waitFor(async () => {
                expect(queryByText('Data')).not.toBe(null);
            });
            fireEvent.click(queryByTestId('e-widget-editor-dialog-next')!);
            await waitFor(async () => {
                expect(queryByText('Content')).not.toBe(null);
            });
            await waitFor(() => {
                expect(xtremRedux.actions.updateUserWidgetDefinition).toHaveBeenCalledWith(
                    {
                        aggregations: [],
                        category: 'SOME_CATEGORY',
                        columns: [],
                        filters: [],
                        groupBy: undefined,
                        node: '@sage/xtrem-test/ATestNode',
                        orderBy: [],
                        selectedProperties: {
                            numericField: {
                                data: { canFilter: true, canSort: true, type: 'Int' },
                                id: 'numericField',
                                label: 'Numeric',
                                labelPath: 'numericField',
                            },
                        },
                        title: 'Test title',
                        type: 'TABLE',
                        xAxis: undefined,
                    },
                    'home',
                );
            });
        });

        it('should be enabled if the current step is valid', () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetDefinition = {
                category: 'SOME_CATEGORY',
                title: 'Test title',
                type: 'INDICATOR_TILE',
                node: '@sage/xtrem-test/ATestNode',
            };
            mockStore = getMockStore(mockState);

            const { getByText, queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                </Provider>,
            );

            expect(getByText('{{stepIndex}}. Select a widget to get started')).toBeInTheDocument();

            fireEvent.click(queryByTestId('e-widget-editor-dialog-next')!);

            expect(getByText('{{stepIndex}}. Select the data to add to your widget')).toBeInTheDocument();
        });

        it('should be able to step back', () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetDefinition = {
                category: 'SOME_CATEGORY',
                title: 'Test title',
                type: 'INDICATOR_TILE',
                node: '@sage/xtrem-test/ATestNode',
            };
            mockStore = getMockStore(mockState);

            const { getByText, queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                </Provider>,
            );

            expect(getByText('{{stepIndex}}. Select a widget to get started')).toBeInTheDocument();

            fireEvent.click(queryByTestId('e-widget-editor-dialog-next')!);

            expect(getByText('{{stepIndex}}. Select the data to add to your widget')).toBeInTheDocument();

            fireEvent.click(queryByTestId('e-widget-editor-dialog-previous')!);

            expect(getByText('{{stepIndex}}. Select a widget to get started')).toBeInTheDocument();
        });
    });

    describe('step indicator', () => {
        it('should not render the step indicator if no type was selected', () => {
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
            expect(queryByTestId('e-widget-editor-indicator-container')!.childElementCount).toEqual(0);
        });

        it('should render the right step indicators indicator tile type is selected', () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetDefinition.type =
                'INDICATOR_TILE';
            mockStore = getMockStore(mockState);
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
            const steps = Array.from(queryByTestId('e-widget-editor-indicator-container')!.querySelectorAll('li')).map(
                li => li.textContent,
            );
            expect(steps[0]).toEqual('1Widget');
            expect(steps[1]).toEqual('2Data');
            expect(steps[2]).toEqual('3Content');
            expect(steps[3]).toEqual('4Filters');
            expect(steps[4]).toEqual('5Layout');
        });

        it('should render the right step indicators table type is selected', () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetDefinition.type = 'TABLE';
            mockStore = getMockStore(mockState);
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
            const steps = Array.from(queryByTestId('e-widget-editor-indicator-container')!.querySelectorAll('li')).map(
                li => li.textContent,
            );
            expect(steps[0]).toEqual('1Widget');
            expect(steps[1]).toEqual('2Data');
            expect(steps[2]).toEqual('3Content');
            expect(steps[3]).toEqual('4Filters');
            expect(steps[4]).toEqual('5Sorting');
            expect(steps[5]).toEqual('6Layout');
        });
    });
});
