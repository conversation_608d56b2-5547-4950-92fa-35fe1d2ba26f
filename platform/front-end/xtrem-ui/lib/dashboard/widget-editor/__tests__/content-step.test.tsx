import * as React from 'react';
import { render, cleanup, waitFor, fireEvent } from '@testing-library/react';
import { applyActionMocks, getMockState, getMockStore, userEvent } from '../../../__tests__/test-helpers';
import * as i18n from '../../../service/i18n-service';
import * as nodeInformationService from '../../../service/node-information-service';
import { ContentStep } from '../content-step';
import { GraphQLTypes } from '../../../types';
import { ConnectedWidgetEditorDialog } from '../widget-editor-dialog';
import type { XtremAppState } from '../../../redux';
import { Provider } from 'react-redux';
import type { ContentStepProps } from '../content-step/content-utils';
import { aggregationTranslations } from '../content-step/indicator-tile-widget-content';
import { Aggregations, aggregationsGraphqlMapping } from '@sage/xtrem-shared';
import type { Property } from '@sage/xtrem-shared';
import '@testing-library/jest-dom';

const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect;
const testDashboardGroup = 'home';

function mockedGetBoundingClientRect(this: any) {
    const dataElement = this.getAttribute('data-element');
    if (dataElement === 'select-list-wrapper') {
        return {
            height: 1080,
            width: 1920,
        } as any;
    }
    return originalGetBoundingClientRect.bind(this)();
}

const setupResizeObserverMock = () => {
    if (!window) {
        return;
    }
    window.ResizeObserver =
        window.ResizeObserver ||
        jest.fn().mockImplementation((callback: ResizeObserverCallback) => {
            let hasCalledCallback = false;
            const observer: ResizeObserver = {
                disconnect: jest.fn(),
                // observe mock needs to actually call the callback straight away, as this is what a real ResizeObserver does
                // and this behaviour is needed for the FixedNavigationBarContextProvider to work properly.
                // Note that we must only call the callback once per ResizeObserver instance, to avoid stack overflows in
                // react-virtual.
                observe: jest.fn((target: Element) => {
                    if (!hasCalledCallback) {
                        hasCalledCallback = true;
                        callback([{ target } as ResizeObserverEntry], observer);
                    }
                }),
                unobserve: jest.fn(),
            };
            return observer;
        });
};

const mockDOMRect = (width: number, height: number, elementIdentifier: string) => {
    Element.prototype.getBoundingClientRect = jest.fn(function patata(this: HTMLElement) {
        if (this.getAttribute('data-component') === elementIdentifier) {
            return getDOMRect(width, height);
        }
        return getDOMRect(0, 0);
    });
};

const getDOMRect = (width: number, height: number): DOMRect => ({
    width,
    height,
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    x: 0,
    y: 0,
    toJSON: () => {},
});

/* function getTextValue(element: HTMLElement) {
    if (element instanceof HTMLInputElement) {
        return element.value;
    }
    return element.textContent;
} */

beforeEach(() => {
    jest.useFakeTimers();
});

afterEach(() => {
    jest.useRealTimers();
});

describe('content widget editor step', () => {
    describe('Table', () => {
        let props: ContentStepProps;

        beforeAll(() => {
            Element.prototype.getBoundingClientRect = mockedGetBoundingClientRect;
            setupResizeObserverMock();
            mockDOMRect(40, 100, 'select-list-scrollable-container');
            mockDOMRect(40, 100, 'select-list-wrapper');
        });

        afterAll(() => {
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
        });

        beforeEach(() => {
            jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);
            cleanup();

            props = {
                group: testDashboardGroup,
                locale: 'en-US',
                nodeNames: {},
                browserIs: {
                    l: true,
                    m: false,
                    s: false,
                    xs: false,
                },
                onWidgetDefinitionUpdated: jest.fn(),
                stepIndex: 3,
                widgetDefinition: {
                    type: 'TABLE',
                    selectedProperties: {
                        Boolean: {
                            label: 'Boolean',
                            data: { type: GraphQLTypes.Boolean, canFilter: true, canSort: true },
                            id: 'Boolean',
                            labelPath: 'Boolean',
                        } as Property,
                        Date: {
                            label: 'Date',
                            data: { type: GraphQLTypes.Date, canFilter: true, canSort: true },
                            id: 'Date',
                            labelPath: 'Date',
                        } as Property,
                        DateTime: {
                            label: 'DateTime',
                            data: { type: GraphQLTypes.DateTime, canFilter: true, canSort: true },
                            id: 'DateTime',
                            labelPath: 'DateTime',
                        } as Property,
                        Decimal: {
                            label: 'Decimal',
                            data: { type: GraphQLTypes.Decimal, canFilter: true, canSort: true },
                            id: 'Decimal',
                            labelPath: 'Decimal',
                        } as Property,
                        Enum: {
                            label: 'Enum',
                            data: { type: GraphQLTypes.Enum, canFilter: true, canSort: true },
                            id: 'Enum',
                            labelPath: 'Enum',
                        } as Property,
                        ExternalReference: {
                            label: 'ExternalReference',
                            data: { type: GraphQLTypes.ExternalReference, canFilter: true, canSort: true },
                            id: 'ExternalReference',
                            labelPath: 'ExternalReference',
                        } as Property,
                        Float: {
                            label: 'Float',
                            data: { type: GraphQLTypes.Float, canFilter: true, canSort: true },
                            id: 'Float',
                            labelPath: 'Float',
                        } as Property,
                        Id: {
                            label: 'Id',
                            data: { type: 'Id', canFilter: true, canSort: true },
                            id: 'Id',
                            labelPath: 'Id',
                        } as Property,
                        _InputStream: {
                            label: '_InputStream',
                            data: { type: '_InputStream', canFilter: true, canSort: true },
                            id: '_InputStream',
                            labelPath: '_InputStream',
                        } as Property,
                        Int: {
                            label: 'Int',
                            data: { type: GraphQLTypes.Int, canFilter: true, canSort: true },
                            id: 'Int',
                            labelPath: 'Int',
                        } as Property,
                        IntReference: {
                            label: 'IntReference',
                            data: { type: GraphQLTypes.IntReference, canFilter: true, canSort: true },
                            id: 'IntReference',
                            labelPath: 'IntReference',
                        } as Property,
                        Json: {
                            label: 'Json',
                            data: { type: GraphQLTypes.Json, canFilter: true, canSort: true },
                            id: 'Json',
                            labelPath: 'Json',
                        } as Property,
                        String: {
                            label: 'String',
                            data: { type: GraphQLTypes.String, canFilter: true, canSort: true },
                            id: 'String',
                            labelPath: 'String',
                        } as Property,
                        IntOrString: {
                            label: 'IntOrString',
                            data: { type: GraphQLTypes.IntOrString, canFilter: true, canSort: true },
                            id: 'IntOrString',
                            labelPath: 'IntOrString',
                        } as Property,
                    },
                },
            };
        });

        afterEach(() => {
            jest.resetAllMocks();
            applyActionMocks();
        });

        it('Table: should render the add button & empty table text', async () => {
            const { findByRole, findByText } = render(<ContentStep {...props} />);
            await findByRole('button', { name: 'Add column' });
            await findByText('No data to display');
        });

        it('Table: should render a table row after clicking on the "Add column" button', async () => {
            mockDOMRect(40, 100, 'select-list-scrollable-container');
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
            const { findByRole, findByText, findAllByPlaceholderText, queryByText, findAllByTestId, findByTestId } =
                render(<ContentStep {...props} />);
            await findByText('No data to display');
            fireEvent.click(await findByRole('button', { name: 'Add column' }));
            expect(queryByText('Add column')).not.toBeInTheDocument();
            expect(queryByText('No data to display')).not.toBeInTheDocument();
            await findByText('Property', { selector: 'th > div > div > div' });
            await findByText('Presentation', { selector: 'th > div > div > div' });
            await findByText('Decimal places', { selector: 'th > div > div > div' });
            await findByText('Divisor', { selector: 'th > div > div > div' });
            await findByText('Actions', { selector: 'th > div' });
            expect((await findAllByPlaceholderText('Select property...')).length).toBe(1);
            expect(((await findByTestId('e-widget-editor-content-presentation-1')) as HTMLInputElement).disabled).toBe(
                true,
            );
            expect((await findAllByTestId('flat-table-remove-button')).length).toBe(1);
            expect((await findAllByTestId('flat-table-add-button')).length).toBe(1);
        });

        it('Table: should validate divisor', async () => {
            mockDOMRect(40, 100, 'select-list-scrollable-container');
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
            const { findByRole, findByText, findByTestId, container } = render(<ContentStep {...props} />);
            await findByText('No data to display');
            fireEvent.click(await findByRole('button', { name: 'Add column' }));
            const propertyTypeInput = (await findByTestId('e-widget-editor-content-property-1')) as HTMLInputElement;
            fireEvent.change(propertyTypeInput, { target: { value: 'Float' } });
            await userEvent.tab();
            await waitFor(async () => {
                expect(((await findByTestId('e-widget-editor-content-divisor-1')) as HTMLInputElement).value).toBe('1');
            });
            fireEvent.change(await findByTestId('e-widget-editor-content-divisor-1'), { target: { value: '0' } });
            await userEvent.tab();
            const toast = await waitFor(() => {
                expect(container.querySelector('[data-element="error"]')).not.toBe(null);
                return container.querySelector('[data-element="error"]') as Element;
            });
            fireEvent.focus(toast);
            await findByText('This value needs to be greater than 0.');
            fireEvent.change(await findByTestId('e-widget-editor-content-divisor-1'), { target: { value: '10' } });
            await waitFor(() => {
                expect(container.querySelector('[data-element="error"]')).toBe(null);
            });
        }, 20000);

        /* it('Table: should render the right presentation types upon property selection', async () => {
            const presentationTranslations = {
                Checkbox: 'Checkbox',
                Date: 'Date',
                Label: 'Label',
                Numeric: 'Numeric',
                Progress: 'Progress',
                Text: 'Text',
            };
            const { findByTestId, findByText, getByTestId, findByRole } = render(<ContentStep {...props} />);
            fireEvent.click(await findByRole('button', { name: 'Add column' }));
            /* eslint-disable no-restricted-syntax, no-await-in-loop, @typescript-eslint/no-loop-func */
        /*
            for (const property of Object.values(props.widgetDefinition.selectedProperties!)) {
                const propertyTypeInput = (await findByTestId(
                    'e-widget-editor-content-property-1',
                )) as HTMLInputElement;
                await userEvent.type(propertyTypeInput, `${property.label}{Enter}`, {
                    initialSelectionStart: 0,
                    initialSelectionEnd: propertyTypeInput.value.length,
                });
                expect(((await findByTestId('e-widget-editor-content-property-1')) as HTMLInputElement).value).toBe(
                    property.label,
                );
                jest.runOnlyPendingTimers();
                if (includes(numericFields, property.data.type!)) {
                    await waitFor(async () => {
                        expect(
                            ((await findByTestId('e-widget-editor-content-divisor-1')) as HTMLInputElement).value,
                        ).toBe('1');
                    });
                }
                if (presentationGraphqlMapping[property.data.type!] === undefined) {
                    // no types available, property should not be selectable
                    await findByText(`No results for "${property.label}"`);
                } else {
                    const presentationMethodInput = await findByTestId('e-widget-editor-content-presentation-1');
                    const presentationMethod = getTextValue(presentationMethodInput);
                    expect(presentationMethod).toBe(presentationGraphqlMapping[property.data.type!][0]);
                    if (
                        property.data.type &&
                        (property.data.type === GraphQLTypes.Decimal || property.data.type === GraphQLTypes.Float) &&
                        presentationMethod === FieldKey.Numeric
                    ) {
                        await waitFor(async () => {
                            expect(
                                ((await findByTestId('e-widget-editor-content-formatting-1')) as HTMLInputElement)
                                    .value,
                            ).toBe('0');
                        });
                    }
                    await userEvent.tab();
                    await userEvent.type(
                        (await findByTestId('e-widget-editor-content-presentation-1')) as HTMLInputElement,
                        '{arrowdown}',
                    );
                    await waitFor(async () => {
                        const propertyTypeInput = getByTestId('e-widget-editor-content-property-1') as HTMLInputElement;
                        expect(propertyTypeInput?.getAttribute('aria-expanded')).toBe('false');
                    });

                    if (presentationGraphqlMapping[property.data.type!].length > 1) {
                        await waitFor(async () => {
                            const presentationTypeInput = getByTestId(
                                'e-widget-editor-content-presentation-1',
                            ) as HTMLInputElement;
                            expect(presentationTypeInput?.getAttribute('aria-expanded')).toBe('true');
                        });
                        await waitFor(
                            () => {
                                const dropdowns = Array.from(
                                    document.querySelectorAll('[data-element="select-list-wrapper"]'),
                                );
                                const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                                const presentationTypes = Array.from(
                                    lastDropdown?.querySelectorAll('li') ?? [],
                                ) as HTMLLIElement[];
                                const availablePresentation = presentationTypes.map(method => method.textContent);
                                return expect(availablePresentation).toEqual(
                                    [...(presentationGraphqlMapping[property.data.type!] ?? [])].map(
                                        p => presentationTranslations[p],
                                    ),
                                );
                            },
                            { timeout: 5000, interval: 100 },
                        );
                    }
                }
            }
        }, 80000); */
    });

    describe('Bar chart', () => {
        let props: ContentStepProps;

        beforeAll(() => {
            Element.prototype.getBoundingClientRect = mockedGetBoundingClientRect;
            setupResizeObserverMock();
            mockDOMRect(40, 100, 'select-list-scrollable-container');
        });

        afterAll(() => {
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
        });

        beforeEach(() => {
            jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);
            cleanup();

            props = {
                group: testDashboardGroup,
                locale: 'en-US',
                nodeNames: {},
                browserIs: {
                    l: true,
                    m: false,
                    s: false,
                    xs: false,
                },
                onWidgetDefinitionUpdated: jest.fn(),
                stepIndex: 3,
                widgetDefinition: {
                    type: 'BAR_CHART',
                    selectedProperties: {
                        Boolean: {
                            label: 'Boolean',
                            data: { type: GraphQLTypes.Boolean, canFilter: true, canSort: true },
                            id: 'Boolean',
                            labelPath: 'Boolean',
                        } as Property,
                        Date: {
                            label: 'Date',
                            data: { type: GraphQLTypes.Date, canFilter: true, canSort: true },
                            id: 'Date',
                            labelPath: 'Date',
                        } as Property,
                        DateTime: {
                            label: 'DateTime',
                            data: { type: GraphQLTypes.DateTime, canFilter: true, canSort: true },
                            id: 'DateTime',
                            labelPath: 'DateTime',
                        } as Property,
                        Decimal: {
                            label: 'Decimal',
                            data: { type: GraphQLTypes.Decimal, canFilter: true, canSort: true },
                            id: 'Decimal',
                            labelPath: 'Decimal',
                        } as Property,
                        Enum: {
                            label: 'Enum',
                            data: { type: GraphQLTypes.Enum, canFilter: true, canSort: true },
                            id: 'Enum',
                            labelPath: 'Enum',
                        } as Property,
                        ExternalReference: {
                            label: 'ExternalReference',
                            data: { type: GraphQLTypes.ExternalReference, canFilter: true, canSort: true },
                            id: 'ExternalReference',
                            labelPath: 'ExternalReference',
                        } as Property,
                        Float: {
                            label: 'Float',
                            data: { type: GraphQLTypes.Float, canFilter: true, canSort: true },
                            id: 'Float',
                            labelPath: 'Float',
                        } as Property,
                        Id: {
                            label: 'Id',
                            data: { type: 'Id', canFilter: true, canSort: true },
                            id: 'Id',
                            labelPath: 'Id',
                        } as Property,
                        _InputStream: {
                            label: '_InputStream',
                            data: { type: '_InputStream', canFilter: true, canSort: true },
                            id: '_InputStream',
                            labelPath: '_InputStream',
                        } as Property,
                        Int: {
                            label: 'Int',
                            data: { type: GraphQLTypes.Int, canFilter: true, canSort: true },
                            id: 'Int',
                            labelPath: 'Int',
                        } as Property,
                        IntReference: {
                            label: 'IntReference',
                            data: { type: GraphQLTypes.IntReference, canFilter: true, canSort: true },
                            id: 'IntReference',
                            labelPath: 'IntReference',
                        } as Property,
                        Json: {
                            label: 'Json',
                            data: { type: GraphQLTypes.Json, canFilter: true, canSort: true },
                            id: 'Json',
                            labelPath: 'Json',
                        } as Property,
                        String: {
                            label: 'String',
                            data: { type: GraphQLTypes.String, canFilter: true, canSort: true },
                            id: 'String',
                            labelPath: 'String',
                        } as Property,
                        IntOrString: {
                            label: 'IntOrString',
                            data: { type: GraphQLTypes.IntOrString, canFilter: true, canSort: true },
                            id: 'IntOrString',
                            labelPath: 'IntOrString',
                        } as Property,
                    },
                },
            };
        });

        afterEach(() => {
            jest.resetAllMocks();
            applyActionMocks();
        });

        it('Bar chart: should render empty state', async () => {
            const { findByRole, findByText } = render(<ContentStep {...props} />);
            await findByText('Horizontal axis');
            await findByText('Vertical axes');
            await findByText('No data to display');
            await findByRole('button', { name: 'Add value' });
        });

        it('Bar chart: should validate divisor', async () => {
            mockDOMRect(40, 100, 'select-list-scrollable-container');
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
            const { findByRole, findByText, findByTestId, container } = render(<ContentStep {...props} />);
            await findByText('No data to display');
            fireEvent.click(await findByRole('button', { name: 'Add value' }));
            const propertyTypeInput = (await findByTestId('e-widget-editor-content-property-1')) as HTMLInputElement;
            fireEvent.change(propertyTypeInput, { target: { value: 'Float' } });
            await userEvent.tab();
            await waitFor(async () => {
                expect(((await findByTestId('e-widget-editor-content-divisor-1')) as HTMLInputElement).value).toBe('1');
            });
            fireEvent.change(await findByTestId('e-widget-editor-content-divisor-1'), { target: { value: '0' } });
            await userEvent.tab();
            const toast = await waitFor(() => {
                expect(container.querySelector('[data-element="error"]')).not.toBe(null);
                return container.querySelector('[data-element="error"]') as Element;
            });
            fireEvent.focus(toast);
            await findByText('This value needs to be greater than 0.');
            fireEvent.change(await findByTestId('e-widget-editor-content-divisor-1'), { target: { value: '10' } });
            await waitFor(() => {
                expect(container.querySelector('[data-element="error"]')).toBe(null);
            });
        }, 20000);

        it('Bar chart: should render a table row after clicking on the "Add value" button', async () => {
            mockDOMRect(40, 100, 'select-list-scrollable-container');
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
            const { findByRole, findByText, findAllByPlaceholderText, queryByText, findAllByTestId, findByTestId } =
                render(<ContentStep {...props} />);
            await findByText('No data to display');
            fireEvent.click(await findByRole('button', { name: 'Add value' }));
            expect(queryByText('Add value')).not.toBeInTheDocument();
            expect(queryByText('No data to display')).not.toBeInTheDocument();
            await findByText('Property', { selector: 'th > div > div > div' });
            await findByText('Label', { selector: 'th > div > div > div' });
            await findByText('Decimal places', { selector: 'th > div > div > div' });
            await findByText('Divisor', { selector: 'th > div > div > div' });
            await findByText('Aggregation method', { selector: 'th > div > div > div' });
            await findByText('Actions', { selector: 'th > div' });
            expect((await findAllByPlaceholderText('Select property...')).length).toBe(1);
            expect(((await findByTestId('e-widget-editor-grouping-method-1')) as HTMLInputElement).disabled).toBe(true);
            expect((await findAllByTestId('flat-table-remove-button')).length).toBe(1);
            expect((await findAllByTestId('flat-table-add-button')).length).toBe(1);
        });

        /* it('Bar chart: should render the right labels & aggregation methods upon property selection', async () => {
            const { findByTestId, getByTestId, findByRole } = render(<ContentStep {...props} />);
            fireEvent.click(await findByRole('button', { name: 'Add value' }));
            /* eslint-disable no-restricted-syntax, no-await-in-loop, @typescript-eslint/no-loop-func */
        /*
            for (const property of Object.values(props.widgetDefinition.selectedProperties!)) {
                const propertyTypeInput = (await findByTestId(
                    'e-widget-editor-content-property-1',
                )) as HTMLInputElement;
                await userEvent.type(propertyTypeInput, `${property.label}{tab}`);
                jest.runOnlyPendingTimers();
                await userEvent.tab();
                await waitFor(async () => {
                    expect(((await findByTestId('e-widget-editor-content-label-1')) as HTMLInputElement).value).toBe(
                        property.label,
                    );
                });
                if (!includes(numericFields, property.data.type!) || property.data.type === GraphQLTypes.IntReference) {
                    const groupingMethodInput = await findByTestId('e-widget-editor-grouping-method-1');
                    expect(getTextValue(groupingMethodInput)).toBe('Distinct count');
                } else {
                    await waitFor(async () => {
                        expect(
                            ((await findByTestId('e-widget-editor-grouping-method-1')) as HTMLInputElement).value,
                        ).toBe('Sum');
                    });
                    await waitFor(async () => {
                        expect(
                            ((await findByTestId('e-widget-editor-content-divisor-1')) as HTMLInputElement).value,
                        ).toBe('1');
                    });
                    expect(
                        ((await findByTestId('e-widget-editor-grouping-method-1')) as HTMLButtonElement).disabled,
                    ).toBe(false);
                    await userEvent.click(await findByTestId('e-widget-editor-grouping-method-1'));
                    await userEvent.type(
                        (await findByTestId('e-widget-editor-grouping-method-1')) as HTMLInputElement,
                        '{arrowdown}',
                    );
                    await waitFor(async () => {
                        const propertyTypeInput = getByTestId('e-widget-editor-content-property-1') as HTMLInputElement;
                        expect(propertyTypeInput?.getAttribute('aria-expanded')).toBe('false');
                    });
                    await waitFor(async () => {
                        const aggregationMethodInput = getByTestId(
                            'e-widget-editor-grouping-method-1',
                        ) as HTMLInputElement;
                        expect(aggregationMethodInput?.getAttribute('aria-expanded')).toBe('true');
                    });
                    await waitFor(
                        () => {
                            const dropdowns = Array.from(
                                document.querySelectorAll('[data-element="select-list-wrapper"]'),
                            );
                            const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                            const aggregationTypes = Array.from(
                                lastDropdown?.querySelectorAll('li') ?? [],
                            ) as HTMLLIElement[];
                            const availableAggregations = aggregationTypes.map(method => method.textContent);
                            return expect(availableAggregations).toEqual(
                                [...(aggregationsGraphqlMapping![property.data.type!] ?? [])].map(
                                    p => aggregationTranslations()[p],
                                ),
                            );
                        },
                        { timeout: 5000, interval: 100 },
                    );
                }
            }
        }, 90000); */

        /* it('Bar chart: should be able to add the same property multiple times', async () => {
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
            const { findByTestId, getByTestId, findByRole } = render(<ContentStep {...props} />);
            // add first row row
            fireEvent.click(await findByRole('button', { name: 'Add value' }));
            const decimalProp = Object.values(props.widgetDefinition.selectedProperties!)[3];
            const firtLinePropertyTypeInput = (await findByTestId(
                'e-widget-editor-content-property-1',
            )) as HTMLInputElement;
            // select decimal property
            await userEvent.type(firtLinePropertyTypeInput, `${decimalProp.label}{tab}`);
            await userEvent.tab();
            await waitFor(async () => {
                expect(((await findByTestId('e-widget-editor-content-label-1')) as HTMLInputElement).value).toBe(
                    decimalProp.label,
                );
            });
            // check label
            expect(((await findByTestId('e-widget-editor-grouping-method-1')) as HTMLButtonElement).disabled).toBe(
                false,
            );
            // open grouping method dropdown of first row
            await userEvent.click(await findByTestId('e-widget-editor-grouping-method-1'));

            await waitFor(async () => {
                expect(
                    (getByTestId('e-widget-editor-content-property-1') as HTMLInputElement)?.getAttribute(
                        'aria-expanded',
                    ),
                ).toBe('false');
            });
            await waitFor(async () => {
                expect(
                    (getByTestId('e-widget-editor-grouping-method-1') as HTMLInputElement)?.getAttribute(
                        'aria-expanded',
                    ),
                ).toBe('true');
            });
            await userEvent.type(
                (await findByTestId('e-widget-editor-grouping-method-1')) as HTMLInputElement,
                '{arrowdown}',
            );
            // check dropdown options for first row
            await waitFor(
                () => {
                    const dropdowns = Array.from(document.querySelectorAll('[data-element="select-list-wrapper"]'));
                    const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                    const aggregationTypes = Array.from(lastDropdown?.querySelectorAll('li') ?? []) as HTMLLIElement[];
                    const availableAggregations = aggregationTypes.map(method => method.textContent);
                    return expect(availableAggregations).toEqual(
                        [...(aggregationsGraphqlMapping![decimalProp.data.type!] ?? [])].map(
                            p => aggregationTranslations()[p],
                        ),
                    );
                },
                { timeout: 5000, interval: 100 },
            );
            // select "Minimum" as an aggregation method (which by hitting arrow down is already selected)
            await userEvent.type(getByTestId('e-widget-editor-grouping-method-1') as HTMLInputElement, '{enter}', {
                skipClick: true,
            });
            await userEvent.tab();
            await waitFor(() => {
                expect((getByTestId('e-widget-editor-grouping-method-1') as HTMLInputElement).value).toBe('Average');
            });

            // add second row
            await userEvent.click(await findByTestId('flat-table-add-button'));
            // choose decimal property again
            const secondLinePropertyTypeInput = (await findByTestId(
                'e-widget-editor-content-property-2',
            )) as HTMLInputElement;
            await userEvent.type(secondLinePropertyTypeInput, `${decimalProp.label}{tab}`);
            await userEvent.tab();
            await waitFor(async () => {
                expect(((await findByTestId('e-widget-editor-content-label-2')) as HTMLInputElement).value).toBe(
                    decimalProp.label,
                );
            });
            // check second row label
            expect(((await findByTestId('e-widget-editor-grouping-method-2')) as HTMLButtonElement).disabled).toBe(
                false,
            );
            await userEvent.click(await findByTestId('e-widget-editor-grouping-method-2'));
            // open grouping method dropdown of second row
            await userEvent.type(
                (await findByTestId('e-widget-editor-grouping-method-2')) as HTMLInputElement,
                '{arrowdown}',
            );
            await waitFor(async () => {
                expect(
                    (getByTestId('e-widget-editor-content-property-2') as HTMLInputElement)?.getAttribute(
                        'aria-expanded',
                    ),
                ).toBe('false');
            });
            await waitFor(async () => {
                expect(
                    (getByTestId('e-widget-editor-grouping-method-2') as HTMLInputElement)?.getAttribute(
                        'aria-expanded',
                    ),
                ).toBe('true');
            });
            // check dropdown options for second row
            await waitFor(
                () => {
                    const dropdowns = Array.from(document.querySelectorAll('[data-element="select-list-wrapper"]'));
                    const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                    const aggregationTypes = Array.from(lastDropdown?.querySelectorAll('li') ?? []) as HTMLLIElement[];
                    const availableAggregations = aggregationTypes.map(method => method.textContent);
                    return expect(availableAggregations).toEqual(
                        [...(aggregationsGraphqlMapping![decimalProp.data.type!] ?? [])]
                            // all except "Average" are available
                            .filter(a => a !== Aggregations.avg)
                            .map(p => aggregationTranslations()[p]),
                    );
                },
                { timeout: 5000, interval: 100 },
            );
        }, 90000); */

        it('Bar chart: should default grouping method to first in list if "sum" already used', async () => {
            mockDOMRect(40, 100, 'select-list-scrollable-container');
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
            const { findByTestId, findByRole } = render(<ContentStep {...props} />);
            // add first row row
            fireEvent.click(await findByRole('button', { name: 'Add value' }));
            const decimalProp = Object.values(props.widgetDefinition.selectedProperties!)[3];
            const firtLinePropertyTypeInput = (await findByTestId(
                'e-widget-editor-content-property-1',
            )) as HTMLInputElement;
            // select decimal property
            await userEvent.type(firtLinePropertyTypeInput, `${decimalProp.label}{tab}`);
            await userEvent.tab();
            await waitFor(async () => {
                expect(((await findByTestId('e-widget-editor-content-label-1')) as HTMLInputElement).value).toBe(
                    decimalProp.label,
                );
            });
            // check label
            expect(((await findByTestId('e-widget-editor-grouping-method-1')) as HTMLButtonElement).disabled).toBe(
                false,
            );
            await waitFor(async () => {
                expect(((await findByTestId('e-widget-editor-grouping-method-1')) as HTMLInputElement).value).toBe(
                    'Sum',
                );
            });

            // add second row
            await userEvent.click(await findByTestId('flat-table-add-button'));
            // choose decimal property again
            const secondLinePropertyTypeInput = (await findByTestId(
                'e-widget-editor-content-property-2',
            )) as HTMLInputElement;
            await userEvent.type(secondLinePropertyTypeInput, `${decimalProp.label}{tab}`);
            await userEvent.tab();
            await waitFor(async () => {
                expect(((await findByTestId('e-widget-editor-content-label-2')) as HTMLInputElement).value).toBe(
                    decimalProp.label,
                );
            });
            // check second row label
            expect(((await findByTestId('e-widget-editor-grouping-method-2')) as HTMLButtonElement).disabled).toBe(
                false,
            );
            await waitFor(async () => {
                expect(((await findByTestId('e-widget-editor-grouping-method-2')) as HTMLInputElement).value).toBe(
                    'Minimum',
                );
            });
            // add third row
            await userEvent.click(await findByTestId('flat-table-add-button'));
            // choose decimal property again
            const thirdLinePropertyTypeInput = (await findByTestId(
                'e-widget-editor-content-property-3',
            )) as HTMLInputElement;
            await userEvent.type(thirdLinePropertyTypeInput, `${decimalProp.label}{tab}`);
            await userEvent.tab();
            await waitFor(async () => {
                expect(((await findByTestId('e-widget-editor-content-label-3')) as HTMLInputElement).value).toBe(
                    decimalProp.label,
                );
            });
            // check third row label
            expect(((await findByTestId('e-widget-editor-grouping-method-3')) as HTMLButtonElement).disabled).toBe(
                false,
            );
            await waitFor(async () => {
                expect(((await findByTestId('e-widget-editor-grouping-method-3')) as HTMLInputElement).value).toBe(
                    'Maximum',
                );
            });
        }, 90000);

        /* it('Bar chart: should be able to aggregate by non numeric properties using distinct count', async () => {
            mockDOMRect(40, 100, 'select-list-scrollable-container');
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
            const { findByTestId, findByRole } = render(<ContentStep {...props} />);
            fireEvent.click(await findByRole('button', { name: 'Add value' }));
            for (const property of Object.values(props.widgetDefinition.selectedProperties!).filter(
                p => !includes(numericFields, p.data.type!),
            )) {
                const propertyTypeInput = (await findByTestId(
                    'e-widget-editor-content-property-1',
                )) as HTMLInputElement;
                await userEvent.type(propertyTypeInput, `${property.label}{tab}`);
                jest.runOnlyPendingTimers();
                await userEvent.tab();
                await waitFor(async () => {
                    expect(((await findByTestId('e-widget-editor-content-label-1')) as HTMLInputElement).value).toBe(
                        property.label,
                    );
                });
                await waitFor(async () => {
                    expect(
                        ((await findByTestId('e-widget-editor-grouping-method-1')) as HTMLParagraphElement).textContent,
                    ).toBe('Distinct count');
                });
            }
        }, 90000); */
    });

    describe('Indicator tile', () => {
        let props: ContentStepProps;

        beforeAll(() => {
            Element.prototype.getBoundingClientRect = mockedGetBoundingClientRect;
            setupResizeObserverMock();
            mockDOMRect(40, 100, 'select-list-scrollable-container');
        });

        afterAll(() => {
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
        });

        beforeEach(() => {
            jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);
            cleanup();

            props = {
                group: testDashboardGroup,
                locale: 'en-US',
                nodeNames: {},
                browserIs: {
                    l: true,
                    m: false,
                    s: false,
                    xs: false,
                },
                onWidgetDefinitionUpdated: jest.fn(),
                stepIndex: 3,
                widgetDefinition: {
                    type: 'INDICATOR_TILE',
                    selectedProperties: {
                        Boolean: {
                            label: 'Boolean',
                            data: { type: GraphQLTypes.Boolean, canFilter: true, canSort: true },
                            id: 'Boolean',
                            labelPath: 'Boolean',
                        } as Property,
                        Date: {
                            label: 'Date',
                            data: { type: GraphQLTypes.Date, canFilter: true, canSort: true },
                            id: 'Date',
                            labelPath: 'Date',
                        } as Property,
                        DateTime: {
                            label: 'DateTime',
                            data: { type: GraphQLTypes.DateTime, canFilter: true, canSort: true },
                            id: 'DateTime',
                            labelPath: 'DateTime',
                        } as Property,
                        Decimal: {
                            label: 'Decimal',
                            data: {
                                type: GraphQLTypes.Decimal,
                                canFilter: true,
                                canSort: true,
                            },
                            id: 'Decimal',
                            labelPath: 'Decimal',
                        } as Property,
                        Enum: {
                            label: 'Enum',
                            data: { type: GraphQLTypes.Enum, canFilter: true, canSort: true },
                            id: 'Enum',
                            labelPath: 'Enum',
                        } as Property,
                        ExternalReference: {
                            label: 'ExternalReference',
                            data: { type: GraphQLTypes.ExternalReference, canFilter: true, canSort: true },
                            id: 'ExternalReference',
                            labelPath: 'ExternalReference',
                        } as Property,
                        Float: {
                            label: 'Float',
                            data: { type: GraphQLTypes.Float, canFilter: true, canSort: true },
                            id: 'Float',
                            labelPath: 'Float',
                        } as Property,
                        Id: {
                            label: 'Id',
                            data: { type: 'Id', canFilter: true, canSort: true },
                            id: 'Id',
                            labelPath: 'Id',
                        } as Property,
                        _InputStream: {
                            label: '_InputStream',
                            data: {
                                type: '_InputStream',
                                canFilter: true,
                                canSort: true,
                            },
                            id: '_InputStream',
                            labelPath: '_InputStream',
                        } as Property,
                        Int: {
                            label: 'Int',
                            data: { type: GraphQLTypes.Int, canFilter: true, canSort: true },
                            id: 'Int',
                            labelPath: 'Int',
                        } as Property,
                        IntReference: {
                            label: 'IntReference',
                            data: { type: GraphQLTypes.IntReference, canFilter: true, canSort: true },
                            id: 'IntReference',
                            labelPath: 'IntReference',
                        } as Property,
                        Json: {
                            label: 'Json',
                            data: { type: GraphQLTypes.Json, canFilter: true, canSort: true },
                            id: 'Json',
                            labelPath: 'Json',
                        } as Property,
                        String: {
                            label: 'String',
                            data: { type: GraphQLTypes.String, canFilter: true, canSort: true },
                            id: 'String',
                            labelPath: 'String',
                        } as Property,
                        IntOrString: {
                            label: 'IntOrString',
                            data: { type: GraphQLTypes.IntOrString, canFilter: true, canSort: true },
                            id: 'IntOrString',
                            labelPath: 'IntOrString',
                        } as Property,
                    },
                },
            };
        });

        it('should render the title', async () => {
            const { getByText } = render(<ContentStep {...props} />);
            expect(getByText('{{stepIndex}}. Add your content')).toBeInTheDocument();
        });

        it('Indicator tile: should render with empty dropdowns and grouping method', async () => {
            const { findByTestId } = render(<ContentStep {...props} />);
            expect(((await findByTestId('e-widget-editor-grouping-property')) as HTMLInputElement).value).toEqual('');
            expect(((await findByTestId('e-widget-editor-grouping-method')) as HTMLInputElement).value).toEqual('');
            expect(((await findByTestId('e-widget-editor-grouping-method')) as HTMLInputElement).disabled).toBe(true);
        });

        it('Indicator tile: should not render grouping methods if not indicator tile', async () => {
            props.widgetDefinition.type = 'TABLE';
            const { getByText, queryByTestId } = render(<ContentStep {...props} />);
            expect(getByText('{{stepIndex}}. Add your content')).toBeInTheDocument();
            expect(queryByTestId('e-widget-editor-grouping-property')).toBeNull();
            expect(queryByTestId('e-widget-editor-grouping-method')).toBeNull();
        });

        it('Indicator tile: should enable grouping method upon group property selection', async () => {
            const { findByTestId } = render(<ContentStep {...props} />);
            expect(((await findByTestId('e-widget-editor-grouping-method')) as HTMLInputElement).disabled).toBe(true);
            fireEvent.change((await findByTestId('e-widget-editor-grouping-property')) as HTMLButtonElement, {
                target: { value: 'Decimal' },
            });
            await userEvent.tab();
            expect(((await findByTestId('e-widget-editor-grouping-method')) as HTMLInputElement).disabled).toBe(false);
        });

        it('Indicator tile: should render the right grouping method upon property selection', async () => {
            const { findByTestId, findByText, getByTestId, debug } = render(<ContentStep {...props} />);
            /* eslint-disable no-restricted-syntax, no-await-in-loop, @typescript-eslint/no-loop-func */
            for (const property of Object.values(props.widgetDefinition.selectedProperties!)) {
                const groupingPropertyInput = (await findByTestId(
                    'e-widget-editor-grouping-property',
                )) as HTMLInputElement;
                await userEvent.type(groupingPropertyInput, `${property.label}{Enter}`, {
                    initialSelectionStart: 0,
                    initialSelectionEnd: groupingPropertyInput.value.length,
                });
                if (
                    ((await findByTestId('e-widget-editor-grouping-property')) as HTMLInputElement).value !==
                    property.label
                ) {
                    debug();
                }
                expect(((await findByTestId('e-widget-editor-grouping-property')) as HTMLInputElement).value).toBe(
                    property.label,
                );
                if (
                    aggregationsGraphqlMapping[property.data.type!] === undefined ||
                    !property.data.canFilter ||
                    !property.data.canSort
                ) {
                    // no aggregations method available, property should not be selectable
                    await findByText(`No results for "${property.label}"`);
                } else {
                    jest.runOnlyPendingTimers();
                    await userEvent.tab();
                    fireEvent.focus((await findByTestId('e-widget-editor-grouping-method')) as HTMLInputElement);
                    jest.runOnlyPendingTimers();
                    await waitFor(async () => {
                        const groupingPropertyInput = getByTestId(
                            'e-widget-editor-grouping-property',
                        ) as HTMLInputElement;
                        expect(groupingPropertyInput?.getAttribute('aria-expanded')).toBe('false');
                    });
                    const expectedGroupingMethods = aggregationsGraphqlMapping[property.data.type!]?.map(
                        p => aggregationTranslations()[p],
                    );
                    if (expectedGroupingMethods?.length === 1) {
                        expect((getByTestId('e-widget-editor-grouping-method') as HTMLInputElement).value).toBe(
                            expectedGroupingMethods[0],
                        );
                        expect((getByTestId('e-widget-editor-grouping-method') as HTMLInputElement).readOnly).toBe(
                            true,
                        );
                    } else {
                        await waitFor(async () => {
                            const groupingMethodInput = getByTestId(
                                'e-widget-editor-grouping-method',
                            ) as HTMLInputElement;
                            expect(groupingMethodInput?.getAttribute('aria-expanded')).toBe('true');
                        });
                        await waitFor(
                            () => {
                                const dropdowns = Array.from(
                                    document.querySelectorAll('[data-element="select-list-wrapper"]'),
                                );
                                const lastDropdown = dropdowns[dropdowns.length - 1] as Element | undefined;
                                const groupingMethods = Array.from(
                                    lastDropdown?.querySelectorAll('li') ?? [],
                                ) as HTMLLIElement[];
                                const availableGroupingMethods = groupingMethods.map(method => method.textContent);
                                return expect(availableGroupingMethods).toEqual(expectedGroupingMethods);
                            },
                            { timeout: 3000, interval: 100 },
                        );
                    }
                }
            }
        }, 80000);

        it('Indicator tile: set group property and method', async () => {
            mockDOMRect(40, 100, 'select-list-scrollable-container');
            Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
            const { findByTestId } = render(<ContentStep {...props} />);
            expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledTimes(1);
            expect(props.onWidgetDefinitionUpdated).toHaveBeenNthCalledWith(1, {
                groupBy: undefined,
                selectedProperties: {
                    Boolean: {
                        label: 'Boolean',
                        data: { type: GraphQLTypes.Boolean, canFilter: true, canSort: true },
                        id: 'Boolean',
                        labelPath: 'Boolean',
                    },
                    Date: {
                        label: 'Date',
                        data: { type: GraphQLTypes.Date, canFilter: true, canSort: true },
                        id: 'Date',
                        labelPath: 'Date',
                    },
                    DateTime: {
                        label: 'DateTime',
                        data: { type: GraphQLTypes.DateTime, canFilter: true, canSort: true },
                        id: 'DateTime',
                        labelPath: 'DateTime',
                    },
                    Decimal: {
                        label: 'Decimal',
                        data: {
                            type: GraphQLTypes.Decimal,
                            canFilter: true,
                            canSort: true,
                        },
                        id: 'Decimal',
                        labelPath: 'Decimal',
                    },
                    Enum: {
                        label: 'Enum',
                        data: { type: GraphQLTypes.Enum, canFilter: true, canSort: true },
                        id: 'Enum',
                        labelPath: 'Enum',
                    },
                    ExternalReference: {
                        label: 'ExternalReference',
                        data: { type: GraphQLTypes.ExternalReference, canFilter: true, canSort: true },
                        id: 'ExternalReference',
                        labelPath: 'ExternalReference',
                    },
                    Float: {
                        label: 'Float',
                        data: { type: GraphQLTypes.Float, canFilter: true, canSort: true },
                        id: 'Float',
                        labelPath: 'Float',
                    },
                    Id: {
                        label: 'Id',
                        data: { type: 'Id', canFilter: true, canSort: true },
                        id: 'Id',
                        labelPath: 'Id',
                    },
                    _InputStream: {
                        label: '_InputStream',
                        data: { type: '_InputStream', canFilter: true, canSort: true },
                        id: '_InputStream',
                        labelPath: '_InputStream',
                    },
                    Int: {
                        label: 'Int',
                        data: { type: GraphQLTypes.Int, canFilter: true, canSort: true },
                        id: 'Int',
                        labelPath: 'Int',
                    },
                    IntReference: {
                        label: 'IntReference',
                        data: { type: GraphQLTypes.IntReference, canFilter: true, canSort: true },
                        id: 'IntReference',
                        labelPath: 'IntReference',
                    },
                    Json: {
                        label: 'Json',
                        data: { type: GraphQLTypes.Json, canFilter: true, canSort: true },
                        id: 'Json',
                        labelPath: 'Json',
                    },
                    String: {
                        label: 'String',
                        data: { type: GraphQLTypes.String, canFilter: true, canSort: true },
                        id: 'String',
                        labelPath: 'String',
                    },
                    IntOrString: {
                        label: 'IntOrString',
                        data: { type: GraphQLTypes.IntOrString, canFilter: true, canSort: true },
                        id: 'IntOrString',
                        labelPath: 'IntOrString',
                    },
                },
                type: 'INDICATOR_TILE',
            });
            fireEvent.change((await findByTestId('e-widget-editor-grouping-property')) as HTMLInputElement, {
                target: { value: 'Decimal' },
            });
            await userEvent.tab();
            expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledTimes(2);
            expect(props.onWidgetDefinitionUpdated).toHaveBeenNthCalledWith(2, {
                groupBy: undefined,
                selectedProperties: {
                    Boolean: {
                        label: 'Boolean',
                        data: { type: GraphQLTypes.Boolean, canFilter: true, canSort: true },
                        id: 'Boolean',
                        labelPath: 'Boolean',
                    },
                    Date: {
                        label: 'Date',
                        data: { type: GraphQLTypes.Date, canFilter: true, canSort: true },
                        id: 'Date',
                        labelPath: 'Date',
                    },
                    DateTime: {
                        label: 'DateTime',
                        data: { type: GraphQLTypes.DateTime, canFilter: true, canSort: true },
                        id: 'DateTime',
                        labelPath: 'DateTime',
                    },
                    Decimal: {
                        label: 'Decimal',
                        data: { type: GraphQLTypes.Decimal, canFilter: true, canSort: true },
                        id: 'Decimal',
                        labelPath: 'Decimal',
                    },
                    Enum: {
                        label: 'Enum',
                        data: { type: GraphQLTypes.Enum, canFilter: true, canSort: true },
                        id: 'Enum',
                        labelPath: 'Enum',
                    },
                    ExternalReference: {
                        label: 'ExternalReference',
                        data: { type: GraphQLTypes.ExternalReference, canFilter: true, canSort: true },
                        id: 'ExternalReference',
                        labelPath: 'ExternalReference',
                    },
                    Float: {
                        label: 'Float',
                        data: { type: GraphQLTypes.Float, canFilter: true, canSort: true },
                        id: 'Float',
                        labelPath: 'Float',
                    },
                    Id: {
                        label: 'Id',
                        data: { type: 'Id', canFilter: true, canSort: true },
                        id: 'Id',
                        labelPath: 'Id',
                    },
                    _InputStream: {
                        label: '_InputStream',
                        data: { type: '_InputStream', canFilter: true, canSort: true },
                        id: '_InputStream',
                        labelPath: '_InputStream',
                    },
                    Int: {
                        label: 'Int',
                        data: { type: GraphQLTypes.Int, canFilter: true, canSort: true },
                        id: 'Int',
                        labelPath: 'Int',
                    },
                    IntReference: {
                        label: 'IntReference',
                        data: { type: GraphQLTypes.IntReference, canFilter: true, canSort: true },
                        id: 'IntReference',
                        labelPath: 'IntReference',
                    },
                    Json: {
                        label: 'Json',
                        data: { type: GraphQLTypes.Json, canFilter: true, canSort: true },
                        id: 'Json',
                        labelPath: 'Json',
                    },
                    String: {
                        label: 'String',
                        data: { type: GraphQLTypes.String, canFilter: true, canSort: true },
                        id: 'String',
                        labelPath: 'String',
                    },
                    IntOrString: {
                        label: 'IntOrString',
                        data: { type: GraphQLTypes.IntOrString, canFilter: true, canSort: true },
                        id: 'IntOrString',
                        labelPath: 'IntOrString',
                    },
                },
                type: 'INDICATOR_TILE',
            });
            fireEvent.change((await findByTestId('e-widget-editor-grouping-method')) as HTMLInputElement, {
                target: { value: 'Minimum' },
            });
            await userEvent.tab();
            expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledTimes(3);
            expect(props.onWidgetDefinitionUpdated).toHaveBeenNthCalledWith(3, {
                groupBy: {
                    decimalDigits: undefined,
                    divisor: 1,
                    method: 'min',
                    property: {
                        label: 'Decimal',
                        data: { type: GraphQLTypes.Decimal, canFilter: true, canSort: true },
                        id: 'Decimal',
                        labelPath: 'Decimal',
                    },
                },
                selectedProperties: {
                    Boolean: {
                        label: 'Boolean',
                        data: { type: GraphQLTypes.Boolean, canFilter: true, canSort: true },
                        id: 'Boolean',
                        labelPath: 'Boolean',
                    },
                    Date: {
                        label: 'Date',
                        data: { type: GraphQLTypes.Date, canFilter: true, canSort: true },
                        id: 'Date',
                        labelPath: 'Date',
                    },
                    DateTime: {
                        label: 'DateTime',
                        data: { type: GraphQLTypes.DateTime, canFilter: true, canSort: true },
                        id: 'DateTime',
                        labelPath: 'DateTime',
                    },
                    Decimal: {
                        label: 'Decimal',
                        data: {
                            type: GraphQLTypes.Decimal,
                            canFilter: true,
                            canSort: true,
                        },
                        id: 'Decimal',
                        labelPath: 'Decimal',
                    },
                    Enum: {
                        label: 'Enum',
                        data: { type: GraphQLTypes.Enum, canFilter: true, canSort: true },
                        id: 'Enum',
                        labelPath: 'Enum',
                    },
                    ExternalReference: {
                        label: 'ExternalReference',
                        data: { type: GraphQLTypes.ExternalReference, canFilter: true, canSort: true },
                        id: 'ExternalReference',
                        labelPath: 'ExternalReference',
                    },
                    Float: {
                        label: 'Float',
                        data: { type: GraphQLTypes.Float, canFilter: true, canSort: true },
                        id: 'Float',
                        labelPath: 'Float',
                    },
                    Id: {
                        label: 'Id',
                        data: { type: 'Id', canFilter: true, canSort: true },
                        id: 'Id',
                        labelPath: 'Id',
                    },
                    _InputStream: {
                        label: '_InputStream',
                        data: { type: '_InputStream', canFilter: true, canSort: true },
                        id: '_InputStream',
                        labelPath: '_InputStream',
                    },
                    Int: {
                        label: 'Int',
                        data: { type: GraphQLTypes.Int, canFilter: true, canSort: true },
                        id: 'Int',
                        labelPath: 'Int',
                    },
                    IntReference: {
                        label: 'IntReference',
                        data: { type: GraphQLTypes.IntReference, canFilter: true, canSort: true },
                        id: 'IntReference',
                        labelPath: 'IntReference',
                    },
                    Json: {
                        label: 'Json',
                        data: { type: GraphQLTypes.Json, canFilter: true, canSort: true },
                        id: 'Json',
                        labelPath: 'Json',
                    },
                    String: {
                        label: 'String',
                        data: { type: GraphQLTypes.String, canFilter: true, canSort: true },
                        id: 'String',
                        labelPath: 'String',
                    },
                    IntOrString: {
                        label: 'IntOrString',
                        data: { type: GraphQLTypes.IntOrString, canFilter: true, canSort: true },
                        id: 'IntOrString',
                        labelPath: 'IntOrString',
                    },
                },
                type: 'INDICATOR_TILE',
            });
        });

        describe('Indicator tile: integration', () => {
            let mockStore;
            let mockState: XtremAppState;

            beforeEach(() => {
                jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);
                jest.spyOn(nodeInformationService, 'fetchNodeDetails').mockImplementation(() => Promise.resolve({}));

                mockState = getMockState();
                mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.isOpen = true;
                mockState.dashboard.nodeNames = {
                    '@sage/xtrem-test/ATestNode': 'ZZZZZZ',
                    '@sage/xtrem-test/AnotherTestNode': 'AAAA',
                    '@sage/xtrem-test/ThirdTestNode': 'BBBB',
                };
                mockState.dashboard.widgetCategories = {
                    SOME_CATEGORY: 'Some Category',
                    ANOTHER_CATEGORY: 'Another Category',
                };
                mockState.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetDefinition = {
                    category: 'SOME_CATEGORY',
                    title: 'Test title',
                    type: 'INDICATOR_TILE',
                    node: '@sage/xtrem-test/ATestNode',
                    groupBy: undefined,
                    selectedProperties: {
                        Boolean: {
                            label: 'Boolean',
                            data: { type: GraphQLTypes.Boolean, canFilter: true, canSort: true },
                            id: 'Boolean',
                            labelPath: 'Boolean',
                        } as Property,
                        Date: {
                            label: 'Date',
                            data: { type: GraphQLTypes.Date, canFilter: true, canSort: true },
                            id: 'Date',
                            labelPath: 'Date',
                        } as Property,
                        DateTime: {
                            label: 'DateTime',
                            data: { type: GraphQLTypes.DateTime, canFilter: true, canSort: true },
                            id: 'DateTime',
                            labelPath: 'DateTime',
                        } as Property,
                        Decimal: {
                            label: 'Decimal',
                            data: { type: GraphQLTypes.Decimal, canFilter: true, canSort: true },
                            id: 'Decimal',
                            labelPath: 'Decimal',
                        } as Property,
                        Enum: {
                            label: 'Enum',
                            data: { type: GraphQLTypes.Enum, canFilter: true, canSort: true },
                            id: 'Enum',
                            labelPath: 'Enum',
                        } as Property,
                        ExternalReference: {
                            label: 'ExternalReference',
                            data: { type: GraphQLTypes.ExternalReference, canFilter: true, canSort: true },
                            id: 'ExternalReference',
                            labelPath: 'ExternalReference',
                        } as Property,
                        Float: {
                            label: 'Float',
                            data: { type: GraphQLTypes.Float, canFilter: true, canSort: true },
                            id: 'Float',
                            labelPath: 'Float',
                        } as Property,
                        Id: {
                            label: 'Id',
                            data: { type: 'Id', canFilter: true, canSort: true },
                            id: 'Id',
                            labelPath: 'Id',
                        } as Property,
                        _InputStream: {
                            label: '_InputStream',
                            data: { type: '_InputStream', canFilter: true, canSort: true },
                            id: '_InputStream',
                            labelPath: '_InputStream',
                        } as Property,
                        Int: {
                            label: 'Int',
                            data: { type: GraphQLTypes.Int },
                            id: 'Int',
                            labelPath: 'Int',
                        } as Property,
                        IntReference: {
                            label: 'IntReference',
                            data: { type: GraphQLTypes.IntReference, canFilter: true, canSort: true },
                            id: 'IntReference',
                            labelPath: 'IntReference',
                        } as Property,
                        Json: {
                            label: 'Json',
                            data: { type: GraphQLTypes.Json, canFilter: true, canSort: true },
                            id: 'Json',
                            labelPath: 'Json',
                        } as Property,
                        String: {
                            label: 'String',
                            data: { type: GraphQLTypes.String, canFilter: true, canSort: true },
                            id: 'String',
                            labelPath: 'String',
                        } as Property,
                        IntOrString: {
                            label: 'IntOrString',
                            data: { type: GraphQLTypes.IntOrString, canFilter: true, canSort: true },
                            id: 'IntOrString',
                            labelPath: 'IntOrString',
                        } as Property,
                    },
                };
                mockStore = getMockStore(mockState);
            });

            afterEach(() => {
                jest.resetAllMocks();
                applyActionMocks();
            });
            const setup = (storeCustomizer?: (state: XtremAppState) => void) => {
                if (storeCustomizer) {
                    storeCustomizer(mockState);
                    mockStore = getMockStore(mockState);
                }
                return render(
                    <Provider store={mockStore}>
                        <ConnectedWidgetEditorDialog group={testDashboardGroup} />
                    </Provider>,
                );
            };

            it('Indicator tile: should validate divisor', async () => {
                mockDOMRect(40, 100, 'select-list-scrollable-container');
                Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
                const { findByTestId, container, findByText } = render(<ContentStep {...props} />);
                expect(props.onWidgetDefinitionUpdated).toHaveBeenCalledTimes(1);
                fireEvent.change((await findByTestId('e-widget-editor-grouping-property')) as HTMLInputElement, {
                    target: { value: 'Decimal' },
                });
                await userEvent.tab();

                await findByTestId('e-widget-editor-content-divisor');
                fireEvent.change(await findByTestId('e-widget-editor-content-divisor'), { target: { value: '0' } });
                await userEvent.tab();
                const toast = await waitFor(() => {
                    expect(container.querySelector('[data-element="error"]')).not.toBe(null);
                    return container.querySelector('[data-element="error"]') as Element;
                });
                fireEvent.focus(toast);
                await findByText('This value needs to be greater than 0.');
                fireEvent.change(await findByTestId('e-widget-editor-content-divisor'), { target: { value: '10' } });
                await waitFor(() => {
                    expect(container.querySelector('[data-element="error"]')).toBe(null);
                });
            }, 20000);

            it('can render with next button disabled', async () => {
                mockDOMRect(40, 100, 'select-list-scrollable-container');
                Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
                const { findByText, findByTestId } = setup();
                fireEvent.click(await findByTestId('e-widget-editor-dialog-next'));
                fireEvent.click(await findByText('Select the data to add to your widget', { exact: false }));
                fireEvent.click(await findByTestId('e-widget-editor-dialog-next'));
                expect(((await findByTestId('e-widget-editor-grouping-property')) as HTMLInputElement).value).toEqual(
                    '',
                );
                expect(((await findByTestId('e-widget-editor-grouping-method')) as HTMLInputElement).value).toEqual('');
                expect(((await findByTestId('e-widget-editor-grouping-method')) as HTMLInputElement).disabled).toBe(
                    true,
                );
                expect(((await findByTestId('e-widget-editor-dialog-previous')) as HTMLButtonElement).disabled).toBe(
                    false,
                );
                expect(((await findByTestId('e-widget-editor-dialog-next')) as HTMLButtonElement).disabled).toBe(true);
            });

            it('can render with next button enabled', async () => {
                mockDOMRect(40, 100, 'select-list-scrollable-container');
                Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
                const { findByText, findByTestId, getByTestId } = setup(state => {
                    state.dashboard.dashboardGroups[testDashboardGroup].widgetEditor.widgetDefinition.groupBy = {
                        method: Aggregations.min,
                        property: {
                            label: 'Decimal',
                            data: { type: GraphQLTypes.Decimal },
                            id: 'Decimal',
                            labelPath: 'Decimal',
                        } as Property,
                        divisor: 1,
                    };
                });
                fireEvent.click(await findByTestId('e-widget-editor-dialog-next'));
                fireEvent.click(await findByText('Select the data to add to your widget', { exact: false }));
                fireEvent.click(await findByTestId('e-widget-editor-dialog-next'));
                await waitFor(() => {
                    const groupingProperty = getByTestId('e-widget-editor-grouping-property') as HTMLInputElement;
                    return groupingProperty.value === 'Decimal';
                });
                await waitFor(() => {
                    const groupingMethod = getByTestId('e-widget-editor-grouping-method') as HTMLInputElement;
                    return groupingMethod.value === 'Minimum';
                });
                await waitFor(() => {
                    const divisor = getByTestId('e-widget-editor-content-divisor') as HTMLInputElement;
                    return divisor.value === '1';
                });
                expect(((await findByTestId('e-widget-editor-grouping-method')) as HTMLButtonElement).disabled).toBe(
                    false,
                );
                expect(((await findByTestId('e-widget-editor-dialog-previous')) as HTMLButtonElement).disabled).toBe(
                    false,
                );
                expect(((await findByTestId('e-widget-editor-dialog-next')) as HTMLButtonElement).disabled).toBe(false);
            });
        });
    });
});
