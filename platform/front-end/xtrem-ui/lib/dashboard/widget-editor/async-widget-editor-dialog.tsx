import * as React from 'react';
import type { WidgetEditorDialogExternalProps } from './widget-editor-dialog';

const ConnectedWidgetEditorDialog = React.lazy(() =>
    import('./widget-editor-dialog').then(m => ({ default: m.ConnectedWidgetEditorDialog })),
);

export function AsyncConnectedWidgetEditorDialog(props: WidgetEditorDialogExternalProps): React.ReactElement {
    /**
     * No fallback component is used because the widget editor dialog is mounted first in a closed state.
     */
    return (
        <React.Suspense fallback={<span />}>
            <ConnectedWidgetEditorDialog {...props} />
        </React.Suspense>
    );
}
