import { type Dict, numericFields, objectKeys, type Property } from '@sage/xtrem-shared';
import * as React from 'react';
import { aggregatableGraphqlTypes } from '../../component/types';
import type { UserCustomizableWidget, UserWidgetDefinition } from '../../redux/state';
import { localize } from '../../service/i18n-service';
import { includes } from 'lodash';
import type { DashboardContextVariables } from '../dashboard-types';

export interface WidgetEditorStepProps {
    contextVariables?: DashboardContextVariables;
    group: string;
    locale: string;
    nodeNames: Dict<string>;
    onWidgetDefinitionUpdated: (newData: UserWidgetDefinition) => void;
    stepIndex: number;
    widgetDefinition: UserWidgetDefinition;
}

export interface WidgetEditorStep {
    key: WidgetEditorStepType;
    title: string;
}

export type StepStatus = 'complete' | 'current' | 'incomplete';

export const DEFAULT_CHART_COLORS: { label: string; value: string }[] = [
    {
        value: '#008247',
        label: 'Jade', // 139
    },
    {
        value: '#1478A9',
        label: 'Navy', // 201
    },
    { value: '#0D7E7D', label: 'Teal' }, // 263
    {
        value: '#BE466B',
        label: 'Cherry', // 325
    },
    {
        value: '#C64532',
        label: 'Terra', // 387
    },
    {
        value: '#A56006',
        label: 'Amber', // 449
    },
    {
        value: '#00331C',
        label: 'Jade dark',
    },
    {
        value: '#062E43',
        label: 'Navy dark',
    },
    { value: '#003131', label: 'Teal dark' },
    {
        value: '#4D1929',
        label: 'Cherry dark',
    },
    {
        value: '#4D1B14',
        label: 'Terra dark',
    },
    {
        value: '#402502',
        label: 'Amber dark',
    },
];

export interface WidgetStepDefinition {
    title: string;
    isValid: (uwd: UserWidgetDefinition) => boolean;
}

export type WidgetEditorStepType = 'BASIC_INFO' | 'DATA' | 'CONTENT' | 'FILTERS' | 'SORTING' | 'LAYOUT';

/** This most be a function and cannot be a const, as the localize function must be called when the widget editor is
 * being opened to enure that the strings were loaded.
 * */
export const getWidgetEditorSteps = (): Dict<WidgetStepDefinition> => ({
    BASIC_INFO: {
        title: localize('@sage/xtrem-ui/widget-editor-step-basic-title', 'Widget'),
        isValid: (uwd: UserWidgetDefinition): boolean => !!uwd.node && !!uwd.title && !!uwd.type,
    },
    DATA: {
        title: localize('@sage/xtrem-ui/widget-editor-step-data-title', 'Data'),
        isValid: (uwd: UserWidgetDefinition): boolean => {
            if (isChart(uwd)) {
                const selectableProps = Object.values(uwd.selectedProperties ?? {}).filter(
                    s => s.data.type && s.data.canSort,
                );
                return selectableProps.length >= 2;
            }
            return (
                (uwd.type === 'TABLE' && objectKeys(uwd.selectedProperties ?? {}).length > 0) ||
                (uwd.type === 'INDICATOR_TILE' &&
                    Object.values(uwd.selectedProperties ?? {}).some(
                        p =>
                            p.data.type &&
                            includes(aggregatableGraphqlTypes, p.data.type) &&
                            p.data.canFilter &&
                            p.data.canSort,
                    )) // Can be aggregated: p.data.canFilter && p.data.canSort
            );
        },
    },
    CONTENT: {
        title: localize('@sage/xtrem-ui/widget-editor-step-content-title', 'Content'),
        isValid: (uwd: UserWidgetDefinition): boolean => {
            if (isChart(uwd)) {
                const isXAxisDate =
                    uwd.xAxis?.property.data.type === 'Date' || uwd.xAxis?.property.data.type === 'DateTime';
                const isXAxisNumeric = Boolean(
                    uwd.xAxis?.property.data.type && includes(numericFields, uwd.xAxis?.property.data.type),
                );

                return (
                    uwd.xAxis != null &&
                    ((isXAxisDate && uwd.xAxis.groupBy != null) ||
                        (isXAxisNumeric && uwd.xAxis.decimalDigits != null) ||
                        (!isXAxisDate && !isXAxisNumeric && uwd.xAxis.property != null)) &&
                    (uwd.aggregations?.length ?? 0) > 0
                );
            }
            return (
                (uwd.type === 'INDICATOR_TILE' &&
                    uwd.groupBy?.method !== undefined &&
                    uwd.groupBy?.property !== undefined &&
                    !Number.isNaN(Number(uwd.groupBy?.divisor)) &&
                    Number(uwd.groupBy.divisor) > 0) ||
                (uwd.type === 'TABLE' && (uwd.columns?.length ?? 0) > 0)
            );
        },
    },
    FILTERS: {
        title: localize('@sage/xtrem-ui/widget-editor-step-filters-title', 'Filters'),
        isValid: (): boolean => true,
    },
    SORTING: {
        title: localize('@sage/xtrem-ui/widget-editor-step-sorting-title', 'Sorting'),
        isValid: (): boolean => true,
    },
    LAYOUT: {
        title: localize('@sage/xtrem-ui/widget-editor-step-layout-title', 'Layout'),
        isValid: (uwd: UserWidgetDefinition): boolean => {
            // If no create action OR the create action has a label and a page set
            const isCreateActionValid =
                !uwd.createAction ||
                !uwd.createAction.isEnabled ||
                (!!uwd.createAction.page && !!uwd.createAction.title);

            // If no see all action OR the see all action has a label and a page set

            const isSeeAllActionValid =
                (!uwd.createAction ||
                    !uwd.createAction.isEnabled ||
                    (!!uwd.createAction.page && !!uwd.createAction.title)) &&
                (!uwd.seeAllAction ||
                    !uwd.seeAllAction.isEnabled ||
                    (!!uwd.seeAllAction.page && !!uwd.seeAllAction.title));

            // If no decimal digits defined but the aggregation method is not count
            const isIndicatorTileSpecificFieldsValid =
                uwd.type !== 'INDICATOR_TILE' ||
                uwd.groupBy?.method === 'distinctCount' ||
                uwd.groupBy?.property.data.type === 'String' ||
                !Object.prototype.hasOwnProperty.call(uwd, 'decimalDigits') ||
                (Object.prototype.hasOwnProperty.call(uwd, 'decimalDigits') && uwd.decimalDigits !== undefined);

            return isCreateActionValid && isSeeAllActionValid && isIndicatorTileSpecificFieldsValid;
        },
    },
});

export const stepsForWidgetTypes: Record<UserCustomizableWidget, WidgetEditorStepType[]> = {
    INDICATOR_TILE: ['BASIC_INFO', 'DATA', 'CONTENT', 'FILTERS', 'LAYOUT'],
    BAR_CHART: ['BASIC_INFO', 'DATA', 'CONTENT', 'FILTERS', 'SORTING', 'LAYOUT'],
    LINE_CHART: ['BASIC_INFO', 'DATA', 'CONTENT', 'FILTERS', 'SORTING', 'LAYOUT'],
    TABLE: ['BASIC_INFO', 'DATA', 'CONTENT', 'FILTERS', 'SORTING', 'LAYOUT'],
};

export function isChart(widgetDefinition: UserWidgetDefinition): boolean {
    return widgetDefinition.type === 'BAR_CHART' || widgetDefinition.type === 'LINE_CHART';
}

export const getStepStatus = (availableSteps: string[], activeStep: string, step: string): StepStatus => {
    const activeStepPosition = availableSteps.indexOf(activeStep);
    const stepPosition = availableSteps.indexOf(step);
    if (stepPosition > activeStepPosition) {
        return 'incomplete';
    }
    if (stepPosition < activeStepPosition) {
        return 'complete';
    }

    return 'current';
};

export const PropertyTableHeader: React.FC = React.memo(() => {
    return (
        <tr>
            <th>{localize('@sage/xtrem-ui/name', 'Name')}</th>
            <th>{localize('@sage/xtrem-ui/parent', 'Parent')}</th>
        </tr>
    );
});

export const getPropertyParentNode = ({
    property,
    nodeNames,
    widgetDefinition,
}: {
    property: Property;
    nodeNames: Dict<string>;
    widgetDefinition: UserWidgetDefinition;
}): string => {
    const split = property.labelPath.split('.');
    return split.length > 1 ? split.slice(-2, -1)[0] : (nodeNames[widgetDefinition.node ?? ''] ?? '');
};

export const getWidgetDefaultSize = ({ type, columns }: UserWidgetDefinition): { w: number; h: number } => {
    switch (type) {
        case 'TABLE':
            return { w: Math.min(columns ? columns.length * 2 : 2, 12), h: 2 };
        case 'LINE_CHART':
        case 'BAR_CHART':
            return { w: 6, h: 3 };
        default:
            return { w: 2, h: 2 };
    }
};
