import Typography from 'carbon-react/esm/components/typography';
import * as React from 'react';
import { localize } from '../../service/i18n-service';
import type { WidgetEditorStepProps } from './widget-editor-utils';
import { isChart, getPropertyParentNode } from './widget-editor-utils';
import { getGutterSize } from '../../utils/responsive-utils';
import type { ResponsiveTypes, OrderByProperty } from '../../redux/state';
import type * as xtremRedux from '../../redux';
import { connect } from 'react-redux';
import { SortConditionEditor } from '@sage/xtrem-ui-components';
import { objectKeys, type Dict, type Property } from '@sage/xtrem-shared';
import type { Order } from '@sage/xtrem-ui-components';

export interface SortingStepProps extends WidgetEditorStepProps {
    browserIs: ResponsiveTypes;
}

export function SortingStep({
    stepIndex,
    browserIs,
    widgetDefinition,
    onWidgetDefinitionUpdated,
    nodeNames,
}: SortingStepProps): React.ReactElement | null {
    const value = (widgetDefinition.orderBy || []).reduce((prevValue: Dict<Order>, v: OrderByProperty) => {
        prevValue[v.id] = v.order;
        return prevValue;
    }, {} as Dict<Order>);

    const sortableProperties = React.useMemo(
        () =>
            objectKeys(widgetDefinition.selectedProperties ?? {})
                .filter(key => {
                    if (!widgetDefinition.selectedProperties) {
                        return false;
                    }
                    const p = widgetDefinition.selectedProperties[key];
                    return (
                        p.data?.canSort &&
                        (isChart(widgetDefinition)
                            ? widgetDefinition.xAxis?.property.id === p.id ||
                              widgetDefinition.aggregations?.find(a => a.id === p.id)
                            : true)
                    );
                })
                .reduce((prevValue: Dict<Property>, key: string) => {
                    if (widgetDefinition.selectedProperties) {
                        prevValue[key] = widgetDefinition.selectedProperties[key];
                    }
                    return prevValue;
                }, {} as Dict<Property>),
        [widgetDefinition],
    );

    const onChange = React.useCallback(
        (newOrder: Dict<Order>): void => {
            onWidgetDefinitionUpdated({
                ...widgetDefinition,
                orderBy: objectKeys(newOrder).map((id: string) => {
                    return {
                        ...sortableProperties[id],
                        order: newOrder[id],
                    };
                }),
            });
        },
        [onWidgetDefinitionUpdated, sortableProperties, widgetDefinition],
    );

    const gridGutter = getGutterSize(browserIs);

    const canAddNewLines = React.useMemo(() => {
        return (
            widgetDefinition.selectedProperties &&
            (widgetDefinition.orderBy || []).length < objectKeys(sortableProperties || {}).length
        );
    }, [widgetDefinition.orderBy, sortableProperties, widgetDefinition.selectedProperties]);

    if (
        widgetDefinition.type !== 'TABLE' &&
        widgetDefinition.type !== 'BAR_CHART' &&
        widgetDefinition.type !== 'LINE_CHART'
    ) {
        return null;
    }
    return (
        <div>
            <Typography variant="h2" data-testid="e-widget-editor-step-title">
                {localize('@sage/xtrem-ui/widget-editor-sorting-step-title', '{{stepIndex}}. Define sorting', {
                    stepIndex,
                })}
            </Typography>
            <div className="e-widget-editor-section">
                <SortConditionEditor<Property>
                    canAddNewLines={canAddNewLines}
                    gridGutter={gridGutter}
                    localize={localize}
                    properties={sortableProperties}
                    value={value}
                    onChange={onChange}
                    getPropertySubtitle={(property: Property): string =>
                        getPropertyParentNode({ nodeNames, property, widgetDefinition })
                    }
                />
            </div>
        </div>
    );
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: WidgetEditorStepProps): SortingStepProps => {
    return {
        ...props,
        browserIs: state.browser.is,
    };
};

export const ConnectedSortingStep = connect(mapStateToProps)(SortingStep);
