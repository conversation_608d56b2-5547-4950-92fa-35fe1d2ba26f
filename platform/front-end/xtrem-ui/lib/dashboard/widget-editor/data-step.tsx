import Typography from 'carbon-react/esm/components/typography';
import * as React from 'react';
import { localize } from '../../service/i18n-service';
import type { WidgetEditorStepProps } from './widget-editor-utils';
import { GraphQLTypes } from '../../types';
import { NodeBrowserTree } from '../../component/ui/node-browser-tree/node-browser-tree';
import type { NodeDetails } from '../../service/node-information-service';
import { type Dict, type TreeElement, type Property, objectKeys } from '@sage/xtrem-shared';
import type { DefaultPropertyType } from '@sage/xtrem-ui-components';

export function DataStep({
    stepIndex,
    widgetDefinition,
    onWidgetDefinitionUpdated,
    locale,
}: WidgetEditorStepProps): React.ReactElement {
    const filter = React.useCallback(
        (v: NodeDetails[]): NodeDetails[] => {
            return widgetDefinition.type === 'BAR_CHART' ||
                widgetDefinition.type === 'LINE_CHART' ||
                widgetDefinition.type === 'INDICATOR_TILE'
                ? v.filter((c): boolean => c.canFilter && c.canSort && c.type !== GraphQLTypes.InputStream)
                : v.filter(c => c.type !== GraphQLTypes.InputStream);
        },
        [widgetDefinition.type],
    );

    const onCheckedItemsUpdated = React.useCallback(
        (checkedItems: Dict<Property>) => {
            const graphqlTypes = Object.values(GraphQLTypes);
            /**
             * For enums set type to 'GraphQLTypes.Enum' and set the 'node' property to actual type
             */
            const selectedProperties = objectKeys(checkedItems).reduce<Dict<DefaultPropertyType>>((acc, key) => {
                // @ts-expect-error checking if it's a primitive GraphQl type, it's fine :D
                if (!graphqlTypes.includes(checkedItems[key].data.type)) {
                    acc[key] = {
                        ...checkedItems[key],
                        data: {
                            ...checkedItems[key].data,
                            node: checkedItems[key].data.type,
                            type: GraphQLTypes.Enum,
                        },
                    };
                } else {
                    acc[key] = checkedItems[key] as unknown as Property;
                }
                return acc;
            }, {});
            onWidgetDefinitionUpdated({ ...widgetDefinition, selectedProperties });
        },
        [onWidgetDefinitionUpdated, widgetDefinition],
    );

    return (
        <div className="e-data-step-tree">
            <Typography variant="h2" data-testid="e-widget-editor-step-title">
                {localize(
                    '@sage/xtrem-ui/widget-editor-data-step-title',
                    '{{stepIndex}}. Select the data to add to your widget',
                    { stepIndex },
                )}
            </Typography>
            <span className="e-widget-editor-step-subtitle" data-testid="e-widget-editor-step-subtitle">
                {localize(
                    '@sage/xtrem-ui/widget-editor-content-step-subtitle',
                    'Select a field for grouping and fields to use as filters.',
                )}
            </span>
            <NodeBrowserTree
                locale={locale}
                filter={filter}
                checkedItems={(widgetDefinition.selectedProperties || {}) as unknown as Dict<TreeElement>}
                node={widgetDefinition.node}
                onCheckedItemsUpdated={onCheckedItemsUpdated}
            />
        </div>
    );
}
