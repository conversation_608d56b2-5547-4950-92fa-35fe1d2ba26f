import { objectKeys, type Dict } from '@sage/xtrem-shared';
import { memoize } from 'lodash';
import { localize } from '../../service/i18n-service';

export const indicatorTileIcons = memoize(
    (): Dict<string> => ({
        accounting: localize('@sage/xtrem-ui/detailed-icon-name-accounting', 'Accounting'),
        addons: localize('@sage/xtrem-ui/detailed-icon-name-addons', 'Addons'),
        animal: localize('@sage/xtrem-ui/detailed-icon-name-animal', 'Animal'),
        apple: localize('@sage/xtrem-ui/detailed-icon-name-apple', 'Apple'),
        asset_mgt: localize('@sage/xtrem-ui/detailed-icon-name-asset_mgt', 'Asset management'),
        award: localize('@sage/xtrem-ui/detailed-icon-name-award', 'Award'),
        bag: localize('@sage/xtrem-ui/detailed-icon-name-bag', 'Bag'),
        bakery: localize('@sage/xtrem-ui/detailed-icon-name-bakery', 'Bakery'),
        barcode: localize('@sage/xtrem-ui/detailed-icon-name-barcode', 'Barcode'),
        bicycle: localize('@sage/xtrem-ui/detailed-icon-name-bicycle', 'Bicycle'),
        binocular: localize('@sage/xtrem-ui/detailed-icon-name-binocular', 'Binoculars'),
        book: localize('@sage/xtrem-ui/detailed-icon-name-book', 'Book'),
        bright: localize('@sage/xtrem-ui/detailed-icon-name-bright', 'Bright'),
        building: localize('@sage/xtrem-ui/detailed-icon-name-building', 'Building'),
        calculator: localize('@sage/xtrem-ui/detailed-icon-name-calculator', 'Calculator'),
        calendar: localize('@sage/xtrem-ui/detailed-icon-name-calendar', 'Calendar'),
        camera: localize('@sage/xtrem-ui/detailed-icon-name-camera', 'Camera'),
        card: localize('@sage/xtrem-ui/detailed-icon-name-card', 'Card'),
        cart: localize('@sage/xtrem-ui/detailed-icon-name-cart', 'Cart'),
        certificate: localize('@sage/xtrem-ui/detailed-icon-name-certificate', 'Certificate'),
        check: localize('@sage/xtrem-ui/detailed-icon-name-check', 'Check'),
        checkbox: localize('@sage/xtrem-ui/detailed-icon-name-checkbox', 'Checkbox'),
        checklist: localize('@sage/xtrem-ui/detailed-icon-name-checklist', 'Checklist'),
        chemical: localize('@sage/xtrem-ui/detailed-icon-name-chemical', 'Chemical'),
        chess: localize('@sage/xtrem-ui/detailed-icon-name-chess', 'Chess'),
        click: localize('@sage/xtrem-ui/detailed-icon-name-click', 'Click'),
        clock: localize('@sage/xtrem-ui/detailed-icon-name-clock', 'Clock'),
        close: localize('@sage/xtrem-ui/detailed-icon-name-close', 'Close'),
        clothes: localize('@sage/xtrem-ui/detailed-icon-name-clothes', 'Clothes'),
        cloud: localize('@sage/xtrem-ui/detailed-icon-name-cloud', 'Cloud'),
        coffee: localize('@sage/xtrem-ui/detailed-icon-name-coffee', 'Coffee'),
        compass: localize('@sage/xtrem-ui/detailed-icon-name-compass', 'Compass'),
        connected: localize('@sage/xtrem-ui/detailed-icon-name-connected', 'Connected'),
        consultant: localize('@sage/xtrem-ui/detailed-icon-name-consultant', 'Consultant'),
        conversation: localize('@sage/xtrem-ui/detailed-icon-name-conversation', 'Conversation'),
        cooking: localize('@sage/xtrem-ui/detailed-icon-name-cooking', 'Cooking'),
        cpu: localize('@sage/xtrem-ui/detailed-icon-name-cpu', 'CPU'),
        crowd: localize('@sage/xtrem-ui/detailed-icon-name-crowd', 'Crowd'),
        crown: localize('@sage/xtrem-ui/detailed-icon-name-crown', 'Crown'),
        data: localize('@sage/xtrem-ui/detailed-icon-name-data', 'Data'),
        database: localize('@sage/xtrem-ui/detailed-icon-name-database', 'Database'),
        decline: localize('@sage/xtrem-ui/detailed-icon-name-decline', 'Decline'),
        desktop: localize('@sage/xtrem-ui/detailed-icon-name-desktop', 'Desktop'),
        devices: localize('@sage/xtrem-ui/detailed-icon-name-devices', 'Devices'),
        dollar: localize('@sage/xtrem-ui/detailed-icon-name-dollar', 'Dollar'),
        download: localize('@sage/xtrem-ui/detailed-icon-name-download', 'Download'),
        ear: localize('@sage/xtrem-ui/detailed-icon-name-ear', 'Ear'),
        ecomm: localize('@sage/xtrem-ui/detailed-icon-name-ecomm', 'E-commerce'),
        euro: localize('@sage/xtrem-ui/detailed-icon-name-euro', 'Euro'),
        excavator: localize('@sage/xtrem-ui/detailed-icon-name-excavator', 'Excavator'),
        eye: localize('@sage/xtrem-ui/detailed-icon-name-eye', 'Eye'),
        factory: localize('@sage/xtrem-ui/detailed-icon-name-factory', 'Factory'),
        favorite: localize('@sage/xtrem-ui/detailed-icon-name-favorite', 'Favorite'),
        filter: localize('@sage/xtrem-ui/detailed-icon-name-filter', 'Filter'),
        financials: localize('@sage/xtrem-ui/detailed-icon-name-financials', 'Financials'),
        flag: localize('@sage/xtrem-ui/detailed-icon-name-flag', 'Flag'),
        folder: localize('@sage/xtrem-ui/detailed-icon-name-folder', 'Folder'),
        food: localize('@sage/xtrem-ui/detailed-icon-name-food', 'Food'),
        form: localize('@sage/xtrem-ui/detailed-icon-name-form', 'Form'),
        jewelry: localize('@sage/xtrem-ui/detailed-icon-name-jewelry', 'Jewelry'),
        keys: localize('@sage/xtrem-ui/detailed-icon-name-keys', 'Keys'),
        lab: localize('@sage/xtrem-ui/detailed-icon-name-lab', 'Lab'),
        label: localize('@sage/xtrem-ui/detailed-icon-name-label', 'Label'),
        laptop: localize('@sage/xtrem-ui/detailed-icon-name-laptop', 'Laptop'),
        lightning: localize('@sage/xtrem-ui/detailed-icon-name-lightning', 'Lightning'),
        like: localize('@sage/xtrem-ui/detailed-icon-name-like', 'Like'),
        link: localize('@sage/xtrem-ui/detailed-icon-name-link', 'Link'),
        locations: localize('@sage/xtrem-ui/detailed-icon-name-locations', 'Locations'),
        lock: localize('@sage/xtrem-ui/detailed-icon-name-lock', 'Lock'),
        lock_unlocked: localize('@sage/xtrem-ui/detailed-icon-name-lock_unlocked', 'Lock Unlocked'),
        mail: localize('@sage/xtrem-ui/detailed-icon-name-mail', 'Mail'),
        map: localize('@sage/xtrem-ui/detailed-icon-name-map', 'Map'),
        medical: localize('@sage/xtrem-ui/detailed-icon-name-medical', 'Medical'),
        megaphone: localize('@sage/xtrem-ui/detailed-icon-name-megaphone', 'Megaphone'),
        memo: localize('@sage/xtrem-ui/detailed-icon-name-memo', 'Memo'),
        microphone: localize('@sage/xtrem-ui/detailed-icon-name-microphone', 'Microphone'),
        minus: localize('@sage/xtrem-ui/detailed-icon-name-minus', 'Minus'),
        mouse: localize('@sage/xtrem-ui/detailed-icon-name-mouse', 'Mouse'),
        newspaper: localize('@sage/xtrem-ui/detailed-icon-name-newspaper', 'Newspaper'),
        note: localize('@sage/xtrem-ui/detailed-icon-name-note', 'Note'),
        notebook: localize('@sage/xtrem-ui/detailed-icon-name-notebook', 'Notebook'),
        office: localize('@sage/xtrem-ui/detailed-icon-name-office', 'Office'),
        page: localize('@sage/xtrem-ui/detailed-icon-name-page', 'Page'),
        payment: localize('@sage/xtrem-ui/detailed-icon-name-payment', 'Payment'),
        payroll: localize('@sage/xtrem-ui/detailed-icon-name-payroll', 'Payroll'),
        pen: localize('@sage/xtrem-ui/detailed-icon-name-pen', 'Pen'),
        pencil: localize('@sage/xtrem-ui/detailed-icon-name-pencil', 'Pencil'),
        person: localize('@sage/xtrem-ui/detailed-icon-name-person', 'Person'),
        phone: localize('@sage/xtrem-ui/detailed-icon-name-phone', 'Phone'),
        pin: localize('@sage/xtrem-ui/detailed-icon-name-pin', 'Pin'),
        plus: localize('@sage/xtrem-ui/detailed-icon-name-plus', 'Plus'),
        point: localize('@sage/xtrem-ui/detailed-icon-name-point', 'Point'),
        pound: localize('@sage/xtrem-ui/detailed-icon-name-pound', 'Pound'),
        power: localize('@sage/xtrem-ui/detailed-icon-name-power', 'Power'),
        presentation: localize('@sage/xtrem-ui/detailed-icon-name-presentation', 'Presentation'),
        print: localize('@sage/xtrem-ui/detailed-icon-name-print', 'Print'),
        processing: localize('@sage/xtrem-ui/detailed-icon-name-processing', 'Processing'),
        puzzle: localize('@sage/xtrem-ui/detailed-icon-name-puzzle', 'Puzzle'),
        question: localize('@sage/xtrem-ui/detailed-icon-name-question', 'Question'),
        receipts: localize('@sage/xtrem-ui/detailed-icon-name-receipts', 'Receipts'),
        recycle: localize('@sage/xtrem-ui/detailed-icon-name-recycle', 'Recycle'),
        redo: localize('@sage/xtrem-ui/detailed-icon-name-redo', 'Redo'),
        remote: localize('@sage/xtrem-ui/detailed-icon-name-remote', 'Remote'),
        rocket: localize('@sage/xtrem-ui/detailed-icon-name-rocket', 'Rocket'),
        safe: localize('@sage/xtrem-ui/detailed-icon-name-safe', 'Safe'),
        satelite: localize('@sage/xtrem-ui/detailed-icon-name-satelite', 'Satellite'),
        savings: localize('@sage/xtrem-ui/detailed-icon-name-savings', 'Savings'),
        scissors: localize('@sage/xtrem-ui/detailed-icon-name-scissors', 'Scissors'),
        server: localize('@sage/xtrem-ui/detailed-icon-name-server', 'Server'),
        service: localize('@sage/xtrem-ui/detailed-icon-name-service', 'Service'),
        setting: localize('@sage/xtrem-ui/detailed-icon-name-setting', 'Setting'),
        share: localize('@sage/xtrem-ui/detailed-icon-name-share', 'Share'),
        shoes: localize('@sage/xtrem-ui/detailed-icon-name-shoes', 'Shoes'),
        shuffle: localize('@sage/xtrem-ui/detailed-icon-name-shuffle', 'Shuffle'),
        sign: localize('@sage/xtrem-ui/detailed-icon-name-sign', 'Sign'),
        sim: localize('@sage/xtrem-ui/detailed-icon-name-sim', 'SIM Card'),
        smartphone: localize('@sage/xtrem-ui/detailed-icon-name-smartphone', 'Smartphone'),
        stationeries: localize('@sage/xtrem-ui/detailed-icon-name-stationeries', 'Stationeries'),
        store: localize('@sage/xtrem-ui/detailed-icon-name-store', 'Store'),
        support: localize('@sage/xtrem-ui/detailed-icon-name-support', 'Support'),
        sync: localize('@sage/xtrem-ui/detailed-icon-name-sync', 'Sync'),
        tab: localize('@sage/xtrem-ui/detailed-icon-name-tab', 'Tab'),
        tablet: localize('@sage/xtrem-ui/detailed-icon-name-tablet', 'Tablet'),
        thermometer: localize('@sage/xtrem-ui/detailed-icon-name-thermometer', 'Thermometer'),
        timer: localize('@sage/xtrem-ui/detailed-icon-name-timer', 'Timer'),
        tools: localize('@sage/xtrem-ui/detailed-icon-name-tools', 'Tools'),
        travel: localize('@sage/xtrem-ui/detailed-icon-name-travel', 'Travel'),
        truck: localize('@sage/xtrem-ui/detailed-icon-name-truck', 'Truck'),
        undo: localize('@sage/xtrem-ui/detailed-icon-name-undo', 'Undo'),
        video: localize('@sage/xtrem-ui/detailed-icon-name-video', 'Video'),
        wallet: localize('@sage/xtrem-ui/detailed-icon-name-wallet', 'Wallet'),
        warehouse: localize('@sage/xtrem-ui/detailed-icon-name-warehouse', 'Warehouse'),
        warning: localize('@sage/xtrem-ui/detailed-icon-name-warning', 'Warning'),
        weather: localize('@sage/xtrem-ui/detailed-icon-name-weather', 'Weather'),
        wireless: localize('@sage/xtrem-ui/detailed-icon-name-wireless', 'Wireless'),
        wrench: localize('@sage/xtrem-ui/detailed-icon-name-wrench', 'Wrench'),
        writing: localize('@sage/xtrem-ui/detailed-icon-name-writing', 'Writing'),
        handshake: localize('@sage/xtrem-ui/detailed-icon-name-handshake', 'Handshake'),
        gauge: localize('@sage/xtrem-ui/detailed-icon-name-gauge', 'Gauge'),
        gears: localize('@sage/xtrem-ui/detailed-icon-name-gears', 'Gears'),
        glasses: localize('@sage/xtrem-ui/detailed-icon-name-glasses', 'Glasses'),
        globe: localize('@sage/xtrem-ui/detailed-icon-name-globe', 'Globe'),
        green: localize('@sage/xtrem-ui/detailed-icon-name-green', 'Green'),
        happy: localize('@sage/xtrem-ui/detailed-icon-name-happy', 'Happy'),
        heart: localize('@sage/xtrem-ui/detailed-icon-name-heart', 'Heart'),
        hide: localize('@sage/xtrem-ui/detailed-icon-name-hide', 'Hide'),
        holiday: localize('@sage/xtrem-ui/detailed-icon-name-holiday', 'Holiday'),
        home: localize('@sage/xtrem-ui/detailed-icon-name-home', 'Home'),
        hourglass: localize('@sage/xtrem-ui/detailed-icon-name-hourglass', 'Hourglass'),
        hub: localize('@sage/xtrem-ui/detailed-icon-name-hub', 'Hub'),
        idea: localize('@sage/xtrem-ui/detailed-icon-name-idea', 'Idea'),
        incline: localize('@sage/xtrem-ui/detailed-icon-name-incline', 'Incline'),
        industry: localize('@sage/xtrem-ui/detailed-icon-name-industry', 'Industry'),
        info: localize('@sage/xtrem-ui/detailed-icon-name-info', 'Info'),
        integration: localize('@sage/xtrem-ui/detailed-icon-name-integration', 'Integration'),
        table: localize('@sage/xtrem-ui/detailed-icon-name-table', 'Table'),
    }),
);

export const indicatorTileIconsSortedArray = memoize((): Array<{ value: string; label: string }> => {
    const keys = objectKeys(indicatorTileIcons());

    return keys.map(k => ({ value: k, label: indicatorTileIcons()[k] })).sort((a, b) => a.label.localeCompare(b.label));
});
