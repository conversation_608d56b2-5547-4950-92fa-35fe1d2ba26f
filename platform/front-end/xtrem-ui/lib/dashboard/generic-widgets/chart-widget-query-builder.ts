import { numericFields } from '@sage/xtrem-shared';
import { get, includes, set } from 'lodash';
import { AbstractWidgetQueryBuilder } from '.';
import type { AggregationProperty } from '../../redux/state';
import { calculateDeepPaths } from '../../service/customization-service';
import { formatDateToCurrentLocale } from '../../service/i18n-service';

export class ChartWidgetQueryBuilder extends AbstractWidgetQueryBuilder {
    public build(queryParams?: { first: number }): any {
        if (!this.userWidgetDefinition.xAxis) {
            throw new Error('Cannot build chart query: x-axis is undefined.');
        }
        if (!this.userWidgetDefinition.aggregations || this.userWidgetDefinition.aggregations.length === 0) {
            throw new Error('Cannot build chart query: no series are defined');
        }
        return this.buildGroupByMultipleValuesQuery({
            group: this.userWidgetDefinition.xAxis,
            aggregations: this.userWidgetDefinition.aggregations,
            first: queryParams?.first,
        })
            .buildFilter()
            .buildOrderBy().query;
    }

    public getValue(data: any): any {
        if (!this.userWidgetDefinition.xAxis) {
            throw new Error('Cannot build chart query: x-axis is undefined.');
        }
        return get(data, `${this.nodePath}.queryAggregate.edges`, []).map(({ node }: any) => {
            const groupProperty = String(
                this.userWidgetDefinition.xAxis?.property.id || this.userWidgetDefinition.xAxis?.property.path,
            );
            const { subpath, selectorSegments } = calculateDeepPaths(groupProperty);
            let groupValue = get(node, `group.${subpath.join('.')}`);
            if (typeof groupValue === 'string' && selectorSegments.length > 0) {
                groupValue = JSON.parse(groupValue);
            }
            let formattedGroupValue: string = groupValue;
            if (this.userWidgetDefinition.xAxis!.groupBy && groupValue != null) {
                formattedGroupValue = `${formatDateToCurrentLocale(
                    groupValue,
                    this.userWidgetDefinition.xAxis!.groupBy === 'day' ? 'FullDate' : 'MonthYear',
                )}`;
                if (this.userWidgetDefinition.xAxis!.groupBy === 'year') {
                    formattedGroupValue = formattedGroupValue.slice(-4);
                }
            } else if (
                this.userWidgetDefinition.xAxis!.decimalDigits != null &&
                Boolean(
                    this.userWidgetDefinition.xAxis?.property.data.type &&
                        includes(numericFields, this.userWidgetDefinition.xAxis?.property.data.type),
                )
            ) {
                const num = Number(groupValue);
                formattedGroupValue = Number.isNaN(num)
                    ? '0'
                    : num.toFixed(this.userWidgetDefinition.xAxis!.decimalDigits);
            }

            return (this.userWidgetDefinition.aggregations ?? []).reduce(
                (acc, aggregationProperty) => {
                    const aggregationPropertyPath = String(aggregationProperty.id || aggregationProperty.path);
                    const { subpath: aggregationPropertySubPath, selectorSegments: aggregationSelectorSegments } =
                        calculateDeepPaths(aggregationPropertyPath);
                    let value = get(
                        node,
                        `values.${aggregationPropertySubPath.join('.')}.${aggregationProperty.groupingMethod}`,
                        '',
                    );
                    if (typeof value === 'string' && aggregationSelectorSegments.length > 0) {
                        value = JSON.parse(value);
                    }
                    const formattedValue = this.formatValue({ property: aggregationProperty, value });
                    set(acc, `${aggregationPropertyPath}.${aggregationProperty.groupingMethod}`, formattedValue);
                    return acc;
                },
                set(
                    {},
                    this.userWidgetDefinition.xAxis!.property.id ?? this.userWidgetDefinition.xAxis!.property.path,
                    formattedGroupValue ?? '-',
                ),
            );
        });
    }

    protected formatValue({
        property: {
            data: { type },
            decimalDigits,
            divisor,
        },
        value,
    }: {
        property: AggregationProperty;
        value: any;
    }): number | string {
        if (!type) {
            return 0;
        }
        switch (type) {
            case 'Date':
            case 'DateTime':
                return new Date(value).getTime();
            case 'Decimal':
            case 'Float':
            case 'Int':
            case 'IntReference':
                const num = Number(value);
                if (Number.isNaN(num)) {
                    return 0;
                }
                return (num / (divisor ?? 1)).toFixed(decimalDigits);
            case 'Enum':
            case 'ExternalReference':
            case 'ID':
            case 'InputStream':
            case 'IntOrString':
            case 'Json':
            case 'String':
            default:
                break;
        }
        return Number.isNaN(Number(value)) ? 0 : Number(value);
    }
}
