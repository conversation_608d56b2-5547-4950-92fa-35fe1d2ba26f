import type { UserWidgetDefinition } from '../../redux/state';
import type { DashboardContextVariables } from '../dashboard-types';
import { AbstractWidget } from '../widgets/abstract-widget';
import { indicatorTile } from '../widgets/indicator-tile-widget-decorator';
import { getCallToActionsFromWidgetDefinition } from './generic-widget-utils';
import { IndicatorTileWidgetQueryBuilder } from './indicator-tile-widget-query-builder';

export const createGenericIndicatorTileWidgetDefinition = (
    userWidgetDefinition: UserWidgetDefinition,
    dashboardId: string,
    widgetId: string,
    group: string,
    contextVariables?: DashboardContextVariables,
    isPreview = false,
): AbstractWidget => {
    const queryBuilder = new IndicatorTileWidgetQueryBuilder(userWidgetDefinition);
    const callToActions = getCallToActionsFromWidgetDefinition(
        userWidgetDefinition,
        queryBuilder.getFilterObject(),
        isPreview,
    );

    @indicatorTile({
        title() {
            return userWidgetDefinition.title || '';
        },
        subtitle() {
            return userWidgetDefinition.subtitle || '';
        },
        callToActions,
        value() {
            return queryBuilder.getValue(this.$.data);
        },
        getQuery() {
            return queryBuilder.build();
        },
        icon() {
            return userWidgetDefinition.icon || '';
        },
    })
    class GenericIndicatorTileWidget extends AbstractWidget {}

    (GenericIndicatorTileWidget.prototype as any).__id = widgetId;
    (GenericIndicatorTileWidget.prototype as any).__dashboardId = dashboardId;
    (GenericIndicatorTileWidget.prototype as any).__group = group;
    (GenericIndicatorTileWidget.prototype as any).__contextVariables = contextVariables;

    return new GenericIndicatorTileWidget();
};
