import { get } from 'lodash';
import { AbstractWidgetQueryBuilder } from '.';
import { Aggregations } from '@sage/xtrem-shared';
import { formatDateToCurrentLocale, formatNumberToCurrentLocale } from '../../service/i18n-service';

export class IndicatorTileWidgetQueryBuilder extends AbstractWidgetQueryBuilder {
    public build(): any {
        return this.buildGroupBySingleValueQuery().buildFilter().query;
    }

    public getValue(data: any): any {
        if (!this.groupByPath) {
            return '';
        }

        const value = get(data, this.groupByPath, '');

        const property = this.userWidgetDefinition.groupBy?.property;
        if (
            value &&
            (property?.data.type === 'Float' ||
                property?.data.type === 'Decimal' ||
                property?.data.type === 'Int' ||
                this.userWidgetDefinition.groupBy?.method === Aggregations.distinctCount)
        ) {
            return formatNumberToCurrentLocale(
                value / (this.userWidgetDefinition.groupBy?.divisor ?? 1),
                this.userWidgetDefinition.decimalDigits,
            );
        }

        if (value && property?.data.type === 'Date') {
            return formatDateToCurrentLocale(value, 'FullDate');
        }

        return value;
    }
}
