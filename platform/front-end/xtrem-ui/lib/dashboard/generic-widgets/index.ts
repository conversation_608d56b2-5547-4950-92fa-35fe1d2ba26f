import { AbstractWidgetQueryBuilder } from './abstract-widget-query-builder';

import type { UserWidgetDefinition } from '../../redux/state';
import type { DashboardContextVariables } from '../dashboard-types';
import type { AbstractWidget } from '../widgets/abstract-widget';
import { createGenericChartWidgetDefinition } from './generic-chart-widget';
import { createGenericIndicatorTileWidgetDefinition } from './generic-indicator-tile-widget';
import { createGenericTableWidgetDefinition } from './generic-table-widget';

export { AbstractWidgetQueryBuilder };
export const GENERIC_PLATFORM_WIDGET_PREFIX = '@sage/xtrem-ui';

export const getGenericWidgetDefinition = (
    userWidgetDefinition: UserWidgetDefinition,
    dashboardId: string,
    widgetId: string,
    group: string,
    contextVariables?: DashboardContextVariables,
    isPreview = false,
): AbstractWidget => {
    switch (userWidgetDefinition.type) {
        case 'TABLE':
            return createGenericTableWidgetDefinition(
                userWidgetDefinition,
                dashboardId,
                widgetId,
                group,
                contextVariables,
                isPreview,
            );
        case 'INDICATOR_TILE':
            return createGenericIndicatorTileWidgetDefinition(
                userWidgetDefinition,
                dashboardId,
                widgetId,
                group,
                contextVariables,
                isPreview,
            );
        case 'LINE_CHART':
        case 'BAR_CHART':
            return createGenericChartWidgetDefinition(
                userWidgetDefinition,
                dashboardId,
                widgetId,
                group,
                contextVariables,
                isPreview,
            );
        default:
            throw new Error('Unhandled widget');
    }
};

export const getGenericWidgetArtifactName = (userWidgetDefinition: UserWidgetDefinition): string => {
    switch (userWidgetDefinition.type) {
        case 'TABLE':
            return `${GENERIC_PLATFORM_WIDGET_PREFIX}/GenericTableWidget`;
        case 'INDICATOR_TILE':
            return `${GENERIC_PLATFORM_WIDGET_PREFIX}/GenericIndicatorTileWidget`;
        case 'LINE_CHART':
            return `${GENERIC_PLATFORM_WIDGET_PREFIX}/GenericLineChartWidget`;
        case 'BAR_CHART':
            return `${GENERIC_PLATFORM_WIDGET_PREFIX}/GenericBarChartWidget`;
        default:
            throw new Error('Unhandled widget');
    }
};
