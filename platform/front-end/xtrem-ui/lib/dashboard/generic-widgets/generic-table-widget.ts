import type { UserWidgetDefinition } from '../../redux/state';
import type { DashboardContextVariables } from '../dashboard-types';
import { AbstractWidget } from '../widgets/abstract-widget';
import { table } from '../widgets/table-widget-decorator';
import { getCallToActionsFromWidgetDefinition } from './generic-widget-utils';
import { TableWidgetQueryBuilder } from './table-widget-query-builder';

export const createGenericTableWidgetDefinition = (
    userWidgetDefinition: UserWidgetDefinition,
    dashboardId: string,
    widgetId: string,
    group: string,
    contextVariables?: DashboardContextVariables,
    isPreview = false,
): AbstractWidget => {
    const queryBuilder = new TableWidgetQueryBuilder(userWidgetDefinition);
    const callToActions = getCallToActionsFromWidgetDefinition(
        userWidgetDefinition,
        queryBuilder.getFilterObject(),
        isPreview,
    );

    @table({
        rowDefinition: queryBuilder.rowDefinition,
        title() {
            return userWidgetDefinition.title || '';
        },
        callToActions,
        content() {
            return queryBuilder.getValue(this.$.data);
        },
        getQuery(queryArgs) {
            return queryBuilder.build(queryArgs);
        },
    })
    class GenericTableWidget extends AbstractWidget {}

    (GenericTableWidget.prototype as any).__id = widgetId;
    (GenericTableWidget.prototype as any).__dashboardId = dashboardId;
    (GenericTableWidget.prototype as any).__group = group;
    (GenericTableWidget.prototype as any).__contextVariables = contextVariables;

    return new GenericTableWidget();
};
