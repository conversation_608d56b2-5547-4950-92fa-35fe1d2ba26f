import { Aggregations, GraphQLTypes } from '@sage/xtrem-shared';
import type { UserWidgetDefinition } from '../../../redux/state';
import * as i18n from '../../../service/i18n-service';
import { GraphQLKind } from '../../../types';
import { ChartWidgetQueryBuilder } from '../chart-widget-query-builder';

export class ChartWidgetQueryBuilderTester extends ChartWidgetQueryBuilder {
    public filters = {};

    public orderBy = {};

    public groupByPath?: string;

    public nodePath: string;

    public query: any;

    public userWidgetDefinition: UserWidgetDefinition;

    public buildPath() {
        return super.buildPath();
    }

    public buildQuery() {
        return super.buildQuery();
    }

    public buildGroupByQuery() {
        return super.buildGroupBySingleValueQuery();
    }

    public buildOrderBy() {
        return super.buildOrderBy();
    }

    public buildFilter() {
        return super.buildFilter();
    }
}

const simpleTextProperty1 = {
    label: 'Product',
    data: {
        type: GraphQLTypes.String,
        kind: GraphQLKind.Scalar,
        isCollection: false,
        name: 'product',
        canFilter: true,
        canSort: true,
        label: 'Product',
        isStored: true,
        isOnInputType: true,
        isOnOutputType: true,
        dataType: 'descriptionDataType',
        targetNode: '',
        enumType: null,
        isCustom: false,
        isMutable: false,
    },
    id: 'product',
    key: 'product',
    labelKey: 'Product',
    labelPath: 'Product',
};
const simpleTextProperty2 = {
    label: 'Description',
    data: {
        type: GraphQLTypes.String,
        kind: GraphQLKind.Scalar,
        isCollection: false,
        name: 'description',
        canFilter: true,
        canSort: true,
        label: 'Description',
        isStored: true,
        isOnInputType: true,
        isOnOutputType: true,
        dataType: 'descriptionDataType',
        targetNode: '',
        enumType: null,
        isCustom: false,
        isMutable: false,
    },
    id: 'description',
    key: 'description',
    labelKey: 'Product',
    labelPath: 'Product',
};

const deepTextProperty = {
    label: 'Provider name',
    data: {
        type: GraphQLTypes.String,
        kind: GraphQLKind.Scalar,
        isCollection: false,
        name: 'provider.name',
        canFilter: true,
        canSort: true,
        label: 'Provider name',
        isStored: true,
        isOnInputType: true,
        isOnOutputType: true,
        dataType: 'descriptionDataType',
        targetNode: '',
        enumType: null,
        isCustom: false,
        isMutable: false,
    },
    id: 'provider.name',
    key: 'provider.name',
    labelKey: 'Provider name',
    labelPath: 'Provider name',
};

const simpleDateProperty = {
    label: 'Release date',
    data: {
        type: GraphQLTypes.Date,
        kind: GraphQLKind.Scalar,
        isCollection: false,
        name: 'releaseDate',
        canFilter: true,
        canSort: true,
        label: 'Release date',
        isStored: true,
        isOnInputType: true,
        isOnOutputType: true,
        dataType: '',
        targetNode: '',
        enumType: null,
        isCustom: false,
        isMutable: false,
    },
    id: 'releaseDate',
    key: 'releaseDate',
    labelKey: 'Release date',
    labelPath: 'Release date',
};

const customDataProperty1 = {
    label: 'testField',
    data: {
        type: GraphQLTypes.String,
        kind: GraphQLKind.Scalar,
        name: '_customData.testField',
        canFilter: true,
        canSort: true,
        label: 'testField',
        isStored: true,
        isOnInputType: true,
        isOnOutputType: true,
        dataType: '',
        targetNode: '',
        enumType: null,
        isCustom: true,
        isMutable: false,
    },
    id: '_customData.testField',
    key: '_customData.testField',
    labelKey: 'testField',
    labelPath: 'testField',
};

const customDataProperty2 = {
    label: 'anotherField',
    data: {
        type: 'Enum',
        kind: GraphQLKind.Scalar,
        name: '_customData.anotherField',
        canFilter: true,
        canSort: true,
        label: 'anotherField',
        isStored: true,
        isOnInputType: true,
        isOnOutputType: true,
        dataType: '',
        targetNode: '',
        enumType: null,
        isCustom: true,
        isMutable: false,
    },
    id: '_customData.anotherField',
    key: '_customData.anotherField',
    labelKey: 'anotherField',
    labelPath: 'anotherField',
};

const customDataProperty3 = {
    label: 'testField',
    data: {
        type: GraphQLTypes.Date,
        kind: GraphQLKind.Scalar,
        name: '_customData.testField',
        canFilter: true,
        canSort: true,
        label: 'testDateField',
        isStored: true,
        isOnInputType: true,
        isOnOutputType: true,
        dataType: '',
        targetNode: '',
        enumType: null,
        isCustom: true,
        isMutable: false,
    },
    id: '_customData.testDateField',
    key: '_customData.testDateField',
    labelKey: 'testDateField',
    labelPath: 'testDateField',
};

const deepCustomDataProperty = {
    label: 'customProviderField',
    data: {
        type: GraphQLTypes.String,
        kind: GraphQLKind.Scalar,
        name: '_customData.customProviderField',
        canFilter: true,
        canSort: true,
        label: 'customProviderField',
        isStored: true,
        isOnInputType: true,
        isOnOutputType: true,
        dataType: '',
        targetNode: '',
        enumType: null,
        isCustom: true,
        isMutable: false,
    },
    id: 'provider._customData.customProviderField',
    key: 'provider._customData.customProviderField',
    labelKey: 'Provider.customProviderField',
    labelPath: 'Provider.customProviderField',
};

describe('chart widget query builder', () => {
    let uwd: UserWidgetDefinition;
    beforeEach(() => {
        jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);

        uwd = {
            node: '@sage/xtrem-show-case/ShowCaseProduct',
            title: 'test',
            type: 'BAR_CHART',
            usedEnums: ['Integer'],
            selectedProperties: {
                [customDataProperty1.id]: {
                    ...customDataProperty1,
                },
                [customDataProperty2.id]: {
                    ...customDataProperty2,
                },
                [simpleTextProperty1.id]: {
                    ...simpleTextProperty1,
                },
                [simpleTextProperty2.id]: {
                    ...simpleTextProperty2,
                },
                [deepCustomDataProperty.id]: {
                    ...deepCustomDataProperty,
                },
                [simpleDateProperty.id]: {
                    ...simpleDateProperty,
                },
                [deepTextProperty.id]: {
                    ...deepTextProperty,
                },
            },
            orderBy: [],
            aggregationsCount: 20,
        };
    });

    describe('query generation', () => {
        it('should build aggregation query with simple text property grouped by a text property', () => {
            uwd.xAxis = { property: simpleTextProperty2 };
            uwd.aggregations = [
                { ...simpleTextProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            __args: {
                                first: 20,
                            },
                            edges: {
                                node: {
                                    group: {
                                        description: {
                                            __aliasFor: 'description',
                                        },
                                    },
                                    values: {
                                        product: {
                                            __aliasFor: 'product',
                                            distinctCount: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should build aggregation query with simple text property grouped by date / year', () => {
            uwd.xAxis = { property: simpleDateProperty, groupBy: 'year' };
            uwd.aggregations = [
                { ...simpleTextProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            __args: {
                                first: 20,
                            },
                            edges: {
                                node: {
                                    group: {
                                        releaseDate: {
                                            __aliasFor: 'releaseDate',
                                            __args: {
                                                by: expect.objectContaining({
                                                    value: 'year',
                                                }),
                                            },
                                        },
                                    },
                                    values: {
                                        product: {
                                            __aliasFor: 'product',
                                            distinctCount: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should build aggregation query with simple text property grouped by date / month', () => {
            uwd.xAxis = { property: simpleDateProperty, groupBy: 'month' };
            uwd.aggregations = [
                { ...simpleTextProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            __args: {
                                first: 20,
                            },
                            edges: {
                                node: {
                                    group: {
                                        releaseDate: {
                                            __aliasFor: 'releaseDate',
                                            __args: {
                                                by: expect.objectContaining({
                                                    value: 'month',
                                                }),
                                            },
                                        },
                                    },
                                    values: {
                                        product: {
                                            __aliasFor: 'product',
                                            distinctCount: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should build aggregation query with two text properties', () => {
            uwd.xAxis = { property: simpleDateProperty, groupBy: 'month' };
            uwd.aggregations = [
                { ...simpleTextProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title 1' },
                { ...simpleTextProperty2, groupingMethod: Aggregations.distinctCount, title: 'Column title 2' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            __args: {
                                first: 20,
                            },
                            edges: {
                                node: {
                                    group: {
                                        releaseDate: {
                                            __aliasFor: 'releaseDate',
                                            __args: {
                                                by: expect.objectContaining({
                                                    value: 'month',
                                                }),
                                            },
                                        },
                                    },
                                    values: {
                                        product: {
                                            __aliasFor: 'product',
                                            distinctCount: true,
                                        },
                                        description: {
                                            __aliasFor: 'description',
                                            distinctCount: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should build aggregation query with a deep text properties', () => {
            uwd.xAxis = { property: simpleDateProperty, groupBy: 'month' };
            uwd.aggregations = [
                { ...deepTextProperty, groupingMethod: Aggregations.distinctCount, title: 'Column title 1' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            __args: {
                                first: 20,
                            },
                            edges: {
                                node: {
                                    group: {
                                        releaseDate: {
                                            __aliasFor: 'releaseDate',
                                            __args: {
                                                by: expect.objectContaining({
                                                    value: 'month',
                                                }),
                                            },
                                        },
                                    },
                                    values: {
                                        provider__name: {
                                            __aliasFor: 'provider',
                                            name: {
                                                distinctCount: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should build aggregation query with a custom data property grouped by a text property', () => {
            uwd.xAxis = { property: simpleTextProperty2 };
            uwd.aggregations = [
                { ...customDataProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            __args: {
                                first: 20,
                            },
                            edges: {
                                node: {
                                    group: {
                                        description: {
                                            __aliasFor: 'description',
                                        },
                                    },
                                    values: {
                                        _customData__testField: {
                                            __aliasFor: '_customData',
                                            __args: {
                                                selector: 'testField',
                                            },
                                            distinctCount: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should build aggregation query with two custom data property grouped by a text property', () => {
            uwd.xAxis = { property: simpleTextProperty2 };
            uwd.aggregations = [
                { ...customDataProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title 1' },
                { ...customDataProperty2, groupingMethod: Aggregations.distinctCount, title: 'Column title 2' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);

            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            __args: {
                                first: 20,
                            },
                            edges: {
                                node: {
                                    group: {
                                        description: {
                                            __aliasFor: 'description',
                                        },
                                    },
                                    values: {
                                        _customData__testField: {
                                            __aliasFor: '_customData',
                                            __args: {
                                                selector: 'testField',
                                            },
                                            distinctCount: true,
                                        },
                                        _customData__anotherField: {
                                            __aliasFor: '_customData',
                                            __args: {
                                                selector: 'anotherField',
                                            },
                                            distinctCount: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should build aggregation query with a deep custom data property grouped by a text property', () => {
            uwd.xAxis = { property: simpleTextProperty2 };
            uwd.aggregations = [
                { ...deepCustomDataProperty, groupingMethod: Aggregations.distinctCount, title: 'Column title 1' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);

            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            __args: {
                                first: 20,
                            },
                            edges: {
                                node: {
                                    group: {
                                        description: {
                                            __aliasFor: 'description',
                                        },
                                    },
                                    values: {
                                        provider___customData__customProviderField: {
                                            __aliasFor: 'provider',
                                            _customData: {
                                                __args: {
                                                    selector: 'customProviderField',
                                                },
                                                distinctCount: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should build  a complex aggregation query with a bunch of properties', () => {
            uwd.xAxis = { property: simpleTextProperty2 };
            uwd.aggregations = [
                { ...simpleTextProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title 1' },
                { ...customDataProperty1, groupingMethod: Aggregations.min, title: 'Column title 2' },
                { ...customDataProperty2, groupingMethod: Aggregations.distinctCount, title: 'Column title 3' },
                { ...simpleDateProperty, groupingMethod: Aggregations.max, title: 'Column title 4' },
                { ...deepCustomDataProperty, groupingMethod: Aggregations.distinctCount, title: 'Column title 5' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);

            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            __args: {
                                first: 20,
                            },
                            edges: {
                                node: {
                                    group: {
                                        description: {
                                            __aliasFor: 'description',
                                        },
                                    },
                                    values: {
                                        _customData__anotherField: {
                                            __aliasFor: '_customData',
                                            __args: {
                                                selector: 'anotherField',
                                            },
                                            distinctCount: true,
                                        },
                                        _customData__testField: {
                                            __aliasFor: '_customData',
                                            __args: {
                                                selector: 'testField',
                                            },
                                            min: true,
                                        },
                                        product: {
                                            __aliasFor: 'product',
                                            distinctCount: true,
                                        },
                                        provider___customData__customProviderField: {
                                            __aliasFor: 'provider',
                                            _customData: {
                                                __args: {
                                                    selector: 'customProviderField',
                                                },
                                                distinctCount: true,
                                            },
                                        },
                                        releaseDate: {
                                            __aliasFor: 'releaseDate',
                                            max: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should build an aggregation query with custom field in the group definition', () => {
            uwd.xAxis = { property: customDataProperty1 };
            uwd.aggregations = [
                { ...simpleTextProperty2, groupingMethod: Aggregations.distinctCount, title: 'Column title 1' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);

            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            __args: {
                                first: 20,
                            },
                            edges: {
                                node: {
                                    group: {
                                        _customData__testField: {
                                            __aliasFor: '_customData',
                                            __args: {
                                                selector: 'testField',
                                            },
                                        },
                                    },
                                    values: {
                                        description: {
                                            __aliasFor: 'description',
                                            distinctCount: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should build an aggregation query with deep custom field in the group definition', () => {
            uwd.xAxis = { property: deepCustomDataProperty };
            uwd.aggregations = [
                { ...simpleTextProperty2, groupingMethod: Aggregations.distinctCount, title: 'Column title 1' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);

            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            __args: {
                                first: 20,
                            },
                            edges: {
                                node: {
                                    group: {
                                        provider___customData__customProviderField: {
                                            __aliasFor: 'provider',
                                            _customData: {
                                                __args: {
                                                    selector: 'customProviderField',
                                                },
                                            },
                                        },
                                    },
                                    values: {
                                        description: {
                                            __aliasFor: 'description',
                                            distinctCount: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should build an aggregation query with custom date field in the group definition', () => {
            uwd.xAxis = { property: customDataProperty3, groupBy: 'month' };
            uwd.aggregations = [
                { ...simpleTextProperty2, groupingMethod: Aggregations.distinctCount, title: 'Column title 1' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);

            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            __args: {
                                first: 20,
                            },
                            edges: {
                                node: {
                                    group: {
                                        _customData__testDateField: {
                                            __aliasFor: '_customData',
                                            __args: {
                                                by: expect.objectContaining({ value: 'month' }),
                                                selector: 'testDateField',
                                            },
                                        },
                                    },
                                    values: {
                                        description: {
                                            __aliasFor: 'description',
                                            distinctCount: true,
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });
    });

    describe('response parsing', () => {
        it('should parse a simple text field from the response', () => {
            uwd.xAxis = { property: simpleDateProperty };
            uwd.aggregations = [
                { ...simpleTextProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
            expect(
                queryBuilder.getValue({
                    xtremShowCase: {
                        showCaseProduct: {
                            queryAggregate: {
                                edges: [
                                    {
                                        node: {
                                            group: { releaseDate: '2019-09-01' },
                                            values: { product: { distinctCount: 12 } },
                                        },
                                    },
                                    {
                                        node: {
                                            group: { releaseDate: '2019-10-01' },
                                            values: { product: { distinctCount: 30 } },
                                        },
                                    },
                                ],
                            },
                        },
                    },
                }),
            ).toEqual([
                { product: { distinctCount: 12 }, releaseDate: '2019-09-01' },
                { product: { distinctCount: 30 }, releaseDate: '2019-10-01' },
            ]);
        });

        it('should parse custom data property from response', () => {
            uwd.xAxis = { property: simpleDateProperty };
            uwd.aggregations = [
                { ...customDataProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
            expect(
                queryBuilder.getValue({
                    xtremShowCase: {
                        showCaseProduct: {
                            queryAggregate: {
                                edges: [
                                    {
                                        node: {
                                            group: { releaseDate: '2019-09-01' },
                                            values: { _customData__testField: { distinctCount: 0 } },
                                        },
                                    },
                                    {
                                        node: {
                                            group: { releaseDate: '2019-10-01' },
                                            values: { _customData__testField: { distinctCount: 0 } },
                                        },
                                    },
                                ],
                            },
                        },
                    },
                }),
            ).toEqual([
                {
                    _customData: {
                        testField: {
                            distinctCount: 0,
                        },
                    },
                    releaseDate: '2019-09-01',
                },
                {
                    _customData: {
                        testField: {
                            distinctCount: 0,
                        },
                    },
                    releaseDate: '2019-10-01',
                },
            ]);
        });

        it('should parse multiple custom data properties from response', () => {
            uwd.xAxis = { property: simpleDateProperty };
            uwd.aggregations = [
                { ...customDataProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title 1' },
                { ...customDataProperty2, groupingMethod: Aggregations.distinctCount, title: 'Column title 2' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
            expect(
                queryBuilder.getValue({
                    xtremShowCase: {
                        showCaseProduct: {
                            queryAggregate: {
                                edges: [
                                    {
                                        node: {
                                            group: { releaseDate: '2019-09-01' },
                                            values: {
                                                _customData__testField: { distinctCount: 0 },
                                                _customData__anotherField: { distinctCount: 0 },
                                            },
                                        },
                                    },
                                    {
                                        node: {
                                            group: { releaseDate: '2019-10-01' },
                                            values: {
                                                _customData__testField: { distinctCount: 2 },
                                                _customData__anotherField: { distinctCount: 2 },
                                            },
                                        },
                                    },
                                ],
                            },
                        },
                    },
                }),
            ).toEqual([
                {
                    _customData: {
                        anotherField: {
                            distinctCount: 0,
                        },
                        testField: {
                            distinctCount: 0,
                        },
                    },
                    releaseDate: '2019-09-01',
                },
                {
                    _customData: {
                        anotherField: {
                            distinctCount: 2,
                        },
                        testField: {
                            distinctCount: 2,
                        },
                    },
                    releaseDate: '2019-10-01',
                },
            ]);
        });

        it('should parse a deep custom data property from the response', () => {
            uwd.xAxis = { property: simpleDateProperty };
            uwd.aggregations = [
                { ...deepCustomDataProperty, groupingMethod: Aggregations.distinctCount, title: 'Column title 1' },
            ];
            const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
            expect(
                queryBuilder.getValue({
                    xtremShowCase: {
                        showCaseProduct: {
                            queryAggregate: {
                                edges: [
                                    {
                                        node: {
                                            group: { releaseDate: '2019-09-01' },
                                            values: {
                                                provider___customData__customProviderField: {
                                                    _customData: { distinctCount: 2 },
                                                },
                                            },
                                        },
                                    },
                                    {
                                        node: {
                                            group: { releaseDate: '2019-10-01' },
                                            values: {
                                                provider___customData__customProviderField: {
                                                    _customData: { distinctCount: 0 },
                                                },
                                            },
                                        },
                                    },
                                ],
                            },
                        },
                    },
                }),
            ).toEqual([
                {
                    provider: {
                        _customData: {
                            customProviderField: {
                                distinctCount: 2,
                            },
                        },
                    },
                    releaseDate: '2019-09-01',
                },
                {
                    provider: {
                        _customData: {
                            customProviderField: {
                                distinctCount: 0,
                            },
                        },
                    },
                    releaseDate: '2019-10-01',
                },
            ]);
        });
    });

    it('should parse custom data date group month from response', () => {
        uwd.xAxis = { property: customDataProperty3, groupBy: 'month' };
        uwd.aggregations = [
            { ...simpleTextProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title' },
        ];
        const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
        expect(
            queryBuilder.getValue({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            edges: [
                                {
                                    node: {
                                        group: { _customData__testDateField: '"2019-09-01"' },
                                        values: { product: { distinctCount: 6 } },
                                    },
                                },
                                {
                                    node: {
                                        group: { _customData__testDateField: '"2019-10-01"' },
                                        values: { product: { distinctCount: 5 } },
                                    },
                                },
                            ],
                        },
                    },
                },
            }),
        ).toEqual([
            {
                product: {
                    distinctCount: 6,
                },
                _customData: {
                    testDateField: '09/2019',
                },
            },
            {
                product: {
                    distinctCount: 5,
                },
                _customData: {
                    testDateField: '10/2019',
                },
            },
        ]);
    });

    it('should parse custom data text group from response', () => {
        uwd.xAxis = { property: customDataProperty1 };
        uwd.aggregations = [
            { ...simpleTextProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title' },
        ];
        const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
        expect(
            queryBuilder.getValue({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            edges: [
                                {
                                    node: {
                                        group: { _customData__testField: '"a"' },
                                        values: { product: { distinctCount: 6 } },
                                    },
                                },
                                {
                                    node: {
                                        group: { _customData__testField: '"b"' },
                                        values: { product: { distinctCount: 5 } },
                                    },
                                },
                            ],
                        },
                    },
                },
            }),
        ).toEqual([
            {
                product: {
                    distinctCount: 6,
                },
                _customData: {
                    testField: 'a',
                },
            },
            {
                product: {
                    distinctCount: 5,
                },
                _customData: {
                    testField: 'b',
                },
            },
        ]);
    });

    it('should parse deep custom data text group from response', () => {
        uwd.xAxis = { property: deepCustomDataProperty };
        uwd.aggregations = [
            { ...simpleTextProperty1, groupingMethod: Aggregations.distinctCount, title: 'Column title' },
        ];
        const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);
        expect(
            queryBuilder.getValue({
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            edges: [
                                {
                                    node: {
                                        group: {
                                            provider___customData__customProviderField: {
                                                _customData: '"d"',
                                            },
                                        },
                                        values: { product: { distinctCount: 6 } },
                                    },
                                },
                                {
                                    node: {
                                        group: {
                                            provider___customData__customProviderField: {
                                                _customData: '"c"',
                                            },
                                        },
                                        values: { product: { distinctCount: 5 } },
                                    },
                                },
                            ],
                        },
                    },
                },
            }),
        ).toEqual([
            {
                product: {
                    distinctCount: 6,
                },
                provider: {
                    _customData: {
                        customProviderField: 'd',
                    },
                },
            },
            {
                product: {
                    distinctCount: 5,
                },
                provider: {
                    _customData: {
                        customProviderField: 'c',
                    },
                },
            },
        ]);
    });

    it('Should build a correct aggregation query when reference field is used in xAxis', () => {
        uwd.aggregations = [
            {
                id: 'lineAmountExcludingTax',
                key: 'lineAmountExcludingTax',
                data: {
                    kind: 'SCALAR',
                    name: 'lineAmountExcludingTax',
                    type: 'Decimal',
                    label: 'Line amount excluding tax',
                    canSort: true,
                    dataType: 'priceInSalesPrice',
                    enumType: null,
                    isCustom: false,
                    canFilter: true,
                    isMutable: false,
                    targetNode: '',
                    isCollection: false,
                    isStored: true,
                    isOnInputType: false,
                    isOnOutputType: true,
                },
                color: '#008247',
                label: 'Line amount excluding tax',
                title: 'Line amount excluding tax',
                divisor: 1,
                labelKey: 'Line amount excluding tax',
                labelPath: 'Line amount excluding tax',
                groupingMethod: Aggregations.sum,
            },
        ];
        uwd.xAxis = {
            property: {
                id: 'item.name',
                key: 'item.name',
                data: {
                    kind: 'SCALAR',
                    name: 'name',
                    type: 'String',
                    label: 'Name',
                    canSort: true,
                    dataType: 'localizedName',
                    enumType: null,
                    isCustom: false,
                    canFilter: true,
                    isMutable: false,
                    targetNode: '',
                    isCollection: false,
                    isStored: true,
                    isOnInputType: true,
                    isOnOutputType: true,
                },
                label: 'Name',
                labelKey: 'Item.Name',
                labelPath: 'Item.Name',
            },
        };
        const queryBuilder = new ChartWidgetQueryBuilderTester(uwd);

        expect(queryBuilder.build()).toEqual({
            xtremShowCase: {
                showCaseProduct: {
                    queryAggregate: {
                        __args: {
                            first: 20,
                        },
                        edges: {
                            node: {
                                group: {
                                    item__name: {
                                        __aliasFor: 'item',
                                        name: true,
                                    },
                                },
                                values: {
                                    lineAmountExcludingTax: {
                                        __aliasFor: 'lineAmountExcludingTax',
                                        sum: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
    });
});
