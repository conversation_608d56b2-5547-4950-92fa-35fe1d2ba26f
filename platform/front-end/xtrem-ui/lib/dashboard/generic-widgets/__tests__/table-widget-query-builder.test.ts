import { GraphQLTypes, type FilterProperty, type Property } from '@sage/xtrem-shared';
import type { OrderByProperty, TableProperty, UserWidgetDefinition } from '../../../redux/state';
import * as i18n from '../../../service/i18n-service';
import type { RowDefinition } from '../../widgets/table-widget-decorator';
import { TableWidgetQueryBuilder } from '../table-widget-query-builder';
import { GraphQLKind } from '../../../types';

export class TableWidgetQueryBuilderTester extends TableWidgetQueryBuilder {
    public filters = {};

    public orderBy = {};

    public groupByPath?: string;

    public nodePath: string;

    public query: any;

    public rowDefinition: RowDefinition<any>;

    public userWidgetDefinition: UserWidgetDefinition;

    public formatValue({ column, value }: { column: TableProperty; value: any }): string {
        return super.formatValue({ column, value });
    }

    public buildPath() {
        return super.buildPath();
    }

    public buildQuery() {
        return super.buildQuery();
    }

    public buildOrderBy() {
        return super.buildOrderBy();
    }

    public buildFilter() {
        return super.buildFilter();
    }
}

describe('table widget query builder', () => {
    let uwd: UserWidgetDefinition;
    beforeEach(() => {
        jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);

        uwd = {
            title: 'prov',
            category: 'DEMO_CATEGORY',
            node: '@sage/xtrem-show-case/ShowCaseProvider',
            filters: [
                {
                    label: 'Text Field',
                    filterType: 'contains',
                    filterValue: 'a',
                    data: {
                        type: GraphQLTypes.String,
                    },
                    id: 'textField',
                    labelPath: 'textField',
                } as FilterProperty,
                {
                    label: 'Text Field',
                    filterType: 'contains',
                    filterValue: 'o',
                    data: {
                        type: GraphQLTypes.String,
                    },
                    id: 'textField',
                    labelPath: 'textField',
                } as FilterProperty,
                {
                    label: 'Name',
                    filterType: 'startsWith',
                    filterValue: 's',
                    data: {
                        type: GraphQLTypes.String,
                    },
                    id: 'item.name',
                    labelPath: 'item.name',
                } as FilterProperty,
            ],
            orderBy: [
                {
                    label: 'Integer Field',
                    order: 'descending',
                    data: {
                        type: GraphQLTypes.Int,
                    },
                    id: 'integerField',
                    labelPath: 'integerField',
                } as OrderByProperty,
                {
                    label: 'Date Field',
                    order: 'ascending',
                    data: {
                        type: GraphQLTypes.Date,
                    },
                    id: 'dateField',
                    labelPath: 'dateField',
                } as OrderByProperty,
                {
                    label: 'Name',
                    order: 'ascending',
                    data: {
                        type: GraphQLTypes.String,
                    },
                    id: 'item.name',
                    labelPath: 'item.name',
                } as OrderByProperty,
            ],
            selectedProperties: {
                textField: {
                    label: 'Text Field',
                    data: {
                        type: GraphQLTypes.String,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'textField',
                        label: 'Text Field',
                    } as any,
                    id: 'textField',
                    labelPath: 'textField',
                } as Property,
                integerField: {
                    label: 'Integer Field',
                    data: {
                        type: GraphQLTypes.Int,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'integerField',
                        label: 'Integer Field',
                    } as any,
                    id: 'integerField',
                    labelPath: 'integerField',
                } as Property,
                decimalField: {
                    label: 'Decimal Field',
                    data: {
                        type: GraphQLTypes.Decimal,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'decimalField',
                        label: 'Decimal Field',
                    } as any,
                    id: 'decimalField',
                    labelPath: 'decimalField',
                } as Property,
                booleanField: {
                    label: 'Boolean Field',
                    data: {
                        type: GraphQLTypes.Boolean,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'booleanField',
                        label: 'Boolean Field',
                    } as any,
                    id: 'booleanField',
                    labelPath: 'booleanField',
                } as Property,
                dateField: {
                    label: 'Date Field',
                    data: {
                        type: GraphQLTypes.Date,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'dateField',
                        label: 'Date Field',
                    } as any,
                    id: 'dateField',
                    labelPath: 'dateField',
                } as Property,
                'item.name': {
                    label: 'Name',
                    data: {
                        type: GraphQLTypes.String,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'name',
                        label: 'Name',
                    } as any,
                    id: 'item.name',
                    labelPath: 'item.name',
                } as Property,
            },
            type: 'TABLE',
            columns: [
                {
                    label: 'Boolean Field',
                    title: 'Boolean Title',
                    presentation: 'Checkbox' as any,
                    data: {
                        type: GraphQLTypes.Boolean,
                    },
                    id: 'booleanField',
                    labelPath: 'booleanField',
                } as TableProperty,
                {
                    label: 'Text Field',
                    title: 'Text Title',
                    presentation: 'Text' as any,
                    data: {
                        type: GraphQLTypes.String,
                    },
                    id: 'textField',
                    labelPath: 'textField',
                } as TableProperty,
                {
                    label: 'Date Field',
                    title: 'Date Title',
                    presentation: 'Date' as any,
                    data: {
                        type: GraphQLTypes.Date,
                    },
                    id: 'dateField',
                    labelPath: 'dateField',
                } as TableProperty,
                {
                    label: 'Integer Field',
                    title: 'Integer Title',
                    presentation: 'Numeric' as any,
                    data: {
                        type: GraphQLTypes.Int,
                    },
                    id: 'integerField',
                    labelPath: 'integerField',
                } as TableProperty,
                {
                    label: 'Decimal Field',
                    title: 'Decimal Title',
                    presentation: 'Label' as any,
                    formatting: 2,
                    data: {
                        type: GraphQLTypes.Decimal,
                    },
                    id: 'decimalField',
                    labelPath: 'decimalField',
                } as TableProperty,
                {
                    label: 'Name',
                    title: 'Name title',
                    presentation: 'Text' as any,
                    data: {
                        type: GraphQLTypes.String,
                    },
                    id: 'item.name',
                    labelPath: 'item.name',
                } as TableProperty,
            ],
        };
    });

    describe('filters', () => {
        it('should be able to set an empty filter', () => {
            uwd.filters = [];
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.buildFilter().filters).toEqual({});
        });

        it('should be able to combine filters for different properties', () => {
            uwd.filters?.push({
                label: 'Integer Field',
                filterType: 'greaterThan',
                filterValue: '12',
                data: {
                    type: GraphQLTypes.Int,
                },
                id: 'integerField',
                labelPath: 'integerField',
            } as FilterProperty);
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.buildFilter().filters).toEqual({
                integerField: {
                    _and: [
                        {
                            _gt: '12',
                        },
                    ],
                },
                item: {
                    name: {
                        _and: [
                            {
                                _options: 'i',
                                _regex: '^s',
                            },
                        ],
                    },
                },
                textField: {
                    _and: [
                        {
                            _options: 'i',
                            _regex: 'a',
                        },
                        {
                            _options: 'i',
                            _regex: 'o',
                        },
                    ],
                },
            });
        });

        it('should be able to combine filters for the same property', () => {
            uwd.filters?.pop();
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.buildFilter().filters).toEqual({
                textField: {
                    _and: [
                        {
                            _options: 'i',
                            _regex: 'a',
                        },
                        {
                            _options: 'i',
                            _regex: 'o',
                        },
                    ],
                },
            });
        });

        it('should be able to generate a date filter', () => {
            uwd.filters = [
                {
                    label: 'Date Field',
                    filterType: 'lessThan',
                    filterValue: {
                        formattedValue: '03/08/2023',
                        rawValue: '2023-03-08',
                    },
                    data: {
                        type: GraphQLTypes.Date,
                    },
                    id: 'dateField',
                    labelPath: 'dateField',
                } as FilterProperty,
            ];
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.buildFilter().filters).toEqual({
                dateField: {
                    _and: [
                        {
                            _lt: '2023-03-08',
                        },
                    ],
                },
            });
        });

        it('should be able to generate a date range filter', () => {
            uwd.filters = [
                {
                    label: 'Date field',
                    filterType: 'inRange',
                    filterValue: [
                        {
                            formattedValue: '03/01/2023',
                            rawValue: '2023-03-01',
                        },
                        {
                            formattedValue: '03/08/2023',
                            rawValue: '2023-03-08',
                        },
                    ],
                    data: {
                        type: GraphQLTypes.Date,
                    },
                    id: 'dateField',
                    labelPath: 'dateField',
                } as FilterProperty,
            ];
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.buildFilter().filters).toEqual({
                dateField: {
                    _and: [
                        {
                            _gte: '2023-03-01',
                            _lte: '2023-03-08',
                        },
                    ],
                },
            });
        });
    });

    describe('order by', () => {
        it('should be able to set an empty order by', () => {
            uwd.orderBy = [];
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.buildOrderBy().orderBy).toEqual({});
        });

        it('should be able to combine multiple sorting conditions', () => {
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            // test with strings as order of keys is important
            expect(JSON.stringify(queryBuilder.buildOrderBy().orderBy)).toBe(
                JSON.stringify({
                    integerField: -1,
                    dateField: 1,
                    item: { name: 1 },
                }),
            );
        });
    });

    describe('path', () => {
        it('should be able to set the right query path', () => {
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.buildPath().nodePath).toEqual('xtremShowCase.showCaseProvider');
        });
    });

    describe('format values for bms', () => {
        it('should be able to format boolean values', () => {
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(
                queryBuilder.formatValue({
                    value: true,
                    column: uwd.columns![0],
                }),
            ).toBe('True');
            expect(
                queryBuilder.formatValue({
                    value: false,
                    column: uwd.columns![0],
                }),
            ).toBe('False');
            expect(
                queryBuilder.formatValue({
                    value: 'true',
                    column: uwd.columns![0],
                }),
            ).toBe('False');
        });

        it('should be able to format numeric values', () => {
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(
                queryBuilder.formatValue({
                    value: 42,
                    column: uwd.columns![4],
                }),
            ).toBe('42.00');
        });
    });

    describe('get value from data', () => {
        it('should be able to get content from data', () => {
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            const data = {
                xtremShowCase: {
                    showCaseProvider: {
                        query: {
                            edges: [
                                {
                                    cursor: '2',
                                    node: {
                                        booleanField: true,
                                        textField: 'Amazon',
                                        dateField: '2019-06-02',
                                        decimalField: '1.123',
                                        integerField: 2,
                                        item__name: {
                                            name: 'Scooter',
                                        },
                                    },
                                },
                                {
                                    cursor: '3',
                                    node: {
                                        booleanField: false,
                                        textField: 'Ali Express',
                                        dateField: '2020-06-02',
                                        decimalField: '3',
                                        integerField: 4,
                                        item__name: {
                                            name: 'Moto',
                                        },
                                    },
                                },
                            ],
                        },
                    },
                },
            };
            expect(queryBuilder.getValue(data)).toEqual([
                {
                    _id: '2',
                    line2: '02/06/2019',
                    line2Right: '2',
                    line3: '1.12',
                    line3Right: 'Scooter',
                    title: 'True',
                    titleRight: 'Amazon',
                },
                {
                    _id: '3',
                    line2: '02/06/2020',
                    line2Right: '4',
                    line3: '3.00',
                    line3Right: 'Moto',
                    title: 'False',
                    titleRight: 'Ali Express',
                },
            ]);
        });
        it('should be able to get content from legacy data', () => {
            const queryBuilder = new TableWidgetQueryBuilderTester({
                title: 'prov',
                category: 'DEMO_CATEGORY',
                node: '@sage/xtrem-authorization/RoleActivity',
                type: 'TABLE',
                selectedProperties: {
                    _id: {
                        data: {
                            kind: GraphQLKind.Scalar,
                            name: '_id',
                            type: 'Id',
                            label: '_id',
                            isCollection: false,
                        },
                        path: '_id',
                        label: '_id',
                        labelPath: '_id',
                    },
                },
                columns: [
                    {
                        data: {
                            type: 'Id',
                        },
                        path: '_id',
                        label: '_id',
                        title: '_id',
                        labelPath: '_id',
                        presentation: 'Text',
                    },
                ],
            } as any);
            const data = {
                xtremAuthorization: {
                    roleActivity: {
                        query: {
                            edges: [
                                {
                                    cursor: '["Support User",500]#64',
                                    node: {
                                        _id: '6281',
                                    },
                                },
                                {
                                    cursor: '["Support User",11700]#14',
                                    node: {
                                        _id: '6258',
                                    },
                                },
                                {
                                    cursor: '["Support User",11800]#77',
                                    node: {
                                        _id: '6287',
                                    },
                                },
                            ],
                        },
                    },
                },
            };
            expect(queryBuilder.getValue(data)).toEqual([
                {
                    _id: '["Support User",500]#64',
                    title: '6281',
                },
                {
                    _id: '["Support User",11700]#14',
                    title: '6258',
                },
                {
                    _id: '["Support User",11800]#77',
                    title: '6287',
                },
            ]);
        });
    });

    describe('query', () => {
        it('should be able to set the right query', () => {
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.buildQuery().buildQuery().query).toEqual({
                xtremShowCase: {
                    showCaseProvider: {
                        query: {
                            edges: {
                                node: {
                                    _id: true,
                                    booleanField: {
                                        __aliasFor: 'booleanField',
                                    },
                                    dateField: {
                                        __aliasFor: 'dateField',
                                    },
                                    decimalField: {
                                        __aliasFor: 'decimalField',
                                    },
                                    integerField: {
                                        __aliasFor: 'integerField',
                                    },
                                    item__name: {
                                        __aliasFor: 'item',
                                        name: true,
                                    },
                                    textField: {
                                        __aliasFor: 'textField',
                                    },
                                },
                                cursor: true,
                            },
                        },
                    },
                },
            });
        });

        it('should be able to set the right query with filters & sort conditions', () => {
            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProvider: {
                        query: {
                            __args: {
                                filter: '{"textField":{"_and":[{"_regex":"a","_options":"i"},{"_regex":"o","_options":"i"}]},"item":{"name":{"_and":[{"_regex":"^s","_options":"i"}]}}}',
                                orderBy: '{"integerField":-1,"dateField":1,"item":{"name":1}}',
                            },
                            edges: {
                                node: {
                                    _id: true,
                                    booleanField: {
                                        __aliasFor: 'booleanField',
                                    },
                                    dateField: {
                                        __aliasFor: 'dateField',
                                    },
                                    decimalField: {
                                        __aliasFor: 'decimalField',
                                    },
                                    integerField: {
                                        __aliasFor: 'integerField',
                                    },
                                    item__name: {
                                        __aliasFor: 'item',
                                        name: true,
                                    },
                                    textField: {
                                        __aliasFor: 'textField',
                                    },
                                },
                                cursor: true,
                            },
                        },
                    },
                },
            });
        });

        it('should be able to set the right query with a custom field', () => {
            uwd.selectedProperties!['_customData.myCustomField'] = {
                label: 'My Test Custom Field',
                title: 'My Test Custom Field',
                presentation: 'Label' as any,
                data: {
                    type: GraphQLTypes.String,
                },
                id: '_customData.myCustomField',
                labelPath: '_customData.myCustomField',
            } as TableProperty;
            uwd.columns!.push({
                label: 'My Test Custom Field',
                title: 'My Test Custom Field',
                presentation: 'Label' as any,
                data: {
                    type: GraphQLTypes.String,
                },
                id: '_customData.myCustomField',
                labelPath: '_customData.myCustomField',
            } as TableProperty);

            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.buildQuery().buildQuery().query).toEqual({
                xtremShowCase: {
                    showCaseProvider: {
                        query: {
                            edges: {
                                node: {
                                    _customData__myCustomField: {
                                        __aliasFor: '_customData',
                                    },
                                    _id: true,
                                    booleanField: {
                                        __aliasFor: 'booleanField',
                                    },
                                    dateField: {
                                        __aliasFor: 'dateField',
                                    },
                                    decimalField: {
                                        __aliasFor: 'decimalField',
                                    },
                                    integerField: {
                                        __aliasFor: 'integerField',
                                    },
                                    item__name: {
                                        __aliasFor: 'item',
                                        name: true,
                                    },
                                    textField: {
                                        __aliasFor: 'textField',
                                    },
                                },
                                cursor: true,
                            },
                        },
                    },
                },
            });
        });

        it('should be able to set the right query with a custom field with sorting', () => {
            uwd.selectedProperties!['_customData.myCustomField'] = {
                label: 'My Test Custom Field',
                title: 'My Test Custom Field',
                presentation: 'Label' as any,
                data: {
                    type: GraphQLTypes.String,
                },
                id: '_customData.myCustomField',
                labelPath: '_customData.myCustomField',
            } as TableProperty;
            uwd.columns!.push({
                label: 'My Test Custom Field',
                title: 'My Test Custom Field',
                presentation: 'Label' as any,
                data: {
                    type: GraphQLTypes.String,
                },
                id: '_customData.myCustomField',
                labelPath: '_customData.myCustomField',
            } as TableProperty);
            uwd.filters = [];
            uwd.orderBy = [
                {
                    label: 'My Test Custom Field',
                    title: 'My Test Custom Field',
                    data: {
                        type: GraphQLTypes.String,
                    },
                    id: '_customData.myCustomField',
                    labelPath: '_customData.myCustomField',
                    order: 'ascending',
                } as any,
            ];

            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProvider: {
                        query: {
                            __args: {
                                orderBy: '{"_customData":{"myCustomField":1}}',
                            },
                            edges: {
                                node: {
                                    _customData__myCustomField: {
                                        __aliasFor: '_customData',
                                    },
                                    _id: true,
                                    booleanField: {
                                        __aliasFor: 'booleanField',
                                    },
                                    dateField: {
                                        __aliasFor: 'dateField',
                                    },
                                    decimalField: {
                                        __aliasFor: 'decimalField',
                                    },
                                    integerField: {
                                        __aliasFor: 'integerField',
                                    },
                                    item__name: {
                                        __aliasFor: 'item',
                                        name: true,
                                    },
                                    textField: {
                                        __aliasFor: 'textField',
                                    },
                                },
                                cursor: true,
                            },
                        },
                    },
                },
            });
        });
        it('should be able to set the right query with a custom field with filter', () => {
            uwd.selectedProperties!['_customData.myCustomField'] = {
                label: 'My Test Custom Field',
                title: 'My Test Custom Field',
                presentation: 'Label' as any,
                data: {
                    type: GraphQLTypes.String,
                },
                id: '_customData.myCustomField',
                labelPath: '_customData.myCustomField',
            } as TableProperty;
            uwd.columns!.push({
                label: 'My Test Custom Field',
                title: 'My Test Custom Field',
                presentation: 'Label' as any,
                data: {
                    type: GraphQLTypes.String,
                },
                id: '_customData.myCustomField',
                labelPath: '_customData.myCustomField',
            } as TableProperty);
            uwd.filters = [
                {
                    label: 'My Test Custom Field',
                    title: 'My Test Custom Field',
                    data: {
                        type: GraphQLTypes.String,
                    },
                    id: '_customData.myCustomField',
                    labelPath: '_customData.myCustomField',
                    filterType: 'equals',
                    filterValue: 'abc',
                } as any,
            ];
            uwd.orderBy = [];

            const queryBuilder = new TableWidgetQueryBuilderTester(uwd);
            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProvider: {
                        query: {
                            __args: {
                                filter: '{"_customData":{"myCustomField":{"_and":[{"_eq":"abc"}]}}}',
                            },
                            edges: {
                                node: {
                                    _customData__myCustomField: {
                                        __aliasFor: '_customData',
                                    },
                                    _id: true,
                                    booleanField: {
                                        __aliasFor: 'booleanField',
                                    },
                                    dateField: {
                                        __aliasFor: 'dateField',
                                    },
                                    decimalField: {
                                        __aliasFor: 'decimalField',
                                    },
                                    integerField: {
                                        __aliasFor: 'integerField',
                                    },
                                    item__name: {
                                        __aliasFor: 'item',
                                        name: true,
                                    },
                                    textField: {
                                        __aliasFor: 'textField',
                                    },
                                },
                                cursor: true,
                            },
                        },
                    },
                },
            });
        });
    });
});
