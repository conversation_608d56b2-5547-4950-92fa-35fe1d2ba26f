const goToMock = jest.fn();
jest.mock('../../../service/router', () => ({
    getRouter: () => ({
        goTo: goToMock,
    }),
    // eslint-disable-next-line func-names, object-shorthand
    Router: function () {},
}));

import { NEW_PAGE, QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER } from '../../../utils/constants';
import { getCallToActionsFromWidgetDefinition } from '../generic-widget-utils';

describe('generic widget utils', () => {
    beforeEach(() => {
        goToMock.mockReset();
    });

    describe('getCallToActionsFromWidgetDefinition', () => {
        it('should return an empty object if no actions defined', () => {
            const ctas = getCallToActionsFromWidgetDefinition({});
            expect(ctas).toEqual({});
        });
        it('should return an empty object if actions defined but disabled', () => {
            const ctas = getCallToActionsFromWidgetDefinition({
                createAction: { isEnabled: false },
                seeAllAction: { isEnabled: false },
            });
            expect(ctas).toEqual({});
        });

        it('should create CTA for creating new items', () => {
            const ctas = getCallToActionsFromWidgetDefinition({
                createAction: { isEnabled: true, title: 'CREATE', page: '@sage/xtrem-test/MyPage' },
            });

            expect(ctas).toEqual({ createAction: { title: 'CREATE', onClick: expect.any(Function) } });
        });

        it('should navigate to the creation mode of the nominated page if the create action is triggered', () => {
            const ctas = getCallToActionsFromWidgetDefinition({
                createAction: { isEnabled: true, title: 'CREATE', page: '@sage/xtrem-test/MyPage' },
            });

            expect(goToMock).not.toHaveBeenCalled();
            ctas.createAction?.onClick.apply({});
            expect(goToMock).toHaveBeenCalledWith('@sage/xtrem-test/MyPage', { _id: NEW_PAGE });
        });

        it('should navigate to the see all link when no filter is defined', () => {
            const ctas = getCallToActionsFromWidgetDefinition({
                seeAllAction: { isEnabled: true, title: 'CREATE', page: '@sage/xtrem-test/MyPage' },
            });

            expect(goToMock).not.toHaveBeenCalled();
            ctas.seeAllAction?.onClick.apply({});
            expect(goToMock).toHaveBeenCalledWith('@sage/xtrem-test/MyPage', {});
        });

        it('should navigate to the see all link with a serialized filter query parameter if a filter is provided', () => {
            const ctas = getCallToActionsFromWidgetDefinition(
                {
                    seeAllAction: { isEnabled: true, title: 'CREATE', page: '@sage/xtrem-test/MyPage' },
                },
                { _product: { _eq: 'test' } },
            );

            expect(goToMock).not.toHaveBeenCalled();
            ctas.seeAllAction?.onClick.apply({});
            expect(goToMock).toHaveBeenCalledWith('@sage/xtrem-test/MyPage', {
                [QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: '{"_product":{"_eq":"test"}}',
            });
        });
    });
});
