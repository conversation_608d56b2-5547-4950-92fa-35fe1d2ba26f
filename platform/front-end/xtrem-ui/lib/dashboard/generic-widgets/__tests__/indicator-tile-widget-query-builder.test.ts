import { GraphQLTypes, type FilterProperty } from '@sage/xtrem-shared';
import type { AggregationProperty, TableProperty, UserWidgetDefinition } from '../../../redux/state';
import { IndicatorTileWidgetQueryBuilder } from '../indicator-tile-widget-query-builder';
import { GraphQLKind } from '../../../types';

export class IndicatorTileQueryBuilderTester extends IndicatorTileWidgetQueryBuilder {
    public filters = {};

    public orderBy = {};

    public groupByPath?: string;

    public nodePath: string;

    public query: any;

    public userWidgetDefinition: UserWidgetDefinition;

    public buildPath() {
        return super.buildPath();
    }

    public buildQuery() {
        return super.buildQuery();
    }

    public buildGroupByQuery() {
        return super.buildGroupBySingleValueQuery();
    }

    public buildOrderBy() {
        return super.buildOrderBy();
    }

    public buildFilter() {
        return super.buildFilter();
    }
}

describe('indicator tile widget query builder', () => {
    let uwd: UserWidgetDefinition;
    beforeEach(() => {
        uwd = {
            title: 'eionfo',
            category: 'DEMO_CATEGORY',
            node: '@sage/xtrem-show-case/ShowCaseProvider',
            filters: [
                {
                    label: 'Text Field',
                    filterType: 'contains',
                    filterValue: 'a',
                    data: {
                        type: GraphQLTypes.String,
                    },
                    id: 'textField',
                    labelPath: 'textField',
                } as FilterProperty,
                {
                    label: 'Text Field',
                    filterType: 'contains',
                    filterValue: 'o',
                    data: {
                        type: GraphQLTypes.String,
                    },
                    id: 'textField',
                    labelPath: 'textField',
                } as FilterProperty,
                {
                    label: 'Name',
                    filterType: 'contains',
                    filterValue: 's',
                    data: {
                        type: GraphQLTypes.String,
                    },
                    id: 'item.name',
                    labelPath: 'item.name',
                } as FilterProperty,
            ],
            groupBy: {
                method: 'distinctCount' as any,
                property: {
                    label: 'Name',
                    data: {
                        type: GraphQLTypes.String,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'name',
                        label: 'Name',
                    } as any,
                    id: 'item.name',
                    labelPath: 'item.name',
                } as AggregationProperty,
            },
            selectedProperties: {
                textField: {
                    label: 'Text Field',
                    data: {
                        type: GraphQLTypes.String,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'textField',
                        label: 'Text Field',
                    } as any,
                    id: 'textField',
                    labelPath: 'textField',
                } as TableProperty,
                integerField: {
                    label: 'Integer Field',
                    data: {
                        type: GraphQLTypes.Int,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'integerField',
                        label: 'Integer Field',
                    } as any,
                    id: 'integerField',
                    labelPath: 'integerField',
                } as TableProperty,
                decimalField: {
                    label: 'Decimal Field',
                    data: {
                        type: GraphQLTypes.Decimal,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'decimalField',
                        label: 'Decimal Field',
                    } as any,
                    id: 'decimalField',
                    labelPath: 'decimalField',
                } as TableProperty,
                booleanField: {
                    label: 'Boolean Field',
                    data: {
                        type: GraphQLTypes.Boolean,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'booleanField',
                        label: 'Boolean Field',
                    } as any,
                    id: 'booleanField',
                    labelPath: 'booleanField',
                } as TableProperty,
                dateField: {
                    label: 'Date Field',
                    data: {
                        type: GraphQLTypes.Date,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'dateField',
                        label: 'Date Field',
                    } as any,
                    id: 'dateField',
                    labelPath: 'dateField',
                } as TableProperty,
                'item.name': {
                    label: 'Name',
                    data: {
                        type: GraphQLTypes.String,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'name',
                        label: 'Name',
                    } as any,
                    id: 'item.name',
                    labelPath: 'item.name',
                } as TableProperty,
            },
            type: 'INDICATOR_TILE',
        };
    });

    describe('filters', () => {
        it('should be able to set an empty filter', () => {
            uwd.filters = [];
            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            expect(queryBuilder.buildFilter().filters).toEqual({});
        });

        it('should be able to combine filters for different properties', () => {
            uwd.filters?.push({
                label: 'Integer Field',
                filterType: 'greaterThan',
                filterValue: '12',
                data: {
                    type: GraphQLTypes.Int,
                },
                id: 'integerField',
                labelPath: 'integerField',
            } as FilterProperty);
            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            expect(queryBuilder.buildFilter().filters).toEqual({
                integerField: {
                    _and: [
                        {
                            _gt: '12',
                        },
                    ],
                },
                item: {
                    name: {
                        _and: [
                            {
                                _options: 'i',
                                _regex: 's',
                            },
                        ],
                    },
                },
                textField: {
                    _and: [
                        {
                            _options: 'i',
                            _regex: 'a',
                        },
                        {
                            _options: 'i',
                            _regex: 'o',
                        },
                    ],
                },
            });
        });

        it('should be able to combine filters for the same property', () => {
            uwd.filters?.pop();
            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            expect(queryBuilder.buildFilter().filters).toEqual({
                textField: {
                    _and: [
                        {
                            _options: 'i',
                            _regex: 'a',
                        },
                        {
                            _options: 'i',
                            _regex: 'o',
                        },
                    ],
                },
            });
        });
    });

    describe('group by', () => {
        it('should be able to perform group by query', () => {
            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            expect(queryBuilder.buildGroupByQuery().query).toEqual({
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            item__name: {
                                __aliasFor: 'item',
                                name: {
                                    distinctCount: true,
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should be able to perform group by query with custom data fields', () => {
            uwd.groupBy = {
                method: 'distinctCount' as any,
                property: {
                    label: 'Name',
                    data: {
                        type: GraphQLTypes.String,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'name',
                        label: 'Name',
                    } as any,
                    id: '_customData.myCustomField',
                    labelPath: '_customData.myCustomField',
                } as AggregationProperty,
            };
            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            expect(queryBuilder.buildGroupByQuery().query).toEqual({
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            _customData__myCustomField: {
                                __aliasFor: '_customData',
                                __args: {
                                    selector: 'myCustomField',
                                },
                                distinctCount: true,
                            },
                        },
                    },
                },
            });
        });

        it('should be able to perform group by query with filters', () => {
            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            expect(queryBuilder.build()).toEqual({
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            __args: {
                                filter: '{"textField":{"_and":[{"_regex":"a","_options":"i"},{"_regex":"o","_options":"i"}]},"item":{"name":{"_and":[{"_regex":"s","_options":"i"}]}}}',
                            },
                            item__name: {
                                __aliasFor: 'item',
                                name: {
                                    distinctCount: true,
                                },
                            },
                        },
                    },
                },
            });
        });
    });

    describe('get value from data', () => {
        it('should be able to get content from data', () => {
            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            const data = {
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            item__name: {
                                name: {
                                    distinctCount: 1,
                                },
                            },
                        },
                    },
                },
            };
            expect(queryBuilder.buildGroupByQuery().getValue(data)).toEqual('1');
        });

        it('should be able to get content from data for a custom field', () => {
            uwd.groupBy = {
                method: 'distinctCount' as any,
                property: {
                    label: 'Name',
                    data: {
                        type: GraphQLTypes.String,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'name',
                        label: 'Name',
                    } as any,
                    id: '_customData.myCustomField',
                    labelPath: '_customData.myCustomField',
                } as AggregationProperty,
            };

            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            const data = {
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            _customData__myCustomField: {
                                distinctCount: 11,
                            },
                        },
                    },
                },
            };
            expect(queryBuilder.buildGroupByQuery().getValue(data)).toEqual('11');
        });

        it('should be able to get content from date field with max', () => {
            uwd.groupBy = {
                method: 'max' as any,
                property: {
                    label: 'Start Date',
                    data: {
                        type: GraphQLTypes.Date,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'startDate',
                        label: 'Start Date',
                    } as any,
                    id: 'startDate',
                    labelPath: 'startDate',
                } as AggregationProperty,
            };

            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            const data = {
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            startDate: { max: '2024-01-30' },
                        },
                    },
                },
            };
            expect(queryBuilder.buildGroupByQuery().getValue(data)).toEqual('30/01/2024');
        });

        it('should be able to get content from date field with min', () => {
            uwd.groupBy = {
                method: 'min' as any,
                property: {
                    label: 'Start Date',
                    data: {
                        type: GraphQLTypes.Date,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'startDate',
                        label: 'Start Date',
                    } as any,
                    id: 'startDate',
                    labelPath: 'startDate',
                } as AggregationProperty,
            };

            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            const data = {
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            startDate: { min: '2024-01-01' },
                        },
                    },
                },
            };
            expect(queryBuilder.buildGroupByQuery().getValue(data)).toEqual('01/01/2024');
        });

        it('should be able to get content from date field with distinct count', () => {
            uwd.groupBy = {
                method: 'distinctCount' as any,
                property: {
                    label: 'Start Date',
                    data: {
                        type: GraphQLTypes.Date,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'startDate',
                        label: 'Start Date',
                    } as any,
                    id: 'startDate',
                    labelPath: 'startDate',
                } as AggregationProperty,
            };

            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            const data = {
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            startDate: { distinctCount: 6 },
                        },
                    },
                },
            };
            expect(queryBuilder.buildGroupByQuery().getValue(data)).toEqual('6');
        });

        it('should be able to get content from float field using local formatting', () => {
            uwd.groupBy = {
                method: 'sum' as any,
                property: {
                    label: 'sales',
                    data: {
                        type: GraphQLTypes.Float,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'sales',
                        label: 'Start Date',
                    } as any,
                    id: 'sales',
                    labelPath: 'sales',
                } as AggregationProperty,
            };

            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            const data = {
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            sales: { sum: 123456.78 },
                        },
                    },
                },
            };
            expect(queryBuilder.buildGroupByQuery().getValue(data)).toEqual('123,456.78');
        });

        it('should be able to get content from decimal field using local formatting', () => {
            uwd.groupBy = {
                method: 'sum' as any,
                property: {
                    label: 'sales',
                    data: {
                        type: GraphQLTypes.Decimal,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'sales',
                        label: 'Start Date',
                    } as any,
                    id: 'sales',
                    labelPath: 'sales',
                } as AggregationProperty,
            };

            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            const data = {
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            sales: { sum: 123456.78 },
                        },
                    },
                },
            };
            expect(queryBuilder.buildGroupByQuery().getValue(data)).toEqual('123,456.78');
        });

        it('should be able to get content from integer field using local formatting', () => {
            uwd.groupBy = {
                method: 'sum' as any,
                property: {
                    label: 'sales',
                    data: {
                        type: GraphQLTypes.Int,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'sales',
                        label: 'Start Date',
                    } as any,
                    id: 'sales',
                    labelPath: 'sales',
                } as AggregationProperty,
            };

            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            const data = {
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            sales: { sum: 123456 },
                        },
                    },
                },
            };
            expect(queryBuilder.buildGroupByQuery().getValue(data)).toEqual('123,456');
        });

        it('should be able to get content from float field using local formatting with a divisor', () => {
            uwd.groupBy = {
                divisor: 10,
                method: 'sum' as any,
                property: {
                    label: 'sales',
                    data: {
                        type: GraphQLTypes.Float,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'sales',
                        label: 'Start Date',
                    } as any,
                    id: 'sales',
                    labelPath: 'sales',
                } as AggregationProperty,
            };

            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            const data = {
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            sales: { sum: 123456.78 },
                        },
                    },
                },
            };
            expect(queryBuilder.buildGroupByQuery().getValue(data)).toEqual('12,345.678');
        });

        it('should be able to get content from decimal field using local formatting with a divisor', () => {
            uwd.groupBy = {
                divisor: 10,
                method: 'sum' as any,
                property: {
                    label: 'sales',
                    data: {
                        type: GraphQLTypes.Decimal,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'sales',
                        label: 'Start Date',
                    } as any,
                    id: 'sales',
                    labelPath: 'sales',
                } as AggregationProperty,
            };

            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            const data = {
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            sales: { sum: 123456.78 },
                        },
                    },
                },
            };
            expect(queryBuilder.buildGroupByQuery().getValue(data)).toEqual('12,345.678');
        });

        it('should be able to get content from integer field using local formatting with a divisor', () => {
            uwd.groupBy = {
                divisor: 10,
                method: 'sum' as any,
                property: {
                    label: 'sales',
                    data: {
                        type: GraphQLTypes.Int,
                        kind: GraphQLKind.Scalar,
                        isCollection: false,
                        name: 'sales',
                        label: 'Start Date',
                    } as any,
                    id: 'sales',
                    labelPath: 'sales',
                } as AggregationProperty,
            };

            const queryBuilder = new IndicatorTileQueryBuilderTester(uwd);
            const data = {
                xtremShowCase: {
                    showCaseProvider: {
                        readAggregate: {
                            sales: { sum: 123456 },
                        },
                    },
                },
            };
            expect(queryBuilder.buildGroupByQuery().getValue(data)).toEqual('12,345.6');
        });
    });
});
