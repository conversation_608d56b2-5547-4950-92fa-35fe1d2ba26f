import type { Dict } from '@sage/xtrem-shared';
import { isEmpty, isNil } from 'lodash';
import type { UserWidgetDefinition } from '../../redux/state';
import { localize } from '../../service/i18n-service';
import { showToast } from '../../service/toast-service';
import { getRouter } from '../../service/router';
import type { AbstractWidget, CallToActionDefinition } from '../widgets/abstract-widget';
import { NEW_PAGE, DASHBOARD_SCREEN_ID, QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER } from '../../utils/constants';

const displayInactiveFeatureToast = (): void =>
    showToast(
        localize(
            '@sage/xtrem-ui/widget-editor-layout-step-no-actions-in-preview',
            'Actions do not work in the preview. They work when you add the widget to your dashboard.',
        ),
        { type: 'info' },
    );

export const getCallToActionsFromWidgetDefinition = (
    userWidgetDefinition: UserWidgetDefinition,
    filter?: any,
    isPreview = false,
): Dict<CallToActionDefinition<AbstractWidget>> => {
    const callToActions: Dict<CallToActionDefinition<AbstractWidget>> = {};
    if (userWidgetDefinition.createAction?.title && userWidgetDefinition.createAction?.isEnabled) {
        callToActions.createAction = {
            title: userWidgetDefinition.createAction.title,
            onClick: (): void => {
                if (isPreview) {
                    displayInactiveFeatureToast();
                    return;
                }
                if (userWidgetDefinition.createAction?.page) {
                    getRouter(DASHBOARD_SCREEN_ID).goTo(userWidgetDefinition.createAction.page, { _id: NEW_PAGE });
                }
            },
        };
    }

    if (userWidgetDefinition.seeAllAction?.title && userWidgetDefinition.seeAllAction?.isEnabled) {
        callToActions.seeAllAction = {
            title: userWidgetDefinition.seeAllAction.title,
            onClick: (): void => {
                if (isPreview) {
                    displayInactiveFeatureToast();
                    return;
                }
                if (userWidgetDefinition.seeAllAction?.page) {
                    const params: Dict<string> =
                        !isNil(filter) && !isEmpty(filter)
                            ? { [QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify(filter) }
                            : {};

                    getRouter(DASHBOARD_SCREEN_ID).goTo(userWidgetDefinition.seeAllAction.page, params);
                }
            },
        };
    }

    return callToActions;
};
