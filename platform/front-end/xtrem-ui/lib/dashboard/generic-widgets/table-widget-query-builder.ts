import type { RenderAsType } from '@sage/bms-dashboard';
import type { Dict, presentationGraphqlMapping } from '@sage/xtrem-shared';
import { get, isNil } from 'lodash';
import { AbstractWidgetQueryBuilder } from '.';
import type { TableProperty, UserWidgetDefinition } from '../../redux/state';
import { calculateDeepPaths } from '../../service/customization-service';
import type { LoadMoreRowsParams } from '../../service/dashboard-service';
import { formatDateToCurrentLocale, localize, localizeEnumMember } from '../../service/i18n-service';
import type { RecordValue } from '../../utils/types';
import type { RowDefinition, TableWidgetRow } from '../widgets/table-widget-decorator';

const presentationMapping: Record<RecordValue<typeof presentationGraphqlMapping>[number], RenderAsType> = {
    Checkbox: 'normal',
    Date: 'normal',
    Label: 'pill',
    Numeric: 'normal',
    Progress: 'normal',
    Text: 'normal',
};
export class TableWidgetQueryBuilder extends AbstractWidgetQueryBuilder {
    private columns: string[];

    public rowDefinition: RowDefinition<any>;

    constructor(userWidgetDefinition: UserWidgetDefinition) {
        super(userWidgetDefinition);
        this.columns = [
            'title',
            'titleRight',
            'line2',
            'line2Right',
            'line3',
            'line3Right',
            'line4',
            'line4Right',
            'line5',
            'line5Right',
        ];
        // We need a unique object key for the dashboard library for each column
        for (let i = 4; i < 100; i += 1) {
            this.columns.push(`line${i}`);
        }
        this.rowDefinition = this.columns.reduce<RowDefinition<any>>((acc, key, index) => {
            const column = this.userWidgetDefinition.columns?.[index];
            if (column) {
                acc[key] = {
                    title: column.title,
                    renderedAs: get(presentationMapping, column.presentation),
                    displayOptions: {},
                    valueFormat: column.presentation === 'Numeric' ? 'number' : 'string',
                    decimalDigits:
                        column.presentation === 'Numeric' && !isNil(column.formatting) ? column.formatting : 2,
                };
            }
            return acc;
        }, {} as RowDefinition<any>);
    }

    public build(queryArgs?: LoadMoreRowsParams): any {
        return this.buildQuery(queryArgs).buildFilter().buildOrderBy().query;
    }

    public getValue(data: any): any {
        const edges = get(data, `${this.nodePath}.query.edges`);
        if (!edges) {
            return [];
        }

        return edges.map(({ node, cursor }: any) => {
            return this.columns.reduce<TableWidgetRow>(
                (acc, k, index) => {
                    const column = this.userWidgetDefinition.columns?.[index];
                    if (column) {
                        const columnId = column.id ?? column.path;
                        const { subpath, selectorSegments } = calculateDeepPaths(columnId);

                        let value = get(node, subpath.join('.'), '');
                        const isJsonSubfield = selectorSegments.length > 0;
                        if (isJsonSubfield) {
                            value = get(JSON.parse(value || '{}'), selectorSegments.join('.'), '');
                        }
                        acc[k] = this.formatValue({ value, column, isJsonSubfield });
                    }
                    return acc;
                },
                { _id: cursor } as TableWidgetRow,
            );
        });
    }

    protected formatValue({
        column: {
            data: { type, enumValues, node },
            formatting,
            divisor,
        },
        value,
        isJsonSubfield,
    }: {
        column: TableProperty;
        value: any;
        isJsonSubfield?: boolean;
    }): string {
        if (!type) {
            return String(value);
        }
        switch (type) {
            case 'Boolean':
                return value === true
                    ? localize('@sage/xtrem-ui/true', 'True')
                    : localize('@sage/xtrem-ui/false', 'False');
            case 'Date':
            case 'DateTime':
                return value ? formatDateToCurrentLocale(value) : '';
            case 'Decimal':
            case 'Float':
            case 'Int':
            case 'IntReference':
                if (value === '') {
                    return '';
                }
                const num = Number(value);
                if (Number.isNaN(num)) {
                    return '0';
                }
                return (num / (divisor ?? 1)).toFixed(formatting);
            case 'Enum':
                const localizedOptions =
                    this.userWidgetDefinition.node && enumValues && node
                        ? enumValues.reduce((enumValueDict: Dict<string>, key: string) => {
                              const enumType =
                                  node.indexOf('/') === -1
                                      ? `${this.userWidgetDefinition.node?.split('/').slice(0, -1).join('/')}/${node}`
                                      : node;
                              enumValueDict[key] = localizeEnumMember(enumType, key);
                              return enumValueDict;
                          }, {} as Dict<string>)
                        : undefined;
                return localizedOptions?.[value] ?? String(value);
            case 'ExternalReference':
            case 'ID':
            case 'InputStream':
            case 'IntOrString':
            case 'Json':
            case 'String':
            default:
                break;
        }
        return value === null && isJsonSubfield ? '' : String(value);
    }
}
