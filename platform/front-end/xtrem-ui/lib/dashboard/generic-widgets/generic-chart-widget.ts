import type { UserWidgetDefinition } from '../../redux/state';
import type { DashboardContextVariables } from '../dashboard-types';
import { AbstractWidget } from '../widgets/abstract-widget';
import { barChart } from '../widgets/bar-chart-widget-decorator';
import { lineChart } from '../widgets/line-chart-widget-decorator';
import { ChartWidgetQueryBuilder } from './chart-widget-query-builder';
import { getCallToActionsFromWidgetDefinition } from './generic-widget-utils';

export const createGenericChartWidgetDefinition = (
    userWidgetDefinition: UserWidgetDefinition,
    dashboardId: string,
    widgetId: string,
    group: string,
    contextVariables?: DashboardContextVariables,
    isPreview = false,
): AbstractWidget => {
    const queryBuilder = new ChartWidgetQueryBuilder(userWidgetDefinition);
    const callToActions = getCallToActionsFromWidgetDefinition(
        userWidgetDefinition,
        queryBuilder.getFilterObject(),
        isPreview,
    );
    if (!userWidgetDefinition.xAxis) {
        throw new Error('Cannot create chart without an X axis.');
    }
    if (!userWidgetDefinition.aggregations || userWidgetDefinition.aggregations.length === 0) {
        throw new Error('Cannot create chart without a series.');
    }
    const chartAgrs: Parameters<typeof barChart>[0] = {
        primaryAxis: {
            bind: userWidgetDefinition.xAxis.property.id,
            title: userWidgetDefinition.xAxis.property.label,
        },
        primaryAxisLabel: userWidgetDefinition.horizontalAxisLabel,
        secondaryAxisLabel: userWidgetDefinition.verticalAxisLabel,
        secondaryAxes: userWidgetDefinition.aggregations.map(a => {
            return {
                bind: `${a.id}.${a.groupingMethod}`,
                title: a.title,
                color: a.color,
                tooltipContent(_, __, value): string {
                    return String(value);
                },
            };
        }),
        title() {
            return userWidgetDefinition.title || '';
        },
        callToActions,
        content() {
            return queryBuilder.getValue(this.$.data);
        },
        getQuery(queryArgs) {
            return queryBuilder.build(queryArgs);
        },
    };
    class GenericChartWidget extends AbstractWidget {}
    if (userWidgetDefinition.type === 'BAR_CHART') {
        barChart(chartAgrs)(GenericChartWidget);
    } else if (userWidgetDefinition.type === 'LINE_CHART') {
        lineChart(chartAgrs)(GenericChartWidget);
    }

    (GenericChartWidget.prototype as any).__id = widgetId;
    (GenericChartWidget.prototype as any).__dashboardId = dashboardId;
    (GenericChartWidget.prototype as any).__group = group;
    (GenericChartWidget.prototype as any).__contextVariables = contextVariables;

    return new GenericChartWidget();
};
