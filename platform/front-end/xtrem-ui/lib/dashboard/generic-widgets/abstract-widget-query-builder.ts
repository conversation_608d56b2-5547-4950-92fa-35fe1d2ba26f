import { getFilterObject } from '@sage/xtrem-filter-utils';
import type { Aggregations, LocalizeLocale } from '@sage/xtrem-shared';
import { EnumType } from 'json-to-graphql-query';
import { camelCase, chain, isEmpty, isNil, merge, set } from 'lodash';
import { getStore } from '../../redux';
import type { AggregationProperty, UserWidgetDefinition } from '../../redux/state';
import { calculateDeepPaths } from '../../service/customization-service';
import type { LoadMoreRowsParams } from '../../service/dashboard-service';

export class AbstractWidgetQueryBuilder {
    protected filters = {};

    protected groupByPath?: string;

    protected nodePath: string;

    protected orderBy = {};

    protected query: any;

    protected queryType: 'readAggregate' | 'queryAggregate' | 'query';

    protected userWidgetDefinition: UserWidgetDefinition;

    constructor(userWidgetDefinition: UserWidgetDefinition) {
        this.userWidgetDefinition = userWidgetDefinition;
        this.buildPath();
    }

    public getFilterObject(): any {
        const state = getStore().getState();
        const locale = state.applicationContext?.locale as unknown as LocalizeLocale;
        return getFilterObject({ filters: this.userWidgetDefinition.filters ?? [], locale });
    }

    protected buildPath(): this {
        if (!this.userWidgetDefinition.node) {
            throw new Error("Cannot find widget definition's node");
        }

        const [pkg, node] = this.userWidgetDefinition.node.split('/').slice(-2);
        if (!pkg || !node) {
            throw new Error(`Invalid widget definition's node: ${this.userWidgetDefinition.node}`);
        }
        this.nodePath = `${camelCase(pkg)}.${camelCase(node)}`;

        return this;
    }

    protected buildQuery(queryArgs?: LoadMoreRowsParams): this {
        this.queryType = 'query';
        const columns = this.userWidgetDefinition.columns;
        if (!columns || columns.length === 0) {
            throw new Error("Cannot find widget's column definitions");
        }
        this.query = chain({})
            .set(`${this.nodePath}.${this.queryType}.edges`, { cursor: true })
            .thru(q =>
                queryArgs
                    ? set(q, `${this.nodePath}.${this.queryType}.__args`, {
                          after: queryArgs.after?._id,
                          first: queryArgs.first,
                      })
                    : q,
            )
            .set(
                `${this.nodePath}.${this.queryType}.edges.node`,
                columns.reduce(
                    (acc, column) => {
                        const columnId = String(column.id || column.path);
                        const { subpath, alias, mainSegments } = calculateDeepPaths(columnId);
                        set(acc, subpath, true);
                        set(acc, `${alias}.__aliasFor`, mainSegments[0]);
                        return acc;
                    },
                    { _id: true },
                ),
            )
            .value();
        return this;
    }

    protected buildGroupBySingleValueQuery(): this {
        this.queryType = 'readAggregate';
        const aggregatePath = `${this.nodePath}.${this.queryType}`;
        const groupBy = this.userWidgetDefinition.groupBy;
        if (!groupBy) {
            throw new Error("Cannot find widget's group by property");
        }
        const groupByProperty = groupBy.property.id || groupBy.property.path;
        if (!groupByProperty) {
            throw new Error("Cannot find widget's group by property name");
        }

        const { subpath } = calculateDeepPaths(groupByProperty);
        this.groupByPath = `${aggregatePath}.${subpath.join('.')}.${groupBy.method}`;
        this.query = set({}, aggregatePath, this.createAggregationFragment(groupByProperty, groupBy.method));

        return this;
    }

    protected buildGroupByMultipleValuesQuery({
        group,
        aggregations,
        first = this.userWidgetDefinition.aggregationsCount ?? 20,
    }: {
        group: NonNullable<UserWidgetDefinition['xAxis']>;
        aggregations: AggregationProperty[];
        first?: number;
    }): this {
        this.queryType = 'queryAggregate';
        const queryPath = `${this.nodePath}.${this.queryType}.edges.node`;
        const propertyPath = String(group.property.id || group.property.path);

        // set limit
        this.query = set({}, `${this.nodePath}.${this.queryType}.__args`, { first });

        // set group
        this.query = set(this.query, `${queryPath}.group`, this.createAggregationFragment(propertyPath));

        if (group.groupBy) {
            const { subpath } = calculateDeepPaths(propertyPath);
            set(this.query, `${queryPath}.group.${subpath.join('.')}.__args.by`, new EnumType(group.groupBy));
        }

        // set values
        this.query = set(
            this.query,
            `${queryPath}.values`,
            aggregations.reduce((acc, aggregation) => {
                const aggregationProperty = String(aggregation.id || aggregation.path);

                return merge(acc, this.createAggregationFragment(aggregationProperty, aggregation.groupingMethod));
            }, {}),
        );

        return this;
    }

    protected createAggregationFragment(aggregationProperty: string, aggregationMethod?: Aggregations): any {
        const acc: any = {};
        const { alias, mainSegments, selectorSegments, subpath } = calculateDeepPaths(aggregationProperty);

        set(acc, `${alias}.__aliasFor`, mainSegments[0]);

        if (selectorSegments.length > 0) {
            set(acc, `${subpath.join('.')}.__args.selector`, selectorSegments.join('.'));
        }

        if (aggregationMethod) {
            set(acc, `${subpath.join('.')}.${aggregationMethod}`, true);
        } else if (subpath.length > 1 && selectorSegments.length === 0) {
            set(acc, subpath.join('.'), true);
        }

        return acc;
    }

    protected buildOrderBy(): this {
        const sortConditions = this.userWidgetDefinition.orderBy?.reduce((acc, o) => {
            return set(acc, String(o.id || o.path), o.order === 'ascending' ? 1 : -1);
        }, {});

        if (sortConditions != null && !isEmpty(sortConditions)) {
            this.orderBy = sortConditions;
            set(this.query, `${this.nodePath}.${this.queryType}.__args.orderBy`, JSON.stringify(sortConditions));
        }

        return this;
    }

    protected buildFilter(): this {
        const filters = this.getFilterObject();

        if (!isNil(filters) && !isEmpty(filters)) {
            this.filters = filters;
            set(this.query, `${this.nodePath}.${this.queryType}.__args.filter`, JSON.stringify(filters));
        }

        return this;
    }

    public build(): any {
        throw new Error('Must implement this method');
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    public getValue(_data: any): any {
        throw new Error('Must implement this method');
    }
}
