import * as React from 'react';
import { connect } from 'react-redux';
import { XtremTabs } from '../component/ui/tabs/xtrem-tabs';
import type { DashboardGroupState, DashboardListItem } from '../redux/state';
import * as xtremRedux from '../redux';
import { localize } from '../service/i18n-service';
import type { DashboardContextVariables } from './dashboard-types';

export interface DashboardSelectionTabsExternalProps {
    canChangeTabs?: () => Promise<void>;
    onCreateDialogOpen?: () => void;
    // eslint-disable-next-line react/no-unused-prop-types
    contextVariables?: DashboardContextVariables;
    // eslint-disable-next-line react/no-unused-prop-types
    group: string;
}
export interface DashboardSelectionTabsProps extends DashboardSelectionTabsExternalProps {
    availableDashboards: DashboardListItem[];
    selectedDashboardId?: string;
    setSelectedDashboard: (dashboardId: string) => void;
}

const CREATE_ACTION_TAB_ID = '$create';

export function DashboardSelectionTabs({
    availableDashboards,
    onCreateDialogOpen,
    selectedDashboardId,
    setSelectedDashboard,
    canChangeTabs,
}: DashboardSelectionTabsProps): React.ReactElement | null {
    const [selectedTabId, setSelectedTabId] = React.useState<string | null>(selectedDashboardId || null);

    React.useEffect(() => {
        setSelectedTabId(selectedDashboardId || null);
    }, [selectedDashboardId]);

    if (!availableDashboards?.length) {
        return null;
    }

    const onTabsChange = async (tabId: string): Promise<void> => {
        if (onCreateDialogOpen && tabId === CREATE_ACTION_TAB_ID) {
            onCreateDialogOpen();
            return;
        }
        if (canChangeTabs) {
            try {
                await canChangeTabs();
            } catch (_err) {
                // We return here if the promise is rejected
                return;
            }
        }
        setSelectedTabId(tabId);
        setSelectedDashboard(tabId);
    };

    return (
        <div className="e-dashboard-selection-tab-container" data-testid="e-dashboard-selection-tab-container">
            <XtremTabs
                onTabChange={onTabsChange}
                selectedTabId={selectedTabId || availableDashboards[0]._id}
                tabs={(availableDashboards || []).map(d => ({
                    id: d._id,
                    title: d.title,
                }))}
            />
        </div>
    );
}

export const ConnectedDashboardSelectionTabs = connect(
    (state: xtremRedux.XtremAppState, props: DashboardSelectionTabsExternalProps): DashboardSelectionTabsProps => {
        const dashboardGroup: DashboardGroupState = state.dashboard.dashboardGroups[props.group];
        if (!dashboardGroup) {
            return {
                ...props,
                availableDashboards: [],
                setSelectedDashboard: xtremRedux.actions.actionStub,
            };
        }

        const selectedDashboardId = Object.values(dashboardGroup.dashboards).find(s => s.isSelected)?._id;
        const availableDashboards =
            state.dashboard.canEditDashboards &&
            props.onCreateDialogOpen &&
            dashboardGroup.availableDashboards.length > 0
                ? [
                      ...dashboardGroup.availableDashboards,
                      {
                          _id: CREATE_ACTION_TAB_ID,
                          title: localize('@sage/xtrem-ui/create-new-dashboard', '+ Create'),
                      },
                  ]
                : dashboardGroup.availableDashboards;

        return {
            ...props,
            availableDashboards,
            selectedDashboardId: selectedDashboardId ? String(selectedDashboardId) : undefined,
            setSelectedDashboard: xtremRedux.actions.actionStub,
        };
    },
    (
        dispatch: xtremRedux.AppThunkDispatch,
        props: DashboardSelectionTabsExternalProps,
    ): Partial<DashboardSelectionTabsProps> => {
        return {
            setSelectedDashboard: (dashboardId: string): void => {
                dispatch(xtremRedux.actions.setSelectedDashboard(dashboardId, props.group, props.contextVariables));
            },
        };
    },
)(DashboardSelectionTabs);
