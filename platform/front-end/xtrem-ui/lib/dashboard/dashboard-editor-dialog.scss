.e-dashboard-editor {
    display: flex;
    height: calc(100vh - 151px);
    width: calc(100vw - 80px);

    .e-page-navigation-panel {
        display: flex;
        margin-left: 0;
    }

    .db-widget-skeleton .db-widget-type-preview {
        overflow: hidden;
    }

    .e-dashboard-editor-sidebar {
        background-color: var(--colorsYang100);
        width: 200px;
    }

    .e-dashboard-editor-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-width: 0; // https://stackoverflow.com/questions/36230944/prevent-flex-items-from-overflowing-a-container
    }

    .e-dashboard-editor-tab-container {
        margin-top: 24px;
        margin-left: 24px;
    }

    .e-dashboard-editor-body {
        flex: 1;
        position: relative;
        overflow-y: auto;
    }

    .widgetSkeleton {
        position: relative;
    }

    .react-resizable-handle {
        position: absolute;
        width: 20px;
        height: 20px;
        bottom: 0;
        right: 0;
    }

    .e-dashboard-editor-history {
        margin-top: 20px;
        margin-bottom: 8px;
    }

    .e-dashboard-editor-title {
        margin-right: 12px;
    }

    .e-dashboard-editor-title-line {
        display: flex;

        >h1 {
            display: flex;
            flex: 1;
            height: 32px;

            >div {
                flex: 1;
            }
        }
    }

    .e-navigation-panel-toggle {
        position: absolute;
        left: -40px;
        top: 0;
    }
}