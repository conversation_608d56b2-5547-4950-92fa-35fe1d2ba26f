import * as React from 'react';
import Preview from 'carbon-react/esm/components/preview';

export interface DashboardPlaceholderProps {
    isTitlePlaceholderHidden?: boolean;
}

function DashboardItemPlaceholder({ style }: { style: React.CSSProperties }): React.ReactElement {
    return (
        <div className="e-dashboard-placeholder-item" style={style}>
            <Preview height="200px" />
        </div>
    );
}

export function DashboardPlaceholder({ isTitlePlaceholderHidden }: DashboardPlaceholderProps): React.ReactElement {
    const placeHolderItemsStyle = [
        { gridColumn: 'span 4 / auto' },
        { gridColumn: 'span 8 / auto' },
        { gridColumn: 'span 2 / auto' },
        { gridColumn: 'span 6 / auto' },
        { gridColumn: 'span 4 / auto' },
        { gridColumn: 'span 6 / auto' },
        { gridColumn: 'span 3 / auto' },
        { gridColumn: 'span 3 / auto' },
    ];
    return (
        <div className="e-dashboard-placeholder" data-testid="e-dashboard-placeholder">
            {!isTitlePlaceholderHidden && (
                <div className="e-dashboard-placeholder-title">
                    <Preview loading width="256px" />
                </div>
            )}
            <div className="e-dashboard-placeholder-body">
                {placeHolderItemsStyle.map((itemStyle, index) => (
                    // eslint-disable-next-line react/no-array-index-key
                    <DashboardItemPlaceholder key={index} style={itemStyle} />
                ))}
            </div>
        </div>
    );
}
