import type { DragDropCanvasProps } from '@sage/bms-dashboard';
import type { Dict } from '@sage/xtrem-shared';
import { breakpoints } from '../integration';
import type { XtremAppState } from '../redux';
import type { WidgetDefinitionWithLoadingState } from './dashboard-types';
import type { WidgetDefinition } from './widgets/abstract-widget';

export const DASHBOARD_COLS: Extract<DragDropCanvasProps, { type: 'responsive' }>['layout']['cols'] = {
    xxs: 2,
    xs: 4,
    sm: 6,
    md: 8,
    lg: 12,
};

export const DASHBOARD_BREAKPOINTS: Extract<DragDropCanvasProps, { type: 'responsive' }>['layout']['breakpoints'] = {
    xxs: 375,
    xs: breakpoints.xs,
    sm: breakpoints.s,
    md: breakpoints.m,
    lg: breakpoints.l,
};

export const DASHBOARD_EDITOR_SIDEBAR_WIDTH = 322;
const DASHBOARD_EDITOR_PADDING = 80;

export const DASHBOARD_EDITOR_WITH_SIDEBAR_BREAKPOINTS: Extract<
    DragDropCanvasProps,
    { type: 'responsive' }
>['layout']['breakpoints'] = {
    xxs: 375 - (DASHBOARD_EDITOR_SIDEBAR_WIDTH + DASHBOARD_EDITOR_PADDING),
    xs: breakpoints.xs - (DASHBOARD_EDITOR_SIDEBAR_WIDTH + DASHBOARD_EDITOR_PADDING),
    sm: breakpoints.s - (DASHBOARD_EDITOR_SIDEBAR_WIDTH + DASHBOARD_EDITOR_PADDING),
    md: breakpoints.m - (DASHBOARD_EDITOR_SIDEBAR_WIDTH + DASHBOARD_EDITOR_PADDING),
    lg: breakpoints.l - (DASHBOARD_EDITOR_SIDEBAR_WIDTH + DASHBOARD_EDITOR_PADDING),
};

export function getWidgetsWithLoadingState({
    widgetState,
    widgets,
}: {
    widgetState: XtremAppState['loading']['widgets'];
    widgets: Dict<WidgetDefinition>;
}): Dict<WidgetDefinitionWithLoadingState> {
    return Object.keys(widgets ?? {}).reduce((acc, curr) => {
        acc[curr] = { ...widgets[curr], isLoading: widgetState[curr]?.isVisibleLoading };
        return acc;
    }, {} as Dict<WidgetDefinitionWithLoadingState>);
}
