import { getMockState, getMockStore } from '../../../__tests__/test-helpers';
import * as graphqlUtils from '../../../service/graphql-utils';
import { AbstractWidget, WidgetCacheLifespan, WidgetDeveloperApi, WidgetType } from '../abstract-widget';
import type { TableWidgetProperties } from '../table-widget-decorator';
import { table } from '../table-widget-decorator';

const testDashboardGroup = 'home';

let executeGraphqlQueryMock: jest.SpyInstance<Promise<any>, any>;

describe('table widget decorator', () => {
    beforeEach(() => {
        const mockState = getMockState();
        mockState.dashboard = {
            dashboardGroups: {
                [testDashboardGroup]: {
                    availableDashboards: [],
                    widgetEditor: {
                        isOpen: false,
                        isDirty: false,
                        widgetDefinition: {},
                    },
                    dashboards: {
                        '12': {
                            _id: '12',
                            title: 'A fake dashboard',
                            isSelected: true,
                            children: [
                                {
                                    _id: '3',
                                    positions: [
                                        { x: 0, y: 0, breakpoint: 'xxs', w: 0, h: 0 },
                                        { x: 0, y: 0, breakpoint: 'xs', w: 0, h: 0 },
                                        { x: 0, y: 0, breakpoint: 'sm', w: 0, h: 0 },
                                        { x: 0, y: 0, breakpoint: 'md', w: 0, h: 0 },
                                        { x: 0, y: 0, breakpoint: 'lg', w: 0, h: 0 },
                                    ],
                                    type: 'table',
                                    settings: { aRandomSetting: 32 },
                                },
                            ],
                        },
                    },
                    widgets: {
                        '3': {
                            _id: '3',
                            artifactName: '@sage/xtrem-test/TestWidget',
                            data: { test: 134 },
                            properties: {
                                title: 'Test widget',
                                getQuery: () => ({ fake: { graphql: { query: true } } }),
                                totalCount: () => 50,
                                content: () => [{ _id: '1', title: 'Product 1' }],
                                rowDefinition: () => ({
                                    title: {
                                        title: 'Title',
                                        onClick: jest.fn(),
                                    },
                                }),
                            } as TableWidgetProperties<any>,
                            widgetType: WidgetType.table,
                            widgetObject: {} as any,
                        },
                    },
                    dashboardEditor: {
                        isOpen: false,
                        currentDashboardDefinition: { _id: '12', title: 'A fake dashboard', children: [] },
                        currentHistoryIndex: 0,
                        history: [],
                        isDirty: false,
                    },
                },
            },
            nodeNames: {},
            widgetCategories: {},
            canEditDashboards: true,
        };
        getMockStore(mockState);
    });

    it('should add the developer API to the widget', () => {
        @table({
            title: 'my title',
            cacheLifespan: WidgetCacheLifespan.hour,
            getQuery() {
                return { fake: { graphql: { query: true } } };
            },
            content() {
                return [{ _id: '1', title: 'Product 1' }];
            },
            rowDefinition() {
                return {
                    title: {
                        title: 'Title',
                        onClick: jest.fn(),
                    },
                };
            },
        })
        class TestWidget extends AbstractWidget {}
        (TestWidget.prototype as any).__id = '3';
        (TestWidget.prototype as any).__dashboardId = '12';
        (TestWidget.prototype as any).__group = testDashboardGroup;

        const widgetInstance = new TestWidget();
        expect(widgetInstance.$).toBeInstanceOf(WidgetDeveloperApi);
        expect(widgetInstance.$.data).toEqual({ test: 134 });
        expect(widgetInstance.$.settings).toEqual({ aRandomSetting: 32 });
    });

    describe('executeMutation', () => {
        let widgetInstance: AbstractWidget;

        beforeEach(() => {
            @table({
                title: 'my title',
                cacheLifespan: WidgetCacheLifespan.hour,
                getQuery() {
                    return { fake: { graphql: { query: true } } };
                },
                content() {
                    return [{ _id: '1', title: 'Product 1' }];
                },
                rowDefinition() {
                    return {
                        title: {
                            title: 'Title',
                            onClick: jest.fn(),
                        },
                    };
                },
            })
            class TestWidget extends AbstractWidget {}
            (TestWidget.prototype as any).__id = '3';
            (TestWidget.prototype as any).__dashboardId = '12';
            (TestWidget.prototype as any).__group = testDashboardGroup;

            widgetInstance = new TestWidget();
            executeGraphqlQueryMock = jest.spyOn(graphqlUtils, 'executeGraphqlQuery');
        });

        afterEach(() => {
            executeGraphqlQueryMock.mockReset();
        });

        it('should execute an exposed mutation successfully', async () => {
            jest.spyOn(widgetInstance.$.graph, 'node').mockImplementation(
                () =>
                    ({
                        mutations: {
                            generateFakeNotification: jest.fn().mockReturnValue({
                                execute: jest.fn().mockResolvedValue({
                                    data: { generateFakeNotification: { success: true } },
                                    errors: null,
                                }),
                            }),
                        },
                    }) as any,
            );
            const result = await widgetInstance.$.graph
                .node('@sage/xtrem-show-case/ShowCaseCountry')
                .mutations.generateFakeNotification(true, {
                    description: '',
                    icon: '',
                    level: '' as any,
                    shouldDisplayToast: true,
                    title: '',
                })
                .execute();
            expect(result).toEqual({ data: { generateFakeNotification: { success: true } }, errors: null });
            expect(widgetInstance.$.graph.node).toHaveBeenCalledWith('@sage/xtrem-show-case/ShowCaseCountry');
        });

        it('should not expose query in the widget', () => {
            const node = widgetInstance.$.graph.node('@sage/xtrem-show-case/ShowCaseCustomer');
            expect(node).toHaveProperty('mutations');
            expect(node).toHaveProperty('create');
            expect(node).toHaveProperty('delete');
            expect(node).toHaveProperty('deleteById');
            expect(node).toHaveProperty('update');
            expect(node).toHaveProperty('updateById');
            expect(node).not.toHaveProperty('query');
            expect(node).not.toHaveProperty('read');
            expect(node).not.toHaveProperty('aggregate');
            expect(node).not.toHaveProperty('getDefaults');
            expect(node).not.toHaveProperty('lookups');
        });
    });
});
