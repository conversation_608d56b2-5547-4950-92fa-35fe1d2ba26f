import { getMockState, getMockStore } from '../../../__tests__/test-helpers';

import { AbstractWidget, WidgetCacheLifespan } from '..';
import { WidgetDeveloperApi, WidgetType } from '../abstract-widget';
import { indicatorTile } from '../indicator-tile-widget-decorator';

const testDashboardGroup = 'home';

describe('indicator tile widget decorator', () => {
    beforeEach(() => {
        const mockState = getMockState();
        mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'] = {
            _id: '12',
            title: 'A fake dashboard',
            isSelected: true,
            children: [
                {
                    _id: '3',
                    positions: [
                        { x: 0, y: 0, breakpoint: 'xxs', w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'xs', w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'sm', w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'md', w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'lg', w: 0, h: 0 },
                    ],
                    type: 'indicator tile',
                    settings: { aRandomSetting: 32 },
                },
            ],
        };

        mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = {
            '3': {
                _id: '3',
                artifactName: '@sage/xtrem-test/TestWidget',
                data: { test: 134 },
                properties: {
                    title: 'Test widget',
                    getQuery: () => ({ fake: { graphql: { query: true } } }),
                },
                widgetType: WidgetType.staticContent,
                widgetObject: {} as any,
            },
        };
        getMockStore(mockState);
    });

    it('should add the developer API to the widget', () => {
        @indicatorTile({
            title: 'my title',
            cacheLifespan: WidgetCacheLifespan.hour,
            getQuery() {
                return { fake: { graphql: { query: true } } };
            },
            color: '#FF00FF',
            onClick: jest.fn(),
            value: '12',
            icon: 'alert',
        })
        class TestWidget extends AbstractWidget {}
        (TestWidget.prototype as any).__id = '3';
        (TestWidget.prototype as any).__dashboardId = '12';
        (TestWidget.prototype as any).__group = testDashboardGroup;

        const widgetInstance = new TestWidget();
        expect(widgetInstance.$).toBeInstanceOf(WidgetDeveloperApi);
        expect(widgetInstance.$.data).toEqual({ test: 134 });
        expect(widgetInstance.$.settings).toEqual({ aRandomSetting: 32 });
    });

    it('should capture the properties and the type on the prototype', () => {
        @indicatorTile({
            title: 'my title',
            cacheLifespan: WidgetCacheLifespan.hour,
            getQuery() {
                return { fake: { graphql: { query: true } } };
            },
            color: '#FF00FF',
            onClick: jest.fn(),
            value: '12',
            icon: 'alert',
        })
        class TestWidget extends AbstractWidget {}
        (TestWidget.prototype as any).__id = 3;
        (TestWidget.prototype as any).__group = testDashboardGroup;

        expect((TestWidget.prototype as any).__type).toEqual('indicatorTile');
        expect((TestWidget.prototype as any).__properties).toEqual({
            cacheLifespan: WidgetCacheLifespan.hour,
            onClick: expect.any(Function),
            getQuery: expect.any(Function),
            title: 'my title',
            color: '#FF00FF',
            value: '12',
            icon: 'alert',
        });
    });
});
