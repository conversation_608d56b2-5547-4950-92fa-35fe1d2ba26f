import { getMockState, getMockStore } from '../../../__tests__/test-helpers';

import { AbstractWidget, WidgetCacheLifespan } from '..';
import { WidgetDeveloperApi, WidgetType } from '../abstract-widget';
import { staticContent } from '../static-content-widget-decorator';

const testDashboardGroup = 'home';

describe('static content widget decorator', () => {
    beforeEach(() => {
        const mockState = getMockState();
        mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'] = {
            _id: '12',
            title: 'A fake dashboard',
            isSelected: true,
            children: [
                {
                    _id: '3',
                    positions: [
                        { x: 0, y: 0, breakpoint: 'xxs', w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'xs', w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'sm', w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'md', w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'lg', w: 0, h: 0 },
                    ],
                    type: 'indicator tile',
                    settings: { aRandomSetting: 3 },
                },
            ],
        };

        mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = {
            '3': {
                _id: '3',
                artifactName: '@sage/xtrem-test/TestWidget',
                data: { test: 1 },
                properties: {
                    title: 'Test widget',
                    getQuery: () => ({ fake: { graphql: { query: true } } }),
                },
                widgetType: WidgetType.staticContent,
                widgetObject: {} as any,
            },
        };
        getMockStore(mockState);
    });

    it('should add the developer API to the widget', () => {
        @staticContent({
            title: 'my title',
            cacheLifespan: WidgetCacheLifespan.hour,
            getQuery() {
                return { fake: { graphql: { query: true } } };
            },
            content: '*alert*',
        })
        class TestWidget extends AbstractWidget {}
        (TestWidget.prototype as any).__id = '3';
        (TestWidget.prototype as any).__dashboardId = '12';
        (TestWidget.prototype as any).__group = testDashboardGroup;

        const widgetInstance = new TestWidget();
        expect(widgetInstance.$).toBeInstanceOf(WidgetDeveloperApi);
        expect(widgetInstance.$.data).toEqual({ test: 1 });
        expect(widgetInstance.$.settings).toEqual({ aRandomSetting: 3 });
    });

    it('should capture the properties and the type on the prototype', () => {
        @staticContent({
            title: 'my title',
            cacheLifespan: WidgetCacheLifespan.hour,
            content: '*alert*',
            getQuery() {
                return { fake: { graphql: { query: true } } };
            },
        })
        class TestWidget extends AbstractWidget {}
        (TestWidget.prototype as any).__id = 3;
        (TestWidget.prototype as any).__group = testDashboardGroup;

        expect((TestWidget.prototype as any).__type).toEqual('staticContent');
        expect((TestWidget.prototype as any).__properties).toEqual({
            cacheLifespan: WidgetCacheLifespan.hour,
            getQuery: expect.any(Function),
            title: 'my title',
            content: '*alert*',
        });
    });
});
