import { getMockState, getMockStore } from '../../../__tests__/test-helpers';

import { AbstractWidget, WidgetCacheLifespan } from '..';
import { WidgetDeveloperApi, WidgetType } from '../abstract-widget';
import { indicatorTileGroup } from '../indicator-tile-group-widget-decorator';

const testDashboardGroup = 'home';

describe('indicator tile group widget decorator', () => {
    beforeEach(() => {
        const mockState = getMockState();
        mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'] = {
            _id: '12',
            title: 'A fake dashboard',
            isSelected: true,
            children: [
                {
                    _id: '3',
                    positions: [
                        { x: 0, y: 0, breakpoint: 'xxs', w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'xs', w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'sm', w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'md', w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'lg', w: 0, h: 0 },
                    ],
                    type: 'indicator tile group',
                    settings: { aRandomSetting: 42 },
                },
            ],
        };

        mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = {
            '3': {
                _id: '3',
                artifactName: '@sage/xtrem-test/TestWidget',
                data: { test: 456 },
                properties: {
                    title: 'Test widget group',
                    getQuery: () => ({ fake: { graphql: { query: true } } }),
                },
                widgetType: WidgetType.staticContent,
                widgetObject: {} as any,
            },
        };
        getMockStore(mockState);
    });

    it('should add the developer API to the widget', () => {
        @indicatorTileGroup({
            title: 'my group title',
            cacheLifespan: WidgetCacheLifespan.hour,
            getQuery() {
                return { fake: { graphql: { query: true } } };
            },
            content: [
                {
                    title: 'Tile 1',
                    value: '123',
                    valueColor: '#00FF00',
                    icon: 'alert',
                    iconColor: '#FF0000',
                    hasSeparatorAfter: true,
                    contentAlignment: 'left',
                },
                {
                    title: 'Tile 2',
                    value: '456',
                    valueColor: '#0000FF',
                    icon: 'info',
                    contentAlignment: 'right',
                },
            ],
        })
        class TestWidget extends AbstractWidget {}
        (TestWidget.prototype as any).__id = '3';
        (TestWidget.prototype as any).__dashboardId = '12';
        (TestWidget.prototype as any).__group = testDashboardGroup;

        const widgetInstance = new TestWidget();
        expect(widgetInstance.$).toBeInstanceOf(WidgetDeveloperApi);
        expect(widgetInstance.$.data).toEqual({ test: 456 });
        expect(widgetInstance.$.settings).toEqual({ aRandomSetting: 42 });
    });

    it('should capture the properties and the type on the prototype', () => {
        @indicatorTileGroup({
            title: 'my group title',
            cacheLifespan: WidgetCacheLifespan.hour,
            getQuery() {
                return { fake: { graphql: { query: true } } };
            },
            content: [
                {
                    title: 'Tile 1',
                    value: '123',
                    valueColor: '#00FF00',
                    icon: 'alert',
                    iconColor: '#FF0000',
                    hasSeparatorAfter: true,
                    contentAlignment: 'left',
                },
                {
                    title: 'Tile 2',
                    value: '456',
                    valueColor: '#0000FF',
                    icon: 'info',
                    contentAlignment: 'right',
                },
            ],
        })
        class TestWidget extends AbstractWidget {}
        (TestWidget.prototype as any).__id = 3;
        (TestWidget.prototype as any).__group = testDashboardGroup;

        expect((TestWidget.prototype as any).__type).toEqual('indicatorTileGroup');
        expect((TestWidget.prototype as any).__properties).toEqual({
            cacheLifespan: WidgetCacheLifespan.hour,
            getQuery: expect.any(Function),
            title: 'my group title',
            content: [
                {
                    title: 'Tile 1',
                    value: '123',
                    valueColor: '#00FF00',
                    icon: 'alert',
                    iconColor: '#FF0000',
                    hasSeparatorAfter: true,
                    contentAlignment: 'left',
                },
                {
                    title: 'Tile 2',
                    value: '456',
                    valueColor: '#0000FF',
                    icon: 'info',
                    contentAlignment: 'right',
                },
            ],
        });
    });
});
