PATH: XTREEM/Widgets+API/Static+Content+Widget

A widget that displays static content using [Markdown](https://en.wikipedia.org/wiki/Markdown). It can be used to display announcements or other text based summaries to the users.

## Example
```typescript
import * as ui from '@sage/xtrem-ui';

@ui.widgets.staticContent<ListOfUsers>({
    title: 'List of users',
    content() {
        const body = this.$.data.xtremSystem.user.query.edges
            .map((e: any) => `- ${e.node.firstName} ${e.node.lastName} (${e.node.email})`)
            .join('\n');

        return `**List of users**\n${body}`;
    },
    getQuery() {
        return {
            xtremSystem: {
                user: {
                    query: {
                        edges: {
                            node: {
                                email: true,
                                firstName: true,
                                lastName: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class ListOfUsers extends ui.widgets.AbstractWidget {}

```

## Properties
 - **title**: The title of the widget. It can be defined as a value or callback.
 - **content**: The content of the widget in mark down format.
 - **callToActions**: Additional actions buttons that are rendered on the bottom of the widget. The actions defined as an object dictionary containing the following keys:
    - **title**: Title that is rendered on the button. It can be defined as a property or a callback function. If it is defined as a property its value us automatically extracted for localization.
    - **isDisabled**: Whether the action button is disabled. It can be defined as a callback function.
    - **isHidden**: Whether the action button is hidden. It can be defined as a callback function.
    - **onClick**: The event handler that is called when the event is clicked.
