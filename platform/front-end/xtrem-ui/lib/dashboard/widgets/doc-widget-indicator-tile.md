PATH: XTREEM/Widgets+API/Indicator+Tile+Widget

The indicator tile widget is a simple status or value summary indicator. It consists of a main value, an icon with a particular background color and a subtitle.

## Example
```typescript
import * as ui from '@sage/xtrem-ui';

@ui.widgets.indicatorTile<SystemVersion>({
    title: 'System version',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    color: ui.tokens.colorsSemanticCaution500,
    icon: 'sync',
    value() {
        return this.$.data.xtremSystem.sysPackVersion.query.edges?.[0]?.node?.version || 'N/A';
    },
    getQuery() {
        return {
            xtremSystem: {
                sysPackVersion: {
                    query: {
                        __args: { filter: "{name:'@sage/xtrem-system'}" },
                        edges: {
                            node: {
                                version: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class SystemVersion extends ui.widgets.AbstractWidget {}
```

## Properties
 - **title**: The title of the widget. In the case of this widget type, it is displayed under the main value. It can be defined as a value or callback.
 - **value**: The key value, it is displayed in the middle of the indicator tile with large, bold font type. It can be defined as a value or callback.
 - **color**: Color of the indicator icon. It should be defined as a HEX color code. It is applied to the icon as is but with 60% opacity for the icon background circle. It can be defined as a value or callback.
 - **icon**: The icon that is displayed on the left side of the indicator tile widget. It can be defined as a value or callback.
 - **callToActions**: Additional actions buttons that are rendered on the bottom of the widget. The actions defined as an object dictionary containing the following keys:
    - **title**: Title that is rendered on the button. It can be defined as a property or a callback function. If it is defined as a property its value us automatically extracted for localization.
    - **isDisabled**: Whether the action button is disabled. It can be defined as a callback function.
    - **isHidden**: Whether the action button is hidden. It can be defined as a callback function.
    - **onClick**: The event handler that is called when the event is clicked.
