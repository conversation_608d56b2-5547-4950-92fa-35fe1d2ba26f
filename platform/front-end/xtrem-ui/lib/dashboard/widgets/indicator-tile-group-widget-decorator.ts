import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { Constructible } from '../../types';
import type { AbstractWidget, AbstractWidgetDecorator } from './abstract-widget';
import { WidgetType } from './abstract-widget';
import type { WidgetValueOrCallback } from './widget-utils';
import { standardWidgetDecorator } from './widget-utils';

export interface IndicatorTileGroupContent<W extends AbstractWidget> {
    title: string;
    value: string;
    valueColor?: string;
    icon?: IconType;
    iconColor?: string;
    hasSeparatorAfter?: boolean;
    contentAlignment?: 'left' | 'right';
    onClick?: (this: W, index: number | string) => void;
}

export interface IndicatorTileGroupWidgetProperties<W extends AbstractWidget> extends AbstractWidgetDecorator<W> {
    content?: WidgetValueOrCallback<W, IndicatorTileGroupContent<W>[]>;
}

/**
 * The indicator tile group widget is a group of indicator tiles. Each tile consists of a title, a value, an icon and a value color
 * and an icon color.
 * */
export function indicatorTileGroup<W extends AbstractWidget>(
    properties: IndicatorTileGroupWidgetProperties<W>,
): (ctor: Constructible<AbstractWidget>) => void {
    return standardWidgetDecorator(properties, WidgetType.indicatorTileGroup);
}
