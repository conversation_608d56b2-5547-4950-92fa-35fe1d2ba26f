import type { Constructible } from '../../types';
import type { AbstractWidget, AbstractWidgetDecorator } from './abstract-widget';
import { WidgetType } from './abstract-widget';
import type { WidgetValueOrCallback } from './widget-utils';
import { standardWidgetDecorator } from './widget-utils';

export interface StaticContentWidgetProperties<W extends AbstractWidget> extends AbstractWidgetDecorator<W> {
    /** Content of the widget in [Markdown](https://en.wikipedia.org/wiki/Markdown) format. */
    content?: WidgetValueOrCallback<W, string>;
}

/**
 * A widget that displays static content using [Markdown](https://en.wikipedia.org/wiki/Markdown). It can be used to display announcements or other text based summaries to the users.
 */
export function staticContent<W extends AbstractWidget>(
    properties: StaticContentWidgetProperties<W>,
): (ctor: Constructible<AbstractWidget>) => void {
    return standardWidgetDecorator(properties, WidgetType.staticContent);
}
