import type { Constructible } from '../../types';
import type { AbstractWidget, AbstractWidgetDecorator } from './abstract-widget';
import { WidgetType } from './abstract-widget';
import type { WidgetValueOrCallback } from './widget-utils';
import { standardWidgetDecorator } from './widget-utils';

export interface IndicatorTileWidgetProperties<W extends AbstractWidget> extends AbstractWidgetDecorator<W> {
    /** Icon that is displayed on the left side of the widget */
    icon?: WidgetValueOrCallback<W, string>;
    /** Color of the icon and its background */
    color?: WidgetValueOrCallback<W, string>;
    /** Key property value that is displayed in the middle of the widget */
    value?: WidgetValueOrCallback<W, string>;
    /** Key property value that is displayed in the middle of the widget */
    subtitle?: WidgetValueOrCallback<W, string>;
    /** Key property value that is displayed in the middle of the widget */
    decimalDigits?: WidgetValueOrCallback<W, number>;
    /** Action triggered when the tile is clicked. */
    onClick?: (this: W) => void;
}

/**
 * The indicator tile widget is a simple status or value summary indicator. It consists of a main value, an icon with a particular
 * background color and a subtitle.
 * */
export function indicatorTile<W extends AbstractWidget>(
    properties: IndicatorTileWidgetProperties<W>,
): (ctor: Constructible<AbstractWidget>) => void {
    return standardWidgetDecorator(properties, WidgetType.indicatorTile);
}
