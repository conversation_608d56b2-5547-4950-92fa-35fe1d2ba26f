PATH: XTREEM/Widgets+API/Table+Widget

A widget that tabular data with either a card or a table view. It can be configured with various options including filter dropdowns and selection checkboxes.

## Example

```typescript
import * as ui from '@sage/xtrem-ui';
@ui.widgets.table<ListOfUsersTable>({
    title: 'Users',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    content() {
        return this.$.data.xtremSystem.user.query.edges.map(({ node }: any) => ({
            _id: node._id,
            title: node.firstName,
            titleRight: node.lastName,
            longContent: node.userDescription,
            line2: node.email,
            line2Right: node.userType,
            image: node.photo?.value,
        }));
    },
    canSwitchViewMode: true,
    displayMode: 'card',
    canSelect: true,
    dataDropdownMenu: {
        userType: {
            all: { title: 'All users' },
            normal: { title: 'Normal accounts' },
            administrative: { title: 'Administrative accounts' },
            inactive: { title: 'Inactive accounts' },
        },
        orderBy: {
            firstName: { title: 'Sort by first name' },
            lastName: { title: 'Sort by last name' },
            userType: { title: 'Sort by user type' },
        },
    },
    rowDefinition: {
        title: { title: 'First name', displayOptions: { columnWidth: 500 }},
        titleRight: { title: 'Last name', displayOptions: { isLabelDisplayedOnCard: true }  },
        longContent: {title: 'Description' }
        line2: {
            title: 'Email',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-authorization/User', { _id });
            },
        },
        line2Right: {
            title: 'User Type',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    return Number(_id) % 2 ? 'warning' : 'positive';
                },
                tooltipText(_id: string) {
                    const userNode = this.$.data.xtremSystem.user.query.edges.find(
                        ({ node }: any) => node._id === _id,
                    );

                    return `This belongs to ${userNode.node.firstName}`;
                },
            },
        },
    },
    rowActions: [
        {
            label: 'Open',
            onClick(_id) {
                this.$.router.goTo('@sage/xtrem-authorization/User', { _id });
            },
            isHidden(_id) {
                return true;
            },
        },
        ui.menuSeparator(),
        {
            label: 'Disabled',
            isDisabled(_id) {
                return true;
            },
        },
        ui.menuSeparator(),
        {
            label: 'Click',
            onClick(_id, row) {
                console.log(_id, row);
            },
        },
        ui.menuSeparator(),
        {
            label: 'Delete',
            isDestructive: true,
            icon: 'delete',
            onClick(_id) {
                this.$.router.goTo('@sage/xtrem-authorization/User', { _id });
            },
        },
    ],
    getQuery() {
        const filter: any = {};
        if (this.$.options.dataOptions?.userType === 'normal') {
            filter.isAdministrator = false;
            filter.isActive = true;
        }

        if (this.$.options.dataOptions?.userType === 'administrative') {
            filter.isAdministrator = true;
        }

        if (this.$.options.dataOptions?.userType === 'inactive') {
            filter.isActive = false;
        }

        const orderBy: any = {};
        if (!this.$.options.dataOptions?.orderBy || this.$.options.dataOptions?.orderBy === 'firstName') {
            orderBy.firstName = 1;
        }

        if (this.$.options.dataOptions?.orderBy === 'lastName') {
            orderBy.lastName = 1;
        }

        if (this.$.options.dataOptions?.userType === 'userType') {
            orderBy.userType = 1;
        }

        return {
            xtremSystem: {
                user: {
                    query: {
                        __args: { filter: JSON.stringify(filter), orderBy: JSON.stringify(orderBy) },
                        edges: {
                            node: {
                                _id: true,
                                email: true,
                                firstName: true,
                                lastName: true,
                                userDescription: true,
                                userType: true,
                                photo: {
                                    value: true,
                                },
                            },
                        },
                    },
                },
            },
        };
    },
})
export class ListOfUsersTable extends ui.widgets.TableWidget {}

```

## Properties

- **title**: The title of the widget. It can be defined as a value or callback.
- **businessIcon**: A business icon to be displayed in the table widget's header.
- **rowDefinition**: The metadata that is used to identify in which place and which order columns should be rendered. If you wish to use the card layout, you can define the location of your field by using one of the following keys: `title`, `titleRight`, `line2`, `line2Right`. In order to display an icon or an image in each row (for both card and table mode), define either the `image` key or the `icon` key. Please see below for more details about the content of this dictionary object in the "Column definition object" section. For images, the expected value is a binary stream value, for icons a design system icon type in string format.
In case of table view presentation The `longContent` key adds an expandable row that is suitable for displaying long, multi line content. In case of card presentation, it is displayed between the title and line 2 rows.
- **content**: The data that gets displayed to the user. It is expected to be an array of objects. The object properties are only rendered if a corresponding definition is found in the `rowDefinition` property.
- **displayMode**: The view mode of the table, it can be either `card` or `table`, defaults to `table`.
- **canSwitchViewMode**: Whether the user can switch between view modes, defaults to false.
- **canSelect**: Whether selection checkboxes should be displayed in each row. The list IDs of the selected rows can be accessed via the `this.$.options.selectedItems` property from the application code.
- **filterMenuType**: Either `dropdown` or `tabs`. Depending on this value the data options specified by the `dataDropdownMenu` will be available as dropdown options or as tabs.
- **dataDropdownMenu**: Dictionary of dropdown menu definition. The dropdown menus are displayed between the header and the body. When the user changes the selected value on the dropdown, the data will be refetched from the server. It can be used for quick filters and sorting operations. The user's selection set can be accessed via the `this.$.options.dataOptions` property from the application code.
- **callToActions**: Additional actions buttons that are rendered on the bottom of the widget. The actions defined as an object dictionary containing the following keys:
  - **title**: Title that is rendered on the button. It can be defined as a property or a callback function. If it is defined as a property its value us automatically extracted for localization.
  - **isDisabled**: Whether the action button is disabled. It can be defined as a callback function.
  - **isHidden**: Whether the action button is hidden. It can be defined as a callback function.
  - **onClick**: The event handler that is called when the event is clicked.
- **dateFilter**: Specifies the properties for the date or date period by which the table's data is filter. The dateFilter accepts the following properties:
  - **defaultDate**: Date the date filter defaults to, when not modified by the end user. When left unspecified, the property defaults to the current date.
  - **defaultPeriodType**: Period type the date filter defaults to, when not modified by the end user. When left unspecified, the property defaults to "DateFilterPeriodType.DAY".
  - **maxDate**: Upper bound of the filtered data.
  - **minDate**: Lower bound of the filtered data.
  - **periodTypes**: Period types the end user can choose from. When left unspecified, the property defaults to all available period types. The following period types are available:
    - DateFilterPeriodType.DAY
    - DateFilterPeriodType.WEEK
    - DateFilterPeriodType.MONTH
    - DateFilterPeriodType.QUARTER
    - DateFilterPeriodType.YEAR
- **totalCount**: Callback that allows the application developer to define the total count of items in the table. The application developer should account for the total count in their getQuery() returned query. After executing the returned query, the totalCount() callback will be called, in which the developer can return the total count based on the this.$.data variable that contains the query result.
- **rowActions**: A list of actions that are displayed at the end of the table as a drop-down menu. See below under "Row action properties" how they can be configured.

## Column definition object

A definition object determines how a given column is rendered.

- **title**: The title of the columns. If it is defined as a property, the value is automatically extracted for internationalization.
- **renderedAs**: Determines how the field is rendered. It can be unset, `pill`, `link`, or `icon`.
  - **`unset`**: Renders the field with the default style, without specific visual modifications.
  - **`pill`**: Renders the field as a pill-shaped element, suitable for labels or tags.
  - **`link`**: Renders the field as a clickable link, useful for navigation to other pages or resources.
  - **`icon`**: Renders the field as an icon, providing a visual indicator or status symbol. This option is useful for quickly conveying the field's state or category through a graphical element.

- **onClick**: Event listener which is triggered when the column is clicked. The `_id` property of the row is supplied as an argument.
- **displayOptions**: Further formatting options
  - **tooltipText**: Text content which is displayed as a tooltip when the user hovers over the column. It is also available as a callback with the row _id as an argument.
  - **colorVariant**: Color variant for the pill view mode. It is also available as a callback with the row _id as an argument.
  - **columnWidth**: Column width is to set the width for each column. It is expecting a number, it is not available as a callback.
  - **isLabelDisplayedOnCard**: The isLabelDisplayedOnCard is a boolean to see the title of certain data elements prepended in the cardview.


### Row action properties

-   **title**: The title of the action displayed in the a drop-down menu.
-   **icon**: An optional icon for the action, to be displayed alongside the action title.
-   **onClick**: Event triggered when an action is selected in the drop-down menu.
-   **isDisabled**: Whether the dropdown action is enabled (disabled = false) or not (disabled = true). It can also be defined as callback function that returns a boolean. It is enabled by default.
-   **isHidden**: Whether the table action is visible (isHidden = false) or not (isHidden = true). It can also be defined as callback function that returns a boolean. The row action is visible by default.
-   **isDestructive**: If set, the icon corresponding to the action is rendered in red.

In addition to a row action, a separator line can also added using `ui.menuSeparator({ id?: string, insertAfter?: string, insertBefore?: string })`.

If only one dropdown action is set, then only one button is displayed, otherwise a dropdown containing the available dropdown actions.

