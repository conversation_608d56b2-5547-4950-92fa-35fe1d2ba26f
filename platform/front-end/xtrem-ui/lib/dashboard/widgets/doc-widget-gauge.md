PATH: XTREEM/Widgets+API/Gauge+Widget

A widget to display a gauge. Optionally, it also has an evolution indicator.

## Example
```typescript
import * as ui from '@sage/xtrem-ui';
import { ShowCaseProduct } from '@sage/xtrem-show-case-api';

@ui.widgets.gauge<AdministratorGauge, ShowCaseProduct>({
    title: 'Ratio of administrators',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    content() {
        const body = this.$.data.xtremSystem.user;
        return {
            value: body.adminCount.totalCount,
            totalValue: body.totalUserCount.totalCount,
            evolutionValue: 1,
        };
    },
    scale: 0,
    color: ui.tokens.colorsSemanticPositive500,
    evolutionUnit: 'pcs',
    callToActions: {
        cantDoThis: {
            title: 'Some action',
            onClick() {
                this.$.showToast('Well you cannot do much with these users');
            },
        },
    },
    getQuery() {
        return {
            xtremSystem: {
                user: {
                    adminCount: {
                        __aliasFor: 'query',
                        __args: { filter: '{isAdministrator: true}' },
                        totalCount: true,
                    },
                    totalUserCount: {
                        __aliasFor: 'query',
                        totalCount: true,
                    },
                },
            },
        };
    },
})
export class AdministratorGauge extends ui.widgets.AbstractWidget { }
```

## Properties
 - **title**: The title of the widget. It can be defined as a value or callback.
 - **content**: A function to remap the data returned by the `getQuery` function. It can be defined as a value or callback. The expected return value is an object that contains the following properties:
    - **value**: The value to be displayed on the gauge.
    - **totalValue**: The maximum value of the gauge, it is used to determine the ratio of the gauge. It is optional, the default value is `100`.
    - **evolutionValue**: The value that is displayed on the evolution indicator. It is optional, if not provided, the evolution indicator is not displayed.
 - **scale**: Number of decimal digits to be displayed for the value and the evolution indicator. It can be defined as a value or callback.
 - **color**: The color of the bar indicating the value on the gauge. It can be defined as a value or callback.
 - **valueUnit**: Unit postfix for the value, it defaults to `%`. It can be defined as a value or callback.
 - **evolutionUnit**: Unit postfix for the value, it defaults to `%`. It can be defined as a value or callback.
 - **callToActions**: Additional actions buttons that are rendered on the bottom of the widget. The actions defined as an object dictionary containing the following keys:
    - **title**: Title that is rendered on the button. It can be defined as a property or a callback function. If it is defined as a property its value us automatically extracted for localization.
    - **isDisabled**: Whether the action button is disabled. It can be defined as a callback function.
    - **isHidden**: Whether the action button is hidden. It can be defined as a callback function.
    - **onClick**: The event handler that is called when the event is clicked.
