import type { Constructible } from '../../types';
import type { AbstractWidget, AbstractWidgetDecorator } from './abstract-widget';
import { WidgetType } from './abstract-widget';
import type { WidgetValueOrCallback } from './widget-utils';
import { standardWidgetDecorator } from './widget-utils';

export interface GaugeWidgetData {
    value: number;
    totalValue?: number;
    evolutionValue?: number;
}

export interface GaugeWidgetProperties<W extends AbstractWidget> extends AbstractWidgetDecorator<W> {
    content?: WidgetValueOrCallback<W, GaugeWidgetData>;
    scale?: WidgetValueOrCallback<W, number>;
    valueUnit?: WidgetValueOrCallback<W, string>;
    color?: WidgetValueOrCallback<W, string>;
    evolutionUnit?: WidgetValueOrCallback<W, string>;
}

export function gauge<W extends AbstractWidget>(
    properties: GaugeWidgetProperties<W>,
): (ctor: Constructible<AbstractWidget>) => void {
    return standardWidgetDecorator(properties, WidgetType.gauge);
}
