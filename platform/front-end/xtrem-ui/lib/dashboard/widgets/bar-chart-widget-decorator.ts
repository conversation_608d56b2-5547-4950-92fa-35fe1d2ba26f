import type { Dict } from '@sage/xtrem-shared';
import type { Constructible } from '../../types';
import type { AbstractWidget, AbstractWidgetDecorator, WithContent } from './abstract-widget';
import { WidgetType } from './abstract-widget';
import type { ChartProps, ExtractWidgetDataType } from './chart-types';
import { standardWidgetDecorator } from './widget-utils';

export interface BarChartWidgetProperties<W extends AbstractWidget, C = any, P = any, S = any>
    extends AbstractWidgetDecorator<W>,
        WithContent<W, C>,
        ChartProps<W, C, P, S> {
    areAxesStacked?: boolean;
    areAxesSwapped?: boolean;
    dataOptions?: Dict<Dict<string>>;
    isHistogram?: boolean;
}

// A widget that displays a bar chart
export function barChart<W extends AbstractWidget, C = any, P = any, S = number>(
    properties: BarChartWidgetProperties<W, C, P, S>,
): (ctor: Constructible<AbstractWidget<ExtractWidgetDataType<W>>>) => void {
    return standardWidgetDecorator(properties, WidgetType.barChart);
}
