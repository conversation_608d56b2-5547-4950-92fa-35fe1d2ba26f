import Box from 'carbon-react/esm/components/box';
import styled from 'styled-components';
import type { Constructible } from '../../types';
import type { AbstractWidget, AbstractWidgetDecorator, WidgetType } from './abstract-widget';
import { WidgetCacheLifespan } from './abstract-widget';

export const SplitBox = styled(Box)`
    & > div {
        flex: 1;
    }
`;

export const standardWidgetDecorator =
    <W extends AbstractWidget>(properties: AbstractWidgetDecorator<W>, type: WidgetType) =>
    (ctor: Constructible<AbstractWidget>): void => {
        // Sets the basic types to the prototype.
        ctor.prototype.__properties = {
            ...properties,
            cacheLifespan: properties.cacheLifespan || WidgetCacheLifespan.day,
        };
        ctor.prototype.__type = type;
    };

export type WidgetValueOrCallback<Context extends AbstractWidget, T extends any = any> = T | ((this: Context) => T);

export type WidgetValueOrCallbackWithId<Context extends AbstractWidget, T extends any = any> =
    | T
    | ((this: Context, _id: string) => T);
