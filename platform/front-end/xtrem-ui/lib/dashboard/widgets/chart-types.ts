import type { Dict } from '@sage/xtrem-shared';
import type { AbstractWidget } from './abstract-widget';
import type { WidgetValueOrCallback } from './widget-utils';

interface AxisDefinition<W extends AbstractWidget> {
    bind: string;
    title?: WidgetValueOrCallback<W, string>;
}

interface SecondaryAxisDefinition<W extends AbstractWidget, T = any, P = any, S = any> extends AxisDefinition<W> {
    color?: React.CSSProperties['color'];
    onClick?: (this: W, record: T, primaryValue: P, secondaryValue: S) => void;
    tooltipContent?: (this: W, record: T, primaryValue: P, secondaryValue: S) => string;
}

interface ChartProps<W extends AbstractWidget, T = any, P = any, S = any> {
    dataOptions?: Dict<Dict<string>>;
    primaryAxis: AxisDefinition<W>;
    primaryAxisLabel?: string;
    secondaryAxes: SecondaryAxisDefinition<W, T, P, S>[];
    secondaryAxisLabel?: string;
}

type ExtractWidgetDataType<W extends AbstractWidget> = W extends AbstractWidget<infer D> ? D : any;

export type { AxisDefinition, ChartProps, ExtractWidgetDataType, SecondaryAxisDefinition };
