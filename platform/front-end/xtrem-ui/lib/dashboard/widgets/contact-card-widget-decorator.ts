import type { ContactCardProps } from '@sage/bms-dashboard';
import type { Constructible } from '../../types';
import { WidgetType, type AbstractWidget, type AbstractWidgetDecorator } from './abstract-widget';
import { standardWidgetDecorator, type WidgetValueOrCallback } from './widget-utils';

export type ContactCardWidgetOptions = Omit<ContactCardProps, 'stringLiterals'> & {
    selectedContactType?: 'contact' | 'site';
};

type ContactCardWidgetType = AbstractWidget<any, ContactCardWidgetOptions>;
export type ContactCardWidgetContent = Omit<ContactCardProps, 'stringLiterals'>;

export interface ContactCardWidgetProperties<W extends ContactCardWidgetType> extends AbstractWidgetDecorator<W> {
    content?: WidgetValueOrCallback<W, ContactCardWidgetContent>;
}

/**
 * The contact card widget
 * */
export function contactCard<W extends ContactCardWidgetType>(
    properties: ContactCardWidgetProperties<W>,
): (ctor: Constructible<ContactCardWidgetType>) => void {
    return standardWidgetDecorator(properties, WidgetType.contactCard);
}
