PATH: XTREEM/Widgets+API/Visual+Process+Widget

A widget to render visual process on the dashboard. It uses `@sage/visual-process-editor` under the hood to render the vp documents.

## Example
```typescript
import * as ui from '@sage/xtrem-ui';
import { ShowCaseProduct } from '@sage/xtrem-show-case-api';

@ui.widgets.visualProcess<VisualProcessExample>({
    title: 'Products',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    content() {
        return {
            // ... Visual process JSON document.
        }
    },
    callToActions: {
        doSomething: {
            title: 'Action',
            onClick() {
                this.$.showToast('Perform action on products');
            },
        },
    },
    getQuery() {
        return {
            xtremShowCase: {
                showCaseProduct: {
                    query: {
                        edges: {
                            node: {
                                _id: true,
                                product: true,
                                description: true,
                                listPrice: true,
                                tax: true,
                                amount: true,
                                total: true,
                                releaseDate: true,
                                qty: true,
                                netPrice: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class VisualProcessExample extends ui.widgets.AbstractWidget {}

```

## Properties
 - **title**: The title of the widget. In the case of this widget type, it is displayed under the main value. It can be defined as a value or callback.
 - **content**: A function to remap the data returned by the `getQuery` function. The output of this function will be used as input data for the the visual process. The content must be an XDocument interface compliant visual process object.
 - **callToActions**: Additional actions buttons that are rendered on the bottom of the widget. The actions defined as an object dictionary containing the following keys:
    - **title**: Title that is rendered on the button. It can be defined as a property or a callback function. If it is defined as a property its value us automatically extracted for localization.
    - **isDisabled**: Whether the action button is disabled. It can be defined as a callback function.
    - **isHidden**: Whether the action button is hidden. It can be defined as a callback function.
    - **onClick**: The event handler that is called when the event is clicked.
