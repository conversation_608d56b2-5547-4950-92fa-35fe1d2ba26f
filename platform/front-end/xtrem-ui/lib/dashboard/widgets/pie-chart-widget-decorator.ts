import type { PieChartProps } from '@sage/bms-dashboard';
import type { Dict } from '@sage/xtrem-shared';
import type { Constructible } from '../../types';
import type { AbstractWidget, AbstractWidgetDecorator } from './abstract-widget';
import { WidgetType } from './abstract-widget';
import type { RowDefinition, TableWidgetRow } from './table-widget-decorator';
import type { WidgetValueOrCallback } from './widget-utils';
import { standardWidgetDecorator } from './widget-utils';

export type PieChartWidgetOptions = {
    selectedFilter?: string;
};

type PieChartWidgetType = AbstractWidget<any, PieChartWidgetOptions>;

export interface PieChartWidgetProperties<W extends PieChartWidgetType>
    extends AbstractWidgetDecorator<W>,
        Omit<PieChartProps, 'pieChartData' | 'callToActions'> {
    rowDefinition: WidgetValueOrCallback<W, RowDefinition<W>>;
    content?: WidgetValueOrCallback<W, TableWidgetRow[]>;
    isDonut?: boolean;
    hasCardView?: boolean;
    filter?: {
        ariaLabel: string;
        options: Dict<string>;
        onChange(this: PieChartWidgetType, newValue: string | null): void;
        value?: string;
        isFullWidth?: boolean;
        representation?: 'dropdown' | 'tabs';
        isEmptyAllowed?: boolean;
    };
}

// A widget that displays a pie chart
export function pieChart<W extends PieChartWidgetType>(
    properties: PieChartWidgetProperties<W>,
): (ctor: Constructible<W>) => void {
    return standardWidgetDecorator(properties, WidgetType.pieChart);
}
