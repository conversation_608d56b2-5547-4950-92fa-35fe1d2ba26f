import type { Constructible } from '../../types';
import type { AbstractWidget, AbstractWidgetDecorator } from './abstract-widget';
import { WidgetType } from './abstract-widget';
import type { WidgetValueOrCallback } from './widget-utils';
import { standardWidgetDecorator } from './widget-utils';
import type { XDocument } from '@sage/visual-process-editor';

export interface VisualProcessWidgetProperties<W extends AbstractWidget> extends AbstractWidgetDecorator<W> {
    content?: WidgetValueOrCallback<W, XDocument>;
}

export function visualProcess<W extends AbstractWidget>(
    properties: VisualProcessWidgetProperties<W>,
): (ctor: Constructible<AbstractWidget>) => void {
    return standardWidgetDecorator(properties, WidgetType.visualProcess);
}
