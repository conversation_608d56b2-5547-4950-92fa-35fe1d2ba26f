PATH: XTREEM/Widgets+API/Bar+Chart+Widget

A widget to show bar charts.

## Example
```typescript
import * as ui from '@sage/xtrem-ui';
import { ShowCaseProduct } from '@sage/xtrem-show-case-api';

interface Product {
    id: string;
    listPrice: number;
    amount: number;
}
@ui.widgets.barChart<ProductBarChart, Product>({
    title: 'Products',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    content() {
        return this.$.data.xtremShowCase.showCaseProduct.query.edges.map(
            ({ node: { listPrice, amount, _id } }: { node: ShowCaseProduct }) => ({
                id: _id,
                listPrice: Number.parseFloat(listPrice),
                amount: Number.parseFloat(amount),
            }),
        );
    },
    callToActions: {
        doSomething: {
            title: 'Action',
            onClick() {
                this.$.showToast('Perform action on products');
            },
        },
    },
    primaryAxis: {
        bind: 'id',
        title: 'Product ID',
    },
    secondaryAxes: [
        {
            bind: 'listPrice',
            title: 'Price',
        },
        {
            bind: 'amount',
            title: 'Amount',
            onClick({ record, primaryValue, secondaryValue }) {
                this.$.showToast(`Clicked on: ${JSON.stringify(record)}`);
            },
            tooltipContent({ primaryValue, secondaryValue }) {
                return `ID: ${primaryValue} - Amount: ${secondaryValue}`;
            },
        },
    ],
    getQuery() {
        return {
            xtremShowCase: {
                showCaseProduct: {
                    query: {
                        edges: {
                            node: {
                                _id: true,
                                product: true,
                                description: true,
                                listPrice: true,
                                tax: true,
                                amount: true,
                                total: true,
                                releaseDate: true,
                                qty: true,
                                netPrice: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class ProductBarChart extends ui.widgets.AbstractWidget<{
    xtremShowCase: { showCaseProduct: { query: { edges: { node: ShowCaseProduct }[] } } };
}> {}

```

## Properties
 - **title**: The title of the widget. In the case of this widget type, it is displayed under the main value. It can be defined as a value or callback.
 - **content**: A function to remap the data returned by the `getQuery` function. The output of this function will be used as input data for the bar chart.
 - **primaryAxis**: The primary axis definition:
    - **bind**: The property the primary axis is bound to.
    - **title**: The title for the primary axis.
 - **secondaryAxes**: The secondary axes definition, specified as an array of:
    - **bind**: The property the secondary axis is bound to.
    - **title**: The title for the secondary axis.
    - **onClick**: Function that gets executed whenever a line circle is clicked. It receives the following arguments:
        - **record**: The value of the entire record.
        - **primaryValue**: The value of the primary axis.
        - **secondaryValue**: The value of the secondary axis.
    - **tooltipContent**: Function that gets executed whenever the user hovers over the line chart. Must return a string. It receives the following arguments:
        - **record**: The value of the entire record.
        - **primaryValue**: The value of the primary axis.
        - **secondaryValue**: The value of the secondary axis.
 - **areAxesSwapped**: Whether primary and secondary axes are to be swapped. Defaults to `false`.
 - **areAxesStacked**: Whether secondary axes are to be stacked. Defaults to `false`.
 - **isHistogram**: Whether bars are to be adjacent to each other. Defaults to `false`.
 - **callToActions**: Additional actions buttons that are rendered on the bottom of the widget. The actions defined as an object dictionary containing the following keys:
    - **title**: Title that is rendered on the button. It can be defined as a property or a callback function. If it is defined as a property its value us automatically extracted for localization.
    - **isDisabled**: Whether the action button is disabled. It can be defined as a callback function.
    - **isHidden**: Whether the action button is hidden. It can be defined as a callback function.
    - **onClick**: The event handler that is called when the event is clicked.
