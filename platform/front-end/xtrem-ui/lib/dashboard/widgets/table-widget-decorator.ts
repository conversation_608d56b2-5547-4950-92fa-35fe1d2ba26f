import type {
    DateFilterPeriodType,
    PeriodDetails,
    RenderAsType,
    TableWidgetProps,
    ValueFormatType,
    WidgetCollectionItem,
} from '@sage/bms-dashboard';
import type { Dict } from '@sage/xtrem-shared';
import type { IconType } from 'carbon-react/esm/components/icon';
import type { VoidPromise } from '../../component/field/traits';
import type { Constructible } from '../../types';
import { WidgetType, type AbstractWidget, type AbstractWidgetDecorator } from './abstract-widget';
import type { WidgetValueOrCallback, WidgetValueOrCallbackWithId } from './widget-utils';
import { standardWidgetDecorator } from './widget-utils';

export type TableWidgetOptions = {
    mode?: 'card' | 'table';
    dataOptions?: Dict<string>;
    selectedPeriod?: PeriodDetails;
    selectedItems?: string[];
};

type TableWidgetType = AbstractWidget<any, TableWidgetOptions>;

export type ColorVariant = Extract<
    NonNullable<NonNullable<TableWidgetProps['rowDefinition'][number]>['displayOptions']>['colorVariant'],
    string
>;
export interface WidgetDisplayOptions<W extends TableWidgetType> {
    colorVariant?: WidgetValueOrCallbackWithId<W, ColorVariant>;
    tooltipText?: WidgetValueOrCallbackWithId<W, string>;
    columnWidth?: number;
    isLabelDisplayedOnCard?: boolean;
}
export interface ColumnDefinitionOptions<W extends TableWidgetType> {
    title: string;
    onClick?: (this: W, _id: string) => VoidPromise;
    displayOptions?: WidgetDisplayOptions<W>;
    renderedAs?: RenderAsType;
    valueFormat?: ValueFormatType;
    decimalDigits?: number;
}

export interface RowDefinition<W extends AbstractWidget> {
    title: ColumnDefinitionOptions<W>;
    titleRight?: ColumnDefinitionOptions<W>;
    line2?: ColumnDefinitionOptions<W>;
    line2Right?: ColumnDefinitionOptions<W>;
    line3?: ColumnDefinitionOptions<W>;
    line3Right?: ColumnDefinitionOptions<W>;
    line4?: ColumnDefinitionOptions<W>;
    line4Right?: ColumnDefinitionOptions<W>;
    line5?: ColumnDefinitionOptions<W>;
    line5Right?: ColumnDefinitionOptions<W>;
    image?: ColumnDefinitionOptions<W>;
    icon?: ColumnDefinitionOptions<W>;
    [key: string]: ColumnDefinitionOptions<W> | undefined;
}

export interface TableWidgetRow extends Omit<WidgetCollectionItem, 'i'> {
    _id: number | string;
}

export type TableDropDownOption = Dict<{ title: string }>;
export type TableDropDownMenu = Dict<TableDropDownOption>;

export interface TableWidgetDateFilter<W extends TableWidgetType> {
    minDate?: WidgetValueOrCallback<W, Date>;
    maxDate?: WidgetValueOrCallback<W, Date>;
    /** Type of periods that the user can choose from. Defaults to all available periods */
    periodTypes?: DateFilterPeriodType[];
    /** Defaults to day */
    defaultPeriodType?: DateFilterPeriodType;
    /** Defaults to today */
    defaultDate?: WidgetValueOrCallback<W, Date>;
}

export interface TableWidgetProperties<W extends TableWidgetType> extends AbstractWidgetDecorator<W> {
    businessIcon?: WidgetValueOrCallback<W, TableWidgetProps['iconSrc']>;
    canSelect?: WidgetValueOrCallback<W, boolean>;
    canSwitchViewMode?: WidgetValueOrCallback<W, boolean>;
    content?: WidgetValueOrCallback<W, TableWidgetRow[]>;
    dataDropdownMenu?: WidgetValueOrCallback<W, TableDropDownMenu>;
    dateFilter?: TableWidgetDateFilter<W>;
    displayMode?: WidgetValueOrCallback<W, 'card' | 'table'>;
    filterMenuType?: TableWidgetProps['filterMenuType'];
    rowDefinition: WidgetValueOrCallback<W, RowDefinition<W>>;
    totalCount?: WidgetValueOrCallback<W, number | undefined>;
    rowActions?: WidgetValueOrCallback<
        W,
        (
            | (Omit<NonNullable<TableWidgetProps['rowActions']>[number], 'onClick' | 'isHidden' | 'isDisabled'> & {
                  icon?: IconType;
                  isDestructive?: boolean;
                  isDisabled?: boolean | ((this: W, i: string | number, row: TableWidgetRow) => boolean);
                  isHidden?: boolean | ((this: W, i: string | number, row: TableWidgetRow) => boolean);
                  isMenuSeparator?: false;
                  onClick: (this: W, i: string | number, row: TableWidgetRow) => VoidPromise;
                  title: string;
              })
            | (Omit<NonNullable<TableWidgetProps['rowActions']>[number], 'onClick' | 'isHidden' | 'isDisabled'> & {
                  id?: string;
                  isHidden?: boolean | ((this: W, i: string | number, row: TableWidgetRow) => boolean);
                  isMenuSeparator: true;
              })
        )[]
    >;
}

/**
 * A widget that displays tabular data as a table or card view.
 */
export function table<W extends TableWidgetType>(
    properties: TableWidgetProperties<W>,
): (ctor: Constructible<TableWidgetType>) => void {
    return standardWidgetDecorator(properties, WidgetType.table);
}
