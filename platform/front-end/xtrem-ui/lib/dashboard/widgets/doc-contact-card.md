PATH: XTREEM/Widgets+API/Contact+Card+Widget

The contact card widget is a widget is used to display some information about contacts and sites. A toggle button allows the user to switch between two different views and a dropdown allows them to select either a contact or a site from a list.

## Example

```typescript
import * as ui from '@sage/xtrem-ui';

@ui.widgets.contactCard<ContactList>({
  title: "Contact cards",
  cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
  description: "Contact cards",
  category: "OTHER_CATEGORY",
  content() {
    const edges = this.$.data?.xtremShowCase?.showCaseProvider?.query?.edges;
    return {
      notes: this.$.options.notes ?? edges?.[0]?.node?.notes ?? [],
      address:
        this.$.options.address ?? edges?.[0]?.node?.address ?? "",
      addressRole:
        this.$.options.addressRole ?? edges?.[0]?.node?.addressRole ?? "",
      addressFunction:
        this.$.options.addressFunction ??
        edges?.[0]?.node?.addressFunction ??
        "",
      contactName:
        this.$.options.contactName ??
        edges?.[0]?.node?.contactName ?? '',
      contactEmailAddress:
        this.$.options.contactEmailAddress ??
        edges?.[0]?.node?.contactEmailAddress ?? '',
      contactImage:
        this.$.options.contactImage ??
        edges?.[0]?.node?.contactImage ?? '',
      contactPhoneNumber:
        this.$.options.contactPhoneNumber ??
        edges?.[0]?.node?.contactPhoneNumber ?? '',
      contactPosition:
        this.$.options.contactPosition ??
        edges?.[0]?.node?.contactPosition ?? '',
      contactRole:
        this.$.options.contactRole ??
        edges?.[0]?.node?.contactRole ?? '',
      contacts: this.$.options.contacts ?? edges?.[0]?.node?.contacts ?? {},
      icon: this.$.options.icon ?? edges?.[0]?.node?.icon ?? "",
      numberOfAddresses:
        this.$.options.numberOfAddresses ?? edges?.[0]?.node?.numberOfAddresses ?? 0,
      numberOfContacts:
        this.$.options.numberOfContacts ?? edges?.[0]?.node?.numberOfContacts ?? 0,
      onContactTypeSwitchChanged: (selectedContactType) => {
        this.$.showToast("Contact type switched", { type: "success" });
      },
      onContactAdd: () => {
        this.$.showToast("Contact added", { type: "success" });
      },
      onNoteAdded: (newNoteText) => {
        this.$.showToast("Note added", { type: "success" });
      },
      onNoteDeleted: (deletedId) => {
        this.$.showToast("Note deleted", { type: "success" });
      },
      onNoteEdited: (editedId, text) => {
        this.$.showToast("Note edited", { type: "success" });
      },
      onSelectedContactChanged: (selectedContactId) => {
        this.$.showToast(
          `Selected ${
            this.$.options.selectedContactType ?? ""
          }: ${selectedContactId}`,
          {
            type: "success",
          }
        );
      },
    };
  },
  getQuery() {
    return {
      xtremShowCase: {
        showCaseProvider: {
          query: {
            edges: {
              node: {
                notes: {
                    _id: true,
                    text: true,
                    timestamp: true,
                },
                address: true,
                addressFunction: true,
                contactName: true,
                contactEmailAddress: true,
                contactImage: true,
                contactPhoneNumber: true,
                contactPosition: true,
                contactRole: true,
                contacts: true,
                icon: true,
                numberOfAddresses: true,
                numberOfContacts: true,
              },
            },
          },
        },
      },
    };
  },
})
export class ContactList extends ui.widgets.ContactCardWidget<{
  xtremShowCase: {
    showCaseProvider: {
      query: {
        edges: {
          node: {};
        }[];
      };
    };
  };
}> {}


```

## Properties

- **title**: A title for the widget. It is displayed under the main value and can be defined as a value or callback.
- **description**: A description for the widget. It is displayed under the main value and can be defined as a value or callback.
- **category**: A category for the widget. It is displayed above the main value and can be defined as a value or callback.
- **content**: The data that gets displayed to the user. It is expected to be an object with the following properties:
  - **address**: The address
  - **addressRole**: The address role
  - **addressFunction**: The address function
  - **canAddNotes**: If the value is true or unset then the Add note button available otherwise it is not
  - **contactEmailAddress**: The contact's email
  - **contactImage**: The contact's image which will be used as a `src` attribute for an image element
  - **contactName**: The contact's name
  - **contactPhoneNumber**: The contact's phone number
  - **contactPosition**: The contact's position
  - **contactRole**: The contact's role
  - **contacts**: A dictionary of contacts/sites, which will be displayed in the main dropdown
  - **icon**: The contact's icon
  - **notes**: A list of notes as an array of objects with the following properties
    - **_id**: The note's ID
    - **text**: The note's text
    - **timestamp**: The note's timestamp as a Javascript `Date`
  - **numberOfAddresses**: The total number of addresses
  - **numberOfContacts**: The total number of contacts
  - **onContactAdd**: A callback function that is triggered when there are no contacts/sites and the "Add contact" button is clicked
  - **onContactTypeSwitchChanged**: A callback function that is triggered when the user navigates from the contact view to the site view or viceversa
  - **onNoteAdded**: A callback function that is triggered when a new note is added
  - **onNoteDeleted**: A callback function that is triggered when a note is deleted
  - **onNoteEdited**: A callback function that is triggered when a note is edited
  - **onSelectedContactChanged**: A callback function that is triggered when a contact/site is selected
  - **selectedAddressId**: The ID of the currently selected address. If set to `undefined` then the first available address will be initially selected. If set to `null` then no address will be selected.
  - **selectedContactId**: The ID of the currently selected contact. If set to `undefined` then the first available contact will be initially selected. If set to `null` then no contact will be selected.
