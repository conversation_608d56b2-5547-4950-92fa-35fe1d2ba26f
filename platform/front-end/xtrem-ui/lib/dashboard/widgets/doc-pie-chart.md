PATH: XTREEM/Widgets+API/Pie+Chart+Widget

The pie chart widget is a widget is used to display some information in the format of a graphic pie or derivations of it or a card view table-like data. A toggle button allows the user to switch between these two views and a dropdown allows them to filter what data should be visible in the respective view.

## Example

```typescript
import * as ui from '@sage/xtrem-ui';

@ui.widgets.pieChart<PieChart>({
  title: "Pie chart",
  cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
  description: "Pie Chart",
  category: "OTHER_CATEGORY",
  content() {
    const edges = this.$.data?.xtremShowCase?.showCaseProvider?.query?.edges;
    return {
      pieChartData: this.$.options.pieChartData ?? edges?.[0]?.node?.pieChartData ?? []
    }
  },
  isDonut: true,
  hasCardView: true,
  chartLabel: '',
  tableDataDefinition: {
    title: { title: 'Some title' },
    titleRight: {
      title: 'Some Info',
      renderedAs: 'pill',
    },
    line2: {
      title: 'Some label',
    },
    line2Right: {
      decimalDigits: 2,
      title: '3243.33',
      valueFormat: 'number',
    },
  },
  locale: 'en-US',
  filterData: {
    name: 'Filter data',
    id: 'filter-data',
    options: [
      {
        value: 'category 1',
        text: 'Category 1',
      },
      {
        value: 'category 2',
        text: 'Category 2',
      },
    ],
    filterChangeCallback: e => {
      console.log('Event received ', e);
    },
  },
  callToActions: {
        doSomething: {
            title: 'Action',
            onClick() {
                this.$.showToast('Perform action on pie chart');
            },
        },
    },
  getQuery() {
    return {
      xtremShowCase: {
        showCaseProvider: {
          query: {
            edges: {
              node: {
                _id: true,
                title: true,
                titleRight: true,
                line2: true,
                line2Right: true,
                value: true,
              },
            },
          },
        },
      },
    };
  },
})
export class ContactList extends ui.widgets.PieChartWidget<{
  xtremShowCase: {
    showCaseProvider: {
      query: {
        edges: {
          node: {};
        }[];
      };
    };
  };
}> {}


```

## Properties

- **title**: A title for the widget. It is displayed under the main value and can be defined as a value or callback.
- **description**: A description for the widget. It is displayed under the main value and can be defined as a value or callback.
- **category**: A category for the widget. It is displayed above the main value and can be defined as a value or callback.
- **content**: The data that gets displayed to the user. It is expected to be an object with the following properties:
  - **address**: The address
  - **addressFunction**: The address function
  - **contactEmailAddress**: The contact's email
  - **contactImage**: The contact's image which will be used as a `src` attribute for an image element
  - **contactName**: The contact's name
  - **contactPhoneNumber**: The contact's phone number
  - **contactPosition**: The contact's position
  - **contactRole**: The contact's role
  - **contacts**: A dictionary of contacts/sites, which will be displayed in the main dropdown
  - **icon**: The contact's icon
  - **notes**: A list of notes as an array of objects with the following properties
    - **_id**: The note's ID
    - **text**: The note's text
    - **timestamp**: The note's timestamp as a Javascript `Date`
  - **numberOfAddresses**: The total number of addresses
  - **numberOfContacts**: The total number of contacts
  - **onContactAdd**: A callback function that is triggered when there are no contacts/sites and the "Add contact" button is clicked
  - **onContactTypeSwitchChanged**: A callback function that is triggered when the user navigates from the contact view to the site view or viceversa
  - **onNoteAdded**: A callback function that is triggered when a new note is added
  - **onNoteDeleted**: A callback function that is triggered when a note is deleted
  - **onNoteEdited**: A callback function that is triggered when a note is edited
  - **onSelectedContactChanged**: A callback function that is triggered when a contact/site is selected
