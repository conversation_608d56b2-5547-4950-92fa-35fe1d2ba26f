PATH: XTREEM/Client+Framework/Widgets+API

## Introduction

Widgets are front-end artifacts that can be displayed on one of the dashboards. The layout of the dashboard is configured by the end user. Users are able to choose which widgets should be presented (not yet implemented).

All widgets should be placed in its own file located in the `lib/widgets` folder.

## Workflow

The platform provides base widget types that application developers can implement. Each type has its own visual representation and set of decorator properties that determine what and how is rendered to the user. Similarly to other artifact types, widgets are also defined as decorated TypeScript classes that come with the `$` developer API property that enables developers to execute various functions.

All widgets are expected to define a `getQuery` decorator property function which returns a GraphQL query. This query is automatically executed by the framework. The query should contain all fields that are needed for rendering. The result of the query can be retried in any other property callback or method using the `data` property on the developer API object (e.g. `this.$.data`). The query result is cached by the framework according (not yet implemented) to `cacheLifespan` decorator property value, but it can be manually refreshed by the user.

## User settings (not yet implemented)

All widgets can be associated with a settings page. These pages are normal Xtreem pages and are loaded into a page dialog when the user opens the settings from the dropdown menu of the widget. The values from the fields are saved by the framework and can be retrieved using the developer API, for example settings can be used to modify the widget's query to display a different set of data.


## Widget Developer API ($)

Like other client artifact types, application developers can interact with other parts of the framework using the `$` developer API property. The list of functions is limited compared to the pages, for example developers cannot call the GraphQL API:

- **data**: The data which is fetched from the server using `getQuery()` query.
- **settings**: The values from the settings page of the widget.
- **options**: Unlike settings, widget options are not persisted when the page is reloaded. They are meant to be used for transient values such as selected items, filter and sorting options.
- **refreshWidget**: This function can be called by functional code to refresh a widget's data.
- **dialog**: [The dialog API](./Dialog+API)
- **router**: [The router API](./Router+API)
- **loader**: [The loader API](./Loader+API)
- **showToast**: [The toast API](./Toast+API)
- **storage**: [The locale storage API](./Storage+API)
- **username**: The username of the current user
- **userCode**: The userCode can be used if the application has a username that is different from the login user.

### GraphQL Mutations

Widgets can execute GraphQL mutations directly via the `$` API. The `graph` property on the `WidgetDeveloperApi` allows developers to execute mutations on any available node.

#### Example: Executing a Mutation on a Node

```ts
this.$.graph
    .node('@sage/xtrem-show-case/ShowCaseInvoice')
    .deleteById(String(invoiceId))
    .execute()
    .then(() => {
        this.$.showToast('Invoice deleted successfully');
        this.$.refreshWidget();
    })
    .catch(() => {
        this.$.showToast('Error deleting invoice', { type: 'error' });
    });
