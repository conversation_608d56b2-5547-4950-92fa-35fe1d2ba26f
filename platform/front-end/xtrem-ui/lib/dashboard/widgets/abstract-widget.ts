import type { Dashboard, Dict } from '@sage/xtrem-shared';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { VoidPromise } from '../../component/field/traits';
import { getStore } from '../../redux';
import { setWidgetOptions } from '../../redux/actions';
import { refreshWidget, type LoadMoreRowsParams } from '../../service/dashboard-service';
import { GraphQLMutationApi } from '../../service/graphql-api';
import { BaseDeveloperApi } from '../../service/screen-base';
import { DASHBOARD_SCREEN_ID } from '../../utils/constants';
import type { DashboardContextVariables } from '../dashboard-types';
import type { WidgetValueOrCallback } from './widget-utils';

export interface DashboardWidgetHeaderAction<W extends AbstractWidget> {
    onClick: (this: W) => VoidPromise;
    title: string;
    icon?: IconType;
}
export interface DashboardWidgetFooterAction<W extends AbstractWidget> {
    onClick: (this: W) => VoidPromise;
    title: string;
}

export enum WidgetCacheLifespan {
    hour = 3600000,
    day = 86400000,
    noCache = -1,
}

export interface CallToActionDefinition<W extends AbstractWidget> {
    title: WidgetValueOrCallback<W, string>;
    isDisabled?: WidgetValueOrCallback<W, boolean>;
    isHidden?: WidgetValueOrCallback<W, boolean>;
    onClick: (this: W) => VoidPromise;
}

export interface WithContent<W extends AbstractWidget, C = any> {
    content?: WidgetValueOrCallback<W, C[]>;
}
export interface AbstractWidgetDecorator<W extends AbstractWidget> {
    /** Title of the widget */
    title: WidgetValueOrCallback<W, string>;

    /** Default category that is used to organize the widgets in groups on the left list, it is passed onto the dashboard manager which provides a user friendly title. */
    category?: string;

    /** Description of the widget */
    description?: string;

    /** The widget group that the widget is available for the user to use. If not specified, the widget is available on the home page. */
    group?: string;

    /** GraphQL query that is used to fetch data for the widget. */
    getQuery: (this: W, queryParams?: LoadMoreRowsParams) => string | object;

    headerActions?: DashboardWidgetHeaderAction<W>[];

    footerActions?: DashboardWidgetFooterAction<W>[];

    /**
     * Used to cache the query result of the widget, defaults to a day
     */
    cacheLifespan?: WidgetCacheLifespan;

    /**
     * An xtrem page url where the end user can define settings for a given widget
     */
    settingsPage?: string;

    callToActions?: Dict<CallToActionDefinition<W>>;
}

export enum WidgetType {
    barChart = 'barChart',
    contactCard = 'contactCard',
    gauge = 'gauge',
    indicatorTile = 'indicatorTile',
    indicatorTileGroup = 'indicatorTileGroup',
    lineChart = 'lineChart',
    pieChart = 'pieChart',
    staticContent = 'staticContent',
    table = 'table',
    visualProcess = 'visualProcess',
}

export interface WidgetDefinition<WidgetOptions extends Record<string, any> = {}> {
    _id: string;

    properties: AbstractWidgetDecorator<any>;

    artifactName: string;

    widgetObject: AbstractWidget<any, WidgetOptions>;

    data?: any;

    widgetType: WidgetType;

    /**
     * Unlike settings, the widget options are not saved, it's only for the duration of the page load. These contain things such as selected items, filter and sorting options
     */
    options?: WidgetOptions;
}

export class WidgetDeveloperApi<
    T = any,
    WidgetOptions extends Record<string, any> = {},
    TGraphApi = any,
> extends BaseDeveloperApi<TGraphApi, any> {
    readonly graph: GraphQLMutationApi<TGraphApi>;

    constructor(
        private readonly parentWidget: AbstractWidget<T, any>,
        private readonly widgetId: string,
        private readonly dashboardId: string,
        private readonly group: string,
        private readonly _contextVariables?: DashboardContextVariables,
    ) {
        super(DASHBOARD_SCREEN_ID);
        this.graph = new GraphQLMutationApi<TGraphApi>();
    }

    refreshWidget(): void {
        refreshWidget(this.widgetId, this.group);
    }

    get data(): T | null {
        if (this.parentWidget.constructor.prototype.__data) {
            return this.parentWidget.constructor.prototype.__data;
        }
        const state = getStore().getState();
        const widgetDefinition = state.dashboard.dashboardGroups[this.group].widgets[this.widgetId];
        return widgetDefinition?.data || null;
    }

    get contextVariables(): DashboardContextVariables | null {
        return this._contextVariables ? Object.freeze(this._contextVariables) : null;
    }

    get settings(): any {
        const state = getStore().getState();

        // If the dashboard editor is open, we use the settings from the working copy of the dashboard editor
        const dashboardDefinition: Dashboard =
            state.dashboard.dashboardGroups[this.group].dashboardEditor.isOpen &&
            state.dashboard.dashboardGroups[this.group].dashboardEditor.currentDashboardDefinition
                ? state.dashboard.dashboardGroups[this.group].dashboardEditor.currentDashboardDefinition
                : state.dashboard.dashboardGroups[this.group].dashboards?.[this.dashboardId];
        const widgetConfiguration = dashboardDefinition?.children?.find(c => String(c._id) === String(this.widgetId));

        return widgetConfiguration?.settings || {};
    }

    get options(): WidgetOptions | Partial<WidgetOptions> {
        const state = getStore().getState();
        const widgetDefinition = state.dashboard.dashboardGroups[this.group].widgets[this.widgetId];
        return widgetDefinition.options || ({} as Partial<WidgetOptions>);
    }

    set options(newOptions: WidgetOptions) {
        const dashboardId = this.dashboardId;
        const widgetId = this.widgetId;
        const group = this.group;
        getStore().dispatch(setWidgetOptions(dashboardId, widgetId, group, { ...newOptions }));
    }
}

export abstract class AbstractWidget<T = any, WidgetOptions extends Record<string, any> = {}, TGraphApi = any> {
    private readonly _$: WidgetDeveloperApi<T, WidgetOptions, TGraphApi>;

    constructor() {
        const widgetId = this.constructor.prototype.__id;
        const dashboardId = this.constructor.prototype.__dashboardId;
        const group = this.constructor.prototype.__group;
        const contextVariables = Object.freeze(this.constructor.prototype.__contextVariables);
        this._$ = new WidgetDeveloperApi<T, WidgetOptions>(this, widgetId, dashboardId, group, contextVariables);
    }

    get $(): WidgetDeveloperApi<T, WidgetOptions, TGraphApi> {
        return this._$;
    }
}
