import { AbstractWidget } from './abstract-widget';
import type { ContactCardWidgetOptions } from './contact-card-widget-decorator';
import type { PieChartWidgetOptions } from './pie-chart-widget-decorator';
import type { TableWidgetOptions } from './table-widget-decorator';

export { AbstractWidget, WidgetCacheLifespan } from './abstract-widget';
export { barChart } from './bar-chart-widget-decorator';
export {
    contactCard,
    type ContactCardWidgetContent,
    type ContactCardWidgetOptions,
    type ContactCardWidgetProperties,
} from './contact-card-widget-decorator';
export { gauge } from './gauge-widget-decorator';
export { indicatorTileGroup } from './indicator-tile-group-widget-decorator';
export { indicatorTile } from './indicator-tile-widget-decorator';
export { lineChart } from './line-chart-widget-decorator';
export { pieChart, type PieChartWidgetProperties } from './pie-chart-widget-decorator';
export { staticContent } from './static-content-widget-decorator';
export { table } from './table-widget-decorator';
export { visualProcess } from './visual-process-widget-decorator';

export class ContactCardWidget<T = any> extends AbstractWidget<T, ContactCardWidgetOptions> {}
export class TableWidget<T = any, TGraphApi = any> extends AbstractWidget<T, TableWidgetOptions, TGraphApi> {}
export class PieChartWidget<T = any> extends AbstractWidget<T, PieChartWidgetOptions> {}
