import type { Dict } from '@sage/xtrem-shared';
import type { Constructible } from '../../types';
import type { AbstractWidget, AbstractWidgetDecorator, WithContent } from './abstract-widget';
import { WidgetType } from './abstract-widget';
import type { ChartProps, ExtractWidgetDataType } from './chart-types';
import { standardWidgetDecorator } from './widget-utils';

export interface LineChartWidgetProperties<W extends AbstractWidget, C = any, P = any, S = any>
    extends AbstractWidgetDecorator<W>,
        WithContent<W, C>,
        ChartProps<W, C, P, S> {
    dataOptions?: Dict<Dict<string>>;
}

// A widget that displays a line chart
export function lineChart<W extends AbstractWidget, C = any, P = any, S = number>(
    properties: LineChartWidgetProperties<W, C, P, S>,
): (ctor: Constructible<AbstractWidget<ExtractWidgetDataType<W>>>) => void {
    return standardWidgetDecorator(properties, WidgetType.lineChart);
}
