const dashboardComponentMock = jest.fn();
jest.mock('../async-drag-drop-canvas-wrapper', () => ({
    __esModule: true,
    default: (props: any) => {
        dashboardComponentMock(props);
        return <div data-testid="bms-dashboard">BMS dashboard placeholder</div>;
    },
}));

import { getMockState, getMockStore } from '../../__tests__/test-helpers';

import * as actions from '../../redux/actions';
import * as React from 'react';
import { act, render, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { ConnectedDashboardComponent } from '../dashboard-component';
import * as dashboardService from '../../service/dashboard-service';
import { WidgetType } from '../widgets/abstract-widget';

const testDashboardGroup = 'home';

describe('dashboard component', () => {
    let mockStore;
    beforeEach(() => {
        dashboardComponentMock.mockClear();
        jest.spyOn(dashboardService, 'getWidgetsFromDashboardItems').mockReturnValue([
            {
                i: 'fakeWidgetDefinition',
                xxs: { x: 0, y: 0, w: 0, h: 0 },
                xs: { x: 0, y: 0, w: 0, h: 0 },
                sm: { x: 0, y: 0, w: 0, h: 0 },
                md: { x: 0, y: 0, w: 0, h: 0 },
                lg: { x: 0, y: 0, w: 0, h: 0 },
                data: 0,
                type: 'basic' as any,
            },
        ]);
        jest.spyOn(dashboardService, 'fetchDashboardTemplates').mockResolvedValue([
            {
                _id: 'testTemplate',
                title: 'Test Template',
                description: 'Test Description',
                listIcon: 'cpu',
            },
        ]);
        const mockState = getMockState();
        mockState.dashboard.canEditDashboards = false;
        mockStore = getMockStore();
        jest.useFakeTimers();
    });
    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('empty dashboard', () => {
        it('should render the component and trigger loading if the dashboards are not loaded yet', () => {
            expect(actions.fetchUserDashboardDefinition).not.toHaveBeenCalled();
            expect(dashboardComponentMock).not.toHaveBeenCalled();
            render(
                <Provider store={mockStore}>
                    <ConnectedDashboardComponent group={testDashboardGroup} />
                </Provider>,
            );
            expect(actions.fetchUserDashboardDefinition).toHaveBeenCalled();
            expect(dashboardComponentMock).not.toHaveBeenCalled();
        });

        it('should not render the "create dashboard" button if user not authorized', async () => {
            const mockState = getMockState();
            mockState.dashboard.canEditDashboards = false;
            mockStore = getMockStore(mockState);

            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardComponent group={testDashboardGroup} />
                </Provider>,
            );
            await act(async () => {
                jest.advanceTimersByTime(100);
            });

            await waitFor(() => {
                expect(queryByTestId('e-dashboard-create')).not.toBeInTheDocument();
            });
        });

        it('should render the "create dashboard" button if user authorized', async () => {
            const mockState = getMockState();
            mockState.dashboard.canEditDashboards = true;
            mockStore = getMockStore(mockState);

            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardComponent group={testDashboardGroup} />
                </Provider>,
            );
            await act(async () => {
                jest.advanceTimersByTime(100);
            });

            await waitFor(() => {
                expect(queryByTestId('e-dashboard-create')).toBeInTheDocument();
            });
        });
    });

    describe('dashboard with data', () => {
        const setupMockState = (canEditDashboards = true) => {
            const mockState = getMockState();
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'] = {
                _id: '12',
                title: 'A fake dashboard',
                isSelected: true,
                children: [
                    {
                        _id: '3',
                        positions: [
                            { x: 0, y: 0, breakpoint: 'xxs', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'xs', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'sm', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'md', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'lg', w: 0, h: 0 },
                        ],
                        type: 'indicator tile',
                        settings: {},
                    },
                ],
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = {
                '3': {
                    _id: '3',
                    artifactName: '@sage/xtrem-test/MyFirstWidget',
                    data: {},
                    properties: {
                        title: 'Test widget',
                        getQuery: () => ({ fake: { graphql: { query: true } } }),
                    },
                    widgetType: WidgetType.staticContent,
                    widgetObject: {} as any,
                },
            };
            mockState.translations = {
                'en-US': {
                    '@sage/xtrem-ui/action-edit': 'patata',
                },
            };
            mockState.dashboard.canEditDashboards = canEditDashboards;
            return mockState;
        };
        beforeEach(() => {
            const mockState = setupMockState();
            mockStore = getMockStore(mockState);
        });

        it('should not display the loader if the widget and dashboard definition is loaded to the state', async () => {
            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardComponent group={testDashboardGroup} />
                </Provider>,
            );

            await waitFor(() => {
                expect(wrapper.baseElement.querySelector('[data-component="loader"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-testid="bms-dashboard"]')).not.toBeNull();
            });
        });

        it('should pass on the correct arguments to the dashboard library', async () => {
            expect(dashboardComponentMock).not.toHaveBeenCalled();

            render(
                <Provider store={mockStore}>
                    <ConnectedDashboardComponent group={testDashboardGroup} />
                </Provider>,
            );

            await waitFor(() => {
                expect(dashboardComponentMock).toHaveBeenCalledTimes(1);
            });

            expect(dashboardComponentMock).toHaveBeenCalledWith({
                json: {
                    isHeaderHidden: true,
                    onBreakpointChange: expect.any(Function),
                    locale: 'en-US',
                    isEditMode: false,
                    type: 'responsive',
                    layout: {
                        cols: { xxs: 2, xs: 4, sm: 6, md: 8, lg: 12 },
                        breakpoints: {
                            lg: 1920,
                            md: 1259,
                            sm: 959,
                            xs: 703,
                            xxs: 375,
                        },
                        isDraggable: false,
                        rowHeight: 120,
                        width: 1200,
                    },
                    stringLiterals: expect.anything(),
                    widgets: [
                        {
                            data: 0,
                            i: 'fakeWidgetDefinition',
                            type: 'basic',
                            xxs: { x: 0, y: 0, w: 0, h: 0 },
                            xs: { x: 0, y: 0, w: 0, h: 0 },
                            sm: { x: 0, y: 0, w: 0, h: 0 },
                            md: { x: 0, y: 0, w: 0, h: 0 },
                            lg: { x: 0, y: 0, w: 0, h: 0 },
                        },
                    ],
                },
            });
        });

        it('should pass on the correct string literals', async () => {
            render(
                <Provider store={mockStore}>
                    <ConnectedDashboardComponent group={testDashboardGroup} />
                </Provider>,
            );

            await waitFor(() => {
                expect(dashboardComponentMock).toHaveBeenCalledTimes(1);
            });

            expect(dashboardComponentMock).toHaveBeenCalledWith({
                json: {
                    isHeaderHidden: true,
                    onBreakpointChange: expect.any(Function),
                    locale: 'en-US',
                    type: 'responsive',
                    layout: {
                        cols: { xxs: 2, xs: 4, sm: 6, md: 8, lg: 12 },
                        breakpoints: {
                            lg: 1920,
                            md: 1259,
                            sm: 959,
                            xs: 703,
                            xxs: 375,
                        },
                        isDraggable: false,
                        rowHeight: 120,
                        width: 1200,
                    },
                    widgets: expect.anything(),
                    isEditMode: false,
                    stringLiterals: expect.objectContaining({
                        action_edit: 'patata',
                        action_clone: 'Clone',
                        action_close: 'Close',
                        action_delete: 'Delete',
                        action_share: 'Share',
                        dashboard_settings: '',
                        general_finish_editing: 'Finish editing',
                        general_invalid_json: 'Invalid Json',
                        general_welcome_page: 'Welcome page',
                        no_change: 'No change',
                        table_compare_with_previous_period: 'Compare with previous period',
                        table_select_all: 'Select all',
                        table_switch_to_card_view: 'Switch to card view',
                        table_switch_to_table_view: 'Switch to table view',
                        table_variant_area: 'Area View',
                        table_variant_card: 'Card View',
                        table_variant_table: 'Table View',
                    }),
                },
            });
        });

        it('should not render the "add widget" button if user not authorized', async () => {
            const mockState = setupMockState(false);
            const mockStore = getMockStore(mockState);
            jest.spyOn(dashboardService, 'getWidgetsFromDashboardItems').mockReturnValue([]);
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardComponent group={testDashboardGroup} />
                </Provider>,
            );
            await act(async () => {
                jest.advanceTimersByTime(100);
            });

            await waitFor(() => {
                expect(queryByTestId('e-dashboard-create')).not.toBeInTheDocument();
            });
            jest.restoreAllMocks();
        });

        it('should render the "add widget" button if user authorized', async () => {
            const mockState = setupMockState(true);
            const mockStore = getMockStore(mockState);
            jest.spyOn(dashboardService, 'getWidgetsFromDashboardItems').mockReturnValue([]);
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardComponent group={testDashboardGroup} />
                </Provider>,
            );
            await act(async () => {
                jest.advanceTimersByTime(100);
            });

            await waitFor(() => {
                expect(queryByTestId('e-dashboard-create')).toBeInTheDocument();
            });
            jest.restoreAllMocks();
        });

        it('should not render the actions menu if user not authorized', async () => {
            const mockState = setupMockState(false);
            const mockStore = getMockStore(mockState);
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardComponent group={testDashboardGroup} />
                </Provider>,
            );
            await act(async () => {
                jest.advanceTimersByTime(100);
            });
            await waitFor(() => {
                expect(queryByTestId('e-dashboard-actions')).not.toBeInTheDocument();
            });
        });

        it('should render the actions menu if user authorized', async () => {
            const mockState = setupMockState(true);
            const mockStore = getMockStore(mockState);
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardComponent group={testDashboardGroup} />
                </Provider>,
            );
            await act(async () => {
                jest.advanceTimersByTime(100);
            });
            await waitFor(() => {
                expect(queryByTestId('e-dashboard-actions')).toBeInTheDocument();
            });
        });
    });
});
