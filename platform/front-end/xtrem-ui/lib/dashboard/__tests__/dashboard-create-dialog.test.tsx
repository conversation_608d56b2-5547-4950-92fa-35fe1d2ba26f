import { render, waitFor } from '@testing-library/react';
import React from 'react';
import type { DashboardCreateDialogProps } from '../dashboard-create-dialog';
import { DashboardCreateDialog } from '../dashboard-create-dialog';
import * as dashboardService from '../../service/dashboard-service';

describe('dashboard create dialog', () => {
    let componentProps: DashboardCreateDialogProps;
    beforeEach(() => {
        jest.resetAllMocks();
        componentProps = {
            group: 'home',
            isOpen: true,
            dialogSize: 'extra-large',
            onClose: jest.fn(),
            onNextClicked: jest.fn(),
            createDashboard: jest.fn().mockResolvedValue({}),
            cloneDashboard: jest.fn().mockResolvedValue({}),
        };
        jest.spyOn(dashboardService, 'fetchDashboardTemplates').mockResolvedValue([
            {
                _id: 'testTemplate',
                title: 'Test Template',
                description: 'Test Description',
                listIcon: 'cpu',
            },
        ]);
    });

    it('should render the component', () => {
        const wrapper = render(<DashboardCreateDialog {...componentProps} />);
        expect(wrapper.getByTestId('e-dashboard-dialog-create-container')).not.toBeNull();
    });

    it('should NOT render the component', () => {
        componentProps.isOpen = false;
        const wrapper = render(<DashboardCreateDialog {...componentProps} />);
        expect(wrapper.queryByTestId('e-dashboard-dialog-create-container')).toBeNull();
    });
    it('should call onClose when clicking on cancel button', () => {
        const wrapper = render(<DashboardCreateDialog {...componentProps} />);
        wrapper.getByTestId('e-dashboard-dialog-create-cancel-button').click();
        expect(componentProps.onClose).toHaveBeenCalled();
    });

    it('next button should be disabled because no template has been selected', () => {
        const wrapper = render(<DashboardCreateDialog {...componentProps} />);
        expect(wrapper.getByTestId('e-dashboard-dialog-create-next-button')).toHaveAttribute('disabled');
    });

    it('Should set blank template as selected after clicking on it', async () => {
        const wrapper = render(<DashboardCreateDialog {...componentProps} />);
        wrapper.getByTestId('e-selection-card-_blank').click();
        await waitFor(() => {
            expect(wrapper.getByTestId('e-selection-card-_blank')).toHaveClass('e-selection-card-selected');
        });
    });

    it('Should unset blank template as selected after clicking on it twice', async () => {
        const wrapper = render(<DashboardCreateDialog {...componentProps} />);
        wrapper.getByTestId('e-selection-card-_blank').click();
        await waitFor(() => {
            expect(wrapper.getByTestId('e-selection-card-_blank')).toHaveClass('e-selection-card-selected');
        });
        wrapper.getByTestId('e-selection-card-_blank').click();
        await waitFor(() => {
            expect(wrapper.getByTestId('e-selection-card-_blank')).not.toHaveClass('e-selection-card-selected');
        });
    });

    it('Should call createDashboard after clicking on blank template and clicking on next button', async () => {
        const wrapper = render(<DashboardCreateDialog {...componentProps} />);
        (await wrapper.findByTestId('e-selection-card-_blank')).click();
        (await wrapper.findByTestId('e-dashboard-dialog-create-next-button')).click();
        await waitFor(() => {
            expect(componentProps.createDashboard).toHaveBeenCalled();
        });
    });

    it('Should render additional templates after fetching it from the server', async () => {
        const wrapper = render(<DashboardCreateDialog {...componentProps} />);
        await waitFor(() => {
            expect(wrapper.getByTestId('e-selection-card-testTemplate')).not.toBeNull();
        });
        expect(wrapper.getByText('Test Template')).not.toBeNull();
        expect(wrapper.getByText('Test Description')).not.toBeNull();
    });

    it('Should call cloneDashboard after clicking on a template and clicking on next button', async () => {
        const wrapper = render(<DashboardCreateDialog {...componentProps} />);
        await waitFor(() => {
            expect(wrapper.getByTestId('e-selection-card-testTemplate')).not.toBeNull();
        });
        (await wrapper.findByTestId('e-selection-card-testTemplate')).click();
        (await wrapper.findByTestId('e-dashboard-dialog-create-next-button')).click();
        await waitFor(() => {
            expect(componentProps.cloneDashboard).toHaveBeenCalledWith('testTemplate');
        });
    });
});
