import { getMockState, getMockStore } from '../../__tests__/test-helpers';
import * as React from 'react';
import { Provider } from 'react-redux';
import { fireEvent, render, waitFor } from '@testing-library/react';
import { ConnectedDashboardSelectionTabs, DashboardSelectionTabs } from '../dashboard-selection-tabs';

const testDashboardGroup = 'home';

describe('Dashboard selection tabs component', () => {
    let mockStore;

    describe('Empty availableDashboards', () => {
        beforeEach(() => {
            mockStore = getMockStore();
            const mockState = getMockState();
            mockState.dashboard.dashboardGroups[testDashboardGroup].availableDashboards = [];
            mockStore = getMockStore(mockState);
        });
        it('should not render the component', async () => {
            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardSelectionTabs group={testDashboardGroup} />
                </Provider>,
            );

            await waitFor(() => {
                expect(wrapper.baseElement.querySelector('.e-dashboard-selection-tab-container')).toBeNull();
            });
        });
    });

    describe('Has availableDashboards', () => {
        beforeEach(() => {
            mockStore = getMockStore();
            const mockState = getMockState();
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'] = {
                _id: '12',
                title: 'A fake dashboard',
                isSelected: true,
                children: [
                    {
                        _id: '3',
                        positions: [
                            { x: 0, y: 0, breakpoint: 'xxs', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'xs', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'sm', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'md', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'lg', w: 0, h: 0 },
                        ],
                        type: 'indicator tile',
                        settings: {},
                    },
                ],
            };
            mockState.dashboard.dashboardGroups[testDashboardGroup].availableDashboards = [
                {
                    _id: 'testId',
                    title: 'Test Title',
                },
            ];
            mockState.dashboard.canEditDashboards = false;
            mockStore = getMockStore(mockState);
        });

        it('should render the component', async () => {
            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardSelectionTabs group={testDashboardGroup} />
                </Provider>,
            );

            await waitFor(() => {
                expect(wrapper.baseElement.querySelector('.e-dashboard-selection-tab-container')).not.toBeNull();
            });
        });

        it('should not render the "create" tab if user not authorized', async () => {
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardSelectionTabs group={testDashboardGroup} />
                </Provider>,
            );

            await waitFor(() => {
                expect(queryByTestId('e-xtrem-tab-create')).toBeNull();
            });
        });
    });

    describe('Interactions', () => {
        let setSelectedDashboard: jest.Mock;

        beforeEach(() => {
            mockStore = getMockStore();
            const mockState = getMockState();
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'] = {
                _id: '12',
                title: 'A fake dashboard',
                isSelected: true,
                children: [
                    {
                        _id: '3',
                        positions: [
                            { x: 0, y: 0, breakpoint: 'xxs', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'xs', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'sm', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'md', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'lg', w: 0, h: 0 },
                        ],
                        type: 'indicator tile',
                        settings: {},
                    },
                ],
            };
            mockState.dashboard.dashboardGroups[testDashboardGroup].availableDashboards = [
                {
                    _id: 'testId',
                    title: 'Test Title',
                },
            ];
            mockStore = getMockStore(mockState);
            setSelectedDashboard = jest.fn();
        });

        it('should trigger setSelectedDashboard action with the right id when click on a tab', async () => {
            const result = render(
                <DashboardSelectionTabs
                    availableDashboards={[
                        {
                            _id: 'testId',
                            title: 'Test Title',
                        },
                    ]}
                    setSelectedDashboard={setSelectedDashboard}
                    group={testDashboardGroup}
                />,
            );

            fireEvent.click(result.queryByTestId('e-xtrem-tab-testTitle', { exact: false })!);

            await waitFor(() => {
                expect(setSelectedDashboard).toHaveBeenCalledTimes(1);
                expect(setSelectedDashboard).toHaveBeenCalledWith('testId');
            });
        });
    });
});
