const dashboardComponentMock = jest.fn();
jest.mock('../async-drag-drop-canvas-wrapper', () => ({
    __esModule: true,
    default: (props: any) => {
        dashboardComponentMock(props);
        return <div data-testid="bms-dashboard">BMS dashboard placeholder</div>;
    },
}));

import { applyActionMocks, getMockState, getMockStore } from '../../__tests__/test-helpers';
import { render, fireEvent, waitFor } from '@testing-library/react';
import React from 'react';
import { ConnectedDashboardEditorDialog } from '../dashboard-editor-dialog';
import { createDashboardPageDefinition } from '../../redux/actions/dashboard-editor-actions';
import type { XtremUiWidget } from '../../service/dashboard-service';
import { DASHBOARD_SCREEN_ID } from '../../utils/constants';
import { cloneDeep } from 'lodash';
import type { XtremAppState } from '../../redux';
import { Provider } from 'react-redux';
import * as i18n from '../../service/i18n-service';
import * as xtremRedux from '../../redux';
import { triggerNestedFieldEvent } from '../../utils/events';
import '@testing-library/jest-dom';

const testDashboardGroup = 'home';

const widgets: XtremUiWidget[] = [
    {
        category: { _id: '_OTHERS', title: 'Others', sortValue: 9007199254740991 },
        description: '',
        title: 'Ratio of administrators',
        type: 'gauge',
        _id: '@sage/xtrem-show-case/AdministratorGauge',
    },
    {
        category: { _id: 'DEMO_CATEGORY', title: 'My demo category', sortValue: 1 },
        description: 'Detailed list about the current users',
        title: 'Users',
        type: 'table',
        _id: '@sage/xtrem-show-case/ListOfUsersTable',
    },
];

describe('dashboard editor dialog', () => {
    let mockStore;
    let mockState: XtremAppState;

    beforeEach(() => {
        dashboardComponentMock.mockClear();
        jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);

        mockState = getMockState();
        mockStore = getMockStore(mockState);
        mockState.screenDefinitions[DASHBOARD_SCREEN_ID] = createDashboardPageDefinition(
            mockStore.dispatch,
            testDashboardGroup,
            cloneDeep(widgets),
            [],
            undefined,
            {},
            'en-US',
        );
        mockState.screenDefinitions[DASHBOARD_SCREEN_ID].selectedRecordId = '-1';
        mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards = {
            '1234': {
                _id: '1234',
                isSelected: true,
                children: [],
                title: 'My dashboard title',
            },
        };
        mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.currentDashboardDefinition = {
            _id: '1234',
            children: [],
            title: 'My dashboard title',
        };
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    it('should render closed if the dashboard is closed', () => {
        mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.isOpen = false;
        mockStore = getMockStore(mockState);
        const { baseElement } = render(
            <Provider store={mockStore}>
                <ConnectedDashboardEditorDialog group={testDashboardGroup} />
            </Provider>,
        );
        expect(baseElement.querySelector('[data-component="dialog-full-screen"]')!.getAttribute('data-state')).toEqual(
            'closed',
        );
    });

    it('should render main components if the dashboard is open', () => {
        mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.isOpen = true;
        mockStore = getMockStore(mockState);
        const { baseElement, queryByTestId } = render(
            <Provider store={mockStore}>
                <ConnectedDashboardEditorDialog group={testDashboardGroup} />
            </Provider>,
        );
        expect(baseElement.querySelector('[data-component="dialog-full-screen"]')!.getAttribute('data-state')).toEqual(
            'open',
        );
        expect(queryByTestId('e-dashboard-editor-title')).toHaveTextContent('My dashboard title');
        expect(queryByTestId('bms-dashboard')).not.toBeNull();
    });

    describe('save button', () => {
        beforeEach(() => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.isOpen = true;
        });

        it('should enable save button when the editor is dirty', async () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.isDirty = true;
            mockStore = getMockStore(mockState);
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
            await waitFor(() => {
                expect(queryByTestId('e-dashboard-editor-dialog-save')).not.toBeNull();
            });
            expect(queryByTestId('e-dashboard-editor-dialog-save')!.hasAttribute('disabled')).toEqual(false);
        });

        it('should disable save button when the editor is not dirty', async () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.isDirty = false;
            mockStore = getMockStore(mockState);
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
            await waitFor(() => {
                expect(queryByTestId('e-dashboard-editor-dialog-save')).not.toBeNull();
            });
            expect(queryByTestId('e-dashboard-editor-dialog-save')!.hasAttribute('disabled')).toEqual(true);
        });

        it('should save dashboard when the save button is clicked', async () => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.isDirty = true;
            mockStore = getMockStore(mockState);
            expect(xtremRedux.actions.saveDashboardEditorState).not.toHaveBeenCalled();
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
            await waitFor(() => {
                expect(queryByTestId('e-dashboard-editor-dialog-save')).not.toBeNull();
            });
            fireEvent.click(queryByTestId('e-dashboard-editor-dialog-save')!);
            await waitFor(() => {
                expect(xtremRedux.actions.saveDashboardEditorState).toHaveBeenCalled();
            });
        });
    });

    describe('cancel / close button', () => {
        beforeEach(() => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.isOpen = true;
            mockStore = getMockStore(mockState);
        });

        it('should close the dialog if cancel button is clicked', async () => {
            expect(xtremRedux.actions.closeDashboardEditorDialog).not.toHaveBeenCalled();
            const { queryByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
            await waitFor(() => {
                expect(queryByTestId('e-dashboard-editor-dialog-cancel')).not.toBeNull();
            });
            fireEvent.click(queryByTestId('e-dashboard-editor-dialog-cancel')!);
            await waitFor(() => {
                expect(xtremRedux.actions.closeDashboardEditorDialog).toHaveBeenCalled();
            });
        });

        it('should close the dialog if close button is clicked', async () => {
            expect(xtremRedux.actions.closeDashboardEditorDialog).not.toHaveBeenCalled();
            const { baseElement } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
            await waitFor(() => {
                expect(baseElement.querySelector('[data-element="close"]')).not.toBeNull();
            });
            fireEvent.click(baseElement.querySelector('[data-element="close"]')!);
            await waitFor(() => {
                expect(xtremRedux.actions.closeDashboardEditorDialog).toHaveBeenCalled();
            });
        });
    });

    describe('widget list', () => {
        beforeEach(() => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboardEditor.isOpen = true;
            mockStore = getMockStore(mockState);
        });

        it('should render widget list', async () => {
            const { queryByTestId, queryAllByTestId } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
            await waitFor(() => {
                expect(queryByTestId('e-table-field-mobile-rows')).not.toBeNull();
            });

            const cards = queryAllByTestId('e-field-bind-title', { exact: false });
            expect(cards).toHaveLength(2);
        });

        it('should trigger action on clicking the add link', async () => {
            const { queryByTestId, baseElement } = render(
                <Provider store={mockStore}>
                    <ConnectedDashboardEditorDialog group={testDashboardGroup} />
                </Provider>,
            );
            await waitFor(() => {
                expect(queryByTestId('e-table-field-mobile-rows')).not.toBeNull();
            });

            const addButton = baseElement.querySelectorAll('.e-field-nested .e-link-field a')[0];
            expect(triggerNestedFieldEvent).not.toHaveBeenCalled();
            fireEvent.click(addButton);
            await waitFor(() => {
                expect(triggerNestedFieldEvent).toHaveBeenCalled();
            });
        });
    });
});
