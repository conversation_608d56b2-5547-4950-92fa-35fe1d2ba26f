@import '~@sage/bms-dashboard/bms-dashboard.css';

.e-dashboard {
    width: 100%;
    background: var(--colorsUtilityMajor025);
    box-sizing: border-box;
    padding-top: 49px;
    height: 100%;
    overflow-y: auto;
    position: relative;

    >h1 {
        display: flex;
    }

    #dashboardWrapper {
        padding-bottom: 40px;
    }

    th,
    td {
        text-align: left;
    }

    .e-dashboard-title {
        display: inline-block;
        flex: 1;
    }

    .e-dashboard-title-menu {
        display: inline-block;
    }

    .db-widget-skeleton .db-widget-type-preview {
        overflow: hidden;
    }

    .e-dashboard-empty {
        display: flex;
        flex-direction: column;
        height: 100%;

        &>div[data-component="heading"] {
            box-sizing: border-box;
            margin: 0;
            padding-top: 25px;
            padding-left: 50px;
        }
    }

    .e-dashboard-loading {
        display: flex;
        height: 100%;
        align-items: center;
        justify-content: center;
    }

    .e-dashboard-empty-center-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        row-gap: 50px;
        column-gap: 100px;
        flex: 1;
        flex-wrap: wrap;
        align-content: flex-start;
        margin-top: 120px;
    }

    .e-dashboard-empty-right-subtitle {
        font-family: var(--fontFamiliesDefault);
        font-style: normal;
        font-weight: 400;
        font-size: var(--fontSizes200);
    }

    .e-dashboard-empty-right-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        gap: 16px;
    }

    .e-dashboard-selection-tab-container {
        width: 100%;
        padding-left: 24px;
        padding-top: 24px;
        box-sizing: border-box;

        [role="tablist"] {
            width: 100%;
            margin-bottom: 0;
            overflow-x: auto;

            button {
                flex: 1;
                text-align: center;
                font-size: 14px;
                font-family: $fontAdelle;

                div {
                    padding: 0;
                }
            }

        }

        &>.e-xtrem-tabs {
            // Negative margin to account for possible right scrollbar when page overflows vertically
            margin-right: calc(-1 * (100vw - 100% - 115px));

            @include extra_small {
                margin-right: calc(-1 * (100vw - 100% - 52px));
            }
        }
    }
}

.db-table-table-numeric-column .db-table-table-cell-wrapper {
    display: block
}