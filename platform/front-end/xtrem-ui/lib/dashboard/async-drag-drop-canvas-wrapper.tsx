import * as React from 'react';
import type { DragDropCanvasProps } from '@sage/bms-dashboard';
import { DashboardPlaceholder } from './dashboard-placeholder';

const DragDropCanvasWrapper = React.lazy(() => import('@sage/bms-dashboard'));

export function AsyncDragDropCanvasWrapper(props: {
    json: Extract<DragDropCanvasProps, { type: 'responsive' }>;
}): React.ReactElement {
    return (
        <React.Suspense fallback={<DashboardPlaceholder isTitlePlaceholderHidden={true} />}>
            <DragDropCanvasWrapper {...props} />
        </React.Suspense>
    );
}

export default AsyncDragDropCanvasWrapper;
