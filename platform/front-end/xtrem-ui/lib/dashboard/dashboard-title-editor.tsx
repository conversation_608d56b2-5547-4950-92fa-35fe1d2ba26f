import * as React from 'react';
import { localize } from '../service/i18n-service';
import Typography from 'carbon-react/esm/components/typography';
import IconButton from 'carbon-react/esm/components/icon-button';
import Icon from 'carbon-react/esm/components/icon';
import Textbox from 'carbon-react/esm/components/textbox';

export interface DashboardTitleEditorProps {
    title: string;
    onChange: (newTitle: string) => void;
}
export function DashboardTitleEditor({ title, onChange }: DashboardTitleEditorProps): React.ReactElement {
    const [currentTitle, setCurrentTitle] = React.useState<string>(title);
    const [isEditing, setEditing] = React.useState(false);

    React.useEffect(() => {
        setCurrentTitle(title);
    }, [title]);

    const onKeyDown = React.useCallback(
        (ev: React.KeyboardEvent<HTMLInputElement>) => {
            if (ev.key === 'Enter') {
                onChange(currentTitle);
                setEditing(false);
            }
        },
        [onChange, currentTitle],
    );

    return (
        <Typography variant="h1" mt={3} mr={3} ml={3} mb={0} fontSize="24px">
            {!isEditing && (
                <span className="e-dashboard-editor-title" data-testid="e-dashboard-editor-title">
                    {currentTitle}
                </span>
            )}
            {isEditing && (
                <Textbox
                    data-testid="e-dashboard-editor-title"
                    value={currentTitle}
                    onChange={(ev: React.ChangeEvent<HTMLInputElement>): void => setCurrentTitle(ev.target.value)}
                    onKeyDown={onKeyDown}
                    maxLength={80}
                />
            )}
            {!isEditing && (
                <IconButton
                    data-testid="e-dashboard-editor-title-edit"
                    onClick={(): void => setEditing(true)}
                    aria-label={localize('@sage/xtrem-ui/dashboard-editor-edit-title', 'Edit title')}
                >
                    <Icon
                        tooltipMessage={localize('@sage/xtrem-ui/dashboard-editor-edit-title', 'Edit title')}
                        type="edit"
                    />
                </IconButton>
            )}
        </Typography>
    );
}
