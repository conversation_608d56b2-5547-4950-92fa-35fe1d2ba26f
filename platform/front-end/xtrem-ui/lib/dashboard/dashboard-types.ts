import type { Dashboard } from '@sage/xtrem-shared';
import type { WidgetDefinition } from './widgets/abstract-widget';

export interface UiDashboard extends Dashboard {
    isSelected?: boolean;
}

export interface DashboardContextVariables {
    recordId: string;
}

export type DashboardBreakpoint = 'xxs' | 'xs' | 'sm' | 'md' | 'lg';

export type OnBreakpointChange = (newBreakpoint: DashboardBreakpoint, newCols: number) => void;

export type WidgetDefinitionWithLoadingState = WidgetDefinition & { isLoading: boolean };
