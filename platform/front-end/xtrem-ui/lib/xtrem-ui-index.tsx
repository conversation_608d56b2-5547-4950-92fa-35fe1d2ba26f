import { setLocalizeImplementation } from '@sage/xtrem-date-time';
import GlobalStyle from 'carbon-react/esm/style/global-style';
import * as React from 'react';
import { Provider } from 'react-redux';
import * as xtremRedux from './redux';
import type { ApplicationContext } from './redux/state';
import { errorDialog } from './service/dialog-service';
import { localize } from './service/i18n-service';
import { notifyConsumerOnError } from './service/telemetry-service';
import type { StickerDefinition } from './service/sticker-definition';
import { hasAnyDirtyScreenDefinitions } from './utils/state-utils';
import { ConnectedXtremController } from './xtrem-controller';
import { setAutoFreeze } from 'immer';
import I18nProvider from 'carbon-react/esm/components/i18n-provider';
import { carbonLocale } from './utils/carbon-locale';
import { objectKeys } from '@sage/xtrem-shared';
import { NodeCacheService } from './service/node-cache-service';
import { xtremConsole } from './utils/console';

setAutoFreeze(false);
setLocalizeImplementation(localize);

export interface XtremUiIndexProps {
    path: string;
    applicationContext: ApplicationContext;
}

/** Based on https://developer.mozilla.org/en-US/docs/Web/API/WindowEventHandlers/onbeforeunload */
const onBeforeUnloadListener = (ev: BeforeUnloadEvent): string | undefined => {
    if (!hasAnyDirtyScreenDefinitions(xtremRedux.getStore().getState())) {
        delete ev.returnValue;
        return undefined;
    }

    const message = localize('@sage/xtrem-ui/unsaved-changes-content', 'Leave and discard your changes?');
    ev.preventDefault();
    ev.returnValue = message;
    return message;
};

// This function is needed so the automation suite is not getting blocked.
(window as any).__REMOVE_UNLOAD_LISTENER = (): void =>
    window.removeEventListener('beforeunload', onBeforeUnloadListener);

/**
 * Entry point of the Xtrem UI framework. Instantiates the react Providers, sets the application
 * context and renders the framework components
 */
export class XtremUiIndex extends React.Component<XtremUiIndexProps> {
    constructor(props: XtremUiIndexProps) {
        super(props);
        this.setGlobalState(props);
    }

    componentDidMount(): void {
        window.addEventListener('beforeunload', onBeforeUnloadListener);
    }

    componentDidUpdate(prevProps: XtremUiIndexProps): void {
        if (
            !prevProps.applicationContext ||
            prevProps.path !== this.props.path ||
            // BL: The locale and the login is unlikely to change without a page reload, but just in case...
            prevProps.applicationContext.login !== this.props.applicationContext.login ||
            prevProps.applicationContext.locale !== this.props.applicationContext.locale ||
            prevProps.applicationContext.isPlaySoundEnabled !== this.props.applicationContext.isPlaySoundEnabled
        ) {
            this.setGlobalState(this.props);
        }
    }

    componentWillUnmount(): void {
        window.removeEventListener('beforeunload', onBeforeUnloadListener);
        const thunkDispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
        thunkDispatch({ type: xtremRedux.ActionType.ClearAllSubscriptions });
    }

    /** This method is used by xtrem-standalone */
    // eslint-disable-next-line react/no-unused-class-component-methods
    onWebSocketMessage(event: MessageEvent): void {
        let content: any = {};
        try {
            content = JSON.parse(event.data);
        } catch {
            xtremConsole.warn(`Websocket message received but could not be parsed: ${event.data}`);
        }
        if (content.category === 'nodeModified' && content.payload && typeof content.payload === 'string') {
            const [node] = content.payload.split('/');
            if (node) {
                NodeCacheService.flushCache([node]);
            }
        }
        if (content.category && typeof content.category === 'string' && content.payload) {
            const thunkDispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
            thunkDispatch({
                type: xtremRedux.ActionType.TriggerCategoryCallbacks,
                value: { category: content.category, args: [content.payload] },
            });
        }
    }

    private async setGlobalState(props: XtremUiIndexProps): Promise<void> {
        const thunkDispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;

        // Maybe we need a high level page loaded event, with setting various properties
        const currentPath = xtremRedux.getStore().getState().path;
        await thunkDispatch(xtremRedux.actions.setApplicationContext(props.applicationContext));

        if (props.applicationContext) {
            await thunkDispatch(xtremRedux.actions.setInitialMetaData(props.applicationContext));
        }

        const state = xtremRedux.getStore().getState();

        const stateStickerDefinitions = objectKeys(state.screenDefinitions).reduce<StickerDefinition[]>(
            (stickers, screenDefinitionKey) => {
                if (state.screenDefinitions[screenDefinitionKey].type === 'sticker') {
                    stickers.push(state.screenDefinitions[screenDefinitionKey] as StickerDefinition);
                }

                return stickers;
            },
            [],
        );

        if (currentPath !== props.path && stateStickerDefinitions.length > 0) {
            await this.navigate(thunkDispatch, props.path, true);
        } else if (currentPath !== props.path) {
            await thunkDispatch(xtremRedux.actions.discoverStickers());
            await this.navigate(thunkDispatch, props.path, true);
        }
    }

    async navigate(
        thunkDispatch: xtremRedux.AppThunkDispatch,
        path: string,
        navigatingFromMenu: boolean,
    ): Promise<void> {
        try {
            await thunkDispatch(xtremRedux.actions.navigate(path, {}, navigatingFromMenu));
        } catch (error) {
            errorDialog('Navigation error', 'Error', error);
            notifyConsumerOnError(error);
        }
    }

    /** This method is used by xtrem-standalone */
    // eslint-disable-next-line react/no-unused-class-component-methods
    cleanToasts(): void {
        const thunkDispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
        thunkDispatch(xtremRedux.actions.removeToasts());
    }

    render(): React.ReactNode {
        return (
            <Provider store={xtremRedux.getStore()}>
                <GlobalStyle />
                <I18nProvider locale={carbonLocale(this.props.applicationContext!.locale || 'en-US')}>
                    <ConnectedXtremController />
                </I18nProvider>
            </Provider>
        );
    }
}
