import * as React from 'react';
import { connect } from 'react-redux';
import { ConnectedDialogRouter } from './component/container/dialog/dialog-router';
// eslint-disable-next-line import/no-named-as-default
import LoadingDialog from './component/container/dialog/loading-dialog';
import PageComponent from './component/container/page/page-component';
import { ConnectedToastComponent } from './component/toast/toast-component';
import type * as xtremRedux from './redux';
import type { ResponsiveTypes } from './redux/state';
import type { Toast } from './service/toast-service';
import type { PageDefinition } from './service/page-definition';
import { ContextType } from './types';
import { getPageBodyColumnCount } from './utils/responsive-utils';
import { getMainPageDefinitionFromState } from './utils/state-utils';

/** @internal */
export interface XtremControllerProps {
    browserIs: ResponsiveTypes;
    loading: boolean;
    toasts: Toast[];
    pageDefinition?: PageDefinition;
    path: string | null;
}

/** @internal */
class XtremController extends React.Component<XtremControllerProps> {
    renderBody(): JSX.Element | null {
        const gridColumnCount = getPageBodyColumnCount(this.props.browserIs);

        if (this.props.pageDefinition && this.props.pageDefinition.isReady) {
            return (
                <PageComponent
                    key={this.props.pageDefinition.metadata.screenId}
                    pageDefinition={this.props.pageDefinition}
                    contextType={ContextType.page}
                    availableColumns={gridColumnCount}
                />
            );
        }
        return null;
    }

    render(): React.ReactNode {
        const cssClasses = 'e-xtrem-controller';
        return (
            <div className={cssClasses} style={!this.props.path ? { display: 'none' } : {}}>
                {this.renderBody()}
                <ConnectedDialogRouter />

                {this.props.loading && <LoadingDialog />}
                {this.props.toasts.map(toast => (
                    <ConnectedToastComponent key={toast.id} toast={toast} />
                ))}
            </div>
        );
    }
}

/** @internal */
const mapStateToProps = (state: xtremRedux.XtremAppState): XtremControllerProps => ({
    path: state.path,
    browserIs: state.browser.is,
    loading: state.loading.globalLoading,
    toasts: state.toasts,
    pageDefinition: getMainPageDefinitionFromState(state),
});

/** @internal */
export const ConnectedXtremController = connect(mapStateToProps)(XtremController);
