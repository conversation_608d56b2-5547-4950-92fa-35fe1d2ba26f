const pathMock = jest.fn(() => '');
jest.mock('@sage/xtrem-ui-components', () => ({
    getInternalPathFromExternal: pathMock,
}));

import { getMockStore } from '../../__tests__/test-helpers';
import axios from 'axios';
import { render } from '@testing-library/react';
import * as React from 'react';
import * as consumerMock from '../consumer-mock';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../redux';

describe('Consumer Mock', () => {
    let mockStore: MockStoreEnhanced<XtremAppState>;
    beforeEach(() => {
        mockStore = getMockStore();
        jest.spyOn(axios, 'post').mockResolvedValue({
            data: {
                data: {
                    pages: [{ key: '@sage/xtrem-test/TestPage', title: 'Test Page' }],
                    dashboard: {},
                    installedPackages: [],
                    customizationWizardPage: '@sage/xtrem-test/CustomizationPage',
                    userInfo: {
                        _id: 1,
                        email: 'admin',
                        userCode: 'admin',
                    },
                },
            },
        });
        jest.spyOn(axios, 'get').mockResolvedValue({
            data: {
                productName: 'XTreeM',
            },
        });
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('Snapshots', () => {
        it('should render the consumer mock without default path', () => {
            const { container } = render(
                <Provider store={mockStore}>
                    <consumerMock.ConsumerMock />
                </Provider>,
            );
            expect(container).toMatchSnapshot();
        });

        it('should render the consumer mock with default path', () => {
            pathMock.mockReturnValue('salesOrders/item/1');
            const { container } = render(
                <Provider store={mockStore}>
                    <consumerMock.ConsumerMock />
                </Provider>,
            );
            expect(container).toMatchSnapshot();
        });
    });
});
