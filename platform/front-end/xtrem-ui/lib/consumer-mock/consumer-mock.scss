/* TODO Extract common mobile mixin and .json files to common place? */
@import '../render/style/mixins';
@import '../render/style/variables';
@import '~@sage/design-tokens/css/base.css';

@mixin mobile {
    @media (max-width: #{$mobile}) {
        @content;
    }
}

/* Sage Text font family */
@font-face {
    font-family: "Sage Text";
    src: url('https://fonts.sage.com/Sage_Text-Light.woff2') format("woff2"), url('https://fonts.sage.com/Sage_Text-Light.woff') format("woff");
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Sage Text";
    src: url('https://fonts.sage.com/Sage_Text-Light_Italic.woff2') format("woff2"), url('https://fonts.sage.com/Sage_Text-Light_Italic.woff') format("woff");
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Sage Text";
    src: url('https://fonts.sage.com/Sage_Text-Regular.woff2') format("woff2"), url('https://fonts.sage.com/Sage_Text-Regular.woff') format("woff");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

html,
body,
#root,
.consumer-container {
    margin: 0;
    min-height: 100vh;
    min-width: 100%;
    font-size: 12px;
    height: 100%;

    // set a max height for carbon's popover
    div[role="menu"][data-element="additional-buttons"] {
        max-height: 80%;
        overflow-y: auto;
    }
}

.consumer-logo-container {
    color: var(--colorsYang100);
    display: flex;
}

.consumer-app-bar,
.consumer-app-bar>div {
    background: #000;
    box-shadow: none;
    display: flex;
    flex-flow: row nowrap;
    height: 48px;
    position: fixed;
    transition: all 0.2s ease;
    justify-content: space-between;
    width: 100vw;
    z-index: 10;

    .consumer-sticker-icons {
        display: flex;
    }

    .consumer-sticker-icon span.carbon-icon {
        height: 100%;
    }

    >button:first-child {
        height: 32px;
        width: 185px;
        margin-top: 8px;
        margin-bottom: 8px;
        box-sizing: border-box;
    }
}

.consumer-container {
    display: flex;
    flex-direction: column;
    background-size: cover;

    .consumer-sticker-icon {
        position: relative;

        .consumer-navigation-menu-item {
            padding: 0 10px;

            &::before {
                line-height: 48px;
                font-size: 20px;
            }
        }

        .consumer-toast-indicator {
            position: absolute;
            right: 4px;
            top: 4px;
            z-index: 1;
            min-width: 16px;
            line-height: 16px;
            padding: 0;
            font-size: 10px;
            cursor: pointer;
        }
    }

    .consumer-sticker-icon-left {
        position: absolute;
    }

    header .carbon-icon {
        font-size: 20px;
        cursor: pointer;
    }

    .consumer-sitemap {
        font-family: $fontAdelle;
        font-size: 14px;
        overflow-y: auto;
        overflow-x: hidden;
        position: fixed;
        top: 49px;
        bottom: 0;
        left: 0;
        width: 320px;
        background-color: var(--colorsYang100);
        z-index: 15;
        color: var(--colorsYin090);
        padding-left: 10px;
        white-space: nowrap;

        @include s_section_shadow;

        @include extra_small {
            top: 56px;
            width: 100%;
        }

        >.consumer-sitemap-menu-list {
            max-height: 100%;

            >ul:first-child {
                padding-top: 10px;
            }

            >ul {
                margin-bottom: 10px;
                padding-left: 5px;
                max-height: 100%;
                margin-top: 0;
                overflow: auto;

                >button {
                    background-color: var(--colorsYang100);
                    border: 0px var(--colorsYang100);
                }

                >li {
                    padding: 10px;
                }
            }

            .consumer-search-close {
                padding: 13px;
            }
        }
    }

    .consumer-body {
        flex: 1;
        overflow-y: hidden;
        display: flex;
        margin-left: 64px;
        border-left: 1px solid var(--colorsUtilityMajor200);
        box-sizing: border-box;

        @include extra_small {
            margin-left: 0;
            border-right: unset;
        }
    }

    .consumer-logo {
        height: 32px;
        width: 58px;
        margin-top: 8px;
        margin-bottom: 8px;
        margin-left: 24px;
        padding-right: 16px;
        border-right: 1px solid #5e5e5e;
    }

    .consumer-text-logo-main-name {
        color: var(--colorsYang100);
        font-family: 'Sage Text', sans-serif;
        font-weight: 500;
        font-size: 18px;
        line-height: 18px;
        margin: 14px 12px 10px 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
        vertical-align: top;

        @include medium_and_below {
            display: none;
        }
    }

    .consumer-path-input {
        flex: 1;

        input {
            color: var(--colorsYang100);
        }

        &>div::before {
            border-color: var(--colorsYang100);
        }
    }

    .consumer-menu {
        padding: 10px;
        margin: 50px auto;
        max-width: 600px;
    }

    .consumer-xtrem-wrapper {
        width: 100%;
        display: flex;

        // Info: Overrides Carbon's default styling of the design tokens. Using :only-child selector to ensure the
        //       dashboard is rendered correctly.
        >div:only-child {
            flex: 1;
            display: block;
            overflow-x: hidden;
        }
    }
}

.consumer-clearpath,
.consumer-endpoint,
.consumer-locale-selection,
.consumer-testids,
.consumer-sound-toggle {
    display: inline-block;
    width: 140px;
    padding: 4px;

    @include extra_small {
        display: none;
    }
}

.consumer-username {
    display: inline-block;
    color: var(--colorsYang100);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    label {
        display: block;
        font-size: 14px;
        line-height: 20px;
    }
}

.e-consumer-mock-id-helper {
    position: fixed;
    font-size: 6px;
}

.consumer-navigation {
    width: 64px;
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    margin-top: 48px;

    .consumer-navigation-menu-item button {
        width: 64px;
        height: 64px;
        border-bottom: 1px solid var(--colorsUtilityMajor200);
        box-sizing: border-box;
    }
}

.consumer-dirty-indicator {
    color: var(--colorsYang100);
    font-size: 16px;
    line-height: 48px;
}