// COMMENT THIS OUT TO TEST ADC WRAPPER EVENTS
/*
(window as any).XTREM_WRAPPER_MOBILE_APP_INTERFACE = {
    // eslint-disable-next-line no-console
    onFocus: (arg: any) => {
        console.log('WRAPPER - FOCUS', arg);
        const titleElement = document.querySelector('.consumer-logo-container');
        if (titleElement) {
            titleElement.innerHTML = 'BUTTON DISPLAYED';
        }
    },
    // eslint-disable-next-line no-console
    onBlur: (arg: any) => {
        console.log('WRAPPER - BLUR', arg);
        const titleElement = document.querySelector('.consumer-logo-container');
        if (titleElement) {
            titleElement.innerHTML = 'BUTTON Hidden';
        }
    },
};

*/

import axios from 'axios';
import { Option, Select } from 'carbon-react/esm/components/select';
import Textbox from 'carbon-react/esm/components/textbox';
import Button from 'carbon-react/esm/components/button';
import Link from 'carbon-react/esm/components/link';
import Pill from 'carbon-react/esm/components/pill';
import CarbonProvider from 'carbon-react/esm/components/carbon-provider';
import { camelCase } from 'lodash';
import * as React from 'react';
import { createRoot } from 'react-dom/client';
import { Icon } from '../component/ui/icon/icon-component';
import type { ApplicationContext, Menu } from '../integration';
import { XtremUiIndex, DashboardRootComponent } from '../integration';
import * as xtremRedux from '../redux';
import { queryMetadata } from '../service/metadata-service';
import { localize } from '../service/i18n-service';
import { isHidden } from '../utils/dom';
import type { Dict } from '@sage/xtrem-shared';
import { xtremConsole } from '../utils/console';
import sageTheme from 'carbon-react/esm/style/themes/sage';
import * as tokens from '@sage/design-tokens/js/base/common';
import IconButton from 'carbon-react/esm/components/icon-button';
import type { QueryParameters } from '../utils/types';
import { getInternalPathFromExternal } from '@sage/xtrem-ui-components';
import { executeGraphqlQuery } from '../service/graphql-utils';
import './element-details-logger';
import '../render/style/index.scss';
import './consumer-mock.scss';

const colors = [
    '#000080',
    '#00008B',
    '#0000CD',
    '#0000FF',
    '#006400',
    '#008000',
    '#008080',
    '#008B8B',
    '#00BFFF',
    '#00CED1',
    '#00FA9A',
    '#00FF00',
    '#00FF7F',
    '#00FFFF',
    '#00FFFF',
    '#191970',
    '#1E90FF',
    '#20B2AA',
    '#228B22',
    '#2E8B57',
    '#2F4F4F',
    '#2F4F4F',
    '#32CD32',
    '#3CB371',
    '#40E0D0',
    '#4169E1',
    '#4682B4',
    '#483D8B',
    '#48D1CC',
    '#4B0082',
    '#556B2F',
    '#5F9EA0',
    '#6495ED',
    '#663399',
    '#66CDAA',
    '#696969',
    '#696969',
    '#6A5ACD',
    '#6B8E23',
    '#708090',
    '#708090',
    '#778899',
    '#778899',
    '#7B68EE',
    '#7CFC00',
    '#7FFF00',
    '#7FFFD4',
    '#800000',
    '#800080',
    '#808000',
    '#808080',
    '#808080',
    '#87CEEB',
    '#87CEFA',
    '#8A2BE2',
    '#8B0000',
    '#8B008B',
    '#8B4513',
    '#8FBC8F',
    '#90EE90',
    '#9370DB',
    '#9400D3',
    '#98FB98',
    '#9932CC',
    '#9ACD32',
    '#A0522D',
    '#A52A2A',
    '#A9A9A9',
    '#A9A9A9',
    '#ADD8E6',
    '#ADFF2F',
    '#AFEEEE',
    '#B0C4DE',
    '#B0E0E6',
    '#B22222',
    '#B8860B',
    '#BA55D3',
    '#BC8F8F',
    '#BDB76B',
    '#C0C0C0',
    '#C71585',
    '#CD5C5C',
    '#CD853F',
    '#D2691E',
    '#D2B48C',
    '#D3D3D3',
    '#D3D3D3',
    '#D8BFD8',
    '#DA70D6',
    '#DAA520',
    '#DB7093',
    '#DC143C',
    '#DCDCDC',
    '#DDA0DD',
    '#DEB887',
    '#E0FFFF',
    '#E6E6FA',
    '#E9967A',
    '#EE82EE',
    '#EEE8AA',
    '#F08080',
    '#F0E68C',
    '#F0F8FF',
    '#F0FFF0',
    '#F0FFFF',
    '#F4A460',
    '#F5DEB3',
    '#F5F5DC',
    '#F5F5F5',
    '#F5FFFA',
    '#F8F8FF',
    '#FA8072',
    '#FAEBD7',
    '#FAF0E6',
    '#FAFAD2',
    '#FDF5E6',
    '#FF0000',
    '#FF00FF',
    '#FF00FF',
    '#FF1493',
    '#FF4500',
    '#FF6347',
    '#FF69B4',
    '#FF7F50',
    '#FF8C00',
    '#FFA07A',
    '#FFA500',
    '#FFB6C1',
    '#FFC0CB',
    '#FFD700',
    '#FFDAB9',
];

export interface PageMetadata {
    key: string;
    title: string;
}

export interface PageEntry {
    name: string;
    pages: PageMetadata[];
}

export interface ConsumerMockState {
    error?: Error;
    isAppDirty: boolean;
    isPlaySoundEnabled: boolean;
    isSitemapVisible: boolean;
    locale: string;
    menuItems: Menu[];
    openedModules?: Dict<boolean>;
    openPath: string | null;
    pageSearch: string;
    preNavigationConfirmation: (() => Promise<void>) | null;
    productName: string;
    siteMap: PageEntry[];
    userCode?: string;
    username?: string;
}

const supportedLocales: { value: string; text: string }[] = [
    { value: 'en-US', text: 'English US' },
    { value: 'en-GB', text: 'English GB' },
    { value: 'es-ES', text: 'Spanish' },
    { value: 'fr-FR', text: 'French' },
    { value: 'de-DE', text: 'German' },
    { value: 'pl-PL', text: 'Polish' },
    { value: 'pt-PT', text: 'Portuguese' },
    { value: 'zh-CN', text: 'Chinese' },
];

const toggleSoundOptions: { value: boolean; text: string }[] = [
    { value: true, text: 'Sound Enabled' },
    { value: false, text: 'Sound Disabled' },
];

export class ConsumerMock extends React.Component<{}, ConsumerMockState> {
    private readonly containerRef = React.createRef<HTMLDivElement>();

    private readonly xtremUiRef = React.createRef<XtremUiIndex>();

    constructor(props: any) {
        super(props);

        this.state = {
            openPath: getInternalPathFromExternal(),
            isSitemapVisible: false,
            siteMap: [],
            menuItems: [],
            pageSearch: '',
            isAppDirty: false,
            preNavigationConfirmation: null,
            productName: 'XTreeM',
            locale: (window.localStorage && window.localStorage.getItem('consumer-mock-locale')) || 'en-US',
            userCode: (window.localStorage && window.localStorage.getItem('consumer-mock-username')) || 'admin',
            isPlaySoundEnabled: Boolean(
                window.localStorage && window.localStorage.getItem('consumer-mock-sound') === 'true',
            ),
        };

        executeGraphqlQuery({
            query: {
                userInfo: {
                    userCode: true,
                    email: true,
                },
            },
            endpoint: '/metadata',
        }).then(d => {
            this.setState({
                userCode: d.data.userInfo.userCode,
                username: d.data.userInfo.email,
            });
        });

        axios.get('/standalone-config').then(response => {
            this.setState({
                productName: response.data?.productName || 'XTreeM',
            });
        });

        queryMetadata({ metadataType: 'pages', metadataProperties: ['key', 'title'], locale: this.state.locale })
            .then(response => {
                const pages = response.pages;
                if (!pages || pages.length === 0) {
                    throw new Error(localize('@sage/xtrem-ui/no-pages-found', 'Could not find any pages'));
                }
                const keys = Array.from(new Set(pages.map(page => page.key!.substr(0, page.key!.lastIndexOf('/')))));
                const buildSiteMapStateObject = (): {
                    name: string;
                    pages: {
                        key: string;
                        title: string;
                    }[];
                }[] => {
                    return keys.map(keyName => {
                        const buildPagesMetadata = (): any =>
                            pages
                                .filter(pageEntry => pageEntry.key!.startsWith(`${keyName}/`))
                                .map(page => ({ key: page.key!, title: page.title! }))
                                .sort((p1, p2) => (p1.title || p1.key || '').localeCompare(p2.title || p2.key || ''));
                        return { name: keyName, pages: buildPagesMetadata() };
                    });
                };

                this.setState({
                    siteMap: buildSiteMapStateObject(),
                });
            })
            .catch(err => {
                throw new Error(`Could not get siteMap due to the following error: ${err}`);
            });

        // CC: The following subscription is necessary since we keep track of the responsive state of the page through ReduxResponsive
        xtremRedux.getStore().subscribe(() => {
            this.forceUpdate();
        });
    }

    componentDidMount(): void {
        this.initiateSocketConnection();
    }

    componentDidCatch(error: Error): void {
        this.setState({ error });
    }

    initiateSocketConnection = (protocols?: string): void => {
        let keepAliveIntervalRef: NodeJS.Timeout | null;
        const webSocket = new WebSocket('ws://websocket.localhost:8240', protocols);

        const sendKeepAliveMessage = (): void => {
            try {
                webSocket?.send(JSON.stringify({ category: 'keepAlive' }));
            } catch (err) {
                xtremConsole.warn(err);
            }
        };

        const clearKeepAliveInterval = (): void => {
            if (keepAliveIntervalRef) {
                clearInterval(keepAliveIntervalRef);
                keepAliveIntervalRef = null;
            }
        };

        webSocket.onclose = (): void => {
            clearKeepAliveInterval();
            setTimeout(() => {
                this.initiateSocketConnection(protocols);
            }, 30000);
        };

        webSocket.onopen = (): void => {
            clearKeepAliveInterval();
            keepAliveIntervalRef = setInterval(sendKeepAliveMessage, 25000);
        };

        webSocket.onmessage = async (event: any): Promise<void> => {
            this.xtremUiRef.current?.onWebSocketMessage(event);
        };
    };

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onInternalNavigate = (path: string, queryParameters: QueryParameters = {}): void => {
        const nextUrl = `${window.location.origin}/${path}`;
        window.history.pushState({ id: path }, `Xtrem UI ${path}`, nextUrl);
        this.onClearTestIds();
        this.setState({ openPath: path });

        // TODO Add query parameters to the url
        // TODO Enable back navigation
    };

    onMenuChange = (newMenuItems: Menu[]): void => {
        this.setState({ menuItems: newMenuItems });
    };

    onLocaleChange = (event: React.ChangeEvent<{ value: string }>): void => {
        const locale = event.target.value;
        this.setState({ locale });

        if (window.localStorage) {
            window.localStorage.setItem('consumer-mock-locale', locale);
        }
    };

    onSoundToggleChange = (event: React.ChangeEvent<{ value: string }>): void => {
        this.setState({ isPlaySoundEnabled: event.target.value === 'true' });
        if (window.localStorage) {
            window.localStorage.setItem('consumer-mock-sound', event.target.value);
        }
    };

    onUserChange = (event: any): void => {
        const username = event.target.value;
        this.setState({ username, userCode: username });

        if (window.localStorage) {
            window.localStorage.setItem('consumer-mock-username', username);
        }
    };

    filterSitemapItems = (page: PageMetadata): boolean => {
        return (
            !this.state.pageSearch ||
            (page.title?.toLowerCase().includes(this.state.pageSearch.toLowerCase()) ?? false) ||
            (page.key?.toLowerCase().includes(this.state.pageSearch.toLowerCase()) ?? false)
        );
    };

    sitemapClick = (): void => {
        this.setState(state => ({ isSitemapVisible: !state.isSitemapVisible }));
    };

    renderMenuItems = (menuItems: Menu[], isLeft = false): React.ReactNode =>
        menuItems.map(menuItem => {
            const responsiveItem = {
                $isHiddenMobile: menuItem.isHiddenMobile,
                $isHiddenDesktop: menuItem.isHiddenDesktop,
            };
            return isHidden(responsiveItem, xtremRedux.getStore().getState().browser) ? null : (
                <div
                    key={menuItem.id}
                    title={menuItem.title}
                    className={`consumer-sticker-icon${isLeft ? ' consumer-sticker-icon-left' : ''}`}
                >
                    {menuItem.badgeContent && (
                        <Pill fill={true} onClick={menuItem.onClick}>
                            {menuItem.badgeContent}
                        </Pill>
                    )}

                    <Icon
                        className="consumer-navigation-menu-item carbon-icon"
                        onClick={menuItem.onClick}
                        type={menuItem.icon || 'home'}
                        color={tokens.colorsYang100}
                    />
                </div>
            );
        });

    openMenuItem = (url: string) => async (): Promise<void> => {
        if (this.state.isAppDirty && this.state.preNavigationConfirmation) {
            try {
                await this.state.preNavigationConfirmation();
            } catch {
                return;
            }
        }
        window.history.pushState(null, '', url);
        const pathToBeOpened = url.substring(1);
        this.setState({ openPath: pathToBeOpened, isSitemapVisible: false });
    };

    onClearTestIds = (): void => {
        document.querySelectorAll('.e-consumer-mock-id-helper').forEach((e: HTMLElement) => {
            e.parentNode?.removeChild(e);
        });
    };

    onShowTestIds = (): void => {
        this.onClearTestIds();
        if (this.containerRef.current) {
            const decorateItem = (e: HTMLElement, content: string, color: string, zIndex?: number): void => {
                const boundingBox = e.getBoundingClientRect();
                const testIdInfo = document.createElement('div');
                testIdInfo.style.top = `${boundingBox.top}px`;
                testIdInfo.style.left = `${boundingBox.left + boundingBox.width - 100}px`;
                testIdInfo.style.backgroundColor = color;
                testIdInfo.style.color = 'white';
                testIdInfo.style.width = '100px';
                if (zIndex) {
                    testIdInfo.style.zIndex = String(zIndex);
                }
                testIdInfo.textContent = content;
                testIdInfo.classList.add('e-consumer-mock-id-helper');
                this.containerRef.current?.appendChild(testIdInfo);
                const indicatorTop = document.createElement('div');
                indicatorTop.style.top = `${boundingBox.top}px`;
                indicatorTop.style.left = `${boundingBox.left}px`;
                indicatorTop.style.width = `${boundingBox.width}px`;
                indicatorTop.style.outline = `1px solid ${color}`;
                if (zIndex) {
                    indicatorTop.style.zIndex = String(zIndex);
                }
                indicatorTop.classList.add('e-consumer-mock-id-helper');
                this.containerRef.current?.appendChild(indicatorTop);
                const indicatorBottom = document.createElement('div');
                indicatorBottom.style.top = `${boundingBox.top + boundingBox.height}px`;
                indicatorBottom.style.left = `${boundingBox.left}px`;
                indicatorBottom.style.width = `${boundingBox.width}px`;
                indicatorBottom.style.outline = `1px solid ${color}`;
                if (zIndex) {
                    indicatorBottom.style.zIndex = String(zIndex);
                }
                indicatorBottom.classList.add('e-consumer-mock-id-helper');
                this.containerRef.current?.appendChild(indicatorBottom);
                const indicatorLeft = document.createElement('div');
                indicatorLeft.style.top = `${boundingBox.top}px`;
                indicatorLeft.style.left = `${boundingBox.left}px`;
                indicatorLeft.style.height = `${boundingBox.height}px`;
                indicatorLeft.style.outline = `1px solid ${color}`;
                if (zIndex) {
                    indicatorLeft.style.zIndex = String(zIndex);
                }
                indicatorLeft.classList.add('e-consumer-mock-id-helper');
                this.containerRef.current?.appendChild(indicatorLeft);
                const indicatorRight = document.createElement('div');
                indicatorRight.style.top = `${boundingBox.top}px`;
                indicatorRight.style.left = `${boundingBox.left + boundingBox.width}px`;
                indicatorRight.style.height = `${boundingBox.height}px`;
                indicatorRight.style.outline = `1px solid ${color}`;
                if (zIndex) {
                    indicatorRight.style.zIndex = String(zIndex);
                }
                indicatorRight.classList.add('e-consumer-mock-id-helper');
                this.containerRef.current?.appendChild(indicatorRight);
            };

            this.containerRef.current
                .querySelectorAll('[data-testid].e-field')
                .forEach((e: HTMLElement, index: number) => {
                    const color = colors[index % colors.length];
                    const attribute = e.attributes.getNamedItem('data-testid');
                    const matches = attribute?.value.match(
                        // eslint-disable-next-line @sage/redos/no-vulnerable
                        /e-([a-zA-Z0-9]+)-field[0-9a-z\sA-Z-]+e-field-bind-([a-zA-Z0-9]+)/,
                    );
                    if (matches) {
                        decorateItem(e, `Type: ${matches[1]}, Bind: ${matches[2]}`, color);
                    }
                });

            this.containerRef.current
                .querySelectorAll('[data-testid].e-block')
                .forEach((e: HTMLElement, index: number) => {
                    const color = colors[index % colors.length];
                    const attribute = e.attributes.getNamedItem('data-testid');
                    if (attribute && attribute.value.match(/e-field-bind-([a-zA-Z0-9]+)/)) {
                        const matches = attribute.value.match(/e-field-bind-([a-zA-Z0-9]+)/)!;
                        decorateItem(e, `Type: block, Bind: ${matches[1]}`, color);
                    }
                });

            this.containerRef.current
                .querySelectorAll('[data-testid].e-section')
                .forEach((e: HTMLElement, index: number) => {
                    const color = colors[index % colors.length];
                    const attribute = e.attributes.getNamedItem('data-testid');
                    if (attribute && attribute.value.match(/e-field-bind-([a-zA-Z0-9]+)/)) {
                        const matches = attribute.value.match(/e-field-bind-([a-zA-Z0-9]+)/)!;
                        decorateItem(e, `Type: section, Bind: ${matches[1]}`, color);
                    }
                });

            this.containerRef.current.querySelectorAll('.ag-cell').forEach((e: HTMLElement, index: number) => {
                const color = colors[index % colors.length];
                if (
                    e.className.match(
                        // eslint-disable-next-line @sage/redos/no-vulnerable
                        /e-nested-cell-bind-([a-zA-Z0-9]+) [a-z\sA-Z-]+e-nested-cell-field-([a-zA-Z0-9]+)/,
                    )
                ) {
                    const matches = e.className.match(
                        // eslint-disable-next-line @sage/redos/no-vulnerable
                        /e-nested-cell-bind-([a-zA-Z0-9]+) [a-z\sA-Z-]+e-nested-cell-field-([a-zA-Z0-9]+)/,
                    )!;
                    decorateItem(e, `Type: ${matches[2]}, Bind: ${matches[1]}`, color);
                }
            });

            this.containerRef.current.querySelectorAll('.e-header-action').forEach((e: HTMLElement, index: number) => {
                const color = colors[index % colors.length];
                const attribute = e.attributes.getNamedItem('data-testid');
                if (attribute && attribute.value.match(/e-header-action-bind-([a-zA-Z0-9]+)/)) {
                    const matches = attribute.value.match(/e-header-action-bind-([a-zA-Z0-9]+)/)!;
                    decorateItem(e, `Type: field action, Bind: ${matches[1]}`, color);
                }
            });

            this.containerRef.current
                .querySelectorAll('.e-business-action')
                .forEach((e: HTMLElement, index: number) => {
                    const color = colors[index % colors.length];
                    const attribute = e.attributes.getNamedItem('data-testid');
                    if (attribute && attribute.value.match(/e-field-bind-([a-zA-Z0-9]+)/)) {
                        const matches = attribute.value.match(/e-field-bind-([a-zA-Z0-9]+)/)!;
                        decorateItem(e, `Type: business action, Bind: ${matches[1]}`, color, 10);
                    }
                });

            this.containerRef.current
                .querySelectorAll('.e-page-crud-button')
                .forEach((e: HTMLElement, index: number) => {
                    const color = colors[index % colors.length];
                    const attribute = e.attributes.getNamedItem('data-testid');
                    if (attribute && attribute.value.match(/e-page-crud-button-([a-zA-Z0-9]+)/)) {
                        const matches = attribute.value.match(/e-page-crud-button-([a-zA-Z0-9]+)/)!;
                        decorateItem(e, `Type: crud action, Bind: ${matches[1]}`, color, 10);
                    }
                });
        }
    };

    onPageTitleChange = (newTitle: string | null): void => {
        if (newTitle) {
            document.title = `${newTitle} - ${this.state.productName}`;
        } else {
            document.title = this.state.productName;
        }
    };

    getApplicationContext = (): ApplicationContext => ({
        login: this.state.username,
        userCode: this.state.userCode,
        locale: this.state.locale,
        handleNavigation: this.onInternalNavigate,
        onPageTitleChange: this.onPageTitleChange,
        updateMenu: this.onMenuChange,
        onDirtyStatusChange: (isAppDirty: boolean, preNavigationConfirmation: () => Promise<void>): void => {
            this.setState({ isAppDirty, preNavigationConfirmation });
        },
        onApiRequestError: (error: any): void => {
            // don't log controlled cancellations (axios.CancelToken.source().token)
            if (!axios.isCancel(error)) {
                xtremConsole.log(error);
            }
        },
        requestHeaders: {
            'custom-header-for-graphql': 'This could well be a token',
        },
        isPlaySoundEnabled: this.state.isPlaySoundEnabled,
    });

    onToggleModuleName = (moduleName: string) => (): void => {
        this.setState(prevState => ({
            openedModules: { ...prevState.openedModules, [moduleName]: !prevState.openedModules?.[moduleName] },
        }));
    };

    renderNavigation = (): React.ReactNode => {
        return (
            <div className="consumer-navigation-toggle">
                <span className="consumer-navigation-menu-item">
                    <IconButton onClick={this.sitemapClick} aria-label="Search pages">
                        <Icon tooltipMessage="Search pages" type="search" />
                    </IconButton>
                </span>
            </div>
        );
    };

    onPageSearch = (searchText: string): void => {
        this.setState({ pageSearch: searchText });
        if (!searchText) {
            this.setState({ openedModules: {} });
        }
    };

    isPageInModuleAndOpenModule = (moduleEntry: PageEntry, pageSearch: string): boolean => {
        if (!pageSearch) return false;
        const pageInModule = moduleEntry.pages.find(
            (page: PageMetadata) =>
                (page.title?.toLowerCase().includes(pageSearch.toLowerCase()) ?? false) ||
                (page.key?.toLowerCase().includes(pageSearch.toLowerCase()) ?? false),
        );
        if (pageInModule) {
            this.setState(prevState => ({
                openedModules: { ...prevState.openedModules, [moduleEntry.name]: true },
            }));
        }
        return Boolean(pageInModule);
    };

    render(): React.ReactNode {
        const applicationContext: ApplicationContext = this.getApplicationContext();
        const leftMenuItems = this.state.menuItems.filter(x => x.alignedLeft);
        const rightMenuItems = this.state.menuItems.filter(x => !x.alignedLeft);

        if (this.state.error) {
            return (
                <div style={{ padding: '20px' }}>
                    <h1>{this.state.error.message}</h1>
                    <pre>{this.state.error.stack}</pre>
                </div>
            );
        }

        if (!this.state.username) {
            return null;
        }

        return (
            <div className="consumer-container">
                <div className="consumer-app-bar">
                    <CarbonProvider theme={sageTheme}>
                        {this.renderMenuItems(leftMenuItems, true)}
                        {this.state.isSitemapVisible ? (
                            <IconButton onClick={(): void => this.setState({ isSitemapVisible: false })}>
                                <Icon color="white" tooltipMessage="Close" type="close" />
                            </IconButton>
                        ) : (
                            <div onClick={this.sitemapClick} className="consumer-logo-container">
                                <img className="consumer-logo" src="/images/sage-logo.svg" alt="Sage" />
                                <span className="consumer-text-logo-main-name">{this.state.productName}</span>
                            </div>
                        )}
                        {this.state.isAppDirty && <div className="consumer-dirty-indicator">Dirty</div>}
                        <div className="consumer-sticker-icons">{this.renderMenuItems(rightMenuItems)}</div>
                        <div className="consumer-testids">
                            <Button onClick={this.onShowTestIds} buttonType="primary" noWrap={true}>
                                {localize('@sage/xtrem-ui/consumer-mock-show-test-ids', 'Show IDs')}
                            </Button>
                        </div>
                        <div className="consumer-testids">
                            <Button onClick={this.onClearTestIds} buttonType="primary" noWrap={true}>
                                {localize('@sage/xtrem-ui/consumer-mock-hide-test-ids', 'Hide IDs')}
                            </Button>
                        </div>
                        <div className="consumer-username">
                            <label>User: {this.state.username}</label>
                            <label>Code: {this.state.userCode}</label>
                        </div>
                        <div className="consumer-locale-selection" data-testid="consumer-locale-selection">
                            <Select
                                onChange={this.onLocaleChange}
                                placeholder="Locale"
                                value={String(this.state.locale)}
                                readOnly={false}
                                size="medium"
                                ariaLabel="Locale"
                            >
                                {supportedLocales.map(l => (
                                    <Option
                                        key={l.text}
                                        {...l}
                                        data-testid={`consumer-locale-selection-option-${camelCase(l.text)}`}
                                    />
                                ))}
                            </Select>
                        </div>
                        <div className="consumer-sound-toggle" data-testid="consumer-sound-toggle">
                            <Select
                                ariaLabel="Sound Setting"
                                onChange={this.onSoundToggleChange}
                                placeholder="Sound"
                                value={String(this.state.isPlaySoundEnabled)}
                                readOnly={false}
                                size="medium"
                                label=""
                            >
                                {toggleSoundOptions.map(l => (
                                    <Option
                                        key={l.text}
                                        text={l.text}
                                        value={String(l.value)}
                                        data-testid={`consumer-sound-toggle-option-${camelCase(l.text)}`}
                                    />
                                ))}
                            </Select>
                        </div>
                    </CarbonProvider>
                </div>

                {this.state.isSitemapVisible ? (
                    <CarbonProvider theme={sageTheme}>
                        <div className="consumer-sitemap">
                            <div className="consumer-sitemap-menu-list">
                                <div className="consumer-search-close">
                                    <Textbox
                                        placeholder="Search pages"
                                        value={this.state.pageSearch}
                                        onChange={(ev): void => this.onPageSearch(ev.target.value)}
                                    />
                                </div>

                                <div key="dashboard">
                                    <Button buttonType="tertiary" size="small" onClick={this.openMenuItem('')}>
                                        Dashboard
                                    </Button>
                                </div>
                                {this.state.siteMap.map(
                                    moduleEntry =>
                                        moduleEntry.pages &&
                                        moduleEntry.pages.filter(this.filterSitemapItems).length > 0 && (
                                            <>
                                                <div key={`${moduleEntry.name}-button`}>
                                                    <Button
                                                        buttonType="tertiary"
                                                        size="small"
                                                        onClick={this.onToggleModuleName(moduleEntry.name)}
                                                        iconPosition="before"
                                                        iconType={
                                                            this.state.openedModules?.[moduleEntry.name]
                                                                ? 'chevron_down'
                                                                : 'chevron_right'
                                                        }
                                                    >
                                                        {moduleEntry.name}
                                                    </Button>
                                                </div>
                                                {this.state.openedModules?.[moduleEntry.name] ||
                                                this.isPageInModuleAndOpenModule(moduleEntry, this.state.pageSearch) ? (
                                                    <ul key={`${moduleEntry.name}-list`}>
                                                        {moduleEntry.pages.filter(this.filterSitemapItems).map(page => {
                                                            return (
                                                                <li key={page.key}>
                                                                    <Link onClick={this.openMenuItem(`/${page.key}`)}>
                                                                        {page.title || '&nbsp'}
                                                                    </Link>
                                                                </li>
                                                            );
                                                        })}
                                                    </ul>
                                                ) : null}
                                            </>
                                        ),
                                )}
                            </div>
                        </div>
                    </CarbonProvider>
                ) : null}
                <div className="consumer-body">
                    <div className="consumer-navigation">{this.renderNavigation()}</div>
                    <div className="consumer-xtrem-wrapper" ref={this.containerRef}>
                        <CarbonProvider theme={sageTheme}>
                            <XtremUiIndex
                                ref={this.xtremUiRef}
                                path={this.state.openPath!}
                                applicationContext={applicationContext}
                            />
                            {!this.state.openPath && (
                                <DashboardRootComponent applicationContext={applicationContext} group="home" />
                            )}
                        </CarbonProvider>
                    </div>
                </div>
            </div>
        );
    }
}

/* istanbul ignore next */
(window as any).start = (): void => {
    createRoot(window.document.getElementById('root') as HTMLElement).render(<ConsumerMock />);
};
