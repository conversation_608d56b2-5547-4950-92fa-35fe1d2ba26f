/* eslint-disable no-console */
import type { NestedField } from '../component/nested-fields';
import { convertDeepBindToPathNotNull } from '../utils/nested-field-utils';
import type { Level } from '../component/field/nested-grid/nested-grid-component-types';
import type { Dict } from '@sage/xtrem-shared';
import { objectKeys } from '@sage/xtrem-shared';
import * as xtremRedux from '../redux';
import { isEmpty } from 'lodash';

function printNestedFields(nestedFields: NestedField<any, any>[]): void {
    nestedFields.forEach((column: NestedField<any, any>) => {
        console.groupCollapsed(
            convertDeepBindToPathNotNull(column.properties.bind),
            column.properties._controlObjectType,
        );
        printPropertyGroup(column.properties);
        printExtensionDetails(column.properties);
        console.groupEnd();
    });
}

function printExtensionDetails(properties: Dict<any>): void {
    console.groupCollapsed('%cExtension details', 'color:yellow;background:black;');

    if (properties._declaredInExtension) {
        console.log('Field declared in extension:', properties._declaredInExtension);
    } else {
        console.log('Field declared in the base page.');
    }

    if (isEmpty(properties._modifyingExtensions)) {
        console.log('No extensions modify this field.');
    } else {
        console.groupCollapsed('Extensions modifying this field');
        objectKeys(properties._modifyingExtensions).forEach(extension => {
            if (!properties._modifyingExtensions) {
                return;
            }
            console.groupCollapsed(extension);
            printPropertyGroup(properties._modifyingExtensions[extension]);
            console.groupEnd();
        });
        console.groupEnd();
    }
    console.groupEnd();
}

function printPropertyGroup(properties: Dict<any>): void {
    objectKeys(properties).forEach(key => {
        if (key.startsWith('_') || key === 'columns' || key === 'nestedFields' || key === 'levels') {
            return;
        }
        console.log(key, properties[key]);
    });

    if (properties.columns) {
        console.groupCollapsed('columns');
        printNestedFields(properties.columns);
        console.groupEnd();
    }
    if (properties.columnsOverrides) {
        console.groupCollapsed('columnsOverrides');
        printNestedFields(properties.columnsOverrides);
        console.groupEnd();
    }

    if (properties.nestedFields) {
        console.groupCollapsed('nestedFields');
        printNestedFields(properties.nestedFields);
        console.groupEnd();
    }
    if (properties.levels) {
        console.groupCollapsed('levels');
        properties.levels.forEach((level: Level<any>, index: number) => {
            console.groupCollapsed(`Level ${index}`);
            printNestedFields(level.columns);
            console.groupEnd();
        });
        console.groupEnd();
    }
}

(window as unknown as any).__XTREM_PRINT_ELEMENT_INFO = function PrintXtremElementInfo(
    screenId: string,
    fieldId: string,
): any {
    const state = xtremRedux.getStore().getState();
    const screenDefinition = state.screenDefinitions[screenId];

    if (!screenDefinition) {
        console.log(`${screenId} screen not found`);
        return;
    }

    const properties = screenDefinition.metadata.uiComponentProperties[fieldId];

    if (!properties) {
        console.log(`${fieldId} not found on ${screenId} screen`);
        return;
    }

    console.group(
        `%c Element info. Screen: "${screenId}" screen, element: "${fieldId}"`,
        'color:lime;background:black;',
    );
    console.log('%cElement type:', 'color:yellow;background:black;', properties._controlObjectType);
    console.groupCollapsed('%cElement properties', 'color:yellow;background:black;');
    printPropertyGroup(properties);
    console.groupEnd();

    printExtensionDetails(properties);

    console.groupEnd();
};
