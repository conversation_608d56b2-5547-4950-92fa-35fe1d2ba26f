import { AES, enc, PBKDF2 } from 'crypto-js';
import { xtremConsole } from '../utils/console';
import { isDevMode } from '../utils/window';

const salt = ')!L2EvEK%E@avy_%*AFH';
const options = {
    iv: enc.Utf8.parse('XWe7N*8hd)Ft_Q9FDaPh'),
};

export const encryptJsonDocument = (content: any, encryptionKey: string): string | null => {
    try {
        const text = JSON.stringify(content);
        const key = PBKDF2(encryptionKey, enc.Utf8.parse(salt), { keySize: 512 / 32, iterations: 1000 });
        const encryptedCP = AES.encrypt(text, key, options);

        return encryptedCP.toString();
    } catch (e) {
        if (isDevMode()) {
            xtremConsole.error('Failed to encrypt cache entry.');
            xtremConsole.error(e);
        }
        return null;
    }
};

export const decryptJsonDocument = (content: string, encryptionKey: string): any | null => {
    try {
        const key = PBKDF2(encryptionKey, enc.Utf8.parse(salt), { keySize: 512 / 32, iterations: 1000 });
        const decryptedFromText = AES.decrypt(content, key, options);
        if (!decryptedFromText) {
            return null;
        }
        const decryptedString = decryptedFromText.toString(enc.Utf8);

        if (!decryptedString) {
            return null;
        }

        return JSON.parse(decryptedString);
    } catch (e) {
        if (isDevMode()) {
            xtremConsole.error('Failed to decrypt cache entry.');
            xtremConsole.error(e);
        }
        return null;
    }
};
