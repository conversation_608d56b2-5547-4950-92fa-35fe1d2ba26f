import type { AsyncOperation } from '@sage/xtrem-client';
import { type Dict, objectKeys } from '@sage/xtrem-shared';
import { camelCase } from 'lodash';
import type { Store } from 'redux';
import type { CustomContentSupportedControlObjects } from '../component/container/dialog/body/custom-content';
import type { PageControlObject, SectionControlObject } from '../component/control-objects';
import { getFieldTitle } from '../component/field/carbon-helpers';
import * as xtremRedux from '../redux';
import type {
    AsyncLoaderDialogOptions,
    CustomDialogContentType,
    CustomDialogOptions,
    DialogButton,
    DialogButtonOptions,
    DialogContentType,
    DialogDescription,
    DialogLevel,
    DialogOptions,
    DialogType,
    IDialogControl,
    LookupDialogContent,
    LookupDialogOptions,
    PageDialogOptions,
    TableSidebarDialogContent,
} from '../types/dialogs';
import { xtremConsole } from '../utils/console';
import { DUPLICATE_INDICATOR_QUERY_EDITOR_PARAM, ELEMENT_ID_APPLICATION_CODE_LOOKUP } from '../utils/constants';
import { applyDefaultValuesOnNestedField, getDefaultColumnsFromDataType } from '../utils/data-type-utils';
import { triggerScreenEvent } from '../utils/events';
import { resolveByValue } from '../utils/resolve-value-utils';
import { schemaTypeNameFromNodeName, splitValueToMergedValue } from '../utils/transformers';
import type { QueryParameters } from '../utils/types';
import { isDevMode } from '../utils/window';
import { localize } from './i18n-service';
import { fetchNodeDetails } from './node-information-service';
import type { PageDefinition } from './page-definition';
import { PromiseTracker } from './promise-tracker';
import { getScreenElement } from './screen-base-definition';

const ASYNC_MUTATION_POLLING_INTERVAL = 5000;

let nextDialogId = 0;
let isDialogPromiseTrackerActive = false;

/** For unit tests only */
export const resetDialogIds = (): void => {
    nextDialogId = 0;
};

const triggerOnCloseActions = async ({
    dialogId,
    isWizardFinished,
    suppressDiscardEvent = false,
}: {
    dialogId: number;
    isWizardFinished?: boolean;
    suppressDiscardEvent?: boolean;
}): Promise<void> => {
    const store = xtremRedux.getStore();
    const state = store.getState();
    const activeDialog = state.activeDialogs[dialogId];
    const thunkDispatch = store.dispatch as xtremRedux.AppThunkDispatch;

    if (isDialogPromiseTrackerActive) {
        isDialogPromiseTrackerActive = false;
        PromiseTracker.popDialog();
    }
    if (
        activeDialog &&
        activeDialog.type === 'page' &&
        activeDialog.screenId &&
        state.screenDefinitions[activeDialog.screenId]
    ) {
        const screenId = activeDialog.screenId;
        await triggerScreenEvent(screenId, 'onClose', isWizardFinished);
    }

    if (activeDialog && activeDialog.type === 'table-sidebar' && activeDialog.screenId) {
        await thunkDispatch(xtremRedux.actions.closeDialog(dialogId));
        thunkDispatch(xtremRedux.actions.closeTableSidebar(dialogId, suppressDiscardEvent));
    } else {
        thunkDispatch(xtremRedux.actions.closeDialog(dialogId));
    }
};

type DialogArgs = {
    dialogId: number;
    screenId: string | null;
    level: DialogLevel;
    title: string | undefined;
    content: DialogContentType;
    getButtons: (
        dialogId: number,
        resolve: (value?: any) => void,
        reject: (reason?: any) => void,
    ) => Dict<DialogButton>;
    options?: PageDialogOptions & CustomDialogOptions;
    isSticker?: boolean;
    isDirtyCheck: boolean;
    subtitle?: string;
    readonly type?: DialogType;
    onClose?: () => void;
};

export class DialogControl implements IDialogControl {
    [Symbol.toStringTag]: string;

    readonly id: number;

    protected readonly dialogPromise: Promise<void>;

    private rejectPromise: (reason?: Error) => void;

    private resolvePromise: (value: any) => void;

    private readonly dialogArgs: DialogArgs;

    readonly onClose?: () => void;

    constructor(args: DialogArgs) {
        this.dialogArgs = args;
        const {
            content,
            dialogId,
            getButtons,
            isDirtyCheck = false,
            isSticker = false,
            level,
            onClose,
            options,
            screenId,
            subtitle,
            title,
            type,
        } = args;
        const thunkDispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
        this.dialogPromise = new Promise((resolve, reject) => {
            this.rejectPromise = reject;
            this.resolvePromise = resolve;
            const dialog: DialogDescription<PageDialogOptions & CustomDialogOptions> = {
                isSticker,
                isDirtyCheck,
                dialogId,
                buttons: getButtons(dialogId, resolve, reject),
                content,
                level,
                options: {
                    dialogTitle: options?.dialogTitle || undefined,
                    fullScreen: options?.fullScreen || false,
                    hasGreyBackground: options?.hasGreyBackground || false,
                    height: options?.height,
                    isDuplicate: options?.isDuplicate || false,
                    isMainListDisplayedInDialog: options?.isMainListDisplayedInDialog || false,
                    mdContent: options?.mdContent || false,
                    resolveOnCancel: options?.resolveOnCancel || false,
                    reverseButtons: options?.reverseButtons || false,
                    rightAligned: options?.rightAligned || false,
                    size: options?.size,
                    skipDirtyCheck: options?.skipDirtyCheck || false,
                    isPaddingRemoved: options?.isPaddingRemoved || false,
                },
                screenId,
                title,
                subtitle,
                dialogControl: this,
                type,
            };

            thunkDispatch(xtremRedux.actions.openDialog(dialogId, dialog));
        });
        this.onClose = onClose;
        this.id = dialogId;
        isDialogPromiseTrackerActive = true;
        PromiseTracker.pushDialog();
    }

    cancel(reason?: Error): void {
        if (this.rejectPromise) {
            if (this.dialogArgs.type === 'lookup' && this.dialogArgs.screenId) {
                const content = this.dialogArgs.content as LookupDialogContent;
                content.onLookupDialogClose?.('cancel');
            }
            this.rejectPromise(reason);
            triggerOnCloseActions({ dialogId: this.id });
        } else {
            // This branch is only reached if we call cancel right after creating the class instance;
            // in this case, the promise callback might not have been executed yet, so the rejectPromise
            // attribute might not be defined yet. In that case, we set an immediate to cancel the promise
            // right after the promise callback has been executed
            // eslint-disable-next-line @typescript-eslint/no-implied-eval
            setTimeout(this.rejectPromise, 0);
        }
    }

    catch(callback: (dialogId: number) => any): Promise<any> {
        return this.dialogPromise.catch(callback).finally(() => {
            if (this.dialogArgs.type === 'lookup' && this.dialogArgs.screenId) {
                const content = this.dialogArgs.content as LookupDialogContent;
                content.onLookupDialogClose?.('close');
            }
            this.finally.bind(this);
        });
    }

    then<TResult1 = any, TResult2 = any>(
        onFulfilled?: ((value: any) => TResult1 | PromiseLike<TResult1>) | undefined | null,
        onRejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null,
    ): Promise<TResult1 | TResult2> {
        return this.dialogPromise
            .then(onFulfilled, onRejected)
            .then(value => {
                if (this.dialogArgs.type === 'lookup' && this.dialogArgs.screenId) {
                    const content = this.dialogArgs.content as LookupDialogContent;
                    content.onLookupDialogClose?.('select');
                }
                return value;
            })
            .finally(this.finally.bind(this));
    }

    finally(onFinally?: (() => void) | null | undefined): Promise<any> {
        return this.dialogPromise.finally(() => {
            onFinally?.();
        });
    }

    resolve(value: any): void {
        if (this.resolvePromise) {
            this.resolvePromise(value);
        }
        const thunkDispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
        thunkDispatch(xtremRedux.actions.closeDialog(this.id));
    }
}

export class PageDialogControl extends DialogControl {
    [Symbol.toStringTag]: string;

    constructor(dialogId: number, options?: PageDialogOptions) {
        super({
            dialogId,
            screenId: null,
            level: 'info',
            title: localize('@sage/xtrem-ui/dialog-loading', 'Loading...'),
            content: null,
            getButtons: () => ({}),
            options,
            isSticker: false,
            isDirtyCheck: false,
            subtitle: '',
            type: 'page',
        });
    }

    resolve(value: Dict<any>): void {
        super.resolve(value);
    }

    finally(onFinally?: (() => void) | null | undefined): Promise<any> {
        return this.promise.finally(onFinally);
    }

    get promise(): Promise<void> {
        return this.dialogPromise;
    }
}

export const closeDialog = async ({
    dialogId,
    result,
    isWizardCompleted,
    suppressDiscardEvent = false,
}: {
    dialogId: number;
    result?: any;
    isWizardCompleted?: boolean;
    suppressDiscardEvent?: boolean;
}): Promise<void> => {
    const serializedResult = result ? JSON.parse(JSON.stringify(result)) : undefined;
    const activeDialog = xtremRedux.getStore().getState().activeDialogs[dialogId];
    if (activeDialog?.screenId) {
        await triggerOnCloseActions({ dialogId, isWizardFinished: isWizardCompleted, suppressDiscardEvent });
        activeDialog.dialogControl.resolve(serializedResult);
    } else {
        throw new Error(`Cannot close dialog with invalid id: ${dialogId}`);
    }
};

export const createDialog = <T extends DialogOptions = DialogOptions>(
    screenId: string,
    level: DialogLevel,
    title: string | undefined,
    content: DialogContentType,
    getButtons: (
        dialogId: number,
        resolve: (value?: any) => void,
        reject: (reason?: any) => void,
    ) => Dict<DialogButton>,
    options?: T,
    isSticker = false,
    type?: DialogType,
    onClose?: () => void,
): DialogControl => {
    nextDialogId += 1;
    return new DialogControl({
        dialogId: nextDialogId,
        screenId,
        level,
        title,
        content,
        getButtons,
        options,
        isSticker,
        isDirtyCheck: options?.isDirtyCheck || false,
        subtitle: undefined,
        type,
        onClose,
    });
};

export const createStickerDialog = (
    store: Store<xtremRedux.XtremAppState, xtremRedux.AppAction>,
    screenId: string,
    level: DialogLevel,
    title: string | undefined,
    content: SectionControlObject,
    options?: CustomDialogOptions,
): DialogControl => {
    const dialogControl = createDialog(screenId, level, title, content, () => ({}), options, true);
    const onFinish = (result: any): void => {
        closeDialog({ dialogId: dialogControl.id, result });
    };
    store.dispatch(xtremRedux.actions.setStickerDialogId(screenId, dialogControl.id, onFinish));
    return dialogControl;
};

export const getDialogButton = (
    dialogId: number,
    callback: (value: any) => void,
    defaultText: string,
    buttonConfiguration?: DialogButtonOptions,
): DialogButton => ({
    className: buttonConfiguration?.className,
    isDisabled: buttonConfiguration?.isDisabled,
    isHidden: buttonConfiguration?.isHidden,
    isNegative: buttonConfiguration?.isNegative,
    isDestructive: buttonConfiguration?.isDestructive,
    onClick: (value: any): void => {
        triggerOnCloseActions({ dialogId });
        callback(value);
    },
    text: buttonConfiguration?.text || defaultText,
});

const DEFAULT_CONFIRMATION_DIALOG_BUTTON_OPTION = {
    className: 'e-ui-confirmation-dialog-button',
};

export const getAcceptButton = (
    dialogId: number,
    resolve: (value?: any) => void,
    buttonConfiguration?: DialogButtonOptions,
): DialogButton =>
    getDialogButton(dialogId, resolve, localize('@sage/xtrem-ui/ok', 'OK'), {
        ...DEFAULT_CONFIRMATION_DIALOG_BUTTON_OPTION,
        ...buttonConfiguration,
    });

export const getCancelButton = (
    dialogId: number,
    callback: (reason?: any) => void,
    buttonConfiguration?: DialogButtonOptions,
): DialogButton =>
    getDialogButton(dialogId, () => callback(), localize('@sage/xtrem-ui/cancel', 'Cancel'), {
        ...DEFAULT_CONFIRMATION_DIALOG_BUTTON_OPTION,
        ...buttonConfiguration,
        isNegative: true,
    });

export const getConfirmationButtons =
    (options?: DialogOptions) =>
    (dialogId: number, resolve: (value?: any) => void, reject: (reason?: any) => void): Dict<DialogButton> => ({
        accept: getAcceptButton(dialogId, resolve, options?.acceptButton),
        cancel: getCancelButton(dialogId, options?.resolveOnCancel ? resolve : reject, options?.cancelButton),
    });

export const errorDialog = (screenId: string, title: string, error: Error): void => {
    const getButtons = (dialogId: number, resolve: (value?: any) => void): Dict<DialogButton> => {
        return {
            accept: getAcceptButton(dialogId, resolve),
        };
    };

    xtremConsole.error('An error ocurred creating the dialog', error);

    createDialog(screenId, 'error', title, error, getButtons, undefined, false, 'error').catch((dialogError: any) => {
        if (isDevMode()) {
            xtremConsole.log('An error ocurred creating the dialog', dialogError);
        }
    });
};

export const confirmationDialog = (
    screenId: string,
    level: DialogLevel,
    title: string,
    message: string,
    options?: DialogOptions,
): DialogControl =>
    createDialog(screenId, level, title, message, getConfirmationButtons(options), options, false, 'confirmation');

export const messageDialog = (
    screenId: string,
    level: DialogLevel,
    title: string | undefined,
    message: string,
    options?: DialogOptions,
): DialogControl => {
    const getButtons = (dialogId: number, resolve: (value?: any) => void): Dict<DialogButton> => {
        return {
            accept: getAcceptButton(dialogId, resolve, options && options.acceptButton),
        };
    };

    return createDialog(screenId, level, title, message, getButtons, options, false, 'message');
};

export const customDialog = (
    screenId: string,
    level: DialogLevel,
    content: CustomDialogContentType,
    options?: CustomDialogOptions,
): DialogControl => {
    const sections: Array<CustomContentSupportedControlObjects> = content instanceof Array ? content : [content];
    const sectionTitle: string = options?.dialogTitle || getFieldTitle(screenId, sections[0], null) || '';

    return createDialog(
        screenId,
        level,
        sectionTitle,
        sections,
        getConfirmationButtons(options),
        options,
        false,
        'custom',
    );
};
export const asyncLoaderDialog = (
    screenId: string,
    level: DialogLevel,
    nodeName: string,
    asyncOperationName: string,
    parameters: any,
    selector: any,
    options?: AsyncLoaderDialogOptions,
): DialogControl => {
    // TODO: Once we have the websocket notification, we should increase the poll frequency to a higher value
    const store = xtremRedux.getStore();
    const state = store.getState();
    const screenElement = getScreenElement(state.screenDefinitions[screenId]);
    const operation = screenElement.$.graph.node(nodeName).asyncOperations[asyncOperationName] as AsyncOperation<
        any,
        any
    >;

    // The value is set once the dialog is opened
    let dialogControl: DialogControl;
    // The value is set once the operation is successfully started
    let mutationTrackingId: string | undefined;

    const onTrackProgress = (scheduleNewCheck = false): void => {
        if (!xtremRedux.getStore().getState().activeDialogs[dialogControl.id]) {
            // If the dialog is closed, stop tracking
            return;
        }

        if (mutationTrackingId) {
            operation
                .track({ status: true, result: selector, errorMessage: true }, { trackingId: mutationTrackingId })
                .execute()
                .then(response => {
                    if (response.status === 'success') {
                        onFinishTracking(response.result);
                    } else if (response.status === 'error') {
                        const error = new Error(
                            response.errorMessage ||
                                localize(
                                    '@sage/xtrem-ui/async-operation-error',
                                    'An error occurred while processing the operation.',
                                ),
                        );
                        onFinishTracking(null, error);
                    } else if (scheduleNewCheck) {
                        setTimeout(() => onTrackProgress(true), ASYNC_MUTATION_POLLING_INTERVAL);
                    }
                });
        }
    };

    const onWebsocketNotification = ({ trackingId, status }: { status: string; trackingId: string }): void => {
        if (trackingId === mutationTrackingId && (status === 'success' || status === 'error')) {
            onTrackProgress();
        }
    };

    /**
     * This function is called when the async operation is completed, either successfully or with an error.
     * It unsubscribes from the websocket notification and resolves or rejects the dialog control based on the result.
     */
    const onFinishTracking = (result: any = null, error?: Error): void => {
        store.dispatch({
            type: xtremRedux.ActionType.UnsubscribeFromEvent,
            value: {
                category: 'asyncMutationComplete',
                callback: onWebsocketNotification,
            },
        });

        if (dialogControl) {
            if (error) {
                dialogControl.cancel(error);
            } else {
                dialogControl.resolve(result);
            }
        }
    };

    /**
     * This function is called when the user clicks on the "Notify Me" button or closes the dialog using the X button.
     * It will request the user notification for the async operation and then finish tracking.
     */
    const onNotifyMe = (): void => {
        if (mutationTrackingId) {
            operation
                .requestUserNotification(true, { trackingId: mutationTrackingId })
                .execute()
                .then(() => {
                    onFinishTracking();
                });
        }
    };

    /**
     * This function is called when the user clicks on the "Stop" button.
     * It will stop the async operation and finish tracking.
     */
    const onStop = (): void => {
        operation
            .stop(true, { trackingId: mutationTrackingId!, reason: 'USER_CANCELLATION' })
            .execute()
            .then(() => {
                onFinishTracking();
            });
    };

    // Subscribe to the websocket notification for the async operation
    store.dispatch({
        type: xtremRedux.ActionType.SubscribeToEvent,
        value: {
            category: 'asyncMutationComplete',
            callback: onWebsocketNotification,
        },
    });

    // Start the operation and track its progress
    operation
        .start({ trackingId: true }, parameters)
        .execute()
        .then(({ trackingId }: { trackingId: string }) => {
            mutationTrackingId = trackingId;
            setTimeout(() => onTrackProgress(true), ASYNC_MUTATION_POLLING_INTERVAL);
        })
        .catch((error: Error) => {
            onFinishTracking(null, error);
            throw error; // Re-throw the error to be caught in the catch block below
        });

    const dialogTitle =
        options?.dialogTitle ||
        localize('@sage/xtrem-ui/async-mutation-dialog-title', 'Your operation is still in progress');
    const dialogContent =
        options?.dialogContent ||
        localize(
            '@sage/xtrem-ui/async-mutation-dialog-content',
            'The process is taking longer than expected. You can go back and try again later without losing your work. You can keep waiting or let the process run in the background.',
        );
    dialogControl = createDialog(
        screenId,
        level,
        dialogTitle,
        {
            onNotifyMe,
            onStop,
            isStopAvailable: options?.isStopAvailable || false,
            dialogTitle,
            dialogContent,
        },
        () => ({}),
        {
            size: 'medium-small',
            dialogTitle,
            dialogContent,
            isStopAvailable: options?.isStopAvailable || false,
            resolveOnCancel: true,
        },
        false,
        'async-loader',
        onNotifyMe,
    );

    return dialogControl;
};

export async function customLookupDialog(
    store: { dispatch: xtremRedux.AppThunkDispatch; getState: () => xtremRedux.XtremAppState },
    screenId: string,
    options: LookupDialogOptions<any>,
): Promise<any[]> {
    const locale = store.getState()?.applicationContext?.locale || 'en-US';
    await fetchNodeDetails({ locale, nodeName: schemaTypeNameFromNodeName(options.node) });
    // We need a fresh copy here
    const { dataTypes, nodeTypes } = store.getState();
    const dataType = dataTypes[camelCase(schemaTypeNameFromNodeName(options.node))];
    const columns =
        options.columns ||
        getDefaultColumnsFromDataType({
            dataTypes,
            nodeTypes,
            targetNode: options.node,
            ignoreCardDefinition: true,
            dataType,
        });

    columns.forEach(column => {
        applyDefaultValuesOnNestedField(nodeTypes, dataTypes, column, options.node);
    });

    const fieldId = options.id
        ? `${options.id}_${ELEMENT_ID_APPLICATION_CODE_LOOKUP}`
        : ELEMENT_ID_APPLICATION_CODE_LOOKUP;

    const selectedRecords = options.selectedRecordId
        ? Array.isArray(options.selectedRecordId)
            ? options.selectedRecordId
            : [options.selectedRecordId]
        : [];
    store.dispatch(xtremRedux.actions.setFieldProperties(screenId, fieldId, { selectedRecords } as any));

    return lookupDialog(screenId, 'info', {
        ...options,
        fieldId,
        selectedRecordId: options.selectedRecordId,
        fieldProperties: {
            isTransient: true,
            columns,
            node: options.node,
            filter: options.filter,
            orderBy: options.orderBy,
            tunnelPage: options.tunnelPage ?? dataType?.tunnelPage,
            mapServerRecord: options.mapServerRecord,
        },
    });
}

export const lookupDialog = (
    screenId: string,
    level: DialogLevel,
    options: LookupDialogContent & DialogOptions,
): DialogControl => {
    const title =
        options.dialogTitle ||
        resolveByValue<string>({
            screenId,
            propertyValue: options.fieldProperties.lookupDialogTitle,
            skipHexFormat: true,
            rowValue: splitValueToMergedValue(options.recordContext || {}),
        }) ||
        getFieldTitle(screenId, options.fieldProperties, options.recordContext) ||
        localize('@sage/xtrem-ui/lookup-dialog-dialog-title', 'Selection');

    xtremRedux.getStore().getState().applicationContext?.onTelemetryEvent?.(`lookupOpened-${options.fieldId}`, {
        elementId: options.fieldId,
    });

    return createDialog(screenId, level, title, options, getConfirmationButtons(options), options, false, 'lookup');
};

export const internalDialog = (
    screenId: string,
    level: DialogLevel,
    content: React.ReactElement,
    options?: CustomDialogOptions,
): DialogControl => {
    const title = options?.dialogTitle;

    return createDialog(screenId, level, title, content, getConfirmationButtons(), {}, false, 'internal');
};

export function resolvePageDialog(resolutionValue: any): void {
    const activeDialogs = objectKeys(xtremRedux.getStore().getState().activeDialogs);
    const lastActiveDialog = activeDialogs[activeDialogs.length - 1];
    xtremRedux.getStore().getState().activeDialogs[lastActiveDialog].dialogControl.resolve(resolutionValue);
}

export async function pageDialog<T>(
    store: { dispatch: xtremRedux.AppThunkDispatch; getState: () => xtremRedux.XtremAppState },
    path: string,
    queryParameters: QueryParameters = {},
    options?: PageDialogOptions,
): Promise<void | T> {
    nextDialogId += 1;
    const dialogId = nextDialogId;
    const control = new PageDialogControl(dialogId, options);

    const onFinish = (result: any): void => {
        closeDialog({ dialogId, result });
    };

    const screenId = await xtremRedux.actions.loadPageDialogContent(
        path,
        queryParameters,
        options?.values,
        onFinish,
        options?.isDuplicate,
    )(store.dispatch, store.getState);

    if (!screenId) {
        throw new Error(localize('@sage/xtrem-ui/page-failed-to-load', 'Failed to load page'));
    }
    const state = store.getState();
    const screenDefinition = state.screenDefinitions[screenId];
    const pageControlObject = screenDefinition.metadata.controlObjects[screenId] as PageControlObject;

    // Check if the dialog is still open, it could be closed while the page definition was being retrieved
    if (state.activeDialogs[control.id]) {
        store.dispatch(
            xtremRedux.actions.setScreenDefinitionDialogId(
                screenId,
                dialogId,
                pageControlObject,
                options?.title || getFieldTitle(screenId, pageControlObject, null) || '',
                options?.subtitle || pageControlObject.subtitle || '',
                !!(screenDefinition as PageDefinition)?.queryParameters?.[DUPLICATE_INDICATOR_QUERY_EDITOR_PARAM],
            ),
        );
        // Here a fresh copy of the state is needed.
        state.applicationContext?.updateMenu(store.getState()!.menuItems);
    }

    return control.promise;
}

export async function openPageDialog<T>(
    path: string,
    queryParameters: QueryParameters = {},
    options?: PageDialogOptions,
): Promise<void | T> {
    return pageDialog<T>(xtremRedux.getStore(), path, queryParameters, options);
}

export function openTableSidebarDialog(
    args: {
        screenId: string;
    } & TableSidebarDialogContent,
): DialogControl {
    return createDialog(
        args.screenId,
        'info',
        localize('@sage/xtrem-ui/dialog-loading', 'Loading...'),
        args,
        getConfirmationButtons(),
        {
            rightAligned: true,
        },
        false,
        'table-sidebar',
    );
}
