PATH: XTREEM/Client+Framework/Client+I18n+API

This page describes how to use and design client side artifacts that are suitable for internationalization and translation by the content team.

## Introduction

For making string literals available for translation, there are two distinctive methods in the UI framework. One is the decorator-driven approach, the other is the `localize` function. On this page both of them described and examples are provided. In addition to these approaches, string resolution logic and best practices are also discussed.

## Making content available for localization

#### Decorator-driven approach

The decorator-driven approach is automated, there is not much to take care of as an application developer, but there are some rules to keep in mind.
On compilation, the framework automatically extracts string literals from given decorator properties: `title` and `helperText`. The content of these fields **must** be a string literal, variables and other references are not allowed.
Based on the location of the decorator (such as being a field title or a table column), the framework will generate keys automatically for these string literals and puts them together to a JSON file.

Example:
In case of the following field, the values of the `title`, `helperText` and `placeholder` values will be extracted, but the `prefix` field will be left as is given it represents a unit not and not a translatable string literal.

```ts
@ui.decorators.numericField<NumericField>({
    parent() {
        return this.numericBlock;
    },
    title: 'A numeric field',
    prefix: 'kg',
    helperText: 'This text goes underneath the field',
})
aSimpleNumericField: ui.fields.Numeric;
```

#### Localize function driven approach

Not all translatable content is located on decorators, for these you can use the `localize` function which is available on the root of the UI framework library.
The `localize` function takes 3 parameters:

-   **key:** The key must start with the package's name (such as `@sage/x3-project-management/`), the key and content combination **must** be unique in a package, reusing the same key with different content is not allowed and will result a compiler error.
-   **content:** The content of the message which gets extracted and made available for the translation team. The content is processed by the handlebars library and can contain handlebars-style variables, for more info on that please click [here](https://handlebarsjs.com/).
-   **parameters:** _(optional)_ The parameters that are used to populate the string content literal, it can be an array or an object.

```ts
import * as ui from '@sage/xtrem-ui';

onClick() {
    this.$.dialog.confirmation('warn',
        ui.localize('@sage/x3-my-package/delete-dialog-title', 'Delete'),
        ui.localize('@sage/x3-my-package/delete-dialog-content', 'Do you really want to delete {{numOfItems}} items?', {numOfItems: 10}),
    );
}
```

There is also a function available especially for looking up localized enum member names which looks up the literal by the name of the enum and the member name.

```ts
import * as ui from '@sage/xtrem-ui';
    ui.localizeEnumMember('@sage/x3-my-package/MyEnumName', 'inProgress'),
}
```

Please note that it works automatically for select fields as long as you provide the `optionType` decorator property.

## Location in the file structure

The string literals are placed to the `lib/i18n` folder. The literals from the code are extracted directly to the `base.json` file, they expected to be in pure English. The localization team reviews this content and creates the British (`en-GB.json`) and the US (`en-US.json`) English versions. The keys are written to all other localized JSON files (such as `es-ES.json` or `en-GB.json`) with empty values.

## String resolution

If a string is not found in the requested language, the framework attempts to resolve find the _best match_ based on the locale. All locales that the system support has a fallback path to other locales. It iterates over the fallback path until it finds a match.

| Locale | Fallback path                            |
| ------ | ---------------------------------------- |
| en-GB  | en-GB -> en-US -> base                   |
| en-US  | en-US -> en-GB -> base                   |
| fr-FR  | fr-FR -> en-US -> en-GB -> base          |
| es-ES  | es-ES -> en-US -> en-GB -> base          |
| de-DE  | de-DE -> en-US -> en-GB -> base          |
| it-IT  | it-IT -> en-US -> en-GB -> base          |
| zh-CN  | zh-CN -> en-US -> en-GB -> base          |
| pl-PL  | pl-PL -> en-US -> en-GB -> base          |
| pt-PT  | pt-PT -> pt-BR -> en-US -> en-GB -> base |
| pt-BR  | pt-BR -> pt-PT -> en-US -> en-GB -> base |
| ar-SA  | ar-SA -> en-US -> en-GB -> base          |

### Notes

-   Whenever the content of an existing literal changes in the code, all translations will be removed so the localization team is aware of that the literal needs to be retranslated.
-   In order to get the strings extracted to the JSON files, a proper compilation (`pnpm run build`) is needed, the strings are not extracted in watch mode, neither changes to the localization files are detected in watch mode.

## Localize a date
You can format and localize a date by using `ui.formatDateToCurrentLocale` and its presets.

It's convenient because it will adapt to each user's language automatically.
### `formatDateToCurrentLocale(date, presetFormat?)`
### Parameters

| parameter | type    | description                                      |
| --------- | ------- | ------------------------------------------------ |
| `date`     | Date or String  | A Javascript native Date or a String in iso format ('YYYY-MM-DD')       |
| `presetFormat`  | DatePresetFormat | Optional. Defaults to FullDate. Available values: 'FullDate', 'LongMonth', 'LongMonthYear', 'MonthDay' and MonthYear'.           |

**Returns** `String`, the localized and formated date as a string.

### Example

```typescript
import * as ui from '@sage/xtrem-ui';

@ui.decorators.dateField<DateField>({
    parent() {
        return this.block;
    },
    title: 'Date',
    onChange() {
        ui.formatDateToCurrentLocale(this.value.value, 'LongMonthYear');
    },
})
value: ui.fields.Date;
```
