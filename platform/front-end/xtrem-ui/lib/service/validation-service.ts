import { DateValue, datePropertyValueToDateString } from '@sage/xtrem-date-time';
import type { Dict, WorkflowError } from '@sage/xtrem-shared';
import { get } from 'lodash';
import type { UiComponentProperties } from '../component/abstract-ui-control-object';
import type { PodProperties, ReferenceProperties } from '../component/control-objects';
import type {
    DateDecoratorProperties,
    MultiDropdownDecoratorProperties,
    NumericDecoratorProperties,
    TextAreaDecoratorProperties,
    TextDecoratorProperties,
} from '../component/decorators';
import type { EditableFieldProperties } from '../component/editable-field-control-object';
import { getReferenceValueField } from '../component/field/reference/reference-utils';
import type { NestedField, NestedFieldTypes } from '../component/nested-fields';
import type { ComponentKey } from '../component/types';
import { FieldKey } from '../component/types';
import type { XtremAppState } from '../redux';
import { getStore } from '../redux';
import type { NodePropertyType } from '../types';
import { findColumnDefinitionByBind, getContainerChildFields } from '../utils/abstract-fields-utils';
import { getElementAccessStatus } from '../utils/access-utils';
import { xtremConsole } from '../utils/console';
import { dateToString } from '../utils/formatters';
import { isHiddenOrDisabledInLayout } from '../utils/layout-utils';
import { convertDeepBindToPathNotNull } from '../utils/nested-field-utils';
import { resolveByValue } from '../utils/resolve-value-utils';
import { checkIfPageIsLoaded } from '../utils/state-utils';
import { splitValueToMergedValue } from '../utils/transformers';
import { isNumber } from '../utils/type-utils';
import { isDevMode } from '../utils/window';
import { CollectionValue } from './collection-data-service';
import { localize } from './i18n-service';
import type { DataTypeDetails, FormattedNodeDetails } from './metadata-types';
import type { AccessBindings } from './page-definition';
import type { ScreenBase } from './screen-base';
import type { ValidationResult } from './screen-base-definition';
import { getScreenElement } from './screen-base-definition';

type ExtendedFieldProperties = EditableFieldProperties &
    TextDecoratorProperties &
    TextAreaDecoratorProperties &
    NumericDecoratorProperties &
    DateDecoratorProperties &
    MultiDropdownDecoratorProperties;

function isDatetimeRangeValue(val: any): val is { start: any; end: any } {
    return val && typeof val === 'object' && 'start' in val && 'end' in val;
}

function getMandatoryFieldErrorMessage(fieldType?: ComponentKey | null): string {
    switch (fieldType) {
        case FieldKey.Select:
        case FieldKey.DropdownList:
        case FieldKey.MultiDropdown:
            return localize('@sage/xtrem-ui/field-select-mandatory', 'You need to select a value.');
        case FieldKey.MultiReference:
        case FieldKey.Reference:
        case FieldKey.Date:
        case FieldKey.Datetime:
        case FieldKey.DatetimeRange:
            return localize('@sage/xtrem-ui/field-select-mandatory-or-enter', 'You need to select or enter a value.');
        case FieldKey.Checkbox:
            return localize('@sage/xtrem-ui/field-select-mandatory-checkbox', 'You need to select this checkbox.');
        default:
            return localize('@sage/xtrem-ui/field-mandatory', 'You need to enter a value.');
    }
}

export const CHILDREN_VALIDATION_ERROR_MESSAGE = (): string =>
    localize('@sage/xtrem-ui/validation-error-of-children', 'Validation error in children records');

export const CHILDREN_VALIDATION_RULE = (): string => 'children-records';

export const executeValidationRulesOnField = async ({
    screenId,
    elementId,
    fieldProperties,
    value,
    rowValue: inputRowValue,
    columnId,
    recordId,
}: {
    screenId: string;
    elementId: string;
    fieldProperties?: ExtendedFieldProperties;
    value: any;
    rowValue?: Dict<any>;
    columnId?: string;
    recordId?: string;
}): Promise<ValidationResult[]> => {
    const fieldType = fieldProperties?._controlObjectType;
    const rowValue = inputRowValue ? splitValueToMergedValue(inputRowValue) : inputRowValue;
    // Fields Validation
    const noErrors = [
        {
            elementId,
            screenId,
            message: '',
            validationRule: '',
            columnId,
            recordId,
        },
    ];

    // If the field properties are not available, then no validation can be executed.
    if (!fieldProperties) {
        return noErrors;
    }

    const titleOrId =
        resolveByValue({
            screenId,
            propertyValue: fieldProperties.title,
            skipHexFormat: true,
            rowValue: undefined,
            fieldValue: undefined,
        }) ||
        columnId ||
        elementId;

    const isNotZeroValue = resolveByValue({
        screenId,
        propertyValue: fieldProperties.isNotZero,
        skipHexFormat: true,
        rowValue: undefined, // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
        fieldValue: rowValue, // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
    });

    const isMandatoryValue = resolveByValue({
        screenId,
        propertyValue: fieldProperties.isMandatory,
        skipHexFormat: true,
        rowValue: undefined, // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
        fieldValue: rowValue, // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
    });

    const isNullReference =
        !(value instanceof Array) &&
        fieldProperties._controlObjectType === FieldKey.Reference &&
        get(value, getReferenceValueField(fieldProperties as ReferenceProperties<any>), null) === null;
    // IsMandatory Validation
    if (
        (isMandatoryValue || isNotZeroValue) &&
        ((!value && value !== 0) || // Numeric field values
            isNullReference ||
            (value instanceof Array && value.length === 0)) // Array field values (multi reference, multi select)
    ) {
        return [
            {
                elementId,
                screenId,
                message: getMandatoryFieldErrorMessage(fieldType),
                validationRule: 'isMandatory',
                columnId,
                recordId,
            },
        ];
    }

    // isNotZero validation
    if (isNotZeroValue && parseFloat(value) === 0) {
        return [
            {
                elementId,
                screenId,
                message: localize('@sage/xtrem-ui/field-non-zero', '{{0}} cannot be zero', [titleOrId]),
                validationRule: 'isNotZero',
                columnId,
                recordId,
            },
        ];
    }

    const minValidationValue = resolveByValue({
        screenId,
        propertyValue: fieldProperties.min,
        skipHexFormat: true,
        rowValue: undefined, // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
        fieldValue: rowValue, // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
    });
    // Numeric min-max validation
    if (isNumber(minValidationValue) && value < minValidationValue) {
        return [
            {
                elementId,
                screenId,
                message: localize('@sage/xtrem-ui/field-minimum-value', '{{0}} minimum value is {{1}}', [
                    titleOrId,
                    minValidationValue,
                ]),
                validationRule: 'min',
                columnId,
                recordId,
            },
        ];
    }

    const maxValidationValue = resolveByValue({
        screenId,
        propertyValue: fieldProperties.max,
        skipHexFormat: true,
        rowValue: undefined, // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
        fieldValue: rowValue, // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
    });
    if (isNumber(maxValidationValue) && value > maxValidationValue) {
        return [
            {
                elementId,
                screenId,
                message: localize('@sage/xtrem-ui/field-maximum-value', '{{0}} maximum value is {{1}}', [
                    titleOrId,
                    maxValidationValue,
                ]),
                validationRule: 'max',
                columnId,
                recordId,
            },
        ];
    }

    // Date min-max date validation
    const minDateValidationValue = resolveByValue({
        screenId,
        propertyValue: fieldProperties.minDate,
        skipHexFormat: true,
        rowValue: undefined, // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
        fieldValue: rowValue, // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
    });
    const dateToCompareMin = isDatetimeRangeValue(value) ? value.start : value;
    if (
        minDateValidationValue &&
        dateToCompareMin &&
        DateValue.parse(datePropertyValueToDateString(dateToCompareMin)!).compare(
            DateValue.parse(datePropertyValueToDateString(minDateValidationValue)!),
        ) < 0
    ) {
        const minDate: Date = minDateValidationValue as Date;
        const stringMinDate: string = dateToString(minDate, localize('@sage/xtrem-ui/date-format', 'DD/MM/YYYY'));

        return [
            {
                elementId,
                screenId,
                message: localize('@sage/xtrem-ui/field-minimum-date-value', '{{0}} minimum date is {{1}}', [
                    titleOrId,
                    stringMinDate,
                ]),
                validationRule: 'minDate',
                columnId,
                recordId,
            },
        ];
    }

    const maxDateValidationValue = resolveByValue({
        screenId,
        propertyValue: fieldProperties.maxDate,
        skipHexFormat: true,
        rowValue: undefined, // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
        fieldValue: rowValue, // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
    });
    const dateToCompareMax = isDatetimeRangeValue(value) ? value.end : value;
    if (
        maxDateValidationValue &&
        dateToCompareMax &&
        DateValue.parse(datePropertyValueToDateString(dateToCompareMax)!).compare(
            DateValue.parse(datePropertyValueToDateString(maxDateValidationValue)!),
        ) > 0
    ) {
        const maxDate: Date = maxDateValidationValue as Date;
        const stringMaxDate: string = dateToString(maxDate, localize('@sage/xtrem-ui/date-format', 'DD/MM/YYYY'));

        return [
            {
                elementId,
                screenId,
                message: localize('@sage/xtrem-ui/field-maximum-date-value', '{{0}} maximum date is {{1}}', [
                    titleOrId,
                    stringMaxDate,
                ]),
                validationRule: 'maxDate',
                columnId,
                recordId,
            },
        ];
    }

    // Text field max-min length validation
    const minLengthValue = resolveByValue({
        screenId,
        propertyValue: fieldProperties.minLength,
        skipHexFormat: true,
        rowValue: undefined, // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
        fieldValue: rowValue, // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
    });
    if (minLengthValue && String(value) && String(value).length < minLengthValue) {
        return [
            {
                elementId,
                screenId,
                message: localize('@sage/xtrem-ui/field-minimum-length-value', '{{0}} minimum length is {{1}}', [
                    titleOrId,
                    minLengthValue,
                ]),
                validationRule: 'minLength',
                columnId,
                recordId,
            },
        ];
    }

    const maxLengthValue = resolveByValue({
        screenId,
        propertyValue: fieldProperties.maxLength,
        skipHexFormat: true,
        rowValue: undefined, // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
        fieldValue: rowValue, // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
    });
    if (maxLengthValue && value && value.length > maxLengthValue) {
        return [
            {
                elementId,
                screenId,
                message: localize('@sage/xtrem-ui/field-maximum-length-value', '{{0}} maximum length is {{1}}', [
                    titleOrId,
                    maxLengthValue,
                ]),
                validationRule: 'maxLength',
                columnId,
                recordId,
            },
        ];
    }

    const minItems = resolveByValue({
        screenId,
        propertyValue: fieldProperties.minItems,
        skipHexFormat: true,
        rowValue: undefined, // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
        fieldValue: rowValue, // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
    });
    if (minItems != null && minItems > (value?.length ?? 0)) {
        return [
            {
                elementId,
                screenId,
                message: localize(
                    '@sage/xtrem-ui/field-min-items-value',
                    '{{0}} the minimum number of selectable items is {{1}}',
                    [titleOrId, minItems],
                ),
                validationRule: 'minItems',
                columnId,
                recordId,
            },
        ];
    }

    const maxItems = resolveByValue({
        screenId,
        propertyValue: fieldProperties.maxItems,
        skipHexFormat: true,
        rowValue: undefined, // Intentionally undefined, the validation rule should not depend on the value that is validated by that very rule
        fieldValue: rowValue, // Pass in the row value as the field value because the field value is passed in as the first argument to the functional code
    });
    if (maxItems != null && maxItems < (value?.length ?? 0)) {
        return [
            {
                elementId,
                screenId,
                message: localize(
                    '@sage/xtrem-ui/field-max-items-value',
                    '{{0}} the maximum number of selectable items is {{1}}',
                    [titleOrId, maxItems],
                ),
                validationRule: 'maxItems',
                columnId,
                recordId,
            },
        ];
    }

    if (value instanceof CollectionValue) {
        if (value.hasDirtyPhantomRows()) {
            const isOSX =
                (navigator as any)?.userAgentData?.platform === 'macOS' || navigator.platform?.startsWith('Mac');
            const phantomRowErrorMessage = isOSX
                ? localize(
                      '@sage/xtrem-ui/dirty-phantom-row-validation-error-message-mac',
                      'You need to select the line and validate it by selecting command + Enter, so that you can save your document.',
                  )
                : localize(
                      '@sage/xtrem-ui/dirty-phantom-row-validation-error-message-pc',
                      'You need to select the line and validate it by selecting Ctrl + Enter, so that you can save your document.',
                  );
            return [
                {
                    elementId,
                    screenId,
                    message: phantomRowErrorMessage,
                    validationRule: 'dirtyPhantomRow',
                },
            ];
        }
        if (fieldProperties.isTransient) {
            await value.validate(false);
        }
        const result = await value.validateField({ skipDispatch: true, withoutServerErrors: true });
        if (result.length > 0) {
            return result;
        }
    }

    // If we are in a pod or dynamic pod, we need to check the nested validation
    if (
        fieldProperties._controlObjectType === FieldKey.Pod ||
        fieldProperties._controlObjectType === FieldKey.DynamicPod
    ) {
        const properties = fieldProperties as PodProperties<ScreenBase>;
        let errors: ValidationResult[] = [];
        if (fieldProperties.validation) {
            const message = await resolveByValue({
                screenId,
                propertyValue: fieldProperties.validation,
                rowValue,
                fieldValue: value,
                skipHexFormat: true,
            });
            if (message && message.length > 0) {
                errors.push({
                    elementId,
                    screenId,
                    message,
                    validationRule: 'validation',
                });
            }
        }

        const columns = properties.columns as NestedField<ScreenBase, NestedFieldTypes>[];
        const nestedRecordErrors = await checkNestedRecordValidationStatus({
            screenId,
            elementId,
            rowValue: value,
            columnDefinitions: columns,
            columnsToValidate: columns.reduce<Set<string>>((prevValue, currentValue) => {
                const bind = currentValue.properties.bind;
                if (bind) {
                    prevValue.add(convertDeepBindToPathNotNull(bind));
                }
                return prevValue;
            }, new Set()),
            recordId: recordId || '',
        });
        const nestedErrors = Object.values(nestedRecordErrors) as ValidationResult[];
        errors = [...errors, ...nestedErrors];
        if (errors.length > 0) {
            return errors;
        }
        return noErrors;
    }

    const validation = fieldProperties.validation;
    if (!validation) {
        return noErrors;
    }

    if (fieldProperties._controlObjectType === FieldKey.Workflow) {
        const errors: WorkflowError[] = await resolveByValue({
            screenId,
            propertyValue: fieldProperties.validation,
            fieldValue: value,
            rowValue,
            skipHexFormat: true,
        });

        if (errors && errors.length > 0) {
            return errors.map(error => ({
                elementId,
                screenId,
                message: error.message,
                validationRule: 'validation',
                columnId,
                recordId: error.stepId,
            }));
        }
        return noErrors;
    }

    // TODO: XT-638 ADD THE NESTED VALIDATION EXECUTION HERE
    // Check if there are fieldProperties.columns and then call this method recursively

    // Regexp Validation
    if (validation instanceof RegExp) {
        if (!validation.test(String(value))) {
            return [
                {
                    elementId,
                    screenId,
                    message: localize('@sage/xtrem-ui/field-not-valid', '{{0}} is not valid', [titleOrId]),
                    validationRule: 'validation',
                    columnId,
                    recordId,
                },
            ];
        }
        return noErrors;
    }

    // Custom function validation
    try {
        const message = await resolveByValue({
            screenId,
            propertyValue: fieldProperties.validation,
            rowValue,
            fieldValue: value instanceof CollectionValue ? value.getFormattedActiveRecords() : value,
            skipHexFormat: true,
        });

        if (message) {
            return [
                {
                    elementId,
                    screenId,
                    message,
                    validationRule: 'validation',
                    columnId,
                    recordId,
                },
            ];
        }
    } catch (error) {
        if (isDevMode()) {
            xtremConsole.error(`${screenId} - ${elementId} could not be validated to due an unexpected error:`);
            xtremConsole.error(error);
        }
        return [
            {
                elementId,
                screenId,
                message: error.message ? error.message : String(error),
                validationRule: 'validation',
                columnId,
                recordId,
            },
        ];
    }

    return [
        {
            elementId,
            screenId,
            message: '',
            validationRule: '',
            columnId,
            recordId,
        },
    ];
};

export const checkNestedRecordValidationStatus = async ({
    rowValue,
    columnDefinitions,
    columnsToValidate,
    screenId,
    elementId,
    recordId,
}: {
    screenId: string;
    elementId: string;
    rowValue: Dict<any>;
    columnDefinitions: NestedField<ScreenBase, NestedFieldTypes>[];
    columnsToValidate: Set<string>;
    recordId: string;
}): Promise<Dict<ValidationResult>> => {
    const validationResults = await Promise.all(
        Array.from(columnsToValidate).map(bind =>
            executeValidationRulesOnField({
                screenId,
                elementId,
                columnId: bind,
                fieldProperties: findColumnDefinitionByBind(columnDefinitions, bind)
                    ?.properties as ExtendedFieldProperties,
                recordId,
                rowValue,
                value: get(rowValue, bind.replace('__', '.')),
            }),
        ),
    );

    return validationResults
        .flat()
        .filter(r => !!r.message)
        .reduce<Dict<ValidationResult>>((prevValue, currentValue) => {
            return {
                ...prevValue,
                [currentValue.columnId!]: currentValue,
            };
        }, {});
};
/**
 * The function checks all validation rules.
 *
 * @param screenElement
 * @param elementId
 * @param componentProperties
 * @param value
 */
export const checkValidationStatus = async (
    screenElement: ScreenBase,
    elementId: string,
    componentProperties: Dict<UiComponentProperties>,
    value: any,
    accessBindings: AccessBindings,
    nodeTypes: Dict<FormattedNodeDetails>,
    dataTypes: Dict<DataTypeDetails>,
    contextNode?: NodePropertyType,
): Promise<ValidationResult[]> => {
    const screenId = screenElement._pageMetadata.screenId;

    // Fields Validation
    const result: ValidationResult[] = [
        {
            elementId,
            screenId,
            message: '',
            validationRule: '',
        },
    ];

    const fieldProperties: ExtendedFieldProperties = componentProperties[elementId];

    if (!fieldProperties) {
        return result;
    }

    // If the user doesn't have to access to edit the field, then we don't validate it.
    const accessStatus = getElementAccessStatus({
        accessBindings,
        bind: elementId,
        elementProperties: componentProperties,
        contextNode,
        nodeTypes,
        dataTypes,
    });
    if (accessStatus === 'unavailable' || accessStatus === 'unauthorized') {
        return result;
    }

    if (
        isHiddenOrDisabledInLayout({
            componentProperties,
            screenId,
            elementId,
            accessBindings,
            contextNode,
            dataTypes,
            nodeTypes,
        })
    ) {
        return result;
    }

    return executeValidationRulesOnField({ value, fieldProperties, screenId, elementId });
};

export const getSectionValidationMessage = (
    screenId: string,
    sectionId: string,
    state: XtremAppState,
): string | null => {
    const screen = state.screenDefinitions[screenId];
    const screenElement = getScreenElement(screen);
    const controlObjects = state.screenDefinitions[screenId].metadata.controlObjects;
    const children = getContainerChildFields(screenElement, Object.values(controlObjects), sectionId);
    const totalErrors = children.flatMap(c => screen.errors[c.id]).filter(c => !!c).length;
    if (totalErrors === 0) {
        return null;
    }
    return totalErrors > 1
        ? localize('@sage/xtrem-ui/validation-errors-total', '{{0}} errors', [totalErrors])
        : localize('@sage/xtrem-ui/validation-error-total', '1 error');
};

export const getValidationState = (screenId: string, elementId: string, state = getStore().getState()): boolean => {
    checkIfPageIsLoaded(screenId, state);

    const screen = state.screenDefinitions[screenId];
    return (
        getContainerChildFields(getScreenElement(screen), Object.values(screen.metadata.controlObjects), elementId)
            .map(co => screen.errors[co.id])
            .filter(e => !!e).length === 0
    );
};
