/**
 * @packageDocumentation
 * @module root
 * */
import type { ClientError, ClientNode } from '@sage/xtrem-client';
import { type Dict, type Locale, objectKeys } from '@sage/xtrem-shared';
import type { PageActionControlObject } from '../component/control-objects';
import type { AppThunkDispatch } from '../redux';
import { ActionType, getStore } from '../redux';
import { setPageValidationErrors } from '../redux/actions';
import type { IDialogApi } from '../types/dialogs';
import { triggerScreenEvent } from '../utils/events';
import { transformServerErrorToToast } from '../utils/server-error-transformer';
import { getPageDefinitionFromState, isScreenDefinitionDirty } from '../utils/state-utils';
import { parseScreenValues } from '../utils/transformers';
import { getDialogApi } from './dialog-api';
import { notifyConsumerAboutDirtyStatus, onPageDirtyChange } from './dirty-state-service';
import type { ContainerValidationResult } from './dispatch-service';
import { GraphQLApi } from './graphql-api';
import { localize } from './i18n-service';
import type { Loader } from './loader';
import loader from './loader';
import type { PageMetadata } from './page-metadata';
import type { IRouter } from './router';
import { Router } from './router';
import { SoundApi } from './sound-api';
import * as storageService from './storage-service';
import type { ToastOptions } from './toast-service';
import { removeToasts, showContainerValidationToast, showToast } from './toast-service';
import { commitTransaction, isTransactionInProgress, setFieldValue } from './transactions-service';

export abstract class BaseDeveloperApi<TGraphqlApi, CT extends ScreenBase<TGraphqlApi>> {
    readonly router: IRouter;

    readonly dialog: IDialogApi<CT>;

    readonly loader: Loader;

    readonly sound: SoundApi;

    readonly showToast: (content: string, options?: Omit<ToastOptions, 'language'>) => void;

    readonly showValidationToast: (
        containerValidationErrors: ContainerValidationResult,
        options?: ToastOptions,
    ) => void;

    readonly removeToasts: () => void;

    constructor(screenId: string) {
        this.router = new Router(screenId);
        this.dialog = getDialogApi<CT>(screenId, getStore());
        this.loader = loader;
        this.sound = new SoundApi();
        this.showToast = (content, options): void => showToast(content, { ...options, language: 'markdown' });
        this.showValidationToast = (containerValidationErrors, options): void => {
            const state = getStore().getState();
            const pageMetadata = state.screenDefinitions[screenId].metadata;
            showContainerValidationToast(pageMetadata, containerValidationErrors, options);
        };
        this.removeToasts = removeToasts;
    }

    /**
     * Returns the unique ID (email address) of the user that is currently uses the application
     */
    get username(): string | null {
        const state = getStore().getState();
        return state.applicationContext?.login || null;
    }

    /**
     * The session username can be used if the application has a username that is different from the login user.
     */
    get userCode(): string | null {
        const state = getStore().getState();
        return state.applicationContext?.userCode || null;
    }

    get locale(): Locale | null {
        const state = getStore().getState();
        return (state.applicationContext?.locale as Locale) || null;
    }

    /** Cross-page storage object that preserves the content for the session */
    get storage(): storageService.Storage {
        return Object.seal(storageService);
    }
}
export abstract class DeveloperApi<TGraphqlApi, CT extends ScreenBase<TGraphqlApi> = any> extends BaseDeveloperApi<
    TGraphqlApi,
    CT
> {
    readonly graph: GraphQLApi<TGraphqlApi>;

    readonly router: IRouter;

    readonly dialog: IDialogApi<CT>;

    readonly loader: Loader;

    readonly showToast: (content: string, options?: ToastOptions) => void;

    readonly showValidationToast: (
        containerValidationErrors: ContainerValidationResult,
        options?: ToastOptions,
    ) => void;

    readonly removeToasts: () => void;

    constructor(protected readonly screenBase: CT) {
        super(screenBase._pageMetadata.screenId);

        this.graph = new GraphQLApi<TGraphqlApi>(screenBase);
    }

    subscribeToEvent(category: string, callback: (...args: any[]) => void): void {
        const store = getStore();
        store.dispatch({ type: ActionType.SubscribeToEvent, value: { category, callback } });
    }

    unsubscribeFromEvent(category: string, callback: (...args: any[]) => void): void {
        const store = getStore();
        store.dispatch({ type: ActionType.UnsubscribeFromEvent, value: { category, callback } });
    }

    /**
     * Returns a new object containing the values of all the page's non-transient fields.
     * It is a heavy operation that serializes all values of the page. It should only be used
     * when you need the value of most of the fields in a server friendly way.
     */
    get values(): Dict<any> {
        return parseScreenValues(this.screenBase._pageMetadata.screenId);
    }

    set values(boundValues: Dict<any>) {
        objectKeys(boundValues).forEach(key => {
            setFieldValue(this.screenBase._pageMetadata.screenId, key, boundValues[key]);
        });
    }

    get businessActions(): PageActionControlObject<ScreenBase>[] {
        const extensionActions = this.screenBase._pageMetadata.businessActionsExtensionsThunk.flatMap(e =>
            e.apply(this.screenBase),
        );
        return this.screenBase._pageMetadata.businessActionsThunk
            ? Object.seal([
                  ...this.screenBase._pageMetadata.businessActionsThunk.apply(this.screenBase),
                  ...extensionActions,
              ])
            : [];
    }

    setPageClean(): void {
        const store = getStore();
        const pageMetadata = this.screenBase._pageMetadata;
        const screenId = pageMetadata.screenId;

        store.dispatch({ type: ActionType.SetPageClean, value: screenId });

        // A fresh copy of the updated state is needed.
        notifyConsumerAboutDirtyStatus(store.getState());
        onPageDirtyChange(store.getState(), screenId, false);
        triggerScreenEvent(screenId, 'onDirtyStateUpdated', false);
    }

    /** Whether the page currently contains any dirty fields */
    get isDirty(): boolean {
        const state = getStore().getState();
        return isScreenDefinitionDirty(getPageDefinitionFromState(this.screenBase._pageMetadata.screenId, state)!);
    }

    isTransactionInProgress(): boolean {
        return isTransactionInProgress(this.screenBase._pageMetadata.screenId);
    }

    /** Update the screen with the changes that were applied by the functional code. */
    commitValueAndPropertyChanges(): Promise<void> {
        return new Promise(resolve => {
            commitTransaction(this.screenBase._pageMetadata.screenId);
            // An immediate would probably do too, but just be sure that all rendering can
            setTimeout(resolve, 50);
        });
    }

    finish(result?: any): void {
        const state = getStore().getState();
        const screenId = this.screenBase._pageMetadata.screenId;
        const screenDefinition = state.screenDefinitions[screenId];
        screenDefinition.onFinish?.(result);
    }

    public async processServerErrors(error: ClientError): Promise<void> {
        const transformedError = await transformServerErrorToToast(error, this.screenBase);

        if (transformedError.validationErrors && objectKeys(transformedError.validationErrors).length > 0) {
            const store = getStore();
            const dispatch = store.dispatch as AppThunkDispatch;
            await dispatch(
                setPageValidationErrors(this.screenBase._pageMetadata.screenId, transformedError.validationErrors),
            );
        }

        if (transformedError.globalError) {
            throw transformedError.globalError;
        }
    }

    public isServiceOptionEnabled(serviceOptionName: string): boolean {
        const state = getStore().getState();
        return state.serviceOptions[serviceOptionName] || false;
    }

    public getEnabledServiceOptions(): string[] {
        const state = getStore().getState();
        return objectKeys(state.serviceOptions).filter(key => state.serviceOptions[key]);
    }
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export abstract class ScreenBase<TGraphqlApi = any, _TNodeType extends ClientNode = any> {
    public readonly _pageMetadata: PageMetadata;

    // do not pass the second generic on purpose to avoid incompatibility with the generic coming from the page class
    public abstract $: DeveloperApi<TGraphqlApi>;

    public $standardDeleteButton: string = localize('@sage/xtrem-ui/crud-delete-record-button', 'Delete');

    public $standardCancelButton: string = localize('@sage/xtrem-ui/crud-cancel', 'Cancel');

    /**
     * Confirmation dialog title for the standard CRUD delete platform action.
     * Override this property to change the dialog title.
     * */
    public $standardDeletePromptTitle: string = localize(
        '@sage/xtrem-ui/crud-delete-record-warning-title',
        'Delete record',
    );

    /**
     * Confirmation dialog message content for the standard CRUD delete platform action.
     * Override this property to change the dialog title.
     * */
    public $standardDeletePromptMessage: string = localize(
        '@sage/xtrem-ui/crud-delete-record-warning-message',
        'You are about to delete this record.',
    );

    /**
     * Toast content for the standard CRUD save platform action for successfully updating an existing record.
     * Override this property to change the content of the toast.
     * */
    public $standardSaveSuccessMessage: string = localize(
        '@sage/xtrem-ui/crud-update-success',
        'Record has been updated successfully.',
    );

    /**
     * Toast content for the standard CRUD save platform action for successfully creating an existing record.
     * Override this property to change the content of the toast.
     * */
    public $standardCreateSuccessMessage: string = localize(
        '@sage/xtrem-ui/crud-create-success',
        'Record has been created successfully.',
    );

    /**
     * Toast content for the standard CRUD save platform action for save failure.
     * Override this property to change the content of the toast.
     * */
    public $standardSaveFailedMessage: string = localize('@sage/xtrem-ui/crud-save-failed', 'Failed to save record.');

    /**
     * Toast content for the standard CRUD duplicate platform action for duplication failure.
     * Override this property to change the content of the toast.
     * */
    public $standardDuplicateFailedMessage: string = localize(
        '@sage/xtrem-ui/crud-duplicate-failed',
        'Failed to duplicate record.',
    );

    constructor() {
        // eslint-disable-next-line no-proto
        this._pageMetadata = (this as any).__proto__.constructor._pageMetadata;
    }

    /**
     * This function serializes the pages values into a format that is ready for server operations. This function is used
     * by the standard CRUD actions, by overriding this function, the application developers can control what values are
     * sent to the server.
     *
     * @returns serializable data which will be used for server interactions
     */
    getSerializedValues(): any {
        return parseScreenValues(this._pageMetadata.screenId, false);
    }
}
