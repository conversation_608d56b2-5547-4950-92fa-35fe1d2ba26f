import * as XtremDateTime from '@sage/xtrem-date-time';
import type { LocalizeEnumFunction, LocalizeFunction, LocalizeLocale } from '@sage/xtrem-shared';
import { format, objectKeys } from '@sage/xtrem-shared';
import { snakeCase } from 'lodash';
import { getStore } from '../redux';
import { xtremConsole } from '../utils/console';
import type { DatePropertyValue } from '../utils/types';

/**
 * This function will be either injected by the 'decoratorTransformer' (which wraps a specific set of decorators
 * properties) or can be used in any page or sticker for any string that has to be translated.
 * The translations will be fetched by the 'screen-loader-service' through the metadata endpoint based on
 * the browser's locale settings. Hence the 'localize' function simply relies on the state to fetch all translations.
 *
 * @export
 * @param {string} key the translation key
 * @param {string} value the translation default value
 * @returns {string} the translated string
 */
export const localize: LocalizeFunction = (key: string, value: string, data: object | any[] = {}): string => {
    if (typeof getStore !== 'function') {
        xtremConsole.warn('localize is NOT a function!');
        return value;
    }
    const store = getStore();
    const state = store.getState();
    const locale =
        state.applicationContext && state.applicationContext.locale ? state.applicationContext.locale : 'en-US';
    const localeLiterals = state.translations[locale] || {};
    const literal = localeLiterals[key] || value;

    return format(literal, locale, data, true);
};

export const localizeText = localize;

/**
 * Use this function to format and check if the localization is available for a specific enum member.
 * @param enumFullPathName ex: "@sage/xtrem-workflow/WorkflowMutationArgumentOrigin"
 * @param memberName optional enum member name
 * @returns { localizeKey: string | undefined; key: string } localizeKey is from the state, undefined if not found. Key is the formatted key in case you need to use to fetch.
 */
export const getLocaleKey = (
    enumFullPathName: string,
    memberName?: string,
): { localizeKey: string | undefined; key: string } => {
    const components = enumFullPathName.split('/');
    const key = memberName
        ? `/enums__${snakeCase(components[2])}__${memberName}`
        : `/enums__${snakeCase(components[2])}`;
    const store = getStore();
    const state = store.getState();
    const locale =
        state.applicationContext && state.applicationContext.locale ? state.applicationContext.locale : 'en-US';
    const localeLiterals = state.translations[locale] || {};
    const localizeKey = objectKeys(localeLiterals).find(k => k.indexOf(key) !== -1);
    return { localizeKey, key };
};

export const localizeEnumMember: LocalizeEnumFunction = (enumFullPathName: string, memberName: string): string => {
    const { localizeKey, key } = getLocaleKey(enumFullPathName, memberName);
    if (!localizeKey) {
        xtremConsole.warn(
            `localizeEnumMember: No localization found for enum ${enumFullPathName} and member ${memberName}.`,
        );
    }
    // TODO dirty trick to not to trigger automated localize extension.
    const locoLocalize = localize;
    return locoLocalize(localizeKey || key, memberName);
};

export const formatDateToCurrentLocale = (
    date: DatePropertyValue,
    fmt: XtremDateTime.DatePresetFormat = 'FullDate',
): string => {
    const store = getStore();
    const state = store.getState();
    const locale =
        state.applicationContext && state.applicationContext.locale ? state.applicationContext.locale : 'base';
    return XtremDateTime.formatDateToCurrentLocale(date, locale as LocalizeLocale, fmt);
};
export const formatNumberToCurrentLocale = (value: number | string, scale?: number): string => {
    const state = getStore().getState();
    const formatOptions = { minimumFractionDigits: scale, maximumFractionDigits: scale };
    return new Intl.NumberFormat(state.applicationContext?.locale || 'en-US', formatOptions).format(Number(value));
};
