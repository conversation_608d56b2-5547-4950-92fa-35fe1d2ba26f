import type { EditableFieldProperties } from '../component/editable-field-control-object';
import type { ReduxResponsive } from '../plugin';
import type { QueryProperty } from './graphql-utils';
import type { ToastOptions } from './toast-service';
import type { IRouter } from './router';
import type { QueryParameters } from '../utils/types';

/**
 * Props that are passed to the plugin component. These values can be used to retrieve all essential information that is
 * needed in order to render a plugin field. It contains a number of functions which can be used to provide updates back
 * to the framework or query additional data from the server.
 **/
export interface XtremUiPluginComponentProps<
    XtremUiPluginCustomDecoratorProperties extends {} = {},
    FieldValue extends any = any,
> {
    /** The ID of the field, based on the property name of the field declaration on the page. */
    elementId: string;

    /** ID of the page that the addon field is loaded into. */
    screenId: string;

    /** The actual value of the field which should be displayed. */
    value: FieldValue;

    /** Information about the actual device that the field is rendered into. */
    browser?: ReduxResponsive;

    /**
     * Field properties, it includes all values that are declared in the field decorator or on runtime using the
     * `Plugin` control object's `setProperty` method.
     **/
    fieldProperties: EditableFieldProperties & XtremUiPluginCustomDecoratorProperties;

    /** Query parameters that are passed to the page that the plugin is loaded into. */
    queryParameters: QueryParameters;

    /** Locale of the user, such as `en-US` or `fr-FR`. */
    locale: string;

    /** Username or unique ID of the user. */
    username: string;

    /** Internal router object. It enables the plugin field to make trigger various navigation events. */
    router: IRouter;

    /** Execute GraphQL queries directly against the transaction GraphQL endpoint of the application (`/api`) */
    executeQuery: (query: string) => Promise<any>;

    /**
     * Send an update about the field value. It should be called whenever the user finishes changing the value of the
     * field, for example on blur event.
     **/
    setFieldValue: (value: FieldValue) => void;

    /**
     * Update a property of the field properties object. This function has the same effect as the `Plugin` control
     * object's setter methods.
     **/
    setFieldProperty: (
        property: keyof EditableFieldProperties & XtremUiPluginCustomDecoratorProperties,
        value: any,
    ) => void;

    /** Display a toast on the top of the screen. */
    showToast: (content: string, options?: ToastOptions) => void;

    /** A fixed height that is set by the framework in some special cases. If it is set, the plugin is expected to expand or shrink its implementation to this size. */
    fixedHeight?: number;
}

/**
 * Type that the plugin component is expected to implement. The first generic type represents the custom decorator
 * properties that the plugin field can receive from the field declaration on the page. The second generic type
 * represents the value of the field, it is used the determine the type of the `value` property and argument type of the
 * `setValue` method.
 **/
export type XtremUiPluginComponent<
    XtremUiPluginCustomDecoratorProperties extends {} = {},
    FieldValue extends any = any,
> = React.ComponentType<XtremUiPluginComponentProps<XtremUiPluginCustomDecoratorProperties, FieldValue>>;

/**
 * Xtrem UI Plugin declaration. This interface *must* be implemented and *default exported* from the main file of the
 * plugin NPM package in order to integrate with the Xtrem UI framework.
 **/
export interface XtremUiPlugin<XtremUiPluginCustomDecoratorProperties extends {} = {}, FieldValue extends any = any> {
    /** Plugin name */
    name: string;
    /** React component that is used to render the body of the plugin field. */
    component: XtremUiPluginComponent<XtremUiPluginCustomDecoratorProperties, FieldValue>;

    /** Create a GraphQL query fragment that is used to query the value of field. */
    createFieldQuery?: (
        /**
         * Field properties, it includes all values that are declared in the field decorator or on runtime using the
         * `Plugin` control object's `setProperty` method.
         **/
        fieldProperties: XtremUiPluginCustomDecoratorProperties & EditableFieldProperties,
    ) => QueryProperty;

    /**
     * Transform the GraphQL query result value to the type that is needed by the field. For example, it enables
     * plugin developers to convert raw values to their desired type (for example converting strings to numbers) of
     * their component. This is called before the initial value of the component is registered by the framework.
     **/
    transformFromGraphValue?: (rawValueFromQueryResult: any) => FieldValue;

    /**
     * Transform the value of the field to a GraphQL mutation friendly format. This function is called when the value
     * of the page is prepared to a CRUD operation, for example when the `this.$.values` variable is accessed on a page.
     */
    transformToGraphValue?: (value: FieldValue) => any;
}
