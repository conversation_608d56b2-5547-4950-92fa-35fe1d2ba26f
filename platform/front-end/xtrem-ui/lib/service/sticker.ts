/**
 * @packageDocumentation
 * @module root
 * */

import type { StickerControlObject } from '../component/control-objects';
import { DeveloperApi, ScreenBase } from './screen-base';

export class StickerDeveloperApi<TGraphqlApi> extends DeveloperApi<TGraphqlApi, any> {
    // eslint-disable-next-line @typescript-eslint/no-useless-constructor
    constructor(sticker: Sticker) {
        super(sticker);
    }

    /** This sticker */
    get sticker(): StickerControlObject {
        return this.screenBase._pageMetadata.controlObjects[
            this.screenBase._pageMetadata.screenId
        ] as StickerControlObject;
    }

    finish(): void {
        super.finish();
    }
}

export abstract class Sticker<TGraphqlApi = any> extends ScreenBase {
    public readonly $: StickerDeveloperApi<TGraphqlApi>;

    constructor() {
        super();
        this.$ = new StickerDeveloperApi(this);
    }
}
