PATH: XTREEM/Client+Framework/Storage+API

## Introduction

The Storage API provides simple cross-page key-value storage functionality. The values of the storage only preserved until the end of the user session. The values are persisted to browser's `sessionStorage` object.

Only primitive types such as strings, numbers and booleans can be stored.

## Basic example:

```ts
onChange() {
    this.$.storage.set('myKey', this.aSimpleTextField.value);
}
```

## Usage

-   **set:** Adds a single value to the storage. If the key is already used, it overwrites the value.
-   **get:** Retrieves a single value from the storage. If the key does not exist, `null` is returned.
-   **getAll:** Retrieves all entries from the storage, the values are returned as a simple dictionary.
-   **remove:** Removes a single entry from the storage. It does not throw an exception if the key does not exist.
