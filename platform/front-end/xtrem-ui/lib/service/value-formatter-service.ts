import type { ClientNode } from '@sage/xtrem-client';
import { Datetime } from '@sage/xtrem-date-time';
import { type Dict, objectKeys } from '@sage/xtrem-shared';
import { get, isNil, isObject, isString, set } from 'lodash';
import type {
    AggregateProperties,
    NestedGridProperties,
    PluginProperties,
    TableProperties,
    TreeProperties,
} from '../component/control-objects';
import type {
    MultiFileDepositDecoratorProperties,
    NumericDecoratorProperties,
} from '../component/decorator-properties';
import { getMultiFileDepositFieldColumns } from '../component/field/multi-file-deposit/multi-file-deposit-utils';
import { getLevelMap } from '../component/field/nested-grid/nested-grid-utils';
import type { PodCollectionDecoratorProperties } from '../component/field/pod-collection/pod-collection-types';
import type { PropertyValueType } from '../component/field/reference/reference-types';
import { getReferenceValueField } from '../component/field/reference/reference-utils';
import type { NestedField, NestedFieldTypes } from '../component/nested-fields';
import type { ReadonlyFieldControlObject, ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import type { CollectionItem, ComponentKey } from '../component/types';
import { ActionKey, ContainerKey, FieldKey } from '../component/types';
import { GraphQLKind, GraphQLTypes } from '../types';
import { xtremConsole } from '../utils/console';
import { HEADER_IMAGE, HEADER_TITLE } from '../utils/constants';
import { getMimeTypeUserFriendlyName } from '../utils/file-deposit-utils';
import { humanFileSize } from '../utils/formatters';
import {
    convertDeepBindToPath,
    convertDeepBindToPathNotNull,
    getNestedFieldsFromProperties,
} from '../utils/nested-field-utils';
import { findDeepPropertyDetails } from '../utils/node-utils';
import { resolveByValue } from '../utils/resolve-value-utils';
import { schemaTypeNameFromNodeName, splitValueToMergedValue } from '../utils/transformers';
import { CollectionValue } from './collection-data-service';
import { CollectionFieldTypes } from './collection-data-types';
import { getBindAndSelectorFromDeepBinding } from './graphql-query-builder';
import type { FormattedNodeDetails } from './metadata-types';
import type { PageDefinition } from './page-definition';
import type { XtremUiPlugin } from './plugin-service';
import type { ScreenBase } from './screen-base';
import { getUiComponentProperties } from './transactions-service';
import type { UiComponentUserSettings } from '../redux/state';
import type { TableUserSettings, TableViewLevel } from '../component/field/table/table-component-types';
import { getOrderByFromSortModel } from '../utils/table-component-utils';

const unwrapAggregateValue = (aggregateValue: Object): string => {
    const firstKey = objectKeys(aggregateValue)[0];
    const firstItem = aggregateValue[firstKey];
    return !firstItem || typeof firstItem === 'string' || typeof firstItem === 'number'
        ? firstItem
        : unwrapAggregateValue(firstItem);
};

/**
 * Gets the value of the fields by element ID. In case of nested bind, it denormalizes the value.
 * @param elementId
 * @param fieldProperties
 * @param values
 * @returns
 */
const getValue = (
    elementId: string,
    values: Dict<any>,
    nodeTypes: Dict<FormattedNodeDetails>,
    contextNode?: string,
    bind?: PropertyValueType,
): any => {
    if (bind) {
        const bindSegments = convertDeepBindToPathNotNull(bind).split('.');

        // TODO: DO NOT GO ONLY BY THE FIRST SEGMENT, WE NEED TO CHECK THE WHOLE PATH FROM JSON PROPERTIES
        if (contextNode) {
            const { valuePath, jsonSelector } = getBindAndSelectorFromDeepBinding(
                bind,
                contextNode!,
                nodeTypes,
                elementId,
            );
            const value = get(values, valuePath, null);
            if (value === null || value === undefined) {
                return null;
            }
            if (!jsonSelector) {
                return value;
            }
            const parsedJsonField = isObject(value) ? value : isString(value) ? JSON.parse(value) : null;
            if (!parsedJsonField) {
                return null;
            }

            return get(parsedJsonField, jsonSelector, null);
        }

        // Given that the element ID is used as an alias in the query, we need to replace the first component of the bind path with the element ID.
        bindSegments[0] = elementId;
        return get(values, bindSegments.join('.'), null);
    }

    return values[elementId];
};

/**
 * Formats a single field into a suitable format based on its server-side GraphQL type or the corresponding control
 * object
 *
 * @param screenId
 * @param elementId
 * @param nodeTypes
 * @param fieldType
 * @param fieldProperties
 * @param value
 * @param parentNode
 */
export const formatValue = ({
    pageDefinition,
    elementId,
    fieldProperties,
    fieldType,
    nodeTypes,
    parentNode,
    plugins,
    rowValue,
    screenId,
    value,
    userSettings,
}: {
    pageDefinition?: PageDefinition;
    elementId: string;
    fieldProperties: ReadonlyFieldProperties;
    fieldType: ComponentKey;
    nodeTypes: Dict<FormattedNodeDetails>;
    parentNode?: string;
    plugins: Dict<XtremUiPlugin>;
    rowValue?: any;
    screenId: string;
    value?: any;
    userSettings: Dict<UiComponentUserSettings>;
}): any => {
    // Empty values are set to null except tables/trees as they need to be wrapped to the CollectionValue object
    if (
        (value === null || value === undefined) &&
        fieldType !== FieldKey.Table &&
        fieldType !== FieldKey.TableSummary &&
        fieldType !== FieldKey.Tree &&
        fieldType !== FieldKey.PodCollection &&
        fieldType !== FieldKey.NestedGrid
    ) {
        return null;
    }

    // IDs always formatted as strings
    if (elementId === '_id') {
        return String(value);
    }

    // First we try to format primitive values by their server side scalar type.
    if (parentNode && !fieldProperties.isTransient && !(fieldProperties as ReadonlyFieldProperties).isTransientInput) {
        const formattedNodeName = schemaTypeNameFromNodeName(parentNode);
        const nodePropertyDetails = findDeepPropertyDetails(
            formattedNodeName,
            /* TODO: review for workflow fieldProperties.bind || */
            elementId,
            nodeTypes,
        );
        if (nodePropertyDetails) {
            const type = nodePropertyDetails.type;
            const kind = nodePropertyDetails.kind;
            switch (type) {
                case GraphQLTypes.Float:
                case GraphQLTypes.Decimal:
                    const convertFloat = (cValue: any): number | null =>
                        Number.isNaN(Number(cValue)) ? null : parseFloat(cValue);
                    if (kind === GraphQLKind.List && Array.isArray(value)) {
                        return value.map(convertFloat);
                    }
                    return convertFloat(value);
                case GraphQLTypes.Int:
                    const convertInt = (cValue: any): number | null =>
                        Number.isNaN(Number(cValue)) ? null : parseInt(cValue, 10);
                    if (kind === GraphQLKind.List && Array.isArray(value)) {
                        return value.map(convertInt);
                    }
                    return convertInt(value);
                case GraphQLTypes.String:
                    const convertString = (cValue: any): string => String(cValue);
                    if (kind === GraphQLKind.List && Array.isArray(value)) {
                        return value.map(convertString);
                    }
                    if (value !== Object(value)) {
                        return convertString(value);
                    }
                    return null;
                case GraphQLTypes.Boolean:
                case GraphQLTypes.Date:
                    // Format date?
                    return value;
                case GraphQLTypes.DateTime:
                    return Datetime.parse(value, undefined, undefined, 'UTC') || null;
                case GraphQLTypes.DatetimeRange: {
                    if (
                        value &&
                        typeof value === 'string' &&
                        (value.startsWith('[') || value.startsWith('(')) &&
                        (value.endsWith(']') || value.endsWith(')')) &&
                        value.split(',').length === 2
                    ) {
                        const rangeComponents = value.substring(1, value.length - 1).split(',');
                        return {
                            start: rangeComponents[0]
                                ? Datetime.parse(rangeComponents[0], undefined, undefined, 'UTC')
                                : null,
                            end: rangeComponents[1]
                                ? Datetime.parse(rangeComponents[1], undefined, undefined, 'UTC')
                                : null,
                        };
                    }
                    if (value?.start instanceof Datetime || value?.end instanceof Datetime) {
                        return {
                            start: value.start,
                            end: value.end,
                        };
                    }
                    return null;
                }
                case GraphQLTypes.Json:
                    /* We only parse JSON if we are in the dynamic pod context
                    the reason behind this is because functional devs are already parsing the JSON in the
                    functional code in other fields, so we don't want to parse it twice in order to avoid errors.
                     */
                    if (
                        fieldProperties._controlObjectType === FieldKey.DynamicPod ||
                        fieldProperties._controlObjectType === FieldKey.Workflow ||
                        fieldProperties._controlObjectType === FieldKey.TechnicalJson
                    ) {
                        // We only parse if is a string, otherwise we return the value as is.
                        if (typeof value === 'string') {
                            try {
                                return JSON.parse(value);
                            } catch (e) {
                                xtremConsole.error('Error parsing JSON', e);
                                return value;
                            }
                        }
                    }
                    return value;
                default:
                    xtremConsole.warn('Unhandled type ', type);
            }
        }
    }
    // If we couldn't format by the primitive type, we format by the control object type
    switch (fieldType) {
        case FieldKey.Plugin:
            const pluginProperties = fieldProperties as PluginProperties;
            const plugin = plugins[pluginProperties.pluginPackage];
            return plugin.transformFromGraphValue ? plugin.transformFromGraphValue(value) : value;
        case FieldKey.NestedGrid:
            // Table values wrapped into CollectionValue objects
            const nestedGridProps = fieldProperties as NestedGridProperties;
            const nestedGridFilters = nestedGridProps.levels.map(l =>
                resolveByValue({
                    screenId,
                    skipHexFormat: true,
                    propertyValue: l.filter,
                    rowValue: null,
                    fieldValue: null,
                }),
            );

            const nestedGridOptionsMenu = resolveByValue({
                propertyValue: nestedGridProps.optionsMenu,
                rowValue: null,
                screenId,
                fieldValue: null,
                skipHexFormat: true,
            });

            const nodes = nestedGridProps.levels.map(l => String(l.node));

            return new CollectionValue({
                bind: nestedGridProps.bind,
                columnDefinitions: nestedGridProps.levels.map(level => level.columns),
                elementId,
                fieldType: CollectionFieldTypes.NESTED_GRID,
                filter: nestedGridFilters,
                hasNextPage: value?.pageInfo ? value.pageInfo.hasNextPage : false,
                initialValues: value?.data ?? [],
                isTransient: Boolean(nestedGridProps.isTransient) || Boolean(nestedGridProps.isTransientInput),
                levelMap: getLevelMap(nestedGridProps.levels) as any,
                mapServerRecordFunctions: nestedGridProps.levels.map(l => l.mapServerRecord),
                nodes,
                nodeTypes,
                orderBy: nestedGridProps.levels.map(l => l.orderBy),
                screenId,
                activeOptionsMenuItem:
                    pageDefinition?.userSettings?.[elementId]?.$current?.content?.[0]?.optionsMenuItem ||
                    nestedGridOptionsMenu?.[0],
            });
        case FieldKey.MultiFileDeposit:
            const multiFileDepositProps = fieldProperties as MultiFileDepositDecoratorProperties;
            const initialValues = value?.data || (Array.isArray(value) ? value : []);

            return new CollectionValue({
                bind: multiFileDepositProps.bind,
                screenId,
                elementId,
                isTransient:
                    Boolean(multiFileDepositProps.isTransient) || Boolean(multiFileDepositProps.isTransientInput),
                hasNextPage: value?.pageInfo ? value.pageInfo.hasNextPage : false,
                orderBy: [multiFileDepositProps.orderBy],
                columnDefinitions: [getMultiFileDepositFieldColumns()],
                fieldType: CollectionFieldTypes.MULTI_FILE_DEPOSIT,
                initialValues,
                mapServerRecordFunctions: [
                    (row: any): any => ({
                        ...row,
                        attachment: {
                            ...row.attachment,
                            humanFriendlyContentLength: humanFileSize(row.attachment.contentLength),
                            humanFriendlyFileType: getMimeTypeUserFriendlyName(row.attachment.mimeType),
                        },
                    }),
                ],
                nodes: [multiFileDepositProps.node as string],
                nodeTypes,
            });
        case FieldKey.Table:
        case FieldKey.TableSummary:
        case FieldKey.Tree:
        case FieldKey.Calendar:
        case FieldKey.PodCollection:
            // Table values wrapped into CollectionValue objects
            const props = fieldProperties as TableProperties & PodCollectionDecoratorProperties;
            const filter = resolveByValue({
                propertyValue: props.filter,
                rowValue: null,
                screenId,
                fieldValue: null,
                skipHexFormat: true,
            });
            const tableOptionsMenu = resolveByValue({
                propertyValue: props.optionsMenu,
                rowValue: null,
                screenId,
                fieldValue: null,
                skipHexFormat: true,
            });

            const tableView: TableUserSettings = userSettings.$current;
            const viewContent = tableView?.content?.[0] as TableViewLevel;

            const orderBy = viewContent?.sortOrder
                ? getOrderByFromSortModel(viewContent?.sortOrder || [], props.columns || [])
                : props.orderBy;

            return new CollectionValue({
                bind: props.bind,
                screenId,
                elementId,
                isTransient: Boolean(props.isTransient) || Boolean(props.isTransientInput),
                hasNextPage: value?.pageInfo ? value.pageInfo.hasNextPage : false,
                orderBy: [orderBy],
                columnDefinitions: [getNestedFieldsFromProperties(props)],
                fieldType:
                    fieldType === FieldKey.Table
                        ? CollectionFieldTypes.DESKTOP_TABLE
                        : fieldType === FieldKey.Tree
                          ? CollectionFieldTypes.TREE
                          : CollectionFieldTypes.POD,
                filter: [filter],
                initialValues: value?.data ? value.data : value instanceof Array ? value : [],
                mapServerRecordFunctions: [props.mapServerRecord],
                nodes: [props.node as string],
                nodeTypes,
                activeOptionsMenuItem:
                    pageDefinition?.userSettings?.[elementId]?.$current?.content?.[0]?.optionsMenuItem ||
                    tableOptionsMenu?.[0],
                sublevelProperty: convertDeepBindToPath((fieldProperties as TreeProperties).sublevelProperty),
            });
        case FieldKey.Text:
            // Check for objects, arrays etc
            if (value !== Object(value)) {
                return String(value);
            }
            return null;
        case FieldKey.MultiReference:
            if (value) {
                return value.map(splitValueToMergedValue);
            }
            return null;
        case FieldKey.Datetime:
            return Datetime.parse(value, undefined, undefined, 'UTC') || null;
        case FieldKey.DatetimeRange:
            if (
                value &&
                typeof value === 'string' &&
                (value.startsWith('[') || value.startsWith('(')) &&
                (value.endsWith(']') || value.endsWith(')')) &&
                value.split(',').length === 2
            ) {
                const rangeComponents = value.substring(1, value.length - 1).split(',');
                return {
                    start: rangeComponents[0] ? Datetime.parse(rangeComponents[0], undefined, undefined, 'UTC') : null,
                    end: rangeComponents[1] ? Datetime.parse(rangeComponents[1], undefined, undefined, 'UTC') : null,
                };
            }
            return null;
        case FieldKey.FilterEditor:
        case FieldKey.ContentTable:
            if (typeof value === 'string') {
                return JSON.parse(value);
            }
            if (isObject(value)) {
                return value;
            }
            return null;
        case FieldKey.FilterSelect:
            const filterSelectValueFieldPath = getReferenceValueField(fieldProperties as any);
            if (filterSelectValueFieldPath) {
                const filterSelectActualValue = get(value, filterSelectValueFieldPath, null);
                return set(value, filterSelectValueFieldPath, filterSelectActualValue);
            }
            return value;
        case FieldKey.Reference:
            const referenceValue = value ? splitValueToMergedValue(value) : value;
            const referenceValueFieldPath = getReferenceValueField(fieldProperties as any)!;
            if (referenceValueFieldPath) {
                const referenceActualValue = get(referenceValue, referenceValueFieldPath, null);
                return set(referenceValue, referenceValueFieldPath, referenceActualValue);
            }
            return referenceValue;
        case FieldKey.Pod:
        case FieldKey.VitalPod:
            return value ? splitValueToMergedValue(value) : null;
        case FieldKey.Numeric:
            if (Number.isNaN(Number(value))) {
                return null;
            }
            const numericProps = fieldProperties as NumericDecoratorProperties;
            const scale = resolveByValue({
                screenId,
                skipHexFormat: true,
                rowValue: rowValue ? splitValueToMergedValue(rowValue) : rowValue,
                fieldValue: value,
                propertyValue: numericProps.scale,
            });
            const unit = resolveByValue({
                screenId,
                skipHexFormat: true,
                rowValue: rowValue ? splitValueToMergedValue(rowValue) : rowValue,
                fieldValue: value,
                propertyValue: numericProps.unit,
            });
            return (scale && scale !== 0) || unit ? parseFloat(value) : parseInt(value, 10);

        case FieldKey.Aggregate:
            // If we have value and we dont have a readAggregate we return as is
            if (!isNil(value) && !value.readAggregate) {
                return value;
            }
            if (!value || !value.readAggregate) {
                return null;
            }
            const aggregateProperties = fieldProperties as AggregateProperties<any, any>;
            if (typeof aggregateProperties.aggregateOn === 'string') {
                return (
                    value.readAggregate[aggregateProperties.aggregateOn][aggregateProperties.aggregationMethod] || null
                );
            }
            return unwrapAggregateValue(value.readAggregate);

        case FieldKey.Count:
            if (!value?.query) {
                return null;
            }
            return parseInt(value.query.totalCount || 0, 10);
        case FieldKey.NodeBrowserTree:
            if (!value) {
                return null;
            }

            if (typeof value === 'string') {
                return JSON.parse(value);
            }
            if (typeof value === 'object') {
                return value;
            }

            return null;
        case FieldKey.Workflow:
            if (typeof value === 'string') {
                return JSON.parse(value);
            }
            if (typeof value === 'object') {
                return value;
            }
            return null;
        default:
            // If the value cannot be formatted, we return the original value as is.
            return value;
    }
};

export interface FormatCollectionItemArgs<T extends ClientNode = any> {
    screenId: string;
    nodeTypes: Dict<FormattedNodeDetails>;
    collectionItem: CollectionItem;
    plugins?: Dict<XtremUiPlugin>;
    columnsDefinitions: NestedField<ScreenBase, NestedFieldTypes, T>[];
    parentNode?: string;
    contextRow?: any;
}

/**
 * Format all values of a nested collection item into client-side suitable format based on various rules depending on
 * the server-side node GraphQL types and the control object types.
 * @param screenId
 * @param nodeTypes
 * @param collectionItem
 * @param columnsDefinitions
 * @param parentNode
 */
export const formatCollectionItem = <T extends ClientNode = any>({
    screenId,
    nodeTypes,
    collectionItem,
    plugins = {},
    columnsDefinitions,
    parentNode,
    contextRow,
}: FormatCollectionItemArgs<T>): T => {
    const formattedCollectionItem = objectKeys(collectionItem).reduce((previousItem, key) => {
        const columnDefinitions = columnsDefinitions.filter(c => c.properties.bind === key);
        if (columnDefinitions.length === 0) {
            previousItem[key] = collectionItem[key];
            return previousItem;
        }
        columnDefinitions.forEach(columnDefinition => {
            if (
                columnDefinition &&
                !columnDefinition.properties.isTransient &&
                !columnDefinition.properties.isTransientInput
            ) {
                const formattedValue = formatValue({
                    elementId: key,
                    fieldProperties: columnDefinition.properties,
                    fieldType: columnDefinition.type,
                    nodeTypes,
                    parentNode,
                    plugins,
                    rowValue: contextRow || collectionItem,
                    screenId,
                    value: collectionItem[key],
                    userSettings: {},
                });
                if (previousItem[key] !== undefined) {
                    xtremConsole.warn(
                        `Duplicate column definition for ${key} in ${screenId}, this will overwrite the previous value ${JSON.stringify(previousItem[key])} by ${JSON.stringify(formattedValue)}`,
                    );
                }
                previousItem[key] = formattedValue;
            } else {
                previousItem[key] = collectionItem[key];
            }
        });

        return previousItem;
    }, {} as any);

    columnsDefinitions.forEach(c => {
        const path = convertDeepBindToPathNotNull(c.properties.bind);
        const { valuePath, jsonSelector } = getBindAndSelectorFromDeepBinding(path, parentNode!, nodeTypes);
        if (!jsonSelector) {
            return;
        }
        const rawJsonFieldValue = get(collectionItem, valuePath, null);
        if (!rawJsonFieldValue) {
            return;
        }

        const parsedJsonField = isObject(rawJsonFieldValue)
            ? rawJsonFieldValue
            : isString(rawJsonFieldValue)
              ? JSON.parse(rawJsonFieldValue)
              : null;
        if (!parsedJsonField) {
            return;
        }

        const value = get(parsedJsonField, jsonSelector);
        if (value === undefined) {
            return;
        }
        set(formattedCollectionItem, path, value);
    });

    return formattedCollectionItem;
};
/**
 * Format all values of a screen into client-side suitable format based on various rules depending on the server-side
 * node GraphQL types and the control object types.
 *
 * If the `onlyElementIds` parameter is supplied, it will only format those values which are provided in this parameter.
 *
 * @param screenId
 * @param controlObjects
 * @param uiComponentProperties
 * @param nodeTypes
 * @param values
 * @param parentNode
 */
export const formatScreenValues = ({
    controlObjects,
    nodeTypes,
    onlyElementIds,
    pageDefinition,
    parentNode,
    plugins,
    screenId,
    userSettings,
    values,
}: {
    controlObjects: Dict<ReadonlyFieldControlObject<any, any, any>>;
    nodeTypes: Dict<FormattedNodeDetails>;
    onlyElementIds?: string[];
    pageDefinition?: PageDefinition;
    parentNode?: string;
    plugins: Dict<XtremUiPlugin>;
    screenId: string;
    userSettings: Dict<Dict<UiComponentUserSettings>>;
    values: Dict<any>;
}): Dict<any> => {
    const nonBindingControlObjects = [
        ContainerKey.Section,
        ContainerKey.Block,
        ContainerKey.Page,
        FieldKey.Separator,
        ActionKey.PageAction,
    ];

    const resultValues: any = {
        _id: values._id,
        _etag: values._etag,
    };

    if (values[HEADER_IMAGE]) {
        resultValues[HEADER_IMAGE] = values[HEADER_IMAGE];
    }
    if (values[HEADER_TITLE]) {
        resultValues[HEADER_TITLE] = values[HEADER_TITLE];
    }

    objectKeys(controlObjects).forEach(elementId => {
        const fieldProperties = getUiComponentProperties(screenId, elementId) as TableProperties | undefined;
        const fieldType = controlObjects[elementId].componentType;

        if (
            fieldProperties != null &&
            elementId !== screenId &&
            !nonBindingControlObjects.includes(fieldType) &&
            (!onlyElementIds || onlyElementIds.includes(elementId)) &&
            !fieldProperties.isTransient &&
            !fieldProperties.isTransientInput
        ) {
            resultValues[elementId] = formatValue({
                pageDefinition,
                screenId,
                elementId,
                nodeTypes,
                fieldType,
                plugins,
                fieldProperties,
                value: getValue(elementId, values, nodeTypes, parentNode, fieldProperties.bind),
                parentNode,
                rowValue: values,
                userSettings: userSettings[elementId] || {},
            });
        }
    });
    return resultValues;
};
