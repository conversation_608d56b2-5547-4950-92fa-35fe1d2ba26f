PATH: XTREEM/Client+Framework/Page+Extensions

This documents briefly introduces page extensions and provides examples on how to implement them.

## Introduction

Similarly to node extensions, page extensions can be used to add new functionality to a dependency package. They enable the developer to add new fields, blocks and other elements to existing pages. Theoretically, page extensions can be applied on pages that are exist in the same package, but that is considered to be very counter intuitive. They should be only used to extend the pages of other packages.

## Location in a package and naming

The page extensions must be located in the `lib/page-extensions` folder and should follow the standard naming conventions: the file name must be in kebab-case format (e.g. `my-first-page-extension`) and the extension class name within the file must be defined in a pascal-case format (e.g. `MyFirstPageExtension`).

## CRUD operations

From a data persistency point of view, the extension fields are handled the same way as the core fields. When the page is loaded their value is fetched from the server and when the page is saved their value is sent back with the rest of the fields.

## Scope

The `this` variable within the extension class is a union of the base page's properties and the extension properties. This means that any callbacks in the extension has access to the fields, containers actions of the base class. For example, an extension can change the value of a base field in an event listener.

## Positioning of components

The extension fields and containers are positioned very similarly to the ones on pages. In addition to the `parent()` decorator property callback that defines the parent container, in extensions the `insertBefore()` callback can be used.
The `insertBefore()` callbacks return value defines to where the field is inserted in within the container. As the name suggests, the extension field is inserted before the nominated base field. Please note that if the `parent()` value of the base field is different or the `insertBefore()` function is not defined, the field is inserted to the end of the parent container.

## Example

The following example adds two new fields to an existing page.

```ts

import { GraphApi } from '@sage/xtrem-spanish-vertical';
import { User } from '@sage/xtrem-system/lib/pages/user';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<SpanishUserExtension>({
    extends: '@sage/xtrem-system/User',
    onLoad() {
        this.documentNumber.isDisabled = !this.identityDocumentType.value;
    },
    navigationPanel: {
        optionsMenu: [
            {
                title: 'Only foreigners',
                graphQLFilter:{
                    identityDocumentType: {
                        code: { _eq: 'NIE' }
                    }
                }
            },
            {
                title: 'Only citizens',
                graphQLFilter:{
                    identityDocumentType: {
                        code: { _eq: 'DNI' }
                    }
                }
            },
        ],
        listItem: {
            line2: ui.nestedFieldExtensions.reference({
                title: 'Identity document',
                node: '@sage/xtrem-spanish-vertical/IdentityDocumentType',
                valueField: 'name',
            })
        }
    },
})
export class SpanishUserExtension extends ui.PageExtension<User, GraphApi> {

    @ui.decorators.referenceField<SpanishUserExtension>({
        title: 'Identity document',
        node: '@sage/xtrem-spanish-vertical/IdentityDocumentType',
        isAutoSelectEnabled: true,
        insertBefore() {
            return this.description;
        },
        parent() {
            return this.userInformationBlock;
        },
        onChange() {
            this.documentNumber.isDisabled = !this.identityDocumentType.value;
        }
        valueField: 'name',
        helperTextField: 'code',
    })
    identityDocumentType: ui.fields.Reference;

    @ui.decorators.textField<SpanishUserExtension>({
        title: 'Document Number',
        isDisabled: true,
        parent() {
            return this.userInformationBlock;
        },
        validate() {
            const value = this.documentNumber.value;
            const errorMessage = ui.localize('@sage/xtrem-spanish-vertical/error_invalid_id_number','Invalid ID number')
            if (this.identityDocumentType.value.code === 'DNI' && !value.match(/[0-9]{8}[A-Z]/)) {
                return errorMessage;
            } else (this.identityDocumentType.value.code === 'NIE' && !value.match(/[X-Z][0-9]{7}[A-Z]/)) {
                return errorMessage;
            }
        },
    })
    documentNumber: ui.fields.Text;
}

declare module '@sage/xtrem-system/lib/pages/user' {
    interface User extends SpanishUserExtension { }
}

```

## Implementation guide

This section describes how to implement an extension using a scenario. Please note that this guide only focuses on the page extension and assumes that the corresponding node extension has already been done.

The example above implements the following scenario: We develop a vertical for the Spanish market (`@sage/xtrem-spanish-vertical`). The xtrem application should keep track of a government issued ID number for our users. In Spain, there are various identity documents which are issued by the government. The base xtrem-system implementation does not handle these Spanish documents, so a page extension must add functionality to handle these. There are two types of ID documents issued in Spain: for Spanish citizens, the ID number is called `DNI` and for foreigners the identity number is called `NIE`. These numbers have different formats so they require different validation rules.

In this example, two new fields are added to the base screen:

-   A reference field which indicates the document type.
-   A text field which contains the alphanumeric document number

### Imports

In this example first we import the GraphApi of the **extension** package which is assigned to the extension class as a generic type below. Then the base page is imported. Please note that unlike node extensions, the page needs to be imported using a direct path such as the one in the example (`'@sage/xtrem-system/lib/pages/user'`)

### Class decorator definition

In the page extension decorator the most important property is the `extends` field. This must contain the path to the base page using the standard page format: `@vendor/package-name/PageName`. On the top of that, developers can add a custom `onLoad()` hook which is called after the base page's `onLoad()` function. In this example, the `documentNumber` field is disabled if the `identityDocumentType` field is empty.
The class definition must extend `ui.PageExtension` with two generics:

1. Extended page: The class of the page being extended
2. GraphApi: the GraphApi of the extension package, this generic is optional. If it is not declared, the base page's GraphApi is applied.

### Fields

In this example there are two fields added as explained above. In general, the field definitions are very similar to the normal page with the addition of the `insertBefore()` callback. This function is expected to return a field from the base package. The extension field is inserted before this field if they have the same parent container.

#### `identityDocumentType` field

The first field is a reference field which will be inserted in the base page's `userInformationBlock` before the `description` field. This field allows the user to choose the document type. The document type suggestions are fetched from the extension package's `@sage/xtrem-spanish-vertical/IdentityDocumentType` node.

#### `documentNumber` field

When the user chooses a document type, the `onChange()` callback enables the `documentNumber` field.
The second field stores the ID document number. Unlike the first field, this one does not implement the `insertBefore()` callback which means it is inserted to the end of the `parent()` container. As described above this field is only enabled if the document type is not empty. Its validation rules depend on the document type as the number format is different.

#### `extensionAccessBinding` field

By default the access and availibity of a page extension is linked to the access of the page it `extends`. With `extensionAccessBinding` the access to extensions can be bound to the users access to another node and/or binding (node property or operation).

## Overrides

On top of adding and repositioning fields, page extensions allow users to modify the extended field's original decorator property values, except for structural ones like `bind, node, parent...`

To accomplish this, each type of field decorator can use its corresponding `Override` pair to redecorate the preexisting member with the overriding property's values. For example:

```typescript
@ui.decorators.pageExtension<AnotherExtension>({
    extends: '@sage/xtrem-system/SpanishUserExtension',
})
export class AnotherExtension extends ui.PageExtension<SpanishUserExtension, GraphApi> {

    @ui.decorators.referenceFieldOverride<SpanishUserExtension>({
        helperText: 'DNI',
    })
    identityDocumentType: ui.fields.Reference;
}
```
As identityDocumentType member is of Reference type, use of `referenceFieldOverride` supersedes the properties (in this case the helperTextField) but keeps the rest as it was declared initially in the referenceField decorator.

Both nested fields overrides and extensions are supported by `columnOverrides`+`nestedFieldOverride` listings and `nestedFieldExtensions`.
```typescript
import { GraphApi, BiodegradabilityCategory } from '@sage/xtrem-show-case-bundle-api';
import { ShowCaseProduct, ShowCaseProvider } from '@sage/xtrem-show-case-api';
import { Table } from '@sage/xtrem-show-case/lib/pages/table';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<TableExtension>({
    extends: '@sage/xtrem-show-case/Table',
})
export class TableExtension extends ui.PageExtension<Table, GraphApi> {
    @ui.decorators.tableFieldOverride<TableExtension, ShowCaseProduct>({
        title: 'Table Extension',
        onRowSelectedAfter(_id: string, item: any) {
            this.$.showToast(`${this.tableSelectedTotal.value}`);
        },
        columnOverrides: [
            ui.nestedFieldOverrides.numeric<TableExtension>({
                bind: 'qty',
                scale: 1,
                insertBefore: 'description',
            }),
        ],
        columns: [
            ui.nestedFieldExtensions.text<TableExtension>({
                bind: 'newNestedField',
                isTransient: true,
                insertBefore: 'provider',
                title: 'Transient ext column',
            }),
        ],
    })
    field: ui.fields.Table;
}
```
#### After Events
Overrides family of decorators comes with after events for each event handler of the field type. For example buttonField only exposes a `onClick` handler, hence its pair buttonFieldOverride exposes `onClick` and `onClickAfter`. This allows the override decorator to either completely overwrite the inherited onClick or implement a onClickAfter to be executed sequentially after when the event is triggered in the runtime. Notice how in the previous example we have used onRowSelectedAfter to show a toast that will run immediately and synchronously after onRowSelected in the original page implementation has updated the this.tableSelectedTotal value.

### Navigation panel extensions

#### `listItem`
In full-width navigation panel mode the navigation panel table can be extended similarly to table extensions. The new columns should be added to the list item definition. The position of the new columns can be controlled by the `insertBefore` decorator property.
In split-view mode the card definition can be modified by declaring new fields using keys which were already used in the base page definition.

### `optionsMenu`
The option menu definition of the base page cannot be overridden, it only can be extended. The option items of `optionsMenu` definition of the extension are added to base option menu definitions.
