import type { Dict } from '@sage/xtrem-shared';
import { isEqual, isUndefined } from 'lodash';
import type { PageProperties } from '../component/control-objects';
import type { ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import type { AppThunkDispatch } from '../redux';
import { actions, getStore } from '../redux';
import { getContainerChildFields } from '../utils/abstract-fields-utils';
import { getTopLevelBindFromNestedBindNotNull } from '../utils/nested-field-utils';
import type { PageDefinition } from './page-definition';
import type { ValidationResult } from './screen-base-definition';
import { getScreenElement } from './screen-base-definition';
import { checkValidationStatus } from './validation-service';
import { SERVER_VALIDATION_RULE_PREFIX } from '../utils/constants';
import type { ControlObjectInstance } from '../component/types';
import type { ScreenBase } from './screen-base';
import type { BaseControlObject } from '../component/base-control-object';
import type { TableSidebarDialogContent } from '../types/dialogs';

export interface ContainerValidationResult {
    allErrors: ValidationResult[];
    blockingErrors: ValidationResult[];
}

export const runAndDispatchFieldValidation = async (
    screenId: string,
    elementId: string,
    value?: any,
): Promise<ValidationResult[] | undefined> => {
    const store = getStore();
    const state = store.getState();
    const screenDefinition = state.screenDefinitions[screenId];
    const screenElement = getScreenElement(state.screenDefinitions[screenId]);
    const fieldValue = isUndefined(value) ? screenDefinition.values[elementId] : value;
    const previousValidationResult = screenDefinition.errors[elementId];
    const pageDefinition = screenDefinition as PageDefinition;
    const pageProperties = pageDefinition.metadata.uiComponentProperties[screenId] as PageProperties<any>;

    const validationResults = await checkValidationStatus(
        screenElement,
        elementId,
        screenDefinition.metadata.uiComponentProperties,
        fieldValue,
        pageDefinition.accessBindings || {},
        state.nodeTypes,
        state.dataTypes,
        pageProperties?.node,
    );

    const nonEmptyValidationResults = validationResults.filter(v => v.message || v.validationRule);

    // Remove Validation Error
    if (previousValidationResult && nonEmptyValidationResults.length === 0) {
        store.dispatch(actions.removeNonNestedErrors(screenId, elementId));
    } else if (!isEqual(previousValidationResult || [], nonEmptyValidationResults)) {
        store.dispatch(actions.setFieldValidationErrors(screenId, elementId, nonEmptyValidationResults));
    }

    return nonEmptyValidationResults;
};

async function validateAndDispatch(
    getControlObjects: (
        screen: ScreenBase,
        controlObjects: Dict<ControlObjectInstance<any>>,
    ) => BaseControlObject<any, any>[],
    screenId: string,
): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }> {
    const store = getStore();
    const state = store.getState();
    const screen = state.screenDefinitions[screenId];
    const screenElement = getScreenElement(screen);
    const controlObjects = state.screenDefinitions[screenId].metadata.controlObjects;

    const fieldControlObjects = getControlObjects(screenElement, controlObjects);

    const pageDefinition = screen as PageDefinition;
    const pageProperties = pageDefinition.metadata.uiComponentProperties[screenId] as PageProperties<any>;

    const fieldsValidationPromises = fieldControlObjects.map(async field => {
        const fieldProperties = screen.metadata.uiComponentProperties[field.id] as ReadonlyFieldProperties<any>;
        const fieldValue = screen.values[field.id || getTopLevelBindFromNestedBindNotNull(fieldProperties.bind)];
        return {
            errors: await checkValidationStatus(
                screenElement,
                field.id,
                screen.metadata.uiComponentProperties,
                fieldValue,
                pageDefinition.accessBindings || {},
                state.nodeTypes,
                state.dataTypes,
                pageProperties?.node,
            ),
            screenId,
            elementId: field.id,
        };
    });

    const validationErrorArray: ValidationResult[] = (await Promise.all(fieldsValidationPromises))
        .map(e =>
            e.errors.length > 0
                ? e.errors
                : [
                      {
                          elementId: e.elementId,
                          screenId: e.screenId,
                          message: '',
                          validationRule: '',
                      },
                  ],
        )
        .flat();
    Object.values(screen.internalErrors).forEach(internalError => {
        if (internalError?.message || internalError.validationRule) {
            validationErrorArray.push(internalError);
        }
    });

    const validationErrors = validationErrorArray.reduce<Dict<ValidationResult[] | undefined>>(
        (prevValue, validationResult) => {
            const errors =
                prevValue[validationResult.elementId] ??
                (screen.errors[validationResult.elementId] || []).filter(e =>
                    e.validationRule.startsWith(SERVER_VALIDATION_RULE_PREFIX),
                );
            if (validationResult.message || validationResult.validationRule) {
                errors.push(validationResult);
            }
            prevValue[validationResult.elementId] = errors.length > 0 ? errors : undefined;
            return prevValue;
        },
        {},
    );

    store.dispatch(actions.setPageValidationErrors(screenId, validationErrors));

    const blockingErrors = validationErrorArray.filter(m => m.message || m.validationRule);
    const allErrors = Object.values(validationErrors)
        .filter(Boolean)
        .flat()
        .filter(m => m!.message || m!.validationRule) as ValidationResult[];
    return { allErrors, blockingErrors };
}

export const dispatchContainerValidation = async (
    screenId: string,
    elementId: string,
): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }> => {
    return validateAndDispatch(
        (screen, controlObjects) =>
            screenId === elementId
                ? Object.values(controlObjects)
                : getContainerChildFields(screen, Object.values(controlObjects), elementId),
        screenId,
    );
};

export const dispatchFieldsValidation = async (
    elementsId: string[],
    screenId: string,
): Promise<{ allErrors: ValidationResult[]; blockingErrors: ValidationResult[] }> => {
    return validateAndDispatch((_, controlObjects) => elementsId.map(id => controlObjects[id]), screenId);
};

export const dispatchSetFieldDirty = ({ screenId, elementId }: { screenId: string; elementId: string }): void => {
    const store = getStore();
    const dispatch: AppThunkDispatch = store.dispatch;
    dispatch(actions.setFieldDirty({ screenId, elementId }));
};

export const dispatchCloseSideBar = ({
    screenId,
    elementId,
    recordId,
}: {
    screenId: string;
    elementId: string;
    recordId: string;
}): void => {
    const store = getStore();
    const state = store.getState();
    const dispatch: AppThunkDispatch = store.dispatch;

    const sidebarDialog = Object.values(state.activeDialogs).find(dialog => {
        if (dialog.type !== 'table-sidebar') {
            return false;
        }

        const content = dialog.content as TableSidebarDialogContent;
        return dialog.screenId === screenId && content.elementId === elementId && content.recordId === recordId;
    });

    if (sidebarDialog) {
        dispatch(actions.closeDialog(sidebarDialog.dialogId));
    }
};

/**
 *
 * @param screenId
 * @param elementId
 * @param validationResult If not validation result is passed in, the field is marked valid.
 */
export const dispatchFieldValidation = (
    screenId: string,
    elementId: string,
    validationResult?: ValidationResult[],
): void => {
    const store = getStore();
    const state = store.getState();
    const screenDefinition = state.screenDefinitions[screenId];
    if (screenDefinition) {
        const previousValidationResult = screenDefinition.errors[elementId];
        // Only dispatch if the validation status changed compared to previous state.
        const hasValidationResultChanged = !isEqual(previousValidationResult, validationResult);
        if (hasValidationResultChanged) {
            if (validationResult && validationResult.some(v => v.message)) {
                store.dispatch(actions.setFieldValidationErrors(screenId, elementId, validationResult!));
            } else {
                store.dispatch(actions.removeNonNestedErrors(screenId, elementId));
            }
        }
    }
};

/**
 *
 * @param screenId
 * @param elementId
 * @param validationResult If not validation result is passed in, the field is marked valid.
 */
export const dispatchUpdateNestedFieldValidationErrorsForRecord = ({
    screenId,
    elementId,
    recordId,
    level,
    validationResult = [],
    isUncommitted = false,
}: {
    screenId: string;
    elementId: string;
    recordId: string;
    validationResult?: ValidationResult[];
    isUncommitted?: boolean;
    level?: number;
}): void => {
    const store = getStore();
    store.dispatch(actions.removePhantomErrors(screenId, elementId));
    store.dispatch(
        actions.updateNestedFieldValidationErrorsForRecord({
            screenId,
            elementId,
            recordId,
            validationErrors: validationResult,
            level,
            isUncommitted,
        }),
    );
};
