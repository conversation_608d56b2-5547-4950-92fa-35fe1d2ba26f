import type { Dict, Meta<PERSON>ustomField } from '@sage/xtrem-shared';
import type { UiComponentProperties } from '../component/abstract-ui-control-object';
import type {
    DetailPanelControlObject,
    FragmentFieldsControlObject,
    PageActionControlObject,
    PageControlObject,
} from '../component/control-objects';
import type { ControlObjectInstance } from '../component/types';
import type { PageArticleItem, PageArticleLayout } from './layout-types';
import type { Page } from './page';
import type { ScreenBase } from './screen-base';
import type { Key } from './shortcut-service';
import type { AttachmentInformation } from './node-information-service';
import type { DataTypeDetails, FormattedNodeDetails } from './metadata-types';

export interface ElementWithShortcut {
    elementId: string;
    shortcut: Key | Key[];
}

/**
 * This object is used while the decorators are executed to gather all metadata and share data between the decorators.
 */
export interface PageMetadata<E extends UiComponentProperties = any> {
    attachmentInformation?: AttachmentInformation;
    blockThunks: Dict<
        (nodeTypes: Dict<FormattedNodeDetails>, dataTypes: Dict<DataTypeDetails>) => Partial<PageArticleItem>
    >;
    businessActionsExtensionsThunk: Array<(ctx: Page) => PageActionControlObject[]>;
    businessActionsThunk?: (ctx: Page) => PageActionControlObject[];
    controlObjects: Dict<ControlObjectInstance<any>>;
    customizableNodes?: string[];
    customizableNodesWizard?: string[];
    customizations: Dict<MetaCustomField[]>;
    defaultEntryThunk?: (ctx: Page) => null | string | Promise<null | string>;
    defaultUiComponentProperties: Dict<UiComponentProperties>;
    definitionOrder: string[];
    detailPanelThunk?: (ctx: Page) => DetailPanelControlObject;
    duplicateBindings?: string[];
    elementsWithShortcut: ElementWithShortcut[];
    exportTemplatesByNode?: Dict<Dict<string>>;
    extensionOverrideThunks: Dict<(() => E)[]>;
    fragmentFieldsThunks: Dict<() => FragmentFieldsControlObject>;
    fragmentFields: Dict<string[]>;
    fieldThunks: Dict<(nodeTypes: Dict<FormattedNodeDetails>, dataTypes: Dict<DataTypeDetails>) => PageArticleItem>;
    fieldBindings: Dict<string>;
    hasAttachmentsSection?: boolean;
    layout: PageArticleLayout;
    layoutBlocks: Dict<Partial<PageArticleItem>>;
    layoutFields: Dict<PageArticleItem>;
    pageActions: Dict<PageActionControlObject>;
    pageActionThunks: Dict<
        (nodeTypes: Dict<FormattedNodeDetails>, dataTypes: Dict<DataTypeDetails>) => PageActionControlObject
    >;
    pageExtensionThunks: Array<() => void>;
    pageFragmentThunks: Array<() => void>;
    pageThunk?: (nodeTypes: Dict<FormattedNodeDetails>, dataTypes: Dict<DataTypeDetails>) => PageControlObject;
    screenId: string;
    sectionThunks: Dict<
        (nodeTypes: Dict<FormattedNodeDetails>, dataTypes: Dict<DataTypeDetails>) => Partial<PageArticleItem>
    >;
    target?: ScreenBase;
    rootNode?: string;
    isTransient: boolean;
    uiComponentProperties: Dict<UiComponentProperties>;
    hasRecordPrintingTemplates?: boolean;
}

let preferredScreenId: string | null;

export const setPreferredScreenId = (screenId: string): void => {
    preferredScreenId = screenId;
};

export const unsetPreferredScreenId = (): void => {
    preferredScreenId = null;
};

export const getNewPageMetadata = (screenId: string, target?: ScreenBase): PageMetadata => {
    const defaultMetadata: PageMetadata = {
        blockThunks: {},
        businessActionsExtensionsThunk: [],
        controlObjects: {},
        customizations: {},
        defaultUiComponentProperties: {},
        definitionOrder: [],
        elementsWithShortcut: [],
        extensionOverrideThunks: {},
        fieldBindings: {},
        fieldThunks: {},
        fragmentFields: {},
        fragmentFieldsThunks: {},
        hasRecordPrintingTemplates: false,
        isTransient: false,
        layout: { $items: [] },
        layoutBlocks: {},
        layoutFields: {},
        pageActions: {},
        pageActionThunks: {},
        pageExtensionThunks: [],
        pageFragmentThunks: [],
        screenId,
        sectionThunks: {},
        target,
        uiComponentProperties: {},
    };

    return defaultMetadata;
};

/**
 * Get the page metadata object from the constructor
 *
 * The metadata object is added to the constructor of the page which is being built because all decorator functions
 * have direct access to the object constructor, so it is an easy way to have a global shared object.
 */
export const getPageMetadata = (pageConstructor: Function, target?: ScreenBase): PageMetadata => {
    /**
     * Checks if the constructor already has a metadata object assigned, if not it creates and assigns a new one. Only
     * a single metadata object should be created for every page load.
     *  */
    const currentMetadataObject = (pageConstructor as any)._pageMetadata as PageMetadata;
    if (!currentMetadataObject) {
        const defaultMetadataObject = getNewPageMetadata(preferredScreenId || pageConstructor.name, target);
        (pageConstructor as any)._pageMetadata = defaultMetadataObject;
        return defaultMetadataObject;
    }

    if (!currentMetadataObject.target) {
        currentMetadataObject.target = target;
    }

    return currentMetadataObject;
};
