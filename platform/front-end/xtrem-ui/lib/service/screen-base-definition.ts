import type { ClientNode } from '@sage/xtrem-client';
import type { Dict } from '@sage/xtrem-shared';
import type { NavigationPanelState, UiComponentUserSettings } from '../redux/state';
import type { AccessBindings, PageDefinition } from './page-definition';
import type { PageMetadata } from './page-metadata';
import type { ScreenBase } from './screen-base';
import type { StickerDefinition } from './sticker-definition';

export interface ValidationResult {
    columnId?: string;
    elementId: string;
    level?: number;
    message: string;
    messagePrefix?: string;
    recordId?: string;
    screenId: string;
    validationRule: string;
}
export interface ScreenBaseDefinition<T extends ClientNode = any> {
    accessBindings: AccessBindings;
    dialogId?: number;
    dirtyStates: Dict<boolean>;
    errors: Dict<ValidationResult[]>;
    internalErrors: Dict<ValidationResult>;
    metadata: PageMetadata;
    navigationPanel?: NavigationPanelState<T>;
    selectedRecordId?: string | null;
    userSettings: Dict<Dict<UiComponentUserSettings>>;
    type: 'page' | 'sticker';
    values: Dict<any | null>;
    initialValues?: Dict<any | null>;
    onFinish?: (values: any) => void;
}

export const getScreenElement = (screenDefinition: ScreenBaseDefinition): ScreenBase => {
    if (screenDefinition.type === 'page') {
        return (screenDefinition as PageDefinition).page;
    }
    return (screenDefinition as StickerDefinition).sticker;
};
