import type { <PERSON><PERSON>N<PERSON>, OnlySelected, Selector } from '@sage/xtrem-client';
import type { Store } from 'redux';
import type { AppAction, XtremAppState } from '../redux';
import type {
    AsyncLoaderDialogOptions,
    AsyncOperationGetter,
    AsyncOperationReturn,
    CustomDialogContentType,
    CustomDialogOptions,
    DialogLevel,
    DialogOptions,
    GraphqlApiGetterType,
    IDialogApi,
    LookupDialogOptions,
    PageDialogOptions,
} from '../types/dialogs';
import type { QueryParameters } from '../utils/types';
import type { DialogControl } from './dialog-service';
import {
    asyncLoaderDialog,
    confirmationDialog,
    customDialog,
    customLookupDialog,
    messageDialog,
    pageDialog,
} from './dialog-service';
import type { ScreenBase } from './screen-base';

export const getDialogApi = <CT extends ScreenBase>(
    screenId: string,
    store: Store<XtremAppState, AppAction>,
): IDialogApi<CT> => ({
    confirmation: (level: DialogLevel, title: string, message: string, options?: DialogOptions): DialogControl =>
        confirmationDialog(screenId, level, title, message, options),
    message: (level: DialogLevel, title: string, message: string, options?: DialogOptions): DialogControl =>
        messageDialog(screenId, level, title, message, options),
    custom: (level: DialogLevel, content: CustomDialogContentType, options?: CustomDialogOptions): DialogControl =>
        customDialog(screenId, level, content, options),
    page: (path: string, queryParameters: QueryParameters = {}, options?: PageDialogOptions): Promise<any> =>
        pageDialog(store, path, queryParameters, options),
    lookup: <NodeType extends ClientNode = any>(options: LookupDialogOptions<CT, NodeType>): Promise<NodeType[]> => {
        return customLookupDialog(store, screenId, options);
    },
    asyncLoader: <
        N extends keyof GraphqlApiGetterType<CT>,
        O extends keyof AsyncOperationGetter<GraphqlApiGetterType<CT>, N>,
        SelectorT extends Selector<
            AsyncOperationReturn<GraphqlApiGetterType<CT>, N>[O] extends (infer U)[]
                ? U
                : AsyncOperationReturn<GraphqlApiGetterType<CT>, N>[O]
        >,
    >(
        nodeName: N,
        operation: O,
        parameters: AsyncOperationGetter<GraphqlApiGetterType<CT>, N>[O],
        selector: SelectorT,
        dialogOptions?: AsyncLoaderDialogOptions,
    ): Promise<OnlySelected<AsyncOperationReturn<GraphqlApiGetterType<CT>, N>[O], SelectorT> | null> => {
        return asyncLoaderDialog(
            screenId,
            'info',
            String(nodeName),
            String(operation),
            parameters,
            selector,
            dialogOptions,
        );
    },
});
