import { DateValue, Datetime, datePropertyValueToDateString } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import { objectKeys, type Dict } from '@sage/xtrem-shared';
import { isEmpty, isNil, isObject, merge, set } from 'lodash';
import type { InternalSectionProperties } from '../component/control-objects';
import type { ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import { FieldKey } from '../component/types';
import { GraphQLKind, GraphQLTypes } from '../types';
import { getParentSection } from '../utils/abstract-fields-utils';
import { xtremConsole } from '../utils/console';
import { convertDeepBindToPathNotNull } from '../utils/nested-field-utils';
import { findDeepPropertyDetails } from '../utils/node-utils';
import { cleanMetadataFromRecord } from '../utils/transformers';
import { isDevMode } from '../utils/window';
import { CollectionValue } from './collection-data-service';
import type { FormattedNodeDetails, FormattedNodeDetailsProperty } from './metadata-types';
import type { ScreenBaseDefinition } from './screen-base-definition';

const isEmptyValue = (value: any): boolean => value === null || value === undefined || value === '';

// Sometimes not the same version of the decimal library is used, so the first check can fail, that's why we need the second, more nasty approach
const isDecimal = (value: any): value is Decimal =>
    Decimal.isDecimal(value) || (isObject(value) && value.constructor?.name === 'Decimal');

// Checks if the object and all of its properties is null or undefined
const isEmptyObject = (obj: object): boolean => {
    return Object.values(obj).every((value: any) => {
        if (isNil(value)) return true;
        if ((value instanceof Array || value instanceof Object) && isEmpty(value)) return true;
        if (value instanceof Array && !isEmpty(value)) return isEmptyObject(value);
        if (value instanceof Object && !isEmpty(value)) return isEmptyObject(value);
        return false;
    });
};

/**
 * Gets a saveable dataset from a `CollectionValue` instance.
 *
 * @param value
 * @param fieldType
 * @param isRequestingDefaults
 * @returns
 */
const getCollectionValueChangeSet = (
    value: CollectionValue,
    fieldType: FieldKey,
    bind: string,
    isRequestingDefaults = false,
    targetNode?: string,
    nodeTypes?: Dict<FormattedNodeDetails>,
): any[] => {
    const targetProperty = findDeepPropertyDetails(targetNode, bind, nodeTypes, false);
    if (fieldType === FieldKey.NestedGrid) {
        // nested grid
        const changeSet = value.getChangedRecordsAsTree();
        if (changeSet.length > 0) {
            return changeSet;
        }
    } else if (targetProperty?.type === GraphQLTypes.Json) {
        return value.getData({ cleanMetadata: true, noLimit: true });
    } else {
        const changeSet = value.getNormalizedChangedRecords(isRequestingDefaults);
        const cleanChangeSet = changeSet.map(element => cleanMetadataFromRecord(element));
        if (changeSet.length > 0) {
            return cleanChangeSet;
        }
    }

    return [];
};

/**
 * Formats any data to a GraphQL input based on provided node type definition.
 * It checks the expected GraphQL input type and converts the value to it wherever it is possible. In case of nested structures in
 * converts the properties deeply by following the graph definitions.
 *
 * Whenever a client side value cannot be converted to the server defined type (e.g. boolean to an object), an exception is thrown
 *
 * @param param0
 * @returns
 */
export const formatInputProperty = ({
    isRequestingDefaults = false,
    isTopLevel,
    nodeTypes,
    property,
    value,
}: {
    property: FormattedNodeDetailsProperty;
    value: any;
    nodeTypes: Dict<FormattedNodeDetails>;
    isRequestingDefaults?: boolean;
    isTopLevel?: boolean;
}): any => {
    if (property?.isCollection && value) {
        if (Array.isArray(value)) {
            return value.map(v =>
                formatInputProperty({
                    nodeTypes,
                    value: v,
                    property: { ...property, isCollection: false },
                }),
            );
        }
        throw new Error(`Cannot serialize non iterable value to a collection. Value: ${value}`);
    }

    switch (property.type) {
        case GraphQLTypes.ID:
            if (value === null) {
                throw new Error("Couldn't serialize the ID type property because its value is null.");
            }

            if (value instanceof Object) {
                if (value._id) {
                    return String(value._id);
                }
                throw new Error(`Couldn't serialize ${value} to an input string, no _id property was found.`);
            }

            return String(value);
        case GraphQLTypes.Boolean:
            if (value === null) {
                return null;
            }
            return value === 'false' ? false : Boolean(value);
        case GraphQLTypes.Decimal:
            if (isEmptyValue(value)) {
                return null;
            }
            if (isDecimal(value)) {
                return value.toNumber();
            }
            return value;
        case GraphQLTypes.IntOrString:
            if (isEmptyValue(value)) {
                return null;
            }

            if (isDecimal(value)) {
                return value.round().toNumber();
            }

            if (value instanceof Object) {
                if (value._id) {
                    return String(value._id);
                }
                throw new Error(`Couldn't serialize ${value} to an input string, no _id property was found.`);
            }

            const intOrStringReturnValue = Math.round(Number(value));

            if (Number.isNaN(intOrStringReturnValue)) {
                throw new Error(`Couldn't serialize ${value} to a IntOrString input type`);
            }
            return String(intOrStringReturnValue);
        case GraphQLTypes.Int:
            if (isEmptyValue(value)) {
                return null;
            }
            if (isDecimal(value)) {
                return value.round().toNumber();
            }
            const intReturnValue = Math.round(Number(value));
            if (Number.isNaN(intReturnValue)) {
                throw new Error(`Couldn't serialize ${value} to a Int input type`);
            }
            return intReturnValue;
        case GraphQLTypes.Float:
            if (isEmptyValue(value)) {
                return null;
            }

            if (isDecimal(value)) {
                return value.toNumber();
            }

            const floatReturnValue = Number(value);
            if (Number.isNaN(floatReturnValue)) {
                throw new Error(`Couldn't serialize ${value} to a Float input type`);
            }
            return floatReturnValue;
        case GraphQLTypes.String:
            if (value instanceof Object) {
                if (value._id) {
                    return String(value._id);
                }
                throw new Error(`Couldn't serialize ${value} to an input string, no _id property was found.`);
            }
            return value ? String(value) : null;
        case GraphQLTypes.Date:
            if (isEmptyValue(value)) {
                return null;
            }

            if (!(value instanceof Date) && Number.isInteger(Number(value))) {
                return datePropertyValueToDateString(DateValue.fromJsDate(new Date(Number(value))));
            }

            return datePropertyValueToDateString(value);
        case GraphQLTypes.DateTime:
            if (isEmptyValue(value)) {
                return null;
            }

            if (!(value instanceof Date) && Number.isInteger(Number(value))) {
                return datePropertyValueToDateString(new Date(Number(value)), true);
            }
            if (value instanceof Datetime) {
                return value.toString();
            }

            return datePropertyValueToDateString(value, true);
        case GraphQLTypes.DatetimeRange:
            if (isEmptyValue(value) || (value?.start === null && value?.end === null)) {
                return null;
            }
            if (value?.start instanceof Datetime || value?.end instanceof Datetime) {
                const datetimeRangeValue = value;
                return `[${datetimeRangeValue.start ? datetimeRangeValue.start.toString() : ''},${datetimeRangeValue.end ? datetimeRangeValue.end.toString() : ''}]`;
            }
            return value;
        case GraphQLTypes.IntReference:
        case GraphQLTypes.ExternalReference:
            if (!value) {
                return null;
            }

            if (typeof value === 'number' || typeof value === 'string') {
                return String(value);
            }

            if (typeof value !== 'object' || value._id === null || value._id === undefined) {
                return null;
            }
            // Non-vital references
            return value && value._id ? `_id:${value._id}` : null;
        case GraphQLTypes.InputStream:
        case GraphQLTypes.OutputTextStream:
            if (!value) {
                return null;
            }

            if (typeof value === 'string') {
                return { value };
            }

            if (typeof value === 'object' && Object.prototype.hasOwnProperty.call(value, 'value')) {
                return value.value ? value : null;
            }
            throw new Error(`Cannot serialize ${value} to Input stream`);
        case GraphQLTypes.Json:
            if (!value) {
                return null;
            }
            if (typeof value === 'string') {
                return value;
            }

            return JSON.stringify(value);
        default:
            // Here we handel vital object and collection types and enums
            if (!value) {
                return null;
            }
    }

    // Here we handel vital object and enums
    if (!isEmpty(nodeTypes) && (isTopLevel || property.isMutable)) {
        if (typeof value !== 'object') {
            throw new Error(`Cannot serialize ${value} into input object`);
        }
        // Vital references
        return objectKeys(value).reduce((prevValue, currentValue) => {
            if (!property.type) {
                return merge(prevValue, {
                    [currentValue]: value[currentValue],
                });
            }

            let childNodeType: FormattedNodeDetailsProperty | null = findDeepPropertyDetails(
                property.type,
                currentValue,
                nodeTypes,
            );

            if (!childNodeType && currentValue === '_action') {
                childNodeType = {
                    type: GraphQLTypes.String,
                    kind: GraphQLKind.Scalar,
                    name: '_action',
                    isOnInputType: true,
                };
            }

            if (!childNodeType || !childNodeType.isOnInputType || (currentValue === '_id' && !value[currentValue])) {
                return prevValue;
            }

            const mergedValue = merge(prevValue, {
                [currentValue]: formatInputProperty({
                    nodeTypes,
                    property: childNodeType,
                    value: value[currentValue],
                    isRequestingDefaults,
                }),
            });

            // If all keys of the object is null, then we send a null value instead
            if (isEmptyObject(mergedValue)) {
                return null;
            }

            return mergedValue;
        }, {} as any);
    }

    // Enums
    if (property.type === GraphQLTypes.Enum) {
        if (isEmptyValue(value)) {
            return null;
        }

        if (Number.isInteger(Number(value))) {
            return Number(value);
        }

        if (typeof value === 'string') {
            return value;
        }

        throw new Error(`Cannot serialize ${value} to enum input type (${property.type})`);
    }

    if (!property.isMutable && property.kind === GraphQLKind.Object && value) {
        // If an object property is not vital/mutable, we need to serialize it as an id
        if (typeof value === 'number' || typeof value === 'string') {
            return String(value);
        }

        if (typeof value !== 'object' || value._id === null || value._id === undefined) {
            return null;
        }
        // Non-vital references
        return value && value._id ? `_id:${value._id}` : null;
    }

    if (isDevMode()) {
        xtremConsole.log(`Couldn't serialize ${property.type} GraphQL property type.`);
    }
    return null;
};

export interface SerializePageDataParams {
    screenDefinition: ScreenBaseDefinition;
    nodeTypes: Dict<FormattedNodeDetails>;
    targetNode: string;
    isRequestingDefaults?: boolean;
}

/**
 * Builds the mutation `data` payload from the values and properties, first it normalizes the value structure and filters out the
 * non saveable fields, then format the values to match the input value.
 */
export const serializePageData = ({
    screenDefinition,
    isRequestingDefaults,
    nodeTypes,
    targetNode,
}: SerializePageDataParams): any => {
    const { values, metadata } = screenDefinition;
    const output: any = {};
    // Normalize flat data structure to structured payload
    objectKeys(values).forEach(elementId => {
        if (elementId === '_etag' && values._etag) {
            set(output, '_etag', values._etag);
        }
        const properties = metadata.uiComponentProperties[elementId] as ReadonlyFieldProperties | undefined;
        if (!properties) {
            return;
        }

        const parentSection = getParentSection(screenDefinition, elementId);
        const parentSectionProperties: InternalSectionProperties | null = parentSection
            ? metadata.uiComponentProperties[parentSection]
            : null;
        const isLoadedField =
            !parentSectionProperties || !parentSectionProperties.isLazyLoaded || !!parentSectionProperties.isLoaded;
        const fieldType = properties._controlObjectType as FieldKey;
        const stateValue = values[elementId];
        // Rule out certain fields
        if (
            properties.isTransient ||
            fieldType === FieldKey.Aggregate ||
            fieldType === FieldKey.Count ||
            fieldType === FieldKey.Separator ||
            !isLoadedField
        ) {
            return;
        }

        if (elementId === '_id' && !stateValue) {
            return;
        }

        const bind = properties.bind ? convertDeepBindToPathNotNull(properties.bind) : elementId;
        const isCollectionValue = stateValue instanceof CollectionValue;
        const valueToSet = isCollectionValue
            ? getCollectionValueChangeSet(stateValue, fieldType, bind, isRequestingDefaults, targetNode, nodeTypes)
            : stateValue;

        /**
         * Empty collection values are not included into the payload because it would reset the collection. If the changeset is empty, we don't need to send anything to the server
         * because it indicates that nothing has actually changed.
         *  */
        if (isCollectionValue && Array.isArray(valueToSet) && valueToSet.length === 0) {
            return;
        }
        set(output, bind, valueToSet);
    });

    const formattedOutput = formatInputProperty({
        isRequestingDefaults,
        nodeTypes,
        property: { type: targetNode, name: targetNode, kind: 'OBJECT', isMutable: true },
        value: output,
        isTopLevel: true,
    });

    return formattedOutput;
};
