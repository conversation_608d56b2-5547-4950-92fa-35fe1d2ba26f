import type { ContainerWidth, FieldWidth } from '../component/types';

export interface PageArticleLayout {
    $items: Partial<PageArticleItem>[];
    $inlineMode?: boolean;
    $widths?: string;
}

// TODO: Check with authoring tool if we can split in different interfaces by field or container
export interface PageArticleItem {
    $breakAfter?: boolean;
    $isHidden?: boolean;
    $isMaskable?: boolean;
    $containerId?: string;
    $bind: string;
    $category?: string;
    $layout?: PageArticleLayout;
    $doubleColumn?: boolean;
    $columnId?: number;
    $isColumnSeparator?: boolean;
    $style?: string;
    $isHiddenMobile?: boolean;
    $isHiddenDesktop?: boolean;
    $stackedFields?: boolean;
    $containerType?: string;
    $columnWidth?: ContainerWidth | FieldWidth;
    $isFullWidth?: boolean;
}
