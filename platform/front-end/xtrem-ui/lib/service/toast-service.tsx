import { ContainerValidationErrorsComponent } from '../component/container/page/container-validation-errors';
import * as toastActions from '../redux/actions/toast-actions';
import { getStore } from '../redux/store';
import type { ToastProps } from 'carbon-react/esm/components/toast';
import type { ContainerValidationResult } from './dispatch-service';
import type { PageMetadata } from './page-metadata';
import * as React from 'react';

export type ToastType = ToastProps['variant'];

export interface ToastOptions {
    language?: 'markdown' | 'jsx';
    timeout?: number;
    type?: ToastType;
}

export interface Toast {
    content: string | React.ReactNode;
    isDismissed: boolean;
    id: number;
    language?: ToastOptions['language'];
    timeout: number;
    type: ToastType;
}

export const PERMANENT_TOAST = Number.MIN_SAFE_INTEGER;

let toastId = 0;

export const showToast = (content: Toast['content'], options: ToastOptions = {}): void => {
    toastId += 1;
    const language = options.language || 'markdown';
    if (language !== 'jsx' && typeof content !== 'string') {
        throw new Error(`Invalid toast content: jsx cannot be rendered in "${language}".`);
    }
    const toast: Toast = {
        content,
        id: toastId,
        language,
        isDismissed: false,
        timeout: options.timeout || 4000,
        type: options.type || 'info',
    };

    getStore().dispatch(toastActions.showToast(toast));
};

export const showContainerValidationToast = (
    pageMetadata: PageMetadata,
    result: ContainerValidationResult,
    options: ToastOptions = {
        type: 'error',
        language: 'jsx',
    },
): void => {
    showToast(
        <ContainerValidationErrorsComponent
            validationResults={result.allErrors}
            pageMetadata={pageMetadata}
            screenId={pageMetadata.screenId}
        />,
        options,
    );
};

export const removeToasts = (): void => {
    getStore().dispatch(toastActions.removeToasts());
};
