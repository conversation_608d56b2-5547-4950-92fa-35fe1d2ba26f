import type { AppThunkDispatch, XtremAppState } from '../redux';
import { actions, getStore } from '../redux';
import { findNavPanelRecordLocation } from '../redux/actions/router-actions';
import { NEW_PAGE, REG_EXP_INTERNAL_URL_PATTERN, REG_EXP_URL_PATTERN } from '../utils/constants';
import { getPageDefinitionFromState, isScreenDefinitionDirty } from '../utils/state-utils';
import type { QueryParameters } from '../utils/types';
import { errorDialog } from './dialog-service';
import { openDirtyPageConfirmationDialog } from './dirty-state-service';
import { wipeCache } from './graphql-utils';
import { notifyConsumerOnError } from './telemetry-service';

/** Router API. It encapsulates function that enable developers to navigate to various pages and open records programmatically. */
export interface IRouter {
    /** Navigates to another page, query parameters can be sent to the page that is being opened. */
    goTo: (
        path: string,
        queryParameters?: QueryParameters,
        skipDirtyCheck?: boolean,
        isBackNavigation?: boolean,
    ) => void;
    /** Navigates to an external link in a new tab. */
    goToExternal: (link: string) => void;
    /**
     * Refreshes the current page, it restores the page to it's original state including field values and all component properties.
     * If the page is bound to any record, the value is refetched from the server.
     * It triggers the `onLoad` lifecycle hooks in page and page extension decorators.
     */
    refresh: (skipDirtyCheck?: boolean) => Promise<void>;
    /**
     * Clears all field values and resets all component properties. It triggers the `onLoad` lifecycle hooks in page and page extension decorators.
     */
    emptyPage: (skipDirtyCheck?: boolean) => Promise<void>;

    /**
     * Close the current record and navigates to the full width nav panel.
     */
    closeRecord: (skipDirtyCheck?: boolean) => Promise<void>;

    /**
     * Selects a record of the current page. The action is identical to when the the user selects a record from the navigation panel.
     */
    selectRecord: (_id: number | string, skipDirtyCheck?: boolean) => Promise<void>;
    /**
     * Selects the first record from the navigation panel
     */
    firstRecord: (skipDirtyCheck?: boolean) => Promise<void>;
    /**
     * Selects the next record from the navigation panel. This action can only be used if a navigation panel is defined for the page.
     */
    nextRecord: (skipDirtyCheck?: boolean) => Promise<void>;
    /**
     * Selects the previous record from the navigation panel. This action can only be used if a navigation panel is defined for the page.
     */
    previousRecord: (skipDirtyCheck?: boolean) => Promise<void>;
    /**
     *  Determines whether there is a next record on the navigation panel. This action can only be used if a navigation panel is defined for the page.
     */
    hasNextRecord: () => Promise<boolean>;
    /**
     * Determines whether there is a previous record on the navigation panel. This action can only be used if a navigation panel is defined for the page.
     */
    hasPreviousRecord: () => Promise<boolean>;

    hardRefresh: () => Promise<void>;

    goHome: (skipDirtyCheck?: boolean) => Promise<void>;
}

export class Router implements IRouter {
    constructor(private readonly screenId: string) {}

    private isScreenDirty(): boolean {
        // Special screen name starts with $ symbol, such as $dashboards, in this case we don't need to check if they are dirty
        return (
            !this.screenId.startsWith('$') &&
            isScreenDefinitionDirty(getStore().getState().screenDefinitions[this.screenId])
        );
    }

    private async wrapNavigationMethod<T extends any = any>(
        skipDirtyCheck: boolean,
        navigationImplementation: () => (dispatch: AppThunkDispatch, getState: () => XtremAppState) => Promise<T>,
    ): Promise<T> {
        const storeDispatch: AppThunkDispatch = getStore().dispatch;

        if (!skipDirtyCheck && this.isScreenDirty()) {
            return new Promise<any>((resolve, reject) =>
                openDirtyPageConfirmationDialog(this.screenId)
                    .then(() =>
                        storeDispatch(navigationImplementation())
                            .then(resolve)
                            .catch(error => {
                                errorDialog(this.screenId, 'Navigation error', error?.message);
                                notifyConsumerOnError(error);
                                reject();
                            }),
                    )
                    .catch(error => {
                        // resolve dialog promise when go-back button is clicked
                        if (!error) resolve(true);
                    }),
            );
        }

        return storeDispatch(navigationImplementation());
    }

    async goTo(path: string, queryParameters: QueryParameters = {}, skipDirtyCheck = false): Promise<void> {
        await this.wrapNavigationMethod(skipDirtyCheck, () => actions.navigate(path, queryParameters, false));
    }

    goToExternal(link: string): void {
        if (!link.match(REG_EXP_URL_PATTERN) && !link.match(REG_EXP_INTERNAL_URL_PATTERN)) {
            throw new Error('Unexpected link format.');
        } else {
            window.open(link, '_blank', 'noopener=true');
        }
    }

    async refresh(skipDirtyCheck = false): Promise<void> {
        return this.wrapNavigationMethod(skipDirtyCheck, () => {
            const pageDefinition = getPageDefinitionFromState(this.screenId);
            const recordId = pageDefinition?.queryParameters?._id;
            return actions.selectRecord(this.screenId, recordId ? String(recordId) : null, true);
        });
    }

    async emptyPage(skipDirtyCheck = false): Promise<void> {
        return this.wrapNavigationMethod(skipDirtyCheck, () => actions.selectRecord(this.screenId, NEW_PAGE));
    }

    async closeRecord(skipDirtyCheck = false): Promise<void> {
        return this.wrapNavigationMethod(skipDirtyCheck, () => actions.selectRecord(this.screenId, null));
    }

    async selectRecord(recordId: number | string, skipDirtyCheck = false): Promise<void> {
        return this.wrapNavigationMethod(skipDirtyCheck, () =>
            actions.selectRecord(this.screenId, String(recordId), false),
        );
    }

    async firstRecord(skipDirtyCheck = false): Promise<void> {
        return this.wrapNavigationMethod(skipDirtyCheck, () => actions.selectFirstRecord(this.screenId));
    }

    async nextRecord(skipDirtyCheck = false): Promise<void> {
        if (await this.hasNextRecord()) {
            return this.wrapNavigationMethod(skipDirtyCheck, () => actions.selectNextRecord(this.screenId));
        }

        return undefined;
    }

    async previousRecord(skipDirtyCheck = false): Promise<void> {
        if (await this.hasPreviousRecord()) {
            return this.wrapNavigationMethod(skipDirtyCheck, () => actions.selectPreviousRecord(this.screenId));
        }

        return undefined;
    }

    async hasPreviousRecord(): Promise<boolean> {
        try {
            const nextRecordId = await findNavPanelRecordLocation(this.screenId, getStore().getState(), -1);
            return nextRecordId !== null;
        } catch {
            return false;
        }
    }

    async hasNextRecord(): Promise<boolean> {
        try {
            const nextRecordId = await findNavPanelRecordLocation(this.screenId, getStore().getState(), 1);
            return nextRecordId !== null;
        } catch {
            return false;
        }
    }

    async hardRefresh(): Promise<void> {
        await wipeCache();
        window.location.reload();
    }

    async goHome(skipDirtyCheck = false): Promise<void> {
        return this.wrapNavigationMethod(skipDirtyCheck, () => actions.goHome());
    }
}

export const getRouter = (screenId: string): Router => new Router(screenId);
