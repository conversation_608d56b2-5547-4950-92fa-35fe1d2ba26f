PATH: XTREEM/Client+Framework/Global+variables

Using global variables in client side artifacts (such as pages or stickers) is strictly forbidden as they are considered to be a security risk. The framework blocks most of the global variables and at runtime their value will be `undefined`.

## Exceptions

Some variables are not blocked, you can find them in the list below. If you would like to use any other global variable, please consult the platform team.

-   `atob`
-   `btoa`
-   `setTimeout`
-   `clearTimeout`
-   `setInterval`
-   `clearInterval`
-   `console`
-   `JSON`
-   `Date`
-   `String`
-   `Number`
-   `Boolean`
-   `parseInt`
-   `parseFloat`
-   `isNaN`
-   `isFinite`
