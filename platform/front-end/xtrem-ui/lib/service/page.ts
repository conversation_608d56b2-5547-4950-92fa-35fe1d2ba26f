/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { Dict } from '@sage/xtrem-shared';
import type {
    BlockControlObject,
    DetailPanelControlObject,
    MultiFileDepositControlObject,
    PageAction,
    PageControlObject,
    PageMode,
} from '../component/control-objects';
import type { ReadonlyFieldControlObject } from '../component/readonly-field-control-object';
import type { AppThunkDispatch } from '../redux';
import { getStore } from '../redux';
import { refreshNavigationPanel } from '../redux/actions';
import { applyDefaultValues } from '../redux/actions/field-value-actions';
import { setNavigationPanelIsHidden } from '../redux/actions/navigation-panel-actions';
import {
    ATTACHMENTS_ELEMENT_ID,
    DUPLICATE_INDICATOR_QUERY_EDITOR_PARAM,
    NEW_PAGE,
    QUERY_PARAM_TUNNEL_SEGMENTS,
} from '../utils/constants';
import {
    checkIfPageIsLoaded,
    getPageDefinitionFromState,
    getPagePropertiesFromPageDefinition,
    getPagePropertiesFromState,
} from '../utils/state-utils';
import type { PageArticleItem } from './layout-types';
import { DeveloperApi, ScreenBase } from './screen-base';

export class PageDeveloperApi<TGraphqlApi, CT extends ScreenBase<TGraphqlApi>> extends DeveloperApi<TGraphqlApi, CT> {
    private readonly _detailPanel: DetailPanelControlObject | null;

    private readonly _headerLineBlock: Partial<PageArticleItem> | null;

    constructor(page: Page<TGraphqlApi>) {
        super(page as unknown as CT);
        this._detailPanel = page._pageMetadata.detailPanelThunk ? page._pageMetadata.detailPanelThunk(page) : null;

        if (page.$header) {
            this._headerLineBlock = Object.seal(page._pageMetadata.layoutBlocks[page.$header.id]);
        }
    }

    private get screenId(): string {
        return this.page.id;
    }

    /** Light-weight function that returns the ID of the page from the query parameters */
    public get recordId(): string | undefined {
        const state = getStore().getState();

        const pageDefinition = getPageDefinitionFromState(this.screenBase._pageMetadata.screenId, state);
        const params = pageDefinition?.queryParameters || {};
        return params._id && params._id !== NEW_PAGE && !params[DUPLICATE_INDICATOR_QUERY_EDITOR_PARAM]
            ? String(params._id)
            : undefined;
    }

    get isNavigationPanelHidden(): boolean {
        checkIfPageIsLoaded(this.screenId);
        const pageDefinition = getPageDefinitionFromState(this.screenId);
        return pageDefinition?.navigationPanel?.isHidden || false;
    }

    set isNavigationPanelHidden(state: boolean) {
        checkIfPageIsLoaded(this.screenId);
        const dispatch = getStore().dispatch;
        setNavigationPanelIsHidden(state, this.screenId)(dispatch);
    }

    get hasAttachmentsDialog(): boolean {
        return !!this.screenBase._pageMetadata.attachmentInformation;
    }

    get attachments(): MultiFileDepositControlObject | null {
        return (
            (this.screenBase._pageMetadata.controlObjects[ATTACHMENTS_ELEMENT_ID] as MultiFileDepositControlObject) ||
            null
        );
    }

    get headerLineBlock(): Partial<PageArticleItem> | null {
        return this._headerLineBlock;
    }

    get queryParameters(): Dict<string | number | boolean> {
        const state = getStore().getState();
        const pageDefinition = getPageDefinitionFromState(this.screenBase._pageMetadata.screenId, state);
        const params = { ...(pageDefinition?.queryParameters || {}) };
        if (params._id === NEW_PAGE) {
            delete params._id;
        }
        return params;
    }

    /** Container that can be used to display additional information outside of the page layout */
    get detailPanel(): DetailPanelControlObject | null {
        return this._detailPanel;
    }

    /** This page */
    get page(): PageControlObject {
        return this.screenBase._pageMetadata.controlObjects[
            this.screenBase._pageMetadata.screenId
        ] as PageControlObject;
    }

    /** Visualization mode */
    get mode(): PageMode {
        const pageProperties = getPagePropertiesFromState(this.screenBase._pageMetadata.screenId);
        return pageProperties.mode || 'default';
    }

    async refreshNavigationPanel(recordAdded = false): Promise<void> {
        const store = getStore();
        const dispatch = store.dispatch as AppThunkDispatch;
        await dispatch(refreshNavigationPanel(this.screenBase._pageMetadata.screenId, recordAdded));
    }

    get isInDialog(): boolean {
        const state = getStore().getState();
        const pageDefinition = getPageDefinitionFromState(this.screenBase._pageMetadata.screenId, state);
        return Boolean(pageDefinition && pageDefinition.isInDialog);
    }

    get isInTunnel(): boolean {
        const state = getStore().getState();
        const pageDefinition = getPageDefinitionFromState(this.screenBase._pageMetadata.screenId, state);
        return this.isInDialog && !!pageDefinition.queryParameters[QUERY_PARAM_TUNNEL_SEGMENTS];
    }

    get isNewPage(): boolean {
        const state = getStore().getState();
        const pageDefinition = getPageDefinitionFromState(this.screenBase._pageMetadata.screenId, state);
        const params = { ...(pageDefinition?.queryParameters || {}) };
        return !params._id || params._id === NEW_PAGE;
    }

    /**
     * Fetch default values from the server even if those fields are dirty.
     * If the `skipSet` argument is set to true, the values are requested from the server but not applied to the page.
     */
    async fetchDefaults(
        requestedFieldIds: (string | ReadonlyFieldControlObject<any, any, any>)[],
        skipSet = false,
    ): Promise<Dict<any>> {
        checkIfPageIsLoaded(this.screenId);

        const store = getStore();
        const state = store.getState();
        const dispatch = store.dispatch as AppThunkDispatch;
        const screenDefinition = getPageDefinitionFromState(this.screenBase._pageMetadata.screenId, state);
        const pageProperties = getPagePropertiesFromPageDefinition(screenDefinition);

        if (pageProperties.isTransient) {
            throw new Error('Default values cannot be requested on a transient page.');
        }
        if (!pageProperties.node) {
            throw new Error('Default values cannot be requested on a page without a node.');
        }

        const resolvedFieldIds = requestedFieldIds.map(e => (typeof e === 'string' ? e : e.id));
        return applyDefaultValues({
            dispatch,
            screenDefinition,
            plugins: state.plugins,
            nodeTypes: state.nodeTypes,
            requestedFieldIds: resolvedFieldIds,
            skipDispatch: skipSet,
        });
    }
}

export abstract class Page<TGraphqlApi = any, TNodeType extends ClientNode = any> extends ScreenBase<
    TGraphqlApi,
    TNodeType
> {
    public readonly $: PageDeveloperApi<TGraphqlApi, typeof this>;

    /**
     * Standard Delete CRUD action. First it displays a confirmation prompt, then if the user confirms it calls the
     * `delete` mutation associated with the page node.
     */
    public readonly $standardDeleteAction: PageAction<Page<TGraphqlApi, TNodeType>>;

    /**
     * Standard Save CRUD action. If the record has a valid ID, it calls the associated page node's `update` mutation,
     * if not it calls the `create` mutation.
     */
    public readonly $standardSaveAction: PageAction<Page<TGraphqlApi, TNodeType>>;

    /**
     * Empties the page to for creating a new record.
     */
    public readonly $standardNewAction: PageAction<Page<TGraphqlApi, TNodeType>>;

    /**
     * Restores the page to its original state.
     */
    public readonly $standardCancelAction: PageAction<Page<TGraphqlApi, TNodeType>>;

    /**
     * Opens duplication page in a dialog.
     */
    public readonly $standardDuplicateAction: PageAction<Page<TGraphqlApi, TNodeType>>;

    /**
     * Executes the duplication graph mutation.
     */
    public readonly $standardExecuteDuplicationAction: PageAction<Page<TGraphqlApi, TNodeType>>;

    /**
     * Opens the customization wizard if the application is configured with a customization manager and the page has customizable nodes
     */
    public readonly $standardOpenCustomizationPageWizardAction: PageAction<Page<TGraphqlApi, TNodeType>>;

    /**
     * Opens a simple dialog with information about the record's history.
     */
    public readonly $standardOpenRecordHistoryAction: PageAction<Page<TGraphqlApi, TNodeType>>;

    /**
     * Closes the page dialog and serializes its value into the dialog result
     */
    public readonly $standardDialogConfirmationAction: PageAction<Page<TGraphqlApi, TNodeType>>;

    constructor() {
        super();
        this.$ = new PageDeveloperApi<TGraphqlApi, typeof this>(this);
    }

    /** Block that provides highlighted information, relevant to the current page, through labels and icons */
    $header?: BlockControlObject<Page<TGraphqlApi, TNodeType>>;
}

export abstract class PageFragment<TGraphqlApi = any, TNodeType extends ClientNode = any> extends Page<
    TGraphqlApi,
    TNodeType
> {}

(PageFragment.prototype as any).___isPageFragment = true;
