import type { Dict } from '@sage/xtrem-shared';
import { isNil } from 'lodash';
import { xtremConsole } from '../utils/console';

const callbacks: Dict<(rawValue: string) => void> = {};
let mobileAppInterface: WrapperMobileAppInterface | null = null;

/**
 * Create unique string key for a field from a `FieldLocator` object.
 * @param fieldLocator
 * @returns unique string key
 */
const getFieldKey = (fieldLocator: FieldLocator): string => {
    const parts = [fieldLocator.screenId, fieldLocator.elementId];

    if (!isNil(fieldLocator.recordId)) {
        parts.push(fieldLocator.recordId);
    }

    if (!isNil(fieldLocator.nestedElementId)) {
        parts.push(fieldLocator.nestedElementId);
    }

    if (!isNil(fieldLocator.level)) {
        parts.push(String(fieldLocator.level));
    }

    return parts.join('-');
};

let focussedField: FieldLocator | null = null;

interface XtremCommunicationWindow extends Window {
    XTREM_WRAPPER_MOBILE_APP_INTERFACE?: WrapperMobileAppInterface;
    XTREM_WRAPPER_WEB_APP_INTERFACE?: WrapperWebAppInterface;
}
export interface FieldLocator {
    screenId: string;
    elementId: string;
    recordId?: string;
    title?: string;
    nestedElementId?: string;
    level?: number;
}

/**
 * This interface should be implemented by the native mobile application. It contains a set
 * of event handlers which are required for the communication between the mobile and the native apps.
 */
export interface WrapperMobileAppInterface {
    /** Called when the mobile app recognizes the presence of the mobile wrapper and it registered its global communication interface */
    onLoad?: () => void;
    /**
     * Called when a scannable field gets focussed and the scanner button can be displayed.
     * @param fieldLocator unique field identifier
     */
    onFocus?: (fieldLocator: FieldLocator) => void;
    /**
     * Called when a scannable field loses focus and and the scanner button should be hidden.
     * @param fieldLocator unique field identifier
     */
    onBlur?: (fieldLocator?: FieldLocator) => void;
    /**
     * Called when the mobile app initiates scanning
     */
    captureData?: (fieldLocator: FieldLocator) => void;
}

/**
 * This interface should be implemented by the web app. These events are called by the mobile app to notify the web app about certain events.
 * */
export interface WrapperWebAppInterface {
    /** Notifies the web app that the scanner activity was successfully opened. */
    startScanning: (fieldLocator: FieldLocator) => void;
    /** Notifies the web app that the scanner successfully scanned some value and the scanner was closed. */
    setRawData: (fieldLocator: FieldLocator, rawValue: string) => void;
}

/**
 * Initiate scanning from the web application.
 * This method is called by the web app to request the opening of the scanner activity.
 * It also takes a callback which is invoked by the native app if the scanning is successful.
 */
export const captureData = (fieldLocator: FieldLocator, callback: (rawValue: string) => void): void => {
    if (mobileAppInterface?.captureData) {
        const fieldKey = getFieldKey(fieldLocator);
        callbacks[fieldKey] = callback;
        mobileAppInterface.captureData(fieldLocator);
    }
};

/**
 * Update the native app about the currently focussed field.
 * This method is called by the web app to notify the native app that the focus moved to a new field which is ready for scanning.
 * It also takes a callback which is invoked by the native app if the scanning is successful.
 **/
export const onFocus = (fieldLocator: FieldLocator, callback: (rawValue: string) => void): void => {
    if (mobileAppInterface?.onFocus) {
        const fieldKey = getFieldKey(fieldLocator);
        callbacks[fieldKey] = callback;
        mobileAppInterface.onFocus(fieldLocator);
        focussedField = fieldLocator;
    }
};

/**
 * Update the native app about the currently focussed field.
 * This method is called by the web app to notify the native app that the focus was removed from a field and it should hide the
 * scanner button.
 */
export const onBlur = (fieldLocator?: FieldLocator): void => {
    if (
        mobileAppInterface?.onBlur &&
        (!focussedField ||
            fieldLocator?.screenId !== focussedField.screenId ||
            fieldLocator.elementId === focussedField.elementId)
    ) {
        mobileAppInterface.onBlur(fieldLocator);
    }
};

/** */
const startScanning = (fieldLocator: FieldLocator): void => {
    xtremConsole.log('Start scanning', fieldLocator);
    // Intentionally empty for now.
};

const setRawData = (fieldLocator: FieldLocator, rawValue: string): void => {
    xtremConsole.log('Set raw data', fieldLocator);
    const fieldKey = getFieldKey(fieldLocator);
    const callback = callbacks[fieldKey];
    if (callback) {
        try {
            callback(rawValue);
        } catch (err) {
            xtremConsole.error('Failed to execute set value callback:', fieldLocator);
        }
    } else {
        xtremConsole.error('No callback found for field:', fieldLocator);
    }
};

/**
 * Checks if `XTREM_WRAPPER_MOBILE_APP_INTERFACE` is declared on the global scope. When it detects that the mobile API is present,
 * it declares the client communication API as `XTREM_WRAPPER_WEB_APP_INTERFACE` on the global scope and calls the mobile API's `onLoad`
 * method to notify it that the web client is ready and detected the presence of the mobile wrapper.
 */
const initiateWrapper = (): void => {
    const windowWithXtremInterfaces: XtremCommunicationWindow = window;
    // Check if the mobile wrapper is present
    if (windowWithXtremInterfaces.XTREM_WRAPPER_MOBILE_APP_INTERFACE) {
        // Capture the global reference so it call be used by the web app callbacks.
        mobileAppInterface = windowWithXtremInterfaces.XTREM_WRAPPER_MOBILE_APP_INTERFACE;
        // Declare the web app interface
        windowWithXtremInterfaces.XTREM_WRAPPER_WEB_APP_INTERFACE = {
            startScanning,
            setRawData,
        } as WrapperWebAppInterface;
        // Notify the mobile app that the web interface is now available on the global scope
        if (windowWithXtremInterfaces.XTREM_WRAPPER_MOBILE_APP_INTERFACE.onLoad) {
            windowWithXtremInterfaces.XTREM_WRAPPER_MOBILE_APP_INTERFACE.onLoad();
        }
    }
};

setTimeout(initiateWrapper, 100);
