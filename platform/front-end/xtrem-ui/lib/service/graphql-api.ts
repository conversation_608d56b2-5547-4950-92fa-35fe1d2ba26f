import type {
    CreateOperation,
    DeleteOperation,
    DuplicateOperation,
    Mutations,
    QueryOperation,
    ReadOperation,
    UpdateOperation,
} from '@sage/xtrem-client';
import { ClientError, Graph, GraphMutation } from '@sage/xtrem-client';
import type { Dict, ErrorExtensions, ErrorWithDiagnoses } from '@sage/xtrem-shared';
import { InteropError, objectKeys } from '@sage/xtrem-shared';
import { get, merge, uniq } from 'lodash';
import type { OmitProperties } from 'ts-essentials';
import type { PageProperties } from '../component/container/container-properties';
import type { InternalSectionProperties } from '../component/control-objects';
import type { ReadonlyFieldControlObject } from '../component/readonly-field-control-object';
import type { ComponentKey, FieldDecoratorProps } from '../component/types';
import { ContainerKey, FieldKey } from '../component/types';
import * as xtremRedux from '../redux';
import { refreshNavigationPanel, removePageServerErrors, setIdToQueryParameters } from '../redux/actions';
import { filterFields } from '../utils/abstract-fields-utils';
import { xtremConsole } from '../utils/console';
import { getNonLazySectionsFromScreenDefinition } from '../utils/page-utils';
import { getNavigationPanelDefinitionFromState } from '../utils/state-utils';
import { getFieldProperties } from './graphql-query-builder';
import type { QueryProperty } from './graphql-utils';
import { executeGraphqlQuery, removeEdges } from './graphql-utils';
import { localize } from './i18n-service';
import { NodeCacheService } from './node-cache-service';
import type { PageDefinition } from './page-definition';
import type { ScreenBase } from './screen-base';
import type { ScreenBaseDefinition } from './screen-base-definition';
import { formatScreenValues } from './value-formatter-service';

export const nonSavableFieldType: ComponentKey[] = [FieldKey.Aggregate, FieldKey.Count, FieldKey.Separator];

interface MutationData {
    _id?: string | number;
    data?: Dict<any>;
}

interface NodeApi {
    query: QueryOperation<MutationData>;
    read: ReadOperation<MutationData>;
    queries: {};
    create: CreateOperation<MutationData, MutationData>;
    update: UpdateOperation<MutationData, MutationData>;
    duplicate: DuplicateOperation<MutationData, MutationData>;
    deleteById: DeleteOperation<string>;
    mutations: {};
}
interface DeletionArguments<K> {
    nodeName?: K;
    _id?: string | number;
}

interface DuplicationArguments<K> {
    nodeName?: K;
    _id?: string | number;
    values?: Dict<any>;
}

interface CreationArguments<K> extends DeletionArguments<K> {
    values?: Dict<any>;
    fieldsToReturn?: string[];
}

type ModificationArguments<K> = CreationArguments<K>;

type QueryOrReadExtractor<T> = {
    [K in keyof T]: K extends 'query' | 'read' | 'aggregate' ? T[K] : never;
};

type QueryOrRead<T> = OmitProperties<QueryOrReadExtractor<T>, never>;

const fetcher = (query: string, isMutation: boolean, selector: any, endpoint = '/api'): Promise<any> => {
    const result = executeGraphqlQuery({ query, endpoint }).catch(
        errorResponse => errorResponse?.response?.data || fetcher,
    );
    if (isMutation && selector.mutation) {
        NodeCacheService.flushCacheAfterMutation(selector.mutation);
    }
    return result;
};

const getApiError = (data: {
    extensions?: ErrorExtensions;
    errors: { message: string; extensions?: ErrorExtensions }[];
}): ErrorWithDiagnoses => {
    const message = data.errors.map(e => e.message).join('\n');
    const diagnoses = data.extensions?.diagnoses || [];
    data.errors.forEach(e => {
        diagnoses.push(...(e.extensions?.diagnoses || []));
    });
    return new InteropError(message, diagnoses);
};

export class ReadOnlyGraphQLApi<TGraphqlApi> {
    readonly graph: Graph<TGraphqlApi>;

    constructor() {
        this.graph = new Graph<TGraphqlApi>({
            fetcher,
        });
    }

    node<K extends keyof TGraphqlApi>(nodeName: K): QueryOrRead<TGraphqlApi[K]> {
        return this.graph.node(nodeName) as unknown as QueryOrRead<TGraphqlApi[K]>;
    }

    raw(query: string, isMetaDataQuery = false): Promise<any> {
        if (query.includes('mutation')) {
            xtremConsole.warn('Mutations on this context are not supported. Use at your own risk.');
        }

        return fetcher(query, false, {}, isMetaDataQuery ? '/metadata' : '/api').then(data => {
            if (data) {
                if (data.errors) {
                    throw getApiError(data);
                }

                return { ...data.data, diagnoses: data.extensions?.diagnoses };
            }

            throw new Error(localize('@sage/xtrem-ui/invalid-response-no-data', 'Invalid response, no data provided'));
        });
    }
}

export class GraphQLApi<TGraphqlApi> {
    readonly graph: Graph<TGraphqlApi>;

    constructor(protected readonly screenBase: ScreenBase) {
        this.graph = new Graph<TGraphqlApi>({
            fetcher,
        });
    }

    private getQueryParameters(): Dict<string | number | boolean> {
        return (this.screenBase.$ as unknown as PageDefinition).queryParameters;
    }

    node<K extends keyof TGraphqlApi>(nodeName: K): TGraphqlApi[K] {
        return this.graph.node(nodeName);
    }

    /**
     * Executes a raw GraphQL query string against the server endpoint.
     * @param query the query string
     * @param skipErrorProcessing if this property is set to true, the framework will not try to intercept the error and
     * @returns
     */
    async raw(query: string, skipErrorProcessing = false, isMetaDataQuery = false): Promise<any> {
        const data = await fetcher(query, false, {}, isMetaDataQuery ? '/metadata' : '/api');

        if (data) {
            if (data.errors) {
                if (!skipErrorProcessing) {
                    await this.screenBase.$.processServerErrors(new ClientError(data));
                }

                throw getApiError(data);
            } else {
                return { ...data.data, diagnoses: data.extensions?.diagnoses };
            }
        } else {
            throw new Error(localize('@sage/xtrem-ui/invalid-response-no-data', 'Invalid response, no data provided'));
        }
    }

    private nodeName<K>(node?: K): K {
        const pageProperties = this.screenBase._pageMetadata.uiComponentProperties[
            this.screenBase._pageMetadata.screenId
        ] as PageProperties<any>;
        const nodeName: K | undefined = node || (pageProperties.node as unknown as K);
        if (!nodeName) {
            throw new Error(
                localize('@sage/xtrem-ui/no-node', 'No node was provided neither {{0}} specifies one', [
                    this.screenBase._pageMetadata.screenId,
                ]),
            );
        }

        return nodeName;
    }

    private async applyUpdateToValue(
        nodeName: string,
        response: any,
        args?: DeletionArguments<any>,
        recordAdded = false,
    ): Promise<void> {
        if (!args || !args.nodeName) {
            const store = xtremRedux.getStore();
            const state = store.getState();

            const values = removeEdges(response, true, true);
            const screenId = this.screenBase._pageMetadata.screenId;
            const controlObjects = this.getLoadedFieldControlObjects(state.screenDefinitions[screenId]);
            this.screenBase.$.values = formatScreenValues({
                screenId,
                controlObjects,
                plugins: state.plugins,
                nodeTypes: state.nodeTypes,
                values,
                parentNode: nodeName.toString(),
                userSettings: state.screenDefinitions[screenId].userSettings,
            });

            if (values._id) {
                const dispatch = store.dispatch as xtremRedux.AppThunkDispatch;
                dispatch(setIdToQueryParameters(screenId, values._id));
                if (getNavigationPanelDefinitionFromState(this.screenBase._pageMetadata.screenId, state)) {
                    // This promise is intentionally not awaited so we do not block the UI while the navigation panel is refreshed
                    dispatch(refreshNavigationPanel(this.screenBase._pageMetadata.screenId, recordAdded));
                }
            }
        }
    }

    private getLoadedFieldControlObjects(
        screenDefinition: ScreenBaseDefinition,
    ): Dict<ReadonlyFieldControlObject<any, any, any>> {
        const nonLazySections = getNonLazySectionsFromScreenDefinition(screenDefinition);
        const loadedSection = objectKeys(this.screenBase._pageMetadata.controlObjects).filter(
            key =>
                this.screenBase._pageMetadata.controlObjects[key].componentType === ContainerKey.Section &&
                (screenDefinition.metadata.uiComponentProperties[key] as InternalSectionProperties)?.isLoaded,
        );
        const sectionsToLoad = uniq([...nonLazySections, ...loadedSection]);
        return filterFields(
            this.screenBase._pageMetadata.controlObjects,
            sectionsToLoad,
            screenDefinition as PageDefinition,
            true,
        );
    }

    private getNonTransientFieldsQuery(args?: CreationArguments<any>, forQuery = false): Object {
        const state = xtremRedux.getStore().getState();
        const screenDefinition = state.screenDefinitions[this.screenBase._pageMetadata.screenId];
        const uiComponentProperties = this.screenBase._pageMetadata.uiComponentProperties as FieldDecoratorProps<any>;
        const controlObjects = this.getLoadedFieldControlObjects(screenDefinition);
        return (
            objectKeys(controlObjects)
                // Transient fields are not sent to the server in any case
                .filter(
                    key =>
                        !get(uiComponentProperties, `${key}.isTransient`) &&
                        !get(uiComponentProperties, `${key}.isTransientInput`),
                )
                // Some fields are not saved as they are always the result of a server side calculation.
                .filter(key => forQuery || nonSavableFieldType.indexOf(controlObjects[key].componentType) === -1)
                // Fields that the functional developer not requested are filtered out
                .filter(key => (args && args.fieldsToReturn ? args.fieldsToReturn.indexOf(key) !== -1 : true))
                .map(key => {
                    const fieldProperties: QueryProperty = getFieldProperties(
                        key,
                        controlObjects[key].componentType,
                        get(uiComponentProperties, key),
                        screenDefinition,
                        get(uiComponentProperties, `${key}.bind`),
                        state.nodeTypes,
                        this.nodeName(args?.nodeName),
                        screenDefinition.userSettings?.[key]?.$current,
                    );
                    return typeof fieldProperties === 'string'
                        ? {
                              [fieldProperties]: true,
                          }
                        : fieldProperties;
                })
                .reduce((reduced, next) => merge(reduced, next), {})
        );
    }

    async create<K extends keyof TGraphqlApi>(args?: CreationArguments<K>): Promise<void> {
        // Allow any event handlers to settle and finish updating the value
        await new Promise(resolve => setTimeout(resolve, 20));

        const nodeName = this.nodeName(args && args.nodeName);

        const values: Dict<any> = args?.values ?? this.screenBase.getSerializedValues();

        // ID is not sent whenever a new record is being created
        if (values._id) {
            delete values._id;
        }

        const valuesKeys = this.getNonTransientFieldsQuery(args, true);

        try {
            const node: NodeApi = this.graph.node(nodeName) as unknown as NodeApi;
            const response = await node.create({ ...valuesKeys, _id: true }, { data: values }).execute();
            await this.applyUpdateToValue(nodeName.toString(), response, args, true);
            const dispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
            dispatch(removePageServerErrors(this.screenBase._pageMetadata.screenId));
        } catch (error) {
            if (error.errors) {
                await this.screenBase.$.processServerErrors(error);
            } else {
                xtremRedux.getStore().getState()?.applicationContext?.onTelemetryEvent?.('error', error.message);
                throw new Error(localize('@sage/xtrem-ui/create-error', 'Create error: {{0}}', [error]));
            }
        }
    }

    async update<K extends keyof TGraphqlApi>(args?: ModificationArguments<K>, skipUpdate = false): Promise<void> {
        // Allow any event handlers to settle and finish updating the value
        await new Promise(resolve => setTimeout(resolve, 20));
        const nodeName = this.nodeName(args && args.nodeName);

        const values: Dict<any> = args?.values ?? this.screenBase.getSerializedValues();
        const valuesKeys = this.getNonTransientFieldsQuery(args, true);

        const recordId = this.getQueryParameters()._id as number;
        const argsId = args?._id || args?.values?._id;
        const additionalValues = { _id: argsId || String(recordId) };

        try {
            const node: NodeApi = this.graph.node(nodeName) as unknown as NodeApi;
            const response = await node
                .update({ ...valuesKeys, _id: true, _etag: true }, { data: { ...additionalValues, ...values } })
                .execute();
            if (((recordId && !argsId) || argsId === recordId) && !skipUpdate) {
                await this.applyUpdateToValue(nodeName.toString(), response, args);
                const dispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
                dispatch(removePageServerErrors(this.screenBase._pageMetadata.screenId));
            }
        } catch (error) {
            if (error.errors) {
                await this.screenBase.$.processServerErrors(error);
            } else {
                throw new Error(localize('@sage/xtrem-ui/update-error', 'Update error: {{0}}', [error]));
            }
        }
    }

    async delete<K extends keyof TGraphqlApi>(args?: DeletionArguments<K>): Promise<void> {
        const nodeName = this.nodeName(args && args.nodeName);
        const recordId = args?._id || this.getQueryParameters()._id;

        if (!recordId) {
            throw new Error(
                localize('@sage/xtrem-ui/no-record-id-provided', 'No record id was provided through query parameters'),
            );
        }

        try {
            const node: NodeApi = this.graph.node(nodeName) as unknown as NodeApi;
            await node.deleteById(String(recordId)).execute();
            const dispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
            if (
                getNavigationPanelDefinitionFromState(this.screenBase._pageMetadata.screenId) &&
                (!args?._id || args?._id === this.getQueryParameters()._id)
            ) {
                // This promise is intentionally not awaited so we do not block the UI while the navigation panel is refreshed
                dispatch(refreshNavigationPanel(this.screenBase._pageMetadata.screenId));
            }
        } catch (error) {
            if (error.errors) {
                await this.screenBase.$.processServerErrors(error);
            } else {
                throw new Error(localize('@sage/xtrem-ui/delete-error', 'Delete error: {{0}}', [error]));
            }
        }
    }

    async duplicate<K extends keyof TGraphqlApi>(args?: DuplicationArguments<K>): Promise<string | null> {
        const nodeName = this.nodeName(args && args.nodeName);
        const recordId = args?._id || this.getQueryParameters()._id;

        if (!recordId) {
            throw new Error(
                localize('@sage/xtrem-ui/no-record-id-provided', 'No record id was provided through query parameters'),
            );
        }
        const values: Dict<any> | undefined = args?.values;
        const duplicateArgs: { _id: string; data?: Dict<any> } = { _id: String(recordId) };
        if (values) {
            duplicateArgs.data = values;
        }

        try {
            const node: NodeApi = this.graph.node(nodeName) as unknown as NodeApi;
            const response = await node.duplicate({ _id: true }, duplicateArgs).execute();
            const dispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
            dispatch(removePageServerErrors(this.screenBase._pageMetadata.screenId));
            return String(response._id);
        } catch (error) {
            if (error.errors) {
                await this.screenBase.$.processServerErrors(error);
                return null;
            }
            throw new Error(localize('@sage/xtrem-ui/duplicate-error', 'Duplication error: {{0}}', [error]));
        }
    }
}

export class GraphQLMutationApi<TGraphqlApi> {
    readonly graph: GraphMutation<TGraphqlApi>;

    constructor() {
        this.graph = new GraphMutation<TGraphqlApi>({
            fetcher,
        });
    }

    node<K extends keyof TGraphqlApi & string>(nodeName: K): Pick<TGraphqlApi[K], Mutations & keyof TGraphqlApi[K]> {
        return this.graph.node(nodeName) as Pick<TGraphqlApi[K], Mutations & keyof TGraphqlApi[K]>;
    }
}
