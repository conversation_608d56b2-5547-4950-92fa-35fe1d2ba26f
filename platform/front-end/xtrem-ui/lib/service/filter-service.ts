import type { ClientNode } from '@sage/xtrem-client';
import { date as xtremDate } from '@sage/xtrem-date-time';
import { geFilterQuery } from '@sage/xtrem-filter-utils';
import type { Dict, FilterType, FiltrableType } from '@sage/xtrem-shared';
import { isEmpty, isEqual, set, uniqWith } from 'lodash';
import { getFieldTitle } from '../component/field/carbon-helpers';
import { getReferencePath } from '../component/field/reference/reference-utils';
import type { NestedField, NestedFieldTypes } from '../component/nested-fields';
import { FieldKey, RANGE_DIVIDER } from '../component/types';
import type { FilterManagerField } from '../component/ui/filter/filter-manager';
import type { NodePropertyType } from '../types';
import {
    findColumnDefinitionByBind,
    getPropertyScalarType,
    normalizeUnderscoreBind,
} from '../utils/abstract-fields-utils';
import { convertDeepBindToPathNotNull } from '../utils/nested-field-utils';
import { findDeepPropertyType } from '../utils/node-utils';
import { schemaTypeNameFromNodeName } from '../utils/transformers';
import type { GraphQLFilter, QueryArguments } from './graphql-utils';
import type { FormattedNodeDetails } from './metadata-types';
import type { ScreenBase } from './screen-base';

export interface Filter<T extends FiltrableType = string> {
    id: string;
    value: Array<{
        filterValue: FilterValue<T>;
        /** Indicates that the filter should be applied against the ID field, it is used with nested objects (e.g reference columns) */
        filterById?: boolean;
        filterType: {
            value: FilterType<T>;
            text: string;
        };
    }>;
}

export type FilterValue<T extends FiltrableType = string> = T extends string ? T | T[] : T;

const agGridFilterMidnightDateToStringDate = (value: any): string => {
    try {
        const asDate = xtremDate.parse(value, 'base', 'YYYY-MM-DD 00:00:00');
        return asDate.format('YYYY-MM-DD');
    } catch {
        return value;
    }
};

const generate = <T extends FiltrableType>(filter: Filter<T>, column: FilterManagerField): any => {
    const columnType = column?.type || '';
    const isDate = columnType === FieldKey.Date;
    const filterType = filter.value[0].filterType;
    const filterValue = isDate
        ? (filter.value[0].filterValue as string)
              .toString()
              .split(RANGE_DIVIDER)
              .map(agGridFilterMidnightDateToStringDate)
              .join(RANGE_DIVIDER)
        : (filter.value[0].filterValue as string);
    if (filterValue !== undefined && filterValue !== null) {
        const filterQuery = geFilterQuery({
            filterType: filterType.value,
            filterValue,
            type: column.properties.type ?? 'String',
        });
        const isReferenceObject = columnType === FieldKey.Reference;
        if (isReferenceObject) {
            const valueFieldKey = filter.value[0]?.filterById
                ? '_id'
                : getReferencePath<any>(column.properties.valueField);
            return set<any>({}, valueFieldKey, filterQuery);
        }
        return filterQuery;
    }
    return {};
};
const transformFilterToGraphQLFilter = <T extends FiltrableType>(
    filter: Filter<T>,
    column: FilterManagerField,
): GraphQLFilter => {
    const bind = normalizeUnderscoreBind(filter.id);
    const queryFilter: GraphQLFilter = {};
    const { filterValue } = filter.value[0];
    if (filterValue !== undefined && filterValue !== null) {
        if (filter.value.length > 1) {
            // Combined Filter (_or)
            queryFilter._or = filter.value.map(value => set({}, bind, generate({ id: bind, value: [value] }, column)));
        } else {
            // Single Filter
            set(queryFilter, bind, generate(filter, column));
        }
    }
    return queryFilter;
};

export const getGraphQLFilter = <T extends FiltrableType>(
    filters: Filter<T>[],
    nestedFields: FilterManagerField[],
): GraphQLFilter => {
    let queryFilter: GraphQLFilter = {};

    if (filters.length === 1) {
        const rtFilter = filters[0];
        queryFilter = transformFilterToGraphQLFilter(rtFilter, findColumnDefinitionByBind(nestedFields, rtFilter.id)!);
    } else if (filters.length > 1) {
        queryFilter._and = filters.map(rtFilter =>
            transformFilterToGraphQLFilter(rtFilter, findColumnDefinitionByBind(nestedFields, rtFilter.id)!),
        );
    }

    return queryFilter;
};

export const getGraphQLFilterAsString = (queryFilter: GraphQLFilter): QueryArguments['filter'] => {
    return isEmpty(queryFilter) ? undefined : JSON.stringify(queryFilter);
};

export const getFilterAsGraphQLFilterString = (
    filters: Filter[],
    nestedFields: FilterManagerField[],
): QueryArguments['filter'] => {
    const queryFilter = getGraphQLFilter(filters, nestedFields);
    return getGraphQLFilterAsString(queryFilter);
};

export const mergeGraphQLFilters = <T extends ClientNode = any>(
    graphQLFilters: (GraphQLFilter<T> | undefined)[],
): any => {
    let filters = uniqWith(graphQLFilters, isEqual);
    filters = filters.filter(filter => !isEmpty(filter)) as GraphQLFilter<T>[];
    return isEmpty(filters) ? undefined : filters.length === 1 ? filters[0] : { _and: filters };
};

export const getTypedNestedFields = (
    screenId: string,
    node: NodePropertyType,
    nestedFields: NestedField<ScreenBase, NestedFieldTypes>[],
    knownNodeTypes: Dict<FormattedNodeDetails>,
): FilterManagerField[] => {
    return nestedFields.map(nestedField => {
        const bind = convertDeepBindToPathNotNull(nestedField.properties.bind);
        const propertyType =
            findDeepPropertyType(schemaTypeNameFromNodeName(node), nestedField.properties.bind, knownNodeTypes, true)
                ?.type || FieldKey.Text;

        const fieldType = nestedField.type;
        return {
            type: fieldType,
            properties: {
                ...nestedField.properties,
                bind,
                title: getFieldTitle(
                    screenId,
                    { title: 'title' in nestedField.properties ? nestedField.properties.title : undefined },
                    null,
                ),
                type: getPropertyScalarType(
                    knownNodeTypes,
                    propertyType,
                    nestedField.type,
                    (nestedField.properties as any).valueField,
                ),
            },
        };
    });
};
