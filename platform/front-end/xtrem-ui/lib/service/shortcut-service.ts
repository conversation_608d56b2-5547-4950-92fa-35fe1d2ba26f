import { objectKeys, type Dict } from '@sage/xtrem-shared';
import { getStore as shortcutService } from '../redux';

const currentSequence: string[] = [];

const registeredCombinations: Dict<RegisteredKeyCombo> = {};

// For testing
export const getCurrentKeySequence = (): string[] => {
    return [...currentSequence];
};

export const clearKeyCombination = (): void => {
    objectKeys(registeredCombinations).forEach(k => {
        delete registeredCombinations[k];
    });
    currentSequence.splice(0, currentSequence.length);
};

const multiKeys: string[] = ['Shift', 'Meta', 'Control', 'Alt'];
const normalizeKey = (event: KeyboardEvent): string => {
    // BL: Some selenium based tests do not provide the KEY on the event, we need to handle that
    if (event.key && event.code) {
        let key = event.code.replace('Key', '');
        multiKeys.forEach(k => {
            if (key.indexOf(k) === 0) {
                key = key.replace('Right', '').replace('Left', '');
            }
        });
        key = key.toLowerCase();
        return key === 'meta' ? 'control' : key;
    }
    return 'unknown';
};

export type Key =
    | 'escape'
    | 'control'
    | 'shift'
    | 'alt'
    | 'arrowup'
    | 'arrowdown'
    | 'space'
    | 'a'
    | 'b'
    | 'c'
    | 'd'
    | 'e'
    | 'f'
    | 'g'
    | 'h'
    | 'i'
    | 'j'
    | 'k'
    | 'l'
    | 'm'
    | 'n'
    | 'o'
    | 'p'
    | 'q'
    | 'r'
    | 's'
    | 't'
    | 'v'
    | 'w'
    | 'z'
    | '1'
    | '2'
    | '3'
    | '4'
    | '5'
    | '6'
    | '7'
    | '8'
    | '9'
    | '0'
    | 'f1'
    | 'f2'
    | 'f3'
    | 'f4'
    | 'f5'
    | 'f6'
    | 'f7'
    | 'f8'
    | 'f9'
    | 'f10'
    | 'f11'
    | 'f12';

interface RegisteredKeyCombo {
    combination: Key[];
    callback: () => void;
}

document.addEventListener('keydown', (event: KeyboardEvent): boolean => {
    const key = normalizeKey(event);
    if (currentSequence.indexOf(key) === -1 && key !== 'dead') {
        currentSequence.push(key);
        const sortedCombination = currentSequence;
        sortedCombination.sort();
        // eslint-disable-next-line no-restricted-syntax
        for (const keyCombination of Object.values(registeredCombinations).reverse()) {
            if (
                shortcutService().getState().isKeyboardShortcutsEnabled &&
                keyCombination.combination.length === sortedCombination.length &&
                keyCombination.combination.every(e => sortedCombination.includes(e))
            ) {
                event.preventDefault();
                event.stopPropagation();
                keyCombination.callback();
                return false;
            }
        }
    }
    return true;
});

document.addEventListener('keyup', (event: KeyboardEvent): void => {
    const normalizedKey = normalizeKey(event);
    const index = currentSequence.indexOf(normalizedKey);

    if (index !== -1) {
        currentSequence.splice(index, 1);
    }
});

// Clean up keys when the user navigates away from the window
window.addEventListener('blur', (): void => {
    currentSequence.splice(0, currentSequence.length);
});

let id = 1;

export const subscribe = (combination: Key | Key[], callback: () => void): number => {
    id += 1;
    const sortedCombination = Array.isArray(combination) ? combination : [combination];
    sortedCombination.sort();
    registeredCombinations[id] = {
        combination: sortedCombination,
        callback,
    };
    return id;
};

export const unsubscribe = (shortcutSubscriptionId: number | null): void => {
    if (shortcutSubscriptionId !== null) {
        delete registeredCombinations[shortcutSubscriptionId];
    }
};
