import type { AggregateValuesSelector, ClientNode } from '@sage/xtrem-client';
import { DateValue, datePropertyValueToDateString } from '@sage/xtrem-date-time';
import { objectKeys, type Dict } from '@sage/xtrem-shared';
import { EnumType } from 'json-to-graphql-query';
import { cloneDeep, get, isEmpty, merge, omit, set } from 'lodash';
import type { MarkRequired } from 'ts-essentials';
import {
    applyDeepNestedKey,
    createOrderByQueryArgument,
    getAggregationValues,
    getFilterAsString,
    getNestedFieldsQuery,
    queryBuilder,
} from '.';
import { navigationPanelId } from '../../component/container/navigation-panel/navigation-panel-types';
import type {
    AggregateProperties,
    CardProperties,
    CountProperties,
    DetailListProperties,
    InternalChartProperties,
    InternalNestedGridProperties,
    MultiFileDepositProperties,
    OptionsMenuItem,
    PluginProperties,
    PodCollectionDecoratorProperties,
    PodProperties,
    ReferenceProperties,
    TableProperties,
    TableSummaryProperties,
    TreeProperties,
    VitalPodProperties,
} from '../../component/control-objects';
import type { PageDecoratorProperties } from '../../component/decorator-properties';
import type { InternalCalendarProperties } from '../../component/field/calendar/calendar-types';
import { getMultiFileDepositFieldColumns } from '../../component/field/multi-file-deposit/multi-file-deposit-utils';
import type { PropertyValueType } from '../../component/field/reference/reference-types';
import type {
    InternalTableProperties,
    TableUserSettings,
    TableViewLevel,
} from '../../component/field/table/table-component-types';
import type { NestedGroupAggregations } from '../../component/field/traits';
import type { NestedField, NestedFieldTypes, PodNestedFieldTypes } from '../../component/nested-fields';
import type { NestedTechnicalProperties } from '../../component/nested-fields-properties';
import type { AggregationMethod, ComponentKey, OrderByType } from '../../component/types';
import { FieldKey } from '../../component/types';
import type { UiComponentProperties } from '../../plugin';
import { getStore } from '../../redux';
import type { UiComponentUserSettings } from '../../redux/state';
import { GraphQLKind, type NodePropertyType } from '../../types';
import { getFirstDayOfMonth, getLastDayOfMonth } from '../../utils/formatters';
import {
    convertDeepBindToPath,
    convertDeepBindToPathNotNull,
    getNestedFieldsFromProperties,
} from '../../utils/nested-field-utils';
import { findDeepPropertyDetails } from '../../utils/node-utils';
import { resolveByValue } from '../../utils/resolve-value-utils';
import { getOrderByFromSortModel } from '../../utils/table-component-utils';
import { parseScreenValues, schemaTypeNameFromNodeName } from '../../utils/transformers';
import type { AggFunc } from '../collection-data-utils';
import { mergeGraphQLFilters } from '../filter-service';
import type {
    GraphQLFilter,
    NodeCollection,
    QueryArguments,
    QueryBuilderOptions,
    QueryProperty,
    QueryWrapper,
} from '../graphql-utils';
import { getNodeForLookupOperation, wrapQuery } from '../graphql-utils';
import type { FormattedNodeDetails } from '../metadata-types';
import type { Page } from '../page';
import type { ScreenBase } from '../screen-base';
import type { ScreenBaseDefinition } from '../screen-base-definition';
import { formatInputProperty } from '../value-serializer-service';

type CollectionPropertiesType =
    | Omit<InternalTableProperties<Page>, 'selectedRecords'>
    | DetailListProperties
    | PodCollectionDecoratorProperties
    | MultiFileDepositProperties;

export interface FieldQueryBuilderFunctionArgs {
    elementId: string;
    fieldProperties: UiComponentProperties;
    screenDefinition: ScreenBaseDefinition;
    nodeTypes?: Dict<FormattedNodeDetails>;
    node?: NodePropertyType;
    userSettings?: UiComponentUserSettings;
}

export const getFieldProperties = (
    elementId: string,
    fieldType: ComponentKey,
    fieldProperties: UiComponentProperties,
    screenDefinition: ScreenBaseDefinition,
    bind?: PropertyValueType,
    nodeTypes?: Dict<FormattedNodeDetails>,
    node?: NodePropertyType,
    userSettings?: UiComponentUserSettings,
): QueryProperty => {
    return applyDeepNestedKey(
        elementId,
        getFieldPropertiesWithoutAlias(fieldType, {
            elementId,
            fieldProperties,
            screenDefinition,
            node,
            nodeTypes,
            userSettings,
        }),
        Boolean(bind && bind !== elementId),
        bind,
        node,
        nodeTypes,
    );
};

/**
 * Create a GraphQL request fragment for a given field type.
 * The function checks the field type, and based on that calls the corresponding query builder function.
 *
 * @param fieldType
 * @param queryBuilderFunctionArgs
 * @returns
 */
export const getFieldPropertiesWithoutAlias = (
    fieldType: ComponentKey,
    queryBuilderFunctionArgs: FieldQueryBuilderFunctionArgs,
): QueryProperty => {
    switch (fieldType) {
        case FieldKey.Card:
            return getCardFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.Calendar:
            return getCalendarFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.Chart:
            return getChartFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.Count:
            return getCountFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.Aggregate:
            return getAggregateFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.FilterEditor:
            throw new Error(`Unimplemented getFieldPropertiesWithoutAlias`);
        case FieldKey.FileDeposit:
            return getFileDepositProperties();
        case FieldKey.MultiFileDeposit:
            return getMultiFileDepositProperties(queryBuilderFunctionArgs);
        case FieldKey.File:
        case FieldKey.Preview:
        case FieldKey.Image:
        case FieldKey.VisualProcess:
        case FieldKey.RichText:
        case FieldKey.FormDesigner:
            return getStreamFieldProperties();
        case FieldKey.MultiReference:
        case FieldKey.Reference:
            return getReferenceFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.Table:
            return getTableFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.TableSummary:
            return getTableSummaryFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.Tree:
            return getTreeFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.Technical:
            return getTechnicalFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.PodCollection:
            return getPodCollectionFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.NestedGrid:
            return getNestedGridFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.DetailList:
            return getDetailListFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.Plugin:
            return getPluginFieldProperties(queryBuilderFunctionArgs);
        case FieldKey.VitalPod:
        case FieldKey.Pod:
            return getPodFieldProperties(queryBuilderFunctionArgs);
        default:
            // Primitive field types such as number, text, checkbox etc come here.
            return true;
    }
};

const getCalendarDefaultDateRange = (): { firstDay: DateValue; lastDay: DateValue } => {
    const currentDate = DateValue.today();
    const lastDayOfPreviousMonth = getFirstDayOfMonth(currentDate);
    const lastDayOfCurrentMonth = getLastDayOfMonth(currentDate);

    // By default, we need to get the last events of the previous month and the first events
    // of the following month
    const firstDay = lastDayOfPreviousMonth.addDays(-6);
    const lastDay = lastDayOfCurrentMonth.addDays(5);

    return {
        firstDay,
        lastDay,
    };
};

export interface BuildReferenceFieldQueryProps<T extends ClientNode = any> {
    after?: string;
    contextNode?: NodePropertyType;
    elementId: string;
    fieldProperties: Omit<ReferenceProperties<ScreenBase, T> | TableProperties<ScreenBase, T>, 'parent'>;
    filter?: GraphQLFilter<T>;
    group?: { key: string; value?: string; aggFunc?: AggFunc; type: FieldKey };
    isFilterLimitedToDataset?: boolean;
    level?: number;
    nodeTypes?: Dict<FormattedNodeDetails>;
    /**
     * If it is an initial page load query for the navigation panel, the option menu is added to the query filter by this method.
     * In other cases (e.g. filter change, sorting change), the collection data service provided filter already contains the currently selected
     * option item.
     *  */
    optionMenuItem?: OptionsMenuItem;
    orderBy?: OrderByType<T>;
    pageSize?: number;
    parentElementId?: string;
    queryAlias?: string;
    recordContext?: Dict<any>;
    screenDefinition: ScreenBaseDefinition;
    screenId: string;
    valueField?: any;
}

export const buildTableQuery = ({
    rootNodeId,
    elementId,
    columns,
    screenDefinition,
    queryArguments,
    bind,
    group,
    treeProperties,
    totalCount = false,
    node,
    nodeTypes,
}: {
    rootNodeId: string;
    elementId: string;
    columns: NestedField<Page, NestedFieldTypes>[];
    screenDefinition: ScreenBaseDefinition;
    queryArguments?: QueryArguments;
    bind?: PropertyValueType;
    group?: { key: string; value?: string; type: FieldKey };
    treeProperties?: TreeProperties<Page>;
    totalCount?: boolean;
    node?: string;
    nodeTypes?: Dict<FormattedNodeDetails>;
}): NodeCollection => {
    let aggregations;
    if (group?.value !== undefined && treeProperties) {
        // eslint-disable-next-line @sage/redos/no-vulnerable
        const nestedValueMatch = group.key.match(/(.*)\./);
        const values = getAggregationValues(
            nestedValueMatch && group.type === FieldKey.Reference
                ? set({}, `${nestedValueMatch[1]}._id`, 'distinctCount')
                : {},
        );

        aggregations = {
            queryAggregate: {
                edges: {
                    node: {
                        group: set(
                            nestedValueMatch && group.type === FieldKey.Reference
                                ? set({}, `${nestedValueMatch[1]}._id`, true)
                                : {},
                            group.key,
                            true,
                        ),
                        values,
                    },
                    cursor: true,
                },
            },
        };
    }

    return {
        query: {
            __args: { filter: JSON.stringify({ _id: { _eq: rootNodeId } }) },
            edges: {
                node: {
                    _id: true,
                    ...applyDeepNestedKey(
                        elementId,
                        {
                            ...(group?.value === undefined &&
                                getNestedFieldsQuery({
                                    screenDefinition,
                                    nestedFields: columns,
                                    node,
                                    nodeTypes,
                                    queryArguments,
                                })),
                            ...aggregations,
                        },
                        Boolean(bind !== elementId),
                        bind,
                        node,
                        nodeTypes,
                    ),
                },
                cursor: true,
            },
            pageInfo: {
                startCursor: true,
                endCursor: true,
                hasPreviousPage: true,
                hasNextPage: true,
            },
            ...(totalCount && { totalCount: true }),
        },
    };
};

export const buildNestedAggregationQuery = ({
    group,
    rootNodeId,
    elementId,
    bind,
}: {
    group: { key: string; value?: string; type: FieldKey };
    rootNodeId: string;
    elementId: string;
    bind?: PropertyValueType;
}): NodeCollection => {
    // eslint-disable-next-line @sage/redos/no-vulnerable
    const nestedValueMatch = group.key.match(/(.*)\./);
    const values = getAggregationValues(
        nestedValueMatch && group.type === FieldKey.Reference
            ? set({}, `${nestedValueMatch[1]}._id`, 'distinctCount')
            : {},
    );

    const aggregations = {
        queryAggregate: {
            edges: {
                node: {
                    group: set(
                        nestedValueMatch && group.type === FieldKey.Reference
                            ? set({}, `${nestedValueMatch[1]}._id`, true)
                            : {},
                        group.key,
                        true,
                    ),
                    values,
                },
                cursor: true,
            },
        },
    };

    return {
        query: {
            __args: { filter: JSON.stringify({ _id: { _eq: rootNodeId } }) },
            edges: {
                node: {
                    _id: true,
                    ...applyDeepNestedKey(elementId, aggregations, false, bind),
                },
                cursor: true,
            },
        },
    };
};

export const buildSimpleAggregationQuery = ({
    contextNode,
    fieldNode,
    referenceProperties,
    group,
    isFilterLimitedToDataset,
    queryAlias,
    queryBuilderOptions,
    nodeTypes,
}: {
    contextNode?: NodePropertyType;
    fieldNode: NodePropertyType;
    referenceProperties: ReferenceProperties;
    group?: { key: string; value?: string; aggFunc?: AggFunc; type: FieldKey };
    isFilterLimitedToDataset?: boolean;
    queryAlias?: string;
    queryBuilderOptions: QueryBuilderOptions;
    nodeTypes?: Dict<FormattedNodeDetails>;
}): {
    query: QueryWrapper<any>;
    bind?: string | null;
    fieldNode: NodePropertyType;
    node: NodePropertyType;
} => {
    let groupBy;
    if (isFilterLimitedToDataset) {
        const valueField =
            typeof referenceProperties.valueField === 'object'
                ? objectKeys(referenceProperties.valueField)[0]
                : referenceProperties.valueField;
        groupBy = { key: `${referenceProperties.bind}.${valueField}` };
    } else {
        groupBy = group;
    }
    if (!groupBy) {
        throw Error('For some unknown reason we have groupBy as undefined, please check.');
    }
    // eslint-disable-next-line @sage/redos/no-vulnerable
    const nestedValueMatch = groupBy.key.match(/(.*)\./);

    const mainGroupAggregation = set(
        {},
        groupBy.key,
        groupBy.aggFunc === undefined ? true : { __args: { by: new EnumType(groupBy.aggFunc) } },
    );

    const values = { _id: { distinctCount: true } };

    if (referenceProperties.columns && nodeTypes) {
        referenceProperties.columns.forEach(column => {
            const groupAggregationMethod = (column.properties as NestedGroupAggregations).groupAggregationMethod;
            if (!groupAggregationMethod) {
                return;
            }
            let normalizedBind = convertDeepBindToPathNotNull(column.properties.bind);
            if (column.type === FieldKey.Reference) {
                normalizedBind += `._id`;
            }

            const propDetails = findDeepPropertyDetails(
                schemaTypeNameFromNodeName(fieldNode),
                normalizedBind,
                nodeTypes,
            );
            if (propDetails?.canFilter && propDetails.canSort && propDetails.isOnOutputType) {
                set(values, `${normalizedBind}.${groupAggregationMethod}`, true);
            }
        });
    }

    const aggregations = {
        queryAggregate: {
            edges: {
                node: {
                    group:
                        group?.type === FieldKey.Reference && nestedValueMatch
                            ? set(mainGroupAggregation, `${nestedValueMatch[1]}._id`, true)
                            : mainGroupAggregation,
                    values,
                },
                cursor: true,
            },
            __args:
                queryBuilderOptions.queryArguments && objectKeys(queryBuilderOptions.queryArguments).length > 0
                    ? queryBuilderOptions.queryArguments
                    : undefined,
        },
    };
    const nodeName = isFilterLimitedToDataset && contextNode ? contextNode : fieldNode;
    return {
        query: wrapQuery(nodeName, aggregations, queryAlias),
        bind: null,
        node: fieldNode,
        fieldNode,
    };
};

export function buildLookupsQuery({
    contextNode,
    elementId,
    fieldNode,
    fieldProperties,
    level,
    nodeCollectionQuery,
    nodeTypes,
    parentElementId,
    queryAlias,
    recordContext,
    screenDefinition,
    screenId,
    screenProperties,
}: {
    contextNode?: NodePropertyType;
    elementId: string;
    fieldNode: NodePropertyType;
    fieldProperties: Omit<ReferenceProperties | TableProperties, 'parent'>;
    level?: number;
    nodeCollectionQuery: NodeCollection;
    nodeTypes: Dict<FormattedNodeDetails>;
    parentElementId?: string;
    queryAlias?: string;
    recordContext?: Record<string, any>;
    screenDefinition: ScreenBaseDefinition;
    screenId: string;
    screenProperties: PageDecoratorProperties<any>;
}): {
    query: QueryWrapper<any>;
    bind?: string | null;
    fieldNode: NodePropertyType;
    node: NodePropertyType;
} {
    const bind = convertDeepBindToPathNotNull(fieldProperties.bind || elementId);
    // No need for aliases
    delete nodeCollectionQuery.query.edges.node.__aliasFor;
    // If the record context is provided, the lookup query is triggered from a nested field, if not we use the page's value
    const lookupData = recordContext
        ? formatInputProperty({
              value: omit(cloneDeep(recordContext), bind),
              property: {
                  type: schemaTypeNameFromNodeName(String(contextNode || screenProperties.node)),
                  kind: GraphQLKind.Object,
              },
              nodeTypes,
              isTopLevel: true,
          })
        : parseScreenValues(screenId);

    // Filter out invalid, local IDs
    if (lookupData?._id && Number(lookupData._id) < 0) {
        delete lookupData._id;
    }

    const lookups = {
        lookups: {
            __args: {
                data: lookupData ?? {},
            },
        },
    };

    set(lookups.lookups, bind, nodeCollectionQuery.query);

    const node = getNodeForLookupOperation({ parentElementId, screenDefinition, level });

    return { query: wrapQuery(node, lookups, queryAlias), bind, node, fieldNode };
}

export function buildReferenceFieldQuery({
    after,
    contextNode,
    elementId,
    fieldProperties,
    filter,
    group,
    isFilterLimitedToDataset,
    level,
    nodeTypes = {},
    optionMenuItem,
    orderBy,
    pageSize,
    parentElementId,
    queryAlias,
    recordContext,
    screenDefinition,
    screenId,
}: BuildReferenceFieldQueryProps): {
    query: QueryWrapper<any>;
    bind?: string | null;
    fieldNode: NodePropertyType;
    node: NodePropertyType;
} {
    let queryFilter = filter;
    const fieldNode = fieldProperties.node as NodePropertyType;
    const screenProperties = screenDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<any>;
    const node: NodePropertyType = fieldNode;
    const bind: string | null = null;

    const queryBuilderOptions: MarkRequired<QueryBuilderOptions, 'queryArguments'> = {
        properties: [],
        queryArguments: {},
    };

    if (optionMenuItem?.graphQLFilter) {
        /**
         * The option menu filter is only added to the query here if the initial page data is getting queried. In any other case, the filter
         * provided by the collection item value contains the selection option menu filter
         **/
        queryFilter = mergeGraphQLFilters([queryFilter || {}, optionMenuItem?.graphQLFilter]);
    }

    if (queryFilter && !isEmpty(queryFilter)) {
        queryBuilderOptions.queryArguments.filter = JSON.stringify(queryFilter);
    }

    const shouldOnlyGenerateAggregationQuery =
        (group !== undefined && (level ?? 0) <= 0 && fieldProperties && group.value == null) ||
        isFilterLimitedToDataset;

    /**
     * We need to make sure that the _id is always part of the sorting order so we can ensure that the server serves the entries in a predictable
     * order.
     * The only exception is we request aggregated data, in that case we only interested in the aggregated value and no sorting is taking place.
     *  */
    if (!isEmpty(orderBy)) {
        queryBuilderOptions.queryArguments.orderBy = JSON.stringify(
            orderBy?._id || shouldOnlyGenerateAggregationQuery ? orderBy : { ...orderBy, _id: 1 },
        );
    } else if (!shouldOnlyGenerateAggregationQuery) {
        queryBuilderOptions.queryArguments.orderBy = JSON.stringify({ _id: 1 });
    }

    if (after) {
        queryBuilderOptions.queryArguments.after = after;
    }

    if (pageSize) {
        queryBuilderOptions.queryArguments.first = pageSize;
    }

    /**
     * This condition chunk of code will only generate a AggregationQuery and return it.
     */
    if (shouldOnlyGenerateAggregationQuery) {
        return buildSimpleAggregationQuery({
            contextNode,
            fieldNode,
            referenceProperties: fieldProperties as ReferenceProperties,
            group,
            isFilterLimitedToDataset,
            queryAlias,
            queryBuilderOptions,
            nodeTypes,
        });
    }

    const bindForQueryBuilder: string | undefined =
        // If the field is transient, we should not pass in the bind property as we don't need an alias.
        fieldProperties.bind &&
        !fieldProperties.isTransient &&
        !screenProperties.isTransient &&
        !!screenProperties.node &&
        // We also don't need an alias if the field is deeply bound
        !convertDeepBindToPathNotNull(fieldProperties.bind).includes('.')
            ? fieldProperties.bind
            : undefined;

    const generatedQuery = getFieldProperties(
        elementId,
        FieldKey.Reference,
        fieldProperties,
        screenDefinition,
        bindForQueryBuilder,
        nodeTypes,
        node,
    );

    const nodeCollectionQuery = queryBuilder(fieldNode, queryBuilderOptions);
    nodeCollectionQuery.query.edges.node = merge(nodeCollectionQuery.query.edges.node, get(generatedQuery, elementId));

    const isDynamicPod = Boolean(
        parentElementId &&
            screenDefinition.metadata.uiComponentProperties[parentElementId] &&
            screenDefinition.metadata.uiComponentProperties[parentElementId]._controlObjectType === FieldKey.DynamicPod,
    );

    // If the page or the field is NOT transient OR the parent is not a Dynamic pod, we use the lookup API instead of globally querying the graph for possible items
    const shouldUseLookups =
        !isDynamicPod &&
        fieldProperties._controlObjectType !== FieldKey.FilterSelect &&
        !screenProperties?.isTransient &&
        screenProperties?.node &&
        !fieldProperties.isTransient &&
        !fieldProperties.isTransientInput &&
        // We should never try to use the lookups if the target element is the navigation panel
        elementId !== navigationPanelId &&
        // We can't use lookups for deep nested reference fields
        !(convertDeepBindToPath(fieldProperties?.bind) || elementId).includes('.');
    /**
     * This condition chunk of code will only generate a lookups query and return it.
     */
    if (
        shouldUseLookups &&
        (screenDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<Page>).node
    ) {
        return buildLookupsQuery({
            contextNode,
            elementId,
            fieldNode,
            fieldProperties,
            level,
            nodeCollectionQuery,
            nodeTypes,
            parentElementId,
            queryAlias,
            recordContext,
            screenDefinition,
            screenId,
            screenProperties,
        });
    }

    return {
        query: wrapQuery(fieldNode, nodeCollectionQuery, queryAlias),
        bind,
        node,
        fieldNode,
    };
}

const getCalendarFilters = (calendarProperties: InternalCalendarProperties): GraphQLFilter => {
    const defaultRange = getCalendarDefaultDateRange();

    const rangeStartValue = calendarProperties.rangeStart
        ? datePropertyValueToDateString(calendarProperties.rangeStart)
        : defaultRange.firstDay.toString();

    const rangeEndValue = calendarProperties.rangeEnd
        ? datePropertyValueToDateString(calendarProperties.rangeEnd)
        : defaultRange.lastDay.toString();

    const filterStartFieldOnly = set({}, convertDeepBindToPathNotNull(calendarProperties.startDateField), {
        _gte: rangeStartValue,
        _lte: rangeEndValue,
    });

    return calendarProperties.endDateField
        ? {
              _or: [
                  // We need to filter on start date only for events that don't have an end date
                  {
                      ...filterStartFieldOnly,
                  },
                  {
                      _and: [
                          set({}, convertDeepBindToPathNotNull(calendarProperties.startDateField), {
                              _lte: rangeEndValue,
                          }),
                          set({}, convertDeepBindToPathNotNull(calendarProperties.endDateField), {
                              _gte: rangeStartValue,
                          }),
                      ],
                  },
              ],
          }
        : filterStartFieldOnly;
};

const getCardFieldProperties = ({
    fieldProperties,
    screenDefinition,
    node,
    nodeTypes,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const cardProperties = fieldProperties as CardProperties;
    const cardDefinition = cardProperties.cardDefinition;
    const fieldsToRequest: NestedField<Page, NestedFieldTypes>[] = [];
    fieldsToRequest.push(cardDefinition.title);
    if (cardDefinition.titleRight) {
        fieldsToRequest.push(cardDefinition.titleRight);
    }
    if (cardDefinition.line2) {
        fieldsToRequest.push(cardDefinition.line2);
    }
    if (cardDefinition.line2Right) {
        fieldsToRequest.push(cardDefinition.line2Right);
    }
    if (cardDefinition.line3) {
        fieldsToRequest.push(cardDefinition.line3);
    }
    if (cardDefinition.line3Right) {
        fieldsToRequest.push(cardDefinition.line3Right);
    }
    if (cardDefinition.line4) {
        fieldsToRequest.push(cardDefinition.line4);
    }
    if (cardDefinition.line4Right) {
        fieldsToRequest.push(cardDefinition.line4Right);
    }
    if (cardDefinition.line5) {
        fieldsToRequest.push(cardDefinition.line5);
    }
    if (cardDefinition.line5Right) {
        fieldsToRequest.push(cardDefinition.line5Right);
    }
    if (cardDefinition.image) {
        fieldsToRequest.push(cardDefinition.image);
    }

    return {
        // Always query for "_id" for reference fields
        ...getNestedFieldsQuery({
            screenDefinition,
            nestedFields: fieldsToRequest,
            node,
            nodeTypes,
            wrapNodeWithEdges: false,
        }),
        _id: true,
    };
};

const getCalendarFieldProperties = ({
    fieldProperties,
    screenDefinition,
    nodeTypes,
    node,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const calendarProperties = fieldProperties as InternalCalendarProperties;

    const fieldsToRequest: NestedField<Page, NestedFieldTypes>[] = [];
    fieldsToRequest.push(calendarProperties.eventCard.title);

    if (calendarProperties.eventCard.line2) {
        fieldsToRequest.push(calendarProperties.eventCard.line2);
    }
    if (calendarProperties.eventCard.left) {
        fieldsToRequest.push(calendarProperties.eventCard.left);
    }
    if (calendarProperties.eventCard.right) {
        fieldsToRequest.push(calendarProperties.eventCard.right);
    }

    const filter = getFilterAsString(
        [calendarProperties.filter, calendarProperties.activeUserFilter, getCalendarFilters(calendarProperties)],
        screenDefinition,
    );

    const nestedFieldsQuery = getNestedFieldsQuery({
        screenDefinition,
        nestedFields: fieldsToRequest,
        node,
        nodeTypes,
        queryArguments: {
            first: 100,
            ...(filter && { filter }),
        },
    });

    nestedFieldsQuery.query.edges.node = {
        _id: true,
        ...nestedFieldsQuery.query.edges.node,
        ...set({}, convertDeepBindToPathNotNull(calendarProperties.startDateField), true),
    };

    if (calendarProperties.endDateField) {
        nestedFieldsQuery.query.edges.node = {
            ...nestedFieldsQuery.query.edges.node,
            ...set({}, convertDeepBindToPathNotNull(calendarProperties.endDateField), true),
        };
    }

    return nestedFieldsQuery;
};

const getChartFieldProperties = ({
    fieldProperties,
    screenDefinition,
    node,
    nodeTypes,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const chartProperties = fieldProperties as InternalChartProperties<Page>;
    const filter = getFilterAsString([chartProperties.filter, chartProperties.activeUserFilter], screenDefinition);

    const orderByObject = {};
    set(orderByObject, convertDeepBindToPathNotNull(chartProperties.chart.xAxis.properties.bind), 1);
    const orderBy = JSON.stringify(orderByObject);

    return getNestedFieldsQuery({
        screenDefinition,
        nestedFields: [chartProperties.chart.xAxis, ...chartProperties.chart.series],
        node,
        nodeTypes,
        queryArguments: {
            ...(filter && { filter }),
            orderBy,
        },
    });
};

const getStreamFieldProperties = (): QueryProperty => {
    return {
        value: true,
    };
};

const getReferenceFieldProperties = ({
    fieldProperties,
    screenDefinition,
    nodeTypes,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const referenceProperties = fieldProperties as ReferenceProperties;
    const nestedFields: NestedField<Page, NestedFieldTypes>[] = referenceProperties.columns
        ? [...referenceProperties.columns]
        : [];

    const referenceRequestBody = { _id: true };
    // Only request these properties if they are declared
    if (referenceProperties.valueField) {
        set(referenceRequestBody, convertDeepBindToPathNotNull(referenceProperties.valueField), true);
    }

    if (referenceProperties.helperTextField) {
        set(referenceRequestBody, convertDeepBindToPathNotNull(referenceProperties.helperTextField), true);
    }

    if (referenceProperties.imageField) {
        set(referenceRequestBody, convertDeepBindToPathNotNull(referenceProperties.imageField), { value: true });
    }

    if (referenceProperties.tunnelPageIdField) {
        set(referenceRequestBody, convertDeepBindToPathNotNull(referenceProperties.tunnelPageIdField), true);
    }

    return merge(
        referenceRequestBody,
        getNestedFieldsQuery({
            screenDefinition,
            nestedFields,
            wrapNodeWithEdges: false,
            node: referenceProperties.node,
            nodeTypes,
        }),
    );
};

const getPodFieldProperties = ({
    fieldProperties,
    screenDefinition,
    nodeTypes,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const referenceProperties = fieldProperties as VitalPodProperties | PodProperties<ScreenBase>;
    const nestedFields: NestedField<ScreenBase, PodNestedFieldTypes>[] = [...(referenceProperties.columns || [])];
    if (referenceProperties.headerLabel) {
        nestedFields.push(referenceProperties.headerLabel);
    }

    return {
        ...getNestedFieldsQuery({
            screenDefinition,
            nestedFields: nestedFields || [],
            wrapNodeWithEdges: false,
            nodeTypes,
            node: referenceProperties.node,
        }),
        // Always query for "_id" for reference fields
        _id: true,
    };
};

const getCountFieldProperties = ({
    fieldProperties,
    screenDefinition,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const countFieldProperties = fieldProperties as CountProperties<Page>;
    const filter = getFilterAsString([countFieldProperties.filter], screenDefinition);
    return {
        query: {
            ...(filter ? { __args: { filter } } : {}),
            totalCount: true,
        },
    };
};

const getAggregateQueryFromAggregateValuesSelector = (
    selector: AggregateValuesSelector<any>,
    aggregationMethod: AggregationMethod,
): any => {
    if (selector instanceof Object) {
        const firstKey = objectKeys(selector)[0];
        const firstItem = get(selector, firstKey);
        if (firstItem === true) {
            set(selector, firstKey, { [aggregationMethod]: true });
        } else {
            set(selector, firstKey, getAggregateQueryFromAggregateValuesSelector(firstItem, aggregationMethod));
        }
    }
    return selector;
};

const getAggregateFieldProperties = ({
    elementId,
    fieldProperties,
    screenDefinition,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const aggregateFieldProperties = fieldProperties as AggregateProperties<Page>;
    const filter = getFilterAsString([aggregateFieldProperties.filter], screenDefinition);
    if (typeof aggregateFieldProperties.aggregateOn === 'string') {
        return {
            readAggregate: {
                ...(filter ? { __args: { filter } } : {}),
                [aggregateFieldProperties.aggregateOn]: {
                    [aggregateFieldProperties.aggregationMethod]: true,
                },
            },
        };
    }
    if (aggregateFieldProperties.aggregateOn instanceof Object) {
        const aggregateOn = cloneDeep(aggregateFieldProperties.aggregateOn);
        const transformedQuery = getAggregateQueryFromAggregateValuesSelector(
            aggregateOn,
            aggregateFieldProperties.aggregationMethod,
        );
        return {
            readAggregate: {
                ...(filter ? { __args: { filter } } : {}),
                ...transformedQuery,
            },
        };
    }
    // This should never happen.
    throw new Error(
        `Invalid aggregateOn value for ${elementId} field: ${String(aggregateFieldProperties.aggregateOn)}`,
    );
};

const getTableFieldProperties = ({
    fieldProperties,
    screenDefinition,
    nodeTypes,
    userSettings,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const tableProperties = fieldProperties as InternalTableProperties<Page>;
    const optionsMenu = resolveByValue<OptionsMenuItem[]>({
        propertyValue: tableProperties.optionsMenu,
        rowValue: null,
        screenId: screenDefinition.metadata.screenId,
        fieldValue: null,
        skipHexFormat: true,
    });

    const tableUserSettings = userSettings as TableUserSettings;
    const filters = [tableProperties.filter];

    // If there is an option menu defined for the field, we should apply the first element's filter to the query.
    const optionMenuItem = tableUserSettings?.content?.[0]?.optionsMenuItem || optionsMenu?.[0];
    if (optionMenuItem) {
        filters.push(optionMenuItem.graphQLFilter);
    }

    const filter = getFilterAsString(filters, screenDefinition);
    return getCollectionFieldProperties(
        getNestedFieldsFromProperties(tableProperties),
        tableProperties,
        screenDefinition,
        filter,
        true,
        nodeTypes,
        tableProperties.node,
        userSettings,
    );
};

const getTableSummaryFieldProperties = ({
    elementId,
    fieldProperties,
    screenDefinition,
    nodeTypes,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    return getTableFieldProperties({
        elementId,
        screenDefinition,
        fieldProperties: { ...fieldProperties, pageSize: 100 } as TableSummaryProperties,
        nodeTypes,
    });
};

const getPodCollectionFieldProperties = ({
    fieldProperties,
    screenDefinition,
    nodeTypes,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const podProperties = fieldProperties as PodCollectionDecoratorProperties<Page>;
    const filter = getFilterAsString([podProperties.filter], screenDefinition);

    return getCollectionFieldProperties(
        getNestedFieldsFromProperties(podProperties),
        podProperties,
        screenDefinition,
        filter,
        true,
        nodeTypes,
        podProperties.node,
    );
};

const getNestedGridFieldProperties = ({
    fieldProperties,
    screenDefinition,
    nodeTypes,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const nestedGridProperties = fieldProperties as InternalNestedGridProperties<Page>;
    const optionsMenu = resolveByValue<OptionsMenuItem[]>({
        propertyValue: nestedGridProperties.optionsMenu,
        rowValue: null,
        screenId: screenDefinition.metadata.screenId,
        fieldValue: null,
        skipHexFormat: true,
    });

    const filters = [nestedGridProperties.levels[0].filter];
    const orderBy = nestedGridProperties.levels[0].orderBy;

    // If there is an option menu defined for the field, we should apply the first element's filter to the query.
    if (optionsMenu?.[0]) {
        filters.push(optionsMenu[0].graphQLFilter);
    }

    const filter = getFilterAsString(filters, screenDefinition);
    return getCollectionFieldProperties(
        nestedGridProperties.levels[0].columns,
        { ...nestedGridProperties, columns: nestedGridProperties.levels[0].columns, orderBy },
        screenDefinition,
        filter,
        true,
        nodeTypes,
        nestedGridProperties.levels[0].node,
    );
};

const getTreeFieldProperties = ({
    fieldProperties,
    node,
    nodeTypes,
    elementId,
    screenDefinition,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const tableProperties = getTableFieldProperties({ fieldProperties, node, nodeTypes, elementId, screenDefinition });
    /*  const treeProperties = fieldProperties as TreeDecoratorProperties<Page>;

    set(
        tableProperties as any,
        `query.edges.node.${convertDeepBindToPathNotNull(treeProperties.sublevelProperty)}.query.totalCount`,
        true,
    );*/
    return tableProperties;
};

const getFileDepositProperties = (): QueryProperty => {
    return {
        _id: true,
        filename: true,
        mimeType: true,
        lastModified: true,
        contentLength: true,
        status: true,
        downloadUrl: true,
    };
};

const getMultiFileDepositProperties = ({
    fieldProperties,
    screenDefinition,
    nodeTypes,
    node,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    return getCollectionFieldProperties(
        getMultiFileDepositFieldColumns(),
        fieldProperties as MultiFileDepositProperties<any>,
        screenDefinition,
        undefined,
        true,
        nodeTypes,
        node,
    );
};

const getDetailListFieldProperties = ({
    fieldProperties,
    screenDefinition,
    nodeTypes,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const detailListProperties = fieldProperties as DetailListProperties<Page>;
    const filter = getFilterAsString([detailListProperties.filter], screenDefinition);
    return getCollectionFieldProperties(
        detailListProperties.fields,
        detailListProperties,
        screenDefinition,
        filter,
        true,
        nodeTypes,
        detailListProperties.node,
    );
};

const getPluginFieldProperties = ({ fieldProperties }: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const plugins = getStore().getState().plugins;
    const pluginProperties = fieldProperties as PluginProperties;
    const plugin = plugins[pluginProperties.pluginPackage];
    // TODO: Fix here the bind to allow deep references
    return plugin.createFieldQuery ? plugin.createFieldQuery(fieldProperties) : true;
};

const getTechnicalFieldProperties = ({
    fieldProperties,
    screenDefinition,
    nodeTypes,
}: FieldQueryBuilderFunctionArgs): QueryProperty => {
    const fieldProps = fieldProperties as NestedTechnicalProperties;
    if (fieldProps.nestedFields) {
        return getCollectionFieldProperties(
            fieldProps.nestedFields,
            {} as any,
            screenDefinition,
            undefined,
            false,
            nodeTypes,
            fieldProps.node,
        );
    }
    return true;
};

const getCollectionFieldProperties = (
    nestedFields: NestedField<ScreenBase, NestedFieldTypes>[],
    collectionProperties: CollectionPropertiesType,
    screenDefinition: ScreenBaseDefinition,
    filter?: string,
    wrapNodeWithEdges = true,
    nodeTypes?: Dict<FormattedNodeDetails>,
    node?: NodePropertyType,
    userSettings?: UiComponentUserSettings,
): QueryProperty => {
    const viewContent = userSettings?.content?.[0] as TableViewLevel;

    const orderBy = viewContent?.sortOrder
        ? getOrderByFromSortModel(viewContent?.sortOrder || [], nestedFields || [])
        : collectionProperties.orderBy;

    return getNestedFieldsQuery({
        screenDefinition,
        nestedFields,
        queryArguments: {
            first: (collectionProperties as any).pageSize || 20,
            ...(!isEmpty(orderBy)
                ? { orderBy: createOrderByQueryArgument(orderBy) }
                : { orderBy: JSON.stringify({ _id: -1 }) }),
            ...(filter && { filter }),
        },
        wrapNodeWithEdges,
        nodeTypes,
        node,
    });
};
