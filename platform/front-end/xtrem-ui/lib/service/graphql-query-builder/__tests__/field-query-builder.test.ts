import { <PERSON><PERSON><PERSON> } from '@sage/xtrem-shared';
import type { ReferenceProperties } from '../../../component/control-objects';
import type { PageDecoratorProperties } from '../../../component/decorator-properties';
import type { BuildReferenceFieldQueryProps } from '../field-query-builder';
import { buildNestedAggregationQuery, buildReferenceFieldQuery } from '../field-query-builder';

describe('FieldQueryBuilder', () => {
    describe('buildReferenceFieldQuery', () => {
        let commonProps: BuildReferenceFieldQueryProps;

        beforeEach(() => {
            commonProps = {
                elementId: 'elementId',
                screenId: 'screenId',
                fieldProperties: {
                    node: '@sage/xtrem-test/some-node',
                    valueField: 'valueField',
                    bind: 'bind',
                } as ReferenceProperties,
                screenDefinition: {
                    metadata: {
                        screenId: 'screenId',
                        uiComponentProperties: {
                            screenId: {
                                isTransient: false,
                                node: '@sage/xtrem-test/another-node',
                            } as PageDecoratorProperties<any, any>,
                            elementId: {
                                node: '@sage/xtrem-test/some-node',
                            },
                        },
                    },
                } as any,
            };
        });

        it('Should return the correct query using common props', () => {
            const expectedQuery = {
                bind: 'bind',
                fieldNode: '@sage/xtrem-test/some-node',
                node: '@sage/xtrem-test/another-node',
                query: {
                    xtremTest: {
                        anotherNode: {
                            lookups: {
                                __args: {
                                    data: {},
                                },
                                bind: {
                                    __args: {
                                        orderBy: '{"_id":1}',
                                    },
                                    edges: {
                                        cursor: true,
                                        node: {
                                            _id: true,
                                            valueField: true,
                                        },
                                    },
                                    pageInfo: {
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                        endCursor: true,
                                    },
                                },
                            },
                        },
                    },
                },
            };
            expect(buildReferenceFieldQuery(commonProps)).toEqual(expectedQuery);
        });

        it('Should return the correct query with transient fields', () => {
            commonProps.screenDefinition.metadata.uiComponentProperties.screenId.isTransient = true;
            const expectedQuery = {
                bind: null,
                fieldNode: '@sage/xtrem-test/some-node',
                node: '@sage/xtrem-test/some-node',
                query: {
                    xtremTest: {
                        someNode: {
                            query: {
                                __args: {
                                    orderBy: '{"_id":1}',
                                },
                                edges: {
                                    cursor: true,
                                    node: {
                                        _id: true,
                                        valueField: true,
                                    },
                                },
                                pageInfo: {
                                    endCursor: true,
                                    hasNextPage: true,
                                    hasPreviousPage: true,
                                    startCursor: true,
                                },
                            },
                        },
                    },
                },
            };
            expect(buildReferenceFieldQuery(commonProps)).toEqual(expectedQuery);
        });

        it('Should return the correct query with a group', () => {
            commonProps.group = { key: 'products', type: FieldKey.Reference };
            const expectedQuery = {
                bind: null,
                fieldNode: '@sage/xtrem-test/some-node',
                node: '@sage/xtrem-test/some-node',
                query: {
                    xtremTest: {
                        someNode: {
                            queryAggregate: {
                                edges: {
                                    cursor: true,
                                    node: {
                                        group: {
                                            products: true,
                                        },
                                        values: {
                                            _id: {
                                                distinctCount: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            };
            expect(buildReferenceFieldQuery(commonProps)).toEqual(expectedQuery);
        });

        it('Should return the correct query with isFilterLimitedToDataset', () => {
            commonProps.isFilterLimitedToDataset = true;
            commonProps.group = { key: 'bind.valueField', type: FieldKey.Reference };
            const expectedQuery = {
                bind: null,
                fieldNode: '@sage/xtrem-test/some-node',
                node: '@sage/xtrem-test/some-node',
                query: {
                    xtremTest: {
                        someNode: {
                            queryAggregate: {
                                edges: {
                                    cursor: true,
                                    node: {
                                        group: {
                                            bind: {
                                                _id: true,
                                                valueField: true,
                                            },
                                        },
                                        values: {
                                            _id: {
                                                distinctCount: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            };
            expect(buildReferenceFieldQuery(commonProps)).toEqual(expectedQuery);
        });

        it('Should return the correct query with a filter and orderBy', () => {
            commonProps.filter = {
                products: {
                    _or: [
                        {
                            product: {
                                _regex: 'whatever',
                                _options: 'i',
                            },
                        },
                    ],
                },
            };
            commonProps.orderBy = { products: 1 };
            const expectedQuery = {
                bind: 'bind',
                fieldNode: '@sage/xtrem-test/some-node',
                node: '@sage/xtrem-test/another-node',
                query: {
                    xtremTest: {
                        anotherNode: {
                            lookups: {
                                __args: {
                                    data: {},
                                },
                                bind: {
                                    __args: {
                                        filter: '{"products":{"_or":[{"product":{"_regex":"whatever","_options":"i"}}]}}',
                                        orderBy: '{"products":1,"_id":1}',
                                    },
                                    edges: {
                                        cursor: true,
                                        node: {
                                            _id: true,
                                            products: {
                                                _id: true,
                                                product: true,
                                            },
                                            valueField: true,
                                        },
                                    },
                                    pageInfo: {
                                        endCursor: true,
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                    },
                                },
                            },
                        },
                    },
                },
            };
            expect(buildReferenceFieldQuery(commonProps)).toEqual(expectedQuery);
        });

        it('Should return the correct query with a filter and orderBy with transient fields', () => {
            commonProps.screenDefinition.metadata.uiComponentProperties.screenId.isTransient = true;
            commonProps.filter = {
                products: {
                    _or: [
                        {
                            product: {
                                _regex: 'whatever',
                                _options: 'i',
                            },
                        },
                    ],
                },
            };
            commonProps.orderBy = { products: 1 };
            const expectedQuery = {
                bind: null,
                fieldNode: '@sage/xtrem-test/some-node',
                node: '@sage/xtrem-test/some-node',
                query: {
                    xtremTest: {
                        someNode: {
                            query: {
                                __args: {
                                    filter: '{"products":{"_or":[{"product":{"_regex":"whatever","_options":"i"}}]}}',
                                    orderBy: '{"products":1,"_id":1}',
                                },
                                edges: {
                                    cursor: true,
                                    node: {
                                        _id: true,
                                        products: {
                                            _id: true,
                                            product: true,
                                        },
                                        valueField: true,
                                    },
                                },
                                pageInfo: {
                                    endCursor: true,
                                    hasNextPage: true,
                                    hasPreviousPage: true,
                                    startCursor: true,
                                },
                            },
                        },
                    },
                },
            };
            expect(buildReferenceFieldQuery(commonProps)).toEqual(expectedQuery);
        });

        it('Should return the correct query with a filter and orderBy with a group', () => {
            commonProps.group = { key: 'products', type: FieldKey.Reference };
            commonProps.filter = {
                products: {
                    _or: [
                        {
                            product: {
                                _regex: 'whatever',
                                _options: 'i',
                            },
                        },
                    ],
                },
            };
            commonProps.orderBy = { products: 1 };
            const expectedQuery = {
                bind: null,
                fieldNode: '@sage/xtrem-test/some-node',
                node: '@sage/xtrem-test/some-node',
                query: {
                    xtremTest: {
                        someNode: {
                            queryAggregate: {
                                __args: {
                                    filter: '{"products":{"_or":[{"product":{"_regex":"whatever","_options":"i"}}]}}',
                                    orderBy: '{"products":1}',
                                },
                                edges: {
                                    cursor: true,
                                    node: {
                                        group: {
                                            products: true,
                                        },
                                        values: {
                                            _id: {
                                                distinctCount: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            };
            expect(buildReferenceFieldQuery(commonProps)).toEqual(expectedQuery);
        });
    });

    describe('buildNestedAggregationQuery', () => {
        const props = {
            group: { key: 'products.products', type: FieldKey.Reference },
            rootNodeId: '49',
            elementId: 'elementId',
        };

        it('Should return the correct query', () => {
            const expected = {
                query: {
                    __args: {
                        filter: '{"_id":{"_eq":"49"}}',
                    },
                    edges: {
                        cursor: true,
                        node: {
                            _id: true,
                            elementId: {
                                queryAggregate: {
                                    edges: {
                                        cursor: true,
                                        node: {
                                            group: {
                                                products: {
                                                    products: true,
                                                    _id: true,
                                                },
                                            },
                                            values: {
                                                products: {
                                                    _id: {
                                                        distinctCount: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            };
            expect(buildNestedAggregationQuery(props)).toEqual(expected);
        });
    });
});
