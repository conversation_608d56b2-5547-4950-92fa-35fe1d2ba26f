import type {
    ClientNode,
    Filter,
    FilterOperatorComparable,
    FilterOperatorEquality,
    FilterOperatorInclusion,
    Logical,
    Quantifiers,
} from '@sage/xtrem-client';
import type { DateValue } from '@sage/xtrem-date-time';
import { isValidIsoDate, isValidLocalizedDate, parseIsoDate, parseLocalizedDate } from '@sage/xtrem-date-time';
import type { Dict, LocalizeLocale } from '@sage/xtrem-shared';
import { deepMerge, flat, objectKeys } from '@sage/xtrem-shared';
import { EnumType } from 'json-to-graphql-query';
import {
    camelCase,
    get,
    has,
    isEmpty,
    isPlainObject,
    isUndefined,
    mapValues,
    merge,
    omit,
    reduceRight,
    remove,
    set,
    uniq,
    without,
} from 'lodash';
import type { PageProperties } from '../../component/container/container-properties';
import type {
    CollectionValueFieldProperties,
    InternalNestedGridProperties,
    MultiReferenceProperties,
    ReferenceProperties,
    TableProperties,
} from '../../component/control-objects';
import type { PodCollectionDecoratorProperties } from '../../component/field/pod-collection/pod-collection-types';
import type { PropertyValueType } from '../../component/field/reference/reference-types';
import { getReferencePath, getReferenceValueFieldPath } from '../../component/field/reference/reference-utils';
import type { InternalTableProperties } from '../../component/field/table/table-component-types';
import {
    withoutNestedTechnical,
    type NestedField,
    type NestedFieldTypes,
    type NestedFieldTypesWithoutTechnical,
} from '../../component/nested-fields';
import type { NestedReferenceProperties } from '../../component/nested-fields-properties';
import type {
    ReadonlyFieldControlObject,
    ReadonlyFieldProperties,
} from '../../component/readonly-field-control-object';
import type { ComponentKey, OrderByType } from '../../component/types';
import { FieldKey } from '../../component/types';
import type { CardDefinition } from '../../component/ui/card/card-component';
import type { UiComponentUserSettings } from '../../redux/state';
import type { NodePropertyType } from '../../types';
import { GraphQLKind, GraphQLTypes } from '../../types';
import { filterFields, getNestedFieldElementId } from '../../utils/abstract-fields-utils';
import { deepFindPropertyValues } from '../../utils/common-util';
import { xtremConsole } from '../../utils/console';
import { HEADER_IMAGE, HEADER_TITLE, NEW_PAGE } from '../../utils/constants';
import {
    convertDeepBindToPath,
    convertDeepBindToPathNotNull,
    getNestedFieldsFromProperties,
    getTopLevelBindFromNestedBind,
} from '../../utils/nested-field-utils';
import { findDeepPropertyDetails, findDeepPropertyType } from '../../utils/node-utils';
import { parseScreenValues, pascalCase, schemaTypeNameFromNodeName } from '../../utils/transformers';
import { AllAndOnly, reduceDict } from '../../utils/type-utils';
import { isDevMode } from '../../utils/window';
import { CollectionValue } from '../collection-data-service';
import { CollectionFieldTypes, type InternalValue } from '../collection-data-types';
import { mergeGraphQLFilters } from '../filter-service';
import type {
    CollectionQuery,
    ExecutableQuery,
    GraphQLFilter,
    GraphQLFilterValueType,
    NodeCollection,
    NonArrayGraphQLFilter,
    QueryArguments,
    QueryBuilderOptions,
    QueryEnumWrapper,
    QueryFilterOperator,
    QueryProperty,
    QueryWrapper,
    TotalCountQuery,
} from '../graphql-utils';
import { escapeStringRegexp, wrapQuery } from '../graphql-utils';
import type { FormattedNodeDetails } from '../metadata-types';
import type { Page } from '../page';
import type { PageDefinition } from '../page-definition';
import type { PageMetadata } from '../page-metadata';
import type { ScreenBase } from '../screen-base';
import type { ScreenBaseDefinition } from '../screen-base-definition';
import { getScreenElement } from '../screen-base-definition';
import { formatInputProperty } from '../value-serializer-service';
import { getFieldProperties } from './field-query-builder';

export const RESTRICTED_COLUMN_PREFIX = '__restricted_';
export const RESTRICTED_COLUMN_ALL_PREFIX = '__all_';
export type QuantifierType = keyof Quantifiers;
export const quantifiers: QuantifierType[] = AllAndOnly<QuantifierType>()(['_atLeast', '_atMost', '_every', '_none']);

export * from './field-query-builder';

export type ExtendedTitleProps = Array<{
    [key: string]: {
        __aliasFor: string;
        [key: string]: boolean | string;
    };
}>;

export type FilterReservedWord =
    | keyof FilterOperatorComparable<any>
    | keyof FilterOperatorEquality<any>
    | keyof FilterOperatorInclusion<any>
    | keyof Logical<any>
    | keyof Quantifiers
    | keyof QueryFilterOperator;

export const filterReservedWords = AllAndOnly<FilterReservedWord>()([
    '_and',
    '_or',
    '_not',
    '_eq',
    '_ne',
    '_in',
    '_nin',
    '_contains',
    '_containsRange',
    '_containedBy',
    'start',
    'end',
    '_gt',
    '_gte',
    '_lt',
    '_lte',
    '_atLeast',
    '_atMost',
    '_every',
    '_none',
    '_regex',
    '_options',
    '_mod',
]);

const filterReducer = reduceDict<Filter<any>, CollectionQuery['edges']['node']>();

const DATA_TYPE_ENUM_VALUES_FRAGMENT = {
    value: true,
    title: true,
};

const DATA_TYPE_FIELD_FRAGMENT = {
    bind: true,
    title: true,
    type: true,
};

export const DATA_TYPE_FRAGMENT = {
    name: true,
    type: true,
    title: true,
    node: true,
    precision: true,
    scale: true,
    maxLength: true,
    value: DATA_TYPE_FIELD_FRAGMENT,
    helperText: DATA_TYPE_FIELD_FRAGMENT,
    imageField: DATA_TYPE_FIELD_FRAGMENT,
    columns: DATA_TYPE_FIELD_FRAGMENT,
    values: DATA_TYPE_ENUM_VALUES_FRAGMENT,
    tunnelPage: true,
    tunnelPageId: DATA_TYPE_FIELD_FRAGMENT,
};

export function getNodeDetailsPropertiesFragment(): any {
    return {
        name: true,
        title: true,
        canSort: true,
        canFilter: true,
        type: true,
        isCustom: true,
        isMutable: true,
        isStored: true,
        isOnInputType: true,
        isOnOutputType: true,
        enumType: true,
        dataType: true,
        dataTypeDetails: DATA_TYPE_FRAGMENT,
        targetNode: true,
    };
}

export function getNodeDetailsFragment(): any {
    return {
        packageName: true,
        name: true,
        title: true,
        defaultDataType: true,
        defaultDataTypeDetails: DATA_TYPE_FRAGMENT,
        hasAttachments: true,
        hasNotes: true,
        properties: getNodeDetailsPropertiesFragment(),
        mutations: {
            name: true,
            title: true,
            parameters: {
                name: true,
                title: true,
            },
        },
    };
}

export function getNodeCustomDetailsFragment(): any {
    return {
        name: true,
        properties: getNodeDetailsPropertiesFragment(),
    };
}

export const getPlatformStringLiteralsSchemaFragment = (): ExecutableQuery => ({
    __args: {
        filter: {
            packageOrPage: '@sage/xtrem-ui,@sage/xtrem-date-time,@sage/xtrem-ui-components,@sage/xtrem-document-editor',
        },
    },
    key: true,
    content: true,
});

export function getBindAndSelectorFromDeepBinding(
    bind: string,
    contextNode: string,
    nodeTypes: Dict<FormattedNodeDetails>,
    alias?: string,
): { valuePath: string; jsonSelector?: string } {
    const remapAlias = (s: string[]): string => {
        if (alias) {
            const copy = [...s];
            copy[0] = alias;
            return copy.join('.');
        }
        return s.join('.');
    };

    const valuePath = convertDeepBindToPathNotNull(bind);
    const segments = valuePath.split('.');

    const node = schemaTypeNameFromNodeName(contextNode);
    for (let i = 0; i < segments.length; i += 1) {
        const bindSegments = segments.slice(0, i + 1);
        const type = findDeepPropertyDetails(node, bindSegments.join('.'), nodeTypes);
        if (type?.type === GraphQLTypes.Json) {
            const jsonSelector = segments.slice(i + 1).join('.');

            return {
                valuePath: remapAlias(bindSegments),
                jsonSelector,
            };
        }
    }

    return {
        valuePath: remapAlias(segments),
    };
}

export const addTotalCountTransform = (filter: Filter<any>, initial: CollectionQuery['edges']['node']): Filter<any> =>
    filterReducer(filter, initial, (_value, acc, key, path, source): any => {
        if ((quantifiers as string[]).includes(key as string)) {
            const parentKey = path[path.length - 2] as string;
            const parentPath = path.slice(0, -1);
            const parentValue = get(source, parentPath);
            const fieldName = pascalCase(parentKey);
            acc[`${RESTRICTED_COLUMN_ALL_PREFIX}${fieldName}`] = {
                __aliasFor: parentKey,
                query: {
                    totalCount: true,
                },
            };
            acc[`${RESTRICTED_COLUMN_PREFIX}${fieldName}`] = {
                __aliasFor: parentKey,
                query: {
                    totalCount: true,
                    __args: { filter: JSON.stringify(omit(parentValue, [key])) },
                },
            };
            delete acc[parentKey];
        }
        return acc;
    });

export const addReferenceIdsAssign = (
    filter: Filter<any>,
    node?: NodePropertyType,
    nodeTypes?: Dict<FormattedNodeDetails>,
): Filter<any> =>
    filterReducer(filter, {}, (_value, acc, _key, path) => {
        const isReserved = (k: string): boolean => filterReservedWords.includes(k as any);
        const cleanPath = remove([...path], k => typeof k !== 'number' && !isReserved(k));
        const targetType = findDeepPropertyDetails(
            schemaTypeNameFromNodeName(node),
            cleanPath.join('.'),
            nodeTypes,
            true,
        );
        const isNodeProperty = cleanPath.length === 1;
        const isNodeReference = cleanPath.length >= 2;
        const isNode = (isNodeProperty || isNodeReference) && targetType?.type !== GraphQLTypes.Json;

        if (isNodeProperty && targetType?.kind === GraphQLKind.Object) {
            return merge(acc, { [cleanPath[0]]: { _id: true } });
        }

        if (isNode) {
            return merge(
                acc,
                reduceRight(
                    cleanPath,
                    (acc2, k) => (isEmpty(acc2) ? { [k]: true } : { [k]: { ...acc2, _id: true } }),
                    {},
                ),
            );
        }
        return acc;
    });

export const parseFilterEnumFields = (source: NonArrayGraphQLFilter): GraphQLFilter => {
    let result = source;

    if (isPlainObject(source)) {
        result = mapValues(source, (x: GraphQLFilterValueType) =>
            Array.isArray(x) ? x.map(parseFilterEnumFields) : parseFilterEnumFields(x),
        ) as GraphQLFilter;

        if (result._isEnum) {
            // TODO Should we throw an exception if an operator other than _eq is used or if no _eq is provided?
            result._eq = new EnumType(result._eq as string) as any;
            delete result._isEnum;
        }
    }

    return result;
};

export const convertFilterDecoratorToGraphQLFilter = (
    screenDefinition: ScreenBaseDefinition,
    filter?: GraphQLFilter | ((this: Page) => GraphQLFilter),
    recordContext?: Dict<any>,
): GraphQLFilter | undefined => {
    if (filter) {
        if (typeof filter === 'function') {
            const screenBase = getScreenElement(screenDefinition);
            return parseFilterEnumFields(filter.apply(screenBase, [recordContext]));
        }
        return parseFilterEnumFields(filter);
    }
    return undefined;
};

const buildNodeQueryFromProperties = (
    properties: QueryProperty[],
    startFrom: any = {},
    isRequestingDefaults = false,
): any => {
    let node = { ...startFrom };
    properties.forEach(prop => {
        if (typeof prop === 'string') {
            if (isRequestingDefaults && prop === '_sortValue') {
                return;
            }
            node[prop] = true;
        }
        if (typeof prop === 'object') {
            const merged = merge(prop, node);
            node = isRequestingDefaults ? sortValueCleaner(merged) : merged;
        }
    });

    return node;
};

const sortValueCleaner = (input: QueryProperty[]): any => {
    if (!input || typeof input !== 'object') {
        return input;
    }
    if (Array.isArray(input)) {
        return input
            .filter(e => e !== '_sortValue')
            .map((e: any) => {
                return sortValueCleaner(e);
            });
    }
    return objectKeys(input).reduce((r, k) => {
        if (k === '_sortValue') {
            return r;
        }
        r[k] = sortValueCleaner(input[k]);
        return r;
    }, {} as any);
};

const buildCollectionNodeWithQuery = (
    options: QueryBuilderOptions,
    wrapWithEdges = true,
    node?: NodePropertyType,
    nodeTypes?: Dict<FormattedNodeDetails>,
): NodeCollection => {
    if (!wrapWithEdges) {
        let singleNode: any = { _id: true };

        if (options.properties) {
            options.properties.forEach(prop => {
                if (typeof prop === 'string') {
                    singleNode[prop] = true;
                }
                if (typeof prop === 'object') {
                    singleNode = deepMerge(prop, singleNode);
                }
            });
        }

        return singleNode;
    }

    const collection: NodeCollection = {
        query: {
            edges: {
                node: {
                    _id: true,
                },
                cursor: true,
            },
            // If we would have an empty object, the library puts empty parentheses and breaks the query
            __args:
                options.queryArguments && objectKeys(options.queryArguments).length > 0
                    ? options.queryArguments
                    : undefined,
        },
    };

    if (!options.noPageInfo) {
        collection.query.pageInfo = {
            startCursor: true,
            endCursor: true,
            hasPreviousPage: true,
            hasNextPage: true,
        };
    }

    if (options.properties) {
        collection.query.edges.node = buildNodeQueryFromProperties(options.properties, collection.query.edges.node);
    }

    if (options.queryArguments?.filter) {
        const parsedFilter: Filter<any> = JSON.parse(options.queryArguments.filter);
        collection.query.edges.node = merge(
            collection.query.edges.node,
            addReferenceIdsAssign(parsedFilter, node, nodeTypes),
        );
        collection.query.edges.node = addTotalCountTransform(parsedFilter, collection.query.edges.node);
    }
    return collection;
};

export const queryBuilder = (node: NodePropertyType, options: QueryBuilderOptions): NodeCollection => {
    const query = buildCollectionNodeWithQuery(options);
    if (options.alias) {
        query.__aliasFor = camelCase(String(node).split('/').pop());
    }
    return query;
};

export const getUsedEnums = (pageMetadata: PageMetadata, knownEnumDefinitions: string[]): string[] => {
    const enums = deepFindPropertyValues<string>('optionType', pageMetadata.uiComponentProperties).map<string>(
        schemaTypeNameFromNodeName,
    );
    return uniq(without(enums, ...knownEnumDefinitions)).filter(v => !!v);
};

export const buildEnumQuery = (enumName: string): QueryEnumWrapper => {
    const typeName = schemaTypeNameFromNodeName(enumName);
    return {
        [typeName]: {
            __aliasFor: '__type',
            __args: { name: typeName },
            enumValues: {
                name: true,
            },
        },
    };
};

export const getUsedNodes = (
    pageMetadata: PageMetadata,
    knownTypeDefinitions?: string[],
    rootNode?: string,
): string[] => {
    const nodes = deepFindPropertyValues<string>('node', pageMetadata.uiComponentProperties).filter(
        node => typeof node === 'string',
    );

    if (rootNode) {
        nodes.push(rootNode);
    }

    if (knownTypeDefinitions) {
        return uniq(without(nodes, ...knownTypeDefinitions)).filter(v => !!v);
    }

    return nodes;
};

export const buildTypeQuery = (nodeNames: string[], knownTypes: string[], depth = 3): QueryEnumWrapper => {
    const missingNodeNames = nodeNames.map(schemaTypeNameFromNodeName);
    const knownNodeNames = knownTypes.map(schemaTypeNameFromNodeName);

    return {
        getNodeDetailsList: {
            __args: { missingNodeNames, knownNodeNames, depth },
            ...getNodeDetailsFragment(),
        },
        getNodeCustomDetailsList: {
            __args: { missingNodeNames, knownNodeNames, depth },
            ...getNodeCustomDetailsFragment(),
        },
    };
};

export const getRequestableFieldIds = (
    fieldControlObjects: Dict<ReadonlyFieldControlObject<any, any, any>>,
    screenDefinition: ScreenBaseDefinition,
): string[] =>
    objectKeys(fieldControlObjects).filter(f => {
        return (
            !screenDefinition.metadata.uiComponentProperties?.[f]?.isTransient &&
            !(screenDefinition.metadata.uiComponentProperties?.[f] as ReadonlyFieldProperties)?.isTransientInput
        );
    });

export const buildEnumQueries = (enums: string[]): QueryEnumWrapper =>
    enums.map(buildEnumQuery).reduce((optionsQuery, nextQuery) => ({ ...optionsQuery, ...nextQuery }), {});

export const buildTableTotalCountQuery = ({ filter }: { filter: string }): TotalCountQuery => {
    return {
        query: {
            __args: { filter },
            totalCount: true,
        },
    };
};

export const buildFieldQuery = (
    rootNode: NodePropertyType,
    fieldControlObject: ReadonlyFieldControlObject<any, any, any>,
    screenDefinition: ScreenBaseDefinition,
    fieldProperties: ReadonlyFieldProperties,
    elementId: string,
    recordId: string,
    nodeTypes: Dict<FormattedNodeDetails>,
    userSettings?: UiComponentUserSettings,
): NodeCollection => {
    const queryProps = getFieldProperties(
        elementId,
        fieldControlObject.componentType,
        fieldProperties,
        screenDefinition,
        fieldProperties.bind,
        nodeTypes,
        rootNode,
        userSettings,
    );
    const filter = JSON.stringify({ _id: { _eq: recordId } });
    const options: QueryBuilderOptions = { queryArguments: { filter }, properties: [queryProps] };
    return queryBuilder(rootNode, options);
};

export const buildRootNodeQuery = (
    rootNode: NodePropertyType,
    fieldControlObjects: Dict<ReadonlyFieldControlObject<any, any, any>>,
    screenDefinition: ScreenBaseDefinition,
    _id: string,
    alias?: string,
    nodeTypes?: Dict<FormattedNodeDetails>,
    isDuplicate = false,
    skipHeaderFields?: boolean,
): QueryWrapper<any> => {
    const fieldIds = getRequestableFieldIds(fieldControlObjects, screenDefinition);
    const screenId = screenDefinition.metadata.screenId;
    const uiComponentProps = screenDefinition.metadata.uiComponentProperties[screenId] as PageProperties<any>;

    let image: string | undefined;
    let title: string | undefined;

    if (uiComponentProps && uiComponentProps.navigationPanel) {
        if (uiComponentProps.navigationPanel.listItem.image) {
            image = getBindForQuery(uiComponentProps.navigationPanel.listItem.image);
        }
        title = getBindForQuery(uiComponentProps.navigationPanel.listItem.title);
    }

    const queryProps = getFieldsProperties(fieldIds, fieldControlObjects, screenDefinition, nodeTypes, rootNode);
    let extendedTitleProps: ExtendedTitleProps = [];
    let extendedImageProps: ExtendedTitleProps = [];

    if (title && !skipHeaderFields) {
        extendedTitleProps = title.includes('.')
            ? buildAliasObject(title, HEADER_TITLE)
            : [{ [HEADER_TITLE]: { __aliasFor: title } }];
    }

    if (image && !skipHeaderFields) {
        extendedImageProps = image.includes('.')
            ? buildAliasObject(image, HEADER_IMAGE)
            : [{ [HEADER_IMAGE]: { __aliasFor: image, value: true } }];
    }

    const extendedQueryProps = [...queryProps, ...extendedImageProps, ...extendedTitleProps];

    return wrapQuery(
        rootNode,
        {
            [isDuplicate ? 'getDuplicate' : 'read']: {
                __args: { _id: String(_id) },
                ...buildNodeQueryFromProperties([...extendedQueryProps, '_etag', '_id']),
            },
        },
        alias,
    );
};

const buildAliasObject = (value: string, key: string): any => {
    const object = [...value.split('.')].reverse().reduce((prevFilter: any, currentKey: string, index: number) => {
        if (key === HEADER_IMAGE && index === 0) {
            return { [currentKey]: { value: true } };
        }
        return { [currentKey]: index === 0 ? true : prevFilter };
    }, {});

    object[value.split('.')[0]].__aliasFor = value.split('.')[0];

    const currentObjectKey = objectKeys(object)[0];

    object[key] = object[currentObjectKey];
    delete object[currentObjectKey];

    return [object];
};

const getBindForQuery = (field: NestedField<any, NestedFieldTypesWithoutTechnical, any, any>): string => {
    return field.type === FieldKey.Reference
        ? `${convertDeepBindToPathNotNull(field.properties.bind as PropertyValueType)}.${getReferencePath<any>(
              (field.properties as NestedReferenceProperties<any, any>).valueField,
          )}`
        : convertDeepBindToPathNotNull(field?.properties.bind as PropertyValueType);
};

const getFieldsProperties = (
    fieldIds: string[],
    fieldControlObjects: Dict<ReadonlyFieldControlObject<any, any, any>>,
    screenDefinition: ScreenBaseDefinition,
    nodeTypes?: Dict<FormattedNodeDetails>,
    node?: NodePropertyType,
): QueryProperty[] => {
    return fieldIds.reduce((props, fieldId) => {
        const fieldType = fieldControlObjects[fieldId].componentType;
        const fieldProperties: ReadonlyFieldProperties = screenDefinition.metadata.uiComponentProperties[fieldId];
        const userSettings = screenDefinition.userSettings[fieldId]?.$current;
        return [
            ...props,
            getFieldProperties(
                fieldId,
                fieldType,
                fieldProperties,
                screenDefinition,
                fieldProperties.bind,
                nodeTypes,
                node,
                userSettings,
            ),
        ];
    }, [] as QueryProperty[]);
};

export const getFilterAsString = (
    filters: (GraphQLFilter | (() => GraphQLFilter) | undefined)[],
    screenDefinition: ScreenBaseDefinition,
    recordContext?: Dict<any>,
): string | undefined => {
    const graphQLFilters = mergeFilters(filters, screenDefinition, recordContext);

    return !isEmpty(graphQLFilters) ? JSON.stringify(graphQLFilters) : undefined;
};

export const mergeFilters = (
    filters: (GraphQLFilter | (() => GraphQLFilter) | undefined)[],
    screenDefinition: ScreenBaseDefinition,
    recordContext?: Dict<any>,
): GraphQLFilter => {
    const graphQLFilters = filters.reduce((currentGraphQLFilters, filter) => {
        const graphQLFilter = convertFilterDecoratorToGraphQLFilter(screenDefinition, filter, recordContext);

        if (graphQLFilter) {
            currentGraphQLFilters.push(graphQLFilter);
        }

        return currentGraphQLFilters;
    }, [] as GraphQLFilter[]);

    if (!isEmpty(graphQLFilters)) {
        return graphQLFilters.length === 1 ? graphQLFilters[0] : mergeGraphQLFilters(graphQLFilters);
    }
    return {};
};

export const applyDeepNestedKey = (
    elementId: string,
    fieldDetails: any,
    addAlias = false,
    bind?: PropertyValueType,
    parentNode?: NodePropertyType,
    nodeTypes?: Dict<FormattedNodeDetails>,
): any => {
    let pathComponents = convertDeepBindToPathNotNull(bind || elementId).split('.');
    let valueToSet = fieldDetails;
    let pathParent = schemaTypeNameFromNodeName(parentNode);
    for (let i = 0; i < pathComponents.length; i += 1) {
        const propertyType = findDeepPropertyDetails(pathParent, pathComponents[i], nodeTypes);
        if (propertyType?.type === GraphQLTypes.Json) {
            /**
             * We cannot deep query JSON columns, we need to fetch the JSON column, then
             * parse it and then finally return the property value we really need.
             *  */
            pathComponents = [...pathComponents].slice(0, i + 1);
            // In case of a JSON column, we need to fetch the whole column and then parse it
            valueToSet = true;
            break;
        }

        pathParent = propertyType?.type;
    }

    const object = [...pathComponents].reverse().reduce((prevFilter: any, currentKey: string) => {
        return { [currentKey]: prevFilter };
    }, valueToSet);

    if (!addAlias) {
        return object;
    }

    // On primitive types we just return an alias object
    if (typeof object[pathComponents[0]] !== 'object') {
        if (elementId === pathComponents[0]) {
            return object;
        }

        return {
            [elementId]: { __aliasFor: pathComponents[0] },
        };
    }

    // Swap the alias with the object key
    const currentObjectKey = objectKeys(object)[0];

    if (currentObjectKey !== elementId) {
        object[pathComponents[0]].__aliasFor = pathComponents[0];
        object[elementId] = object[currentObjectKey];
        delete object[currentObjectKey];
    }

    return object;
};

/**
 * Inspects the filter object, and prepare it for server side consumption.
 * It remaps id field names to match current server inconsistencies and creates nested sorting conditions for reference
 * fields.
 * @param orderBy
 */
export const createOrderByQueryArgument = (orderBy: OrderByType): string => {
    const remappedOrderBy: Dict<-1 | 1 | Dict<-1 | 1>> = {};

    objectKeys(orderBy).forEach(fieldKey => {
        const filterValue = orderBy[fieldKey];
        const keyToSet = fieldKey.split('__').join('.'); // Split by the nested bind separator
        set(remappedOrderBy, keyToSet, filterValue);
    });
    return JSON.stringify(remappedOrderBy);
};

export const getAggregationValues = (aggregations: object): any =>
    Object.entries(flat(aggregations)).reduce<any>((acc, [key, value]) => {
        set(acc, `${key}.${value}`, true);
        return acc;
    }, {});

export const nestedFieldsToQueryProperties = ({
    screenDefinition,
    nestedFields,
    node,
    nodeTypes,
}: {
    screenDefinition: ScreenBaseDefinition;
    nestedFields: NestedField<Page, NestedFieldTypes>[];
    nodeTypes?: Dict<FormattedNodeDetails>;
    node?: NodePropertyType;
}): QueryProperty[] =>
    nestedFields
        ?.filter(nestedField => !nestedField.properties.isTransient && !nestedField.properties.isTransientInput)
        .map(nestedField => {
            return getFieldProperties(
                getTopLevelBindFromNestedBind(nestedField.properties.bind),
                nestedField.type,
                nestedField.properties,
                screenDefinition,
                nestedField.properties.bind,
                nodeTypes,
                node,
            );
        });

export const getNestedFieldsQuery = ({
    screenDefinition,
    nestedFields,
    queryArguments,
    alias,
    wrapNodeWithEdges = true,
    node,
    nodeTypes,
}: {
    screenDefinition: ScreenBaseDefinition;
    nestedFields: NestedField<Page, NestedFieldTypes>[];
    nodeTypes?: Dict<FormattedNodeDetails>;
    node?: NodePropertyType;
    queryArguments?: QueryArguments;
    alias?: string;
    wrapNodeWithEdges?: boolean;
}): NodeCollection => {
    const properties = nestedFieldsToQueryProperties({ screenDefinition, nestedFields, node, nodeTypes });
    return buildCollectionNodeWithQuery({ alias, properties, queryArguments }, wrapNodeWithEdges, node, nodeTypes);
};

// This function avoids adding undefined properties to the GraphQL queries that would result in errors
export const parseQueryArguments = (inputArguments: QueryArguments): QueryArguments => {
    const outputArguments: QueryArguments = {};

    if (inputArguments.after) {
        outputArguments.after = inputArguments.after;
    }

    if (inputArguments.before) {
        outputArguments.before = inputArguments.before;
    }

    if (inputArguments.filter) {
        outputArguments.filter = inputArguments.filter;
    }

    if (inputArguments.first) {
        outputArguments.first = inputArguments.first;
    }

    if (inputArguments.last) {
        outputArguments.last = inputArguments.last;
    }

    if (inputArguments.orderBy) {
        outputArguments.orderBy = inputArguments.orderBy;
    }

    return outputArguments;
};

export const getDirtyNestedValues = ({
    screenDefinition,
    elementId,
    recordId,
    level = 0,
}: {
    screenDefinition: ScreenBaseDefinition;
    elementId: string;
    recordId?: string;
    level?: number;
}): Dict<any> => {
    if (!recordId) {
        return {};
    }

    const value = screenDefinition.values[elementId];
    if (!value || value instanceof CollectionValue === false) {
        return {};
    }
    const actualLevel = level === 0 ? undefined : level;
    const row = (screenDefinition.values[elementId] as CollectionValue).getRawRecord({
        id: recordId,
        cleanMetadata: false,
        level: actualLevel,
    }) as Partial<InternalValue<ClientNode>>;

    if (!row) {
        return {};
    }

    return Array.from(row.__dirtyColumns || []).reduce((acc, dirtyColumn) => {
        return set(acc, dirtyColumn, get(row, dirtyColumn));
    }, {});
};

export const buildRowDefaultQuery = ({
    screenDefinition,
    elementId,
    isNewRow = true,
    alias,
    recordId,
    data,
    nodeTypes,
}: {
    screenDefinition: ScreenBaseDefinition;
    elementId: string;
    isNewRow?: boolean;
    alias?: string;
    recordId?: string;
    data?: any;
    nodeTypes: Dict<FormattedNodeDetails>;
}): QueryWrapper<any> => {
    if (!isNewRow && recordId === undefined) {
        throw new Error('A row id is needed in order to fetch default values for an existing row.');
    }
    const fieldProperties = screenDefinition.metadata.uiComponentProperties[elementId] as
        | InternalTableProperties<Page>
        | InternalNestedGridProperties<Page>
        | PodCollectionDecoratorProperties<Page>;

    const inputData = data || getDirtyNestedValues({ screenDefinition, elementId, recordId });
    const queryData = formatInputProperty({
        property: { type: schemaTypeNameFromNodeName(fieldProperties.node || ''), kind: GraphQLKind.Object },
        value: inputData,
        nodeTypes,
        isTopLevel: true,
    });
    let additionalData;
    if (!queryData) {
        additionalData = { [fieldProperties.bind ? String(fieldProperties.bind) : elementId]: [{}] };
    } else {
        additionalData = { [fieldProperties.bind ? String(fieldProperties.bind) : elementId]: [queryData] };
    }
    return buildDefaultsQuery({
        screenDefinition,
        alias,
        requestedFieldIds: [elementId],
        additionalData,
        sendNonDirtyFields: true,
    });
};

export const buildNestedDefaultsQuery = ({
    screenDefinition,
    elementId,
    isNewRow = true,
    alias,
    recordId,
    level = 0,
    data,
    nodeTypes,
}: {
    screenDefinition: ScreenBaseDefinition;
    elementId: string;
    isNewRow?: boolean;
    alias?: string;
    recordId?: string;
    level?: number;
    data?: any;
    nodeTypes: Dict<FormattedNodeDetails>;
}): QueryWrapper<any> => {
    if (!isNewRow && recordId === undefined) {
        throw new Error('A row id is needed in order to fetch default values for an existing row.');
    }
    const fieldProperties = screenDefinition.metadata.uiComponentProperties[elementId] as
        | InternalTableProperties<Page>
        | InternalNestedGridProperties<Page>
        | PodCollectionDecoratorProperties<Page>;

    const isNestedGrid = Object.prototype.hasOwnProperty.call(fieldProperties, 'levels');

    const inputData = data || getDirtyNestedValues({ screenDefinition, elementId, recordId, level });
    const columnDefinitions: NestedField<Page, NestedFieldTypes>[] = isNestedGrid
        ? (fieldProperties as InternalNestedGridProperties<Page>).levels[level].columns
        : getNestedFieldsFromProperties(fieldProperties as TableProperties | PodCollectionDecoratorProperties);
    const node = String(
        isNestedGrid
            ? (fieldProperties as InternalNestedGridProperties<Page>).levels[level].node
            : fieldProperties.node,
    );
    let columns: NestedField<Page, NestedFieldTypes>[] = [];

    if (!isUndefined(recordId) || !isNewRow || data) {
        const dirtyKeys = objectKeys(inputData).map(key => key.split('__')[0]);
        columns = columnDefinitions.filter(
            column => dirtyKeys.indexOf(getNestedFieldElementId(column).split('__')[0]) === -1,
        );
    } else {
        columns = columnDefinitions;
    }
    columns = columns.filter(f => f.properties.bind !== '_sortValue');

    if (isEmpty(columns)) {
        return {};
    }

    const queryProps = [
        {
            ...getNestedFieldsQuery({
                screenDefinition,
                nestedFields: columns,
                wrapNodeWithEdges: false,
                node,
                nodeTypes,
            }),
        },
    ];
    const fieldsToRequest = buildNodeQueryFromProperties(queryProps);

    // We don't request the fake ID that the defaults service returns
    delete fieldsToRequest._id;

    const queryData = formatInputProperty({
        property: { type: schemaTypeNameFromNodeName(node), kind: GraphQLKind.Object },
        value: inputData,
        nodeTypes,
        isTopLevel: true,
    });
    return wrapQuery(
        node,
        {
            getDefaults: {
                ...((!isNewRow || data) && {
                    __args: {
                        data: queryData,
                    },
                }),
                ...fieldsToRequest,
            },
        },
        alias,
    );
};

/**
 * Builds a query to request default values from the server.
 * If a list of fields is not provided as the last argument, the function checks the screen definition and requests values for the non-dirty fields.
 */
export const buildDefaultsQuery = ({
    screenDefinition,
    alias,
    requestedFieldIds,
    clean = false,
    additionalData,
    sendNonDirtyFields = false,
    node,
    nodeTypes,
    values,
}: {
    screenDefinition: ScreenBaseDefinition;
    alias?: string;
    requestedFieldIds?: string[];
    clean?: boolean;
    additionalData?: Dict<any>;
    sendNonDirtyFields?: boolean;
    nodeTypes?: Dict<FormattedNodeDetails>;
    node?: NodePropertyType;
    values?: Dict<any>;
}): QueryWrapper<any> => {
    const screenId = screenDefinition.metadata.screenId;
    const controlObjects = filterFields(screenDefinition.metadata.controlObjects);
    const isNewPage = Boolean((screenDefinition as PageDefinition).queryParameters?._id === NEW_PAGE);
    const isMainList =
        screenDefinition.selectedRecordId === null &&
        screenDefinition.navigationPanel &&
        (screenDefinition as PageDefinition).isMainPage === true &&
        !isNewPage;
    const hasFetchedDefaults = objectKeys(controlObjects).some(key => {
        const ctrlObjProps = controlObjects[key].properties;
        return ctrlObjProps?.fetchesDefaults || ctrlObjProps.columns?.some((c: any) => c.properties?.fetchesDefaults);
    });
    const hasQuickEntry = objectKeys(controlObjects).some(key => {
        return controlObjects[key].properties?.canAddNewLine;
    });
    if (isMainList || (!hasFetchedDefaults && !hasQuickEntry)) {
        return {};
    }
    const parsedScreenValues = parseScreenValues(screenId, false, true);

    // Only dirty fields that the user manually modified should be sent to the server
    let dataValues: any = {};
    if (!clean && !sendNonDirtyFields) {
        dataValues = objectKeys(parsedScreenValues).reduce<Dict<any>>((prevValue: Dict<any>, fieldId: string) => {
            if (
                (screenDefinition.dirtyStates[fieldId] === true || has(screenDefinition.initialValues, fieldId)) &&
                (!requestedFieldIds || requestedFieldIds.indexOf(fieldId) === -1)
            ) {
                const bind =
                    (screenDefinition.metadata.uiComponentProperties?.[fieldId] as ReadonlyFieldProperties)?.bind ||
                    fieldId;
                set(prevValue, convertDeepBindToPathNotNull(bind), parsedScreenValues[fieldId]);
            }
            return prevValue;
        }, {});
        if (values) {
            Object.entries(values).forEach(([key, value]) => {
                const bind = convertDeepBindToPathNotNull(key);
                set(dataValues, bind, value);
            });
        }
    } else if (sendNonDirtyFields) {
        // All fields should be sent to the server
        dataValues = parsedScreenValues;
    }

    const fieldIds =
        requestedFieldIds ||
        getRequestableFieldIds(controlObjects, screenDefinition).filter(fieldId => {
            // Dirty fields will not be requested as we don't want the user's manual changes to be overwritten by the default values.
            return !screenDefinition.dirtyStates[fieldId];
        });

    if (isEmpty(fieldIds)) {
        return {};
    }

    const queryProps = getFieldsProperties(fieldIds, controlObjects, screenDefinition, nodeTypes, node);
    const screenProperties = screenDefinition.metadata.uiComponentProperties[screenId] as PageProperties<any>;
    const fieldsToRequest = buildNodeQueryFromProperties(queryProps, {}, true);

    // We don't request the fake ID that the defaults service returns
    delete fieldsToRequest._id;

    if (isEmpty(fieldsToRequest)) {
        return {};
    }

    // If the fake $new ID is used in duplication mode, we remove it from the payload because that is only used in the client
    if (dataValues._id === NEW_PAGE) {
        delete dataValues._id;
    } else if (!dataValues._id && screenDefinition.selectedRecordId !== NEW_PAGE) {
        dataValues._id = screenDefinition.selectedRecordId;
    }

    // TODO Filter out dirty fields
    return wrapQuery(
        screenProperties.node || '',
        {
            getDefaults: {
                __args: {
                    data: { ...dataValues, ...additionalData },
                },
                ...fieldsToRequest,
            },
        },
        alias,
    );
};

export const getNavigationPanelOrderByDefinition = (
    listItem: CardDefinition,
    explicitOrderBy?: OrderByType,
): OrderByType => {
    if (explicitOrderBy) {
        return explicitOrderBy;
    }
    const sortableFields: ComponentKey[] = [
        FieldKey.Text,
        FieldKey.Reference,
        FieldKey.Label,
        FieldKey.Numeric,
        FieldKey.Date,
        FieldKey.Progress,
    ];

    const titleProps: Omit<ReferenceProperties, 'parent' | 'onChange'> = listItem.title
        .properties as NestedReferenceProperties;
    const line2Props: Omit<ReferenceProperties, 'parent' | 'onChange'> = listItem.line2
        ?.properties as NestedReferenceProperties;
    const titleRightProps: Omit<ReferenceProperties, 'parent' | 'onChange'> = listItem.titleRight
        ?.properties as NestedReferenceProperties;

    const valueField =
        titleProps &&
        !titleProps.isTransient &&
        titleProps._controlObjectType &&
        sortableFields.includes(titleProps._controlObjectType) &&
        getReferenceValueFieldPath(titleProps);

    const orderBy = valueField ? set<any>({}, valueField, 1) : {};

    if (
        line2Props?.bind &&
        !line2Props?.isTransient &&
        line2Props._controlObjectType &&
        sortableFields.includes(line2Props._controlObjectType)
    ) {
        const line2ValueField = getReferenceValueFieldPath(line2Props);
        if (line2ValueField) {
            set<any>(orderBy, line2ValueField, 1);
        }
    } else if (
        titleRightProps?.bind &&
        !titleRightProps.isTransient &&
        titleRightProps._controlObjectType &&
        sortableFields.includes(titleRightProps._controlObjectType)
    ) {
        const titleLineRightValueField = getReferenceValueFieldPath(titleRightProps);
        if (titleLineRightValueField) {
            set<any>(orderBy, titleLineRightValueField, 1);
        }
    }

    return orderBy;
};

const getDateForFiltering = (value: string, locale: LocalizeLocale): string | null => {
    let dateValue: DateValue | null = null;
    if (isValidIsoDate(value, locale)) {
        dateValue = parseIsoDate(value, locale);
    }

    if (isValidLocalizedDate(value, locale, 'FullDate')) {
        dateValue = parseLocalizedDate(value, locale, 'FullDate');
    }

    if (!dateValue) {
        return null;
    }

    return dateValue.format('YYYY-MM-DD', locale);
};

export const buildSearchBoxFilterForNestedFields = <T extends ClientNode = any>(
    nestedFields: NestedField<ScreenBase, NestedFieldTypes, T>[],
    nodeTypes: Dict<FormattedNodeDetails>,
    filterText: string,
    locale?: string,
    node?: NodePropertyType,
): GraphQLFilter<T>[] => {
    const filterStatements: GraphQLFilter[] = [];
    const trimmedFilterText = escapeStringRegexp(filterText.trim());

    // If we dont have node it means the page or the field is transient so we dont check the nodeTypes to filter
    if (!node) {
        withoutNestedTechnical(nestedFields)
            .filter(itemDefinition => itemDefinition && itemDefinition.properties.canFilter !== false)
            .forEach(itemDefinition => {
                const bind = convertDeepBindToPathNotNull(itemDefinition.properties.bind as any);
                const regexTextFilter = { _regex: trimmedFilterText, _options: 'i' };
                filterStatements.push(
                    applyDeepNestedKey(
                        bind,
                        regexTextFilter,
                        false,
                        undefined,
                        node ? String(node) : undefined,
                        nodeTypes,
                    ),
                );
            });
        return filterStatements;
    }
    withoutNestedTechnical(nestedFields)
        .filter(itemDefinition => {
            const bind = convertDeepBindToPathNotNull(itemDefinition.properties.bind as any);
            const graphqlPropertyType = findDeepPropertyType(
                schemaTypeNameFromNodeName(node),
                bind as PropertyValueType,
                nodeTypes,
            );

            return itemDefinition && itemDefinition.properties.canFilter !== false && graphqlPropertyType?.canFilter;
        })
        .forEach(itemDefinition => {
            const bind = convertDeepBindToPathNotNull(itemDefinition.properties.bind as any);
            const regexTextFilter = { _regex: trimmedFilterText, _options: 'i' };
            const graphqlPropertyType = findDeepPropertyType(
                schemaTypeNameFromNodeName(node),
                bind as PropertyValueType,
                nodeTypes,
            );

            // TODO: Handle enums, numbers and additional types
            if (
                graphqlPropertyType?.type === GraphQLTypes.String ||
                (graphqlPropertyType?.type === GraphQLTypes.IntOrString && graphqlPropertyType.name === '_id')
            ) {
                // If the type is string, we apply a partial regex match
                filterStatements.push(set({}, bind, regexTextFilter));
            } else if (
                graphqlPropertyType?.type === GraphQLTypes.Date &&
                getDateForFiltering(filterText.trim(), locale as LocalizeLocale)
            ) {
                filterStatements.push(
                    set({}, bind, { _eq: getDateForFiltering(filterText.trim(), locale as LocalizeLocale) }),
                );
            } else if (itemDefinition.type === FieldKey.Reference) {
                // If field type is reference, we do the same type check for the value and helper text fields.
                const referenceProperties = itemDefinition.properties as NestedReferenceProperties;
                const referredNode = String(referenceProperties.node);
                const valueFieldKey = convertDeepBindToPath(referenceProperties.valueField);

                const isNestedReference = valueFieldKey && valueFieldKey.indexOf('.') !== -1;
                const valueFieldType = findDeepPropertyType(
                    schemaTypeNameFromNodeName(referredNode),
                    referenceProperties.valueField,
                    nodeTypes,
                );

                // Here there is a nested reference, so we probably don't know the exact type, so let's just hope for the best!
                if (valueFieldType?.type === GraphQLTypes.String || isNestedReference) {
                    if (isNestedReference && isDevMode()) {
                        xtremConsole.warn(
                            `Type could not be determined for '${valueFieldKey}' nested reference field, assuming String type.`,
                        );
                    }

                    const filter = set<any>(
                        {},
                        `${convertDeepBindToPathNotNull(itemDefinition.properties.bind as string)}.${valueFieldKey}`,
                        regexTextFilter,
                    );
                    filterStatements.push(filter);
                }

                const helperTextFieldKey = convertDeepBindToPath(referenceProperties.helperTextField);
                const helperTextFieldType = findDeepPropertyType(
                    schemaTypeNameFromNodeName(referredNode),
                    referenceProperties.helperTextField,
                    nodeTypes,
                );

                if (helperTextFieldKey && helperTextFieldType?.type === GraphQLTypes.String) {
                    const filter = set<any>(
                        {},
                        `${convertDeepBindToPathNotNull(
                            itemDefinition.properties.bind as string,
                        )}.${helperTextFieldKey}`,
                        regexTextFilter,
                    );
                    filterStatements.push(filter);
                }
            }
        });

    return filterStatements;
};

export const buildSearchBoxFilter = <T extends ClientNode = any>(
    fieldProperties:
        | ReferenceProperties<ScreenBase, T>
        | MultiReferenceProperties<ScreenBase, T>
        | CollectionValueFieldProperties<ScreenBase, T>,
    nodeTypes: Dict<FormattedNodeDetails>,
    locale?: string,
    collectionFieldType?: CollectionFieldTypes,
    searchBoxText?: string,
    exactMatch = false,
    isArray = false,
): GraphQLFilter<T> | undefined => {
    if (!searchBoxText) return undefined;
    const nodeType = schemaTypeNameFromNodeName(String(fieldProperties.node));
    const isSearchQueryANumber = Number.isFinite(Number(searchBoxText));

    const orFilters: GraphQLFilter[] = [];
    if (collectionFieldType === CollectionFieldTypes.LOOKUP_DIALOG) {
        const referenceProperties = fieldProperties as ReferenceProperties;
        if (
            !Object.prototype.hasOwnProperty.call(referenceProperties, 'valueField') &&
            !Object.prototype.hasOwnProperty.call(referenceProperties, 'helperTextField')
        ) {
            throw new Error(`Neither 'valueField' nor 'helperTextField' found: ${JSON.stringify(referenceProperties)}`);
        }
        const condition = {
            _regex: escapeStringRegexp(searchBoxText),
            ...(!exactMatch && { _options: 'i' }),
        };

        if (referenceProperties.helperTextField) {
            const helperTextFieldType = findDeepPropertyType(
                nodeType,
                referenceProperties.helperTextField,
                nodeTypes,
                true,
            );

            if (
                helperTextFieldType?.canFilter &&
                (helperTextFieldType?.type === GraphQLTypes.String ||
                    ((helperTextFieldType?.type === GraphQLTypes.IntOrString ||
                        helperTextFieldType?.type === GraphQLTypes.Int) &&
                        isSearchQueryANumber))
            ) {
                const helperTextFieldPath = convertDeepBindToPathNotNull(referenceProperties.helperTextField);
                const filter = set<any>(
                    isArray ? set({}, [...helperTextFieldPath.split('.').slice(0, -1), '_atLeast'], 1) : {},
                    helperTextFieldPath,
                    condition,
                );
                orFilters.push(filter);
            }
        }

        if (referenceProperties.valueField) {
            const valueFieldType = findDeepPropertyType(nodeType, referenceProperties.valueField, nodeTypes, true);

            if (
                valueFieldType?.canFilter &&
                (valueFieldType?.type === GraphQLTypes.String ||
                    ((valueFieldType?.type === GraphQLTypes.IntOrString || valueFieldType?.type === GraphQLTypes.Int) &&
                        isSearchQueryANumber))
            ) {
                const valueFieldPath = convertDeepBindToPathNotNull(referenceProperties.valueField);
                const filter = set<any>(
                    isArray ? set({}, [...valueFieldPath.split('.').slice(0, -1), '_atLeast'], 1) : {},
                    valueFieldPath,
                    condition,
                );
                orFilters.push(filter);
            }
        }

        if (referenceProperties.columns && referenceProperties.shouldSuggestionsIncludeColumns) {
            orFilters.push(
                ...buildSearchBoxFilterForNestedFields<T>(
                    referenceProperties.columns,
                    nodeTypes,
                    searchBoxText,
                    locale,
                    referenceProperties.node,
                ),
            );
        }
    } else {
        const tableProperties = fieldProperties as TableProperties;
        orFilters.push(
            ...buildSearchBoxFilterForNestedFields<T>(
                tableProperties.columns || [],
                nodeTypes,
                searchBoxText,
                locale,
                tableProperties.node,
            ),
        );
    }

    return isEmpty(orFilters) ? undefined : ({ _or: orFilters } as GraphQLFilter);
};
