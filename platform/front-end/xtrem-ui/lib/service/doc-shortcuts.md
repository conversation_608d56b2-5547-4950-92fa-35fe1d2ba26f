PATH: XTREEM/Client+Framework/Key+Shortcuts+API

## Introduction

This article summarizes how functional developers can bind events to certain key combinations and how the combinations work.

### Binding

Shortcuts can be bound to [button fields](./Button+Field) and [page actions](Page+Action). The key combination can be passed in the `shortcut` decorator property of the element, it can be defined as a string representing a key, or an array of keys representing a key combination. When the key combination is detected, it triggers the element's `onClick` event handler and the default browser behavior is prevented.

Please note that certain key combinations cannot be used as they interfere with OS or browser standards. Given that such combinations are not even sent to the Xtrem UI layer by the aforementioned, there is little we can do to support them.

### Keys

All standard keyboard keys can be used on the keyboard including all letters, numbers, function and modifier keys. The Xtrem UI engine does not differentiate between left or right versions of the modifier keys.

### OSX Key mapping rules

- The `Command` key is always parsed as the `control` key.
- The `Option` key is always parsed as the `alt` key.
