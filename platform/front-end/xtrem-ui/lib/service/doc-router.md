PATH: XTREEM/Client+Framework/Router+API

## Introduction

This page describes the routing actions that can be triggered from the functional code and how they can be accessed.

## Basic example:

```ts
onClick() {
    this.$.router.goTo('@sage/xtrem-sales/SalesInvoice', { _id : '2837152'});
}
```

All router actions can be accessed from `this.$.router` object.

## Methods

#### goTo

Navigates to another page, query parameters can be sent to the page that is being opened. Arguments:

1. Path of the page to open, for example: `'@sage/xtrem-sales/SalesInvoice'`.
2. Query parameters object

#### goToExternal

Navigates to an external link in a new tab. Arguments:

1. Link to open. It can be an external link with format: `'http://www.link.com'` or an internal link with format: `'@vendorName/package-name/PageName'`.

#### refresh

Refreshes the current page, it restores the page to its original state including field values and all component properties. It triggers the `onLoad` lifecycle hooks in page and page extension decorators. If the page is bound to any record, the value is refetched from the server.

#### emptyPage

Clears all field values and resets all component properties. It triggers the `onLoad` lifecycle hooks in page and page extension decorators.

#### selectRecord

Selects a record of the current page. The action is identical to when the the user selects a record from the navigation panel. Arguments:

1. Record it as a string.
2. Select record for duplication
3. Skip dirty check dialog

#### firstRecord

Selects the first record on the navigation panel. This action can only be used if a navigation panel is defined for the page and it has records loaded. Otherwise, it will throw an exception
#### nextRecord

Selects the next record from the navigation panel. This action can only be used if a navigation panel is defined for the page.

#### previousRecord

Selects the previous record from the navigation panel. This action can only be used if a navigation panel is defined for the page.

#### hasNextRecord

Determines whether there is a next record on the navigation panel. This action can only be used if a navigation panel is defined for the page.

#### hasPreviousRecord

Determines whether there is a previous record on the navigation panel. This action can only be used if a navigation panel is defined for the page.

#### goHome

Redirects the user to the home page.

## Unsaved changes
When any of the navigation events is triggered, the framework checks if the current page has any unsaved changes. If so it will display a confirmation dialog to the user before executing the navigation event. This can be overridden by the `skipDirtyCheck` argument that is available in all navigation functions of the router.

## Special query parameters
Certain query parameters can influence the functionality of the target page of the navigation. The name of these query parameters are available as constants exported from `@sage/xtrem-ui`. They are named `QUERY_PARAM_*`.

 - `QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER`: Filters the main list. The value is expected to be a filter object serialized to a JSON string. If the page has a main list and this value is set, an additional "My selected data" option menu filter is added to the main list and the filter is applied to the list

 - `QUERY_PARAM_SELECTED_SECTION_ID`: Sets the default section on the page. The value is expected to be the ID of a section on the target page. If the value is set and the section exists, this section is going to be activated by default, not the first one.