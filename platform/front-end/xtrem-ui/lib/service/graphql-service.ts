import type { ClientNode as metadataService } from '@sage/xtrem-client';
import { objectKeys, type Dict, type WorkflowNode } from '@sage/xtrem-shared';
import type { CancelToken } from 'axios';
import { get, isEmpty, set } from 'lodash';
import type { ComponentProperties } from '../component/base-control-object';
import { navigationPanelId } from '../component/container/navigation-panel/navigation-panel-types';
import type { OptionsMenuItem, PageProperties } from '../component/container/page/page-types';
import type { InternalNestedGridProperties, PodCollectionDecoratorProperties } from '../component/control-objects';
import type { PageDecoratorProperties } from '../component/decorators';
import type { ReferenceProperties, TreeProperties } from '../component/field/field-properties';
import type { PropertyValueType } from '../component/field/reference/reference-types';
import { getReferenceOrderBy } from '../component/field/reference/reference-utils';
import type {
    InternalTableProperties,
    TableDecoratorProperties,
    TableViewLevel,
} from '../component/field/table/table-component-types';
import type { Changeable } from '../component/field/traits';
import type { NestedField, NestedFieldTypes } from '../component/nested-fields';
import type { ReadonlyFieldControlObject, ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import type { OrderByType } from '../component/types';
import { FieldKey } from '../component/types';
import * as xtremRedux from '../redux';
import type { ApplicationContext, UiComponentUserSettings } from '../redux/state';
import type { NodePropertyType } from '../types';
import { GraphQLKind } from '../types';
import { filterFields, getNestedFieldElementId } from '../utils/abstract-fields-utils';
import { NEW_PAGE, QUERY_ALIAS_NAV_PANEL, QUERY_ALIAS_PAGE_DATA } from '../utils/constants';
import { convertDeepBindToPathNotNull, getNestedFieldsFromProperties } from '../utils/nested-field-utils';
import { findDeepPropertyDetails } from '../utils/node-utils';
import { resolveByValue } from '../utils/resolve-value-utils';
import { getOrderByFromSortModel } from '../utils/table-component-utils';
import { CollectionValue } from './collection-data-service';
import { CollectionFieldTypes } from './collection-data-types';
import { getGroupKey } from './collection-data-utils';
import {
    buildDefaultsQuery,
    buildFieldQuery,
    buildNestedAggregationQuery,
    buildNestedDefaultsQuery,
    buildReferenceFieldQuery,
    buildRootNodeQuery,
    buildRowDefaultQuery,
    buildSearchBoxFilter,
    buildTableQuery,
    buildTableTotalCountQuery,
    mergeFilters,
    nestedFieldsToQueryProperties,
    queryBuilder,
} from './graphql-query-builder';
import type { GraphQLFilter, MixedQueryWrapper, NodeCollectionResult, QueryArguments } from './graphql-utils';
import {
    executeGraphqlQuery,
    executeGraphqlQueryWithCache,
    getAllGroups,
    getGroups,
    removeEdges,
    splitNodeName,
    unwrapQueryResponse,
    wrapQuery,
} from './graphql-utils';
import { localize } from './i18n-service';
import type { DataTypeDetails, FormattedNodeDetails } from './metadata-types';
import { createNavigationTableProperties, getInitialOptionMenuItem } from './navigation-panel-service';
import type { Page } from './page';
import type { PageDefinition } from './page-definition';
import type { XtremUiPlugin } from './plugin-service';
import type { ScreenBase } from './screen-base';
import type { ScreenBaseDefinition } from './screen-base-definition';
import { showToast } from './toast-service';
import { formatScreenValues } from './value-formatter-service';

export const fetchCollectionDataCount = async ({
    rootNode,
    filter,
}: {
    rootNode: NodePropertyType;
    filter: string;
}): Promise<number> => {
    const query = buildTableTotalCountQuery({
        filter,
    });
    const wrappedQuery = wrapQuery(rootNode, query);
    const response = await executeGraphqlQueryWithCache({ query: wrappedQuery, node: String(rootNode) });
    const unwrappedResponse = unwrapQueryResponse(rootNode, response.data);
    return unwrappedResponse.query.totalCount;
};

export const fetchCollectionData = async ({
    bind,
    elementId,
    group,
    nestedFields,
    node,
    nodeTypes,
    queryArguments,
    rootNode,
    rootNodeId,
    screenDefinition,
    totalCount = false,
    treeProperties,
}: {
    bind?: PropertyValueType;
    elementId: string;
    group?: { key: string; value?: string; type: FieldKey };
    nestedFields: NestedField<ScreenBase, NestedFieldTypes>[];
    node?: string;
    nodeTypes?: Dict<FormattedNodeDetails>;
    queryArguments?: QueryArguments;
    rootNode: NodePropertyType;
    rootNodeId: string;
    screenDefinition: ScreenBaseDefinition;
    totalCount?: boolean;
    treeProperties?: TreeProperties<Page>;
}): Promise<any> => {
    const query = buildTableQuery({
        rootNodeId,
        elementId,
        columns: nestedFields,
        screenDefinition,
        queryArguments,
        bind: treeProperties?.sublevelProperty ? convertDeepBindToPathNotNull(treeProperties?.sublevelProperty) : bind,
        group,
        treeProperties,
        totalCount,
        node,
        nodeTypes,
    });

    const nodeToUse = treeProperties?.sublevelProperty && node ? node : rootNode;
    const wrappedQuery = wrapQuery(nodeToUse, query);
    const response = await executeGraphqlQueryWithCache({ query: wrappedQuery, node: String(nodeToUse) });
    const groups = group ? getGroups(response.data, getGroupKey({ groupKey: group.key, type: group.type })) : undefined;
    const unwrappedResponse = unwrapQueryResponse(nodeToUse, response.data);
    const nodeData = removeEdges(unwrappedResponse, true, true);
    if (!nodeData || !nodeData.data || !nodeData.data[0] || !nodeData.data[0][elementId]) {
        return {};
    }

    const { data = [], pageInfo = {} } = get(
        nodeData.data[0],
        treeProperties?.sublevelProperty
            ? [elementId, ...convertDeepBindToPathNotNull(treeProperties?.sublevelProperty).split('.').slice(1)]
            : [
                  elementId,
                  ...convertDeepBindToPathNotNull(bind || elementId)
                      .split('.')
                      .slice(1),
              ],
    );
    if (groups) {
        // Groups are appended to the data array with an '__isGroup' flag
        data.push(...groups.groups.map((g: any) => ({ ...g.node, __cursor: g.cursor })));
    }

    return { data, pageInfo, totalCount: nodeData.totalCount };
};

export const fetchCollectionRecord = async (
    screenDefinition: ScreenBaseDefinition,
    nestedFields: NestedField<ScreenBase, NestedFieldTypes, any, any>[],
    nodeName: NodePropertyType,
    recordId: string,
): Promise<any> => {
    const properties = nestedFieldsToQueryProperties({ screenDefinition, nestedFields });

    const query = queryBuilder(nodeName, {
        properties,
        queryArguments: { filter: JSON.stringify({ _id: recordId }) },
        noPageInfo: true,
    });
    const wrappedQuery = wrapQuery(nodeName, query);

    const response = await executeGraphqlQueryWithCache({ query: wrappedQuery, node: String(nodeName) });
    const unwrappedResponse = unwrapQueryResponse(nodeName, response.data);
    return unwrappedResponse?.query?.edges?.[0]?.node || null;
};

export const formatNavigationPanelResponse = (navigationPanelResponse: NodeCollectionResult): any[] => {
    if (!navigationPanelResponse.query || !navigationPanelResponse.query.edges) {
        showToast(
            localize('@sage/xtrem-ui/navigation-panel-failed', 'An error ocurred loading the navigation panel items'),
            { type: 'error' },
        );
    }

    return removeEdges(navigationPanelResponse, true, true).data;
};

export const fetchNavigationPanelData = async (
    screenId: string,
    screenDefinition: ScreenBaseDefinition,
    tableDecoratorProperties: TableDecoratorProperties,
    orderBy?: OrderByType,
    filter?: GraphQLFilter,
    first = 30,
    nodeTypes?: Dict<FormattedNodeDetails>,
    /**
     * If it is an initial page load query for the navigation panel, the option menu is added to the query filter by this method.
     * In other cases (e.g. filter change, sorting change), the collection data service provided filter already contains the currently selected
     * option item.
     *  */
    optionMenuItem?: OptionsMenuItem,
): Promise<any[]> => {
    const { query, node } = buildReferenceFieldQuery({
        fieldProperties: tableDecoratorProperties,
        elementId: navigationPanelId,
        screenDefinition,
        screenId,
        orderBy,
        filter,
        valueField: navigationPanelId,
        pageSize: first,
        nodeTypes,
        optionMenuItem,
    });

    const response = await executeGraphqlQueryWithCache({ query, node: String(node) });
    const navigationPanelResponse = unwrapQueryResponse(tableDecoratorProperties.node as string, response.data);
    return formatNavigationPanelResponse(navigationPanelResponse);
};

export interface FetchReferenceFieldSuggestionsInput<T extends metadataService = any> {
    after?: string;
    contextNode?: NodePropertyType;
    exactMatch?: boolean;
    fieldId: string;
    fieldProperties: Omit<ReferenceProperties<ScreenBase, T>, 'parent' | 'onChange'>;
    filterValue?: string;
    isFilterLimitedToDataset?: boolean;
    level?: number;
    locale?: string;
    parentElementId?: string;
    recordContext?: Dict<any>;
    screenId: string;
}

export const fetchReferenceFieldSuggestions = async <T extends metadataService = any>({
    after,
    contextNode,
    exactMatch,
    fieldId,
    fieldProperties,
    filterValue,
    isFilterLimitedToDataset = false,
    level,
    locale,
    parentElementId,
    recordContext,
    screenId,
}: FetchReferenceFieldSuggestionsInput<T>): Promise<T[]> => {
    const allFilters: (GraphQLFilter | (() => GraphQLFilter) | undefined)[] = [fieldProperties.filter];
    const state = xtremRedux.getStore().getState();
    const nodeTypes = state.nodeTypes;
    let isArray = false;
    const bind = fieldProperties.bind;
    const valueField =
        typeof fieldProperties.valueField === 'object'
            ? objectKeys(fieldProperties.valueField)[0]
            : fieldProperties.valueField;

    if (typeof fieldProperties.valueField === 'object') {
        const type = findDeepPropertyDetails(
            fieldProperties.node as string,
            fieldProperties.valueField as PropertyValueType,
            nodeTypes,
        );
        isArray = type?.kind === GraphQLKind.List;
    }

    const searchBoxGraphQLFilter = buildSearchBoxFilter(
        fieldProperties,
        nodeTypes,
        locale,
        CollectionFieldTypes.LOOKUP_DIALOG,
        filterValue,
        exactMatch,
        isArray,
    );

    if (searchBoxGraphQLFilter) {
        allFilters.push(searchBoxGraphQLFilter);
    }

    const screen = state.screenDefinitions[screenId];
    const filter = mergeFilters(allFilters, screen, recordContext);
    const orderBy = getReferenceOrderBy<T>(fieldProperties);
    let filterLimitedToDataset;
    let orderByLimitedToDataSet;
    if (isFilterLimitedToDataset && filter) {
        filterLimitedToDataset = { [String(fieldProperties.bind)]: filter };
    }
    if (isFilterLimitedToDataset && orderBy) {
        orderByLimitedToDataSet = { [String(fieldProperties.bind)]: orderBy } as OrderByType<T>;
    }

    const queryResult = await fetchReferenceFieldData<T>({
        after,
        contextNode,
        elementId: fieldId,
        fieldProperties,
        filter: isFilterLimitedToDataset ? filterLimitedToDataset : filter,
        isFilterLimitedToDataset,
        level,
        orderBy: isFilterLimitedToDataset ? orderByLimitedToDataSet : orderBy,
        parentElementId,
        recordContext,
        screenId,
        valueField: fieldProperties.valueField,
    });

    let initialValues = removeEdges(queryResult, true, true)?.data ?? [];
    if (isFilterLimitedToDataset) {
        initialValues = (removeEdges(queryResult, true, true)?.data ?? []).map((node: any) => ({
            _id: isFilterLimitedToDataset ? node[String(bind)]._id : node._id,
            [String(valueField)]: isFilterLimitedToDataset ? node[String(bind)][valueField] : node[valueField],
        }));
    }

    const collectionValue = new CollectionValue<T>({
        bind: fieldProperties.bind,
        screenId,
        elementId: fieldId,
        nodeTypes,
        isTransient: !!fieldProperties.isTransient,
        hasNextPage: !!queryResult.query?.pageInfo?.hasNextPage,
        orderBy: [orderBy],
        columnDefinitions: [fieldProperties.columns || []],
        nodes: [String(fieldProperties.node)],
        filter: [filter],
        initialValues,
        fieldType: CollectionFieldTypes.LOOKUP_DIALOG,
        parentElementId,
        recordContext,
        dbKey: 'lookup-temp',
    });

    return collectionValue.getData({
        cleanMetadata: false,
        inGroup: isFilterLimitedToDataset,
        temporaryRecords: resolveByValue({
            skipHexFormat: true,
            screenId,
            propertyValue: fieldProperties.additionalLookupRecords,
            rowValue: null,
            fieldValue: null,
        }),
    });
};

export const fetchReferenceFieldData = async <T extends metadataService = any>({
    after,
    axiosCancelToken,
    contextNode,
    elementId,
    fieldProperties,
    filter,
    group,
    isFilterLimitedToDataset,
    level,
    orderBy,
    pageSize,
    parentElementId,
    recordContext,
    screenId,
    valueField,
}: {
    after?: string;
    axiosCancelToken?: CancelToken;
    contextNode?: NodePropertyType;
    elementId: string;
    fieldProperties: Omit<ReferenceProperties<ScreenBase, T>, 'parent' | 'onChange'>;
    filter?: GraphQLFilter<T>;
    group?: { key: string; value?: string; type: FieldKey };
    isFilterLimitedToDataset?: boolean;
    level?: number;
    orderBy?: OrderByType<T>;
    pageSize?: number;
    parentElementId?: string;
    recordContext?: Dict<any>;
    screenId: string;
    valueField?: any;
}): Promise<NodeCollectionResult<T>> => {
    const state = xtremRedux.getStore().getState();

    // We check if we are in a nested collection context
    if (
        parentElementId !== '$navigationPanel' &&
        contextNode &&
        state.screenDefinitions[screenId].values[String(parentElementId)] &&
        isFilterLimitedToDataset
    ) {
        const value = state.screenDefinitions[screenId].values[String(parentElementId)];

        if (value instanceof CollectionValue) {
            const { parentNode, parentId, childLevel, parentBind } = value.getParentNodeAndParentIdFromChildNode({
                node: String(contextNode),
            });

            /*
            If we have the filter limited to the dataset we need to group and do a aggregation query.
            To be sure, we also we check if we have all the ids and nodes needed and if the level it's correct in the collection value
        */
            if (parentNode && parentId && childLevel > 0) {
                const referenceProperties = fieldProperties as ReferenceProperties<ScreenBase>;
                const parsedValueField =
                    typeof referenceProperties.valueField === 'object'
                        ? objectKeys(referenceProperties.valueField)[0]
                        : referenceProperties.valueField;
                const groupBy = { key: `${referenceProperties.bind}.${parsedValueField}`, type: FieldKey.Reference };

                const aggregationQuery = buildNestedAggregationQuery({
                    elementId,
                    group: groupBy,
                    rootNodeId: parentId,
                    bind: parentBind,
                });
                const res = await executeGraphqlQueryWithCache({
                    query: wrapQuery(parentNode, aggregationQuery),
                    axiosCancelToken,
                    node: String(parentNode),
                });
                const groups = groupBy
                    ? getGroups(res.data, getGroupKey({ groupKey: groupBy.key, type: groupBy.type }))
                    : undefined;
                const edgesPath = `${splitNodeName(parentNode).join('.')}.query.edges`;
                // Groups are appended to the data array with an '__isGroup' flag
                set(res.data, edgesPath, groups.groups);
                return unwrapQueryResponse(parentNode, res.data);
            }
        }
    }

    const { query, bind, node, fieldNode } = buildReferenceFieldQuery({
        after,
        contextNode,
        elementId,
        fieldProperties,
        filter,
        group,
        isFilterLimitedToDataset,
        level,
        nodeTypes: state.nodeTypes,
        orderBy,
        pageSize,
        parentElementId,
        recordContext,
        screenDefinition: state.screenDefinitions[screenId],
        screenId,
        valueField,
    });
    let data = (await executeGraphqlQueryWithCache({ query, node: String(fieldNode), axiosCancelToken }))?.data;
    if (!bind) {
        // Not using lookup API
        const groups = group ? getGroups(data, getGroupKey({ groupKey: group?.key, type: group.type })) : undefined;
        if (groups) {
            const edgesPath = `${splitNodeName(fieldNode).join('.')}.query.edges`;
            // Groups are appended to the data array with an '__isGroup' flag
            set(data, edgesPath, [...groups.groups, ...get(data, edgesPath, [])]);
        }
    } else {
        /**
         * Using lookup API => build object to be unwrapped
         *
         * {
                "xtremShowCase": {
                    "showCaseProduct": {
                    "lookups": {
                        "provider": {
                        "edges": [
                            {
                            "node": {
                                "textField": "Ali Express",
                                "_id": "1"
                            },
                            "cursor": "[1]#78"
                            },
                            {
                            "node": {
                                "textField": "Amazon",
                                "_id": "2"
                            },
                            "cursor": "[2]#37"
                            }
                        ],
                        "pageInfo": {
                            "startCursor": "[1]#78",
                            "endCursor": "[2]#37",
                            "hasPreviousPage": false,
                            "hasNextPage": false
                        }
                        }
                    }
                    }
                }
            }
            becomes
            {
                "xtremShowCase": {
                    "showCaseProvider": {
                    "query": {
                        "edges": [
                        {
                            "node": {
                            "textField": "Ali Express",
                            "_id": "1"
                            },
                            "cursor": "[1]#78"
                        },
                        {
                            "node": {
                            "textField": "Amazon",
                            "_id": "2"
                            },
                            "cursor": "[2]#37"
                        }
                        ],
                        "pageInfo": {
                        "startCursor": "[1]#78",
                        "endCursor": "[2]#37",
                        "hasPreviousPage": false,
                        "hasNextPage": false
                        }
                    }
                    }
                }
            }
         */
        const lookupKeyAccessor = splitNodeName(node).join('.');
        const edges = get(data, `${lookupKeyAccessor}.lookups.${bind}`);
        const setKey = splitNodeName(fieldNode).join('.');
        data = set<any>({}, `${setKey}.query`, edges);
    }
    return unwrapQueryResponse(fieldNode, data);
};

export const fetchReferenceFieldDataById = async <T extends metadataService = any>({
    _id,
    contextNode,
    elementId,
    fieldProperties,
    level,
    parentElementId,
    recordContext,
    screenId,
    valueField,
}: {
    _id: string;
    contextNode?: NodePropertyType;
    elementId: string;
    fieldProperties: Omit<ReferenceProperties<ScreenBase, T>, 'parent'>;
    level?: number;
    parentElementId?: string;
    recordContext?: Dict<any>;
    screenId: string;
    valueField?: any;
}): Promise<any> => {
    const result = await fetchReferenceFieldData<T>({
        filter: { _id } as any,
        contextNode,
        elementId,
        fieldProperties,
        level,
        parentElementId,
        recordContext,
        screenId,
        valueField,
    });

    return result.query.edges[0]?.node;
};
export interface FetchRecordDataArgs {
    nodeTypes: Dict<FormattedNodeDetails>;
    recordId?: string;
    requiredSections?: string[];
    screenDefinition: ScreenBaseDefinition;
    screenId: string;
    skipHeaderFields?: boolean;
    includeFieldsWithNoParent?: boolean;
}

export const fetchRecordData = async ({
    requiredSections = [],
    screenId,
    recordId,
    screenDefinition,
    nodeTypes,
    skipHeaderFields,
    includeFieldsWithNoParent = false,
}: FetchRecordDataArgs): Promise<any> => {
    const controlObjects = screenDefinition.metadata.controlObjects;
    const uiComponentProperties = screenDefinition.metadata.uiComponentProperties;
    const pageProperties = uiComponentProperties[screenId] as PageProperties;
    const nodeName = pageProperties.node!;
    const rootNodeQuery = recordId
        ? buildRootNodeQuery(
              nodeName,
              filterFields(
                  controlObjects,
                  requiredSections,
                  screenDefinition as PageDefinition,
                  includeFieldsWithNoParent,
              ),
              screenDefinition,
              recordId,
              undefined,
              nodeTypes,
              false,
              skipHeaderFields,
          )
        : buildDefaultsQuery({
              screenDefinition,
              alias: QUERY_ALIAS_PAGE_DATA,
              node: nodeName,
              nodeTypes,
          });
    const response = await executeGraphqlQueryWithCache({ query: rootNodeQuery, node: String(nodeName) });
    const unwrapped = removeEdges(unwrapQueryResponse(nodeName, response.data), true, true);
    const result = unwrapped.read;

    const groups = getGroups(response.data);
    if (groups) {
        if (!result[groups.key].data) {
            result[groups.key].data = [];
        }
        // Groups are appended to the data array with an '__isGroup' flag
        result[groups.key].data.push(...groups.groups.map((g: any) => ({ ...g.node, __cursor: g.cursor })));
    }
    return result;
};

export const fetchField = async (
    screenDefinition: ScreenBaseDefinition,
    fieldProperties: ReadonlyFieldProperties,
    recordId: string,
    elementId: string,
    plugins: Dict<XtremUiPlugin>,
    nodeTypes: Dict<FormattedNodeDetails>,
    nodeName?: string,
    keepPageInfo = false,
    userSettings?: Dict<Dict<UiComponentUserSettings>>,
): Promise<any> => {
    const pageMetadata = screenDefinition.metadata;
    const pageProperties = pageMetadata.uiComponentProperties[pageMetadata.screenId] as PageProperties;

    const nodeQuery = buildFieldQuery(
        pageProperties.node!,
        pageMetadata.controlObjects[elementId] as ReadonlyFieldControlObject<any, any, any>,
        screenDefinition,
        fieldProperties,
        elementId,
        recordId,
        nodeTypes,
        userSettings?.[elementId]?.$current,
    );

    const query = wrapQuery(pageProperties.node!, nodeQuery);

    const response = await executeGraphqlQuery({ query });
    const unwrapped = unwrapQueryResponse(pageProperties.node!, response.data);
    const noEdges = removeEdges(unwrapped, keepPageInfo, true);
    const nodeData = keepPageInfo ? (noEdges.data && noEdges.data[0]) || {} : noEdges[0] || {};
    const formattedValues = formatScreenValues({
        screenId: pageMetadata.screenId,
        controlObjects: pageMetadata.controlObjects as Dict<ReadonlyFieldControlObject<any, any, any>>,
        plugins,
        nodeTypes,
        values: nodeData,
        parentNode: pageProperties.node as string,
        onlyElementIds: [elementId],
        userSettings: screenDefinition.userSettings,
    });
    return formattedValues[nodeName || elementId];
};

export interface InitialData {
    navigationPanel: any[];
    values: ScreenBaseDefinition['values'];
}

export interface FetchInitialDataArgs {
    pageDefinition: PageDefinition;
    recordId?: string;
    path?: string;
    applicationContext?: ApplicationContext;
    nodeTypes?: Dict<FormattedNodeDetails>;
    dataTypes?: Dict<DataTypeDetails>;
    /**
     * If the values are provided, they are not fetched from the server.
     * This feature is used to pre-populate page dialogs with data.
     *  */
    values?: Dict<any>;
    isDuplicate?: boolean;
    serviceOptions: Dict<boolean>;
    requiredSections?: string[];
    includeFieldsWithNoParent?: boolean;
}

export const fetchInitialData = async ({
    pageDefinition,
    recordId,
    path,
    applicationContext,
    nodeTypes,
    dataTypes,
    /**
     * If the values are provided, they are not fetched from the server.
     * This feature is used to pre-populate page dialogs with data.
     *  */
    values,
    isDuplicate = false,
    serviceOptions = {},
    requiredSections = [],
}: FetchInitialDataArgs): Promise<InitialData> => {
    const pageMetadata = pageDefinition.metadata;
    const { screenId } = pageMetadata;
    const pageProperties = pageMetadata.uiComponentProperties[screenId] as PageProperties;
    const rootNode = pageProperties.node as NodePropertyType;

    let query: MixedQueryWrapper = {};
    // If no root node is provided, the page is handled as it would be transient because it can't fetch data without a target node.
    const isPageTransient = pageProperties.isTransient || !rootNode;

    if (!isPageTransient && recordId && recordId !== NEW_PAGE) {
        // If the page is not transient and there is a record ID, query the record data
        const nodeQuery = buildRootNodeQuery(
            rootNode,
            filterFields(pageMetadata.controlObjects, requiredSections, pageDefinition, true),
            pageDefinition,
            recordId,
            QUERY_ALIAS_PAGE_DATA,
            nodeTypes,
            isDuplicate,
        );
        query = {
            ...query,
            ...nodeQuery,
        };
    } else if (!isPageTransient && recordId === NEW_PAGE) {
        // If the NEW_PAGE record ID provided and the page is not transient, query the default values for the new record
        query = {
            ...query,
            ...buildDefaultsQuery({
                screenDefinition: pageDefinition,
                alias: QUERY_ALIAS_PAGE_DATA,
                node: rootNode,
                nodeTypes,
                values,
            }),
        };
    }

    if (pageProperties.navigationPanel && rootNode && nodeTypes && dataTypes && !isDuplicate) {
        const navigationTableProperties = await createNavigationTableProperties(
            screenId,
            rootNode,
            pageDefinition,
            nodeTypes,
            dataTypes,
            path,
            applicationContext,
            serviceOptions,
        );

        const optionMenuItem = getInitialOptionMenuItem(pageDefinition, navigationTableProperties);
        const navigationPanelUserSetting: TableViewLevel =
            pageDefinition.userSettings?.[navigationPanelId]?.$current?.content?.[0];

        /**
         * The navigation panel query is identical to a transient reference field suggestions lookup query, so we reuse
         * the same function. In both cases we query the root list of the node type with a given criteria.
         *  */
        const { query: navPanelQuery } = buildReferenceFieldQuery({
            fieldProperties: navigationTableProperties,
            elementId: navigationPanelId,
            nodeTypes,
            screenDefinition: pageDefinition,
            screenId,
            orderBy: navigationPanelUserSetting?.sortOrder
                ? getOrderByFromSortModel(navigationPanelUserSetting.sortOrder, navigationTableProperties.columns || [])
                : navigationTableProperties.orderBy,
            filter: navigationTableProperties.filter,
            valueField: navigationPanelId,
            queryAlias: QUERY_ALIAS_NAV_PANEL,
            optionMenuItem,
        });

        query = {
            ...query,
            ...navPanelQuery,
        };
    }

    const initialData: InitialData = {
        values: values || {},
        navigationPanel: [],
    };

    if (isEmpty(query)) {
        return initialData;
    }

    const response = await executeGraphqlQueryWithCache({ query, node: String(rootNode) });
    if (rootNode && response.data) {
        initialData.values = {};
        const unwrappedRootNode: any = response.data[QUERY_ALIAS_PAGE_DATA]
            ? unwrapQueryResponse(rootNode, response.data, QUERY_ALIAS_PAGE_DATA)
            : false;
        if (unwrappedRootNode?.getDefaults) {
            // Handling default values
            initialData.values = unwrappedRootNode.getDefaults;
        } else if (unwrappedRootNode) {
            // Handling real record data
            initialData.values = removeEdges(
                unwrappedRootNode[isDuplicate ? 'getDuplicate' : 'read'] || {},
                true,
                true,
            );
        } else if (values) {
            initialData.values = values;
        }

        const groups = getAllGroups(response.data.rootNode);
        objectKeys(groups).forEach(fieldKey => {
            const group = groups[fieldKey];
            if (!initialData.values[group.key].data) {
                initialData.values[group.key].data = [];
            }
            // Groups are appended to the data array with an '__isGroup' flag
            initialData.values[group.key].data.push(
                ...group.groups.map((g: any) => ({ ...g.node, __cursor: g.cursor })),
            );
        });
        if (recordId && response.data[QUERY_ALIAS_PAGE_DATA] && objectKeys(initialData.values).length === 0) {
            showToast(`No record found with id ${recordId}`, { type: 'error' });
        }
    }

    if (response.data[QUERY_ALIAS_NAV_PANEL] && rootNode) {
        const navigationPanelResponse = unwrapQueryResponse(rootNode, response.data, QUERY_ALIAS_NAV_PANEL);
        initialData.navigationPanel = formatNavigationPanelResponse(navigationPanelResponse);
    }

    return initialData;
};

export const fetchNestedDefaultValues = async ({
    screenId,
    elementId,
    isNewRow = true,
    recordId,
    nestedElementId,
    level = 0,
    data,
}: {
    screenId: string;
    elementId: string;
    isNewRow?: boolean;
    recordId?: string;
    nestedElementId?: string;
    level?: number;
    data?: any;
}): Promise<{ pageDefaults?: Dict<any>; nestedDefaults?: Dict<any> }> => {
    const pageDefaults = 'pageDefaults';
    const nestedDefaults = 'nestedDefaults';

    const state = xtremRedux.getStore().getState();
    const screenDefinition = state.screenDefinitions[screenId];
    const screenProperties = screenDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<any>;
    const fieldProperties = screenDefinition.metadata.uiComponentProperties[elementId] as Changeable<Page> &
        (InternalTableProperties<Page> | InternalNestedGridProperties<Page> | PodCollectionDecoratorProperties<Page>);

    const isNestedGrid = Object.prototype.hasOwnProperty.call(fieldProperties, 'levels');
    const nestedLevel = isNestedGrid ? (fieldProperties as InternalNestedGridProperties<Page>).levels[level] : null;
    const nestedField = isNestedGrid
        ? nestedLevel?.columns.find(column => getNestedFieldElementId(column) === nestedElementId)
        : getNestedFieldsFromProperties(
              fieldProperties as InternalTableProperties<Page> | PodCollectionDecoratorProperties<Page>,
          ).find(column => getNestedFieldElementId(column) === nestedElementId);
    const nestedFieldProperties = nestedField?.properties as Changeable<Page>;
    const node = isNestedGrid ? nestedLevel?.node : fieldProperties.node;
    const shouldFetchNestedDefaults =
        (isNewRow && node && !fieldProperties.isTransient) ||
        (nestedFieldProperties?.fetchesDefaults && node && !fieldProperties.isTransient);

    const result: Dict<any> = {};

    const queryParameters = (screenDefinition as PageDefinition).queryParameters;

    const shouldFetchPageDefaults =
        !isNewRow &&
        fieldProperties.fetchesDefaults &&
        screenProperties.node &&
        !screenProperties.isTransient &&
        screenDefinition.type === 'page' &&
        (!queryParameters?._id || queryParameters?._id === NEW_PAGE);

    if (shouldFetchPageDefaults) {
        const pageQueryResult = await executeGraphqlQuery({
            query: buildDefaultsQuery({ screenDefinition, alias: pageDefaults }),
        });
        result[pageDefaults] = unwrapQueryResponse(
            String(screenProperties.node),
            pageQueryResult.data,
            pageDefaults,
        ).getDefaults;
    }

    if (
        shouldFetchNestedDefaults &&
        (isNestedGrid || (fieldProperties as ComponentProperties)._controlObjectType !== FieldKey.Table)
    ) {
        const nestedQueryResult = await executeGraphqlQuery({
            query: buildNestedDefaultsQuery({
                screenDefinition,
                elementId,
                isNewRow,
                alias: nestedDefaults,
                recordId,
                level,
                data,
                nodeTypes: state.nodeTypes,
            }),
        });
        const rowNode = isNestedGrid
            ? String((fieldProperties as InternalNestedGridProperties<Page>).levels[level].node)
            : String(fieldProperties.node);
        result[nestedDefaults] = unwrapQueryResponse(rowNode, nestedQueryResult.data, nestedDefaults).getDefaults;
    } else if (shouldFetchNestedDefaults) {
        const nestedQueryResult = await executeGraphqlQuery({
            query: buildRowDefaultQuery({
                screenDefinition,
                elementId,
                isNewRow,
                alias: nestedDefaults,
                recordId,
                data,
                nodeTypes: state.nodeTypes,
            }),
        });
        const node = String(screenProperties.node);

        result[nestedDefaults] = removeEdges(
            unwrapQueryResponse(node, nestedQueryResult.data, nestedDefaults).getDefaults,
        )[elementId][0];
    }

    return result;
};

export const fetchDefaultValues = async (
    screenDefinition: ScreenBaseDefinition,
    rootNode: string,
    requestedFieldIds?: string[],
    clean?: boolean,
    nodeTypes?: Dict<FormattedNodeDetails>,
): Promise<Dict<any>> => {
    const query = buildDefaultsQuery({ screenDefinition, requestedFieldIds, clean, node: rootNode, nodeTypes });
    if (isEmpty(query)) {
        return {};
    }

    const result = await executeGraphqlQuery({ query });
    const unwrapped = unwrapQueryResponse(rootNode, result.data);
    return unwrapped.getDefaults || {};
};

export const fetchWorkflowNodes = async (): Promise<WorkflowNode[]> => {
    const query = {
        workflow: {
            getAvailableWorkflowNodes: {
                key: true,
                title: true,
                description: true,
                color: true,
                icon: true,
                configurationPage: true,
                defaultConfig: true,
                type: true,
            },
        },
    };

    const result = await executeGraphqlQuery({ query, endpoint: '/metadata' });
    return result.data.workflow.getAvailableWorkflowNodes || [];
};
