import type { Dict } from '@sage/xtrem-shared';

import type { ReferenceDecoratorProperties } from '../component/decorator-properties';
import { withoutNestedTechnical } from '../component/nested-fields';
import { GraphQLTypes, type OrderByType } from '../component/types';
import type { AgGridColumnConfigWithScreenIdAndColDef } from '../utils/ag-grid/ag-grid-utility-types';
import { xtremConsole } from '../utils/console';
import { convertDeepBindToPath, convertDeepBindToPathNotNull } from '../utils/nested-field-utils';
import { findDeepPropertyType } from '../utils/node-utils';
import { schemaTypeNameFromNodeName } from '../utils/transformers';
import { executeGraphqlQuery } from './graphql-utils';
import { localize } from './i18n-service';
import type { FormattedNodeDetails } from './metadata-types';

export interface ExportTableDataArgs {
    node: string;
    orderBy?: OrderByType;
    filter: any;
    columns: AgGridColumnConfigWithScreenIdAndColDef[];
    nodeTypes: Dict<FormattedNodeDetails>;
    outputFormat: 'csv' | 'xlsx';
}

export async function exportTableData({
    columns,
    nodeTypes,
    node,
    orderBy,
    filter,
    outputFormat,
}: ExportTableDataArgs): Promise<void> {
    const nodeName = schemaTypeNameFromNodeName(node);
    // Remove technical columns
    const templateDefinition = withoutNestedTechnical(
        columns
            // Filter out hidden columns and technical columns such as actions, selection checkboxes, validation status, etc.
            .filter(c => !c.hide && !!c.type && !!c.context.columnDefinition)
            .map(c => c.context.columnDefinition!),
    )
        // Filter out transient columns
        .filter(c => !c.properties.isTransient)
        .map(columnDefinition => {
            const bind = convertDeepBindToPathNotNull(columnDefinition.properties.bind);
            const valueField = convertDeepBindToPath(
                (columnDefinition.properties as ReferenceDecoratorProperties).valueField,
            );
            return {
                // In case of reference fields, we need to send the displayed value field
                path: valueField ? `${bind}.${valueField}` : bind,
                title: columnDefinition.properties.title,
            };
        })
        .filter(c => {
            const deepType = findDeepPropertyType(nodeName, c.path, nodeTypes);
            // Filter out non scalar types and those which are not on the output type
            if (
                !deepType ||
                deepType.isCollection ||
                !deepType.isOnOutputType ||
                deepType.type === GraphQLTypes._InputStream
            ) {
                return false;
            }
            return true;
        });

    if (templateDefinition.length === 0) {
        throw new Error(localize('@sage/xtrem-ui/table-export-service-no-columns', 'No columns to export.'));
    }

    const query = {
        mutation: {
            global: {
                exportByTemplateDefinition: {
                    start: {
                        __args: {
                            templateDefinition,
                            nodeName,
                            orderBy: JSON.stringify(orderBy),
                            filter: JSON.stringify(filter || {}),
                            outputFormat,
                        },
                        trackingId: true,
                    },
                },
            },
        },
    };
    try {
        const result = await executeGraphqlQuery({ query });
        xtremConsole.info(
            `Table data export started, tracking ID: ${result.data.global.exportByTemplateDefinition.start.trackingId}`,
        );
    } catch (error) {
        xtremConsole.error('Failed to export table data', error);
        throw error;
    }
}
