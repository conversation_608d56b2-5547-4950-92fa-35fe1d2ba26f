import lokijs, { <PERSON>O<PERSON> } from '@sage/lokijs';
import type { ClientNode, ExtractEdgesPartial } from '@sage/xtrem-client';
import { deepMerge, objectKeys } from '@sage/xtrem-shared';
import { cloneDeep, get, groupBy, initial, isEmpty, isNil, mergeWith, noop } from 'lodash';
import { navigationPanelId } from '../component/container/navigation-panel/navigation-panel-types';
import type { NestedField, NestedFieldTypes } from '../component/nested-fields';
import type { PartialCollectionValue, PartialCollectionValueWithIds } from '../component/types';
import { xtremConsole } from '../utils/console';
import { SERVER_VALIDATION_RULE_PREFIX } from '../utils/constants';
import { transformToLokiJsFilter } from '../utils/lokijs-filter-transformer';
import { cleanMetadataFromRecord } from '../utils/transformers';
import { isDevMode } from '../utils/window';
import type {
    CollectionGlobalValidationState,
    CollectionValueType,
    InternalValue,
    NormalizedCollection,
} from './collection-data-types';
import { RecordActionType } from './collection-data-types';
import { sortResultSet } from './collection-data-utils';
import type { ScreenBase } from './screen-base';
import type { ValidationResult } from './screen-base-definition';

type LokiOptions<T extends ClientNode = any> = {
    dbKey?: string;
    elementId: string;
    initialValues: PartialCollectionValue<T>[];
    isTransient: boolean;
    screenId: string;
};
const mappingNoop = (d: any): any => d;

const COMPOSITE_KEY_SEPARATOR = '.';

export const extendLokiOperators = (): void => {
    // Custom operator
    (LokiOps as any).$empty = (obj: Object, flag: boolean): boolean => {
        const empty = isEmpty(obj);
        return flag ? empty : !empty;
    };
};

extendLokiOperators();
// eslint-disable-next-line new-cap
const lokiDb = new lokijs('CollectionDatabase');
export const destroyScreenCollections = (screenId: string, keepNavigationPanelDb = false): void =>
    lokiDb
        .listCollections()
        .filter(c => c.name.indexOf(`${screenId}-`) === 0)
        .filter(c => !keepNavigationPanelDb || c.name.indexOf(navigationPanelId) === -1)
        .forEach(c => lokiDb.removeCollection(c.name));

export class LokiDb<T extends ClientNode = any> implements NormalizedCollection<T> {
    protected readonly collection: lokijs.Collection<InternalValue<T>>;

    protected minId = 0;

    protected result: Resultset<InternalValue<T> & LokiObj> | null = null;

    protected readonly screenId: string;

    protected readonly elementId: string;

    static getCollectionId(screenId: string, elementId: string, dbKey?: string): string {
        return `${screenId}-${elementId}${dbKey ? `-${dbKey}` : ''}`;
    }

    constructor({ screenId, elementId, initialValues, isTransient, dbKey }: LokiOptions<T>) {
        this.screenId = screenId;
        this.elementId = elementId;

        const collectionId = LokiDb.getCollectionId(screenId, elementId, dbKey);
        if (lokiDb.getCollection(collectionId)) {
            lokiDb.removeCollection(collectionId);
        }

        this.collection = lokiDb.addCollection<InternalValue<T>>(collectionId, {
            unique: ['__path'],
            indices: ['__dirty', '__action', '_id', '__path', '__compositeKey', '__level', '__uncommitted'],
            disableMeta: true,
            caseInsensitive: true,
        });

        initialValues.forEach(initialValue => {
            const hasId = Object.prototype.hasOwnProperty.call(initialValue, '_id');
            this.addRecord({
                data: { ...initialValue },
                dirty: !hasId,
                action: !hasId && !isTransient ? RecordActionType.ADDED : 'none',
            });
        });
    }

    static groupBy(input: any[], groupKey: string): any {
        return groupBy(input, element => get(element, groupKey));
    }

    public calculateAggregatedValue({
        aggregationKey,
        aggregationMethod,
        ...findAllParams
    }: Parameters<NormalizedCollection<T>['calculateAggregatedValue']>[0]): number {
        const items = this.findAll({ ...findAllParams }).value();

        const getSum = (): number => {
            return items.reduce((acc, item) => {
                const value = get(item, aggregationKey);
                if (value != null && Number.isNaN(Number(value))) {
                    throw new Error(`Unsupported sum aggregation on value "${value}"`);
                }
                return value == null ? acc : (acc || 0) + value;
            }, Number.NaN);
        };

        switch (aggregationMethod) {
            case 'min':
                return items.reduce((acc, item) => {
                    const value = get(item, aggregationKey);
                    return value == null || (acc || Number.MAX_SAFE_INTEGER) < value ? acc : value;
                }, Number.NaN);
            case 'max':
                return items.reduce((acc, item) => {
                    const value = get(item, aggregationKey);
                    return value == null || (acc || Number.MIN_SAFE_INTEGER) > value ? acc : value;
                }, Number.NaN);
            case 'sum':
                return getSum();
            case 'avg':
                const sum = getSum();
                return Number.isNaN(sum) ? sum : sum / items.length;
            case 'count':
                return items.reduce((acc, item) => {
                    return get(item, aggregationKey) ? (acc || 0) + 1 : acc;
                }, Number.NaN);
            default:
                throw new Error(`Unsupported group method: "${aggregationMethod}"`);
        }
    }

    public withTemporaryRecords<B>(body: (db: this) => B, temporaryRecords: ExtractEdgesPartial<T>[] = []): B {
        let cleanup = noop;
        try {
            const references = temporaryRecords.map(data => {
                const addedRecord = this.collection.add(
                    this.createRecord({ data, action: RecordActionType.TEMPORARY }),
                );
                return addedRecord.$loki;
            });
            cleanup = (): void => references.forEach(r => this.collection.remove(r));
            return body(this);
        } finally {
            cleanup();
        }
    }

    public getCompositeKey({
        _id,
        __level = 0,
        __isGroup: isGroup,
        __groupKey: groupKey,
        __uncommitted,
    }: {
        _id: string;
        __level?: number;
        __isGroup?: boolean;
        __groupKey?: string;
        __uncommitted?: boolean;
    }): string {
        if (isGroup === undefined || isGroup === false) {
            return `${_id}${COMPOSITE_KEY_SEPARATOR}${__level}${__uncommitted ? '_UNCOMMITTED' : ''}`;
        }
        return `${_id}${COMPOSITE_KEY_SEPARATOR}${__level}${COMPOSITE_KEY_SEPARATOR}${
            groupKey ?? ''
        }${COMPOSITE_KEY_SEPARATOR}${__uncommitted ? '_UNCOMMITTED' : ''}group`;
    }

    public getPathProperty(record: {
        _id: string;
        __level?: number;
        __parentId?: string;
        __uncommitted?: boolean;
    }): string {
        const path = [String(record.__level || 0), ...this.getIdPathToRecordByRecord(record)].join(',');
        return record.__uncommitted ? `${path},uncommitted` : path;
    }

    public getIdAndLevelFromCompositeKey(compositeKey: string): { _id: string; __level?: number } {
        const parts = compositeKey.split(COMPOSITE_KEY_SEPARATOR);
        if (parts.length === 0) {
            throw new Error(`Invalid composite key, separator not found: ${compositeKey}`);
        }

        return {
            _id: parts[0],
            __level: isNil(parts[1]) ? undefined : Number(parts[1]),
        };
    }

    protected ensureResult(): void {
        if (this.result === null) {
            throw new Error('Empty result!');
        }
    }

    public generateIndex(): string {
        const ids = this.collection
            .chain()
            .data({ removeMeta: true })
            .map(r => cloneDeep(r))
            .reduce(
                (previousValue, currentRow) => {
                    const id = parseInt(currentRow._id || '', 10);
                    if (!currentRow.__isGroup && Number.isInteger(id)) {
                        previousValue.push(id);
                    }

                    return previousValue;
                },
                [0],
            );
        this.minId = Math.min(...ids, this.minId) - 1;
        return String(this.minId);
    }

    /** Prepare the collection information for server side processing. Generated IDs and LokJS flags removed */
    public exportDataForServerProcessing(): T[] {
        return this.collection
            .chain()
            .data({ removeMeta: true })
            .map(r => cloneDeep(r))
            .map(({ _id, ...rest }) => {
                const updatedRecord = Number.parseInt(_id, 10) < 0 ? { ...rest } : { _id, ...rest };
                // The __action field is not removed as the server needs that value for knowing how the change is applied
                return cleanMetadataFromRecord(updatedRecord, false);
            });
    }

    public findOne(args: {
        id?: string;
        level?: number | null;
        parentId?: string;
        where?: LokiQuery<InternalValue<T>>;
        cleanMetadata?: undefined;
        includeUnloaded?: boolean;
        isUncommitted?: boolean;
    }): PartialCollectionValueWithIds<T>;
    public findOne(args: {
        id?: string;
        level?: number | null;
        parentId?: string;
        where?: LokiQuery<InternalValue<T>>;
        cleanMetadata?: true;
        includeUnloaded?: boolean;
        isUncommitted?: boolean;
    }): PartialCollectionValueWithIds<T>;
    public findOne(args: {
        id?: string;
        level?: number | null;
        parentId?: string;
        where?: LokiQuery<InternalValue<T>>;
        cleanMetadata?: false;
        includeUnloaded?: boolean;
        isUncommitted?: boolean;
    }): InternalValue<T>;
    public findOne(args: {
        id?: string;
        level?: number | null;
        parentId?: string;
        where?: LokiQuery<InternalValue<T>>;
        cleanMetadata?: boolean;
        includeUnloaded?: boolean;
        isUncommitted?: boolean;
    }): InternalValue<T> | PartialCollectionValueWithIds<T>;
    public findOne({
        id,
        level = 0,
        parentId,
        where = {},
        cleanMetadata = true,
        includeUnloaded = false,
        isUncommitted = false,
    }: Parameters<NormalizedCollection<T>['findOne']>[0]):
        | (InternalValue<T> & LokiObj)
        | PartialCollectionValueWithIds<T>
        | null {
        const result = this.collection.findOne({
            ...where,
            ...{ __uncommitted: isUncommitted ? { $eq: true } : { $in: [0, undefined] } },
            ...(id && { _id: { $eq: String(id) } }),
            __level: level === 0 ? { $in: [0, undefined] } : { $eq: level },
            ...(parentId && { __parentId: { $eq: parentId } }),
            ...(!includeUnloaded && { __unloaded: { $ne: true } }),
        });
        return cleanMetadata ? cleanMetadataFromRecord(cloneDeep(result)) : cloneDeep(result);
    }

    public findAll({
        level,
        parentId,
        where = {},
        includeUnloaded = false,
        includePhantom = false,
        isUncommitted = false,
    }: Parameters<NormalizedCollection<T>['findAll']>[0] = {}): this {
        const parentCondition =
            parentId === undefined
                ? { __parentId: { $exists: false } }
                : parentId === null
                  ? {}
                  : { __parentId: { $eq: parentId } };
        const levelCondition =
            level === undefined || level === 0
                ? { __level: { $in: [0, undefined] } }
                : level === null
                  ? {}
                  : { __level: { $eq: level } };
        const unloadedCondition = !includeUnloaded ? { __unloaded: { $ne: true } } : {};
        const uncommittedCondition = { __uncommitted: isUncommitted ? { $eq: true } : { $in: [0, undefined] } };

        this.result = (this.result || this.collection.chain())
            .find({ ...(!includePhantom && { __phantom: { $ne: true } }), ...where })
            .find({
                ...levelCondition,
                ...parentCondition,
                ...unloadedCondition,
                ...uncommittedCondition,
            });

        return this;
    }

    public where(fun: (data: InternalValue<T> & LokiObj) => boolean): this {
        this.result = (this.result || this.collection.chain()).where(fun);
        return this;
    }

    public clear(): this {
        this.collection.clear();
        return this;
    }

    public remove({
        data,
        beforeRemove = noop,
        afterRemove = noop,
    }: Parameters<NormalizedCollection<T>['remove']>[0]): this {
        beforeRemove();
        this.collection.remove(data);
        afterRemove();
        return this;
    }

    public sort({
        columnDefinitions,
        orderBy,
    }: {
        columnDefinitions: NestedField<ScreenBase, NestedFieldTypes, T, any>[];
        orderBy?: Parameters<NormalizedCollection<T>['sort']>[0]['orderBy'];
    }): this {
        if (orderBy) {
            this.ensureResult();
            this.result = sortResultSet({
                resultSet: this.result!,
                orderBy,
                columnDefinitions,
            });
        }
        return this;
    }

    public filter({ where, groupByKey }: Parameters<NormalizedCollection<T>['filter']>[0]): this {
        if (where && !isEmpty(where)) {
            this.ensureResult();
            const filter = groupByKey
                ? { _or: [{ ...this.filter, __isGroup: { _neq: true } }, { __isGroup: { _eq: true } }] }
                : this.filter;

            const lokiJsFilter = transformToLokiJsFilter(filter);
            try {
                this.result = this.result!.find(transformToLokiJsFilter(lokiJsFilter));
            } catch (e) {
                if (isDevMode()) {
                    xtremConsole.error(e);
                    xtremConsole.error(
                        `Failed to filter dataset using in-memory database for ${this.screenId} - ${this.elementId}`,
                    );
                    xtremConsole.error('GraphQL filter:');
                    xtremConsole.error(JSON.stringify(filter, null, 4));
                    xtremConsole.error('LokiJS filter:');
                    xtremConsole.error(JSON.stringify(lokiJsFilter, null, 4));
                }
            }
        }
        return this;
    }

    public update({
        data,
        beforeUpdate = mappingNoop,
        afterUpdate = noop,
        cleanMetadata = true,
    }: Parameters<NormalizedCollection<T>['update']>[0]): InternalValue<T> {
        const updated = beforeUpdate(data);
        this.collection.update(updated);
        afterUpdate(updated);
        return cleanMetadata ? cleanMetadataFromRecord(updated) : updated;
    }

    public getIdPathToRecordByIdAndLevel({
        id,
        level,
    }: Parameters<NormalizedCollection<T>['getIdPathToRecordByIdAndLevel']>[0]): string[] {
        const record = this.findOne({ id, level, cleanMetadata: false });
        return this.getIdPathToRecordByRecord(record);
    }

    public getIdPathToRecordByRecord(startRecord: { _id: string; __parentId?: string; __level?: number }): string[] {
        const result: string[] = [];

        let record = startRecord;
        if (!record) {
            return [];
        }
        if (record._id) {
            result.push(record._id);
        }
        while (record?.__parentId) {
            // eslint-disable-next-line no-prototype-builtins
            record = record.hasOwnProperty('__level')
                ? this.findOne({ id: record.__parentId, level: record.__level! - 1, cleanMetadata: false })
                : this.findOne({ id: record.__parentId, cleanMetadata: false });
            if (record && record._id) {
                result.push(record._id);
            }
        }
        return result.reverse();
    }

    public createRecord({
        data,
        level,
        dirty = true,
        action = RecordActionType.ADDED,
        beforeCreate = mappingNoop,
        parentId,
        isUncommitted = false,
    }: Parameters<NormalizedCollection<T>['createRecord']>[0]): InternalValue<T> {
        const _id =
            !Object.prototype.hasOwnProperty.call(data, '_id') ||
            (data as ExtractEdgesPartial<T>)._id === null ||
            (data as ExtractEdgesPartial<T>)._id === undefined
                ? this.generateIndex()
                : String((data as ExtractEdgesPartial<T>)._id);
        const actualAction = action === 'none' ? undefined : action;
        const record = {
            ...(data && beforeCreate(cloneDeep(data))),
            _id,
            __dirty: dirty,
            // If we are creating an uncommitted row by cloning an existing one, we need to keep the dirty status of the columns
            __dirtyColumns: !isUncommitted
                ? new Set<string>()
                : (data as CollectionValueType).__dirtyColumns || new Set<string>(),
            ...(actualAction && { __action: actualAction }),
            ...(level !== undefined && level !== 0 && { __level: level }),
            ...(parentId !== undefined && { __parentId: parentId }),
            ...(isUncommitted && { __uncommitted: true }),
        } as InternalValue<T>;
        record.__compositeKey = this.getCompositeKey(record);
        record.__path = this.getPathProperty(record);

        return record;
    }

    public addRecord({
        data,
        level,
        cleanMetadata = true,
        dirty = true,
        action = RecordActionType.ADDED,
        beforeInsert = (d: any): any => d as any,
        afterInsert = noop,
        parentId,
        isUncommitted,
    }: Parameters<NormalizedCollection<T>['addRecord']>[0]): any {
        const record = this.createRecord({
            data,
            level,
            dirty,
            action,
            beforeCreate: beforeInsert,
            parentId,
            isUncommitted,
        });
        this.collection.add(record);
        if (action !== 'none') {
            afterInsert({ record: cloneDeep(record), action });
        }
        return cleanMetadata ? cleanMetadataFromRecord(cloneDeep(record)) : cloneDeep(record);
    }

    public skip(offset: Parameters<NormalizedCollection<T>['skip']>[0]): this {
        this.ensureResult();
        this.result = this.result!.offset(offset);
        return this;
    }

    public take(limit: Parameters<NormalizedCollection<T>['take']>[0]): this {
        this.ensureResult();
        this.result = this.result!.limit(limit);
        return this;
    }

    public getAncestorIds({ id, level }: Parameters<NormalizedCollection<T>['getAncestorIds']>[0]): string[] {
        return initial(this.getIdPathToRecordByIdAndLevel({ id, level }));
    }

    public value({
        cleanMetadata,
        cleanDatabaseMeta,
    }: {
        cleanMetadata: true;
        cleanDatabaseMeta?: boolean;
    }): PartialCollectionValueWithIds<T>[];
    public value({
        cleanMetadata,
        cleanDatabaseMeta,
    }: {
        cleanMetadata: false;
        cleanDatabaseMeta?: boolean;
    }): InternalValue<T>[];
    public value({
        cleanMetadata,
        cleanDatabaseMeta,
    }: {
        cleanMetadata?: boolean;
        cleanDatabaseMeta?: boolean;
    }): PartialCollectionValueWithIds<T>[];
    public value(): PartialCollectionValueWithIds<T>[];
    public value({
        cleanMetadata = true,
        cleanDatabaseMeta = true,
    }: Parameters<NormalizedCollection<T>['value']>[0] = {}): any[] {
        this.ensureResult();
        const data = this.result!.data({ removeMeta: cleanDatabaseMeta }).map(cloneDeep);
        const result = cleanMetadata ? data.map((r: any) => cleanMetadataFromRecord(r)) : data;
        this.result = null;
        return result;
    }

    public getValidationStateByColumn(levels: number): CollectionGlobalValidationState {
        return Array.from({ length: levels })
            .map((_, index) => index)
            .map(level => {
                return mergeWith(
                    {},
                    ...this.getAllInvalidRecords({ level, parentId: null, includePhantom: true })
                        .value({ cleanMetadata: false })
                        .map(r => r.__validationState),
                    (s = [], o: ValidationResult) => [...s, o],
                );
            });
    }

    public getAllServerRecordWithServerSideValidationIssues(): string[] {
        return (
            this.collection
                .chain()
                .find({
                    // Deleted or temporary records should not be validated
                    __action: { $nin: [RecordActionType.REMOVED, RecordActionType.TEMPORARY] },
                    __phantom: { $ne: true },
                })
                // Find items where there is a server side validation errors
                .where(
                    r =>
                        !!r.__validationState &&
                        Object.values(r.__validationState)
                            .map(v => v.validationRule)
                            .filter(k => k.startsWith(SERVER_VALIDATION_RULE_PREFIX)).length > 0,
                )
                .data()
                .map(r => r.__compositeKey!)
        );
    }

    public getInvalidLoadedRecords({
        level,
        parentId,
        withoutServerErrors,
        includePhantom = false,
    }: {
        level?: number | null;
        parentId?: string | null;
        withoutServerErrors?: boolean;
        includePhantom?: boolean;
    } = {}): this {
        return this.findAll({
            where: {
                __action: { $nin: [RecordActionType.REMOVED, RecordActionType.TEMPORARY] },
                __validationState: { $ne: undefined },
            },
            level,
            parentId,
            includePhantom,
        }).where(r => {
            const validationRules = objectKeys(r.__validationState || {}).map(
                k => r.__validationState![k].validationRule,
            );

            return (
                validationRules.length > 0 &&
                (!withoutServerErrors ||
                    validationRules.filter(vr => !vr.startsWith(SERVER_VALIDATION_RULE_PREFIX)).length > 0)
            );
        });
    }

    public getInvalidUnloadedRecords({
        level,
        parentId,
        withoutServerErrors,
        includePhantom = false,
    }: {
        level?: number | null;
        parentId?: string | null;
        withoutServerErrors?: boolean;
        includePhantom?: boolean;
    } = {}): this {
        return this.findAll({
            where: {
                __action: { $nin: [RecordActionType.REMOVED, RecordActionType.TEMPORARY] },
                __unloaded: { $eq: true },
            },
            level,
            parentId,
            includeUnloaded: true,
            includePhantom,
        }).where(
            r =>
                !!r.__validationState &&
                objectKeys(r.__validationState).length > 0 &&
                (!withoutServerErrors ||
                    objectKeys(r.__validationState).filter(k => k.indexOf(SERVER_VALIDATION_RULE_PREFIX) === 0).length >
                        0),
        );
    }

    public getAllInvalidRecords({
        level,
        parentId,
        includePhantom = false,
    }: { level?: number | null; parentId?: string | null; includePhantom?: boolean } = {}): this {
        return this.findAll({
            where: {
                __action: { $ne: RecordActionType.TEMPORARY },
            },
            level,
            parentId,
            includeUnloaded: true,
            includePhantom,
        }).where(r => !!r.__validationState && objectKeys(r.__validationState).length > 0);
    }

    public isCollectionValid(): boolean {
        return (
            this.collection
                .chain()
                // Deleted or temporary records should not be validated
                .find({
                    __action: { $nin: [RecordActionType.REMOVED, RecordActionType.TEMPORARY] },
                })
                .where(r => !!r.__validationState && objectKeys(r.__validationState).length > 0)
                .count() === 0
        );
    }

    public isCollectionDirty(): boolean {
        return (
            this.collection
                .chain()
                .find({ __dirty: { $eq: true } })
                .count() > 0
        );
    }

    public startRecordTransaction({
        recordId,
        recordLevel,
    }: Parameters<NormalizedCollection<T>['startRecordTransaction']>[0]): void {
        const result = this.collection.find({
            _id: { $eq: String(recordId) },
            __level: recordLevel === 0 ? { $in: [0, undefined] } : { $eq: recordLevel },
        });

        if (result.length === 0) {
            throw new Error('Cannot start transaction on a non-existing record');
        }
        if (result.length > 1) {
            throw new Error('Too many records found, maybe a transaction has already been started on the record');
        }

        const record = cloneDeep(result[0]);
        delete (record as any).$loki; // TODO: Set it correctly
        record.__uncommitted = true;
        this.collection.add(
            this.createRecord({
                data: record,
                isUncommitted: true,
                action: record.__action || RecordActionType.MODIFIED,
            }),
        );
    }

    // The return value indicates if the committed record was a new row.
    public commitRecord({ recordId, recordLevel }: Parameters<NormalizedCollection<T>['commitRecord']>[0]): boolean {
        const uncommittedRecord = this.collection.find({
            _id: { $eq: String(recordId) },
            __level: recordLevel === 0 ? { $in: [0, undefined] } : { $eq: recordLevel },
            __uncommitted: { $eq: true },
        });

        if (uncommittedRecord.length === 0) {
            throw new Error(`Cannot commit a record that is not in transaction ${recordId} ${recordLevel || ''}`);
        }
        if (uncommittedRecord.length > 1) {
            throw new Error('Too many records found, the dataset seems to be corrupted');
        }

        let updatedRecord;
        if (uncommittedRecord[0].__phantom) {
            // In case of a new record we don't need to look up the original record, given that doesn't exist.
            updatedRecord = {
                ...uncommittedRecord[0],
                __uncommitted: undefined,
                __phantom: undefined,
            };

            updatedRecord.__compositeKey = this.getCompositeKey(updatedRecord);
            updatedRecord.__path = this.getPathProperty(updatedRecord);
            this.collection.update(updatedRecord as unknown as InternalValue<T>);
            return true;
        }

        // For updates we need to merge the uncommitted changes on the top of the original record, then update the original record and remove the uncommitted working copy
        const originalRecord = this.collection.find({
            _id: { $eq: String(recordId) },
            __level: recordLevel === 0 ? { $in: [0, undefined] } : { $eq: recordLevel },
            __uncommitted: { $in: [false, undefined] },
        });

        if (originalRecord.length === 0) {
            throw new Error(`Cannot find original record of the transaction ${recordId} ${recordLevel || ''}`);
        }

        updatedRecord = deepMerge(originalRecord[0], uncommittedRecord[0], false, true);
        updatedRecord.$loki = originalRecord[0].$loki;
        updatedRecord.__uncommitted = undefined;
        updatedRecord.__compositeKey = originalRecord[0].__compositeKey;
        updatedRecord.__path = originalRecord[0].__path;

        this.collection.update(updatedRecord);

        // We need to remove the uncommitted working copy from the database because now it would be duplicated
        this.collection.remove(uncommittedRecord);
        return false;
    }

    public cancelRecordTransaction({
        recordId,
        recordLevel,
    }: Parameters<NormalizedCollection<T>['cancelRecordTransaction']>[0]): void {
        const uncommittedRecord = this.collection.find({
            _id: { $eq: String(recordId) },
            __level: recordLevel === 0 ? { $in: [0, undefined] } : { $eq: recordLevel },
            __uncommitted: { $eq: true },
        });

        if (uncommittedRecord.length === 0) {
            throw new Error(`Cannot cancel a record that is not in transaction ${recordId} ${recordLevel || ''}`);
        }
        if (uncommittedRecord.length > 1) {
            throw new Error('Too many records found, the dataset seems to be corrupted');
        }

        this.collection.remove(uncommittedRecord);
    }
}
