PATH: XTREEM/Client+Framework/Developer+API

## Introduction

The runtime developer API is a collection of functions that enable application developers to execute various actions on the screen, for example display dialogs, navigate to another page or show toasts.

## Basic example:

The developer API is available using the `$` object of any page or sticker.

```typescript
onClick() {
    this.$.dialog.confirmation('info', 'Question', 'Do you want to open a dialog?');
}
```

## Available APIs

-   **screenId**: Returns the ID of the screen that the function is called from.
-   **username**: Returns the unique ID (email address) of the user that is currently uses the application
-   **userCode**: The userCode can be used it the application has a username that is different from the login user.
-   **storage**: [The storage API](./Storage+API).
-   **loader**: [The loader API](./Loader+API).
-   **dialog**: [The dialog API](./Dialog+API).
-   **router**: [The router API](./Router+API).
-   **graph**: The graph client. This API object can be used to query the server-side GraphQL API or execute mutations manually.
-   **subscribeToEvent**: Enables subscription to server-sent websocket events. Accepts a `category` (string) as the first parameter and a callback function as the second parameter, which will be invoked when an event in the specified category is received.
-   **unsubscribeFromEvent**: Unsubscribes from a server-side WebSocket event. This method requires a `category` (string) as the first parameter and a callback function as the second parameter. The specified callback will be removed from the list of listeners for the given event category.
-   **showToast**: Displays a toast on the top of the screen. You can find more about toasts [here](./Toast+API).
-   **removeToasts**: Erases all currently displayed system and application code initiated toasts from the screen.
-   **values**: Values of the current page formatted for server consumption. Transient field values are excluded and collections values are mapped to a changeset. It is a heavy operation that serializes all values of the page. It should only be used when you need the value of most of the fields in a server friendly way. This property can also be reassigned, in this case the values should be formatted the same way.
-   **commitValueAndPropertyChanges**: Updates the screen with the component property and value changes. By default the framework batches up changes that affect the screen and redraws it after all functional callbacks were executed or the application waits for a promise. This function forces the rendering engine to apply the changes immediately.
-   **finish**: Closes the current page or sticker. In case pages, when this function is called, the user is directed back to the home screen. In case of a page dialog, this function closes the dialog and the application returns to the screen that the user used before. When an argument is passed to the `finish` function, it is used as the resolution value of the page dialog promise, this allows communication between the dialog and the parent page.

-   **fetchDefaults([ fieldIds ], skipSet)**: Force re-fetches default values. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **refreshNavigationPanel**: Dispatches refreshNavigationPanel action. In this way Navigation Panel can be refreshed manually. It's optional to pass in a boolean argument when calling the function, eg: this.$.refreshNavigationPanel(true), this will consider if the navigation panel should get a new item when refreshing or not.
-   **processServerErrors**: Parses server side `ClientError` errors. It also allocates the error messages in the object to the corresponding fields.
-   **recordId**: A lightweight operation to get the record ID of the current page, it short-cut to `queryParameters._id` value.
-   **isEditingRecordDuplicate**: Whether the user is currently in duplication mode.
-   **isServiceOptionEnabled(serviceOptionName)**: Checks if the provided as argument service option is enabled, if it doesnt exist it will return false.
