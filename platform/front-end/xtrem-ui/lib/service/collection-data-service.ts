import type { ClientNode } from '@sage/xtrem-client';
import { Decimal } from '@sage/xtrem-decimal';
import type { Dict, FiltrableType } from '@sage/xtrem-shared';
import { deepMerge, flat, objectKeys } from '@sage/xtrem-shared';
import type { CancelToken } from 'axios';
import {
    get,
    intersection,
    isArray,
    isEmpty,
    isEqual,
    isNil,
    isPlainObject,
    omitBy,
    partition,
    set,
    setWith,
    sortBy,
    uniq,
} from 'lodash';
import type { Unsubscribe } from 'redux';
import { navigationPanelId } from '../component/container/navigation-panel/navigation-panel-types';
import type {
    OptionsMenuItem,
    PageProperties,
    TableOptionsMenuItem,
    TableProperties,
    TreeProperties,
} from '../component/control-objects';
import type { PageDecoratorProperties } from '../component/decorators';
import type { PropertyValueType } from '../component/field/reference/reference-types';
import type { ServerRecordMapperFunction } from '../component/field/traits';
import type { NestedField, NestedFieldTypes } from '../component/nested-fields';
import type { NestedReferenceProperties } from '../component/nested-fields-properties';
import type { OrderByType, PartialCollectionValue, PartialCollectionValueWithIds } from '../component/types';
import { FieldKey } from '../component/types';
import type { TableValidationError } from '../component/ui/nested-field-errors-component';
import { getStore } from '../redux';
import type { NodePropertyType } from '../types';
import { GraphQLTypes } from '../types';
import { xtremConsole } from '../utils/console';
import {
    DEEP_BIND_QUERY_ALIAS_GLUE,
    ELEMENT_ID_APPLICATION_CODE_LOOKUP,
    SERVER_VALIDATION_RULE_PREFIX,
} from '../utils/constants';
import { getIsoDate } from '../utils/formatters';
import { transformToLokiJsFilter } from '../utils/lokijs-filter-transformer';
import { convertDeepBindToPath, getNestedFieldsFromProperties } from '../utils/nested-field-utils';
import { findDeepPropertyDetails } from '../utils/node-utils';
import { resolveByValue } from '../utils/resolve-value-utils';
import { findNestedFieldProperties } from '../utils/server-error-transformer';
import { getPageDefinitionFromState, getPagePropertiesFromPageDefinition } from '../utils/state-utils';
import { cleanMetadataFromRecord, schemaTypeNameFromNodeName, splitValueToMergedValue } from '../utils/transformers';
import type { MakeRequired } from '../utils/types';
import { isDevMode } from '../utils/window';
import type {
    AggregationMethod,
    CollectionGlobalValidationState,
    CollectionValueFieldPropertiesWithAdditionalRecords,
    CollectionValueType,
    FetchNestedGridArgs,
    InternalValue,
    NormalizedCollection,
    ValidityChangeSubscriptionCallback,
    ValueChangeSubscriptionCallback,
} from './collection-data-types';
import { CollectionFieldTypes, RecordActionType } from './collection-data-types';
import {
    CollectionDataRow,
    getGroupFilterValue,
    getGroupKey,
    transformFilterForCollectionValue,
    type AggFunc,
} from './collection-data-utils';
import {
    dispatchCloseSideBar,
    dispatchFieldValidation,
    dispatchSetFieldDirty,
    dispatchUpdateNestedFieldValidationErrorsForRecord,
} from './dispatch-service';
import type { Filter } from './filter-service';
import { getGraphQLFilter, getTypedNestedFields, mergeGraphQLFilters } from './filter-service';
import { buildSearchBoxFilter, convertFilterDecoratorToGraphQLFilter } from './graphql-query-builder';
import {
    fetchCollectionData,
    fetchCollectionRecord,
    fetchNestedDefaultValues,
    fetchReferenceFieldData,
} from './graphql-service';
import type { GraphQLFilter, QueryArguments } from './graphql-utils';
import { removeEdgesDeep } from './graphql-utils';
import { LokiDb } from './loki';
import type { FormattedNodeDetails } from './metadata-types';
import type { Page } from './page';
import type { PageDefinition } from './page-definition';
import type { ScreenBase } from './screen-base';
import type { ValidationResult } from './screen-base-definition';
import { checkNestedRecordValidationStatus } from './validation-service';
import { formatCollectionItem } from './value-formatter-service';

export interface GridLodable extends Omit<CollectionValueFieldPropertiesWithAdditionalRecords, 'selectedRecords'> {}

interface GetPageParams<T extends ClientNode = any> {
    axiosCancelToken?: CancelToken;
    cleanMetadata?: boolean;
    cursor?: string;
    fetchPageSize?: number;
    filters?: Filter<FiltrableType>[];
    group?: { key: string; value?: string; aggFunc?: AggFunc; type: FieldKey };
    groups?: any;
    level?: number;
    orderBy?: OrderByType;
    pageNumber?: number;
    pageSize?: number;
    parentId?: string;
    rawFilter?: GraphQLFilter;
    searchText?: string;
    selectedOptionsMenuItem?: TableOptionsMenuItem<T>;
    tableFieldProperties: GridLodable;
    totalCount?: boolean;
}

interface GetFetchServerRecordsOptionsParams<T extends ClientNode = any> {
    filters: Filter<FiltrableType>[];
    group?: { key: string; value?: string };
    level?: number;
    orderBy?: OrderByType;
    parentId?: string;
    rawFilter?: GraphQLFilter;
    searchText?: string;
    selectedOptionsMenuItem?: TableOptionsMenuItem<T>;
    tableFieldProperties: GridLodable;
}

interface GetFilteredSortedAndTemporaryRecordsParams {
    cleanMetadata?: boolean;
    group?: { key: string; value?: string; type: FieldKey };
    level?: number;
    pageNumber?: number;
    pageSize?: number;
    parentId?: string;
    tableFieldProperties: GridLodable;
}

export class CollectionValue<T extends ClientNode = any> {
    protected initialFilter: Array<GraphQLFilter<T> | undefined> = [];

    protected filter: Array<Record<number, GraphQLFilter<T>>> = [];

    /**
     * Stores a filter snapshot whenever a search text is combined with
     * active filters (i.e. nav panel in split view)
     * so that it can be later restored by calling "restoreFilterSnapshot"
     */
    private readonly filterSnapshot: Array<Record<number, GraphQLFilter<T>>> = [];

    protected forceRefetch = false;

    protected lastFetchedPage: Array<Record<number, number>> = [];

    protected orderBy: Array<Record<number, OrderByType>> = [];

    protected initialOrderBy: Array<OrderByType | undefined> = [];

    protected activeOptionsMenuItem?: TableOptionsMenuItem<T>;

    protected readonly columnDefinitions: Array<NestedField<ScreenBase, NestedFieldTypes>[]>;

    protected readonly db: LokiDb<T>;

    protected readonly bind?: PropertyValueType | string;

    public readonly elementId: string;

    protected readonly fieldType?: CollectionFieldTypes;

    protected readonly isTransient: boolean;

    protected readonly isNoServerLookups: boolean;

    protected readonly levelMap?: Record<number, (keyof T & string) | undefined>;

    protected readonly nodeTypes: Dict<FormattedNodeDetails>;

    protected readonly parentElementId?: string;

    protected readonly recordContext?: Dict<any>;

    public readonly screenId: string;

    protected readonly locale: string;

    protected readonly valueChangeSubscribers: ValueChangeSubscriptionCallback[] = [];

    protected readonly valueChangeUncommittedSubscribers: ValueChangeSubscriptionCallback[] = [];

    protected readonly validityChangeSubscribers: ValidityChangeSubscriptionCallback[] = [];

    protected readonly validityChangeUncommittedSubscribers: ValidityChangeSubscriptionCallback[] = [];

    protected readonly mapServerRecordFunctions?: (ServerRecordMapperFunction | undefined)[];

    protected readonly sublevelProperty?: string | null;

    public hasNextPage: boolean;

    public readonly initialValues: PartialCollectionValue<T>[] = [];

    public readonly nodes: string[];

    public readonly contextNode?: string;

    /** Level which is used when the collection value is in a lookup dialog that belongs to a nested grid */
    public readonly referenceLookupContextLevel?: number;

    private creatingNewPhantomRowPromise: Promise<PartialCollectionValueWithIds<T>> | null = null;

    private unCommittedCreatingNewPhantomRowPromise: Promise<PartialCollectionValueWithIds<T>> | null = null;

    private readonly forbiddenKeys: (keyof CollectionValueType | '$loki' | string)[] = [
        '__aggFunc',
        '__level',
        '__dirtyColumns',
        '__forceRowUpdate',
        '__validationState',
        '__compositeKey',
        '__unloaded',
        '__action',
        '__isGroup',
        '__phantom',
        '$loki',
    ];

    constructor({
        activeOptionsMenuItem,
        bind,
        columnDefinitions,
        contextNode,
        dbKey,
        elementId,
        fieldType,
        filter = [],
        hasNextPage,
        initialValues = [],
        isNoServerLookups,
        isTransient,
        levelMap,
        locale = getStore().getState().applicationContext?.locale || 'en-US',
        mapServerRecordFunctions,
        nodes,
        nodeTypes = getStore().getState().nodeTypes,
        orderBy = [],
        parentElementId,
        recordContext,
        screenId,
        referenceLookupContextLevel,
        sublevelProperty,
    }: {
        activeOptionsMenuItem?: TableOptionsMenuItem<T>;
        bind?: PropertyValueType | string;
        columnDefinitions: Array<NestedField<ScreenBase, NestedFieldTypes, T>[]>;
        contextNode?: NodePropertyType;
        dbKey?: string;
        elementId: string;
        fieldType?: CollectionFieldTypes;
        filter?: Array<GraphQLFilter<T> | undefined>;
        hasNextPage: boolean;
        initialValues: PartialCollectionValue<T>[];
        isNoServerLookups?: boolean;
        isTransient: boolean;
        levelMap?: Record<number, (keyof T & string) | undefined>;
        locale?: string;
        mapServerRecordFunctions?: (ServerRecordMapperFunction | undefined)[];
        nodes: string[];
        nodeTypes?: Dict<FormattedNodeDetails>;
        orderBy?: Array<OrderByType | undefined>;
        parentElementId?: string;
        recordContext?: Dict<any>;
        referenceLookupContextLevel?: number;
        screenId: string;
        sublevelProperty?: string | null;
    }) {
        this.activeOptionsMenuItem = activeOptionsMenuItem || undefined;
        this.bind = bind || undefined;
        this.columnDefinitions = columnDefinitions as Array<NestedField<ScreenBase, NestedFieldTypes>[]>;
        this.elementId = elementId;
        this.fieldType = fieldType;
        this.locale = locale;
        this.initialFilter = Array.from(Array(nodes.length).keys()).map(i => filter[i]);
        setWith(this.filter, [0, 0], filter?.[0], Object);
        this.hasNextPage = hasNextPage;
        this.initialValues = initialValues;
        this.isTransient = isTransient;
        this.isNoServerLookups = !!isNoServerLookups;
        this.levelMap = levelMap;
        this.contextNode = contextNode ? String(contextNode) : undefined;
        this.nodes = nodes;
        this.nodeTypes = nodeTypes;
        this.initialOrderBy = Array.from(Array(nodes.length).keys()).map(i => orderBy[i]);
        setWith(this.orderBy, [0, 0], orderBy?.[0], Object);
        setWith(this.lastFetchedPage, [0, 0], 0, Object);
        this.parentElementId = parentElementId;
        this.recordContext = recordContext;
        this.screenId = screenId;
        this.mapServerRecordFunctions = mapServerRecordFunctions;
        this.referenceLookupContextLevel = referenceLookupContextLevel;
        this.sublevelProperty = sublevelProperty;

        this.db = new LokiDb<T>({
            dbKey,
            elementId: this.elementId,
            initialValues: [],
            isTransient: this.isTransient,
            screenId: this.screenId,
        });

        this.initialValues.forEach(recordData => {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            const { [this.levelMap?.[0] ?? '']: _, ...data } = recordData;
            const newRecord = this.db.addRecord({
                data: data as PartialCollectionValue<T>,
                level: 0,
                beforeInsert: record => this.formatRecordValue(this.repackRecordData(record), undefined, true, 0),
                cleanMetadata: false,
                action: 'none',
                dirty: false,
            });

            this.addNestedLevelRecursive({
                recordData: recordData as any,
                level: 0,
                shouldNotifySubscribers: false,
                parentId: newRecord._id,
                action: 'none',
                dirty: false,
            });
        });
    }

    public async getPhantomRecords({
        level,
        parentId,
    }: {
        level?: number;
        parentId?: string;
    } = {}): Promise<PartialCollectionValueWithIds<T>[]> {
        const records = this.db
            .findAll({ level, parentId, where: { __phantom: { $eq: true } } })
            .value({ cleanMetadata: false });
        if (records.length > 0) {
            return records;
        }
        const newRecord = await this.createNewPhantomRow({
            level,
            parentId,
            shouldNotifySubscribers: false,
            cleanMetadata: false,
        });
        return [...records, newRecord];
    }

    public hasDirtyPhantomRows(): boolean {
        const records = this.db.findAll({ where: { __phantom: { $eq: true }, __dirty: { $eq: true } } }).value();

        return records.length !== 0;
    }

    public calculateAggregatedValue({
        aggregationMethod,
        aggregationKey,
        level = 0,
        parentId,
        where = transformToLokiJsFilter(this.getInternalFilter({ level: level || 0, parentId: parentId || undefined })),
        includeUnloaded,
        includePhantom,
        isUncommitted,
    }: {
        aggregationMethod: AggregationMethod;
        aggregationKey: string;
        level?: number | null;
        parentId?: string | null;
        where?: LokiQuery<InternalValue<T> & LokiObj>;
        includeUnloaded?: boolean;
        includePhantom?: boolean;
        isUncommitted?: boolean;
        cleanMetadata?: boolean;
    }): number {
        if (this.hasNextPage) {
            return Number.NaN;
        }
        return this.db.calculateAggregatedValue({
            aggregationMethod,
            aggregationKey,
            level,
            parentId,
            where,
            includeUnloaded,
            includePhantom,
            isUncommitted,
        });
    }

    public async getDefaultPhantomRow({ level = 0 }: { level?: number } = {}): Promise<any> {
        const fieldProperties = getStore().getState().screenDefinitions[this.screenId].metadata.uiComponentProperties[
            this.elementId
        ] as TableProperties;
        const emptyRecord = this.getColumnDefinitions(level).reduce((acc, colDef) => {
            const bind = String(colDef.properties.bind);
            if (bind !== '_id') {
                acc[bind] = null;
            }
            return acc;
        }, {} as Dict<any>);

        const { _id: discardedId, ...record }: Dict<any> = fieldProperties.isTransient
            ? emptyRecord
            : (
                  await fetchNestedDefaultValues({
                      screenId: this.screenId,
                      elementId: this.elementId,
                      data: { __phantom: true },
                  })
              ).nestedDefaults || emptyRecord;
        return {
            ...record,
            __phantom: true,
            __dirtyColumns: new Set(),
        };
    }

    public async resetRowToDefaults({
        id,
        level,
        parentId,
        shouldNotifySubscribers = true,
        isOrganicChange = false,
        resetDirtiness = false,
    }: {
        id: string;
        level?: number;
        parentId?: string;
        shouldNotifySubscribers?: boolean;
        isOrganicChange?: boolean;
        resetDirtiness?: boolean;
    }): Promise<void> {
        const record = this.db.findOne({ id, level, parentId, cleanMetadata: false });
        const defaults = splitValueToMergedValue(await this.getDefaultPhantomRow());
        const updatedData: InternalValue<T> = { ...record, ...defaults, __validationState: {} };
        if (resetDirtiness) {
            updatedData.__dirty = false;
            updatedData.__dirtyColumns = new Set();
        }
        this.db.update({
            data: updatedData,
            beforeUpdate: toBeUpdated =>
                this.formatRecordValue(this.repackRecordData(toBeUpdated), undefined, true, level),
            afterUpdate: () => {
                if (shouldNotifySubscribers) {
                    this.notifyValueChangeSubscribers(RecordActionType.MODIFIED, updatedData);
                }
                if (isOrganicChange) {
                    this.notifyValidationSubscribers([updatedData]);
                    dispatchUpdateNestedFieldValidationErrorsForRecord({
                        screenId: this.screenId,
                        elementId: this.elementId,
                        recordId: updatedData._id,
                        level,
                    });
                }
            },
        });
    }

    public async createNewPhantomRow({
        level,
        parentId,
        shouldNotifySubscribers = true,
        cleanMetadata = true,
        isUncommitted = false,
    }: {
        level?: number;
        parentId?: string;
        shouldNotifySubscribers?: boolean;
        cleanMetadata?: boolean;
        isUncommitted?: boolean;
    } = {}): Promise<PartialCollectionValueWithIds<T>> {
        /**
         * If a getDefaults call take very long, this function might be called again while the defaults are being fetched from the server.
         * We should not execute two default calls at once, first its bad server performance, second it can result two phantom rows in the collection
         * value which cause all sort of problems.
         *  */
        // Determine which promise variable to use based on isUncommitted
        const promiseKey = isUncommitted ? 'unCommittedCreatingNewPhantomRowPromise' : 'creatingNewPhantomRowPromise';

        if (!this[promiseKey]) {
            this[promiseKey] = new Promise<PartialCollectionValueWithIds<T>>(resolve =>
                this.getDefaultPhantomRow().then(recordData => {
                    const undeletedPhantomRow = this.db.findOne({
                        cleanMetadata: false,
                        level,
                        parentId,
                        where: { __phantom: { $eq: true } },
                    });

                    // If there is a phantom row, we should remove it from the db.
                    if (undeletedPhantomRow) {
                        this.db.remove({ data: undeletedPhantomRow });
                    }

                    const record = this.addRecord({
                        recordData,
                        level,
                        shouldNotifySubscribers,
                        parentId,
                        cleanMetadata,
                        isUncommitted,
                        dirty: false,
                    });

                    resolve(record);
                    this[promiseKey] = null; // Reset the appropriate promise variable after completion
                }),
            );
        }

        return this[promiseKey]!;
    }

    public async resetPhantomRowToDefault(): Promise<void> {
        const recordData = await this.getDefaultPhantomRow();
        const oldRecord = this.db.findOne({ where: { __phantom: { $eq: true } }, cleanMetadata: false });
        const updatedData = { ...oldRecord, ...recordData };
        if (!oldRecord) {
            this.db.addRecord({
                data: recordData,
                dirty: false,
                afterInsert: () => {
                    recordData.__forceRowUpdate = true; // We only want to tell to desktop table to force reload the row but not save in db for next uses.
                    this.notifyValueChangeSubscribers(RecordActionType.ADDED, recordData as InternalValue<T>);
                },
            });
        } else {
            this.db.update({
                data: updatedData,
                afterUpdate: () => {
                    updatedData.__forceRowUpdate = true; // We only want to tell to desktop table to force reload the row but not save in db for next uses.
                    this.notifyValueChangeSubscribers(RecordActionType.MODIFIED, updatedData as InternalValue<T>);
                },
            });
        }
    }

    public createRecord(args: Parameters<NormalizedCollection<T>['createRecord']>[0]): InternalValue<T> {
        return this.db.createRecord(args);
    }

    public commitPhantomRow({
        id,
        level,
        parentId,
        shouldNotifySubscribers = true,
    }: {
        id: string;
        level?: number;
        parentId?: string;
        shouldNotifySubscribers?: boolean;
    }): void {
        const record = this.db.findOne({ id, level, parentId, cleanMetadata: false });
        // eslint-disable-next-line @typescript-eslint/naming-convention
        const { __phantom: _, ...updatedData } = record;
        this.db.update({
            data: updatedData as InternalValue<T>,
            beforeUpdate: toBeUpdated =>
                this.formatRecordValue(this.repackRecordData(toBeUpdated), undefined, true, level),
            afterUpdate: () => {
                if (shouldNotifySubscribers) {
                    this.notifyValueChangeSubscribers(RecordActionType.ADDED, updatedData as InternalValue<T>);
                }
            },
        });
    }

    protected getTreeFromNormalizedCollection({
        records,
        includeActions = true,
        excludeEmptyChildCollections = false,
        isRequestingDefaults = false,
        removeNegativeId = false,
        cleanInputTypes = true,
    }: {
        records: InternalValue<T>[];
        includeActions?: boolean;
        excludeEmptyChildCollections?: boolean;
        isRequestingDefaults?: boolean;
        removeNegativeId?: boolean;
        cleanInputTypes?: boolean;
    }): any[] {
        if (records.length === 0) {
            return [];
        }
        if (this.levelMap === undefined) {
            throw new Error('Missing level information!');
        }
        const levelLength = objectKeys(this.levelMap).length;
        /**
         * full path of IDs to all records
         * e.g.
         * 1
         * ├── -1
         * └── 1
         *     ├── 2
         *     └── 3
         * becomes:
         * [
         *   [1]
         *   [1,-1],
         *   [1,1],
         *   [1,1,2],
         *   [1,1,3],
         * ]
         */
        const paths = records.map(element => {
            return this.db.getIdPathToRecordByIdAndLevel({ id: element._id, level: element.__level });
        });
        /**
         * unique IDs by level with new items at the end
         * e.g.
         *
         * [
         *   [1],
         *   [1,-1],
         *   [3,2],
         * ]
         *
         */
        const uniqueIdsByLevel = Array.from({ length: levelLength }, (_, i) => i).reduce<string[][]>((acc, i) => {
            acc[i] = sortBy(uniq(paths.map(path => path[i]).filter(Boolean))).reverse();
            return acc;
        }, []);
        // cache for explored records (cache key = <record.__compositeKey>)
        const explored: any = {};
        // cache for record's children (cache key = <record_id>.<level> )
        const deps: Record<string, string[]> = {};
        const tree: any[] = [];
        // process 'uniqueIdsByLevel' backwards, i.e. start with more nested records
        Array.from({ length: levelLength }, (_, i) => levelLength - i - 1).forEach(i => {
            const ids = uniqueIdsByLevel[i];
            ids.forEach(id => {
                const level = i === 0 ? undefined : i;
                const element = this.db.findOne({ id, level, cleanMetadata: false })!;
                if (element.__compositeKey) {
                    if (i > 0) {
                        deps[`${element.__parentId}.${i - 1}`] = [
                            ...(deps[`${element.__parentId}.${i - 1}`] || []),
                            element.__compositeKey,
                        ];
                    }

                    explored[element.__compositeKey] = element;
                    const childProperty = this.levelMap && this.levelMap[i];
                    // add all children
                    if (childProperty !== undefined) {
                        const children = deps[element.__compositeKey] || [];
                        // do not set empty child collections so that the server doesn't override any previous value
                        if (!excludeEmptyChildCollections || children.length !== 0) {
                            set(
                                element,
                                childProperty,
                                children.map(dep => {
                                    return new CollectionDataRow<T>(
                                        explored[dep],
                                        this.columnDefinitions,
                                        this.nodes,
                                        this.nodeTypes,
                                        {
                                            includeActions,
                                            removeNegativeId,
                                            isRequestingDefaults,
                                            cleanInputTypes,
                                            isTree: !!this.sublevelProperty,
                                        },
                                    ).getChangeset();
                                }),
                            );
                        }
                    }
                }

                if (i === 0) {
                    const topLevelRecord = new CollectionDataRow<T>(
                        element,
                        this.columnDefinitions,
                        this.nodes,
                        this.nodeTypes,
                        {
                            includeActions,
                            removeNegativeId,
                            isRequestingDefaults,
                            cleanInputTypes,
                            isTree: !!this.sublevelProperty,
                        },
                    ).getChangeset();
                    // add only top-level records to tree
                    tree.push(topLevelRecord);
                }
            });
        });
        return tree;
    }

    protected formatRecordValue = (
        collectionItem: Partial<InternalValue<T>>,
        previousRecord?: Partial<InternalValue<T>>,
        shouldCallRemapServer = false,
        level = 0,
    ): any => {
        const nodeIndex = collectionItem.__level ?? level;
        const formattedValue = formatCollectionItem<T>({
            screenId: this.screenId,
            nodeTypes: this.nodeTypes,
            collectionItem,
            columnsDefinitions: this.getColumnDefinitions(nodeIndex) as NestedField<ScreenBase, NestedFieldTypes, T>[],
            parentNode: this.getNode(nodeIndex),
            contextRow: previousRecord,
        });

        if (shouldCallRemapServer && this.mapServerRecordFunctions && this.mapServerRecordFunctions[nodeIndex]) {
            const mapFunction = this.mapServerRecordFunctions[nodeIndex];
            const clearRecord = cleanMetadataFromRecord(formattedValue);
            const remappedRecord = resolveByValue({
                propertyValue: mapFunction,
                screenId: this.screenId,
                rowValue: null,
                fieldValue: clearRecord,
                skipHexFormat: true,
            });
            const modifiedMetadataProperties = intersection(objectKeys(remappedRecord), this.forbiddenKeys);
            if (modifiedMetadataProperties.length > 0) {
                throw new Error(
                    `The remap function is not allowed to modify metadata properties. Modified properties: ${modifiedMetadataProperties.join(
                        ', ',
                    )}`,
                );
            }

            if (remappedRecord._id !== formattedValue._id) {
                throw new Error('The remap function is not allowed to modify the _id property.');
            }

            return { ...formattedValue, ...remappedRecord };
        }

        if (this.sublevelProperty && this.fieldType === CollectionFieldTypes.TREE) {
            const childRecordCount = get(collectionItem, `${this.sublevelProperty}.query.totalCount`, 0);
            (formattedValue as any).__isGroup = childRecordCount > 0;
        }

        return formattedValue;
    };

    public getOrderBy({ level, parentId }: { level?: number | null; parentId?: string | null }): OrderByType {
        if (this.sublevelProperty) {
            return isEmpty(this.initialOrderBy[0]) ? { _id: -1 } : this.initialOrderBy[0];
        }

        const initialOrderBy = this.initialOrderBy[level ?? 0] as OrderByType;
        const fallbackOrderBy: OrderByType = isEmpty(initialOrderBy) ? { _id: -1 } : initialOrderBy;
        const storedOrderBy = get(this.orderBy, [level ?? 0, parentId ?? 0]);
        return isEmpty(storedOrderBy) ? fallbackOrderBy : storedOrderBy;
    }

    public getFilter({ level, parentId }: { level: number; parentId?: string | null }): Filter<any> {
        let filter: Filter<any> = get(
            this.filter,
            [this.sublevelProperty ? 0 : level, parentId ?? 0],
            this.initialFilter[this.sublevelProperty ? 0 : level],
        );
        if (!level && this.activeOptionsMenuItem && filter) {
            filter = mergeGraphQLFilters<any>([filter, this.activeOptionsMenuItem.graphQLFilter]);
        } else if (!level && this.activeOptionsMenuItem?.graphQLFilter) {
            filter = this.activeOptionsMenuItem.graphQLFilter as Filter<any>;
        }
        return omitBy(filter, v => v === undefined) as Filter<any>;
    }

    private getInternalFilter({ level, parentId }: { level: number; parentId?: string | null }): Filter<any> {
        return transformFilterForCollectionValue(omitBy(this.getFilter({ level, parentId }), v => v === undefined));
    }

    public resetFilter(): void {
        setWith(this.filter, [0, 0], this.initialFilter?.[0], Object);
    }

    public async fetchInvalidUnloadedRecords(): Promise<void> {
        const idsToFetch = this.getInvalidUnloadedRecords().map(r => r._id);
        const pageDefinition = getPageDefinitionFromState(this.screenId);
        const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);
        const idFilter = { _id: { _in: idsToFetch } };
        const result = await fetchCollectionData({
            screenDefinition: pageDefinition,
            rootNode: pageProperties.node as string,
            rootNodeId: pageDefinition.selectedRecordId || '',
            elementId: this.elementId,
            nestedFields: this.getColumnDefinitions(0),
            queryArguments: { filter: JSON.stringify(idFilter) },
            bind: this.bind,
            nodeTypes: this.nodeTypes,
            node: this.getNode(0),
        });
        this.addQueryResultToCollection({
            result,
            level: 0,
            markAsAdded: false,
        });
    }

    private isReferenceField(
        fieldProperties: GridLodable,
        pageProperties: PageProperties & PageDecoratorProperties<Page>,
    ): boolean {
        // If it has valueField its a referenceField so we know for sure it's okay

        if (
            fieldProperties._controlObjectType === FieldKey.Reference ||
            fieldProperties._controlObjectType === FieldKey.Pod ||
            fieldProperties._controlObjectType === FieldKey.FilterSelect ||
            Object.prototype.hasOwnProperty.call(fieldProperties, 'valueField')
        ) {
            return true;
        }

        if (
            fieldProperties._controlObjectType === FieldKey.Table ||
            fieldProperties._controlObjectType === FieldKey.Tree ||
            fieldProperties._controlObjectType === FieldKey.Calendar ||
            fieldProperties._controlObjectType === FieldKey.MultiFileDeposit ||
            fieldProperties._controlObjectType === FieldKey.NestedGrid
        ) {
            return false;
        }

        // If not we need to check if we are checking some reference from other fields like pod
        // If the nodeType of the property is a Object and it DONT start with a underscore it means that its a reference to other node.
        const propertyType = findDeepPropertyDetails(
            schemaTypeNameFromNodeName(pageProperties?.node),
            this.bind || this.elementId,
            this.nodeTypes,
        );
        return propertyType?.kind === 'OBJECT' && !propertyType?.type.startsWith('_');
    }

    protected async fetchServerRecords({
        pageDefinition,
        pageSize,
        tableFieldProperties,
        group,
        axiosCancelToken,
        level = 0,
        parentId,
        cursor,
        totalCount = false,
    }: {
        pageDefinition: PageDefinition;
        tableFieldProperties: GridLodable;
        pageSize: number;
        group?: { key: string; value?: string; aggFunc?: AggFunc; type: FieldKey };
        axiosCancelToken?: CancelToken;
        level?: number;
        parentId?: string;
        cursor?: string;
        totalCount?: boolean;
    }): Promise<any> {
        if (this.isNoServerLookups) {
            return {};
        }

        const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);

        const selectedRecordId = parentId || pageDefinition.selectedRecordId || '';

        const queryArguments: QueryArguments = {
            first: pageSize,
        };
        const sortOrder = this.getOrderBy({ level, parentId });
        let orderBy = sortOrder;
        if (group !== undefined && group?.value === undefined) {
            // eslint-disable-next-line @sage/redos/no-vulnerable
            const nestedValueMatch = group.key.match(/(.*)\./);

            orderBy = deepMerge(
                // always order by id of nested references
                group?.type === FieldKey.Reference && nestedValueMatch
                    ? set(set({}, group.key, 1), `${nestedValueMatch[1]}._id`, 1)
                    : {},
                // for groups add only sort conditions on the grouping column
                objectKeys(flat(sortOrder))
                    .filter(k => k.startsWith(group.key))
                    .reduce((acc, curr) => {
                        set(acc, curr, get(sortOrder, curr));
                        return acc;
                    }, {}),
            );
        }
        if (orderBy && !isEmpty(orderBy)) {
            queryArguments.orderBy = JSON.stringify(orderBy);
        }
        let filter = this.getFilter({ level, parentId });
        if (filter || group !== undefined) {
            const groupFilter = group ? getGroupFilterValue({ group, mode: 'server' }) : {};
            filter = deepMerge<Filter<any>>(filter, groupFilter);
            queryArguments.filter = JSON.stringify(filter);
        }
        if (cursor) {
            queryArguments.after = cursor;
        }
        let graphqlQueryResult;
        const parentLevel =
            level > 0
                ? (pageDefinition.metadata.uiComponentProperties[this.elementId] as any)?.levels?.[level - 1]
                : undefined;
        const rootNode = parentLevel ? String(parentLevel.node) : (pageProperties.node as string);
        const rootNodeId = parentLevel ? parentLevel.childProperty : this.elementId;
        const bind = parentLevel ? undefined : convertDeepBindToPath(tableFieldProperties.bind || this.elementId);
        const targetProperty = findDeepPropertyDetails(
            schemaTypeNameFromNodeName(rootNode),
            bind,
            this.nodeTypes,
            false,
        );
        /**
         * A table is considered transient if the field is transient OR the page that the field is on is transient OR
         * the table doesn't belong to any node OR has not been saved to the server yet and we cannot query it OR it is
         * bound to a JSON deep nested array.
         *  */
        const isTransient =
            !selectedRecordId ||
            this.isTransient ||
            pageProperties.isTransient ||
            !pageProperties.node ||
            !this.nodes.length ||
            targetProperty?.type === GraphQLTypes.Json;

        if (
            this.isReferenceField(tableFieldProperties, pageProperties) ||
            this.elementId === navigationPanelId ||
            this.elementId === ELEMENT_ID_APPLICATION_CODE_LOOKUP
        ) {
            const referenceFieldProperties = tableFieldProperties as NestedReferenceProperties;
            const data = await fetchReferenceFieldData({
                fieldProperties: referenceFieldProperties,
                screenId: this.screenId,
                elementId: this.elementId,
                filter,
                orderBy,
                after: queryArguments.after,
                valueField: referenceFieldProperties.valueField || this.elementId,
                parentElementId: this.parentElementId,
                recordContext: this.recordContext,
                contextNode: this.contextNode,
                axiosCancelToken,
                level: this.referenceLookupContextLevel,
                group,
                pageSize,
            });
            const result = removeEdgesDeep(data, true, true);
            const dataAccessor = result?.lookups ? `lookups.${tableFieldProperties?.bind}` : 'query';
            graphqlQueryResult = get(result, dataAccessor);
        } else if (!isTransient) {
            const treeProperties = parentLevel ? undefined : (tableFieldProperties as TreeProperties<Page>);
            graphqlQueryResult = await fetchCollectionData({
                screenDefinition: pageDefinition,
                rootNode,
                rootNodeId: selectedRecordId,
                elementId: rootNodeId,
                nestedFields: getNestedFieldsFromProperties(tableFieldProperties),
                queryArguments,
                bind: bind || undefined,
                group,
                treeProperties,
                totalCount,
                nodeTypes: this.nodeTypes,
                node: this.getNode(level),
            });
        }
        this.addQueryResultToCollection({ result: graphqlQueryResult, level, parentId, markAsAdded: false, group });
        return graphqlQueryResult;
    }

    /** Compacts query results with __ aliases into a single object for better rendering and data management purposes */
    public repackRecordData = (
        rv: PartialCollectionValue<T> | Omit<PartialCollectionValue<T>, '_id'> | Partial<InternalValue<T>>,
    ): InternalValue<T> => {
        // We don't change primitive values
        const recordValue = rv as any;
        if (
            !recordValue ||
            typeof recordValue === 'boolean' ||
            typeof recordValue === 'string' ||
            typeof recordValue === 'number'
        ) {
            return recordValue;
        }
        return (
            objectKeys(recordValue)
                // Keys must be sorted so first so if there is a mix of normal keys and keys with `__` in them, we process the normal keys first.
                .sort()
                .reduce((previousValue, key) => {
                    // If the key includes a forbidden key we skip it
                    if (this.forbiddenKeys.includes(key)) {
                        return { ...previousValue, [key]: recordValue[key] };
                    }
                    const parts = key.split(DEEP_BIND_QUERY_ALIAS_GLUE);
                    if (parts.length > 1 && parts[0] !== '' && parts[1] !== '') {
                        const keyToUse = parts[0];
                        const isArrayValue = isArray(recordValue[key]);
                        const defaultValue = isArrayValue ? [] : {};
                        const currentValue = previousValue[keyToUse] || defaultValue;
                        let repackedValue: any = {};
                        if (isArrayValue) {
                            repackedValue = deepMerge(currentValue, recordValue[key], isArrayValue).map(
                                this.repackRecordData,
                            );
                            return { ...previousValue, [keyToUse]: repackedValue };
                        }

                        if (isPlainObject(recordValue[key])) {
                            repackedValue = this.repackRecordData(recordValue[key]);
                        } else {
                            repackedValue[parts[1]] = recordValue[key];
                        }

                        previousValue[keyToUse] =
                            repackedValue instanceof Object && objectKeys(repackedValue).length === 0
                                ? null
                                : { ...repackedValue, ...previousValue[keyToUse] };
                    } else if (
                        recordValue[key] instanceof Decimal ||
                        recordValue[key]?.constructor?.name === 'Decimal' ||
                        !!recordValue[key]?.constructor?.Decimal
                    ) {
                        previousValue[key] = recordValue[key].toNumber();
                    } else if (recordValue[key] instanceof Date) {
                        previousValue[key] = getIsoDate(recordValue[key]);
                    } else if (isArray(recordValue[key])) {
                        previousValue[key] = recordValue[key].map(this.repackRecordData);
                    } else if (isPlainObject(recordValue[key])) {
                        previousValue[key] = this.repackRecordData(recordValue[key]);
                    } else {
                        previousValue[key] = recordValue[key];
                    }

                    return previousValue;
                }, {} as any)
        );
    };

    protected notifyValueChangeSubscribers(
        type: RecordActionType,
        recordValue: Partial<InternalValue<T>>,
        isUncommitted = false,
    ): void {
        if (!isUncommitted && this.db.isCollectionDirty()) {
            dispatchSetFieldDirty({
                screenId: this.screenId,
                elementId: this.elementId,
            });
        }

        const subscriptions = isUncommitted ? this.valueChangeUncommittedSubscribers : this.valueChangeSubscribers;
        subscriptions.forEach(subscriber => {
            subscriber(type, recordValue);
        });
    }

    public getErrorMessages({
        records = this.getAllInvalidRecords({ level: null, parentId: null }),
        fieldProperties = getStore().getState().screenDefinitions[this.screenId].metadata.uiComponentProperties[
            this.elementId
        ] as MakeRequired<TableProperties, 'mainField'>,
        withoutServerErrors = false,
    }: {
        records?: InternalValue<T>[];
        fieldProperties?: MakeRequired<TableProperties, 'mainField'>;
        withoutServerErrors?: boolean;
    } = {}): TableValidationError[] {
        const fieldTitle = resolveByValue({
            screenId: this.screenId,
            skipHexFormat: true,
            propertyValue: fieldProperties.title,
            rowValue: undefined,
        });
        return records.reduce<TableValidationError[]>((acc, curr) => {
            const { __validationState: validationState, _id: recordId } = curr;

            if (validationState) {
                objectKeys(validationState).forEach(valKey => {
                    const { message, columnId, validationRule } = validationState[valKey];

                    if (withoutServerErrors && validationRule.indexOf(SERVER_VALIDATION_RULE_PREFIX) === 0) {
                        return acc;
                    }
                    if (!columnId) {
                        acc.push({
                            fieldName: fieldTitle,
                            mainFieldValue: String(get(curr, fieldProperties.mainField || '_id')),
                            message,
                            recordId,
                            validationRule,
                        });
                        return acc;
                    }

                    const mainFieldName = findNestedFieldProperties(
                        this.screenId,
                        fieldProperties.mainField || '_id',
                        fieldProperties,
                        0,
                    );
                    const columnName = findNestedFieldProperties(this.screenId, columnId, fieldProperties, 0);
                    return acc.push({
                        fieldName: fieldTitle,
                        mainFieldName,
                        mainFieldValue: String(get(curr, fieldProperties.mainField || '_id')),
                        columnId,
                        columnName,
                        message,
                        recordId,
                        validationRule,
                        level: curr.__level,
                    });
                });
            }

            return acc;
        }, []);
    }

    public getAllInvalidRecords({
        level,
        parentId,
    }: { level?: number | null; parentId?: string | null } = {}): InternalValue<T>[] {
        return this.db
            .getAllInvalidRecords({ level, parentId })
            .sort({
                orderBy: this.getOrderBy({ level, parentId }),
                columnDefinitions: this.getColumnDefinitions(level),
            })
            .value({ cleanMetadata: false });
    }

    public getInvalidUnloadedRecords({
        level,
        parentId,
        withoutServerErrors,
    }: { level?: number | null; parentId?: string | null; withoutServerErrors?: boolean } = {}): InternalValue<T>[] {
        return this.db
            .getInvalidUnloadedRecords({ level, parentId, withoutServerErrors })
            .sort({
                orderBy: this.getOrderBy({ level, parentId }),
                columnDefinitions: this.getColumnDefinitions(level),
            })
            .value({ cleanMetadata: false });
    }

    public getInvalidLoadedRecords({
        level,
        parentId,
        withoutServerErrors,
    }: { level?: number | null; parentId?: string | null; withoutServerErrors?: boolean } = {}): InternalValue<T>[] {
        return this.db
            .getInvalidLoadedRecords({ level, parentId, withoutServerErrors })
            .sort({
                orderBy: this.getOrderBy({ level, parentId }),
                columnDefinitions: this.getColumnDefinitions(level),
            })
            .value({ cleanMetadata: false });
    }

    public async fetchAllErrors(): Promise<void> {
        const fieldProperties = getStore().getState().screenDefinitions[this.screenId].metadata.uiComponentProperties[
            this.elementId
        ] as MakeRequired<TableProperties, 'mainField'>;
        const invalidUnloaded = this.getInvalidUnloadedRecords();
        if (fieldProperties.mainField !== '_id') {
            const idsToFetch = invalidUnloaded.slice(0, 500).map(r => r._id);
            const pageDefinition = getPageDefinitionFromState(this.screenId);
            const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);
            const idFilter = { _id: { _in: idsToFetch } };
            const result = await fetchCollectionData({
                screenDefinition: pageDefinition,
                rootNode: pageProperties.node as string,
                rootNodeId: pageDefinition.selectedRecordId || '',
                elementId: this.elementId,
                nestedFields: this.getColumnDefinitions(0),
                queryArguments: { filter: JSON.stringify(idFilter), first: idsToFetch.length },
                bind: this.bind,
            });
            this.addQueryResultToCollection({
                result,
                level: 0,
                markAsAdded: false,
            });
        }
    }

    public validateField = async ({
        skipDispatch = false,
        withoutServerErrors = false,
    }: { skipDispatch?: boolean; withoutServerErrors?: boolean } = {}): Promise<ValidationResult[]> => {
        const isValid = this.db.isCollectionValid();
        if (isValid) {
            if (!skipDispatch) {
                dispatchFieldValidation(this.screenId, this.elementId);
            }
            return [];
        }
        const invalidLoaded = this.getInvalidLoadedRecords({ level: null, parentId: null, withoutServerErrors });
        const invalidUnloaded = this.getInvalidUnloadedRecords({ level: null, parentId: null, withoutServerErrors });
        const allInvalid = [...invalidLoaded, ...invalidUnloaded];
        const expectedLength = Math.min(10, allInvalid.length);
        const fieldProperties = getStore().getState().screenDefinitions[this.screenId].metadata.uiComponentProperties[
            this.elementId
        ] as MakeRequired<TableProperties, 'mainField'>;
        let errors;
        let validationResult;
        if (invalidLoaded.length >= expectedLength) {
            errors = this.getErrorMessages({
                records: invalidLoaded,
                fieldProperties,
            });
            validationResult = this.getErrorList(errors);
            if (!skipDispatch) {
                dispatchFieldValidation(this.screenId, this.elementId, validationResult);
            }

            return validationResult;
        }

        if (fieldProperties.mainField && fieldProperties.mainField !== '_id') {
            const idsToFetch = invalidUnloaded.slice(0, expectedLength - invalidLoaded.length).map(r => r._id);
            const pageDefinition = getPageDefinitionFromState(this.screenId);
            const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);
            const idFilter = { _id: { _in: idsToFetch } };
            const result = await fetchCollectionData({
                screenDefinition: pageDefinition,
                rootNode: pageProperties.node as string,
                rootNodeId: pageDefinition.selectedRecordId || '',
                elementId: this.elementId,
                nestedFields: this.getColumnDefinitions(0),
                queryArguments: { filter: JSON.stringify(idFilter) },
                bind: this.bind,
            });
            this.addQueryResultToCollection({
                result,
                level: 0,
                markAsAdded: false,
            });
        }

        const invalidRecords = this.getAllInvalidRecords({ level: null });
        errors = this.getErrorMessages({
            records: invalidRecords,
            fieldProperties,
        });
        validationResult = this.getErrorList(errors);
        if (!skipDispatch) {
            dispatchFieldValidation(this.screenId, this.elementId, validationResult);
        }
        return validationResult;
    };

    getErrorList = (errors: TableValidationError[]): ValidationResult[] => {
        return errors.map(
            ({ mainFieldName, mainFieldValue, columnId, columnName, message, recordId, validationRule, level }) => {
                let messagePrefix = mainFieldName ? `${mainFieldName}: ${mainFieldValue} -` : `Id: ${recordId}`;
                if (columnId) {
                    messagePrefix += ` ${columnName}`;
                }
                return {
                    screenId: this.screenId,
                    elementId: this.elementId,
                    columnId,
                    recordId,
                    message,
                    messagePrefix,
                    validationRule,
                    level,
                };
            },
        );
    };

    isValueEmpty = (value: any): boolean => {
        if (typeof value === 'number') {
            return false;
        }
        if (typeof value === 'boolean') {
            return false;
        }
        return isEmpty(value);
    };

    protected hasMultipleLevels(): boolean {
        return this.levelMap !== undefined;
    }

    public getValidationStateByColumn(): CollectionGlobalValidationState {
        return this.db.getValidationStateByColumn(this.columnDefinitions.length);
    }

    protected notifyValidationSubscribers(
        recordsWithChangedValidationStatus: InternalValue<T>[],
        isUncommitted = false,
    ): void {
        // Calculate global validation status.
        const globalValidationState = this.getValidationStateByColumn();

        const subscribers = isUncommitted ? this.validityChangeUncommittedSubscribers : this.validityChangeSubscribers;

        // Notify the subscribers for every record
        recordsWithChangedValidationStatus.forEach(r =>
            subscribers.forEach(subscriber => {
                subscriber({
                    globalValidationState,
                    recordValidationState: r.__validationState || {},
                    recordId: r._id,
                    recordLevel: r.__level,
                });
            }),
        );
    }

    protected getUpdatedRecord({
        previous,
        newData,
        toBeMarkedAsDirty,
        addToDirty = [],
    }: {
        previous: InternalValue<T>;
        newData: InternalValue<T>;
        toBeMarkedAsDirty?: string[];
        addToDirty?: string[];
    }): InternalValue<T> {
        const recordData = this.formatRecordValue(newData, previous);

        const updatedKeys = (
            toBeMarkedAsDirty ||
            objectKeys(recordData).reduce<string[]>((acc, key) => {
                return key === '_id' ||
                    key.startsWith('__') ||
                    (this.isValueEmpty(get(previous, key)) && this.isValueEmpty(recordData[key])) ||
                    isEqual(recordData[key], get(previous, key))
                    ? acc
                    : acc.concat(key);
            }, [])
        ).concat(addToDirty);
        const newDirtyColumns = Array.from(newData.__dirtyColumns || []);
        const dirtyColumns = [...updatedKeys, ...newDirtyColumns].reduce<Set<string>>((acc, key) => {
            return acc.add(key);
        }, previous.__dirtyColumns || new Set<string>());

        /**
         * When items removed from array field (multi reference, multi select etc), the deep merge operation above will persist and duplicate the values,
         *  so we need to ensure that the array remains unique
         *  */
        const mergedRecordValue = deepMerge(previous, recordData, false, true);

        return {
            ...mergedRecordValue,
            __dirty: toBeMarkedAsDirty ? toBeMarkedAsDirty.length > 0 : true,
            __dirtyColumns: dirtyColumns,
            // If it's a new record which has not been saved yet, we keep it as is
            __action:
                previous?.__action === RecordActionType.ADDED ? RecordActionType.ADDED : RecordActionType.MODIFIED,
        };
    }

    public getChangedRecords(): any[] {
        return this.db
            .findAll({
                where: {
                    __action: {
                        $in: [RecordActionType.ADDED, RecordActionType.MODIFIED, RecordActionType.REMOVED],
                    },
                },
            })
            .value({ cleanMetadata: false });
    }

    public updateMany(records: any[]): void {
        records.forEach(recordData => {
            this.addOrUpdateRecordValue({ recordData });
        });
    }

    protected findAndUpdate({
        recordData,
        level,
        shouldNotifySubscribers = true,
        upsert = false,
        toBeMarkedAsDirty,
        resetRecordAction = false,
        isOrganicChange = false,
        changedColumnId,
        isUncommitted = false,
    }: {
        recordData: PartialCollectionValue<T>;
        level?: number;
        shouldNotifySubscribers?: boolean;
        upsert?: boolean;
        toBeMarkedAsDirty?: string[];
        resetRecordAction?: boolean;
        isOrganicChange?: boolean;
        changedColumnId?: string;
        isUncommitted?: boolean;
    }): InternalValue<T> | null {
        if (!recordData._id && !upsert) {
            throw new Error(`Cannot update a record without an '_id' property: ${{ recordData }}`);
        }
        let rec = recordData._id
            ? this.db.findOne({
                  id: recordData._id as string,
                  level,
                  cleanMetadata: false,
                  includeUnloaded: true,
                  isUncommitted,
              })
            : null;

        // If a record is found and there is an uncommitted version of it, we should update that instead.
        const uncommittedRecord = this.db.findOne({
            id: recordData._id as string,
            level,
            cleanMetadata: false,
            includeUnloaded: true,
            isUncommitted: true,
        });

        if (uncommittedRecord) {
            rec = uncommittedRecord;
        }

        if (!rec) {
            if (!upsert) {
                throw new Error(
                    `A record with ID '${recordData._id}' could not be found therefore it could not be updated. Are sure you want to update an existing record?`,
                );
            } else {
                return this.db.addRecord({
                    data: recordData,
                    level,
                    beforeInsert: record => this.formatRecordValue(this.repackRecordData(record)),
                    afterInsert: ({ action, record }) => {
                        if (shouldNotifySubscribers) {
                            this.notifyValueChangeSubscribers(action, record, false);
                        }
                    },
                });
            }
        } else {
            const updatedData = this.getUpdatedRecord({
                previous: rec,
                newData: recordData as InternalValue<T>,
                toBeMarkedAsDirty,
            });

            if (resetRecordAction) {
                delete updatedData.__action;
            }

            return this.db.update({
                data: updatedData,
                beforeUpdate: record => this.formatRecordValue(this.repackRecordData(record), undefined, true, level),
                afterUpdate: () => {
                    if (shouldNotifySubscribers) {
                        this.notifyValueChangeSubscribers(
                            RecordActionType.MODIFIED,
                            updatedData,
                            rec?.__uncommitted || false,
                        );
                    }
                    if (isOrganicChange) {
                        this.runValidationOnRecord({ recordData: updatedData, changedColumnId });
                    }
                },
            });
        }
    }

    protected addQueryResultToCollection({
        result,
        level,
        markAsAdded = false,
        parentId,
        group,
    }: {
        result: any;
        level?: number;
        markAsAdded?: boolean;
        parentId?: string;
        group?: { key: string; value?: string; aggFunc?: AggFunc; type: FieldKey };
    }): InternalValue<T>[] {
        if (result?.data) {
            this.hasNextPage = Boolean(result.pageInfo?.hasNextPage);
            return result.data.map((rec: any) => {
                const existingRecord = this.db.findOne({
                    id: rec._id,
                    level,
                    parentId,
                    cleanMetadata: false,
                    includeUnloaded: true,
                    where: {
                        __isGroup: { $eq: rec.__isGroup },
                        __groupKey: { $eq: rec.__groupKey },
                    },
                });

                /**
                 * If the record was already in the database for some reason we update it with the data coming from the server, but letting local changes to take priority, EXCEPT the __groupCount & __cursor (which can change with groups)
                 *  */
                if (existingRecord) {
                    const data = {
                        ...rec,
                        ...existingRecord,
                        ...(rec.__groupCount && { __groupCount: rec.__groupCount }),
                        ...(rec.__cursor && { __cursor: rec.__cursor }),
                        ...(group?.aggFunc && { __aggFunc: group.aggFunc }),
                    };

                    // We do not merge deeply the incoming record and the group key property could be lost so we need to ensure that we preserve it
                    if (group?.key) {
                        const groupKeyPath = getGroupKey({ groupKey: group.key, type: group.type });
                        if (!get(data, groupKeyPath)) {
                            set(data, groupKeyPath, get(rec, groupKeyPath));
                        }
                    }

                    // It is important to unset the __unloaded flag.
                    delete data.__unloaded;
                    return this.db.update({
                        data,
                        beforeUpdate: record =>
                            this.formatRecordValue(this.repackRecordData(record), undefined, true, level),
                    });
                }

                return this.db.addRecord({
                    data: { ...rec, ...(group?.aggFunc && { __aggFunc: group.aggFunc }) },
                    dirty: false,
                    level,
                    parentId,
                    cleanMetadata: true,
                    ...(!markAsAdded && { action: 'none' }),
                    beforeInsert: record =>
                        this.formatRecordValue(this.repackRecordData(record), undefined, true, level),
                });
            });
        }

        return [];
    }

    /** For data to be used in control objects without lokiJs metadata */
    public getRawRecords(): any[] {
        return this.db.findAll({ level: null, parentId: null }).value();
    }

    public getFormattedActiveRecords = (): any[] => {
        return this.db
            .findAll({
                level: null,
                parentId: null,
                where: {
                    __action: { $nin: [RecordActionType.REMOVED, RecordActionType.TEMPORARY] },
                },
            })
            .value({ cleanMetadata: true, cleanDatabaseMeta: true })
            .map(splitValueToMergedValue);
    };

    /** For data to be used in control objects without lokiJs metadata */
    public getRawRecord(args: {
        cleanMetadata: undefined;
        id: string;
        includeUnloaded?: boolean;
        level?: number;
        temporaryRecords?: PartialCollectionValueWithIds<T>[];
        isUncommitted?: boolean;
    }): InternalValue<T> & LokiObj;
    public getRawRecord(args: {
        cleanMetadata?: false;
        id: string;
        includeUnloaded?: boolean;
        level?: number;
        temporaryRecords?: PartialCollectionValueWithIds<T>[];
        isUncommitted?: boolean;
    }): InternalValue<T> & LokiObj;
    public getRawRecord(args: {
        cleanMetadata?: true;
        id: string;
        includeUnloaded?: boolean;
        level?: number;
        temporaryRecords?: PartialCollectionValueWithIds<T>[];
        isUncommitted?: boolean;
    }): PartialCollectionValueWithIds<T>;
    public getRawRecord(args: {
        cleanMetadata?: boolean;
        id: string;
        includeUnloaded?: boolean;
        level?: number;
        isUncommitted?: boolean;
    }): (InternalValue<T> & LokiObj) | PartialCollectionValueWithIds<T>;
    public getRawRecord({
        cleanMetadata = true,
        id,
        includeUnloaded = false,
        level,
        temporaryRecords = [],
        isUncommitted = false,
    }: {
        cleanMetadata?: boolean;
        id: string;
        includeUnloaded?: boolean;
        level?: number;
        temporaryRecords?: PartialCollectionValueWithIds<T>[];
        isUncommitted?: boolean;
    }): (InternalValue<T> & LokiObj) | PartialCollectionValueWithIds<T> {
        return this.db.withTemporaryRecords(db => {
            return db.findOne({ id, level, cleanMetadata, includeUnloaded, isUncommitted });
        }, temporaryRecords);
    }

    /** Find a record based on a property's value */
    public getRawRecordByFieldValue({
        fieldName,
        fieldValue,
        level = 0,
        includeUnloaded = false,
    }: {
        fieldName: string;
        fieldValue: any;
        level?: number;
        includeUnloaded?: boolean;
    }): PartialCollectionValueWithIds<T> | null {
        const record = this.db.findOne({
            level,
            where: {
                [fieldName as any]: { $eq: fieldValue },
            },
            includeUnloaded,
        });
        if (!record) {
            return null;
        }
        if (this.levelMap?.[level]) {
            record[this.levelMap?.[level]] = this.getNestedRecordsRecursive({
                parentId: record._id,
                level: level + 1,
            });
        }
        return splitValueToMergedValue(cleanMetadataFromRecord(record));
    }

    public getAncestorIds(args: { id: string; level?: number }): string[] {
        return this.db.getAncestorIds(args);
    }

    protected getNestedRecordsRecursive({
        parentId,
        level,
        cb = (record: Partial<InternalValue<T>>): Partial<InternalValue<T>> => record,
    }: {
        parentId: string;
        level: number;
        cb?: (record: Partial<InternalValue<T>>) => Partial<InternalValue<T>>;
    }): any {
        if (this.levelMap === undefined) {
            throw new Error('Missing level information!');
        }
        const records = this.db
            .findAll({ parentId, level })
            .sort({ orderBy: { _id: -1 }, columnDefinitions: this.getColumnDefinitions(level) })
            .value({ cleanMetadata: false });
        if (this.levelMap[level]) {
            records.forEach(record => {
                set(
                    record,
                    this.levelMap![level] as keyof InternalValue<T>,
                    this.getNestedRecordsRecursive({
                        parentId: record._id,
                        level: level + 1,
                        cb,
                    }),
                );
            });
        }
        return records.map(record => splitValueToMergedValue(cleanMetadataFromRecord(cb(record))));
    }

    public getRecordWithChildren({
        id,
        level = 0,
        isUncommitted = false,
        cleanMetadata = true,
        cb = (record: PartialCollectionValueWithIds<T>): PartialCollectionValueWithIds<T> => record,
    }: {
        id: string;
        level?: number;
        isUncommitted?: boolean;
        cleanMetadata?: boolean;
        cb?: (record: PartialCollectionValueWithIds<T>) => PartialCollectionValueWithIds<T>;
    }): PartialCollectionValueWithIds<T> | null {
        const foundRecord = this.db.findOne({ id, level, cleanMetadata: false, isUncommitted });
        if (!foundRecord) {
            return null;
        }
        if (this.levelMap?.[level]) {
            (foundRecord as any)[this.levelMap?.[level]] = this.getNestedRecordsRecursive({
                parentId: foundRecord._id,
                level: level + 1,
            });
        }
        return cleanMetadata ? splitValueToMergedValue(cleanMetadataFromRecord(cb(foundRecord))) : cb(foundRecord);
    }

    public getRecordByIdAndLevel(args: { id: string; level?: number }): InternalValue<T> {
        return this.db.findOne({ ...args, cleanMetadata: false });
    }

    public cleanCollectionData(): void {
        this.db.clear();
        setWith(this.lastFetchedPage, [0, 0], 0, Object);
        this.filter = [];
        this.orderBy = [];
        this.forceRefetch = true;
    }

    public async fetchNestedGrid({
        rootNode,
        selectedRecordId,
        level,
        levelProps,
        queryArguments,
        childProperty,
    }: FetchNestedGridArgs): Promise<InternalValue<T>[]> {
        const pageDefinition = getPageDefinitionFromState(this.screenId);
        const graphqlQueryResult = await fetchCollectionData({
            screenDefinition: pageDefinition,
            rootNode,
            rootNodeId: selectedRecordId,
            elementId: childProperty,
            nestedFields: levelProps.columns,
            queryArguments,
        });
        const newLevelData = graphqlQueryResult
            ? {
                  ...graphqlQueryResult,
                  data: (graphqlQueryResult.data || []).map((d: any) => ({
                      ...d,
                      __level: level,
                      __parentId: selectedRecordId,
                  })),
              }
            : graphqlQueryResult;

        return this.addQueryResultToCollection({
            result: newLevelData,
            level,
            markAsAdded: false,
            parentId: selectedRecordId,
        });
    }

    public getNestedGrid({ level, parentId }: { level: number; parentId: string }): InternalValue<T>[] {
        return this.db
            .findAll({
                level,
                parentId,
                where: { __action: { $ne: RecordActionType.REMOVED } },
            })
            .sort({
                orderBy: this.getOrderBy({ level, parentId }),
                columnDefinitions: this.getColumnDefinitions(level),
            })
            .value({ cleanMetadata: false });
    }

    public getFilteredSortedRecords({
        pageSize,
        pageNumber,
        group,
        limit = pageSize,
        level = 0,
        cleanMetadata = true,
        parentId,
        temporaryRecords = [],
    }: {
        pageSize: number;
        pageNumber: number;
        group?: { key: string; value?: string; aggFunc?: AggFunc; type: FieldKey };
        limit?: number;
        level?: number;
        cleanMetadata?: boolean;
        parentId?: string;
        temporaryRecords?: PartialCollectionValueWithIds<T>[];
    }): any {
        return this.db.withTemporaryRecords(db => {
            const queryBuilder = db.findAll({
                where: { __action: { $ne: RecordActionType.REMOVED } },
                level,
                parentId,
            });
            const filter = this.getInternalFilter({ level, parentId });
            if (group !== undefined) {
                const groupKey = getGroupKey({ groupKey: group.key, type: group.type });
                queryBuilder.findAll({
                    where:
                        group.key && group.value !== undefined
                            ? {
                                  $and: [
                                      ...[this.sublevelProperty ? {} : { __isGroup: { $ne: true } }],
                                      getGroupFilterValue({ group, mode: 'client' }),
                                      { __aggFunc: { $eq: group.aggFunc } },
                                  ] as any,
                              }
                            : {
                                  $and: [
                                      ...[this.sublevelProperty ? {} : { __isGroup: { $eq: true } }],
                                      { __groupKey: { $eq: groupKey } },
                                      { __groupCount: { $gt: 0 } },
                                      { __aggFunc: { $eq: group.aggFunc } },
                                  ],
                              },
                    level,
                    parentId,
                });
            } else {
                queryBuilder.findAll({
                    where: this.sublevelProperty ? {} : { __isGroup: { $ne: true } },
                    level,
                    parentId,
                });
            }
            if (filter && !isEmpty(filter)) {
                const lokiJsFilter = transformToLokiJsFilter(filter);
                const where = group === undefined || group?.value !== undefined ? lokiJsFilter : {};
                try {
                    queryBuilder.findAll({
                        where,
                        level,
                        parentId,
                    });
                } catch (e) {
                    if (isDevMode()) {
                        xtremConsole.error(e);
                        xtremConsole.error(
                            `Failed to filter dataset using in-memory database for ${this.screenId} - ${this.elementId}`,
                        );
                        xtremConsole.error('GraphQL filter:');
                        xtremConsole.error(JSON.stringify(filter, null, 4));
                        xtremConsole.error('LokiJS filter:');
                        xtremConsole.error(JSON.stringify(where, null, 4));
                    }
                }
            }
            let orderBy = this.getOrderBy({ level, parentId });
            if (group !== undefined && group.value === undefined) {
                // eslint-disable-next-line @sage/redos/no-vulnerable
                const nestedValueMatch = group.key.match(/(.*)\./);
                orderBy = deepMerge(
                    nestedValueMatch ? set(set({}, group.key, 1), `${nestedValueMatch[1]}._id`, 1) : {},
                    objectKeys(flat(orderBy))
                        .filter(k => k.startsWith(group.key))
                        .reduce((acc, curr) => {
                            set(acc, curr, get(orderBy, curr));
                            return acc;
                        }, {}),
                );
            }
            queryBuilder
                .sort({
                    orderBy,
                    columnDefinitions: this.getColumnDefinitions(level),
                })
                .skip(pageNumber * pageSize)
                .take(limit);
            return queryBuilder.value({ cleanMetadata });
        }, temporaryRecords);
    }

    public generateIndex(): string {
        return this.db.generateIndex();
    }

    public getActiveOptionsMenuItem(): OptionsMenuItem | undefined {
        return this.activeOptionsMenuItem;
    }

    public async getPageWithCurrentQueryArguments({
        pageNumber,
        pageSize,
        tableFieldProperties,
        group,
        level = 0,
        parentId,
        cleanMetadata = true,
        cursor,
        totalCount = false,
    }: {
        tableFieldProperties: GridLodable;
        pageSize: number;
        pageNumber: number;
        group?: { key: string; value?: string; aggFunc?: AggFunc; type: FieldKey };
        level?: number;
        parentId?: string;
        cleanMetadata?: boolean;
        cursor?: string;
        totalCount?: boolean;
    }): Promise<any[]> {
        const temporaryRecords = resolveByValue({
            skipHexFormat: true,
            screenId: this.screenId,
            propertyValue: tableFieldProperties.additionalLookupRecords,
            rowValue: null,
            fieldValue: null,
        });
        if (group === undefined && pageNumber <= get(this.lastFetchedPage, [level, parentId ?? 0], -1)) {
            return this.getFilteredSortedRecords({
                pageSize,
                pageNumber,
                group,
                cleanMetadata,
                level,
                parentId,
                temporaryRecords,
            });
        }
        const pageDefinition = getPageDefinitionFromState(this.screenId);
        await this.fetchServerRecords({
            pageDefinition,
            tableFieldProperties,
            pageSize,
            group,
            level,
            parentId,
            cursor,
            totalCount,
        });
        setWith(this.lastFetchedPage, [level ?? 0, parentId ?? 0], pageNumber, Object);
        return this.getFilteredSortedRecords({
            pageSize,
            pageNumber,
            group,
            cleanMetadata,
            level,
            parentId,
            temporaryRecords,
        });
    }

    public async getRecordWithCurrentQueryArguments({
        pageNumber,
        pageSize,
        tableFieldProperties,
        group,
        level,
        parentId,
        cursor,
        cleanMetadata = true,
    }: {
        tableFieldProperties: GridLodable;
        pageSize: number;
        pageNumber: number;
        group?: { key: string; value?: string; aggFunc?: AggFunc; type: FieldKey };
        level?: number;
        parentId?: string;
        cursor?: string;
        cleanMetadata?: boolean;
    }): Promise<any[]> {
        const pageDefinition = getPageDefinitionFromState(this.screenId);
        const temporaryRecords = resolveByValue({
            skipHexFormat: true,
            screenId: this.screenId,
            propertyValue: tableFieldProperties.additionalLookupRecords,
            rowValue: null,
            fieldValue: null,
        });

        await this.fetchServerRecords({
            pageDefinition,
            tableFieldProperties,
            pageSize: 1,
            group,
            level,
            parentId,
            cursor,
        });

        return this.getFilteredSortedRecords({
            pageSize,
            pageNumber,
            group,
            level,
            parentId,
            cleanMetadata,
            temporaryRecords,
        });
    }

    public async getPage({
        axiosCancelToken,
        cleanMetadata = true,
        cursor,
        fetchPageSize,
        filters = [],
        group,
        level = 0,
        orderBy: order,
        pageNumber = 0,
        pageSize = 20,
        parentId,
        rawFilter,
        searchText: text,
        selectedOptionsMenuItem,
        tableFieldProperties,
        totalCount,
    }: GetPageParams): Promise<any[]> {
        const { pageDefinition, shouldRefetch } = this.getFetchServerRecordsOptions({
            filters,
            group,
            level,
            orderBy: order,
            parentId,
            rawFilter,
            searchText: text,
            selectedOptionsMenuItem,
            tableFieldProperties,
        });

        if (shouldRefetch) {
            await this.fetchServerRecords({
                pageDefinition,
                tableFieldProperties,
                pageSize: fetchPageSize || pageSize,
                group,
                axiosCancelToken,
                parentId,
                level,
                cursor,
                totalCount,
            });
            this.forceRefetch = false;
        }

        return this.getFilteredSortedAndTemporaryRecords({
            cleanMetadata,
            group,
            level,
            pageNumber,
            pageSize,
            parentId,
            tableFieldProperties,
        });
    }

    // INFO: This function fetches the record data iteratively, page by page
    //       until the last page has been retrieved. For large data sets, this
    //       may cause performance issues. Unless all records are required
    //       (e.g. the excel/csv export), the getPage() function should be used
    //       instead.
    public async getPagesIteratively({
        axiosCancelToken,
        cleanMetadata = true,
        cursor,
        fetchPageSize,
        filters = [],
        group,
        level = 0,
        orderBy: order,
        pageSize = 20,
        parentId,
        rawFilter,
        searchText: text,
        selectedOptionsMenuItem,
        tableFieldProperties,
        totalCount,
    }: GetPageParams): Promise<any[]> {
        const { pageDefinition, shouldRefetch } = this.getFetchServerRecordsOptions({
            filters,
            group,
            level,
            orderBy: order,
            parentId,
            rawFilter,
            searchText: text,
            selectedOptionsMenuItem,
            tableFieldProperties,
        });

        if (shouldRefetch) {
            let result: any;
            let currCursor: string | undefined = cursor;

            do {
                result = await this.fetchServerRecords({
                    pageDefinition,
                    tableFieldProperties,
                    pageSize: fetchPageSize || pageSize,
                    group,
                    axiosCancelToken,
                    parentId,
                    level,
                    cursor: currCursor,
                    totalCount,
                });
                currCursor = result?.pageInfo?.endCursor;
            } while (result?.pageInfo?.hasNextPage);

            this.forceRefetch = false;
        }

        return this.getFilteredSortedAndTemporaryRecords({
            cleanMetadata,
            group,
            level,
            pageNumber: 0,
            pageSize: Number.POSITIVE_INFINITY,
            parentId,
            tableFieldProperties,
        });
    }

    private takeFilterSnapshot({
        level = 0,
        parentId,
        snapshot,
    }: {
        parentId?: string;
        level?: number;
        snapshot: GraphQLFilter<T>;
    }): void {
        setWith(this.filterSnapshot, [level, parentId ?? 0], snapshot, Object);
    }

    public restoreFilterSnapshot(): void {
        if (isNil(this.filterSnapshot)) {
            return;
        }
        this.filter = this.filterSnapshot;
    }

    private getFetchServerRecordsOptions({
        filters = [],
        group,
        level = 0,
        orderBy: order,
        parentId,
        rawFilter,
        searchText: text,
        selectedOptionsMenuItem,
        tableFieldProperties,
    }: GetFetchServerRecordsOptionsParams): { pageDefinition: PageDefinition; shouldRefetch: boolean } {
        const orderBy = order ?? this.getOrderBy({ level, parentId });
        const state = getStore().getState();
        const pageDefinition = getPageDefinitionFromState(this.screenId, state);
        const applicableFilters: GraphQLFilter[] = [];

        // Resolve functional developer defined filters
        if (tableFieldProperties.filter) {
            const screenDefinition = getPageDefinitionFromState(this.screenId);
            // In nested context like reference lookup we only send rowValue as argument, but in normal fields like a table we send value and rowValue for filters, more details at lib/component/field/traits.ts interface.
            if (this.fieldType === CollectionFieldTypes.LOOKUP_DIALOG) {
                const filters = convertFilterDecoratorToGraphQLFilter(
                    screenDefinition,
                    tableFieldProperties.filter,
                    this.recordContext,
                );
                if (filters) {
                    applicableFilters.push(filters);
                }
            } else {
                applicableFilters.push(
                    resolveByValue({
                        propertyValue: tableFieldProperties.filter,
                        rowValue: null,
                        fieldValue: null,
                        screenId: this.screenId,
                        skipHexFormat: true,
                    }),
                );
            }
        }

        // Resolve user defined filers
        if (filters && filters.length > 0) {
            applicableFilters.push(
                getGraphQLFilter(
                    filters,
                    getTypedNestedFields(
                        this.screenId,
                        this.getNode(0),
                        getNestedFieldsFromProperties(tableFieldProperties),
                        this.nodeTypes,
                    ),
                ),
            );
        }

        if (rawFilter) {
            applicableFilters.push(rawFilter);
        }
        // Always take a snapshot even with an empty text search
        this.takeFilterSnapshot({ level, parentId, snapshot: mergeGraphQLFilters(applicableFilters) || {} });
        if (text) {
            const fieldProperties = tableFieldProperties;
            applicableFilters.push(
                buildSearchBoxFilter(fieldProperties, state.nodeTypes, this.locale, this.fieldType, text),
            );
        }

        let hasOptionMenuItemChanged = false;
        if (!isEqual(this.activeOptionsMenuItem, selectedOptionsMenuItem)) {
            this.activeOptionsMenuItem = selectedOptionsMenuItem;
            hasOptionMenuItemChanged = true;
        }

        if (this.activeOptionsMenuItem) {
            applicableFilters.push(this.activeOptionsMenuItem.graphQLFilter);
        }
        // Merge functional developer and end-user filters.
        const combinedFilters = (mergeGraphQLFilters(applicableFilters) || {}) as GraphQLFilter<T>;
        const hasSortingChanged = !isEqual(this.getOrderBy({ level, parentId }), orderBy);
        const hasFilterChanged = !isEqual(this.getFilter({ level, parentId }), combinedFilters);
        const shouldRefetch =
            this.forceRefetch ||
            group !== undefined ||
            hasOptionMenuItemChanged ||
            hasSortingChanged ||
            hasFilterChanged;

        setWith(this.orderBy, [level, parentId ?? 0], orderBy, Object);
        setWith(this.filter, [level, parentId ?? 0], combinedFilters, Object);

        /**
         * When sorting or filtering changes, we need to delete all groups because we will fetch new data from the server.
         * It's not enough to just merge server data with existing groups because all filters apply to their children,
         * which are lazy loaded. It would be then impossible for us to know which groups are relevant for the current
         * page. On the other hand we need to keep groups in case a new page is being fetched.
         */
        if (hasFilterChanged && group !== undefined && !isEmpty(combinedFilters)) {
            this.db
                .findAll({ where: { __isGroup: { $eq: true } } })
                .value({ cleanMetadata: false, cleanDatabaseMeta: false })
                .forEach(r => {
                    this.db.remove({ data: r });
                });
        }

        return {
            pageDefinition,
            shouldRefetch,
        };
    }

    private getFilteredSortedAndTemporaryRecords({
        cleanMetadata = true,
        group,
        level = 0,
        pageNumber = 0,
        pageSize = 20,
        parentId,
        tableFieldProperties,
    }: GetFilteredSortedAndTemporaryRecordsParams): any {
        setWith(this.lastFetchedPage, [level, parentId ?? 0], 0, Object);

        const temporaryRecords = resolveByValue({
            skipHexFormat: true,
            screenId: this.screenId,
            propertyValue: tableFieldProperties.additionalLookupRecords,
            rowValue: null,
            fieldValue: null,
        });

        return this.getFilteredSortedRecords({
            pageSize,
            pageNumber,
            group,
            parentId,
            level,
            cleanMetadata,
            temporaryRecords,
        });
    }

    public subscribeForValueChanges(callback: ValueChangeSubscriptionCallback, isUncommitted = false): Unsubscribe {
        const subscriptions = isUncommitted ? this.valueChangeUncommittedSubscribers : this.valueChangeSubscribers;
        subscriptions.push(callback);
        return (): void => {
            const index = subscriptions.indexOf(callback);
            if (index !== -1) {
                subscriptions.splice(index, 1);
            }
        };
    }

    public subscribeForValidityChanges(
        callback: ValidityChangeSubscriptionCallback,
        isUncommitted = false,
    ): Unsubscribe {
        const subscriptions = isUncommitted
            ? this.validityChangeUncommittedSubscribers
            : this.validityChangeSubscribers;

        subscriptions.push(callback);
        return (): void => {
            const index = subscriptions.indexOf(callback);
            if (index !== -1) {
                subscriptions.splice(index, 1);
            }
        };
    }

    public runValidationOnRecord = async ({
        recordData,
        changedColumnId,
        isUncommitted,
        columnsToRevalidate,
    }: {
        recordData: InternalValue<T>;
        changedColumnId?: string;
        isUncommitted?: boolean;
        columnsToRevalidate?: string[];
    }): Promise<Dict<ValidationResult>> => {
        const level = recordData.__level ?? 0;
        const columnDefinitions = this.getColumnDefinitions(level);
        const rowValue = cleanMetadataFromRecord(recordData);

        // Execute validators of the row that is changed
        const currentValidationState = await checkNestedRecordValidationStatus({
            rowValue,
            elementId: this.elementId,
            screenId: this.screenId,
            recordId: recordData._id,
            columnDefinitions,
            columnsToValidate: columnsToRevalidate
                ? new Set(columnsToRevalidate)
                : recordData.__dirtyColumns || new Set<string>(),
        });

        // If the validation result is an empty object, we normalize to to undefined
        let normalizedCurrentValidationState = isEmpty(currentValidationState) ? undefined : currentValidationState;
        const normalizedPreviousValidationState = isEmpty(recordData.__validationState)
            ? undefined
            : recordData.__validationState;

        // If the user just edited a record, keep the server side validation errors unless they belong to the cell that the user just edited
        if (changedColumnId && normalizedPreviousValidationState) {
            /**
             * Copy over server validation rules to the new validation state of the record because the client record validation can't regenerate
             * the server side errors.
             *
             * The client side errors take precedence, so if there is a validation error in the new validation result we will not keep
             * the server generated value because the client validation error blocks the user from even submitting the page value for server
             * evaluation
             *  */
            Object.values(normalizedPreviousValidationState).forEach((e: ValidationResult) => {
                if (
                    e.columnId &&
                    e.columnId !== changedColumnId &&
                    e.validationRule.startsWith(SERVER_VALIDATION_RULE_PREFIX) &&
                    !Object.values(normalizedCurrentValidationState || {}).find(v => v.columnId === e.columnId)
                ) {
                    if (!normalizedCurrentValidationState) {
                        normalizedCurrentValidationState = {};
                    }
                    normalizedCurrentValidationState[e.columnId] = e;
                }
            });
        }

        // Only trigger event if the validation state of the record changed compared to its previous state.
        if (!isEqual(normalizedCurrentValidationState, normalizedPreviousValidationState)) {
            // Get a fresh copy of the record from the database because the values could have changed while the validation run
            const updatedRecord = {
                ...this.getRawRecord({
                    id: recordData._id,
                    level: recordData.__level,
                    cleanMetadata: false,
                    isUncommitted,
                }),
                __validationState: normalizedCurrentValidationState,
            };
            this.db.update({
                data: updatedRecord,
                beforeUpdate: record => this.formatRecordValue(this.repackRecordData(record), undefined, true, level),
                afterUpdate: () => this.notifyValidationSubscribers([updatedRecord], isUncommitted),
                isUncommitted,
            });

            const errors =
                isEmpty(normalizedCurrentValidationState) ||
                updatedRecord.__phantom === true ||
                updatedRecord.__uncommitted === true
                    ? []
                    : this.getErrorList(this.getErrorMessages({ records: [updatedRecord] }));

            dispatchUpdateNestedFieldValidationErrorsForRecord({
                screenId: this.screenId,
                elementId: this.elementId,
                recordId: recordData._id,
                level,
                validationResult: errors,
                isUncommitted,
            });
        }

        return currentValidationState;
    };

    public getIdPathToNestedRecord(id: string, level: number): string[] {
        return this.db.getIdPathToRecordByIdAndLevel({ id, level });
    }

    /**
     *
     * @param forceValidate If true, all records will be validated, otherwise only dirty records
     * @returns
     */
    public async validate(forceValidate: boolean): Promise<ValidationResult[]> {
        const columnDefinition: NestedField<any, any>[] = this.getColumnDefinitions(0);
        const revalidateColumns = forceValidate ? columnDefinition.map(c => c.properties.bind) : undefined;
        const validationResult = await Promise.all(
            this.db
                .findAll({
                    where: {
                        ...(forceValidate ? {} : { __dirty: { $eq: true } }),
                        __action: { $nin: [RecordActionType.REMOVED, RecordActionType.TEMPORARY] },
                    },
                })
                .value({ cleanMetadata: false, cleanDatabaseMeta: false })
                .map(r => this.runValidationOnRecord({ recordData: r, columnsToRevalidate: revalidateColumns })),
        );

        return validationResult
            .map(r => Object.values(r))
            .reduce(
                (prevValue: ValidationResult[], value: ValidationResult[]) => [...prevValue, ...value],
                [] as ValidationResult[],
            );
    }

    /**
     * This function adds external validation errors directly to the collection without running the client side rules. It is used to push server generated
     * errors to the records. Listener toast events are batched together and executed at the end to optimize performance and reduce the number of
     * rendering cycles.
     */
    public async addValidationErrors({
        validationErrors,
        shouldNotifySubscribers = false,
    }: {
        validationErrors: ValidationResult[];
        shouldNotifySubscribers?: boolean;
    }): Promise<void> {
        const remappedValidationErrors: Dict<ValidationResult[]> = validationErrors.reduce(
            (prevValue, v) => {
                if (!v.recordId) {
                    return prevValue;
                }
                const key = this.db.getCompositeKey({ _id: v.recordId, __level: v.level });
                if (prevValue[key]) {
                    prevValue[key].push(v);
                    return prevValue;
                }
                return { ...prevValue, [key]: [v] };
            },
            {} as Dict<ValidationResult[]>,
        );

        // Get all records that we might need to check: records that currently invalid by server side validation errors and new incoming server side errors
        const recordCompositeKeysToCheck: string[] = uniq([
            ...objectKeys(remappedValidationErrors),
            ...this.db.getAllServerRecordWithServerSideValidationIssues(),
        ]);

        const records: { record: InternalValue<T>; id: string; level?: number; compositeKey: string }[] =
            recordCompositeKeysToCheck
                .map(k => ({ ...this.db.getIdAndLevelFromCompositeKey(k), compositeKey: k }))
                .map(({ _id: id, __level: level, compositeKey }) => ({
                    record: this.getRawRecord({
                        cleanMetadata: false,
                        id,
                        includeUnloaded: true,
                        level,
                    }),
                    id,
                    level,
                    compositeKey,
                }));
        const [existingRecords, unloadedRecords] = partition(records, r => r.record !== null);
        unloadedRecords.forEach(({ compositeKey, id, level }) => {
            const incomingServerSideValidationError = remappedValidationErrors[compositeKey];
            /**
             * If no record data is set, a small "unloaded" placeholder record is created with the validation error
             *  */
            const validationState = incomingServerSideValidationError.reduce((prevRecord, v) => {
                prevRecord[v.columnId || `__rowError_${v.recordId}`] = v;
                return prevRecord;
            }, {} as Dict<ValidationResult>);
            const newRecord: any = {
                _id: id,
                __unloaded: true,
                __validationState: validationState,
            };

            this.db.addRecord({
                action: 'none',
                data: newRecord,
                level,
                cleanMetadata: false,
            });
        });
        const existingInvalidRecords = existingRecords.reduce<InternalValue<T>[]>(
            (acc, { record: recordData, compositeKey }) => {
                const incomingServerSideValidationError = remappedValidationErrors[compositeKey];
                let currentValidationState = { ...(recordData.__validationState || {}) };
                // Remove previous server side errors
                objectKeys(currentValidationState).forEach(s => {
                    if (currentValidationState[s].validationRule.startsWith(SERVER_VALIDATION_RULE_PREFIX)) {
                        delete currentValidationState[s];
                    }
                });

                // Check if there is any new error messages sent by the server and add it to the validation object
                if (incomingServerSideValidationError && incomingServerSideValidationError.length) {
                    currentValidationState = incomingServerSideValidationError.reduce((prevRecord, v) => {
                        prevRecord[v.columnId || `__rowError_${v.recordId}`] = v;
                        return prevRecord;
                    }, currentValidationState);
                }
                // If the validation result is an empty object, we normalize to to undefined
                const normalizedCurrentValidationState = isEmpty(currentValidationState)
                    ? undefined
                    : currentValidationState;
                const normalizedPreviousValidationState = isEmpty(recordData.__validationState)
                    ? undefined
                    : recordData.__validationState;

                // Only trigger event if the validation state of the record changed compared to its previous state.
                if (!isEqual(normalizedCurrentValidationState, normalizedPreviousValidationState)) {
                    // Get a fresh copy of the record from the database because the values could have changed while the validation was running
                    const updatedRecordData = {
                        ...recordData,
                        __validationState: normalizedCurrentValidationState,
                    };

                    acc.push(
                        this.db.update({
                            data: updatedRecordData,
                            beforeUpdate: record =>
                                this.formatRecordValue(this.repackRecordData(record), undefined, true),
                            cleanMetadata: false,
                        }),
                    );
                }
                return acc;
            },
            [],
        );

        // Notify subscribers about the updated state.
        if (shouldNotifySubscribers) {
            await this.validateField();
            this.notifyValidationSubscribers(existingInvalidRecords);
        }
    }

    public getMergedValue({
        recordId,
        columnId,
        value,
        level = 0,
        toBeMarkedAsDirty,
        addToDirty,
        isUncommitted,
    }: {
        recordId: string;
        columnId: string;
        value: any;
        level?: number;
        isOrganicChange?: boolean;
        toBeMarkedAsDirty?: string[];
        addToDirty?: string[];
        isUncommitted?: boolean;
    }): PartialCollectionValueWithIds<T> {
        const record = this.db.findOne({
            id: recordId,
            level,
            cleanMetadata: false,
            isUncommitted,
        });
        if (!record) {
            throw new Error(`Could not set cell value: ${JSON.stringify({ recordId, columnId, level, value })}`);
        }
        const delta = set({}, columnId, value) as InternalValue<T>;
        return this.getUpdatedRecord({ previous: record, newData: delta, toBeMarkedAsDirty, addToDirty });
    }

    public async setCellValue({
        recordId,
        columnId,
        value,
        level = 0,
        isOrganicChange = false,
        toBeMarkedAsDirty,
        addToDirty,
        isUncommitted,
        shouldFetchDefault = false,
    }: {
        recordId: string;
        columnId: string;
        value: any;
        level?: number;
        isOrganicChange?: boolean;
        toBeMarkedAsDirty?: string[];
        addToDirty?: string[];
        isUncommitted?: boolean;
        shouldFetchDefault?: boolean;
    }): Promise<PartialCollectionValueWithIds<T>> {
        let updatedData = this.getMergedValue({
            recordId,
            columnId,
            value,
            level,
            toBeMarkedAsDirty,
            addToDirty,
            isUncommitted,
        }) as InternalValue<T>;

        this.db.update({
            data: updatedData,
            beforeUpdate: toBeUpdated =>
                this.formatRecordValue(this.repackRecordData(toBeUpdated), undefined, true, level),
            afterUpdate: () => {
                this.notifyValueChangeSubscribers(RecordActionType.MODIFIED, updatedData, isUncommitted);
                if (isOrganicChange) {
                    this.runValidationOnRecord({ recordData: updatedData, changedColumnId: columnId, isUncommitted });
                }
            },
        });

        // Only fetch if column has the fetchDefault and is a new record.
        if (shouldFetchDefault && Number(recordId) < 0) {
            const defaultValues = await fetchNestedDefaultValues({
                screenId: this.screenId,
                elementId: this.elementId,
                recordId,
                data: Array.from(updatedData.__dirtyColumns || []).reduce(
                    (acc: any, dirtyColumn: string & keyof typeof updatedData) => {
                        acc[dirtyColumn] = updatedData[dirtyColumn];
                        return acc;
                    },
                    {},
                ),
                isNewRow: true,
            });

            if (defaultValues.nestedDefaults) {
                delete defaultValues.nestedDefaults._id;
                updatedData = { ...updatedData, ...defaultValues.nestedDefaults };
            }

            // We only update the DB if the record is still present in the dataset after the default value fetching
            if (this.db.findOne({ id: recordId, level, isUncommitted })) {
                this.db.update({
                    data: updatedData,
                    beforeUpdate: toBeUpdated =>
                        this.formatRecordValue(this.repackRecordData(toBeUpdated), undefined, true, level),
                    afterUpdate: () => {
                        this.notifyValueChangeSubscribers(RecordActionType.MODIFIED, updatedData, isUncommitted);
                        if (isOrganicChange) {
                            this.runValidationOnRecord({
                                recordData: updatedData,
                                changedColumnId: columnId,
                                isUncommitted,
                            });
                        }
                    },
                });
            }
        }

        return updatedData;
    }

    public addOrUpdateRecordValue({
        recordData,
        shouldNotifySubscribers = true,
        level,
        parentId,
        includeUnloaded = false,
    }: {
        recordData: PartialCollectionValueWithIds<T> | PartialCollectionValue<T> | InternalValue<T>;
        shouldNotifySubscribers?: boolean;
        level?: number;
        parentId?: string;
        includeUnloaded?: boolean;
    }): PartialCollectionValueWithIds<T> {
        if (recordData._id && this.db.findOne({ id: String(recordData._id), level, includeUnloaded })) {
            this.setRecordValue({
                recordData: recordData as InternalValue<T>,
                shouldNotifySubscribers,
                level,
                upsert: true,
            });
            return this.getRecordWithChildren({
                id: String(recordData._id),
                level,
            }) as PartialCollectionValueWithIds<T>;
        }

        if (
            recordData._id &&
            this.db.findOne({ id: String(recordData._id), level, includeUnloaded, isUncommitted: true })
        ) {
            this.setRecordValue({
                recordData: recordData as InternalValue<T>,
                shouldNotifySubscribers,
                level,
                upsert: true,
                isUncommitted: true,
            });
            return this.getRecordWithChildren({
                id: String(recordData._id),
                level,
                isUncommitted: true,
            }) as PartialCollectionValueWithIds<T>;
        }

        if (level !== undefined && level !== 0 && parentId === undefined) {
            throw new Error(
                `Cannot create a record of type '${this.getNode(level)}' without a 'parentId': ${{
                    level,
                    recordValue: recordData,
                }}`,
            );
        }

        return this.addRecord({
            recordData,
            level,
            shouldNotifySubscribers,
            parentId,
        });
    }

    public setRecordValue({
        recordData,
        shouldNotifySubscribers = true,
        level = 0,
        upsert = false,
        toBeMarkedAsDirty,
        resetRecordAction = false,
        isOrganicChange = false,
        changedColumnId,
        isUncommitted,
    }: {
        recordData: PartialCollectionValueWithIds<T>;
        shouldNotifySubscribers?: boolean;
        level?: number;
        upsert?: boolean;
        toBeMarkedAsDirty?: string[];
        resetRecordAction?: boolean;
        isOrganicChange?: boolean;
        changedColumnId?: string;
        isUncommitted?: boolean;
    }): void {
        const childProperty = this.levelMap?.[level] || '';
        const { [childProperty]: childData, ...updateData } = recordData;
        const { _id: parentId } = this.findAndUpdate({
            recordData: updateData as PartialCollectionValue<T>,
            level,
            shouldNotifySubscribers,
            upsert,
            toBeMarkedAsDirty,
            resetRecordAction,
            changedColumnId,
            isOrganicChange,
            isUncommitted,
        }) as InternalValue<T>;
        get<any, string>(recordData, String(childProperty), []).forEach((child: any) => {
            this.setRecordValue({
                recordData: { ...child, __parentId: parentId },
                shouldNotifySubscribers,
                level: level + 1,
                upsert,
                toBeMarkedAsDirty,
                isOrganicChange,
                changedColumnId,
            });
        });
    }

    protected addNestedLevelRecursive({
        recordData,
        action = RecordActionType.ADDED,
        dirty = true,
        level,
        parentId,
        shouldNotifySubscribers,
    }: {
        action?: RecordActionType | 'none';
        dirty?: boolean;
        level: number;
        parentId: string;
        recordData: InternalValue<T>;
        shouldNotifySubscribers: boolean;
    }): void {
        if (this.levelMap === undefined) {
            return;
        }
        const childProperty = this.levelMap[level];
        const children: any[] = (recordData as any)[childProperty] || [];
        children.forEach(childRecordData => {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            const { [this.levelMap?.[level + 1] ?? '']: _, ...data } = childRecordData;
            const newChildRecord = this.db.addRecord({
                data: { ...data, __parentId: parentId },
                level: level + 1,
                beforeInsert: record => this.formatRecordValue(this.repackRecordData(record), undefined, true, level),
                afterInsert: ({ action, record }) =>
                    shouldNotifySubscribers && this.notifyValueChangeSubscribers(action, record),
                cleanMetadata: false,
                action,
                dirty,
            });

            if (
                this.levelMap &&
                level + 1 < objectKeys(this.levelMap).length &&
                this.levelMap[level + 1] !== undefined
            ) {
                this.addNestedLevelRecursive({
                    recordData: childRecordData,
                    level: level + 1,
                    shouldNotifySubscribers,
                    parentId: newChildRecord._id,
                    dirty,
                    action,
                });
            }
        });
    }

    public addRecord({
        recordData,
        level,
        shouldNotifySubscribers = true,
        parentId,
        cleanMetadata = true,
        action = RecordActionType.ADDED,
        isUncommitted = false,
        dirty = true,
    }: {
        recordData: PartialCollectionValue<T> | Omit<PartialCollectionValue<T>, '_id'>;
        level?: number;
        shouldNotifySubscribers?: boolean;
        parentId?: string;
        cleanMetadata?: boolean;
        action?: RecordActionType | 'none';
        isUncommitted?: boolean;
        dirty?: boolean;
    }): PartialCollectionValueWithIds<T> {
        if (level === undefined || this.levelMap === undefined) {
            return this.db.addRecord({
                data: recordData,
                afterInsert: ({ action: dropdownAction, record }) =>
                    shouldNotifySubscribers && this.notifyValueChangeSubscribers(dropdownAction, record, isUncommitted),
                level,
                beforeInsert: record => this.formatRecordValue(this.repackRecordData(record)),
                cleanMetadata,
                action,
                isUncommitted,
                dirty,
            });
        }

        // eslint-disable-next-line @typescript-eslint/naming-convention
        const { [this.levelMap?.[level] ?? '']: _, ...data } = recordData;
        const newRecord = this.db.addRecord({
            data: data as PartialCollectionValue<T>,
            level,
            beforeInsert: record => this.formatRecordValue(this.repackRecordData(record)),
            afterInsert: ({ action: insertAction, record }) =>
                shouldNotifySubscribers && this.notifyValueChangeSubscribers(insertAction, record),
            cleanMetadata: false,
            parentId,
            isUncommitted,
            dirty,
        });

        this.addNestedLevelRecursive({
            recordData: recordData as any,
            level,
            shouldNotifySubscribers,
            parentId: newRecord._id,
        });
        return this.getRecordWithChildren({
            id: newRecord._id,
            level,
            isUncommitted,
            cleanMetadata,
        }) as PartialCollectionValueWithIds<T>;
    }

    public removeRecord({
        recordId,
        shouldNotifySubscribers = true,
        level,
        includeUnloaded = false,
    }: {
        recordId: string;
        shouldNotifySubscribers?: boolean;
        level?: number;
        includeUnloaded?: boolean;
    }): void {
        const record = this.db.findOne({ id: recordId, level, cleanMetadata: false, includeUnloaded });

        if (!record) {
            throw new Error(
                `No record found with ID '${recordId}' in the collection value, therefore we cannot execute the removal. Are you sure you use a valid ID?`,
            );
        }

        if (record.__action === RecordActionType.ADDED) {
            // If this record has never been saved to the database, we don't need to keep it.
            this.db.remove({
                data: record,
                afterRemove: () => {
                    this.db
                        .findAll({ level: (level || 0) + 1, parentId: recordId })
                        .value({ cleanMetadata: false })
                        .forEach(r =>
                            this.removeRecord({ recordId: r._id, shouldNotifySubscribers: false, level: r.__level }),
                        );
                    if (shouldNotifySubscribers) {
                        this.notifyValueChangeSubscribers(RecordActionType.REMOVED, record);
                    }
                },
            });
        } else {
            const updatedData = {
                ...record,
                __dirty: true,
                __validationState: undefined,
                __action: RecordActionType.REMOVED,
            };
            this.db.update({
                data: updatedData,
                beforeUpdate: toBeUpdated =>
                    this.formatRecordValue(this.repackRecordData(toBeUpdated), undefined, true, level),
                afterUpdate: () => {
                    this.db
                        .findAll({ level: (level || 0) + 1, parentId: recordId })
                        .value({ cleanMetadata: false })
                        .forEach(r =>
                            this.removeRecord({ recordId: r._id, shouldNotifySubscribers: false, level: r.__level }),
                        );
                    if (shouldNotifySubscribers) {
                        this.notifyValueChangeSubscribers(RecordActionType.REMOVED, updatedData);
                    }
                },
            });
        }

        dispatchUpdateNestedFieldValidationErrorsForRecord({
            screenId: this.screenId,
            elementId: this.elementId,
            recordId,
            level,
        });
        dispatchCloseSideBar({ screenId: this.screenId, elementId: this.elementId, recordId });
        this.notifyValidationSubscribers([record]);
    }

    public getData({
        cleanMetadata = true,
        includePhantom,
        inGroup = false,
        isUncommitted,
        level = 0,
        limit = 20,
        noLimit = false,
        parentId,
        temporaryRecords,
        where = transformToLokiJsFilter(this.getInternalFilter({ level, parentId })),
    }: {
        cleanMetadata?: boolean;
        includePhantom?: boolean;
        inGroup?: boolean;
        isUncommitted?: boolean;
        level?: number;
        limit?: number;
        noLimit?: boolean;
        parentId?: string | null;
        temporaryRecords?: PartialCollectionValueWithIds<T>[];
        where?: LokiQuery<InternalValue<T> & LokiObj>;
    } = {}): any[] {
        return this.db.withTemporaryRecords(db => {
            let data = db
                .findAll({
                    where: {
                        __action: { $ne: RecordActionType.REMOVED },
                        ...(this.sublevelProperty ? {} : { __isGroup: { $ne: !inGroup } }),
                        ...where,
                    },
                    level,
                    parentId,
                    includePhantom,
                    isUncommitted,
                })
                .sort({
                    orderBy: this.getOrderBy({ level, parentId }),
                    columnDefinitions: this.getColumnDefinitions(level),
                });
            if (!noLimit) {
                data = data.take(limit);
            }
            return data.value({ cleanMetadata });
        }, temporaryRecords);
    }

    public getAllDataAsTree({
        excludeEmptyChildCollections = false,
        cleanInputTypes = true,
    }: {
        excludeEmptyChildCollections?: boolean;
        cleanInputTypes?: boolean;
    } = {}): any[] {
        const records = this.db.findAll({ level: null, parentId: null }).value({ cleanMetadata: false });
        return this.getTreeFromNormalizedCollection({
            records,
            includeActions: false,
            excludeEmptyChildCollections,
            cleanInputTypes,
        });
    }

    /** Creates a change dataset understood by the server */
    public getChangedRecordsAsTree({
        excludeEmptyChildCollections = true,
    }: {
        excludeEmptyChildCollections?: boolean;
    } = {}): any[] {
        const records = this.db
            .findAll({ where: { __action: { $exists: true } as any }, level: null, parentId: null })
            .value({ cleanMetadata: false });
        return this.getTreeFromNormalizedCollection({
            records,
            excludeEmptyChildCollections,
            removeNegativeId: true,
        });
    }

    /** Creates a change dataset understood by the server */
    public getNormalizedChangedRecords(isRequestingDefaults = false): any[] {
        return this.db
            .findAll({ where: { __action: { $exists: true } } as any, level: null, parentId: null })
            .value({ cleanMetadata: false })
            .map(r => {
                return new CollectionDataRow<T>(r, this.columnDefinitions, this.nodes, this.nodeTypes, {
                    removeNegativeId: false,
                    includeActions: true,
                    isRequestingDefaults,
                    isTree: !!this.sublevelProperty,
                }).getChangeset();
            });
    }

    /** Gets all new records */
    public getNewRecords(): any[] {
        return this.db
            .findAll({ where: { __action: RecordActionType.ADDED } as any, level: null, parentId: null })
            .value({ cleanMetadata: false })
            .map(r => {
                return new CollectionDataRow<T>(r, this.columnDefinitions, this.nodes, this.nodeTypes, {
                    removeNegativeId: false,
                    includeActions: false,
                    isRequestingDefaults: false,
                    isTree: !!this.sublevelProperty,
                }).getChangeset();
            });
    }

    public async refreshRecord({
        recordId,
        level = 0,
        skipUpdate = false,
    }: {
        recordId: string;
        level?: number;
        skipUpdate?: boolean;
    }): Promise<any> {
        if (this.isTransient) {
            throw new Error('Transient collections cannot be refreshed.');
        }
        const pageDefinition = getPageDefinitionFromState(this.screenId);
        const nodeName = this.getNode(level);
        const nestedFields = this.getColumnDefinitions(level);
        const result = await fetchCollectionRecord(pageDefinition, nestedFields, nodeName, recordId);
        if (!result) {
            return null;
        }

        const recordValue = splitValueToMergedValue(this.repackRecordData(result));
        if (!skipUpdate) {
            this.setRecordValue({
                recordData: recordValue,
                shouldNotifySubscribers: true,
                level,
                upsert: false,
                toBeMarkedAsDirty: [],
                resetRecordAction: true,
            });
        }
        return splitValueToMergedValue(recordValue);
    }

    public startRecordTransaction({ recordId, recordLevel }: { recordId: string; recordLevel?: number }): void {
        this.db.startRecordTransaction({ recordId, recordLevel });
    }

    public commitRecord({
        recordId,
        recordLevel,
    }: {
        recordId: string;
        recordLevel?: number;
    }): PartialCollectionValueWithIds<T> {
        const isNewRecord = this.db.commitRecord({ recordId, recordLevel });
        const updatedData = this.getRecordByIdAndLevel({ id: recordId, level: recordLevel });
        this.notifyValueChangeSubscribers(
            isNewRecord ? RecordActionType.ADDED : RecordActionType.MODIFIED,
            updatedData,
        );

        if (isNewRecord) {
            const fieldProperties = getStore().getState().screenDefinitions[this.screenId].metadata
                .uiComponentProperties[this.elementId] as TableProperties;
            if (fieldProperties.canAddNewLine && !fieldProperties.isPhantomRowDisabled) {
                setTimeout(() => {
                    this.createNewPhantomRow({ level: recordLevel, parentId: updatedData.__parentId });
                }, 500);
            }
        }

        return updatedData;
    }

    public cancelRecordTransaction({ recordId, recordLevel }: { recordId: string; recordLevel?: number }): void {
        this.db.cancelRecordTransaction({ recordId, recordLevel });
    }

    // This method will retrieve the previous or next record in the collection and the server if the index is major than our collection and we have next page
    private async getRecordByOffset({
        offset,
        recordId,
        recordLevel,
        tableProperties,
    }: {
        offset: number;
        recordId: string;
        recordLevel?: number;
        tableProperties?: TableProperties;
    }): Promise<PartialCollectionValueWithIds<T> | null> {
        const record: any = this.getRawRecord({ id: recordId, level: recordLevel, cleanMetadata: false });
        if (!record) {
            return null;
        }
        const level = record.__level;
        const parentId = record.__parentId;
        const records = this.getData({ level, parentId, noLimit: true });
        const recordIndex = records.findIndex(r => r._id === recordId);

        if (recordIndex === -1) {
            return null;
        }
        const newRecordIndex = recordIndex + offset;
        if (newRecordIndex >= 0 && newRecordIndex < records.length) {
            return records[newRecordIndex];
        }
        if (newRecordIndex >= records.length && this.hasNextPage && tableProperties) {
            const nextPage = await this.getPageWithCurrentQueryArguments({
                tableFieldProperties: tableProperties,
                pageSize: 20,
                pageNumber: get(this.lastFetchedPage, [level ?? 0, parentId ?? 0], -1) + 1,
                cursor: record.__cursor,
                level: recordLevel,
            });
            if (!nextPage) {
                return null;
            }
            return nextPage[0];
        }
        return null;
    }

    //  This method will retrieve the next record in the collection if it's the last one locally it will call the server to get more pages until the last page
    public async getNextRecord({
        recordId,
        recordLevel,
        tableProperties,
    }: {
        recordId: string;
        recordLevel?: number;
        tableProperties: TableProperties;
    }): Promise<PartialCollectionValueWithIds<T> | null> {
        return this.getRecordByOffset({ offset: 1, recordId, recordLevel, tableProperties });
    }

    // This method will retrieve the previous record from the collection
    public async getPreviousRecord({
        recordId,
        recordLevel,
    }: {
        recordId: string;
        recordLevel?: number;
    }): Promise<PartialCollectionValueWithIds<T> | null> {
        return this.getRecordByOffset({ offset: -1, recordId, recordLevel });
    }

    // This method will retrieve the parent node, parent id and current level of the provided node.
    public getParentNodeAndParentIdFromChildNode({ node }: { node: string }): {
        childLevel: number;
        parentNode?: string;
        parentId?: string;
        parentBind?: string;
    } {
        const level = this.nodes.indexOf(node);
        if (level === -1 || level === 0) {
            return { childLevel: 0 };
        }
        const parentNode = this.getNode(level - 1);
        const parentRecord = this.db.findOne({
            level,
            cleanMetadata: false,
        }) as CollectionValueType;
        return {
            parentId: parentRecord.__parentId,
            parentNode,
            childLevel: level,
            parentBind: this.levelMap && this.levelMap[level - 1],
        };
    }

    private readonly getNode = (level?: number | null): string => {
        if (this.sublevelProperty) {
            return this.nodes[0];
        }

        const targetLevel = isNil(level) ? 0 : level;
        return this.nodes[targetLevel];
    };

    public readonly getColumnDefinitions = (level?: number | null): NestedField<ScreenBase, NestedFieldTypes, T>[] => {
        if (this.sublevelProperty) {
            return this.columnDefinitions[0];
        }

        const targetLevel = isNil(level) ? 0 : level;
        return this.columnDefinitions[targetLevel];
    };
}
