import type { AppThunkDispatch } from '../redux';
import { ActionType } from '../redux';
import type { XtremAppState } from '../redux/state';
import {
    getMainPageDefinitionFromState,
    getPageDefinitionFromState,
    hasAnyDirtyScreenDefinitions,
} from '../utils/state-utils';
import type { DialogControl } from './dialog-service';
import { confirmationDialog } from './dialog-service';
import { localize } from './i18n-service';

export const openDirtyPageConfirmationDialog = (screenId: string): DialogControl =>
    confirmationDialog(
        screenId,
        'warn',
        localize('@sage/xtrem-ui/unsaved-changes-title', 'Unsaved changes'),
        localize('@sage/xtrem-ui/unsaved-changes-content', 'Leave and discard your changes?'),
        {
            acceptButton: { text: localize('@sage/xtrem-ui/unsaved-changes-yes', 'Yes') },
            cancelButton: { text: localize('@sage/xtrem-ui/unsaved-changes-go-back', 'Go back') },
            size: 'small',
            reverseButtons: true,
            isDirtyCheck: true,
        },
    );

export const onPageDirtyChange = (state: XtremAppState, screenId: string, isDirty: boolean): void => {
    const pageDefinition = getPageDefinitionFromState(screenId, state);
    if (!pageDefinition || !pageDefinition.page || !pageDefinition.page.$standardDuplicateAction) return;
    if (isDirty) {
        pageDefinition.page.$standardDuplicateAction.isDisabled = true;
    } else {
        pageDefinition.page.$standardDuplicateAction.isDisabled = false;
    }
};

export const notifyConsumerAboutDirtyStatus = (
    state: XtremAppState,
    isDirty = hasAnyDirtyScreenDefinitions(state),
    dispatch?: AppThunkDispatch,
): Promise<void> => {
    if (state.applicationContext?.onDirtyStatusChange) {
        state.applicationContext.onDirtyStatusChange(isDirty, () => {
            const mainPage = getMainPageDefinitionFromState(state);

            const confirmationPromise =
                mainPage && isDirty ? openDirtyPageConfirmationDialog(mainPage.metadata.screenId) : Promise.resolve();

            confirmationPromise.catch(() => {
                if (dispatch && state.navigation.isBackNavigation) {
                    dispatch({ type: ActionType.SetBackNavigation, value: false });
                }
            });

            return confirmationPromise;
        });
    }
    return Promise.resolve();
};
