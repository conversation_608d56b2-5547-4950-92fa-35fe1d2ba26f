import type { Constructible } from '../types';
import { getPageMetadata } from './page-metadata';
import type { ScreenBaseDefinition } from './screen-base-definition';
import type { Sticker } from './sticker';

export interface StickerDefinition extends ScreenBaseDefinition {
    sticker: Sticker;
}

export const getStickerDefinition = (stickerConstructor: Constructible<Sticker>): StickerDefinition => {
    return {
        accessBindings: {},
        dirtyStates: {},
        errors: {},
        internalErrors: {},
        metadata: getPageMetadata(stickerConstructor),
        // eslint-disable-next-line new-cap
        sticker: new stickerConstructor(),
        userSettings: {},
        type: 'sticker',
        values: {},
    };
};
