import type { Dict } from '@sage/xtrem-shared';

const keyPrefix = 'xtrem_';

export type PrimitiveType = string | number | boolean | null;

export interface Storage {
    set: (key: string, value: string | number | boolean) => void;
    get: (key: string) => PrimitiveType;
    getAll: () => Dict<PrimitiveType>;
    remove: (key: string) => void;
}

/** Add a single entry from the storage */
export const set = (key: string, value: string | number | boolean): void => {
    window.sessionStorage.setItem(keyPrefix + key, JSON.stringify(value));
};

/** Get a single entry from the storage */
export const get = (key: string): PrimitiveType => {
    const item = window.sessionStorage.getItem(keyPrefix + key);
    if (item) {
        return JSON.parse(item);
    }
    return null;
};

/** Get all entries from the storage as a dictionary */
export const getAll = (): Dict<PrimitiveType> => {
    const dict: Dict<PrimitiveType> = {};
    for (let i = 0; i < window.sessionStorage.length; i += 1) {
        const key = window.sessionStorage.key(i);
        if (key && key.indexOf(keyPrefix) === 0) {
            const replacedKey = key.replace(keyPrefix, '');
            dict[replacedKey] = get(replacedKey);
        }
    }

    return dict;
};

/** Removes a single entry from the storage */
export const remove = (key: string): void => {
    window.sessionStorage.removeItem(keyPrefix + key);
};
