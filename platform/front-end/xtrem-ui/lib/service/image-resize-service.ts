import Pica from 'pica';

const picaConfig: Pica.PicaOptions = {};
const pica = new Pica(picaConfig);
const IMAGE_WIDTH = 1200;
const IMAGE_HEIGHT = 800;

export async function resizeImage(file: File): Promise<Blob> {
    // If the file is not an image, reject the promise
    if (!file.type.startsWith('image/')) {
        return Promise.reject(new Error('File is not an image'));
    }
    // if the file is a SVG file we return without resizing
    if (file.type === 'image/svg+xml') {
        return Promise.resolve(file);
    }

    // Create an image element and load the file into it
    const img = new Image();
    const url = URL.createObjectURL(file);
    img.src = url;

    await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
    });

    // Calculate the new dimensions while maintaining the aspect ratio
    const { width: newWidth, height: newHeight } = calculateNewDimensions(img);

    // Create a canvas and draw the image onto it
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = img.width;
    canvas.height = img.height;
    if (!ctx) {
        throw new Error('Could not get 2d context');
    }
    ctx.drawImage(img, 0, 0);

    // Create new Canvas with the desired dimensions
    const newCanvas = document.createElement('canvas');
    newCanvas.width = newWidth;
    newCanvas.height = newHeight;

    // Resize the canvas using Pica
    const resizedCanvas = await pica.resize(canvas, newCanvas);

    // Convert the resized canvas to a JPEG Blob with the specified quality
    const blob = await pica.toBlob(resizedCanvas, 'image/jpeg', 0.65);

    // Clean up the object URL
    URL.revokeObjectURL(url);

    return blob;
}

export function calculateNewDimensions(img: HTMLImageElement): { width: number; height: number } {
    let newWidth = img.width;
    let newHeight = img.height;

    if (img.width > IMAGE_WIDTH || img.height > IMAGE_HEIGHT) {
        const aspectRatio = img.width / img.height;
        if (img.width > img.height) {
            newWidth = IMAGE_WIDTH;
            newHeight = IMAGE_WIDTH / aspectRatio;
        } else {
            newHeight = IMAGE_HEIGHT;
            newWidth = IMAGE_HEIGHT * aspectRatio;
        }
    }

    return { width: newWidth, height: newHeight };
}
