import type { AccessStatus, Dict } from '@sage/xtrem-shared';
import { cloneDeep } from 'lodash';
import type { Constructible } from '../types';
import type { Page } from './page';
import { getPageMetadata } from './page-metadata';
import type { ScreenBaseDefinition } from './screen-base-definition';
import type { QueryParameters } from '../utils/types';
import { applyDuplicationBusinessActions, applyDuplicationLayoutStructure } from '../utils/duplication-utils';

export type AccessBindings = Dict<Dict<AccessStatus>>;

export interface PageDefinition extends ScreenBaseDefinition {
    activeSection: string | null;
    insightCount: number;
    is360ViewOn?: boolean;
    isInDialog: boolean;
    isMainPage: boolean;
    isReady: boolean;
    page: Page;
    path: string;
    queryParameters: QueryParameters;
    selectedRecordId: string | null;
}

const PAGE_DEFINITION_ROOT: Partial<PageDefinition> = {
    accessBindings: {},
    dirtyStates: {},
    errors: {},
    insightCount: 0,
    internalErrors: {},
    isInDialog: false,
    isReady: false,
    queryParameters: {},
    selectedRecordId: null,
    values: {},
};

export const getNewPageDefinition = (): Partial<PageDefinition> => cloneDeep(PAGE_DEFINITION_ROOT);

export const getPageDefinition = (
    pageConstructor: Constructible<Page>,
    /** Full artifact path */
    path: string,
    isMainPage: boolean,
    isDuplicate = false,
): PageDefinition => {
    const pageDefinition: PageDefinition = {
        ...(getNewPageDefinition() as PageDefinition),
        metadata: getPageMetadata(pageConstructor),
        // eslint-disable-next-line new-cap
        page: new pageConstructor(),
        path,
        isMainPage,
        type: 'page',
    };

    if (isDuplicate) {
        applyDuplicationBusinessActions(pageDefinition);
        applyDuplicationLayoutStructure(pageDefinition);
        // Remove businessActionsExtensionsThunk from the pageDefinition
        pageDefinition.metadata.businessActionsExtensionsThunk = [];
    }

    return pageDefinition;
};
