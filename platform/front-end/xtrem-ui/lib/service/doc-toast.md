PATH: XTREEM/Client+Framework/Toast+API

This document describes how application developers can show toast-like toasts to end users.

## Introduction

The UI framework has a very simple way of displaying toasts to users. These toasts are displayed in the top-right corner of the window.

## Adding toasts:

```ts
onClick() {
    this.$.showToast('The order is successfully recorded', { type: 'success', timeout: 5000 });
}
```

The `showToast` function takes two arguments:

-   **content**: The text content of the toast.
-   **options**: _(optional)_ The options that determine the type and the timeout of the toast toast.
    -   **type**: _(optional)_ The type determines the icon and the colour of the toast, be default it is rendered as an "info" message. The available types are `info`, `success`, `error`, `help`, `warning` and `new`.
    -   **timeout**: _(optional)_ The timeout in milliseconds. When the time is up, the toast is automatically removed from the screen. The default value is 4000 ms.

## Clearing toasts

```ts
onClick() {
    this.$.removeToasts();
}
```

Toasts can be cleared by calling the `this.$.removeToasts()` method. This method removes all toasts, including the ones raised by the framework.

## Notes

-   All toasts can be closed by the user by the "X" in the right side of the toast.
-   There are no event listeners available on toasts
-   Multiple toasts opened at the same time are queued up

## Sandbox

Check out the dialogs on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Toasts).
