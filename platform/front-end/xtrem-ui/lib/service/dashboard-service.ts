import {
    DateFilterPeriodType,
    type AbstractResponsiveWidget,
    type BarChartProps,
    type CallToActionDefinition as BmsCallToActionDefinition,
    type CollectionDateFilter,
    type ContactCardProps,
    type DropdownItem,
    type DropdownItemDivider,
    type GaugeProps,
    type LineChartProps,
    type PeriodDetails,
    type PieChartProps,
    type SimpleIndicatorProps,
    type StatiContentProps,
    type TableWidgetProps,
    type VisualProcessProps,
    type WidgetCollectionItem,
    type WidgetCollectionItemDefinition,
} from '@sage/bms-dashboard';
import type { XLinkProps } from '@sage/visual-process-editor';
import type { ClientNode } from '@sage/xtrem-client';
import { objectKeys, type Dashboard, type DashboardItem, type Dict } from '@sage/xtrem-shared';
import { MD5 } from 'crypto-js';
import { dropWhile, isEmpty, isEqual, escape as lodashEscape, noop, sortBy, uniqBy } from 'lodash';
import memoizeOne from 'memoize-one';
import * as showdown from 'showdown';
import { getImageUrlFromValue } from '../component/field/image/image-utils';
import type { VoidPromise } from '../component/field/traits';
import type { DashboardBreakpoint, WidgetDefinitionWithLoadingState } from '../dashboard/dashboard-types';
import { GENERIC_PLATFORM_WIDGET_PREFIX } from '../dashboard/generic-widgets';
import type { ContactCardWidgetProperties } from '../dashboard/widgets';
import type { AbstractWidget, CallToActionDefinition, WidgetDefinition } from '../dashboard/widgets/abstract-widget';
import { WidgetType } from '../dashboard/widgets/abstract-widget';
import type { BarChartWidgetProperties } from '../dashboard/widgets/bar-chart-widget-decorator';
import type { SecondaryAxisDefinition } from '../dashboard/widgets/chart-types';
import type { GaugeWidgetProperties } from '../dashboard/widgets/gauge-widget-decorator';
import type {
    IndicatorTileGroupContent,
    IndicatorTileGroupWidgetProperties,
} from '../dashboard/widgets/indicator-tile-group-widget-decorator';
import type { IndicatorTileWidgetProperties } from '../dashboard/widgets/indicator-tile-widget-decorator';
import type { LineChartWidgetProperties } from '../dashboard/widgets/line-chart-widget-decorator';
import type { PieChartWidgetProperties } from '../dashboard/widgets/pie-chart-widget-decorator';
import type { StaticContentWidgetProperties } from '../dashboard/widgets/static-content-widget-decorator';
import type {
    ColorVariant,
    TableDropDownMenu,
    TableWidgetOptions,
    TableWidgetProperties,
} from '../dashboard/widgets/table-widget-decorator';
import type { VisualProcessWidgetProperties } from '../dashboard/widgets/visual-process-widget-decorator';
import * as xtremRedux from '../redux';
import type { DashboardListItem } from '../redux/state';
import { xtremConsole } from '../utils/console';
import { DASHBOARD_SCREEN_ID, PREVIEW_WIDGET_ID } from '../utils/constants';
import { resolveDetailedIcon } from '../utils/detailed-icons-utils';
import { resolveImageUrl } from '../utils/vp-utils';
import { getPlatformStringLiteralsSchemaFragment } from './graphql-query-builder';
import { executeGraphqlQuery } from './graphql-utils';
import { localize } from './i18n-service';
import { processStringLiteralsFromResponse } from './screen-loader-service';
import { executeVisualProcessLink } from './visual-process-service';

export const DASHBOARD_DETAILS_QUERY = {
    _id: true,
    title: true,
    isTemplate: true,
    children: {
        _id: true,
        type: true,
        settings: true,
        positions: {
            x: true,
            y: true,
            breakpoint: true,
            h: true,
            w: true,
        },
    },
};

export const fetchCurrentDashboardDefinition = async (
    group: string,
    shouldFetchPlatformLiterals = false,
): Promise<{
    dashboard: Dashboard | null;
    stringLiterals: Dict<string>;
    availableDashboards: DashboardListItem[] | null;
    canEditDashboards: boolean;
}> => {
    // Load source code of the page and its extensions from the server
    const query: any = {
        dashboard: {
            selectedDashboard: {
                ...DASHBOARD_DETAILS_QUERY,
                __args: {
                    group,
                },
            },
            availableDashboards: {
                _id: true,
                title: true,
                __args: {
                    group,
                },
            },
            canEditDashboards: true,
        },
    };

    if (shouldFetchPlatformLiterals) {
        query.strings = getPlatformStringLiteralsSchemaFragment();
    }

    const { data } = await executeGraphqlQuery({
        query,
        endpoint: '/metadata',
    });
    const dashboard = data.dashboard.selectedDashboard as Dashboard;
    if (!dashboard) {
        return {
            dashboard: null,
            stringLiterals: {},
            availableDashboards: null,
            canEditDashboards: data.dashboard.canEditDashboards,
        };
    }
    dashboard.children = sortBy(
        dashboard.children?.map((c: DashboardItem) => ({
            ...c,
            settings: JSON.parse(c.settings as any),
        })) || [],
        ['y', 'x'], // We need to reorder by y and x to maintain the taborder in the DOM
    );

    const stringLiterals: Dict<string> = shouldFetchPlatformLiterals
        ? processStringLiteralsFromResponse(undefined, data)
        : {};

    const availableDashboards = data.dashboard.availableDashboards;

    return { dashboard, stringLiterals, availableDashboards, canEditDashboards: data.dashboard.canEditDashboards };
};

export const fetchWidgetCategories = async (): Promise<Dict<string>> => {
    const response = await executeGraphqlQuery({
        query: { dashboard: { getWidgetCategories: { key: true, title: true } } },
        endpoint: '/metadata',
    });
    if (Array.isArray(response.data?.dashboard?.getWidgetCategories)) {
        return response.data.dashboard.getWidgetCategories.reduce(
            (prevValue: Dict<string>, currentValue: { key: string; title: string }) => {
                return { ...prevValue, [currentValue.key]: currentValue.title };
            },
            {} as Dict<string>,
        );
    }

    throw new Error('Failed to load widget categories from the server');
};

export const fetchDashboardTemplates = async (group: string): Promise<DashboardListItem[]> => {
    const query = {
        dashboard: {
            templates: {
                __args: {
                    group,
                },
                _id: true,
                title: true,
                description: true,
                listIcon: true,
            },
        },
    };
    const { data } = await executeGraphqlQuery({
        query,
        endpoint: '/metadata',
    });
    return data.dashboard.templates;
};

export const setSelectedDashboard = async (selectedId: string, group: string): Promise<Dashboard> => {
    const { data } = await executeGraphqlQuery({
        query: {
            mutation: {
                dashboard: {
                    selectDashboard: {
                        __args: {
                            dashboardId: selectedId,
                            group,
                        },
                        ...DASHBOARD_DETAILS_QUERY,
                    },
                },
            },
        },
        endpoint: '/metadata',
    });
    if (data.dashboard.selectDashboard) {
        const children = data.dashboard.selectDashboard.children?.map((c: any) => ({
            ...c,
            settings: c.settings ? JSON.parse(c.settings) : {},
        }));
        return { ...data.dashboard.selectDashboard, children };
    }

    throw new Error('Failed to select dashboard');
};

export const cloneDashboard = async (dashboardIdToClone: string): Promise<Dashboard> => {
    const { data, error } = await executeGraphqlQuery({
        query: {
            mutation: {
                dashboard: {
                    cloneDashboard: {
                        __args: {
                            dashboardId: dashboardIdToClone,
                        },
                        _id: true,
                        title: true,
                    },
                },
            },
        },
        endpoint: '/metadata',
    });

    if (!error && data.dashboard.cloneDashboard) {
        return data.dashboard.cloneDashboard;
    }

    throw new Error('Failed to clone dashboard');
};

export const deleteDashboard = async (dashboardIdToDelete: string): Promise<void> => {
    const { error } = await executeGraphqlQuery({
        query: {
            mutation: {
                dashboard: {
                    deleteDashboard: {
                        __args: {
                            dashboardId: dashboardIdToDelete,
                        },
                    },
                },
            },
        },
        endpoint: '/metadata',
    });

    if (error) {
        xtremConsole.error(error);
        throw new Error('Failed to delete dashboard');
    }
};

export const createEmptyDashboard = async (group: string): Promise<Dashboard> => {
    const { data, error } = await executeGraphqlQuery({
        query: {
            mutation: {
                dashboard: {
                    createDashboard: {
                        ...DASHBOARD_DETAILS_QUERY,
                        __args: {
                            group,
                        },
                    },
                },
            },
        },
        endpoint: '/metadata',
    });

    if (!error && data.dashboard.createDashboard) {
        return data.dashboard.createDashboard;
    }

    throw new Error('Failed to create dashboard');
};

export const updateDashboard = async (dashboardDefinition: Dashboard): Promise<Dashboard> => {
    const { data, error } = await executeGraphqlQuery({
        query: {
            mutation: {
                dashboard: {
                    updateDashboardLayout: {
                        __args: {
                            dashboardId: dashboardDefinition._id,
                            layout: JSON.stringify(dashboardDefinition),
                        },
                        ...DASHBOARD_DETAILS_QUERY,
                    },
                },
            },
        },
        endpoint: '/metadata',
    });

    if (!error && data.dashboard.updateDashboardLayout) {
        data.dashboard.updateDashboardLayout.children = sortBy(
            data.dashboard.updateDashboardLayout.children?.map((c: DashboardItem) => ({
                ...c,
                settings: JSON.parse(c.settings as any),
            })) || [],
            ['y', 'x'], // We need to reorder by y and x to maintain the taborder in the DOM
        );
        return data.dashboard.updateDashboardLayout;
    }

    throw new Error('Failed to update dashboards.');
};
export interface XtremUiWidgetCategory extends ClientNode {
    title: string;
    sortValue?: number;
}
export interface XtremUiWidget extends ClientNode {
    category: XtremUiWidgetCategory;
    title: string;
    description: string;
    type: string;
}
/**
 * Fetches widget list from the server and adds "Others" category to those which doesn't have one.
 * It also adds a _sortValue as the items should be categorized alphabetically, e
 * except the Others, so we cannot sort them just by their label
 * @returns
 */
export const getWidgetList = async (
    group?: string,
): Promise<{ widgets: XtremUiWidget[]; categories: XtremUiWidgetCategory[] }> => {
    const { data } = await executeGraphqlQuery({
        query: {
            widgets: {
                __args: {
                    filter: group ? { group } : {},
                },
                key: true,
                category: true,
                categoryLabel: true,
                type: true,
                title: true,
                description: true,
            },
        },
        endpoint: '/metadata',
    });

    if (!data.widgets || !Array.isArray(data.widgets)) {
        throw new Error('Failed to fetch widget list.');
    }

    const otherCategory: XtremUiWidgetCategory = {
        _id: '_OTHERS',
        title: localize('@sage/xtrem-ui/dashboard-widget-category-others', 'Others'),
        sortValue: Number.MAX_SAFE_INTEGER,
    };

    const uniqueServerCategories = uniqBy<XtremUiWidgetCategory>(
        data.widgets.filter((w: any) => !!w.category).map((w: any) => ({ _id: w.category, title: w.categoryLabel })),
        w => w._id,
    );

    const sortedServerCategories = sortBy(uniqueServerCategories, w => w.title).map(
        (category: XtremUiWidgetCategory, index: number) => ({ ...category, sortValue: index + 1 }),
    );
    const categories: XtremUiWidgetCategory[] = [...sortedServerCategories, otherCategory];
    const widgets = data.widgets.map(
        (w: any): XtremUiWidget => ({
            _id: w.key,
            type: w.type,
            description: w.description,
            title: w.title,
            category: w.category ? categories.find(c => c._id === w.category) || otherCategory : otherCategory,
        }),
    );

    return { widgets, categories };
};

const updateWidgetOptions = ({
    dashboardId,
    widgetId,
    group,
    shouldRefreshData,
    options = {},
    skipUnset = false,
}: {
    dashboardId: string;
    widgetId: string;
    group: string;
    shouldRefreshData: boolean;
    options?: any;
    skipUnset?: boolean;
}): void => {
    const thunkDispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
    const state = xtremRedux.getStore().getState();
    if (!state.loading.widgets[widgetId].isActualLoading) {
        thunkDispatch(xtremRedux.actions.setWidgetOptions(dashboardId, widgetId, group, options));

        if (shouldRefreshData) {
            refreshWidget(widgetId, group, skipUnset);
        }
    }
};

export const refreshWidget = (widgetId: string, group: string, skipUnset = false): void => {
    const thunkDispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
    thunkDispatch(xtremRedux.actions.loadWidgetData({ widgetId, forceRefetch: true, skipUnset, group }));
};

export const fetchWidgetPage = async ({
    queryArgs,
    widgetId,
    group,
}: {
    widgetId: string;
    queryArgs: LoadMoreRowsParams;
    group: string;
}): Promise<WidgetCollectionItem[]> => {
    const thunkDispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
    return thunkDispatch(
        xtremRedux.actions.loadWidgetData({ widgetId, forceRefetch: false, queryArgs, dataMode: 'ADD', group }),
    );
};

const getStaticContentWidgetConfig = (
    widgetId: string,
    widgetDefinition: WidgetDefinition,
    group: string,
): Partial<AbstractResponsiveWidget<StatiContentProps>> => {
    const staticContentProperties = widgetDefinition.properties as StaticContentWidgetProperties<any>;
    const mdContent =
        resolveWidgetProperty(staticContentProperties.content, group, widgetDefinition.widgetObject || widgetId) || '';
    const converter = new showdown.Converter();
    return {
        type: 'basic',
        data: {
            htmlContent: `<div style="font-size: 14px">${converter.makeHtml(lodashEscape(mdContent))}</div>`,
        },
    };
};

const getContactCardWidgetConfig = (
    widgetId: string,
    widgetDefinition: WidgetDefinition,
    group: string,
    isEditing: boolean,
): Partial<AbstractResponsiveWidget<ContactCardProps>> => {
    const contactCardWidgetProperties = widgetDefinition.properties as ContactCardWidgetProperties<any>;

    const data = resolveWidgetProperty(
        contactCardWidgetProperties.content,
        group,
        widgetDefinition.widgetObject || widgetId,
    );

    if (data && isEditing) {
        data.canAddNotes = false;
    }

    return {
        title: resolveWidgetProperty(
            contactCardWidgetProperties.title,
            group,
            widgetDefinition.widgetObject || widgetId,
        ),
        type: 'contact-card',
        data: {
            ...data,
            iconSrc: data?.iconSrc ? resolveDetailedIcon(data.iconSrc) : undefined,
            contacts: data?.contacts ?? {},
            numberOfAddresses: data?.numberOfAddresses ?? 0,
            numberOfContacts: data?.numberOfContacts ?? 0,
        },
    };
};

const createAxesConfig =
    (widgetDefinition: WidgetDefinition, group: string) =>
    (s: SecondaryAxisDefinition<any>): any => {
        const { onClick, tooltipContent } = s;
        return {
            ...s,
            onClick: onClick
                ? ({ record, primaryValue, secondaryValue }: any): void =>
                      resolveWidgetProperty(
                          onClick,
                          group,
                          widgetDefinition.widgetObject,
                          record,
                          primaryValue,
                          secondaryValue,
                      )
                : undefined,
            tooltipContent: tooltipContent
                ? ({ record, primaryValue, secondaryValue }: any): string | undefined =>
                      resolveWidgetProperty(
                          tooltipContent,
                          group,
                          widgetDefinition.widgetObject,
                          record,
                          primaryValue,
                          secondaryValue,
                      )
                : undefined,
        };
    };

const getBarChartWidgetConfig = (
    widgetId: string,
    widgetDefinition: WidgetDefinition,
    group: string,
): Partial<AbstractResponsiveWidget<BarChartProps>> => {
    const barChartProperties = widgetDefinition.properties as BarChartWidgetProperties<any>;
    const primaryAxis = resolveWidgetProperty(
        barChartProperties.primaryAxis,
        group,
        widgetDefinition.widgetObject || widgetId,
    );
    return {
        title: resolveWidgetProperty(barChartProperties.title, group, widgetDefinition.widgetObject || widgetId),
        type: 'bar-chart',
        data: {
            areAxesStacked: resolveWidgetProperty(
                barChartProperties.areAxesStacked,
                group,
                widgetDefinition.widgetObject || widgetId,
            ),
            areAxesSwapped: resolveWidgetProperty(
                barChartProperties.areAxesSwapped,
                group,
                widgetDefinition.widgetObject || widgetId,
            ),
            data:
                resolveWidgetProperty(barChartProperties.content, group, widgetDefinition.widgetObject || widgetId) ??
                [],
            isHistogram: resolveWidgetProperty(
                barChartProperties.isHistogram,
                group,
                widgetDefinition.widgetObject || widgetId,
            ),
            primaryAxis: {
                ...primaryAxis,
                bind: primaryAxis?.bind ?? '',
                title: primaryAxis?.title
                    ? resolveWidgetProperty(
                          barChartProperties.primaryAxis.title,
                          group,
                          widgetDefinition.widgetObject || widgetId,
                      )
                    : undefined,
            },
            primaryAxisLabel: resolveWidgetProperty(
                barChartProperties.primaryAxisLabel,
                group,
                widgetDefinition.widgetObject || widgetId,
            ),
            secondaryAxisLabel: resolveWidgetProperty(
                barChartProperties.secondaryAxisLabel,
                group,
                widgetDefinition.widgetObject || widgetId,
            ),
            secondaryAxes: barChartProperties.secondaryAxes.map(createAxesConfig(widgetDefinition, group)),
        },
    };
};

const getLineChartWidgetConfig = (
    widgetId: string,
    widgetDefinition: WidgetDefinition,
    group: string,
): Partial<AbstractResponsiveWidget<LineChartProps>> => {
    const lineChartProperties = widgetDefinition.properties as LineChartWidgetProperties<any>;
    const primaryAxis = resolveWidgetProperty(
        lineChartProperties.primaryAxis,
        group,
        widgetDefinition.widgetObject || widgetId,
    );
    return {
        title: resolveWidgetProperty(lineChartProperties.title, group, widgetDefinition.widgetObject || widgetId),
        type: 'line-chart',
        data: {
            data:
                resolveWidgetProperty(lineChartProperties.content, group, widgetDefinition.widgetObject || widgetId) ??
                [],
            primaryAxis: {
                ...primaryAxis,
                bind: primaryAxis?.bind ?? '',
                title: primaryAxis?.title
                    ? resolveWidgetProperty(
                          lineChartProperties.primaryAxis.title,
                          group,
                          widgetDefinition.widgetObject || widgetId,
                      )
                    : undefined,
            },
            secondaryAxes: lineChartProperties.secondaryAxes.map(createAxesConfig(widgetDefinition, group)),
            primaryAxisLabel: resolveWidgetProperty(
                lineChartProperties.primaryAxisLabel,
                group,
                widgetDefinition.widgetObject || widgetId,
            ),
            secondaryAxisLabel: resolveWidgetProperty(
                lineChartProperties.secondaryAxisLabel,
                group,
                widgetDefinition.widgetObject || widgetId,
            ),
        },
    };
};

const getPieChartWidgetConfig = (
    widgetId: string,
    widgetDefinition: WidgetDefinition,
    group: string,
): Partial<AbstractResponsiveWidget<PieChartProps>> => {
    const pieChartWidgetProperties = widgetDefinition.properties as PieChartWidgetProperties<any>;

    const data = (
        resolveWidgetProperty(pieChartWidgetProperties.content, group, widgetDefinition.widgetObject || widgetId) || []
    ).map((r: any) => ({
        ...r,
        i: r._id,
    }));

    return {
        title: resolveWidgetProperty(pieChartWidgetProperties.title, group, widgetDefinition.widgetObject || widgetId),
        type: 'pie-chart',
        data: {
            pieChartData: data,
            collectionItemDefinition: getRowDefinition({
                widgetDefinition,
                widgetId,
                group,
                tableProperties: pieChartWidgetProperties,
            }),
            isDonut: pieChartWidgetProperties.isDonut,
            hasCardView: pieChartWidgetProperties.hasCardView,
            filter: pieChartWidgetProperties.filter
                ? {
                      ...pieChartWidgetProperties.filter,
                      onChange: pieChartWidgetProperties.filter.onChange
                          ? (newValue: string | null): void => {
                                resolveWidgetProperty(
                                    pieChartWidgetProperties?.filter?.onChange,
                                    group,
                                    widgetDefinition.widgetObject || widgetId,
                                    newValue,
                                );
                            }
                          : noop,
                  }
                : undefined,
        },
    };
};

const getIndicatorTileWidgetConfig = (
    widgetId: string,
    widgetDefinition: WidgetDefinition,
    group: string,
): Partial<AbstractResponsiveWidget<SimpleIndicatorProps>> => {
    const indicatorTileProperties = widgetDefinition.properties as IndicatorTileWidgetProperties<any>;
    const icon = resolveWidgetProperty(indicatorTileProperties.icon, group, widgetDefinition.widgetObject || widgetId);
    return {
        title: undefined,
        type: 'simple-indicator',
        data: {
            description: resolveWidgetProperty(
                indicatorTileProperties.subtitle,
                group,
                widgetDefinition.widgetObject || widgetId,
            ),
            mainIndicator: {
                title: String(
                    resolveWidgetProperty(
                        indicatorTileProperties.value,
                        group,
                        widgetDefinition.widgetObject || widgetId,
                    ),
                ),
                subtitle: resolveWidgetProperty(
                    indicatorTileProperties.title,
                    group,
                    widgetDefinition.widgetObject || widgetId,
                ),
            },
            icon: icon
                ? {
                      url: resolveDetailedIcon(icon),
                      color: resolveWidgetProperty(
                          indicatorTileProperties.color,
                          group,
                          widgetDefinition.widgetObject || widgetId,
                      ),
                  }
                : undefined,
            type: 'text',
        },
    };
};

export const getIndicatorTileGroupWidgetConfig = (
    widgetId: string,
    widgetDefinition: WidgetDefinition,
    group: string,
    isEditing = false,
): Partial<AbstractResponsiveWidget> => {
    const indicatorTileGroupProperties = widgetDefinition.properties as IndicatorTileGroupWidgetProperties<any>;
    const content = resolveWidgetProperty(
        indicatorTileGroupProperties.content,
        group,
        widgetDefinition.widgetObject || widgetId,
    );

    return {
        title: undefined,
        type: 'tile-group-indicator',
        data: {
            tiles: content?.map((tile: IndicatorTileGroupContent<any>) => ({
                title: tile.title,
                value: tile.value,
                valueColor: tile.valueColor,
                icon: tile.icon
                    ? {
                          url: resolveDetailedIcon(tile.icon),
                          color: tile.iconColor,
                      }
                    : undefined,
                hasSeparatorAfter: tile.hasSeparatorAfter,
                contentAlignment: tile.contentAlignment,
                onClick: (i: string | number): void => {
                    if (isEditing) {
                        return;
                    }
                    resolveWidgetProperty(tile.onClick, group, widgetDefinition.widgetObject || widgetId, i);
                },
            })),
        },
    };
};

const getGaugeWidgetConfig = (
    widgetId: string,
    widgetDefinition: WidgetDefinition,
    group: string,
): Partial<AbstractResponsiveWidget<GaugeProps>> => {
    const gaugeProperties = widgetDefinition.properties as GaugeWidgetProperties<any>;
    return {
        title: resolveWidgetProperty(gaugeProperties.title, group, widgetDefinition.widgetObject || widgetId),
        type: 'gauge',
        data: {
            data: resolveWidgetProperty(gaugeProperties.content, group, widgetDefinition.widgetObject || widgetId) ?? {
                value: 0,
            },
            color: resolveWidgetProperty(gaugeProperties.color, group, widgetDefinition.widgetObject || widgetId),
            scale: resolveWidgetProperty(gaugeProperties.scale, group, widgetDefinition.widgetObject || widgetId),
            valueUnit: resolveWidgetProperty(
                gaugeProperties.valueUnit,
                group,
                widgetDefinition.widgetObject || widgetId,
            ),
            evolutionUnit: resolveWidgetProperty(
                gaugeProperties.evolutionUnit,
                group,
                widgetDefinition.widgetObject || widgetId,
            ),
        },
    };
};

const getTableFilterMenu = ({
    widgetDefinition,
    widgetId,
    group,
    dataDropdownMenu,
}: {
    widgetDefinition: WidgetDefinition;
    widgetId: string;
    group: string;
    dataDropdownMenu: TableWidgetProperties<any>['dataDropdownMenu'];
}): Dict<string> => {
    const dropdownMenu: TableDropDownMenu =
        resolveWidgetProperty(dataDropdownMenu, group, widgetDefinition.widgetObject || widgetId) || {};
    const firstKey = objectKeys(dropdownMenu)[0] ?? '';
    return objectKeys(dropdownMenu[firstKey] ?? []).reduce((acc, key) => {
        acc[key] = dropdownMenu[firstKey][key].title;
        return acc;
    }, {} as Dict<string>);
};

const getDateFilterDefinition = ({
    widgetDefinition,
    widgetId,
    group,
    tableProperties,
}: {
    widgetDefinition: WidgetDefinition;
    widgetId: string;
    group: string;
    tableProperties: TableWidgetProperties<any>;
}): CollectionDateFilter | undefined => {
    if (!tableProperties.dateFilter) {
        return undefined;
    }

    return {
        periodTypes: tableProperties.dateFilter.periodTypes || [
            DateFilterPeriodType.DAY,
            DateFilterPeriodType.WEEK,
            DateFilterPeriodType.MONTH,
            DateFilterPeriodType.YEAR,
        ],
        defaultDate: resolveWidgetProperty(
            tableProperties.dateFilter.defaultDate,
            group,
            widgetDefinition.widgetObject || widgetId,
        ),
        defaultPeriodType: tableProperties.dateFilter.defaultPeriodType,
        maxDate: resolveWidgetProperty(
            tableProperties.dateFilter.maxDate,
            group,
            widgetDefinition.widgetObject || widgetId,
        ),
        minDate: resolveWidgetProperty(
            tableProperties.dateFilter.minDate,
            group,
            widgetDefinition.widgetObject || widgetId,
        ),
    };
};

const getRowDefinition = ({
    widgetDefinition,
    widgetId,
    group,
    tableProperties,
    // If set to true, all click listeners are disabled
    isEditing = false,
}: {
    widgetDefinition: WidgetDefinition;
    widgetId: string;
    group: string;
    tableProperties: TableWidgetProperties<any>;
    // If set to true, all click listeners are disabled
    isEditing?: boolean;
}): WidgetCollectionItemDefinition => {
    const rowDefinition = resolveWidgetProperty(
        tableProperties.rowDefinition,
        group,
        widgetDefinition.widgetObject || widgetId,
    );
    if (isEmpty(rowDefinition)) {
        return { title: { title: '' } };
    }

    return objectKeys(rowDefinition).reduce<WidgetCollectionItemDefinition>((prevValue, k) => {
        prevValue[k] = {
            ...rowDefinition[k]!,
            onClick: isEditing
                ? noop
                : (i: string | number): VoidPromise =>
                      resolveWidgetProperty(
                          rowDefinition[k]?.onClick,
                          group,
                          widgetDefinition.widgetObject || widgetId,
                          String(i),
                      ),
            displayOptions: {
                colorVariant: rowDefinition[k]!.displayOptions?.colorVariant
                    ? (i: string | number): ColorVariant =>
                          resolveWidgetProperty(
                              rowDefinition[k]!.displayOptions?.colorVariant,
                              group,
                              widgetDefinition.widgetObject || widgetId,
                              String(i),
                          ) ?? 'default'
                    : undefined,
                tooltipText: (i: string | number): string | undefined =>
                    resolveWidgetProperty(
                        rowDefinition[k]?.displayOptions?.tooltipText,
                        group,
                        widgetDefinition.widgetObject || widgetId,
                        String(i),
                    ),
                columnWidth: rowDefinition[k]?.displayOptions?.columnWidth,
                isLabelDisplayedOnCard: rowDefinition[k]?.displayOptions?.isLabelDisplayedOnCard,
            },
            renderedAs: rowDefinition[k]!.renderedAs,
            valueFormat: rowDefinition[k]!.valueFormat,
        };
        return prevValue;
    }, {} as WidgetCollectionItemDefinition);
};

export type LoadMoreRowsParams = {
    startIndex: number;
    stopIndex: number;
    pageCount: number;
    pageSize: number;
    after?: WidgetCollectionItem;
    first: number;
};

const getTableOnFilterMenuChanged =
    ({
        widgetDefinition,
        widgetId,
        group,
        tableProperties,
        dashboardId,
    }: {
        widgetDefinition: WidgetDefinition<TableWidgetOptions>;
        widgetId: string;
        group: string;
        tableProperties: TableWidgetProperties<any>;
        dashboardId: string;
    }) =>
    (selectedFilter: string | null): void => {
        const dropdownMenu: TableDropDownMenu =
            resolveWidgetProperty(tableProperties.dataDropdownMenu, group, widgetDefinition.widgetObject || widgetId) ||
            {};
        const firstKey = objectKeys(dropdownMenu)[0] ?? '';
        if (firstKey) {
            updateWidgetOptions({
                dashboardId,
                widgetId,
                group,
                shouldRefreshData: true,
                skipUnset: true,
                options: {
                    ...widgetDefinition.options,
                    dataOptions: {
                        ...widgetDefinition.options?.dataOptions,
                        [firstKey]: selectedFilter,
                    },
                },
            });
        }
    };

const onTableSelectionChanged =
    ({
        widgetDefinition,
        widgetId,
        group,
        dashboardId,
    }: {
        widgetDefinition: WidgetDefinition<TableWidgetOptions>;
        widgetId: string;
        group: string;
        dashboardId: string;
    }) =>
    (selectedItems: string[]): void => {
        updateWidgetOptions({
            dashboardId,
            widgetId,
            group,
            shouldRefreshData: false,
            skipUnset: true,
            options: { ...widgetDefinition.options, selectedItems },
        });
    };

const onTableRepresentationModeChanged =
    ({
        widgetDefinition,
        widgetId,
        group,
        dashboardId,
    }: {
        widgetDefinition: WidgetDefinition<TableWidgetOptions>;
        widgetId: string;
        group: string;
        dashboardId: string;
    }) =>
    (updateMode: 'card' | 'table'): void => {
        updateWidgetOptions({
            dashboardId,
            widgetId,
            group,
            shouldRefreshData: false,
            skipUnset: true,
            options: {
                ...widgetDefinition.options,
                mode: updateMode,
            },
        });
    };

const onTableDateFilterChanged = ({
    widgetDefinition,
    widgetId,
    group,
    dashboardId,
}: {
    widgetDefinition: WidgetDefinition<TableWidgetOptions>;
    widgetId: string;
    group: string;
    dashboardId: string;
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
}) =>
    memoizeOne((selectedPeriod: PeriodDetails): void => {
        updateWidgetOptions({
            dashboardId,
            widgetId,
            group,
            shouldRefreshData: true,
            options: { ...widgetDefinition.options, selectedPeriod },
            skipUnset: true,
        });
    }, isEqual);

const getTableWidgetConfig = (
    widgetId: string,
    widgetDefinition: WidgetDefinition<TableWidgetOptions>,
    group: string,
    dashboardId: string,
    // If set to true, all click listeners are disabled
    isEditing = false,
): Partial<AbstractResponsiveWidget<TableWidgetProps>> => {
    const tableProperties = widgetDefinition.properties as TableWidgetProperties<any>;

    const canSelect = isEditing
        ? false
        : !!resolveWidgetProperty(tableProperties.canSelect, group, widgetDefinition.widgetObject || widgetId);
    const mode =
        widgetDefinition.options?.mode ||
        resolveWidgetProperty(tableProperties.displayMode, group, widgetDefinition.widgetObject || widgetId) ||
        'table';
    const canChangeViewMode = resolveWidgetProperty(
        tableProperties.canSwitchViewMode,
        group,
        widgetDefinition.widgetObject || widgetId,
    );

    const data = (
        resolveWidgetProperty(tableProperties.content, group, widgetDefinition.widgetObject || widgetId) || []
    ).map((r: any) => ({
        ...r,
        i: r._id,
        image: r.image ? getImageUrlFromValue(r.image) : undefined,
    }));

    const totalCount = resolveWidgetProperty(tableProperties.totalCount, group, widgetDefinition.widgetObject);

    const iconSrc = tableProperties.businessIcon
        ? resolveDetailedIcon(
              resolveWidgetProperty(tableProperties.businessIcon, group, widgetDefinition.widgetObject || widgetId) ??
                  '',
          )
        : undefined;

    const filterMenuType = tableProperties.filterMenuType;

    const rowActions = resolveWidgetProperty(tableProperties.rowActions, group, widgetDefinition.widgetObject);

    return {
        type: 'table',
        data: {
            totalCount,
            data,
            canSelect,
            mode,
            canChangeViewMode,
            selectedPeriod: widgetDefinition.options?.selectedPeriod,
            rowDefinition: getRowDefinition({ widgetDefinition, widgetId, group, tableProperties, isEditing }),
            filterMenu: getTableFilterMenu({
                widgetDefinition,
                widgetId,
                group,
                dataDropdownMenu: tableProperties.dataDropdownMenu,
            }),
            dateFilter: getDateFilterDefinition({ widgetDefinition, widgetId, group, tableProperties }),
            onFilterMenuChanged: getTableOnFilterMenuChanged({
                widgetDefinition,
                widgetId,
                group,
                tableProperties,
                dashboardId,
            }),
            onSelectionChanged: onTableSelectionChanged({ widgetDefinition, widgetId, group, dashboardId }),
            onRepresentationModeChanged: onTableRepresentationModeChanged({
                widgetDefinition,
                widgetId,
                group,
                dashboardId,
            }),
            onDateFilterChanged: onTableDateFilterChanged({ widgetDefinition, widgetId, group, dashboardId }),
            iconSrc,
            filterMenuType,
            loadMoreRows: async (
                loadMoreParams = {
                    startIndex: 0,
                    stopIndex: 20,
                    pageCount: 1,
                    pageSize: 20,
                    first: 20,
                },
            ): Promise<WidgetCollectionItem[]> => {
                if (loadMoreParams.after && widgetDefinition._id !== PREVIEW_WIDGET_ID) {
                    return fetchWidgetPage({
                        widgetId: widgetDefinition._id,
                        queryArgs: loadMoreParams,
                        group,
                    });
                }
                return [];
            },
            pageSize: 20,
            rowActions: rowActions?.map(r => {
                if (r.isMenuSeparator) {
                    return {
                        ...r,
                        id: r.id,
                        isHidden: (i: string | number, row: any): boolean =>
                            resolveWidgetProperty(
                                r.isHidden,
                                group,
                                widgetDefinition.widgetObject || widgetId,
                                i,
                                row,
                            ) ?? false,
                        isMenuSeparator: true,
                    };
                }

                return {
                    ...r,
                    onClick: (i: string | number, row: any): void => {
                        resolveWidgetProperty(r.onClick, group, widgetDefinition.widgetObject || widgetId, i, row);
                    },
                    isHidden: (i: string | number, row: any): boolean =>
                        resolveWidgetProperty(r.isHidden, group, widgetDefinition.widgetObject || widgetId, i, row) ??
                        false,
                    isDisabled: (i, row): boolean =>
                        isEditing ||
                        (resolveWidgetProperty(
                            r.isDisabled,
                            group,
                            widgetDefinition.widgetObject || widgetId,
                            i,
                            row,
                        ) ??
                            false),
                    isMenuSeparator: false,
                    label: r.title,
                };
            }),
            externalSelectedRows: widgetDefinition.options?.selectedItems,
        },
    };
};

export const getVisualProcessWidgetConfig = (
    widgetId: string,
    widgetDefinition: WidgetDefinition,
    group: string,
    // If set to true, all click listeners are disabled
    isEditing = false,
): Partial<AbstractResponsiveWidget<VisualProcessProps>> => {
    const visualProcessProperties = widgetDefinition.properties as VisualProcessWidgetProperties<any>;
    const visualProcessContent = resolveWidgetProperty(
        visualProcessProperties.content,
        group,
        widgetDefinition.widgetObject || widgetId,
    );
    if (!visualProcessContent) {
        throw new Error('No content found for visual process');
    }
    return {
        type: 'visual-process',
        data: {
            value: visualProcessContent,
            resolveImageUrl,
            onLinkClick: isEditing
                ? noop
                : (link: XLinkProps): void => executeVisualProcessLink(DASHBOARD_SCREEN_ID, link),
        },
    };
};

const getCallToActionDefinition = ({
    widgetId,
    group,
    widgetDefinition,
    callToActions,
    isEditing,
}: {
    widgetId: string;
    group: string;
    widgetDefinition: WidgetDefinition;
    callToActions: Dict<CallToActionDefinition<any>>;
    isEditing?: boolean;
}): Dict<BmsCallToActionDefinition> => {
    return objectKeys(callToActions).reduce((prevValue, key: string) => {
        const action = callToActions[key];
        prevValue[key] = {
            title: resolveWidgetProperty(action.title, group, widgetDefinition.widgetObject || widgetId) ?? '',
            isDisabled:
                isEditing ||
                !!resolveWidgetProperty(action.isDisabled, group, widgetDefinition.widgetObject || widgetId),
            isHidden: !!resolveWidgetProperty(action.isHidden, group, widgetDefinition.widgetObject || widgetId),
            onClick: (): void =>
                resolveWidgetProperty<any>(action.onClick, group, widgetDefinition.widgetObject || widgetId),
        };
        return prevValue;
    }, {} as Dict<BmsCallToActionDefinition>);
};

const addSettingsPageToProps = (
    widgetId: string,
    group: string,
    widgetDefinition: WidgetDefinition,
    props: AbstractResponsiveWidget,
): void => {
    const isGenericWidget = widgetDefinition.artifactName.startsWith(GENERIC_PLATFORM_WIDGET_PREFIX);
    if (widgetDefinition.properties.settingsPage && props.dropdown) {
        props.dropdown.items = [
            {
                i: 'settings',
                label: localize('@sage/xtrem-ui/dashboard-widget-settings', 'Settings'),
                icon: 'settings',
                onClick: async (): Promise<void> => {
                    try {
                        if (widgetDefinition.properties.settingsPage) {
                            const store = xtremRedux.getStore();
                            const state = store.getState();
                            const settings: Dict<string | number> =
                                state.dashboard.dashboardGroups[
                                    group
                                ].dashboardEditor.currentDashboardDefinition.children.find(c => c._id === widgetId)
                                    ?.settings || ({} as any);

                            const updatedSettings = await widgetDefinition.widgetObject.$.dialog.page(
                                widgetDefinition.properties.settingsPage,
                                settings,
                                { rightAligned: true },
                            );

                            const thunkDispatch = store.dispatch as xtremRedux.AppThunkDispatch;
                            await thunkDispatch(
                                xtremRedux.actions.updateWidgetSettings(widgetId, group, updatedSettings),
                            );
                        }
                    } catch {
                        // Do nothing.
                    }
                },
            },
            ...(props.dropdown.items || []),
        ];
    }

    if (isGenericWidget && props.dropdown) {
        props.dropdown.items = [
            {
                i: 'edit',
                label: localize('@sage/xtrem-ui/dashboard-editor-widget-edit', 'Edit'),
                icon: 'settings',
                onClick(): void {
                    const store = xtremRedux.getStore();
                    const state = store.getState();
                    const settings = state.dashboard.dashboardGroups[
                        group
                    ].dashboardEditor.currentDashboardDefinition.children.find(c => c._id === widgetId)?.settings;
                    if (settings) {
                        const thunkDispatch = store.dispatch as xtremRedux.AppThunkDispatch;
                        thunkDispatch(xtremRedux.actions.openWidgetEditorDialog(group, widgetId, settings));
                    }
                },
            },
            ...(props.dropdown.items || []),
        ];
    }
};

const addHeaderActionsToProps = ({
    widgetId,
    group,
    widgetDefinition,
    props,
    isEditing,
}: {
    widgetId: string;
    group: string;
    widgetDefinition: WidgetDefinition;
    props: AbstractResponsiveWidget;
    isEditing: boolean;
}): void => {
    if (widgetDefinition.properties.headerActions && props.dropdown) {
        props.dropdown.items = [
            ...(isEditing
                ? []
                : [
                      ...widgetDefinition.properties.headerActions.map(ha => ({
                          i: `${ha.title}-${ha.icon}`,
                          label: ha.title,
                          icon: ha.icon,
                          onClick: (): void =>
                              resolveWidgetProperty<any>(ha.onClick, group, widgetDefinition.widgetObject || widgetId),
                      })),
                      { i: 'header-actions-separator', isDivider: true },
                  ]),
            ...(props.dropdown.items || []),
        ];
    }
};

const getDefaultProps = ({
    widgets,
    group,
    dashboardItem,
    isEditing,
    canEditDashboards,
    onCloseCallback,
    breakpoint,
}: {
    widgets: Dict<WidgetDefinitionWithLoadingState>;
    group: string;
    dashboardItem: DashboardItem;
    isEditing: boolean;
    canEditDashboards: boolean;
    onCloseCallback?: (widgetId: string) => void;
    breakpoint: DashboardBreakpoint;
}): AbstractResponsiveWidget => {
    const dropDownItems: (DropdownItem | DropdownItemDivider)[] = isEditing
        ? []
        : [
              {
                  i: 'refresh',
                  label: localize('@sage/xtrem-ui/dashboard-widget-refresh', 'Refresh'),
                  icon: 'refresh',
                  onClick: (): void => refreshWidget(dashboardItem._id, group),
              },
          ];

    if (isEditing && canEditDashboards) {
        dropDownItems.push({ i: 'close-separator', isDivider: true });
        dropDownItems.push({
            i: 'close',
            label: localize('@sage/xtrem-ui/dashboard-widget-close', 'Close'),
            icon: 'cross',
            onClick: onCloseCallback ? (): void => onCloseCallback(dashboardItem._id) : undefined,
        });
    }

    const widgetDefinition = widgets?.[dashboardItem._id];

    const b = dashboardItem.positions.find(p => p.breakpoint === breakpoint);
    const positions = dashboardItem.positions.reduce(
        (acc, { x, y, w, h, breakpoint: b }) => {
            acc[b] = { x, y, w, h };
            return acc;
        },
        {
            xxs: { x: b?.x ?? 0, y: b?.y ?? 0, w: b?.w ?? 2, h: b?.h ?? 2 },
            xs: { x: b?.x ?? 0, y: b?.y ?? 0, w: b?.w ?? 2, h: b?.h ?? 2 },
            sm: { x: b?.x ?? 0, y: b?.y ?? 0, w: b?.w ?? 2, h: b?.h ?? 2 },
            md: { x: b?.x ?? 0, y: b?.y ?? 0, w: b?.w ?? 2, h: b?.h ?? 2 },
            lg: { x: b?.x ?? 0, y: b?.y ?? 0, w: b?.w ?? 2, h: b?.h ?? 2 },
        } as {
            xxs: { x: number; y: number; w: number; h: number };
            xs: { x: number; y: number; w: number; h: number };
            sm: { x: number; y: number; w: number; h: number };
            md: { x: number; y: number; w: number; h: number };
            lg: { x: number; y: number; w: number; h: number };
        },
    );
    return {
        i: String(dashboardItem._id),
        type: 'basic',
        ...positions,
        title: resolveWidgetProperty(
            widgetDefinition.properties?.title,
            group,
            widgetDefinition.widgetObject || dashboardItem._id,
        ),
        dropdown: {
            items: dropDownItems,
        },
        isLoading: widgetDefinition.isLoading,
    };
};

// TO RENDER DASHBOARD CALL THIS
export const getWidgetsFromDashboardItems = ({
    dashboardId,
    group,
    children,
    widgets,
    isEditing = false,
    onCloseCallback,
    canEditDashboards = true,
    breakpoint,
}: {
    dashboardId: string;
    group: string;
    children: DashboardItem[];
    widgets: Dict<WidgetDefinitionWithLoadingState>;
    // If set to true, all click listeners are disabled
    isEditing?: boolean;
    onCloseCallback?: (widgetId: string) => void;
    canEditDashboards?: boolean;
    breakpoint: DashboardBreakpoint;
}): AbstractResponsiveWidget[] =>
    children.map(dashboardItem => {
        const widgetId = String(dashboardItem._id);
        const props: AbstractResponsiveWidget = getDefaultProps({
            widgets,
            group,
            dashboardItem,
            isEditing,
            canEditDashboards,
            onCloseCallback,
            breakpoint,
        });

        if (!widgets?.[dashboardItem._id] || !widgets?.[dashboardItem._id]?.data) {
            // Set loading state of the widget
            props.type = 'preview';
            props.title = undefined;
            props.dropdown = undefined;
            props.data = {};
            return props;
        }

        const widgetDefinition = widgets[dashboardItem._id];

        if (isEditing) {
            // The settings are only available in edit mode
            addSettingsPageToProps(widgetId, group, widgetDefinition, props);
        }

        if (canEditDashboards) {
            addHeaderActionsToProps({ widgetId, group, widgetDefinition, props, isEditing });
        }

        if (widgetDefinition.properties.callToActions) {
            props.callToActions = getCallToActionDefinition({
                widgetId,
                group,
                widgetDefinition,
                callToActions: widgetDefinition.properties.callToActions,
                isEditing,
            });
        }

        if (props.dropdown?.items) {
            props.dropdown.items = dropWhile(props.dropdown.items, i => i.i.endsWith('-separator'));
        }

        switch (widgetDefinition.widgetType) {
            case WidgetType.barChart:
                return {
                    ...props,
                    ...getBarChartWidgetConfig(widgetId, widgetDefinition, group),
                };
            case WidgetType.lineChart:
                return {
                    ...props,
                    ...getLineChartWidgetConfig(widgetId, widgetDefinition, group),
                };
            case WidgetType.pieChart:
                return {
                    ...props,
                    ...getPieChartWidgetConfig(widgetId, widgetDefinition, group),
                };
            case WidgetType.indicatorTile:
                return {
                    ...props,
                    ...getIndicatorTileWidgetConfig(widgetId, widgetDefinition, group),
                };
            case WidgetType.indicatorTileGroup:
                return {
                    ...props,
                    ...getIndicatorTileGroupWidgetConfig(widgetId, widgetDefinition, group, isEditing),
                };
            case WidgetType.gauge:
                return {
                    ...props,
                    ...getGaugeWidgetConfig(widgetId, widgetDefinition, group),
                };
            case WidgetType.staticContent:
                return {
                    ...props,
                    ...getStaticContentWidgetConfig(widgetId, widgetDefinition, group),
                };
            case WidgetType.contactCard:
                return {
                    ...props,
                    ...getContactCardWidgetConfig(widgetId, widgetDefinition, group, isEditing),
                };
            case WidgetType.table:
                return {
                    ...props,
                    ...getTableWidgetConfig(widgetId, widgetDefinition, group, dashboardId, isEditing),
                };
            case WidgetType.visualProcess:
                return {
                    ...props,
                    ...getVisualProcessWidgetConfig(widgetId, widgetDefinition, group, isEditing),
                };
            default:
                props.data = {
                    htmlContent: `<div>Unknown widget: ${widgetDefinition.widgetType}</div>`,
                };
        }

        return props;
    });

export const fetchWidgetData = async ({
    forceRefetch = false,
    locale,
    version,
    widgetDefinition,
    queryArgs,
    group,
}: {
    forceRefetch?: boolean;
    locale?: string;
    version?: string;
    widgetDefinition: WidgetDefinition;
    group: string;
    queryArgs?: Partial<LoadMoreRowsParams>;
}): Promise<any> => {
    const query = queryArgs
        ? resolveWidgetProperty(widgetDefinition.properties.getQuery, group, widgetDefinition.widgetObject, queryArgs)
        : resolveWidgetProperty(widgetDefinition.properties.getQuery, group, widgetDefinition.widgetObject);
    // The query is hashed and added to the key
    const settingsHash = MD5(JSON.stringify(query)).toString();
    const requestArgs = {
        query,
        cacheSettings:
            version && locale
                ? {
                      key: `${widgetDefinition.artifactName}-${settingsHash}-widget-data`,
                      locale,
                      version,
                      shouldFetchPlatformLiterals: false,
                  }
                : undefined,
        forceRefetch,
    };

    let queryResult = await executeGraphqlQuery({ ...requestArgs });

    const cacheLifespan = resolveWidgetProperty(
        widgetDefinition.properties.cacheLifespan,
        group,
        widgetDefinition.widgetObject || widgetDefinition._id,
    );
    // Check whether the result is coming from the cache, validate its timestamp
    if (queryResult.cachedAt && queryResult.cachedAt < Date.now() - (cacheLifespan ?? 0)) {
        // If it is cached and it was cached outside the cache time frame, force refetch the cache for the widget.
        queryResult = await executeGraphqlQuery({ ...requestArgs, forceRefetch: true });
    }

    return queryResult.data;
};

export function resolveWidgetProperty<T>(
    property: T,
    group: string,
    /** The widget ID or the AbstractWidget object */
    widget: string | AbstractWidget,
    ...args: any[]
): T extends (...args: any) => any ? ReturnType<T> : T | undefined {
    if (typeof property === 'function') {
        if (typeof widget === 'string') {
            const state = xtremRedux.getStore().getState();
            const widgetDefinition: WidgetDefinition = state.dashboard.dashboardGroups[group].widgets[widget];
            if (widgetDefinition) {
                return property.apply(widgetDefinition.widgetObject, args);
            }
            return undefined as any;
        }

        return property.apply(widget, args);
    }

    return property as any;
}
