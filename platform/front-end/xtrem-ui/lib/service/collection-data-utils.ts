import type { ClientNode } from '@sage/xtrem-client';
import { DateRange } from '@sage/xtrem-date-time';
import * as xtremDecimal from '@sage/xtrem-decimal';
import type { Dict } from '@sage/xtrem-shared';
import { flat, objectKeys } from '@sage/xtrem-shared';
import {
    chain,
    cloneDeep,
    difference,
    get,
    intersection,
    isEmpty,
    isNil,
    set,
    sortBy,
    take,
    uniq,
    unset,
} from 'lodash';
import type { Merge } from 'ts-essentials';
import type { NestedField, NestedFieldTypes } from '../component/nested-fields';
import type { OrderByType } from '../component/types';
import { FieldKey } from '../component/types';
import type { DateAggregation } from '../redux/state';
import { GraphQLKind } from '../types';
import { findColumnDefinitionByBind, getNestedFieldElementId } from '../utils/abstract-fields-utils';
import { xtremConsole } from '../utils/console';
import {
    cleanMetadataFromRecord,
    removeNullOnlyLeaves,
    schemaTypeNameFromNodeName,
    splitValueToMergedValue,
} from '../utils/transformers';
import { isDevMode } from '../utils/window';
import type { InternalValue } from './collection-data-types';
import { RecordActionType } from './collection-data-types';
import { filterReservedWords } from './graphql-query-builder';
import type { FormattedNodeDetails } from './metadata-types';
import type { ScreenBase } from './screen-base';
import { formatInputProperty } from './value-serializer-service';

const numericRegex = /^-?[\d.]+$/;

export const isNumeric = (value: any): boolean => !!numericRegex.test(String(value).trim());

export type AggFunc = DateAggregation;

export const formatValueForSorting = (
    object: any,
    path: string,
    type?: NestedFieldTypes,
): { value: string | xtremDecimal.Decimal; isDecimal: boolean } => {
    const value = get(object, path);
    if (value === null || value === undefined) {
        return { value: '', isDecimal: false };
    }

    if (path.endsWith('_sortValue') && isNumeric(value)) {
        return { value: xtremDecimal.Decimal.make(String(value)), isDecimal: true };
    }

    if ((path.endsWith('_id') || type === FieldKey.Numeric) && isNumeric(value)) {
        return { value: xtremDecimal.Decimal.make(String(value)), isDecimal: true };
    }

    return { value: String(value), isDecimal: false };
};

export const caseInsensitiveSorting =
    (sortProperties: [string, boolean][], columnDefinitions: NestedField<ScreenBase, NestedFieldTypes>[]) =>
    (obj1: any, obj2: any): number => {
        for (let i = 0; i < sortProperties.length; i += 1) {
            const sortCondition = sortProperties[i];
            const property = sortCondition[0];
            const bind = property;
            const sortingOrder = sortCondition[1];
            const { type } = findColumnDefinitionByBind(columnDefinitions, bind) ?? {
                type: FieldKey.Text,
                properties: {} as any,
            };
            const { value: value1, isDecimal: isValue1Decimal } = formatValueForSorting(obj1, property, type);
            const { value: value2, isDecimal: isValue2Decimal } = formatValueForSorting(obj2, property, type);

            if (value1 === null || (value1 === undefined && value2 !== null && value2 !== undefined)) {
                return sortingOrder ? -1 : 1;
            }

            if (value2 === null || (value2 === undefined && value1 !== null && value1 !== undefined)) {
                return !sortingOrder ? -1 : 1;
            }

            if (value1 === null || (value1 === undefined && value2 === null && value2 === undefined)) {
                return 0;
            }

            if (isValue1Decimal && isValue2Decimal && bind) {
                const result = xtremDecimal.Decimal.make(isValue1Decimal ? value1 : 0).comparedTo(
                    xtremDecimal.Decimal.make(isValue2Decimal ? value2 : 0),
                );
                if (result !== 0) {
                    return sortingOrder ? result * -1 : result;
                }
            } else {
                // TODO: switch to numeric: true when Basalt changes their default collation (XT-24930)
                const collator = new Intl.Collator(undefined, { numeric: false, sensitivity: 'base' });
                const result = collator.compare(String(value1), String(value2));
                if (result !== 0) {
                    return sortingOrder ? result * -1 : result;
                }
            }
        }

        return 0;
    };

export function transformFilterForCollectionValue(outerInput: any): any {
    function getParent({ parent, key, parentKey }: { parent: string; key: string; parentKey: string }): string {
        if (parent === '') {
            return key;
        }
        const isOperator = filterReservedWords.includes(key as any) || filterReservedWords.includes(parentKey as any);
        if (isOperator || parent.slice(-1) === ']') {
            return `${parent}.${key}`;
        }
        return `${parent}.${key}`;
    }

    const aggregationFilterKeys: typeof filterReservedWords = [
        '_containedBy',
        '_atLeast',
        '_atMost',
        '_every',
        '_none',
    ];

    function nested({
        input,
        parent = '',
        parentKey = '',
        realPath = '',
        obj = {},
    }: {
        input: any;
        parent?: string;
        parentKey?: string;
        realPath?: string;
        obj?: any;
    }): any {
        if (typeof input !== 'object' || !input) {
            set(obj, parent, input);
            return obj;
        }
        if (Array.isArray(input)) {
            input.forEach((i, index) =>
                nested({
                    input: i,
                    realPath: `${realPath}[${index}]`,
                    parentKey,
                    obj,
                    parent: `${realPath}[${index}]`,
                }),
            );
        } else {
            const inputKeys = objectKeys(input);
            const nonAggregationFilterKeys = inputKeys.filter(k => !aggregationFilterKeys.includes(k as any));
            if (nonAggregationFilterKeys.length !== inputKeys.length) {
                // eslint-disable-next-line no-restricted-syntax
                for (const key of nonAggregationFilterKeys) {
                    // unset all keys except aggregation as they are not store in the collection value
                    unset(input, key);
                }
            }
            // eslint-disable-next-line no-restricted-syntax
            for (const key of objectKeys(input)) {
                nested({
                    input: input[key],
                    parent: getParent({ key, parent, parentKey }),
                    realPath: realPath === '' ? key : `${realPath}.${key}`,
                    parentKey: key,
                    obj,
                });
            }
        }
        return obj;
    }
    return nested({ input: cloneDeep(outerInput) });
}

/**
 * Sorts a referenced ResultSet based on the received orderBy sequence
 * @param resultSet - lokijs ResultSet instance
 * @param [orderBy] - sequence of property name / sorting type (1 for asc, -1 for desc) pairs
 * @returns ResultSet
 */
export const sortResultSet = ({
    resultSet,
    orderBy,
    columnDefinitions,
}: {
    resultSet: Resultset<any>;
    orderBy?: OrderByType;
    columnDefinitions: NestedField<ScreenBase, NestedFieldTypes>[];
}): Resultset<any> => {
    if (orderBy && !isEmpty(orderBy)) {
        const flattenedOrderBy = flat(orderBy);
        const sortProperties = objectKeys(flattenedOrderBy).map(key => {
            const isDescending: boolean = get(orderBy, key) === -1;
            return [key, isDescending] as [string, boolean];
        });
        return resultSet.sort(
            caseInsensitiveSorting([['__level', false], ...sortProperties, ['_id', false]], columnDefinitions),
        );
    }
    return resultSet.compoundsort([
        ['__level', false],
        ['_id', false],
    ]);
};

export function getGroupKey({ groupKey, type }: { groupKey: string; type: FieldKey }): string {
    // eslint-disable-next-line @sage/redos/no-vulnerable
    const nestedValueMatch = groupKey?.match(/(.*)\./);
    return nestedValueMatch && type === FieldKey.Reference ? `${nestedValueMatch[1]}._id` : (groupKey ?? '');
}

function getNullGroupByCondition({
    mode,
    groupKey,
    type,
}: {
    mode: 'server' | 'client';
    groupKey: string;
    type: FieldKey;
}): any {
    const split = getGroupKey({ groupKey, type }).split('.');
    const prefix = mode === 'client' ? '$' : '_';
    return chain(Array.from({ length: split.length - 1 }, (_, i) => i + 1))
        .map(t => take(split, t).join('.'))
        .map(key => (prefix === '$' ? { [key]: { [`${prefix}eq`]: null } } : set({}, key, { [`${prefix}eq`]: null })))
        .thru(clause => ({
            [`${prefix}or`]: clause.concat(
                prefix === '$'
                    ? { [groupKey]: { [`${prefix}eq`]: null } }
                    : set({}, groupKey, { [`${prefix}eq`]: null }),
            ),
        }))
        .value();
}

export const getGroupFilterValue = ({
    group,
    mode,
}: {
    group: { key: string; value?: string; aggFunc?: AggFunc; type: FieldKey };
    mode: 'server' | 'client';
}): any => {
    if (group?.value == null) {
        return null;
    }
    if (group.value === '') {
        return getNullGroupByCondition({ groupKey: group.key, mode, type: group.type });
    }
    const prefix = mode === 'client' ? '$' : '_';
    if (group.aggFunc) {
        const { start, end } = DateRange.getDateRange({ date: group.value, range: `same-${group.aggFunc}` });
        const filterValue = {
            [`${prefix}and`]: [{ [`${prefix}gte`]: start?.toString() }, { [`${prefix}lte`]: end?.toString() }],
        };
        return mode === 'client' ? { [group.key]: filterValue } : set({}, group.key, filterValue);
    }
    return mode === 'client'
        ? { [getGroupKey({ groupKey: group.key, type: group.type })]: group.value }
        : set({}, getGroupKey({ groupKey: group.key, type: group.type }), { _eq: group.value });
};

export class CollectionDataRow<T extends ClientNode> {
    private result: any;

    private readonly includeActions: boolean;

    private readonly removeNegativeId: boolean;

    private readonly isRequestingDefaults: boolean;

    private readonly cleanInputTypes: boolean;

    constructor(
        private readonly input: InternalValue<T>,
        private readonly columnDefinitions: Array<NestedField<ScreenBase, NestedFieldTypes, any>[]>,
        private readonly nodes: string[],
        private readonly nodeTypes: Dict<FormattedNodeDetails>,
        private readonly options?: {
            includeActions?: boolean;
            removeNegativeId?: boolean;
            isRequestingDefaults?: boolean;
            cleanInputTypes?: boolean;
            isTree?: boolean;
        },
    ) {
        this.result = input;
        this.includeActions = this.options?.includeActions ?? true;
        this.removeNegativeId = this.options?.removeNegativeId ?? false;
        this.isRequestingDefaults = this.options?.isRequestingDefaults ?? false;
        this.cleanInputTypes = this.options?.cleanInputTypes ?? true;
    }

    protected getLevel(): number {
        return this.input.__level ?? 0;
    }

    protected getDirtyColumns(): Set<string> | undefined {
        return this.input.__dirtyColumns;
    }

    protected getAction(): RecordActionType | undefined {
        return this.input.__action;
    }

    protected isRemoved(): boolean {
        return this.getAction() === RecordActionType.REMOVED;
    }

    protected removeTransientColumns(): this {
        if (!this.isRemoved()) {
            const level = this.getLevel();

            this.result = objectKeys(this.result).reduce((acc, key) => {
                const colDef = this.getColumnDefinitions(level)?.find(d => getNestedFieldElementId(d) === key);
                if (!colDef?.properties?.isTransient) {
                    acc[key] = this.result[key];
                }
                return acc;
            }, {} as any);
        }

        return this;
    }

    // This function should remove all computed columns like Aggregation from the record.
    protected removeComputedColumns(): this {
        if (!this.isRemoved()) {
            const level = this.getLevel();

            this.result = objectKeys(this.result).reduce((acc, key) => {
                const colDef = this.getColumnDefinitions(level)?.find(d => getNestedFieldElementId(d) === key);
                const isComputed =
                    colDef?.properties._controlObjectType === FieldKey.Aggregate ||
                    colDef?.properties._controlObjectType === FieldKey.Count;

                if (!isComputed) {
                    acc[key] = this.result[key];
                }
                return acc;
            }, {} as any);
        }

        return this;
    }

    protected cleanupRemoved(): this {
        if (this.getAction() === RecordActionType.REMOVED) {
            this.result = { _id: this.result._id, _action: RecordActionType.REMOVED };
        }
        return this;
    }

    protected cleanMetadataFromRecord(): this {
        if (!this.isRemoved()) {
            this.result = cleanMetadataFromRecord(this.result, true, this.isRequestingDefaults);
        }
        return this;
    }

    protected removeNonInputTypes(): this {
        if (!this.isRemoved() && this.cleanInputTypes) {
            const level = this.getLevel();
            const node = this.nodeTypes[schemaTypeNameFromNodeName(this.getNode(level))];
            if (node) {
                const inputProperties = objectKeys(node.properties).filter(k => !!node.properties[k].isOnInputType);
                const inputKeys = sortBy(uniq([...inputProperties, '_id', '_action', '_sortValue']));
                this.result = intersection(inputKeys, objectKeys(this.result)).reduce((acc, key) => {
                    acc[key] = this.result[key];
                    return acc;
                }, {} as any);

                // Print warning in dev mode in case any key is removed
                if (isDevMode()) {
                    const removedKeys = difference(objectKeys(this.result), inputKeys);
                    if (removedKeys.length > 0) {
                        xtremConsole.warn(
                            `The following keys were removed from ${JSON.stringify(this.result)}: ${JSON.stringify(
                                removedKeys,
                            )}, are you sure you are using the correct node type ('${this.getNode(level)}')?`,
                        );
                    }
                }
            }
        }

        return this;
    }

    protected extractDirty(): this {
        if (!this.isRemoved() && this.isRequestingDefaults) {
            const dirtyColumns = this.getDirtyColumns();

            this.result = objectKeys(this.result).reduce<
                Merge<InternalValue<T>, { _id?: string; _action?: RecordActionType }>
            >(
                (acc, key) => {
                    if (dirtyColumns?.has(key)) {
                        set(acc, key, this.result[key]);
                    }
                    return acc;
                },
                { _id: this.result._id, _action: this.result._action } as any,
            );
        }
        return this;
    }

    protected cloneAction(): this {
        if (this.includeActions) {
            this.result._action = this.input.__action || 'update';
        }
        return this;
    }

    protected deleteNegativeId(): this {
        if (this.removeNegativeId) {
            const { _id, ...rest } = this.result;
            this.result = {
                ...(_id !== undefined && _id !== null && Number.parseInt(_id, 10) > 0 && { _id }),
                ...rest,
            };
        }
        return this;
    }

    protected removeNullOnlyLeaves(): this {
        removeNullOnlyLeaves(this.result);
        return this;
    }

    protected splitValueToMergedValue(): this {
        this.result = splitValueToMergedValue(this.result);
        return this;
    }

    getChangeset(): any {
        const result = this.cleanupRemoved()
            .cloneAction()
            .removeTransientColumns()
            .removeComputedColumns()
            .cleanMetadataFromRecord()
            .splitValueToMergedValue()
            .removeNonInputTypes()
            .extractDirty()
            .removeNullOnlyLeaves()
            .deleteNegativeId().result;

        if (!this.cleanInputTypes) {
            return result;
        }

        const level = this.getLevel();
        return formatInputProperty({
            value: result,
            nodeTypes: this.nodeTypes,
            isTopLevel: true,
            property: {
                type: schemaTypeNameFromNodeName(this.getNode(level)),
                kind: GraphQLKind.Object,
                isMutable: true,
            },
        });
    }

    private readonly getNode = (level?: number | null): string => {
        if (this.options?.isTree) {
            return this.nodes[0];
        }

        const targetLevel = isNil(level) ? 0 : level;
        return this.nodes[targetLevel];
    };

    private readonly getColumnDefinitions = (level?: number | null): NestedField<ScreenBase, NestedFieldTypes, T>[] => {
        if (this.options?.isTree) {
            return this.columnDefinitions?.[0] || [];
        }

        const targetLevel = isNil(level) ? 0 : level;
        return this.columnDefinitions?.[targetLevel] || [];
    };
}
