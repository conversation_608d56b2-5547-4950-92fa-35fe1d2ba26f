import { Page } from './page';
import type { ScreenBase } from './screen-base';

/**
 * This type is used for extended fields. It checks the type. If the generic argument (`This`) extends the PageExtension,
 * it will return a merged type of the extension properties and the base class properties.
 *
 * If the generic argument (`This`) does not extend PageExtension, so it's not an extended page, it will leave the type
 * as is without appending any other properties.
 */
export type Extend<This extends ScreenBase> =
    This extends PageExtension<infer Base, any> ? ExtensionMembers<This & Base> : This;
export type ExtensionMembers<T> = Pick<T, Exclude<keyof T, '_dummy'>>;

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export abstract class PageExtension<Base extends Page, Api> extends Page<Base extends Page<infer U> ? U : any> {
    constructor() {
        super();
        throw new Error('Cannot instantiate extension class');
    }

    public basePage: string;

    // The _dummy property forces type inference on Base --
    protected _dummy: Base;
}

(PageExtension.prototype as any).___isPageExtension = true;
