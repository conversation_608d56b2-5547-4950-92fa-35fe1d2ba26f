import type { UiComponentUserSettings } from '../redux/state';
import { executeGraphqlQuery } from './graphql-utils';

export async function fetchUserClientSettingsForElement(
    screenId: string,
    elementId: string,
): Promise<Array<UiComponentUserSettings>> {
    const response = await executeGraphqlQuery({
        endpoint: '/metadata',
        query: {
            clientUserSettings: {
                variantsForElement: {
                    __args: {
                        screenId,
                        elementId,
                    },
                    _id: true,
                    title: true,
                    description: true,
                    content: true,
                },
            },
        },
    });

    return response.data.clientUserSettings.variantsForElement
        .map((v: any) => ({
            ...v,
            content: JSON.parse(v.content || '{}'),
        }))
        .sort((a: UiComponentUserSettings, b: UiComponentUserSettings) => a.title.localeCompare(b.title));
}
export async function unselectView(screenId: string, elementId: string): Promise<void> {
    await executeGraphqlQuery({
        endpoint: '/metadata',
        query: {
            mutation: {
                clientUserSettings: {
                    unselectClientUserSettings: {
                        __args: {
                            screenId,
                            elementId,
                        },
                        _id: true,
                    },
                },
            },
        },
    });
}
export async function selectView(screenId: string, elementId: string, viewId: string): Promise<void> {
    await executeGraphqlQuery({
        endpoint: '/metadata',
        query: {
            mutation: {
                clientUserSettings: {
                    setCurrentUserClientSetting: {
                        __args: {
                            screenId,
                            elementId,
                            _id: viewId,
                        },
                        _id: true,
                        title: true,
                        description: true,
                        content: true,
                    },
                },
            },
        },
    });
}
