import type { XLinkProps } from '@sage/visual-process-editor';
import { XLinkType } from '@sage/visual-process-editor';
import * as nestedFields from '../component/nested-fields';
import type { ReferenceDecoratorProperties } from '../component/field/reference/reference-types';
import { GraphQLKind, GraphQLTypes } from '../types';
import { REG_EXP_URL_PATTERN, VISUAL_PROCESS_FAKE_NODE_TYPE_PAGE } from '../utils/constants';
import { CollectionValue } from './collection-data-service';
import { executeGraphqlQuery } from './graphql-utils';
import { localize } from './i18n-service';
import { getRouter } from './router';
import { splitValueToMergedValue } from '../utils/transformers';

export const executeVisualProcessLink = (screenId: string, xlinkProps: XLinkProps): void => {
    if (!xlinkProps.xtype || !xlinkProps.xcode) {
        return;
    }

    if (xlinkProps.xtype === XLinkType.URL && xlinkProps.xcode.match(REG_EXP_URL_PATTERN)) {
        if (xlinkProps.xtarget === '_blank') {
            window.open(xlinkProps.xcode, '_blank');
        } else {
            window.location.href = xlinkProps.xcode;
        }
    } else if (
        xlinkProps.xtype === XLinkType.ERP ||
        (xlinkProps.xtype === XLinkType.URL && xlinkProps.xcode.split('/').length === 3)
    ) {
        getRouter(screenId).goTo(
            xlinkProps.xcode,
            xlinkProps.xparam1
                ? {
                      _id: xlinkProps.xparam1 as string,
                  }
                : undefined,
        );
    } else {
        throw new Error(`Unsupported link format: ${xlinkProps.xtype}`);
    }
};

export const getVisualProcessPageLookupProperties = (): ReferenceDecoratorProperties => ({
    title: localize('@sage/xtrem-ui/visual-process-lookup-page-dialog-title', 'Choose a page'),
    lookupDialogTitle: localize('@sage/xtrem-ui/visual-process-lookup-page-dialog-title', 'Choose a page'),
    node: VISUAL_PROCESS_FAKE_NODE_TYPE_PAGE,
    valueField: '_id',
    helperTextField: 'title',
    isTransient: true,
    columns: [
        nestedFields.text({
            bind: 'title',
            title: localize('@sage/xtrem-ui/visual-process-lookup-page-title-column', 'Page title'),
        }),
        nestedFields.text({
            bind: 'key',
            title: localize('@sage/xtrem-ui/visual-process-lookup-page-path-column', 'Page path'),
        }),
    ],
});

export const getPageLookupDialogCollectionValue = async (
    screenId: string,
    elementId: string,
): Promise<CollectionValue> => {
    const result = await executeGraphqlQuery({
        endpoint: '/metadata',
        query: {
            pages: {
                key: true,
                title: true,
            },
        },
    });
    const initialValues = (result.data?.pages || [])
        .map((r: any) => ({ _id: r.key, ...r }))
        .map(splitValueToMergedValue);
    const tableProperties = getVisualProcessPageLookupProperties();
    return new CollectionValue({
        screenId,
        elementId,
        columnDefinitions: [tableProperties.columns || []],
        hasNextPage: false,
        isTransient: true,
        isNoServerLookups: true,
        initialValues,
        nodes: [VISUAL_PROCESS_FAKE_NODE_TYPE_PAGE],
        nodeTypes: {
            [VISUAL_PROCESS_FAKE_NODE_TYPE_PAGE]: {
                title: VISUAL_PROCESS_FAKE_NODE_TYPE_PAGE,
                name: VISUAL_PROCESS_FAKE_NODE_TYPE_PAGE,
                packageName: '@sage/xtrem-ui',
                properties: {
                    _id: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    title: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    key: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                },
                mutations: {},
            },
        },
    });
};
