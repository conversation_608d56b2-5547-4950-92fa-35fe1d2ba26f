PATH: XTREEM/Client+Framework/Dialog+API

This page describes how the client side dialog API works, highlights various dialog types and their potential use cases.

## Introduction

The Dialog API is available in any callback functions which has access to the page instance but in some cases it is not advised to use them, for example in case of validation() callbacks.

## Basic example:

```ts
onClick() {
    this.$.dialog.confirmation('info', 'Question', 'Do you want to open a dialog?');
}
```

## Key behavior

-   Given dialogs are supposed to facilitate interactions between the application and the user, all of them returns some result to the developer in a form of promises. If the dialog's positive button is clicked, the promise will be resolved, if the negative button is clicked or the dialog is closed, the promise will be rejected.

## Dialog options

All kind of dialogs can be initiated with a number of different options, which determine their behavior and how they will be presented to the user.

-   **acceptButton**: Properties of the positive button, developers can decide how it should be labelled and whether it is hidden, disabled.
-   **cancelButton**: Properties of the negative button, developers can decide how it should be labelled and whether it is hidden, disabled.
-   **rightAligned**: If set to true, the dialog will be rendered as a sidebar on the right side of the screen.
-   **height**: If set the fixed height of the dialog in pixels.
-   **fullScreen**: If set to true, the dialog will be rendered as a full-screen object, overlapping all other parts of the screen.
-   **dialogTitle**: (only for custom dialogs) If set, it is used as the dialog title.
-   **skipDirtyCheck**: (only for page dialogs) If set to true, the framework does not check if the page has any unsaved changes when the user closes the dialog.
-   **values**: (only for page dialogs) Initial value of the dialog. When the value is set, the field values of the page dialog are automatically populated.


## Custom dialog
Custom dialogs can take one or more sections from the page body and render them inside a dialog.
#### Example with one section:
```ts
this.$.dialog.custom(
    'info', // Dialog level
    this.mySectionToRenderInsideTheDialog, // The section that is displayed within the dialog
    options, // Optional dialog options
);
```
#### Example with multiple section:
```ts
this.$.dialog.custom(
    'info', // Dialog level
    [this.mySectionToRenderInsideTheDialog, this.myOtherSection], // An array with the sections that should be displayed within the dialog
    options, // Optional dialog options
);
```

## Page dialogs
As the name suggests, page dialog is a dialog with a page in it. It returns a promise that resolves to the serialized value of the page. Similarly to other dialogs, the promise is resolved if the user clicks a positive action button, it is rejected if the user uses a negative action button or closes the dialog.

```ts
    const result = await this.$.dialog.page('@sage/xtrem-show-case/ShowCaseProduct', { _id: '321' }, options);
    consol.log(result);
```

## Lookup dialog
Lookup dialogs are identical to the dialogs that are opened from reference or multi-reference dialog. It returns a promise that resolves to an array of the selected items.

```ts
const result = await this.$.dialog.lookup<ShowCaseProduct>({
    acceptButton: { text: 'Select and add to order' },
    dialogTitle: 'Add products to your order',
    node: '@sage/xtrem-show-case/ShowCaseProduct',
    isEditable: true,
    isMultiSelect: true,
    filter: {
        provider: { textField: 'Amazon' },
        netPrice: { _gt: '15' },
    },
    orderBy: {
        product: 1,
    },
    columns: [
        ui.nestedFields.text({ bind: '_id', isReadOnly: true, title: 'ID' }),
        ui.nestedFields.text({ bind: 'product', isReadOnly: true, title: 'Product' }),
        ui.nestedFields.text({
            bind: { provider: { textField: true } },
            isReadOnly: true,
            title: 'Description',
        }),
        ui.nestedFields.numeric({ bind: 'netPrice', title: 'Price' }),
    ],
});
ui.console.log(result);
this.$.showToast(
    `Selected ${result.length} products\n${result.map(p => `\n- ${p.product} - ${p.netPrice}`).join('\n')}`,
    { language: 'markdown' },
);
```

On the top of traditional dialog options, the lookup dialog takes the following options:
- `node`: The node type which the lookup dialog displays the options from. Mandatory
- `isMultiSelect`: If set, the user is allowed to select multiple items. Defaults to false.
- `isEditable`: If set, the user is allowed to make modifications to the row data. In this case, all columns that are not expected to be edited should be marked as `isReadOnly: true`. Defaults to false.
- `filter`: GraphQL filter expression that filters the list of options.
- `orderBy`: GraphQL order expression that sets the default sorting order of the list of options.
- `columns`: List of nested columns that is displayed in the lookup dialog. If not defined, the columns are automatically configured based on the datatype definition for the selected node.

## Async loader dialog

The async dialog loader dialog is a dialog that executes an async mutation. First, it displays a full screen loader for 8 seconds, then the user is presented with a choice whether they wish to keep waiting or be notified once the operation ended.

When the keep waiting button is clicked, then the full screen loader is displayed for another 8 seconds.
When the notify me option button is clicked, then the client requests the server to send a notification center notification once the operation ended. This notification must be implemented by the application code on the server. In this case, the dialog is resolved with no result.

If the operation finishes while the dialog or the loader is displayed, the dialog/loader is automatically closed and the dialog is resolved with the selected result of the async mutation.

Example:

```ts
  const result = await this.$.dialog.asyncLoader(
    '@sage/xtrem-show-case/ShowCaseCustomer',
    'asyncTestMutation',
    {
        customerId: this.customer.value?._id,
        newName: this.newCustomerName.value,
    },
    { name: true, _id: true },
    {
        isStopAvailable: this.canUserStopAsyncMutation.value ?? false,
        dialogContent: this.asyncDialogContent.value || undefined,
        dialogTitle: this.asyncDialogTitle.value || undefined,
    },
);

if (result) {
    this.$.showToast(`New customer name is successfully applied: ${result.name}`);
}
```

## Parameters:
- `nodeName`: the name of the node on which the async mutation is located on
- `mutationName`: the name of the async mutation
- `mutationParameters`: the parameters of the async mutation
- `selector`: the result selector, it is used to request the result once the operation finishes
- `dialogOptions`: Optional configuration properties
    - `isStopAvailable`: if set, an additional "Stop" button is displayed on the dialog. If clicked, the client requests the server to stop the operation
    - `dialogTitle`: Allows the app code to set an alternative dialog title
    - `dialogContent`: Allows the app code to set an alternative dialog content message



## Sandbox

Check out the dialogs on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Dialogs).
