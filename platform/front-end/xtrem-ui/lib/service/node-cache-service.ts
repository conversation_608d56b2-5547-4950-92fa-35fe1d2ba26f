import { MD5 } from 'crypto-js';
import { cloneDeep, isNil, isString } from 'lodash';
import { xtremConsole } from '../utils/console';
import { capitalize, getArtifactDescription } from '../utils/transformers';
import { hasNodeCacheLogging, isDevMode } from '../utils/window';
import { queryToGraphQuery } from './graphql-utils';

type NodeCache = Record<string, Record<string, any>>;

export class NodeCacheService {
    private static cache: NodeCache = {};

    private static async getCacheKeys(node: string, query: string): Promise<{ nodeName: string; queryHash: string }> {
        const nodeDescription = getArtifactDescription(node);
        const stringifiedQuery = isString(query) ? query.toString() : queryToGraphQuery(query);
        const queryHash = MD5(stringifiedQuery).toString();
        return { nodeName: nodeDescription.name, queryHash };
    }

    private static setGlobalCache(cache: NodeCache): void {
        if (isDevMode()) {
            (window as any).__XTREM_NODE_CACHE = cache;
        }
    }

    static flushCache(nodeNames?: string[]): void {
        const nodeNamesToFlush = nodeNames ?? Object.keys(NodeCacheService.cache);
        if (hasNodeCacheLogging()) {
            xtremConsole.log(
                `%cNODE CACHE - %cFLUSH %c${nodeNamesToFlush.join(', ')}`,
                'font-weight: bold;',
                'color: orange; font-weight: bold;',
                'font-weight: bold;',
            );
        }
        nodeNamesToFlush.forEach(nodeName => {
            delete NodeCacheService.cache[nodeName];
        });
        NodeCacheService.setGlobalCache(NodeCacheService.cache);
    }

    static flushCacheAfterMutation(mutation: NodeCache): void {
        Object.keys(mutation).forEach(packageName => {
            Object.keys(mutation[packageName]).forEach(nodeName => {
                const node = capitalize(nodeName);
                delete NodeCacheService.cache[node];
            });
        });
        NodeCacheService.setGlobalCache(NodeCacheService.cache);
    }

    static async get<T = any>(fullNodeName: string, query: string): Promise<T> {
        const { nodeName, queryHash } = await NodeCacheService.getCacheKeys(fullNodeName, query);
        const cachedItem = NodeCacheService.cache[nodeName]?.[queryHash];
        if (hasNodeCacheLogging()) {
            const isHit = !isNil(cachedItem);
            xtremConsole.log(
                `%cNODE CACHE - %c${isHit ? 'HIT' : 'MISS'} %c${nodeName}\n%c${query.toString()}`,
                'font-weight: bold;',
                isHit ? 'color: green; font-weight: bold;' : 'color: red; font-weight: bold;',
                'font-weight: bold;',
                'font-weight: lighter; font-size: xx-small;',
            );
        }
        return cloneDeep(cachedItem);
    }

    static async set<T = any>(fullNodeName: string, query: string, result: T): Promise<void> {
        const { nodeName, queryHash } = await NodeCacheService.getCacheKeys(fullNodeName, query);
        if (!NodeCacheService.cache[nodeName]) {
            NodeCacheService.cache[nodeName] = {};
        }
        NodeCacheService.cache[nodeName][queryHash] = cloneDeep(result);
        NodeCacheService.setGlobalCache(NodeCacheService.cache);
    }
}
