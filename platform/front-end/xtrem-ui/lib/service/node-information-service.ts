import type { Dict, NodeDetails, Property } from '@sage/xtrem-shared';
import { CUSTOM_DATA_PROPERTY, objectKeys } from '@sage/xtrem-shared';
import { getStore } from '../redux';
import { addSchemaInfo } from '../redux/actions/common-actions';
import { GraphQLKind, GraphQLTypes, textStreams } from '../types';
import { getArtifactDescription } from '../utils/transformers';
import { executeGraphqlQuery } from './graphql-utils';
import * as metadataService from './metadata-service';
import type { FormattedNodeDetailsProperty, RawNodeDetails } from './metadata-types';

export { NodeDetails };

interface NodeTitle {
    name: string;
    title: string;
}

export const fetchNodeNames = async (onlyPublishedNodes = false): Promise<Dict<string>> => {
    const response = await executeGraphqlQuery({
        query: {
            getNodeNames: {
                __args: {
                    onlyPublishedNodes,
                },
                name: true,
                title: true,
            },
        },
        endpoint: '/metadata',
    });
    if (Array.isArray(response.data?.getNodeNames)) {
        return response.data.getNodeNames
            .sort((a: NodeTitle, b: NodeTitle) => a.title.localeCompare(b.title))
            .reduce((prevValue: Dict<string>, currentValue: NodeTitle) => {
                return { ...prevValue, [currentValue.name]: currentValue.title };
            }, {} as Dict<string>);
    }

    throw new Error('Failed to load node names from the server');
};

export interface AttachmentInformation {
    attachmentAssociationNode: string;
    attachmentFileNode: string;
}

function getPropertyName(propertyDetails: FormattedNodeDetailsProperty): string {
    if (propertyDetails?.isCustom) {
        const prefix = `${CUSTOM_DATA_PROPERTY}.`;
        if (propertyDetails?.name?.startsWith(prefix)) {
            return propertyDetails.name;
        }
        return `${prefix}${propertyDetails.name}`;
    }

    if (propertyDetails?.type === GraphQLTypes.OutputTextStream) {
        return `${propertyDetails.name}.value`;
    }

    if (!propertyDetails.name) {
        throw new Error('Property name is missing');
    }

    return propertyDetails.name;
}

function getEnumType(
    propertyDetails: FormattedNodeDetailsProperty,
    enumTypes: Dict<{ values: string[]; translations: string[]; name: string }>,
): string[] | undefined {
    if (propertyDetails.type === GraphQLTypes.Enum && propertyDetails.enumType) {
        const { name } = getArtifactDescription(propertyDetails.enumType);
        return enumTypes[name]?.values;
    }
    return undefined;
}
function getNode(propertyDetails: FormattedNodeDetailsProperty): string | undefined {
    if (propertyDetails.kind === GraphQLKind.Object) {
        return propertyDetails.type;
    }
    if (propertyDetails.type === GraphQLTypes.Enum) {
        return propertyDetails.enumType;
    }
    return undefined;
}

const remapProperties = (
    properties: Dict<FormattedNodeDetailsProperty>,
    enumTypes: Dict<{
        values: string[];
        name: string;
        translations: string[];
    }>,
    getCollections: boolean,
): Dict<NodeDetails> => {
    return objectKeys(properties)
        .filter(
            propertyKey =>
                propertyKey !== CUSTOM_DATA_PROPERTY &&
                propertyKey !== '_etag' &&
                (getCollections || !properties[propertyKey].isCollection),
        )
        .reduce((prevValue: Dict<NodeDetails>, propertyKey: string) => {
            const propertyDetails = properties[propertyKey];

            if (propertyKey === '_id') {
                prevValue[propertyKey] = {
                    ...propertyDetails,
                    type: GraphQLTypes.IntOrString,
                    kind: GraphQLKind.Scalar,
                    name: propertyKey,
                    canFilter: true,
                    canSort: true,
                    label: '_id',
                    isStored: true,
                    isOnInputType: false,
                    isOnOutputType: true,
                    dataType: '',
                    targetNode: '',
                    enumType: null,
                    isCustom: false,
                    isMutable: false,
                };
            } else {
                prevValue[propertyKey] = {
                    ...propertyDetails,
                    name: getPropertyName(propertyDetails),
                    canFilter: propertyDetails?.canFilter || false,
                    canSort:
                        (propertyDetails.canSort && !textStreams.includes(propertyDetails.type as GraphQLTypes)) ||
                        false,
                    label: propertyDetails.title || propertyKey,
                    isStored: propertyDetails.isStored || false,
                    isOnInputType: propertyDetails.isOnInputType || false,
                    isOnOutputType: propertyDetails.isOnOutputType || false,
                    dataType: propertyDetails.dataType || '',
                    targetNode: propertyDetails.targetNode || '',
                    enumType: propertyDetails.enumType || null,
                    isCustom: propertyDetails.isCustom || false,
                    isMutable: propertyDetails.isMutable || false,
                    enumValues: getEnumType(propertyDetails, enumTypes),
                    node: getNode(propertyDetails),
                };
            }
            return prevValue;
        }, {} as Dict<NodeDetails>);
};

export const fetchNodeDetails = async ({
    locale,
    nodeName,
    getCollections = false,
}: {
    locale: string;
    nodeName: string;
    getCollections?: boolean;
}): Promise<Dict<NodeDetails>> => {
    const store = getStore();

    const nodeDetailsResponse = await metadataService.fetchNodeDetails([nodeName], 1);
    store.dispatch(
        addSchemaInfo(
            nodeDetailsResponse.nodeTypes,
            nodeDetailsResponse.dataTypes,
            nodeDetailsResponse.enumTypes,
            locale,
        ),
    );
    const nodeDetails = nodeDetailsResponse.nodeTypes[nodeName];
    if (!nodeDetails) {
        throw new Error(`No details found for node: ${nodeName}`);
    }
    return remapProperties(nodeDetails.properties, nodeDetailsResponse.enumTypes, getCollections);
};

export const fetchNodePackageName = async (nodeName: string): Promise<string> => {
    const packageNames = await metadataService.fetchNodePackageNames([nodeName], 1);
    return packageNames[0];
};

export const mapNodeDetailsToTreeProperty = (parent: Property, nodeDetails: NodeDetails[]): Property[] => {
    return nodeDetails.map(nodeInfo => ({
        data: {
            ...nodeInfo,
            canSort: parent.data.canSort && nodeInfo.canSort,
            canFilter: parent.data.canFilter && nodeInfo.canFilter,
        },
        key: parent.key ? `${parent.key}.${nodeInfo.name}` : nodeInfo.name,
        id: parent.key ? `${parent.key}.${nodeInfo.name}` : nodeInfo.name,
        labelKey: parent.label ? `${parent.label}.${nodeInfo.label}` : nodeInfo.label,
        labelPath: parent.label ? `${parent.label}.${nodeInfo.label}` : nodeInfo.label,
        label: nodeInfo.label,
        canBeExpanded:
            (nodeInfo.kind === 'OBJECT' && !textStreams.includes(nodeInfo.type as GraphQLTypes)) ||
            nodeInfo.kind === 'INTERFACE',
        canBeSelected:
            (nodeInfo.kind !== 'OBJECT' || textStreams.includes(nodeInfo.type as GraphQLTypes)) &&
            nodeInfo.kind !== 'INTERFACE',
    }));
};

export const rawNodeDetailsToTreeProperty = (
    parent: Property,
    rawNodeDetails: RawNodeDetails[],
    getCollections = false,
): Property[] => {
    const { nodeTypes, enumTypes } = metadataService.processNodeDetailsResponse(rawNodeDetails);
    const nodeDetails = nodeTypes[parent.data.type];

    const remappedNodeDetails = remapProperties(nodeDetails.properties, enumTypes, getCollections);
    return mapNodeDetailsToTreeProperty(parent, Object.values(remappedNodeDetails));
};
