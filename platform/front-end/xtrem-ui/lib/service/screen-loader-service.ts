import * as xtremAsyncHelper from '@sage/xtrem-async-helper';
import * as xtremClient from '@sage/xtrem-client';
import * as xtremDateTime from '@sage/xtrem-date-time';
import * as xtremDecimal from '@sage/xtrem-decimal';
import type { AccessStatus, Dict, LocalizeLocale, MetaCustomField } from '@sage/xtrem-shared';
import * as xtremShared from '@sage/xtrem-shared';
import * as lodash from 'lodash';
import * as React from 'react';
import type { UiComponentProperties } from '../component/abstract-ui-control-object';
import { navigationPanelId } from '../component/container/navigation-panel/navigation-panel-types';
import type { PageProperties, RuntimePageNavigationPanel } from '../component/container/page/page-types';
import type { OrderByType } from '../component/types';
import { ContainerKey } from '../component/types';
import type { AbstractWidget } from '../dashboard/widgets/abstract-widget';
import type { AppThunkDispatch, XtremAppState } from '../redux';
import { addPlugin, addTranslations, finishScreenLoading } from '../redux/actions';
import { addNodeTypes } from '../redux/actions/node-type-actions';
import type { ApplicationContext, UiComponentUserSettings } from '../redux/state';
import type { Constructible } from '../types';
import { xtremConsole } from '../utils/console';
import {
    DEFAULT_VIEW_ID,
    NEW_PAGE,
    PLUGIN_CACHE,
    QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER,
    QUERY_PARAM_SELECTED_SECTION_ID,
} from '../utils/constants';
import { arePlatformLiteralsMissing, getPagePropertiesFromPageDefinition } from '../utils/state-utils';
import { getArtifactDescription, schemaTypeNameFromNodeName } from '../utils/transformers';
import type { QueryParameters } from '../utils/types';
import { isDevMode } from '../utils/window';
import { errorDialog } from './dialog-service';
import * as graphqlService from './graphql-service';
import * as metadataService from './metadata-service';
import { getHeadersFromApplicationContext } from './graphql-utils';
import { localize, localizeEnumMember } from './i18n-service';
import type {
    ArtifactType,
    DataTypeDetails,
    ExportTemplateDetails,
    ExportTemplatesByNode,
    FormattedNodeDetails,
    Fragment,
    MetadataAccessPropertyBinding,
    MetadataAccessRightsResult,
    MetadataResponse,
    MetadataStringLiteralTypeEntry,
    UserSettings,
} from './metadata-types';
import {
    createNavigationPanelDefaultView,
    createNavigationPanelValue,
    createNavigationTableProperties,
    getInitialOptionMenuItem,
    getMergedOptionMenu,
} from './navigation-panel-service';
import type { Page } from './page';
import type { AccessBindings, PageDefinition } from './page-definition';
import { getPageDefinition } from './page-definition';
import { getPageMetadata, setPreferredScreenId, unsetPreferredScreenId } from './page-metadata';
import { getScreenElement } from './screen-base-definition';
import type { Sticker } from './sticker';
import type { StickerDefinition } from './sticker-definition';
import { getStickerDefinition } from './sticker-definition';
import { notifyConsumerOnError } from './telemetry-service';
import { formatScreenValues } from './value-formatter-service';
import { addDuplicationLayoutContainers } from '../utils/duplication-utils';
import {
    addAttachmentElements,
    getAttachmentInformation,
    setAttachmentCountOnMetadata,
} from '../utils/attachment-utils';
import type { TableViewLevel } from '../component/field/table/table-component-types';
import { getOrderByFromSortModel } from '../utils/table-component-utils';
import { getNonLazySectionsFromScreenDefinition } from '../utils/page-utils';
import type { ReadonlyFieldControlObject } from '../component/readonly-field-control-object';

const varNameRegex: RegExp = /^[a-zA-Z_$][0-9a-zA-Z_$]*$/;
const globalWhiteList: string[] = [
    'atob',
    'Boolean',
    'btoa',
    'clearInterval',
    'clearTimeout',
    'console',
    'Date',
    'isFinite',
    'isNaN',
    'JSON',
    'Number',
    'parseFloat',
    'parseInt',
    'require',
    'setInterval',
    'setTimeout',
    'String',
];

let xtremArtifact: Dict<Constructible<any>> = {};

export const getXtremArtifactVariable = (): Dict<Constructible<any>> => xtremArtifact;

export const getDeclarationPackage = (): string => (xtremArtifact as any).packageName;
declare global {
    let xtremArtifact: Dict<Constructible<any>>;
}

function evaluateJavascriptResponse<T>(
    scriptContent: string,
    className: string,
    extensions: { content: string; packageName: string }[] = [],
    fragments: Fragment[] = [],
): Constructible<T> {
    /**
     * BL: This data structure is needed to expose the module loaded via the webpack loader. All modules are added as
     * a new property, so later on we just need to pick the
     * entry module by the class name, which is provided in the last segment of the URL.
     * */
    xtremArtifact = {};

    /**
     * BL: injecting non-existing variables to the new function scope to prevent access to the corresponding real variables
     */
    const globalVariables = xtremShared.objectKeys(window);
    const globalVariablesToBlock: string[] = ['globalThis'];

    // eslint-disable-next-line no-restricted-syntax
    for (const globalVariableName of globalVariables) {
        if (
            globalVariableName.match(varNameRegex) &&
            !globalWhiteList.includes(globalVariableName) &&
            globalVariableName !== 'window'
        ) {
            globalVariablesToBlock.push(globalVariableName);
        }
    }

    // The main module is put to a local variable so we can initiate it.
    let contentToEvaluate = scriptContent;
    // Add extensions to the code to evaluate
    if (extensions.length > 0) {
        // We capture the loaded main module (e.g page or sticker) so extensions can get access to it
        contentToEvaluate += extensions
            .map(
                e =>
                    `\nxtremArtifact.packageName='${e.packageName}';\n${e.content}\nxtremArtifact.packageName=undefined;\n`,
            )
            .join('\n');
        // Clean up global scope
    }
    if (fragments.length > 0) {
        contentToEvaluate += fragments.map(f => `\n${f.content}`).join('\n');
    }

    // Create safe evaluation scope
    // eslint-disable-next-line @typescript-eslint/no-implied-eval
    const moduleDefinition = new Function(
        ...['require', 'xtremArtifact', 'window', ...globalVariablesToBlock, contentToEvaluate],
    );

    /**
     * Execute dynamically loaded modules, this is where ALL decorator functions are executed. The second parameter is
     * the fake Window object.
     **/
    moduleDefinition(xtremRequire, xtremArtifact);
    return xtremArtifact[className] as Constructible<T>;
}

/**
 * Fetches the plugin source code from the server or restores it from the cache.
 *
 * @param pluginPackage
 * @param applicationContext
 * @param cache
 * @returns plugin source code
 */
export async function fetchPlugin(
    pluginPackage: string,
    applicationContext: ApplicationContext,
    version: string,
    cache?: Cache,
): Promise<string> {
    const endpoint = `/plugins/${btoa(pluginPackage)}`;
    const url = applicationContext.path ? applicationContext.path + endpoint : endpoint;

    // For plugin caching, the CacheStorage API is used because it is capable of storing large files.
    if (cache && !isDevMode() && window.location.protocol === 'https:') {
        const cachedPlugin = await cache.match(url);

        if (cachedPlugin) {
            // If there is a match in the cache, we clone the response and then read its body.
            const clonedCacheEntry = cachedPlugin.clone();

            // Check if the stored plugin in the cache has the same version number as the one which is currently installed on the server
            if (clonedCacheEntry.headers.get('X-XTREM-PLUGIN-VERSION') === version) {
                return clonedCacheEntry.text();
            }

            // If the version is different, then we wipe out the cached version and proceed with making the request to the server
            await cache.delete(url);
        }
    }
    const headers = getHeadersFromApplicationContext();
    // Execute the request with native fetch.
    const result = await window.fetch(url, { headers });

    // If the response is OK, the response object is cloned and put into the CacheStorage before reading the content.
    if (result.status === 200) {
        if (cache && !isDevMode()) {
            cache.put(url, result.clone());
        }

        return result.text();
    }

    throw new Error(`Failed to load plugin: ${pluginPackage}`);
}

/**
 * Check and evaluate raw Xtrem UI plugin script content. If the plugin is compliant with the expected standards, then
 * it will be added to the application state.
 **/
export const loadPlugins = async (
    dispatch: AppThunkDispatch,
    plugins: string[],
    applicationContext: ApplicationContext,
    loadedPlugins: string[],
    applicationPackages: Dict<string>,
): Promise<void> => {
    if (plugins.length === 0) {
        return;
    }
    let cache: Cache | undefined;
    // The CacheStorage API is only available when hosted on HTTPS or localhost
    try {
        cache = window.caches ? await window.caches.open(PLUGIN_CACHE) : undefined;
    } catch {
        /**
         * Intentionally left empty. In Firefox the cache object is declared on insecure origins, but when the code tries to open the
         * cache, it throws an exception.
         *  */
    }
    // eslint-disable-next-line no-restricted-syntax
    for (const pluginPackage of plugins) {
        if (!loadedPlugins.includes(pluginPackage)) {
            // eslint-disable-next-line no-await-in-loop
            const pluginSourceCode = await fetchPlugin(
                pluginPackage,
                applicationContext,
                applicationPackages[pluginPackage],
                cache,
            );
            const exports: any = {};
            // eslint-disable-next-line @typescript-eslint/naming-convention
            const __webpack_nonce__ = (window as any).__webpack_nonce__;

            if (!__webpack_nonce__) {
                xtremConsole.warn('No nonce was found, plugins might not load');
            }

            // eslint-disable-next-line @typescript-eslint/no-implied-eval
            const func = new Function('require', 'exports', '__webpack_nonce__', pluginSourceCode);

            func(xtremRequire, exports);

            if (!exports.default) {
                throw new Error('The format of the plugin is invalid. Default export from main file is missing.');
            }

            if (!exports.default.component) {
                throw new Error('The format of the plugin is invalid. The plugin does not expose a component.');
            }

            dispatch(addPlugin(pluginPackage, exports.default));
        }
    }
};

const getArtifactErrorHandler =
    (artifactName: string) =>
    (error: any): null => {
        errorDialog(artifactName, 'Error', error.message ? error.message : error);
        notifyConsumerOnError(error);
        xtremConsole.error(error);
        return null;
    };

export const processStringLiteralsFromResponse = (
    metadataType: ArtifactType | undefined,
    response: MetadataResponse,
): Dict<string> => {
    const stringLiterals: MetadataStringLiteralTypeEntry[] = [];
    if (response.strings) {
        stringLiterals.push(...response.strings);
    }

    if (metadataType && lodash.get(response, `${metadataType}.[0].strings`)) {
        stringLiterals.push(...lodash.get(response, `${metadataType}.[0].strings`, []));
    }

    return stringLiterals.reduce((prevValue: Dict<string>, entry: MetadataStringLiteralTypeEntry) => {
        prevValue[entry.key] = entry.content;
        return prevValue;
    }, {} as Dict<string>);
};

/**
 * This function will mutate pageDefinition object in order to add all the info related to navigation panel.
 */
const setUpNavigationPanel = async ({
    applicationContext,
    dataResponse,
    dataTypes,
    nodeTypes,
    pageDefinition,
    path,
    queryParameters,
    screenId,
    serviceOptions,
}: {
    applicationContext: ApplicationContext;
    dataResponse: graphqlService.InitialData;
    dataTypes: Dict<DataTypeDetails>;
    nodeTypes: Dict<FormattedNodeDetails>;
    pageDefinition: PageDefinition;
    path: string;
    queryParameters: QueryParameters;
    screenId: string;
    serviceOptions?: Dict<boolean>;
}): Promise<void> => {
    const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);
    // Set navigation panel information and format items.
    const navigationPanelDefinition = pageProperties.navigationPanel as RuntimePageNavigationPanel<any>;

    if (navigationPanelDefinition) {
        navigationPanelDefinition.optionsMenu = await getMergedOptionMenu(
            pageDefinition,
            navigationPanelDefinition,
            applicationContext,
            serviceOptions,
        );
        const defaultNavPanelDefinition = (
            pageDefinition.metadata.defaultUiComponentProperties[screenId] as PageProperties
        ).navigationPanel;

        if (defaultNavPanelDefinition?.optionsMenu) {
            defaultNavPanelDefinition.optionsMenu = navigationPanelDefinition.optionsMenu;
        }

        const navigationPanelTableProperties = await createNavigationTableProperties(
            screenId,
            String(pageProperties.node),
            pageDefinition,
            nodeTypes,
            dataTypes,
            path,
            applicationContext,
            serviceOptions,
        );

        pageDefinition.metadata.uiComponentProperties[navigationPanelId] =
            navigationPanelTableProperties as UiComponentProperties;
        pageDefinition.metadata.defaultUiComponentProperties[navigationPanelId] =
            navigationPanelTableProperties as UiComponentProperties;

        const optionMenuItem = getInitialOptionMenuItem(pageDefinition, navigationPanelTableProperties);

        if (!pageDefinition.userSettings[navigationPanelId]?.$current) {
            pageDefinition.userSettings[navigationPanelId] = {
                $current: createNavigationPanelDefaultView({
                    defaultTableProperties: pageDefinition.metadata.defaultUiComponentProperties[navigationPanelId],
                    screenId,
                    dataTypes,
                    nodeTypes,
                    accessBindings: pageDefinition.accessBindings,
                    customMainListFilter: queryParameters[QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER],
                }),
            };
        }

        const selectedView = pageDefinition.userSettings?.[navigationPanelId]?.$current;
        const viewContent = selectedView.content?.[0] as TableViewLevel;
        const sortOrder: OrderByType =
            selectedView._id !== DEFAULT_VIEW_ID
                ? getOrderByFromSortModel(viewContent?.sortOrder || [], navigationPanelTableProperties.columns || [])
                : navigationPanelTableProperties.orderBy || {};
        pageDefinition.navigationPanel = {
            isHeaderHidden: false,
            isHidden: false,
            isOpened: !queryParameters._id,
            isRefreshing: false,
            value: createNavigationPanelValue(
                screenId,
                navigationPanelTableProperties,
                dataResponse.navigationPanel,
                nodeTypes,
                applicationContext.locale as LocalizeLocale,
                optionMenuItem,
                sortOrder,
            ),
        };
    }
};

/**
 * Fetches the page definition corresponding to the path from the server and evaluates the javascript response
 *
 * @param path the xtrem path in string format
 */
export const fetchPageDefinition = async ({
    getState,
    dispatch,
    path,
    isMainPage = false,
    queryParameters = {},
    values,
    onFinish,
    isDuplicate = false,
}: {
    getState: () => XtremAppState;
    dispatch: AppThunkDispatch;
    path: string;
    isMainPage?: boolean;
    queryParameters?: QueryParameters;
    values?: Dict<any>;
    onFinish?: (values?: Dict<any>) => void;
    isDuplicate?: boolean;
}): Promise<PageDefinition | null> => {
    const artifact = getArtifactDescription(path);
    const state = getState();
    const applicationContext = state.applicationContext;

    if (!applicationContext) {
        throw new Error('Cannot load page without an application context.');
    }

    const pagePackageVersion = state.applicationPackages?.[`${artifact.vendor}/${artifact.package}`];

    if (!pagePackageVersion) {
        throw new Error(`Cannot load page without the package version: ${artifact.vendor}/${artifact.package}`);
    }

    const artifactPath = `${artifact.vendor}/${artifact.package}/${artifact.name}`;
    const locale = (applicationContext.locale as LocalizeLocale) || 'en-US';

    const shouldFetchPlatformLiterals = arePlatformLiteralsMissing(state, locale);
    const applicationPackages = state.applicationPackages ?? {};

    try {
        // Load source code of the page and its extensions from the server
        const metadataResponse = await metadataService.queryMetadata({
            applicationPackages,
            metadataType: 'pages',
            metadataProperties: [
                'activeUserSettings',
                'content',
                'customFields',
                'customizableNodes',
                'customizableNodesWizard',
                'duplicateBindings',
                'exportTemplatesByNode',
                'extensions',
                'fragments',
                'hasRecordPrintingTemplates',
                'nodeDetails',
                'plugins',
            ],
            path: artifactPath,
            shouldFetchPlatformLiterals,
            exactMatch: true,
            locale,
        });

        const dataTypeInfo = metadataService.processNodeDetailsResponse(metadataResponse?.pages?.[0]?.nodeDetails);

        // Validate the data
        const pageContent = metadataResponse?.pages?.[0]?.content;

        const extensions = metadataResponse?.pages?.[0]?.extensions;

        const plugins = metadataResponse?.pages?.[0]?.plugins;

        const accessBindings = metadataResponse?.pages?.[0]?.access;

        const exportTemplatesByNode = metadataResponse?.pages?.[0]?.exportTemplatesByNode;

        const activeUserSettings: UserSettings[] = metadataResponse?.pages?.[0]?.activeUserSettings || [];

        const duplicateBindings = metadataResponse?.pages?.[0]?.duplicateBindings;

        const hasRecordPrintingTemplates = metadataResponse?.pages?.[0]?.hasRecordPrintingTemplates;

        const customFields = metadataResponse?.pages?.[0]?.customFields || [];
        const customizableNodes: string[] = (metadataResponse?.pages?.[0]?.customizableNodes || []).map(
            n => n?.fullName,
        );
        const customizableNodesWizard: string[] = (metadataResponse?.pages?.[0]?.customizableNodesWizard || []).map(
            n => n?.fullName,
        );

        const customFieldDictionary = customFields.reduce(
            (prev, current) => {
                const properties = current.properties.map<MetaCustomField>(p => {
                    if (p.componentAttributes) {
                        return { ...p, componentAttributes: JSON.parse(p.componentAttributes) };
                    }
                    return p;
                });
                prev[current.name] = properties;
                return prev;
            },
            {} as Dict<MetaCustomField[]>,
        );

        const pageFragments = metadataResponse?.pages?.[0]?.fragments;

        await loadPlugins(
            dispatch,
            plugins || [],
            applicationContext,
            xtremShared.objectKeys(state.plugins),
            applicationPackages,
        );

        if (!pageContent) {
            throw new Error(
                localize(
                    '@sage/xtrem-ui/no-page-content-found',
                    'Could not find any content for page. Make sure the page exists and the format of the URL is /@<vendor name>/@<package name>/@<page name>',
                ),
            );
        }

        const stringLiterals = processStringLiteralsFromResponse('pages', metadataResponse);
        dispatch(addTranslations(locale, stringLiterals));

        if (isDuplicate) {
            setPreferredScreenId(`${artifact.name}Duplicate`);
        }

        // Evaluate the artifact source code sent from the server and execute decorators
        const pageConstructor = evaluateJavascriptResponse<Page>(pageContent, artifact.name, extensions, pageFragments);

        // Get the page metadata that was created as the result of the execution above.
        const pageMetadata = getPageMetadata(pageConstructor);
        const mergedNodeTypes = { ...state.nodeTypes, ...dataTypeInfo.nodeTypes };

        if (pageMetadata.rootNode && pageMetadata.hasAttachmentsSection) {
            // Check if the node of the page manages attachments
            const attachmentInformation = getAttachmentInformation(pageMetadata.rootNode, mergedNodeTypes);

            if (attachmentInformation) {
                pageMetadata.attachmentInformation = attachmentInformation;
                // Add attachment blocks and fields to the page prototype
                addAttachmentElements(pageMetadata, attachmentInformation);
            }
        }
        // Add user-defined custom fields to the page metadata object so they can be accessed in the page decorator
        pageMetadata.customizations = customFieldDictionary;
        // Add list of customizable nodes to metadata for page rendering
        pageMetadata.customizableNodes = customizableNodes;
        // Add list of customizable nodes to metadata to be used by custom fields wizard
        pageMetadata.customizableNodesWizard = customizableNodesWizard;
        // Add the duplicate bindings that the user needs to modify in order to duplicate the page
        pageMetadata.duplicateBindings = duplicateBindings ?? [];

        pageMetadata.hasRecordPrintingTemplates = hasRecordPrintingTemplates ?? false;

        if (exportTemplatesByNode) {
            pageMetadata.exportTemplatesByNode = exportTemplatesByNode.reduce(
                (prevValue: Dict<Dict<string>>, currentValue: ExportTemplatesByNode): Dict<Dict<string>> => {
                    prevValue[currentValue.name] = currentValue.exportTemplates.reduce(
                        (innerPrevValue: Dict<string>, currentInnerValue: ExportTemplateDetails) => {
                            innerPrevValue[currentInnerValue.id] = currentInnerValue.name;
                            return innerPrevValue;
                        },
                        {} as Dict<string>,
                    );
                    return prevValue;
                },
                {} as Dict<Dict<string>>,
            );
        }

        const screenId = pageMetadata.screenId;

        const mergedDataTypes = { ...state.dataTypes, ...dataTypeInfo.dataTypes };

        // Build the layouts and assign control objects to the page object.
        if (pageMetadata.pageThunk) {
            if (isDuplicate) {
                addDuplicationLayoutContainers(pageMetadata);
            }
            pageMetadata.controlObjects[screenId] = pageMetadata.pageThunk(mergedNodeTypes, mergedDataTypes);
            pageMetadata.pageExtensionThunks.forEach(pt => pt());
            pageMetadata.pageFragmentThunks.forEach(pt => pt());
        }

        const pageDefinition = getPageDefinition(pageConstructor, artifactPath, isMainPage, isDuplicate);
        pageDefinition.onFinish = onFinish;
        if (accessBindings) {
            pageDefinition.accessBindings = accessBindings.reduce(
                (prevValue: AccessBindings, currentValue: MetadataAccessRightsResult): AccessBindings => {
                    prevValue[schemaTypeNameFromNodeName(currentValue.node)] = currentValue.bindings.reduce(
                        (innerPrevValue: Dict<AccessStatus>, currentInnerValue: MetadataAccessPropertyBinding) => {
                            innerPrevValue[currentInnerValue.name] = currentInnerValue.status;
                            return innerPrevValue;
                        },
                        {} as Dict<AccessStatus>,
                    );
                    return prevValue;
                },
                {} as AccessBindings,
            );
        }

        pageDefinition.userSettings = activeUserSettings.reduce(
            (prevValue: Dict<Dict<UiComponentUserSettings>>, currentView: UserSettings) => {
                prevValue[currentView.elementId] = {
                    $current: {
                        _id: currentView._id,
                        description: currentView.description,
                        title: currentView.title,
                        content: JSON.parse(currentView.content),
                    },
                };

                return prevValue;
            },
            {} as Dict<Dict<UiComponentUserSettings>>,
        );

        const defaultEntry = pageDefinition.metadata.defaultEntryThunk
            ? await pageDefinition.metadata.defaultEntryThunk.apply(getScreenElement(pageDefinition))
            : null;

        if (defaultEntry && !queryParameters._id) {
            queryParameters._id = defaultEntry;
        }

        pageDefinition.queryParameters = queryParameters;

        const activeSection = queryParameters[QUERY_PARAM_SELECTED_SECTION_ID];
        if (
            typeof activeSection === 'string' &&
            pageDefinition.metadata.uiComponentProperties[activeSection]._controlObjectType === ContainerKey.Section
        ) {
            pageDefinition.activeSection = activeSection;
        }

        unsetPreferredScreenId();

        const enumTypes = await metadataService.fetchInitialEnumSchemaInfo(pageDefinition, pagePackageVersion, locale, [
            ...xtremShared.objectKeys(dataTypeInfo.enumTypes),
            ...xtremShared.objectKeys(state.enumTypes),
        ]);

        const mergedEnumTypes = { ...dataTypeInfo.enumTypes, ...enumTypes };

        dispatch(
            finishScreenLoading(
                pageDefinition,
                dataTypeInfo.nodeTypes,
                dataTypeInfo.dataTypes,
                mergedEnumTypes,
                locale,
            ),
        );

        /**
         * If the query arguments have an _id field or a defaultEntry property was declared on the page, the
         * transactional data will be requested from the server to populate the fields on the screen.
         *
         *  */

        const recordId =
            queryParameters.mode === 'create' || queryParameters._id === NEW_PAGE ? NEW_PAGE : queryParameters._id;

        const dataResponse = await graphqlService.fetchInitialData({
            pageDefinition,
            recordId: recordId ? String(recordId) : undefined,
            path: artifactPath,
            applicationContext,
            nodeTypes: mergedNodeTypes,
            dataTypes: mergedDataTypes,
            values,
            isDuplicate,
            serviceOptions: state.serviceOptions,
            includeFieldsWithNoParent: true,
            requiredSections: !isDuplicate ? getNonLazySectionsFromScreenDefinition(pageDefinition) : undefined,
        });

        if (pageMetadata.rootNode && pageMetadata.hasAttachmentsSection) {
            const attachmentInformation = getAttachmentInformation(pageMetadata.rootNode, mergedNodeTypes);
            if (attachmentInformation) {
                setAttachmentCountOnMetadata(pageMetadata, dataResponse.values);
            }
        }

        const loadedPlugins = getState().plugins;

        pageDefinition.queryParameters = queryParameters;

        await setUpNavigationPanel({
            applicationContext,
            dataResponse,
            nodeTypes: mergedNodeTypes,
            dataTypes: mergedDataTypes,
            pageDefinition,
            queryParameters,
            screenId,
            path: artifactPath,
            serviceOptions: state.serviceOptions,
        });

        const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);

        // Format screen values before setting them to the state.
        pageDefinition.values = formatScreenValues({
            pageDefinition,
            screenId,
            controlObjects: pageDefinition.metadata.controlObjects as Dict<ReadonlyFieldControlObject<any, any, any>>,
            plugins: loadedPlugins,
            nodeTypes: mergedNodeTypes,
            values: dataResponse.values,
            parentNode: pageProperties.node as string,
            userSettings: pageDefinition.userSettings,
        });

        pageDefinition.initialValues = { ...values };

        return pageDefinition;
    } catch (e) {
        unsetPreferredScreenId();
        getArtifactErrorHandler(artifact.name)(e);
        return null;
    }
};

/**
 * Fetches the sticker definition corresponding to the path from the server and evaluates the javascript response
 *
 * @param path the xtrem path in string format
 */
export const fetchStickerDefinition = async (
    getState: () => XtremAppState,
    dispatch: AppThunkDispatch,
    path: string,
    onFinish?: (values?: Dict<any>) => void,
): Promise<StickerDefinition | null> => {
    const artifact = getArtifactDescription(path);
    const state = getState();

    const artifactPath = `${artifact.vendor}/${artifact.package}/${artifact.name}`;
    const locale = (state.applicationContext?.locale as LocalizeLocale) || 'en-US';

    const pagePackageVersion = state.applicationPackages?.[`${artifact.vendor}/${artifact.package}`];

    if (!pagePackageVersion) {
        throw new Error(`Cannot load page without the package version: ${artifact.vendor}/${artifact.package}`);
    }

    try {
        const metadataResponse = await metadataService.queryMetadata({
            applicationPackages: state.applicationPackages!,
            metadataType: 'stickers',
            metadataProperties: ['content'],
            path: artifactPath,
            shouldFetchPlatformLiterals: false,
            exactMatch: true,
            locale,
        });

        const stickerContent =
            metadataResponse &&
            metadataResponse.stickers &&
            metadataResponse.stickers[0] &&
            metadataResponse.stickers[0].content;
        if (!stickerContent) {
            throw new Error(
                localize('@sage/xtrem-ui/no-sticker-content-found', 'Could not find any content for this sticker'),
            );
        }

        const stringLiterals = processStringLiteralsFromResponse('stickers', metadataResponse);
        dispatch(addTranslations(locale, stringLiterals));

        const stickerConstructor = evaluateJavascriptResponse<Sticker>(stickerContent, artifact.name);
        const stickerDefinition = getStickerDefinition(stickerConstructor);
        stickerDefinition.onFinish = onFinish;

        const initialTypes = await metadataService.fetchInitialNodeDetails(
            stickerDefinition,
            pagePackageVersion,
            locale,
            xtremShared.objectKeys(state.nodeTypes),
        );
        if (initialTypes?.nodeTypes) {
            dispatch(addNodeTypes(initialTypes.nodeTypes));
        }
        return stickerDefinition;
    } catch (err) {
        return getArtifactErrorHandler(artifact.name)(err);
    }
};
/**
 * Fetches the sticker definition corresponding to the path from the server and evaluates the javascript response
 *
 * @param path the xtrem path in string format
 */
export const fetchWidgetDefinitions = async (
    getState: () => XtremAppState,
    dispatch: AppThunkDispatch,
    widgetsToLoad: string[],
): Promise<Dict<Constructible<AbstractWidget>> | null> => {
    const state = getState();

    const locale = state.applicationContext?.locale || 'en-US';

    try {
        const widgetResponse = await Promise.all(
            widgetsToLoad.map(async (path: string) =>
                metadataService.queryMetadata({
                    applicationPackages: state.applicationPackages!,
                    metadataType: 'widgets',
                    metadataProperties: ['content'],
                    path,
                    shouldFetchPlatformLiterals: false,
                    exactMatch: true,
                    locale,
                }),
            ),
        );

        return widgetResponse
            .map((r: any, index: number) => {
                const stringLiterals = processStringLiteralsFromResponse('widgets', r);
                dispatch(addTranslations(locale, stringLiterals));
                const widgetSourceCode = r?.widgets?.[0]?.content;
                if (!widgetSourceCode) {
                    throw new Error(
                        localize(
                            '@sage/xtrem-ui/no-widget-content-found',
                            'Could not find any content for the {{widget}} widget',
                            { widget: widgetsToLoad[index] },
                        ),
                    );
                }

                return widgetSourceCode;
            })
            .reduce(
                (prevValue, c: string, index: number) => {
                    const widgetPath = widgetsToLoad[index];
                    const artifact = getArtifactDescription(widgetPath);
                    const widgetClassName = artifact.name;
                    const widgetConstructor = evaluateJavascriptResponse<AbstractWidget>(c, widgetClassName);
                    prevValue[widgetPath] = widgetConstructor;
                    return prevValue;
                },
                {} as Dict<Constructible<AbstractWidget>>,
            );
    } catch (err) {
        return getArtifactErrorHandler('widgets')(err);
    }
};

// Definition for the consumers
const xtremRequire = (moduleName: string): any => {
    switch (moduleName) {
        case '@sage/xtrem-ui':
            // eslint-disable-next-line global-require
            return require('..');
        case '@sage/xtrem-decimal':
            return xtremDecimal;
        case '@sage/xtrem-async-helper':
            return xtremAsyncHelper;
        case '@sage/xtrem-shared':
            return xtremShared;
        case '@sage/xtrem-client':
            return xtremClient;
        case '@sage/xtrem-date-time':
            return xtremDateTime;
        case '@sage/xtrem-i18n':
            return {
                localeText: localize,
                localizeEnumMember,
            };
        case 'lodash':
            return lodash;
        case 'react':
            return React;
        default:
            throw new Error(`Couldn't inject dependency: ${moduleName}`);
    }
};
