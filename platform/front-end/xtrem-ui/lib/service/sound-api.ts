import { getStore } from '../redux/store';

export class SoundApi {
    private readonly isFieldSoundEnabled: boolean;

    constructor(isFieldSoundDisabled = false) {
        this.isFieldSoundEnabled = !isFieldSoundDisabled;
    }

    async success(): Promise<void> {
        if (getStore().getState().applicationContext?.isPlaySoundEnabled && this.isFieldSoundEnabled) {
            const sound = new Audio(`${document.baseURI}/success.wav`);
            await sound.play();
        }
    }

    async error(): Promise<void> {
        if (getStore().getState().applicationContext?.isPlaySoundEnabled && this.isFieldSoundEnabled) {
            const sound = new Audio(`${document.baseURI}/error.wav`);
            await sound.play();
        }
    }
}
