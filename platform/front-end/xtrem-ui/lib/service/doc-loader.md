PATH: XTREEM/Client+Framework/Loader+API

This page describes how to block user interactions with a full screen loader.

## Introduction

The Loader API can be used to block all user interactions. When the loader activated, a dialog with a spinner is displayed that prevents the user from clicking anywhere on the screen. It can be used for example, when querying the graph for data that is essential to determine continue the user journey.

Please keep in mind that after displaying the loader, it will be displayed until the code instructs it to hide again. It is not automatically hidden in case of an unhandled error.

When unhandled errors occur in the event handlers, the loader is automatically hidden by the framework.

## Example:

```
...
async onChange() {
    this.$.loader.isHidden = false;

    const result = await this.$.graph
        .node('@sage/x3-project-management/operationAssignment')
        .query({ ...queryParameters })
        .execute();

    this.$.loader.isHidden = true;
}
...
```

### Properties:

-   **isHidden**: Whether the loader is displayed on the screen.

## Sandbox

Check out the loader on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Loader).
