/**
 * This service updates the page definitions to include user defined custom fields, which are loaded to the product
 * from the `customFields` page metadata property
 */

import { CUSTOM_DATA_PROPERTY, type Dict, type MetaCustomField, objectKeys } from '@sage/xtrem-shared';
import { insertColumn } from '../component/abstract-decorator-utils';
import { navigationPanelId } from '../component/container/navigation-panel/navigation-panel-types';
import type { BlockControlObject } from '../component/control-objects';
import type { EditableFieldProperties } from '../component/editable-field-control-object';
import type { PropertyValueType } from '../component/field/reference/reference-types';
import type { ExtensionField, HasOptions, HasParent, Mappable } from '../component/field/traits';
import type { NestedField } from '../component/nested-fields';
import type { ReadonlyFieldProperties } from '../component/readonly-field-control-object';
import type { ComponentKey, ControlObjectInstance } from '../component/types';
import { <PERSON>Key } from '../component/types';
import type { Constructible } from '../types';
import type { Page } from './page';
import type { PageMetadata } from './page-metadata';
import type { FormattedNodeDetails } from './metadata-types';
import { findDeepPropertyDetails, findDeepPropertyType } from '../utils/node-utils';
import { schemaTypeNameFromNodeName } from '../utils/transformers';
import { camelCase, set, slice, uniqBy } from 'lodash';
import { convertDeepBindToPathNotNull } from '../utils/nested-field-utils';
import { textField } from '../component/field/text/text-decorator';
import { selectField } from '../component/field/select/select-decorator';
import { switchField } from '../component/field/switch/switch-decorator';
import { numericField } from '../component/field/numeric/numeric-decorator';
import { checkboxField } from '../component/field/checkbox/checkbox-decorator';
import { dateField } from '../component/field/date/date-decorator';
import { multiDropdownField } from '../component/field/multi-dropdown/multi-dropdown-decorator';
import * as nestedFields from '../component/nested-fields';

const createBasicFieldPropertiesFromCustomFieldConfig = (
    f: MetaCustomField,
    fieldBind: string,
    parentPath: string[],
): ReadonlyFieldProperties & { bind: PropertyValueType } => {
    const properties = f.componentAttributes || {};
    const pathComponents = [...parentPath, CUSTOM_DATA_PROPERTY, fieldBind];
    return {
        ...properties,
        title: properties.title || f.name,
        // We use standard deep binding for the field.
        bind: set({}, pathComponents.join('.'), true),
    };
};

function getOptionsFromProperties(f: MetaCustomField): HasOptions<any> & Mappable<any> {
    const availableOptions = f.componentAttributes.options || [];

    const optionDictionary = availableOptions
        .filter((item: any) => item.isActive === undefined || item.isActive)
        .reduce((prevValue: Dict<string>, currentValue: any) => {
            prevValue[currentValue.technicalName] = currentValue.displayedName;
            return prevValue;
        }, {} as Dict<string>);

    return {
        options: objectKeys(optionDictionary),
        map: v => optionDictionary[v],
    };
}

export const addCustomFieldsToPageBody = (
    nodeTypes: Dict<FormattedNodeDetails>,
    pageMetadata: PageMetadata,
    pageConstructor: Constructible<Page>,
): void => {
    const node = schemaTypeNameFromNodeName(pageMetadata.rootNode);
    if (!pageMetadata.customizations) {
        return;
    }

    objectKeys(pageMetadata.fieldBindings).forEach(elementId => {
        if (!pageMetadata.rootNode) {
            return;
        }
        const bind = pageMetadata.fieldBindings[elementId];
        // We get the parent node type of the property and check if it is mutable.
        const bindPath = bind.split('.');
        const fieldBind = bindPath.pop();
        if (!fieldBind) {
            return;
        }

        let parentNodeType = pageMetadata.rootNode;
        if (bindPath.length) {
            // If it is a deep bound property, we need to ensure that it is mutable
            // TODO: In case of at least 2 degree depth, we should validate that intermediate nodes are mutable too.
            const details = findDeepPropertyType(node, bindPath.join('.'), nodeTypes);
            if (!details?.isMutable || !details?.targetNode) {
                return;
            }
            parentNodeType = details.targetNode;
        }

        const customFieldsToInsert = pageMetadata.customizations[parentNodeType]?.filter(
            c => c.anchorPropertyName === fieldBind,
        );

        customFieldsToInsert?.forEach(f => {
            const fieldName = `_customField_${camelCase(schemaTypeNameFromNodeName(parentNodeType))}_${f.name}`;
            const properties: EditableFieldProperties & ExtensionField<any, any> & HasParent<any, BlockControlObject> =
                {
                    ...createBasicFieldPropertiesFromCustomFieldConfig(f, f.name, bindPath),
                    ...(f.anchorPosition === 'before'
                        ? {
                              insertBefore(): ControlObjectInstance<any> | null {
                                  return pageMetadata.controlObjects[elementId];
                              },
                          }
                        : {}),
                    ...(f.anchorPosition === 'after'
                        ? {
                              insertAfter(): ControlObjectInstance<any> | null {
                                  return pageMetadata.controlObjects[elementId];
                              },
                          }
                        : {}),

                    parent() {
                        const anchorFieldProps = pageMetadata.uiComponentProperties[elementId] as HasParent<
                            any,
                            BlockControlObject
                        >;
                        return anchorFieldProps?.parent?.apply(pageMetadata.target);
                    },
                };

            // We call the decorator function of the corresponding field type. It creates the control object, updates the layout, just like for an ordinary field.
            switch (f.componentType) {
                case 'textField':
                    textField(properties)(pageConstructor.prototype, fieldName);
                    break;
                case 'numericField':
                    numericField(properties)(pageConstructor.prototype, fieldName);
                    break;
                case 'checkboxField':
                    checkboxField(properties)(pageConstructor.prototype, fieldName);
                    break;
                case 'switchField':
                    switchField(properties)(pageConstructor.prototype, fieldName);
                    break;
                case 'dateField':
                    dateField(properties)(pageConstructor.prototype, fieldName);
                    break;
                case 'selectField':
                    selectField({ ...properties, ...getOptionsFromProperties(f) })(
                        pageConstructor.prototype,
                        fieldName,
                    );
                    break;
                case 'multiDropdownField':
                    multiDropdownField({ ...properties, ...getOptionsFromProperties(f) })(
                        pageConstructor.prototype,
                        fieldName,
                    );
                    break;
                default:
                    throw new Error(`Unhandled custom field: ${f.componentType}`);
            }
        });
    });
};

export const componentsWithLookup: ComponentKey[] = [
    FieldKey.Reference,
    FieldKey.FilterSelect,
    FieldKey.MultiReference,
    FieldKey.Pod,
];

export const addCustomColumnToNestedFields = (
    contextNode: string,
    nodeTypes: Dict<FormattedNodeDetails>,
    currentNestedFields: NestedField<any, any>[],
    elementId: string,
    componentType: ComponentKey,
    onlyInsertIfFound: boolean,
    customFields?: Dict<MetaCustomField[]>,
): void => {
    const nestedCustomFields = getCustomFields(
        contextNode,
        nodeTypes,
        currentNestedFields,
        elementId,
        componentType,
        customFields,
        onlyInsertIfFound,
    );
    if (nestedCustomFields.length === 0) {
        return;
    }
    nestedCustomFields.forEach(nestedField => {
        insertColumn(currentNestedFields, nestedField, false, onlyInsertIfFound);
    });
};

function shouldAddCustomField(f: MetaCustomField, elementId: string, componentType: ComponentKey): boolean {
    if (elementId === navigationPanelId) {
        return f.destinationTypes?.includes('navigationBar') || false;
    }

    if (componentsWithLookup.includes(componentType)) {
        return f.destinationTypes?.includes('lookup') || false;
    }

    return f.destinationTypes?.includes('page') || false;
}

export const getCustomFields = (
    contextNode: string,
    nodeTypes: Dict<FormattedNodeDetails>,
    currentNestedFields: NestedField<any, any>[],
    elementId: string,
    componentType: ComponentKey,
    customFields?: Dict<MetaCustomField[]>,
    onlyInsertIfFound = false,
): NestedField<any, any>[] => {
    if (!customFields || !contextNode || !nodeTypes) {
        return [];
    }
    const node = schemaTypeNameFromNodeName(contextNode);

    return uniqBy(
        currentNestedFields.flatMap(({ properties }): NestedField<any, any>[] => {
            const bind = convertDeepBindToPathNotNull(properties.bind);
            // We get the parent node type of the property and check if it is mutable.
            const bindPath = bind.split('.');
            const fieldBind = bindPath.pop();
            if (!fieldBind) {
                return [];
            }

            let parentNodeType = contextNode;
            if (bindPath.length) {
                // If it is a deep bound property, we need to ensure that it is mutable
                // TODO: In case of at least 2 degree depth, we should validate that intermediate nodes are mutable too.
                const details = findDeepPropertyType(node, bindPath.join('.'), nodeTypes);
                if (!details?.isMutable || !details?.targetNode) {
                    return [];
                }
                parentNodeType = details.targetNode;
            }

            const customFieldsToInsert =
                customFields[parentNodeType]?.filter(
                    c =>
                        shouldAddCustomField(c, elementId, componentType) &&
                        (c.anchorPropertyName === fieldBind || !onlyInsertIfFound),
                ) || [];

            return (
                customFieldsToInsert.map((f): NestedField<any, any> => {
                    const anchorBinding = [...bindPath, f.anchorPropertyName].join('.');
                    const properties = {
                        ...createBasicFieldPropertiesFromCustomFieldConfig(f, f.name, bindPath),
                        ...(f.anchorPosition === 'before' ? { insertBefore: anchorBinding } : {}),
                        ...(f.anchorPosition === 'after' ? { insertAfter: anchorBinding } : {}),
                    };

                    switch (f.componentType) {
                        case 'textField':
                            return nestedFields.text(properties);
                        case 'numericField':
                            return nestedFields.numeric(properties);
                        case 'checkboxField':
                            return nestedFields.checkbox(properties);
                        case 'switchField':
                            return nestedFields.switch(properties);
                        case 'dateField':
                            return nestedFields.date(properties);
                        case 'selectField':
                            return nestedFields.select({ ...properties, ...getOptionsFromProperties(f) });
                        case 'multiDropdownField':
                            return nestedFields.multiDropdown({ ...properties, ...getOptionsFromProperties(f) });
                        default:
                            throw new Error(`Unhandled custom field column: ${f.componentType}`);
                    }
                }) || []
            );
        }),
        f => convertDeepBindToPathNotNull(f.properties.bind),
    );
};

/*
 * This function checks the nodeTypes in order to determine if is a NOT a vital Node and set the custom field as readonly
 */
export const isCustomFieldReadOnly = (
    nodeTypes: Dict<FormattedNodeDetails>,
    nodeName: string,
    bind: string,
): boolean => {
    const nodeType = findDeepPropertyDetails(nodeName, bind, nodeTypes);
    return !nodeType?.isMutable;
};

export function calculateDeepPaths(propertyPath: string): {
    subpath: string[];
    selectorSegments: string[];
    mainSegments: string[];
    alias: string;
} {
    const alias = propertyPath.split('.').join('__');
    const aggregationPropertySegments = propertyPath.split('.');
    const customDataLocation = aggregationPropertySegments.indexOf(CUSTOM_DATA_PROPERTY);
    const mainSegments = slice(
        aggregationPropertySegments,
        0,
        customDataLocation === -1 ? undefined : customDataLocation + 1,
    );

    // Properties that are inside the _customData JSON object
    const selectorSegments =
        customDataLocation === -1 ? [] : slice(aggregationPropertySegments, customDataLocation + 1);

    const subpath = [alias, ...slice(mainSegments, 1)];

    return { subpath, selectorSegments, mainSegments, alias };
}
