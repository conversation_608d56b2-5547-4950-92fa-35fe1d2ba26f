import type { ClientN<PERSON>, Filter, FilterOperator } from '@sage/xtrem-client';
import { flat, objectKeys } from '@sage/xtrem-shared';
import type { AxiosError, AxiosRequestConfig, CancelToken } from 'axios';
import axios from 'axios';
import type * as idb from 'idb';
import type { EnumType } from 'json-to-graphql-query';
import { jsonToGraphQLQuery } from 'json-to-graphql-query';
import { camelCase, escapeRegExp, get, isString, set } from 'lodash';
import {
    NestedGridControlObject,
    PodCollectionControlObject,
    TableControlObject,
    VitalPodControlObject,
} from '../component/control-objects';
import type { PageDecoratorProperties } from '../component/decorator-properties';
import * as xtremRedux from '../redux';
import type { ApplicationContext } from '../redux/state';
import type { NodePropertyType } from '../types';
import type { ArtifactCacheEntry } from '../utils/artifact-cache-utils';
import { cacheArtifact, getArtifactCacheDatabase, getArtifactCachedEntry } from '../utils/artifact-cache-utils';
import { xtremConsole } from '../utils/console';
import { PLUGIN_CACHE } from '../utils/constants';
import { hasQueryLogging, isDevMode } from '../utils/window';
import { CollectionValue } from './collection-data-service';
import { purgeMetaDataCache } from './metadata-service';
import { NodeCacheService } from './node-cache-service';
import { PromiseTracker } from './promise-tracker';
import type { ScreenBaseDefinition } from './screen-base-definition';

export type ExecutableQuery = QueryWrapper<NodeCollection> | QueryEnumWrapper | object | string;

export interface GraphqlCacheSettings {
    key: string;
    version: string;
    locale: string;
    shouldFetchPlatformLiterals: boolean;
}

export const queryToGraphQuery = (query: any): string =>
    jsonToGraphQLQuery(objectKeys(query)[0] !== 'mutation' ? { query } : query, { pretty: isDevMode() });

export function escapeStringRegexp(input: string): string {
    return escapeRegExp(input);
}

export interface ExecuteGraphqlQueryArgs {
    /** The GraphQL query to execute. */
    query: ExecutableQuery;
    /** The endpoint to run the query against, "/api" by default */
    endpoint?: string;
    /** Cache key components, the query first executed against the locale cache, if not met the server is called. The result is put into the cache */
    axiosCancelToken?: CancelToken;
    /** If set, the cache is not used for fetching data, but the result is stored. */
    forceRefetch?: boolean;

    cacheSettings?: GraphqlCacheSettings;
}

/**
 * The function that executes all API calls. It adds request headers based on the application context.
 * If `cacheSettings` provided and the request is successful, the response is put into the cache.
 */
const MAX_ATTEMPTS = 3;

const waitForMs = (ms: number): Promise<any> => new Promise(resolve => setTimeout(resolve, ms));

const axiosPostWithRetry = (url: string, query: any, config: AxiosRequestConfig): Promise<any> => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
        for (let i = 0; i < MAX_ATTEMPTS; i += 1) {
            try {
                const result = await axios.post(url, query, config);
                resolve(result);
                break;
            } catch (err) {
                const error = err as AxiosError;
                // If the response is a 503, and we have a retry timeout and it is not the last attempt, we try again
                if (
                    error.response &&
                    error.response.status === 503 &&
                    error.response.headers['retry-after'] &&
                    i < MAX_ATTEMPTS - 1
                ) {
                    const ms = parseInt(error.response.headers['retry-after'], 10);
                    await waitForMs(ms);
                } else {
                    // otherwise we fail
                    reject(error);
                    break;
                }
            }
        }
    });
};
export const untrackedExecuteGraphqlQuery = async ({
    query,
    endpoint = '/api',
    cacheSettings,
    axiosCancelToken,
    forceRefetch,
}: ExecuteGraphqlQueryArgs): Promise<any> => {
    const stringifiedQuery = isString(query) ? query.toString() : queryToGraphQuery(query);
    const shouldUseCache = window.location.protocol === 'https:' && !isDevMode() && !!cacheSettings;
    const applicationContext: ApplicationContext | null = xtremRedux.getStore().getState().applicationContext;

    let db: idb.IDBPDatabase | null = null;
    const passphrase = applicationContext?.cacheEncryptionKey;
    if (shouldUseCache) {
        // Open database connection to IndexedDB for caching operations
        db = await getArtifactCacheDatabase();
    }

    if (hasQueryLogging()) {
        xtremConsole.log(stringifiedQuery);
    }

    // If the cache is enabled, we search the artifact object store instead of sending a request to the server.
    if (db && shouldUseCache && !forceRefetch) {
        const value: ArtifactCacheEntry = await getArtifactCachedEntry({ db, cacheSettings, passphrase });
        if (value) {
            return {
                ...value,
                key: value.key,
                version: value.version,
                locale: value.locale,
                shouldFetchPlatformLiterals: value.shouldFetchPlatformLiterals,
                cachedAt: value.cachedAt,
            };
        }
    }

    // Configure the request
    const headers = getHeadersFromApplicationContext(applicationContext);
    const url = applicationContext && applicationContext.path ? applicationContext.path + endpoint : endpoint;

    try {
        const response = await axiosPostWithRetry(
            url,
            { query: stringifiedQuery },
            { headers, ...(axiosCancelToken ? { cancelToken: axiosCancelToken } : {}) },
        );
        // Check if cache should be used and the query was executed successfully
        if (
            db &&
            shouldUseCache &&
            cacheSettings &&
            response.status === 200 &&
            !response.data.errors &&
            response.data.data
        ) {
            await cacheArtifact({ db, cacheSettings, data: response.data, passphrase });
        }

        if (hasQueryLogging()) {
            xtremConsole.log(JSON.stringify(response.data, null, 4));
        }

        return response.data;
    } catch (err) {
        if (applicationContext?.onApiRequestError) {
            applicationContext?.onApiRequestError(err.response || err);
        }
        throw err;
    }
};

export const executeGraphqlQueryWithCache = async ({
    query,
    endpoint = '/api',
    cacheSettings,
    axiosCancelToken,
    forceRefetch,
    node,
}: ExecuteGraphqlQueryArgs & { node: string }): Promise<any> => {
    const isNodeCacheDisabled = xtremRedux.getStore().getState().applicationContext?.isNodeCacheDisabled ?? false;
    const nodeName = String(node);
    const stringifiedQuery = isString(query) ? query.toString() : queryToGraphQuery(query);
    const cachedResponse = isNodeCacheDisabled ? false : await NodeCacheService.get(nodeName, stringifiedQuery);
    if (cachedResponse) {
        return cachedResponse;
    }
    const response = await executeGraphqlQuery({ query, axiosCancelToken, endpoint, cacheSettings, forceRefetch });
    if (!isNodeCacheDisabled) {
        await NodeCacheService.set(node, stringifiedQuery, response);
    }
    return response;
};

export const executeGraphqlQuery = async ({
    query,
    endpoint = '/api',
    cacheSettings,
    axiosCancelToken,
    forceRefetch,
}: ExecuteGraphqlQueryArgs): Promise<any> => {
    return PromiseTracker.withTracker(async () =>
        untrackedExecuteGraphqlQuery({ query, endpoint, cacheSettings, axiosCancelToken, forceRefetch }),
    );
};

export const covertPageToStringKey = (path: string): string => {
    const components = path.split('/');
    return `${components[0]}/${components[1]}`;
};

export const getHeadersFromApplicationContext = (
    applicationContext?: ApplicationContext | null,
): Record<string, string> => {
    let headers = {
        'Accept-Language': applicationContext?.locale || 'en-US',
    };

    if (applicationContext?.requestHeaders) {
        headers = { ...headers, ...applicationContext.requestHeaders };
    }

    return headers;
};

export type GraphQLFilterOperator = FilterOperator<any>;
export type NonArrayGraphQLFilter = GraphQLFilterOperator | GraphQLFilter | EnumType | undefined;
export type GraphQLFilter<T extends ClientNode = any> = Filter<T>;

export type GraphQLFilterValueType = FilterOperator<any> | GraphQLFilter[] | GraphQLFilter | EnumType | undefined;

export interface GraphQLPropertyObject {
    name: string;
    properties?: GraphQLProperty[];
    isCollection?: boolean;
    options?: GraphQLQueryArguments;
}

export type GraphQLProperty = string | GraphQLPropertyObject;

export interface GraphQLQueryArguments<T extends ClientNode = any> {
    first?: number;
    last?: number;
    after?: string;
    before?: string;
    filter?: GraphQLFilter<T>;
    orderBy?: string[];
}

export interface GraphQLQueryOptions extends GraphQLQueryArguments {
    properties: GraphQLProperty[];
}

export interface GraphQLQuery {
    node: string;
    options: GraphQLQueryOptions;
}

export interface GraphQLRawQuery {
    node: string;
    query: string;
}

/* Framework filter classes  */

export interface QueryFilterOperator {
    _eq?: string | number;
    _ne?: string;
    _gt?: string | number;
    _gte?: string | number;
    _lt?: string | number;
    _lte?: string | number;
    _in?: string[];
    _nin?: string[];
    _regex?: string;
    _options?: string;
    _mod?: number[];
}

export type QueryProperty = string | Object;

export interface QueryEnum {
    __args: { name: string };
    __aliasFor?: string;
    enumValues: {
        name: boolean;
    };
}

export interface QueryArguments {
    after?: string;
    before?: string;
    filter?: string;
    first?: number;
    last?: number;
    orderBy?: string;
    skip?: number;
}

export interface QueryBuilderOptions {
    alias?: string;
    properties?: QueryProperty[];
    queryArguments?: QueryArguments;
    noPageInfo?: boolean;
}

export interface PageInfoQuery {
    endCursor?: boolean;
    hasNextPage?: boolean;
    hasPreviousPage?: boolean;
    startCursor?: boolean;
}
export interface PageInfoResult {
    endCursor?: string;
    hasNextPage?: boolean;
    hasPreviousPage?: boolean;
    startCursor?: string;
}

export interface CollectionQuery {
    __args?: QueryArguments;
    pageInfo?: PageInfoQuery;
    totalCount?: boolean;
    edges: { node: any; cursor?: boolean };
}

export interface TotalCountQuery {
    __aliasFor?: string;
    query: TotalCountSelector;
}

export interface TotalCountSelector {
    __args?: QueryArguments;
    totalCount: true;
}

export interface CollectionQueryResult<T extends ClientNode = any> {
    pageInfo?: PageInfoResult;
    totalCount?: number;
    edges: { node?: T; cursor?: string }[];
}

interface LookupQuery {
    __args?: { _id: string };
}
export interface NodeCollection {
    __aliasFor?: string;
    query: CollectionQuery;
    lookups?: LookupQuery;
}

export interface NodeCollectionResult<T extends ClientNode = any> {
    query: CollectionQueryResult<T>;
}

export interface QueryEnumWrapper {
    [typeName: string]: any;
}

export interface QueryWrapper<T> {
    [pkg: string]: {
        [key: string]: T;
    };
}

export type MixedQueryWrapper = QueryWrapper<NodeCollection> | QueryEnumWrapper;

export interface NavigationPanelData {
    navigationPanelCursor?: string;
    navigationPanelItems: any[];
    hasNextPage: boolean;
}

// TODO: this will fail if we have properties called 'edges' in our model
export const removeEdges = (target: NodeCollectionResult, keepPageInfo = false, keepCursor = false): any => {
    if (!target || typeof target !== 'object') {
        return target;
    }
    if (target.query && target.query.edges && Array.isArray(target.query.edges)) {
        const data = target.query.edges.map((e: any) => {
            const nextTarget = keepCursor ? { ...e.node, __cursor: e.cursor } : e.node;
            return removeEdges(nextTarget, keepPageInfo, keepCursor);
        });
        return keepPageInfo
            ? { data, pageInfo: target.query.pageInfo || {}, totalCount: target.query.totalCount }
            : data;
    }
    return objectKeys(target).reduce((r, k) => {
        r[k] = Array.isArray(target[k])
            ? target[k]
            : removeEdges(get<any, string>(target, k), keepPageInfo, keepCursor);
        return r;
    }, {} as any);
};

// TODO: this will fail if we have properties called 'edges' in our model
export const removeEdgesDeep = (target: any, keepPageInfo = false, keepCursor = false): any => {
    if (!target || typeof target !== 'object') {
        return target;
    }
    if (target.edges && Array.isArray(target.edges)) {
        const data = target.edges.map((e: any) => {
            const nextTarget = keepCursor ? { ...e.node, __cursor: e.cursor } : e.node;
            return removeEdgesDeep(nextTarget, keepPageInfo, keepCursor);
        });
        return keepPageInfo ? { data, pageInfo: target.pageInfo || {}, totalCount: target.totalCount } : data;
    }
    return objectKeys(target).reduce((r, k) => {
        r[k] = Array.isArray(target[k]) ? target[k] : removeEdgesDeep(target[k], keepPageInfo, keepCursor);
        return r;
    }, {} as any);
};
/**
 * Gets all groups along with their aggregations.
 *
 * Returns all groups along with their group key.
 *
 * @export
 * @param {*} target
 * @param {string} [k]
 * @returns {*}
 */
export function getGroups(target: any, groupKey?: string): any {
    return getGroupsInternal(groupKey)(target);
}

const getGroupsInternal =
    (groupKey?: string) =>
    (target: any, k?: string): any => {
        if (target && typeof target === 'object') {
            if (target.queryAggregate) {
                return {
                    groups: target.queryAggregate.edges.map(queryAggregateMapper(groupKey)),
                    key: k,
                };
            }
            if (!Array.isArray(target)) {
                // eslint-disable-next-line no-restricted-syntax
                for (const key of objectKeys(target)) {
                    const result = getGroupsInternal(groupKey)(target[key], key);
                    if (result) {
                        return result;
                    }
                }
            } else {
                // eslint-disable-next-line no-restricted-syntax
                for (const element of target) {
                    const result = getGroupsInternal(groupKey)(element);
                    if (result) {
                        return result;
                    }
                }
            }
        }

        return undefined;
    };

const queryAggregateMapper =
    (key?: string) =>
    (e: any): any => {
        const { group, values } = e.node;
        const flatGroup = flat(group);
        const [groupByIdKey, firstKey] = objectKeys(flatGroup).sort();
        const groupKey = key ?? firstKey ?? groupByIdKey;
        const groupValue = flatGroup[groupKey];
        const aggregations = Object.entries(flat(values)).reduce((acc, [k, value]) => {
            const newKey = k.split('.').slice(0, -1).join('.');
            set(acc, newKey, value);
            return acc;
        }, {});
        const node = {
            ...aggregations,
            ...group,
            // always assign an _id
            _id: `__group-${groupValue}`,
            __isGroup: true,
            __groupKey: groupKey ?? groupByIdKey,
            __groupCount: values?._id?.distinctCount,
        };
        return {
            node,
            cursor: e.cursor,
        };
    };

export function getAllGroups(input: any): any {
    const result: any = {};

    function getAllGroupsInternal(target: any, k?: string): void {
        if (target && typeof target === 'object') {
            if (target.queryAggregate && k) {
                result[k] = {
                    groups: target.queryAggregate.edges.map(queryAggregateMapper()),
                    key: k,
                };
            }
            if (!Array.isArray(target)) {
                // eslint-disable-next-line no-restricted-syntax
                for (const key of objectKeys(target)) {
                    getAllGroupsInternal(target[key], key);
                }
            } else {
                // eslint-disable-next-line no-restricted-syntax
                for (const element of target) {
                    getAllGroupsInternal(element);
                }
            }
        }

        return undefined;
    }

    getAllGroupsInternal(input);
    return result;
}

export const flattenEdges = (edges: Object[]): Object[] => edges.map((e: any) => (e.node ? e.node : e));

const buildNestedQuery = (queryObject: any, nameParts: string[], alias?: string | null): QueryWrapper<any> => {
    const [firstPart, ...missingParts] = nameParts;
    const nestedQueryContent = missingParts.length > 0 ? buildNestedQuery(queryObject, missingParts) : queryObject;
    const nestedQuery = { [alias || firstPart]: nestedQueryContent };
    if (alias) {
        nestedQueryContent.__aliasFor = firstPart;
    }
    return nestedQuery;
};

export const splitNodeName = (nodeName: NodePropertyType): string[] =>
    String(nodeName)
        .replace(/^@\w*\//, '')
        .split('/')
        .map(camelCase);

export const wrapQuery = (nodeName: NodePropertyType, queryObject: any, alias?: string | null): QueryWrapper<any> => {
    const nameParts = splitNodeName(nodeName);
    return buildNestedQuery(queryObject, nameParts, alias);
};

export const unwrapQueryResponse = (nodeName: NodePropertyType, queryResponse: any, alias?: string | null): any => {
    const nameParts = splitNodeName(nodeName);
    const queryKeys = alias ? [alias, ...nameParts.slice(1)] : nameParts;
    return queryKeys.reduce((reduced, key) => reduced[key], queryResponse);
};

export interface GetNodeForLookupOperationsArgs {
    parentElementId?: string;
    level?: number;
    screenDefinition: ScreenBaseDefinition;
}

export const getNodeForLookupOperation = ({
    parentElementId,
    screenDefinition,
    level,
}: GetNodeForLookupOperationsArgs): NodePropertyType => {
    const screenId = screenDefinition.metadata.screenId;

    if (parentElementId) {
        const value = screenDefinition.values[parentElementId];
        const parentElement = screenDefinition.metadata.controlObjects[parentElementId];

        if (
            parentElement instanceof VitalPodControlObject ||
            parentElement instanceof TableControlObject ||
            parentElement instanceof PodCollectionControlObject
        ) {
            return parentElement.node;
        }

        if (parentElement instanceof NestedGridControlObject && value instanceof CollectionValue) {
            const targetLevel = level || 0;
            return value.nodes[targetLevel];
        }
    }

    return (screenDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<any>)
        .node as NodePropertyType;
};

export const wipeCache = async (): Promise<void> => {
    window.localStorage.clear();
    await purgeMetaDataCache();
    await window.caches.delete(PLUGIN_CACHE);
};
