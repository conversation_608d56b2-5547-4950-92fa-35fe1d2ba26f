import { objectKeys, type Dict, type LocalizeLocale } from '@sage/xtrem-shared';
import lodash, { isEqual, isNil } from 'lodash';
import { mergeColumnPropertyDefinitions, mergeMobileCards } from '../component/abstract-decorator-utils';
import { navigationPanelId } from '../component/container/navigation-panel/navigation-panel-types';
import type {
    MainListAction,
    MainListActionOrMenuSeparator,
    OptionsMenuItem,
    OptionsMenuItemType,
    OptionsMenuType,
    RuntimePageNavigationPanel,
    TableOptionsMenuItem,
} from '../component/container/page/page-types';
import type { PageActionControlObject } from '../component/control-objects';
import type {
    InternalTableProperties,
    TableDecoratorProperties,
    TableUserSettings,
    TableViewSortedColumn,
} from '../component/field/table/table-component-types';
import type { Nested } from '../component/field/traits';
import { withoutNestedTechnical, type GridNestedFieldTypes, type NestedField } from '../component/nested-fields';
import type { OrderByType, PartialCollectionValue } from '../component/types';
import { FieldKey } from '../component/types';
import type { CardExtensionDefinition } from '../component/ui/card/card-component';
import type {
    CollectionItemAction,
    CollectionItemActionOrMenuSeparator,
} from '../component/ui/table-shared/table-dropdown-actions/table-dropdown-action-types';
import type { ApplicationContext } from '../integration';
import type { AppThunkDispatch } from '../redux';
import { getStore } from '../redux';
import { refreshNavigationPanel } from '../redux/actions/navigation-panel-actions';
import type { NodePropertyType } from '../types';
import { COLUMN_ID_VALIDATIONS } from '../utils/ag-grid/ag-grid-column-config';
import { isColumnAvailable } from '../utils/ag-grid/ag-grid-service';
import { DEFAULT_VIEW_ID, QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER } from '../utils/constants';
import { applyDefaultValuesOnNestedField } from '../utils/data-type-utils';
import { convertDeepBindToPathNotNull, getImagePlaceholderValue } from '../utils/nested-field-utils';
import { resolveByValue } from '../utils/resolve-value-utils';
import { getNavigationPanelState, getPagePropertiesFromPageDefinition } from '../utils/state-utils';
import {
    getNestedFieldArrayFromCardDefinition,
    getNestedFieldArrayFromCardExtensionDefinition,
} from '../utils/transformers';
import { CollectionValue } from './collection-data-service';
import { CollectionFieldTypes } from './collection-data-types';
import { addCustomColumnToNestedFields } from './customization-service';
import { ReadOnlyGraphQLApi } from './graphql-api';
import { getNavigationPanelOrderByDefinition } from './graphql-query-builder';
import { localize } from './i18n-service';
import type { DataTypeDetails, FormattedNodeDetails } from './metadata-types';
import type { AccessBindings, PageDefinition } from './page-definition';
import { getRouter } from './router';
import type { ScreenBase } from './screen-base';
import { getScreenElement } from './screen-base-definition';
import * as storageService from './storage-service';

const sealedStorageService = Object.seal(storageService);

export function createMySelectedDataOptionItem(customMainListFilter: string): OptionsMenuItem {
    return {
        title: localize('@sage/xtrem-ui/navigation-panel-my-view', 'My selected data'),
        graphQLFilter: JSON.parse(String(customMainListFilter)),
    };
}

const mapMainListActionToCollectionItemAction =
    (screenId: string) =>
    (a: MainListAction<any>): CollectionItemAction<any> => {
        const originalOnClick = a.onClick;
        const onClick = async function OverriddenOnClick(this: ScreenBase, ...args: any[]): Promise<void> {
            await originalOnClick.apply(this, args);

            const dispatch: AppThunkDispatch = getStore().dispatch;
            if (a.refreshesMainList === 'record') {
                await getNavigationPanelState(screenId)?.value?.refreshRecord({ recordId: args[0] });
            }

            if (a.refreshesMainList === 'list') {
                await dispatch(refreshNavigationPanel(screenId));
            }
        };

        return { ...a, onClick };
    };

const mapMainListActionOrMenuSeparatorToCollectionItemAction =
    (screenId: string) =>
    (a: MainListActionOrMenuSeparator<any>): CollectionItemActionOrMenuSeparator<any> => {
        return a.isMenuSeparator ? a : mapMainListActionToCollectionItemAction(screenId)(a);
    };

export const resolveNavigationPanelOptionFilter =
    (pageDefinition: PageDefinition) =>
    (optionItem?: OptionsMenuItem): OptionsMenuItem | undefined => {
        if (!optionItem) {
            return undefined;
        }
        if (lodash.isFunction(optionItem.graphQLFilter)) {
            const graphQLFilter = optionItem.graphQLFilter.apply({}, [storageService, pageDefinition.queryParameters]);
            return { ...optionItem, graphQLFilter };
        }

        return optionItem;
    };

export const resolveOptionsMenu = async (
    pageDefinition: PageDefinition,
    optionsMenu: OptionsMenuType<any> | undefined,
    applicationContext?: ApplicationContext,
    serviceOptions: Dict<boolean> = {},
): Promise<OptionsMenuItemType[]> => {
    const options: Array<OptionsMenuItemType> = [];

    if (pageDefinition.queryParameters?.[QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]) {
        // If the "_filter" query param is set, we parse it and use it as a filter option
        options.push(
            createMySelectedDataOptionItem(String(pageDefinition.queryParameters[QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER])),
        );
    }

    if (optionsMenu && lodash.isFunction(optionsMenu)) {
        const resolvedMenu: OptionsMenuType<any> = await optionsMenu(
            new ReadOnlyGraphQLApi(),
            sealedStorageService,
            pageDefinition.queryParameters,
            applicationContext?.login || '',
            applicationContext?.userCode || '',
            serviceOptions,
        );

        options.push(
            ...(resolvedMenu
                .map(resolveNavigationPanelOptionFilter(pageDefinition))
                .filter(v => !!v) as OptionsMenuItemType[]),
        );
    } else if (optionsMenu) {
        options.push(
            ...(optionsMenu
                .map(resolveNavigationPanelOptionFilter(pageDefinition))
                .filter(v => !!v) as OptionsMenuItemType[]),
        );
    }

    // In case there is no option menu defined for the page but we added a filter based on the query parameters, the we need to add an "All" option too
    if (pageDefinition.queryParameters?.[QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER] && options.length === 1) {
        options.push({
            title: localize('@sage/xtrem-ui/navigation-panel-no-filter', 'All'),
            graphQLFilter: {},
        });
    }

    return options;
};

export const getMergedOptionMenu = async (
    pageDefinition: PageDefinition,
    navigationPanel: RuntimePageNavigationPanel<any>,
    applicationContext?: ApplicationContext,
    serviceOptions: Dict<boolean> = {},
): Promise<OptionsMenuItemType[]> => {
    let result: OptionsMenuItemType[] = await resolveOptionsMenu(
        pageDefinition,
        navigationPanel.optionsMenu,
        applicationContext,
        serviceOptions,
    );

    if (navigationPanel.optionMenus) {
        // eslint-disable-next-line no-restricted-syntax
        for (const optionsMenu of navigationPanel.optionMenus) {
            // eslint-disable-next-line no-await-in-loop
            const extensionResult = await resolveOptionsMenu(
                pageDefinition,
                optionsMenu,
                applicationContext,
                serviceOptions,
            );
            result = [...result, ...extensionResult];
        }
    }

    return lodash.uniqWith(result, lodash.isEqual);
};

export const getInitialOptionMenuItem = (
    pageDefinition: PageDefinition,
    navigationTableProperties: InternalTableProperties,
): OptionsMenuItem | undefined => {
    const screenId = pageDefinition.metadata.screenId;
    const optionMenu = resolveByValue<OptionsMenuItem[]>({
        propertyValue: navigationTableProperties.optionsMenu,
        screenId,
        rowValue: null,
        fieldValue: null,
        skipHexFormat: true,
    });

    const savedTableViewMenuItem =
        pageDefinition.userSettings?.[navigationPanelId]?.$current?.content?.[0]?.optionsMenuItem;

    // We need to check if the previous saved table view option is still a valid option in the current optionMenu list
    // If not, we set the first optionMenu item as the default one
    const optionItem =
        savedTableViewMenuItem && optionMenu.find(item => isEqual(item, savedTableViewMenuItem))
            ? savedTableViewMenuItem
            : optionMenu[0];

    return resolveNavigationPanelOptionFilter(pageDefinition)(optionItem);
};

export const onNavigationPanelRowClick =
    (pageDefinition: PageDefinition, navigationPanel: RuntimePageNavigationPanel<any>, path?: string) =>
    (recordId: string, rowItem: any, isModifierKeyPushed = false): void => {
        if (isModifierKeyPushed && path) {
            const url = `${path.split('/').slice(0, 3).join('/')}/${btoa(JSON.stringify({ _id: recordId }))}`;
            window.open(url, '_blank');
            return;
        }
        if (navigationPanel.onSelect) {
            // If the onSelect returns true, we consider the event consumed and do not execute the actual navigation event.
            const navigationPrevented = navigationPanel.onSelect.apply(getScreenElement(pageDefinition), [rowItem]);
            if (navigationPrevented) {
                return;
            }
        }
        getRouter(pageDefinition.metadata.screenId).selectRecord(recordId);
    };

export const createNavigationTableProperties = async (
    screenId: string,
    pageNode: NodePropertyType,
    pageDefinition: PageDefinition,
    nodeTypes: Dict<FormattedNodeDetails>,
    dataTypes: Dict<DataTypeDetails>,
    path?: string,
    applicationContext?: ApplicationContext,
    serviceOptions: Dict<boolean> = {},
): Promise<InternalTableProperties> => {
    const pageProperties = getPagePropertiesFromPageDefinition(pageDefinition);
    const navigationPanel = pageProperties.navigationPanel as RuntimePageNavigationPanel<any>;
    if (!navigationPanel) {
        throw new Error(`No navigation panel definition found on ${screenId}.`);
    }
    const optionsMenu = await getMergedOptionMenu(pageDefinition, navigationPanel, applicationContext, serviceOptions);
    const listItem = { ...navigationPanel.listItem };
    if (listItem.image) {
        listItem.image.properties = {
            placeholderValue: (recordValue: Dict<any>): string | undefined =>
                getImagePlaceholderValue(recordValue, listItem.title),
            ...listItem.image.properties,
        };
    }
    const orderBy = getNavigationPanelOrderByDefinition(listItem, navigationPanel.orderBy);
    const baseColumns = getNestedFieldArrayFromCardDefinition(listItem) as NestedField<
        ScreenBase,
        GridNestedFieldTypes
    >[];

    const node = String(pageProperties.node);

    const columns: NestedField<ScreenBase, GridNestedFieldTypes>[] = (navigationPanel.extensionListItem || []).reduce(
        (prevValue: NestedField<ScreenBase, GridNestedFieldTypes>[], currentValue: CardExtensionDefinition<any>) =>
            mergeColumnPropertyDefinitions<GridNestedFieldTypes>(
                undefined,
                prevValue,
                getNestedFieldArrayFromCardExtensionDefinition<GridNestedFieldTypes>(currentValue),
            ).map(column => {
                applyDefaultValuesOnNestedField(nodeTypes, dataTypes, column, node);
                return column;
            }),
        baseColumns,
    );

    columns.forEach(column => {
        applyDefaultValuesOnNestedField(nodeTypes, dataTypes, column, node);
    });

    addCustomColumnToNestedFields(
        node,
        nodeTypes,
        columns,
        navigationPanelId,
        FieldKey.Table,
        false,
        pageDefinition.metadata.customizations,
    );

    const mobileCard = (navigationPanel.extensionListItem || []).reduce(
        (acc, extension) => mergeMobileCards(acc, extension),
        navigationPanel.listItem,
    );

    /**
     * INFO: Casting the result to an PageActionControlObject[] should be fine, since we make sure
     *       the create actions are arrays in the page decorator.
     */
    const createActions = resolveByValue<PageActionControlObject | PageActionControlObject[]>({
        propertyValue: pageProperties.createAction,
        screenId,
        rowValue: null,
        fieldValue: null,
        skipHexFormat: true,
    });

    return {
        canExport: true,
        canSelect: (navigationPanel.bulkActions ?? []).length > 0,
        canUserHideColumns: true,
        columns,
        emptyStateClickableText: navigationPanel.emptyStateClickableText,
        emptyStateText: navigationPanel.emptyStateText,
        hasSearchBoxMobile: true,
        headerBusinessActions: createActions || undefined,
        isReadOnly: true,
        isTitleHidden: false,
        mobileCard,
        node: pageNode,
        onEmptyStateLinkClick: navigationPanel.onEmptyStateLinkClick,
        onRowClick: onNavigationPanelRowClick(pageDefinition, navigationPanel, path),
        optionsMenu: optionsMenu as TableOptionsMenuItem[],
        optionsMenuType: navigationPanel.menuType,
        dropdownActions: navigationPanel.dropdownActions?.map(
            mapMainListActionOrMenuSeparatorToCollectionItemAction(screenId),
        ),
        inlineActions: navigationPanel.inlineActions?.map(mapMainListActionToCollectionItemAction(screenId)),
        orderBy,
        pageSize: 20,
        title: pageProperties.objectTypePlural,
        canResizeColumns: true,
        cardColor: navigationPanel.cardColor,
        startDateField: navigationPanel.startDateField,
        endDateField: navigationPanel.endDateField,
        minDate: navigationPanel.minDate,
        maxDate: navigationPanel.maxDate,
        isEventMovable: navigationPanel.isEventMovable,
    };
};

export const createNavigationPanelValue = (
    screenId: string,
    navigationPanelTableProperties: TableDecoratorProperties,
    initialValues: PartialCollectionValue<any>[],
    nodeTypes = getStore().getState().nodeTypes,
    locale: LocalizeLocale = 'en-US',
    activeOptionsMenuItem?: OptionsMenuItem,
    orderBy?: OrderByType,
): CollectionValue =>
    new CollectionValue({
        activeOptionsMenuItem,
        columnDefinitions: [navigationPanelTableProperties.columns || []],
        elementId: navigationPanelId,
        fieldType: CollectionFieldTypes.NAVIGATION_PANEL,
        filter: [navigationPanelTableProperties.filter],
        hasNextPage: true,
        initialValues,
        isTransient: false,
        locale,
        nodes: [navigationPanelTableProperties.node as string],
        nodeTypes,
        orderBy: [orderBy || navigationPanelTableProperties.orderBy],
        screenId,
    });

export function createNavigationPanelDefaultView({
    defaultTableProperties,
    screenId,
    nodeTypes,
    dataTypes,
    accessBindings,
    customMainListFilter,
}: {
    accessBindings: AccessBindings;
    defaultTableProperties: TableDecoratorProperties;
    screenId: string;
    nodeTypes: Dict<FormattedNodeDetails>;
    dataTypes: Dict<DataTypeDetails>;
    customMainListFilter?: any;
}): TableUserSettings {
    const orderBy = defaultTableProperties.orderBy || {};
    const sortOrder = objectKeys(orderBy).reduce((acc: TableViewSortedColumn[], key, sortIndex) => {
        const v = orderBy[key];
        if (v === 1 || v === -1) {
            acc.push({ colId: key, sort: v === 1 ? 'asc' : 'desc', sortIndex });
        }
        return acc;
    }, [] as TableViewSortedColumn[]);

    const availableColumns = withoutNestedTechnical(defaultTableProperties.columns || [])?.filter(columnDefinition => {
        return isColumnAvailable({
            contextNode: String(defaultTableProperties.node),
            screenId,
            columnDefinition,
            nodeTypes,
            dataTypes,
            accessBindings,
        });
    });

    const columnHidden = availableColumns?.reduce(
        (acc: Dict<boolean>, c) => {
            const bind = convertDeepBindToPathNotNull(c.properties.bind);
            let columnId = bind;
            let i = 1;
            while (!isNil(acc[columnId])) {
                columnId = `${bind}_${i}`;
                i += 1;
            }
            acc[columnId] = !!(c.properties as Nested).isHiddenOnMainField;
            return acc;
        },
        {
            [COLUMN_ID_VALIDATIONS]: true,
        } as Dict<boolean>,
    );

    return {
        _id: DEFAULT_VIEW_ID,
        title: localize('@sage/xtrem-ui/table-views-default', 'Default view'),
        content: [
            {
                filter: {},
                sortOrder,
                columnHidden,
                optionsMenuItem: customMainListFilter
                    ? createMySelectedDataOptionItem(customMainListFilter)
                    : undefined,
            },
        ],
    };
}
