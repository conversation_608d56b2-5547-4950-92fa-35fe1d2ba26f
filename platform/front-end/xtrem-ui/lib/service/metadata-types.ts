import type { AccessStatus, Dict, Graph<PERSON>Types, MetaCustomField, NodeTypeKind } from '@sage/xtrem-shared';

export interface MetadataStringLiteralTypeEntry {
    key: string;
    content: string;
}

export interface MetadataAccessPropertyBinding {
    name: string;
    status: AccessStatus;
}
export interface MetadataAccessRightsResult {
    node: string;
    bindings: MetadataAccessPropertyBinding[];
}

export interface MetadataCustomNode {
    name: string;
    properties: MetaCustomField[];
}

export interface CustomizableNode {
    node: string;
    fullName: string;
}
export interface ExportTemplateDetails {
    id: string;
    name: string;
}

export interface ExportTemplatesByNode {
    name: string;
    exportTemplates: ExportTemplateDetails[];
}

export interface UserSettings {
    _id: string;
    content: string;
    elementId: string;
    title: string;
    description: string;
}

export interface Fragment {
    name: string;
    content: string;
}

export interface MetadataType {
    access?: MetadataAccessRightsResult[];
    activeUserSettings?: UserSettings[];
    content?: string;
    customFields?: MetadataCustomNode[];
    customizableNodes?: CustomizableNode[];
    customizableNodesWizard?: CustomizableNode[];
    duplicateBindings?: string[] | null;
    exportTemplatesByNode?: ExportTemplatesByNode[];
    extensions?: { content: string; packageName: string }[];
    fragments?: Fragment[];
    hasRecordPrintingTemplates?: boolean;
    key?: string;
    nodeDetails?: RawNodeDetails[];
    plugins?: string[];
    strings?: MetadataStringLiteralTypeEntry[];
    title?: string;
}

export interface MetadataResponse {
    pages?: MetadataType[];
    stickers?: MetadataType[];
    strings?: MetadataStringLiteralTypeEntry[];
}

export type ModuleEntryProperties = keyof MetadataType;
export type ArtifactType = 'pages' | 'stickers' | 'widgets' | 'strings';

export type DataTypePropertyType =
    | 'string'
    | 'uuid'
    | 'enum'
    | 'date'
    | 'decimal'
    | 'double'
    | 'short'
    | 'integer'
    | 'boolean'
    | 'json'
    | 'textStream'
    | 'binaryStream';

export interface DataTypeProperty {
    bind: string;
    title: string;
    type: DataTypePropertyType;
}

export interface DataTypeDetails {
    name: string;
    type: string;
    title: string;
    node: string;
    tunnelPage?: string;
    tunnelPageId?: DataTypeProperty;
    value: DataTypeProperty;
    helperText: DataTypeProperty;
    columns: DataTypeProperty[];
    imageField?: DataTypeProperty;
    precision?: number;
    scale?: number;
    maxLength?: number;
    values?: {
        value: string;
        title: string;
    }[];
}

export interface NodeDetailsProperty {
    name?: string;
    title?: string;
    canSort?: boolean;
    canFilter?: boolean;
    type: string;
    isCustom?: boolean;
    isMutable?: boolean;
    targetNode?: string;
    dataType?: string;
    isStored?: boolean;
    isOnInputType?: boolean;
    isOnOutputType?: boolean;
    enumType?: string;
    parentNode?: string;
}

export interface FormattedNodeDetailsProperty extends NodeDetailsProperty {
    type: GraphQLTypes | string;
    kind: NodeTypeKind;
    enumValues?: string[];
    isCollection?: boolean;
}

export interface FormattedMutationDetailsProperty {
    name: string;
    title: string;
    parameters: NodeDetailsMutationParameter[];
}

export interface RawNodeDetailsProperty extends NodeDetailsProperty {
    targetNodeDetails?: RawNodeDetails;
    dataTypeDetails?: DataTypeDetails;
}

export interface RawNodeDetailsMutation {
    name: string;
    title: string;
    parameters: NodeDetailsMutationParameter[];
}

export interface NodeDetailsMutationParameter {
    name: string;
    title: string;
}

export interface NodeDetails {
    name: string;
    title: string;
    defaultDataType?: string;
    hasAttachments?: boolean;
    hasNotes?: boolean;
    packageName: string;
}

export interface RawNodeDetails extends NodeDetails {
    properties: RawNodeDetailsProperty[];
    mutations: RawNodeDetailsMutation[];
    defaultDataTypeDetails?: DataTypeDetails;
}

export interface FormattedNodeDetails extends NodeDetails {
    properties: Dict<FormattedNodeDetailsProperty>;
    mutations: Dict<FormattedMutationDetailsProperty>;
}
