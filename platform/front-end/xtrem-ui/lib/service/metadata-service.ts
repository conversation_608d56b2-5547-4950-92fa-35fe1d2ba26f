import { MD5 } from 'crypto-js';
import * as idb from 'idb';
import { cloneDeep, find, isEmpty, memoize, snakeCase, sortBy } from 'lodash';

import type { Dict, LocalizeLocale } from '@sage/xtrem-shared';
import { CUSTOM_DATA_PROPERTY, GraphQLTypes, objectKeys } from '@sage/xtrem-shared';

import type { PageProperties } from '../component/control-objects';
import * as xtremRedux from '../redux';
import { addSchemaInfo } from '../redux/actions';
import type { PrintingSettings, UiComponentUserSettings } from '../redux/state';
import { GraphQLKind } from '../types';
import { ARTIFACT_DATABASE_NAME } from '../utils/constants';
import { getArtifactDescription } from '../utils/transformers';
import {
    DATA_TYPE_FRAGMENT,
    buildEnumQueries,
    buildTypeQuery,
    getNodeCustomDetailsFragment,
    getNodeDetailsFragment,
    getPlatformStringLiteralsSchemaFragment,
    getUsedEnums,
    getUsedNodes,
} from './graphql-query-builder';
import type { GraphqlCacheSettings } from './graphql-utils';
import { executeGraphqlQuery } from './graphql-utils';
import type {
    ArtifactType,
    DataTypeDetails,
    FormattedMutationDetailsProperty,
    FormattedNodeDetails,
    FormattedNodeDetailsProperty,
    MetadataResponse,
    ModuleEntryProperties,
    RawNodeDetails,
    RawNodeDetailsMutation,
    RawNodeDetailsProperty,
} from './metadata-types';
import type { ScreenBaseDefinition } from './screen-base-definition';
import { processStringLiteralsFromResponse } from './screen-loader-service';

export interface ProcessedNodeDetails {
    dataTypes: Dict<DataTypeDetails>;
    nodeTypes: Dict<FormattedNodeDetails>;
    enumTypes: Dict<{ values: string[]; name: string; translations: string[] }>;
}

function getGraphqlKindFromNodeProperty(value: RawNodeDetailsProperty): GraphQLKind {
    if (value.type === 'collection') {
        return GraphQLKind.List;
    }
    if (value.targetNode) {
        return GraphQLKind.Object;
    }

    return GraphQLKind.Scalar;
}

function getGraphqlTypeFromNodeProperty(value: RawNodeDetailsProperty): GraphQLTypes | string {
    if (value.name === '_id') {
        return GraphQLTypes.IntOrString;
    }
    if (value.enumType || (value.type === 'enum' && value.isCustom)) {
        return GraphQLTypes.Enum;
    }

    if (value.targetNode) {
        return getArtifactDescription(value.targetNode).name;
    }

    switch (value.type) {
        case 'boolean':
            return GraphQLTypes.Boolean;
        case 'string':
        case 'uuid':
            return GraphQLTypes.String;
        case 'float':
            return GraphQLTypes.Float;
        case 'integer':
        case 'short':
            return GraphQLTypes.Int;
        case 'decimal':
        case 'double':
            return GraphQLTypes.Decimal;
        case 'json':
            return GraphQLTypes.Json;
        case 'date':
            return GraphQLTypes.Date;
        case 'textStream':
            return GraphQLTypes._OutputTextStream;
        case 'binaryStream':
            return GraphQLTypes._InputStream;
        default:
            return value.type;
    }
}

function getIsCollection(value: RawNodeDetailsProperty): boolean {
    if (
        value.type === 'collection' ||
        value.type === 'enumArray' ||
        value.type === 'integerArray' ||
        value.type === 'stringArray' ||
        value.type === 'floatArray' ||
        value.type === 'decimalArray' ||
        value.type === 'referenceArray'
    ) {
        return true;
    }
    return false;
}

export const fetchEnumTranslations = async (enums: string[], locale: string): Promise<Dict<string>> => {
    const path = enums.map(enumName => `enums__${snakeCase(enumName)}`).join(',');

    const metadataResponse = await queryMetadata({
        metadataType: 'strings',
        metadataProperties: ['key', 'content'],
        path,
        locale,
    });

    return processStringLiteralsFromResponse('pages', metadataResponse);
};

function convertFormattedPropertyToRaw(prop: FormattedNodeDetailsProperty): RawNodeDetailsProperty {
    const { kind, enumValues, isCollection, ...base } = prop;

    return {
        ...base,
        type: prop.isCollection && prop.kind === GraphQLKind.List ? 'collection' : prop.type,
        targetNodeDetails: undefined,
        dataTypeDetails: undefined,
    };
}

function convertFormattedToRaw(formatted: FormattedNodeDetails): RawNodeDetails {
    return {
        name: formatted.name,
        title: formatted.title,
        packageName: formatted.packageName,
        defaultDataType: formatted.defaultDataType,
        hasAttachments: formatted.hasAttachments,
        hasNotes: formatted.hasNotes,
        properties: Object.values(formatted.properties).map(convertFormattedPropertyToRaw),
        mutations: Object.values(formatted.mutations),
    };
}

const normalizeCustomPropertyName = (prop: any): any => {
    const prefixedName = prop.name.startsWith(CUSTOM_DATA_PROPERTY)
        ? prop.name
        : `${CUSTOM_DATA_PROPERTY}.${prop.name}`;
    return {
        ...prop,
        name: prefixedName,
    };
};

function mergeNodeCustomDetails(
    rawNodeDetails: RawNodeDetails[],
    rawNodeCustomDetails: RawNodeDetails[] = [],
): RawNodeDetails[] {
    const nodeTypes = xtremRedux.getStore().getState().nodeTypes;

    const clonedRawNodeDetails = cloneDeep(rawNodeDetails ?? []);

    (rawNodeCustomDetails ?? []).forEach(customNode => {
        const normalizedCustomProps = customNode.properties.map(normalizeCustomPropertyName);
        const existingNode = find(clonedRawNodeDetails, { name: customNode.name });

        if (existingNode) {
            const standardProperties = existingNode.properties.filter(property => !property.isCustom);
            existingNode.properties = [...standardProperties, ...normalizedCustomProps];
            return;
        }

        if (nodeTypes[customNode.name]) {
            const formattedNode = nodeTypes[customNode.name] as FormattedNodeDetails;
            const baseNode = convertFormattedToRaw(formattedNode);

            clonedRawNodeDetails.push({
                ...baseNode,
                properties: [...baseNode.properties, ...normalizedCustomProps],
            });
        }
    });

    return clonedRawNodeDetails;
}

export function processNodeDetailsResponse(rawNodeDetails?: RawNodeDetails[]): ProcessedNodeDetails {
    const dataTypes: Dict<DataTypeDetails> = {};
    const nodeTypes: Dict<FormattedNodeDetails> = {};
    const enumTypes: Dict<{ values: string[]; name: string; translations: string[] }> = {};

    if (!rawNodeDetails) {
        return {
            nodeTypes,
            dataTypes,
            enumTypes,
        };
    }

    const processDataTypeDetails = (dataTypeDetails?: DataTypeDetails, enumType?: string): void => {
        if (!dataTypeDetails) {
            return;
        }

        if (!dataTypes[dataTypeDetails.name]) {
            dataTypes[dataTypeDetails.name] = dataTypeDetails;

            if (dataTypeDetails.type === 'enum' && enumType && dataTypeDetails.values) {
                const { name: enumName } = getArtifactDescription(enumType);
                enumTypes[enumName] = {
                    name: enumType,
                    values: dataTypeDetails.values.map(v => v.value),
                    translations: dataTypeDetails.values.map(v => v.title),
                };
            }
        }
    };

    const processDataTypeProperties = (nodeDetails?: RawNodeDetails): void => {
        if (!nodeDetails?.name || nodeTypes[nodeDetails.name]) {
            return;
        }

        processDataTypeDetails(nodeDetails.defaultDataTypeDetails);

        nodeTypes[nodeDetails.name] = {
            name: nodeDetails.name,
            title: nodeDetails.title,
            packageName: nodeDetails.packageName,
            defaultDataType: nodeDetails.defaultDataType,
            hasAttachments: nodeDetails.hasAttachments,
            hasNotes: nodeDetails.hasNotes,
            properties: nodeDetails.properties.reduce(
                (currentValue: Dict<FormattedNodeDetailsProperty>, value: RawNodeDetailsProperty) => {
                    if (!value?.name) {
                        return currentValue;
                    }
                    processDataTypeProperties(value.targetNodeDetails);
                    processDataTypeDetails(value.dataTypeDetails, value.enumType);
                    const strippedValue = { ...value };
                    delete strippedValue.targetNodeDetails;
                    delete strippedValue.dataTypeDetails;
                    return {
                        ...currentValue,
                        [value.name]: {
                            ...strippedValue,
                            isCollection: getIsCollection(value),
                            type: getGraphqlTypeFromNodeProperty(value),
                            kind: getGraphqlKindFromNodeProperty(value),
                        },
                    };
                },
                {},
            ),
            mutations: (nodeDetails?.mutations || []).reduce(
                (currentValue: Dict<FormattedMutationDetailsProperty>, value: RawNodeDetailsMutation) => {
                    if (!value?.name) {
                        return currentValue;
                    }
                    currentValue[value.name] = value;
                    return currentValue;
                },
                {},
            ),
        };
    };

    if (rawNodeDetails instanceof Array) {
        rawNodeDetails.forEach(processDataTypeProperties);
    }

    return {
        nodeTypes,
        dataTypes,
        enumTypes,
    };
}

export function fetchNodeDetailsResponse(
    nodes: string[],
    depth = 3,
    cacheSettings?: GraphqlCacheSettings,
): Promise<any> {
    const query = buildTypeQuery(nodes, [], depth);

    return executeGraphqlQuery({
        query,
        endpoint: '/metadata',
        cacheSettings,
    });
}

export async function fetchNodeDetails(
    nodes: string[],
    depth = 3,
    cacheSettings?: GraphqlCacheSettings,
): Promise<{
    dataTypes: Dict<DataTypeDetails>;
    nodeTypes: Dict<FormattedNodeDetails>;
    enumTypes: Dict<{ values: string[]; name: string; translations: string[] }>;
}> {
    if (isEmpty(nodes)) {
        return {
            nodeTypes: {},
            dataTypes: {},
            enumTypes: {},
        };
    }
    const response = await fetchNodeDetailsResponse(nodes, depth, cacheSettings);

    const mergedNodeDetails = mergeNodeCustomDetails(
        response?.data?.getNodeDetailsList || [],
        response?.data?.getNodeCustomDetailsList || [],
    );
    return processNodeDetailsResponse(mergedNodeDetails);
}

// TODO: remove this
const skipCache = true;

export async function fetchNodePackageNames(
    nodes: string[],
    depth = 3,
    cacheSettings?: GraphqlCacheSettings,
): Promise<string[]> {
    const store = xtremRedux.getStore();
    if (isEmpty(nodes)) return [];

    if (skipCache) {
        const response = await fetchNodeDetailsResponse(nodes, depth, cacheSettings);
        return response.data.getNodeDetailsList.map((node: any) => node.packageName);
    }

    const state = store.getState();
    const knownNodes = objectKeys(state.nodeTypes);
    const nodesToRequest = nodes.filter(node => !knownNodes.includes(node));

    if (nodesToRequest.length === 0) {
        const response = await fetchNodeDetailsResponse(nodesToRequest, depth, cacheSettings);
        const { nodeTypes, dataTypes, enumTypes } = processNodeDetailsResponse(response.data.getNodeDetailsList);
        store.dispatch(addSchemaInfo(nodeTypes, dataTypes, enumTypes, state.applicationContext?.locale || 'en-US'));
    }

    return Object.values(store.getState().nodeTypes)
        .filter(node => nodes.includes(node.name))
        .map(node => node.packageName);
}

export async function fetchInitialNodeDetails(
    screenDefinition: ScreenBaseDefinition,
    pagePackageVersion: string,
    locale: LocalizeLocale,
    knownNodeTypes: string[] = [],
): Promise<{
    dataTypes: Dict<DataTypeDetails>;
    nodeTypes: Dict<FormattedNodeDetails>;
    enumTypes: Dict<{ values: string[]; name: string; translations: string[] }>;
}> {
    const { metadata } = screenDefinition;
    const { screenId } = metadata;
    const pageProperties = metadata.uiComponentProperties[screenId] as PageProperties;

    const nodes = getUsedNodes(metadata, knownNodeTypes, pageProperties.node as string);
    const cacheKey = `data_type_query_${MD5(nodes.join(',')).toString()}`;

    return fetchNodeDetails(nodes, 3, {
        locale,
        key: cacheKey,
        version: pagePackageVersion,
        shouldFetchPlatformLiterals: false,
    });
}

export const fetchInitialEnumSchemaInfo = async (
    screenDefinition: ScreenBaseDefinition,
    pagePackageVersion: string,
    locale: LocalizeLocale,
    knownEnumTypes: string[] = [],
): Promise<Dict<{ values: string[]; name: string; translations: string[] }>> => {
    const enums = getUsedEnums(screenDefinition.metadata, knownEnumTypes);
    const cacheKey = `enum_type_query_${MD5(sortBy(enums).join(',')).toString()}`;
    return fetchEnumDefinitions(enums, {
        locale,
        key: cacheKey,
        version: pagePackageVersion,
        shouldFetchPlatformLiterals: false,
    });
};

export const fetchEnumDefinitions = async (
    enums: string[],
    cacheSettings?: GraphqlCacheSettings,
): Promise<Dict<{ name: string; values: string[]; translations: string[] }>> => {
    const query = buildEnumQueries(enums);

    if (isEmpty(query)) {
        return {};
    }

    const response = await executeGraphqlQuery({
        query,
        cacheSettings,
    });

    return objectKeys(response.data)
        .filter(k => objectKeys(query).indexOf(k) !== -1)
        .reduce(
            (
                currentValue: Dict<{ values: string[]; name: string; translations: string[] }>,
                key: string,
                index: number,
            ) => {
                return {
                    ...currentValue,
                    [key]: {
                        name: enums[index],
                        values: response.data[key].enumValues.map((v: any) => v.name),
                        translations: [],
                    },
                };
            },
            {},
        );
};

export const fetchDataTypeDefinitions = async (typeNames: string[]): Promise<Dict<DataTypeDetails>> => {
    const dataTypeNames = typeNames.filter(x => !!x);
    if (!dataTypeNames.length) {
        return {};
    }

    const query = {
        getDataType: {
            __args: { dataTypeNames },
            ...DATA_TYPE_FRAGMENT,
        },
    };
    const response = await executeGraphqlQuery({ query, endpoint: '/metadata' });

    if (Array.isArray(response.data?.getDataType)) {
        return response.data.getDataType.reduce((values: Dict<DataTypeDetails>, next: DataTypeDetails) => {
            return { ...values, [next.name]: next };
        }, {});
    }

    throw new Error('Failed to load data type from the server');
};

export const purgeMetaDataCache = async (): Promise<void> => {
    await idb.deleteDB(ARTIFACT_DATABASE_NAME);
};

export interface QueryMetadataArgs {
    applicationPackages?: Dict<string>;
    metadataType: ArtifactType;
    metadataProperties: ModuleEntryProperties[];
    path?: string;
    shouldFetchPlatformLiterals?: boolean;
    exactMatch?: boolean;
    locale?: string;
    node?: string;
}

export const queryMetadata = async ({
    applicationPackages = {},
    metadataType,
    metadataProperties,
    path,
    shouldFetchPlatformLiterals = false,
    locale = 'en-US',
    exactMatch = false,
    node,
}: QueryMetadataArgs): Promise<MetadataResponse> => {
    let cacheSettings: GraphqlCacheSettings | undefined;
    if (metadataType && path && exactMatch) {
        const artifact = getArtifactDescription(path);
        const key = `${metadataType}/${path}`;
        const version = applicationPackages[`${artifact.vendor}/${artifact.package}`];
        if (version) {
            cacheSettings = { version, key, locale, shouldFetchPlatformLiterals };
        }
    }

    // If a page is requested, we need to get the access rights too.
    const shouldRequestAccessRights = exactMatch && metadataType === 'pages';
    const artifactQuery: any = {};

    const accessRightsQueryFragment = {
        node: true,
        bindings: {
            name: true,
            status: true,
        },
    };

    const queryArguments = {
        filter: {
            packageOrPage: path,
            ...(node && { node }),
            ...(exactMatch && { exactMatch }),
        },
    };

    // Add filters to query if path provided
    if (path) {
        artifactQuery.__args = queryArguments;
    }

    // Add requested properties to the query.
    metadataProperties.forEach(p => {
        if (p === 'fragments') {
            artifactQuery[p] = {
                name: true,
                content: true,
                packageName: true,
            };
        } else if (p === 'extensions') {
            artifactQuery[p] = {
                packageName: true,
                content: true,
            };
        } else {
            artifactQuery[p] = true;
        }
    });

    if (artifactQuery.customFields) {
        artifactQuery.customFields = {
            name: true,
            properties: {
                name: true,
                dataType: true,
                componentType: true,
                anchorPropertyName: true,
                anchorPosition: true,
                destinationTypes: true,
                componentAttributes: true,
            },
        };
    }

    if (artifactQuery.customizableNodes) {
        artifactQuery.customizableNodes = {
            fullName: true,
        };
    }

    if (artifactQuery.customizableNodesWizard) {
        artifactQuery.customizableNodesWizard = {
            fullName: true,
        };
    }

    if (artifactQuery.nodeDetails) {
        const knownNodeNames = objectKeys(xtremRedux.getStore().getState().nodeTypes).filter(
            name => !name.startsWith('XtremUiWidget'),
        );

        artifactQuery.nodeDetails = {
            __args: { knownNodeNames },
            ...getNodeDetailsFragment(),
        };
        // Don't pass knownNodeNames to getCustomDetails because customFields may have changed in known nodes.
        artifactQuery.nodeCustomDetails = getNodeCustomDetailsFragment();
    }

    if (artifactQuery.exportTemplatesByNode) {
        artifactQuery.exportTemplatesByNode = {
            name: true,
            exportTemplates: {
                id: true,
                name: true,
            },
        };
    }

    if (artifactQuery.hasRecordPrintingTemplates) {
        artifactQuery.hasRecordPrintingTemplates = true;
    }

    if (artifactQuery.activeUserSettings) {
        artifactQuery.activeUserSettings = {
            _id: true,
            content: true,
            elementId: true,
            title: true,
            description: true,
        };
    }

    // If a page is requested, we need to get the access rights too.
    if (shouldRequestAccessRights) {
        artifactQuery.access = accessRightsQueryFragment;
    }

    /**
     * If this request is for an exact match with the intention of loading the details of the artifact, we load the list
     * of used string literals.
     **/
    if (exactMatch) {
        artifactQuery.strings = {
            key: true,
            content: true,
        };
    }

    const query: any = {
        [metadataType]: artifactQuery,
    };

    // If platform strings were not previously loaded we request them.
    if (shouldFetchPlatformLiterals) {
        query.strings = getPlatformStringLiteralsSchemaFragment();
    }

    const metadata = await executeGraphqlQuery({ query, endpoint: '/metadata', cacheSettings });
    const metadataArray = metadata.data[metadataType];
    /**
     * If access rights data is required a cached response result is not enough because the access right allocations might
     * have changed since the response was put in the cache. This case we trigger a lightweight request to get the access rights only.
     *  */
    if (shouldRequestAccessRights && metadata.cachedAt) {
        const lightQuery = {
            ...query,
            strings: undefined,
            [metadataType]: {
                ...artifactQuery,
                content: undefined,
                customizableNodes: undefined,
                customizableNodesWizard: undefined,
                // Do not request nodeDetails but request nodeCustomDetails as custom fields may have changed
                nodeDetails: undefined,
                extensions: undefined,
                fragments: undefined,
                plugins: undefined,
                strings: undefined,
            },
        };

        const metadataWithAccessRights = await executeGraphqlQuery({ query: lightQuery, endpoint: '/metadata' });
        metadataArray[0] = {
            ...metadataArray[0],
            ...metadataWithAccessRights.data[metadataType][0],
        };
    }
    if (metadataArray[0]?.nodeDetails && metadataArray[0].nodeCustomDetails?.length) {
        metadataArray[0].nodeDetails = mergeNodeCustomDetails(
            metadataArray[0].nodeDetails,
            metadataArray[0].nodeCustomDetails,
        );
    }
    return metadata.data as MetadataResponse;
};

export type InstalledPackageList = Array<{ name: string; version: string }>;

export interface InitialMetadataQueryResult {
    installedPackages: InstalledPackageList;
    customizationWizardPage: string | null;
    exportConfigurationPage: string | null;
    clientUserSettings: {
        editPage: string | null;
        listPage: string | null;
    };
    printingSettings: PrintingSettings | null;
    serviceOptions: Array<{ name: string; isActive: boolean }>;
}

export const queryInitialMetadata = async (): Promise<InitialMetadataQueryResult> => {
    const result = await executeGraphqlQuery({
        query: {
            installedPackages: {
                name: true,
                version: true,
            },
            customizationWizardPage: true,
            exportConfigurationPage: true,
            clientUserSettings: {
                editPage: true,
                listPage: true,
            },
            printingSettings: {
                canAccessListPrintingWizard: true,
                canAccessPrintingAssignmentDialog: true,
                canAccessRecordPrintingWizard: true,
                recordPrintingGlobalBulkMutationName: true,
                listPrintingGlobalMutationConfigPage: true,
                listPrintingWizardUrl: true,
                printingAssignmentDialogUrl: true,
                recordPrintingWizardUrl: true,
            },
            serviceOptions: {
                name: true,
                isActive: true,
            },
        },
        endpoint: '/metadata',
    });

    return result.data;
};

export const queryPagesByNodeType = memoize(
    (pageNode: string): Promise<Array<{ key: string; title: string }>> =>
        executeGraphqlQuery({
            endpoint: '/metadata',
            query: {
                pages: {
                    __args: {
                        filter: {
                            pageNode,
                        },
                    },
                    key: true,
                    title: true,
                },
            },
        }).then(d => d.data.pages),
);

export async function updateUserClientSettings(view: UiComponentUserSettings): Promise<void> {
    await executeGraphqlQuery({
        endpoint: '/metadata',
        query: {
            mutation: {
                clientUserSettings: {
                    updateUserClientSetting: {
                        __args: {
                            _id: view._id,
                            title: view.title || '',
                            description: view.description || '',
                            content: JSON.stringify(view.content),
                        },
                        _id: true,
                    },
                },
            },
        },
    });
}

export async function createUserClientSettings(
    screenId: string,
    elementId: string,
    view: UiComponentUserSettings,
): Promise<string> {
    const result = await executeGraphqlQuery({
        endpoint: '/metadata',
        query: {
            mutation: {
                clientUserSettings: {
                    createUserClientSetting: {
                        __args: {
                            screenId,
                            elementId,
                            title: view.title || '',
                            description: view.description || '',
                            content: JSON.stringify(view.content),
                        },
                        _id: true,
                    },
                },
            },
        },
    });
    return result.data.clientUserSettings.createUserClientSetting._id;
}
