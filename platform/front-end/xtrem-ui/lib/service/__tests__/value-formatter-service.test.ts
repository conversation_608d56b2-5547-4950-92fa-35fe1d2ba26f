import { addBlockToState, addFieldToState, getMockScreenDefinition, getMockStore } from '../../__tests__/test-helpers';
import * as nestedFields from '../../component/nested-fields';
import { FieldKey, GraphQLTypes } from '../../component/types';
import type { XtremAppState } from '../../redux/state';
import type { Dict } from '@sage/xtrem-shared';
import { formatScreenValues, formatValue } from '../value-formatter-service';
import { CollectionValue } from '../collection-data-service';
import type { ScreenBaseDefinition } from '../screen-base-definition';
import type { FormattedNodeDetails } from '../metadata-types';
import { GraphQLKind } from '../../types';
import type { TableUserSettings } from '../../component/field/table/table-component-types';
import type { ReadonlyFieldControlObject } from '../../component/readonly-field-control-object';

const screenId = 'TestPage';
const nodeTypes: Dict<FormattedNodeDetails> = {
    SalesOrder: {
        name: 'SalesOrder',
        title: 'SalesOrder',
        packageName: '@sage/xtrem-test',
        properties: {
            _id: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
            textSimpleField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
            bind02: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
            orderCity: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
            aDateField: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar },
            aDecimalField: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
            aFloatField: { type: GraphQLTypes.Float, kind: GraphQLKind.Scalar },
            anotherIntegerField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
            aTransientIntegerField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
            aTableField: { type: 'SalesOrderLine', kind: 'LIST' },
            address: { type: 'SalesOrderLineAddress', kind: 'OBJECT' },
        },
        mutations: {},
    },
    SalesOrderLine: {
        name: 'SalesOrderLine',
        title: 'SalesOrderLine',
        packageName: '@sage/xtrem-test',
        properties: {
            aStringField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
            aNumericField: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
            anotherNumericField: { type: GraphQLTypes.Float, kind: GraphQLKind.Scalar },
            anIntegerField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
            address: { type: 'SalesOrderLineAddress', kind: 'OBJECT' },
        },
        mutations: {},
    },
    SalesOrderLineAddress: {
        name: 'SalesOrderLineAddress',
        title: 'SalesOrderLineAddress',
        packageName: '@sage/xtrem-test',
        properties: {
            addressLine1: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
            addressLine2: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
            number: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
            town: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
        },
        mutations: {},
    },
};

describe('value formatter service', () => {
    let state: XtremAppState;
    let screenDefinition: ScreenBaseDefinition;

    beforeEach(() => {
        state = getMockStore().getState();
        state.screenDefinitions[screenId] = getMockScreenDefinition(screenId, 'page');
        screenDefinition = state.screenDefinitions[screenId];
        addFieldToState(FieldKey.Text, state, screenId, '_id', {});
        addFieldToState(FieldKey.Text, state, screenId, 'textSimpleField', {});
        addFieldToState(FieldKey.Text, state, screenId, 'propertyWithBind', { bind: 'textSimpleField' });
        addFieldToState(FieldKey.Text, state, screenId, 'propertyWithNestedBind', {
            bind: { address: { addressLine1: true } },
        });
        addFieldToState(FieldKey.Text, state, screenId, 'aDecimalField', {});
        addFieldToState(FieldKey.Text, state, screenId, 'aTransientField', {
            isTransient: true,
        });
        addFieldToState(FieldKey.Text, state, screenId, 'aFloatField', {});
        addFieldToState(FieldKey.Text, state, screenId, 'anotherIntegerField', {});
        addFieldToState(FieldKey.Plugin, state, screenId, 'aPluginField', {
            pluginPackage: '@sage/myPluginPackage',
        });
        addFieldToState(FieldKey.Text, state, screenId, 'aTransientIntegerField', {
            isTransient: true,
        });
        addFieldToState(FieldKey.Text, state, screenId, 'unknownStringField', {});
        addFieldToState(FieldKey.Numeric, state, screenId, 'unknownNumericField', {});
        addFieldToState(FieldKey.Numeric, state, screenId, 'unknownNumericFieldWithZeroScale', {
            scale: 0,
        });
        addFieldToState(FieldKey.Numeric, state, screenId, 'unknownNumericFieldWithScale', {
            scale: 4,
        });
        addFieldToState(FieldKey.Progress, state, screenId, 'aProgressField', {});
        addFieldToState<FieldKey.Aggregate>(FieldKey.Aggregate, state, screenId, 'aSimpleAggregateField', {
            aggregateOn: 'someProperty',
            aggregationMethod: 'max',
        });
        addFieldToState(FieldKey.Aggregate, state, screenId, 'aComplexAggregateField', {
            aggregateOn: {
                someReferredProperty: {
                    withAnotherChild: {
                        andTheFinalProperty: true,
                    },
                },
            },
            aggregationMethod: 'min',
        });
        addFieldToState<FieldKey.Count>(FieldKey.Count, state, screenId, 'aSimpleCountField', {});
        addFieldToState(FieldKey.Table, state, screenId, 'aTableField', {
            node: '@sage/xtrem-test-package/SalesOrderLine',
            columns: [
                nestedFields.text<any, any>({ bind: 'aStringField' }),
                nestedFields.numeric<any, any>({ bind: 'aNumericField' }),
                nestedFields.numeric<any, any>({ bind: 'anotherNumericField' }),
                nestedFields.text<any, any>({ bind: 'anIntegerField' }),
            ],
        });

        addBlockToState(state, screenId, 'testBlock', { title: 'Test Block' }, [
            { $bind: 'aTableField' },
            { $bind: 'unknownStringField' },
        ]);
    });

    it('should format _id field as string', () => {
        const resultValues = formatScreenValues({
            screenId,
            controlObjects: screenDefinition.metadata.controlObjects as Dict<ReadonlyFieldControlObject<any, any, any>>,
            plugins: {},
            nodeTypes,
            values: { _id: 6 },
            parentNode: '@sage/xtrem-test-package/SalesOrder',
            userSettings: {},
        });

        expect(resultValues._id).toEqual('6');
    });

    it('should format null fields as null', () => {
        const resultValues = formatScreenValues({
            screenId,
            controlObjects: screenDefinition.metadata.controlObjects as Dict<ReadonlyFieldControlObject<any, any, any>>,
            plugins: {},
            nodeTypes,
            values: { textSimpleField: null },
            parentNode: '@sage/xtrem-test-package/SalesOrder',
            userSettings: {},
        });

        expect(resultValues.textSimpleField).toEqual(null);
    });

    it('should include _etag in resultValues', () => {
        const mockEtag = '04LOqNqMUOuYDV//23pkZi7VdNKVcNoWRZxAz6Ci9TA=';
        const resultValues = formatScreenValues({
            screenId,
            controlObjects: screenDefinition.metadata.controlObjects as Dict<ReadonlyFieldControlObject<any, any, any>>,
            plugins: {},
            nodeTypes,
            values: { _id: 6, _etag: mockEtag },
            parentNode: '@sage/xtrem-test-package/SalesOrder',
            userSettings: {},
        });
        expect(resultValues._etag).toEqual(mockEtag);
    });

    describe('formatScreenValues', () => {
        describe('format by scalar type', () => {
            it('should format String values to string', () => {
                const resultValues = formatScreenValues({
                    screenId,
                    controlObjects: screenDefinition.metadata.controlObjects as Dict<
                        ReadonlyFieldControlObject<any, any, any>
                    >,
                    plugins: {},
                    nodeTypes,
                    values: { textSimpleField: 123 },
                    parentNode: '@sage/xtrem-test-package/SalesOrder',
                    userSettings: {},
                });

                expect(resultValues.textSimpleField).toEqual('123');
            });

            it('should format Float values to float', () => {
                const resultValues = formatScreenValues({
                    screenId,
                    controlObjects: screenDefinition.metadata.controlObjects as Dict<
                        ReadonlyFieldControlObject<any, any, any>
                    >,
                    plugins: {},
                    nodeTypes,
                    values: { aFloatField: '12.456' },
                    parentNode: '@sage/xtrem-test-package/SalesOrder',
                    userSettings: {},
                });

                expect(resultValues.aFloatField).toEqual(12.456);
            });

            it('should format Float values to float', () => {
                const resultValues = formatScreenValues({
                    screenId,
                    controlObjects: screenDefinition.metadata.controlObjects as Dict<
                        ReadonlyFieldControlObject<any, any, any>
                    >,
                    plugins: {},
                    nodeTypes,
                    values: { aDecimalField: '65.4321' },
                    parentNode: '@sage/xtrem-test-package/SalesOrder',
                    userSettings: {},
                });

                expect(resultValues.aDecimalField).toEqual(65.4321);
            });

            it('should format Int values to integer', () => {
                const resultValues = formatScreenValues({
                    screenId,
                    controlObjects: screenDefinition.metadata.controlObjects as Dict<
                        ReadonlyFieldControlObject<any, any, any>
                    >,
                    plugins: {},
                    nodeTypes,
                    values: { anotherIntegerField: '65.000' },
                    parentNode: '@sage/xtrem-test-package/SalesOrder',
                    userSettings: {},
                });

                expect(resultValues.anotherIntegerField).toEqual(65);
            });

            it('should retrieve a scalar type based on its bind value', () => {
                const resultValues = formatScreenValues({
                    screenId,
                    controlObjects: screenDefinition.metadata.controlObjects as Dict<
                        ReadonlyFieldControlObject<any, any, any>
                    >,
                    plugins: {},
                    nodeTypes,
                    values: { propertyWithBind: 123 },
                    parentNode: '@sage/xtrem-test-package/SalesOrder',
                    userSettings: {},
                });

                expect(resultValues.propertyWithBind).toEqual('123');
            });

            it('should retrieve a scalar type based on its nested bind value', () => {
                const resultValues = formatScreenValues({
                    screenId,
                    controlObjects: screenDefinition.metadata.controlObjects as Dict<
                        ReadonlyFieldControlObject<any, any, any>
                    >,
                    plugins: {},
                    nodeTypes,
                    values: { propertyWithNestedBind: { addressLine1: 'Test address' } },
                    parentNode: '@sage/xtrem-test-package/SalesOrder',
                    userSettings: {},
                });

                expect(resultValues.propertyWithNestedBind).toEqual('Test address');
            });

            it('should retrieve null with if deep nested bind element is not found', () => {
                const resultValues = formatScreenValues({
                    screenId,
                    controlObjects: screenDefinition.metadata.controlObjects as Dict<
                        ReadonlyFieldControlObject<any, any, any>
                    >,
                    plugins: {},
                    nodeTypes,
                    values: { propertyWithNestedBind: { addressLine2: 'Test address' } },
                    parentNode: '@sage/xtrem-test-package/SalesOrder',
                    userSettings: {},
                });

                expect(resultValues.propertyWithNestedBind).toEqual(null);
            });
        });
    });

    describe('format by control object type', () => {
        it('should resolve transient fields by their control object type if they are not on the graph', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: { unknownStringField: '12' },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues.unknownStringField).toEqual('12');
        });

        it('should resolve the value of a simple aggregation field with no nesting', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: {
                    aSimpleAggregateField: {
                        readAggregate: {
                            someProperty: {
                                max: 5,
                            },
                        },
                    },
                },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues.aSimpleAggregateField).toEqual(5);
        });

        it('should resolve the value of a complex aggregation field with nested property references', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: {
                    aComplexAggregateField: {
                        readAggregate: {
                            someReferredProperty: {
                                withAnotherChild: {
                                    andTheFinalProperty: {
                                        min: 124,
                                    },
                                },
                            },
                        },
                    },
                },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues.aComplexAggregateField).toEqual(124);
        });

        it('should handle lacking aggregate responses', () => {
            let resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: {
                    aComplexAggregateField: null,
                },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues.aComplexAggregateField).toEqual(null);

            resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: {
                    aComplexAggregateField: {
                        readAggregate: null,
                    },
                },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: {
                    aComplexAggregateField: {
                        readAggregate: {
                            someReferredProperty: null,
                        },
                    },
                },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues.aComplexAggregateField).toEqual(null);
        });

        it('should remap plugin fields by the supplied `transformFromGraphValue` function', () => {
            expect(state.plugins['@sage/myPluginPackage'].transformFromGraphValue).not.toHaveBeenCalled();
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: { aPluginField: 'serverRawValue' },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(state.plugins['@sage/myPluginPackage'].transformFromGraphValue).toHaveBeenCalled();
            expect(state.plugins['@sage/myPluginPackage'].transformFromGraphValue).toHaveBeenCalledWith(
                'serverRawValue',
            );

            expect(resultValues.aPluginField).toEqual('myMockedValue');
        });

        it('should format a numeric field without scale to an integer', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: { unknownNumericField: '12.12' },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues.unknownNumericField).toEqual(12);
        });

        it('should format a numeric field with 0 scale value to an integer', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: { unknownNumericFieldWithZeroScale: '11.1234' },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues.unknownNumericFieldWithZeroScale).toEqual(11);
        });

        it('should format a numeric field with a scale value to an integer', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: { unknownNumericFieldWithScale: '9.12' },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues.unknownNumericFieldWithScale).toEqual(9.12);
        });

        it('should format a table field to CollectionValue', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: {
                    aTableField: {
                        pageInfo: {
                            hasNextPage: true,
                            endCursor: 'aCursorValue',
                        },
                        data: [
                            {
                                _id: '1',
                                aStringField: 12.32,
                                aNumericField: '12.37',
                                anotherNumericField: '1.34',
                                anIntegerField: 4,
                            },
                            {
                                _id: '2',
                                aStringField: 22.32,
                                aNumericField: '22.37',
                                anotherNumericField: '2.34',
                                anIntegerField: 4,
                            },
                            {
                                _id: '3',
                                aStringField: 32.32,
                                aNumericField: '32.37',
                                anotherNumericField: '3.34',
                                anIntegerField: 4,
                            },
                        ],
                    },
                },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues.aTableField).toBeInstanceOf(CollectionValue);

            const fieldValue = resultValues.aTableField as CollectionValue;
            const firstRecord = fieldValue.getRecordByIdAndLevel({ id: '1' });

            expect(typeof firstRecord?._id).toEqual('string');
            expect(typeof firstRecord?.aStringField).toEqual('string');
            expect(typeof firstRecord?.aNumericField).toEqual('number');
            expect(typeof firstRecord?.anotherNumericField).toEqual('number');
            expect(typeof firstRecord?.anIntegerField).toEqual('number');

            expect(fieldValue.hasNextPage).toEqual(true);
        });

        it('should format a table field to CollectionValue with user customization', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: {
                    aTableField: {
                        pageInfo: {
                            hasNextPage: true,
                            endCursor: 'aCursorValue',
                        },
                        data: [
                            {
                                _id: '1',
                                aStringField: 12.32,
                                aNumericField: '12.37',
                                anotherNumericField: '1.34',
                                anIntegerField: 4,
                            },
                            {
                                _id: '2',
                                aStringField: 22.32,
                                aNumericField: '22.37',
                                anotherNumericField: '2.34',
                                anIntegerField: 4,
                            },
                            {
                                _id: '3',
                                aStringField: 32.32,
                                aNumericField: '32.37',
                                anotherNumericField: '3.34',
                                anIntegerField: 4,
                            },
                        ],
                    },
                },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {
                    aTableField: {
                        $current: {
                            title: 'Custom Title',
                            isDefault: true,
                            content: [
                                {
                                    sortOrder: [{ colId: 'anotherNumericField', sort: 'desc' }],
                                },
                            ],
                        } as TableUserSettings,
                    },
                },
            });

            expect(resultValues.aTableField).toBeInstanceOf(CollectionValue);

            const fieldValue = resultValues.aTableField as CollectionValue;
            const firstRecord = fieldValue.getData()[0];

            expect(firstRecord?._id).toEqual('3');
        });

        it('should format a table field to CollectionValue without pagination info', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: {
                    aTableField: {
                        data: [
                            {
                                _id: '1',
                                aStringField: 12.32,
                                aNumericField: '12.37',
                                anotherNumericField: '1.34',
                                anIntegerField: 4,
                            },
                            {
                                _id: '2',
                                aStringField: 22.32,
                                aNumericField: '22.37',
                                anotherNumericField: '2.34',
                                anIntegerField: 4,
                            },
                            {
                                _id: '3',
                                aStringField: 32.32,
                                aNumericField: '32.37',
                                anotherNumericField: '3.34',
                                anIntegerField: 4,
                            },
                        ],
                    },
                },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues.aTableField).toBeInstanceOf(CollectionValue);

            const fieldValue = resultValues.aTableField as CollectionValue;

            expect(fieldValue.hasNextPage).toEqual(false);
        });

        it('should format a table field to CollectionValue without a value', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: {},
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues.aTableField).toBeInstanceOf(CollectionValue);

            const fieldValue = resultValues.aTableField as CollectionValue;

            expect(fieldValue.getData()).toEqual([]);
        });

        it('should leave unhandled types alone', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: { aProgressField: '59.12' },
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues.aProgressField).toEqual('59.12');
        });

        it('should exclude container', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: {},
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues).not.toHaveProperty('testBlock');
        });

        it('should exclude transient fields', () => {
            const resultValues = formatScreenValues({
                screenId,
                controlObjects: screenDefinition.metadata.controlObjects as Dict<
                    ReadonlyFieldControlObject<any, any, any>
                >,
                plugins: state.plugins,
                nodeTypes,
                values: {},
                parentNode: '@sage/xtrem-test-package/SalesOrder',
                userSettings: {},
            });

            expect(resultValues).not.toHaveProperty('aTransientField');
        });
    });

    describe('formatValue', () => {
        it('format numeric values to floats if the scale is set as a callback', () => {
            const rowValue = {
                thisIsTheScale: 3,
            };

            let wasCalled = false;
            const result = formatValue({
                screenId,
                elementId: 'fieldId',
                nodeTypes: {},
                fieldType: FieldKey.Numeric,
                plugins: {},
                fieldProperties: {
                    scale: (fieldValue: any, rowValueArg: any) => {
                        wasCalled = true;
                        expect(rowValueArg).toEqual(rowValue);
                        return rowValueArg.thisIsTheScale;
                    },
                } as any,
                value: 1.234567,
                parentNode: undefined,
                rowValue: {
                    thisIsTheScale: 3,
                },
                userSettings: {},
            });
            expect(result).toEqual(1.234567);
            expect(wasCalled).toBe(true);
        });

        it('format numeric values to floats if the scale is set a number', () => {
            const result = formatValue({
                screenId,
                elementId: 'fieldId',
                nodeTypes: {},
                fieldType: FieldKey.Numeric,
                plugins: {},
                fieldProperties: {
                    scale: 3,
                } as any,
                value: 1.234567,
                parentNode: undefined,
                rowValue: {
                    thisIsTheScale: 3,
                },
                userSettings: {},
            });
            expect(result).toEqual(1.234567);
        });

        it('format numeric values to integers if a scale is not set', () => {
            const result = formatValue({
                screenId,
                elementId: 'fieldId',
                nodeTypes: {},
                fieldType: FieldKey.Numeric,
                plugins: {},
                fieldProperties: {
                    scale: () => {
                        return undefined;
                    },
                } as any,
                value: 1.234567,
                parentNode: undefined,
                userSettings: {},
            });
            expect(result).toEqual(1);
        });

        it('format query.totalCount to a number for a count field', () => {
            const result = formatValue({
                screenId,
                elementId: 'aSimpleCountField',
                nodeTypes: {},
                fieldType: FieldKey.Count,
                plugins: {},
                fieldProperties: {} as any,
                value: { query: { totalCount: 123 } },
                parentNode: undefined,
                userSettings: {},
            });
            expect(result).toEqual(123);
        });
        it('Aggregation should return the value as is if no readAggregate is provided', () => {
            const result = formatValue({
                screenId,
                elementId: 'aSimpleAggregateField',
                nodeTypes: {},
                fieldType: FieldKey.Aggregate,
                plugins: {},
                fieldProperties: {} as any,
                value: 1,
                parentNode: undefined,
                userSettings: {},
            });
            expect(result).toEqual(1);
        });
        it('Aggregation should return the value as is if no readAggregate is provided', () => {
            const result = formatValue({
                screenId,
                elementId: 'aSimpleAggregateField',
                nodeTypes: {},
                fieldType: FieldKey.Aggregate,
                plugins: {},
                fieldProperties: {} as any,
                value: 0,
                parentNode: undefined,
                userSettings: {},
            });
            expect(result).toEqual(0);
        });
        it('Aggregation should return null if value is null or undefined and no readAggregate', () => {
            const result = formatValue({
                screenId,
                elementId: 'aSimpleAggregateField',
                nodeTypes: {},
                fieldType: FieldKey.Aggregate,
                plugins: {},
                fieldProperties: {} as any,
                value: null,
                parentNode: undefined,
                userSettings: {},
            });
            expect(result).toEqual(null);
        });
        it('Aggregation should return readAggregate path value', () => {
            const result = formatValue({
                screenId,
                elementId: 'aSimpleAggregateField',
                nodeTypes: {},
                fieldType: FieldKey.Aggregate,
                plugins: {},
                fieldProperties: { aggregateOn: '_id', aggregationMethod: 'distinctCount' } as any,
                value: { readAggregate: { _id: { distinctCount: 4 } } },
                parentNode: undefined,
                userSettings: {},
            });
            expect(result).toEqual(4);
        });
    });
});
