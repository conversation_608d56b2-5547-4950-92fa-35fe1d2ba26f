import { getPageDefinition } from '../page-definition';
import { applyDuplicationBusinessActions, applyDuplicationLayoutStructure } from '../../utils/duplication-utils';

jest.mock('../../utils/duplication-utils', () => ({
    applyDuplicationBusinessActions: jest.fn(),
    applyDuplicationLayoutStructure: jest.fn(),
}));

describe('getPageDefinition', () => {
    it('should return the page definition without duplication', () => {
        const pageConstructor = jest.fn();
        const path = '/path/to/page';
        const isMainPage = true;
        const isDuplicate = false;

        const pageDefinition = getPageDefinition(pageConstructor, path, isMainPage, isDuplicate);

        expect(pageDefinition.metadata).toBeDefined();
        expect(pageDefinition.page).toBeInstanceOf(pageConstructor);
        expect(pageDefinition.path).toBe(path);
        expect(pageDefinition.isMainPage).toBe(isMainPage);
        expect(pageDefinition.type).toBe('page');

        expect(applyDuplicationBusinessActions).not.toHaveBeenCalled();
        expect(applyDuplicationLayoutStructure).not.toHaveBeenCalled();
        expect(pageDefinition.metadata.businessActionsExtensionsThunk).toEqual([]);
    });

    it('should return the page definition with duplication', () => {
        const pageConstructor = jest.fn();
        const path = '/path/to/page';
        const isMainPage = true;
        const isDuplicate = true;

        const pageDefinition = getPageDefinition(pageConstructor, path, isMainPage, isDuplicate);

        expect(pageDefinition.metadata).toBeDefined();
        expect(pageDefinition.page).toBeInstanceOf(pageConstructor);
        expect(pageDefinition.path).toBe(path);
        expect(pageDefinition.isMainPage).toBe(isMainPage);
        expect(pageDefinition.type).toBe('page');

        expect(applyDuplicationBusinessActions).toHaveBeenCalledWith(pageDefinition);
        expect(applyDuplicationLayoutStructure).toHaveBeenCalledWith(pageDefinition);
        expect(pageDefinition.metadata.businessActionsExtensionsThunk).toEqual([]);
    });
});
