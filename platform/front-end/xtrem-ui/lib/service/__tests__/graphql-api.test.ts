import {
    addFieldToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../__tests__/test-helpers';

import type { AxiosRequestConfig } from 'axios';
import axios from 'axios';
import { page } from '../../component/decorators';
import { FieldKey } from '../../component/types';
import * as xtremRedux from '../../redux';
import type { GraphQLApi } from '../graphql-api';
import { Page } from '../page';
import { GraphQLKind, GraphQLTypes } from '../../types';

const pageNode = '@sage/anyPackage/AnyNode';
@page({
    authorizationCode: 'NONE',
    module: 'Your business',
    node: pageNode,
})
class MockPage extends Page {
    _pageMetadata: any = {};
}
(MockPage.prototype as any)._pageMetadata = {};

const createPostSpy = (resultValue?: Object) => {
    return jest.spyOn(axios, 'post').mockReturnValue(
        Promise.resolve({
            data: resultValue && {
                data: resultValue,
            },
            status: 201,
            statusText: 'CREATED',
            headers: {},
            config: {},
        }),
    );
};

const createPostSpyReject = () => {
    return jest.spyOn(axios, 'post').mockRejectedValue({
        response: {
            data: {
                errors: [
                    {
                        message: 'This is a first error message',
                    },
                    { message: 'This is a second error message' },
                ],
            },
        },
        status: 400,
        statusText: 'bad request',
        headers: {},
        config: {},
    });
};
describe('graphql-api', () => {
    let mockState: xtremRedux.XtremAppState;
    let mockPage: Page;
    let graphqlApi: GraphQLApi<any>;
    let navigationPanelMock: jest.MockInstance<any, any>;
    let removePageServerErrors: jest.MockInstance<any, any>;

    beforeEach(() => {
        const FDBFactory = require('fake-indexeddb/lib/FDBFactory');
        (window.indexedDB as any) = new FDBFactory();
        mockState = getMockState();
        mockState.screenDefinitions[MockPage.name] = getMockPageDefinition(
            MockPage.name,
            {
                queryParameters: {
                    _id: 'AnythingWillDo',
                },
            },
            {
                uiComponentProperties: {
                    [MockPage.name]: {
                        node: '@sage/anyPackage/AnyNode',
                    },
                } as any,
            },
        );
        addFieldToState(FieldKey.Text, mockState, MockPage.name, 'myTestField', {});
        mockState.nodeTypes = {
            AnyNode: {
                title: 'AnyNode',
                name: 'AnyNode',
                packageName: '@sage/xtrem-test',
                properties: {
                    myTestField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                    additionalField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                    field1: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                    field2: { type: GraphQLTypes.Float, kind: GraphQLKind.Scalar, isOnInputType: true },
                    field3: { type: GraphQLTypes.IntReference, kind: GraphQLKind.Scalar, isOnInputType: true },
                    field4: {
                        type: GraphQLTypes.IntReference,
                        kind: GraphQLKind.List,
                        isOnInputType: true,
                        isCollection: true,
                    },
                },
                mutations: {},
            },
        };
        getMockStore(mockState, true);

        mockPage = new MockPage();
        mockPage._pageMetadata.screenId = MockPage.name;
        mockPage._pageMetadata.fieldBindings = {};
        mockPage._pageMetadata.uiComponentProperties =
            mockState.screenDefinitions[MockPage.name].metadata.uiComponentProperties;
        mockPage._pageMetadata.controlObjects = mockState.screenDefinitions[MockPage.name].metadata.controlObjects;
        mockPage._pageMetadata.uiComponentProperties[MockPage.name] =
            mockState.screenDefinitions[MockPage.name].metadata.uiComponentProperties[MockPage.name];
        graphqlApi = mockPage.$.graph;
        (
            mockState.screenDefinitions[MockPage.name].metadata.uiComponentProperties[MockPage.name] as any
        ).navigationPanel = {};

        navigationPanelMock = jest.spyOn(xtremRedux.actions, 'refreshNavigationPanel').mockReturnValue({
            type: 'refreshNavigationPanel',
            then: jest.fn(arg => {
                arg();
                return { catch: jest.fn() };
            }),
        } as any);

        removePageServerErrors = jest.spyOn(xtremRedux.actions, 'removePageServerErrors');
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    describe('Raw requests', () => {
        it('should target the /api endpoint by default', async () => {
            const query = 'Non validated query string provided by functional developers';
            const mockedResponse = {
                whatEver: true,
            };
            createPostSpy(mockedResponse);
            const mutationResponse = await mockPage.$.graph.raw(query);
            expect(axios.post).toHaveBeenCalledWith('/api', expect.any(Object), expect.any(Object));
            expect(mutationResponse).toEqual({ ...mockedResponse, diagnoses: undefined });
        });

        it('should target the /metadata endpoint if specified', async () => {
            const query = 'Non validated query string provided by functional developers';
            const mockedResponse = {
                whatEver: true,
            };
            createPostSpy(mockedResponse);
            const mutationResponse = await mockPage.$.graph.raw(query, false, true);
            expect(axios.post).toHaveBeenCalledWith('/metadata', expect.any(Object), expect.any(Object));
            expect(mutationResponse).toEqual({ ...mockedResponse, diagnoses: undefined });
        });

        it('should execute raw query through API raw method', async () => {
            const query = 'Non validated query string provided by functional developers';
            const mockedResponse = {
                whatEver: true,
            };
            createPostSpy(mockedResponse);
            const mutationResponse = await mockPage.$.graph.raw(query);
            expect(mutationResponse).toEqual({ ...mockedResponse, diagnoses: undefined });
        });

        it('should fail raw query through API raw method', async () => {
            const query = 'Query string provided by functional developers that returns undefined data';

            createPostSpy();
            await expect(mockPage.$.graph.raw(query)).rejects.toThrow('Invalid response, no data provided');
        });

        it('should fail raw query through API raw method', async () => {
            const query = 'Failing query string provided by functional developers';

            createPostSpyReject();
            await expect(mockPage.$.graph.raw(query, true)).rejects.toThrow(
                'This is a first error message\nThis is a second error message',
            );
        });

        it('should add custom headers to the network request', async () => {
            jest.spyOn(axios, 'post').mockResolvedValue({
                data: {},
                status: 201,
                statusText: 'CREATED',
                headers: {},
                config: {},
            });

            mockState.applicationContext!.requestHeaders = {
                'Accept-Language': 'en-US',
                'custom-request-for-graphql': 'This could well be a token',
            };

            const query = 'Non validated query string provided by functional developers';
            await mockPage.$.graph.raw(query);
            expect(axios.post).toHaveBeenCalledTimes(1);
            expect(axios.post).toHaveBeenCalledWith(
                '/api',
                {
                    query,
                },
                { headers: mockState.applicationContext!.requestHeaders },
            );
        });

        it('should call onApiRequestError function in app context on error', async () => {
            jest.spyOn(axios, 'post').mockRejectedValue('MY TEST ERROR');

            mockState.applicationContext!.onApiRequestError = jest.fn();

            const query = 'Non validated query string provided by functional developers';
            expect(mockState.applicationContext!.onApiRequestError).not.toHaveBeenCalled();
            try {
                await mockPage.$.graph.raw(query);
            } catch (e) {
                expect(e).toEqual('MY TEST ERROR');
            }
            expect(mockState.applicationContext!.onApiRequestError).toHaveBeenCalledWith('MY TEST ERROR');
        });

        it('should add custom path prefix to the network request', async () => {
            jest.spyOn(axios, 'post').mockResolvedValue({
                data: {},
                status: 201,
                statusText: 'CREATED',
                headers: {},
                config: {},
            });

            mockState.applicationContext!.path = '/my/very/special/prefix';

            const query = 'Non validated query string provided by functional developers';
            await mockPage.$.graph.raw(query);
            expect(axios.post).toHaveBeenCalledTimes(1);
            expect(axios.post).toHaveBeenCalledWith(
                '/my/very/special/prefix/api',
                expect.anything(),
                expect.anything(),
            );
        });
    });

    describe('Mutation API', () => {
        describe('with navigation panel', () => {
            it('should call the graph.node.create operation on API create method and refresh the navigation panel', async () => {
                const executeMock = Promise.resolve({ _id: '12345' });
                const createMock = jest.fn(() => ({
                    execute: () => executeMock,
                }));

                const nodeMock = {
                    create: createMock,
                };
                const nodeSpy = jest.spyOn(graphqlApi.graph, 'node').mockReturnValue(nodeMock);

                await mockPage.$.graph.create();
                expect(nodeSpy).toHaveBeenCalledWith(pageNode);
                expect(createMock).toHaveBeenCalledWith(
                    { _id: true, myTestField: true },
                    { data: { myTestField: 'Test value' } },
                );
                expect(navigationPanelMock).toHaveBeenCalledWith('MockPage', true);
                expect(removePageServerErrors).toHaveBeenCalledWith(mockPage._pageMetadata.screenId);
            });

            it('should call the graph.node.create operation on API create method with overridden values', async () => {
                const executeMock = Promise.resolve({ _id: '12345' });
                const createMock = jest.fn(() => ({
                    execute: () => executeMock,
                }));

                const nodeMock = {
                    create: createMock,
                };
                const nodeSpy = jest.spyOn(graphqlApi.graph, 'node').mockReturnValue(nodeMock);

                mockPage.getSerializedValues = () => ({ myTestField: 'overwritten value' });
                await mockPage.$.graph.create();
                expect(nodeSpy).toHaveBeenCalledWith(pageNode);
                expect(createMock).toHaveBeenCalledWith(
                    { _id: true, myTestField: true },
                    { data: { myTestField: 'overwritten value' } },
                );
            });

            it('should call the graph.node.update operation on API update method and refresh the navigation panel', async () => {
                const executeMock = Promise.resolve({ _id: '12345' });
                const updateMock = jest.fn(() => ({
                    execute: () => executeMock,
                }));

                const nodeMock = {
                    update: updateMock,
                };
                const nodeSpy = jest.spyOn(graphqlApi.graph, 'node').mockReturnValue(nodeMock);

                await mockPage.$.graph.update();
                expect(nodeSpy).toHaveBeenCalledWith(pageNode);
                expect(updateMock).toHaveBeenCalledWith(
                    { _id: true, myTestField: true, _etag: true },
                    { data: { _id: 'AnythingWillDo', myTestField: 'Test value' } },
                );
                expect(navigationPanelMock).toHaveBeenCalledWith('MockPage', false);
                expect(removePageServerErrors).toHaveBeenCalledWith(mockPage._pageMetadata.screenId);
            });

            it('should call the graph.node.update operation on API update method with overridden values', async () => {
                const executeMock = Promise.resolve({ _id: '12345' });
                const updateMock = jest.fn(() => ({
                    execute: () => executeMock,
                }));

                const nodeMock = {
                    update: updateMock,
                };
                const nodeSpy = jest.spyOn(graphqlApi.graph, 'node').mockReturnValue(nodeMock);

                mockPage.getSerializedValues = () => ({ myTestField: 'overwritten value' });
                await mockPage.$.graph.update();
                expect(nodeSpy).toHaveBeenCalledWith(pageNode);
                expect(updateMock).toHaveBeenCalledWith(
                    { _id: true, myTestField: true, _etag: true },
                    { data: { _id: 'AnythingWillDo', myTestField: 'overwritten value' } },
                );
            });

            it('should call the graph.node.deleteById operation on API delete method and refresh the navigation panel', async () => {
                const nodeMock = {
                    deleteById: () => ({
                        execute: () => Promise.resolve({}),
                    }),
                };
                const nodeSpy = jest.spyOn(graphqlApi.graph, 'node').mockReturnValue(nodeMock);

                await mockPage.$.graph.delete();
                expect(nodeSpy).toHaveBeenCalledWith(pageNode);
                expect(navigationPanelMock).toHaveBeenCalled();
            });
        });
    });

    describe('without navigation panel', () => {
        beforeEach(() => {
            (
                mockState.screenDefinitions[MockPage.name].metadata.uiComponentProperties[MockPage.name] as any
            ).navigationPanel = null;
        });

        it('should call the graph.node.create operation on API create method and not refresh the navigation panel', async () => {
            const nodeMock = {
                create: () => ({
                    execute: () => Promise.resolve({ _id: '12345' }),
                }),
            };
            const nodeSpy = jest.spyOn(graphqlApi.graph, 'node').mockReturnValue(nodeMock);

            await mockPage.$.graph.create();
            expect(nodeSpy).toHaveBeenCalledWith(pageNode);
            expect(navigationPanelMock).not.toHaveBeenCalled();
        });

        it('should call the graph.node.update operation on API update method and not refresh the navigation panel', async () => {
            const nodeMock = {
                update: () => ({
                    execute: () => Promise.resolve({ _id: '12345' }),
                }),
            };
            const nodeSpy = jest.spyOn(graphqlApi.graph, 'node').mockReturnValue(nodeMock);

            await mockPage.$.graph.update();
            expect(nodeSpy).toHaveBeenCalledWith(pageNode);
            expect(navigationPanelMock).not.toHaveBeenCalled();
        });

        it('should call the graph.node.deleteById operation on API delete method and not refresh the navigation panel', async () => {
            const nodeMock = {
                deleteById: () => ({
                    execute: () => Promise.resolve({}),
                }),
            };
            const nodeSpy = jest.spyOn(graphqlApi.graph, 'node').mockReturnValue(nodeMock);

            await mockPage.$.graph.delete();
            expect(nodeSpy).toHaveBeenCalledWith(pageNode);
            expect(navigationPanelMock).not.toHaveBeenCalled();
        });
    });

    it('should call the graph.node.deleteById operation on API delete method with custom node', async () => {
        const nodeMock = {
            deleteById: () => ({
                execute: () => Promise.resolve({}),
            }),
        };
        const nodeSpy = jest.spyOn(graphqlApi.graph, 'node').mockReturnValue(nodeMock);

        await mockPage.$.graph.delete({ nodeName: '@sage/x3-any-package/MyNode' });
        expect(nodeSpy).toHaveBeenCalledWith('@sage/x3-any-package/MyNode');
    });

    it('should call the graph.node.deleteById operation on API delete method with the current record id', async () => {
        const nodeMock = {
            deleteById: jest.fn(() => ({
                execute: () => Promise.resolve({}),
            })),
        };
        jest.spyOn(graphqlApi.graph, 'node').mockReturnValue(nodeMock);

        await mockPage.$.graph.delete();
        expect(nodeMock.deleteById).toHaveBeenCalledWith('AnythingWillDo');
    });

    it('should call the graph.node.deleteById operation on API delete method with custom ID', async () => {
        const nodeMock = {
            deleteById: jest.fn(() => ({
                execute: () => Promise.resolve({}),
            })),
        };

        jest.spyOn(graphqlApi.graph, 'node').mockReturnValue(nodeMock);

        await mockPage.$.graph.delete({ _id: 'TEST_ID' });
        expect(nodeMock.deleteById).toHaveBeenCalledWith('TEST_ID');
    });

    describe('serializing data', () => {
        let postMock: jest.SpyInstance<Promise<unknown>, [string, any?, AxiosRequestConfig?]>;

        beforeEach(() => {
            postMock = createPostSpy();

            addFieldToState<FieldKey.Aggregate>(
                FieldKey.Aggregate,
                mockState,
                MockPage.name,
                'test-aggregate-field',
                {
                    title: 'Test Field Title',
                    aggregateOn: 'anyCollectionField',
                    aggregationMethod: 'max',
                },
                12343,
                mockPage,
            );

            addFieldToState(
                FieldKey.Text,
                mockState,
                MockPage.name,
                'field1',
                {
                    title: 'A Text field',
                },
                'text value',
                mockPage,
            );

            addFieldToState(
                FieldKey.Numeric,
                mockState,
                MockPage.name,
                'field2',
                {
                    title: 'A numeric field',
                },
                123.456,
                mockPage,
            );

            addFieldToState<FieldKey.Reference>(
                FieldKey.Reference,
                mockState,
                MockPage.name,
                'field3',
                {
                    title: 'A reference field',
                    valueField: 'name',
                    helperTextField: 'code',
                    node: '@sage/any/Node',
                },
                {
                    _id: 1234,
                    name: 'product name',
                    code: '00932234',
                },
                mockPage,
            );

            addFieldToState<FieldKey.MultiReference>(
                FieldKey.MultiReference,
                mockState,
                MockPage.name,
                'field4',
                {
                    title: 'A multi-reference field',
                    valueField: 'name',
                    helperTextField: 'code',
                    node: '@sage/any/Node',
                },
                [
                    {
                        _id: 1234,
                        name: 'product name',
                        code: '00932234',
                    },
                    {
                        _id: 1235,
                        name: 'product name 2',
                        code: '00932235',
                    },
                ],
                mockPage,
            );
        });

        it('should serialize value', async () => {
            await expect(mockPage.$.graph.update()).rejects.toThrow();

            expect(postMock.mock.calls[0][1].query).toEqual(`mutation {
    anyPackage {
        anyNode {
            update (data: {_id: "AnythingWillDo", myTestField: "Test value", field1: "text value", field2: 123.456, field3: "_id:1234", field4: ["_id:1234", "_id:1235"]}) {
                myTestField
                test-aggregate-field {
                    readAggregate {
                        anyCollectionField {
                            max
                        }
                    }
                }
                field1
                field2
                field3 {
                    _id
                    name
                    code
                }
                field4 {
                    _id
                    name
                    code
                }
                _id
                _etag
            }
        }
    }
}`);
        });

        it('should exclude values that are not included to the input node type from the serialized mutation', async () => {
            mockState.nodeTypes = {
                AnyNode: {
                    title: 'AnyNode',
                    name: 'AnyNode',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        field1: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                        field3: { type: GraphQLTypes.IntReference, kind: GraphQLKind.Scalar, isOnInputType: true },
                    },
                    mutations: {},
                },
            };
            await expect(mockPage.$.graph.update()).rejects.toThrow();

            expect(postMock.mock.calls[0][1].query).toEqual(`mutation {
    anyPackage {
        anyNode {
            update (data: {_id: "AnythingWillDo", field1: "text value", field3: "_id:1234"}) {
                myTestField
                test-aggregate-field {
                    readAggregate {
                        anyCollectionField {
                            max
                        }
                    }
                }
                field1
                field2
                field3 {
                    _id
                    name
                    code
                }
                field4 {
                    _id
                    name
                    code
                }
                _id
                _etag
            }
        }
    }
}`);
        });

        it("should not serialize transient field's value", async () => {
            addFieldToState(
                FieldKey.Text,
                mockState,
                MockPage.name,
                'additionalField',
                {
                    title: 'Another Text field',
                },
                'additional value',
                mockPage,
            );

            addFieldToState(
                FieldKey.Text,
                mockState,
                MockPage.name,
                'additionalTransientField',
                {
                    title: 'A Transient field',
                    isTransient: true,
                },
                'additional transient value',
                mockPage,
            );

            await expect(mockPage.$.graph.update()).rejects.toThrow();
            expect(postMock.mock.calls[0][1].query).toEqual(`mutation {
    anyPackage {
        anyNode {
            update (data: {_id: "AnythingWillDo", myTestField: "Test value", field1: "text value", field2: 123.456, field3: "_id:1234", field4: ["_id:1234", "_id:1235"], additionalField: "additional value"}) {
                myTestField
                test-aggregate-field {
                    readAggregate {
                        anyCollectionField {
                            max
                        }
                    }
                }
                field1
                field2
                field3 {
                    _id
                    name
                    code
                }
                field4 {
                    _id
                    name
                    code
                }
                additionalField
                _id
                _etag
            }
        }
    }
}`);
        });

        it("should not serialize aggregate field's value", async () => {
            addFieldToState(
                FieldKey.Text,
                mockState,
                MockPage.name,
                'additionalField',
                {
                    title: 'Another Text field',
                },
                'additional value',
            );

            addFieldToState<FieldKey.Aggregate>(
                FieldKey.Aggregate,
                mockState,
                MockPage.name,
                'aggregateField',
                {
                    title: 'A Transient field',
                    aggregationMethod: 'avg',
                    aggregateOn: 'anyField',
                },
                {
                    readAggregate: {
                        anyField: { avg: 1234 },
                    },
                },
                mockPage,
            );

            await expect(mockPage.$.graph.update()).rejects.toThrow();

            expect(postMock.mock.calls[0][1].query).toEqual(`mutation {
    anyPackage {
        anyNode {
            update (data: {_id: "AnythingWillDo", myTestField: "Test value", field1: "text value", field2: 123.456, field3: "_id:1234", field4: ["_id:1234", "_id:1235"], additionalField: "additional value"}) {
                myTestField
                test-aggregate-field {
                    readAggregate {
                        anyCollectionField {
                            max
                        }
                    }
                }
                field1
                field2
                field3 {
                    _id
                    name
                    code
                }
                field4 {
                    _id
                    name
                    code
                }
                additionalField
                aggregateField {
                    readAggregate {
                        anyField {
                            avg
                        }
                    }
                }
                _id
                _etag
            }
        }
    }
}`);
        });
    });
});

// TODO Test get/set values
