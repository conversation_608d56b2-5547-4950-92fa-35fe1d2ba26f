import { resizeImage, calculateNewDimensions } from '../image-resize-service';

describe('resizeImage', () => {
    it('should reject if the file is not an image', async () => {
        const nonImageFile = new File(['dummy content'], 'test.txt', { type: 'text/plain' });
        try {
            await resizeImage(nonImageFile);
        } catch (error) {
            expect(error.message).toEqual('File is not an image');
        }
    });

    it('should resolve without resizing if the file is an SVG', async () => {
        const svgFile = new File(['<svg></svg>'], 'test.svg', { type: 'image/svg+xml' });
        const result = await resizeImage(svgFile);
        expect(result).toEqual(svgFile);
    });
});
describe('calculateNewDimensions', () => {
    it('should return original dimensions if image is smaller than max dimensions', () => {
        const img = new Image();
        img.width = 800;
        img.height = 600;

        const { width, height } = calculateNewDimensions(img);

        expect(width).toEqual(800);
        expect(height).toEqual(600);
    });

    it('should resize image maintaining aspect ratio if width is larger than max width', () => {
        const img = new Image();
        img.width = 3000;
        img.height = 2000;

        const { width, height } = calculateNewDimensions(img);

        expect(width).toEqual(1200);
        expect(height).toBeCloseTo(800, 0);
    });

    it('should resize image maintaining aspect ratio if height is larger than max height', () => {
        const img = new Image();
        img.width = 1000;
        img.height = 2000;

        const { width, height } = calculateNewDimensions(img);

        expect(width).toBeCloseTo(400, 0);
        expect(height).toEqual(800);
    });

    it('should resize image maintaining aspect ratio if both dimensions are larger than max dimensions', () => {
        const img = new Image();
        img.width = 3000;
        img.height = 4000;

        const { width, height } = calculateNewDimensions(img);

        expect(width).toBeCloseTo(600, 0);
        expect(height).toEqual(800);
    });
});
