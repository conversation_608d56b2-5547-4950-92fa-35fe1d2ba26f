import { getMockPageDefinition, getMockState, getMockStore } from '../../__tests__/test-helpers';

// DO NOT REARRANGE THE IMPORTS
import type { ClientNode } from '@sage/xtrem-client';
import { Decimal } from '@sage/xtrem-decimal';
import type { Dict, FiltrableType } from '@sage/xtrem-shared';
import { waitFor } from '@testing-library/dom';
import * as nestedFields from '../../component/nested-fields';
import { FieldKey } from '../../component/types';
import { GraphQLKind, GraphQLTypes } from '../../types';
import { CollectionValue } from '../collection-data-service';
import type { CollectionValueFieldPropertiesWithAdditionalRecords } from '../collection-data-types';
import { CollectionFieldTypes, RecordActionType } from '../collection-data-types';
import type { Filter } from '../filter-service';
import * as graphQLService from '../graphql-service';
import type { LokiDb } from '../loki';
import { destroyScreenCollections } from '../loki';
import type { ScreenBase } from '../screen-base';
import type { TableProperties } from '../../component/control-objects';
import type { FormattedNodeDetails } from '../metadata-types';

class TestCollectionValue<T extends ClientNode = any> extends CollectionValue<T> {
    public db: LokiDb<T>;
}

describe('collection data service', () => {
    const screenId = 'TestPage';
    const fieldId = 'testTable';
    let collectionValue: TestCollectionValue<Node>;
    type Node = {
        _id: string;
        anyField: string;
        someOtherField: number;
        decimalField: number;
        child?: string;
        dateField: string;
        aThridField: string;
        testNonInputField?: string;
        multiDropDownField?: string[];
    };

    beforeEach(() => {
        jest.clearAllMocks();
        const state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId, { selectedRecordId: '1234' });
        state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] = {
            node: '@sage/xtrem-test/AnyNode',
        } as any;
        state.screenDefinitions[screenId].metadata.uiComponentProperties[fieldId] = {
            title: 'field',
            isTransient: false,
        };

        getMockStore(state);

        collectionValue = new TestCollectionValue({
            screenId,
            elementId: fieldId,
            isTransient: false,
            hasNextPage: true,
            orderBy: [{ anyField: 1 }],
            levelMap: { 0: 'child' as const, 1: undefined },
            columnDefinitions: [
                [
                    nestedFields.text<any, any>({ bind: '_id' }),
                    nestedFields.text<any, any>({ bind: 'anyField' }),
                    nestedFields.numeric({ bind: 'someOtherField' }),
                    nestedFields.text({ bind: 'testNonInputField' }),
                    nestedFields.multiDropdown<any, any>({ bind: 'multiDropDownField' }),
                ],
                [nestedFields.text<any, any>({ bind: 'child' })],
            ],
            nodeTypes: {
                AnyNode: {
                    name: 'AnyNode',
                    title: 'AnyNode',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        _id: { type: 'IntReference', kind: GraphQLKind.Scalar, isOnInputType: true },
                        _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar, isOnInputType: true },
                        _action: { type: GraphQLTypes.Enum, kind: GraphQLKind.Scalar, isOnInputType: true },
                        anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                        someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar, isOnInputType: true },
                        child: {
                            type: 'ChildNode',
                            kind: GraphQLKind.List,
                            isCollection: true,
                            isOnInputType: true,
                            isMutable: true,
                        },
                        multiDropDownField: {
                            type: GraphQLTypes.String,
                            kind: GraphQLKind.List,
                            isCollection: true,
                            isOnInputType: true,
                        },
                    },
                    mutations: {},
                },
                ChildNode: {
                    name: 'ChildNode',
                    title: 'ChildNode',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        _id: { type: 'IntReference', kind: GraphQLKind.Scalar, isOnInputType: true },
                        _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar, isOnInputType: true },
                        _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar, isOnInputType: true },
                        anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                        someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar, isOnInputType: true },
                        child: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                    },
                    mutations: {},
                },
            },
            nodes: ['@sage/xtrem-test/AnyNode', '@sage/xtrem-test/ChildNode'],
            filter: [undefined],
            initialValues: [
                { _id: '1', anyField: 'test', someOtherField: 4, multiDropDownField: [] },
                {
                    _id: '2',
                    anyField: 'test string 2',
                    someOtherField: -5,
                    multiDropDownField: ['good', 'great', 'bad'],
                },
                { _id: '3', anyField: 'test aasd', someOtherField: 32432 },
            ],
            fieldType: CollectionFieldTypes.DESKTOP_TABLE,
        });
    });

    afterEach(() => {
        destroyScreenCollections(screenId);
    });

    describe('CollectionValue', () => {
        it('"generateIndex" should return different IDs upon every call', () => {
            expect(collectionValue.generateIndex()).toBe('-1');
            expect(collectionValue.generateIndex()).toBe('-2');
            expect(collectionValue.generateIndex()).toBe('-3');
        });

        it('"getIdPathToRecordByIdAndLevel" should return full path to record', () => {
            expect(collectionValue.db.getIdPathToRecordByIdAndLevel({ id: '1' })).toStrictEqual(['1']);
            const newRow = collectionValue.addOrUpdateRecordValue({
                recordData: { _id: '1' },
                level: 1,
                parentId: '1',
            });
            expect(collectionValue.db.getIdPathToRecordByIdAndLevel({ id: newRow._id, level: 1 })).toStrictEqual([
                '1',
                newRow._id,
            ]);
        });

        it('"calculateAggregatedValue" should return aggregated values', () => {
            // hasNextPage is set to true => no aggregation values are returned
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'sum',
                }),
            ).toStrictEqual(Number.NaN);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'avg',
                }),
            ).toStrictEqual(Number.NaN);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'count',
                }),
            ).toStrictEqual(Number.NaN);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'min',
                }),
            ).toStrictEqual(Number.NaN);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'max',
                }),
            ).toStrictEqual(Number.NaN);

            // hasNextPage is set to false => aggregation values are returned
            collectionValue.hasNextPage = false;
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'sum',
                }),
            ).toStrictEqual(32431);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'avg',
                }),
            ).toStrictEqual(10810.333333333334);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'count',
                }),
            ).toStrictEqual(3);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'min',
                }),
            ).toStrictEqual(-5);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'max',
                }),
            ).toStrictEqual(32432);

            // add a new value to collection => aggregation values are recalculated
            collectionValue.addOrUpdateRecordValue({
                recordData: { _id: '4', someOtherField: -10 },
            });
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'sum',
                }),
            ).toStrictEqual(32421);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'avg',
                }),
            ).toStrictEqual(8105.25);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'count',
                }),
            ).toStrictEqual(4);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'min',
                }),
            ).toStrictEqual(-10);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'max',
                }),
            ).toStrictEqual(32432);

            // a filter is set and applied to aggregated values
            (collectionValue as any).filter = [{ '0': { someOtherField: { _gt: 0 } } }];
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'sum',
                }),
            ).toStrictEqual(32436);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'avg',
                }),
            ).toStrictEqual(16218);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'count',
                }),
            ).toStrictEqual(2);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'min',
                }),
            ).toStrictEqual(4);
            expect(
                collectionValue.calculateAggregatedValue({
                    aggregationKey: 'someOtherField',
                    aggregationMethod: 'max',
                }),
            ).toStrictEqual(32432);
            (collectionValue as any).filter = [undefined];

            // unknown property
            expect(
                collectionValue.calculateAggregatedValue({ aggregationKey: 'unknwon', aggregationMethod: 'sum' }),
            ).toStrictEqual(Number.NaN);
            expect(
                collectionValue.calculateAggregatedValue({ aggregationKey: 'unknwon', aggregationMethod: 'avg' }),
            ).toStrictEqual(Number.NaN);
            expect(
                collectionValue.calculateAggregatedValue({ aggregationKey: 'unknwon', aggregationMethod: 'count' }),
            ).toStrictEqual(Number.NaN);
            expect(
                collectionValue.calculateAggregatedValue({ aggregationKey: 'unknwon', aggregationMethod: 'min' }),
            ).toStrictEqual(Number.NaN);
            expect(
                collectionValue.calculateAggregatedValue({ aggregationKey: 'unknwon', aggregationMethod: 'max' }),
            ).toStrictEqual(Number.NaN);

            // aggregation on dates
            collectionValue.addOrUpdateRecordValue({
                recordData: { _id: '5', someOtherField: -10, dateField: '2019-10-10' },
            });
            collectionValue.addOrUpdateRecordValue({
                recordData: { _id: '6', someOtherField: -10, dateField: '2020-10-10' },
            });
            expect(() =>
                collectionValue.calculateAggregatedValue({ aggregationKey: 'dateField', aggregationMethod: 'sum' }),
            ).toThrow('Unsupported sum aggregation on value "2019-10-10"');
            expect(() =>
                collectionValue.calculateAggregatedValue({ aggregationKey: 'dateField', aggregationMethod: 'avg' }),
            ).toThrow('Unsupported sum aggregation on value "2019-10-10"');
            expect(
                collectionValue.calculateAggregatedValue({ aggregationKey: 'dateField', aggregationMethod: 'count' }),
            ).toStrictEqual(2);
            expect(
                collectionValue.calculateAggregatedValue({ aggregationKey: 'dateField', aggregationMethod: 'min' }),
            ).toStrictEqual('2019-10-10');
            expect(
                collectionValue.calculateAggregatedValue({ aggregationKey: 'dateField', aggregationMethod: 'max' }),
            ).toStrictEqual('2020-10-10');
        });

        it('"getAncestorIds" should return all ancestor IDs', () => {
            expect(collectionValue.db.getAncestorIds({ id: '1' })).toStrictEqual([]);
            const newRow = collectionValue.addOrUpdateRecordValue({
                recordData: { _id: '1' },
                level: 1,
                parentId: '1',
            });
            expect(collectionValue.db.getAncestorIds({ id: newRow._id, level: 1 })).toStrictEqual(['1']);
        });

        it('"getAllDataAsTree" should not return empty child collections if "excludeEmptyChildCollections" is true', () => {
            collectionValue.addOrUpdateRecordValue({
                recordData: { child: 'childValue' },
                level: 1,
                parentId: '1',
            });
            expect(collectionValue.getAllDataAsTree({ excludeEmptyChildCollections: true })).toStrictEqual([
                {
                    _id: '3',
                    anyField: 'test aasd',
                    someOtherField: 32432,
                },
                {
                    _id: '2',
                    anyField: 'test string 2',
                    someOtherField: -5,
                    multiDropDownField: ['good', 'great', 'bad'],
                },
                {
                    _id: '1',
                    anyField: 'test',
                    child: [
                        {
                            _id: '-1',
                            child: 'childValue',
                        },
                    ],
                    someOtherField: 4,
                    multiDropDownField: [],
                },
            ]);
        });

        it('getRawRecords() should return the records as an array without any metadata', () => {
            const result = collectionValue.getRawRecords();
            expect(result).toBeInstanceOf(Array);
            expect(result).toHaveLength(3);
            expect(Object.keys(result[0])).toEqual(['_id', 'anyField', 'multiDropDownField', 'someOtherField']);
        });

        it('getFormattedActiveRecords() should return the records as an array without any metadata', () => {
            const result = collectionValue.getFormattedActiveRecords();
            expect(result).toBeInstanceOf(Array);
            expect(result).toHaveLength(3);
            expect(Object.keys(result[0])).toEqual(['_id', 'anyField', 'multiDropDownField', 'someOtherField']);
        });

        it('getFormattedActiveRecords() should return not include deleted records', () => {
            collectionValue.removeRecord({ recordId: '2' });
            const result = collectionValue.getFormattedActiveRecords();
            expect(result).toBeInstanceOf(Array);
            expect(result).toHaveLength(2);
            expect(result[0]._id).toEqual('1');
            expect(result[1]._id).toEqual('3');
        });

        it('getRawRecord() should return an object without any metadata if found', () => {
            const result = collectionValue.getRawRecord({ id: '2' });
            expect(result._id).toEqual('2');
            expect(result.anyField).toEqual('test string 2');
            expect(result.someOtherField).toEqual(-5);
            expect(Object.keys(result)).toEqual(['_id', 'anyField', 'multiDropDownField', 'someOtherField']);
        });

        it('getRawRecordByFieldValue() should return an object without any metadata if found', () => {
            const result = collectionValue.getRawRecordByFieldValue({ fieldName: 'someOtherField', fieldValue: -5 });
            expect(result!._id).toEqual('2');
            expect(Object.keys(result!)).toEqual(['_id', 'anyField', 'multiDropDownField', 'someOtherField', 'child']);
        });

        it('addOrUpdateRecordValue should add record to the DB if it does not have an ID', () => {
            expect(collectionValue.getData()).toHaveLength(3);
            const result = collectionValue.addOrUpdateRecordValue({
                recordData: { anyField: 'new record', someOtherField: 123 },
                shouldNotifySubscribers: false,
            });
            expect(result._id).toEqual('-1');
            expect(collectionValue.getData()).toHaveLength(4);
        });

        it("should remove properties that don't belong to input type when sending data to server", () => {
            collectionValue.addOrUpdateRecordValue({
                recordData: { anyField: 'new record', someOtherField: 123, calculated: 'calculated' } as any,
                shouldNotifySubscribers: false,
            });
            expect(collectionValue.getData()).toHaveLength(4);
            expect(collectionValue.getAllDataAsTree()).toStrictEqual([
                { _id: '3', anyField: 'test aasd', child: [], someOtherField: 32432 },
                {
                    _id: '2',
                    anyField: 'test string 2',
                    child: [],
                    someOtherField: -5,
                    multiDropDownField: ['good', 'great', 'bad'],
                },
                { _id: '1', anyField: 'test', child: [], someOtherField: 4, multiDropDownField: [] },
                { _id: '-1', anyField: 'new record', child: [], someOtherField: 123 },
            ]);
        });

        it("should NOT remove properties that don't belong to input type when returning value to functional code", () => {
            collectionValue.addOrUpdateRecordValue({
                recordData: { anyField: 'new record', someOtherField: 123, calculated: 'calculated' } as any,
                shouldNotifySubscribers: false,
            });
            expect(collectionValue.getData()).toHaveLength(4);
            expect(collectionValue.getAllDataAsTree({ cleanInputTypes: false })).toStrictEqual([
                { _id: '3', anyField: 'test aasd', child: [], someOtherField: 32432 },
                {
                    _id: '2',
                    anyField: 'test string 2',
                    child: [],
                    someOtherField: -5,
                    multiDropDownField: ['good', 'great', 'bad'],
                },
                { _id: '1', anyField: 'test', child: [], someOtherField: 4, multiDropDownField: [] },
                { _id: '-1', anyField: 'new record', child: [], someOtherField: 123, calculated: 'calculated' },
            ]);
        });

        it('addOrUpdateRecordValue should add record to the DB if its ID does not exist', () => {
            expect(collectionValue.getData()).toHaveLength(3);
            const result = collectionValue.addOrUpdateRecordValue({
                recordData: { anyField: 'new record', someOtherField: 123, _id: '123' },
                shouldNotifySubscribers: false,
            });
            expect(result._id).toEqual('123');
            expect(collectionValue.getData()).toHaveLength(4);
        });

        it('addOrUpdateRecordValue should update record in the DB if its ID already exists', () => {
            let record = collectionValue.getRawRecord({ id: '2' });
            expect(record.anyField).toEqual('test string 2');
            expect(collectionValue.getData()).toHaveLength(3);
            const result = collectionValue.addOrUpdateRecordValue({
                recordData: { anyField: 'updated record', someOtherField: 123, _id: '2' },
                shouldNotifySubscribers: false,
            });
            expect(result._id).toEqual('2');
            expect(collectionValue.getData()).toHaveLength(3);
            record = collectionValue.getRawRecord({ id: '2' });
            expect(record.anyField).toEqual('updated record');
        });

        it('getRawRecord() should return null if no object is found with the key', () => {
            const result = collectionValue.getRawRecord({ id: '7' });
            expect(result).toEqual(null);
        });

        it('setRecordValue() should update a record in the database and mark it modified', () => {
            const newData = { _id: '3', anyField: 'my new value', someOtherField: 1234 };
            collectionValue.setRecordValue({ recordData: newData, shouldNotifySubscribers: false });
            const recordFromCollection = collectionValue.db.findOne({ id: newData._id, cleanMetadata: false });
            expect(recordFromCollection?.anyField).toEqual(newData.anyField);
            expect(recordFromCollection?.someOtherField).toEqual(newData.someOtherField);
            expect(recordFromCollection?.__action).toEqual(RecordActionType.MODIFIED);
        });

        it('setRecordValue() should not mark the record modified if it is called with resetRecordAction', () => {
            const newData = { _id: '3', anyField: 'my new value', someOtherField: 1234 };
            collectionValue.setRecordValue({
                recordData: newData,
                shouldNotifySubscribers: false,
                toBeMarkedAsDirty: [],
                resetRecordAction: true,
            });
            const recordFromCollection = collectionValue.db.findOne({ id: newData._id, cleanMetadata: false });
            expect(recordFromCollection?.__action).toEqual(undefined);
        });

        it('setRecordValue() should only mark those field dirty that is passed in when the toBeMarkedAsDirty option is used', () => {
            const newData = { _id: '3', anyField: 'my new value', someOtherField: 1234 };
            collectionValue.setRecordValue({
                recordData: newData,
                shouldNotifySubscribers: false,
                toBeMarkedAsDirty: ['anyField'],
            });
            const recordFromCollection = collectionValue.db.findOne({ id: newData._id, cleanMetadata: false });
            expect(Array.from(recordFromCollection!.__dirtyColumns!.values())).toEqual(['anyField']);
        });

        it('setRecordValue() should pass the values through the value formatter', () => {
            const newData = { _id: '3', anyField: 'my new value', someOtherField: '1234.40' as any };
            collectionValue.setRecordValue({ recordData: newData, shouldNotifySubscribers: false });
            const recordFromCollection = collectionValue.db.findOne({ id: '3' });
            expect(recordFromCollection?.someOtherField).toEqual(1234);
        });

        it('setRecordValue() should throw if the record does not exist', () => {
            const newData = { _id: '1232', anyField: 'my new value', someOtherField: 1234.4 };
            expect(() =>
                collectionValue.setRecordValue({ recordData: newData, shouldNotifySubscribers: false }),
            ).toThrow(
                "A record with ID '1232' could not be found therefore it could not be updated. Are sure you want to update an existing record?",
            );
        });

        it('setCellValue() should update a record in the database and mark it modified', () => {
            const newFieldValue = 'my new test content';
            collectionValue.setCellValue({ recordId: '2', columnId: 'anyField', value: newFieldValue });
            const recordFromCollection = collectionValue.db.findOne({ id: '2', cleanMetadata: false });
            expect(recordFromCollection?.anyField).toEqual(newFieldValue);
            expect(recordFromCollection?.__action).toEqual(RecordActionType.MODIFIED);
        });

        it('setCellValue() should update an array field ', () => {
            const newFieldValue = ['good'];
            collectionValue.setCellValue({ recordId: '2', columnId: 'multiDropDownField', value: newFieldValue });
            const recordFromCollection = collectionValue.db.findOne({ id: '2', cleanMetadata: false });
            expect(recordFromCollection?.multiDropDownField).toEqual(newFieldValue);
        });

        it('addRecord() should create a new record in the database and mark it added', () => {
            const newData = { _id: '4', anyField: 'my new value', someOtherField: 1234 };
            collectionValue.addRecord({ recordData: newData });
            const recordFromCollection = collectionValue.db.findOne({ id: newData._id, cleanMetadata: false });
            expect(recordFromCollection?.anyField).toEqual(newData.anyField);
            expect(recordFromCollection?.someOtherField).toEqual(newData.someOtherField);
            expect(recordFromCollection?.__action).toEqual(RecordActionType.ADDED);
            expect(recordFromCollection?.__dirty).toEqual(true);
        });

        it('addRecord() should automatically assign an ID to a new row without an ID field', () => {
            const newData = { anyField: 'my new value', someOtherField: 1234 };
            const updatedData = collectionValue.addRecord({ recordData: newData });

            expect(updatedData._id).toEqual('-1');
        });

        it('addRecord() should automatically assign an ID to a new row with an undefined id field', () => {
            const newData = { _id: undefined, anyField: 'my new value', someOtherField: 1234 };
            const updatedData = collectionValue.addRecord({ recordData: newData });

            expect(updatedData._id).toEqual('-1');
        });

        it('addRecord() should automatically assign an ID to a new row with an undefined id field', () => {
            const newData = { _id: undefined, anyField: 'my new value', someOtherField: 1234 };
            const updatedData = collectionValue.addRecord({ recordData: newData });

            expect(updatedData._id).toEqual('-1');
        });

        it('setRecordValue() should retain of the ADDED status if the row was not saved yet', () => {
            const newData = { _id: '4', anyField: 'my new value', someOtherField: 1234 };
            const createdRow = collectionValue.addRecord({ recordData: newData });

            expect(collectionValue.db.findOne({ id: '4', cleanMetadata: false })?.__action).toEqual(
                RecordActionType.ADDED,
            );
            collectionValue.setRecordValue({
                recordData: { ...createdRow, anyField: 'Some new value' },
                shouldNotifySubscribers: false,
            });
            const recordFromCollection = collectionValue.db.findOne({ id: newData._id, cleanMetadata: false });
            expect(recordFromCollection?.__action).toEqual(RecordActionType.ADDED);
        });

        it('removeRecord() should keep record in the database and mark it removed', () => {
            const recordFromCollection = collectionValue.db.findOne({ id: '3', cleanMetadata: false });
            expect(recordFromCollection?.__action).not.toEqual(RecordActionType.REMOVED);
            expect(recordFromCollection?.__dirty).toEqual(false);
            collectionValue.removeRecord({ recordId: '3' });
            const recordFromCollection2 = collectionValue.db.findOne({ id: '3', cleanMetadata: false });
            expect(recordFromCollection2?.__action).toEqual(RecordActionType.REMOVED);
            expect(recordFromCollection2?.__dirty).toEqual(true);
        });

        it('removeRecord() should throw if the record does not exist', () => {
            expect(() => collectionValue.removeRecord({ recordId: '1232' })).toThrow(
                "No record found with ID '1232' in the collection value, therefore we cannot execute the removal. Are you sure you use a valid ID?",
            );
        });

        it('removeRecord() should keep record in the database and mark it removed', () => {
            collectionValue.removeRecord({ recordId: '1' });

            const newModifiedData = { _id: '2', anyField: 'my new value', someOtherField: 1234 };
            collectionValue.setRecordValue({ recordData: newModifiedData, shouldNotifySubscribers: false });

            const newFieldValue = 'my new test content';
            collectionValue.setCellValue({ recordId: '3', columnId: 'anyField', value: newFieldValue });

            const newData = { _id: undefined, anyField: 'my new data', someOtherField: 12432134 };
            collectionValue.addRecord({ recordData: newData });

            const changedRecords = collectionValue.getNormalizedChangedRecords();

            expect(changedRecords).toEqual([
                { _id: '1', _action: RecordActionType.REMOVED },
                {
                    _action: RecordActionType.MODIFIED,
                    _id: '2',
                    anyField: 'my new value',
                    someOtherField: 1234,
                    multiDropDownField: ['good', 'great', 'bad'],
                },
                {
                    _action: RecordActionType.MODIFIED,
                    _id: '3',
                    anyField: 'my new test content',
                    someOtherField: 32432,
                },
                { _id: '-1', _action: RecordActionType.ADDED, anyField: 'my new data', someOtherField: 12432134 },
            ]);
        });

        it('getChangedRecords() should exclude fields that are not on the input type', () => {
            collectionValue.setCellValue({ recordId: '2', columnId: 'anyField', value: '9876' });
            collectionValue.setCellValue({ recordId: '3', columnId: 'testNonInputField', value: '3232' });
            collectionValue.setCellValue({ recordId: '3', columnId: 'anyField', value: 'my new value' });

            const changedRecords = collectionValue.getNormalizedChangedRecords();

            expect(changedRecords).toEqual([
                {
                    _action: RecordActionType.MODIFIED,
                    _id: '2',
                    anyField: '9876',
                    someOtherField: -5,
                    multiDropDownField: ['good', 'great', 'bad'],
                },
                { _action: RecordActionType.MODIFIED, _id: '3', anyField: 'my new value', someOtherField: 32432 },
            ]);
        });

        describe('nested grid', () => {
            beforeEach(() => {
                collectionValue = new TestCollectionValue({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    levelMap: { 0: 'child' as const, 1: undefined },
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                            nestedFields.numeric({ bind: 'someOtherField' }),
                        ],
                        [
                            nestedFields.text<any, any>({ bind: 'anyField2' }),
                            nestedFields.text<any, any>({ bind: 'someOtherField2' }),
                        ],
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField3' }),
                            nestedFields.text<any, any>({ bind: 'someOtherField3' }),
                        ],
                    ],
                    nodeTypes: {
                        AnyNode: {
                            name: 'AnyNode',
                            title: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: {
                                    type: GraphQLTypes.IntOrString,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                _sortValue: {
                                    type: GraphQLTypes.IntOrString,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                _action: {
                                    type: GraphQLTypes.Enum,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                anyField: {
                                    type: GraphQLTypes.String,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                someOtherField: {
                                    type: GraphQLTypes.Int,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                children: {
                                    type: 'ChildNode',
                                    kind: GraphQLKind.List,
                                    isCollection: true,
                                    isMutable: true,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                            },
                            mutations: {},
                        },
                        ChildNode: {
                            name: 'ChildNode',
                            title: 'ChildNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: {
                                    type: GraphQLTypes.IntOrString,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                _sortValue: {
                                    type: GraphQLTypes.IntOrString,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                _action: {
                                    type: GraphQLTypes.Enum,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                anyField2: {
                                    type: GraphQLTypes.String,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                someOtherField2: {
                                    type: GraphQLTypes.Int,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                children: {
                                    type: 'SecondChildNode',
                                    kind: GraphQLKind.List,
                                    isCollection: true,
                                    isMutable: true,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                            },
                            mutations: {},
                        },
                        SecondChildNode: {
                            name: 'SecondChildNode',
                            title: 'SecondChildNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: {
                                    type: GraphQLTypes.IntOrString,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                _sortValue: {
                                    type: GraphQLTypes.IntOrString,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                _action: {
                                    type: GraphQLTypes.Enum,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                anyField3: {
                                    type: GraphQLTypes.String,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                                someOtherField3: {
                                    type: GraphQLTypes.Int,
                                    kind: GraphQLKind.Scalar,
                                    isOnInputType: true,
                                    canFilter: true,
                                },
                            },
                            mutations: {},
                        },
                    },
                    nodes: [
                        '@sage/xtrem-test/AnyNode',
                        '@sage/xtrem-test/ChildNode',
                        '@sage/xtrem-test/SecondChildNode',
                    ],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test', someOtherField: 4 },
                        { _id: '2', anyField: 'test string 2', someOtherField: -5 },
                        { _id: '3', anyField: 'test aasd', someOtherField: 32432 },
                        { _id: '1', __parentId: '2', __level: 1, anyField2: 'child record 1', someOtherField2: -5 },
                        { _id: '2', __parentId: '2', __level: 1, anyField2: 'child record 2', someOtherField2: -5 },
                        {
                            _id: '1',
                            __parentId: '1',
                            __level: 2,
                            anyField3: 'second child record 3',
                            someOtherField2: -5,
                        },
                        {
                            _id: '2',
                            __parentId: '1',
                            __level: 2,
                            anyField3: 'second child record 3',
                            someOtherField2: -5,
                        },
                        { _id: '90', __parentId: '1', __level: 1, anyField2: 'asdasd', someOtherField2: -5 },
                        { _id: '43', __parentId: '90', __level: 2, anyField3: 'asdasd', someOtherField2: -5 },
                    ],
                    fieldType: CollectionFieldTypes.NESTED_GRID,
                });
            });

            it('should remove bottom level child record', () => {
                expect(
                    collectionValue.db.findOne({ id: '2', level: 2, parentId: '1', cleanMetadata: false }).__action,
                ).toBeUndefined();
                collectionValue.removeRecord({ recordId: '2', level: 2 });
                expect(
                    collectionValue.db.findOne({ id: '2', level: 2, parentId: '1', cleanMetadata: false }).__action,
                ).toEqual(RecordActionType.REMOVED);
            });

            it('should remove middle level record and its children', () => {
                expect(
                    collectionValue.db.findOne({ id: '1', level: 1, parentId: '2', cleanMetadata: false }).__action,
                ).toBeUndefined();
                const children = collectionValue.db
                    .findAll({ level: 2, parentId: '1' })
                    .value({ cleanMetadata: false });
                expect(children).toHaveLength(2);
                expect(children[0].__action).toBeUndefined();
                expect(children[1].__action).toBeUndefined();
                collectionValue.removeRecord({ recordId: '1', level: 1 });
                expect(
                    collectionValue.db.findOne({ id: '1', level: 1, parentId: '2', cleanMetadata: false }).__action,
                ).toEqual(RecordActionType.REMOVED);
                expect(
                    collectionValue.db.findOne({
                        id: children[0]._id,
                        level: children[0].__level,
                        parentId: children[0].__parentId,
                        cleanMetadata: false,
                    }).__action,
                ).toEqual(RecordActionType.REMOVED);
                expect(
                    collectionValue.db.findOne({
                        id: children[1]._id,
                        level: children[1].__level,
                        parentId: children[1].__parentId,
                        cleanMetadata: false,
                    }).__action,
                ).toEqual(RecordActionType.REMOVED);
            });
            it('should remove top level record and all of its children recursively', () => {
                expect(collectionValue.db.findOne({ id: '2', cleanMetadata: false }).__action).toBeUndefined();
                const children = collectionValue.db
                    .findAll({ level: 1, parentId: '2' })
                    .value({ cleanMetadata: false });
                expect(children).toHaveLength(2);
                expect(children[0].__action).toBeUndefined();
                expect(children[1].__action).toBeUndefined();
                const grandChildren = collectionValue.db
                    .findAll({ level: 2, parentId: '1' })
                    .value({ cleanMetadata: false });
                expect(grandChildren).toHaveLength(2);
                expect(grandChildren[0].__action).toBeUndefined();
                expect(grandChildren[1].__action).toBeUndefined();
                collectionValue.removeRecord({ recordId: '2' });
                expect(
                    collectionValue.db.findOne({ id: '1', level: 1, parentId: '2', cleanMetadata: false }).__action,
                ).toEqual(RecordActionType.REMOVED);
                expect(
                    collectionValue.db.findOne({
                        id: grandChildren[0]._id,
                        level: grandChildren[0].__level,
                        parentId: grandChildren[0].__parentId,
                        cleanMetadata: false,
                    }).__action,
                ).toEqual(RecordActionType.REMOVED);
                expect(
                    collectionValue.db.findOne({
                        id: grandChildren[1]._id,
                        level: grandChildren[1].__level,
                        parentId: grandChildren[1].__parentId,
                        cleanMetadata: false,
                    }).__action,
                ).toEqual(RecordActionType.REMOVED);
                expect(
                    collectionValue.db.findOne({
                        id: children[0]._id,
                        level: children[0].__level,
                        parentId: children[0].__parentId,
                        cleanMetadata: false,
                    }).__action,
                ).toEqual(RecordActionType.REMOVED);
                expect(
                    collectionValue.db.findOne({
                        id: children[1]._id,
                        level: children[1].__level,
                        parentId: children[1].__parentId,
                        cleanMetadata: false,
                    }).__action,
                ).toEqual(RecordActionType.REMOVED);
            });

            it('should remove top level record but leave alone any record that is not in its inheritance tree', () => {
                expect(
                    collectionValue.db.findOne({ id: '90', parentId: '1', level: 1, cleanMetadata: false }).__action,
                ).toBeUndefined();
                expect(
                    collectionValue.db.findOne({ id: '2', parentId: '1', level: 2, cleanMetadata: false }).__action,
                ).toBeUndefined();
                collectionValue.removeRecord({ recordId: '2' });
                expect(
                    collectionValue.db.findOne({ id: '90', parentId: '1', level: 1, cleanMetadata: false }).__action,
                ).toBeUndefined();
                expect(
                    collectionValue.db.findOne({ id: '43', parentId: '90', level: 2, cleanMetadata: false }).__action,
                ).toBeUndefined();
            });

            describe('newly added records', () => {
                beforeEach(() => {
                    collectionValue = new TestCollectionValue({
                        screenId,
                        elementId: fieldId,
                        isTransient: false,
                        hasNextPage: true,
                        orderBy: [{ anyField: 1 }],
                        levelMap: { 0: 'child' as const, 1: undefined },
                        columnDefinitions: [
                            [
                                nestedFields.text<any, any>({ bind: '_id' }),
                                nestedFields.text<any, any>({ bind: 'anyField' }),
                                nestedFields.numeric({ bind: 'someOtherField' }),
                            ],
                            [
                                nestedFields.text<any, any>({ bind: 'anyField2' }),
                                nestedFields.text<any, any>({ bind: 'someOtherField2' }),
                            ],
                            [
                                nestedFields.text<any, any>({ bind: '_id' }),
                                nestedFields.text<any, any>({ bind: 'anyField3' }),
                                nestedFields.text<any, any>({ bind: 'someOtherField3' }),
                            ],
                        ],
                        nodeTypes: {
                            AnyNode: {
                                name: 'AnyNode',
                                title: 'AnyNode',
                                packageName: '@sage/xtrem-test',
                                properties: {
                                    _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                    _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                    _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                    anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                    someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                    children: { type: 'ChildNode', kind: 'LIST' },
                                },
                                mutations: {},
                            },
                            ChildNode: {
                                name: 'ChildNode',
                                title: 'ChildNode',
                                packageName: '@sage/xtrem-test',
                                properties: {
                                    _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                    _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                    _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                    anyField2: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                    someOtherField2: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                    children: { type: 'SecondChildNode', kind: 'LIST' },
                                },
                                mutations: {},
                            },
                            SecondChildNode: {
                                name: 'SecondChildNode',
                                title: 'SecondChildNode',
                                packageName: '@sage/xtrem-test',
                                properties: {
                                    _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                    _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                    _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                    anyField3: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                    someOtherField3: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                },
                                mutations: {},
                            },
                        },
                        nodes: [
                            '@sage/xtrem-test/AnyNode',
                            '@sage/xtrem-test/ChildNode',
                            '@sage/xtrem-test/SecondChildNode',
                        ],
                        filter: [undefined],
                        initialValues: [
                            { _id: '1', __action: RecordActionType.ADDED, anyField: 'test', someOtherField: 4 },
                            {
                                _id: '2',
                                __action: RecordActionType.ADDED,
                                anyField: 'test string 2',
                                someOtherField: -5,
                            },
                            {
                                _id: '3',
                                __action: RecordActionType.ADDED,
                                anyField: 'test aasd',
                                someOtherField: 32432,
                            },
                            {
                                _id: '1',
                                __parentId: '2',
                                __level: 1,
                                __action: RecordActionType.ADDED,
                                anyField2: 'child record 1',
                                someOtherField2: -5,
                            },
                            {
                                _id: '2',
                                __parentId: '2',
                                __level: 1,
                                __action: RecordActionType.ADDED,
                                anyField2: 'child record 2',
                                someOtherField2: -5,
                            },
                            {
                                _id: '1',
                                __parentId: '1',
                                __level: 2,
                                __action: RecordActionType.ADDED,
                                anyField3: 'second child record 3',
                                someOtherField2: -5,
                            },
                            {
                                _id: '2',
                                __parentId: '1',
                                __level: 2,
                                __action: RecordActionType.ADDED,
                                anyField3: 'second child record 3',
                                someOtherField2: -5,
                            },
                            {
                                _id: '90',
                                __parentId: '1',
                                __level: 1,
                                __action: RecordActionType.ADDED,
                                anyField2: 'asdasd',
                                someOtherField2: -5,
                            },
                            {
                                _id: '43',
                                __parentId: '90',
                                __level: 2,
                                __action: RecordActionType.ADDED,
                                anyField3: 'asdasd',
                                someOtherField2: -5,
                            },
                        ],
                        fieldType: CollectionFieldTypes.NESTED_GRID,
                    });
                });

                it('should remove bottom level child record', () => {
                    expect(
                        collectionValue.db.findOne({ id: '2', level: 2, parentId: '1', cleanMetadata: false }),
                    ).not.toBeNull();
                    collectionValue.removeRecord({ recordId: '2', level: 2 });
                    expect(
                        collectionValue.db.findOne({ id: '2', level: 2, parentId: '1', cleanMetadata: false }),
                    ).toBeNull();
                });

                it('should remove middle level record and its children', () => {
                    expect(
                        collectionValue.db.findOne({ id: '1', level: 1, parentId: '2', cleanMetadata: false }),
                    ).not.toBeNull();
                    expect(
                        collectionValue.db.findAll({ level: 2, parentId: '1' }).value({ cleanMetadata: false }),
                    ).toHaveLength(2);
                    collectionValue.removeRecord({ recordId: '1', level: 1 });
                    expect(
                        collectionValue.db.findOne({ id: '1', level: 1, parentId: '2', cleanMetadata: false }),
                    ).toBeNull();
                    expect(
                        collectionValue.db.findAll({ level: 2, parentId: '1' }).value({ cleanMetadata: false }),
                    ).toHaveLength(0);
                });
                it('should remove top level record and all of its children recursively', () => {
                    expect(collectionValue.db.findOne({ id: '2', cleanMetadata: false })).not.toBeNull();
                    expect(
                        collectionValue.db.findAll({ level: 1, parentId: '2' }).value({ cleanMetadata: false }),
                    ).toHaveLength(2);
                    expect(
                        collectionValue.db.findAll({ level: 2, parentId: '1' }).value({ cleanMetadata: false }),
                    ).toHaveLength(2);
                    collectionValue.removeRecord({ recordId: '2' });
                    expect(
                        collectionValue.db.findOne({ id: '1', level: 1, parentId: '2', cleanMetadata: false }),
                    ).toBeNull();
                    expect(
                        collectionValue.db.findAll({ level: 1, parentId: '2' }).value({ cleanMetadata: false }),
                    ).toHaveLength(0);
                    expect(
                        collectionValue.db.findAll({ level: 2, parentId: '1' }).value({ cleanMetadata: false }),
                    ).toHaveLength(0);
                });

                it('should remove top level record but leave alone any record that is not in its inheritance tree', () => {
                    expect(
                        collectionValue.db.findOne({ id: '90', parentId: '1', level: 1, cleanMetadata: false }),
                    ).not.toBeNull();
                    expect(
                        collectionValue.db.findOne({ id: '2', parentId: '1', level: 2, cleanMetadata: false }),
                    ).not.toBeNull();
                    collectionValue.removeRecord({ recordId: '2' });
                    expect(
                        collectionValue.db.findOne({ id: '90', parentId: '1', level: 1, cleanMetadata: false }),
                    ).not.toBeNull();
                    expect(
                        collectionValue.db.findOne({ id: '43', parentId: '90', level: 2, cleanMetadata: false }),
                    ).not.toBeNull();
                });
            });
        });

        describe('filters', () => {
            let collectionValue2: TestCollectionValue<Node>;
            let fetchCollectionDataMock: any;
            let testFilter: (filter: any) => any;
            beforeEach(() => {
                collectionValue2 = new TestCollectionValue({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                            nestedFields.numeric({ bind: 'someOtherField' }),
                            nestedFields.numeric({ bind: 'decimalField', scale: 2 }),
                            nestedFields.date({ bind: 'dateField' }),
                        ],
                    ],
                    nodeTypes: {
                        AnyNode: {
                            title: 'Any Node',
                            name: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                decimalField: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
                                dateField: { type: 'Datetime', kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    },
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test', someOtherField: 4, decimalField: 1.11, dateField: '2020-12-31' },
                        {
                            _id: '2',
                            anyField: 'test string 2',
                            someOtherField: -5,
                            decimalField: -2.22,
                            dateField: '2020-12-31',
                        },
                        {
                            _id: '3',
                            anyField: 'test aasd',
                            someOtherField: 32432,
                            decimalField: 3.33,
                            dateField: '2020-12-30',
                        },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                });
                testFilter = async (filter: any) => {
                    return collectionValue2.getPage({
                        tableFieldProperties: {
                            columns: [
                                {
                                    defaultUiProperties: { canFilter: true },
                                    properties: { bind: '_id', canFilter: true },
                                    type: FieldKey.Text,
                                },
                                {
                                    defaultUiProperties: { canFilter: true },
                                    properties: { bind: 'anyField', canFilter: true },
                                    type: FieldKey.Text,
                                },
                                {
                                    defaultUiProperties: { canFilter: true },
                                    properties: { bind: 'someOtherField', canFilter: true },
                                    type: FieldKey.Numeric,
                                },
                                {
                                    defaultUiProperties: { canFilter: true },
                                    properties: { bind: 'decimalField', canFilter: true },
                                    type: FieldKey.Numeric,
                                },
                                {
                                    defaultUiProperties: { canFilter: true },
                                    properties: { bind: 'dateField', canFilter: true },
                                    type: FieldKey.Date,
                                },
                            ] as any,
                        },
                        filters: filter,
                        orderBy: { id: 1 },
                        cursor: 'CURSOR',
                    });
                };

                fetchCollectionDataMock = jest.spyOn(graphQLService, 'fetchCollectionData').mockImplementation(() => {
                    return new Promise(resolve => {
                        process.nextTick(() =>
                            resolve({
                                pageInfo: { endCursor: false, endPage: false },
                                data: [
                                    {
                                        _id: '4',
                                        anyField: '4 test',
                                        someOtherField: 400,
                                        decimalField: '44.44',
                                        dateField: '2020-12-31',
                                    },
                                    {
                                        _id: '5',
                                        anyField: 'test string 3',
                                        someOtherField: 55555,
                                        decimalField: '555.55',
                                        dateField: '2020-12-29',
                                    },
                                ],
                            }),
                        );
                    });
                });
            });

            afterEach(() => {
                fetchCollectionDataMock.mockClear();
            });
            it('contains filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: '_id',
                        value: [{ filterType: { value: 'contains' }, filterValue: '4' }],
                    },
                ]);
                expect(test).toHaveLength(1);
                expect(test[0]._id).toBe('4');
            });
            it('startsWith filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'anyField',
                        value: [{ filterType: { value: 'startsWith' }, filterValue: 'test' }],
                    },
                ]);
                expect(test).toHaveLength(4);
                expect(test.map((v: any) => v.anyField)).toStrictEqual([
                    'test',
                    'test string 2',
                    'test aasd',
                    'test string 3',
                ]);
            });
            it('endsWith filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'anyField',
                        value: [{ filterType: { value: 'endsWith' }, filterValue: 'test' }],
                    },
                ]);
                expect(test).toHaveLength(2);
                expect(test.map((v: any) => v.anyField)).toStrictEqual(['test', '4 test']);
            });
            it('equal (integer) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'someOtherField',
                        value: [{ filterType: { value: 'equals' }, filterValue: -5 }],
                    },
                ]);
                expect(test).toHaveLength(1);
                expect(test.map((v: any) => v.someOtherField)).toStrictEqual([-5]);
            });
            it('equal (decimal) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'decimalField',
                        value: [{ filterType: { value: 'equals' }, filterValue: 555.55 }],
                    },
                ]);
                expect(test).toHaveLength(1);
                expect(test.map((v: any) => v.decimalField)).toStrictEqual([555.55]);
            });
            it('equal (date) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'dateField',
                        value: [{ filterType: { value: 'equals' }, filterValue: '2020-12-31' }],
                    },
                ]);
                expect(test).toHaveLength(3);
                expect(test.map((v: any) => v.dateField)).toStrictEqual(['2020-12-31', '2020-12-31', '2020-12-31']);
            });
            it('notEqual (integer) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'someOtherField',
                        value: [{ filterType: { value: 'notEqual' }, filterValue: 400 }],
                    },
                ]);
                expect(test).toHaveLength(4);
                expect(test.map((v: any) => v.someOtherField)).toStrictEqual([4, -5, 32432, 55555]);
            });
            it('notEqual (decimal) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'decimalField',
                        value: [{ filterType: { value: 'notEqual' }, filterValue: 1.11 }],
                    },
                ]);
                expect(test).toHaveLength(4);
                expect(test.map((v: any) => v.decimalField)).toStrictEqual([-2.22, 3.33, 44.44, 555.55]);
            });
            it('notEqual (date) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'dateField',
                        value: [{ filterType: { value: 'notEqual' }, filterValue: '2020-12-31' }],
                    },
                ]);
                expect(test).toHaveLength(2);
                expect(test.map((v: any) => v.dateField)).toStrictEqual(['2020-12-30', '2020-12-29']);
            });
            it('greaterThanOrEqual (integer) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'someOtherField',
                        value: [{ filterType: { value: 'greaterThanOrEqual' }, filterValue: 0 }],
                    },
                ]);
                expect(test).toHaveLength(4);
                expect(test.map((v: any) => v.someOtherField)).toStrictEqual([4, 32432, 400, 55555]);
            });
            it('greaterThanOrEqual (decimal) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'decimalField',
                        value: [{ filterType: { value: 'greaterThanOrEqual' }, filterValue: 3.3 }],
                    },
                ]);
                expect(test).toHaveLength(3);
                expect(test.map((v: any) => v.decimalField)).toStrictEqual([3.33, 44.44, 555.55]);
            });
            it('greaterThanOrEqual (date) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'dateField',
                        value: [{ filterType: { value: 'greaterThanOrEqual' }, filterValue: '2020-12-30' }],
                    },
                ]);
                expect(test).toHaveLength(4);
                expect(test.map((v: any) => v.dateField)).toStrictEqual([
                    '2020-12-31',
                    '2020-12-31',
                    '2020-12-30',
                    '2020-12-31',
                ]);
            });
            it('lessThanOrEqual (integer) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'someOtherField',
                        value: [{ filterType: { value: 'lessThanOrEqual' }, filterValue: 4 }],
                    },
                ]);
                expect(test).toHaveLength(2);
                expect(test.map((v: any) => v.someOtherField)).toStrictEqual([4, -5]);
            });
            it('lessThanOrEqual (decimal) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'decimalField',
                        value: [{ filterType: { value: 'lessThanOrEqual' }, filterValue: -2.23 }],
                    },
                ]);
                expect(test).toHaveLength(0);
            });
            it('lessThanOrEqual (date) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'dateField',
                        value: [{ filterType: { value: 'lessThanOrEqual' }, filterValue: '2020-12-29' }],
                    },
                ]);
                expect(test).toHaveLength(1);
                expect(test.map((v: any) => v.dateField)).toStrictEqual(['2020-12-29']);
            });
            it('inRange (integer) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'someOtherField',
                        value: [{ filterType: { value: 'inRange' }, filterValue: '32431~1000000' }],
                    },
                ]);
                expect(test).toHaveLength(2);
                expect(test.map((v: any) => v.someOtherField)).toStrictEqual([32432, 55555]);
            });
            it('inRange (decimal) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'decimalField',
                        value: [{ filterType: { value: 'inRange' }, filterValue: '1~3.33' }],
                    },
                ]);
                expect(test).toHaveLength(2);
                expect(test.map((v: any) => v.decimalField)).toStrictEqual([1.11, 3.33]);
            });
            it('inRange (date) filter should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'dateField',
                        value: [{ filterType: { value: 'inRange' }, filterValue: '2020-12-29~2020-12-30' }],
                    },
                ]);
                expect(test).toHaveLength(2);
                expect(test.map((v: any) => v._id)).toStrictEqual(['3', '5']);
            });
            it('or operator should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'someOtherField',
                        value: [
                            { filterType: { value: 'equals' }, filterValue: 4 },
                            { filterType: { value: 'equals' }, filterValue: -5 },
                        ],
                    },
                ]);
                expect(test).toHaveLength(2);
                expect(test.map((v: any) => v.someOtherField)).toStrictEqual([4, -5]);
            });
            it('and operator should apply', async () => {
                const test = await testFilter([
                    {
                        id: 'someOtherField',
                        value: [{ filterType: { value: 'equals' }, filterValue: 4 }],
                    },
                    {
                        id: 'decimalField',
                        value: [{ filterType: { value: 'equals' }, filterValue: 1.11 }],
                    },
                ]);
                expect(test).toHaveLength(1);
                expect(test.map((v: any) => v._id)).toStrictEqual(['1']);
            });

            it('should not reuse the initial filter if no filter is passed in to getPage call for reference lookup', async () => {
                const fetchReferenceFieldDataMock = jest
                    .spyOn(graphQLService, 'fetchReferenceFieldData')
                    .mockResolvedValue({ query: { edges: [] } });

                collectionValue2 = new TestCollectionValue({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                            nestedFields.numeric({ bind: 'someOtherField' }),
                            nestedFields.numeric({ bind: 'decimalField', scale: 2 }),
                            nestedFields.date({ bind: 'dateField' }),
                        ],
                    ],
                    nodeTypes: {
                        AnyNode: {
                            title: 'AnyNode',
                            name: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                decimalField: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
                                dateField: { type: 'Datetime', kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    },
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [{ _or: [{ stuff: { _le: 4 } }] }],
                    initialValues: [
                        { _id: '1', anyField: 'test', someOtherField: 4, decimalField: 1.11, dateField: '2020-12-31' },
                        {
                            _id: '2',
                            anyField: 'test string 2',
                            someOtherField: -5,
                            decimalField: -2.22,
                            dateField: '2020-12-31',
                        },
                        {
                            _id: '3',
                            anyField: 'test aasd',
                            someOtherField: 32432,
                            decimalField: 3.33,
                            dateField: '2020-12-30',
                        },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                });
                testFilter = async () => {
                    return collectionValue2.getPage({
                        tableFieldProperties: {
                            valueField: 'anyField',
                            columns: [
                                {
                                    defaultUiProperties: { canFilter: true },
                                    properties: { bind: '_id', canFilter: true },
                                    type: FieldKey.Text,
                                },
                                {
                                    defaultUiProperties: { canFilter: true },
                                    properties: { bind: 'anyField', canFilter: true },
                                    type: FieldKey.Text,
                                },
                                {
                                    defaultUiProperties: { canFilter: true },
                                    properties: { bind: 'someOtherField', canFilter: true },
                                    type: FieldKey.Numeric,
                                },
                                {
                                    defaultUiProperties: { canFilter: true },
                                    properties: { bind: 'decimalField', canFilter: true },
                                    type: FieldKey.Numeric,
                                },
                                {
                                    defaultUiProperties: { canFilter: true },
                                    properties: { bind: 'dateField', canFilter: true },
                                    type: FieldKey.Date,
                                },
                            ],
                        } as any,
                        filters: undefined,
                        orderBy: { _id: 1 },
                    });
                };

                expect(fetchReferenceFieldDataMock).not.toHaveBeenCalled();

                await testFilter(undefined);

                expect(fetchReferenceFieldDataMock).toHaveBeenCalledWith(
                    expect.objectContaining({ filter: {}, after: undefined }),
                );
            });
        });

        it('setCellValue() should update a record in the database and pass it through the value formatter', () => {
            const newFieldValue = '13.46';
            collectionValue.setCellValue({ recordId: '2', columnId: 'someOtherField', value: newFieldValue });
            const recordFromCollection = collectionValue.db.findOne({ id: '2' });
            expect(recordFromCollection?.someOtherField).toEqual(13);
        });

        it('setCellValue() should fetch default values if argument is true and send only dirtyValues to it', async () => {
            collectionValue.addRecord({ recordData: { anyField: 'new record', someOtherField: 123 } });
            const mockedFetchDefault = jest.spyOn(graphQLService, 'fetchNestedDefaultValues').mockResolvedValue({
                nestedDefaults: {
                    someOtherField: '2',
                    anyField: 'default value server',
                    id: '-1',
                },
            });
            const newFieldValue = '2';
            await collectionValue.setCellValue({
                recordId: '-1',
                columnId: 'someOtherField',
                value: newFieldValue,
                shouldFetchDefault: true,
                isOrganicChange: true,
            });
            const recordFromCollection = collectionValue.db.findOne({ id: '-1' });
            expect(recordFromCollection?.someOtherField).toEqual(2);
            expect(recordFromCollection?.anyField).toEqual('default value server');
            expect(mockedFetchDefault).toHaveBeenCalledWith({
                screenId,
                elementId: fieldId,
                recordId: '-1',
                data: { someOtherField: 2 },
                isNewRow: true,
            });
        });

        describe('non-transient collection', () => {
            let collectionValue3: TestCollectionValue<Node>;
            getMockStore({
                screenDefinitions: {
                    [screenId]: {
                        selectedRecordId: '123',
                        metadata: {
                            uiComponentProperties: {
                                [screenId]: {
                                    node: '@sage/xtrem-test/AnyNode',
                                    title: 'page',
                                    isTransient: false,
                                },
                                [fieldId]: {
                                    title: 'field',
                                    isTransient: false,
                                    columns: [
                                        { properties: { bind: '_id' }, type: FieldKey.Text },
                                        { properties: { bind: 'anyField' }, type: FieldKey.Text },
                                        { properties: { bind: 'someOtherField' }, type: FieldKey.Numeric },
                                    ],
                                },
                            },
                        },
                    },
                    [fieldId]: {
                        title: 'field',
                        isTransient: false,
                    },
                },
            } as any);
            beforeEach(() => {
                collectionValue3 = new TestCollectionValue<Node>({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    columnDefinitions: [
                        [
                            nestedFields.text<any, Node>({ bind: '_id' }),
                            nestedFields.text<any, Node>({ bind: 'anyField' }),
                            nestedFields.numeric<any, Node>({ bind: 'someOtherField' }),
                            nestedFields.text<any, Node>({ bind: 'aThridField' }),
                        ],
                    ],
                    nodeTypes: {
                        AnyNode: {
                            name: 'AnyNode',
                            title: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                decimalField: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
                                dateField: { type: 'Datetime', kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    },
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test', someOtherField: 4 },
                        { _id: '2', anyField: 'test string 2', someOtherField: -5 },
                        { _id: '3', anyField: 'test aasd', someOtherField: 32432 },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                });
            });
            describe('getPage method', () => {
                it('Should refetch if forceRefetch is true and then set to false', async () => {
                    const mockfn = jest.fn();
                    jest.spyOn(graphQLService, 'fetchCollectionData').mockImplementation(mockfn);
                    collectionValue3.cleanCollectionData(); // This will set forceRefetch to true
                    const tableFieldProperties: CollectionValueFieldPropertiesWithAdditionalRecords<ScreenBase> = {
                        bind: fieldId,
                        columns: [],
                    };
                    await collectionValue3.getPage({ tableFieldProperties, searchText: '', cursor: 'CURSOR' });
                    expect(mockfn).toHaveBeenCalled();
                });

                it('Should refetch if the orderBy has changed', async () => {
                    const mockfn = jest.fn();
                    jest.spyOn(graphQLService, 'fetchCollectionData').mockImplementation(mockfn);
                    const tableFieldProperties: CollectionValueFieldPropertiesWithAdditionalRecords<ScreenBase> = {
                        bind: fieldId,
                        columns: [],
                    };
                    const orderBy: Dict<1 | -1> = { col1: 1 };
                    await collectionValue3.getPage({ tableFieldProperties, orderBy, searchText: '', cursor: 'CURSOR' });
                    expect(mockfn).toHaveBeenCalled();
                });

                it('Should refetch if the filter has changed', async () => {
                    const mockfn = jest.fn();
                    jest.spyOn(graphQLService, 'fetchCollectionData').mockImplementation(mockfn);
                    const tableFieldProperties: CollectionValueFieldPropertiesWithAdditionalRecords<ScreenBase> = {
                        bind: fieldId,
                        columns: [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                            nestedFields.numeric<any, any>({ bind: 'someOtherField' }),
                        ],
                    };
                    const filters: Filter<FiltrableType>[] = [
                        {
                            id: 'anyField',
                            value: [
                                {
                                    filterType: { text: 'patata', value: 'contains' },
                                    filterValue: 'patata2',
                                },
                            ],
                        },
                    ];
                    await collectionValue3.getPage({ tableFieldProperties, filters, searchText: '', cursor: 'CURSOR' });
                    expect(mockfn).toHaveBeenCalled();
                });

                describe('temporary records', () => {
                    describe('getPage', () => {
                        it('should include temporary records in the record set', async () => {
                            jest.spyOn(graphQLService, 'fetchCollectionData').mockResolvedValue({
                                data: [{ _id: '4', anyField: 'test server', someOtherField: 5 }],
                                pageInfo: { hasNextPage: false },
                            });
                            const tableFieldProperties: CollectionValueFieldPropertiesWithAdditionalRecords<ScreenBase> =
                                {
                                    bind: fieldId,
                                    additionalLookupRecords: () => [
                                        { _id: '-1', anyField: 'test local record 1 asd', someOtherField: 15 },
                                        { _id: '-2', anyField: 'test local record 2', someOtherField: 15 },
                                    ],
                                    columns: [
                                        nestedFields.text<any, any>({ bind: '_id' }),
                                        nestedFields.text<any, any>({ bind: 'anyField' }),
                                        nestedFields.numeric<any, any>({ bind: 'someOtherField' }),
                                    ],
                                };
                            const filters: Filter<FiltrableType>[] = [
                                {
                                    id: 'anyField',
                                    value: [
                                        {
                                            filterType: { text: 'test', value: 'contains' },
                                            filterValue: 'test',
                                        },
                                    ],
                                },
                            ];

                            const page = await collectionValue3.getPage({
                                tableFieldProperties,
                                filters,
                                searchText: '',
                                cursor: 'CURSOR',
                            });
                            expect(page).toEqual([
                                {
                                    _id: '1',
                                    anyField: 'test',
                                    someOtherField: 4,
                                },
                                {
                                    _id: '3',
                                    anyField: 'test aasd',
                                    someOtherField: 32432,
                                },
                                {
                                    _id: '-1',
                                    anyField: 'test local record 1 asd',
                                    someOtherField: 15,
                                },
                                {
                                    _id: '-2',
                                    anyField: 'test local record 2',
                                    someOtherField: 15,
                                },
                                {
                                    _id: '4',
                                    anyField: 'test server',
                                    someOtherField: 5,
                                },
                                {
                                    _id: '2',
                                    anyField: 'test string 2',
                                    someOtherField: -5,
                                },
                            ]);
                        });

                        it('should include temporary records in the record set and sort them', async () => {
                            jest.spyOn(graphQLService, 'fetchCollectionData').mockResolvedValue({
                                data: [{ _id: '4', anyField: 'test server', someOtherField: 5 }],
                                pageInfo: { hasNextPage: false },
                            });
                            const tableFieldProperties: CollectionValueFieldPropertiesWithAdditionalRecords<ScreenBase> =
                                {
                                    bind: fieldId,
                                    additionalLookupRecords: () => [
                                        { _id: '-1', anyField: 'test local record 1 asd', someOtherField: 15 },
                                        { _id: '-2', anyField: 'test local record 2', someOtherField: 15 },
                                    ],
                                    columns: [
                                        nestedFields.text<any, any>({ bind: '_id' }),
                                        nestedFields.text<any, any>({ bind: 'anyField' }),
                                        nestedFields.numeric<any, any>({ bind: 'someOtherField' }),
                                    ],
                                };
                            const filters: Filter<FiltrableType>[] = [
                                {
                                    id: 'anyField',
                                    value: [
                                        {
                                            filterType: { text: 'test', value: 'contains' },
                                            filterValue: 'test',
                                        },
                                    ],
                                },
                            ];

                            const page = await collectionValue3.getPage({
                                tableFieldProperties,
                                filters,
                                searchText: '',
                                cursor: 'CURSOR',
                                orderBy: { someOtherField: 1 },
                            });
                            expect(page).toEqual([
                                {
                                    _id: '2',
                                    anyField: 'test string 2',
                                    someOtherField: -5,
                                },
                                {
                                    _id: '1',
                                    anyField: 'test',
                                    someOtherField: 4,
                                },
                                {
                                    _id: '4',
                                    anyField: 'test server',
                                    someOtherField: 5,
                                },
                                {
                                    _id: '-2',
                                    anyField: 'test local record 2',
                                    someOtherField: 15,
                                },
                                {
                                    _id: '-1',
                                    anyField: 'test local record 1 asd',
                                    someOtherField: 15,
                                },
                                {
                                    _id: '3',
                                    anyField: 'test aasd',
                                    someOtherField: 32432,
                                },
                            ]);
                        });
                    });

                    it('should include temporary records in the record set and when filtered mix them in to the result set', async () => {
                        jest.spyOn(graphQLService, 'fetchCollectionData').mockResolvedValue({
                            data: [{ _id: '4', anyField: 'test server asd', someOtherField: 5 }],
                            pageInfo: { hasNextPage: false },
                        });
                        const tableFieldProperties: CollectionValueFieldPropertiesWithAdditionalRecords<ScreenBase> = {
                            bind: fieldId,
                            additionalLookupRecords: () => [
                                { _id: '-1', anyField: 'test local record 1 asd', someOtherField: 15 },
                                { _id: '-2', anyField: 'test local record 2', someOtherField: 15 },
                            ],
                            columns: [
                                nestedFields.text<any, any>({ bind: '_id' }),
                                nestedFields.text<any, any>({ bind: 'anyField' }),
                                nestedFields.numeric<any, any>({ bind: 'someOtherField' }),
                            ],
                        };
                        const filters: Filter<FiltrableType>[] = [
                            {
                                id: 'anyField',
                                value: [
                                    {
                                        filterType: { text: 'asd', value: 'contains' },
                                        filterValue: 'asd',
                                    },
                                ],
                            },
                        ];

                        const page = await collectionValue3.getPage({
                            tableFieldProperties,
                            filters,
                            searchText: '',
                            cursor: 'CURSOR',
                            orderBy: { someOtherField: 1 },
                        });
                        expect(page).toEqual([
                            {
                                _id: '4',
                                anyField: 'test server asd',
                                someOtherField: 5,
                            },
                            {
                                _id: '-1',
                                anyField: 'test local record 1 asd',
                                someOtherField: 15,
                            },
                            {
                                _id: '3',
                                anyField: 'test aasd',
                                someOtherField: 32432,
                            },
                        ]);
                    });

                    it('should remove temporary records from the collection right after the operation is finished', async () => {
                        jest.spyOn(graphQLService, 'fetchCollectionData').mockResolvedValue({
                            data: [],
                            pageInfo: { hasNextPage: false },
                        });
                        const tableFieldProperties: CollectionValueFieldPropertiesWithAdditionalRecords<ScreenBase> = {
                            bind: fieldId,
                            additionalLookupRecords: () => [
                                { _id: '-1', anyField: 'test local record 1 asd', someOtherField: 15 },
                                { _id: '-2', anyField: 'test local record 2', someOtherField: 15 },
                            ],
                            columns: [
                                nestedFields.text<any, any>({ bind: '_id' }),
                                nestedFields.text<any, any>({ bind: 'anyField' }),
                                nestedFields.numeric<any, any>({ bind: 'someOtherField' }),
                            ],
                        };

                        expect(collectionValue3.db.findAll().value()).toHaveLength(3);

                        const page = await collectionValue3.getPage({
                            tableFieldProperties,
                            searchText: '',
                            cursor: 'CURSOR',
                            orderBy: { someOtherField: 1 },
                        });
                        expect(page).toHaveLength(5);
                        expect(collectionValue3.db.findAll().value()).toHaveLength(3);
                    });
                });
            });

            describe('getData', () => {
                it('should include temporary records in the record set', () => {
                    const page = collectionValue3.getData({
                        temporaryRecords: [
                            { _id: '-1', anyField: 'test local record 1 asd', someOtherField: 15 },
                            { _id: '-2', anyField: 'test local record 2', someOtherField: 15 },
                        ],
                    });
                    expect(page).toEqual([
                        {
                            _id: '1',
                            anyField: 'test',
                            someOtherField: 4,
                        },
                        {
                            _id: '3',
                            anyField: 'test aasd',
                            someOtherField: 32432,
                        },
                        {
                            _id: '-1',
                            anyField: 'test local record 1 asd',
                            someOtherField: 15,
                        },
                        {
                            _id: '-2',
                            anyField: 'test local record 2',
                            someOtherField: 15,
                        },
                        {
                            _id: '2',
                            anyField: 'test string 2',
                            someOtherField: -5,
                        },
                    ]);
                });
            });

            it('should remove temporary records from the collection right after the operation is finished', () => {
                expect(collectionValue3.db.findAll().value()).toHaveLength(3);
                const page = collectionValue3.getData({
                    temporaryRecords: [
                        { _id: '-1', anyField: 'test local record 1 asd', someOtherField: 15 },
                        { _id: '-2', anyField: 'test local record 2', someOtherField: 15 },
                    ],
                });
                expect(page).toHaveLength(5);
                expect(collectionValue3.db.findAll().value()).toHaveLength(3);
            });
        });

        describe('validity state management', () => {
            beforeEach(() => {
                collectionValue = new TestCollectionValue({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    levelMap: { 0: 'child' as const, 1: undefined },
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id', isMandatory: true }),
                            nestedFields.text<any, any>({ bind: 'anyField', maxLength: 4 }),
                            nestedFields.numeric<any, any>({ bind: 'someOtherField', min: 2, max: 5 }),
                        ],
                        [nestedFields.text<any, any>({ bind: 'aThridField', isMandatory: true })],
                    ],
                    nodeTypes: {
                        AnyNode: {
                            name: 'AnyNode',
                            title: 'Any Node',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                child: { type: 'ChildNode', kind: 'LIST' },
                            },
                            mutations: {},
                        },
                        ChildNode: {
                            title: 'ChildNode',
                            name: 'ChildNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    },
                    nodes: ['@sage/xtrem-test/AnyNode', '@sage/xtrem-test/ChildNode'],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test', someOtherField: 4, aThridField: 'someContent' },
                        { _id: '2', anyField: 'test string 2', someOtherField: -5, aThridField: 'someContent' },
                        { _id: '3', anyField: 'test aasd', someOtherField: 32432, aThridField: 'someContent' },
                        { _id: '1', __parentId: '1', __level: 1, someOtherField: 'test aasd' },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                });
            });

            describe('validate function', () => {
                it('should not validate clean fields', async () => {
                    // Some fields would fail by default but because they are clean, they are excluded from the validation procedure
                    const result = await collectionValue.validate(false);
                    expect(result).toEqual([]);
                });

                it('should return a validation error', async () => {
                    collectionValue.setCellValue({ recordId: '2', columnId: 'anyField', value: 'a long text' });
                    const result = await collectionValue.validate(false);
                    expect(result).toEqual([
                        {
                            screenId,
                            elementId: fieldId,
                            columnId: 'anyField',
                            message: 'anyField maximum length is 4',
                            recordId: '2',
                            validationRule: 'maxLength',
                        },
                    ]);
                });

                it('should update validation errors after row removals', async () => {
                    collectionValue.setCellValue({ recordId: '2', columnId: 'anyField', value: 'a long text' });
                    const result1 = await collectionValue.validate(false);
                    expect(result1).toHaveLength(1);
                    collectionValue.removeRecord({ recordId: '2' });
                    const result2 = await collectionValue.validate(false);
                    expect(result2).toHaveLength(0);
                });

                it('should return multiple validation errors when more rows fail', async () => {
                    collectionValue.setCellValue({ recordId: '2', columnId: 'anyField', value: 'a long text' });
                    collectionValue.setCellValue({ recordId: '2', columnId: 'someOtherField', value: 1 });
                    const result = await collectionValue.validate(false);
                    expect(result).toEqual([
                        {
                            screenId,
                            elementId: fieldId,
                            columnId: 'anyField',
                            message: 'anyField maximum length is 4',
                            recordId: '2',
                            validationRule: 'maxLength',
                        },
                        {
                            screenId,
                            elementId: fieldId,
                            columnId: 'someOtherField',
                            message: 'someOtherField minimum value is 2',
                            recordId: '2',
                            validationRule: 'min',
                        },
                    ]);
                });

                it('should exclude deleted records from validation', async () => {
                    collectionValue.setCellValue({ recordId: '3', columnId: 'anyField', value: 'a long text' });
                    collectionValue.setCellValue({ recordId: '2', columnId: 'someOtherField', value: 1 });
                    collectionValue.removeRecord({ recordId: '3' });

                    const result = await collectionValue.validate(false);
                    expect(result).toEqual([
                        {
                            screenId,
                            elementId: fieldId,
                            columnId: 'someOtherField',
                            message: 'someOtherField minimum value is 2',
                            recordId: '2',
                            validationRule: 'min',
                        },
                    ]);
                });
            });

            describe('subscription for validation status changes', () => {
                let validationSubscriberMock: jest.Mock;
                beforeEach(() => {
                    validationSubscriberMock = jest.fn();
                    collectionValue.subscribeForValidityChanges(validationSubscriberMock);
                });

                it('should not notify the subscriber if the record becomes invalid from a non organic event', () => {
                    expect(validationSubscriberMock).not.toHaveBeenCalled();
                    collectionValue.setCellValue({ recordId: '3', columnId: 'anyField', value: 'a long text' });
                    expect(validationSubscriberMock).not.toHaveBeenCalled();
                });

                it('should notify the subscriber if the record becomes invalid from an organic event', async () => {
                    expect(validationSubscriberMock).not.toHaveBeenCalled();
                    collectionValue.setCellValue({
                        recordId: '3',
                        columnId: 'anyField',
                        value: 'a long text',
                        isOrganicChange: true,
                    });
                    await waitFor(() => expect(validationSubscriberMock).toHaveBeenCalled());
                });

                it('should not notify the subscriber if the validation state does not changed compared to the previous one', async () => {
                    expect(validationSubscriberMock).not.toHaveBeenCalled();
                    collectionValue.setCellValue({
                        recordId: '3',
                        columnId: 'anyField',
                        value: 'a long text',
                        isOrganicChange: true,
                    });

                    await waitFor(() => expect(validationSubscriberMock).toHaveBeenCalledTimes(1), { timeout: 150 });

                    collectionValue.setCellValue({
                        recordId: '3',
                        columnId: 'anyField',
                        value: 'another invalid long text',
                        isOrganicChange: true,
                    });

                    await waitFor(() => expect(validationSubscriberMock).toHaveBeenCalledTimes(1), { timeout: 150 });

                    collectionValue.setCellValue({
                        recordId: '3',
                        columnId: 'anyField',
                        value: 'abc',
                        isOrganicChange: true,
                    });

                    await waitFor(() => expect(validationSubscriberMock).toHaveBeenCalledTimes(2), { timeout: 150 });

                    collectionValue.setCellValue({
                        recordId: '3',
                        columnId: 'anyField',
                        value: 'cba',
                        isOrganicChange: true,
                    });

                    await waitFor(() => expect(validationSubscriberMock).toHaveBeenCalledTimes(2), { timeout: 150 });

                    collectionValue.setCellValue({
                        recordId: '3',
                        columnId: 'anyField',
                        value: 'another invalid long text',
                        isOrganicChange: true,
                    });

                    await waitFor(() => expect(validationSubscriberMock).toHaveBeenCalledTimes(3), { timeout: 150 });
                });

                it('should call the subscriber with record validation and global collection value details', async () => {
                    collectionValue.setCellValue({
                        recordId: '3',
                        columnId: 'anyField',
                        value: 'a long text',
                        isOrganicChange: true,
                    });
                    await waitFor(() => expect(validationSubscriberMock).toHaveBeenCalledTimes(1));

                    expect(validationSubscriberMock.mock.calls[0][0]).toEqual({
                        globalValidationState: [
                            {
                                anyField: [
                                    {
                                        columnId: 'anyField',
                                        elementId: 'testTable',
                                        message: 'anyField maximum length is 4',
                                        recordId: '3',
                                        screenId: 'TestPage',
                                        validationRule: 'maxLength',
                                    },
                                ],
                            },
                            {},
                        ],
                        recordValidationState: {
                            anyField: {
                                columnId: 'anyField',
                                elementId: 'testTable',
                                message: 'anyField maximum length is 4',
                                recordId: '3',
                                screenId: 'TestPage',
                                validationRule: 'maxLength',
                            },
                        },
                        recordId: '3',
                        recordLevel: undefined,
                    });

                    collectionValue.setCellValue({
                        recordId: '2',
                        columnId: 'someOtherField',
                        value: 'b',
                        isOrganicChange: true,
                    });
                    await waitFor(() => expect(validationSubscriberMock).toHaveBeenCalledTimes(2));

                    expect(validationSubscriberMock.mock.calls[1][0]).toEqual({
                        globalValidationState: [
                            {
                                anyField: [
                                    {
                                        columnId: 'anyField',
                                        elementId: 'testTable',
                                        message: 'anyField maximum length is 4',
                                        recordId: '3',
                                        screenId: 'TestPage',
                                        validationRule: 'maxLength',
                                    },
                                ],
                                someOtherField: [
                                    {
                                        columnId: 'someOtherField',
                                        elementId: 'testTable',
                                        message: 'someOtherField minimum value is 2',
                                        recordId: '2',
                                        screenId: 'TestPage',
                                        validationRule: 'min',
                                    },
                                ],
                            },
                            {},
                        ],
                        recordValidationState: {
                            someOtherField: {
                                columnId: 'someOtherField',
                                elementId: 'testTable',
                                message: 'someOtherField minimum value is 2',
                                recordId: '2',
                                screenId: 'TestPage',
                                validationRule: 'min',
                            },
                        },
                        recordId: '2',
                        recordLevel: undefined,
                    });

                    const newRecord = collectionValue.addRecord({
                        level: 1,
                        recordData: { aThridField: 'b' } as any,
                        parentId: '1',
                    });

                    collectionValue.setCellValue({
                        recordId: newRecord._id,
                        level: 1,
                        columnId: 'aThridField',
                        value: null,
                        isOrganicChange: true,
                    });

                    await waitFor(() => expect(validationSubscriberMock).toHaveBeenCalledTimes(3));

                    expect(validationSubscriberMock.mock.calls[2][0]).toEqual({
                        globalValidationState: [
                            {
                                anyField: [
                                    {
                                        columnId: 'anyField',
                                        elementId: 'testTable',
                                        message: 'anyField maximum length is 4',
                                        recordId: '3',
                                        screenId: 'TestPage',
                                        validationRule: 'maxLength',
                                    },
                                ],
                                someOtherField: [
                                    {
                                        columnId: 'someOtherField',
                                        elementId: 'testTable',
                                        message: 'someOtherField minimum value is 2',
                                        recordId: '2',
                                        screenId: 'TestPage',
                                        validationRule: 'min',
                                    },
                                ],
                            },
                            {
                                aThridField: [
                                    {
                                        columnId: 'aThridField',
                                        elementId: 'testTable',
                                        message: 'You need to enter a value.',
                                        recordId: '-1',
                                        screenId: 'TestPage',
                                        validationRule: 'isMandatory',
                                    },
                                ],
                            },
                        ],
                        recordValidationState: {
                            aThridField: {
                                columnId: 'aThridField',
                                elementId: 'testTable',
                                message: 'You need to enter a value.',
                                recordId: '-1',
                                screenId: 'TestPage',
                                validationRule: 'isMandatory',
                            },
                        },
                        recordId: '-1',
                        recordLevel: 1,
                    });
                });

                describe('server side validation errors', () => {
                    it('should add a server side error to the validation state object of the row', async () => {
                        expect(validationSubscriberMock).not.toHaveBeenCalled();
                        expect(collectionValue.db.isCollectionValid()).toBe(true);
                        await collectionValue.addValidationErrors({
                            validationErrors: [
                                {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    recordId: '3',
                                    message: 'Some error',
                                    validationRule: 'server-some-server-side-error',
                                },
                            ],
                        });
                        expect(validationSubscriberMock).not.toHaveBeenCalled();
                        expect(collectionValue.db.isCollectionValid()).toBe(false);
                        expect(
                            collectionValue.getRawRecord({ id: '3', cleanMetadata: false })!.__validationState,
                        ).toEqual({
                            someOtherField: {
                                screenId,
                                elementId: fieldId,
                                columnId: 'someOtherField',
                                recordId: '3',
                                message: 'Some error',
                                validationRule: 'server-some-server-side-error',
                            },
                        });
                    });

                    it('should add a server side error to the validation state object of the row on the second level', async () => {
                        expect(validationSubscriberMock).not.toHaveBeenCalled();
                        expect(collectionValue.db.isCollectionValid()).toBe(true);
                        await collectionValue.addValidationErrors({
                            validationErrors: [
                                {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    recordId: '1',
                                    level: 1,
                                    message: 'Some error on the second level',
                                    validationRule: 'server-some-server-side-error',
                                },
                            ],
                        });
                        expect(validationSubscriberMock).not.toHaveBeenCalled();
                        expect(collectionValue.db.isCollectionValid()).toBe(false);
                        expect(
                            collectionValue.getRawRecord({ id: '1', level: 1, cleanMetadata: false })!
                                .__validationState,
                        ).toEqual({
                            someOtherField: {
                                screenId,
                                elementId: fieldId,
                                columnId: 'someOtherField',
                                recordId: '1',
                                level: 1,
                                message: 'Some error on the second level',
                                validationRule: 'server-some-server-side-error',
                            },
                        });
                    });

                    it('should notify subscribers when a server error is added and the notifySubscribers arg is set', async () => {
                        expect(validationSubscriberMock).not.toHaveBeenCalled();
                        await collectionValue.addValidationErrors({
                            validationErrors: [
                                {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    recordId: '3',
                                    message: 'Some error',
                                    validationRule: 'server-some-server-side-error',
                                },
                            ],
                            shouldNotifySubscribers: true,
                        });
                        expect(validationSubscriberMock).toHaveBeenCalledTimes(1);
                        expect(validationSubscriberMock).toHaveBeenCalledWith({
                            globalValidationState: [
                                {
                                    someOtherField: [
                                        {
                                            screenId,
                                            elementId: fieldId,
                                            columnId: 'someOtherField',
                                            message: 'Some error',
                                            recordId: '3',
                                            validationRule: 'server-some-server-side-error',
                                        },
                                    ],
                                },
                                {},
                            ],
                            recordValidationState: {
                                someOtherField: {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    message: 'Some error',
                                    recordId: '3',
                                    validationRule: 'server-some-server-side-error',
                                },
                            },
                            recordId: '3',
                            recordLevel: undefined,
                        });
                    });

                    it('should not notify subscribers again if the state remains the same', async () => {
                        expect(validationSubscriberMock).not.toHaveBeenCalled();
                        await collectionValue.addValidationErrors({
                            validationErrors: [
                                {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    recordId: '3',
                                    message: 'Some error',
                                    validationRule: 'server-some-server-side-error',
                                },
                            ],
                            shouldNotifySubscribers: true,
                        });
                        expect(validationSubscriberMock).toHaveBeenCalledTimes(1);
                        await collectionValue.addValidationErrors({
                            validationErrors: [
                                {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    recordId: '3',
                                    message: 'Some error',
                                    validationRule: 'server-some-server-side-error',
                                },
                            ],
                            shouldNotifySubscribers: true,
                        });
                        expect(validationSubscriberMock).toHaveBeenCalledTimes(1);
                        await collectionValue.addValidationErrors({
                            validationErrors: [
                                {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    recordId: '3',
                                    message: 'Some slightly different error',
                                    validationRule: 'server-some-server-side-error',
                                },
                            ],
                            shouldNotifySubscribers: true,
                        });
                        expect(validationSubscriberMock).toHaveBeenCalledTimes(2);
                    });

                    it('should should remove the server validation error if the server clears off the error', async () => {
                        await collectionValue.addValidationErrors({
                            validationErrors: [
                                {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    recordId: '3',
                                    message: 'Some error',
                                    validationRule: 'server-some-server-side-error',
                                },
                            ],
                        });

                        expect(
                            collectionValue.getRawRecord({ id: '3', cleanMetadata: false })!.__validationState,
                        ).toEqual({
                            someOtherField: {
                                screenId,
                                elementId: fieldId,
                                columnId: 'someOtherField',
                                recordId: '3',
                                message: 'Some error',
                                validationRule: 'server-some-server-side-error',
                            },
                        });

                        expect(collectionValue.db.isCollectionValid()).toBe(false);

                        await collectionValue.addValidationErrors({ validationErrors: [] });

                        expect(
                            collectionValue.getRawRecord({ id: '3', cleanMetadata: false })!.__validationState,
                        ).toBeUndefined();

                        expect(collectionValue.db.isCollectionValid()).toBe(true);
                    });

                    it('should should remove only errors that become removed by the server and keep others', async () => {
                        await collectionValue.addValidationErrors({
                            validationErrors: [
                                {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    recordId: '3',
                                    message: 'Some error',
                                    validationRule: 'server-some-server-side-error',
                                },
                                {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    recordId: '1',
                                    level: 1,
                                    message: 'Some error on the second level',
                                    validationRule: 'server-some-server-side-error',
                                },
                            ],
                        });

                        expect(
                            collectionValue.getRawRecord({ id: '3', cleanMetadata: false })!.__validationState,
                        ).toEqual({
                            someOtherField: {
                                screenId,
                                elementId: fieldId,
                                columnId: 'someOtherField',
                                recordId: '3',
                                message: 'Some error',
                                validationRule: 'server-some-server-side-error',
                            },
                        });

                        expect(
                            collectionValue.getRawRecord({ id: '1', level: 1, cleanMetadata: false })!
                                .__validationState,
                        ).toEqual({
                            someOtherField: {
                                screenId,
                                elementId: fieldId,
                                columnId: 'someOtherField',
                                recordId: '1',
                                level: 1,
                                message: 'Some error on the second level',
                                validationRule: 'server-some-server-side-error',
                            },
                        });

                        expect(collectionValue.db.isCollectionValid()).toBe(false);

                        // We only add one error, so the validation state for the other record should be cleaned up
                        await collectionValue.addValidationErrors({
                            validationErrors: [
                                {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    recordId: '3',
                                    message: 'Some error',
                                    validationRule: 'server-some-server-side-error',
                                },
                            ],
                        });

                        expect(
                            collectionValue.getRawRecord({ id: '1', level: 1, cleanMetadata: false })!
                                .__validationState,
                        ).toBeUndefined();

                        expect(
                            collectionValue.getRawRecord({ id: '3', cleanMetadata: false })!.__validationState,
                        ).toEqual({
                            someOtherField: {
                                screenId,
                                elementId: fieldId,
                                columnId: 'someOtherField',
                                recordId: '3',
                                message: 'Some error',
                                validationRule: 'server-some-server-side-error',
                            },
                        });
                        expect(collectionValue.db.isCollectionValid()).toBe(false);
                    });

                    it('should create "unloaded" entries for those validation errors which have not been loaded yet', async () => {
                        await collectionValue.addValidationErrors({
                            validationErrors: [
                                {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    recordId: '6',
                                    message: 'Some error',
                                    validationRule: 'server-some-server-side-error',
                                },
                            ],
                        });

                        expect(
                            collectionValue.db.findOne({
                                cleanMetadata: false,
                                id: '6',
                                level: 0,
                                includeUnloaded: true,
                            }),
                        ).toEqual(
                            expect.objectContaining({
                                __compositeKey: '6.0',
                                __dirty: true,
                                __dirtyColumns: expect.anything(),
                                __unloaded: true,
                                __validationState: {
                                    someOtherField: {
                                        columnId: 'someOtherField',
                                        screenId,
                                        elementId: fieldId,
                                        message: 'Some error',
                                        recordId: '6',
                                        validationRule: 'server-some-server-side-error',
                                    },
                                },
                                _id: '6',
                            }),
                        );

                        expect(
                            collectionValue.db
                                .findAll({ where: { _id: '6' }, level: 0, includeUnloaded: true })
                                .value({ cleanMetadata: false }),
                        ).toEqual([
                            expect.objectContaining({
                                __compositeKey: '6.0',
                                __dirty: true,
                                __dirtyColumns: expect.anything(),
                                __unloaded: true,
                                __validationState: {
                                    someOtherField: {
                                        columnId: 'someOtherField',
                                        screenId,
                                        elementId: fieldId,
                                        message: 'Some error',
                                        recordId: '6',
                                        validationRule: 'server-some-server-side-error',
                                    },
                                },
                                _id: '6',
                            }),
                        ]);
                    });

                    it('should excluded unloaded entries if the unloaded flag is not passed', async () => {
                        await collectionValue.addValidationErrors({
                            validationErrors: [
                                {
                                    screenId,
                                    elementId: fieldId,
                                    columnId: 'someOtherField',
                                    recordId: '6',
                                    message: 'Some error',
                                    validationRule: 'server-some-server-side-error',
                                },
                            ],
                        });

                        expect(collectionValue.db.findOne({ cleanMetadata: false, id: '6', level: 0 })).toEqual(null);
                        expect(
                            collectionValue.db
                                .findAll({ where: { _id: '6' }, level: 0 })
                                .value({ cleanMetadata: false }),
                        ).toEqual([]);
                    });

                    it('should set the key as row error because it doesnt have columnId', async () => {
                        await collectionValue.addValidationErrors({
                            validationErrors: [
                                {
                                    screenId,
                                    elementId: fieldId,
                                    recordId: '3',
                                    message: 'Some error',
                                    validationRule: 'server-some-server-side-error',
                                },
                            ],
                        });
                        expect(validationSubscriberMock).not.toHaveBeenCalled();
                        expect(collectionValue.db.isCollectionValid()).toBe(false);
                        expect(
                            collectionValue.getRawRecord({ id: '3', cleanMetadata: false })!.__validationState,
                        ).toEqual({
                            __rowError_3: {
                                screenId,
                                elementId: fieldId,
                                recordId: '3',
                                message: 'Some error',
                                validationRule: 'server-some-server-side-error',
                            },
                        });
                    });
                });
            });
        });

        describe('mapServerRecord functionality', () => {
            let fetchCollectionDataMock: jest.SpyInstance<any, any>;
            beforeEach(() => {
                fetchCollectionDataMock = jest.spyOn(graphQLService, 'fetchCollectionData').mockResolvedValue({
                    pageInfo: { endCursor: false, endPage: false },
                    data: [
                        {
                            _id: '4',
                            anyField: '4 test',
                        },
                        {
                            _id: '5',
                            anyField: 'test string 3',
                        },
                    ],
                });
            });

            it('should not fetch records with "mainField" === "_id" to show validation errors ', async () => {
                const screenDefinition = {
                    selectedRecordId: '123',
                    metadata: {
                        uiComponentProperties: {
                            [screenId]: {
                                node: '@sage/xtrem-test/AnyNode',
                                title: 'page',
                                isTransient: false,
                            },
                            [fieldId]: {
                                title: 'Title',
                                isTransient: false,
                                mainField: '_id',
                            },
                        },
                        screenId,
                    },
                    errors: {},
                };
                getMockStore({
                    translations: { 'en-US': {} },
                    screenDefinitions: {
                        [screenId]: screenDefinition,
                        [fieldId]: {
                            title: 'field',
                            isTransient: false,
                        },
                    },
                } as any);
                const columns = [
                    nestedFields.text<any, any>({ bind: '_id', isMandatory: true }),
                    nestedFields.text<any, any>({ bind: 'anyField', maxLength: 4 }),
                ];
                const collectionValue = new TestCollectionValue({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    columnDefinitions: [columns],
                    nodeTypes: {
                        AnyNode: {
                            name: 'AnyNode',
                            title: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    },
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test 1' },
                        { _id: '2', anyField: 'test 2' },
                        { _id: '3', anyField: 'test 3' },
                        { _id: '4', anyField: 'test 4' },
                        { _id: '5', anyField: 'test 5' },
                        { _id: '6', anyField: 'test 6' },
                        { _id: '7', anyField: 'test 7' },
                        { _id: '8', anyField: 'test 8' },
                        { _id: '9', anyField: 'test 9' },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                });
                for (let i = 0; i < 10; i++) {
                    const recordId = String(i + 1);
                    // eslint-disable-next-line no-await-in-loop
                    await collectionValue.addValidationErrors({
                        validationErrors: [
                            {
                                screenId,
                                elementId: fieldId,
                                columnId: 'anyField',
                                recordId,
                                message: `Some error ${recordId}`,
                                validationRule: 'server-some-server-side-error',
                            },
                        ],
                    });
                }
                expect(fetchCollectionDataMock).not.toHaveBeenCalled();
            });

            it('should not fetch records with "mainField" !== "_id" to show validation errors ', async () => {
                const screenDefinition = {
                    selectedRecordId: '123',
                    metadata: {
                        uiComponentProperties: {
                            [screenId]: {
                                node: '@sage/xtrem-test/AnyNode',
                                title: 'page',
                                isTransient: false,
                            },
                            [fieldId]: {
                                title: 'Title',
                                isTransient: false,
                                mainField: 'anyField',
                            },
                        },
                        screenId,
                    },
                    errors: {},
                };
                getMockStore({
                    translations: { 'en-US': {} },
                    screenDefinitions: {
                        [screenId]: screenDefinition,
                        [fieldId]: {
                            title: 'field',
                            isTransient: false,
                        },
                    },
                } as any);
                const columns = [
                    nestedFields.text<any, any>({ bind: '_id', isMandatory: true }),
                    nestedFields.text<any, any>({ bind: 'anyField', maxLength: 4 }),
                ];
                const collectionValue = new TestCollectionValue({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    columnDefinitions: [columns],
                    nodeTypes: {
                        AnyNode: {
                            name: 'AnyNode',
                            title: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    },
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test 1' },
                        { _id: '2', anyField: 'test 2' },
                        { _id: '3', anyField: 'test 3' },
                        { _id: '4', anyField: 'test 4' },
                        { _id: '5', anyField: 'test 5' },
                        { _id: '6', anyField: 'test 6' },
                        { _id: '7', anyField: 'test 7' },
                        { _id: '8', anyField: 'test 8' },
                        { _id: '9', anyField: 'test 9' },
                        { _id: '10', anyField: 'test 10' },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                });
                for (let i = 0; i < 10; i++) {
                    const recordId = String(i + 1);
                    // eslint-disable-next-line no-await-in-loop
                    await collectionValue.addValidationErrors({
                        validationErrors: [
                            {
                                screenId,
                                elementId: fieldId,
                                columnId: 'anyField',
                                recordId,
                                message: `Some error ${recordId}`,
                                validationRule: 'server-some-server-side-error',
                            },
                        ],
                    });
                }
                expect(fetchCollectionDataMock).not.toHaveBeenCalled();
            });

            it('should fetch records with "mainField" !== "_id" to show validation errors ', async () => {
                const screenDefinition = {
                    selectedRecordId: '123',
                    metadata: {
                        uiComponentProperties: {
                            [screenId]: {
                                node: '@sage/xtrem-test/AnyNode',
                                title: 'page',
                                isTransient: false,
                            },
                            [fieldId]: {
                                title: 'Title',
                                isTransient: false,
                                mainField: 'anyField',
                            },
                        },
                        screenId,
                    },
                    errors: {},
                };
                getMockStore({
                    translations: { 'en-US': {} },
                    screenDefinitions: {
                        [screenId]: screenDefinition,
                        [fieldId]: {
                            title: 'field',
                            isTransient: false,
                        },
                    },
                } as any);
                const columns = [
                    nestedFields.text<any, any>({ bind: '_id', isMandatory: true }),
                    nestedFields.text<any, any>({ bind: 'anyField', maxLength: 4 }),
                ];
                const collectionValue = new TestCollectionValue({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    columnDefinitions: [columns],
                    nodeTypes: {
                        AnyNode: {
                            name: 'AnyNode',
                            title: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    },
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test 1' },
                        { _id: '2', anyField: 'test 2' },
                        { _id: '3', anyField: 'test 3' },
                        { _id: '4', anyField: 'test 4' },
                        { _id: '5', anyField: 'test 5' },
                        { _id: '6', anyField: 'test 6' },
                        { _id: '7', anyField: 'test 7' },
                        { _id: '8', anyField: 'test 8' },
                        { _id: '9', anyField: 'test 9' },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                });
                for (let i = 0; i < 10; i++) {
                    const recordId = String(i + 1);
                    // eslint-disable-next-line no-await-in-loop
                    await collectionValue.addValidationErrors({
                        validationErrors: [
                            {
                                screenId,
                                elementId: fieldId,
                                columnId: 'anyField',
                                recordId,
                                message: `Some error ${recordId}`,
                                validationRule: 'server-some-server-side-error',
                            },
                        ],
                        shouldNotifySubscribers: true,
                    });
                }
                expect(fetchCollectionDataMock).toHaveBeenCalledWith({
                    screenDefinition,
                    rootNode: '@sage/xtrem-test/AnyNode',
                    rootNodeId: '123',
                    elementId: fieldId,
                    nestedFields: columns,
                    queryArguments: { filter: JSON.stringify({ _id: { _in: ['10'] } }) },
                    bind: undefined,
                });
            });

            it('should throw error if mapper function tries to modify the _id property', () => {
                expect(() => {
                    const mapperMock = jest.fn(v => ({
                        ...v,
                        _id: `${v._id} modified`,
                    }));
                    const columns = [
                        nestedFields.text<any, any>({ bind: '_id', isMandatory: true }),
                        nestedFields.text<any, any>({ bind: 'anyField', maxLength: 4 }),
                    ];
                    new TestCollectionValue({
                        screenId,
                        elementId: fieldId,
                        isTransient: false,
                        hasNextPage: true,
                        orderBy: [{ anyField: 1 }],
                        mapServerRecordFunctions: [mapperMock],
                        columnDefinitions: [columns],
                        nodeTypes: {
                            AnyNode: {
                                name: 'AnyNode',
                                title: 'AnyNode',
                                packageName: '@sage/xtrem-test',
                                properties: {
                                    _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                    _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                    _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                    anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                },
                                mutations: {},
                            },
                        },
                        nodes: ['@sage/xtrem-test/AnyNode'],
                        filter: [undefined],
                        initialValues: [
                            { _id: '1', anyField: 'test' },
                            { _id: '2', anyField: 'test string 2' },
                            { _id: '3', anyField: 'test aasd' },
                        ],
                        fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                    });
                }).toThrow('The remap function is not allowed to modify the _id property.');
            });

            it('should throw if the mapper function tries to override a metadata property', () => {
                const columns = [
                    nestedFields.text<any, any>({ bind: '_id', isMandatory: true }),
                    nestedFields.text<any, any>({ bind: 'anyField', maxLength: 4 }),
                ];
                const props = {
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }] as any,
                    columnDefinitions: [columns],
                    nodeTypes: {
                        AnyNode: {
                            name: 'AnyNode',
                            title: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    } as Dict<FormattedNodeDetails>,
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test' },
                        { _id: '2', anyField: 'test string 2' },
                        { _id: '3', anyField: 'test aasd' },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                };

                expect(() => {
                    new TestCollectionValue({
                        ...props,
                        mapServerRecordFunctions: [
                            jest.fn(v => ({
                                ...v,
                                $loki: 5,
                            })),
                        ],
                    });
                }).toThrow(
                    'The remap function is not allowed to modify metadata properties. Modified properties: $loki',
                );

                expect(() => {
                    new TestCollectionValue({
                        ...props,
                        mapServerRecordFunctions: [
                            jest.fn(v => ({
                                ...v,
                                __level: 1,
                            })),
                        ],
                    });
                }).toThrow(
                    'The remap function is not allowed to modify metadata properties. Modified properties: __level',
                );
                expect(() => {
                    new TestCollectionValue({
                        ...props,
                        mapServerRecordFunctions: [
                            jest.fn(v => ({
                                ...v,
                                __dirtyColumns: {},
                            })),
                        ],
                    });
                }).toThrow(
                    'The remap function is not allowed to modify metadata properties. Modified properties: __dirtyColumns',
                );
                expect(() => {
                    new TestCollectionValue({
                        ...props,
                        mapServerRecordFunctions: [
                            jest.fn(v => ({
                                ...v,
                                __validationState: {},
                            })),
                        ],
                    });
                }).toThrow(
                    'The remap function is not allowed to modify metadata properties. Modified properties: __validationState',
                );
                expect(() => {
                    new TestCollectionValue({
                        ...props,
                        mapServerRecordFunctions: [
                            jest.fn(v => ({
                                ...v,
                                __compositeKey: '3.5',
                            })),
                        ],
                    });
                }).toThrow(
                    'The remap function is not allowed to modify metadata properties. Modified properties: __compositeKey',
                );
                expect(() => {
                    new TestCollectionValue({
                        ...props,
                        mapServerRecordFunctions: [
                            jest.fn(v => ({
                                ...v,
                                __unloaded: false,
                            })),
                        ],
                    });
                }).toThrow(
                    'The remap function is not allowed to modify metadata properties. Modified properties: __unloaded',
                );
            });

            it('should allow overriding properties and add values on construction for multi level collection (nested grid)', async () => {
                const mapperMock1 = jest.fn(v => ({
                    ...v,
                    someTransientProperty: `some new field here ${v._id}`,
                    anyField: `mapped ${v.anyField}`,
                }));
                const mapperMock2 = jest.fn(v => ({
                    ...v,
                    someTransientPropertyOnLevel2: `level 2 ${v._id}`,
                }));
                const columns1 = [
                    nestedFields.text<any, any>({ bind: '_id', isMandatory: true }),
                    nestedFields.text<any, any>({ bind: 'anyField', maxLength: 4 }),
                ];
                const columns2 = [
                    nestedFields.text<any, any>({ bind: '_id', isMandatory: true }),
                    nestedFields.text<any, any>({ bind: 'anyField', maxLength: 4 }),
                ];
                collectionValue = new TestCollectionValue({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    mapServerRecordFunctions: [mapperMock1, mapperMock2],
                    columnDefinitions: [columns1, columns2],
                    nodeTypes: {
                        AnyNode: {
                            name: 'AnyNode',
                            title: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                child: { type: 'ChildNode', kind: 'LIST' },
                            },
                            mutations: {},
                        },
                        ChildNode: {
                            name: 'ChildNode',
                            title: 'ChildNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyFieldLevel2: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    },
                    nodes: ['@sage/xtrem-test/AnyNode', '@sage/xtrem-test/ChildNode'],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test' },
                        { _id: '2', anyField: 'test string 2' },
                        { _id: '3', anyField: 'test aasd' },
                        { _id: '1', __level: 1, anyFieldLevel2: 'some text' },
                        { _id: '2', __level: 1, anyFieldLevel2: 'some other text' },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                });

                expect(mapperMock1).toHaveBeenCalledTimes(3);
                expect(mapperMock2).toHaveBeenCalledTimes(2);
                // Should not pass in metadata properties
                expect(mapperMock1).toHaveBeenNthCalledWith(
                    1,
                    {
                        _id: '1',
                        anyField: 'test',
                    },
                    null,
                );
                expect(mapperMock2).toHaveBeenNthCalledWith(
                    1,
                    {
                        _id: '1',
                        anyFieldLevel2: 'some text',
                    },
                    null,
                );

                expect(collectionValue.getRecordByIdAndLevel({ id: '1' })).toEqual(
                    expect.objectContaining({
                        anyField: 'mapped test',
                        someTransientProperty: 'some new field here 1',
                    }),
                );
                expect(collectionValue.getRecordByIdAndLevel({ id: '2' })).toEqual(
                    expect.objectContaining({
                        anyField: 'mapped test string 2',
                        someTransientProperty: 'some new field here 2',
                    }),
                );
                expect(collectionValue.getRecordByIdAndLevel({ id: '3' })).toEqual(
                    expect.objectContaining({
                        anyField: 'mapped test aasd',
                        someTransientProperty: 'some new field here 3',
                    }),
                );

                expect(collectionValue.getRecordByIdAndLevel({ id: '1', level: 1 })).toEqual(
                    expect.objectContaining({
                        anyFieldLevel2: 'some text',
                        someTransientPropertyOnLevel2: 'level 2 1',
                    }),
                );
                expect(collectionValue.getRecordByIdAndLevel({ id: '2', level: 1 })).toEqual(
                    expect.objectContaining({
                        anyFieldLevel2: 'some other text',
                        someTransientPropertyOnLevel2: 'level 2 2',
                    }),
                );

                expect(fetchCollectionDataMock!).not.toHaveBeenCalled();

                await collectionValue.getPageWithCurrentQueryArguments({
                    pageNumber: 1,
                    pageSize: 5,
                    cursor: 'someRandomCursor',
                    tableFieldProperties: {
                        isTransient: false,
                        node: '@sage/xtrem-test/AnyNode',
                        columns: columns1,
                    },
                });

                expect(fetchCollectionDataMock!).toHaveBeenCalled();

                expect(mapperMock1).toHaveBeenCalledTimes(5);
                // Should not pass in metadata properties
                expect(mapperMock1).toHaveBeenNthCalledWith(
                    4,
                    {
                        _id: '4',
                        anyField: '4 test',
                    },
                    null,
                );
                expect(collectionValue.getRecordByIdAndLevel({ id: '4' })).toEqual(
                    expect.objectContaining({
                        anyField: 'mapped 4 test',
                        someTransientProperty: 'some new field here 4',
                    }),
                );
                expect(collectionValue.getRecordByIdAndLevel({ id: '5' })).toEqual(
                    expect.objectContaining({
                        anyField: 'mapped test string 3',
                        someTransientProperty: 'some new field here 5',
                    }),
                );
            });

            it('should allow overriding properties and add values on construction for single level collection (table, pod-collection)', async () => {
                const mapperMock = jest.fn(v => ({
                    ...v,
                    someTransientProperty: `some new field here ${v._id}`,
                    anyField: `mapped ${v.anyField}`,
                }));
                const columns = [
                    nestedFields.text<any, any>({ bind: '_id', isMandatory: true }),
                    nestedFields.text<any, any>({ bind: 'anyField', maxLength: 4 }),
                ];
                collectionValue = new TestCollectionValue({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    mapServerRecordFunctions: [mapperMock],
                    columnDefinitions: [columns],
                    nodeTypes: {
                        AnyNode: {
                            name: 'AnyNode',
                            title: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    },
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test' },
                        { _id: '2', anyField: 'test string 2' },
                        { _id: '3', anyField: 'test aasd' },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                });

                expect(mapperMock).toHaveBeenCalledTimes(3);
                // Should not pass in metadata properties
                expect(mapperMock).toHaveBeenNthCalledWith(
                    1,
                    {
                        _id: '1',
                        anyField: 'test',
                    },
                    null,
                );

                expect(collectionValue.getRecordByIdAndLevel({ id: '1' })).toEqual(
                    expect.objectContaining({
                        anyField: 'mapped test',
                        someTransientProperty: 'some new field here 1',
                    }),
                );
                expect(collectionValue.getRecordByIdAndLevel({ id: '2' })).toEqual(
                    expect.objectContaining({
                        anyField: 'mapped test string 2',
                        someTransientProperty: 'some new field here 2',
                    }),
                );
                expect(collectionValue.getRecordByIdAndLevel({ id: '3' })).toEqual(
                    expect.objectContaining({
                        anyField: 'mapped test aasd',
                        someTransientProperty: 'some new field here 3',
                    }),
                );

                expect(fetchCollectionDataMock!).not.toHaveBeenCalled();

                await collectionValue.getPageWithCurrentQueryArguments({
                    pageNumber: 1,
                    pageSize: 5,
                    cursor: 'someRandomCursor',
                    tableFieldProperties: {
                        isTransient: false,
                        node: '@sage/xtrem-test/AnyNode',
                        columns,
                    },
                });

                expect(fetchCollectionDataMock!).toHaveBeenCalled();

                expect(mapperMock).toHaveBeenCalledTimes(5);
                // Should not pass in metadata properties
                expect(mapperMock).toHaveBeenNthCalledWith(
                    4,
                    {
                        _id: '4',
                        anyField: '4 test',
                    },
                    null,
                );
                expect(collectionValue.getRecordByIdAndLevel({ id: '4' })).toEqual(
                    expect.objectContaining({
                        anyField: 'mapped 4 test',
                        someTransientProperty: 'some new field here 4',
                    }),
                );
                expect(collectionValue.getRecordByIdAndLevel({ id: '5' })).toEqual(
                    expect.objectContaining({
                        anyField: 'mapped test string 3',
                        someTransientProperty: 'some new field here 5',
                    }),
                );
            });
        });

        describe('repackRowValue', () => {
            it('should repack row value', () => {
                const input = {
                    destination__name: { name: 'US Dollar', _id: '4' },
                    destination__id: { icon: { value: null }, id: 'USD', _id: '4' },
                    _id: '1',
                    dateRate: '2020-01-01',
                    rate: '1.1234',
                    destination: { id: 'USD', _id: '4' },
                    base: { id: 'EUR', _id: '2' },
                    __cursor: '[1]#78',
                };
                const result = collectionValue.repackRecordData(input);
                expect(result).toEqual({
                    _id: '1',
                    dateRate: '2020-01-01',
                    rate: '1.1234',
                    destination: { name: 'US Dollar', _id: '4', id: 'USD', icon: { value: null } },
                    base: { id: 'EUR', _id: '2' },
                    __cursor: '[1]#78',
                });
            });

            it('should repack other example', () => {
                const input = {
                    _id: '18',
                    country: { _id: '3' },
                    country__id: { id: 'FR', _id: '3' },
                    taxCategory__id: { id: 'VAT', _id: '1' },
                    name: 'Exempt rate collected on debits',
                    __cursor: '["Exempt rate collected on debits","VAT","FR_TVA_EXEMPT_COLLECTED_ON_DEBITS"]#38',
                };
                const result = collectionValue.repackRecordData(input);

                expect(result).toEqual({
                    _id: '18',
                    country: { _id: '3', id: 'FR' },
                    taxCategory: { id: 'VAT', _id: '1' },
                    name: 'Exempt rate collected on debits',
                    __cursor: '["Exempt rate collected on debits","VAT","FR_TVA_EXEMPT_COLLECTED_ON_DEBITS"]#38',
                });
            });

            it('Should iterate the object even if the key dont have underscore and repack the values inside', () => {
                const input: any = {
                    product: {
                        name: 'patata',
                        destination__name: { name: 'US Dollar', _id: '4' },
                        destination__id: { icon: { value: null }, id: 'USD', _id: '4' },
                        destination: { id: 'USD', _id: '4' },
                    },
                    base__id: 'EUR',
                    base__name: 'Euro',
                };
                const result = collectionValue.repackRecordData(input);
                expect(result).toEqual({
                    product: {
                        name: 'patata',
                        destination: { name: 'US Dollar', _id: '4', id: 'USD', icon: { value: null } },
                    },
                    base: { id: 'EUR', name: 'Euro' },
                });
            });

            it('Should repack the object returning the array correctly', () => {
                const input = {
                    addresses__name: [
                        { name: 'Sant Cugat Office', _id: '3' },
                        { name: 'Paris Office', _id: '4' },
                    ],
                    __cursor: '[2]#37',
                    _id: '2',
                };
                const expectOutput = {
                    addresses: [
                        { name: 'Sant Cugat Office', _id: '3' },
                        { name: 'Paris Office', _id: '4' },
                    ],
                    __cursor: '[2]#37',
                    _id: '2',
                };
                const result = collectionValue.repackRecordData(input);
                expect(result).toEqual(expectOutput);
            });

            it('Should return the value as Number if the type of the value is Decimal', () => {
                const input = {
                    destination__name: { name: 'US Dollar', _id: '4', rateValue: new Decimal(1.1234) },
                    destination__id: { icon: { value: null }, id: 'USD', _id: '4' },
                    _id: '1',
                    dateRate: '2020-01-01',
                    rate: new Decimal(1.1234),
                    destination: { id: 'USD', _id: '4' },
                    base: { id: 'EUR', _id: '2' },
                    __cursor: '[1]#78',
                };
                const expectedOutput = {
                    _id: '1',
                    dateRate: '2020-01-01',
                    rate: 1.1234,
                    destination: {
                        rateValue: 1.1234,
                        name: 'US Dollar',
                        _id: '4',
                        id: 'USD',
                        icon: { value: null },
                    },
                    base: { id: 'EUR', _id: '2' },
                    __cursor: '[1]#78',
                };
                const result = collectionValue.repackRecordData(input);
                expect(result).toEqual(expectedOutput);
            });

            it('Should not attempt to repack primitive values', () => {
                const input = {
                    destination__name: { name: 'US Dollar', _id: '4' },
                    destination__id: {
                        icon: { value: null },
                        id: 'USD',
                        _id: '4',
                        someStringArray: ['hi', 'there'],
                    },
                    _id: '1',
                    dateRate: '2020-01-01',
                    someStringArray: ['hi', 'there', true],
                    destination: { id: 'USD', _id: '4' },
                    base: { id: 'EUR', _id: '2' },
                    __cursor: '[1]#78',
                };
                const expectedOutput = {
                    _id: '1',
                    dateRate: '2020-01-01',
                    destination: {
                        name: 'US Dollar',
                        _id: '4',
                        id: 'USD',
                        icon: { value: null },
                        someStringArray: ['hi', 'there'],
                    },
                    base: { id: 'EUR', _id: '2' },
                    someStringArray: ['hi', 'there', true],
                    __cursor: '[1]#78',
                };
                const result = collectionValue.repackRecordData(input);
                expect(result).toEqual(expectedOutput);
            });
        });

        describe('isCollectionDirty', () => {
            it('should return false if none of the records marked as dirty', () => {
                collectionValue = new TestCollectionValue({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    levelMap: { 0: 'child' as const, 1: undefined },
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                            nestedFields.numeric({ bind: 'someOtherField' }),
                        ],
                        [
                            nestedFields.text<any, any>({ bind: 'anyField2' }),
                            nestedFields.text<any, any>({ bind: 'someOtherField2' }),
                        ],
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField3' }),
                            nestedFields.text<any, any>({ bind: 'someOtherField3' }),
                        ],
                    ],
                    nodeTypes: {
                        AnyNode: {
                            name: 'AnyNode',
                            title: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                children: { type: 'ChildNode', kind: 'LIST' },
                            },
                            mutations: {},
                        },
                        ChildNode: {
                            name: 'ChildNode',
                            title: 'ChildNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField2: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField2: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                children: { type: 'SecondChildNode', kind: 'LIST' },
                            },
                            mutations: {},
                        },
                        SecondChildNode: {
                            name: 'SecondChildNode',
                            title: 'SecondChildNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField3: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField3: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    },
                    nodes: [
                        '@sage/xtrem-test/AnyNode',
                        '@sage/xtrem-test/ChildNode',
                        '@sage/xtrem-test/SecondChildNode',
                    ],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', __action: RecordActionType.ADDED, anyField: 'test', someOtherField: 4 },
                        {
                            _id: '2',
                            __action: RecordActionType.ADDED,
                            anyField: 'test string 2',
                            someOtherField: -5,
                        },
                        {
                            _id: '3',
                            __action: RecordActionType.ADDED,
                            anyField: 'test aasd',
                            someOtherField: 32432,
                        },
                        {
                            _id: '1',
                            __parentId: '2',
                            __level: 1,
                            __action: RecordActionType.ADDED,
                            anyField2: 'child record 1',
                            someOtherField2: -5,
                        },
                        {
                            _id: '2',
                            __parentId: '2',
                            __level: 1,
                            __action: RecordActionType.ADDED,
                            anyField2: 'child record 2',
                            someOtherField2: -5,
                        },
                        {
                            _id: '1',
                            __parentId: '1',
                            __level: 2,
                            __action: RecordActionType.ADDED,
                            anyField3: 'second child record 3',
                            someOtherField2: -5,
                        },
                        {
                            _id: '2',
                            __parentId: '1',
                            __level: 2,
                            __action: RecordActionType.ADDED,
                            anyField3: 'second child record 3',
                            someOtherField2: -5,
                        },
                        {
                            _id: '90',
                            __parentId: '1',
                            __level: 1,
                            __action: RecordActionType.ADDED,
                            anyField2: 'asdasd',
                            someOtherField2: -5,
                        },
                        {
                            _id: '43',
                            __parentId: '90',
                            __level: 2,
                            __action: RecordActionType.ADDED,
                            anyField3: 'asdasd',
                            someOtherField2: -5,
                        },
                    ],
                    fieldType: CollectionFieldTypes.NESTED_GRID,
                });

                expect(collectionValue.db.isCollectionDirty()).toBe(false);
            });

            it('should return true if any of the records marked as dirty', () => {
                collectionValue = new TestCollectionValue({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    levelMap: { 0: 'child' as const, 1: undefined },
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                            nestedFields.numeric({ bind: 'someOtherField' }),
                        ],
                        [
                            nestedFields.text<any, any>({ bind: 'anyField2' }),
                            nestedFields.text<any, any>({ bind: 'someOtherField2' }),
                        ],
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField3' }),
                            nestedFields.text<any, any>({ bind: 'someOtherField3' }),
                        ],
                    ],
                    nodeTypes: {
                        AnyNode: {
                            name: 'AnyNode',
                            title: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                children: { type: 'ChildNode', kind: 'LIST' },
                            },
                            mutations: {},
                        },
                        ChildNode: {
                            name: 'ChildNode',
                            title: 'ChildNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField2: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField2: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                children: { type: 'SecondChildNode', kind: 'LIST' },
                            },
                            mutations: {},
                        },
                        SecondChildNode: {
                            name: 'SecondChildNode',
                            title: 'SecondChildNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField3: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField3: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    },
                    nodes: [
                        '@sage/xtrem-test/AnyNode',
                        '@sage/xtrem-test/ChildNode',
                        '@sage/xtrem-test/SecondChildNode',
                    ],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', __action: RecordActionType.ADDED, anyField: 'test', someOtherField: 4 },
                        {
                            _id: '2',
                            __action: RecordActionType.ADDED,
                            anyField: 'test string 2',
                            someOtherField: -5,
                        },
                        {
                            _id: '3',
                            __action: RecordActionType.ADDED,
                            anyField: 'test aasd',
                            someOtherField: 32432,
                        },
                        {
                            _id: '1',
                            __parentId: '2',
                            __level: 1,
                            __action: RecordActionType.ADDED,
                            anyField2: 'child record 1',
                            someOtherField2: -5,
                        },
                        {
                            _id: '2',
                            __parentId: '2',
                            __level: 1,
                            __action: RecordActionType.ADDED,
                            anyField2: 'child record 2',
                            someOtherField2: -5,
                        },
                        {
                            _id: '1',
                            __parentId: '1',
                            __level: 2,
                            __action: RecordActionType.ADDED,
                            anyField3: 'second child record 3',
                            someOtherField2: -5,
                        },
                        {
                            _id: '2',
                            __parentId: '1',
                            __level: 2,
                            __action: RecordActionType.ADDED,
                            anyField3: 'second child record 3',
                            someOtherField2: -5,
                        },
                        {
                            _id: '90',
                            __parentId: '1',
                            __level: 1,
                            __action: RecordActionType.ADDED,
                            anyField2: 'asdasd',
                            someOtherField2: -5,
                        },
                        {
                            _id: '43',
                            __parentId: '90',
                            __level: 2,
                            __action: RecordActionType.ADDED,
                            anyField3: 'asdasd',
                            someOtherField2: -5,
                        },
                    ],
                    fieldType: CollectionFieldTypes.NESTED_GRID,
                });

                collectionValue.setCellValue({
                    recordId: '90',
                    level: 1,
                    isOrganicChange: true,
                    columnId: 'anyField2',
                    value: 'test',
                });

                expect(collectionValue.db.isCollectionDirty()).toBe(true);
            });
        });

        describe('refreshRecord', () => {
            beforeEach(() => {
                jest.spyOn(graphQLService, 'fetchCollectionRecord').mockResolvedValue({
                    _id: '2',
                    anyField: 'refreshed value',
                    someOtherField: 123,
                });
            });

            it('should replace the value in the collection with the refreshed value', async () => {
                await collectionValue.refreshRecord({ recordId: '2' });
                const record = collectionValue.getRawRecord({ id: '2' });
                expect(record.anyField).toEqual('refreshed value');
                expect(record.someOtherField).toEqual(123);
            });

            it('should reset the action status of the record', async () => {
                collectionValue.setCellValue({
                    recordId: '2',
                    columnId: 'anyField',
                    value: 'locally modified field',
                    isOrganicChange: true,
                });

                expect(collectionValue.getRawRecord({ id: '2', cleanMetadata: false })!.__action).toEqual(
                    RecordActionType.MODIFIED,
                );

                await collectionValue.refreshRecord({ recordId: '2' });

                expect(collectionValue.getRawRecord({ id: '2', cleanMetadata: false })!.__action).toEqual(undefined);
            });

            it('should not set the value if skipSet is passed, only return it', async () => {
                const returnedRecord = await collectionValue.refreshRecord({ recordId: '2', skipUpdate: true });
                const recordInCollection = collectionValue.getRawRecord({ id: '2' });
                expect(returnedRecord.anyField).toEqual('refreshed value');
                expect(returnedRecord.someOtherField).toEqual(123);
                expect(recordInCollection.anyField).toEqual('test string 2');
                expect(recordInCollection.someOtherField).toEqual(-5);
            });
        });
    });

    describe('initialize database', () => {
        it('should serialize date objects on start', () => {
            collectionValue = new TestCollectionValue({
                screenId,
                elementId: fieldId,
                isTransient: false,
                hasNextPage: true,
                orderBy: [{ anyField: 1 }],
                columnDefinitions: [
                    [nestedFields.text<any, any>({ bind: '_id' }), nestedFields.date<any, any>({ bind: 'anyField' })],
                ],
                nodeTypes: {
                    AnyNode: {
                        name: 'AnyNode',
                        title: 'AnyNode',
                        packageName: '@sage/xtrem-test',
                        properties: {
                            _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                            _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                            _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                            anyField: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar },
                        },
                        mutations: {},
                    },
                },
                nodes: ['@sage/xtrem-test/AnyNode'],
                filter: [undefined],
                initialValues: [{ _id: '1', anyField: new Date(1675434180723) }],
                fieldType: CollectionFieldTypes.NESTED_GRID,
            });

            expect(collectionValue.getData()).toEqual([
                {
                    _id: '1',
                    anyField: '2023-02-03',
                },
            ]);
        });
    });

    describe('record transaction system ', () => {
        describe('startRecordTransaction', () => {
            it('should start a new transaction for the recordId 1', () => {
                collectionValue.startRecordTransaction({ recordId: '1' });
                const record = collectionValue.getRawRecord({ id: '1', cleanMetadata: false, isUncommitted: true });
                expect(record).toBeTruthy();
                expect(record.__uncommitted).toBeTruthy();
            });
            it('should return a error if the recordId is already in a transaction', () => {
                collectionValue.startRecordTransaction({ recordId: '1' });
                expect(() => collectionValue.startRecordTransaction({ recordId: '1' })).toThrow();
            });
            it('should return a error if the recordId is not found', () => {
                expect(() => collectionValue.startRecordTransaction({ recordId: 'patata' })).toThrow();
            });
        });
        describe('commitRecord', () => {
            it('should commit the transaction for the recordId 1 with the new value', () => {
                collectionValue.startRecordTransaction({ recordId: '1' });
                collectionValue.setCellValue({
                    recordId: '1',
                    columnId: 'anyField',
                    value: 'locally modified field',
                    isOrganicChange: true,
                    isUncommitted: true,
                });
                collectionValue.commitRecord({ recordId: '1' });
                const uncommittedRecord = collectionValue.getRawRecord({
                    id: '1',
                    cleanMetadata: false,
                    isUncommitted: true,
                });
                expect(uncommittedRecord).toBeFalsy();
                const commitedRecord = collectionValue.getRawRecord({ id: '1', cleanMetadata: false });
                expect(commitedRecord).toBeTruthy();
                expect(commitedRecord.__uncommitted).toBeFalsy();
                expect(commitedRecord.anyField).toEqual('locally modified field');
            });
            it('should fail the commit if before we didnt start the transaction for the record', () => {
                expect(() => collectionValue.commitRecord({ recordId: '1' })).toThrow();
            });
            it('should fail the commit if the recordId is not found', () => {
                expect(() => collectionValue.commitRecord({ recordId: 'patata' })).toThrow();
            });
        });
        describe('cancelRecordTransaction', () => {
            it('should cancel the transaction for the recordId 1', () => {
                collectionValue.startRecordTransaction({ recordId: '1' });
                collectionValue.setCellValue({
                    recordId: '1',
                    columnId: 'anyField',
                    value: 'locally modified field',
                    isOrganicChange: true,
                    isUncommitted: true,
                });
                collectionValue.cancelRecordTransaction({ recordId: '1' });
                const uncommittedRecord = collectionValue.getRawRecord({
                    id: '1',
                    cleanMetadata: false,
                    isUncommitted: true,
                });
                expect(uncommittedRecord).toBeFalsy();
                const commitedRecord = collectionValue.getRawRecord({ id: '1', cleanMetadata: false });
                expect(commitedRecord).toBeTruthy();
                expect(commitedRecord.__uncommitted).toBeFalsy();
                expect(commitedRecord.anyField).toEqual('test');
            });
            it('should fail the cancel if before we didnt start the transaction for the record', () => {
                expect(() => collectionValue.cancelRecordTransaction({ recordId: '1' })).toThrow();
            });
            it('should fail the cancel if the recordId is not found', () => {
                expect(() => collectionValue.cancelRecordTransaction({ recordId: 'patata' })).toThrow();
            });
        });
        // TAKE IN ACCOUNT THE ORDER BY IS anyfield: 1
        describe('getNextRecord', () => {
            const tableProperties: TableProperties = {
                bind: fieldId,
                columns: [
                    nestedFields.text<any, any>({ bind: '_id' }),
                    nestedFields.text<any, any>({ bind: 'anyField' }),
                    nestedFields.numeric<any, any>({ bind: 'someOtherField' }),
                ],
            };
            it('should return the next recordId of the recordId 1 orderBy anyField', async () => {
                expect(await collectionValue.getNextRecord({ recordId: '1', tableProperties })).toEqual({
                    _id: '3',
                    anyField: 'test aasd',
                    someOtherField: 32432,
                });
            });
            it('should return null if there is no next record', async () => {
                collectionValue.hasNextPage = false;
                expect(await collectionValue.getNextRecord({ recordId: '2', tableProperties })).toEqual(null);
            });
            it('should return null if the recordId is not found', async () => {
                expect(await collectionValue.getNextRecord({ recordId: 'patata', tableProperties })).toEqual(null);
            });
            it('Should fetch server next page and return the first value if hasNextPage is true', async () => {
                const mockGetPageWithCurrentQueryArguments = jest
                    .spyOn(collectionValue, 'getPageWithCurrentQueryArguments')
                    .mockResolvedValue([
                        { _id: '4', anyField: 'test aasd 4', someOtherField: 32432 },
                        { _id: '5', anyField: 'test aasd 5', someOtherField: 32432 },
                        { _id: '6', anyField: 'test aasd 6', someOtherField: 32432 },
                    ]);
                collectionValue.hasNextPage = true;
                expect(await collectionValue.getNextRecord({ recordId: '2', tableProperties })).toEqual({
                    _id: '4',
                    anyField: 'test aasd 4',
                    someOtherField: 32432,
                });
                expect(mockGetPageWithCurrentQueryArguments).toHaveBeenCalledWith({
                    tableFieldProperties: tableProperties,
                    pageSize: 20,
                    pageNumber: 1,
                    cursor: undefined,
                });
            });
        });
        // TAKE IN ACCOUNT THE ORDER BY IS anyfield: 1
        describe('getPreviousRecord', () => {
            it('should return the previous recordId of the recordId 3 orderBy anyField', async () => {
                expect(await collectionValue.getPreviousRecord({ recordId: '3' })).toEqual({
                    _id: '1',
                    anyField: 'test',
                    someOtherField: 4,
                    multiDropDownField: [],
                });
            });
            it('should return null if there is no previous record', async () => {
                expect(await collectionValue.getPreviousRecord({ recordId: '1' })).toEqual(null);
            });
            it('should return null if the recordId is not found', async () => {
                expect(await collectionValue.getPreviousRecord({ recordId: 'patata' })).toEqual(null);
            });
        });

        describe('getParentNodeAndParentIdFromChildNode', () => {
            let nestedCollectionValue: TestCollectionValue;
            beforeEach(() => {
                nestedCollectionValue = new TestCollectionValue({
                    screenId,
                    elementId: fieldId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    levelMap: { 0: 'child', 1: 'second_child' },
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                            nestedFields.numeric<any, any>({ bind: 'someOtherField' }),
                        ],
                        [
                            nestedFields.text<any, any>({ bind: 'anyField2' }),
                            nestedFields.text<any, any>({ bind: 'someOtherField2' }),
                        ],
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField3' }),
                            nestedFields.text<any, any>({ bind: 'someOtherField3' }),
                        ],
                    ],
                    nodeTypes: {
                        AnyNode: {
                            name: 'AnyNode',
                            title: 'AnyNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                children: { type: 'ChildNode', kind: 'LIST' },
                            },
                            mutations: {},
                        },
                        ChildNode: {
                            name: 'ChildNode',
                            title: 'ChildNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField2: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField2: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                                children: { type: 'SecondChildNode', kind: 'LIST' },
                            },
                            mutations: {},
                        },
                        SecondChildNode: {
                            name: 'SecondChildNode',
                            title: 'SecondChildNode',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                                _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                                _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                                anyField3: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                                someOtherField3: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                            },
                            mutations: {},
                        },
                    },
                    nodes: [
                        '@sage/xtrem-test/AnyNode',
                        '@sage/xtrem-test/ChildNode',
                        '@sage/xtrem-test/SecondChildNode',
                    ],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', __action: RecordActionType.ADDED, anyField: 'test', someOtherField: 4 },
                        {
                            _id: '2',
                            __action: RecordActionType.ADDED,
                            anyField: 'test string 2',
                            someOtherField: -5,
                        },
                        {
                            _id: '3',
                            __action: RecordActionType.ADDED,
                            anyField: 'test aasd',
                            someOtherField: 32432,
                        },
                        {
                            _id: '1',
                            __parentId: '2',
                            __level: 1,
                            __action: RecordActionType.ADDED,
                            anyField2: 'child record 1',
                            someOtherField2: -5,
                        },
                        {
                            _id: '2',
                            __parentId: '2',
                            __level: 1,
                            __action: RecordActionType.ADDED,
                            anyField2: 'child record 2',
                            someOtherField2: -5,
                        },
                        {
                            _id: '1',
                            __parentId: '1',
                            __level: 2,
                            __action: RecordActionType.ADDED,
                            anyField3: 'second child record 3',
                            someOtherField2: -5,
                        },
                        {
                            _id: '2',
                            __parentId: '1',
                            __level: 2,
                            __action: RecordActionType.ADDED,
                            anyField3: 'second child record 3',
                            someOtherField2: -5,
                        },
                        {
                            _id: '90',
                            __parentId: '1',
                            __level: 1,
                            __action: RecordActionType.ADDED,
                            anyField2: 'asdasd',
                            someOtherField2: -5,
                        },
                        {
                            _id: '43',
                            __parentId: '90',
                            __level: 2,
                            __action: RecordActionType.ADDED,
                            anyField3: 'asdasd',
                            someOtherField2: -5,
                        },
                    ],
                    fieldType: CollectionFieldTypes.NESTED_GRID,
                });
            });

            it('should return the parent node and parent id and current level from a child node', () => {
                const childNode = '@sage/xtrem-test/SecondChildNode';
                expect(nestedCollectionValue.getParentNodeAndParentIdFromChildNode({ node: childNode })).toEqual({
                    parentNode: '@sage/xtrem-test/ChildNode',
                    parentId: '90',
                    childLevel: 2,
                    parentBind: 'second_child',
                });
            });
            it('Should just return only the level if there is not parent node', () => {
                const childNode = '@sage/xtrem-test/AnyNode';
                expect(nestedCollectionValue.getParentNodeAndParentIdFromChildNode({ node: childNode })).toEqual({
                    childLevel: 0,
                });
            });
        });
    });

    describe('createNewPhantomRow', () => {
        it('should create a new phantom row', async () => {
            collectionValue = new TestCollectionValue({
                screenId,
                elementId: fieldId,
                isTransient: false,
                hasNextPage: true,
                orderBy: [{ anyField: 1 }],
                columnDefinitions: [
                    [nestedFields.text<any, any>({ bind: '_id' }), nestedFields.text<any, any>({ bind: 'anyField' })],
                ],
                nodeTypes: {
                    AnyNode: {
                        name: 'AnyNode',
                        title: 'AnyNode',
                        packageName: '@sage/xtrem-test',
                        properties: {
                            _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                            _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                            _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                            anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                        },
                        mutations: {},
                    },
                },
                nodes: ['@sage/xtrem-test/AnyNode'],
                filter: [undefined],
                initialValues: [{ _id: '1', anyField: 'test' }],
                fieldType: CollectionFieldTypes.DESKTOP_TABLE,
            });

            const phantomRow = await collectionValue.createNewPhantomRow();
            expect(phantomRow).toEqual({
                _id: '-1',
                anyField: 'default value server',
                id: '-1',
                someOtherField: '2',
            });
        });
        it('should maintain only one dirty phantom row in the db', async () => {
            collectionValue = new TestCollectionValue({
                screenId,
                elementId: fieldId,
                isTransient: false,
                hasNextPage: true,
                orderBy: [{ anyField: 1 }],
                columnDefinitions: [
                    [nestedFields.text<any, any>({ bind: '_id' }), nestedFields.text<any, any>({ bind: 'anyField' })],
                ],
                nodeTypes: {
                    AnyNode: {
                        name: 'AnyNode',
                        title: 'AnyNode',
                        packageName: '@sage/xtrem-test',
                        properties: {
                            _id: { type: 'IntReference', kind: GraphQLKind.Scalar },
                            _sortValue: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                            _action: { type: 'SystemProperties_EnumInput', kind: GraphQLKind.Scalar },
                            anyField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                        },
                        mutations: {},
                    },
                },
                nodes: ['@sage/xtrem-test/AnyNode'],
                filter: [undefined],
                initialValues: [{ _id: '1', anyField: 'test' }],
                fieldType: CollectionFieldTypes.DESKTOP_TABLE,
            });
            const phantomRow = await collectionValue.createNewPhantomRow();
            await collectionValue.setCellValue({
                recordId: phantomRow._id,
                columnId: 'anyField',
                value: 'locally modified field',
                isOrganicChange: true,
            });
            let updatedPhantomRow = collectionValue.db.findOne({
                where: { __phantom: { $eq: true }, __dirty: { $eq: true } },
            });
            expect(updatedPhantomRow).toBeTruthy();

            await collectionValue.createNewPhantomRow();

            updatedPhantomRow = collectionValue.db.findOne({
                where: { __phantom: { $eq: true }, __dirty: { $eq: true } },
            });
            expect(updatedPhantomRow).toBeFalsy();
        });
    });
});
