import { NodeCacheService } from '../node-cache-service';

jest.mock('crypto-js', () => ({
    MD5: (str: string) => str,
}));

jest.mock('../../utils/transformers', () => ({
    capitalize: (str: string) => str.charAt(0).toUpperCase() + str.slice(1),
    getArtifactDescription: (node: string) => ({ name: node }),
    sha256: async (str: string) => `hash_${str}`,
}));

jest.mock('../graphql-utils', () => ({
    queryToGraphQuery: (query: any) => JSON.stringify(query),
}));

jest.mock('../../utils/window', () => ({
    isDevMode: () => true,
    hasNodeCacheLogging: () => false,
}));

describe('NodeCacheService', () => {
    const node = 'TestNode';
    const query = 'query';
    const result = { data: 123 };

    beforeEach(() => {
        (window as any).__XTREM_NODE_CACHE = {};
    });

    afterEach(() => {
        NodeCacheService.flushCache();
    });

    it('set and get should store and retrieve a value', async () => {
        await NodeCacheService.set(node, query, result);
        const cached = await NodeCacheService.get(node, query);
        expect(cached).toEqual(result);
        expect((window as any).__XTREM_NODE_CACHE).toBeDefined();
        expect((window as any).__XTREM_NODE_CACHE[node]).toBeDefined();
        expect((window as any).__XTREM_NODE_CACHE[node][query]).toEqual(result);
    });

    it('get should return undefined for missing entry', async () => {
        const cached = await NodeCacheService.get(node, query);
        expect(cached).toBeUndefined();
        expect((window as any).__XTREM_NODE_CACHE).toBeDefined();
        expect((window as any).__XTREM_NODE_CACHE[node]).toBeUndefined();
    });

    it('flushCacheAfterMutation should clear cache for mutated nodes', async () => {
        await NodeCacheService.set('TestNode', query, result);
        await NodeCacheService.set('OtherNode', query, { data: 456 });

        expect((window as any).__XTREM_NODE_CACHE.TestNode).toBeDefined();
        expect((window as any).__XTREM_NODE_CACHE.OtherNode).toBeDefined();

        NodeCacheService.flushCacheAfterMutation({
            somePkg: { testNode: true },
        });

        const cachedTest = await NodeCacheService.get('TestNode', query);
        const cachedOther = await NodeCacheService.get('OtherNode', query);

        expect(cachedTest).toBeUndefined();
        expect(cachedOther).toEqual({ data: 456 });
        expect((window as any).__XTREM_NODE_CACHE.TestNode).toBeUndefined();
        expect((window as any).__XTREM_NODE_CACHE.OtherNode).toBeDefined();
    });

    it('set should overwrite existing value', async () => {
        await NodeCacheService.set(node, query, result);
        expect((window as any).__XTREM_NODE_CACHE[node]).toBeDefined();
        expect((window as any).__XTREM_NODE_CACHE[node][query]).toEqual(result);

        await NodeCacheService.set(node, query, { data: 999 });
        const cached = await NodeCacheService.get(node, query);

        expect(cached).toEqual({ data: 999 });
        expect((window as any).__XTREM_NODE_CACHE[node]).toBeDefined();
        expect((window as any).__XTREM_NODE_CACHE[node][query]).toEqual({ data: 999 });
    });

    it('should handle string queries', async () => {
        const strQuery = 'foo=bar';
        await NodeCacheService.set(node, strQuery, result);
        const cached = await NodeCacheService.get(node, strQuery);

        expect(cached).toEqual(result);
        expect((window as any).__XTREM_NODE_CACHE[node]).toBeDefined();
        expect((window as any).__XTREM_NODE_CACHE[node][strQuery]).toEqual(result);
    });

    it('flushCache should update __XTREM_NODE_CACHE in dev mode', async () => {
        await NodeCacheService.set(node, query, result);
        expect((window as any).__XTREM_NODE_CACHE).toBeDefined();
        expect((window as any).__XTREM_NODE_CACHE[node]).toBeDefined();

        NodeCacheService.flushCache();
        expect((window as any).__XTREM_NODE_CACHE).toBeDefined();
        expect((window as any).__XTREM_NODE_CACHE[node]).toBeUndefined();
    });

    it('flushCache should clear specific nodes when nodeNames is provided', async () => {
        await NodeCacheService.set('Node1', query, { data: 1 });
        await NodeCacheService.set('Node2', query, { data: 2 });

        expect((window as any).__XTREM_NODE_CACHE.Node1).toBeDefined();
        expect((window as any).__XTREM_NODE_CACHE.Node2).toBeDefined();

        NodeCacheService.flushCache(['Node1']);

        const cachedNode1 = await NodeCacheService.get('Node1', query);
        const cachedNode2 = await NodeCacheService.get('Node2', query);

        expect(cachedNode1).toBeUndefined();
        expect(cachedNode2).toEqual({ data: 2 });
        expect((window as any).__XTREM_NODE_CACHE.Node1).toBeUndefined();
        expect((window as any).__XTREM_NODE_CACHE.Node2).toBeDefined();
    });

    it('flushCache should clear all nodes when nodeNames is not provided', async () => {
        await NodeCacheService.set('Node1', query, { data: 1 });
        await NodeCacheService.set('Node2', query, { data: 2 });

        expect((window as any).__XTREM_NODE_CACHE.Node1).toBeDefined();
        expect((window as any).__XTREM_NODE_CACHE.Node2).toBeDefined();

        NodeCacheService.flushCache();

        const cachedNode1 = await NodeCacheService.get('Node1', query);
        const cachedNode2 = await NodeCacheService.get('Node2', query);

        expect(cachedNode1).toBeUndefined();
        expect(cachedNode2).toBeUndefined();
        expect((window as any).__XTREM_NODE_CACHE.Node1).toBeUndefined();
        expect((window as any).__XTREM_NODE_CACHE.Node2).toBeUndefined();
    });

    it('set should clone the input object to prevent mutations', async () => {
        const inputObject = { data: { nested: { value: 123 } } };
        await NodeCacheService.set(node, query, inputObject);
        inputObject.data.nested.value = 456;
        const cached = await NodeCacheService.get(node, query);
        expect(cached).toEqual({ data: { nested: { value: 123 } } });
        expect((window as any).__XTREM_NODE_CACHE[node][query]).toEqual({ data: { nested: { value: 123 } } });
    });

    it('get should return a clone of the cached object to prevent mutations', async () => {
        const inputObject = { data: { nested: { value: 123 } } };
        await NodeCacheService.set(node, query, inputObject);
        const cached = await NodeCacheService.get(node, query);
        cached.data.nested.value = 456;
        const cachedAgain = await NodeCacheService.get(node, query);
        expect(cachedAgain).toEqual({ data: { nested: { value: 123 } } });
        expect((window as any).__XTREM_NODE_CACHE[node][query]).toEqual({ data: { nested: { value: 123 } } });
    });

    it('should handle array cloning in set and get operations', async () => {
        const inputArray = [{ id: 1, data: [1, 2, 3] }];
        await NodeCacheService.set(node, query, inputArray);
        const cached = await NodeCacheService.get(node, query);
        cached[0].id = 2;
        cached[0].data.push(4);
        const cachedAgain = await NodeCacheService.get(node, query);
        expect(cachedAgain).toEqual([{ id: 1, data: [1, 2, 3] }]);
        expect((window as any).__XTREM_NODE_CACHE[node][query]).toEqual([{ id: 1, data: [1, 2, 3] }]);
    });
});
