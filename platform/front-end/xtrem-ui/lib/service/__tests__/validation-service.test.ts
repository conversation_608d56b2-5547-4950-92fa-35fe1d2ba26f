import {
    addBlockToState,
    addFieldToState,
    addSectionToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../__tests__/test-helpers';

import type { AccessBindings } from '../page-definition';
import type { XtremAppState } from '../../redux';
import { FieldKey } from '../../component/types';
import { getScreenElement } from '../screen-base-definition';
import {
    checkValidationStatus,
    executeValidationRulesOnField,
    getSectionValidationMessage,
} from '../validation-service';
import { nestedFields } from '../..';
import { CollectionValue } from '../collection-data-service';
import { GraphQLKind } from '../../types';
import { GraphQLTypes } from '@sage/xtrem-shared';

describe('Validate action', () => {
    const screenId = 'TestPage';
    const elementId = 'fieldId';
    const nodeTypes = {
        MyTestNode: {
            name: 'MyTestNode',
            title: 'MyTestNode',
            packageName: '@sage/xtrem-test',
            properties: {
                [elementId]: {
                    name: elementId,
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
                aTextField: {
                    name: 'aTextField',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
                aNumericField: {
                    name: 'aNumericField',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.Decimal,
                },
                field1: {
                    name: 'field1',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
                field2: {
                    name: 'field2',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
                block1: {
                    name: 'block1',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
                section1: {
                    name: 'section1',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
            },
            mutations: {},
        },
        AnyNode: {
            name: 'AnyNode',
            title: 'AnyNode',
            packageName: '@sage/xtrem-test',
            properties: {
                [elementId]: {
                    name: elementId,
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
                aTextField: {
                    name: 'aTextField',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
                field1: {
                    name: 'field1',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
                field2: {
                    name: 'field2',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
                block1: {
                    name: 'block1',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
                section1: {
                    name: 'section1',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
                section2: {
                    name: 'section2',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                },
                aNumericField: {
                    name: 'aNumericField',
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.Decimal,
                },
            },
            mutations: {},
        },
    };
    let state: XtremAppState;

    const runValidation = (state: XtremAppState, value: any, accessBindings: AccessBindings = {}) => {
        return checkValidationStatus(
            getScreenElement(state.screenDefinitions[screenId]),
            elementId,
            state.screenDefinitions[screenId].metadata.uiComponentProperties,
            value,
            accessBindings,
            nodeTypes,
            {},
            '@sage/xtrem-test/MyTestNode',
        );
    };

    beforeEach(() => {
        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] = {
            node: '@sage/xtrem-test/MyTestNode',
            valueField: 'name',
        } as any;
        getMockStore(state);
    });

    describe('Is mandatory check', () => {
        it('should return an error if the fields is mandatory and the value is undefined', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: true,
            });

            expect(await runValidation(state, undefined)).toContainEqual({
                elementId,
                screenId,
                validationRule: 'isMandatory',
                message: 'You need to enter a value.',
            });
        });

        it('should return an error if the fields is mandatory and the value is an empty array', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: true,
            });

            expect(await runValidation(state, [])).toContainEqual({
                elementId,
                screenId,
                validationRule: 'isMandatory',
                message: 'You need to enter a value.',
            });
        });

        it('should return an error if the fields is mandatory and the value is an array with a value', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: true,
            });

            expect(await runValidation(state, [{ _id: '123', name: 'John Doe' }])).toContainEqual({
                elementId,
                screenId,
                validationRule: '',
                message: '',
            });
        });

        it('should return a valid state if the field is unauthorized', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: true,
            });

            expect(
                await runValidation(state, undefined, {
                    MyTestNode: { [elementId]: 'unauthorized' },
                }),
            ).toContainEqual({
                elementId,
                screenId,
                validationRule: '',
                message: '',
            });
        });

        it('should return a valid state if the field is unavailable', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: true,
            });

            expect(
                await runValidation(state, undefined, {
                    MyTestNode: { [elementId]: 'unavailable' },
                }),
            ).toContainEqual({
                elementId,
                screenId,
                validationRule: '',
                message: '',
            });
        });

        it('should return an invalid state if the element is invalid and rendered into an authorized element structure', async () => {
            addSectionToState(state, screenId, 'section1');
            addBlockToState(state, screenId, 'block1', {
                parent: () => state.screenDefinitions[screenId].metadata.controlObjects.section1 as any,
            });
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: true,
                parent: () => state.screenDefinitions[screenId].metadata.controlObjects.block1 as any,
            });

            expect(
                await runValidation(state, undefined, {
                    MyTestNode: {
                        [elementId]: 'authorized',
                        block1: 'authorized',
                        section1: 'authorized',
                    },
                }),
            ).toContainEqual({
                elementId,
                screenId,
                validationRule: 'isMandatory',
                message: 'You need to enter a value.',
            });
        });

        it('should return an invalid state the element is invalid into an unspecified access right element structure', async () => {
            addSectionToState(state, screenId, 'section1');
            addBlockToState(state, screenId, 'block1', {
                parent: () => state.screenDefinitions[screenId].metadata.controlObjects.section1 as any,
            });
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: true,
                parent: () => state.screenDefinitions[screenId].metadata.controlObjects.block1 as any,
            });

            expect(await runValidation(state, undefined, {})).toContainEqual({
                elementId,
                screenId,
                validationRule: 'isMandatory',
                message: 'You need to enter a value.',
            });
        });

        it('should return a valid state if the field is rendered into an unavailable block ', async () => {
            addSectionToState(state, screenId, 'section1');
            addBlockToState(state, screenId, 'block1', {
                parent: () => state.screenDefinitions[screenId].metadata.controlObjects.section1 as any,
            });
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: true,
                parent: () => state.screenDefinitions[screenId].metadata.controlObjects.block1 as any,
            });

            expect(
                await runValidation(state, undefined, {
                    MyTestNode: { [elementId]: 'authorized', block1: 'unavailable' },
                }),
            ).toContainEqual({
                elementId,
                screenId,
                validationRule: '',
                message: '',
            });
        });

        it('should return a valid state if the field is rendered into an unavailable section ', async () => {
            addSectionToState(state, screenId, 'section1');
            addBlockToState(state, screenId, 'block1', {
                parent: () => state.screenDefinitions[screenId].metadata.controlObjects.section1 as any,
            });
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: true,
                parent: () => state.screenDefinitions[screenId].metadata.controlObjects.block1 as any,
            });

            expect(
                await runValidation(state, undefined, {
                    MyTestNode: { [elementId]: 'authorized', section1: 'unavailable' },
                }),
            ).toContainEqual({
                elementId,
                screenId,
                validationRule: '',
                message: '',
            });
        });

        it('should return a valid state if the field is rendered into an unauthorized block ', async () => {
            addSectionToState(state, screenId, 'section1');
            addBlockToState(state, screenId, 'block1', {
                parent: () => state.screenDefinitions[screenId].metadata.controlObjects.section1 as any,
            });
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: true,
                parent: () => state.screenDefinitions[screenId].metadata.controlObjects.block1 as any,
            });

            expect(
                await runValidation(state, undefined, {
                    MyTestNode: { [elementId]: 'authorized', block1: 'unauthorized' },
                }),
            ).toContainEqual({
                elementId,
                screenId,
                validationRule: '',
                message: '',
            });
        });

        it('should return a valid state if the field is rendered into an unauthorized section ', async () => {
            addSectionToState(state, screenId, 'section1');
            addBlockToState(state, screenId, 'block1', {
                parent: () => state.screenDefinitions[screenId].metadata.controlObjects.section1 as any,
            });
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: true,
                parent: () => state.screenDefinitions[screenId].metadata.controlObjects.block1 as any,
            });

            expect(await runValidation(state, undefined, { MyTestNode: { section1: 'unauthorized' } })).toContainEqual({
                elementId,
                screenId,
                validationRule: '',
                message: '',
            });
        });

        it('should return an error if the field is mandatory by a callback definition and the value is undefined', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory() {
                    return true;
                },
            });

            expect(await runValidation(state, undefined)).toContainEqual({
                elementId,
                screenId,
                validationRule: 'isMandatory',
                message: 'You need to enter a value.',
            });
        });

        it('should not return an error if the field is not mandatory by a callback definition and the value is undefined', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory() {
                    return false;
                },
            });

            expect(await runValidation(state, undefined)).toContainEqual({
                elementId,
                screenId,
                validationRule: '',
                message: '',
            });
        });

        it('should not return an error message because the field is mandatory but has a value', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: true,
            });

            expect(await runValidation(state, 'I am the value!')).toContainEqual({
                elementId,
                screenId,
                message: '',
                validationRule: '',
            });
        });

        it('should not return an error message because the field is mandatory by a callback definition but has a value', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory() {
                    return true;
                },
            });

            expect(await runValidation(state, 'I am the value!')).toContainEqual({
                elementId,
                screenId,
                message: '',
                validationRule: '',
            });
        });

        it('should not return an error if the value is unset because the field is not mandatory', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                isMandatory: false,
            });
            getMockStore(state);

            expect(await runValidation(state, undefined)).toContainEqual({
                elementId,
                screenId,
                message: '',
                validationRule: '',
            });
        });

        it('should return a validation error if the value is an empty array', async () => {
            addFieldToState<FieldKey.MultiReference>(
                FieldKey.MultiReference,
                state,
                screenId,
                elementId,
                {
                    isMandatory: true,
                    node: '@sage/xtrem-test/AnyNode',
                    valueField: 'name',
                },
                [],
            );
            getMockStore(state);

            expect(await runValidation(state, undefined)).toContainEqual({
                elementId,
                screenId,
                validationRule: 'isMandatory',
                message: 'You need to select or enter a value.',
            });
        });
    });

    describe('Numeric validation check', () => {
        it('should return an error if the minimum value is less than the validation rule', async () => {
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Numeric, state, screenId, elementId, {
                min: 5,
            });

            expect(await runValidation(state, 1)).toContainEqual({
                elementId,
                screenId,
                message: 'fieldId minimum value is 5',
                validationRule: 'min',
            });
        });

        it('should return an error if the isNotZero property is set and the value is zero', async () => {
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Numeric, state, screenId, elementId, {
                isNotZero: true,
            });

            expect(await runValidation(state, 0)).toContainEqual({
                elementId,
                screenId,
                message: 'fieldId cannot be zero',
                validationRule: 'isNotZero',
            });
        });

        it('should return an error if the isNotZero property is set by a callback and the value is zero', async () => {
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Numeric, state, screenId, elementId, {
                isNotZero() {
                    return true;
                },
            });

            expect(await runValidation(state, 0)).toContainEqual({
                elementId,
                screenId,
                message: 'fieldId cannot be zero',
                validationRule: 'isNotZero',
            });
        });

        it('should not return an error if the isNotZero property is set to false by a callback and the value is zero', async () => {
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Numeric, state, screenId, elementId, {
                isNotZero() {
                    return false;
                },
            });

            expect(await runValidation(state, 0)).toContainEqual({
                elementId,
                screenId,
                message: '',
                validationRule: '',
            });
        });

        it('should return an error if the minimum value defined by a callback is less than the validation rule', async () => {
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Numeric, state, screenId, elementId, {
                min() {
                    return 19;
                },
            });

            expect(await runValidation(state, 1)).toContainEqual({
                elementId,
                screenId,
                message: 'fieldId minimum value is 19',
                validationRule: 'min',
            });
        });

        it('should return an error of the maximum value is greater than the validation rule', async () => {
            addFieldToState(FieldKey.Numeric, state, screenId, elementId, {
                max: 10,
            });

            expect(await runValidation(state, 20)).toContainEqual({
                elementId,
                screenId,
                message: 'fieldId maximum value is 10',
                validationRule: 'max',
            });
        });

        it('should return an error of the maximum value defined by a callback is greater than the validation rule', async () => {
            addFieldToState(FieldKey.Numeric, state, screenId, elementId, {
                max() {
                    return 15;
                },
            });

            expect(await runValidation(state, 20)).toContainEqual({
                elementId,
                screenId,
                message: 'fieldId maximum value is 15',
                validationRule: 'max',
            });
        });

        it('should not return an error if the value is within the validation range', async () => {
            addFieldToState(FieldKey.Numeric, state, screenId, elementId, {
                min: 5,
                max: 10,
            });

            expect(await runValidation(state, undefined)).toContainEqual({
                elementId,
                screenId,
                message: '',
                validationRule: '',
            });
        });
    });

    describe('Min length', () => {
        it('should return an error of the minimum length value is less than the validation rule', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                minLength: 40,
            });

            expect(await runValidation(state, 'test string')).toContainEqual({
                elementId,
                screenId,
                message: 'fieldId minimum length is 40',
                validationRule: 'minLength',
            });
        });

        it('should return an error of the minimum length value is less than the validation rule set by a callback', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                minLength() {
                    return 32;
                },
            });

            expect(await runValidation(state, 'test string')).toContainEqual({
                elementId,
                screenId,
                message: 'fieldId minimum length is 32',
                validationRule: 'minLength',
            });
        });

        it('should not return an error of the minimum length value is greater than the validation rule set by a callback', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                minLength() {
                    return 5;
                },
            });

            expect(await runValidation(state, 'test string')).toContainEqual({
                elementId,
                screenId,
                message: '',
                validationRule: '',
            });
        });
    });

    describe('Max length', () => {
        it('should return an error of the maximum length value is greater than the validation rule', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                maxLength: 7,
            });

            expect(await runValidation(state, 'test string')).toContainEqual({
                elementId,
                screenId,
                message: 'fieldId maximum length is 7',
                validationRule: 'maxLength',
            });
        });

        it('should return an error of the maximum length value is greater than the validation rule set by a callback', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                maxLength() {
                    return 4;
                },
            });

            expect(await runValidation(state, 'test string')).toContainEqual({
                elementId,
                screenId,
                message: 'fieldId maximum length is 4',
                validationRule: 'maxLength',
            });
        });

        it('should not return an error of the maximum length value is less than the validation rule set by a callback', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                maxLength() {
                    return 54;
                },
            });

            expect(await runValidation(state, 'test string')).toContainEqual({
                elementId,
                screenId,
                message: '',
                validationRule: '',
            });
        });
    });

    describe('Date validation check', () => {
        it('should return an error if the minimum date is before the validation rule', async () => {
            const minDate = '2018-01-01';

            addFieldToState(FieldKey.Date, state, screenId, elementId, {
                minDate,
            });

            expect(await runValidation(state, '2017-01-01')).toContainEqual({
                elementId,
                screenId,
                message: `${elementId} minimum date is 01/01/2018`,
                validationRule: 'minDate',
            });
        });

        it('should return an error if the minimum date is after the validation rule', async () => {
            const maxDate = new Date('2018-01-01');

            addFieldToState(FieldKey.Date, state, screenId, elementId, {
                maxDate,
            });

            expect(await runValidation(state, new Date('2019-01-01'))).toContainEqual({
                elementId,
                screenId,
                message: `${elementId} maximum date is 01/01/2018`,
                validationRule: 'maxDate',
            });
        });
    });

    describe('Regexp validation check', () => {
        it("should return an error if the field value doesn't match the regular expression", async () => {
            addFieldToState(FieldKey.Numeric, state, screenId, elementId, {
                validation: /^([1-9]|10)$/,
            });

            expect(await runValidation(state, 12)).toContainEqual({
                elementId,
                screenId,
                message: `${elementId} is not valid`,
                validationRule: 'validation',
            });
        });

        it("should not return an error if the field's value matches the regular expression", async () => {
            addFieldToState(FieldKey.Numeric, state, screenId, elementId, {
                validation: /^([1-9]|10)$/,
            });
            const result = await runValidation(state, 5);
            expect(result).toContainEqual({
                elementId,
                screenId,
                message: '',
                validationRule: '',
            });
        });
    });

    describe('Custom validation function check', () => {
        it("should return an error if the value doesn't pass the custom validation rule", async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                validation: (value: string) => {
                    return value !== 'test' ? 'The text should be test' : undefined;
                },
            });

            expect(await runValidation(state, 'coffee')).toContainEqual({
                elementId,
                screenId,
                message: 'The text should be test',
                validationRule: 'validation',
            });
        });
        it("should return an error if the value doesn't pass the custom async validation rule", async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                validation: (value: string) =>
                    new Promise(resolve => {
                        resolve(value !== 'test' ? 'The text should be test' : undefined);
                    }),
            });

            expect(await runValidation(state, 'coffee')).toContainEqual({
                elementId,
                screenId,
                message: 'The text should be test',
                validationRule: 'validation',
            });
        });

        it('should return an error if the async validation rule fails', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                validation: () =>
                    new Promise((resolve, reject) => {
                        reject(new Error('this meant to fail'));
                    }),
            });

            expect(await runValidation(state, 'coffee')).toContainEqual({
                elementId,
                screenId,
                message: 'this meant to fail',
                validationRule: 'validation',
            });
        });

        it('should not return an error message if the value passes the custom validation rule', async () => {
            addFieldToState(FieldKey.Text, state, screenId, elementId, {
                validation: (value: string) => {
                    return value !== 'test' ? 'The text should be test' : undefined;
                },
            });

            expect(await runValidation(state, 'test')).toContainEqual({
                elementId,
                screenId,
                message: '',
                validationRule: '',
            });
        });

        it('should call validate() on the value if it is a CollectionValue object to validate all nested records', async () => {
            const columnDefinitions = [
                nestedFields.text<any>({
                    bind: 'aTextField',
                    title: 'A text field',
                }),
                nestedFields.numeric<any>({
                    bind: 'aNumericField',
                    title: 'A numeric field',
                }),
            ];

            const fieldValue = new CollectionValue({
                columnDefinitions: [columnDefinitions],
                nodes: ['@sage/xtrem-test/AnyNode'],
                nodeTypes: {},
                elementId,
                screenId,
                hasNextPage: false,
                initialValues: [
                    {
                        _id: '1',
                        aTextField: 'text field content',
                        aNumericField: 2,
                        __validationState: {
                            aTextField: {
                                elementId,
                                screenId,
                                columnId: 'aTextField',
                                message: 'some random message',
                                validationRule: 'some random rule',
                            },
                        },
                    },
                ],
                isTransient: false,
            });

            addFieldToState(
                FieldKey.Table,
                state,
                screenId,
                elementId,
                {
                    node: '@sage/xtrem-test/AnyNode',
                    columns: columnDefinitions,
                    mainField: 'aTextField',
                    title: 'Table title',
                },
                fieldValue,
            );

            const validationResult = await runValidation(state, fieldValue);
            expect(validationResult.length).toEqual(1);
            expect(validationResult[0].elementId).toEqual(elementId);
            expect(validationResult[0].screenId).toEqual(screenId);
            expect(validationResult[0].validationRule).toEqual('some random rule');
            expect(validationResult[0].message).toEqual('some random message');
            expect(validationResult[0].messagePrefix).toEqual('A text field: text field content - A text field');
        });

        it('should call validate() on the value if it is a CollectionValue object to validate all nested records and return empty if no errors found', async () => {
            const columnDefinitions = [
                nestedFields.text<any>({
                    bind: 'aTextField',
                    title: 'A text field',
                }),
                nestedFields.numeric<any>({
                    bind: 'aNumericField',
                    title: 'A numeric field',
                }),
            ];

            const fieldValue = new CollectionValue({
                columnDefinitions: [columnDefinitions],
                nodes: ['@sage/xtrem-test/AnyNode'],
                nodeTypes: {},
                elementId,
                screenId,
                hasNextPage: false,
                initialValues: [
                    {
                        _id: '1',
                        aTextField: 'text field content',
                        aNumericField: 2,
                    },
                ],
                isTransient: false,
            });

            addFieldToState(
                FieldKey.Table,
                state,
                screenId,
                elementId,
                {
                    node: '@sage/xtrem-test/AnyNode',
                    columns: columnDefinitions,
                    mainField: 'aTextField',
                    title: 'Table title',
                },
                fieldValue,
            );

            const validationResult = await runValidation(state, fieldValue);
            expect(validationResult.length).toEqual(1);
            expect(validationResult[0].message).toEqual('');
        });

        it('should call validate() on the value if it is a CollectionValue object to validate all nested records, excluding server side errors', async () => {
            const columnDefinitions = [
                nestedFields.text<any>({
                    bind: 'aTextField',
                    title: 'A text field',
                }),
                nestedFields.numeric<any>({
                    bind: 'aNumericField',
                    title: 'A numeric field',
                }),
            ];

            const fieldValue = new CollectionValue({
                columnDefinitions: [columnDefinitions],
                nodes: ['@sage/xtrem-test/AnyNode'],
                nodeTypes: {},
                elementId,
                screenId,
                hasNextPage: false,
                initialValues: [
                    {
                        _id: '1',
                        aTextField: 'text field content',
                        aNumericField: 2,
                        __validationState: {
                            aTextField: {
                                elementId,
                                screenId,
                                columnId: 'aTextField',
                                message: 'some random message',
                                validationRule: 'server-business-rule',
                            },
                        },
                    },
                ],
                isTransient: false,
            });

            addFieldToState(
                FieldKey.Table,
                state,
                screenId,
                elementId,
                {
                    node: '@sage/xtrem-test/AnyNode',
                    columns: columnDefinitions,
                    mainField: 'aTextField',
                    title: 'Table title',
                },
                fieldValue,
            );

            const validationResult = await runValidation(state, fieldValue);
            expect(validationResult.length).toEqual(1);
            expect(validationResult[0].message).toEqual('');
        });
    });

    describe('nested validations with executeValidationRulesOnField', () => {
        const elementId = 'someParentField';
        const columnId = 'someColumnId';
        const recordId = '1234';

        const basicValidationProperties = {
            screenId,
            elementId,
            recordId,
            columnId,
        };

        it('isMandatory callback should have row value as its first argument and validation logic can be based on that', async () => {
            const mockCallbackImplementation: any = jest.fn((rowValue: any) => {
                if (rowValue.someOtherColumn === 'FAIL') {
                    return true;
                }
                return false;
            });

            expect(mockCallbackImplementation).not.toHaveBeenCalled();
            let result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: undefined,
                rowValue: {
                    someColumnId: undefined,
                    someOtherColumn: 'FAIL',
                },
                fieldProperties: {
                    isMandatory: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(1);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: 'You need to enter a value.',
                validationRule: 'isMandatory',
            });

            result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: undefined,
                rowValue: {
                    someColumnId: undefined,
                    someOtherColumn: 'NO FAIL',
                },
                fieldProperties: {
                    isMandatory: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(2);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: '',
                validationRule: '',
            });
        });

        it('isNotZero callback should have row value as its first argument and validation logic can be based on that', async () => {
            const mockCallbackImplementation: any = jest.fn((rowValue: any) => {
                if (rowValue.someOtherColumn === 'FAIL') {
                    return true;
                }
                return false;
            });
            expect(mockCallbackImplementation).not.toHaveBeenCalled();

            let result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: 0,
                rowValue: {
                    someColumnId: 0,
                    someOtherColumn: 'FAIL',
                },
                fieldProperties: {
                    isNotZero: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(1);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: 'someColumnId cannot be zero',
                validationRule: 'isNotZero',
            });

            result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: 0,
                rowValue: {
                    someColumnId: 0,
                    someOtherColumn: 'NO FAIL',
                },
                fieldProperties: {
                    isNotZero: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(2);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: '',
                validationRule: '',
            });
        });

        it('min value callback should have row value as its first argument and validation logic can be based on that', async () => {
            const mockCallbackImplementation: any = jest.fn((rowValue: any) => {
                return rowValue.someOtherColumn;
            });

            expect(mockCallbackImplementation).not.toHaveBeenCalled();

            let result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: 1,
                rowValue: {
                    someColumnId: 1,
                    someOtherColumn: 2,
                },
                fieldProperties: {
                    min: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(1);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: 'someColumnId minimum value is 2',
                validationRule: 'min',
            });

            result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: 1,
                rowValue: {
                    someColumnId: 1,
                    someOtherColumn: 0,
                },
                fieldProperties: {
                    min: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(2);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: '',
                validationRule: '',
            });
        });

        it('max value callback should have row value as its first argument and validation logic can be based on that', async () => {
            const mockCallbackImplementation: any = jest.fn((rowValue: any) => {
                return rowValue.someOtherColumn;
            });
            expect(mockCallbackImplementation).not.toHaveBeenCalled();

            let result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: 1,
                rowValue: {
                    someColumnId: 1,
                    someOtherColumn: 0,
                },
                fieldProperties: {
                    max: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(1);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: 'someColumnId maximum value is 0',
                validationRule: 'max',
            });

            result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: 1,
                rowValue: {
                    someColumnId: 1,
                    someOtherColumn: 2,
                },
                fieldProperties: {
                    max: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(2);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: '',
                validationRule: '',
            });
        });

        it('minLength value callback should have row value as its first argument and validation logic can be based on that', async () => {
            const mockCallbackImplementation: any = jest.fn((rowValue: any) => {
                return rowValue.someOtherColumn + 2;
            });
            expect(mockCallbackImplementation).not.toHaveBeenCalled();

            let result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: 'test string',
                rowValue: {
                    someColumnId: 'test string',
                    someOtherColumn: 20,
                },
                fieldProperties: {
                    minLength: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(1);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: 'someColumnId minimum length is 22',
                validationRule: 'minLength',
            });

            result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: 'test string',
                rowValue: {
                    someColumnId: 'test string',
                    someOtherColumn: 5,
                },
                fieldProperties: {
                    minLength: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(2);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: '',
                validationRule: '',
            });
        });

        it('maxLength value callback should have row value as its first argument and validation logic can be based on that', async () => {
            const mockCallbackImplementation: any = jest.fn((rowValue: any) => {
                return rowValue.someOtherColumn + 2;
            });
            expect(mockCallbackImplementation).not.toHaveBeenCalled();

            let result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: 'test string',
                rowValue: {
                    someColumnId: 'test string',
                    someOtherColumn: 4,
                },
                fieldProperties: {
                    maxLength: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(1);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: 'someColumnId maximum length is 6',
                validationRule: 'maxLength',
            });

            result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: 'test string',
                rowValue: {
                    someColumnId: 'test string',
                    someOtherColumn: 20,
                },
                fieldProperties: {
                    maxLength: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(2);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: '',
                validationRule: '',
            });
        });

        it('minDate value callback should have row value as its first argument and validation logic can be based on that', async () => {
            const mockCallbackImplementation: any = jest.fn((rowValue: any) => {
                return rowValue.someOtherColumn;
            });
            expect(mockCallbackImplementation).not.toHaveBeenCalled();

            let result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: '2021-09-15',
                rowValue: {
                    someColumnId: '2021-09-15',
                    someOtherColumn: '2021-09-17',
                },
                fieldProperties: {
                    minDate: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(1);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: 'someColumnId minimum date is 17/09/2021',
                validationRule: 'minDate',
            });

            result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: '2021-09-15',
                rowValue: {
                    someColumnId: '2021-09-15',
                    someOtherColumn: '2021-09-10',
                },
                fieldProperties: {
                    minDate: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(2);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: '',
                validationRule: '',
            });
        });

        it('maxDate value callback should have row value as its first argument and validation logic can be based on that', async () => {
            const mockCallbackImplementation: any = jest.fn((rowValue: any) => {
                return rowValue.someOtherColumn;
            });
            expect(mockCallbackImplementation).not.toHaveBeenCalled();

            let result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: '2021-09-15',
                rowValue: {
                    someColumnId: '2021-09-15',
                    someOtherColumn: '2021-09-11',
                },
                fieldProperties: {
                    maxDate: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(1);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: 'someColumnId maximum date is 11/09/2021',
                validationRule: 'maxDate',
            });

            result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: '2021-09-15',
                rowValue: {
                    someColumnId: '2021-09-15',
                    someOtherColumn: '2021-09-19',
                },
                fieldProperties: {
                    maxDate: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(2);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: '',
                validationRule: '',
            });
        });

        it('validation property callback should have the value and the row value as arguments', async () => {
            const mockCallbackImplementation: any = jest.fn((fieldValue: any, rowValue: any) => {
                if (rowValue.someOtherColumn === fieldValue) {
                    return 'oops this not allowed!';
                }

                return undefined;
            });

            expect(mockCallbackImplementation).not.toHaveBeenCalled();

            let result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: 1,
                rowValue: {
                    someColumnId: 1,
                    someOtherColumn: 1,
                },
                fieldProperties: {
                    validation: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(1);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: 'oops this not allowed!',
                validationRule: 'validation',
            });

            result = await executeValidationRulesOnField({
                ...basicValidationProperties,
                value: 1,
                rowValue: {
                    someColumnId: 1,
                    someOtherColumn: 2,
                },
                fieldProperties: {
                    validation: mockCallbackImplementation,
                },
            });
            expect(mockCallbackImplementation).toHaveBeenCalledTimes(2);

            expect(result).toContainEqual({
                ...basicValidationProperties,
                message: '',
                validationRule: '',
            });
        });
    });

    describe('getSectionValidationMessage', () => {
        beforeEach(() => {
            const getCo = (elementId: string): any =>
                state.screenDefinitions[screenId].metadata.controlObjects[elementId];
            addSectionToState(state, screenId, 'section1');
            addBlockToState(state, screenId, 'block1', { parent: () => getCo('section1') });
            addSectionToState(state, screenId, 'section2');
            addBlockToState(state, screenId, 'block2', { parent: () => getCo('section2') });

            addFieldToState(FieldKey.Text, state, screenId, 'field1', {
                parent: () => getCo('block1'),
            });
            addFieldToState(FieldKey.Text, state, screenId, 'field2', {
                parent: () => getCo('block1'),
            });
            addFieldToState(FieldKey.Text, state, screenId, 'field3', {
                parent: () => getCo('block2'),
            });
            addFieldToState(FieldKey.Text, state, screenId, 'field4', {
                parent: () => getCo('block2'),
            });
        });

        it('should return null if the container is valid', () => {
            expect(getSectionValidationMessage(screenId, 'section1', state)).toEqual(null);
        });

        it('should return an error message if a child field in the hierarchy is invalid without a title', () => {
            state.screenDefinitions[screenId].errors.field1 = [
                {
                    screenId,
                    elementId: 'field1',
                    message: 'Something is wrong here',
                    validationRule: 'my-own-rule',
                },
            ];
            expect(getSectionValidationMessage(screenId, 'section1', state)).toEqual('1 error');
            expect(getSectionValidationMessage(screenId, 'section2', state)).toEqual(null);
        });

        it('should return an error message if a child field in the hierarchy is invalid with a title set', () => {
            state.screenDefinitions[screenId].errors.field1 = [
                {
                    screenId,
                    elementId: 'field1',
                    message: 'Something is wrong here',
                    validationRule: 'my-own-rule',
                },
            ];
            state.screenDefinitions[screenId].metadata.uiComponentProperties.field1.title = 'Look a title';
            expect(getSectionValidationMessage(screenId, 'section1', state)).toEqual('1 error');
            expect(getSectionValidationMessage(screenId, 'section2', state)).toEqual(null);
        });

        it('should return an error message if a child field in the hierarchy is invalid with a title callback function', () => {
            state.screenDefinitions[screenId].errors.field1 = [
                {
                    screenId,
                    elementId: 'field1',
                    message: 'Something is wrong here',
                    validationRule: 'my-own-rule',
                },
            ];
            state.screenDefinitions[screenId].metadata.uiComponentProperties.field1.title = () =>
                'Look a callback title';
            expect(getSectionValidationMessage(screenId, 'section1', state)).toEqual('1 error');
            expect(getSectionValidationMessage(screenId, 'section2', state)).toEqual(null);
        });

        it('should return a joint error message if a child fields in the hierarchy are invalid', () => {
            state.screenDefinitions[screenId].errors.field1 = [
                {
                    screenId,
                    elementId: 'field1',
                    message: 'Something is wrong here',
                    validationRule: 'my-own-rule',
                },
            ];
            state.screenDefinitions[screenId].errors.field2 = [
                {
                    screenId,
                    elementId: 'field2',
                    message: 'Something is wrong here too',
                    validationRule: 'my-own-rule',
                },
            ];
            state.screenDefinitions[screenId].errors.field3 = [
                {
                    screenId,
                    elementId: 'field3',
                    message: 'And here as well!',
                    validationRule: 'my-own-rule',
                },
            ];
            expect(getSectionValidationMessage(screenId, 'section1', state)).toEqual('2 errors');
            expect(getSectionValidationMessage(screenId, 'section2', state)).toEqual('1 error');
        });
    });

    describe('Min items', () => {
        it('should return an error of the selected number of the items if it is less than the validation rule', async () => {
            addFieldToState(FieldKey.MultiDropdown, state, screenId, elementId, {
                minItems: 2,
            });

            expect(await runValidation(state, [0])).toContainEqual({
                elementId,
                screenId,
                message: 'fieldId the minimum number of selectable items is 2',
                validationRule: 'minItems',
            });
        });
    });

    describe('Max items', () => {
        it('should return an error of the selected number of the items if it is more than the validation rule', async () => {
            addFieldToState(FieldKey.MultiReference, state, screenId, elementId, {
                maxItems: 2,
                node: '@sage/xtrem-test/AnyNode',
                valueField: '_id',
            });

            expect(await runValidation(state, [0, 1, 2])).toContainEqual({
                elementId,
                screenId,
                message: 'fieldId the maximum number of selectable items is 2',
                validationRule: 'maxItems',
            });
        });
    });
});
