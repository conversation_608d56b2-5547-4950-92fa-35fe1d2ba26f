import {
    BlockLayout,
    DetailPanelLayout,
    PageLayout,
    SectionLayout,
    StickerLayout,
} from '../../component/container/layouts';
import type { BlockControlObject } from '../../component/control-objects';
import { detailPanelId, SectionControlObject } from '../../component/control-objects';
import type { BlockDecoratorProperties, SectionDecoratorProperties } from '../../component/decorators';
import type { ReadonlyFieldProperties } from '../../component/readonly-field-control-object';
import { getMockPageMetadata } from '../../__tests__/test-helpers';
import type { PageArticleItem } from '../layout-types';
import type { Page } from '../page';
import type { ScreenBase } from '../screen-base';

const pageItemGenerator = (name: string, type: string): Partial<PageArticleItem> => ({
    $containerId: name,
    $category: type,
    $isHiddenMobile: false,
    $isHiddenDesktop: false,
    $layout: { $items: [] },
});

describe('Layout service', () => {
    const screenId = 'TestPage';

    describe('Block layout Builder', () => {
        it('Should create a layout block and return the blockLayout', () => {
            const pageMetadata = getMockPageMetadata();
            const uiComponentProperties: ReadonlyFieldProperties = {
                isHiddenMobile: true,
                isHiddenDesktop: false,
            };

            pageMetadata.uiComponentProperties.field1 = uiComponentProperties;
            const properties: BlockDecoratorProperties<ScreenBase> = {
                title: 'title',
                isHiddenDesktop: false,
                isHiddenMobile: true,
            };
            const expected = {
                $category: 'block',
                $containerId: 'test1',
                $isHiddenDesktop: false,
                $isHiddenMobile: true,
                $layout: {
                    $items: [],
                },
            };
            const result = new BlockLayout({} as Page, 'test1', {}, {}, { pageMetadata, properties }).build().layout;

            expect(result).toEqual(expected);
            expect(pageMetadata.layoutBlocks.test1).toEqual(expected);
        });
    });

    describe('Page Layout Builder', () => {
        it('Should call populate function thunks for Blocks & Section', () => {
            const block1Layout = pageItemGenerator('block1', 'block');
            const block2Layout = pageItemGenerator('block2', 'block');
            const fakeSectionThunkFn = jest.fn(() => ({}));
            const fakeBlockThunkFn = jest.fn().mockReturnValue(block1Layout);
            const fakeBlockThunkFn2 = jest.fn().mockReturnValue(block2Layout);

            // Preparing the Page as we should receive it in the function.
            const pageMetadata = getMockPageMetadata();
            const sectionLayout = pageItemGenerator('section1', 'section');

            pageMetadata.layout.$items.push(sectionLayout);
            pageMetadata.blockThunks = {
                block1: fakeBlockThunkFn,
                block2: fakeBlockThunkFn2,
            };
            pageMetadata.sectionThunks = {
                section1: fakeSectionThunkFn,
            };
            pageMetadata.layoutBlocks = {
                block1: block1Layout,
                block2: block2Layout,
            };
            const sectionControlObject = {
                get layout() {
                    return sectionLayout;
                },
            };

            pageMetadata.controlObjects = {
                block1: {
                    parent() {
                        return sectionControlObject;
                    },
                    get layout() {
                        return block1Layout;
                    },
                } as unknown as BlockControlObject,
                block2: {
                    parent() {
                        return sectionControlObject;
                    },
                    get layout() {
                        return block2Layout;
                    },
                } as unknown as BlockControlObject,
            };
            new PageLayout(
                () => {},
                screenId,
                {},
                {},
                {
                    properties: {
                        authorizationCode: 'ANYTHING',
                        module: 'WILLDO',
                    },
                    pageMetadata,
                },
            ).build();
            expect(fakeBlockThunkFn.mock.calls.length).toBe(1);
            expect(fakeBlockThunkFn2.mock.calls.length).toBe(1);
            expect(fakeSectionThunkFn.mock.calls.length).toBe(1);
            expect(pageMetadata.layout!.$items[0].$layout!.$items[0]).toBe(block1Layout);
            expect(pageMetadata.layout!.$items[0].$layout!.$items[1]).toBe(block2Layout);
        });
    });

    describe('Remove helper panel items', () => {
        it('Should remove the helper panel sections from the layout', () => {
            const pageMetadata = getMockPageMetadata();

            const headerSectionLayout = {
                $containerId: 'headerSection',
                $category: 'section',
                $layout: { $items: [] },
            };
            const bodySectionLayout = {
                $containerId: 'bodySection',
                $category: 'section',
                $layout: { $items: [] },
            };
            const pageSectionLayout = {
                $containerId: 'pageSection',
                $category: 'section',
                $layout: { $items: [] },
            };
            pageMetadata.layout.$items = [headerSectionLayout, bodySectionLayout, pageSectionLayout];

            const headerSection = new SectionControlObject({
                elementId: 'headerSection',
                dispatchSectionValidation: jest.fn(),
                getUiComponentProperties: jest.fn(),
                getValidationState: jest.fn(),
                layout: {},
                screenId,
                setUiComponentProperties: jest.fn(),
            });
            const bodySection = new SectionControlObject({
                elementId: 'bodySection',
                dispatchSectionValidation: jest.fn(),
                getUiComponentProperties: jest.fn(),
                getValidationState: jest.fn(),
                layout: {},
                screenId,
                setUiComponentProperties: jest.fn(),
            });

            const detailPanelLayouts = new DetailPanelLayout(
                {} as Page,
                detailPanelId,
                {},
                {},
                {
                    detailPanelHeader: headerSection,
                    detailPanelSections: [bodySection],
                    pageMetadata,
                    properties: {
                        header: headerSection,
                        sections: [bodySection],
                    },
                },
            ).build().layout;
            expect(detailPanelLayouts.detailPanelHeaderLayout).toBe(headerSectionLayout);
            expect(detailPanelLayouts.detailPanelSectionsLayout).toEqual([bodySectionLayout]);
            expect(pageMetadata.layout.$items).toEqual([pageSectionLayout]);
        });
    });

    describe('Section Layout Builder', () => {
        it('Should push a section item to layout item array.', () => {
            const pageMetadata = getMockPageMetadata();

            const properties: SectionDecoratorProperties<ScreenBase> = {
                title: 'title',
                isHiddenDesktop: false,
                isHiddenMobile: true,
            };
            const expected = {
                $category: 'section',
                $containerId: 'testSection1',
                $isHiddenDesktop: false,
                $isHiddenMobile: true,
                $layout: { $items: [] },
            };
            new SectionLayout({} as Page, 'testSection1', {}, {}, { pageMetadata, properties }).build();
            expect(pageMetadata.layout.$items[0]).toEqual(expected);
        });
    });

    describe('Page Layout Builder', () => {
        it('Should call populate function thunks for Blocks & Section', () => {
            const block1Id = 'block1';
            const block2Id = 'block2';
            const block1Layout = pageItemGenerator(block1Id, 'block');
            const block2Layout = pageItemGenerator(block2Id, 'block');
            const fieldId = 'field1';
            const fieldPageArticleItem = { $bind: fieldId } as PageArticleItem;
            block2Layout.$layout!.$items.push(fieldPageArticleItem);
            const block1Thunk = jest.fn().mockReturnValue(block1Layout);
            const block2Thunk = jest.fn().mockReturnValue(block2Layout);
            const sectionLayout = pageItemGenerator('section1', 'section');
            const sectionThunk = jest.fn(() => ({}));

            const pageMetadata = getMockPageMetadata();
            pageMetadata.layout.$items.push(sectionLayout);
            pageMetadata.blockThunks = {
                [block1Id]: block1Thunk,
                [block2Id]: block2Thunk,
            };
            pageMetadata.sectionThunks = {
                section1: sectionThunk,
            };
            pageMetadata.layoutBlocks = {
                [block1Id]: block1Layout,
                [block2Id]: block2Layout,
            };

            const sectionControlObject = {
                get layout() {
                    return sectionLayout;
                },
            };

            pageMetadata.controlObjects = {
                [block1Id]: {
                    parent() {
                        return sectionControlObject;
                    },
                    get layout() {
                        return block1Layout;
                    },
                } as unknown as BlockControlObject,
                [block2Id]: {
                    parent() {
                        return sectionControlObject;
                    },
                    get layout() {
                        return block2Layout;
                    },
                } as unknown as BlockControlObject,
            };
            new StickerLayout(
                () => {},
                screenId,
                {},
                {},
                {
                    properties: {
                        icon: 'analysis',
                        isActive: () => true,
                    },
                    pageMetadata,
                },
            ).build();

            expect(block1Thunk.mock.calls.length).toBe(1);
            expect(block2Thunk.mock.calls.length).toBe(1);
            expect(sectionThunk.mock.calls.length).toBe(1);

            const block1LayoutItem = pageMetadata.layout!.$items[0].$layout!.$items[0];
            const block2LayoutItem = pageMetadata.layout!.$items[0].$layout!.$items[1];
            expect(block1LayoutItem).toBe(block1Layout);
            expect(block1LayoutItem.$containerId).toBe(block1Id);
            expect(block2LayoutItem).toBe(block2Layout);
            expect(block2LayoutItem.$containerId).toBe(block2Id);

            const fieldLayoutItem = block2LayoutItem.$layout!.$items[0];
            expect(fieldLayoutItem).toBe(fieldPageArticleItem);
            expect(fieldLayoutItem.$bind).toBe(fieldId);
        });
    });
});
