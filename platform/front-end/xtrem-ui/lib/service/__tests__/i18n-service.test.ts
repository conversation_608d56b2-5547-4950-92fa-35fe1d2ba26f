import * as XtremDateTime from '@sage/xtrem-date-time';
import { getMockState, getMockStore } from '../../__tests__/test-helpers';
import type * as xtremRedux from '../../redux';
import * as i18nService from '../i18n-service';

jest.mock('@sage/xtrem-date-time');

describe('Screen loader service', () => {
    let mockState: xtremRedux.XtremAppState;

    beforeEach(() => {
        mockState = getMockState({
            applicationContext: {
                locale: 'en-US',
                handleNavigation: jest.fn(),
                updateMenu: jest.fn(),
            },
            translations: {
                'en-US': {
                    '@sage/xtrem-test-package/pages__biodegradability_category____title': 'american string',
                    '@sage/xtrem-test-package/string_with_object_keys': 'You have {{number}} new {{items}}',
                    '@sage/xtrem-test-package/string_with_array_keys': 'You have {{0}} new {{1}}',
                    '@sage/xtrem-test-package/enums__show_case_product_category__awful': 'Awful',
                    '@sage/xtrem-test-package/enums__show_case_product_category__good': 'Good',
                    '@sage/xtrem-test-package/enums__show_case_product_category__great': 'Great',
                    '@sage/xtrem-test-package/enums__show_case_product_category__notBad': 'Not bad',
                    '@sage/xtrem-test-package/enums__show_case_product_category__ok': 'Ok',
                    '@sage/xtrem-ui/date-format': 'DD/MM/YYYY',
                },
                'es-ES': {
                    '@sage/xtrem-test-package/pages__biodegradability_category____title': 'spanish string',
                    '@sage/xtrem-test-package/enums__show_case_product_category__awful': 'Mal',
                    '@sage/xtrem-test-package/enums__show_case_product_category__good': 'Bien',
                    '@sage/xtrem-test-package/enums__show_case_product_category__great': 'Muy bien',
                    '@sage/xtrem-test-package/enums__show_case_product_category__notBad': 'Not bad',
                    '@sage/xtrem-test-package/enums__show_case_product_category__ok': 'Ok',
                    '@sage/xtrem-ui/date-format': 'DD.MM.YYYY',
                },
            },
        });
        getMockStore(mockState);
    });

    it('should use en-US as the default locale', () => {
        mockState.applicationContext!.locale = undefined;
        const result = i18nService.localize(
            '@sage/xtrem-test-package/pages__biodegradability_category____title',
            'default value',
        );
        expect(result).toEqual('american string');
    });

    it('should resolve string in the right language', () => {
        let result = i18nService.localize(
            '@sage/xtrem-test-package/pages__biodegradability_category____title',
            'default value',
        );
        expect(result).toEqual('american string');

        mockState.applicationContext!.locale = 'es-ES';
        result = i18nService.localize(
            '@sage/xtrem-test-package/pages__biodegradability_category____title',
            'default value',
        );
        expect(result).toEqual('spanish string');
    });

    it('should format strings with object parameters', () => {
        const result = i18nService.localize('@sage/xtrem-test-package/string_with_object_keys', 'default value', {
            number: 4,
            items: 'messages',
        });
        expect(result).toEqual('You have 4 new messages');
    });

    it('should format strings with array parameters', () => {
        const result = i18nService.localize('@sage/xtrem-test-package/string_with_array_keys', 'default value', [
            4,
            'messages',
        ]);
        expect(result).toEqual('You have 4 new messages');
    });

    it('should return the default value if the string cannot be resolved', () => {
        const result = i18nService.localize('@sage/xtrem-test-package/pages__non_existing_entry', 'default value');
        expect(result).toEqual('default value');
    });

    it('should resolve enum members', () => {
        let result = i18nService.localizeEnumMember('@sage/xtrem-test-package/ShowCaseProductCategory', 'great');
        expect(result).toEqual('Great');

        mockState.applicationContext!.locale = 'es-ES';
        result = i18nService.localizeEnumMember('@sage/xtrem-test-package/ShowCaseProductCategory', 'great');
        expect(result).toEqual('Muy bien');
    });

    it('should format date according to current language', () => {
        expect(XtremDateTime.formatDateToCurrentLocale).not.toHaveBeenCalled();
        i18nService.formatDateToCurrentLocale('2021-01-10');
        expect(XtremDateTime.formatDateToCurrentLocale).toHaveBeenLastCalledWith('2021-01-10', 'en-US', 'FullDate');
        mockState.applicationContext!.locale = 'es-ES';
        i18nService.formatDateToCurrentLocale('2021-01-10', 'MonthYear');
        expect(XtremDateTime.formatDateToCurrentLocale).toHaveBeenLastCalledWith('2021-01-10', 'es-ES', 'MonthYear');
    });
});
