import type { Dict } from '@sage/xtrem-shared';
import { GraphQLKind } from '../../types';
import * as graphqlUtils from '../graphql-utils';
import type { FormattedNodeDetails } from '../metadata-types';
import { GraphQLTypes } from '../../component/types';
import { exportTableData } from '../table-export-service';
import { nestedFields } from '../..';
import type { AgGridColumnConfigWithScreenIdAndColDef } from '../../utils/ag-grid/ag-grid-utility-types';

const node = '@sage/xtrem-test/Address';

const nodeTypes: Dict<FormattedNodeDetails> = {
    Address: {
        name: 'Address',
        title: 'Address',
        packageName: '@sage/xtrem-test',
        properties: {
            _id: {
                name: '_id',
                type: 'IntOrString',
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                isOnOutputType: true,
                canFilter: true,
            },
            line1: {
                name: 'line1',
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                isOnOutputType: true,
                canFilter: true,
            },
            line2: {
                name: 'line2',
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                isOnOutputType: true,
                canFilter: true,
            },
            country: {
                name: 'country',
                type: 'Country',
                kind: GraphQLKind.Object,
                isOnInputType: true,
                isOnOutputType: true,
                canFilter: true,
                isMutable: true,
            },
        },
        mutations: {},
    },
    Country: {
        name: 'Country',
        title: 'Country',
        packageName: '@sage/xtrem-test',
        properties: {
            _id: {
                name: '_id',
                type: 'IntOrString',
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                isOnOutputType: true,
                canFilter: true,
            },
            name: {
                name: 'name',
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                isOnOutputType: true,
                canFilter: true,
            },
            code: {
                name: 'code',
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                isOnOutputType: true,
                canFilter: true,
            },
            flag: {
                name: 'flag',
                type: GraphQLTypes._InputStream,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                isOnOutputType: true,
                canFilter: true,
            },
            inputOnlyProperty: {
                name: 'inputOnlyProperty',
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                isOnOutputType: false,
                canFilter: true,
            },
        },
        mutations: {},
    },
};
describe('table export service', () => {
    let executeGraphqlQueryMock: jest.SpyInstance<Promise<any>, any>;

    beforeEach(() => {
        executeGraphqlQueryMock = jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockResolvedValue({
            data: { global: { exportByTemplateDefinition: { start: { trackingId: 'XXXXX' } } } },
        });
    });

    afterEach(() => {
        executeGraphqlQueryMock.mockRestore();
    });

    describe('exportTableData', () => {
        it('should create a basic export query', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            await exportTableData({
                outputFormat: 'csv',
                node,
                nodeTypes,
                filter: {},
                orderBy: {},
                columns: [
                    {
                        type: 'Text',
                        context: { columnDefinition: nestedFields.text({ bind: 'line1', title: 'Line 1' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                    {
                        type: 'Text',
                        context: { columnDefinition: nestedFields.text({ bind: 'line2', title: 'Line 2' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                ],
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                query: {
                    mutation: {
                        global: {
                            exportByTemplateDefinition: {
                                start: {
                                    __args: {
                                        outputFormat: 'csv',
                                        filter: '{}',
                                        nodeName: 'Address',
                                        orderBy: '{}',
                                        templateDefinition: [
                                            { path: 'line1', title: 'Line 1' },
                                            { path: 'line2', title: 'Line 2' },
                                        ],
                                    },
                                    trackingId: true,
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should create a basic export query with reference field', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            await exportTableData({
                outputFormat: 'csv',
                node,
                nodeTypes,
                filter: {},
                orderBy: {},
                columns: [
                    {
                        type: 'Text',
                        context: { columnDefinition: nestedFields.text({ bind: 'line1', title: 'Line 1' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                    {
                        type: 'Reference',
                        context: {
                            columnDefinition: nestedFields.reference({
                                bind: 'country',
                                valueField: 'name',
                                title: 'Country name',
                            }),
                        },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                ],
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                query: {
                    mutation: {
                        global: {
                            exportByTemplateDefinition: {
                                start: {
                                    __args: {
                                        outputFormat: 'csv',
                                        filter: '{}',
                                        nodeName: 'Address',
                                        orderBy: '{}',
                                        templateDefinition: [
                                            { path: 'line1', title: 'Line 1' },
                                            { path: 'country.name', title: 'Country name' },
                                        ],
                                    },
                                    trackingId: true,
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should create a basic export query with deep bound property', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            await exportTableData({
                outputFormat: 'csv',
                node,
                nodeTypes,
                filter: {},
                orderBy: {},
                columns: [
                    {
                        type: 'Text',
                        context: { columnDefinition: nestedFields.text({ bind: 'line1', title: 'Line 1' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                    {
                        type: 'Text',
                        context: {
                            columnDefinition: nestedFields.text({
                                bind: { country: { name: true } },
                                title: 'Country name',
                            }),
                        },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                ],
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                query: {
                    mutation: {
                        global: {
                            exportByTemplateDefinition: {
                                start: {
                                    __args: {
                                        outputFormat: 'csv',
                                        filter: '{}',
                                        nodeName: 'Address',
                                        orderBy: '{}',
                                        templateDefinition: [
                                            { path: 'line1', title: 'Line 1' },
                                            { path: 'country.name', title: 'Country name' },
                                        ],
                                    },
                                    trackingId: true,
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should filter out hidden columns', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            await exportTableData({
                outputFormat: 'csv',
                node,
                nodeTypes,
                filter: {},
                orderBy: {},
                columns: [
                    {
                        type: 'Text',
                        context: { columnDefinition: nestedFields.text({ bind: 'line1', title: 'Line 1' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                    {
                        type: 'Text',
                        hide: true,
                        context: { columnDefinition: nestedFields.text({ bind: 'line2', title: 'Line 2' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                ],
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                query: {
                    mutation: {
                        global: {
                            exportByTemplateDefinition: {
                                start: {
                                    __args: {
                                        outputFormat: 'csv',
                                        filter: '{}',
                                        nodeName: 'Address',
                                        orderBy: '{}',
                                        templateDefinition: [{ path: 'line1', title: 'Line 1' }],
                                    },
                                    trackingId: true,
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should filter out properties that are not on the output type', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            await exportTableData({
                outputFormat: 'csv',
                node,
                nodeTypes,
                filter: {},
                orderBy: {},
                columns: [
                    {
                        type: 'Text',
                        context: { columnDefinition: nestedFields.text({ bind: 'line1', title: 'Line 1' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                    {
                        type: 'Text',
                        context: {
                            columnDefinition: nestedFields.text({
                                bind: { country: { inputOnlyProperty: true } },
                                title: 'non output prop',
                            }),
                        },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                ],
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                query: {
                    mutation: {
                        global: {
                            exportByTemplateDefinition: {
                                start: {
                                    __args: {
                                        outputFormat: 'csv',
                                        filter: '{}',
                                        nodeName: 'Address',
                                        orderBy: '{}',
                                        templateDefinition: [{ path: 'line1', title: 'Line 1' }],
                                    },
                                    trackingId: true,
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should filter out transient fields', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            await exportTableData({
                outputFormat: 'csv',
                node,
                nodeTypes,
                filter: {},
                orderBy: {},
                columns: [
                    {
                        type: 'Text',
                        context: {
                            columnDefinition: nestedFields.text({ bind: 'line1', title: 'Line 1', isTransient: true }),
                        },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                    {
                        type: 'Text',
                        context: { columnDefinition: nestedFields.text({ bind: 'line2', title: 'Line 2' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                ],
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                query: {
                    mutation: {
                        global: {
                            exportByTemplateDefinition: {
                                start: {
                                    __args: {
                                        outputFormat: 'csv',
                                        filter: '{}',
                                        nodeName: 'Address',
                                        orderBy: '{}',
                                        templateDefinition: [{ path: 'line2', title: 'Line 2' }],
                                    },
                                    trackingId: true,
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should filter out binary stream properties', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            await exportTableData({
                outputFormat: 'csv',
                node,
                nodeTypes,
                filter: {},
                orderBy: {},
                columns: [
                    {
                        type: 'Text',
                        context: { columnDefinition: nestedFields.text({ bind: 'line1', title: 'Line 1' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                    {
                        type: 'Image',
                        context: {
                            columnDefinition: nestedFields.image({
                                bind: { country: { flag: true } },
                                title: 'Country flag',
                            }),
                        },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                ],
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                query: {
                    mutation: {
                        global: {
                            exportByTemplateDefinition: {
                                start: {
                                    __args: {
                                        outputFormat: 'csv',
                                        filter: '{}',
                                        nodeName: 'Address',
                                        orderBy: '{}',
                                        templateDefinition: [{ path: 'line1', title: 'Line 1' }],
                                    },
                                    trackingId: true,
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should throw an error if no exportable columns are found', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            await expect(
                exportTableData({
                    outputFormat: 'csv',
                    node,
                    nodeTypes,
                    filter: {},
                    orderBy: {},
                    columns: [
                        {
                            type: 'Text',
                            hide: true,
                            context: { columnDefinition: nestedFields.text({ bind: 'line1', title: 'Line 1' }) },
                        } as AgGridColumnConfigWithScreenIdAndColDef,
                        {
                            type: 'Text',
                            context: {
                                columnDefinition: nestedFields.text({
                                    bind: 'line1',
                                    title: 'Line 1',
                                    isTransient: true,
                                }),
                            },
                        } as AgGridColumnConfigWithScreenIdAndColDef,
                        {
                            type: 'Image',
                            context: {
                                columnDefinition: nestedFields.image({
                                    bind: { country: { flag: true } },
                                    title: 'Country flag',
                                }),
                            },
                        } as AgGridColumnConfigWithScreenIdAndColDef,
                        {
                            type: 'Text',
                            context: {
                                columnDefinition: nestedFields.text({
                                    bind: { country: { inputOnlyProperty: true } },
                                    title: 'non output prop',
                                }),
                            },
                        } as AgGridColumnConfigWithScreenIdAndColDef,
                    ],
                }),
            ).rejects.toThrow('No columns to export.');
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
        });

        it('should serialize the orderBy and filter arguments', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            await exportTableData({
                outputFormat: 'csv',
                node,
                nodeTypes,
                filter: { line1: { _eq: 'test' } },
                orderBy: { line1: 1, line2: -1 },
                columns: [
                    {
                        type: 'Text',
                        context: { columnDefinition: nestedFields.text({ bind: 'line1', title: 'Line 1' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                    {
                        type: 'Text',
                        context: { columnDefinition: nestedFields.text({ bind: 'line2', title: 'Line 2' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                ],
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                query: {
                    mutation: {
                        global: {
                            exportByTemplateDefinition: {
                                start: {
                                    __args: {
                                        outputFormat: 'csv',
                                        filter: '{"line1":{"_eq":"test"}}',
                                        nodeName: 'Address',
                                        orderBy: '{"line1":1,"line2":-1}',
                                        templateDefinition: [
                                            { path: 'line1', title: 'Line 1' },
                                            { path: 'line2', title: 'Line 2' },
                                        ],
                                    },
                                    trackingId: true,
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should pass on the output format', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            await exportTableData({
                outputFormat: 'xlsx',
                node,
                nodeTypes,
                filter: { line1: { _eq: 'test' } },
                orderBy: { line1: 1, line2: -1 },
                columns: [
                    {
                        type: 'Text',
                        context: { columnDefinition: nestedFields.text({ bind: 'line1', title: 'Line 1' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                    {
                        type: 'Text',
                        context: { columnDefinition: nestedFields.text({ bind: 'line2', title: 'Line 2' }) },
                    } as AgGridColumnConfigWithScreenIdAndColDef,
                ],
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                query: {
                    mutation: {
                        global: {
                            exportByTemplateDefinition: {
                                start: {
                                    __args: {
                                        outputFormat: 'xlsx',
                                        filter: '{"line1":{"_eq":"test"}}',
                                        nodeName: 'Address',
                                        orderBy: '{"line1":1,"line2":-1}',
                                        templateDefinition: [
                                            { path: 'line1', title: 'Line 1' },
                                            { path: 'line2', title: 'Line 2' },
                                        ],
                                    },
                                    trackingId: true,
                                },
                            },
                        },
                    },
                },
            });
        });
    });
});
