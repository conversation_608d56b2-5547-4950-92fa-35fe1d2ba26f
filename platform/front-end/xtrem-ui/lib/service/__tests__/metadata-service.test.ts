// This block must be the first one
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../__tests__/test-helpers';

// DON'T REARRANGE IMPORTS

import { setLocalizeImplementation } from '@sage/xtrem-date-time';
import { <PERSON><PERSON><PERSON>, type Dict } from '@sage/xtrem-shared';
import axios from 'axios';
import * as idb from 'idb';
import type { PageProperties } from '../../component/container/page/page-types';
import type * as xtremRedux from '../../redux';
import type { ApplicationContext } from '../../redux/state';
import { GraphQLKind, GraphQLTypes } from '../../types';
import { ARTIFACT_CACHE_STORE, ARTIFACT_DATABASE_NAME } from '../../utils/constants';
import * as windowUtils from '../../utils/window';
import * as graphQLQueryBuilder from '../graphql-query-builder';
import type { GraphQLFilter } from '../graphql-utils';
import * as graphqlUtils from '../graphql-utils';
import * as metadataService from '../metadata-service';
import type { FormattedNodeDetails } from '../metadata-types';
import type { Page } from '../page';
import type { PageDefinition } from '../page-definition';
import * as storageService from '../storage-service';

setLocalizeImplementation((key: string, _template: string) => {
    return _template;
});

const elementId = 'myField';

const nodeTypes: Dict<FormattedNodeDetails> = {
    SalesOrder: {
        name: 'SalesOrder',
        title: 'Sales Order',
        packageName: '@sage/xtrem-test',
        properties: {
            bind01: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
            bind02: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
            orderCity: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
            aDateField: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar },
            aNumberField: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
            aReferenceField: { type: 'SalesOrderLine', kind: GraphQLKind.Scalar },
        },
        mutations: {},
    },
    SalesOrderLine: {
        name: 'SalesOrderLine',
        title: 'SalesOrderLine',
        packageName: '@sage/xtrem-test',
        properties: {
            aStringField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
            aNumericField: { type: GraphQLTypes.Decimal, kind: GraphQLKind.Scalar },
        },
        mutations: {},
    },
    AnyNode: {
        name: 'AnyNode',
        title: 'AnyNode',
        packageName: '@sage/xtrem-test',
        properties: {
            _id: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
            [elementId]: { type: GraphQLTypes.IntReference, kind: GraphQLKind.Scalar },
            field1: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
            field3: { type: GraphQLTypes.Boolean, kind: GraphQLKind.Scalar },
            field4: { type: GraphQLTypes.IntReference, kind: GraphQLKind.Scalar },
            field5: { type: 'SomeOtherObject', kind: GraphQLKind.Scalar },
        },
        mutations: {},
    },
};

describe('graphql-service', () => {
    const screenId = 'TestPage';
    let pageDefinition: PageDefinition | null = null;
    let mockState: xtremRedux.XtremAppState;
    let executeGraphqlQueryMock: jest.SpyInstance<Promise<any>, any>;
    let isDevModeMock: jest.SpyInstance<any, any>;

    beforeEach(() => {
        delete (window as any).location;
        (global as any).window.location = {
            port: '80',
            protocol: 'https:',
            hostname: 'localhost',
        };
        mockState = getMockState({ nodeTypes });
        mockState.applicationContext = {
            cacheEncryptionKey: 'TEST_ENCRYPTION_KEY',
            locale: 'en-US',
        } as ApplicationContext;
        getMockStore(mockState);
        pageDefinition = getMockPageDefinition(screenId, {}, { uiComponentProperties: { [screenId]: {} } });
        executeGraphqlQueryMock = jest.spyOn(graphqlUtils, 'executeGraphqlQuery');
        isDevModeMock = jest.spyOn(windowUtils, 'isDevMode').mockReturnValue(true);
    });

    afterEach(() => {
        executeGraphqlQueryMock.mockReset();
    });

    describe('getArtifactsMetadata', () => {
        let postMock: jest.SpyInstance<Promise<any>, any>;

        beforeEach(() => {
            // For artifact metadata we don't need this mock:
            executeGraphqlQueryMock.mockRestore();

            mockState = getMockState();
            getMockStore(mockState);
            postMock = jest.spyOn(axios, 'post').mockResolvedValue({
                data: {
                    data: {
                        pages: [
                            {
                                content: 'This is content',
                            },
                        ],
                    },
                },
                status: 200,
                statusText: 'OK',
                headers: {},
                config: {},
            });
        });

        afterEach(() => {
            postMock.mockReset();
        });

        it('should generate query with multiple parameters', async () => {
            expect(postMock).not.toHaveBeenCalled();
            await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.3' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: true,
                exactMatch: true,
            });
            expect(postMock).toHaveBeenCalledTimes(1);
            expect(postMock).toHaveBeenCalledWith(
                '/metadata',
                {
                    query: `query {
    pages (filter: {packageOrPage: "@sage/x3-package", exactMatch: true}) {
        key
        content
        access {
            node
            bindings {
                name
                status
            }
        }
        strings {
            key
            content
        }
    }
    strings (filter: {packageOrPage: "@sage/xtrem-ui,@sage/xtrem-date-time,@sage/xtrem-ui-components,@sage/xtrem-document-editor"}) {
        key
        content
    }
}`,
                },
                {
                    headers: {
                        'Accept-Language': 'en-US',
                    },
                },
            );
        });

        it('should cache query results and return the cached version on a second call in prod mode', async () => {
            isDevModeMock.mockReturnValue(false);
            expect(postMock).not.toHaveBeenCalled();
            const call1Result = await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.3' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'en-US',
            });
            expect(postMock).toHaveBeenCalledTimes(1);
            const call2Result = await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.3' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'en-US',
            });
            expect(postMock).toHaveBeenCalledTimes(2);
            expect(call1Result).toEqual(call2Result);
            // The last call only fetches access rights
            expect(postMock.mock.calls[1][1]).toEqual(
                expect.objectContaining({
                    query: 'query { pages (filter: {packageOrPage: "@sage/x3-package", exactMatch: true}) { key access { node bindings { name status } } } }',
                }),
            );
        });

        it('should encrypt cached entities when putting them to IndexDB', async () => {
            isDevModeMock.mockReturnValue(false);
            expect(postMock).not.toHaveBeenCalled();
            await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.3' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'en-US',
            });
            const db = await idb.openDB(ARTIFACT_DATABASE_NAME, 1, {
                async upgrade(database) {
                    // The records are tracked by the key and the version number of the corresponding package.
                    await database.createObjectStore(ARTIFACT_CACHE_STORE, {
                        keyPath: ['key', 'version', 'locale', 'shouldFetchPlatformLiterals'],
                    });
                },
            });
            const value = await db.get(ARTIFACT_CACHE_STORE, ['pages/@sage/x3-package', '1.2.3', 'en-US', 'false']);
            expect(value.data).toEqual(
                'IjHMio0QkFGjftEhzVU0ga2Xw8K4NGUsNo7AMxPN/9DJ1h/1wxSA+Enn3BjPlC4qiGGK9dLM7MAlgtlq6Hp8iQ==',
            );
        });

        it('should cache query results but still make a small query to check changes in access rights', async () => {
            isDevModeMock.mockReturnValue(false);
            expect(postMock).not.toHaveBeenCalled();
            postMock.mockResolvedValue({
                data: {
                    data: {
                        pages: [
                            {
                                access: [
                                    {
                                        node: '@sage/xtrem-system/SysPackAllocation',
                                        bindings: [
                                            {
                                                name: 'read',
                                                status: 'authorized',
                                            },
                                        ],
                                    },
                                ],
                                content: 'This is content',
                            },
                        ],
                    },
                },
                status: 200,
                statusText: 'OK',
                headers: {},
                config: {},
            });

            const call1Result = await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.3' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package/TestPage',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'en-US',
            });

            expect(call1Result.pages?.[0]?.access?.[0]?.bindings?.[0]?.status).toEqual('authorized');
            expect(postMock).toHaveBeenCalledTimes(1);
            expect(postMock.mock.calls[0][1]).toEqual(
                expect.objectContaining({
                    query: 'query { pages (filter: {packageOrPage: "@sage/x3-package/TestPage", exactMatch: true}) { key content access { node bindings { name status } } strings { key content } } }',
                }),
            );
            postMock.mockResolvedValue({
                data: {
                    data: {
                        pages: [
                            {
                                access: [
                                    {
                                        node: '@sage/xtrem-system/SysPackAllocation',
                                        bindings: [
                                            {
                                                name: 'read',
                                                status: 'unauthorized',
                                            },
                                        ],
                                    },
                                ],
                            },
                        ],
                    },
                },
                status: 200,
                statusText: 'OK',
                headers: {},
                config: {},
            });

            const call2Result = await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.3' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package/TestPage',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'en-US',
            });
            expect(call2Result.pages?.[0]?.access?.[0]?.bindings?.[0]?.status).toEqual('unauthorized');

            expect(postMock).toHaveBeenCalledTimes(2);
            expect(postMock.mock.calls[1][1]).toEqual(
                expect.objectContaining({
                    query: 'query { pages (filter: {packageOrPage: "@sage/x3-package/TestPage", exactMatch: true}) { key access { node bindings { name status } } } }',
                }),
            );
            expect(postMock).toHaveBeenCalledTimes(2);
        });

        it('should request a again the metadata artifact if the version number changes', async () => {
            isDevModeMock.mockReturnValue(false);
            expect(postMock).not.toHaveBeenCalled();
            await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.3' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'en-US',
            });
            expect(postMock).toHaveBeenCalledTimes(1);
            // Version changed here, expect another call
            await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.4' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'en-US',
            });
            expect(postMock).toHaveBeenCalledTimes(2);

            // No version change between these two calls so no new calls are expected
            await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.4' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'en-US',
            });
            expect(postMock).toHaveBeenCalledTimes(3);
            // The last call only fetches access rights
            expect(postMock.mock.calls[2][1]).toEqual(
                expect.objectContaining({
                    query: 'query { pages (filter: {packageOrPage: "@sage/x3-package", exactMatch: true}) { key access { node bindings { name status } } } }',
                }),
            );
        });

        it('should request a again the metadata artifact if the selected locale changes', async () => {
            isDevModeMock.mockReturnValue(false);
            expect(postMock).not.toHaveBeenCalled();
            await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.3' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'en-US',
            });
            expect(postMock).toHaveBeenCalledTimes(1);
            // Version changed here, expect another call
            await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.4' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'fr-FR',
            });
            expect(postMock).toHaveBeenCalledTimes(2);

            // No version change between these two calls so no new calls are expected
            await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.4' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'fr-FR',
            });
            expect(postMock).toHaveBeenCalledTimes(3);
            // The last call only fetches access rights
            expect(postMock.mock.calls[2][1]).toEqual(
                expect.objectContaining({
                    query: 'query { pages (filter: {packageOrPage: "@sage/x3-package", exactMatch: true}) { key access { node bindings { name status } } } }',
                }),
            );
        });

        it('should not cache query results in dev mode', async () => {
            isDevModeMock.mockReturnValue(true);
            expect(postMock).not.toHaveBeenCalled();
            await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.3' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'en-US',
            });
            expect(postMock).toHaveBeenCalledTimes(1);
            await metadataService.queryMetadata({
                applicationPackages: { '@sage/x3-package': '1.2.3' },
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
                locale: 'en-US',
            });
            expect(postMock).toHaveBeenCalledTimes(2);
        });

        it('should post the query with the path prefix if it is defined in the app context', async () => {
            expect(postMock).not.toHaveBeenCalled();
            mockState.applicationContext = {
                path: '/my/special/endpoint',
                handleNavigation: jest.fn(),
                updateMenu: jest.fn(),
            };
            await metadataService.queryMetadata({
                metadataType: 'pages',
                metadataProperties: ['key', 'content'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
            });
            expect(postMock).toHaveBeenCalledTimes(1);
            expect(postMock).toHaveBeenCalledWith(
                '/my/special/endpoint/metadata',
                {
                    query: `query {
    pages (filter: {packageOrPage: "@sage/x3-package", exactMatch: true}) {
        key
        content
        access {
            node
            bindings {
                name
                status
            }
        }
        strings {
            key
            content
        }
    }
}`,
                },
                {
                    headers: {
                        'Accept-Language': 'en-US',
                    },
                },
            );
        });

        it('should generate query with filter', async () => {
            /* eslint-disable no-useless-escape */
            expect(postMock).not.toHaveBeenCalled();
            const path = '@sage/x3-package/page';
            await metadataService.queryMetadata({
                metadataType: 'pages',
                metadataProperties: ['key'],
                path,
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
            });
            expect(postMock).toHaveBeenCalledTimes(1);
            expect(postMock).toHaveBeenCalledWith(
                '/metadata',
                {
                    query: `query {
    pages (filter: {packageOrPage: \"@sage/x3-package/page\", exactMatch: true}) {
        key
        access {
            node
            bindings {
                name
                status
            }
        }
        strings {
            key
            content
        }
    }
}`,
                },
                {
                    headers: {
                        'Accept-Language': 'en-US',
                    },
                },
            );
            /* eslint-enable no-useless-escape */
        });

        it('should generate set the Accept-Language header based on the context', async () => {
            expect(postMock).not.toHaveBeenCalled();
            mockState.applicationContext!.locale = 'fr-FR';
            const path = '@sage/x3-package/page';
            await metadataService.queryMetadata({
                metadataType: 'pages',
                metadataProperties: ['key'],
                path,
                shouldFetchPlatformLiterals: false,
                exactMatch: true,
            });
            expect(postMock).toHaveBeenCalledTimes(1);
            expect(postMock).toHaveBeenCalledWith('/metadata', expect.anything(), {
                headers: {
                    'Accept-Language': 'fr-FR',
                },
            });
        });

        it('should add custom headers when fetching artifacts', async () => {
            const requestHeaders: Dict<string> = {
                'custom-request-for-graphql': 'This could well be a token',
                'Accept-Language': 'en-US',
            };

            mockState.applicationContext = { handleNavigation: jest.fn(), updateMenu: jest.fn(), requestHeaders };

            expect(postMock).not.toHaveBeenCalled();
            await metadataService.queryMetadata({
                metadataType: 'pages',
                metadataProperties: ['key'],
                path: '@sage/x3-package',
                shouldFetchPlatformLiterals: true,
            });
            expect(postMock).toHaveBeenCalledTimes(1);
            expect(postMock).toHaveBeenCalledWith(
                '/metadata',
                {
                    query: `query {
    pages (filter: {packageOrPage: "@sage/x3-package"}) {
        key
    }
    strings (filter: {packageOrPage: "@sage/xtrem-ui,@sage/xtrem-date-time,@sage/xtrem-ui-components,@sage/xtrem-document-editor"}) {
        key
        content
    }
}`,
                },
                {
                    headers: requestHeaders,
                },
            );
        });
    });

    describe('mergeNodeCustomDetails', () => {
        it('should keep standard properties and append custom ones without duplicates, normalizing names with _customData prefix', async () => {
            const baseNodeDetails = [
                {
                    name: 'SalesOrder',
                    title: 'SalesOrder',
                    packageName: '@sage/xtrem-test',
                    defaultDataType: 'SalesOrder',
                    hasAttachments: false,
                    hasNotes: false,
                    properties: [
                        { name: 'bind01', isCustom: false },
                        { name: 'orderCity', isCustom: false },
                    ],
                    mutations: [],
                },
            ];

            const customNodeDetails = [
                {
                    name: 'SalesOrder',
                    title: 'SalesOrder',
                    packageName: '@custom',
                    defaultDataType: 'SalesOrder',
                    hasAttachments: false,
                    hasNotes: false,
                    properties: [
                        { name: 'orderCity', isCustom: true },
                        { name: 'customField1', isCustom: true },
                    ],
                    mutations: [],
                },
            ];

            executeGraphqlQueryMock.mockResolvedValueOnce({
                data: {
                    pages: [
                        {
                            nodeDetails: baseNodeDetails,
                            nodeCustomDetails: customNodeDetails,
                        },
                    ],
                },
            });

            const result = await metadataService.queryMetadata({
                metadataType: 'pages',
                metadataProperties: ['nodeDetails'],
                path: 'SalesOrder',
                exactMatch: true,
                locale: 'en-US',
            });

            const node = result.pages?.[0]?.nodeDetails?.find((n: any) => n.name === 'SalesOrder');
            expect(node).toBeDefined();
            if (!node) {
                throw new Error('Expected node to be defined');
            }
            expect(node.properties).toEqual(
                expect.arrayContaining([
                    { name: 'bind01', isCustom: false },
                    { name: 'orderCity', isCustom: false },
                    { name: '_customData.orderCity', isCustom: true },
                    { name: '_customData.customField1', isCustom: true },
                ]),
            );
        });

        it('should return nodeDetails unmodified when nodeCustomDetails is not present', async () => {
            const baseNodeDetails = [
                {
                    name: 'SalesOrder',
                    title: 'SalesOrder',
                    packageName: '@sage/xtrem-test',
                    defaultDataType: 'SalesOrder',
                    hasAttachments: false,
                    hasNotes: false,
                    properties: [{ name: 'existingField', isCustom: false }],
                    mutations: [],
                },
            ];

            executeGraphqlQueryMock.mockResolvedValueOnce({
                data: {
                    pages: [
                        {
                            nodeDetails: baseNodeDetails,
                            nodeCustomDetails: undefined,
                        },
                    ],
                },
            });

            const result = await metadataService.queryMetadata({
                metadataType: 'pages',
                metadataProperties: ['nodeDetails'],
                path: 'SalesOrder',
                exactMatch: true,
                locale: 'en-US',
            });

            const node = result.pages?.[0]?.nodeDetails?.find((n: any) => n.name === 'SalesOrder');
            expect(node).toBeDefined();
            if (!node) {
                throw new Error('Expected node to be defined');
            }
            expect(node.properties).toEqual([{ name: 'existingField', isCustom: false }]);
        });

        it('should create new node from nodeTypes if nodeDetails is empty and nodeCustomDetails exists', async () => {
            executeGraphqlQueryMock.mockResolvedValueOnce({
                data: {
                    pages: [
                        {
                            nodeDetails: [],
                            nodeCustomDetails: [
                                {
                                    name: 'AnyNode',
                                    title: 'AnyNode',
                                    packageName: '@custom/package',
                                    defaultDataType: 'AnyNode',
                                    hasAttachments: false,
                                    hasNotes: false,
                                    properties: [{ name: 'customX', isCustom: true }],
                                    mutations: [],
                                },
                            ],
                        },
                    ],
                },
            });

            const result = await metadataService.queryMetadata({
                metadataType: 'pages',
                metadataProperties: ['nodeDetails'],
                path: 'AnyNode',
                exactMatch: true,
                locale: 'en-US',
            });

            const node = result.pages?.[0]?.nodeDetails?.find((n: any) => n.name === 'AnyNode');
            expect(node).toBeDefined();
            if (!node) {
                throw new Error('Expected node to be defined');
            }
            expect(node.properties).toEqual(
                expect.arrayContaining([expect.objectContaining({ name: '_customData.customX', isCustom: true })]),
            );
        });
    });

    describe('fetchEnumTranslations', () => {
        it('should fetch and format the enum translations from the server', async () => {
            const executeGraphqlQueryMockImpl = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        strings: [
                            {
                                key: '@sage/xtrem-show-case/enums__show_case_product_category__awful',
                                content: 'Awful',
                            },
                            {
                                key: '@sage/xtrem-show-case/enums__show_case_product_category__good',
                                content: 'Good',
                            },
                            {
                                key: '@sage/xtrem-show-case/enums__show_case_product_category__great',
                                content: 'Great',
                            },
                            {
                                key: '@sage/xtrem-show-case/enums__show_case_product_category__notBad',
                                content: 'Not bad',
                            },
                            {
                                key: '@sage/xtrem-show-case/enums__show_case_product_category__ok',
                                content: 'Ok',
                            },
                        ],
                    },
                };
            });
            executeGraphqlQueryMock.mockImplementation(executeGraphqlQueryMockImpl);

            const result = await metadataService.fetchEnumTranslations(['MyTestEnum'], 'fr-FR');

            expect(result).toEqual({
                '@sage/xtrem-show-case/enums__show_case_product_category__awful': 'Awful',
                '@sage/xtrem-show-case/enums__show_case_product_category__good': 'Good',
                '@sage/xtrem-show-case/enums__show_case_product_category__great': 'Great',
                '@sage/xtrem-show-case/enums__show_case_product_category__notBad': 'Not bad',
                '@sage/xtrem-show-case/enums__show_case_product_category__ok': 'Ok',
            });
        });
    });

    describe('fetchInitialEnumSchemaInfo', () => {
        beforeEach(() => {
            jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockResolvedValue(() => ({ data: {} }));

            mockState.screenDefinitions[screenId] = pageDefinition!;
        });

        it('should not query the enum schema information from the server if no enum definitions are missing', async () => {
            await metadataService.fetchInitialEnumSchemaInfo(
                mockState.screenDefinitions[screenId],
                '1.2.3',
                'en-US',
                [],
            );
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
        });

        it('should query the enum schema information from the server', async () => {
            addFieldToState(FieldKey.DropdownList, mockState, screenId, elementId, {
                optionType: '@sage/xtrem-test/MyMissingEnum',
            });
            await metadataService.fetchInitialEnumSchemaInfo(
                mockState.screenDefinitions[screenId],
                '1.2.3',
                'en-US',
                [],
            );
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                cacheSettings: {
                    key: 'enum_type_query_391f623624df57d0d43c8a0ac5b5644c',
                    locale: 'en-US',
                    shouldFetchPlatformLiterals: false,
                    version: '1.2.3',
                },
                query: {
                    MyMissingEnum: {
                        __aliasFor: '__type',
                        __args: { name: 'MyMissingEnum' },
                        enumValues: { name: true },
                    },
                },
            });
        });

        it('should query the enum schema information from the server for multiple enums', async () => {
            addFieldToState(FieldKey.DropdownList, mockState, screenId, elementId, {
                optionType: '@sage/xtrem-test/MyMissingEnum',
            });
            addFieldToState(FieldKey.DropdownList, mockState, screenId, 'otherField', {
                optionType: '@sage/xtrem-test/MyOtherMissingEnum',
            });
            await metadataService.fetchInitialEnumSchemaInfo(
                mockState.screenDefinitions[screenId],
                '1.2.3',
                'fr-FR',
                [],
            );
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                cacheSettings: {
                    key: 'enum_type_query_96cdfe024768cc81df54873fcf35ed39',
                    locale: 'fr-FR',
                    shouldFetchPlatformLiterals: false,
                    version: '1.2.3',
                },
                query: {
                    MyMissingEnum: {
                        __aliasFor: '__type',
                        __args: { name: 'MyMissingEnum' },
                        enumValues: { name: true },
                    },
                    MyOtherMissingEnum: {
                        __aliasFor: '__type',
                        __args: { name: 'MyOtherMissingEnum' },
                        enumValues: { name: true },
                    },
                },
            });
        });
    });

    describe('fetchInitialSchemaInfo', () => {
        beforeEach(() => {
            jest.spyOn(storageService, 'get').mockImplementation(key => key);
            jest.spyOn(graphQLQueryBuilder, 'parseFilterEnumFields').mockImplementation(
                filter => filter as GraphQLFilter,
            );
        });

        it('should query the schema information from the server', async () => {
            const pageProperties: PageProperties<Page> = {
                node: '@sage/x3-sales/SalesOrder',
                isTransient: true,
            };

            pageDefinition!.metadata = {
                ...pageDefinition!.metadata,
                screenId,
            };

            (pageDefinition!.metadata.uiComponentProperties as Dict<PageProperties<Page>>) = {
                [screenId]: pageProperties,
            };

            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(() =>
                Promise.resolve({
                    data: {
                        getNodeDetailsList: [
                            {
                                name: 'SalesOrder',
                                title: 'SalesOrder',
                                defaultDataType: 'SalesOrder',
                                defaultDataTypeDetails: {
                                    name: 'salesOrder',
                                    type: 'reference',
                                    title: 'Show case order',
                                    node: '@sage/xtrem-show-case/SalesOrder',
                                    precision: null,
                                    scale: null,
                                    maxLength: null,
                                    value: {
                                        bind: 'customer.name',
                                        title: 'Name',
                                        type: 'string',
                                    },
                                    helperText: {
                                        bind: 'customer.name',
                                        title: 'Name',
                                        type: 'string',
                                    },
                                    columns: [
                                        {
                                            bind: 'customer.name',
                                            title: 'Name',
                                            type: 'string',
                                        },
                                        {
                                            bind: 'customer.email',
                                            title: 'Email',
                                            type: 'string',
                                        },
                                    ],
                                    values: null,
                                },
                                properties: [
                                    {
                                        name: '_attachments',
                                        title: '_attachments',
                                        canSort: false,
                                        canFilter: false,
                                        type: 'collection',
                                        isCustom: false,
                                        isMutable: true,
                                        isOnInputType: true,
                                        isOnOutputType: true,
                                        enumType: null,
                                        dataType: 'attachmentAssociation',
                                        dataTypeDetails: {
                                            name: 'attachmentAssociation',
                                            type: 'reference',
                                            title: 'Attachment association',
                                            node: '@sage/xtrem-upload/AttachmentAssociation',
                                            precision: null,
                                            scale: null,
                                            maxLength: null,
                                            value: {
                                                bind: '_id',
                                                title: 'ID',
                                                type: 'integer',
                                            },
                                            helperText: {
                                                bind: '_id',
                                                title: 'ID',
                                                type: 'integer',
                                            },
                                            columns: [
                                                {
                                                    bind: '_id',
                                                    title: 'ID',
                                                    type: 'integer',
                                                },
                                            ],
                                            values: null,
                                        },
                                        targetNode: '@sage/xtrem-upload/AttachmentAssociation',
                                    },
                                ],
                            },
                        ],
                    },
                }),
            );
            jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);
            const result = await metadataService.fetchInitialNodeDetails(pageDefinition!, '1.2.3', 'en-US', []);
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
            expect(result.nodeTypes.SalesOrder).toBeDefined();
        });
    });
});
