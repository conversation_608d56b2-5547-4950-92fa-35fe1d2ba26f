import { getMockPageDefinition } from '../../__tests__/test-helpers/common-helpers';
import type { PageDecoratorProperties } from '../../component/decorators';
import { createNavigationTableProperties, resolveOptionsMenu } from '../navigation-panel-service';
import type { PageDefinition } from '../page-definition';
import * as nestedFields from '../../component/nested-fields';
import type { RuntimePageNavigationPanel } from '../../component/control-objects';

describe('navigation panel service', () => {
    let pageDefinition: PageDefinition;

    beforeEach(() => {
        pageDefinition = getMockPageDefinition('TestPage');
    });
    describe('resolveOptionsMenu', () => {
        it('should return an empty array if no option menu is defined', async () => {
            const result = await resolveOptionsMenu(pageDefinition, []);
            expect(result).toEqual([]);
        });

        it('should return an array if an option menu list is defined', async () => {
            const result = await resolveOptionsMenu(pageDefinition, [
                { title: 'test', graphQLFilter: { test: { _eq: true } } },
                { title: 'test2', graphQLFilter: { amount: { _gt: 5 } } },
            ]);
            expect(result).toEqual([
                { title: 'test', graphQLFilter: { test: { _eq: true } } },
                { title: 'test2', graphQLFilter: { amount: { _gt: 5 } } },
            ]);
        });

        it('should return an array if an option menu list is defined as a function', async () => {
            const result = await resolveOptionsMenu(
                pageDefinition,
                jest.fn().mockResolvedValue([
                    { title: 'test', graphQLFilter: { test: { _eq: true } } },
                    { title: 'test2', graphQLFilter: { amount: { _gt: 5 } } },
                ]),
            );

            expect(result).toEqual([
                { title: 'test', graphQLFilter: { test: { _eq: true } } },
                { title: 'test2', graphQLFilter: { amount: { _gt: 5 } } },
            ]);
        });

        it('should add both "My selection" and "All items" to the option menu if a filter query param is defined and the menu is not defined', async () => {
            pageDefinition.queryParameters = { _filter: '{"_product":{"_eq":"test"}}' };
            const result = await resolveOptionsMenu(pageDefinition, []);
            expect(result).toEqual([
                {
                    graphQLFilter: {
                        _product: {
                            _eq: 'test',
                        },
                    },
                    title: 'My selected data',
                },
                {
                    graphQLFilter: {},
                    title: 'All',
                },
            ]);
        });

        it('should only add My selection the option menu if a filter query param is defined and there is an option menu defined too', async () => {
            pageDefinition.queryParameters = { _filter: '{"_product":{"_eq":"test"}}' };
            const result = await resolveOptionsMenu(pageDefinition, [
                { title: 'test', graphQLFilter: { test: { _eq: true } } },
                { title: 'test2', graphQLFilter: { amount: { _gt: 5 } } },
            ]);
            expect(result).toEqual([
                {
                    graphQLFilter: {
                        _product: {
                            _eq: 'test',
                        },
                    },
                    title: 'My selected data',
                },
                { title: 'test', graphQLFilter: { test: { _eq: true } } },
                { title: 'test2', graphQLFilter: { amount: { _gt: 5 } } },
            ]);
        });
    });

    describe('createNavigationTableProperties', () => {
        const screenId = 'TestPage';
        let pageDecoratorProperties: PageDecoratorProperties<any>;

        beforeEach(() => {
            pageDecoratorProperties = {
                navigationPanel: {
                    listItem: {
                        title: nestedFields.text<any>({ bind: 'product' }),
                        titleRight: nestedFields.label<any>({
                            bind: 'category',
                            optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
                        }),
                        line2: nestedFields.reference<any>({
                            bind: 'provider',
                            valueField: 'textField',
                            node: '@sage/xtrem-show-case/ShowCaseProvider',
                        }),
                        line2Right: nestedFields.text<any>({
                            bind: 'someField',
                        }),
                        line3: nestedFields.text<any>({ bind: 'releaseDate' }),
                        line3Right: nestedFields.text<any>({ bind: 'endingDate' }),
                    },
                },
            } as PageDecoratorProperties<any>;
        });

        it('should set basic table properties for the navigation panel', async () => {
            const screenDefinition = getMockPageDefinition(
                screenId,
                { navigationPanel: { isOpened: true } as any },
                {
                    uiComponentProperties: {
                        [screenId]: pageDecoratorProperties,
                    },
                },
            );

            const result = await createNavigationTableProperties(
                screenId,
                '@sage/xtrem-test/AnyNode',
                screenDefinition,
                {},
                {},
            );

            expect(result.canExport).toEqual(true);
            expect(result.isReadOnly).toEqual(true);
            expect(result.hasSearchBoxMobile).toEqual(true);
            expect(result.canResizeColumns).toEqual(true);
            expect(result.canUserHideColumns).toEqual(true);
            expect(result.pageSize).toEqual(20);
        });

        it('should map all mobile card properties', async () => {
            const screenDefinition = getMockPageDefinition(
                screenId,
                { navigationPanel: { isOpened: true } as any },
                {
                    uiComponentProperties: {
                        [screenId]: pageDecoratorProperties,
                    },
                },
            );

            const result = await createNavigationTableProperties(
                screenId,
                '@sage/xtrem-test/AnyNode',
                screenDefinition,
                {},
                {},
            );

            expect(Object.keys(result?.mobileCard || {})).toEqual([
                'title',
                'titleRight',
                'line2',
                'line2Right',
                'line3',
                'line3Right',
            ]);
        });

        it('should map all mobile card properties for extended pages', async () => {
            const screenDefinition = getMockPageDefinition(
                screenId,
                { navigationPanel: { isOpened: true } as any },
                {
                    uiComponentProperties: {
                        [screenId]: {
                            ...pageDecoratorProperties,
                            navigationPanel: {
                                ...pageDecoratorProperties.navigationPanel,
                                extensionListItem: [
                                    {
                                        line2: nestedFields.text<any>({ bind: 'fieldFromExtension' }),
                                        line4: nestedFields.text<any>({ bind: 'testline4' }),
                                        line4Right: nestedFields.text<any>({ bind: 'testline4r' }),
                                        line5: nestedFields.text<any>({ bind: 'testline5' }),
                                        line5Right: nestedFields.text<any>({ bind: 'testline5r' }),
                                    },
                                ],
                            } as RuntimePageNavigationPanel<any>,
                        } as PageDecoratorProperties<any>,
                    },
                },
            );

            const result = await createNavigationTableProperties(
                screenId,
                '@sage/xtrem-test/AnyNode',
                screenDefinition,
                {},
                {},
            );

            expect(Object.keys(result.mobileCard || {})).toEqual([
                'title',
                'titleRight',
                'line2',
                'line2Right',
                'line3',
                'line3Right',
                'line4',
                'line4Right',
                'line5',
                'line5Right',
            ]);

            expect(result.mobileCard?.line2?.properties.bind).toEqual('fieldFromExtension');
            expect(result.mobileCard?.line5?.properties.bind).toEqual('testline5');
        });

        it('should map all mobile card properties for extended pages, adding', async () => {
            const screenDefinition = getMockPageDefinition(
                screenId,
                { navigationPanel: { isOpened: true } as any },
                {
                    uiComponentProperties: {
                        [screenId]: {
                            ...pageDecoratorProperties,
                            navigationPanel: {
                                ...pageDecoratorProperties.navigationPanel,
                                extensionListItem: [
                                    {
                                        line4: nestedFields.text<any>({ bind: 'fieldFromExtension' }),
                                    },
                                ],
                            } as RuntimePageNavigationPanel<any>,
                        } as PageDecoratorProperties<any>,
                    },
                },
            );

            const result = await createNavigationTableProperties(
                screenId,
                '@sage/xtrem-test/AnyNode',
                screenDefinition,
                {},
                {},
            );

            expect(Object.keys(result.mobileCard || {})).toEqual([
                'title',
                'titleRight',
                'line2',
                'line2Right',
                'line3',
                'line3Right',
                'line4',
            ]);

            expect(result.mobileCard?.line4?.properties.bind).toEqual('fieldFromExtension');
        });

        it('should map all mobile card properties for extended pages, removing', async () => {
            const screenDefinition = getMockPageDefinition(
                screenId,
                { navigationPanel: { isOpened: true } as any },
                {
                    uiComponentProperties: {
                        [screenId]: {
                            ...pageDecoratorProperties,
                            navigationPanel: {
                                ...pageDecoratorProperties.navigationPanel,
                                extensionListItem: [
                                    {
                                        line2Right: null,
                                    },
                                ],
                            } as RuntimePageNavigationPanel<any>,
                        } as PageDecoratorProperties<any>,
                    },
                },
            );

            const result = await createNavigationTableProperties(
                screenId,
                '@sage/xtrem-test/AnyNode',
                screenDefinition,
                {},
                {},
            );

            expect(Object.keys(result.mobileCard || {})).toEqual([
                'title',
                'titleRight',
                'line2',
                'line3',
                'line3Right',
            ]);
        });

        it('should set the sorting condition based on the first visible field', async () => {
            const screenDefinition = getMockPageDefinition(
                screenId,
                { navigationPanel: { isOpened: true } as any },
                {
                    uiComponentProperties: {
                        [screenId]: pageDecoratorProperties,
                    },
                },
            );

            const result = await createNavigationTableProperties(
                screenId,
                '@sage/xtrem-test/AnyNode',
                screenDefinition,
                {},
                {},
            );
            expect(result.orderBy).toEqual({ product: 1, provider: { textField: 1 } });
        });
    });
});
