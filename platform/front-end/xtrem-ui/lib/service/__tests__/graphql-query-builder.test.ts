import {
    createFieldControlObject,
    createNoValueControlObject,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../__tests__/test-helpers';

// DO NOT REARRANGE THE IMPORTS
import { DateValue } from '@sage/xtrem-date-time';
import type { Dict } from '@sage/xtrem-shared';
import { get } from 'lodash';
import { ChartTypes } from '../../component/chart-types';
import type { PageProperties } from '../../component/container/container-properties';
import type {
    AggregateProperties,
    CalendarProperties,
    ChartProperties,
    InternalChartProperties,
    PodCollectionProperties,
    ReferenceProperties,
    TableProperties,
    TableSummaryProperties,
    VitalPodProperties,
} from '../../component/control-objects';
import {
    AggregateControlObject,
    CalendarControlObject,
    ChartControlObject,
    DateControlObject,
    ImageControlObject,
    NumericControlObject,
    PodCollectionControlObject,
    ReferenceControlObject,
    SelectControlObject,
    TableControlObject,
    TextControlObject,
    VitalPodControlObject,
} from '../../component/control-objects';
import type { ReferenceDecoratorProperties } from '../../component/decorator-properties';
import type { InternalCalendarProperties } from '../../component/field/calendar/calendar-types';
import type { InternalTableProperties } from '../../component/field/table/table-component-types';
import * as nestedFields from '../../component/nested-fields';
import type {
    ReadonlyFieldControlObject,
    ReadonlyFieldProperties,
} from '../../component/readonly-field-control-object';
import { FieldKey } from '../../component/types';
import { GraphQLKind, GraphQLTypes } from '../../types';
import * as formatters from '../../utils/formatters';
import * as transformers from '../../utils/transformers';
import { CollectionValue } from '../collection-data-service';
import { CollectionFieldTypes } from '../collection-data-types';
import {
    addReferenceIdsAssign,
    addTotalCountTransform,
    buildDefaultsQuery,
    buildEnumQueries,
    buildEnumQuery,
    buildFieldQuery,
    buildNestedDefaultsQuery,
    buildRootNodeQuery,
    buildRowDefaultQuery,
    buildTableQuery,
    convertFilterDecoratorToGraphQLFilter,
    createOrderByQueryArgument,
    getDirtyNestedValues,
    getFieldProperties,
    getNestedFieldsQuery,
    getRequestableFieldIds,
    parseFilterEnumFields,
    queryBuilder,
} from '../graphql-query-builder';
import type { GraphQLFilter } from '../graphql-utils';
import { queryToGraphQuery } from '../graphql-utils';
import type { Page } from '../page';
import type { PageDefinition } from '../page-definition';
import type { PageMetadata } from '../page-metadata';
import type { ScreenBase } from '../screen-base';
import * as valueSerializerService from '../value-serializer-service';

describe('graphql-query-builder', () => {
    const screenId = 'TestPage';
    let pageDefinition: PageDefinition | null = null;

    beforeEach(() => {
        pageDefinition = getMockPageDefinition(screenId, {}, { uiComponentProperties: { [screenId]: {} } });
        const mockState = getMockState();
        mockState.screenDefinitions = { [screenId]: pageDefinition! };
        mockState.nodeTypes = {
            MyTestNestedNode: {
                name: 'MyTestNestedNode',
                title: 'MyTestNestedNode',
                packageName: '@sage/xtrem-test',
                properties: {
                    orderedQuantity: { kind: GraphQLKind.Scalar, type: GraphQLTypes.Int },
                    isInvoiced: { kind: GraphQLKind.Scalar, type: GraphQLTypes.Boolean },
                    orderCategory: { kind: GraphQLKind.Scalar, type: GraphQLTypes.String },
                },
                mutations: {},
            },
        };
        getMockStore(mockState);
    });

    describe('parseFilterEnumFields', () => {
        it('should replace enum operators with the corresponding object', () => {
            const originalFilter: GraphQLFilter = {
                _or: [{ company$Name: { _eq: 'EnumKey', _isEnum: true } }],
            };
            const expected: GraphQLFilter = {
                _or: [{ company$Name: { _eq: { value: 'EnumKey' } } }],
            };
            const result = parseFilterEnumFields(originalFilter);
            expect(result).toEqual(expected);
        });
    });

    describe('createOrderByQueryArgument', () => {
        it('should remap simple conditions', () => {
            expect(createOrderByQueryArgument({ name: 1, date: -1, isAdmin: 1 })).toEqual(
                '{"name":1,"date":-1,"isAdmin":1}',
            );
        });

        it('should remap _id fields to _id due to the server inconsistency', () => {
            expect(createOrderByQueryArgument({ _id: 1, date: -1 })).toEqual('{"_id":1,"date":-1}');
        });

        it('should remap __ joint nested field filter definitions', () => {
            expect(createOrderByQueryArgument({ user__name: 1, location__country: -1, date: -1 })).toEqual(
                '{"user":{"name":1},"location":{"country":-1},"date":-1}',
            );
        });

        it('should _id fields on nested filters', () => {
            expect(createOrderByQueryArgument({ user___id: -1, date: 1 })).toEqual('{"user":{"_id":-1},"date":1}');
        });
    });

    describe('queryBuilder', () => {
        it('Should generate query object with simple attributes', () => {
            const properties = ['_id', 'orderDate', 'orderCountryName'];
            const result = queryBuilder('salesOrder', { properties });
            expect(queryToGraphQuery(result)).toMatchSnapshot();
        });

        it('Should generate query object without properties', () => {
            const result = queryBuilder('salesOrder', {});
            expect(queryToGraphQuery(result)).toMatchSnapshot();
        });

        it('Should generate query object with reference attributes', () => {
            const properties = [
                {
                    billToCustomer: {
                        code: true,
                        companyName: true,
                    },
                },
                {
                    payByCustomer: {
                        code: true,
                        currency: {
                            iso: true,
                        },
                    },
                },
            ];
            const result = queryBuilder('salesOrder', { properties });
            expect(queryToGraphQuery(result)).toMatchSnapshot();
        });

        it('Should generate a query object with both simple & reference attributes', () => {
            const properties = [
                'orderDate',
                '_id',
                'orderCountryName',
                {
                    billToCustomer: {
                        code: true,
                        companyName: true,
                    },
                },
                {
                    payByCustomer: {
                        code: true,
                        currency: {
                            iso: true,
                        },
                    },
                },
            ];
            const result = queryBuilder('salesOrder', { properties });
            expect(queryToGraphQuery(result)).toMatchSnapshot();
        });

        it('Should generate a query object with arguments ', () => {
            const properties = ['orderDate', 'orderCountryName'];
            const queryArguments = {
                filter: JSON.stringify({
                    _id: { _eq: 'SalesOrder:QTEFR0110040' },
                }),
                first: 10,
            };
            const result = queryBuilder('salesOrder', { properties, queryArguments });
            expect(queryToGraphQuery(result)).toMatchSnapshot();
        });

        it('Should generate a query object with _atLeast quantifier in filter', () => {
            const properties = ['orderDate', 'orderCountryName', 'lines'];
            const queryArguments = {
                filter: JSON.stringify({
                    _id: { _eq: 'SalesOrder:QTEFR0110040' },
                    lines: { _atLeast: 3, orderQuantity: { _gt: 50 } },
                }),
                first: 10,
            };
            const result = queryBuilder('salesOrder', { properties, queryArguments });
            expect(queryToGraphQuery(result)).toMatchSnapshot();
        });

        it('Should generate a query object with complex _atLeast quantifier in filter', () => {
            const properties = ['orderDate', 'orderCountryName', 'lines'];
            const queryArguments = {
                filter: JSON.stringify({
                    _id: { _eq: 'SalesOrder:QTEFR0110040' },
                    _and: [
                        {
                            lines: { _atLeast: 3, product: { qty: { _gt: 15 } }, orderQuantity: { _gt: 50 } },
                            newLines: {
                                _atLeast: 3,
                                _and: [{ newProduct: { qty: { _gt: 15 } }, newOrderQuantity: { _gt: 50 } }],
                            },
                        },
                    ],
                }),
                first: 10,
            };
            const result = queryBuilder('salesOrder', { properties, queryArguments });
            expect(queryToGraphQuery(result)).toMatchSnapshot();
        });

        it('Should generate a query object with alias', () => {
            const properties = ['orderDate', 'orderCountryName'];
            const alias = 'navigationPanelOrders';
            const result = queryBuilder('salesOrder', { properties, alias });
            expect(queryToGraphQuery(result)).toMatchSnapshot();
        });

        it('should be able to merge two queries and make a valid graphQL query', () => {
            const propertiesSalesOrder = ['orderDate', 'orderCountryName'];
            const propertiesTimeEntry = ['task'];
            const salesOrder = queryBuilder('salesOrder', { properties: propertiesSalesOrder });
            const timeEntry = queryBuilder('timeEntry', { properties: propertiesTimeEntry });
            const mergedQueries = { ...salesOrder, ...timeEntry };
            expect(queryToGraphQuery(mergedQueries)).toMatchSnapshot();
        });
    });

    describe('buildEnumQuery', () => {
        it('should generate a query object to receive the enums of a property', () => {
            const enumName = 'OrderCityEnum';
            const result = buildEnumQuery(enumName);
            expect(queryToGraphQuery(result)).toMatchSnapshot();
        });
    });

    describe('buildTableQuery', () => {
        const nestedFieldsDefinitions: nestedFields.NestedField<Page, nestedFields.NestedFieldTypes>[] = [
            {
                defaultUiProperties: { ...TextControlObject.defaultUiProperties, bind: 'item' },
                properties: { bind: 'item', title: 'Item', _controlObjectType: FieldKey.Text },
                type: FieldKey.Text,
            },
            {
                defaultUiProperties: { ...NumericControlObject.defaultUiProperties, bind: 'quantity' },
                properties: { bind: 'quantity', title: 'Quantity', scale: 0, _controlObjectType: FieldKey.Numeric },
                type: FieldKey.Numeric,
            },
        ];

        it('should build a table query with filter', () => {
            const result = buildTableQuery({
                rootNodeId: 'testNode',
                elementId: 'testId1',
                columns: nestedFieldsDefinitions,
                screenDefinition: pageDefinition!,
                queryArguments: {
                    filter: JSON.stringify({ item: { _eq: 'test' } }),
                },
            });
            expect(queryToGraphQuery(result)).toMatchSnapshot();
        });

        it('should build a table query with a simple bind property', () => {
            const result = buildTableQuery({
                rootNodeId: 'testNode',
                elementId: 'lorem',
                columns: nestedFieldsDefinitions,
                screenDefinition: pageDefinition!,
                queryArguments: {
                    filter: JSON.stringify({ item: { _eq: 'test' } }),
                },
                bind: 'ipsum',
            });
            expect(result).toEqual({
                query: {
                    __args: {
                        filter: '{"_id":{"_eq":"testNode"}}',
                    },
                    edges: {
                        cursor: true,
                        node: {
                            _id: true,
                            lorem: {
                                __aliasFor: 'ipsum',
                                query: {
                                    __args: {
                                        filter: '{"item":{"_eq":"test"}}',
                                    },
                                    edges: {
                                        cursor: true,
                                        node: {
                                            _id: true,
                                            item: true,
                                            quantity: true,
                                        },
                                    },
                                    pageInfo: {
                                        endCursor: true,
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                    },
                                },
                            },
                        },
                    },
                    pageInfo: {
                        endCursor: true,
                        hasNextPage: true,
                        hasPreviousPage: true,
                        startCursor: true,
                    },
                },
            });
        });

        it('should build a table query with nested bind property', () => {
            const result = buildTableQuery({
                rootNodeId: 'testNode',
                elementId: 'lorem',
                columns: nestedFieldsDefinitions,
                screenDefinition: pageDefinition!,
                queryArguments: {
                    filter: JSON.stringify({ item: { _eq: 'test' } }),
                },
                bind: { ipsum: { emit: { sit: true } } },
            });
            expect(result).toEqual({
                query: {
                    __args: {
                        filter: '{"_id":{"_eq":"testNode"}}',
                    },
                    edges: {
                        cursor: true,
                        node: {
                            _id: true,
                            lorem: {
                                __aliasFor: 'ipsum',
                                emit: {
                                    sit: {
                                        query: {
                                            __args: {
                                                filter: '{"item":{"_eq":"test"}}',
                                            },
                                            edges: {
                                                cursor: true,
                                                node: {
                                                    _id: true,
                                                    item: true,
                                                    quantity: true,
                                                },
                                            },
                                            pageInfo: {
                                                endCursor: true,
                                                hasNextPage: true,
                                                hasPreviousPage: true,
                                                startCursor: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                    pageInfo: {
                        endCursor: true,
                        hasNextPage: true,
                        hasPreviousPage: true,
                        startCursor: true,
                    },
                },
            });
        });
    });

    describe('buildFieldQuery', () => {
        const callBuildFieldQueryAndAssert = (
            controlObject: ReadonlyFieldControlObject<any, FieldKey, any>,
            properties:
                | InternalTableProperties<Page>
                | TableSummaryProperties<Page>
                | (ChartProperties & InternalChartProperties)
                | CalendarProperties
                | AggregateProperties
                | VitalPodProperties<Page, any>,
        ) => {
            const query = buildFieldQuery(
                'testObject',
                controlObject,
                pageDefinition!,
                properties as any,
                'fieldId',
                '',
                {},
            );
            expect(queryToGraphQuery(query)).toMatchSnapshot();
        };

        describe('should build a query for Table field', () => {
            const columns: nestedFields.NestedField<any, nestedFields.GridNestedFieldTypes, any>[] = [
                {
                    type: FieldKey.Text,
                    properties: {
                        bind: 'textProperty',
                        canFilter: true,
                    },
                    defaultUiProperties: { bind: 'textProperty' },
                },
                {
                    type: FieldKey.Numeric,
                    properties: {
                        bind: 'numericProperty',
                        canFilter: true,
                    },
                    defaultUiProperties: { bind: 'numericProperty' },
                },
            ];
            const properties: InternalTableProperties = {
                bind: 'fieldId',
                title: 'propertyTitle',
                filter: {
                    textProperty: { _eq: 'bindProp' },
                },
                pageSize: 20,
                columns,
            };
            const controlObject = createFieldControlObject<FieldKey.Table>(
                FieldKey.Table,
                screenId,
                TableControlObject,
                'fieldId',
                undefined,
                properties,
            );

            it('query with default filter', () => {
                callBuildFieldQueryAndAssert(controlObject, properties);
            });

            it('should build a query with an option menu', () => {
                callBuildFieldQueryAndAssert(controlObject, {
                    ...properties,
                    filter: undefined,
                    optionsMenu: () => {
                        return [
                            {
                                title: 'Option menu 1',
                                graphQLFilter: {
                                    orderDate: { _lt: '2022-02-22' },
                                },
                            },
                            {
                                title: 'Option menu 2',
                                graphQLFilter: {
                                    orderDate: { _lt: '2022-02-23' },
                                },
                            },
                            {
                                title: 'Option menu 3',
                                graphQLFilter: {
                                    orderDate: { _lt: '2022-02-24' },
                                },
                            },
                        ];
                    },
                });
            });

            it('should build a query with an option menu and filter combined', () => {
                callBuildFieldQueryAndAssert(controlObject, {
                    ...properties,
                    filter: {
                        orderDate: { _gt: '2022-02-24' },
                    },
                    optionsMenu: () => {
                        return [
                            {
                                title: 'Option menu 1',
                                graphQLFilter: {
                                    orderDate: { _lt: '2022-02-22' },
                                },
                            },
                            {
                                title: 'Option menu 2',
                                graphQLFilter: {
                                    orderDate: { _lt: '2022-02-23' },
                                },
                            },
                            {
                                title: 'Option menu 3',
                                graphQLFilter: {
                                    orderDate: { _lt: '2022-02-24' },
                                },
                            },
                        ];
                    },
                });
            });
        });

        describe('should build a query for Chart field', () => {
            const properties: ChartProperties & InternalChartProperties = {
                title: 'TEST_FIELD_TITLE',
                chart: {
                    type: ChartTypes.Bar,
                    series: [
                        {
                            type: FieldKey.Numeric,
                            properties: {
                                bind: 'numericProperty1',
                                canFilter: true,
                            },
                            defaultUiProperties: { bind: 'numericProperty1' },
                        },
                        {
                            type: FieldKey.Numeric,
                            properties: {
                                bind: 'numericProperty2',
                                canFilter: true,
                            },
                            defaultUiProperties: { bind: 'numericProperty2' },
                        },
                    ],
                    xAxis: {
                        type: FieldKey.Text,
                        properties: {
                            bind: 'xAxis',
                            title: 'Chart xAxis',
                        },
                        defaultUiProperties: { bind: 'xAxis' },
                    },
                },
                filter: () => ({
                    _and: [{ numericProperty1: { _lte: '1' } }, { numericProperty1: { _gte: '5' } }],
                }),
            };
            const controlObject = createFieldControlObject<FieldKey.Chart>(
                FieldKey.Chart,
                screenId,
                ChartControlObject,
                'fieldId',
                undefined,
                properties,
            );

            it('query with default filter', () => {
                callBuildFieldQueryAndAssert(controlObject, properties);
            });

            it('query with default and user filter', () => {
                properties.activeUserFilter = {
                    _and: [{ numericProperty2: { _lte: '10' } }, { numericProperty2: { _gte: '50' } }],
                };
                callBuildFieldQueryAndAssert(controlObject, properties);
            });
        });

        describe('should build a query for Aggregate field', () => {
            let properties: AggregateProperties;
            let controlObject: AggregateControlObject;

            beforeEach(() => {
                properties = {
                    title: 'TEST_FIELD_TITLE',
                    aggregateOn: 'anyRandomField',
                    aggregationMethod: 'distinctCount',
                    filter: () => ({
                        _and: [{ numericProperty1: { _lte: '1' } }, { numericProperty1: { _gte: '5' } }],
                    }),
                };
                controlObject = createFieldControlObject<FieldKey.Aggregate>(
                    FieldKey.Aggregate,
                    screenId,
                    AggregateControlObject,
                    'fieldId',
                    undefined,
                    properties,
                );
            });

            it('query without a filter', () => {
                properties.filter = undefined;
                callBuildFieldQueryAndAssert(controlObject, properties);
            });

            it('query without a filter', () => {
                callBuildFieldQueryAndAssert(controlObject, properties);
            });

            it('query with default and user filter', () => {
                properties.filter = {
                    _and: [{ numericProperty2: { _lte: '10' } }, { numericProperty2: { _gte: '50' } }],
                };
                callBuildFieldQueryAndAssert(controlObject, properties);
            });
        });

        describe('should build a query for vital pod field', () => {
            let properties: VitalPodProperties<any, any>;
            let controlObject: VitalPodControlObject<any, any>;

            beforeEach(() => {
                properties = {
                    title: 'TEST_FIELD_TITLE',
                    columns: [
                        {
                            type: FieldKey.Reference,
                            defaultUiProperties: { bind: 'product' },
                            properties: {
                                bind: 'product',
                                valueField: 'code',
                                helperTextField: 'description1',
                            },
                        },
                        {
                            type: FieldKey.Text,
                            defaultUiProperties: { bind: 'expectedDeliveryDate' },
                            properties: {
                                bind: 'expectedDeliveryDate',
                            },
                        },
                    ],
                };
                controlObject = createFieldControlObject<FieldKey.VitalPod>(
                    FieldKey.VitalPod,
                    screenId,
                    VitalPodControlObject,
                    'fieldId',
                    undefined,
                    properties,
                );
            });

            it('query without a filter', () => {
                properties.filter = undefined;
                callBuildFieldQueryAndAssert(controlObject, properties);
            });

            it('query without a filter', () => {
                callBuildFieldQueryAndAssert(controlObject, properties);
            });

            it('query with header label', () => {
                properties.headerLabel = {
                    type: FieldKey.Label,
                    defaultUiProperties: { bind: 'orderCategory' },
                    properties: {
                        bind: 'orderCategory',
                    },
                };
                callBuildFieldQueryAndAssert(controlObject, properties);
            });
        });

        describe('should build a query for pod collection field', () => {
            let properties: PodCollectionProperties<any, any>;
            let controlObject: PodCollectionControlObject;

            beforeEach(() => {
                properties = {
                    title: 'TEST_FIELD_TITLE',
                    columns: [
                        {
                            type: FieldKey.Reference,
                            defaultUiProperties: { bind: 'product' },
                            properties: {
                                bind: 'product',
                                valueField: 'code',
                                helperTextField: 'description1',
                            },
                        },
                        {
                            type: FieldKey.Text,
                            defaultUiProperties: { bind: 'expectedDeliveryDate' },
                            properties: {
                                bind: 'expectedDeliveryDate',
                            },
                        },
                    ],
                };
                controlObject = createFieldControlObject<FieldKey.PodCollection>(
                    FieldKey.PodCollection,
                    screenId,
                    PodCollectionControlObject,
                    'fieldId',
                    undefined,
                    properties,
                );
            });

            it('query without a filter', () => {
                properties.filter = undefined;
                callBuildFieldQueryAndAssert(controlObject as any, properties as any);
            });

            it('query without a filter', () => {
                callBuildFieldQueryAndAssert(controlObject as any, properties as any);
            });

            it('query with header label', () => {
                properties.headerLabel = {
                    type: FieldKey.Label,
                    defaultUiProperties: { bind: 'orderCategory' },
                    properties: {
                        bind: 'orderCategory',
                    },
                };
                callBuildFieldQueryAndAssert(controlObject as any, properties as any);
            });
        });

        describe('should build a query for complex aggregate field with nested aggregation', () => {
            let properties: AggregateProperties;
            let controlObject: AggregateControlObject;

            beforeEach(() => {
                properties = {
                    title: 'TEST_FIELD_TITLE',
                    aggregateOn: {
                        anyRandomField: {
                            withAdditionalChild: {
                                property: true,
                            },
                        },
                    },
                    aggregationMethod: 'max',
                    filter: () => ({
                        _and: [{ numericProperty1: { _lte: '1' } }, { numericProperty1: { _gte: '5' } }],
                    }),
                };
                controlObject = createFieldControlObject<FieldKey.Aggregate>(
                    FieldKey.Aggregate,
                    screenId,
                    AggregateControlObject,
                    'fieldId',
                    undefined,
                    properties,
                );
            });

            it('query without a filter', () => {
                properties.filter = undefined;
                callBuildFieldQueryAndAssert(controlObject, properties);
            });

            it('query without a filter', () => {
                callBuildFieldQueryAndAssert(controlObject, properties);
            });

            it('query with default and user filter', () => {
                properties.filter = {
                    _and: [{ numericProperty2: { _lte: '10' } }, { numericProperty2: { _gte: '50' } }],
                };
                callBuildFieldQueryAndAssert(controlObject, properties);
            });
        });
    });

    describe('getFieldProperties', () => {
        const testElementId = 'myTestField';

        describe('no bind property', () => {
            it('should build simple text field query', () => {
                const result = getFieldProperties(testElementId, FieldKey.Text, { title: 'Hi!' }, pageDefinition!);
                expect(result).toEqual({ myTestField: true });
            });

            it('should build simple numeric field query', () => {
                const result = getFieldProperties(testElementId, FieldKey.Numeric, { title: 'Hi!' }, pageDefinition!);
                expect(result).toEqual({ myTestField: true });
            });

            it('should build simple checkbox field query', () => {
                const result = getFieldProperties(testElementId, FieldKey.Checkbox, { title: 'Hi!' }, pageDefinition!);
                expect(result).toEqual({ myTestField: true });
            });

            it('should build simple reference field query', () => {
                const result = getFieldProperties(
                    testElementId,
                    FieldKey.Reference,
                    {
                        title: 'Hi!',
                        node: '@sage/MyTestPackage/MyTestNode',
                        valueField: 'anyField',
                    } as ReferenceDecoratorProperties,
                    pageDefinition!,
                );
                expect(result).toEqual({
                    myTestField: {
                        _id: true,
                        anyField: true,
                    },
                });
            });

            it('should build reference field query with nested value field', () => {
                const result = getFieldProperties(
                    testElementId,
                    FieldKey.Reference,
                    {
                        title: 'Hi!',
                        node: '@sage/MyTestPackage/MyTestNode',
                        valueField: { anotherNode: 'anyField' } as any,
                    } as ReferenceDecoratorProperties,
                    pageDefinition!,
                );
                expect(result).toEqual({
                    myTestField: {
                        _id: true,
                        anotherNode: { anyField: true },
                    },
                });
            });

            it('should build reference field query with nested columns', () => {
                const result = getFieldProperties(
                    testElementId,
                    FieldKey.Reference,
                    {
                        title: 'Hi!',
                        node: '@sage/MyTestPackage/MyTestNode',
                        valueField: 'anyField',
                        columns: [nestedFields.text({ bind: { elit: { set: true } } as any, title: 'Column 1' })],
                    } as ReferenceDecoratorProperties,
                    pageDefinition!,
                );
                expect(result).toEqual({
                    myTestField: {
                        _id: true,
                        anyField: true,
                        elit: {
                            set: true,
                        },
                    },
                });
            });
        });

        describe('with bind property', () => {
            it('should build simple text field query', () => {
                const result = getFieldProperties(
                    testElementId,
                    FieldKey.Text,
                    { title: 'Hi!' },
                    pageDefinition!,
                    'loremIpsum',
                );
                expect(result).toEqual({ myTestField: { __aliasFor: 'loremIpsum' } });
            });

            it('should build simple numeric field query', () => {
                const result = getFieldProperties(
                    testElementId,
                    FieldKey.Numeric,
                    { title: 'Hi!' },
                    pageDefinition!,
                    'loremIpsum',
                );
                expect(result).toEqual({ myTestField: { __aliasFor: 'loremIpsum' } });
            });

            it('should build simple checkbox field query', () => {
                const result = getFieldProperties(
                    testElementId,
                    FieldKey.Checkbox,
                    { title: 'Hi!' },
                    pageDefinition!,
                    'loremIpsum',
                );
                expect(result).toEqual({ myTestField: { __aliasFor: 'loremIpsum' } });
            });

            it('should build simple reference field query', () => {
                const result = getFieldProperties(
                    testElementId,
                    FieldKey.Reference,
                    {
                        title: 'Hi!',
                        node: '@sage/MyTestPackage/MyTestNode',
                        valueField: 'anyField',
                    } as ReferenceDecoratorProperties,
                    pageDefinition!,
                    'loremIpsum',
                );
                expect(result).toEqual({
                    myTestField: {
                        __aliasFor: 'loremIpsum',
                        _id: true,
                        anyField: true,
                    },
                });
            });

            it('should build reference field query with nested value field', () => {
                const result = getFieldProperties(
                    testElementId,
                    FieldKey.Reference,
                    {
                        title: 'Hi!',
                        node: '@sage/MyTestPackage/MyTestNode',
                        valueField: { anotherNode: 'anyField' } as any,
                    } as ReferenceDecoratorProperties,
                    pageDefinition!,
                    'loremIpsum',
                );
                expect(result).toEqual({
                    myTestField: {
                        __aliasFor: 'loremIpsum',
                        _id: true,
                        anotherNode: { anyField: true },
                    },
                });
            });

            it('should build reference field query with nested columns', () => {
                const result = getFieldProperties(
                    testElementId,
                    FieldKey.Reference,
                    {
                        title: 'Hi!',
                        node: '@sage/MyTestPackage/MyTestNode',
                        valueField: 'anyField',
                        columns: [nestedFields.text({ bind: { elit: { set: true } } as any, title: 'Column 1' })],
                    } as ReferenceDecoratorProperties,
                    pageDefinition!,
                    'loremIpsum',
                );
                expect(result).toEqual({
                    myTestField: {
                        __aliasFor: 'loremIpsum',
                        _id: true,
                        anyField: true,
                        elit: {
                            set: true,
                        },
                    },
                });
            });
        });

        describe('with deep bind property', () => {
            it('should build simple text field query', () => {
                const result = getFieldProperties(testElementId, FieldKey.Text, { title: 'Hi!' }, pageDefinition!, {
                    lorem: { ipsum: { dolor: true } },
                });
                expect(result).toEqual({ myTestField: { __aliasFor: 'lorem', ipsum: { dolor: true } } });
            });

            it('should build simple numeric field query', () => {
                const result = getFieldProperties(testElementId, FieldKey.Numeric, { title: 'Hi!' }, pageDefinition!, {
                    lorem: { ipsum: { dolor: true } },
                });
                expect(result).toEqual({ myTestField: { __aliasFor: 'lorem', ipsum: { dolor: true } } });
            });

            it('should build simple checkbox field query', () => {
                const result = getFieldProperties(testElementId, FieldKey.Checkbox, { title: 'Hi!' }, pageDefinition!, {
                    lorem: { ipsum: { dolor: true } },
                });
                expect(result).toEqual({ myTestField: { __aliasFor: 'lorem', ipsum: { dolor: true } } });
            });

            it('should build simple reference field query', () => {
                const result = getFieldProperties(
                    testElementId,
                    FieldKey.Reference,
                    {
                        title: 'Hi!',
                        node: '@sage/MyTestPackage/MyTestNode',
                        valueField: 'anyField',
                    } as ReferenceDecoratorProperties,
                    pageDefinition!,
                    { lorem: { ipsum: { dolor: true } } },
                );
                expect(result).toEqual({
                    myTestField: {
                        __aliasFor: 'lorem',
                        ipsum: {
                            dolor: {
                                _id: true,
                                anyField: true,
                            },
                        },
                    },
                });
            });

            it('should build reference field query with nested value field', () => {
                const result = getFieldProperties(
                    testElementId,
                    FieldKey.Reference,
                    {
                        title: 'Hi!',
                        node: '@sage/MyTestPackage/MyTestNode',
                        valueField: { anotherNode: 'anyField' } as any,
                    } as ReferenceDecoratorProperties,
                    pageDefinition!,
                    { lorem: { ipsum: { dolor: true } } },
                );
                expect(result).toEqual({
                    myTestField: {
                        __aliasFor: 'lorem',
                        ipsum: {
                            dolor: {
                                _id: true,
                                anotherNode: { anyField: true },
                            },
                        },
                    },
                });
            });

            it('should build reference field query with nested columns', () => {
                const result = getFieldProperties(
                    testElementId,
                    FieldKey.Reference,
                    {
                        title: 'Hi!',
                        node: '@sage/MyTestPackage/MyTestNode',
                        valueField: 'anyField',
                        columns: [nestedFields.text({ bind: { elit: { set: true } } as any, title: 'Column 1' })],
                    } as ReferenceDecoratorProperties,
                    pageDefinition!,
                    { lorem: { ipsum: { dolor: true } } },
                );
                expect(result).toEqual({
                    myTestField: {
                        __aliasFor: 'lorem',
                        ipsum: {
                            dolor: {
                                _id: true,
                                anyField: true,
                                elit: {
                                    set: true,
                                },
                            },
                        },
                    },
                });
            });
        });
    });

    describe('buildRootNodeQuery', () => {
        let controlObjects: Dict<ReadonlyFieldControlObject<Page, FieldKey, ReadonlyFieldProperties<any>>>;
        const getComplexControlObjects = (): Dict<
            ReadonlyFieldControlObject<Page, FieldKey, ReadonlyFieldProperties<any>>
        > => {
            return {
                isSigned: createFieldControlObject<FieldKey.Select>(
                    FieldKey.Select,
                    screenId,
                    SelectControlObject,
                    'isSigned',
                    undefined,
                    {},
                ),
                orderNumber: createFieldControlObject<FieldKey.Text>(
                    FieldKey.Text,
                    screenId,
                    TextControlObject,
                    'orderNumber',
                    undefined,
                    {},
                ),
                billToCustomer: createFieldControlObject<FieldKey.Reference>(
                    FieldKey.Reference,
                    screenId,
                    ReferenceControlObject,
                    'billToCustomer',
                    undefined,
                    { node: '@sage/xtrem-ui-test/BillToCustomer', valueField: 'name' },
                ) as any,
                orderDate: createFieldControlObject<FieldKey.Date>(
                    FieldKey.Date,
                    screenId,
                    DateControlObject,
                    'customerOrderRef',
                    undefined,
                    {},
                ),
                revisionNumber: createFieldControlObject<FieldKey.Numeric>(
                    FieldKey.Numeric,
                    screenId,
                    NumericControlObject,
                    'revisionNumber',
                    undefined,
                    {},
                ),
                productImage: createFieldControlObject<FieldKey.Image>(
                    FieldKey.Image,
                    screenId,
                    ImageControlObject,
                    'productImage',
                    undefined,
                    {},
                ),
                lines: createFieldControlObject<FieldKey.Table>(
                    FieldKey.Table,
                    screenId,
                    TableControlObject,
                    'lines',
                    undefined,
                    {
                        pageSize: 20,
                        columns: [],
                    },
                ),
            };
        };

        const setComplexUIComponentProperties = () => {
            pageDefinition!.metadata.uiComponentProperties = {
                isSigned: {},
                orderNumber: {},
                billToCustomer: {
                    helperTextField: 'companyName',
                    valueField: 'code',
                } as ReferenceProperties<ScreenBase>,
                orderDate: {},
                revisionNumber: {},
                productImage: {},
                lines: {
                    pageSize: 20,
                    columns: [
                        {
                            type: FieldKey.Reference,
                            properties: {
                                bind: 'product',
                                valueField: 'code',
                                helperTextField: 'description1',
                            },
                        },
                        {
                            type: FieldKey.Text,
                            properties: {
                                bind: 'expectedDeliveryDate',
                                fetchesDefaults: true,
                            },
                        },
                        {
                            type: FieldKey.Numeric,
                            properties: {
                                bind: 'orderedQuantity',
                            },
                        },
                        {
                            type: FieldKey.Checkbox,
                            properties: {
                                bind: 'isInvoiced',
                            },
                        },
                        {
                            type: FieldKey.Label,
                            properties: {
                                bind: 'orderCategory',
                            },
                        },
                    ],
                } as InternalTableProperties,
                [screenId]: {},
            };
        };

        const callBuildRootNodeQueryAndAssert = () => {
            const query = buildRootNodeQuery('testObject', controlObjects, pageDefinition!, '1');
            expect(queryToGraphQuery(query)).toMatchSnapshot();
        };

        it('should build a query for a basic page', () => {
            controlObjects = {
                isSigned: createFieldControlObject<FieldKey.Select>(
                    FieldKey.Select,
                    screenId,
                    SelectControlObject,
                    'isSigned',
                    undefined,
                    {},
                ),
                orderNumber: createFieldControlObject<FieldKey.Text>(
                    FieldKey.Text,
                    screenId,
                    TextControlObject,
                    'orderNumber',
                    undefined,
                    {},
                ),
            };
            pageDefinition!.metadata.uiComponentProperties = {
                isSigned: {},
                orderNumber: {},
            };

            callBuildRootNodeQueryAndAssert();
        });

        it('should build a query for a complex page', () => {
            controlObjects = getComplexControlObjects();
            setComplexUIComponentProperties();
            callBuildRootNodeQueryAndAssert();
        });

        it('should build a query for a complex page with args', () => {
            controlObjects = getComplexControlObjects();
            setComplexUIComponentProperties();

            (pageDefinition!.metadata.uiComponentProperties.lines as TableProperties).orderBy = {
                expectedDeliveryDate: -1,
                product: 1,
            };

            (pageDefinition!.metadata.uiComponentProperties.lines as TableProperties).filter = {
                _or: [{ textField: { _regex: '$TEST' } }],
            };

            callBuildRootNodeQueryAndAssert();
        });

        describe('Calendar component', () => {
            let calendarProperties: CalendarProperties<ScreenBase>;

            const addCalendarPropertiesToPageDefinition = () => {
                pageDefinition!.metadata.uiComponentProperties = {
                    calendar: calendarProperties,
                };
            };

            beforeEach(() => {
                jest.spyOn(formatters, 'getFirstDayOfMonth').mockImplementation(() => DateValue.parse('2019-11-01'));
                jest.spyOn(formatters, 'getLastDayOfMonth').mockImplementation(() => DateValue.parse('2019-11-30'));
                calendarProperties = {
                    eventCard: {
                        title: {
                            defaultUiProperties: { ...TextControlObject.defaultUiProperties, bind: 'calendarTitle' },
                            properties: {
                                bind: 'calendarTitle',
                            },
                            type: FieldKey.Text,
                        },
                    },
                    startDateField: 'startDate',
                };

                controlObjects = {
                    calendar: createNoValueControlObject<FieldKey.Calendar>(
                        FieldKey.Calendar,
                        screenId,
                        CalendarControlObject,
                        'calendar',
                        calendarProperties,
                    ),
                };
            });

            it('should build a query for a calendar page', () => {
                addCalendarPropertiesToPageDefinition();
                callBuildRootNodeQueryAndAssert();
            });

            it('should build a query for a calendar page setting line2', () => {
                calendarProperties.eventCard.line2 = {
                    defaultUiProperties: { ...TextControlObject.defaultUiProperties, bind: 'calendarSubtitle' },
                    properties: {
                        bind: 'calendarSubtitle',
                    },
                    type: FieldKey.Text,
                };
                addCalendarPropertiesToPageDefinition();
                callBuildRootNodeQueryAndAssert();
            });

            it('should build a query for a calendar page setting left', () => {
                calendarProperties.eventCard.right = {
                    defaultUiProperties: { ...TextControlObject.defaultUiProperties, bind: 'right' },
                    properties: {
                        bind: 'right',
                    },
                    type: FieldKey.Text,
                };
                addCalendarPropertiesToPageDefinition();
                callBuildRootNodeQueryAndAssert();
            });

            it('should build a query for a calendar page setting left', () => {
                calendarProperties.eventCard.left = {
                    defaultUiProperties: { ...TextControlObject.defaultUiProperties, bind: 'left' },
                    properties: {
                        bind: 'left',
                    },
                    type: FieldKey.Text,
                };
                addCalendarPropertiesToPageDefinition();
                callBuildRootNodeQueryAndAssert();
            });

            it('should build a query for a calendar page setting endDateField', () => {
                calendarProperties.endDateField = 'endDate';
                addCalendarPropertiesToPageDefinition();
                callBuildRootNodeQueryAndAssert();
            });

            it('should build a query for a calendar page setting rangeStart', () => {
                (calendarProperties as InternalCalendarProperties<ScreenBase>).rangeStart = '2019-11-28';
                addCalendarPropertiesToPageDefinition();
                callBuildRootNodeQueryAndAssert();
            });

            it('should build a query for a calendar page setting rangeStart as Date', () => {
                (calendarProperties as InternalCalendarProperties<ScreenBase>).rangeStart = new Date('2019-11-28');
                addCalendarPropertiesToPageDefinition();
                callBuildRootNodeQueryAndAssert();
            });

            it('should build a query for a calendar page setting rangeEnd', () => {
                (calendarProperties as InternalCalendarProperties<ScreenBase>).rangeEnd = '2019-11-28';
                addCalendarPropertiesToPageDefinition();
                callBuildRootNodeQueryAndAssert();
            });

            it('should build a query for a calendar page setting rangeEnd as Date', () => {
                (calendarProperties as InternalCalendarProperties<ScreenBase>).rangeEnd = new Date('2019-11-28');
                addCalendarPropertiesToPageDefinition();
                callBuildRootNodeQueryAndAssert();
            });

            it('should build a query for a calendar page having elementId distinct from bind', () => {
                calendarProperties.bind = 'calendarBind';
                addCalendarPropertiesToPageDefinition();
                callBuildRootNodeQueryAndAssert();
            });
        });
    });

    describe('buildDefaultsQuery', () => {
        const getComplexControlObjects = (): Dict<
            ReadonlyFieldControlObject<Page, FieldKey, ReadonlyFieldProperties>
        > => {
            return {
                _id: createFieldControlObject<FieldKey.Text>(
                    FieldKey.Text,
                    screenId,
                    TextControlObject,
                    '_id',
                    undefined,
                    {},
                ),
                isSigned: createFieldControlObject<FieldKey.Select>(
                    FieldKey.Select,
                    screenId,
                    SelectControlObject,
                    'isSigned',
                    undefined,
                    {},
                ),
                orderNumber: createFieldControlObject<FieldKey.Text>(
                    FieldKey.Text,
                    screenId,
                    TextControlObject,
                    'orderNumber',
                    undefined,
                    {},
                ),
                billToCustomer: createFieldControlObject<FieldKey.Reference>(
                    FieldKey.Reference,
                    screenId,
                    ReferenceControlObject,
                    'billToCustomer',
                    undefined,
                    { node: '@sage/xtrem-ui-test/BillToCustomer', valueField: 'name' },
                ) as any,
                orderDate: createFieldControlObject<FieldKey.Date>(
                    FieldKey.Date,
                    screenId,
                    DateControlObject,
                    'customerOrderRef',
                    undefined,
                    { fetchesDefaults: true },
                ),
                revisionNumber: createFieldControlObject<FieldKey.Numeric>(
                    FieldKey.Numeric,
                    screenId,
                    NumericControlObject,
                    'revisionNumber',
                    undefined,
                    {},
                ),
                productImage: createFieldControlObject<FieldKey.Image>(
                    FieldKey.Image,
                    screenId,
                    ImageControlObject,
                    'productImage',
                    undefined,
                    {},
                ),
                lines: createFieldControlObject<FieldKey.Table>(
                    FieldKey.Table,
                    screenId,
                    TableControlObject,
                    'lines',
                    undefined,
                    {
                        pageSize: 20,
                        columns: [],
                        fetchesDefaults: true,
                    },
                ),
            };
        };

        const getColumns = () => {
            return [
                {
                    type: FieldKey.Reference,
                    properties: {
                        bind: 'product',
                        valueField: 'code',
                        helperTextField: 'description1',
                    },
                },
                {
                    type: FieldKey.Text,
                    properties: {
                        bind: 'expectedDeliveryDate',
                    },
                },
                {
                    type: FieldKey.Numeric,
                    properties: {
                        bind: 'orderedQuantity',
                    },
                },
                {
                    type: FieldKey.Checkbox,
                    properties: {
                        bind: 'isInvoiced',
                    },
                },
                {
                    type: FieldKey.Label,
                    properties: {
                        bind: 'orderCategory',
                    },
                },
            ];
        };
        const setComplexUIComponentProperties = (columns = getColumns()) => {
            pageDefinition!.metadata.uiComponentProperties = {
                _id: {},
                isSigned: {},
                orderNumber: {},
                billToCustomer: {
                    helperTextField: 'companyName',
                    valueField: 'code',
                } as ReferenceProperties<ScreenBase>,
                orderDate: {},
                revisionNumber: {},
                productImage: {},
                lines: {
                    pageSize: 20,
                    columns,
                } as InternalTableProperties,
                [screenId]: {
                    node: '@sage/xtrem-show-case/MyTestNode',
                } as any,
            };
        };

        it("should exclude transient and transient input fields from 'getNestedFieldsQuery'", () => {
            const columns = getColumns();
            (columns[0].properties as any).isTransientInput = true;
            (columns[1].properties as any).isTransient = true;
            setComplexUIComponentProperties(columns);
            const actual = getNestedFieldsQuery({ screenDefinition: pageDefinition!, nestedFields: columns as any });
            expect(actual).toEqual({
                query: {
                    __args: undefined,
                    edges: {
                        cursor: true,
                        node: {
                            _id: true,
                            isInvoiced: true,
                            orderCategory: true,
                            orderedQuantity: true,
                        },
                    },
                    pageInfo: {
                        endCursor: true,
                        hasNextPage: true,
                        hasPreviousPage: true,
                        startCursor: true,
                    },
                },
            });
        });

        it("should exclude transient and transient input fields from 'getRequestableFieldIds'", () => {
            setComplexUIComponentProperties();
            (pageDefinition!.metadata.uiComponentProperties.orderDate as any).isTransientInput = true;
            pageDefinition!.metadata.uiComponentProperties.lines.isTransient = true;
            const controlObjects = getComplexControlObjects();
            pageDefinition!.metadata.controlObjects = controlObjects as any;
            const actual = getRequestableFieldIds(controlObjects as any, pageDefinition!);
            expect(actual).toEqual([
                '_id',
                'isSigned',
                'orderNumber',
                'billToCustomer',
                'revisionNumber',
                'productImage',
            ]);
        });

        it('should not request defaults for main lists', () => {
            const controlObjects = getComplexControlObjects();
            setComplexUIComponentProperties();
            pageDefinition!.metadata.controlObjects = controlObjects as any;
            pageDefinition!.navigationPanel = {
                isOpened: true,
                value: {} as any,
                isHeaderHidden: false,
                isHidden: false,
                isRefreshing: false,
            };
            const query = buildDefaultsQuery({ screenDefinition: pageDefinition! });
            expect(query).toEqual({});
        });

        it('should not request defaults when no fetchesDefaults', () => {
            const controlObjects = getComplexControlObjects();
            setComplexUIComponentProperties();
            pageDefinition!.selectedRecordId = '1';
            pageDefinition!.metadata.controlObjects = {
                ...(controlObjects as any),
                orderDate: createFieldControlObject<FieldKey.Date>(
                    FieldKey.Date,
                    screenId,
                    DateControlObject,
                    'customerOrderRef',
                    undefined,
                    {},
                ),
            };
            delete pageDefinition!.metadata.controlObjects.lines;
            const query = buildDefaultsQuery({ screenDefinition: pageDefinition! });
            expect(query).toEqual({});
        });

        it('should build a defaults query when there is a quick entry table', () => {
            const controlObjects = getComplexControlObjects();
            setComplexUIComponentProperties();
            pageDefinition!.selectedRecordId = '1';
            pageDefinition!.metadata.controlObjects = {
                ...(controlObjects as any),
                ...{
                    orderDate: createFieldControlObject<FieldKey.Date>(
                        FieldKey.Date,
                        screenId,
                        DateControlObject,
                        'customerOrderRef',
                        undefined,
                        {},
                    ),
                    lines: createFieldControlObject<FieldKey.Table>(
                        FieldKey.Table,
                        screenId,
                        TableControlObject,
                        'lines',
                        undefined,
                        {
                            pageSize: 20,
                            columns: [],
                            canAddNewLine: true,
                        },
                    ),
                },
            };
            const query = buildDefaultsQuery({ screenDefinition: pageDefinition! });
            expect(get(query, 'xtremShowCase.myTestNode.getDefaults')).toBeTruthy();
        });

        it('should build a defaults query for an empty page', () => {
            const controlObjects = getComplexControlObjects();
            setComplexUIComponentProperties();
            pageDefinition!.metadata.controlObjects = controlObjects as any;
            pageDefinition!.selectedRecordId = '1';
            const query = buildDefaultsQuery({ screenDefinition: pageDefinition! });
            expect(query.xtremShowCase.myTestNode.getDefaults.__args.data).toEqual({ _id: '1' });
            expect(query.xtremShowCase.myTestNode.getDefaults.productImage).toEqual({ value: true });
            expect(query.xtremShowCase.myTestNode.getDefaults.orderDate).toEqual(true);
            expect(query.xtremShowCase.myTestNode.getDefaults.orderNumber).toEqual(true);
            expect(query.xtremShowCase.myTestNode.getDefaults.isSigned).toEqual(true);
            expect(query.xtremShowCase.myTestNode.getDefaults.lines).toEqual({
                query: {
                    __args: {
                        first: 20,
                        orderBy: '{"_id":-1}',
                    },
                    edges: {
                        cursor: true,
                        node: {
                            _id: true,
                            expectedDeliveryDate: true,
                            isInvoiced: true,
                            orderCategory: true,
                            orderedQuantity: true,
                            product: {
                                _id: true,
                                code: true,
                                description1: true,
                            },
                        },
                    },
                    pageInfo: {
                        endCursor: true,
                        hasNextPage: true,
                        hasPreviousPage: true,
                        startCursor: true,
                    },
                },
            });
            expect(query.xtremShowCase.myTestNode.getDefaults.billToCustomer).toEqual({
                _id: true,
                code: true,
                companyName: true,
            });
        });

        it('should build a defaults query without the dirty fields and should send their values to the server', () => {
            const controlObjects = getComplexControlObjects();
            setComplexUIComponentProperties();
            pageDefinition!.dirtyStates = {
                lines: true,
                productImage: true,
            };
            pageDefinition!.metadata.controlObjects = controlObjects as any;
            pageDefinition!.selectedRecordId = '1';
            const query = buildDefaultsQuery({ screenDefinition: pageDefinition! });
            expect(query.xtremShowCase.myTestNode.getDefaults).not.toHaveProperty('lines');
            expect(query.xtremShowCase.myTestNode.getDefaults).not.toHaveProperty('productImage');
        });

        it('should build a defaults query with the dirty fields as arguments if clean flag is false', () => {
            const parsedScreenValues = {
                _id: '2',
                product: {
                    _id: 'p1',
                    code: 'p1',
                    description1: 'description1',
                },
                expectedDeliveryDate: '2021-01-31',
                lines: {
                    pageSize: 20,
                },
                orderedQuantity: 3,
                isInvoiced: true,
                orderCategory: 'orderCategory',
            };
            jest.spyOn(transformers, 'parseScreenValues').mockReturnValue(parsedScreenValues);

            const controlObjects = getComplexControlObjects();
            setComplexUIComponentProperties();
            pageDefinition!.dirtyStates = {
                lines: true,
                productImage: true,
            };
            const expectedArgs = {
                _id: '1',
                lines: {
                    pageSize: 20,
                },
            };

            pageDefinition!.metadata.controlObjects = controlObjects as any;
            pageDefinition!.selectedRecordId = '1';
            const query = buildDefaultsQuery({
                screenDefinition: pageDefinition!,
                alias: '',
                requestedFieldIds: ['billToCustomer'],
            });
            expect(query.xtremShowCase.myTestNode.getDefaults.__args.data).toEqual(expectedArgs);
        });

        it('should build a defaults query without the dirty fields as arguments if clean flag is true', () => {
            const parsedScreenValues = {
                _id: '2',
                product: {
                    _id: 'p1',
                    code: 'p1',
                    description1: 'description1',
                },
                expectedDeliveryDate: '2021-01-31',
                lines: {
                    pageSize: 20,
                },
                orderedQuantity: 3,
                isInvoiced: true,
                orderCategory: 'orderCategory',
            };
            jest.spyOn(transformers, 'parseScreenValues').mockReturnValue(parsedScreenValues);

            const controlObjects = getComplexControlObjects();
            setComplexUIComponentProperties();
            pageDefinition!.dirtyStates = {
                lines: true,
                productImage: true,
            };
            const expectedArgs = {
                _id: '1',
            };

            pageDefinition!.metadata.controlObjects = controlObjects as any;
            pageDefinition!.selectedRecordId = '1';
            const query = buildDefaultsQuery({
                screenDefinition: pageDefinition!,
                alias: '',
                requestedFieldIds: ['billToCustomer'],
                clean: true,
            });
            expect(query.xtremShowCase.myTestNode.getDefaults.__args.data).toEqual(expectedArgs);
        });

        it('should return an empty object if there are not fields to request from the server', () => {
            const controlObjects = getComplexControlObjects();
            setComplexUIComponentProperties();
            pageDefinition!.dirtyStates = {
                lines: true,
                productImage: true,
                orderDate: true,
                orderedQuantity: true,
                product: true,
                isSigned: true,
                billToCustomer: true,
                revisionNumber: true,
                orderNumber: true,
            };
            pageDefinition!.metadata.controlObjects = controlObjects as any;
            const query = buildDefaultsQuery({ screenDefinition: pageDefinition! });
            expect(query).toEqual({});
        });

        it('should not create an request if the _id field is the only dirty one', () => {
            const controlObjects = getComplexControlObjects();
            setComplexUIComponentProperties();
            pageDefinition!.dirtyStates = {
                _id: false,
                productImage: true,
                orderDate: true,
                orderedQuantity: true,
                product: true,
                isSigned: true,
                billToCustomer: true,
                revisionNumber: true,
                orderNumber: true,
                lines: true,
            };
            pageDefinition!.metadata.controlObjects = controlObjects as any;
            const query = buildDefaultsQuery({ screenDefinition: pageDefinition!, clean: true });
            expect(query).toEqual({});
        });
    });

    describe('buildNestedDefaultsQuery', () => {
        const setComplexUIComponentProperties = () => {
            pageDefinition!.metadata.uiComponentProperties = {
                lines: {
                    pageSize: 20,
                    columns: [
                        {
                            type: FieldKey.Reference,
                            properties: {
                                bind: 'product',
                                valueField: 'code',
                                helperTextField: 'description1',
                            },
                        },
                        {
                            type: FieldKey.Text,
                            properties: {
                                bind: 'expectedDeliveryDate',
                            },
                        },
                        {
                            type: FieldKey.Numeric,
                            properties: {
                                bind: 'orderedQuantity',
                            },
                        },
                        {
                            type: FieldKey.Checkbox,
                            properties: {
                                bind: 'isInvoiced',
                            },
                        },
                        {
                            type: FieldKey.Label,
                            properties: {
                                bind: 'orderCategory',
                            },
                        },
                    ],
                    node: '@sage/xtrem-show-case/MyTestNestedNode',
                } as InternalTableProperties<Page>,
            };
        };

        it('should build a defaults query for an empty page', () => {
            setComplexUIComponentProperties();
            const query = buildNestedDefaultsQuery({
                screenDefinition: pageDefinition!,
                elementId: 'lines',
                alias: 'testAlias',
                nodeTypes: {},
            });
            expect(query.testAlias.__aliasFor).toBe('xtremShowCase');
            expect(query.testAlias.myTestNestedNode.getDefaults.__args).toBeUndefined();
            expect(query.testAlias.myTestNestedNode.getDefaults).toEqual({
                expectedDeliveryDate: true,
                isInvoiced: true,
                orderCategory: true,
                orderedQuantity: true,
                product: {
                    _id: true,
                    code: true,
                    description1: true,
                },
            });
        });

        it('should build a defaults query for an empty row with some app developer provided data', () => {
            setComplexUIComponentProperties();
            const query = buildNestedDefaultsQuery({
                screenDefinition: pageDefinition!,
                elementId: 'lines',
                alias: 'testAlias',
                data: {
                    orderedQuantity: 3,
                    isInvoiced: false,
                },
                nodeTypes: {
                    MyTestNestedNode: {
                        name: 'MyTestNestedNode',
                        title: 'My Test Nested Node',
                        packageName: '@sage/xtrem-test',
                        properties: {
                            orderedQuantity: {
                                kind: GraphQLKind.Scalar,
                                type: GraphQLTypes.Int,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            isInvoiced: {
                                kind: GraphQLKind.Scalar,
                                type: GraphQLTypes.Boolean,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            orderCategory: {
                                kind: GraphQLKind.Scalar,
                                type: GraphQLTypes.String,
                                isOnInputType: true,
                                canFilter: true,
                            },
                        },
                        mutations: {},
                    },
                },
            });
            expect(query.testAlias.__aliasFor).toBe('xtremShowCase');
            expect(query.testAlias.myTestNestedNode.getDefaults).toEqual({
                __args: { data: { isInvoiced: false, orderedQuantity: 3 } },
                expectedDeliveryDate: true,
                orderCategory: true,
                product: {
                    _id: true,
                    code: true,
                    description1: true,
                },
            });
        });

        it('should return an empty object if there are not fields to request from the server', () => {
            setComplexUIComponentProperties();
            const elementId = 'lines';

            type Node = {
                _id: string;
                product: {
                    _id: string;
                    code: string;
                    description1: string;
                };
                expectedDeliveryDate: string;
                orderedQuantity: number;
                isInvoiced: boolean;
                orderCategory: string;
            };
            pageDefinition!.dirtyStates = {
                lines: true,
            };
            const collectionValue = new CollectionValue<Node>({
                screenId,
                elementId,
                isTransient: false,
                hasNextPage: false,
                orderBy: [{}],
                columnDefinitions: [
                    [
                        nestedFields.reference<any, Node, Node['product']>({
                            bind: 'product',
                            node: '@sage/package/AnyNode',
                            valueField: 'code',
                            helperTextField: 'description1',
                        }),
                        nestedFields.text<any, Node>({ bind: 'expectedDeliveryDate' }),
                        nestedFields.numeric<any, Node>({ bind: 'orderedQuantity' }),
                        nestedFields.checkbox<any, Node>({ bind: 'isInvoiced' }),
                        nestedFields.text<any, Node>({ bind: 'orderCategory' }),
                    ],
                ],
                nodeTypes: {},
                nodes: ['@sage/xtrem-test/AnyNode'],
                filter: [undefined],
                initialValues: [
                    {
                        _id: '2',
                        product: {
                            _id: 'p1',
                            code: 'p1',
                            description1: 'description1',
                        },
                        expectedDeliveryDate: '2021-01-31',
                        orderedQuantity: 3,
                        isInvoiced: true,
                        orderCategory: 'orderCategory',
                    },
                ],
            });
            pageDefinition!.values[elementId] = collectionValue;
            collectionValue.addOrUpdateRecordValue({
                recordData: {
                    _id: '2',
                    __dirtyColumns: new Set([
                        'product__code',
                        'expectedDeliveryDate',
                        'orderedQuantity',
                        'isInvoiced',
                        'orderCategory',
                    ]),
                },
            });
            const query = buildNestedDefaultsQuery({
                screenDefinition: pageDefinition!,
                elementId: 'lines',
                isNewRow: false,
                alias: 'testAlias',
                recordId: '2',
                nodeTypes: {},
            });
            expect(query).toEqual({});
        });
    });

    describe('buildRowDefaultQuery', () => {
        const getComplexControlObjects = (): Dict<
            ReadonlyFieldControlObject<Page, FieldKey, ReadonlyFieldProperties>
        > => {
            return {
                _id: createFieldControlObject<FieldKey.Text>(
                    FieldKey.Text,
                    screenId,
                    TextControlObject,
                    '_id',
                    undefined,
                    {},
                ),
                isSigned: createFieldControlObject<FieldKey.Select>(
                    FieldKey.Select,
                    screenId,
                    SelectControlObject,
                    'isSigned',
                    undefined,
                    {},
                ),
                orderNumber: createFieldControlObject<FieldKey.Text>(
                    FieldKey.Text,
                    screenId,
                    TextControlObject,
                    'orderNumber',
                    undefined,
                    {},
                ),
                billToCustomer: createFieldControlObject<FieldKey.Reference>(
                    FieldKey.Reference,
                    screenId,
                    ReferenceControlObject,
                    'billToCustomer',
                    undefined,
                    { node: '@sage/xtrem-ui-test/BillToCustomer', valueField: 'name' },
                ) as any,
                orderDate: createFieldControlObject<FieldKey.Date>(
                    FieldKey.Date,
                    screenId,
                    DateControlObject,
                    'customerOrderRef',
                    undefined,
                    {},
                ),
                revisionNumber: createFieldControlObject<FieldKey.Numeric>(
                    FieldKey.Numeric,
                    screenId,
                    NumericControlObject,
                    'revisionNumber',
                    undefined,
                    {},
                ),
                productImage: createFieldControlObject<FieldKey.Image>(
                    FieldKey.Image,
                    screenId,
                    ImageControlObject,
                    'productImage',
                    undefined,
                    {},
                ),
                lines: createFieldControlObject<FieldKey.Table>(
                    FieldKey.Table,
                    screenId,
                    TableControlObject,
                    'lines',
                    undefined,
                    {
                        pageSize: 20,
                        columns: [],
                        fetchesDefaults: true,
                    },
                ),
            };
        };

        const setComplexUIComponentProperties = () => {
            pageDefinition!.metadata.uiComponentProperties = {
                ...pageDefinition!.metadata.uiComponentProperties,
                lines: {
                    pageSize: 20,
                    columns: [
                        {
                            type: FieldKey.Reference,
                            properties: {
                                bind: 'product',
                                valueField: 'code',
                                helperTextField: 'description1',
                            },
                        },
                        {
                            type: FieldKey.Text,
                            properties: {
                                bind: 'expectedDeliveryDate',
                            },
                        },
                        {
                            type: FieldKey.Numeric,
                            properties: {
                                bind: 'orderedQuantity',
                            },
                        },
                        {
                            type: FieldKey.Checkbox,
                            properties: {
                                bind: 'isInvoiced',
                            },
                        },
                        {
                            type: FieldKey.Label,
                            properties: {
                                bind: 'orderCategory',
                            },
                        },
                    ],
                    node: '@sage/xtrem-show-case/MyTestNestedNode',
                } as InternalTableProperties<Page>,
            };
        };

        it('should build a defaults query for an empty row with no data', () => {
            setComplexUIComponentProperties();
            const controlObjects = getComplexControlObjects();
            pageDefinition!.metadata.controlObjects = controlObjects as any;
            pageDefinition!.selectedRecordId = '1';
            jest.spyOn(transformers, 'parseScreenValues').mockReturnValue({});
            const query = buildRowDefaultQuery({
                screenDefinition: pageDefinition!,
                elementId: 'lines',
                alias: 'testAlias',
                nodeTypes: {},
            });
            const expectedQuery = {
                testAlias: {
                    getDefaults: {
                        __args: { data: { _id: '1', lines: [{}] } },
                        lines: {
                            query: {
                                edges: {
                                    node: {
                                        orderCategory: true,
                                        isInvoiced: true,
                                        orderedQuantity: true,
                                        expectedDeliveryDate: true,
                                        product: { _id: true, code: true, description1: true },
                                        _id: true,
                                    },
                                    cursor: true,
                                },
                                __args: { first: 20, orderBy: '{"_id":-1}' },
                                pageInfo: {
                                    startCursor: true,
                                    endCursor: true,
                                    hasPreviousPage: true,
                                    hasNextPage: true,
                                },
                            },
                        },
                    },
                    __aliasFor: 'undefined',
                },
            };
            expect(query.testAlias.getDefaults.__args).toEqual(expectedQuery.testAlias.getDefaults.__args);
            expect(query.testAlias.getDefaults.lines).toEqual(expectedQuery.testAlias.getDefaults.lines);
        });

        it('should get dirty nested values based on dirty columns and nested binds', async () => {
            const screenDefinition = getMockPageDefinition(screenId, { selectedRecordId: '1234' });
            const elementId = 'fieldId';
            screenDefinition.metadata.uiComponentProperties[screenId] = {
                node: '@sage/xtrem-test/AnyNode',
            } as any;
            screenDefinition.metadata.uiComponentProperties[elementId] = {
                title: 'field',
                isTransient: false,
            };
            const collectionValue = new CollectionValue({
                screenId,
                elementId,
                isTransient: false,
                hasNextPage: true,
                orderBy: [{}],
                levelMap: {},
                columnDefinitions: [
                    [
                        nestedFields.text<any, any>({ bind: '_id' }),
                        nestedFields.text<any, any>({ bind: { nested: { column: { prop: true } } } }),
                    ],
                ],
                nodeTypes: {},
                nodes: ['@sage/xtrem-test/AnyNode'],
                filter: [undefined],
                initialValues: [],
                fieldType: CollectionFieldTypes.DESKTOP_TABLE,
            });
            screenDefinition.values[elementId] = collectionValue;
            const state = getMockState();
            state.screenDefinitions[screenId] = screenDefinition;
            getMockStore(state);
            collectionValue.addRecord({
                recordData: { nested: { column: { prop: 'value' } } },
            });
            await collectionValue.setCellValue({
                recordId: '-1',
                columnId: 'nested.column.prop',
                value: 'newValue',
                shouldFetchDefault: true,
                isOrganicChange: true,
                toBeMarkedAsDirty: ['nested.column.prop'],
            });
            expect(getDirtyNestedValues({ elementId, screenDefinition, recordId: '-1' })).toEqual({
                nested: {
                    column: {
                        prop: 'newValue',
                    },
                },
            });
        });

        it('should build a defaults query for a row with data', () => {
            setComplexUIComponentProperties();
            const controlObjects = getComplexControlObjects();
            pageDefinition!.metadata.controlObjects = controlObjects as any;
            pageDefinition!.selectedRecordId = '1';
            const parsedScreenValues = {
                lines: [
                    {
                        _id: '2',
                        product: {
                            _id: 'p1',
                            code: 'p1',
                            description1: 'description1',
                        },
                        expectedDeliveryDate: '2021-01-31',
                        orderedQuantity: 3,
                        isInvoiced: true,
                        orderCategory: 'orderCategory',
                    },
                ],
            };
            jest.spyOn(transformers, 'parseScreenValues').mockReturnValue(parsedScreenValues);
            jest.spyOn(valueSerializerService, 'formatInputProperty').mockReturnValue({
                orderedQuantity: 4,
            });
            const query = buildRowDefaultQuery({
                screenDefinition: pageDefinition!,
                elementId: 'lines',
                alias: 'testAlias',
                nodeTypes: {},
            });

            const expectedQuery = {
                testAlias: {
                    getDefaults: {
                        __args: { data: { _id: '1', lines: [{ orderedQuantity: 4 }] } },
                        lines: {
                            query: {
                                edges: {
                                    node: {
                                        orderCategory: true,
                                        isInvoiced: true,
                                        orderedQuantity: true,
                                        expectedDeliveryDate: true,
                                        product: { _id: true, code: true, description1: true },
                                        _id: true,
                                    },
                                    cursor: true,
                                },
                                __args: { first: 20, orderBy: '{"_id":-1}' },
                                pageInfo: {
                                    startCursor: true,
                                    endCursor: true,
                                    hasPreviousPage: true,
                                    hasNextPage: true,
                                },
                            },
                        },
                    },
                    __aliasFor: 'undefined',
                },
            };
            expect(query.testAlias.getDefaults.__args).toEqual(expectedQuery.testAlias.getDefaults.__args);
            expect(query.testAlias.getDefaults.lines).toEqual(expectedQuery.testAlias.getDefaults.lines);
        });
    });

    describe('buildEnumQueries', () => {
        it('should generate a valid query to check enums of various types', () => {
            const result = buildEnumQueries(['CalendarLeftNode']);
            expect(queryToGraphQuery(result)).toMatchSnapshot();
        });
    });

    describe('convertFilterDecoratorToGraphQLFilter', () => {
        let pageMetadata: PageMetadata | null = null;

        beforeEach(() => {
            const pageProperties: PageProperties = {
                node: '@sage/x3-sales/SalesOrder',
                isTransient: false,
                navigationPanel: {
                    listItem: {
                        title: nestedFields.reference<any, any>({
                            bind: 'bind03',
                            valueField: 'companyName',
                            helperTextField: 'code',
                            node: '@sage/x3-sales/NavigationPanelFieldNode',
                        }),
                    },
                },
            };

            pageMetadata = {
                screenId,
                uiComponentProperties: {
                    [screenId]: pageProperties,

                    referenceField: {
                        isTransient: false,
                        node: '@sage/x3-sales/RefFieldNode',
                    } as any,
                },
            } as any;
        });

        it('should handle hardcoded filter decorator property', () => {
            const result = convertFilterDecoratorToGraphQLFilter(
                {
                    metadata: pageMetadata,
                } as any,
                { myTestFilter: 1235 },
            );

            expect(result).toEqual({ myTestFilter: 1235 });
        });

        it('should handle with function filter decorator property', () => {
            const mock = jest.fn(() => ({ myTestFilter: 1235 }));
            const result = convertFilterDecoratorToGraphQLFilter(
                {
                    metadata: pageMetadata,
                } as any,
                mock,
            );
            expect(result).toEqual({ myTestFilter: 1235 });
            expect(mock).toHaveBeenCalledWith(undefined);
        });

        it('should handle with function filter decorator property and pass on record context if provided', () => {
            const mock = jest.fn(() => ({ myTestFilter: 1235 }));
            const fakeRecordContext = { _id: '23', myField: 'John Doe' };
            const result = convertFilterDecoratorToGraphQLFilter(
                {
                    metadata: pageMetadata,
                } as any,
                mock,
                fakeRecordContext,
            );
            expect(result).toEqual({ myTestFilter: 1235 });
            expect(mock).toHaveBeenCalledWith(fakeRecordContext);
        });
    });

    describe('query node modifications from filter', () => {
        [
            {
                input: {
                    _and: [
                        { provider: { textField: 'Ali Express' } },
                        {
                            _or: [
                                {
                                    description: {
                                        _regex: 'Spi',
                                        _options: 'i',
                                    },
                                },
                                {
                                    product: {
                                        _regex: 'Spi',
                                        _options: 'i',
                                    },
                                },
                            ],
                        },
                    ],
                },
                output: {
                    provider: { textField: true, _id: true },
                    description: true,
                    product: true,
                },
            },
            {
                input: [
                    {
                        _and: [
                            {
                                provider: {
                                    cto: {
                                        name: {
                                            _regex: 'Joe',
                                            _options: 'i',
                                        },
                                    },
                                },
                            },
                            {
                                _or: [
                                    {
                                        description: {
                                            _regex: 'Spi',
                                            _options: 'i',
                                        },
                                    },
                                    {
                                        product: {
                                            _regex: 'Spi',
                                            _options: 'i',
                                        },
                                    },
                                ],
                            },
                        ],
                    },
                    {},
                ],
                output: {
                    provider: { cto: { _id: true, name: true }, _id: true },
                    description: true,
                    product: true,
                },
            },
        ].forEach(({ input, output }, index) => {
            it(`addReferenceIdsAssign works correctly for case ${index}`, () => {
                const filter = input;
                const test = addReferenceIdsAssign(filter);
                expect(test).toEqual(output);
            });
        });
    });

    [
        {
            input: [
                { lines: { _atLeast: 2, product: { qty: { _gt: 1 } } } },
                {
                    lines: { query: { totalCount: true } },
                    customer__name: {
                        name: true,
                        _id: true,
                        __aliasFor: 'customer',
                    },
                    _id: true,
                    purchaseDate: true,
                },
            ],
            output: {
                __all_Lines: { __aliasFor: 'lines', query: { totalCount: true } },
                __restricted_Lines: {
                    __aliasFor: 'lines',
                    query: {
                        totalCount: true,
                        __args: { filter: '{"product":{"qty":{"_gt":1}}}' },
                    },
                },
                customer__name: { name: true, _id: true, __aliasFor: 'customer' },
                _id: true,
                purchaseDate: true,
            },
        },
    ].forEach(({ input, output }, index) => {
        it(`addTotalCountTransform works correctly for case ${index}`, () => {
            const [filter, collection] = input;
            const test = addTotalCountTransform(filter, collection);
            expect(test).toEqual(output);
        });
    });
});
