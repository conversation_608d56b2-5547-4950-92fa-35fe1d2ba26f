import { GraphQLTypes } from '@sage/xtrem-shared';
import { GraphQLKind } from '../../types';
import * as windowUtils from '../../utils/window';
import * as graphqlUtils from '../graphql-utils';
import { fetchNodeDetails, fetchNodeNames } from '../node-information-service';

describe('node information service', () => {
    let executeGraphqlQueryMock: jest.SpyInstance<Promise<any>, any>;

    beforeEach(() => {
        executeGraphqlQueryMock = jest.spyOn(graphqlUtils, 'executeGraphqlQuery');
        jest.spyOn(windowUtils, 'isDevMode').mockReturnValue(true);
    });

    afterEach(() => {
        executeGraphqlQueryMock.mockReset();
    });

    describe('fetchNodeNames', () => {
        it('should create the correct query and sort the results', async () => {
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    getNodeNames: [
                        {
                            name: '@sage/xtrem-test/ATestNode',
                            title: 'ZZZZZZ',
                        },
                        {
                            name: '@sage/xtrem-test/AnotherTestNode',
                            title: 'AAAA',
                        },
                        {
                            name: '@sage/xtrem-test/ThirdTestNode',
                            title: 'BBBB',
                        },
                    ],
                },
            });

            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            const result = await fetchNodeNames();
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                endpoint: '/metadata',
                query: {
                    getNodeNames: {
                        __args: {
                            onlyPublishedNodes: false,
                        },
                        name: true,
                        title: true,
                    },
                },
            });
            expect(Object.keys(result)).toEqual([
                '@sage/xtrem-test/AnotherTestNode',
                '@sage/xtrem-test/ThirdTestNode',
                '@sage/xtrem-test/ATestNode',
            ]);
            expect(result).toEqual({
                '@sage/xtrem-test/ATestNode': 'ZZZZZZ',
                '@sage/xtrem-test/AnotherTestNode': 'AAAA',
                '@sage/xtrem-test/ThirdTestNode': 'BBBB',
            });
        });

        it('should throw an exception if the response is invalid', async () => {
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    getNodeNames: null,
                },
            });

            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            expect(fetchNodeNames()).rejects.toThrow('Failed to load node names from the server');
        });
    });

    describe('fetchNodeDetails', () => {
        let executeGraphqlQueryMock: jest.SpyInstance<Promise<any>, any>;

        beforeEach(() => {
            executeGraphqlQueryMock = jest
                .spyOn(graphqlUtils, 'executeGraphqlQuery')
                .mockImplementation(({ endpoint }) => {
                    if (endpoint === '/metadata') {
                        return Promise.resolve({
                            // response for the node localization query
                            data: {
                                getNodeDetailsList: [
                                    {
                                        name: 'ShowCaseProduct',
                                        properties: [
                                            {
                                                name: 'product',
                                                title: 'Product',
                                                canFilter: true,
                                                canSort: true,
                                                type: GraphQLTypes.String,
                                                enumType: null,
                                                isCustom: false,
                                                dataType: 'descriptionDataType',
                                                targetNode: '',
                                                isStored: true,
                                                isOnInputType: true,
                                                isOnOutputType: true,
                                                isMutable: false,
                                            },
                                        ],
                                        title: 'Show Case Product',
                                        defaultDataType: '',
                                        hasAttachments: false,
                                    },
                                ],
                            },
                        });
                    }
                    return Promise.resolve({
                        // response for the introspection query
                        data: {
                            ShowCaseProduct: {
                                fields: [
                                    {
                                        name: '_access',
                                        type: {
                                            enumValues: null,
                                            kind: GraphQLKind.List,
                                            name: null,
                                            ofType: { name: '_OutputAccessBinding' },
                                        },
                                    },
                                    {
                                        name: 'someListType',
                                        type: {
                                            enumValues: null,
                                            kind: GraphQLKind.List,
                                            name: null,
                                            ofType: { name: 'SomeObject' },
                                        },
                                    },
                                    {
                                        name: 'product',
                                        type: {
                                            enumValues: null,
                                            kind: GraphQLKind.Scalar,
                                            name: 'String',
                                            ofType: null,
                                        },
                                    },
                                ],
                            },
                        },
                    });
                });
            jest.spyOn(windowUtils, 'isDevMode').mockReturnValue(true);
        });

        afterEach(() => {
            executeGraphqlQueryMock.mockReset();
        });

        it('should create the correct query and sort the results', async () => {
            const result = await fetchNodeDetails({ nodeName: 'ShowCaseProduct', locale: 'en-US' });
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                cacheSettings: undefined,
                endpoint: '/metadata',
                query: {
                    getNodeDetailsList: {
                        __args: {
                            depth: 1,
                            knownNodeNames: [],
                            missingNodeNames: ['ShowCaseProduct'],
                        },
                        hasAttachments: true,
                        hasNotes: true,
                        defaultDataType: true,
                        defaultDataTypeDetails: {
                            columns: {
                                bind: true,
                                title: true,
                                type: true,
                            },
                            helperText: {
                                bind: true,
                                title: true,
                                type: true,
                            },
                            imageField: {
                                bind: true,
                                title: true,
                                type: true,
                            },
                            maxLength: true,
                            name: true,
                            node: true,
                            precision: true,
                            scale: true,
                            title: true,
                            tunnelPage: true,
                            tunnelPageId: {
                                bind: true,
                                title: true,
                                type: true,
                            },
                            type: true,
                            value: {
                                bind: true,
                                title: true,
                                type: true,
                            },
                            values: {
                                title: true,
                                value: true,
                            },
                        },
                        name: true,
                        packageName: true,
                        properties: {
                            canFilter: true,
                            canSort: true,
                            dataType: true,
                            dataTypeDetails: {
                                columns: {
                                    bind: true,
                                    title: true,
                                    type: true,
                                },
                                helperText: {
                                    bind: true,
                                    title: true,
                                    type: true,
                                },
                                imageField: {
                                    bind: true,
                                    title: true,
                                    type: true,
                                },
                                maxLength: true,
                                name: true,
                                node: true,
                                precision: true,
                                scale: true,
                                title: true,
                                tunnelPage: true,
                                tunnelPageId: {
                                    bind: true,
                                    title: true,
                                    type: true,
                                },
                                type: true,
                                value: {
                                    bind: true,
                                    title: true,
                                    type: true,
                                },
                                values: {
                                    title: true,
                                    value: true,
                                },
                            },
                            enumType: true,
                            isCustom: true,
                            isMutable: true,
                            isStored: true,
                            isOnInputType: true,
                            isOnOutputType: true,
                            name: true,
                            targetNode: true,
                            title: true,
                            type: true,
                        },
                        mutations: {
                            name: true,
                            title: true,
                            parameters: {
                                name: true,
                                title: true,
                            },
                        },
                        title: true,
                    },
                    getNodeCustomDetailsList: {
                        __args: {
                            depth: 1,
                            knownNodeNames: [],
                            missingNodeNames: ['ShowCaseProduct'],
                        },
                        name: true,

                        properties: {
                            canFilter: true,
                            canSort: true,
                            dataType: true,
                            dataTypeDetails: {
                                columns: {
                                    bind: true,
                                    title: true,
                                    type: true,
                                },
                                helperText: {
                                    bind: true,
                                    title: true,
                                    type: true,
                                },
                                imageField: {
                                    bind: true,
                                    title: true,
                                    type: true,
                                },
                                maxLength: true,
                                name: true,
                                node: true,
                                precision: true,
                                scale: true,
                                title: true,
                                tunnelPage: true,
                                tunnelPageId: {
                                    bind: true,
                                    title: true,
                                    type: true,
                                },
                                type: true,
                                value: {
                                    bind: true,
                                    title: true,
                                    type: true,
                                },
                                values: {
                                    title: true,
                                    value: true,
                                },
                            },
                            enumType: true,
                            isCustom: true,
                            isMutable: true,
                            isStored: true,
                            isOnInputType: true,
                            isOnOutputType: true,
                            name: true,
                            targetNode: true,
                            title: true,
                            type: true,
                        },
                    },
                },
            });

            expect(result).toEqual({
                product: {
                    enumValues: undefined,
                    isCollection: false,
                    kind: GraphQLKind.Scalar,
                    label: 'Product',
                    name: 'product',
                    type: GraphQLTypes.String,
                    canFilter: true,
                    canSort: true,
                    dataType: 'descriptionDataType',
                    targetNode: '',
                    enumType: null,
                    isCustom: false,
                    isStored: true,
                    isOnInputType: true,
                    isOnOutputType: true,
                    isMutable: false,
                    node: undefined,
                    title: 'Product',
                },
            });
        });
    });
});
