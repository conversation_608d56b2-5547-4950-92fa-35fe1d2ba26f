import {
    addBlockToState,
    addFieldToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../__tests__/test-helpers';

import type { BlockControlObject } from '../../component/control-objects';
import { FieldKey } from '../../component/types';
import * as redux from '../../redux';
import {
    dispatchContainerValidation,
    dispatchFieldValidation,
    runAndDispatchFieldValidation,
} from '../dispatch-service';
import { SERVER_VALIDATION_RULE_PREFIX } from '../../utils/constants';
import type { ValidationResult } from '../screen-base-definition';

describe('Dispatch service', () => {
    const screenId = 'TestPage';
    let spySetFieldValidationErrors: jest.SpyInstance<redux.AppAction, any>;
    let spyRemoveValidationError: jest.SpyInstance<redux.AppAction, any>;
    let spySetPageValidationErrors: jest.SpyInstance<redux.AppAction, any>;
    let mockState: redux.XtremAppState;
    const testFieldId1 = 'bind01';
    const testFieldId2 = 'bind02';
    const testFieldId3 = 'bind03';

    beforeEach(() => {
        spySetFieldValidationErrors = jest.spyOn(redux.actions, 'setFieldValidationErrors');
        spyRemoveValidationError = jest.spyOn(redux.actions, 'removeNonNestedErrors');
        spySetPageValidationErrors = jest.spyOn(redux.actions, 'setPageValidationErrors');

        mockState = getMockState();
        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
    });

    afterEach(() => {
        spySetFieldValidationErrors.mockReset();
        spyRemoveValidationError.mockReset();
        spySetPageValidationErrors.mockReset();
        applyActionMocks();
    });

    describe('runAndDispatchFieldValidation', () => {
        it('should dispatch a field validation if it becomes invalid', async () => {
            addFieldToState(FieldKey.Text, mockState, screenId, testFieldId1, {
                isMandatory: true,
            });
            getMockStore(mockState);

            const result = await runAndDispatchFieldValidation(screenId, testFieldId1, null);

            expect(result).toContainEqual({
                screenId,
                elementId: 'bind01',
                message: 'You need to enter a value.',
                validationRule: 'isMandatory',
            });

            expect(spySetFieldValidationErrors).toHaveBeenCalledWith('TestPage', 'bind01', [
                {
                    screenId,
                    elementId: 'bind01',
                    message: 'You need to enter a value.',
                    validationRule: 'isMandatory',
                },
            ]);
            expect(spyRemoveValidationError).not.toHaveBeenCalled();
        });

        it('should not dispatch a field validation action if it field remains valid', async () => {
            addFieldToState(FieldKey.Text, mockState, screenId, testFieldId1, {
                isMandatory: true,
            });
            getMockStore(mockState);

            const result = await runAndDispatchFieldValidation(screenId, testFieldId1, 'Test value');
            expect(result).toEqual([]);
            expect(spySetFieldValidationErrors).not.toHaveBeenCalled();
            expect(spyRemoveValidationError).not.toHaveBeenCalled();
        });

        it('should not dispatch a field validation action if it field remains invalid but still return the error', async () => {
            mockState.screenDefinitions[screenId].errors = {
                [testFieldId1]: [
                    {
                        columnId: undefined,
                        recordId: undefined,
                        elementId: testFieldId1,
                        screenId,
                        message: 'You need to enter a value.',
                        validationRule: 'isMandatory',
                    },
                ],
            };
            addFieldToState(FieldKey.Text, mockState, screenId, testFieldId1, {
                isMandatory: true,
            });
            getMockStore(mockState);

            const result = await runAndDispatchFieldValidation(screenId, testFieldId1, '');
            expect(result).toContainEqual({
                screenId,
                elementId: 'bind01',
                message: 'You need to enter a value.',
                validationRule: 'isMandatory',
            });
            expect(spySetFieldValidationErrors).not.toHaveBeenCalled();
            expect(spyRemoveValidationError).not.toHaveBeenCalled();
        });

        it('should dispatch a field validation action if it field remains invalid but still the validation error changes', async () => {
            mockState.screenDefinitions[screenId].errors = {
                [testFieldId1]: [
                    {
                        elementId: testFieldId1,
                        screenId,
                        message: 'You need to enter a value.',
                        validationRule: 'isMandatory',
                    },
                ],
            };
            addFieldToState(FieldKey.Text, mockState, screenId, testFieldId1, {
                isMandatory: true,
                maxLength: 5,
            });
            getMockStore(mockState);

            const result = await runAndDispatchFieldValidation(screenId, testFieldId1, 'too long value');
            expect(result).toContainEqual({
                screenId,
                elementId: 'bind01',
                message: 'bind01 maximum length is 5',
                validationRule: 'maxLength',
            });
            expect(spySetFieldValidationErrors).toHaveBeenCalledWith('TestPage', 'bind01', [
                {
                    screenId,
                    elementId: 'bind01',
                    message: 'bind01 maximum length is 5',
                    validationRule: 'maxLength',
                },
            ]);
            expect(spyRemoveValidationError).not.toHaveBeenCalled();
        });

        it('should dispatch a remove validation action if it field becomes valid ', async () => {
            mockState.screenDefinitions[screenId].errors = {
                [testFieldId1]: [
                    {
                        elementId: testFieldId1,
                        screenId,
                        message: 'You need to enter a value.',
                        validationRule: 'isMandatory',
                    },
                ],
            };

            addFieldToState(FieldKey.Text, mockState, screenId, testFieldId1, {
                isMandatory: true,
            });
            getMockStore(mockState);

            const result = await runAndDispatchFieldValidation(screenId, testFieldId1, 'any value');
            expect(result).toEqual([]);
            expect(spySetFieldValidationErrors).not.toHaveBeenCalled();
            expect(spyRemoveValidationError).toHaveBeenCalled();
        });
    });

    describe('dispatchContainerValidation', () => {
        const blockId = 'myBlockId';

        beforeEach(() => {
            addBlockToState(mockState, screenId, blockId);
        });

        it('should dispatch a validation for container and its fields when all fields are valid', async () => {
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId1,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    isMandatory: true,
                },
                'Hi there',
            );
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId2,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    minLength: 3,
                },
                'Hola!',
            );
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId3,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    maxLength: 32,
                },
                'Short value',
            );

            getMockStore(mockState);

            const result = await dispatchContainerValidation(screenId, blockId);
            expect(spySetPageValidationErrors).toHaveBeenCalledWith(screenId, {
                [testFieldId1]: undefined,
                [testFieldId2]: undefined,
                [testFieldId3]: undefined,
                [blockId]: undefined,
            });
            expect(spySetPageValidationErrors).toHaveBeenCalled();
            expect(result.allErrors).toEqual([]);
            expect(result.blockingErrors).toEqual([]);
        });

        it('should differentiate between blocking and non-blocking errors', async () => {
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId1,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    isMandatory: true,
                },
                'Hi there',
            );
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId2,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    minLength: 3,
                },
                'Hola!',
            );
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId3,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    maxLength: 32,
                },
                'Short value',
            );
            // Simulate server error
            const validationError: ValidationResult = {
                columnId: '',
                elementId: testFieldId1,
                level: undefined,
                message: 'Server Error',
                messagePrefix: '',
                recordId: undefined,
                screenId,
                validationRule: SERVER_VALIDATION_RULE_PREFIX,
            };
            mockState.screenDefinitions[screenId].errors[testFieldId1] = [validationError];
            getMockStore(mockState);

            const result = await dispatchContainerValidation(screenId, blockId);
            expect(spySetPageValidationErrors).toHaveBeenCalledWith(screenId, {
                [testFieldId1]: [validationError],
                [testFieldId2]: undefined,
                [testFieldId3]: undefined,
                [blockId]: undefined,
            });
            expect(spySetPageValidationErrors).toHaveBeenCalled();
            expect(result.allErrors).toEqual([validationError]);
            expect(result.blockingErrors).toEqual([]);
        });

        it('should dispatch a validation for indicated container and its fields when some fields are valid', async () => {
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId1,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    isMandatory: true,
                },
                'Hi there',
            );
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId2,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    minLength: 14,
                },
                'Hola!',
            );
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId3,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    maxLength: 32,
                },
                'Short value',
            );

            getMockStore(mockState);

            const result = await dispatchContainerValidation(screenId, blockId);
            expect(spySetPageValidationErrors).toHaveBeenCalledWith(screenId, {
                [testFieldId1]: undefined,
                [testFieldId2]: [
                    {
                        elementId: 'bind02',
                        message: 'bind02 minimum length is 14',
                        screenId: 'TestPage',
                        validationRule: 'minLength',
                    },
                ],
                [testFieldId3]: undefined,
                [blockId]: undefined,
            });
            expect(spySetPageValidationErrors).toHaveBeenCalled();
            expect(result.allErrors).toEqual([
                {
                    elementId: testFieldId2,
                    message: 'bind02 minimum length is 14',
                    screenId,
                    validationRule: 'minLength',
                },
            ]);
            expect(result.blockingErrors).toEqual([
                {
                    elementId: testFieldId2,
                    message: 'bind02 minimum length is 14',
                    screenId,
                    validationRule: 'minLength',
                },
            ]);
        });

        it('should dispatch a validation for the container when all fields are invalid', async () => {
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId1,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    isMandatory: true,
                },
                null,
            );
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId2,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    minLength: 14,
                },
                'Hola!',
            );
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId3,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    maxLength: 4,
                },
                'Short value',
            );

            getMockStore(mockState);

            const result = await dispatchContainerValidation(screenId, blockId);

            expect(spySetPageValidationErrors).toHaveBeenCalledWith(screenId, {
                [testFieldId1]: [
                    {
                        elementId: testFieldId1,
                        message: 'You need to enter a value.',
                        screenId,
                        validationRule: 'isMandatory',
                    },
                ],
                [testFieldId2]: [
                    {
                        elementId: testFieldId2,
                        message: 'bind02 minimum length is 14',
                        screenId,
                        validationRule: 'minLength',
                    },
                ],
                [testFieldId3]: [
                    {
                        elementId: testFieldId3,
                        message: 'bind03 maximum length is 4',
                        screenId,
                        validationRule: 'maxLength',
                    },
                ],
                [blockId]: undefined,
            });
            expect(spySetPageValidationErrors).toHaveBeenCalled();
            expect(result.allErrors).toEqual([
                {
                    elementId: testFieldId1,
                    message: 'You need to enter a value.',
                    screenId,
                    validationRule: 'isMandatory',
                },
                {
                    elementId: testFieldId2,
                    message: 'bind02 minimum length is 14',
                    screenId,
                    validationRule: 'minLength',
                },
                {
                    elementId: testFieldId3,
                    message: 'bind03 maximum length is 4',
                    screenId,
                    validationRule: 'maxLength',
                },
            ]);
            expect(result.blockingErrors).toEqual([
                {
                    elementId: testFieldId1,
                    message: 'You need to enter a value.',
                    screenId,
                    validationRule: 'isMandatory',
                },
                {
                    elementId: testFieldId2,
                    message: 'bind02 minimum length is 14',
                    screenId,
                    validationRule: 'minLength',
                },
                {
                    elementId: testFieldId3,
                    message: 'bind03 maximum length is 4',
                    screenId,
                    validationRule: 'maxLength',
                },
            ]);
        });

        it('should dispatch a validation for the container when all fields are valid but the container is invalid', async () => {
            addBlockToState(mockState, screenId, blockId, {
                validation: () => {
                    return 'This is going to fail!';
                },
            });

            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId1,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    isMandatory: true,
                },
                'Hi there',
            );
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId2,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    minLength: 3,
                },
                'Hola!',
            );
            addFieldToState(
                FieldKey.Text,
                mockState,
                screenId,
                testFieldId3,
                {
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects[
                            blockId
                        ] as BlockControlObject;
                    },
                    maxLength: 32,
                },
                'Short value',
            );

            getMockStore(mockState);

            const result = await dispatchContainerValidation(screenId, blockId);

            expect(spySetPageValidationErrors).toHaveBeenCalledWith(screenId, {
                [testFieldId1]: undefined,
                [testFieldId2]: undefined,
                [testFieldId3]: undefined,
                [blockId]: [
                    {
                        screenId,
                        elementId: blockId,
                        message: 'This is going to fail!',
                        validationRule: 'validation',
                    },
                ],
            });
            expect(spySetPageValidationErrors).toHaveBeenCalled();
            expect(result.allErrors).toEqual([
                {
                    screenId,
                    elementId: blockId,
                    message: 'This is going to fail!',
                    validationRule: 'validation',
                },
            ]);
            expect(result.blockingErrors).toEqual([
                {
                    screenId,
                    elementId: blockId,
                    message: 'This is going to fail!',
                    validationRule: 'validation',
                },
            ]);
        });
    });

    describe('dispatchFieldValidation', () => {
        const validField = 'validField';
        const invalidField = 'invalidField';
        beforeEach(() => {
            mockState.screenDefinitions[screenId].errors = {
                [invalidField]: [
                    {
                        elementId: invalidField,
                        screenId,
                        message: 'invalidField is mandatory',
                        validationRule: 'isMandatory',
                    },
                ],
            };
            getMockStore(mockState);
        });

        it('should dispatch validation if the a valid field becomes invalid', () => {
            expect(spySetFieldValidationErrors).not.toHaveBeenCalled();
            expect(spyRemoveValidationError).not.toHaveBeenCalled();
            const validationError = [
                {
                    screenId,
                    elementId: validField,
                    message: 'This field is invalid',
                    validationRule: 'some-rule',
                },
            ];
            dispatchFieldValidation(screenId, validField, validationError);
            expect(spyRemoveValidationError).not.toHaveBeenCalled();
            expect(spySetFieldValidationErrors).toHaveBeenCalledWith(screenId, validField, validationError);
        });

        it('should not dispatch validation if the field has the same validation error', () => {
            expect(spySetFieldValidationErrors).not.toHaveBeenCalled();
            expect(spyRemoveValidationError).not.toHaveBeenCalled();
            const validationError = [
                {
                    elementId: invalidField,
                    screenId,
                    message: 'invalidField is mandatory',
                    validationRule: 'isMandatory',
                },
            ];
            dispatchFieldValidation(screenId, invalidField, validationError);
            expect(spyRemoveValidationError).not.toHaveBeenCalled();
            expect(spySetFieldValidationErrors).not.toHaveBeenCalled();
        });

        it('should not dispatch validation if the field remains clean', () => {
            expect(spySetFieldValidationErrors).not.toHaveBeenCalled();
            expect(spyRemoveValidationError).not.toHaveBeenCalled();
            dispatchFieldValidation(screenId, validField);
            expect(spyRemoveValidationError).not.toHaveBeenCalled();
            expect(spySetFieldValidationErrors).not.toHaveBeenCalled();
        });

        it('should dispatch validation if the an invalid field becomes valid', () => {
            expect(spySetFieldValidationErrors).not.toHaveBeenCalled();
            expect(spyRemoveValidationError).not.toHaveBeenCalled();
            dispatchFieldValidation(screenId, invalidField);
            expect(spyRemoveValidationError).toHaveBeenCalledWith(screenId, invalidField);
            expect(spySetFieldValidationErrors).not.toHaveBeenCalled();
        });
    });
});
