// This block must be the first one
import {
    addBlockToState,
    addFieldToState,
    addPageControlObject,
    addSectionToState,
    createFieldControlObject,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../__tests__/test-helpers';

// DON'T REARRANGE IMPORTS

import { setLocalizeImplementation } from '@sage/xtrem-date-time';
import type { Dict } from '@sage/xtrem-shared';
import type { PageProperties } from '../../component/container/page/page-types';
import type {
    BlockControlObject,
    ReferenceProperties,
    SectionControlObject,
    SelectProperties,
    TableProperties,
} from '../../component/control-objects';
import { NumericControlObject, SelectControlObject, TextControlObject } from '../../component/control-objects';
import type { CalendarProperties } from '../../component/field/calendar/calendar-types';
import type { NestedField, NestedFieldTypes } from '../../component/nested-fields';
import * as nestedFields from '../../component/nested-fields';
import { FieldKey } from '../../component/types';
import type * as xtremRedux from '../../redux';
import type { ApplicationContext } from '../../redux/state';
import { GraphQLKind, GraphQLTypes } from '../../types';
import { NEW_PAGE } from '../../utils/constants';
import { CollectionValue } from '../collection-data-service';
import { CollectionFieldTypes } from '../collection-data-types';
import * as graphQLQueryBuilder from '../graphql-query-builder';
import { buildSearchBoxFilter, buildSearchBoxFilterForNestedFields } from '../graphql-query-builder';
import * as graphqlService from '../graphql-service';
import type { GraphQLFilter } from '../graphql-utils';
import * as graphqlUtils from '../graphql-utils';
import type { PageArticleItem } from '../layout-types';
import { NodeCacheService } from '../node-cache-service';
import type { FormattedNodeDetails } from '../metadata-types';
import { createNavigationTableProperties } from '../navigation-panel-service';
import type { Page } from '../page';
import type { PageDefinition } from '../page-definition';
import type { ScreenBase } from '../screen-base';
import * as storageService from '../storage-service';

setLocalizeImplementation((key: string, _template: string) => {
    return _template;
});

const elementId = 'myField';

type SalesOrder = {
    _id: string;
    code: string;
    bind01: string;
    bind02: string;
    orderCity: string;
    aDateField: Date;
    aNumberField: number;
    aReferenceField: SalesOrderLine;
    companyName: string;
    name: string;
    codePropertyThatIsNotOnInputType: string;
    companyNameThatIsNotOnInputType: string;
};

type SalesOrderLine = {
    _id: string;
    aStringField: string;
    aNumericField: number;
};

const nodeTypes: Dict<FormattedNodeDetails> = {
    SalesOrder: {
        name: 'SalesOrder',
        title: 'Sales Order',
        packageName: '@sage/xtrem-test',
        properties: {
            bind01: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true, isOnInputType: true },
            bind02: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true, isOnInputType: true },
            bind03: {
                kind: GraphQLKind.Object,
                type: 'Company',
                isMutable: true,
                isOnInputType: true,
                isOnOutputType: true,
            },
            orderCity: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true, isOnInputType: true },
            aDateField: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar, canFilter: true, isOnInputType: true },
            aNumberField: {
                type: GraphQLTypes.Decimal,
                kind: GraphQLKind.Scalar,
                canFilter: true,
                isOnInputType: true,
            },
            aReferenceField: { type: 'SalesOrderLine', kind: GraphQLKind.Object, canFilter: true, isOnInputType: true },
        },
        mutations: {},
    },
    SalesOrderLine: {
        name: 'SalesOrderLine',
        title: 'SalesOrderLine',
        packageName: '@sage/xtrem-test',
        properties: {
            aStringField: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true, isOnInputType: true },
            aNumericField: {
                type: GraphQLTypes.Decimal,
                kind: GraphQLKind.Scalar,
                canFilter: true,
                isOnInputType: true,
            },
        },
        mutations: {},
    },
    AnyNode: {
        name: 'AnyNode',
        title: 'AnyNode',
        packageName: '@sage/xtrem-test',
        properties: {
            _id: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true, isOnInputType: true },
            [elementId]: {
                type: GraphQLTypes.IntReference,
                kind: GraphQLKind.Scalar,
                canFilter: true,
                isOnInputType: true,
            },
            field1: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true, isOnInputType: true },
            field3: { type: GraphQLTypes.Boolean, kind: GraphQLKind.Scalar, canFilter: true, isOnInputType: true },
            field4: { type: GraphQLTypes.IntReference, kind: GraphQLKind.Scalar, canFilter: true, isOnInputType: true },
            field5: { type: 'SomeOtherObject', kind: GraphQLKind.Object, canFilter: true },
        },
        mutations: {},
    },
    Company: {
        name: 'Company',
        title: 'Company',
        packageName: '@sage/xtrem-test',
        properties: {
            companyName: {
                kind: GraphQLKind.Scalar,
                type: GraphQLTypes.String,
                isOnInputType: true,
                isOnOutputType: true,
            },
            code: {
                kind: GraphQLKind.Scalar,
                type: GraphQLTypes.String,
                isOnInputType: true,
                isOnOutputType: true,
            },
        },
        mutations: {},
    },
};

describe('graphql-service', () => {
    const screenId = 'TestPage';
    let pageDefinition: PageDefinition | null = null;
    let mockState: xtremRedux.XtremAppState;
    let executeGraphqlQueryMock: jest.SpyInstance<Promise<any>, any>;

    beforeEach(() => {
        delete (window as any).location;
        (global as any).window.location = {
            port: '80',
            protocol: 'https:',
            hostname: 'localhost',
        };
        mockState = getMockState({ nodeTypes });
        mockState.applicationContext = {
            cacheEncryptionKey: 'TEST_ENCRYPTION_KEY',
            locale: 'en-US',
        } as ApplicationContext;
        getMockStore(mockState);
        pageDefinition = getMockPageDefinition(screenId, {}, { uiComponentProperties: { [screenId]: {} } });
        executeGraphqlQueryMock = jest.spyOn(graphqlUtils, 'executeGraphqlQuery');
    });

    afterEach(() => {
        executeGraphqlQueryMock.mockReset();
        NodeCacheService.flushCache();
    });

    describe('fetchTableData', () => {
        it('should create the correct graphql query and execute the call', async () => {
            const executeGraphqlQueryMockImpl = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        testNode: {
                            query: {
                                edges: [
                                    {
                                        node: {
                                            _id: 'testId',
                                            lines: {
                                                query: {
                                                    edges: [
                                                        {
                                                            node: {
                                                                _id: 'id1',
                                                                item: 'test',
                                                                quantity: 1,
                                                            },
                                                            cursor: 'testCursor~1~1',
                                                        },
                                                    ],
                                                },
                                            },
                                        },
                                        cursor: 'testCursor~1',
                                        pageInfo: {
                                            hasNextPage: false,
                                        },
                                    },
                                ],
                            },
                        },
                    },
                };
            });
            const expectedReturn = {
                data: [{ __cursor: 'testCursor~1~1', _id: 'id1', item: 'test', quantity: 1 }],
                pageInfo: {},
            };
            executeGraphqlQueryMock.mockImplementation(executeGraphqlQueryMockImpl);
            const nestedFieldsDefinitions: nestedFields.NestedField<ScreenBase, nestedFields.NestedFieldTypes>[] = [
                {
                    defaultUiProperties: { ...TextControlObject.defaultUiProperties, bind: 'item' },
                    properties: { bind: 'item', title: 'Item' },
                    type: FieldKey.Text,
                },
                {
                    defaultUiProperties: { ...NumericControlObject.defaultUiProperties, bind: 'quantity' },
                    properties: { bind: 'quantity', title: 'Quantity', scale: 0 },
                    type: FieldKey.Numeric,
                },
            ];
            const result = await graphqlService.fetchCollectionData({
                screenDefinition: pageDefinition!,
                rootNode: 'testNode',
                rootNodeId: 'testId',
                elementId: 'lines',
                nestedFields: nestedFieldsDefinitions,
                queryArguments: { filter: JSON.stringify({ item: { _eq: 'test' } }) },
            });
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedReturn);
        });
    });

    describe('fetchNavigationPanelData', () => {
        beforeEach(() => {
            jest.spyOn(storageService, 'get').mockImplementation(key => key);
        });

        it('should generate a correct filter with mandatory values and query to graphql', async () => {
            (
                pageDefinition!.metadata.uiComponentProperties[
                    pageDefinition!.metadata.screenId
                ] as PageProperties<Page>
            ).navigationPanel = {
                listItem: {
                    title: nestedFields.text<any, any>({ bind: 'bind01' }),
                },
            };
            const executeGraphqlQueryMockImpl = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        x3Sales: {
                            salesOrder: {
                                query: {
                                    edges: [
                                        {
                                            node: {
                                                _id: '01',
                                                bind01: 'testFilterValue',
                                            },
                                            cursor: 'cursorValue',
                                        },
                                    ],
                                    pageInfo: {
                                        hasNextPage: false,
                                    },
                                },
                            },
                        },
                    },
                };
            });
            const expectedReturn = [{ _id: '01', bind01: 'testFilterValue', __cursor: 'cursorValue' }];
            executeGraphqlQueryMock.mockImplementation(executeGraphqlQueryMockImpl);
            const tableProperties = await createNavigationTableProperties(
                screenId,
                '@sage/x3-sales/SalesOrder',
                pageDefinition!,
                nodeTypes,
                {},
            );
            const result = await graphqlService.fetchNavigationPanelData(
                screenId,
                pageDefinition!,
                tableProperties,
                tableProperties.orderBy,
                tableProperties.filter,
                undefined,
                nodeTypes,
            );
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedReturn);
        });

        it('should generate a correct filter with mandatory values + non mandatory and query to graphql', async () => {
            (
                pageDefinition!.metadata.uiComponentProperties[
                    pageDefinition!.metadata.screenId
                ] as PageProperties<Page>
            ).navigationPanel = {
                listItem: {
                    title: nestedFields.text<any, any>({ bind: 'bind01' }),
                    titleRight: nestedFields.text<any, any>({ bind: 'bind02' }),
                },
            };
            const executeGraphqlQueryMockImpl = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        x3Sales: {
                            salesOrder: {
                                query: {
                                    edges: [
                                        {
                                            node: {
                                                _id: '01',
                                                bind01: 'testFilterValue',
                                                bind02: 'testFilterValue2',
                                            },
                                            cursor: 'cursorValue',
                                        },
                                    ],
                                    pageInfo: {
                                        hasNextPage: false,
                                    },
                                },
                            },
                        },
                    },
                };
            });
            const expectedReturn = [
                { _id: '01', bind01: 'testFilterValue', bind02: 'testFilterValue2', __cursor: 'cursorValue' },
            ];
            executeGraphqlQueryMock.mockImplementation(executeGraphqlQueryMockImpl);
            const tableProperties = await createNavigationTableProperties(
                screenId,
                '@sage/x3-sales/SalesOrder',
                pageDefinition!,
                nodeTypes,
                {},
            );
            const result = await graphqlService.fetchNavigationPanelData(
                screenId,
                pageDefinition!,
                tableProperties,
                tableProperties.orderBy,
                tableProperties.filter,
                undefined,
                nodeTypes,
            );
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedReturn);
        });

        it('should generate a correct filter with mandatory values + non mandatory + nested filter and query to graphql', async () => {
            (
                pageDefinition!.metadata.uiComponentProperties[
                    pageDefinition!.metadata.screenId
                ] as PageProperties<Page>
            ).navigationPanel = {
                listItem: {
                    title: nestedFields.text<any, any>({ bind: 'bind01' }),
                    titleRight: nestedFields.text<any, any>({ bind: 'bind02' }),
                },
            };
            const tableProperties = await createNavigationTableProperties(
                screenId,
                '@sage/x3-sales/SalesOrder',
                pageDefinition!,
                nodeTypes,
                {},
            );
            tableProperties.filter = { orderCity: { _regex: 'Paris', _options: 'i' } };
            const executeGraphqlQueryMockImpl = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        x3Sales: {
                            salesOrder: {
                                query: {
                                    edges: [
                                        {
                                            node: {
                                                _id: '01',
                                                bind01: 'testFilterValue',
                                                bind02: 'testFilterValue2',
                                                orderCity: 'Paris',
                                            },
                                            cursor: 'cursorValue',
                                        },
                                    ],
                                    pageInfo: {
                                        hasNextPage: false,
                                    },
                                },
                            },
                        },
                    },
                };
            });
            const expectedReturn = [
                {
                    _id: '01',
                    bind01: 'testFilterValue',
                    bind02: 'testFilterValue2',
                    orderCity: 'Paris',
                    __cursor: 'cursorValue',
                },
            ];
            executeGraphqlQueryMock.mockImplementation(executeGraphqlQueryMockImpl);
            const result = await graphqlService.fetchNavigationPanelData(
                screenId,
                pageDefinition!,
                tableProperties,
                tableProperties.orderBy,
                tableProperties.filter,
                undefined,
                nodeTypes,
            );
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedReturn);
        });

        it('should generate a correct filter with empty and query to graphql and return the data', async () => {
            (
                pageDefinition!.metadata.uiComponentProperties[
                    pageDefinition!.metadata.screenId
                ] as PageProperties<Page>
            ).navigationPanel = {
                listItem: {
                    title: nestedFields.text<any, any>({ bind: 'bind01' }),
                    titleRight: nestedFields.text<any, any>({ bind: 'bind02' }),
                },
            };
            const tableProperties = await createNavigationTableProperties(
                screenId,
                '@sage/x3-sales/SalesOrder',
                pageDefinition!,
                nodeTypes,
                {},
            );

            const executeGraphqlQueryMockImpl = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        x3Sales: {
                            salesOrder: {
                                query: {
                                    edges: [
                                        {
                                            node: {
                                                _id: '01',
                                                bind01: 'testFilterValue',
                                                bind02: 'testFilterValue2',
                                                orderCity: 'Paris',
                                            },
                                            cursor: 'cursorValue',
                                        },
                                    ],
                                    pageInfo: {
                                        hasNextPage: false,
                                    },
                                },
                            },
                        },
                    },
                };
            });
            const expectedReturn = [
                {
                    _id: '01',
                    bind01: 'testFilterValue',
                    bind02: 'testFilterValue2',
                    orderCity: 'Paris',
                    __cursor: 'cursorValue',
                },
            ];
            executeGraphqlQueryMock.mockImplementation(executeGraphqlQueryMockImpl);
            const result = await graphqlService.fetchNavigationPanelData(
                screenId,
                pageDefinition!,
                tableProperties,
                tableProperties.orderBy,
                tableProperties.filter,
                undefined,
                nodeTypes,
            );
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedReturn);
        });

        it('should generate a correct filter with empty filter Value + dropdown filters and query to graphql', async () => {
            (
                pageDefinition!.metadata.uiComponentProperties[
                    pageDefinition!.metadata.screenId
                ] as PageProperties<Page>
            ).navigationPanel = {
                listItem: {
                    title: nestedFields.text<any, any>({ bind: 'bind01' }),
                    titleRight: nestedFields.text<any, any>({ bind: 'bind02' }),
                },
            };
            const tableProperties = await createNavigationTableProperties(
                screenId,
                '@sage/x3-sales/SalesOrder',
                pageDefinition!,
                nodeTypes,
                {},
            );

            tableProperties.filter = { orderCity: { _regex: 'Paris', _options: 'i' } };
            const executeGraphqlQueryMockImpl = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        x3Sales: {
                            salesOrder: {
                                query: {
                                    edges: [
                                        {
                                            node: {
                                                _id: '01',
                                                bind01: 'testFilterValue',
                                                bind02: 'testFilterValue2',
                                                orderCity: 'Paris',
                                            },
                                            cursor: 'cursorValue',
                                        },
                                    ],
                                    pageInfo: {
                                        hasNextPage: false,
                                    },
                                },
                            },
                        },
                    },
                };
            });
            const expectedReturn = [
                {
                    _id: '01',
                    bind01: 'testFilterValue',
                    bind02: 'testFilterValue2',
                    orderCity: 'Paris',
                    __cursor: 'cursorValue',
                },
            ];
            executeGraphqlQueryMock.mockImplementation(executeGraphqlQueryMockImpl);
            const result = await graphqlService.fetchNavigationPanelData(
                screenId,
                pageDefinition!,
                tableProperties,
                tableProperties.orderBy,
                tableProperties.filter,
                undefined,
                nodeTypes,
            );
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedReturn);
        });

        it('should generate a correct filter with reference field and query to graphql', async () => {
            (
                pageDefinition!.metadata.uiComponentProperties[
                    pageDefinition!.metadata.screenId
                ] as PageProperties<Page>
            ).navigationPanel = {
                listItem: {
                    title: nestedFields.text<any, any>({ bind: 'bind01' }),
                    titleRight: nestedFields.reference<any, any, any>({
                        bind: 'bind02',
                        valueField: 'subBind02',
                        helperTextField: 'code',
                        node: '@sage/x3-any/Company',
                    }),
                },
            };

            const tableProperties = await createNavigationTableProperties(
                screenId,
                '@sage/x3-sales/SalesOrder',
                pageDefinition!,
                nodeTypes,
                {},
            );

            const executeGraphqlQueryMockImpl = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        x3Sales: {
                            salesOrder: {
                                query: {
                                    edges: [
                                        {
                                            node: {
                                                _id: '01',
                                                bind01: 'testFilterValue',
                                                bind02: {
                                                    subBind02: 'potato',
                                                },
                                                orderCity: 'Paris',
                                            },
                                            cursor: 'cursorValue',
                                        },
                                    ],
                                    pageInfo: {
                                        hasNextPage: false,
                                    },
                                },
                            },
                        },
                    },
                };
            });
            const expectedReturn = [
                {
                    _id: '01',
                    bind01: 'testFilterValue',
                    bind02: { subBind02: 'potato' },
                    orderCity: 'Paris',
                    __cursor: 'cursorValue',
                },
            ];
            executeGraphqlQueryMock.mockImplementation(executeGraphqlQueryMockImpl);
            const result = await graphqlService.fetchNavigationPanelData(
                screenId,
                pageDefinition!,
                tableProperties,
                tableProperties.orderBy,
                tableProperties.filter,
                undefined,
                nodeTypes,
            );
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedReturn);
        });
    });

    describe('fetchReferenceFieldSuggestions', () => {
        let executeGraphqlQueryMockImpl: jest.Mock;
        const fieldId = 'code';

        beforeEach(() => {
            const state = getMockState();
            state.nodeTypes = {
                Code: {
                    name: 'Code',
                    title: 'Code',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        _id: {
                            type: GraphQLTypes.String,
                            kind: GraphQLKind.Scalar,
                            canFilter: true,
                            isOnInputType: true,
                        },
                        companyName: {
                            type: GraphQLTypes.String,
                            kind: GraphQLKind.Scalar,
                            canFilter: true,
                            isOnInputType: true,
                        },
                        code: {
                            type: GraphQLTypes.String,
                            kind: GraphQLKind.Scalar,
                            canFilter: true,
                            isOnInputType: true,
                        },
                    },
                    mutations: {},
                },
                User: {
                    name: 'User',
                    title: 'User',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        _id: {
                            type: GraphQLTypes.String,
                            kind: GraphQLKind.Scalar,
                            canFilter: true,
                            isOnInputType: true,
                        },
                        firstName: {
                            type: GraphQLTypes.String,
                            kind: GraphQLKind.Scalar,
                            canFilter: true,
                            isOnInputType: true,
                        },
                        lastName: {
                            type: GraphQLTypes.String,
                            kind: GraphQLKind.Scalar,
                            canFilter: true,
                            isOnInputType: true,
                        },
                    },
                    mutations: {},
                },
                Customer: {
                    name: 'Customer',
                    title: 'Customer',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        _id: {
                            kind: GraphQLKind.Scalar,
                            type: GraphQLTypes.IntOrString,
                            canFilter: true,
                            isOnInputType: true,
                        },
                        companyName: {
                            kind: GraphQLKind.Scalar,
                            type: GraphQLTypes.String,
                            canFilter: true,
                            isOnInputType: true,
                        },
                        name: {
                            kind: GraphQLKind.Scalar,
                            type: GraphQLTypes.String,
                            canFilter: true,
                            isOnInputType: true,
                        },
                        code: {
                            kind: GraphQLKind.Scalar,
                            type: GraphQLTypes.String,
                            canFilter: true,
                            isOnInputType: true,
                        },
                        aReferenceField: {
                            kind: GraphQLKind.Object,
                            type: 'CustomerReference',
                            isOnInputType: true,
                            canFilter: true,
                        },
                    },
                    mutations: {},
                },
                CustomerReference: {
                    name: 'CustomerReference',
                    title: 'CustomerReference',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        _id: {
                            kind: GraphQLKind.Scalar,
                            type: GraphQLTypes.IntOrString,
                            canFilter: true,
                            isOnInputType: true,
                        },
                        aStringField: {
                            kind: GraphQLKind.Scalar,
                            type: GraphQLTypes.String,
                            canFilter: true,
                            isOnInputType: true,
                        },
                    },
                    mutations: {},
                },
            };
            state.screenDefinitions[screenId] = getMockPageDefinition(
                screenId,
                {},
                {
                    uiComponentProperties: {
                        [screenId]: {
                            node: '@sage/xtrem-test/Invoice',
                        } as PageProperties<any, any>,
                    },
                },
            );
            getMockStore(state);
            executeGraphqlQueryMockImpl = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        xtremTest: {
                            invoice: {
                                lookups: {
                                    [fieldId]: {
                                        edges: [
                                            {
                                                node: {
                                                    _id: 'Customer:FR001',
                                                    companyName: 'Urban Cycle',
                                                    code: 'FR001',
                                                },
                                                cursor: 'FR001',
                                            },
                                        ],
                                    },
                                },
                            },
                        },
                    },
                };
            });
            executeGraphqlQueryMock.mockImplementation(executeGraphqlQueryMockImpl);
        });

        afterEach(() => {
            jest.resetAllMocks();
        });

        it('should generate a valid graphQL query for a reference field and do the request', async () => {
            const filterValue = 'FR001';
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                valueField: 'code',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
            };
            const expectedResult = [
                expect.objectContaining({ _id: 'Customer:FR001', code: 'FR001', companyName: 'Urban Cycle' }),
            ];
            const result = await graphqlService.fetchReferenceFieldSuggestions<SalesOrder>({
                fieldProperties,
                screenId,
                fieldId,
                filterValue,
            });
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedResult);
        });

        it('should generate a valid graphQL query for a reference field without helperTextField', async () => {
            const filterValue = 'FR001';
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                valueField: 'code',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
            };
            const expectedResult = [
                expect.objectContaining({ _id: 'Customer:FR001', code: 'FR001', companyName: 'Urban Cycle' }),
            ];

            const result = await graphqlService.fetchReferenceFieldSuggestions<SalesOrder>({
                fieldProperties,
                screenId,
                fieldId,
                filterValue,
            });
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedResult);
        });

        it('should generate a valid graphQL query for a reference field with true exact match option', async () => {
            const newFieldId = 'code';
            const filterValue = 'FR001';
            const fieldProperties = {
                valueField: 'code',
                node: 'customer',
                title: 'Choose a Customer',
                isMandatory: true,
            } as any;

            await graphqlService.fetchReferenceFieldSuggestions({
                fieldProperties,
                screenId,
                fieldId: newFieldId,
                filterValue,
                exactMatch: true,
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    xtremTest: {
                        invoice: {
                            lookups: {
                                __args: {
                                    data: {},
                                },
                                code: {
                                    __args: {
                                        filter: '{"_or":[{"code":{"_regex":"FR001"}}]}',
                                        orderBy: '{"code":1,"_id":1}',
                                    },
                                    edges: { cursor: true, node: { _id: true, code: true } },
                                    pageInfo: {
                                        endCursor: true,
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should generate a valid graphQL query for a reference field with false exact match option', async () => {
            const newFieldId = 'code';
            const filterValue = 'FR001';
            const fieldProperties = {
                valueField: 'code',
                node: 'customer',
                title: 'Choose a Customer',
                isMandatory: true,
            } as any;

            await graphqlService.fetchReferenceFieldSuggestions({
                fieldProperties,
                screenId,
                fieldId: newFieldId,
                filterValue,
                exactMatch: false,
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    xtremTest: {
                        invoice: {
                            lookups: {
                                __args: {
                                    data: {},
                                },
                                code: {
                                    __args: {
                                        filter: '{"_or":[{"code":{"_regex":"FR001","_options":"i"}}]}',
                                        orderBy: '{"code":1,"_id":1}',
                                    },
                                    edges: { cursor: true, node: { _id: true, code: true } },
                                    pageInfo: {
                                        endCursor: true,
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should generate a valid graphQL query for a reference field with escaped character filter value', async () => {
            const newFieldId = 'code';
            const filterValue = 'FR001\\';
            const fieldProperties = {
                valueField: 'code',
                node: 'customer',
                title: 'Choose a Customer',
                isMandatory: true,
            } as any;

            await graphqlService.fetchReferenceFieldSuggestions({
                fieldProperties,
                screenId,
                fieldId: newFieldId,
                filterValue,
                exactMatch: true,
            });

            expect(executeGraphqlQueryMock).toHaveBeenCalled();
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    xtremTest: {
                        invoice: {
                            lookups: {
                                __args: {
                                    data: {},
                                },
                                code: {
                                    __args: {
                                        filter: '{"_or":[{"code":{"_regex":"FR001\\\\\\\\"}}]}',
                                        orderBy: '{"code":1,"_id":1}',
                                    },
                                    edges: { cursor: true, node: { _id: true, code: true } },
                                    pageInfo: {
                                        endCursor: true,
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should generate a valid graphQL query for a reference field a reference callback option and record context', async () => {
            const newFieldId = 'code';
            const filterValue = 'FR001';
            const recordContext = { _id: '1232', firstName: 'John', lastName: 'Doe' };
            const filterMock = jest.fn(recordContextArg => ({ testProperty: recordContextArg.firstName }));
            const fieldProperties = {
                valueField: 'code',
                node: 'customer',
                title: 'Choose a Customer',
                isMandatory: true,
                filter: filterMock,
            } as any;

            await graphqlService.fetchReferenceFieldSuggestions({
                fieldProperties,
                screenId,
                fieldId: newFieldId,
                filterValue,
                exactMatch: false,
                recordContext,
                contextNode: '@sage/xtrem-test/User',
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    xtremTest: {
                        invoice: {
                            lookups: {
                                __args: {
                                    data: {
                                        _id: '1232',
                                        firstName: 'John',
                                        lastName: 'Doe',
                                    },
                                },
                                code: {
                                    __args: {
                                        filter: '{"_and":[{"testProperty":"John"},{"_or":[{"code":{"_regex":"FR001","_options":"i"}}]}]}',
                                        orderBy: '{"code":1,"_id":1}',
                                    },
                                    edges: { cursor: true, node: { _id: true, code: true, testProperty: true } },
                                    pageInfo: {
                                        endCursor: true,
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should generate a valid graphQL query for a reference field without valueField', async () => {
            const filterValue = 'Urban';
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
                valueField: 'name',
            };
            const expectedResult = [
                expect.objectContaining({ _id: 'Customer:FR001', code: 'FR001', companyName: 'Urban Cycle' }),
            ];

            const result = await graphqlService.fetchReferenceFieldSuggestions<SalesOrder>({
                fieldProperties,
                screenId,
                fieldId,
                filterValue,
            });
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedResult);
        });

        it('should generate a valid graphQL query and return the cursor in the response', async () => {
            const filterValue = 'FR001';
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                valueField: 'code',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
            };
            const expectedResult = [
                expect.objectContaining({
                    _id: 'Customer:FR001',
                    code: 'FR001',
                    companyName: 'Urban Cycle',
                    __cursor: 'FR001',
                }),
            ];
            const result = await graphqlService.fetchReferenceFieldSuggestions<SalesOrder>({
                fieldProperties,
                screenId,
                fieldId,
                filterValue,
            });
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedResult);
        });

        it('should generate a valid graphQL query with a custom filter and do the request', async () => {
            const filterValue = 'FR001';
            const customFilter = { code: { _regex: '.*1$' } };
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                valueField: 'code',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
                filter: customFilter,
            };
            const expectedResult = [
                expect.objectContaining({ _id: 'Customer:FR001', code: 'FR001', companyName: 'Urban Cycle' }),
            ];
            const result = await graphqlService.fetchReferenceFieldSuggestions<SalesOrder>({
                fieldProperties,
                screenId,
                fieldId,
                filterValue,
            });
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedResult);
        });

        it('should generate a valid graphQL query with a custom function filter and do the request', async () => {
            const filterValue = 'FR001';
            const customFilter = () => {
                return { code: { _regex: '.*1$' } };
            };

            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                valueField: 'code',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
                filter: customFilter,
            };

            const expectedResult = [
                expect.objectContaining({ _id: 'Customer:FR001', code: 'FR001', companyName: 'Urban Cycle' }),
            ];

            const result = await graphqlService.fetchReferenceFieldSuggestions<SalesOrder>({
                fieldProperties,
                screenId,
                fieldId,
                filterValue,
            });
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalledTimes(1);
            expect(result).toEqual(expectedResult);
        });

        it('should generate a valid graphQL query without filterValue for a reference field and do the request', async () => {
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                valueField: 'code',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
            };
            const expectedResult = [
                expect.objectContaining({ _id: 'Customer:FR001', code: 'FR001', companyName: 'Urban Cycle' }),
            ];

            const result = await graphqlService.fetchReferenceFieldSuggestions<SalesOrder>({
                fieldProperties,
                screenId,
                fieldId,
            });
            expect(executeGraphqlQueryMockImpl).toHaveBeenCalled();
            expect(result).toEqual(expectedResult);
        });
    });

    describe('buildSearchBoxFilter ', () => {
        const nodeTypes: Dict<FormattedNodeDetails> = {
            Customer: {
                name: 'Customer',
                title: 'Customer',
                packageName: '@sage/xtrem-test',
                properties: {
                    _id: { kind: GraphQLKind.Scalar, type: GraphQLTypes.IntOrString, canFilter: true },
                    companyName: { kind: GraphQLKind.Scalar, type: GraphQLTypes.String, canFilter: true },
                    code: { kind: GraphQLKind.Scalar, type: GraphQLTypes.String, canFilter: true },
                    aReferenceField: { kind: 'OBJECT', type: 'CustomerReference' },
                },
                mutations: {},
            },
            CustomerReference: {
                name: 'CustomerReference',
                title: 'CustomerReference',
                packageName: '@sage/xtrem-test',
                properties: {
                    _id: { kind: GraphQLKind.Scalar, type: GraphQLTypes.IntOrString, canFilter: true },
                    aStringField: { kind: GraphQLKind.Scalar, type: GraphQLTypes.String, canFilter: true },
                },
                mutations: {},
            },
        };

        it('should return undefined when the search text is empty', () => {
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                valueField: 'code',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
            };
            expect(
                buildSearchBoxFilter<SalesOrder>(
                    fieldProperties,
                    nodeTypes,
                    'en-US',
                    CollectionFieldTypes.LOOKUP_DIALOG,
                    '',
                ),
            ).toBeUndefined();
        });

        it('should return filter object', () => {
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                valueField: 'code',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
            };
            expect(
                buildSearchBoxFilter<SalesOrder>(
                    fieldProperties,
                    nodeTypes,
                    'en-US',
                    CollectionFieldTypes.LOOKUP_DIALOG,
                    'Search expression',
                ),
            ).toEqual({
                _or: [
                    { companyName: { _options: 'i', _regex: 'Search expression' } },
                    { code: { _options: 'i', _regex: 'Search expression' } },
                ],
            });
        });

        it('should return filter object for nested bind on value field', () => {
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                valueField: { aReferenceField: { aStringField: true } },
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
            };
            expect(
                buildSearchBoxFilter<SalesOrder>(
                    fieldProperties,
                    nodeTypes,
                    'en-US',
                    CollectionFieldTypes.LOOKUP_DIALOG,
                    'Search expression',
                ),
            ).toEqual({
                _or: [
                    {
                        companyName: { _options: 'i', _regex: 'Search expression' },
                    },
                    {
                        aReferenceField: {
                            aStringField: {
                                _options: 'i',
                                _regex: 'Search expression',
                            },
                        },
                    },
                ],
            });
        });

        it('should not filter on integer field objects if the supplied search text is not a number', () => {
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                valueField: '_id',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
            };
            expect(
                buildSearchBoxFilter<SalesOrder>(
                    fieldProperties,
                    nodeTypes,
                    'en-US',
                    CollectionFieldTypes.LOOKUP_DIALOG,
                    'Search expression',
                ),
            ).toEqual({
                _or: [
                    {
                        companyName: {
                            _options: 'i',
                            _regex: 'Search expression',
                        },
                    },
                ],
            });
        });

        it('should handle when value field is not on the input node type', () => {
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                valueField: 'codePropertyThatIsNotOnInputType',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
            };
            expect(
                buildSearchBoxFilter<SalesOrder>(
                    fieldProperties,
                    nodeTypes,
                    'en-US',
                    CollectionFieldTypes.LOOKUP_DIALOG,
                    'Search expression',
                ),
            ).toEqual({
                _or: [{ companyName: { _options: 'i', _regex: 'Search expression' } }],
            });
        });

        it('should handle when helper text field is not on the input node type', () => {
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyNameThatIsNotOnInputType',
                valueField: 'code',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
            };
            expect(
                buildSearchBoxFilter<SalesOrder>(
                    fieldProperties,
                    nodeTypes,
                    'en-US',
                    CollectionFieldTypes.LOOKUP_DIALOG,
                    'Search expression',
                ),
            ).toEqual({
                _or: [{ code: { _options: 'i', _regex: 'Search expression' } }],
            });
        });

        it('should filter on integer field objects if the supplied search text is a number', () => {
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                valueField: '_id',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
            };
            expect(
                buildSearchBoxFilter<SalesOrder>(
                    fieldProperties,
                    nodeTypes,
                    'en-US',
                    CollectionFieldTypes.LOOKUP_DIALOG,
                    '12345',
                ),
            ).toEqual({
                _or: [
                    {
                        companyName: { _options: 'i', _regex: '12345' },
                    },
                    {
                        _id: {
                            _options: 'i',
                            _regex: '12345',
                        },
                    },
                ],
            });
        });

        it('should filter on text field objects if the supplied search text is a text', () => {
            const fieldProperties: Omit<ReferenceProperties<ScreenBase, SalesOrder>, 'parent'> = {
                helperTextField: 'companyName',
                valueField: '_id',
                node: '@sage/xtrem-test/Customer',
                title: 'Choose a Customer',
                isMandatory: true,
            };
            expect(
                buildSearchBoxFilter<SalesOrder>(
                    fieldProperties,
                    nodeTypes,
                    'en-US',
                    CollectionFieldTypes.LOOKUP_DIALOG,
                    'asdf',
                ),
            ).toEqual({
                _or: [
                    {
                        companyName: { _options: 'i', _regex: 'asdf' },
                    },
                ],
            });
        });
    });

    describe('fetchRecordData', () => {
        it('should generate a valid graphQL query and do the request ', async () => {
            const id = '123456';
            const nodeName = 'testObject';

            pageDefinition!.metadata.controlObjects = {
                isSigned: createFieldControlObject<FieldKey.Select>(
                    FieldKey.Select,
                    screenId,
                    SelectControlObject,
                    'isSigned',
                    undefined,
                    {},
                ),
                orderNumber: createFieldControlObject<FieldKey.Text>(
                    FieldKey.Text,
                    screenId,
                    TextControlObject,
                    'orderNumber',
                    undefined,
                    {},
                ),
            };

            (pageDefinition!.metadata.uiComponentProperties as Dict<PageProperties<Page>>) = {
                isSigned: {},
                orderNumber: {},
                [screenId]: {
                    node: nodeName,
                } as PageProperties<Page>,
            };

            // eslint-disable-next-line @typescript-eslint/no-shadow
            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        [nodeName]: {
                            read: {
                                _id: 123456,
                                currency: {
                                    iso: 'EUR',
                                    code: 'EUR',
                                },
                                isSigned: 'no',
                                lines: {
                                    query: {
                                        edges: [
                                            {
                                                node: {
                                                    _id: 56789,
                                                    product: {
                                                        code: 'DIS001',
                                                        description11: 'Standard keyboard',
                                                    },
                                                    orderedQuantity: 1,
                                                },
                                            },
                                            {
                                                node: {
                                                    _id: 56780,
                                                    product: {
                                                        code: 'DIS003',
                                                        description11: 'Standard mouse',
                                                    },
                                                    orderedQuantity: 1,
                                                },
                                            },
                                        ],
                                    },
                                },
                                orderNumber: 'QTEFR0110041',
                            },
                        },
                    },
                };
            });
            jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);
            const expectedReturn = {
                _id: 123456,
                currency: {
                    code: 'EUR',
                    iso: 'EUR',
                },
                isSigned: 'no',
                lines: {
                    data: [
                        {
                            _id: 56789,
                            orderedQuantity: 1,
                            product: {
                                code: 'DIS001',
                                description11: 'Standard keyboard',
                            },
                        },
                        {
                            _id: 56780,
                            orderedQuantity: 1,
                            product: {
                                code: 'DIS003',
                                description11: 'Standard mouse',
                            },
                        },
                    ],
                    pageInfo: {},
                },
                orderNumber: 'QTEFR0110041',
            };
            const result = await graphqlService.fetchRecordData({
                screenDefinition: pageDefinition!,
                screenId,
                recordId: id,
                nodeTypes,
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
            expect(result).toEqual(expectedReturn);
        });
    });

    describe('fetchInitialData', () => {
        beforeEach(() => {
            jest.spyOn(storageService, 'get').mockImplementation(key => key);
            jest.spyOn(graphQLQueryBuilder, 'parseFilterEnumFields').mockImplementation(
                filter => filter as GraphQLFilter,
            );
        });

        it('should not call the server if the query is empty', async () => {
            const pageProperties: PageProperties<Page> = {
                isTransient: true,
            };

            pageDefinition!.metadata = {
                ...pageDefinition!.metadata,
                screenId,
            };

            (pageDefinition!.metadata.uiComponentProperties as Dict<PageProperties<Page>>) = {
                [screenId]: pageProperties,
            };

            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(() =>
                Promise.resolve({
                    data: {
                        SalesOrder: { key: 'stuff' },
                    },
                }),
            );
            jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);
            await graphqlService.fetchInitialData({
                pageDefinition: pageDefinition!,
                nodeTypes,
                dataTypes: {},
                serviceOptions: {},
            });
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
        });

        it('should generate a valid graphQL Filter and do the request ', async () => {
            const pageProperties: PageProperties<Page> = {
                node: '@sage/x3-sales/SalesOrder',
                isTransient: false,
            };

            pageDefinition!.metadata = {
                ...pageDefinition!.metadata,
                screenId,
                controlObjects: {
                    bind01: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind01',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                    bind02: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind02',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                },
            };

            (pageDefinition!.metadata.uiComponentProperties as Dict<PageProperties<Page>>) = {
                [screenId]: pageProperties,
                bind01: {
                    isTransient: false,
                },
                bind02: {
                    isTransient: false,
                },
            };

            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        rootNode: {
                            salesOrder: {
                                read: {
                                    _id: '01',
                                    bind01: 'testFilterValue',
                                    bind02: 'testFilterValue2',
                                },
                            },
                        },
                    },
                };
            });
            jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);
            const expected: graphqlService.InitialData = {
                navigationPanel: [],
                values: { _id: '01', bind01: 'testFilterValue', bind02: 'testFilterValue2' },
            };
            const result = await graphqlService.fetchInitialData({
                pageDefinition: pageDefinition!,
                recordId: '123456',
                nodeTypes,
                serviceOptions: {},
            });
            expect(result).toEqual(expected);
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
        });

        it('should generate a query with getDefaults and mainlist request if the record ID is NEW_PAGE', async () => {
            const pageProperties: PageProperties<Page> = {
                node: '@sage/x3-sales/SalesOrder',
                isTransient: false,
                navigationPanel: {
                    listItem: {
                        title: nestedFields.reference<any, any, any>({
                            bind: 'bind03',
                            valueField: 'companyName',
                            helperTextField: 'code',
                            node: '@sage/x3-any/Company',
                        }),
                    },
                },
            };

            pageDefinition!.metadata = {
                ...pageDefinition!.metadata,
                screenId,
                controlObjects: {
                    bind01: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind01',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(() => ({
                            fetchesDefaults: true,
                        })),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                    bind02: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind02',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                },
            };

            (pageDefinition!.metadata.uiComponentProperties as Dict<PageProperties<Page>>) = {
                [screenId]: pageProperties,
                bind01: {
                    isTransient: false,
                },
                bind02: {
                    isTransient: false,
                },
            };

            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(async () => {
                return {
                    data: {
                        rootNode: {
                            salesOrder: {
                                read: {
                                    _id: '01',
                                    bind01: 'testFilterValue',
                                    bind02: 'testFilterValue2',
                                },
                            },
                        },
                    },
                };
            });
            const spy = jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);

            await graphqlService.fetchInitialData({
                pageDefinition: pageDefinition!,
                recordId: NEW_PAGE,
                nodeTypes,
                serviceOptions: {},
            });
            expect(spy).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: expect.objectContaining({
                    rootNode: {
                        __aliasFor: 'x3Sales',
                        salesOrder: {
                            getDefaults: {
                                __args: {
                                    data: {
                                        _id: null,
                                    },
                                },
                                bind01: true,
                                bind02: true,
                            },
                        },
                    },
                }),
            });
        });

        it('should generate a query without getDefaults and mainlist request if the record ID is unset', async () => {
            const pageProperties: PageProperties<Page> = {
                node: '@sage/x3-sales/SalesOrder',
                isTransient: false,
                navigationPanel: {
                    listItem: {
                        title: nestedFields.reference<any, any, any>({
                            bind: 'bind03',
                            valueField: 'companyName',
                            helperTextField: 'code',
                            node: '@sage/x3-any/Company',
                        }),
                    },
                },
            };

            pageDefinition!.metadata = {
                ...pageDefinition!.metadata,
                screenId,
                controlObjects: {
                    bind01: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind01',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(() => ({
                            fetchesDefaults: true,
                        })),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                    bind02: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind02',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                },
            };

            (pageDefinition!.metadata.uiComponentProperties as Dict<PageProperties<Page>>) = {
                [screenId]: pageProperties,
                bind01: {
                    isTransient: false,
                },
                bind02: {
                    isTransient: false,
                },
            };

            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(async () => {
                return {
                    data: {
                        rootNode: {
                            salesOrder: {
                                read: {
                                    _id: '01',
                                    bind01: 'testFilterValue',
                                    bind02: 'testFilterValue2',
                                },
                            },
                        },
                    },
                };
            });
            const spy = jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);
            await graphqlService.fetchInitialData({
                pageDefinition: pageDefinition!,
                nodeTypes,
                serviceOptions: {},
            });
            expect(spy).not.toHaveBeenCalledWith({
                query: expect.objectContaining({
                    rootNode: {
                        __aliasFor: 'x3Sales',
                        salesOrder: {
                            getDefaults: {
                                __args: {
                                    data: {
                                        _id: null,
                                    },
                                },
                                bind01: true,
                                bind02: true,
                            },
                        },
                    },
                }),
            });
        });

        it('should generate a valid graphQL Filter with navigationPanelItems and do the request, returns the result + mutate pageMetadata', async () => {
            const pageProperties: PageProperties<Page> = {
                node: '@sage/x3-sales/SalesOrder',
                isTransient: false,
                navigationPanel: {
                    listItem: {
                        title: nestedFields.reference<any, any, any>({
                            bind: 'bind03',
                            valueField: 'companyName',
                            helperTextField: 'code',
                            node: '@sage/x3-any/Company',
                        }),
                    },
                },
            };

            pageDefinition!.metadata = {
                ...pageDefinition!.metadata,
                screenId,
                controlObjects: {
                    bind01: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind01',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                    bind02: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind02',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                },
            };

            (pageDefinition!.metadata.uiComponentProperties as Dict<PageProperties<Page> | SelectProperties>) = {
                [screenId]: pageProperties,
                bind01: {
                    isTransient: false,
                },
                bind02: {
                    isTransient: false,
                },
                bind03: {
                    isTransient: false,
                    optionType: 'OrderCityEnum',
                } as SelectProperties,
            };

            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        rootNode: {
                            salesOrder: {
                                read: {
                                    _id: '01',
                                    bind01: 'testFilterValue',
                                    bind02: 'testFilterValue2',
                                },
                            },
                        },
                        navigationPanelItems: {
                            salesOrder: {
                                query: {
                                    edges: [
                                        {
                                            node: {
                                                bind03: {
                                                    companyName: 'company1',
                                                    code: '010',
                                                },
                                            },
                                        },
                                    ],
                                    pageInfo: {
                                        hasNextPage: false,
                                    },
                                },
                            },
                        },
                        OrderCityEnum: {
                            enumValues: [{ name: 'BCN' }, { name: 'Madrid' }, { name: 'Valencia' }, { name: 'Paris' }],
                        },
                    },
                };
            });
            jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);
            const expected: graphqlService.InitialData = {
                navigationPanel: [
                    {
                        bind03: {
                            code: '010',
                            companyName: 'company1',
                        },
                    },
                ],
                values: {
                    _id: '01',
                    bind01: 'testFilterValue',
                    bind02: 'testFilterValue2',
                },
            };

            const result = await graphqlService.fetchInitialData({
                pageDefinition: pageDefinition!,
                recordId: '123456',
                nodeTypes,
                serviceOptions: {},
                dataTypes: {},
            });
            expect(result).toEqual(expected);
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
        });

        it('should generate a valid graphQL Filter with navigationPanelItems & DropDownMenu and do the request, returns the result + mutate pageMetadata', async () => {
            const pageProperties: PageProperties<Page> = {
                node: '@sage/x3-sales/SalesOrder',
                isTransient: false,
                navigationPanel: {
                    listItem: {
                        title: nestedFields.reference<any, any, any>({
                            bind: 'bind03',
                            valueField: 'companyName',
                            helperTextField: 'code',
                            node: '@sage/x3-any/Company',
                        }),
                    },
                    optionsMenu: [
                        {
                            title: 'Item1',
                            graphQLFilter: { bind03: { _regex: 'testFilterValue2', _options: 'i' } },
                        },
                    ],
                },
            };

            pageDefinition!.metadata = {
                ...pageDefinition!.metadata,
                screenId,
                controlObjects: {
                    bind01: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind01',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                    bind02: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind02',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                },
            };

            (pageDefinition!.metadata.uiComponentProperties as Dict<PageProperties<Page> | SelectProperties>) = {
                [screenId]: pageProperties,
                bind01: {
                    isTransient: false,
                },
                bind02: {
                    isTransient: false,
                },
                bind03: {
                    isTransient: false,
                    optionType: 'OrderCityEnum',
                } as SelectProperties,
            };

            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        rootNode: {
                            salesOrder: {
                                read: {
                                    _id: '01',
                                    bind01: 'testFilterValue',
                                    bind02: 'testFilterValue2',
                                },
                            },
                        },
                        navigationPanelItems: {
                            salesOrder: {
                                query: {
                                    edges: [
                                        {
                                            node: {
                                                bind03: {
                                                    companyName: 'company1',
                                                    code: '010',
                                                },
                                            },
                                        },
                                    ],
                                    pageInfo: {
                                        hasNextPage: false,
                                    },
                                },
                            },
                        },
                    },
                };
            });
            jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);
            const expected: graphqlService.InitialData = {
                navigationPanel: [
                    {
                        bind03: {
                            code: '010',
                            companyName: 'company1',
                        },
                    },
                ],
                values: {
                    _id: '01',
                    bind01: 'testFilterValue',
                    bind02: 'testFilterValue2',
                },
            };
            const result = await graphqlService.fetchInitialData({
                pageDefinition: pageDefinition!,
                recordId: '123456',
                nodeTypes,
                serviceOptions: {},
                dataTypes: {},
            });
            expect(result).toEqual(expected);
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
        });

        it('should generate a valid graphQL Filter with navigationPanelItems & DropDownMenu with GraphQLFilter function and do the request, returns the result + mutate pageMetadata', async () => {
            const pageProperties: PageProperties<Page> = {
                node: '@sage/x3-sales/SalesOrder',
                isTransient: false,
                navigationPanel: {
                    listItem: {
                        title: nestedFields.reference<any, any, any>({
                            bind: 'bind03',
                            valueField: 'companyName',
                            helperTextField: 'code',
                            node: '@sage/x3-any/Company',
                        }),
                    },
                    optionsMenu: [
                        {
                            title: 'Item1',
                            graphQLFilter: (storage, queryParameters) => ({
                                _or: [
                                    { bind01: { _regex: String(storage.get('testGetStorage')), _options: 'i' } },
                                    {
                                        bind02: {
                                            _regex: String(queryParameters.testQueryParameters),
                                            _options: 'i',
                                        },
                                    },
                                ],
                            }),
                        },
                    ],
                },
            };

            pageDefinition!.metadata = {
                ...pageDefinition!.metadata,
                screenId,
                controlObjects: {
                    bind01: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind01',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                    bind02: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind02',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                },
            };

            (pageDefinition!.metadata.uiComponentProperties as Dict<PageProperties<Page> | SelectProperties>) = {
                [screenId]: pageProperties,
                bind01: {
                    isTransient: false,
                },
                bind02: {
                    isTransient: false,
                },
                bind03: {
                    isTransient: false,
                    optionType: 'OrderCityEnum',
                } as SelectProperties,
            };

            pageDefinition!.queryParameters = {
                testQueryParameters: 'testQueryParameters',
            };

            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        rootNode: {
                            salesOrder: {
                                read: {
                                    _id: '01',
                                    bind01: 'testFilterValue',
                                    bind02: 'testFilterValue2',
                                },
                            },
                        },
                        navigationPanelItems: {
                            salesOrder: {
                                query: {
                                    edges: [
                                        {
                                            node: {
                                                bind03: {
                                                    companyName: 'company1',
                                                    code: '010',
                                                },
                                            },
                                        },
                                    ],
                                    pageInfo: {
                                        hasNextPage: false,
                                    },
                                },
                            },
                        },
                        OrderCityEnum: {
                            enumValues: [{ name: 'BCN' }, { name: 'Madrid' }, { name: 'Valencia' }, { name: 'Paris' }],
                        },
                    },
                };
            });
            jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);
            const expected: graphqlService.InitialData = {
                navigationPanel: [
                    {
                        bind03: {
                            code: '010',
                            companyName: 'company1',
                        },
                    },
                ],
                values: {
                    _id: '01',
                    bind01: 'testFilterValue',
                    bind02: 'testFilterValue2',
                },
            };
            const result = await graphqlService.fetchInitialData({
                pageDefinition: pageDefinition!,
                recordId: '123456',
                nodeTypes,
                serviceOptions: {},
                dataTypes: {},
            });
            expect(result).toEqual(expected);
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
        });

        it('should generate a valid graphQL Filter with navigationPanelItems & DropDownMenu without GraphQLFilter and do the request, returns the result + mutate pageMetadata', async () => {
            const pageProperties: PageProperties<Page> = {
                node: '@sage/x3-sales/SalesOrder',
                isTransient: false,
                navigationPanel: {
                    listItem: {
                        title: nestedFields.reference<any, any, any>({
                            bind: 'bind03',
                            valueField: 'companyName',
                            node: '@sage/x3-any/Company',
                            helperTextField: 'code',
                        }),
                    },
                    optionsMenu: [
                        {
                            title: 'Item1',
                            page: 'dropdownPage',
                        },
                    ],
                },
            };

            pageDefinition!.metadata = {
                ...pageDefinition!.metadata,
                screenId,
                controlObjects: {
                    bind01: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind01',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                    bind02: new TextControlObject({
                        componentKey: FieldKey.Text,
                        elementId: 'bind02',
                        dispatchValidation: jest.fn(),
                        getValue: jest.fn(),
                        getUiComponentProperties: jest.fn(),
                        layout: {} as PageArticleItem,
                        screenId,
                        setUiComponentProperties: jest.fn(),
                        setValue: jest.fn(),
                        refresh: jest.fn(),
                        focus: jest.fn(),
                        isFieldDirty: jest.fn(),
                        setFieldDirty: jest.fn(),
                        setFieldClean: jest.fn(),
                        isFieldInFocus: jest.fn(),
                    }),
                },
            };

            (pageDefinition!.metadata.uiComponentProperties as Dict<PageProperties<Page> | SelectProperties>) = {
                [screenId]: pageProperties,
                bind01: {
                    isTransient: false,
                },
                bind02: {
                    isTransient: false,
                },
                bind03: {
                    isTransient: false,
                    optionType: 'OrderCityEnum',
                } as SelectProperties,
            };
            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {
                        rootNode: {
                            salesOrder: {
                                read: {
                                    _id: '01',
                                    bind01: 'testFilterValue',
                                    bind02: 'testFilterValue2',
                                },
                            },
                        },
                        navigationPanelItems: {
                            salesOrder: {
                                query: {
                                    edges: [
                                        {
                                            node: {
                                                bind03: {
                                                    companyName: 'company1',
                                                    code: '010',
                                                },
                                            },
                                        },
                                    ],
                                    pageInfo: {
                                        hasNextPage: false,
                                    },
                                },
                            },
                        },
                    },
                };
            });
            jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);
            const expected: graphqlService.InitialData = {
                navigationPanel: [
                    {
                        bind03: {
                            code: '010',
                            companyName: 'company1',
                        },
                    },
                ],
                values: {
                    _id: '01',
                    bind01: 'testFilterValue',
                    bind02: 'testFilterValue2',
                },
            };
            const result = await graphqlService.fetchInitialData({
                pageDefinition: pageDefinition!,
                recordId: '123456',
                nodeTypes,
                serviceOptions: {},
                dataTypes: {},
            });
            expect(result).toEqual(expected);
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
        });

        it('should return empty data & navigationPanelItems because the no data is returned from the server', async () => {
            const pageProperties: PageProperties<Page> = {
                node: '@sage/x3-sales/SalesOrder',
                isTransient: false,
            };

            pageDefinition!.metadata = {
                ...pageDefinition!.metadata,
                screenId,
            };

            (pageDefinition!.metadata.uiComponentProperties as Dict<
                PageProperties<Page> | SelectProperties | TableProperties<Page> | CalendarProperties<Page>
            >) = {
                [screenId]: pageProperties,
                select: {
                    isTransient: false,
                    optionType: 'OrderCityEnum',
                } as SelectProperties,
                table: {
                    columns: [
                        nestedFields.select({
                            bind: 'orderFoodEnum',
                            isTransient: false,
                            optionType: 'OrderFoodEnum',
                        }),
                    ],
                } as TableProperties<Page>,
                calendar: {
                    eventCard: {
                        title: nestedFields.select({
                            bind: 'orderDrinkEnum',
                            isTransient: false,
                            optionType: 'OrderDrinkEnum',
                        }),
                    },
                } as CalendarProperties<Page>,
            };
            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {},
                };
            });
            jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);
            const expected: graphqlService.InitialData = {
                navigationPanel: [],
                values: {},
            };
            const result = await graphqlService.fetchInitialData({
                pageDefinition: pageDefinition!,
                recordId: '123456',
                nodeTypes,
                serviceOptions: {},
            });
            expect(result).toEqual(expected);
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
        });

        it('should fetch the page data from the server if values are provided as an arg', async () => {
            const pageProperties: PageProperties<Page> = {
                node: '@sage/x3-sales/SalesOrder',
                isTransient: false,
            };

            pageDefinition!.metadata = {
                ...pageDefinition!.metadata,
                screenId,
            };

            (pageDefinition!.metadata.uiComponentProperties as Dict<
                PageProperties<Page> | SelectProperties | TableProperties<Page> | CalendarProperties<Page>
            >) = {
                [screenId]: pageProperties,
                select: {
                    isTransient: false,
                    optionType: 'OrderCityEnum',
                } as SelectProperties,
            };
            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {},
                };
            });

            jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);
            const expected: graphqlService.InitialData = {
                navigationPanel: [],
                values: { someValue: 'value' },
            };

            const result = await graphqlService.fetchInitialData({
                pageDefinition: pageDefinition!,
                recordId: '123456',
                nodeTypes,
                serviceOptions: {},
                values: { someValue: 'value' },
            });
            expect(result).toEqual(expected);
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
        });

        it('should only fetch fields in sections that are explicitly requested and orphan fields if the requestedSections option is set', async () => {
            const pageProperties: PageProperties<Page> = {
                node: '@sage/x3-sales/SalesOrder',
                isTransient: false,
                mode: 'tabs',
            };
            const pageDefinition = getMockPageDefinition(screenId);
            mockState.screenDefinitions[screenId] = pageDefinition;
            addPageControlObject(mockState, screenId, pageProperties);

            const field1 = addFieldToState(FieldKey.Text, mockState, screenId, 'field1', {
                parent() {
                    return mockState.screenDefinitions[screenId].metadata.controlObjects
                        .block1 as BlockControlObject<any>;
                },
            });

            const field2 = addFieldToState(FieldKey.Text, mockState, screenId, 'field2', {
                parent() {
                    return mockState.screenDefinitions[screenId].metadata.controlObjects
                        .block2 as BlockControlObject<any>;
                },
            });

            addFieldToState(FieldKey.Text, mockState, screenId, 'field3', {});

            const block1 = addBlockToState(
                mockState,
                screenId,
                'block1',
                {
                    title: 'Block 1',
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects
                            .testSection1 as SectionControlObject<any>;
                    },
                },
                [field1],
            );

            addSectionToState(
                mockState,
                screenId,
                'testSection1',
                {
                    title: 'My Section',
                },
                [block1],
            );

            const block2 = addBlockToState(
                mockState,
                screenId,
                'block2',
                {
                    title: 'Block 2',
                    parent() {
                        return mockState.screenDefinitions[screenId].metadata.controlObjects
                            .testSection2 as SectionControlObject<any>;
                    },
                },
                [field2],
            );

            addSectionToState(
                mockState,
                screenId,
                'testSection2',
                {
                    title: 'My Other Section',
                },
                [block2],
            );

            // eslint-disable-next-line @typescript-eslint/no-shadow
            const executeGraphqlQueryMock = jest.fn(async query => {
                expect(query).toMatchSnapshot();
                return {
                    data: {},
                };
            });

            jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockImplementation(executeGraphqlQueryMock);
            const expected: graphqlService.InitialData = {
                navigationPanel: [],
                values: { someValue: 'value' },
            };

            const result = await graphqlService.fetchInitialData({
                pageDefinition: pageDefinition!,
                recordId: '123456',
                nodeTypes,
                serviceOptions: {},
                values: { someValue: 'value' },
                requiredSections: ['testSection1'],
            });
            expect(result).toEqual(expected);
            expect(executeGraphqlQueryMock).toHaveBeenCalled();
        });
    });

    describe('fetchReferenceFieldData', () => {
        // eslint-disable-next-line @typescript-eslint/no-shadow
        let executeGraphqlQueryMock: jest.SpyInstance<
            Promise<any>,
            [query: graphqlUtils.ExecutableQuery, endpoint?: string]
        >;

        beforeEach(() => {
            executeGraphqlQueryMock = jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockResolvedValue({
                data: {
                    anyPackage: {
                        anyNode: {
                            lookups: {
                                [elementId]: {
                                    edges: {
                                        node: {},
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        afterEach(() => {
            executeGraphqlQueryMock.mockReset();
        });

        it('should fetch reference field items without lookup if the field is transient', async () => {
            pageDefinition = getMockPageDefinition(
                screenId,
                {},
                {
                    uiComponentProperties: {
                        [screenId]: {
                            node: '@sage/any-package/AnyNode',
                        } as PageProperties<any, any>,
                    },
                },
            );
            mockState = getMockState({
                nodeTypes,
                screenDefinitions: {
                    [screenId]: pageDefinition!,
                },
            });
            getMockStore(mockState);

            await graphqlService.fetchReferenceFieldData({
                fieldProperties: {
                    node: '@sage/any-package/AnyNode',
                    valueField: 'anyField' as any,
                    isTransient: true,
                },
                screenId,
                elementId: 'myField',
                filter: { netPrice: { _gt: 12 } } as any,
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    anyPackage: {
                        anyNode: {
                            query: {
                                __args: {
                                    filter: '{"netPrice":{"_gt":12}}',
                                    orderBy: '{"_id":1}',
                                },
                                edges: {
                                    cursor: true,
                                    node: {
                                        _id: true,
                                        anyField: true,
                                        netPrice: true,
                                    },
                                },
                                pageInfo: {
                                    endCursor: true,
                                    hasNextPage: true,
                                    hasPreviousPage: true,
                                    startCursor: true,
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should fetch reference field items without lookup if the page is transient', async () => {
            pageDefinition = getMockPageDefinition(
                screenId,
                {},
                {
                    uiComponentProperties: {
                        [screenId]: {
                            node: '@sage/AnyNode',
                            isTransient: true,
                        } as PageProperties<any, any>,
                    },
                },
            );
            mockState = getMockState({
                nodeTypes,
                screenDefinitions: {
                    [screenId]: pageDefinition!,
                },
            });
            getMockStore(mockState);

            await graphqlService.fetchReferenceFieldData({
                fieldProperties: {
                    node: '@sage/any-package/AnyNode',
                    valueField: 'anyField' as any,
                },
                screenId,
                elementId,
                filter: { netPrice: { _gt: 12 } } as any,
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    anyPackage: {
                        anyNode: {
                            query: {
                                __args: {
                                    filter: '{"netPrice":{"_gt":12}}',
                                    orderBy: '{"_id":1}',
                                },
                                edges: {
                                    cursor: true,
                                    node: {
                                        _id: true,
                                        anyField: true,
                                        netPrice: true,
                                    },
                                },
                                pageInfo: {
                                    endCursor: true,
                                    hasNextPage: true,
                                    hasPreviousPage: true,
                                    startCursor: true,
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should fetch reference field items without lookup if the page does not have a node property', async () => {
            pageDefinition = getMockPageDefinition(
                screenId,
                {},
                {
                    uiComponentProperties: {
                        [screenId]: {} as PageProperties<any, any>,
                    },
                },
            );
            mockState = getMockState({
                nodeTypes,
                screenDefinitions: {
                    [screenId]: pageDefinition!,
                },
            });
            getMockStore(mockState);

            await graphqlService.fetchReferenceFieldData({
                fieldProperties: {
                    node: '@sage/any-package/AnyNode',
                    valueField: 'anyField' as any,
                },
                screenId,
                elementId,
                filter: { netPrice: { _gt: 12 } } as any,
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    anyPackage: {
                        anyNode: {
                            query: {
                                __args: {
                                    filter: '{"netPrice":{"_gt":12}}',
                                    orderBy: '{"_id":1}',
                                },
                                edges: {
                                    cursor: true,
                                    node: {
                                        _id: true,
                                        anyField: true,
                                        netPrice: true,
                                    },
                                },
                                pageInfo: {
                                    endCursor: true,
                                    hasNextPage: true,
                                    hasPreviousPage: true,
                                    startCursor: true,
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should fetch reference field items with lookup without context', async () => {
            pageDefinition = getMockPageDefinition(
                screenId,
                {
                    values: {
                        [elementId]: { anyField: 'asd', _id: '12' },
                    },
                },
                {
                    uiComponentProperties: {
                        [screenId]: {
                            node: '@sage/any-package/AnyNode',
                        } as PageProperties<any, any>,
                        [elementId]: {
                            _controlObjectType: FieldKey.Reference,
                            node: '@sage/any-package/AnyNode',
                            valueField: 'anyField',
                        } as any,
                    },
                },
            );
            mockState = getMockState({
                nodeTypes,
                screenDefinitions: {
                    [screenId]: pageDefinition!,
                },
            });
            getMockStore(mockState);

            await graphqlService.fetchReferenceFieldData({
                fieldProperties: {
                    node: '@sage/any-package/AnyNode',
                    valueField: 'anyField' as any,
                },
                screenId,
                elementId,
                filter: { netPrice: { _gt: 12 } } as any,
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    anyPackage: {
                        anyNode: {
                            lookups: {
                                __args: {
                                    data: {
                                        [elementId]: '_id:12',
                                    },
                                },
                                [elementId]: {
                                    __args: {
                                        filter: '{"netPrice":{"_gt":12}}',
                                        orderBy: '{"_id":1}',
                                    },
                                    edges: {
                                        cursor: true,
                                        node: {
                                            _id: true,
                                            anyField: true,
                                            netPrice: true,
                                        },
                                    },
                                    pageInfo: {
                                        endCursor: true,
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should fetch reference field items with lookup with supplied context', async () => {
            pageDefinition = getMockPageDefinition(
                screenId,
                {
                    values: {
                        [elementId]: { anyField: 'asd', _id: '12' },
                        myParentElementId: new CollectionValue({
                            screenId,
                            elementId: 'myParentElementId',
                            isTransient: false,
                            hasNextPage: false,
                            orderBy: [{}],
                            columnDefinitions: [[]],
                            nodeTypes: {},
                            nodes: ['@sage/any-package/AnyNode'],
                            filter: [{}],
                            initialValues: [],
                            fieldType: CollectionFieldTypes.LOOKUP_DIALOG,
                        }),
                    },
                },
                {
                    uiComponentProperties: {
                        [screenId]: {
                            node: '@sage/any-package/AnyNode',
                        } as PageProperties<any, any>,
                        [elementId]: {
                            node: '@sage/any-package/AnyNode',
                            valueField: 'anyField',
                        } as any,
                    },
                },
            );
            mockState = getMockState({
                nodeTypes,
                screenDefinitions: {
                    [screenId]: pageDefinition!,
                },
            });
            getMockStore(mockState);

            await graphqlService.fetchReferenceFieldData({
                fieldProperties: {
                    node: '@sage/any-package/AnyNode',
                    valueField: 'anyField' as any,
                },
                screenId,
                elementId,
                filter: { netPrice: { _gt: 12 } } as any,
                parentElementId: 'myParentElementId',
                contextNode: '@sage/any-package/AnyNode',
                recordContext: {
                    _id: '234',
                    field1: 'hello',
                    field3: true,
                },
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    anyPackage: {
                        anyNode: {
                            lookups: {
                                __args: {
                                    data: {
                                        _id: '234',
                                        field1: 'hello',
                                        field3: true,
                                    },
                                },
                                [elementId]: {
                                    __args: {
                                        filter: '{"netPrice":{"_gt":12}}',
                                        orderBy: '{"_id":1}',
                                    },
                                    edges: {
                                        cursor: true,
                                        node: {
                                            _id: true,
                                            anyField: true,
                                            netPrice: true,
                                        },
                                    },
                                    pageInfo: {
                                        endCursor: true,
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should serialize the reference field lookup data argument based on the page node if the field is not nested', async () => {
            pageDefinition = getMockPageDefinition(
                screenId,
                {
                    values: {
                        [elementId]: { anyField: 'asd', _id: '12' },
                        myParentElementId: new CollectionValue({
                            screenId,
                            elementId: 'myParentElementId',
                            isTransient: false,
                            hasNextPage: false,
                            orderBy: [{}],
                            columnDefinitions: [[]],
                            nodeTypes: {},
                            nodes: ['@sage/any-package/AnyNode'],
                            filter: [{}],
                            initialValues: [],
                            fieldType: CollectionFieldTypes.LOOKUP_DIALOG,
                        }),
                    },
                },
                {
                    uiComponentProperties: {
                        [screenId]: {
                            node: '@sage/any-package/AnyNode',
                        } as PageProperties<any, any>,
                        [elementId]: {
                            node: '@sage/any-package/AnyNode',
                            valueField: 'anyField',
                        } as any,
                    },
                },
            );
            mockState = getMockState({
                nodeTypes,
                screenDefinitions: {
                    [screenId]: pageDefinition!,
                },
            });

            mockState = getMockState({
                nodeTypes: {
                    ...nodeTypes,
                    AnyNode: {
                        title: 'AnyNode',
                        name: 'AnyNode',
                        packageName: '@sage/xtrem-test',
                        properties: {
                            _id: {
                                type: GraphQLTypes.IntOrString,
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field1: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field3: {
                                type: GraphQLTypes.Boolean,
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field4: {
                                type: 'IntReference',
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field5: { type: 'Object', kind: GraphQLKind.Scalar, canFilter: true, isOnInputType: false },
                        },
                        mutations: {},
                    },
                },
                screenDefinitions: {
                    [screenId]: pageDefinition!,
                },
            });
            getMockStore(mockState);

            await graphqlService.fetchReferenceFieldData({
                fieldProperties: {
                    node: '@sage/any-package/AnyNode',
                    valueField: 'anyField' as any,
                },
                screenId,
                elementId,
                filter: { netPrice: { _gt: 12 } } as any,
                parentElementId: 'myParentElementId',
                recordContext: {
                    _id: '234',
                    field1: 'hello',
                    field3: true,
                    field4: { _id: '5678', code: 'POXXXXXX', name: 'John Doe' },
                    field5: { _id: '123', name: 'Foo' },
                },
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    anyPackage: {
                        anyNode: {
                            lookups: {
                                __args: {
                                    data: {
                                        _id: '234',
                                        field1: 'hello',
                                        field3: true,
                                        field4: '_id:5678',
                                    },
                                },
                                myField: {
                                    __args: {
                                        filter: '{"netPrice":{"_gt":12}}',
                                        orderBy: '{"_id":1}',
                                    },
                                    edges: {
                                        cursor: true,
                                        node: {
                                            _id: true,
                                            anyField: true,
                                            netPrice: true,
                                        },
                                    },
                                    pageInfo: {
                                        endCursor: true,
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should fetch reference field items with lookup with supplied context that contains a reference field', async () => {
            pageDefinition = getMockPageDefinition(
                screenId,
                {
                    values: {
                        [elementId]: { anyField: 'asd', _id: '12' },
                        myParentElementId: new CollectionValue({
                            screenId,
                            elementId: 'myParentElementId',
                            isTransient: false,
                            hasNextPage: false,
                            orderBy: [{}],
                            columnDefinitions: [[]],
                            nodeTypes: {},
                            nodes: ['@sage/any-package/AnyNode'],
                            filter: [{}],
                            initialValues: [],
                            fieldType: CollectionFieldTypes.LOOKUP_DIALOG,
                        }),
                    },
                },
                {
                    uiComponentProperties: {
                        [screenId]: {
                            node: '@sage/any-package/AnyNode',
                        } as PageProperties<any, any>,
                        [elementId]: {
                            node: '@sage/any-package/AnyNode',
                            valueField: 'anyField',
                        } as any,
                    },
                },
            );
            mockState = getMockState({
                nodeTypes,
                screenDefinitions: {
                    [screenId]: pageDefinition!,
                },
            });

            mockState = getMockState({
                nodeTypes: {
                    ...nodeTypes,
                    AnyNode: {
                        title: 'AnyNode',
                        name: 'AnyNode',
                        packageName: '@sage/xtrem-test',
                        properties: {
                            _id: {
                                type: GraphQLTypes.IntOrString,
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field1: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field3: {
                                type: GraphQLTypes.Boolean,
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field4: {
                                type: 'IntReference',
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field5: {
                                type: 'Object',
                                kind: GraphQLKind.Scalar,
                                canFilter: false,
                                isOnInputType: false,
                            },
                        },
                        mutations: {},
                    },
                },
                screenDefinitions: {
                    [screenId]: pageDefinition!,
                },
            });
            getMockStore(mockState);

            await graphqlService.fetchReferenceFieldData({
                fieldProperties: {
                    node: '@sage/any-package/AnOtherNode',
                    valueField: 'anyField' as any,
                },
                screenId,
                elementId,
                filter: { netPrice: { _gt: 12 } } as any,
                parentElementId: 'myParentElementId',
                contextNode: '@sage/any-package/AnyNode',
                recordContext: {
                    _id: '234',
                    field1: 'hello',
                    field3: true,
                    field4: { _id: '5678', code: 'POXXXXXX', name: 'John Doe' },
                    field5: { _id: '123', name: 'Foo' },
                },
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    anyPackage: {
                        anyNode: {
                            lookups: {
                                __args: {
                                    data: {
                                        _id: '234',
                                        field1: 'hello',
                                        field3: true,
                                        field4: '_id:5678',
                                    },
                                },
                                myField: {
                                    __args: {
                                        filter: '{"netPrice":{"_gt":12}}',
                                        orderBy: '{"_id":1}',
                                    },
                                    edges: {
                                        cursor: true,
                                        node: {
                                            _id: true,
                                            anyField: true,
                                            netPrice: true,
                                        },
                                    },
                                    pageInfo: {
                                        endCursor: true,
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should fetch reference field items with lookup with a bind property', async () => {
            pageDefinition = getMockPageDefinition(
                screenId,
                {
                    values: {
                        [elementId]: { anyField: 'asd', _id: '12' },
                        myParentElementId: new CollectionValue({
                            screenId,
                            elementId: 'myParentElementId',
                            isTransient: false,
                            hasNextPage: false,
                            orderBy: [{}],
                            columnDefinitions: [[]],
                            nodeTypes: {},
                            nodes: ['@sage/any-package/AnyNode'],
                            filter: [{}],
                            initialValues: [],
                            fieldType: CollectionFieldTypes.LOOKUP_DIALOG,
                        }),
                    },
                },
                {
                    uiComponentProperties: {
                        [screenId]: {
                            node: '@sage/any-package/AnyNode',
                        } as PageProperties<any, any>,
                        [elementId]: {
                            node: '@sage/any-package/AnyNode',
                            valueField: 'anyField',
                        } as any,
                    },
                },
            );
            mockState = getMockState({
                nodeTypes,
                screenDefinitions: {
                    [screenId]: pageDefinition!,
                },
            });

            mockState = getMockState({
                nodeTypes: {
                    ...nodeTypes,
                    AnyNode: {
                        title: 'AnyNode',
                        name: 'AnyNode',
                        packageName: '@sage/xtrem-test',
                        properties: {
                            _id: {
                                type: GraphQLTypes.IntOrString,
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field1: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field3: {
                                type: GraphQLTypes.Boolean,
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field4: {
                                type: 'IntReference',
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field5: {
                                type: 'Object',
                                kind: GraphQLKind.Scalar,
                                canFilter: false,
                                isOnInputType: false,
                            },
                        },
                        mutations: {},
                    },
                },
                screenDefinitions: {
                    [screenId]: pageDefinition!,
                },
            });
            getMockStore(mockState);

            await graphqlService.fetchReferenceFieldData({
                fieldProperties: {
                    node: '@sage/any-package/AnOtherNode',
                    valueField: 'anyField' as any,
                    bind: 'myBindProperty',
                },
                screenId,
                elementId,
                filter: { netPrice: { _gt: 12 } } as any,
                parentElementId: 'myParentElementId',
                contextNode: '@sage/any-package/AnyNode',
                recordContext: {
                    _id: '234',
                    field1: 'hello',
                    field3: true,
                    field4: { _id: '5678', code: 'POXXXXXX', name: 'John Doe' },
                    field5: { _id: '123', name: 'Foo' },
                },
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    anyPackage: {
                        anyNode: {
                            lookups: {
                                __args: {
                                    data: {
                                        _id: '234',
                                        field1: 'hello',
                                        field3: true,
                                        field4: '_id:5678',
                                    },
                                },
                                myBindProperty: {
                                    __args: {
                                        filter: '{"netPrice":{"_gt":12}}',
                                        orderBy: '{"_id":1}',
                                    },
                                    edges: {
                                        cursor: true,
                                        node: {
                                            _id: true,
                                            anyField: true,
                                            netPrice: true,
                                        },
                                    },
                                    pageInfo: {
                                        endCursor: true,
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });

        it('should filter out fields that are not on the input type when fetching reference suggestions', async () => {
            pageDefinition = getMockPageDefinition(
                screenId,
                {
                    values: {
                        [elementId]: { anyField: 'asd', _id: '12' },
                        myParentElementId: new CollectionValue({
                            screenId,
                            elementId: 'myParentElementId',
                            isTransient: false,
                            hasNextPage: false,
                            orderBy: [{}],
                            columnDefinitions: [[]],
                            nodeTypes: {},
                            nodes: ['@sage/any-package/AnyNode'],
                            filter: [{}],
                            initialValues: [],
                            fieldType: CollectionFieldTypes.LOOKUP_DIALOG,
                        }),
                    },
                },
                {
                    uiComponentProperties: {
                        [screenId]: {
                            node: '@sage/any-package/AnyNode',
                        } as PageProperties<any, any>,
                        [elementId]: {
                            node: '@sage/any-package/AnyNode',
                            valueField: 'anyField',
                        } as any,
                    },
                },
            );
            mockState = getMockState({
                nodeTypes: {
                    ...nodeTypes,
                    AnyNode: {
                        title: 'AnyNode',
                        name: 'AnyNode',
                        packageName: '@sage/xtrem-test',
                        properties: {
                            _id: {
                                type: GraphQLTypes.IntOrString,
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field1: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                            field3: {
                                type: GraphQLTypes.Boolean,
                                kind: GraphQLKind.Scalar,
                                canFilter: true,
                                isOnInputType: true,
                            },
                        },
                        mutations: {},
                    },
                },
                screenDefinitions: {
                    [screenId]: pageDefinition!,
                },
            });
            getMockStore(mockState);

            await graphqlService.fetchReferenceFieldData({
                fieldProperties: {
                    node: '@sage/any-package/AnyNode',
                    valueField: 'anyField' as any,
                },
                screenId,
                elementId,
                filter: { netPrice: { _gt: 12 } } as any,
                parentElementId: 'myParentElementId',
                recordContext: {
                    _id: '234',
                    field1: 'hello',
                    field2: 1323,
                    field3: true,
                    field4: { _id: '5678', code: 'POXXXXXX', name: 'John Doe' },
                },
                contextNode: '@sage/any-package/AnyNode',
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    anyPackage: {
                        anyNode: {
                            lookups: {
                                __args: {
                                    data: {
                                        _id: '234',
                                        field1: 'hello',
                                        field3: true,
                                    },
                                },
                                [elementId]: {
                                    __args: {
                                        filter: '{"netPrice":{"_gt":12}}',
                                        orderBy: '{"_id":1}',
                                    },
                                    edges: {
                                        cursor: true,
                                        node: {
                                            _id: true,
                                            anyField: true,
                                            netPrice: true,
                                        },
                                    },
                                    pageInfo: {
                                        endCursor: true,
                                        hasNextPage: true,
                                        hasPreviousPage: true,
                                        startCursor: true,
                                    },
                                },
                            },
                        },
                    },
                },
            });
        });
    });

    describe('fetchDefaultValues', () => {
        let executeGraphqlQueryMock: jest.SpyInstance<
            Promise<any>,
            [query: graphqlUtils.ExecutableQuery, endpoint?: string]
        >;

        beforeEach(() => {
            executeGraphqlQueryMock = jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockResolvedValue({});
        });

        afterEach(() => {
            executeGraphqlQueryMock.mockReset();
        });

        it('should not call the server if the page definition is empty', async () => {
            const result = await graphqlService.fetchDefaultValues(pageDefinition!, '@sage/xtrem-test/MyTestNode');
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            expect(result).toEqual({});
        });
    });

    describe('buildSearchBoxFilterForNestedFields', () => {
        it('Should return a array of filters with a regex filter because property is String', () => {
            const nestedFields: Partial<NestedField<ScreenBase, NestedFieldTypes, any>>[] = [
                {
                    properties: {
                        bind: 'bind001',
                    },
                    type: FieldKey.Text,
                },
            ];
            const nodeTypes: Dict<FormattedNodeDetails> = {
                SalesOrder: {
                    name: 'SalesOrder',
                    title: 'Sales Order',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        bind001: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true },
                    },
                    mutations: {},
                },
            };

            const result = buildSearchBoxFilterForNestedFields(
                nestedFields as any,
                nodeTypes,
                'textSearch',
                'en-US',
                '@sage/x3-sales/SalesOrder',
            );

            const expected = [{ bind001: { _regex: 'textSearch', _options: 'i' } }];
            expect(result).toEqual(expected);
        });

        it('Should escape regex in the search criteria', () => {
            const nestedFields: Partial<NestedField<ScreenBase, NestedFieldTypes, any>>[] = [
                {
                    properties: {
                        bind: 'bind001',
                    },
                    type: FieldKey.Text,
                },
            ];
            const nodeTypes: Dict<FormattedNodeDetails> = {
                SalesOrder: {
                    name: 'SalesOrder',
                    title: 'Sales Order',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        bind001: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true },
                    },
                    mutations: {},
                },
            };

            const result = buildSearchBoxFilterForNestedFields(
                nestedFields as any,
                nodeTypes,
                'Wine \\[0-9\\]+ ml ',
                'en-US',
                '@sage/x3-sales/SalesOrder',
            );

            const expected = [{ bind001: { _regex: 'Wine \\\\\\[0-9\\\\\\]\\+ ml', _options: 'i' } }];
            expect(result).toEqual(expected);
        });

        it('Should return a array of filters with a regex filter because deeply bound property is String', () => {
            const nestedFields: Partial<NestedField<ScreenBase, NestedFieldTypes, any>>[] = [
                {
                    properties: {
                        bind: { supplier: { name: true } },
                    },
                    type: FieldKey.Text,
                },
            ];
            const nodeTypes: Dict<FormattedNodeDetails> = {
                SalesOrder: {
                    name: 'SalesOrder',
                    title: 'Sales Order',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        bind001: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true },
                        supplier: { type: 'Supplier', kind: GraphQLKind.Object, canFilter: true },
                    },
                    mutations: {},
                },
                Supplier: {
                    name: 'Supplier',
                    title: 'Supplier',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        name: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true },
                    },
                    mutations: {},
                },
            };

            const result = buildSearchBoxFilterForNestedFields(
                nestedFields as any,
                nodeTypes,
                'textSearch',
                'en-US',
                '@sage/x3-sales/SalesOrder',
            );

            const expected = [{ supplier: { name: { _regex: 'textSearch', _options: 'i' } } }];
            expect(result).toEqual(expected);
        });

        it('Should return a array empty because all the properties has canFilter to false', () => {
            const nestedFields: Partial<NestedField<ScreenBase, NestedFieldTypes, any>>[] = [
                {
                    properties: {
                        bind: 'bind001',
                        canFilter: false,
                    },
                    type: FieldKey.Text,
                },
            ];
            const nodeTypes: Dict<FormattedNodeDetails> = {
                SalesOrder: {
                    name: 'SalesOrder',
                    title: 'Sales Order',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        bind001: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true },
                    },
                    mutations: {},
                },
            };

            const result = buildSearchBoxFilterForNestedFields(
                nestedFields as any,
                nodeTypes,
                'textSearch',
                'en-US',
                '@sage/x3-sales/SalesOrder',
            );

            const expected = [];
            expect(result).toEqual(expected);
        });

        it("Should return a array empty because the SalesOrder's bind001 property is not filterable", () => {
            const nestedFields: Partial<NestedField<ScreenBase, NestedFieldTypes, any>>[] = [
                {
                    properties: {
                        bind: 'bind001',
                    },
                    type: FieldKey.Text,
                },
            ];
            const nodeTypes: Dict<FormattedNodeDetails> = {
                SalesOrder: {
                    name: 'SalesOrder',
                    title: 'Sales Order',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        bind001: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: false },
                    },
                    mutations: {},
                },
            };

            const result = buildSearchBoxFilterForNestedFields(
                nestedFields as any,
                nodeTypes,
                'textSearch',
                'en-US',
                '@sage/x3-sales/SalesOrder',
            );

            const expected = [];
            expect(result).toEqual(expected);
        });

        it('Should return a array with 1 regex filter because the it doesnt have node (transient page)', () => {
            const nestedFields: Partial<NestedField<ScreenBase, NestedFieldTypes, any>>[] = [
                {
                    properties: {
                        bind: 'transientBind',
                    },
                    type: FieldKey.Text,
                },
            ];
            const nodeTypes: Dict<FormattedNodeDetails> = {
                SalesOrder: {
                    name: 'SalesOrder',
                    title: 'Sales Order',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        bind001: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true },
                    },
                    mutations: {},
                },
            };

            const result = buildSearchBoxFilterForNestedFields(nestedFields as any, nodeTypes, 'textSearch', 'en-US');

            const expected = [{ transientBind: { _regex: 'textSearch', _options: 'i' } }];
            expect(result).toEqual(expected);
        });

        it('Should return a array with 1 date filter', () => {
            const nestedFields: Partial<NestedField<ScreenBase, NestedFieldTypes, any>>[] = [
                {
                    properties: {
                        bind: 'bind001',
                    },
                    type: FieldKey.Date,
                },
            ];
            const nodeTypes: Dict<FormattedNodeDetails> = {
                SalesOrder: {
                    name: 'SalesOrder',
                    title: 'Sales Order',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        bind001: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar, canFilter: true },
                    },
                    mutations: {},
                },
            };

            const result = buildSearchBoxFilterForNestedFields(
                nestedFields as any,
                nodeTypes,
                '2020-01-31',
                'en-US',
                '@sage/x3-sales/SalesOrder',
            );

            const expected = [{ bind001: { _eq: '2020-01-31' } }];
            expect(result).toEqual(expected);
        });

        it('Should return a array with 1 date filter for a deeply bound date field', () => {
            const nestedFields: Partial<NestedField<ScreenBase, NestedFieldTypes, any>>[] = [
                {
                    properties: {
                        bind: { supplier: { createdAt: true } },
                    },
                    type: FieldKey.Date,
                },
            ];

            const nodeTypes: Dict<FormattedNodeDetails> = {
                SalesOrder: {
                    name: 'SalesOrder',
                    title: 'Sales Order',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        bind001: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true },
                        supplier: { type: 'Supplier', kind: 'OBJECT' },
                    },
                    mutations: {},
                },
                Supplier: {
                    name: 'Supplier',
                    title: 'Supplier',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        createdAt: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar, canFilter: true },
                    },
                    mutations: {},
                },
            };

            const result = buildSearchBoxFilterForNestedFields(
                nestedFields as any,
                nodeTypes,
                '2020-01-31',
                'en-US',
                '@sage/x3-sales/SalesOrder',
            );

            const expected = [{ supplier: { createdAt: { _eq: '2020-01-31' } } }];
            expect(result).toEqual(expected);
        });

        it('Should return a array with 1 date filter when a localized date value is passed in', () => {
            const nestedFields: Partial<NestedField<ScreenBase, NestedFieldTypes, any>>[] = [
                {
                    properties: {
                        bind: 'bind001',
                    },
                    type: FieldKey.Date,
                },
            ];
            const nodeTypes: Dict<FormattedNodeDetails> = {
                SalesOrder: {
                    name: 'SalesOrder',
                    title: 'Sales Order',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        bind001: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar, canFilter: true },
                    },
                    mutations: {},
                },
            };

            const result = buildSearchBoxFilterForNestedFields(
                nestedFields as any,
                nodeTypes,
                '31/01/2020',
                'en-US',
                '@sage/x3-sales/SalesOrder',
            );

            const expected = [{ bind001: { _eq: '2020-01-31' } }];
            expect(result).toEqual(expected);
        });

        it('Should return a array with 1 nested field filter', () => {
            const nestedFields: Partial<NestedField<ScreenBase, NestedFieldTypes, any>>[] = [
                {
                    properties: {
                        bind: 'bind001',
                        node: '@sage/x3-sales/SalesOrder',
                        valueField: 'valueFieldBind',
                        helperText: 'helperTextBind',
                    },
                    type: FieldKey.Reference,
                },
            ];
            const nodeTypes: Dict<FormattedNodeDetails> = {
                SalesOrder: {
                    name: 'SalesOrder',
                    title: 'Sales Order',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        bind001: { type: 'IntReference', kind: GraphQLKind.Scalar, canFilter: true },
                        valueFieldBind: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true },
                    },
                    mutations: {},
                },
            };

            const result = buildSearchBoxFilterForNestedFields(
                nestedFields as any,
                nodeTypes,
                'textPatata',
                'en-US',
                '@sage/x3-sales/SalesOrder',
            );

            const expected = [{ bind001: { valueFieldBind: { _regex: 'textPatata', _options: 'i' } } }];
            expect(result).toEqual(expected);
        });

        it('Should return a array with 1 nested field with deeply bound reference with deeply bound value field filter', () => {
            const nestedFields: Partial<NestedField<ScreenBase, NestedFieldTypes, any>>[] = [
                {
                    properties: {
                        title: 'Country',
                        bind: { supplier: { address: true } },
                        node: '@sage/x3-sales/SalesOrder',
                        valueField: { country: { currency: { symbol: true } } },
                        helperText: 'helperTextBind',
                    },
                    type: FieldKey.Reference,
                },
            ];

            const nodeTypes: Dict<FormattedNodeDetails> = {
                SalesOrder: {
                    name: 'SalesOrder',
                    title: 'Sales Order',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        bind001: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true },
                        supplier: {
                            type: 'Supplier',
                            kind: GraphQLKind.Object,
                            canFilter: true,
                            isMutable: true,
                            isOnInputType: true,
                        },
                    },
                    mutations: {},
                },
                Supplier: {
                    name: 'Supplier',
                    title: 'Supplier',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        createdAt: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar, canFilter: true },
                        address: {
                            type: 'Address',
                            kind: GraphQLKind.Object,
                            isMutable: true,
                            isOnInputType: true,
                            canFilter: true,
                        },
                    },
                    mutations: {},
                },
                Address: {
                    name: 'Address',
                    title: 'Address',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        createdAt: {
                            type: GraphQLTypes.Date,
                            kind: GraphQLKind.Scalar,
                            canFilter: true,
                            isOnInputType: true,
                        },
                        country: {
                            type: 'Country',
                            kind: GraphQLKind.Object,
                            isMutable: true,
                            isOnInputType: true,
                            canFilter: true,
                        },
                    },
                    mutations: {},
                },
                Country: {
                    name: 'Country',
                    title: 'Country',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        createdAt: {
                            type: GraphQLTypes.Date,
                            kind: GraphQLKind.Scalar,
                            canFilter: true,
                            isOnInputType: true,
                        },
                        currency: {
                            type: 'Currency',
                            kind: GraphQLKind.Object,
                            isMutable: true,
                            isOnInputType: true,
                            canFilter: true,
                        },
                    },
                    mutations: {},
                },
                Currency: {
                    name: 'Currency',
                    title: 'Currency',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        symbol: {
                            type: GraphQLTypes.String,
                            kind: GraphQLKind.Scalar,
                            canFilter: true,
                            isOnInputType: true,
                        },
                    },
                    mutations: {},
                },
            };

            const result = buildSearchBoxFilterForNestedFields(
                nestedFields as any,
                nodeTypes,
                'textPatata',
                'en-US',
                '@sage/x3-sales/SalesOrder',
            );

            const expected = [
                {
                    supplier: {
                        address: { country: { currency: { symbol: { _regex: 'textPatata', _options: 'i' } } } },
                    },
                },
            ];
            expect(result).toEqual(expected);
        });

        it('Should return a array with 1 nested field with reference filter', () => {
            const nestedFields: Partial<NestedField<ScreenBase, NestedFieldTypes, any>>[] = [
                {
                    properties: {
                        bind: 'bind001',
                        node: '@sage/x3-sales/SalesOrder',
                        valueField: { valueFieldBind: { patataText: true } },
                        helperText: 'helperTextBind',
                    },
                    type: FieldKey.Reference,
                },
            ];
            const nodeTypes: Dict<FormattedNodeDetails> = {
                SalesOrder: {
                    name: 'SalesOrder',
                    title: 'Sales Order',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        bind001: { type: 'Reference', kind: GraphQLKind.Scalar, canFilter: true },
                        valueFieldBind: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true },
                        patataText: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, canFilter: true },
                    },
                    mutations: {},
                },
            };

            const result = buildSearchBoxFilterForNestedFields(
                nestedFields as any,
                nodeTypes,
                'textPatata',
                'en-US',
                '@sage/x3-sales/SalesOrder',
            );

            const expected = [{ bind001: { valueFieldBind: { patataText: { _regex: 'textPatata', _options: 'i' } } } }];
            expect(result).toEqual(expected);
        });
    });

    describe('fetchCollectionRecord', () => {
        const nestedFields: NestedField<ScreenBase, NestedFieldTypes, any>[] = [
            {
                properties: {
                    bind: 'bind001',
                    node: '@sage/x3-sales/SalesOrder',
                    valueField: 'valueFieldBind',
                    helperText: 'helperTextBind',
                },
                type: FieldKey.Reference,
                defaultUiProperties: { bind: 'bind001' },
            },
            {
                properties: {
                    bind: 'bind002',
                    title: 'Test text field',
                },
                type: FieldKey.Text,
                defaultUiProperties: { bind: 'bind002' },
            },
            {
                properties: {
                    bind: 'bind003',
                    title: 'Test numeric field',
                },
                type: FieldKey.Numeric,
                defaultUiProperties: { bind: 'bind003' },
            },
        ];

        beforeEach(() => {
            executeGraphqlQueryMock = jest.spyOn(graphqlUtils, 'executeGraphqlQuery').mockResolvedValue({});
        });

        afterEach(() => {
            executeGraphqlQueryMock.mockReset();
        });

        it('should get an individual collection record with fetchCollectionRecord', async () => {
            const testNodeValue = {
                _id: '1234',
                bind001: { valueFieldBind: 'test', helperTextBind: 'test again' },
                bind002: 'another test',
                bind003: 124,
            };
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    xtremTest: {
                        sampleNode: {
                            query: {
                                edges: [{ node: testNodeValue }],
                            },
                        },
                    },
                },
            });

            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            const result = await graphqlService.fetchCollectionRecord(
                pageDefinition!,
                nestedFields,
                '@sage/xtrem-test/SampleNode',
                '1234',
            );
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                axiosCancelToken: undefined,
                cacheSettings: undefined,
                endpoint: '/api',
                forceRefetch: undefined,
                query: {
                    xtremTest: {
                        sampleNode: {
                            query: {
                                __args: {
                                    filter: '{"_id":"1234"}',
                                },
                                edges: {
                                    cursor: true,
                                    node: {
                                        _id: true,
                                        bind001: {
                                            _id: true,
                                            valueFieldBind: true,
                                        },
                                        bind002: true,
                                        bind003: true,
                                    },
                                },
                            },
                        },
                    },
                },
            });

            expect(result).toEqual(testNodeValue);
        });

        it('should return null if no record is found', async () => {
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    xtremTest: {
                        sampleNode: {
                            query: {
                                edges: [],
                            },
                        },
                    },
                },
            });

            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            const result = await graphqlService.fetchCollectionRecord(
                pageDefinition!,
                nestedFields,
                '@sage/xtrem-test/SampleNode',
                '1234',
            );

            expect(result).toEqual(null);
        });
    });
});
