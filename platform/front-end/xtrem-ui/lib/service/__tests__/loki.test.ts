import Lokijs from '@sage/lokijs';
import { FieldKey } from '@sage/xtrem-shared';
import { getGroupFilterValue } from '../collection-data-utils';

describe('Loki js', () => {
    it('should be able to get nested null values', () => {
        const lokiDb = new Lokijs('db');
        lokiDb.addCollection<any>('collection', {
            unique: ['__compositeKey'],
            indices: ['__dirty', '__action', '_id', '__compositeKey', '__level', '__uncommitted'],
            disableMeta: true,
        });
        const collection = lokiDb.getCollection('collection');
        const random = { nested: { category: 'random' } };
        collection.add(random);
        const nullCategory = { nested: { category: null } };
        collection.add(nullCategory);
        const nullCategoryName = { nested: { category: { name: null } } };
        collection.add(nullCategoryName);
        const nonNullCategoryName = { nested: { category: { name: 'name' } } };
        collection.add(nonNullCategoryName);
        const clientFilter = getGroupFilterValue({
            group: { key: 'nested.category.name', value: '', type: FieldKey.Reference },
            mode: 'client',
        });
        expect(clientFilter).toEqual({
            $or: [
                {
                    nested: {
                        $eq: null,
                    },
                },
                {
                    'nested.category': {
                        $eq: null,
                    },
                },
                {
                    'nested.category.name': {
                        $eq: null,
                    },
                },
            ],
        });
        const nulls = collection.find(clientFilter);
        expect(nulls).not.toContain(random);
        expect(nulls).not.toContain(nonNullCategoryName);
        expect(nulls).toContain(nullCategory);
        expect(nulls).toContain(nullCategoryName);
        const serverFilter = getGroupFilterValue({
            group: { key: 'nested.category.name', value: '', type: FieldKey.Reference },
            mode: 'server',
        });
        expect(serverFilter).toEqual({
            _or: [
                {
                    nested: {
                        _eq: null,
                    },
                },
                {
                    nested: {
                        category: {
                            _eq: null,
                        },
                    },
                },
                {
                    nested: {
                        category: {
                            name: {
                                _eq: null,
                            },
                        },
                    },
                },
            ],
        });
    });
});
