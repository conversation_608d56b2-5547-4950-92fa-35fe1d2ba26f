// This block must be the first one
import { getMockState, getMockStore } from '../../__tests__/test-helpers';

import axios from 'axios';
import type * as xtremRedux from '../../redux';
import * as windowUtils from '../../utils/window';
import type { NodeCollection, QueryWrapper } from '../graphql-utils';
import { executeGraphqlQuery, getGroups, removeEdges, unwrapQueryResponse, wrapQuery } from '../graphql-utils';

describe('graphql-utils', () => {
    const resolveValue = { test: 'test' };
    let mockState: xtremRedux.XtremAppState;

    beforeEach(() => {
        jest.spyOn(windowUtils, 'isDevMode').mockReturnValue(true);
        jest.spyOn(axios, 'post').mockResolvedValue({
            data: resolveValue,
            status: 200,
            statusText: 'OK',
            headers: {},
            config: {},
        });
        mockState = getMockState();
        getMockStore(mockState);
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('executeGraphqlQuery', () => {
        it('should call the right endpoint with the query', async () => {
            expect(axios.post).not.toHaveBeenCalled();
            await executeGraphqlQuery({ query: { objectProperty: { valueProperty: true } } });
            expect(axios.post).toHaveBeenCalledTimes(1);
            expect(axios.post).toHaveBeenCalledWith(
                '/api',
                {
                    query: `query {
    objectProperty {
        valueProperty
    }
}`,
                },
                { headers: { 'Accept-Language': 'en-US' } },
            );
        });

        it('should call the right endpoint when a path prefix is defined', async () => {
            mockState.applicationContext!.path = '/special/endpoint';
            expect(axios.post).not.toHaveBeenCalled();
            await executeGraphqlQuery({ query: { objectProperty: { valueProperty: true } } });
            expect(axios.post).toHaveBeenCalledTimes(1);
            expect(axios.post).toHaveBeenCalledWith(
                '/special/endpoint/api',
                {
                    query: `query {
    objectProperty {
        valueProperty
    }
}`,
                },
                { headers: { 'Accept-Language': 'en-US' } },
            );
        });

        it('should resolve with the "data" objectProperty of the response', async () => {
            expect(axios.post).not.toHaveBeenCalled();
            const response = await executeGraphqlQuery({ query: { objectProperty: { valueProperty: true } } });
            expect(response).toEqual(resolveValue);
        });

        it('should add custom headers to the network request', async () => {
            mockState.applicationContext!.requestHeaders = {
                'Accept-Language': 'en-US',
                'custom-request-for-graphql': 'This could well be a token',
            };
            getMockStore(mockState);

            expect(axios.post).not.toHaveBeenCalled();
            await executeGraphqlQuery({ query: { objectProperty: { valueProperty: true } } });
            expect(axios.post).toHaveBeenCalledTimes(1);
            expect(axios.post).toHaveBeenCalledWith(
                '/api',
                {
                    query: `query {
    objectProperty {
        valueProperty
    }
}`,
                },
                { headers: mockState.applicationContext!.requestHeaders },
            );
        });
    });

    it('should call onApiRequestError function in app context on error', async () => {
        (axios.post as any).mockRejectedValue('MY TEST ERROR');

        mockState.applicationContext!.onApiRequestError = jest.fn();

        expect(mockState.applicationContext!.onApiRequestError).not.toHaveBeenCalled();
        try {
            await executeGraphqlQuery({ query: { objectProperty: { valueProperty: true } } });
        } catch (e) {
            expect(e).toEqual('MY TEST ERROR');
        }
        expect(mockState.applicationContext!.onApiRequestError).toHaveBeenCalledWith('MY TEST ERROR');
    });

    it('should retry post call numAttempts if 503 server error after ms milliseconds have ellapsed', async () => {
        jest.spyOn(global, 'setTimeout');
        const numAttempts = 3;
        const ms = 10;
        (axios.post as any).mockRejectedValue({ response: { status: 503, headers: { 'retry-after': ms } } });
        try {
            await executeGraphqlQuery({ query: { objectProperty: { valueProperty: true } } });
        } catch (error) {
            expect(axios.post).toHaveBeenCalledTimes(numAttempts);
            for (let i = 1; i <= numAttempts - 1; i += 1) {
                expect(setTimeout).toHaveBeenNthCalledWith(i, expect.any(Function), ms);
            }
        }
    });

    describe('removeEdges', () => {
        it('should properly remove property edge from the object received', () => {
            const received = {
                query: {
                    edges: [
                        {
                            node: {
                                lines: {
                                    query: {
                                        edges: [
                                            {
                                                node: {
                                                    product: {
                                                        code: 'PC001',
                                                        description: 'Test Product',
                                                    },
                                                    _id: 123456,
                                                    category: 'normal',
                                                },
                                                cursor: 'TEST00SO111111~1000~1000',
                                            },
                                        ],
                                    },
                                },
                                customer: {
                                    code: 'C001',
                                    description: 'Test Customer',
                                },
                                _id: 123456,
                            },
                            cursor: 'TEST00SO111111',
                        },
                    ],
                },
            };
            const expected = [
                {
                    _id: 123456,
                    customer: { code: 'C001', description: 'Test Customer' },
                    lines: [
                        {
                            _id: 123456,
                            category: 'normal',
                            product: { code: 'PC001', description: 'Test Product' },
                        },
                    ],
                },
            ];
            expect(removeEdges(received)).toEqual(expected);
        });
    });

    describe('wrapQuery', () => {
        let node: string;
        let alias: string;
        let nodeCollectionQuery: NodeCollection;
        beforeEach(() => {
            node = 'lines';
            alias = 'itemsLines';
            nodeCollectionQuery = {
                query: {
                    edges: {
                        node: {
                            product: { code: true, description: true },
                            _id: true,
                            orderedQuantity: true,
                            orderCategory: true,
                        },
                        cursor: true,
                    },
                    __args: { filter: JSON.stringify({}) },
                },
            };
        });

        it('should properly wrap the query object with the node indicated', () => {
            const expected = {
                lines: {
                    query: {
                        __args: { filter: '{}' },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                orderCategory: true,
                                orderedQuantity: true,
                                product: { code: true, description: true },
                            },
                        },
                    },
                },
            };
            expect(wrapQuery(node, nodeCollectionQuery)).toEqual(expected);
        });

        it('should properly wrap the query object with the alias indicated', () => {
            const expected = {
                itemsLines: {
                    __aliasFor: 'lines',
                    query: {
                        __args: { filter: '{}' },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                orderCategory: true,
                                orderedQuantity: true,
                                product: { code: true, description: true },
                            },
                        },
                    },
                },
            };
            expect(wrapQuery(node, nodeCollectionQuery, alias)).toEqual(expected);
        });
    });

    describe('unwrapQueryResponse', () => {
        let node: string;
        let alias: string;
        let result: QueryWrapper<{}>;
        beforeEach(() => {
            node = '@sage/xtrem-sales/salesOrder';
            alias = 'itemsPanel';
            result = {
                xtremSales: {
                    salesOrder: {
                        query: {
                            edges: [
                                {
                                    node: {
                                        lines: {
                                            query: {
                                                edges: [
                                                    {
                                                        node: {
                                                            product: {
                                                                code: 'PC001',
                                                                description: 'Test Product',
                                                            },
                                                            _id: 12345,
                                                            orderedQuantity: 1,
                                                            orderCategory: 'normal',
                                                        },
                                                        cursor: 'TEST00SO111111~1000~1000',
                                                    },
                                                ],
                                            },
                                        },
                                        salesSite: {
                                            code: 'C001',
                                            name: 'Test company',
                                        },
                                        _id: 56789,
                                        orderNumber: 'TEST00SO111111',
                                        orderDate: '2018-07-27',
                                    },
                                    cursor: 'TEST00SO111111',
                                },
                            ],
                        },
                    },
                },
                itemsPanel: {
                    salesOrder: {
                        query: {
                            edges: [
                                {
                                    node: {
                                        billToCustomer: {
                                            companyName: 'Test customer 1',
                                            code: 'AO001',
                                        },
                                        _id: 56789,
                                        orderNumber: 'TEST00SO111111',
                                    },
                                    cursor: 'TEST00SO111111',
                                },
                                {
                                    node: {
                                        billToCustomer: {
                                            companyName: 'Test customer 2',
                                            code: 'AO002',
                                        },
                                        _id: 56780,
                                        orderNumber: 'TEST00SO222222',
                                    },
                                    cursor: 'TEST00SO222222',
                                },
                            ],
                        },
                    },
                },
            };
        });

        it('should properly unwrap the result from the node and the result received', () => {
            const expected = {
                query: {
                    edges: [
                        {
                            cursor: 'TEST00SO111111',
                            node: {
                                _id: 56789,
                                lines: {
                                    query: {
                                        edges: [
                                            {
                                                cursor: 'TEST00SO111111~1000~1000',
                                                node: {
                                                    _id: 12345,
                                                    orderCategory: 'normal',
                                                    orderedQuantity: 1,
                                                    product: { code: 'PC001', description: 'Test Product' },
                                                },
                                            },
                                        ],
                                    },
                                },
                                orderDate: '2018-07-27',
                                orderNumber: 'TEST00SO111111',
                                salesSite: {
                                    code: 'C001',
                                    name: 'Test company',
                                },
                            },
                        },
                    ],
                },
            };
            expect(unwrapQueryResponse(node, result)).toEqual(expected);
        });

        it('should properly unwrap the result from the alias and the result received', () => {
            const expected = {
                query: {
                    edges: [
                        {
                            cursor: 'TEST00SO111111',
                            node: {
                                _id: 56789,
                                billToCustomer: { code: 'AO001', companyName: 'Test customer 1' },
                                orderNumber: 'TEST00SO111111',
                            },
                        },
                        {
                            cursor: 'TEST00SO222222',
                            node: {
                                _id: 56780,
                                billToCustomer: { code: 'AO002', companyName: 'Test customer 2' },
                                orderNumber: 'TEST00SO222222',
                            },
                        },
                    ],
                },
            };
            expect(unwrapQueryResponse(node, result, alias)).toEqual(expected);
        });
    });

    describe('getGroups', () => {
        it('should return the correct groups along with their aggregations', () => {
            const target = {
                xtremShowCase: {
                    showCaseProduct: {
                        queryAggregate: {
                            edges: [
                                {
                                    node: {
                                        group: {
                                            provider: {
                                                textField: 'Ali Express',
                                                _id: '1',
                                            },
                                        },
                                        values: {
                                            _id: {
                                                distinctCount: 249,
                                            },
                                        },
                                    },
                                    cursor: '["Ali Express",1]#80',
                                },
                                {
                                    node: {
                                        group: {
                                            provider: {
                                                textField: 'Amazon',
                                                _id: '2',
                                            },
                                        },
                                        values: {
                                            _id: {
                                                distinctCount: 251,
                                            },
                                        },
                                    },
                                    cursor: '["Amazon",2]#07',
                                },
                                {
                                    node: {
                                        group: {
                                            provider: {
                                                textField: 'Barcelona Activa',
                                                _id: '4',
                                            },
                                        },
                                        values: {
                                            _id: {
                                                distinctCount: 1,
                                            },
                                        },
                                    },
                                    cursor: '["Barcelona Activa",4]#28',
                                },
                                {
                                    node: {
                                        group: {
                                            provider: {
                                                textField: 'Decathlon',
                                                _id: '3',
                                            },
                                        },
                                        values: {
                                            _id: {
                                                distinctCount: 1,
                                            },
                                        },
                                    },
                                    cursor: '["Decathlon",3]#10',
                                },
                            ],
                        },
                    },
                },
            };
            const expected = {
                groups: [
                    {
                        node: {
                            provider: {
                                textField: 'Ali Express',
                                _id: '1',
                            },
                            _id: '__group-Ali Express',
                            __isGroup: true,
                            __groupKey: 'provider.textField',
                            __groupCount: 249,
                        },
                        cursor: '["Ali Express",1]#80',
                    },
                    {
                        node: {
                            provider: {
                                textField: 'Amazon',
                                _id: '2',
                            },
                            _id: '__group-Amazon',
                            __isGroup: true,
                            __groupKey: 'provider.textField',
                            __groupCount: 251,
                        },
                        cursor: '["Amazon",2]#07',
                    },
                    {
                        node: {
                            provider: {
                                textField: 'Barcelona Activa',
                                _id: '4',
                            },
                            _id: '__group-Barcelona Activa',
                            __isGroup: true,
                            __groupKey: 'provider.textField',
                            __groupCount: 1,
                        },
                        cursor: '["Barcelona Activa",4]#28',
                    },
                    {
                        node: {
                            provider: {
                                textField: 'Decathlon',
                                _id: '3',
                            },
                            _id: '__group-Decathlon',
                            __isGroup: true,
                            __groupKey: 'provider.textField',
                            __groupCount: 1,
                        },
                        cursor: '["Decathlon",3]#10',
                    },
                ],
                key: 'showCaseProduct',
            };
            const result = getGroups(target);
            expect(result).toEqual(expected);
        });
    });
});
