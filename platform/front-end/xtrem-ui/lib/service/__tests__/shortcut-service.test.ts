import { dispatchKeyEvent, getMockStore } from '../../__tests__/test-helpers';
import * as shortcutService from '../shortcut-service';

describe('Hotkey Service', () => {
    let state: any;
    beforeEach(() => {
        state = { isKeyboardShortcutsEnabled: true };
        getMockStore(state);
    });

    afterEach(() => {
        shortcutService.clearKeyCombination();
        dispatchKeyEvent('escape', 'keyup');
        dispatchKeyEvent('d', 'keyup');
        jest.clearAllTimers();
        jest.clearAllMocks();
    });

    it('should listen on keyboard events', () => {
        const spy = jest.fn();
        shortcutService.subscribe(['escape', 'd'], spy);
        expect(spy).not.toHaveBeenCalled();
        dispatchKeyEvent('escape', 'keydown');
        expect(spy).not.toHaveBeenCalled();
        dispatchKeyEvent('d', 'keydown');
        expect(spy).toHaveBeenCalledTimes(1);
    });

    it('should listen repeated keyboard events', () => {
        const spy = jest.fn();
        shortcutService.subscribe(['escape', 'd'], spy);
        dispatchKeyEvent('escape', 'keydown');
        dispatchKeyEvent('d', 'keydown');
        expect(spy).toHaveBeenCalledTimes(1);
        dispatchKeyEvent('d', 'keyup');
        expect(spy).toHaveBeenCalledTimes(1);
        dispatchKeyEvent('d', 'keydown');
        expect(spy).toHaveBeenCalledTimes(2);
    });

    it('should remove direction from control keys', () => {
        let spy = jest.fn();
        shortcutService.subscribe(['control', 'd'], spy);
        dispatchKeyEvent('ControlLeft', 'keydown');
        dispatchKeyEvent('d', 'keydown');
        expect(spy).toHaveBeenCalledTimes(1);
        shortcutService.clearKeyCombination();

        spy = jest.fn();
        shortcutService.subscribe(['alt', 'd'], spy);
        dispatchKeyEvent('AltRight', 'keydown');
        dispatchKeyEvent('d', 'keydown');
        expect(spy).toHaveBeenCalledTimes(1);
        shortcutService.clearKeyCombination();

        spy = jest.fn();
        shortcutService.subscribe(['shift', 'b'], spy);
        dispatchKeyEvent('ShiftRight', 'keydown');
        dispatchKeyEvent('b', 'keydown');
        expect(spy).toHaveBeenCalledTimes(1);
    });

    it('should remap the meta key to control', () => {
        const spy = jest.fn();
        shortcutService.subscribe(['control', 'd'], spy);
        dispatchKeyEvent('MetaLeft', 'keydown');
        dispatchKeyEvent('d', 'keydown');
        expect(spy).toHaveBeenCalledTimes(1);
        shortcutService.clearKeyCombination();
    });

    it('should release all keys when the window loses focus', () => {
        const spy = jest.fn();
        shortcutService.subscribe(['control', 'd'], spy);
        dispatchKeyEvent('MetaLeft', 'keydown');
        dispatchKeyEvent('d', 'keydown');
        expect(shortcutService.getCurrentKeySequence()).toEqual(['control', 'd']);
        window.dispatchEvent(new FocusEvent('blur'));
        expect(shortcutService.getCurrentKeySequence()).toEqual([]);
    });

    it('should trigger the last matching callback only', () => {
        const spy = jest.fn();
        const spy2 = jest.fn();
        shortcutService.subscribe(['escape', 'd'], spy);
        shortcutService.subscribe(['escape', 'd'], spy2);
        dispatchKeyEvent('escape', 'keydown');
        dispatchKeyEvent('d', 'keydown');
        expect(spy).not.toHaveBeenCalled();
        expect(spy2).toHaveBeenCalledTimes(1);
    });

    it('should not listen on the keyboard events if it is disabled by the state', () => {
        const spy = jest.fn();
        state.isKeyboardShortcutsEnabled = false;

        shortcutService.subscribe(['escape', 'd'], spy);
        expect(spy).not.toHaveBeenCalled();
        dispatchKeyEvent('escape', 'keydown');
        expect(spy).not.toHaveBeenCalled();
        dispatchKeyEvent('d', 'keydown');
        expect(spy).not.toHaveBeenCalled();
    });

    it('should handle unsubscriptions by passing the subscription id', () => {
        const spy = jest.fn();
        const id = shortcutService.subscribe(['escape', 'd'], spy);
        dispatchKeyEvent('escape', 'keydown');
        dispatchKeyEvent('d', 'keydown');
        expect(spy).toHaveBeenCalledTimes(1);
        dispatchKeyEvent('escape', 'keyup');
        dispatchKeyEvent('d', 'keyup');
        shortcutService.unsubscribe(id);
        dispatchKeyEvent('escape', 'keydown');
        dispatchKeyEvent('d', 'keydown');
        expect(spy).toHaveBeenCalledTimes(1);
    });
});
