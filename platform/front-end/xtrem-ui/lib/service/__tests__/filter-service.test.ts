import { FieldKey, SET } from '../../component/types';
import type { Filter } from '../filter-service';
import { getGraphQLFilter, mergeGraphQLFilters } from '../filter-service';

describe('Filter service', () => {
    describe('getGraphQLFilter function', () => {
        it('Should retrieve a correct graphQL filter using filters and no nested field', () => {
            const filters: Filter<any>[] = [
                { id: '_id', value: [{ filterValue: ['623'], filterType: { text: '_id in list', value: SET } }] },
            ];
            const graphQLFilter = getGraphQLFilter(filters, [
                {
                    type: FieldKey.Text,
                    properties: {
                        bind: '_id',
                        type: null,
                    },
                },
            ]);
            const expected = { _id: { _in: ['623'] } };
            expect(graphQLFilter).toEqual(expected);
        });
    });
    describe('mergeGraphQLFilters function', () => {
        it('Should merge graphQLFilters correctly', () => {
            const graphQLFilters = [{ _id: { _lt: '11' } }, { category: { _in: ['great', 'good', 'ok'] } }];
            const mergedGraphQLFilter = mergeGraphQLFilters(graphQLFilters);
            const expected = { _and: graphQLFilters };
            expect(mergedGraphQLFilter).toEqual(expected);
        });
        it('Should return undefined if graphQLFilters is empty', () => {
            const graphQLFilters: any[] = [];
            const mergedGraphQLFilter = mergeGraphQLFilters(graphQLFilters);
            expect(mergedGraphQLFilter).toBeUndefined();
        });
        it('Should not duplicate if the condition is the same', () => {
            const graphQLFilters = [{ _id: { _lt: '11' } }, { _id: { _lt: '11' } }];
            const mergedGraphQLFilter = mergeGraphQLFilters(graphQLFilters);
            const expected = { _id: { _lt: '11' } };
            expect(mergedGraphQLFilter).toEqual(expected);
        });
        it('should remove empty filters', () => {
            const filter1 = { id: { _eq: 1 } };
            const filter2 = {};
            const result = mergeGraphQLFilters([filter1, filter2]);
            expect(result).toEqual(filter1);
        });
    });
});
