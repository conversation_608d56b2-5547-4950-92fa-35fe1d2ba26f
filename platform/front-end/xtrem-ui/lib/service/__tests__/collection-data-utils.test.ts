import * as xtremDecimal from '@sage/xtrem-decimal';
import { FieldKey } from '../../component/types';
import { caseInsensitiveSorting, formatValueForSorting, getGroupKey } from '../collection-data-utils';

describe('collection data utils', () => {
    describe('getGroupKey', () => {
        it('deep bound reference', () => {
            expect(getGroupKey({ groupKey: 'a.b.c', type: FieldKey.Reference })).toEqual('a.b._id');
        });

        it('deep bound non reference', () => {
            expect(getGroupKey({ groupKey: 'a.b.c', type: FieldKey.Text })).toEqual('a.b.c');
        });
    });

    describe('formatValueForSorting', () => {
        it('should format simple string value', () => {
            const { value } = formatValueForSorting({ key: 'Hey!' }, 'key', FieldKey.Text);
            expect(value).toEqual('Hey!');
        });

        it('should number', () => {
            const { value } = formatValueForSorting({ key: 1232 }, 'key', FieldKey.Numeric);
            expect(value).toBeInstanceOf(xtremDecimal.Decimal);
            expect(xtremDecimal.eq(xtremDecimal.Decimal.make(1232), value)).toEqual(true);
        });

        it('should decimal', () => {
            const { value } = formatValueForSorting({ key: 1232.32 }, 'key', FieldKey.Numeric);
            expect(value).toBeInstanceOf(xtremDecimal.Decimal);
            expect(xtremDecimal.eq(xtremDecimal.Decimal.make(1232.32), value)).toEqual(true);
        });

        it('should a string decimal', () => {
            const { value } = formatValueForSorting({ key: '1232.32' }, 'key', FieldKey.Numeric);
            expect(value).toBeInstanceOf(xtremDecimal.Decimal);
            expect(xtremDecimal.eq(xtremDecimal.Decimal.make(1232.32), value)).toEqual(true);
        });

        it('should format _sortValue as an integer', () => {
            const { value } = formatValueForSorting({ _sortValue: '10' }, '_sortValue');
            expect(value).toBeInstanceOf(xtremDecimal.Decimal);
            expect(xtremDecimal.eq(xtremDecimal.Decimal.make(10), value)).toEqual(true);
        });

        it('should number looking non-numeric values to string', () => {
            const { value } = formatValueForSorting({ key: '37890-2' }, 'key', FieldKey.Text);
            expect(value).not.toBeInstanceOf(xtremDecimal.Decimal);
            expect(value).toEqual('37890-2');
        });
    });

    describe('caseInsensitiveSorting', () => {
        const obj1 = {
            anIntegerProperty: 4,
            aDecimalProperty: '12.43',
            aStringProperty: 'aaa',
            aReferenceProperty: {
                _id: '23',
                title: 'Test',
                numericReferenceProperty: '1.32',
            },
            aBooleanProperty: true,
            identicalStringProperty: 'hey',
            identicalNumericProperty: '1.2345',
            thisValueOnlyExistHere: 'AAA',
            itMightBeNull: null,
            itMightBeUndefinedNumber: '1.43',
            numberAgainstLetter: 'b',
        };

        const obj2 = {
            anIntegerProperty: 10,
            aDecimalProperty: '2.43',
            aStringProperty: 'BAa',
            aReferenceProperty: {
                _id: '123',
                title: 'Another test',
                numericReferenceProperty: '54.32',
            },
            aBooleanProperty: false,
            identicalStringProperty: 'hey',
            identicalNumericProperty: '1.2345',
            itMightBeNull: 'test',
            numberAgainstLetter: 1,
        };

        const colDefs: any = [
            { properties: { bind: 'anIntegerProperty' }, type: FieldKey.Numeric },
            { properties: { bind: 'aDecimalProperty' }, type: FieldKey.Numeric },
            { properties: { bind: 'aStringProperty' }, type: FieldKey.Text },
            { properties: { bind: 'aReferenceProperty', valueField: 'title' }, type: FieldKey.Reference },
            {
                properties: { bind: 'aReferenceProperty', valueField: 'numericReferenceProperty' },
                type: FieldKey.Reference,
            },
            { properties: { bind: 'aBooleanProperty' }, type: FieldKey.Checkbox },
            { properties: { bind: 'identicalStringProperty' }, type: FieldKey.Text },
            { properties: { bind: 'identicalNumericProperty' }, type: FieldKey.Numeric },
            { properties: { bind: 'thisValueOnlyExistHere' }, type: FieldKey.Text },
            { properties: { bind: 'itMightBeNull' }, type: FieldKey.Text },
            { properties: { bind: 'itMightBeUndefinedNumber' }, type: FieldKey.Numeric },
            { properties: { bind: 'numberAgainstLetter' }, type: FieldKey.Text },
        ];
        it('should compare integer properties', () => {
            expect(caseInsensitiveSorting([['anIntegerProperty', false]], colDefs)(obj1, obj2)).toEqual(-1);
            expect(caseInsensitiveSorting([['anIntegerProperty', true]], colDefs)(obj1, obj2)).toEqual(1);
        });

        it('should compare decimal properties', () => {
            expect(caseInsensitiveSorting([['aDecimalProperty', false]], colDefs)(obj1, obj2)).toEqual(1);
            expect(caseInsensitiveSorting([['aDecimalProperty', true]], colDefs)(obj1, obj2)).toEqual(-1);
        });

        it('should compare string properties', () => {
            expect(caseInsensitiveSorting([['aStringProperty', false]], colDefs)(obj1, obj2)).toEqual(-1);
            expect(caseInsensitiveSorting([['aStringProperty', true]], colDefs)(obj1, obj2)).toEqual(1);
        });

        it('should compare nested string properties', () => {
            expect(caseInsensitiveSorting([['aReferenceProperty.title', false]], colDefs)(obj1, obj2)).toEqual(1);
            expect(caseInsensitiveSorting([['aReferenceProperty.title', true]], colDefs)(obj1, obj2)).toEqual(-1);
        });

        it('should compare nested boolean properties', () => {
            expect(caseInsensitiveSorting([['aBooleanProperty', false]], colDefs)(obj1, obj2)).toEqual(1);
            expect(caseInsensitiveSorting([['aBooleanProperty', true]], colDefs)(obj1, obj2)).toEqual(-1);
        });

        it('should compare nested identical string properties', () => {
            expect(caseInsensitiveSorting([['identicalStringProperty', false]], colDefs)(obj1, obj2)).toEqual(0);
            expect(caseInsensitiveSorting([['identicalStringProperty', true]], colDefs)(obj1, obj2)).toEqual(0);
        });

        it('should compare nested identical decimal properties', () => {
            expect(caseInsensitiveSorting([['identicalNumericProperty', false]], colDefs)(obj1, obj2)).toEqual(0);
            expect(caseInsensitiveSorting([['identicalNumericProperty', true]], colDefs)(obj1, obj2)).toEqual(0);
        });

        it('should compare nested string properties', () => {
            expect(
                caseInsensitiveSorting([['aReferenceProperty.numericReferenceProperty', false]], colDefs)(obj1, obj2),
            ).toEqual(-1);
            expect(
                caseInsensitiveSorting([['aReferenceProperty.numericReferenceProperty', true]], colDefs)(obj1, obj2),
            ).toEqual(1);
        });

        it('should compare an existing string with a non-existing one', () => {
            expect(caseInsensitiveSorting([['thisValueOnlyExistHere', false]], colDefs)(obj1, obj2)).toEqual(1);
            expect(caseInsensitiveSorting([['thisValueOnlyExistHere', true]], colDefs)(obj1, obj2)).toEqual(-1);
        });

        it('should compare an existing string with a null value', () => {
            expect(caseInsensitiveSorting([['itMightBeNull', false]], colDefs)(obj1, obj2)).toEqual(-1);
            expect(caseInsensitiveSorting([['itMightBeNull', true]], colDefs)(obj1, obj2)).toEqual(1);
        });

        it('should compare an existing number with a null value', () => {
            expect(caseInsensitiveSorting([['itMightBeUndefinedNumber', false]], colDefs)(obj1, obj2)).toEqual(1);
            expect(caseInsensitiveSorting([['itMightBeUndefinedNumber', true]], colDefs)(obj1, obj2)).toEqual(-1);
        });

        it('should compare numbers against letters', () => {
            expect(caseInsensitiveSorting([['numberAgainstLetter', false]], colDefs)(obj1, obj2)).toEqual(1);
        });

        it('should handle complex conditions', () => {
            expect(
                caseInsensitiveSorting(
                    [
                        ['identicalStringProperty', false],
                        ['identicalNumericProperty', false],
                        ['aReferenceProperty.numericReferenceProperty', false],
                    ],
                    colDefs,
                )(obj1, obj2),
            ).toEqual(-1);

            expect(
                caseInsensitiveSorting(
                    [
                        ['identicalStringProperty', true],
                        ['identicalNumericProperty', false],
                        ['aReferenceProperty.numericReferenceProperty', true],
                    ],
                    colDefs,
                )(obj1, obj2),
            ).toEqual(1);
        });
    });
});
