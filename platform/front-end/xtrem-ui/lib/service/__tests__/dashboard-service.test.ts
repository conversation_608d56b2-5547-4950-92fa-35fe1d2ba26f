import { applyActionMocks, getMockState, getMockStore } from '../../__tests__/test-helpers';

import type { DropdownItem } from '@sage/bms-dashboard';
import { XLinkType } from '@sage/visual-process-editor';
import type { DashboardItem, Dict } from '@sage/xtrem-shared';
import { waitFor } from '@testing-library/react';
import { WidgetCacheLifespan, WidgetType } from '../../dashboard/widgets/abstract-widget';
import * as xtremRedux from '../../redux';
import * as dashboardService from '../dashboard-service';
import * as graphqlUtils from '../graphql-utils';
import * as i18nService from '../i18n-service';
import * as router from '../router';
import type { WidgetDefinitionWithLoadingState } from '../../dashboard/dashboard-types';

const testDashboardGroup = 'home';

describe('dashboard service', () => {
    let executeGraphqlQueryMock: jest.SpyInstance<Promise<any>, any>;
    let mockState: xtremRedux.XtremAppState;
    let fakeRouter: router.Router;

    beforeEach(() => {
        mockState = getMockState();
        fakeRouter = { goTo: jest.fn() } as unknown as router.Router;
        jest.spyOn(router, 'getRouter').mockReturnValue(fakeRouter);

        executeGraphqlQueryMock = jest.spyOn(graphqlUtils, 'executeGraphqlQuery');
        jest.spyOn(i18nService, 'localize').mockImplementation((_key: string, str: string): string => str);
    });

    afterEach(() => {
        executeGraphqlQueryMock.mockReset();
        applyActionMocks();
    });

    describe('fetchCurrentDashboardDefinition', () => {
        it('should fetch and handler dashboard definition from the server without platform strings', async () => {
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    dashboard: {
                        selectedDashboard: {
                            _id: '2345',
                            title: 'Test dashboard',
                            isTemplate: false,
                            children: [
                                {
                                    _id: '678',
                                    type: '@sage/xtrem-test/MyFirstWidget',
                                    x: 1,
                                    y: 2,
                                    w: 13,
                                    h: 10,
                                    // This property should be parsed
                                    settings: '{"json":"property"}',
                                },
                            ],
                        },
                    },
                },
            });

            const result = await dashboardService.fetchCurrentDashboardDefinition(testDashboardGroup);
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                endpoint: '/metadata',
                query: {
                    dashboard: {
                        availableDashboards: {
                            __args: {
                                group: 'home',
                            },
                            _id: true,
                            title: true,
                        },
                        selectedDashboard: {
                            __args: {
                                group: 'home',
                            },
                            _id: true,
                            isTemplate: true,
                            children: {
                                _id: true,
                                settings: true,
                                type: true,
                                positions: {
                                    breakpoint: true,
                                    h: true,
                                    w: true,
                                    x: true,
                                    y: true,
                                },
                            },
                            title: true,
                        },
                        canEditDashboards: true,
                    },
                },
            });
            expect(result).toEqual({
                dashboard: {
                    _id: '2345',
                    title: 'Test dashboard',
                    isTemplate: false,
                    children: [
                        {
                            _id: '678',
                            type: '@sage/xtrem-test/MyFirstWidget',
                            x: 1,
                            y: 2,
                            w: 13,
                            h: 10,
                            settings: {
                                json: 'property',
                            },
                        },
                    ],
                },
                stringLiterals: {},
            });
        });
        it('should fetch and handler dashboard definition from the server with platform strings', async () => {
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    dashboard: {
                        selectedDashboard: {
                            _id: '2345',
                            title: 'Test dashboard',
                            isTemplate: true,
                            children: [
                                {
                                    _id: '678',
                                    type: '@sage/xtrem-test/MyFirstWidget',
                                    x: 1,
                                    y: 2,
                                    w: 13,
                                    h: 10,
                                    // This property should be parsed
                                    settings: '{"json":"property"}',
                                },
                            ],
                        },
                    },
                    strings: [
                        { key: '@sage/xtrem-ui/some-string', content: 'Some string' },
                        { key: '@sage/xtrem-ui/some-other-string', content: 'Some other string' },
                    ],
                },
            });

            const result = await dashboardService.fetchCurrentDashboardDefinition(testDashboardGroup, true);
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                endpoint: '/metadata',
                query: {
                    strings: {
                        __args: {
                            filter: {
                                packageOrPage:
                                    '@sage/xtrem-ui,@sage/xtrem-date-time,@sage/xtrem-ui-components,@sage/xtrem-document-editor',
                            },
                        },
                        content: true,
                        key: true,
                    },
                    dashboard: {
                        availableDashboards: {
                            _id: true,
                            title: true,
                            __args: {
                                group: 'home',
                            },
                        },
                        selectedDashboard: {
                            __args: {
                                group: 'home',
                            },
                            _id: true,
                            isTemplate: true,
                            children: {
                                _id: true,
                                settings: true,
                                type: true,
                                positions: {
                                    breakpoint: true,
                                    h: true,
                                    w: true,
                                    x: true,
                                    y: true,
                                },
                            },
                            title: true,
                        },
                        canEditDashboards: true,
                    },
                },
            });
            expect(result).toEqual({
                dashboard: {
                    _id: '2345',
                    title: 'Test dashboard',
                    isTemplate: true,
                    children: [
                        {
                            _id: '678',
                            type: '@sage/xtrem-test/MyFirstWidget',
                            x: 1,
                            y: 2,
                            w: 13,
                            h: 10,
                            settings: {
                                json: 'property',
                            },
                        },
                    ],
                },
                stringLiterals: {
                    '@sage/xtrem-ui/some-other-string': 'Some other string',
                    '@sage/xtrem-ui/some-string': 'Some string',
                },
            });
        });
        it('Should sort dashboard childrens by Y and X', async () => {
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    dashboard: {
                        selectedDashboard: {
                            _id: '2345',
                            title: 'Test dashboard',
                            children: [
                                {
                                    _id: '1',
                                    type: '@sage/xtrem-test/MyFirstWidget',
                                    x: 1,
                                    y: 2,
                                    w: 13,
                                    h: 10,
                                    // This property should be parsed
                                    settings: '{"json":"property"}',
                                },
                                {
                                    _id: '2',
                                    type: '@sage/xtrem-test/MyFirstWidget',
                                    x: 4,
                                    y: 2,
                                    w: 13,
                                    h: 10,
                                    // This property should be parsed
                                    settings: '{"json":"property"}',
                                },
                                {
                                    _id: '3',
                                    type: '@sage/xtrem-test/MyFirstWidget',
                                    x: 1,
                                    y: 3,
                                    w: 13,
                                    h: 10,
                                    // This property should be parsed
                                    settings: '{"json":"property"}',
                                },
                                {
                                    _id: '4',
                                    type: '@sage/xtrem-test/MyFirstWidget',
                                    x: 1,
                                    y: 5,
                                    w: 13,
                                    h: 10,
                                    // This property should be parsed
                                    settings: '{"json":"property"}',
                                },
                                {
                                    _id: '5',
                                    type: '@sage/xtrem-test/MyFirstWidget',
                                    x: 2,
                                    y: 2,
                                    w: 13,
                                    h: 10,
                                    // This property should be parsed
                                    settings: '{"json":"property"}',
                                },
                            ],
                        },
                    },
                },
            });

            const result = await dashboardService.fetchCurrentDashboardDefinition(testDashboardGroup);
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                endpoint: '/metadata',
                query: {
                    dashboard: {
                        availableDashboards: {
                            __args: {
                                group: 'home',
                            },
                            _id: true,
                            title: true,
                        },
                        selectedDashboard: {
                            __args: {
                                group: 'home',
                            },
                            _id: true,
                            isTemplate: true,
                            children: {
                                _id: true,
                                settings: true,
                                type: true,
                                positions: {
                                    breakpoint: true,
                                    h: true,
                                    w: true,
                                    x: true,
                                    y: true,
                                },
                            },
                            title: true,
                        },
                        canEditDashboards: true,
                    },
                },
            });
            expect(result).toEqual({
                dashboard: {
                    _id: '2345',
                    title: 'Test dashboard',
                    children: [
                        {
                            _id: '1',
                            type: '@sage/xtrem-test/MyFirstWidget',
                            x: 1,
                            y: 2,
                            w: 13,
                            h: 10,
                            // This property should be parsed
                            settings: {
                                json: 'property',
                            },
                        },
                        {
                            _id: '5',
                            type: '@sage/xtrem-test/MyFirstWidget',
                            x: 2,
                            y: 2,
                            w: 13,
                            h: 10,
                            // This property should be parsed
                            settings: {
                                json: 'property',
                            },
                        },
                        {
                            _id: '2',
                            type: '@sage/xtrem-test/MyFirstWidget',
                            x: 4,
                            y: 2,
                            w: 13,
                            h: 10,
                            // This property should be parsed
                            settings: {
                                json: 'property',
                            },
                        },
                        {
                            _id: '3',
                            type: '@sage/xtrem-test/MyFirstWidget',
                            x: 1,
                            y: 3,
                            w: 13,
                            h: 10,
                            // This property should be parsed
                            settings: {
                                json: 'property',
                            },
                        },
                        {
                            _id: '4',
                            type: '@sage/xtrem-test/MyFirstWidget',
                            x: 1,
                            y: 5,
                            w: 13,
                            h: 10,
                            // This property should be parsed
                            settings: {
                                json: 'property',
                            },
                        },
                    ],
                },
                stringLiterals: {},
            });
        });
    });

    describe('getWidgetsFromDashboardItems', () => {
        beforeEach(() => {
            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards = {
                '12': {
                    _id: '12',
                    title: 'A fake dashboard',
                    isSelected: true,
                    children: [],
                },
            };
        });

        it('should display a waning if the widget type is not supported', () => {
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [
                    {
                        _id: '1',
                        positions: [
                            { x: 0, y: 0, breakpoint: 'xxs', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'xs', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'sm', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'md', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'lg', w: 0, h: 0 },
                        ],
                        settings: { some: 'settings' },
                        type: '@sage/xtrem-test/MyTestWidget',
                    },
                ],
                widgets: {
                    '1': {
                        _id: '1',
                        widgetObject: {} as any,
                        artifactName: '@sage/xtrem-test/MyTestWidget',
                        widgetType: 'unsupported type' as any,
                        data: { some: { real: { data: 123 } } },
                        properties: {
                            getQuery: () => ({
                                test: true,
                            }),
                            title: 'title',
                        },
                        isLoading: false,
                    },
                },
                isEditing: false,
                breakpoint: 'md',
            });

            expect(bmsWidgetDefinition).toEqual({
                dropdown: {
                    items: [{ i: 'refresh', icon: 'refresh', label: 'Refresh', onClick: expect.any(Function) }],
                },
                i: '1',
                title: 'title',
                type: 'basic',
                isLoading: false,
                xxs: { x: 0, y: 0, w: 0, h: 0 },
                xs: { x: 0, y: 0, w: 0, h: 0 },
                sm: { x: 0, y: 0, w: 0, h: 0 },
                md: { x: 0, y: 0, w: 0, h: 0 },
                lg: { x: 0, y: 0, w: 0, h: 0 },
                data: {
                    htmlContent: '<div>Unknown widget: unsupported type</div>',
                },
            });
        });

        it('should generate an preview dashboard widget if no data is set', () => {
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [
                    {
                        _id: '1',
                        positions: [
                            { x: 0, y: 0, breakpoint: 'xxs', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'xs', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'sm', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'md', w: 0, h: 0 },
                            { x: 0, y: 0, breakpoint: 'lg', w: 0, h: 0 },
                        ],
                        settings: { some: 'settings' },
                        type: '@sage/xtrem-test/MyTestWidget',
                    },
                ],
                widgets: {
                    '1': {
                        _id: '1',
                        widgetObject: {} as any,
                        artifactName: '@sage/xtrem-test/MyTestWidget',
                        widgetType: WidgetType.indicatorTile,
                        properties: {
                            getQuery: () => ({
                                test: true,
                            }),
                            title: 'title',
                        },
                        isLoading: false,
                    },
                },
                breakpoint: 'md',
            });

            expect(bmsWidgetDefinition).toEqual({
                data: {},
                i: '1',
                type: 'preview',
                isLoading: false,
                xxs: { x: 0, y: 0, w: 0, h: 0 },
                xs: { x: 0, y: 0, w: 0, h: 0 },
                sm: { x: 0, y: 0, w: 0, h: 0 },
                md: { x: 0, y: 0, w: 0, h: 0 },
                lg: { x: 0, y: 0, w: 0, h: 0 },
            });
        });

        it('should call refresh action when the Refresh button is triggered', () => {
            const dashboardWidgetItem = {
                _id: '1',
                positions: [
                    { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                ],
                settings: { some: 'settings' },
                type: '@sage/xtrem-test/MyTestWidget',
            };

            const loadedWidgets = {
                '1': {
                    _id: '1',
                    widgetObject: {} as any,
                    artifactName: '@sage/xtrem-test/MyTestWidget',
                    data: { some: { Data: true } },
                    widgetType: WidgetType.indicatorTile,
                    properties: {
                        getQuery: () => ({
                            test: true,
                        }),
                        title: () => 'function title',
                        color: '#00AA00',
                        value: () => 4 + 6,
                    } as any,
                    isLoading: false,
                },
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
            getMockStore(mockState);
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [dashboardWidgetItem],
                widgets: loadedWidgets,
                isEditing: false,
                breakpoint: 'md',
            });

            expect(xtremRedux.actions.loadWidgetData).not.toHaveBeenCalled();

            (bmsWidgetDefinition.dropdown!.items![0] as DropdownItem)!.onClick!();

            expect(xtremRedux.actions.loadWidgetData).toHaveBeenCalled();
        });

        it('should generate an tile indicator definition', () => {
            const dashboardWidgetItem = {
                _id: '1',
                positions: [
                    { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                ],
                settings: { some: 'settings' },
                type: '@sage/xtrem-test/MyTestWidget',
            };

            const loadedWidgets = {
                '1': {
                    _id: '1',
                    widgetObject: {} as any,
                    artifactName: '@sage/xtrem-test/MyTestWidget',
                    data: { some: { Data: true } },
                    widgetType: WidgetType.indicatorTile,
                    properties: {
                        getQuery: () => ({
                            test: true,
                        }),
                        title: () => 'function title',
                        color: '#00AA00',
                        value: () => 4 + 6,
                    } as any,
                    isLoading: false,
                },
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
            getMockStore(mockState);
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [dashboardWidgetItem],
                widgets: loadedWidgets,
                isEditing: false,
                breakpoint: 'md',
            });

            expect(bmsWidgetDefinition).toEqual({
                i: '1',
                type: 'simple-indicator',
                title: undefined,
                xxs: { x: 0, y: 0, w: 0, h: 0 },
                xs: { x: 0, y: 0, w: 0, h: 0 },
                sm: { x: 0, y: 0, w: 0, h: 0 },
                md: { x: 0, y: 0, w: 0, h: 0 },
                lg: { x: 0, y: 0, w: 0, h: 0 },
                isLoading: false,
                dropdown: {
                    items: [{ i: 'refresh', icon: 'refresh', label: 'Refresh', onClick: expect.any(Function) }],
                },
                data: {
                    type: 'text',
                    description: undefined,
                    icon: undefined,
                    mainIndicator: {
                        title: '10',
                        subtitle: 'function title',
                    },
                },
            });
        });

        it('should generate a gauge definition', () => {
            const dashboardWidgetItem = {
                _id: '1',
                positions: [
                    { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                ],
                settings: { some: 'settings' },
                type: '@sage/xtrem-test/MyTestWidget',
            };

            const loadedWidgets = {
                '1': {
                    _id: '1',
                    widgetObject: {
                        $: {
                            data: { some: { max: 4, current: 2 } },
                        },
                    } as any,
                    artifactName: '@sage/xtrem-test/MyTestWidget',
                    data: { some: { max: 4, current: 2 } },
                    widgetType: WidgetType.gauge,
                    properties: {
                        getQuery: () => ({
                            test: true,
                        }),
                        title: () => 'function title',
                        color: '#00AA00',
                        content() {
                            return {
                                value: this.$.data.some.current,
                                totalValue: this.$.data.some.max,
                            };
                        },
                        scale: 4,
                        evolutionUnit: 'kg',
                        valueUnit: '*',
                    } as any,
                    isLoading: false,
                },
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
            getMockStore(mockState);
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [dashboardWidgetItem],
                widgets: loadedWidgets,
                isEditing: false,
                breakpoint: 'md',
            });

            expect(bmsWidgetDefinition).toEqual({
                i: '1',
                type: 'gauge',
                xxs: { x: 0, y: 0, w: 0, h: 0 },
                xs: { x: 0, y: 0, w: 0, h: 0 },
                sm: { x: 0, y: 0, w: 0, h: 0 },
                md: { x: 0, y: 0, w: 0, h: 0 },
                lg: { x: 0, y: 0, w: 0, h: 0 },
                isLoading: false,
                dropdown: {
                    items: [{ i: 'refresh', icon: 'refresh', label: 'Refresh', onClick: expect.any(Function) }],
                },
                title: 'function title',
                data: {
                    data: {
                        totalValue: 4,
                        value: 2,
                    },
                    evolutionUnit: 'kg',
                    scale: 4,
                    valueUnit: '*',
                    color: '#00AA00',
                },
            });
        });

        it('should add only "refresh" header action if user not authorized to edit dashboards', () => {
            const dashboardWidgetItem = {
                _id: '1',
                positions: [
                    { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                ],
                settings: { some: 'settings' },
                type: '@sage/xtrem-test/MyTestWidget',
            };

            const loadedWidgets = {
                '1': {
                    _id: '1',
                    widgetObject: {} as any,
                    artifactName: '@sage/xtrem-test/MyTestWidget',
                    data: { some: { Data: true } },
                    widgetType: WidgetType.indicatorTile,
                    properties: {
                        getQuery: () => ({
                            test: true,
                        }),
                        title: () => 'function title',
                        color: '#00AA00',
                        value: () => 4 + 6,
                    } as any,
                    isLoading: false,
                },
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
            getMockStore(mockState);
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [dashboardWidgetItem],
                widgets: loadedWidgets,
                isEditing: true,
                onCloseCallback: undefined,
                canEditDashboards: false,
                breakpoint: 'md',
            });

            expect(bmsWidgetDefinition).toEqual({
                i: '1',
                type: 'simple-indicator',
                xxs: { x: 0, y: 0, w: 0, h: 0 },
                xs: { x: 0, y: 0, w: 0, h: 0 },
                sm: { x: 0, y: 0, w: 0, h: 0 },
                md: { x: 0, y: 0, w: 0, h: 0 },
                lg: { x: 0, y: 0, w: 0, h: 0 },
                isLoading: false,
                title: undefined,
                dropdown: {
                    items: [],
                },
                data: {
                    type: 'text',
                    description: undefined,
                    icon: undefined,
                    mainIndicator: {
                        title: '10',
                        subtitle: 'function title',
                    },
                },
            });
        });

        it('should default actions if user is authorized to edit dashboards', () => {
            const dashboardWidgetItem = {
                _id: '1',
                positions: [
                    { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                ],
                settings: { some: 'settings' },
                type: '@sage/xtrem-test/MyTestWidget',
            };

            const loadedWidgets = {
                '1': {
                    _id: '1',
                    widgetObject: {} as any,
                    artifactName: '@sage/xtrem-test/MyTestWidget',
                    data: { some: { Data: true } },
                    widgetType: WidgetType.indicatorTile,
                    properties: {
                        getQuery: () => ({
                            test: true,
                        }),
                        title: () => 'function title',
                        color: '#00AA00',
                        value: () => 4 + 6,
                    } as any,
                    isLoading: false,
                },
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
            getMockStore(mockState);
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [dashboardWidgetItem],
                widgets: loadedWidgets,
                isEditing: true,
                onCloseCallback: undefined,
                canEditDashboards: true,
                breakpoint: 'md',
            });

            expect(bmsWidgetDefinition).toEqual({
                i: '1',
                type: 'simple-indicator',
                xxs: { x: 0, y: 0, w: 0, h: 0 },
                xs: { x: 0, y: 0, w: 0, h: 0 },
                sm: { x: 0, y: 0, w: 0, h: 0 },
                md: { x: 0, y: 0, w: 0, h: 0 },
                lg: { x: 0, y: 0, w: 0, h: 0 },
                description: undefined,
                icon: undefined,
                title: undefined,
                isLoading: false,
                dropdown: {
                    items: [
                        {
                            i: 'close',
                            icon: 'cross',
                            label: 'Close',
                        },
                    ],
                },
                data: {
                    type: 'text',
                    mainIndicator: {
                        title: '10',
                        subtitle: 'function title',
                    },
                },
            });
        });

        describe('visual process', () => {
            let dashboardWidgetItem: DashboardItem;
            let loadedWidgets: Dict<WidgetDefinitionWithLoadingState>;

            beforeEach(() => {
                dashboardWidgetItem = {
                    _id: '1',
                    positions: [
                        { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                    ],
                    settings: { some: 'settings' },
                    type: '@sage/xtrem-test/MyVisualProcessWidget',
                };

                loadedWidgets = {
                    '1': {
                        _id: '1',
                        widgetObject: {} as any,
                        artifactName: '@sage/xtrem-test/MyVisualProcessWidget',
                        data: { some: { Data: true } },
                        widgetType: WidgetType.visualProcess,
                        properties: {
                            content: { visual: 'Process content' },
                            title: 'Test Title',
                        } as any,
                        isLoading: false,
                    },
                };

                mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [
                    dashboardWidgetItem,
                ];
                mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
                getMockStore(mockState);
            });

            it('should generate an basic widget definition for a visual process widget', () => {
                const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                    dashboardId: '12345',
                    group: testDashboardGroup,
                    children: [dashboardWidgetItem],
                    widgets: loadedWidgets,
                    isEditing: false,
                    breakpoint: 'md',
                });

                expect(bmsWidgetDefinition).toEqual({
                    i: '1',
                    type: 'visual-process',
                    xxs: { x: 0, y: 0, w: 0, h: 0 },
                    xs: { x: 0, y: 0, w: 0, h: 0 },
                    sm: { x: 0, y: 0, w: 0, h: 0 },
                    md: { x: 0, y: 0, w: 0, h: 0 },
                    lg: { x: 0, y: 0, w: 0, h: 0 },
                    isLoading: false,
                    title: 'Test Title',
                    dropdown: {
                        items: [{ i: 'refresh', icon: 'refresh', label: 'Refresh', onClick: expect.any(Function) }],
                    },
                    data: {
                        onLinkClick: expect.any(Function),
                        resolveImageUrl: expect.any(Function),
                        value: {
                            visual: 'Process content',
                        },
                    },
                });
            });

            it('should call the router when the onLinkClick is triggered from a visual process', () => {
                const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                    dashboardId: '12345',
                    group: testDashboardGroup,
                    children: [dashboardWidgetItem],
                    widgets: loadedWidgets,
                    breakpoint: 'md',
                });

                expect(bmsWidgetDefinition.data.onLinkClick).toBeInstanceOf(Function);
                expect(fakeRouter.goTo).not.toHaveBeenCalled();
                bmsWidgetDefinition.data.onLinkClick({
                    xtype: XLinkType.URL,
                    xcode: '@sage/some-package/PageName',
                    xparam1: '124',
                });
                expect(fakeRouter.goTo).toHaveBeenCalledWith('@sage/some-package/PageName', { _id: '124' });
            });

            it('should resolve the url of static resources correctly', () => {
                const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                    dashboardId: '12345',
                    group: testDashboardGroup,
                    children: [dashboardWidgetItem],
                    widgets: loadedWidgets,
                    isEditing: false,
                    breakpoint: 'md',
                });

                expect(bmsWidgetDefinition.data.resolveImageUrl).toBeInstanceOf(Function);
                expect(fakeRouter.goTo).not.toHaveBeenCalled();
                expect(bmsWidgetDefinition.data.resolveImageUrl('colId', 'resId')).toEqual(
                    '/images/vp/colId/resId.svg',
                );
            });
        });

        it('should generate an basic widget definition for a static content widget', () => {
            const dashboardWidgetItem = {
                _id: '1',
                positions: [
                    { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                ],
                settings: { some: 'settings' },
                type: '@sage/xtrem-test/MyTestWidget',
            };

            const loadedWidgets = {
                '1': {
                    _id: '1',
                    widgetObject: {} as any,
                    artifactName: '@sage/xtrem-test/MyTestWidget',
                    data: { some: { Data: true } },
                    widgetType: WidgetType.staticContent,
                    properties: {
                        getQuery: () => ({
                            test: true,
                        }),
                        title: () => 'function title',
                        content: '*md content*',
                    } as any,
                    isLoading: false,
                },
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
            getMockStore(mockState);
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [dashboardWidgetItem],
                widgets: loadedWidgets,
                isEditing: false,
                breakpoint: 'md',
            });

            expect(bmsWidgetDefinition).toEqual({
                i: '1',
                type: 'basic',
                xxs: { x: 0, y: 0, w: 0, h: 0 },
                xs: { x: 0, y: 0, w: 0, h: 0 },
                sm: { x: 0, y: 0, w: 0, h: 0 },
                md: { x: 0, y: 0, w: 0, h: 0 },
                lg: { x: 0, y: 0, w: 0, h: 0 },
                isLoading: false,
                title: 'function title',
                dropdown: {
                    items: [{ i: 'refresh', icon: 'refresh', label: 'Refresh', onClick: expect.any(Function) }],
                },
                data: { htmlContent: '<div style="font-size: 14px"><p><em>md content</em></p></div>' },
            });
        });

        it("should not pass on any HTML tags from the content to the basic widget's HTML content ", () => {
            const dashboardWidgetItem = {
                _id: '1',
                positions: [
                    { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                ],
                settings: { some: 'settings' },
                type: '@sage/xtrem-test/MyTestWidget',
            };

            const loadedWidgets = {
                '1': {
                    _id: '1',
                    widgetObject: {} as any,
                    artifactName: '@sage/xtrem-test/MyTestWidget',
                    data: { some: { Data: true } },
                    widgetType: WidgetType.staticContent,
                    properties: {
                        getQuery: () => ({
                            test: true,
                        }),
                        title: () => 'function title',
                        content: '*md content with some dangerous <iframe src="https://dangerous.site.com" />*',
                    } as any,
                    isLoading: false,
                },
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
            getMockStore(mockState);
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [dashboardWidgetItem],
                widgets: loadedWidgets,
                isEditing: false,
                breakpoint: 'md',
            });

            expect(bmsWidgetDefinition).toEqual({
                i: '1',
                type: 'basic',
                xxs: { x: 0, y: 0, w: 0, h: 0 },
                xs: { x: 0, y: 0, w: 0, h: 0 },
                sm: { x: 0, y: 0, w: 0, h: 0 },
                md: { x: 0, y: 0, w: 0, h: 0 },
                lg: { x: 0, y: 0, w: 0, h: 0 },
                isLoading: false,
                title: 'function title',
                dropdown: {
                    items: [{ i: 'refresh', icon: 'refresh', label: 'Refresh', onClick: expect.any(Function) }],
                },
                data: {
                    htmlContent:
                        '<div style="font-size: 14px"><p><em>md content with some dangerous &lt;iframe src=&quot;https://dangerous.site.com&quot; /&gt;</em></p></div>',
                },
            });
        });

        it('should add a dropdown items if the widget has settings page in edit mode', () => {
            const dashboardWidgetItem = {
                _id: '1',
                positions: [
                    { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                ],
                settings: { some: 'settings' },
                type: '@sage/xtrem-test/MyTestWidget',
            };

            const loadedWidgets = {
                '1': {
                    _id: '1',
                    widgetObject: {} as any,
                    artifactName: '@sage/xtrem-test/MyTestWidget',
                    data: { some: { Data: true } },
                    widgetType: WidgetType.indicatorTile,
                    properties: {
                        getQuery: () => ({
                            test: true,
                        }),
                        settingsPage: '@sage/xtrem-test/MyWidgetSettingsPage',
                        title: () => 'function title',
                        color: '#00AA00',
                        value: () => 4 + 6,
                    } as any,
                    isLoading: false,
                },
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
            getMockStore(mockState);
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [dashboardWidgetItem],
                widgets: loadedWidgets,
                isEditing: true,
                breakpoint: 'md',
            });

            expect(bmsWidgetDefinition).toEqual({
                i: '1',
                type: 'simple-indicator',
                xxs: { x: 0, y: 0, w: 0, h: 0 },
                xs: { x: 0, y: 0, w: 0, h: 0 },
                sm: { x: 0, y: 0, w: 0, h: 0 },
                md: { x: 0, y: 0, w: 0, h: 0 },
                lg: { x: 0, y: 0, w: 0, h: 0 },
                description: undefined,
                icon: undefined,
                isLoading: false,
                title: undefined,
                dropdown: {
                    items: [
                        {
                            onClick: expect.any(Function),
                            i: 'settings',
                            icon: 'settings',
                            label: 'Settings',
                        },
                        {
                            i: 'close-separator',
                            isDivider: true,
                        },
                        {
                            i: 'close',
                            icon: 'cross',
                            label: 'Close',
                            onClick: undefined,
                        },
                    ],
                },
                data: {
                    type: 'text',
                    mainIndicator: {
                        title: '10',
                        subtitle: 'function title',
                    },
                },
            });
        });

        it('should open the settings page when the user clicks the settings option in edit mode', async () => {
            const dashboardWidgetItem = {
                _id: '1',
                positions: [
                    { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                ],
                settings: { some: 'settings' },
                type: '@sage/xtrem-test/MyTestWidget',
            };
            const pageDialogMock = jest.fn().mockResolvedValue({ newSetting: true });

            const loadedWidgets = {
                '1': {
                    _id: '1',
                    widgetObject: { $: { dialog: { page: pageDialogMock } } } as any,
                    artifactName: '@sage/xtrem-test/MyTestWidget',
                    data: { some: { Data: true } },
                    widgetType: WidgetType.indicatorTile,
                    properties: {
                        getQuery: () => ({
                            test: true,
                        }),
                        settingsPage: '@sage/xtrem-test/MyWidgetSettingsPage',
                        title: () => 'function title',
                        color: '#00AA00',
                        value: () => 4 + 6,
                    } as any,
                    isLoading: false,
                },
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[
                testDashboardGroup
            ].dashboardEditor.currentDashboardDefinition.children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
            getMockStore(mockState);
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [dashboardWidgetItem],
                widgets: loadedWidgets,
                isEditing: true,
                breakpoint: 'md',
            });

            expect(pageDialogMock).not.toHaveBeenCalled();
            expect(xtremRedux.actions.loadWidgetData).not.toHaveBeenCalled();

            (bmsWidgetDefinition.dropdown!.items![0] as DropdownItem)!.onClick!();
            expect(pageDialogMock).toHaveBeenCalledWith(
                '@sage/xtrem-test/MyWidgetSettingsPage',
                { some: 'settings' },
                {
                    rightAligned: true,
                },
            );

            await waitFor(() => {
                expect(xtremRedux.actions.updateWidgetSettings).toHaveBeenCalledWith('1', 'home', { newSetting: true });
            });
        });

        it('should add a dropdown items if the widget defines custom header actions', () => {
            const dashboardWidgetItem = {
                _id: '1',
                positions: [
                    { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                ],
                settings: { some: 'settings' },
                type: '@sage/xtrem-test/MyTestWidget',
            };

            const loadedWidgets = {
                '1': {
                    _id: '1',
                    widgetObject: {} as any,
                    artifactName: '@sage/xtrem-test/MyTestWidget',
                    data: { some: { Data: true } },
                    widgetType: WidgetType.indicatorTile,
                    properties: {
                        getQuery: () => ({
                            test: true,
                        }),
                        title: () => 'function title',
                        headerActions: [
                            {
                                title: 'Test',
                                icon: 'plus',
                                onClick: jest.fn(),
                            },
                        ],
                        color: '#00AA00',
                        value: () => 4 + 6,
                    } as any,
                    isLoading: false,
                },
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
            getMockStore(mockState);
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [dashboardWidgetItem],
                widgets: loadedWidgets,
                isEditing: false,
                breakpoint: 'md',
            });

            expect(bmsWidgetDefinition).toEqual({
                i: '1',
                type: 'simple-indicator',
                xxs: { x: 0, y: 0, w: 0, h: 0 },
                xs: { x: 0, y: 0, w: 0, h: 0 },
                sm: { x: 0, y: 0, w: 0, h: 0 },
                md: { x: 0, y: 0, w: 0, h: 0 },
                lg: { x: 0, y: 0, w: 0, h: 0 },
                description: undefined,
                icon: undefined,
                title: undefined,
                isLoading: false,
                dropdown: {
                    items: [
                        {
                            onClick: expect.any(Function),
                            i: 'Test-plus',
                            icon: 'plus',
                            label: 'Test',
                        },
                        {
                            i: 'header-actions-separator',
                            isDivider: true,
                        },
                        { i: 'refresh', icon: 'refresh', label: 'Refresh', onClick: expect.any(Function) },
                    ],
                },
                data: {
                    mainIndicator: {
                        title: '10',
                        subtitle: 'function title',
                    },
                    type: 'text',
                },
            });
        });

        it('should add a dropdown items if the widget in edit mode and is a custom widget', () => {
            const dashboardWidgetItem = {
                _id: '1',
                positions: [
                    { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                ],
                settings: { some: 'settings' },
                type: '@sage/xtrem-ui/MyTestWidget',
            };

            const loadedWidgets = {
                '1': {
                    _id: '1',
                    widgetObject: {} as any,
                    artifactName: '@sage/xtrem-ui/MyTestWidget',
                    data: { some: { Data: true } },
                    widgetType: WidgetType.indicatorTile,
                    properties: {
                        getQuery: () => ({
                            test: true,
                        }),
                        title: () => 'function title',
                        color: '#00AA00',
                        value: () => 4 + 6,
                    } as any,
                    isLoading: false,
                },
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
            getMockStore(mockState);
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [dashboardWidgetItem],
                widgets: loadedWidgets,
                isEditing: true,
                breakpoint: 'md',
            });

            expect(bmsWidgetDefinition).toEqual({
                i: '1',
                type: 'simple-indicator',
                xxs: { x: 0, y: 0, w: 0, h: 0 },
                xs: { x: 0, y: 0, w: 0, h: 0 },
                sm: { x: 0, y: 0, w: 0, h: 0 },
                md: { x: 0, y: 0, w: 0, h: 0 },
                lg: { x: 0, y: 0, w: 0, h: 0 },
                description: undefined,
                icon: undefined,
                title: undefined,
                isLoading: false,
                dropdown: {
                    items: [
                        {
                            onClick: expect.any(Function),
                            i: 'edit',
                            icon: 'settings',
                            label: 'Edit',
                        },
                        {
                            i: 'close-separator',
                            isDivider: true,
                        },
                        {
                            i: 'close',
                            icon: 'cross',
                            label: 'Close',
                            onClick: undefined,
                        },
                    ],
                },
                data: {
                    type: 'text',
                    mainIndicator: {
                        title: '10',
                        subtitle: 'function title',
                    },
                },
            });
        });

        it('should open the widget edit dialog for custom widgets when the user clicks the edit option in edit mode', async () => {
            const dashboardWidgetItem = {
                _id: '1',
                positions: [
                    { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                    { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                ],
                settings: { some: 'settings' },
                type: '@sage/xtrem-ui/MyTestWidget',
            };
            const pageDialogMock = jest.fn().mockResolvedValue({ newSetting: true });

            const loadedWidgets = {
                '1': {
                    _id: '1',
                    widgetObject: { $: { dialog: { page: pageDialogMock }, settings: { some: 'settings' } } } as any,
                    artifactName: '@sage/xtrem-ui/MyTestWidget',
                    data: { some: { Data: true } },
                    widgetType: WidgetType.indicatorTile,
                    properties: {
                        getQuery: () => ({
                            test: true,
                        }),
                        title: () => 'function title',
                        color: '#00AA00',
                        value: () => 4 + 6,
                    } as any,
                    isLoading: false,
                },
            };

            mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards['12'].children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[
                testDashboardGroup
            ].dashboardEditor.currentDashboardDefinition.children = [dashboardWidgetItem];
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
            getMockStore(mockState);
            const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                dashboardId: '12345',
                group: testDashboardGroup,
                children: [dashboardWidgetItem],
                widgets: loadedWidgets,
                isEditing: true,
                breakpoint: 'md',
            });

            expect(pageDialogMock).not.toHaveBeenCalled();
            (bmsWidgetDefinition.dropdown!.items![0] as DropdownItem)!.onClick!();
            await waitFor(() => {
                expect(xtremRedux.actions.openWidgetEditorDialog).toHaveBeenCalledWith('home', '1', {
                    some: 'settings',
                });
            });
        });

        describe('call to action configuration', () => {
            const dashboardId = '12';
            const widgetId = '1';
            let dashboardWidgetItem: DashboardItem;
            let loadedWidgets: Dict<WidgetDefinitionWithLoadingState>;

            const widgetContext: any = {
                a: 4,
                b: 3,
            };

            beforeEach(() => {
                dashboardWidgetItem = {
                    _id: widgetId,
                    positions: [
                        { x: 0, y: 0, breakpoint: 'xxs' as const, w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'xs' as const, w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'sm' as const, w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'md' as const, w: 0, h: 0 },
                        { x: 0, y: 0, breakpoint: 'lg' as const, w: 0, h: 0 },
                    ],
                    settings: { some: 'settings' },
                    type: '@sage/xtrem-test/MyTestWidget',
                };

                loadedWidgets = {
                    [widgetId]: {
                        _id: widgetId,
                        widgetObject: widgetContext,
                        artifactName: '@sage/xtrem-test/MyTestWidget',
                        data: { some: { Data: true } },
                        widgetType: WidgetType.indicatorTile,
                        properties: {
                            getQuery: () => ({
                                test: true,
                            }),
                            title: () => 'function title',
                            color: '#00AA00',
                            value: () => 4 + 6,
                        } as any,
                        isLoading: false,
                    },
                };

                mockState.dashboard.dashboardGroups[testDashboardGroup].dashboards[dashboardId].children = [
                    dashboardWidgetItem,
                ];
                mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = loadedWidgets;
                getMockStore(mockState);
            });

            it('should render with hardcoded title', () => {
                const callToAction = {
                    test: {
                        title: 'Test',
                        onClick: jest.fn(),
                    },
                };

                mockState.dashboard.dashboardGroups[testDashboardGroup].widgets[widgetId].properties.callToActions =
                    callToAction;
                loadedWidgets[widgetId].properties.callToActions = callToAction;
                getMockStore(mockState);

                const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                    dashboardId,
                    group: testDashboardGroup,
                    children: [dashboardWidgetItem],
                    widgets: loadedWidgets,
                    breakpoint: 'md',
                });

                expect(bmsWidgetDefinition.callToActions!.test.title).toEqual('Test');
            });

            it('should render with callback title based on widget context', () => {
                const callToAction = {
                    test: {
                        title(this: any) {
                            return `Test ${this.a}`;
                        },
                        onClick: jest.fn(),
                    },
                };

                mockState.dashboard.dashboardGroups[testDashboardGroup].widgets[widgetId].properties.callToActions =
                    callToAction;
                loadedWidgets[widgetId].properties.callToActions = callToAction;
                getMockStore(mockState);

                const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                    dashboardId,
                    group: testDashboardGroup,
                    children: [dashboardWidgetItem],
                    widgets: loadedWidgets,
                    breakpoint: 'md',
                });

                expect(bmsWidgetDefinition.callToActions!.test.title).toEqual('Test 4');
            });

            it('should pass on the isDisabled property', () => {
                const callToAction = {
                    test: {
                        title: 'Test',
                        onClick: jest.fn(),
                        isDisabled: true,
                    },
                };

                mockState.dashboard.dashboardGroups[testDashboardGroup].widgets[widgetId].properties.callToActions =
                    callToAction;
                loadedWidgets[widgetId].properties.callToActions = callToAction;
                getMockStore(mockState);

                const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                    dashboardId,
                    group: testDashboardGroup,
                    children: [dashboardWidgetItem],
                    widgets: loadedWidgets,
                    breakpoint: 'md',
                });

                expect(bmsWidgetDefinition.callToActions!.test.isDisabled).toEqual(true);
                expect(bmsWidgetDefinition.callToActions!.test.isHidden).toEqual(false);
            });

            it('should resolve the isDisabled callback property and pass on the result', () => {
                const callToAction = {
                    test: {
                        title: 'Test',
                        onClick: jest.fn(),
                        isDisabled(this: any) {
                            return this.a === 4;
                        },
                    },
                };

                mockState.dashboard.dashboardGroups[testDashboardGroup].widgets[widgetId].properties.callToActions =
                    callToAction;
                loadedWidgets[widgetId].properties.callToActions = callToAction;
                getMockStore(mockState);

                const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                    dashboardId,
                    group: testDashboardGroup,
                    children: [dashboardWidgetItem],
                    widgets: loadedWidgets,
                    breakpoint: 'md',
                });

                expect(bmsWidgetDefinition.callToActions!.test.isDisabled).toEqual(true);
                expect(bmsWidgetDefinition.callToActions!.test.isHidden).toEqual(false);
            });
            it('should pass on the isHidden property', () => {
                const callToAction = {
                    test: {
                        title: 'Test',
                        onClick: jest.fn(),
                        isHidden: true,
                    },
                };

                mockState.dashboard.dashboardGroups[testDashboardGroup].widgets[widgetId].properties.callToActions =
                    callToAction;
                loadedWidgets[widgetId].properties.callToActions = callToAction;
                getMockStore(mockState);

                const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                    dashboardId,
                    group: testDashboardGroup,
                    children: [dashboardWidgetItem],
                    widgets: loadedWidgets,
                    breakpoint: 'md',
                });

                expect(bmsWidgetDefinition.callToActions!.test.isHidden).toEqual(true);
                expect(bmsWidgetDefinition.callToActions!.test.isDisabled).toEqual(false);
            });

            it('should resolve the isHidden callback property and pass on the result', () => {
                const callToAction = {
                    test: {
                        title: 'Test',
                        onClick: jest.fn(),
                        isHidden(this: any) {
                            return this.a === 4;
                        },
                    },
                };

                mockState.dashboard.dashboardGroups[testDashboardGroup].widgets[widgetId].properties.callToActions =
                    callToAction;
                loadedWidgets[widgetId].properties.callToActions = callToAction;
                getMockStore(mockState);

                const [bmsWidgetDefinition] = dashboardService.getWidgetsFromDashboardItems({
                    dashboardId,
                    group: testDashboardGroup,
                    children: [dashboardWidgetItem],
                    widgets: loadedWidgets,
                    breakpoint: 'md',
                });

                expect(bmsWidgetDefinition.callToActions!.test.isHidden).toEqual(true);
                expect(bmsWidgetDefinition.callToActions!.test.isDisabled).toEqual(false);
            });
        });
    });

    describe('fetchWidgetData', () => {
        let dateNowMock: jest.SpyInstance<number, []>;
        const now = 1638087587756;
        const twoDaysAgo = 1637911300;

        beforeEach(() => {
            dateNowMock = jest.spyOn(Date, 'now').mockReturnValue(now);
        });

        afterEach(() => {
            dateNowMock.mockReset();
        });

        it('should fetch data from the server and return the data element', async () => {
            const widget = {
                _id: '12',
                artifactName: '@sage/xtrem-test/TestWidget',
                widgetObject: {} as any,
                widgetType: WidgetType.staticContent,
                properties: {
                    getQuery: () => ({ xtremTest: { some: { random: true } } }),
                    title: 'Test',
                    cacheLifespan: WidgetCacheLifespan.day,
                },
            };
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = { '12': widget };

            getMockStore(mockState);
            executeGraphqlQueryMock.mockResolvedValue({ data: { xtremTest: { some: { random: 'data' } } } });
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            const result = await dashboardService.fetchWidgetData({
                widgetDefinition: widget,
                version: '1.1.0',
                locale: 'en-US',
                group: testDashboardGroup,
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(result).toEqual({ xtremTest: { some: { random: 'data' } } });
        });

        it('should fetch again if cached response is too old', async () => {
            const widget = {
                _id: '12',
                artifactName: '@sage/xtrem-test/TestWidget',
                widgetObject: {} as any,
                widgetType: WidgetType.staticContent,
                properties: {
                    getQuery: () => ({ xtremTest: { some: { random: true } } }),
                    title: 'Test',
                    cacheLifespan: WidgetCacheLifespan.day,
                },
            };
            mockState.dashboard.dashboardGroups[testDashboardGroup].widgets = { '12': widget };

            getMockStore(mockState);
            executeGraphqlQueryMock.mockResolvedValue({
                data: { xtremTest: { some: { random: 'data' } } },
                cachedAt: twoDaysAgo,
            });

            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            const result = await dashboardService.fetchWidgetData({
                widgetDefinition: widget,
                version: '1.1.0',
                locale: 'en-US',
                group: testDashboardGroup,
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(2);
            expect(result).toEqual({ xtremTest: { some: { random: 'data' } } });
        });
    });

    describe('cloneDashboard', () => {
        beforeEach(() => {
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    dashboard: {
                        cloneDashboard: {
                            _id: '456',
                            title: 'Cloned Dashboard',
                        },
                    },
                },
            });
        });

        it('should create mutation to clone the current dashboard', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            const result = await dashboardService.cloneDashboard('123');
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    endpoint: '/metadata',
                    query: {
                        mutation: {
                            dashboard: {
                                cloneDashboard: {
                                    __args: {
                                        dashboardId: '123',
                                    },
                                    _id: true,
                                    title: true,
                                },
                            },
                        },
                    },
                }),
            );

            expect(result).toEqual({
                _id: '456',
                title: 'Cloned Dashboard',
            });
        });

        it('throw an exception in an error is returned', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            executeGraphqlQueryMock.mockResolvedValue({
                error: 'Some error',
                data: {
                    dashboard: {
                        cloneDashboard: {
                            _id: '456',
                            title: 'Cloned Dashboard',
                        },
                    },
                },
            });
            await expect(dashboardService.cloneDashboard('123')).rejects.toThrow('Failed to clone dashboard');
        });

        it('throw an exception if no data is returned', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    dashboard: {
                        cloneDashboard: null,
                    },
                },
            });
            await expect(dashboardService.cloneDashboard('123')).rejects.toThrow('Failed to clone dashboard');
        });
    });

    describe('createEmptyDashboard', () => {
        beforeEach(() => {
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    dashboard: {
                        createDashboard: {
                            _id: '324324',
                            title: 'New Dashboard',
                            children: [],
                        },
                    },
                },
            });
        });

        it('should create mutation to create a new dashboard', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            const result = await dashboardService.createEmptyDashboard(testDashboardGroup);
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    endpoint: '/metadata',
                    query: {
                        mutation: {
                            dashboard: {
                                createDashboard: {
                                    __args: { group: 'home' },
                                    _id: true,
                                    title: true,
                                    isTemplate: true,
                                    children: {
                                        _id: true,
                                        positions: {
                                            breakpoint: true,
                                            x: true,
                                            y: true,
                                            h: true,
                                            w: true,
                                        },
                                        type: true,
                                        settings: true,
                                    },
                                },
                            },
                        },
                    },
                }),
            );

            expect(result).toEqual({
                _id: '324324',
                title: 'New Dashboard',
                children: [],
            });
        });

        it('throw an exception in an error is returned', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            executeGraphqlQueryMock.mockResolvedValue({
                error: 'Some error',
                data: {
                    dashboard: {
                        createDashboard: {
                            _id: '324324',
                            title: 'New Dashboard',
                            children: [],
                        },
                    },
                },
            });
            await expect(dashboardService.createEmptyDashboard(testDashboardGroup)).rejects.toThrow(
                'Failed to create dashboard',
            );
        });

        it('throw an exception if no data is returned', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    dashboard: {
                        createDashboard: null,
                    },
                },
            });
            await expect(dashboardService.createEmptyDashboard(testDashboardGroup)).rejects.toThrow(
                'Failed to create dashboard',
            );
        });
    });

    describe('updateDashboard', () => {
        beforeEach(() => {
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    dashboard: {
                        updateDashboardLayout: {
                            _id: '324324',
                            title: 'New Dashboard',
                            children: [
                                {
                                    _id: '123',
                                    h: 1,
                                    x: 1,
                                    w: 2,
                                    y: 5,
                                    settings: '{}',
                                    type: '@sage/xtrem-test/MyWidget',
                                },
                            ],
                        },
                    },
                },
            });
        });

        it('should create mutation to update a dashboard', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            const result = await dashboardService.updateDashboard({
                _id: '324324',
                title: 'New Dashboard',
                children: [
                    {
                        _id: '123',
                        positions: [
                            { x: 1, y: 5, breakpoint: 'xxs' as const, w: 2, h: 1 },
                            { x: 1, y: 5, breakpoint: 'xs' as const, w: 2, h: 1 },
                            { x: 1, y: 5, breakpoint: 'sm' as const, w: 2, h: 1 },
                            { x: 1, y: 5, breakpoint: 'md' as const, w: 2, h: 1 },
                            { x: 1, y: 5, breakpoint: 'lg' as const, w: 2, h: 1 },
                        ],
                        settings: {},
                        type: '@sage/xtrem-test/MyWidget',
                    },
                ],
            });
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    endpoint: '/metadata',
                    query: {
                        mutation: {
                            dashboard: {
                                updateDashboardLayout: {
                                    __args: {
                                        dashboardId: '324324',
                                        layout: '{"_id":"324324","title":"New Dashboard","children":[{"_id":"123","positions":[{"x":1,"y":5,"breakpoint":"xxs","w":2,"h":1},{"x":1,"y":5,"breakpoint":"xs","w":2,"h":1},{"x":1,"y":5,"breakpoint":"sm","w":2,"h":1},{"x":1,"y":5,"breakpoint":"md","w":2,"h":1},{"x":1,"y":5,"breakpoint":"lg","w":2,"h":1}],"settings":{},"type":"@sage/xtrem-test/MyWidget"}]}',
                                    },
                                    _id: true,
                                    children: {
                                        _id: true,
                                        positions: { breakpoint: true, h: true, w: true, x: true, y: true },
                                        settings: true,
                                        type: true,
                                    },
                                    isTemplate: true,
                                    title: true,
                                },
                            },
                        },
                    },
                }),
            );

            expect(result).toEqual({
                _id: '324324',
                title: 'New Dashboard',
                children: [
                    {
                        _id: '123',
                        h: 1,
                        settings: {},
                        type: '@sage/xtrem-test/MyWidget',
                        w: 2,
                        x: 1,
                        y: 5,
                    },
                ],
            });
        });

        it('throw an exception in an error is returned', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            executeGraphqlQueryMock.mockResolvedValue({
                error: 'Some error',
                data: {
                    dashboard: {
                        updateDashboardLayout: {
                            _id: '324324',
                            title: 'New Dashboard',
                            children: [],
                        },
                    },
                },
            });
            await expect(
                dashboardService.updateDashboard({
                    _id: '324324',
                    title: 'New Dashboard',
                    children: [
                        {
                            _id: '123',
                            positions: [
                                { x: 1, y: 5, breakpoint: 'xxs' as const, w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'xs' as const, w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'sm' as const, w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'md' as const, w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'lg' as const, w: 2, h: 1 },
                            ],
                            settings: {},
                            type: '@sage/xtrem-test/MyWidget',
                        },
                    ],
                }),
            ).rejects.toThrow('Failed to update dashboard');
        });

        it('throw an exception if no data is returned', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    dashboard: {
                        updateDashboardLayout: null,
                    },
                },
            });
            await expect(
                dashboardService.updateDashboard({
                    _id: '324324',
                    title: 'New Dashboard',
                    children: [
                        {
                            _id: '123',
                            positions: [
                                { x: 1, y: 5, breakpoint: 'xxs' as const, w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'xs' as const, w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'sm' as const, w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'md' as const, w: 2, h: 1 },
                                { x: 1, y: 5, breakpoint: 'lg' as const, w: 2, h: 1 },
                            ],
                            settings: {},
                            type: '@sage/xtrem-test/MyWidget',
                        },
                    ],
                }),
            ).rejects.toThrow('Failed to update dashboard');
        });
    });

    describe('deleteDashboard', () => {
        beforeEach(() => {
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    dashboard: {
                        deleteDashboard: null,
                    },
                },
            });
        });

        it('should create mutation to delete a dashboard', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            const result = await dashboardService.deleteDashboard('234');
            expect(executeGraphqlQueryMock).toHaveBeenCalledTimes(1);
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    endpoint: '/metadata',
                    query: {
                        mutation: {
                            dashboard: {
                                deleteDashboard: {
                                    __args: { dashboardId: '234' },
                                },
                            },
                        },
                    },
                }),
            );

            expect(result).toEqual(undefined);
        });

        it('throw an exception in an error is returned', async () => {
            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            executeGraphqlQueryMock.mockResolvedValue({
                error: 'Some error',
                data: {
                    dashboard: {
                        deleteDashboard: null,
                    },
                },
            });
            await expect(dashboardService.deleteDashboard('234')).rejects.toThrow('Failed to delete dashboard');
        });
    });

    describe('fetchWidgetCategories', () => {
        it('should create the correct query', async () => {
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    dashboard: {
                        getWidgetCategories: [
                            {
                                key: 'SOME_CATEGORY',
                                title: 'Some Category',
                            },
                            {
                                key: 'ANOTHER_CATEGORY',
                                title: 'Another Category',
                            },
                        ],
                    },
                },
            });

            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            const result = await dashboardService.fetchWidgetCategories();
            expect(executeGraphqlQueryMock).toHaveBeenCalledWith({
                endpoint: '/metadata',
                query: {
                    dashboard: { getWidgetCategories: { key: true, title: true } },
                },
            });
            expect(result).toEqual({
                SOME_CATEGORY: 'Some Category',
                ANOTHER_CATEGORY: 'Another Category',
            });
        });

        it('should throw an exception if the response is invalid', async () => {
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    dashboard: null,
                },
            });

            expect(executeGraphqlQueryMock).not.toHaveBeenCalled();
            expect(dashboardService.fetchWidgetCategories()).rejects.toThrow(
                'Failed to load widget categories from the server',
            );
        });
    });
});
