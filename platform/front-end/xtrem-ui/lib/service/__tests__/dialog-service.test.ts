import {
    getMockState,
    getMockStore,
    applyActionMocks,
    getMockPageDefinition,
    addPageControlObject,
} from '../../__tests__/test-helpers';

import type { SectionControlObject } from '../../component/control-objects';
import * as actions from '../../redux/actions';
import type { DialogDescription } from '../../types/dialogs';
import * as dialogService from '../dialog-service';
import type { Store } from 'redux';
import type { AppAction, XtremAppState } from '../../redux';
import { waitFor } from '@testing-library/dom';

describe('Dialog Service', () => {
    const acceptButton = {
        text: 'Accept',
    };
    const cancelButton = {
        text: 'No way',
    };
    const error = new Error('Something went wrong');
    const level = 'error';
    const message = 'Very important message that needs to be delivered';
    const screenId = 'TestPage';
    const title = 'Error title';
    let mockStore: Store<XtremAppState, AppAction>;
    let mockState: XtremAppState;

    beforeEach(() => {
        dialogService.resetDialogIds();
        mockState = getMockState();
        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        mockState.screenDefinitions.MyTestPage = getMockPageDefinition('MyTestPage');
        addPageControlObject(mockState, 'MyTestPage', { title: 'My Page Title' });
        mockStore = getMockStore(mockState, true);
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    describe('Dialog creation', () => {
        let dialog: DialogDescription;

        beforeEach(() => {
            jest.spyOn(actions, 'openDialog').mockImplementation((_, _dialog) => {
                dialog = _dialog;
                return { type: 'TEST_REDUX_ACTION' } as any;
            });
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should dispatch openDialog on createDialog', () => {
            dialogService.createDialog(screenId, level, title, message, jest.fn());

            expect(actions.openDialog).toHaveBeenCalledTimes(1);
            expect(dialog.level).toBe(level);
            expect(dialog.title).toBe(title);
            expect(dialog.content).toBe(message);
        });

        it('should resolve on dialog accept', done => {
            dialogService
                .createDialog(screenId, level, title, message, (dialogId, resolve) => ({
                    accept: {
                        onClick: resolve,
                        text: 'Ok',
                    },
                }))
                .then(done)
                .catch(() => {
                    expect('This code should never be reached').toBe(undefined);
                });

            dialog.buttons.accept.onClick();
        });

        it('should resolve dialog controls', async () => {
            const resolutionObj = { testKey1: 'testValue1', testKey2: 'testValue2' };
            const promise = dialogService.createDialog(screenId, level, title, message, (dialogId, resolve) => ({
                accept: {
                    onClick: resolve,
                    text: 'Ok',
                },
            })) as Promise<any>;
            dialog.dialogControl.resolve(resolutionObj);

            const result = await promise;
            expect(result).toEqual(resolutionObj);
        });

        it('should reject on dialog cancel', done => {
            const dialogInstance = dialogService.createDialog(
                screenId,
                level,
                title,
                message,
                (dialogId, resolve, reject) => ({
                    cancel: {
                        onClick: () => reject(dialogId),
                        text: 'Cancel',
                    },
                }),
            );

            dialogInstance
                .then(() => {
                    expect('This code should never be reached').toBe(undefined);
                })
                .catch(_dialogId => {
                    expect(_dialogId).toEqual(dialogInstance.id);
                    done();
                });

            dialog.buttons.cancel.onClick();
        });

        it('should resolve the promise when a dialog closes', async () => {
            const mockState = getMockState();
            const getButtonsCallback = jest.fn(() => ({}));
            const dialogControl = dialogService.createDialog(screenId, level, title, message, getButtonsCallback);
            mockState.activeDialogs = {
                123: {
                    buttons: {},
                    content: '',
                    dialogId: 123,
                    isSticker: false,
                    level: 'error',
                    screenId,
                    title: '',
                    dialogControl,
                    options: {},
                },
            };
            getMockStore(mockState);
            const resolveSpy = jest.spyOn(dialogControl, 'resolve');

            expect(resolveSpy).not.toHaveBeenCalled();
            await dialogService.closeDialog({ dialogId: 123 });
            expect(resolveSpy).toHaveBeenCalledTimes(1);
        });

        it('should remove unserializable entities from dialog result', async () => {
            const mockState = getMockState();
            const getButtonsCallback = jest.fn(() => ({}));
            const dialogControl = dialogService.createDialog(screenId, level, title, message, getButtonsCallback);
            mockState.activeDialogs = {
                123: {
                    buttons: {},
                    content: '',
                    dialogId: 123,
                    isSticker: false,
                    level: 'error',
                    screenId,
                    title: '',
                    dialogControl,
                    options: {},
                },
            };
            getMockStore(mockState);
            const resolveSpy = jest.spyOn(dialogControl, 'resolve');

            expect(resolveSpy).not.toHaveBeenCalled();
            await dialogService.closeDialog({ dialogId: 123, result: { a: 124, b: 'stuff', c: () => 'd' } });
            expect(resolveSpy).toHaveBeenCalledTimes(1);
            expect(resolveSpy).toHaveBeenCalledWith({ a: 124, b: 'stuff' });
        });
    });

    describe('Dialog usage', () => {
        beforeEach(() => {
            jest.spyOn(dialogService, 'createDialog');
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should call createDialog on confirmationDialog call', () => {
            const options = {
                acceptButton,
                cancelButton,
            };
            dialogService.confirmationDialog(screenId, level, title, message, options);

            expect(dialogService.createDialog).toHaveBeenCalledTimes(1);
            expect(dialogService.createDialog).toHaveBeenCalledWith(
                screenId,
                level,
                title,
                message,
                expect.anything(),
                options,
                false,
                'confirmation',
            );
        });

        it('should call createDialog on customDialog call', () => {
            const options = {
                acceptButton,
                cancelButton,
            };
            const section = { title: 'Anything' } as SectionControlObject;
            dialogService.customDialog(screenId, level, section, options);

            expect(dialogService.createDialog).toHaveBeenCalledTimes(1);
            expect(dialogService.createDialog).toHaveBeenCalledWith(
                screenId,
                level,
                section.title,
                [section],
                expect.anything(),
                options,
                false,
                'custom',
            );
        });

        it('should request page definition on pageDialog call', async () => {
            const loadPageDialogContentMock = actions.loadPageDialogContent as jest.Mock;
            loadPageDialogContentMock.mockReturnValue(() => Promise.resolve('MyTestPage'));
            expect(loadPageDialogContentMock).not.toHaveBeenCalled();
            dialogService.pageDialog(mockStore, '@sage/xtrem-test/MyTestPage');
            await waitFor(() =>
                expect(loadPageDialogContentMock).toHaveBeenCalledWith(
                    '@sage/xtrem-test/MyTestPage',
                    {},
                    undefined,
                    expect.any(Function),
                    undefined,
                ),
            );
        });

        it('should dispatch the update action when the page is loaded', async () => {
            mockState.activeDialogs = { 1: {} } as any;
            mockStore = getMockStore(mockState);
            const dispatchSpy = jest.spyOn(mockStore, 'dispatch');
            const loadPageDialogContentMock = actions.loadPageDialogContent as jest.Mock;
            loadPageDialogContentMock.mockReturnValue(() => Promise.resolve('MyTestPage'));
            expect(loadPageDialogContentMock).not.toHaveBeenCalled();
            expect(dispatchSpy).not.toHaveBeenCalled();
            dialogService.pageDialog(mockStore, '@sage/xtrem-test/MyTestPage');
            await waitFor(() =>
                expect(dispatchSpy).toHaveBeenCalledWith(
                    expect.objectContaining({ type: 'SetScreenDefinitionDialogId' }),
                ),
            );
        });

        it('should not dispatch the update action when the dialog is removed from the state while the page is fetched', async () => {
            const dispatchSpy = jest.spyOn(mockStore, 'dispatch');
            const loadPageDialogContentMock = actions.loadPageDialogContent as jest.Mock;
            loadPageDialogContentMock.mockReturnValue(() => Promise.resolve('MyTestPage'));
            expect(loadPageDialogContentMock).not.toHaveBeenCalled();
            expect(dispatchSpy).not.toHaveBeenCalled();
            dialogService.pageDialog(mockStore, '@sage/xtrem-test/MyTestPage');
            await waitFor(() => expect(dispatchSpy).not.toHaveBeenCalledWith(), { timeout: 150 });
        });

        it('should update the menu items with the right title after the page is loaded', async () => {
            mockState.activeDialogs = { 1: {} } as any;
            mockStore = getMockStore(mockState);
            const loadPageDialogContentMock = actions.loadPageDialogContent as jest.Mock;
            loadPageDialogContentMock.mockReturnValue(() => Promise.resolve('MyTestPage'));
            expect(mockState.applicationContext!.updateMenu).not.toHaveBeenCalled();
            dialogService.pageDialog(mockStore, '@sage/xtrem-test/MyTestPage');
            await waitFor(() => expect(mockState.applicationContext!.updateMenu).toHaveBeenCalled());
        });

        it('should call createDialog on errorDialog call', () => {
            dialogService.errorDialog(screenId, title, error);

            expect(dialogService.createDialog).toHaveBeenCalledTimes(1);
            expect(dialogService.createDialog).toHaveBeenCalledWith(
                screenId,
                level,
                title,
                error,
                expect.anything(),
                undefined,
                false,
                'error',
            );
        });

        it('should call createDialog on messageDialog call', () => {
            const options = {
                acceptButton,
            };
            dialogService.messageDialog(screenId, level, title, message, options);

            expect(dialogService.createDialog).toHaveBeenCalledTimes(1);
            expect(dialogService.createDialog).toHaveBeenCalledWith(
                screenId,
                level,
                title,
                message,
                expect.anything(),
                options,
                false,
                'message',
            );
        });
    });
});
