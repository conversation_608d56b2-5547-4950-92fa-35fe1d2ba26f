/* eslint-disable react/jsx-indent */
jest.mock('../loader');
import { applyActionMocks, getMockState, getMockStore, restoreActionHandlerMocks } from '../../__tests__/test-helpers';
import React from 'react';

import * as windowUtils from '../../utils/window';
import axios from 'axios';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type {
    PageActionDecoratorProperties,
    PageDecoratorProperties,
    TableDecoratorProperties,
} from '../../component/decorators';
import { sticker } from '../../component/decorators';
import * as dialogService from '../dialog-service';
import * as toastService from '../toast-service';
import * as screenLoaderService from '../screen-loader-service';
import * as transactionService from '../transactions-service';
import type { XtremAppState } from '../../redux';
import { actions } from '../../redux';
import * as events from '../../utils/events';
import { render, waitFor } from '@testing-library/react';
import { NEW_PAGE, QUERY_PARAM_SELECTED_SECTION_ID } from '../../utils/constants';
import { navigationPanelId } from '../../component/container/navigation-panel/navigation-panel-types';
import { FieldKey } from '../../component/types';
import * as pageMetadata from '../page-metadata';
import { GraphQLTypes } from '../../types';

describe('Screen loader service', () => {
    const moduleName = 'test-module';
    let mockState: XtremAppState;
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let errorDialogSpy: jest.SpyInstance;
    let confirmationDialogSpy: jest.SpyInstance;
    let messageDialogSpy: jest.SpyInstance;
    let showToastDialogSpy: jest.SpyInstance;
    let postMock: jest.SpyInstance;
    const pageName = 'TestPage';
    const extensionName = 'TestPageExtension';

    beforeEach(() => {
        jest.spyOn(windowUtils, 'isDevMode').mockReturnValue(true);
        errorDialogSpy = jest.spyOn(dialogService, 'errorDialog');
        confirmationDialogSpy = jest.spyOn(dialogService, 'confirmationDialog').mockResolvedValue({});
        messageDialogSpy = jest.spyOn(dialogService, 'messageDialog').mockResolvedValue({});
        showToastDialogSpy = jest.spyOn(toastService, 'showToast');
        postMock = jest.spyOn(axios, 'post');

        mockState = getMockState({
            translations: {
                'en-US': {},
            },
            applicationPackages: {
                '@sage/test-module': '12.3.4',
            },
            screenDefinitions: {
                [pageName]: {
                    metadata: {
                        screenId: pageName,
                        uiComponentProperties: {
                            [pageName]: {},
                        },
                    },
                    dirtyStates: {},
                } as any,
            },
        });
        mockStore = getMockStore(mockState, true);
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    describe('Fetch page description', () => {
        let pageJsCode: string;

        beforeEach(() => {
            pageJsCode = `xtremArtifact.${pageName} = (function(){
                    const __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                        return c > 3 && r && Object.defineProperty(target, key, r), r;
                    };
                    const ui = require('@sage/xtrem-ui');

                    class ${pageName} extends ui.Page {
                        constructor() {
                            super(...arguments);
                            //// eslint-disable-next-line no-console
console.log('Page constructor is being called');
                        }
                    }

                    __decorate([
                        ui.decorators.section({
                            title: 'A section',
                        })
                    ], ${pageName}.prototype, "aSection", void 0);

                    __decorate([
                        ui.decorators.section({
                            title: 'B section',
                        })
                    ], ${pageName}.prototype, "bSection", void 0);

                    __decorate([
                        ui.decorators.numericField({
                            title: 'Numeric field title',
                            isTransient: true,
                            onChange() {
                                this.$.showToast('Changed');
                            },
                        })
                    ], ${pageName}.prototype, "aNumericField", void 0);

                    __decorate([
                        ui.decorators.tableField({
                            title: 'Table field title',
                            isTransient: true,
                            columns:[
                                ui.nestedFields.text({ bind: "textItem" }),
                                ui.nestedFields.numeric({ bind: "numericField" }),
                            ],
                            dropdownActions: [
                                {
                                    id: 'edit',
                                    icon: 'edit',
                                    title: 'Edit',
                                },
                                {
                                    id: 'delete',
                                    icon: 'delete',
                                    title: 'Delete',
                                },
                            ],
                            inlineActions: [
                                {
                                    id: 'view',
                                    icon: 'eye',
                                    title: 'View Details',
                                },
                            ],
                        })
                    ], ${pageName}.prototype, "aTableField", void 0);

                    ${pageName} = __decorate([
                        ui.decorators.page({
                            authorizationCode: 'TESTCO',
                            module: '${moduleName}',
                            node: '@sage/xtrem-any/AnyNode',
                            title: 'Test page',
                            isTransient: true,
                        })
                    ], ${pageName});

                    return ${pageName};
                })()`;
            postMock.mockResolvedValue({
                data: {
                    data: {
                        getDataType: [],
                        getNodeDetailsList: [],
                        getNodeDetails: {},
                        pages: [
                            {
                                content: pageJsCode,
                                access: [
                                    {
                                        node: '@sage/xtrem-authorization/GroupRoleSite',
                                        bindings: [
                                            {
                                                name: 'read',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'close',
                                                status: 'unauthorized',
                                            },
                                            {
                                                name: 'delete',
                                                status: 'unavailable',
                                            },
                                            {
                                                name: 'create',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'infoSection',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'groupInfoBlock',
                                                status: 'authorized',
                                            },
                                            {
                                                name: '_id',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'id',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'name',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'groupRoleBlock',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'groupSiteBlock',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'customSave',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'groupList',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'editGroupRoleSite',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'rolesActivitiesSection',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'rolesActivitiesBlock',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'associatedUserSection',
                                                status: 'authorized',
                                            },
                                            {
                                                name: 'associatedUsersBlock',
                                                status: 'authorized',
                                            },
                                        ],
                                    },
                                    {
                                        node: '@sage/xtrem-authorization/GroupRole',
                                        bindings: [
                                            {
                                                name: 'groupRoles',
                                                status: 'unauthorized',
                                            },
                                        ],
                                    },
                                    {
                                        node: '@sage/xtrem-authorization/GroupSite',
                                        bindings: [
                                            {
                                                name: 'groupSites',
                                                status: 'authorized',
                                            },
                                        ],
                                    },
                                ],
                            },
                        ],
                    },
                },
                status: 200,
                statusText: 'OK',
                headers: {},
                config: {},
            });
        });

        it('should retrieve page definition', async () => {
            const path = `@sage/${moduleName}/${pageName}`;
            const pageDefinition = await screenLoaderService.fetchPageDefinition({
                getState: mockStore.getState,
                dispatch: mockStore.dispatch,
                path,
            });
            expect(pageDefinition).not.toBe(null);
            expect(pageDefinition!.page).not.toBe(null);
            expect(pageDefinition!.page.$.page).not.toBe(null);
            expect(Object.keys(pageDefinition!.metadata.controlObjects)).toContain('aNumericField');
            expect(pageDefinition!.metadata.uiComponentProperties.aNumericField.title).toEqual('Numeric field title');
        });

        it('should select the default section based on the query params', async () => {
            const path = `@sage/${moduleName}/${pageName}`;
            const pageDefinition = await screenLoaderService.fetchPageDefinition({
                getState: mockStore.getState,
                dispatch: mockStore.dispatch,
                path,
                queryParameters: {
                    [QUERY_PARAM_SELECTED_SECTION_ID]: 'bSection',
                },
            });

            expect(pageDefinition?.activeSection).toEqual('bSection');
        });

        it('should reduce access rights into an object', async () => {
            const path = `@sage/${moduleName}/${pageName}`;
            const pageDefinition = await screenLoaderService.fetchPageDefinition({
                getState: mockStore.getState,
                dispatch: mockStore.dispatch,
                path,
            });
            expect(pageDefinition).not.toBe(null);
            expect(pageDefinition!.accessBindings).toBeInstanceOf(Object);
            expect(Object.keys(pageDefinition!.accessBindings!)).toHaveLength(3);
            expect(Object.keys(pageDefinition!.accessBindings!.GroupRoleSite)).toHaveLength(18);
            expect(Object.keys(pageDefinition!.accessBindings!.GroupRole)).toHaveLength(1);
            expect(Object.keys(pageDefinition!.accessBindings!.GroupSite)).toHaveLength(1);

            expect(pageDefinition!.accessBindings!.GroupRoleSite.read).toEqual('authorized');
            expect(pageDefinition!.accessBindings!.GroupRoleSite.close).toEqual('unauthorized');
            expect(pageDefinition!.accessBindings!.GroupRoleSite.delete).toEqual('unavailable');
            expect(pageDefinition!.accessBindings!.GroupSite.groupSites).toEqual('authorized');
            expect(pageDefinition!.accessBindings!.GroupRole.groupRoles).toEqual('unauthorized');
        });

        it('should handle exceptions', async () => {
            // eslint-disable-next-line prefer-promise-reject-errors
            postMock.mockImplementation(() => Promise.reject('Some network error ocurred'));
            const path = `@sage/${moduleName}/${pageName}`;

            expect(errorDialogSpy).not.toHaveBeenCalled();

            const pageDefinition = await screenLoaderService.fetchPageDefinition({
                getState: mockStore.getState,
                dispatch: mockStore.dispatch,
                path,
            });

            expect(pageDefinition).toBe(null);
            expect(errorDialogSpy).toHaveBeenLastCalledWith('TestPage', 'Error', 'Some network error ocurred');
        });

        it('should display an error dialog if the page content is missing', async () => {
            postMock.mockResolvedValue({
                data: {
                    data: {
                        getDataType: [],
                        getNodeDetailsList: [],
                        getNodeDetails: {},
                        pages: [],
                    },
                },
                status: 200,
                statusText: 'OK',
                headers: {},
                config: {},
            });

            const path = `@sage/${moduleName}/${pageName}`;

            expect(errorDialogSpy).not.toHaveBeenCalled();

            const pageDefinition = await screenLoaderService.fetchPageDefinition({
                getState: mockStore.getState,
                dispatch: mockStore.dispatch,
                path,
            });

            expect(pageDefinition).toBe(null);
            expect(errorDialogSpy).toHaveBeenLastCalledWith(
                'TestPage',
                'Error',
                'Could not find any content for page. Make sure the page exists and the format of the URL is /@<vendor name>/@<package name>/@<page name>',
            );
        });

        describe('generic page actions', () => {
            const path = `@sage/${moduleName}/${pageName}/eyJfaWQiOiIxIn0=`;

            it('should set default page actions', async () => {
                const pageDefinition = await screenLoaderService.fetchPageDefinition({
                    getState: mockStore.getState,
                    dispatch: mockStore.dispatch,
                    path,
                    isMainPage: true,
                    queryParameters: { _id: '123' },
                });
                expect(Object.keys(pageDefinition!.metadata.pageActions)).toHaveLength(9);
                expect(pageDefinition!.metadata.pageActions.$standardDeleteAction).toBeInstanceOf(Object);
                expect(pageDefinition!.metadata.pageActions.$standardSaveAction).toBeInstanceOf(Object);
                expect(pageDefinition!.metadata.pageActions.$standardNewAction).toBeInstanceOf(Object);
                expect(pageDefinition!.metadata.pageActions.$standardCancelAction).toBeInstanceOf(Object);
                expect(pageDefinition!.metadata.pageActions.$standardOpenRecordHistoryAction).toBeInstanceOf(Object);
                expect(pageDefinition!.metadata.pageActions.$standardOpenCustomizationPageWizardAction).toBeInstanceOf(
                    Object,
                );
                expect(pageDefinition!.metadata.pageActions.$standardDialogConfirmationAction).toBeInstanceOf(Object);
            });

            describe('delete crud action', () => {
                it('should have the correct title and icon', async () => {
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });
                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardDeleteAction as PageActionDecoratorProperties<any>;
                    expect(properties.title).toEqual('Delete');
                    expect(properties.icon).toEqual('delete');
                    expect(properties.onClick).toBeInstanceOf(Function);
                });

                it('should display a confirmation message when triggered', async () => {
                    confirmationDialogSpy.mockRejectedValue({});
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });
                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardDeleteAction as PageActionDecoratorProperties<any>;
                    expect(confirmationDialogSpy).not.toHaveBeenCalled();
                    properties.onClick!.apply(pageDefinition!.page);
                    expect(confirmationDialogSpy).toHaveBeenCalledWith(
                        'TestPage',
                        'warn',
                        'Delete record',
                        'You are about to delete this record.',
                        { acceptButton: { text: 'Delete', isDestructive: true }, cancelButton: { text: 'Cancel' } },
                    );
                });

                it('should call the graph with a delete mutation when the user confirms', async () => {
                    confirmationDialogSpy.mockResolvedValue({});
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });
                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardDeleteAction as PageActionDecoratorProperties<any>;
                    const deleteMock = jest.spyOn(pageDefinition!.page.$.graph, 'delete').mockResolvedValue();
                    expect(showToastDialogSpy).not.toHaveBeenCalled();
                    await properties.onClick!.apply(pageDefinition!.page);
                    expect(deleteMock).toHaveBeenCalled();
                    expect(showToastDialogSpy).toHaveBeenCalledWith('Record has been deleted successfully.', {
                        language: 'markdown',
                        type: 'success',
                    });
                });

                it('should not display an error message when the user rejects the confirmation window of the delete operation', async () => {
                    confirmationDialogSpy.mockRejectedValue({});
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });
                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardDeleteAction as PageActionDecoratorProperties<any>;
                    const deleteMock = jest.spyOn(pageDefinition!.page.$.graph, 'delete').mockResolvedValue();
                    expect(showToastDialogSpy).not.toHaveBeenCalled();
                    await properties.onClick!.apply(pageDefinition!.page);
                    expect(deleteMock).not.toHaveBeenCalled();
                    expect(showToastDialogSpy).not.toHaveBeenCalled();
                });

                it('should clean the page when the record is deleted', async () => {
                    confirmationDialogSpy.mockResolvedValue({});
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });
                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardDeleteAction as PageActionDecoratorProperties<any>;
                    jest.spyOn(pageDefinition!.page.$.graph, 'delete').mockResolvedValue();
                    expect(actions.selectRecord).not.toHaveBeenCalled();
                    await properties.onClick!.apply(pageDefinition!.page);
                    expect(actions.selectRecord).toHaveBeenCalledWith(pageName, null);
                });
            });

            describe('add crud action', () => {
                it('should have the correct title and icon', async () => {
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });
                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardNewAction as PageActionDecoratorProperties<any>;
                    expect(properties.title).toEqual('Create');
                    expect(properties.icon).toEqual('add');
                    expect(properties.onClick).toBeInstanceOf(Function);
                });

                it('should clean the page if it is not in a dialog', async () => {
                    confirmationDialogSpy.mockResolvedValue({});
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });
                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardNewAction as PageActionDecoratorProperties<any>;
                    expect(actions.selectRecord).not.toHaveBeenCalled();
                    await properties.onClick!.apply(pageDefinition!.page);
                    expect(actions.selectRecord).toHaveBeenCalledWith(pageName, NEW_PAGE);
                });

                it('should not clean the page if it is in a dialog', async () => {
                    confirmationDialogSpy.mockResolvedValue({});

                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });

                    jest.spyOn(pageDefinition!.page.$, 'isInDialog', 'get').mockReturnValue(true);

                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardNewAction as PageActionDecoratorProperties<any>;
                    expect(actions.selectRecord).not.toHaveBeenCalled();
                    await properties.onClick!.apply(pageDefinition!.page);
                    expect(actions.selectRecord).toHaveBeenCalledWith(pageName, NEW_PAGE);
                });
            });

            describe('cancel crud action', () => {
                it('should have the correct title and icon', async () => {
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });
                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardCancelAction as PageActionDecoratorProperties<any>;
                    expect(properties.title).toEqual('Cancel');
                    expect(properties.onClick).toBeInstanceOf(Function);
                });

                it('should clean the page ', async () => {
                    confirmationDialogSpy.mockResolvedValue({});
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });
                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardCancelAction as PageActionDecoratorProperties<any>;
                    pageDefinition!.queryParameters = { _id: 123 };
                    expect(actions.selectRecord).not.toHaveBeenCalled();
                    await properties.onClick!.apply(pageDefinition!.page);
                    expect(actions.selectRecord).toHaveBeenCalledWith(pageName, null, true);
                });
            });

            describe('record history action', () => {
                it('should display a message dialog with the record history', async () => {
                    messageDialogSpy.mockResolvedValue({});
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });

                    mockState = getMockState({
                        translations: {
                            'en-US': {},
                        },
                        screenDefinitions: {
                            [pageName]: {
                                queryParameters: { _id: 123 },
                                metadata: {
                                    screenId: pageName,
                                    uiComponentProperties: {
                                        [pageName]: { node: '@sage/xtrem-test/TestNode' },
                                    },
                                },
                                dirtyStates: {},
                            } as any,
                        },
                    });
                    mockStore = getMockStore(mockState, true);

                    postMock.mockResolvedValue({
                        data: {
                            data: {
                                getDataType: [],
                                getNodeDetailsList: [],
                                getNodeDetails: {},
                                xtremTest: {
                                    testNode: {
                                        read: {
                                            _createStamp: '2024-02-01T07:55:12.383Z',
                                            _updateStamp: '2024-02-02T07:55:12.383Z',
                                            _createUser: {
                                                firstName: 'Jane',
                                                lastName: 'Doe',
                                                displayName: 'Doe, Jane',
                                            },
                                            _updateUser: {
                                                firstName: 'John',
                                                lastName: 'Doe',
                                                displayName: 'Doe, John',
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    });

                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardOpenRecordHistoryAction as PageActionDecoratorProperties<any>;
                    expect(messageDialogSpy).not.toHaveBeenCalled();
                    await properties.onClick!.apply(pageDefinition!.page);

                    expect(messageDialogSpy).toHaveBeenCalledWith(
                        pageName,
                        'info',
                        'Record history',
                        expect.stringContaining('By **Doe, Jane** on **01/02/2024** at'),
                        { mdContent: true },
                    );
                    expect(messageDialogSpy).toHaveBeenCalledWith(
                        pageName,
                        'info',
                        'Record history',
                        expect.stringContaining('By **Doe, John** on **02/02/2024** at'),
                        { mdContent: true },
                    );
                    expect(showToastDialogSpy).not.toHaveBeenCalledWith(pageName, null, true);
                });
            });

            describe('save crud action', () => {
                it('should have the correct title and icon', async () => {
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });
                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardSaveAction as PageActionDecoratorProperties<any>;
                    expect(properties.title).toEqual('Save');
                    expect(properties.onClick).toBeInstanceOf(Function);
                });

                it('should validate the page and display a toast if it fails', async () => {
                    confirmationDialogSpy.mockResolvedValue({});
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: '123' },
                    });
                    jest.spyOn(pageDefinition!.page.$.page, 'validateWithDetails').mockResolvedValue({
                        allErrors: [
                            {
                                message: 'Validation message 1',
                                screenId: pageName,
                                elementId: 'aNumericField',
                                validationRule: 'business-rule-error',
                            },
                            {
                                message: 'Validation message 2',
                                screenId: pageName,
                                elementId: 'aTableField',
                                validationRule: 'business-rule-error',
                            },
                        ],
                        blockingErrors: [
                            {
                                message: 'Validation message 1',
                                screenId: pageName,
                                elementId: 'aNumericField',
                                validationRule: 'business-rule-error',
                            },
                            {
                                message: 'Validation message 2',
                                screenId: pageName,
                                elementId: 'aTableField',
                                validationRule: 'business-rule-error',
                            },
                        ],
                    } as any);

                    mockState = getMockState({
                        translations: {
                            'en-US': {},
                        },
                        screenDefinitions: {
                            [pageDefinition!.metadata.screenId]: pageDefinition!,
                        },
                    });
                    mockStore = getMockStore(mockState);
                    restoreActionHandlerMocks();
                    expect(showToastDialogSpy).not.toHaveBeenCalled();
                    await events.triggerFieldEvent(pageDefinition!.metadata.screenId, '$standardSaveAction', 'onClick');
                    await waitFor(() => {
                        expect(showToastDialogSpy).toHaveBeenCalled();
                    });

                    expect(render(showToastDialogSpy.mock.calls[0][0]).container.innerHTML).toEqual(
                        render(
                            <div className="e-page-validation-error-list">
                                <h6>Validation Errors</h6>
                                <ul>
                                    <li>
                                        <p>
                                            <strong>Numeric field title:</strong>
                                            &#32;Validation message 1
                                        </p>
                                    </li>
                                    <li>
                                        <p>
                                            <strong>Table field title:</strong>
                                            &#32;Validation message 2
                                        </p>
                                    </li>
                                </ul>
                            </div>,
                        ).container.innerHTML,
                    );
                    expect(showToastDialogSpy.mock.calls[0][1]).toEqual({
                        type: 'error',
                        language: 'jsx',
                    });
                });

                it('should call create if the page has no value', async () => {
                    confirmationDialogSpy.mockResolvedValue({});
                    jest.spyOn(transactionService, 'getUiComponentProperties').mockReturnValue(false);
                    const setUiPropertiesMock = jest
                        .spyOn(transactionService, 'setUiComponentProperties')
                        .mockReturnValue();

                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                        isMainPage: true,
                        queryParameters: { _id: 123 },
                    });
                    jest.spyOn(pageDefinition!.page.$.page, 'validateWithDetails').mockResolvedValue({
                        allErrors: [],
                        blockingErrors: [],
                    } as any);
                    const createMock = jest.spyOn(pageDefinition!.page.$.graph, 'create').mockResolvedValue();
                    const updateMock = jest.spyOn(pageDefinition!.page.$.graph, 'update').mockResolvedValue();
                    const properties = pageDefinition!.metadata.uiComponentProperties
                        .$standardSaveAction as PageActionDecoratorProperties<any>;
                    expect(createMock).not.toHaveBeenCalled();
                    expect(updateMock).not.toHaveBeenCalled();
                    expect(setUiPropertiesMock).not.toHaveBeenCalled();
                    await properties.onClick!.apply(pageDefinition!.page);
                    expect(createMock).toHaveBeenCalled();
                    expect(updateMock).not.toHaveBeenCalled();
                    expect(setUiPropertiesMock).toHaveBeenCalled();
                    expect(setUiPropertiesMock).toHaveBeenCalledWith(pageName, '$standardDeleteAction', {
                        isDisabled: false,
                    });
                });
            });
        });

        describe('custom fields', () => {
            it('should load a nav panel only page with custom fields', async () => {
                pageJsCode = `xtremArtifact.${pageName} = (function(){
                    const __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                        return c > 3 && r && Object.defineProperty(target, key, r), r;
                    };
                    const ui = require('@sage/xtrem-ui');

                    class ${pageName} extends ui.Page {
                        constructor() {
                            super(...arguments);
                            //// eslint-disable-next-line no-console
console.log('Page constructor is being called');
                        }
                    }

                    ${pageName} = __decorate([
                        ui.decorators.page({
                            authorizationCode: 'TESTCO',
                            module: '${moduleName}',
                            mode: 'default',
                            node: '@sage/xtrem-master-data/ItemSite',
                            navigationPanel: {
                                onSelect() {
                                    return true;
                                },
                                listItem: {
                                    image: ui.nestedFields.image({
                                        bind: { item: { image: true } },
                                    }),
                                    title: ui.nestedFields.text({ bind: { item: { id: true } }, title: 'Field', isHidden: true }),
                                },
                            },
                        })
                    ], ${pageName});

                    return ${pageName};
                })()`;
                postMock.mockImplementation((path: string) => {
                    return {
                        data: {
                            data:
                                path === '/metadata'
                                    ? {
                                          getDataType: [],
                                          getNodeDetailsList: [],
                                          getNodeDetails: {},
                                          pages: [
                                              {
                                                  content: pageJsCode,
                                                  customFields: [
                                                      {
                                                          name: '@sage/xtrem-master-data/ItemSite',
                                                          properties: [
                                                              {
                                                                  name: 'testCustomField',
                                                                  datatype: GraphQLTypes.Date,
                                                                  componentType: 'dateField',
                                                                  anchorPropertyName: 'site',
                                                                  anchorPosition: 'after',
                                                                  destinationTypes: ['page', 'lookup', 'navigationBar'],
                                                                  componentAttributes:
                                                                      '{"max":null,"min":null,"scale":null,"title":"Test custom field","prefix":null,"maxDate":null,"minDate":null,"postfix":null,"maxLength":null,"minLength":null,"helperText":null,"infoMessage":null,"isMandatory":null,"warningMessage":null,"isSortedAlphabetically":null}',
                                                              },
                                                          ],
                                                      },
                                                  ],
                                              },
                                          ],
                                      }
                                    : {
                                          navigationPanelItems: {
                                              itemSite: {
                                                  query: {
                                                      edges: [
                                                          {
                                                              node: {
                                                                  _id: '1',
                                                                  item: {
                                                                      id: '1324',
                                                                      stockUnit: {
                                                                          _id: '12',
                                                                          name: 'Liter',
                                                                          decimalDigits: 2,
                                                                          symbol: 'L',
                                                                      },
                                                                      category: {
                                                                          _id: '3',
                                                                          id: 'OTHER',
                                                                      },
                                                                      _id: '1',
                                                                      name: 'Acrylates C10-30',
                                                                      image: null,
                                                                  },
                                                                  site: {
                                                                      currency: {
                                                                          _id: '4',
                                                                          name: 'US Dollar',
                                                                          decimalDigits: 2,
                                                                          symbol: '$',
                                                                      },
                                                                      _id: '3',
                                                                      name: 'Chem. Atlanta',
                                                                  },
                                                                  stockValue: '0',
                                                                  onQualityControlStockQuantity: '0',
                                                                  rejectedStockQuantity: '0',
                                                                  acceptedStockQuantity: '0',
                                                                  allocatedQuantity: '0',
                                                                  inStockQuantity: '0',
                                                                  _customData: '{}',
                                                              },
                                                              cursor: '["1324",1]#30',
                                                          },
                                                      ],
                                                      pageInfo: {
                                                          startCursor: '["1324",1]#30',
                                                          endCursor: '["3376",20]#08',
                                                          hasPreviousPage: false,
                                                          hasNextPage: true,
                                                      },
                                                  },
                                              },
                                          },
                                      },
                        },
                        status: 200,
                        statusText: 'OK',
                        headers: {},
                        config: {},
                    };
                });

                const path = `@sage/${moduleName}/${pageName}`;
                const pageDefinition = await screenLoaderService.fetchPageDefinition({
                    getState: mockStore.getState,
                    dispatch: mockStore.dispatch,
                    path,
                });
                expect(pageDefinition).not.toBe(null);
            });
        });

        describe('page extension', () => {
            beforeEach(() => {
                const pageExtensionJsCode = `
                    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                        return c > 3 && r && Object.defineProperty(target, key, r), r;
                    };

                    const ui = require('@sage/xtrem-ui');

                    let ${extensionName} = class ${extensionName} extends ui.PageExtension {
                        myVeryFineMethod(eventName) {
                            return 'TEST_EXTENSION_FUNCTION_RETURN_VALUE';
                        }
                    };
                    __decorate([
                        ui.decorators.textField({
                            title: 'Extension field title',
                            isTransient: true,
                            onChange() {
                                this.$.showToast('Changed');
                            },
                        })
                    ], ${extensionName}.prototype, "aSampleTextField", void 0);
                    __decorate([
                        ui.decorators.textField({
                            title: 'Extension at the end',
                            isDisabled: true,
                        })
                    ], ${extensionName}.prototype, "anotherSampleTextField", void 0);
                    ${extensionName} = __decorate([
                        ui.decorators.pageExtension({
                            extends: '@sage/${moduleName}/${pageName}',
                            onLoad() {
                                // eslint-disable-next-line no-console
console.log('on load called');
                            },
                            onClose() {
                                // eslint-disable-next-line no-console
console.log('on close called');
                            },
                        })
                    ], ${extensionName});`;

                postMock.mockResolvedValue({
                    data: {
                        data: {
                            getDataType: [],
                            getNodeDetailsList: [],
                            getNodeDetails: {},
                            pages: [
                                {
                                    content: pageJsCode,
                                    extensions: [
                                        { content: pageExtensionJsCode, packageName: '@sage/xtrem-test-extension' },
                                    ],
                                },
                            ],
                        },
                    },
                    status: 200,
                    statusText: 'OK',
                    headers: {},
                    config: {},
                });
            });

            it('should retrieve page definition with the extensions and create control object for its fields', async () => {
                const path = `@sage/${moduleName}/${pageName}`;
                const pageDefinition = await screenLoaderService.fetchPageDefinition({
                    getState: mockStore.getState,
                    dispatch: mockStore.dispatch,
                    path,
                });
                expect(pageDefinition).not.toBe(null);
                if (pageDefinition) {
                    expect(Object.keys(pageDefinition.metadata.controlObjects)).toContain('aSampleTextField');
                    expect(Object.keys(pageDefinition.metadata.controlObjects)).toContain('anotherSampleTextField');
                }
            });

            it('should preserve the onLoad and onClose callbacks from the extension', async () => {
                const path = `@sage/${moduleName}/${pageName}`;
                const pageDefinition = await screenLoaderService.fetchPageDefinition({
                    getState: mockStore.getState,
                    dispatch: mockStore.dispatch,
                    path,
                });
                expect(pageDefinition).not.toBe(null);
                expect(Object.keys(pageDefinition!.metadata.uiComponentProperties[pageName])).toContain('onLoadAfter');
                expect(Object.keys(pageDefinition!.metadata.defaultUiComponentProperties[pageName])).toContain(
                    'onLoadAfter',
                );
                expect(Object.keys(pageDefinition!.metadata.uiComponentProperties[pageName])).toContain('onCloseAfter');
                expect(Object.keys(pageDefinition!.metadata.defaultUiComponentProperties[pageName])).toContain(
                    'onCloseAfter',
                );
            });

            it('should retrieve page definition with the extensions and set the decorator properties', async () => {
                const path = `@sage/${moduleName}/${pageName}`;
                const pageDefinition = await screenLoaderService.fetchPageDefinition({
                    getState: mockStore.getState,
                    dispatch: mockStore.dispatch,
                    path,
                });
                expect(pageDefinition).not.toBe(null);
                if (pageDefinition) {
                    expect(Object.keys(pageDefinition.metadata.uiComponentProperties)).toContain('aSampleTextField');
                    expect(Object.keys(pageDefinition.metadata.uiComponentProperties)).toContain(
                        'anotherSampleTextField',
                    );

                    expect(pageDefinition.metadata.uiComponentProperties.aSampleTextField.title).toEqual(
                        'Extension field title',
                    );
                    expect(pageDefinition.metadata.uiComponentProperties.aSampleTextField.isTransient).toEqual(true);

                    expect(pageDefinition.metadata.uiComponentProperties.anotherSampleTextField.title).toEqual(
                        'Extension at the end',
                    );
                    expect(pageDefinition.metadata.uiComponentProperties.anotherSampleTextField.isDisabled).toEqual(
                        true,
                    );
                }
            });

            it('should copy methods from the extension to the target artifact', async () => {
                const path = `@sage/${moduleName}/${pageName}`;
                const pageDefinition = await screenLoaderService.fetchPageDefinition({
                    getState: mockStore.getState,
                    dispatch: mockStore.dispatch,
                    path,
                });
                expect(pageDefinition).not.toBe(null);
                expect(pageDefinition!.page).not.toBe(null);

                // Ensure that property exists
                expect((pageDefinition!.page as any).myVeryFineMethod).toBeDefined();
                expect((pageDefinition!.page as any).myVeryFineMethod).toBeInstanceOf(Function);

                // Calling method
                expect((pageDefinition!.page as any).myVeryFineMethod()).toEqual(
                    'TEST_EXTENSION_FUNCTION_RETURN_VALUE',
                );
            });

            describe('extending the navigation panel', () => {
                beforeEach(() => {
                    pageJsCode = `xtremArtifact.${pageName} = (function(){
                        const __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                            var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                            if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                            else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                            return c > 3 && r && Object.defineProperty(target, key, r), r;
                        };
                        const ui = require('@sage/xtrem-ui');

                        class ${pageName} extends ui.Page {
                            constructor() {
                                super(...arguments);
                                //// eslint-disable-next-line no-console
    console.log('Page constructor is being called');
                            }
                        }

                        __decorate([
                            ui.decorators.numericField({
                                title: 'Numeric field title',
                                isTransient: true,
                                onChange() {
                                    this.$.showToast('Changed');
                                },
                            })
                        ], ${pageName}.prototype, "aNumericField", void 0);

                        __decorate([
                            ui.decorators.tableField({
                                title: 'Table field title',
                                isTransient: true,
                                columns:[
                                    ui.nestedFields.text({ bind: "textItem" }),
                                    ui.nestedFields.numeric({ bind: "numericField" }),
                                ]
                            })
                        ], ${pageName}.prototype, "aTableField", void 0);

                        ${pageName} = __decorate([
                            ui.decorators.page({
                                authorizationCode: 'TESTCO',
                                module: '${moduleName}',
                                node: '@sage/xtrem-any/AnyNode',
                                title: 'Test page',
                                navigationPanel: {
                                    listItem: {
                                        title: ui.nestedFields.text({ bind: 'product', title: 'Product' }),
                                        titleRight: ui.nestedFields.text({ bind: '_id', title: 'ID' }),
                                        line2: ui.nestedFields.numeric({ bind: 'listPrice', title: 'List Price', isHiddenDesktop: true }),
                                    },
                                    dropdownActions: [
                                        {
                                            id: 'edit',
                                            icon: 'edit',
                                            title: 'Edit',
                                        },
                                        {
                                            id: 'delete',
                                            icon: 'delete',
                                            title: 'Delete',
                                        },
                                    ],
                                    inlineActions: [
                                        {
                                            id: 'edit',
                                            icon: 'edit',
                                            title: 'Edit',
                                        },
                                        {
                                            id: 'delete',
                                            icon: 'delete',
                                            title: 'Delete',
                                        },
                                    ],
                                },
                            })
                        ], ${pageName});

                        return ${pageName};
                    })()`;
                    const pageExtensionJsCode = `
                    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                        return c > 3 && r && Object.defineProperty(target, key, r), r;
                    };

                    const ui = require('@sage/xtrem-ui');

                    let ${extensionName} = class ${extensionName} extends ui.PageExtension {
                        myVeryFineMethod(eventName) {
                            return 'TEST_EXTENSION_FUNCTION_RETURN_VALUE';
                        }
                    };
                    __decorate([
                        ui.decorators.textField({
                            title: 'Extension field title',
                            isTransient: true,
                            onChange() {
                                this.$.showToast('Changed');
                            },
                        })
                    ], ${extensionName}.prototype, "aSampleTextField", void 0);
                    __decorate([
                        ui.decorators.textField({
                            title: 'Extension at the end',
                            isDisabled: true,
                        })
                    ], ${extensionName}.prototype, "anotherSampleTextField", void 0);
                    ${extensionName} = __decorate([
                        ui.decorators.pageExtension({
                            extends: '@sage/${moduleName}/${pageName}',
                            navigationPanel: {
                                listItem:{
                                    line2: ui.nestedFieldExtensions.reference({
                                        title: 'Biodegradability Category',
                                        node: '@sage/xtrem-show-case-bundle/BiodegradabilityCategory',
                                        bind: 'biodegradabilityCategory',
                                        valueField: 'description',
                                        insertBefore: 'listPrice',
                                        canFilter: true,
                                    }),
                                },
                                dropdownActions: [
                                    {
                                        id: 'share',
                                        icon: 'share',
                                        title: 'Share',
                                    },
                                    {
                                        id: 'export',
                                        icon: 'download',
                                        title: 'Export',
                                        insertAfter: 'edit',
                                    },
                                    {
                                        id: 'archive',
                                        icon: 'archive',
                                        title: 'Archive',
                                        insertBefore: 'delete',
                                    },
                                ],
                                inlineActions: [
                                    {
                                        id: 'share',
                                        icon: 'share',
                                        title: 'Share',
                                    },
                                    {
                                        id: 'export',
                                        icon: 'download',
                                        title: 'Export',
                                        insertAfter: 'edit',
                                    },
                                    {
                                        id: 'archive',
                                        icon: 'archive',
                                        title: 'Archive',
                                        insertBefore: 'delete',
                                    },
                                ],
                            }
                        })
                    ], ${extensionName});`;

                    postMock.mockResolvedValue({
                        data: {
                            data: {
                                getDataType: [],
                                getNodeDetailsList: [],
                                getNodeDetails: {},
                                pages: [
                                    {
                                        content: pageJsCode,
                                        extensions: [
                                            { content: pageExtensionJsCode, packageName: '@sage/xtrem-test-extension' },
                                        ],
                                    },
                                ],
                            },
                        },
                        status: 200,
                        statusText: 'OK',
                        headers: {},
                        config: {},
                    });
                });

                it('should merge column definitions from the extensions', async () => {
                    const path = `@sage/${moduleName}/${pageName}`;
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                    });

                    const navigationPanelProperties = pageDefinition!.metadata.uiComponentProperties[
                        navigationPanelId
                    ] as TableDecoratorProperties;
                    // Calling method
                    expect(navigationPanelProperties.columns).toEqual([
                        expect.objectContaining({
                            type: FieldKey.Text,
                            properties: expect.objectContaining({
                                bind: 'product',
                            }),
                        }),
                        expect.objectContaining({
                            type: FieldKey.Text,
                            properties: expect.objectContaining({
                                bind: '_id',
                            }),
                        }),
                        expect.objectContaining({
                            type: FieldKey.Reference,
                            properties: expect.objectContaining({
                                bind: 'biodegradabilityCategory',
                            }),
                        }),
                        expect.objectContaining({
                            type: FieldKey.Numeric,
                            properties: expect.objectContaining({
                                bind: 'listPrice',
                            }),
                        }),
                    ]);
                });

                it('should merge card definition from the extensions', async () => {
                    const path = `@sage/${moduleName}/${pageName}`;
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                    });

                    const navigationPanelProperties = pageDefinition!.metadata.uiComponentProperties[
                        navigationPanelId
                    ] as TableDecoratorProperties;
                    // Calling method
                    expect(navigationPanelProperties.mobileCard).toEqual({
                        line2: expect.objectContaining({
                            type: FieldKey.Reference,
                            properties: expect.objectContaining({
                                bind: 'biodegradabilityCategory',
                            }),
                        }),
                        title: expect.objectContaining({
                            type: FieldKey.Text,
                            properties: expect.objectContaining({
                                bind: 'product',
                            }),
                        }),
                        titleRight: expect.objectContaining({
                            type: FieldKey.Text,
                            properties: expect.objectContaining({
                                bind: '_id',
                            }),
                        }),
                    });
                });
                it('should insert dropdownActions and inlineActions in the correct order based on insertAfter and insertBefore', async () => {
                    const path = `@sage/${moduleName}/${pageName}`;
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: mockStore.dispatch,
                        path,
                    });

                    const navigationPanelProperties = pageDefinition!.metadata.uiComponentProperties[
                        navigationPanelId
                    ] as TableDecoratorProperties;

                    expect(navigationPanelProperties.dropdownActions).toEqual([
                        expect.objectContaining({
                            id: 'edit',
                            icon: 'edit',
                            title: 'Edit',
                        }),
                        expect.objectContaining({
                            id: 'export',
                            icon: 'download',
                            title: 'Export',
                        }),
                        expect.objectContaining({
                            id: 'archive',
                            icon: 'archive',
                            title: 'Archive',
                        }),
                        expect.objectContaining({
                            id: 'delete',
                            icon: 'delete',
                            title: 'Delete',
                        }),
                        expect.objectContaining({
                            id: 'share',
                            icon: 'share',
                            title: 'Share',
                        }),
                    ]);
                    expect(navigationPanelProperties.inlineActions).toEqual([
                        expect.objectContaining({
                            id: 'edit',
                            icon: 'edit',
                            title: 'Edit',
                        }),
                        expect.objectContaining({
                            id: 'export',
                            icon: 'download',
                            title: 'Export',
                        }),
                        expect.objectContaining({
                            id: 'archive',
                            icon: 'archive',
                            title: 'Archive',
                        }),
                        expect.objectContaining({
                            id: 'delete',
                            icon: 'delete',
                            title: 'Delete',
                        }),
                        expect.objectContaining({
                            id: 'share',
                            icon: 'share',
                            title: 'Share',
                        }),
                    ]);
                });
            });

            describe('Inserting headerDropDownActions in Page Extension', () => {
                beforeEach(() => {
                    pageJsCode = `xtremArtifact.${pageName} = (function() {
                        const __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                            var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                            if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                            else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                            return c > 3 && r && Object.defineProperty(target, key, r), r;
                        };
                        const ui = require('@sage/xtrem-ui');

                        class ${pageName} extends ui.Page {
                            constructor() {
                                super(...arguments);
                            }
                        }
                        __decorate([
                            ui.decorators.pageAction({
                                icon: 'add',
                                title: 'Base Action 1',
                                onClick: () => console.log('Base Action 1 triggered')
                            })
                        ], ${pageName}.prototype, "BaseAction1", void 0);

                        __decorate([
                            ui.decorators.pageAction({
                                icon: 'add',
                                title: 'Base Action 2',
                                onClick: () => console.log('Base Action 2 triggered')
                            })
                        ], ${pageName}.prototype, "BaseAction2", void 0);

                        ${pageName} = __decorate([
                            ui.decorators.page({
                                authorizationCode: 'TESTCO',
                                module: '${moduleName}',
                                node: '@sage/xtrem-any/AnyNode',
                                title: 'Test page',
                                isTransient: true,
                                headerDropDownActions() {
                                    return [this.BaseAction1, this.BaseAction2];
                                }
                            })
                        ], ${pageName});

                        return ${pageName};
                    })()`;
                });

                it('should add new headerDropDownActions correctly with insertBefore, insertAfter, or at the end if not found', async () => {
                    const pageExtensionJsCode = `
                    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                        return c > 3 && r && Object.defineProperty(target, key, r), r;
                    };

                    const ui = require('@sage/xtrem-ui');

                    let ${extensionName} = class ${extensionName} extends ui.PageExtension {
                        myVeryFineMethod(eventName) {
                            return 'TEST_EXTENSION_FUNCTION_RETURN_VALUE';
                        }
                    };

                     __decorate([
                        ui.decorators.pageAction({
                            icon: 'add',
                            title: 'Extension Action Before Base Action 2',
                            onClick: () => console.log('Extension Action Before Base Action 2 triggered'),
                            insertBefore() {
                                return this.BaseAction2;
                            },
                        })
                    ], ${extensionName}.prototype, "ExtensionActionBeforeBaseAction2", void 0);

                    __decorate([
                        ui.decorators.pageAction({
                            icon: 'add',
                            title: 'Extension Action After Base Action 1',
                            onClick: () => console.log('Extension Action After Base Action 1 triggered'),
                            insertAfter() {
                                return this.BaseAction1;
                            },
                        })
                    ], ${extensionName}.prototype, "ExtensionActionAfterBaseAction1", void 0);

                    __decorate([
                        ui.decorators.pageAction({
                            icon: 'add',
                            title: 'Extension Action Without Target',
                            onClick: () => console.log('Extension Action Without Target triggered')
                        })
                    ], ${extensionName}.prototype, "ExtensionActionWithoutTarget", void 0);

                    ${extensionName} = __decorate([
                        ui.decorators.pageExtension({
                            extends: '@sage/${moduleName}/${pageName}',
                            onLoad() {
                                // eslint-disable-next-line no-console
    console.log('on load called');
                            },
                            headerDropDownActions() {
                                return [this.ExtensionActionBeforeBaseAction2, this.ExtensionActionAfterBaseAction1, this.ExtensionActionWithoutTarget];
                            }
                        })
                    ], ${extensionName})`;

                    postMock.mockResolvedValue({
                        data: {
                            data: {
                                getDataType: [],
                                getNodeDetailsList: [],
                                getNodeDetails: {},
                                pages: [
                                    {
                                        content: pageJsCode,
                                        extensions: [
                                            { content: pageExtensionJsCode, packageName: '@sage/xtrem-test-extension' },
                                        ],
                                    },
                                ],
                            },
                        },
                        status: 200,
                        statusText: 'OK',
                        headers: {},
                        config: {},
                    });

                    const path = `@sage/${moduleName}/${pageName}`;
                    const dispatchMock = jest.fn(mockStore.dispatch);
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: dispatchMock,
                        path,
                    });

                    const pageProperties = pageDefinition!.metadata.uiComponentProperties[
                        pageName
                    ] as PageDecoratorProperties<any>;

                    expect(pageProperties).not.toBeNull();
                    expect(pageProperties).toBeDefined();

                    const actions =
                        typeof pageProperties?.headerDropDownActions === 'function'
                            ? pageProperties.headerDropDownActions.call(pageDefinition?.page)
                            : undefined;

                    expect(actions).toBeDefined();

                    expect(actions).toEqual([
                        expect.objectContaining({ id: 'BaseAction1' }),
                        expect.objectContaining({ id: 'ExtensionActionAfterBaseAction1' }),
                        expect.objectContaining({ id: 'ExtensionActionBeforeBaseAction2' }),
                        expect.objectContaining({ id: 'BaseAction2' }),
                        expect.objectContaining({ id: 'ExtensionActionWithoutTarget' }),
                    ]);
                });
            });

            describe('Inserting headerQuickActions in Page Extension', () => {
                beforeEach(() => {
                    pageJsCode = `xtremArtifact.${pageName} = (function() {
                        const __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                            var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                            if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                            else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                            return c > 3 && r && Object.defineProperty(target, key, r), r;
                        };
                        const ui = require('@sage/xtrem-ui');

                        class ${pageName} extends ui.Page {
                            constructor() {
                                super(...arguments);
                            }
                        }
                         __decorate([
                            ui.decorators.pageAction({
                                icon: 'add',
                                title: 'Base Action 1',
                                onClick: () => console.log('Base Action 1 triggered')
                            })
                        ], ${pageName}.prototype, "BaseAction1", void 0);

                        __decorate([
                            ui.decorators.pageAction({
                                icon: 'add',
                                title: 'Base Action 2',
                                onClick: () => console.log('Base Action 2 triggered')
                            })
                        ], ${pageName}.prototype, "BaseAction2", void 0);

                        ${pageName} = __decorate([
                            ui.decorators.page({
                                authorizationCode: 'TESTCO',
                                module: '${moduleName}',
                                node: '@sage/xtrem-any/AnyNode',
                                title: 'Test page',
                                isTransient: true,
                                headerQuickActions() {
                                    return [this.BaseAction1, this.BaseAction2];
                                }
                            })
                        ], ${pageName});

                        return ${pageName};
                    })()`;
                });

                it('should add new headerQuickActions correctly with insertBefore, insertAfter, or at the end if not found', async () => {
                    const pageExtensionJsCode = `
                    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                        return c > 3 && r && Object.defineProperty(target, key, r), r;
                    };

                    const ui = require('@sage/xtrem-ui');

                    let ${extensionName} = class ${extensionName} extends ui.PageExtension {
                        myVeryFineMethod(eventName) {
                            return 'TEST_EXTENSION_FUNCTION_RETURN_VALUE';
                        }
                    };

                     __decorate([
                        ui.decorators.pageAction({
                            icon: 'add',
                            title: 'Extension Action Before Base Action 2',
                            onClick: () => console.log('Extension Action Before Base Action 2 triggered'),
                            insertBefore() {
                                return this.BaseAction2;
                            },
                        })
                    ], ${extensionName}.prototype, "ExtensionActionBeforeBaseAction2", void 0);

                    __decorate([
                        ui.decorators.pageAction({
                            icon: 'add',
                            title: 'Extension Action After Base Action 1',
                            onClick: () => console.log('Extension Action After Base Action 1 triggered'),
                            insertAfter() {
                                return this.BaseAction1;
                            },
                        })
                    ], ${extensionName}.prototype, "ExtensionActionAfterBaseAction1", void 0);

                    __decorate([
                        ui.decorators.pageAction({
                            icon: 'add',
                            title: 'Extension Action Without Target',
                            onClick: () => console.log('Extension Action Without Target triggered')
                        })
                    ], ${extensionName}.prototype, "ExtensionActionWithoutTarget", void 0);

                    ${extensionName} = __decorate([
                        ui.decorators.pageExtension({
                            extends: '@sage/${moduleName}/${pageName}',
                            onLoad() {
                                console.log('on load called');
                            },
                            headerQuickActions() {
                                return [this.ExtensionActionBeforeBaseAction2, this.ExtensionActionAfterBaseAction1, this.ExtensionActionWithoutTarget];
                            }
                        })
                    ], ${extensionName})`;

                    postMock.mockResolvedValue({
                        data: {
                            data: {
                                getDataType: [],
                                getNodeDetailsList: [],
                                getNodeDetails: {},
                                pages: [
                                    {
                                        content: pageJsCode,
                                        extensions: [
                                            { content: pageExtensionJsCode, packageName: '@sage/xtrem-test-extension' },
                                        ],
                                    },
                                ],
                            },
                        },
                        status: 200,
                        statusText: 'OK',
                        headers: {},
                        config: {},
                    });

                    const path = `@sage/${moduleName}/${pageName}`;
                    const dispatchMock = jest.fn(mockStore.dispatch);
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: dispatchMock,
                        path,
                    });

                    const pageProperties = pageDefinition!.metadata.uiComponentProperties[
                        pageName
                    ] as PageDecoratorProperties<any>;

                    expect(pageProperties).not.toBeNull();
                    expect(pageProperties).toBeDefined();

                    const actions =
                        typeof pageProperties?.headerQuickActions === 'function'
                            ? pageProperties.headerQuickActions.call(pageDefinition?.page)
                            : undefined;

                    expect(actions).toBeDefined();

                    expect(actions).toEqual([
                        expect.objectContaining({ id: 'BaseAction1' }),
                        expect.objectContaining({ id: 'ExtensionActionAfterBaseAction1' }),
                        expect.objectContaining({ id: 'ExtensionActionBeforeBaseAction2' }),
                        expect.objectContaining({ id: 'BaseAction2' }),
                        expect.objectContaining({ id: 'ExtensionActionWithoutTarget' }),
                    ]);
                });
            });
        });

        describe('loading plugins', () => {
            let fakePlugin = `
            var React = require('react');
            exports.default = {
                component: class FakePlugin extends React.Component {
                    render() {
                        return React.createElement('div', null, 'My fake plugin');
                    }
                },

            }`;

            beforeEach(() => {
                jest.useFakeTimers();

                postMock.mockImplementation(() =>
                    Promise.resolve({
                        data: {
                            data: {
                                getDataType: [],
                                getNodeDetailsList: [],
                                getNodeDetails: {},
                                pages: [
                                    {
                                        content: pageJsCode,
                                        plugins: ['@sage/fakePlugin'],
                                    },
                                ],
                            },
                        },
                        status: 200,
                        statusText: 'OK',
                        headers: {},
                        config: {},
                    }),
                );
                (window as any).fetch = jest.fn(() => {
                    return Promise.resolve({
                        status: 200,
                        text: () => Promise.resolve(fakePlugin),
                    });
                });
            });

            afterEach(() => {
                jest.useRealTimers();
                (window as any).fetch = null;
            });

            it('should load plugin source code and add it to the state', async () => {
                const path = `@sage/${moduleName}/${pageName}`;
                const dispatchMock = jest.fn(mockStore.dispatch);
                expect(dispatchMock).not.toHaveBeenCalled();
                await screenLoaderService.fetchPageDefinition({
                    getState: mockStore.getState,
                    dispatch: dispatchMock,
                    path,
                });
                jest.runAllTicks();
                expect(dispatchMock).toHaveBeenCalled();
                expect(errorDialogSpy).not.toHaveBeenCalled();
                expect(actions.addPlugin).toHaveBeenCalled();
                expect(actions.addPlugin).toHaveBeenCalledWith('@sage/fakePlugin', { component: expect.any(Function) });
            });

            it('should throw an exception if the plugin does not have a default export', async () => {
                fakePlugin = `
                    var React = require('react');
                    exports.MyPlugin = {
                        component: class FakePlugin extends React.Component {
                            render() {
                                return React.createElement('div', null, 'My fake plugin');
                            }
                        },
                    }`;
                const path = `@sage/${moduleName}/${pageName}`;
                const dispatchMock = jest.fn(mockStore.dispatch);
                expect(dispatchMock).not.toHaveBeenCalled();
                expect(errorDialogSpy).not.toHaveBeenCalled();
                expect(
                    await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: dispatchMock,
                        path,
                    }),
                ).toEqual(null);
                expect(dispatchMock).not.toHaveBeenCalled();
                expect(errorDialogSpy).toHaveBeenLastCalledWith(
                    'TestPage',
                    'Error',
                    'The format of the plugin is invalid. Default export from main file is missing.',
                );
            });

            it('should throw an exception if the plugin does not have a component', async () => {
                fakePlugin = 'exports.default = {};';
                const path = `@sage/${moduleName}/${pageName}`;
                const dispatchMock = jest.fn(mockStore.dispatch);
                expect(dispatchMock).not.toHaveBeenCalled();
                expect(errorDialogSpy).not.toHaveBeenCalled();
                expect(
                    await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: dispatchMock,
                        path,
                    }),
                ).toEqual(null);
                expect(dispatchMock).not.toHaveBeenCalled();
                expect(errorDialogSpy).toHaveBeenLastCalledWith(
                    'TestPage',
                    'Error',
                    'The format of the plugin is invalid. The plugin does not expose a component.',
                );
            });

            it('should throw an exception if the request fails', async () => {
                (window as any).fetch = jest.fn(() => {
                    return Promise.resolve({
                        status: 500,
                    });
                });
                const path = `@sage/${moduleName}/${pageName}`;
                const dispatchMock = jest.fn(mockStore.dispatch);
                expect(dispatchMock).not.toHaveBeenCalled();
                expect(errorDialogSpy).not.toHaveBeenCalled();
                expect(
                    await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: dispatchMock,
                        path,
                    }),
                ).toEqual(null);
                expect(dispatchMock).not.toHaveBeenCalled();
                expect(errorDialogSpy).toHaveBeenLastCalledWith(
                    'TestPage',
                    'Error',
                    'Failed to load plugin: @sage/fakePlugin',
                );
            });
        });

        describe('extension properties', () => {
            it('should throw an error if an extension property tries to override a non-existent property', async () => {
                const pageExtensionJsCode = `
                var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                    return c > 3 && r && Object.defineProperty(target, key, r), r;
                };

                const ui = require('@sage/xtrem-ui');

                let ${extensionName} = class ${extensionName} extends ui.PageExtension {
                    myVeryFineMethod(eventName) {
                        return 'TEST_EXTENSION_FUNCTION_RETURN_VALUE';
                    }
                };
                __decorate([
                    ui.decorators.textField({
                        title: 'Extension field title',
                        isTransient: true,
                        onChange() {
                            this.$.showToast('Changed');
                        },
                    })
                ], ${extensionName}.prototype, "aSampleTextField", void 0);

                // THIS FIELD MATTERS FOR THE TEST CASE:
                __decorate([
                    ui.decorators.textFieldOverride({
                        title: 'This field does not really exist',
                        isTransient: true,
                        onChange() {
                            this.$.showToast('Changed');
                        },
                    })
                ], ${extensionName}.prototype, "aNonExistentField", void 0);
                __decorate([
                    ui.decorators.textField({
                        title: 'Extension at the end',
                        isDisabled: true,
                    })
                ], ${extensionName}.prototype, "anotherSampleTextField", void 0);
                ${extensionName} = __decorate([
                    ui.decorators.pageExtension({
                        extends: '@sage/${moduleName}/${pageName}',
                        onLoad() {
                            // eslint-disable-next-line no-console
console.log('on load called');
                        },
                    })
                ], ${extensionName});`;

                postMock.mockResolvedValue({
                    data: {
                        data: {
                            getDataType: [],
                            getNodeDetailsList: [],
                            getNodeDetails: {},
                            pages: [
                                {
                                    content: pageJsCode,
                                    extensions: [
                                        { content: pageExtensionJsCode, packageName: '@sage/xtrem-test-extension' },
                                    ],
                                },
                            ],
                        },
                    },
                    status: 200,
                    statusText: 'OK',
                    headers: {},
                    config: {},
                });

                const path = `@sage/${moduleName}/${pageName}`;
                const dispatchMock = jest.fn(mockStore.dispatch);
                expect(dispatchMock).not.toHaveBeenCalled();
                expect(errorDialogSpy).not.toHaveBeenCalled();
                expect(
                    await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: dispatchMock,
                        path,
                    }),
                ).toEqual(null);
                expect(errorDialogSpy).toHaveBeenCalledWith(
                    pageName,
                    'Error',
                    'The targeted screen member does not exist on the screen that you are about to extend. (TestPageExtension: aNonExistentField)',
                );
            });

            it('property extension decorators should throw an error in normal page context', async () => {
                pageJsCode = `{
                    ${pageName}: (function(){
                        const __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                            var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                            if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                            else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                            return c > 3 && r && Object.defineProperty(target, key, r), r;
                        };
                        const ui = require('@sage/xtrem-ui');

                        class ${pageName} extends ui.Page {
                            constructor() {
                                super(...arguments);
                                console.log('Page constructor is being called');
                            }
                        }

                        __decorate([
                            ui.decorators.numericFieldOverride({
                                title: 'Numeric field title',
                                onChange() {
                                    this.$.showToast('Changed');
                                },
                            })
                        ], ${pageName}.prototype, "aNumericField", void 0);

                        ${pageName} = __decorate([
                            ui.decorators.page({
                                authorizationCode: 'TESTCO',
                                module: '${moduleName}',
                                node: '@sage/xtrem-any/AnyNode',
                                title: 'Test page',
                                isTransient: true,
                            })
                        ], ${pageName});

                        return ${pageName};
                    })()
                }`;

                postMock.mockResolvedValue({
                    data: {
                        data: {
                            getDataType: [],
                            getNodeDetailsList: [],
                            getNodeDetails: {},
                            pages: [
                                {
                                    content: pageJsCode,
                                },
                            ],
                        },
                    },
                    status: 200,
                    statusText: 'OK',
                    headers: {},
                    config: {},
                });

                const path = `@sage/${moduleName}/${pageName}`;
                const dispatchMock = jest.fn(mockStore.dispatch);
                expect(dispatchMock).not.toHaveBeenCalled();
                expect(errorDialogSpy).not.toHaveBeenCalled();
                expect(
                    await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: dispatchMock,
                        path,
                    }),
                ).toEqual(null);
                expect(errorDialogSpy).toHaveBeenCalledWith(
                    pageName,
                    'Error',
                    'Property override decorators (those that ends with Extension) can only be used in extension screens.',
                );
            });

            it('property extension decorators override basic properties', async () => {
                const pageExtensionJsCode = `
                var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                    return c > 3 && r && Object.defineProperty(target, key, r), r;
                };

                const ui = require('@sage/xtrem-ui');

                let ${extensionName} = class ${extensionName} extends ui.PageExtension {
                    myVeryFineMethod(eventName) {
                        return 'TEST_EXTENSION_FUNCTION_RETURN_VALUE';
                    }
                };

                // THIS FIELD MATTERS FOR THIS TEST:
                __decorate([
                    ui.decorators.numericFieldOverride({
                        title: 'This title is set by the extension',
                        width: 'large',
                        isDisabled: true,
                    })
                ], ${extensionName}.prototype, "aNumericField", void 0);

                __decorate([
                    ui.decorators.textField({
                        title: 'Extension at the end',
                        isDisabled: true,
                    })
                ], ${extensionName}.prototype, "anotherSampleTextField", void 0);
                ${extensionName} = __decorate([
                    ui.decorators.pageExtension({
                        extends: '@sage/${moduleName}/${pageName}',
                        onLoad() {
                            // eslint-disable-next-line no-console
console.log('on load called');
                        },
                    })
                ], ${extensionName});`;

                postMock.mockResolvedValue({
                    data: {
                        data: {
                            getDataType: [],
                            getNodeDetailsList: [],
                            getNodeDetails: {},
                            pages: [
                                {
                                    content: pageJsCode,
                                    extensions: [
                                        { content: pageExtensionJsCode, packageName: '@sage/xtrem-test-extension' },
                                    ],
                                },
                            ],
                        },
                    },
                    status: 200,
                    statusText: 'OK',
                    headers: {},
                    config: {},
                });

                const path = `@sage/${moduleName}/${pageName}`;
                const dispatchMock = jest.fn(mockStore.dispatch);
                expect(dispatchMock).not.toHaveBeenCalled();
                expect(errorDialogSpy).not.toHaveBeenCalled();
                const pageDefinition = await screenLoaderService.fetchPageDefinition({
                    getState: mockStore.getState,
                    dispatch: dispatchMock,
                    path,
                });
                expect(pageDefinition).not.toBeNull();
                expect(errorDialogSpy).not.toHaveBeenCalled();

                expect(pageDefinition!.metadata.uiComponentProperties.aNumericField.title).toEqual(
                    'This title is set by the extension',
                );

                expect(pageDefinition!.metadata.uiComponentProperties.aNumericField.isDisabled).toEqual(true);
            });

            describe('inserting new fields', () => {
                beforeEach(() => {
                    pageJsCode = `xtremArtifact.${pageName} = (function(){
                        const __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                            var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                            if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                            else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                            return c > 3 && r && Object.defineProperty(target, key, r), r;
                        };
                        const ui = require('@sage/xtrem-ui');

                        class ${pageName} extends ui.Page {
                            constructor() {
                                super(...arguments);
                                //// eslint-disable-next-line no-console
    console.log('Page constructor is being called');
                            }
                        }

                        __decorate([
                            ui.decorators.section({
                                title: 'A section',
                            })
                        ], ${pageName}.prototype, "aSection", void 0);

                        __decorate([
                            ui.decorators.block({
                                title: 'A block',
                                parent(){
                                    return this.aSection;
                                }
                            })
                        ], ${pageName}.prototype, "aBlock", void 0);

                        __decorate([
                            ui.decorators.numericField({
                                title: 'Numeric field title',
                                isTransient: true,
                                parent(){
                                    return this.aBlock;
                                },
                                onChange() {
                                    this.$.showToast('Changed');
                                },
                            })
                        ], ${pageName}.prototype, "aNumericField", void 0);

                        __decorate([
                            ui.decorators.tableField({
                                title: 'Table field title',
                                isTransient: true,
                                parent(){
                                    return this.aBlock;
                                },
                                columns:[
                                    ui.nestedFields.text({ bind: "textItem" }),
                                    ui.nestedFields.numeric({ bind: "numericField" }),
                                ]
                            })
                        ], ${pageName}.prototype, "aTableField", void 0);

                        ${pageName} = __decorate([
                            ui.decorators.page({
                                authorizationCode: 'TESTCO',
                                module: '${moduleName}',
                                node: '@sage/xtrem-any/AnyNode',
                                title: 'Test page',
                                isTransient: true,
                            })
                        ], ${pageName});

                        return ${pageName};
                    })()`;
                });

                it('should be able to insert extension field to the first position in a block', async () => {
                    const pageExtensionJsCode = `
                var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                    return c > 3 && r && Object.defineProperty(target, key, r), r;
                };

                const ui = require('@sage/xtrem-ui');

                let ${extensionName} = class ${extensionName} extends ui.PageExtension {
                    myVeryFineMethod(eventName) {
                        return 'TEST_EXTENSION_FUNCTION_RETURN_VALUE';
                    }
                };


                // THIS FIELD MATTERS FOR THIS TEST:

                __decorate([
                    ui.decorators.textField({
                        isDisabled: true,
                        parent(){
                            return this.aBlock;
                        },
                        insertBefore() {
                            return this.aNumericField;
                        }
                    })
                ], ${extensionName}.prototype, "anotherSampleTextField", void 0);

                ${extensionName} = __decorate([
                    ui.decorators.pageExtension({
                        extends: '@sage/${moduleName}/${pageName}',
                        onLoad() {
                            // eslint-disable-next-line no-console
console.log('on load called');
                        },
                    })
                ], ${extensionName});`;

                    postMock.mockResolvedValue({
                        data: {
                            data: {
                                getDataType: [],
                                getNodeDetailsList: [],
                                getNodeDetails: {},
                                pages: [
                                    {
                                        content: pageJsCode,
                                        extensions: [
                                            { content: pageExtensionJsCode, packageName: '@sage/xtrem-test-extension' },
                                        ],
                                    },
                                ],
                            },
                        },
                        status: 200,
                        statusText: 'OK',
                        headers: {},
                        config: {},
                    });

                    const path = `@sage/${moduleName}/${pageName}`;
                    const dispatchMock = jest.fn(mockStore.dispatch);
                    expect(dispatchMock).not.toHaveBeenCalled();
                    expect(errorDialogSpy).not.toHaveBeenCalled();
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: dispatchMock,
                        path,
                    });
                    expect(pageDefinition).not.toBeNull();
                    expect(errorDialogSpy).not.toHaveBeenCalled();

                    expect(pageDefinition!.metadata.layout.$items[0].$layout!.$items[0].$layout!.$items).toEqual([
                        expect.objectContaining({ $bind: 'anotherSampleTextField' }),
                        expect.objectContaining({ $bind: 'aNumericField' }),
                        expect.objectContaining({ $bind: 'aTableField' }),
                    ]);
                });

                it('should be able to insert extension field to a middle position in a block', async () => {
                    const pageExtensionJsCode = `
                var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                    return c > 3 && r && Object.defineProperty(target, key, r), r;
                };

                const ui = require('@sage/xtrem-ui');

                let ${extensionName} = class ${extensionName} extends ui.PageExtension {
                    myVeryFineMethod(eventName) {
                        return 'TEST_EXTENSION_FUNCTION_RETURN_VALUE';
                    }
                };


                // THIS FIELD MATTERS FOR THIS TEST:

                __decorate([
                    ui.decorators.textField({
                        isDisabled: true,
                        parent(){
                            return this.aBlock;
                        },
                        insertBefore() {
                            return this.aTableField;
                        }
                    })
                ], ${extensionName}.prototype, "anotherSampleTextField", void 0);

                ${extensionName} = __decorate([
                    ui.decorators.pageExtension({
                        extends: '@sage/${moduleName}/${pageName}',
                        onLoad() {
                            // eslint-disable-next-line no-console
console.log('on load called');
                        },
                    })
                ], ${extensionName});`;

                    postMock.mockResolvedValue({
                        data: {
                            data: {
                                getDataType: [],
                                getNodeDetailsList: [],
                                getNodeDetails: {},
                                pages: [
                                    {
                                        content: pageJsCode,
                                        extensions: [
                                            { content: pageExtensionJsCode, packageName: '@sage/xtrem-test-extension' },
                                        ],
                                    },
                                ],
                            },
                        },
                        status: 200,
                        statusText: 'OK',
                        headers: {},
                        config: {},
                    });

                    const path = `@sage/${moduleName}/${pageName}`;
                    const dispatchMock = jest.fn(mockStore.dispatch);
                    expect(dispatchMock).not.toHaveBeenCalled();
                    expect(errorDialogSpy).not.toHaveBeenCalled();
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: dispatchMock,
                        path,
                    });
                    expect(pageDefinition).not.toBeNull();
                    expect(errorDialogSpy).not.toHaveBeenCalled();

                    expect(pageDefinition!.metadata.layout.$items[0].$layout!.$items[0].$layout!.$items).toEqual([
                        expect.objectContaining({ $bind: 'aNumericField' }),
                        expect.objectContaining({ $bind: 'anotherSampleTextField' }),
                        expect.objectContaining({ $bind: 'aTableField' }),
                    ]);
                });
                it('should be able to insert extension field to the end of a block', async () => {
                    const pageExtensionJsCode = `
                var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                    return c > 3 && r && Object.defineProperty(target, key, r), r;
                };

                const ui = require('@sage/xtrem-ui');

                let ${extensionName} = class ${extensionName} extends ui.PageExtension {
                    myVeryFineMethod(eventName) {
                        return 'TEST_EXTENSION_FUNCTION_RETURN_VALUE';
                    }
                };


                // THIS FIELD MATTERS FOR THIS TEST:

                __decorate([
                    ui.decorators.textField({
                        isDisabled: true,
                        parent(){
                            return this.aBlock;
                        }
                    })
                ], ${extensionName}.prototype, "anotherSampleTextField", void 0);

                ${extensionName} = __decorate([
                    ui.decorators.pageExtension({
                        extends: '@sage/${moduleName}/${pageName}',
                        onLoad() {
                            // eslint-disable-next-line no-console
console.log('on load called');
                        },
                    })
                ], ${extensionName});`;

                    postMock.mockResolvedValue({
                        data: {
                            data: {
                                getDataType: [],
                                getNodeDetailsList: [],
                                getNodeDetails: {},
                                pages: [
                                    {
                                        content: pageJsCode,
                                        extensions: [
                                            { content: pageExtensionJsCode, packageName: '@sage/xtrem-test-extension' },
                                        ],
                                    },
                                ],
                            },
                        },
                        status: 200,
                        statusText: 'OK',
                        headers: {},
                        config: {},
                    });

                    const path = `@sage/${moduleName}/${pageName}`;
                    const dispatchMock = jest.fn(mockStore.dispatch);
                    expect(dispatchMock).not.toHaveBeenCalled();
                    expect(errorDialogSpy).not.toHaveBeenCalled();
                    const pageDefinition = await screenLoaderService.fetchPageDefinition({
                        getState: mockStore.getState,
                        dispatch: dispatchMock,
                        path,
                    });
                    expect(pageDefinition).not.toBeNull();
                    expect(errorDialogSpy).not.toHaveBeenCalled();

                    expect(pageDefinition!.metadata.layout.$items[0].$layout!.$items[0].$layout!.$items).toEqual([
                        expect.objectContaining({ $bind: 'aNumericField' }),
                        expect.objectContaining({ $bind: 'aTableField' }),
                        expect.objectContaining({ $bind: 'anotherSampleTextField' }),
                    ]);
                });
            });

            it('table property extension should add columns to base table', async () => {
                const pageExtensionJsCode = `
                var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                    return c > 3 && r && Object.defineProperty(target, key, r), r;
                };

                const ui = require('@sage/xtrem-ui');

                let ${extensionName} = class ${extensionName} extends ui.PageExtension {
                };

                __decorate([
                    ui.decorators.tableFieldOverride({
                        title: 'This title is set by the extension',
                        width: 'large',
                        columns: [
                            ui.nestedFieldExtensions.text({ bind: 'extensionColumn1' }),
                            ui.nestedFieldExtensions.text({ bind: 'extensionColumn2', insertBefore: 'numericField' }),
                        ]
                    })
                ], ${extensionName}.prototype, "aTableField", void 0);
                `;

                postMock.mockResolvedValue({
                    data: {
                        data: {
                            getDataType: [],
                            getNodeDetailsList: [],
                            getNodeDetails: {},
                            pages: [
                                {
                                    content: pageJsCode,
                                    extensions: [
                                        { content: pageExtensionJsCode, packageName: '@sage/xtrem-test-extension' },
                                    ],
                                },
                            ],
                        },
                    },
                    status: 200,
                    statusText: 'OK',
                    headers: {},
                    config: {},
                });

                const path = `@sage/${moduleName}/${pageName}`;
                const dispatchMock = jest.fn(mockStore.dispatch);
                expect(dispatchMock).not.toHaveBeenCalled();
                expect(errorDialogSpy).not.toHaveBeenCalled();
                const pageDefinition = await screenLoaderService.fetchPageDefinition({
                    getState: mockStore.getState,
                    dispatch: dispatchMock,
                    path,
                });
                expect(pageDefinition).not.toBeNull();
                expect(errorDialogSpy).not.toHaveBeenCalled();

                expect(pageDefinition!.metadata.uiComponentProperties.aTableField.title).toEqual(
                    'This title is set by the extension',
                );

                expect((pageDefinition!.metadata.uiComponentProperties.aTableField as any).columns).toEqual(
                    expect.arrayContaining([
                        expect.objectContaining({
                            properties: expect.objectContaining({ bind: 'textItem' }),
                        }),
                        expect.objectContaining({
                            properties: expect.objectContaining({ bind: 'extensionColumn2' }),
                        }),
                        expect.objectContaining({
                            properties: expect.objectContaining({ bind: 'numericField' }),
                        }),
                        expect.objectContaining({
                            properties: expect.objectContaining({ bind: 'extensionColumn1' }),
                        }),
                    ]),
                );
            });
            it('table property extension should insert dropdownActions and inlineActions in the correct order', async () => {
                const pageExtensionJsCode = `
                var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                    return c > 3 && r && Object.defineProperty(target, key, r), r;
                };

                const ui = require('@sage/xtrem-ui');

                let ${extensionName} = class ${extensionName} extends ui.PageExtension {
                };

                __decorate([
                    ui.decorators.tableFieldOverride({
                        title: 'This title is set by the extension',
                        width: 'large',
                        columns: [
                            ui.nestedFieldExtensions.text({ bind: 'extensionColumn1' }),
                            ui.nestedFieldExtensions.text({ bind: 'extensionColumn2', insertBefore: 'numericField' }),
                        ],
                        dropdownActions: [
                            {
                                id: 'share',
                                icon: 'share',
                                title: 'Share',
                            },
                            {
                                id: 'export',
                                icon: 'download',
                                title: 'Export',
                                insertAfter: 'edit',
                            },
                            {
                                id: 'archive',
                                icon: 'archive',
                                title: 'Archive',
                                insertBefore: 'delete',
                            },
                        ],
                        inlineActions: [
                            {
                                id: 'export',
                                icon: 'download',
                                title: 'Export',
                            },
                            {
                                id: 'share',
                                icon: 'share',
                                title: 'Share',
                                insertAfter: 'view',
                            },
                        ],
                    })
                ], ${extensionName}.prototype, "aTableField", void 0);
                `;
                postMock.mockResolvedValue({
                    data: {
                        data: {
                            getDataType: [],
                            getNodeDetailsList: [],
                            getNodeDetails: {},
                            pages: [
                                {
                                    content: pageJsCode,
                                    extensions: [
                                        { content: pageExtensionJsCode, packageName: '@sage/xtrem-test-extension' },
                                    ],
                                },
                            ],
                        },
                    },
                    status: 200,
                    statusText: 'OK',
                    headers: {},
                    config: {},
                });
                const path = `@sage/${moduleName}/${pageName}`;
                const pageDefinition = await screenLoaderService.fetchPageDefinition({
                    getState: mockStore.getState,
                    dispatch: mockStore.dispatch,
                    path,
                });

                const tableFieldProperties = pageDefinition!.metadata.uiComponentProperties.aTableField as any;
                expect(tableFieldProperties.dropdownActions).toEqual([
                    expect.objectContaining({
                        id: 'edit',
                        icon: 'edit',
                        title: 'Edit',
                    }),
                    expect.objectContaining({
                        id: 'export',
                        icon: 'download',
                        title: 'Export',
                    }),
                    expect.objectContaining({
                        id: 'archive',
                        icon: 'archive',
                        title: 'Archive',
                    }),
                    expect.objectContaining({
                        id: 'delete',
                        icon: 'delete',
                        title: 'Delete',
                    }),
                    expect.objectContaining({
                        id: 'share',
                        icon: 'share',
                        title: 'Share',
                    }),
                ]);

                expect(tableFieldProperties.inlineActions).toEqual([
                    expect.objectContaining({
                        id: 'view',
                        icon: 'eye',
                        title: 'View Details',
                    }),
                    expect.objectContaining({
                        id: 'share',
                        icon: 'share',
                        title: 'Share',
                    }),
                    expect.objectContaining({
                        id: 'export',
                        icon: 'download',
                        title: 'Export',
                    }),
                ]);
            });
        });

        describe('duplication page', () => {
            it('Should set as preffered screen the screenId+Duplicate and after doing stuff unset it', async () => {
                const path = `@sage/${moduleName}/${pageName}`;
                const spySetPreferredScreenId = jest.spyOn(pageMetadata, 'setPreferredScreenId');
                const spyUnsetSetPreferredScreenId = jest.spyOn(pageMetadata, 'unsetPreferredScreenId');

                await screenLoaderService.fetchPageDefinition({
                    getState: mockStore.getState,
                    dispatch: mockStore.dispatch,
                    path,
                    isMainPage: false,
                    isDuplicate: true,
                });
                expect(spySetPreferredScreenId).toHaveBeenCalledWith(`${pageName}Duplicate`);
                expect(spyUnsetSetPreferredScreenId).toHaveBeenCalled();
            });
        });
    });

    describe('Fetch sticker description', () => {
        const stickerName = 'TestSticker';

        beforeEach(() => {
            const stickerJsCode = `xtremArtifact.${stickerName} = (function() {
                    const __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                        return c > 3 && r && Object.defineProperty(target, key, r), r;
                    };
                    const ui = require('@sage/xtrem-ui');

                    class ${stickerName} extends ui.Sticker {
                        constructor() {
                            super(...arguments);
                            //// eslint-disable-next-line no-console
console.log('Sticker constructor is being called');
                        }
                    }

                    ${stickerName} = __decorate([
                        ui.decorators.sticker({
                            title: 'Test sticker',
                            isTransient: true,
                            icon: 'home'
                        })
                    ], ${stickerName});

                    return ${stickerName};
                })()`;

            postMock.mockResolvedValue({
                data: {
                    data: {
                        getDataType: [],
                        getNodeDetailsList: [],
                        getNodeDetails: {},
                        stickers: [
                            {
                                content: stickerJsCode,
                            },
                        ],
                    },
                },
                status: 200,
                statusText: 'OK',
                headers: {},
                config: {},
            });
        });

        it('should retrieve sticker definition', async () => {
            const path = `@sage/${moduleName}/${stickerName}`;
            const stickerDefinition = await screenLoaderService.fetchStickerDefinition(
                mockStore.getState,
                mockStore.dispatch,
                path,
            );
            expect(stickerDefinition).not.toBe(null);
            if (stickerDefinition) {
                expect(stickerDefinition.sticker).not.toBe(null);
                expect(stickerDefinition.sticker.$.sticker).not.toBe(null);
            }
        });

        it('should handle exceptions', async () => {
            // eslint-disable-next-line prefer-promise-reject-errors
            postMock.mockImplementation(() => Promise.reject('Some network error ocurred'));
            const path = `@sage/${moduleName}/${sticker}`;
            const stickerDefinition = await screenLoaderService.fetchStickerDefinition(
                mockStore.getState,
                mockStore.dispatch,
                path,
            );
            expect(stickerDefinition).toBe(null);
        });
    });

    describe('Fetch widget definitions', () => {
        let state;
        beforeEach(() => {
            state = getMockState();
            postMock.mockImplementation((path: string, payload: any) => {
                const className = payload.query.match(/packageOrPage: "@(.*)\/(.*)\/([A-Za-z]*)"/)[3];
                return Promise.resolve({
                    data: {
                        data: {
                            getDataType: [],
                            strings: [
                                {
                                    key: '@sage/xtrem-sales/widgets__deal_size_trend____title',
                                    content: 'Volume des opérations',
                                },
                                {
                                    key: '@sage/xtrem-sales/widgets__deal_size_trend____title_other',
                                    content: 'Volume des opérations other',
                                },
                            ],
                            widgets: [
                                {
                                    content: `xtremArtifact.${className} = (function(){
                                    const ui = require('@sage/xtrem-ui');
                                    var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
                                        var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
                                        if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
                                        else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
                                        return c > 3 && r && Object.defineProperty(target, key, r), r;
                                    };
                                        let ${className} = class ${className} extends ui.widgets.AbstractWidget { };
                                        ${className} = __decorate([
                                            ui.widgets.indicatorTile({
                                                title: 'My widget',
                                                cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
                                                color: ui.tokens.colorsSemanticCaution500,
                                                icon: 'sync',
                                                value() {
                                                    var _a, _b, _c;
                                                    return ((_c = (_b = (_a = this.$.data.xtremSystem.sysPackVersion.query.edges) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.node) === null || _c === void 0 ? void 0 : _c.version) || 'N/A';
                                                },
                                                getQuery() {
                                                    return {
                                                        xtremSystem: {
                                                            sysPackVersion: {
                                                                query: {
                                                                    __args: { filter: "{name:'@sage/xtrem-system'}" },
                                                                    edges: {
                                                                        node: {
                                                                            version: true,
                                                                        },
                                                                    },
                                                                },
                                                            },
                                                        },
                                                    };
                                                },
                                            })
                                        ], ${className});
                                        return ${className};
                                    })()`,
                                },
                            ],
                        },
                    },
                    status: 200,
                    statusText: 'OK',
                    headers: {},
                    config: {},
                });
            });
        });

        it('should load widgets', async () => {
            const result = await screenLoaderService.fetchWidgetDefinitions(() => state, mockStore.dispatch, [
                '@sage/xtrem-test/MyFirstWidget',
                '@sage/xtrem-test/MySecond',
            ]);
            expect(Object.keys(result!)).toEqual(['@sage/xtrem-test/MyFirstWidget', '@sage/xtrem-test/MySecond']);
            expect(result!['@sage/xtrem-test/MyFirstWidget']).toBeInstanceOf(Function);
            expect(result!['@sage/xtrem-test/MySecond']).toBeInstanceOf(Function);
        });

        it('should load the localized strings to the state', async () => {
            const dispatchMock = jest.fn(mockStore.dispatch);
            expect(dispatchMock).not.toHaveBeenCalled();
            await screenLoaderService.fetchWidgetDefinitions(() => state, dispatchMock, [
                '@sage/xtrem-test/MyFirstWidget',
                '@sage/xtrem-test/MySecond',
            ]);
            expect(dispatchMock).toHaveBeenCalledTimes(2);
            expect(dispatchMock).toHaveBeenCalledWith({
                type: 'AddTranslations',
                value: [
                    'en-US',
                    {
                        '@sage/xtrem-sales/widgets__deal_size_trend____title': 'Volume des opérations',
                        '@sage/xtrem-sales/widgets__deal_size_trend____title_other': 'Volume des opérations other',
                    },
                ],
            });
        });
    });
});
