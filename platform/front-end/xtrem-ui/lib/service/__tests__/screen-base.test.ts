import { getMockPageDefinition, getMockState, getMockPageMetadata, getMockStore } from '../../__tests__/test-helpers';

import type { ReferenceProperties } from '../../component/control-objects';
import type { ReadonlyFieldProperties } from '../../component/readonly-field-control-object';
import * as redux from '../../redux';
import type { Dict } from '@sage/xtrem-shared';
import * as dialogService from '../dialog-service';
import { Page } from '../page';
import type { PageMetadata } from '../page-metadata';
import { Developer<PERSON>pi } from '../screen-base';
import * as transactionService from '../transactions-service';
import * as menuActionsModule from '../../redux/actions/menu-actions';
import { getMockStickerDefinition } from '../../__tests__/test-helpers/common-helpers';
import { FieldKey, GraphQLTypes } from '../../component/types';
import { GraphQLKind } from '../../types';

class MockPage extends Page {
    getDeveloperApi() {
        return this.$;
    }
}

describe('ScreenBase', () => {
    let mockState: any;
    beforeEach(() => {
        jest.clearAllMocks();
        (MockPage as any).prototype.constructor._pageMetadata = getMockPageMetadata();
        mockState = getMockState();
        getMockStore(mockState);
    });

    it('should construct just fine', () => {
        const screen = new MockPage();
        expect(screen).not.toBe(null);
    });

    it('should have the developer API', () => {
        const screen = new MockPage();
        expect(screen.getDeveloperApi() instanceof DeveloperApi).toBe(true);
    });

    describe('Developer API', () => {
        describe('username', () => {
            it('should connect the username to the application context', () => {
                mockState.applicationContext!.login = 'TESTUSER';

                const screen = new MockPage();
                expect(screen.getDeveloperApi().username).toEqual(mockState.applicationContext!.login);
            });

            it('should return null if the username is undefined', () => {
                mockState.applicationContext!.login = undefined;

                const screen = new MockPage();
                expect(screen.getDeveloperApi().username).toBe(null);
            });

            it('should return null if the application context is null', () => {
                mockState.applicationContext = null;

                const screen = new MockPage();
                expect(screen.getDeveloperApi().username).toBe(null);
            });

            it('should trigger the onFinish callback on dialog pages', () => {
                const onFinishMock = jest.fn();
                const screenId = 'TestPage';
                mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId, { onFinish: onFinishMock });
                getMockStore(mockState);

                const screen = new MockPage();
                expect(onFinishMock).not.toHaveBeenCalled();
                screen.$.finish();
                expect(onFinishMock).toHaveBeenCalledTimes(1);

                const myResultObject = { a: 12345, b: 'asdf' };
                screen.$.finish(myResultObject);
                expect(onFinishMock).toHaveBeenCalledTimes(2);
                expect(onFinishMock).toHaveBeenCalledWith(myResultObject);
            });

            it('should trigger the onFinish callback on stickers', () => {
                const onFinishMock = jest.fn();

                mockState = getMockState();
                const screenId = 'TestPage';
                jest.spyOn(menuActionsModule, 'addMenuItem').mockImplementation(jest.fn());
                mockState.applicationContext = {
                    handleNavigation: jest.fn(),
                    updateMenu: jest.fn(),
                };
                mockState.activeDialogs = {
                    1: {
                        buttons: {},
                        content: '',
                        dialogControl: dialogService.createDialog(
                            screenId,
                            'info',
                            '',
                            '',
                            jest.fn(() => ({})),
                        ),
                        dialogId: 1,
                        isSticker: true,
                        level: 'info',
                        screenId,
                        title: '',
                        options: {},
                    },
                };
                mockState.screenDefinitions[screenId] = getMockStickerDefinition(screenId, { onFinish: onFinishMock });
                getMockStore(mockState);
                const screen = new MockPage();
                expect(onFinishMock).not.toHaveBeenCalled();
                screen.$.finish();
                expect(onFinishMock).toHaveBeenCalledTimes(1);
            });
        });

        describe('get values', () => {
            mockState = getMockState();
            const screenId = 'TestPage';

            beforeEach(() => {
                mockState = getMockState();
                mockState.nodeTypes = {
                    MyTestNode: {
                        name: 'MyTestNode',
                        title: 'MyTestNode',
                        properties: {
                            field1: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnOutputType: true,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            field02: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnOutputType: true,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            field3: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnOutputType: true,
                                isOnInputType: true,
                                canFilter: true,
                            },
                        },
                    },
                    TestPage: {
                        name: 'TestPage',
                        title: 'TestPage',
                        properties: {
                            field1: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnOutputType: true,
                                canFilter: true,
                            },
                            field3: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnOutputType: true,
                                canFilter: true,
                            },
                        },
                    },
                };
            });

            it('should get simple non-transient fields values', () => {
                const values = {
                    field1: 'test01',
                    field2: 'test02',
                    field3: 'test03',
                };
                const uiComponentProperties: Dict<ReadonlyFieldProperties> = {
                    field1: {},
                    field2: { isTransient: true },
                    field3: {},
                    [screenId]: {},
                };
                mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
                    values,
                    metadata: { uiComponentProperties, controlObjects: {} } as PageMetadata,
                });
                getMockStore(mockState);
                const screen = new MockPage();

                const expectedValues = {
                    field1: 'test01',
                    field2: 'test02',
                    field3: 'test03',
                };
                expect(screen.$.values).toEqual(expectedValues);
            });
            it('should get fields values and remove _id from field2 Arrays (table)', () => {
                const values = {
                    field1: 'test01',
                    field2: [
                        { _id: 1, id: 1, product: 'laser' },
                        { _id: 2, id: 2, product: 'table' },
                    ],
                    field3: 'test03',
                };
                const uiComponentProperties: Dict<ReadonlyFieldProperties> = {
                    field1: {},
                    field2: {},
                    field3: {},
                    [screenId]: {},
                };
                mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
                    values,
                    metadata: { uiComponentProperties, controlObjects: {} } as PageMetadata,
                });
                getMockStore(mockState);
                const screen = new MockPage();

                const expectedValues = {
                    field1: 'test01',
                    field2: [
                        { _id: 1, id: 1, product: 'laser' },
                        { _id: 2, id: 2, product: 'table' },
                    ],
                    field3: 'test03',
                };
                expect(screen.$.values).toEqual(expectedValues);
            });
            it('should get fields values and use _id as value for field2 because is ReferenceField with node property', () => {
                const values = {
                    field1: 'test01',
                    field2: { _id: 'composite~record~id', code: 'code01' },
                    field3: 'test03',
                };
                const uiComponentProperties: Dict<ReadonlyFieldProperties> = {
                    field1: {},
                    field2: { node: '@sage/somepackage' } as ReferenceProperties<any>,
                    field3: {},
                    [screenId]: {},
                };
                mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
                    values,
                    metadata: { uiComponentProperties, controlObjects: {} } as PageMetadata,
                });
                getMockStore(mockState);
                const screen = new MockPage();

                const expectedValues = {
                    field1: 'test01',
                    field2: '_id:composite~record~id',
                    field3: 'test03',
                };
                expect(screen.$.values).toEqual(expectedValues);
            });
            it('should get fields values and set data as value for field2 without _id', () => {
                const values = {
                    field1: 'test01',
                    field2: {
                        _id: 'id01',
                        id: 'id01',
                        code: 'code01',
                        data: [
                            { _id: 1, id: 1, product: 'table' },
                            { _id: 2, id: 2, product: 'table2' },
                        ],
                    },
                    field3: 'test03',
                };
                const uiComponentProperties: Dict<ReadonlyFieldProperties> = {
                    field1: {},
                    field2: {} as ReferenceProperties<any>,
                    field3: {},
                    [screenId]: {},
                };
                mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
                    values,
                    metadata: { uiComponentProperties, controlObjects: {} } as PageMetadata,
                });
                getMockStore(mockState);
                const screen = new MockPage();

                const expectedValues = {
                    field1: 'test01',
                    field2: [
                        { _id: 1, id: 1, product: 'table' },
                        { _id: 2, id: 2, product: 'table2' },
                    ],
                    field3: 'test03',
                };
                expect(screen.$.values).toEqual(expectedValues);
            });
            it('should get fields values and remove _id for field2', () => {
                const values = {
                    field1: 'test01',
                    field2: {
                        _id: 'id01',
                        id: 'id01',
                        code: 'code01',
                        product: { id: '01', name: 'table' },
                    },
                    field3: 'test03',
                };
                const uiComponentProperties: Dict<ReadonlyFieldProperties> = {
                    field1: {},
                    field2: {} as ReferenceProperties<any>,
                    field3: {},
                    [screenId]: {},
                };
                mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
                    values,
                    metadata: { uiComponentProperties, controlObjects: {} } as PageMetadata,
                });
                getMockStore(mockState);
                const screen = new MockPage();

                const expectedValues = {
                    field1: 'test01',
                    field2: { _id: 'id01', code: 'code01', id: 'id01', product: { id: '01', name: 'table' } },
                    field3: 'test03',
                };
                expect(screen.$.values).toEqual(expectedValues);
            });
            it('should get fields values using the bind as key for field2', () => {
                const values = {
                    field1: 'test01',
                    field2: 'test02',
                    field3: 'test03',
                };
                const uiComponentProperties: Dict<ReadonlyFieldProperties> = {
                    field1: {
                        _controlObjectType: FieldKey.Text,
                    },
                    field2: {
                        _controlObjectType: FieldKey.Text,
                        bind: 'bind02',
                    },
                    field3: {
                        _controlObjectType: FieldKey.Text,
                    },
                    [screenId]: {},
                };
                mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
                    values,
                    metadata: { uiComponentProperties, controlObjects: {} } as PageMetadata,
                });
                getMockStore(mockState);
                const screen = new MockPage();

                const expectedValues = {
                    field1: 'test01',
                    bind02: 'test02',
                    field3: 'test03',
                };
                expect(screen.$.values).toEqual(expectedValues);
            });

            it('should get only field values that are present on the server side input type if a node is set', () => {
                const values = {
                    field1: 'test01',
                    field2: 'test02',
                    field3: 'test03',
                };
                const uiComponentProperties: Dict<ReadonlyFieldProperties> = {
                    field1: {
                        _controlObjectType: FieldKey.Text,
                    },
                    field2: {
                        _controlObjectType: FieldKey.Text,
                    },
                    field3: {
                        _controlObjectType: FieldKey.Text,
                    },
                    [screenId]: {
                        node: '@sage/xtrem-fixture/MyTestNode',
                    } as any,
                };
                mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
                    values,
                    metadata: { uiComponentProperties, controlObjects: {} } as PageMetadata,
                });
                mockState.nodeTypes = {
                    MyTestNode: {
                        name: 'MyTestNode',
                        title: 'MyTestNode',
                        properties: {
                            field1: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                            field3: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                        },
                        mutations: {},
                    },
                };
                getMockStore(mockState);
                const screen = new MockPage();

                const expectedValues = {
                    field1: 'test01',
                    field3: 'test03',
                };
                expect(screen.$.values).toEqual(expectedValues);
            });
        });
        describe('set values', () => {
            mockState = getMockState();
            const screenId = 'TestPage';

            beforeEach(() => {
                mockState = getMockState();
            });
            it('should call setFieldValues action for each object property', () => {
                const values = {
                    field1: 'oldtest01',
                    field2: 'oldtest02',
                    field3: 'oldtest03',
                };
                const uiComponentProperties: Dict<ReadonlyFieldProperties> = {
                    field1: {},
                    field2: {},
                    field3: {},
                };
                mockState.nodeTypes = {
                    TestPage: {
                        field1: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                        field2: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                        field3: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                        field4: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar, isOnInputType: true },
                    },
                };
                mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId, {
                    values,
                    metadata: { uiComponentProperties, controlObjects: {} } as PageMetadata,
                });
                getMockStore(mockState);
                const setFieldSpy = jest.spyOn(transactionService, 'setFieldValue');
                const screen = new MockPage();

                screen.$.values = {
                    field1: 'newtest01',
                    field2: 'newtest02',
                    field3: 'newtest03',
                };
                expect(setFieldSpy).toHaveBeenCalledTimes(3);
                expect(setFieldSpy.mock.calls[0]).toEqual([screenId, 'field1', 'newtest01']);
                expect(setFieldSpy.mock.calls[1]).toEqual([screenId, 'field2', 'newtest02']);
                expect(setFieldSpy.mock.calls[2]).toEqual([screenId, 'field3', 'newtest03']);
            });
        });
        describe('refresh navigation panel manually', () => {
            it('should dispatch refreshNavigationPanel action', () => {
                const screen = new MockPage();

                expect(redux.actions.refreshNavigationPanel).not.toHaveBeenCalled();

                screen.$.refreshNavigationPanel();

                expect(redux.actions.refreshNavigationPanel).toHaveBeenCalled();
            });
        });
    });
});
