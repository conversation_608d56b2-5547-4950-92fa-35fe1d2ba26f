import { getMockState, getMockStore } from '../../__tests__/test-helpers';

import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../redux';
import * as toastService from '../toast-service';

describe('Toast service', () => {
    let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

    beforeEach(() => {
        const state = getMockState();
        mockStore = getMockStore(state, false);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('showToast must dispatch showToast action', () => {
        const content = 'Toast message';
        const options: toastService.ToastOptions = {
            type: 'success',
            timeout: 1000,
        };
        const expectedAction: xtremRedux.AppAction = {
            type: xtremRedux.ActionType.ShowToast,
            value: {
                content,
                id: 1,
                isDismissed: false,
                timeout: options.timeout!,
                type: options.type!,
                language: 'markdown',
            },
        };

        toastService.showToast(content, options);
        const actions = mockStore.getActions();

        expect(actions[0]).toEqual(expectedAction);
    });

    it('showToast must dispatch showToast action with default values', () => {
        const content = 'Toast message';
        const expectedAction: xtremRedux.AppAction = {
            type: xtremRedux.ActionType.ShowToast,
            value: {
                content,
                id: 2,
                isDismissed: false,
                timeout: 4000,
                type: 'info',
                language: 'markdown',
            },
        };

        toastService.showToast(content);
        const actions = mockStore.getActions();

        expect(actions[0]).toEqual(expectedAction);
    });

    it('showToast must dispatch showToast action with default values', () => {
        const expectedAction: xtremRedux.AppAction = {
            type: xtremRedux.ActionType.RemoveToasts,
            value: null,
        };

        toastService.removeToasts();
        const actions = mockStore.getActions();

        expect(actions[0]).toEqual(expectedAction);
    });
});
