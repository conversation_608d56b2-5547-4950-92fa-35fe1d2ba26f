import { applyActionMocks, getMockPageDefinition, getMockState, getMockStore } from '../../__tests__/test-helpers';

import type { PageDecoratorProperties } from '../../component/decorators';
import * as actions from '../../redux/actions';
import type { XtremAppState } from '../../redux/state';
import { NEW_PAGE } from '../../utils/constants';
import * as dirtyStateService from '../dirty-state-service';
import type { PageDefinition } from '../page-definition';
import * as router from '../router';

describe('Router API', () => {
    const screenId = 'MyTestPage';
    let routerInstance: router.Router;
    let state: XtremAppState;
    let openDirtyPageConfirmationDialogMock: jest.MockInstance<any, any>;
    let windowSpy;
    beforeEach(() => {
        routerInstance = router.getRouter(screenId);
        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] = {};
        getMockStore(state);

        openDirtyPageConfirmationDialogMock = jest
            .spyOn(dirtyStateService, 'openDirtyPageConfirmationDialog')
            .mockResolvedValue({});

        windowSpy = jest.spyOn(window, 'open');
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
        windowSpy.mockRestore();
    });

    describe('actions on non-dirty page', () => {
        it('should trigger navigate with the right arguments when goTo called', async () => {
            expect(actions.navigate).not.toHaveBeenCalled();
            await routerInstance.goTo('@sage/my-test-package/MyTestPage', { someRandomParams: 123 });
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.navigate).toHaveBeenCalledWith(
                '@sage/my-test-package/MyTestPage',
                {
                    someRandomParams: 123,
                },
                false,
            );
        });

        it('should trigger selectRecord with the right arguments when refresh called and no record id is selected', async () => {
            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.refresh();
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', null, true);
        });

        it('should trigger selectRecord with the right arguments when refresh called and a record id is selected', async () => {
            (state.screenDefinitions[screenId] as PageDefinition).queryParameters = { _id: 3223 };
            getMockStore(state);

            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.refresh();
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', '3223', true);
        });

        it('should trigger selectRecord with null argument when emptyPage called is called', async () => {
            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.emptyPage();
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', NEW_PAGE);
        });

        it('should trigger selectRecord with record id when selectRecord called is called', async () => {
            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.selectRecord('12345');
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', '12345', false);
        });

        it('should trigger selectRecord with null argument when closeRecord called is called', async () => {
            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.closeRecord();
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', null);
        });

        it('should trigger window.open with the right arguments when goToExternal is called (external link)', async () => {
            windowSpy.mockImplementation(() => ({
                location: {
                    origin: 'https://www.google.com',
                },
            }));
            await routerInstance.goToExternal('https://www.google.com');
            expect(windowSpy).toHaveBeenCalled();
            expect(window.open).toHaveBeenCalledWith('https://www.google.com', '_blank', 'noopener=true');
        });

        it('should trigger window.open with the right arguments when goToExternal is called (internal link)', async () => {
            windowSpy.mockImplementation(() => ({
                location: {
                    origin: '@sage/xtrem-show-case/ShowCaseProduct',
                },
            }));
            await routerInstance.goToExternal('@sage/xtrem-show-case/ShowCaseProduct');
            expect(windowSpy).toHaveBeenCalled();
            expect(window.open).toHaveBeenCalledWith(
                '@sage/xtrem-show-case/ShowCaseProduct',
                '_blank',
                'noopener=true',
            );
        });

        it('should throw error when a wrong link format is provided when goToExternal is called (external link)', async () => {
            expect(() => {
                routerInstance.goToExternal('google.com');
            }).toThrow('Unexpected link format.');
        });

        it('should throw error when a wrong link format is provided when goToExternal is called (internal link)', async () => {
            expect(() => {
                routerInstance.goToExternal('xtrem-show-case/ShowCaseProduct');
            }).toThrow('Unexpected link format.');
        });
    });

    describe('actions on dirty page with skip validation set by the functions', () => {
        beforeEach(() => {
            const store = getMockStore(state);

            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] = {
                skipDirtyCheck: true,
            } as PageDecoratorProperties<any>;

            jest.spyOn(store, 'dispatch').mockImplementation(() => Promise.resolve() as any);
            state.screenDefinitions[screenId].dirtyStates = {
                someField: true,
            };
        });

        it('should trigger navigate with the right arguments when goTo called', async () => {
            expect(actions.navigate).not.toHaveBeenCalled();
            await routerInstance.goTo('@sage/my-test-package/MyTestPage', { someRandomParams: 123 });
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.navigate).toHaveBeenCalledWith(
                '@sage/my-test-package/MyTestPage',
                {
                    someRandomParams: 123,
                },
                false,
            );
        });

        it('should trigger selectRecord with the right arguments when refresh called and no record id is selected', async () => {
            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.refresh();
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', null, true);
        });

        it('should trigger selectRecord with the right arguments when refresh called and a record id is selected', async () => {
            (state.screenDefinitions[screenId] as PageDefinition).queryParameters = { _id: 3223 };
            getMockStore(state);

            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.refresh();
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', '3223', true);
        });

        it('should trigger selectRecord with null argument when emptyPage called is called', async () => {
            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.emptyPage();
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', NEW_PAGE);
        });

        it('should trigger selectRecord with record id when selectRecord called is called', async () => {
            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.selectRecord('12345');
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', '12345', false);
        });
    });

    describe('actions on dirty page with skip validation set by the page', () => {
        beforeEach(() => {
            const store = getMockStore(state);
            jest.spyOn(store, 'dispatch').mockImplementation(() => Promise.resolve() as any);
            state.screenDefinitions[screenId].dirtyStates = {
                someField: true,
            };
        });

        it('should trigger navigate with the right arguments when goTo called', async () => {
            expect(actions.navigate).not.toHaveBeenCalled();
            await routerInstance.goTo('@sage/my-test-package/MyTestPage', { someRandomParams: 123 }, true);
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.navigate).toHaveBeenCalledWith(
                '@sage/my-test-package/MyTestPage',
                {
                    someRandomParams: 123,
                },
                false,
            );
        });

        it('should trigger selectRecord with the right arguments when refresh called and no record id is selected', async () => {
            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.refresh(true);
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', null, true);
        });

        it('should trigger selectRecord with the right arguments when refresh called and a record id is selected', async () => {
            (state.screenDefinitions[screenId] as PageDefinition).queryParameters = { _id: 3223 };
            getMockStore(state);

            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.refresh(true);
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', '3223', true);
        });

        it('should trigger selectRecord with null argument when emptyPage called is called', async () => {
            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.emptyPage(true);
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', NEW_PAGE);
        });

        it('should trigger selectRecord with record id when selectRecord called is called', async () => {
            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.selectRecord('12345', true);
            expect(openDirtyPageConfirmationDialogMock).not.toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', '12345', false);
        });
    });

    describe('actions on dirty page', () => {
        beforeEach(() => {
            const store = getMockStore(state);
            jest.spyOn(store, 'dispatch').mockImplementation(() => Promise.resolve() as any);
            state.screenDefinitions[screenId].dirtyStates = {
                someField: true,
            };
        });

        it('should trigger navigate with the right arguments when goTo called', async () => {
            expect(actions.navigate).not.toHaveBeenCalled();
            await routerInstance.goTo('@sage/my-test-package/MyTestPage', { someRandomParams: 123 });
            expect(actions.navigate).toHaveBeenCalledWith(
                '@sage/my-test-package/MyTestPage',
                {
                    someRandomParams: 123,
                },
                false,
            );
            expect(openDirtyPageConfirmationDialogMock).toHaveBeenCalled();
        });

        it('should trigger selectRecord with the right arguments when refresh called and no record id is selected', async () => {
            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.refresh();
            expect(openDirtyPageConfirmationDialogMock).toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', null, true);
        });

        it('should trigger selectRecord with the right arguments when refresh called and a record id is selected', async () => {
            (state.screenDefinitions[screenId] as PageDefinition).queryParameters = { _id: 3223 };
            const store = getMockStore(state);
            jest.spyOn(store, 'dispatch').mockImplementation(() => Promise.resolve() as any);

            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.refresh();
            expect(openDirtyPageConfirmationDialogMock).toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', '3223', true);
        });

        it('should trigger selectRecord with null argument when emptyPage called is called', async () => {
            expect(actions.selectRecord).not.toHaveBeenCalled();
            await routerInstance.emptyPage();
            expect(openDirtyPageConfirmationDialogMock).toHaveBeenCalled();
            expect(actions.selectRecord).toHaveBeenCalledWith('MyTestPage', NEW_PAGE);
        });

        it('should trigger goHome after dirty dialog confirmation', async () => {
            expect(actions.goHome).not.toHaveBeenCalled();
            await routerInstance.goHome();
            expect(openDirtyPageConfirmationDialogMock).toHaveBeenCalled();
            expect(actions.goHome).toHaveBeenCalled();
        });
    });
});
