// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`graphql-service fetchEnumTranslations should fetch and format the enum translations from the server 1`] = `
Object {
  "cacheSettings": undefined,
  "endpoint": "/metadata",
  "query": Object {
    "strings": Object {
      "__args": Object {
        "filter": Object {
          "packageOrPage": "enums__my_test_enum",
        },
      },
      "content": true,
      "key": true,
    },
  },
}
`;
