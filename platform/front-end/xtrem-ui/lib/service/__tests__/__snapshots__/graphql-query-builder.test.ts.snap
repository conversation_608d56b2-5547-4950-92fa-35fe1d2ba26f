// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`graphql-query-builder buildEnumQueries should generate a valid query to check enums of various types 1`] = `
"query {
    CalendarLeftNode: __type (name: \\"CalendarLeftNode\\") {
        enumValues {
            name
        }
    }
}"
`;

exports[`graphql-query-builder buildEnumQuery should generate a query object to receive the enums of a property 1`] = `
"query {
    OrderCityEnum: __type (name: \\"OrderCityEnum\\") {
        enumValues {
            name
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for Aggregate field query with default and user filter 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    readAggregate (filter: \\"{\\\\\\"_and\\\\\\":[{\\\\\\"numericProperty2\\\\\\":{\\\\\\"_lte\\\\\\":\\\\\\"10\\\\\\"}},{\\\\\\"numericProperty2\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"50\\\\\\"}}]}\\") {
                        anyRandomField {
                            distinctCount
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for Aggregate field query without a filter 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    readAggregate {
                        anyRandomField {
                            distinctCount
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for Aggregate field query without a filter 2`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    readAggregate (filter: \\"{\\\\\\"_and\\\\\\":[{\\\\\\"numericProperty1\\\\\\":{\\\\\\"_lte\\\\\\":\\\\\\"1\\\\\\"}},{\\\\\\"numericProperty1\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"5\\\\\\"}}]}\\") {
                        anyRandomField {
                            distinctCount
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for Chart field query with default and user filter 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    query (filter: \\"{\\\\\\"_and\\\\\\":[{\\\\\\"_and\\\\\\":[{\\\\\\"numericProperty1\\\\\\":{\\\\\\"_lte\\\\\\":\\\\\\"1\\\\\\"}},{\\\\\\"numericProperty1\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"5\\\\\\"}}]},{\\\\\\"_and\\\\\\":[{\\\\\\"numericProperty2\\\\\\":{\\\\\\"_lte\\\\\\":\\\\\\"10\\\\\\"}},{\\\\\\"numericProperty2\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"50\\\\\\"}}]}]}\\", orderBy: \\"{\\\\\\"xAxis\\\\\\":1}\\") {
                        edges {
                            node {
                                numericProperty2
                                numericProperty1
                                xAxis
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for Chart field query with default filter 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    query (filter: \\"{\\\\\\"_and\\\\\\":[{\\\\\\"numericProperty1\\\\\\":{\\\\\\"_lte\\\\\\":\\\\\\"1\\\\\\"}},{\\\\\\"numericProperty1\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"5\\\\\\"}}]}\\", orderBy: \\"{\\\\\\"xAxis\\\\\\":1}\\") {
                        edges {
                            node {
                                numericProperty2
                                numericProperty1
                                xAxis
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for Table field query with default filter 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    query (first: 20, orderBy: \\"{\\\\\\"_id\\\\\\":-1}\\", filter: \\"{\\\\\\"textProperty\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"bindProp\\\\\\"}}\\") {
                        edges {
                            node {
                                numericProperty
                                textProperty
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for Table field should build a query with an option menu 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    query (first: 20, orderBy: \\"{\\\\\\"_id\\\\\\":-1}\\", filter: \\"{\\\\\\"orderDate\\\\\\":{\\\\\\"_lt\\\\\\":\\\\\\"2022-02-22\\\\\\"}}\\") {
                        edges {
                            node {
                                numericProperty
                                textProperty
                                _id
                                orderDate
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for Table field should build a query with an option menu and filter combined 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    query (first: 20, orderBy: \\"{\\\\\\"_id\\\\\\":-1}\\", filter: \\"{\\\\\\"_and\\\\\\":[{\\\\\\"orderDate\\\\\\":{\\\\\\"_gt\\\\\\":\\\\\\"2022-02-24\\\\\\"}},{\\\\\\"orderDate\\\\\\":{\\\\\\"_lt\\\\\\":\\\\\\"2022-02-22\\\\\\"}}]}\\") {
                        edges {
                            node {
                                numericProperty
                                textProperty
                                _id
                                orderDate
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for complex aggregate field with nested aggregation query with default and user filter 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    readAggregate (filter: \\"{\\\\\\"_and\\\\\\":[{\\\\\\"numericProperty2\\\\\\":{\\\\\\"_lte\\\\\\":\\\\\\"10\\\\\\"}},{\\\\\\"numericProperty2\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"50\\\\\\"}}]}\\") {
                        anyRandomField {
                            withAdditionalChild {
                                property {
                                    max
                                }
                            }
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for complex aggregate field with nested aggregation query without a filter 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    readAggregate {
                        anyRandomField {
                            withAdditionalChild {
                                property {
                                    max
                                }
                            }
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for complex aggregate field with nested aggregation query without a filter 2`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    readAggregate (filter: \\"{\\\\\\"_and\\\\\\":[{\\\\\\"numericProperty1\\\\\\":{\\\\\\"_lte\\\\\\":\\\\\\"1\\\\\\"}},{\\\\\\"numericProperty1\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"5\\\\\\"}}]}\\") {
                        anyRandomField {
                            withAdditionalChild {
                                property {
                                    max
                                }
                            }
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for pod collection field query with header label 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    query (first: 20, orderBy: \\"{\\\\\\"_id\\\\\\":-1}\\") {
                        edges {
                            node {
                                orderCategory
                                expectedDeliveryDate
                                product {
                                    _id
                                    code
                                    description1
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for pod collection field query without a filter 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    query (first: 20, orderBy: \\"{\\\\\\"_id\\\\\\":-1}\\") {
                        edges {
                            node {
                                expectedDeliveryDate
                                product {
                                    _id
                                    code
                                    description1
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for pod collection field query without a filter 2`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    query (first: 20, orderBy: \\"{\\\\\\"_id\\\\\\":-1}\\") {
                        edges {
                            node {
                                expectedDeliveryDate
                                product {
                                    _id
                                    code
                                    description1
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for vital pod field query with header label 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    orderCategory
                    expectedDeliveryDate
                    product {
                        _id
                        code
                        description1
                    }
                    _id
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for vital pod field query without a filter 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    expectedDeliveryDate
                    product {
                        _id
                        code
                        description1
                    }
                    _id
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildFieldQuery should build a query for vital pod field query without a filter 2`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"\\\\\\"}}\\") {
        edges {
            node {
                fieldId {
                    expectedDeliveryDate
                    product {
                        _id
                        code
                        description1
                    }
                    _id
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery Calendar component should build a query for a calendar page 1`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            calendar {
                query (first: 100, filter: \\"{\\\\\\"startDate\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"2019-10-26\\\\\\",\\\\\\"_lte\\\\\\":\\\\\\"2019-12-05\\\\\\"}}\\") {
                    edges {
                        node {
                            _id
                            calendarTitle
                            startDate
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery Calendar component should build a query for a calendar page having elementId distinct from bind 1`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            calendar: calendarBind {
                query (first: 100, filter: \\"{\\\\\\"startDate\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"2019-10-26\\\\\\",\\\\\\"_lte\\\\\\":\\\\\\"2019-12-05\\\\\\"}}\\") {
                    edges {
                        node {
                            _id
                            calendarTitle
                            startDate
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery Calendar component should build a query for a calendar page setting endDateField 1`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            calendar {
                query (first: 100, filter: \\"{\\\\\\"_or\\\\\\":[{\\\\\\"startDate\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"2019-10-26\\\\\\",\\\\\\"_lte\\\\\\":\\\\\\"2019-12-05\\\\\\"}},{\\\\\\"_and\\\\\\":[{\\\\\\"startDate\\\\\\":{\\\\\\"_lte\\\\\\":\\\\\\"2019-12-05\\\\\\"}},{\\\\\\"endDate\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"2019-10-26\\\\\\"}}]}]}\\") {
                    edges {
                        node {
                            _id
                            calendarTitle
                            startDate
                            endDate
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery Calendar component should build a query for a calendar page setting left 1`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            calendar {
                query (first: 100, filter: \\"{\\\\\\"startDate\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"2019-10-26\\\\\\",\\\\\\"_lte\\\\\\":\\\\\\"2019-12-05\\\\\\"}}\\") {
                    edges {
                        node {
                            _id
                            right
                            calendarTitle
                            startDate
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery Calendar component should build a query for a calendar page setting left 2`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            calendar {
                query (first: 100, filter: \\"{\\\\\\"startDate\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"2019-10-26\\\\\\",\\\\\\"_lte\\\\\\":\\\\\\"2019-12-05\\\\\\"}}\\") {
                    edges {
                        node {
                            _id
                            left
                            calendarTitle
                            startDate
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery Calendar component should build a query for a calendar page setting line2 1`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            calendar {
                query (first: 100, filter: \\"{\\\\\\"startDate\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"2019-10-26\\\\\\",\\\\\\"_lte\\\\\\":\\\\\\"2019-12-05\\\\\\"}}\\") {
                    edges {
                        node {
                            _id
                            calendarSubtitle
                            calendarTitle
                            startDate
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery Calendar component should build a query for a calendar page setting rangeEnd 1`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            calendar {
                query (first: 100, filter: \\"{\\\\\\"startDate\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"2019-10-26\\\\\\",\\\\\\"_lte\\\\\\":\\\\\\"2019-11-28\\\\\\"}}\\") {
                    edges {
                        node {
                            _id
                            calendarTitle
                            startDate
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery Calendar component should build a query for a calendar page setting rangeEnd as Date 1`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            calendar {
                query (first: 100, filter: \\"{\\\\\\"startDate\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"2019-10-26\\\\\\",\\\\\\"_lte\\\\\\":\\\\\\"2019-11-28\\\\\\"}}\\") {
                    edges {
                        node {
                            _id
                            calendarTitle
                            startDate
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery Calendar component should build a query for a calendar page setting rangeStart 1`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            calendar {
                query (first: 100, filter: \\"{\\\\\\"startDate\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"2019-11-28\\\\\\",\\\\\\"_lte\\\\\\":\\\\\\"2019-12-05\\\\\\"}}\\") {
                    edges {
                        node {
                            _id
                            calendarTitle
                            startDate
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery Calendar component should build a query for a calendar page setting rangeStart as Date 1`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            calendar {
                query (first: 100, filter: \\"{\\\\\\"startDate\\\\\\":{\\\\\\"_gte\\\\\\":\\\\\\"2019-11-28\\\\\\",\\\\\\"_lte\\\\\\":\\\\\\"2019-12-05\\\\\\"}}\\") {
                    edges {
                        node {
                            _id
                            calendarTitle
                            startDate
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery should build a query for a basic page 1`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            orderNumber
            isSigned
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery should build a query for a complex page 1`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            lines {
                query (first: 20, orderBy: \\"{\\\\\\"_id\\\\\\":-1}\\") {
                    edges {
                        node {
                            orderCategory
                            isInvoiced
                            orderedQuantity
                            expectedDeliveryDate
                            product {
                                _id
                                code
                                description1
                            }
                            _id
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
            productImage {
                value
            }
            revisionNumber
            orderDate
            billToCustomer {
                _id
                code
                companyName
            }
            orderNumber
            isSigned
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildRootNodeQuery should build a query for a complex page with args 1`] = `
"query {
    testObject {
        read (_id: \\"1\\") {
            lines {
                query (first: 20, orderBy: \\"{\\\\\\"expectedDeliveryDate\\\\\\":-1,\\\\\\"product\\\\\\":1}\\", filter: \\"{\\\\\\"_or\\\\\\":[{\\\\\\"textField\\\\\\":{\\\\\\"_regex\\\\\\":\\\\\\"$TEST\\\\\\"}}]}\\") {
                    edges {
                        node {
                            orderCategory
                            isInvoiced
                            orderedQuantity
                            expectedDeliveryDate
                            product {
                                _id
                                code
                                description1
                            }
                            _id
                            textField
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
            productImage {
                value
            }
            revisionNumber
            orderDate
            billToCustomer {
                _id
                code
                companyName
            }
            orderNumber
            isSigned
            _etag
            _id
        }
    }
}"
`;

exports[`graphql-query-builder buildTableQuery should build a table query with filter 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"testNode\\\\\\"}}\\") {
        edges {
            node {
                _id
                testId1 {
                    query (filter: \\"{\\\\\\"item\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"test\\\\\\"}}\\") {
                        edges {
                            node {
                                quantity
                                item
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder queryBuilder Should generate a query object with _atLeast quantifier in filter 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"SalesOrder:QTEFR0110040\\\\\\"},\\\\\\"lines\\\\\\":{\\\\\\"_atLeast\\\\\\":3,\\\\\\"orderQuantity\\\\\\":{\\\\\\"_gt\\\\\\":50}}}\\", first: 10) {
        edges {
            node {
                _id
                orderDate
                orderCountryName
                __all_Lines: lines {
                    query {
                        totalCount
                    }
                }
                __restricted_Lines: lines {
                    query (filter: \\"{\\\\\\"orderQuantity\\\\\\":{\\\\\\"_gt\\\\\\":50}}\\") {
                        totalCount
                    }
                }
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder queryBuilder Should generate a query object with alias 1`] = `
"query: salesOrder {
    query {
        edges {
            node {
                _id
                orderDate
                orderCountryName
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder queryBuilder Should generate a query object with arguments  1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"SalesOrder:QTEFR0110040\\\\\\"}}\\", first: 10) {
        edges {
            node {
                _id
                orderDate
                orderCountryName
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder queryBuilder Should generate a query object with both simple & reference attributes 1`] = `
"query {
    query {
        edges {
            node {
                payByCustomer {
                    code
                    currency {
                        iso
                    }
                }
                billToCustomer {
                    code
                    companyName
                }
                _id
                orderDate
                orderCountryName
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder queryBuilder Should generate a query object with complex _atLeast quantifier in filter 1`] = `
"query {
    query (filter: \\"{\\\\\\"_id\\\\\\":{\\\\\\"_eq\\\\\\":\\\\\\"SalesOrder:QTEFR0110040\\\\\\"},\\\\\\"_and\\\\\\":[{\\\\\\"lines\\\\\\":{\\\\\\"_atLeast\\\\\\":3,\\\\\\"product\\\\\\":{\\\\\\"qty\\\\\\":{\\\\\\"_gt\\\\\\":15}},\\\\\\"orderQuantity\\\\\\":{\\\\\\"_gt\\\\\\":50}},\\\\\\"newLines\\\\\\":{\\\\\\"_atLeast\\\\\\":3,\\\\\\"_and\\\\\\":[{\\\\\\"newProduct\\\\\\":{\\\\\\"qty\\\\\\":{\\\\\\"_gt\\\\\\":15}},\\\\\\"newOrderQuantity\\\\\\":{\\\\\\"_gt\\\\\\":50}}]}}]}\\", first: 10) {
        edges {
            node {
                _id
                orderDate
                orderCountryName
                __all_Lines: lines {
                    query {
                        totalCount
                    }
                }
                __restricted_Lines: lines {
                    query (filter: \\"{\\\\\\"product\\\\\\":{\\\\\\"qty\\\\\\":{\\\\\\"_gt\\\\\\":15}},\\\\\\"orderQuantity\\\\\\":{\\\\\\"_gt\\\\\\":50}}\\") {
                        totalCount
                    }
                }
                __all_NewLines: newLines {
                    query {
                        totalCount
                    }
                }
                __restricted_NewLines: newLines {
                    query (filter: \\"{\\\\\\"_and\\\\\\":[{\\\\\\"newProduct\\\\\\":{\\\\\\"qty\\\\\\":{\\\\\\"_gt\\\\\\":15}},\\\\\\"newOrderQuantity\\\\\\":{\\\\\\"_gt\\\\\\":50}}]}\\") {
                        totalCount
                    }
                }
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder queryBuilder Should generate query object with reference attributes 1`] = `
"query {
    query {
        edges {
            node {
                payByCustomer {
                    code
                    currency {
                        iso
                    }
                }
                billToCustomer {
                    code
                    companyName
                }
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder queryBuilder Should generate query object with simple attributes 1`] = `
"query {
    query {
        edges {
            node {
                _id
                orderDate
                orderCountryName
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder queryBuilder Should generate query object without properties 1`] = `
"query {
    query {
        edges {
            node {
                _id
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;

exports[`graphql-query-builder queryBuilder should be able to merge two queries and make a valid graphQL query 1`] = `
"query {
    query {
        edges {
            node {
                _id
                task
            }
            cursor
        }
        pageInfo {
            startCursor
            endCursor
            hasPreviousPage
            hasNextPage
        }
    }
}"
`;
