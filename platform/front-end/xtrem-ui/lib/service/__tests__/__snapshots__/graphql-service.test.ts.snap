// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`graphql-service fetchInitialData should fetch the page data from the server if values are provided as an arg 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "rootNode": Object {
      "__aliasFor": "x3Sales",
      "salesOrder": Object {
        "read": Object {
          "__args": Object {
            "_id": "123456",
          },
          "_etag": true,
          "_id": true,
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchInitialData should generate a valid graphQL Filter and do the request  1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "rootNode": Object {
      "__aliasFor": "x3Sales",
      "salesOrder": Object {
        "read": Object {
          "__args": Object {
            "_id": "123456",
          },
          "_etag": true,
          "_id": true,
          "bind01": true,
          "bind02": true,
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchInitialData should generate a valid graphQL Filter with navigationPanelItems & DropDownMenu and do the request, returns the result + mutate pageMetadata 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "navigationPanelItems": Object {
      "__aliasFor": "x3Sales",
      "salesOrder": Object {
        "query": Object {
          "__args": Object {
            "filter": "{\\"bind03\\":{\\"_regex\\":\\"testFilterValue2\\",\\"_options\\":\\"i\\"}}",
            "orderBy": "{\\"bind03\\":{\\"companyName\\":1},\\"_id\\":1}",
          },
          "edges": Object {
            "cursor": true,
            "node": Object {
              "_id": true,
              "bind03": Object {
                "_id": true,
                "code": true,
                "companyName": true,
              },
            },
          },
          "pageInfo": Object {
            "endCursor": true,
            "hasNextPage": true,
            "hasPreviousPage": true,
            "startCursor": true,
          },
        },
      },
    },
    "rootNode": Object {
      "__aliasFor": "x3Sales",
      "salesOrder": Object {
        "read": Object {
          "__args": Object {
            "_id": "123456",
          },
          "_etag": true,
          "_headerTitle": Object {
            "__aliasFor": "bind03",
            "companyName": true,
          },
          "_id": true,
          "bind01": true,
          "bind02": true,
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchInitialData should generate a valid graphQL Filter with navigationPanelItems & DropDownMenu with GraphQLFilter function and do the request, returns the result + mutate pageMetadata 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "navigationPanelItems": Object {
      "__aliasFor": "x3Sales",
      "salesOrder": Object {
        "query": Object {
          "__args": Object {
            "filter": "{\\"_or\\":[{\\"bind01\\":{\\"_regex\\":\\"testGetStorage\\",\\"_options\\":\\"i\\"}},{\\"bind02\\":{\\"_regex\\":\\"testQueryParameters\\",\\"_options\\":\\"i\\"}}]}",
            "orderBy": "{\\"bind03\\":{\\"companyName\\":1},\\"_id\\":1}",
          },
          "edges": Object {
            "cursor": true,
            "node": Object {
              "_id": true,
              "bind01": true,
              "bind02": true,
              "bind03": Object {
                "_id": true,
                "code": true,
                "companyName": true,
              },
            },
          },
          "pageInfo": Object {
            "endCursor": true,
            "hasNextPage": true,
            "hasPreviousPage": true,
            "startCursor": true,
          },
        },
      },
    },
    "rootNode": Object {
      "__aliasFor": "x3Sales",
      "salesOrder": Object {
        "read": Object {
          "__args": Object {
            "_id": "123456",
          },
          "_etag": true,
          "_headerTitle": Object {
            "__aliasFor": "bind03",
            "companyName": true,
          },
          "_id": true,
          "bind01": true,
          "bind02": true,
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchInitialData should generate a valid graphQL Filter with navigationPanelItems & DropDownMenu without GraphQLFilter and do the request, returns the result + mutate pageMetadata 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "navigationPanelItems": Object {
      "__aliasFor": "x3Sales",
      "salesOrder": Object {
        "query": Object {
          "__args": Object {
            "orderBy": "{\\"bind03\\":{\\"companyName\\":1},\\"_id\\":1}",
          },
          "edges": Object {
            "cursor": true,
            "node": Object {
              "_id": true,
              "bind03": Object {
                "_id": true,
                "code": true,
                "companyName": true,
              },
            },
          },
          "pageInfo": Object {
            "endCursor": true,
            "hasNextPage": true,
            "hasPreviousPage": true,
            "startCursor": true,
          },
        },
      },
    },
    "rootNode": Object {
      "__aliasFor": "x3Sales",
      "salesOrder": Object {
        "read": Object {
          "__args": Object {
            "_id": "123456",
          },
          "_etag": true,
          "_headerTitle": Object {
            "__aliasFor": "bind03",
            "companyName": true,
          },
          "_id": true,
          "bind01": true,
          "bind02": true,
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchInitialData should generate a valid graphQL Filter with navigationPanelItems and do the request, returns the result + mutate pageMetadata 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "navigationPanelItems": Object {
      "__aliasFor": "x3Sales",
      "salesOrder": Object {
        "query": Object {
          "__args": Object {
            "orderBy": "{\\"bind03\\":{\\"companyName\\":1},\\"_id\\":1}",
          },
          "edges": Object {
            "cursor": true,
            "node": Object {
              "_id": true,
              "bind03": Object {
                "_id": true,
                "code": true,
                "companyName": true,
              },
            },
          },
          "pageInfo": Object {
            "endCursor": true,
            "hasNextPage": true,
            "hasPreviousPage": true,
            "startCursor": true,
          },
        },
      },
    },
    "rootNode": Object {
      "__aliasFor": "x3Sales",
      "salesOrder": Object {
        "read": Object {
          "__args": Object {
            "_id": "123456",
          },
          "_etag": true,
          "_headerTitle": Object {
            "__aliasFor": "bind03",
            "companyName": true,
          },
          "_id": true,
          "bind01": true,
          "bind02": true,
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchInitialData should only fetch fields in sections that are explicitly requested and orphan fields if the requestedSections option is set 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "rootNode": Object {
      "__aliasFor": "x3Sales",
      "salesOrder": Object {
        "read": Object {
          "__args": Object {
            "_id": "123456",
          },
          "_etag": true,
          "_id": true,
          "field1": true,
          "field3": true,
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchInitialData should return empty data & navigationPanelItems because the no data is returned from the server 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "rootNode": Object {
      "__aliasFor": "x3Sales",
      "salesOrder": Object {
        "read": Object {
          "__args": Object {
            "_id": "123456",
          },
          "_etag": true,
          "_id": true,
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchNavigationPanelData should generate a correct filter with empty and query to graphql and return the data 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "x3Sales": Object {
      "salesOrder": Object {
        "query": Object {
          "__args": Object {
            "first": 30,
            "orderBy": "{\\"bind01\\":1,\\"bind02\\":1,\\"_id\\":1}",
          },
          "edges": Object {
            "cursor": true,
            "node": Object {
              "_id": true,
              "bind01": true,
              "bind02": true,
            },
          },
          "pageInfo": Object {
            "endCursor": true,
            "hasNextPage": true,
            "hasPreviousPage": true,
            "startCursor": true,
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchNavigationPanelData should generate a correct filter with empty filter Value + dropdown filters and query to graphql 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "x3Sales": Object {
      "salesOrder": Object {
        "query": Object {
          "__args": Object {
            "filter": "{\\"orderCity\\":{\\"_regex\\":\\"Paris\\",\\"_options\\":\\"i\\"}}",
            "first": 30,
            "orderBy": "{\\"bind01\\":1,\\"bind02\\":1,\\"_id\\":1}",
          },
          "edges": Object {
            "cursor": true,
            "node": Object {
              "_id": true,
              "bind01": true,
              "bind02": true,
              "orderCity": true,
            },
          },
          "pageInfo": Object {
            "endCursor": true,
            "hasNextPage": true,
            "hasPreviousPage": true,
            "startCursor": true,
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchNavigationPanelData should generate a correct filter with mandatory values + non mandatory + nested filter and query to graphql 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "x3Sales": Object {
      "salesOrder": Object {
        "query": Object {
          "__args": Object {
            "filter": "{\\"orderCity\\":{\\"_regex\\":\\"Paris\\",\\"_options\\":\\"i\\"}}",
            "first": 30,
            "orderBy": "{\\"bind01\\":1,\\"bind02\\":1,\\"_id\\":1}",
          },
          "edges": Object {
            "cursor": true,
            "node": Object {
              "_id": true,
              "bind01": true,
              "bind02": true,
              "orderCity": true,
            },
          },
          "pageInfo": Object {
            "endCursor": true,
            "hasNextPage": true,
            "hasPreviousPage": true,
            "startCursor": true,
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchNavigationPanelData should generate a correct filter with mandatory values + non mandatory and query to graphql 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "x3Sales": Object {
      "salesOrder": Object {
        "query": Object {
          "__args": Object {
            "first": 30,
            "orderBy": "{\\"bind01\\":1,\\"bind02\\":1,\\"_id\\":1}",
          },
          "edges": Object {
            "cursor": true,
            "node": Object {
              "_id": true,
              "bind01": true,
              "bind02": true,
            },
          },
          "pageInfo": Object {
            "endCursor": true,
            "hasNextPage": true,
            "hasPreviousPage": true,
            "startCursor": true,
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchNavigationPanelData should generate a correct filter with mandatory values and query to graphql 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "x3Sales": Object {
      "salesOrder": Object {
        "query": Object {
          "__args": Object {
            "first": 30,
            "orderBy": "{\\"bind01\\":1,\\"_id\\":1}",
          },
          "edges": Object {
            "cursor": true,
            "node": Object {
              "_id": true,
              "bind01": true,
            },
          },
          "pageInfo": Object {
            "endCursor": true,
            "hasNextPage": true,
            "hasPreviousPage": true,
            "startCursor": true,
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchNavigationPanelData should generate a correct filter with reference field and query to graphql 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "x3Sales": Object {
      "salesOrder": Object {
        "query": Object {
          "__args": Object {
            "first": 30,
            "orderBy": "{\\"bind01\\":1,\\"bind02\\":{\\"subBind02\\":1},\\"_id\\":1}",
          },
          "edges": Object {
            "cursor": true,
            "node": Object {
              "_id": true,
              "bind01": true,
              "bind02": Object {
                "_id": true,
                "code": true,
                "subBind02": true,
              },
            },
          },
          "pageInfo": Object {
            "endCursor": true,
            "hasNextPage": true,
            "hasPreviousPage": true,
            "startCursor": true,
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchRecordData should generate a valid graphQL query and do the request  1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "testObject": Object {
      "read": Object {
        "__args": Object {
          "_id": "123456",
        },
        "_etag": true,
        "_id": true,
        "isSigned": true,
        "orderNumber": true,
      },
    },
  },
}
`;

exports[`graphql-service fetchReferenceFieldSuggestions should generate a valid graphQL query and return the cursor in the response 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "xtremTest": Object {
      "invoice": Object {
        "lookups": Object {
          "__args": Object {
            "data": Object {},
          },
          "code": Object {
            "__args": Object {
              "filter": "{\\"_or\\":[{\\"companyName\\":{\\"_regex\\":\\"FR001\\",\\"_options\\":\\"i\\"}},{\\"code\\":{\\"_regex\\":\\"FR001\\",\\"_options\\":\\"i\\"}}]}",
              "orderBy": "{\\"code\\":1,\\"_id\\":1}",
            },
            "edges": Object {
              "cursor": true,
              "node": Object {
                "_id": true,
                "code": true,
                "companyName": true,
              },
            },
            "pageInfo": Object {
              "endCursor": true,
              "hasNextPage": true,
              "hasPreviousPage": true,
              "startCursor": true,
            },
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchReferenceFieldSuggestions should generate a valid graphQL query for a reference field a reference callback option and record context 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "xtremTest": Object {
      "invoice": Object {
        "lookups": Object {
          "__args": Object {
            "data": Object {
              "_id": "1232",
              "firstName": "John",
              "lastName": "Doe",
            },
          },
          "code": Object {
            "__args": Object {
              "filter": "{\\"_and\\":[{\\"testProperty\\":\\"John\\"},{\\"_or\\":[{\\"code\\":{\\"_regex\\":\\"FR001\\",\\"_options\\":\\"i\\"}}]}]}",
              "orderBy": "{\\"code\\":1,\\"_id\\":1}",
            },
            "edges": Object {
              "cursor": true,
              "node": Object {
                "_id": true,
                "code": true,
                "testProperty": true,
              },
            },
            "pageInfo": Object {
              "endCursor": true,
              "hasNextPage": true,
              "hasPreviousPage": true,
              "startCursor": true,
            },
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchReferenceFieldSuggestions should generate a valid graphQL query for a reference field and do the request 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "xtremTest": Object {
      "invoice": Object {
        "lookups": Object {
          "__args": Object {
            "data": Object {},
          },
          "code": Object {
            "__args": Object {
              "filter": "{\\"_or\\":[{\\"companyName\\":{\\"_regex\\":\\"FR001\\",\\"_options\\":\\"i\\"}},{\\"code\\":{\\"_regex\\":\\"FR001\\",\\"_options\\":\\"i\\"}}]}",
              "orderBy": "{\\"code\\":1,\\"_id\\":1}",
            },
            "edges": Object {
              "cursor": true,
              "node": Object {
                "_id": true,
                "code": true,
                "companyName": true,
              },
            },
            "pageInfo": Object {
              "endCursor": true,
              "hasNextPage": true,
              "hasPreviousPage": true,
              "startCursor": true,
            },
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchReferenceFieldSuggestions should generate a valid graphQL query for a reference field with escaped character filter value 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "xtremTest": Object {
      "invoice": Object {
        "lookups": Object {
          "__args": Object {
            "data": Object {},
          },
          "code": Object {
            "__args": Object {
              "filter": "{\\"_or\\":[{\\"code\\":{\\"_regex\\":\\"FR001\\\\\\\\\\\\\\\\\\"}}]}",
              "orderBy": "{\\"code\\":1,\\"_id\\":1}",
            },
            "edges": Object {
              "cursor": true,
              "node": Object {
                "_id": true,
                "code": true,
              },
            },
            "pageInfo": Object {
              "endCursor": true,
              "hasNextPage": true,
              "hasPreviousPage": true,
              "startCursor": true,
            },
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchReferenceFieldSuggestions should generate a valid graphQL query for a reference field with false exact match option 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "xtremTest": Object {
      "invoice": Object {
        "lookups": Object {
          "__args": Object {
            "data": Object {},
          },
          "code": Object {
            "__args": Object {
              "filter": "{\\"_or\\":[{\\"code\\":{\\"_regex\\":\\"FR001\\",\\"_options\\":\\"i\\"}}]}",
              "orderBy": "{\\"code\\":1,\\"_id\\":1}",
            },
            "edges": Object {
              "cursor": true,
              "node": Object {
                "_id": true,
                "code": true,
              },
            },
            "pageInfo": Object {
              "endCursor": true,
              "hasNextPage": true,
              "hasPreviousPage": true,
              "startCursor": true,
            },
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchReferenceFieldSuggestions should generate a valid graphQL query for a reference field with true exact match option 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "xtremTest": Object {
      "invoice": Object {
        "lookups": Object {
          "__args": Object {
            "data": Object {},
          },
          "code": Object {
            "__args": Object {
              "filter": "{\\"_or\\":[{\\"code\\":{\\"_regex\\":\\"FR001\\"}}]}",
              "orderBy": "{\\"code\\":1,\\"_id\\":1}",
            },
            "edges": Object {
              "cursor": true,
              "node": Object {
                "_id": true,
                "code": true,
              },
            },
            "pageInfo": Object {
              "endCursor": true,
              "hasNextPage": true,
              "hasPreviousPage": true,
              "startCursor": true,
            },
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchReferenceFieldSuggestions should generate a valid graphQL query for a reference field without helperTextField 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "xtremTest": Object {
      "invoice": Object {
        "lookups": Object {
          "__args": Object {
            "data": Object {},
          },
          "code": Object {
            "__args": Object {
              "filter": "{\\"_or\\":[{\\"code\\":{\\"_regex\\":\\"FR001\\",\\"_options\\":\\"i\\"}}]}",
              "orderBy": "{\\"code\\":1,\\"_id\\":1}",
            },
            "edges": Object {
              "cursor": true,
              "node": Object {
                "_id": true,
                "code": true,
              },
            },
            "pageInfo": Object {
              "endCursor": true,
              "hasNextPage": true,
              "hasPreviousPage": true,
              "startCursor": true,
            },
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchReferenceFieldSuggestions should generate a valid graphQL query for a reference field without valueField 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "xtremTest": Object {
      "invoice": Object {
        "lookups": Object {
          "__args": Object {
            "data": Object {},
          },
          "code": Object {
            "__args": Object {
              "filter": "{\\"_or\\":[{\\"companyName\\":{\\"_regex\\":\\"Urban\\",\\"_options\\":\\"i\\"}},{\\"name\\":{\\"_regex\\":\\"Urban\\",\\"_options\\":\\"i\\"}}]}",
              "orderBy": "{\\"name\\":1,\\"_id\\":1}",
            },
            "edges": Object {
              "cursor": true,
              "node": Object {
                "_id": true,
                "companyName": true,
                "name": true,
              },
            },
            "pageInfo": Object {
              "endCursor": true,
              "hasNextPage": true,
              "hasPreviousPage": true,
              "startCursor": true,
            },
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchReferenceFieldSuggestions should generate a valid graphQL query with a custom filter and do the request 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "xtremTest": Object {
      "invoice": Object {
        "lookups": Object {
          "__args": Object {
            "data": Object {},
          },
          "code": Object {
            "__args": Object {
              "filter": "{\\"_and\\":[{\\"code\\":{\\"_regex\\":\\".*1$\\"}},{\\"_or\\":[{\\"companyName\\":{\\"_regex\\":\\"FR001\\",\\"_options\\":\\"i\\"}},{\\"code\\":{\\"_regex\\":\\"FR001\\",\\"_options\\":\\"i\\"}}]}]}",
              "orderBy": "{\\"code\\":1,\\"_id\\":1}",
            },
            "edges": Object {
              "cursor": true,
              "node": Object {
                "_id": true,
                "code": true,
                "companyName": true,
              },
            },
            "pageInfo": Object {
              "endCursor": true,
              "hasNextPage": true,
              "hasPreviousPage": true,
              "startCursor": true,
            },
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchReferenceFieldSuggestions should generate a valid graphQL query with a custom function filter and do the request 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "xtremTest": Object {
      "invoice": Object {
        "lookups": Object {
          "__args": Object {
            "data": Object {},
          },
          "code": Object {
            "__args": Object {
              "filter": "{\\"_and\\":[{\\"code\\":{\\"_regex\\":\\".*1$\\"}},{\\"_or\\":[{\\"companyName\\":{\\"_regex\\":\\"FR001\\",\\"_options\\":\\"i\\"}},{\\"code\\":{\\"_regex\\":\\"FR001\\",\\"_options\\":\\"i\\"}}]}]}",
              "orderBy": "{\\"code\\":1,\\"_id\\":1}",
            },
            "edges": Object {
              "cursor": true,
              "node": Object {
                "_id": true,
                "code": true,
                "companyName": true,
              },
            },
            "pageInfo": Object {
              "endCursor": true,
              "hasNextPage": true,
              "hasPreviousPage": true,
              "startCursor": true,
            },
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchReferenceFieldSuggestions should generate a valid graphQL query without filterValue for a reference field and do the request 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "xtremTest": Object {
      "invoice": Object {
        "lookups": Object {
          "__args": Object {
            "data": Object {},
          },
          "code": Object {
            "__args": Object {
              "orderBy": "{\\"code\\":1,\\"_id\\":1}",
            },
            "edges": Object {
              "cursor": true,
              "node": Object {
                "_id": true,
                "code": true,
                "companyName": true,
              },
            },
            "pageInfo": Object {
              "endCursor": true,
              "hasNextPage": true,
              "hasPreviousPage": true,
              "startCursor": true,
            },
          },
        },
      },
    },
  },
}
`;

exports[`graphql-service fetchTableData should create the correct graphql query and execute the call 1`] = `
Object {
  "axiosCancelToken": undefined,
  "cacheSettings": undefined,
  "endpoint": "/api",
  "forceRefetch": undefined,
  "query": Object {
    "testNode": Object {
      "query": Object {
        "__args": Object {
          "filter": "{\\"_id\\":{\\"_eq\\":\\"testId\\"}}",
        },
        "edges": Object {
          "cursor": true,
          "node": Object {
            "_id": true,
            "lines": Object {
              "query": Object {
                "__args": Object {
                  "filter": "{\\"item\\":{\\"_eq\\":\\"test\\"}}",
                },
                "edges": Object {
                  "cursor": true,
                  "node": Object {
                    "_id": true,
                    "item": true,
                    "quantity": true,
                  },
                },
                "pageInfo": Object {
                  "endCursor": true,
                  "hasNextPage": true,
                  "hasPreviousPage": true,
                  "startCursor": true,
                },
              },
            },
          },
        },
        "pageInfo": Object {
          "endCursor": true,
          "hasNextPage": true,
          "hasPreviousPage": true,
          "startCursor": true,
        },
      },
    },
  },
}
`;
