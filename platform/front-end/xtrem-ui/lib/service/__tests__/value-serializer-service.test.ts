import { getMockPageDefinition } from '../../__tests__/test-helpers';

import Decimal from 'decimal.js';
import { GraphQLKind, GraphQLTypes } from '../../types';
import * as valueSerializerService from '../value-serializer-service';
import { formatInputProperty, serializePageData } from '../value-serializer-service';
import { FieldKey } from '@sage/xtrem-shared';
import { Datetime } from '@sage/xtrem-date-time';

describe('value serializer service', () => {
    describe('formatInputProperty', () => {
        describe('Should serialize to boolean', () => {
            it('Should boolean true return boolean true', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Boolean, kind: GraphQLKind.Scalar },
                    value: true,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(true);
            });

            it('Should not set nested reference to null if object at "valueField" level has an "_id" property', () => {
                const result = formatInputProperty({
                    property: { type: 'Custom', kind: GraphQLKind.Object, isMutable: true, isOnInputType: true },
                    value: { other: { nested: { _id: '1', attribute: 'value' } } },
                    nodeTypes: {
                        Custom: {
                            name: 'Customer',
                            title: 'Customer',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { kind: GraphQLKind.Scalar, type: 'Id', isOnInputType: true },
                                other: {
                                    kind: GraphQLKind.Object,
                                    type: 'Second',
                                    isMutable: true,
                                    isOnInputType: true,
                                },
                            },
                            mutations: {},
                        },
                        Second: {
                            name: 'Second',
                            title: 'Second',
                            packageName: '@sage/xtrem-test',

                            properties: {
                                _id: { kind: GraphQLKind.Scalar, type: 'Id' },
                                nested: {
                                    kind: GraphQLKind.Object,
                                    type: 'Third',
                                    isMutable: true,
                                    isOnInputType: true,
                                },
                            },
                            mutations: {},
                        },
                        Third: {
                            name: 'Third',
                            title: 'Third',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                _id: { kind: GraphQLKind.Scalar, type: 'Id', isOnInputType: true },
                                attribute: { kind: GraphQLKind.Scalar, type: 'String', isOnInputType: true },
                            },
                            mutations: {},
                        },
                    },
                    isRequestingDefaults: false,
                });
                expect(result).toEqual({ other: { nested: { _id: '1', attribute: 'value' } } });
            });
            it('Should string true return boolean true', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Boolean, kind: GraphQLKind.Scalar },
                    value: 'true',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(true);
            });
            it('Should boolean false return boolean false', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Boolean, kind: GraphQLKind.Scalar },
                    value: false,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(false);
            });
            it('Should string false return boolean false', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Boolean, kind: GraphQLKind.Scalar },
                    value: 'false',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(false);
            });
            it('Should undefined return boolean false', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Boolean, kind: GraphQLKind.Scalar },
                    value: undefined,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(false);
            });
            it('Should null return boolean to null', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Boolean, kind: GraphQLKind.Scalar },
                    value: null,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
        });
        describe('Should serialize IntOrString', () => {
            it('Should null return null', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                    value: null,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should undefined return null', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                    value: undefined,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should empty string return null', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should throw if unserializable string value is provided', () => {
                expect(() =>
                    formatInputProperty({
                        property: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                        value: 'patata',
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow("Couldn't serialize patata to a IntOrString input type");
            });

            it('Should serialize non vital object to string input', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                    value: { _id: '4', someOther: 'Property' },
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe('4');
            });

            it('Should throw an error when it tries to serialize and object to string without an _id property', () => {
                expect(() =>
                    formatInputProperty({
                        property: { type: GraphQLTypes.IntOrString, kind: GraphQLKind.Scalar },
                        value: { someOther: 'Property' },
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow();
            });
        });
        describe('Should serialize String', () => {
            it('Should with string return the same value', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    value: 'patata',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe('patata');
            });
            it('Should return null with empty string', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should return null with undefined', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should return null with false', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });

            it('Should serialize non vital object to string input', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    value: { _id: '4', someOther: 'Property' },
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe('4');
            });
            it('Should throw an error when it tries to serialize and object to string without an _id property', () => {
                expect(() =>
                    formatInputProperty({
                        property: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                        value: { someOther: 'Property' },
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow();
            });
        });
        describe('Should serialize Int property', () => {
            it('Should throw if unserializable string value is provided', () => {
                expect(() =>
                    formatInputProperty({
                        property: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                        value: 'patata',
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow("Couldn't serialize patata to a Int input type");
            });
            it('Should null return null', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    value: null,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should undefined return null', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    value: undefined,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should empty string return null', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should return the same value', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    value: 1234,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(1234);
            });
            it('Should convert string to number', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    value: '1234',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(1234);
            });
            it('Should round non-integer string number', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    value: '1234.56',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(1235);
            });
            it('Should round non-integer number', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    value: 1234.34,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(1234);
            });

            it('Should convert Decimal instance to non-integer number', () => {
                const value = new Decimal('1234.34');
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Int, kind: GraphQLKind.Scalar },
                    value,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(1234);
            });
        });
        describe('Should serialize Float property', () => {
            it('Should throw if unserializable string value is provided', () => {
                expect(() =>
                    formatInputProperty({
                        property: { type: GraphQLTypes.Float, kind: GraphQLKind.Scalar },
                        value: 'patata',
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow("Couldn't serialize patata to a Float input type");
            });
            it('Should null return null', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Float, kind: GraphQLKind.Scalar },
                    value: null,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should undefined return null', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Float, kind: GraphQLKind.Scalar },
                    value: undefined,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should empty string return null', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Float, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should return the same value', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Float, kind: GraphQLKind.Scalar },
                    value: 12.34,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(12.34);
            });
            it('Should convert string to number', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Float, kind: GraphQLKind.Scalar },
                    value: '12.34',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(12.34);
            });

            it('Should convert Decimal instance to number', () => {
                const value = new Decimal('12.34');
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Float, kind: GraphQLKind.Scalar },
                    value,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(12.34);
            });
        });
        describe('Should serialize String', () => {
            it('Should with string return the same value', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    value: 'patata',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe('patata');
            });
            it('Should return null with empty string', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should return null with undefined', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should return null with false', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.String, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
        });
        describe('Should serialize Date', () => {
            it('Should throw if unserializable string value is provided', () => {
                expect(() =>
                    formatInputProperty({
                        property: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar },
                        value: 'patata',
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow('Invalid date provided: patata');
            });
            it('Should return null with empty string', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should return null with undefined', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should a serialized date if a date string is passed in', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar },
                    value: '2020-08-12',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('2020-08-12');
            });
            it('Should return a serialized date if a date object is passed in', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar },
                    value: new Date(1628753192071),
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('2021-08-12');
            });

            it('Should return a serialized date if a dateTime string is passed in', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar },
                    value: '2020-08-12T12:34:00.000Z',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('2020-08-12');
            });

            it('Should return a serialized unix timestamp if a date object is passed in', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar },
                    value: 1628753192071,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('2021-08-12');
            });

            it('Should return a serialized unix timestamp string if a date object is passed in', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar },
                    value: 1628753192071,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('2021-08-12');
            });
        });
        describe('Should serialize datetime', () => {
            it('Should throw if unserializable string value is provided', () => {
                expect(() =>
                    formatInputProperty({
                        property: { type: 'datetime', kind: GraphQLKind.Scalar },
                        value: 'patata',
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow('Invalid date provided: patata');
            });
            it('Should return null with empty string', () => {
                const result = formatInputProperty({
                    property: { type: 'datetime', kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });

            it('Should return null with undefined', () => {
                const result = formatInputProperty({
                    property: { type: 'datetime', kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });

            it('Should return a serialized date if a date string is passed in', () => {
                const result = formatInputProperty({
                    property: { type: 'datetime', kind: GraphQLKind.Scalar },
                    value: '2020-08-12',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('2020-08-12T00:00:00.000Z');
            });

            it('Should return a serialized date if a dateTime string is passed in', () => {
                const result = formatInputProperty({
                    property: { type: 'datetime', kind: GraphQLKind.Scalar },
                    value: '2020-08-12T12:34:00.000Z',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('2020-08-12T12:34:00.000Z');
            });

            it('Should return a serialized date if a date object is passed in', () => {
                const result = formatInputProperty({
                    property: { type: 'datetime', kind: GraphQLKind.Scalar },
                    value: new Date(1628753192071),
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('2021-08-12T07:26:32.071Z');
            });

            it('Should a serialized a serialized date if a unix timestamp is passed in', () => {
                const result = formatInputProperty({
                    property: { type: 'datetime', kind: GraphQLKind.Scalar },
                    value: 1628753192071,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('2021-08-12T07:26:32.071Z');
            });

            it('Should a serialized a serialized date if a unix timestamp string is passed in', () => {
                const result = formatInputProperty({
                    property: { type: 'datetime', kind: GraphQLKind.Scalar },
                    value: '1628753192071',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('2021-08-12T07:26:32.071Z');
            });
        });
        describe('Should serialize DateTimeRange', () => {
            it('Should return null if empty value', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.DatetimeRange, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should return null if undefined value', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.DatetimeRange, kind: GraphQLKind.Scalar },
                    value: undefined,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should format a date range in a server side format [start, end]', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.DatetimeRange, kind: GraphQLKind.Scalar },
                    value: {
                        start: Datetime.parse('2024-01-01T00:00:00Z'),
                        end: Datetime.parse('2024-01-02T00:00:00Z'),
                    },
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('[2024-01-01T00:00:00.000Z,2024-01-02T00:00:00.000Z]');
            });
            it('Should format if start is null but end is provided', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.DatetimeRange, kind: GraphQLKind.Scalar },
                    value: { start: null, end: Datetime.parse('2024-01-02T00:00:00Z') },
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(JSON.stringify(result)).toEqual('"[,2024-01-02T00:00:00.000Z]"');
            });
            it('Should format if end is null but start is provided', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.DatetimeRange, kind: GraphQLKind.Scalar },
                    value: { start: Datetime.parse('2024-01-01T00:00:00Z'), end: null },
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(JSON.stringify(result)).toEqual('"[2024-01-01T00:00:00.000Z,]"');
            });
        });

        describe('Should serialize IntReference and ExternalReference', () => {
            it('Should return null with falsy values', () => {
                let result = formatInputProperty({
                    property: { type: 'IntReference', kind: GraphQLKind.Scalar },
                    value: false,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
                result = formatInputProperty({
                    property: { type: 'ExternalReference', kind: GraphQLKind.Scalar },
                    value: false,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });

            it('Should return null if is not a object', () => {
                let result = () =>
                    formatInputProperty({
                        property: { type: 'IntReference', kind: GraphQLKind.Scalar },
                        value: true,
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    });
                expect(result()).toBeNull();

                result = () =>
                    formatInputProperty({
                        property: { type: 'ExternalReference', kind: GraphQLKind.Scalar },
                        value: true,
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    });
                expect(result()).toBeNull();
            });

            it('Should leave serialized id reference as is', () => {
                let result = formatInputProperty({
                    property: { type: 'IntReference', kind: GraphQLKind.Scalar },
                    value: '_id:987',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('_id:987');

                result = formatInputProperty({
                    property: { type: 'ExternalReference', kind: GraphQLKind.Scalar },
                    value: '_id:987',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('_id:987');
            });

            it('Should string to id reference', () => {
                let result = formatInputProperty({
                    property: { type: 'IntReference', kind: GraphQLKind.Scalar },
                    value: 'NA043~32',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('NA043~32');

                result = formatInputProperty({
                    property: { type: 'ExternalReference', kind: GraphQLKind.Scalar },
                    value: 'NA043~32',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('NA043~32');
            });

            it('Should number to id reference', () => {
                let result = formatInputProperty({
                    property: { type: 'IntReference', kind: GraphQLKind.Scalar },
                    value: 1234,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('1234');

                result = formatInputProperty({
                    property: { type: 'ExternalReference', kind: GraphQLKind.Scalar },
                    value: 1234,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual('1234');
            });

            it('Should return null if the _id is null', () => {
                let result = () =>
                    formatInputProperty({
                        property: { type: 'IntReference', kind: GraphQLKind.Scalar },
                        value: {
                            _id: null,
                        },
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    });
                expect(result()).toBeNull();

                result = () =>
                    formatInputProperty({
                        property: { type: 'ExternalReference', kind: GraphQLKind.Scalar },
                        value: {
                            _id: null,
                        },
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    });
                expect(result()).toBeNull();
            });

            it('Should return a formatted string id', () => {
                let result = formatInputProperty({
                    property: { type: 'IntReference', kind: GraphQLKind.Scalar },
                    value: {
                        _id: 'patataId',
                    },
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe('_id:patataId');

                result = formatInputProperty({
                    property: { type: 'ExternalReference', kind: GraphQLKind.Scalar },
                    value: {
                        _id: 'patataId',
                    },
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe('_id:patataId');
            });
        });
        describe('Should serialize enums', () => {
            it('Should return null with empty value', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Enum, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should return null with undefined value', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Enum, kind: GraphQLKind.Scalar },
                    value: undefined,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });

            it('Should return a string if the supplied value is a string', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Enum, kind: GraphQLKind.Scalar },
                    value: 'patata',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe('patata');
            });

            it('Should return a number if the provided value is an integer', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Enum, kind: GraphQLKind.Scalar },
                    value: 3,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(3);
            });

            it('Should return a number if the provided value is an integer string', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Enum, kind: GraphQLKind.Scalar },
                    value: '4',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(4);
            });

            it('Should throw an error if a float provided', () => {
                expect(() =>
                    formatInputProperty({
                        property: { type: GraphQLTypes.Enum, kind: GraphQLKind.Scalar },
                        value: 1.32,
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow('Cannot serialize 1.32 to enum input type (Enum)');
            });

            it('Should throw an error if an object provided', () => {
                expect(() =>
                    formatInputProperty({
                        property: { type: GraphQLTypes.Enum, kind: GraphQLKind.Scalar },
                        value: {},
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow('Cannot serialize [object Object] to enum input type (Enum)');
            });
        });
        describe('Should serialize JSON property', () => {
            it('Should return null with null/undefined/empty value', () => {
                let result = formatInputProperty({
                    property: { type: GraphQLTypes.Json, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
                result = formatInputProperty({
                    property: { type: GraphQLTypes.Json, kind: GraphQLKind.Scalar },
                    value: undefined,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
                result = formatInputProperty({
                    property: { type: GraphQLTypes.Json, kind: GraphQLKind.Scalar },
                    value: null,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });
            it('Should return a serialized object if an object is passed in', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Json, kind: GraphQLKind.Scalar },
                    value: { this: ['array', 'another member'], embeddedObject: { foo: 'bar' } },
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe('{"this":["array","another member"],"embeddedObject":{"foo":"bar"}}');
            });

            it('Should return a serialized array if an array is passed in', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Json, kind: GraphQLKind.Scalar },
                    value: ['patata', 'potato'],
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe('["patata","potato"]');
            });

            it('Should return a string if a string is passed in', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.Json, kind: GraphQLKind.Scalar },
                    value: '{"serialized":"json object"}',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe('{"serialized":"json object"}');
            });
        });
        describe('Should serialize InputStream property', () => {
            it('Should return null with null/undefined/empty value', () => {
                let result = formatInputProperty({
                    property: { type: GraphQLTypes.InputStream, kind: GraphQLKind.Scalar },
                    value: '',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
                result = formatInputProperty({
                    property: { type: GraphQLTypes.InputStream, kind: GraphQLKind.Scalar },
                    value: undefined,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
                result = formatInputProperty({
                    property: { type: GraphQLTypes.InputStream, kind: GraphQLKind.Scalar },
                    value: null,
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toBe(null);
            });

            it('Should return an serialized object if an object is passed in with a value key that contains a string', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.InputStream, kind: GraphQLKind.Scalar },
                    value: { value: 'Some string content' },
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual({ value: 'Some string content' });
            });

            it('Should return null if an object is passed in with a value key that contains an empty value', () => {
                let result = formatInputProperty({
                    property: { type: GraphQLTypes.InputStream, kind: GraphQLKind.Scalar },
                    value: { value: '' },
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual(null);
                result = formatInputProperty({
                    property: { type: GraphQLTypes.InputStream, kind: GraphQLKind.Scalar },
                    value: { value: null },
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual(null);
                result = formatInputProperty({
                    property: { type: GraphQLTypes.InputStream, kind: GraphQLKind.Scalar },
                    value: { value: undefined },
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual(null);
            });

            it('Should return an object with a value key if an string is passed in', () => {
                const result = formatInputProperty({
                    property: { type: GraphQLTypes.InputStream, kind: GraphQLKind.Scalar },
                    value: 'Some string content',
                    nodeTypes: {},
                    isRequestingDefaults: false,
                });
                expect(result).toEqual({ value: 'Some string content' });
            });

            it('Should throw an error if an invalid value is passed in', () => {
                expect(() =>
                    formatInputProperty({
                        property: { type: GraphQLTypes.InputStream, kind: GraphQLKind.Scalar },
                        value: { aStrange: 'object' },
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow('Cannot serialize [object Object] to Input stream');
                expect(() =>
                    formatInputProperty({
                        property: { type: GraphQLTypes.InputStream, kind: GraphQLKind.Scalar },
                        value: true,
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow('Cannot serialize true to Input stream');
                expect(() =>
                    formatInputProperty({
                        property: { type: GraphQLTypes.InputStream, kind: GraphQLKind.Scalar },
                        value: 8,
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow('Cannot serialize 8 to Input stream');
                expect(() =>
                    formatInputProperty({
                        property: { type: GraphQLTypes.InputStream, kind: GraphQLKind.Scalar },
                        value: ['one', 'two'],
                        nodeTypes: {},
                        isRequestingDefaults: false,
                    }),
                ).toThrow('Cannot serialize one,two to Input stream');
            });
        });

        describe('Should serialize vital object input', () => {
            it('Should return serialize simple input object', () => {
                const result = formatInputProperty({
                    property: {
                        type: 'SomeTestObject',
                        kind: GraphQLKind.Object,
                        isMutable: true,
                        isOnInputType: true,
                    },
                    value: { someNumber: '5', user: { name: 'test' } },
                    nodeTypes: {
                        SomeTestObject: {
                            name: 'SomeTestObject',
                            title: 'SomeTestObject',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                someNumber: { kind: GraphQLKind.Scalar, type: 'Int', isOnInputType: true },
                                user: {
                                    kind: GraphQLKind.Object,
                                    type: 'UserTestObject',
                                    isMutable: true,
                                    isOnInputType: true,
                                },
                            },
                            mutations: {},
                        },
                        UserTestObject: {
                            name: 'UserTestObject',
                            title: 'UserTestObject',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                name: { kind: GraphQLKind.Scalar, type: 'String', isOnInputType: true },
                            },
                            mutations: {},
                        },
                    },
                    isRequestingDefaults: false,
                    isTopLevel: true,
                });
                expect(result).toEqual({ someNumber: 5, user: { name: 'test' } });
            });

            it('Should reduce empty objects to null', () => {
                const result = formatInputProperty({
                    property: { type: 'SomeTestObject', kind: GraphQLKind.Scalar },
                    value: { user: null },
                    nodeTypes: {
                        SomeTestObject: {
                            name: 'SomeTestObject',
                            title: 'SomeTestObject',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                someNumber: { kind: GraphQLKind.Scalar, type: 'Int' },
                                user: { kind: 'OBJECT', type: 'UserTestObject' },
                            },
                            mutations: {},
                        },
                        UserTestObject: {
                            name: 'UserTestObject',
                            title: 'UserTestObject',
                            packageName: '@sage/xtrem-test',
                            properties: {
                                name: { kind: GraphQLKind.Scalar, type: 'String' },
                            },
                            mutations: {},
                        },
                    },
                    isRequestingDefaults: false,
                });
                expect(result).toEqual(null);
            });
        });
    });
});

describe('serializePageData', () => {
    const screenId = 'TestScreenId';

    beforeEach(() => {
        // Mock formatInputProperty to return the same value
        jest.spyOn(valueSerializerService, 'formatInputProperty').mockImplementation(args => args.value);
    });

    afterAll(() => {
        jest.restoreAllMocks();
    });

    it('should serialize page data correctly', () => {
        const values = {
            _id: '123',
            _etag: 'abc',
            field1: 'value1',
            field2: 'value2',
        };

        const uiComponentProperties = {
            _id: { _controlObjectType: FieldKey.Text },
            _etag: { _controlObjectType: FieldKey.Text },
            field1: { _controlObjectType: FieldKey.Text },
            field2: { _controlObjectType: FieldKey.Text },
        };

        const isRequestingDefaults = false;

        const nodeTypes = {};

        const targetNode = 'Custom';

        const result = serializePageData({
            screenDefinition: getMockPageDefinition(screenId, { values }, { uiComponentProperties }),
            isRequestingDefaults,
            nodeTypes,
            targetNode,
        });

        expect(result).toEqual({
            _id: '123',
            _etag: 'abc',
            field1: 'value1',
            field2: 'value2',
        });
    });

    it('Should not serialize Aggregate, Count or Separator fields', () => {
        const values = {
            _id: '123',
            _etag: 'abc',
            field1: 'value1',
            field2: 'value2',
            aggregateField: 'aggregate',
            countField: 'count',
            separatorField: 'separator',
        };

        const uiComponentProperties = {
            _id: { _controlObjectType: FieldKey.Text },
            _etag: { _controlObjectType: FieldKey.Text },
            field1: { _controlObjectType: FieldKey.Text },
            field2: { _controlObjectType: FieldKey.Text },
            aggregateField: { _controlObjectType: FieldKey.Aggregate },
            countField: { _controlObjectType: FieldKey.Count },
            separatorField: { _controlObjectType: FieldKey.Separator },
        };

        const isRequestingDefaults = false;

        const nodeTypes = {};

        const targetNode = 'Custom';

        const result = serializePageData({
            screenDefinition: getMockPageDefinition(screenId, { values }, { uiComponentProperties }),
            isRequestingDefaults,
            nodeTypes,
            targetNode,
        });

        expect(result).toEqual({
            _id: '123',
            _etag: 'abc',
            field1: 'value1',
            field2: 'value2',
        });
    });
});
