import { addFieldToState, getMockScreenDefinition, getMockState, getMockStore } from '../../__tests__/test-helpers';

import { FieldKey } from '../../component/types';
import type * as xtremRedux from '../../redux';
import * as transactionService from '../transactions-service';
import { CollectionValue } from '../collection-data-service';
import { CollectionFieldTypes } from '../collection-data-types';

describe('Transaction service', () => {
    const screenId = 'TestPage';
    const dispatchMock = jest.fn();
    let getState: jest.SpyInstance;
    let mockState: xtremRedux.XtremAppState;
    const testField1Id = 'test-field-1';
    const testField2Id = 'test-field-2';
    const testTableId = 'test-table';

    beforeEach(() => {
        jest.useRealTimers();
        mockState = getMockState();
        mockState.screenDefinitions[screenId] = getMockScreenDefinition(screenId, 'page');

        addFieldToState(
            FieldKey.Text,
            mockState,
            screenId,
            testField1Id,
            {
                title: 'Hello',
                isHidden: true,
            },
            'Hello',
        );
        addFieldToState(
            FieldKey.Text,
            mockState,
            screenId,
            testField2Id,
            {
                isTransient: true,
            },
            { someStrangeKey: 'forThisStrangeValue' },
        );
        addFieldToState(
            FieldKey.Table,
            mockState,
            screenId,
            testTableId,
            {
                isDisabled: false,
                columns: [],
            },
            new CollectionValue<any>({
                screenId,
                elementId: testTableId,
                isTransient: false,
                hasNextPage: false,
                orderBy: [{}],
                columnDefinitions: [[]],
                nodeTypes: {},
                nodes: [],
                filter: [{}],
                locale: 'en-US',
                initialValues: [
                    {
                        _id: '1',
                        whatEver: 2,
                    },
                    {
                        _id: '3',
                        whatEver: 2,
                    },
                ],
                fieldType: CollectionFieldTypes.DESKTOP_TABLE,
            }),
        );

        const mockStore = getMockStore(mockState);
        mockStore.dispatch = dispatchMock;

        getState = jest.spyOn(mockStore, 'getState');
        dispatchMock.mockClear();
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('State values', () => {
        it('should retrieve state value on getFieldValue without transaction', () => {
            transactionService.getFieldValue(screenId, testField1Id);
            const fieldValue = transactionService.getFieldValue(screenId, testField1Id);
            expect(getState).toHaveBeenCalledTimes(2);
            expect(fieldValue).toEqual(mockState.screenDefinitions[screenId].values![testField1Id]);
        });

        it('should retrieve transaction value on getFieldValue with transaction', () => {
            const newValue = 'New value';
            transactionService.setFieldValue(screenId, testField1Id, newValue);
            transactionService.getFieldValue(screenId, testField1Id);
            const fieldValue = transactionService.getFieldValue(screenId, testField1Id);
            expect(getState).toHaveBeenCalledTimes(4);
            expect(fieldValue).not.toBe(mockState.screenDefinitions[screenId].values![testField1Id]);
            expect(fieldValue).toEqual(newValue);
        });

        it('should retrieve undefined on getFieldValue with transaction for non existing value', () => {
            transactionService.setFieldValue(screenId, testField1Id, 'New value');
            const fieldValue = transactionService.getFieldValue(screenId, 'field-with-non-existing-value');
            expect(fieldValue).toBe(undefined);
        });

        it('should not clone state on transaction start', () => {
            transactionService.setFieldValue(screenId, testField1Id, 'New value');
            const field2Value = transactionService.getFieldValue(screenId, testField2Id);
            expect(field2Value).toEqual(mockState.screenDefinitions[screenId].values![testField2Id]);
        });

        it('should not clone CollectionValue values on transaction start', () => {
            const originalValue = transactionService.getFieldValue(screenId, testTableId);
            transactionService.setFieldValue(screenId, testField1Id, { test: 32 });
            const tableValue = transactionService.getFieldValue(screenId, testTableId);
            expect(tableValue).toBe(originalValue);
        });

        it('should dispatch CommitTransaction when transaction ends', done => {
            transactionService.setFieldValue(screenId, testField1Id, 'New value');
            expect(dispatchMock).not.toHaveBeenCalled();
            setImmediate(() => {
                expect(dispatchMock).toHaveBeenCalledTimes(1);
                done();
            });
        });
    });

    describe('State uiComponentProperties', () => {
        it('should retrieve state value on getUiComponentProperties without transaction', () => {
            transactionService.getUiComponentProperties(screenId, testField1Id);
            const fieldValue = transactionService.getUiComponentProperties(screenId, testField1Id);
            expect(getState).toHaveBeenCalledTimes(4);
            expect(fieldValue).toEqual(
                mockState.screenDefinitions[screenId].metadata.uiComponentProperties![testField1Id],
            );
        });

        it('should retrieve transaction value on getUiComponentProperties with transaction', () => {
            const newValue = {
                isDisabled: true,
            };
            transactionService.setUiComponentProperties<FieldKey.Text>(screenId, testField1Id, newValue);
            transactionService.getUiComponentProperties(screenId, testField1Id);
            const fieldValue = transactionService.getUiComponentProperties(screenId, testField1Id);
            expect(getState).toHaveBeenCalledTimes(3);
            expect(fieldValue).not.toBe(
                mockState.screenDefinitions[screenId].metadata.uiComponentProperties![testField1Id],
            );
            expect(fieldValue).toEqual(newValue);
        });

        it('should retrieve undefined on getUiComponentProperties with transaction for non existing value', () => {
            transactionService.setUiComponentProperties(screenId, testField1Id, { isDisabled: true });
            const fieldValue = transactionService.getUiComponentProperties(screenId, 'field-with-non-existing-value');
            expect(fieldValue).toBe(undefined);
        });

        it('should not clone state on transaction start', () => {
            transactionService.setUiComponentProperties(screenId, testField1Id, { isDisabled: true });
            const transactionTableValue = transactionService.getUiComponentProperties(screenId, testTableId);
            expect(transactionTableValue).toEqual(
                mockState.screenDefinitions[screenId].metadata.uiComponentProperties![testTableId],
            );
        });

        it('should dispatch CommitTransaction when transaction ends', done => {
            transactionService.setUiComponentProperties(screenId, testField1Id, { isDisabled: true });
            expect(dispatchMock).not.toHaveBeenCalled();
            setImmediate(() => {
                expect(dispatchMock).toHaveBeenCalledTimes(1);
                done();
            });
        });
    });
});
