PATH: XTREEM/Client+Framework/Plugins

This page describes the how plugins can be added to Xtrem pages. Plugins are developed as React components and designed to allow the development of specialized _fields_.

## Important!

Plugins are reserved for very specific use-cases and should not be developed by functional development teams. All Sage and externally developed plugins are subject to the Sage Design Language System.

## What is a UI plugin?

An Xtrem UI plugin is a field type that is rendered by an external React component, developed independently from the base of the application. From a framework perspective, plugins are fields and the usual field properties apply to them such as sizing, title, disabled etc. Plugins are developed in standalone NPM packages but are expected to adhere to certain interface declarations which are described below.

## Use case for a plugin

This section introduces possible reasons for plugin development.

### Internally

Build a rich, HTML template, a user needs a context-aware rich HTML editor with handlebars support. The most straightforward solution to that would be providing a field type that is based on Monaco editor (open-source, browser friendly version of Microsoft VS Code). Monaco editor is an enormous library (20 MB+ minified), therefore it cannot be part of the main bundle as the library would be only required by a minor subset of the pages and loading it for every user would increase the initial loading time significantly and waste bandwidth.

In order to address this need a plugin field could be created that encapsulates Monaco editor and only loaded to the user's browser when they navigate to a page which uses the monaco editor plugin field.

### Externally

An Xtrem partner designs a delivery tracking solution. They would like to work with their own geographical data and map provider and display the position of the delivery trucks on an Xtrem distribution screen. The Xtrem UI framework does not provide map field out of the box.

The partner decides to develop a plugin that encapsulates their map library of choice as a React component. They map the incoming values from the graph to map data points and forward them to the map rendering library. This way their end user does not need to leave the Xtrem application to interact with a highly customized component.

## Plugin structure

Plugins are developed and delivered as standard NPM packages. The `package.json` file must have a `"xtremPlugin": true` property in order to be recognized as an Xtrem UI plugin.
Plugins expected to default export an implementation of the `XtremUiPlugin` interface in their "main" file (designated by the package.json. Plugins can only be developed using TypeScript.

## Building plugins

Plugins can be built using the `xtrem-cli`'s `compile-plugin` command. It uses webpack under the hood to create the plugin bundle. It has a factory webpack configuration embedded, but it can be extended by creating a `webpack.config.js` in the plugin package's root directory. If the webpack config file exists, the `xtrem-cli` merges it with the factory configuration.

## Loading plugins

Plugins are loaded on demand. This means that the plugin source code only gets loaded from the server when the user navigates to a page that uses that particular plugin.

## Interfaces

## Limitations

-   Plugins do not support nested fields
