import { LicenseManager } from '@ag-grid-enterprise/core';
import { getStore } from '../redux';
import { xtremConsole } from '../utils/console';

declare const AG_LICENCE: string;

let loadedLicenceKey: string | null = null;

export const setAgGridLicence = (): void => {
    const stateLicenceKey = getStore().getState()?.applicationContext?.agGridLicenceKey?.trim();
    if (stateLicenceKey) {
        if (loadedLicenceKey !== stateLicenceKey) {
            LicenseManager.setLicenseKey(stateLicenceKey);
            loadedLicenceKey = stateLicenceKey;
        }
    } else {
        try {
            if (AG_LICENCE !== 'UNSET') {
                const decodedLicence = atob(AG_LICENCE.trim());
                if (loadedLicenceKey !== decodedLicence) {
                    LicenseManager.setLicenseKey(decodedLicence);
                    loadedLicenceKey = decodedLicence;
                }
            }
        } catch (e) {
            xtremConsole.warn('No AG-Grid licence was loaded.');
        }
    }
};
