import type { ClientNode, ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Dict } from '@sage/xtrem-shared';
import type { Merge } from 'ts-essentials';
import type { CollectionValueFieldProperties } from '../component/field/collection-value-field';
import type { Level } from '../component/field/nested-grid/nested-grid-component-types';
import type { HasDynamicLookupSuggestions } from '../component/field/traits';
import type { NestedField, NestedFieldTypes } from '../component/nested-fields';
import type { OrderByType, PartialCollectionValue, PartialCollectionValueWithIds } from '../component/types';
import type { ScreenExtension } from '../types';
import type { AggFunc } from './collection-data-utils';
import type { QueryArguments } from './graphql-utils';
import type { ScreenBase } from './screen-base';
import type { ValidationResult } from './screen-base-definition';

export enum RecordActionType {
    MODIFIED = 'update',
    ADDED = 'create',
    REMOVED = 'delete',
    TEMPORARY = 'temporary',
}

export enum CollectionFieldTypes {
    DESKTOP_TABLE = 'desktop table',
    MOBILE_TABLE = 'mobile table',
    NESTED_GRID = 'nested grid',
    TREE = 'tree',
    LOOKUP_DIALOG = 'lookup dialog',
    GRID_ROW = 'grid row',
    POD = 'pod',
    CALENDAR = 'calendar',
    NAVIGATION_PANEL = 'navigation panel',
    MULTI_FILE_DEPOSIT = 'multi file deposit',
}

/** It represents all validation errors in a collection sorted by level, then column */
export type CollectionGlobalValidationState = Dict<ValidationResult[]>[];

export type ValueChangeSubscriptionCallback = (type: RecordActionType, rowValue: any) => void;

export type ValidityChangeSubscriptionCallback = ({
    globalValidationState,
    recordValidationState,
    recordId,
    recordLevel,
}: {
    globalValidationState: CollectionGlobalValidationState;
    recordValidationState: Dict<ValidationResult>;
    recordId: string;
    recordLevel?: number;
}) => void;

export type FetchNestedGridArgs<N extends ClientNode = any> = {
    rootNode: string;
    selectedRecordId: string;
    level: number;
    levelProps: Level<any, N>;
    queryArguments: QueryArguments;
    childProperty: string;
};

export interface CollectionValueFieldPropertiesWithAdditionalRecords<CT extends ScreenExtension<CT> = ScreenBase>
    extends CollectionValueFieldProperties<CT>,
        HasDynamicLookupSuggestions<CT> {
    columns?: NestedField<any, NestedFieldTypes>[];
}

type BaseCollectionValueType = {
    __action?: RecordActionType;
    __compositeKey?: string;
    __dirty?: boolean;
    __dirtyColumns?: Set<string>;
    __isGroup?: boolean;
    __forceRowUpdate?: boolean;
    __aggFunc?: AggFunc;
    __groupCount?: number;
    __level?: number;
    __parentId?: string;
    __phantom?: boolean;
    __validationState?: Dict<ValidationResult>;
    __uncommitted?: boolean;
    __path?: string;
    _id: string;

    /**
     * This flag is used to indicate a record that we know exists but have not been been fully loaded yet, such as records we know exist from validation
     * error results, but the user have not requested them from the server yet. These records should be excluded from all queries other than validation related ones.
     *  */
    __unloaded?: boolean;

    __cursor?: string;
};
export type CollectionValueType<T extends BaseCollectionValueType = BaseCollectionValueType> = T & {
    __groupKey?: keyof T & string;
};

export type InternalValue<T extends ClientNode> = Merge<PartialCollectionValue<T>, CollectionValueType>;
export type AggregationMethod = 'min' | 'max' | 'sum' | 'avg' | 'count';
export interface NormalizedCollection<T extends ClientNode = any> {
    addRecord(args: {
        beforeInsert?: (
            d: PartialCollectionValue<T> | Omit<PartialCollectionValue<T>, '_id'>,
        ) => Partial<InternalValue<T>>;
        afterInsert?: (args: { record: Partial<InternalValue<T>>; action: RecordActionType }) => void;
        data: PartialCollectionValue<T> | Omit<PartialCollectionValue<T>, '_id'>;
        level?: number;
        cleanMetadata?: boolean;
        dirty?: boolean;
        action?: RecordActionType | 'none';
        parentId?: string;
        isUncommitted?: boolean;
    }): InternalValue<T>;
    calculateAggregatedValue(args: {
        aggregationMethod: AggregationMethod;
        aggregationKey: string;
        level?: number | null;
        parentId?: string | null;
        where?: LokiQuery<InternalValue<T> & LokiObj>;
        includeUnloaded?: boolean;
        includePhantom?: boolean;
        isUncommitted?: boolean;
    }): number;
    clear(): void;
    createRecord(args: {
        beforeCreate?: (
            d: PartialCollectionValue<T> | Omit<PartialCollectionValue<T>, '_id'>,
        ) => Partial<InternalValue<T>>;
        data: PartialCollectionValue<T> | Omit<PartialCollectionValue<T>, '_id'>;
        level?: number;
        dirty?: boolean;
        action?: RecordActionType | 'none';
        parentId?: string;
        isUncommitted?: boolean;
    }): InternalValue<T>;
    exportDataForServerProcessing(): any[];
    filter(args: { where?: LokiQuery<InternalValue<T>>; groupByKey?: string }): this;
    findAll(args: {
        level?: number | null;
        parentId?: string | null;
        where?: LokiQuery<InternalValue<T> & LokiObj>;
        includeUnloaded?: boolean;
        includePhantom?: boolean;
        isUncommitted?: boolean;
    }): this;
    findOne(args: {
        id?: string;
        level?: number | null;
        parentId?: string;
        where?: LokiQuery<InternalValue<T>>;
        cleanMetadata?: undefined;
        includeUnloaded?: boolean;
        isUncommitted?: boolean;
    }): PartialCollectionValueWithIds<T>;
    findOne(args: {
        id?: string;
        level?: number | null;
        parentId?: string;
        where?: LokiQuery<InternalValue<T>>;
        cleanMetadata?: true;
        includeUnloaded?: boolean;
        isUncommitted?: boolean;
    }): PartialCollectionValueWithIds<T>;
    findOne(args: {
        id?: string;
        level?: number | null;
        parentId?: string;
        where?: LokiQuery<InternalValue<T>>;
        cleanMetadata?: false;
        unloaded?: boolean;
        isUncommitted?: boolean;
    }): InternalValue<T>;
    findOne(args: {
        id?: string;
        level?: number | null;
        parentId?: string;
        where?: LokiQuery<InternalValue<T>>;
        cleanMetadata?: boolean;
        includeUnloaded?: boolean;
        isUncommitted?: boolean;
    }): InternalValue<T> | PartialCollectionValueWithIds<T>;
    findOne(args: {
        id?: string;
        level?: number | null;
        parentId?: string;
        where?: LokiQuery<InternalValue<T>>;
        cleanMetadata?: boolean;
        includeUnloaded?: boolean;
        isUncommitted?: boolean;
    }): InternalValue<T> | PartialCollectionValueWithIds<T>;
    getAncestorIds(args: { id: string; level?: number }): string[];
    getIdPathToRecordByIdAndLevel(args: { id: string; level?: number }): string[];
    getValidationStateByColumn(levels: number): CollectionGlobalValidationState;
    generateIndex(): string;
    /** Queries the database to return the current validation state of the content, it does not run the validations.  */
    isCollectionValid(): boolean;
    isCollectionDirty(): boolean;
    remove(args: { data: InternalValue<T>; beforeRemove?: () => void; afterRemove?: () => void }): this;
    skip(offset: number): this;
    sort(args: { orderBy?: OrderByType<T>; columnDefinitions: NestedField<ScreenBase, NestedFieldTypes>[] }): this;
    take(limit: number): this;
    update(args: {
        data: InternalValue<T>;
        beforeUpdate?: (data: Partial<InternalValue<T>>) => any;
        afterUpdate?: (record: Partial<InternalValue<T>>) => void;
        cleanMetadata?: boolean;
        isUncommitted?: boolean;
    }): InternalValue<T>;
    value({
        cleanMetadata,
    }: {
        cleanMetadata: true;
        cleanDatabaseMeta?: boolean;
        isUncommitted?: boolean;
    }): PartialCollectionValueWithIds<T>[];
    value({
        cleanMetadata,
    }: {
        cleanMetadata: false;
        cleanDatabaseMeta?: boolean;
        isUncommitted?: boolean;
    }): InternalValue<T>[];
    value({
        cleanMetadata,
    }: {
        cleanMetadata?: boolean;
        cleanDatabaseMeta?: boolean;
        isUncommitted?: boolean;
    }): PartialCollectionValueWithIds<T>[];
    value(): PartialCollectionValueWithIds<T>[];
    value(args: { cleanMetadata?: boolean; cleanDatabaseMeta?: boolean; isUncommitted?: boolean }): any[];
    where(fun: (data: InternalValue<T> & LokiObj) => boolean): this;
    withTemporaryRecords<B>(body: (db: NormalizedCollection<T>) => B, temporaryRecords: ExtractEdgesPartial<T>[]): B;
    startRecordTransaction(args: { recordId: string; recordLevel?: number }): void;
    commitRecord(args: { recordId: string; recordLevel?: number }): boolean;
    cancelRecordTransaction(args: { recordId: string; recordLevel?: number }): void;
}
