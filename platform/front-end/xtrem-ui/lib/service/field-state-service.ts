import type { AppThunkDispatch } from '../redux/action-types';
import { getStore } from '../redux/store';
import * as focusActions from '../redux/actions/focus-actions';
import * as dirtyStateActions from '../redux/actions/dirty-state-actions';
import * as fieldValueActions from '../redux/actions/field-value-actions';
import { checkIfPageIsLoaded, getPageDefinitionFromState } from '../utils/state-utils';
import { fetchField } from './graphql-service';
import type { PageDefinition } from './page-definition';
import { getUiComponentProperties } from './transactions-service';
import type { ReadonlyFieldControlObject } from '../component/readonly-field-control-object';
import { CollectionValue } from './collection-data-service';

export const isFieldDirty = (screenId: string, elementId: string): boolean =>
    getStore().getState().screenDefinitions[screenId].dirtyStates[elementId] || false;

export const setFieldDirty = (screenId: string, elementId: string): void => {
    const dispatch = getStore().dispatch as AppThunkDispatch;
    dispatch(dirtyStateActions.setFieldDirty({ screenId, elementId }));
};

export const setFieldClean = (screenId: string, elementId: string): void => {
    const dispatch = getStore().dispatch as AppThunkDispatch;
    dispatch(dirtyStateActions.setFieldClean({ screenId, elementId }));
};

export const refreshField = async ({
    screenId,
    elementId,
    keepPageInfo = false,
    keepModifications = true,
}: {
    screenId: string;
    elementId: string;
    keepPageInfo?: boolean;
    keepModifications?: boolean;
}): Promise<any> => {
    const store = getStore();
    const state = store.getState();
    const dispatch = getStore().dispatch as AppThunkDispatch;

    checkIfPageIsLoaded(screenId, state);

    const screenDefinition = state.screenDefinitions[screenId] as PageDefinition;
    const screenProperties = getUiComponentProperties(screenId, screenId);
    const userSettings = screenDefinition.userSettings || {};

    if (screenProperties.isTransient) {
        throw new Error('Cannot refresh fields on transient pages');
    }

    const fieldProps = getUiComponentProperties(screenId, elementId);

    if (fieldProps.isTransient) {
        throw new Error('Cannot refresh a transient field');
    }

    if (fieldProps.isTransientInput) {
        throw new Error('Cannot refresh a transient input field');
    }

    const queryParameters = screenDefinition.queryParameters || {};
    const id = queryParameters._id;
    if (!id) {
        throw new Error('Cannot refresh a field on an unloaded page');
    }

    const oldValue = state.screenDefinitions[screenId].values[elementId];

    const updatedValues = oldValue instanceof CollectionValue && keepModifications ? oldValue.getChangedRecords() : [];

    const newValue = await fetchField(
        screenDefinition,
        fieldProps,
        String(id),
        elementId,
        state.plugins,
        state.nodeTypes,
        elementId,
        keepPageInfo,
        userSettings,
    );

    if (oldValue instanceof CollectionValue && keepModifications) {
        (newValue as CollectionValue).updateMany(updatedValues);
    }

    if (oldValue !== newValue) {
        dispatch(fieldValueActions.setFieldValue(screenId, elementId, newValue, false));
    }

    return newValue;
};

export const focusField = (screenId: string, elementId: string): void => {
    checkIfPageIsLoaded(screenId);
    const store = getStore();
    store.dispatch(focusActions.setFocusPosition(screenId, elementId));
};

export const isFieldInFocus = (screenId: string, elementId: string): boolean => {
    checkIfPageIsLoaded(screenId);
    const state = getStore().getState();
    return state.focusPosition?.screenId === screenId && state.focusPosition?.elementId === elementId;
};

export const getFocussedField = (screenId: string): ReadonlyFieldControlObject<any, any, any, any> | null => {
    const state = getStore().getState();
    if (!state.focusPosition || state.focusPosition.screenId !== screenId || !state.focusPosition.elementId) {
        return null;
    }

    return getPageDefinitionFromState(screenId, state)!.metadata.controlObjects[
        state.focusPosition.elementId
    ] as ReadonlyFieldControlObject<any, any, any, any>;
};
