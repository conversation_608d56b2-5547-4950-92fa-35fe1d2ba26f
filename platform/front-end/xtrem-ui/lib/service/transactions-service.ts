import { checkIfPageIsLoaded, isFieldDataLoaded } from '../utils/state-utils';
import { mapValues } from 'lodash';
import type { ComponentKey, ControlObjectProps, FieldInternalValue, FieldKey } from '../component/types';
import { getStore } from '../redux/store';
import type { Dict } from '@sage/xtrem-shared';
import type { PageMetadata } from './page-metadata';
import type { ScreenBaseDefinition } from './screen-base-definition';
import { xtremConsole } from '../utils/console';
import * as transactionActions from '../redux/actions/transaction-actions';
import { NEW_PAGE } from '../utils/constants';

type UCPType<T extends ComponentKey> =
    ControlObjectProps<T> extends {
        getUiComponentProperties: (...args: any[]) => infer U;
    }
        ? U
        : any;

const transactionCommitReferences: Dict<number> = {};

class ReadOnlyTransactionError extends Error {}

let isReadOnly = false;
export interface XtremUiTransactionEntry {
    value: any;
    hasChangedInTransaction: boolean;
}

export interface XtremUiTransaction {
    values: Dict<XtremUiTransactionEntry>;
    uiComponentProperties: Dict<XtremUiTransactionEntry>;
}

const transactions: Dict<XtremUiTransaction> = {};

export const commitTransaction = (screenId: string): void => {
    const transaction = transactions[screenId];
    if (transaction) {
        const store = getStore();
        store.dispatch(transactionActions.commitTransaction(screenId, transaction));
        delete transactions[screenId];
        delete transactionCommitReferences[screenId];
    }
};

export const rollbackTransaction = (screenId: string): void => {
    if (transactionCommitReferences[screenId]) {
        clearTimeout(transactionCommitReferences[screenId]);
        delete transactionCommitReferences[screenId];
    }

    if (transactions[screenId]) {
        delete transactions[screenId];
    }
};

export const isTransactionInProgress = (screenId: string): boolean => !!transactions[screenId];

const transactionize = (
    from: ScreenBaseDefinition['values'] | PageMetadata['uiComponentProperties'],
): Dict<XtremUiTransactionEntry> =>
    mapValues(from, value => ({
        hasChangedInTransaction: false,
        value,
    }));

const startTransaction = (screenId: string): void => {
    if (!transactions[screenId]) {
        const store = getStore();
        const state = store.getState();
        checkIfPageIsLoaded(screenId, state);

        transactions[screenId] = {
            values: transactionize(state.screenDefinitions[screenId].values),
            uiComponentProperties: transactionize(state.screenDefinitions[screenId].metadata.uiComponentProperties),
        };
        transactionCommitReferences[screenId] = window.setTimeout(() => commitTransaction(screenId));
    }
};

/* State setters and getters */
export const getFieldValue = <T extends FieldKey>(screenId: string, elementId: string): FieldInternalValue<T> => {
    const state = getStore().getState();
    if (!isFieldDataLoaded(state.screenDefinitions[screenId], elementId)) {
        throw new Error(`Cannot get value of field "${elementId}" on "${screenId}" because it is not loaded yet.`);
    }

    let fieldValue;
    if (transactions[screenId]) {
        fieldValue = !transactions[screenId].values[elementId]
            ? undefined
            : transactions[screenId].values[elementId].value;
    } else {
        fieldValue = state.screenDefinitions[screenId].values[elementId];
    }

    if (elementId === '_id' && fieldValue === NEW_PAGE) {
        return null as FieldInternalValue<T>;
    }
    return fieldValue;
};

export const setFieldValue = <T extends FieldKey>(
    screenId: string,
    elementId: string,
    newValue: FieldInternalValue<T>,
): void => {
    if (isReadOnly) {
        throw new ReadOnlyTransactionError(
            `It is forbidden to set a value of a field from a callback property. Updating other parts of your screen is only allowed by using event handlers. Screen ID: ${screenId}, element ID: ${elementId}`,
        );
    }

    const state = getStore().getState();

    if (!isFieldDataLoaded(state.screenDefinitions[screenId], elementId)) {
        throw new Error(`Cannot set value of field "${elementId}" on "${screenId}" because it is not loaded yet.`);
    }

    startTransaction(screenId);
    transactions[screenId].values[elementId] = {
        value: newValue,
        hasChangedInTransaction: true,
    };
};

export const getUiComponentProperties = <T extends ComponentKey>(screenId: string, elementId: string): UCPType<T> => {
    checkIfPageIsLoaded(screenId);

    let uiComponentProperties: UCPType<T>;
    if (transactions[screenId]) {
        uiComponentProperties = !transactions[screenId].uiComponentProperties[elementId]
            ? undefined
            : transactions[screenId].uiComponentProperties[elementId].value;
    } else {
        uiComponentProperties = getStore().getState().screenDefinitions[screenId].metadata.uiComponentProperties[
            elementId
        ] as UCPType<T>;
    }
    return uiComponentProperties;
};

export const setUiComponentProperties = <T extends ComponentKey>(
    screenId: string,
    elementId: string,
    newValue: Partial<ControlObjectProps<T>>,
): void => {
    if (isReadOnly) {
        throw new ReadOnlyTransactionError(
            `It is forbidden to set a property of a field from a callback property. Updating other parts of your screen is only allowed by using event handlers. Screen ID: ${screenId}, element ID: ${elementId}`,
        );
    }
    startTransaction(screenId);
    transactions[screenId].uiComponentProperties[elementId] = {
        value: newValue,
        hasChangedInTransaction: true,
    };
};

export const executeInReadOnlyTransaction = (callback: () => any): any => {
    isReadOnly = true;
    let result = null;
    try {
        result = callback();
        isReadOnly = false;
        return result;
    } catch (e) {
        isReadOnly = false;
        if (e instanceof ReadOnlyTransactionError) {
            xtremConsole.error(e);
            return result;
        }
        throw e;
    } finally {
        isReadOnly = false;
    }
};
