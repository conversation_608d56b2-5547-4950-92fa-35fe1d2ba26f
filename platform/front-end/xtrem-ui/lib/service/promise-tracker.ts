export abstract class PromiseTracker {
    /** The number of tracked async operations which are currently executing */
    private static pendingPromisesCount = 0;

    /** add the has-pending-promises element to the document */
    private static ensurePendingElementExists = (): void => {
        if (!document.getElementById('has-pending-promises')) {
            const apiReadyElement = document.createElement('div');
            apiReadyElement.setAttribute('id', 'has-pending-promises');
            apiReadyElement.setAttribute('hidden', 'true');
            document.body.appendChild(apiReadyElement);
        }
    };

    private static deletePendingElementIfExists(): void {
        const apiReadyElement = document.getElementById('has-pending-promises');
        if (apiReadyElement) document.body.removeChild(apiReadyElement);
    }

    private static pushPending(): void {
        if (this.pendingPromisesCount === 0) this.ensurePendingElementExists();
        this.pendingPromisesCount += 1;
    }

    private static popPending(): void {
        this.pendingPromisesCount -= 1;
        if (this.pendingPromisesCount === 0) this.deletePendingElementIfExists();
    }

    /**
     * This function is called when a dialog is open on the screen.
     *
     * Dialogs are tricky because they create a promise which is only resolved when the user closes the dialog.
     * We have to decrement pendingPromisesCount so that it can get back to 0 while the dialog is open.
     */
    static pushDialog(): void {
        this.popPending();
    }

    /**
     * This function is called when a dialog is closed.
     *
     * We increment pendingPromisesCount to counter-balance what we pushDialog did earlier.
     */
    static popDialog(): void {
        this.pushPending();
    }

    /**
     * This function tracks the execution of an async operations
     *
     * It adds a hidden #has-pending-promises div element to the DOM at the beginning of the operation and
     * removes it when the operation completes.
     *
     * This div elements is used to wait on the completion of async operations (graphql API calls, refresh of components)
     * in our library of cucumber steps (see the waitForPromises() function in xtrem-cli)
     */
    static async withTracker<T>(body: () => Promise<T>): Promise<T> {
        this.pushPending();
        try {
            return await body();
        } finally {
            this.popPending();
        }
    }
}
