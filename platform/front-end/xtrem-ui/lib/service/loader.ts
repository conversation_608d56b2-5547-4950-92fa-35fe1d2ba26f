import type { AppThunkDispatch } from '../redux';
import { actions, getStore } from '../redux';

/**
 * Global loader dialog that blocks the screen so the user cannot interact with the application while it is displayed.
 */
export class Loader {
    /**
     * Hide the global loader dialog that blocks the user interactions.
     * @deprecated#7.0.0
     */
    hide(): void {
        const dispatch: AppThunkDispatch = getStore().dispatch;
        dispatch(actions.setGlobalLoading(false));
    }

    /**
     * Display the global loader dialog that blocks the user interactions.
     * @deprecated#7.0.0
     */
    display(): void {
        const dispatch: AppThunkDispatch = getStore().dispatch;
        dispatch(actions.setGlobalLoading(true));
    }

    /**
     * Whether the global loading dialog is hidden or not
     */
    set isHidden(value: boolean) {
        const dispatch: AppThunkDispatch = getStore().dispatch;
        dispatch(actions.setGlobalLoading(!value));
    }

    /**
     * Whether the global loading dialog is hidden or not
     */
    get isHidden(): boolean {
        return getStore().getState().loading.globalLoading;
    }
}

export default new Loader();
