import type {
    BinaryStream,
    BinaryStreamMatcher,
    ClientCollectionMatcher,
    ClientNode,
    EdgesMatcher,
    ExtractEdgesPartial,
} from '@sage/xtrem-client';
import type {
    Dict,
    AttachmentAssociation as SharedAttachmentAssociation,
    stringFilter,
    UploadedFile,
    User,
} from '@sage/xtrem-shared';
import { aggregationsGraphqlMapping, FieldKey, filterGraphqlMapping, GraphQLTypes } from '@sage/xtrem-shared';
import type { Builtin, OmitProperties } from 'ts-essentials';
import type { CollectionValue } from '../service/collection-data-service';
import type { PageArticleItem } from '../service/layout-types';
import type { Page } from '../service/page';
import type { PageMetadata } from '../service/page-metadata';
import type { ScreenBase } from '../service/screen-base';
import type { ValidationResult } from '../service/screen-base-definition';
import type { Sticker } from '../service/sticker';
import type { PickOwnProperties, SingleKeyed } from '../utils/types';
import type { AbstractFieldLayoutBuilder } from './abstract-field-layout-builder';
import type {
    BlockLayout,
    DetailPanelLayout,
    GridRowBlockLayout,
    PageLayout,
    SectionLayout,
    StickerLayout,
    TileLayout,
} from './container/layouts';
import type {
    AggregateControlObject,
    AggregateProperties,
    BlockControlObject,
    BlockProperties,
    ButtonControlObject,
    ButtonProperties,
    CalendarControlObject,
    CalendarProperties,
    CardControlObject,
    CardProperties,
    ChartControlObject,
    ChartProperties,
    CheckboxControlObject,
    CheckboxProperties,
    ContentTableControlObject,
    ContentTableProperties,
    CountControlObject,
    CountProperties,
    DateControlObject,
    DateProperties,
    DatetimeControlObject,
    DatetimeProperties,
    DetailListControlObject,
    DetailListProperties,
    DetailPanelControlObject,
    DetailPanelProperties,
    DropdownListControlObject,
    DropdownListProperties,
    DynamicPodControlObject,
    DynamicSelectControlObject,
    FileControlObject,
    FileDepositControlObject,
    FileDepositProperties,
    FileProperties,
    FilterEditorControlObject,
    FilterEditorProperties,
    FilterSelectControlObject,
    FilterSelectProperties,
    FormDesignerControlObject,
    FormDesignerProperties,
    FragmentFieldsControlObject,
    GridRowBlockControlObject,
    GridRowBlockProperties,
    IBlockControlObject,
    IconControlObject,
    IconProperties,
    IDetailPanelControlObject,
    IFragmentFieldsControlObject,
    IGridRowBlockControlObject,
    ImageControlObject,
    ImageProperties,
    IPageAction,
    IPageControlObject,
    IPageFragmentControlObject,
    ISectionControlObject,
    IStickerControlObject,
    ITileControlObject,
    LabelControlObject,
    LabelProperties,
    LinkControlObject,
    LinkProperties,
    MessageControlObject,
    MessageProperties,
    MultiDropdownControlObject,
    MultiDropdownProperties,
    MultiFileDepositControlObject,
    MultiFileDepositProperties,
    MultiReferenceControlObject,
    MultiReferenceProperties,
    NestedGridControlObject,
    NestedGridProperties,
    NodeBrowserTreeComponentProperties,
    NodeBrowserTreeControlObject,
    NumericControlObject,
    NumericProperties,
    PageActionControlObject,
    PageActionProperties,
    PageControlObject,
    PageFragmentControlObject,
    PageProperties,
    PluginControlObject,
    PluginProperties,
    PodCollectionControlObject,
    PodCollectionProperties,
    PodControlObject,
    PodProperties,
    PreviewControlObject,
    PreviewProperties,
    ProgressControlObject,
    ProgressProperties,
    RadioControlObject,
    RadioProperties,
    ReferenceControlObject,
    ReferenceProperties,
    RelativeDateControlObject,
    RichTextControlObject,
    RichTextProperties,
    SectionControlObject,
    SectionProperties,
    SelectControlObject,
    SelectionCardControlObject,
    SelectionCardProperties,
    SelectProperties,
    SeparatorControlObject,
    SeparatorProperties,
    StaticContentControlObject,
    StaticContentProperties,
    StepSequenceControlObject,
    StepSequenceProperties,
    StickerControlObject,
    StickerProperties,
    SwitchControlObject,
    SwitchProperties,
    TableControlObject,
    TableProperties,
    TableSummaryControlObject,
    TableSummaryProperties,
    TechnicalJsonControlObject,
    TextAreaControlObject,
    TextAreaProperties,
    TextControlObject,
    TextProperties,
    TileControlObject,
    TileProperties,
    TimeControlObject,
    TimeProperties,
    ToggleControlObject,
    ToggleProperties,
    TreeControlObject,
    TreeProperties,
    VisualProcessControlObject,
    VisualProcessProperties,
    VitalPodControlObject,
    VitalPodProperties,
    WorkflowControlObject,
    WorkflowProperties,
} from './control-objects';
import type {
    AggregateDecoratorProperties,
    BlockDecoratorProperties,
    ButtonDecoratorProperties,
    CalendarDecoratorProperties,
    CardDecoratorProperties,
    ChartDecoratorProperties,
    CheckboxDecoratorProperties,
    ContentTableDecoratorProperties,
    CountDecoratorProperties,
    DateDecoratorProperties,
    DatetimeDecoratorProperties,
    DetailListDecoratorProperties,
    DetailPanelDecoratorProperties,
    DropdownListDecoratorProperties,
    DynamicPodDecoratorProperties,
    DynamicSelectDecoratorProperties,
    FileDecoratorProperties,
    FileDepositDecoratorProperties,
    FilterEditorDecoratorProperties,
    FilterSelectDecoratorProperties,
    FormDesignerDecoratorProperties,
    FragmentFieldsDecoratorProperties,
    GridRowBlockDecoratorProperties,
    IconDecoratorProperties,
    ImageDecoratorProperties,
    LabelDecoratorProperties,
    LinkDecoratorProperties,
    MessageDecoratorProperties,
    MultiDropdownDecoratorProperties,
    MultiFileDepositDecoratorProperties,
    MultiReferenceDecoratorProperties,
    NestedGridDecoratorProperties,
    NodeBrowserTreeDecoratorProperties,
    NumericDecoratorProperties,
    PageActionDecoratorProperties,
    PageDecoratorProperties,
    PageFragmentDecoratorProperties,
    PdfDecoratorProperties,
    PluginDecoratorProperties,
    PodDecoratorProperties,
    ProgressDecoratorProperties,
    RadioDecoratorProperties,
    ReferenceDecoratorProperties,
    RelativeDateDecoratorProperties,
    RichTextDecoratorProperties,
    SectionDecoratorProperties,
    SelectDecoratorProperties,
    SelectionCardDecoratorProperties,
    SeparatorDecoratorProperties,
    StaticContentDecoratorProperties,
    StepSequenceDecoratorProperties,
    StickerDecoratorProperties,
    SwitchDecoratorProperties,
    TableDecoratorProperties,
    TableSummaryDecoratorProperties,
    TechnicalJsonDecoratorProperties,
    TextAreaDecoratorProperties,
    TextDecoratorProperties,
    TileDecoratorProperties,
    TimeDecoratorProperties,
    ToggleDecoratorProperties,
    TreeDecoratorProperties,
    VisualProcessDecoratorProperties,
    VitalPodDecoratorProperties,
    WorkflowDecoratorProperties,
} from './decorator-properties';
import type { ContentTableFieldValue } from './field/content-table/content-table-types';
import type { DatetimeRangeControlObject } from './field/datetime-range/datetime-range-control-object';
import type {
    DatetimeRangeDecoratorProperties,
    DatetimeRangeProperties,
    DatetimeRangeValue,
} from './field/datetime-range/datetime-range-types';
import type { DynamicPodProperties } from './field/dynamic-pod/dynamic-pod-types';
import type { DynamicSelectProperties } from './field/dynamic-select/dynamic-select-types';
import type { FilterEditorFieldValue } from './field/filter-editor/filter-editor-types';
import type { NodeBrowserTreeValue } from './field/node-browser-tree/node-browser-tree-types';
import type { DropdownActionItem, DropdownActionItemOrMenuSeparator } from './field/traits';
import type { WorkflowFieldValue } from './field/workflow/workflow-types';
import type { ReadonlyFieldProperties } from './readonly-field-control-object';
import type {
    NestedCollectionItemAction,
    NestedCollectionItemActionGroup,
    NestedCollectionItemActionOrMenuSeparator,
} from './ui/table-shared/table-dropdown-actions/table-dropdown-action-types';
import type { DatetimeValue } from './field/datetime/datetime-types';

export { FieldKey };

export { GraphQLTypes };

export type ComponentKey = ContainerKey | FieldKey | ActionKey;

export enum ActionKey {
    PageAction = 'PageAction',
}

export enum ContainerKey {
    Block = 'Block',
    DetailPanel = 'DetailPanel',
    GridRowBlock = 'GridRowBlock',
    Page = 'Page',
    PageFragment = 'PageFragment',
    FragmentFields = 'FragmentFields',
    Section = 'Section',
    Sticker = 'Sticker',
    Tile = 'Tile',
}

export type ContainerWidth = 'extra-large' | 'large' | 'medium' | 'half' | 'small' | 'extra-small';
export type FieldWidth = 'extra-large' | 'large' | 'medium' | 'small-medium' | 'half' | 'small';

export type DecoratorTarget<T> = T extends ContainerKey.Page
    ? Function
    : T extends ContainerKey.PageFragment
      ? Function
      : T extends ContainerKey.Sticker
        ? Function
        : ScreenBase;

export type ControlObjectConstructor<T extends ComponentKey> = (new (
    props: ControlObjectConstructorProps<T>,
) => ControlObjectInstance<T>) & { defaultUiProperties: Partial<DecoratorProperties<T>> };

export type ContainerProperties =
    | BlockDecoratorProperties
    | FragmentFieldsDecoratorProperties
    | SectionDecoratorProperties
    | GridRowBlockDecoratorProperties
    | TileDecoratorProperties;

export type Omit<T, K> = Pick<T, Exclude<keyof T, K>>;

export type CollectionValueFieldKey =
    | FieldKey.Calendar
    | FieldKey.MultiFileDeposit
    | FieldKey.PodCollection
    | FieldKey.Table
    | FieldKey.TableSummary
    | FieldKey.Tree;

export type DecoratorProperties<
    T extends ComponentKey,
    CT extends ScreenBase = any,
    N extends ClientNode = any,
> = T extends ContainerKey
    ? ContainerDecoratorProps<T, CT>
    : T extends FieldKey
      ? FieldDecoratorProps<T, CT, N>
      : T extends ActionKey
        ? ActionDecoratorProps<T, CT>
        : never;

export type ContainerDecoratorProps<T extends ContainerKey, CT extends ScreenBase = any> = T extends ContainerKey.Block
    ? BlockDecoratorProperties<CT>
    : T extends ContainerKey.GridRowBlock
      ? GridRowBlockDecoratorProperties<CT>
      : T extends ContainerKey.Tile
        ? TileDecoratorProperties<CT>
        : T extends ContainerKey.Section
          ? SectionDecoratorProperties<CT>
          : T extends ContainerKey.Sticker
            ? StickerDecoratorProperties<Sticker>
            : T extends ContainerKey.Page
              ? PageDecoratorProperties<Page>
              : T extends ContainerKey.PageFragment
                ? PageFragmentDecoratorProperties
                : T extends ContainerKey.FragmentFields
                  ? FragmentFieldsDecoratorProperties
                  : T extends ContainerKey.DetailPanel
                    ? DetailPanelDecoratorProperties<CT>
                    : never;

export type FieldDecoratorProps<
    T extends FieldKey,
    CT extends ScreenBase = any,
    N extends ClientNode = any,
> = T extends FieldKey.Plugin
    ? PluginDecoratorProperties<CT>
    : T extends FieldKey.Aggregate
      ? AggregateDecoratorProperties<CT, N>
      : T extends FieldKey.Button
        ? ButtonDecoratorProperties<CT>
        : T extends FieldKey.Card
          ? CardDecoratorProperties<CT, N>
          : T extends FieldKey.Calendar
            ? CalendarDecoratorProperties<CT, N>
            : T extends FieldKey.Chart
              ? ChartDecoratorProperties<CT, N>
              : T extends FieldKey.Checkbox
                ? CheckboxDecoratorProperties<CT>
                : T extends FieldKey.ContentTable
                  ? ContentTableDecoratorProperties<CT>
                  : T extends FieldKey.Count
                    ? CountDecoratorProperties<CT, N>
                    : T extends FieldKey.Date
                      ? DateDecoratorProperties<CT>
                      : T extends FieldKey.Time
                        ? TimeDecoratorProperties<CT>
                        : T extends FieldKey.TechnicalJson
                          ? TechnicalJsonDecoratorProperties<CT>
                          : T extends FieldKey.Datetime
                            ? DatetimeDecoratorProperties<CT>
                            : T extends FieldKey.DatetimeRange
                              ? DatetimeRangeDecoratorProperties<CT>
                              : T extends FieldKey.DetailList
                                ? DetailListDecoratorProperties<CT, N>
                                : T extends FieldKey.DynamicPod
                                  ? DynamicPodDecoratorProperties<CT>
                                  : T extends FieldKey.DynamicSelect
                                    ? DynamicSelectDecoratorProperties<CT>
                                    : T extends FieldKey.DropdownList
                                      ? DropdownListDecoratorProperties<CT>
                                      : T extends FieldKey.File
                                        ? FileDecoratorProperties<CT>
                                        : T extends FieldKey.FileDeposit
                                          ? FileDepositDecoratorProperties<CT>
                                          : T extends FieldKey.FilterEditor
                                            ? FilterEditorDecoratorProperties<CT>
                                            : T extends FieldKey.FilterSelect
                                              ? FilterSelectDecoratorProperties<CT, N>
                                              : T extends FieldKey.FormDesigner
                                                ? FormDesignerDecoratorProperties<CT>
                                                : T extends FieldKey.Icon
                                                  ? IconDecoratorProperties<CT>
                                                  : T extends FieldKey.Image
                                                    ? ImageDecoratorProperties<CT>
                                                    : T extends FieldKey.Label
                                                      ? LabelDecoratorProperties<CT>
                                                      : T extends FieldKey.Link
                                                        ? LinkDecoratorProperties<CT>
                                                        : T extends FieldKey.Message
                                                          ? MessageDecoratorProperties<CT>
                                                          : T extends FieldKey.MultiDropdown
                                                            ? MultiDropdownDecoratorProperties<CT>
                                                            : T extends FieldKey.MultiFileDeposit
                                                              ? MultiFileDepositDecoratorProperties<CT>
                                                              : T extends FieldKey.MultiReference
                                                                ? MultiReferenceDecoratorProperties<CT, N>
                                                                : T extends FieldKey.NestedGrid
                                                                  ? NestedGridDecoratorProperties<CT>
                                                                  : T extends FieldKey.NodeBrowserTree
                                                                    ? NodeBrowserTreeDecoratorProperties<CT>
                                                                    : T extends FieldKey.Numeric
                                                                      ? NumericDecoratorProperties<CT>
                                                                      : T extends FieldKey.Progress
                                                                        ? ProgressDecoratorProperties<CT>
                                                                        : T extends FieldKey.Radio
                                                                          ? RadioDecoratorProperties<CT>
                                                                          : T extends FieldKey.Reference
                                                                            ? ReferenceDecoratorProperties<CT, N>
                                                                            : T extends FieldKey.RelativeDate
                                                                              ? RelativeDateDecoratorProperties<CT>
                                                                              : T extends FieldKey.RichText
                                                                                ? RichTextDecoratorProperties<CT>
                                                                                : T extends FieldKey.Pod
                                                                                  ? PodDecoratorProperties<CT, N>
                                                                                  : T extends FieldKey.PodCollection
                                                                                    ? PodCollectionProperties<CT, N>
                                                                                    : T extends FieldKey.Select
                                                                                      ? SelectDecoratorProperties<CT>
                                                                                      : T extends FieldKey.SelectionCard
                                                                                        ? SelectionCardDecoratorProperties<CT>
                                                                                        : T extends FieldKey.Separator
                                                                                          ? SeparatorDecoratorProperties<CT>
                                                                                          : T extends FieldKey.StaticContent
                                                                                            ? StaticContentDecoratorProperties<CT>
                                                                                            : T extends FieldKey.StepSequence
                                                                                              ? StepSequenceDecoratorProperties<CT>
                                                                                              : T extends FieldKey.Switch
                                                                                                ? SwitchDecoratorProperties<CT>
                                                                                                : T extends FieldKey.Table
                                                                                                  ? TableDecoratorProperties<
                                                                                                        CT,
                                                                                                        N
                                                                                                    >
                                                                                                  : T extends FieldKey.TableSummary
                                                                                                    ? TableSummaryDecoratorProperties<
                                                                                                          CT,
                                                                                                          N
                                                                                                      >
                                                                                                    : T extends FieldKey.Text
                                                                                                      ? TextDecoratorProperties<CT>
                                                                                                      : T extends FieldKey.TextArea
                                                                                                        ? TextAreaDecoratorProperties<CT>
                                                                                                        : T extends FieldKey.Toggle
                                                                                                          ? ToggleDecoratorProperties<CT>
                                                                                                          : T extends FieldKey.Tree
                                                                                                            ? TreeDecoratorProperties<
                                                                                                                  CT,
                                                                                                                  N
                                                                                                              >
                                                                                                            : T extends FieldKey.VisualProcess
                                                                                                              ? VisualProcessDecoratorProperties<CT>
                                                                                                              : T extends FieldKey.VitalPod
                                                                                                                ? VitalPodDecoratorProperties<
                                                                                                                      CT,
                                                                                                                      N
                                                                                                                  >
                                                                                                                : T extends FieldKey.Workflow
                                                                                                                  ? WorkflowDecoratorProperties<CT>
                                                                                                                  : T extends FieldKey.Preview
                                                                                                                    ? PdfDecoratorProperties<CT>
                                                                                                                    : never;

export type ActionDecoratorProps<T extends ActionKey, CT extends ScreenBase = any> = T extends ActionKey.PageAction
    ? PageActionDecoratorProperties<CT>
    : never;

export type ContainerLayoutBuilder<T extends ContainerKey> = T extends ContainerKey.Block
    ? BlockLayout
    : T extends ContainerKey.Section
      ? SectionLayout
      : T extends ContainerKey.GridRowBlock
        ? GridRowBlockLayout
        : T extends ContainerKey.Tile
          ? TileLayout
          : T extends ContainerKey.Sticker
            ? StickerLayout
            : T extends ContainerKey.Page
              ? PageLayout
              : T extends ContainerKey.DetailPanel
                ? DetailPanelLayout
                : never;

export type LayoutBuilder<T extends ComponentKey> = T extends ContainerKey
    ? ContainerLayoutBuilder<T>
    : T extends FieldKey
      ? AbstractFieldLayoutBuilder<T>
      : never;

export type ContainerControlObjectInstance<T extends ContainerKey> = T extends ContainerKey.Block
    ? BlockControlObject
    : T extends ContainerKey.GridRowBlock
      ? GridRowBlockControlObject
      : T extends ContainerKey.Tile
        ? TileControlObject
        : T extends ContainerKey.Page
          ? PageControlObject
          : T extends ContainerKey.FragmentFields
            ? FragmentFieldsControlObject
            : T extends ContainerKey.PageFragment
              ? PageFragmentControlObject
              : T extends ContainerKey.Section
                ? SectionControlObject
                : T extends ContainerKey.Sticker
                  ? StickerControlObject
                  : T extends ContainerKey.DetailPanel
                    ? DetailPanelControlObject
                    : never;

export type FieldControlObjectInstance<T extends FieldKey> = T extends FieldKey.Plugin
    ? PluginControlObject
    : T extends FieldKey.Aggregate
      ? AggregateControlObject
      : T extends FieldKey.Button
        ? ButtonControlObject
        : T extends FieldKey.Card
          ? CardControlObject
          : T extends FieldKey.Calendar
            ? CalendarControlObject
            : T extends FieldKey.Chart
              ? ChartControlObject
              : T extends FieldKey.Checkbox
                ? CheckboxControlObject
                : T extends FieldKey.ContentTable
                  ? ContentTableControlObject
                  : T extends FieldKey.Count
                    ? CountControlObject
                    : T extends FieldKey.Date
                      ? DateControlObject
                      : T extends FieldKey.Time
                        ? TimeControlObject
                        : T extends FieldKey.TechnicalJson
                          ? TechnicalJsonControlObject
                          : T extends FieldKey.Datetime
                            ? DatetimeControlObject
                            : T extends FieldKey.DatetimeRange
                              ? DatetimeRangeControlObject
                              : T extends FieldKey.DetailList
                                ? DetailListControlObject
                                : T extends FieldKey.DynamicPod
                                  ? DynamicPodControlObject
                                  : T extends FieldKey.DynamicSelect
                                    ? DynamicSelectControlObject
                                    : T extends FieldKey.DropdownList
                                      ? DropdownListControlObject
                                      : T extends FieldKey.File
                                        ? FileControlObject
                                        : T extends FieldKey.FileDeposit
                                          ? FileDepositControlObject
                                          : T extends FieldKey.FilterEditor
                                            ? FilterEditorControlObject
                                            : T extends FieldKey.FilterSelect
                                              ? FilterSelectControlObject
                                              : T extends FieldKey.FormDesigner
                                                ? FormDesignerControlObject
                                                : T extends FieldKey.Icon
                                                  ? IconControlObject
                                                  : T extends FieldKey.Image
                                                    ? ImageControlObject
                                                    : T extends FieldKey.Label
                                                      ? LabelControlObject
                                                      : T extends FieldKey.Link
                                                        ? LinkControlObject
                                                        : T extends FieldKey.Message
                                                          ? MessageControlObject
                                                          : T extends FieldKey.MultiDropdown
                                                            ? MultiDropdownControlObject
                                                            : T extends FieldKey.MultiReference
                                                              ? MultiReferenceControlObject
                                                              : T extends FieldKey.MultiFileDeposit
                                                                ? MultiFileDepositControlObject
                                                                : T extends FieldKey.NestedGrid
                                                                  ? NestedGridControlObject
                                                                  : T extends FieldKey.Numeric
                                                                    ? NumericControlObject
                                                                    : T extends FieldKey.NodeBrowserTree
                                                                      ? NodeBrowserTreeControlObject
                                                                      : T extends FieldKey.Progress
                                                                        ? ProgressControlObject
                                                                        : T extends FieldKey.Radio
                                                                          ? RadioControlObject
                                                                          : T extends FieldKey.Reference
                                                                            ? ReferenceControlObject
                                                                            : T extends FieldKey.RelativeDate
                                                                              ? RelativeDateControlObject
                                                                              : T extends FieldKey.RichText
                                                                                ? RichTextControlObject
                                                                                : T extends FieldKey.Pod
                                                                                  ? PodControlObject
                                                                                  : T extends FieldKey.PodCollection
                                                                                    ? PodCollectionControlObject
                                                                                    : T extends FieldKey.Select
                                                                                      ? SelectControlObject
                                                                                      : T extends FieldKey.SelectionCard
                                                                                        ? SelectionCardControlObject
                                                                                        : T extends FieldKey.Separator
                                                                                          ? SeparatorControlObject
                                                                                          : T extends FieldKey.StaticContent
                                                                                            ? StaticContentControlObject
                                                                                            : T extends FieldKey.StepSequence
                                                                                              ? StepSequenceControlObject
                                                                                              : T extends FieldKey.Switch
                                                                                                ? SwitchControlObject
                                                                                                : T extends FieldKey.Table
                                                                                                  ? TableControlObject
                                                                                                  : T extends FieldKey.TableSummary
                                                                                                    ? TableSummaryControlObject
                                                                                                    : T extends FieldKey.Text
                                                                                                      ? TextControlObject
                                                                                                      : T extends FieldKey.TextArea
                                                                                                        ? TextAreaControlObject
                                                                                                        : T extends FieldKey.Toggle
                                                                                                          ? ToggleControlObject
                                                                                                          : T extends FieldKey.Tree
                                                                                                            ? TreeControlObject
                                                                                                            : T extends FieldKey.VisualProcess
                                                                                                              ? VisualProcessControlObject
                                                                                                              : T extends FieldKey.VitalPod
                                                                                                                ? VitalPodControlObject
                                                                                                                : T extends FieldKey.Workflow
                                                                                                                  ? WorkflowControlObject
                                                                                                                  : T extends FieldKey.Preview
                                                                                                                    ? PreviewControlObject
                                                                                                                    : never;

export type ControlObjectInstance<T extends ComponentKey> = T extends ContainerKey
    ? ContainerControlObjectInstance<T>
    : T extends FieldKey
      ? FieldControlObjectInstance<T>
      : T extends ActionKey.PageAction
        ? PageActionControlObject
        : never;

export interface MetadataProps<T extends ComponentKey> {
    pageMetadata: PageMetadata;
    properties: DecoratorProperties<T>;
    extensionPackageName?: string;
}

export type ContainerComponentProps<T extends ContainerKey> = T extends ContainerKey.Block
    ? BlockProperties
    : T extends ContainerKey.GridRowBlock
      ? GridRowBlockProperties
      : T extends ContainerKey.Tile
        ? TileProperties
        : T extends ContainerKey.Page
          ? PageProperties<any>
          : T extends ContainerKey.FragmentFields
            ? FragmentFieldsDecoratorProperties<any>
            : T extends ContainerKey.PageFragment
              ? PageFragmentDecoratorProperties<any>
              : T extends ContainerKey.Section
                ? SectionProperties
                : T extends ContainerKey.Sticker
                  ? StickerProperties
                  : T extends ContainerKey.DetailPanel
                    ? DetailPanelProperties
                    : never;

export type ControlObjectConstructorProps<T extends ComponentKey> = T extends ContainerKey.Block
    ? IBlockControlObject
    : T extends ContainerKey.GridRowBlock
      ? IGridRowBlockControlObject
      : T extends ContainerKey.Tile
        ? ITileControlObject
        : T extends ContainerKey.Page
          ? IPageControlObject
          : T extends ContainerKey.FragmentFields
            ? IFragmentFieldsControlObject
            : T extends ContainerKey.PageFragment
              ? IPageFragmentControlObject
              : T extends ContainerKey.Section
                ? ISectionControlObject
                : T extends ContainerKey.Sticker
                  ? IStickerControlObject
                  : T extends ContainerKey.DetailPanel
                    ? IDetailPanelControlObject
                    : T extends FieldKey
                      ? FieldControlObjectConstructorProps<T>
                      : T extends ActionKey.PageAction
                        ? IPageAction
                        : never;

export type ControlObjectProps<T extends ComponentKey> = T extends ContainerKey
    ? ContainerComponentProps<T>
    : T extends FieldKey
      ? FieldComponentProps<T>
      : T extends ActionKey.PageAction
        ? PageActionProperties
        : never;

export interface ContainerControlObjectConstructorProps<T extends ContainerKey> {
    elementId: string;
    getValidationState?: () => Promise<boolean>;
    getUiComponentProperties?: (screenId: string, elementId: string) => ContainerComponentProps<T>;
    insertBefore?: InsertBeforeType<T>;
    insertAfter?: InsertBeforeType<T>;
    layout: LayoutContent<T>;
    screenId: string;
    setUiComponentProperties?: (screenId: string, elementId: string, state: ContainerComponentProps<T>) => void;
}

export type CollectionItem = Dict<any>;

export interface BinaryValue {
    value: string;
}

export interface GraphqlCollection<T = CollectionItem> {
    data: T[];
    pageInfo: PageInfo;
}

export interface PageInfo {
    startCursor?: string;
    endCursor?: string;
    hasPreviousPage?: boolean;
    hasNextPage?: boolean;
}

export type FieldValue<T extends FieldKey, N extends ClientNode = any> = T extends
    | FieldKey.Numeric
    | FieldKey.Progress
    | FieldKey.Aggregate
    ? number
    : T extends FieldKey.Workflow
      ? WorkflowFieldValue
      : T extends FieldKey.Chart
        ? CollectionItem[]
        : T extends FieldKey.Table
          ? CollectionItem[]
          : T extends FieldKey.NodeBrowserTree
            ? NodeBrowserTreeValue
            : T extends FieldKey.PodCollection
              ? CollectionItem[]
              : T extends FieldKey.MultiFileDeposit
                ? CollectionItem[]
                : T extends FieldKey.Calendar
                  ? CollectionItem[]
                  : T extends FieldKey.ContentTable
                    ? ContentTableFieldValue
                    : T extends FieldKey.Datetime
                      ? DatetimeValue
                      : T extends FieldKey.DatetimeRange
                        ? DatetimeRangeValue
                        : T extends FieldKey.DetailList
                          ? CollectionItem[]
                          : T extends FieldKey.DynamicPod
                            ? any
                            : T extends FieldKey.DynamicSelect
                              ? string
                              : T extends FieldKey.TableSummary
                                ? CollectionItem[]
                                : T extends FieldKey.TechnicalJson
                                  ? any
                                  : T extends FieldKey.Checkbox
                                    ? boolean
                                    : T extends FieldKey.Switch
                                      ? boolean
                                      : T extends FieldKey.Count
                                        ? number | null
                                        : T extends FieldKey.File
                                          ? BinaryValue
                                          : T extends FieldKey.FileDeposit
                                            ? FileDepositValue
                                            : T extends FieldKey.FilterEditor
                                              ? FilterEditorFieldValue
                                              : T extends FieldKey.FormDesigner
                                                ? BinaryValue
                                                : T extends FieldKey.Image
                                                  ? BinaryValue
                                                  : T extends FieldKey.StepSequence
                                                    ? string
                                                    : T extends FieldKey.VisualProcess
                                                      ? BinaryValue
                                                      : T extends FieldKey.MultiDropdown
                                                        ? string[]
                                                        : T extends FieldKey.MultiFileDeposit
                                                          ? FileDepositValue
                                                          : T extends FieldKey.MultiReference
                                                            ? Partial<CollectionItem>[]
                                                            : T extends FieldKey.NestedGrid
                                                              ? PartialCollectionValue<N>[]
                                                              : T extends FieldKey.Reference
                                                                ? Partial<CollectionItem>
                                                                : T extends FieldKey.Card
                                                                  ? Partial<CollectionItem>
                                                                  : T extends FieldKey.Pod
                                                                    ? Partial<CollectionItem>
                                                                    : T extends FieldKey.VitalPod
                                                                      ? Partial<CollectionItem>
                                                                      : T extends FieldKey.Preview
                                                                        ? string | BinaryValue
                                                                        : string;

export type FieldInternalValue<T extends FieldKey, N extends ClientNode = any> = T extends FieldKey.Table
    ? CollectionValue<N>
    : T extends FieldKey.Tree
      ? CollectionValue<N>
      : T extends FieldKey.PodCollection
        ? CollectionValue<N>
        : T extends FieldKey.MultiFileDeposit
          ? CollectionValue<N>
          : T extends FieldKey.TableSummary
            ? CollectionValue<N>
            : T extends FieldKey.Calendar
              ? CollectionValue<N>
              : T extends FieldKey.NestedGrid
                ? CollectionValue<N>
                : T extends FieldKey.DetailList
                  ? GraphqlCollection
                  : T extends FieldKey.Chart
                    ? GraphqlCollection
                    : FieldValue<T, N>;

export interface FieldControlObjectConstructorProps<T extends FieldKey> {
    componentKey: T;
    dispatchValidation?: (screenId: string, elementId: string) => Promise<ValidationResult[] | undefined>;
    elementId: string;
    focus(screenId: string, elementId: string, row?: number, nestedField?: string): void;
    isFieldInFocus(screenId: string, elementId: string): boolean;
    getValue: (screenId: string, elementId: string) => FieldInternalValue<T>;
    getUiComponentProperties: (screenId: string, elementId: string) => FieldComponentProps<T>;
    isFieldDirty: (screenId: string, elementId: string) => boolean;
    setFieldDirty: (screenId: string, elementId: string) => void;
    setFieldClean: (screenId: string, elementId: string) => void;
    layout: LayoutContent<T>;
    parent?: ParentType<T>;
    insertBefore?: InsertBeforeType<T>;
    insertAfter?: InsertBeforeType<T>;
    refresh: (args: {
        screenId: string;
        elementId: string;
        keepPageInfo: boolean;
        keepModifications?: boolean;
    }) => Promise<FieldInternalValue<T>>;
    screenId: string;
    setValue: (screenId: string, elementId: string, newValue: FieldInternalValue<T> | null) => void;
    setUiComponentProperties: (screenId: string, elementId: string, newValue: FieldComponentProps<T>) => void;
}

export type FieldComponentProps<
    T extends FieldKey,
    CT extends ScreenBase = any,
    N extends ClientNode = any,
> = T extends FieldKey.Aggregate
    ? AggregateProperties<CT, N>
    : T extends FieldKey.Button
      ? ButtonProperties<CT>
      : T extends FieldKey.Card
        ? CardProperties<CT, N>
        : T extends FieldKey.Calendar
          ? CalendarProperties<CT, N>
          : T extends FieldKey.Chart
            ? ChartProperties<CT, N>
            : T extends FieldKey.Checkbox
              ? CheckboxProperties<CT>
              : T extends FieldKey.ContentTable
                ? ContentTableProperties<CT, N>
                : T extends FieldKey.Count
                  ? CountProperties<CT, N>
                  : T extends FieldKey.Date
                    ? DateProperties<CT>
                    : T extends FieldKey.Time
                      ? TimeProperties<CT>
                      : T extends FieldKey.Datetime
                        ? DatetimeProperties<CT>
                        : T extends FieldKey.DatetimeRange
                          ? DatetimeRangeProperties<CT>
                          : T extends FieldKey.DetailList
                            ? DetailListProperties<CT, N>
                            : T extends FieldKey.DynamicPod
                              ? DynamicPodProperties<CT, N>
                              : T extends FieldKey.DynamicSelect
                                ? DynamicSelectProperties<CT, N>
                                : T extends FieldKey.DropdownList
                                  ? DropdownListProperties<CT>
                                  : T extends FieldKey.FilterEditor
                                    ? FilterEditorProperties<CT>
                                    : T extends FieldKey.FilterSelect
                                      ? FilterSelectProperties<CT, N>
                                      : T extends FieldKey.File
                                        ? FileProperties<CT>
                                        : T extends FieldKey.FormDesigner
                                          ? FormDesignerProperties<CT>
                                          : T extends FieldKey.FileDeposit
                                            ? FileDepositProperties<CT>
                                            : T extends FieldKey.Icon
                                              ? IconProperties<CT>
                                              : T extends FieldKey.Image
                                                ? ImageProperties<CT>
                                                : T extends FieldKey.Label
                                                  ? LabelProperties<CT>
                                                  : T extends FieldKey.Link
                                                    ? LinkProperties<CT>
                                                    : T extends FieldKey.Message
                                                      ? MessageProperties<CT>
                                                      : T extends FieldKey.MultiDropdown
                                                        ? MultiDropdownProperties<CT>
                                                        : T extends FieldKey.MultiFileDeposit
                                                          ? MultiFileDepositProperties<CT>
                                                          : T extends FieldKey.MultiReference
                                                            ? MultiReferenceProperties<CT, N>
                                                            : T extends FieldKey.NestedGrid
                                                              ? NestedGridProperties<CT>
                                                              : T extends FieldKey.NodeBrowserTree
                                                                ? NodeBrowserTreeComponentProperties<CT>
                                                                : T extends FieldKey.Numeric
                                                                  ? NumericProperties<CT>
                                                                  : T extends FieldKey.Plugin
                                                                    ? PluginProperties<CT>
                                                                    : T extends FieldKey.Progress
                                                                      ? ProgressProperties<CT>
                                                                      : T extends FieldKey.Pod
                                                                        ? PodProperties<CT, N>
                                                                        : T extends FieldKey.Radio
                                                                          ? RadioProperties<CT>
                                                                          : T extends FieldKey.Reference
                                                                            ? ReferenceProperties<CT, N>
                                                                            : T extends FieldKey.RichText
                                                                              ? RichTextProperties<CT>
                                                                              : T extends FieldKey.PodCollection
                                                                                ? PodCollectionProperties<CT>
                                                                                : T extends FieldKey.Select
                                                                                  ? SelectProperties<CT, N>
                                                                                  : T extends FieldKey.SelectionCard
                                                                                    ? SelectionCardProperties<CT>
                                                                                    : T extends FieldKey.Separator
                                                                                      ? SeparatorProperties<CT>
                                                                                      : T extends FieldKey.StaticContent
                                                                                        ? StaticContentProperties<CT>
                                                                                        : T extends FieldKey.Switch
                                                                                          ? SwitchProperties<CT, N>
                                                                                          : T extends FieldKey.StepSequence
                                                                                            ? StepSequenceProperties<CT>
                                                                                            : T extends FieldKey.Table
                                                                                              ? TableProperties<CT, N>
                                                                                              : T extends FieldKey.TableSummary
                                                                                                ? TableSummaryProperties<
                                                                                                      CT,
                                                                                                      N
                                                                                                  >
                                                                                                : T extends FieldKey.Text
                                                                                                  ? TextProperties<CT>
                                                                                                  : T extends FieldKey.TextArea
                                                                                                    ? TextAreaProperties<
                                                                                                          CT,
                                                                                                          N
                                                                                                      >
                                                                                                    : T extends FieldKey.Toggle
                                                                                                      ? ToggleProperties<CT>
                                                                                                      : T extends FieldKey.Tree
                                                                                                        ? TreeProperties<
                                                                                                              CT,
                                                                                                              N
                                                                                                          >
                                                                                                        : T extends FieldKey.VisualProcess
                                                                                                          ? VisualProcessProperties<CT>
                                                                                                          : T extends FieldKey.VitalPod
                                                                                                            ? VitalPodProperties<
                                                                                                                  CT,
                                                                                                                  N
                                                                                                              >
                                                                                                            : T extends FieldKey.Workflow
                                                                                                              ? WorkflowProperties<CT>
                                                                                                              : T extends FieldKey.Preview
                                                                                                                ? PreviewProperties<CT>
                                                                                                                : ReadonlyFieldProperties;

export type ParentType<T extends ComponentKey> = (contextType: ScreenBase) => LayoutParent<T>;
export type InsertBeforeType<T extends ComponentKey> = T extends ActionKey
    ? null
    : T extends FieldKey
      ? (contextType: ScreenBase) => FieldControlObjectInstance<T>
      : T extends ContainerKey
        ? (contextType: ScreenBase) => ContainerControlObjectInstance<T>
        : never;

export type LayoutContent<T extends ComponentKey> = T extends ContainerKey.Page
    ? null
    : T extends ContainerKey.DetailPanel
      ? {
            detailPanelHeaderLayout: Partial<PageArticleItem>;
            detailPanelSectionsLayout: Partial<PageArticleItem>[];
        }
      : T extends ContainerKey
        ? Partial<PageArticleItem>
        : PageArticleItem;

export type PageHeaderFieldTypes =
    | FieldKey.Aggregate
    | FieldKey.Count
    | FieldKey.Date
    | FieldKey.DropdownList
    | FieldKey.Label
    | FieldKey.Numeric
    | FieldKey.Reference
    | FieldKey.Select
    | FieldKey.Text;

export type LayoutParent<T extends ComponentKey> = T extends FieldKey
    ? BlockControlObject | GridRowBlockControlObject
    : T extends PageHeaderFieldTypes
      ? TileControlObject | BlockControlObject
      : T extends FieldKey.PodCollection
        ? SectionControlObject | BlockControlObject
        : T extends FieldKey.MultiFileDeposit
          ? SectionControlObject | BlockControlObject
          : T extends FieldKey.Plugin
            ? SectionControlObject | BlockControlObject
            : T extends FieldKey.VitalPod
              ? SectionControlObject | BlockControlObject
              : T extends FieldKey.FormDesigner
                ? SectionControlObject | BlockControlObject
                : T extends FieldKey.Pod
                  ? SectionControlObject | BlockControlObject
                  : T extends FieldKey.Table
                    ? SectionControlObject | BlockControlObject
                    : T extends FieldKey.Tree
                      ? SectionControlObject | BlockControlObject
                      : T extends FieldKey.NodeBrowserTree
                        ? SectionControlObject | BlockControlObject
                        : T extends FieldKey.Message
                          ? SectionControlObject | BlockControlObject
                          : T extends FieldKey.Workflow
                            ? SectionControlObject | BlockControlObject
                            : T extends FieldKey.NestedGrid
                              ? SectionControlObject | BlockControlObject
                              : T extends FieldKey.TableSummary
                                ? SectionControlObject | BlockControlObject
                                : T extends ContainerKey.Block
                                  ? SectionControlObject
                                  : never;

export type PageCategory =
    | 'COUNT'
    | 'DELIVERY'
    | 'GLOSSARY'
    | 'INVENTORY'
    | 'ITEMS'
    | 'MANUFACTURING'
    | 'MOVE'
    | 'PURCHASING'
    | 'RECEIPT'
    | 'RESOURCES'
    | 'PICKING'
    | 'SALES'
    | 'SETTINGS'
    | 'SHOWCASE'
    | 'STOCK_CONTROL'
    | 'STOCK_INQUIRY'
    | 'OTHER'
    | 'TRANSFER';

export type AggregationMethod = 'min' | 'max' | 'avg' | 'sum' | 'distinctCount';

export interface CarbonStyleOverrides {
    root?: (() => React.CSSProperties) | React.CSSProperties;
    input?: (() => React.CSSProperties) | React.CSSProperties;
    label?: (() => React.CSSProperties) | React.CSSProperties;
}

export type PropertyRecursiveBind<T> = SingleKeyed<{
    [K in keyof T]: T[K] extends object
        ? T[K] extends ClientCollectionMatcher<any>
            ? never
            : T[K] extends EdgesMatcher<infer U>
              ? PropertyRecursiveBind<U>[]
              : T[K] extends BinaryStreamMatcher
                ? T[K] extends ClientNode
                    ? PropertyRecursiveBind<T[K]>
                    : boolean | PropertyRecursiveBind<T[K]>
                : PropertyRecursiveBind<T[K]>
        : boolean;
}>;

export type ClientCollectionRecursiveBind<T> = SingleKeyed<{
    [K in keyof T]: T[K] extends object
        ? T[K] extends ClientCollectionMatcher<any>
            ? boolean
            : T[K] extends ClientNode
              ? ClientCollectionRecursiveBind<T[K]>
              : never
        : never;
}>;

export type ReferenceRecursiveBind<T> = SingleKeyed<{
    [K in keyof T]: T[K] extends object
        ? T[K] extends ClientCollectionMatcher<any>
            ? never
            : T[K] extends EdgesMatcher<infer U>
              ? ReferenceRecursiveBind<U>[]
              : T[K] extends ClientNode
                ? boolean | ReferenceRecursiveBind<T[K]>
                : never
        : ReferenceRecursiveBind<T[K]>;
}>;

export type ReferenceRecursiveOrderBy<T> = {
    [K in keyof T]?: T[K] extends object
        ? T[K] extends ClientCollectionMatcher<any>
            ? never
            : T[K] extends EdgesMatcher<infer U>
              ? ReferenceRecursiveOrderBy<U>[]
              : ReferenceRecursiveOrderBy<T[K]>
        : 1 | -1;
};

export type NestedReferenceBind<NodeType extends ClientNode = any> = keyof OmitProperties<
    NodeType,
    Builtin | BinaryStream
>;

export type BindType<NodeType extends ClientNode = any, K = string> = NonNullable<keyof PickOwnProperties<NodeType, K>>;

export type OrderByType<T = any> = { [K in keyof T]?: 1 | -1 | undefined | OrderByType<T[K]> };

export enum TableDisplayMode {
    comfortable = 'comfortable',
    compact = 'compact',
}

export type Id<T> = {} & { [P in keyof T]: T[P] };

export type PartialCollectionValue<T> = ExtractEdgesPartial<T>;
export type PartialCollectionValueWithIds<T> = PartialCollectionValue<T> & { _id: string };

export type Events =
    | 'on360ViewSwitched'
    | 'onActive'
    | 'onAllDataLoaded'
    | 'onChange'
    | 'onClick'
    | 'onClose'
    | 'onCloseLookupDialog'
    | 'onDataLoaded'
    | 'onDayClick'
    | 'onDirtyStateUpdated'
    | 'onEmptyStateLinkClick'
    | 'onEventClick'
    | 'onFooterValueChanged'
    | 'onHeaderValueChanged'
    | 'onInactive'
    | 'onInputValueChange'
    | 'onLevelExpanded'
    | 'onLoad'
    | 'onOpen'
    | 'onOpenLookupDialog'
    | 'onReady'
    | 'onRecordAdded'
    | 'onRecordClick'
    | 'onRecordRemoved'
    | 'onRowActivated'
    | 'onRowAdded'
    | 'onRowClick'
    | 'onRowDeactivated'
    | 'onRowSelected'
    | 'onRowUnselected';

export const LESS_THAN = 'lessThan';
export const LESS_THAN_EQUAL = 'lessThanOrEqual';
export const RANGE = 'inRange';
export const MULTIPLE_RANGE = 'multipleRange';
export const GREATER_THAN = 'greaterThan';
export const GREATER_THAN_EQUAL = 'greaterThanOrEqual';

export const CONTAINS = 'contains';
export const STARTS_WITH = 'startsWith';
export const ENDS_WITH = 'endsWith';
export const MATCHES = 'matches';
export const EQUALS = 'equals';
export const NOT_EQUALS = 'notEqual';
export const MULTI_NOT_EQUALS = 'multiNotEqual';
export const SET = 'set';

export const RANGE_DIVIDER = '~';

export type StringFilter = (typeof stringFilter)[number];
export const rangeFilter = [
    LESS_THAN,
    LESS_THAN_EQUAL,
    EQUALS,
    NOT_EQUALS,
    GREATER_THAN,
    GREATER_THAN_EQUAL,
    RANGE,
] as const;
export const enumFilter = [SET, MULTI_NOT_EQUALS] as const;
export type RangeFilter = (typeof rangeFilter)[number];
export type EnumFilter = (typeof enumFilter)[number];
export type MultipleRangeFilter = typeof MULTIPLE_RANGE;
export type BooleanFilter = typeof MATCHES;
export type SetFilter = typeof SET;

export const filterableGraphqlTypes = Object.entries(filterGraphqlMapping).reduce<(keyof typeof GraphQLTypes)[]>(
    (acc, [k, v]) => {
        if (v !== undefined) {
            acc.push(k as keyof typeof GraphQLTypes);
        }
        return acc;
    },
    [],
);

export const aggregatableGraphqlTypes = Object.entries(aggregationsGraphqlMapping).reduce<
    (keyof typeof GraphQLTypes)[]
>((acc, [k, v]) => {
    if (v !== undefined) {
        acc.push(k as keyof typeof GraphQLTypes);
    }
    return acc;
}, []);

export interface FileDepositValue extends ClientNode, Omit<UploadedFile, '_id'> {
    lastModified?: string;
    _createUser: User<any>;
}

export interface AttachmentAssociation extends ClientNode, Omit<SharedAttachmentAssociation, '_id' | 'attachment'> {
    attachment: FileDepositValue;
}

export type GridRowActionType = Array<
    DropdownActionItem<any> | NestedCollectionItemAction<any> | NestedCollectionItemActionGroup<any>
>;

export type GridRowActionOrMenuSeparatorType = Array<
    | DropdownActionItemOrMenuSeparator<any>
    | NestedCollectionItemActionOrMenuSeparator<any>
    | NestedCollectionItemActionGroup<any>
>;
