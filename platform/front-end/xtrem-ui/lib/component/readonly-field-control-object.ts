/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import { get } from 'lodash';
import { ActionType } from '../redux/action-types';
import { getStore } from '../redux/store';
import { showToast } from '../service/toast-service';
import type { Page } from '../service/page';
import type { ScreenBase } from '../service/screen-base';
import { getScreenElement } from '../service/screen-base-definition';
import { findNextField } from '../utils/layout-utils';
import { convertDeepBindToPathNotNull } from '../utils/nested-field-utils';
import { getPageDefinitionFromState } from '../utils/state-utils';
import type { UiComponentProperties } from './abstract-ui-control-object';
import { AbstractUiControlObject } from './abstract-ui-control-object';
import type { PropertyValueType } from './field/reference/reference-types';
import { ControlObjectProperty } from './property-decorators/control-object-property-decorator';
import { FieldControlObjectResolvedProperty } from './property-decorators/control-object-resolved-property-decorator';
import type { FieldControlObjectConstructorProps, FieldInternalValue, FieldKey, FieldValue, FieldWidth } from './types';

export interface ReadonlyFieldProperties<CT extends ScreenBase = ScreenBase> extends UiComponentProperties<CT> {
    /** The GraphQL node that the field’s value is bound to. If not provided, the field name is used instead */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    bind?: PropertyValueType<ScreenBase extends ScreenBase<infer _A, infer U> ? U : ClientNode>;
    /** A text that will be displayed below the field */
    helperText?: string;
    /** Whether the field spans all the parent width or not. Defaults to false */
    isFullWidth?: boolean;
    /** The width of the block relative to the section where it is place.
     * Must be a number between 1 and 12, both included */
    width?: FieldWidth;
    /** Whether the field helper text is hidden or not. Defaults to false */
    isHelperTextHidden?: boolean;
    /** Whether the value is bound only to GraphQL mutations (isTransientInput = true) or not (isTransientInput = false). Defaults to false */
    isTransientInput?: boolean;
}

/**
 * Any element holding a value than can be placed inside a page and can be interacted with (i.e. retrieving
 * and/or setting field's ui properties values). The element value CANNOT be modified
 */
export abstract class ReadonlyFieldControlObject<
    CT extends ScreenBase,
    T extends FieldKey,
    S extends ReadonlyFieldProperties<CT>,
    N extends ClientNode = any,
> extends AbstractUiControlObject<CT, S> {
    public layout: FieldControlObjectConstructorProps<T>['layout'];

    public parent?: FieldControlObjectConstructorProps<T>['parent'];

    public insertBefore?: FieldControlObjectConstructorProps<T>['insertBefore'];

    public insertAfter?: FieldControlObjectConstructorProps<T>['insertAfter'];

    protected readonly _getValue: () => FieldInternalValue<T, N> | null;

    protected readonly _setValue: (newValue: FieldInternalValue<T, N> | null) => void;

    protected readonly _refresh: (args: {
        keepPageInfo: boolean;
        keepModifications?: boolean;
    }) => Promise<FieldInternalValue<T>>;

    // CC: This function parameters were never used at the time of development (X3-180847)
    protected readonly _focus: (row?: number, nestedField?: string) => void;

    static readonly defaultUiProperties: Partial<ReadonlyFieldProperties<any>> = {
        ...AbstractUiControlObject.defaultUiProperties,
        isFullWidth: false,
        isHelperTextHidden: false,
        isHidden: false,
    };

    constructor(properties: FieldControlObjectConstructorProps<T>) {
        super(
            properties.screenId,
            properties.elementId,
            properties.getUiComponentProperties as unknown as (screenId: string, bind: string) => S,
            properties.setUiComponentProperties as unknown as (screenId: string, bind: string, newValue: S) => void,
            properties.componentKey,
        );

        this._getValue = (): FieldInternalValue<T, N> | null => {
            return properties.getValue(this.screenId, this.elementId);
        };

        this._setValue = (newValue: FieldInternalValue<T, N> | null): void =>
            properties.setValue(this.screenId, this.elementId, newValue);

        this._refresh = ({ keepPageInfo, keepModifications = false }): Promise<FieldInternalValue<T>> => {
            return properties.refresh({
                screenId: this.screenId,
                elementId: this.elementId,
                keepPageInfo,
                keepModifications,
            });
        };

        this._focus = (row?: number, nestedField?: string): void => {
            // Allow time for rendering to finish in case it is called from the onLoad function.
            setTimeout(() => {
                properties.focus(this.screenId, this.elementId, row, nestedField);
            }, 150);
        };

        this.layout = properties.layout;
        this.parent = properties.parent;
        this.insertBefore = properties.insertBefore;
        this.insertAfter = properties.insertAfter;
    }

    /** Refreshes the field's value from the server. */
    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: false }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }

    protected _redraw(columnBind?: string): Promise<void> {
        return new Promise<void>(resolve =>
            setTimeout(() => {
                getStore().dispatch({
                    type: ActionType.RedrawComponent,
                    value: {
                        elementId: this.elementId,
                        screenId: this.screenId,
                        columnBind,
                    },
                });
                resolve();
            }),
        );
    }

    @ControlObjectProperty<ReadonlyFieldProperties<CT>, ReadonlyFieldControlObject<CT, T, S>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<ReadonlyFieldProperties<CT>, ReadonlyFieldControlObject<CT, T, S>>()
    /** Whether the field helper text is hidden or not. Defaults to false */
    isHelperTextHidden?: boolean;

    @ControlObjectProperty<ReadonlyFieldProperties<CT>, ReadonlyFieldControlObject<CT, T, S>>()
    /** Whether the value is bound only to GraphQL mutations (isTransientInput = true) or not (isTransientInput = false). Defaults to false */
    isTransientInput?: boolean;

    @FieldControlObjectResolvedProperty<ReadonlyFieldProperties<CT>, ReadonlyFieldControlObject<CT, T, S>>()
    /**
     * Whether the HTML element is editable (disabled = false) or not (disabled = true)
     *
     * The difference with readOnly is that disabled suggests that the field is not editable
     * for some validation reason (e.g. a button which can be clicked due to validation errors)
     */
    isDisabled?: boolean;

    /** Field's value */
    get value(): FieldValue<T, N> | null {
        // The following cast allows for field values different from what is exposed to the functional code.
        // In these cases custom getters & setters must be provided to marshal/unmarshal the value properly.
        // Refer to 'table-control-object' as an example.
        return this._getValue() as unknown as FieldValue<T>;
    }

    /** Field's value */
    set value(newValue: FieldValue<T, N> | null) {
        // The following cast allows for field values different from what is exposed to the functional code.
        // In these cases custom getters & setters must be provided to marshal/unmarshal the value properly.
        // Refer to 'table-control-object' as an example.
        this._setValue(newValue as unknown as FieldInternalValue<T, N>);
    }

    /**
     * Fetch default values from the server even if the field is dirty.
     * If the `skipSet` argument is set to true, the values are requested from the server but not applied to the page.
     */
    async fetchDefault(skipSet = false): Promise<FieldInternalValue<T, N>> {
        const screenDefinition = getPageDefinitionFromState(this.screenId);

        if (screenDefinition.type !== 'page') {
            throw new Error('Default values can only be fetched in pages.');
        }

        const page = getScreenElement(screenDefinition) as Page;
        const result = await page.$.fetchDefaults([this.elementId], skipSet);
        return get(result, convertDeepBindToPathNotNull(this.properties.bind || this.elementId));
    }

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    /** Returns the next field compared to the position of this field instance */
    getNextField(isFocusable = false): ReadonlyFieldControlObject<CT, any, any> | null {
        return findNextField(this.screenId, this.elementId, isFocusable) as ReadonlyFieldControlObject<
            CT,
            any,
            any
        > | null;
    }
}
