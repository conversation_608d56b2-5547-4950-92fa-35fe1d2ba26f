import type { Dict } from '@sage/xtrem-shared';
import { runAndDispatchFieldValidation } from '../service/dispatch-service';
import {
    focusField,
    isFieldDirty,
    isFieldInFocus,
    refreshField,
    setField<PERSON>lean,
    setFieldDirty,
} from '../service/field-state-service';
import type { DataTypeDetails, FormattedNodeDetails, FormattedNodeDetailsProperty } from '../service/metadata-types';
import {
    getFieldValue,
    getUiComponentProperties,
    setFieldValue,
    setUiComponentProperties,
} from '../service/transactions-service';
import { addTitleToProperties } from '../utils/data-type-utils';
import { convertDeepBindToPathNotNull } from '../utils/nested-field-utils';
import { findDeepPropertyType } from '../utils/node-utils';
import { schemaTypeNameFromNodeName } from '../utils/transformers';
import { AbstractDecorator } from './abstract-decorator';
import type { ReadonlyFieldProperties } from './readonly-field-control-object';
import type {
    ControlObjectConstructorProps,
    DecoratorProperties,
    DecoratorTarget,
    FieldDecoratorProps,
    FieldKey,
    MetadataProps,
} from './types';
import type { HasNode } from './field/traits';
import { GraphQLKind } from '../types';

export class AbstractFieldDecorator<T extends FieldKey> extends AbstractDecorator<T> {
    constructor(
        protected target: DecoratorTarget<T>,
        protected elementId: string,
        protected componentType: T,
        protected nodeTypes: Dict<FormattedNodeDetails>,
        protected dataTypes: Dict<DataTypeDetails>,
        protected _metadataProps: MetadataProps<T>,
    ) {
        super(target, elementId, _metadataProps, componentType, nodeTypes, dataTypes, {
            componentKey: componentType,
            getValue: getFieldValue,
            setValue: setFieldValue,
            refresh: refreshField,
            getUiComponentProperties,
            setUiComponentProperties,
            isFieldDirty,
            setFieldDirty,
            setFieldClean,
            isFieldInFocus,
            focus: focusField,
            dispatchValidation: runAndDispatchFieldValidation,
        } as ControlObjectConstructorProps<T>);
    }

    protected getComponentPropertiesFromDataType(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _dataType: DataTypeDetails,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _propertyDetails: FormattedNodeDetailsProperty,
    ): Partial<FieldDecoratorProps<T>> {
        return {};
    }

    private computeDefaultPropertiesForNonTransientField(rootNode: string): Partial<DecoratorProperties<T, any, any>> {
        let dataTypeProvidedProperties: Partial<DecoratorProperties<T, any, any>> = {};
        const fieldDecoratorProperties = this._metadataProps.properties as ReadonlyFieldProperties;
        const bind = fieldDecoratorProperties?.bind
            ? convertDeepBindToPathNotNull(fieldDecoratorProperties?.bind)
            : this.elementId;
        const contextNode = schemaTypeNameFromNodeName(rootNode);
        const propertyDetails = findDeepPropertyType(contextNode, bind, this.nodeTypes);
        if (propertyDetails) {
            const nodeDetails = findDeepPropertyType(contextNode, bind, this.nodeTypes);
            if (nodeDetails) {
                addTitleToProperties({
                    propertyDetails,
                    properties: dataTypeProvidedProperties as any,
                });
                if (nodeDetails.dataType && this.dataTypes[nodeDetails.dataType]) {
                    const dataType = this.dataTypes[nodeDetails.dataType];
                    dataTypeProvidedProperties = {
                        ...dataTypeProvidedProperties,
                        ...this.getComponentPropertiesFromDataType(dataType, nodeDetails),
                    };
                }
            }
        }

        return dataTypeProvidedProperties;
    }

    /**
     * Compute default decorator properties with fields that have a `node` decorator property
     * @param fieldNode
     * @returns
     */
    private computeDefaultPropertiesForTransientField(fieldNode: string): Partial<DecoratorProperties<T, any, any>> {
        const schemaTypeName = schemaTypeNameFromNodeName(fieldNode);
        const nodeDetails = this.nodeTypes[schemaTypeName];

        if (!nodeDetails?.defaultDataType) {
            return {};
        }

        const dataType = this.dataTypes[nodeDetails.defaultDataType];
        if (!dataType) {
            return {};
        }

        const propertyDetails: FormattedNodeDetailsProperty = {
            targetNode: fieldNode,
            title: nodeDetails?.title,
            kind: GraphQLKind.Object,
            type: schemaTypeName,
            isMutable: false,
            isCustom: false,
        };

        const dataTypeProvidedProperties: Partial<DecoratorProperties<T, any, any>> = {};

        addTitleToProperties({
            propertyDetails,
            properties: dataTypeProvidedProperties as any,
        });

        return {
            ...dataTypeProvidedProperties,
            ...this.getComponentPropertiesFromDataType(dataType, propertyDetails),
        };
    }

    protected setUiComponentProperties = (): this => {
        const actualPageMetadata = this.getPageMetadata();
        const fieldDecoratorProperties = this._metadataProps.properties as ReadonlyFieldProperties;
        const isTransient = !!fieldDecoratorProperties.isTransient;

        const rootNode = actualPageMetadata.rootNode;
        const fieldNode = (fieldDecoratorProperties as HasNode<any>).node;
        if (!isTransient && !actualPageMetadata.isTransient && rootNode) {
            /**
             * If the page and the field is bound (and not transient), we can compute the default properties based on the
             * context node's property data type details
             *  */
            return super.setUiComponentProperties(this.computeDefaultPropertiesForNonTransientField(rootNode));
        }

        if (fieldNode) {
            /**
             * If the field or the page is transient, but it has a `node` decorator property, try computing defaults from that
             */
            return super.setUiComponentProperties(this.computeDefaultPropertiesForTransientField(String(fieldNode)));
        }

        return super.setUiComponentProperties();
    };
}
