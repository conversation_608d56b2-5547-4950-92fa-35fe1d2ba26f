import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../service/page-extension';
import type { ScreenBase } from '../service/screen-base';
import type { ScreenExtension } from '../types/index';
import {
    AggregateControlObject,
    CheckboxControlObject,
    CountControlObject,
    DateControlObject,
    DropdownListControlObject,
    FilterSelectControlObject,
    IconControlObject,
    ImageControlObject,
    LabelControlObject,
    LinkControlObject,
    MultiDropdownControlObject,
    MultiReferenceControlObject,
    NumericControlObject,
    ProgressControlObject,
    ReferenceControlObject,
    SelectControlObject,
    SwitchControlObject,
    TextControlObject,
} from './control-objects';
import type { NestedFieldTypes } from './nested-fields';
import { getNestedFieldDefaultProperties } from './nested-fields';
import type {
    NestedAggregateProperties,
    NestedCheckboxProperties,
    NestedCountProperties,
    NestedDateProperties,
    NestedDropdownListProperties,
    NestedFilterSelectProperties,
    NestedIconProperties,
    NestedImageProperties,
    NestedLabelProperties,
    NestedLinkProperties,
    NestedMultiDropdownProperties,
    NestedMultiReferenceProperties,
    NestedNumericProperties,
    NestedProgressProperties,
    NestedReferenceProperties,
    NestedSelectProperties,
    NestedSwitchProperties,
    NestedTextAreaProperties,
    NestedTextProperties,
} from './nested-fields-properties';
import { FieldKey } from './types';

export interface NestedExtensionInsertBeforeProperties {
    // Determines before which column this new field should be inserted in.
    insertBefore?: string;
    // Determines before which column this new field should be inserted in.
    insertAfter?: string;
}

export interface NestedAggregateExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedAggregateProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedCheckboxExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedCheckboxProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedCountExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedCountProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedDateExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedDateProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedDropdownListExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedDropdownListProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedFilterSelectExtensionProperties<
    CT extends ScreenBase,
    C extends ClientNode = any,
    R extends ClientNode = any,
> extends NestedFilterSelectProperties<CT, C, R>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedIconExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedIconProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedImageExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedImageProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedLabelExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedLabelProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedLinkExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedLinkProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedNumericExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedNumericProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}
export interface NestedMultiDropdownExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedMultiDropdownProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedMultiReferenceExtensionProperties<
    CT extends ScreenBase,
    C extends ClientNode = any,
    R extends ClientNode = any,
> extends NestedMultiReferenceProperties<CT, C, R>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedProgressExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedProgressProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedReferenceExtensionProperties<
    CT extends ScreenBase,
    C extends ClientNode = any,
    R extends ClientNode = any,
> extends NestedReferenceProperties<CT, C, R>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedSelectExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedSelectProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedSwitchExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedSwitchProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedTextExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedTextProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedTextAreaExtensionProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends NestedTextAreaProperties<CT, C>,
        NestedExtensionInsertBeforeProperties {}

export interface NestedFieldsExtensionPropertiesMap<
    CT extends ScreenBase,
    C extends ClientNode = any,
    R extends ClientNode = any,
> {
    [FieldKey.Aggregate]: NestedAggregateExtensionProperties<CT, C>;
    [FieldKey.Checkbox]: NestedCheckboxExtensionProperties<CT, C>;
    [FieldKey.Count]: NestedCountExtensionProperties<CT, C>;
    [FieldKey.Date]: NestedDateExtensionProperties<CT, C>;
    [FieldKey.DropdownList]: NestedDropdownListExtensionProperties<CT, C>;
    [FieldKey.FilterSelect]: NestedFilterSelectExtensionProperties<CT, C, R>;
    [FieldKey.Icon]: NestedIconExtensionProperties<CT, C>;
    [FieldKey.Image]: NestedImageExtensionProperties<CT, C>;
    [FieldKey.Label]: NestedLabelExtensionProperties<CT, C>;
    [FieldKey.Link]: NestedLinkExtensionProperties<CT, C>;
    [FieldKey.Numeric]: NestedNumericExtensionProperties<CT, C>;
    [FieldKey.MultiDropdown]: NestedMultiDropdownExtensionProperties<CT, C>;
    [FieldKey.MultiReference]: NestedMultiReferenceExtensionProperties<CT, C, R>;
    [FieldKey.Numeric]: NestedNumericExtensionProperties<CT, C>;
    [FieldKey.Progress]: NestedProgressExtensionProperties<CT, C>;
    [FieldKey.Reference]: NestedReferenceExtensionProperties<CT, C, R>;
    [FieldKey.Select]: NestedSelectExtensionProperties<CT, C>;
    [FieldKey.Switch]: NestedSwitchExtensionProperties<CT, C>;
    [FieldKey.Text]: NestedTextExtensionProperties<CT, C>;
    [FieldKey.TextArea]: NestedTextAreaExtensionProperties<CT, C>;
}

export type NestedFieldsExtensionProperties<
    T extends NestedFieldTypes,
    CT extends ScreenBase = any,
    C extends ClientNode = any,
    R extends ClientNode = any,
> = {
    [K in keyof NestedFieldsExtensionPropertiesMap<CT, C, R>]: K extends T
        ? NestedFieldsExtensionPropertiesMap<CT, C, R>[K]
        : never;
}[keyof {
    [K in keyof NestedFieldsExtensionPropertiesMap<CT, C, R>]: K extends T
        ? NestedFieldsExtensionPropertiesMap<CT, C, R>[K]
        : never;
}];

export interface NestedExtensionField<
    CT extends ScreenExtension<CT>,
    T extends NestedFieldTypes,
    C extends ClientNode = any,
    R extends ClientNode = any,
> {
    defaultUiProperties: NestedFieldsExtensionProperties<T, Extend<CT>, C, R>;
    properties: NestedFieldsExtensionProperties<T, Extend<CT>, C, R>;
    type: T;
}
export const aggregate = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedAggregateExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Aggregate, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Aggregate, CT, NodeType>(
        AggregateControlObject.defaultUiProperties,
        FieldKey.Aggregate,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Aggregate },
    type: FieldKey.Aggregate,
});

export const checkbox = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedCheckboxExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Checkbox, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Checkbox, CT, NodeType>(
        CheckboxControlObject.defaultUiProperties,
        FieldKey.Checkbox,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Checkbox },
    type: FieldKey.Checkbox,
});

export const count = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedCountExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Count, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Count, CT, NodeType>(
        CountControlObject.defaultUiProperties,
        FieldKey.Count,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Count },
    type: FieldKey.Count,
});

export const date = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedDateExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Date, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Date, CT, NodeType>(
        DateControlObject.defaultUiProperties,
        FieldKey.Date,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Date },
    type: FieldKey.Date,
});

export const dropdownList = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedDropdownListExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.DropdownList, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.DropdownList, CT, NodeType>(
        DropdownListControlObject.defaultUiProperties,
        FieldKey.DropdownList,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.DropdownList },
    type: FieldKey.DropdownList,
});

export const filterSelect = <
    CT extends ScreenExtension<CT> = any,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
>(
    properties: Omit<NestedFilterSelectExtensionProperties<Extend<CT>, NodeType, R>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.FilterSelect, NodeType, R> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.FilterSelect, CT, NodeType, R>(
        FilterSelectControlObject.defaultUiProperties,
        FieldKey.FilterSelect,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.FilterSelect },
    type: FieldKey.FilterSelect,
});

export const icon = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedIconExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Icon, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Icon, CT, NodeType>(
        IconControlObject.defaultUiProperties,
        FieldKey.Icon,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Icon },
    type: FieldKey.Icon,
});

export const image = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedImageExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Image, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Image, CT, NodeType>(
        ImageControlObject.defaultUiProperties,
        FieldKey.Image,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Image },
    type: FieldKey.Image,
});

export const label = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedLabelExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Label, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Label, CT, NodeType>(
        LabelControlObject.defaultUiProperties,
        FieldKey.Label,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Label },
    type: FieldKey.Label,
});

export const link = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedLinkProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Link, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Link, CT, NodeType>(
        LinkControlObject.defaultUiProperties,
        FieldKey.Link,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Link },
    type: FieldKey.Link,
});

export const numeric = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedNumericExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Numeric, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Numeric, CT, NodeType>(
        NumericControlObject.defaultUiProperties,
        FieldKey.Numeric,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Numeric },
    type: FieldKey.Numeric,
});

export const multiDropdown = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedMultiDropdownExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.MultiDropdown, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.MultiDropdown, CT, NodeType>(
        MultiDropdownControlObject.defaultUiProperties,
        FieldKey.MultiDropdown,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.MultiDropdown },
    type: FieldKey.MultiDropdown,
});

export const multiReference = <
    CT extends ScreenExtension<CT> = any,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
>(
    properties: Omit<NestedMultiReferenceExtensionProperties<Extend<CT>, NodeType, R>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.MultiReference, NodeType, R> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.MultiReference, CT, NodeType, R>(
        MultiReferenceControlObject.defaultUiProperties,
        FieldKey.MultiReference,
    ) as any,
    properties: { ...properties, _controlObjectType: FieldKey.MultiReference },
    type: FieldKey.MultiReference,
});

export const progress = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedProgressExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Progress, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Progress, CT, NodeType>(
        ProgressControlObject.defaultUiProperties,
        FieldKey.Progress,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Progress },
    type: FieldKey.Progress,
});

export const reference = <
    CT extends ScreenExtension<CT> = any,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
>(
    properties: Omit<NestedReferenceExtensionProperties<Extend<CT>, NodeType, R>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Reference, NodeType, R> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Reference, CT, NodeType, R>(
        ReferenceControlObject.defaultUiProperties,
        FieldKey.Reference,
    ) as any,
    properties: { ...properties, _controlObjectType: FieldKey.Reference },
    type: FieldKey.Reference,
});

export const select = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedSelectExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Select, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Select, CT, NodeType>(
        SelectControlObject.defaultUiProperties,
        FieldKey.Select,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Select },
    type: FieldKey.Select,
});

const nestedSwitch = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedSwitchExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Switch, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Switch, CT, NodeType>(
        SwitchControlObject.defaultUiProperties,
        FieldKey.Switch,
    ) as any,
    properties: { ...properties, _controlObjectType: FieldKey.Switch },
    type: FieldKey.Switch,
});

export { nestedSwitch as switch };

export const text = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedTextExtensionProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedExtensionField<CT, FieldKey.Text, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Text, CT, NodeType>(
        TextControlObject.defaultUiProperties,
        FieldKey.Text,
    ),
    properties: { ...properties, _controlObjectType: FieldKey.Text },
    type: FieldKey.Text,
});
