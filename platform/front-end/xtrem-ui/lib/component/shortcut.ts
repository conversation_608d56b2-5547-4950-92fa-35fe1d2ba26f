import * as React from 'react';
import * as ReactDom from 'react-dom';
import type { Key } from '../service/shortcut-service';
import { subscribe, unsubscribe } from '../service/shortcut-service';

export interface ShortcutProps {
    combination: Key[];
    children: React.ReactNode;
}

/* TODO: This component should be changed to no longer rely on ReactDom.findDomNode(...) as
 *       this may cause issues if we should ever migrate to function components.
 *
 *       From the react documentation:
 *       -----------------------------
 *       findDOMNode only works on mounted components (that is, components that have been placed
 *       in the DOM). If you try to call this on a component that has not been mounted yet (like
 *       calling findDOMNode() in render() on a component that has yet to be created) an exception
 *       will be thrown.
 *
 *       findDOMNode cannot be used on function components.
 */
export class Shortcut extends React.Component<ShortcutProps> {
    private keyId: null | number = null;

    componentDidMount(): void {
        // eslint-disable-next-line react/no-find-dom-node
        const element = ReactDom.findDOMNode(this);
        const callback = (): void => {
            if (element && window.document) {
                const event = window.document.createEvent('MouseEvent');
                event.initEvent('click', true, true);
                element.dispatchEvent(event);
            }
        };
        this.keyId = subscribe(this.props.combination, callback);
    }

    componentWillUnmount(): void {
        unsubscribe(this.keyId!);
    }

    render(): React.ReactNode {
        return this.props.children;
    }
}
