import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../service/page-extension';
import type { ScreenBase } from '../service/screen-base';
import type { ScreenExtension } from '../types/index';
import type {
    ChangeableNestedOverrideDecoratorProperties,
    ClickableNestedOverrideDecoratorProperties,
    OverrideNestedDecoratorProperties,
} from '../utils/decorator-utils';
import {
    AggregateControlObject,
    CheckboxControlObject,
    CountControlObject,
    DateControlObject,
    DropdownListControlObject,
    FilterSelectControlObject,
    IconControlObject,
    ImageControlObject,
    LabelControlObject,
    LinkControlObject,
    MultiDropdownControlObject,
    MultiReferenceControlObject,
    NumericControlObject,
    ProgressControlObject,
    ReferenceControlObject,
    SelectControlObject,
    SwitchControlObject,
    TextControlObject,
} from './control-objects';
import type { NestedFieldTypes } from './nested-fields';
import { getNestedFieldDefaultProperties } from './nested-fields';
import type {
    NestedAggregateProperties,
    NestedCheckboxProperties,
    NestedCountProperties,
    NestedDateProperties,
    NestedDropdownListProperties,
    NestedFilterSelectProperties,
    NestedIconProperties,
    NestedImageProperties,
    NestedLabelProperties,
    NestedLinkProperties,
    NestedMultiDropdownProperties,
    NestedMultiReferenceProperties,
    NestedNumericProperties,
    NestedProgressProperties,
    NestedReferenceProperties,
    NestedSelectProperties,
    NestedSwitchProperties,
    NestedTextAreaProperties,
    NestedTextProperties,
} from './nested-fields-properties';
import { FieldKey } from './types';

export interface NestedOverrideInsertBeforeProperties {
    // Determines before which column this new field should be inserted in.
    insertBefore?: string;
    // Determines before which column this new field should be inserted in.
    insertAfter?: string;
}

export interface NestedAggregateOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedAggregateProperties<CT, C>>,
        ClickableNestedOverrideDecoratorProperties<NestedAggregateProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedCheckboxOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedCheckboxProperties<CT, C>>,
        ChangeableNestedOverrideDecoratorProperties<NestedCheckboxProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedCountOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedCountProperties<CT, C>>,
        ClickableNestedOverrideDecoratorProperties<NestedCountProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedDateOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedDateProperties<CT, C>>,
        ChangeableNestedOverrideDecoratorProperties<NestedDateProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedDropdownListOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedDropdownListProperties<CT, C>>,
        ChangeableNestedOverrideDecoratorProperties<NestedDropdownListProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedFilterSelectOverrideProperties<
    CT extends ScreenBase,
    C extends ClientNode = any,
    R extends ClientNode = any,
> extends OverrideNestedDecoratorProperties<NestedFilterSelectProperties<CT, C, R>>,
        ChangeableNestedOverrideDecoratorProperties<NestedFilterSelectProperties<CT, C, R>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedIconOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedIconProperties<CT, C>>,
        ClickableNestedOverrideDecoratorProperties<NestedIconProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedImageOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedImageProperties<CT, C>>,
        ClickableNestedOverrideDecoratorProperties<NestedImageProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedLabelOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedLabelProperties<CT, C>>,
        ClickableNestedOverrideDecoratorProperties<NestedLabelProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedLinkOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedLinkProperties<CT, C>>,
        ClickableNestedOverrideDecoratorProperties<NestedLinkProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedNumericOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedNumericProperties<CT, C>>,
        ChangeableNestedOverrideDecoratorProperties<NestedNumericProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}
export interface NestedMultiDropdownOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedMultiDropdownProperties<CT, C>>,
        ChangeableNestedOverrideDecoratorProperties<NestedMultiDropdownProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedMultiReferenceOverrideProperties<
    CT extends ScreenBase,
    C extends ClientNode = any,
    R extends ClientNode = any,
> extends OverrideNestedDecoratorProperties<NestedMultiReferenceProperties<CT, C, R>>,
        ChangeableNestedOverrideDecoratorProperties<NestedMultiReferenceProperties<CT, C, R>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedProgressOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedProgressProperties<CT, C>>,
        ClickableNestedOverrideDecoratorProperties<NestedProgressProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}
export interface NestedReferenceOverrideProperties<
    CT extends ScreenBase,
    C extends ClientNode = any,
    R extends ClientNode = any,
> extends OverrideNestedDecoratorProperties<NestedReferenceProperties<CT, C, R>>,
        ChangeableNestedOverrideDecoratorProperties<NestedReferenceProperties<CT, C, R>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedSelectOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedSelectProperties<CT, C>>,
        ChangeableNestedOverrideDecoratorProperties<NestedSelectProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedSwitchOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedSwitchProperties<CT, C>>,
        ChangeableNestedOverrideDecoratorProperties<NestedSwitchProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedTextOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedTextProperties<CT, C>>,
        ChangeableNestedOverrideDecoratorProperties<NestedTextProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export interface NestedTextAreaOverrideProperties<CT extends ScreenBase, C extends ClientNode = any>
    extends OverrideNestedDecoratorProperties<NestedTextAreaProperties<CT, C>>,
        ChangeableNestedOverrideDecoratorProperties<NestedTextAreaProperties<CT, C>, CT, C>,
        NestedOverrideInsertBeforeProperties {}

export type NestedFieldsOverrideProperties<
    CT extends ScreenBase,
    C extends ClientNode = any,
    R extends ClientNode = any,
> =
    | NestedAggregateOverrideProperties<CT, C>
    | NestedCheckboxOverrideProperties<CT, C>
    | NestedCountOverrideProperties<CT, C>
    | NestedDateOverrideProperties<CT, C>
    | NestedDropdownListOverrideProperties<CT, C>
    | NestedFilterSelectOverrideProperties<CT, C, R>
    | NestedIconOverrideProperties<CT, C>
    | NestedImageOverrideProperties<CT, C>
    | NestedLabelOverrideProperties<CT, C>
    | NestedLinkOverrideProperties<CT, C>
    | NestedNumericOverrideProperties<CT, C>
    | NestedMultiDropdownOverrideProperties<CT, C>
    | NestedMultiReferenceOverrideProperties<CT, C, R>
    | NestedProgressOverrideProperties<CT, C>
    | NestedReferenceOverrideProperties<CT, C, R>
    | NestedSelectOverrideProperties<CT, C>
    | NestedSwitchOverrideProperties<CT, C>
    | NestedTextOverrideProperties<CT, C>
    | NestedTextAreaOverrideProperties<CT, C>;

export interface NestedOverrideField<
    CT extends ScreenExtension<CT>,
    T extends NestedFieldTypes,
    C extends ClientNode = any,
    R extends ClientNode = any,
> {
    defaultUiProperties: Partial<NestedFieldsOverrideProperties<Extend<CT>, C, R>>;
    properties: NestedFieldsOverrideProperties<Extend<CT>, C, R>;
    type: T;
}

export const aggregate = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedAggregateOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Aggregate, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Aggregate, CT, NodeType>(
        AggregateControlObject.defaultUiProperties,
        FieldKey.Aggregate,
    ),
    properties,
    type: FieldKey.Aggregate,
});

export const checkbox = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedCheckboxOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Checkbox, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Checkbox, CT, NodeType>(
        CheckboxControlObject.defaultUiProperties,
        FieldKey.Checkbox,
    ),
    properties,
    type: FieldKey.Checkbox,
});

export const count = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedCountOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Count, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Count, CT, NodeType>(
        CountControlObject.defaultUiProperties,
        FieldKey.Count,
    ),
    properties,
    type: FieldKey.Count,
});

export const date = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedDateOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Date, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Date, CT, NodeType>(
        DateControlObject.defaultUiProperties,
        FieldKey.Date,
    ),
    properties,
    type: FieldKey.Date,
});

export const dropdown = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedDropdownListOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.DropdownList, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.DropdownList, CT, NodeType>(
        DropdownListControlObject.defaultUiProperties,
        FieldKey.DropdownList,
    ),
    properties,
    type: FieldKey.DropdownList,
});

export const filterSelect = <
    CT extends ScreenExtension<CT> = any,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
>(
    properties: Omit<NestedFilterSelectOverrideProperties<Extend<CT>, NodeType, R>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.FilterSelect, NodeType, R> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.FilterSelect, CT, NodeType, R>(
        FilterSelectControlObject.defaultUiProperties,
        FieldKey.FilterSelect,
    ),
    properties,
    type: FieldKey.FilterSelect,
});

export const icon = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedIconOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Icon, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Icon, CT, NodeType>(
        IconControlObject.defaultUiProperties,
        FieldKey.Icon,
    ),
    properties,
    type: FieldKey.Icon,
});

export const image = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedImageOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Image, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Image, CT, NodeType>(
        ImageControlObject.defaultUiProperties,
        FieldKey.Image,
    ),
    properties,
    type: FieldKey.Image,
});

export const label = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedLabelOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Label, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Label, CT, NodeType>(
        LabelControlObject.defaultUiProperties,
        FieldKey.Label,
    ),
    properties,
    type: FieldKey.Label,
});

export const link = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedLinkProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Link, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Link, CT, NodeType>(
        LinkControlObject.defaultUiProperties,
        FieldKey.Link,
    ),
    properties,
    type: FieldKey.Link,
});

export const numeric = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedNumericOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Numeric, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Numeric, CT, NodeType>(
        NumericControlObject.defaultUiProperties,
        FieldKey.Numeric,
    ),
    properties,
    type: FieldKey.Numeric,
});

export const multiDropdown = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedMultiDropdownOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.MultiDropdown, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.MultiDropdown, CT, NodeType>(
        MultiDropdownControlObject.defaultUiProperties,
        FieldKey.MultiDropdown,
    ),
    properties,
    type: FieldKey.MultiDropdown,
});

export const multiReference = <
    CT extends ScreenExtension<CT> = any,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
>(
    properties: Omit<NestedMultiReferenceOverrideProperties<Extend<CT>, NodeType, R>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.MultiReference, NodeType, R> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.MultiReference, CT, NodeType, R>(
        MultiReferenceControlObject.defaultUiProperties,
        FieldKey.MultiReference,
    ),
    properties,
    type: FieldKey.MultiReference,
});

export const progress = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedProgressOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Progress, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Progress, CT, NodeType>(
        ProgressControlObject.defaultUiProperties,
        FieldKey.Progress,
    ),
    properties,
    type: FieldKey.Progress,
});

export const reference = <
    CT extends ScreenExtension<CT> = any,
    NodeType extends ClientNode = any,
    R extends ClientNode = any,
>(
    properties: Omit<NestedReferenceOverrideProperties<Extend<CT>, NodeType, R>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Reference, NodeType, R> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Reference, CT, NodeType, R>(
        ReferenceControlObject.defaultUiProperties,
        FieldKey.Reference,
    ),
    properties,
    type: FieldKey.Reference,
});

export const select = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedSelectOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Select, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Select, CT, NodeType>(
        SelectControlObject.defaultUiProperties,
        FieldKey.Select,
    ),
    properties,
    type: FieldKey.Select,
});

const nestedSwitch = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedCheckboxOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Switch, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Switch, CT, NodeType>(
        SwitchControlObject.defaultUiProperties,
        FieldKey.Switch,
    ),
    properties,
    type: FieldKey.Switch,
});

export { nestedSwitch as switch };

export const text = <CT extends ScreenExtension<CT> = any, NodeType extends ClientNode = any>(
    properties: Omit<NestedTextOverrideProperties<Extend<CT>, NodeType>, '_controlObjectType'>,
): NestedOverrideField<CT, FieldKey.Text, NodeType> => ({
    defaultUiProperties: getNestedFieldDefaultProperties<FieldKey.Text, CT, NodeType>(
        TextControlObject.defaultUiProperties,
        FieldKey.Text,
    ),
    properties,
    type: FieldKey.Text,
});
