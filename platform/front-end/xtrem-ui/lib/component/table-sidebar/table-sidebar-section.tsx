import { connect } from 'react-redux';
import type * as xtremRedux from '../../redux';
import React from 'react';
import { ConnectedTableSidebarBlock } from './table-sidebar-block';
import type { CollectionValue } from '../../service/collection-data-service';
import { resolveByValue } from '../../utils/resolve-value-utils';
import { objectKeys } from '@sage/xtrem-shared';
import type { SidebarBlockDefinition, SidebarSectionDefinition } from './table-sidebar-types';
import type { Dict } from '@sage/xtrem-shared';
import type { NestedField } from '../nested-fields';

export interface TableSidebarSectionExternalProps {
    section: SidebarSectionDefinition;
    screenId: string;
    elementId: string;
    recordId: string;
    level?: number;
    columns: Array<NestedField<any, any>>;
}

export interface TableSidebarSectionProps extends TableSidebarSectionExternalProps {
    blocks: Dict<SidebarBlockDefinition>;
}

export function TableSidebarSection({
    blocks,
    columns,
    elementId,
    level,
    recordId,
    screenId,
}: TableSidebarSectionProps): React.ReactElement {
    return (
        <div className="e-table-sidebar-section">
            {Object.keys(blocks).map(blockKey => (
                <ConnectedTableSidebarBlock
                    block={blocks[blockKey]}
                    columns={columns}
                    elementId={elementId}
                    key={blockKey}
                    level={level}
                    recordId={recordId}
                    screenId={screenId}
                />
            ))}
        </div>
    );
}

export const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: TableSidebarSectionExternalProps,
): TableSidebarSectionProps => {
    const { screenId, elementId, level, recordId } = props;

    const screenDefinition = state.screenDefinitions[screenId];
    const value: CollectionValue = screenDefinition.values[elementId];
    const recordValue = value.getRawRecord({
        id: recordId,
        cleanMetadata: true,
        isUncommitted: true,
        level,
    });

    const blocks = objectKeys(props.section.blocks)
        .filter(
            b =>
                !resolveByValue({
                    propertyValue: props.section.blocks[b].isHidden,
                    rowValue: recordValue,
                    fieldValue: recordValue?._id,
                    screenId,
                    skipHexFormat: true,
                }),
        )
        .reduce((acc: Dict<SidebarBlockDefinition>, blockId: string) => {
            acc[blockId] = props.section.blocks[blockId];
            return acc;
        }, {} as Dict<SidebarBlockDefinition>);

    return {
        ...props,
        blocks,
    };
};

export const ConnectedTableSidebarSection = connect(mapStateToProps)(TableSidebarSection);
