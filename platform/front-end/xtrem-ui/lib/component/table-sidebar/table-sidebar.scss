.e-table-sidebar {
    height: calc(100vh - 132px);
    overflow-y: hidden;
    margin-left: calc(-1 * var(--spacing400));
    margin-right: calc(-1 * var(--spacing400));
    margin-top: calc(-1 * var(--spacing300));

    @include extra_small {
        height: calc(100vh - 116px);
        margin: 0;
    }
}

.e-sidebar-header {
    display: flex;
    align-items: baseline;
    margin-bottom: 0;
    flex: 1;
    margin-top: -4px;
    width: calc(100% - 40px);

    @include extra_small {
        padding-right: 0;
        width: calc(100% - 20px);

        .e-action-popover-mobile .e-action-popover-mobile-button {
            line-height: 30px;
        }
    }

    .e-sidebar-title {
        flex: 1;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        margin-left: 12px;

        @include extra_small {
            -webkit-line-clamp: 1;
        }
    }

    .e-header-navigation-arrow-container {
        @include extra_small {
            margin-left: 12px;
        }

        button {
            margin-right: 0;
        }
    }
}

.e-table-sidebar-block {
    margin-top: 24px;
}

.e-table-sidebar-footer {
    display: flex;
    justify-content: flex-end;
    height: 72px;
    background: var(--colorsYang100);
    box-shadow: inset 0px 1px 0px var(--colorsActionMinor200);
    padding: 16px;
    box-sizing: border-box;
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: right;

    @include extra_small {
        margin-left: 0;
        margin-right: 0;
    }
}

.e-table-sidebar-body {
    height: calc(100vh - 128px);
    display: flex;
    flex-direction: column;

    @include extra_small {
        height: calc(100vh - 118px)
    }

    .e-xtrem-tabs .e-xtrem-tab-container {
        padding-left: 12px;
    }

    .e-card {
        border: none;
        background-color: var(--colorsUtilityMajor025);
        padding: 0 24px 0 8px;
        box-sizing: border-box;
    }
}

.e-table-sidebar-section {
    background-color: var(--colorsYang100);
    flex: 1;
    overflow-y: auto;
    padding-bottom: 72px;
    padding-left: var(--spacing300);
    padding-right: var(--spacing300);
}

.e-sidebar-dropdown-action-container {
    margin-left: 14px;

    @include extra_small {
        margin-left: 8px;
    }
}