PATH: XTREEM/Table+Field/Table+Sidebar

## Introduction

The table sidebar is a dialog-like extension to the standard table field. It enables users to edit the table row data on a sidebar dialog. Unlike the grid row block, the changes are not automatically applied to the table value, the user has to proactively confirm or discard the changes.

The changes remain pending until the user confirms the edit and are not applied directly to the rest of the screen. Any application code updates that target the currently open record are applied to the working copy, so these changes are only applied to the page when the user confirms the changes on the sidebar. Until then, the changes are only visible on the sidebar. If the user discards the changes on the sidebar, these application code initiated record changes are discarded too.

## Example:

```ts
@ui.decorators.tableField<TableField>({
    ...
    columns: [
        ui.nestedFields.text({
            bind: 'product',
            canFilter: true,
            title: 'Product',
        }),
        ui.nestedFields.numeric({
            bind: 'qty',
            canFilter: true,
            title: 'Quantity',
            scale: 0,
        }),
        ui.nestedFields.text({
            bind: { supplier: { name: true } },
            title: 'Supplier Name',
        }),
    ],
    ...
    sidebar: {
        headerDropdownActions: [
            {
                title: 'Do stuff',
                onClick(_id, rowData) {
                    this.$.showToast(`It did stuff! ${_id} ${rowData.product}`);
                },
            },
            ui.menuSeparator(),
            {
                title: 'Some other stuff',
                icon: 'bank_with_card',
                onClick(_id, rowData) {
                    this.$.showToast(`It did some other stuff! ${_id} ${rowData.product}`, {
                        type: 'warning',
                    });
                },
            },
        ],
        headerQuickActions: [
            {
                title: 'Lorem',
                icon: 'three_boxes',
                onClick(_id, rowData) {
                    this.$.showToast(`You clicked Lorem ${_id} ${rowData.product}`);
                },
            },
            {
                title: 'Ipsum',
                icon: 'video',
                onClick(_id, rowData) {
                    this.$.showToast(`You clicked Ipsum ${_id} ${rowData.product}`);
                },
            },
        ],
        async onRecordOpened(_id) {
            this.originAddress.value = this.field.getRecordValue(_id).originAddress;
        },
        title(_id, recordValue) {
            return recordValue.product;
        },
        layout() {
            return {
                mainSection: {
                    title: 'Main section',
                    blocks: {
                        mainBlock: {
                            title: 'Some block title',
                            fields: ['qty', { supplier: { name: true }} ],
                        },
                        anotherBlock: {
                            title: 'Address',
                            fields: [this.originAddress],
                        },
                    },
                },
                anotherSection: {
                    isHidden(_id, rowData) {
                        return rowData.qty < 10;
                    },
                    title: 'Some other section',
                    blocks: {
                        someRandomBlock: {
                            fields: ['product'],
                        },
                    },
                },
            };
        },
    },

})
products: ui.fields.Table<SalesOrderItem>;
```

## Opening the sidebar

The sidebar is opened via the corresponding table's control object:

`this.products.openSidebar(recordId);`

The `recordId` argument is optional. If it is not provided the sidebar, the field values are populated using a default values query to the server and on confirmation a new record is added to the table's dataset.

The sidebar will automatically close if the record is removed from the table.

## Lifecycle events

-   **onSidebarOpen**: Triggered after the user opens the sidebar or changes record using the navigation arrows in the sidebar header. When the event triggered, the row is already considered to be in transaction mode, so any changes applied to the record are only applied to the sidebar until the user confirms the changes. This could be ideal place to update the value of any page body fields that are used on the sidebar.
-   **onSidebarConfirmed**: Triggered after the user confirms the changes in the sidebar. The event is triggered after the sidebar values are applied to the table, so this event can be ideal to recalculate totals for example.
-   **onSidebarDiscarded**: Triggered when the user discards the changes on the sidebar. The application code cannot access the discarded record value.

## Title

The title of the sidebar can be defined using the `title` decorator property. It can be defined as a callback. If no title is provided, the table's title is used.

## Header actions

There are two types of actions of the sidebar, they both follow the same interface definition as the record actions of the table. More info [here](./Table+Field#TableField-Recordactionproperties).

- Quick actions: Represented by icon buttons on the right of the sidebar header.
- Dropdown actions: Represented by a dropdown menu between the quick actions and the close icon in the sidebar header.

## Header card

The `mobileCard` table decorator property is used to defined the header card for the sidebar.

## Sidebar layout

The sidebar layout consists of the traditional 3 levels of elements that can be found across the XTreeM platform: sections, blocks and fields.

### Title translations

The title translations are only extracted automatically if the layout is defined as pure object, not as a function that returns the layout. In case of the function approach is used, the titles should be localized manually using the `ui.localize` function.

### Sections

The sidebar sections are represented as tabs in the sidebar. Only one section is visible at any given time, the user can change the active section using the tab control on the top of the sidebar.

#### Properties:
-   **title**: The title of the section.
-   **isHidden(recordId, recordValue)**: Function property, if the returned value is true, the section is not rendered.
-   **blocks**: Dictionary of children blocks.


## Blocks

The sidebar blocks are used to group fields on the user interface. They are rendered within the section on the top of each other.

-   **title**: The title of the block.
-   **isHidden(recordId, recordValue)**: Function property, if the returned value is true, the section is not rendered.
-   **fields**: Array of children fields that consists of simple column bind values (e.g `'qty'`), deep nested column values (e.g `{ supplier: { name: true }}`) or page field control object references (e.g `this.someFieldFromThePage`). Please note that the field that is referred in the array must be defined as an existing column or a field on the page body. If a bind value is defined which in the sidebar field list that is not in the column list, an error will be thrown.


### Field sizing
Like any other property, sizing related properties are inherited from the corresponding columns definition or in case of page fields from the field definition.
