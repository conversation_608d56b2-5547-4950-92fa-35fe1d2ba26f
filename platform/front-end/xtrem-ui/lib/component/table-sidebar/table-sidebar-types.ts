import type { ClientNode, Dict } from '@sage/xtrem-client';
import type { ScreenExtension } from '../../types';
import type { ValueOrCallback } from '../../utils/types';
import type { PropertyValueType } from '../field/reference/reference-types';
import type {
    CollectionItemAction,
    CollectionItemActionGroup,
    CollectionItemActionOrMenuSeparator,
} from '../ui/table-shared/table-dropdown-actions/table-dropdown-action-types';
import type { FieldControlObjectInstance, FieldKey } from '../types';
import type { VoidPromise } from '../field/traits';

export interface SidebarFieldBinding {
    /* Element Id in case of a normal field, normalized nested bind fields */
    fieldId: string;
    isNested: boolean;
}

export type SidebarFieldDefinition<ReferencedItemType extends ClientNode = any> =
    | PropertyValueType<ReferencedItemType>
    | FieldControlObjectInstance<FieldKey>;

export interface SidebarBlockDefinition<
    CT extends ScreenExtension<CT> = any,
    ReferencedItemType extends ClientNode = any,
> {
    title?: string;
    fields: SidebarFieldDefinition<ReferencedItemType>[];
    isHidden?: (this: CT, _id: string, recordValue?: ReferencedItemType) => boolean;
}

export interface SidebarSectionDefinition<
    CT extends ScreenExtension<CT> = any,
    ReferencedItemType extends ClientNode = any,
> {
    title: string;
    blocks: Dict<SidebarBlockDefinition<CT, ReferencedItemType>>;
    isHidden?: (this: CT, _id: string, recordValue?: ReferencedItemType) => boolean;
    onActive?: (this: CT, _id: string, recordValue?: ReferencedItemType) => VoidPromise;
}

export interface SidebarDefinitionDecorator<
    CT extends ScreenExtension<CT> = any,
    ReferencedItemType extends ClientNode = any,
> {
    isHeaderCardHidden?: boolean;
    title?: string | ((this: CT, _id: string, recordValue?: ReferencedItemType) => string);
    isReadOnly?: (this: CT, _id: string, recordValue?: ReferencedItemType) => boolean;
    onRecordOpened?: (this: CT, _id: string, recordValue?: ReferencedItemType) => VoidPromise;
    onRecordConfirmed?: (this: CT, _id: string, recordValue?: ReferencedItemType) => VoidPromise;
    onRecordDiscarded?: (this: CT, _id: string, recordValue?: ReferencedItemType) => VoidPromise;
    layout: ValueOrCallback<CT, Dict<SidebarSectionDefinition<CT, ReferencedItemType>>>;
    headerDropdownActions?: Array<
        CollectionItemActionOrMenuSeparator<CT, ReferencedItemType> | CollectionItemActionGroup<CT, ReferencedItemType>
    >;
    headerQuickActions?: Array<CollectionItemAction<CT, ReferencedItemType>>;
}
