import { connect } from 'react-redux';
import type * as xtremRedux from '../../redux';
import { getPageDefinitionFromState } from '../../utils/state-utils';
import React from 'react';
import type { SidebarFieldBinding } from './table-sidebar-types';
import type { TableProperties } from '../field/table/table-component-types';
import { findColumnDefinitionByBind, normalizeUnderscoreBind } from '../../utils/abstract-fields-utils';
import { FieldKey } from '../types';
import { calculateFieldWidth } from '../../utils/responsive-utils';
import { GridColumn } from '@sage/xtrem-ui-components';
import { AsyncAggregateComponent } from '../field/aggregate/async-aggregate-component';
import { AsyncButtonComponent } from '../field/button/async-button-component';
import { AsyncCardComponent } from '../field/card/async-card-component';
import { AsyncCheckboxComponent } from '../field/checkbox/async-checkbox-component';
import { AsyncCountComponent } from '../field/count/async-count-component';
import { AsyncDateComponent } from '../field/date/async-date-component';
import { AsyncFilterSelectComponent } from '../field/filter-select/async-filter-select-component';
import { AsyncIconComponent } from '../field/icon/async-icon-component';
import { AsyncImageComponent } from '../field/image/async-image-component';
import { AsyncLabelComponent } from '../field/label/async-label-component';
import { AsyncLinkComponent } from '../field/link/async-link-component';
import { AsyncMultiReferenceComponent } from '../field/multi-reference/async-multi-reference-component';
import { AsyncNumericComponent } from '../field/numeric/async-numeric-component';
import { AsyncPodCollectionComponent } from '../field/pod-collection/async-pod-collection-component';
import { AsyncPodComponent } from '../field/pod/async-pod-component';
import { AsyncProgressComponent } from '../field/progress/async-progress-component';
import { AsyncReferenceComponent } from '../field/reference/async-reference-component';
import { AsyncRichTextComponent } from '../field/rich-text/async-rich-text-component';
import { AsyncStaticContentComponent } from '../field/static-content/async-static-content-component';
import { AsyncStepSequenceComponent } from '../field/step-sequence/async-step-sequence-component';
import { AsyncSwitchComponent } from '../field/switch/async-switch-component';
import { AsyncTableSummaryComponent } from '../field/table-summary/async-table-summary-component';
import { AsyncTextAreaComponent } from '../field/text-area/async-text-area-component';
import { AsyncTextComponent } from '../field/text/async-text-component';
import { AsyncToggleComponent } from '../field/toggle/async-toggle-component';
import { AsyncVisualProcessComponent } from '../field/visual-process/async-visual-process-component';
import { AsyncMessageComponent } from '../field/message/async-message-component';
import { AsyncFileComponent } from '../field/file/async-file-component';
import { AsyncSeparatorComponent } from '../field/separator/async-separator-component';
import { AsyncVitalPodComponent } from '../field/vital-pod/async-vital-pod-component';
import type {
    BaseErrorableComponentProperties,
    NestedFieldsAdditionalProperties,
} from '../field/field-base-component-types';
import type { Dict, LocalizeLocale } from '@sage/xtrem-shared';
import type { ReduxResponsive } from '../../plugin';
import { get, isNil, noop } from 'lodash';
import { ConnectedSelectLikeFieldWrapper } from '../../render/select-like-field-wrapper';
import { ContextType } from '../../types';
import type { CollectionValue } from '../../service/collection-data-service';
import { withCollectionValueItemSubscription } from '../connected-collection';
import type { EditableFieldProperties } from '../editable-field-control-object';
import type { ValidationResult } from '../../service/screen-base-definition';
import type { FormattedNodeDetails } from '../../service/metadata-types';
import type { NestedGridDecoratorProperties, TableDecoratorProperties } from '../decorator-properties';
import { AsyncDatetimeRangeComponent } from '../field/datetime-range/async-datetime-range-component';
import type { NestedField } from '../nested-fields';
import { AsyncDatetimeComponent } from '../field/datetime/async-datetime-component';

export interface TableSidebarNestedFieldExternalProps extends SidebarFieldBinding {
    availableColumns: number;
    screenId: string;
    elementId: string;
    recordId: string;
    level?: number;
    isUncommitted: boolean;
    columns: Array<NestedField<any, any>>;
}

export interface TableSidebarNestedFieldProps extends TableSidebarNestedFieldExternalProps {
    fieldProperties: EditableFieldProperties;
    setFieldProperties: (bind: string, fieldProperties: EditableFieldProperties) => void;
    browser: ReduxResponsive;
    fieldType: FieldKey;
    onChange?: (bind: string, value: any) => Promise<void>;
    nodeTypes: Dict<FormattedNodeDetails>;
    locale: LocalizeLocale;
    value: CollectionValue;
    recordValue: any;
    shouldFetchDefault: boolean;
    tableProperties: TableDecoratorProperties | NestedGridDecoratorProperties;
    validationErrors?: ValidationResult[];
}

export function TableSidebarNestedField({
    availableColumns,
    browser,
    elementId,
    fieldId,
    fieldProperties,
    fieldType,
    level,
    locale,
    onChange,
    recordValue,
    screenId,
    tableProperties,
    validationErrors,
    setFieldProperties,
}: TableSidebarNestedFieldProps): React.ReactElement | null {
    const renderField = React.useCallback((): React.ReactElement | null => {
        let contextNode: string | undefined;
        if (tableProperties.node) {
            contextNode = String(tableProperties.node);
        } else if (!isNil(level) && !isNil((tableProperties as NestedGridDecoratorProperties).levels)) {
            contextNode = (tableProperties as NestedGridDecoratorProperties).levels[level].node;
        }
        const props: BaseErrorableComponentProperties<any, any, NestedFieldsAdditionalProperties> = {
            value: get(recordValue, fieldId),
            fieldProperties,
            elementId: fieldId,
            contextType: ContextType.sidebar,
            locale,
            screenId,
            level,
            contextNode,
            isNested: true,
            parentElementId: elementId,
            recordContext: recordValue,
            onFocus: noop,
            setFieldProperties,
            shouldRenderLabelInNestedReadOnlyMode: true,
            validate: () => Promise.resolve([]),
            setFieldValue: async (bind: string, v: string): Promise<void> => {
                if (onChange) {
                    await onChange(bind, v);
                }
            },
            validationErrors:
                validationErrors?.filter(e => (e.columnId || e.recordId) === normalizeUnderscoreBind(fieldId)) || [],
            browser,
            removeNonNestedErrors: noop,
            handlersArguments: {
                rowValue: recordValue,
                onClick: [recordValue._id, recordValue],
                onChange: [recordValue._id, recordValue],
            },
        };

        // Shitty thing because switch case complains in sonarcloud about too many cases
        if (fieldType === FieldKey.Aggregate) {
            return <AsyncAggregateComponent {...props} />;
        }
        if (fieldType === FieldKey.Button) {
            return <AsyncButtonComponent {...props} />;
        }

        switch (fieldType) {
            case FieldKey.Card:
                return <AsyncCardComponent {...props} />;
            case FieldKey.Checkbox:
                return <AsyncCheckboxComponent {...props} />;
            case FieldKey.Count:
                return <AsyncCountComponent {...props} />;
            case FieldKey.Date:
                return <AsyncDateComponent {...props} />;
            case FieldKey.Datetime:
                return <AsyncDatetimeComponent {...props} />;
            case FieldKey.DatetimeRange:
                return <AsyncDatetimeRangeComponent {...props} />;
            case FieldKey.File:
                return <AsyncFileComponent {...props} />;
            case FieldKey.FilterSelect:
                return <AsyncFilterSelectComponent {...props} contextNode={contextNode} />;
            case FieldKey.Icon:
                return <AsyncIconComponent {...props} />;
            case FieldKey.Image:
                return <AsyncImageComponent {...props} />;
            case FieldKey.Label:
                return <AsyncLabelComponent {...props} />;
            case FieldKey.Link:
                return <AsyncLinkComponent {...props} />;
            case FieldKey.Message:
                return <AsyncMessageComponent {...props} />;
            case FieldKey.MultiReference:
                return <AsyncMultiReferenceComponent {...props} contextNode={contextNode} />;
            case FieldKey.Numeric:
                return <AsyncNumericComponent {...props} />;
            case FieldKey.Progress:
                return <AsyncProgressComponent {...props} />;
            case FieldKey.Reference:
                return <AsyncReferenceComponent {...props} contextNode={contextNode} />;
            case FieldKey.RichText:
                return <AsyncRichTextComponent {...props} />;
            case FieldKey.PodCollection:
                return <AsyncPodCollectionComponent {...props} />;
            case FieldKey.Separator:
                return <AsyncSeparatorComponent {...props} />;
            case FieldKey.StaticContent:
                return <AsyncStaticContentComponent {...props} />;
            case FieldKey.StepSequence:
                return <AsyncStepSequenceComponent {...props} />;
            case FieldKey.Switch:
                return <AsyncSwitchComponent {...props} />;
            case FieldKey.TableSummary:
                return <AsyncTableSummaryComponent {...props} />;
            case FieldKey.Text:
                return <AsyncTextComponent {...props} />;
            case FieldKey.TextArea:
                return <AsyncTextAreaComponent {...props} />;
            case FieldKey.Toggle:
                return <AsyncToggleComponent {...props} />;
            case FieldKey.VisualProcess:
                return <AsyncVisualProcessComponent {...props} />;
            case FieldKey.VitalPod:
                return <AsyncVitalPodComponent {...props} />;
            case FieldKey.Pod:
                return <AsyncPodComponent {...props} />;
            case FieldKey.Select:
            case FieldKey.MultiDropdown:
            case FieldKey.DropdownList:
            case FieldKey.Radio:
                return <ConnectedSelectLikeFieldWrapper {...props} fieldType={fieldType} />;

            default:
                return (
                    <div>
                        Unhandled field:
                        {fieldType}
                    </div>
                );
        }
    }, [
        browser,
        elementId,
        fieldId,
        fieldProperties,
        fieldType,
        level,
        locale,
        onChange,
        recordValue,
        screenId,
        tableProperties,
        validationErrors,
        setFieldProperties,
    ]);

    const columnSpan = React.useMemo(
        () =>
            calculateFieldWidth(
                browser.is,
                fieldType,
                availableColumns,
                fieldProperties?.isFullWidth,
                fieldProperties?.width,
            ),
        [availableColumns, browser.is, fieldProperties?.isFullWidth, fieldProperties?.width, fieldType],
    );

    if (!recordValue) {
        return null;
    }

    return <GridColumn columnSpan={columnSpan}>{renderField()}</GridColumn>;
}

export const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: TableSidebarNestedFieldExternalProps,
): Partial<TableSidebarNestedFieldProps> => {
    const pageDefinition = getPageDefinitionFromState(props.screenId, state);
    const tableProperties = pageDefinition?.metadata.uiComponentProperties[props.elementId] as TableProperties;
    const fieldProperties = findColumnDefinitionByBind(props.columns || [], props.fieldId)?.properties as any;

    const shouldFetchDefault = fieldProperties?.fetchesDefaults;
    const fieldType = findColumnDefinitionByBind(props.columns || [], props.fieldId)?.type;
    const value = pageDefinition.values[props.elementId];

    return {
        ...props,
        value,
        tableProperties,
        browser: state.browser,
        fieldProperties,
        fieldType: fieldType as FieldKey,
        locale: state.applicationContext?.locale as LocalizeLocale,
        nodeTypes: state.nodeTypes,
        isUncommitted: true,
        shouldFetchDefault,
    };
};

export const ConnectedTableSidebarNestedField = connect(mapStateToProps)(
    withCollectionValueItemSubscription(TableSidebarNestedField),
);
