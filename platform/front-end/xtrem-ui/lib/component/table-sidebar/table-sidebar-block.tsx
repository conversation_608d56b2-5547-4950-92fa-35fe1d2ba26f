import { connect } from 'react-redux';
import type * as xtremRedux from '../../redux';
import React from 'react';
import type { SidebarBlockDefinition, SidebarFieldBinding } from './table-sidebar-types';
import Typography from 'carbon-react/esm/components/typography';
import type { ReduxResponsive } from '../../redux/state';
import { calculateContainerWidth, getGutterSize } from '../../utils/responsive-utils';
import { GridRow } from '@sage/xtrem-ui-components';
import { AbstractUiControlObject } from '../abstract-ui-control-object';
import { convertDeepBindToPathNotNull } from '../../utils/nested-field-utils';
import { ConnectedTableSidebarNestedField } from './table-sidebar-nested-field';
import { ConnectedTableSidebarNormalField } from './table-sidebar-normal-field';
import type { NestedField } from '../nested-fields';
import { findColumnDefinitionByBind } from '../../utils/abstract-fields-utils';
import { FieldKey } from '../types';

const COLUMN_COUNT = 4;

export interface TableSidebarBlockExternalProps {
    block: SidebarBlockDefinition;
    columns: Array<NestedField<any, any>>;
    elementId: string;
    recordId: string;
    screenId: string;
    level?: number;
}

export interface TableSidebarBlockProps extends TableSidebarBlockExternalProps {
    browser: ReduxResponsive;
    fields: SidebarFieldBinding[];
    level?: number;
    recordId: string;
    screenId: string;
    title?: string;
}

export function TableSidebarBlock({
    browser,
    columns,
    elementId,
    fields,
    level,
    recordId,
    screenId,
    title,
}: TableSidebarBlockProps): React.ReactElement {
    const computedWidth = calculateContainerWidth(browser.is, COLUMN_COUNT, 'extra-large');
    const gridGutter = getGutterSize(browser.is);

    const filteredFields = fields.filter((f: SidebarFieldBinding) => {
        if (!f.isNested) {
            return true;
        }
        const fieldType = findColumnDefinitionByBind(columns || [], f.fieldId)?.type;
        if (!fieldType || fieldType === FieldKey.Technical) {
            return false;
        }
        return true;
    });

    return (
        <div className="e-table-sidebar-block">
            {title && (
                <Typography variant="h4" mb="8px">
                    {title}
                </Typography>
            )}
            <GridRow columns={computedWidth} gutter={gridGutter} margin={0} verticalMargin={0}>
                {filteredFields.map(f =>
                    f.isNested ? (
                        <ConnectedTableSidebarNestedField
                            {...f}
                            elementId={elementId}
                            recordId={recordId}
                            level={level}
                            columns={columns}
                            isUncommitted={true}
                            screenId={screenId}
                            key={`${convertDeepBindToPathNotNull(f.fieldId)}-${f.isNested ? 'nested' : 'main'}}`}
                            availableColumns={COLUMN_COUNT}
                        />
                    ) : (
                        <ConnectedTableSidebarNormalField
                            screenId={screenId}
                            key={`${convertDeepBindToPathNotNull(f.fieldId)}-${f.isNested ? 'nested' : 'main'}}`}
                            availableColumns={COLUMN_COUNT}
                            fieldId={f.fieldId}
                        />
                    ),
                )}
            </GridRow>
        </div>
    );
}

export const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: TableSidebarBlockExternalProps,
): TableSidebarBlockProps => {
    const fields = props.block.fields.map<SidebarFieldBinding>(f => {
        if (f instanceof AbstractUiControlObject) {
            return {
                isNested: false,
                fieldId: f.id,
            };
        }

        return {
            fieldId: convertDeepBindToPathNotNull(f as string),
            isNested: true,
        };
    });

    return {
        ...props,
        fields,
        browser: state.browser,
        title: props.block.title,
    };
};

export const ConnectedTableSidebarBlock = connect(mapStateToProps)(TableSidebarBlock);
