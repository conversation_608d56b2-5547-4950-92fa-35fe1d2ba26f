jest.mock('react-redux', () => ({
    connect: () => {
        return component => {
            return component;
        };
    },
}));

jest.mock('../table-sidebar-nested-field', () => ({
    ConnectedTableSidebarNestedField: e => {
        return <div>{`Mocked ConnectedTableSidebarNestedField ${e.fieldId}`}</div>;
    },
    TableSidebarNestedField: e => {
        return <div>{`Mocked TableSidebarNestedField ${e.fieldId}`}</div>;
    },
}));

jest.mock('../table-sidebar-normal-field', () => ({
    ConnectedTableSidebarNormalField: e => {
        return <div>{`Mocked ConnectedTableSidebarNormalField ${e.fieldId}`}</div>;
    },
    TableSidebarNormalField: e => {
        return <div>{`Mocked TableSidebarNormalField ${e.fieldId}`}</div>;
    },
}));

import React from 'react';
import { render } from '@testing-library/react';
import type { TableSidebarBlockProps } from '../table-sidebar-block';
import type { SidebarFieldBinding } from '../table-sidebar-types';
import { TextControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { PageArticleItem } from '../../../service/layout-types';
import { text } from '../../nested-fields';
import { TableSidebarBlock } from '../table-sidebar-block';

describe('Table sidebar block component', () => {
    let tableSideProps: TableSidebarBlockProps;
    let fields: SidebarFieldBinding[];
    const controlObject = new TextControlObject({
        componentKey: FieldKey.Text,
        elementId: 'field3',
        dispatchValidation: jest.fn(),
        getValue: jest.fn().mockReturnValue(''),
        getUiComponentProperties: jest.fn(),
        layout: {} as PageArticleItem,
        screenId: 'screenId',
        setUiComponentProperties: jest.fn(),
        setValue: jest.fn(),
        refresh: jest.fn(),
        focus: jest.fn(),
        isFieldDirty: jest.fn(),
        setFieldDirty: jest.fn(),
        setFieldClean: jest.fn(),
        isFieldInFocus: jest.fn(),
    });

    beforeEach(() => {
        fields = [
            {
                fieldId: 'field1',
                isNested: false,
            },
            {
                fieldId: 'field2',
                isNested: true,
            },
        ];

        tableSideProps = {
            screenId: 'screenId',
            title: 'title',
            elementId: 'elementId',
            browser: {
                is: {
                    l: true,
                    m: false,
                    s: false,
                    xs: false,
                },
            } as any,
            fields,
            recordId: '123',
            block: {
                title: 'title block',
                fields: ['field1', 'field2', controlObject],
            },
            columns: [text({ bind: 'field1' }), text({ bind: 'field2' })],
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should render and match the snapshot', () => {
        const { container } = render(<TableSidebarBlock {...tableSideProps} />);
        expect(container.parentElement).toMatchSnapshot();
    });
});
