// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Table sidebar block component should render and match the snapshot 1`] = `
.c0 {
  font-style: normal;
  font-size: 18px;
  font-weight: 400;
  text-transform: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  line-height: 22.5px;
  margin: 0;
  padding: 0;
  margin-bottom: 8px;
  color: rgba(0,0,0,0.90);
}

<body>
  <div>
    <div
      class="e-table-sidebar-block"
    >
      <h4
        class="c0"
        color="blackOpacity90"
      >
        title
      </h4>
      <div
        class="e-grid-row e-grid-row-4 "
        style="grid-template-columns: repeat(4, 1fr); padding: 0px 0px 0px 0px;"
      >
        <div>
          Mocked ConnectedTableSidebarNormalField field1
        </div>
        <div>
          Mocked ConnectedTableSidebarNestedField field2
        </div>
      </div>
    </div>
  </div>
</body>
`;
