import React from 'react';
import type { CollectionValue } from '../../service/collection-data-service';
import type { CardDefinition } from '../ui/card/card-component';
import { CardComponent } from '../ui/card/card-component';

export interface TableSidebarCardProps {
    cardDefinition?: CardDefinition<any>;
    screenId: string;
    recordValue: any;
    elementId: string;
    // eslint-disable-next-line react/no-unused-prop-types
    recordId?: string;
    // eslint-disable-next-line react/no-unused-prop-types
    value: CollectionValue;
    // eslint-disable-next-line react/no-unused-prop-types
    level?: number;
    // eslint-disable-next-line react/no-unused-prop-types
    isUncommitted: boolean;
}

export function TableSidebarCard({
    cardDefinition,
    screenId,
    elementId,
    recordValue,
}: TableSidebarCardProps): React.ReactElement | null {
    if (!cardDefinition) {
        return null;
    }

    return (
        <CardComponent
            canDrag={false}
            canSelect={false}
            cardDefinition={cardDefinition}
            isDisabled={false}
            isNewItem={false}
            isSelected={false}
            parentElementId={elementId}
            screenId={screenId}
            value={recordValue}
        />
    );
}
