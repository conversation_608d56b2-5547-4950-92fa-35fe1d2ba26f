import { connect } from 'react-redux';
import * as xtremRedux from '../../redux';
import type { XtremTabItem } from '../ui/tabs/xtrem-tabs';
import { XtremTabs } from '../ui/tabs/xtrem-tabs';
import React from 'react';
import { ConnectedTableSidebarSection } from './table-sidebar-section';
import { TableSidebarCard } from './table-sidebar-card';
import type { CollectionValue } from '../../service/collection-data-service';
import { resolveByValue } from '../../utils/resolve-value-utils';
import { withCollectionValueItemSubscription } from '../connected-collection';
import { objectKeys, type Dict } from '@sage/xtrem-shared';
import type { SidebarDefinitionDecorator, SidebarSectionDefinition } from './table-sidebar-types';
import type { ValidationResult } from '../../service/screen-base-definition';
import type { CardDefinition } from '../ui/card/card-component';
import Form from 'carbon-react/esm/components/form';
import Button from 'carbon-react/esm/components/button';
import { localize } from '../../service/i18n-service';
import { getResolvedSidebarLayout, getSidebarNormalFields } from '../../utils/state-utils';
import Loader from 'carbon-react/esm/components/loader';
import type { NestedField } from '../nested-fields';
import { executeEventHandlerWithExternalHandler } from '../../utils/events';
import { xtremConsole } from '../../utils/console';
import { useDeepCompareEffect } from '@sage/xtrem-ui-components';
import { BusinessActionButtonWrapper } from '../container/footer/bussiness-action-button-wrapper';

export interface TableSidebarBodyExternalProps {
    cardDefinition?: CardDefinition;
    columns: Array<NestedField<any, any>>;
    dialogId: number;
    elementId: string;
    isNewRecord?: boolean;
    level?: number;
    recordId?: string;
    screenId: string;
    sidebarDefinition: SidebarDefinitionDecorator;
}

export interface TableSidebarBodyProps extends TableSidebarBodyExternalProps {
    hasExternalErrors: boolean;
    isHeaderCardHidden?: boolean;
    isScreenLessThanM: boolean;
    isUncommitted: boolean;
    onCancel: () => void;
    onConfirm: (addNewRecord?: boolean) => void;
    recordValue?: any;
    sidebarLayoutDefinition: Dict<SidebarSectionDefinition>;
    validationErrors: ValidationResult[];
    value: CollectionValue;
}

export function TableSidebarBody({
    cardDefinition,
    columns,
    elementId,
    hasExternalErrors,
    isHeaderCardHidden,
    isNewRecord,
    isScreenLessThanM,
    level,
    onCancel,
    onConfirm,
    recordId,
    recordValue,
    screenId,
    sidebarLayoutDefinition,
    validationErrors,
    value,
}: TableSidebarBodyProps): React.ReactElement {
    const sections = React.useMemo(
        () =>
            objectKeys(sidebarLayoutDefinition).reduce((acc: XtremTabItem[], currentKey: string) => {
                const sectionDefinition = sidebarLayoutDefinition[currentKey];
                const isHidden =
                    (!!screenId &&
                        resolveByValue({
                            propertyValue: sectionDefinition.isHidden,
                            rowValue: recordValue,
                            fieldValue: recordId,
                            screenId,
                            skipHexFormat: true,
                        })) ||
                    false;

                if (isHidden) {
                    return acc;
                }
                let errorMessage;
                objectKeys(sectionDefinition.blocks).some(blockKey => {
                    const fields = sectionDefinition.blocks[blockKey].fields;
                    // Find if inside validation errors columnId match with some of the fields array and return that validationError
                    const validationError = validationErrors.find(error => fields && fields.includes(error.columnId));
                    if (validationError?.message) {
                        errorMessage = validationError?.message;
                        return true;
                    }
                    return false;
                });

                acc.push({ id: currentKey, title: sectionDefinition.title, validationMessage: errorMessage });
                return acc;
            }, [] as XtremTabItem[]),
        [recordId, recordValue, screenId, sidebarLayoutDefinition, validationErrors],
    );

    const [currentTab, setCurrentTab] = React.useState(sections[0]?.id || '');
    const [isSectionLoading, setSectionLoading] = React.useState(false);

    const activeEventHandler = React.useMemo(
        () => sidebarLayoutDefinition[currentTab].onActive,
        [currentTab, sidebarLayoutDefinition],
    );

    const onTabSelection = React.useCallback(
        (tabId: string): void => {
            setCurrentTab(tabId);
            if (sidebarLayoutDefinition[tabId].onActive) {
                setSectionLoading(true);
            }
        },
        [sidebarLayoutDefinition],
    );

    useDeepCompareEffect(() => {
        const executeHandler = async (): Promise<void> => {
            if (activeEventHandler) {
                try {
                    await executeEventHandlerWithExternalHandler({
                        elementId,
                        screenId,
                        eventHandler: activeEventHandler as any,
                        args: [recordId, recordValue],
                    });
                } catch (error) {
                    xtremConsole.error('Error executing event handler:', error);
                }
            }
            setSectionLoading(false);
        };

        if (activeEventHandler) {
            setSectionLoading(true);
        }

        executeHandler();
    }, [recordId, currentTab, elementId, screenId, recordValue]);

    const applyAndAddNewLabel = isScreenLessThanM
        ? localize('@sage/xtrem-ui/sidebar-mobile-apply-changes-and-create-new', 'Apply & new')
        : localize('@sage/xtrem-ui/sidebar-apply-changes-and-create-new', 'Apply and add new');

    const applyLabel = localize('@sage/xtrem-ui/sidebar-apply-changes', 'Apply');
    const cancelLabel = localize('@sage/xtrem-ui/cancel', 'Cancel');

    if (!recordId) {
        return <Loader />;
    }

    return (
        <Form
            onSubmit={(ev: React.FormEvent<HTMLFormElement>): void => {
                ev.preventDefault();
            }}
            rightSideButtons={
                <>
                    {isNewRecord && (
                        <BusinessActionButtonWrapper title={applyAndAddNewLabel} id="apply-and-add-new-button">
                            <Button
                                data-testid="e-table-sidebar-confirm-and-add-new-button"
                                buttonType="secondary"
                                onClick={(): void => onConfirm(true)}
                                ml="24px"
                                disabled={validationErrors.length > 0 || hasExternalErrors}
                            >
                                {applyAndAddNewLabel}
                            </Button>
                        </BusinessActionButtonWrapper>
                    )}
                    <BusinessActionButtonWrapper title={applyLabel} id="apply-button">
                        <Button
                            data-testid="e-table-sidebar-confirm-button"
                            buttonType="primary"
                            onClick={(): void => onConfirm(false)}
                            ml="24px"
                            disabled={validationErrors.length > 0 || hasExternalErrors}
                        >
                            {applyLabel}
                        </Button>
                    </BusinessActionButtonWrapper>
                </>
            }
            leftSideButtons={
                <BusinessActionButtonWrapper title={cancelLabel} id="cancel-button">
                    <Button
                        data-testid="e-table-sidebar-cancel-button"
                        buttonType="tertiary"
                        onClick={onCancel}
                        ml="24px"
                    >
                        {cancelLabel}
                    </Button>
                </BusinessActionButtonWrapper>
            }
        >
            <div data-testid="e-table-sidebar" className="e-table-sidebar">
                <div className="e-table-sidebar-body">
                    {!isHeaderCardHidden && (
                        <TableSidebarCard
                            elementId={elementId}
                            isUncommitted={true}
                            recordValue={recordValue}
                            screenId={screenId}
                            value={value}
                            cardDefinition={cardDefinition}
                            level={level}
                        />
                    )}

                    {sections.length > 1 && (
                        <XtremTabs
                            tabs={sections}
                            selectedTabId={currentTab}
                            onTabChange={onTabSelection}
                            inSidebar={true}
                        />
                    )}
                    {isSectionLoading && (
                        <div className="e-table-sidebar-section">
                            <Loader size="large" mt={10} />
                        </div>
                    )}
                    {!isSectionLoading && (
                        <ConnectedTableSidebarSection
                            columns={columns}
                            elementId={elementId}
                            level={level}
                            recordId={recordId}
                            screenId={screenId}
                            section={sidebarLayoutDefinition[currentTab]}
                        />
                    )}
                </div>
            </div>
        </Form>
    );
}

export const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: TableSidebarBodyExternalProps,
): Partial<TableSidebarBodyProps> => {
    const screenDefinition = state.screenDefinitions[props.screenId];
    const value = screenDefinition.values[props.elementId];

    const sidebarLayoutDefinition = getResolvedSidebarLayout({
        layout: props.sidebarDefinition.layout,
        elementId: props.elementId,
        nodeTypes: state.nodeTypes,
        level: props.level,
        screenDefinition,
        value,
    });

    let hasExternalErrors = false;

    const validationErrors = Object.keys(screenDefinition.errors).filter(e => screenDefinition.errors[e].length > 0);

    if (validationErrors.length > 0) {
        const fields = getSidebarNormalFields(sidebarLayoutDefinition).map(field => field.id);
        hasExternalErrors = Boolean(validationErrors.find(field => fields.includes(field)));
    }

    return {
        cardDefinition: props.cardDefinition,
        hasExternalErrors,
        isHeaderCardHidden: props.sidebarDefinition.isHeaderCardHidden,
        isScreenLessThanM: state.browser.lessThan.m,
        isUncommitted: true,
        level: props.level,
        recordId: props.recordId,
        screenId: props.screenId,
        sidebarLayoutDefinition,
        value,
    };
};

export const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: TableSidebarBodyProps,
): Partial<TableSidebarBodyProps> => {
    return {
        onConfirm: (addNewRecord = false): void => {
            dispatch(xtremRedux.actions.confirmTableSidebar(props.dialogId, addNewRecord));
        },
        onCancel: (): void => {
            dispatch(xtremRedux.actions.closeTableSidebar(props.dialogId));
        },
    };
};

export const ConnectedTableSidebarBody = connect(
    mapStateToProps,
    mapDispatchToProps,
)(withCollectionValueItemSubscription(TableSidebarBody));
