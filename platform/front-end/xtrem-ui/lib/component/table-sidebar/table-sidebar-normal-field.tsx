import { connect } from 'react-redux';
import type * as xtremRedux from '../../redux';
import { getPageDefinitionFromState } from '../../utils/state-utils';
import React from 'react';
import type { ReadonlyFieldProperties } from '../readonly-field-control-object';
import { ContextType } from '../../types';
import ConnectedFieldWrapper from '../../render/field-wrapper';

export interface TableSidebarNormalFieldExternalProps {
    fieldId: string;
    availableColumns: number;
    screenId: string;
}

export interface TableSidebarNormalFieldProps extends TableSidebarNormalFieldExternalProps {
    fieldProperties: ReadonlyFieldProperties;
}

export function TableSidebarNormalField({
    fieldProperties,
    availableColumns,
    fieldId,
    screenId,
}: TableSidebarNormalFieldProps): React.ReactElement {
    return (
        <ConnectedFieldWrapper
            screenId={screenId}
            item={{ $bind: fieldId, $isFullWidth: fieldProperties.isFullWidth }}
            availableColumns={availableColumns}
            contextType={ContextType.sidebar}
        />
    );
}

export const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: TableSidebarNormalFieldExternalProps,
): Partial<TableSidebarNormalFieldProps> => {
    const pageDefinition = getPageDefinitionFromState(props.screenId, state);
    const fieldProperties = pageDefinition.metadata.uiComponentProperties[props.fieldId];

    return {
        ...props,
        fieldProperties,
    };
};

export const ConnectedTableSidebarNormalField = connect(mapStateToProps)(TableSidebarNormalField);
