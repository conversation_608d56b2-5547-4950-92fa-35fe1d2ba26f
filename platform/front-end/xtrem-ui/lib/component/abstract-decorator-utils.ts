import { objectKeys, type Dict } from '@sage/xtrem-shared';
import lodash, { without } from 'lodash';
import { addCustomColumnToNestedFields, componentsWithLookup } from '../service/customization-service';
import type { FormattedNodeDetails } from '../service/metadata-types';
import type { PageMetadata } from '../service/page-metadata';
import type { ScreenExtension } from '../types';
import type { MenuSeparatorResult } from '../utils/action-menu-utils';
import { convertDeepBindToPathNotNull } from '../utils/nested-field-utils';
import { arrayMoveMutate } from '../utils/transformers';
import type { PageActionControlObject } from './control-objects';
import type { DetailListDecoratorProperties } from './decorator-properties';
import type { ExtensionLevel, Level } from './field/nested-grid/nested-grid-component-types';
import type { NestedReferenceProperties } from './field/reference/reference-types';
import type { TableDecoratorProperties, TableProperties } from './field/table/table-component-types';
import type { GridNestedFieldTypes, NestedField, NestedFieldTypes } from './nested-fields';
import type { NestedExtensionField, NestedReferenceExtensionProperties } from './nested-fields-extensions';
import type { NestedOverrideField } from './nested-fields-overrides';
import type { SidebarSectionDefinition } from './table-sidebar/table-sidebar-types';
import type { ComponentKey, DecoratorProperties } from './types';
import type { CardDefinition, CardExtensionDefinition } from './ui/card/card-component';
import type { CollectionItemActionOrMenuSeparator } from './ui/table-shared/table-dropdown-actions/table-dropdown-action-types';

export function mergeTableActions<
    T extends Array<CollectionItemActionOrMenuSeparator<any>>,
    U extends CollectionItemActionOrMenuSeparator<any>,
>(baseActions: T, newActions: Array<U>): T {
    const updatedBaseActions = Array.isArray(baseActions) ? [...baseActions] : [];
    const actionsToAdd = Array.isArray(newActions) ? [...newActions] : [];

    actionsToAdd.forEach(newAction => {
        const { insertBefore, insertAfter } = newAction;
        let insertionIndex = updatedBaseActions.length;

        if (insertBefore) {
            const index = updatedBaseActions.findIndex(
                action => typeof action === 'object' && 'id' in action && action.id === insertBefore,
            );
            if (index !== -1) insertionIndex = index;
        } else if (insertAfter) {
            const index = updatedBaseActions.findIndex(
                action => typeof action === 'object' && 'id' in action && action.id === insertAfter,
            );
            if (index !== -1) insertionIndex = index + 1;
        }

        updatedBaseActions.splice(insertionIndex, 0, newAction);
    });

    return updatedBaseActions as T;
}

export const findColumnIndex = (columns: NestedField<any, NestedFieldTypes>[], match: string): number => {
    const insertBeforeParts = match.split('__');
    // First try to find with comparing the `bind` AND the `valueField` properties.
    let index = columns.findIndex(
        baseColumnDef =>
            baseColumnDef.properties.bind === insertBeforeParts[0] &&
            (baseColumnDef.properties as NestedReferenceExtensionProperties<any>).valueField === insertBeforeParts[1],
    );
    // If not found, we just use the `bind` property.
    if (index === -1) {
        index = columns.findIndex(
            baseColumnDef =>
                convertDeepBindToPathNotNull(baseColumnDef.properties.bind) ===
                convertDeepBindToPathNotNull(insertBeforeParts[0]),
        );
    }
    return index;
};

export const insertColumn = (
    columns: NestedField<any, any>[],
    column: NestedExtensionField<any, any>,
    override = false,
    onlyInsertIfFound = false,
): void => {
    if (column?.properties.insertBefore) {
        const insertBeforeIdx = findColumnIndex(columns, column.properties.insertBefore);
        if (insertBeforeIdx !== -1) {
            if (override) {
                // Move column
                const currentIdx = findColumnIndex(columns, convertDeepBindToPathNotNull(column.properties.bind));
                arrayMoveMutate(columns, currentIdx, insertBeforeIdx);
                return;
            }
            // Splice column
            columns.splice(insertBeforeIdx, 0, column as any);
            return;
        }
    } else if (column?.properties.insertAfter) {
        const insertAfterIdx = findColumnIndex(columns, column.properties.insertAfter);
        if (insertAfterIdx !== -1) {
            if (override) {
                // Move column
                const currentIdx = findColumnIndex(columns, convertDeepBindToPathNotNull(column.properties.bind));
                arrayMoveMutate(columns, currentIdx, insertAfterIdx);
                return;
            }
            // Splice column
            columns.splice(insertAfterIdx + 1, 0, column as any);
            return;
        }
    }
    // Append column: If none of the conditions were met above, we just push the column to the end.
    if (!override && !onlyInsertIfFound) {
        columns.push(column as any);
    }
};

/**
 * It merges the custom fields into the sidePanelLayout and returns a copy with the custom fields on it
 */
export const mergeIntoSidebarLayout = (
    sidePanelLayout: Dict<SidebarSectionDefinition>,
    customFields: NestedExtensionField<any, any>[],
): Dict<SidebarSectionDefinition> => {
    const cloneSidePanelLayout = { ...sidePanelLayout };
    customFields.forEach(customField => {
        const { insertBefore, insertAfter } = customField.properties;
        objectKeys(cloneSidePanelLayout).forEach(sectionKey => {
            const section = cloneSidePanelLayout[sectionKey];
            objectKeys(section.blocks).forEach(blockKey => {
                const block = section.blocks[blockKey];
                if (Array.isArray(block?.fields)) {
                    const blockIndex = block.fields.findIndex(
                        field =>
                            (field?.constructor?.name === 'String' || field?.constructor?.name === 'Object') &&
                            (convertDeepBindToPathNotNull(field) === insertBefore ||
                                convertDeepBindToPathNotNull(field) === insertAfter),
                    );
                    if (insertBefore && blockIndex !== -1) {
                        block.fields.splice(blockIndex, 0, convertDeepBindToPathNotNull(customField.properties.bind));
                    } else if (insertAfter && blockIndex !== -1) {
                        block.fields.splice(
                            blockIndex + 1,
                            0,
                            convertDeepBindToPathNotNull(customField.properties.bind),
                        );
                    }
                }
            });
        });
    });

    return cloneSidePanelLayout;
};

/**
 * It merges the columns defined by the base page and the columns provided by the extension.
 *  */
export const mergeColumnPropertyDefinitions = <T extends NestedFieldTypes = NestedFieldTypes>(
    sourcePackage?: string,
    currentColumns: NestedField<any, T>[] = [],
    columnExtensions: NestedExtensionField<any, T>[] = [],
): NestedField<any, T>[] => {
    const columns = [...currentColumns];
    columnExtensions.forEach(extensionColumnDef => {
        insertColumn(
            columns,
            {
                ...extensionColumnDef,
                properties: { ...extensionColumnDef.properties, _declaredInExtension: sourcePackage },
            },
            false,
        );
    });
    return columns;
};

export const overrideColumnPropertyDefinitions = <T extends NestedFieldTypes = NestedFieldTypes>(
    sourcePackage: string,
    currentColumns: NestedField<any, T>[] = [],
    columnOverrides: NestedOverrideField<any, T>[] = [],
): NestedField<any, T>[] => {
    const columns: NestedField<any, T>[] = [...currentColumns];
    columnOverrides?.forEach(override => {
        const bind = override?.properties?.bind;
        const valueField = (override?.properties as NestedReferenceProperties)?.valueField;
        const columnToOverride =
            bind &&
            (valueField
                ? columns.find(
                      column =>
                          column?.properties?.bind === bind &&
                          (column?.properties as NestedReferenceProperties)?.valueField === valueField,
                  )
                : columns.find(column => column?.properties?.bind === bind));
        if (columnToOverride) {
            const modifyingExtensions = columnToOverride.properties._modifyingExtensions || {};
            modifyingExtensions[sourcePackage] = override.properties;
            columnToOverride.properties = {
                ...columnToOverride.properties,
                ...override.properties,
                _modifyingExtensions: modifyingExtensions,
            };
            if (override.properties.insertBefore || override.properties.insertAfter) {
                insertColumn(columns, columnToOverride, true);
            }
        }
    });
    return columns;
};

/** Merge the level definition of existing levels */
export const mergeLevelPropertyDefinition = (
    sourcePackage: string,
    currentLevels: Level<any, any>[] = [],
    levelExtensions: ExtensionLevel<any>[] = [],
): Level<any, any>[] => {
    const levels = [...currentLevels];
    levelExtensions.forEach((extensionLevelDefinition: ExtensionLevel<any, any>, i: number) => {
        // We merge the columns by respecting the `insertBefore` or `insertAfter` property, so reuse the logic developed for the table columns
        levels[i].columns = mergeColumnPropertyDefinitions(
            sourcePackage,
            levels[i]?.columns || [],
            extensionLevelDefinition.columns,
        );
        levels[i].columns = overrideColumnPropertyDefinitions<GridNestedFieldTypes>(
            sourcePackage,
            levels[i]?.columns || [],
            extensionLevelDefinition.columnOverrides,
        );
        levels[i].dropdownActions = [
            ...(levels[i]?.dropdownActions || []),
            ...(extensionLevelDefinition.dropdownActions || []),
        ];
    });

    return levels;
};

export const insertExtendedActions = <
    CT extends ScreenExtension<CT>,
    T extends Array<PageActionControlObject<CT> | MenuSeparatorResult>,
>(
    context: CT,
    baseActions: T,
    extendedActions: Array<PageActionControlObject<CT> | MenuSeparatorResult> = [],
): T => {
    const newActions = [...baseActions];

    extendedActions.forEach(extendedAction => {
        const { insertBefore, insertAfter } = extendedAction;

        let insertionIndex = newActions.length;

        const targetFn = insertBefore || insertAfter;
        const offset = insertBefore ? 0 : 1;

        if (targetFn) {
            const target =
                typeof targetFn === 'string'
                    ? newActions.find((action: PageActionControlObject<CT> | MenuSeparatorResult) => {
                          return action.id === targetFn;
                      })
                    : targetFn.call(context);

            if (target && target.id) {
                const index = newActions.findIndex(
                    baseAction => typeof baseAction === 'object' && 'id' in baseAction && baseAction.id === target.id,
                );
                if (index !== -1) {
                    insertionIndex = index + offset;
                }
            }
        }
        newActions.splice(insertionIndex, 0, extendedAction);
    });

    return newActions as T;
};

export const overrideExtendedProperties = <T extends ComponentKey>(
    nodeTypes: Dict<FormattedNodeDetails>,
    elementId: string,
    pageMetadata: PageMetadata,
    properties: DecoratorProperties<T>,
    componentType: ComponentKey,
): DecoratorProperties<T> => {
    const decoratorProperties = { ...properties, _modifyingExtensions: {} } as any;
    pageMetadata.extensionOverrideThunks[elementId]?.forEach(overrideThunk => {
        const overrideThunkProps = overrideThunk();
        const declaredInExtension = overrideThunkProps._declaredInExtension;
        const orderedPropertyNames = [...without(objectKeys(overrideThunkProps), 'columnOverrides'), 'columnOverrides'];
        orderedPropertyNames.forEach(propertyName => {
            const propValue = overrideThunkProps[propertyName];
            if (propertyName.indexOf('After') !== -1 && typeof propValue === 'function') {
                if (decoratorProperties[propertyName]) {
                    decoratorProperties[propertyName].push(propValue);
                } else {
                    decoratorProperties[propertyName] = [propValue];
                }
            } else if (propertyName === 'columns' || propertyName === 'fields') {
                // We handle columns in a different way, this property allows the addition of new columns, not replaces the existing columns.
                decoratorProperties[propertyName] = mergeColumnPropertyDefinitions(
                    declaredInExtension,
                    decoratorProperties[propertyName],
                    propValue,
                );
            } else if (propertyName === 'columnOverrides') {
                if ((decoratorProperties as TableDecoratorProperties)?.columns && propValue) {
                    (decoratorProperties as TableDecoratorProperties).columns = overrideColumnPropertyDefinitions(
                        declaredInExtension,
                        (decoratorProperties as TableDecoratorProperties).columns,
                        propValue,
                    );
                }
            } else if (propertyName === 'fieldOverrides') {
                if ((decoratorProperties as DetailListDecoratorProperties)?.fields && propValue) {
                    (decoratorProperties as DetailListDecoratorProperties).fields = overrideColumnPropertyDefinitions(
                        declaredInExtension,
                        (decoratorProperties as DetailListDecoratorProperties).fields,
                        propValue,
                    );
                }
            } else if (propertyName === 'mobileCard') {
                decoratorProperties[propertyName] = mergeMobileCards(decoratorProperties[propertyName], propValue);
            } else if (propertyName === 'levels') {
                // Nested Grid levels are also special, we need to loop through all levels and merge the columns.
                decoratorProperties[propertyName] = mergeLevelPropertyDefinition(
                    declaredInExtension,
                    decoratorProperties[propertyName],
                    propValue,
                );
            } else if (propertyName === 'dropdownActions' || propertyName === 'inlineActions') {
                decoratorProperties[propertyName] = mergeTableActions(
                    decoratorProperties[propertyName] || [],
                    propValue,
                );
            } else if (propertyName === '_declaredInExtension' && declaredInExtension) {
                decoratorProperties._modifyingExtensions[declaredInExtension] = overrideThunkProps;
            } else {
                decoratorProperties[propertyName] = propValue;
            }
        });
    });

    const decoratorPropertiesWithColumnsAndNode = decoratorProperties as TableProperties;

    if (
        decoratorPropertiesWithColumnsAndNode.node &&
        decoratorPropertiesWithColumnsAndNode.columns &&
        pageMetadata.customizations
    ) {
        addCustomColumnToNestedFields(
            String(decoratorPropertiesWithColumnsAndNode.node),
            nodeTypes,
            decoratorPropertiesWithColumnsAndNode.columns,
            elementId,
            componentType,
            false,
            pageMetadata.customizations,
        );

        // We check if the columns have nested columns, and if so, we add the custom fields to the nested columns as well.
        decoratorPropertiesWithColumnsAndNode.columns.forEach(column => {
            const columnProperties = column.properties as NestedReferenceExtensionProperties<any>;
            if (componentsWithLookup.includes(column.type) && columnProperties.columns) {
                addCustomColumnToNestedFields(
                    String(columnProperties.node),
                    nodeTypes,
                    columnProperties.columns,
                    columnProperties.bind,
                    column.type,
                    false,
                    pageMetadata.customizations,
                );
            }
        });
    }

    return decoratorProperties;
};

/**
 * Merges two mobile card configurations, handling null and explicitly-undefined values as deletions
 *
 * @param {CardDefinition} source - The original mobile card configuration
 * @param {CardExtensionDefinition} extension - The extended mobile card configuration
 * @returns {CardDefinition} The merged mobile card configuration, with null and undefined values from extension removed
 */

export const mergeMobileCards = (source: CardDefinition, extension: CardExtensionDefinition): CardDefinition => {
    const clonedSource = lodash.cloneDeep(source);
    const clonedExtension = lodash.cloneDeep(extension);
    const mergedDefinition = lodash.assign({}, clonedSource, clonedExtension);
    const keysToRemove = lodash.keys(lodash.pickBy(clonedExtension, lodash.isNil));
    return lodash.omit(mergedDefinition, keysToRemove) as CardDefinition;
};
