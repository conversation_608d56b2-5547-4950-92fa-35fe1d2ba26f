import { cloneDeep } from 'lodash';
import type { ScreenBase } from '../service/screen-base';
import type { ScreenExtension } from '../types';
import { deepFreeze } from '../utils/common-util';
import { resolveByValue } from '../utils/resolve-value-utils';
import type {
    ValueOrCallbackKeysExtractor,
    ValueOrCallbackReturnExtractor,
    ValueOrCallbackWithFieldValue,
} from '../utils/types';
import type { PageActionProperties } from './control-objects';
import type { HasAccessRights } from './field/traits';
import { ContainerControlObjectResolvedProperty } from './property-decorators/control-object-resolved-property-decorator';
import type { ComponentKey } from './types';
import type { Dict } from '@sage/xtrem-shared';

export interface ComponentProperties<CT extends ScreenExtension<CT> = ScreenBase> extends HasAccessRights {
    /**
     * Whether the HTML element is disabled or not. Defaults to false
     *
     * The difference with readOnly is that disabled suggests that the field is not editable
     * for some validation reason (e.g. a button which can't be clicked due to validation errors)
     */
    isDisabled?: ValueOrCallbackWithFieldValue<CT, boolean>;

    /** The type of the corresponding control object, for internal use only. */
    _controlObjectType?: ComponentKey | null;

    _declaredInExtension?: string;

    _modifyingExtensions?: Dict<any>;
}

/**
 * Any element than can be placed inside a page and can be interacted with (i.e. retrieving
 * and/or setting element's properties values)
 */
export abstract class BaseControlObject<CT extends ScreenExtension<CT>, S extends ComponentProperties<CT>> {
    static readonly defaultUiProperties: Partial<ComponentProperties> = {};

    constructor(
        protected readonly screenId: string,
        protected readonly elementId: string,
        protected readonly _getUiComponentProperties: (screenId: string, elementId: string) => S,
        protected readonly _setUiComponentProperties: (
            screenId: string,
            elementId: string,
            state: PageActionProperties<ScreenExtension<CT>>,
        ) => void,
        protected readonly _componentKey: ComponentKey,
    ) {}

    protected get uiComponentProperties(): S {
        return this._getUiComponentProperties(this.screenId, this.elementId);
    }

    /**
     * Get and clone the property value to ensure immutability. Function properties are not cloned.
     * @param propertyName
     * @returns deep cloned property value
     */
    protected getUiComponentProperty<K extends keyof S>(propertyName: K): S[K] {
        const properties = this._getUiComponentProperties(this.screenId, this.elementId);
        const property = properties[propertyName];
        return typeof property === 'function' ? property : cloneDeep(property);
    }

    protected getResolvedProperty<K extends ValueOrCallbackKeysExtractor<S>>(
        key: K,
        skipHexFormat = true,
    ): ValueOrCallbackReturnExtractor<S, K> {
        return resolveByValue({
            screenId: this.screenId,
            propertyValue: this.getUiComponentProperty(key),
            skipHexFormat,
            rowValue: null,
            fieldValue: null,
        });
    }

    /**
     * Clone and then set the property value to ensure immutability. Function properties are not cloned.
     * @param propertyName
     * @param propertyValue
     */
    protected setUiComponentProperties<K extends keyof S>(propertyName: K, propertyValue: S[K]): void {
        const state = { ...this.uiComponentProperties };
        state[propertyName] = typeof propertyValue === 'function' ? propertyValue : cloneDeep(propertyValue);
        this._setUiComponentProperties(this.screenId, this.elementId, state);
    }

    /**
     * Only returns a READ-ONLY copy of the properties. Any updates to the object will not affect to the framework.
     */
    public get properties(): S {
        return deepFreeze(cloneDeep(this.uiComponentProperties || {}));
    }

    /** Id of the control object */
    get id(): string {
        return this.elementId;
    }

    @ContainerControlObjectResolvedProperty<ComponentProperties<CT>, BaseControlObject<CT, any>>()
    /**
     * Whether the HTML element is editable (disabled = false) or not (disabled = true)
     *
     * The difference with readOnly is that disabled suggests that the field is not editable
     * for some validation reason (e.g. a button which can be clicked due to validation errors)
     */
    isDisabled?: boolean;

    get componentType(): ComponentKey {
        return this._componentKey;
    }
}
