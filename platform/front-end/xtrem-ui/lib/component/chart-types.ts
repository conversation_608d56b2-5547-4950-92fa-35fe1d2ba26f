import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../service/screen-base';
import type { NestedField } from './nested-fields';
import type { FieldKey } from './types';

export interface CommonChartOptions<CT extends ScreenBase, NodeType extends ClientNode = any> {
    enableMouseTracking?: boolean;
    series: NestedField<CT, FieldKey.Numeric>[];
    xAxis: NestedField<CT, FieldKey.Numeric | FieldKey.Text | FieldKey.Date, NodeType>;
}

export enum ChartTypes {
    Bar = 'bar',
    Line = 'line',
    Pie = 'pie',
}

export interface ChartDeclaration<CT extends ScreenBase, NodeType extends ClientNode = any>
    extends CommonChartOptions<CT, NodeType> {
    type: ChartTypes;
}

export const line = <CT extends ScreenBase, NodeType extends ClientNode = any>(
    options: CommonChartOptions<CT, NodeType>,
): ChartDeclaration<CT, NodeType> => {
    return {
        enableMouseTracking: options.enableMouseTracking,
        series: options.series,
        type: ChartTypes.Line,
        xAxis: options.xAxis,
    };
};

export const bar = <CT extends ScreenBase, NodeType extends ClientNode = any>(
    options: CommonChartOptions<CT, NodeType>,
): ChartDeclaration<CT, NodeType> => {
    return {
        enableMouseTracking: options.enableMouseTracking,
        series: options.series,
        type: ChartTypes.Bar,
        xAxis: options.xAxis,
    };
};

export const pie = <CT extends ScreenBase, NodeType extends ClientNode = any>(
    options: CommonChartOptions<CT, NodeType>,
): ChartDeclaration<CT, NodeType> => {
    return {
        enableMouseTracking: options.enableMouseTracking,
        series: options.series,
        type: ChartTypes.Pie,
        xAxis: options.xAxis,
    };
};
