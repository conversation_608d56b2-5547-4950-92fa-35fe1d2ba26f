import { resolveByValue } from '../../utils/resolve-value-utils';
import type { BaseControlObject } from '../base-control-object';
import type { ReadonlyFieldControlObject, ReadonlyFieldProperties } from '../readonly-field-control-object';

export interface FieldControlObjectResolvedPropertyHooks<CT> {
    onSet?: (this: CT, screenId: string, elementId: string, value: any) => void;
    onGet?: (this: CT, screenId: string, elementId: string) => void;
}

export const FieldControlObjectResolvedProperty =
    <
        PropertiesType extends Object,
        ConstructorType extends BaseControlObject<any, PropertiesType & ReadonlyFieldProperties<any>>,
    >(
        hooks?: FieldControlObjectResolvedPropertyHooks<ConstructorType>,
    ) =>
    (controlObjectConstructor: ConstructorType, name: keyof PropertiesType): void => {
        Object.defineProperty(controlObjectConstructor, name, {
            get(this: ConstructorType) {
                if (hooks?.onGet) {
                    hooks.onGet.apply(this, [this.screenId, this.elementId]);
                }
                return resolveByValue({
                    fieldValue: (this as unknown as ReadonlyFieldControlObject<any, any, any>).value,
                    propertyValue: this.getUiComponentProperty(name),
                    skipHexFormat: true,
                    screenId: this.screenId,
                    rowValue: null, // The decorators are not used in nested context directly, therefore it's fine to pass null as the row value
                });
            },
            set(this: ConstructorType, value) {
                if (hooks?.onSet) {
                    hooks.onSet.apply(this, [this.screenId, this.elementId, value]);
                }
                this.setUiComponentProperties(name, value);
            },
            enumerable: true,
            configurable: true,
        });
    };

export const ContainerControlObjectResolvedProperty =
    <PropertiesType extends Object, ConstructorType extends BaseControlObject<any, any>>() =>
    (controlObjectConstructor: ConstructorType, name: keyof PropertiesType): void => {
        Object.defineProperty(controlObjectConstructor, name, {
            get(this: ConstructorType) {
                return resolveByValue({
                    propertyValue: this.getUiComponentProperty(name),
                    skipHexFormat: true,
                    screenId: this.screenId,
                    rowValue: null, // The decorators are not used in nested context directly, therefore it's fine to pass null as the row value
                });
            },
            set(this: ConstructorType, value) {
                this.setUiComponentProperties(name, value);
            },
            enumerable: true,
            configurable: true,
        });
    };
