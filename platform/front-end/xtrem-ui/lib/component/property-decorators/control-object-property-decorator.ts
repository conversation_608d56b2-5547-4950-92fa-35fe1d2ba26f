import type { BaseControlObject } from '../base-control-object';

export const ControlObjectProperty =
    <PropertiesType extends Object, ConstructorType extends BaseControlObject<any, PropertiesType>>() =>
    (controlObjectConstructor: ConstructorType, name: keyof PropertiesType): void => {
        Object.defineProperty(controlObjectConstructor, name, {
            get(this: ConstructorType) {
                return this.getUiComponentProperty(name);
            },
            set(this: ConstructorType, value) {
                this.setUiComponentProperties(name, value);
            },
            enumerable: true,
            configurable: true,
        });
    };
