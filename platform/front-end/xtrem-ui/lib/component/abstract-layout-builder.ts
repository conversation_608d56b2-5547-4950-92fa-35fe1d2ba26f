import type { Dict } from '@sage/xtrem-shared';
import type { PageArticleItem } from '../service/layout-types';
import type { ResponsiveProperties } from './abstract-ui-control-object';
import type { ComponentKey, ContainerWidth, DecoratorTarget, FieldWidth, LayoutContent, MetadataProps } from './types';
import type { DataTypeDetails, FormattedNodeDetails } from '../service/metadata-types';

export interface DefaultFieldLayoutProps extends ResponsiveProperties {
    elementId: string;
    containerType: string;
    width?: FieldWidth;
    isFullWidth: boolean;
}

export abstract class AbstractLayoutBuilder<T extends ComponentKey> {
    public layout: LayoutContent<T>;

    constructor(
        public target: DecoratorTarget<T>,
        public elementId: string,
        public readonly nodeTypes: Dict<FormattedNodeDetails>,
        public readonly dataTypes: Dict<DataTypeDetails>,
        public readonly metadata: MetadataProps<T>,
    ) {}

    public readonly build = (): this => {
        return this.buildLayout();
    };

    protected abstract buildLayout: () => this;

    protected buildContainerLayout(
        elementId: string,
        category: string,
        isHiddenMobile = false,
        isHiddenDesktop = false,
        width?: ContainerWidth,
    ): Partial<PageArticleItem> {
        return {
            $containerId: elementId,
            $isHiddenMobile: isHiddenMobile,
            $isHiddenDesktop: isHiddenDesktop,
            $category: category,
            $layout: { $items: [] },
            $columnWidth: width,
        };
    }

    protected buildFieldLayout({
        elementId,
        containerType = 'block',
        isHiddenDesktop,
        isHiddenMobile,
        width,
        isFullWidth,
    }: Partial<DefaultFieldLayoutProps>): PageArticleItem {
        return {
            $bind: elementId!, // IG: We need to maintain the $bind naming because legacy authoring tool on Syracuse
            $isHiddenMobile: isHiddenMobile,
            $isHiddenDesktop: isHiddenDesktop,
            $containerType: containerType,
            $columnWidth: width,
            $isFullWidth: Boolean(isFullWidth),
        };
    }
}
