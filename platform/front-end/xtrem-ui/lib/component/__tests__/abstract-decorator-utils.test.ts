import { FieldKey, type Dict } from '@sage/xtrem-shared';
import {
    mergeIntoSidebarLayout,
    mergeColumnPropertyDefinitions,
    mergeLevelPropertyDefinition,
    mergeMobileCards,
    overrideColumnPropertyDefinitions,
} from '../abstract-decorator-utils';
import type { NestedReferenceProperties } from '../field/reference/reference-types';
import * as nestedFields from '../nested-fields';
import * as nestedFieldExtension from '../nested-fields-extensions';
import * as nestedFieldOverrides from '../nested-fields-overrides';
import type { SidebarSectionDefinition } from '../table-sidebar/table-sidebar-types';

describe('abstract-decorator-utils', () => {
    let baseColumns: () => nestedFields.NestedField<any, nestedFields.NestedFieldTypes>[] = () => [];
    beforeAll(() => {
        baseColumns = () => [
            nestedFields.text({ bind: 'name', title: 'Name' }),
            nestedFields.reference<any, any>({
                bind: 'country',
                valueField: 'isoCode',
                title: 'Country code',
                node: '@sage/xtrem-test/AnyNode',
            }),
            nestedFields.reference<any, any>({
                bind: 'country',
                valueField: 'name',
                title: 'Country of origin',
                node: '@sage/xtrem-test/AnyNode',
            }),
            nestedFields.date<any, any>({ bind: 'birthDate', title: 'Birth Date' }),
        ];
    });

    describe('mergeColumnPropertyDefinitions', () => {
        it('should add additional columns to the end', () => {
            const additionalColumns = [
                nestedFieldExtension.text<any, any>({ bind: 'myExtensionColumn1', title: 'Extension 1' }),
                nestedFieldExtension.numeric<any, any>({ bind: 'myExtensionColumn2', title: 'Extension 2' }),
            ];

            const result = mergeColumnPropertyDefinitions(
                '@sage/xtrem-test-extension',
                baseColumns(),
                additionalColumns,
            );

            expect(result).toEqual([
                expect.objectContaining({
                    properties: expect.objectContaining({
                        bind: 'name',
                        title: 'Name',
                        _controlObjectType: FieldKey.Text,
                    }),
                }),
                expect.objectContaining({
                    properties: expect.objectContaining({
                        bind: 'country',
                        title: 'Country code',
                        valueField: 'isoCode',
                        node: '@sage/xtrem-test/AnyNode',
                        _controlObjectType: FieldKey.Reference,
                    }),
                }),
                expect.objectContaining({
                    properties: expect.objectContaining({
                        bind: 'country',
                        title: 'Country of origin',
                        valueField: 'name',
                        node: '@sage/xtrem-test/AnyNode',
                        _controlObjectType: FieldKey.Reference,
                    }),
                }),
                expect.objectContaining({
                    properties: expect.objectContaining({
                        bind: 'birthDate',
                        title: 'Birth Date',
                        _controlObjectType: 'Date',
                    }),
                }),
                expect.objectContaining({
                    properties: expect.objectContaining({
                        bind: 'myExtensionColumn1',
                        title: 'Extension 1',
                        _controlObjectType: FieldKey.Text,
                    }),
                }),
                expect.objectContaining({
                    properties: expect.objectContaining({
                        bind: 'myExtensionColumn2',
                        title: 'Extension 2',
                        _controlObjectType: FieldKey.Numeric,
                    }),
                }),
            ]);
        });

        it('should add additional columns to the middle', () => {
            const additionalColumns = [
                nestedFieldExtension.text<any, any>({
                    bind: 'myExtensionColumn1',
                    title: 'Extension 1',
                    insertBefore: 'name',
                }),
                nestedFieldExtension.numeric<any, any>({
                    bind: 'myExtensionColumn2',
                    title: 'Extension 2',
                    insertBefore: 'country',
                }),
            ];

            const result = mergeColumnPropertyDefinitions(
                '@sage/xtrem-test-extension',
                baseColumns(),
                additionalColumns,
            );

            expect(result).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'myExtensionColumn1',
                            title: 'Extension 1',
                            insertBefore: 'name',
                            _controlObjectType: FieldKey.Text,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'name',
                            title: 'Name',
                            _controlObjectType: FieldKey.Text,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'myExtensionColumn2',
                            title: 'Extension 2',
                            insertBefore: 'country',
                            _controlObjectType: FieldKey.Numeric,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'country',
                            title: 'Country code',
                            valueField: 'isoCode',
                            node: '@sage/xtrem-test/AnyNode',
                            _controlObjectType: FieldKey.Reference,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'country',
                            title: 'Country of origin',
                            valueField: 'name',
                            node: '@sage/xtrem-test/AnyNode',
                            _controlObjectType: FieldKey.Reference,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'birthDate',
                            title: 'Birth Date',
                            _controlObjectType: 'Date',
                        }),
                    }),
                ]),
            );
        });

        it('should add additional column before a specific reference field, using __ convention', () => {
            const additionalColumns = [
                nestedFieldExtension.text<any, any>({
                    bind: 'myExtensionColumn1',
                    title: 'Extension 1',
                    insertBefore: 'name',
                }),
                nestedFieldExtension.numeric<any, any>({
                    bind: 'myExtensionColumn2',
                    title: 'Extension 2',
                    insertBefore: 'country__name',
                }),
            ];

            const result = mergeColumnPropertyDefinitions(
                '@sage/xtrem-test-extension',
                baseColumns(),
                additionalColumns,
            );

            expect(result).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'myExtensionColumn1',
                            title: 'Extension 1',
                            insertBefore: 'name',
                            _controlObjectType: FieldKey.Text,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'name',
                            title: 'Name',
                            _controlObjectType: FieldKey.Text,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'country',
                            title: 'Country code',
                            valueField: 'isoCode',
                            node: '@sage/xtrem-test/AnyNode',
                            _controlObjectType: FieldKey.Reference,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'myExtensionColumn2',
                            title: 'Extension 2',
                            insertBefore: 'country__name',
                            _controlObjectType: FieldKey.Numeric,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'country',
                            title: 'Country of origin',
                            valueField: 'name',
                            node: '@sage/xtrem-test/AnyNode',
                            _controlObjectType: FieldKey.Reference,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'birthDate',
                            title: 'Birth Date',
                            _controlObjectType: 'Date',
                        }),
                    }),
                ]),
            );
        });

        it('should add the additional column to the end if no match was found for the insert before item', () => {
            const additionalColumns = [
                nestedFieldExtension.text<any, any>({
                    bind: 'myExtensionColumn1',
                    title: 'Extension 1',
                    insertBefore: 'name',
                }),
                nestedFieldExtension.numeric<any, any>({
                    bind: 'myExtensionColumn2',
                    title: 'Extension 2',
                    insertBefore: 'someNonExistingField',
                }),
            ];

            const result = mergeColumnPropertyDefinitions(
                '@sage/xtrem-test-extension',
                baseColumns(),
                additionalColumns,
            );

            expect(result).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'myExtensionColumn1',
                            title: 'Extension 1',
                            insertBefore: 'name',
                            _controlObjectType: FieldKey.Text,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'name',
                            title: 'Name',
                            _controlObjectType: FieldKey.Text,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'country',
                            title: 'Country code',
                            valueField: 'isoCode',
                            node: '@sage/xtrem-test/AnyNode',
                            _controlObjectType: FieldKey.Reference,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'country',
                            title: 'Country of origin',
                            valueField: 'name',
                            node: '@sage/xtrem-test/AnyNode',
                            _controlObjectType: FieldKey.Reference,
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'birthDate',
                            title: 'Birth Date',
                            _controlObjectType: 'Date',
                        }),
                    }),
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'myExtensionColumn2',
                            title: 'Extension 2',
                            insertBefore: 'someNonExistingField',
                            _controlObjectType: FieldKey.Numeric,
                        }),
                    }),
                ]),
            );
        });
    });

    describe('mergeLevelPropertyDefinition', () => {
        const baseLevels = () => [
            {
                columns: [
                    nestedFields.text<any, any>({ bind: 'name', title: 'Name' }),
                    nestedFields.reference<any, any>({
                        bind: 'country',
                        valueField: 'isoCode',
                        title: 'Country code',
                        node: '@sage/xtrem-test/AnyNode',
                    }),
                    nestedFields.reference<any, any>({
                        bind: 'country',
                        valueField: 'name',
                        title: 'Country of origin',
                        node: '@sage/xtrem-test/AnyNode',
                    }),
                    nestedFields.date<any, any>({ bind: 'birthDate', title: 'Birth Date' }),
                ],
                node: '@sage/xtrem-test/AnyNode',
                childProperty: 'invoices',
                dropdownActions: [
                    { title: 'Level 1 Action 1', onClick: jest.fn() },
                    { title: 'Level 1 Action 2', onClick: jest.fn() },
                ],
            },
            {
                node: '@sage/xtrem-test/AnyOtherNode',
                columns: [
                    nestedFields.text<any, any>({ bind: 'number', title: 'Invoice Number' }),
                    nestedFields.date<any, any>({ bind: 'date', title: 'Issue Date' }),
                ],
            },
        ];

        it('should add and override additional columns to various levels', () => {
            const levelExtensions = [
                {
                    columns: [
                        nestedFieldExtension.text<any, any>({ bind: 'myExtensionColumn1', title: 'Extension 1' }),
                        nestedFieldExtension.text<any, any>({ bind: 'myExtensionColumn2', title: 'Extension 2' }),
                    ],
                },
                {
                    columns: [
                        nestedFieldExtension.text<any, any>({ bind: 'myExtensionColumn3', title: 'Extension 3' }),
                    ],
                    columnOverrides: [
                        nestedFieldOverrides.date<any, any>({ bind: 'date', title: 'Issue Date override' }),
                    ],
                },
            ];
            const result = mergeLevelPropertyDefinition('@sage/xtrem-test-extension', baseLevels(), levelExtensions);

            expect(result).toEqual([
                {
                    node: '@sage/xtrem-test/AnyNode',
                    childProperty: 'invoices',
                    dropdownActions: expect.anything(),
                    columns: [
                        expect.objectContaining({
                            properties: expect.objectContaining({
                                bind: 'name',
                                title: 'Name',
                                _controlObjectType: FieldKey.Text,
                            }),
                        }),
                        expect.objectContaining({
                            properties: expect.objectContaining({
                                bind: 'country',
                                title: 'Country code',
                                valueField: 'isoCode',
                                node: '@sage/xtrem-test/AnyNode',
                                _controlObjectType: FieldKey.Reference,
                            }),
                        }),
                        expect.objectContaining({
                            properties: expect.objectContaining({
                                bind: 'country',
                                title: 'Country of origin',
                                valueField: 'name',
                                node: '@sage/xtrem-test/AnyNode',
                                _controlObjectType: FieldKey.Reference,
                            }),
                        }),
                        expect.objectContaining({
                            properties: expect.objectContaining({
                                bind: 'birthDate',
                                title: 'Birth Date',
                                _controlObjectType: 'Date',
                            }),
                        }),
                        expect.objectContaining({
                            properties: expect.objectContaining({
                                bind: 'myExtensionColumn1',
                                title: 'Extension 1',
                                _controlObjectType: FieldKey.Text,
                            }),
                        }),
                        expect.objectContaining({
                            properties: expect.objectContaining({
                                bind: 'myExtensionColumn2',
                                title: 'Extension 2',
                                _controlObjectType: FieldKey.Text,
                            }),
                        }),
                    ],
                },
                {
                    node: '@sage/xtrem-test/AnyOtherNode',
                    dropdownActions: expect.anything(),
                    columns: [
                        expect.objectContaining({
                            properties: expect.objectContaining({
                                bind: 'number',
                                title: 'Invoice Number',
                                _controlObjectType: FieldKey.Text,
                            }),
                        }),
                        expect.objectContaining({
                            properties: expect.objectContaining({
                                bind: 'date',
                                title: 'Issue Date override',
                                _controlObjectType: 'Date',
                            }),
                        }),
                        expect.objectContaining({
                            properties: expect.objectContaining({
                                bind: 'myExtensionColumn3',
                                title: 'Extension 3',
                                _controlObjectType: FieldKey.Text,
                            }),
                        }),
                    ],
                },
            ]);
        });
        it('should merge dropdown actions on all levels', () => {
            const levelExtensions = [
                {
                    columns: [
                        nestedFieldExtension.text<any, any>({ bind: 'myExtensionColumn1', title: 'Extension 1' }),
                        nestedFieldExtension.text<any, any>({ bind: 'myExtensionColumn2', title: 'Extension 2' }),
                    ],
                    dropdownActions: [{ title: 'Level 1 Extension Action 1', onClick: jest.fn() }],
                },
                {
                    columns: [
                        nestedFieldExtension.text<any, any>({ bind: 'myExtensionColumn3', title: 'Extension 3' }),
                    ],
                    dropdownActions: [
                        { title: 'Level 2 Extension Action 1', onClick: jest.fn() },
                        { title: 'Level 2 Extension Action 2', onClick: jest.fn() },
                    ],
                },
            ];
            const result = mergeLevelPropertyDefinition('@sage/xtrem-test-extension', baseLevels(), levelExtensions);

            expect(result).toEqual([
                {
                    node: '@sage/xtrem-test/AnyNode',
                    childProperty: 'invoices',
                    dropdownActions: [
                        expect.objectContaining({ title: 'Level 1 Action 1' }),
                        expect.objectContaining({ title: 'Level 1 Action 2' }),
                        expect.objectContaining({ title: 'Level 1 Extension Action 1' }),
                    ],
                    columns: expect.anything(),
                },
                {
                    node: '@sage/xtrem-test/AnyOtherNode',
                    dropdownActions: [
                        expect.objectContaining({ title: 'Level 2 Extension Action 1' }),
                        expect.objectContaining({ title: 'Level 2 Extension Action 2' }),
                    ],
                    columns: expect.anything(),
                },
            ]);
        });
    });

    describe('overrideColumnPropertyDefinitions', () => {
        it('should not override unexisting columns', () => {
            // typescript will also prevent this in compilation
            const overrideColumns = [
                nestedFieldOverrides.text<any, any>({ bind: 'unexisting-bind', title: 'override' }),
            ];
            const result = overrideColumnPropertyDefinitions(
                '@sage/xtrem-test-extension',
                baseColumns(),
                overrideColumns,
            );
            expect(result).toEqual(baseColumns());
        });

        it('should override existing nested columns', () => {
            const overrideColumns = [
                nestedFieldOverrides.text<any, any>({ bind: 'name', title: 'override name title' }),
            ];
            const result = overrideColumnPropertyDefinitions(
                '@sage/xtrem-test-extension',
                baseColumns(),
                overrideColumns,
            );
            expect(result).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'name',
                            title: 'override name title',
                        }),
                    }),
                ]),
            );
        });

        it('should override existing reference nested columns', () => {
            const overrideColumns = [
                nestedFieldOverrides.reference<any, any>({
                    bind: 'country',
                    valueField: 'name',
                    title: 'override country name title',
                }),
            ];
            const result = overrideColumnPropertyDefinitions(
                '@sage/xtrem-test-extension',
                baseColumns(),
                overrideColumns,
            );
            expect(result).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        properties: expect.objectContaining({
                            bind: 'country',
                            valueField: 'name',
                            title: 'override country name title',
                        }),
                    }),
                ]),
            );
        });

        it('should apply insertBefore on overrides', () => {
            const overrideColumns = [
                nestedFieldOverrides.date<any, any>({ bind: 'birthDate', insertBefore: 'country__isoCode' }),
            ];
            const result = overrideColumnPropertyDefinitions(
                '@sage/xtrem-test-extension',
                baseColumns(),
                overrideColumns,
            );
            expect(result[0].properties.bind).toBe('name');
            expect(result[1].properties.bind).toBe('birthDate');
            expect(result[2].properties.bind).toBe('country');
            expect((result[2].properties as NestedReferenceProperties).valueField).toBe('isoCode');
            expect(result[3].properties.bind).toBe('country');
            expect((result[3].properties as NestedReferenceProperties).valueField).toBe('name');
        });
    });

    describe('override mobileCard', () => {
        it('should override mobileCard, updating', () => {
            const source = {
                title: nestedFields.text<any>({ bind: 'someField1' }),
                titleRight: nestedFields.text<any>({ bind: 'someField2' }),
                line2: nestedFields.text<any>({ bind: 'someField3' }),
                line2Right: nestedFields.text<any>({ bind: 'someField4' }),
            };

            const extension = {
                titleRight: nestedFields.text<any>({ bind: 'someField5' }),
            };

            const result = mergeMobileCards(source, extension);

            expect(result).toEqual({
                title: source.title,
                titleRight: extension.titleRight,
                line2: source.line2,
                line2Right: source.line2Right,
            });
        });

        it('should override mobileCard, removing', () => {
            const source = {
                title: nestedFields.text<any>({ bind: 'someField1' }),
                titleRight: nestedFields.text<any>({ bind: 'someField2' }),
                line2: nestedFields.text<any>({ bind: 'someField3' }),
                line2Right: nestedFields.text<any>({ bind: 'someField4' }),
            };

            const extension = {
                line2Right: null,
            };

            const result = mergeMobileCards(source, extension);

            expect(result).toEqual({
                title: source.title,
                titleRight: source.titleRight,
                line2: source.line2,
            });
        });

        it('should override mobileCard, adding', () => {
            const source = {
                title: nestedFields.text<any>({ bind: 'someField1' }),
                titleRight: nestedFields.text<any>({ bind: 'someField2' }),
                line2: nestedFields.text<any>({ bind: 'someField3' }),
                line2Right: nestedFields.text<any>({ bind: 'someField4' }),
            };

            const extension = {
                line3Right: nestedFields.text<any>({ bind: 'someField6' }),
            };

            const result = mergeMobileCards(source, extension);

            expect(result).toEqual({
                title: source.title,
                titleRight: source.titleRight,
                line2: source.line2,
                line2Right: source.line2Right,
                line3Right: extension.line3Right,
            });
        });
    });

    describe('mergeIntoSidebarLayout', () => {
        it('should merge custom fields into the sidePanelLayout', () => {
            const sidePanelLayout: Dict<SidebarSectionDefinition<any, any>> = {
                section1: {
                    blocks: {
                        block1: {
                            fields: ['field1', 'field2'],
                        },
                    },
                    title: '',
                },
                section2: {
                    blocks: {
                        block2: {
                            fields: ['field3', 'field4'],
                        },
                    },
                    title: '',
                },
            };
            const customFields: any[] = [
                {
                    properties: {
                        bind: 'customField1',
                        insertBefore: 'field2',
                    },
                },
                {
                    properties: {
                        bind: 'customField2',
                        insertAfter: 'field3',
                    },
                },
            ];
            const expected = {
                section1: {
                    blocks: {
                        block1: {
                            fields: ['field1', 'customField1', 'field2'],
                        },
                    },
                    title: '',
                },
                section2: {
                    blocks: {
                        block2: {
                            fields: ['field3', 'customField2', 'field4'],
                        },
                    },
                    title: '',
                },
            };
            const result = mergeIntoSidebarLayout(sidePanelLayout, customFields);
            expect(result).toEqual(expected);
        });

        it('should not modify the original sidePanelLayout', () => {
            const sidePanelLayout: Dict<SidebarSectionDefinition<any, any>> = {
                section1: {
                    blocks: {
                        block1: {
                            fields: ['field1', 'field2'],
                        },
                    },
                    title: '',
                },
            };
            const customFields: any[] = [
                {
                    properties: {
                        bind: 'customField1',
                        insertBefore: 'field2',
                    },
                },
            ];
            const result = mergeIntoSidebarLayout(sidePanelLayout, customFields);
            expect(result).not.toBe(sidePanelLayout);
        });

        it('should handle empty customFields array', () => {
            const sidePanelLayout: Dict<SidebarSectionDefinition<any, any>> = {
                section1: {
                    blocks: {
                        block1: {
                            fields: ['field1', 'field2'],
                        },
                    },
                    title: '',
                },
            };
            const customFields = [];
            const result = mergeIntoSidebarLayout(sidePanelLayout, customFields);
            expect(result).toEqual(sidePanelLayout);
        });

        it('should handle empty sidePanelLayout object', () => {
            const sidePanelLayout: Dict<SidebarSectionDefinition<any, any>> = {};
            const customFields: any[] = [
                {
                    properties: {
                        bind: 'customField1',
                        insertBefore: 'field2',
                    },
                },
            ];
            const result = mergeIntoSidebarLayout(sidePanelLayout, customFields);
            expect(result).toEqual(sidePanelLayout);
        });

        it('should handle customFields with insertBefore not found', () => {
            const sidePanelLayout: Dict<SidebarSectionDefinition<any, any>> = {
                section1: {
                    blocks: {
                        block1: {
                            fields: ['field1', 'field2'],
                        },
                    },
                    title: '',
                },
            };
            const customFields: any[] = [
                {
                    properties: {
                        bind: 'customField1',
                        insertBefore: 'field3',
                    },
                },
            ];
            const result = mergeIntoSidebarLayout(sidePanelLayout, customFields);
            expect(result).toEqual(sidePanelLayout);
        });

        it('should handle customFields with insertAfter not found', () => {
            const sidePanelLayout: Dict<SidebarSectionDefinition<any, any>> = {
                section1: {
                    blocks: {
                        block1: {
                            fields: ['field1', 'field2'],
                        },
                    },
                    title: '',
                },
            };
            const customFields: any[] = [
                {
                    properties: {
                        bind: 'customField1',
                        insertAfter: 'field3',
                    },
                },
            ];
            const result = mergeIntoSidebarLayout(sidePanelLayout, customFields);
            expect(result).toEqual(sidePanelLayout);
        });
    });
});
