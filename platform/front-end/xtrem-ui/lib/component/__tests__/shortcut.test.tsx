import * as React from 'react';
import * as hotkeyService from '../../service/shortcut-service';
import { Shortcut } from '../shortcut';
import { render, cleanup } from '@testing-library/react';

describe('Shortcut', () => {
    const keyCombination: hotkeyService.Key[] = ['escape', 'a'];
    let onClickListener: jest.Mock<any, any> | null = null;
    let subscribeMock: jest.MockInstance<any, any> | null = null;
    let unsubscribeMock: jest.MockInstance<any, any> | null = null;

    const getTestElement = (combination: hotkeyService.Key[]) => {
        return (
            <Shortcut combination={combination}>
                <div onClick={onClickListener!}>Dummy content</div>
            </Shortcut>
        );
    };

    beforeEach(() => {
        onClickListener = jest.fn();
        subscribeMock = jest.spyOn(hotkeyService, 'subscribe').mockReturnValue(1234);
        unsubscribeMock = jest.spyOn(hotkeyService, 'unsubscribe').mockImplementation();
    });

    afterEach(() => {
        cleanup();
        jest.clearAllMocks();
    });

    it('should subscribe with the provided key combination', () => {
        expect(subscribeMock).not.toHaveBeenCalled();
        render(getTestElement(['escape', 'a']));
        expect(subscribeMock).toHaveBeenCalledTimes(1);
        expect(subscribeMock!.mock.calls[0][0]).toEqual(keyCombination);
    });

    it('should unsubscribe with the provided key combination', () => {
        expect(unsubscribeMock).not.toHaveBeenCalled();
        const component = render(getTestElement(['escape', 'a']));
        expect(unsubscribeMock).not.toHaveBeenCalled();
        component.unmount();
        expect(unsubscribeMock).toHaveBeenCalledTimes(1);

        expect(unsubscribeMock!.mock.calls[0][0]).toEqual(1234);
    });

    it('should trigger listener', () => {
        const div: HTMLDivElement = document.createElement('div');
        document.body.appendChild(div);
        expect(subscribeMock).not.toHaveBeenCalled();
        render(getTestElement(['escape', 'a']), { container: div });
        expect(subscribeMock).toHaveBeenCalledTimes(1);

        const callback = subscribeMock!.mock.calls[0][1];
        expect(callback).toBeInstanceOf(Function);
        expect(onClickListener).not.toHaveBeenCalled();
        callback();
        expect(onClickListener).toHaveBeenCalledTimes(1);
    });
});
