import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../__tests__/test-helpers';

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type * as xtremRedux from '../../redux';
import { FieldKey } from '../types';
import { getScreenElement } from '../../service/screen-base-definition';
import { render } from '@testing-library/react';
import ConnectedLabelComponent from '../field/label/label-component';
import ConnectedTextComponent from '../field/text/text-component';
import ConnectedNumericComponent from '../field/numeric/numeric-component';

describe('Property callbacks with page context', () => {
    const screenId = 'TestPage';
    const elementId = 'testElementId';
    let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;
    let screenElement: any;
    let state: xtremRedux.XtremAppState;

    beforeEach(() => {
        state = getMockState();
        const pageDefinition = getMockPageDefinition(screenId);
        state.screenDefinitions[screenId] = pageDefinition;
        screenElement = getScreenElement(pageDefinition) as any;
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('label', () => {
        it('should render with title', () => {
            screenElement.TESTTITLE = 'TESTTITLE';

            addFieldToState(
                FieldKey.Label,
                state,
                screenId,
                elementId,
                {
                    title() {
                        expect(this).toBe(screenElement);
                        return (this as any).TESTTITLE;
                    },
                },
                'Test Value',
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedLabelComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect(wrapper.getByTestId('e-field-label')).toHaveTextContent('TESTTITLE');
        });

        it('should render with prefix', () => {
            screenElement.TESTPREFIX = 'TESTPREFIX';

            addFieldToState(
                FieldKey.Label,
                state,
                screenId,
                elementId,
                {
                    prefix() {
                        expect(this).toBe(screenElement);
                        return (this as any).TESTPREFIX;
                    },
                },
                'Test Value',
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedLabelComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect(wrapper.container).toHaveTextContent('TESTPREFIX Test Value');
        });

        it('should render with postfix', () => {
            screenElement.TESTPOSTFIX = 'TESTPOST';

            addFieldToState(
                FieldKey.Label,
                state,
                screenId,
                elementId,
                {
                    postfix() {
                        expect(this).toBe(screenElement);
                        return (this as any).TESTPOSTFIX;
                    },
                },
                'Test Value',
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedLabelComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect(wrapper.container).toHaveTextContent('Test Value TESTPOST');
        });

        it('should render disabled', () => {
            screenElement.ISDEFAULT = true;

            addFieldToState(
                FieldKey.Label,
                state,
                screenId,
                elementId,
                {
                    isDisabled() {
                        expect(this).toBe(screenElement);
                        return (this as any).ISDEFAULT;
                    },
                },
                'Test Value',
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedLabelComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect((wrapper.container.firstChild as HTMLDivElement).classList.contains('e-disabled')).toEqual(true);
        });
    });

    describe('text', () => {
        it('should render with title', () => {
            screenElement.TESTTITLE = 'TESTTITLE';

            addFieldToState(
                FieldKey.Text,
                state,
                screenId,
                elementId,
                {
                    title() {
                        expect(this).toBe(screenElement);
                        return (this as any).TESTTITLE;
                    },
                },
                'Test Value',
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedTextComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect(wrapper.container.querySelector('label')).toHaveTextContent('TESTTITLE');
        });

        it('should render with prefix', () => {
            screenElement.TESTPREFIX = 'TESTPREFIX';

            addFieldToState(
                FieldKey.Text,
                state,
                screenId,
                elementId,
                {
                    prefix() {
                        expect(this).toBe(screenElement);
                        return (this as any).TESTPREFIX;
                    },
                },
                'Test Value',
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedTextComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect(wrapper.container.querySelector('.e-field-prefix')).toHaveTextContent(screenElement.TESTPREFIX);
        });

        it('should render with postfix', () => {
            screenElement.TESTPOSTFIX = 'TESTPOST';

            addFieldToState(
                FieldKey.Text,
                state,
                screenId,
                elementId,
                {
                    postfix() {
                        expect(this).toBe(screenElement);
                        return (this as any).TESTPOSTFIX;
                    },
                },
                'Test Value',
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedTextComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect(wrapper.container.querySelector('.e-field-postfix')).toHaveTextContent(screenElement.TESTPOSTFIX);
        });

        it('should render disabled', () => {
            screenElement.ISDEFAULT = true;

            addFieldToState(
                FieldKey.Text,
                state,
                screenId,
                elementId,
                {
                    isDisabled() {
                        expect(this).toBe(screenElement);
                        return (this as any).ISDEFAULT;
                    },
                },
                'Test Value',
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedTextComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect((wrapper.container.firstChild as HTMLDivElement).classList.contains('e-disabled')).toEqual(true);
        });

        it('should render readonly', () => {
            screenElement.ISREADONLY = true;

            addFieldToState(
                FieldKey.Text,
                state,
                screenId,
                elementId,
                {
                    isReadOnly() {
                        expect(this).toBe(screenElement);
                        return (this as any).ISREADONLY;
                    },
                },
                'Test Value',
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedTextComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect((wrapper.container.firstChild as HTMLDivElement).classList.contains('e-read-only')).toEqual(true);
        });
    });

    describe('numeric', () => {
        it('should render with title', () => {
            screenElement.TESTTITLE = 'TESTTITLE';

            addFieldToState(
                FieldKey.Numeric,
                state,
                screenId,
                elementId,
                {
                    title() {
                        expect(this).toBe(screenElement);
                        return (this as any).TESTTITLE;
                    },
                },
                'Test Value',
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedNumericComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect(wrapper.container.querySelector('label')).toHaveTextContent('TESTTITLE');
        });

        it('should render with prefix', () => {
            screenElement.TESTPREFIX = 'TESTPREFIX';

            addFieldToState(
                FieldKey.Numeric,
                state,
                screenId,
                elementId,
                {
                    prefix() {
                        expect(this).toBe(screenElement);
                        return (this as any).TESTPREFIX;
                    },
                },
                1.234,
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedNumericComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect(wrapper.container.querySelector('.e-field-prefix')).toHaveTextContent(screenElement.TESTPREFIX);
        });

        it('should render with postfix', () => {
            screenElement.TESTPOSTFIX = 'TESTPOST';

            addFieldToState(
                FieldKey.Numeric,
                state,
                screenId,
                elementId,
                {
                    postfix() {
                        expect(this).toBe(screenElement);
                        return (this as any).TESTPOSTFIX;
                    },
                },
                1.234,
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedNumericComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect(wrapper.container.querySelector('.e-field-postfix')).toHaveTextContent(screenElement.TESTPOSTFIX);
        });

        it('should render disabled', () => {
            screenElement.ISDEFAULT = true;

            addFieldToState(
                FieldKey.Numeric,
                state,
                screenId,
                elementId,
                {
                    isDisabled() {
                        expect(this).toBe(screenElement);
                        return (this as any).ISDEFAULT;
                    },
                },
                1.234,
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedNumericComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect((wrapper.container.firstChild as HTMLDivElement).classList.contains('e-disabled')).toEqual(true);
        });

        it('should render readonly', () => {
            screenElement.ISREADONLY = true;

            addFieldToState(
                FieldKey.Numeric,
                state,
                screenId,
                elementId,
                {
                    isReadOnly() {
                        expect(this).toBe(screenElement);
                        return (this as any).ISREADONLY;
                    },
                },
                1.234,
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedNumericComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect((wrapper.container.firstChild as HTMLDivElement).classList.contains('e-read-only')).toEqual(true);
        });

        it('should render with a scale', () => {
            screenElement.SCALE = 4;

            addFieldToState(
                FieldKey.Numeric,
                state,
                screenId,
                elementId,
                {
                    scale() {
                        expect(this).toBe(screenElement);
                        return (this as any).SCALE;
                    },
                },
                1.234,
            );
            mockStore = getMockStore(state);

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedNumericComponent screenId={screenId} elementId={elementId} />
                </Provider>,
            );
            expect(wrapper.container.querySelector('input')!.value).toEqual('1.2340');
        });
    });
});
