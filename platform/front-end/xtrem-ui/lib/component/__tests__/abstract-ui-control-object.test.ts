import type { ContainerProperties } from '../abstract-container';
import { AbstractUiControlObject } from '../control-objects';
import { FieldKey } from '../types';

class AbstractUiControlObjectImplementation extends AbstractUiControlObject {}

describe('Abstract container', () => {
    const screenId = 'TestScreen';
    const elementId = 'abstract-container-id';
    let container: AbstractUiControlObject;
    let containerProperties: ContainerProperties;

    beforeEach(() => {
        containerProperties = {
            title: 'Sticker title',
            isHidden: true,
            isDisabled: true,
            isTransient: true,
        };
        const getPropertiesMock = jest.fn(() => containerProperties);
        const setPropertiesMock = jest.fn((_screenId: string, elementId: string, value: any) => {
            containerProperties = { ...value };
        });
        container = new AbstractUiControlObjectImplementation(
            screenId,
            elementId,
            getPropertiesMock,
            setPropertiesMock,
            FieldKey.Text,
        );
    });

    it('should retrieve isDisabled from properties', () => {
        expect(container.isDisabled).toBe(containerProperties.isDisabled);
    });

    it('should retrieve title from properties', () => {
        expect(container.title).toBe(containerProperties.title);
    });

    it('should retrieve isTransient from properties', () => {
        expect(container.isTransient).toEqual(!!containerProperties.isTransient);
    });

    it('should set isDisabled property in object properties', () => {
        const newValue = false;
        container.isDisabled = newValue;
        expect(containerProperties.isDisabled).toEqual(newValue);
    });

    it('should set title property in object properties', () => {
        const newValue = 'new value';
        container.title = newValue;
        expect(containerProperties.title).toBe(newValue);
    });
});
