import type { ScreenBase } from '../service/screen-base';
import type { ScreenExtension } from '../types';
import type { ValueOrCallbackWithFieldValue } from '../utils/types';
import type { ComponentProperties } from './base-control-object';
import { BaseControlObject } from './base-control-object';
import { ControlObjectProperty } from './property-decorators/control-object-property-decorator';
import { FieldControlObjectResolvedProperty } from './property-decorators/control-object-resolved-property-decorator';

export type ColumnWidth = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;

export interface ResponsiveProperties {
    /** Whether the element is hidden or not in mobile devices. Defaults to false */
    isHiddenMobile?: boolean;
    /** Whether the element is hidden or not in desktop devices. Defaults to false */
    isHiddenDesktop?: boolean;
}

export interface UiComponentProperties<CT extends ScreenBase = ScreenBase>
    extends ComponentProperties<CT>,
        ResponsiveProperties {
    /** Whether the element title is hidden or not. Defaults to false */
    isTitleHidden?: boolean;
    /** Whether the value is bound to a GraphQL node (transient = false) or not (transient = true). Defaults to false */
    isTransient?: boolean;
    /** Whether the HTML element is hidden or not. Defaults to false */
    isHidden?: ValueOrCallbackWithFieldValue<CT, boolean>;
    /** The title of the HTML element */
    title?: ValueOrCallbackWithFieldValue<CT, string>;
}

/**
 * Any element than can be placed inside a page and can be interacted with (i.e. retrieving
 * and/or setting element's properties values)
 */
export abstract class AbstractUiControlObject<
    CT extends ScreenExtension<CT> = ScreenBase,
    S extends UiComponentProperties<CT> = {},
> extends BaseControlObject<CT, S> {
    static readonly defaultUiProperties: Partial<UiComponentProperties> = {
        ...BaseControlObject.defaultUiProperties,
        isHiddenDesktop: false,
        isHiddenMobile: false,
        isTitleHidden: false,
        isTransient: false,
    };

    @ControlObjectProperty<UiComponentProperties<CT>, AbstractUiControlObject<CT, UiComponentProperties<CT>>>()
    /** Whether the element title is hidden or not. Defaults to false */
    isTitleHidden?: boolean;

    /** Whether the value is bind to a GraphQL node (transient = false) or not (transient = true). */
    get isTransient(): boolean {
        return !!this.getUiComponentProperty('isTransient');
    }

    @FieldControlObjectResolvedProperty<
        UiComponentProperties<CT>,
        AbstractUiControlObject<CT, UiComponentProperties<CT>>
    >()
    /** Title of the component */
    title?: string;

    @FieldControlObjectResolvedProperty<
        UiComponentProperties<CT>,
        AbstractUiControlObject<CT, UiComponentProperties<CT>>
    >()
    /** Whether the HTML element is visible or not */
    isHidden?: boolean;
}
