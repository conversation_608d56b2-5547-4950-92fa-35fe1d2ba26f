import type { CollectionValue } from '../service/collection-data-service';
import * as React from 'react';
import type { CollectionGlobalValidationState, CollectionValueType } from '../service/collection-data-types';
import { RecordActionType } from '../service/collection-data-types';
import type { Unsubscribe } from 'redux';
import { objectKeys, type Dict } from '@sage/xtrem-shared';
import type { ValidationResult } from '../service/screen-base-definition';
import { cleanMetadataFromRecord } from '../utils/transformers';
import { isEqual } from 'lodash';
import type { ClientNode } from '@sage/xtrem-client';
import type * as xtremRedux from '../redux';
import { useDeepEqualSelector } from '../utils/hooks/use-deep-equal-selector';

interface ConnectedCollectionValueProps {
    value?: CollectionValue;
    isUncommitted?: boolean;
}

abstract class BaseCollectionValueSubscriptionHOC<
    PropType extends ConnectedCollectionValueProps,
> extends React.Component<PropType> {
    private valueChangeSubscription: Unsubscribe | undefined;

    private validityChangeSubscription: Unsubscribe | undefined;

    componentDidMount(): void {
        this.subscribeToChanges();
    }

    componentDidUpdate(prevProps: ConnectedCollectionValueProps): void {
        if (prevProps.value !== this.props.value) {
            this.subscribeToChanges();
        }
    }

    componentWillUnmount(): void {
        if (this.validityChangeSubscription) {
            this.validityChangeSubscription();
        }

        if (this.valueChangeSubscription) {
            this.valueChangeSubscription();
        }
    }

    abstract shouldUpdateOnChange(type: RecordActionType, rowValue: Partial<CollectionValueType>): boolean;

    abstract shouldUpdateOnValidityChange(
        globalValidationState: CollectionGlobalValidationState,
        recordValidationState: Dict<ValidationResult>,
        id: string,
        level?: number,
    ): boolean;

    subscribeToChanges(): void {
        this.valueChangeSubscription = this.props.value?.subscribeForValueChanges(
            (type, rowValue: Partial<CollectionValueType>) => {
                if (this.shouldUpdateOnChange(type, rowValue)) {
                    this.forceUpdate();
                }
            },
            this.props.isUncommitted,
        );

        this.validityChangeSubscription = this.props.value?.subscribeForValidityChanges(
            ({ globalValidationState, recordValidationState, recordId, recordLevel }) => {
                if (
                    this.shouldUpdateOnValidityChange(
                        globalValidationState,
                        recordValidationState,
                        recordId,
                        recordLevel,
                    )
                ) {
                    this.forceUpdate();
                }
            },
            this.props.isUncommitted,
        );
    }
}
class CollectionValueSubscription extends BaseCollectionValueSubscriptionHOC<ConnectedCollectionValueProps> {
    shouldUpdateOnChange(type: RecordActionType): boolean {
        return type === RecordActionType.ADDED || type === RecordActionType.REMOVED;
    }

    shouldUpdateOnValidityChange(): boolean {
        return false;
    }
}

/**
 * If a CollectionValue `value` property is provided, the HOC subscribes to the add and remove events of collection value
 * items and re-renders the child component on change
 * @param WrappedComponent
 */
export const withCollectionValueSubscription = (
    WrappedComponent: React.ComponentType<ConnectedCollectionValueProps>,
): React.ComponentType =>
    class extends CollectionValueSubscription {
        render(): React.ReactNode {
            return <WrappedComponent {...this.props} />;
        }
    };

class CollectionValueAndOrderSubscription extends CollectionValueSubscription {
    private order: string[];

    constructor(props: ConnectedCollectionValueProps) {
        super(props);
        this.order = props.value?.getData().map(d => d._id) || [];
    }

    shouldUpdateOnChange(type: RecordActionType): boolean {
        let shouldUpdate = super.shouldUpdateOnChange(type);
        if (!shouldUpdate && type === RecordActionType.MODIFIED) {
            const newOrder = this.props.value?.getData().map(d => d._id) || [];
            shouldUpdate = !isEqual(this.order, newOrder);
            this.order = newOrder;
            return shouldUpdate;
        }

        return shouldUpdate;
    }

    shouldUpdateOnValidityChange(): boolean {
        return false;
    }
}
/**
 * If a CollectionValue `value` property is provided, the HOC subscribes to the add and remove events of collection value
 * items and re-renders the child component on change
 * @param WrappedComponent
 */
export const withCollectionValueAndOrderSubscription = (
    WrappedComponent: React.ComponentType<ConnectedCollectionValueProps>,
): React.ComponentType =>
    class extends CollectionValueAndOrderSubscription {
        render(): React.ReactNode {
            return <WrappedComponent {...this.props} />;
        }
    };
export interface ConnectedCollectionItemValueProps extends ConnectedCollectionValueProps {
    recordId?: string;
    level?: number;
    onChange?: (bind: string, value: any, recordId: string) => Promise<void>;
    recordValue?: ClientNode;
    validationState?: Dict<ValidationResult>;
    isUncommitted?: boolean;
    shouldFetchDefault?: boolean;
}

/**
 * If a CollectionValue `value` property and a valid `recordId` property are provided, the HOC subscribes to changes to
 * the item tracked by the record ID and re-renders the child component if the item changes.
 * @param WrappedComponent
 */
export const withCollectionValueItemSubscription = <U extends ConnectedCollectionItemValueProps>(
    WrappedComponent: React.ComponentType<U>,
): React.ComponentType<U> =>
    class CollectionValueItemSubscription extends BaseCollectionValueSubscriptionHOC<U> {
        shouldUpdateOnValidityChange(
            globalValidationState: CollectionGlobalValidationState,
            recordValidationState: Dict<ValidationResult>,
            id: string,
            level?: number,
        ): boolean {
            return id === this.props.recordId && (level ?? 0) === (this.props.level ?? 0);
        }

        shouldUpdateOnChange(type: RecordActionType, rowValue: Partial<CollectionValueType>): boolean {
            return (
                !!rowValue._id &&
                rowValue._id === this.props.recordId &&
                (rowValue.__level ?? 0) === (this.props.level ?? 0)
            );
        }

        onChange = async (bind: string, value: any): Promise<void> => {
            if (this.props.value && this.props.recordId) {
                this.props.value.setCellValue({
                    recordId: this.props.recordId!,
                    level: this.props.level,
                    isOrganicChange: true,
                    columnId: bind,
                    value,
                    isUncommitted: this.props.isUncommitted,
                    shouldFetchDefault: this.props.shouldFetchDefault,
                });
            }

            if (this.props.onChange) {
                await this.props.onChange(bind, value, this.props.recordId!);
            }
        };

        getRecordValue(): any {
            if (this.props.value && this.props.recordId) {
                return this.props.value?.getRawRecord({
                    id: this.props.recordId!,
                    level: this.props.level,
                    cleanMetadata: false,
                    isUncommitted: this.props.isUncommitted,
                });
            }

            return undefined;
        }

        render(): React.ReactNode {
            const value = this.getRecordValue();
            // Remap the errors into a validation array from the object structure of collection value
            const validationErrors = objectKeys(value?.__validationState || {}).reduce<ValidationResult[]>(
                (prevValue, key) => {
                    prevValue.push(value!.__validationState[key]);
                    return prevValue;
                },
                [],
            );
            return (
                <WrappedComponent
                    {...this.props}
                    onChange={this.onChange}
                    recordValue={cleanMetadataFromRecord(value)}
                    validationErrors={validationErrors}
                    validate={(): Promise<any> => Promise.resolve(undefined)}
                />
            );
        }
    };

export function useCollectionItemRecord({
    elementId,
    screenId,
    recordId,
    level = 0,
    isUncommitted = false,
}: {
    elementId: string;
    screenId: string;
    recordId: string;
    level?: number;
    isUncommitted?: boolean;
}): any | null {
    const [recordValue, setRecordValue] = React.useState<any>(null);
    const collectionValue = useDeepEqualSelector<xtremRedux.XtremAppState, CollectionValue>(state => {
        return state.screenDefinitions[screenId].values[elementId];
    });

    const onRecordChange = React.useCallback(
        (type: RecordActionType, rowValue: any) => {
            const recordLevel = rowValue.__level ?? 0;
            if (rowValue._id !== recordId || recordLevel === level) {
                setRecordValue(null);
            }

            if (type === RecordActionType.ADDED || type === RecordActionType.MODIFIED) {
                setRecordValue(rowValue);
            }
        },
        [level, recordId],
    );

    React.useEffect(() => {
        setRecordValue(
            collectionValue.getRawRecord({
                id: recordId,
                level,
                isUncommitted,
            }),
        );
        return collectionValue.subscribeForValueChanges(onRecordChange, isUncommitted);
    }, [collectionValue, recordId, level, isUncommitted, onRecordChange]);

    return recordValue;
}
