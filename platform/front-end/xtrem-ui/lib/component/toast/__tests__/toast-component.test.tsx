import { fireEvent, render } from '@testing-library/react';
import * as React from 'react';
import type { Toast } from '../../../service/toast-service';
import { PERMANENT_TOAST } from '../../../service/toast-service';
import { ToastComponent } from '../toast-component';

describe('Toast component', () => {
    let toast: Toast = {
        content: 'Toast content',
        id: 1,
        timeout: 4000,
        isDismissed: false,
        type: 'error',
    };
    let removeToast: jest.Mock;

    beforeEach(() => {
        jest.useFakeTimers();
        removeToast = jest.fn();
        toast = {
            content: 'Toast content',
            id: 1,
            isDismissed: false,
            timeout: 4000,
            type: 'error',
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.useRealTimers();
    });

    it('should call removeToast after timeout', () => {
        render(<ToastComponent toast={toast} removeToast={removeToast} />);
        jest.advanceTimersByTime(4000 + 1000);
        expect(removeToast).toHaveBeenCalledTimes(1);
    });

    it('should call removeToast on dismiss click', async () => {
        const wrapper = render(<ToastComponent toast={toast} removeToast={removeToast} />);
        fireEvent.click(wrapper.baseElement.querySelector('span[type="close"]')!);
        jest.advanceTimersByTime(1000);
        expect(removeToast).toHaveBeenCalledTimes(1);
        jest.advanceTimersByTime(4000 + 1000);
        expect(removeToast).toHaveBeenCalledTimes(1);
    });

    it('should permanently keep toasts until the user cancels them if opened with special flag', async () => {
        toast.timeout = PERMANENT_TOAST;
        const wrapper = render(<ToastComponent toast={toast} removeToast={removeToast} />);
        jest.advanceTimersByTime(4000);
        expect(removeToast).toHaveBeenCalledTimes(0);
        jest.advanceTimersByTime(4000);
        expect(removeToast).toHaveBeenCalledTimes(0);
        jest.advanceTimersByTime(4000);
        fireEvent.click(wrapper.baseElement.querySelector('span[type="close"]')!);
        jest.advanceTimersByTime(2000);
        expect(removeToast).toHaveBeenCalledTimes(1);
    });

    it('should render rich text based on MD input', () => {
        toast.content = '**Hello** *world!*';
        const wrapper = render(<ToastComponent toast={toast} removeToast={removeToast} />);

        expect(wrapper.baseElement.querySelector('strong')!).toHaveTextContent('Hello');
        expect(wrapper.baseElement.querySelector('em')!).toHaveTextContent('world!');
    });

    it('should escape HTML tags in the content', () => {
        toast.content =
            'Content: <div id="wow"></div><iframe class="dangerous" src="javascript:alert(\'OMG!\');"></iframe>';
        const wrapper = render(<ToastComponent toast={toast} removeToast={removeToast} />);

        expect(wrapper.baseElement.querySelector('iframe')).toBeNull();
        expect(wrapper.baseElement.querySelector('#wow')).toBeNull();
    });
});
