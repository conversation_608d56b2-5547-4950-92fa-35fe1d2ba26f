import CarbonToast from 'carbon-react/esm/components/toast';
import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../redux';
import type { Toast } from '../../service/toast-service';
import { PERMANENT_TOAST } from '../../service/toast-service';
import * as showdown from 'showdown';
import { escape as lodashEscape } from 'lodash';

const converter = new showdown.Converter();
export interface ToastComponentExternalProps {
    toast: Toast;
}

export interface ToastComponentState {
    open: boolean;
}

export interface ToastComponentProps extends ToastComponentExternalProps {
    removeToast: () => void;
}

export class ToastComponent extends React.Component<ToastComponentProps, ToastComponentState> {
    timeout?: number;

    constructor(props: ToastComponentProps) {
        super(props);
        this.state = { open: true };
    }

    componentDidMount(): void {
        if (this.props.toast.timeout !== PERMANENT_TOAST) {
            this.timeout = setTimeout(this.onDismiss, this.props.toast.timeout) as any;
        }
    }

    onDismiss = (): void => {
        if (this.timeout) {
            clearTimeout(this.timeout);
        }

        this.setState({ open: false });

        // eslint-disable-next-line @typescript-eslint/no-implied-eval
        setTimeout(this.props.removeToast.bind(this), 1000);
    };

    render(): React.ReactNode {
        const { content, type, language = 'markdown' } = this.props.toast;
        return (
            <CarbonToast
                variant={type}
                open={this.state.open}
                onDismiss={this.onDismiss}
                targetPortalId="stacked"
                maxWidth={`${Math.min(550, window.innerWidth || 300)}px`}
            >
                {language === 'markdown' && (
                    <span
                        data-testid={`e-toast-content e-toast-content-type-${type}`}
                        className="e-toast-content"
                        // eslint-disable-next-line react/no-danger
                        dangerouslySetInnerHTML={{
                            __html: converter.makeHtml(lodashEscape(content as string)),
                        }}
                    />
                )}
                {language === 'jsx' && content}
            </CarbonToast>
        );
    }
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: ToastComponentExternalProps): ToastComponentProps => {
    return {
        ...props,
        removeToast: xtremRedux.actions.actionStub,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: ToastComponentExternalProps,
): Partial<ToastComponentProps> => {
    return {
        removeToast: (): void => {
            dispatch(xtremRedux.actions.removeToast(props.toast.id));
        },
    };
};

export const ConnectedToastComponent = connect(mapStateToProps, mapDispatchToProps)(ToastComponent);
