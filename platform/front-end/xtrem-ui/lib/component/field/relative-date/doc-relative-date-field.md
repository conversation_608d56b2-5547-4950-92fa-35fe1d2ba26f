PATH: XTREEM/UI+Field+Widgets/Relative+Date+Field

## Introduction

Relative date field is a read-only type which renders `Dates` and `Datetimes` to a localized, language-sensitive time format relative to the current date.

The value is set by assingning either:

- Date object (Javascript)
- Simplified extended format ISO 8601 string (YYYY-MM-DD[T]HH:mm:ss.sssTZD)
- Complete date format ISO 8601 string (YYYT-MM-DD)

or by binding the field to a GraphQL `Date` or `Datetime` types.

## Example:

```ts
@ui.decorators.relativeDateField<Page>({
    parent() {
        return this.block;
    },
    title: 'A relative date field',
})
relativeDate: ui.fields.RelativeDate;
```

#### Use of field as nested field:

```ts
@ui.decorators.tableField<Page>({
    ...,
    columns: [
        ...,
        ui.nestedFields.relativeDateField({
            bind: 'lastModified',
            title: 'Last Modified',
        }),
    ],
})
table: ui.fields.Table;
```
As a nested field, it can be used in tables, pods and cards alike.

### Display decorator properties:

-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **helperText**: The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized. If the helper text is not provided, the value is of the field is displayed in its place.

### Binding decorator properties:

-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.

### Event handler decorator properties:

-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

#### Runtime Functions

-   **refresh()**: Refetches the field's values from the server and updates the user interface.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
