import type { BaseReadonlyComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { ScreenBase } from '../../../service/screen-base';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { Clickable, ExtensionField, HasParent, Nested, NestedClickable, Sizable } from '../traits';
import type { FieldControlObjectInstance } from '../../types';
import type { BlockControlObject } from '../../control-objects';
import type { ClientNode } from '@sage/xtrem-client';

export interface RelativeDateDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<RelativeDateProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        HasParent<CT, BlockControlObject<CT>>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Sizable {}

export interface NestedRelativeDateProperties<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any>
    extends Omit<RelativeDateProperties<CT>, 'bind'>,
        Nested<NodeType>,
        NestedClickable<CT, NodeType>,
        Sizable {}

export interface RelativeDateProperties<CT extends ScreenBase = ScreenBase> extends ReadonlyFieldProperties<CT> {}

export type RelativeDateComponentProps = BaseReadonlyComponentProperties<
    RelativeDateProperties,
    string, // iso-date?
    NestedFieldsAdditionalProperties & { scope?: 'date' | 'datetime' }
>;
