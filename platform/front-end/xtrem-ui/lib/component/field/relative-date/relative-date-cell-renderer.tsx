import * as React from 'react';
import type { NestedRelativeDateProperties } from '../../nested-fields-properties';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import { RelativeDate } from '../../ui/relative-date/relative-date-component';

export function RelativeDateCellRenderer(
    props: CellParams<NestedRelativeDateProperties> & {
        colDef: { cellRendererParams: { relativeDateScope: 'date' | 'datetime' } };
    },
): React.ReactElement | null {
    const { value } = props;
    if (value === undefined || value === '' || Number.isNaN(value)) {
        return null;
    }
    return (
        <props.fieldProperties.wrapper {...props}>
            <RelativeDate value={value} scope={props.colDef?.cellRendererParams?.relativeDateScope ?? 'datetime'} />
        </props.fieldProperties.wrapper>
    );
}
