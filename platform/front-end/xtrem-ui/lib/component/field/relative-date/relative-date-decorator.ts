/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { RelativeDateControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { RelativeDateDecoratorProperties } from './relative-date-types';

class RelativeDateDecorator extends AbstractFieldDecorator<FieldKey.RelativeDate> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = RelativeDateControlObject;
}

/**
 * Initializes the decorated member as a [Progress]{@link ProgressControlObject} field with the provided properties
 *
 * @param properties The properties that the [Progress]{@link ProgressControlObject} field will be initialized with
 */
export function relativeDateField<T extends ScreenExtension<T>>(
    properties: RelativeDateDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.RelativeDate>(
        properties,
        RelativeDateDecorator,
        FieldKey.RelativeDate,
    );
}

export function relativeDateFieldOverride<T extends ScreenExtension<T>>(
    properties: ClickableOverrideDecoratorProperties<RelativeDateDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.RelativeDate>(properties);
}
