import * as React from 'react';
import { connect } from 'react-redux';
import { mapDispatchToProps, mapReadonlyStateToProps, ReadonlyFieldBaseComponent } from '../field-base-component';
import type { NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { RelativeDateDecoratorProperties } from './relative-date-types';
import { CarbonWrapper } from '../carbon-wrapper';
import { formatDateToCurrentLocale, isValidDatePropertyValue } from '@sage/xtrem-date-time';
import { getRelativeDateDisplayValue } from './relative-date-utils';
import type { DatePropertyValue } from '../../../utils/types';

export class RelativeDateComponent extends ReadonlyFieldBaseComponent<
    RelativeDateDecoratorProperties,
    DatePropertyValue,
    NestedFieldsAdditionalProperties
> {
    render(): React.ReactNode {
        const value = this.props.value || '';
        const isValid = isValidDatePropertyValue(value);

        return (
            <CarbonWrapper
                {...this.props}
                className="e-relative-date-field"
                componentName="relative-date"
                componentRef={this.componentRef}
                fieldProperties={{
                    ...this.props.fieldProperties,
                    isReadOnly: true,
                }}
                handlersArguments={this.props.handlersArguments}
                readOnlyTooltip={isValid ? formatDateToCurrentLocale(value) : ''}
                readOnlyDisplayValue={isValid ? getRelativeDateDisplayValue(value) : ''}
                value={value}
            >
                <noscript />
            </CarbonWrapper>
        );
    }
}

export const ConnectedRelativeDateComponent = connect(
    mapReadonlyStateToProps(),
    mapDispatchToProps(),
)(RelativeDateComponent);

export default ConnectedRelativeDateComponent;
