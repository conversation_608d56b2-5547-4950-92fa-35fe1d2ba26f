import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { RelativeDateComponentProps } from './relative-date-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedRelativeDateComponent = React.lazy(() => import('./relative-date-component'));

export function AsyncConnectedRelativeDateComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedRelativeDateComponent {...props} />
        </React.Suspense>
    );
}

const RelativeDateComponent = React.lazy(() =>
    import('./relative-date-component').then(c => ({ default: c.RelativeDateComponent })),
);

export function AsyncRelativeDateComponent(props: RelativeDateComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <RelativeDateComponent {...props} />
        </React.Suspense>
    );
}
