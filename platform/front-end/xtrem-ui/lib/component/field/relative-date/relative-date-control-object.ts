/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a numeric value and represents it as a percentage (e.g. progress bar)
 */
export class RelativeDateControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends ReadonlyFieldControlObject<
    CT,
    FieldKey.RelativeDate,
    FieldComponentProps<FieldKey.RelativeDate>
> {}
