import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { FieldKey } from '../../../types';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import type { XtremAppState } from '../../../../redux';
import { ConnectedRelativeDateComponent } from '../relative-date-component';
import type { ReadonlyFieldProperties } from '../../../readonly-field-control-object';
import { render, cleanup } from '@testing-library/react';
import '@testing-library/jest-dom';

describe('RelativeDateComponent', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: ReadonlyFieldProperties;
    let now: Date;
    let nowString: string;
    let todayString: string;

    beforeEach(() => {
        cleanup();
        mockFieldProperties = {
            title: 'Test Field Title',
        };
        now = new Date(Date.now());
        nowString = now.toISOString();
        todayString = now.toISOString().substring(0, 10);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(
                FieldKey.RelativeDate,
                state,
                screenId,
                'test-relative-date-field-now-date',
                mockFieldProperties,
                now,
            );
            addFieldToState(
                FieldKey.RelativeDate,
                state,
                screenId,
                'test-relative-date-field-now-string',
                mockFieldProperties,
                nowString,
            );
            addFieldToState(
                FieldKey.RelativeDate,
                state,
                screenId,
                'test-relative-date-field-today-string',
                mockFieldProperties,
                todayString,
            );
            addFieldToState(
                FieldKey.RelativeDate,
                state,
                screenId,
                'test-relative-date-field-invalid-string',
                mockFieldProperties,
                'invalid-date',
            );
            addFieldToState(
                FieldKey.RelativeDate,
                state,
                screenId,
                'test-relative-date-field-null',
                mockFieldProperties,
                null,
            );
            mockStore = getMockStore(state);
        });

        describe('values', () => {
            it('should render relative datetimes from Date', () => {
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedRelativeDateComponent
                            screenId={screenId}
                            elementId="test-relative-date-field-now-date"
                        />
                    </Provider>,
                );
                const content = wrapper.queryByTestId('e-field-value');
                expect(content).toHaveTextContent('now');
            });

            it('should render relative datetimes from ISO strings', () => {
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedRelativeDateComponent
                            screenId={screenId}
                            elementId="test-relative-date-field-now-string"
                        />
                    </Provider>,
                );
                const content = wrapper.queryByTestId('e-field-value');
                expect(content).toHaveTextContent('now');
            });

            it('should render relative dates from ISO strings', () => {
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedRelativeDateComponent
                            screenId={screenId}
                            elementId="test-relative-date-field-today-string"
                        />
                    </Provider>,
                );
                const content = wrapper.queryByTestId('e-field-value');
                expect(content).toHaveTextContent('today');
            });

            it('should not render on invalid dates', () => {
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedRelativeDateComponent
                            screenId={screenId}
                            elementId="test-relative-date-field-invalid-string"
                        />
                    </Provider>,
                );
                const content = wrapper.queryByTestId('e-field-value');
                expect(content).toHaveTextContent('');
            });

            it('should not render on null', () => {
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedRelativeDateComponent screenId={screenId} elementId="test-relative-date-field-null" />
                    </Provider>,
                );
                const content = wrapper.queryByTestId('e-field-value');
                expect(content).toHaveTextContent('');
            });
        });
    });
});
