import { toRelative_ } from '../relative-date-utils';

describe('toRelative (testable toRelative_)', () => {
    it('should be today', () => {
        expect(toRelative_('date', 'en-US', '2020-04-04', '2020-04-04')).toBe('today');
    });
    it('should be hoy', () => {
        expect(toRelative_('date', 'es-ES', '2020-04-04', '2020-04-04')).toBe('hoy');
    });
    it('should be yesterday', () => {
        expect(toRelative_('date', 'en-US', '2020-04-03', '2020-04-04')).toBe('yesterday');
    });
    it('should be yesterday', () => {
        expect(toRelative_('date', 'en-US', '2023-02-28', '2023-03-01')).toBe('yesterday');
    });
    it('should be tomorrow', () => {
        expect(toRelative_('date', 'en-US', '2023-03-02', '2023-03-01')).toBe('tomorrow');
    });
    it('should be 2 days ago', () => {
        expect(toRelative_('date', 'en-US', '2020-04-02', '2020-04-04')).toBe('2 days ago');
    });
    it('should be in 2 days', () => {
        expect(toRelative_('date', 'en-US', '2020-04-04', '2020-04-02')).toBe('in 2 days');
    });
    it('should be 2 weeks ago', () => {
        expect(toRelative_('date', 'en-US', '2020-03-20', '2020-04-04')).toBe('2 weeks ago');
    });
    it('should be in 2 weeks', () => {
        expect(toRelative_('date', 'en-US', '2020-04-04', '2020-03-20')).toBe('in 2 weeks');
    });
    it('should be 2 months ago', () => {
        expect(toRelative_('date', 'en-US', '2020-02-04', '2020-04-04')).toBe('2 months ago');
    });
    it('should be in 2 months', () => {
        expect(toRelative_('date', 'en-US', '2020-04-04', '2020-02-04')).toBe('in 2 months');
    });
    it('should be 2 years ago', () => {
        expect(toRelative_('date', 'en-US', '2018-04-02', '2020-04-04')).toBe('2 years ago');
    });
    it('should be in 2 years', () => {
        expect(toRelative_('date', 'en-US', '2020-04-02', '2018-04-01')).toBe('in 2 years');
    });

    it('should be 10 hours ago (supports TZ diffs)', () => {
        const sixAtSydney = new Date(Date.parse('2020-04-04T18:00:00.000+11:00'));
        const sixAtParis = new Date(Date.parse('2020-04-04T18:00:00.000+01:00'));
        expect(toRelative_('datetime', 'en-US', sixAtSydney, sixAtParis)).toBe('10 hours ago');
    });

    it('should be in 10 hours (supports TZ diffs)', () => {
        const sixAtSydney = new Date(Date.parse('2020-04-04T18:00:00.000+11:00'));
        const sixAtParis = new Date(Date.parse('2020-04-04T18:00:00.000+01:00'));
        expect(toRelative_('datetime', 'en-US', sixAtParis, sixAtSydney)).toBe('in 10 hours');
    });

    it('should be 2 minutes ago', () => {
        expect(toRelative_('datetime', 'en-US', '2020-04-04T18:00:00.000+01:00', '2020-04-04T18:02:00.000+01:00')).toBe(
            '2 minutes ago',
        );
    });

    it('should be in 2 minutes', () => {
        expect(toRelative_('datetime', 'en-US', '2020-04-04T18:02:00.000+01:00', '2020-04-04T18:00:00.000+01:00')).toBe(
            'in 2 minutes',
        );
    });

    it('should be now', () => {
        expect(toRelative_('datetime', 'en-US', '2020-04-04T18:02:04.000+01:00', '2020-04-04T18:02:00.000+01:00')).toBe(
            'now',
        );
        expect(toRelative_('datetime', 'en-US', '2020-04-04T18:02:00.000+01:00', '2020-04-04T18:02:04.000+01:00')).toBe(
            'now',
        );
        expect(toRelative_('datetime', 'en-US', '2020-04-04T18:02:00.000+01:00', '2020-04-04T18:02:00.000+01:00')).toBe(
            'now',
        );
    });
});
