import { Datetime, DateValue, formatDateToCurrentLocale, datePropertyValueToDateString } from '@sage/xtrem-date-time';
import type { Locale } from '@sage/xtrem-shared';
import { intervalToDuration } from 'date-fns';
import { getStore } from '../../../redux';
import { isDateOrDateTime } from '../../../utils/formatters';
import type { DatePropertyValue, DateTimePropertyValue } from '../../../utils/types';

export type RelativeDateScope = 'date' | 'datetime';

const getFormattedValue = (date: DatePropertyValue | DateTimePropertyValue): DateValue | Datetime => {
    const valueType = isDateOrDateTime(date);

    if (valueType === 'date') {
        return DateValue.parse(datePropertyValueToDateString(date));
    }
    return Datetime.parse(datePropertyValueToDateString(date, true));
};

export function toRelative_(
    type: RelativeDateScope,
    lang: Locale,
    end: DatePropertyValue | DateTimePropertyValue,
    start: DatePropertyValue | DateTimePropertyValue = new Date(Date.now()),
): string | false {
    const startDate = getFormattedValue(start);
    const endDate = getFormattedValue(end);
    /* eslint-enable */
    const format = (value: number, unit: Intl.RelativeTimeFormatUnit): string => {
        // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/RelativeTimeFormat/RelativeTimeFormat#parameters
        const intlRelativeTimeFormat = new Intl.RelativeTimeFormat(lang, {
            numeric: 'auto',
            localeMatcher: 'best fit',
            style: 'long',
        });
        return intlRelativeTimeFormat.format(value, unit);
    };

    let weeks = 0;
    let days = 0;
    const {
        years,
        months,
        days: originalDays,
        hours,
        minutes,
        seconds,
    } = intervalToDuration({ start: startDate.toJsDate().getTime(), end: endDate.toJsDate().getTime() });

    if (originalDays && Math.abs(originalDays) >= 7) {
        weeks = Math.trunc(originalDays / 7);
        days = originalDays % 7;
    }

    if (years && Math.abs(years) > 0) {
        return format(years, 'year');
    }

    if (months && Math.abs(months) > 0) {
        return format(months, 'month');
    }

    if (weeks && Math.abs(weeks) > 0) {
        return format(weeks, 'week');
    }

    if (type === 'date') {
        const startDateValue = DateValue.parse(startDate.toString().substring(0, 10));
        const endDateValue = DateValue.parse(endDate.toString().substring(0, 10));
        const day = endDateValue.daysDiff(startDateValue);
        return format(day, 'day');
    }

    if (days && Math.abs(days) > 0) {
        return format(days, 'day');
    }

    if (hours && Math.abs(hours) > 0) {
        return format(hours, 'hour');
    }

    if (minutes && Math.abs(minutes) > 0) {
        return format(minutes, 'minute');
    }

    if ((seconds && Math.abs(seconds) >= 0) || seconds === 0) {
        if (Math.abs(seconds) < 20) {
            return format(0, 'second');
        }
        return format(seconds, 'second');
    }

    if (endDate.toJsDate().getTime() - startDate.toJsDate().getTime() < 1000) {
        return format(0, 'second');
    }

    return false;
}

export function toRelative(type: RelativeDateScope, end: DatePropertyValue): string | false {
    const state = getStore().getState();
    return toRelative_(type, (state.applicationContext?.locale as Locale) || 'en-US', end);
}

export const getRelativeDateDisplayValue = (value: DatePropertyValue, scope?: RelativeDateScope): string => {
    let correctedScope: RelativeDateScope | null = scope || null;
    if (!correctedScope) {
        correctedScope = isDateOrDateTime(value);
    }

    return toRelative(correctedScope ?? 'datetime', value) || formatDateToCurrentLocale(value);
};
