import type { ScreenBase } from '../../../service/screen-base';
import type { BlockControlObject } from '../../control-objects';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type { Clickable, ExtensionField, HasParent } from '../traits';

export interface StaticContentProperties<CT extends ScreenBase = ScreenBase> extends ReadonlyFieldProperties<CT> {
    content?: string;
    isMarkdown?: boolean;
    maxVisibleLines?: number;
}

export interface StaticContentDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<StaticContentProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>> {}

export type StaticContentComponentProps = BaseEditableComponentProperties<
    StaticContentDecoratorProperties<any>,
    string
>;
