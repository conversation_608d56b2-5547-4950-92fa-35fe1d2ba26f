import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { StaticContentComponentProps } from './static-content-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedStaticContentComponent = React.lazy(() => import('./static-content-component'));

export function AsyncConnectedStaticContentComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedStaticContentComponent {...props} />
        </React.Suspense>
    );
}

const StaticContentComponent = React.lazy(() =>
    import('./static-content-component').then(c => ({ default: c.StaticContentComponent })),
);

export function AsyncStaticContentComponent(props: StaticContentComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={true} bodyHeight="200px" />}>
            <StaticContentComponent {...props} />
        </React.Suspense>
    );
}
