/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { StaticContentControlObject } from './static-content-control-object';
import type { StaticContentDecoratorProperties } from './static-content-types';

export { StaticContentDecoratorProperties } from './static-content-types';

class StaticContentDecorator extends AbstractFieldDecorator<FieldKey.StaticContent> {
    protected _controlObjectConstructor = StaticContentControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

export function staticContentField<T extends ScreenExtension<T>>(
    properties: StaticContentDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.StaticContent>(
        properties,
        StaticContentDecorator,
        FieldKey.StaticContent,
    );
}

export function staticContentFieldOverride<T extends ScreenExtension<T>>(
    properties: ClickableOverrideDecoratorProperties<StaticContentDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.StaticContent>(properties);
}
