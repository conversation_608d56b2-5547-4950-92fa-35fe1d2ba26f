import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { StaticContentProperties } from './static-content-types';

export { StaticContentProperties } from './static-content-types';

export class StaticContentControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends ReadonlyFieldControlObject<
    CT,
    FieldKey.StaticContent,
    FieldComponentProps<FieldKey.StaticContent>
> {
    static readonly defaultUiProperties: Partial<FieldComponentProps<FieldKey.StaticContent>> = {
        ...ReadonlyFieldControlObject.defaultUiProperties,
    };

    @ControlObjectProperty<StaticContentProperties<CT>, StaticContentControlObject<CT>>()
    /**
     * Support for the title property
     * When set, and title is also set, beside it, an Info icon appears
     * When hovered, titleHelp appears as a tooltip.
     */
    content?: string;

    @ControlObjectProperty<StaticContentProperties<CT>, StaticContentControlObject<CT>>()
    // Controls the number of visible lines. By default all lines are visible.
    maxVisibleLines?: number;
}
