/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import * as React from 'react';
import { connect } from 'react-redux';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-utils';
import type { StaticContentComponentProps } from './static-content-types';
import * as showdown from 'showdown';
import { escape as lodashEscape } from 'lodash';
import { useClampText } from 'use-clamp-text';
import { localize } from '../../../service/i18n-service';

const converter = new showdown.Converter();

export function StaticContentComponent({
    fieldProperties,
    screenId,
    value,
    elementId,
    ...rest
}: StaticContentComponentProps): React.ReactElement {
    const [isExpanded, setIsExpanded] = React.useState(false);

    const content = React.useMemo(() => value || fieldProperties.content, [fieldProperties.content, value]);

    const text = React.useMemo(
        () => (fieldProperties.isMarkdown ? lodashEscape(content ?? '') : (content ?? '')),
        [content, fieldProperties.isMarkdown],
    );

    const [ref, { noClamp, clampedText, key }] = useClampText({
        text,
        ellipsis: '...',
        lines: fieldProperties.maxVisibleLines ?? Number.POSITIVE_INFINITY,
        expanded: false,
    });

    const displayedText = React.useMemo(() => (isExpanded ? text : clampedText), [clampedText, isExpanded, text]);

    const getClickHandler = React.useCallback((): void => {
        triggerFieldEvent(screenId, elementId, 'onClick');
    }, [elementId, screenId]);

    const resolvedTitle = React.useMemo(
        () =>
            resolveByValue({
                screenId,
                propertyValue: fieldProperties.title,
                skipHexFormat: true,
                fieldValue: value,
                rowValue: null, // Not available as nested field
            }),
        [fieldProperties.title, screenId, value],
    );

    const hasLabel = React.useMemo(
        () => resolvedTitle && !fieldProperties.isTitleHidden,
        [fieldProperties.isTitleHidden, resolvedTitle],
    );

    const hasHelperText = React.useMemo(
        () => fieldProperties.helperText && !fieldProperties.isHelperTextHidden,
        [fieldProperties.helperText, fieldProperties.isHelperTextHidden],
    );

    const toggleExpanded = React.useCallback(() => {
        setIsExpanded(prevExpanded => !prevExpanded);
    }, []);

    const showLess = React.useMemo(() => localize('@sage/xtrem-ui/show-less', 'Show less'), []);

    const showMore = React.useMemo(() => localize('@sage/xtrem-ui/show-more', 'Show more'), []);

    return (
        <CarbonWrapper
            {...rest}
            fieldProperties={fieldProperties}
            screenId={screenId}
            elementId={elementId}
            className="e-static-content-field"
            componentName="static-content"
            noReadOnlySupport={false}
            value={value || fieldProperties.content}
        >
            {hasLabel && <FieldLabel label={resolvedTitle} />}
            <div className="e-static-content-field-wrapper">
                {fieldProperties.isMarkdown ? (
                    <span
                        key={key}
                        ref={ref}
                        className="e-dialog-text-content"
                        onClick={getClickHandler}
                        // eslint-disable-next-line react/no-danger
                        dangerouslySetInnerHTML={{
                            __html: converter.makeHtml(displayedText),
                        }}
                    />
                ) : (
                    <pre key={key} ref={ref as any} onClick={getClickHandler}>
                        {displayedText}
                    </pre>
                )}
                {!noClamp && (
                    <button className="e-static-content-field-button" type="button" onClick={toggleExpanded}>
                        {isExpanded ? showLess : showMore}
                    </button>
                )}
            </div>
            {hasHelperText && <HelperText helperText={fieldProperties.helperText} />}
        </CarbonWrapper>
    );
}

export const ConnectedStaticContentComponent = connect(mapStateToProps(), mapDispatchToProps())(StaticContentComponent);

export default ConnectedStaticContentComponent;
