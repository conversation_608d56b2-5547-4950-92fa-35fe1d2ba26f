import type { Page } from '../../../..';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata } from '../../../../__tests__/test-helpers';
import { staticContentField } from '../static-content-decorator';
import type { StaticContentDecoratorProperties } from '../static-content-types';

describe('StaticContent decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'staticContentField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should set default values when no component properties provided', () => {
        staticContentField({})({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: StaticContentDecoratorProperties<ScreenBase> =
            pageMetadata.uiComponentProperties[fieldId];
        expect(mappedComponentProperties.onClick).toBeUndefined();
        expect(mappedComponentProperties.content).toBeUndefined();
        expect(mappedComponentProperties.title).toBeUndefined();
        expect(mappedComponentProperties.helperText).toBeUndefined();
    });

    it('should set the content', () => {
        staticContentField({ content: 'Test content' })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: StaticContentDecoratorProperties<ScreenBase> =
            pageMetadata.uiComponentProperties[fieldId];
        expect(mappedComponentProperties.content).toEqual('Test content');
    });
});
