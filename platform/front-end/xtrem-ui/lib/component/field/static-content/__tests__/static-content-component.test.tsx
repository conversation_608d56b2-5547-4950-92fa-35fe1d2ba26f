import { renderWithRedux, applyActionMocks } from '../../../../__tests__/test-helpers';

import * as React from 'react';
import type { StaticContentDecoratorProperties } from '../static-content-types';
import type { ScreenBase } from '../../../../service/screen-base';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import ConnectedStaticContentComponent from '../static-content-component';
import '@testing-library/jest-dom';
import { waitFor } from '@testing-library/react';

const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'clientHeight');
const SIZE = 50;
beforeAll(() => {
    Object.defineProperty(HTMLElement.prototype, 'clientHeight', { configurable: true, value: SIZE });
});

afterAll(() => {
    Object.defineProperty(HTMLElement.prototype, 'clientHeight', originalOffsetHeight ?? {});
});

describe('static content component', () => {
    const screenId = 'TestPage';
    const fieldId = 'test-static-content-field';

    const setup = (
        staticContentProps: StaticContentDecoratorProperties<ScreenBase> = {},
        value: FieldInternalValue<FieldKey.StaticContent> | null = 'Value of the field',
    ) => {
        const initialState = {
            screenDefinitions: {
                [screenId]: {
                    metadata: {
                        uiComponentProperties: {
                            [fieldId]: staticContentProps,
                        },
                    },
                    values: {
                        ...(value && { [fieldId]: value as any }),
                    },
                    errors: {},
                },
            },
        };
        const utils = renderWithRedux<FieldKey.StaticContent, any>(
            <ConnectedStaticContentComponent screenId={screenId} elementId={fieldId} />,
            {
                initialState,
                fieldType: FieldKey.StaticContent,
                fieldValue: value as any,
                fieldProperties: staticContentProps,
                elementId: fieldId,
                screenId,
                mockActions: true,
            },
        ) as any;

        const staticContentField = utils.getByTestId('e-field-bind-test-static-content-field', { exact: false });
        const staticContentBody = staticContentField.getElementsByTagName('pre');

        return {
            ...utils,
            staticContentField,
            staticContentInput: staticContentBody[0],
        };
    };

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    it('can render with redux with defaults', async () => {
        const { staticContentField, staticContentInput } = setup({ title: 'Test Field Title' });
        await waitFor(() => {
            expect(staticContentField).toHaveTextContent('Test Field Title');
            expect(staticContentInput).toBeInTheDocument();
        });
    });

    it('can render with redux with value', async () => {
        const { staticContentInput } = setup({ title: 'Test Field Title' }, 'This is the value');
        await waitFor(() => {
            expect(staticContentInput).toHaveTextContent('This is the value');
        });
    });

    it('should render the content if no value is available', async () => {
        const { staticContentInput } = setup({ title: 'Test Field Title', content: 'This is the content' }, null);
        await waitFor(() => {
            expect(staticContentInput).toHaveTextContent('This is the content');
        });
    });

    it('should render helperText', async () => {
        const { staticContentField } = setup({ title: 'Test Field Title', helperText: 'This is a helper text' });
        await waitFor(() => {
            expect(staticContentField).toHaveTextContent('This is a helper text');
        });
    });
});
