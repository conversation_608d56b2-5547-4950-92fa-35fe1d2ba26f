PATH: XTREEM/UI+Field+Widgets/Static+Content+Field

## Introduction

A read-only field that displays its value without an input wrapper. The value is rendered as a normal paragraph.

## Example

```ts
@ui.decorators.staticContentField<StaticContentField>({
    parent() {
        return this.block;
    },
    title: 'The title of the field',
    helperText:'This goes below the field',
    content:'This is a \ntranslatable static decorator property',
})
textField: ui.fields.StaticContent;
```

### Display decorator properties

- **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
- **helperText**: The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.
- **isFullWidth**: Whether a field should take the full width of the screen.
- **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
- **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
- **content**: If the field doesn't have a value, this static text is displayed. It is automatically picked up by the i18n engine and externalized for translation.
- **maxVisibleLines**: Controls the number of visible lines. By default all lines are visible.
- **isMarkdown**: Parses the value or the content decorator property as markdown.

### Binding decorator properties

- **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
- **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
- **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

### Event handler decorator properties

- **onClick**: Triggered when any parts of the field is clicked, no arguments provided.
- **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

#### Runtime Functions

- **refresh()**: Refetches the field's values from the server and updates the user interface.
- **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
