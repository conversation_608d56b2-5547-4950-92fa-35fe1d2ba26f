import React from 'react';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import type { DatetimeRangeValue, NestedDatetimeRangeProperties } from './datetime-range-types';
import type { Datetime } from '@sage/xtrem-date-time';
import { formatDatetime } from '../../ui/datetime/datetime-utils';

export const DatetimeRangeCellRenderer: React.FC<
    CellParams<NestedDatetimeRangeProperties, DatetimeRangeValue | undefined>
> = React.memo(props => {
    const fieldProperties = React.useMemo(() => props.fieldProperties, [props.fieldProperties]);

    const value = React.useMemo(() => props.value, [props.value]);

    const start = React.useMemo(
        () =>
            (value?.start as Datetime)
                ? formatDatetime({ date: value?.start as Datetime, locale: props.locale, separator: ' ' })
                : '',
        [props.locale, value?.start],
    );

    const end = React.useMemo(
        () =>
            (value?.end as Datetime)
                ? formatDatetime({ date: value?.end as Datetime, locale: props.locale, separator: ' ' })
                : '',
        [props.locale, value?.end],
    );

    const dataTestId = React.useMemo(
        () => `${props.tableElementId}-${props.node.rowIndex}-${props.api.getColumns()!.indexOf(props.column!) + 1}`,
        [props.api, props.column, props.node.rowIndex, props.tableElementId],
    );

    return (
        <fieldProperties.wrapper {...props}>
            {start === '' && end === '' ? null : (
                <div data-testid={dataTestId} className="e-datetime-cell-renderer">
                    <span data-testid={`${dataTestId}-start`} className="e-datetime-cell-renderer-start">
                        {start}
                    </span>
                    <span>-</span>
                    <span data-testid={`${dataTestId}-end`} className="e-datetime-cell-renderer-end">
                        {end}
                    </span>
                </div>
            )}
        </fieldProperties.wrapper>
    );
});

DatetimeRangeCellRenderer.displayName = 'DatetimeRangeCellRenderer';
