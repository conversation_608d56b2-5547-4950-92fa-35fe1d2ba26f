import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { DatetimeRangeComponentProps } from './datetime-range-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedDatetimeRangeComponent = React.lazy(() => import('./datetime-range-component'));

export function AsyncConnectedDatetimeRangeComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedDatetimeRangeComponent {...props} />
        </React.Suspense>
    );
}

const DatetimeRangeComponent = React.lazy(() =>
    import('./datetime-range-component').then(c => ({ default: c.DatetimeRangeComponent })),
);

export function AsyncDatetimeRangeComponent(props: DatetimeRangeComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <DatetimeRangeComponent {...props} />
        </React.Suspense>
    );
}
