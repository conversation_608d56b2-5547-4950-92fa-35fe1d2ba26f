import { Datetime } from '@sage/xtrem-date-time';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import { DatetimeRangeControlObject } from '../../../control-objects';
import type { FieldKey } from '../../../types';
import type { DatetimeRangeProperties, DatetimeRangeValue } from '../datetime-range-types';

describe('DatetimeRange control object', () => {
    let controlObject: DatetimeRangeControlObject;
    let datetimeRangeProperties: DatetimeRangeProperties<any>;
    let datetimeRangeValue: DatetimeRangeValue;

    beforeEach(() => {
        datetimeRangeProperties = {
            title: 'TEST_FIELD_TITLE',
        };
        datetimeRangeValue = {
            start: Datetime.make(2024, 1, 1, 1, 0, 0, 0),
            end: Datetime.make(2024, 1, 1, 1, 0, 0, 0),
        };
        controlObject = buildControlObject<FieldKey.DatetimeRange>(DatetimeRangeControlObject, {
            fieldValue: datetimeRangeValue,
            fieldProperties: datetimeRangeProperties,
        });
    });

    it('should be able to get the value', () => {
        expect(controlObject.value).toEqual(datetimeRangeValue);
    });

    it('should be able to set the value', () => {
        const newValue = { start: Datetime.make(2024, 1, 1, 1, 0, 0, 0), end: Datetime.make(2024, 1, 1, 1, 0, 0, 0) };
        controlObject.value = newValue;
        expect(controlObject.value).toEqual(newValue);
    });

    it('should be able to set the value to null', () => {
        controlObject.value = null;
        expect(controlObject.value).toEqual(null);
    });

    it('should be able to set the title', () => {
        const title = 'Test Title';
        expect(datetimeRangeProperties.title).not.toEqual(title);
        controlObject.title = title;
        expect(datetimeRangeProperties.title).toEqual(title);
    });

    it('should be able to set the helper text', () => {
        const helperText = 'Test Title';
        expect(datetimeRangeProperties.title).not.toEqual(helperText);
        controlObject.helperText = helperText;
        expect(datetimeRangeProperties.helperText).toEqual(helperText);
    });

    it('should be able to get/set the minDate', () => {
        expect(controlObject.minDate).toEqual(undefined);
        const minDate = '2022-01-01T00:00:00';
        controlObject.minDate = minDate;
        expect(controlObject.minDate).toEqual(minDate);
    });

    it('should be able to get/set the maxDate', () => {
        expect(controlObject.maxDate).toEqual(undefined);
        const maxDate = '2022-01-01T00:00:00';
        controlObject.maxDate = maxDate;
        expect(controlObject.maxDate).toEqual(maxDate);
    });

    it('should be able to get/set the defaultDate', () => {
        expect(controlObject.defaultDate).toEqual(undefined);
        const defaultDate = '2022-01-01T00:00:00';
        controlObject.defaultDate = defaultDate;
        expect(controlObject.defaultDate).toEqual(defaultDate);
    });

    it('should be able to get/set the isTimeZoneHidden', () => {
        expect(controlObject.isTimeZoneHidden).toEqual(undefined);
        controlObject.isTimeZoneHidden = true;
        expect(controlObject.isTimeZoneHidden).toEqual(true);
    });

    it('should be able to get/set the timeZone', () => {
        expect(controlObject.timeZone).toEqual(undefined);
        controlObject.timeZone = 'UTC';
        expect(controlObject.timeZone).toEqual('UTC');
    });
});
