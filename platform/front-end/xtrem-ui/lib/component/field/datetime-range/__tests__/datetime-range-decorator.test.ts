import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import * as pageMetaData from '../../../../service/page-metadata';
import { dateTimeRangeField } from '../datetime-range-decorator';

describe('DatetimeRange Decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'dateTimeRangeField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(dateTimeRangeField, pageMetadata, fieldId);
        });
    });
});
