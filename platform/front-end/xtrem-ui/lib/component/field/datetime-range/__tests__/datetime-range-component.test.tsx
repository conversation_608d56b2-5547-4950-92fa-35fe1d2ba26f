import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import '@testing-library/jest-dom';
import { fireEvent, render, waitFor, screen } from '@testing-library/react';
import * as React from 'react';
import { Provider } from 'react-redux';
import * as xtremRedux from '../../../../redux';
import type { ScreenBase } from '../../../../service/screen-base';
import { FieldKey } from '../../../types';
import ConnectedDatetimeRangeComponent from '../datetime-range-component';
import type { DatetimeRangeDecoratorProperties } from '../datetime-range-types';
import { DateValue, Datetime } from '@sage/xtrem-date-time';
import { normalizeUnderscoreBind } from '../../../../utils/abstract-fields-utils';

jest.useFakeTimers();

const screenId = 'TestPage';
const fieldId = 'test-datetime-range-field';
const fieldTitle = 'Test Field Title';
const START_DATE_LABEL = 'Test Field Title - Start';
const END_DATE_LABEL = 'Test Field Title - End';
const START_TIME_INPUT_HOURS = 'e-datetime-input-start-time-input-hours';
const START_TIME_INPUT_MINUTES = 'e-datetime-input-start-time-input-minutes';
const END_TIME_INPUT = 'e-datetime-input-end';
const END_TIME_INPUT_HOURS = 'e-datetime-input-end-time-input-hours';
const END_TIME_INPUT_MINUTES = 'e-datetime-input-end-time-input-minutes';
const TIMEZONE_INPUT_START = 'e-datetime-input-start-time-input-timezone';
const TIMEZONE_INPUT_END = 'e-datetime-input-end-time-input-timezone';
const OPEN_TIMEZONE_BUTTON_START = 'e-time-component-open-timezone-start';
const OPEN_TIMEZONE_BUTTON_END = 'e-time-component-open-timezone-end';
const START_DATE_INFINITE_CHECKBOX = 'e-datetime-input-start-date-picker-checkbox';
const END_DATE_INFINITE_CHECKBOX = 'e-datetime-input-end-date-picker-checkbox';

describe('DatetimeRange Component', () => {
    let setValueMock;

    const getTodayLabel = (selected: boolean = false): string => {
        const today = new Date();
        const day = today.toLocaleDateString('en-US', { weekday: 'long' });
        const date = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
        return `Today, ${day}, ${date}${selected ? ' selected' : ''}`;
    };

    const clickTimePeriodButton = (buttonLabel: 'AM' | 'PM') => {
        fireEvent.click(screen.getByRole('button', { name: buttonLabel }));
    };

    function Wrapper({ mockStore, testFieldId }: { mockStore: any; testFieldId: string }): React.ReactNode {
        React.useLayoutEffect(() => {
            const start = document.getElementById(`--${screenId}-bind-${normalizeUnderscoreBind(fieldId)}-start`);
            const end = document.getElementById(`--${screenId}-bind-${normalizeUnderscoreBind(fieldId)}-end`);
            document.querySelector('[data-testid="e-datetime-input-start"]')!.addEventListener('blur', () => {
                start!.hidePopover();
            });
            document.querySelector('[data-testid="e-datetime-input-end"]')!.addEventListener('blur', () => {
                end!.hidePopover();
            });
            start!.style.display = 'none';
            end!.style.display = 'none';
        }, []);
        return (
            <Provider store={mockStore}>
                <ConnectedDatetimeRangeComponent screenId={screenId} elementId={testFieldId} />
            </Provider>
        );
    }

    const setup = (fieldProps?: Partial<DatetimeRangeDecoratorProperties<ScreenBase, any>>) => {
        const fieldProperties = {
            title: fieldTitle,
            ...fieldProps,
        };
        setValueMock = jest
            .spyOn(xtremRedux.actions, 'setFieldValue')
            .mockReturnValue(() => Promise.resolve({ type: 'SetFieldValue' }) as any);

        const state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        addFieldToState(FieldKey.DatetimeRange, state, screenId, fieldId, fieldProperties, null);
        const mockStore = getMockStore(state);
        return <Wrapper mockStore={mockStore} testFieldId={fieldId} />;
    };

    beforeEach(() => {
        HTMLElement.prototype.showPopover = jest.fn().mockImplementation(function _(this: HTMLElement) {
            const type = this.getAttribute('data-type');
            const popover = document.getElementById(`--${screenId}-bind-${normalizeUnderscoreBind(fieldId)}-${type}`);
            if (popover) {
                popover.style.display = 'absolute';
                popover.style.inset = '0';
                popover.style.top = '50px';
                popover.style.left = '0';
            }
        });
        HTMLElement.prototype.hidePopover = jest.fn().mockImplementation(function _(this: HTMLElement) {
            const type = this.getAttribute('data-type');
            const popover = document.getElementById(`--${screenId}-bind-${normalizeUnderscoreBind(fieldId)}-${type}`);
            if (popover) {
                popover.style.display = 'none';
            }
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should render with redux with defaults', async () => {
        render(setup());

        const startDateInput = screen.getByLabelText(START_DATE_LABEL);
        fireEvent.focus(startDateInput);

        const todayLabel = getTodayLabel();

        expect(screen.getAllByLabelText(todayLabel)).toHaveLength(2);
        expect(screen.getByTestId(OPEN_TIMEZONE_BUTTON_START)).toBeDisabled();
        fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
        expect(screen.getAllByLabelText(todayLabel)).toHaveLength(2);
    });

    it('should render with redux with default timezone', async () => {
        render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0, 'GMT') }));

        const startDateInput = screen.getByLabelText(START_DATE_LABEL);
        fireEvent.focus(startDateInput);

        fireEvent.click(screen.getByTestId(OPEN_TIMEZONE_BUTTON_START));

        const startDateTimeZone = screen.getByTestId(TIMEZONE_INPUT_START);
        expect(startDateTimeZone).toHaveValue('GMT');

        fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
        fireEvent.click(screen.getByTestId(OPEN_TIMEZONE_BUTTON_END));
        const endDateTimeZone = screen.getByTestId(TIMEZONE_INPUT_END);
        expect(endDateTimeZone).toHaveValue('GMT');
    });

    it('should trigger setFieldValue with currentDates when both start and end datetimes are valid', async () => {
        render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0, 'GMT') }));

        fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));

        fireEvent.click(screen.getByRole('button', { name: 'Sunday, June 11, 2023' }));

        fireEvent.click(screen.getByTestId(START_TIME_INPUT_HOURS));
        fireEvent.change(screen.getByTestId(START_TIME_INPUT_HOURS), {
            target: { value: '08' },
        });
        fireEvent.blur(screen.getByTestId(START_TIME_INPUT_HOURS));

        fireEvent.click(screen.getByTestId(START_TIME_INPUT_MINUTES));
        fireEvent.change(screen.getByTestId(START_TIME_INPUT_MINUTES), {
            target: { value: '00' },
        });
        fireEvent.blur(screen.getByTestId(START_TIME_INPUT_MINUTES));
        fireEvent.blur(screen.getByLabelText(START_DATE_LABEL));

        fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
        fireEvent.click(screen.getByRole('button', { name: 'Friday, June 16, 2023' }));

        fireEvent.focus(screen.getByTestId(END_TIME_INPUT_HOURS));
        fireEvent.change(screen.getByTestId(END_TIME_INPUT_HOURS), {
            target: { value: '10' },
        });
        fireEvent.blur(screen.getByTestId(END_TIME_INPUT_HOURS));
        fireEvent.focus(screen.getByTestId(END_TIME_INPUT_MINUTES));
        fireEvent.change(screen.getByTestId(END_TIME_INPUT_MINUTES), {
            target: { value: '00' },
        });
        fireEvent.blur(screen.getByTestId(END_TIME_INPUT_MINUTES));

        fireEvent.click(document.body);

        await waitFor(() => {
            expect(setValueMock).toHaveBeenLastCalledWith(
                screenId,
                fieldId,
                {
                    start: Datetime.make(2023, 6, 11, 8, 0, 0, 0, 'GMT'),
                    end: Datetime.make(2023, 6, 16, 10, 0, 0, 0, 'GMT'),
                },
                true,
            );
        });
    });

    it('should invalidate end date if start date is later than end date', async () => {
        render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0, 'GMT') }));

        fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));

        fireEvent.click(await screen.findByRole('button', { name: 'Tuesday, June 13, 2023' }));

        await waitFor(() => {
            expect(screen.getByLabelText(END_DATE_LABEL).getAttribute('value')).toBe('');
        });

        fireEvent.click(document.body);

        await waitFor(() => {
            expect(setValueMock).toHaveBeenLastCalledWith(
                screenId,
                fieldId,
                { end: null, start: Datetime.make(2023, 6, 13, 0, 0, 0, 0, 'GMT') },
                true,
            );
        });
    });

    it('should invalidate end date if user makes start date later than end date by changing start date time period', async () => {
        render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0, 'GMT') }));

        fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));

        clickTimePeriodButton('PM');

        await waitFor(() => {
            expect(screen.getByLabelText(END_DATE_LABEL).getAttribute('value')).toBe('');
        });

        fireEvent.click(document.body);

        await waitFor(() => {
            expect(setValueMock).toHaveBeenLastCalledWith(
                screenId,
                fieldId,
                { end: null, start: Datetime.make(2023, 6, 12, 12, 0, 0, 0, 'GMT') },
                true,
            );
        });
    });

    it('should show an error if user makes end date earlier than start date by changing end date time period', async () => {
        render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0, 'GMT') }));

        fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
        clickTimePeriodButton('PM');
        fireEvent.blur(screen.getByLabelText(END_DATE_LABEL));

        fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
        clickTimePeriodButton('PM');
        fireEvent.blur(screen.getByLabelText(START_DATE_LABEL));

        fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));

        clickTimePeriodButton('AM');

        await waitFor(() => {
            expect(screen.getByTestId(END_TIME_INPUT).getAttribute('aria-invalid')).toBe('true');
        });

        fireEvent.click(document.body);
    });

    it('should clear the error if user corrects end date to be later than start date by changing end date time period', async () => {
        render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0, 'GMT') }));

        fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
        clickTimePeriodButton('PM');
        fireEvent.blur(screen.getByLabelText(END_DATE_LABEL));

        fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
        clickTimePeriodButton('PM');
        fireEvent.blur(screen.getByLabelText(START_DATE_LABEL));

        fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
        clickTimePeriodButton('AM');

        await waitFor(() => {
            expect(screen.getByTestId(END_TIME_INPUT).getAttribute('aria-invalid')).toBe('true');
        });

        clickTimePeriodButton('PM');

        await waitFor(() => {
            expect(screen.getByTestId(END_TIME_INPUT).getAttribute('aria-invalid')).toBe('false');
        });

        fireEvent.click(document.body);

        await waitFor(() => {
            expect(setValueMock).toHaveBeenCalled();
        });
    });

    it('should clear the error if user corrects start date to be earlier than end date by changing start date time period', async () => {
        render(setup({ defaultDate: Datetime.make(2023, 6, 12, 15, 0, 0, 0, 'GMT') }));

        fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
        clickTimePeriodButton('AM');
        fireEvent.blur(screen.getByLabelText(END_DATE_LABEL));

        await waitFor(() => {
            expect(screen.getByTestId(END_TIME_INPUT).getAttribute('aria-invalid')).toBe('true');
        });
        fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));

        clickTimePeriodButton('AM');

        await waitFor(() => {
            expect(screen.getByTestId(END_TIME_INPUT).getAttribute('aria-invalid')).toBe('false');
        });

        fireEvent.click(document.body);

        await waitFor(() => {
            expect(setValueMock).toHaveBeenCalled();
        });
    });

    it('should show an error if dates are equal and user enters an end time earlier than start time', async () => {
        render(setup({ defaultDate: Datetime.make(2023, 6, 12, 1, 0, 0, 0, 'GMT') }));

        fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
        fireEvent.focus(screen.getByTestId(END_TIME_INPUT_HOURS));
        fireEvent.change(screen.getByTestId(END_TIME_INPUT_HOURS), {
            target: { value: '0' },
        });
        fireEvent.blur(screen.getByTestId(END_TIME_INPUT_HOURS));

        expect(screen.getByTestId(END_TIME_INPUT).getAttribute('aria-invalid')).toBe('true');

        fireEvent.click(document.body);
    });

    it('should clear the error if dates are equal and user enters an end time later than start time', async () => {
        render(setup({ defaultDate: Datetime.make(2023, 6, 12, 1, 0, 0, 0, 'GMT') }));

        fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
        fireEvent.focus(screen.getByTestId(END_TIME_INPUT_HOURS));
        fireEvent.change(screen.getByTestId(END_TIME_INPUT_HOURS), {
            target: { value: '0' },
        });
        fireEvent.blur(screen.getByTestId(END_TIME_INPUT_HOURS));

        expect(screen.getByTestId(END_TIME_INPUT).getAttribute('aria-invalid')).toBe('true');

        fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
        fireEvent.focus(screen.getByTestId(END_TIME_INPUT_HOURS));
        fireEvent.change(screen.getByTestId(END_TIME_INPUT_HOURS), {
            target: { value: '2' },
        });
        fireEvent.blur(screen.getByTestId(END_TIME_INPUT_HOURS));

        fireEvent.click(document.body);

        expect(screen.getByTestId(END_TIME_INPUT).getAttribute('aria-invalid')).toBe('false');

        await waitFor(() => {
            expect(setValueMock).toHaveBeenCalled();
        });
    });

    it('should invalidate end date and time if dates are equal and user enters a start time is later than end time', async () => {
        render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0, 'GMT') }));
        const expectedDate = Datetime.make(2023, 6, 12, 1, 0, 0, 0, 'GMT');
        fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
        fireEvent.click(screen.getByTestId(START_TIME_INPUT_HOURS));
        fireEvent.change(screen.getByTestId(START_TIME_INPUT_HOURS), {
            target: { value: '01' },
        });
        fireEvent.blur(screen.getByTestId(START_TIME_INPUT_HOURS));

        fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));

        await waitFor(() => {
            expect(screen.getByLabelText(END_DATE_LABEL).getAttribute('value')).toBe('');
            expect(screen.getByTestId(END_TIME_INPUT_HOURS).getAttribute('value')).toBe('');
            expect(screen.getByTestId(END_TIME_INPUT_MINUTES).getAttribute('value')).toBe('');
        });

        fireEvent.click(document.body);

        await waitFor(() => {
            expect(setValueMock).toHaveBeenLastCalledWith(screenId, fieldId, { end: null, start: expectedDate }, true);
        });
    });

    describe('AM/PM toggle', () => {
        it('should show PM when default time is in the afternoon', async () => {
            render(setup({ defaultDate: Datetime.make(2023, 6, 12, 15, 0, 0, 0, 'GMT') }));

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            fireEvent.click(screen.getByTestId(START_TIME_INPUT_HOURS));

            expect(screen.getByRole('button', { name: 'PM' })).toBeInTheDocument();
        });
        it('should show AM when default time is in the morning', async () => {
            render(setup({ defaultDate: Datetime.make(2023, 6, 12, 6, 0, 0, 0, 'GMT') }));

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            fireEvent.click(screen.getByTestId(START_TIME_INPUT_HOURS));

            expect(screen.getByRole('button', { name: 'AM' })).toBeInTheDocument();
        });
    });

    describe('Timezone', () => {
        it('should show the default timezone', async () => {
            render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0) }));

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            fireEvent.click(screen.getByTestId(START_TIME_INPUT_HOURS));
            fireEvent.click(screen.getByTestId(OPEN_TIMEZONE_BUTTON_START));
            expect(screen.getByTestId(TIMEZONE_INPUT_START)).toHaveValue('GMT');
        });

        it('should show the correct timezone', async () => {
            render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0), timeZone: 'Europe/London' }));

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            fireEvent.click(screen.getByTestId(START_TIME_INPUT_HOURS));
            fireEvent.click(screen.getByTestId(OPEN_TIMEZONE_BUTTON_START));

            expect(screen.getByTestId(TIMEZONE_INPUT_START)).toHaveValue('Europe/London');
        });
        it('should show the correct timezone when the default date is in a different timezone', async () => {
            render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0), timeZone: 'Europe/London' }));

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            fireEvent.click(screen.getByTestId(START_TIME_INPUT_HOURS));
            fireEvent.click(screen.getByTestId(OPEN_TIMEZONE_BUTTON_START));

            expect(screen.getByTestId(TIMEZONE_INPUT_START)).toHaveValue('Europe/London');
        });
    });
    describe('Date range selection', () => {
        it('should show the dates between the start and end date with the right class applied', async () => {
            render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0, 'GMT') }));

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            fireEvent.click(screen.getByRole('button', { name: 'Sunday, June 11, 2023' }));
            fireEvent.blur(screen.getByLabelText(START_DATE_LABEL));

            fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
            fireEvent.click(screen.getByRole('button', { name: 'Friday, June 16, 2023' }));
            fireEvent.blur(screen.getByLabelText(END_DATE_LABEL));

            await waitFor(() => {
                const start = document.body.querySelectorAll('.is-first-selected-in-range');
                expect(start).toHaveLength(1);
                expect(start[0].getAttribute('aria-label')).toBe('Sunday, June 11, 2023');
                const end = document.body.querySelectorAll('.is-last-selected-in-range');
                expect(end).toHaveLength(1);
                expect(end[0].getAttribute('aria-label')).toBe('Friday, June 16, 2023 selected');
                const interSelectedDates = document.body.querySelectorAll('.is-inter-selected');
                expect(interSelectedDates).toHaveLength(4);
            });
        });
    });

    describe('min - max date', () => {
        beforeAll(() => {
            jest.useFakeTimers();
            jest.setSystemTime(new Date(2025, 6, 8, 12));
        });

        afterAll(() => {
            jest.useRealTimers();
        });

        it('should disable all dates before minDate and after maxDate (as Date)', async () => {
            const minDate = new Date(2025, 6, 8);
            const maxDate = new Date(2025, 6, 10);
            render(setup({ minDate, maxDate }));

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            const calendarButtons = screen.getAllByRole('button');

            const beforeMin = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 7, 2025'));
            expect(beforeMin).toHaveAttribute('aria-disabled', 'true');

            const min = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 8, 2025'));
            const minAria = min?.getAttribute('aria-disabled');
            expect(minAria === null || minAria === 'false').toBe(true);

            const max = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 10, 2025'));
            const maxAria = max?.getAttribute('aria-disabled');
            expect(maxAria === null || maxAria === 'false').toBe(true);

            const afterMax = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 11, 2025'));
            expect(afterMax).toHaveAttribute('aria-disabled', 'true');
        });

        it('should disable all dates before minDate and after maxDate (as string)', async () => {
            const minDate = '2025-07-08';
            const maxDate = '2025-07-10';
            render(setup({ minDate, maxDate }));

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            const calendarButtons = screen.getAllByRole('button');

            const beforeMin = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 7, 2025'));
            expect(beforeMin).toHaveAttribute('aria-disabled', 'true');

            const min = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 8, 2025'));
            const minAria = min?.getAttribute('aria-disabled');
            expect(minAria === null || minAria === 'false').toBe(true);

            const max = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 10, 2025'));
            const maxAria = max?.getAttribute('aria-disabled');
            expect(maxAria === null || maxAria === 'false').toBe(true);

            const afterMax = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 11, 2025'));
            expect(afterMax).toHaveAttribute('aria-disabled', 'true');
        });

        it('should disable all dates before minDate and after maxDate (as DateValue)', async () => {
            const minDate = DateValue.make(2025, 7, 8);
            const maxDate = DateValue.make(2025, 7, 10);
            render(setup({ minDate, maxDate }));

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            const calendarButtons = screen.getAllByRole('button');

            const beforeMin = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 7, 2025'));
            expect(beforeMin).toHaveAttribute('aria-disabled', 'true');

            const min = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 8, 2025'));
            const minAria = min?.getAttribute('aria-disabled');
            expect(minAria === null || minAria === 'false').toBe(true);

            const max = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 10, 2025'));
            const maxAria = max?.getAttribute('aria-disabled');
            expect(maxAria === null || maxAria === 'false').toBe(true);

            const afterMax = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 11, 2025'));
            expect(afterMax).toHaveAttribute('aria-disabled', 'true');
        });

        it('should accept minDate and maxDate as callbacks', async () => {
            const minDate = () => new Date(2025, 6, 8);
            const maxDate = () => new Date(2025, 6, 10);
            render(setup({ minDate, maxDate }));

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            const calendarButtons = screen.getAllByRole('button');

            const beforeMin = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 7, 2025'));
            expect(beforeMin).toHaveAttribute('aria-disabled', 'true');

            const min = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 8, 2025'));
            const minAria = min?.getAttribute('aria-disabled');
            expect(minAria === null || minAria === 'false').toBe(true);

            const max = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 10, 2025'));
            const maxAria = max?.getAttribute('aria-disabled');
            expect(maxAria === null || maxAria === 'false').toBe(true);

            const afterMax = calendarButtons.find(btn => btn.getAttribute('aria-label')?.includes('July 11, 2025'));
            expect(afterMax).toHaveAttribute('aria-disabled', 'true');
        });
    });

    describe('Infinite checkbox', () => {
        it('should set start date to null when infinite checkbox is checked', async () => {
            render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0, 'GMT') }));

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            fireEvent.click(screen.getByRole('button', { name: 'Sunday, June 11, 2023' }));
            fireEvent.blur(screen.getByLabelText(START_DATE_LABEL));
            fireEvent.click(screen.getByTestId(START_TIME_INPUT_HOURS));
            fireEvent.change(screen.getByTestId(START_TIME_INPUT_HOURS), {
                target: { value: '08' },
            });
            fireEvent.blur(screen.getByTestId(START_TIME_INPUT_HOURS));
            fireEvent.click(screen.getByTestId(START_TIME_INPUT_MINUTES));
            fireEvent.change(screen.getByTestId(START_TIME_INPUT_MINUTES), {
                target: { value: '00' },
            });
            fireEvent.blur(screen.getByTestId(START_TIME_INPUT_MINUTES));

            fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
            fireEvent.click(screen.getByRole('button', { name: 'Sunday, June 11, 2023' }));
            fireEvent.blur(screen.getByLabelText(END_DATE_LABEL));
            fireEvent.click(screen.getByTestId(END_TIME_INPUT_HOURS));
            fireEvent.change(screen.getByTestId(END_TIME_INPUT_HOURS), {
                target: { value: '08' },
            });
            fireEvent.blur(screen.getByTestId(END_TIME_INPUT_HOURS));
            fireEvent.click(screen.getByTestId(END_TIME_INPUT_MINUTES));
            fireEvent.change(screen.getByTestId(END_TIME_INPUT_MINUTES), {
                target: { value: '00' },
            });
            fireEvent.blur(screen.getByTestId(END_TIME_INPUT_MINUTES));

            fireEvent.click(document.body);

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            fireEvent.click(screen.getByTestId(START_DATE_INFINITE_CHECKBOX));

            fireEvent.click(document.body);

            await waitFor(() => {
                expect(setValueMock).toHaveBeenLastCalledWith(
                    screenId,
                    fieldId,
                    { start: null, end: Datetime.make(2023, 6, 11, 8, 0, 0, 0, 'GMT') },
                    true,
                );
            });
        });

        it('should set end date to null when infinite checkbox is checked', async () => {
            render(setup({ defaultDate: Datetime.make(2023, 6, 12, 0, 0, 0, 0, 'GMT') }));

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));
            fireEvent.click(screen.getByRole('button', { name: 'Sunday, June 11, 2023' }));
            fireEvent.blur(screen.getByLabelText(START_DATE_LABEL));
            fireEvent.click(screen.getByTestId(START_TIME_INPUT_HOURS));
            fireEvent.change(screen.getByTestId(START_TIME_INPUT_HOURS), {
                target: { value: '08' },
            });
            fireEvent.blur(screen.getByTestId(START_TIME_INPUT_HOURS));

            fireEvent.click(screen.getByTestId(START_TIME_INPUT_MINUTES));
            fireEvent.change(screen.getByTestId(START_TIME_INPUT_MINUTES), {
                target: { value: '00' },
            });
            fireEvent.blur(screen.getByTestId(START_TIME_INPUT_MINUTES));

            fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));
            fireEvent.click(screen.getByRole('button', { name: 'Friday, June 16, 2023' }));
            fireEvent.click(screen.getByTestId(END_TIME_INPUT_HOURS));
            fireEvent.change(screen.getByTestId(END_TIME_INPUT_HOURS), {
                target: { value: '08' },
            });
            fireEvent.blur(screen.getByTestId(END_TIME_INPUT_HOURS));

            fireEvent.click(screen.getByTestId(END_TIME_INPUT_MINUTES));
            fireEvent.change(screen.getByTestId(END_TIME_INPUT_MINUTES), {
                target: { value: '00' },
            });
            fireEvent.blur(screen.getByTestId(END_TIME_INPUT_MINUTES));

            fireEvent.click(screen.getByTestId(END_DATE_INFINITE_CHECKBOX));
            fireEvent.click(document.body);

            await waitFor(() => {
                expect(screen.getByTestId(END_DATE_INFINITE_CHECKBOX)).toBeChecked();
                expect(setValueMock).toHaveBeenLastCalledWith(
                    screenId,
                    fieldId,
                    { start: Datetime.make(2023, 6, 11, 8, 0, 0, 0, 'GMT'), end: null },
                    true,
                );
            });
        });

        it('infinite should be checked if start date is null', async () => {
            render(setup());

            fireEvent.focus(screen.getByLabelText(START_DATE_LABEL));

            await waitFor(() => {
                expect(screen.getByTestId(START_DATE_INFINITE_CHECKBOX)).toBeChecked();
            });
        });

        it('infinite should be checked if end date is null', async () => {
            render(setup());

            fireEvent.focus(screen.getByLabelText(END_DATE_LABEL));

            await waitFor(() => {
                expect(screen.getByTestId(END_DATE_INFINITE_CHECKBOX)).toBeChecked();
            });
        });
    });
});
