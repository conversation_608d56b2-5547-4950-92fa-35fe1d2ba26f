/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { DatetimeRangeControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type {
    DatetimeRangeDecoratorProperties,
    DatetimeRangeExtensionDecoratorProperties,
} from './datetime-range-types';

class DatetimeRangeDecorator extends AbstractFieldDecorator<FieldKey.DatetimeRange> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = DatetimeRangeControlObject;
}

/**
 * Initializes the decorated member as a [DatetimeRange]{@link DatetimeRangeControlObject} field with the provided properties.
 *
 * @param properties The properties that the [DatetimeRange]{@link DatetimeRangeControlObject} field will be initialized with.
 */
export function dateTimeRangeField<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: DatetimeRangeDecoratorProperties<Extend<T>, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.DatetimeRange>(
        properties,
        DatetimeRangeDecorator,
        FieldKey.DatetimeRange,
        true,
    );
}

export function dateTimeRangeFieldOverride<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: DatetimeRangeExtensionDecoratorProperties<T, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.DatetimeRange>(properties);
}
