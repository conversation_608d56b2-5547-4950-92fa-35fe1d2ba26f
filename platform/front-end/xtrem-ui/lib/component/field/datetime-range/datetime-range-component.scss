.e-nested-cell-field-datetime-range {
    [data-element="calendar"]::before {
        font-size: var(--sizing175);
    }
}

.e-datetime-cell-renderer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.e-datetime-cell-renderer-start {
    flex: 1;
    text-align: right;
}

.e-datetime-cell-renderer-end {
    flex: 1;
    text-align: left;
}

.e-datetime-range-container {
    display: flex;
    justify-content: space-between;
    gap: 16px;

    &:focus {
        outline: 1px solid var(--colorsSemanticFocus500);
        box-shadow: inset 0 0 0 2px var(--colorsSemanticFocus500);
    }
}

.e-date-input-wrapper {
    position: relative;
    flex: 1;

    .e-input-title {
        color: var(--colorsUtilityYin090);
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 150%;
    }
}

.e-combined-input-wrapper {
    position: relative;
    width: 100%;
    display: flex;
    flex-flow: row nowrap;
    align-content: center;
    justify-content: center;
    align-items: center;

    >div {
        width: 100%;
    }

    .e-combined-input {
        font-size: 1em;
        border: 1px solid #ccc;
        border-radius: 5px;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        background-color: var(--colorsYang100);
        width: 100%;
        padding: 8px 30px 8px 8px;
        text-align: left;
        box-sizing: border-box;
    }

    .e-combined-input:focus {
        outline: none;
    }

    .e-calendar-icon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .e-infinite-indicator {
        align-self: flex-end;
        margin-bottom: 4px;
        margin-left: 8px;
    }
}

@position-try --top {
    inset: auto;
    left: anchor(left);
    bottom: anchor(top);
}

.e-popover-dialog {
    // progressive enhancement: only works in Chromium for now
    position-try-fallbacks: --top;
    position: absolute;
    inset: auto;
    margin: 0;
    margin-block-start: 1rem;
    width: 300px;
    padding: 0px;
    background-color: var(--colorsYang100);
    border: 1px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.e-calendar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 36px;
    margin-bottom: 10px;
    padding: 8px;



    button {
        background: none;
        border: none;
        font-size: 1.2em;
    }

    .e-month-selector-wrapper {
        display: flex;
        flex-grow: 1;
        position: relative;
        justify-content: center;
        align-items: center;

        span.navigate-month-icon::before {
            font-size: smaller;
            font-weight: 700;
            color: var(--colorsActionMinor500)
        }

        .navigate-month-button-left {
            left: 0;
        }

        .navigate-month-button-right {
            right: 0;
        }

        .navigate-month-button-right,
        .navigate-month-button-left {
            position: absolute;
            border-radius: 4px;

            &:focus {
                appearance: none;
                box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500), 0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
                outline: transparent solid 3px;
                z-index: 2;
            }
        }
    }

    .e-month-selector {
        color: var(--colorsYin090);
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        align-items: center;
        border: none;
        padding: 0;
        border-radius: 4px;

        &:focus {
            appearance: none;
            box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500), 0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
            outline: transparent solid 3px;
            z-index: 2;
        }
    }

    .e-year-selector {
        color: var(--colorsYin090);
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        width: 22%;
        padding: 0px;
        border: none;
        border-radius: 4px;


        &:focus {
            appearance: none;
            box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500), 0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
            outline: transparent solid 3px;
            z-index: 2;
        }
    }

    &[data-disabled="true"] {
        button {
            color: var(--colorsYin065);
        }

        span {
            color: var(--colorsYin065);
        }

        .e-month-selector-wrapper {
            color: var(--colorsYin065);

            .e-month-selector {
                color: var(--colorsYin065);
            }
        }

        .e-year-selector {
            color: var(--colorsYin065);
        }



    }
}

.e-calendar-container {
    padding: 8px;

    .e-calendar-infinite-container {
        padding: 4px;
    }
}

.e-time-component-container {
    padding: 0px 16px;
    background: var(--colorsUtilityMajor040, #EDF1F2);
    border-top: 1px solid #ddd;
    margin-top: 10px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;

    & .e-time-component-title {
        color: var(--colorsUtilityYin090);
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        margin: 8px 16px 0px 16px;

    }

    & .e-time-component-wrapper {
        padding-top: 16px;
        padding-bottom: 16px;
        display: flex;

        .e-time-field-container {
            width: 100%;
            --fieldSpacing: 0;
        }
    }
}

.e-button-container {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    background-color: var(--colorsYang100);
    border-top: 1px solid #ddd;
}


.e-time-component-timezone-container {
    display: flex;
    align-items: center;
    padding-top: 16px;
    padding-bottom: 16px;
}



.e-time-component-timezone-input {
    flex-grow: 1;
}

.e-button-container button {
    margin-left: 10px;
}

.e-calendar-date-cell {
    width: 40px;
    height: 40px;
    box-sizing: border-box;
}

.react-aria-Calendar {
    td[role="gridcell"] {
        padding-left: 0px;
        padding-right: 0px;
        line-height: 3;
        text-align: center;
    }
}

.react-aria-CalendarGridHeader {
    box-shadow: none;
    position: unset;

    th {
        text-align: center;
    }
}


.react-aria-CalendarCell {
    position: relative;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    outline: none;
    cursor: pointer;

    &[aria-disabled="true"] {
        cursor: default;
    }
}


.react-aria-CalendarCell.is-first-selected-in-range {
    border-radius: 32px 0px 0px 32px;
    border-top: 1px solid #00283D;
    border-bottom: 1px solid #00283D;
    border-left: 1px solid #00283D;
    background: var(--colorsUtilityMajor050);
}

.react-aria-CalendarCell.is-last-selected-in-range {
    border-radius: 0px 32px 32px 0px;
    border-top: 1px solid #00283D;
    border-right: 1px solid #00283D;
    border-bottom: 1px solid #00283D;
    background: var(--colorsUtilityMajor050);
}


.react-aria-CalendarCell.is-inter-selected {
    border-top: 1px solid #00283D;
    border-bottom: 1px solid #00283D;
    background: var(--colorsUtilityMajor050);
}


.react-aria-CalendarCell[data-selected] .e-calendar-date-cell,
.react-aria-CalendarCell[data-focused] .e-calendar-date-cell,
.react-aria-CalendarCell.is-selected .e-calendar-date-cell,
.react-aria-CalendarCell.is-first-selected-in-range .e-calendar-date-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #001926;
    color: var(--colorsYang100);
    border-radius: 32px;
    border: 3px solid var(--colorsActionMinor500);
}

.react-aria-CalendarCell .e-calendar-date-cell:hover {
    color: var(--colorsYin090);
    background-color: var(--colorsActionMinor200);
    border-radius: 32px;
}

.react-aria-CalendarCell.e-date-today .e-calendar-date-cell {
    border-radius: 32px;
    border: 3px solid var(--colorsActionMinor500);
    display: flex;
    justify-content: center;
    align-items: center;
}

.react-aria-CalendarCell[data-unavailable],
.react-aria-CalendarCell[data-disabled] {
    color: var(--colorsUtilityYin055);
    pointer-events: none;
    cursor: not-allowed;
}

.react-aria-CalendarCell[data-focused] .e-calendar-date-cell {
    border-radius: 50px;
    border: 3px solid var(--colorsSemanticFocus500);
}

.react-aria-CalendarCell[data-focused] .e-calendar-date-cell::before {
    content: '';
    position: absolute;
    top: -3px;
    width: 46px;
    height: 46px;
    border-radius: 50%;
    border: 3px solid #000;
    box-sizing: border-box;
}

.react-aria-CalendarHeaderCell {
    color: var(--colorsUtilityYin055);
    text-align: center;
    font-family: var(--fontFamiliesDefault);
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    text-transform: uppercase;
}

[data-testid="underlay"] {
    // Best hack I could come up with
    inset: unset !important;
}