import type { CalendarDate } from '@internationalized/date';
import type { Datetime } from '@sage/xtrem-date-time';
import { Time } from '@sage/xtrem-date-time';
import type { LocalizeLocale } from '@sage/xtrem-shared';
import { usePrevious } from '@sage/xtrem-ui-components';
import { useCallback, useEffect, useMemo, useReducer } from 'react';
import {
    areDateRangesEqual,
    datetimeToCalendarDate,
    datetimeToTime,
    isValidTimeZone,
    makeDatetime,
    toDatetime,
} from '../../ui/datetime/datetime-utils';
import type {
    DatetimeRangeProperties,
    DatetimeRangeValue,
    NestedDatetimeRangeProperties,
} from './datetime-range-types';

export interface DatetimeRangeComponentState {
    value: DatetimeRangeValue;
    endDate: CalendarDate | null;
    validationError: boolean;
    endTime: string | null;
    startDate: CalendarDate | null;
    startTime: string | null;
    timeZone: string;
    openInputPopover: 'start' | 'end' | null;
}

export type Action =
    | { type: 'SET_START_DATE'; date: DatetimeRangeComponentState['startDate'] }
    | { type: 'SET_END_DATE'; date: DatetimeRangeComponentState['endDate'] }
    | { type: 'SET_START_TIME'; time: DatetimeRangeComponentState['startTime'] }
    | { type: 'SET_END_TIME'; time: DatetimeRangeComponentState['endTime'] }
    | { type: 'SET_OPEN_POPOVER_ON_INPUT'; inputName: 'start' | 'end'; isOpen: boolean }
    | { type: 'RESET'; value?: DatetimeRangeValue | null; timeZone: string };

function datetimeRangeReducer(state: DatetimeRangeComponentState, action: Action): DatetimeRangeComponentState {
    switch (action.type) {
        case 'SET_START_DATE':
            return handleSetStartDate(state, action);
        case 'SET_END_DATE':
            return handleSetEndDate(state, action);
        case 'SET_START_TIME':
            return handleSetStartTime(state, action);
        case 'SET_END_TIME':
            return handleSetEndTime(state, action);
        case 'RESET':
            return handleReset(state, action);
        case 'SET_OPEN_POPOVER_ON_INPUT':
            return handleSetOpenPopover(state, action);
        default:
            return state;
    }
}

function handleSetStartDate(
    state: DatetimeRangeComponentState,
    action: Extract<Action, { type: 'SET_START_DATE' }>,
): DatetimeRangeComponentState {
    let newStartDatetime: Datetime | null = null;
    let newEndDatetime: Datetime | null = state.value.end;

    if (action.date && state.startTime) {
        newStartDatetime = makeDatetime(action.date, Time.parse(state.startTime), state.timeZone);
    }

    if (action.date && state.endDate && state.endDate.compare(action.date) < 0) {
        newEndDatetime = null;
    }

    const value = {
        ...state.value,
        start: newStartDatetime,
        end: newEndDatetime,
    };

    return {
        ...state,
        startDate: action.date,
        endDate: newEndDatetime ? datetimeToCalendarDate(newEndDatetime, state.timeZone) : null,
        value,
    };
}

function handleSetEndDate(
    state: DatetimeRangeComponentState,
    action: Extract<Action, { type: 'SET_END_DATE' }>,
): DatetimeRangeComponentState {
    let newEndDatetime: Datetime | null = null;
    let validationError = false;
    if (action.date && state.endTime) {
        newEndDatetime = makeDatetime(action.date, Time.parse(state.endTime), state.timeZone);
    }

    if (newEndDatetime && state.value.start && newEndDatetime.compare(state.value.start) < 0) {
        validationError = true;
    }

    const value = {
        ...state.value,
        end: newEndDatetime,
    };

    return {
        ...state,
        endDate: action.date,
        value,
        validationError,
    };
}

function handleSetStartTime(
    state: DatetimeRangeComponentState,
    action: Extract<Action, { type: 'SET_START_TIME' }>,
): DatetimeRangeComponentState {
    let endDate: CalendarDate | null = state.endDate;
    let endTime: string | null = state.endTime;
    let newStartDatetime: Datetime | null = null;
    let newEndDatetime: Datetime | null = state.value.end;

    if (action.time && state.startDate) {
        newStartDatetime = makeDatetime(state.startDate, Time.parse(action.time), state.timeZone);
    }

    const validationError =
        (newStartDatetime && state.value.end && state.value.end.compare(newStartDatetime) < 0) || false;

    if (validationError) {
        endDate = null;
        endTime = null;
        newEndDatetime = null;
    }

    const value = {
        ...state.value,
        start: newStartDatetime,
        end: newEndDatetime,
    };

    return {
        ...state,
        startTime: action.time,
        endDate,
        endTime,
        value,
        validationError,
    };
}

function handleSetEndTime(
    state: DatetimeRangeComponentState,
    action: Extract<Action, { type: 'SET_END_TIME' }>,
): DatetimeRangeComponentState {
    let newEndDatetime: Datetime | null = null;
    if (action.time && state.endDate) {
        newEndDatetime = makeDatetime(state.endDate, Time.parse(action.time), state.timeZone);
    }

    const validationError =
        (newEndDatetime && state.value.start && newEndDatetime.compare(state.value.start) < 0) || false;

    const value = {
        ...state.value,
        end: newEndDatetime,
    };

    return {
        ...state,
        endTime: action.time,
        value,
        validationError,
    };
}

function handleReset(
    state: DatetimeRangeComponentState,
    action: Extract<Action, { type: 'RESET' }>,
): DatetimeRangeComponentState {
    const { timeZone, value } = action;
    return {
        ...state,
        value: { start: value?.start || null, end: value?.end || null },
        endDate: value?.end ? datetimeToCalendarDate(value.end, timeZone) : null,
        validationError: false,
        endTime: value?.end ? datetimeToTime(value.end, timeZone) : null,
        startDate: value?.start ? datetimeToCalendarDate(value.start, timeZone) : null,
        startTime: value?.start ? datetimeToTime(value.start, timeZone) : null,
        timeZone,
        openInputPopover: null,
    };
}

function handleSetOpenPopover(
    state: DatetimeRangeComponentState,
    action: Extract<Action, { type: 'SET_OPEN_POPOVER_ON_INPUT' }>,
): DatetimeRangeComponentState {
    const { isOpen, inputName } = action;
    return {
        ...state,
        openInputPopover: isOpen ? inputName : null,
    };
}

export interface UseDatetimeRangeProps {
    value?: DatetimeRangeValue | null;
    fieldProperties: DatetimeRangeProperties | NestedDatetimeRangeProperties;
    locale: LocalizeLocale;
    setFieldValue: any;
    validate: any;
    elementId: string;
    screenId: string;
    onChange: (value: DatetimeRangeValue | null) => void;
    minDate?: CalendarDate;
    maxDate?: CalendarDate;
}

export interface UseDatetimeRangeResult {
    currentDates: DatetimeRangeValue;
    endDate: CalendarDate | null;
    validationError: boolean;
    endTime: string | null;
    handleEndDateChange: (date: CalendarDate) => void;
    handleEndTimeChange: (time: string) => void;
    handlePopoverOpenChange: (isOpen: boolean, inputName: 'start' | 'end') => void;
    handleStartDateChange: (date: CalendarDate) => void;
    handleStartTimeChange: (time: string) => void;
    openInputPopover: 'start' | 'end' | null;
    startDate: CalendarDate | null;
    startTime: string | null;
    timeZone: string;
    initialDate?: CalendarDate;
}

function getStateFromValue({
    value,
    fieldProperties,
}: {
    value: UseDatetimeRangeProps['value'];
    fieldProperties: UseDatetimeRangeProps['fieldProperties'];
}): DatetimeRangeComponentState {
    const timeZone =
        fieldProperties.timeZone && isValidTimeZone(fieldProperties.timeZone) ? fieldProperties.timeZone : 'GMT';
    return {
        value: { start: value?.start || null, end: value?.end || null },
        endDate: value?.end ? datetimeToCalendarDate(value.end, timeZone) : null,
        validationError: false,
        endTime: value?.end ? datetimeToTime(value.end, timeZone) : null,
        startDate: value?.start ? datetimeToCalendarDate(value.start, timeZone) : null,
        startTime: value?.start ? datetimeToTime(value.start, timeZone) : null,
        timeZone,
        openInputPopover: null,
    };
}

function getInitialState({
    value,
    fieldProperties,
}: {
    value: UseDatetimeRangeProps['value'];
    fieldProperties: UseDatetimeRangeProps['fieldProperties'];
}): DatetimeRangeComponentState {
    if (value) {
        return getStateFromValue({ value, fieldProperties });
    }
    const timeZone =
        fieldProperties.timeZone && isValidTimeZone(fieldProperties.timeZone) ? fieldProperties.timeZone : 'GMT';
    const initialDate = fieldProperties.defaultDate ? toDatetime(fieldProperties.defaultDate, timeZone) : null;
    return getStateFromValue({ value: { start: initialDate, end: initialDate }, fieldProperties });
}

export function useDatetimeRange({ fieldProperties, value, onChange }: UseDatetimeRangeProps): UseDatetimeRangeResult {
    const previousValue = usePrevious(value);
    const timeZone = useMemo(
        () =>
            fieldProperties.timeZone && isValidTimeZone(fieldProperties.timeZone) ? fieldProperties.timeZone : 'GMT',
        [fieldProperties.timeZone],
    );

    const initialDate = useMemo(() => {
        const defaultDate = fieldProperties.defaultDate ? toDatetime(fieldProperties.defaultDate, timeZone) : null;
        return defaultDate != null ? datetimeToCalendarDate(defaultDate, timeZone) : undefined;
    }, [fieldProperties.defaultDate, timeZone]);

    const [state, dispatch] = useReducer(datetimeRangeReducer, getInitialState({ value, fieldProperties }));

    const handleStartDateChange = useCallback((date: CalendarDate): void => {
        dispatch({ type: 'SET_START_DATE', date });
    }, []);

    const handleEndDateChange = useCallback((date: CalendarDate): void => {
        dispatch({ type: 'SET_END_DATE', date });
    }, []);

    const handleStartTimeChange = useCallback((time: string): void => {
        dispatch({ type: 'SET_START_TIME', time });
    }, []);

    const handleEndTimeChange = useCallback((time: string): void => {
        dispatch({ type: 'SET_END_TIME', time });
    }, []);

    const handlePopoverOpenChange = useCallback((isOpen: boolean, inputName: 'start' | 'end'): void => {
        dispatch({ type: 'SET_OPEN_POPOVER_ON_INPUT', isOpen, inputName });
    }, []);

    useEffect(() => {
        // external change
        if (!areDateRangesEqual(state.value, value) && !areDateRangesEqual(previousValue, value)) {
            dispatch({ type: 'RESET', value, timeZone });
        }
    }, [value, timeZone, state.value, previousValue]);

    useEffect(() => {
        // internal change
        if (!areDateRangesEqual(state.value, value) && areDateRangesEqual(previousValue, value)) {
            onChange(state.value);
        }
    }, [value, timeZone, state.value, previousValue, onChange]);

    return {
        currentDates: state.value,
        endDate: state.endDate,
        validationError: state.validationError,
        endTime: state.endTime,
        handleEndDateChange,
        handleEndTimeChange,
        handlePopoverOpenChange,
        handleStartDateChange,
        handleStartTimeChange,
        openInputPopover: state.openInputPopover,
        startDate: state.startDate,
        startTime: state.startTime,
        timeZone,
        initialDate,
    };
}
