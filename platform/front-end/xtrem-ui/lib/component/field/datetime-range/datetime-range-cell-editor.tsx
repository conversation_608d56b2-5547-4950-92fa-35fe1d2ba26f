import React from 'react';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import type { DatetimeRangeValue, NestedDatetimeRangeProperties } from './datetime-range-types';
import { noop } from 'lodash';
import { localize } from '../../../service/i18n-service';
import { ContextType } from '../../../types';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { generateFieldId, getLabelTitle, isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import { useDatetimeRange } from './use-datetime-range';
import { DatetimeInputComponent } from '../../ui/datetime/datetime-input-component';
import { areDateRangesEqual } from '../../ui/datetime/datetime-utils';
import { datePropertyValueToCalendarDate } from '../../../utils/date-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';

export function DatetimeRangeCellEditor({
    fieldProperties,
    tableElementId: parentElementId,
    screenId,
    node,
    api,
    column,
    eGridCell,
    elementId,
    locale,
    value,
    data,
    isEditing,
    onValueChange,
}: CellParams<NestedDatetimeRangeProperties, DatetimeRangeValue | undefined>): React.ReactNode {
    const allColumns = React.useMemo(() => api.getColumns() || [], [api]);
    const width = React.useMemo(() => eGridCell?.style?.width || '250px', [eGridCell?.style?.width]);
    const containerRef = React.useRef<HTMLDivElement>(null);
    const startDateRef = React.useRef<HTMLInputElement>(null);

    React.useEffect(() => {
        if (isEditing) {
            containerRef.current?.focus();
        }
    }, [isEditing]);

    const setFieldValue = React.useCallback<(bind: string, value: unknown) => Promise<void>>(
        async (_, value) => {
            onValueChange(value);
        },
        [onValueChange],
    );

    const onChange = React.useCallback(
        (newValue: DatetimeRangeValue | null) => {
            if (areDateRangesEqual(newValue, value)) {
                return;
            }
            handleChange(elementId, newValue, setFieldValue);
        },
        [elementId, setFieldValue, value],
    );

    const minDate = React.useMemo(
        () =>
            datePropertyValueToCalendarDate(
                resolveByValue({
                    propertyValue: fieldProperties.minDate,
                    screenId,
                    rowValue: null,
                    fieldValue: null,
                    skipHexFormat: true,
                }) || new Date(1970, 0, 1),
            ),
        [fieldProperties.minDate, screenId],
    );

    const maxDate = React.useMemo(
        () =>
            datePropertyValueToCalendarDate(
                resolveByValue({
                    propertyValue: fieldProperties.maxDate,
                    screenId,
                    rowValue: null,
                    fieldValue: null,
                    skipHexFormat: true,
                }) || new Date(2100, 11, 31),
            ),
        [fieldProperties.maxDate, screenId],
    );

    const {
        currentDates,
        endDate,
        validationError,
        endTime,
        handleEndDateChange,
        handleEndTimeChange,
        handlePopoverOpenChange,
        handleStartDateChange,
        handleStartTimeChange,
        openInputPopover,
        startDate,
        startTime,
        timeZone,
        initialDate,
    } = useDatetimeRange({
        elementId,
        fieldProperties,
        locale,
        onChange,
        screenId,
        setFieldValue,
        validate: noop,
        value,
        minDate,
        maxDate,
    });

    const isDisabled = React.useMemo(
        () => isFieldDisabled(screenId, fieldProperties, currentDates, null),
        [currentDates, fieldProperties, screenId],
    );

    const isReadOnly = React.useMemo(
        () => isFieldReadOnly(screenId, fieldProperties, currentDates, null),
        [currentDates, fieldProperties, screenId],
    );

    const fieldId = React.useMemo(
        () =>
            generateFieldId({ screenId, elementId, contextType: ContextType.table, fieldProperties, isNested: false }),
        [elementId, fieldProperties, screenId],
    );

    const resolvedTitle = React.useMemo(
        () => getLabelTitle(screenId, fieldProperties, null),
        [fieldProperties, screenId],
    );

    const ariaLabelTitle = React.useMemo(
        () => resolvedTitle || localize('@sage/xtrem-ui/datetime-range-aria-label', 'Date and time range'),
        [resolvedTitle],
    );

    const startLabel = React.useMemo(() => localize('@sage/xtrem-ui/date-time-range-start-date', 'Start'), []);
    const endLabel = React.useMemo(() => localize('@sage/xtrem-ui/date-time-range-end-date', 'End'), []);

    const getValidationErrors = React.useCallback(() => {
        return validationError
            ? localize(
                  '@sage/xtrem-ui/datetime-range-end-date-error',
                  'You need to enter an End date later than the Start date',
              )
            : '';
    }, [validationError]);

    return (
        <div
            className="ag-cell-edit-wrapper e-nested-cell-field-datetime-range"
            data-testid={`${parentElementId}-${node.rowIndex}-${allColumns.indexOf(column) + 1}`}
            style={{ width }}
        >
            <div className="e-datetime-range-container" ref={containerRef} tabIndex={0}>
                <div className="e-date-input-wrapper">
                    <DatetimeInputComponent
                        inputRef={startDateRef}
                        nestedRowId={data?._id ? `-${data._id}` : ''}
                        aria-label={`${ariaLabelTitle} - ${startLabel}`}
                        date={startDate}
                        elementId={elementId}
                        fieldId={`${fieldId}-start-date`}
                        initialDate={initialDate}
                        isDisabled={isDisabled}
                        isPopoverOpen={openInputPopover === 'start'}
                        isReadOnly={isReadOnly}
                        isTimeZoneHidden={fieldProperties.isTimeZoneHidden}
                        locale={locale}
                        maxDate={maxDate}
                        minDate={minDate}
                        onDateChange={handleStartDateChange}
                        onPopperOpenChange={handlePopoverOpenChange}
                        onTimeChange={handleStartTimeChange}
                        rangeStartDate={null}
                        screenId={screenId}
                        time={startTime}
                        timeZone={timeZone}
                        size="small"
                        title={undefined}
                        type="start"
                    />
                </div>
                <div className="e-date-input-wrapper">
                    <DatetimeInputComponent
                        nestedRowId={data?._id ? `-${data._id}` : ''}
                        aria-label={`${ariaLabelTitle} - ${endLabel}`}
                        date={endDate}
                        elementId={elementId}
                        fieldId={`${fieldId}-end-date`}
                        initialDate={initialDate}
                        isDisabled={isDisabled}
                        isPopoverOpen={openInputPopover === 'end'}
                        isReadOnly={isReadOnly}
                        isTimeZoneHidden={fieldProperties.isTimeZoneHidden}
                        locale={locale}
                        maxDate={maxDate}
                        minDate={minDate}
                        onDateChange={handleEndDateChange}
                        onPopperOpenChange={handlePopoverOpenChange}
                        onTimeChange={handleEndTimeChange}
                        rangeStartDate={startDate}
                        screenId={screenId}
                        time={endTime}
                        timeZone={timeZone}
                        title={undefined}
                        type="end"
                        size="small"
                        validationError={getValidationErrors()}
                    />
                </div>
            </div>
        </div>
    );
}
