/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import type { Datetime } from '@sage/xtrem-date-time';
import { isNil } from 'lodash';
import type { ScreenBase } from '../../../service/screen-base';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { DatetimeRangeProperties, DatetimeRangeValue } from './datetime-range-types';

type AllowableDate = Datetime | Date | string;
export class DatetimeRangeControlObject<
    ReferencedItemType extends ClientNode = any,
    CT extends ScreenBase = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.DatetimeRange, FieldComponentProps<FieldKey.DatetimeRange>> {
    @ControlObjectProperty<DatetimeRangeProperties<CT>, DatetimeRangeControlObject<ReferencedItemType, CT>>()
    isTimeZoneHidden?: boolean;

    @ControlObjectProperty<DatetimeRangeProperties<CT>, DatetimeRangeControlObject<ReferencedItemType, CT>>()
    timeZone?: string;

    @ControlObjectProperty<DatetimeRangeProperties<CT>, DatetimeRangeControlObject<ReferencedItemType, CT>>()
    minDate?: AllowableDate;

    @ControlObjectProperty<DatetimeRangeProperties<CT>, DatetimeRangeControlObject<ReferencedItemType, CT>>()
    maxDate?: AllowableDate;

    @ControlObjectProperty<DatetimeRangeProperties<CT>, DatetimeRangeControlObject<ReferencedItemType, CT>>()
    defaultDate?: AllowableDate;

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    get value(): DatetimeRangeValue | null {
        return this._getValue();
    }

    set value(value: DatetimeRangeValue | null) {
        if (isNil(value)) {
            this._setValue(null);
            return;
        }
        this._setValue(value);
    }
}
