/**
 * @packageDocumentation
 * @module root
 * */
import type { ClientNode } from '@sage/xtrem-client';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import { noop } from 'lodash';
import { lookupDialog } from '../../../service/dialog-service';
import { fetchReferenceFieldSuggestions } from '../../../service/graphql-service';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import type { ScreenBase } from '../../../service/screen-base';
import { showToast } from '../../../service/toast-service';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { CollectionItem, FieldComponentProps, FieldKey, PartialCollectionValue } from '../../types';
import type { ReferenceDecoratorProperties } from './reference-types';
import { getReferenceValueField } from './reference-utils';
import { setFieldValue } from '../../../redux/actions';
import { getStore } from '../../../redux';

/**
 * [Field]{@link EditableFieldControlObject} that holds a reference to an existing GraphQL object. The type of GraphQL object
 * must be specified through the 'node' property, while the 'valueField' and 'helperTextField'
 * properties define which properties of the GraphQL object will be displayed in the field
 * and will be matched against the user provided text
 */
export class ReferenceControlObject<
    ReferencedItemType extends ClientNode = any,
    CT extends ScreenBase = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.Reference, FieldComponentProps<FieldKey.Reference>> {
    static readonly defaultUiProperties: Partial<ReferenceDecoratorProperties<ScreenBase>> = {
        ...EditableFieldControlObject.defaultUiProperties,
        canFilter: true,
        isAutoSelectEnabled: false,
    };

    /** Graphql filter that will restrict the results of the reference field */
    get filter(): GraphQLFilter<ReferencedItemType> | ((this: CT) => GraphQLFilter<ReferencedItemType>) | undefined {
        return resolveByValue({
            fieldValue: undefined,
            propertyValue: this.getUiComponentProperty('filter'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /** Graphql filter that will restrict the results of the reference field */
    set filter(
        filter: GraphQLFilter<ReferencedItemType> | ((this: CT) => GraphQLFilter<ReferencedItemType>) | undefined,
    ) {
        this.setUiComponentProperties('filter', filter);
        if (!this.getUiComponentProperty('isTransient')) {
            this.refresh().catch(() => {
                /* Intentional fire and forget */
            });
        }
    }

    /** The GraphQL node property that will be displayed below the reference field */
    get helperTextField(): string | undefined {
        return this.getUiComponentProperty('helperTextField') as string | undefined;
    }

    /** The GraphQL node property that will be used to display an image in front of the values. t*/
    get imageField(): string | undefined {
        return this.getUiComponentProperty('imageField') as string | undefined;
    }

    /** The GraphQL node that the field suggestions will be fetched from */
    get node(): string {
        return String(this.getUiComponentProperty('node'));
    }

    @ControlObjectProperty<
        ReferenceDecoratorProperties<CT, ReferencedItemType>,
        ReferenceControlObject<ReferencedItemType, CT>
    >()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<
        ReferenceDecoratorProperties<CT, ReferencedItemType>,
        ReferenceControlObject<ReferencedItemType, CT>
    >()
    /** Icon of the input field. It will be placed on the right side. */
    icon?: IconType;

    @ControlObjectProperty<
        ReferenceDecoratorProperties<CT, ReferencedItemType>,
        ReferenceControlObject<ReferencedItemType, CT>
    >()
    /** Color of the icon, only supported in tile containers */
    iconColor?: string;

    @ControlObjectProperty<
        ReferenceDecoratorProperties<CT, ReferencedItemType>,
        ReferenceControlObject<ReferencedItemType, CT>
    >()
    /** Indicator, whether sounds play on successful/erroneous selection */
    isSoundDisabled?: boolean;

    @ControlObjectProperty<
        ReferenceDecoratorProperties<CT, ReferencedItemType>,
        ReferenceControlObject<ReferencedItemType, CT>
    >()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    @ControlObjectProperty<
        ReferenceDecoratorProperties<CT, ReferencedItemType>,
        ReferenceControlObject<ReferencedItemType, CT>
    >()
    /** Lookup Dialog title **/
    lookupDialogTitle?: string;

    /** Field's value */
    get value(): PartialCollectionValue<ReferencedItemType> | null {
        const value = this._getValue();
        return value ? splitValueToMergedValue(value) : null;
    }

    /** Field's value */
    set value(newValue: PartialCollectionValue<ReferencedItemType> | null) {
        /** *
         *  $ / _ hack: if the value has an `_id` value, we transform it to `_id`.
         * We will streamline these properties for R2
         *
         *  */
        if (newValue && (newValue as any).$id) {
            const remappedValue = { ...newValue, _id: (newValue as any).$id } as any;
            delete remappedValue.$id;
            this._setValue(remappedValue);
        } else {
            this._setValue(newValue);
        }
    }

    /** The GraphQL node property that will be used as the reference field value */
    get valueField(): string | undefined {
        return getReferenceValueField(this.uiComponentProperties);
    }

    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    openDialog(): void {
        this.setUiComponentProperties('isReferenceDialogOpen', true);
        lookupDialog(this.screenId, 'info', {
            contextNode: this.getUiComponentProperty('node'),
            createTunnelLinkText: this.getUiComponentProperty('createTunnelLinkText'),
            fieldId: this.elementId,
            fieldProperties: this._getUiComponentProperties(this.screenId, this.elementId),
            isLinkCreateNewText: !!this.getUiComponentProperty('tunnelPage'),
            isMultiSelect: false,
            level: undefined,
            onCreateNewItemLinkClick: undefined,
            parentElementId: undefined,
            recordContext: undefined,
            searchText: undefined,
            selectedRecordId: (this.value as ClientNode | undefined)?._id,
            value: this.value,
            valueField: this.getUiComponentProperty('valueField'),
        })
            .catch(noop)
            .then(([selection]) => {
                const { dispatch, getState } = getStore();
                handleChange<Partial<CollectionItem> | null>(
                    this.elementId,
                    selection ?? null,
                    async () => {
                        await setFieldValue(this.screenId, this.elementId, selection, true)(dispatch, getState);
                    },
                    this.validateWithDetails.bind(this),
                    () => {
                        triggerFieldEvent(this.screenId, this.elementId, 'onChange');
                    },
                );
            });
    }

    async fetchSuggestions(searchText?: string): Promise<PartialCollectionValue<ReferencedItemType>[]> {
        const result = await fetchReferenceFieldSuggestions({
            fieldProperties: this.properties,
            screenId: this.screenId,
            fieldId: this.elementId,
            filterValue: searchText,
        });

        if (result) {
            // The remove edges function return an object with integer keys so we need to convert it back to an array.
            return Object.values(result).map((e: any) => splitValueToMergedValue(e));
        }

        return [];
    }
}
