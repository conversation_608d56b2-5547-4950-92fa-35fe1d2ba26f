import type { UseComboboxStateChangeTypes } from 'downshift';
import { get } from 'lodash';
import React from 'react';
import uid from 'uid';
import * as xtremRedux from '../../../redux';
import { lookupDialog } from '../../../service/dialog-service';
import { fetchReferenceFieldSuggestions } from '../../../service/graphql-service';
import type { LookupDialogContent } from '../../../types/dialogs';
import {
    getInitialCellEditorState,
    setDefaultAgGridInputStyles,
} from '../../../utils/ag-grid/ag-grid-cell-editor-utils';
import { shouldRenderDropdownAbove } from '../../../utils/table-component-utils';
import { cleanMetadataAndNonPersistentIdFromRecord, splitValueToMergedValue } from '../../../utils/transformers';
import type { CollectionItem } from '../../types';
import type { SelectItem } from '../../ui/select/select-component';
import { Select } from '../../ui/select/select-component';
import type {
    ReferenceCellEditorProps,
    ReferenceCellEditorState,
    ReferenceDecoratorProperties,
} from './reference-types';
import { getReferenceSelectedRecord, hasLookupIcon, nodeToSelectItem, nodesToSelectItems } from './reference-utils';

export default class ReferenceCellEditor extends React.Component<ReferenceCellEditorProps, ReferenceCellEditorState> {
    private selectRef: React.RefObject<HTMLInputElement>;

    constructor(props: ReferenceCellEditorProps) {
        super(props);
        this.selectRef = React.createRef();
        this.state = this.createInitialState(props);
    }

    componentDidMount(): void {
        const input = this.selectRef.current;
        if (!input) {
            throw new Error('No input');
        }
        // try to match the styles of ag-grid
        setDefaultAgGridInputStyles(input);
        const inputDiv = input.parentNode as HTMLInputElement;
        inputDiv.style.width = '100%';
        inputDiv.style.height = '100%';
        inputDiv.style.border = 'none';
        inputDiv.style.display = 'flex';
        inputDiv.style.alignItems = 'center';

        input.focus();
        if (this.state.highlightOnFocus) {
            input.select();
            this.setState({
                highlightOnFocus: false,
            });
        } else {
            // when we started editing, we want the caret at the end, not the start.
            // this comes into play in two scenarios: a) when user hits F2 and b)
            // when user hits a printable character, then on IE (and only IE) the caret
            // was placed after the first character, thus 'apply' would end up as 'pplea'
            const length = input.value ? input.value.length : 0;
            if (length > 0) {
                input.setSelectionRange(length, length);
            }
        }
    }

    getItems = async (filterValue: string): Promise<SelectItem[]> => {
        const level = this.props.node?.data?.__level;
        const nodes = await fetchReferenceFieldSuggestions({
            fieldProperties: this.props.fieldProperties,
            screenId: this.props.screenId,
            fieldId: this.props.elementId.split('.').slice(0, 2).join('.'),
            filterValue,
            parentElementId: this.props.tableElementId,
            recordContext: splitValueToMergedValue(cleanMetadataAndNonPersistentIdFromRecord(this.props.data)),
            level,
            contextNode: String(this.props.contextNode),
        });
        return nodesToSelectItems({ nodes, fieldProperties: this.props.fieldProperties });
    };

    getWidth(): string {
        return this.props.eGridCell?.style?.width ?? '200px';
    }

    onSelected = (selectedItem: SelectItem | null | undefined, selectionType?: UseComboboxStateChangeTypes): void => {
        this.props.onValueChange(selectedItem?.__collectionItem ?? null);
        if (selectionType !== '__input_blur__') {
            this.props.stopEditing(true);
        }
    };

    isLookupDialogOpen = (): boolean => this.state.isLookupPanelOpen && !!this.props.fieldProperties.columns;

    onLookupDialogSelectionFinished = ([collectionItem]: CollectionItem[] = []): void => {
        if (collectionItem) {
            this.onSelected(nodeToSelectItem({ collectionItem, fieldProperties: this.props.fieldProperties }));
        } else {
            this.onSelected(undefined);
        }
    };

    getLookupContent = (): LookupDialogContent => {
        return {
            fieldProperties: this.props.fieldProperties as ReferenceDecoratorProperties,
            fieldId: this.props.elementId,
            onLookupDialogClose: async (): Promise<void> => {
                setTimeout(() => {
                    this.props.api.setFocusedCell(
                        this.props.node.rowIndex ?? 0,
                        this.props.column,
                        this.props.node.isRowPinned() ? 'top' : null,
                    );
                }, 50);
                this.props.stopEditing(true);
            },
            parentElementId: this.props.tableElementId,
            contextNode: String(this.props.contextNode),
            recordContext: splitValueToMergedValue(this.props.data),
            level: this.props.node?.data?.__level,
            createTunnelLinkText: this.props.fieldProperties.createTunnelLinkText,
            onCreateNewItemLinkClick: this.handleOnCreateNewItemLinkClick,
            isLinkCreateNewText: !!this.props.fieldProperties.tunnelPage,
        };
    };

    openLookupDialog = async (
        event: React.MouseEvent<HTMLAnchorElement | HTMLButtonElement, MouseEvent>,
    ): Promise<void> => {
        event.stopPropagation();
        event.preventDefault();

        try {
            const selection = await lookupDialog(this.props.screenId, 'info', this.getLookupContent());
            this.onLookupDialogSelectionFinished(selection);
        } catch {
            /* intentionally left empty */
        }
    };

    onOpenTunnel = async (isNew = false): Promise<void> => {
        const dispatch = xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch;
        const newValue = await dispatch(
            xtremRedux.actions.openTunnel({
                elementId: this.props.elementId,
                parentElementId: this.props.tableElementId,
                screenId: this.props.screenId,
                recordContext: this.props.data,
                contextNode: this.props.contextNode,
                fieldProperties: this.props.fieldProperties as ReferenceDecoratorProperties,
                value: isNew ? null : this.props.value,
            }),
        );

        // Only update the value if the tunnel results some changes. If null returned, no changes were done.
        if (newValue) {
            this.onLookupDialogSelectionFinished([newValue]);
        }
    };

    handleOnCreateNewItemLinkClick = (): void => {
        this.onOpenTunnel(true);
    };

    createInitialState(props: ReferenceCellEditorProps): ReferenceCellEditorState {
        const { highlightOnFocus, value: startValue } = getInitialCellEditorState({
            eventKey: props.eventKey,
            initialValue: props.initialValue,
        });

        return {
            id: uid(16),
            isLookupPanelOpen: false,
            highlightOnFocus,
            selectedRecord: getReferenceSelectedRecord({
                value: get(this.props.data, this.props.colDef.field),
                fieldProperties: this.props.fieldProperties,
            }),
            startValue,
        };
    }

    render(): React.ReactNode {
        const dataTestId = `${this.props.tableElementId}-${this.props.node.rowIndex}-${
            this.props.api.getColumns()!.indexOf(this.props.column!) + 1
        }`;
        const hasLookup = hasLookupIcon({
            fieldProperties: this.props.fieldProperties,
            screenId: this.props.screenId,
            value: splitValueToMergedValue(this.props.data[this.props.colDef.field]),
            rowValue: splitValueToMergedValue(this.props.data),
        });

        const width = this.getWidth();
        let inputWidth = `${width} - 32px`;
        if (this.props.fieldProperties.imageField) {
            inputWidth += ' - 34px';
        }

        return (
            <div data-testid={dataTestId} style={{ width }} className="e-reference-cell-editor">
                <Select
                    allowClearOnTab={this.props.fieldProperties.isAutoSelectEnabled}
                    allowSelectOnTab={this.props.fieldProperties.isAutoSelectEnabled}
                    autoSelect={this.props.fieldProperties.isAutoSelectEnabled}
                    defaultIsOpen={!hasLookup}
                    elementId={this.props.elementId}
                    escapeBehavior="revert"
                    getItems={this.getItems}
                    hasLookupIcon={hasLookup}
                    initialInputValue={this.state.startValue}
                    inputWidth={`calc(${inputWidth})`}
                    isDropdownDisabled={this.props.fieldProperties.isDropdownDisabled}
                    isSortedAlphabetically={true}
                    isInTable={true}
                    lookupIconId={`e-reference-cell-editor-lookup-icon-${this.state.id}`}
                    minLookupCharacters={this.props.fieldProperties.minLookupCharacters ?? 3}
                    onLookupIconClick={this.openLookupDialog}
                    onSelected={this.onSelected}
                    placeholder={this.props.fieldProperties.placeholder}
                    preventSelectionOnBlur={this.isLookupDialogOpen()}
                    ref={this.selectRef}
                    screenId={this.props.screenId}
                    selectedItem={this.state.selectedRecord}
                    shouldFilterItems={false}
                    shouldRenderOptionsAbove={shouldRenderDropdownAbove({
                        isPhantomRow: !!this.props.data?.__phantom,
                        pageSize: this.props.api.paginationGetPageSize(),
                        rowIndex: this.props.node.rowIndex ?? 0,
                    })}
                    testId={`${dataTestId}-input`}
                    variant="plain"
                    size="small"
                    createTunnelLinkText={this.props.fieldProperties.createTunnelLinkText}
                    onCreateNewItemLinkClick={this.handleOnCreateNewItemLinkClick}
                    isLinkCreateNewText={!!this.props.fieldProperties.tunnelPage}
                    node={this.props.fieldProperties.node}
                />
            </div>
        );
    }
}
