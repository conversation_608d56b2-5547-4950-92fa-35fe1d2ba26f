import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import type { GraphQLFilter } from '../../../../service/graphql-utils';
import { ReferenceControlObject } from '../../../control-objects';
import type { ReferenceDecoratorProperties } from '../../../decorators';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import * as graphqlService from '../../../../service/graphql-service';

describe('Reference Field', () => {
    const screenId = 'TestPage';
    const fieldID = 'test';
    const referenceValue = 'TEST VALUE';
    const referenceHelperText = 'TEST HELPER TEXT';
    const valueField = 'theFieldWithTheValue';
    const helperTextField = 'theFieldWithTheHelperText';
    const values = { [valueField]: referenceValue, [helperTextField]: referenceHelperText, _id: '123' };
    const properties = {
        title: 'TEST_FIELD_TITLE',
        isHidden: true,
        isDisabled: true,
        valueField,
        helperTextField,
        node: '@sage/xtrem-ui-test/test-property',
    };
    let referenceFieldControlObject: ReferenceControlObject;

    describe('properties', () => {
        beforeEach(() => {
            referenceFieldControlObject = createFieldControlObject<FieldKey.Reference>(
                FieldKey.Reference,
                screenId,
                ReferenceControlObject,
                fieldID,
                values,
                properties,
            );
        });

        it('should retrieve the field helper text property name', () => {
            expect(referenceFieldControlObject.helperTextField).toEqual(helperTextField);
        });

        it('should retrieve the field value property name', () => {
            expect(referenceFieldControlObject.valueField).toEqual(valueField);
        });

        it('should retrieve the field value', () => {
            expect(referenceFieldControlObject.value).toEqual({
                theFieldWithTheHelperText: 'TEST HELPER TEXT',
                theFieldWithTheValue: 'TEST VALUE',
                _id: '123',
            });
        });

        it('should remove __ keys from reference field values', () => {
            const referenceFieldWithNestedUnderscoreKeys = (referenceFieldControlObject =
                createFieldControlObject<FieldKey.Reference>(
                    FieldKey.Reference,
                    screenId,
                    ReferenceControlObject,
                    'testWithNested',
                    {
                        item__name: {
                            name: 'TEST',
                            _id: '17',
                        },
                        _id: '16',
                    },
                    properties,
                ));
            expect(referenceFieldWithNestedUnderscoreKeys.value).toEqual({
                item: {
                    name: 'TEST',
                    _id: '17',
                },
                _id: '16',
            });
        });

        it('should return null for empty value', () => {
            const emptyReferenceField = (referenceFieldControlObject = createFieldControlObject<FieldKey.Reference>(
                FieldKey.Reference,
                screenId,
                ReferenceControlObject,
                'testEmpty',
                null,
                properties,
            ));
            expect(emptyReferenceField.value).toEqual(null);
        });
    });

    describe('properties update', () => {
        it('should set the value', () => {
            const updatedValue = {
                theFieldWithTheHelperText: 'TEST HELPER TEXT',
                theFieldWithTheValue: 'TEST VALUE',
                _id: '123',
            };
            referenceFieldControlObject.value = updatedValue;
            expect(referenceFieldControlObject.value).toEqual(updatedValue);
        });

        it('should set the title', () => {
            const updatedTitle = 'Test Numeric Field Title';
            expect(referenceFieldControlObject.title).not.toEqual(updatedTitle);
            referenceFieldControlObject.title = updatedTitle;
            expect(referenceFieldControlObject.title).toEqual(updatedTitle);
        });
    });

    describe('GraphQl filter property', () => {
        let setUiComponentProperties: jest.Mock<void, [string, string, ReferenceDecoratorProperties]>;
        let refresh: jest.Mock<Promise<FieldInternalValue<FieldKey.Reference>>>;
        let newProperties: ReferenceDecoratorProperties;
        const executeTest = async (filter: GraphQLFilter | (() => GraphQLFilter)) => {
            referenceFieldControlObject.filter = filter;
            expect(setUiComponentProperties).toHaveBeenCalled();
            expect(refresh).toHaveBeenCalled();
            expect(newProperties.filter).toEqual(filter);
        };

        beforeEach(() => {
            setUiComponentProperties = jest.fn(
                (_screenId: string, _elementId: string, _value: ReferenceDecoratorProperties) => {
                    newProperties = { ..._value };
                },
            );

            refresh = jest.fn();
            referenceFieldControlObject = createFieldControlObject<FieldKey.Reference>(
                FieldKey.Reference,
                screenId,
                ReferenceControlObject,
                fieldID,
                values,
                properties,
                { setUiComponentProperties, refresh },
            );
        });

        it('should update filter with GraphQL filter object', () => {
            executeTest({ description: { _regex: 'policy', _options: 'i' } });
        });

        it('should update filter with function', () => {
            executeTest(() => ({ description: { _regex: 'policy', _options: 'i' } }));
        });
    });

    describe('fetchSuggestions', () => {
        beforeEach(() => {
            jest.spyOn(graphqlService, 'fetchReferenceFieldSuggestions').mockResolvedValue([
                {
                    _id: '1',
                    property: 'asd',
                } as any,
                {
                    _id: '2',
                    property: 'asd2',
                    client__name: { _id: 1, name: 'John Doe' },
                    client__orderNumber: { _id: 1, orderNumber: '12123' },
                } as any,
            ]);

            referenceFieldControlObject = createFieldControlObject<FieldKey.Reference>(
                FieldKey.Reference,
                screenId,
                ReferenceControlObject,
                fieldID,
                values,
                properties,
            );
        });

        it('should fetch suggestions from the server when this function is called and return them to the application code', async () => {
            const suggestions = await referenceFieldControlObject.fetchSuggestions('my query');
            expect(suggestions).toBeInstanceOf(Array);
            expect(suggestions[0]._id).toEqual('1');
            expect(suggestions[1]._id).toEqual('2');
        });

        it('should remap split reference values to objects', async () => {
            const suggestions = await referenceFieldControlObject.fetchSuggestions('my query');
            expect(suggestions[1].client).toEqual({
                _id: 1,
                name: 'John Doe',
                orderNumber: '12123',
            });
        });
    });
});
