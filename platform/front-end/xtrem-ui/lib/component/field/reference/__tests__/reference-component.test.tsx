import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    renderWithRedux,
    applyActionMocks,
    textContentMatcher,
    userEvent,
} from '../../../../__tests__/test-helpers';

import { fireEvent, render, waitFor } from '@testing-library/react';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import * as graphqlService from '../../../../service/graphql-service';
import type { ScreenBase } from '../../../../service/screen-base';
import * as events from '../../../../utils/events';
import type { GridNestedFieldTypes, NestedField } from '../../../nested-fields';
import type { FieldInternalValue } from '../../../types';
import { <PERSON><PERSON>ey, GraphQLTypes } from '../../../types';
import { NumericControlObject } from '../../numeric/numeric-control-object';
import { TextControlObject } from '../../text/text-control-object';
import { ConnectedReferenceComponent, ReferenceComponent } from '../reference-component';
import type { ReferenceDecoratorProperties } from '../reference-types';
import * as i18n from '../../../../service/i18n-service';
import type { Class } from 'utility-types';
import type { ClientNode } from '@sage/xtrem-client';
import * as AbstractFieldUtilsModule from '../../../../utils/abstract-fields-utils';
import type { DeepPartial } from 'ts-essentials';
import type { PageDefinition } from '../../../../service/page-definition';
import { GraphQLKind } from '../../../../types';

import '@testing-library/jest-dom';

const imageValue =
    'iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==';

interface NodeType extends ClientNode {
    code: string;
    column1: string;
    column2: string;
    name: string;
    image?: string;
    product?: {
        _id: string;
        name: string;
    };
}

describe('Reference component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: ReferenceDecoratorProperties<ScreenBase, NodeType>;
    const fieldId = 'test-reference-field';
    let handleChangeSpy: jest.SpyInstance | null = null;

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test Field Title',
            valueField: 'code',
            node: '@sage/xtrem-ui-test/TestNode',
        };

        jest.spyOn(graphqlService, 'fetchReferenceFieldSuggestions').mockResolvedValue([
            { _id: '1', code: 'Test code 1', name: 'Test name 1' } as NodeType,
            { _id: '2', code: 'Test code 2', name: 'Test name 2' } as NodeType,
            { _id: '3', code: 'Test code 3', name: 'Test name 3' } as NodeType,
            { _id: '4', code: 'Test code 4', name: 'Test name 4' } as NodeType,
        ]);
        handleChangeSpy = jest.spyOn(AbstractFieldUtilsModule, 'handleChange').mockImplementation(() => {});
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    const getColumns = (): NestedField<any, GridNestedFieldTypes, NodeType>[] => [
        {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: 'column1',
            },
            properties: {
                bind: 'column1',
                title: 'Column 1',
            },
            type: FieldKey.Text,
        },
        {
            defaultUiProperties: {
                ...NumericControlObject.defaultUiProperties,
                bind: 'column2',
            },
            properties: {
                bind: 'column2',
                title: 'Column 2',
            },
            type: FieldKey.Numeric,
        },
    ];

    describe('connected', () => {
        let state: xtremRedux.XtremAppState;
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        const getReferenceField = (referenceFieldId = fieldId, isParentDisabled?: boolean) => (
            <Provider store={mockStore}>
                <ConnectedReferenceComponent
                    screenId={screenId}
                    elementId={referenceFieldId}
                    isParentDisabled={isParentDisabled}
                />
            </Provider>
        );

        const getUnconnectedReferenceField = (referenceFieldId = fieldId, nestedReadOnlyField = false) => {
            const properties = mockStore.getState().screenDefinitions[screenId].metadata.uiComponentProperties[
                referenceFieldId
            ] as any;
            const value = mockStore.getState().screenDefinitions[screenId].values[referenceFieldId] as any;
            return (
                <ReferenceComponent
                    screenId={screenId}
                    elementId={referenceFieldId}
                    nestedReadOnlyField={nestedReadOnlyField}
                    setFieldValue={jest.fn()}
                    validate={jest.fn()}
                    removeNonNestedErrors={jest.fn()}
                    fieldProperties={properties}
                    onFocus={jest.fn()}
                    value={value}
                    locale="en-US"
                    openTunnel={jest.fn().mockResolvedValue(null)}
                    hasAccessToTunnelPage={false}
                />
            );
        };

        const getReferenceFieldForMobile = (referenceFieldId = fieldId) => {
            state = {
                ...state,
                browser: {
                    ...state.browser,
                    greaterThan: { ...state.browser.greaterThan, s: false },
                },
            };
            mockStore = getMockStore(state);

            return getReferenceField(referenceFieldId);
        };

        beforeEach(() => {
            state = getMockState();
            (state.screenDefinitions[screenId] as PageDefinition) = {
                ...getMockPageDefinition(screenId),
                accessBindings: {
                    TestNode: {
                        $create: 'authorized',
                        $read: 'authorized',
                    },
                },
            };

            addFieldToState<FieldKey.Reference, any, NodeType>(
                FieldKey.Reference,
                state,
                screenId,
                fieldId,
                mockFieldProperties,
                {
                    code: 'XX123',
                    name: 'Capsule corp.',
                    image: { value: imageValue },
                },
            );
            addFieldToState<FieldKey.Reference, any, NodeType>(
                FieldKey.Reference,
                state,
                screenId,
                'test-empty-reference-field',
                mockFieldProperties,
                null,
            );
            addFieldToState<FieldKey.Reference, any, NodeType>(
                FieldKey.Reference,
                state,
                screenId,
                'test-empty-value-field',
                mockFieldProperties,
                { code: '', someOtherField: '1232asdf' },
            );
            mockStore = getMockStore(state);
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        describe('integration', () => {
            let fetchReferenceFieldSuggestionsMock;
            const defaultFetchReferenceFieldSuggestionsMock = ({
                filterValue: searchText,
            }: {
                filterValue?: string;
            }): Promise<any> => {
                const edges = [
                    { _id: 1, code: 'Wine', name: 'Wine' },
                    { _id: 2, code: 'White wine', name: 'White wine' },
                    { _id: 3, code: 'Cheese', name: 'Cheese' },
                ].filter(item => !searchText || item.name.toLowerCase().indexOf(searchText.toLowerCase()) !== -1);
                return Promise.resolve(edges);
            };
            const setup = (
                referenceProps: ReferenceDecoratorProperties<ScreenBase, NodeType> = mockFieldProperties,
                value: FieldInternalValue<FieldKey.Reference> = {},
                fetchReferenceFieldSuggestions = defaultFetchReferenceFieldSuggestionsMock,
            ) => {
                fetchReferenceFieldSuggestionsMock = jest
                    .spyOn(graphqlService, 'fetchReferenceFieldSuggestions')
                    .mockImplementation(fetchReferenceFieldSuggestions);
                const initialState: DeepPartial<xtremRedux.XtremAppState> = {
                    nodeTypes: {
                        TestNode: {
                            name: 'TestNode',
                            title: 'TestNode',
                            properties: {
                                code: {
                                    type: GraphQLTypes.String,
                                    kind: GraphQLKind.Scalar,
                                    canFilter: true,
                                    isOnInputType: true,
                                },
                            },
                        },
                    },
                    screenDefinitions: {
                        [screenId]: {
                            metadata: {
                                uiComponentProperties: {
                                    [fieldId]: referenceProps,
                                },
                            },
                            ...(value && {
                                values: {
                                    [fieldId]: value,
                                },
                            }),
                        },
                    },
                };
                const utils = renderWithRedux<FieldKey.Reference, any, NodeType>(
                    <ConnectedReferenceComponent screenId={screenId} elementId={fieldId} />,
                    {
                        mockStore,
                        initialState,
                        fieldType: FieldKey.Reference,
                        fieldValue: value,
                        fieldProperties: referenceProps,
                        elementId: fieldId,
                        screenId,
                    },
                );
                // const input = utils.getByTestId('e-reference-field-lookup-input-uniqguidmock');
                const reference = utils.getByTestId(
                    'e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field',
                ) as HTMLDivElement;

                const input = reference.querySelector(
                    'input[data-testid="e-reference-field-lookup-input-uniqguidmock"]',
                );
                const getIcon = (iconType: string) =>
                    reference.querySelector(`span[data-component="icon"][data-element="${iconType}"]`);

                const getDropdown = () => utils.getByTestId('e-ui-select-dropdown');
                const changeInput = (value: string) => {
                    fireEvent.change(input!, { target: { value } });
                };
                const blurInput = (target: object) => {
                    fireEvent.blur(input!, { target });
                };
                const getDropdownItems = () => {
                    return utils.getAllByTestId('e-ui-select-suggestion-value', {});
                };
                const getDropdownItemsLi = () => {
                    return utils.baseElement.querySelectorAll('[data-testid="e-ui-select-dropdown"] li');
                };
                const getLookupLinkButton = () => {
                    return utils.baseElement.querySelector('.e-ui-select-dropdown-lookup-link button');
                };
                const getCreateNewLinkButton = () => {
                    return utils.baseElement.querySelector('.e-ui-select-dropdown-create-new-item-link button');
                };
                const getLookupButton = () => {
                    return utils.getByTestId('e-ui-select-lookup-button');
                };
                const getLookupDialog = () => {
                    return utils.baseElement.querySelector('#mockLookupDialog');
                };
                const isLookupDialogOpen = (): boolean => {
                    const lookupDialog = getLookupDialog();
                    if (lookupDialog) {
                        return lookupDialog.attributes.getNamedItem('data-open')?.textContent === 'true';
                    }
                    return false;
                };

                return {
                    ...utils,
                    changeInput,
                    blurInput,
                    getIcon,
                    input,
                    isLookupDialogOpen,
                    getDropdown,
                    getDropdownItems,
                    getDropdownItemsLi,
                    getLookupDialog,
                    getLookupLinkButton,
                    getCreateNewLinkButton,
                    getLookupButton,
                    reference,
                };
            };

            afterEach(() => {
                jest.restoreAllMocks();
            });

            it('can render with redux with defaults', () => {
                const { input, reference } = setup();
                expect(reference).toHaveTextContent('Test Field Title');
                expect(input).toBeInTheDocument();
            });

            it('can search text', async () => {
                const { changeInput, getDropdownItems, input } = setup();
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                const dropdownItems = getDropdownItems();
                expect(dropdownItems).toHaveLength(2);
                expect(dropdownItems[0].textContent).toBe('White wine');
                expect(dropdownItems[1].textContent).toBe('Wine');
            });

            it('can select item', async () => {
                const { changeInput, input, getDropdownItems, getByText } = setup();
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                const dropdownItems = getDropdownItems();
                const selectedItem = dropdownItems[1].textContent as string;
                fireEvent.click(getByText(textContentMatcher(selectedItem)));
                await waitFor(() => expect(input!.getAttribute('value')).toBe(selectedItem));
            });

            it('can select and deselect item', async () => {
                const { changeInput, input, getDropdownItems, getByText } = setup();
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                const dropdownItems = getDropdownItems();
                const selectedItem = dropdownItems[1].textContent as string;
                fireEvent.click(getByText(textContentMatcher(selectedItem)));
                await waitFor(() => expect(input!.getAttribute('value')).toBe(selectedItem));
                changeInput('');
                fireEvent.blur(input as any);
                await waitFor(() => expect(input!.getAttribute('value')).toBe(''));
            });

            it('can autoselect an item', async () => {
                const { changeInput, input } = setup(
                    { ...mockFieldProperties, isAutoSelectEnabled: true },
                    { code: 'Cheese' },
                );
                changeInput('white');
                await waitFor(() => {
                    expect(fetchReferenceFieldSuggestionsMock).toHaveBeenCalledTimes(1);
                });
                fireEvent.blur(input as any);
                await waitFor(() => expect(input!.getAttribute('value')).toBe('White wine'));
            });

            it('can render "Type to search..." upon first focus', async () => {
                const { input, getByText } = setup(undefined, { code: '' });
                fireEvent.click(input as any);
                expect(graphqlService.fetchReferenceFieldSuggestions).not.toHaveBeenCalled();
                expect(getByText('Type to search...')).toBeInTheDocument();
            });

            it('can select with nested valueField', async () => {
                const items: any[] = [{ name: 'product1' }, { name: 'product2' }, { name: 'product3' }];
                const { getDropdownItems, input, getByText, changeInput } = setup(
                    {
                        ...mockFieldProperties,
                        valueField: { product: { name: true } } as any,
                    },
                    {},
                    ({ filterValue: searchText }: { filterValue?: string }) => {
                        const edges = items
                            .filter(
                                item => !searchText || item.name.toLowerCase().indexOf(searchText.toLowerCase()) !== -1,
                            )
                            .map(({ name }) => ({ product: { name } }));
                        return Promise.resolve(edges);
                    },
                );
                changeInput('product');
                expect(input!.getAttribute('value')).toBe('product');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                let dropdownItems = getDropdownItems();
                expect(dropdownItems.length).toBe(3);
                let selectedItem = dropdownItems[0].textContent as string;
                fireEvent.click(getByText(selectedItem));
                await waitFor(() => expect(input!.getAttribute('value')).toBe(selectedItem));
                changeInput('product2');
                expect(input!.getAttribute('value')).toBe('product2');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(2));
                dropdownItems = getDropdownItems();
                expect(dropdownItems.length).toBe(1);
                selectedItem = dropdownItems[0].textContent as string;
                fireEvent.click(getByText(selectedItem));
                await waitFor(() => expect(input!.getAttribute('value')).toBe('product2'));
            });

            it('can sort by order-by property', async () => {
                const { changeInput, input } = setup({
                    ...mockFieldProperties,
                    minLookupCharacters: 0,
                    orderBy: { name: 1 },
                });
                changeInput('wi');
                expect(input!.getAttribute('value')).toBe('wi');
                const expected = {
                    fieldId: 'test-reference-field',
                    fieldProperties: {
                        _controlObjectType: 'Reference',
                        minLookupCharacters: 0,
                        node: '@sage/xtrem-ui-test/TestNode',
                        orderBy: {
                            name: 1,
                        },
                        title: 'Test Field Title',
                        valueField: 'code',
                    },
                    filterValue: 'wi',
                    screenId: 'TestPage',
                };
                await waitFor(() =>
                    expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledWith(expected),
                );
            });

            it('can sort by first column', async () => {
                const { changeInput, input } = setup({
                    ...mockFieldProperties,
                    minLookupCharacters: 0,
                    columns: getColumns(),
                });
                // Do not mock 'fetchReferenceFieldSuggestions'
                fetchReferenceFieldSuggestionsMock.mockRestore();
                // Mock 'fetchReferenceFieldData' instead
                jest.spyOn(graphqlService, 'fetchReferenceFieldData').mockResolvedValue({ query: { edges: [] } });
                changeInput('wi');
                expect(input!.getAttribute('value')).toBe('wi');
                const expected = {
                    fieldProperties: {
                        _controlObjectType: 'Reference',
                        columns: [
                            {
                                defaultUiProperties: {
                                    bind: 'column1',
                                    isFullWidth: false,
                                    isHelperTextHidden: false,
                                    isHidden: false,
                                    isHiddenDesktop: false,
                                    isHiddenMobile: false,
                                    isMandatory: false,
                                    isReadOnly: false,
                                    isTitleHidden: false,
                                    isTransient: false,
                                },
                                properties: { bind: 'column1', title: 'Column 1' },
                                type: FieldKey.Text,
                            },
                            {
                                defaultUiProperties: {
                                    bind: 'column2',
                                    isFullWidth: false,
                                    isHelperTextHidden: false,
                                    isHidden: false,
                                    isHiddenDesktop: false,
                                    isHiddenMobile: false,
                                    isMandatory: false,
                                    isReadOnly: false,
                                    isTitleHidden: false,
                                    isTransient: false,
                                },
                                properties: { bind: 'column2', title: 'Column 2' },
                                type: FieldKey.Numeric,
                            },
                        ],
                        minLookupCharacters: 0,
                        node: '@sage/xtrem-ui-test/TestNode',
                        title: 'Test Field Title',
                        valueField: 'code',
                    },
                    isFilterLimitedToDataset: false,
                    screenId: 'TestPage',
                    valueField: 'code',
                    elementId: 'test-reference-field',
                    filter: { _or: [{ code: { _regex: 'wi', _options: 'i' } }] },
                    orderBy: { column1: 1, _id: 1 },
                };

                await waitFor(() =>
                    expect(graphqlService.fetchReferenceFieldData).toHaveBeenCalledWith(
                        expect.objectContaining(expected),
                    ),
                );
            });

            it('can render "No results are available." when minLookupCharacters is zero upon first focus', async () => {
                const { getDropdownItems, input, getByText } = setup(
                    {
                        ...mockFieldProperties,
                        minLookupCharacters: 0,
                    },
                    { code: '' },
                );
                fireEvent.click(input as any);
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                const dropdownItems = getDropdownItems();
                expect(getByText('No results are available.')).toBeInTheDocument();
                expect(dropdownItems).toHaveLength(3);
            });

            it('can render with icon', () => {
                const { getIcon } = setup({ ...mockFieldProperties, icon: 'scan' });
                expect(getIcon('scan')).toBeInTheDocument();
            });

            it('should clear the input field the user does not select a valid value', async () => {
                state.screenDefinitions[screenId].values[fieldId] = {};
                const { changeInput, input, blurInput } = setup();
                changeInput('Random');
                expect(input!.getAttribute('value')).toBe('Random');
                blurInput({ id: 'stuff' });
                await waitFor(() => {
                    expect(input!.getAttribute('value')).toBe('');
                });
            });

            it('should reset to empty value on Escape keydown', () => {
                const { input, changeInput } = setup(
                    {
                        ...mockFieldProperties,
                        minLookupCharacters: 0,
                    },
                    {
                        _id: 'XX123',
                        code: '1234567',
                        name: 'Capsule corp.',
                        column1: 'column1',
                        column2: 'column2',
                    },
                );
                expect(input!.getAttribute('value')).toBe('1234567');
                changeInput('12345678');
                expect(input!.getAttribute('value')).toBe('12345678');
                fireEvent.keyDown(input!, { key: 'Escape' });
                expect(input!.getAttribute('value')).toBe('12345678');
                fireEvent.keyDown(input!, { key: 'Escape' });
                expect(input!.getAttribute('value')).toBe('');
            });

            it('should be able to cycle the suggestions list using arrow down key when no lookup dialog is available', async () => {
                const { changeInput, getDropdownItemsLi, input } = setup();
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                await waitFor(() => expect(getDropdownItemsLi()).toHaveLength(2));
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'true');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'false');
                });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'false');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'true');
                });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'true');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'false');
                });
            });

            it('should be able to cycle the suggestions list using arrow up key when no lookup dialog is available', async () => {
                const { changeInput, getDropdownItemsLi, input } = setup();
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                await waitFor(() => expect(getDropdownItemsLi()).toHaveLength(2));
                fireEvent.keyDown(input!, { key: 'ArrowUp' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'false');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'true');
                });
                fireEvent.keyDown(input!, { key: 'ArrowUp' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'true');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'false');
                });
                fireEvent.keyDown(input!, { key: 'ArrowUp' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'false');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'true');
                });
            });

            it('should be able to cycle the suggestions list with the arrow down key including the lookup link', async () => {
                const { changeInput, getDropdownItemsLi, input, getLookupLinkButton } = setup({
                    ...mockFieldProperties,
                    columns: getColumns(),
                });
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                await waitFor(() => expect(getDropdownItemsLi()).toHaveLength(2));
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    const lookupLinkButton = getLookupLinkButton();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'true');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'false');
                    expect(document.activeElement).not.toBe(lookupLinkButton);
                });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    const lookupLinkButton = getLookupLinkButton();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'false');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'true');
                    expect(document.activeElement).not.toBe(lookupLinkButton);
                });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    const lookupLinkButton = getLookupLinkButton();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'false');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'false');
                    expect(document.activeElement).toBe(lookupLinkButton);
                });
                fireEvent.keyDown(getLookupLinkButton()!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    const lookupLinkButton = getLookupLinkButton();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'true');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'false');
                    expect(document.activeElement).not.toBe(lookupLinkButton);
                });
            });
            it('should be able to cycle the suggestions list with the arrow down key including the lookup and create new link', async () => {
                const { changeInput, getDropdownItemsLi, input, getLookupLinkButton, getCreateNewLinkButton } = setup({
                    ...{ tunnelPage: '@sage/xtrem-test/TunnelPage', ...mockFieldProperties },
                    columns: getColumns(),
                });
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                await waitFor(() => expect(getDropdownItemsLi()).toHaveLength(2));
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    const lookupLinkButton = getLookupLinkButton();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'true');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'false');
                    expect(document.activeElement).not.toBe(lookupLinkButton);
                });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    const lookupLinkButton = getLookupLinkButton();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'false');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'true');
                    expect(document.activeElement).not.toBe(lookupLinkButton);
                });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    const lookupLinkButton = getLookupLinkButton();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'false');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'false');
                    expect(document.activeElement).toBe(lookupLinkButton);
                });
                fireEvent.keyDown(getLookupLinkButton()!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    const createNewLinkButton = getCreateNewLinkButton();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'false');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'false');
                    expect(document.activeElement).toBe(createNewLinkButton);
                });
                fireEvent.keyDown(getCreateNewLinkButton()!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    const createNewLinkButton = getCreateNewLinkButton();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'true');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'false');
                    expect(document.activeElement).not.toBe(createNewLinkButton);
                });
            });

            it('should be able to return to the list of suggestions using the arrow up key from the lookup link', async () => {
                const { changeInput, getDropdownItemsLi, input, getLookupLinkButton } = setup({
                    ...mockFieldProperties,
                    columns: getColumns(),
                });
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                await waitFor(() => expect(getDropdownItemsLi()).toHaveLength(2));
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const lookupLinkButton = getLookupLinkButton();
                    expect(document.activeElement).toBe(lookupLinkButton);
                });
                fireEvent.keyDown(getLookupLinkButton()!, { key: 'ArrowUp' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    const lookupLinkButton = getLookupLinkButton();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'false');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'true');
                    expect(document.activeElement).not.toBe(lookupLinkButton);
                });
            });
            it('should be able to return to the list of suggestions using the arrow up key from the create new link', async () => {
                const { changeInput, getDropdownItemsLi, input, getLookupLinkButton, getCreateNewLinkButton } = setup({
                    ...{ tunnelPage: '@sage/xtrem-test/TunnelPage', ...mockFieldProperties },
                    columns: getColumns(),
                });
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                await waitFor(() => expect(getDropdownItemsLi()).toHaveLength(2));
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                fireEvent.keyDown(getLookupLinkButton()!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const crateNewLinkButton = getCreateNewLinkButton();
                    expect(document.activeElement).toBe(crateNewLinkButton);
                });
                fireEvent.keyDown(getLookupLinkButton()!, { key: 'ArrowUp' });
                fireEvent.keyDown(getCreateNewLinkButton()!, { key: 'ArrowUp' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    const crateNewLinkButton = getCreateNewLinkButton();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems![0]).toHaveAttribute('aria-selected', 'false');
                    expect(dropdownItems![1]).toHaveAttribute('aria-selected', 'true');
                    expect(document.activeElement).not.toBe(crateNewLinkButton);
                });
            });

            it('should focus the lookup button if tabbed from the lookup link', async () => {
                const { changeInput, getDropdownItemsLi, input, getLookupLinkButton, getLookupButton } = setup({
                    ...mockFieldProperties,
                    columns: getColumns(),
                });
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                await waitFor(() => expect(getDropdownItemsLi()).toHaveLength(2));
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const lookupLinkButton = getLookupLinkButton();
                    expect(document.activeElement).toBe(lookupLinkButton);
                });
                fireEvent.keyDown(getLookupLinkButton()!, { key: 'Tab' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    expect(dropdownItems).toHaveLength(0); // The list should be closed if we focus the lookup button
                    expect(document.activeElement).toBe(getLookupButton()!);
                });
            });

            it('should focus the lookup button if tabbed from the create new link', async () => {
                const {
                    changeInput,
                    getDropdownItemsLi,
                    input,
                    getLookupLinkButton,
                    getCreateNewLinkButton,
                    getLookupButton,
                } = setup({
                    tunnelPage: '@sage/xtrem-test/TunnelPage',
                    ...mockFieldProperties,
                    columns: getColumns(),
                });
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                await waitFor(() => expect(getDropdownItemsLi()).toHaveLength(2));
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                fireEvent.keyDown(input!, { key: 'ArrowDown' });
                fireEvent.keyDown(getLookupLinkButton()!, { key: 'ArrowDown' });
                await waitFor(() => {
                    const createNewLinkButton = getCreateNewLinkButton();
                    expect(document.activeElement).toBe(createNewLinkButton);
                });
                fireEvent.keyDown(getLookupLinkButton()!, { key: 'Tab' });
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    expect(dropdownItems).toHaveLength(0); // The list should be closed if we focus the lookup button
                    expect(document.activeElement).toBe(getLookupButton()!);
                });
            });

            it('should render tunnel link if the user has access to read target nodes', async () => {
                (state.screenDefinitions[screenId] as PageDefinition) = {
                    ...getMockPageDefinition(screenId),
                    accessBindings: {
                        TestNode: {
                            $create: 'authorized',
                            $read: 'authorized',
                        },
                    },
                };
                const { container } = setup(
                    {
                        tunnelPage: '@sage/xtrem-test/TunnelPage',
                        helperTextField: 'name',
                        ...mockFieldProperties,
                        columns: getColumns(),
                    },
                    { _id: '3', code: 'Test code 3', name: 'Test name 3' } as NodeType,
                );

                expect(
                    container.querySelector('[href="@sage/xtrem-test/TunnelPage/eyJfaWQiOiIzIn0="]'),
                ).toHaveTextContent('Test name 3');
            });

            it('should not render tunnel link if the user has no access to read target nodes', async () => {
                (state.screenDefinitions[screenId] as PageDefinition) = {
                    ...getMockPageDefinition(screenId),
                    accessBindings: {
                        TestNode: {
                            $create: 'authorized',
                            $read: 'unauthorized',
                        },
                    },
                };
                const { container } = setup(
                    {
                        tunnelPage: '@sage/xtrem-test/TunnelPage',
                        helperTextField: 'name',
                        ...mockFieldProperties,
                        columns: getColumns(),
                    },
                    { _id: '3', code: 'Test code 3', name: 'Test name 3' } as NodeType,
                );

                expect(container.querySelector('span[data-element="help"]')).toHaveTextContent('Test name 3');
                expect(container.querySelector('[href="@sage/xtrem-test/TunnelPage/eyJfaWQiOiIzIn0="]')).toBeNull();
            });

            it("should highlight items in the suggestion list' value column as the user types", async () => {
                const { changeInput, getDropdownItemsLi, input } = setup({
                    ...mockFieldProperties,
                });
                changeInput('win');
                await waitFor(() => expect(input!.getAttribute('value')).toBe('win'));
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems[0].querySelector('.e-ui-select-highlight')!).toHaveTextContent('win');
                    expect(dropdownItems[1].querySelector('.e-ui-select-highlight')!).toHaveTextContent('Win');
                });
                changeInput('wine');
                await waitFor(() => expect(input!.getAttribute('value')).toBe('wine'));
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    expect(dropdownItems).toHaveLength(2);
                    expect(dropdownItems[0].querySelector('.e-ui-select-highlight')!).toHaveTextContent('wine');
                    expect(dropdownItems[1].querySelector('.e-ui-select-highlight')!).toHaveTextContent('Wine');
                });
            });

            it("should highlight items in the suggestion list's helper text column as the user types", async () => {
                const { changeInput, getDropdownItemsLi, input } = setup({
                    ...mockFieldProperties,
                    helperTextField: 'name',
                });
                changeInput('win');
                await waitFor(() => expect(input!.getAttribute('value')).toBe('win'));
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    expect(dropdownItems).toHaveLength(2);
                    expect(
                        dropdownItems[0].querySelector('.e-ui-select-suggestion-helper .e-ui-select-highlight')!,
                    ).toHaveTextContent('win');
                    expect(
                        dropdownItems[1].querySelector('.e-ui-select-suggestion-helper .e-ui-select-highlight')!,
                    ).toHaveTextContent('Win');
                });
                changeInput('wine');
                await waitFor(() => expect(input!.getAttribute('value')).toBe('wine'));
                await waitFor(() => {
                    const dropdownItems = getDropdownItemsLi();
                    expect(dropdownItems).toHaveLength(2);
                    expect(
                        dropdownItems[0].querySelector('.e-ui-select-suggestion-helper .e-ui-select-highlight')!,
                    ).toHaveTextContent('wine');
                    expect(
                        dropdownItems[1].querySelector('.e-ui-select-suggestion-helper .e-ui-select-highlight')!,
                    ).toHaveTextContent('Wine');
                });
            });
        });

        describe('snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(getReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(getReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(getReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render disabled 2', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(getReferenceField());
                expect(container.querySelector('input')).toBeDisabled();
            });

            it('should render disabled when parent container is disabled', () => {
                const { container } = render(getReferenceField(fieldId, true));
                expect(container.querySelector('input')).toBeDisabled();
            });

            it('should render with full-width', () => {
                mockFieldProperties.isFullWidth = true;
                const { container } = render(getReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render in read-only mode', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = render(getReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render with onOpenLookupDialog callback', () => {
                mockFieldProperties.onOpenLookupDialog = () => {};
                const { container } = render(getReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render with onCloseLookupDialog callback', () => {
                mockFieldProperties.onCloseLookupDialog = () => {};
                const { container } = render(getReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperTextField = 'name';
                const { container } = render(getReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render with columns', () => {
                mockFieldProperties.columns = getColumns();
                const { container } = render(getReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render empty when no value', () => {
                const { container } = render(getReferenceField('test-empty-reference-field'));
                expect(container).toMatchSnapshot();
            });

            it('should render empty when no value field is empty', () => {
                const { queryByTestId } = render(getUnconnectedReferenceField('test-empty-value-field', true));
                const node = queryByTestId('e-field-value');
                expect(node).toHaveTextContent('');
            });

            it('should render in mobile mode', () => {
                const referenceComponent = getReferenceFieldForMobile();
                const { container } = render(referenceComponent);
                expect(container).toMatchSnapshot();
            });

            it('should trigger search and open the lookup dialog after typing one character on mobile', async () => {
                const { container } = render(getReferenceFieldForMobile());
                // Type a single character
                const input = container.querySelector('input')!;
                await userEvent.type(input, 'T');
                // Verify search was triggered once
                await waitFor(() => {
                    expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1);
                    expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledWith(
                        expect.objectContaining({
                            filterValue: 'T',
                            fieldId: 'test-reference-field',
                        }),
                    );
                });
                // Verify lookup dialog was opened
                expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(1);
            });

            it('should auto-select value without opening lookup dialog when only one match exists', async () => {
                mockFieldProperties.isAutoSelectEnabled = true;
                // Mock single result for auto-selection
                jest.spyOn(graphqlService, 'fetchReferenceFieldSuggestions').mockResolvedValue([
                    { _id: '1', code: 'Test1', name: 'Test name 1' } as any,
                ]);
                const { container } = render(getReferenceFieldForMobile());
                // Type a search term
                const input = container.querySelector('input')!;
                fireEvent.change(input, { target: { value: 'Test1' } });
                // Verify search was triggered
                await waitFor(() => {
                    expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1);
                });
                // Verify lookup dialog was NOT opened
                expect(xtremRedux.actions.openDialog).not.toHaveBeenCalled();
                // Verify value was auto-selected
                await waitFor(() => {
                    expect(handleChangeSpy).toHaveBeenCalledTimes(1);
                    expect(handleChangeSpy).toHaveBeenNthCalledWith(
                        1,
                        'test-reference-field',
                        expect.objectContaining({
                            _id: '1',
                            code: 'Test1',
                            name: 'Test name 1',
                        }),
                        expect.any(Function),
                        expect.any(Function),
                        expect.any(Function),
                    );
                });
            });

            it('should auto-select null and not open the lookup dialog when no match exists', async () => {
                mockFieldProperties.isAutoSelectEnabled = true;
                // Mock single result for auto-selection
                jest.spyOn(graphqlService, 'fetchReferenceFieldSuggestions').mockResolvedValue([]);
                const { container } = render(getReferenceFieldForMobile());
                // Type a search term
                const input = container.querySelector('input')!;
                fireEvent.change(input, { target: { value: 'Test1' } });
                // Verify search was triggered
                await waitFor(() => {
                    expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1);
                });
                // Verify value was auto-selected
                await waitFor(() => {
                    expect(handleChangeSpy).toHaveBeenCalledTimes(1);
                    expect(handleChangeSpy).toHaveBeenNthCalledWith(
                        1,
                        'test-reference-field',
                        null,
                        expect.any(Function),
                        expect.any(Function),
                        expect.any(Function),
                    );
                });
                // Verify lookup dialog was opened
                expect(xtremRedux.actions.openDialog).not.toHaveBeenCalled();
            });

            it('should open the lookup dialog when no match exists', async () => {
                // Mock single result for auto-selection
                jest.spyOn(graphqlService, 'fetchReferenceFieldSuggestions').mockResolvedValue([
                    { _id: '1', code: 'Test1', name: 'Test name 1' } as any,
                ]);
                const { container } = render(getReferenceFieldForMobile());
                // Type a search term
                const input = container.querySelector('input')!;
                await userEvent.type(input, 'Test1');
                // Verify search was triggered
                await waitFor(() => {
                    expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1);
                });
                await waitFor(() => {
                    expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(1);
                    expect(handleChangeSpy).not.toHaveBeenCalled();
                });
            });

            it('should not call onChange when lookup dialog is open', async () => {
                const { container } = render(getReferenceFieldForMobile());
                // Type a search term
                const input = container.querySelector('input')!;
                await userEvent.type(input, 'Test1');
                // Verify search was triggered
                await waitFor(() => {
                    expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1);
                });
                // Verify lookup dialog was opened
                expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(1);
                // Verify the onChange was not called
                expect(handleChangeSpy).not.toHaveBeenCalled();
            });

            it('should render with various field sizes', () => {
                mockFieldProperties.size = 'small';
                let wrapper = render(getReferenceField());
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'medium';
                wrapper = render(getReferenceField());
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'large';
                wrapper = render(getReferenceField());
                expect(wrapper.container).toMatchSnapshot();
            });

            it('should render with an info message', async () => {
                mockFieldProperties.infoMessage = 'Info message!!';
                const { baseElement } = render(getReferenceField());

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an info message from a callback', async () => {
                mockFieldProperties.infoMessage = () => 'Info message!!';
                const { baseElement } = render(getReferenceField());

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an warning message', async () => {
                mockFieldProperties.warningMessage = 'Warning message!!';
                const { baseElement } = render(getReferenceField());

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with an warning message from a callback', async () => {
                mockFieldProperties.warningMessage = () => 'Warning message!!';
                const { baseElement } = render(getReferenceField());

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render read-only using a conditional declaration', () => {
                mockFieldProperties.isReadOnly = () => {
                    return true;
                };

                let wrapper = render(getReferenceField());

                expect(wrapper.container.querySelector('input[readonly]')).not.toBeNull();

                mockFieldProperties.isReadOnly = () => {
                    return false;
                };

                wrapper = render(getReferenceField());

                expect(wrapper.container.querySelector('input[readonly]')).toBeNull();
            });

            describe('with image', () => {
                it('should render with image value inside the input field', () => {
                    mockFieldProperties.imageField = 'image';
                    const { container } = render(
                        <Provider store={mockStore}>
                            <ConnectedReferenceComponent screenId={screenId} elementId={fieldId} />
                        </Provider>,
                    );
                    expect(container).toMatchSnapshot();
                });

                it('should render with image value next to the field when read-only', () => {
                    mockFieldProperties.imageField = 'image';
                    mockFieldProperties.isReadOnly = true;
                    const { container } = render(
                        <Provider store={mockStore}>
                            <ConnectedReferenceComponent screenId={screenId} elementId={fieldId} />
                        </Provider>,
                    );
                    expect(container).toMatchSnapshot();
                });

                it('should render with image value next to the field when disabled', () => {
                    mockFieldProperties.imageField = 'image';
                    mockFieldProperties.isDisabled = true;
                    const { container } = render(
                        <Provider store={mockStore}>
                            <ConnectedReferenceComponent screenId={screenId} elementId={fieldId} />
                        </Provider>,
                    );
                    expect(container).toMatchSnapshot();
                });

                it('should render with medium image value next to the field when disabled and no helper text', () => {
                    mockFieldProperties.imageField = 'image';
                    mockFieldProperties.isDisabled = true;
                    mockFieldProperties.helperTextField = undefined;
                    const { container } = render(
                        <Provider store={mockStore}>
                            <ConnectedReferenceComponent screenId={screenId} elementId={fieldId} />
                        </Provider>,
                    );
                    expect(container).toMatchSnapshot();
                });
            });
        });

        describe('interactions', () => {
            describe('openLookupDialog', () => {
                beforeEach(() => {
                    jest.spyOn(xtremRedux.actions, 'openDialog');
                });
                afterEach(() => {
                    jest.clearAllMocks();
                });

                it('should open lookup dialog on desktop', async () => {
                    mockFieldProperties.columns = getColumns();
                    const { queryByTestId } = render(getReferenceField());
                    const lookupButton = queryByTestId('e-ui-select-lookup-button');
                    expect(lookupButton).not.toBeNull();
                    expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(0);
                    fireEvent.click(lookupButton as HTMLElement);
                    await waitFor(() => {
                        expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(1);
                        expect(xtremRedux.actions.openDialog).toHaveBeenLastCalledWith(
                            expect.any(Number),
                            expect.objectContaining({ title: 'Test Field Title' }),
                        );
                    });
                });

                it('should open lookup dialog on mobile', async () => {
                    mockFieldProperties.columns = getColumns();
                    const { queryByTestId } = render(getReferenceFieldForMobile());
                    const lookupButton = queryByTestId('e-ui-select-lookup-button');
                    expect(lookupButton).not.toBeNull();
                    expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(0);
                    fireEvent.click(lookupButton as HTMLElement);
                    await waitFor(() => {
                        expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(1);
                        expect(xtremRedux.actions.openDialog).toHaveBeenLastCalledWith(
                            expect.any(Number),
                            expect.objectContaining({
                                content: expect.objectContaining({
                                    searchText: 'XX123',
                                }),
                            }),
                        );
                    });
                });
            });

            describe('lookup dialog render', () => {
                beforeEach(() => {
                    mockFieldProperties.columns = getColumns();
                    mockFieldProperties.isReferenceDialogOpen = true;
                });

                it('should trigger onInputValueChange when the value of the field has changed with some delay', async () => {
                    const { container } = render(
                        <ReferenceComponent<NodeType>
                            elementId={fieldId}
                            fieldProperties={mockFieldProperties}
                            value={{
                                _id: 'XX123',
                                code: '1234567',
                                name: 'Capsule corp.',
                                column1: 'column1',
                                column2: 'column2',
                            }}
                            setFieldValue={jest.fn().mockResolvedValue(undefined)}
                            validate={jest.fn().mockResolvedValue(undefined)}
                            removeNonNestedErrors={jest.fn()}
                            screenId={screenId}
                            onFocus={jest.fn()}
                            locale="en-US"
                            openTunnel={jest.fn().mockResolvedValue(null)}
                            hasAccessToTunnelPage={false}
                        />,
                    );

                    const input = container.querySelector('input')!;

                    expect(input).not.toBeNull();

                    fireEvent.change(input, { target: { value: '1234567890' } });

                    expect(events.triggerFieldEvent).not.toHaveBeenCalled();
                    await waitFor(() => {
                        expect(events.triggerFieldEvent).toHaveBeenCalledWith(
                            screenId,
                            fieldId,
                            'onInputValueChange',
                            '1234567890',
                        );
                    });
                });
            });
        });
    });

    describe('unconnected', () => {
        let setFieldValueMock: jest.Mock;
        let validateMock: jest.Mock;
        let removeErrorsFromFieldMock: jest.Mock;
        const defaultValue = () => ({
            _id: 'XX123',
            code: 'XX123',
            name: 'Capsule corp.',
            column1: 'column1',
            column2: 'column2',
        });
        const mountReferenceComponent = (
            customFieldProperties: Partial<
                React.ComponentProps<Class<ReferenceComponent<NodeType>>>['fieldProperties']
            > = {},
            value = defaultValue(),
        ) => {
            return render(
                <ReferenceComponent<NodeType>
                    elementId={fieldId}
                    fieldProperties={{ ...mockFieldProperties, ...customFieldProperties }}
                    value={value}
                    setFieldValue={setFieldValueMock}
                    validate={validateMock}
                    removeNonNestedErrors={removeErrorsFromFieldMock}
                    screenId={screenId}
                    onFocus={jest.fn()}
                    locale="en-US"
                    openTunnel={jest.fn().mockResolvedValue(null)}
                    hasAccessToTunnelPage={false}
                />,
            );
        };

        beforeEach(() => {
            jest.useFakeTimers();
            setFieldValueMock = jest.fn().mockResolvedValue(undefined);
            validateMock = jest.fn().mockResolvedValue(undefined);
            removeErrorsFromFieldMock = jest.fn();
            jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);
            jest.spyOn(events, 'triggerFieldEvent').mockImplementation(jest.fn());
        });

        afterEach(() => {
            jest.useRealTimers();
        });

        describe('snapshots', () => {
            it('should render just fine', () => {
                const { container } = render(
                    <ReferenceComponent<NodeType>
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        value={{
                            _id: 'XX123',
                            code: 'XX123',
                            name: 'Capsule corp.',
                            column1: 'column1',
                            column2: 'column2',
                        }}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={removeErrorsFromFieldMock}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                        openTunnel={jest.fn().mockResolvedValue(null)}
                        hasAccessToTunnelPage={false}
                    />,
                );
                expect(container).toMatchSnapshot();
            });
        });

        describe('interactions', () => {
            const triggerOnBlur = (wrapper: HTMLElement, relatedTarget?: { id?: string }) => {
                const reference = wrapper.querySelector('input')!;
                expect(reference).not.toBeNull();
                fireEvent.blur(reference, { relatedTarget: relatedTarget || new EventTarget() });
            };

            it('should not call handleChange on blur', () => {
                const { container } = mountReferenceComponent();

                triggerOnBlur(container);

                expect(handleChangeSpy).not.toHaveBeenCalled();
            });

            it('should clear value when an empty value is set', () => {
                const { container } = mountReferenceComponent();

                const input = container.querySelector('input')!;
                expect(input).toHaveValue('XX123');
                fireEvent.change(input, { target: { value: '' } });
                expect(input).toHaveValue('');
            });

            it('value should be the same', () => {
                const { container, rerender } = mountReferenceComponent();

                const input = container.querySelector('input')!;
                expect(input).toHaveValue('XX123');
                rerender(
                    <ReferenceComponent<NodeType>
                        elementId={fieldId}
                        fieldProperties={{ ...mockFieldProperties }}
                        value={{ code: 'XX123' } as any}
                        setFieldValue={setFieldValueMock}
                        validate={validateMock}
                        removeNonNestedErrors={removeErrorsFromFieldMock}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                        openTunnel={jest.fn().mockResolvedValue(null)}
                        hasAccessToTunnelPage={false}
                    />,
                );
                expect(input).toHaveValue('XX123');
            });

            it('should change helperText', () => {
                const { container, rerender } = mountReferenceComponent({
                    valueField: 'name',
                    helperTextField: 'code',
                });

                const input = container.querySelector('input')!;
                expect(input).toHaveValue('Capsule corp.');
                expect(container.querySelector('span[data-element="help"]')).toHaveTextContent('XX123');
                rerender(
                    <ReferenceComponent<NodeType>
                        elementId={fieldId}
                        fieldProperties={{ ...mockFieldProperties, valueField: 'name', helperTextField: 'code' }}
                        value={{ code: 'XX1234', name: 'Capsule corp.' } as any}
                        setFieldValue={setFieldValueMock}
                        validate={validateMock}
                        removeNonNestedErrors={removeErrorsFromFieldMock}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                        openTunnel={jest.fn().mockResolvedValue(null)}
                        hasAccessToTunnelPage={false}
                    />,
                );

                expect(container.querySelector('span[data-element="help"]')).toHaveTextContent('XX1234');
            });

            it('should not call handleChange on blur when component is read only', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = mountReferenceComponent();

                triggerOnBlur(container);
                expect(handleChangeSpy).not.toHaveBeenCalled();
            });

            it('should not call handleChange on blur when component is disable', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = mountReferenceComponent();

                triggerOnBlur(container);
                expect(handleChangeSpy).not.toHaveBeenCalled();
            });

            it('should NOT call handleChange on blur when previously selected item is restored', () => {
                const { container } = mountReferenceComponent();
                const input = container.querySelector('input')!;
                fireEvent.change(input, { target: { value: 'anything' } });
                triggerOnBlur(container);
                expect(handleChangeSpy).not.toHaveBeenCalled();
            });

            it('should call handleChange on blur with null', () => {
                const { container } = mountReferenceComponent();
                const input = container.querySelector('input')!;

                fireEvent.change(input, { target: { value: '' } });

                triggerOnBlur(container);
                expect(handleChangeSpy).toHaveBeenCalledTimes(1);
                expect(handleChangeSpy).toHaveBeenNthCalledWith(
                    1,
                    fieldId,
                    null,
                    setFieldValueMock,
                    validateMock,
                    expect.any(Function),
                );
            });

            it('should clear value with autoselect', () => {
                const { container } = mountReferenceComponent();
                const input = container.querySelector('input')!;

                fireEvent.change(input, { target: { value: '' } });
                triggerOnBlur(container);
                expect(handleChangeSpy).toHaveBeenCalledTimes(1);
                expect(handleChangeSpy).toHaveBeenNthCalledWith(
                    1,
                    fieldId,
                    null,
                    setFieldValueMock,
                    validateMock,
                    expect.any(Function),
                );
            });
        });
    });
});
