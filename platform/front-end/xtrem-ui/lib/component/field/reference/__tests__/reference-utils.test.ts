import * as utils from '../reference-utils';

describe('reference utils tests', () => {
    describe('getReferenceOrderBy', () => {
        it('should order ascending by _id given no orderBy, columns and valueField', () => {
            const fieldProperties: any = {
                bind: undefined,
                columns: [],
                orderBy: undefined,
                valueField: undefined,
            };
            const result = utils.getReferenceOrderBy(fieldProperties);
            expect(result).toEqual({ _id: 1 });
        });

        it('should order by orderBy given orderBy', () => {
            const fieldProperties: any = {
                bind: undefined,
                columns: [],
                orderBy: {
                    foo: 1,
                    bar: 1,
                    _id: -1,
                },
                valueField: undefined,
            };
            const result = utils.getReferenceOrderBy(fieldProperties);
            expect(result).toEqual({ foo: 1, bar: 1, _id: -1 });
        });

        it('should order by orderBy and lastly ascending by _id given orderby without _id', () => {
            const fieldProperties: any = {
                bind: undefined,
                columns: [],
                orderBy: {
                    foo: 1,
                    bar: 1,
                },
                valueField: undefined,
            };
            const result = utils.getReferenceOrderBy(fieldProperties);
            expect(result).toEqual({ foo: 1, bar: 1, _id: 1 });
        });

        it('should order by first column given no orderBy but first column bound to _id', () => {
            const fieldProperties: any = {
                bind: 'foo',
                columns: [{ properties: { bind: '_id' } }],
                orderBy: undefined,
                valueField: undefined,
            };
            const result = utils.getReferenceOrderBy(fieldProperties);
            expect(result).toEqual({ _id: 1 });
        });

        it('should order by first column and lastly ascending by _id given no orderBy but first column not bound to _id', () => {
            const fieldProperties: any = {
                bind: 'foo',
                columns: [{ properties: { bind: 'bar' } }],
                orderBy: undefined,
                valueField: undefined,
            };
            const result = utils.getReferenceOrderBy(fieldProperties);
            expect(result).toEqual({ bar: 1, _id: 1 });
        });

        it('should order by valueField given no orderby or columns but valueField bound to _id', () => {
            const fieldProperties: any = {
                bind: undefined,
                columns: [],
                orderBy: undefined,
                valueField: '_id',
            };
            const result = utils.getReferenceOrderBy(fieldProperties);
            expect(result).toEqual({ _id: 1 });
        });

        it('should order by valueField and lastly ascending by _id given no orderby or columns but valueField not bound to _id', () => {
            const fieldProperties: any = {
                bind: undefined,
                columns: [],
                orderBy: undefined,
                valueField: 'foo',
            };
            const result = utils.getReferenceOrderBy(fieldProperties);
            expect(result).toEqual({ foo: 1, _id: 1 });
        });
    });
});
