// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Reference component connected snapshots should render disabled 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
  color: var(--colorsUtilityYin030);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
  color: var(--colorsUtilityYin030);
  cursor: not-allowed;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background: var(--colorsUtilityDisabled400);
  border-color: var(--colorsUtilityDisabled600);
  cursor: not-allowed;
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-reference-field e-disabled"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                disabled=""
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                disabled=""
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  disabled=""
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render empty when no value 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c13::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: hidden;
}

<div>
  <div
    class="e-field e-reference-field"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-empty-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-empty-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-empty-reference-field"
                id="TestPage-test-empty-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-empty-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-close"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      hidden=""
                      tabindex="-1"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c11 c13"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-empty-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render helperText 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c13::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c14 {
  display: block;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin-top: 8px;
  white-space: pre-wrap;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-describedby="TestPage-test-reference-field-field-help"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c11 c13"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <span
            class="c14"
            data-element="help"
            id="TestPage-test-reference-field-field-help"
          >
            Capsule corp.
          </span>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="top: -8px; position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render hidden 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c13::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field e-hidden"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c11 c13"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render in mobile mode 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c15 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c15:hover {
  color: #006437;
  background-color: transparent;
}

.c15::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e96f";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c13 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: transparent;
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
  padding: 0px;
  width: 40px;
  min-height: 40px;
}

.c13:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c13 .c11 {
  color: var(--colorsActionMajor500);
}

.c13:hover {
  background: var(--colorsActionMajor600);
  color: var(--colorsActionMajorYang100);
}

.c13:hover .c11 {
  color: var(--colorsActionMajorYang100);
}

.c13 .c11 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c13 .c11 svg {
  margin-top: 0;
}

.c14 {
  border-radius: var(--borderRadius050);
  background: transparent;
  padding: var(--spacing100);
  color: var(--colorsActionMinor500);
  padding-left: var(--spacing150);
  padding-right: var(--spacing150);
}

.c14 .c11 {
  position: absolute;
}

.c14 .c11 {
  color: var(--colorsActionMinor500);
}

.c14:hover {
  color: var(--colorsActionMinorYang100);
  background: var(--colorsActionMinor600);
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field"
      style="align-items: flex-end;"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 0px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-search-text-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0; overflow: hidden; flex: 1 1 0%;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                  style="flex: 0 0 24px;"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-lookup-button"
                  style="flex: 0 0 40px;"
                >
                  <button
                    aria-label="lookup"
                    class="c13 c14"
                    data-component="button-minor"
                    data-component-size="medium"
                    data-testid="e-ui-select-lookup-button"
                    draggable="false"
                    id="e-reference-field-lookup-icon-uniqguidmock"
                    type="button"
                  >
                    <span
                      aria-hidden="true"
                      class="c11 c15"
                      color="--colorsActionMajor500"
                      data-component="icon"
                      data-element="lookup"
                      data-role="icon"
                      font-size="small"
                      type="lookup"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render in read-only mode 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background-color: var(--colorsUtilityReadOnly400);
  border-color: var(--colorsUtilityReadOnly600);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-reference-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                readonly=""
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  readonly=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render with columns 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c15 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c15:hover {
  color: #006437;
  background-color: transparent;
}

.c15::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e96f";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c13 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: transparent;
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
  padding: 0px;
  width: 40px;
  min-height: 40px;
}

.c13:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c13 .c11 {
  color: var(--colorsActionMajor500);
}

.c13:hover {
  background: var(--colorsActionMajor600);
  color: var(--colorsActionMajorYang100);
}

.c13:hover .c11 {
  color: var(--colorsActionMajorYang100);
}

.c13 .c11 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c13 .c11 svg {
  margin-top: 0;
}

.c14 {
  border-radius: var(--borderRadius050);
  background: transparent;
  padding: var(--spacing100);
  color: var(--colorsActionMinor500);
  padding-left: var(--spacing150);
  padding-right: var(--spacing150);
}

.c14 .c11 {
  position: absolute;
}

.c14 .c11 {
  color: var(--colorsActionMinor500);
}

.c14:hover {
  color: var(--colorsActionMinorYang100);
  background: var(--colorsActionMinor600);
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field e-reference-field-lookup"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 0px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0; overflow: hidden; flex: 1 1 0%;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                  style="flex: 0 0 24px;"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-lookup-button"
                  style="flex: 0 0 40px;"
                >
                  <button
                    aria-label="lookup"
                    class="c13 c14"
                    data-component="button-minor"
                    data-component-size="medium"
                    data-testid="e-ui-select-lookup-button"
                    draggable="false"
                    id="e-reference-field-lookup-icon-uniqguidmock"
                    type="button"
                  >
                    <span
                      aria-hidden="true"
                      class="c11 c15"
                      color="--colorsActionMajor500"
                      data-component="icon"
                      data-element="lookup"
                      data-role="icon"
                      font-size="small"
                      type="lookup"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render with default properties 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c13::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c11 c13"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render with full-width 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c13::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field e-full-width"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c11 c13"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render with onCloseLookupDialog callback 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c13::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c11 c13"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render with onOpenLookupDialog callback 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c13::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c11 c13"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render with various field sizes 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c13::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing400);
}

.c6 .c7 {
  padding: 0 var(--spacing100);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c11 c13"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render with various field sizes 2`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c13::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c14 .c7 {
  padding: 0 var(--spacing100);
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-3-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-3-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-3-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c11 c13"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-3-label"
        class="e-ui-select-dropdown"
        id="downshift-3-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots should render with various field sizes 3`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c13::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c14 .c7 {
  padding: 0 var(--spacing100);
}

.c15 .c7 {
  padding: 0 var(--spacing150);
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing600);
}

.c6 .c7 {
  padding: 0 var(--spacing200);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-6-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-6-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-6-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c11 c13"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-6-label"
        class="e-ui-select-dropdown"
        id="downshift-6-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots with image should render with image value inside the input field 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c14 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c14::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c15 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c15::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c9 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c11 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c11.c11 {
  padding: var(--spacing000);
}

.c11:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c11:hover {
  cursor: pointer;
}

.c11::-moz-focus-inner {
  border: none;
}

.c11 .c13 {
  position: relative;
}

.c11 .c13:focus {
  border: none;
}

.c8 {
  height: inherit;
  min-width: inherit;
}

.c7 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  max-width: 32px;
  min-width: 32px;
  height: 32px;
  overflow: hidden;
  border-radius: 0px;
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

.c12 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-inline-image"
                  >
                    <div
                      class="e-portrait"
                    >
                      <div
                        class="c7"
                        data-component="portrait"
                        shape="square"
                      >
                        <img
                          alt="iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=="
                          class="c8"
                          data-element="user-image"
                          src="data:image;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=="
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c9 c10 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c11 c12"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c13 c14"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c13 c15"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots with image should render with image value next to the field when disabled 1`] = `
.c3 {
  margin-bottom: var(--fieldSpacing);
}

.c2 + .c2 {
  margin-top: 16px;
}

.c3.c3.c3 {
  margin: var(--spacing000);
}

.c4 {
  display: block;
}

.c6 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
  color: var(--colorsUtilityYin030);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
  color: var(--colorsUtilityYin030);
  cursor: not-allowed;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c7 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background: var(--colorsUtilityDisabled400);
  border-color: var(--colorsUtilityDisabled600);
  cursor: not-allowed;
}

.c8 .c9 {
  padding: 0 var(--spacing150);
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c1 {
  height: inherit;
  min-width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  max-width: 72px;
  min-width: 72px;
  height: 72px;
  overflow: hidden;
  border-radius: 0px;
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div>
  <div
    class="e-field e-reference-field e-reference-inline-picture e-disabled"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-portrait"
    >
      <div
        class="c0"
        data-component="portrait"
        shape="square"
      >
        <img
          alt="XX123"
          class="c1"
          data-element="user-image"
          src="data:image;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=="
        />
      </div>
    </div>
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c2 c3"
        >
          <div
            class="c4"
            data-role="field-line"
          >
            <div
              class="c5"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c6"
                data-element="label"
                disabled=""
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c7"
              data-role="input-presentation-container"
            >
              <div
                class="c8"
                disabled=""
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c9 c10 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  disabled=""
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots with image should render with image value next to the field when read-only 1`] = `
.c3 {
  margin-bottom: var(--fieldSpacing);
}

.c2 + .c2 {
  margin-top: 16px;
}

.c3.c3.c3 {
  margin: var(--spacing000);
}

.c4 {
  display: block;
}

.c6 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c7 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background-color: var(--colorsUtilityReadOnly400);
  border-color: var(--colorsUtilityReadOnly600);
}

.c8 .c9 {
  padding: 0 var(--spacing150);
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c1 {
  height: inherit;
  min-width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  max-width: 72px;
  min-width: 72px;
  height: 72px;
  overflow: hidden;
  border-radius: 0px;
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div>
  <div
    class="e-field e-reference-field e-reference-inline-picture e-read-only"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-portrait"
    >
      <div
        class="c0"
        data-component="portrait"
        shape="square"
      >
        <img
          alt="XX123"
          class="c1"
          data-element="user-image"
          src="data:image;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=="
        />
      </div>
    </div>
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c2 c3"
        >
          <div
            class="c4"
            data-role="field-line"
          >
            <div
              class="c5"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c6"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c7"
              data-role="input-presentation-container"
            >
              <div
                class="c8"
                readonly=""
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c9 c10 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  readonly=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component connected snapshots with image should render with medium image value next to the field when disabled and no helper text 1`] = `
.c3 {
  margin-bottom: var(--fieldSpacing);
}

.c2 + .c2 {
  margin-top: 16px;
}

.c3.c3.c3 {
  margin: var(--spacing000);
}

.c4 {
  display: block;
}

.c6 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
  color: var(--colorsUtilityYin030);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c10 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
  color: var(--colorsUtilityYin030);
  cursor: not-allowed;
}

.c10:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c10::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c10::placeholder {
  color: var(--colorsUtilityYin055);
}

.c7 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c8 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background: var(--colorsUtilityDisabled400);
  border-color: var(--colorsUtilityDisabled600);
  cursor: not-allowed;
}

.c8 .c9 {
  padding: 0 var(--spacing150);
}

.c8 input::-ms-clear {
  display: none;
}

.c8 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c1 {
  height: inherit;
  min-width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  max-width: 72px;
  min-width: 72px;
  height: 72px;
  overflow: hidden;
  border-radius: 0px;
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div>
  <div
    class="e-field e-reference-field e-reference-inline-picture e-disabled"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-portrait"
    >
      <div
        class="c0"
        data-component="portrait"
        shape="square"
      >
        <img
          alt="XX123"
          class="c1"
          data-element="user-image"
          src="data:image;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=="
        />
      </div>
    </div>
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c2 c3"
        >
          <div
            class="c4"
            data-role="field-line"
          >
            <div
              class="c5"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c6"
                data-element="label"
                disabled=""
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c7"
              data-role="input-presentation-container"
            >
              <div
                class="c8"
                disabled=""
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c9 c10 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  disabled=""
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Reference component unconnected snapshots should render just fine 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c12 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c12::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e992";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c13 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c13::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c11 {
  position: relative;
}

.c9 .c11:focus {
  border: none;
}

.c10 {
  visibility: visible;
}

<div>
  <div
    class="e-field e-reference-field"
    data-label="Test Field Title"
    data-testid="e-reference-field e-field-label-testFieldTitle e-field-bind-test-reference-field"
  >
    <div
      class="e-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-reference-field"
                id="TestPage-test-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value="XX123"
                />
                <div
                  class="e-ui-select-close"
                >
                  <span
                    class="e-ui-select-close-icon"
                  >
                    <button
                      aria-label="Clear"
                      class="c9 c10"
                      data-component="icon-button"
                      data-testid="e-ui-select-close"
                      tabindex="0"
                      type="button"
                    >
                      <div>
                        <span
                          class="c11 c12"
                          data-component="icon"
                          data-element="cross_circle"
                          data-role="icon"
                          font-size="small"
                          type="cross_circle"
                        />
                      </div>
                    </button>
                  </span>
                </div>
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c11 c13"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;
