import type { Page } from '../../../..';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { ReferenceDecoratorProperties } from '../reference-types';
import { referenceField } from '../reference-decorator';

type ReferenceType = {
    _id: string;
    name: string;
};
describe('Reference Decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'referenceField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should set default values when no component properties provided', () => {
        referenceField<any, ReferenceType>({
            node: '@sage/xtrem-ui-test/test-property',
            valueField: 'name',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: ReferenceDecoratorProperties<ScreenBase> = pageMetadata.uiComponentProperties[
            fieldId
        ] as ReferenceDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onChange).toBeUndefined();
        expect(mappedComponentProperties.onClick).toBeUndefined();
    });

    it('should inherit false abstract-field booleans when no provided', () => {
        referenceField<any, ReferenceType>({
            node: '@sage/xtrem-ui-test/test-property',
            valueField: 'name',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: ReferenceDecoratorProperties<ScreenBase> = pageMetadata.uiComponentProperties[
            fieldId
        ] as ReferenceDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.isHiddenMobile).toBe(false);
        expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
        expect(mappedComponentProperties.isFullWidth).toBe(false);
        expect(mappedComponentProperties.isHidden).toBe(false);
        expect(mappedComponentProperties.isTransient).toBe(false);
    });

    it('should set values when component properties provided', () => {
        const clickFunc: () => void = jest.fn().mockImplementation(() => {});
        const changeFunc: () => void = jest.fn().mockImplementation(() => {});

        referenceField<any, ReferenceType>({
            onChange: changeFunc,
            onClick: clickFunc,
            minLookupCharacters: 5,
            node: '@sage/xtrem-ui/test-property',
            valueField: 'name',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: ReferenceDecoratorProperties<ScreenBase> = pageMetadata.uiComponentProperties[
            fieldId
        ] as ReferenceDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onChange).not.toBeUndefined();
        expect(mappedComponentProperties.onChange).toBe(changeFunc);
        expect(mappedComponentProperties.onClick).not.toBeUndefined();
        expect(mappedComponentProperties.onClick).toBe(clickFunc);
        expect(mappedComponentProperties.minLookupCharacters).toBe(5);
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(referenceField, pageMetadata, fieldId);
        });
    });
});
