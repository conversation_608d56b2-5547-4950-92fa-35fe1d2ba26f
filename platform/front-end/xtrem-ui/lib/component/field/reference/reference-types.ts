import type { ClientNode } from '@sage/xtrem-client';
import type { IsStrictlyAny } from '@sage/xtrem-shared';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import type { BlockControlObject, TileControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { GridNestedFieldTypes, NestedField, NestedPropertiesWrapper } from '../../nested-fields';
import type {
    ClientCollectionRecursiveBind,
    FieldControlObjectInstance,
    Id,
    PropertyRecursiveBind,
    ReferenceRecursiveBind,
    ReferenceRecursiveOrderBy,
} from '../../types';
import type { SelectItem } from '../../ui/select/select-component';
import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasDynamicLookupSuggestions,
    HasFilter,
    HasHelperTextField,
    HasIcon,
    HasImageField,
    HasInputValueChangeListener,
    HasLookupDialogTitle,
    HasNestedFilter,
    HasNode,
    HasParent,
    HasServerRecordMapperFunction,
    HasSound,
    HasTunnel,
    HasValueField,
    Nested,
    NestedChangeable,
    NestedClickable,
    NestedGroupAggregations,
    NestedHasLookupDialogTitle,
    NestedValidatable,
    Sizable,
    Validatable,
} from '../traits';
import type { CardDefinition } from '../../ui/card/card-component';

export type PropertyValueType<T extends ClientNode = any> =
    IsStrictlyAny<T> extends true ? any : (keyof T & string) | Id<PropertyRecursiveBind<T>>;

export type ClientCollectionValueType<T extends ClientNode = any> =
    IsStrictlyAny<T> extends true ? any : (keyof T & string) | Id<ClientCollectionRecursiveBind<T>>;

export type ReferenceValueType<T extends ClientNode = any> =
    IsStrictlyAny<T> extends true ? any : (keyof T & string) | Id<ReferenceRecursiveBind<T>>;

export interface ReferenceProperties<
    CT extends ScreenExtension<CT> = ScreenBase,
    ReferencedItemType extends ClientNode = any,
    ContextNodeType = any,
> extends EditableFieldProperties<CT, ContextNodeType>,
        HasDynamicLookupSuggestions<CT, ReferencedItemType>,
        HasFilter<CT, ReferencedItemType>,
        HasIcon,
        HasSound<CT>,
        HasNode<CT>,
        HasValueField<ReferencedItemType>,
        HasHelperTextField<ReferencedItemType>,
        HasImageField<ReferencedItemType>,
        HasTunnel<ReferencedItemType>,
        Sizable {
    /** Whether the rows of the reference lookup can be filtered or not. Defaults to true */
    canFilter?: boolean;
    /** Column definitions for the lookup dialog */
    columns?: NestedField<CT, GridNestedFieldTypes, ReferencedItemType>[];
    /** The definitions of the nested fields used to represent the lookup dialog table rows in mobile devices.
     * If no value is provided, the first four columns are used by default */
    mobileCard?: CardDefinition<CT, ReferencedItemType>;
    /** Minimum number of characters to trigger server lookup. If `0` supplied, the first page of suggestions will be fetched on focus. Defaults to 3 */
    minLookupCharacters?: number;
    /** The column or the set of columns which the table should be sorted by */
    orderBy?: ReferenceRecursiveOrderBy<ReferencedItemType>;
    /** Placeholder to be displayed in the field body */
    placeholder?: string;
    /** Auto-select item if search matches only one element */
    isReferenceDialogOpen?: boolean;
    /** Auto-select item if search matches only one element */
    isAutoSelectEnabled?: boolean;
    /** Should auto select and suggestion be based on all known columns? */
    shouldSuggestionsIncludeColumns?: boolean;
}

export interface ReferenceDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    ReferencedItemType extends ClientNode = any,
> extends Omit<ReferenceProperties<CT, ReferencedItemType>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        HasInputValueChangeListener<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasLookupDialogTitle<CT>,
        HasParent<CT, BlockControlObject<CT> | TileControlObject<CT>>,
        HasServerRecordMapperFunction<CT, ReferencedItemType>,
        HasSound<CT>,
        Sizable,
        Validatable<CT, ReferencedItemType> {
    /** Function to be executed when the reference's dialog is opened */
    onOpenLookupDialog?: (this: CT) => void;
    /** Function to be executed when the reference's dialog is closed */
    onCloseLookupDialog?: (this: CT) => void;
    /** Minimum number of characters to trigger server lookup */
    minLookupCharacters?: number;
    /** Whether the dropdown is disabled or not. If disabled the lookup dialog will be forcefully enabled. */
    isDropdownDisabled?: boolean;
}

export interface NestedReferenceProperties<
    CT extends ScreenBase = ScreenBase,
    ContextNodeType extends ClientNode = any,
    ReferencedNode extends ClientNode = any,
> extends Omit<NestedPropertiesWrapper<ReferenceProperties<CT, ReferencedNode, ContextNodeType>>, 'filter'>,
        NestedChangeable<CT>,
        NestedClickable<CT, ContextNodeType>,
        Nested<ContextNodeType, ReferenceValueType<ContextNodeType>>,
        NestedGroupAggregations<'distinctCount'>,
        Sizable,
        NestedHasLookupDialogTitle<CT, ContextNodeType>,
        HasNestedFilter<CT, ContextNodeType, ReferencedNode>,
        NestedValidatable<CT, ReferencedNode, ContextNodeType> {
    /** Minimum number of characters to trigger server lookup */
    minLookupCharacters?: number;
    /** Whether the dropdown is disabled or not. If disabled the lookup dialog will be set to true. */
    isDropdownDisabled?: boolean;
    /** Limit the values to the ones used in the existing dataset */
    isFilterLimitedToDataset?: boolean;
    /** Path reference to an other page. The user can use this page to view the details or modify the selected record.
     * When no value is selected, the user can create a new record using this page.
     * */
    tunnelPage?: string;
    /** Function to be executed when the reference's dialog is opened */
    onOpenLookupDialog?: (this: CT, _id: string) => void;
    /** Function to be executed when the reference's dialog is closed */
    onCloseLookupDialog?: (this: CT, _id: string) => void;
}

export type BaseReferenceExtensionDecoratorProperties<CT extends ScreenBase = ScreenBase> = {
    onCloseLookupDialogAfter?: (this: CT, _id: any) => void;
    onOpenLookupDialogAfter?: (this: CT, _id: any) => void;
    onInputValueChangeAfter?: (this: CT, inputValue: string, isBulkChange: boolean) => void;
};

export type ReferenceComponentProps<T extends ClientNode = any> = BaseEditableComponentProperties<
    ReferenceDecoratorProperties<any, T>,
    T,
    NestedFieldsAdditionalProperties
>;

export interface ConnectedReferenceComponentProps<T extends ClientNode = any> extends ReferenceComponentProps<T> {
    hasAccessToTunnelPage: boolean;
    openTunnel: (fieldProperties: ReferenceDecoratorProperties, value: any) => Promise<any | null>;
}

export interface ReferenceComponentState {
    id: string;
    isLookupDialogOpen: boolean;
}

export interface ReferenceCellEditorState {
    id: string;
    highlightOnFocus: boolean;
    isLookupPanelOpen: boolean;
    selectedRecord?: SelectItem;
    startValue: string;
}

export interface ReferenceCellEditorProps extends CellParams<NestedReferenceProperties, any> {}

export type ReferenceCellRendererExternalProps = CellParams<NestedReferenceProperties>;

export interface ReferenceCellRendererProps extends ReferenceCellRendererExternalProps {
    hasAccessToTunnelPage: boolean;
    openTunnel: (fieldProperties: ReferenceDecoratorProperties, value: any) => Promise<any | null>;
}
