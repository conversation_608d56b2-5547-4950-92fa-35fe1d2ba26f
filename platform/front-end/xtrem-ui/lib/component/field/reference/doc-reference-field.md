PATH: XTREEM/UI+Field+Widgets/Reference+Field

## Introduction

Reference fields can be used to represent a value from node. It is designed to represent links between nodes.
It is also available as a nested field in the navigation panel, tables, charts and calendars.

## Example:

```ts
@ui.decorators.referenceField<ReferenceField, ShowCaseProduct>({
        node: '@sage/x3-products/Product',
        valueField: 'code', // can also be an object such as { provider: { code: true }}
        helperTextField: 'productCategory', // can also be an object such as { provider: { code: true }}
        isFullWidth: true,
        columns: [
            ui.nestedFields.text({ bind: 'accountingCode', title: 'Accounting Code' }),
            ui.nestedFields.text({ bind: 'code', title: 'Product Code' }),
            ui.nestedFields.text({ bind: 'productCategory', title: 'Product Category' }),
        ],
        parent() {
            return this.referenceFieldBlock;
        },
        onChange() {
            if (this.referenceField.value !== this.referenceFieldValue.value) {
                this.referenceFieldValue.value = this.referenceField.value;
            }
            if (this.referenceField.helperText !== this.referenceFieldHelperText.value) {
                this.referenceFieldHelperText.value = this.referenceField.helperText;
            }
        },
    })
    referenceField: ui.fields.Reference;
```

### Display decorator properties

-   **columns**: Column definitions for the lookup dialog.
-   **isDropdownDisabled**: Whether the dropdown is disabled or not. If disabled the lookup dialog will be forcefully enabled.
-   **isFullWidth**: Whether a field should take the full width.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **helperText**: The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **orderBy**: An object that specifies how reference items should be sorted by. E.g. ```{ accountingCode: 1 }```, where `1` indicates `ascending` order and `-1` `descending` order. By default if no `orderBy` is provided then the first `column` will be used. If no `columns` are present then all items will be sorted by their IDs.
-   **placeholder**: Placeholder text which is displayed inside the field body when the field is empty. It is automatically picked up by the i18n engine and externalized.
-   **size**: Vertical size of the field, it can be `small`, `medium` or `large`. It is set to medium by default.
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **isReadOnly**: Whether the field is editable (isReadOnly = false) or not (isReadOnly = true). The difference with disabled is that isReadOnly suggests that the field is never editable. It can be defined as a boolean, or conditionally by a callback that returns a boolean.
-   **icon**: Icon to be displayed on the right side of the input. If the reference field has a lookup dialog, the icon will be displayed on the left of the lookup icon. The list of icons are defined by [DLS](https://brand.sage.com/d/NdbrveWvNheA/foundations#/icons/icons).
-   **iconColor**: Color of the input icon, only supported if the field is rendered within a tile container.
-   **lookupDialogTitle**: The title that is displayed in the lookup dialog of the reference field.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.
-   **isSoundDisabled**: Specifies whether the sound output of the field should be disabled/silenced. Defaults to false.
-   **mobileCard**: The definitions of the nested fields used to represent the lookup dialog table rows in mobile devices. If no value is provided, the first four columns are used by default 

### Binding decorator properties

-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **filter**: The GraphQL filter applied to the values. Nested logic operators such as `_or` within another `_or` filter object is not supported.
-   **helperTextField**: The GraphQL node property that will be displayed below the reference field. This value will override the one provided through helperText. It can be either a **`string`** referring to an existing property of the given **`node`** (e.g. in the above example the product's code) or an `object` in order to be able to reference a nested node. For example, given that a `product` has/belongs to one `provider`, to use the provider's code as a value, the `valueField` property can be set to`{ provider: { code: true }}`. Note that autocompletion on this object will be provided as long as you provide a node type as a second parameter to the reference field's decorator (e.g. `@ui.decorators.referenceField<ReferenceField, ShowCaseProduct>`).
-   **imageField**: The GraphQL node property that will be used as the image field value, it must be a binary stream GraphQL object.
-   **isAutoSelectEnabled**: Auto-select item if search matches only one element. A success sound will play if there's a single match, or an error sound will play if there's no single match.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **minLookupCharacters**: Minimum number of characters to trigger a server lookup. If set to `0`, the first 20 suggestions will be fetched on focus. Defaults to 3.
-   **node**: The GraphQL node that the field suggestions will be fetched from.
-   **valueField**: The GraphQL node property that will be used as the reference field value. It can be either a **`string`** referring to an existing property of the given **`node`** (e.g. in the above example the product's code) or an `object` in order to be able to reference a nested node. For example, given that a `product` has/belongs to one `provider`, to use the provider's code as a value, the `valueField` property can be set to`{ provider: { code: true }}`. Note that autocompletion on this object will be provided as long as you provide a node type as a second parameter to the reference field's decorator (e.g. `@ui.decorators.referenceField<ReferenceField, ShowCaseProduct>`).
-   **shouldSuggestionsIncludeColumns**: When set to true, the lookup suggestion search is extended to the column definitions.
-   **additionalLookupRecords**: Records provided here are added to the lookup dialog and dropdown in addition to the server provided records.
-   **isFilterLimitedToDataset**: When using reference in a grid, the filter will only show options available in the dataset.
-   **tunnelPage**: Path reference to an other page. The user can use this page to view the details or modify the selected record. When no value is selected, the user can create a new record using this page.
-   **createTunnelLinkText**: Button label for the create new button at the end of the dropdown list and the top of the lookup button.
-   **tunnelPageIdField**: The GraphQL node property that will be used as the _id parameter in the tunnel link. It can be either a **`string`** referring to an existing property of the given **`node`** (e.g. in the above example the product's code) or an `object` in order to be able to reference a nested node. For example, given that a `product` has/belongs to one `provider`, to use the provider's _id property, the `tunnelPageIdField` property can be set to`{ provider: { _id: true }}`.

### Event handler decorator properties

-   **onOpenLookupDialog**: Triggered when the reference's dialog is opened.
-   **onCloseLookupDialog**: Triggered when the reference's dialog is closed.
-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.
-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).
-   **onInputValueChange**: Triggered when the user stops typing into the input of the field. The execution is debounced by 150ms to cater for continuos typing.

### Validator decorator properties

-   **isMandatory**: Makes the field mandatory, empty values will raise an error message. It can also be defined as callback function that returns a boolean.
-   **validation**: Custom validation callback, the new value is provided in the argument. If the function returns a non-empty string (or promise resolving to a non-empty string), it will be used as a validation error message. If returns a falsy value, the field is considered to be valid.

### Other decorator properties

-   **fetchesDefaults**: When set to true and when the reference value changes, a request to the server for default values for the whole page will be requested. False by default.
-   **groupAggregationMethod**: Set the grouping method for computing a value for the group totals row on the main list. The value is only displayed if the main list is grouped. Available methods for this field type: `distinctCount`.

## Runtime functions

-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **focus()**: Moves the focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **openDialog()**: Open the lookup dialog of the reference field. It triggers the same action as the lookup icon click event.
-   **fetchSuggestions(searchText: string)**: Fetches suggestions based on the configuration of the reference field and the search text parameter. It returns a promise with the list of suggestions.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

## Adding columns in extension pages

Additional fields can be added to a reference in extension page artifacts using the `referenceFieldExtension` decorator. In this case, the `columns` decorator property accepts an `nestedFieldExtension` array. This set of nested fields comes with an additional decorator property which determines the position of the extension columns within the original reference. This property is called `insertBefore`.

### Positioning extension fields within the reference

The `insertBefore` decorator property determines the position of the extension field.
#### Not provided
If this decorator property is not provided, the extension field is added to the end of the columns.

#### Using the bind value
The position of the extension column can be set by providing the `bind` decorator property value of the base column that the field should be inserted before to the `insertBefore` extension column decorator property.

#### Base reference fields bound to the same property
When the base page contains reference field based columns that are bound to the same property, the position of the additional column can be set more precisely by including the `valueField` property value into the `insertBefore` extension property. This must be done by joining the two values by a double underscore in the following format: `bind__valueField`. For example if the field should be positioned before a reference field that is bound to `product` and the displayed value field is `description`, than the `insertBefore` extension property would be `product__description`.

### Example

```ts
import * as ui from '@sage/xtrem-ui';

...

export class ReferenceExtension extends ui.PageExtension<Reference, GraphApi> {

    ...

    @ui.decorators.referenceFieldOverride<ReferenceExtension, ShowCaseProduct>({
        title: 'Overridden Title',
        ...
        columns: [
            ui.nestedFieldExtensions.reference<ReferenceExtension, ShowCaseProduct, BiodegradabilityCategory>({
                bind: 'biodegradabilityCategory',
                valueField: 'description',
                node: '@sage/xtrem-show-case-bundle/BiodegradabilityCategory',
                // This column is inserted before the `product` bound reference column that has `name` valueField configuration
                insertBefore: 'product__name',
                columns: [
                    ui.nestedFields.text({ bind: 'description', title: 'Description' }),
                    ui.nestedFields.text({ bind: 'energyToProduce', title: 'Energy to produce' })
                ],
                title: 'Biodegradability category',
                isAutoSelectEnabled: true,
            }),
            // This column goes to the end.
            ui.nestedFieldExtensions.text<ReferenceExtension>({
                bind: 'someTransientFieldAtTheEnd',
                isTransient: true,
                title: 'Transient ext column',
            }),
            // This column goes to the end.
            ui.nestedFieldExtensions.text<ReferenceExtension>({
                bind: 'someTransientFieldAtTheVeryEnd',
                isTransient: true,
                title: 'Transient ext column 2',
            }),
            // This column is inserted before the `provider` bound base column.
            ui.nestedFieldExtensions.numeric<ReferenceExtension>({
                bind: 'someTransientFieldInTheMiddle',
                isTransient: true,
                title: 'Transient number',
                insertBefore: 'provider',
                scale: 5
            }),
        ]
    })
    field: ui.fields.Reference;
}
```
## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Reference).
