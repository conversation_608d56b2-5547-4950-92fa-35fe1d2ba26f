import * as React from 'react';
import { useDispatch } from 'react-redux';
import * as xtremRedux from '../../../redux';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { ReferenceComponentProps } from './reference-types';
import { getElementAccessStatusWithoutId } from '../../../utils/access-utils';
import type { ScreenBaseDefinition } from '../../../service/screen-base-definition';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

const ConnectedReferenceComponent = React.lazy(() => import('./reference-component'));

export function AsyncConnectedReferenceComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedReferenceComponent {...props} />
        </React.Suspense>
    );
}

const ReferenceComponent = React.lazy(() =>
    import('./reference-component').then(c => ({ default: c.ReferenceComponent })),
);

export function AsyncReferenceComponent(props: ReferenceComponentProps): React.ReactElement {
    const dispatch = useDispatch();
    const openTunnel = React.useCallback(
        async () =>
            dispatch(
                xtremRedux.actions.openTunnel({
                    screenId: props.screenId,
                    elementId: props.elementId,
                    fieldProperties: props.fieldProperties,
                    parentElementId: props.parentElementId,
                    recordContext: props.recordContext,
                    contextNode: props.contextNode,
                    value: props.value,
                }),
            ),
        [dispatch, props],
    );

    const screenDefinition = useDeepEqualSelector<xtremRedux.XtremAppState, ScreenBaseDefinition>(
        s => s.screenDefinitions[props.screenId],
    );

    const hasAccessToTunnelPage =
        !!props.fieldProperties.tunnelPage &&
        getElementAccessStatusWithoutId(screenDefinition.accessBindings, {
            node: String(props.fieldProperties.node),
            bind: '$read',
        }) === 'authorized';

    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <ReferenceComponent {...props} openTunnel={openTunnel} hasAccessToTunnelPage={hasAccessToTunnelPage} />
        </React.Suspense>
    );
}
