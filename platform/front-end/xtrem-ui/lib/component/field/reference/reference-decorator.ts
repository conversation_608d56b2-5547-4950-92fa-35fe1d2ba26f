/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import {
    addColumnsToProperties,
    addDisabledToProperties,
    addHelperTextFieldToProperties,
    addImageFieldToProperties,
    addNodeToProperties,
    addTunnelPageToProperties,
    addValueFieldToProperties,
} from '../../../utils/data-type-utils';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { ReferenceControlObject } from '../../control-objects';
import type { GridNestedFieldTypes } from '../../nested-fields';
import type { NestedExtensionField } from '../../nested-fields-extensions';
import type { NestedOverrideField } from '../../nested-fields-overrides';
import { FieldKey } from '../../types';
import type { BaseReferenceExtensionDecoratorProperties, ReferenceDecoratorProperties } from './reference-types';

export interface ReferenceExtensionDecoratorProperties<T extends ScreenExtension<T>, NodeType extends ClientNode = any>
    extends BaseReferenceExtensionDecoratorProperties<Extend<T>>,
        ChangeableOverrideDecoratorProperties<
            Omit<ReferenceDecoratorProperties<Extend<T>, NodeType>, 'isReferenceDialogOpen'>,
            Extend<T>
        > {
    /** The definitions of the nested fields used to represent the table rows */
    columns?: NestedExtensionField<T, GridNestedFieldTypes, NodeType>[];

    /** Allows overriding existing column properties in the base page's columns */
    columnOverrides?: NestedOverrideField<T, GridNestedFieldTypes, NodeType>[];
}

class ReferenceDecorator extends AbstractFieldDecorator<FieldKey.Reference> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = ReferenceControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<ReferenceDecoratorProperties> {
        const properties: Partial<ReferenceDecoratorProperties> = {};
        addNodeToProperties({ dataType, propertyDetails, properties });
        addColumnsToProperties(
            {
                dataType,
                propertyDetails,
                properties,
                dataTypes: this.dataTypes,
                nodeTypes: this.nodeTypes,
            },
            true,
        );
        addValueFieldToProperties({
            dataType,
            propertyDetails,
            properties,
        });
        addHelperTextFieldToProperties({
            dataType,
            propertyDetails,
            properties,
        });
        addImageFieldToProperties({
            dataType,
            propertyDetails,
            properties,
        });
        addTunnelPageToProperties({
            dataType,
            propertyDetails,
            properties,
        });
        addDisabledToProperties({
            propertyDetails,
            dataType,
            properties,
        });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [Reference]{@link ReferenceControlObject} field with the provided properties
 *
 * @param properties The properties that the [Reference]{@link ReferenceControlObject} field will be initialized with
 */
export function referenceField<T extends ScreenExtension<T>, NodeType extends ClientNode = any>(
    properties: Omit<ReferenceDecoratorProperties<Extend<T>, NodeType>, 'isReferenceDialogOpen'>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Reference, NodeType>(
        properties,
        ReferenceDecorator,
        FieldKey.Reference,
        true,
    );
}

export function referenceFieldOverride<T extends ScreenExtension<T>, NodeType extends ClientNode = any>(
    properties: ReferenceExtensionDecoratorProperties<T, NodeType>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Reference, NodeType>(properties);
}
