import type { ClientNode } from '@sage/xtrem-client';
import type { UseComboboxStateChangeTypes } from 'downshift';
import { useCombobox } from 'downshift';
import { debounce } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import uid from 'uid';
import * as xtremRedux from '../../../redux';
import { lookupDialog } from '../../../service/dialog-service';
import { fetchReferenceFieldSuggestions } from '../../../service/graphql-service';
import { ContextType } from '../../../types';
import type { LookupDialogContent } from '../../../types/dialogs';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { getElementAccessStatusWithoutId } from '../../../utils/access-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { CarbonLinkEvent } from '../../../utils/types';
import { text } from '../../nested-fields';
import type { CollectionItem } from '../../types';
import type { PortraitSize } from '../../ui/portrait-component';
import { Portrait } from '../../ui/portrait-component';
import type { SelectItem } from '../../ui/select/select-component';
import { Select } from '../../ui/select/select-component';
import { getCommonCarbonComponentProperties, getLabelTitle } from '../carbon-helpers';
import { CarbonWrapper } from '../carbon-wrapper';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { EditableFieldComponentProperties, FieldComponentExternalProperties } from '../field-base-component-types';
import type {
    ConnectedReferenceComponentProps,
    ReferenceComponentProps,
    ReferenceComponentState,
    ReferenceDecoratorProperties,
    ReferenceProperties,
} from './reference-types';
import {
    getReferenceHelperText,
    getReferenceSearchText,
    getReferenceSelectedRecord,
    getReferenceTunnelLinkId,
    hasLookupIcon,
    nodesToSelectItems,
} from './reference-utils';

export class ReferenceComponent<T extends ClientNode = any> extends EditableFieldBaseComponent<
    ReferenceDecoratorProperties<any, T>,
    T,
    ConnectedReferenceComponentProps<T>,
    ReferenceComponentState
> {
    private readonly lookupButtonRef: React.RefObject<HTMLButtonElement> = React.createRef();

    private readonly selectInputRef: React.RefObject<HTMLInputElement>;

    private searchText: string;

    constructor(props: ConnectedReferenceComponentProps<T>) {
        super(props);
        const { value, fieldProperties } = props;
        const searchText = getReferenceSearchText<T>({ value, fieldProperties });
        this.state = {
            id: uid(16),
            isLookupDialogOpen: false,
        };

        this.searchText = searchText;
        this.selectInputRef = React.createRef();
    }

    private readonly onInputValueChanged = debounce(
        async (searchText: string) => {
            if (!this.props.parentElementId) {
                await triggerFieldEvent(this.props.screenId, this.props.elementId, 'onInputValueChange', searchText);
            }
        },
        150,
        { leading: false, trailing: true },
    );

    componentDidMount(): void {
        // On mobile if there are no columns by default we add "valueField" & "helperTextField"
        if (
            !this.props.fieldProperties.columns &&
            !this.isReadOnly() &&
            !(this.props.browser?.greaterThan.s ?? true) &&
            this.props.setFieldProperties
        ) {
            this.props.setFieldProperties(this.props.elementId, {
                ...this.props.fieldProperties,
                columns: [this.props.fieldProperties.valueField, this.props.fieldProperties.helperTextField]
                    .filter(c => !!c)
                    .map(c => {
                        // Hacky as query generation works with object as well as strings
                        const bind = typeof c === 'object' ? c : String(c);
                        return text({ bind: bind as any });
                    }),
            });
        }
    }

    getIsReferenceDialogOpen = (): boolean => {
        return Boolean(
            (
                xtremRedux.getStore().getState().screenDefinitions[this.props.screenId].metadata.uiComponentProperties[
                    this.props.elementId
                ] as ReferenceProperties
            )?.isReferenceDialogOpen,
        );
    };

    onChange = (collectionItem: CollectionItem | undefined, isOrganicChange: boolean): void => {
        if (isOrganicChange) {
            handleChange(
                this.props.elementId,
                collectionItem ?? null,
                this.props.setFieldValue,
                this.props.validate,
                this.triggerChangeListener,
            );
        }
    };

    onMobileButtonClick = (event: React.MouseEvent<HTMLElement>): void => {
        event.stopPropagation();
        event.preventDefault();
        if (this.selectInputRef.current) {
            this.searchText = this.selectInputRef.current.value ?? '';
            this.openLookupDialog();
        }
    };

    onButtonClick = (event: CarbonLinkEvent): void => {
        event.stopPropagation();
        event.preventDefault();
        this.openLookupDialog();
    };

    getLookupContent = (): LookupDialogContent => {
        return {
            onLookupDialogClose: async (): Promise<void> => {
                if (this.props.setFieldProperties) {
                    this.props.setFieldProperties(this.props.elementId, {
                        ...this.props.fieldProperties,
                        isReferenceDialogOpen: false,
                    });
                }
                this.lookupButtonRef.current?.focus();
                if (this.componentRef.current) {
                    const input = this.getFocusableElement(this.componentRef.current);
                    if (
                        input &&
                        document.activeElement !== input &&
                        (this.lookupButtonRef.current === null ||
                            document.activeElement !== this.lookupButtonRef.current)
                    ) {
                        input.focus();
                    }
                }
                this.setState({ isLookupDialogOpen: false });
            },
            contextNode: this.props.contextNode,
            createTunnelLinkText: this.props.fieldProperties.createTunnelLinkText,
            fieldId: this.props.elementId,
            fieldProperties: this.props.fieldProperties,
            isLinkCreateNewText: !!this.props.fieldProperties.tunnelPage,
            isMultiSelect: false,
            level: this.props.level,
            onCreateNewItemLinkClick: this.handleOnCreateNewItemLinkClick,
            recordContext: this.props.recordContext,
            searchText: (this.props.browser?.greaterThan.s ?? true) ? undefined : this.searchText,
            selectedRecordId: this.props.value?._id,
            value: this.props.value,
            valueField: this.props.fieldProperties.valueField,
            parentElementId: this.props.parentElementId,
        };
    };

    openLookupDialog = async (): Promise<void> => {
        try {
            const [selectedRecord] = await lookupDialog(this.props.screenId, 'info', this.getLookupContent());
            this.onChange(selectedRecord, true);
            this.lookupButtonRef.current?.focus();
        } catch {
            /* intentionally left empty */
        }
    };

    hasLookupIcon = (): boolean =>
        hasLookupIcon({
            screenId: this.props.screenId,
            fieldProperties: this.props.fieldProperties,
            value: this.props.value,
            rowValue: this.props.handlersArguments?.rowValue
                ? splitValueToMergedValue(this.props.handlersArguments?.rowValue)
                : this.props.handlersArguments?.rowValue,
        });

    isLookupDialogOpen = (): boolean =>
        (!!this.getIsReferenceDialogOpen() || !!this.state.isLookupDialogOpen) && !!this.props.fieldProperties.columns;

    isLookupDialogOpenMobile = (): boolean =>
        (!!this.getIsReferenceDialogOpen() || !!this.state.isLookupDialogOpen) &&
        !this.isReadOnly() &&
        !this.isDisabled();

    isReadOnlyAndHasPicture = (): boolean =>
        !!this.props.fieldProperties.imageField && (this.isReadOnly() || this.isDisabled());

    render(): React.ReactNode {
        let className = 'e-reference-field';
        if (this.hasLookupIcon()) {
            className += ' e-reference-field-lookup';
        }
        if (this.isReadOnlyAndHasPicture()) {
            className += ' e-reference-inline-picture';
        }
        if (!this.isReadOnly() && !(this.props.browser?.greaterThan.s ?? true)) {
            return this.renderMobile(className);
        }
        return this.renderDesktop(className);
    }

    handleOnCreateNewItemLinkClick = (): void => {
        this.onOpenTunnel(true);
    };

    renderMobile = (className: string): React.ReactNode => {
        const carbonProps = getCommonCarbonComponentProperties(this.props);
        const { value, fieldProperties } = this.props;
        const helperText = getReferenceHelperText({ value, fieldProperties });
        const wrapperStyle =
            helperText && !this.props.fieldProperties.isHelperTextHidden
                ? { alignItems: 'center' }
                : { alignItems: 'flex-end' };
        const searchText = getReferenceSearchText({
            value,
            fieldProperties,
        });
        const initialSelectedItem = getReferenceSelectedRecord({
            value,
            fieldProperties,
        });
        return (
            <CarbonWrapper
                {...this.props}
                className={className}
                componentName="reference"
                componentRef={this.componentRef}
                handlersArguments={this.props.handlersArguments}
                helperText={helperText}
                noReadOnlySupport={true}
                readOnlyDisplayValue={searchText}
            >
                <div className="e-reference-field" style={wrapperStyle}>
                    <Select
                        {...carbonProps}
                        autoSelect={this.props.fieldProperties.isAutoSelectEnabled}
                        autoSelectPolicy="always"
                        disabled={this.isDisabled()}
                        error={this.props.validationErrors?.[0]?.message}
                        fullWidth={this.props.fieldProperties.isFullWidth}
                        getItems={this.getItems}
                        hasLookupIcon={true}
                        hasHighlightMatchText={true}
                        helperText={helperText}
                        helperTextLink={this.getHelperTextLink()}
                        isLinkHelperText={!!this.props.fieldProperties.tunnelPage}
                        icon={this.props.fieldProperties.icon}
                        onHelperTextLinkClick={this.onOpenTunnel}
                        initialInputValue={searchText}
                        inputId={carbonProps.id}
                        hasClearFieldButton={true}
                        isSortedAlphabetically={true}
                        isDropdownDisabled={true}
                        isHelperTextHidden={this.props.fieldProperties.isHelperTextHidden}
                        label={getLabelTitle(
                            this.props.screenId,
                            this.props.fieldProperties,
                            this.props.handlersArguments?.rowValue,
                        )}
                        lookupButtonRef={this.lookupButtonRef}
                        lookupIconId={`e-reference-field-lookup-icon-${this.state.id}`}
                        closeIconClassName="e-ui-select-close-icon"
                        minLookupCharacters={this.getMinLookupCharactersForMobile()}
                        onChange={this.onChange}
                        onInputChange={this.onInputChange}
                        onInputFocus={this.props.onFocus}
                        onLookupIconClick={this.onMobileButtonClick}
                        onItemsFetched={this.onItemsFetched}
                        placeholder={this.props.fieldProperties.placeholder}
                        preventSelectionOnBlur={
                            Boolean(this.getIsReferenceDialogOpen()) || Boolean(this.state.isLookupDialogOpen)
                        }
                        readOnly={this.isReadOnly()}
                        ref={this.selectInputRef}
                        selectedItem={initialSelectedItem}
                        testId={`e-reference-field-lookup-search-text-${this.state.id}`}
                        screenId={this.props.screenId}
                        elementId={this.props.elementId}
                        shouldFilterItems={false}
                        isSoundDisabled={resolveByValue({
                            screenId: this.props.screenId,
                            propertyValue: this.props.fieldProperties.isSoundDisabled,
                            skipHexFormat: true,
                            fieldValue: null,
                            rowValue: null,
                        })}
                        createTunnelLinkText={this.props.fieldProperties.createTunnelLinkText}
                        onCreateNewItemLinkClick={this.handleOnCreateNewItemLinkClick}
                        node={this.props.fieldProperties.node}
                        isLinkCreateNewText={!!this.props.fieldProperties.tunnelPage}
                        refetchIfChanged={this.props.fieldProperties.filter}
                    />
                </div>
            </CarbonWrapper>
        );
    };

    onOpenTunnel = async (isNew = false): Promise<void> => {
        const newValue = await this.props.openTunnel(this.props.fieldProperties, isNew ? null : this.props.value);
        // Only update the value if the tunnel results some changes. If null returned, no changes were done.
        if (newValue) {
            this.onChange(newValue, true);
        }
    };

    getHelperTextLink = (): string | undefined => {
        if (!this.props.fieldProperties.tunnelPage || !this.props.value) {
            return undefined;
        }

        const _id = getReferenceTunnelLinkId({ fieldProperties: this.props.fieldProperties, value: this.props.value });
        if (!_id) {
            return undefined;
        }

        return `${this.props.fieldProperties.tunnelPage}/${btoa(JSON.stringify({ _id }))}`;
    };

    onInputChange = async (searchText: string, type?: UseComboboxStateChangeTypes): Promise<void> => {
        if (type !== useCombobox.stateChangeTypes.FunctionSelectItem) {
            this.onInputValueChanged.cancel();
            await this.onInputValueChanged(searchText);
        }
    };

    getPortraitSize = (): PortraitSize => {
        if (this.props.contextType === ContextType.table) {
            return 'M';
        }
        if (this.props.fieldProperties.helperTextField || this.props.fieldProperties.helperText) {
            return 'XL';
        }
        return 'L';
    };

    onItemsFetched: React.ComponentProps<typeof Select>['onItemsFetched'] = args => {
        if (args.state === 'autoselect' || args.state === 'error' || this.isLookupDialogOpenMobile()) {
            return;
        }
        this.setState(
            {
                isLookupDialogOpen: true,
            },
            () => {
                this.searchText = this.selectInputRef.current?.value ?? '';
                this.openLookupDialog();
            },
        );
    };

    getItems = async (filterValue: string): Promise<SelectItem[]> => {
        const nodes = await fetchReferenceFieldSuggestions<T>({
            fieldProperties: this.props.fieldProperties,
            screenId: this.props.screenId,
            fieldId: this.props.elementId,
            filterValue,
            parentElementId: this.props.parentElementId,
            recordContext: this.props.handlersArguments?.rowValue,
            contextNode: this.props.contextNode,
            level: this.props.level,
        });

        return nodesToSelectItems<T>({
            nodes,
            fieldProperties: this.props.fieldProperties,
        });
    };

    getMinLookupCharacters = (): number => this.props.fieldProperties.minLookupCharacters ?? 3;

    getMinLookupCharactersForMobile = (): number => Math.max(this.props.fieldProperties.minLookupCharacters ?? 1, 1);

    renderDesktop = (className: string): React.ReactNode => {
        const { value, fieldProperties } = this.props;
        const searchText = getReferenceSearchText({
            value,
            fieldProperties,
        });
        const selectedRecord = getReferenceSelectedRecord({
            value,
            fieldProperties,
        });
        const helperText = getReferenceHelperText({ value, fieldProperties });
        const carbonProps = getCommonCarbonComponentProperties(this.props);

        return (
            <CarbonWrapper
                {...this.props}
                className={className}
                componentName="reference"
                componentRef={this.componentRef}
                helperText={this.props.fieldProperties.helperText}
                noReadOnlySupport={true}
                readOnlyDisplayValue={searchText}
            >
                {this.props.fieldProperties.imageField &&
                    selectedRecord &&
                    (this.isReadOnly() || this.isDisabled()) && (
                        <Portrait
                            image={selectedRecord.image}
                            placeholderValue={String(selectedRecord.value)}
                            size={this.getPortraitSize()}
                        />
                    )}

                <div onClick={this.getClickHandler()} className="e-reference-field-body">
                    <Select
                        autoSelect={this.props.fieldProperties.isAutoSelectEnabled}
                        disabled={this.isDisabled()}
                        error={this.props.validationErrors?.[0]?.message}
                        warning={carbonProps.warning}
                        info={carbonProps.info}
                        fullWidth={this.props.fieldProperties.isFullWidth}
                        getItems={this.getItems}
                        hasLookupIcon={this.hasLookupIcon()}
                        hasHighlightMatchText={true}
                        helperText={helperText}
                        helperTextLink={this.getHelperTextLink()}
                        icon={this.props.fieldProperties.icon}
                        initialInputValue={searchText}
                        inputId={carbonProps.id}
                        onInputChange={this.onInputChange}
                        onHelperTextLinkClick={this.onOpenTunnel}
                        isSortedAlphabetically={true}
                        isDropdownDisabled={this.props.fieldProperties.isDropdownDisabled}
                        isHelperTextHidden={this.props.fieldProperties.isHelperTextHidden}
                        isLinkHelperText={!!this.props.hasAccessToTunnelPage}
                        hasClearFieldButton={true}
                        label={
                            !this.props.fieldProperties.isTitleHidden
                                ? getLabelTitle(
                                      this.props.screenId,
                                      this.props.fieldProperties,
                                      this.props.handlersArguments?.rowValue,
                                  )
                                : undefined
                        }
                        lookupIconId={`e-reference-field-lookup-icon-${this.state.id}`}
                        closeIconClassName="e-ui-select-close-icon"
                        lookupButtonRef={this.lookupButtonRef}
                        minLookupCharacters={this.getMinLookupCharacters()}
                        onChange={this.onChange}
                        onInputFocus={this.props.onFocus}
                        onLookupIconClick={this.onButtonClick}
                        placeholder={this.props.fieldProperties.placeholder}
                        readOnly={!this.isDisabled() && this.isReadOnly()}
                        selectedItem={selectedRecord}
                        size={this.props.fieldProperties.size}
                        testId={`e-reference-field-lookup-input-${this.state.id}`}
                        screenId={this.props.screenId}
                        elementId={this.props.elementId}
                        shouldFilterItems={false}
                        isSoundDisabled={resolveByValue({
                            screenId: this.props.screenId,
                            propertyValue: this.props.fieldProperties.isSoundDisabled,
                            skipHexFormat: true,
                            fieldValue: null,
                            rowValue: null,
                        })}
                        createTunnelLinkText={this.props.fieldProperties.createTunnelLinkText}
                        onCreateNewItemLinkClick={this.handleOnCreateNewItemLinkClick}
                        isLinkCreateNewText={!!this.props.hasAccessToTunnelPage}
                        node={this.props.fieldProperties.node}
                        refetchIfChanged={this.props.fieldProperties.filter}
                    />
                </div>
            </CarbonWrapper>
        );
    };

    isRelatedTargetEqualTo = (e: React.FocusEvent<HTMLInputElement>, input: string): boolean =>
        (e.relatedTarget as any)?.id === `${input}-${this.state.id}`;
}

const extendedMapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: FieldComponentExternalProperties,
): ConnectedReferenceComponentProps => {
    const componentProperties = mapStateToProps()(state, props) as EditableFieldComponentProperties<
        ReferenceDecoratorProperties,
        any
    >;
    const screenDefinition = state.screenDefinitions[props.screenId];
    const hasAccessToTunnelPage =
        !!componentProperties.fieldProperties.tunnelPage &&
        getElementAccessStatusWithoutId(screenDefinition.accessBindings, {
            node: String(componentProperties.fieldProperties.node),
            bind: '$read',
        }) === 'authorized';

    return {
        ...props,
        ...componentProperties,
        hasAccessToTunnelPage,
        openTunnel: xtremRedux.actions.actionStub,
    };
};

const extendedMapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: ReferenceComponentProps,
): Partial<ConnectedReferenceComponentProps> => {
    const defaultMapDispatchToProps = mapDispatchToProps<ReferenceDecoratorProperties>()(dispatch, props);
    return {
        ...defaultMapDispatchToProps,
        openTunnel: (fieldProperties: ReferenceDecoratorProperties, value: any): Promise<any> =>
            dispatch(
                xtremRedux.actions.openTunnel({
                    elementId: props.elementId,
                    parentElementId: props.parentElementId,
                    screenId: props.screenId,
                    recordContext: props.recordContext,
                    contextNode: props.contextNode,
                    fieldProperties,
                    value,
                }),
            ),
    };
};

export const ConnectedReferenceComponent = connect(
    extendedMapStateToProps,
    extendedMapDispatchToProps,
)(ReferenceComponent);

export default ConnectedReferenceComponent;
