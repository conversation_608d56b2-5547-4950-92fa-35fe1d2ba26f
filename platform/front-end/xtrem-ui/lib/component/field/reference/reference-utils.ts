import type { ClientNode } from '@sage/xtrem-client';
import { objectKeys } from '@sage/xtrem-shared';
import { get, set } from 'lodash';
import { fetchReferenceFieldSuggestions } from '../../../service/graphql-service';
import type { ContextType } from '../../../types';
import type { AgGridColumnConfigWithScreenIdAndColDef } from '../../../utils/ag-grid/ag-grid-utility-types';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { CollectionItem, OrderByType } from '../../types';
import { FieldKey } from '../../types';
import type { SelectItem } from '../../ui/select/select-component';
import { isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import type {
    NestedReferenceProperties,
    PropertyValueType,
    ReferenceDecoratorProperties,
    ReferenceProperties,
} from './reference-types';

type FieldProps<T extends ClientNode = any> = Pick<
    ReferenceDecoratorProperties<any, T>,
    | 'columns'
    | 'helperText'
    | 'helperTextField'
    | 'imageField'
    | 'isDisabled'
    | 'isReadOnly'
    | 'isHelperTextHidden'
    | 'tunnelPage'
    | 'tunnelPageIdField'
    | 'valueField'
>;

export const fetchReferenceItems = async (
    filterValue: string,
    columnDefinition: AgGridColumnConfigWithScreenIdAndColDef,
    after?: string,
): Promise<SelectItem[]> => {
    const valueField = columnDefinition.cellRendererParams.fieldProperties.valueField;
    const isFilterLimitedToDataset = columnDefinition.cellRendererParams.fieldProperties.isFilterLimitedToDataset;
    const nodes = await fetchReferenceFieldSuggestions({
        after,
        contextNode: columnDefinition.cellRendererParams.contextNode,
        fieldProperties: {
            ...columnDefinition.cellRendererParams.fieldProperties,
            isTransient: true,
            columns: undefined,
            helperTextField: undefined,
            minLookupCharacters: 0,
            orderBy: set({}, convertDeepBindToPathNotNull(valueField), 1),
        },
        fieldId: columnDefinition.cellRendererParams.columnId,
        filterValue: filterValue || undefined,
        isFilterLimitedToDataset,
        parentElementId: columnDefinition.cellRendererParams.tableElementId,
        recordContext: {},
        screenId: columnDefinition.context.screenId,
    });

    return nodesToSelectItems({
        nodes,
        fieldProperties: columnDefinition.cellRendererParams.fieldProperties,
    });
};

export function getReferenceImage<T extends ClientNode = any>({
    value,
    fieldProperties: { imageField },
}: {
    value?: T;
    fieldProperties: FieldProps<T>;
}): SelectItem['image'] {
    if (!value || !imageField) {
        return undefined;
    }
    return get(value, String(imageField), undefined) as SelectItem['image'] | undefined;
}

export function getReferenceSearchText<T extends ClientNode = any>({
    value,
    fieldProperties: { valueField },
}: {
    value?: T;
    fieldProperties: FieldProps<T>;
}): string {
    if (!value || !valueField || !get(value, getReferencePath(valueField), '')) {
        return '';
    }
    return String(get(value, getReferencePath(valueField), ''));
}

export function getReferenceHelperText<T extends ClientNode = any>({
    value,
    fieldProperties: { helperTextField, helperText },
}: {
    value?: T;
    fieldProperties: FieldProps<T>;
}): string | undefined {
    if (!value || !helperTextField) {
        return helperText;
    }
    return get(value, getReferencePath(helperTextField), helperText) as string | undefined;
}

export function getReferenceTunnelLinkId<T extends ClientNode = any>({
    fieldProperties,
    value,
}: {
    fieldProperties: FieldProps<T>;
    value?: T;
}): string | undefined {
    if (!value) {
        return undefined;
    }

    if (fieldProperties.tunnelPageIdField) {
        const fieldPath = convertDeepBindToPathNotNull(fieldProperties.tunnelPageIdField);
        const _id = get(value, fieldPath);
        if (_id) {
            return _id;
        }
        return undefined;
    }

    return value._id;
}

export function isReferenceDisabled<T extends ClientNode = any>({
    screenId,
    fieldProperties,
    value,
    rowValue,
}: {
    screenId: string;
    fieldProperties: Pick<FieldProps<T>, 'isDisabled'>;
    value?: T;
    rowValue?: any;
}): boolean {
    return isFieldDisabled(screenId, fieldProperties, value, rowValue ? splitValueToMergedValue(rowValue) : rowValue);
}

export function isReferenceReadOnly<T extends ClientNode = any>({
    screenId,
    fieldProperties,
    value,
    rowValue,
    contextType,
}: {
    screenId: string;
    fieldProperties: Pick<FieldProps<T>, 'isReadOnly'>;
    value?: T;
    rowValue?: any;
    contextType?: ContextType;
}): boolean {
    return isFieldReadOnly(
        screenId,
        fieldProperties,
        value,
        rowValue ? splitValueToMergedValue(rowValue) : rowValue,
        contextType,
    );
}

export function hasHelperTextField<T extends ClientNode = any>({
    screenId,
    fieldProperties,
    value,
    rowValue,
    contextType,
}: {
    screenId: string;
    fieldProperties: FieldProps<T>;
    value?: T;
    rowValue?: any;
    contextType?: ContextType;
}): boolean {
    return Boolean(
        !isReferenceReadOnly({
            screenId,
            fieldProperties,
            value,
            rowValue: rowValue ? splitValueToMergedValue(rowValue) : rowValue,
            contextType,
        }) &&
            !isReferenceDisabled({ screenId, fieldProperties, value, rowValue }) &&
            !fieldProperties.isHelperTextHidden,
    );
}

export function hasLookupIcon<T extends ClientNode = any>({
    screenId,
    fieldProperties,
    value,
    rowValue,
    contextType,
}: {
    screenId: string;
    fieldProperties: FieldProps<T>;
    value?: T | T[];
    rowValue: any;
    contextType?: ContextType;
}): boolean {
    return Boolean(
        !isReferenceReadOnly<T>({
            screenId,
            fieldProperties,
            value: value as any,
            rowValue: rowValue ? splitValueToMergedValue(rowValue) : rowValue,
            contextType,
        }) &&
            !isReferenceDisabled<T>({ screenId, fieldProperties, value: value as any, rowValue }) &&
            fieldProperties.columns &&
            fieldProperties.columns.length > 0,
    );
}

export function getReferenceSelectedRecord<T extends ClientNode = any>({
    value,
    fieldProperties,
}: {
    value?: T;
    fieldProperties: FieldProps<T>;
}): SelectItem | undefined {
    const searchText = getReferenceSearchText<T>({ value, fieldProperties });
    const cleanValue = value ? splitValueToMergedValue(value) : value;
    return searchText
        ? {
              value: searchText,
              id: value!._id ?? (value! as any).id,
              image: getReferenceImage<T>({ value: cleanValue, fieldProperties }),
              helperText: getReferenceHelperText<T>({ value: cleanValue, fieldProperties }),
              displayedAs: searchText,
              __collectionItem: cleanValue,
          }
        : undefined;
}

export function nodeToSelectItem<T extends ClientNode = any>({
    collectionItem,
    fieldProperties: { valueField, helperTextField, imageField },
}: {
    fieldProperties: FieldProps<T>;
    collectionItem: CollectionItem;
}): SelectItem {
    const value = get(collectionItem, convertDeepBindToPathNotNull(valueField as PropertyValueType));
    const helperText = helperTextField
        ? get(collectionItem, convertDeepBindToPathNotNull(helperTextField as PropertyValueType))
        : null;
    const displayedAs = value;
    return {
        id: collectionItem._id ?? collectionItem.id,
        displayedAs,
        helperText,
        image: imageField
            ? get(collectionItem, convertDeepBindToPathNotNull(imageField as PropertyValueType))
            : undefined,
        value,
        __collectionItem: collectionItem,
    };
}

export function nodesToSelectItems<T extends ClientNode = any>({
    nodes,
    fieldProperties,
}: {
    nodes?: CollectionItem[];
    fieldProperties: FieldProps<T>;
}): SelectItem[] {
    if (!nodes) {
        return [];
    }
    return nodes.map(collectionItem => nodeToSelectItem({ collectionItem, fieldProperties }));
}

export const getReferenceValueField = <T extends ClientNode = any>(
    fieldProperties: Omit<ReferenceProperties<any, T>, 'onChange'>,
): string => {
    return getReferencePath<T>(fieldProperties.valueField);
};
export const getReferenceValueFieldPath = <T extends ClientNode = any>(
    fieldProperties: Omit<ReferenceProperties<any, T>, 'onChange'>,
): string => {
    const valueFieldPath = getReferencePath<T>(fieldProperties.valueField);
    if (valueFieldPath) {
        return `${convertDeepBindToPathNotNull(fieldProperties.bind)}.${valueFieldPath}`;
    }
    return convertDeepBindToPathNotNull(fieldProperties.bind);
};

export const getReferenceOrderBy = <T extends ClientNode = any>(
    fieldProperties: ReferenceProperties<any, T>,
): OrderByType<T> => {
    if (fieldProperties.orderBy) {
        const orderBy = Object.prototype.hasOwnProperty.call(fieldProperties.orderBy, '_id')
            ? fieldProperties.orderBy
            : { ...fieldProperties.orderBy, _id: 1 };

        return orderBy as OrderByType<T>;
    }

    const firstColumn = fieldProperties.columns?.filter(c => c.type !== FieldKey.Image)?.[0];
    if (firstColumn) {
        const bind =
            firstColumn.type === FieldKey.Reference
                ? `${convertDeepBindToPathNotNull(
                      firstColumn.properties.bind as PropertyValueType,
                  )}.${getReferencePath<any>(
                      (firstColumn.properties as NestedReferenceProperties<any, any>).valueField,
                  )}`
                : convertDeepBindToPathNotNull(firstColumn.properties.bind as PropertyValueType);
        const orderBy = set<any>({}, convertDeepBindToPathNotNull(bind), 1);
        if (bind !== '_id') {
            orderBy._id = 1;
        }

        return orderBy as OrderByType<T>;
    }

    if (fieldProperties.valueField) {
        const orderBy = set<any>({}, convertDeepBindToPathNotNull(fieldProperties.valueField as PropertyValueType), 1);
        if (fieldProperties.valueField !== '_id') {
            orderBy._id = 1;
        }

        return orderBy as OrderByType<T>;
    }

    return { _id: 1 } as OrderByType<T>;
};

export function getReferencePath<T extends ClientNode = any>(value: ReferenceProperties<any, T>['valueField']): string;
export function getReferencePath<T extends ClientNode = any>(
    value: ReferenceProperties<any, T>['helperTextField'],
): string;
export function getReferencePath<T extends ClientNode = any>(
    value: ReferenceProperties<any, T>['valueField'] | ReferenceProperties<any, T>['helperTextField'],
): string | undefined {
    if (!value) {
        return undefined;
    }
    if (typeof value === 'string' || typeof value === 'number') {
        return String(value);
    }
    let path = '';
    const getPath = (obj: any): void => {
        if (typeof obj !== 'object') {
            return;
        }
        objectKeys(obj).forEach(key => {
            path += `${key}.`;
            getPath(obj[key]);
        });
    };
    getPath(value);
    return path.slice(0, -1);
}

export const isStringReferenceValueField = (valueField: ReferenceProperties<any, any>['valueField']): boolean =>
    typeof valueField === 'string';
