import { get, isEqual } from 'lodash';
import React from 'react';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import type { CarbonLinkEvent } from '../../../utils/types';
import type { NestedReferenceProperties, ReferenceDecoratorProperties } from '../../decorators';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { EditableFieldComponentProperties } from '../field-base-component-types';
import { AUTO_COLUMN_ID } from '../../../utils/table-component-utils';
import { getReferenceValueFieldPath } from './reference-utils';
import { getImageUrlFromValue } from '../image/image-utils';
import Link from 'carbon-react/esm/components/link';
import { splitValueToMergedValue } from '../../../utils/transformers';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import * as xtremRedux from '../../../redux';
import { connect } from 'react-redux';
import type { ReferenceCellRendererExternalProps, ReferenceCellRendererProps } from './reference-types';
import type { ImageValue } from '../image/image-types';
import { getElementAccessStatusWithoutId } from '../../../utils/access-utils';
import { TUNNEL_LINK_CLASS } from '../../../utils/constants';

export class ReferenceCellRenderer extends React.Component<ReferenceCellRendererProps> {
    private readonly linkRef = React.createRef<HTMLButtonElement | HTMLAnchorElement>();

    componentDidMount(): void {
        if (this.props.eGridCell && this.shouldRenderTunnelPageLink()) {
            this.props.eGridCell.addEventListener('focus', this.onFocus);
        }
    }

    shouldComponentUpdate(nextProps: ReferenceCellRendererProps): boolean {
        const isMandatory = (this.props.fieldProperties as EditableFieldProperties).isMandatory ?? false;
        const validationErrors =
            (this.props as unknown as EditableFieldComponentProperties<NestedReferenceProperties, any>)
                .validationErrors ?? false;

        if (!isEqual(this.props.value, nextProps.value)) {
            return true;
        }

        // Trigger updates when validation is updated:
        if (isMandatory && !this.props.value) {
            return true;
        }
        if (
            !isEqual(
                validationErrors,
                (nextProps as unknown as EditableFieldComponentProperties<NestedReferenceProperties, any>)
                    .validationErrors,
            )
        ) {
            return true;
        }

        return false;
    }

    componentWillUnmount(): void {
        if (this.props.eGridCell && this.shouldRenderTunnelPageLink()) {
            this.props.eGridCell.removeEventListener('focus', this.onFocus);
        }
    }

    private readonly onFocus = (): void => {
        if (this.shouldRenderTunnelPageLink()) {
            this.linkRef.current?.focus();
        }
    };

    /**
     * Searches for the reference field image from the row using the the dot path, first using the bind path of the reference column, then the path of the `imageField`;
     * @returns
     */
    getImageFromProps = (): ImageValue | undefined => {
        if (!this.props.fieldProperties.imageField) {
            return undefined;
        }
        return (
            get(
                this.props.data,
                `${this.props.colDef.field}.${convertDeepBindToPathNotNull(this.props.fieldProperties.imageField)}`,
            ) || null
        );
    };

    private getTunnelPageLink(): string {
        const referencedObjectId = get(
            this.props.data,
            `${convertDeepBindToPathNotNull(this.props.fieldProperties.bind)}._id`,
        );
        return `${this.props.fieldProperties.tunnelPage}/${btoa(JSON.stringify({ _id: referencedObjectId }))}`;
    }

    private readonly shouldRenderTunnelPageLink = (): boolean =>
        (this.props.isTableReadOnly || this.isReadOnly()) && !!this.props.hasAccessToTunnelPage;

    private readonly isReadOnly = (): boolean =>
        resolveByValue<boolean>({
            screenId: this.props.screenId,
            propertyValue: this.props.fieldProperties.isReadOnly,
            rowValue: splitValueToMergedValue(this.props.data),
            fieldValue: this.props.value,
            skipHexFormat: true,
        });

    onTunnelLinkClick = async (ev: CarbonLinkEvent): Promise<void> => {
        if (ev.ctrlKey || ev.metaKey) {
            return;
        }
        ev.preventDefault();

        const referenceValue =
            get(this.props.data, convertDeepBindToPathNotNull(this.props.fieldProperties.bind)) || null;
        const newValue = await this.props.openTunnel(
            this.props.fieldProperties as ReferenceDecoratorProperties,
            referenceValue,
        );

        // Only update the value if the tunnel results some changes. If null returned, no changes were done.
        if (newValue && this.props.setValue) {
            this.props.setValue(newValue);
        }
    };

    render(): React.ReactNode {
        if (
            !this.props.data ||
            (!this.props.isTree && !this.props.data.__isGroup && this.props.colDef.colId === AUTO_COLUMN_ID)
        ) {
            return null;
        }

        // If group append children count
        const displayValue = this.props.valueFormatted ?? '';

        const image = this.getImageFromProps();

        if (this.props.node?.footer) {
            return (
                <this.props.fieldProperties.wrapper {...this.props}>
                    <strong>
                        {get(this.props.data, `${convertDeepBindToPathNotNull(this.props.fieldProperties.bind)}._id`)}
                    </strong>
                </this.props.fieldProperties.wrapper>
            );
        }

        const shouldHideValue =
            !!this.props.data.__isGroup &&
            this.props.colDef.colId !== AUTO_COLUMN_ID &&
            this.props.data.__groupKey === getReferenceValueFieldPath(this.props.fieldProperties);

        return (
            <this.props.fieldProperties.wrapper {...this.props}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    {image && (
                        <img
                            src={getImageUrlFromValue(image.value)}
                            style={{ width: '24px', height: '24px', paddingRight: '10px' }}
                            alt={displayValue}
                        />
                    )}
                    {this.shouldRenderTunnelPageLink() && !shouldHideValue && (
                        <div data-testid={`${this.props.tableElementId}-${this.props.node.rowIndex}`}>
                            <Link
                                onClick={this.onTunnelLinkClick}
                                variant="neutral"
                                href={this.getTunnelPageLink()}
                                ref={this.linkRef}
                                className={TUNNEL_LINK_CLASS}
                            >
                                {displayValue}
                            </Link>
                        </div>
                    )}
                    {!this.shouldRenderTunnelPageLink() && (
                        <span
                            data-testid={`${this.props.tableElementId}-${this.props.node.rowIndex}-${
                                this.props.api.getColumns()!.indexOf(this.props.column!) + 1
                            }`}
                            style={{ textOverflow: 'ellipsis', overflow: 'hidden' }}
                        >
                            {shouldHideValue ? '' : displayValue}
                        </span>
                    )}
                </div>
            </this.props.fieldProperties.wrapper>
        );
    }
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: ReferenceCellRendererExternalProps,
): ReferenceCellRendererProps => {
    const screenDefinition = state.screenDefinitions[props.screenId];
    const hasAccessToTunnelPage =
        !!props.fieldProperties.tunnelPage &&
        getElementAccessStatusWithoutId(screenDefinition.accessBindings, {
            node: String(props.fieldProperties.node),
            bind: '$read',
        }) === 'authorized';

    return {
        ...props,
        hasAccessToTunnelPage,
        openTunnel: xtremRedux.actions.actionStub,
    };
};

const extendedMapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: ReferenceCellRendererExternalProps,
): Partial<ReferenceCellRendererProps> => {
    return {
        openTunnel: (fieldProperties: ReferenceDecoratorProperties, value: any): Promise<any> =>
            dispatch(
                xtremRedux.actions.openTunnel({
                    elementId: props.elementId,
                    parentElementId: props.tableElementId,
                    screenId: props.screenId,
                    recordContext: props.data,
                    contextNode: props.contextNode,
                    fieldProperties,
                    value,
                }),
            ),
    };
};

export const ConnectedReferenceCellRenderer = connect(
    mapStateToProps,
    extendedMapDispatchToProps,
)(ReferenceCellRenderer);

export default ConnectedReferenceCellRenderer;
