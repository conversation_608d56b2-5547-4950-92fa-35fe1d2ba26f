import type { LocalizeLocale } from '@sage/xtrem-shared';
import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import type { CollectionValue } from '../../../service/collection-data-service';
import { runAndDispatchFieldValidation } from '../../../service/dispatch-service';
import type { PageDefinition } from '../../../service/page-definition';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import { getDataTestIdAttribute, isHidden } from '../../../utils/dom';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { cleanMetadataFromRecord, splitValueToMergedValue } from '../../../utils/transformers';
import type { InternalNestedGridProperties } from '../../control-objects';
import { getFieldTitle, isFieldDisabled } from '../carbon-helpers';
import { HelperText } from '../carbon-utility-components';
import { AsyncDesktopNestedGridComponent } from './async-desktop-nested-grid-component';
import type {
    NestedGridComponentExternalProps,
    NestedGridComponentProps,
    NestedGridItemId,
} from './nested-grid-component-types';
import { MobileNestedGridComponent } from './mobile-nested-grid-component';

export class NestedGridComponent extends React.Component<NestedGridComponentProps> {
    getComponentClass = (): string => {
        const className = ['e-field', 'e-nested-grid-field'];
        if (
            isHidden(this.props.item, this.props.browser) ||
            resolveByValue({
                screenId: this.props.screenId,
                skipHexFormat: true,
                propertyValue: this.props.fieldProperties.isHidden,
                rowValue: null,
            })
        ) {
            className.push('e-hidden');
        }

        if (this.props.fieldProperties.isFullWidth) {
            className.push('full-width');
        }

        if (this.props.fieldProperties.isHelperTextHidden) {
            className.push('e-helper-text-hidden');
        }

        if (this.props.fieldProperties.isTitleHidden) {
            className.push('e-title-hidden');
        }

        if (
            this.props.isParentDisabled ||
            isFieldDisabled(this.props.screenId, this.props.fieldProperties, undefined, undefined)
        ) {
            className.push('e-disabled');
        }

        return className.join(' ');
    };

    onRowClick = (recordId: NestedGridItemId, level: number): void => {
        if (this.props.value) {
            const rowValue = this.props.value.getRawRecord({ id: recordId, level });
            if (this.props.onRowClick) {
                this.props.onRowClick(rowValue);
            } else {
                triggerFieldEvent(
                    this.props.screenId,
                    this.props.elementId,
                    'onRowClick',
                    recordId,
                    cleanMetadataFromRecord(splitValueToMergedValue(rowValue)),
                    level,
                );
            }
        }
    };

    render(): React.ReactNode {
        const isMobileTable = this.props.fieldProperties.cardView || !this.props.browser.greaterThan.s;

        return (
            <div
                className={this.getComponentClass()}
                style={{ height: this.props.fixedHeight }}
                data-testid={getDataTestIdAttribute(
                    'nestedGrid',
                    getFieldTitle(this.props.screenId, this.props.fieldProperties, null),
                    this.props.elementId,
                )}
            >
                {!isMobileTable && (
                    <AsyncDesktopNestedGridComponent
                        accessBindings={this.props.accessBindings}
                        dataTypes={this.props.dataTypes}
                        elementId={this.props.elementId}
                        enumTypes={this.props.enumTypes}
                        fieldProperties={this.props.fieldProperties}
                        fixedHeight={this.props.fixedHeight ? this.props.fixedHeight - 48 : undefined}
                        isParentDisabled={this.props.isParentDisabled}
                        isUsingInfiniteScroll={this.props.isUsingInfiniteScroll}
                        locale={this.props.locale}
                        nodeTypes={this.props.nodeTypes}
                        onRowClick={this.onRowClick}
                        screenId={this.props.screenId}
                        setFieldProperties={this.props.setFieldProperties}
                        setGlobalLoading={this.props.setGlobalLoading}
                        username={this.props.username}
                        validationErrors={this.props.validationErrors}
                        value={this.props.value}
                    />
                )}
                {isMobileTable && (
                    <MobileNestedGridComponent
                        elementId={this.props.elementId}
                        fieldProperties={this.props.fieldProperties}
                        isDisabled={this.props.isParentDisabled || false}
                        value={this.props.value}
                        validationErrors={this.props.validationErrors}
                        screenId={this.props.screenId}
                        openSidebar={this.props.openSidebar}
                        canAddNewLine={false}
                        setFieldProperties={this.props.setFieldProperties}
                        onRowClick={this.onRowClick}
                    />
                )}
                {this.props.fieldProperties.helperText && (
                    <HelperText helperText={this.props.fieldProperties.helperText} />
                )}
            </div>
        );
    }
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: NestedGridComponentExternalProps,
): NestedGridComponentProps => {
    const screenDefinition = state.screenDefinitions[props.screenId] as PageDefinition;
    const fieldProperties = screenDefinition.metadata.uiComponentProperties[
        props.elementId
    ] as InternalNestedGridProperties<ScreenBase>;
    const pageProperties = screenDefinition.metadata.uiComponentProperties[props.screenId];

    return {
        ...props,
        browser: state.browser,
        dataTypes: state.dataTypes,
        fieldProperties: {
            ...fieldProperties,
            isTransient: pageProperties.isTransient || fieldProperties.isTransient,
        },
        username: state.applicationContext?.login,
        locale: (state.applicationContext?.locale as LocalizeLocale) || 'base',
        setFieldProperties: xtremRedux.actions.actionStub,
        validate: xtremRedux.actions.actionStub,
        openSidebar: xtremRedux.actions.actionStub,
        value: screenDefinition.values[props.elementId],
        enumTypes: state.enumTypes,
        nodeTypes: state.nodeTypes,
        accessBindings: screenDefinition.accessBindings || {},
        validationErrors: screenDefinition.errors[props.elementId] || [],
        onTelemetryEvent: state.applicationContext?.onTelemetryEvent,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: NestedGridComponentProps,
): Partial<NestedGridComponentProps> => {
    return {
        validate: (elementId: string, value: CollectionValue): Promise<ValidationResult[] | undefined> =>
            runAndDispatchFieldValidation(props.screenId, elementId, value.getNormalizedChangedRecords()),
        setFieldProperties: (elementId: string, value: InternalNestedGridProperties<ScreenBase>): void => {
            dispatch(xtremRedux.actions.setFieldProperties(props.screenId, elementId, value));
        },
        setGlobalLoading: (loaderState: boolean): void => {
            dispatch(xtremRedux.actions.setGlobalLoading(loaderState));
        },
        openSidebar: ({
            screenId,
            elementId,
            level,
            parentId,
            fieldProperties,
        }: {
            screenId: string;
            elementId: string;
            level: number;
            fieldProperties: InternalNestedGridProperties<ScreenBase>;
            parentId?: string;
        }): void => {
            const fieldPropertiesLevel = props.fieldProperties || fieldProperties;
            dispatch(
                xtremRedux.actions.openTableSidebar({
                    screenId,
                    elementId,
                    level,
                    parentId,
                    sidebarDefinition: fieldPropertiesLevel.levels[level].sidebar,
                    cardDefinition: fieldPropertiesLevel.mobileCard,
                    columns: fieldPropertiesLevel.levels[level].columns,
                }),
            );
        },
    };
};

export const ConnectedNestedGridComponent = connect(mapStateToProps, mapDispatchToProps)(NestedGridComponent);

export default ConnectedNestedGridComponent;
