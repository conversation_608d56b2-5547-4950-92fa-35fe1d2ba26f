import { useDeepCompareEffect, useDeepCompareMemo } from '@sage/xtrem-ui-components';
import type { TargetAndTransition, Variants } from 'framer-motion';
import { AnimatePresence, motion } from 'framer-motion';
import { noop } from 'lodash';
import React, { useState } from 'react';
import type { CollectionValue } from '../../../service/collection-data-service';
import { RecordActionType } from '../../../service/collection-data-types';
import { triggerFieldEvent, triggerNestedFieldEvent, type NestedFieldComponentProperties } from '../../../utils/events';
import { convertDeepBindToPath } from '../../../utils/nested-field-utils';
import { cleanMetadataFromRecord } from '../../../utils/transformers';
import type { NestedFieldsProperties, NestedFieldTypes } from '../../nested-fields';
import { FieldKey, type CollectionItem, type Events } from '../../types';
import { MobileTable, type UiMobileTableProps } from '../../ui/mobile-table/mobile-table-component';
import { isFieldTitleHidden } from '../carbon-helpers';
import { getReferenceValueFieldPath } from '../reference/reference-utils';
import { MobileNestedGridHeader } from './mobile-nested-grid-header';
import type { MobileNestedGridComponentProps } from './nested-grid-types';
import { fetchRows } from './nested-grid-utils';

export function MobileNestedGridComponent({
    elementId,
    fieldProperties,
    isDisabled,
    onRowClick: controlledOnRowClick,
    screenId,
    setFieldProperties,
    value,
    openSidebar,
}: MobileNestedGridComponentProps): React.ReactElement {
    const [isFetchingNextPage, setIsFetchingNextPage] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [hasMorePages, setHasMorePages] = useState(false);
    const [valuesDisplayed, setValuesDisplayed] = useState<any[]>([]);
    const [pageNumber, setPageNumber] = useState(0);
    const [currentLevel, setCurrentLevel] = useState(0);
    const [parentByLevel, setParentByLevel] = useState<any[]>([]);
    const [direction, setDirection] = useState(1);
    const [isAnimationComplete, setIsAnimationComplete] = useState(true);
    const isTitleHidden = useDeepCompareMemo(
        () => isFieldTitleHidden(screenId, fieldProperties, null),
        [fieldProperties, screenId],
    );
    const currentLevelObject = useDeepCompareMemo(
        () => fieldProperties.levels[currentLevel],
        [currentLevel, fieldProperties.levels],
    );
    const parentLevelObject = useDeepCompareMemo(
        () => (currentLevel > 0 ? fieldProperties.levels[currentLevel - 1] : undefined),
        [currentLevel, fieldProperties.levels],
    );

    const rowSelectionMode = useDeepCompareMemo(() => {
        if (fieldProperties.canActivate) {
            return 'single';
        }
        if (fieldProperties.canSelect) {
            return 'multiple';
        }
        return 'none';
    }, [fieldProperties.canSelect, fieldProperties.canActivate]);

    const parentId = useDeepCompareMemo(
        () => (currentLevel > 0 ? parentByLevel[currentLevel - 1]?._id : undefined),
        [currentLevel, parentByLevel],
    );

    const selectedRecords = useDeepCompareMemo(() => {
        const result = [...(fieldProperties.selectedRecords || [])];
        for (let i = 0; i <= currentLevel; i += 1) {
            if (!result[i]) {
                result[i] = [];
            }
        }
        return result;
    }, [fieldProperties.selectedRecords, currentLevel]);

    const setSelectedRecords = useDeepCompareMemo(
        () =>
            (selectedRecordsClone: string[][]): void => {
                if (setFieldProperties) {
                    setFieldProperties(elementId, {
                        ...fieldProperties,
                        selectedRecords: selectedRecordsClone,
                    });
                }
            },
        [elementId, fieldProperties, setFieldProperties],
    );

    const triggerEvent = useDeepCompareMemo(
        () =>
            (
                record: CollectionItem,
                eventType: Extract<Events, 'onRowSelected' | 'onRowUnselected' | 'onRowActivated' | 'onRowDeactivated'>,
                level: number,
            ): Promise<void> => {
                return triggerFieldEvent(
                    screenId,
                    elementId,
                    eventType,
                    record._id,
                    cleanMetadataFromRecord(record),
                    level,
                );
            },
        [screenId, elementId],
    );

    const onRowActivated = useDeepCompareMemo(
        () =>
            (row: CollectionItem): void => {
                const isSelected = selectedRecords[currentLevel].includes(row._id);
                let eventType: 'onRowActivated' | 'onRowDeactivated';
                if (!isSelected) {
                    eventType = 'onRowActivated';
                    selectedRecords[currentLevel] = [];
                    selectedRecords[currentLevel].push(row._id);
                } else {
                    eventType = 'onRowDeactivated';
                    selectedRecords[currentLevel] = [];
                }
                setSelectedRecords(selectedRecords);
                triggerEvent(row, eventType, currentLevel);
            },
        [currentLevel, setSelectedRecords, selectedRecords, triggerEvent],
    );

    const onRowSelected = useDeepCompareMemo(
        () =>
            (row: CollectionItem): void => {
                if (rowSelectionMode === 'multiple') {
                    const isSelected = selectedRecords[currentLevel].includes(row._id);
                    let eventType: 'onRowSelected' | 'onRowUnselected';
                    const promises: Promise<void>[] = [];
                    if (!isSelected) {
                        eventType = 'onRowSelected';
                        selectedRecords[currentLevel].push(row._id);
                        if (currentLevel > 0) {
                            const values = valuesDisplayed.filter(
                                v => parentId && v.__level === currentLevel && v.__parentId === parentId,
                            );
                            const allChecked = values.every(v => selectedRecords[currentLevel].includes(v._id));
                            if (allChecked && parentId) {
                                if (!selectedRecords[currentLevel - 1].includes(parentId)) {
                                    selectedRecords[currentLevel - 1].push(parentId);
                                    const triggerSelectParent = triggerEvent(
                                        parentByLevel[currentLevel - 1],
                                        eventType,
                                        currentLevel - 1,
                                    );
                                    promises.push(triggerSelectParent);
                                }
                            }
                        }
                    } else {
                        eventType = 'onRowUnselected';
                        const currentPosition = selectedRecords[currentLevel].indexOf(row._id);
                        selectedRecords[currentLevel].splice(currentPosition, 1);
                        if (currentLevel > 0) {
                            for (let j = currentLevel - 1; j >= 0; j -= 1) {
                                if (selectedRecords[j].includes(parentByLevel[j]?._id)) {
                                    const position = selectedRecords[j]?.indexOf(parentByLevel[j]?._id);
                                    selectedRecords[j].splice(position, 1);
                                    const triggerUnselectAncestor = triggerEvent(parentByLevel[j], eventType, j);
                                    promises.push(triggerUnselectAncestor);
                                }
                            }
                        }
                    }
                    setSelectedRecords(selectedRecords);
                    const triggerToggleRow = triggerEvent(row, eventType, currentLevel);
                    promises.push(triggerToggleRow);
                    Promise.all(promises);
                }
            },
        [
            currentLevel,
            parentId,
            parentByLevel,
            rowSelectionMode,
            setSelectedRecords,
            selectedRecords,
            triggerEvent,
            valuesDisplayed,
        ],
    );

    useDeepCompareEffect(() => {
        const pageSize = fieldProperties.pageSize || 20;
        setIsFetchingNextPage(true);
        setIsLoading(true);
        fetchRows({
            value,
            fieldProperties,
            pageNumber,
            currentLevel,
            parentId,
            pageSize,
            cursor:
                valuesDisplayed?.[valuesDisplayed.length - 1]?.__level === currentLevel
                    ? valuesDisplayed?.[valuesDisplayed.length - 1]?.__cursor
                    : undefined,
        }).then(newData => {
            setIsFetchingNextPage(false);
            setIsLoading(false);
            setValuesDisplayed(pageNumber === 0 ? newData : [...valuesDisplayed, ...newData]);
            setHasMorePages(newData.length >= pageSize);
            if (pageNumber === 0 && currentLevel > 0 && parentId && direction > 0) {
                triggerNestedFieldEvent(
                    screenId,
                    elementId,
                    fieldProperties.levels[currentLevel - 1] as NestedFieldComponentProperties,
                    'onLevelExpanded',
                    parentId,
                    newData,
                );
            }
        });
    }, [parentId, value, fieldProperties, pageNumber, currentLevel, screenId, elementId, direction, pageNumber]);

    useDeepCompareEffect(() => {
        if (rowSelectionMode === 'multiple' && currentLevel > 0) {
            const parentIsSelected = parentId && selectedRecords[currentLevel - 1]?.includes(parentId);
            if (parentIsSelected) {
                const valuesToSelect = valuesDisplayed.filter(
                    row =>
                        row.__level === currentLevel &&
                        row.__parentId === parentId &&
                        !selectedRecords[currentLevel].includes(row._id),
                );
                if (valuesToSelect.length > 0) {
                    const promises = valuesToSelect.map((row: any) => {
                        selectedRecords[currentLevel].push(row._id);
                        return triggerEvent(row, 'onRowSelected', currentLevel);
                    });
                    setSelectedRecords(selectedRecords);
                    Promise.all(promises);
                }
            }
        }
    }, [valuesDisplayed, selectedRecords, currentLevel, parentId, triggerEvent, setSelectedRecords, rowSelectionMode]);

    const onFetchNextPage = useDeepCompareMemo(
        () => (): void => {
            setPageNumber(current => current + 1);
        },
        [],
    );

    const onBackButtonClick = useDeepCompareMemo(
        () => (): void => {
            setCurrentLevel(currentLevel - 1);
            setDirection(-1);
            if (currentLevel < 1) {
                setParentByLevel([]);
            } else {
                setParentByLevel(parentByLevel.slice(0, currentLevel - 1));
            }
        },
        [currentLevel, parentByLevel],
    );

    const onRowClick = useDeepCompareMemo<(i: any) => () => void>(
        () => item => (): void => {
            setCurrentLevel(currentLevel + 1);
            setParentByLevel([...parentByLevel, item]);
            setDirection(1);
            controlledOnRowClick?.(item._id, currentLevel);
        },
        [currentLevel, parentByLevel, controlledOnRowClick],
    );

    const getTitlePath = useDeepCompareMemo(
        () =>
            (properties?: NestedFieldsProperties<NestedFieldTypes>): string | null => {
                return properties?._controlObjectType === FieldKey.Reference
                    ? getReferenceValueFieldPath(properties as NestedFieldsProperties<FieldKey.Reference>)
                    : convertDeepBindToPath(properties?.bind);
            },
        [],
    );

    const uiMobileProps = useDeepCompareMemo<UiMobileTableProps>(
        () => ({
            elementId,
            fieldProperties: fieldProperties as any,
            columns: currentLevelObject.columns,
            groupByField: null,
            hasMorePages,
            isDisabled,
            isGreaterThanSmall: false,
            isFetchingNextPage,
            isNavigationPanel: false,
            onFetchNextPage,
            onRowSelected: rowSelectionMode === 'single' ? onRowActivated : onRowSelected,
            dropdownActions: currentLevelObject.dropdownActions,
            screenId,
            valuesDisplayed,
            onRowClick: currentLevel < fieldProperties.levels.length - 1 ? onRowClick : undefined,
            isLoading,
            isNestedGrid: true,
            level: currentLevel,
        }),
        [
            elementId,
            fieldProperties,
            currentLevelObject.columns,
            currentLevelObject.dropdownActions,
            hasMorePages,
            isDisabled,
            isFetchingNextPage,
            onFetchNextPage,
            rowSelectionMode,
            onRowActivated,
            onRowSelected,
            screenId,
            valuesDisplayed,
            currentLevel,
            onRowClick,
            isLoading,
        ],
    );

    const animationVariants = useDeepCompareMemo(
        (): Variants => ({
            enter: (incDirection: number): TargetAndTransition => {
                return {
                    x: incDirection > 0 ? 1000 : -1000,
                    opacity: 0,
                };
            },
            center: {
                zIndex: 1,
                x: 0,
                opacity: 1,
            },
            exit: (incDirection: number): TargetAndTransition => {
                return {
                    zIndex: 0,
                    x: incDirection < 0 ? 1000 : -1000,
                    opacity: 0,
                };
            },
        }),
        [],
    );

    useDeepCompareEffect(() => {
        const subscribeToCollectionValueEvents = (
            pageSize: number,
            collectionValue?: CollectionValue,
        ): (() => void) => {
            if (!collectionValue) {
                return noop;
            }
            return collectionValue.subscribeForValueChanges(async (actionType: RecordActionType, rowValue: any) => {
                const rowPosition = valuesDisplayed.findIndex(
                    r => r._id === rowValue._id && r.__level === rowValue.__level,
                );
                const isRowOnScreen = rowPosition !== -1;
                if (actionType === RecordActionType.MODIFIED && isRowOnScreen) {
                    setValuesDisplayed(state => {
                        const newValues = [...state];
                        newValues[rowPosition] = rowValue;
                        return newValues;
                    });
                } else if (actionType === RecordActionType.REMOVED && isRowOnScreen) {
                    const newValues = await collectionValue.getRecordWithCurrentQueryArguments({
                        tableFieldProperties: fieldProperties,
                        pageSize,
                        pageNumber,
                        cursor: [...valuesDisplayed].reverse()[0]?.__cursor,
                        cleanMetadata: false,
                        parentId,
                        level: currentLevel,
                    });
                    setValuesDisplayed(newValues);
                } else if (actionType === RecordActionType.ADDED) {
                    setIsLoading(true);
                    const newValues = await collectionValue.getPage({
                        tableFieldProperties: fieldProperties,
                        pageSize: Math.max(valuesDisplayed.length, fieldProperties.pageSize || 20),
                        cursor: undefined,
                        cleanMetadata: false,
                        parentId,
                        level: currentLevel,
                    });
                    setIsLoading(false);
                    setValuesDisplayed(newValues);
                    setPageNumber(0);
                }
            });
        };
        return subscribeToCollectionValueEvents(fieldProperties.pageSize || 20, value);
    }, [currentLevel, fieldProperties, pageNumber, parentId, value, valuesDisplayed]);

    return (
        <div data-testid="e-mobile-nested-grid-component">
            <MobileNestedGridHeader
                currentLevel={currentLevel}
                currentLevelObject={currentLevelObject}
                elementId={elementId}
                fieldProperties={fieldProperties}
                isTitleHidden={isTitleHidden}
                onBackButtonClick={onBackButtonClick}
                openSidebar={openSidebar}
                parentByLevel={parentByLevel}
                parentLevelObject={parentLevelObject}
                screenId={screenId}
                getTitlePath={getTitlePath}
            />
            <AnimatePresence initial={false} custom={direction}>
                <motion.div
                    key={currentLevel}
                    custom={direction}
                    variants={animationVariants}
                    initial="enter"
                    animate="center"
                    exit="exit"
                    transition={{
                        x: { type: 'spring', stiffness: 300, damping: 30 },
                        opacity: { duration: 0.2 },
                    }}
                    onAnimationStart={() => setIsAnimationComplete(false)}
                    onAnimationComplete={() => setIsAnimationComplete(true)}
                >
                    {isAnimationComplete && <MobileTable {...uiMobileProps} />}
                </motion.div>
            </AnimatePresence>
        </div>
    );
}
