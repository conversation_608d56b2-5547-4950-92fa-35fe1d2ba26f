/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { ElementOf } from 'ts-essentials';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import type { BlockControlObject, NestedGridProperties, SectionControlObject } from '../../control-objects';
import { NestedGridControlObject } from '../../control-objects';
import * as nestedFields from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import { FieldKey } from '../../types';
import type {
    NestedCollectionItemActionGroup,
    NestedCollectionItemActionOrMenuSeparator,
} from '../../ui/table-shared/table-dropdown-actions/table-dropdown-action-types';
import type { Clickable, ExtensionField, HasFieldActions, HasParent, VoidPromise } from '../traits';
import type { MappedExtensionLevel, NestedGridItemId } from './nested-grid-component-types';

export interface NestedGridDecoratorProperties<CT extends ScreenBase = ScreenBase, L extends ClientNode[] = [any, any]>
    extends NestedGridProperties<CT, L>,
        Clickable<CT>,
        HasFieldActions<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>> {
    /** Function to be executed when a row is activated */
    onRowActivated?: (this: CT, recordId: NestedGridItemId, rowItem: ElementOf<L>, level: number) => VoidPromise;
    /** Function to be executed when a row is deactivated */
    onRowDeactivated?: (this: CT, recordId: NestedGridItemId, rowItem: ElementOf<L>, level: number) => VoidPromise;
    /** Function to be executed when a row is selected */
    onRowSelected?: (this: CT, recordId: NestedGridItemId, rowItem: ElementOf<L>, level: number) => VoidPromise;
    /** Function to be executed when a row is unselected */
    onRowUnselected?: (this: CT, recordId: NestedGridItemId, rowItem: ElementOf<L>, level: number) => VoidPromise;
    /** Function to be executed when the nestedGrid field's value changes */
    onChange?: (this: CT, recordId: NestedGridItemId, column: string, rowItem: ElementOf<L>) => VoidPromise;
    /** Function to be executed when any part of a row is clicked */
    onRowClick?: (this: CT, recordId: NestedGridItemId, rowItem: ElementOf<L>, level: number) => VoidPromise;
    /** Actions that are rendered at the end of the nestedGrid as a drop-down menu */
    dropdownActions?: Array<NestedCollectionItemActionOrMenuSeparator<CT> | NestedCollectionItemActionGroup<CT>>;
    /** Displays line numbers in front of each line. The line numbers are static and not related to the data */
    hasLineNumbers?: boolean;
}

type BaseNestedGridExtensionDecoratorProperties<CT extends ScreenBase = ScreenBase, L extends ClientNode[] = any> = {
    /** Function to be executed when a row is activated and after onRowActivated*/
    onRowActivatedAfter?: (this: CT, recordId: NestedGridItemId, rowItem: ElementOf<L>, level: number) => VoidPromise;
    /** Function to be executed when a row is deactivated and after onRowDeactivated */
    onRowDeactivatedAfter?: (this: CT, recordId: NestedGridItemId, rowItem: ElementOf<L>, level: number) => VoidPromise;
    /** Function to be executed when a row is selected and after onRowSelected*/
    onRowSelectedAfter?: (this: CT, recordId: NestedGridItemId, rowItem: ElementOf<L>, level: number) => VoidPromise;
    /** Function to be executed when a row is unselected and after onRowUnselected*/
    onRowUnselectedAfter?: (this: CT, recordId: NestedGridItemId, rowItem: ElementOf<L>, level: number) => VoidPromise;
    /** Function to be executed when the nestedGrid field's value changes and after onChange*/
    onChangeAfter?: (this: CT, recordId: NestedGridItemId, column: string, rowItem: ElementOf<L>) => VoidPromise;
    /** Function to be executed when any part of a row is clicked and after onRowClick */
    onRowClickAfter?: (this: CT, recordId: NestedGridItemId, rowItem: ElementOf<L>) => VoidPromise;
};

export interface NestedGridExtensionDecoratorProperties<
    CT extends ScreenExtension<CT>,
    L extends ClientNode[] = [any, any],
> extends BaseNestedGridExtensionDecoratorProperties<Extend<CT>>,
        ClickableOverrideDecoratorProperties<NestedGridDecoratorProperties<Extend<CT>, L>, CT> {
    /** The definitions of the nested grid's levels */
    levels?: MappedExtensionLevel<CT, L>;
}

class NestedGridDecorator extends AbstractFieldDecorator<FieldKey.NestedGrid> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = NestedGridControlObject;
}

/**
 * Initializes the decorated member as a [NestedGrid]{@link NestedGridControlObject} field with the provided properties
 *
 * @param properties The properties that the [NestedGrid]{@link NestedGridControlObject} field will be initialized with
 */
export function nestedGridField<T extends ScreenExtension<T>, L extends ClientNode[] = [any, any]>(
    properties: NestedGridDecoratorProperties<T, L>,
): (target: T, name: string) => void {
    const levels = properties.levels;
    if (levels.length < 2) {
        throw new Error('A nested grid must have at least two levels.');
    }
    const levelsLength = levels.length;
    for (let i = 0; i < levelsLength - 1; i += 1) {
        const level = levels[i];
        if (level.childProperty === undefined) {
            throw new Error('All nested grid\'s levels but the last must have a "childProperty" set.');
        }
    }
    if (levels[levelsLength - 1].childProperty !== undefined) {
        throw new Error('The last level of a nested grid should not have a "childProperty" set.');
    }

    levels.forEach(({ columns }) => {
        if (!columns.find(c => c.properties.bind === '_id')) {
            columns.push(nestedFields.text({ bind: '_id', isHidden: true }));
        }
    });
    return standardDecoratorImplementation<T, FieldKey.NestedGrid>(
        properties as any,
        NestedGridDecorator,
        FieldKey.NestedGrid,
        true,
    );
}

export function nestedGridFieldOverride<T extends ScreenExtension<T>, L extends ClientNode[] = [any, any]>(
    properties: NestedGridExtensionDecoratorProperties<T, L>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.NestedGrid>(properties as any);
}
