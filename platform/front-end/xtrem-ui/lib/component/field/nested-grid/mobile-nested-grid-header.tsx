import Button from 'carbon-react/esm/components/button';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import { get } from 'lodash';
import React from 'react';
import { localize } from '../../../service/i18n-service';
import { getCardDefinitionFromColumns } from '../../../utils/table-component-utils';
import { getFieldTitle } from '../carbon-helpers';
import type { MobileNestedGridHeaderProps } from './nested-grid-types';
import { useDeepCompareMemo } from '@sage/xtrem-ui-components';

export function MobileNestedGridHeader({
    currentLevel,
    currentLevelObject,
    elementId,
    fieldProperties,
    isTitleHidden,
    onBackButtonClick,
    openSidebar,
    parentByLevel,
    parentLevelObject,
    screenId,
    getTitlePath,
}: MobileNestedGridHeaderProps): React.ReactNode {
    const parentNestedFields = useDeepCompareMemo(
        () =>
            getCardDefinitionFromColumns({
                columns: parentLevelObject?.columns,
                mobileCard: fieldProperties.mobileCard,
                screenId,
                isGreaterThanSmall: false,
            }),
        [parentLevelObject?.columns, fieldProperties.mobileCard, screenId],
    );

    const titleBind = useDeepCompareMemo(
        () => (currentLevel > 0 ? getTitlePath(parentNestedFields.title?.properties) : undefined),
        [currentLevel, getTitlePath, parentNestedFields.title?.properties],
    );

    const headerTitle = useDeepCompareMemo(
        () =>
            currentLevel === 0 && fieldProperties.title && !isTitleHidden
                ? getFieldTitle(screenId, fieldProperties, null)
                : null,
        [currentLevel, fieldProperties, isTitleHidden, screenId],
    );

    const childTitle = useDeepCompareMemo(
        () => (titleBind ? get(parentByLevel[currentLevel - 1], titleBind) : null),
        [currentLevel, parentByLevel, titleBind],
    );

    const onAddLineInSidebar = useDeepCompareMemo(
        () => (): void => {
            openSidebar({
                screenId,
                elementId,
                level: currentLevel,
                parentId: parentByLevel[currentLevel - 1]?._id,
                fieldProperties,
            });
        },
        [currentLevel, elementId, fieldProperties, openSidebar, parentByLevel, screenId],
    );

    return (
        <div data-testid="e-mobile-nested-grid-header" className="e-nested-grid-mobile-header">
            <div className="e-nested-grid-mobile-header-left-container">
                {headerTitle && (
                    <span data-testid="e-mobile-nested-grid-header-title" className="e-nested-grid-mobile-header-text">
                        {headerTitle}
                    </span>
                )}
                {currentLevel > 0 && (
                    <ButtonMinor
                        data-testid="e-mobile-nested-grid-header-back-button"
                        iconType="arrow_left"
                        onClick={onBackButtonClick}
                        buttonType="tertiary"
                        size="large"
                    />
                )}
                {childTitle && (
                    <span
                        data-testid="e-mobile-nested-grid-header-child-title"
                        className="e-nested-grid-mobile-header-text"
                    >
                        {childTitle}
                    </span>
                )}
            </div>
            <div className="e-mobile-nested-grid-header-right-container">
                {currentLevelObject.canAddNewLine && currentLevelObject.sidebar && (
                    <Button onClick={onAddLineInSidebar} buttonType="primary">
                        {localize('@sage/xtrem-ui/add-item-in-line', 'Add line')}
                    </Button>
                )}
                <span data-testid="e-mobile-grid-header-level" className="e-nested-grid-mobile-header-text">
                    {currentLevel + 1}/{fieldProperties.levels.length}
                </span>
            </div>
        </div>
    );
}
