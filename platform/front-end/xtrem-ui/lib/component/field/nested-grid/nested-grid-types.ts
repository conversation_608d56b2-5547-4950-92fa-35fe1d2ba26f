import type { CollectionValue } from '../../../service/collection-data-service';
import type { ValidationResult } from '../../../service/screen-base-definition';
import type { NestedFieldsProperties, NestedFieldTypes } from '../../nested-fields';
import type { NestedGridItemId } from './nested-grid-component-types';
import type { InternalNestedGridProperties } from './nested-grid-control-object';

export interface MobileNestedGridComponentProps {
    canAddNewLine: boolean;
    elementId: string;
    fieldProperties: InternalNestedGridProperties;
    isDisabled: boolean;
    openSidebar: (args: {
        screenId: string;
        elementId: string;
        level: number;
        fieldProperties: InternalNestedGridProperties;
        parentId?: string;
    }) => void;
    onRowClick?: (recordId: NestedGridItemId, level: number) => void;
    screenId: string;
    setFieldProperties?: (elementId: string, value: any) => void;
    validationErrors: ValidationResult[];
    value: CollectionValue;
}

export interface MobileNestedGridHeaderProps {
    currentLevel: number;
    currentLevelObject: any;
    elementId: MobileNestedGridComponentProps['elementId'];
    fieldProperties: MobileNestedGridComponentProps['fieldProperties'];
    getTitlePath: (properties?: NestedFieldsProperties<NestedFieldTypes>) => string | null;
    isTitleHidden: boolean;
    onBackButtonClick: () => void;
    openSidebar: MobileNestedGridComponentProps['openSidebar'];
    parentByLevel: any[];
    parentLevelObject?: any;
    screenId: MobileNestedGridComponentProps['screenId'];
}
