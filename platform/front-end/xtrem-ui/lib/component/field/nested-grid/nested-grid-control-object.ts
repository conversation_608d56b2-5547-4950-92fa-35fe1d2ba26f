/**
 * @packageDocumentation
 * @module root
 * */
import type { ClientNode } from '@sage/xtrem-client';
import { objectKeys } from '@sage/xtrem-shared';
import { get, set, sortBy, uniq } from 'lodash';
import { ActionType, getStore, type AppThunkDispatch } from '../../../redux';
import { openTableSidebar } from '../../../redux/actions';
import { CollectionValue } from '../../../service/collection-data-service';
import { CollectionFieldTypes } from '../../../service/collection-data-types';
import { fetchNestedDefaultValues } from '../../../service/graphql-service';
import type { GraphQLFilter, QueryArguments } from '../../../service/graphql-utils';
import { PromiseTracker } from '../../../service/promise-tracker';
import type { ScreenBase } from '../../../service/screen-base';
import { showToast } from '../../../service/toast-service';
import type { ScreenBaseGenericType, ScreenExtension } from '../../../types';
import { xtremConsole } from '../../../utils/console';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { ValueOrCallback, ValueOrCallbackWithFieldValue } from '../../../utils/types';
import type { OptionsMenuItem, TableOptionsMenuType } from '../../container/page/page-types';
import type { NestedGridDecoratorProperties } from '../../decorators';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { PageActionControlObject } from '../../page-action/page-action-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldKey, PartialCollectionValue, PartialCollectionValueWithIds } from '../../types';
import { TableDisplayMode } from '../../types';
import type { CardDefinition } from '../../ui/card/card-component';
import type { AdditionalFieldAction } from '../field-actions-component';
import type { HasFieldActions, HasOptionsMenu, HasRowChangeIndicators } from '../traits';
import type { MappedLevel, MappedSelected, NestedGridItemId } from './nested-grid-component-types';
import { getLevelMap } from './nested-grid-utils';

export interface NestedGridProperties<CT extends ScreenExtension<CT> = ScreenBase, L extends ClientNode[] = [any, any]>
    extends Omit<EditableFieldProperties<CT>, 'onChange'>,
        HasFieldActions<CT>,
        HasOptionsMenu<CT>,
        HasRowChangeIndicators {
    /** Whether the rows of the nested grid can be filtered or not. Defaults to true */
    canFilter?: boolean;
    /** Whether the rows of the nested grid can be activated or not. Defaults to false.
     *  If set to true then single row selection will be used, such that when you select a row, any previously selected row gets unselected.
     */
    canActivate?: boolean;
    /** Whether the rows of the nested grid can be selected or not. Defaults to true.
     *  Allows multiple rows to be selected.
     */
    canSelect?: boolean;
    /** Whether the user can decide which nested grid columns to display. Defaults to true */
    canUserHideColumns?: boolean;
    /** The definitions of the nested grid's levels */
    levels: MappedLevel<CT, L>;
    /** Determines how the nested grid rows are displayed. Defaults to "comfortable" */
    displayMode?: TableDisplayMode;
    /**  Whether in mobile devices a search box should be displayed or not*/
    hasSearchBoxMobile?: boolean;
    /** The definitions of the nested fields used to represent the nested grid rows on mobile.
     * If no value is provided, the first four columns are used by default */
    mobileCard?: CardDefinition<CT>;
    /** Number of lines displayed by default in the nested grid. Defaults to 20 */
    pageSize?: number;
    /** Selected rows identifiers */
    selectedRecords?: MappedSelected<L>;
    /** The GraphQL node that the nested grid represents, needed for filtering */
    node?: keyof ScreenBaseGenericType<CT>;
    /** Forces a mobile-like card view on desktop */
    cardView?: boolean;
    /** Indicate additional warning message, rendered as tooltip and blue border. */
    infoMessage?: ValueOrCallbackWithFieldValue<CT, string>;
    /** Indicate additional information, rendered as tooltip and orange border. */
    warningMessage?: ValueOrCallbackWithFieldValue<CT, string>;
}

export interface InternalNestedGridProperties<CT extends ScreenExtension<CT> = ScreenBase>
    extends NestedGridProperties<CT> {
    activeUserFilter?: GraphQLFilter;
    headerBusinessActions?: ValueOrCallback<CT, PageActionControlObject[]>;
    optionsMenu?: ValueOrCallback<CT, OptionsMenuItem[]>;
    optionsMenuType?: TableOptionsMenuType;
    additionalFieldActions?: AdditionalFieldAction[];
}

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a set of values of any type. It can contain nested fields
 */
export class NestedGridControlObject<
    L extends ClientNode[] = [any, any],
    CT extends ScreenExtension<CT> = ScreenBase,
> extends ReadonlyFieldControlObject<CT, FieldKey.NestedGrid, NestedGridProperties<CT, L>> {
    static readonly defaultUiProperties: Partial<NestedGridDecoratorProperties> = {
        ...ReadonlyFieldControlObject.defaultUiProperties,
        canFilter: true,
        canSelect: true,
        canUserHideColumns: true,
        displayMode: TableDisplayMode.comfortable,
        pageSize: 20,
    };

    private ensureNestedGridHasValue(initialValues: any[] = []): CollectionValue<any> {
        const currentValue = this._getValue();
        return !currentValue ? this.setFieldValue(initialValues) : currentValue;
    }

    private setFieldValue(initialValues: any[] = []): CollectionValue<any> {
        const levels = this.getUiComponentProperty('levels');
        const nodes = levels.map(l => String(l.node));
        const nestedGridFilters = levels.map(l =>
            resolveByValue({
                screenId: this.screenId,
                skipHexFormat: true,
                propertyValue: l.filter,
                rowValue: null,
                fieldValue: null,
            }),
        );

        const value = new CollectionValue({
            bind: this.uiComponentProperties.bind,
            columnDefinitions: levels.map(level => level.columns),
            elementId: this.elementId,
            fieldType: CollectionFieldTypes.NESTED_GRID,
            filter: nestedGridFilters,
            hasNextPage: false,
            initialValues,
            levelMap: getLevelMap(levels) as any,
            mapServerRecordFunctions: levels.map(l => l.mapServerRecord),
            isTransient: !!this.getUiComponentProperty('isTransient'),
            nodes,
            orderBy: levels.map(l => l.orderBy),
            screenId: this.screenId,
        });

        this._setValue(value);

        return value;
    }

    private defaultSelectedRecords(): MappedSelected<L> {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        return this.getUiComponentProperty('levels' as any).map((_: any) => []) as MappedSelected<L>;
    }

    /** Whether user can hide columns or not */
    get canUserHideColumns(): boolean {
        const propertyValue = this.getUiComponentProperty('canUserHideColumns');
        return propertyValue === undefined ? true : propertyValue;
    }

    /** Whether user can hide columns or not */
    set canUserHideColumns(newValue: boolean) {
        this.setUiComponentProperties('canUserHideColumns', newValue);
    }

    @ControlObjectProperty<NestedGridProperties<CT, L>, NestedGridControlObject<L, CT>>()
    /** Number of lines displayed by default in the nested grid (defaults to 20) */
    pageSize?: number;

    @ControlObjectProperty<NestedGridProperties<CT, L>, NestedGridControlObject<L, CT>>()
    /** Whether the rows of this nested grid can be selected or not */
    canSelect?: boolean;

    @ControlObjectProperty<NestedGridProperties<CT, L>, NestedGridControlObject<L, CT>>()
    /** Whether the rows of this nested grid can be filtered or not */
    canFilter?: boolean;

    /**
     * Indicate additional warning message, rendered as tooltip and blue border.
     */
    get infoMessage(): string | undefined {
        return resolveByValue({
            fieldValue: this.value,
            propertyValue: this.getUiComponentProperty('infoMessage'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /**
     * Indicate additional warning message, rendered as tooltip and blue border.
     */
    set infoMessage(infoMessage: string | undefined) {
        this.setUiComponentProperties('infoMessage', infoMessage);
    }

    /**
     * Indicate additional information, rendered as tooltip and orange border
     */
    get warningMessage(): string | undefined {
        return resolveByValue({
            fieldValue: this.value,
            propertyValue: this.getUiComponentProperty('warningMessage'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /**
     * Indicate additional information, rendered as tooltip and orange border
     */
    set warningMessage(warningMessage: string | undefined) {
        this.setUiComponentProperties('warningMessage', warningMessage);
    }

    /** Selected rows identifiers */
    get selectedRecords(): MappedSelected<L> {
        this.ensureNestedGridHasValue();
        const selectedRecords: MappedSelected<L> =
            this.getUiComponentProperty('selectedRecords') || this.defaultSelectedRecords();

        return selectedRecords.map(l => uniq(l)) as MappedSelected<L>;
    }

    /** Selected rows identifiers */
    set selectedRecords(rowIds: MappedSelected<L>) {
        this.ensureNestedGridHasValue();
        this.setUiComponentProperties('selectedRecords', rowIds.map(l => sortBy(uniq(l))) as MappedSelected<L>);
    }

    /** Selects a given row by its identifier*/
    selectRecord<N extends number>(recordId: NestedGridItemId, level: N): void {
        this.ensureNestedGridHasValue();
        const rowIds = get(this.getUiComponentProperty('selectedRecords'), level, []) as MappedSelected<L>[N];
        if (rowIds.indexOf(String(recordId)) === -1) {
            rowIds.push(String(recordId));
            const newSelectedRecords = set(
                this.getUiComponentProperty('selectedRecords') || this.defaultSelectedRecords(),
                level,
                uniq(sortBy(rowIds)),
            ) as MappedSelected<L>;
            this.setUiComponentProperties('selectedRecords', newSelectedRecords);
        }
    }

    async refresh(): Promise<void> {
        await PromiseTracker.withTracker(() =>
            this._refresh({ keepPageInfo: true }).catch(e => {
                showToast(e.message || e, { type: 'warning' });
            }),
        );
    }

    /** Unselects a given row by its identifier */
    unselectRecord<N extends number>(recordId: NestedGridItemId, level: N): void {
        this.ensureNestedGridHasValue();
        const rowIds = get(this.getUiComponentProperty('selectedRecords'), level, []) as MappedSelected<L>[N];
        const rowIdIndex = rowIds.indexOf(String(recordId));
        if (rowIdIndex !== -1) {
            rowIds.splice(rowIdIndex, 1);
            const newSelectedRecords = set(
                this.getUiComponentProperty('selectedRecords') || this.defaultSelectedRecords(),
                level,
                rowIds,
            ) as MappedSelected<L>;
            this.setUiComponentProperties('selectedRecords', newSelectedRecords);
        }
    }

    /** Unselects all items */
    unselectAllRecords(): void {
        this.ensureNestedGridHasValue();
        this.setUiComponentProperties('selectedRecords', this.defaultSelectedRecords());
    }

    /**
     * Return all data known to the client (it does not include rows which were not previously fetched by some
     * user interaction such as pagination) in normalized collection form.
     * */
    get normalizedValue(): PartialCollectionValueWithIds<L[0]>[] {
        const value = this._getValue();
        if (!value) {
            return [];
        }

        return value.getRawRecords();
    }

    /**
     * Return all data known to the client (it does not include rows which were not previously fetched by some
     * user interaction such as pagination) in a tree form.
     * */
    get value(): PartialCollectionValueWithIds<L[0]>[] {
        const value = this._getValue();
        if (!value) {
            return [];
        }

        return value.getAllDataAsTree({
            cleanInputTypes: false,
        });
    }

    /**
     * Resets the value of the nested grid, all pending changes will be overridden and all cached rows will be discarded
     * */
    set value(newValue: PartialCollectionValueWithIds<L[0]>[]) {
        if (!this.getUiComponentProperty('isTransient')) {
            xtremConsole.warn(
                'Reassigning nestedGrid values is discouraged. Please update individual rows using the setRecordValue(), addRecord(), removeRecord() functions.',
            );
        }
        this.setUiComponentProperties(
            'selectedRecords',
            objectKeys(this.getUiComponentProperty('levels')).map(
                () => [],
            ) as typeof this.uiComponentProperties.selectedRecords,
        );
        this.setFieldValue(newValue);
    }

    private validateLevel(level: number): void {
        const maxLevel = this.getUiComponentProperty('levels').length - 1;
        if (level < 0 || level > maxLevel) {
            throw new Error(`Invalid level '${level}' received. Please specify a value between 0 and ${maxLevel}.`);
        }
    }

    openSidebar(recordId?: string, level = 0): void {
        const sidebarDefinition = this.properties.levels[level].sidebar;
        const columns = this.properties.levels[level].columns;
        const cardDefinition = level === 0 ? this.properties.mobileCard : undefined;
        const dispatch = getStore().dispatch as AppThunkDispatch;
        dispatch(
            openTableSidebar({
                screenId: this.screenId,
                elementId: this.elementId,
                recordId,
                level,
                sidebarDefinition,
                cardDefinition,
                columns,
            }),
        );
    }

    /** Update the value of a single row in the collection */
    setRecordValue<N extends number>(recordValue: PartialCollectionValueWithIds<L[N]>, level: N): void {
        const value = this.ensureNestedGridHasValue();
        value.setRecordValue({ recordData: recordValue, level });
    }

    /** Return a single record that is already known to the client */
    getRecordValue<N extends number>(recordId: string, level: N): PartialCollectionValueWithIds<L[N]> | null {
        const value = this.ensureNestedGridHasValue();
        this.validateLevel(level);
        if (!value) {
            return null;
        }

        return value.getRecordWithChildren({
            id: recordId,
            level,
        }) as PartialCollectionValueWithIds<L[N]> | null;
    }

    /** Add a single row to the nested grid and in its dataset with default values coming from the server */
    async addRecordWithDefaults<N extends number>(
        level: N,
        parentId: N extends 0 ? undefined : string,
    ): Promise<PartialCollectionValue<L[N]>> {
        const value = this.ensureNestedGridHasValue();
        this.validateLevel(level);
        const recordValue = await fetchNestedDefaultValues({
            screenId: this.screenId,
            elementId: this.elementId,
            level,
        });

        return splitValueToMergedValue(
            value.addRecord({
                recordData: recordValue?.nestedDefaults ?? {},
                level,
                parentId,
            }),
        ) as PartialCollectionValue<L[N]>;
    }

    /** Add a single row to the nested grid and in its dataset */
    addRecord<N extends number>(
        recordValue: PartialCollectionValue<L[N]>,
        level: N,
        parentId: N extends 0 ? undefined : string,
    ): PartialCollectionValue<L[N]> {
        const value = this.ensureNestedGridHasValue();
        this.validateLevel(level);
        return splitValueToMergedValue(
            value.addRecord({
                recordData: recordValue as any,
                level,
                parentId,
            }),
        ) as PartialCollectionValue<L[N]>;
    }

    /** Remove a single row from the nested grid and its dataset */
    removeRecord(recordId: NestedGridItemId, level: number): void {
        const value = this.ensureNestedGridHasValue();
        this.validateLevel(level);
        const rowLevel = level === 0 ? undefined : level;
        value.removeRecord({ recordId, ...(rowLevel && { level: rowLevel }) });
    }

    /** Add or update row in the nested grid depending of the existence of the ID field */
    addOrUpdateRecordValue<N extends number>(
        recordData: PartialCollectionValue<L[N]>,
        level: N,
        parentId: N extends 0 ? undefined : string,
    ): PartialCollectionValue<L[N]> {
        const value = this.ensureNestedGridHasValue();
        this.validateLevel(level);
        return splitValueToMergedValue(
            value.addOrUpdateRecordValue({
                recordData,
                level,
                parentId,
            }),
        ) as PartialCollectionValueWithIds<L[N]>;
    }

    /** Add or update row in the nested grid (depending of the existence of its "_id" field and a given "level") */
    getRecordByFieldValue<N extends number, P extends keyof L[N]>(
        fieldName: P,
        fieldValue: L[N][P],
        level: N,
    ): PartialCollectionValue<L[N]> | null {
        const value = this.ensureNestedGridHasValue();
        this.validateLevel(level);
        return value.getRawRecordByFieldValue({ fieldName: String(fieldName), fieldValue, level });
    }

    /** Gets all records that have been added/updated/removed. */
    getChangedRecords(): PartialCollectionValue<L[0]>[] {
        const value = this.ensureNestedGridHasValue();
        return value.getChangedRecordsAsTree();
    }

    async refreshRecord<N extends number>(
        recordId: string,
        level: N,
        skipUpdate = false,
    ): Promise<PartialCollectionValueWithIds<L[N]>> {
        const value = this.ensureNestedGridHasValue();
        return value.refreshRecord({ recordId, level, skipUpdate });
    }

    /**
     * Fetches data for children record based on parent record id and child level. Default limit is 100. If the
     * openLevel attribute is set to true, the parent record is unfolded on the user interface and the children are
     * displayed.
     */
    async loadChildRecords<N extends number>({
        childLevel,
        parentRecordId,
        limit = 100,
        filter,
        openLevel = false,
    }: {
        childLevel: N;
        parentRecordId: string;
        limit?: number;
        filter?: GraphQLFilter<L[N]>;
        openLevel?: boolean;
    }): Promise<PartialCollectionValue<L[N]>[]> {
        const value = this.ensureNestedGridHasValue();
        const maxLevel = this.getUiComponentProperty('levels').length - 1;
        const parentLevel = childLevel - 1;

        if (childLevel < 1 || childLevel > maxLevel) {
            throw new Error(
                `Invalid level '${childLevel}' received. Please specify a value between 1 and ${maxLevel}.`,
            );
        }

        const parentRecord = value.getRawRecord({ id: parentRecordId, level: parentLevel });
        if (!parentRecord) {
            throw new Error(
                `Invalid record: could not find a record with ID ${parentRecordId} on level ${parentLevel}.`,
            );
        }

        const parentLevelProps = this.getUiComponentProperty('levels')[parentLevel];
        const levelProps = this.getUiComponentProperty('levels')[childLevel];
        const first = limit > 0 && limit <= 500 ? limit : 500;
        const queryArguments: QueryArguments = { first };

        if (filter) {
            queryArguments.filter = JSON.stringify(filter);
        }

        const data = (await value.fetchNestedGrid({
            level: childLevel,
            queryArguments,
            rootNode: String(parentLevelProps.node),
            selectedRecordId: parentRecordId,
            levelProps: levelProps as any,
            childProperty: String(parentLevelProps.childProperty!),
        })) as PartialCollectionValue<L[N]>[];

        if (openLevel) {
            const recordPath = value.getIdPathToNestedRecord(parentRecordId, parentLevel);
            getStore().dispatch({
                type: ActionType.OpenNestedGridLevel,
                value: {
                    elementId: this.elementId,
                    screenId: this.screenId,
                    recordPath,
                },
            });
        }

        return data;
    }

    redraw(): Promise<void> {
        return this._redraw();
    }
}
