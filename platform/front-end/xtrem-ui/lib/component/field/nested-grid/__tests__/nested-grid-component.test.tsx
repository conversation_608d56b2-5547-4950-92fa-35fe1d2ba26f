jest.mock('../async-desktop-nested-grid-component');

import { applyActionMocks, renderWithRedux, userEvent } from '../../../../__tests__/test-helpers';

import { cleanup, fireEvent, waitFor } from '@testing-library/react';
import { set } from 'lodash';
import * as React from 'react';
import { CollectionValue } from '../../../../service/collection-data-service';
import { nestedFields } from '../../../..';
import type { PageArticleItem } from '../../../../service/layout-types';
import * as events from '../../../../utils/events';
import type { NestedGridDecoratorProperties } from '../../../decorator-properties';
import type { FieldInternalValue, PartialCollectionValue } from '../../../types';
import { FieldKey } from '../../../types';
import { ConnectedNestedGridComponent } from '../nested-grid-component';
import type { Invoice, InvoiceLine, Order, Product } from './node-types';
import type { UnionType } from '../../../../utils/types';
import { getLevelMap } from '../nested-grid-utils';
import { destroyScreenCollections } from '../../../../service/loki';
import { ActionType } from '../../../../redux';
import { getSubscriptions } from '../../../../redux/middleware/action-subscription-middleware';
import { xtremConsole } from '../../../../utils/console';
import type { AccessBindings } from '../../../../service/page-definition';
import { GraphQLKind } from '../../../../types';
import '@testing-library/jest-dom';
import { COLUMN_ID_ROW_SELECTION } from '../../../../utils/ag-grid/ag-grid-column-config';

const { CollectionValue: ActualCollectionValue } = jest.requireActual('../../../../service/collection-data-service');

/**
 * It is IMPORTANT to declare this mock at the very beginning of the file.
 * Jest requires the following variable to start with the 'mock' prefix, hence 'mockCollectionValue' is used.
 * The following allows us to mock/spy only a single method of a the 'CollectionValue' class (i.e. 'setCellValue').
 * We should avoid calling 'jest.restoreAllMocks()' as this mock must not be restored.
 */
const setCellValueMock: jest.MockInstance<any, any> | null = jest
    .fn()
    .mockImplementation(({ columnId, value }: { columnId: string; value: any }) => ({ [columnId]: value }));
const fetchNestedGridMock: jest.MockInstance<any, any> | null = jest.fn().mockImplementation(() => {});

jest.mock('../../../../service/collection-data-service', () => {
    return {
        CollectionValue: jest.fn().mockImplementation((...args) => {
            const actual = new ActualCollectionValue(...args);
            const methods = Object.getOwnPropertyNames(Object.getPrototypeOf(actual));
            const originalMethods = methods.reduce((prev, curr) => {
                return { ...prev, [curr]: actual[curr].bind(actual) };
            }, {});
            return { ...originalMethods, setCellValue: setCellValueMock, fetchNestedGrid: fetchNestedGridMock };
        }),
        // https://github.com/kulshekhar/ts-jest/issues/281
        RecordActionType: {
            MODIFIED: 'update',
            ADDED: 'create',
            REMOVED: 'delete',
        },
    };
});

const screenId = 'TestPage';
const nestedGridElementId = 'nestedGridElementId';
const onEmptyStateLinkClick = jest.fn();
const defaultFieldProperties = (): NestedGridDecoratorProperties<any, [Order, Invoice, InvoiceLine]> => ({
    title: 'NestedGrid Test Title',
    levels: [
        {
            node: '@sage/xtrem-test/Order',
            childProperty: 'invoices',
            emptyStateText: 'We are in level 1',
            emptyStateClickableText: 'Click me',
            onEmptyStateLinkClick,
            columns: [
                nestedFields.text<any, Order>({ bind: '_id', title: 'Order ID' }),
                nestedFields.date<any, Order>({ bind: 'orderDate', title: 'Order Date', canFilter: true }),
            ],
        },
        {
            node: '@sage/xtrem-test/Invoice',
            childProperty: 'lines',
            emptyStateText: 'We are in level 2',
            emptyStateClickableText: 'Click me',
            onEmptyStateLinkClick,
            columns: [
                nestedFields.text<any, Invoice>({ bind: '_id', title: 'Invoice ID' }),
                nestedFields.date<any, Invoice>({ bind: 'invoiceDate', title: 'Invoice Date', canFilter: true }),
            ],
        },
        {
            node: '@sage/xtrem-test/InvoiceLine',
            emptyStateText: 'We are in level 3',
            emptyStateClickableText: 'Click me',
            onEmptyStateLinkClick,
            columns: [
                nestedFields.text<any, InvoiceLine>({ bind: '_id', title: 'Invoice Line ID' }),
                nestedFields.reference<any, InvoiceLine, Product>({
                    bind: 'product',
                    valueField: 'product',
                    node: '@sage/xtrem-test/Product',
                    title: 'Product',
                }),
            ],
        },
    ],
});

const defaultData: () => PartialCollectionValue<UnionType<[Order, Invoice, InvoiceLine]>>[] = () => [
    {
        _id: 'orderId1',
        orderDate: '2020-12-01',
    },
    {
        _id: 'orderId2',
        orderDate: '2020-12-02',
    },
];

const defaultFieldValue = (
    { levels }: NestedGridDecoratorProperties<any, [Order, Invoice, InvoiceLine]> = defaultFieldProperties(),
    data = defaultData(),
) => {
    const result: { data: PartialCollectionValue<Order | Invoice | InvoiceLine>[]; pageInfo: any } = {
        data,
        pageInfo: {
            startCursor: 'startCursor',
            endCursor: 'endCursor',
            hasPreviousPage: false,
            hasNextPage: false,
        },
    };

    return new CollectionValue<Order | Invoice | InvoiceLine>({
        columnDefinitions: levels.map(level => level.columns) as any,
        elementId: nestedGridElementId,
        filter: [undefined],
        hasNextPage: result.pageInfo.hasNextPage,
        initialValues: result.data,
        isTransient: false,
        levelMap: getLevelMap(levels),
        nodes: ['@sage/xtrem-test/Order', '@sage/xtrem-test/Invoice', '@sage/xtrem-test/InvoiceLine'],
        nodeTypes: {
            Order: {
                name: 'Order',
                title: 'Order',
                packageName: '@sage/xtrem-test',
                properties: {},
                mutations: {},
            },
            Invoice: {
                name: 'Invoice',
                title: 'Invoice',
                packageName: '@sage/xtrem-test',
                properties: {},
                mutations: {},
            },
            InvoiceLine: {
                name: 'InvoiceLine',
                title: 'InvoiceLine',
                packageName: '@sage/xtrem-test',
                properties: {},
                mutations: {},
            },
        },
        orderBy: [{ _id: 1 }],
        screenId,
        locale: 'en-US',
    });
};

interface SetupNestedGridParameters {
    fieldProperties?: NestedGridDecoratorProperties<any, [Order, Invoice, InvoiceLine]>;
    fieldValue?: FieldInternalValue<FieldKey.NestedGrid, Order | Invoice | InvoiceLine>;
    mockActions?: boolean;
    isParentDisabled?: boolean;
    accessBindings?: AccessBindings;
    data?: PartialCollectionValue<UnionType<[Order, Invoice, InvoiceLine]>>[];
}

const setup = ({
    fieldProperties = defaultFieldProperties(),
    data = defaultData(),
    fieldValue = defaultFieldValue(fieldProperties, data),
    mockActions = true,
    isParentDisabled = false,
    accessBindings = {},
}: SetupNestedGridParameters = {}) => {
    const item: PageArticleItem = { $bind: nestedGridElementId };
    const utils = renderWithRedux(
        <ConnectedNestedGridComponent
            screenId={screenId}
            item={item}
            elementId={nestedGridElementId}
            isParentDisabled={isParentDisabled}
        />,
        {
            initialState: {
                nodeTypes: {
                    Order: {
                        name: 'Order',
                        title: 'Order',
                        properties: {
                            _id: {
                                name: '_id',
                                type: 'IntOrString',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            orderDate: {
                                name: 'orderDate',
                                type: 'String',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            invoices: {
                                name: 'invoices',
                                type: 'Invoice',
                                kind: GraphQLKind.List,
                                isCollection: true,
                                isOnInputType: true,
                                canFilter: true,
                                isMutable: true,
                            },
                        },
                    },
                    Invoice: {
                        name: 'Invoice',
                        title: 'Invoice',
                        properties: {
                            _id: {
                                name: '_id',
                                type: 'IntOrString',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            lines: {
                                name: 'lines',
                                type: 'InvoiceLine',
                                kind: GraphQLKind.List,
                                isCollection: true,
                                isOnInputType: true,
                                canFilter: true,
                                isMutable: true,
                            },
                            invoiceDate: {
                                name: 'invoiceDate',
                                type: 'String',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                        },
                    },
                    InvoiceLine: {
                        name: 'InvoiceLine',
                        title: 'InvoiceLine',
                        properties: {
                            _id: {
                                name: '_id',
                                type: 'IntOrString',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            product: {
                                name: 'product',
                                type: 'Product',
                                kind: GraphQLKind.Object,
                                isOnInputType: true,
                                canFilter: true,
                                isMutable: true,
                            },
                        },
                    },
                    Product: {
                        name: 'Product',
                        title: 'Product',
                        properties: {
                            _id: {
                                name: '_id',
                                type: 'IntOrString',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            product: {
                                name: 'product',
                                type: 'String',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                        },
                    },
                },
            } as any,
            fieldType: FieldKey.NestedGrid,
            fieldValue,
            fieldProperties: fieldProperties as any,
            screenId,
            elementId: nestedGridElementId,
            mockActions,
            accessBindings,
        },
    );

    const getAllHeaders = () => Array.from(utils.container.querySelectorAll<HTMLSpanElement>('.ag-header-cell-text'));
    const getHeader = (columnIndex: number) => {
        return utils.container.querySelector(`.ag-header-cell-text[aria-colindex="${columnIndex + 1}"]`);
    };
    const getAllHeadersTextContent = () => getAllHeaders().map((h: HTMLSpanElement) => h.textContent);
    const getColumnByIndex = (columnIndex: number) =>
        Array.from(
            utils.container.querySelectorAll<HTMLDivElement>(
                `.ag-cell-value[aria-colindex="${columnIndex + 1}"][role="gridcell"]`,
            ),
        ).map((c: HTMLDivElement) => c.textContent);
    const getColumnById = (columnId: number) =>
        Array.from(
            utils.container.querySelectorAll<HTMLDivElement>(`.ag-cell-value[col-id="${columnId}"][role="gridcell"]`),
        ).map((c: HTMLDivElement) => c.textContent);

    const expectCellContent = (args: {
        rowIndex: number;
        columnIndex: number;
        expanded?: boolean;
        level?: number;
    }): { toBe: (expected: string) => Promise<void> } => {
        return {
            toBe: (e: string) => {
                return waitFor(() => {
                    expect(getCellContent(args)).toBe(e);
                });
            },
        };
    };

    const getCell = ({
        rowIndex,
        columnIndex,
        expanded,
        level,
    }: {
        rowIndex: number;
        columnIndex: number;
        expanded?: boolean;
        level?: number;
    }): Element | null => {
        const levelStr = level !== undefined && level > 0 ? `.ag-row-level-${level} ` : '';
        const expandedStr = expanded === true ? `[aria-expanded="${expanded}"]` : '';
        return utils.container.querySelector(
            `${levelStr}.ag-row[row-index="${rowIndex}"]${expandedStr} > .ag-cell-value[aria-colindex="${columnIndex + 1}"]`,
        );
    };

    const expandCell = (rowIndex: number, columnIndex: number): void => {
        const icon = utils.container.querySelector(
            `.ag-row[row-index="${rowIndex}"] > .ag-cell-value[aria-colindex="${columnIndex + 1}"] .ag-icon`,
        );
        if (!icon) {
            throw new Error('No icon found');
        }
        fireEvent.click(icon);
    };
    const getCellContent = (args: {
        rowIndex: number;
        columnIndex: number;
        expanded?: boolean;
        level?: number;
    }): string | null | undefined => (getCell(args)?.textContent ?? '').trim();

    const includesTestId = (selectedIds: string): Element | null => {
        return utils.getByTestId(dataTestId => {
            const selectedIdsList = selectedIds.split(' ');
            const ids = dataTestId.split(' ');
            return selectedIdsList.every(selectedId => ids.includes(selectedId));
        });
    };

    const clickCell = (rowIndex: number, columnIndex: number) => {
        const cell = getCell({ rowIndex, columnIndex });
        if (!cell) {
            return;
        }
        fireEvent.click(cell);
    };

    const selectCell = async (rowIndex: number) => {
        let cell;
        await waitFor(() => {
            cell = utils.container.querySelector(
                `.ag-row[row-index="${rowIndex}"] > .ag-cell[col-id="${COLUMN_ID_ROW_SELECTION}"] > div > div > div > div > .ag-checkbox-input`,
            );
            expect(cell).toBeInTheDocument();
        });
        fireEvent.click(cell);
    };

    const clickHeader = (columnIndex: number, options?: {}) => {
        const header = getHeader(columnIndex);
        if (!header) {
            return;
        }
        fireEvent.click(header, options);
    };
    const hitEnter = (input: HTMLInputElement) =>
        fireEvent.keyDown(input, {
            altKey: false,
            bubbles: true,
            cancelBubble: false,
            cancelable: true,
            charCode: 0,
            code: 'Enter',
            composed: true,
            ctrlKey: false,
            defaultPrevented: false,
            detail: 0,
            eventPhase: 3,
            isComposing: false,
            isTrusted: true,
            key: 'Enter',
            keyCode: 13,
            location: 0,
            metaKey: false,
            repeat: false,
            returnValue: true,
            shiftKey: false,
            which: 13,
        });

    const hitEsc = (input: HTMLInputElement) =>
        fireEvent.keyDown(input, {
            altKey: false,
            bubbles: true,
            cancelBubble: false,
            cancelable: true,
            charCode: 0,
            code: 'Esc',
            composed: true,
            ctrlKey: false,
            defaultPrevented: false,
            detail: 0,
            eventPhase: 3,
            isComposing: false,
            isTrusted: true,
            key: 'Esc',
            keyCode: 27,
            location: 0,
            metaKey: false,
            repeat: false,
            returnValue: true,
            shiftKey: false,
            which: 27,
        });

    const pickDate = async (rowIndex: number, columnIndex: number, day: number) => {
        const cell = getCell({ rowIndex, columnIndex });
        if (!cell) {
            return;
        }
        await userEvent.click(cell);
        await waitFor(() => {
            expect(document.querySelector('[data-floating-placement]')).toBeInTheDocument();
        });
        const datePicker = (utils.container.closest('body')! as HTMLBodyElement).querySelector('.rdp-root');
        const days = Array.from(datePicker!.querySelectorAll('.rdp-day_button'));
        const dayNode = days.find(d => d.textContent === String(day));
        if (dayNode) {
            fireEvent.click(dayNode);
            const editingCell = (await utils.findByTestId(
                `${nestedGridElementId}-${rowIndex}-${columnIndex + 1}`,
            )) as HTMLInputElement;
            hitEnter(editingCell);
        }
    };

    const checkBoxToggle = async (rowIndex: number, columnIndex: number) => {
        const checkbox = (await waitFor(() => {
            return utils.container.querySelector(
                `.ag-row[row-index="${rowIndex}"] > div[aria-colindex="${columnIndex + 1}"] > input[type="checkbox"]`,
            );
        })) as HTMLDivElement;
        fireEvent.click(checkbox);
    };

    const selectCellValue = async (rowIndex: number, columnIndex: number, value: string) => {
        const cell = getCell({ rowIndex, columnIndex });
        if (!cell) {
            return;
        }
        fireEvent.doubleClick(cell);
        const editingCell = (await waitFor(() => {
            return includesTestId(`${nestedGridElementId}-${rowIndex}-${columnIndex + 1}-input`);
        })) as HTMLInputElement;

        if (!editingCell) {
            xtremConsole.log(`No editing cell found for row ${rowIndex} and column ${columnIndex + 1}`);
            return;
        }
        await userEvent.type(editingCell, '{backspace}', { skipClick: true });
        const dropDownItem = utils
            .getAllByTestId('e-ui-select-suggestion-value')
            .find((h: HTMLElement) => h.textContent === value);
        if (!dropDownItem) {
            xtremConsole.log(`No dropdown item found matching value ${value}`);
            return;
        }
        fireEvent.click(dropDownItem);
    };
    const changeCell = async (rowIndex: number, columnIndex: number, value: string | number): Promise<void> => {
        const cell = getCell({ rowIndex, columnIndex });
        if (!cell) {
            return;
        }
        fireEvent.doubleClick(cell);
        let editingCell: HTMLInputElement;
        if (typeof value === 'string') {
            editingCell = (await waitFor(() => {
                return utils.container.querySelector(
                    `.ag-row[row-index="${rowIndex}"] > .ag-cell-inline-editing.ag-cell-value[aria-colindex="${
                        columnIndex + 1
                    }"] > .ag-cell-edit-wrapper > .ag-cell-editor > div > input`,
                );
            })) as HTMLInputElement;
        } else {
            editingCell = (await utils.findByTestId(
                `${nestedGridElementId}-${rowIndex}-${columnIndex + 1}`,
            )) as HTMLInputElement;
        }
        if (!editingCell) {
            xtremConsole.log(`No editing cell found for row ${rowIndex} and column ${columnIndex + 1}`);
            return;
        }
        await userEvent.type(editingCell, `{backspace}${value}`, { skipClick: true });
        await waitFor(() => expect(editingCell.value).toBe(String(value)));
        hitEnter(editingCell);
    };

    const changeReferenceByTyping = async (
        rowIndex: number,
        columnIndex: number,
        value: string,
        cancel = false,
    ): Promise<void> => {
        const cell = getCell({ rowIndex, columnIndex });
        if (!cell) {
            return;
        }
        fireEvent.doubleClick(cell);
        const editingCell = (await waitFor(() => {
            return includesTestId(`${nestedGridElementId}-${rowIndex}-${columnIndex + 1}-input`);
        })) as HTMLInputElement;

        if (!editingCell) {
            xtremConsole.log(`No editing cell found for row ${rowIndex} and column ${columnIndex + 1}`);
            return;
        }
        await userEvent.type(editingCell, `{backspace}${value}`, { skipClick: true });
        await waitFor(() => expect(editingCell.value).toBe(String(value)));
        if (!cancel) {
            hitEnter(editingCell);
            await waitFor(() => expect(getCell({ rowIndex, columnIndex })!.textContent).toBe(String(value)));
        } else {
            hitEsc(editingCell);
        }
    };

    const changeReferenceBySelectingFromDropdown = async (
        rowIndex: number,
        columnIndex: number,
        value: string,
    ): Promise<void> => {
        const cell = getCell({ rowIndex, columnIndex });
        if (!cell) {
            return;
        }
        fireEvent.doubleClick(cell);
        const editingCell = (await waitFor(() => {
            return includesTestId(`${nestedGridElementId}-${rowIndex}-${columnIndex + 1}-input`);
        })) as HTMLInputElement;

        if (!editingCell) {
            xtremConsole.log(`No editing cell found for row ${rowIndex} and column ${columnIndex + 1}`);
            return;
        }
        await userEvent.type(editingCell, `{backspace}${value}`, { skipClick: true });
        await waitFor(() => expect(editingCell.value).toBe(String(value)));
        const lastItem = await waitFor(() => {
            const dropdownItems = utils.getAllByTestId('e-ui-select-suggestion-value');
            const lastItem = dropdownItems[dropdownItems.length - 1];
            expect(lastItem!.textContent).toBe(String(value));
            return lastItem;
        });
        fireEvent.click(lastItem);
        await waitFor(() => expect(getCell({ rowIndex, columnIndex })!.textContent).toBe(String(value)));
    };

    const changeReferenceBySelectingFromLookupPanel = async (
        rowIndex: number,
        columnIndex: number,
        value: string,
    ): Promise<void> => {
        const cell = getCell({ rowIndex, columnIndex });
        if (!cell) {
            return;
        }
        fireEvent.doubleClick(cell);
        const editingCell = (await waitFor(() => {
            return includesTestId(`${nestedGridElementId}-${rowIndex}-${columnIndex + 1}-input`);
        })) as HTMLInputElement;

        if (!editingCell) {
            xtremConsole.log(`No editing cell found for row ${rowIndex} and column ${columnIndex + 1}`);
            return;
        }
        await waitFor(() => {
            expect(includesTestId('e-ui-select-lookup-button'));
        });
        fireEvent.click(includesTestId('e-ui-select-lookup-button') as any);
        await waitFor(() => {
            expect(document.querySelector('.carbon-portal')).toBeInTheDocument();
        });
        const lookupDialog = document.querySelector('.e-lookup-dialog');
        if (!lookupDialog) {
            xtremConsole.log('No lookup dialog found');
            return;
        }
        await waitFor(() => {
            expect(document.querySelector('.ag-cell-value')).toBeInTheDocument();
        });
        const secondItem = await waitFor(() => {
            const allCells = Array.from(lookupDialog.querySelectorAll('.ag-cell-value'));
            const secondDropdownItem = allCells.find(r => r.textContent === value);
            expect(secondDropdownItem).not.toBeUndefined();
            return secondDropdownItem;
        });
        if (!secondItem) {
            xtremConsole.log(`No lookup item found matching value ${value}`);
            return;
        }
        fireEvent.click(secondItem);
        await waitFor(() => expect(getCell({ rowIndex, columnIndex })!.textContent).toBe(String(value)));
    };

    const waitForFirstPaint = async (fieldProps = defaultFieldProperties(), count?: number) => {
        await waitFor(() => {
            const columnLength =
                count ||
                (fieldProps.canSelect ? fieldProps.levels[0].columns.length + 1 : fieldProps.levels[0].columns.length);
            const headers = getAllHeaders();
            expect(headers.length).toBe(columnLength);
        });
        if (count === undefined) {
            await waitFor(() => {
                expect(utils.getByText('01/12/2020')).toBeInTheDocument();
            });
        }

        await waitFor(() => {
            // ag-grid uses innerText so 'getByText' does NOT work...
            const colIndex = fieldProps.canSelect ? 2 : 1;
            const group = document.querySelector(
                `.ag-center-cols-viewport > .ag-center-cols-container > .ag-row[row-index="0"] > .ag-cell-value[aria-colindex="${
                    colIndex + 1
                }"] > .ag-cell-wrapper > .ag-group-value`,
            );
            if (!group) {
                throw new Error('Could not find group');
            }
            expect((group as HTMLSpanElement).textContent!.trim()).toBe('orderId1');
        });
    };
    return {
        ...utils,
        changeCell,
        checkBoxToggle,
        clickHeader,
        clickCell,
        getAllHeaders,
        getAllHeadersTextContent,
        includesTestId,
        getColumnByIndex,
        getColumnById,
        getCell,
        getCellContent,
        pickDate,
        changeReferenceByTyping,
        changeReferenceBySelectingFromDropdown,
        changeReferenceBySelectingFromLookupPanel,
        selectCellValue,
        waitForFirstPaint,
        selectCell,
        expectCellContent,
        expandCell,
    };
};

describe('Nested field - no actions mocked', () => {
    it('should be able to activate a row', async () => {
        const onRowActivatedMock = jest.fn();
        const onRowSelectedMock = jest.fn();
        const fieldProperties: NestedGridDecoratorProperties<any, [Order, Invoice, InvoiceLine]> = {
            ...defaultFieldProperties(),
            canActivate: true,
            onRowActivated: onRowActivatedMock,
            onRowSelected: onRowSelectedMock,
        };
        const utils = setup({ fieldProperties, mockActions: false });
        await utils.waitForFirstPaint();
        utils.clickCell(0, 1);
        await waitFor(() => {
            const expected = {
                _id: 'orderId1',
                orderDate: '2020-12-01',
            };
            expect(onRowSelectedMock).not.toHaveBeenCalled();
            expect(onRowActivatedMock).toHaveBeenCalledTimes(1);
            expect(onRowActivatedMock).toHaveBeenNthCalledWith(1, 'orderId1', expected, 0);
        });
    }, 30000);

    it('should be able to activate a row with "canActivate" and "canSelect" set to true', async () => {
        const onRowActivatedMock = jest.fn();
        const onRowSelectedMock = jest.fn();
        const fieldProperties: NestedGridDecoratorProperties<any, [Order, Invoice, InvoiceLine]> = {
            ...defaultFieldProperties(),
            canActivate: true,
            canSelect: true,
            onRowActivated: onRowActivatedMock,
            onRowSelected: onRowSelectedMock,
        };
        const utils = setup({ fieldProperties, mockActions: false });
        await utils.waitForFirstPaint();
        utils.clickCell(0, 1);
        await waitFor(() => {
            const expected = {
                _id: 'orderId1',
                orderDate: '2020-12-01',
            };
            expect(onRowSelectedMock).not.toHaveBeenCalled();
            expect(onRowActivatedMock).toHaveBeenCalledTimes(1);
            expect(onRowActivatedMock).toHaveBeenNthCalledWith(1, 'orderId1', expected, 0);
        });
    }, 40000);

    it('should be able to select a row', async () => {
        const onRowActivatedMock = jest.fn();
        const onRowSelectedMock = jest.fn();
        const fieldProperties: NestedGridDecoratorProperties<any, [Order, Invoice, InvoiceLine]> = {
            ...defaultFieldProperties(),
            canSelect: true,
            onRowActivated: onRowActivatedMock,
            onRowSelected: onRowSelectedMock,
        };
        const utils = setup({ fieldProperties, mockActions: false });
        await utils.waitForFirstPaint(fieldProperties);
        await utils.selectCell(0);
        await waitFor(() => {
            const expected = {
                _id: 'orderId1',
                orderDate: '2020-12-01',
            };
            expect(onRowActivatedMock).not.toHaveBeenCalled();
            expect(onRowSelectedMock).toHaveBeenCalledTimes(1);
            expect(onRowSelectedMock).toHaveBeenNthCalledWith(1, 'orderId1', expected, 0);
        });
    }, 40000);
});

describe('NestedGrid field', () => {
    beforeEach(() => {
        cleanup();
        destroyScreenCollections(screenId);
        applyActionMocks();
        jest.clearAllMocks();
    });

    it('should render nestedGrid component with data', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        expect(utils.includesTestId('e-nestedGrid-field')).not.toHaveClass('e-hidden');
        expect(utils.includesTestId('e-nestedGrid-field')).not.toHaveClass('e-disabled');
        expect(utils.getAllHeadersTextContent()).toEqual(['Order ID', 'Order Date']);
        expect(utils.getColumnByIndex(2)).toEqual(expect.arrayContaining(['01/12/2020', '02/12/2020']));
    });

    it('should render emptylink if table has no data', async () => {
        const utils = setup({ data: [] });
        await waitFor(
            () => {
                expect(utils.includesTestId('e-no-rows-found-component-button'));
                const emptyLinkButton = utils.container.querySelector('.e-no-rows-found-component-button button')!;
                fireEvent.click(emptyLinkButton);
                expect(onEmptyStateLinkClick).toHaveBeenCalledTimes(1);
                expect(onEmptyStateLinkClick).toHaveBeenCalledWith(undefined, 0);
            },
            { timeout: 5000 },
        );
    });
    it('should render emptylink if in nested levels', async () => {
        const fieldProperties = defaultFieldProperties();
        fieldProperties.levels.splice(2, 1);
        const fieldValue = defaultFieldValue(fieldProperties);
        const utils = setup({ fieldProperties, fieldValue });

        await utils.waitForFirstPaint();
        await waitFor(() => {
            expect(utils.container.querySelector('.ag-group-contracted')).not.toBe(null);
        });
        utils.expandCell(0, 1);
        await waitFor(() => {
            expect(utils.includesTestId('e-no-rows-found-component-button'));
            const emptyLinkButton = utils.container.querySelector('.e-no-rows-found-component-button button')!;
            fireEvent.click(emptyLinkButton);
            expect(onEmptyStateLinkClick).toHaveBeenCalledTimes(1);
            expect(onEmptyStateLinkClick).toHaveBeenCalledWith('orderId1', 1);
        });
    });

    it('should render disabled', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), isDisabled: true } });
        await utils.waitForFirstPaint();
        expect(utils.includesTestId('e-nestedGrid-field')).toHaveClass('e-disabled');

        // It should also make any editable cells non editable
        expect(utils.getCell({ rowIndex: 0, columnIndex: 1 })?.className).not.toContain('e-nested-cell-editable');
        expect(utils.getCell({ rowIndex: 1, columnIndex: 1 })?.className).not.toContain('e-nested-cell-editable');
    });

    it('should render disabled if parent field is disabled', async () => {
        const utils = setup({ isParentDisabled: true });
        await utils.waitForFirstPaint();
        expect(utils.includesTestId('e-nestedGrid-field')).toHaveClass('e-disabled');

        // It should also make any editable cells non editable
        expect(utils.getCell({ rowIndex: 0, columnIndex: 1 })?.className).not.toContain('e-nested-cell-editable');
        expect(utils.getCell({ rowIndex: 1, columnIndex: 1 })?.className).not.toContain('e-nested-cell-editable');
    });

    it('should render nestedGrid with 2 levels', async () => {
        const fieldProperties = defaultFieldProperties();
        fieldProperties.levels.splice(2, 1);
        const fieldValue = defaultFieldValue(fieldProperties);
        const utils = setup({ fieldProperties, fieldValue });
        fieldValue.addRecord({
            recordData: {
                _id: 'invoiceId11',
                invoiceDate: '2020-11-11',
            },
            parentId: 'orderId1',
            level: 1,
        });
        await utils.waitForFirstPaint();
        await utils.expectCellContent({ rowIndex: 0, columnIndex: 2, expanded: false }).toBe('01/12/2020');
        await utils.expectCellContent({ rowIndex: 1, columnIndex: 2, expanded: false }).toBe('02/12/2020');
        utils.expandCell(0, 1);
        await utils.expectCellContent({ rowIndex: 0, columnIndex: 2, expanded: true }).toBe('01/12/2020');
        await utils.expectCellContent({ rowIndex: 0, columnIndex: 2, expanded: false, level: 1 }).toBe('11/11/2020');
    });

    it('should render title', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), title: 'TEST TITLE' } });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('.e-field-title')).toHaveTextContent('TEST TITLE');
    });

    it('should render title by callback function', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), title: () => 'TEST TITLE' } });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('.e-field-title')).toHaveTextContent('TEST TITLE');
    });

    it('should render not render the title if it is not set', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), title: undefined } });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('.e-field-title')).toBeNull();
    });

    it('should not render not the title if the callback property returns a falsy value', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), title: () => '' } });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('.e-field-title')).toBeNull();
    });

    it('should not render not the title if the isHiddenTitle property is set to true', async () => {
        const utils = setup({
            fieldProperties: { ...defaultFieldProperties(), title: 'my test title', isTitleHidden: true },
        });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('.e-field-title')).toBeNull();
    });

    it('should render the component hidden', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), isHidden: true } });
        expect(utils.includesTestId('e-nestedGrid-field')).toHaveClass('e-hidden');
    });

    it('should render with an info message', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), infoMessage: 'Hi there!' } });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('[data-element="info"]')).not.toBeNull();
    });

    it('should render with an warning message', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), warningMessage: 'Hi there!' } });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
    });

    it('should prioritize warning over info message', async () => {
        const utils = setup({
            fieldProperties: { ...defaultFieldProperties(), infoMessage: 'Hi there!', warningMessage: 'Hi there!' },
        });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
        expect(utils.baseElement.querySelector('[data-element="info"]')).toBeNull();
    });

    // it('should render disabled', async () => {
    //     const utils = setup({ fieldProperties: { ...defaultFieldProperties, isDisabled: true } });
    //     expect(utils.getByTestId('e-nestedGrid-field')).toHaveClass('e-disabled');
    // });

    it('should trigger click event for text cell', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        utils.clickCell(0, 1);
        await waitFor(() => {
            expect(events.triggerFieldEvent).toHaveBeenCalledWith(
                screenId,
                nestedGridElementId,
                'onRowClick',
                'orderId1',
                {
                    _id: 'orderId1',
                    orderDate: '2020-12-01',
                },
                0,
            );

            // onCellClicked
            expect(events.triggerNestedFieldEvent).toHaveBeenCalledWith(
                screenId,
                nestedGridElementId,
                { bind: '_id', title: 'Order ID', _controlObjectType: 'Text' },
                'onClick',
                'orderId1',
                { _id: 'orderId1', orderDate: '2020-12-01' },
                0,
            );
        });
    });

    it('should not trigger click event for text cell if the nested grid is disabled', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), isDisabled: true } });
        await utils.waitForFirstPaint();
        utils.clickCell(0, 1);
        await waitFor(
            () => {
                expect(events.triggerNestedFieldEvent).not.toHaveBeenCalled();
            },
            { timeout: 500 },
        );
    });
    it("should not trigger click event for text cell if the nested grid's parent is disabled", async () => {
        const utils = setup({ isParentDisabled: true });
        await utils.waitForFirstPaint();
        utils.clickCell(0, 1);
        await waitFor(
            () => {
                expect(events.triggerNestedFieldEvent).not.toHaveBeenCalled();
            },
            { timeout: 500 },
        );
    });

    it('should render hidden column', async () => {
        const fieldProperties = set(defaultFieldProperties(), 'levels[0].columns[1].properties.isHidden', true);
        const utils = setup({ fieldProperties });
        await waitFor(() => {
            expect(utils.getAllHeadersTextContent()).not.toBe([]);
        });
        await waitFor(() => {
            const headers = utils.getAllHeadersTextContent();
            expect(headers.length).toEqual(1);
            expect(headers).toEqual(expect.not.arrayContaining(['Order Date']));
        });
    });

    it('should be able to change a cell triggering actions & event listeners', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        await utils.pickDate(0, 2, 17);
        await waitFor(() => {
            expect(setCellValueMock).toHaveBeenCalledWith({
                columnId: 'orderDate',
                level: undefined,
                recordId: 'orderId1',
                value: '2020-12-17',
                isOrganicChange: true,
            });
            // the first time "triggerEventListener" gets called when cell is double clicked
            expect(events.triggerFieldEvent).toHaveBeenCalledTimes(2);
        });

        const beforeChangeExpected = {
            _id: 'orderId1',
            orderDate: '2020-12-01',
        };

        const afterChangeExpected = {
            _id: 'orderId1',
            orderDate: '2020-12-17', // this is what changed
        };

        expect(events.triggerFieldEvent).toHaveBeenCalledTimes(2);
        expect((events.triggerFieldEvent as any).mock.calls).toEqual([
            [screenId, nestedGridElementId, 'onRowClick', 'orderId1', beforeChangeExpected, 0],
            [screenId, nestedGridElementId, 'onChange', 'orderDate', afterChangeExpected, 0],
        ]);
        await waitFor(() => {
            expect(utils.getCellContent({ rowIndex: 0, columnIndex: 2 })).toBe('17/12/2020');
        });
    });

    it('should expand child level on open nested grid action', async () => {
        const fieldProperties: NestedGridDecoratorProperties<any, [Order, Invoice, InvoiceLine]> = {
            ...defaultFieldProperties(),
        };
        const fieldValue = defaultFieldValue(fieldProperties);
        const getPageWithCurrentQueryArgumentsSpy = jest.spyOn(fieldValue, 'getPageWithCurrentQueryArguments');
        const utils = setup({ fieldValue, fieldProperties });
        await utils.waitForFirstPaint(fieldProperties);
        await waitFor(() => {
            expect(utils.container.querySelector('.ag-group-contracted')).not.toBe(null);
        });
        getSubscriptions().forEach(s =>
            s({
                type: ActionType.OpenNestedGridLevel,
                value: {
                    elementId: nestedGridElementId,
                    screenId,
                    recordPath: ['orderId1'],
                },
            }),
        );
        await waitFor(() => {
            expect(getPageWithCurrentQueryArgumentsSpy).toHaveBeenCalledTimes(1);
        });

        expect(getPageWithCurrentQueryArgumentsSpy).toHaveBeenCalledWith(
            expect.objectContaining({
                level: 1,
                pageNumber: 0,
                pageSize: 20,
                parentId: 'orderId1',
            }),
        );
    });

    describe('access rights', () => {
        describe('top level', () => {
            it('should render all columns on the level and not disable them if no rights are given', async () => {
                const utils = setup({
                    accessBindings: {
                        Order: {},
                        Invoice: {},
                        InvoiceLine: {},
                    },
                });
                await utils.waitForFirstPaint();
                expect(utils.getCell({ rowIndex: 0, columnIndex: 1 })).toHaveClass('e-nested-cell-editable');
                expect(utils.getCell({ rowIndex: 0, columnIndex: 2 })).toHaveClass('e-nested-cell-editable');
            });

            it('should render hide unavailable fields', async () => {
                const utils = setup({
                    accessBindings: {
                        Order: {
                            orderDate: 'unavailable',
                        },
                        Invoice: {},
                        InvoiceLine: {},
                    },
                });

                await utils.waitForFirstPaint(defaultFieldProperties(), 1);
                expect(utils.getCell({ rowIndex: 0, columnIndex: 1 })).toHaveClass('e-nested-cell-editable');
            });
            it('should render disable unauthorized fields', async () => {
                const utils = setup({
                    accessBindings: {
                        Order: {
                            orderDate: 'unauthorized',
                            _id: 'unauthorized',
                        },
                        '@sage/xtrem-test/Invoice': {},
                        '@sage/xtrem-test/InvoiceLine': {},
                    },
                });

                await utils.waitForFirstPaint(defaultFieldProperties());
                expect(utils.getCell({ rowIndex: 0, columnIndex: 1 })).not.toHaveClass('e-nested-cell-editable');
                expect(utils.getCell({ rowIndex: 0, columnIndex: 2 })).not.toHaveClass('e-nested-cell-editable');
            });
        });
    });
});
