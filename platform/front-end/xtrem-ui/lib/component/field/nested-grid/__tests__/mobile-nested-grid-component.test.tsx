import React from 'react';
import { Provider } from 'react-redux';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MobileNestedGridComponent } from '../mobile-nested-grid-component';
import type { MobileNestedGridComponentProps } from '../nested-grid-types';
import { FieldKey, TableDisplayMode } from '../../../types';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';

describe('MobileNestedGridComponent', () => {
    let mockProps: MobileNestedGridComponentProps;
    let store;

    beforeEach(() => {
        const state = getMockState();
        state.screenDefinitions['test-screen'] = getMockPageDefinition('test-screen');
        const fieldProperties = {
            title: 'Orders',
            bind: 'orders',
            levels: [
                {
                    node: '@sage/xtrem-show-case/ShowCaseOrder',
                    childProperty: 'invoices',
                    columns: [
                        {
                            defaultUiProperties: {} as any,
                            properties: {
                                bind: '_id',
                                title: 'Id',
                                isHiddenDesktop: false,
                                isReadOnly: true,
                                _controlObjectType: FieldKey.Text,
                            },
                            type: FieldKey.Text,
                        },
                        {
                            defaultUiProperties: {} as any,
                            properties: {
                                bind: 'orderDate',
                                title: 'Order Date',
                                canFilter: true,
                                _controlObjectType: FieldKey.Date,
                            },
                            type: FieldKey.Date,
                        },
                    ],
                    emptyStateText: 'We are in level 1',
                    emptyStateClickableText: 'Click me',
                    dropdownActions: [
                        {
                            icon: 'add',
                            title: 'Random Action',
                        },
                        {
                            title: 'Refresh record',
                            icon: 'refresh',
                        },
                    ],
                },
                {
                    node: '@sage/xtrem-show-case/ShowCaseInvoice',
                    childProperty: 'lines',
                    columns: [
                        {
                            defaultUiProperties: {} as any,
                            properties: {
                                bind: '_id',
                                title: 'Id',
                                isHiddenDesktop: false,
                                isReadOnly: true,
                                _controlObjectType: FieldKey.Text,
                            },
                            type: FieldKey.Text,
                        },
                        {
                            defaultUiProperties: {} as any,
                            properties: {
                                bind: 'totalProductQty',
                                title: 'Total Product Quantity',
                                canFilter: true,
                                _controlObjectType: FieldKey.Numeric,
                            },
                            type: FieldKey.Numeric,
                        },
                        {
                            defaultUiProperties: {} as any,
                            properties: {
                                bind: 'purchaseDate',
                                title: 'Purchase Date',
                                canFilter: true,
                                _controlObjectType: FieldKey.Date,
                            },
                            type: FieldKey.Date,
                        },
                    ],
                    emptyStateText: 'We are in level 2',
                    emptyStateClickableText: 'Click me',
                    dropdownActions: [
                        {
                            icon: 'add',
                            title: 'Row Action',
                        },
                        {
                            icon: 'none',
                            title: 'Row Action 2',
                        },
                    ],
                },
                {
                    node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
                    columns: [
                        {
                            properties: {
                                bind: '_id',
                                title: 'Id',
                                isHiddenDesktop: false,
                                isReadOnly: true,
                                _controlObjectType: 'Text',
                            },
                            type: 'Text',
                        },
                        {
                            properties: {
                                bind: 'orderQuantity',
                                title: 'Quantity',
                                scale: 0,
                                canFilter: true,
                                _controlObjectType: 'Numeric',
                            },
                            type: 'Numeric',
                        },
                        {
                            properties: {
                                bind: 'netPrice',
                                title: 'Net Price',
                                scale: 2,
                                canFilter: true,
                                _controlObjectType: 'Numeric',
                            },
                            type: 'Numeric',
                        },
                        {
                            properties: {
                                isFilterLimitedToDataset: true,
                                bind: 'product',
                                node: '@sage/xtrem-show-case/ShowCaseProduct',
                                valueField: {
                                    product: true,
                                },
                                helperTextField: {
                                    _id: true,
                                },
                                title: 'Product',
                                canFilter: true,
                                _controlObjectType: 'Reference',
                            },
                            type: 'Reference',
                        },
                        {
                            properties: {
                                bind: 'priceDiscount',
                                title: 'Price Discount',
                                scale: 2,
                                _controlObjectType: 'Numeric',
                            },
                            type: 'Numeric',
                        },
                    ],
                    emptyStateText: 'We are in level 3',
                    emptyStateClickableText: 'Click me',
                    dropdownActions: [
                        {
                            icon: 'add',
                            title: 'Random Action',
                        },
                        {
                            title: 'Refresh record',
                            icon: 'refresh',
                        },
                    ],
                },
            ] as any,
            isHiddenDesktop: false,
            isHiddenMobile: false,
            isTitleHidden: false,
            isTransient: false,
            isFullWidth: false,
            isHelperTextHidden: false,
            isHidden: false,
            canFilter: true,
            canSelect: true,
            canUserHideColumns: true,
            displayMode: TableDisplayMode.comfortable,
            pageSize: 20,
            _controlObjectType: FieldKey.NestedGrid,
        };
        addFieldToState(FieldKey.NestedGrid, state, 'test-screen', 'test-element', fieldProperties, null);
        store = getMockStore(state);
        mockProps = {
            elementId: 'test-element',
            fieldProperties,
            isDisabled: false,
            openSidebar: jest.fn(),
            screenId: 'test-screen',
            value: {
                getPageWithCurrentQueryArguments: jest.fn().mockResolvedValue([
                    {
                        __cursor: '[12]#36',
                        _id: '12',
                        orderDate: '2021-01-01',
                        orderType: null,
                        __dirty: false,
                        __dirtyColumns: {},
                        __compositeKey: '12.0',
                        __path: '0,12',
                    },
                ]),
                subscribeForValueChanges: jest.fn(),
            } as any,
            validationErrors: [],
            canAddNewLine: true,
        };
    });
    it('should render without crashing', () => {
        render(
            <Provider store={store}>
                <MobileNestedGridComponent {...mockProps} />
            </Provider>,
        );
        expect(screen.getByTestId('e-mobile-nested-grid-component')).toBeInTheDocument();
        expect(screen.getByTestId('e-mobile-nested-grid-header')).toBeInTheDocument();
    });

    it('should display the title when currentLevel is 0', () => {
        render(
            <Provider store={store}>
                <MobileNestedGridComponent {...mockProps} />
            </Provider>,
        );
        expect(screen.getByTestId('e-mobile-nested-grid-header-title')).toHaveTextContent('Orders');
        expect(screen.getByTestId('e-mobile-grid-header-level').textContent).toBe('1/3');
    });

    it('should call fetchRows on mount', async () => {
        render(
            <Provider store={store}>
                <MobileNestedGridComponent {...mockProps} />
            </Provider>,
        );
        await waitFor(() => expect(mockProps.value.getPageWithCurrentQueryArguments).toHaveBeenCalled());
    });

    it('should handle row click', async () => {
        const mockOnRowClick = jest.fn();
        const propsWithRowClick = { ...mockProps, onRowClick: mockOnRowClick };
        render(
            <Provider store={store}>
                <MobileNestedGridComponent {...propsWithRowClick} />
            </Provider>,
        );

        // Wait for the initial fetch call to complete
        await waitFor(() => {
            expect(mockProps.value.getPageWithCurrentQueryArguments).toHaveBeenCalled();
            expect(screen.queryByTestId('e-is-loading-more-button')).toBeNull();
        });

        expect(mockProps.value.getPageWithCurrentQueryArguments).toHaveBeenCalled();
        fireEvent.click(screen.getByTestId('e-card-row-chevron-right'));
        expect(mockOnRowClick).toHaveBeenCalled();
        expect(screen.getByTestId('e-mobile-grid-header-level').textContent).toBe('2/3');
        expect(screen.getByTestId('e-mobile-nested-grid-header-child-title').textContent).toBe('12');
    });

    it('should handle back button click', async () => {
        render(
            <Provider store={store}>
                <MobileNestedGridComponent {...mockProps} />
            </Provider>,
        );

        // Wait for the initial fetch call to complete
        await waitFor(() => {
            expect(mockProps.value.getPageWithCurrentQueryArguments).toHaveBeenCalled();
            expect(screen.queryByTestId('e-is-loading-more-button')).toBeNull();
        });

        // Perform the first click and check the assertions
        fireEvent.click(screen.getByTestId('e-card-row-chevron-right'));
        expect(screen.getByTestId('e-mobile-nested-grid-header-child-title').textContent).toBe('12');
        expect(screen.getByTestId('e-mobile-grid-header-level').textContent).toBe('2/3');
        expect(screen.getByTestId('e-mobile-nested-grid-header-child-title').textContent).toBe('12');
        expect(screen.queryByTestId('e-mobile-nested-grid-header-title')).toBeNull();

        // Perform the back button click and check the assertions
        fireEvent.click(screen.getByTestId('e-mobile-nested-grid-header-back-button'));
        expect(screen.queryByTestId('e-mobile-nested-grid-header-child-title')).not.toBeInTheDocument();
        expect(screen.getByTestId('e-mobile-grid-header-level').textContent).toBe('1/3');
        expect(screen.getByTestId('e-mobile-nested-grid-header-title')).toHaveTextContent('Orders');
    });
});
