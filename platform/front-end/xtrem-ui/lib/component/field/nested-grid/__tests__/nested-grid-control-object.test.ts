import type { Page } from '../../../..';
import { nestedFields } from '../../../..';
import { createFieldControlObject, getMockPageDefinition } from '../../../../__tests__/test-helpers';
import { CollectionValue } from '../../../../service/collection-data-service';
import { CollectionFieldTypes, RecordActionType } from '../../../../service/collection-data-types';
import * as graphQLService from '../../../../service/graphql-service';
import { GraphQLKind } from '../../../../types';
import { xtremConsole } from '../../../../utils/console';
import * as stateUtils from '../../../../utils/state-utils';
import { NestedGrid as NestedGridControlObject } from '../../../control-objects';
import type { NestedGridDecoratorProperties } from '../../../decorators';
import type { NestedField, NestedFieldTypes } from '../../../nested-fields';
import type { FieldInternalValue, PartialCollectionValue } from '../../../types';
import { FieldKey, GraphQLTypes } from '../../../types';
import type { MappedSelected } from '../nested-grid-component-types';
import { getLevelMap } from '../nested-grid-utils';
import type { Invoice, InvoiceLine, Order } from './node-types';

jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() => getMockPageDefinition('TestPage'));
const nodeTypes = {
    Order: {
        title: 'Order',
        name: 'Order',
        packageName: '@sage/xtrem-test',
        properties: {
            _id: {
                type: GraphQLTypes.IntOrString,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
            _sortValue: {
                type: GraphQLTypes.IntOrString,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
            _action: {
                type: 'SystemProperties_EnumInput',
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
            orderDate: { type: GraphQLTypes.Date, kind: GraphQLKind.Scalar, isOnInputType: true },
            invoices: {
                type: 'Invoice',
                kind: GraphQLKind.List,
                isCollection: true,
                isOnInputType: true,
                canFilter: true,
                isMutable: true,
            },
        },
        mutations: {},
    },
    Invoice: {
        title: 'Invoice',
        name: 'Invoice',
        packageName: '@sage/xtrem-test',
        properties: {
            _id: {
                type: GraphQLTypes.IntOrString,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
            _sortValue: {
                type: GraphQLTypes.IntOrString,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
            _action: {
                type: GraphQLTypes.Enum,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
            invoiceDate: {
                type: GraphQLTypes.Date,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
            lines: {
                type: 'InvoiceLine',
                kind: GraphQLKind.List,
                isCollection: true,
                isMutable: true,
                isOnInputType: true,
                canFilter: true,
            },
        },
        mutations: {},
    },
    InvoiceLine: {
        title: 'InvoiceLine',
        name: 'InvoiceLine',
        packageName: '@sage/xtrem-test',
        properties: {
            _id: {
                type: GraphQLTypes.IntOrString,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
            _sortValue: {
                type: GraphQLTypes.IntOrString,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
            _action: {
                type: GraphQLTypes.Enum,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
            product: {
                type: 'Product',
                kind: GraphQLKind.Object,
                isOnInputType: true,
                canFilter: true,
                isMutable: true,
            },
        },
        mutations: {},
    },
    Product: {
        title: 'Product',
        name: 'Product',
        packageName: '@sage/xtrem-test',
        properties: {
            _id: {
                type: 'IntReference',
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
            _sortValue: {
                type: GraphQLTypes.IntOrString,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
            product: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isOnInputType: true,
                canFilter: true,
            },
        },
        mutations: {},
    },
};

describe('NestedGrid Field', () => {
    const screenId = 'TestPage';
    const fieldId = 'test';
    const nestedGridValue = [{ _id: '111', orderDate: '2020-11-11', invoices: [] }];
    let value: FieldInternalValue<FieldKey.NestedGrid, Order>;
    let nestedGridFieldControlObject: NestedGridControlObject<[Order, Invoice, InvoiceLine], any>;
    const levelsMock: () => NestedGridDecoratorProperties<any, [Order, Invoice, InvoiceLine]>['levels'] = () => [
        {
            node: 'Order',
            columns: [],
            childProperty: 'invoices',
        },
        {
            node: 'Invoice',
            columns: [],
            childProperty: 'lines',
        },
        {
            node: 'InvoiceLine',
            columns: [],
        },
    ];

    const properties: NestedGridDecoratorProperties<any, [Order, Invoice, InvoiceLine]> = {
        title: 'TEST_FIELD_TITLE',
        isHidden: true,
        isDisabled: true,
        levels: levelsMock(),
    };

    describe('value get/set without mocks', () => {
        let currentValue: CollectionValue<Order>;

        beforeEach(() => {
            currentValue = new CollectionValue<Order>({
                columnDefinitions: [
                    [nestedFields.text<any, any>({ bind: '_id' }), nestedFields.date<any, any>({ bind: 'orderDate' })],
                    [
                        nestedFields.text<any, any>({ bind: '_id' }),
                        nestedFields.date<any, any>({ bind: 'invoiceDate' }),
                    ],
                    [nestedFields.text<any, any>({ bind: '_id' })],
                ] as any,
                elementId: fieldId,
                fieldType: CollectionFieldTypes.NESTED_GRID,
                filter: [undefined],
                hasNextPage: false,
                initialValues: nestedGridValue,
                isTransient: false,
                levelMap: getLevelMap(levelsMock()),
                nodes: ['@sage/xtrem-test/Order', '@sage/xtrem-test/Invoice', '@sage/xtrem-test/InvoiceLine'],
                nodeTypes,
                orderBy: [{}],
                screenId,
            });

            nestedGridFieldControlObject = createFieldControlObject<FieldKey.NestedGrid>(
                FieldKey.NestedGrid,
                screenId,
                NestedGridControlObject,
                fieldId,
                currentValue,
                properties as any,
                {
                    setValue: (_, __, v) => {
                        currentValue = v!;
                    },
                    getValue: () => currentValue,
                },
            ) as any;
        });

        it('should be able to reset a value by assigning an emtpty array', () => {
            nestedGridFieldControlObject.value = [];
            expect(
                currentValue.getAllDataAsTree({
                    cleanInputTypes: false,
                }),
            ).toStrictEqual([]);
        });

        it('should reset selected records when value is reassigned', () => {
            nestedGridFieldControlObject.selectRecord('111', 0);
            expect((nestedGridFieldControlObject as any).getUiComponentProperty('selectedRecords')).toStrictEqual([
                ['111'],
                [],
                [],
            ]);
            nestedGridFieldControlObject.value = [
                {
                    _id: '111',
                    orderDate: '2020-11-11',
                    invoices: [],
                },
            ];
            expect((nestedGridFieldControlObject as any).getUiComponentProperty('selectedRecords')).toStrictEqual([
                [],
                [],
                [],
            ]);
        });
    });

    describe('properties and values', () => {
        beforeEach(() => {
            value = new CollectionValue<Order>({
                columnDefinitions: [
                    [nestedFields.text<any, any>({ bind: '_id' }), nestedFields.date<any, any>({ bind: 'orderDate' })],
                    [
                        nestedFields.text<any, any>({ bind: '_id' }),
                        nestedFields.date<any, any>({ bind: 'invoiceDate' }),
                    ],
                    [nestedFields.text<any, any>({ bind: '_id' })],
                ] as any,
                elementId: fieldId,
                fieldType: CollectionFieldTypes.NESTED_GRID,
                filter: [undefined],
                hasNextPage: false,
                initialValues: nestedGridValue,
                isTransient: false,
                levelMap: getLevelMap(levelsMock()),
                nodes: ['@sage/xtrem-test/Order', '@sage/xtrem-test/Invoice', '@sage/xtrem-test/InvoiceLine'],
                nodeTypes,
                orderBy: [{}],
                screenId,
            });

            nestedGridFieldControlObject = createFieldControlObject<FieldKey.NestedGrid>(
                FieldKey.NestedGrid,
                screenId,
                NestedGridControlObject,
                fieldId,
                value,
                properties as any,
            ) as any;
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('getting field value', () => {
            expect(nestedGridFieldControlObject.value).toEqual(nestedGridValue);
            expect(nestedGridFieldControlObject.normalizedValue).toEqual([{ _id: '111', orderDate: '2020-11-11' }]);
        });

        describe('setting and getting updated properties', () => {
            const testNestedGridProps = (
                nestedGridProp: string,
                nestedGridPropValue:
                    | string
                    | boolean
                    | Array<NestedField<Page, NestedFieldTypes> | string>
                    | MappedSelected,
            ) => {
                expect(nestedGridFieldControlObject[nestedGridProp]).not.toEqual(nestedGridPropValue);
                nestedGridFieldControlObject[nestedGridProp] = nestedGridPropValue;
                expect(nestedGridFieldControlObject[nestedGridProp]).toEqual(nestedGridPropValue);
            };

            it('should set the title', () => {
                testNestedGridProps('title', 'Test Title');
            });

            it('should set nested field and its properties', () => {
                const testFixture = [
                    nestedFields.date<any, Order>({
                        bind: 'orderDate',
                        isHiddenMobile: true,
                        isHiddenDesktop: true,
                    }),
                ];
                testNestedGridProps('columns', testFixture);
            });

            it('should set the canFilter columns option', () => {
                testNestedGridProps('canFilter', true);
            });

            it('should set the canSelect rows option', () => {
                testNestedGridProps('canSelect', true);
            });

            it('should set the selected items', () => {
                testNestedGridProps('selectedRecords', [['1', '2', '3'], []]);
            });

            it('should set the selected items uniquely', () => {
                nestedGridFieldControlObject.selectedRecords = [['1', '2', '1'], ['4', '3', '3'], []];
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([['1', '2'], ['3', '4'], []]);
            });

            it('should set the canUserHideColumns columns option', () => {
                testNestedGridProps('canUserHideColumns', false);
            });

            it('should select an item', () => {
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([[], [], []]);
                nestedGridFieldControlObject.selectRecord('4', 0);
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([['4'], [], []]);
                nestedGridFieldControlObject.selectRecord('5', 0);
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([['4', '5'], [], []]);
                nestedGridFieldControlObject.selectRecord('5', 0);
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([['4', '5'], [], []]);
            });

            it('should select items uniquely', () => {
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([[], [], []]);
                nestedGridFieldControlObject.selectRecord('4', 0);
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([['4'], [], []]);
                nestedGridFieldControlObject.selectRecord('4', 0);
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([['4'], [], []]);
            });

            it('should unselect an item', () => {
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([[], [], []]);
                nestedGridFieldControlObject.unselectRecord('3', 0);
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([[], [], []]);
                nestedGridFieldControlObject.selectedRecords = [['2', '3', '4'], [], []];
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([['2', '3', '4'], [], []]);
                nestedGridFieldControlObject.unselectRecord('3', 0);
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([['2', '4'], [], []]);
                nestedGridFieldControlObject.unselectRecord('3', 0);
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([['2', '4'], [], []]);
                nestedGridFieldControlObject.unselectRecord('2', 0);
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([['4'], [], []]);
            });

            it('should unselect all items', () => {
                nestedGridFieldControlObject.selectedRecords = [['2', '3', '4'], [], []];
                nestedGridFieldControlObject.unselectAllRecords();
                expect(nestedGridFieldControlObject.selectedRecords).toEqual([[], [], []]);
            });
        });

        describe('get and set value of the nestedGrid', () => {
            beforeEach(() => {
                value = new CollectionValue<Order>({
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.date<any, any>({ bind: 'orderDate' }),
                        ],
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.date<any, any>({ bind: 'invoiceDate' }),
                        ],
                        [nestedFields.text<any, any>({ bind: '_id' })],
                    ] as any,
                    elementId: fieldId,
                    fieldType: CollectionFieldTypes.NESTED_GRID,
                    filter: [undefined],
                    hasNextPage: true,
                    initialValues: [
                        { _id: '1', orderDate: '2020-11-11', invoices: [] },
                        { _id: '2', orderDate: '2020-11-30', invoices: [] },
                        { _id: '3', orderDate: '2020-12-24', invoices: [] },
                    ],
                    isTransient: false,
                    levelMap: getLevelMap(levelsMock()),
                    nodes: ['@sage/xtrem-test/Order', '@sage/xtrem-test/Invoice', '@sage/xtrem-test/InvoiceLine'],
                    nodeTypes,
                    orderBy: [{ anyField: 1 }],
                    screenId,
                });

                nestedGridFieldControlObject = createFieldControlObject<FieldKey.NestedGrid>(
                    FieldKey.NestedGrid,
                    screenId,
                    NestedGridControlObject,
                    fieldId,
                    value,
                    properties as any,
                ) as any;
            });

            afterEach(() => {
                jest.clearAllMocks();
            });

            it('should be able to add a row with nested levels', () => {
                const newOrder = nestedGridFieldControlObject.addRecord<0>(
                    {
                        orderDate: '2021-01-13',
                        invoices: [
                            { invoiceDate: '2021-01-15', lines: [{ product: { product: 'new product' } }] },
                            {
                                invoiceDate: '2021-01-17',
                                lines: [
                                    { product: { product: 'new product' } },
                                    { product: { product: 'new product 2' } },
                                ],
                            },
                        ],
                    },
                    0,
                    undefined,
                )!;
                expect(newOrder).toStrictEqual({
                    _id: '-1',
                    orderDate: '2021-01-13',
                    invoices: [
                        {
                            _id: '-2',
                            invoiceDate: '2021-01-15',
                            lines: [
                                {
                                    _id: '-3',
                                    product: {
                                        product: 'new product',
                                    },
                                },
                            ],
                        },
                        {
                            _id: '-4',
                            invoiceDate: '2021-01-17',
                            lines: [
                                {
                                    _id: '-5',
                                    product: {
                                        product: 'new product',
                                    },
                                },
                                {
                                    _id: '-6',
                                    product: {
                                        product: 'new product 2',
                                    },
                                },
                            ],
                        },
                    ],
                });
                expect(nestedGridFieldControlObject.getRecordValue<0>('-1', 0)).toStrictEqual(newOrder);
                expect(nestedGridFieldControlObject.getRecordValue<1>('-2', 1)).toStrictEqual({
                    _id: '-2',
                    invoiceDate: '2021-01-15',
                    lines: [
                        {
                            _id: '-3',
                            product: {
                                product: 'new product',
                            },
                        },
                    ],
                });
                expect(nestedGridFieldControlObject.getRecordValue<2>('-3', 2)).toStrictEqual({
                    _id: '-3',
                    product: {
                        product: 'new product',
                    },
                });
            });

            it('should be able to assign a value with nested levels', () => {
                const newOrder = (nestedGridFieldControlObject.value = [
                    {
                        _id: '1',
                        orderDate: '2021-01-13',
                        invoices: [
                            {
                                _id: '2',
                                invoiceDate: '2021-01-15',
                                lines: [{ _id: '3', product: { product: 'new product' } }],
                            },
                            {
                                _id: '4',
                                invoiceDate: '2021-01-17',
                                lines: [
                                    { _id: '5', product: { product: 'new product' } },
                                    { _id: '6', product: { product: 'new product 2' } },
                                ],
                            },
                        ],
                    },
                    {
                        _id: '7',
                        orderDate: '2021-02-13',
                        invoices: [
                            { _id: '8', invoiceDate: '2021-02-15' },
                            {
                                _id: '9',
                                invoiceDate: '2021-02-17',
                                lines: [{ _id: '10', product: { product: 'new product' } }],
                            },
                        ],
                    },
                    {
                        _id: '11',
                        orderDate: '2021-03-13',
                    },
                ]);
                expect(newOrder).toStrictEqual([
                    {
                        _id: '1',
                        invoices: [
                            {
                                _id: '2',
                                invoiceDate: '2021-01-15',
                                lines: [{ _id: '3', product: { product: 'new product' } }],
                            },
                            {
                                _id: '4',
                                invoiceDate: '2021-01-17',
                                lines: [
                                    { _id: '5', product: { product: 'new product' } },
                                    { _id: '6', product: { product: 'new product 2' } },
                                ],
                            },
                        ],
                        orderDate: '2021-01-13',
                    },
                    {
                        _id: '7',
                        invoices: [
                            { _id: '8', invoiceDate: '2021-02-15' },
                            {
                                _id: '9',
                                invoiceDate: '2021-02-17',
                                lines: [{ _id: '10', product: { product: 'new product' } }],
                            },
                        ],
                        orderDate: '2021-02-13',
                    },
                    { _id: '11', orderDate: '2021-03-13' },
                ]);
            });

            it('should be able to add a row with nested levels', () => {
                nestedGridFieldControlObject.addRecord<0>(
                    {
                        orderDate: '2021-01-13',
                        invoices: [
                            { invoiceDate: '2021-01-15', lines: [{ product: { product: 'new product' } }] },
                            {
                                invoiceDate: '2021-01-17',
                                lines: [
                                    { product: { product: 'new product' } },
                                    { product: { product: 'new product 2' } },
                                ],
                            },
                        ],
                    },
                    0,
                    undefined,
                )!;
                const expected = {
                    _id: '-1',
                    orderDate: '2021-01-13',
                    invoices: [
                        {
                            _id: '-2',
                            invoiceDate: '2021-01-15',
                            lines: [
                                {
                                    _id: '-3',
                                    product: {
                                        product: 'new product',
                                    },
                                },
                            ],
                        },
                        {
                            _id: '-4',
                            invoiceDate: '2021-01-17',
                            lines: [
                                {
                                    _id: '-5',
                                    product: {
                                        product: 'new product',
                                    },
                                },
                                {
                                    _id: '-6',
                                    product: {
                                        product: 'new product 2',
                                    },
                                },
                            ],
                        },
                    ],
                };
                expect(nestedGridFieldControlObject.getRecordValue<0>('-1', 0)).toStrictEqual(expected);
            });

            it('should be able get all changed records', () => {
                nestedGridFieldControlObject.addRecord<0>(
                    {
                        orderDate: '2021-01-13',
                        invoices: [
                            { invoiceDate: '2021-01-15', lines: [{ product: { product: 'new product' } }] },
                            {
                                invoiceDate: '2021-01-17',
                                lines: [
                                    { product: { product: 'new product' } },
                                    { product: { product: 'new product 2' } },
                                ],
                            },
                        ],
                    },
                    0,
                    undefined,
                )!;
                const expected = [
                    {
                        invoices: [
                            {
                                _action: 'create',
                                invoiceDate: '2021-01-17',
                                lines: [
                                    {
                                        _action: 'create',
                                        product: { product: 'new product 2' },
                                    },
                                    {
                                        _action: 'create',
                                        product: { product: 'new product' },
                                    },
                                ],
                            },
                            {
                                _action: 'create',
                                invoiceDate: '2021-01-15',
                                lines: [
                                    {
                                        _action: 'create',
                                        product: { product: 'new product' },
                                    },
                                ],
                            },
                        ],
                        orderDate: '2021-01-13',
                    },
                ];
                expect(nestedGridFieldControlObject.getChangedRecords()).toStrictEqual(expected);
            });

            it('should be able get all records in a tree form', () => {
                nestedGridFieldControlObject.addRecord<0>(
                    {
                        orderDate: '2021-01-13',
                        invoices: [
                            { invoiceDate: '2021-01-15', lines: [{ product: { product: 'new product' } }] },
                            {
                                invoiceDate: '2021-01-17',
                                lines: [
                                    { product: { product: 'new product' } },
                                    { product: { product: 'new product 2' } },
                                ],
                            },
                        ],
                    },
                    0,
                    undefined,
                )!;
                const expected = [
                    { _id: '3', orderDate: '2020-12-24', invoices: [] },
                    { _id: '2', orderDate: '2020-11-30', invoices: [] },
                    { _id: '1', orderDate: '2020-11-11', invoices: [] },
                    {
                        _id: '-1',
                        invoices: [
                            {
                                _id: '-4',
                                invoiceDate: '2021-01-17',
                                lines: [
                                    {
                                        _id: '-6',
                                        product: { product: 'new product 2' },
                                    },
                                    {
                                        _id: '-5',
                                        product: { product: 'new product' },
                                    },
                                ],
                            },
                            {
                                _id: '-2',
                                invoiceDate: '2021-01-15',
                                lines: [
                                    {
                                        _id: '-3',
                                        product: { product: 'new product' },
                                    },
                                ],
                            },
                        ],
                        orderDate: '2021-01-13',
                    },
                ];
                expect(nestedGridFieldControlObject.value).toStrictEqual(expected);
            });

            it('should return value without removing non-input properties', () => {
                const getAllDataAsTreeSpy = jest.spyOn(value, 'getAllDataAsTree');
                xtremConsole.log(nestedGridFieldControlObject.value);
                expect(getAllDataAsTreeSpy).toHaveBeenCalledWith({
                    cleanInputTypes: false,
                });
                getAllDataAsTreeSpy.mockRestore();
            });

            it('should get a row value when the getRecordValue() is called', () => {
                const result = nestedGridFieldControlObject.getRecordValue<0>('2', 0)!;
                expect(result._id).toEqual('2');
                expect(result.orderDate).toEqual('2020-11-30');
                expect(result.invoices).toEqual([]);
                expect(Object.keys(result)).toEqual(['_id', 'orderDate', 'invoices']);
            });

            it('getRecordByFieldValue() should return an object without any metadata if found', () => {
                const result = nestedGridFieldControlObject.getRecordByFieldValue<0, 'orderDate'>(
                    'orderDate',
                    '2020-11-30',
                    0,
                );
                expect(result!._id).toEqual('2');
                expect(Object.keys(result!)).toEqual(['_id', 'orderDate', 'invoices']);
            });

            it('addOrUpdateValue should set the nestedGrid value if it is empty', () => {
                nestedGridFieldControlObject = createFieldControlObject<FieldKey.NestedGrid, any, Order>(
                    FieldKey.NestedGrid,
                    screenId,
                    NestedGridControlObject,
                    fieldId,
                    null,
                    properties as any,
                ) as any;

                expect(nestedGridFieldControlObject.normalizedValue).toEqual([]);
                expect(nestedGridFieldControlObject.normalizedValue).toHaveLength(0);
                const result = nestedGridFieldControlObject.addOrUpdateRecordValue(
                    {
                        _id: '1',
                        orderDate: '2020-12-25',
                        invoices: [],
                    },
                    0,
                    undefined,
                );
                expect(nestedGridFieldControlObject.normalizedValue).toHaveLength(1);
                expect(result._id).toEqual('1');
                expect(nestedGridFieldControlObject.normalizedValue).toHaveLength(1);
            });

            it('addOrUpdateValue should add record to the DB if it does not have an ID', () => {
                expect(nestedGridFieldControlObject.normalizedValue).toHaveLength(3);
                const result = nestedGridFieldControlObject.addOrUpdateRecordValue(
                    {
                        orderDate: '2020-12-25',
                        invoices: [],
                    },
                    0,
                    undefined,
                );
                expect(result).toStrictEqual({
                    _id: '-1',
                    invoices: [],
                    orderDate: '2020-12-25',
                });
                expect(nestedGridFieldControlObject.normalizedValue).toHaveLength(4);
            });

            it('addOrUpdateValue should add record to the DB if its ID does not exist', () => {
                expect(nestedGridFieldControlObject.normalizedValue).toHaveLength(3);
                const result = nestedGridFieldControlObject.addOrUpdateRecordValue(
                    {
                        orderDate: '2020-12-25',
                        _id: '123',
                    },
                    0,
                    undefined,
                );
                expect(result).toStrictEqual({
                    _id: '123',
                    invoices: [],
                    orderDate: '2020-12-25',
                });
                expect(nestedGridFieldControlObject.normalizedValue).toHaveLength(4);
            });

            it('value should return empty collection values', () => {
                expect(nestedGridFieldControlObject.value).toStrictEqual([
                    { _id: '3', invoices: [], orderDate: '2020-12-24' },
                    { _id: '2', invoices: [], orderDate: '2020-11-30' },
                    { _id: '1', invoices: [], orderDate: '2020-11-11' },
                ]);
            });

            it('addOrUpdateValue should update record in the DB if its ID already exists', () => {
                let record: PartialCollectionValue<Order> | null = nestedGridFieldControlObject.getRecordValue<0>(
                    '2',
                    0,
                )!;
                expect(record).toStrictEqual({
                    _id: '2',
                    orderDate: '2020-11-30',
                    invoices: [],
                });
                expect(nestedGridFieldControlObject.normalizedValue).toHaveLength(3);
                const result = nestedGridFieldControlObject.addOrUpdateRecordValue<0>(
                    {
                        _id: '2',
                        orderDate: '2020-12-25',
                        invoices: [{ invoiceDate: '2020-12-24', lines: [{ product: { product: 'product1' } }] }],
                    },
                    0,
                    undefined,
                )!;
                expect(result).toStrictEqual({
                    _id: '2',
                    invoices: [
                        {
                            _id: '-1',
                            invoiceDate: '2020-12-24',
                            lines: [{ _id: '-2', product: { product: 'product1' } }],
                        },
                    ],
                    orderDate: '2020-12-25',
                });
                const collectionValue = (nestedGridFieldControlObject as any)._getValue() as CollectionValue<Order>;
                let order = collectionValue.getRecordByIdAndLevel({ id: '2', level: 0 });
                expect(order.__dirty).toBe(true);
                expect(order.__action).toBe(RecordActionType.MODIFIED);
                let invoice = collectionValue.getRecordByIdAndLevel({ id: '-1', level: 1 });
                expect(invoice.__dirty).toBe(true);
                expect(invoice.__action).toBe(RecordActionType.ADDED);
                expect(nestedGridFieldControlObject.normalizedValue).toHaveLength(5);
                record = nestedGridFieldControlObject.getRecordValue<0>('2', 0);
                expect(record).toStrictEqual({
                    _id: '2',
                    invoices: [
                        {
                            _id: '-1',
                            invoiceDate: '2020-12-24',
                            lines: [{ _id: '-2', product: { product: 'product1' } }],
                        },
                    ],
                    orderDate: '2020-12-25',
                });
                // same update to check dirty flag and action have been set
                nestedGridFieldControlObject.addOrUpdateRecordValue<0>(
                    {
                        _id: '2',
                        orderDate: '2020-12-25',
                        invoices: [{ _id: '-1', invoiceDate: '2020-12-24' }],
                    },
                    0,
                    undefined,
                )!;
                order = collectionValue.getRecordByIdAndLevel({ id: '2', level: 0 });
                expect(order.__dirty).toBe(true);
                expect(order.__action).toBe(RecordActionType.MODIFIED);
                invoice = collectionValue.getRecordByIdAndLevel({ id: '-1', level: 1 });
                expect(invoice.__dirty).toBe(true);
                expect(invoice.__action).toBe(RecordActionType.ADDED);
            });
        });
    });

    describe('loading child records', () => {
        let fetchSpy: jest.SpyInstance;
        let fetchCollectionDataSpy: jest.SpyInstance;

        beforeEach(() => {
            value = new CollectionValue<Order>({
                columnDefinitions: [
                    [
                        nestedFields.text<any, any>({ bind: '_id' }),
                        nestedFields.date<any, Order>({ bind: 'orderDate' }),
                    ],
                    [
                        nestedFields.text<any, any>({ bind: '_id' }),
                        nestedFields.date<any, Order>({ bind: 'invoiceDate' }),
                    ],
                    [nestedFields.text<any, any>({ bind: '_id' })],
                ] as any,
                elementId: fieldId,
                fieldType: CollectionFieldTypes.NESTED_GRID,
                filter: [undefined],
                hasNextPage: true,
                initialValues: [
                    { _id: '1', orderDate: '2020-11-11', invoices: [] },
                    { _id: '2', orderDate: '2020-11-30', invoices: [] },
                    { _id: '3', orderDate: '2020-12-24', invoices: [] },
                ],
                isTransient: false,
                levelMap: getLevelMap(levelsMock()),
                nodes: ['@sage/xtrem-test/Order', '@sage/xtrem-test/Invoice', '@sage/xtrem-test/InvoiceLine'],
                nodeTypes,
                orderBy: [{ anyField: 1 }],
                screenId,
                bind: 'bind',
            });

            nestedGridFieldControlObject = createFieldControlObject<FieldKey.NestedGrid>(
                FieldKey.NestedGrid,
                screenId,
                NestedGridControlObject,
                fieldId,
                value,
                properties as any,
            ) as any;

            fetchSpy = jest.spyOn(value, 'fetchNestedGrid');
            fetchCollectionDataSpy = jest.spyOn(graphQLService, 'fetchCollectionData').mockResolvedValue([]);
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should call the server to fetch child records if valid props provided', async () => {
            expect(fetchSpy).not.toHaveBeenCalled();
            await nestedGridFieldControlObject.loadChildRecords({ parentRecordId: '1', childLevel: 1 });
            expect(fetchSpy).toHaveBeenCalledWith({
                childProperty: 'invoices',
                level: 1,
                levelProps: { childProperty: 'lines', columns: [], node: 'Invoice' },
                queryArguments: { first: 100 },
                rootNode: 'Order',
                selectedRecordId: '1',
            });
            expect(fetchCollectionDataSpy).toHaveBeenCalledWith({
                screenDefinition: getMockPageDefinition('TestPage'),
                rootNode: 'Order',
                rootNodeId: '1',
                elementId: 'invoices',
                nestedFields: [],
                queryArguments: { first: 100 },
            });
        });

        it('should call the server to fetch child records with custom filter', async () => {
            expect(fetchSpy).not.toHaveBeenCalled();
            await nestedGridFieldControlObject.loadChildRecords({
                parentRecordId: '1',
                childLevel: 1,
                filter: { _id: { _eq: '5' } },
            });
            expect(fetchSpy).toHaveBeenCalledWith({
                childProperty: 'invoices',
                level: 1,
                levelProps: {
                    childProperty: 'lines',
                    columns: [],
                    node: 'Invoice',
                },
                queryArguments: { first: 100, filter: '{"_id":{"_eq":"5"}}' },
                rootNode: 'Order',
                selectedRecordId: '1',
            });
            expect(fetchCollectionDataSpy).toHaveBeenCalledWith({
                screenDefinition: getMockPageDefinition('TestPage'),
                rootNode: 'Order',
                rootNodeId: '1',
                elementId: 'invoices',
                nestedFields: [],
                queryArguments: {
                    first: 100,
                    filter: '{"_id":{"_eq":"5"}}',
                },
            });
        });

        it('should call the server to fetch child records with custom page size', async () => {
            expect(fetchSpy).not.toHaveBeenCalled();
            await nestedGridFieldControlObject.loadChildRecords({ parentRecordId: '1', childLevel: 1, limit: 123 });
            expect(fetchSpy).toHaveBeenCalledWith({
                childProperty: 'invoices',
                level: 1,
                levelProps: {
                    childProperty: 'lines',
                    columns: [],
                    node: 'Invoice',
                },
                queryArguments: {
                    first: 123,
                },
                rootNode: 'Order',
                selectedRecordId: '1',
            });
            expect(fetchCollectionDataSpy).toHaveBeenCalledWith({
                screenDefinition: getMockPageDefinition('TestPage'),
                rootNode: 'Order',
                rootNodeId: '1',
                elementId: 'invoices',
                nestedFields: [],
                queryArguments: { first: 123 },
            });
        });

        it('custom page size should not exceed 500', async () => {
            expect(fetchSpy).not.toHaveBeenCalled();
            await nestedGridFieldControlObject.loadChildRecords({ parentRecordId: '1', childLevel: 1, limit: 501 });
            expect(fetchSpy).toHaveBeenCalledWith({
                childProperty: 'invoices',
                level: 1,
                levelProps: {
                    childProperty: 'lines',
                    columns: [],
                    node: 'Invoice',
                },
                queryArguments: {
                    first: 500,
                },
                rootNode: 'Order',
                selectedRecordId: '1',
            });
            expect(fetchCollectionDataSpy).toHaveBeenCalledWith({
                screenDefinition: getMockPageDefinition('TestPage'),
                rootNode: 'Order',
                rootNodeId: '1',
                elementId: 'invoices',
                nestedFields: [],
                queryArguments: { first: 500 },
            });
        });

        it('should throw error when invalid level is provided', async () => {
            expect(fetchSpy).not.toHaveBeenCalled();
            await expect(
                nestedGridFieldControlObject.loadChildRecords({ parentRecordId: '1', childLevel: 0 }),
            ).rejects.toThrow("Invalid level '0' received. Please specify a value between 1 and 2.");
            expect(fetchSpy).not.toHaveBeenCalled();
            await expect(
                nestedGridFieldControlObject.loadChildRecords({ parentRecordId: '1', childLevel: 3 }),
            ).rejects.toThrow("Invalid level '3' received. Please specify a value between 1 and 2.");
            expect(fetchSpy).not.toHaveBeenCalled();
        });

        it('should throw error when invalid id-level pair is provided', async () => {
            expect(fetchSpy).not.toHaveBeenCalled();
            await expect(
                nestedGridFieldControlObject.loadChildRecords({ parentRecordId: '4', childLevel: 1 }),
            ).rejects.toThrow('Invalid record: could not find a record with ID 4 on level 0');
        });
    });

    describe('refreshRecord', () => {
        beforeEach(() => {
            jest.spyOn(value, 'refreshRecord').mockResolvedValue({ _id: 3, someField: 4 });
        });
        afterEach(() => {
            jest.clearAllMocks();
        });

        it("should call collection value's refreshRecord function", async () => {
            expect(value.refreshRecord).not.toHaveBeenCalled();
            const result = await nestedGridFieldControlObject.refreshRecord('4', 1);
            expect(result).toEqual({ _id: 3, someField: 4 });
            expect(value.refreshRecord).toHaveBeenCalledWith({ recordId: '4', level: 1, skipUpdate: false });
        });
    });
});
