import { fail } from 'assert';
import { set } from 'lodash';
import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata } from '../../../../__tests__/test-helpers';
import type { NestedField, NestedFieldTypes } from '../../../nested-fields';
import * as nestedFields from '../../../nested-fields';
import type { NestedGridProperties } from '../nested-grid-control-object';
import type { NestedGridDecoratorProperties } from '../nested-grid-decorator';
import { nestedGridField } from '../nested-grid-decorator';
import type { Invoice, InvoiceLine, Order } from './node-types';

describe('NestedGrid Decorator', () => {
    const fieldId = 'nestedGridField';
    let pageMetadata: pageMetaData.PageMetadata;

    describe('mapping values', () => {
        let mappedComponentProperties: NestedGridDecoratorProperties<ScreenBase, [any, any]>;
        const levelsMock: () => NestedGridDecoratorProperties<any, [Order, Invoice, InvoiceLine]>['levels'] = () => [
            {
                node: 'Order',
                columns: [],
                childProperty: 'invoices',
            },
            {
                node: 'Invoice',
                columns: [],
                childProperty: 'lines',
            },
            {
                node: 'InvoiceLine',
                columns: [],
            },
        ];
        beforeEach(() => {
            pageMetadata = getMockPageMetadata();
            jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
            nestedGridField({ levels: levelsMock() })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            mappedComponentProperties = pageMetadata.uiComponentProperties[fieldId] as NestedGridProperties<ScreenBase>;
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('NestedGridProperties -> should set default values when no component properties provided', () => {
            expect(mappedComponentProperties.bind).toBeUndefined();
            expect(mappedComponentProperties.canFilter).toBe(true);
            expect(mappedComponentProperties.canSelect).toBe(true);
            expect(mappedComponentProperties.canUserHideColumns).toBe(true);
            expect(mappedComponentProperties.helperText).toBeUndefined();
            expect(mappedComponentProperties.isFullWidth).toBe(false);
            expect(mappedComponentProperties.isHidden).toBe(false);
            expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
            expect(mappedComponentProperties.isHiddenMobile).toBe(false);
            expect(mappedComponentProperties.isTransient).toBe(false);
            expect(mappedComponentProperties.onChange).toBeUndefined();
            expect(mappedComponentProperties.onClick).toBeUndefined();
            expect(mappedComponentProperties.onRowSelected).toBeUndefined();
            expect(mappedComponentProperties.onRowUnselected).toBeUndefined();
            expect(mappedComponentProperties.parent).toBeUndefined();
            expect(mappedComponentProperties.selectedRecords).toBeUndefined();
            expect(mappedComponentProperties.title).toBeUndefined();
        });

        describe('mapping mandatory props for nested fields', () => {
            const keys = [
                'Aggregate',
                'Checkbox',
                'Count',
                'Date',
                'FilterSelect',
                'Icon',
                'Image',
                'Label',
                'Link',
                'MultiReference',
                'Numeric',
                'Progress',
                'Reference',
                'Select',
                'Switch',
                'Text',
            ];

            const mandatoryProps = {
                Aggregate: { bind: 'Aggregate' },
                Checkbox: { bind: 'Checkbox' },
                Count: { bind: 'Count' },
                Date: { bind: 'Date' },
                FilterSelect: { bind: 'FilterSelect' },
                Icon: { bind: 'Icon' },
                Image: { bind: 'Image' },
                Label: { bind: 'Label' },
                Link: { bind: 'Link', page: 'Test page' },
                MultiReference: { bind: 'MultiReference' },
                Numeric: { bind: 'Numeric' },
                Progress: { bind: 'Progress' },
                Reference: { bind: 'Reference' },
                Select: { bind: 'Select' },
                Switch: { bind: 'Switch' },
                Text: { bind: 'Text' },
            };

            const columns: NestedField<Page, NestedFieldTypes>[] = keys.map(key => {
                const lowerCaseKey = key[0].toLowerCase() + key.slice(1);
                return nestedFields[lowerCaseKey]({ ...mandatoryProps[key] });
            });

            it('should set values when component properties provided', () => {
                expect(columns.length).toEqual(keys.length);
                columns.forEach(column => {
                    if (column.properties) {
                        expect(keys).toContain(column.properties.bind);
                    } else {
                        fail('Test failed: no properties in Column object');
                    }
                });
            });
        });

        describe('Runtime validations', () => {
            it('missing levels', () => {
                expect(() => {
                    nestedGridField<any, []>({
                        levels: [],
                    })({} as Page, fieldId);
                }).toThrow('A nested grid must have at least two levels.');
            });

            it('"childProperty" is set', () => {
                for (let i = 0; i < levelsMock.length - 1; i += 1) {
                    const levels = set(levelsMock(), `[${i}].childProperty`, undefined);
                    expect(() => {
                        nestedGridField<any, [Order, Invoice, InvoiceLine]>({
                            levels,
                        })({} as Page, fieldId);
                    }).toThrow('All nested grid\'s levels but the last must have a "childProperty" set.');
                }
            });

            it('last level does not have a "childProperty"', () => {
                const levels = set(levelsMock(), '[2].childProperty', 'invalid');
                expect(() => {
                    nestedGridField<any, [Order, Invoice, InvoiceLine]>({
                        levels,
                    })({} as Page, fieldId);
                }).toThrow('The last level of a nested grid should not have a "childProperty" set.');
            });

            it('should add "_id" is missing from columns', () => {
                nestedGridField<any, [Order, Invoice, InvoiceLine]>({
                    levels: levelsMock(),
                })({} as Page, fieldId);
                const nestedId = nestedFields.text<any, any>({ bind: '_id', isHidden: true });
                mappedComponentProperties.levels.forEach((_, index) => {
                    expect(mappedComponentProperties.levels[index].columns.length).toBe(1);
                    expect(mappedComponentProperties.levels[index].columns[0]).toMatchObject(nestedId);
                });
            });

            it('should not add "_id" to columns if already present', () => {
                const nestedId = nestedFields.text<any, any>({ bind: '_id' });
                const levels = set(levelsMock(), '[0].columns[0]', nestedId);
                nestedGridField<any, [Order, Invoice, InvoiceLine]>({
                    levels,
                })({} as Page, fieldId);
                const hiddenId = nestedFields.text<any, any>({ bind: '_id', isHidden: true });
                mappedComponentProperties.levels.forEach((_, index) => {
                    expect(mappedComponentProperties.levels[index].columns.length).toBe(1);
                    if (index === 0) {
                        expect(mappedComponentProperties.levels[index].columns[0]).toMatchObject(nestedId);
                    } else {
                        expect(mappedComponentProperties.levels[index].columns[0]).toMatchObject(hiddenId);
                    }
                });
            });
        });
    });
});
