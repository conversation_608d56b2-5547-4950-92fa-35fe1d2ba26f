import type { ClientCollection } from '@sage/xtrem-client';

type Product = {
    _id: string;
    product: string;
};

type InvoiceLine = {
    _id: string;
    product: Product;
};

type Invoice = {
    _id: string;
    invoiceDate: string;
    lines: ClientCollection<InvoiceLine>;
};
type Order = {
    _id: string;
    orderDate: string;
    invoiceDate: string;
    invoices: ClientCollection<Invoice>;
};

type Customer = {
    _id: string;
    orders: ClientCollection<Order>;
};

export { Product, Invoice, InvoiceLine, Order, Customer };
