import type { RowNode } from '@ag-grid-community/core';
import type { ClientNode } from '@sage/xtrem-client';
import type { Dict, LocalizeLocale } from '@sage/xtrem-shared';
import type { OnTelemetryEventFunction, ReduxResponsive } from '../../../redux/state';
import type { CollectionValue } from '../../../service/collection-data-service';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import type { PageArticleItem } from '../../../service/layout-types';
import type { DataTypeDetails, FormattedNodeDetails } from '../../../service/metadata-types';
import type { AccessBindings } from '../../../service/page-definition';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import type { ContextType, ScreenBaseGenericType, ScreenExtension } from '../../../types';
import type { FixedSizeTuple } from '../../../utils/types';
import type { InternalNestedGridProperties } from '../../control-objects';
import type { GridNestedFieldTypes, NestedField } from '../../nested-fields';
import type { NestedExtensionField } from '../../nested-fields-extensions';
import type { NestedOverrideField } from '../../nested-fields-overrides';
import type { SidebarDefinitionDecorator } from '../../table-sidebar/table-sidebar-types';
import type { CollectionItem, OrderByType } from '../../types';
import type {
    NestedCollectionItemActionGroup,
    NestedCollectionItemActionOrMenuSeparator,
} from '../../ui/table-shared/table-dropdown-actions/table-dropdown-action-types';
import type { PropertyValueType } from '../reference/reference-types';
import type { ServerRecordMapperFunction, VoidPromise } from '../traits';

export interface Column {
    /** Field title label */
    headerName?: string;
    /** Bind */
    field: string;
}

export interface GetDetailRowDataParams {
    node: RowNode;
    data: any;
    // success callback, pass the rows back the grid asked for
    successCallback(rowValue: any[]): void;
}

export interface NestedGridComponentExternalProps {
    elementId: string;
    isParentDisabled?: boolean;
    item?: PageArticleItem;
    mobilePerformInitialLoadNestedGridData?: boolean;
    screenId: string;
    searchText?: string;
}

export interface NestedGridInternalComponentProps {
    accessBindings: AccessBindings;
    contextType?: ContextType;
    dataTypes: Dict<DataTypeDetails>;
    elementId: string;
    enumTypes: Dict<string[]>;
    fieldProperties: InternalNestedGridProperties;
    fixedHeight?: number;
    groupTitle?: string;
    isParentDisabled?: boolean;
    isUsingInfiniteScroll?: boolean;
    locale: LocalizeLocale;
    nodeTypes: Dict<FormattedNodeDetails>;
    onRowClick?: (recordId: NestedGridItemId, level: number) => void;
    screenId: string;
    searchText?: string;
    setFieldProperties?: (elementId: string, properties: InternalNestedGridProperties) => void;
    setGlobalLoading?: (setGlobalLoading: boolean) => void;
    username?: string;
    validationErrors: ValidationResult[];
    value: CollectionValue;
    onTelemetryEvent?: OnTelemetryEventFunction;
}

export interface NestedGridComponentProps extends NestedGridComponentExternalProps {
    accessBindings: AccessBindings;
    browser: ReduxResponsive;
    contextType?: ContextType;
    dataTypes: Dict<DataTypeDetails>;
    enableMobileLoadMore?: boolean;
    enumTypes: Dict<string[]>;
    fieldProperties: InternalNestedGridProperties;
    fixedHeight?: number;
    isLoading?: boolean;
    isUsingInfiniteScroll?: boolean;
    locale: LocalizeLocale;
    nodeTypes: Dict<FormattedNodeDetails>;
    onRowClick?: (rowValue: CollectionItem) => void;
    onTelemetryEvent?: OnTelemetryEventFunction;
    openSidebar: (args: {
        elementId: string;
        screenId: string;
        level: number;
        parentId: string;
        fieldProperties: InternalNestedGridProperties<ScreenBase>;
    }) => void;
    setFieldProperties?: (elementId: string, properties: InternalNestedGridProperties) => void;
    setGlobalLoading?: (setGlobalLoading: boolean) => void;
    username?: string;
    validate?: (elementId: string, value: CollectionValue) => Promise<ValidationResult[] | undefined>;
    validationErrors: ValidationResult[];
    value: CollectionValue;
}

export interface ColumnsData {
    columnDefinition: NestedField<ScreenBase, GridNestedFieldTypes>;
    bind: string;
    elementId: string;
}

export type ExtensionLevel<CT extends ScreenExtension<CT>, T extends ClientNode = any> = {
    /** The definitions of the nested fields used to represent the table rows */
    columns?: NestedExtensionField<CT, GridNestedFieldTypes, T>[];

    /** Allows overriding existing column properties in the base page's columns */
    columnOverrides?: NestedOverrideField<CT, GridNestedFieldTypes, T>[];

    /** Additional row actions */
    dropdownActions?: Array<NestedCollectionItemActionOrMenuSeparator<CT, T> | NestedCollectionItemActionGroup<CT, T>>;
};

export type Level<CT extends ScreenExtension<CT>, Node extends ClientNode = any, ChildNode extends ClientNode = any> = {
    childProperty?: PropertyValueType<Node>;
    columns: NestedField<CT, GridNestedFieldTypes, Node>[];
    /** Sets a text when no data is available in the table */
    emptyStateText?: string;
    /** Sets a complementary text link when no data is available in the table  */
    emptyStateClickableText?: string;
    /** Function to be executed when the clickable text is clicked */
    onEmptyStateLinkClick?: (this: CT, recordId: NestedGridItemId, level: number) => void;
    /** Function to be executed when a level is expanded. It applies only to nested levels and is only triggered when the first batch of children is loaded. */
    onLevelExpanded?: (this: CT, parentId: string, children: ChildNode[]) => VoidPromise;
    filter?: GraphQLFilter<Node> | ((this: CT) => GraphQLFilter<Node>);
    /** The GraphQL node that the grid level represents */
    node: keyof ScreenBaseGenericType<CT> & string;
    /** The column or the set of columns which the nested grid should be sorted by */
    orderBy?: OrderByType<Node>;
    /** Actions that are rendered at the end of the table as a drop-down menu */
    dropdownActions?: Array<
        NestedCollectionItemActionOrMenuSeparator<CT, Node> | NestedCollectionItemActionGroup<CT, Node>
    >;
    /** Allows changing record values before those are rendered to the screen. This function is invoked whenever new entities are fetched from the server. */
    mapServerRecord?: ServerRecordMapperFunction<CT, Node>;
    canAddNewLine?: boolean;
    sidebar?: SidebarDefinitionDecorator<CT, Node>;
};

export type MappedExtensionLevel<CT extends ScreenExtension<CT>, T extends ClientNode[] = [any, any]> = {
    [K in keyof T]: T[K] extends ClientNode ? ExtensionLevel<CT, T[K]> : never;
};

type GetChildNode<T extends ClientNode[] = [any, any], U extends ClientNode[] = []> = T extends [
    ClientNode,
    ...infer Tail,
]
    ? Tail extends ClientNode[]
        ? GetChildNode<Tail, [...U, Tail[0]]>
        : [...U, T[0]]
    : U;

export type MappedLevel<CT extends ScreenExtension<CT>, T extends ClientNode[] = [any, any]> = {
    [K in keyof T]: T[K] extends ClientNode
        ? Level<CT, T[K], K extends keyof GetChildNode<T> ? GetChildNode<T>[K] : never>
        : never;
};

export type MappedSelected<T extends ClientNode[] = [any, any]> = FixedSizeTuple<string[], T['length']>;

export type NestedGridItemId = string;
