PATH: XTREEM/UI+Field+Widgets/NestedGrid+Field

## Introduction

NestedGrid fields are used in order to represent nestedGrids.

## Example:

```ts
/**
 * ShowCaseCustomer: node used as a starting point for the first one-to-many relationship
 * (i.e. one ShowCaseCustomer has many 'orders' of type ShowCaseOrder)
 *
 * ShowCaseOrder: node used for the top-level grid. It has a one-to-many relationship with 'invoices'
 * (i.e. one ShowCaseOrder has many 'invoices' of type ShowCaseInvoice)
 *
 * ShowCaseInvoice: node used for the first-level grid. It has a one-to-many relationship with 'lines'
 * (i.e. one ShowCaseInvoice has many 'lines' of type ShowCaseInvoiceLine)
 *
 * ShowCaseInvoiceLine: node used for the second-level grid.
 *
 */
@ui.decorators.nestedGridField<NestedGrid, [ShowCaseOrder, ShowCaseInvoice, ShowCaseInvoiceLine]>({
        bind: 'orders',
        canFilter: true,
        levels: [
            {
                node: '@sage/xtrem-show-case/ShowCaseOrder',
                childProperty: 'invoices',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: true,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'orderDate',
                        title: 'Order Date',
                        canFilter: true,
                    }),
                ],
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoice',
                childProperty: 'lines',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: true,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.date({
                        bind: 'purchaseDate',
                        title: 'Purchase Date',
                    }),
                ],
                dropdownActions: [{
                    icon: 'add',
                    title: 'Add',
                    onClick(recordId: string, data: ShowCaseInvoice, level: number, parentId: string[]) {
                        this.$.dialog.message(
                            'info',
                            'Add',
                            `Adding at level: ${level}`,
                        );
                    },
                },]
            },
            {
                node: '@sage/xtrem-show-case/ShowCaseInvoiceLine',
                columns: [
                    ui.nestedFields.text({
                        bind: '_id',
                        title: 'Id',
                        isHiddenDesktop: true,
                        isReadOnly: true,
                    }),
                    ui.nestedFields.numeric({
                        bind: 'orderQuantity',
                        title: 'Quantity',
                        scale: 0,
                    }),
                    ui.nestedFields.reference<any, any, any><NestedGrid, ShowCaseInvoiceLine, ShowCaseProduct>({
                        bind: 'product',
                        node: '@sage/xtrem-show-case/ShowCaseProduct',
                        valueField: { product: true },
                        helperTextField: { _id: true },
                        title: 'Product',
                        canFilter: true,
                    }),
                ],
            }
        ],
        parent() {
            return this.fieldBlock;
        },
    })
```

### Display decorator properties:

-   **canFilter**: Whether the rows of the nested grid can be filtered or not. Defaults to true. A filter icon will be displayed on the upper right hand corner as long as at least one nested grid column is marked as filterable.
-   **canActivate**: Whether a row can be activated or not. Defaults to false. Activation will use single row selection, such that when you select a row, any previously selected row gets unselected. A row can be activated simply by clicking on it. Note that if set to true this property will take precedence over `canSelect`.
-   **canSelect**: Whether the rows of the nested grid can be selected or not. Defaults to true.
-   **emptyStateClickableText**: string; Sets a complementary text link when no data is available in the table
-   **emptyStateText**: Sets a text when no data is available in the table
-   **levels**: A list of levels representing the hierarchy of nested grids (see example above). Note that at least two levels are needed in order for a nested grid to work. If all you need is one level then take a look at the [table field](./Table). Each level has the following properties:
    -   `node`: the node type for the nested grid;
    -   **canAddNewLine**: Whether new rows can be added or not. If set to true a new "phantom row" will appear at the top of the table. Additionally an "Add line" action button will be added to the table. In order to commit a new row the user can tab through all table columns or hit Command/Control + Enter at any time while the "phantom row" is in focus. Only valid rows can be committed to the table. Defaults to `false`.
    -   `columns`: a list of nested fields to be displayed as columns. The following types can be used as nested fields: 'checkbox', 'icon', 'image', 'label', 'link', 'numeric', 'progress', 'reference', 'text', 'date';
    -   `childProperty`: the property of `node` that is used to identify the "many" side of a one-to-many relationship (see example above). This property is mandatory for all levels except the last.
    -   `dropdownActions`: A list of actions that are displayed at the end of each row as a drop-down menu. See below under "Row action properties" how they can be configured.
    - `sidebar`: Defines the sidebar component of the level. For more details see [here](./Table+Sidebar).

-   **helperText**: The helper text that is displayed above the nested grid. It is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Whether the nested grid is disabled or not. It can also be defined as callback function that returns a boolean. Defaults to false.
-   **isHidden**: Whether the nested grid is hidden or not. Defaults to false.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **isChangeIndicatorDisabled**: Disable indicator background color that signals if a row is added or modified
-   **mapServerRecord(record)=>record**: Allows changing record values before those are rendered to the screen. This function is invoked whenever new entities are fetched from the server. The function is not allowed to modify the ID of the record or any other metadata property. The function may not be called when values are added by the functional code on runtime, only in case of server side communication.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.
-   **hasLineNumbers**: Displays line numbers in front of each line. The line numbers are static and not related to the data

### Binding decorator properties:

-   **bind**: The GraphQL object's property that the nested grid value is bound to.
-   **isTransient**: If marked as true, the nested grid will be excluded from the automatic data binding process (i.e. no data will be fetched from the server). The default value is false.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **node**: The name of the node that the nested grid represents, it must be defined if you want to use the advanced filtering capabilities.

### Event handler decorator properties:

-   **onChange**: Event triggered when the nested grid value changes.
-   **onClick**: Event triggered when any parts of the nested grid is clicked, no arguments provided.
-   **onEmptyStateLinkClick(recordId: NestedGridItemId, level: number)**: Function to be executed when the clickable text is clicked
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).
-   **onRowActivated**: Event triggered when a nested grid row is activated.
-   **onRowClick**: Event triggered when any part of a nested grid row is clicked.
-   **onRowDeactivated**: Event triggered when a nested grid row is deactivated.
-   **onRowSelected**: Event triggered when a nested grid row is selected.
-   **onRowUnselected**: Event triggered when a nested grid row is unselected.

### Row action properties

-   **title**: The title of the action displayed at the end of the table under a drop-down menu.
-   **icon**: An optional icon for the action, to be displayed alongside the action title.
-   **onClick(recordId: string, data: NestedRecordType, level: number, parentId: string[])**: Event triggered when an action is selected in the drop-down menu.
-   **isDisabled(recordId: string, data: NestedRecordType, level: number, parentId: string[])**: Whether the table action is enabled (disabled = false) or not (disabled = true). A table action is enabled by default.
-   **isHidden(recordId: string, data: NestedRecordType, level: number, parentId: string[])**: Whether the table action is visible (isHidden = false) or not (isHidden = true). A table action is visible by default.
-   **isDestructive**: If set, the icon corresponding to the action is rendered in red.
-   If only one dropdown action is set, then only one button is displayed, otherwise a dropdown containing the available dropdown actions.

In addition to a dropdown action, a separator line can also added using `ui.menuSeparator({ id?: string, insertAfter?: string, insertBefore?: string })`.

## Runtime functions

-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **refreshRecord(recordId, level, skipSet=false)**: Like refetch but applied only on a row level. If the skipSet flag is set to true, the value is returned but not applied to the table.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **setRecordValue(rowValue: any)**: Updates a single row on the nested grid and keeps update history.
-   **getRecordByFieldValue(fieldName: keyof NestedRecordType, fieldValue: any)**: Find a row based on a property's value
-   **getRecordValue(recordId:string)**: Returns a single row. It can only return rows that were previously loaded to the client side-cache by some other user interaction.
-   **addRecord(rowValue: any)**: Adds a single row to the nested grid and marks it as a new row.
-   **addOrUpdateRecordValue(rowValue: any)**: Add or update row in the nested grid depending of the existence of the ID field.
-   **removeRecord(recordId:string)**: Removes a single row from the displayed nested grid. It does NOT delete the corresponding server side entity.
-   **selectRecord(recordId:string)**: Selects a single row.
-   **unselectRecord(recordId:string)**: Unselects a single row.
-   **unselectAllRecords()**: Unselects all selected rows.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **loadChildRecords({ parentLevel, parentRecordId, limit, filter, openLevel })**: Fetches data for children record based on parent record id and parent level. Default limit is 100. If the openLevel attribute is set to true, the parent record is unfolded on the user interface and the children are displayed.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

### Column properties

Please refer to the documentation of the corresponding nested field type.


## Adding columns in extension pages

Additional fields can be added to a nested grid in extension page artifacts using the `nestedGridFieldExtension` decorator. In this case, the `columns` decorator property accepts an `nestedFieldExtension` array. This set of nested fields comes with an additional decorator property which determines the position of the extension columns within the original nested grid. This property is called `insertBefore`. The extension columns should be declared on the same level, following the same structure as the base field.

### Positioning extension fields within the nested grid

The `insertBefore` decorator property determines the position of the extension field.
#### Not provided
If this decorator property is not provided, the extension field is added to the end of the columns.

#### Using the bind value
The position of the extension column can be set by providing the `bind` decorator property value of the base column that the field should be inserted before to the `insertBefore` extension column decorator property.

#### Base reference fields bound to the same property
When the base page contains reference field based columns that are bound to the same property, the position of the additional column can be set more precisely by including the `valueField` property value into the `insertBefore` extension property. This must be done by joining the two values by a double underscore in the following format: `bind__valueField`. For example if the field should be positioned before a reference field that is bound to `product` and the displayed value field is `description`, than the `insertBefore` extension property would be `product__description`.

### Infinite scroll

If the nested grid is the only element within the section, then it will use infinite scrolling. This is only applies to the top level of the nested grid. In every other scenario, it will be using the pagination.


### Example

```ts
import * as ui from '@sage/xtrem-ui';

...

export class NestedGridExtension extends ui.PageExtension<NestedGrid, GraphApi> {

    ...

    @ui.decorators.nestedGridFieldOverride<NestedGridExtension, ShowCaseProduct>({
        title: 'Overridden Title',
        levels:[
            {
                dropdownActions: [...],
                columns: [
                    ui.nestedFieldExtensions.reference<NestedGridExtension, ShowCaseProduct, BiodegradabilityCategory>({
                        bind: 'biodegradabilityCategory',
                        valueField: 'description',
                        node: '@sage/xtrem-show-case-bundle/BiodegradabilityCategory',
                        // This column is inserted before the `product` bound reference column that has `name` valueField configuration
                        insertBefore: 'product__name',
                        columns: [
                            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
                            ui.nestedFields.text({ bind: 'energyToProduce', title: 'Energy to produce' })
                        ],
                        title: 'Biodegradability category',
                        isAutoSelectEnabled: true,
                    }),
                    // This column goes to the end.
                    ui.nestedFieldExtensions.text<NestedGridExtension>({
                        bind: 'someTransientFieldAtTheEnd',
                        isTransient: true,
                        title: 'Transient ext column',
                    }),
                ]
            },
            {
                // No columns declared for the second level.
            }
            {
                columns: [
                    // This column goes to the end.
                    ui.nestedFieldExtensions.text<NestedGridExtension>({
                        bind: 'someTransientFieldAtTheVeryEnd',
                        isTransient: true,
                        title: 'Transient ext column 2',
                    }),
                    // This column is inserted before the `provider` bound base column.
                    ui.nestedFieldExtensions.numeric<NestedGridExtension>({
                        bind: 'someTransientFieldInTheMiddle',
                        isTransient: true,
                        title: 'Transient number',
                        insertBefore: 'provider',
                        scale: 5
                    }),
                ]
            },

        ]
    })
    field: ui.fields.NestedGrid;
}
```

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/NestedGrid).
