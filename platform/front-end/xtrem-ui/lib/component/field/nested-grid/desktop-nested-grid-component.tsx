import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import type {
    CellClickedEvent,
    CellEditingStoppedEvent,
    CellKeyDownEvent,
    ColDef,
    ColumnState,
    FilterChangedEvent,
    FirstDataRenderedEvent,
    GetMainMenuItems,
    GridApi,
    GridOptions,
    GridReadyEvent,
    IDetailCellRendererParams,
    IRowNode,
    IServerSideDatasource,
    IServerSideGetRowsParams,
    Module,
    PaginationChangedEvent,
    RowClassParams,
    RowClickedEvent,
    RowGroupOpenedEvent,
    SelectionChangedEvent,
    SortChangedEvent,
} from '@ag-grid-community/core';
import type { AgGridReactProps } from '@ag-grid-community/react';
import { AgGridReact } from '@ag-grid-community/react';
import { EnterpriseCoreModule } from '@ag-grid-enterprise/core';
import { ExcelExportModule } from '@ag-grid-enterprise/excel-export';
import { MasterDetailModule } from '@ag-grid-enterprise/master-detail';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { RichSelectModule } from '@ag-grid-enterprise/rich-select';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { ServerSideRowModelModule } from '@ag-grid-enterprise/server-side-row-model';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { objectKeys, type Dict } from '@sage/xtrem-shared';
import {
    cloneDeep,
    difference,
    get,
    isEqual,
    isNil,
    last,
    memoize,
    partition,
    set,
    setWith,
    sortBy,
    uniq,
} from 'lodash';
import * as React from 'react';
import type { Unsubscribe } from 'redux';
import { ActionType, getStore } from '../../../redux';
import { subscribeToActions } from '../../../redux/middleware/action-subscription-middleware';
import { setAgGridLicence } from '../../../service/ag-grid-licence-service';
import type { CollectionValue, GridLodable } from '../../../service/collection-data-service';
import type { CollectionValueType, ValidityChangeSubscriptionCallback } from '../../../service/collection-data-types';
import { RecordActionType } from '../../../service/collection-data-types';
import { getNestedFieldElementId } from '../../../utils/abstract-fields-utils';
import { xtremConsole } from '../../../utils/console';
import { type NestedFieldComponentProperties, triggerFieldEvent, triggerNestedFieldEvent } from '../../../utils/events';
import { cleanMetadataFromRecord, splitValueToMergedValue } from '../../../utils/transformers';
import { isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import {
    frameworkComponents,
    COLUMN_ID_VALIDATIONS,
    COLUMN_ID_ROW_ACTIONS,
    COLUMN_ID_ROW_SELECTION,
    getSelectionColumnDef,
} from '../../../utils/ag-grid/ag-grid-column-config';
import { getColumns } from '../../../utils/ag-grid/ag-grid-service';
import { localeText } from '../../../utils/ag-grid/ag-grid-strings';
import {
    getOrderByFromSortModel,
    getNestedGridContext,
    setNestedGridContext,
} from '../../../utils/table-component-utils';
import type { GetDetailRowDataParams, NestedGridInternalComponentProps } from './nested-grid-component-types';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import type { PropertyValueType } from '../reference/reference-types';
import { DesktopTableHeaderComponent } from '../../ui/table-shared/desktop-table-header';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import type { OptionsMenuItem } from '../../container/page/page-types';
import { TableConfigurationDialog } from '../../ui/table-shared/table-configuration-dialog/table-configuration-dialog';
import { getRowClassRules } from '../../../utils/ag-grid/ag-grid-cell-editor-utils';
import { TABLE_INLINE_ACTION_CSS_CLASS } from '../../../utils/constants';
import { TableLoadingCellRenderer } from '../../ui/table-shared/cell/table-loading-cell-renderer';
import { onCellKeyDown } from '../../../utils/ag-grid/ag-grid-event-handlers';
import type { AgGridColumnConfigWithScreenIdAndColDef } from '../../../utils/ag-grid/ag-grid-utility-types';
import {
    getColumnStatesForColumnPanel,
    mapAgGridFilterToXtremFilters,
    getSafeGridApiContext,
    callGridMethod,
    tryToCommitPhantomRow,
    getFirstEditableColumn,
    ROW_HEIGHT,
} from '../../../utils/ag-grid/ag-grid-table-utils';
import { hasAgGridLogging } from '../../../utils/window';
import type { NestedGridProperties } from './nested-grid-control-object';

const activatedAgGridModules = [
    ServerSideRowModelModule,
    EnterpriseCoreModule,
    MenuModule,
    RichSelectModule,
    SetFilterModule,
    RowGroupingModule,
    ExcelExportModule,
    MasterDetailModule,
    ClientSideRowModelModule,
] as Module[];

enum DataOperationMode {
    NONE,
    FETCH_PAGE,
    REMOVE_RECORD,
    RESET_TABLE,
}

interface NestedGridDesktopState {
    columns: ColDef[];
    detailCellRendererParams: GridOptions['detailCellRendererParams'];
    isExportingExcel: boolean;
    rowSelectionMode: GridOptions['rowSelection'];
    selectedOptionsMenuItem?: OptionsMenuItem;
    isConfigurationDialogOpen?: boolean;
    columnStates?: ColumnState[];
    tableHasData?: boolean;
}

const getRowClass = (
    params: RowClassParams,
    isChangeIndicatorDisabled: boolean,
    canSelect: boolean,
): string | string[] | undefined => {
    const level = getNestedGridContext(params.api)?.level || 0;
    const idSuffix = params.data?._id ? `-${params.data._id}` : '';
    let classList = [`nested-row-level-${level}${idSuffix}`];
    if (!params.data || isChangeIndicatorDisabled) {
        return classList;
    }
    const customClassRules = getRowClassRules({
        canSelect,
        isChangeIndicatorDisabled,
    });
    objectKeys(customClassRules).forEach(key => {
        if (customClassRules[key](params)) {
            classList = [...classList, key];
        }
    });

    if (
        params.data?.__phantom === undefined &&
        (params.data.__action === RecordActionType.ADDED || params.data.__action === RecordActionType.MODIFIED)
    ) {
        return [...classList, 'ag-row-edited'];
    }

    return classList;
};
export class DesktopNestedGridComponent extends React.Component<
    NestedGridInternalComponentProps,
    NestedGridDesktopState
> {
    private gridApi: GridApi;

    private dataOperationMode: Array<Dict<DataOperationMode>> = [{ 0: DataOperationMode.NONE }];

    private collectionValueChangeSubscription: Unsubscribe;

    private actionSubscription: Unsubscribe;

    private readonly containerRef = React.createRef<HTMLDivElement>();

    // Header classes & subscriptions cached by level & parent ID
    private readonly collectionValidityChangeSubscriptions: Array<
        Record<number, { unsubscribe: Unsubscribe; headerClasses: Dict<boolean> }>
    > = [];

    constructor(props: NestedGridInternalComponentProps) {
        super(props);
        setAgGridLicence();
        const optionsMenu = resolveByValue({
            propertyValue: this.props.fieldProperties.optionsMenu,
            rowValue: null,
            screenId: this.props.screenId,
            fieldValue: null,
            skipHexFormat: true,
        });
        this.state = {
            columns: [],
            detailCellRendererParams: {},
            isExportingExcel: false,
            rowSelectionMode: this.getRowSelectionMode(),
            selectedOptionsMenuItem: optionsMenu ? optionsMenu[0] : undefined,
            tableHasData: props.value && props.value.getData && props.value.getData({ level: 0 }).length > 0,
        };
    }

    componentDidMount(): void {
        this.getColumnDefinitions();
        this.getDetailCellRendererParams();
        this.actionSubscription = subscribeToActions(async action => {
            if (
                action.type === ActionType.OpenNestedGridLevel &&
                this.gridApi &&
                this.props.screenId === action.value.screenId &&
                this.props.elementId === action.value.elementId &&
                action.value.recordPath
            ) {
                const recordPath = action.value.recordPath;
                if (!recordPath) {
                    return;
                }

                const rowId = last(recordPath);
                if (!rowId) {
                    return;
                }

                this.getRowNode({ id: rowId, gridApi: this.getGridApiByRecordPath(recordPath) })?.setExpanded(true);
            }
        });
    }

    componentDidUpdate(prevProps: NestedGridInternalComponentProps): void {
        if (
            prevProps.value !== this.props.value ||
            prevProps.fieldProperties.isDisabled !== this.props.fieldProperties.isDisabled ||
            prevProps.isParentDisabled !== this.props.isParentDisabled
        ) {
            setWith(this.dataOperationMode, [0, 0], DataOperationMode.NONE, Object);
            this.resetOptionsMenu();
            this.getColumnDefinitions();
            this.getDetailCellRendererParams();
        }

        if (
            this.gridApi &&
            !isEqual(prevProps.fieldProperties.selectedRecords, this.props.fieldProperties.selectedRecords)
        ) {
            callGridMethod(this.gridApi, 'deselectAll');
            this.selectTableItems();
        }
    }

    componentWillUnmount(): void {
        if (this.collectionValueChangeSubscription) {
            this.collectionValueChangeSubscription();
        }
        if (this.actionSubscription) {
            this.actionSubscription();
        }

        this.collectionValidityChangeSubscriptions
            .flatMap(c => Object.values(c))
            .forEach(collectionValidityChangeSubscription => {
                collectionValidityChangeSubscription.unsubscribe();
            });
    }

    private getGridApiByRecordPath(recordPath: string[]): GridApi {
        let gridApi = this.gridApi;
        for (let i = 0; i < recordPath.length; i += 1) {
            const rowId = recordPath[i];
            if (i === recordPath.length - 1) {
                // If we are on the target level we open the row
                return gridApi;
            }
            // If we are not on the target level, we get the API of the nested table
            const detailGrid = gridApi.getDetailGridInfo(`detail_${rowId}`);
            if (detailGrid && detailGrid.api) {
                gridApi = detailGrid.api;
            }
        }

        return gridApi;
    }

    private readonly setShouldResetTable = (): void => {
        this.dataOperationMode[0] = { 0: DataOperationMode.RESET_TABLE };
    };

    /**
     * Child tables are destroyed by ag-grid upon paginating
     * so we can just collapse them and let the user expand them again
     */
    private readonly onPaginationChanged = (event: PaginationChangedEvent): void => {
        if (event.newPage) {
            callGridMethod(event.api, 'forEachNode', n => {
                if (n.id && n.expanded) {
                    this.getRowNode({ id: n.id, gridApi: event.api })?.setExpanded(false);
                }
            });
        }
    };

    private readonly onOptionsMenuItemChange = (selectedOptionsMenuItem: OptionsMenuItem): void => {
        this.setState(
            {
                selectedOptionsMenuItem,
            },
            () => {
                if (this.gridApi) {
                    // Reset the applied table filter if the option menu changes
                    callGridMethod(this.gridApi, 'setFilterModel', {});
                    // Refetch all data
                    this.setShouldResetTable();
                    callGridMethod(this.gridApi, 'deselectAll');
                    callGridMethod(this.gridApi, 'setGridOption', 'serverSideDatasource', this.serverSideDataSource());
                }
            },
        );
    };

    private readonly getRowClass: GridOptions['getRowClass'] = params =>
        getRowClass(
            params,
            this.props.fieldProperties.isChangeIndicatorDisabled || false,
            this.props.fieldProperties.canSelect || false,
        );

    private readonly isDisabled = (): boolean =>
        this.props.isParentDisabled || isFieldDisabled(this.props.screenId, this.props.fieldProperties, null, null);

    private readonly isReadOnly = (): boolean =>
        isFieldReadOnly(this.props.screenId, this.props.fieldProperties, null, undefined);

    private readonly noRowsOverlayComponentParams: GridOptions['noRowsOverlayComponentParams'] = {
        ...this.props,
        isEditable: !this.isReadOnly() && !this.isDisabled(),
        isTree: false,
    };

    private readonly onCellKeyDown = (event: CellKeyDownEvent): void => {
        onCellKeyDown(event);

        const keyboardEvent = event.event as KeyboardEvent;

        if (event.data?.__phantom && keyboardEvent.key === 'Enter') {
            if (keyboardEvent.getModifierState('Meta') || keyboardEvent.getModifierState('Control')) {
                this.props.onTelemetryEvent?.(`nestedGridPhantomRowCommittedByKeyboard-${this.props.elementId}`, {
                    screenId: this.props.screenId,
                    elementId: this.props.elementId,
                });
                tryToCommitPhantomRow({
                    api: event.api,
                    screenId: this.props.screenId,
                    elementId: this.props.elementId,
                    value: this.props.value,
                });
            } else if (event.column.getColId() === COLUMN_ID_ROW_ACTIONS) {
                this.props.value.resetRowToDefaults({
                    id: event.data._id,
                    level: event.data.__level,
                    parentId: event.data.__parentId,
                    isOrganicChange: true,
                    resetDirtiness: true,
                });
                callGridMethod(event.api, 'stopEditing');
                callGridMethod(event.api, 'setFocusedCell', 0, COLUMN_ID_ROW_ACTIONS, 'top');
            }
        }
    };

    private readonly onCellClicked = async (event: CellClickedEvent): Promise<void> => {
        if (event.data?.__phantom && event.column.getColId() === COLUMN_ID_ROW_ACTIONS) {
            await this.props.value.resetRowToDefaults({
                id: event.data._id,
                level: event.data.__level,
                parentId: event.data.__parentId,
                isOrganicChange: true,
                resetDirtiness: true,
            });
        }
    };

    // private readonly detailCellRenderer = DetailCellRenderer;

    private readonly getRowNode = ({ id, gridApi }: { id: string; gridApi: GridApi }): IRowNode | undefined => {
        let node = callGridMethod(gridApi, 'getRowNode', id);
        if (node) {
            return node;
        }
        node = gridApi.getPinnedTopRow(0);
        if (node?.data?._id === id) {
            return node;
        }
        return undefined;
    };

    private readonly selectionColumnDef = getSelectionColumnDef({
        contextType: this.props.contextType,
        fieldProperties: this.props.fieldProperties,
    });

    private readonly getCommonGridProps = memoize((isDisabled: boolean): AgGridReactProps => {
        return {
            autoSizeStrategy: {
                type: 'fitCellContents',
            },
            cacheBlockSize: this.getCacheBlockSize(),
            components: frameworkComponents,
            defaultColDef: { flex: 1, resizable: false, singleClickEdit: true },
            detailRowAutoHeight: true,
            domLayout: 'autoHeight',
            getRowClass: this.getRowClass,
            getRowId: this.getRowId,
            gridOptions: {
                selectionColumnDef: this.selectionColumnDef,
            },
            headerHeight: 40,
            localeText: localeText(),
            loadingOverlayComponent: 'Loader',
            loadingOverlayComponentParams: { size: 'large' },
            masterDetail: true,
            maxConcurrentDatasourceRequests: 1,
            noRowsOverlayComponent: 'DesktopNestedGridEmptyComponent',
            noRowsOverlayComponentParams: this.noRowsOverlayComponentParams,
            onCellClicked: this.onCellClicked,
            onCellKeyDown: this.onCellKeyDown,
            onCellEditingStopped: this.onCellEditingStopped,
            onFilterChanged: this.onFilterChanged,
            onFirstDataRendered: this.onFirstDataRendered,
            onPaginationChanged: this.onPaginationChanged,
            onRowGroupOpened: this.onRowGroupOpened,
            onSelectionChanged: this.onSelectionChange,
            onSortChanged: this.onSortChanged,
            pagination: true,
            paginationPageSize: this.props.fieldProperties.pageSize ?? 20,
            paginationPageSizeSelector: false,
            rowHeight: ROW_HEIGHT,
            rowModelType: 'serverSide',
            rowSelection: this.state.rowSelectionMode,
            serverSideSortAllLevels: true,
            singleClickEdit: true,
            suppressCellFocus: isDisabled,
            suppressColumnVirtualisation: process.env.NODE_ENV === 'test',
            suppressContextMenu: true,
            suppressPaginationPanel: isDisabled,
            suppressRowClickSelection:
                typeof this.state.rowSelectionMode === 'object' && this.state.rowSelectionMode.mode === 'multiRow',
            undoRedoCellEditing: true,
            valueCacheNeverExpires: true,
        };
    });

    private readonly onRowGroupOpened = (event: RowGroupOpenedEvent): void => {
        const isOpening = event.expanded;
        let childApi = event.api.getDetailGridInfo(`detail_${event.data._id}`)?.api;
        if (!childApi) {
            event.api.addDetailGridInfo(`detail_${event.data._id}`, { id: `detail_${event.data._id}`, api: event.api });
            childApi = event.api.getDetailGridInfo(`detail_${event.data._id}`)?.api;
        }

        if (isOpening && childApi) {
            const { __level: level = 0, _id: parentId } = event.data;
            const headerClasses = get(
                this.collectionValidityChangeSubscriptions,
                [level + 1, parentId ?? 0, 'headerClasses'],
                {},
            );
            // Set data source for nested grid
            childApi.setGridAriaProperty('grid', `nested-grid-${level + 1}-${parentId}`);
            childApi.setGridOption('serverSideDatasource', { getRows: this.getRows(level + 1, parentId) });
            setNestedGridContext(childApi, c => {
                c.level = level + 1;
                c.parentId = parentId;
                c.parentApi = event.api;

                /**
                 * Context is destroyed whenever a grid is collapsed.
                 * Restore all header classes from previous subscriptions.
                 */
                c.headerClasses = headerClasses;
            });
            childApi.refreshHeader();
        }
    };

    private readonly onFilterChanged = (event: FilterChangedEvent): void => {
        const { level, parentId } = getNestedGridContext(event.api);
        setWith(this.dataOperationMode, [level ?? 0, parentId ?? 0], DataOperationMode.RESET_TABLE, Object);
    };

    private readonly onSortChanged = (event: SortChangedEvent): void => {
        const { level, parentId } = getNestedGridContext(event.api);
        setWith(this.dataOperationMode, [level ?? 0, parentId ?? 0], DataOperationMode.RESET_TABLE, Object);
    };

    private readonly focusPhantomRowAndStartEditing = (gridApi: GridApi): void => {
        setTimeout(async () => {
            const { level, parentId } = getNestedGridContext(gridApi);
            const phantomRecords = await this.props.value.getPhantomRecords({ level, parentId });
            const colKey = getFirstEditableColumn(gridApi, phantomRecords?.[0]);
            if (colKey === undefined) {
                return;
            }
            callGridMethod(gridApi, 'setFocusedCell', 0, colKey, 'top');
            callGridMethod(gridApi, 'ensureColumnVisible', colKey);
            callGridMethod(gridApi, 'startEditingCell', {
                colKey,
                rowIndex: 0,
                key: 'Enter',
                rowPinned: 'top',
            });
        }, 50);
    };

    private readonly onCollectionUpdated = (type: RecordActionType, rowValue: any): void => {
        try {
            if (this.gridApi) {
                const path: string[] = [];
                let currentNode = rowValue;
                while (currentNode.__parentId) {
                    path.push(currentNode.__parentId);
                    currentNode = Object.prototype.hasOwnProperty.call(currentNode, '__level')
                        ? this.props.value.getRecordByIdAndLevel({
                              id: currentNode.__parentId,
                              level: currentNode.__level - 1,
                          })
                        : this.props.value.getRecordByIdAndLevel({ id: currentNode.__parentId });
                }
                let gridApi = this.gridApi;
                path.toReversed().forEach(p => {
                    const gridInfo = gridApi.getDetailGridInfo(`detail_${p}`);
                    if (!gridInfo || !gridInfo.api) {
                        throw new Error(`Could not find detail grid detail_${p}`);
                    }
                    gridApi = gridInfo.api;
                });
                switch (type) {
                    case RecordActionType.MODIFIED:
                        setWith(
                            this.dataOperationMode,
                            [rowValue.__level ?? 0, rowValue.__parentId ?? 0],
                            DataOperationMode.FETCH_PAGE,
                            Object,
                        );

                        if (rowValue?.__phantom && rowValue?.__forceRowUpdate) {
                            delete rowValue.__forceRowUpdate;
                            gridApi.setGridOption('pinnedTopRowData', [rowValue]);
                        } else {
                            const node = this.getRowNode({ id: rowValue._id, gridApi });
                            if (!node) {
                                throw new Error(`Could not update node ${JSON.stringify(rowValue)}`);
                            }
                            node.setData(rowValue);
                        }
                        return;
                    case RecordActionType.ADDED:
                        if (rowValue?.__phantom !== undefined) {
                            gridApi.setGridOption('pinnedTopRowData', [rowValue]);
                            this.focusPhantomRowAndStartEditing(gridApi);
                        } else {
                            setWith(
                                this.dataOperationMode,
                                [rowValue.__level ?? 0, rowValue.__parentId ?? 0],
                                DataOperationMode.FETCH_PAGE,
                                Object,
                            );
                            const nodes = this.props.value.getData({
                                noLimit: true,
                                level: rowValue.__level ?? 0,
                                parentId: rowValue.__parentId ?? 0,
                            });
                            const addIndex = nodes.findIndex(n => n._id === rowValue._id);
                            callGridMethod(gridApi, 'applyServerSideTransaction', {
                                add: [rowValue],
                                addIndex: addIndex === -1 ? 0 : addIndex,
                            });
                        }

                        break;
                    case RecordActionType.REMOVED:
                        setWith(
                            this.dataOperationMode,
                            [rowValue.__level ?? 0, rowValue.__parentId ?? 0],
                            DataOperationMode.REMOVE_RECORD,
                            Object,
                        );
                        callGridMethod(gridApi, 'applyServerSideTransaction', {
                            remove: [rowValue],
                        });
                        break;
                    default:
                        break;
                }

                const tableHasData =
                    this.props.value &&
                    this.props.value.getData &&
                    this.props.value.getData({ level: rowValue.__level, parentId: rowValue.__parentId }).length > 0;
                if ((rowValue.__level ?? 0) === 0) {
                    this.setState({ tableHasData });
                }
                this.showHideNoRowsOverlay(gridApi, !tableHasData);
            }
        } catch (err) {
            xtremConsole.warn(
                `UI update skipped. Either row ${JSON.stringify(
                    rowValue ? splitValueToMergedValue(rowValue) : rowValue,
                )} is not displayed or you need to check the following error: ${err}`,
            );
        }
    };

    private readonly getCurrentlySelectedRecords = (): NestedGridProperties['selectedRecords'] => {
        return (
            getStore().getState().screenDefinitions[this.props.screenId].metadata.uiComponentProperties[
                this.props.elementId
            ] as NestedGridProperties
        ).selectedRecords;
    };

    private readonly isRecordSelected = ({ level, id }: { level: number; id: string }): boolean => {
        return (get(this.getCurrentlySelectedRecords(), level, []) as string[]).includes(id);
    };

    /** Marks items selected on the nestedGrid, this function is triggered by changes initiated by a pagination or reset
     * event
     **/
    private readonly selectNestedGridItems = (getRowsParams: IServerSideGetRowsParams): void => {
        if (typeof this.state.rowSelectionMode === 'object' && this.state.rowSelectionMode.mode === 'multiRow') {
            const { level = 0, parentId } = getNestedGridContext(getRowsParams.api);
            const selectedRecords = get(this.getCurrentlySelectedRecords(), level, []);
            if (parentId && this.isRecordSelected({ level: level - 1, id: parentId })) {
                // parent is selected, select all descendants
                callGridMethod(getRowsParams.api, 'forEachNode', n => {
                    if (n.data?._id) {
                        this.selectUnselectAllChildren({
                            api: (n as any).beans.gos.api,
                            rowId: n.data._id,
                            select: true,
                        });
                    }
                });
            } else {
                // select previously selected direct children
                selectedRecords.forEach(item => {
                    const rowNode = this.getRowNode({ id: item, gridApi: getRowsParams.api });
                    if (rowNode) {
                        rowNode.setSelected(true);
                    }
                });
            }
        }
    };

    private readonly getParentNode = (api: GridApi): IRowNode | undefined => {
        const { parentId, parentApi } = getNestedGridContext(api);
        if (parentApi === undefined || parentId === undefined) {
            return undefined;
        }
        return this.getRowNode({ id: parentId, gridApi: parentApi });
    };

    private readonly unselectParentsRecursive = (scopedApi: GridApi, scopedLevel: number): void => {
        const unselectRec = (api: GridApi | undefined, level: number): void => {
            if (!api) {
                return;
            }

            const parentNode = this.getParentNode(api);
            if (!parentNode) {
                return;
            }
            const parentNodeApi = (parentNode as any).beans.gos.api as GridApi;
            const parentNodeDetailApi = parentNodeApi.getDetailGridInfo(`detail_${parentNode.data._id}`)?.api;
            const totalRows = (parentNodeDetailApi?.getRenderedNodes()?.filter(n => !n.detail) || []).length;
            const totalSelected = (parentNodeDetailApi?.getSelectedNodes()?.filter(n => !n.detail) || []).length;
            if (
                parentNode &&
                this.isRecordSelected({ level: level - 1, id: parentNode.data._id }) &&
                totalSelected < totalRows
            ) {
                this.unselectRow(parentNode);
                const selectedRecords: any = this.getCurrentlySelectedRecords()
                    ? cloneDeep(this.getCurrentlySelectedRecords())
                    : this.props.fieldProperties.levels.map(() => []);
                set(
                    selectedRecords,
                    level - 1,
                    get(this.getCurrentlySelectedRecords(), level - 1, [] as string[]).filter(
                        (r: string) => r !== parentNode.data._id,
                    ),
                );
                if (this.props.setFieldProperties) {
                    this.props.setFieldProperties(this.props.elementId, {
                        ...this.props.fieldProperties,
                        selectedRecords,
                    });
                }

                unselectRec(parentNodeApi, level - 1);
            }
        };
        // Unselect parent node
        const parent = this.getParentNode(scopedApi);
        if (!parent) {
            return;
        }
        const isParentSelected = this.isRecordSelected({ level: parent.data.__level || 0, id: parent.data._id });
        if (isParentSelected) {
            this.unselectRow(parent);
            const selectedRecords: any = this.getCurrentlySelectedRecords()
                ? cloneDeep(this.getCurrentlySelectedRecords())
                : this.props.fieldProperties.levels.map(() => []);
            set(
                selectedRecords,
                scopedLevel - 1,
                get(this.getCurrentlySelectedRecords(), scopedLevel - 1, [] as string[]).filter(
                    (r: string) => r !== parent.data._id,
                ),
            );
            if (this.props.setFieldProperties) {
                this.props.setFieldProperties(this.props.elementId, {
                    ...this.props.fieldProperties,
                    selectedRecords,
                });
            }
            // Possibly unselect parent nodes
            unselectRec((parent as any).beans.gos.api, scopedLevel - 1);
        }
    };

    private readonly selectParentsRecursive = (api: GridApi, level: number): void => {
        const selectNode = (node: IRowNode): void => {
            const selectedRecords: any = this.getCurrentlySelectedRecords()
                ? cloneDeep(this.getCurrentlySelectedRecords())
                : this.props.fieldProperties.levels.map(() => []);
            selectedRecords[node.data.__level ?? 0].push(node.data._id);
            if (this.props.setFieldProperties) {
                this.props.setFieldProperties(this.props.elementId, {
                    ...this.props.fieldProperties,
                    selectedRecords,
                });
            }
        };

        const shouldSelectNode = (node: IRowNode): boolean => {
            if (!node) {
                return false;
            }
            const nodeApi = (node as any).beans.gos.api as GridApi;
            const nodeDetailApi = nodeApi.getDetailGridInfo(`detail_${node.data._id}`)?.api;
            const totalRows = (nodeDetailApi?.getRenderedNodes()?.filter(n => !n.detail) || []).length;
            const totalSelected = (nodeDetailApi?.getSelectedNodes()?.filter(n => !n.detail) || []).length;
            return (
                !this.isRecordSelected({ level: node.data.__level ?? 0, id: node.data._id }) &&
                totalRows === totalSelected
            );
        };

        // Recursive function to select parent nodes
        const selectRec = (scopedApi: GridApi | undefined, level: number): void => {
            if (!scopedApi) {
                return;
            }
            const parentNode = this.getParentNode(scopedApi);
            if (!parentNode) return;
            if (shouldSelectNode(parentNode)) {
                this.selectRow(parentNode);
                selectNode(parentNode);
                const parentNodeApi = (parentNode as any).beans.gos.api as GridApi;
                selectRec(parentNodeApi, level - 1);
            }
        };

        // Begin with the parent node
        const parent = this.getParentNode(api);
        if (!parent) {
            return;
        }
        if (shouldSelectNode(parent)) {
            this.selectRow(parent);
            selectNode(parent);
            selectRec((parent as any).beans.gos.api, level - 1);
        }
    };

    private readonly showHideNoRowsOverlay = (gridApi: GridApi, show: boolean): void => {
        setTimeout(() => {
            if (show) {
                callGridMethod(gridApi, 'showNoRowsOverlay');
            } else {
                callGridMethod(gridApi, 'hideOverlay');
            }
        }, 100);
    };

    private readonly insertDataSourceIntoNestedGrid = (
        rowData: any[],
        getRowsParams: IServerSideGetRowsParams,
        rowCount: number | undefined,
    ): void => {
        getRowsParams.success({ rowData, rowCount });
        callGridMethod(getRowsParams.api, 'hideOverlay');
        this.showHideNoRowsOverlay(getRowsParams.api, rowCount === 0);
    };

    private getCursor(api: GridApi): string | undefined {
        return callGridMethod(api, 'getDisplayedRowAtIndex', (callGridMethod(api, 'getLastDisplayedRow') ?? 1) - 1)
            ?.data?.__cursor;
    }

    /** Marks items selected on the table, this function is triggered by changes initiated by a pagination or reset
     * event
     **/
    private readonly selectTableItems = (): void => {
        // Marking user selected rows, selected in the new dataset
        const selectedRecordLevels = this.getCurrentlySelectedRecords() || [];

        selectedRecordLevels.forEach((items: string[], level: number): void => {
            items.forEach(id => {
                const path = this.props.value.getIdPathToNestedRecord(id, level);
                const gridApi = this.getGridApiByRecordPath(path);
                const rowNode = this.getRowNode({ id, gridApi });
                if (rowNode) {
                    rowNode.setSelected(true);
                }
            });
        });
    };

    private readonly onCellEditingStopped = async (event: CellEditingStoppedEvent): Promise<void> => {
        const isPhantom = event.data?.__phantom !== undefined;
        if (!isPhantom) {
            return;
        }
        if (event.value !== event.oldValue && event.colDef.field) {
            const columnId = event.colDef.field;
            const newValue = event.data[event.colDef.field];
            let mergedValue = this.props.value.getMergedValue({
                recordId: event.data._id,
                columnId,
                value: newValue,
                level: event.data.__level,
            });
            const dirtyColumns = new Set([...mergedValue.__dirtyColumns, columnId]);
            const validationResult = await this.props.value.runValidationOnRecord({
                recordData: {
                    ...mergedValue,
                    __dirtyColumns: dirtyColumns,
                },
                changedColumnId: event.colDef.field,
            });

            mergedValue = this.props.value.getMergedValue({
                recordId: event.data._id,
                columnId,
                value: newValue,
                level: event.data.__level,
            });

            const updatedData = {
                ...mergedValue,
                __validationState: validationResult,
            };

            this.props.value.setRecordValue({
                recordData: updatedData,
                level: event.data.__level,
                toBeMarkedAsDirty: Array.from(dirtyColumns),
                isOrganicChange: true,
                changedColumnId: columnId,
            });
        }
    };

    private readonly onValidityChange =
        ({ api, level, parentId }: { api: GridApi; level: number; parentId?: string }) =>
        ({
            globalValidationState,
            recordId: validationRecordId,
            recordLevel: validationLevel,
            recordValidationState,
        }: Parameters<ValidityChangeSubscriptionCallback>[0]): void => {
            if (
                api &&
                validationLevel === level &&
                !api.isDestroyed() &&
                this.getRowNode({ id: validationRecordId, gridApi: api })
            ) {
                // Current grid's node IDs
                const nodeIds: string[] = [];
                callGridMethod(api, 'forEachNode', n => {
                    if (n && n.data) {
                        nodeIds.push(n.data._id);
                    }
                });
                /**
                 * A given level generally has multiple tables
                 * so make sure a given column is invalid for at least
                 * a row that belongs to the current table.
                 */
                const invalidColumns = objectKeys(globalValidationState[level]).filter(k =>
                    globalValidationState[level][k].some(e => e.recordId && nodeIds.includes(e.recordId)),
                );
                // Mark colum as invalid
                const headerClasses = invalidColumns.reduce((acc, column) => {
                    acc[column] = false;
                    return acc;
                }, {} as Dict<boolean>);
                // Set header classes into the context
                setNestedGridContext(api, c => {
                    c.headerClasses = headerClasses;
                });
                setWith(
                    this.collectionValidityChangeSubscriptions,
                    [level, parentId ?? 0],
                    {
                        unsubscribe: get(this.collectionValidityChangeSubscriptions, [
                            level,
                            parentId ?? 0,
                            'unsubscribe',
                        ]),
                        headerClasses,
                    },
                    Object,
                );
                // Imperatively refresh headers to that they are re-rendered with the appropriate classes
                callGridMethod(api, 'refreshHeader');
                // Display/hide validation column
                const isValidationColumnVisible = Boolean(
                    callGridMethod(api, 'getColumn', COLUMN_ID_VALIDATIONS)?.isVisible(),
                );
                const shouldValidationColumnBeVisible = invalidColumns.length > 0;
                if (isValidationColumnVisible !== shouldValidationColumnBeVisible) {
                    callGridMethod(api, 'setColumnVisible', COLUMN_ID_VALIDATIONS, shouldValidationColumnBeVisible);
                    this.autoSizeAllColumns({ gridApi: api });
                }
                const rowNode = this.getRowNode({ id: validationRecordId, gridApi: api });
                if (rowNode) {
                    const previousValidationState = objectKeys(rowNode.data.__validationState || {});
                    rowNode.setData({
                        ...this.getRowNode({ id: validationRecordId, gridApi: api })?.data,
                        __validationState: recordValidationState,
                    });

                    // Refresh cell
                    callGridMethod(api, 'refreshCells', {
                        rowNodes: [rowNode],
                        columns: difference(previousValidationState, objectKeys(recordValidationState)),
                        force: true,
                    });
                }
            }
        };

    private readonly subscribeToValidationChanges = ({
        api,
        level,
        parentId,
    }: {
        api: GridApi;
        level: number;
        parentId?: string;
    }): void => {
        const existingValidationSubscription = get(this.collectionValidityChangeSubscriptions, [level, parentId ?? 0]);
        /**
         * The given api is subject to changes (i.e. ag-grid creates & destroys it when expanding/collapsing)
         * so we cancel previous subscription & create a new one keeping all previous header classes.
         */
        if (existingValidationSubscription) {
            existingValidationSubscription.unsubscribe();
        }
        const validationSubscription = this.props.value.subscribeForValidityChanges(
            this.onValidityChange({ api, level, parentId }),
        );
        setWith(
            this.collectionValidityChangeSubscriptions,
            [level, parentId ?? 0],
            {
                unsubscribe: validationSubscription,
                headerClasses: get(this.collectionValidityChangeSubscriptions, [level, parentId ?? 0, 'headerClasses']),
            },
            Object,
        );
    };

    private readonly setPhantomRow = async ({
        level,
        parentId,
        gridApi,
    }: {
        level: number;
        parentId?: string;
        gridApi: GridApi;
    }): Promise<void> => {
        const phantomRows = this.props.fieldProperties.levels[level].canAddNewLine
            ? await this.props.value?.getPhantomRecords({ level, parentId })
            : [];
        // Points to the first element to avoid race conditions that could return more than 1 row:
        const newPhantomRows = phantomRows[0] ? [phantomRows[0]] : [];
        const currentPhantomRows = gridApi.getGridOption('pinnedTopRowData') ?? [];
        if (!isEqual(currentPhantomRows, newPhantomRows)) {
            gridApi.setGridOption('pinnedTopRowData', newPhantomRows);
        }
    };

    private readonly getRowCount = ({
        data,
        startRow,
        endRow,
        level,
    }: Pick<IServerSideGetRowsParams['request'], 'startRow' | 'endRow'> & {
        data: CollectionValueType[];
        level: number;
    }): number | undefined => {
        const offset = (endRow || 0) - (startRow || 0);

        if (data.length < offset) {
            return (startRow || 0) + data.length;
        }

        const fallbackRowCount = level > 0 ? undefined : this.getUnknownLastRow();

        return fallbackRowCount;
    };

    /**
     * Ag-grid callback which is triggered whenever the nestedGrid component needs new data from our framework
     * @param getRowsParams
     */
    private readonly getRows =
        (level: number, parentId?: string) =>
        async (getRowsParams: IServerSideGetRowsParams): Promise<void> => {
            const pageSize = callGridMethod(getRowsParams.api, 'paginationGetPageSize') ?? 20;
            const pageNumber = Math.max(0, Math.ceil((getRowsParams.request.endRow || 0) / pageSize) - 1);
            callGridMethod(getRowsParams.api, 'hideOverlay');

            if (!this.props.value) {
                this.insertDataSourceIntoNestedGrid(
                    [],
                    getRowsParams,
                    this.getRowCount({
                        data: [],
                        startRow: getRowsParams.request.startRow,
                        endRow: getRowsParams.request.endRow,
                        level,
                    }),
                );
                return;
            }

            if (!this.isInfiniteScroll() || level > 0) {
                callGridMethod(getRowsParams.api, 'showLoadingOverlay');
            }

            try {
                switch (get(this.dataOperationMode, [level, parentId ?? 0], DataOperationMode.FETCH_PAGE)) {
                    case DataOperationMode.NONE:
                        /**
                         * The top-level grid in initialized in 'DataOperationMode.NONE' mode
                         * so this state is appropriate for subscribing.
                         */
                        if (level === 0) {
                            this.subscribeToValidationChanges({ api: getRowsParams.api, level, parentId });
                        }
                        const data = this.props.value.getData({
                            cleanMetadata: false,
                            level,
                            parentId,
                            limit: pageSize,
                        });
                        this.insertDataSourceIntoNestedGrid(
                            data,
                            getRowsParams,
                            this.getRowCount({
                                data,
                                startRow: getRowsParams.request.startRow,
                                endRow: getRowsParams.request.endRow,
                                level,
                            }),
                        );
                        this.collectionValueChangeSubscription = this.props.value.subscribeForValueChanges(
                            this.onCollectionUpdated,
                        );
                        setTimeout(() => this.autoSizeAllColumns({ gridApi: getRowsParams.api }));
                        break;
                    case DataOperationMode.FETCH_PAGE:
                        /**
                         * Nested grids are initialized in 'DataOperationMode.FETCH_PAGE' mode
                         * so this state is appropriate for subscribing.
                         */
                        if (level > 0) {
                            this.subscribeToValidationChanges({ api: getRowsParams.api, level, parentId });
                        }
                        const newData = await this.props.value.getPageWithCurrentQueryArguments({
                            tableFieldProperties: {
                                ...this.props.fieldProperties,
                                columns: this.props.fieldProperties.levels[level].columns,
                            } as GridLodable,
                            pageSize,
                            pageNumber,
                            level,
                            parentId,
                            cleanMetadata: false,
                            cursor: this.getCursor(getRowsParams.api),
                        });

                        this.insertDataSourceIntoNestedGrid(
                            newData,
                            getRowsParams,
                            this.getRowCount({
                                data: newData,
                                startRow: getRowsParams.request.startRow,
                                endRow: getRowsParams.request.endRow,
                                level,
                            }),
                        );
                        if (getRowsParams.request.startRow === 0 && level > 0 && parentId) {
                            triggerNestedFieldEvent(
                                this.props.screenId,
                                this.props.elementId,
                                this.props.fieldProperties.levels[level - 1] as NestedFieldComponentProperties,
                                'onLevelExpanded',
                                parentId,
                                newData,
                            );
                        }
                        this.selectNestedGridItems(getRowsParams);
                        break;
                    case DataOperationMode.REMOVE_RECORD:
                        callGridMethod(getRowsParams.api, 'deselectAll');

                        // Populating values
                        const mergedData = await this.props.value.getRecordWithCurrentQueryArguments({
                            tableFieldProperties: {
                                ...this.props.fieldProperties,
                                columns: this.props.fieldProperties.levels[level].columns,
                            } as GridLodable,
                            pageSize,
                            pageNumber,
                            level,
                            parentId,
                            cursor: this.getCursor(getRowsParams.api),
                            cleanMetadata: false,
                        });
                        this.insertDataSourceIntoNestedGrid(
                            mergedData,
                            getRowsParams,
                            this.getRowCount({
                                data: mergedData,
                                startRow: getRowsParams.request.startRow,
                                endRow: getRowsParams.request.endRow,
                                level,
                            }),
                        );
                        this.selectNestedGridItems(getRowsParams);
                        break;
                    case DataOperationMode.RESET_TABLE:
                        callGridMethod(getRowsParams.api, 'deselectAll');

                        // Map Ag-grid sorting properties
                        const orderBy = getOrderByFromSortModel(
                            getRowsParams.request.sortModel,
                            this.props.fieldProperties.levels[level].columns,
                        );

                        // Map Ag-grid filter objects into GraphQL/MongoDB friendly filter data structure
                        const userFilters = getRowsParams.request.filterModel
                            ? objectKeys(getRowsParams.request.filterModel).map(
                                  mapAgGridFilterToXtremFilters(getRowsParams.request.filterModel),
                              )
                            : undefined;

                        // Populating values
                        const pageData = await this.props.value.getPage({
                            tableFieldProperties: {
                                ...this.props.fieldProperties,
                                columns: this.props.fieldProperties.levels[level].columns,
                            } as GridLodable,
                            filters: userFilters,
                            orderBy,
                            pageSize: (getRowsParams.request.endRow || 0) - (getRowsParams.request.startRow || 0),
                            level,
                            parentId,
                            cursor: this.getCursor(getRowsParams.api),
                            cleanMetadata: false,
                            selectedOptionsMenuItem: this.state.selectedOptionsMenuItem,
                        });
                        this.insertDataSourceIntoNestedGrid(
                            pageData,
                            getRowsParams,
                            this.getRowCount({
                                data: pageData,
                                startRow: getRowsParams.request.startRow,
                                endRow: getRowsParams.request.endRow,
                                level,
                            }),
                        );
                        this.selectNestedGridItems(getRowsParams);
                        break;
                    default:
                        throw new Error(`Unexpected 'dataOperationMode': ${this.dataOperationMode}`);
                }
                if (
                    this.props.value.getData({
                        cleanMetadata: false,
                        level: 0,
                        parentId,
                    }).length > 0
                ) {
                    this.setState({ tableHasData: true });
                } else {
                    this.setState({ tableHasData: false });
                }

                setWith(this.dataOperationMode, [level, parentId ?? 0], DataOperationMode.FETCH_PAGE, Object);
            } catch (error) {
                xtremConsole.log(error);
                getRowsParams.fail();
            } finally {
                await this.setPhantomRow({ level, parentId, gridApi: getRowsParams.api });
            }
        };

    private readonly serverSideDataSource = (): IServerSideDatasource => ({
        getRows: this.getRows(0),
    });

    /**
     * Ag-grid lifecycle event listener. It is triggered when the nestedGrid is prepared with the initial rendering of the
     * controller components and is ready to receive data.
     **/
    private readonly onGridReady = (params: GridReadyEvent): void => {
        this.gridApi = params.api;
        callGridMethod(params.api, 'setGridOption', 'serverSideDatasource', this.serverSideDataSource());
    };

    /** Event listener, triggered when the user clicks on any part of the row apart from the dropdown actions */
    private readonly onRowClick = (event: RowClickedEvent): void => {
        // Filtering out dropdown action clicks
        const eventPath: HTMLElement[] = (event && event.event && (event.event as any).path) || [];
        const isDropdownAction = !!eventPath.find(
            p => p.classList && Array.from(p.classList).indexOf('e-nested-grid-field-dropdown-actions') !== -1,
        );

        const isInlineAction = !!eventPath.find(
            p => p.classList && Array.from(p.classList).indexOf(TABLE_INLINE_ACTION_CSS_CLASS) !== -1,
        );

        // Row action is only triggered if the row has data and was not triggered by clicking on the dropdown actions menu
        if (this.props.onRowClick && event.data && !isDropdownAction && !isInlineAction) {
            this.props.onRowClick(event.data._id, event.data.__level ?? 0);
        }
    };

    /**
     * Gets the IDs of all rows that have been selected.
     *
     * @private
     * @memberof DesktopNestedGridComponent
     */
    private readonly getSelectedNodes = (api: GridApi): Dict<IRowNode> => {
        let selected: Dict<IRowNode> = {};

        const addNodes = (nodes: IRowNode[]): void => {
            selected = nodes.reduce((acc, node) => {
                // cache by compisite key as the same _id could be present across different levels
                acc[node.data.__compositeKey] = node;
                return acc;
            }, selected);
        };

        const getNodes = (gridApi: GridApi): void => {
            getSafeGridApiContext(
                selectedNodes => {
                    addNodes(selectedNodes);
                    callGridMethod(gridApi, 'forEachDetailGridInfo', d => {
                        if (d.api) {
                            getNodes(d.api);
                        }
                    });
                },
                gridApi,
                'getSelectedNodes',
            );
        };

        getNodes(api);

        return selected;
    };

    private readonly getPreviouslySelectedItemIds = (level: number): string[] =>
        get(this.getCurrentlySelectedRecords(), level, []) as string[];

    /**
     * Gets the IDs of all the rows that have been unselected.
     *
     * @private
     * @memberof DesktopNestedGridComponent
     */
    private readonly getUnselectedIds = ({
        level,
        newSelectionIdSet,
        parentId,
    }: {
        level: number;
        parentId?: string;
        newSelectionIdSet: string[];
    }): string[] => {
        const previousSet = this.getPreviouslySelectedItemIds(level)
            .map(id => this.props.value.getRawRecord({ id, level, cleanMetadata: false }))
            .filter(n => n && n.__parentId === parentId)
            .map(n => n._id);
        return difference(previousSet, newSelectionIdSet);
    };

    private readonly selectRow = (node: IRowNode): void => {
        const rawRecord = this.props.value.getRawRecord({
            id: node.data._id,
            level: node.data.__level ?? 0,
            cleanMetadata: false,
        });
        node.setSelected(true);

        const eventHandler = this.state.rowSelectionMode === 'single' ? 'onRowActivated' : 'onRowSelected';
        triggerFieldEvent(
            this.props.screenId,
            this.props.elementId,
            eventHandler as 'onRowActivated' & 'onRowSelected',
            rawRecord._id,
            cleanMetadataFromRecord(rawRecord),
            rawRecord.__level ?? 0,
        );
    };

    private readonly unselectRow = (node: IRowNode): void => {
        const rawRecord = this.props.value.getRawRecord({
            id: node.data._id,
            level: node.data.__level ?? 0,
            cleanMetadata: false,
        });
        node.setSelected(false);

        const eventHandler = this.state.rowSelectionMode === 'single' ? 'onRowDeactivated' : 'onRowUnselected';
        triggerFieldEvent(
            this.props.screenId,
            this.props.elementId,
            eventHandler as 'onRowDeactivated' & 'onRowUnselected',
            rawRecord._id,
            cleanMetadataFromRecord(rawRecord),
            rawRecord.__level ?? 0,
        );
    };

    private readonly getNewlySelectedRowIds = ({
        level,
        selectedNodes,
    }: {
        level: number;
        selectedNodes: Dict<IRowNode>;
    }): string[] => {
        return difference(objectKeys(selectedNodes), this.getPreviouslySelectedItemIds(level));
    };

    private readonly selectUnselectAllChildren = ({
        api,
        rowId,
        select,
    }: {
        api?: GridApi;
        rowId: string;
        select: boolean;
    }): void => {
        if (!api) {
            return;
        }
        const selectedRecords: any = this.getCurrentlySelectedRecords()
            ? cloneDeep(this.getCurrentlySelectedRecords())
            : this.props.fieldProperties.levels.map(() => []);
        const selectUnselect = ({
            api,
            rowId,
            select,
            level,
        }: {
            api?: GridApi | 'none';
            rowId: string;
            select: boolean;
            level: number;
        }): void => {
            if (!api) {
                return;
            }
            const detailGridApi = api !== 'none' ? api.getDetailGridInfo(`detail_${rowId}`)?.api : undefined;
            if (detailGridApi) {
                detailGridApi.forEachNode(n => {
                    const actualLevel = n.data.__level || 0;
                    selectUnselect({
                        api: (n as any).beans.gos.api,
                        rowId: n.data._id,
                        select,
                        level: actualLevel + 1,
                    });
                    if (select) {
                        if (!this.isRecordSelected({ level: actualLevel, id: n.data._id })) {
                            this.selectRow(n);
                            set(selectedRecords, actualLevel, [...get(selectedRecords, actualLevel, []), n.data._id]);
                        } else if (!n.isSelected()) {
                            n.setSelected(true);
                        }
                    } else {
                        // eslint-disable-next-line no-lonely-if
                        if (this.isRecordSelected({ level: actualLevel, id: n.data._id })) {
                            this.unselectRow(n);
                            set(
                                selectedRecords,
                                actualLevel,
                                get(selectedRecords, actualLevel, []).filter((r: string) => r !== n.data._id),
                            );
                        } else if (n.isSelected()) {
                            n.setSelected(false);
                        }
                    }
                });
            } else {
                // detail grid is not expanded, update selectedRecords anyway
                const nestedRecords = this.props.value.getNestedGrid({ level: level + 1, parentId: rowId });
                nestedRecords.forEach(n => {
                    const actualLevel = n.__level || 0;
                    selectUnselect({ api: 'none', rowId: n._id, select, level: actualLevel + 1 });
                    if (select) {
                        set(selectedRecords, actualLevel, [...get(selectedRecords, actualLevel, []), n._id]);
                    } else {
                        set(
                            selectedRecords,
                            actualLevel,
                            get(selectedRecords, actualLevel, []).filter((r: string) => r !== n._id),
                        );
                    }
                    const eventHandler = select ? 'onRowSelected' : 'onRowUnselected';
                    triggerFieldEvent(
                        this.props.screenId,
                        this.props.elementId,
                        eventHandler as 'onRowSelected' & 'onRowUnselected',
                        n._id,
                        cleanMetadataFromRecord(n),
                        n.__level ?? 0,
                    );
                });
            }
        };
        const level: number = getNestedGridContext(api)?.level ?? 0;
        selectUnselect({ api, rowId, select, level });
        // select first row
        const rowNode = this.getRowNode({ id: rowId, gridApi: api });
        if (!rowNode) {
            return;
        }
        if (select) {
            if (!this.isRecordSelected({ level, id: rowId })) {
                this.selectRow(rowNode);
                set(selectedRecords, level, uniq([...get(selectedRecords, level, []), rowId]));
            } else if (!rowNode.isSelected()) {
                rowNode.setSelected(true);
            }
        } else {
            // eslint-disable-next-line no-lonely-if
            if (this.isRecordSelected({ level, id: rowId })) {
                const n = this.getRowNode({ id: rowId, gridApi: api });
                if (n) {
                    this.unselectRow(n);
                    set(
                        selectedRecords,
                        level,
                        get(selectedRecords, level, []).filter((r: string) => r !== rowId),
                    );
                }
            } else if (rowNode.isSelected()) {
                rowNode.setSelected(false);
            }
        }

        if (this.props.setFieldProperties) {
            this.props.setFieldProperties(this.props.elementId, {
                ...this.props.fieldProperties,
                selectedRecords,
            });
        }
    };

    /**
     * Event listener, triggered when the user clicks the checkbox on the front of the row. Both for selection and
     * deselection.
     * */
    private readonly onSelectionChange = (event: SelectionChangedEvent): void => {
        if (!this.props.setFieldProperties || event.source.startsWith('api')) {
            return;
        }
        const level = getNestedGridContext(event.api)?.level;
        const actualLevel = level ?? 0;
        if (this.state.rowSelectionMode === 'single') {
            const [toSelect, toUnselect] = partition(
                Object.values(this.getSelectedNodes(this.gridApi)),
                s => s.data.__level === level,
            );
            toSelect.forEach(s => {
                this.selectRow(s);
            });
            toUnselect.forEach(u => {
                this.unselectRow(u);
            });
            const selectedRecords = set(
                this.props.fieldProperties.levels.map(() => []),
                actualLevel,
                sortBy(toSelect.map(s => s.data._id)),
            ) as any;
            if (this.props.setFieldProperties) {
                this.props.setFieldProperties(this.props.elementId, {
                    ...this.props.fieldProperties,
                    selectedRecords,
                });
            }
            return;
        }
        const parentId = getNestedGridContext(event.api)?.parentId;
        const allSelectedNodes = this.getSelectedNodes(event.api);
        const allSelectedNodeValues = Object.values(allSelectedNodes);
        const selectedNodes = allSelectedNodeValues
            .filter(s => s.data.__level === level)
            .reduce<Dict<IRowNode>>((acc, selected) => {
                acc[selected.data._id] = selected;
                return acc;
            }, {});
        const newSelectionIdSet = objectKeys(selectedNodes);
        const newSelectionSet = this.getNewlySelectedRowIds({ level: actualLevel, selectedNodes });
        newSelectionSet.forEach(selected => {
            this.selectUnselectAllChildren({ api: event.api, rowId: selected, select: true });
        });
        const newUnselectedSet = this.getUnselectedIds({ level: actualLevel, newSelectionIdSet, parentId });
        newUnselectedSet.forEach(unselected => {
            this.selectUnselectAllChildren({ api: event.api, rowId: unselected, select: false });
        });
        if (newSelectionSet.length > 0) {
            this.selectParentsRecursive(event.api, actualLevel);
        }

        if (newUnselectedSet.length > 0) {
            this.unselectParentsRecursive(event.api, actualLevel);
        }
    };

    private readonly getRowId: GridOptions<{ _id: string }>['getRowId'] = ({ data }): string => data._id;

    /**
     * This function gets called whenever a row gets expanded.
     * This is where the sever-side data source is set.
     * Information about the grid's level as well as its parent ID & parent grid api is added to ag-grid's context
     * and is used for filtering, sorting and selection.
     *
     * @private
     * @param {GetDetailRowDataParams} { successCallback, ...params }
     * @memberof DesktopNestedGridComponent
     */
    private readonly getDetailRowData = async ({ successCallback }: GetDetailRowDataParams): Promise<void> => {
        /**
         * Nested grid is still not available at this point
         * Resolve with emppty array and set server-side
         * data source in "onRowGroupOpened"
         **/
        successCallback([]);
    };

    private readonly getDetailCellRendererParams = (): void => {
        const detailCellRendererParams = this.props.fieldProperties.levels.reduce(
            (prev, curr, index, arr) => {
                if (index <= 0) {
                    return prev;
                }
                const columnsData = curr.columns.map(columnDefinition => ({
                    columnDefinition,
                    bind: convertDeepBindToPathNotNull(columnDefinition.properties.bind as PropertyValueType),
                    elementId: getNestedFieldElementId(columnDefinition),
                }));
                const fieldProperties: Parameters<typeof getColumns>[0]['fieldProperties'] = () => ({
                    ...this.props.fieldProperties,
                    ...curr,
                    ...(this.state.rowSelectionMode === 'single' && { canSelect: false }),
                });
                const levelColumns = getColumns({
                    accessBindings: this.props.accessBindings,
                    columnsData,
                    elementId: this.props.elementId,
                    enumTypes: this.props.enumTypes,
                    fieldProperties,
                    isDisabled: this.isDisabled(),
                    isFilteringDisabled: !this.props.fieldProperties.canFilter,
                    isParentDisabled: () => !!this.props.isParentDisabled,
                    level: index,
                    locale: this.props.locale,
                    nodeTypes: this.props.nodeTypes,
                    dataTypes: this.props.dataTypes,
                    screenId: this.props.screenId,
                    value: this.getCollectionValue,
                });

                if (curr.childProperty !== undefined) {
                    // Override column's renderer to show caret
                    let offset = 1;

                    if (fieldProperties().hasLineNumbers) {
                        offset += 1;
                    }

                    const innerRenderer = levelColumns[offset].cellRenderer;
                    levelColumns[offset].cellRenderer = 'agGroupCellRenderer';
                    levelColumns[offset].cellRendererParams.innerRenderer = innerRenderer;
                }
                const getDetailRowData = this.getDetailRowData;

                prev.detailCellRendererParams = {
                    refreshStrategy: 'rows',
                    detailGridOptions: {
                        ...this.getCommonGridProps(this.isDisabled()),
                        columnDefs: levelColumns,
                        onRowClicked: this.onRowClick,
                    } satisfies GridOptions,
                    getDetailRowData,
                };
                const levelLength = arr.length;
                if (index === levelLength - 1) {
                    return {
                        refreshStrategy: 'rows',
                        detailGridOptions: levelLength > 2 ? prev : prev.detailCellRendererParams.detailGridOptions,
                        getDetailRowData,
                    };
                }
                return prev.detailCellRendererParams.detailGridOptions;
            },
            {} as { detailCellRendererParams: Partial<IDetailCellRendererParams> },
        );
        this.setState({ detailCellRendererParams });
    };

    private readonly getCollectionValue = (): CollectionValue => this.props.value;

    private readonly getMainMenuItems: GetMainMenuItems = () => {
        /**
         * params.defaultItems are
        [
            "pinSubMenu",
            "separator",
            "autoSizeThis",
            "autoSizeAll",
            "separator",
            "separator",
            "resetColumns"
        ]
         */

        return ['pinSubMenu', 'separator', 'autoSizeThis', 'autoSizeAll'];
    };

    private readonly getAllColumnIds = (gridApi: GridApi): string[] => {
        return (callGridMethod(gridApi, 'getColumns') ?? [])
            .filter(
                column =>
                    column.getColId() !== COLUMN_ID_ROW_SELECTION &&
                    column.getColId() !== COLUMN_ID_ROW_ACTIONS &&
                    column.getColId() !== COLUMN_ID_VALIDATIONS,
            )
            .map(column => column.getColId());
    };

    updateColumnState(): void {
        getSafeGridApiContext(
            columnStates => {
                this.setState({ columnStates });
            },
            this.gridApi,
            'getColumnState',
        );
    }

    private readonly onColumnPanelHiddenStateChange = (colId: string, isHidden: boolean): void => {
        getSafeGridApiContext(
            () => {
                this.updateColumnState();
            },
            this.gridApi,
            'setColumnVisible',
            colId,
            isHidden,
        );
    };

    private readonly onColumnPanelOrderChangeChange = (colIds: string[]): void => {
        getSafeGridApiContext(
            () => {
                this.updateColumnState();
            },
            this.gridApi,
            'moveColumns',
            colIds,
            0,
        );
    };

    private readonly autoSizeAllColumns = ({ gridApi }: { gridApi: GridApi }): void => {
        callGridMethod(gridApi, 'autoSizeColumns', this.getAllColumnIds(gridApi));
        const { parentId, level } = getNestedGridContext(gridApi);
        const parentGridElement = document.querySelector(`[aria-grid="nested-grid-${level}-${parentId}"]`);
        const availableWidth =
            !isNil(parentId) && !isNil(level)
                ? (parentGridElement?.clientWidth ?? 0)
                : (this.containerRef.current?.clientWidth ?? 0);
        const usedWidth = (gridApi.getAllDisplayedColumns() ?? []).reduce((acc, c) => acc + c.getActualWidth(), 0);
        if (availableWidth - usedWidth > 20) {
            setTimeout(() => {
                callGridMethod(gridApi, 'sizeColumnsToFit');
            }, 0);
        }
    };

    private readonly onFirstDataRendered = ({ api: gridApi }: FirstDataRenderedEvent): void => {
        setTimeout(() => this.autoSizeAllColumns({ gridApi }), 100);
    };

    private readonly resetOptionsMenu = (): void => {
        const optionsMenu = resolveByValue({
            propertyValue: this.props.fieldProperties.optionsMenu,
            rowValue: null,
            screenId: this.props.screenId,
            fieldValue: null,
            skipHexFormat: true,
        });
        this.setState({ selectedOptionsMenuItem: optionsMenu?.[0] });
    };

    private readonly getColumnDefinitions = (): void => {
        const firstLevel = this.props.fieldProperties.levels[0];
        const columnsData = firstLevel.columns.map(columnDefinition => ({
            columnDefinition,
            bind: convertDeepBindToPathNotNull(columnDefinition.properties.bind as PropertyValueType),
            elementId: getNestedFieldElementId(columnDefinition),
        }));
        const fieldProperties: Parameters<typeof getColumns>[0]['fieldProperties'] = () => ({
            ...this.props.fieldProperties,
            ...firstLevel,
            ...(this.state.rowSelectionMode === 'single' && { canSelect: false }),
        });
        const columns = getColumns({
            accessBindings: this.props.accessBindings,
            columnsData,
            elementId: this.props.elementId,
            enumTypes: this.props.enumTypes,
            fieldProperties,
            isDisabled: this.isDisabled(),
            isFilteringDisabled: !this.props.fieldProperties.canFilter,
            isParentDisabled: () => !!this.props.isParentDisabled,
            isSortingDisabled: this.isDisabled(),
            level: 0,
            locale: this.props.locale,
            nodeTypes: this.props.nodeTypes,
            dataTypes: this.props.dataTypes,
            screenId: this.props.screenId,
            value: this.getCollectionValue,
        });

        // Override column's renderer to show caret
        let offset = 1;

        if (fieldProperties().hasLineNumbers) {
            offset += 1;
        }

        const innerRenderer = columns[offset].cellRenderer;
        columns[offset].cellRenderer = 'agGroupCellRenderer';
        columns[offset].cellRendererParams.innerRenderer = innerRenderer;

        this.setState({ columns }, () => {
            callGridMethod(this.gridApi, 'stopEditing');
            callGridMethod(this.gridApi, 'deselectAll');
            /**
             * Column definitions have to be reset in order the new properties based on the new collection value
             * object and event listeners to take effect
             */
            callGridMethod(this.gridApi, 'setGridOption', 'columnDefs', this.state.columns);
            /**
             * Datasource has to be reset so the grid detects the new collection value and rerenders itself.
             */
            callGridMethod(this.gridApi, 'setGridOption', 'serverSideDatasource', this.serverSideDataSource());
        });
    };

    private readonly getCacheBlockSize = (): number | undefined => this.props.fieldProperties.pageSize ?? 20;

    private readonly getRowSelectionMode = (): GridOptions['rowSelection'] => {
        const { canActivate, canSelect } = this.props.fieldProperties;
        if (canActivate && canSelect) {
            xtremConsole.warn(
                'A nested grid cannot have "canActivate" and "canSelect" set to "true" at the same time, activation will take precedence, i.e. only one row will be selectable at a time.',
            );
        }
        if (canActivate) {
            return 'single';
        }
        if (canSelect) {
            return {
                mode: 'multiRow',
                groupSelects: 'filteredDescendants',
                selectAll: 'filtered',
            };
        }
        return undefined;
    };

    private readonly loadingCellRendererParams: GridOptions['loadingCellRendererParams'] = {
        elementId: this.props.elementId,
    };

    private readonly onFocusPhantomRow = (): void => this.focusPhantomRowAndStartEditing(this.gridApi);

    private readonly isInfiniteScroll = (): boolean => {
        return Boolean(this.props.isUsingInfiniteScroll || (this.props.fixedHeight && this.props.fixedHeight > 0));
    };

    private readonly getDefaultHeight = (): number => {
        return (
            this.props.fixedHeight || (this.props.fieldProperties.pageSize || 20) * ROW_HEIGHT + 85 // this.props.numberOfVisibleRows ???
        );
    };

    private readonly getUnknownLastRow = (): number | undefined => {
        return this.isInfiniteScroll() ? -1 : undefined;
    };

    render(): React.ReactNode {
        const domLayout = this.isInfiniteScroll() ? 'normal' : 'autoHeight';
        const columnPanelColumnStates = getColumnStatesForColumnPanel(
            this.props.screenId,
            (callGridMethod(this.gridApi, 'getColumnDefs') ?? []) as AgGridColumnConfigWithScreenIdAndColDef[],
            this.state.columnStates,
        );

        const hasAddItemsButton = Boolean(this.props.fieldProperties.levels[0].canAddNewLine);

        return (
            <>
                <DesktopTableHeaderComponent
                    canAddNewLine={hasAddItemsButton}
                    elementId={this.props.elementId}
                    fieldProperties={this.props.fieldProperties}
                    hasAddItemsButton={hasAddItemsButton}
                    hasData={this.state.tableHasData}
                    hasSidebar={Boolean(this.props.fieldProperties.levels[0].sidebar)}
                    isDisabled={this.isDisabled()}
                    isReadOnly={this.isReadOnly()}
                    level={0}
                    onOpenColumnPanel={(): void => this.setState({ isConfigurationDialogOpen: true })}
                    onOptionsMenuItemChange={this.onOptionsMenuItemChange}
                    onFocusPhantomRow={this.onFocusPhantomRow}
                    onTelemetryEvent={this.props.onTelemetryEvent}
                    screenId={this.props.screenId}
                    selectedOptionsMenuItem={this.state.selectedOptionsMenuItem}
                    validationErrors={this.props.validationErrors}
                />

                <div data-testid={`e-nested-grid-field-${this.props.elementId}`}>
                    <div
                        ref={this.containerRef}
                        style={{
                            height: this.isInfiniteScroll() ? `${this.getDefaultHeight()}px` : '100%',
                            maxHeight: '100%',
                            width: '100%',
                            visibility: this.state.isExportingExcel ? 'hidden' : 'visible',
                        }}
                        className="ag-theme-balham"
                    >
                        <AgGridReact
                            {...this.getCommonGridProps(this.isDisabled())}
                            debug={hasAgGridLogging()}
                            domLayout={domLayout}
                            animateRows={true}
                            columnDefs={this.state.columns}
                            detailCellRendererParams={this.state.detailCellRendererParams}
                            getMainMenuItems={this.getMainMenuItems}
                            localeText={localeText()}
                            modules={activatedAgGridModules}
                            onGridReady={this.onGridReady}
                            onRowClicked={this.onRowClick}
                            loadingCellRenderer={TableLoadingCellRenderer}
                            loadingCellRendererParams={this.loadingCellRendererParams}
                            pagination={!this.isInfiniteScroll()}
                        />
                    </div>
                </div>
                <TableConfigurationDialog
                    isDialogOpen={this.state.isConfigurationDialogOpen}
                    onDialogClose={(): void => this.setState({ isConfigurationDialogOpen: false })}
                    columnPanelColumnStates={columnPanelColumnStates}
                    onChangeColumnHiddenState={this.onColumnPanelHiddenStateChange}
                    onOrderChange={this.onColumnPanelOrderChangeChange}
                />
            </>
        );
    }
}

export default DesktopNestedGridComponent;
