.e-nested-grid-mobile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    padding: 16px 16px 16px 0;
    box-sizing: border-box;
    .e-nested-grid-mobile-header-left-container,
    .e-nested-grid-mobile-header-right-container {
        display: flex;
        align-items: center;
        .e-nested-grid-mobile-header-text {
            padding-left: 16px;
            font-size: 16px;
            font-weight: 500;
            font-family: var(--fontFamiliesDefault);
        }
    }
    .e-mobile-nested-grid-header-right-container button {
        margin-right: 16px;
    }
    
}
