import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { NestedGridComponentProps } from './nested-grid-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedNestedGridComponent = React.lazy(() => import('./nested-grid-component'));

export function AsyncConnectedNestedGridComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="200px" />}>
            <ConnectedNestedGridComponent {...props} />
        </React.Suspense>
    );
}

const NestedGridComponent = React.lazy(() =>
    import('./nested-grid-component').then(c => ({ default: c.NestedGridComponent })),
);

export function AsyncNestedGridComponent(props: NestedGridComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={true} bodyHeight="200px" />}>
            <NestedGridComponent {...props} />
        </React.Suspense>
    );
}
