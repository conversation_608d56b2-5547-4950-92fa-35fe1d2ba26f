.e-nested-grid-field {
    box-sizing: border-box;

    .e-field-mobile-search-and-actions {
        flex: 1;
        display: flex;
    }

    .ag-root-wrapper {
        border-radius: var(--borderRadius050);
    }

    @include extra_small {
        padding-top: 0;
        box-sizing: content-box;
        @include full_width_mobile_field;

        .e-field-header {
            display: block;
            padding: 0;
        }

        .e-field-title {
            padding-left: 16px;
            padding-right: 16px;
        }
    }

    .ag-details-row {
        & .ag-root-wrapper-body {
            min-height: 400px !important; // 10 rows
        }
    }

    .ag-theme-balham {
        .ag-overlay {
            .ag-overlay-wrapper {
                margin-top: 10px;
                margin-bottom: 10px;
                padding-top: 0px;
                padding-bottom: 30px;
            }
        }

        .ag-selection-checkbox {
            margin: 0 !important;
            height: unset !important;
        }

        .ag-center-cols-container {
            .e-row-is-edited-no-select {
                border-left: 3px solid var(--colorsSemanticFocus500);
            }
        }

        .ag-cell-wrapper {

            // INFO: Styling for the nested grid's first column when editable.
            .ag-group-value {
                flex: 1;
                height: 27px;
            }
        }
    }

}

.e-nested-grid-field-title {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
}