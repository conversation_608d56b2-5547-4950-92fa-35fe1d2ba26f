import type { ClientNode } from '@sage/xtrem-client';
import type { CollectionValue, GridLodable } from '../../../service/collection-data-service';
import type { UnionType } from '../../../utils/types';
import type { MappedLevel } from './nested-grid-component-types';
import type { MobileNestedGridComponentProps } from './nested-grid-types';

export function getLevelMap<L extends ClientNode[] = [any, any]>(
    levels: MappedLevel<any, L>,
): Record<number, keyof UnionType<L> | undefined> {
    return levels.reduce<Record<number, keyof UnionType<L> | undefined>>((acc, level, index) => {
        acc[index] = level.childProperty as any;
        return acc;
    }, {});
}

export async function fetchRows({
    value,
    fieldProperties,
    pageNumber,
    currentLevel,
    pageSize,
    cursor,
    parentId,
}: {
    value: CollectionValue;
    fieldProperties: MobileNestedGridComponentProps['fieldProperties'];
    pageNumber: number;
    currentLevel: number;
    pageSize: number;
    cursor?: string;
    parentId?: string;
}): Promise<any[]> {
    return value.getPageWithCurrentQueryArguments({
        tableFieldProperties: {
            ...fieldProperties,
            columns: fieldProperties.levels[currentLevel].columns,
        } as GridLodable,
        pageSize,
        pageNumber,
        level: currentLevel,
        parentId,
        cleanMetadata: false,
        cursor,
    });
}
