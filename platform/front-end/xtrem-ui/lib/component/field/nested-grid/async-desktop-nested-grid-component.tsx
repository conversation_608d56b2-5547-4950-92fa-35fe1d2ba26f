import * as React from 'react';
import type { NestedGridInternalComponentProps } from './nested-grid-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const DesktopNestedGridComponent = React.lazy(() => import('./desktop-nested-grid-component'));

export function AsyncDesktopNestedGridComponent(props: NestedGridInternalComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={false} bodyHeight="200px" />}>
            <DesktopNestedGridComponent {...props} />
        </React.Suspense>
    );
}
