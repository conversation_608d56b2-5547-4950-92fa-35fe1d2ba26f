import type { BaseReadonlyComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Clickable,
    ExtensionField,
    HasParent,
    Nested,
    NestedClickable,
    Sizable,
    HasFilter,
    Postfixable,
    Prefixable,
    HasIcon,
} from '../traits';
import type { FieldControlObjectInstance } from '../../types';
import type { ScreenBase } from '../../../service/screen-base';
import type { BlockControlObject, TileControlObject } from '../../control-objects';
import type { ClientNode } from '@sage/xtrem-client';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';

export type CountComponentProps = BaseReadonlyComponentProperties<
    CountProperties,
    number,
    NestedFieldsAdditionalProperties
>;

export interface CountProperties<CT extends ScreenBase = ScreenBase, ReferencedItemType extends ClientNode = any>
    extends ReadonlyFieldProperties<CT>,
        Postfixable<CT, ReferencedItemType>,
        Prefixable<CT, ReferencedItemType>,
        Sizable,
        HasIcon,
        HasFilter<CT, ReferencedItemType> {}

export interface CountDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    ReferencedItemType extends ClientNode = any,
> extends CountProperties<CT, ReferencedItemType>,
        Clickable<CT>,
        Sizable,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | TileControlObject<CT>> {}

export interface NestedCountProperties<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any>
    extends Omit<CountProperties<CT>, 'bind'>,
        NestedClickable<CT, NodeType>,
        Nested<NodeType>,
        Sizable {}
