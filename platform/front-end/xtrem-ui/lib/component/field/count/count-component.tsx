import * as React from 'react';
import { connect } from 'react-redux';
import { formatNumericValue } from '../../../utils/formatters';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps, ReadonlyFieldBaseComponent } from '../field-base-component';
import type { NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { CountDecoratorProperties } from './count-types';
import { isNil } from 'lodash';

export class CountComponent extends ReadonlyFieldBaseComponent<
    CountDecoratorProperties,
    number,
    NestedFieldsAdditionalProperties
> {
    render(): React.ReactNode {
        const value = !isNil(this.props.value)
            ? formatNumericValue({ screenId: this.props.screenId, value: this.props.value })
            : null;
        return (
            <CarbonWrapper
                {...this.props}
                className="e-count-field"
                componentName="count"
                componentRef={this.componentRef}
                fieldProperties={{
                    ...this.props.fieldProperties,
                    isReadOnly: true,
                }}
                handlersArguments={this.props.handlersArguments}
                value={value}
            >
                <noscript />
            </CarbonWrapper>
        );
    }
}

export const ConnectedCountComponent = connect(mapStateToProps(), mapDispatchToProps())(CountComponent);

export default ConnectedCountComponent;
