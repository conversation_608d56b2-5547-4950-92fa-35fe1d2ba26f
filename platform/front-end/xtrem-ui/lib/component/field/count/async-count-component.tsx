import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { CountComponentProps } from './count-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedCountComponent = React.lazy(() => import('./count-component'));

export function AsyncConnectedCountComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedCountComponent {...props} />
        </React.Suspense>
    );
}

const CountComponent = React.lazy(() => import('./count-component').then(c => ({ default: c.CountComponent })));

export function AsyncCountComponent(props: CountComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <CountComponent {...props} />
        </React.Suspense>
    );
}
