/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { CountControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { CountDecoratorProperties } from './count-types';

class CountDecorator extends AbstractFieldDecorator<FieldKey.Count> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = CountControlObject;
}

/**
 * Initializes the decorated member as a [Count]{@link CountControlObject} field with the provided properties
 *
 * @param properties The properties that the [Count]{@link CountControlObject} field will be initialized with
 */
export function countField<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: CountDecoratorProperties<Extend<T>, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Count, ReferencedItemType>(
        properties,
        CountDecorator,
        FieldKey.Count,
        true,
    );
}

export function countFieldOverride<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: ClickableOverrideDecoratorProperties<
        CountDecoratorProperties<Extend<T>, ReferencedItemType>,
        Extend<T>
    >,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Count, ReferencedItemType>(properties);
}
