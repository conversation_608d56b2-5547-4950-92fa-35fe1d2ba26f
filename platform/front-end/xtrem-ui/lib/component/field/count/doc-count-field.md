PATH: XTREEM/UI+Field+Widgets/Count+Field

## Introduction

A count fields represents the total number of items in collection property of the page's node.
It is also available as a nested field in the navigation panel, tables, charts and calendars.

## Example:

```ts
@ui.decorators.countField<CountFieldPage>({
    parent() {
        return this.anyBlock;
    },
    title: 'A count field',
    bind: 'aCollectionProperty'
    onClick() {
        console.log('Doing something when the field is clicked');
    },
    prefix: '$',
    postfix: 'Kg',
    helperText: 'This text goes underneath the field',
    filter: {
        propertyOfTheCollection: { _gt: 5 },
    }
})
aSimpleCountField: ui.fields.Count;
```

#### Use as extended field:

```ts
@ui.decorators.countField<PageExtension>({
    title: 'Extended Count Field',
    insertBefore() {
        return this.field;
    },
    parent() {
        return this.block;
    },
})
extended: ui.fields.Count
```

#### Use as nested field:

```ts
@ui.decorators.tableField<Page>({
    ...,
    columns: [
        ...,
        ui.nestedFields.count({
            bind: 'itemCount',
            title: 'Amount of items',
        }),
    ],
})
table: ui.fields.Table<Node>;
```

### Display decorator properties:

-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **helperText**: The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **prefix**: A string that is displayed inside the field before the value, aligned to the left. It can be defined as a string, or conditionally by a callback that returns a string.
-   **postfix**: A string that is displayed inside the field after the value, aligned to the right. It can be defined as a string, or conditionally by a callback that returns a string
-   **placeholder**: Placeholder text which is displayed inside the field body when the field is empty. It is automatically picked up by the i18n engine and externalized.
-   **size**: Vertical size of the field, it can be `small`, `medium` or `large`. It is set to medium by default.
-   **isFullWidth**: Whether a field should take the full width of the screen.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **icon**: Icon to be displayed on the right side of the input. The list of icons are defined by [DLS](https://brand.sage.com/d/NdbrveWvNheA/foundations#/icons/icons).
-   **iconColor**: Color of the input icon, only supported if the field is rendered within a tile container.

### Binding decorator properties:

-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **filter**: Applies this filter to the bound collection, reducing the total number displayed. It can also be used as a callback function.

### Event handler decorator properties:

-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

#### Runtime Functions

-   **refresh()**: Refetches the field's values from the server and updates the user interface.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Count).
