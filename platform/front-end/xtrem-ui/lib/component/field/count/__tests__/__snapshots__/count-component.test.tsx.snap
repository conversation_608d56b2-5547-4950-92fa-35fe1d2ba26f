// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Count component connected Snapshots should render empty when no value 1`] = `
<div>
  <div
    class="e-field e-count-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-empty-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-empty-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        />
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Count component connected Snapshots should render helperText 1`] = `
<div>
  <div
    class="e-field e-count-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          4
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
          This is a helper text
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Count component connected Snapshots should render hidden 1`] = `
<div>
  <div
    class="e-field e-count-field e-hidden e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          4
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Count component connected Snapshots should render in read-only mode with a postfix and a prefix 1`] = `
<div>
  <div
    class="e-field e-count-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          tel: 4 x
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Count component connected Snapshots should render with a postfix 1`] = `
<div>
  <div
    class="e-field e-count-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          4 x
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Count component connected Snapshots should render with a prefix 1`] = `
<div>
  <div
    class="e-field e-count-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          tel: 4
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Count component connected Snapshots should render with default properties 1`] = `
<div>
  <div
    class="e-field e-count-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          4
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Count component connected Snapshots should render with various field sizes 1`] = `
<div>
  <div
    class="e-field e-count-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          4
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Count component connected Snapshots should render with various field sizes 2`] = `
<div>
  <div
    class="e-field e-count-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          4
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Count component connected Snapshots should render with various field sizes 3`] = `
<div>
  <div
    class="e-field e-count-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          4
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Count component connected Snapshots should with full-width 1`] = `
<div>
  <div
    class="e-field e-count-field e-full-width e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          4
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Count component unconnected Snapshots should render just fine with a value 1`] = `
<div>
  <div
    class="e-field e-count-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        >
          3
        </span>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Count component unconnected Snapshots should render just fine without a value 1`] = `
<div>
  <div
    class="e-field e-count-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-count-field e-field-label-testFieldTitle e-field-bind-test-count-field"
  >
    <div>
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
        for="TestPage-test-count-field"
      >
        Test Field Title
      </label>
      <div>
        <span
          class="e-field-read-only"
          data-testid="e-field-value"
        />
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;
