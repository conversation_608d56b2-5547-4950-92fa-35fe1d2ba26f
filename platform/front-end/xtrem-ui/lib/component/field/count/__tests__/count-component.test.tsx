import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { FieldKey } from '../../../types';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import type * as xtremRedux from '../../../../redux';
import type { CountProperties } from '../../../control-objects';
import { ConnectedCountComponent, CountComponent } from '../count-component';
import type { ScreenBase } from '../../../../service/screen-base';
import { render } from '@testing-library/react';

describe('Count component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: CountProperties<ScreenBase>;

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test Field Title',
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Count, state, screenId, 'test-count-field', mockFieldProperties, 4);
            addFieldToState(FieldKey.Count, state, screenId, 'test-empty-count-field', mockFieldProperties, null);
            mockStore = getMockStore(state);
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCountComponent screenId={screenId} elementId="test-count-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCountComponent screenId={screenId} elementId="test-count-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with a prefix', () => {
                mockFieldProperties.prefix = 'tel:';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCountComponent screenId={screenId} elementId="test-count-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with a postfix', () => {
                mockFieldProperties.postfix = 'x';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCountComponent screenId={screenId} elementId="test-count-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render in read-only mode with a postfix and a prefix', () => {
                mockFieldProperties.postfix = 'x';
                mockFieldProperties.prefix = 'tel:';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCountComponent screenId={screenId} elementId="test-count-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should with full-width', () => {
                mockFieldProperties.isFullWidth = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCountComponent screenId={screenId} elementId="test-count-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCountComponent screenId={screenId} elementId="test-count-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render empty when no value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCountComponent screenId={screenId} elementId="test-empty-count-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with various field sizes', () => {
                mockFieldProperties.size = 'small';
                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedCountComponent screenId={screenId} elementId="test-count-field" />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'medium';
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedCountComponent screenId={screenId} elementId="test-count-field" />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'large';
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedCountComponent screenId={screenId} elementId="test-count-field" />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();
            });
        });
    });

    describe('unconnected', () => {
        describe('Snapshots', () => {
            it('should render just fine with a value', () => {
                const { container } = render(
                    <CountComponent
                        elementId="test-count-field"
                        fieldProperties={mockFieldProperties}
                        value={3}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(container).toMatchSnapshot();
            });
            it('should render just fine without a value', () => {
                const { container } = render(
                    <CountComponent
                        elementId="test-count-field"
                        fieldProperties={mockFieldProperties}
                        value={undefined}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(container).toMatchSnapshot();
            });
        });
    });
});
