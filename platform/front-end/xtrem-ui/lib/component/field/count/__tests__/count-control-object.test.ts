import type { <PERSON><PERSON><PERSON> } from '../../../types';
import { CountControlObject } from '../../../control-objects';
import type { CountProperties } from '../count-types';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';

describe('Count control object', () => {
    let countControlObject: CountControlObject;
    let countProperties: CountProperties;
    let countValue: number;

    beforeEach(() => {
        countProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
        };
        countValue = 4;
        countControlObject = buildControlObject<FieldKey.Count>(CountControlObject, {
            fieldValue: countValue as any,
            fieldProperties: countProperties,
        });
    });

    it('getting field value', () => {
        expect(countControlObject.value).toEqual(countValue);
    });

    it('should set the title', () => {
        const testFixture = 'Test Numeric Field Title';
        expect(countProperties.title).not.toEqual(testFixture);
        countControlObject.title = testFixture;
        expect(countProperties.title).toEqual(testFixture);
    });

    it('should set the value prefix', () => {
        const testFixture = '$$';
        expect(countProperties.prefix).not.toEqual(testFixture);
        countControlObject.prefix = testFixture;
        expect(countProperties.prefix).toEqual(testFixture);
    });

    it('should set the value postfix', () => {
        const testFixture = '££';
        expect(countProperties.postfix).not.toEqual(testFixture);
        countControlObject.postfix = testFixture;
        expect(countProperties.postfix).toEqual(testFixture);
    });
});
