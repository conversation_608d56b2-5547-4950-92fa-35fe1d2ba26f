/**
 * @packageDocumentation
 * @module root
 * */

import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import { showToast } from '../../../service/toast-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { CountProperties } from './count-types';
import { isNil } from 'lodash';

/**
 * [Field]{@link AbstractField} that holds a reference to an existing GraphQL object. The type of GraphQL object
 * must be specified through the 'node' property, while the 'valueField' and 'helperTextField'
 * properties define which properties of the GraphQL object will be displayed in the field
 * and will be matched against the user provided text
 */
export class CountControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.Count,
    FieldComponentProps<FieldKey.Count>
> {
    /** Graphql filter that will restrict the results of the reference field */
    get filter(): GraphQLFilter | undefined {
        return resolveByValue({
            fieldValue: undefined,
            propertyValue: this.getUiComponentProperty('filter'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /** Graphql filter that will restrict the results of the reference field */
    set filter(filter: GraphQLFilter | undefined) {
        this.setUiComponentProperties('filter', filter);
        this.refresh().catch(() => {
            /* Intentional fire and forget */
        });
    }

    @ControlObjectProperty<CountProperties<CT>, CountControlObject<CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<CountProperties<CT>, CountControlObject<CT>>()
    /** Text to be displayed inline after the field value */
    prefix?: string;

    @ControlObjectProperty<CountProperties<CT>, CountControlObject<CT>>()
    /** Text to be displayed inline before the field value */
    postfix?: string;

    @ControlObjectProperty<CountProperties<CT>, CountControlObject<CT>>()
    /** Icon of the input field. It will be placed on the right side. */
    icon?: IconType;

    @ControlObjectProperty<CountProperties<CT>, CountControlObject<CT>>()
    /** Color of the icon, only supported in tile containers */
    iconColor?: string;

    /** Field's value */
    get value(): number | null {
        return this._getValue();
    }

    set value(newValue: number | null) {
        if (isNil(newValue)) {
            this._setValue(null);
        } else {
            this._setValue(newValue);
        }
    }

    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }
}
