import React from 'react';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { NestedCountProperties } from '../../nested-fields-properties';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';

export const CountCellRenderer: React.FC<
    CellParams<
        NestedCountProperties,
        {
            query?: {
                totalCount: number;
            };
        }
    >
> = React.memo(props => {
    const { screenId, fieldProperties } = props;

    const prefix = resolveByValue({
        fieldValue: `${props.value?.query?.totalCount}`,
        propertyValue: fieldProperties.prefix,
        rowValue: splitValueToMergedValue(props.data),
        skipHexFormat: true,
        screenId,
    });
    const postfix = resolveByValue({
        fieldValue: `${props.value?.query?.totalCount}`,
        propertyValue: fieldProperties.postfix,
        rowValue: splitValueToMergedValue(props.data),
        skipHexFormat: true,
        screenId,
    });

    return (
        <fieldProperties.wrapper {...props}>
            <div className="e-count-field">
                <span className="e-count-field-prefix">{prefix}</span>
                <span className="e-count-field-value">{props.value?.query?.totalCount}</span>
                <span className="e-count-field-postfix">{postfix}</span>
            </div>
        </fieldProperties.wrapper>
    );
});
