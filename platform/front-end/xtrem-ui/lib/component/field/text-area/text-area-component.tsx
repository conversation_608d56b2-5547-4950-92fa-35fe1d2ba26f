import Textarea from 'carbon-react/esm/components/textarea';
import * as React from 'react';
import { connect } from 'react-redux';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { getCommonCarbonComponentProperties } from '../carbon-helpers';
import { CarbonWrapper } from '../carbon-wrapper';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type { TextAreaDecoratorProperties } from './text-area-types';

type TextAreaComponentProps = BaseEditableComponentProperties<TextAreaDecoratorProperties, string>;

export interface TextAreaComponentState {
    value: string;
}

export class TextAreaComponent extends EditableFieldBaseComponent<
    TextAreaDecoratorProperties,
    string,
    {},
    TextAreaComponentState
> {
    constructor(props: TextAreaComponentProps) {
        super(props);
        this.state = { value: this.props.value || '' };
    }

    onChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
        this.setState({ value: event.target.value });
    };

    onBlur = (): void => {
        const carbonProps = getCommonCarbonComponentProperties(this.props);
        carbonProps.onBlur();
        const newValue = this.state.value || null;
        const propsValue = this.props.value || null;
        if (newValue !== propsValue) {
            handleChange(
                this.props.elementId,
                newValue,
                this.props.setFieldValue,
                this.props.validate,
                this.triggerChangeListener,
            );
        }
    };

    onKeyDown = (event: KeyboardEvent): void => {
        if (event.key === 'Escape') {
            this.setState({ value: this.props.value || '' });
        }
    };

    UNSAFE_componentWillReceiveProps(nextProps: TextAreaComponentProps): void {
        const value = nextProps.value;
        if (this.state.value !== value) {
            this.setState({ value: value || '' });
        }
    }

    getFocusableElement(element: HTMLElement): HTMLTextAreaElement | null {
        return element.querySelector('textarea');
    }

    render(): React.ReactNode {
        return (
            <CarbonWrapper
                {...this.props}
                className="e-text-area-field"
                componentName="text-area"
                componentRef={this.componentRef}
                noReadOnlySupport={true}
                value={this.state.value}
            >
                <Textarea
                    {...getCommonCarbonComponentProperties(this.props)}
                    onChange={this.onChange}
                    onBlur={this.onBlur}
                    onClick={this.getClickHandler()}
                    onKeyDown={this.onKeyDown}
                    value={this.state.value}
                    rows={this.props.fieldProperties.rows}
                />
            </CarbonWrapper>
        );
    }
}

export const ConnectedTextAreaComponent = connect(mapStateToProps(), mapDispatchToProps())(TextAreaComponent);

export default ConnectedTextAreaComponent;
