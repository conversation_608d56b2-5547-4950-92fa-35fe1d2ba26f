import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValueOrCallbackWitRecordValue } from '../../../utils/types';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseErrorableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasMaxMinLength,
    HasParent,
    Nested,
    NestedChangeable,
    NestedClickable,
    NestedValidatable,
    Validatable,
} from '../traits';

export interface TextAreaProperties<CT extends ScreenBase = ScreenBase, ContextNodeType = void>
    extends EditableFieldProperties<CT, ContextNodeType>,
        HasMaxMinLength<CT> {
    /** Placeholder to be displayed in the field body */
    placeholder?: string;
    /** The number of rows the text area will span */
    rows?: number;
}

export interface TextAreaDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<TextAreaProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        Changeable<CT>,
        HasParent<CT, BlockControlObject<CT>>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Validatable<CT, string> {}

export interface NestedTextAreaProperties<CT extends ScreenBase = ScreenBase, ContextNodeType extends ClientNode = any>
    extends NestedPropertiesWrapper<TextAreaProperties<CT, ContextNodeType>>,
        NestedChangeable<CT>,
        NestedClickable<CT, ContextNodeType>,
        Nested<ContextNodeType>,
        NestedValidatable<CT, string, ContextNodeType> {
    /** The maximum length of the text field value */
    maxLength?: ValueOrCallbackWitRecordValue<CT, number, ContextNodeType>;
    /** The minimum length of the text field value */
    minLength?: ValueOrCallbackWitRecordValue<CT, number, ContextNodeType>;
}

export type TextAreaComponentProps = BaseErrorableComponentProperties<
    TextAreaProperties,
    string,
    NestedFieldsAdditionalProperties
>;
