PATH: XTREEM/UI+Field+Widgets/TextArea+Field

## Introduction

The textarea field represents a block of unformatted text in the user interface. This field is also available as an extended field.

## Example

```ts
@ui.decorators.textAreaField<Page>({
    helperText: 'Helper text displayed below the field.',
    isDisabled: false,
    isFullWidth: false,
    isHelperTextHidden: false,
    isHidden: false,
    isMandatory: true,
    isTransient: false,
    placeholder: 'Select a category...',
    size: 'medium',
    title: 'Select Field',
    width: 'medium',
    onChange() {
        console.log(`Do something when the field's value has changed.`);
    },
    onClick() {
        console.log(`Do something when the field is clicked.`);
    },
    parent() {
        return this.block;
    },
    validation(value) {
        return value === 'Hello World'
            ? '"Hello World" is not a valid input'
            : undefined;
    },
})
field: ui.fields.TextArea;
```

```ts
@ui.decorators.textAreaField<PageExtension>({
    ...,
    title: 'Extended TextArea Field',
    insertBefore() {
        return this.field;
    },
    parent() {
        return this.block;
    },
})
extension: ui.fields.TextArea;
```

## Decorator Properties

#### Display Properties

-   **helperText**: The helper text displayed below the field. This property is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.
-   **isHelperTextHidden**: Determines whether the field's helper text is displayed or not.
-   **isHidden**: Determines whether the field is displayed or not.
-   **isTitleHidden**: Determines whether the field's title is displayed or not.
-   **placeholder**: Placeholder text which is displayed inside the field body when the field is empty. It is automatically picked up by the i18n engine and externalized.
-   **rows**: The number of rows, the field should span.
-   **size**: Size the field should be rendered in. The options are "small", "medium" and "large".
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.

#### Binding Properties

-   **bind**: Determines the associated GraphQL node's property the field's value will be bound to.
-   **isTransient**: Determines whether the field will be excluded from the automatic data binding process or not.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **map()**: Custom callback to transform the field's value. The new value and grid's row data (if applicable) is provided as arguments. If implemented, the callbacks return value will be used as the field's value.

#### Validation Properties

-   **isMandatory**: Determines whether leaving the field empty will raise a validation error. It can also be defined as callback function that returns a boolean.
-   **maxLength**: Sets the maximum number of allowed characters. It can also be defined as callback function that returns a number.
-   **minLength**: Sets the minimum number of required characters. It can also be defined as callback function that returns a number.
-   **validation**: Regular expression or callback function to specify custom validation rules. The new value is provided in as argument. If the function returns a non-empty string (or promise resolving to a non-empty string), it will be used as a validation error message. If the function returns a falsy value, the field is considered to be valid.

#### Event Handler Properties

-   **onChange**: Handler triggered when the field's value has changed.
-   **onClick**: Handler triggered when the field is clicked.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

### Other Decorator Properties

-   **fetchesDefaults**: When set to true and when the text area value changes, a request to the server for default values for the whole page will be requested. False by default.

#### Runtime Functions

-   **focus()**: Sets the browser's focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **refresh**: Refetches the field's value from the server and updates the user interface.
-   **validate()**: Executes the validation rules and determines the field's validity.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).


## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/TextArea).
