/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { TextAreaProperties } from './text-area-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a multi-line text value
 */
export class TextAreaControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.TextArea,
    FieldComponentProps<FieldKey.TextArea>
> {
    @ControlObjectProperty<TextAreaProperties<CT>, TextAreaControlObject<CT>>()
    /** The number of rows the text area will span */
    rows?: number;

    @ControlObjectProperty<TextAreaProperties<CT>, TextAreaControlObject<CT>>()
    /** The minimum length of the text field value */
    minLength?: number;

    @ControlObjectProperty<TextAreaProperties<CT>, TextAreaControlObject<CT>>()
    /**  The maximum length of the text field value  */
    maxLength?: number;

    @ControlObjectProperty<TextAreaProperties<CT>, TextAreaControlObject<CT>>()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    @ControlObjectProperty<TextAreaProperties<CT>, TextAreaControlObject<CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }
}
