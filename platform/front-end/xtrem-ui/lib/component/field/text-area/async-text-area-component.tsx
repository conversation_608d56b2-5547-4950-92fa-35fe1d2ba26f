import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { TextAreaComponentProps } from './text-area-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedTextAreaComponent = React.lazy(() => import('./text-area-component'));

export function AsyncConnectedTextAreaComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="80px" />}>
            <ConnectedTextAreaComponent {...props} />
        </React.Suspense>
    );
}

const TextAreaComponent = React.lazy(() =>
    import('./text-area-component').then(c => ({ default: c.TextAreaComponent })),
);

export function AsyncTextAreaComponent(props: TextAreaComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} bodyHeight="80px" />}>
            <TextAreaComponent {...props} />
        </React.Suspense>
    );
}
