import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    applyActionMocks,
} from '../../../../__tests__/test-helpers';

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import type { TextAreaProperties } from '../../../control-objects';
import { FieldKey } from '../../../types';
import { ConnectedTextAreaComponent, TextAreaComponent } from '../text-area-component';
import { fireEvent, render, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

describe('TextAreaComponent', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: TextAreaProperties;

    beforeEach(() => {
        jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue(
            () => Promise.resolve({ type: 'SetFieldValue' }) as any,
        );

        mockFieldProperties = {
            title: 'Test Field Title',
        };
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(
                FieldKey.TextArea,
                state,
                screenId,
                'test-textarea-field',
                mockFieldProperties,
                'Test\nValue',
            );
            addFieldToState(FieldKey.TextArea, state, screenId, 'test-empty-textarea-field', mockFieldProperties, null);
            mockStore = getMockStore(state);
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-textarea-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-textarea-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-textarea-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render in read-only mode', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-textarea-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with set number of lines', () => {
                mockFieldProperties.rows = 5;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-textarea-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-textarea-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render empty when no value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-empty-textarea-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with an info message', async () => {
                mockFieldProperties.infoMessage = 'Info message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-empty-textarea-field" />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an info message from a callback', async () => {
                mockFieldProperties.infoMessage = () => 'Info message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-empty-textarea-field" />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an warning message', async () => {
                mockFieldProperties.warningMessage = 'Warning message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-empty-textarea-field" />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with an warning message from a callback', async () => {
                mockFieldProperties.warningMessage = () => 'Warning message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-empty-textarea-field" />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });
        });

        describe('Interactions', () => {
            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-textarea-field" />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="help"]')).not.toBeNull();
            });

            it('Should not render helperText', () => {
                mockFieldProperties.helperText = '';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-textarea-field" />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="help"]')).toBeNull();
            });

            it('should update parent on value change when it looses focus', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedTextAreaComponent screenId={screenId} elementId="test-textarea-field" />
                    </Provider>,
                );
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                const textAreaElement = container.querySelector('textarea')!;
                fireEvent.change(textAreaElement, { target: { value: 'A\nnew\ninput\nvalue' } });
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                fireEvent.blur(textAreaElement);

                expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledTimes(1);
                expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(
                    screenId,
                    'test-textarea-field',
                    'A\nnew\ninput\nvalue',
                    true,
                );
            });
        });
    });

    describe('unconnected', () => {
        describe('Snapshots', () => {
            it('should render just fine', () => {
                const { container } = render(
                    <TextAreaComponent
                        elementId="test-textarea-field"
                        fieldProperties={mockFieldProperties}
                        locale="en-US"
                        value="testValue"
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                    />,
                );
                expect(container).toMatchSnapshot();
            });
        });
    });
});
