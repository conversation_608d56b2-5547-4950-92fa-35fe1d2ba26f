// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TextAreaComponent connected Snapshots should render disabled 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c3 {
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
  color: var(--colorsUtilityYin030);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c9 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  color: var(--colorsUtilityYin030);
  cursor: not-allowed;
}

.c9:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c9::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c7 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background: var(--colorsUtilityDisabled400);
  border-color: var(--colorsUtilityDisabled600);
  cursor: not-allowed;
}

.c7 .c8 {
  padding: 0 var(--spacing150);
}

.c7 input::-ms-clear {
  display: none;
}

.c7 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c8 {
  box-sizing: border-box;
  resize: none;
  min-height: 64px;
  padding: var(--spacing150) var(--spacing200);
}

<div>
  <div
    class="e-field e-text-area-field e-disabled"
    data-label="Test Field Title"
    data-testid="e-text-area-field e-field-label-testFieldTitle e-field-bind-test-textarea-field"
  >
    <div
      class="c0"
      data-component="textarea"
    >
      <div
        class="c1 c2"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-textarea-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              disabled=""
              for="TestPage-test-textarea-field"
              id="TestPage-test-textarea-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7"
              disabled=""
              role="presentation"
            >
              <textarea
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-element="input"
                data-label="Test Field Title"
                disabled=""
                id="TestPage-test-textarea-field"
                name="test-textarea-field"
                placeholder=""
                type="text"
              >
                Test
Value
              </textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`TextAreaComponent connected Snapshots should render empty when no value 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c3 {
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c9 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
}

.c9:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c9::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c7 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c7 .c8 {
  padding: 0 var(--spacing150);
}

.c7 input::-ms-clear {
  display: none;
}

.c7 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c8 {
  box-sizing: border-box;
  resize: none;
  min-height: 64px;
  padding: var(--spacing150) var(--spacing200);
}

<div>
  <div
    class="e-field e-text-area-field"
    data-label="Test Field Title"
    data-testid="e-text-area-field e-field-label-testFieldTitle e-field-bind-test-empty-textarea-field"
  >
    <div
      class="c0"
      data-component="textarea"
    >
      <div
        class="c1 c2"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-empty-textarea-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-empty-textarea-field"
              id="TestPage-test-empty-textarea-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7"
              role="presentation"
            >
              <textarea
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-empty-textarea-field"
                name="test-empty-textarea-field"
                type="text"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`TextAreaComponent connected Snapshots should render helperText 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c3 {
  display: block;
}

.c10 {
  display: block;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin-top: 8px;
  white-space: pre-wrap;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c9 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
}

.c9:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c9::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c7 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c7 .c8 {
  padding: 0 var(--spacing150);
}

.c7 input::-ms-clear {
  display: none;
}

.c7 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c8 {
  box-sizing: border-box;
  resize: none;
  min-height: 64px;
  padding: var(--spacing150) var(--spacing200);
}

<div>
  <div
    class="e-field e-text-area-field"
    data-label="Test Field Title"
    data-testid="e-text-area-field e-field-label-testFieldTitle e-field-bind-test-textarea-field"
  >
    <div
      class="c0"
      data-component="textarea"
    >
      <div
        class="c1 c2"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-textarea-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-textarea-field"
              id="TestPage-test-textarea-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7"
              role="presentation"
            >
              <textarea
                aria-describedby="TestPage-test-textarea-field-field-help"
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-textarea-field"
                name="test-textarea-field"
                type="text"
              >
                Test
Value
              </textarea>
            </div>
          </div>
        </div>
        <span
          class="c10"
          data-element="help"
          id="TestPage-test-textarea-field-field-help"
        >
          This is a helper text
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`TextAreaComponent connected Snapshots should render hidden 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c3 {
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c9 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
}

.c9:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c9::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c7 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c7 .c8 {
  padding: 0 var(--spacing150);
}

.c7 input::-ms-clear {
  display: none;
}

.c7 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c8 {
  box-sizing: border-box;
  resize: none;
  min-height: 64px;
  padding: var(--spacing150) var(--spacing200);
}

<div>
  <div
    class="e-field e-text-area-field e-hidden"
    data-label="Test Field Title"
    data-testid="e-text-area-field e-field-label-testFieldTitle e-field-bind-test-textarea-field"
  >
    <div
      class="c0"
      data-component="textarea"
    >
      <div
        class="c1 c2"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-textarea-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-textarea-field"
              id="TestPage-test-textarea-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7"
              role="presentation"
            >
              <textarea
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-textarea-field"
                name="test-textarea-field"
                type="text"
              >
                Test
Value
              </textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`TextAreaComponent connected Snapshots should render in read-only mode 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c3 {
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c9 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
}

.c9:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c9::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c7 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background-color: var(--colorsUtilityReadOnly400);
  border-color: var(--colorsUtilityReadOnly600);
}

.c7 .c8 {
  padding: 0 var(--spacing150);
}

.c7 input::-ms-clear {
  display: none;
}

.c7 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c8 {
  box-sizing: border-box;
  resize: none;
  min-height: 64px;
  padding: var(--spacing150) var(--spacing200);
}

<div>
  <div
    class="e-field e-text-area-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-text-area-field e-field-label-testFieldTitle e-field-bind-test-textarea-field"
  >
    <div
      class="c0"
      data-component="textarea"
    >
      <div
        class="c1 c2"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-textarea-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-textarea-field"
              id="TestPage-test-textarea-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7"
              readonly=""
              role="presentation"
            >
              <textarea
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-textarea-field"
                name="test-textarea-field"
                readonly=""
                type="text"
              >
                Test
Value
              </textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`TextAreaComponent connected Snapshots should render with default properties 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c3 {
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c9 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
}

.c9:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c9::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c7 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c7 .c8 {
  padding: 0 var(--spacing150);
}

.c7 input::-ms-clear {
  display: none;
}

.c7 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c8 {
  box-sizing: border-box;
  resize: none;
  min-height: 64px;
  padding: var(--spacing150) var(--spacing200);
}

<div>
  <div
    class="e-field e-text-area-field"
    data-label="Test Field Title"
    data-testid="e-text-area-field e-field-label-testFieldTitle e-field-bind-test-textarea-field"
  >
    <div
      class="c0"
      data-component="textarea"
    >
      <div
        class="c1 c2"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-textarea-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-textarea-field"
              id="TestPage-test-textarea-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7"
              role="presentation"
            >
              <textarea
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-textarea-field"
                name="test-textarea-field"
                type="text"
              >
                Test
Value
              </textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`TextAreaComponent connected Snapshots should render with set number of lines 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c3 {
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c9 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
}

.c9:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c9::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c7 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c7 .c8 {
  padding: 0 var(--spacing150);
}

.c7 input::-ms-clear {
  display: none;
}

.c7 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c8 {
  box-sizing: border-box;
  resize: none;
  min-height: 64px;
  padding: var(--spacing150) var(--spacing200);
}

<div>
  <div
    class="e-field e-text-area-field"
    data-label="Test Field Title"
    data-testid="e-text-area-field e-field-label-testFieldTitle e-field-bind-test-textarea-field"
  >
    <div
      class="c0"
      data-component="textarea"
    >
      <div
        class="c1 c2"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-textarea-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-textarea-field"
              id="TestPage-test-textarea-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7"
              role="presentation"
            >
              <textarea
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-textarea-field"
                name="test-textarea-field"
                rows="5"
                type="text"
              >
                Test
Value
              </textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`TextAreaComponent unconnected Snapshots should render just fine 1`] = `
.c2 {
  margin-bottom: var(--fieldSpacing);
}

.c1 + .c1 {
  margin-top: 16px;
}

.c2.c2.c2 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c3 {
  display: block;
}

.c5 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c9 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
}

.c9:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c9::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::placeholder {
  color: var(--colorsUtilityYin055);
}

.c6 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c7 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c7 .c8 {
  padding: 0 var(--spacing150);
}

.c7 input::-ms-clear {
  display: none;
}

.c7 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c8 {
  box-sizing: border-box;
  resize: none;
  min-height: 64px;
  padding: var(--spacing150) var(--spacing200);
}

<div>
  <div
    class="e-field e-text-area-field"
    data-label="Test Field Title"
    data-testid="e-text-area-field e-field-label-testFieldTitle e-field-bind-test-textarea-field"
  >
    <div
      class="c0"
      data-component="textarea"
    >
      <div
        class="c1 c2"
      >
        <div
          class="c3"
          data-role="field-line"
        >
          <div
            class="c4"
            data-role="label-container"
            id="label-container-TestPage-test-textarea-field-label"
            width="30"
          >
            <label
              class="c5"
              data-element="label"
              for="TestPage-test-textarea-field"
              id="TestPage-test-textarea-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c6"
            data-role="input-presentation-container"
          >
            <div
              class="c7"
              role="presentation"
            >
              <textarea
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-element="input"
                data-label="Test Field Title"
                id="TestPage-test-textarea-field"
                name="test-textarea-field"
                type="text"
              >
                testValue
              </textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
