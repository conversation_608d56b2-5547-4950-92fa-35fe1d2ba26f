import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import { textAreaField } from '../text-area-decorator';

describe('Text area Decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'textAreaField';

        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(textAreaField, pageMetadata, fieldId);
        });
    });
});
