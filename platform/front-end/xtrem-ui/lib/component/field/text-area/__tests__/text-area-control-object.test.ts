import { <PERSON><PERSON><PERSON> } from '../../../types';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { TextAreaControlObject } from '../../../control-objects';

describe('TextArea Field', () => {
    const screenId = 'TestPage';
    let textAreaControlObject: TextAreaControlObject;

    beforeEach(() => {
        textAreaControlObject = createFieldControlObject<FieldKey.TextArea>(
            FieldKey.TextArea,
            screenId,
            TextAreaControlObject,
            'test',
            'TEST_VALUE',
            {
                title: 'TEST_FIELD_TITLE',
                isHidden: true,
                isDisabled: true,
            },
        );
    });

    it('getting field value', () => {
        expect(textAreaControlObject.value).toEqual('TEST_VALUE');
    });

    describe('setting and getting updated values', () => {
        it('should set the title', () => {
            const testFixture = 'Test Title';
            expect(textAreaControlObject.title).not.toEqual(testFixture);
            textAreaControlObject.title = testFixture;
            expect(textAreaControlObject.title).toEqual(testFixture);
        });

        it('should set the number of rows', () => {
            const testFixture = 5;
            expect(textAreaControlObject.rows).not.toEqual(testFixture);
            textAreaControlObject.rows = testFixture;
            expect(textAreaControlObject.rows).toEqual(testFixture);
        });

        it('should set the max length', () => {
            const testFixture = 256;
            expect(textAreaControlObject.maxLength).not.toEqual(testFixture);
            textAreaControlObject.maxLength = testFixture;
            expect(textAreaControlObject.maxLength).toEqual(testFixture);
        });

        it('should set the min length', () => {
            const testFixture = 2;
            expect(textAreaControlObject.minLength).not.toEqual(testFixture);
            textAreaControlObject.minLength = testFixture;
            expect(textAreaControlObject.minLength).toEqual(testFixture);
        });
    });
});
