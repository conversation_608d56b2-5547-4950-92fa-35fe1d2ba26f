import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import { addDisabledToProperties, addMaxLengthToProperties } from '../../../utils/data-type-utils';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { TextAreaControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { TextAreaDecoratorProperties } from './text-area-types';

class TextAreaDecorator extends AbstractFieldDecorator<FieldKey.TextArea> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = TextAreaControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<TextAreaDecoratorProperties> {
        const properties: Partial<TextAreaDecoratorProperties> = {};
        addMaxLengthToProperties({ dataType, propertyDetails, properties });
        addDisabledToProperties({
            propertyDetails,
            dataType,
            properties,
        });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [TextArea]{@link TextAreaControlObject} field with the provided properties
 *
 * @param properties The properties that the [TextArea]{@link TextAreaControlObject} field will be initialized with
 */
export function textAreaField<T extends ScreenExtension<T>>(
    properties: TextAreaDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.TextArea>(properties, TextAreaDecorator, FieldKey.TextArea);
}
export function textAreaFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<TextAreaDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.TextArea>(properties);
}
