import type { ClientNode } from '@sage/xtrem-client';
import type { Unit } from '@sage/xtrem-shared';
import type { SizeOptions } from 'carbon-react/esm/components/button/button.component';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { GraphQLFilter } from '../../service/graphql-utils';
import type { ScreenBase } from '../../service/screen-base';
import type { Key } from '../../service/shortcut-service';
import type { GroupAggregationMethods, NestedRecordId, ScreenBaseGenericType, ScreenExtension } from '../../types';
import type {
    DatePropertyValue,
    ValueOrCallback,
    ValueOrCallbackWitRecordValue,
    ValueOrCallbackWithFieldValue,
} from '../../utils/types';
import type { PageAction, TableOptionsMenuItem, TableOptionsMenuType } from '../control-objects';
import type { GridNestedFieldTypes, NestedField, NestedFieldTypes } from '../nested-fields';
import type { FieldKey, PartialCollectionValue, PartialCollectionValueWithIds } from '../types';
import type { PortraitPlaceholderMode } from '../ui/portrait-component';
import type { PropertyValueType } from './reference/reference-types';

export type UnitMode = 'currency' | 'unitOfMeasure';

export type VoidPromise = void | Promise<void>;

export type ErrorHandlerFunction<CT> = (
    this: CT,
    error: any,
    originScreenId: string,
    originElementId: string,
) => Promise<string | void> | (string | void);

export type ServerRecordMapperFunction<
    CT extends ScreenExtension<CT> = any,
    NestedRecordType extends ClientNode = any,
> = (this: CT, record: PartialCollectionValue<NestedRecordType>) => PartialCollectionValue<NestedRecordType>;

export interface HasServerRecordMapperFunction<
    CT extends ScreenExtension<CT> = any,
    NestedRecordType extends ClientNode = any,
> {
    /** Function that can be used to transform the server record before it is displayed in the field */
    mapServerRecord?: ServerRecordMapperFunction<CT, NestedRecordType>;
}

export interface DropdownActionItem<CT extends ScreenExtension<CT>> extends HasIcon {
    access?: CollectionActionAccessConfiguration;
    id?: string;
    isDisabled?: (this: CT) => boolean;
    isHidden?: (this: CT) => boolean;
    isMenuSeparator?: false;
    onClick: (this: CT) => VoidPromise;
    title: string;
}

export interface DropdownActionMenuSeparator<CT extends ScreenExtension<CT>> {
    id?: string;
    isHidden?: (this: CT) => boolean;
    isMenuSeparator?: true;
}

export type DropdownActionItemOrMenuSeparator<CT extends ScreenExtension<CT>> =
    | DropdownActionItem<CT>
    | DropdownActionMenuSeparator<CT>;

export interface CanFetchDefaults {
    /** Should fetch default values for other fields when the field's value is changed. */
    fetchesDefaults?: boolean;
}

export interface HasGenericErrorHandler<CT> {
    onError?: ErrorHandlerFunction<CT>;
}

export interface HasRowChangeIndicators {
    /** Disable indicator background color that signals if a row is added or modified */
    isChangeIndicatorDisabled?: boolean;
}

export interface Clickable<CT> extends HasGenericErrorHandler<CT> {
    /** Function to be executed when the field is clicked */
    onClick?: (this: CT) => VoidPromise;
}

export interface HasOptionsMenu<CT extends ScreenBase> {
    /** Set of predefined graphql filters that are rendered above the table*/
    optionsMenu?: ValueOrCallback<CT, TableOptionsMenuItem[]>;

    /** Define how predefined graphql filters should be displayed. Defaults to 'dropdown' */
    optionsMenuType?: TableOptionsMenuType;
}

export interface CanBeMandatory<CT extends ScreenBase, ContextNodeType = void> {
    /** Whether is mandatory to provide a value for the field or not. Defaults to false */
    isMandatory?: ValueOrCallbackWithFieldValue<CT, boolean, ContextNodeType>;
}

export interface NestedClickable<CT, NestedRecordType> extends HasGenericErrorHandler<CT> {
    /** Function to be executed when the nested field is clicked */
    onClick?: (this: CT, _id: NestedRecordId, data: PartialCollectionValueWithIds<NestedRecordType>) => VoidPromise;
}

export interface NestedGroupAggregations<SelectedMethods = GroupAggregationMethods> {
    groupAggregationMethod?: SelectedMethods;
}

export interface Changeable<CT> extends HasGenericErrorHandler<CT>, CanFetchDefaults {
    /** Function to be executed when the field's value changes */
    onChange?: (this: CT) => VoidPromise;
}

export interface HasInputValueChangeListener<CT> extends HasGenericErrorHandler<CT> {
    /** Function to be executed when the input of the field changes */
    onInputValueChange?: (this: CT, inputValue: string) => void;
}

export interface NestedChangeable<CT> extends HasGenericErrorHandler<CT>, CanFetchDefaults {
    /** Function to be executed when the field's value changes */
    onChange?: (this: CT, _id: any, value: any) => VoidPromise;
}

export interface Nested<NodeType extends ClientNode = any, BindType = PropertyValueType<NodeType>> {
    /** The GraphQL property that the field is bound to */
    bind: BindType;
    /** Enables filtering nested fields from parent component. Defaults to true */
    canFilter?: boolean;
    /**
     * Hides the field in the main table component. It is useful if the field is used with GridRowBlock and you only
     * want the field to appear on the linked block. Defaults to false
     * */
    isExcludedFromMainField?: boolean;

    isHiddenOnMainField?: boolean;
}

export interface Validatable<CT, V = any> {
    /** Additional validation rules, can be a function or a regex (e.g. /^AZ]$/i) */
    validation?:
        | RegExp
        | ((this: CT, value: V) => string | undefined)
        | ((this: CT, value: V) => Promise<string | undefined>);
}

export interface NestedValidatable<CT, V = any, ContextNodeType = void> {
    /** Additional validation rules, can be a function or a regex (e.g. /^AZ]$/i) */
    validation?:
        | RegExp
        | ((this: CT, value: V, rowValue: ContextNodeType) => string | undefined)
        | ((this: CT, value: V, rowValue: ContextNodeType) => Promise<string | undefined>);
}
export interface ContainerValidation<CT> {
    /** Container validation rule */
    validation?: (this: CT) => void;
}

export interface Mappable<CT> {
    /** Function that can be used to set/transform the component text */
    map?: (this: CT, value?: any, rowValue?: any) => string;
}

export interface MappableIcon<CT> {
    /** Function that can be used to add an icon to the component */
    mapIcon?: (this: CT, value?: any) => IconType;
}

export interface HasParent<CT, P> {
    /** The container in which this component will render */
    parent?: (this: CT) => P;
}

export interface HasNestedImagePlaceholder<CT extends ScreenBase, NestedRecordType> {
    placeholderValue?: ValueOrCallbackWitRecordValue<CT, string, NestedRecordType>;
    placeholderMode?: ValueOrCallbackWitRecordValue<CT, PortraitPlaceholderMode, NestedRecordType>;
}

export interface HasImagePlaceholder<CT extends ScreenBase> {
    placeholderValue?: ValueOrCallback<CT, string>;
    placeholderMode?: ValueOrCallback<CT, PortraitPlaceholderMode>;
}

export interface HasOptions<CT extends ScreenBase> extends HasOptionType {
    /** The list of options available in the select field. */
    options?: ValueOrCallbackWithFieldValue<CT, string[]>;
    /** Forces the options to be rendered in alphabetical order. */
    isSortedAlphabetically?: boolean;
}

export interface HasEmptyValue {
    hasEmptyValue?: boolean;
}

export interface HasCollectionSelectionEventHandlers<CT extends ScreenBase, NestedRecordType> {
    /** Function to be executed when a row is selected */
    onRowSelected?: (this: CT, recordId: NestedRecordId, rowItem: NestedRecordType) => VoidPromise;
    /** Function to be executed when a row is unselected */
    onRowUnselected?: (this: CT, recordId: NestedRecordId, rowItem: NestedRecordType) => VoidPromise;
}
export interface HasCollectionAdditionEventHandler<CT extends ScreenBase, NestedRecordType> {
    /** Function to be executed when a row is added by the user & committed to the collection store */
    onRowAdded?: (this: CT, recordId: NestedRecordId, rowItem: NestedRecordType) => void;
    /** This event is triggered when all the available data from the server has been loaded. */
    onAllDataLoaded?: (this: CT) => void;
    /** This event is triggered when new data is received from the server and added to the collection store. */
    onDataLoaded?: (this: CT) => void;
}

export interface HasCollectionSelectionEventHandlersAfter<CT extends ScreenBase, NestedRecordType> {
    /** Function to be executed when a row is selected after the base event */
    onRowSelectedAfter?: (this: CT, recordId: NestedRecordId, rowItem: NestedRecordType) => VoidPromise;
    /** Function to be executed when a row is unselected after the base event */
    onRowUnselectedAfter?: (this: CT, recordId: NestedRecordId, rowItem: NestedRecordType) => VoidPromise;
}
export interface HasOptionType {
    /**
     * The GraphQL node that the select options will be fetched from.
     * When using this property, the node must be an Enum
     */
    optionType?: string;
}

export interface HasOptionDetailsType<CT extends ScreenBase, NestedRecordType = any> {
    /** Function that can be used to display additional data in the dropdown for select like components  */
    mapDetails?: (this: CT, value?: any, rowValue?: NestedRecordType) => string;
}

export interface Sizable {
    /** The field's vertical size, default is medium */
    size?: SizeOptions;
}
export interface HasPlaceholder {
    /** Placeholder to be displayed in the field body */
    placeholder?: string;
}
export interface HasHelperText {
    /** The helper text underneath the field */
    helperText?: string;
}

export interface HasScale<CT extends ScreenBase, R = void> {
    /**
     * Number of digits after the numeric field value decimal point.
     * Must be in the range 0 - 20, inclusive.
     */
    scale?: ValueOrCallbackWithFieldValue<CT, number | null, string, R>;
}

export interface BackgroundColorable<CT extends ScreenBase> {
    /**
     * @deprecated Use `style` instead
     * The background color of the field */
    backgroundColor?: ValueOrCallbackWithFieldValue<CT, string>;
}

export interface Colorable<CT extends ScreenBase> {
    /**
     * @deprecated Use `style` instead
     * The font color of the field */
    color?: ValueOrCallbackWithFieldValue<CT, string>;
}

export interface HasFieldActions<CT> {
    /** Action icons that are displayed in the header of the table */
    fieldActions?: (this: CT) => PageAction[];
}
export interface HasColumns<
    CT extends ScreenExtension<CT>,
    NodeType extends ClientNode = any,
    AvailableNestedFieldTypes extends NestedFieldTypes = GridNestedFieldTypes,
> {
    /** The definitions of the nested fields used to represent the table rows */
    columns?: NestedField<CT, AvailableNestedFieldTypes, NodeType>[];
}

export interface HasFilter<CT extends ScreenBase, NodeType extends ClientNode = any> {
    filter?: ValueOrCallbackWithFieldValue<CT, GraphQLFilter<NodeType> | undefined>;
}

export interface HasNestedFilter<
    CT extends ScreenBase,
    NodeType extends ClientNode = any,
    ReferencedNode extends ClientNode = any,
> {
    filter?:
        | GraphQLFilter<ReferencedNode>
        | ((this: CT, rowValue: NodeType) => GraphQLFilter<ReferencedNode> | undefined);
}

export interface HasNode<CT> {
    /** The GraphQL node that the field represents */
    node?: keyof ScreenBaseGenericType<CT>;
}

export interface Postfixable<CT extends ScreenBase, R = void> {
    /** Text to be displayed inline after the field value */
    postfix?: ValueOrCallbackWithFieldValue<CT, string, string, R>;
}

export interface Prefixable<CT extends ScreenBase, R = void> {
    /** Text to be displayed inline before the field value */
    prefix?: ValueOrCallbackWithFieldValue<CT, string, string, R>;
}

export interface ExtensionField<CT, T> {
    /** The field before this extension field is inserted */
    insertBefore?: (this: CT) => T;
    /** The field after this extension field is inserted */
    insertAfter?: (this: CT) => T;
}

export interface HasIcon {
    /** Icon of the input field. It will be placed on the right side. */
    icon?: IconType;
    /** Color of the icon, only supported in tile containers */
    iconColor?: string;
}
export interface HasDynamicIcon<CT extends ScreenBase> {
    /** Icon of the input field. It will be placed on the right side. */
    icon?: ValueOrCallback<CT, IconType>;
    /** Color of the icon, only supported in tile containers */
    iconColor?: ValueOrCallback<CT, string>;
}
export interface HasDynamicNestedIcon<CT extends ScreenBase, NodeType extends ClientNode = any> {
    /** Icon of the input field. It will be placed on the right side. */
    icon?: ValueOrCallbackWithFieldValue<CT, IconType, string, NodeType>;
    /** Color of the icon, only supported in tile containers */
    iconColor?: ValueOrCallbackWithFieldValue<CT, string, string, NodeType>;
}

export interface HasUnit<CT extends ScreenBase> {
    /** When the unit is set, this mode determines whether the unit should be handled as a unit of measure or a currency. Defaults to 'currency'. */
    unitMode?: UnitMode;

    /** Unit of measure, if set the prefix/postfix and the scale is automatically set based on the user's locale */
    unit?: ValueOrCallback<CT, Unit | null>;
}

export interface NestedHasUnit<CT extends ScreenBase, NodeType extends ClientNode = any> {
    /** When the unit is set, this mode determines whether the unit should be handled as a unit of measure or a currency. Defaults to 'currency'. */
    unitMode?: UnitMode;

    /** Unit of measure, if set the prefix/postfix and the scale is automatically set based on the user's locale */
    unit?: ValueOrCallbackWithFieldValue<CT, Unit | null, string, NodeType>;
}

export interface HasShortcuts {
    /** Key combination that triggers this action */
    shortcut?: Key | Key[];
}
export interface CanBeReadOnly<CT extends ScreenBase, RecordType> {
    /**
     * Whether the field value can only be read or not. Defaults to false
     *
     * The difference with disabled is that readOnly suggests that the field is never editable
     * (e.g. the id field of an object)
     */
    isReadOnly?: ValueOrCallbackWithFieldValue<CT, boolean, any, RecordType>;
}

export type AccessPermission = '$read' | '$update' | '$delete' | '$lookup' | '$create' | string;

export interface AccessConfiguration {
    node?: string;
    bind?: AccessPermission;
}
export interface CollectionActionAccessConfiguration extends Required<AccessConfiguration> {}

export interface HasAccessRights {
    access?: AccessConfiguration;
}

export interface HasDropdownActions<CT extends ScreenExtension<CT>> {
    dropdownActions?: DropdownActionItem<CT>[];
}

export interface HasHeaderLabel<CT extends ScreenExtension<CT>, NodeType extends ClientNode = any> {
    /** This label field is rendered into the header of the pod in line with the title if defined. */
    headerLabel?: NestedField<CT, FieldKey.Label, NodeType>;
}

export interface HasDynamicLookupSuggestions<CT extends ScreenExtension<CT>, NodeType extends ClientNode = any> {
    /* These items added to the lookup dialog and dropdown in addition to the server provided records. */
    additionalLookupRecords?:
        | PartialCollectionValueWithIds<NodeType>[]
        | ((this: CT) => PartialCollectionValueWithIds<NodeType>[]);
}

export interface HasLookupDialogTitle<CT extends ScreenExtension<CT>> {
    /** Lookup Dialog title **/
    lookupDialogTitle?: ValueOrCallback<CT, string>;
}

export interface NestedHasLookupDialogTitle<CT extends ScreenExtension<CT>, NodeType extends ClientNode = any> {
    /** Lookup Dialog title **/
    lookupDialogTitle?: ValueOrCallbackWitRecordValue<CT, string, NodeType>;
}

export interface HasCalendarConfigurationOptions<CT extends ScreenExtension<CT>, NodeType extends ClientNode = any> {
    /** Node property which is used to render the end of an event. If not defined, the event is rendered as a single point in time rather than a time span */
    endDateField?: PropertyValueType<NodeType>;

    /** Node property which is used to render the record as an event on the calendar. If defined, an option is presented to the user to switch to a calendar view */
    startDateField?: PropertyValueType<NodeType>;

    /** Determines the color of the calendar event card */
    cardColor?: ValueOrCallbackWitRecordValue<CT, string, NodeType>;

    /** The latest date the user is allowed to navigate using the calendar */
    maxDate?: DatePropertyValue;

    /** The earliest date the user is allowed to navigate using the calendar */
    minDate?: DatePropertyValue;

    isEventMovable?: ValueOrCallbackWitRecordValue<CT, boolean, NodeType>;
}

export interface HasSound<CT extends ScreenExtension<CT>> {
    isSoundDisabled?: ValueOrCallback<CT, boolean>;
}

export interface MinItems<CT extends ScreenExtension<CT>> {
    minItems?: ValueOrCallback<CT, number>;
}

export interface MaxItems<CT extends ScreenExtension<CT>> {
    maxItems?: ValueOrCallback<CT, number>;
}

export interface NestedMinItems<CT extends ScreenExtension<CT>, NodeType extends ClientNode = any> {
    minItems?: ValueOrCallbackWitRecordValue<CT, number, NodeType>;
}

export interface NestedMaxItems<CT extends ScreenExtension<CT>, NodeType extends ClientNode = any> {
    maxItems?: ValueOrCallbackWitRecordValue<CT, number, NodeType>;
}

export interface HasMaxMinLength<CT extends ScreenExtension<CT>> {
    /** The maximum length of the text field value */
    maxLength?: ValueOrCallback<CT, number>;
    /** The minimum length of the text field value */
    minLength?: ValueOrCallback<CT, number>;
}
export interface HasMaxMin<CT extends ScreenExtension<CT>> {
    /** The maximum value allowed for the numeric field */
    max?: ValueOrCallback<CT, number>;
    /** The minimum value allowed for the numeric field */
    min?: ValueOrCallback<CT, number>;
}

export interface HasValueField<ReferencedItemType extends ClientNode> {
    /** The GraphQL node property that will be used as the reference field value */
    valueField?: PropertyValueType<ReferencedItemType>;
}
export interface HasMandatoryValueField<ReferencedItemType extends ClientNode> {
    /** The GraphQL node property that will be used as the reference field value */
    valueField: PropertyValueType<ReferencedItemType>;
}
export interface HasHelperTextField<ReferencedItemType extends ClientNode> {
    /** The GraphQL node property that will be displayed below the reference field.
     * This value will override the one provided through helperText
     * */
    helperTextField?: PropertyValueType<ReferencedItemType>;
}
export interface HasImageField<ReferencedItemType extends ClientNode> {
    /** The GraphQL node property that will be used to display an image in front of the values. t*/
    imageField?: PropertyValueType<ReferencedItemType>;
}

export interface HasTunnel<ReferencedItemType extends ClientNode> {
    /** Button label for the create new button at the end of the dropdown list and the top of the lookup button. */
    createTunnelLinkText?: string;

    tunnelPageIdField?: PropertyValueType<ReferencedItemType>;

    /** Path reference to an other page. The user can use this page to view the details or modify the selected record.
     * When no value is selected, the user can create a new record using this page.
     * */
    tunnelPage?: string;
}
