import type { ScreenBase } from '../../../service/screen-base';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type { Changeable, Clickable, ExtensionField, HasParent } from '../traits';

export interface VisualProcessProperties<CT extends ScreenBase = ScreenBase> extends EditableFieldProperties<CT> {
    /** The value of the height attribute. Minimum height should be at least the height of the top edit panel and a line.  */
    height?: string;
}

export interface VisualProcessDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<VisualProcessProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        Changeable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>> {}

export type VisualProcessComponentProperties = BaseEditableComponentProperties<
    VisualProcessDecoratorProperties,
    { value: string }
>;
