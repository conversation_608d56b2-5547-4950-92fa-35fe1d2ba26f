/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { VisualProcessProperties } from './visual-process-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a rich, formatted text value
 */
export class VisualProcessControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.VisualProcess,
    FieldComponentProps<FieldKey.VisualProcess>
> {
    static readonly defaultUiProperties: Partial<VisualProcessProperties> = {
        ...EditableFieldControlObject.defaultUiProperties,
        height: '300px',
    };

    @ControlObjectProperty<VisualProcessProperties<CT>, VisualProcessControlObject<CT>>()
    /** The value of the height attribute of the HTML image (e.g. 100px, 75%, auto, etc.)*/
    height?: string;
}
