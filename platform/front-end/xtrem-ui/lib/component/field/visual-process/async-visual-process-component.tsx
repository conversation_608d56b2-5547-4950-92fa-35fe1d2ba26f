import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import type { VisualProcessComponentProperties } from './visual-process-types';

const ConnectedVisualProcessComponent = React.lazy(() => import('./visual-process-component'));

export function AsyncConnectedVisualProcessComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="400px" />}>
            <ConnectedVisualProcessComponent {...props} />
        </React.Suspense>
    );
}

const VisualProcessComponent = React.lazy(() =>
    import('./visual-process-component').then(e => ({ default: e.VisualProcessComponent })),
);

export function AsyncVisualProcessComponent(props: VisualProcessComponentProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={false} bodyHeight="400px" />}>
            <VisualProcessComponent {...props} />
        </React.Suspense>
    );
}
