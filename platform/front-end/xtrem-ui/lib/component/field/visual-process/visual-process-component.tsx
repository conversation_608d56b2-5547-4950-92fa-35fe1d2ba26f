import type {
    ConsumerDataRequestResult,
    GetDataFunction,
    VisualProcessConsumerAction,
    XLinkProps,
} from '@sage/visual-process-editor';
import { VisualProcessEditorComponent, XLinkType } from '@sage/visual-process-editor';
import { isEqual } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import { lookupDialog } from '../../../service/dialog-service';
import { localize } from '../../../service/i18n-service';
import { showToast } from '../../../service/toast-service';
import {
    executeVisualProcessLink,
    getPageLookupDialogCollectionValue,
    getVisualProcessPageLookupProperties,
} from '../../../service/visual-process-service';
import { resolveImageUrl } from '../../../utils/vp-utils';
import type { VisualProcessProperties } from '../../control-objects';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';

const emptyDocument = {
    acts: {},
    layersArr: [{ xpropsArr: [], alpha: 100, visible: true, lock: false, id: 0 }],
    reachedGroupNum: 27,
    contentSize: { xheight: 640, xwidth: 860 },
    docDims: { xheight: 640, xwidth: 860, xtop: 0, xleft: 0 },
    currentLayerId: 1,
};

const DETAILED_BUSINESS_ICON_COLLECTION = 'business-icons';
const listOfDetailedBusinessIcons = [
    'accounting-app',
    'checkbox',
    'hub',
    'pin-map',
    'accounting-desktop-app',
    'checklist-1',
    'incline',
    'pin',
    'accounting',
    'checklist-board',
    'industry',
    'play-button',
    'addons',
    'chemical',
    'info-circle',
    'plus',
    'animal',
    'chess',
    'info-tip',
    'point',
    'app-100-hr',
    'chevron-down',
    'info',
    'pound',
    'app-100',
    'chevron-left',
    'jewellery',
    'power',
    'app-200-laboral',
    'chevron-right',
    'key-hole',
    'presentation',
    'app-200',
    'chevron-up',
    'keys',
    'print',
    'app-300',
    'click-2',
    'lab',
    'program-setting',
    'app-50-essential',
    'clock-2',
    'label',
    'puzzle',
    'app-50-payroll-dollar',
    'clock',
    'laptop',
    'question-circle',
    'app-50-payroll-euro',
    'close',
    'left-circle',
    'question',
    'app-50-payroll-pound',
    'clothes',
    'light-bulb-2',
    'receipts',
    'app-50',
    'cloud-currency-euro',
    'light-bulb-3',
    'recycle',
    'app-accountant-cloud',
    'cloud-currency-pound',
    'light-bulb',
    'redo',
    'app-accounting-start',
    'cloud-dl',
    'lightning',
    'reload-260',
    'app-accounting',
    'cloud-money',
    'like',
    'remote-activate',
    'app-active',
    'cloud-service-2',
    'link',
    'remote',
    'app-add',
    'cloud-service-3',
    'locations',
    'reset-360',
    'app-checked',
    'cloud-service',
    'lock-unlocked',
    'right-circle',
    'app-cloud-connected',
    'cloud-up',
    'lock',
    'rocket',
    'app-compliance-solution',
    'cloud',
    'logo-android',
    'safe',
    'app-despachos-connected-200',
    'compass',
    'logo-apple',
    'sage-city',
    'app-despachos-connected-50',
    'computer-wifi',
    'logo-blogger',
    'sage-kb',
    'app-enterprise-hr',
    'connected-2',
    'logo-facebook',
    'sage-university',
    'app-favorite',
    'connected',
    'logo-google-plus',
    'satelite',
    'app-fixed-assets',
    'conveyer-belt',
    'logo-instagram',
    'scissors',
    'app-for-accountants',
    'cooking',
    'logo-linkedin',
    'sd-card',
    'app-frp-1000',
    'cpu',
    'logo-microsoft-office',
    'search-user',
    'app-gesrest',
    'credit-card-2',
    'logo-microsoft',
    'secured-signin',
    'app-hr-suite-plus',
    'credit-card',
    'logo-pinterest',
    'server',
    'app-integration',
    'crowd-2',
    'logo-sage',
    'service',
    'app-nomina-plus',
    'crowd',
    'logo-salesforce',
    'setting-2',
    'app-pay',
    'crown',
    'logo-twitter',
    'setting',
    'app-payroll-bureau-solutions',
    'dart-board',
    'logo-windows',
    'share',
    'app-payroll-essential',
    'dashboard-setting',
    'logo-xing',
    'shoes',
    'app-payroll-plus',
    'days-60',
    'logo-youtube',
    'shopping-bag',
    'app-processing',
    'db',
    'magnifying-glass',
    'shuffle',
    'app-xrt-treasury',
    'decline',
    'mail-opened',
    'sign',
    'apple',
    'detail',
    'mail',
    'sim',
    'arrow-down-circle',
    'device-setting',
    'map',
    'smartphone-dollar',
    'arrow-down',
    'devices',
    'medal',
    'smartphone-euro',
    'arrow-left-circle',
    'dollar',
    'medical',
    'smartphone-pound',
    'arrow-left',
    'double-check',
    'megaphone',
    'smartphone',
    'arrow-right-circle',
    'download',
    'memo',
    'star',
    'arrow-right',
    'dump-truck',
    'microphone',
    'stationeries',
    'arrow-up-circle',
    'ear',
    'minus',
    'store',
    'arrow-up',
    'ecomm',
    'mobile-purchase-dollar',
    'support',
    'award',
    'enterprise-app',
    'mobile-purchase-euro',
    'switch-board',
    'bag',
    'enterprise-desktop-app',
    'mobile-purchase-pound',
    'sync',
    'bakery',
    'euro',
    'mobile-wifi',
    'tab',
    'balance',
    'excavator',
    'money-bill',
    'tablet-hand',
    'bank',
    'eye',
    'money-dollar',
    'tablet',
    'barcode',
    'factory',
    'money-euro',
    'thermometer',
    'bicycle',
    'filter',
    'money-pound',
    'time-clock',
    'binocular',
    'financial-app-2',
    'money-received-dollar',
    'timer',
    'book-open',
    'financial-app',
    'money-received-euro',
    'tools',
    'book-search',
    'financial-desktop-app-2',
    'money-received-pound',
    'trash-bin',
    'book-tag',
    'financial-desktop-app',
    'monitor-click',
    'travel',
    'book',
    'financial-services',
    'mouse',
    'truck',
    'box-opened',
    'flag',
    'name-card',
    'two-ways',
    'box',
    'flower-abs',
    'name-tag',
    'undo',
    'bright-black',
    'folder-share',
    'newspaper',
    'user-cards',
    'building-1',
    'folder-write',
    'non-profit',
    'user-message',
    'building-2',
    'folder',
    'note',
    'video-game',
    'business-management',
    'food-serve',
    'notebook',
    'video-guide',
    'business-scale',
    'food',
    'office-worker',
    'video',
    'calculator',
    'form',
    'page',
    'wallet',
    'calendar',
    'gauge',
    'payment-app',
    'warehouse',
    'camera',
    'global',
    'payroll-app',
    'warning-2',
    'card-machine',
    'globe',
    'payroll-desktop-app',
    'warning',
    'card-stacked',
    'grad-hat',
    'pen',
    'weather',
    'card',
    'green-offering',
    'pencil',
    'wifi',
    'cart-warehouse-2',
    'green',
    'people-app',
    'wireless',
    'cart-warehouse',
    'handshake',
    'people-chat',
    'wrench',
    'cart',
    'happy',
    'people-network-2',
    'writing',
    'certificate',
    'heart',
    'people-network-3',
    'zoom-in-2',
    'chair',
    'hide',
    'people-network',
    'zoom-in',
    'chart-bar',
    'holiday',
    'phone',
    'zoom-out-2',
    'chat-bubble',
    'home',
    'pie-break',
    'zoom-out',
    'chat',
    'hourglass',
    'piggy-bank',
    'check',
    'hours-24',
    'pin-dropped',
];

export class VisualProcessComponent extends EditableFieldBaseComponent<VisualProcessProperties, { value: string }> {
    private readonly onChange = (document?: any): void => {
        const isEmptyCurrentValue = !this.props.value || !this.props.value.value;
        const isNewValueEmpty = isEqual(document, emptyDocument);

        if (isEmptyCurrentValue && isNewValueEmpty) {
            return;
        }

        const serializedValue = JSON.stringify(document);

        if (serializedValue !== this.props.value?.value) {
            this.handleChange(
                this.props.elementId,
                document ? { value: JSON.stringify(document) } : null,
                this.props.setFieldValue,
                this.props.validate,
                this.triggerChangeListener,
            );
        }
    };

    private readonly onLinkClick = (xlinkProps: XLinkProps): void => {
        if (this.isDisabled()) {
            return;
        }

        executeVisualProcessLink(this.props.screenId, xlinkProps);
    };

    private readonly onGetData: GetDataFunction = async (
        action: VisualProcessConsumerAction,
    ): Promise<ConsumerDataRequestResult> => {
        if (action === 'FUNCTION_LINK_LOOKUP') {
            const lookupDialogPageValue = await getPageLookupDialogCollectionValue(
                this.props.screenId,
                this.props.elementId,
            );

            try {
                const selection = await lookupDialog(this.props.screenId, 'info', {
                    fieldId: this.props.elementId,
                    fieldProperties: getVisualProcessPageLookupProperties(),
                    value: lookupDialogPageValue,
                });
                return selection && selection.length === 1
                    ? { label: selection[0].title, value: selection[0].key }
                    : null;
            } catch {
                /* intentionally left empty */
            }
        }

        if (action === 'FUNCTION_TRANSACTION_LINK_LOOKUP') {
            showToast(
                localize(
                    '@sage/xtrem-ui/visual-process-transactions-not-supported',
                    'Page transactions are not supported',
                ),
            );
        }

        return { label: '', value: '' };
    };

    render(): React.ReactNode {
        const inlineStyle: React.CSSProperties = { minWidth: '100%', minHeight: '300px' };
        const { isTitleHidden, height } = this.props.fieldProperties;

        inlineStyle.height = height;
        return (
            <div
                {...this.getBaseAttributesDivWrapper(
                    'visual-process',
                    `e-visual-process-field${this.isDisabled() ? ' e-visual-process-field-disabled' : ''}`,
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
            >
                {this.getTitle() && !isTitleHidden && <FieldLabel label={this.getTitle()} />}
                <div className="e-visual-process-field-body">
                    <VisualProcessEditorComponent
                        canEdit={!this.isReadOnly() && !this.isDisabled()}
                        onChange={this.onChange}
                        resolveImageUrl={resolveImageUrl}
                        supportedLinkTypes={[XLinkType.URL, XLinkType.ERP]}
                        icons={{ [DETAILED_BUSINESS_ICON_COLLECTION]: listOfDetailedBusinessIcons }}
                        name="vp"
                        onLinkClick={this.onLinkClick}
                        getData={this.onGetData}
                        document={
                            this.props.value && this.props.value.value
                                ? JSON.parse(this.props.value.value)
                                : emptyDocument
                        }
                    />
                </div>
                {!this.props.validationErrors && this.props.fieldProperties.helperText && (
                    <HelperText helperText={this.props.fieldProperties.helperText} />
                )}
            </div>
        );
    }
}

export const ConnectedVisualProcessComponent = connect(mapStateToProps(), mapDispatchToProps())(VisualProcessComponent);

export default ConnectedVisualProcessComponent;
