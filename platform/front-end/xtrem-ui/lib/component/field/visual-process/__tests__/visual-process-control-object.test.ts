import type { <PERSON><PERSON><PERSON> } from '../../../types';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import { VisualProcessControlObject } from '../../../control-objects';
import type { VisualProcessDecoratorProperties } from '../visual-process-types';

describe('Visual process control object', () => {
    let visualProcessControlObject: VisualProcessControlObject;
    let visualProcessFieldProperties: VisualProcessDecoratorProperties;
    let visualProcessValue: any;

    beforeEach(() => {
        visualProcessFieldProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
            height: '345px',
        };

        visualProcessValue = {
            layersArr: [{ someProps: false }],
        };

        visualProcessControlObject = buildControlObject<FieldKey.VisualProcess>(VisualProcessControlObject, {
            fieldValue: visualProcessValue,
            fieldProperties: visualProcessFieldProperties,
        });
    });

    it('should get the field value', () => {
        expect(visualProcessControlObject.value).toEqual(visualProcessValue);
    });

    it('should set the height', () => {
        const newValue = '1234px';
        expect(visualProcessFieldProperties.height).not.toEqual(newValue);
        visualProcessControlObject.height = newValue;
        expect(visualProcessFieldProperties.height).toEqual(newValue);
    });

    it('should get the height field value', () => {
        expect(visualProcessControlObject.height).toEqual(visualProcessFieldProperties.height);
    });

    it('should set the title', () => {
        const newValue = 'Test Radio Field Title';
        expect(visualProcessFieldProperties.title).not.toEqual(newValue);
        visualProcessControlObject.title = newValue;
        expect(visualProcessFieldProperties.title).toEqual(newValue);
    });

    it('should get the title', () => {
        expect(visualProcessFieldProperties.title).toEqual(visualProcessFieldProperties.title);
    });
});
