const visualProcessComponentMock = jest.fn();
// eslint-disable-next-line
const fakeVisualProcessComponent = function () {
    // eslint-disable-next-line
    const args = Array.from(arguments);
    visualProcessComponentMock(...args);
    return <div id="mockComponent" />;
};

jest.mock('@sage/visual-process-editor', () => ({
    __esModule: true,
    ...jest.requireActual('@sage/visual-process-editor'),
    VisualProcessEditorComponent: fakeVisualProcessComponent,
}));

import { getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import * as React from 'react';
import { VisualProcessComponent } from '../visual-process-component';
import { render, waitFor } from '@testing-library/react';
import type { BaseEditableComponentProperties } from '../../field-base-component-types';
import type { VisualProcessProperties } from '../../../control-objects';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux/state';
import { Provider } from 'react-redux';
import * as router from '../../../../service/router';
import * as graphqlUtils from '../../../../service/graphql-utils';
import { actions } from '../../../../redux';

const mockDocument =
    '{"acts":{},"layersArr":[{"xpropsArr":[],"alpha":100,"visible":true,"lock":false,"id":0},{"xpropsArr":[{"xstrokeProps":{"xend":{"xtype":"none"},"xstart":{"xtype":"none"},"xalpha":100,"xcolor":16739950,"xthickness":16,"xtype":"solidstroke"},"xfillProps":{"xtype":"nofill"},"xactionProps":{"xtype":"none"},"xlinkProps":{"xtype":"none"},"xanchors":[{"type":null,"x":64,"y":342},{"type":null,"x":238,"y":342},{"type":null,"x":238,"y":193},{"type":null,"x":64,"y":193}],"xtext":"","xcaptionProps":{"xsizingMode":"fit","xvertAlignMode":"middle"},"xcaptionPos":{"xleft":64,"xtop":265.5},"xcaptionSize":{"xwidth":174,"xheight":4},"xcaptionDeltaPos":{"x":0,"y":0},"xshadowProps":{"xtype":"none"},"group":null,"xdrawBehaviorCode":"K_API_LINE","xlevel":14,"xshapeType":"apishape","uniqueID":"1585569499439173774"},{"xstrokeProps":{"xend":{"xtype":"none"},"xstart":{"xtype":"none"},"xalpha":100,"xcolor":16739950,"xthickness":16,"xtype":"solidstroke"},"xfillProps":{"xgtype":"linear","xalpha":100,"xcolor":12312803,"xtype":"nofill"},"xactionProps":{"xtype":"none"},"xlinkProps":{"xtype":"none"},"xanchors":[{"type":null,"x":229,"y":158.5},{"type":null,"x":274,"y":111},{"type":null,"x":319,"y":158.5},{"type":null,"x":274,"y":206}],"xtext":"","xcaptionProps":{"xsizingMode":"fit","xvertAlignMode":"middle"},"xcaptionPos":{"xleft":221,"xtop":156.5},"xcaptionSize":{"xwidth":106,"xheight":4},"xcaptionDeltaPos":{"x":0,"y":0},"xshadowProps":{"xtype":"none"},"group":null,"xdrawBehaviorCode":"K_API_ELLIPSE","xlevel":15,"xshapeType":"apishape","uniqueID":"1585569513116692611"}],"alpha":100,"visible":true,"lock":false,"id":1}],"reachedGroupNum":27,"contentSize":{"xheight":353.3,"xwidth":338.3},"docDims":{"xheight":640,"xwidth":860,"xtop":0,"xleft":0},"currentLayerId":1}';
type VisualProcessComponentProps = BaseEditableComponentProperties<VisualProcessProperties, { value: string }>;

describe('visual process component', () => {
    let visualProcessComponentProps: VisualProcessComponentProps;
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let fakeRouter: router.Router;
    let executeGraphqlQueryMock: jest.SpyInstance<Promise<any>, any>;

    beforeEach(() => {
        fakeRouter = { goTo: jest.fn() } as unknown as router.Router;
        jest.spyOn(router, 'getRouter').mockReturnValue(fakeRouter);
        executeGraphqlQueryMock = jest.spyOn(graphqlUtils, 'executeGraphqlQuery');

        const mockState = getMockState();
        mockStore = getMockStore(mockState);
        visualProcessComponentMock.mockReset();
        executeGraphqlQueryMock.mockReset();
        visualProcessComponentProps = {
            elementId: 'testVisualProcessField',
            screenId: 'TestScreenId',
            locale: 'en-US',
            fieldProperties: {
                title: 'title radio',
            },
            onFocus: jest.fn(),
            setFieldValue: jest.fn().mockResolvedValue(undefined),
            validate: jest.fn(),
            removeNonNestedErrors: jest.fn(),
            value: { value: mockDocument },
        };
    });
    it('should render without an value and pass it on to the VP library', async () => {
        expect(visualProcessComponentMock).not.toHaveBeenCalled();
        render(
            <Provider store={mockStore}>
                <VisualProcessComponent {...visualProcessComponentProps} value={undefined} />
            </Provider>,
        );
        await waitFor(() => {
            expect(visualProcessComponentMock).toHaveBeenCalledTimes(1);
            expect(visualProcessComponentMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    supportedLinkTypes: ['url', 'erpfunc'],
                    canEdit: true,
                    name: 'vp',
                    document: {
                        acts: {},
                        contentSize: {
                            xheight: 640,
                            xwidth: 860,
                        },
                        currentLayerId: 1,
                        docDims: {
                            xheight: 640,
                            xleft: 0,
                            xtop: 0,
                            xwidth: 860,
                        },
                        layersArr: [
                            {
                                alpha: 100,
                                id: 0,
                                lock: false,
                                visible: true,
                                xpropsArr: [],
                            },
                        ],
                        reachedGroupNum: 27,
                    },
                }),
                {},
            );
        });
    });

    it('should render in view mode if the component is read only', () => {
        visualProcessComponentProps.fieldProperties.isReadOnly = true;
        expect(visualProcessComponentMock).not.toHaveBeenCalled();
        render(
            <Provider store={mockStore}>
                <VisualProcessComponent {...visualProcessComponentProps} value={undefined} />
            </Provider>,
        );
        expect(visualProcessComponentMock).toHaveBeenCalledTimes(1);
        expect(visualProcessComponentMock).toHaveBeenCalledWith(
            expect.objectContaining({
                canEdit: false,
            }),
            {},
        );
    });

    it('should render in view mode if the component is disabled', () => {
        visualProcessComponentProps.fieldProperties.isDisabled = true;
        expect(visualProcessComponentMock).not.toHaveBeenCalled();
        render(
            <Provider store={mockStore}>
                <VisualProcessComponent {...visualProcessComponentProps} value={undefined} />
            </Provider>,
        );
        expect(visualProcessComponentMock).toHaveBeenCalledTimes(1);
        expect(visualProcessComponentMock).toHaveBeenCalledWith(
            expect.objectContaining({
                canEdit: false,
            }),
            {},
        );
    });

    describe('links', () => {
        let windowOpenSpy: jest.SpyInstance<any, any>;

        beforeEach(() => {
            windowOpenSpy = jest.spyOn(window, 'open');
            visualProcessComponentProps.fieldProperties.isReadOnly = true;
        });

        afterEach(() => {
            windowOpenSpy.mockReset();
        });

        it('should throw an error if an unsupported link is triggered', () => {
            render(
                <Provider store={mockStore}>
                    <VisualProcessComponent {...visualProcessComponentProps} value={undefined} />
                </Provider>,
            );
            expect(visualProcessComponentMock).toHaveBeenCalledTimes(1);
            expect(() => {
                visualProcessComponentMock.mock.calls[0][0].onLinkClick({ xtype: 'unsupported', xcode: 'stuff' });
            }).toThrow('Unsupported link format: unsupported');
        });

        it('should open the URL if a URL link object is triggered with a "blank" target', () => {
            render(
                <Provider store={mockStore}>
                    <VisualProcessComponent {...visualProcessComponentProps} value={undefined} />
                </Provider>,
            );
            expect(visualProcessComponentMock).toHaveBeenCalledTimes(1);

            expect(window.open).not.toHaveBeenCalled();
            visualProcessComponentMock.mock.calls[0][0].onLinkClick({
                xtype: 'url',
                xcode: 'https://www.bbc.com/news',
                xtarget: '_blank',
            });
            expect(window.open).toHaveBeenCalledWith('https://www.bbc.com/news', '_blank');
        });

        it('should open the URL within the same window if no target is defined', () => {
            global.window ??= Object.create(window);
            const url = 'http://example.com';
            Object.defineProperty(window, 'location', {
                value: {
                    href: url,
                },
            });
            expect(window.location.href).toEqual(url);
            render(
                <Provider store={mockStore}>
                    <VisualProcessComponent {...visualProcessComponentProps} value={undefined} />
                </Provider>,
            );
            expect(visualProcessComponentMock).toHaveBeenCalledTimes(1);

            expect((global as any).window.location.href).toEqual(url);
            visualProcessComponentMock.mock.calls[0][0].onLinkClick({
                xtype: 'url',
                xcode: 'https://www.bbc.com/news',
            });
            expect(global.window.location.href).toEqual('https://www.bbc.com/news');
        });

        it('should not open the URL if the field is disabled', () => {
            visualProcessComponentProps.fieldProperties.isDisabled = true;
            render(
                <Provider store={mockStore}>
                    <VisualProcessComponent {...visualProcessComponentProps} value={undefined} />
                </Provider>,
            );
            expect(visualProcessComponentMock).toHaveBeenCalledTimes(1);
            expect(fakeRouter.goTo).not.toHaveBeenCalled();
            expect(window.open).not.toHaveBeenCalled();
            visualProcessComponentMock.mock.calls[0][0].onLinkClick({
                xtype: 'url',
                xcode: '@sage/xtrem-test/SomeRandomPage',
                xtarget: '_blank',
            });
            expect(window.open).not.toHaveBeenCalled();
            expect(fakeRouter.goTo).not.toHaveBeenCalled();
        });

        it('should handle internal "URL" links as ERP links', () => {
            render(
                <Provider store={mockStore}>
                    <VisualProcessComponent {...visualProcessComponentProps} value={undefined} />
                </Provider>,
            );
            expect(visualProcessComponentMock).toHaveBeenCalledTimes(1);
            expect(fakeRouter.goTo).not.toHaveBeenCalled();
            expect(window.open).not.toHaveBeenCalled();
            visualProcessComponentMock.mock.calls[0][0].onLinkClick({
                xtype: 'url',
                xcode: '@sage/xtrem-test/SomeRandomPage',
                xtarget: '_blank',
            });
            expect(window.open).not.toHaveBeenCalled();
            expect(fakeRouter.goTo).toHaveBeenCalledWith('@sage/xtrem-test/SomeRandomPage', undefined);
        });

        it('should handle ERP links', () => {
            render(
                <Provider store={mockStore}>
                    <VisualProcessComponent {...visualProcessComponentProps} value={undefined} />
                </Provider>,
            );
            expect(visualProcessComponentMock).toHaveBeenCalledTimes(1);
            expect(fakeRouter.goTo).not.toHaveBeenCalled();
            expect(window.open).not.toHaveBeenCalled();
            visualProcessComponentMock.mock.calls[0][0].onLinkClick({
                xtype: 'erpfunc',
                xlabel: 'Some random page',
                xcode: '@sage/xtrem-test/SomeRandomPage',
            });
            expect(window.open).not.toHaveBeenCalled();
            expect(fakeRouter.goTo).toHaveBeenCalledWith('@sage/xtrem-test/SomeRandomPage', undefined);
        });

        it('should handle ERP links with _ID', () => {
            render(
                <Provider store={mockStore}>
                    <VisualProcessComponent {...visualProcessComponentProps} value={undefined} />
                </Provider>,
            );
            expect(visualProcessComponentMock).toHaveBeenCalledTimes(1);
            expect(fakeRouter.goTo).not.toHaveBeenCalled();
            expect(window.open).not.toHaveBeenCalled();
            visualProcessComponentMock.mock.calls[0][0].onLinkClick({
                xtype: 'erpfunc',
                xlabel: 'Some random page',
                xcode: '@sage/xtrem-test/SomeRandomPage',
                xparam1: '210',
            });
            expect(window.open).not.toHaveBeenCalled();
            expect(fakeRouter.goTo).toHaveBeenCalledWith('@sage/xtrem-test/SomeRandomPage', { _id: '210' });
        });
    });

    describe('Page lookup dialog', () => {
        beforeEach(() => {
            visualProcessComponentProps.fieldProperties.isReadOnly = true;
            executeGraphqlQueryMock.mockResolvedValue({
                data: {
                    pages: [
                        { key: '@sage/xtrem-test/SomePage1', title: 'Some Page 1' },
                        { key: '@sage/xtrem-test/SomePage2', title: 'Some Page 2' },
                        { key: '@sage/xtrem-test/SomePage3', title: 'Some Page 3' },
                        { key: '@sage/xtrem-test/SomePage4', title: 'Some Page 4' },
                        { key: '@sage/xtrem-test/SomePage5', title: 'Some Page 5' },
                        { key: '@sage/xtrem-test/SomePage6', title: 'Some Page 6' },
                        { key: '@sage/xtrem-test/SomePage7', title: 'Some Page 7' },
                        { key: '@sage/xtrem-test/SomePage8', title: 'Some Page 8' },
                        { key: '@sage/xtrem-test/SomePage9', title: 'Some Page 9' },
                        { key: '@sage/xtrem-test/SomePage10', title: 'Some Page 10' },
                    ],
                },
            });
            jest.spyOn(actions, 'openDialog');
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should initiate the lookup dialog with the right properties', async () => {
            render(
                <Provider store={mockStore}>
                    <VisualProcessComponent {...visualProcessComponentProps} value={undefined} />
                </Provider>,
            );
            expect(actions.openDialog).toHaveBeenCalledTimes(0);
            visualProcessComponentMock.mock.calls[0][0].getData('FUNCTION_LINK_LOOKUP');
            await waitFor(() => {
                expect(actions.openDialog).toHaveBeenCalledTimes(1);

                expect(actions.openDialog).toHaveBeenLastCalledWith(
                    expect.any(Number),
                    expect.objectContaining({ title: 'Choose a page' }),
                );
            });
        });
    });
});
