// https://github.com/konvajs/react-konva/issues/584#issuecomment-1120210476
jest.mock('react-reconciler', () => {
    const ReactReconciler = jest.requireActual('react-reconciler');
    return (hostConfig: Record<string, unknown>) => ReactReconciler({ ...hostConfig, warnsIfNotActing: false });
});
jest.useFakeTimers();
import 'jest-styled-components';
import { VisualProcessComponent } from '../visual-process-component';
import { render } from '@testing-library/react';
import React from 'react';
import type { BaseEditableComponentProperties } from '../../field-base-component-types';
import type { VisualProcessProperties } from '../../../control-objects';

const mockDocument =
    '{"acts":{},"layersArr":[{"xpropsArr":[],"alpha":100,"visible":true,"lock":false,"id":0},{"xpropsArr":[{"xstrokeProps":{"xend":{"xtype":"none"},"xstart":{"xtype":"none"},"xalpha":100,"xcolor":16739950,"xthickness":16,"xtype":"solidstroke"},"xfillProps":{"xtype":"nofill"},"xactionProps":{"xtype":"none"},"xlinkProps":{"xtype":"none"},"xanchors":[{"type":null,"x":64,"y":342},{"type":null,"x":238,"y":342},{"type":null,"x":238,"y":193},{"type":null,"x":64,"y":193}],"xtext":"","xcaptionProps":{"xsizingMode":"fit","xvertAlignMode":"middle"},"xcaptionPos":{"xleft":64,"xtop":265.5},"xcaptionSize":{"xwidth":174,"xheight":4},"xcaptionDeltaPos":{"x":0,"y":0},"xshadowProps":{"xtype":"none"},"group":null,"xdrawBehaviorCode":"K_API_LINE","xlevel":14,"xshapeType":"apishape","uniqueID":"1585569499439173774"},{"xstrokeProps":{"xend":{"xtype":"none"},"xstart":{"xtype":"none"},"xalpha":100,"xcolor":16739950,"xthickness":16,"xtype":"solidstroke"},"xfillProps":{"xgtype":"linear","xalpha":100,"xcolor":12312803,"xtype":"nofill"},"xactionProps":{"xtype":"none"},"xlinkProps":{"xtype":"none"},"xanchors":[{"type":null,"x":229,"y":158.5},{"type":null,"x":274,"y":111},{"type":null,"x":319,"y":158.5},{"type":null,"x":274,"y":206}],"xtext":"","xcaptionProps":{"xsizingMode":"fit","xvertAlignMode":"middle"},"xcaptionPos":{"xleft":221,"xtop":156.5},"xcaptionSize":{"xwidth":106,"xheight":4},"xcaptionDeltaPos":{"x":0,"y":0},"xshadowProps":{"xtype":"none"},"group":null,"xdrawBehaviorCode":"K_API_ELLIPSE","xlevel":15,"xshapeType":"apishape","uniqueID":"1585569513116692611"}],"alpha":100,"visible":true,"lock":false,"id":1}],"reachedGroupNum":27,"contentSize":{"xheight":353.3,"xwidth":338.3},"docDims":{"xheight":640,"xwidth":860,"xtop":0,"xleft":0},"currentLayerId":1}';
type VisualProcessComponentProps = BaseEditableComponentProperties<VisualProcessProperties, { value: string }>;

describe('visual process component', () => {
    let visualProcessComponentProps: VisualProcessComponentProps;

    beforeEach(() => {
        visualProcessComponentProps = {
            elementId: 'test-radio',
            screenId: 'screen-id',
            locale: 'en-US',
            fieldProperties: {
                title: 'title radio',
            },
            onFocus: jest.fn(),
            setFieldValue: jest.fn().mockResolvedValue(undefined),
            validate: jest.fn(),
            removeNonNestedErrors: jest.fn(),
            value: { value: mockDocument },
        };
    });

    it('should render without an value', () => {
        const wrapper = render(<VisualProcessComponent {...visualProcessComponentProps} value={undefined} />);
        expect(wrapper.baseElement).toMatchSnapshot();
    });

    it('should render with a valid value', () => {
        const wrapper = render(<VisualProcessComponent {...visualProcessComponentProps} />);
        expect(wrapper.baseElement).toMatchSnapshot();
    });

    it('should render disabled', () => {
        visualProcessComponentProps.fieldProperties.isDisabled = true;
        const wrapper = render(<VisualProcessComponent {...visualProcessComponentProps} />);
        expect(wrapper.baseElement).toMatchSnapshot();
    });

    it('should render in read only mode', () => {
        visualProcessComponentProps.fieldProperties.isReadOnly = true;
        const wrapper = render(<VisualProcessComponent {...visualProcessComponentProps} />);
        expect(wrapper.baseElement).toMatchSnapshot();
    });

    it('should render with some helper text', () => {
        visualProcessComponentProps.fieldProperties.helperText = 'Best Visual Process Ever';
        const wrapper = render(<VisualProcessComponent {...visualProcessComponentProps} />);
        expect(wrapper.baseElement).toMatchSnapshot();
    });

    it('should render with custom height', () => {
        visualProcessComponentProps.fieldProperties.height = '1234px';
        const wrapper = render(<VisualProcessComponent {...visualProcessComponentProps} />);
        expect(wrapper.baseElement).toMatchSnapshot();
    });
});
