import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { visualProcessField } from '../visual-process-decorator';
import type { VisualProcessDecoratorProperties } from '../visual-process-types';

describe('visual process decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'visualProcessField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should set default values when no component properties provided', () => {
            visualProcessField({})({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties = pageMetadata.uiComponentProperties[
                fieldId
            ] as VisualProcessDecoratorProperties;
            expect(mappedComponentProperties.height).toEqual('300px');
            expect(mappedComponentProperties.title).toBeUndefined();
            expect(mappedComponentProperties.onChange).toBeUndefined();
        });

        it('should set values when component properties provided', () => {
            const mockHeight = '400px';
            const mockTitle = 'Random field title';

            visualProcessField({
                height: mockHeight,
                title: mockTitle,
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});

            const mappedComponentProperties = pageMetadata.uiComponentProperties[
                fieldId
            ] as VisualProcessDecoratorProperties;
            expect(mappedComponentProperties.height).toEqual(mockHeight);
            expect(mappedComponentProperties.title).toEqual(mockTitle);
        });

        it('should assign onClick handler', () => {
            testOnClickHandler(visualProcessField, pageMetadata, fieldId);
        });
    });
});
