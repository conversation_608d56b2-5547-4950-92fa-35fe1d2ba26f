// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`visual process component should render disabled 1`] = `
<body>
  <div>
    <div
      class="e-field e-visual-process-field e-visual-process-field-disabled e-disabled"
      data-label="title radio"
      data-testid="e-visual-process-field e-field-label-titleRadio e-field-bind-test-radio"
    >
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
      >
        title radio
      </label>
      <div
        class="e-visual-process-field-body"
      >
        <div
          class="vp-container"
          data-nocontrol="true"
          tabindex="-1"
        >
          <div
            class="vp-stage"
            data-nocontrol="true"
            tabindex="-1"
          >
            <div>
              <div
                class="konvajs-content"
                role="presentation"
                style="position: relative; user-select: none; width: 348.3px; height: 363.3px;"
              >
                <canvas
                  height="363"
                  style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 348.3px; height: 363.3px; display: block;"
                  width="348"
                />
                <canvas
                  height="363"
                  style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 348.3px; height: 363.3px; display: block;"
                  width="348"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
`;

exports[`visual process component should render in read only mode 1`] = `
<body>
  <div>
    <div
      class="e-field e-visual-process-field e-read-only"
      data-label="title radio"
      data-testid="e-visual-process-field e-field-label-titleRadio e-field-bind-test-radio"
    >
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
      >
        title radio
      </label>
      <div
        class="e-visual-process-field-body"
      >
        <div
          class="vp-container"
          data-nocontrol="true"
          tabindex="-1"
        >
          <div
            class="vp-stage"
            data-nocontrol="true"
            tabindex="-1"
          >
            <div>
              <div
                class="konvajs-content"
                role="presentation"
                style="position: relative; user-select: none; width: 348.3px; height: 363.3px;"
              >
                <canvas
                  height="363"
                  style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 348.3px; height: 363.3px; display: block;"
                  width="348"
                />
                <canvas
                  height="363"
                  style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 348.3px; height: 363.3px; display: block;"
                  width="348"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
`;

exports[`visual process component should render with a valid value 1`] = `
<body>
  <div>
    <div
      class="e-field e-visual-process-field"
      data-label="title radio"
      data-testid="e-visual-process-field e-field-label-titleRadio e-field-bind-test-radio"
    >
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
      >
        title radio
      </label>
      <div
        class="e-visual-process-field-body"
      >
        <div
          class="vp-container"
          data-nocontrol="true"
          tabindex="-1"
        >
          <nav
            class="vp-vertical-navbar"
          >
            <ul
              class="vp-vertical-navbar-nav"
            >
              <li
                class="vp-vertical-nav-item vp-vertical-link-create-shape"
                title="Shapes"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    data-name="Layer 1"
                    id="Layer_1"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect
                      fill="none"
                      height="15"
                      stroke="currentColor"
                      stroke-width="2"
                      width="15"
                      x="8"
                      y="1"
                    />
                    <circle
                      cx="8.5"
                      cy="15.5"
                      fill="currentColor"
                      r="7.5"
                      stroke="currentColor"
                      stroke-width="2"
                    />
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Create shape
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-add-icon"
                title="Icon Gallery"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="20px"
                    version="1.1"
                    viewBox="0 0 20 20"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <defs>
                      <path
                        d="M21,23.75 C20.31,23.75 19.75,24.31 19.75,25 C19.75,25.69 20.31,26.25 21,26.25 C21.69,26.25 22.25,25.69 22.25,25 C22.25,24.31 21.69,23.75 21,23.75 L21,23.75 Z M27,23.75 C26.31,23.75 25.75,24.31 25.75,25 C25.75,25.69 26.31,26.25 27,26.25 C27.69,26.25 28.25,25.69 28.25,25 C28.25,24.31 27.69,23.75 27,23.75 L27,23.75 Z M24,14 C18.48,14 14,18.48 14,24 C14,29.52 18.48,34 24,34 C29.52,34 34,29.52 34,24 C34,18.48 29.52,14 24,14 L24,14 Z M24,32 C19.59,32 16,28.41 16,24 C16,23.71 16.02,23.42 16.05,23.14 C18.41,22.09 20.28,20.16 21.26,17.77 C23.07,20.33 26.05,22 29.42,22 C30.2,22 30.95,21.91 31.67,21.74 C31.88,22.45 32,23.21 32,24 C32,28.41 28.41,32 24,32 L24,32 Z"
                        id="path-1"
                      />
                    </defs>
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="Symbol"
                      stroke="none"
                      stroke-width="1"
                    >
                      <g
                        id="Left-bar-/-Def"
                        transform="translate(-14.000000, -206.000000)"
                      >
                        <g
                          id="left-bar"
                        >
                          <g
                            id="Add-Icon"
                            transform="translate(0.000000, 192.000000)"
                          >
                            <mask
                              id="mask-2"
                            >
                              <use
                                xlink:href="#path-1"
                              />
                            </mask>
                            <use
                              fill="currentColor"
                              id="Mask"
                              xlink:href="#path-1"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Add icon
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-add-image"
                title="Upload Image"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="Upload-images"
                      stroke="none"
                      stroke-width="1"
                    >
                      <path
                        d="M22.4986925,1.5 C23.3278417,1.5 24,2.17492314 24,3.01026821 L24,20.9897318 C24,21.8238299 23.3330537,22.5 22.4986925,22.5 L1.50130749,22.5 C0.672158257,22.5 0,21.8250769 0,20.9897318 L0,3.01026821 C0,2.17617011 0.666946292,1.5 1.50130749,1.5 L22.4986925,1.5 Z M22.5,3 L1.5,3 L1.5,19.5 L6.7522274,10.3086021 C7.1652108,9.5858811 7.87532687,9.56299029 8.33427429,10.2514114 L13.5,17.9999997 L16.9335628,14.5664372 C17.5225398,13.9774603 18.399345,14.0324601 18.8998267,14.6997691 L22.5,19.5 L22.5,3 Z M18,4.5 C19.6568542,4.5 21,5.84314575 21,7.5 C21,9.15685425 19.6568542,10.5 18,10.5 C16.3431458,10.5 15,9.15685425 15,7.5 C15,5.84314575 16.3431458,4.5 18,4.5 Z"
                        fill="currentColor"
                        id="icon-picture-landscape"
                      />
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-add-image-badge-hidden"
                  >
                     
                  </span>
                  <input
                    accept="image/png, image/jpeg, image/svg+xml"
                    class="vp-vertical-link-add-image-input"
                    data-nocontrol="true"
                    id="upload_img"
                    type="file"
                  />
                  <span
                    class="vp-vertical-link-text"
                  >
                    Add image
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-add-action"
                title="Actions"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="Actions"
                      stroke="none"
                      stroke-width="1"
                    >
                      <path
                        d="M18.5306064,9.1124564 L12.3816383,9.1124564 L13.4125292,1.03470826 C13.4352978,0.812182319 13.3064082,0.602643176 13.1007719,0.527876307 C12.8951356,0.453109437 12.6663273,0.532593983 12.5473319,0.720133021 L5.06952946,14.1554079 C4.98048885,14.3039285 4.97671604,14.490215 5.05966658,14.6423989 C5.14261711,14.7945829 5.29932379,14.8888741 5.46932974,14.8888953 L11.5264154,14.8888953 L10.7094475,22.9787981 C10.6929296,23.2004758 10.8257201,23.4052104 11.0314623,23.4752762 C11.2372046,23.5453421 11.4630377,23.4627372 11.5791041,23.2749609 L18.9333404,9.8411301 C19.0201609,9.69241216 19.0223123,9.5073089 18.938973,9.35651095 C18.8556344,9.20568313 18.6996504,9.11246148 18.5306064,9.1124564 Z"
                        fill="currentColor"
                        fill-rule="nonzero"
                        id="Path"
                      />
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Actions
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item"
                title="Select All"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g>
                      <path
                        d="M9,9H15V15H9M7,17H17V7H7M15,5H17V3H15M15,21H17V19H15M19,17H21V15H19M19,9H21V7H19M19,21A2,2 0 0,0 21,19H19M19,13H21V11H19M11,21H13V19H11M9,3H7V5H9M3,17H5V15H3M5,21V19H3A2,2 0 0,0 5,21M19,3V5H21A2,2 0 0,0 19,3M13,3H11V5H13M3,9H5V7H3M7,21H9V19H7M3,13H5V11H3M3,5H5V3A2,2 0 0,0 3,5Z"
                        fill="currentColor"
                      />
                    </g>
                  </svg>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-import-document"
                title="Import"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="File-import"
                      stroke="none"
                      stroke-width="1"
                    >
                      <path
                        d="M8.26885506,3.25 L12.0048245,5.74832617 L17.0017173,5.74832617 L18.2506399,7.00587597 L18.2506399,8.26342577 L4.50984599,8.26342577 L4.50984599,20.7382832 L2.01151982,20.7382832 L2,3.25 L8.26885506,3.25 Z M22,9.49581542 L22,20.7382832 L5.75900907,20.7382832 L5.75713824,9.49581542 L22,9.49581542 Z"
                        fill="currentColor"
                        id="icon-folders"
                      />
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-add-image-badge-hidden"
                  >
                     
                  </span>
                  <input
                    accept=".json"
                    class="vp-vertical-link-upload-document-input"
                    data-nocontrol="true"
                    id="upload_doc"
                    type="file"
                  />
                  <span
                    class="vp-vertical-link-text"
                  >
                    Import File
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-export-document"
                title="Export"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      id="left-bar"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        id="Export-document"
                        stroke="none"
                        stroke-width="1"
                      >
                        <path
                          d="M8.24823714,0.00176285663 L8.24823714,3.00176286 L3.74823714,3.00176286 L3.74823714,21.0017629 L8.24823714,21.0017629 L8.24823714,24.0017629 L0.748237143,24.0004554 L0.748237143,0.00307034412 L8.24823714,0.00176285663 Z M15.7539063,6.00176286 L23.2517629,12.0017629 L15.7482371,18.0017629 L15.7482371,13.5017629 L8.24378235,13.5017629 L8.24378235,10.5017629 L15.7482371,10.5017629 L15.7539063,6.00176286 Z"
                          fill="currentColor"
                          id="icon-logout"
                          transform="translate(12.000000, 12.001763) rotate(-90.000000) translate(-12.000000, -12.001763) "
                        />
                      </g>
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Export document
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-undo"
                title="Undo"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <div
                    class="vp-vertical-link-undo-disabled"
                  >
                    <svg
                      height="24px"
                      version="1.1"
                      viewBox="0 0 24 24"
                      width="24px"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        id="Undo"
                        stroke="none"
                        stroke-width="1"
                      >
                        <path
                          d="M20,14.5442148 C20,18.7938197 16.5424239,22.2511254 12.2930894,22.2511254 L9.56498027,22.2511254 C9.11289464,22.2511254 8.74653851,21.8848144 8.74653851,21.4327287 L8.74653851,21.1598998 C8.74653851,20.7077691 9.11284956,20.341458 9.56498027,20.341458 L12.2930894,20.341458 C15.4895557,20.341458 18.0903326,17.7406811 18.0903326,14.5442148 C18.0903326,11.3477486 15.4895557,8.74697163 12.2930894,8.74697163 L7.25988444,8.74697163 L10.7262499,12.2133371 C11.0985557,12.5855077 11.0985557,13.1913835 10.7262499,13.5635541 C10.364987,13.9245465 9.73625897,13.9240056 9.37580745,13.5635541 L4.27972517,8.46733662 C4.09938672,8.28699817 4,8.04720799 4,7.79209287 C4,7.53715804 4.09938672,7.29723264 4.27972517,7.11698434 L9.37607789,2.02054147 C9.73652941,1.65981951 10.3657983,1.65981951 10.7262949,2.02054147 C11.0986008,2.39262192 11.0986008,2.99858794 10.7262949,3.37062331 L7.25992952,6.83725917 L12.2930894,6.83725917 C16.5424239,6.83725917 20,10.2947001 20,14.5442148 Z"
                          fill="currentColor"
                          fill-rule="nonzero"
                        />
                      </g>
                    </svg>
                  </div>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Undo
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-redo"
                title="Redo"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <div
                    class="vp-vertical-link-redo-disabled"
                  >
                    <svg
                      height="24px"
                      version="1.1"
                      viewBox="0 0 24 24"
                      width="24px"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        id="Redo"
                        stroke="none"
                        stroke-width="1"
                      >
                        <path
                          d="M20,14.5442148 C20,18.7938197 16.5424239,22.2511254 12.2930894,22.2511254 L9.56498027,22.2511254 C9.11289464,22.2511254 8.74653851,21.8848144 8.74653851,21.4327287 L8.74653851,21.1598998 C8.74653851,20.7077691 9.11284956,20.341458 9.56498027,20.341458 L12.2930894,20.341458 C15.4895557,20.341458 18.0903326,17.7406811 18.0903326,14.5442148 C18.0903326,11.3477486 15.4895557,8.74697163 12.2930894,8.74697163 L7.25988444,8.74697163 L10.7262499,12.2133371 C11.0985557,12.5855077 11.0985557,13.1913835 10.7262499,13.5635541 C10.364987,13.9245465 9.73625897,13.9240056 9.37580745,13.5635541 L4.27972517,8.46733662 C4.09938672,8.28699817 4,8.04720799 4,7.79209287 C4,7.53715804 4.09938672,7.29723264 4.27972517,7.11698434 L9.37607789,2.02054147 C9.73652941,1.65981951 10.3657983,1.65981951 10.7262949,2.02054147 C11.0986008,2.39262192 11.0986008,2.99858794 10.7262949,3.37062331 L7.25992952,6.83725917 L12.2930894,6.83725917 C16.5424239,6.83725917 20,10.2947001 20,14.5442148 Z"
                          fill="currentColor"
                          fill-rule="nonzero"
                          transform="translate(12.000000, 12.000563) scale(-1, 1) translate(-12.000000, -12.000563) "
                        />
                      </g>
                    </svg>
                  </div>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Redo
                  </span>
                </div>
              </li>
            </ul>
          </nav>
          <div
            class="vp-stage"
            data-nocontrol="true"
            tabindex="-1"
          >
            <div>
              <div
                class="konvajs-content"
                role="presentation"
                style="position: relative; user-select: none; width: 870px; height: 650px;"
              >
                <canvas
                  height="650"
                  style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 870px; height: 650px; display: block;"
                  width="870"
                />
                <canvas
                  height="650"
                  style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 870px; height: 650px; display: block;"
                  width="870"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
`;

exports[`visual process component should render with custom height 1`] = `
<body>
  <div>
    <div
      class="e-field e-visual-process-field"
      data-label="title radio"
      data-testid="e-visual-process-field e-field-label-titleRadio e-field-bind-test-radio"
    >
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
      >
        title radio
      </label>
      <div
        class="e-visual-process-field-body"
      >
        <div
          class="vp-container"
          data-nocontrol="true"
          tabindex="-1"
        >
          <nav
            class="vp-vertical-navbar"
          >
            <ul
              class="vp-vertical-navbar-nav"
            >
              <li
                class="vp-vertical-nav-item vp-vertical-link-create-shape"
                title="Shapes"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    data-name="Layer 1"
                    id="Layer_1"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect
                      fill="none"
                      height="15"
                      stroke="currentColor"
                      stroke-width="2"
                      width="15"
                      x="8"
                      y="1"
                    />
                    <circle
                      cx="8.5"
                      cy="15.5"
                      fill="currentColor"
                      r="7.5"
                      stroke="currentColor"
                      stroke-width="2"
                    />
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Create shape
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-add-icon"
                title="Icon Gallery"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="20px"
                    version="1.1"
                    viewBox="0 0 20 20"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <defs>
                      <path
                        d="M21,23.75 C20.31,23.75 19.75,24.31 19.75,25 C19.75,25.69 20.31,26.25 21,26.25 C21.69,26.25 22.25,25.69 22.25,25 C22.25,24.31 21.69,23.75 21,23.75 L21,23.75 Z M27,23.75 C26.31,23.75 25.75,24.31 25.75,25 C25.75,25.69 26.31,26.25 27,26.25 C27.69,26.25 28.25,25.69 28.25,25 C28.25,24.31 27.69,23.75 27,23.75 L27,23.75 Z M24,14 C18.48,14 14,18.48 14,24 C14,29.52 18.48,34 24,34 C29.52,34 34,29.52 34,24 C34,18.48 29.52,14 24,14 L24,14 Z M24,32 C19.59,32 16,28.41 16,24 C16,23.71 16.02,23.42 16.05,23.14 C18.41,22.09 20.28,20.16 21.26,17.77 C23.07,20.33 26.05,22 29.42,22 C30.2,22 30.95,21.91 31.67,21.74 C31.88,22.45 32,23.21 32,24 C32,28.41 28.41,32 24,32 L24,32 Z"
                        id="path-1"
                      />
                    </defs>
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="Symbol"
                      stroke="none"
                      stroke-width="1"
                    >
                      <g
                        id="Left-bar-/-Def"
                        transform="translate(-14.000000, -206.000000)"
                      >
                        <g
                          id="left-bar"
                        >
                          <g
                            id="Add-Icon"
                            transform="translate(0.000000, 192.000000)"
                          >
                            <mask
                              id="mask-2"
                            >
                              <use
                                xlink:href="#path-1"
                              />
                            </mask>
                            <use
                              fill="currentColor"
                              id="Mask"
                              xlink:href="#path-1"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Add icon
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-add-image"
                title="Upload Image"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="Upload-images"
                      stroke="none"
                      stroke-width="1"
                    >
                      <path
                        d="M22.4986925,1.5 C23.3278417,1.5 24,2.17492314 24,3.01026821 L24,20.9897318 C24,21.8238299 23.3330537,22.5 22.4986925,22.5 L1.50130749,22.5 C0.672158257,22.5 0,21.8250769 0,20.9897318 L0,3.01026821 C0,2.17617011 0.666946292,1.5 1.50130749,1.5 L22.4986925,1.5 Z M22.5,3 L1.5,3 L1.5,19.5 L6.7522274,10.3086021 C7.1652108,9.5858811 7.87532687,9.56299029 8.33427429,10.2514114 L13.5,17.9999997 L16.9335628,14.5664372 C17.5225398,13.9774603 18.399345,14.0324601 18.8998267,14.6997691 L22.5,19.5 L22.5,3 Z M18,4.5 C19.6568542,4.5 21,5.84314575 21,7.5 C21,9.15685425 19.6568542,10.5 18,10.5 C16.3431458,10.5 15,9.15685425 15,7.5 C15,5.84314575 16.3431458,4.5 18,4.5 Z"
                        fill="currentColor"
                        id="icon-picture-landscape"
                      />
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-add-image-badge-hidden"
                  >
                     
                  </span>
                  <input
                    accept="image/png, image/jpeg, image/svg+xml"
                    class="vp-vertical-link-add-image-input"
                    data-nocontrol="true"
                    id="upload_img"
                    type="file"
                  />
                  <span
                    class="vp-vertical-link-text"
                  >
                    Add image
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-add-action"
                title="Actions"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="Actions"
                      stroke="none"
                      stroke-width="1"
                    >
                      <path
                        d="M18.5306064,9.1124564 L12.3816383,9.1124564 L13.4125292,1.03470826 C13.4352978,0.812182319 13.3064082,0.602643176 13.1007719,0.527876307 C12.8951356,0.453109437 12.6663273,0.532593983 12.5473319,0.720133021 L5.06952946,14.1554079 C4.98048885,14.3039285 4.97671604,14.490215 5.05966658,14.6423989 C5.14261711,14.7945829 5.29932379,14.8888741 5.46932974,14.8888953 L11.5264154,14.8888953 L10.7094475,22.9787981 C10.6929296,23.2004758 10.8257201,23.4052104 11.0314623,23.4752762 C11.2372046,23.5453421 11.4630377,23.4627372 11.5791041,23.2749609 L18.9333404,9.8411301 C19.0201609,9.69241216 19.0223123,9.5073089 18.938973,9.35651095 C18.8556344,9.20568313 18.6996504,9.11246148 18.5306064,9.1124564 Z"
                        fill="currentColor"
                        fill-rule="nonzero"
                        id="Path"
                      />
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Actions
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item"
                title="Select All"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g>
                      <path
                        d="M9,9H15V15H9M7,17H17V7H7M15,5H17V3H15M15,21H17V19H15M19,17H21V15H19M19,9H21V7H19M19,21A2,2 0 0,0 21,19H19M19,13H21V11H19M11,21H13V19H11M9,3H7V5H9M3,17H5V15H3M5,21V19H3A2,2 0 0,0 5,21M19,3V5H21A2,2 0 0,0 19,3M13,3H11V5H13M3,9H5V7H3M7,21H9V19H7M3,13H5V11H3M3,5H5V3A2,2 0 0,0 3,5Z"
                        fill="currentColor"
                      />
                    </g>
                  </svg>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-import-document"
                title="Import"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="File-import"
                      stroke="none"
                      stroke-width="1"
                    >
                      <path
                        d="M8.26885506,3.25 L12.0048245,5.74832617 L17.0017173,5.74832617 L18.2506399,7.00587597 L18.2506399,8.26342577 L4.50984599,8.26342577 L4.50984599,20.7382832 L2.01151982,20.7382832 L2,3.25 L8.26885506,3.25 Z M22,9.49581542 L22,20.7382832 L5.75900907,20.7382832 L5.75713824,9.49581542 L22,9.49581542 Z"
                        fill="currentColor"
                        id="icon-folders"
                      />
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-add-image-badge-hidden"
                  >
                     
                  </span>
                  <input
                    accept=".json"
                    class="vp-vertical-link-upload-document-input"
                    data-nocontrol="true"
                    id="upload_doc"
                    type="file"
                  />
                  <span
                    class="vp-vertical-link-text"
                  >
                    Import File
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-export-document"
                title="Export"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      id="left-bar"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        id="Export-document"
                        stroke="none"
                        stroke-width="1"
                      >
                        <path
                          d="M8.24823714,0.00176285663 L8.24823714,3.00176286 L3.74823714,3.00176286 L3.74823714,21.0017629 L8.24823714,21.0017629 L8.24823714,24.0017629 L0.748237143,24.0004554 L0.748237143,0.00307034412 L8.24823714,0.00176285663 Z M15.7539063,6.00176286 L23.2517629,12.0017629 L15.7482371,18.0017629 L15.7482371,13.5017629 L8.24378235,13.5017629 L8.24378235,10.5017629 L15.7482371,10.5017629 L15.7539063,6.00176286 Z"
                          fill="currentColor"
                          id="icon-logout"
                          transform="translate(12.000000, 12.001763) rotate(-90.000000) translate(-12.000000, -12.001763) "
                        />
                      </g>
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Export document
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-undo"
                title="Undo"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <div
                    class="vp-vertical-link-undo-disabled"
                  >
                    <svg
                      height="24px"
                      version="1.1"
                      viewBox="0 0 24 24"
                      width="24px"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        id="Undo"
                        stroke="none"
                        stroke-width="1"
                      >
                        <path
                          d="M20,14.5442148 C20,18.7938197 16.5424239,22.2511254 12.2930894,22.2511254 L9.56498027,22.2511254 C9.11289464,22.2511254 8.74653851,21.8848144 8.74653851,21.4327287 L8.74653851,21.1598998 C8.74653851,20.7077691 9.11284956,20.341458 9.56498027,20.341458 L12.2930894,20.341458 C15.4895557,20.341458 18.0903326,17.7406811 18.0903326,14.5442148 C18.0903326,11.3477486 15.4895557,8.74697163 12.2930894,8.74697163 L7.25988444,8.74697163 L10.7262499,12.2133371 C11.0985557,12.5855077 11.0985557,13.1913835 10.7262499,13.5635541 C10.364987,13.9245465 9.73625897,13.9240056 9.37580745,13.5635541 L4.27972517,8.46733662 C4.09938672,8.28699817 4,8.04720799 4,7.79209287 C4,7.53715804 4.09938672,7.29723264 4.27972517,7.11698434 L9.37607789,2.02054147 C9.73652941,1.65981951 10.3657983,1.65981951 10.7262949,2.02054147 C11.0986008,2.39262192 11.0986008,2.99858794 10.7262949,3.37062331 L7.25992952,6.83725917 L12.2930894,6.83725917 C16.5424239,6.83725917 20,10.2947001 20,14.5442148 Z"
                          fill="currentColor"
                          fill-rule="nonzero"
                        />
                      </g>
                    </svg>
                  </div>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Undo
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-redo"
                title="Redo"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <div
                    class="vp-vertical-link-redo-disabled"
                  >
                    <svg
                      height="24px"
                      version="1.1"
                      viewBox="0 0 24 24"
                      width="24px"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        id="Redo"
                        stroke="none"
                        stroke-width="1"
                      >
                        <path
                          d="M20,14.5442148 C20,18.7938197 16.5424239,22.2511254 12.2930894,22.2511254 L9.56498027,22.2511254 C9.11289464,22.2511254 8.74653851,21.8848144 8.74653851,21.4327287 L8.74653851,21.1598998 C8.74653851,20.7077691 9.11284956,20.341458 9.56498027,20.341458 L12.2930894,20.341458 C15.4895557,20.341458 18.0903326,17.7406811 18.0903326,14.5442148 C18.0903326,11.3477486 15.4895557,8.74697163 12.2930894,8.74697163 L7.25988444,8.74697163 L10.7262499,12.2133371 C11.0985557,12.5855077 11.0985557,13.1913835 10.7262499,13.5635541 C10.364987,13.9245465 9.73625897,13.9240056 9.37580745,13.5635541 L4.27972517,8.46733662 C4.09938672,8.28699817 4,8.04720799 4,7.79209287 C4,7.53715804 4.09938672,7.29723264 4.27972517,7.11698434 L9.37607789,2.02054147 C9.73652941,1.65981951 10.3657983,1.65981951 10.7262949,2.02054147 C11.0986008,2.39262192 11.0986008,2.99858794 10.7262949,3.37062331 L7.25992952,6.83725917 L12.2930894,6.83725917 C16.5424239,6.83725917 20,10.2947001 20,14.5442148 Z"
                          fill="currentColor"
                          fill-rule="nonzero"
                          transform="translate(12.000000, 12.000563) scale(-1, 1) translate(-12.000000, -12.000563) "
                        />
                      </g>
                    </svg>
                  </div>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Redo
                  </span>
                </div>
              </li>
            </ul>
          </nav>
          <div
            class="vp-stage"
            data-nocontrol="true"
            tabindex="-1"
          >
            <div>
              <div
                class="konvajs-content"
                role="presentation"
                style="position: relative; user-select: none; width: 870px; height: 650px;"
              >
                <canvas
                  height="650"
                  style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 870px; height: 650px; display: block;"
                  width="870"
                />
                <canvas
                  height="650"
                  style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 870px; height: 650px; display: block;"
                  width="870"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
`;

exports[`visual process component should render with some helper text 1`] = `
<body>
  <div>
    <div
      class="e-field e-visual-process-field"
      data-label="title radio"
      data-testid="e-visual-process-field e-field-label-titleRadio e-field-bind-test-radio"
    >
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
      >
        title radio
      </label>
      <div
        class="e-visual-process-field-body"
      >
        <div
          class="vp-container"
          data-nocontrol="true"
          tabindex="-1"
        >
          <nav
            class="vp-vertical-navbar"
          >
            <ul
              class="vp-vertical-navbar-nav"
            >
              <li
                class="vp-vertical-nav-item vp-vertical-link-create-shape"
                title="Shapes"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    data-name="Layer 1"
                    id="Layer_1"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect
                      fill="none"
                      height="15"
                      stroke="currentColor"
                      stroke-width="2"
                      width="15"
                      x="8"
                      y="1"
                    />
                    <circle
                      cx="8.5"
                      cy="15.5"
                      fill="currentColor"
                      r="7.5"
                      stroke="currentColor"
                      stroke-width="2"
                    />
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Create shape
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-add-icon"
                title="Icon Gallery"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="20px"
                    version="1.1"
                    viewBox="0 0 20 20"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <defs>
                      <path
                        d="M21,23.75 C20.31,23.75 19.75,24.31 19.75,25 C19.75,25.69 20.31,26.25 21,26.25 C21.69,26.25 22.25,25.69 22.25,25 C22.25,24.31 21.69,23.75 21,23.75 L21,23.75 Z M27,23.75 C26.31,23.75 25.75,24.31 25.75,25 C25.75,25.69 26.31,26.25 27,26.25 C27.69,26.25 28.25,25.69 28.25,25 C28.25,24.31 27.69,23.75 27,23.75 L27,23.75 Z M24,14 C18.48,14 14,18.48 14,24 C14,29.52 18.48,34 24,34 C29.52,34 34,29.52 34,24 C34,18.48 29.52,14 24,14 L24,14 Z M24,32 C19.59,32 16,28.41 16,24 C16,23.71 16.02,23.42 16.05,23.14 C18.41,22.09 20.28,20.16 21.26,17.77 C23.07,20.33 26.05,22 29.42,22 C30.2,22 30.95,21.91 31.67,21.74 C31.88,22.45 32,23.21 32,24 C32,28.41 28.41,32 24,32 L24,32 Z"
                        id="path-1"
                      />
                    </defs>
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="Symbol"
                      stroke="none"
                      stroke-width="1"
                    >
                      <g
                        id="Left-bar-/-Def"
                        transform="translate(-14.000000, -206.000000)"
                      >
                        <g
                          id="left-bar"
                        >
                          <g
                            id="Add-Icon"
                            transform="translate(0.000000, 192.000000)"
                          >
                            <mask
                              id="mask-2"
                            >
                              <use
                                xlink:href="#path-1"
                              />
                            </mask>
                            <use
                              fill="currentColor"
                              id="Mask"
                              xlink:href="#path-1"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Add icon
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-add-image"
                title="Upload Image"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="Upload-images"
                      stroke="none"
                      stroke-width="1"
                    >
                      <path
                        d="M22.4986925,1.5 C23.3278417,1.5 24,2.17492314 24,3.01026821 L24,20.9897318 C24,21.8238299 23.3330537,22.5 22.4986925,22.5 L1.50130749,22.5 C0.672158257,22.5 0,21.8250769 0,20.9897318 L0,3.01026821 C0,2.17617011 0.666946292,1.5 1.50130749,1.5 L22.4986925,1.5 Z M22.5,3 L1.5,3 L1.5,19.5 L6.7522274,10.3086021 C7.1652108,9.5858811 7.87532687,9.56299029 8.33427429,10.2514114 L13.5,17.9999997 L16.9335628,14.5664372 C17.5225398,13.9774603 18.399345,14.0324601 18.8998267,14.6997691 L22.5,19.5 L22.5,3 Z M18,4.5 C19.6568542,4.5 21,5.84314575 21,7.5 C21,9.15685425 19.6568542,10.5 18,10.5 C16.3431458,10.5 15,9.15685425 15,7.5 C15,5.84314575 16.3431458,4.5 18,4.5 Z"
                        fill="currentColor"
                        id="icon-picture-landscape"
                      />
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-add-image-badge-hidden"
                  >
                     
                  </span>
                  <input
                    accept="image/png, image/jpeg, image/svg+xml"
                    class="vp-vertical-link-add-image-input"
                    data-nocontrol="true"
                    id="upload_img"
                    type="file"
                  />
                  <span
                    class="vp-vertical-link-text"
                  >
                    Add image
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-add-action"
                title="Actions"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="Actions"
                      stroke="none"
                      stroke-width="1"
                    >
                      <path
                        d="M18.5306064,9.1124564 L12.3816383,9.1124564 L13.4125292,1.03470826 C13.4352978,0.812182319 13.3064082,0.602643176 13.1007719,0.527876307 C12.8951356,0.453109437 12.6663273,0.532593983 12.5473319,0.720133021 L5.06952946,14.1554079 C4.98048885,14.3039285 4.97671604,14.490215 5.05966658,14.6423989 C5.14261711,14.7945829 5.29932379,14.8888741 5.46932974,14.8888953 L11.5264154,14.8888953 L10.7094475,22.9787981 C10.6929296,23.2004758 10.8257201,23.4052104 11.0314623,23.4752762 C11.2372046,23.5453421 11.4630377,23.4627372 11.5791041,23.2749609 L18.9333404,9.8411301 C19.0201609,9.69241216 19.0223123,9.5073089 18.938973,9.35651095 C18.8556344,9.20568313 18.6996504,9.11246148 18.5306064,9.1124564 Z"
                        fill="currentColor"
                        fill-rule="nonzero"
                        id="Path"
                      />
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Actions
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item"
                title="Select All"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g>
                      <path
                        d="M9,9H15V15H9M7,17H17V7H7M15,5H17V3H15M15,21H17V19H15M19,17H21V15H19M19,9H21V7H19M19,21A2,2 0 0,0 21,19H19M19,13H21V11H19M11,21H13V19H11M9,3H7V5H9M3,17H5V15H3M5,21V19H3A2,2 0 0,0 5,21M19,3V5H21A2,2 0 0,0 19,3M13,3H11V5H13M3,9H5V7H3M7,21H9V19H7M3,13H5V11H3M3,5H5V3A2,2 0 0,0 3,5Z"
                        fill="currentColor"
                      />
                    </g>
                  </svg>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-import-document"
                title="Import"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="File-import"
                      stroke="none"
                      stroke-width="1"
                    >
                      <path
                        d="M8.26885506,3.25 L12.0048245,5.74832617 L17.0017173,5.74832617 L18.2506399,7.00587597 L18.2506399,8.26342577 L4.50984599,8.26342577 L4.50984599,20.7382832 L2.01151982,20.7382832 L2,3.25 L8.26885506,3.25 Z M22,9.49581542 L22,20.7382832 L5.75900907,20.7382832 L5.75713824,9.49581542 L22,9.49581542 Z"
                        fill="currentColor"
                        id="icon-folders"
                      />
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-add-image-badge-hidden"
                  >
                     
                  </span>
                  <input
                    accept=".json"
                    class="vp-vertical-link-upload-document-input"
                    data-nocontrol="true"
                    id="upload_doc"
                    type="file"
                  />
                  <span
                    class="vp-vertical-link-text"
                  >
                    Import File
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-export-document"
                title="Export"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      id="left-bar"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        id="Export-document"
                        stroke="none"
                        stroke-width="1"
                      >
                        <path
                          d="M8.24823714,0.00176285663 L8.24823714,3.00176286 L3.74823714,3.00176286 L3.74823714,21.0017629 L8.24823714,21.0017629 L8.24823714,24.0017629 L0.748237143,24.0004554 L0.748237143,0.00307034412 L8.24823714,0.00176285663 Z M15.7539063,6.00176286 L23.2517629,12.0017629 L15.7482371,18.0017629 L15.7482371,13.5017629 L8.24378235,13.5017629 L8.24378235,10.5017629 L15.7482371,10.5017629 L15.7539063,6.00176286 Z"
                          fill="currentColor"
                          id="icon-logout"
                          transform="translate(12.000000, 12.001763) rotate(-90.000000) translate(-12.000000, -12.001763) "
                        />
                      </g>
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Export document
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-undo"
                title="Undo"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <div
                    class="vp-vertical-link-undo-disabled"
                  >
                    <svg
                      height="24px"
                      version="1.1"
                      viewBox="0 0 24 24"
                      width="24px"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        id="Undo"
                        stroke="none"
                        stroke-width="1"
                      >
                        <path
                          d="M20,14.5442148 C20,18.7938197 16.5424239,22.2511254 12.2930894,22.2511254 L9.56498027,22.2511254 C9.11289464,22.2511254 8.74653851,21.8848144 8.74653851,21.4327287 L8.74653851,21.1598998 C8.74653851,20.7077691 9.11284956,20.341458 9.56498027,20.341458 L12.2930894,20.341458 C15.4895557,20.341458 18.0903326,17.7406811 18.0903326,14.5442148 C18.0903326,11.3477486 15.4895557,8.74697163 12.2930894,8.74697163 L7.25988444,8.74697163 L10.7262499,12.2133371 C11.0985557,12.5855077 11.0985557,13.1913835 10.7262499,13.5635541 C10.364987,13.9245465 9.73625897,13.9240056 9.37580745,13.5635541 L4.27972517,8.46733662 C4.09938672,8.28699817 4,8.04720799 4,7.79209287 C4,7.53715804 4.09938672,7.29723264 4.27972517,7.11698434 L9.37607789,2.02054147 C9.73652941,1.65981951 10.3657983,1.65981951 10.7262949,2.02054147 C11.0986008,2.39262192 11.0986008,2.99858794 10.7262949,3.37062331 L7.25992952,6.83725917 L12.2930894,6.83725917 C16.5424239,6.83725917 20,10.2947001 20,14.5442148 Z"
                          fill="currentColor"
                          fill-rule="nonzero"
                        />
                      </g>
                    </svg>
                  </div>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Undo
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-redo"
                title="Redo"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <div
                    class="vp-vertical-link-redo-disabled"
                  >
                    <svg
                      height="24px"
                      version="1.1"
                      viewBox="0 0 24 24"
                      width="24px"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        id="Redo"
                        stroke="none"
                        stroke-width="1"
                      >
                        <path
                          d="M20,14.5442148 C20,18.7938197 16.5424239,22.2511254 12.2930894,22.2511254 L9.56498027,22.2511254 C9.11289464,22.2511254 8.74653851,21.8848144 8.74653851,21.4327287 L8.74653851,21.1598998 C8.74653851,20.7077691 9.11284956,20.341458 9.56498027,20.341458 L12.2930894,20.341458 C15.4895557,20.341458 18.0903326,17.7406811 18.0903326,14.5442148 C18.0903326,11.3477486 15.4895557,8.74697163 12.2930894,8.74697163 L7.25988444,8.74697163 L10.7262499,12.2133371 C11.0985557,12.5855077 11.0985557,13.1913835 10.7262499,13.5635541 C10.364987,13.9245465 9.73625897,13.9240056 9.37580745,13.5635541 L4.27972517,8.46733662 C4.09938672,8.28699817 4,8.04720799 4,7.79209287 C4,7.53715804 4.09938672,7.29723264 4.27972517,7.11698434 L9.37607789,2.02054147 C9.73652941,1.65981951 10.3657983,1.65981951 10.7262949,2.02054147 C11.0986008,2.39262192 11.0986008,2.99858794 10.7262949,3.37062331 L7.25992952,6.83725917 L12.2930894,6.83725917 C16.5424239,6.83725917 20,10.2947001 20,14.5442148 Z"
                          fill="currentColor"
                          fill-rule="nonzero"
                          transform="translate(12.000000, 12.000563) scale(-1, 1) translate(-12.000000, -12.000563) "
                        />
                      </g>
                    </svg>
                  </div>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Redo
                  </span>
                </div>
              </li>
            </ul>
          </nav>
          <div
            class="vp-stage"
            data-nocontrol="true"
            tabindex="-1"
          >
            <div>
              <div
                class="konvajs-content"
                role="presentation"
                style="position: relative; user-select: none; width: 870px; height: 650px;"
              >
                <canvas
                  height="650"
                  style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 870px; height: 650px; display: block;"
                  width="870"
                />
                <canvas
                  height="650"
                  style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 870px; height: 650px; display: block;"
                  width="870"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <span
        class="common-input__help-text"
        data-element="help"
        data-testid="e-field-helper-text"
      >
        Best Visual Process Ever
      </span>
    </div>
  </div>
</body>
`;

exports[`visual process component should render without an value 1`] = `
<body>
  <div>
    <div
      class="e-field e-visual-process-field"
      data-label="title radio"
      data-testid="e-visual-process-field e-field-label-titleRadio e-field-bind-test-radio"
    >
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
      >
        title radio
      </label>
      <div
        class="e-visual-process-field-body"
      >
        <div
          class="vp-container"
          data-nocontrol="true"
          tabindex="-1"
        >
          <nav
            class="vp-vertical-navbar"
          >
            <ul
              class="vp-vertical-navbar-nav"
            >
              <li
                class="vp-vertical-nav-item vp-vertical-link-create-shape"
                title="Shapes"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    data-name="Layer 1"
                    id="Layer_1"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect
                      fill="none"
                      height="15"
                      stroke="currentColor"
                      stroke-width="2"
                      width="15"
                      x="8"
                      y="1"
                    />
                    <circle
                      cx="8.5"
                      cy="15.5"
                      fill="currentColor"
                      r="7.5"
                      stroke="currentColor"
                      stroke-width="2"
                    />
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Create shape
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-add-icon"
                title="Icon Gallery"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="20px"
                    version="1.1"
                    viewBox="0 0 20 20"
                    width="20px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <defs>
                      <path
                        d="M21,23.75 C20.31,23.75 19.75,24.31 19.75,25 C19.75,25.69 20.31,26.25 21,26.25 C21.69,26.25 22.25,25.69 22.25,25 C22.25,24.31 21.69,23.75 21,23.75 L21,23.75 Z M27,23.75 C26.31,23.75 25.75,24.31 25.75,25 C25.75,25.69 26.31,26.25 27,26.25 C27.69,26.25 28.25,25.69 28.25,25 C28.25,24.31 27.69,23.75 27,23.75 L27,23.75 Z M24,14 C18.48,14 14,18.48 14,24 C14,29.52 18.48,34 24,34 C29.52,34 34,29.52 34,24 C34,18.48 29.52,14 24,14 L24,14 Z M24,32 C19.59,32 16,28.41 16,24 C16,23.71 16.02,23.42 16.05,23.14 C18.41,22.09 20.28,20.16 21.26,17.77 C23.07,20.33 26.05,22 29.42,22 C30.2,22 30.95,21.91 31.67,21.74 C31.88,22.45 32,23.21 32,24 C32,28.41 28.41,32 24,32 L24,32 Z"
                        id="path-1"
                      />
                    </defs>
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="Symbol"
                      stroke="none"
                      stroke-width="1"
                    >
                      <g
                        id="Left-bar-/-Def"
                        transform="translate(-14.000000, -206.000000)"
                      >
                        <g
                          id="left-bar"
                        >
                          <g
                            id="Add-Icon"
                            transform="translate(0.000000, 192.000000)"
                          >
                            <mask
                              id="mask-2"
                            >
                              <use
                                xlink:href="#path-1"
                              />
                            </mask>
                            <use
                              fill="currentColor"
                              id="Mask"
                              xlink:href="#path-1"
                            />
                          </g>
                        </g>
                      </g>
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Add icon
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-add-image"
                title="Upload Image"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="Upload-images"
                      stroke="none"
                      stroke-width="1"
                    >
                      <path
                        d="M22.4986925,1.5 C23.3278417,1.5 24,2.17492314 24,3.01026821 L24,20.9897318 C24,21.8238299 23.3330537,22.5 22.4986925,22.5 L1.50130749,22.5 C0.672158257,22.5 0,21.8250769 0,20.9897318 L0,3.01026821 C0,2.17617011 0.666946292,1.5 1.50130749,1.5 L22.4986925,1.5 Z M22.5,3 L1.5,3 L1.5,19.5 L6.7522274,10.3086021 C7.1652108,9.5858811 7.87532687,9.56299029 8.33427429,10.2514114 L13.5,17.9999997 L16.9335628,14.5664372 C17.5225398,13.9774603 18.399345,14.0324601 18.8998267,14.6997691 L22.5,19.5 L22.5,3 Z M18,4.5 C19.6568542,4.5 21,5.84314575 21,7.5 C21,9.15685425 19.6568542,10.5 18,10.5 C16.3431458,10.5 15,9.15685425 15,7.5 C15,5.84314575 16.3431458,4.5 18,4.5 Z"
                        fill="currentColor"
                        id="icon-picture-landscape"
                      />
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-add-image-badge-hidden"
                  >
                     
                  </span>
                  <input
                    accept="image/png, image/jpeg, image/svg+xml"
                    class="vp-vertical-link-add-image-input"
                    data-nocontrol="true"
                    id="upload_img"
                    type="file"
                  />
                  <span
                    class="vp-vertical-link-text"
                  >
                    Add image
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-add-action"
                title="Actions"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="Actions"
                      stroke="none"
                      stroke-width="1"
                    >
                      <path
                        d="M18.5306064,9.1124564 L12.3816383,9.1124564 L13.4125292,1.03470826 C13.4352978,0.812182319 13.3064082,0.602643176 13.1007719,0.527876307 C12.8951356,0.453109437 12.6663273,0.532593983 12.5473319,0.720133021 L5.06952946,14.1554079 C4.98048885,14.3039285 4.97671604,14.490215 5.05966658,14.6423989 C5.14261711,14.7945829 5.29932379,14.8888741 5.46932974,14.8888953 L11.5264154,14.8888953 L10.7094475,22.9787981 C10.6929296,23.2004758 10.8257201,23.4052104 11.0314623,23.4752762 C11.2372046,23.5453421 11.4630377,23.4627372 11.5791041,23.2749609 L18.9333404,9.8411301 C19.0201609,9.69241216 19.0223123,9.5073089 18.938973,9.35651095 C18.8556344,9.20568313 18.6996504,9.11246148 18.5306064,9.1124564 Z"
                        fill="currentColor"
                        fill-rule="nonzero"
                        id="Path"
                      />
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Actions
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item"
                title="Select All"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g>
                      <path
                        d="M9,9H15V15H9M7,17H17V7H7M15,5H17V3H15M15,21H17V19H15M19,17H21V15H19M19,9H21V7H19M19,21A2,2 0 0,0 21,19H19M19,13H21V11H19M11,21H13V19H11M9,3H7V5H9M3,17H5V15H3M5,21V19H3A2,2 0 0,0 5,21M19,3V5H21A2,2 0 0,0 19,3M13,3H11V5H13M3,9H5V7H3M7,21H9V19H7M3,13H5V11H3M3,5H5V3A2,2 0 0,0 3,5Z"
                        fill="currentColor"
                      />
                    </g>
                  </svg>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-import-document"
                title="Import"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      fill="none"
                      fill-rule="evenodd"
                      id="File-import"
                      stroke="none"
                      stroke-width="1"
                    >
                      <path
                        d="M8.26885506,3.25 L12.0048245,5.74832617 L17.0017173,5.74832617 L18.2506399,7.00587597 L18.2506399,8.26342577 L4.50984599,8.26342577 L4.50984599,20.7382832 L2.01151982,20.7382832 L2,3.25 L8.26885506,3.25 Z M22,9.49581542 L22,20.7382832 L5.75900907,20.7382832 L5.75713824,9.49581542 L22,9.49581542 Z"
                        fill="currentColor"
                        id="icon-folders"
                      />
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-add-image-badge-hidden"
                  >
                     
                  </span>
                  <input
                    accept=".json"
                    class="vp-vertical-link-upload-document-input"
                    data-nocontrol="true"
                    id="upload_doc"
                    type="file"
                  />
                  <span
                    class="vp-vertical-link-text"
                  >
                    Import File
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-export-document"
                title="Export"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <svg
                    height="24px"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="24px"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g
                      id="left-bar"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        id="Export-document"
                        stroke="none"
                        stroke-width="1"
                      >
                        <path
                          d="M8.24823714,0.00176285663 L8.24823714,3.00176286 L3.74823714,3.00176286 L3.74823714,21.0017629 L8.24823714,21.0017629 L8.24823714,24.0017629 L0.748237143,24.0004554 L0.748237143,0.00307034412 L8.24823714,0.00176285663 Z M15.7539063,6.00176286 L23.2517629,12.0017629 L15.7482371,18.0017629 L15.7482371,13.5017629 L8.24378235,13.5017629 L8.24378235,10.5017629 L15.7482371,10.5017629 L15.7539063,6.00176286 Z"
                          fill="currentColor"
                          id="icon-logout"
                          transform="translate(12.000000, 12.001763) rotate(-90.000000) translate(-12.000000, -12.001763) "
                        />
                      </g>
                    </g>
                  </svg>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Export document
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-undo"
                title="Undo"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <div
                    class="vp-vertical-link-undo-disabled"
                  >
                    <svg
                      height="24px"
                      version="1.1"
                      viewBox="0 0 24 24"
                      width="24px"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        id="Undo"
                        stroke="none"
                        stroke-width="1"
                      >
                        <path
                          d="M20,14.5442148 C20,18.7938197 16.5424239,22.2511254 12.2930894,22.2511254 L9.56498027,22.2511254 C9.11289464,22.2511254 8.74653851,21.8848144 8.74653851,21.4327287 L8.74653851,21.1598998 C8.74653851,20.7077691 9.11284956,20.341458 9.56498027,20.341458 L12.2930894,20.341458 C15.4895557,20.341458 18.0903326,17.7406811 18.0903326,14.5442148 C18.0903326,11.3477486 15.4895557,8.74697163 12.2930894,8.74697163 L7.25988444,8.74697163 L10.7262499,12.2133371 C11.0985557,12.5855077 11.0985557,13.1913835 10.7262499,13.5635541 C10.364987,13.9245465 9.73625897,13.9240056 9.37580745,13.5635541 L4.27972517,8.46733662 C4.09938672,8.28699817 4,8.04720799 4,7.79209287 C4,7.53715804 4.09938672,7.29723264 4.27972517,7.11698434 L9.37607789,2.02054147 C9.73652941,1.65981951 10.3657983,1.65981951 10.7262949,2.02054147 C11.0986008,2.39262192 11.0986008,2.99858794 10.7262949,3.37062331 L7.25992952,6.83725917 L12.2930894,6.83725917 C16.5424239,6.83725917 20,10.2947001 20,14.5442148 Z"
                          fill="currentColor"
                          fill-rule="nonzero"
                        />
                      </g>
                    </svg>
                  </div>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Undo
                  </span>
                </div>
              </li>
              <li
                class="vp-vertical-nav-item vp-vertical-link-redo"
                title="Redo"
              >
                <div
                  class="vp-vertical-nav-link"
                >
                  <div
                    class="vp-vertical-link-redo-disabled"
                  >
                    <svg
                      height="24px"
                      version="1.1"
                      viewBox="0 0 24 24"
                      width="24px"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g
                        fill="none"
                        fill-rule="evenodd"
                        id="Redo"
                        stroke="none"
                        stroke-width="1"
                      >
                        <path
                          d="M20,14.5442148 C20,18.7938197 16.5424239,22.2511254 12.2930894,22.2511254 L9.56498027,22.2511254 C9.11289464,22.2511254 8.74653851,21.8848144 8.74653851,21.4327287 L8.74653851,21.1598998 C8.74653851,20.7077691 9.11284956,20.341458 9.56498027,20.341458 L12.2930894,20.341458 C15.4895557,20.341458 18.0903326,17.7406811 18.0903326,14.5442148 C18.0903326,11.3477486 15.4895557,8.74697163 12.2930894,8.74697163 L7.25988444,8.74697163 L10.7262499,12.2133371 C11.0985557,12.5855077 11.0985557,13.1913835 10.7262499,13.5635541 C10.364987,13.9245465 9.73625897,13.9240056 9.37580745,13.5635541 L4.27972517,8.46733662 C4.09938672,8.28699817 4,8.04720799 4,7.79209287 C4,7.53715804 4.09938672,7.29723264 4.27972517,7.11698434 L9.37607789,2.02054147 C9.73652941,1.65981951 10.3657983,1.65981951 10.7262949,2.02054147 C11.0986008,2.39262192 11.0986008,2.99858794 10.7262949,3.37062331 L7.25992952,6.83725917 L12.2930894,6.83725917 C16.5424239,6.83725917 20,10.2947001 20,14.5442148 Z"
                          fill="currentColor"
                          fill-rule="nonzero"
                          transform="translate(12.000000, 12.000563) scale(-1, 1) translate(-12.000000, -12.000563) "
                        />
                      </g>
                    </svg>
                  </div>
                  <span
                    class="vp-vertical-link-text"
                  >
                    Redo
                  </span>
                </div>
              </li>
            </ul>
          </nav>
          <div
            class="vp-stage"
            data-nocontrol="true"
            tabindex="-1"
          >
            <div>
              <div
                class="konvajs-content"
                role="presentation"
                style="position: relative; user-select: none; width: 870px; height: 650px;"
              >
                <canvas
                  height="650"
                  style="padding: 0px; margin: 0px; border: 0px; background: transparent; position: absolute; top: 0px; left: 0px; width: 870px; height: 650px; display: block;"
                  width="870"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
`;
