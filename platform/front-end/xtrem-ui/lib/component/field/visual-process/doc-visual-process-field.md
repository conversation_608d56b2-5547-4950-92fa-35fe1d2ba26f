PATH: XTREEM/UI+Field+Widgets/Visual+Process+Field

## Introduction

The visual process field can be used to edit or render JSON based visual process diagrams. Visual process fields are designed to work with server side `textStream` fields.

## Example:

```ts
@ui.decorators.visualProcessField<NumericField>({
    parent() {
        return this.visualProcessBlock;
    },
    title: 'A visual process field',
    isDisabled: false,
    isReadOnly: false,
    isTransient: false,
    isFullWidth: false,
    onChange() {
        console.log('Doing something when the value is changed');
    },
    onClick() {
        console.log('Doing something when the field is clicked');
    },
    helperText: 'This text goes underneath the field',
})
aVisualProcessField: ui.fields.VisualProcess;
```

### Display decorator properties:

-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **helperText**: The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **isFullWidth**: Whether a field should take the full width of the screen.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.

### Binding decorator properties:

-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

### Event handler decorator properties:

-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.
-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

## Runtime functions

-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **focus()**: Moves the focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).
