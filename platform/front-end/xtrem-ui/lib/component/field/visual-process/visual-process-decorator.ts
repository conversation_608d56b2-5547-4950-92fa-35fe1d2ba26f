/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { VisualProcessControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { VisualProcessDecoratorProperties } from './visual-process-types';

class VisualProcessDecorator extends AbstractFieldDecorator<FieldKey.VisualProcess> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = VisualProcessControlObject;
}
/**
 * Initializes the decorated member as a [VisualProcess]{@link VisualProcessControlObject} field with the provided properties
 *
 * @param properties The properties that the [VisualProcess]{@link VisualProcessControlObject} field will be initialized with
 */
export function visualProcessField<T extends ScreenExtension<T>>(
    properties: VisualProcessDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.VisualProcess>(
        properties,
        VisualProcessDecorator,
        FieldKey.VisualProcess,
    );
}

export function visualProcessFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<VisualProcessDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.VisualProcess>(properties);
}
