import { CollectionValue } from '../../../../service/collection-data-service';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';
import * as graphQlService from '../../../../service/graphql-service';
import type { GraphQLFilter } from '../../../../service/graphql-utils';
import type { Page } from '../../../../service/page';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { Table as TableControlObject } from '../../../control-objects';
import type { TableDecoratorProperties } from '../../../decorators';
import * as nestedFields from '../../../nested-fields';
import type { NestedField, NestedFieldTypes } from '../../../nested-fields';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';

type NodeType = {
    _id: string;
    field1: string;
};

describe('Table Field', () => {
    const screenId = 'TestPage';
    const elementId = 'test';
    const tableValue = [{ _id: 'testId' }];
    let value: CollectionValue;
    let tableFieldControlObject: TableControlObject;
    const properties: TableDecoratorProperties = {
        title: 'TEST_FIELD_TITLE',
        isHidden: true,
        isDisabled: true,
        columns: [],
    };

    let uiComponentProperties: any = {};
    describe('properties and values', () => {
        beforeEach(() => {
            uiComponentProperties = { ...properties };
            value = new CollectionValue({
                screenId,
                elementId,
                isTransient: false,
                hasNextPage: false,
                orderBy: [{}],
                columnDefinitions: [
                    [
                        nestedFields.text<any, any>({ bind: '_id' }),
                        nestedFields.text<any, any>({ bind: 'anyField' }),
                        nestedFields.numeric({ bind: 'someOtherField' }),
                    ],
                ],
                nodeTypes: {},
                nodes: ['@sage/xtrem-test/AnyNode'],
                filter: [undefined],
                initialValues: tableValue,
                fieldType: CollectionFieldTypes.DESKTOP_TABLE,
            });

            tableFieldControlObject = createFieldControlObject<FieldKey.Table>(
                FieldKey.Table,
                screenId,
                TableControlObject,
                elementId,
                value,
                properties,
                {
                    setUiComponentProperties: (_: string, __: string, props: any) => {
                        uiComponentProperties = { ...props };
                    },
                    getUiComponentProperties: () => uiComponentProperties,
                },
            );
        });

        it('getting field value', () => {
            expect(tableFieldControlObject.value).toEqual(tableValue);
        });

        describe('setting and getting updated properties', () => {
            const testTableProps = (
                tableProp: string,
                tablePropValue: string | boolean | number | Array<NestedField<Page, NestedFieldTypes> | string>,
            ) => {
                expect(tableFieldControlObject[tableProp]).not.toEqual(tablePropValue);
                tableFieldControlObject[tableProp] = tablePropValue;
                expect(tableFieldControlObject[tableProp]).toEqual(tablePropValue);
            };

            it('should set the title', () => {
                testTableProps('title', 'Test Title');
            });

            it('should set nested field and its properties', () => {
                const testFixture = [
                    nestedFields.text<any, NodeType>({
                        bind: 'field1',
                        isHiddenMobile: true,
                        isHiddenDesktop: true,
                    }),
                ];
                testTableProps('columns', testFixture);
            });

            it('should set the canFilter columns option', () => {
                testTableProps('canFilter', true);
            });

            it('should set the canAddNewLine option', () => {
                testTableProps('canAddNewLine', false);
            });

            it('should set the canResizeColumns columns option', () => {
                testTableProps('canResizeColumns', true);
            });

            it('should set sortColumns option', () => {
                testTableProps('sortColumns', 0);
            });

            it('should set the canSelect rows option', () => {
                testTableProps('canSelect', true);
            });

            it('should set the selected items', () => {
                testTableProps('selectedRecords', ['1', '2', '3']);
            });

            it('should set the canUserHideColumns columns option', () => {
                testTableProps('canUserHideColumns', false);
            });

            it('should select an item', () => {
                expect(tableFieldControlObject.selectedRecords).toEqual([]);
                tableFieldControlObject.selectRecord('4');
                expect(tableFieldControlObject.selectedRecords).toEqual(['4']);
                tableFieldControlObject.selectRecord('5');
                expect(tableFieldControlObject.selectedRecords).toEqual(['4', '5']);
                tableFieldControlObject.selectRecord('5');
                expect(tableFieldControlObject.selectedRecords).toEqual(['4', '5']);
            });

            it('should select an item and create a new array, not mutate the existing selection array', () => {
                const originalSelectedRecords = ['1', '2'];
                tableFieldControlObject = createFieldControlObject<FieldKey.Table>(
                    FieldKey.Table,
                    screenId,
                    TableControlObject,
                    elementId,
                    value,
                    { ...properties, selectedRecords: originalSelectedRecords },
                );

                expect(tableFieldControlObject.selectedRecords).toEqual(originalSelectedRecords);
                expect(tableFieldControlObject.selectedRecords).not.toBe(originalSelectedRecords);

                tableFieldControlObject.selectRecord('3');
                expect(tableFieldControlObject.selectedRecords).not.toEqual(originalSelectedRecords);
            });

            it('should unselect an item', () => {
                expect(tableFieldControlObject.selectedRecords).toEqual([]);
                tableFieldControlObject.unselectRecord('3');
                expect(tableFieldControlObject.selectedRecords).toEqual([]);
                tableFieldControlObject.selectedRecords = ['2', '3', '4'];
                expect(tableFieldControlObject.selectedRecords).toEqual(['2', '3', '4']);
                tableFieldControlObject.unselectRecord('3');
                expect(tableFieldControlObject.selectedRecords).toEqual(['2', '4']);
                tableFieldControlObject.unselectRecord('3');
                expect(tableFieldControlObject.selectedRecords).toEqual(['2', '4']);
                tableFieldControlObject.unselectRecord('2');
                expect(tableFieldControlObject.selectedRecords).toEqual(['4']);
            });

            it('should unselect an item and create a new array, not mutate the existing selection array', () => {
                const originalSelectedRecords = ['1', '2'];
                tableFieldControlObject = createFieldControlObject<FieldKey.Table>(
                    FieldKey.Table,
                    screenId,
                    TableControlObject,
                    elementId,
                    value,
                    { ...properties, selectedRecords: originalSelectedRecords },
                );

                expect(tableFieldControlObject.selectedRecords).toEqual(originalSelectedRecords);
                expect(tableFieldControlObject.selectedRecords).not.toBe(originalSelectedRecords);

                tableFieldControlObject.unselectRecord('2');
                expect(tableFieldControlObject.selectedRecords).not.toEqual(originalSelectedRecords);
            });

            it('should unselect all items', () => {
                tableFieldControlObject.selectedRecords = ['2', '3', '4'];
                tableFieldControlObject.unselectAllRecords();
                expect(tableFieldControlObject.selectedRecords).toEqual([]);
            });

            it('should hide and unhide (show) columns', () => {
                expect(uiComponentProperties.hiddenColumns).toBeUndefined();
                tableFieldControlObject.hideColumn('column1');
                tableFieldControlObject.hideColumn('column2');
                expect(uiComponentProperties.hiddenColumns).toEqual(['column1', 'column2']);
                tableFieldControlObject.showColumn('column3');
                expect(uiComponentProperties.hiddenColumns).toEqual(['column1', 'column2']);
                tableFieldControlObject.showColumn('column2');
                expect(uiComponentProperties.hiddenColumns).toEqual(['column1']);
                tableFieldControlObject.showColumn('column1');
                expect(uiComponentProperties.hiddenColumns).toEqual([]);
            });
        });

        describe('get and set value of the table', () => {
            beforeEach(() => {
                value = new CollectionValue({
                    screenId,
                    elementId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                            nestedFields.numeric({ bind: 'someOtherField' }),
                        ],
                    ],
                    nodeTypes: {},
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        { _id: '1', anyField: 'test', someOtherField: 4 },
                        { _id: '2', anyField: 'test string 2', someOtherField: -5 },
                        { _id: '3', anyField: 'test aasd', someOtherField: 32432 },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                });

                tableFieldControlObject = createFieldControlObject<FieldKey.Table>(
                    FieldKey.Table,
                    screenId,
                    TableControlObject,
                    elementId,
                    value,
                    properties,
                );
            });

            afterEach(() => {
                jest.resetAllMocks();
            });

            it('should get a row value when the getRecordValue() is called', () => {
                const result = tableFieldControlObject.getRecordValue('2')!;
                expect(result._id).toEqual('2');
                expect(result.anyField).toEqual('test string 2');
                expect(result.someOtherField).toEqual(-5);
                expect(Object.keys(result)).toEqual(['_id', 'anyField', 'someOtherField']);
            });

            it('getRecordByFieldValue() should return an object without any metadata if found', () => {
                const result = tableFieldControlObject.getRecordByFieldValue('someOtherField', -5);
                expect(result!._id).toEqual('2');
                expect(Object.keys(result!)).toEqual(['_id', 'anyField', 'someOtherField']);
            });

            it('get value() should return all records without metadata', () => {
                const result = tableFieldControlObject.value;
                expect(result[0].__action).toBeUndefined();
                expect(result[1].__action).toBeUndefined();
                expect(result[2].__action).toBeUndefined();
            });

            it('get value() should exclude deleted rows', () => {
                expect(tableFieldControlObject.value).toHaveLength(3);
                tableFieldControlObject.removeRecord('1');
                expect(tableFieldControlObject.value).toHaveLength(2);
            });

            it('addRecord should set the table value if it is empty', () => {
                tableFieldControlObject = createFieldControlObject<FieldKey.Table>(
                    FieldKey.Table,
                    screenId,
                    TableControlObject,
                    elementId,
                    null,
                    properties,
                );

                expect(tableFieldControlObject.value).toEqual([]);
                expect(tableFieldControlObject.value).toHaveLength(0);
                const result = tableFieldControlObject.addRecord({
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(result).toStrictEqual({
                    _id: '-1',
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(tableFieldControlObject.value).toHaveLength(1);
            });

            it('addRecord should add record to the DB', () => {
                expect(tableFieldControlObject.value).toHaveLength(3);
                const result = tableFieldControlObject.addRecord({
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(result).toStrictEqual({
                    _id: '-1',
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(tableFieldControlObject.value).toHaveLength(4);
            });

            it('getRecordValue should return addRecord value without any internal property', () => {
                tableFieldControlObject.addRecord({
                    anyField: 'new record',
                    someOtherField: 123,
                });

                expect(tableFieldControlObject.getRecordValue('-1')).toEqual({
                    anyField: 'new record',
                    someOtherField: 123,
                    _id: '-1',
                });
            });

            it('getRecordValue should return addRecord value with internal properties', () => {
                tableFieldControlObject.addRecord({
                    anyField: 'new record',
                    someOtherField: 123,
                });

                expect(tableFieldControlObject.getInternalRowValue('-1')).toEqual({
                    anyField: 'new record',
                    someOtherField: 123,
                    _id: '-1',
                    $loki: 4,
                    __action: 'create',
                    __dirtyColumns: new Set(),
                    __compositeKey: '-1.0',
                    __dirty: true,
                    __path: '0,-1',
                });
            });

            describe('addRecordWithDefaults', () => {
                let fetchNestedDefaultValuesMock;

                beforeEach(() => {
                    fetchNestedDefaultValuesMock = jest
                        .spyOn(graphQlService, 'fetchNestedDefaultValues')
                        .mockReturnValue(
                            Promise.resolve({
                                nestedDefaults: {
                                    anyField: 'new record',
                                    someOtherField: 123,
                                },
                            }),
                        );
                });

                it('addRecordWithDefaults should set the table value if it is empty', async () => {
                    tableFieldControlObject = createFieldControlObject<FieldKey.Table>(
                        FieldKey.Table,
                        screenId,
                        TableControlObject,
                        elementId,
                        null,
                        properties,
                    );

                    expect(tableFieldControlObject.value).toEqual([]);
                    expect(tableFieldControlObject.value).toHaveLength(0);
                    const result = await tableFieldControlObject.addRecordWithDefaults();

                    expect(tableFieldControlObject.value).toHaveLength(1);
                    expect(result).toStrictEqual({
                        _id: '-1',
                        anyField: 'new record',
                        someOtherField: 123,
                    });
                    expect(tableFieldControlObject.value).toHaveLength(1);

                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledTimes(1);
                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledWith({ screenId, elementId });
                });

                it('addRecordWithDefaults should add record to the DB', async () => {
                    expect(tableFieldControlObject.value).toHaveLength(3);
                    const result = await tableFieldControlObject.addRecordWithDefaults();
                    expect(result).toStrictEqual({
                        _id: '-1',
                        anyField: 'new record',
                        someOtherField: 123,
                    });
                    expect(tableFieldControlObject.value).toHaveLength(4);

                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledTimes(1);
                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledWith({ screenId, elementId });
                });

                it('getRecordValue should return addRecordWithDefaults value without any internal property', async () => {
                    await tableFieldControlObject.addRecordWithDefaults();

                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledTimes(1);
                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledWith({ screenId, elementId });

                    expect(tableFieldControlObject.getRecordValue('-1')).toEqual({
                        anyField: 'new record',
                        someOtherField: 123,
                        _id: '-1',
                    });
                });

                it('getRecordValue should return addRecordWithDefaults value with internal properties', async () => {
                    await tableFieldControlObject.addRecordWithDefaults();

                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledTimes(1);
                    expect(fetchNestedDefaultValuesMock).toHaveBeenCalledWith({ screenId, elementId });

                    expect(tableFieldControlObject.getInternalRowValue('-1')).toEqual({
                        anyField: 'new record',
                        someOtherField: 123,
                        _id: '-1',
                        $loki: 4,
                        __dirtyColumns: new Set(),
                        __action: 'create',
                        __compositeKey: '-1.0',
                        __dirty: true,
                        __path: '0,-1',
                    });
                });
            });

            it('addOrUpdateValue should set the table value if it is empty', () => {
                tableFieldControlObject = createFieldControlObject<FieldKey.Table>(
                    FieldKey.Table,
                    screenId,
                    TableControlObject,
                    elementId,
                    null,
                    properties,
                );

                expect(tableFieldControlObject.value).toEqual([]);
                expect(tableFieldControlObject.value).toHaveLength(0);
                const result = tableFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'new record',
                    someOtherField: 123,
                });
                expect(tableFieldControlObject.value).toHaveLength(1);
                expect(result._id).toEqual('-1');
                expect(tableFieldControlObject.value).toHaveLength(1);
            });

            it('addOrUpdateValue should add record to the DB if it does not have an ID', () => {
                expect(tableFieldControlObject.value).toHaveLength(3);
                const result = tableFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'new record',
                    someOtherField: 123,
                });

                expect(result._id).toEqual('-1');
                expect(tableFieldControlObject.value).toHaveLength(4);
            });

            it('addOrUpdateValue should add record to the DB if its ID does not exist', () => {
                expect(tableFieldControlObject.value).toHaveLength(3);
                const result = tableFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'new record',
                    someOtherField: 123,
                    _id: '123',
                });

                expect(result._id).toEqual('123');
                expect(tableFieldControlObject.value).toHaveLength(4);
            });

            it('should return updated value without any internal property', () => {
                tableFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'updated record',
                    someOtherField: 123,
                    _id: '2',
                })!;

                expect(tableFieldControlObject.getRecordValue('2')).toEqual({
                    anyField: 'updated record',
                    someOtherField: 123,
                    _id: '2',
                });
            });

            it('should return updated value with internal properties', () => {
                tableFieldControlObject.addOrUpdateRecordValue({
                    anyField: 'updated record',
                    someOtherField: 123,
                    _id: '2',
                })!;

                expect(tableFieldControlObject.getInternalRowValue('2')).toEqual({
                    anyField: 'updated record',
                    someOtherField: 123,
                    _id: '2',
                    $loki: 2,
                    __action: 'update',
                    __compositeKey: '2.0',
                    __dirtyColumns: new Set(['anyField', 'someOtherField']),
                    __dirty: true,
                    __path: '0,2',
                });
            });
        });

        describe('Add or update value', () => {
            beforeEach(() => {
                value = new CollectionValue({
                    screenId,
                    elementId,
                    isTransient: false,
                    hasNextPage: true,
                    orderBy: [{ anyField: 1 }],
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                            nestedFields.numeric({ bind: 'someOtherField' }),
                            nestedFields.reference({
                                bind: 'reference',
                                valueField: 'property',
                                node: '@sage/xtrem-test/AnyOtherNode',
                            }),
                            nestedFields.reference({
                                bind: 'reference',
                                valueField: 'other',
                                node: '@sage/xtrem-test/AnyNode',
                            }),
                        ],
                    ],
                    nodeTypes: {},
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        {
                            _id: '1',
                            anyField: 'test',
                            someOtherField: 4,
                            reference__property: { _id: '1', property: 'referenceValue1' },
                            reference__other: { _id: '1', other: 'other1' },
                        },
                        {
                            _id: '2',
                            anyField: 'test string 2',
                            someOtherField: -5,
                            reference__property: { _id: '2', property: 'referenceValue2' },
                            reference__other: { _id: '2', other: 'other2' },
                        },
                        {
                            _id: '3',
                            anyField: 'test aasd',
                            someOtherField: 32432,
                            reference__property: { _id: '3', property: 'referenceValue3' },
                            reference__other: { _id: '3', other: 'other3' },
                        },
                    ],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                });

                tableFieldControlObject = createFieldControlObject<FieldKey.Table>(
                    FieldKey.Table,
                    screenId,
                    TableControlObject,
                    elementId,
                    value,
                    properties,
                );
            });

            afterEach(() => {
                jest.resetAllMocks();
            });

            it('should be able to update a nested reference field', () => {
                const result = tableFieldControlObject.getRecordValue('2')!;
                expect(result._id).toEqual('2');
                expect(result.anyField).toEqual('test string 2');
                expect(result.someOtherField).toEqual(-5);
                expect(result.reference).toEqual({ _id: '2', property: 'referenceValue2', other: 'other2' });
                expect(Object.keys(result).sort()).toEqual(['_id', 'anyField', 'reference', 'someOtherField']);
                const updated = tableFieldControlObject.addOrUpdateRecordValue({
                    _id: 2,
                    reference: { _id: '2', property: 'newProperty', other: 'newOther' },
                });
                expect(updated).toEqual({
                    _id: '2',
                    anyField: 'test string 2',
                    reference: {
                        _id: '2',
                        other: 'newOther',
                        property: 'newProperty',
                    },
                    someOtherField: -5,
                });
                expect((value as any).db.findOne({ id: '2' })).toEqual({
                    _id: '2',
                    anyField: 'test string 2',
                    reference: {
                        _id: '2',
                        other: 'newOther',
                        property: 'newProperty',
                    },
                    someOtherField: -5,
                });
            });
        });

        describe('refreshRecord', () => {
            beforeEach(() => {
                jest.spyOn(value, 'refreshRecord').mockResolvedValue({ _id: 3, someField: 4 });
            });

            it("should call collection value's refreshRecord function", async () => {
                expect(value.refreshRecord).not.toHaveBeenCalled();
                const result = await tableFieldControlObject.refreshRecord('4');
                expect(result).toEqual({ _id: 3, someField: 4 });
                expect(value.refreshRecord).toHaveBeenCalledWith({ recordId: '4', skipUpdate: false });
            });
        });
    });

    describe('GraphQl filter property', () => {
        let setUiComponentProperties: jest.Mock<void, [string, string, TableDecoratorProperties]>;
        let refresh: jest.Mock<Promise<FieldInternalValue<FieldKey.Table>>>;
        let newProperties: TableDecoratorProperties;
        const executeTest = async (filter: GraphQLFilter | (() => GraphQLFilter)) => {
            tableFieldControlObject.filter = filter;
            expect(setUiComponentProperties).toHaveBeenCalled();
            expect(refresh).toHaveBeenCalled();
            expect(newProperties.filter).toEqual(filter);
        };

        beforeEach(() => {
            setUiComponentProperties = jest.fn(
                (_screenId: string, _elementId: string, _value: TableDecoratorProperties) => {
                    newProperties = { ..._value };
                },
            );

            refresh = jest.fn();
            tableFieldControlObject = createFieldControlObject<FieldKey.Table>(
                FieldKey.Table,
                screenId,
                TableControlObject,
                elementId,
                value,
                properties,
                { setUiComponentProperties, refresh },
            );
        });

        it('should update filter with GraphQL filter object', () => {
            executeTest({ description: { _regex: 'policy', _options: 'i' } });
        });

        it('should refresh field keeping all modifications', () => {
            const filter = { description: { _regex: 'policy', _options: 'i' } };
            tableFieldControlObject.filter = filter;
            expect(setUiComponentProperties).toHaveBeenCalled();
            expect(refresh).toHaveBeenCalledWith({
                elementId,
                keepModifications: true,
                keepPageInfo: true,
                screenId,
            });
            expect(newProperties.filter).toEqual(filter);
        });

        it('should update filter with function', () => {
            executeTest(() => ({ description: { _regex: 'policy', _options: 'i' } }));
        });
    });
});
