import {
    addFieldToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';

jest.mock('../../icon/async-icon-component');
jest.mock('../../select/async-select-component');
jest.mock('../../text/async-text-component');
jest.mock('../../checkbox/async-checkbox-component');
jest.mock('../../progress/async-progress-component');
jest.mock('../../link/async-link-component');
jest.mock('../../text-area/async-text-area-component');
jest.mock('../../reference/async-reference-component');
jest.mock('../../label/async-label-component');
jest.mock('../../numeric/async-numeric-component');
jest.mock('../../date/async-date-component');
jest.mock('../../image/async-image-component');

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux';
import type { ScreenBase } from '../../../../service/screen-base';
import type { Dict } from '@sage/xtrem-shared';
import type { NestedField } from '../../../nested-fields';
import {
    CheckboxControlObject,
    IconControlObject,
    ImageControlObject,
    LabelControlObject,
    LinkControlObject,
    ProgressControlObject,
    TextControlObject,
    SelectControlObject,
    ReferenceControlObject,
    NumericControlObject,
    DateControlObject,
} from '../../../control-objects';
import { MobileTableComponent } from '../mobile-table-component';
import * as FilterComponent from '../../../ui/filter/filters-component';
import type { Filter } from '../../../../service/filter-service';
import { CollectionValue } from '../../../../service/collection-data-service';
import type {
    InternalTableProperties,
    TableDecoratorProperties,
    TableInternalComponentProps,
} from '../table-component-types';
import * as eventHandlers from '../../../../utils/events';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';
import { FieldKey } from '../../../types';
import { waitFor } from '@testing-library/dom';
import { fireEvent, render } from '@testing-library/react';
import type { FormattedNodeDetails } from '../../../../service/metadata-types';
import { GraphQLKind } from '../../../../types';

const testGlobalsGenerator = () => {
    const screenId = 'TestPage';
    const elementId = 'fieldId';
    const nodeTypes: Dict<FormattedNodeDetails> = {
        AnyNode: {
            title: 'AnyNode',
            name: 'AnyNode',
            packageName: '@sage/xtrem-test',
            properties: {
                _id: { kind: GraphQLKind.Scalar, type: 'Id' },
                reference: { kind: 'OBJECT', type: 'AnotherType' },
                checkbox: { kind: GraphQLKind.Scalar, type: 'Boolean' },
                date: { kind: GraphQLKind.Scalar, type: 'Date' },
                label: { kind: GraphQLKind.Scalar, type: 'String' },
                select: { kind: GraphQLKind.Scalar, type: 'String' },
                text: { kind: GraphQLKind.Scalar, type: 'String' },
                link: { kind: GraphQLKind.Scalar, type: 'String' },
                numeric: { kind: GraphQLKind.Scalar, type: 'Float' },
                progress: { kind: GraphQLKind.Scalar, type: 'Int' },
            },
            mutations: {},
        },
        AnotherType: {
            title: 'AnyNode',
            name: 'AnyNode',
            packageName: '@sage/xtrem-test',
            properties: {
                _id: { kind: GraphQLKind.Scalar, type: 'Id' },
                status: { kind: GraphQLKind.Scalar, type: 'String' },
                title: { kind: GraphQLKind.Scalar, type: 'String' },
                code: { kind: GraphQLKind.Scalar, type: 'String' },
            },
            mutations: {},
        },
    };
    const nestedFields: Dict<NestedField<ScreenBase, any>> = {
        checkbox: {
            defaultUiProperties: {
                ...CheckboxControlObject.defaultUiProperties,
                bind: 'checkbox',
            },
            properties: {
                bind: 'checkbox',
                title: 'Checkbox',
            },
            type: FieldKey.Checkbox,
        },
        date: {
            defaultUiProperties: {
                ...DateControlObject.defaultUiProperties,
                bind: 'date',
            },
            properties: {
                bind: 'date',
                title: 'Date',
            },
            type: FieldKey.Date,
        },
        icon: {
            defaultUiProperties: {
                ...IconControlObject.defaultUiProperties,
                bind: 'icon',
            },
            properties: {
                bind: 'icon',
                title: 'Icon',
            },
            type: FieldKey.Icon,
        },
        image: {
            defaultUiProperties: {
                ...ImageControlObject.defaultUiProperties,
                bind: 'image',
            },
            properties: {
                bind: 'image',
                title: 'Image',
            },
            type: FieldKey.Image,
        },
        label: {
            defaultUiProperties: {
                ...LabelControlObject.defaultUiProperties,
                bind: 'label',
            },
            properties: {
                bind: 'label',
                title: 'Label',
            },
            type: FieldKey.Label,
        },
        link: {
            defaultUiProperties: {
                ...LinkControlObject.defaultUiProperties,
                bind: 'link',
            },
            properties: {
                bind: 'link',
                title: 'Link',
            },
            type: FieldKey.Link,
        },
        numeric: {
            defaultUiProperties: {
                ...NumericControlObject.defaultUiProperties,
                bind: 'numeric',
            },
            properties: {
                bind: 'numeric',
                title: 'Numeric',
            },
            type: FieldKey.Numeric,
        },
        progress: {
            defaultUiProperties: { ...ProgressControlObject.defaultUiProperties, bind: 'progress' },
            properties: {
                bind: 'progress',
                title: 'Progress',
            },
            type: FieldKey.Progress,
        },
        reference: {
            defaultUiProperties: {
                ...ReferenceControlObject.defaultUiProperties,
                bind: 'reference',
            },
            properties: {
                bind: 'reference',
                title: 'Reference',
                valueField: 'code',
                helperTextField: 'description',
            },
            type: FieldKey.Reference,
        },
        select: {
            defaultUiProperties: {
                ...SelectControlObject.defaultUiProperties,
                bind: 'select',
            },
            properties: {
                bind: 'select',
                title: 'Select',
            },
            type: FieldKey.Select,
        },
        text: {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: 'text',
            },
            properties: {
                bind: 'text',
                title: 'Text',
            },
            type: FieldKey.Text,
        },
    };

    const tableProperties: TableDecoratorProperties = {
        canSelect: false,
        node: '@sage/xtrem-test/AnyNode',
        columns: [
            nestedFields.checkbox,
            nestedFields.date,
            nestedFields.icon,
            nestedFields.image,
            nestedFields.label,
            nestedFields.link,
            nestedFields.numeric,
            nestedFields.progress,
            nestedFields.reference,
            nestedFields.select,
            nestedFields.text,
        ],
    };
    const tableValue = new CollectionValue({
        screenId,
        elementId,
        isTransient: true,
        hasNextPage: false,
        orderBy: [{ text: 1 }],
        columnDefinitions: [tableProperties.columns!],
        nodeTypes,
        nodes: ['@sage/xtrem-test/AnyNode'],
        filter: [undefined],
        initialValues: [
            {
                checkbox: true,
                date: '2019-11-10',
                image: {
                    value: 'iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==',
                },
                label: 'Label value',
                link: 'Link value',
                numeric: 421,
                progress: 731,
                reference__code: {
                    code: 'How 0',
                    description: 'come 0',
                },
                select: 'Select value 0',
                text: 'Text value 0',
                __cursor: '#1',
            },
            {
                checkbox: true,
                date: '2019-11-11',
                image: {
                    value: 'iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==',
                },
                label: 'Label value',
                link: 'Link value',
                numeric: 421,
                progress: 731,
                reference__code: {
                    code: 'How 1',
                    description: 'come 1',
                },
                select: 'Select value 1',
                text: 'Text value 1',
                __cursor: '#2',
            },
            {
                checkbox: true,
                date: '2019-11-12',
                image: {
                    value: 'iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==',
                },
                label: 'Label value',
                link: 'Link value',
                numeric: 422,
                progress: 732,
                reference__code: {
                    code: 'How 2',
                    description: 'come 2',
                },
                select: 'Select value 2',
                text: 'Text value 2',
                __cursor: '#3',
            },
            {
                checkbox: false,
                date: '2019-11-13',
                image: {
                    value: 'iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==',
                },
                label: 'Label value',
                link: 'Link value',
                numeric: 423,
                progress: 733,
                reference__code: {
                    code: 'How',
                    description: 'come',
                },
                select: 'Select value 3',
                text: 'Text value 3',
                __cursor: '#4',
            },
            {
                checkbox: true,
                date: '2019-11-14',
                image: {
                    value: 'iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==',
                },
                label: 'Label value',
                link: 'Link value',
                numeric: 424,
                progress: 734,
                reference__code: {
                    code: 'How 4',
                    description: 'come 4',
                },
                select: 'Select value',
                text: 'Text value 4',
                __cursor: '#5',
            },
            {
                checkbox: false,
                date: '2019-11-15',
                image: {
                    value: 'iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==',
                },
                label: 'Label value',
                link: 'Link value',
                numeric: 425,
                progress: 735,
                reference__code: {
                    code: 'How',
                    description: 'come',
                },
                select: 'Select value 5',
                text: 'Text value 5',
                __cursor: '#6',
            },
        ],
        locale: 'en-US',
    });
    const tableFilters: Filter[] = [
        {
            id: 'filterId',
            value: [
                {
                    filterValue: 'filterValue',
                    filterType: {
                        value: 'contains',
                        text: 'filterText',
                    },
                },
            ],
        },
    ];

    return {
        elementId,
        nestedFields,
        screenId,
        tableFilters,
        tableProperties,
        tableValue,
        nodeTypes,
    };
};

describe('Mobile table component', () => {
    let testGlobals: ReturnType<typeof testGlobalsGenerator> & { store?: MockStoreEnhanced<XtremAppState> };
    let state: XtremAppState;
    let triggerEventListenerMock: jest.MockInstance<any, any>;

    const renderTableComponent = async (
        screenId: string,
        fieldProperties: TableDecoratorProperties,
        nodeTypes: Dict<FormattedNodeDetails>,
        value?: CollectionValue,
        additionalProps: Partial<TableInternalComponentProps> = {},
        selector?: string,
    ) => {
        const result = render(getMobileTableComponent(screenId, fieldProperties, nodeTypes, value, additionalProps));
        await waitFor(() => {
            expect(result.container.querySelector(selector ?? '.e-card')).not.toBeNull();
        });

        return result;
    };

    const getMobileTableComponent = (
        screenId: string,
        fieldProperties: TableDecoratorProperties,
        nodeTypes: Dict<FormattedNodeDetails>,
        value?: CollectionValue,
        additionalProps: Partial<TableInternalComponentProps> = {},
    ) => (
        <Provider store={testGlobals.store!}>
            <MobileTableComponent
                elementId={testGlobals.elementId}
                fieldProperties={fieldProperties}
                enumTypes={{}}
                dataTypes={{}}
                nodeTypes={nodeTypes}
                screenId={screenId}
                value={value as any}
                setFieldProperties={jest.fn()}
                accessBindings={{}}
                validationErrors={[]}
                locale="en-US"
                tableUserSettings={{}}
                {...additionalProps}
            />
        </Provider>
    );

    beforeEach(() => {
        triggerEventListenerMock = jest.spyOn(eventHandlers, 'triggerFieldEvent').mockResolvedValue();
        testGlobals = testGlobalsGenerator();
        state = getMockState();
        state.screenDefinitions[testGlobals.screenId] = getMockPageDefinition(testGlobals.screenId);
        (
            state.screenDefinitions[testGlobals.screenId].metadata.uiComponentProperties[testGlobals.screenId] as any
        ).node = '@sage/xtrem-test/AnyNode';
        state.nodeTypes = testGlobals.nodeTypes;
        addFieldToState(
            FieldKey.Table,
            state,
            testGlobals.screenId,
            testGlobals.elementId,
            {} as any,
            testGlobals.tableValue,
        );
        state.browser.is.xs = true;
        const store: MockStoreEnhanced<XtremAppState> = getMockStore(state);
        testGlobals.store = store;
    });

    afterEach(() => {
        triggerEventListenerMock.mockReset();
        applyActionMocks();
    });

    describe('snapshots', () => {
        it('should render with default properties', async () => {
            const { container } = await renderTableComponent(
                testGlobals.screenId,
                testGlobals.tableProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            expect(container).toMatchSnapshot();
        });

        it('should render with the page size from properties', async () => {
            const pageSizeProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                pageSize: 3,
            };

            const { queryAllByTestId, container } = await renderTableComponent(
                testGlobals.screenId,
                pageSizeProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            expect(queryAllByTestId('e-card').length).toEqual(3);
            expect(container.querySelector('.e-mobile-table-body-empty')).toBeNull();
        });

        it('should render empty if no records provided', async () => {
            const pageSizeProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                pageSize: 3,
            };

            const { queryAllByTestId, container } = await renderTableComponent(
                testGlobals.screenId,
                pageSizeProperties,
                testGlobals.nodeTypes,
                undefined,
                undefined,
                '.e-mobile-table-body-empty',
            );

            expect(queryAllByTestId('e-card').length).toEqual(0);
            expect(container.querySelector('.e-mobile-table-body-empty')).not.toBeNull();
        });

        it('should render with the default page size', async () => {
            const pageSizeProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
            };

            const { queryAllByTestId } = await renderTableComponent(
                testGlobals.screenId,
                pageSizeProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            expect(queryAllByTestId('e-card').length).toEqual(6);
        });

        it('should render the title if it has a value and is not hidden', async () => {
            const titleProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                isTitleHidden: false,
                title: 'Test Title',
            };

            const { container } = await renderTableComponent(
                testGlobals.screenId,
                titleProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            expect(container.querySelector('.e-field-title')).not.toBeNull();
        });

        it('should not render the title if it is hidden', async () => {
            const titleProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                isTitleHidden: true,
                title: 'Test Title',
            };

            const { container } = await renderTableComponent(
                testGlobals.screenId,
                titleProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            expect(container.querySelector('.e-field-title')).toBeNull();
        });

        it('should not render the title if it is empty', async () => {
            const titleProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                isTitleHidden: false,
                title: undefined,
            };

            const { container } = await renderTableComponent(
                testGlobals.screenId,
                titleProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            expect(container.querySelector('.e-field-title')).toBeNull();
        });

        it('should render with mobile card', async () => {
            const mobileCardProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                mobileCard: {
                    title: testGlobals.nestedFields.numeric,
                    titleRight: testGlobals.nestedFields.reference,
                    line2: testGlobals.nestedFields.select,
                    line2Right: testGlobals.nestedFields.text,
                },
            };
            const { queryAllByTestId } = await renderTableComponent(
                testGlobals.screenId,
                mobileCardProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            const cards = queryAllByTestId('e-card');
            expect(cards.length).toEqual(6);
            const row1 = cards[0];
            expect(row1.querySelector('.e-card-row-line-title [data-testid="e-field-value"]')).toHaveTextContent('421');

            expect(
                row1.querySelector('.e-card-row-line-title .e-card-row-right [data-testid="e-field-value"]'),
            ).toHaveTextContent('How 0');
            expect(row1.querySelector('.e-card-row-line-2 [data-testid="e-field-value"]')).toHaveTextContent(
                'Select value 0',
            );
            expect(
                row1.querySelector('.e-card-row-line-2 .e-card-row-right [data-testid="e-field-value"]'),
            ).toHaveTextContent('Text value 0');

            const row2 = cards[1];
            expect(row2.querySelector('.e-card-row-line-title [data-testid="e-field-value"]')).toHaveTextContent('421');
            expect(
                row2.querySelector('.e-card-row-line-title .e-card-row-right [data-testid="e-field-value"]'),
            ).toHaveTextContent('How 1');
            expect(row2.querySelector('.e-card-row-line-2 [data-testid="e-field-value"]')).toHaveTextContent(
                'Select value 1',
            );
            expect(
                row2.querySelector('.e-card-row-line-2 .e-card-row-right [data-testid="e-field-value"]'),
            ).toHaveTextContent('Text value 1');
        });

        it('should render with `line2Right` even if `line2` is not defined', async () => {
            const mobileCardProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                mobileCard: {
                    title: testGlobals.nestedFields.numeric,
                    titleRight: testGlobals.nestedFields.reference,
                    line2Right: testGlobals.nestedFields.text,
                },
            };
            const { queryAllByTestId } = await renderTableComponent(
                testGlobals.screenId,
                mobileCardProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            const cards = queryAllByTestId('e-card');
            expect(cards.length).toEqual(6);
            const row1 = cards[0];

            expect(row1.querySelector('.e-card-row-line-2 [data-testid="e-field-value"]')).toHaveTextContent(
                'Text value 0',
            );
        });

        it('should render with `line3Right` even if `line3` is not defined', async () => {
            const mobileCardProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                mobileCard: {
                    title: testGlobals.nestedFields.numeric,
                    titleRight: testGlobals.nestedFields.reference,
                    line3Right: testGlobals.nestedFields.text,
                },
            };

            const { queryAllByTestId } = await renderTableComponent(
                testGlobals.screenId,
                mobileCardProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            const cards = queryAllByTestId('e-card');
            expect(cards.length).toEqual(6);
            const row1 = cards[0];

            expect(
                row1.querySelector('.e-card-row-line-3 .e-card-row-right [data-testid="e-field-value"]'),
            ).toHaveTextContent('Text value 0');
        });

        it('should skip hidden fields when rendering', async () => {
            const mobileCardProperties: InternalTableProperties = {
                ...testGlobals.tableProperties,
                hiddenColumns: ['progress', 'select'],
                columns: [
                    {
                        defaultUiProperties: {
                            ...CheckboxControlObject.defaultUiProperties,
                            bind: 'checkbox',
                        },
                        properties: {
                            bind: 'checkbox',
                            title: 'Checkbox',
                            isHidden() {
                                return true;
                            },
                        },
                        type: FieldKey.Checkbox,
                    },
                    testGlobals.nestedFields.numeric,
                    testGlobals.nestedFields.progress,
                    testGlobals.nestedFields.reference,
                    testGlobals.nestedFields.select,
                    testGlobals.nestedFields.text,
                ],
            };
            const { queryAllByTestId } = await renderTableComponent(
                testGlobals.screenId,
                mobileCardProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            const cards = queryAllByTestId('e-card');
            expect(cards.length).toEqual(6);
            const row1 = cards[0];

            expect(row1.querySelector('.e-card-title [data-testid="e-field-value"]')).toHaveTextContent('421');
            expect(
                row1.querySelector('.e-card-row-line-title .e-card-row-right [data-testid="e-field-value"]'),
            ).toHaveTextContent('How 0');
            expect(
                row1.querySelector('.e-card-row-line-2 .e-card-line-2 [data-testid="e-field-value"]'),
            ).toHaveTextContent('Text value 0');
            expect(row1.querySelector('.e-card-row-line-2 .e-card-row-right [data-testid="e-field-value"]')).toBeNull();

            const row2 = cards[1];
            expect(row2.querySelector('.e-card-title [data-testid="e-field-value"]')).toHaveTextContent('421');
            expect(
                row2.querySelector('.e-card-row-line-title .e-card-row-right [data-testid="e-field-value"]'),
            ).toHaveTextContent('How 1');
            expect(
                row2.querySelector('.e-card-row-line-2 .e-card-line-2 [data-testid="e-field-value"]'),
            ).toHaveTextContent('Text value 1');
            expect(row2.querySelector('.e-card-row-line-2 .e-card-row-right [data-testid="e-field-value"]')).toBeNull();
        });

        it('should skip hidden fields when rendering isHiddenMobile', async () => {
            const mobileCardProperties: InternalTableProperties = {
                ...testGlobals.tableProperties,
                hiddenColumns: ['progress', 'select'],
                columns: [
                    {
                        defaultUiProperties: {
                            ...CheckboxControlObject.defaultUiProperties,
                            bind: 'checkbox',
                        },
                        properties: {
                            bind: 'checkbox',
                            title: 'Checkbox',
                            isHidden() {
                                return true;
                            },
                        },
                        type: FieldKey.Checkbox,
                    },
                    {
                        defaultUiProperties: {
                            ...NumericControlObject.defaultUiProperties,
                            bind: 'numeric',
                        },
                        properties: {
                            bind: 'numeric',
                            title: 'Numeric',
                            isHiddenMobile: true,
                        },
                        type: FieldKey.Numeric,
                    },
                    testGlobals.nestedFields.progress,
                    testGlobals.nestedFields.reference,
                    testGlobals.nestedFields.select,
                    testGlobals.nestedFields.text,
                ],
            };
            state.browser.greaterThan.s = false;
            const { queryAllByTestId } = await renderTableComponent(
                testGlobals.screenId,
                mobileCardProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            const cards = queryAllByTestId('e-card');
            expect(cards.length).toEqual(6);
            const row1 = cards[0];
            expect(row1.querySelector('.e-card-title [data-testid="e-field-value"]')).toHaveTextContent('How 0');
            expect(
                row1.querySelector('.e-card-row-line-title .e-card-row-right [data-testid="e-field-value"]'),
            ).toHaveTextContent('Text value 0');
            expect(row1.querySelector('.e-table-field-mobile-card-subtitle [data-testid="e-field-value"]')).toBeNull();

            const row2 = cards[1];

            expect(row2.querySelector('.e-card-title [data-testid="e-field-value"]')).toHaveTextContent('How 1');
            expect(
                row2.querySelector('.e-card-row-line-title .e-card-row-right [data-testid="e-field-value"]'),
            ).toHaveTextContent('Text value 1');
            expect(row2.querySelector('.e-table-field-mobile-card-subtitle [data-testid="e-field-value"]')).toBeNull();
        });

        it('should NOT skip hidden fields when rendering isHiddenMobile but viewport is desktop', async () => {
            const mobileCardProperties: InternalTableProperties = {
                ...testGlobals.tableProperties,
                hiddenColumns: ['progress', 'select'],
                columns: [
                    {
                        defaultUiProperties: {
                            ...CheckboxControlObject.defaultUiProperties,
                            bind: 'checkbox',
                        },
                        properties: {
                            bind: 'checkbox',
                            title: 'Checkbox',
                            isHidden() {
                                return true;
                            },
                        },
                        type: FieldKey.Checkbox,
                    },
                    {
                        defaultUiProperties: {
                            ...NumericControlObject.defaultUiProperties,
                            bind: 'numeric',
                        },
                        properties: {
                            bind: 'numeric',
                            title: 'Numeric',
                            isHiddenMobile: true,
                        },
                        type: FieldKey.Numeric,
                    },
                    testGlobals.nestedFields.progress,
                    testGlobals.nestedFields.reference,
                    testGlobals.nestedFields.select,
                    testGlobals.nestedFields.text,
                ],
            };
            state.browser.greaterThan.s = true;
            const { queryAllByTestId } = await renderTableComponent(
                testGlobals.screenId,
                mobileCardProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );
            const cards = queryAllByTestId('e-card');
            expect(cards.length).toEqual(6);
            const row1 = cards[0];
            expect(row1.querySelector('.e-card-title [data-testid="e-field-value"]')).toHaveTextContent('421');
            expect(
                row1.querySelector('.e-card-row-line-title .e-card-row-right [data-testid="e-field-value"]'),
            ).toHaveTextContent('How 0');
            expect(
                row1.querySelector('.e-card-row-line-2 .e-card-line-2 [data-testid="e-field-value"]'),
            ).toHaveTextContent('Text value 0');
            expect(row1.querySelector('.e-card-row-line-2 .e-card-row-right [data-testid="e-field-value"]')).toBeNull();

            const row2 = cards[1];
            expect(row2.querySelector('.e-card-title [data-testid="e-field-value"]')).toHaveTextContent('421');
            expect(
                row2.querySelector('.e-card-row-line-title .e-card-row-right [data-testid="e-field-value"]'),
            ).toHaveTextContent('How 1');
            expect(
                row2.querySelector('.e-card-row-line-2 .e-card-line-2 [data-testid="e-field-value"]'),
            ).toHaveTextContent('Text value 1');
            expect(row2.querySelector('.e-card-row-line-2 .e-card-row-right [data-testid="e-field-value"]')).toBeNull();
        });

        it('should render with checkboxes', async () => {
            const canSelectProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                canSelect: true,
            };
            const { container } = await renderTableComponent(
                testGlobals.screenId,
                canSelectProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );
            expect(container).toMatchSnapshot();
        });

        it('should render with dropdown actions', async () => {
            const dropdownActionsProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                dropdownActions: [
                    {
                        title: 'Test action',
                        icon: 'home',
                        onClick: jest.fn(),
                    },
                ],
            };
            const { container } = await renderTableComponent(
                testGlobals.screenId,
                dropdownActionsProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );
            expect(container).toMatchSnapshot();
        });

        it('should render with load more button', async () => {
            const canSelectProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                isTransient: false,
            };

            const { container } = await renderTableComponent(
                testGlobals.screenId,
                canSelectProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );
            expect(container).toMatchSnapshot();
        });

        it('should render the filter icon if filters are enabled', async () => {
            jest.spyOn(FilterComponent, 'FiltersComponent').mockImplementation(
                (props: FilterComponent.FilterCommonProps) => (
                    <div
                        id="mockFiltersComponent"
                        onClick={() => {
                            props.handleSave(props.filters);
                        }}
                    />
                ),
            );

            const canFilterProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                canFilter: true,
            };

            const { container } = await renderTableComponent(
                testGlobals.screenId,
                canFilterProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            expect(container.querySelector('#mockFiltersComponent')).not.toBeNull();
        });

        it('should not render the filter icon if filters are disabled', async () => {
            jest.spyOn(FilterComponent, 'FiltersComponent').mockImplementation(
                (props: FilterComponent.FilterCommonProps) => (
                    <div
                        id="mockFiltersComponent"
                        onClick={() => {
                            props.handleSave(props.filters);
                        }}
                    />
                ),
            );
            const cannotFilterProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                canFilter: false,
            };
            const { container } = await renderTableComponent(
                testGlobals.screenId,
                cannotFilterProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );
            expect(container.querySelector('#mockFiltersComponent')).toBeNull();
        });

        it('should render with search box', async () => {
            const hasSearchBoxMobileProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                hasSearchBoxMobile: true,
            };
            const { container } = await renderTableComponent(
                testGlobals.screenId,
                hasSearchBoxMobileProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );
            expect(container).toMatchSnapshot();
        });

        it('should hide the header if the table do not have a search box, actions nor is filterable', async () => {
            const props: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                hasSearchBoxMobile: false,
                canFilter: false,
                fieldActions() {
                    return [];
                },
            };
            const { queryByTestId } = await renderTableComponent(
                testGlobals.screenId,
                props,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );
            expect(queryByTestId('e-field-header')).toBeNull();
        });

        describe('grouping', () => {
            it('should render items into groups based on a simple text field value', async () => {
                const props: TableDecoratorProperties = {
                    ...testGlobals.tableProperties,
                };

                const tableValue = new CollectionValue({
                    screenId: testGlobals.screenId,
                    elementId: testGlobals.elementId,
                    isTransient: true,
                    hasNextPage: false,
                    orderBy: [{ label: 1 }],
                    columnDefinitions: [testGlobals.tableProperties.columns!],
                    nodeTypes: testGlobals.nodeTypes,
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        {
                            _id: '1',
                            label: 'Label 1',
                            numeric: 421,
                            progress: 731,
                            reference__code: {
                                code: 'How 0',
                                description: 'come 0',
                            },
                            __cursor: '#1',
                        },
                        {
                            _id: '2',

                            label: 'Label 1',
                            numeric: 234,
                            progress: 543,
                            reference__code: {
                                code: 'How 0',
                                description: 'come 0',
                            },
                            __cursor: '#2',
                        },
                        {
                            _id: '3',
                            label: 'Label 2',
                            numeric: 421,
                            progress: 731,
                            reference__code: {
                                code: 'How 0',
                                description: 'come 0',
                            },
                            __cursor: '#3',
                        },
                        {
                            _id: '4',
                            label: 'Label 2',
                            numeric: 421,
                            progress: 731,
                            reference__code: {
                                code: 'How 0',
                                description: 'come 0',
                            },
                            __cursor: '#4',
                        },
                    ],
                    locale: 'en-US',
                });

                const { queryAllByTestId } = await renderTableComponent(
                    testGlobals.screenId,
                    props,
                    testGlobals.nodeTypes,
                    tableValue,
                    {
                        groupByField: 'label',
                    },
                );

                const separators = queryAllByTestId('e-mobile-table-separator', { exact: false });
                expect(separators.length).toEqual(2);
                expect(separators[0]).toHaveTextContent('Label 1');
                expect(separators[1]).toHaveTextContent('Label 2');
            });

            it('should render items into groups based on a reference field value field', async () => {
                const props: TableDecoratorProperties = {
                    ...testGlobals.tableProperties,
                };

                const tableValue = new CollectionValue({
                    screenId: testGlobals.screenId,
                    elementId: testGlobals.elementId,
                    isTransient: true,
                    hasNextPage: false,
                    orderBy: [{ reference: { code: 1 } }],
                    columnDefinitions: [testGlobals.tableProperties.columns!],
                    nodeTypes: testGlobals.nodeTypes,
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        {
                            _id: '1',
                            label: 'Label 1',
                            numeric: 421,
                            progress: 731,
                            reference__code: {
                                code: 'How 1',
                                description: 'come 0',
                            },
                            __cursor: '#1',
                        },
                        {
                            _id: '2',

                            label: 'Label 1',
                            numeric: 234,
                            progress: 543,
                            reference__code: {
                                code: 'How 0',
                                description: 'come 0',
                            },
                            __cursor: '#2',
                        },
                        {
                            _id: '3',
                            label: 'Label 2',
                            numeric: 421,
                            progress: 731,
                            reference__code: {
                                code: 'How 1',
                                description: 'come 0',
                            },
                            __cursor: '#3',
                        },
                        {
                            _id: '4',
                            label: 'Label 2',
                            numeric: 421,
                            progress: 731,
                            reference__code: {
                                code: 'How 0',
                                description: 'come 0',
                            },
                            __cursor: '#4',
                        },
                    ],
                    locale: 'en-US',
                });

                const { queryAllByTestId } = await renderTableComponent(
                    testGlobals.screenId,
                    props,
                    testGlobals.nodeTypes,
                    tableValue,
                    {
                        groupByField: 'reference',
                    },
                );

                const separators = queryAllByTestId('e-mobile-table-separator', { exact: false });
                expect(separators.length).toEqual(2);
                expect(separators[0]).toHaveTextContent('How 0');
                expect(separators[1]).toHaveTextContent('How 1');
            });
        });
    });

    describe('interactions', () => {
        it('should fetch for data when clicking the load more button', async () => {
            const hasSearchBoxMobileProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                mobileCard: {
                    title: testGlobals.nestedFields.progress,
                    titleRight: testGlobals.nestedFields.reference,
                    line2: testGlobals.nestedFields.select,
                    line2Right: testGlobals.nestedFields.text,
                },
                hasSearchBoxMobile: true,
                pageSize: 4,
                isTransient: true,
            };

            const getNextPageSpy = jest.spyOn(testGlobals.tableValue, 'getPageWithCurrentQueryArguments');

            expect(getNextPageSpy).not.toHaveBeenCalled();

            const { queryAllByTestId, queryByTestId } = await renderTableComponent(
                testGlobals.screenId,
                hasSearchBoxMobileProperties,
                testGlobals.nodeTypes,

                testGlobals.tableValue,
            );

            expect(queryAllByTestId('e-card').length).toEqual(4);
            expect(getNextPageSpy).not.toHaveBeenCalled();

            fireEvent.click(queryByTestId('e-load-more-button')!);

            await waitFor(() => {
                expect(getNextPageSpy).toHaveBeenCalledWith(
                    expect.objectContaining({
                        cursor: '#4',
                        pageNumber: 1,
                        pageSize: 4,
                    }),
                );
            });

            await waitFor(() => {
                expect(queryAllByTestId('e-card').length).toEqual(6);
            });
        });

        it('should call setFieldProperties when user selects a row', async () => {
            const canSelectProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                canSelect: true,
            };
            const fieldPropertiesMock = jest.fn();

            const { container } = await renderTableComponent(
                testGlobals.screenId,
                canSelectProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
                {
                    setFieldProperties: fieldPropertiesMock,
                },
            );

            expect(fieldPropertiesMock).not.toHaveBeenCalled();
            fireEvent.click(container.querySelector('.e-card-check input')!, { target: { checked: true } });

            expect(fieldPropertiesMock).toHaveBeenCalled();
            expect(fieldPropertiesMock).toHaveBeenCalledWith(
                testGlobals.elementId,
                expect.objectContaining({ selectedRecords: ['-1'] }),
            );
        });

        it('should call onRowClick when user clicks a row', async () => {
            const onRowClickMock = jest.fn().mockReturnValue(jest.fn());
            const { container } = await renderTableComponent(
                testGlobals.screenId,
                testGlobals.tableProperties,
                testGlobals.nodeTypes,

                testGlobals.tableValue,
                {
                    onRowClick: onRowClickMock,
                },
            );

            expect(onRowClickMock).not.toHaveBeenCalled();
            fireEvent.click(container.querySelector('.e-card')!);
            expect(onRowClickMock).toHaveBeenCalled();
            expect(onRowClickMock).toHaveBeenCalledWith('-1', undefined, expect.anything());
        });

        it('should not call onRowClick when user clicks a row but the field is disabled', async () => {
            const onRowClickMock = jest.fn().mockReturnValue(jest.fn());
            const props: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                isDisabled: true,
            };
            const { container } = await renderTableComponent(
                testGlobals.screenId,
                props,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
                {
                    onRowClick: onRowClickMock,
                },
            );

            expect(onRowClickMock).not.toHaveBeenCalled();
            fireEvent.click(container.querySelector('.e-card')!);
            expect(onRowClickMock).not.toHaveBeenCalled();
        });

        it('should not call onRowClick when user clicks a row but the field is rendered into a disabled container', async () => {
            const onRowClickMock = jest.fn().mockReturnValue(jest.fn());

            const { container } = await renderTableComponent(
                testGlobals.screenId,
                testGlobals.tableProperties,
                testGlobals.nodeTypes,

                testGlobals.tableValue,
                {
                    onRowClick: onRowClickMock,
                    isParentDisabled: true,
                },
            );

            expect(onRowClickMock).not.toHaveBeenCalled();
            fireEvent.click(container.querySelector('.e-card')!);
            expect(onRowClickMock).not.toHaveBeenCalled();
        });

        it('should not call onRowClick when user clicks the row selection checkbox', async () => {
            const onRowClick: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                canSelect: true,
            };
            const fieldPropertiesMock = jest.fn().mockReturnValue(jest.fn());

            const { container } = await renderTableComponent(
                testGlobals.screenId,
                onRowClick,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
                {
                    onRowClick: fieldPropertiesMock,
                },
            );

            expect(fieldPropertiesMock).not.toHaveBeenCalled();
            const checkbox = container.querySelector('.e-card-check input')!;
            fireEvent.click(checkbox);
            fireEvent.change(checkbox, { target: { checked: true } });
            expect(fieldPropertiesMock).not.toHaveBeenCalled();
        });

        it('should disable the checkboxes if the field is disabled', async () => {
            const onRowClick: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                canSelect: true,
                isDisabled: true,
            };

            const { container } = await renderTableComponent(
                testGlobals.screenId,
                onRowClick,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            expect(container.querySelector('.e-card-check input')).toBeDisabled();
        });

        it('should call setFieldProperties when user deselects a row', async () => {
            const canSelectProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                selectedRecords: ['-1', '-2'],
                canSelect: true,
            };
            const fieldPropertiesMock = jest.fn();

            const { container } = await renderTableComponent(
                testGlobals.screenId,
                canSelectProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
                {
                    setFieldProperties: fieldPropertiesMock,
                },
            );

            expect(fieldPropertiesMock).not.toHaveBeenCalled();
            fireEvent.click(container.querySelector('.e-card-check input')!, { target: { checked: true } });
            expect(fieldPropertiesMock).toHaveBeenCalled();
            expect(fieldPropertiesMock).toHaveBeenCalledWith(
                testGlobals.elementId,
                expect.objectContaining({ selectedRecords: ['-2'] }),
            );
        });

        it('should fetch for data when saving filters', async () => {
            jest.spyOn(FilterComponent, 'FiltersComponent').mockImplementation(
                (props: FilterComponent.FilterCommonProps) => (
                    <div
                        id="mockFiltersComponent"
                        onClick={() => {
                            props.handleSave(props.filters);
                        }}
                    />
                ),
            );

            const filterProperties: TableDecoratorProperties = {
                ...testGlobals.tableProperties,
                canFilter: true,
            };

            const { container } = await renderTableComponent(
                testGlobals.screenId,
                filterProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            fireEvent.click(container.querySelector('#mockFiltersComponent')!);
        });

        describe('event handlers', () => {
            let valueWithReference: CollectionValue;

            const nodeTypes: Dict<FormattedNodeDetails> = {
                AnyNode: {
                    title: 'AnyNode',
                    name: 'AnyNode',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        _id: { kind: GraphQLKind.Scalar, type: 'Id' },
                        reference: { kind: 'OBJECT', type: 'AnotherType' },
                    },
                    mutations: {},
                },
                AnotherType: {
                    title: 'AnotherType',
                    name: 'AnotherType',
                    packageName: '@sage/xtrem-test',
                    properties: {
                        _id: { kind: GraphQLKind.Scalar, type: 'Id' },
                        status: { kind: GraphQLKind.Scalar, type: 'String' },
                        title: { kind: GraphQLKind.Scalar, type: 'String' },
                    },
                    mutations: {},
                },
            };

            beforeEach(() => {
                valueWithReference = new CollectionValue({
                    screenId: testGlobals.screenId,
                    elementId: testGlobals.elementId,
                    isTransient: true,
                    hasNextPage: false,
                    orderBy: [{}],
                    columnDefinitions: [testGlobals.tableProperties.columns!],
                    nodeTypes,
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [
                        {
                            _id: '1234',
                            reference__title: {
                                _id: '3',
                                title: 'The title',
                            },
                            reference__status: {
                                _id: '3',
                                status: 'good',
                            },
                        },
                    ],
                    locale: 'en-US',
                    fieldType: CollectionFieldTypes.MOBILE_TABLE,
                });
            });

            it('should trigger the select event listener with reduced reference value', async () => {
                const onRowSelectedMock = jest.fn();
                const canSelectProperties: TableDecoratorProperties = {
                    ...testGlobals.tableProperties,
                    mobileCard: {
                        title: testGlobals.nestedFields.progress,
                        titleRight: testGlobals.nestedFields.reference,
                        line2: testGlobals.nestedFields.select,
                        line2Right: testGlobals.nestedFields.text,
                    },
                    pageSize: 4,
                    canSelect: true,
                    onRowSelected: onRowSelectedMock,
                    isTransient: true,
                };
                const store: MockStoreEnhanced<XtremAppState> = getMockStore(state);
                testGlobals.store = store;

                const { container } = await renderTableComponent(
                    testGlobals.screenId,
                    canSelectProperties,
                    nodeTypes,
                    valueWithReference,
                );

                expect(triggerEventListenerMock).not.toHaveBeenCalled();
                fireEvent.click(container.querySelector('.e-card-check input')!, { target: { value: 'checked' } });
                expect(triggerEventListenerMock).toHaveBeenCalledWith(
                    testGlobals.screenId,
                    testGlobals.elementId,
                    'onRowSelected',
                    '1234',
                    {
                        _id: '1234',
                        reference: {
                            _id: '3',
                            code: null,
                            status: 'good',
                            title: 'The title',
                        },
                    },
                );
            });

            it('should trigger the unselect event listener with reduced reference value', async () => {
                const onRowUnselectedMock = jest.fn();
                const canSelectProperties: TableDecoratorProperties = {
                    ...testGlobals.tableProperties,
                    mobileCard: {
                        title: testGlobals.nestedFields.progress,
                        titleRight: testGlobals.nestedFields.reference,
                        line2: testGlobals.nestedFields.select,
                        line2Right: testGlobals.nestedFields.text,
                    },
                    canSelect: true,
                    pageSize: 4,
                    onRowUnselected: onRowUnselectedMock,
                    isTransient: true,
                    selectedRecords: ['1234'],
                };
                const store: MockStoreEnhanced<XtremAppState> = getMockStore(state);
                testGlobals.store = store;

                const { container } = await renderTableComponent(
                    testGlobals.screenId,
                    canSelectProperties,
                    nodeTypes,
                    valueWithReference,
                );

                expect(triggerEventListenerMock).not.toHaveBeenCalled();

                fireEvent.click(container.querySelector('.e-card-check input')!, { target: { checked: false } });
                expect(triggerEventListenerMock).toHaveBeenCalledWith(
                    testGlobals.screenId,
                    testGlobals.elementId,
                    'onRowUnselected',
                    '1234',
                    {
                        _id: '1234',
                        reference: {
                            _id: '3',
                            code: null,
                            status: 'good',
                            title: 'The title',
                        },
                    },
                );
            });
        });

        describe('modifying displayed data', () => {
            let props: TableDecoratorProperties;
            beforeEach(() => {
                props = {
                    ...testGlobals.tableProperties,
                    pageSize: 4,
                    orderBy: { text: 1 } as any,
                    mobileCard: {
                        title: testGlobals.nestedFields.text,
                        titleRight: testGlobals.nestedFields.reference,
                        subtitle: testGlobals.nestedFields.select,
                        subtitleRight: testGlobals.nestedFields.progress,
                    },
                } as TableDecoratorProperties;
            });

            it('should update a displayed record', async () => {
                const fieldPropertiesMock = jest.fn();

                const { queryAllByTestId, container } = await renderTableComponent(
                    testGlobals.screenId,
                    props,
                    testGlobals.nodeTypes,
                    testGlobals.tableValue,
                    {
                        setFieldProperties: fieldPropertiesMock,
                    },
                );

                let cards = queryAllByTestId('e-card');
                expect(cards.length).toEqual(4);
                let node = container.querySelector('.e-card .e-card-title [data-testid="e-field-value"]')!;
                expect(node).toHaveTextContent('Text value 0');
                testGlobals.tableValue.setRecordValue({ recordData: { _id: '-1', text: 'updated value' } });
                cards = queryAllByTestId('e-card');
                expect(cards.length).toEqual(4);
                await waitFor(() => {
                    node = container.querySelector('.e-card .e-card-title [data-testid="e-field-value"]')!;
                    expect(node).toHaveTextContent('updated value');
                });
            });

            it('should do nothing if the updated record is not displayed', async () => {
                const fieldPropertiesMock = jest.fn();

                const { queryAllByTestId, container } = await renderTableComponent(
                    testGlobals.screenId,
                    props,
                    testGlobals.nodeTypes,
                    testGlobals.tableValue,
                    {
                        setFieldProperties: fieldPropertiesMock,
                    },
                );

                let cards = queryAllByTestId('e-card');
                expect(cards.length).toEqual(4);
                let node = container.querySelector('.e-card .e-card-title [data-testid="e-field-value"]')!;
                expect(node).toHaveTextContent('Text value 0');
                testGlobals.tableValue.setRecordValue({ recordData: { _id: '-5', text: 'updated value' } });
                cards = queryAllByTestId('e-card');
                expect(cards.length).toEqual(4);
                node = container.querySelector('.e-card .e-card-title [data-testid="e-field-value"]')!;
                expect(node).toHaveTextContent('Text value 0');
            });

            it('should fill in the removed record if it was displayed', async () => {
                const fieldPropertiesMock = jest.fn();

                const { queryAllByTestId, container } = await renderTableComponent(
                    testGlobals.screenId,
                    props,
                    testGlobals.nodeTypes,
                    testGlobals.tableValue,
                    {
                        setFieldProperties: fieldPropertiesMock,
                    },
                );

                const cards = queryAllByTestId('e-card');

                expect(cards.length).toEqual(4);
                const rows = container.querySelectorAll(
                    '.e-card .e-card-title .e-text-field [data-testid="e-field-value"]',
                );
                expect(rows[0]).toHaveTextContent('Text value 0');
                expect(rows[1]).toHaveTextContent('Text value 1');
                expect(rows[2]).toHaveTextContent('Text value 2');
                expect(rows[3]).toHaveTextContent('Text value 3');

                testGlobals.tableValue.removeRecord({ recordId: '-1' });

                await waitFor(() => {
                    const updatedRows = container.querySelectorAll(
                        '.e-card .e-card-title .e-text-field [data-testid="e-field-value"]',
                    );
                    expect(updatedRows[0]).toHaveTextContent('Text value 1');
                    expect(updatedRows[1]).toHaveTextContent('Text value 2');
                    expect(updatedRows[2]).toHaveTextContent('Text value 3');
                    expect(updatedRows[3]).toHaveTextContent('Text value 4');
                });
            });

            it('should not pull in a new item if there are no others left', async () => {
                props.pageSize = 10;
                const fieldPropertiesMock = jest.fn();

                const { queryAllByTestId, container } = await renderTableComponent(
                    testGlobals.screenId,
                    props,
                    testGlobals.nodeTypes,
                    testGlobals.tableValue,
                    {
                        setFieldProperties: fieldPropertiesMock,
                    },
                );

                const cards = queryAllByTestId('e-card');
                expect(cards.length).toEqual(6);
                const rows = container.querySelectorAll(
                    '.e-card .e-card-title .e-text-field [data-testid="e-field-value"]',
                );
                expect(rows[0]).toHaveTextContent('Text value 0');
                expect(rows[1]).toHaveTextContent('Text value 1');
                expect(rows[2]).toHaveTextContent('Text value 2');
                expect(rows[3]).toHaveTextContent('Text value 3');
                expect(rows[4]).toHaveTextContent('Text value 4');
                expect(rows[5]).toHaveTextContent('Text value 5');

                testGlobals.tableValue.removeRecord({ recordId: '-2' });

                await waitFor(() => {
                    expect(queryAllByTestId('e-card').length).toEqual(5);
                    const updatedRows = container.querySelectorAll(
                        '.e-card .e-card-title .e-text-field [data-testid="e-field-value"]',
                    );
                    expect(updatedRows[0]).toHaveTextContent('Text value 0');
                    expect(updatedRows[1]).toHaveTextContent('Text value 2');
                    expect(updatedRows[2]).toHaveTextContent('Text value 3');
                    expect(updatedRows[3]).toHaveTextContent('Text value 4');
                    expect(updatedRows[4]).toHaveTextContent('Text value 5');
                });
            });

            it('should do nothing if the removed record is outside of the scope of the currently displayed page', async () => {
                const fieldPropertiesMock = jest.fn();

                const { queryAllByTestId, container } = await renderTableComponent(
                    testGlobals.screenId,
                    props,
                    testGlobals.nodeTypes,
                    testGlobals.tableValue,
                    {
                        setFieldProperties: fieldPropertiesMock,
                    },
                );

                expect(queryAllByTestId('e-card').length).toEqual(4);
                const rows = container.querySelectorAll(
                    '.e-card .e-card-title .e-text-field [data-testid="e-field-value"]',
                );
                expect(rows[0]).toHaveTextContent('Text value 0');
                expect(rows[1]).toHaveTextContent('Text value 1');
                expect(rows[2]).toHaveTextContent('Text value 2');
                expect(rows[3]).toHaveTextContent('Text value 3');

                testGlobals.tableValue.removeRecord({ recordId: '-5' });

                await waitFor(() => {
                    expect(queryAllByTestId('e-card').length).toEqual(4);
                    const updatedRows = container.querySelectorAll(
                        '.e-card .e-card-title .e-text-field [data-testid="e-field-value"]',
                    );
                    expect(updatedRows[0]).toHaveTextContent('Text value 0');
                    expect(updatedRows[1]).toHaveTextContent('Text value 1');
                    expect(updatedRows[2]).toHaveTextContent('Text value 2');
                    expect(updatedRows[3]).toHaveTextContent('Text value 3');
                });
            });

            it('should add new record in the order of sorting', async () => {
                const fieldPropertiesMock = jest.fn();

                const { queryAllByTestId, container } = await renderTableComponent(
                    testGlobals.screenId,
                    props,
                    testGlobals.nodeTypes,
                    testGlobals.tableValue,
                    {
                        setFieldProperties: fieldPropertiesMock,
                    },
                );

                expect(queryAllByTestId('e-card').length).toEqual(4);
                const rows = container.querySelectorAll(
                    '.e-card .e-card-title .e-text-field [data-testid="e-field-value"]',
                );
                expect(rows[0]).toHaveTextContent('Text value 0');
                expect(rows[1]).toHaveTextContent('Text value 1');
                expect(rows[2]).toHaveTextContent('Text value 2');
                expect(rows[3]).toHaveTextContent('Text value 3');

                testGlobals.tableValue.addRecord({
                    recordData: {
                        checkbox: false,
                        date: '2019-11-15',

                        label: 'Label value',
                        link: 'Link value',
                        numeric: 425,
                        progress: 735,
                        reference: {
                            code: 'How',
                            description: 'come',
                        },
                        select: 'Select value',
                        text: 'Text value 1.5',
                    },
                });

                await waitFor(() => {
                    expect(queryAllByTestId('e-card').length).toEqual(4);
                    const updatedRows = container.querySelectorAll(
                        '.e-card .e-card-title .e-text-field [data-testid="e-field-value"]',
                    );
                    expect(updatedRows[0]).toHaveTextContent('Text value 0');
                    expect(updatedRows[1]).toHaveTextContent('Text value 1');
                    expect(updatedRows[2]).toHaveTextContent('Text value 1.5');
                    expect(updatedRows[3]).toHaveTextContent('Text value 2');
                });
            });

            it('Add button should appear if addNewRecord is true and sidebar is defined', async () => {
                const fieldPropertiesMock = jest.fn();
                props.canAddNewLine = true;
                props.sidebar = {
                    title: 'Sidebar title',
                    layout() {
                        return {
                            mainSection: {
                                title: 'Main section',
                                blocks: {
                                    mainBlock: {
                                        title: 'Some block title',
                                        fields: ['_id', 'tax', 'provider', 'category', 'qty', 'amount', 'description'],
                                    },
                                },
                            },
                        };
                    },
                };
                const { queryByTestId } = await renderTableComponent(
                    testGlobals.screenId,
                    props,
                    testGlobals.nodeTypes,
                    testGlobals.tableValue,
                    {
                        setFieldProperties: fieldPropertiesMock,
                    },
                );
                expect(queryByTestId('e-add-line-button')).not.toBeNull();
            });
        });
    });
});
