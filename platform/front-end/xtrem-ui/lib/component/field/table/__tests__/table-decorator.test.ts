import { fail } from 'assert';
import * as nestedFields from '../../../nested-fields';
import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata } from '../../../../__tests__/test-helpers';
import type { NestedField } from '../../../nested-fields';
import {
    aggregate,
    checkbox,
    count,
    date,
    filterSelect,
    icon,
    image,
    label,
    link,
    numeric,
    progress,
    reference,
    select,
    switch as nestedSwitch,
    technical,
    text,
} from '../../../nested-fields';
import type { TableDecoratorProperties, TableProperties } from '../table-component-types';
import { tableField } from '../table-decorator';

describe('Table Decorator', () => {
    const fieldId = 'tableField';
    let pageMetadata: pageMetaData.PageMetadata;

    describe('mainField', () => {
        const tableDecoratorProperties: TableDecoratorProperties<any, any> = {
            columns: [
                aggregate({ aggregateOn: 'aggregate', bind: 'aggregate', aggregationMethod: 'max' }),
                checkbox({ bind: 'checkbox' }),
                count({ bind: 'count' }),
                date({ bind: 'date' }),
                filterSelect({
                    bind: 'filterSelect',
                    node: '@sage/xtrem-show-case/ShowCaseProduct',
                    valueField: 'value1',
                }),
                icon({ bind: 'icon' }),
                image({ bind: 'image' }),
                label({ bind: 'label' }),
                link({ bind: 'link' }),
                numeric({ bind: 'numeric' }),
                progress({ bind: 'progress' }),
                reference({ bind: 'reference', node: '@sage/xtrem-show-case/ShowCaseProvider', valueField: 'value2' }),
                select({ bind: 'select' }),
                nestedSwitch({ bind: 'nestedSwitch' }),
                technical({ bind: '_id' }),
                text({ bind: 'text' }),
            ],
        };

        it('should be able to set the "mainField" property', () => {
            tableField({ ...tableDecoratorProperties, mainField: 'text' });
        });

        it('should not be able to set the "mainField" to an invalid property', () => {
            expect(() => tableField({ ...tableDecoratorProperties, mainField: 'aggregate' })).toThrow(
                '"aggregate" cannot be used as "mainField". Please use one of the following values: "checkbox", "count", "date", "label", "link", "numeric", "progress", "select", "text".',
            );
        });

        it('should not be able to set the "mainField" to a non-existing column', () => {
            expect(() =>
                tableField({ columns: tableDecoratorProperties.columns?.slice(0, -1), mainField: 'text' }),
            ).toThrow(
                '"text" cannot be used as "mainField". Please use one of the following values: "checkbox", "count", "date", "label", "link", "numeric", "progress", "select" or add a new column bound to "text".',
            );
        });
    });

    describe('mapping values', () => {
        let mappedComponentProperties: TableDecoratorProperties<ScreenBase>;
        beforeEach(() => {
            pageMetadata = getMockPageMetadata();
            jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
            tableField({ columns: [] })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            mappedComponentProperties = pageMetadata.uiComponentProperties[fieldId] as TableProperties<ScreenBase>;
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('TableProperties -> should set default values when no component properties provided', () => {
            expect(mappedComponentProperties.bind).toBeUndefined();
            expect(mappedComponentProperties.canFilter).toBe(true);
            expect(mappedComponentProperties.canResizeColumns).toBe(false);
            expect(mappedComponentProperties.canSelect).toBe(true);
            expect(mappedComponentProperties.canUserHideColumns).toBe(true);
            expect(mappedComponentProperties.columns).toEqual([]);
            expect(mappedComponentProperties.fetchesDefaults).toBe(false);
            expect(mappedComponentProperties.helperText).toBeUndefined();
            expect(mappedComponentProperties.isFullWidth).toBe(false);
            expect(mappedComponentProperties.isHidden).toBe(false);
            expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
            expect(mappedComponentProperties.isHiddenMobile).toBe(false);
            expect(mappedComponentProperties.isTransient).toBe(false);
            expect(mappedComponentProperties.onChange).toBeUndefined();
            expect(mappedComponentProperties.onClick).toBeUndefined();
            expect(mappedComponentProperties.onRowSelected).toBeUndefined();
            expect(mappedComponentProperties.onRowUnselected).toBeUndefined();
            expect(mappedComponentProperties.orderBy).toBeUndefined();
            expect(mappedComponentProperties.parent).toBeUndefined();
            expect(mappedComponentProperties.selectedRecords).toBeUndefined();
            expect(mappedComponentProperties.title).toBeUndefined();
            expect(mappedComponentProperties._controlObjectType).toEqual('Table');
        });

        describe('mapping mandatory props for nested fields', () => {
            const keys = [
                'Aggregate',
                'Checkbox',
                'Count',
                'Date',
                'FilterSelect',
                'Icon',
                'Image',
                'Label',
                'Link',
                'MultiReference',
                'Numeric',
                'Progress',
                'Reference',
                'Select',
                'Switch',
                'Text',
            ];

            const mandatoryProps = {
                Aggregate: { bind: 'Aggregate' },
                Checkbox: { bind: 'Checkbox' },
                Count: { bind: 'Count' },
                Date: { bind: 'Date' },
                FilterSelect: { bind: 'FilterSelect' },
                Icon: { bind: 'Icon' },
                Image: { bind: 'Image' },
                Label: { bind: 'Label' },
                Link: { bind: 'Link', page: 'Test page' },
                MultiReference: { bind: 'MultiReference' },
                Numeric: { bind: 'Numeric' },
                Progress: { bind: 'Progress' },
                Reference: { bind: 'Reference' },
                Select: { bind: 'Select' },
                Switch: { bind: 'Switch' },
                Text: { bind: 'Text' },
            };

            const columns: NestedField<Page, nestedFields.GridNestedFieldTypes>[] = keys.map(key => {
                const lowerCaseKey = key[0].toLowerCase() + key.slice(1);
                return nestedFields[lowerCaseKey]({ ...mandatoryProps[key] });
            });

            it('should set values when component properties provided', () => {
                expect(columns.length).toEqual(keys.length);
                columns.forEach(column => {
                    if (column.properties) {
                        expect(keys).toContain(column.properties.bind);
                    } else {
                        fail('Test failed: no properties in Column object');
                    }
                });
            });
        });
    });
});
