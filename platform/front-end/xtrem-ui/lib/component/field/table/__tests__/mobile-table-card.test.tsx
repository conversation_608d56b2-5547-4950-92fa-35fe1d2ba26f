import {
    addFieldToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';

jest.mock('../../text/async-text-component');
jest.mock('../../text-area/async-text-area-component');
jest.mock('../../reference/async-reference-component');
jest.mock('../../label/async-label-component');
jest.mock('../../numeric/async-numeric-component');
jest.mock('../../date/async-date-component');
jest.mock('../../image/async-image-component');

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux';
import type { ScreenBase } from '../../../../service/screen-base';
import type { Dict } from '@sage/xtrem-shared';
import type { NestedField } from '../../../nested-fields';
import {
    ImageControlObject,
    TextControlObject,
    NumericControlObject,
    DateControlObject,
} from '../../../control-objects';
import { MobileTableComponent } from '../mobile-table-component';
import { CollectionValue } from '../../../../service/collection-data-service';
import type { TableDecoratorProperties, TableInternalComponentProps } from '../table-component-types';
import { FieldKey } from '../../../types';
import { render, waitFor } from '@testing-library/react';
import type { FormattedNodeDetails } from '../../../../service/metadata-types';
import { GraphQLKind } from '../../../../types';
import '@testing-library/jest-dom';

const testGlobalsGenerator = () => {
    const screenId = 'TestPage';
    const elementId = 'TestField';
    const imageSrc =
        'iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==';
    const nodeTypes: Dict<FormattedNodeDetails> = {
        AnyNode: {
            name: 'AnyNode',
            title: 'AnyNode',
            packageName: '@sage/xtrem-test',
            properties: {
                _id: { kind: GraphQLKind.Scalar, type: 'Id' },
                reference: { kind: 'OBJECT', type: 'AnotherType' },
                checkbox: { kind: GraphQLKind.Scalar, type: 'Boolean' },
                date: { kind: GraphQLKind.Scalar, type: 'Date' },
                label: { kind: GraphQLKind.Scalar, type: 'String' },
                select: { kind: GraphQLKind.Scalar, type: 'String' },
                text: { kind: GraphQLKind.Scalar, type: 'String' },
                link: { kind: GraphQLKind.Scalar, type: 'String' },
                numeric: { kind: GraphQLKind.Scalar, type: 'Float' },
                progress: { kind: GraphQLKind.Scalar, type: 'Int' },
            },
            mutations: {},
        },
        AnotherType: {
            name: 'AnotherType',
            title: 'AnotherType',
            packageName: '@sage/xtrem-test',
            properties: {
                _id: { kind: GraphQLKind.Scalar, type: 'Id' },
                status: { kind: GraphQLKind.Scalar, type: 'String' },
                title: { kind: GraphQLKind.Scalar, type: 'String' },
                code: { kind: GraphQLKind.Scalar, type: 'String' },
            },
            mutations: {},
        },
    };
    const nestedFields: Dict<NestedField<ScreenBase, any>> = {
        id: {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: 'id',
            },
            properties: {
                bind: 'id',
                title: 'Id',
            },
            type: FieldKey.Text,
        },
        title: {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: 'title',
            },
            properties: {
                bind: 'title',
                title: 'Title',
            },
            type: FieldKey.Text,
        },
        image: {
            defaultUiProperties: {
                ...ImageControlObject.defaultUiProperties,
                bind: 'image',
            },
            properties: {
                bind: 'image',
                title: 'Image',
            },
            type: FieldKey.Image,
        },
        date: {
            defaultUiProperties: {
                ...DateControlObject.defaultUiProperties,
                bind: 'date',
            },
            properties: {
                bind: 'date',
                title: 'Date',
            },
            type: FieldKey.Date,
        },
        price: {
            defaultUiProperties: {
                ...NumericControlObject.defaultUiProperties,
                bind: 'price',
            },
            properties: {
                bind: 'price',
                title: 'Price',
            },
            type: FieldKey.Numeric,
        },
    };

    const tableProperties: TableDecoratorProperties = {
        canSelect: false,
        columns: [nestedFields.id, nestedFields.title, nestedFields.image, nestedFields.date, nestedFields.price],
        node: '@sage/xtrem-test/AnyNode',
        mobileCard: {
            title: nestedFields.title,
            titleRight: nestedFields.id,
            line2: nestedFields.price,
            line2Right: nestedFields.date,
            image: nestedFields.image,
        },
    };

    const tableValue = new CollectionValue({
        screenId,
        elementId,
        isTransient: true,
        hasNextPage: false,
        orderBy: [{ id: 1 }],
        columnDefinitions: [tableProperties.columns!],
        nodeTypes,
        nodes: ['@sage/xtrem-test/TestNode'],
        filter: [undefined],
        locale: 'en-US',
        initialValues: [
            {
                id: '1',
                title: 'Title #1',
                image: { value: imageSrc },
                date: '2021-01-01',
                price: 99.99,
            },
            {
                id: '2',
                title: 'Title #1',
                image: { value: undefined },
                date: '2021-01-02',
                price: 99.99,
            },
            {
                id: '3',
                title: undefined,
                image: { value: undefined },
                date: '2021-01-03',
                price: 99.99,
            },
        ],
    });

    return {
        screenId,
        elementId,
        imageSrc,
        nestedFields,
        tableProperties,
        tableValue,
        nodeTypes,
    };
};

describe('Mobile Table with Table Card View', () => {
    let testGlobals: ReturnType<typeof testGlobalsGenerator> & { store?: MockStoreEnhanced<XtremAppState> };
    let state: XtremAppState;

    const renderTableComponent = async (
        screenId: string,
        fieldProperties: TableDecoratorProperties,
        nodeTypes: Dict<FormattedNodeDetails>,
        value: CollectionValue,
        additionalProps: Partial<TableInternalComponentProps> = {},
    ) => {
        const result = render(getMobileTableComponent(screenId, fieldProperties, nodeTypes, value, additionalProps));
        await waitFor(() => {
            expect(result.container.querySelector('.e-card')).not.toBeNull();
        });
        return result;
    };

    const getMobileTableComponent = (
        screenId: string,
        fieldProperties: TableDecoratorProperties,
        nodeTypes: Dict<FormattedNodeDetails>,
        value?: CollectionValue,
        additionalProps: Partial<TableInternalComponentProps> = {},
    ) => (
        <Provider store={testGlobals.store!}>
            <MobileTableComponent
                elementId={testGlobals.elementId}
                fieldProperties={fieldProperties}
                enumTypes={{}}
                dataTypes={{}}
                nodeTypes={nodeTypes}
                screenId={screenId}
                value={value as any}
                setFieldProperties={jest.fn()}
                {...additionalProps}
                accessBindings={{}}
                validationErrors={[]}
                locale="en-US"
                tableUserSettings={{}}
            />
        </Provider>
    );

    beforeEach(() => {
        testGlobals = testGlobalsGenerator();
        state = getMockState();
        state.screenDefinitions[testGlobals.screenId] = getMockPageDefinition(testGlobals.screenId);
        addFieldToState(
            FieldKey.Table,
            state,
            testGlobals.screenId,
            testGlobals.elementId,
            {} as any,
            testGlobals.tableValue,
        );
        const store: MockStoreEnhanced<XtremAppState> = getMockStore(state);
        testGlobals.store = store;
    });

    afterEach(() => {
        applyActionMocks();
    });

    describe('Snapshots', () => {
        it('should render with default properties', async () => {
            const { queryAllByTestId } = await renderTableComponent(
                testGlobals.screenId,
                testGlobals.tableProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            const rows = queryAllByTestId('e-card');
            expect(rows).toMatchSnapshot();
            expect(rows.length).toEqual(3);
        });

        it('should render image given truthy image value with title alt message', async () => {
            const { queryAllByTestId } = await renderTableComponent(
                testGlobals.screenId,
                testGlobals.tableProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );

            const rows = queryAllByTestId('e-card');

            const element = rows[0].querySelector(`img[src="data:image;base64,${testGlobals.imageSrc}"]`);
            expect(element).toHaveAttribute('alt', 'Image');
        });

        it('should render image given truthy image value with placeholder alt message', async () => {
            testGlobals.tableProperties.mobileCard!.image!.properties.title = undefined;
            const { queryAllByTestId } = await renderTableComponent(
                testGlobals.screenId,
                testGlobals.tableProperties,
                testGlobals.nodeTypes,

                testGlobals.tableValue,
            );

            const rows = queryAllByTestId('e-card');

            const element = rows[0].querySelector(`img[src="data:image;base64,${testGlobals.imageSrc}"]`);
            expect(element).toHaveAttribute('alt', 'Title #1');
        });

        it('should render initials given falsy image value and truthy title', async () => {
            const { queryAllByTestId } = await renderTableComponent(
                testGlobals.screenId,
                testGlobals.tableProperties,
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );
            const rows = queryAllByTestId('e-card');
            expect(rows.length).toEqual(3);

            expect(rows[1].querySelector('.e-image-field-placeholder')).not.toBeNull();
            expect(rows[1].querySelector('.e-image-field-placeholder div[data-element="initials"]')).not.toBeNull();
            // TODO: Replace selector with span[data-element="image"] once https://github.com/Sage/carbon/pull/3892 is
            //       merged by Carbon and updated in Xtrem.

            expect(rows[1].querySelector('.e-image-field-placeholder span[data-element="individual"]')).toBeNull();
        });

        it('should render with inline actions', async () => {
            const { queryAllByTestId } = await renderTableComponent(
                testGlobals.screenId,
                {
                    ...testGlobals.tableProperties,
                    inlineActions: [{ title: 'Edit', onClick: jest.fn(), icon: 'bank' }],
                },
                testGlobals.nodeTypes,
                testGlobals.tableValue,
            );
            const actions = queryAllByTestId('e-table-inline-action', { exact: false });
            expect(actions.length).toEqual(3);

            expect(actions[1]).toHaveAccessibleName('Edit');
        });
    });
});
