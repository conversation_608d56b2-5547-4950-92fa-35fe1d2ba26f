import { applyActionMocks, renderWithRedux, userEvent } from '../../../../__tests__/test-helpers';

jest.mock('../async-desktop-table-component', () => ({
    AsyncDesktopTableComponent: require('../desktop-table-component').ConnectedDesktopTableComponent,
}));

jest.mock('../../../../utils/feature-toggles');
const featureTogglesMock = require('../../../../utils/feature-toggles');

// Enable client customizations in dev mode:
featureTogglesMock.isUserClientCustomizationEnabled.mockImplementation(() => true);

import { cleanup, fireEvent, waitFor } from '@testing-library/react';
import { set } from 'lodash';
import * as React from 'react';
import { CollectionValue } from '../../../../service/collection-data-service';
import * as collectionDataUtils from '../../../../service/collection-data-utils';
import * as graphqlService from '../../../../service/graphql-service';
import type { PageArticleItem } from '../../../../service/layout-types';
import * as tableExportService from '../../../../service/table-export-service';
import * as events from '../../../../utils/events';
import * as toastService from '../../../../service/toast-service';
import {
    CheckboxControlObject,
    DateControlObject,
    FilterSelectControlObject,
    ImageControlObject,
    LinkControlObject,
    NumericControlObject,
    type PageActionControlObject,
    ProgressControlObject,
    ReferenceControlObject,
    SelectControlObject,
    TextControlObject,
} from '../../../control-objects';
import { ConnectedTableComponent } from '../table-component';
import { destroyScreenCollections } from '../../../../service/loki';
import { xtremConsole } from '../../../../utils/console';
import type { FetchReferenceFieldSuggestionsInput } from '../../../../service/graphql-service';
import type { AccessBindings } from '../../../../service/page-definition';
import type { ClientNode } from '@sage/xtrem-client';
import type { NestedField, GridNestedFieldTypes } from '../../../nested-fields';
import type { FieldDecoratorProps, FieldInternalValue } from '../../../types';
import { AgGridHelpers } from '../../../../__tests__/test-helpers/table-helpers';
import { FieldKey, GraphQLTypes } from '../../../types';
import { GraphQLKind } from '../../../../types';
import '@testing-library/jest-dom';
import { navigationPanelId } from '../../../container/navigation-panel/navigation-panel-types';

const { CollectionValue: ActualCollectionValue } = jest.requireActual('../../../../service/collection-data-service');

jest.setTimeout(10000);

/**
 * It is IMPORTANT to declare this mock at the very beginning of the file.
 * Jest requires the following variable to start with the 'mock' prefix, hence 'mockCollectionValue' is used.
 * The following allows us to mock/spy only a single method of a the 'CollectionValue' class (i.e. 'setCellValue').
 * We should avoid calling 'jest.restoreAllMocks()' as this mock must not be restored.
 */
let setCellValueMock: jest.MockInstance<any, any> | null = null;
const setCellValue: (i: any) => jest.MockInstance<any, any> = implementation => {
    setCellValueMock = jest.fn();
    return setCellValueMock.mockImplementation(implementation);
};

jest.mock('../../../../service/collection-data-service', () => {
    return {
        CollectionValue: jest.fn().mockImplementation((...args) => {
            const actual = new ActualCollectionValue(...args);
            const methods = Object.getOwnPropertyNames(Object.getPrototypeOf(actual));
            const originalMethods: any = methods.reduce((prev, curr) => {
                return { ...prev, [curr]: actual[curr].bind(actual) };
            }, {});
            return { ...originalMethods, setCellValue: setCellValue(originalMethods.setCellValue) };
        }),
        // https://github.com/kulshekhar/ts-jest/issues/281
        RecordActionType: {
            MODIFIED: 'update',
            ADDED: 'create',
            REMOVED: 'delete',
        },
        CollectionFieldTypes: {
            DESKTOP_TABLE: 'desktop table',
            MOBILE_TABLE: 'mobile table',
            NESTED_GRID: 'nested grid',
            TREE: 'tree',
            LOOKUP_DIALOG: 'lookup dialog',
            GRID_ROW: 'grid row',
            POD: 'pod',
        },
    };
});

const screenId = 'TestPage';
const tableElementId = 'tableElementId';

type Node = {
    _id: string;
    column1: string;
    column2: number;
    column3: {
        _id: string;
        code: string;
        description: string;
    };
    column4: 'option1' | 'option2';
    column5: string;
    column6: string;
    column7: number;
    column8: string;
    column9: boolean;
    column10: {
        _id: string;
        code: string;
        description: string;
    };
};
type AddressNode = {
    _id: string;
    line1: string;
    line2: string;
    country: {
        _id: string;
        code: string;
        name: string;
    };
};

const defaultFieldProperties = (canFilter: boolean = true): FieldDecoratorProps<FieldKey.Table, any, Node> => ({
    title: 'Table Test Title',
    pageSize: 20,
    node: '@sage/xtrem-test/SampleNode',
    isDisabled: false,
    canFilter,
    canAddNewLine: false,
    canUserHideColumns: true,
    columns: [
        {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: 'column1',
            },
            properties: {
                bind: 'column1',
                title: 'Column 1',
            },
            type: FieldKey.Text,
        },
        {
            defaultUiProperties: {
                ...NumericControlObject.defaultUiProperties,
                bind: 'column2',
            },
            properties: {
                bind: 'column2',
                title: 'Column 2',
            },
            type: FieldKey.Numeric,
        },
        {
            defaultUiProperties: {
                ...ReferenceControlObject.defaultUiProperties,
                bind: 'column1',
            },
            properties: {
                bind: 'column3',
                title: 'Column 3',
                node: '@sage/xtrem-show-case/Reference',
                valueField: 'code',
                helperTextField: 'description',
                minLookupCharacters: 0,
            },
            type: FieldKey.Reference,
        },
        {
            defaultUiProperties: {
                ...SelectControlObject.defaultUiProperties,
                bind: 'column4',
            },
            properties: {
                bind: 'column4',
                title: 'Column 4',
                optionType: '@sage/xtrem-show-case/Sample',
                options: ['option1', 'option2'],
            },
            type: FieldKey.Select,
        },
        {
            defaultUiProperties: {
                ...LinkControlObject.defaultUiProperties,
                bind: 'column5',
            },
            properties: {
                bind: 'column5',
                title: 'Column 5',
                map(fieldValue) {
                    if (fieldValue) {
                        return `http://${(fieldValue as string).replace(/-|_|\s/g, '').toLowerCase()}.sage.com`;
                    }
                    return '';
                },
            },
            type: FieldKey.Link,
        },
        {
            defaultUiProperties: {
                ...DateControlObject.defaultUiProperties,
                bind: 'column6',
            },
            properties: {
                bind: 'column6',
                title: 'Column 6',
            },
            type: FieldKey.Date,
        },
        {
            defaultUiProperties: {
                ...ProgressControlObject.defaultUiProperties,
                bind: 'column7',
            },
            properties: {
                bind: 'column7',
                title: 'Column 7',
            },
            type: FieldKey.Progress,
        },
        {
            defaultUiProperties: {
                ...ImageControlObject.defaultUiProperties,
                bind: 'column8',
            },
            properties: {
                bind: 'column8',
                title: 'Column 8',
            },
            type: FieldKey.Image,
        },
        {
            defaultUiProperties: {
                ...CheckboxControlObject.defaultUiProperties,
                bind: 'column9',
            },
            properties: {
                bind: 'column9',
                title: 'Column 9',
            },
            type: FieldKey.Checkbox,
        },
        {
            defaultUiProperties: {
                ...FilterSelectControlObject.defaultUiProperties,
                bind: 'column1',
            },
            properties: {
                bind: 'column10',
                title: 'Column 10',
                node: '@sage/xtrem-show-case/FilterSelect',
                valueField: 'code',
                helperTextField: 'description',
                minLookupCharacters: 0,
            },
            type: FieldKey.FilterSelect,
        },
    ],
});

const deepNestedColumnsFieldProperties = (
    canFilter: boolean = true,
): FieldDecoratorProps<FieldKey.Table, any, AddressNode> => ({
    title: 'Table with deep nested columns',
    pageSize: 20,
    node: '@sage/xtrem-test/Address',
    isDisabled: false,
    canFilter,
    canAddNewLine: false,
    columns: [
        {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: 'line1',
            },
            properties: {
                bind: 'line1',
                title: 'Line 1',
                canFilter: true,
            },
            type: FieldKey.Text,
        },
        {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: 'line2',
            },
            properties: {
                bind: 'line2',
                title: 'Line 2',
                canFilter: true,
            },
            type: FieldKey.Text,
        },
        {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: { country: { code: true } },
            },
            properties: {
                bind: { country: { code: true } },
                title: 'Country code',
                canFilter: true,
            },
            type: FieldKey.Text,
        },
        {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: { country: { name: true } },
            },
            properties: {
                bind: { country: { name: true } },
                title: 'Country name',
                canFilter: true,
            },
            type: FieldKey.Text,
        },
    ],
});

const disabledFieldProperties = (): FieldDecoratorProps<FieldKey.Table> => ({
    title: 'Table Test Title',
    pageSize: 20,
    node: '@sage/xtrem-test/Sample',
    isDisabled: true,
    columns: [
        {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: 'column1',
            },
            properties: {
                bind: 'column1',
                canFilter: true,
                title: 'Column 1',
            },
            type: FieldKey.Text,
        },
        {
            defaultUiProperties: {
                ...NumericControlObject.defaultUiProperties,
                bind: 'column2',
            },
            properties: {
                bind: 'column2',
                title: 'Column 2',
            },
            type: FieldKey.Numeric,
        },
        {
            defaultUiProperties: {
                ...ReferenceControlObject.defaultUiProperties,
                bind: 'column3',
            },
            properties: {
                bind: 'column3',
                title: 'Column 3',
                node: '@sage/xtrem-show-case/Reference',
                valueField: 'code',
                helperTextField: 'description',
                minLookupCharacters: 0,
            },
            type: FieldKey.Reference,
        },
        {
            defaultUiProperties: {
                ...SelectControlObject.defaultUiProperties,
                bind: 'column4',
            },
            properties: {
                bind: 'column4',
                title: 'Column 4',
                optionType: '@sage/xtrem-show-case/Sample',
                options: ['option1', 'option2'],
            },
            type: FieldKey.Select,
        },
        {
            defaultUiProperties: {
                ...LinkControlObject.defaultUiProperties,
                bind: 'column5',
            },
            properties: {
                bind: 'column5',
                title: 'Column 5',
                map(fieldValue) {
                    if (fieldValue) {
                        return `http://${(fieldValue as string).replace(/-|_|\s/g, '').toLowerCase()}.sage.com`;
                    }
                    return '';
                },
            },
            type: FieldKey.Link,
        },
        {
            defaultUiProperties: {
                ...DateControlObject.defaultUiProperties,
                bind: 'column6',
            },
            properties: {
                bind: 'column6',
                title: 'Column 6',
            },
            type: FieldKey.Date,
        },
        {
            defaultUiProperties: {
                ...ProgressControlObject.defaultUiProperties,
                bind: 'column7',
            },
            properties: {
                bind: 'column7',
                title: 'Column 7',
            },
            type: FieldKey.Progress,
        },
        {
            defaultUiProperties: {
                ...ImageControlObject.defaultUiProperties,
                bind: 'column8',
            },
            properties: {
                bind: 'column8',
                title: 'Column 8',
            },
            type: FieldKey.Image,
        },
        {
            defaultUiProperties: {
                ...CheckboxControlObject.defaultUiProperties,
                bind: 'column9',
            },
            properties: {
                bind: 'column9',
                title: 'Column 9',
            },
            type: FieldKey.Checkbox,
        },
        {
            defaultUiProperties: {
                ...FilterSelectControlObject.defaultUiProperties,
                bind: 'column10',
            },
            properties: {
                bind: 'column10',
                title: 'Column 10',
                node: '@sage/xtrem-show-case/FilterSelect',
                valueField: 'code',
                helperTextField: 'description',
                minLookupCharacters: 0,
            },
            type: FieldKey.Reference,
        },
    ],
});

const readOnlyFieldProperties = (): FieldDecoratorProps<FieldKey.Table> => ({
    title: 'Table Test Title',
    pageSize: 20,
    node: '@sage/xtrem-test/Sample',
    isReadOnly: true,
    columns: [
        {
            defaultUiProperties: {
                ...TextControlObject.defaultUiProperties,
                bind: 'column1',
            },
            properties: {
                bind: 'column1',
                canFilter: true,
                title: 'Column 1',
            },
            type: FieldKey.Text,
        },
        {
            defaultUiProperties: {
                ...NumericControlObject.defaultUiProperties,
                bind: 'column2',
            },
            properties: {
                bind: 'column2',
                title: 'Column 2',
            },
            type: FieldKey.Numeric,
        },
        {
            defaultUiProperties: {
                ...ReferenceControlObject.defaultUiProperties,
                bind: 'column3',
            },
            properties: {
                bind: 'column3',
                title: 'Column 3',
                node: '@sage/xtrem-show-case/Reference',
                valueField: 'code',
                helperTextField: 'description',
                minLookupCharacters: 0,
            },
            type: FieldKey.Reference,
        },
        {
            defaultUiProperties: {
                ...SelectControlObject.defaultUiProperties,
                bind: 'column4',
            },
            properties: {
                bind: 'column4',
                title: 'Column 4',
                optionType: '@sage/xtrem-show-case/Sample',
                options: ['option1', 'option2'],
            },
            type: FieldKey.Select,
        },
        {
            defaultUiProperties: {
                ...LinkControlObject.defaultUiProperties,
                bind: 'column5',
            },
            properties: {
                bind: 'column5',
                title: 'Column 5',
                map(fieldValue) {
                    if (fieldValue) {
                        return `http://${(fieldValue as string).replace(/-|_|\s/g, '').toLowerCase()}.sage.com`;
                    }
                    return '';
                },
            },
            type: FieldKey.Link,
        },
        {
            defaultUiProperties: {
                ...DateControlObject.defaultUiProperties,
                bind: 'column6',
            },
            properties: {
                bind: 'column6',
                title: 'Column 6',
            },
            type: FieldKey.Date,
        },
        {
            defaultUiProperties: {
                ...ProgressControlObject.defaultUiProperties,
                bind: 'column7',
            },
            properties: {
                bind: 'column7',
                title: 'Column 7',
            },
            type: FieldKey.Progress,
        },
        {
            defaultUiProperties: {
                ...ImageControlObject.defaultUiProperties,
                bind: 'column8',
            },
            properties: {
                bind: 'column8',
                title: 'Column 8',
            },
            type: FieldKey.Image,
        },
        {
            defaultUiProperties: {
                ...CheckboxControlObject.defaultUiProperties,
                bind: 'column9',
            },
            properties: {
                bind: 'column9',
                title: 'Column 9',
            },
            type: FieldKey.Checkbox,
        },
        {
            defaultUiProperties: {
                ...FilterSelectControlObject.defaultUiProperties,
                bind: 'column10',
            },
            properties: {
                bind: 'column10',
                title: 'Column 10',
                node: '@sage/xtrem-show-case/FilterSelect',
                valueField: 'code',
                helperTextField: 'description',
                minLookupCharacters: 0,
            },
            type: FieldKey.Reference,
        },
    ],
});

const defaultFieldValueData = () => {
    return [
        {
            _id: 'id1',
            column1: 'Test Text Value 1',
            column2: 1,
            column3__code: {
                _id: '_id1',
                code: 'Test Code 1',
            },
            column3__description: {
                _id: '_id1',
                description: 'Test Description 1',
            },
            column4: 'option1' as 'option1',
            column5: 'product1',
            column6: '2020-07-14',
            column7: 50,
            column8: 'image',
            column9: true,
            column10__code: {
                _id: '_id1',
                code: 'Test Code 1',
            },
            column10__description: {
                _id: '_id1',
                description: 'Test Description 1',
            },
        },
        {
            _id: 'id2',
            column1: 'Test Text Value 2',
            column2: 2,
            column3__code: {
                _id: '_id2',
                code: 'Test Code 2',
            },
            column3__description: {
                _id: '_id2',
                description: 'Test Description 2',
            },
            column4: 'option2' as 'option2',
            column5: 'product2',
            column6: '2020-07-15',
            column7: 60,
            column8: 'image2',
            column9: false,
            column10__code: {
                _id: '_id2',
                code: 'Test Code 2',
            },
            column10__description: {
                _id: '_id2',
                description: 'Test Description 2',
            },
        },
    ];
};

const defaultFieldValue = <T extends ClientNode = Node>(
    data: any = defaultFieldValueData(),
): FieldInternalValue<FieldKey.Table, T> => {
    const result = {
        data,
        pageInfo: {
            startCursor: 'startCursor',
            endCursor: 'endCursor',
            hasPreviousPage: false,
            hasNextPage: false,
        },
    };

    return new CollectionValue<T>({
        screenId,
        elementId: tableElementId,
        isTransient: false,
        hasNextPage: result.pageInfo.hasNextPage,
        orderBy: [{ id: 1 }],
        columnDefinitions: [defaultFieldProperties().columns as NestedField<any, GridNestedFieldTypes, T, any>[]],
        nodeTypes: {},
        nodes: ['@sage/xtrem-test/AnyNode'],
        filter: [undefined],
        initialValues: result.data,
        locale: 'en-US',
    });
};

interface SetupTableParameters<T extends ClientNode = Node> {
    fieldProperties?: FieldDecoratorProps<FieldKey.Table>;
    fieldValue?: FieldInternalValue<FieldKey.Table, T>;
    mockActions?: boolean;
    isParentDisabled?: boolean;
    accessBindings?: AccessBindings;
    selectionMode?: 'single' | 'multiple';
    fixedHeight?: number;
    elementId?: string;
}
const setup = <T extends ClientNode = Node>({
    fieldProperties = defaultFieldProperties(),
    fieldValue = defaultFieldValue(),
    mockActions = true,
    isParentDisabled = false,
    accessBindings = {},
    selectionMode = undefined,
    fixedHeight,
    elementId = tableElementId,
}: SetupTableParameters<T> = {}) => {
    const item: PageArticleItem = { $bind: elementId };
    const utils = renderWithRedux<FieldKey.Table, any, Node>(
        <ConnectedTableComponent
            screenId={screenId}
            item={item}
            elementId={elementId}
            isParentDisabled={isParentDisabled}
            selectionMode={selectionMode}
            fixedHeight={fixedHeight}
        />,
        {
            initialState: {
                nodeTypes: {
                    Sample: {
                        name: 'Sample',
                        title: 'Sample',
                        properties: {
                            _id: {
                                type: 'IntOrString',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column1: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column2: {
                                type: GraphQLTypes.Decimal,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column3: {
                                type: 'Reference',
                                kind: GraphQLKind.Object,
                                isOnInputType: true,
                                canFilter: true,
                                isMutable: true,
                            },
                            column4: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column5: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column6: {
                                type: GraphQLTypes.Date,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column7: {
                                type: GraphQLTypes.Decimal,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column8: {
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column9: false,
                            column10: {
                                type: 'Reference',
                                kind: GraphQLKind.Object,
                                isOnInputType: true,
                                canFilter: true,
                                isMutable: true,
                            },
                        },
                        mutations: {},
                    },
                    SampleNode: {
                        title: 'SampleNode',
                        name: 'SampleNode',
                        properties: {
                            _id: {
                                name: '_id',
                                type: 'IntOrString',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column1: {
                                name: 'column1',
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column2: {
                                name: 'column2',
                                type: GraphQLTypes.Decimal,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column3: {
                                name: 'column3',
                                type: 'Reference',
                                kind: GraphQLKind.Object,
                                isOnInputType: true,
                                canFilter: true,
                                isMutable: true,
                            },
                            column4: {
                                name: 'column4',
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column5: {
                                name: 'column5',
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column6: {
                                name: 'column6',
                                type: GraphQLTypes.Date,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column7: {
                                name: 'column7',
                                type: GraphQLTypes.Decimal,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column8: {
                                name: 'column8',
                                type: GraphQLTypes.String,
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            column9: false,
                            column10: {
                                name: 'column10',
                                type: 'Reference',
                                kind: GraphQLKind.Object,
                                isOnInputType: true,
                                canFilter: true,
                                isMutable: true,
                            },
                        },
                        mutations: {},
                    },
                    Reference: {
                        name: 'Reference',
                        title: 'Reference',
                        properties: {
                            _id: {
                                name: '_id',
                                type: 'IntOrString',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            code: {
                                name: 'code',
                                type: 'String',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            description: {
                                name: 'description',
                                type: 'String',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                        },
                        mutations: {},
                    },

                    DeepNestedNode: {
                        name: 'DeepNestedNode',
                        title: 'DeepNestedNode',
                        properties: {
                            _id: {
                                name: '_id',
                                type: 'IntOrString',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            code: {
                                name: 'code',
                                type: 'String',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            address: {
                                name: 'addresses',
                                type: 'Address',
                                kind: GraphQLKind.Object,
                                isOnInputType: true,
                                canFilter: true,
                                isMutable: true,
                            },
                        },
                        mutations: {},
                    },
                    Address: {
                        name: 'Address',
                        title: 'Address',
                        properties: {
                            _id: {
                                name: '_id',
                                type: 'IntOrString',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            line1: {
                                name: 'line1',
                                type: 'String',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            line2: {
                                name: 'line2',
                                type: 'String',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            country: {
                                name: 'country',
                                type: 'Country',
                                kind: GraphQLKind.Object,
                                isOnInputType: true,
                                canFilter: true,
                                isMutable: true,
                            },
                        },
                        mutations: {},
                    },
                    Country: {
                        name: 'Country',
                        title: 'Country',
                        properties: {
                            _id: {
                                name: '_id',
                                type: 'IntOrString',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            name: {
                                name: 'name',
                                type: 'String',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                            code: {
                                name: 'code',
                                type: 'String',
                                kind: GraphQLKind.Scalar,
                                isOnInputType: true,
                                canFilter: true,
                            },
                        },
                        mutations: {},
                    },
                },
                applicationContext: {
                    login: 'admin',
                    locale: 'en-US',
                },
            } as any,
            fieldType: FieldKey.Table,
            fieldValue,
            fieldProperties: fieldProperties as any,
            screenId,
            elementId,
            mockActions,
            accessBindings,
        },
    );
    const helpers = new AgGridHelpers(utils);

    const getAllHeaders = () => helpers.getAllHeaders();
    const getHeader = (columnIndex: number) => helpers.getHeader(columnIndex);
    const getCell = (rowIndex: number, columnIndex: number) => helpers.getCell(rowIndex, columnIndex);
    const getRow = async (rowIndex: number) => helpers.getRow(rowIndex);
    const getAllHeadersTextContent = () => helpers.getAllHeadersTextContent();
    const getColumnByIndex = (columnIndex: number) => helpers.getColumnByIndex(columnIndex);
    const getColumnById = (columnId: number) => helpers.getColumnById(columnId);
    const getRowSelectionCheckbox = async (rowIndex: number): Promise<Element | null> =>
        helpers.getRowSelectionCheckbox(rowIndex);
    const getPhantomRow = async (): Promise<Element | null> => helpers.getPhantomRow();
    const getCellContent = (rowIndex: number, columnIndex: number): string | null | undefined =>
        helpers.getCellContent(rowIndex, columnIndex);

    const includesTestId = (selectedIds: string): Element | null => {
        return utils.getByTestId(dataTestId => {
            const selectedIdsList = selectedIds.split(' ');
            const ids = dataTestId.split(' ');
            return selectedIdsList.every(selectedId => ids.includes(selectedId));
        });
    };

    const doubleClickCell = (rowIndex: number, columnIndex: number) => {
        const cell = getCell(rowIndex, columnIndex);
        if (!cell) {
            return;
        }
        fireEvent.click(cell);
    };
    const clickHeader = (columnIndex: number, options?: {}) => {
        const header = getHeader(columnIndex);
        if (!header) {
            return;
        }
        fireEvent.click(header, options);
    };
    const hitEnter = (input: HTMLInputElement) =>
        fireEvent.keyDown(input, {
            altKey: false,
            bubbles: true,
            cancelable: true,
            cancelBubble: false,
            charCode: 0,
            code: 'Enter',
            composed: true,
            ctrlKey: false,
            currentTarget: document,
            defaultPrevented: false,
            detail: 0,
            eventPhase: 1,
            isComposing: false,
            isTrusted: true,
            key: 'Enter',
            keyCode: 13,
            location: 0,
            metaKey: false,
            repeat: false,
            returnValue: true,
            shiftKey: false,
            srcElement: input,
            target: input,
            type: 'keydown',
            which: 13,
        });

    const hitEsc = (input: HTMLInputElement) =>
        fireEvent.keyDown(input, {
            altKey: false,
            bubbles: true,
            cancelable: true,
            cancelBubble: false,
            charCode: 0,
            code: 'Escape',
            composed: true,
            ctrlKey: false,
            defaultPrevented: false,
            detail: 0,
            eventPhase: 3,
            isComposing: false,
            isTrusted: true,
            key: 'Escape',
            keyCode: 27,
            location: 0,
            metaKey: false,
            repeat: false,
            returnValue: true,
            shiftKey: false,
            type: 'keydown',
            which: 27,
        });

    const pickDate = async (rowIndex: number, columnIndex: number, day: number) => {
        const cell = getCell(rowIndex, columnIndex);
        if (!cell) {
            return;
        }
        fireEvent.click(cell);
        await waitFor(() => {
            expect(document.querySelector('[data-floating-placement]')).toBeInTheDocument();
        });
        const datePicker = (utils.container.closest('body')! as HTMLBodyElement).querySelector('.rdp-root');

        const days = Array.from(datePicker!.querySelectorAll('.rdp-day_button'));
        const dayNode = days.find(d => d.textContent === String(day));
        if (dayNode) {
            fireEvent.click(dayNode);
            const editingCell = (await utils.findByTestId(
                `${tableElementId}-${rowIndex}-${columnIndex + 1}`,
            )) as HTMLInputElement;
            hitEnter(editingCell);
        }
    };

    const checkBoxToggle = async (rowIndex: number, columnIndex: number) => {
        const checkbox = (await waitFor(() => {
            return utils.container.querySelector(
                `.ag-row[row-index="${rowIndex}"] > div[aria-colindex="${columnIndex + 1}"] input[type="checkbox"]`,
            );
        })) as HTMLDivElement;
        fireEvent.click(checkbox);
    };

    const selectCellValue = async (rowIndex: number, columnIndex: number, value: string) => {
        const cell = getCell(rowIndex, columnIndex);
        if (!cell) {
            return;
        }
        fireEvent.doubleClick(cell);
        const editingCell = (await waitFor(() => {
            return includesTestId(`${tableElementId}-${rowIndex}-${columnIndex + 1}-input`);
        })) as HTMLInputElement;

        if (!editingCell) {
            xtremConsole.log(`No editing cell found for row ${rowIndex} and column ${columnIndex + 1}`);
            return;
        }
        await userEvent.type(editingCell, '{Backspace}', { skipClick: true });
        const dropDownItem = utils
            .getAllByTestId('e-ui-select-suggestion-value')
            .find((h: HTMLElement) => h.textContent === value);
        if (!dropDownItem) {
            xtremConsole.log(`No dropdown item found matching value ${value}`);
            return;
        }
        fireEvent.click(dropDownItem);
    };

    const testCannotEditCell = async (rowIndex: number, columnIndex: number) => {
        const cell = getCell(rowIndex, columnIndex);
        if (!cell) {
            return;
        }
        fireEvent.doubleClick(cell);
        const editingCell = await waitFor(() => {
            return utils.container.querySelector('.ag-cell-inline-editing');
        });
        expect(editingCell).toBeNull();
    };

    const getCellInput = async (
        rowIndex: number,
        columnIndex: number,
        isTextInput = false,
    ): Promise<HTMLInputElement | null> => {
        let editingCell: HTMLInputElement;
        if (isTextInput) {
            editingCell = (await waitFor(() => {
                return utils.container.querySelector(
                    `.ag-row[row-index="${rowIndex}"] > .ag-cell-inline-editing.ag-cell-value[aria-colindex="${
                        columnIndex + 1
                    }"] > .ag-cell-edit-wrapper > .ag-cell-editor > div > input`,
                );
            })) as HTMLInputElement;
        } else {
            editingCell = utils.container.querySelector(
                `[data-testid="${tableElementId}-${rowIndex}-${columnIndex + 1}"]`,
            ) as HTMLInputElement;
        }
        if (!editingCell) {
            xtremConsole.log(`No editing cell found for row ${rowIndex} and column ${columnIndex + 1}`);
            return null;
        }
        return editingCell;
    };

    const changeCell = async (
        rowIndex: number,
        columnIndex: number,
        value: string | number,
        skipEnter = false,
    ): Promise<void> => {
        const cell = getCell(rowIndex, columnIndex);
        if (!cell) {
            return;
        }
        fireEvent.click(cell);
        const editingCell = await getCellInput(rowIndex, columnIndex, typeof value === 'string');
        if (editingCell) {
            await userEvent.type(editingCell, `{Backspace}${value}`, { skipClick: true });
            await waitFor(() => expect(editingCell.value).toBe(String(value)));
            if (!skipEnter) {
                hitEnter(editingCell);
            }
        }
    };

    const changeReferenceByTyping = async (
        rowIndex: number,
        columnIndex: number,
        value: string,
        cancel = false,
    ): Promise<void> => {
        const cell = getCell(rowIndex, columnIndex);
        if (!cell) {
            return;
        }
        fireEvent.click(cell);
        const editingCell = (await waitFor(() => {
            return includesTestId(`${tableElementId}-${rowIndex}-${columnIndex + 1}-input`);
        })) as HTMLInputElement;

        if (!editingCell) {
            xtremConsole.log(`No editing cell found for row ${rowIndex} and column ${columnIndex + 1}`);
            return;
        }
        await userEvent.type(editingCell, `{Backspace}${value}{Enter}`, {
            skipClick: true,
        });
        await waitFor(() => expect(editingCell.value).toBe(String(value)));
        if (!cancel) {
            hitEnter(editingCell);
            await waitFor(() => {
                expect(getCell(rowIndex, columnIndex)!.textContent).toBe(String(value));
            });
        } else {
            hitEsc(editingCell);
        }
    };

    const changeReferenceBySelectingFromDropdown = async (
        rowIndex: number,
        columnIndex: number,
        value: string,
    ): Promise<void> => {
        const cell = getCell(rowIndex, columnIndex);
        if (!cell) {
            return;
        }
        fireEvent.click(cell);
        const editingCell = (await waitFor(() => {
            return includesTestId(`${tableElementId}-${rowIndex}-${columnIndex + 1}-input`);
        })) as HTMLInputElement;

        if (!editingCell) {
            xtremConsole.log(`No editing cell found for row ${rowIndex} and column ${columnIndex + 1}`);
            return;
        }
        await userEvent.type(editingCell, `{Backspace}`, {
            skipClick: true,
        });
        fireEvent.change(editingCell, { target: { value } });
        await waitFor(() => expect(editingCell.value).toBe(String(value)));
        const lastItem = await waitFor(() => {
            const dropdownItems = utils.getAllByTestId('e-ui-select-suggestion-value');
            const lastItem = dropdownItems[dropdownItems.length - 1];
            expect(lastItem!.textContent).toBe(String(value));
            return lastItem;
        });
        fireEvent.click(lastItem);
        await waitFor(() => expect(getCell(rowIndex, columnIndex)!.textContent).toBe(String(value)));
    };
    const changeSelectBySelectingFromDropdownWithArrowsNoEnter = async (
        rowIndex: number,
        columnIndex: number,
        arrowDownCount: number,
    ): Promise<void> => {
        const cell = getCell(rowIndex, columnIndex);
        if (!cell) {
            return;
        }
        fireEvent.click(cell);
        const editingCell = (await waitFor(() => {
            return includesTestId(`${tableElementId}-${rowIndex}-${columnIndex + 1}-input`);
        })) as HTMLInputElement;

        if (!editingCell) {
            xtremConsole.log(`No editing cell found for row ${rowIndex} and column ${columnIndex + 1}`);
            return;
        }
        await userEvent.type(editingCell, '{Backspace}', { skipClick: true });
        await waitFor(() => expect(editingCell.value).toBe(''));

        await waitFor(() => {
            const content = document.querySelector('[data-testid=e-ui-select-suggestion-value]')?.textContent;
            expect(content).not.toBeFalsy();
            expect(content!.trim()).not.toEqual('Loading...');
        });
        for (let i = 0; i < arrowDownCount; i += 1) {
            // eslint-disable-next-line no-await-in-loop
            await userEvent.type(editingCell, '{ArrowDown}', { skipClick: true });
        }

        await userEvent.tab();
        await waitFor(() => {
            const chevron = utils.getByTestId(`${tableElementId}-${rowIndex}-${columnIndex + 1}-input-chevron`);
            expect(chevron).toHaveFocus();
        });
        await userEvent.tab();
    };

    const waitForFirstPaint = async (
        visibleColumnLength = defaultFieldProperties().columns?.length,
        testValue = 'Test Text Value 1',
    ) => {
        await waitFor(() => {
            expect(getAllHeaders().length).toBe(visibleColumnLength);
        });
        await waitFor(() => {
            expect(utils.getByText(testValue)).toBeInTheDocument();
        });
    };
    return {
        ...utils,
        changeCell,
        changeReferenceBySelectingFromDropdown,
        changeReferenceByTyping,
        changeSelectBySelectingFromDropdownWithArrowsNoEnter,
        checkBoxToggle,
        clickHeader,
        doubleClickCell,
        fieldValue,
        getAllHeaders,
        getAllHeadersTextContent,
        getCell,
        getCellContent,
        getCellInput,
        getColumnById,
        getColumnByIndex,
        getPhantomRow,
        getRow,
        getRowSelectionCheckbox,
        hitEnter,
        hitEsc,
        includesTestId,
        pickDate,
        selectCellValue,
        testCannotEditCell,
        waitForFirstPaint,
    };
};

describe('Desktop table field', () => {
    beforeEach(() => {
        destroyScreenCollections(screenId);
    });

    afterEach(() => {
        cleanup();
        setCellValueMock!.mockClear();
        jest.clearAllMocks();
        applyActionMocks();
    });

    it('should render table component with data', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        expect(utils.includesTestId('e-table-field')).not.toHaveClass('e-hidden');
        expect(utils.includesTestId('e-table-field')).not.toHaveClass('e-disabled');
        expect(utils.getAllHeadersTextContent()).toEqual([
            'Column 1',
            'Column 2',
            'Column 3',
            'Column 4',
            'Column 5',
            'Column 6',
            'Column 7',
            'Column 8',
            'Column 9',
            'Column 10',
        ]);
        expect(utils.getColumnByIndex(1)).toEqual(expect.arrayContaining(['Test Text Value 1', 'Test Text Value 2']));
    });

    // TODO: Fix to use table views XT-44060
    // it('should render table component with correct column order when a column is hidden in userClientCustomizations', async () => {
    //     storage = JSON.stringify(DEFAULT_STORAGE_DATA);
    //     Object.defineProperty(window, 'localStorage', {
    //         value: {
    //             getItem: jest.fn((key: string) => {
    //                 return key === USER_CLIENT_CUSTOMIZATION_KEY ? storage : undefined;
    //             }),
    //             setItem: jest.fn((key: string, value: string) => {
    //                 if (key === USER_CLIENT_CUSTOMIZATION_KEY) {
    //                     storage = value;
    //                 }
    //             }),
    //         },
    //         writable: true,
    //     });

    //     const utils = setup({
    //         fieldValue: defaultFieldValue([
    //             {
    //                 _id: 'id1',
    //                 column1: 'Test Text Value 1',
    //                 column2: 1,
    //                 column3__code: {
    //                     _id: '_id1',
    //                     code: 'Test Code 1',
    //                     description: 'Test Description 1',
    //                     title: 'Test Title 1',
    //                 },
    //                 column4: 'option1',
    //                 column5: 'product1',
    //                 column6: '2020-07-14',
    //                 column7: 50,
    //                 column8: 'image',
    //                 column9: true,
    //                 column10__code: {
    //                     _id: '_id1',
    //                     code: 'Test Code 1',
    //                     description: 'Test Description 1',
    //                     title: 'Test Title 1',
    //                 },
    //             },
    //         ] as any),
    //     });
    //     await utils.waitForFirstPaint();

    //     expect(utils.getAllHeadersTextContent()).toEqual([
    //         'Column 1',
    //         'Column 3',
    //         'Column 4',
    //         'Column 5',
    //         'Column 6',
    //         'Column 7',
    //         'Column 8',
    //         'Column 9',
    //         'Column 10',
    //     ]);
    // });

    it('should render hidden', async () => {
        const fieldProperties = { ...defaultFieldProperties(), isHidden: true };
        const utils = setup({ fieldProperties });
        await utils.waitForFirstPaint();
        expect(utils.includesTestId('e-table-field')).toHaveClass('e-hidden');
    });

    it('should render hidden when isHiddenOnMainField', async () => {
        const visibleColumnLength = (defaultFieldProperties().columns?.length || 1) - 1;
        const fieldProperties = { ...defaultFieldProperties() };
        (fieldProperties.columns?.[1] as any).properties.isHiddenOnMainField = true;
        const utils = setup({ fieldProperties });
        await utils.waitForFirstPaint(visibleColumnLength);
        expect(utils.getAllHeadersTextContent()).toEqual([
            'Column 1',
            'Column 3',
            'Column 4',
            'Column 5',
            'Column 6',
            'Column 7',
            'Column 8',
            'Column 9',
            'Column 10',
        ]);
    });

    it('should render disabled', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), isDisabled: true } });
        await utils.waitForFirstPaint();
        expect(utils.includesTestId('e-table-field')).toHaveClass('e-disabled');

        // It should also make any editable cells non editable
        expect(utils.getCell(0, 1)?.className).not.toContain('e-nested-cell-editable');
        expect(utils.getCell(1, 1)?.className).not.toContain('e-nested-cell-editable');
    });

    it('should render using auto height layout mode by default', async () => {
        const utils = setup({});
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelectorAll('.ag-layout-normal')).toHaveLength(0);
        expect(utils.baseElement.querySelectorAll('.ag-layout-auto-height')).toHaveLength(6);
    });

    it('should render with normal layout mode and set the element height when a fixed height is set', async () => {
        const utils = setup({ fixedHeight: 650 });
        await utils.waitForFirstPaint();
        expect(utils.queryByTestId('e-field-bind-tableElementId', { exact: false })?.style.height).toEqual('702px');
        expect((utils.baseElement.querySelector('.ag-theme-balham') as HTMLElement).style.height).toEqual('650px');
        expect(utils.baseElement.querySelectorAll('.ag-layout-normal')).toHaveLength(6);
        expect(utils.baseElement.querySelectorAll('.ag-layout-auto-height')).toHaveLength(0);
    });

    it('should render read-only', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), isReadOnly: true } });
        await utils.waitForFirstPaint();
        expect(utils.includesTestId('e-table-field')).toHaveClass('e-read-only');

        // It should also make any editable cells non editable
        expect(utils.getCell(0, 1)?.className).not.toContain('e-nested-cell-editable');
        expect(utils.getCell(1, 1)?.className).not.toContain('e-nested-cell-editable');
    });

    it('should render table with nested columns', async () => {
        const utils = setup<AddressNode>({
            fieldProperties: deepNestedColumnsFieldProperties(),
            fieldValue: defaultFieldValue<AddressNode>([
                {
                    _id: '1',
                    line1: 'Av Diagonal 200',
                    line2: 'Sant Marti',
                    country: {
                        _id: '2',
                        name: 'Spain',
                        code: 'ES',
                    },
                },
            ]),
        });
        await utils.waitForFirstPaint(4, 'Av Diagonal 200');

        // It should also make any editable cells non editable
        expect(utils.getCellContent(0, 1)).toEqual('Av Diagonal 200');
        expect(utils.getCellContent(0, 2)).toEqual('Sant Marti');
        expect(utils.getCellContent(0, 3)).toEqual('ES');
        expect(utils.getCellContent(0, 4)).toEqual('Spain');
    });

    it('should render disabled if parent field is disabled', async () => {
        const utils = setup({ isParentDisabled: true });
        await utils.waitForFirstPaint();
        expect(utils.includesTestId('e-table-field')).toHaveClass('e-disabled');

        // It should also make any editable cells non editable
        expect(utils.getCell(0, 1)?.className).not.toContain('e-nested-cell-editable');
        expect(utils.getCell(1, 1)?.className).not.toContain('e-nested-cell-editable');
    });

    it('should render title', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), title: 'TEST TITLE' } });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('.e-field-title')).toHaveTextContent('TEST TITLE');
    });

    it('should render title by callback function', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), title: () => 'TEST TITLE' } });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('.e-field-title')).toHaveTextContent('TEST TITLE');
    });

    it('should render not render the title if it is not set', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), title: undefined } });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('.e-field-title')).toBeNull();
    });

    it('should not render not the title if the callback property returns a falsy value', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), title: () => '' } });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('.e-field-title')).toBeNull();
    });

    it('should not render not the title if the isHiddenTitle property is set to true', async () => {
        const utils = setup({
            fieldProperties: { ...defaultFieldProperties(), title: 'my test title', isTitleHidden: true },
        });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('.e-field-title')).toBeNull();
    });

    it('should trigger click event for text cell', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        utils.doubleClickCell(0, 1);
        await waitFor(() => {
            expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(1);
            expect(events.triggerNestedFieldEvent).toHaveBeenNthCalledWith(
                1,
                screenId,
                tableElementId,
                { bind: 'column1', title: 'Column 1' },
                'onClick',
                'id1',
                {
                    _id: 'id1',
                    column1: 'Test Text Value 1',
                    column2: 1,
                    column3: {
                        _id: '_id1',
                        code: 'Test Code 1',
                        description: 'Test Description 1',
                    },
                    column4: 'option1',
                    column5: 'product1',
                    column6: '2020-07-14',
                    column7: 50,
                    column8: 'image',
                    column9: true,
                    column10: {
                        _id: '_id1',
                        code: 'Test Code 1',
                        description: 'Test Description 1',
                    },
                },
                0,
            );
        });
    });

    it('should not show hidden column', async () => {
        const fieldProperties = set(defaultFieldProperties(), 'columns[1].properties.isHidden', true);
        const utils = setup({ fieldProperties });
        await utils.waitForFirstPaint((fieldProperties.columns?.length || 0) - 1);
        expect(utils.getAllHeadersTextContent()).toEqual(expect.not.arrayContaining(['Column 2']));
    });

    it("should disable a column when 'isReadOnly' is set to true", async () => {
        const fieldProperties = set(defaultFieldProperties(), 'columns[1].properties.isReadOnly', true);
        const utils = setup({ fieldProperties });
        await utils.waitForFirstPaint();
        await utils.testCannotEditCell(0, 2);
    });

    it("should disable a column when 'isReadOnly' callback returns true", async () => {
        const isReadOnlyMock = jest.fn(() => true);
        const fieldProperties = set(defaultFieldProperties(), 'columns[1].properties.isReadOnly', isReadOnlyMock);
        const utils = setup({ fieldProperties });
        await utils.waitForFirstPaint();
        await utils.testCannotEditCell(0, 2);
        expect(isReadOnlyMock).toHaveBeenCalledWith(1, {
            _id: 'id1',
            column1: 'Test Text Value 1',
            column2: 1,
            column3: { _id: '_id1', code: 'Test Code 1', description: 'Test Description 1' },
            column4: 'option1',
            column5: 'product1',
            column6: '2020-07-14',
            column7: 50,
            column8: 'image',
            column9: true,
            column10: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
        });
    });

    it('should render empty cells as empty strings', async () => {
        const value = defaultFieldValueData();
        const allKeys = Object.keys(value[0]).filter(
            v => v !== 'column3__description' && v !== 'column10__description',
        );
        allKeys.forEach((key, index) => {
            if (index === 1) {
                // Hide all column except first, which is being used as an assertion by 'waitForFirstPaint'
                return;
            }
            value[0][key] = null;
        });
        const fieldValue = defaultFieldValue(value);
        const utils = setup({ fieldValue });
        await utils.waitForFirstPaint();
        for (let i = 2; i < allKeys.length - 2; i += 1) {
            const content = utils.getCellContent(0, i);
            // The progress component displays '0%of100%' when empty in a hidden element
            if (content !== '0%of100%') {
                expect(utils.getCellContent(0, i)).toBe('');
            }
        }
    });

    it('should be able to change a text cell triggering actions & event listeners', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        const columnId = 'column1';
        const recordId = 'id1';
        await utils.changeCell(0, 1, 'changed');
        await waitFor(() => {
            expect(setCellValueMock).toHaveBeenCalledWith({
                columnId,
                level: undefined,
                recordId,
                value: 'changed',
                isOrganicChange: true,
            });
            // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
            expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(2);
            expect(events.triggerFieldEvent).toHaveBeenCalledTimes(3);
            expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                1,
                'TestPage',
                'tableElementId',
                'onAllDataLoaded',
            );
        });

        const expected = {
            _id: 'id1',
            column1: 'changed',
            column2: 1,
            column3: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
            column4: 'option1',
            column5: 'product1',
            column6: '2020-07-14',
            column7: 50,
            column8: 'image',
            column9: true,
            column10: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
        };
        expect(events.triggerNestedFieldEvent).toHaveBeenNthCalledWith(
            2,
            screenId,
            tableElementId,
            { bind: 'column1', title: 'Column 1' },
            'onChange',
            columnId,
            expected,
            0,
        );
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
            3,
            screenId,
            tableElementId,
            'onRowClick',
            'id1',
            expected,
            false,
        );
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
            4,
            screenId,
            tableElementId,
            'onChange',
            columnId,
            expected,
            0,
        );

        expect(
            (utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any).__dirtyColumns,
        ).toMatchObject(new Set([columnId]));
        await waitFor(() => {
            expect(utils.getCellContent(0, 1)).toBe('changed');
        });
    });

    it('should change the background color of a cell when it is modified', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        await waitFor(async () => {
            const row = await utils.getRow(0);
            expect(row).not.toHaveClass('ag-row-edited');
        });
        await utils.changeCell(0, 1, 'changed');
        await waitFor(async () => {
            const row = await utils.getRow(0);
            expect(row).toHaveClass('ag-row-edited');
        });
    });

    it('should not change the background color of a cell when it is modified if the isChangeIndicatorDisabled is set', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), isChangeIndicatorDisabled: true } });
        await utils.waitForFirstPaint();
        await waitFor(async () => {
            const row = await utils.getRow(0);
            expect(row).not.toHaveClass('ag-row-edited');
        });
        await utils.changeCell(0, 1, 'changed');
        await waitFor(
            async () => {
                const row = await utils.getRow(0);
                expect(row).not.toHaveClass('ag-row-edited');
            },
            { timeout: 2000 },
        );
    });

    it('should contain editable cell css class if field is not disabled', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties() } });
        await utils.waitForFirstPaint();
        const cellClassname = utils.getCell(0, 1)?.className;
        expect(cellClassname).toContain('e-nested-cell-editable');
    });

    it('should not contain editable cell css class if field is disabled', async () => {
        const utils = setup({ fieldProperties: { ...disabledFieldProperties() } });
        await utils.waitForFirstPaint();
        const cellClassname = utils.getCell(0, 1)?.className;
        expect(cellClassname).not.toContain('e-nested-cell-editable');
    });

    it('should not contain editable cell css class if field is read-only', async () => {
        const utils = setup({ fieldProperties: { ...readOnlyFieldProperties() } });
        await utils.waitForFirstPaint();
        const cellClassname = utils.getCell(0, 1)?.className;
        expect(cellClassname).not.toContain('e-nested-cell-editable');
    });

    it('should render with an info message', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), infoMessage: 'Hi there!' } });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('[data-element="info"]')).not.toBeNull();
    });

    it('should render with an warning message', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), warningMessage: 'Hi there!' } });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
    });

    it('should prioritize warning over info message', async () => {
        const utils = setup({
            fieldProperties: { ...defaultFieldProperties(), infoMessage: 'Hi there!', warningMessage: 'Hi there!' },
        });
        await utils.waitForFirstPaint();
        expect(utils.baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
        expect(utils.baseElement.querySelector('[data-element="info"]')).toBeNull();
    });

    it('should be able to change a numeric cell triggering actions & event listeners', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        await utils.changeCell(0, 2, 3);
        const columnId = 'column2';
        const recordId = 'id1';
        await waitFor(() => {
            expect(setCellValueMock).toHaveBeenCalledWith({
                columnId,
                level: undefined,
                recordId,
                value: 3,
                isOrganicChange: true,
            });
            // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
            expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(2);
            expect(events.triggerFieldEvent).toHaveBeenCalledTimes(3);
            expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                1,
                'TestPage',
                'tableElementId',
                'onAllDataLoaded',
            );
        });

        const expected = {
            _id: 'id1',
            column1: 'Test Text Value 1',
            column2: 3,
            column3: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
            column4: 'option1',
            column5: 'product1',
            column6: '2020-07-14',
            column7: 50,
            column8: 'image',
            column9: true,
            column10: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
        };
        expect(events.triggerNestedFieldEvent).toHaveBeenNthCalledWith(
            2,
            screenId,
            tableElementId,
            { bind: 'column2', title: 'Column 2' },
            'onChange',
            columnId,
            expected,
            0,
        );
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
            3,
            screenId,
            tableElementId,
            'onRowClick',
            'id1',
            expected,
            false,
        );
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
            4,
            screenId,
            tableElementId,
            'onChange',
            columnId,
            expected,
            0,
        );

        expect(
            (utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any).__dirtyColumns,
        ).toMatchObject(new Set([columnId]));
        await waitFor(() => {
            expect(utils.getCellContent(0, 2)).toBe('3');
        });
    });

    it('should only commit numeric value changes when enter is hit', async () => {
        const columnId = 'column2';
        const recordId = 'id1';
        const utils = setup();
        await utils.waitForFirstPaint();
        await utils.changeCell(0, 2, 3, true);
        // Just so any pending event can settle
        jest.runOnlyPendingTimers();
        await waitFor(() => {
            expect(setCellValueMock).not.toHaveBeenCalled();
            // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
            expect(events.triggerNestedFieldEvent).toHaveBeenCalled();
            expect(events.triggerFieldEvent).toHaveBeenCalled();
        });
        await utils.hitEnter(utils.getCell(0, 2) as HTMLInputElement);
        await waitFor(() => {
            expect(setCellValueMock).toHaveBeenCalledWith({
                columnId,
                level: undefined,
                recordId,
                value: 3,
                isOrganicChange: true,
            });
            // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
            expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(2);
            expect(events.triggerFieldEvent).toHaveBeenCalledTimes(3);
            expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                1,
                'TestPage',
                'tableElementId',
                'onAllDataLoaded',
            );
        });
    });

    it('should be able to revert the value of a numeric cell by hitting escape', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        await utils.changeCell(0, 2, 3, true);
        await waitFor(() => {
            return utils.getCellInput(0, 2);
        });
        await utils.hitEsc(utils.getCell(0, 2) as HTMLInputElement);
        // Just so any pending event can settle
        jest.runOnlyPendingTimers();
        await waitFor(() => {
            expect(setCellValueMock).not.toHaveBeenCalled();
            // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
            expect(events.triggerNestedFieldEvent).toHaveBeenCalled();
            expect(events.triggerFieldEvent).toHaveBeenCalled();
        });
        await waitFor(async () => {
            const cell = await utils.getCellInput(0, 2);
            expect(cell).toBe(null);
        });
    });

    it('should be able to update a deep-bound text field', async () => {
        const recordId = '1';
        const utils = setup<AddressNode>({
            fieldProperties: deepNestedColumnsFieldProperties(),
            fieldValue: defaultFieldValue<AddressNode>([
                {
                    _id: recordId,
                    line1: 'Av Diagonal 200',
                    line2: 'Sant Marti',
                    country: {
                        _id: '2',
                        name: 'Spain',
                        code: 'ES',
                    },
                },
            ]),
        });

        await utils.waitForFirstPaint(4, 'Av Diagonal 200');
        await utils.changeCell(0, 4, 'France');
        await waitFor(() => {
            expect(setCellValueMock).toHaveBeenCalledWith({
                columnId: 'country.name',
                level: undefined,
                recordId,
                value: 'France',
                addToDirty: ['country.name'],
                isOrganicChange: true,
            });
            // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
            expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(2);
            expect(events.triggerFieldEvent).toHaveBeenCalledTimes(3);
            expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                1,
                'TestPage',
                'tableElementId',
                'onAllDataLoaded',
            );
        });

        const expected = {
            _id: recordId,
            line1: 'Av Diagonal 200',
            line2: 'Sant Marti',
            country: {
                _id: '2',
                name: 'France',
                code: 'ES',
            },
        };
        expect(events.triggerNestedFieldEvent).toHaveBeenNthCalledWith(
            2,
            screenId,
            tableElementId,
            {
                bind: { country: { name: true } },
                title: 'Country name',
                canFilter: true,
            },
            'onChange',
            'country.name',
            expected,
            0,
        );
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
            3,
            screenId,
            tableElementId,
            'onRowClick',
            '1',
            expected,
            false,
        );
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
            4,
            screenId,
            tableElementId,
            'onChange',
            'country.name',
            expected,
            0,
        );

        const updated = utils.fieldValue.getRawRecord({ id: recordId, cleanMetadata: false }) as any;
        expect(updated.country.name).toEqual('France');
        expect(updated.__dirtyColumns).toMatchObject(new Set(['country', 'country.name']));

        expect(utils.getCellContent(0, 4)).toBe('France');
    });

    describe('access rights', () => {
        it('should render fields enabled if no access right rules are defined', async () => {
            const utils = setup({
                accessBindings: {
                    SampleNode: {},
                },
            });
            await utils.waitForFirstPaint();
            expect(utils.getCell(0, 1)).toHaveClass('e-nested-cell-editable');
            expect(utils.getCell(0, 2)).toHaveClass('e-nested-cell-editable');
            expect(utils.getCell(0, 3)).toHaveClass('e-nested-cell-editable');
            expect(utils.getCell(0, 4)).toHaveClass('e-nested-cell-editable');
            // The label field is not editable
            expect(utils.getCell(0, 5)).not.toHaveClass('e-nested-cell-editable');
            expect(utils.getCell(0, 6)).toHaveClass('e-nested-cell-editable');
            // Progress field is not editable
            expect(utils.getCell(0, 7)).not.toHaveClass('e-nested-cell-editable');
            // Image field is not editable
            expect(utils.getCell(0, 8)).not.toHaveClass('e-nested-cell-editable');
        });

        it('should render authorized columns and not to disable them', async () => {
            const utils = setup({
                accessBindings: {
                    SampleNode: {
                        column1: 'authorized',
                        column2: 'authorized',
                    },
                },
            });
            await utils.waitForFirstPaint();
            expect(utils.getCell(0, 1)).toHaveClass('e-nested-cell-editable');
            expect(utils.getCell(0, 2)).toHaveClass('e-nested-cell-editable');
        });

        it('should render unauthorized columns', async () => {
            const utils = setup({
                accessBindings: {
                    SampleNode: {
                        column4: 'unauthorized',
                        column5: 'unauthorized',
                    },
                },
            });
            await utils.waitForFirstPaint();
        });

        it('should not render unavailable columns', async () => {
            const utils = setup({
                accessBindings: {
                    SampleNode: {
                        column4: 'unavailable',
                        column5: 'unavailable',
                    },
                },
            });
            await utils.waitForFirstPaint(defaultFieldProperties().columns!.length - 2);
            const headers = await utils.getAllHeaders();
            expect(headers[0]).toHaveTextContent('Column 1');
            expect(headers[1]).toHaveTextContent('Column 2');
            expect(headers[2]).toHaveTextContent('Column 3');
            expect(headers[3]).toHaveTextContent('Column 6');
            expect(headers[4]).toHaveTextContent('Column 7');
            expect(headers[5]).toHaveTextContent('Column 8');
            expect(headers[6]).toHaveTextContent('Column 9');
        });

        it('should disable unauthorized columns', async () => {
            const utils = setup({
                accessBindings: {
                    SampleNode: {
                        column1: 'unauthorized',
                        column2: 'unauthorized',
                    },
                },
            });
            await utils.waitForFirstPaint();
            expect(utils.getCell(0, 1)).not.toHaveClass('e-nested-cell-editable');
            expect(utils.getCell(0, 2)).not.toHaveClass('e-nested-cell-editable');
            expect(utils.getCell(0, 3)).toHaveClass('e-nested-cell-editable');
            expect(utils.getCell(0, 4)).toHaveClass('e-nested-cell-editable');
            // The label field is not editable
            expect(utils.getCell(0, 5)).not.toHaveClass('e-nested-cell-editable');
            expect(utils.getCell(0, 6)).toHaveClass('e-nested-cell-editable');
            // Progress field is not editable
            expect(utils.getCell(0, 7)).not.toHaveClass('e-nested-cell-editable');
            // Image field is not editable
            expect(utils.getCell(0, 8)).not.toHaveClass('e-nested-cell-editable');
        });
    });

    describe('should be able to change a reference cell by triggering actions & event listeners', () => {
        const result = {
            query: {
                edges: [
                    {
                        node: { _id: '_id1', code: 'Test Code 1', description: 'Test Description 1' },
                        cursor: 'startCursor',
                    },
                    {
                        node: { _id: '_id2', code: 'Test Code 2', description: 'Test Description 2' },
                        cursor: 'endCursor',
                    },
                ],
                pageInfo: {
                    hasNextPage: false,
                    hasPreviousPage: false,
                    startCursor: 'startCursor',
                    endCursor: 'endCursor',
                },
            },
        };
        const fetchReferenceFieldSuggestionsMock = (args: FetchReferenceFieldSuggestionsInput<any>) => {
            return Promise.resolve(
                result.query.edges
                    .map(n => n.node)
                    .filter(
                        n => n.code.includes(args.filterValue ?? '') || n.description.includes(args.filterValue ?? ''),
                    ),
            );
        };
        const fetchReferenceFieldDataMock = () => {
            return Promise.resolve(result);
        };

        let fetchReferenceFieldSuggestionsSpy: jest.SpyInstance<any, any> | null = null;
        let fetchReferenceFieldDataSpy: jest.SpyInstance<any, any> | null;
        beforeEach(() => {
            fetchReferenceFieldSuggestionsSpy?.mockClear();
            fetchReferenceFieldDataSpy?.mockClear();
            fetchReferenceFieldSuggestionsSpy = jest
                .spyOn(graphqlService, 'fetchReferenceFieldSuggestions')
                .mockImplementation(fetchReferenceFieldSuggestionsMock);
            fetchReferenceFieldDataSpy = jest
                .spyOn(graphqlService, 'fetchReferenceFieldData')
                .mockImplementation(fetchReferenceFieldDataMock);
        });

        it('should be able to type', async () => {
            const utils = setup();
            const original = utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any;
            expect(original.column3).toEqual({
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            });
            const recordId = 'id1';
            await utils.waitForFirstPaint();
            await utils.changeReferenceByTyping(0, 3, 'Test Code 2');
            await waitFor(() => {
                expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalled();
                expect(setCellValueMock).toHaveBeenCalledWith({
                    columnId: 'column3',
                    level: undefined,
                    recordId,
                    value: { _id: '_id2', code: 'Test Code 2', description: 'Test Description 2' },
                    isOrganicChange: true,
                });
                // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
                expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(2);
                expect(events.triggerFieldEvent).toHaveBeenCalledTimes(3);
                expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                    1,
                    'TestPage',
                    'tableElementId',
                    'onAllDataLoaded',
                );
            });

            const expected = {
                _id: 'id1',
                column1: 'Test Text Value 1',
                column2: 1,
                column3: {
                    _id: '_id2',
                    code: 'Test Code 2',
                    description: 'Test Description 2',
                },
                column4: 'option1',
                column5: 'product1',
                column6: '2020-07-14',
                column7: 50,
                column8: 'image',
                column9: true,
                column10: {
                    _id: '_id1',
                    code: 'Test Code 1',
                    description: 'Test Description 1',
                },
            };
            expect(events.triggerNestedFieldEvent).toHaveBeenNthCalledWith(
                2,
                screenId,
                tableElementId,
                {
                    bind: 'column3',
                    helperTextField: 'description',
                    minLookupCharacters: 0,
                    node: '@sage/xtrem-show-case/Reference',
                    title: 'Column 3',
                    valueField: 'code',
                },
                'onChange',
                'column3',
                expected,
                0,
            );
            expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                3,
                screenId,
                tableElementId,
                'onChange',
                'column3',
                expected,
                0,
            );

            const updated = utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any;
            expect(updated.column3).toEqual({
                _id: '_id2',
                code: 'Test Code 2',
                description: 'Test Description 2',
            });
            expect(updated.column3__description).toBeUndefined();
            expect(updated.__dirtyColumns).toMatchObject(new Set(['column3']));

            expect(utils.getCellContent(0, 3)).toBe('Test Code 2');
        });

        it('should select the row using the checkbox', async () => {
            const utils = setup({
                fieldProperties: {
                    ...defaultFieldProperties(),
                    canSelect: true,
                },
                fieldValue: defaultFieldValue([
                    {
                        _id: 'id1',
                        column1: 'Test Text Value 1',
                        column2: 1,
                        column3__code: {
                            _id: '_id1',
                            code: 'Test Code 1',
                            description: 'Test Description 1',
                            title: 'Test Title 1',
                        },
                        column4: 'option1',
                        column5: 'product1',
                        column6: '2020-07-14',
                        column7: 50,
                        column8: 'image',
                        column9: true,
                        column10: {
                            _id: '_id1',
                            code: 'Test Code 1',
                            description: 'Test Description 1',
                        },
                    },
                    {
                        _id: 'id2',
                        column1: 'Test Text Value 2',
                        column2: 2,
                        column3__code: {
                            _id: '_id2',
                            code: 'Test Code 2',
                            description: 'Test Description 2',
                            title: 'Test Title 2',
                        },
                        column4: 'option2',
                        column5: 'product2',
                        column6: '2020-07-15',
                        column7: 60,
                        column8: 'image2',
                        column9: false,
                        column10: {
                            _id: '_id1',
                            code: 'Test Code 1',
                            description: 'Test Description 1',
                        },
                    },
                ] as any),
            });
            await utils.waitForFirstPaint(defaultFieldProperties().columns!.length + 1); // +1 for pinned column

            const checkbox = await utils.getRowSelectionCheckbox(1)!;
            if (checkbox) {
                fireEvent.click(checkbox);
            }

            await waitFor(async () => {
                expect(events.triggerFieldEvent).toHaveBeenCalledWith(
                    screenId,
                    tableElementId,
                    'onRowSelected',
                    'id2',
                    {
                        _id: 'id2',
                        column1: 'Test Text Value 2',
                        column2: 2,
                        column3: {
                            _id: '_id2',
                            code: 'Test Code 2',
                            description: 'Test Description 2',
                            title: 'Test Title 2',
                        },
                        column4: 'option2',
                        column5: 'product2',
                        column6: '2020-07-15',
                        column7: 60,
                        column8: 'image2',
                        column9: false,
                        column10: {
                            _id: '_id1',
                            code: 'Test Code 1',
                            description: 'Test Description 1',
                        },
                    },
                );
            });
        });

        it('should be able to select from dropdown', async () => {
            const utils = setup();
            const original = utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any;
            expect(original.column3).toEqual({
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            });
            const recordId = 'id1';
            const expected = {
                _id: 'id1',
                column1: 'Test Text Value 1',
                column2: 1,
                column3: {
                    _id: '_id2',
                    code: 'Test Code 2',
                    description: 'Test Description 2',
                },
                column4: 'option1',
                column5: 'product1',
                column6: '2020-07-14',
                column7: 50,
                column8: 'image',
                column9: true,
                column10: {
                    _id: '_id1',
                    code: 'Test Code 1',
                    description: 'Test Description 1',
                },
            };

            await utils.waitForFirstPaint();
            await utils.changeReferenceBySelectingFromDropdown(0, 3, 'Test Code 2');
            await waitFor(() => {
                expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalled();
                expect(setCellValueMock).toHaveBeenCalledWith({
                    columnId: 'column3',
                    level: undefined,
                    recordId,
                    value: { _id: '_id2', code: 'Test Code 2', description: 'Test Description 2' },
                    isOrganicChange: true,
                });
                // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
                expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(2);
                expect(events.triggerFieldEvent).toHaveBeenCalledTimes(3);
                expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                    1,
                    'TestPage',
                    'tableElementId',
                    'onAllDataLoaded',
                );
            });

            expect(events.triggerNestedFieldEvent).toHaveBeenNthCalledWith(
                2,
                screenId,
                tableElementId,
                expect.objectContaining({ bind: 'column3' }),
                'onChange',
                'column3',
                expected,
                0,
            );
            expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                3,
                screenId,
                tableElementId,
                'onChange',
                'column3',
                expected,
                0,
            );

            const updated = utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any;
            expect(updated.column3).toEqual({
                _id: '_id2',
                code: 'Test Code 2',
                description: 'Test Description 2',
            });
            expect(updated.__dirtyColumns).toMatchObject(new Set(['column3']));
            expect(utils.getCellContent(0, 3)).toBe('Test Code 2');
        });

        it('should be able to clear value', async () => {
            const utils = setup();
            const original = utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any;
            expect(original.column3).toEqual({
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            });
            const recordId = 'id1';
            const expected = {
                _id: 'id1',
                column1: 'Test Text Value 1',
                column2: 1,
                column3: null,
                column4: 'option1',
                column5: 'product1',
                column6: '2020-07-14',
                column7: 50,
                column8: 'image',
                column9: true,
                column10: {
                    _id: '_id1',
                    code: 'Test Code 1',
                    description: 'Test Description 1',
                },
            };

            await utils.waitForFirstPaint();
            await utils.changeReferenceByTyping(0, 3, '');
            await waitFor(() => {
                expect(setCellValueMock).toHaveBeenCalledWith({
                    columnId: 'column3',
                    level: undefined,
                    recordId,
                    value: null,
                    isOrganicChange: true,
                });
                // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
                expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(2);
                expect(events.triggerFieldEvent).toHaveBeenCalledTimes(3);
                expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                    1,
                    'TestPage',
                    'tableElementId',
                    'onAllDataLoaded',
                );
            });

            expect(events.triggerNestedFieldEvent).toHaveBeenNthCalledWith(
                2,
                screenId,
                tableElementId,
                {
                    bind: 'column3',
                    helperTextField: 'description',
                    minLookupCharacters: 0,
                    node: '@sage/xtrem-show-case/Reference',
                    title: 'Column 3',
                    valueField: 'code',
                },
                'onChange',
                'column3',
                expected,
                0,
            );
            expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                3,
                screenId,
                tableElementId,
                'onChange',
                'column3',
                expected,
                0,
            );

            const updated = utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any;
            expect(updated.column3).toEqual(null);
            expect(updated.__dirtyColumns).toMatchObject(new Set(['column3']));
            expect(utils.getCellContent(0, 3)).toBe('');
        });

        it('should be able to revert previous value', async () => {
            const utils = setup();
            await utils.waitForFirstPaint();

            // random & restore
            await utils.changeReferenceByTyping(0, 3, 'hola', true);
            expect(setCellValueMock).not.toHaveBeenCalled();
            expect(events.triggerNestedFieldEvent).toHaveBeenCalled();
            expect(events.triggerFieldEvent).toHaveBeenCalled();
            await waitFor(() => {
                expect(utils.getCellContent(0, 3)).toBe('Test Code 1');
            });
        });

        it('Should remain empty if the user removes the input', async () => {
            const utils = setup();
            await utils.waitForFirstPaint();
            await utils.changeReferenceByTyping(0, 3, '', true);
            expect(setCellValueMock).toHaveBeenCalledTimes(1);
            expect(setCellValueMock).toHaveBeenNthCalledWith(1, {
                addToDirty: undefined,
                columnId: 'column3',
                isOrganicChange: true,
                level: undefined,
                recordId: 'id1',
                value: null,
            });
            expect(events.triggerNestedFieldEvent).toHaveBeenCalled();
            expect(events.triggerFieldEvent).toHaveBeenCalled();
            await waitFor(() => {
                expect(utils.getCellContent(0, 3)).toBe('');
            });
        });
    });

    describe('should be able to change a select cell by triggering actions & event listeners', () => {
        it('should be able to select from dropdown', async () => {
            const utils = setup();
            const original = utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any;
            expect(original.column3).toEqual({
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            });
            const recordId = 'id1';
            const expected = {
                _id: 'id1',
                column1: 'Test Text Value 1',
                column2: 1,
                column3: {
                    _id: '_id1',
                    code: 'Test Code 1',
                    description: 'Test Description 1',
                },
                column4: 'option2',
                column5: 'product1',
                column6: '2020-07-14',
                column7: 50,
                column8: 'image',
                column9: true,
                column10: {
                    _id: '_id1',
                    code: 'Test Code 1',
                    description: 'Test Description 1',
                },
            };

            await utils.waitForFirstPaint();
            await utils.changeReferenceBySelectingFromDropdown(0, 4, 'option2');
            await waitFor(() => {
                expect(setCellValueMock).toHaveBeenCalledWith({
                    columnId: 'column4',
                    level: undefined,
                    recordId,
                    value: 'option2',
                    isOrganicChange: true,
                });
                // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
                expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(2);
                expect(events.triggerFieldEvent).toHaveBeenCalledTimes(3);
                expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                    1,
                    'TestPage',
                    'tableElementId',
                    'onAllDataLoaded',
                );
            });

            expect(events.triggerNestedFieldEvent).toHaveBeenNthCalledWith(
                2,
                screenId,
                tableElementId,
                expect.objectContaining({ bind: 'column4' }),
                'onChange',
                'column4',
                expected,
                0,
            );
            expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                3,
                screenId,
                tableElementId,
                'onChange',
                'column4',
                expected,
                0,
            );

            const updated = utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any;
            expect(updated.column4).toEqual('option2');
            expect(updated.__dirtyColumns).toMatchObject(new Set(['column4']));
            expect(utils.getCellContent(0, 4)).toBe('option2');
        });

        it('should be able to select from dropdown without hitting enter, just tabbing out', async () => {
            const utils = setup();
            const original = utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any;
            expect(original.column3).toEqual({
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            });
            const recordId = 'id1';
            const expected = {
                _id: 'id1',
                column1: 'Test Text Value 1',
                column2: 1,
                column3: {
                    _id: '_id1',
                    code: 'Test Code 1',
                    description: 'Test Description 1',
                },
                column4: 'option2',
                column5: 'product1',
                column6: '2020-07-14',
                column7: 50,
                column8: 'image',
                column9: true,
                column10: {
                    _id: '_id1',
                    code: 'Test Code 1',
                    description: 'Test Description 1',
                },
            };

            await utils.waitForFirstPaint();
            await utils.changeSelectBySelectingFromDropdownWithArrowsNoEnter(0, 4, 2);
            await waitFor(() => expect(utils.getCell(0, 4)!.textContent).toBe('option2'));

            await waitFor(() => {
                expect(setCellValueMock).toHaveBeenCalledWith({
                    columnId: 'column4',
                    level: undefined,
                    recordId,
                    value: 'option2',
                    isOrganicChange: true,
                });
                // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
                expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(2);
                expect(events.triggerFieldEvent).toHaveBeenCalledTimes(3);
                expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                    1,
                    'TestPage',
                    'tableElementId',
                    'onAllDataLoaded',
                );
            });

            expect(events.triggerNestedFieldEvent).toHaveBeenNthCalledWith(
                2,
                screenId,
                tableElementId,
                expect.objectContaining({ bind: 'column4' }),
                'onChange',
                'column4',
                expected,
                0,
            );
            expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                3,
                screenId,
                tableElementId,
                'onChange',
                'column4',
                expected,
                0,
            );

            const updated = utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any;
            expect(updated.column4).toEqual('option2');
            expect(updated.__dirtyColumns).toMatchObject(new Set(['column4']));
            expect(utils.getCellContent(0, 4)).toBe('option2');
        });

        it('should be able to clear value', async () => {
            const utils = setup();
            const original = utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any;
            expect(original.column4).toEqual('option1');
            const recordId = 'id1';
            const expected = {
                _id: 'id1',
                column1: 'Test Text Value 1',
                column2: 1,
                column3: {
                    _id: '_id1',
                    code: 'Test Code 1',
                    description: 'Test Description 1',
                },
                column4: null,
                column5: 'product1',
                column6: '2020-07-14',
                column7: 50,
                column8: 'image',
                column9: true,
                column10: {
                    _id: '_id1',
                    code: 'Test Code 1',
                    description: 'Test Description 1',
                },
            };

            await utils.waitForFirstPaint();
            await utils.changeReferenceByTyping(0, 4, '');
            await waitFor(() => {
                expect(setCellValueMock).toHaveBeenCalledWith({
                    columnId: 'column4',
                    level: undefined,
                    recordId,
                    value: null,
                    isOrganicChange: true,
                });
                // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
                expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(2);
                expect(events.triggerFieldEvent).toHaveBeenCalledTimes(3);
                expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                    1,
                    'TestPage',
                    'tableElementId',
                    'onAllDataLoaded',
                );
            });

            expect(events.triggerNestedFieldEvent).toHaveBeenNthCalledWith(
                2,
                screenId,
                tableElementId,
                {
                    bind: 'column4',
                    title: 'Column 4',
                    optionType: '@sage/xtrem-show-case/Sample',
                    options: ['option1', 'option2'],
                },
                'onChange',
                'column4',
                expected,
                0,
            );
            expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                3,
                screenId,
                tableElementId,
                'onChange',
                'column4',
                expected,
                0,
            );

            const updated = utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any;
            expect(updated.column4).toEqual(null);
            expect(updated.__dirtyColumns).toMatchObject(new Set(['column4']));
            expect(utils.getCellContent(0, 4)).toBe('');
        });
    });

    it('should be able to call "map" on a nested field', async () => {
        const mapMock = jest.fn();
        const fieldProperties = set(defaultFieldProperties(), 'columns[4].properties.map', mapMock);
        const utils = setup({ fieldProperties });
        await utils.waitForFirstPaint();
        expect(mapMock).toHaveBeenCalledTimes(2);
        expect(mapMock).toHaveBeenNthCalledWith(1, 'product1', {
            _id: 'id1',
            column1: 'Test Text Value 1',
            column2: 1,
            column3: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
            column4: 'option1',
            column5: 'product1',
            column6: '2020-07-14',
            column7: 50,
            column8: 'image',
            column9: true,
            column10: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
        });
        expect(mapMock).toHaveBeenNthCalledWith(2, 'product2', {
            _id: 'id2',
            column1: 'Test Text Value 2',
            column2: 2,
            column3: {
                _id: '_id2',
                code: 'Test Code 2',
                description: 'Test Description 2',
            },
            column4: 'option2',
            column5: 'product2',
            column6: '2020-07-15',
            column7: 60,
            column8: 'image2',
            column9: false,
            column10: {
                _id: '_id2',
                code: 'Test Code 2',
                description: 'Test Description 2',
            },
        });
    });

    it('should not be able to click a "link" cell triggering an event listener', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        utils.doubleClickCell(0, 5);
        await waitFor(() => {
            expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(0);
            expect(events.triggerFieldEvent).toHaveBeenCalledTimes(1);
            expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
                1,
                'TestPage',
                'tableElementId',
                'onAllDataLoaded',
            );
        });
    });

    it('should be able to click a "link" cell\'s link tag triggering an event listener', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        const linkElement = utils.getCell(0, 5)!.querySelector('a')!;
        fireEvent.click(linkElement);
        await waitFor(() => {
            expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(1);
            expect(events.triggerFieldEvent).toHaveBeenCalledTimes(1);
        });

        const expected = {
            _id: 'id1',
            column1: 'Test Text Value 1',
            column2: 1,
            column3: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
            column4: 'option1',
            column5: 'product1',
            column6: '2020-07-14',
            column7: 50,
            column8: 'image',
            column9: true,
            column10: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
        };
        expect(events.triggerNestedFieldEvent).toHaveBeenNthCalledWith(
            1,
            screenId,
            tableElementId,
            expect.objectContaining({ bind: 'column5', map: expect.any(Function), title: 'Column 5' }),
            'onClick',
            'id1',
            expected,
        );
        expect(utils.getCellContent(0, 5)).toBe('http://product1.sage.com');
    });

    it('should be able to change a "date" cell triggering actions & event listeners', async () => {
        const utils = setup();
        const columnId = 'column6';
        const recordId = 'id1';
        await utils.waitForFirstPaint();
        await utils.pickDate(0, 6, 17);
        await waitFor(() => {
            expect(setCellValueMock).toHaveBeenCalledWith({
                columnId,
                level: undefined,
                recordId,
                value: '2020-07-17',
                isOrganicChange: true,
            });
            // the first time "triggerNestedFieldEvent" gets called when cell is double clicked
            expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(2);
            expect(events.triggerFieldEvent).toHaveBeenCalledTimes(3);
        });

        const expected = {
            _id: 'id1',
            column1: 'Test Text Value 1',
            column2: 1,
            column3: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
            column4: 'option1',
            column5: 'product1',
            column6: '2020-07-17',
            column7: 50,
            column8: 'image',
            column9: true,
            column10: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
        };
        expect(events.triggerNestedFieldEvent).toHaveBeenNthCalledWith(
            2,
            screenId,
            tableElementId,
            { bind: 'column6', title: 'Column 6' },
            'onChange',
            columnId,
            expected,
            0,
        );
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
            3,
            screenId,
            tableElementId,
            'onRowClick',
            'id1',
            expected,
            false,
        );
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(
            4,
            screenId,
            tableElementId,
            'onChange',
            columnId,
            expected,
            0,
        );

        expect(
            (utils.fieldValue.getRawRecord({ id: 'id1', cleanMetadata: false }) as any).__dirtyColumns,
        ).toMatchObject(new Set([columnId]));
        await waitFor(() => {
            expect(utils.getCellContent(0, 6)).toBe('17/07/2020');
        });
    });

    it('should "progress" cell trigger click', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(0);
        expect(events.triggerFieldEvent).toHaveBeenCalledTimes(1);
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(1, 'TestPage', 'tableElementId', 'onAllDataLoaded');
        utils.doubleClickCell(0, 7);
        await waitFor(() => {
            expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(1);
            expect(events.triggerFieldEvent).toHaveBeenCalledTimes(2);
        });

        const expected = {
            _id: 'id1',
            column1: 'Test Text Value 1',
            column2: 1,
            column3: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
            column4: 'option1',
            column5: 'product1',
            column6: '2020-07-14',
            column7: 50,
            column8: 'image',
            column9: true,
            column10: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
        };

        expect(events.triggerNestedFieldEvent).toHaveBeenCalledWith(
            screenId,
            tableElementId,
            { bind: 'column7', title: 'Column 7' },
            'onClick',
            'id1',
            expected,
            0,
        );

        expect(events.triggerFieldEvent).toHaveBeenCalledWith(
            screenId,
            tableElementId,
            'onRowClick',
            'id1',
            expected,
            false,
        );
    });

    it('should "image" cell trigger click', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(0);
        expect(events.triggerFieldEvent).toHaveBeenCalledTimes(1);
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(1, 'TestPage', 'tableElementId', 'onAllDataLoaded');
        utils.doubleClickCell(0, 8);
        await waitFor(() => {
            expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(1);
            expect(events.triggerFieldEvent).toHaveBeenCalledTimes(2);
        });

        const expected = {
            _id: 'id1',
            column1: 'Test Text Value 1',
            column2: 1,
            column3: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
            column4: 'option1',
            column5: 'product1',
            column6: '2020-07-14',
            column7: 50,
            column8: 'image',
            column9: true,
            column10: {
                _id: '_id1',
                code: 'Test Code 1',
                description: 'Test Description 1',
            },
        };

        expect(events.triggerNestedFieldEvent).toHaveBeenCalledWith(
            screenId,
            tableElementId,
            { bind: 'column8', title: 'Column 8' },
            'onClick',
            'id1',
            expected,
            0,
        );

        expect(events.triggerFieldEvent).toHaveBeenCalledWith(
            screenId,
            tableElementId,
            'onRowClick',
            'id1',
            expected,
            false,
        );
    });

    it('should "checkbox" cell trigger onChange', async () => {
        const utils = setup();
        await utils.waitForFirstPaint();
        expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(0);
        expect(events.triggerFieldEvent).toHaveBeenCalledTimes(1);
        expect(events.triggerFieldEvent).toHaveBeenNthCalledWith(1, 'TestPage', 'tableElementId', 'onAllDataLoaded');

        utils.checkBoxToggle(0, 9);
        await waitFor(() => {
            expect(setCellValueMock).toHaveBeenCalledWith({
                columnId: 'column9',
                level: undefined,
                recordId: 'id1',
                value: false,
                isOrganicChange: true,
            });
        });
    });

    it('should not trigger on row click when the field is disabled', async () => {
        const utils = setup({ fieldProperties: { ...defaultFieldProperties(), isDisabled: true } });
        await utils.waitForFirstPaint();
        expect(events.triggerHandledEvent).toHaveBeenCalledTimes(0);
        expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(0);
        utils.doubleClickCell(0, 9);
        utils.doubleClickCell(0, 8);
        utils.doubleClickCell(0, 7);
        utils.doubleClickCell(0, 5);
        utils.doubleClickCell(0, 4);
        utils.doubleClickCell(0, 3);
        utils.doubleClickCell(0, 2);
        utils.doubleClickCell(0, 1);
        utils.doubleClickCell(0, 0);
        await waitFor(
            () => {
                expect(events.triggerHandledEvent).toHaveBeenCalledTimes(0);
                expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(0);
            },
            { timeout: 500 },
        );
    });

    it('should not trigger on row click when the field is parent container is disabled', async () => {
        const utils = setup({ isParentDisabled: true });
        await utils.waitForFirstPaint();
        expect(events.triggerHandledEvent).toHaveBeenCalledTimes(0);
        expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(0);
        utils.doubleClickCell(0, 10);
        utils.doubleClickCell(0, 9);
        utils.doubleClickCell(0, 8);
        utils.doubleClickCell(0, 7);
        utils.doubleClickCell(0, 5);
        utils.doubleClickCell(0, 4);
        utils.doubleClickCell(0, 3);
        utils.doubleClickCell(0, 2);
        utils.doubleClickCell(0, 1);
        utils.doubleClickCell(0, 0);
        await waitFor(
            () => {
                expect(events.triggerHandledEvent).toHaveBeenCalledTimes(0);
                expect(events.triggerNestedFieldEvent).toHaveBeenCalledTimes(0);
            },
            { timeout: 500 },
        );
    });

    // TODO: Fix to use table views XT-44060
    // it('should hide columns from hiddenColumns', async () => {
    //     const utils = setup({
    //         fieldProperties: {
    //             ...defaultFieldProperties(),
    //             hiddenColumns: ['column2', 'column4'],
    //         } as any,
    //         fieldValue: defaultFieldValue(),
    //     });
    //     await utils.waitForFirstPaint(defaultFieldProperties().columns.length - 2);
    //     expect(utils.getAllHeadersTextContent()).toEqual([
    //         'Column 1',
    //         'Column 3',
    //         'Column 5',
    //         'Column 6',
    //         'Column 7',
    //         'Column 8',
    //         'Column 9',
    //         'Column 10',
    //     ]);
    // });

    it('Should fetches default values when start editing a phantom row cell', async () => {
        const mockFetchDefault = jest
            .spyOn(graphqlService, 'fetchNestedDefaultValues')
            .mockImplementation(async () => ({}));

        const fieldProperties = defaultFieldProperties();
        fieldProperties.canAddNewLine = true;
        const utils = setup({ fieldProperties });
        await utils.waitForFirstPaint(fieldProperties.columns!.length + 1); // +1 for the pinned column
        const phantomRow = await utils.getPhantomRow();
        expect(phantomRow).toBeInTheDocument();
        await userEvent.click(phantomRow!.children[0]);
        expect(mockFetchDefault).toHaveBeenCalled();
    });

    describe('sorting', () => {
        let sortResultSetSpy: jest.SpyInstance<any, any> | null = null;

        beforeEach(() => {
            sortResultSetSpy = jest.spyOn(collectionDataUtils, 'sortResultSet');
        });

        afterEach(() => {
            sortResultSetSpy!.mockClear();
        });

        it('should not sort if canFilter is disabled', async () => {
            const fieldProperties = set(defaultFieldProperties(false), 'columns[0].properties.canFilter', true);
            const utils = setup({ fieldProperties });
            await utils.waitForFirstPaint();
            sortResultSetSpy!.mockClear();
            utils.clickHeader(1);
            await waitFor(() => {
                expect(sortResultSetSpy).toHaveBeenCalledTimes(0);
            });
        });

        it('should sort by column1', async () => {
            const fieldProperties = set(defaultFieldProperties(), 'columns[0].properties.canFilter', true);
            const utils = setup({ fieldProperties });
            await utils.waitForFirstPaint();
            sortResultSetSpy!.mockClear();
            utils.clickHeader(1);
            await waitFor(() => {
                expect(sortResultSetSpy).toHaveBeenCalledTimes(2);
            });
            expect(sortResultSetSpy!.mock.calls[1][0].orderBy).toEqual({ column1: 1 });
        });

        it('should sort by column1 desc', async () => {
            const fieldProperties = set(defaultFieldProperties(), 'columns[0].properties.canFilter', true);
            const utils = setup({ fieldProperties });
            await utils.waitForFirstPaint();
            sortResultSetSpy!.mockClear();
            utils.clickHeader(1);
            await waitFor(() => {
                expect(sortResultSetSpy).toHaveBeenCalledTimes(2);
            });
            utils.clickHeader(1);
            await waitFor(() => {
                expect(sortResultSetSpy).toHaveBeenCalledTimes(4);
            });
            expect(sortResultSetSpy!.mock.calls[3][0].orderBy).toEqual({ column1: -1 });
        });

        it('should sort by column2 and remove previous sorted by column1', async () => {
            const fieldProperties = set(
                set(defaultFieldProperties(), 'columns[0].properties.canFilter', true),
                'columns[1].properties.canFilter',
                true,
            );
            const utils = setup({ fieldProperties });
            await utils.waitForFirstPaint();
            sortResultSetSpy!.mockClear();
            utils.clickHeader(1);
            await waitFor(() => {
                expect(sortResultSetSpy).toHaveBeenCalledTimes(2);
            });
            utils.clickHeader(2);
            await waitFor(() => {
                expect(sortResultSetSpy).toHaveBeenCalledTimes(4);
            });
            expect(sortResultSetSpy!.mock.calls[3][0].orderBy).toEqual({ column2: 1 });
        });

        it('should sort by column1 and column2', async () => {
            const fieldProperties = set(
                set(defaultFieldProperties(), 'columns[0].properties.canFilter', true),
                'columns[1].properties.canFilter',
                true,
            );
            const utils = setup({ fieldProperties });
            await utils.waitForFirstPaint();
            sortResultSetSpy!.mockClear();
            utils.clickHeader(1);
            await waitFor(() => {
                expect(sortResultSetSpy).toHaveBeenCalledTimes(2);
            });
            utils.clickHeader(2, { shiftKey: true });
            await waitFor(() => {
                expect(sortResultSetSpy).toHaveBeenCalledTimes(4);
            });
            expect(sortResultSetSpy!.mock.calls[3][0].orderBy).toEqual({ column1: 1, column2: 1 });
        });

        it('should not sort by column5 when column5 binds to unknown property', async () => {
            const fieldProperties = set(
                set(defaultFieldProperties(), 'columns[4].properties.bind', 'noColumn'),
                'columns[4].properties.canFilter',
                true,
            );
            const utils = setup({ fieldProperties });
            await utils.waitForFirstPaint();
            sortResultSetSpy!.mockClear();
            utils.clickHeader(5);
            await waitFor(() => {
                expect(sortResultSetSpy).not.toHaveBeenCalled();
            });
        });
    });

    describe('exporting main list', () => {
        let exportTableMock: jest.SpyInstance<Promise<void>, any>;
        let showToastMock: jest.SpyInstance<void, any>;

        beforeEach(() => {
            exportTableMock = jest.spyOn(tableExportService, 'exportTableData').mockResolvedValue(undefined);
            showToastMock = jest.spyOn(toastService, 'showToast').mockReturnValue();
        });

        afterEach(() => {
            exportTableMock.mockRestore();
            showToastMock.mockRestore();
        });

        it('should display an info toast once the export starts', async () => {
            const utils = setup({
                elementId: navigationPanelId,
                fieldProperties: { ...defaultFieldProperties(), canExport: true },
            });
            fireEvent.click(utils.queryByTestId('e-table-export')!);
            await waitFor(() => {
                const exportCsvButton = utils.queryByTestId('e-table-export-csv');
                expect(exportCsvButton).not.toBeNull();
                expect(exportTableMock).not.toHaveBeenCalled();
                expect(showToastMock).not.toHaveBeenCalled();
                fireEvent.click(exportCsvButton!);
            });

            await waitFor(() => {
                expect(exportTableMock).toHaveBeenCalled();
                expect(showToastMock).toHaveBeenCalledWith('Export started.', { type: 'info' });
            });
        });

        it('should display an error toast if the export process fails to start', async () => {
            exportTableMock.mockRejectedValue('Ooops some error happened.');
            const utils = setup({
                elementId: navigationPanelId,
                fieldProperties: { ...defaultFieldProperties(), canExport: true },
            });
            fireEvent.click(utils.queryByTestId('e-table-export')!);
            await waitFor(() => {
                const exportCsvButton = utils.queryByTestId('e-table-export-csv');
                expect(exportCsvButton).not.toBeNull();
                expect(exportTableMock).not.toHaveBeenCalled();
                expect(showToastMock).not.toHaveBeenCalled();
                fireEvent.click(exportCsvButton!);
            });

            await waitFor(() => {
                expect(exportTableMock).toHaveBeenCalled();
                expect(showToastMock).toHaveBeenCalledWith('Failed to export main list content', { type: 'error' });
            });
        });
    });

    describe('DesktopTableHeaderComponent and hasAddItemsButton', () => {
        beforeEach(() => {
            jest.spyOn(graphqlService, 'fetchNestedDefaultValues').mockResolvedValue({
                nestedDefaults: {
                    someOtherField: '2',
                    anyField: 'default value server',
                    id: '-1',
                },
            });
        });

        it('should render DesktopTableHeaderComponent with hasAddItemsButton true when addItemActions are not hidden', async () => {
            const fieldProperties = {
                ...defaultFieldProperties(),
                addItemActions: [
                    { id: 'action-1', title: 'Action 1', isHidden: false },
                    { id: 'action-2', title: 'Action 2', isHidden: false },
                ] as PageActionControlObject[],
                canAddNewLine: false,
                isPhantomRowDisabled: true,
            };

            const utils = setup({ fieldProperties });
            await utils.waitForFirstPaint();

            // Check that DesktopTableHeaderComponent is rendered
            const headerComponent = utils.container.querySelector('.e-field-header-wrapper');
            expect(headerComponent).toBeInTheDocument();

            // Check that the add new row button is rendered (indicating hasAddItemsButton is true)
            const addNewRowButton = utils.container.querySelector(
                '[data-testid="e-table-button-add-new-row-multi-action"]',
            );
            expect(addNewRowButton).toBeInTheDocument();
        });

        it('should render DesktopTableHeaderComponent with hasAddItemsButton false when all addItemActions are hidden', async () => {
            const fieldProperties = {
                ...defaultFieldProperties(),
                addItemActions: [
                    { id: 'action-1', title: 'Action 1', isHidden: true },
                    { id: 'action-2', title: 'Action 2', isHidden: true },
                ] as PageActionControlObject[],
                canAddNewLine: false,
                isPhantomRowDisabled: true,
            };

            const utils = setup({ fieldProperties });
            await utils.waitForFirstPaint();

            // Check that DesktopTableHeaderComponent is rendered
            const headerComponent = utils.container.querySelector('.e-field-header-wrapper');
            expect(headerComponent).toBeInTheDocument();

            // Check that the add new row button is NOT rendered (indicating hasAddItemsButton is false)
            const addNewRowButton = utils.container.querySelector(
                '[data-testid="e-table-button-add-new-row-multi-action"]',
            );
            expect(addNewRowButton).not.toBeInTheDocument();
        });

        it('should render DesktopTableHeaderComponent with hasAddItemsButton true when some addItemActions are not hidden', async () => {
            const fieldProperties = {
                ...defaultFieldProperties(),
                addItemActions: [
                    { id: 'action-1', title: 'Action 1', isHidden: true },
                    { id: 'action-2', title: 'Action 2', isHidden: false },
                    { id: 'action-3', title: 'Action 3', isHidden: true },
                ] as PageActionControlObject[],
                canAddNewLine: false,
                isPhantomRowDisabled: true,
            };

            const utils = setup({ fieldProperties });
            await utils.waitForFirstPaint();

            // Check that DesktopTableHeaderComponent is rendered
            const headerComponent = utils.container.querySelector('.e-field-header-wrapper');
            expect(headerComponent).toBeInTheDocument();

            // Check that the add new row button is rendered (indicating hasAddItemsButton is true)
            const addNewRowButton = utils.container.querySelector(
                '[data-testid="e-table-button-add-new-row-multi-action"]',
            );
            expect(addNewRowButton).toBeInTheDocument();
        });

        it('should render DesktopTableHeaderComponent with hasAddItemsButton true when canAddNewLine is true and isPhantomRowDisabled is false', async () => {
            const fieldProperties = {
                ...defaultFieldProperties(),
                addItemActions: [
                    { id: 'action-1', title: 'Action 1', isHidden: true },
                    { id: 'action-2', title: 'Action 2', isHidden: true },
                ] as PageActionControlObject[],
                canAddNewLine: true,
                isPhantomRowDisabled: false,
            };

            const utils = setup({ fieldProperties });
            await utils.waitForFirstPaint(11);

            // Check that DesktopTableHeaderComponent is rendered
            const headerComponent = utils.container.querySelector('.e-field-header-wrapper');
            expect(headerComponent).toBeInTheDocument();

            // Check that the add new row button is rendered (indicating hasAddItemsButton is true)
            const addNewRowButton = utils.container.querySelector('[data-testid="e-table-button-add-new-row-phantom"]');
            expect(addNewRowButton).toBeInTheDocument();
        });

        it('should render DesktopTableHeaderComponent with hasAddItemsButton false when canAddNewLine is false, isPhantomRowDisabled is true, and all addItemActions are hidden', async () => {
            const fieldProperties = {
                ...defaultFieldProperties(),
                addItemActions: [
                    { id: 'action-1', title: 'Action 1', isHidden: true },
                    { id: 'action-2', title: 'Action 2', isHidden: true },
                ] as PageActionControlObject[],
                canAddNewLine: false,
                isPhantomRowDisabled: true,
            };

            const utils = setup({ fieldProperties });
            await utils.waitForFirstPaint();

            // Check that DesktopTableHeaderComponent is rendered
            const headerComponent = utils.container.querySelector('.e-field-header-wrapper');
            expect(headerComponent).toBeInTheDocument();

            // Check that no add new row button is rendered (indicating hasAddItemsButton is false)
            const addNewRowButton = utils.container.querySelector(
                '[data-testid="e-table-button-add-new-row-multi-action"]',
            );
            const phantomRowButton = utils.container.querySelector(
                '[data-testid="e-table-button-add-new-row-phantom"]',
            );
            expect(addNewRowButton).not.toBeInTheDocument();
            expect(phantomRowButton).not.toBeInTheDocument();
        });

        it('should render DesktopTableHeaderComponent with hasAddItemsButton true when addItemActions is undefined but canAddNewLine is true', async () => {
            const fieldProperties = {
                ...defaultFieldProperties(),
                addItemActions: undefined,
                canAddNewLine: true,
                isPhantomRowDisabled: false,
            };

            const utils = setup({ fieldProperties });
            await utils.waitForFirstPaint(11);

            // Check that DesktopTableHeaderComponent is rendered
            const headerComponent = utils.container.querySelector('.e-field-header-wrapper');
            expect(headerComponent).toBeInTheDocument();

            // Check that the add new row button is rendered (indicating hasAddItemsButton is true)
            const addNewRowButton = utils.container.querySelector('[data-testid="e-table-button-add-new-row-phantom"]');
            expect(addNewRowButton).toBeInTheDocument();
        });
    });
});
