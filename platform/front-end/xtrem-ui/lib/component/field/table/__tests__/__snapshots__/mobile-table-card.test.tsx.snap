// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Mobile Table with Table Card View Snapshots should render with default properties 1`] = `
Array [
  .c1 {
  height: inherit;
  min-width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  max-width: 40px;
  min-width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: 0px;
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div
    data-label="Title #1"
    data-testid="e-card"
  >
    <div
      class="e-card e-card-has-image e-context-table"
    >
      <div
        class="e-card-body"
      >
        <span
          class="e-card-image"
        >
          <div
            class="e-field-nested e-field-nested--1-image"
          >
            <div
              class="e-field e-image-field e-title-hidden e-context-table e-read-only"
              data-nested="true"
              data-testid="e-image-field e-field-label-image e-field-bind-image"
            >
              <div
                class="e-image-field-content-wrapper"
              >
                <div>
                  <div
                    class="e-portrait"
                  >
                    <div
                      class="c0"
                      data-component="portrait"
                      shape="square"
                    >
                      <img
                        alt="Image"
                        class="c1"
                        data-element="user-image"
                        src="data:image;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=="
                      />
                    </div>
                  </div>
                </div>
              </div>
              <span
                class="common-input__help-text"
                data-element="help"
                data-testid="e-field-helper-text"
              >
                 
              </span>
            </div>
          </div>
        </span>
        <div
          class="e-card-content"
        >
          <div
            class="e-card-row-container"
          >
            <div
              class="e-card-row e-card-row-line-title"
            >
              <span
                class="e-card-title"
              >
                <div
                  class="e-field-nested e-field-nested--1-title"
                >
                  <div
                    class="e-field e-text-field e-context-table"
                    data-label="Title"
                    data-nested="true"
                    data-testid="e-text-field e-field-label-title e-field-bind-title"
                  >
                    <div>
                      <div>
                        <span
                          class="e-field-read-only e-field-nested-no-input"
                          data-testid="e-field-value"
                        >
                          Title #1
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
              <span
                class="e-card-row-right"
              >
                <div
                  class="e-field-nested e-field-nested--1-id"
                >
                  <div
                    class="e-field e-text-field e-context-table e-read-only"
                    data-label="Id"
                    data-nested="true"
                    data-testid="e-text-field e-field-label-id e-field-bind-id"
                  >
                    <div>
                      <div>
                        <span
                          class="e-field-read-only e-field-nested-no-input"
                          data-testid="e-field-value"
                        >
                          1
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div
              class="e-card-row e-card-row-line-2"
            >
              <div
                class="e-card-line-2"
              >
                <div
                  class="e-field-nested e-field-nested--1-price"
                >
                  <div
                    class="e-field e-numeric-field e-context-table e-read-only"
                    data-label="Price"
                    data-nested="true"
                    data-testid="e-numeric-field e-field-label-price e-field-bind-price"
                  >
                    <div>
                      <div>
                        <span
                          class="e-field-read-only e-field-nested-no-input"
                          data-testid="e-field-value"
                        >
                          99
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <span
                class="e-card-row-right"
              >
                <div
                  class="e-field-nested e-field-nested--1-date"
                >
                  <div
                    class="e-field e-date-field e-context-table e-read-only"
                    data-label="Date"
                    data-nested="true"
                    data-testid="e-date-field e-field-label-date e-field-bind-date"
                  >
                    <div>
                      <div>
                        <span
                          class="e-field-read-only e-field-nested-no-input"
                          data-testid="e-field-value"
                        >
                          01/01/2021
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div
              class="e-card-row e-card-row-line-3"
            />
            <div
              class="e-card-row e-card-row-line-4"
            />
            <div
              class="e-card-row e-card-row-line-5"
            />
          </div>
        </div>
      </div>
    </div>
  </div>,
  .c1 {
  font-weight: 500;
  font-size: var(--fontSizes300);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  white-space: nowrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: inherit;
  width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  min-width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: var(--borderRadiusCircle);
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div
    data-label="Title #1"
    data-testid="e-card"
  >
    <div
      class="e-card e-card-has-image e-context-table"
    >
      <div
        class="e-card-body"
      >
        <span
          class="e-card-image"
        >
          <div
            class="e-field-nested e-field-nested--2-image"
          >
            <div
              class="e-field e-image-field e-title-hidden e-context-table e-read-only"
              data-nested="true"
              data-testid="e-image-field e-field-label-image e-field-bind-image"
            >
              <div
                class="e-image-field-content-wrapper"
              >
                <div
                  class="e-image-field-placeholder"
                  data-testid="e-image-field-placeholder"
                >
                  <div
                    class="e-portrait"
                  >
                    <div
                      class="c0"
                      data-component="portrait"
                      shape="circle"
                    >
                      <div
                        class="c1"
                        data-element="initials"
                      >
                        T1
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <span
                class="common-input__help-text"
                data-element="help"
                data-testid="e-field-helper-text"
              >
                 
              </span>
            </div>
          </div>
        </span>
        <div
          class="e-card-content"
        >
          <div
            class="e-card-row-container"
          >
            <div
              class="e-card-row e-card-row-line-title"
            >
              <span
                class="e-card-title"
              >
                <div
                  class="e-field-nested e-field-nested--2-title"
                >
                  <div
                    class="e-field e-text-field e-context-table"
                    data-label="Title"
                    data-nested="true"
                    data-testid="e-text-field e-field-label-title e-field-bind-title"
                  >
                    <div>
                      <div>
                        <span
                          class="e-field-read-only e-field-nested-no-input"
                          data-testid="e-field-value"
                        >
                          Title #1
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
              <span
                class="e-card-row-right"
              >
                <div
                  class="e-field-nested e-field-nested--2-id"
                >
                  <div
                    class="e-field e-text-field e-context-table e-read-only"
                    data-label="Id"
                    data-nested="true"
                    data-testid="e-text-field e-field-label-id e-field-bind-id"
                  >
                    <div>
                      <div>
                        <span
                          class="e-field-read-only e-field-nested-no-input"
                          data-testid="e-field-value"
                        >
                          2
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div
              class="e-card-row e-card-row-line-2"
            >
              <div
                class="e-card-line-2"
              >
                <div
                  class="e-field-nested e-field-nested--2-price"
                >
                  <div
                    class="e-field e-numeric-field e-context-table e-read-only"
                    data-label="Price"
                    data-nested="true"
                    data-testid="e-numeric-field e-field-label-price e-field-bind-price"
                  >
                    <div>
                      <div>
                        <span
                          class="e-field-read-only e-field-nested-no-input"
                          data-testid="e-field-value"
                        >
                          99
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <span
                class="e-card-row-right"
              >
                <div
                  class="e-field-nested e-field-nested--2-date"
                >
                  <div
                    class="e-field e-date-field e-context-table e-read-only"
                    data-label="Date"
                    data-nested="true"
                    data-testid="e-date-field e-field-label-date e-field-bind-date"
                  >
                    <div>
                      <div>
                        <span
                          class="e-field-read-only e-field-nested-no-input"
                          data-testid="e-field-value"
                        >
                          02/01/2021
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div
              class="e-card-row e-card-row-line-3"
            />
            <div
              class="e-card-row e-card-row-line-4"
            />
            <div
              class="e-card-row e-card-row-line-5"
            />
          </div>
        </div>
      </div>
    </div>
  </div>,
  .c1 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c1::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e93e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c2.c2 {
  color: inherit;
  height: inherit;
  min-width: inherit;
}

.c2.c2::before {
  font-size: 24px;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  min-width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: var(--borderRadiusCircle);
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div
    data-label="_"
    data-testid="e-card"
  >
    <div
      class="e-card e-card-has-image e-context-table"
    >
      <div
        class="e-card-body"
      >
        <span
          class="e-card-image"
        >
          <div
            class="e-field-nested e-field-nested--3-image"
          >
            <div
              class="e-field e-image-field e-title-hidden e-context-table e-read-only"
              data-nested="true"
              data-testid="e-image-field e-field-label-image e-field-bind-image"
            >
              <div
                class="e-image-field-content-wrapper"
              >
                <div
                  class="e-image-field-placeholder"
                  data-testid="e-image-field-placeholder"
                >
                  <div
                    class="e-portrait"
                  >
                    <div
                      class="c0"
                      data-component="portrait"
                      shape="circle"
                    >
                      <span
                        class="c1 c2"
                        data-component="icon"
                        data-element="image"
                        data-role="icon"
                        font-size="small"
                        type="image"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <span
                class="common-input__help-text"
                data-element="help"
                data-testid="e-field-helper-text"
              >
                 
              </span>
            </div>
          </div>
        </span>
        <div
          class="e-card-content"
        >
          <div
            class="e-card-row-container"
          >
            <div
              class="e-card-row e-card-row-line-title"
            >
              <span
                class="e-card-title"
              >
                <div
                  class="e-field-nested e-field-nested--3-title"
                >
                  <div
                    class="e-field e-text-field e-context-table"
                    data-label="Title"
                    data-nested="true"
                    data-testid="e-text-field e-field-label-title e-field-bind-title"
                  >
                    <div>
                      <div>
                        <span
                          class="e-field-read-only e-field-nested-no-input"
                          data-testid="e-field-value"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </span>
              <span
                class="e-card-row-right"
              >
                <div
                  class="e-field-nested e-field-nested--3-id"
                >
                  <div
                    class="e-field e-text-field e-context-table e-read-only"
                    data-label="Id"
                    data-nested="true"
                    data-testid="e-text-field e-field-label-id e-field-bind-id"
                  >
                    <div>
                      <div>
                        <span
                          class="e-field-read-only e-field-nested-no-input"
                          data-testid="e-field-value"
                        >
                          3
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div
              class="e-card-row e-card-row-line-2"
            >
              <div
                class="e-card-line-2"
              >
                <div
                  class="e-field-nested e-field-nested--3-price"
                >
                  <div
                    class="e-field e-numeric-field e-context-table e-read-only"
                    data-label="Price"
                    data-nested="true"
                    data-testid="e-numeric-field e-field-label-price e-field-bind-price"
                  >
                    <div>
                      <div>
                        <span
                          class="e-field-read-only e-field-nested-no-input"
                          data-testid="e-field-value"
                        >
                          99
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <span
                class="e-card-row-right"
              >
                <div
                  class="e-field-nested e-field-nested--3-date"
                >
                  <div
                    class="e-field e-date-field e-context-table e-read-only"
                    data-label="Date"
                    data-nested="true"
                    data-testid="e-date-field e-field-label-date e-field-bind-date"
                  >
                    <div>
                      <div>
                        <span
                          class="e-field-read-only e-field-nested-no-input"
                          data-testid="e-field-value"
                        >
                          03/01/2021
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div
              class="e-card-row e-card-row-line-3"
            />
            <div
              class="e-card-row e-card-row-line-4"
            />
            <div
              class="e-card-row e-card-row-line-5"
            />
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;
