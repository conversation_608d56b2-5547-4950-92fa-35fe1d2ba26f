import { <PERSON><PERSON>ey, objectKeys, type Dict } from '@sage/xtrem-shared';
import axios from 'axios';
import { isEqual } from 'lodash';
import React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import type { CollectionValue } from '../../../service/collection-data-service';
import { RecordActionType } from '../../../service/collection-data-types';
import type { Filter } from '../../../service/filter-service';
import { getScreenElement } from '../../../service/screen-base-definition';
import { SoundApi } from '../../../service/sound-api';
import { getPropertyScalarType } from '../../../utils/abstract-fields-utils';
import { takeLatest } from '../../../utils/async';
import { containsValueDeep } from '../../../utils/common-util';
import { xtremConsole } from '../../../utils/console';
import { triggerFieldEvent } from '../../../utils/events';
import { convertDeepBindToPathNotNull, getNestedFieldsFromProperties } from '../../../utils/nested-field-utils';
import { findDeepPropertyType } from '../../../utils/node-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { schemaTypeNameFromNodeName, splitValueToMergedValue } from '../../../utils/transformers';
import { navigationPanelId } from '../../container/navigation-panel/navigation-panel-types';
import type { OptionsMenuItem } from '../../container/page/page-types';
import type { CollectionItem } from '../../types';
import type { FilterManagerField } from '../../ui/filter/filter-manager';
import Button from 'carbon-react/esm/components/button';
import { getFieldTitle, isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import { FieldLabel } from '../carbon-utility-components';
import FieldActions from '../field-actions-component';
import { DesktopTableHeaderComponent } from '../../ui/table-shared/desktop-table-header';
import type { FilterModel, TableInternalComponentProps, TableViewLevel } from './table-component-types';
import { getActiveOptionsMenu } from '../../../utils/table-component-utils';
import { ConnectedTableOptionsMenu } from '../../ui/table-shared/table-options-menu';
import { TableSearchBox, getSearchTextboxId } from '../../ui/table-shared/table-search-box-component';
import { focusPageContent } from '../../../utils/dom';
import { mapAgGridFilterToXtremFilters } from '../../../utils/ag-grid/ag-grid-table-utils';
import type { FormattedNodeDetails } from '../../../service/metadata-types';
import { MobileTable, type UiMobileTableProps } from '../../ui/mobile-table/mobile-table-component';
import { localize } from '../../../service/i18n-service';
import { FiltersComponent, FiltersLabels } from '../../ui/filter/filters-component';
import type { ReduxResponsive } from '../../../redux/state';
import type { PageDefinition } from '../../../service/page-definition';

const NAVIGATION_PANEL_ELEMENT_ID_PREFIX = 'navigation-panel-card-';

export interface MobileTableComponentState {
    hasMorePages: boolean;
    isFetchingNextPage?: boolean;
    isLoading?: boolean;
    pageNumber: number;
    searchText?: string;
    valuesDisplayed: any[];
}

export interface MobileTableProps extends TableInternalComponentProps {
    browser: ReduxResponsive;
    hasFieldActions: boolean;
    isRefreshing?: boolean;
    nodeTypes: Dict<FormattedNodeDetails>;
    openSidebar: () => void;
    selectedOptionsMenuItem?: OptionsMenuItem;
}

export class UnconnectedMobileTableComponent extends React.Component<MobileTableProps, MobileTableComponentState> {
    private readonly takeLatest = takeLatest();

    constructor(props: MobileTableProps) {
        super(props);

        const pageSize = this.props.fieldProperties.pageSize || 20;
        this.subscribeToCollectionValueEvents(pageSize, this.props.value);

        this.state = {
            hasMorePages: false,
            isFetchingNextPage: false,
            pageNumber: 0,
            searchText: this.props.searchText || '',
            valuesDisplayed: [],
            isLoading: true,
        };
    }

    async componentDidMount(): Promise<void> {
        const pageSize = this.props.fieldProperties.pageSize || 20;
        const valuesDisplayed = this.props.value
            ? await this.props.value.getPage({
                  cleanMetadata: false,
                  tableFieldProperties: this.props.fieldProperties,
                  filters: this.toXtremFilters(this.props.filterModel),
                  pageNumber: 0,
                  pageSize,
                  searchText: this.props.searchText || '',
                  selectedOptionsMenuItem: this.props.selectedOptionsMenuItem,
              })
            : [];
        this.setState({
            hasMorePages: valuesDisplayed.length >= pageSize,
            valuesDisplayed: valuesDisplayed.slice(0, pageSize),
            isLoading: false,
        });
    }

    async componentDidUpdate(prevProps: Readonly<MobileTableProps>): Promise<void> {
        if (
            prevProps.value !== this.props.value ||
            !isEqual(prevProps.fieldProperties.filter, this.props.fieldProperties.filter) ||
            !isEqual(prevProps.filterModel, this.props.filterModel) ||
            !isEqual(prevProps.selectedOptionsMenuItem, this.props.selectedOptionsMenuItem)
        ) {
            const pageSize = this.props.fieldProperties.pageSize || 20;
            const valuesDisplayed = this.props.value
                ? await this.props.value.getPage({
                      cleanMetadata: false,
                      filters: this.toXtremFilters(this.props.filterModel),
                      tableFieldProperties: this.props.fieldProperties,
                      pageNumber: 0,
                      pageSize,
                      searchText: this.props.searchText || '',
                      selectedOptionsMenuItem: this.props.selectedOptionsMenuItem,
                  })
                : [];
            this.subscribeToCollectionValueEvents(pageSize, this.props.value);
            this.setState({
                hasMorePages: valuesDisplayed.length >= pageSize,
                pageNumber: 0,
                valuesDisplayed: valuesDisplayed.slice(0, pageSize),
                searchText: this.props.searchText || '',
            });
        }
    }

    private getCursor(): string | undefined {
        return [...this.state.valuesDisplayed].reverse()[0]?.__cursor;
    }

    private readonly isNavigationPanel = (): boolean => this.props.elementId === navigationPanelId;

    private readonly toXtremFilters = (filter: Filter<any>[] | FilterModel): Filter<any>[] => {
        return this.isNavigationPanel() ? objectKeys(filter).map(mapAgGridFilterToXtremFilters(filter)) : filter;
    };

    private readonly isDisabled = (): boolean =>
        this.props.isParentDisabled || isFieldDisabled(this.props.screenId, this.props.fieldProperties, null, null);

    private readonly isReadOnly = (): boolean =>
        isFieldReadOnly(this.props.screenId, this.props.fieldProperties, null, null);

    private readonly subscribeToCollectionValueEvents = (pageSize: number, collectionValue?: CollectionValue): void => {
        if (collectionValue) {
            collectionValue.subscribeForValueChanges(async (actionType: RecordActionType, rowValue: any) => {
                // Check if the row modified is displayed on the screen.
                const rowPosition = this.state.valuesDisplayed.findIndex(r => r._id === rowValue._id);
                const isRowOnScreen = rowPosition !== -1;
                if (actionType === RecordActionType.MODIFIED && isRowOnScreen) {
                    // If so, we update it's value in the component state.
                    this.setState(state => {
                        const valuesDisplayed = [...state.valuesDisplayed];
                        valuesDisplayed[rowPosition] = rowValue;

                        return { valuesDisplayed };
                    });
                } else if (actionType === RecordActionType.REMOVED && isRowOnScreen) {
                    // If the removed record was on the screen, we refetch all displayed records
                    const newValues = await collectionValue.getRecordWithCurrentQueryArguments({
                        tableFieldProperties: this.props.fieldProperties,
                        pageSize,
                        // eslint-disable-next-line react/no-access-state-in-setstate
                        pageNumber: this.state.pageNumber,
                        cursor: this.getCursor(),
                        cleanMetadata: false,
                    });
                    this.setState({ valuesDisplayed: newValues });
                } else if (actionType === RecordActionType.ADDED) {
                    // If a new record was added we need to refetch all records displayed so far.
                    /* eslint-disable react/no-access-state-in-setstate */
                    this.setState({ isLoading: true }, async () => {
                        const valuesDisplayed = await collectionValue.getPage({
                            tableFieldProperties: this.props.fieldProperties,
                            filters: this.toXtremFilters(this.props.filterModel),
                            orderBy: this.props.fieldProperties.orderBy,
                            pageSize: Math.max(this.state.valuesDisplayed.length, this.props.fieldProperties.pageSize!),
                            cursor: undefined,
                            cleanMetadata: false,
                        });
                        this.setState({ isLoading: false, valuesDisplayed, pageNumber: 0 });
                    });
                    /* eslint-enable react/no-access-state-in-setstate */
                }
            });
        }
    };

    private readonly onRowSelected = async (row: CollectionItem): Promise<void> => {
        if (this.props.setFieldProperties) {
            const elementId = this.props.elementId;
            const selectedRecords = [...(this.props.fieldProperties.selectedRecords || [])];
            const currentPosition = selectedRecords.indexOf(row._id);
            const selected = currentPosition >= 0;
            if (!selected) {
                selectedRecords.push(row._id);
                this.props.setFieldProperties(elementId, {
                    ...this.props.fieldProperties,
                    selectedRecords,
                });
                await triggerFieldEvent(
                    this.props.screenId,
                    this.props.elementId,
                    'onRowSelected',
                    row._id,
                    splitValueToMergedValue(row),
                );
            } else {
                selectedRecords.splice(currentPosition, 1);
                this.props.setFieldProperties(elementId, {
                    ...this.props.fieldProperties,
                    selectedRecords,
                });
                await triggerFieldEvent(
                    this.props.screenId,
                    this.props.elementId,
                    'onRowUnselected',
                    row._id,
                    splitValueToMergedValue(row),
                );
            }
        }
    };

    private readonly canCreateNewItem = (collection: any[]): boolean => {
        return (
            !!this.props.fieldProperties.isNewEnabled &&
            !!this.props.fieldProperties.valueField &&
            !!this.state.searchText?.length &&
            collection.every(item => {
                return item[`${this.props.fieldProperties.valueField}`] !== this.state.searchText;
            })
        );
    };

    private readonly getCreateNewItem = (): any => ({
        _id: '-1',
        _isNewRecord: true,
        [`${this.props.fieldProperties.valueField}`]: this.state.searchText,
    });

    private readonly onRowClick =
        (item: any) =>
        (_item: any, isModifierKeyPushed = false): void => {
            if (this.props.fieldProperties.isNewEnabled && item._isNewRecord) {
                if (typeof this.props.onRowClick === 'function') {
                    this.props.onRowClick(item._id, splitValueToMergedValue(item), isModifierKeyPushed)();
                }
            } else if (typeof this.props.onRowClick === 'function') {
                this.props.onRowClick(item._id, undefined, isModifierKeyPushed)();
            }
        };

    private readonly refreshTableContent = async (): Promise<void> => {
        const pageSize = this.props.fieldProperties.pageSize || 20;

        this.setState({ isLoading: true });

        const { cancel, token } = axios.CancelToken.source();

        try {
            const params = {
                tableFieldProperties: this.props.fieldProperties,
                filters: this.toXtremFilters(this.props.filterModel),
                orderBy: this.props.fieldProperties.orderBy,
                pageSize,
                searchText: this.state.searchText,
                axiosCancelToken: token,
                cursor: undefined,
                cleanMetadata: false,
                selectedOptionsMenuItem: this.props.selectedOptionsMenuItem,
            };
            const valuesDisplayed: any[] = await this.takeLatest(this.props.value.getPage(params));
            const newState = {
                valuesDisplayed,
                isLoading: false,
                hasMorePages: valuesDisplayed.length === pageSize,
                pageNumber: 0,
            };
            this.setState(newState);
            const soundApi = new SoundApi(
                resolveByValue({
                    screenId: this.props.screenId,
                    propertyValue: this.props.fieldProperties.isSoundDisabled,
                    skipHexFormat: true,
                    fieldValue: null,
                    rowValue: null,
                }),
            );
            if (
                valuesDisplayed.length === 1 &&
                (this.props.isAutoSelectEnabled || this.props.fieldProperties.isAutoSelectEnabled) &&
                containsValueDeep(valuesDisplayed[0], this.state.searchText)
            ) {
                await soundApi.success();
                this.onRowClick(valuesDisplayed[0])(valuesDisplayed[0], false);
            }
            if (
                !valuesDisplayed.length &&
                (this.props.isAutoSelectEnabled || this.props.fieldProperties.isAutoSelectEnabled)
            ) {
                await soundApi.error();
            }
        } catch (err) {
            if (err.name === 'AbortError') {
                // propagate outdated call cancellations to axios
                cancel();
            } else {
                xtremConsole.error(err);
            }
        }
    };

    private readonly onFiltersChanged = async (filters: Filter[] | FilterModel): Promise<void> => {
        if (this.props.setTableViewFilter) {
            this.props.setTableViewFilter(0, filters);
        }
    };

    private readonly getFilterableFields = (): FilterManagerField[] => {
        const node = this.props.fieldProperties.node;
        return (this.props.fieldProperties.columns || []).map(nestedField => {
            const propertyType =
                findDeepPropertyType(
                    schemaTypeNameFromNodeName(node),
                    nestedField.properties.bind,
                    this.props.nodeTypes,
                    true,
                )?.type || FieldKey.Text;

            const fieldType = nestedField.type;
            return {
                type: fieldType,
                properties: {
                    ...nestedField.properties,
                    bind: convertDeepBindToPathNotNull(nestedField.properties.bind),
                    title: getFieldTitle(
                        this.props.screenId,
                        { title: 'title' in nestedField.properties ? nestedField.properties.title : undefined },
                        null,
                    ),
                    type: getPropertyScalarType(
                        this.props.nodeTypes,
                        propertyType,
                        nestedField.type,
                        (nestedField.properties as any).valueField,
                    ),
                },
            };
        });
    };

    private readonly fetchNextPage = (): void => {
        const pageSize = this.props.fieldProperties.pageSize || 20;

        /* eslint-disable react/no-access-state-in-setstate */
        this.setState(
            {
                isFetchingNextPage: true,
                pageNumber: this.state.pageNumber + 1,
            },
            async () => {
                const newValues = await this.props.value.getPageWithCurrentQueryArguments({
                    tableFieldProperties: this.props.fieldProperties,
                    pageSize,
                    pageNumber: this.state.pageNumber,
                    cursor: this.getCursor(),
                    cleanMetadata: false,
                });

                this.setState({
                    valuesDisplayed: [...this.state.valuesDisplayed, ...newValues],
                    isFetchingNextPage: false,
                    hasMorePages: newValues.length === pageSize,
                });
            },
        );
        /* eslint-enable react/no-access-state-in-setstate */
    };

    private readonly getRows = (): any => {
        return this.canCreateNewItem(this.state.valuesDisplayed)
            ? [this.getCreateNewItem(), ...this.state.valuesDisplayed]
            : this.state.valuesDisplayed;
    };

    private readonly getOptionsMenuType = (): string | undefined => {
        if (!this.props.fieldProperties.optionsMenu?.length) {
            return undefined;
        }

        return this.props.fieldProperties.optionsMenuType;
    };

    private readonly onScroll = (event: React.SyntheticEvent<HTMLDivElement>): void => {
        const listBody = event.target as HTMLDivElement;
        // fetch next page when there are only 5 cards left (78px tall)
        const shouldLoadNextPage = listBody.scrollHeight - listBody.scrollTop <= listBody.clientHeight + 390;
        if (this.state.hasMorePages && !this.state.isFetchingNextPage && shouldLoadNextPage) {
            this.fetchNextPage();
        }
    };

    private readonly onKeyDown = (event: React.KeyboardEvent<HTMLDivElement>): void => {
        if (document.activeElement?.id.startsWith(NAVIGATION_PANEL_ELEMENT_ID_PREFIX) && this.isNavigationPanel()) {
            const currentItemIndex = Number(document.activeElement.id.replace(NAVIGATION_PANEL_ELEMENT_ID_PREFIX, ''));
            if (event.key === 'ArrowDown') {
                const nextElement = event.currentTarget.querySelector(
                    `#${NAVIGATION_PANEL_ELEMENT_ID_PREFIX}${currentItemIndex + 1}`,
                ) as HTMLButtonElement;
                nextElement?.focus();
            }

            if (event.key === 'ArrowUp') {
                const previousElement = event.currentTarget.querySelector(
                    `#${NAVIGATION_PANEL_ELEMENT_ID_PREFIX}${currentItemIndex - 1}`,
                ) as HTMLButtonElement;
                previousElement?.focus();
            }

            if (event.key === 'Tab' && !event.shiftKey) {
                event.preventDefault();
                focusPageContent();
            }

            if (event.key === 'Tab' && event.shiftKey) {
                const searchTextboxId = getSearchTextboxId(this.props.screenId, this.props.elementId);
                const inputElement = searchTextboxId && document.querySelector(`#${searchTextboxId}`);
                if (inputElement) {
                    event.preventDefault();
                    (inputElement as HTMLInputElement).focus();
                }
            }
        }
    };

    private readonly onOptionsMenuItemChanged = async (selectedOptionsMenuItem: OptionsMenuItem): Promise<void> => {
        this.props.setTableViewOptionsMenuItem?.(0, selectedOptionsMenuItem);
    };

    private readonly onSearchBoxValueChange = (searchText: string): void => {
        this.setState({ searchText, isLoading: true }, async () => {
            if (this.props.setTableViewSearchText && this.isNavigationPanel()) {
                this.props.setTableViewSearchText(0, searchText);
            }
            await this.refreshTableContent();
        });
    };

    private readonly onResetFilter = (): void => this.onSearchBoxValueChange('');

    private renderMobileHeaderActions(): React.ReactNode {
        const filterableField = this.getFilterableFields();
        return (
            <div className="e-field-actions-wrapper">
                <div className="e-component-actions">
                    <FieldActions
                        screenId={this.props.screenId}
                        fieldId={this.props.elementId}
                        isDisabled={this.isDisabled()}
                    />
                    {this.props.fieldProperties.canFilter && (
                        <FiltersComponent
                            screenId={this.props.screenId}
                            fields={filterableField}
                            handleSave={this.onFiltersChanged}
                            filters={this.toXtremFilters(this.props.filterModel)}
                            isDisabled={this.isDisabled()}
                        />
                    )}
                    {this.props.fieldProperties.canAddNewLine && this.props.fieldProperties.sidebar && (
                        <Button data-testid="e-add-line-button" onClick={this.props.openSidebar} buttonType="primary">
                            {localize('@sage/xtrem-ui/add-item-in-line', 'Add line')}
                        </Button>
                    )}
                </div>
            </div>
        );
    }

    private renderDesktopHeader(): React.ReactNode {
        const hasAddItemsButton = Boolean(
            this.props.fieldProperties.canAddNewLine && !this.props.fieldProperties.isPhantomRowDisabled,
        );

        return (
            <DesktopTableHeaderComponent
                canAddNewLine={Boolean(this.props.fieldProperties.canAddNewLine)}
                elementId={this.props.elementId}
                fieldProperties={this.props.fieldProperties}
                filterComponentFields={this.getFilterableFields()}
                filtersComponentFilters={this.toXtremFilters(this.props.filterModel)}
                hasData={this.getRows().length > 0}
                isDisabled={this.isDisabled()}
                isReadOnly={this.isReadOnly()}
                onFiltersComponentChanged={this.onFiltersChanged}
                onOptionsMenuItemChange={this.onOptionsMenuItemChanged}
                onSearchBoxValueChange={this.onSearchBoxValueChange}
                onTelemetryEvent={this.props.onTelemetryEvent}
                screenId={this.props.screenId}
                selectedOptionsMenuItem={this.props.selectedOptionsMenuItem}
                validationErrors={this.props.validationErrors}
                hasAddItemsButton={hasAddItemsButton}
                hasSidebar={Boolean(this.props.fieldProperties.sidebar)}
            />
        );
    }

    private renderOptionsMenu(): React.ReactNode {
        return (
            <ConnectedTableOptionsMenu
                elementId={this.props.elementId}
                onSelectionChange={this.onOptionsMenuItemChanged}
                optionsMenuItems={resolveByValue<OptionsMenuItem[]>({
                    propertyValue: this.props.fieldProperties.optionsMenu,
                    rowValue: null,
                    screenId: this.props.screenId,
                    fieldValue: null,
                    skipHexFormat: true,
                })}
                optionsMenuType={this.props.fieldProperties.optionsMenuType}
                screenId={this.props.screenId}
                selectedOptionsMenuItem={this.props.selectedOptionsMenuItem}
                onTelemetryEvent={this.props.onTelemetryEvent}
            />
        );
    }

    private renderMobileSearchbox(): React.ReactNode {
        return this.props.fieldProperties.hasSearchBoxMobile ? (
            <TableSearchBox
                elementId={this.props.elementId}
                screenId={this.props.screenId}
                isDisabled={this.isDisabled()}
                fieldProperties={this.props.fieldProperties}
                onSearchBoxValueChange={this.onSearchBoxValueChange}
                filterText={this.state.searchText}
            />
        ) : (
            <div className="e-table-field-mobile-search" />
        );
    }

    private renderMobileHeader(): React.ReactNode {
        const shouldRenderFieldTitle = !!this.props.fieldProperties.title && !this.props.fieldProperties.isTitleHidden;
        const optionsMenu = resolveByValue<OptionsMenuItem[]>({
            propertyValue: this.props.fieldProperties.optionsMenu,
            rowValue: null,
            screenId: this.props.screenId,
            fieldValue: null,
            skipHexFormat: true,
        });

        const shouldRenderFieldHeader =
            this.props.fieldProperties.hasSearchBoxMobile ||
            optionsMenu?.length > 0 ||
            this.props.fieldProperties.canFilter ||
            this.props.hasFieldActions ||
            (this.props.fieldProperties.canAddNewLine && this.props.fieldProperties.sidebar);

        const shouldRenderDropdownOrToggleOptionsMenu =
            optionsMenu?.length > 0 && this.props.fieldProperties.optionsMenuType !== 'tabs';

        const shouldRenderTabsOptionsMenu =
            optionsMenu?.length > 0 && this.props.fieldProperties.optionsMenuType === 'tabs';

        const shouldRenderFilterLabels =
            !this.isNavigationPanel() &&
            this.props.fieldProperties.canFilter &&
            this.toXtremFilters(this.props.filterModel).length > 0;
        const shouldRenderNavigationPanelFilterLabels =
            this.isNavigationPanel() && this.toXtremFilters(this.props.filterModel).length > 0;
        // If none of the header functionality is used, the empty wrapper div should not be rendered
        if (
            !shouldRenderFieldTitle &&
            !shouldRenderFieldHeader &&
            !shouldRenderFilterLabels &&
            !shouldRenderNavigationPanelFilterLabels
        ) {
            return null;
        }

        return (
            <div className="e-mobile-table-header">
                {shouldRenderFieldTitle && (
                    <div className="e-field-title">
                        <FieldLabel label={getFieldTitle(this.props.screenId, this.props.fieldProperties, null)} />
                    </div>
                )}
                {shouldRenderFieldHeader && (
                    <div
                        className="e-field-header"
                        data-testid="e-field-header"
                        data-options-menu-type={this.getOptionsMenuType()}
                    >
                        {shouldRenderDropdownOrToggleOptionsMenu && this.renderOptionsMenu()}
                        <div className="e-table-mobile-searchbox-with-filter-line">
                            {this.renderMobileSearchbox()}
                            {this.renderMobileHeaderActions()}
                        </div>
                        {shouldRenderTabsOptionsMenu && this.renderOptionsMenu()}
                    </div>
                )}
                {(shouldRenderFilterLabels || shouldRenderNavigationPanelFilterLabels) && (
                    <FiltersLabels
                        fields={this.getFilterableFields()}
                        screenId={this.props.screenId}
                        handleSave={this.onFiltersChanged}
                        filters={this.props.filterModel}
                        isDisabled={this.isDisabled()}
                        isNavigationPanel={this.isNavigationPanel()}
                    />
                )}
            </div>
        );
    }

    render(): React.ReactNode {
        // The mobile header gets rendered in mobile or in the split view navigation panel
        const shouldRenderMobileHeader =
            this.props.browser.is.xs ||
            this.props.elementId === navigationPanelId ||
            // The lookup dialog renders in mobile mode on XS and S devices, so in case the table is used on such screen sizes, we still need the mobile mode
            (this.props.browser.is.s &&
                (this.props.fieldProperties._controlObjectType === FieldKey.FilterSelect ||
                    this.props.fieldProperties._controlObjectType === FieldKey.Reference));

        const uiMobileTableProps: UiMobileTableProps = {
            areCardFieldTitlesDisplayed: this.props.fieldProperties.areCardFieldTitlesDisplayed,
            availableColumns: this.props.availableColumns,
            canAddRecord: this.canCreateNewItem(this.state.valuesDisplayed),
            canDragCard: this.props.canDragCard,
            columns: this.props.fieldProperties.columns,
            recordWidth: this.props.fieldProperties.recordWidth,
            dropdownActions: this.props.fieldProperties.dropdownActions,
            elementId: this.props.elementId,
            fieldProperties: this.props.fieldProperties,
            groupByField: this.props.groupByField,
            hasMorePages: this.state.hasMorePages,
            inlineActions: this.props.fieldProperties.inlineActions,
            isDisabled: this.isDisabled(),
            isFetchingNextPage: this.state.isFetchingNextPage,
            isGreaterThanSmall: this.props.browser.greaterThan.s,
            isLoading: this.state.isLoading || this.props.isRefreshing,
            isNavigationPanel: this.isNavigationPanel(),
            isUsingInfiniteScroll: this.props.isUsingInfiniteScroll,
            onFetchNextPage: this.fetchNextPage,
            onKeyDown: this.onKeyDown,
            onResetFilter: this.onResetFilter,
            onRowClick: this.props.onRowClick ? this.onRowClick : undefined,
            onRowSelected: this.onRowSelected,
            onScroll: this.onScroll,
            screenId: this.props.screenId,
            searchText: this.state.searchText,
            valuesDisplayed: this.state.valuesDisplayed,
        };
        return (
            <div className="e-table-field-mobile">
                {shouldRenderMobileHeader ? this.renderMobileHeader() : this.renderDesktopHeader()}
                <MobileTable {...uiMobileTableProps} />
            </div>
        );
    }
}

/** @internal */
const mapStateToProps = (state: xtremRedux.XtremAppState, props: TableInternalComponentProps): MobileTableProps => {
    const screenDefinition = state.screenDefinitions[props.screenId];
    const pageDefinition = screenDefinition as PageDefinition;
    const tableViews = screenDefinition.userSettings?.[props.elementId];
    const currentViewLevel: TableViewLevel = tableViews?.$current?.content?.[0];
    const filter = currentViewLevel?.filter || {};
    const searchText = props.searchText || currentViewLevel?.searchText || '';
    const selectedOptionsMenuItem = getActiveOptionsMenu(props.screenId, tableViews, props.fieldProperties.optionsMenu);

    const screenElement = getScreenElement(screenDefinition);
    const fieldActions =
        typeof props.fieldProperties?.fieldActions === 'function'
            ? props.fieldProperties?.fieldActions.apply(screenElement)
            : [];
    return {
        ...props,
        browser: state.browser,
        nodeTypes: state.nodeTypes,
        hasFieldActions: fieldActions.length > 0,
        filterModel: filter,
        selectedOptionsMenuItem,
        openSidebar: xtremRedux.actions.actionStub,
        searchText,
        isRefreshing: props.elementId === navigationPanelId && pageDefinition?.navigationPanel?.isRefreshing,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: TableInternalComponentProps,
): Partial<MobileTableProps> => ({
    setTableViewFilter: (level: number, filter?: FilterModel): void => {
        dispatch(xtremRedux.actions.setTableViewFilter(props.screenId, props.elementId, level, filter));
    },
    setTableViewOptionsMenuItem: (level: number, optionsMenuItem?: OptionsMenuItem): void => {
        dispatch(
            xtremRedux.actions.setTableViewOptionMenuItem(props.screenId, props.elementId, level, optionsMenuItem),
        );
    },
    setTableViewSearchText: (level: number, searchText: string): void => {
        dispatch(xtremRedux.actions.setTableViewSearchText(props.screenId, props.elementId, level, searchText));
    },
    openSidebar: (): void => {
        dispatch(
            xtremRedux.actions.openTableSidebar({
                columns: getNestedFieldsFromProperties(props.fieldProperties),
                screenId: props.screenId,
                elementId: props.elementId,
                cardDefinition: props.fieldProperties.mobileCard,
                sidebarDefinition: props.fieldProperties.sidebar,
                level: 0,
            }),
        );
    },
});

/** @internal */
export const MobileTableComponent = connect(mapStateToProps, mapDispatchToProps)(UnconnectedMobileTableComponent);
