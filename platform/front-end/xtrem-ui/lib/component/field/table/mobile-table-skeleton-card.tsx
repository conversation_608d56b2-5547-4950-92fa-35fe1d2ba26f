import React from 'react';
import Preview from 'carbon-react/esm/components/preview';
import type { CardDefinition } from '../../ui/card/card-component';
import { GridColumn } from '@sage/xtrem-ui-components';

export interface MobileTableSkeletonCardProps {
    cardDefinition: CardDefinition;
    availableColumns: number;
}

export const MobileTableSkeletonCard = React.memo<MobileTableSkeletonCardProps>(
    ({ cardDefinition, availableColumns }: MobileTableSkeletonCardProps) => (
        <GridColumn columnSpan={availableColumns} className="e-card" key="4">
            <div className="e-card-content e-card-content-skeleton">
                <div className="e-card-content-skeleton-row">
                    <Preview loading width="100%" height="16px" />
                </div>
                {(cardDefinition.line2 || cardDefinition.line2Right) && (
                    <div className="e-card-content-skeleton-row">
                        <Preview loading width="30%" height="16px" />
                    </div>
                )}
                {(cardDefinition.line3 || cardDefinition.line3Right) && (
                    <div className="e-card-content-skeleton-row">
                        <Preview loading width="80%" height="16px" />
                    </div>
                )}
                {(cardDefinition.line4 || cardDefinition.line4Right) && (
                    <div className="e-card-content-skeleton-row">
                        <Preview loading width="80%" height="16px" />
                    </div>
                )}
                {(cardDefinition.line5 || cardDefinition.line5Right) && (
                    <div className="e-card-content-skeleton-row">
                        <Preview loading width="80%" height="16px" />
                    </div>
                )}
            </div>
        </GridColumn>
    ),
);

MobileTableSkeletonCard.displayName = 'MobileTableSkeletonCard';
