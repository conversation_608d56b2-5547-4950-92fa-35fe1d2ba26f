import type {
    BaseExportParams,
    CellClickedEvent,
    CellEditingStoppedEvent,
    CellFocusedEvent,
    CellKeyDownEvent,
    ColDef,
    Column,
    ColumnEverythingChangedEvent,
    ColumnMenuTab,
    ColumnResizedEvent,
    ColumnRowGroupChangedEvent,
    ColumnState,
    FilterChangedEvent,
    FirstDataRenderedEvent,
    GetMainMenuItems,
    GridApi,
    GridOptions,
    GridReadyEvent,
    IRowNode,
    IServerSideDatasource,
    IServerSideGetRowsParams,
    MenuItemDef,
    Module,
    ProcessCellForExportParams,
    RowHeightParams,
    SelectionChangedEvent,
    SortChangedEvent,
} from '@ag-grid-community/core';
import { CsvExportModule } from '@ag-grid-community/csv-export';
import { AgGridReact } from '@ag-grid-community/react';
import { EnterpriseCoreModule } from '@ag-grid-enterprise/core';
import { ExcelExportModule } from '@ag-grid-enterprise/excel-export';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { RichSelectModule } from '@ag-grid-enterprise/rich-select';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { ServerSideRowModelModule } from '@ag-grid-enterprise/server-side-row-model';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { isValidDatePropertyValue } from '@sage/xtrem-date-time';
import type { Dict, FiltrableType } from '@sage/xtrem-shared';
import { flat, objectKeys } from '@sage/xtrem-shared';
import { cloneDeep, difference, get, isEmpty, isEqual, isNil, set, sortBy } from 'lodash';
import * as React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import { connect } from 'react-redux';
import type { Unsubscribe } from 'redux';
import * as xtremRedux from '../../../redux';
import { ActionType } from '../../../redux';
import { subscribeToActions } from '../../../redux/middleware/action-subscription-middleware';
import type { DateAggregation } from '../../../redux/state';
import { setAgGridLicence } from '../../../service/ag-grid-licence-service';
import type { CollectionGlobalValidationState, CollectionValueType } from '../../../service/collection-data-types';
import { RecordActionType } from '../../../service/collection-data-types';
import { getGroupKey, type AggFunc } from '../../../service/collection-data-utils';
import type { Filter } from '../../../service/filter-service';
import { localize } from '../../../service/i18n-service';
import { getScreenElement } from '../../../service/screen-base-definition';
import { exportTableData } from '../../../service/table-export-service';
import { showToast } from '../../../service/toast-service';
import { ContextType } from '../../../types';
import type { TableSidebarDialogContent } from '../../../types/dialogs';
import { findColumnDefinitionByBind, getNestedFieldElementId } from '../../../utils/abstract-fields-utils';
import {
    cellExportFormatter,
    getColumnsToExport,
    getRowClassRules,
} from '../../../utils/ag-grid/ag-grid-cell-editor-utils';
import {
    COLUMN_ID_AUTO_COLUMN,
    COLUMN_ID_LINE_NUMBER,
    COLUMN_ID_ROW_ACTIONS,
    COLUMN_ID_ROW_SELECTION,
    COLUMN_ID_VALIDATIONS,
    frameworkComponents,
    getSelectionColumnDef,
} from '../../../utils/ag-grid/ag-grid-column-config';
import { onRowClick } from '../../../utils/ag-grid/ag-grid-event-handlers';
import { getColumns } from '../../../utils/ag-grid/ag-grid-service';
import localeText from '../../../utils/ag-grid/ag-grid-strings';
import {
    GROUP_ROW_HEIGHT,
    ROW_HEIGHT,
    callGridMethod,
    copySelectedCellValue,
    getColumnStatesForColumnPanel,
    getFilterModel,
    getFirstEditableColumn,
    getSafeGridApiContext,
    getSelectionFilter,
    getTotalRecordCount,
    mapAgGridFilterToXtremFilters,
    pasteToSelectedCell,
    tryToCommitPhantomRow,
} from '../../../utils/ag-grid/ag-grid-table-utils';
import type { AgGridColumnConfigWithScreenIdAndColDef } from '../../../utils/ag-grid/ag-grid-utility-types';
import { xtremConsole } from '../../../utils/console';
import { isRowValueInDateGroup } from '../../../utils/date-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { convertDeepBindToPath, convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { getPageDefinitionFromState, getPagePropertiesFromPageDefinition } from '../../../utils/state-utils';
import {
    AUTO_COLUMN_ID,
    getActiveOptionsMenu,
    getCardDefinitionFromColumns,
    getOrderByFromSortModel,
    getTableContext,
    getTableViewColumnHidden,
    getTableViewColumnOrder,
    getTableViewFilter,
    getTableViewGrouping,
    getTableViewSortOrder,
    isColumnBindVariant,
    setTableContext,
} from '../../../utils/table-component-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import { hasAgGridLogging } from '../../../utils/window';
import { navigationPanelId } from '../../container/navigation-panel/navigation-panel-types';
import type { OptionsMenuItem } from '../../container/page/page-types';
import type { PageActionControlObject, ReferenceProperties } from '../../control-objects';
import type { NestedField } from '../../nested-fields';
import type { OrderByType } from '../../types';
import { FieldKey, SET } from '../../types';
import { AsyncCalendarBodyComponent } from '../../ui/calendar-body/async-calendar-body-component';
import type { CalendarView } from '../../ui/calendar-body/calendar-body-types';
import { Icon } from '../../ui/icon/icon-component';
import { TableLoadingCellRenderer } from '../../ui/table-shared/cell/table-loading-cell-renderer';
import { DesktopTableBulkActionBar } from '../../ui/table-shared/desktop-table-bulk-action-bar';
import { DesktopTableHeaderComponent } from '../../ui/table-shared/desktop-table-header';
import { TableConfigurationDialog } from '../../ui/table-shared/table-configuration-dialog/table-configuration-dialog';
import { generateFieldId, getFieldTitle, isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import { getReferenceValueField } from '../reference/reference-utils';
import type { NestedGroupAggregations } from '../traits';
import type {
    FilterModel,
    InternalTableProperties,
    TableComponentExternalProps,
    TableInternalComponentProps,
    TableProperties,
    TableUserSettings,
    TableViewGrouping,
    TableViewMode,
    TableViewSortedColumn,
} from './table-component-types';

const activatedAgGridModules = [
    ServerSideRowModelModule,
    EnterpriseCoreModule,
    MenuModule,
    RichSelectModule,
    SetFilterModule,
    RowGroupingModule,
    ExcelExportModule,
    CsvExportModule,
] as Module[];

enum DataOperationMode {
    NONE,
    FETCH_PAGE,
    REMOVE_RECORD,
    RESET_TABLE,
    FILTER_ERRORS,
}

interface ResizedColumn {
    key: string | Column;
    newWidth: number;
}

interface TableDesktopState {
    autoGroupColumnDef?: ColDef;
    columns: AgGridColumnConfigWithScreenIdAndColDef[];
    columnStates?: ColumnState[];
    groupByColumn?: AgGridColumnConfigWithScreenIdAndColDef;
    hasFloatingFilters: boolean;
    isConfigurationDialogOpen?: boolean;
    isExportingExcel: boolean;
    isSelectAllChecked: boolean;
    phantomRows: { _id: string }[];
    selectedRowCount: number;
    selectedCalendarView: CalendarView;
    resizedColumns: ResizedColumn[];
    tableHasData: boolean;
}
type OptionalAggFunc = AggFunc | null | undefined;

export class DesktopTableComponent extends React.Component<TableInternalComponentProps, TableDesktopState> {
    private gridApi: GridApi | null = null;

    private readonly gridId = generateFieldId({
        screenId: this.props.screenId,
        elementId: this.props.elementId,
        fieldProperties: this.props.fieldProperties,
        isNested: false,
    });

    private dataOperationMode = DataOperationMode.NONE;

    private readonly autoSizeStrategy: GridOptions['autoSizeStrategy'] = {
        type: 'fitCellContents',
    };

    private isFetching = false;

    private isFilteringErrors = false;

    private actionSubscription: Unsubscribe;

    private collectionValueChangeSubscription: Unsubscribe;

    private collectionValidityChangeSubscription: Unsubscribe;

    protected containerRef = React.createRef<HTMLDivElement>();

    private readonly defaultColDef: GridOptions['defaultColDef'] = { flex: 1, resizable: false, singleClickEdit: true };

    private readonly loadingCellRendererParams: GridOptions['loadingCellRendererParams'] = {
        elementId: this.props.elementId,
    };

    private readonly loadingOverlayComponentParams: GridOptions['loadingOverlayComponentParams'] = { size: 'large' };

    constructor(props: TableInternalComponentProps) {
        super(props);
        setAgGridLicence();

        this.state = {
            autoGroupColumnDef: {
                colId: AUTO_COLUMN_ID,
                rowGroup: true,
                menuTabs: ['filterMenuTab', 'generalMenuTab'],
                flex: undefined,
                width: 250,
                minWidth: 250,
                resizable: true,
                editable: false,
                ...(this.props.groupTitle && { headerName: this.props.groupTitle }),
                sortable: true,
            },
            isConfigurationDialogOpen: false,
            columns: [],
            // Enable floating filters for lookups
            hasFloatingFilters: this.props.selectionMode !== undefined,
            isExportingExcel: false,
            isSelectAllChecked: false,
            phantomRows: [],
            selectedRowCount: 0,
            selectedCalendarView: 'dayGridMonth',
            tableHasData:
                props.value &&
                props.value.getData &&
                props.value.getData({ temporaryRecords: this.props.additionalLookupRecords?.() }).length > 0,
            resizedColumns: [],
        };
    }

    componentDidMount(): void {
        this.getColumnDefinitions({
            keepVisibility: true,
        });
        this.actionSubscription = subscribeToActions(action => {
            if (
                action.type === ActionType.RedrawComponent &&
                this.gridApi &&
                this.props.screenId === action.value.screenId &&
                this.props.elementId === action.value.elementId
            ) {
                const columns: string[] | undefined = action.value.columnBind
                    ? (callGridMethod(this.gridApi, 'getColumnDefs') ?? [])
                          ?.filter((c: ColDef) => c.field?.indexOf(action.value.columnBind!) === 0)
                          .map((c: ColDef) => c.colId as string)
                    : undefined;

                if (columns && columns.length > 0) {
                    this.gridApi.refreshCells({ force: true, columns });
                } else {
                    this.getColumnDefinitions({ keepVisibility: true });
                }
            }
        });
    }

    async componentDidUpdate(prevProps: TableInternalComponentProps): Promise<void> {
        if (!this.isNavigationPanel() && this.props.selectedRecordId !== prevProps.selectedRecordId) {
            this.resetOptionsMenu();
        }

        if (!this.gridApi) {
            return;
        }

        if (!this.props.isInFocus && prevProps.isInFocus) {
            callGridMethod(this.gridApi, 'stopEditing', true);
        }

        // When the value changes (e.g the user switches between records), we should unset filters that were applied to the table
        if (!this.isNavigationPanel() && prevProps.value !== this.props.value) {
            callGridMethod(this.gridApi, 'setFilterModel', {});
        } else if (
            this.isNavigationPanel() &&
            this.props.value &&
            !isEqual(prevProps.tableUserSettings, this.props.tableUserSettings)
        ) {
            await this.initColumnState();
            this.setShouldResetTable();
            this.applyTableView();
            setTimeout(() => {
                this.autoSizeAllColumns();
            }, 0);
        }

        if (!this.state.columnStates) {
            await this.initColumnState();
        }

        if (!isEqual(prevProps.selectedOptionsMenuItem, this.props.selectedOptionsMenuItem)) {
            callGridMethod(this.gridApi, 'setGridOption', 'serverSideDatasource', this.serverSideDataSource);
        }

        if (prevProps.fieldProperties.isPhantomRowDisabled !== this.props.fieldProperties.isPhantomRowDisabled) {
            this.setPhantomRow();
            setTimeout(() => callGridMethod(this.gridApi, 'refreshServerSide', { purge: true }), 0);
        }

        if (
            prevProps.value !== this.props.value ||
            prevProps.fieldProperties.isDisabled !== this.props.fieldProperties.isDisabled ||
            prevProps.fieldProperties.tableViewMode !== this.props.fieldProperties.tableViewMode ||
            prevProps.isParentDisabled !== this.props.isParentDisabled ||
            prevProps.isReadOnly !== this.props.isReadOnly
        ) {
            this.dataOperationMode =
                prevProps.fieldProperties.tableViewMode !== this.props.fieldProperties.tableViewMode
                    ? DataOperationMode.RESET_TABLE
                    : DataOperationMode.NONE;
            const resetTableFilters = this.isNavigationPanel();
            this.getColumnDefinitions({
                keepVisibility: true,
                keepGrouping: true,
                resetTableFilters,
                resetSelection: false,
            });
            // Revalidate field with current data
            this.props.value?.validateField();
            const ctx = getTableContext(this.gridApi);
            if (ctx) {
                // Reset headers
                setTableContext(this.gridApi, c => {
                    c.headerClasses = {};
                });
                if (!this.isNavigationPanel()) {
                    this.gridApi.refreshHeader();
                }
            }
        }

        if (!this.isNavigationPanel() && this.props.openedRecordId !== prevProps.openedRecordId) {
            const rowNodes: IRowNode[] = [];

            if (typeof prevProps.openedRecordId === 'string') {
                const node = this.getRowNode({ id: prevProps.openedRecordId, gridApi: this.gridApi });
                if (node) {
                    rowNodes.push(node);
                }
            }

            if (typeof this.props.openedRecordId === 'string') {
                const node = this.getRowNode({ id: this.props.openedRecordId, gridApi: this.gridApi });
                if (node) {
                    rowNodes.push(node);
                }
            }

            if (rowNodes.length) {
                setTimeout(() => callGridMethod(this.gridApi, 'redrawRows', { rowNodes }));
            }
        }

        if (
            !isEqual(sortBy(prevProps.fieldProperties.hiddenColumns), sortBy(this.props.fieldProperties.hiddenColumns))
        ) {
            this.hideUnhideColumns();
            this.autoSizeAllColumns();
        }

        if (
            !isEqual(
                sortBy(prevProps.fieldProperties.selectedRecords),
                sortBy(this.props.fieldProperties.selectedRecords),
            )
        ) {
            const currentSelectedNodes = callGridMethod(this.gridApi, 'getSelectedNodes');
            const currentSelectedIds = (currentSelectedNodes ?? []).filter(n => n.data?._id).map(n => n.data._id);
            const newlySelected = difference(this.props.fieldProperties.selectedRecords ?? [], currentSelectedIds);
            const newlyUnselected = difference(currentSelectedIds, this.props.fieldProperties.selectedRecords ?? []);
            newlyUnselected.forEach(id => {
                const n = this.getRowNode({ id });
                if (n && n.isSelected()) {
                    n.setSelected(false);
                }
            });
            newlySelected.forEach(id => {
                const n = this.getRowNode({ id });
                if (n && !n.isSelected()) {
                    n.setSelected(true);
                }
            });
        }
    }

    componentWillUnmount(): void {
        if (this.actionSubscription) {
            this.actionSubscription();
        }
        if (this.collectionValueChangeSubscription) {
            this.collectionValueChangeSubscription();
        }

        if (this.collectionValidityChangeSubscription) {
            this.collectionValidityChangeSubscription();
        }
        window.removeEventListener('resize', this.resizeListener);
        this.gridApi?.removeGlobalListener(this.onGridEvent);
    }

    // eslint-disable-next-line react/no-unused-class-component-methods
    public getSelectionFilter(mode: 'server' | 'client' = 'server'): any {
        if (!this.gridApi) {
            return {};
        }
        return getSelectionFilter({
            gridApi: this.gridApi,
            screenId: this.props.screenId,
            isSelectAllChecked: this.state.isSelectAllChecked,
            tableFieldProperties: this.props.fieldProperties,
            activeOptionsMenuItem: this.props.selectedOptionsMenuItem,
            mode,
        });
    }

    // eslint-disable-next-line react/no-unused-class-component-methods
    public isSelectAllChecked(): boolean {
        return this.state.isSelectAllChecked;
    }

    private readonly getRowNode = ({
        id,
        gridApi = this.gridApi,
    }: {
        id: string;
        gridApi?: GridApi | null;
    }): IRowNode | undefined => {
        let node = callGridMethod(gridApi, 'getRowNode', id);
        if (node) {
            return node;
        }
        node = gridApi?.getPinnedTopRow(0);
        if (node?.data?._id === id) {
            return node;
        }
        return undefined;
    };

    private redrawLineNumbers(): void {
        if (this.props.fieldProperties.hasLineNumbers) {
            callGridMethod(this.gridApi, 'refreshCells', {
                force: true,
                columns: [COLUMN_ID_LINE_NUMBER],
            });
        }
    }

    private readonly focusPhantomRowAndStartEditing = (): void => {
        setTimeout(async () => {
            if (!this.gridApi) {
                return;
            }
            const phantomRecords = await this.props.value.getPhantomRecords();
            const colKey = getFirstEditableColumn(this.gridApi, phantomRecords?.[0]);
            if (colKey === undefined) {
                return;
            }
            callGridMethod(this.gridApi, 'setFocusedCell', 0, colKey, 'top');
            callGridMethod(this.gridApi, 'ensureColumnVisible', colKey);

            callGridMethod(this.gridApi, 'startEditingCell', {
                colKey,
                rowIndex: 0,
                key: 'Enter',
                rowPinned: 'top',
            });
        }, 50);
    };

    private readonly showHideNoRowsOverlay = (show: boolean): void => {
        setTimeout(() => {
            if (show) {
                callGridMethod(this.gridApi, 'showNoRowsOverlay');
            } else {
                callGridMethod(this.gridApi, 'hideOverlay');
            }
        }, 100);
    };

    private readonly onCollectionUpdated = (type: RecordActionType, rowValue: CollectionValueType): void => {
        if (this.gridApi) {
            if (type === RecordActionType.MODIFIED) {
                if (rowValue?.__phantom && rowValue?.__forceRowUpdate) {
                    delete rowValue.__forceRowUpdate;
                    this.setState({ phantomRows: [rowValue] });
                } else {
                    // In this case we target one specific row in order to reduce the amplitude of rendering
                    callGridMethod(this.gridApi, 'applyServerSideTransaction', {
                        update: [rowValue],
                    });
                }
            } else if (rowValue?.__phantom !== undefined && type === RecordActionType.ADDED) {
                this.setState({ phantomRows: [rowValue] }, () => {
                    if (this.props.isTableInFocus) {
                        this.focusPhantomRowAndStartEditing();
                    }
                });
            } else if (type === RecordActionType.ADDED) {
                this.dataOperationMode = DataOperationMode.FETCH_PAGE;
                const nodes = this.props.value.getData({ noLimit: true });
                const addIndex = nodes.findIndex(n => n._id === rowValue._id);
                callGridMethod(this.gridApi, 'applyServerSideTransaction', {
                    add: [rowValue],
                    addIndex: addIndex === -1 ? 0 : addIndex,
                });
                this.redrawLineNumbers();
            } else if (type === RecordActionType.REMOVED) {
                this.dataOperationMode = DataOperationMode.REMOVE_RECORD;
                callGridMethod(this.gridApi, 'applyServerSideTransaction', {
                    remove: [rowValue],
                });
                this.redrawLineNumbers();
            }
            const tableHasData =
                this.props.value &&
                this.props.value.getData &&
                this.props.value.getData({ temporaryRecords: this.props.additionalLookupRecords?.() }).length > 0;
            this.setState({ tableHasData });
            this.showHideNoRowsOverlay(!tableHasData);
        }
    };

    private findLastLoadedRowData(api: GridApi): Dict<any> | undefined {
        const lastKnownRowIndex = callGridMethod(api, 'getLastDisplayedRow');
        if (!lastKnownRowIndex) {
            return undefined;
        }

        for (let i = lastKnownRowIndex; i >= 0; i -= 1) {
            const rowNode = callGridMethod(api, 'getDisplayedRowAtIndex', i);

            if (rowNode?.data?._id) {
                return rowNode?.data;
            }
        }
        return undefined;
    }

    private getCursor(api: GridApi): string | undefined {
        return this.findLastLoadedRowData(api)?.__cursor;
    }

    private getFilters(filterModel: FilterModel): Filter<FiltrableType>[] {
        return objectKeys(filterModel)
            .map(mapAgGridFilterToXtremFilters(filterModel))
            .concat(
                this.isFilteringErrors
                    ? [
                          {
                              id: '_id',
                              value: [
                                  {
                                      filterValue: this.props.value.getAllInvalidRecords().map(r => r._id),
                                      filterType: {
                                          text: '_id in list',
                                          value: SET,
                                      },
                                  },
                              ],
                          },
                      ]
                    : [],
            );
    }

    private readonly applyTableView = (): void => {
        callGridMethod(this.gridApi, 'setFilterModel', getTableViewFilter(this.props.tableUserSettings));
        const currentColumnState = callGridMethod(this.gridApi, 'getColumnState');
        if (currentColumnState) {
            const cleanCurrentColumnState = cloneDeep(currentColumnState).map(c =>
                c.colId === AUTO_COLUMN_ID
                    ? c
                    : {
                          ...c,
                          rowGroup: false,
                          sort: null,
                          sortIndex: null,
                          rowGroupIndex: null,
                      },
            ) as ColumnState[];
            const columnVisibility = getTableViewColumnHidden(this.props.tableUserSettings);
            const newTableViewGrouping = getTableViewGrouping(this.props.tableUserSettings);
            const newTableSortOrder = getTableViewSortOrder(this.props.tableUserSettings);
            const newColumnState = objectKeys(columnVisibility ?? {}).reduce((acc, curr) => {
                const col = acc.find(c => c.colId === curr);
                const s = (newTableSortOrder ?? []).find(c => c.colId === curr);
                if (col && col.colId !== COLUMN_ID_VALIDATIONS) {
                    const isGroupColumn = curr === newTableViewGrouping?.key;
                    col.sort = s?.sort ?? null;
                    col.sortIndex = s?.sortIndex ?? null;
                    col.hide = isGroupColumn ? true : (columnVisibility?.[col.colId] ?? false);
                    col.rowGroup = isGroupColumn;
                    col.rowGroupIndex = isGroupColumn ? 0 : null;
                    col.aggFunc = isGroupColumn ? (newTableViewGrouping?.aggFunc ?? null) : null;
                }
                return acc;
            }, cleanCurrentColumnState);

            if (newColumnState && !isEqual(currentColumnState, newColumnState) && this.gridApi) {
                // Only apply new group setting if it has changed
                if (
                    this.state.groupByColumn?.aggFunc !== newTableViewGrouping?.aggFunc ||
                    this.state.groupByColumn?.field !== newTableViewGrouping?.key
                ) {
                    if (newTableViewGrouping) {
                        const colDef = callGridMethod(this.gridApi, 'getColumnDef', newTableViewGrouping.key) as
                            | AgGridColumnConfigWithScreenIdAndColDef
                            | null
                            | undefined;

                        if (colDef) {
                            this.groupByColumn(
                                {
                                    api: this.gridApi,
                                    colDef,
                                    aggFunc: newTableViewGrouping.aggFunc,
                                    sort: newTableViewGrouping.sort,
                                },
                                () => {
                                    callGridMethod(this.gridApi, 'applyColumnState', { state: newColumnState });
                                },
                            );
                        }
                    } else {
                        this.ungroupByColumn({ api: this.gridApi }, () => {
                            callGridMethod(this.gridApi, 'applyColumnState', {
                                state: newColumnState.filter(c => c.colId !== AUTO_COLUMN_ID),
                            });
                        });
                    }
                } else {
                    callGridMethod(this.gridApi, 'applyColumnState', {
                        state: newColumnState.filter(c => c.colId !== AUTO_COLUMN_ID),
                    });
                }
            }

            const currentColumnOrder = this.getCurrentColumnOrder();
            const columnOrder = getTableViewColumnOrder(this.props.tableUserSettings);
            if (columnOrder && !isEqual(currentColumnState, currentColumnOrder)) {
                callGridMethod(this.gridApi, 'moveColumns', columnOrder, this.moveStartIndex);
            }
        }
    };

    private readonly resetOptionsMenu = (): void => {
        const optionsMenu = resolveByValue({
            propertyValue: this.props.fieldProperties.optionsMenu,
            rowValue: null,
            screenId: this.props.screenId,
            fieldValue: null,
            skipHexFormat: true,
        });

        this.props.setTableViewOptionsMenuItemAndViewFilter?.(0, optionsMenu?.[0], {});
    };

    private readonly insertDataSourceIntoTable = (
        getRowsParams: IServerSideGetRowsParams,
        data: CollectionValueType[],
        lastRow?: number,
    ): void => {
        getRowsParams.success({ rowData: data, rowCount: lastRow });
        callGridMethod(getRowsParams.api, 'hideOverlay');
        const isEmptyTable = getRowsParams.request.groupKeys?.length === 0 && lastRow === 0;
        this.showHideNoRowsOverlay(isEmptyTable);
        if (lastRow && lastRow !== -1) {
            triggerFieldEvent(this.props.screenId, this.props.elementId, 'onAllDataLoaded');
        } else {
            triggerFieldEvent(this.props.screenId, this.props.elementId, 'onDataLoaded');
        }
    };

    private readonly updateColumnValidation = (globalValidationState: CollectionGlobalValidationState): void => {
        const ctx = this.gridApi ? getTableContext(this.gridApi) : undefined;
        if (this.gridApi && ctx) {
            // Refresh column's header
            const invalidColumns = objectKeys(globalValidationState?.[0] || {});
            const headerClasses = invalidColumns.reduce((acc, column) => {
                acc[column] = false;
                return acc;
            }, {} as Dict<boolean>);
            setTableContext(this.gridApi, c => {
                c.headerClasses = headerClasses;
            });

            this.gridApi
                .getAllGridColumns()
                .map(c => c.getColDef().field)
                .forEach(k => {
                    if (!k) {
                        return;
                    }
                    if (invalidColumns.includes(k)) {
                        document
                            .querySelectorAll(`[grid-id="${this.gridId}"] .ag-header-cell[col-id="${k}"]`)
                            .forEach(element => {
                                element.classList.add('e-table-field-header-column-error');
                            });
                    } else {
                        document
                            .querySelectorAll(`[grid-id="${this.gridId}"] .ag-header-cell[col-id="${k}"]`)
                            .forEach(element => {
                                element.classList.remove('e-table-field-header-column-error');
                            });
                    }
                });

            // Display/hide validation column
            getSafeGridApiContext(
                column => {
                    const isValidationColumnVisible = Boolean(column?.isVisible());
                    const shouldValidationColumnBeVisible = invalidColumns.length > 0;
                    if (isValidationColumnVisible !== shouldValidationColumnBeVisible) {
                        callGridMethod(
                            this.gridApi,
                            'setColumnVisible',
                            COLUMN_ID_VALIDATIONS,
                            shouldValidationColumnBeVisible,
                        );
                        this.autoSizeAllColumns();
                    }
                },
                this.gridApi,
                'getColumn',
                COLUMN_ID_VALIDATIONS,
            );
        }
    };

    private readonly getUnknownLastRow = (): number | undefined => {
        return this.isInfiniteScroll() ? -1 : undefined;
    };

    private readonly getRowCount = ({
        data,
        getRowsParams,
    }: {
        data: CollectionValueType[];
        getRowsParams: IServerSideGetRowsParams;
    }): number | undefined => {
        return data.length < (getRowsParams.request.endRow || 0) - (getRowsParams.request.startRow || 0)
            ? (getRowsParams.request.startRow || 0) + data.length
            : this.getUnknownLastRow();
    };

    private readonly isNavigationPanel = (): boolean => this.props.elementId === navigationPanelId;

    private readonly belongsToGroup = ({
        groupKey,
        groupValue,
        column,
        aggFunc,
    }: {
        groupKey: string;
        groupValue: string;
        column: NestedField<any, any>;
        aggFunc?: AggFunc;
    }): ((n: IRowNode) => boolean) =>
        aggFunc == null
            ? (n: IRowNode): boolean =>
                  get(n.data, getGroupKey({ groupKey, type: column.type }), null) ===
                  (groupValue === '' || isNil(groupValue) ? null : groupValue)
            : (n: IRowNode): boolean => {
                  const rowValue = get(n.data, groupKey);
                  if (groupValue === '' || isNil(groupValue)) {
                      return isNil(rowValue);
                  }
                  if (!isValidDatePropertyValue(rowValue)) {
                      return false;
                  }
                  return isRowValueInDateGroup(rowValue, groupValue, aggFunc);
              };

    private readonly selectTableItems = (api: GridApi): void => {
        (
            (
                xtremRedux.getStore().getState().screenDefinitions[this.props.screenId].metadata.uiComponentProperties[
                    this.props.elementId
                ] as TableProperties
            )?.selectedRecords ?? []
        ).forEach(id => {
            const rowNode = this.getRowNode({ id, gridApi: api });
            if (rowNode && !rowNode.isSelected()) {
                // IMPORTANT: DO NOT handle in 'onSelectionChanged' event
                rowNode.setSelected(true, false, 'rowDataChanged');
            }
        });
    };

    /**
     * Ag-grid callback which is triggered whenever the table component needs new data from our framework
     * @param getRowsParams
     */
    private readonly getRows = async (getRowsParams: IServerSideGetRowsParams): Promise<void> => {
        const hasGroups = getRowsParams.request.rowGroupCols.length > 0;
        /**
         * Set flag to disable row field selection so events will not be triggered when rows selected
         * programmatically.
         */
        const pageSize = this.props.fieldProperties.pageSize || 20;
        const pageNumber = Math.max(0, Math.ceil((getRowsParams.request.endRow || 0) / pageSize) - 1);
        callGridMethod(getRowsParams.api, 'hideOverlay');

        if (hasGroups) {
            callGridMethod(getRowsParams.api, 'showLoadingOverlay');

            const colId = getRowsParams.request.rowGroupCols[0].id;
            const column = findColumnDefinitionByBind(this.props.fieldProperties.columns || [], colId);
            if (!column) {
                throw new Error(`Cannot find column with the following id: ${colId}`);
            }
            const groupKey =
                column.type === FieldKey.Reference
                    ? `${convertDeepBindToPathNotNull(column?.properties.bind)}.${getReferenceValueField(
                          column?.properties as ReferenceProperties,
                      )}`
                    : convertDeepBindToPathNotNull(column?.properties.bind);
            const groupValue =
                column.type === FieldKey.Reference
                    ? (get(getRowsParams.parentNode.data, getGroupKey({ groupKey, type: column.type })) ??
                      getRowsParams.request.groupKeys?.[0])
                    : getRowsParams.request.groupKeys?.[0];

            let cursor: string | undefined;
            const nodes: IRowNode[] = [];
            if (!isNil(groupValue)) {
                callGridMethod(getRowsParams.api, 'forEachNode', n => {
                    if (
                        !n.group &&
                        this.belongsToGroup({
                            groupKey,
                            groupValue,
                            column,
                            aggFunc: getRowsParams.request.rowGroupCols[0]?.aggFunc as AggFunc | undefined,
                        })(n)
                    ) {
                        nodes.push(n);
                    }
                });
                cursor = nodes[(getRowsParams.request.startRow ?? 1) - 1]?.data?.__cursor;
                if (cursor === undefined && getRowsParams.request.startRow !== 0) {
                    getRowsParams.success({
                        rowData: [],
                        rowCount: nodes.length,
                    });
                    callGridMethod(getRowsParams.api, 'showNoRowsOverlay');

                    return;
                }
            } else {
                callGridMethod(getRowsParams.api, 'forEachNode', n => {
                    if (n.group && n.data) {
                        nodes.push(n);
                    }
                });
                cursor = nodes[(getRowsParams.request.startRow ?? 1) - 1]?.data?.__cursor;
                if (cursor === undefined && getRowsParams.request.startRow !== 0) {
                    getRowsParams.success({
                        rowData: nodes,
                        rowCount: nodes.length,
                    });
                    return;
                }
            }

            let sortOrder = getOrderByFromSortModel(
                getRowsParams.request.sortModel,
                this.props.fieldProperties.columns || [],
                this.state.autoGroupColumnDef,
            );

            if (isEmpty(sortOrder)) {
                sortOrder = { _id: -1 };
            }

            const orderBy: OrderByType =
                groupValue !== undefined
                    ? sortOrder
                    : objectKeys(flat(sortOrder)).reduce((acc, key) => {
                          if (key === groupKey) {
                              set(acc, key, get(sortOrder, key));
                          }
                          return acc;
                      }, {});

            // Map Ag-grid filter objects into GraphQL/MongoDB friendly filter data structure
            const userFilters = this.getFilters(getFilterModel(getRowsParams.api, this.state.groupByColumn?.field));
            const gridPageSize = (getRowsParams.request.endRow || 0) - (getRowsParams.request.startRow || 0);
            const groupedData = await this.props.value.getPage({
                tableFieldProperties: this.props.fieldProperties,
                filters: userFilters,
                orderBy,
                pageSize: gridPageSize,
                group: {
                    key: groupKey,
                    value: groupValue,
                    aggFunc: getRowsParams.request.rowGroupCols[0]?.aggFunc as AggFunc,
                    type: column.type,
                },
                cursor,
                cleanMetadata: false,
                pageNumber: cursor ? pageNumber : undefined,
                selectedOptionsMenuItem: this.props.selectedOptionsMenuItem,
            });

            const rowCount = this.getRowCount({ data: groupedData, getRowsParams });
            getRowsParams.success({
                rowData: groupedData,
                rowCount,
            });
            callGridMethod(getRowsParams.api, 'hideOverlay');
            return;
        }

        if (!this.props.value) {
            this.insertDataSourceIntoTable(getRowsParams, [], 0);
            return;
        }

        if (!this.isInfiniteScroll()) {
            callGridMethod(getRowsParams.api, 'showLoadingOverlay');
        }

        try {
            switch (this.dataOperationMode) {
                case DataOperationMode.NONE:
                    const data = this.props.value.getData({
                        cleanMetadata: false,
                        temporaryRecords: this.props.additionalLookupRecords?.(),
                        limit: this.props.fieldProperties.pageSize || 20,
                    });
                    this.insertDataSourceIntoTable(getRowsParams, data, this.getRowCount({ data, getRowsParams }));
                    this.collectionValueChangeSubscription = this.props.value.subscribeForValueChanges(
                        this.onCollectionUpdated,
                    );
                    this.collectionValidityChangeSubscription = this.props.value.subscribeForValidityChanges(
                        ({ globalValidationState, recordValidationState, recordId }) => {
                            if (this.gridApi) {
                                this.updateColumnValidation(globalValidationState);
                                const rowNode = this.getRowNode({ id: recordId });
                                if (rowNode) {
                                    const previousValidationStateKeys = objectKeys(
                                        rowNode.data.__validationState || {},
                                    );
                                    rowNode.setData({
                                        ...rowNode.data,
                                        __validationState: recordValidationState,
                                    });

                                    // Refresh cells
                                    const columns = difference(
                                        objectKeys(recordValidationState),
                                        previousValidationStateKeys,
                                    );
                                    this.gridApi?.refreshCells({
                                        rowNodes: [rowNode],
                                        columns,
                                        force: true,
                                        suppressFlash: true,
                                    });
                                }
                            }
                        },
                    );
                    this.selectTableItems(getRowsParams.api);
                    break;
                case DataOperationMode.FETCH_PAGE:
                    this.isFetching = true;
                    const newData = await this.props.value.getPageWithCurrentQueryArguments({
                        tableFieldProperties: this.props.fieldProperties,
                        pageSize,
                        pageNumber,
                        group: undefined,
                        cleanMetadata: false,
                        cursor: this.getCursor(getRowsParams.api),
                    });

                    this.updateColumnValidation(this.props.value.getValidationStateByColumn());

                    this.insertDataSourceIntoTable(
                        getRowsParams,
                        newData,
                        this.getRowCount({ data: newData, getRowsParams }),
                    );
                    this.selectTableItems(getRowsParams.api);
                    this.isFetching = false;
                    break;
                case DataOperationMode.REMOVE_RECORD:
                    this.onUnselectAll();
                    // Populating values
                    const mergedData = await this.props.value.getRecordWithCurrentQueryArguments({
                        tableFieldProperties: this.props.fieldProperties,
                        pageSize,
                        pageNumber,
                        group: undefined,
                        cursor: this.getCursor(getRowsParams.api),
                        cleanMetadata: false,
                    });
                    this.insertDataSourceIntoTable(
                        getRowsParams,
                        mergedData,
                        this.getRowCount({ data: mergedData, getRowsParams }),
                    );
                    this.selectTableItems(getRowsParams.api);
                    this.updateColumnValidation(this.props.value.getValidationStateByColumn());
                    break;
                case DataOperationMode.RESET_TABLE:
                    this.onUnselectAll();
                    if (this.isNavigationPanel()) {
                        this.props.value.cleanCollectionData();
                        callGridMethod(
                            this.gridApi,
                            'setGridOption',
                            'serverSideDatasource',
                            this.serverSideDataSource,
                        );
                    }

                    // Map Ag-grid sorting properties
                    let orderBy = getOrderByFromSortModel(
                        getRowsParams.request.sortModel,
                        this.props.fieldProperties.columns || [],
                        this.state.autoGroupColumnDef,
                    );

                    if (isEmpty(orderBy)) {
                        orderBy = { _id: -1 };
                    }

                    // Map Ag-grid filter objects into GraphQL/MongoDB friendly filter data structure
                    const userFilters = this.getFilters(
                        getFilterModel(getRowsParams.api, this.state.groupByColumn?.field),
                    );
                    // Map Ag-grid page sizing properties
                    const gridPageSize = (getRowsParams.request.endRow || 0) - (getRowsParams.request.startRow || 0);

                    // Populating values
                    const pageData = await this.props.value.getPage({
                        tableFieldProperties: this.props.fieldProperties,
                        filters: userFilters,
                        orderBy,
                        pageSize: gridPageSize,
                        group: undefined,
                        cursor: this.getCursor(getRowsParams.api),
                        cleanMetadata: false,
                        pageNumber: !this.getCursor(getRowsParams.api) ? pageNumber : undefined,
                        selectedOptionsMenuItem: this.props.selectedOptionsMenuItem,
                    });
                    this.insertDataSourceIntoTable(
                        getRowsParams,
                        pageData,
                        this.getRowCount({ data: pageData, getRowsParams }),
                    );
                    this.selectTableItems(getRowsParams.api);
                    callGridMethod(this.gridApi, 'paginationGoToFirstPage');
                    break;
                case DataOperationMode.FILTER_ERRORS:
                    if (this.props.value.getAllInvalidRecords().length === 0) {
                        this.insertDataSourceIntoTable(getRowsParams, [], 0);
                    } else if (this.props.fieldProperties.isTransient) {
                        this.insertDataSourceIntoTable(
                            getRowsParams,
                            this.props.value.getAllInvalidRecords(),
                            this.getRowCount({ data: this.props.value.getAllInvalidRecords(), getRowsParams }),
                        );
                    } else {
                        await this.props.value.fetchInvalidUnloadedRecords();
                        const errorPageData = await this.props.value.getPage({
                            tableFieldProperties: this.props.fieldProperties,
                            filters: this.getFilters(
                                getFilterModel(getRowsParams.api, this.state.groupByColumn?.field),
                            ),
                            orderBy: getOrderByFromSortModel(
                                getRowsParams.request.sortModel,
                                this.props.fieldProperties.columns || [],
                                this.state.autoGroupColumnDef,
                            ),
                            pageSize: (getRowsParams.request.endRow || 0) - (getRowsParams.request.startRow || 0),
                            group: undefined,
                            cursor: this.getCursor(getRowsParams.api),
                            cleanMetadata: false,
                            fetchPageSize: 500,
                        });
                        this.insertDataSourceIntoTable(
                            getRowsParams,
                            errorPageData,
                            this.getRowCount({ data: errorPageData, getRowsParams }),
                        );
                    }
                    break;
                default:
                    break;
            }

            if (
                this.props.value.getData({
                    cleanMetadata: false,
                    temporaryRecords: this.props.additionalLookupRecords?.(),
                }).length > 0
            ) {
                this.setState({ tableHasData: true });
            } else {
                this.setState({ tableHasData: false });
            }

            this.dataOperationMode = DataOperationMode.FETCH_PAGE;
        } catch (error) {
            xtremConsole.log(error);
            getRowsParams.fail();
        } finally {
            await this.setPhantomRow();
            this.adjustTableHeight({ api: getRowsParams.api });
        }
    };

    private readonly serverSideDataSource: IServerSideDatasource = {
        getRows: this.getRows,
    };

    private readonly isDisabled = (): boolean => {
        const x = isFieldDisabled(
            this.props.screenId,
            this.props.fieldProperties,
            this.props.recordContext?._id || null,
            this.props.recordContext || null,
        );
        return this.props.isParentDisabled || x;
    };

    private readonly isReadOnly = (): boolean =>
        resolveByValue<boolean>({
            fieldValue: null,
            propertyValue: this.props.isReadOnly,
            skipHexFormat: true,
            rowValue: null,
            screenId: this.props.screenId,
        }) || isFieldReadOnly(this.props.screenId, this.props.fieldProperties, null, undefined);

    private readonly resizeListener = (): void => {
        this.autoSizeAllColumns();
    };

    private readonly onGridEvent: Parameters<GridApi['addGlobalListener']>[0] = async (
        eventType,
        event,
    ): Promise<void> => {
        /**
         * ag-grid correctly selects all children when a group is expanded or data is added
         * but it doesn't automatically trigger the 'onSelectionChanged' event
         */
        if (
            (eventType as any) === 'displayedRowsChanged' &&
            Object.values(event.api.getCacheBlockState() ?? {}).some((b: any) => (b?.loadedRowCount ?? 0) > 0)
        ) {
            this.selectTableItems(event.api);
            this.onSelectionChanged(event);
        }
    };

    /**
     * Ag-grid lifecycle event listener. It is triggered when the table is prepared with the initial rendering of the
     * controller components and is ready to receive data.
     **/
    private readonly onGridReady = async (params: GridReadyEvent): Promise<void> => {
        this.gridApi = params.api;

        params.api.addGlobalListener(this.onGridEvent);

        // Timeout needed in order to avoid ag-grid redraw error
        setTimeout(() => {
            callGridMethod(this.gridApi, 'setGridOption', 'serverSideDatasource', this.serverSideDataSource);
        });

        if (this.props.contextType === ContextType.dialog) {
            (callGridMethod(this.gridApi, 'getRenderedNodes') ?? [])?.[0]?.setSelected?.(true);
        }

        if (this.gridApi) {
            const grouping = getTableViewGrouping(this.props.tableUserSettings);
            const filter = getTableViewFilter(this.props.tableUserSettings);

            if (grouping?.key !== undefined && grouping?.key !== this.state.groupByColumn?.colId) {
                // Restore group
                const colDef = callGridMethod(this.gridApi, 'getColumnDefs')?.find(
                    c => (c as AgGridColumnConfigWithScreenIdAndColDef).colId === grouping.key,
                ) as AgGridColumnConfigWithScreenIdAndColDef | undefined;

                if (colDef != null) {
                    setTimeout(
                        api => {
                            this.groupByColumn(
                                {
                                    api,
                                    colDef: {
                                        ...colDef,
                                        sort: grouping.sort,
                                    },
                                    aggFunc: grouping.aggFunc,
                                },
                                () => {
                                    callGridMethod(this.gridApi, 'setFilterModel', filter);
                                },
                            );
                        },
                        0,
                        this.gridApi,
                    );
                }
            } else {
                callGridMethod(this.gridApi, 'setFilterModel', filter);
            }
        }

        window.addEventListener('resize', this.resizeListener);
    };

    private readonly setPhantomRow = async (): Promise<void> => {
        const phantomRows = await (this.props.fieldProperties.canAddNewLine &&
        !this.props.fieldProperties.isPhantomRowDisabled
            ? this.props.value?.getPhantomRecords()
            : []);
        // Points to the first element to avoid race conditions that could return more than 1 row:
        this.setState(({ phantomRows: currentPhantomRows }) => {
            const newPhantomRows = phantomRows[0] ? [phantomRows[0]] : [];
            return { phantomRows: isEqual(currentPhantomRows, newPhantomRows) ? currentPhantomRows : newPhantomRows };
        });
    };

    private readonly onSwitchView = (view: TableViewMode): void => {
        this.props.setFieldProperties?.(this.props.elementId, {
            ...(xtremRedux.getStore().getState().screenDefinitions[this.props.screenId].metadata.uiComponentProperties[
                this.props.elementId
            ] as TableProperties),
            tableViewMode: view,
        });
    };

    private readonly onSwitchCalendarView = (selectedCalendarView: CalendarView): void => {
        this.props.onTelemetryEvent?.(`tableCalendarViewSwitched-${this.props.elementId}`, {
            view: selectedCalendarView,
            elementId: this.props.elementId,
            screenId: this.props.screenId,
        });
        this.setState({ selectedCalendarView });
    };

    private readonly handleFilterChanged = (params?: FilterChangedEvent): void => {
        const filterModel = getFilterModel(params?.api || this.gridApi, this.state.groupByColumn?.field);

        if (this.props.setTableViewFilter) {
            this.props.setTableViewFilter(0, filterModel);
        }

        this.setShouldResetTable();
    };

    private readonly handleColumnRowGroupChanged = (params: ColumnRowGroupChangedEvent): void => {
        // IMPORTANT to check if rowGroup is set in user provided column def
        if (this.props.setTableViewGrouping && params.column?.getUserProvidedColDef()?.rowGroup) {
            const columnDefinition = params.column?.getColDef();
            this.props.setTableViewGrouping(0, {
                key: params.column?.getColId() || '',
                sort: columnDefinition?.sort || 'asc',
                aggFunc: (columnDefinition?.aggFunc as OptionalAggFunc) || undefined,
            });
        }

        this.setShouldResetTable();
    };

    private readonly setShouldResetTable = (): void => {
        this.dataOperationMode = DataOperationMode.RESET_TABLE;
    };

    private readonly onOptionsMenuItemChange = (selectedOptionsMenuItem: OptionsMenuItem): void => {
        if (this.gridApi) {
            this.setShouldResetTable();
            this.props.setTableViewOptionsMenuItemAndViewFilter?.(0, selectedOptionsMenuItem, {});
            this.getColumnDefinitions({
                keepVisibility: true,
                refreshDataSource: false,
                keepGrouping: true,
            });
        }
    };

    private readonly getRowId: GridOptions<{ _id: string }>['getRowId'] = ({ data }): string => data._id;

    private readonly getRowClass: GridOptions['getRowClass'] = params => {
        if (!params.data || this.props.fieldProperties.isChangeIndicatorDisabled) return undefined;

        if (params.data.__isGroup) {
            return 'ag-user-row-group';
        }

        if (
            params.data?.__phantom === undefined &&
            (params.data?.__action === RecordActionType.ADDED || params.data?.__action === RecordActionType.MODIFIED)
        ) {
            return 'ag-row-edited';
        }

        return undefined;
    };

    private readonly selectionColumnDef = getSelectionColumnDef({
        contextType: this.props.contextType,
        fieldProperties: this.props.fieldProperties,
    });

    private readonly gridOptions: GridOptions = {
        getRowClass: this.getRowClass,
        selectionColumnDef: this.selectionColumnDef,
    };

    private readonly getColumnDefinitions = ({
        refreshDataSource = true,
        resetSelection = true,
        keepVisibility = false,
        resetTableFilters = false,
        keepGrouping = false,
    }: {
        refreshDataSource?: boolean;
        resetSelection?: boolean;
        keepVisibility?: boolean;
        resetTableFilters?: boolean;
        keepGrouping?: boolean;
    } = {}): void => {
        const columnsData = (this.props.fieldProperties.columns || []).map(columnDefinition => ({
            columnDefinition,
            bind: convertDeepBindToPath(columnDefinition.properties.bind) as string,
            elementId: getNestedFieldElementId(columnDefinition),
        }));
        let columns = getColumns({
            accessBindings: this.props.accessBindings,
            columnsData,
            currentTableView: this.props.tableUserSettings?.$current?.content?.[0],
            customizedOrderBy: getTableViewSortOrder(this.props.tableUserSettings),
            elementId: this.props.elementId,
            enumTypes: this.props.enumTypes,
            fieldProperties: () => this.props.fieldProperties,
            isDisabled: this.isDisabled(),
            // eslint-disable-next-line react/no-access-state-in-setstate
            groupBy: this.state.groupByColumn?.colId,
            // eslint-disable-next-line react/no-access-state-in-setstate
            hasFloatingFilters: this.state.hasFloatingFilters,
            isParentDisabled: () => !!this.props.isParentDisabled,
            isReadOnly: this.isReadOnly(),
            level: 0,
            locale: this.props.locale,
            lookupSelectionMode: this.props.selectionMode,
            nodeTypes: this.props.nodeTypes,
            dataTypes: this.props.dataTypes,
            pageNode: this.props.pageNode,
            screenId: this.props.screenId,
            value: () => this.props.value,
        });

        if (keepVisibility) {
            const columnHidden = getTableViewColumnHidden(this.props.tableUserSettings);
            columns = columns.map<AgGridColumnConfigWithScreenIdAndColDef>(k => {
                const colState = this.state.columnStates?.find(cs => cs.colId === k.field);
                if (colState) {
                    return { ...k, hide: !!colState.hide };
                }

                if (columnHidden && k.field) {
                    return { ...k, hide: !!columnHidden[k.field] };
                }

                return k;
            });
        }

        this.setState({ columns }, () => {
            if (this.gridApi) {
                callGridMethod(this.gridApi, 'stopEditing');
                if (resetSelection) {
                    this.onUnselectAll();
                }
                /**
                 * Column definitions have to be reset in order the new properties based on the new collection value
                 * object and event listeners to take effect
                 */
                callGridMethod(this.gridApi, 'setGridOption', 'columnDefs', this.state.columns);
                // AG-grid will not update the order of columns unless we explicitly tell it so
                this.sortColumns();
                if (refreshDataSource) {
                    /**
                     * Datasource has to be reset so the grid detects the new collection value and rerenders itself.
                     */
                    callGridMethod(this.gridApi, 'setGridOption', 'serverSideDatasource', this.serverSideDataSource);
                    if (resetTableFilters) {
                        callGridMethod(
                            this.gridApi,
                            'setFilterModel',
                            getTableViewFilter(this.props.tableUserSettings),
                        );
                    }
                }
                if (keepGrouping && this.state.groupByColumn) {
                    this.groupByColumn({
                        api: this.gridApi,
                        colDef: this.state.groupByColumn,
                        aggFunc: this.state.groupByColumn.aggFunc as DateAggregation,
                    });
                }
            }
        });
    };

    private readonly hasFilterableColumns = (): boolean => this.state.columns.filter(c => c.filter).length > 0;

    private readonly toggleFloatingFilters = (): void => {
        this.setState(
            state => ({
                hasFloatingFilters: !state.hasFloatingFilters,
                autoGroupColumnDef: { ...state.groupByColumn, floatingFilter: !state.hasFloatingFilters },
                groupByColumn: state.groupByColumn
                    ? ({
                          ...state.groupByColumn,
                          floatingFilter: !state.hasFloatingFilters,
                      } as TableDesktopState['groupByColumn'])
                    : undefined,
            }),
            () => {
                this.props.onTelemetryEvent?.(`tableFloatingFilterRowToggled-${this.props.elementId}`, {
                    hasFloatingFilters: this.state.hasFloatingFilters,
                    elementId: this.props.elementId,
                    screenId: this.props.screenId,
                });
                this.getColumnDefinitions({
                    refreshDataSource: false,
                    resetSelection: false,
                    keepVisibility: true,
                    keepGrouping: true,
                });
            },
        );
    };

    private readonly exportToExcelWithInfiniteScroll = async (
        target: 'excel' | 'csv',
        options: BaseExportParams,
    ): Promise<void> => {
        const gapi = this.gridApi;

        this.props.onTelemetryEvent?.(`tableDataExported-${target}-${this.props.elementId}`, {
            screenId: this.props.screenId,
            elementId: this.props.elementId,
            target,
        });

        if (gapi) {
            if (!this.props.fieldProperties.isTransient) {
                this.props.value.cleanCollectionData();
            }
            const pageData = await this.props.value.getPagesIteratively({
                filters: this.getFilters(getFilterModel(gapi, this.state.groupByColumn?.field)),
                tableFieldProperties: this.props.fieldProperties,
                pageSize: 1000,
                cleanMetadata: false,
                selectedOptionsMenuItem: this.props.selectedOptionsMenuItem,
            });

            gapi.applyServerSideRowData({
                successParams: {
                    rowData: pageData,
                    rowCount: pageData.length,
                },
            });

            if (target === 'excel') {
                callGridMethod(gapi, 'exportDataAsExcel', options);
            } else {
                callGridMethod(gapi, 'exportDataAsCsv', options);
            }
        }
    };

    private readonly exportToExcelWithPagination = (target: 'excel' | 'csv', options: BaseExportParams): void => {
        this.props.onTelemetryEvent?.(`tableDataExported-${target}-${this.props.elementId}`, {
            screenId: this.props.screenId,
            elementId: this.props.elementId,
            target,
        });
        getSafeGridApiContext(
            currentPage => {
                if (typeof this.props.setGlobalLoading === 'function') {
                    this.props.setGlobalLoading(true);
                }

                const gettingPages = setInterval(() => {
                    getSafeGridApiContext(
                        isLastPageFound => {
                            if (isLastPageFound) {
                                clearInterval(gettingPages);
                                if (target === 'excel') {
                                    callGridMethod(this.gridApi, 'exportDataAsExcel', options);
                                } else {
                                    callGridMethod(this.gridApi, 'exportDataAsCsv', options);
                                }

                                callGridMethod(this.gridApi, 'paginationGoToPage', currentPage || 0);

                                if (typeof this.props.setGlobalLoading === 'function') {
                                    this.props.setGlobalLoading(false);
                                }

                                this.setState({ isExportingExcel: false });
                            } else if (!this.isFetching) {
                                callGridMethod(this.gridApi, 'paginationGoToNextPage');
                                this.setState({ isExportingExcel: true });
                            }
                        },
                        this.gridApi,
                        'paginationIsLastPageFound',
                    );
                }, 500);
            },
            this.gridApi,
            'paginationGetCurrentPage',
        );
    };

    private readonly exportToExcel = async (target: 'excel' | 'csv'): Promise<void> => {
        if (this.props.elementId === navigationPanelId && this.props.fieldProperties.node) {
            try {
                await exportTableData({
                    node: String(this.props.fieldProperties.node),
                    columns: this.state.columns,
                    nodeTypes: this.props.nodeTypes,
                    filter: this.props.value.getFilter({ level: 0 }),
                    orderBy: this.props.value.getOrderBy({ level: 0 }),
                    outputFormat: target === 'excel' ? 'xlsx' : target,
                });
                showToast(localize('@sage/xtrem-ui/table-export-started', 'Export started.'), { type: 'info' });
            } catch (error) {
                showToast(localize('@sage/xtrem-ui/table-export-failed', 'Failed to export main list content'), {
                    type: 'error',
                });
            }
            return;
        }

        getSafeGridApiContext(
            columns => {
                const getBindIndexFromColumnId = (column: Column): { bind: string; index: number } => {
                    const colId = column.getColDef().field!;
                    const tempIndex = Number(column.getColId().replace(`${colId}_`, ''));
                    const index = Number.isNaN(Number(tempIndex)) ? 0 : tempIndex;

                    return { bind: colId, index };
                };

                const options: BaseExportParams = {
                    columnKeys: getColumnsToExport(columns ?? []),
                    processCellCallback: (params: ProcessCellForExportParams) => {
                        const { bind, index } = getBindIndexFromColumnId(params.column);
                        const filteredColumns = (this.props.fieldProperties.columns || []).filter(
                            c =>
                                getNestedFieldElementId(c) === bind ||
                                getNestedFieldElementId(c).startsWith(`${bind}__`),
                        );
                        const column = filteredColumns[index];
                        return column ? cellExportFormatter(column, params.value) : '';
                    },
                    fileName:
                        getFieldTitle(this.props.screenId, this.props.fieldProperties, null) || this.props.elementId,
                };

                if (this.isInfiniteScroll()) {
                    this.exportToExcelWithInfiniteScroll(target, options);
                } else {
                    this.exportToExcelWithPagination(target, options);
                }
            },
            this.gridApi,
            'getColumns',
        );
    };

    private readonly getCacheBlockSize = (): number => this.props.fieldProperties.pageSize || 20;

    private readonly isServerSideGroup: GridOptions['isServerSideGroup'] = (dataItem): boolean =>
        Boolean(dataItem.__isGroup);

    private readonly excludeInternalColumnsFilter = (column: Column): boolean => {
        const colId = column.getColId();
        return (
            colId !== undefined &&
            colId !== COLUMN_ID_ROW_SELECTION &&
            colId !== COLUMN_ID_ROW_ACTIONS &&
            colId !== COLUMN_ID_VALIDATIONS
        );
    };

    private readonly getAllColumnIds = (gridApi: GridApi | null = this.gridApi): string[] => {
        return (callGridMethod(gridApi, 'getColumns') ?? [])
            .filter(this.excludeInternalColumnsFilter)
            .map(column => column.getColId());
    };

    private readonly getAllDisplayedColumnIds = (gridApi: GridApi | null = this.gridApi): string[] => {
        return (callGridMethod(gridApi, 'getAllDisplayedColumns') ?? []).map(column => {
            const colId = column.getColId();
            return colId;
        });
    };

    autoSizeAllColumns = (gridApi: GridApi | null = this.gridApi): void => {
        if (!gridApi) {
            return;
        }
        callGridMethod(gridApi, 'autoSizeColumns', this.getAllColumnIds(gridApi));
        const availableWidth = this.containerRef.current?.clientWidth ?? 0;
        const usedWidth = (gridApi.getAllDisplayedColumns() ?? []).reduce((acc, c) => acc + c.getActualWidth(), 0);
        if (availableWidth - usedWidth > 20) {
            setTimeout(() => {
                callGridMethod(gridApi, 'sizeColumnsToFit');
            }, 0);
        }
        if (this.state.resizedColumns.length > 0) {
            callGridMethod(this.gridApi, 'setColumnWidths', this.state.resizedColumns);
        }
    };

    private readonly onFirstDataRendered = (event: FirstDataRenderedEvent): void => {
        this.adjustTableHeight(event);
        setTimeout(() => {
            this.autoSizeAllColumns(event.api);
        }, 100);
    };

    private readonly canBeGroupedBy = (column: Column): boolean => {
        const supportedFieldTypes: FieldKey[] = [
            FieldKey.Reference,
            FieldKey.Label,
            FieldKey.Date,
            FieldKey.RelativeDate,
        ];

        const colDef: AgGridColumnConfigWithScreenIdAndColDef =
            column.getColDef() as AgGridColumnConfigWithScreenIdAndColDef;
        if (colDef.cellRendererParams?.fieldProperties?.isTransient) {
            return false;
        }
        return (
            (supportedFieldTypes.includes(colDef.type as FieldKey) && this.isNavigationPanel()) ||
            column.getId() === AUTO_COLUMN_ID
        );
    };

    private readonly ungroupByColumn = (
        {
            colDef,
            api,
        }: {
            colDef?: AgGridColumnConfigWithScreenIdAndColDef;
            api: GridApi;
        },
        cb?: () => void,
    ): void => {
        let newColumns: AgGridColumnConfigWithScreenIdAndColDef[];
        this.setState(
            prevState => {
                newColumns = [
                    ...(prevState.columns ?? []).map(c => {
                        const isPrevGroupByColumn =
                            prevState.groupByColumn && c.field === prevState.groupByColumn.field;
                        return {
                            ...c,
                            ...(isPrevGroupByColumn && { filter: prevState.groupByColumn.filter }),
                            aggFunc: null,
                            rowGroup: false,
                            hide: c.field === (colDef?.field ?? prevState.groupByColumn?.field) ? false : c.hide,
                        };
                    }),
                ];
                return {
                    groupByColumn: undefined,
                    columns: newColumns,
                    autoGroupColumnDef: undefined,
                };
            },
            () => {
                this.onUnselectAll();
                setTimeout(() => {
                    const filter = getTableViewFilter(this.props.tableUserSettings);
                    // TODO: revisit this fix. It fixes XT-39123.
                    callGridMethod(api, 'setFilterModel', filter);
                    callGridMethod(this.gridApi, 'setGridOption', 'columnDefs', newColumns);
                    if (this.props.setTableViewGrouping) {
                        this.props.setTableViewGrouping(0, undefined);
                    }
                    cb?.();
                    this.autoSizeAllColumns();
                }, 10);
            },
        );
    };

    private readonly groupByColumn = (
        {
            colDef,
            api,
            aggFunc,
            sort,
            sortIndex,
        }: {
            api: GridApi;
            colDef: AgGridColumnConfigWithScreenIdAndColDef;
            aggFunc?: 'day' | 'month' | 'year';
            sort?: 'asc' | 'desc';
            sortIndex?: number;
        },
        cb?: () => void,
    ): void => {
        let newColumns: AgGridColumnConfigWithScreenIdAndColDef[];

        this.setState(
            currState => {
                newColumns = [
                    ...(currState.columns ?? []).map(c => {
                        const isGroupByColumn = c.field === colDef.field;
                        const isPrevGroupByColumn =
                            currState.groupByColumn && c.field === currState.groupByColumn.field;
                        let hide = c.hide;
                        let filter = c.filter;

                        if (isGroupByColumn) {
                            hide = true;
                            filter = false;
                            c.aggFunc = aggFunc ?? null;
                        } else if (isPrevGroupByColumn) {
                            hide = false;
                            filter = currState.groupByColumn.filter;
                            c.aggFunc = null;
                        }
                        return {
                            ...c,
                            rowGroup: isGroupByColumn,
                            hide,
                            filter,
                        };
                    }),
                ];

                const { cellRenderer, cellEditor, editable, resizable, ...colDefProps } = colDef;
                const menuTabs: ColumnMenuTab[] = ['filterMenuTab', 'generalMenuTab'];

                return {
                    groupByColumn: { ...colDef, aggFunc },
                    autoGroupColumnDef: {
                        ...colDefProps,
                        colId: AUTO_COLUMN_ID,
                        lockPosition: 'left',
                        rowGroup: true,
                        menuTabs,
                        flex: undefined,
                        width: 250,
                        minWidth: 250,
                        resizable: true,
                        editable: false,
                        ...(this.props.groupTitle && { headerName: this.props.groupTitle }),
                        // by default sort by grouping column in ascending order
                        sort: sort !== undefined ? sort : (colDef.sort ?? 'asc'),
                        sortIndex,
                        sortable: true,
                        headerName: this.props.groupTitle ? this.props.groupTitle : colDef.headerName,
                        cellRendererParams: {
                            ...colDefProps.cellRendererParams,
                            innerRenderer: cellRenderer,
                        },
                    },
                    columns: newColumns,
                };
            },
            () => {
                setTimeout(() => {
                    callGridMethod(api, 'setGridOption', 'columnDefs', newColumns);
                    cb?.();
                    this.autoSizeAllColumns();
                }, 0);
            },
        );
    };

    private readonly updateColDefsStates = (): Promise<void> | undefined => {
        return (
            getSafeGridApiContext(
                (newColumns: AgGridColumnConfigWithScreenIdAndColDef[] | undefined) => {
                    return new Promise<void>(resolve => {
                        this.setState({ columns: newColumns ?? [] }, () => {
                            callGridMethod(this.gridApi, 'setGridOption', 'columnDefs', this.state.columns);
                            resolve();
                        });
                    });
                },
                this.gridApi,
                'getColumnDefs',
            ) ?? Promise.resolve()
        );
    };

    private readonly sortColumns = (): void => {
        (callGridMethod(this.gridApi, 'getColumnDefs') ?? []).forEach((c: AgGridColumnConfigWithScreenIdAndColDef) => {
            if (this.gridApi && c.colId && c.context.xtremSortIndex) {
                callGridMethod(this.gridApi, 'moveColumns', [c.colId], c.context.xtremSortIndex + this.moveStartIndex);
            }
        });
    };

    private readonly getGroupByMenuItem = ({
        isGroupedByThisColumn,
        colDef,
        api,
    }: {
        isGroupedByThisColumn: boolean;
        colDef: AgGridColumnConfigWithScreenIdAndColDef;
        api: GridApi;
    }): MenuItemDef => {
        if ([FieldKey.Date, FieldKey.RelativeDate].includes(colDef.type as FieldKey)) {
            return {
                cssClasses: isGroupedByThisColumn ? ['e-table-field-ungroup'] : ['e-table-field-group-by'],
                name: isGroupedByThisColumn
                    ? localize('@sage/xtrem-ui/ungroup', 'Ungroup')
                    : localize('@sage/xtrem-ui/group-by', 'Group by'),
                icon: isGroupedByThisColumn
                    ? renderToStaticMarkup(
                          // eslint-disable-next-line react/jsx-indent
                          <div style={{ position: 'relative' }}>
                              <Icon type="bullet_list" />
                              {/* Strike-through for ungroup */}
                              <div
                                  style={{
                                      position: 'absolute',
                                      width: '24px',
                                      bottom: '2px',
                                      left: '2px',
                                      borderTop: '1px solid black',
                                      transform: 'rotate(-45deg)',
                                      transformOrigin: '0% 0%',
                                  }}
                              />
                          </div>,
                      )
                    : renderToStaticMarkup(<Icon type="bullet_list" />),
                subMenu: isGroupedByThisColumn
                    ? undefined
                    : [
                          {
                              cssClasses: ['e-table-field-group-by-year'],
                              name: localize('@sage/xtrem-ui/group-by-year', 'Year'),
                              action: (): void => {
                                  setTimeout(() => {
                                      this.groupByColumn({ api, colDef, aggFunc: 'year' });
                                  }, 10);
                              },
                          },
                          {
                              cssClasses: ['e-table-field-group-by-month'],
                              name: localize('@sage/xtrem-ui/group-by-month', 'Month'),
                              action: (): void => {
                                  setTimeout(() => {
                                      this.groupByColumn({ api, colDef, aggFunc: 'month' });
                                  }, 10);
                              },
                          },
                          {
                              cssClasses: ['e-table-field-group-by-day'],
                              name: localize('@sage/xtrem-ui/group-by-day', 'Day'),
                              action: (): void => {
                                  setTimeout(() => {
                                      this.groupByColumn({ api, colDef, aggFunc: 'day' });
                                  }, 10);
                              },
                          },
                      ],
                action: (): void => {
                    if (isGroupedByThisColumn) {
                        this.ungroupByColumn({ colDef, api });
                    }
                },
            };
        }
        return {
            cssClasses: isGroupedByThisColumn ? ['e-table-field-ungroup'] : ['e-table-field-group-by'],
            name: isGroupedByThisColumn
                ? localize('@sage/xtrem-ui/ungroup', 'Ungroup')
                : localize('@sage/xtrem-ui/group-by-this-column', 'Group by this column'),
            icon: isGroupedByThisColumn
                ? renderToStaticMarkup(
                      // eslint-disable-next-line react/jsx-indent
                      <div style={{ position: 'relative' }}>
                          <Icon type="bullet_list" />
                          {/* Strike-through for ungroup */}
                          <div
                              style={{
                                  position: 'absolute',
                                  width: '24px',
                                  bottom: '2px',
                                  left: '2px',
                                  borderTop: '1px solid black',
                                  transform: 'rotate(-45deg)',
                                  transformOrigin: '0% 0%',
                              }}
                          />
                      </div>,
                  )
                : renderToStaticMarkup(<Icon type="bullet_list" />),
            action: (): void => {
                if (isGroupedByThisColumn) {
                    this.ungroupByColumn({ colDef, api });
                } else {
                    setTimeout(() => {
                        this.groupByColumn({ api, colDef });
                    }, 10);
                }
            },
        };
    };

    private readonly getMainMenuItems: GetMainMenuItems = ({ column, api }) => {
        /**
         * params.defaultItems are
        [
            "pinSubMenu",
            "separator",
            "autoSizeThis",
            "autoSizeAll",
            "separator",
            "separator",
            "resetColumns"
        ]
         */
        const defaultMenuItems = ['pinSubMenu', 'separator', 'autoSizeThis', 'autoSizeAll'];
        const colId = column.getColId();
        const isAutoColumn = colId === AUTO_COLUMN_ID;
        const colDef = isAutoColumn ? this.state.groupByColumn : this.state.columns.find(c => c.field === colId);
        if (!colDef) {
            throw new Error('Unable to find column definition');
        }
        const isGroupedByThisColumn = this.state.groupByColumn?.colId === colId || isAutoColumn;
        return this.canBeGroupedBy(column)
            ? [...defaultMenuItems, 'separator', this.getGroupByMenuItem({ api, isGroupedByThisColumn, colDef })]
            : defaultMenuItems;
    };

    onCellFocused = (params: CellFocusedEvent): void => {
        if (this.props.onFocus) {
            this.props.onFocus(
                this.props.screenId,
                this.props.elementId,
                String(params.rowIndex || ''),
                typeof params.column === 'object' && params.column ? params.column.getColId() : '',
            );
        }
        if (this.props.selectionMode === 'single') {
            callGridMethod(this.gridApi, 'forEachNode', node => {
                if (node.rowIndex === params.rowIndex) {
                    node.setSelected(true);
                }
            });
        }
    };

    onCellKeyDown = (event: CellKeyDownEvent): void => {
        const keyboardEvent = event.event as KeyboardEvent;
        if (keyboardEvent?.key === 'Enter') {
            this.props.onRowClick?.(event.data._id, keyboardEvent)();
        }
        if (event.data?.__phantom && keyboardEvent.key === 'Enter') {
            if (keyboardEvent.getModifierState('Meta') || keyboardEvent.getModifierState('Control')) {
                this.props.onTelemetryEvent?.(`tablePhantomRowCommittedByKeyboard-${this.props.elementId}`, {
                    screenId: this.props.screenId,
                    elementId: this.props.elementId,
                });
                tryToCommitPhantomRow({
                    api: event.api,
                    screenId: this.props.screenId,
                    elementId: this.props.elementId,
                    value: this.props.value,
                });
            } else if (event.column.getColId() === COLUMN_ID_ROW_ACTIONS) {
                this.props.value.resetRowToDefaults({
                    id: event.data._id,
                    level: event.data.__level,
                    parentId: event.data.__parentId,
                    isOrganicChange: true,
                    resetDirtiness: true,
                });
                callGridMethod(event.api, 'stopEditing');
                callGridMethod(event.api, 'setFocusedCell', 0, COLUMN_ID_ROW_ACTIONS, 'top');
            }
            return;
        }
        const isCtrlOrCmd = keyboardEvent.ctrlKey || keyboardEvent.metaKey;

        if (isCtrlOrCmd && keyboardEvent.key === 'c') {
            keyboardEvent.preventDefault();
            copySelectedCellValue(String(event.value ?? ''));
        }

        if (isCtrlOrCmd && keyboardEvent.key === 'v') {
            keyboardEvent.preventDefault();
            pasteToSelectedCell(event.api);
        }
    };

    getAllNotVisibleNotHiddenColumns = (tableProperties: InternalTableProperties): string[] => {
        return (tableProperties.columns || [])
            .filter(c => {
                return (c.properties as any).isHiddenOnMainField;
            })
            .map(c => {
                return convertDeepBindToPathNotNull(c.properties.bind);
            });
    };

    hideUnhideColumns = (event?: ColumnEverythingChangedEvent): void => {
        const gridApi = event?.api ?? this.gridApi;
        if (
            !gridApi ||
            gridApi.isDestroyed() ||
            !this.props.fieldProperties.canUserHideColumns ||
            (event && ['toolPanelUi', 'api', 'gridOptionsUpdated'].includes(event.source))
        ) {
            return;
        }

        const hiddenColumns = this.state?.columnStates?.filter(c => c.colId && c.hide).map(c => c.colId as string);
        const allColumns = this.getAllColumnIds(gridApi);
        const visibleColumns = this.getAllDisplayedColumnIds();
        const notVisibleNotHiddenColumns = this.getAllNotVisibleNotHiddenColumns(this.props.fieldProperties);
        const currentlyHiddenColumns = difference(allColumns, visibleColumns);

        const columnsToHide = visibleColumns
            .filter(c => c !== COLUMN_ID_VALIDATIONS)
            .filter(colId => hiddenColumns && hiddenColumns.find(colBind => isColumnBindVariant(colBind, colId)));

        const columnsToUnhide = currentlyHiddenColumns
            .filter(c => c !== COLUMN_ID_VALIDATIONS && c !== this.state.groupByColumn?.field)
            .filter(colId => hiddenColumns && !hiddenColumns.find(colBind => isColumnBindVariant(colBind, colId)))
            .filter(colId => !notVisibleNotHiddenColumns.find(colBind => isColumnBindVariant(colBind, colId)));

        callGridMethod(gridApi, 'setColumnsVisible', columnsToHide, false);
        callGridMethod(gridApi, 'setColumnsVisible', columnsToUnhide, true);
        this.autoSizeAllColumns();
    };

    onFilterByErrors = async (): Promise<void> => {
        this.props.onTelemetryEvent?.(`tableFilteredByErrors-${this.props.elementId}`, {
            screenId: this.props.screenId,
            elementId: this.props.elementId,
        });
        this.isFilteringErrors = true;
        this.dataOperationMode = DataOperationMode.FILTER_ERRORS;
        if (this.gridApi) {
            callGridMethod(this.gridApi, 'setGridOption', 'serverSideDatasource', this.serverSideDataSource);
        }
    };

    onUnsetFilterByErrors = async (): Promise<void> => {
        this.isFilteringErrors = false;
        this.props.value.resetFilter();
        this.dataOperationMode = DataOperationMode.FETCH_PAGE;
        if (this.gridApi) {
            callGridMethod(this.gridApi, 'setFilterModel', null);
            callGridMethod(this.gridApi, 'setGridOption', 'serverSideDatasource', this.serverSideDataSource);
        }
    };

    getDefaultHeight = (): number => {
        return (
            this.props.fixedHeight ||
            (this.props.numberOfVisibleRows || this.props.fieldProperties.pageSize || 20) * ROW_HEIGHT + 85
        );
    };

    private readonly adjustTableHeight = ({ api = this.gridApi }: { api?: GridApi | null } = {}): void => {
        if (!this.containerRef.current) {
            return;
        }

        if (this.props.fixedHeight) {
            this.containerRef.current.style.height = `${this.props.fixedHeight}px`;
            return;
        }

        if (!this.props.fieldProperties.canAddNewLine || !api) {
            return;
        }
        // A minimum row height must be set so we have some place to display the empty placeholders
        getSafeGridApiContext(
            rowCount => {
                const renderedRowCount = Math.max(rowCount, 4);
                const containerHeight = this.containerRef.current?.clientHeight || 0;
                let size = Math.min(renderedRowCount * ROW_HEIGHT + 164, this.getDefaultHeight());
                if (this.props.fieldProperties.canAddNewLine && !this.isDisabled()) {
                    size += ROW_HEIGHT;
                }
                if (containerHeight === size) {
                    return;
                }
                if (this.containerRef.current) {
                    this.containerRef.current.style.height = `${size}px`;
                }
            },
            api,
            'getDisplayedRowCount',
        );
    };

    async updateColumnState(): Promise<void> {
        getSafeGridApiContext(
            columnStates => {
                return new Promise<void>(resolve => {
                    this.setState({ columnStates }, resolve);
                });
            },
            this.gridApi,
            'getColumnState',
        );
    }

    private readonly onColumnPanelHiddenStateChange = async (colId: string, isHidden: boolean): Promise<void> => {
        if (this.gridApi) {
            callGridMethod(this.gridApi, 'setColumnVisible', colId, isHidden);
            await this.updateColumnState();
            await this.updateColDefsStates();
        }

        if (this.state.columnStates && this.props.setTableViewColumnHidden) {
            const visible = this.getAllDisplayedColumnIds();
            const hidden = difference(this.getAllColumnIds(), visible);
            const hiddenColumns: Dict<boolean> = {};

            visible.forEach(column => {
                hiddenColumns[column] = false;
            });

            hidden.forEach(column => {
                hiddenColumns[column] = true;
            });

            this.props.setTableViewColumnHidden(0, hiddenColumns);
        }
    };

    private readonly getCurrentColumnOrder = (): string[] => {
        if (!this.state.columnStates) {
            return [];
        }
        return this.state.columnStates
            .filter(
                columnState =>
                    columnState.colId !== undefined &&
                    columnState.colId !== COLUMN_ID_ROW_SELECTION &&
                    columnState.colId !== COLUMN_ID_ROW_ACTIONS &&
                    columnState.colId !== COLUMN_ID_VALIDATIONS &&
                    columnState.colId !== COLUMN_ID_AUTO_COLUMN,
            )
            .map(columnState => columnState.colId as string);
    };

    private readonly moveStartIndex = this.props.fieldProperties.canSelect ? 1 : 0;

    private readonly onColumnPanelOrderChangeChange = async (colIds: string[]): Promise<void> => {
        if (this.gridApi) {
            callGridMethod(this.gridApi, 'moveColumns', colIds, this.moveStartIndex);
            await this.updateColumnState();
            await this.updateColDefsStates();
        }

        if (this.state.columnStates && this.props.setTableViewColumnOrder) {
            this.props.setTableViewColumnOrder(0, this.getCurrentColumnOrder());
        }
    };

    private readonly onSortChanged = (event: SortChangedEvent): void => {
        if (event.source === 'gridOptionsUpdated') {
            return;
        }
        getSafeGridApiContext(
            columnState => {
                const groupBySort = columnState.find(c => c.colId === AUTO_COLUMN_ID)?.sort;
                const grouping = getTableViewGrouping(this.props.tableUserSettings);

                if (
                    groupBySort &&
                    this.state.groupByColumn?.field &&
                    groupBySort !== grouping?.key &&
                    this.props.setTableViewGrouping
                ) {
                    this.props.setTableViewGrouping(0, {
                        key: this.state.groupByColumn.field,
                        sort: groupBySort,
                        aggFunc: (this.state.groupByColumn.aggFunc as OptionalAggFunc) || undefined,
                    });
                }
            },
            event.api,
            'getColumnState',
        );

        const sortOrder: TableViewSortedColumn[] = event.api
            .getColumnState()
            .filter(
                columnState =>
                    columnState.colId !== undefined &&
                    columnState.colId !== COLUMN_ID_ROW_SELECTION &&
                    columnState.colId !== COLUMN_ID_ROW_ACTIONS &&
                    columnState.colId !== COLUMN_ID_VALIDATIONS &&
                    columnState.colId !== COLUMN_ID_AUTO_COLUMN,
            )
            .map(columnState => {
                return {
                    colId: columnState.colId as string,
                    sort: columnState.sort ?? null,
                    sortIndex: columnState.sortIndex ?? null,
                };
            });

        if (this.props.setTableViewSortOrder) {
            this.props.setTableViewSortOrder(0, sortOrder);
        }

        if (this.state.groupByColumn) {
            // Force scroll to top when sorting with active grouping.
            // Otherwise, ag-Grid may call getRows() with a high startRow,
            // but cursors for those rows no exist after resetting the data source.
            event.api.ensureIndexVisible(0, 'top');
        }
        this.setShouldResetTable();
    };

    renderCalendarView(): React.ReactNode {
        const eventCard = getCardDefinitionFromColumns({
            columns: this.props.fieldProperties.columns,
            mobileCard: this.props.fieldProperties.mobileCard,
            screenId: this.props.screenId,
            isGreaterThanSmall: true,
            hiddenColumns: this.props.fieldProperties.hiddenColumns,
            sortColumns: this.props.fieldProperties.sortColumns,
        });

        if (!this.props.fieldProperties.startDateField) {
            // This should never be the case because the calendar view is only available if the startDateField property is defined
            return null;
        }

        return (
            <div className="e-navigation-panel-calendar-wrapper">
                <AsyncCalendarBodyComponent
                    cardColor={this.props.fieldProperties.cardColor}
                    elementId={this.props.elementId}
                    endDateField={this.props.fieldProperties.endDateField}
                    eventCard={eventCard}
                    selectedView={this.state.selectedCalendarView}
                    fieldProperties={this.props.fieldProperties}
                    fixedHeight={this.props.fixedHeight ? this.props.fixedHeight - 40 : undefined}
                    isGreaterThanSmall={true}
                    onEventClick={(_id, event, isModifierKeyPushed): void => {
                        this.props.onRowClick?.(_id, event, isModifierKeyPushed)();
                    }}
                    screenId={this.props.screenId}
                    selectedOptionsMenuItem={this.props.selectedOptionsMenuItem}
                    startDateField={this.props.fieldProperties.startDateField}
                    value={this.props.value}
                    isEventMovable={this.props.fieldProperties.isEventMovable}
                />
            </div>
        );
    }

    private readonly onCellClicked = async (event: CellClickedEvent): Promise<void> => {
        if (event.data?.__phantom && event.column.getColId() === COLUMN_ID_ROW_ACTIONS) {
            await this.props.value.resetRowToDefaults({
                id: event.data._id,
                level: event.data.__level,
                parentId: event.data.__parentId,
                isOrganicChange: true,
                resetDirtiness: true,
            });
        }
    };

    private readonly onCellEditingStopped = async (event: CellEditingStoppedEvent): Promise<void> => {
        const isPhantom = event.data?.__phantom !== undefined;
        if (!isPhantom) {
            return;
        }
        if (event.value !== event.oldValue) {
            const columnId = event.colDef.field!;
            const newValue = event.data[event.colDef.field!];
            let mergedValue = this.props.value.getMergedValue({
                recordId: event.data._id,
                columnId,
                value: newValue,
                level: event.data.__level,
            });
            const dirtyColumns = new Set([...mergedValue.__dirtyColumns, columnId]);
            const validationResult = await this.props.value.runValidationOnRecord({
                recordData: {
                    ...mergedValue,
                    __dirtyColumns: dirtyColumns,
                },
                changedColumnId: event.colDef.field,
            });

            mergedValue = this.props.value.getMergedValue({
                recordId: event.data._id,
                columnId,
                value: newValue,
                level: event.data.__level,
            });

            const updatedData = {
                ...mergedValue,
                __validationState: validationResult,
            };

            this.props.value.setRecordValue({
                recordData: updatedData,
                level: event.data.__level,
                toBeMarkedAsDirty: Array.from(dirtyColumns),
                isOrganicChange: true,
                changedColumnId: columnId,
            });
        }
    };

    private readonly onColumnResized = async (event: ColumnResizedEvent): Promise<void> => {
        if (event.finished && event.column) {
            const columnKey = event.column.getColId();
            const columnWidth = event.column.getActualWidth();

            const updatedColumn = { key: columnKey, newWidth: columnWidth };

            this.setState(prevState => {
                const currentColumns = [...prevState.resizedColumns];
                const columnIndex = currentColumns.findIndex(col => col.key === columnKey);

                if (columnIndex !== -1) {
                    currentColumns[columnIndex] = updatedColumn;
                } else {
                    currentColumns.push(updatedColumn);
                }

                return { resizedColumns: currentColumns };
            });
        }
    };

    private readonly onOpenColumnPanel = (): void => {
        this.setState({ isConfigurationDialogOpen: true });
    };

    private readonly onCloseColumnPanel = (): void => {
        this.setState({ isConfigurationDialogOpen: false });
    };

    private readonly noRowsOverlayComponentParams: GridOptions['noRowsOverlayComponentParams'] = {
        ...this.props,
        isEditable: !this.isReadOnly() && !this.isDisabled(),
    };

    private readonly onRowClicked: GridOptions['onRowClicked'] = e => {
        return onRowClick(this.props.onRowClick)(e);
    };

    private readonly rowClassRules: GridOptions['rowClassRules'] = getRowClassRules({
        canSelect: this.props.fieldProperties.canSelect || false,
        isChangeIndicatorDisabled: this.props.fieldProperties.isChangeIndicatorDisabled,
        openedRecordId: this.props.openedRecordId,
    });

    private readonly isInfiniteScroll = (): boolean => {
        return Boolean(this.props.isUsingInfiniteScroll || (this.props.fixedHeight && this.props.fixedHeight > 0));
    };

    async initColumnState(): Promise<void> {
        getSafeGridApiContext(
            columnStates => {
                return new Promise<void>(resolve => {
                    const columnHidden = getTableViewColumnHidden(this.props.tableUserSettings) || {};
                    columnStates.forEach((column: ColumnState) => {
                        const isTableViewColumnHidden = columnHidden[column.colId as string];
                        if (
                            isTableViewColumnHidden != null &&
                            Boolean(column.hide) !== Boolean(isTableViewColumnHidden)
                        ) {
                            column.hide = Boolean(columnHidden[column.colId as string]);
                        }
                    });

                    this.setState({ columnStates }, resolve);
                });
            },
            this.gridApi,
            'getColumnState',
        );
    }

    private readonly getRowHeight = (params: RowHeightParams): number => {
        return params?.data?.__isGroup ? GROUP_ROW_HEIGHT : ROW_HEIGHT;
    };

    private readonly onUnselectAll = async (): Promise<void> => {
        if (!this.gridApi) {
            return;
        }
        callGridMethod(this.gridApi, 'deselectAll');
        this.setState(
            {
                selectedRowCount: 0,
                isSelectAllChecked: false,
            },
            () => {
                const currentFieldProps = xtremRedux.getStore().getState().screenDefinitions[this.props.screenId]
                    .metadata.uiComponentProperties[this.props.elementId] as TableProperties | undefined;
                this.props.setFieldProperties?.(this.props.elementId, {
                    ...currentFieldProps,
                    selectedRecords: [],
                });
            },
        );
    };

    private readonly onSelectionChanged = async (
        event: Pick<SelectionChangedEvent, 'api'> & { source?: string },
    ): Promise<void> => {
        if (event.source === 'rowDataChanged') {
            // IMPORTANT: IGNORE when called from selectTableItems
            return;
        }
        const allNodes: IRowNode[] = [];
        event.api.forEachNode(node => {
            if (node.data) {
                allNodes.push(node);
            }
        });
        const selectedNodes = event.api.getSelectedNodes() ?? [];
        const selectedGridIds: string[] = selectedNodes.filter(s => !s.group).map(s => s.data._id);
        const currentFieldProps = xtremRedux.getStore().getState().screenDefinitions[this.props.screenId].metadata
            .uiComponentProperties[this.props.elementId] as TableProperties | undefined;
        const currentlySelectedIds = currentFieldProps?.selectedRecords ?? [];
        const shouldResetSelection =
            selectedNodes.length === 0 &&
            Object.values(event.api.getCacheBlockState() ?? {}).filter((b: any) => (b?.loadedRowCount ?? 0) > 0)
                .length > 1;
        const selectedIds = shouldResetSelection
            ? []
            : selectedGridIds.concat(
                  difference(
                      currentlySelectedIds,
                      allNodes.map(n => n.data._id),
                  ),
              );
        const newlySelected = difference(selectedIds, currentlySelectedIds);
        const newlyUnselected = shouldResetSelection
            ? currentlySelectedIds
            : difference(
                  currentlySelectedIds.filter(id => this.getRowNode({ id })),
                  selectedIds,
              );
        let selectedRecords = currentlySelectedIds;
        if (newlySelected.length > 0 || newlyUnselected.length > 0) {
            selectedRecords = difference(currentlySelectedIds, newlyUnselected).concat(newlySelected);
            this.props.setFieldProperties?.(this.props.elementId, {
                ...currentFieldProps,
                selectedRecords,
            });
            newlySelected.forEach(selectedId => {
                triggerFieldEvent(
                    this.props.screenId,
                    this.props.elementId,
                    'onRowSelected',
                    selectedId,
                    splitValueToMergedValue(this.props.value.getRawRecord({ id: selectedId })),
                );
            });
            newlyUnselected.forEach(selectedId => {
                triggerFieldEvent(
                    this.props.screenId,
                    this.props.elementId,
                    'onRowUnselected',
                    selectedId,
                    splitValueToMergedValue(this.props.value.getRawRecord({ id: selectedId })),
                );
            });
        }

        const selectionState = event.api.getServerSideSelectionState();
        if (selectionState === null) {
            this.setState({
                selectedRowCount: 0,
                isSelectAllChecked: false,
            });
            return;
        }
        let isSelectAllChecked = false;
        if ('selectAllChildren' in selectionState) {
            const nodeCount = allNodes.length;
            isSelectAllChecked =
                Boolean(selectionState.selectAllChildren) ||
                (selectedNodes.length > 0 &&
                    ((nodeCount > 0 && nodeCount === event.api.getSelectedNodes().filter(n => n.data).length) ||
                        selectedRecords.length > allNodes.length));
        }
        if (this.state.groupByColumn) {
            let selectedCount = 0;
            if (isSelectAllChecked) {
                const total = await getTotalRecordCount({
                    api: event.api,
                    fieldProperties: this.props.fieldProperties,
                    screenId: this.props.screenId,
                    node: this.props.fieldProperties.node!,
                    activeOptionsMenuItem: this.props.selectedOptionsMenuItem,
                    groupByColumnField: event.api.getRowGroupColumns()[0]?.getColId(),
                });
                selectedCount = (selectionState.toggledNodes ?? []).reduce((acc, node) => {
                    if (typeof node === 'string' || !node.nodeId) {
                        return acc;
                    }
                    const n = this.getRowNode({ id: node.nodeId });
                    if (!n) {
                        return acc;
                    }
                    if (node.selectAllChildren) {
                        return acc - (node.toggledNodes ?? []).length;
                    }
                    return acc - ((n.data.__groupCount ?? 0) - (node.toggledNodes ?? []).length);
                }, total);
            } else {
                selectedCount = (selectionState.toggledNodes ?? []).reduce((acc, node) => {
                    if (typeof node === 'string' || !node.nodeId) {
                        return acc;
                    }
                    const n = this.getRowNode({ id: node.nodeId });
                    if (!n) {
                        return acc;
                    }
                    if (node.selectAllChildren) {
                        return acc + ((n.data.__groupCount ?? 0) - (node.toggledNodes ?? []).length);
                    }
                    return acc + (node.toggledNodes ?? []).length;
                }, 0);
            }

            this.setState({
                selectedRowCount: selectedCount,
                isSelectAllChecked,
            });
        } else if (isSelectAllChecked) {
            const total = await getTotalRecordCount({
                api: event.api,
                fieldProperties: this.props.fieldProperties,
                screenId: this.props.screenId,
                node: this.props.fieldProperties.node!,
                activeOptionsMenuItem: this.props.selectedOptionsMenuItem,
                groupByColumnField: event.api.getRowGroupColumns()[0]?.getColId(),
            });
            this.setState({
                selectedRowCount: total - (selectionState.toggledNodes ?? []).length,
                isSelectAllChecked,
            });
        } else {
            this.setState({
                selectedRowCount: (selectionState.toggledNodes ?? []).length,
                isSelectAllChecked,
            });
        }
    };

    private readonly getRowSelection = (): GridOptions['rowSelection'] => {
        if (!this.props.fieldProperties.canSelect) {
            return undefined;
        }
        if (this.props.selectionMode === 'single') {
            return 'single';
        }
        return {
            mode: 'multiRow',
            groupSelects: 'filteredDescendants',
            selectAll: 'filtered',
        };
    };

    private readonly rowSelection = this.getRowSelection();

    render(): React.ReactNode {
        const domLayout = this.isInfiniteScroll() ? 'normal' : 'autoHeight';
        const hasBulkActions = (this.props.bulkActions ?? []).length > 0;
        const shouldRenderBulkActionBar = hasBulkActions && this.state.selectedRowCount > 0 && this.gridApi;
        const hasAggregatedGroupColumns = !!this.props.fieldProperties?.columns?.find(
            c => !!(c.properties as NestedGroupAggregations).groupAggregationMethod,
        );
        const columnPanelColumnStates = getColumnStatesForColumnPanel(
            this.props.screenId,
            (callGridMethod(this.gridApi, 'getColumnDefs') ?? []) as AgGridColumnConfigWithScreenIdAndColDef[],
            this.state.columnStates,
        );

        const addItemActions =
            resolveByValue<PageActionControlObject[] | undefined>({
                propertyValue: this.props.fieldProperties.addItemActions,
                rowValue: null,
                screenId: this.props.screenId,
                fieldValue: null,
                skipHexFormat: true,
            }) ?? [];

        const hasAddItemsButton = Boolean(
            (this.props.fieldProperties.canAddNewLine && !this.props.fieldProperties.isPhantomRowDisabled) ||
                addItemActions.filter(action => !action.isHidden).length > 0,
        );

        return (
            <>
                {process.env.NODE_ENV === 'test' && this.gridApi && <p data-testid="api" style={{ display: 'none' }} />}
                {shouldRenderBulkActionBar && this.gridApi && (
                    <DesktopTableBulkActionBar
                        screenId={this.props.screenId}
                        isSelectAllChecked={Boolean(this.state.isSelectAllChecked)}
                        bulkActions={this.props.bulkActions}
                        gridApi={this.gridApi}
                        selectedRowCount={this.state.selectedRowCount}
                        groupByColumn={this.state.groupByColumn}
                        onTelemetryEvent={this.props.onTelemetryEvent}
                        onClearSelection={this.onUnselectAll}
                    />
                )}
                {(!this.props.isLookupDialog || this.props.elementId === navigationPanelId) &&
                    !shouldRenderBulkActionBar && (
                        <DesktopTableHeaderComponent
                            canAddNewLine={Boolean(this.props.fieldProperties.canAddNewLine)}
                            columnPanelDisabled={
                                !!this.state.groupByColumn || !this.props.fieldProperties.canUserHideColumns
                            }
                            elementId={this.props.elementId}
                            fieldProperties={this.props.fieldProperties}
                            hasAddItemsButton={hasAddItemsButton}
                            hasCalendarView={!!this.props.fieldProperties.startDateField}
                            hasData={this.state.tableHasData}
                            hasFloatingFilters={this.state.hasFloatingFilters}
                            hasSidebar={Boolean(this.props.fieldProperties.sidebar)}
                            isDisabled={this.isDisabled()}
                            isExportDisabled={!!this.state.groupByColumn}
                            isReadOnly={this.isReadOnly()}
                            onExport={this.exportToExcel}
                            onFilterByErrors={this.onFilterByErrors}
                            onFocusPhantomRow={this.focusPhantomRowAndStartEditing}
                            onOpenColumnPanel={this.onOpenColumnPanel}
                            onOptionsMenuItemChange={this.onOptionsMenuItemChange}
                            onSwitchCalendarView={this.onSwitchCalendarView}
                            onSwitchView={this.onSwitchView}
                            onTelemetryEvent={this.props.onTelemetryEvent}
                            onToggleFilters={this.hasFilterableColumns() ? this.toggleFloatingFilters : undefined}
                            onUnsetFilterByErrors={this.onUnsetFilterByErrors}
                            screenId={this.props.screenId}
                            selectedCalendarView={this.state.selectedCalendarView}
                            selectedOptionsMenuItem={this.props.selectedOptionsMenuItem}
                            tableViewMode={this.props.fieldProperties.tableViewMode}
                            validationErrors={this.props.validationErrors}
                        />
                    )}
                <div
                    data-testid={`e-table-field-${this.props.elementId}`}
                    style={{
                        height: '100%',
                        overflow: 'hidden',
                    }}
                    className="e-table-field-desktop-wrapper"
                >
                    {this.props.fieldProperties.tableViewMode === 'calendar' && this.renderCalendarView()}
                    {(this.props.fieldProperties.tableViewMode === 'table' ||
                        !this.props.fieldProperties.tableViewMode) && (
                        <div
                            ref={this.containerRef}
                            style={{
                                height: this.isInfiniteScroll() ? `${this.getDefaultHeight()}px` : '100%',
                                maxHeight: '100%',
                                width: '100%',
                                visibility: this.state.isExportingExcel ? 'hidden' : 'visible',
                            }}
                            className="ag-theme-balham"
                        >
                            <AgGridReact
                                stopEditingWhenCellsLoseFocus={true}
                                gridId={this.gridId}
                                autoSizeStrategy={this.autoSizeStrategy}
                                autoGroupColumnDef={this.state.autoGroupColumnDef}
                                cacheBlockSize={this.getCacheBlockSize()}
                                columnDefs={this.state.columns}
                                components={frameworkComponents}
                                debug={hasAgGridLogging()}
                                defaultColDef={this.defaultColDef}
                                domLayout={domLayout}
                                floatingFiltersHeight={ROW_HEIGHT}
                                getMainMenuItems={this.getMainMenuItems}
                                getRowId={this.getRowId}
                                gridOptions={this.gridOptions}
                                groupDisplayType="singleColumn"
                                headerHeight={ROW_HEIGHT}
                                isServerSideGroup={this.isServerSideGroup}
                                loadingCellRenderer={TableLoadingCellRenderer}
                                loadingCellRendererParams={this.loadingCellRendererParams}
                                loadingOverlayComponent="Loader"
                                loadingOverlayComponentParams={this.loadingOverlayComponentParams}
                                localeText={localeText()}
                                maxConcurrentDatasourceRequests={1}
                                modules={activatedAgGridModules}
                                noRowsOverlayComponent="DesktopTableEmptyComponent"
                                noRowsOverlayComponentParams={this.noRowsOverlayComponentParams}
                                onCellClicked={this.onCellClicked}
                                onCellEditingStopped={this.onCellEditingStopped}
                                onCellFocused={this.onCellFocused}
                                onCellKeyDown={this.onCellKeyDown}
                                onColumnEverythingChanged={this.hideUnhideColumns}
                                onColumnResized={this.onColumnResized}
                                onColumnRowGroupChanged={this.handleColumnRowGroupChanged}
                                onFilterChanged={this.handleFilterChanged}
                                onFirstDataRendered={this.onFirstDataRendered}
                                onGridReady={this.onGridReady}
                                onGridSizeChanged={this.adjustTableHeight}
                                onRowClicked={this.onRowClicked}
                                onSelectionChanged={this.onSelectionChanged}
                                onSortChanged={this.onSortChanged}
                                pagination={!this.isInfiniteScroll()}
                                paginationPageSize={this.props.fieldProperties.pageSize || 20}
                                paginationPageSizeSelector={false}
                                pinnedTopRowData={this.state.phantomRows ?? []}
                                rowClassRules={this.rowClassRules}
                                getRowHeight={this.getRowHeight}
                                rowModelType="serverSide"
                                rowSelection={this.rowSelection}
                                serverSideSortAllLevels={true}
                                suppressAggFuncInHeader={true}
                                suppressCellFocus={this.isDisabled()}
                                suppressColumnVirtualisation={process.env.NODE_ENV === 'test'}
                                suppressContextMenu={true}
                                suppressDragLeaveHidesColumns={true}
                                suppressExcelExport={false}
                                suppressPaginationPanel={this.isDisabled()}
                                suppressRowClickSelection={true}
                                undoRedoCellEditing={true}
                                valueCacheNeverExpires={true}
                                groupTotalRow={hasAggregatedGroupColumns ? 'bottom' : undefined}
                            />
                        </div>
                    )}
                </div>
                <TableConfigurationDialog
                    isDialogOpen={this.state.isConfigurationDialogOpen}
                    onDialogClose={this.onCloseColumnPanel}
                    columnPanelColumnStates={columnPanelColumnStates}
                    onChangeColumnHiddenState={this.onColumnPanelHiddenStateChange}
                    onOrderChange={this.onColumnPanelOrderChangeChange}
                />
            </>
        );
    }
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: TableInternalComponentProps,
): Partial<TableInternalComponentProps> => {
    const screenDefinition = getPageDefinitionFromState(props.screenId, state);
    const screenElement = getScreenElement(screenDefinition);
    const selectedRecordId = screenDefinition.queryParameters?._id;
    const openSidebarDialogContent = Object.values(state.activeDialogs).find(
        dialog =>
            dialog.screenId === props.screenId &&
            dialog.type === 'table-sidebar' &&
            (dialog.content as TableSidebarDialogContent).elementId === props.elementId,
    )?.content as TableSidebarDialogContent;
    const pageNode = getPagePropertiesFromPageDefinition(screenDefinition).node;
    const tableUserSettings = screenDefinition.userSettings[props.elementId] as Dict<TableUserSettings>;
    const selectedOptionsMenuItem = getActiveOptionsMenu(
        props.screenId,
        tableUserSettings,
        props.fieldProperties.optionsMenu,
    );

    const isTableInFocus =
        !!state.focusPosition &&
        state.focusPosition.elementId === props.elementId &&
        state.focusPosition.screenId === props.screenId;
    return {
        ...props,
        isTableInFocus,
        isInFocus: Boolean(isTableInFocus && !state.focusPosition?.nestedField),
        graphApi: screenElement.$.graph,
        selectedOptionsMenuItem,
        selectedRecordId,
        openedRecordId: openSidebarDialogContent?.recordId,
        activeLookupDialog: state.activeLookupDialog,
        tableUserSettings,
        pageNode,
        accessBindings: screenDefinition.accessBindings,
        dataTypes: state.dataTypes,
        onTelemetryEvent: state.applicationContext?.onTelemetryEvent,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: TableComponentExternalProps,
): Partial<TableInternalComponentProps> => ({
    setTableViewColumnHidden: (level: number, columnHidden?: Dict<boolean>): void => {
        dispatch(xtremRedux.actions.setTableViewColumnHidden(props.screenId, props.elementId, level, columnHidden));
    },
    setTableViewColumnOrder: (level: number, columnOrder?: string[]): void => {
        dispatch(xtremRedux.actions.setTableViewColumnOrder(props.screenId, props.elementId, level, columnOrder));
    },
    setTableViewFilter: (level: number, filter?: FilterModel): void => {
        dispatch(xtremRedux.actions.setTableViewFilter(props.screenId, props.elementId, level, filter));
    },
    setTableViewGrouping: (level: number, grouping?: TableViewGrouping): void => {
        dispatch(xtremRedux.actions.setTableViewGrouping(props.screenId, props.elementId, level, grouping));
    },
    setTableViewOptionsMenuItem: (level: number, optionsMenuItem?: OptionsMenuItem): void => {
        dispatch(
            xtremRedux.actions.setTableViewOptionMenuItem(props.screenId, props.elementId, level, optionsMenuItem),
        );
    },
    setTableViewOptionsMenuItemAndViewFilter: (
        level: number,
        optionsMenuItem?: OptionsMenuItem,
        filter?: any,
    ): void => {
        dispatch(
            xtremRedux.actions.setTableViewOptionsMenuItemAndViewFilter(
                props.screenId,
                props.elementId,
                level,
                optionsMenuItem,
                filter,
            ),
        );
    },
    setTableViewSortOrder: (level: number, sortOrder?: TableViewSortedColumn[]): void => {
        dispatch(xtremRedux.actions.setTableViewSortOrder(props.screenId, props.elementId, level, sortOrder));
    },
});

export const ConnectedDesktopTableComponent = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(
    DesktopTableComponent,
);

export default ConnectedDesktopTableComponent;
