import type { <PERSON>rid<PERSON><PERSON> } from '@ag-grid-community/core';
import type { ClientNode } from '@sage/xtrem-client';
import type { Dict, LocalizeLocale } from '@sage/xtrem-shared';
import React from 'react';
import type {
    FocusPosition,
    OnTelemetryEventFunction,
    ReduxResponsive,
    UiComponentUserSettings,
} from '../../../redux/state';
import type { CollectionValue } from '../../../service/collection-data-service';
import type { AggFunc } from '../../../service/collection-data-utils';
import type { GraphQLApi } from '../../../service/graphql-api';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import type { DataTypeDetails, FormattedNodeDetails } from '../../../service/metadata-types';
import type { AccessBindings } from '../../../service/page-definition';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValidationResult } from '../../../service/screen-base-definition';
import type { ContextType, NestedRecordId, NodePropertyType, ScreenExtension } from '../../../types';
import type { OverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { ColumnCompareType } from '../../../utils/table-component-utils';
import type { ValueOrCallback, ValueOrCallbackWithFieldValue } from '../../../utils/types';
import type {
    BlockControlObject,
    BulkAction,
    CollectionValueFieldProperties,
    IPageAction,
    OptionsMenuItem,
    PageActionControlObject,
    SectionControlObject,
    TableOptionsMenuType,
} from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { GridNestedFieldTypes, NestedField, NestedFieldTypes } from '../../nested-fields';
import type { NestedExtensionField } from '../../nested-fields-extensions';
import type { NestedOverrideField } from '../../nested-fields-overrides';
import type { SidebarDefinitionDecorator } from '../../table-sidebar/table-sidebar-types';
import type {
    CollectionItem,
    ContainerWidth,
    FieldControlObjectInstance,
    PartialCollectionValueWithIds,
    TableDisplayMode,
} from '../../types';
import type { CardDefinition, CardExtensionDefinition } from '../../ui/card/card-component';
import type {
    CollectionItemActionGroup,
    CollectionItemActionOrMenuSeparator,
    InlineCollectionItemAction,
} from '../../ui/table-shared/table-dropdown-actions/table-dropdown-action-types';
import type { AdditionalFieldAction } from '../field-actions-component';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { PropertyValueType } from '../reference/reference-types';
import type {
    CanBeReadOnly,
    CanFetchDefaults,
    Clickable,
    ExtensionField,
    HasCalendarConfigurationOptions,
    HasCollectionAdditionEventHandler,
    HasCollectionSelectionEventHandlers,
    HasCollectionSelectionEventHandlersAfter,
    HasColumns,
    HasDynamicLookupSuggestions,
    HasFieldActions,
    HasOptionsMenu,
    HasParent,
    HasRowChangeIndicators,
    HasSound,
    VoidPromise,
} from '../traits';
import type { DesktopTableComponent } from './desktop-table-component';

export interface TableViewGrouping {
    key: string;
    sort: SortOrder;
    aggFunc?: AggFunc;
}

export type SortOrder = 'asc' | 'desc';

export interface TableViewSortedColumn {
    colId: string;
    sort: SortOrder | null;
    sortIndex?: number | null;
}

export interface TableViewLevel {
    columnHidden?: Dict<boolean>;
    columnOrder?: string[];
    filter?: Dict<any>;
    grouping?: TableViewGrouping;
    optionsMenuItem?: OptionsMenuItem;
    sortOrder?: TableViewSortedColumn[];
    searchText?: string;
}

export interface TableUserSettings extends UiComponentUserSettings<TableViewLevel[]> {}

export interface TableProperties<CT extends ScreenExtension<CT> = ScreenBase, NestedRecordType extends ClientNode = any>
    extends CollectionValueFieldProperties<CT, NestedRecordType>,
        HasRowChangeIndicators,
        HasColumns<CT, NestedRecordType>,
        HasFieldActions<CT>,
        CanBeReadOnly<CT, NestedRecordType>,
        HasOptionsMenu<CT>,
        HasSound<CT> {
    /**
     * Whether new rows can be added or not.
     * If set to true infinite scroll will be enabled and a new "phantom row" will appear at the bottom of the table.
     * Additionally an "add" action button will be added to the table.
     * Defaults to false
     * */
    canAddNewLine?: boolean;

    /**
     * Whether the "phantom row" will be displayed or not.
     * If set to true the "add" action button will be also removed from the table.
     * Defaults to false.
     */
    isPhantomRowDisabled?: boolean;

    /** Whether the rows of the table can be filtered or not. Defaults to true */
    canFilter?: boolean;

    /** Whether the user can decide which table columns to display. Defaults to true */
    canUserHideColumns?: boolean;

    /** Determines how the table rows are displayed. Defaults to "comfortable" */
    displayMode?: TableDisplayMode;

    /** Whether in mobile devices a search box should be displayed or not */
    hasSearchBoxMobile?: boolean;

    /** The property that will be used to identify a record when showing validation errors. Defaults to "_id". */
    mainField?: PropertyValueType<NestedRecordType> & string;

    /** The definitions of the nested fields used to represent the table rows in mobile devices.
     * If no value is provided, the first four columns are used by default */
    mobileCard?: CardDefinition<CT, NestedRecordType>;

    /** Actions that are rendered at the end of the table as inline buttons **/
    inlineActions?: Array<InlineCollectionItemAction<any>>;

    /** Actions that are rendered at the end of the table as a drop-down menu */
    dropdownActions?: Array<CollectionItemActionOrMenuSeparator<CT> | CollectionItemActionGroup<CT>>;

    /** Selected rows identifiers */
    selectedRecords?: NestedRecordId[];

    /** The GraphQL node that the table represents, needed for filtering */
    cardView?: boolean;

    /** Table can be exported to .XLSX or CSV files */
    canExport?: boolean;

    /** Whether all table columns can be resized or not. Defaults to false. */
    canResizeColumns?: boolean;

    /** Provides a custom function to sort columns */
    sortColumns?: (this: CT, firstColumn: ColumnCompareType, secondColumn: ColumnCompareType) => number;

    /** Actions that are rendered on the top of the field, in the title line */
    headerBusinessActions?: ValueOrCallback<CT, PageActionControlObject | PageActionControlObject[]>;

    /** These actions are added in the add items dropdown, the actions are only rendered if the user can add lines to the table */
    addItemActions?: ValueOrCallback<CT, PageActionControlObject[]>;

    sidebar?: SidebarDefinitionDecorator<CT, NestedRecordType>;

    /** Displays line numbers in front of each line. The line numbers are static and not related to the data */
    hasLineNumbers?: boolean;
}

export interface PageAction<CT extends ScreenExtension<CT>> extends IPageAction<CT> {
    icon: string;
}

export interface TableDecoratorProperties<CT extends ScreenBase = ScreenBase, NestedRecordType extends ClientNode = any>
    extends TableProperties<CT, NestedRecordType>,
        Clickable<CT>,
        HasFieldActions<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>>,
        HasCollectionSelectionEventHandlers<CT, NestedRecordType>,
        HasCollectionAdditionEventHandler<CT, NestedRecordType>,
        CanFetchDefaults,
        HasCalendarConfigurationOptions<CT, NestedRecordType>,
        CanBeReadOnly<CT, NestedRecordType> {
    /** Function to be executed when the table field's value changes */
    onChange?: (this: CT, recordId: NestedRecordId, column: string, rowItem: NestedRecordType) => VoidPromise;

    /** Function to be executed when any part of a row is clicked */
    onRowClick?: (
        this: CT,
        recordId: NestedRecordId,
        rowItem: NestedRecordType,
        isModifierKeyPushed?: boolean,
    ) => VoidPromise;

    /** Actions that are rendered at the end of the table as inline buttons */
    inlineActions?: Array<InlineCollectionItemAction<CT, NestedRecordType>>;

    /** Actions that are rendered at the end of the table as a drop-down menu */
    dropdownActions?: Array<
        CollectionItemActionOrMenuSeparator<CT, NestedRecordType> | CollectionItemActionGroup<CT, NestedRecordType>
    >;

    /** Sets a text when no data is available in the table */
    emptyStateText?: string;

    /** Sets a complementary text link when no data is available in the table  */
    emptyStateClickableText?: string;

    /** Function to be executed when the clickable text is clicked */
    onEmptyStateLinkClick?: (this: CT) => void;

    /** Card with in case of card view on desktop */
    recordWidth?: ContainerWidth;

    /** Force displays field titles in card view */
    areCardFieldTitlesDisplayed?: boolean;
}

type BaseTableExtensionDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    NestedRecordType extends ClientNode = any,
> = HasCollectionSelectionEventHandlersAfter<CT, NestedRecordType> & {
    /** Function to be executed when the table field's value changes after base onChange*/
    onChangeAfter?: (this: CT, recordId: NestedRecordId, column: string, rowItem: NestedRecordType) => VoidPromise;
    /** Function to be executed when any part of a row is clicked after onRowClick */
    onRowClickAfter?: (this: CT, recordId: NestedRecordId, rowItem: NestedRecordType) => VoidPromise;
};

export interface TableExtensionDecoratorProperties<
    CT extends ScreenExtension<CT>,
    ReferencedItemType extends ClientNode = any,
> extends BaseTableExtensionDecoratorProperties<Extend<CT>>,
        OverrideDecoratorProperties<TableDecoratorProperties<Extend<CT>, ReferencedItemType>> {
    /** The definitions of the nested fields used to represent the table rows */
    columns?: NestedExtensionField<CT, GridNestedFieldTypes, ReferencedItemType>[];

    /** Allows overriding existing column properties in the base page's columns */
    columnOverrides?: NestedOverrideField<CT, GridNestedFieldTypes, ReferencedItemType>[];

    /** Allows overriding existing mobile card configuration */
    mobileCard?: CardExtensionDefinition<CT, ReferencedItemType>;
}

export interface InternalTableProperties<CT extends ScreenExtension<CT> = ScreenBase, NRT extends ClientNode = any>
    extends TableProperties<CT, NRT>,
        HasDynamicLookupSuggestions<CT, NRT>,
        HasCalendarConfigurationOptions<CT, NRT> {
    activeUserFilter?: GraphQLFilter;
    additionalFieldActions?: AdditionalFieldAction[];
    areCardFieldTitlesDisplayed?: boolean;
    emptyStateClickableText?: string;
    emptyStateText?: string;
    hiddenColumns?: string[];
    isAutoSelectEnabled?: boolean;
    isNewEnabled?: boolean;
    isReadOnly?: ValueOrCallbackWithFieldValue<CT, boolean, any, NRT>;
    isSelectAllChecked?: boolean;
    onEmptyStateLinkClick?: (this: CT, recordId?: string, level?: number) => void;
    onRowClick?: (this: CT, recordId: NestedRecordId, rowItem: NRT) => void;
    optionsMenu?: ValueOrCallback<CT, OptionsMenuItem[]>;
    optionsMenuType?: TableOptionsMenuType;
    recordWidth?: ContainerWidth;
    selectedOptionsMenuItem?: OptionsMenuItem;
    tableViewMode?: TableViewMode;
    valueField?: string;
}

export interface TableComponentExternalProps extends FieldComponentExternalProperties {
    bulkActions?: BulkAction[];
    mobilePerformInitialLoadTableData?: boolean;
    searchText?: string;
    errorMessage?: string;
    isParentDisabled?: boolean;
    selectionMode?: 'single' | 'multiple';
}

export type OnRowClickFunction = (
    recordId: NestedRecordId,
    newRecord?: CollectionItem,
    isModifierKeyPushed?: boolean,
) => () => void;

export interface TableInternalComponentProps<
    CT extends ScreenExtension<CT> = ScreenBase,
    FieldProperties extends Omit<EditableFieldProperties, 'onChange'> = InternalTableProperties<CT>,
> {
    accessBindings: AccessBindings;
    activeLookupDialog?: FocusPosition | null;
    additionalLookupRecords?: () => PartialCollectionValueWithIds<any>[];
    api?: GridApi;
    availableColumns?: number;
    bulkActions?: BulkAction[];
    canDragCard?: boolean;
    contextType?: ContextType;
    dataTypes: Dict<DataTypeDetails>;
    elementId: string;
    emptyStateClickableText?: string;
    emptyStateText?: string;
    enumTypes: Dict<string[]>;
    errorMessage?: string;
    fieldProperties: FieldProperties;
    filterModel?: any;
    fixedHeight?: number;
    graphApi?: GraphQLApi<any>;
    groupByField?: PropertyValueType;
    groupTitle?: string;
    isAutoSelectEnabled?: boolean;
    isInFocus?: boolean;
    isLookupDialog?: boolean;
    isNavPanel?: boolean;
    isParentDisabled?: boolean;
    isReadOnly?: ValueOrCallbackWithFieldValue<CT, boolean>;
    isTableInFocus?: boolean;
    isUsingInfiniteScroll?: boolean;
    locale: LocalizeLocale;
    nodeTypes: Dict<FormattedNodeDetails>;
    numberOfVisibleRows?: number;
    onEmptyStateClickableText?: () => void;
    onFocus?: (screenId: string, elementId: string, row: string, nestedField: string) => void;
    onRowClick?: OnRowClickFunction;
    onTelemetryEvent?: OnTelemetryEventFunction;
    openedRecordId?: string | number | null;
    pageNode?: NodePropertyType;
    recordContext?: any;
    saveCurrentView?: () => void;
    screenId: string;
    searchText?: string;
    selectedOptionsMenuItem?: OptionsMenuItem;
    selectedRecordId?: string | number | boolean;
    selectionMode?: 'single' | 'multiple';
    setFieldProperties?: (elementId: string, properties: InternalTableProperties<ScreenBase>) => void;
    setGlobalLoading?: (setGlobalLoading: boolean) => void;
    setTableViewColumnHidden?: (level: number, columnHidden?: Dict<boolean>) => void;
    setTableViewColumnOrder?: (level: number, columnOrder?: string[]) => void;
    setTableViewFilter?: (level: number, filter?: any) => void;
    setTableViewGrouping?: (level: number, grouping?: TableViewGrouping) => void;
    setTableViewOptionsMenuItem?: (level: number, optionsMenuItem?: OptionsMenuItem) => void;
    setTableViewOptionsMenuItemAndViewFilter?: (level: number, optionsMenuItem?: OptionsMenuItem, filter?: any) => void;
    setTableViewSearchText?: (level: number, searchText: string) => void;
    setTableViewSortOrder?: (level: number, sortOrder?: TableViewSortedColumn[]) => void;
    tableUserSettings?: Dict<TableUserSettings>;
    username?: string;
    validationErrors: ValidationResult[];
    value: CollectionValue;
}

export interface TableComponentProps extends TableComponentExternalProps {
    accessBindings: AccessBindings;
    additionalLookupRecords?: () => PartialCollectionValueWithIds<any>[];
    browser: ReduxResponsive;
    bulkActions?: BulkAction[];
    canDragCard?: boolean;
    contextType?: ContextType;
    dataTypes: Dict<DataTypeDetails>;
    enableMobileLoadMore?: boolean;
    ensureFieldHasValue?: () => CollectionValue;
    enumTypes: Dict<string[]>;
    fieldProperties: InternalTableProperties<ScreenBase>;
    groupByField?: PropertyValueType;
    groupKey?: string;
    isAutoSelectEnabled?: boolean;
    isLoading?: boolean;
    isLookupDialog?: boolean;
    isUsingInfiniteScroll?: boolean;
    locale: LocalizeLocale;
    nodeTypes: Dict<FormattedNodeDetails>;
    numberOfVisibleRows?: number;
    onEmptyStateClickableText?: () => void;
    onFocus?: (screenId: string, elementId: string, row: string, nestedField: string) => void;
    onRowClick?: (rowValue: CollectionItem, isModifierKeyPushed: boolean) => void;
    recordContext?: any;
    setFieldProperties?: (elementId: string, properties: InternalTableProperties<ScreenBase>) => void;
    setGlobalLoading?: (setGlobalLoading: boolean) => void;
    tableRef?: React.ForwardedRef<DesktopTableComponent>;
    tableUserSettings: Dict<TableUserSettings>;
    username?: string;
    validate?: (elementId: string, value: CollectionValue) => Promise<ValidationResult[] | undefined>;
    validationErrors: ValidationResult[];
    value: CollectionValue;
}

export interface ColumnsData<T extends NestedFieldTypes = NestedFieldTypes> {
    columnDefinition: NestedField<ScreenBase, T>;
    bind: string;
    elementId: string;
}

export type FilterModel = any;

export type TableViewMode = 'table' | 'calendar';

// This abstract class serves as the interface of the ag-grid lifecycle methods, TODO: Add other lifecycle methods
export abstract class AgGridCellEditorComponent<P = {}, S = {}, ValueType extends any = string> extends React.Component<
    P,
    S
> {
    abstract getValue(): ValueType | null;
}
