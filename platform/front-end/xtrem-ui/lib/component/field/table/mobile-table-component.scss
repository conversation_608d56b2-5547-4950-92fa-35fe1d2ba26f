@import '../../../render/style/variables.scss';
@import '../../../render/style/mixins.scss';

.e-table-field .e-mobile-table-header .e-field-header {
    display: block;

    .e-option-item-menu,
    .e-table-field-mobile-search {
        padding-right: 0;
    }

    .e-table-mobile-searchbox-with-filter-line {
        display: flex;
    }

    .e-option-item-menu {
        padding-bottom: 16px;

        .e-ui-select-input-wrapper>div {
            margin-bottom: 0;
        }

        div[data-component="button-toggle-group"] [data-component="button-toggle"] {
            flex: 1;
            display: flex;

            >button {
                flex: 1;
            }

        }
    }
}

.e-table-field .e-table-field-mobile {

    .e-field-title {
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
        align-items: center;
    }

    .e-mobile-table-separator {
        align-items: center;
        background: var(--colorsActionMinor050);
        border-top: 1px solid var(--colorsUtilityMajor100);
        color: var(--colorsYin065);
        display: flex;
        font-family: var(--fontFamiliesDefault);
        font-size: var(--fontSizes100);
        height: 40px;
        padding: 0 16px;
        text-transform: uppercase;
    }


    .e-field-header {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;

        @include extra_small {
            &[data-options-menu-type="tabs"] {
                padding: 16px 16px 0 16px;
            }

            >*:not(:last-child) {
                padding-bottom: 16px;
            }

            >*:last-child {
                padding-bottom: 0;
            }
        }

        .e-option-item-menu {
            padding-bottom: 16px;

            &[data-options-menu-type="tabs"] {
                padding-bottom: 0;
            }

            .e-ui-select-input-wrapper>div {
                margin-bottom: 0;
            }
        }
    }

    .e-component-actions {
        display: flex;
    }

    @include extra_small {
        @include full_width_mobile_field;
        margin-bottom: 0;

        .e-field-title {
            padding-left: 16px;
            padding-right: 16px;
        }

        .e-field-header {
            padding: 16px;

            .e-table-field-mobile-search {
                flex: 1;

                div[role='presentation'] {
                    padding-left: 0;
                    padding-right: 8px;
                }
            }

            .e-field-actions-wrapper {
                margin: 0px;
                flex: none;

                .e-component-actions {
                    margin-left: 12px;
                }
            }
        }
    }

    .e-field-read-only {
        padding: 0;
        line-height: $heightCardLine;
    }

    .e-filter-label-wrapper {
        background-color: var(--colorsYang100);
    }

    .e-table-field-mobile-rows .e-card:hover {
        background-color: #D9ECE7;
        border-bottom: 1px solid var(--colorsUtilityMajor100);
    }

    .e-table-field-mobile-rows .e-card {
        border-radius: 8px;

        .e-field .common-input__label.e-nested-read-only-label {
            padding-top: 8px;
        }

        .e-card-row-line-title {
            .e-field .common-input__label.e-nested-read-only-label {
                padding-top: 0;
            }
        }
    }

    .e-table-field-mobile-rows.e-table-field-mobile-rows-one-column .e-card {
        border-radius: 0;
    }

    .e-table-field-mobile-rows.e-table-field-mobile-rows-one-column {
        background-color: var(--colorsYang100);
    }

    .e-table-field-mobile-rows.e-table-field-mobile-rows-one-column .e-card:not(.e-card-selected) {
        border-left: none;
        border-right: none;
        border-bottom: transparent;
    }

    .e-table-field-mobile-rows.e-table-field-mobile-rows-one-column .e-grid-column:last-child .e-card {
        border-bottom: 1px solid var(--colorsUtilityMajor100);
    }

    .e-load-more-button-container {
        border-top: 1px solid var(--colorsUtilityMajor100);
    }
}


.e-section-context-detail-panel .e-block-body .e-table-field .e-table-field-mobile {
    margin-left: -16px;
    margin-right: -16px;
}

.e-block-body {
    .e-field-grid-column {
        .e-table-field-mobile {
            @include extra_small {
                margin: -16px !important;
            }
        }
    }
}

.e-table-field {
    @include extra_small {
        padding: 0 16px 16px 16px;
    }

}

.e-block-body {
    .e-table-field {
        @include extra_small {
            padding: 0 !important;
        }
    }
}

// The lookup dialog renders in mobile mode on XS and S devices, so in case the table is used on such screen sizes, we still need the mobile mode
@include small {
    .e-lookup-dialog .e-field-header .e-table-field-mobile-search {
        flex: 1;
    }
}