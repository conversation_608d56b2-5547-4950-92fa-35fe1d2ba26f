.e-table-field,
.e-tree-field {

    .e-table-field-desktop-wrapper,
    .e-tree-field-desktop-wrapper {
        border-top-right-radius: var(--borderRadius100);
        border-top-left-radius: var(--borderRadius100);
    }

    .e-table-field-desktop-wrapper:has(.e-table-field-line-number) {

        // avoid doble golden border when the row has line numbers: 
        .e-row-is-edited-no-select.ag-row-not-inline-editing {
            border-left: none;
        }

        .ag-pinned-left-cols-container {
            .ag-row-edited {
                border-left: 3px solid var(--colorsSemanticFocus500);

                .e-table-field-line-number {
                    width: 41px !important;
                }
            }
        }

        .ag-center-cols-viewport {
            .e-row-is-edited-no-select.ag-row-not-inline-editing {
                border-left: none !important;
            }
        }
    }

    .ag-theme-balham {

        .ag-horizontal-right-spacer,
        .ag-horizontal-left-spacer {
            overflow-x: hidden;
        }

        .ag-selection-checkbox {
            margin: 0 !important;
            height: unset !important;
        }

        .ag-overlay .ag-overlay-wrapper {
            margin-top: 10px;
            margin-bottom: 10px;
            padding-top: 40px;
            padding-bottom: 32px;
        }
    }

    // INFO: These styles should apply when the mobile card view is forced on desktop sized screens.
    .e-table-field-mobile {
        .e-field-header .e-option-item-menu {
            padding-right: 0;
        }

        .e-field-header .e-field-mobile-search-and-actions .e-table-field-mobile-search {
            max-width: none;
        }
    }

    .e-field-header {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: stretch;

        .e-option-item-menu {
            min-width: 200px;
            padding-right: 16px;
        }

        .e-field-mobile-search-and-actions {
            flex: 1;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            align-items: center;

            .e-table-field-mobile-search {
                min-width: 200px;
                max-width: 400px;
                flex: 1;
                padding-right: 16px;
            }

            .e-field-actions-wrapper {
                flex: none;
                margin: 0;
            }
        }

        @include small {
            .e-option-item-menu {
                padding-right: 0;
            }

            .e-field-mobile-search-and-actions {
                flex: none;
                justify-content: flex-start;

                .e-table-field-mobile-search {
                    max-width: none;
                }
            }
        }
    }

    .e-field-header[data-options-menu-type="tabs"] {
        flex-direction: column;
        padding: 0;

        .e-field-mobile-search-and-actions {
            margin-bottom: 16px;
        }

        @include small {
            .e-option-item-menu[data-options-menu-type="tabs"] {
                padding: 0;
            }
        }
    }

    .ag-row.ag-row-even.e-row-is-open-in-sidebar,
    .ag-row.ag-row-odd.e-row-is-open-in-sidebar {
        background-color: var(--colorsUtilityMajor075);
    }
}

.e-table-field.e-read-only {
    .ag-theme-balham {
        .ag-header {
            background-color: var(--colorsUtilityReadOnly600);
        }

        --ag-header-cell-moving-background-color: var(--colorsUtilityReadOnly600);
    }

    .ag-header-row {
        color: #000;
    }

    .ag-header-cell::after {
        display: none;
    }

    .ag-row-even {
        background-color: var(--colorsUtilityMajor010);
    }

    .ag-row-even.ag-row-hover,
    .ag-row-odd.ag-row-hover {
        background-color: #D9ECE7;
    }

    .ag-header-cell .ag-header-icon {
        color: #000000E5;
    }

    .ag-header-cell [class^="ag-"]:before {
        color: #000000E5;
    }
}

.ag-cell.e-table-field-select-row {
    display: flex;
    align-items: center;
    justify-content: center;
}

.e-table-field-title {
    display: flex;
    align-items: flex-start;
}

.e-table-validation-errors {
    font-family: var(--fontFamiliesDefault);

    &>ul {
        list-style: disc;
        font-size: 14px;
        line-height: 14px;
        padding: 0;
        margin: 10px 0;
        padding-inline-start: 1.3em;

        &>li:not(:last-child) {
            margin-bottom: 0.5rem;
        }
    }

    strong {
        font-weight: var(--fontWeights500);
        font-family: var(--fontFamiliesDefault);
    }
}

.e-table-validation-global-errors {
    margin-right: 16px;
    text-align: left;

    &>h6 {
        font-weight: var(--fontWeights500);
        font-family: var(--fontFamiliesDefault);
        margin: 0px;
        padding: 0px;
        font-size: 14px;
        line-height: 14px;
    }
}

.e-table-validation-errors-dialog {
    font-family: var(--fontFamiliesDefault);

    ul {
        margin: 0 auto;
        padding: 0;
        padding-inline-start: 1.3rem;
        max-height: calc(100vh - 10rem);
        overflow: auto;
        width: calc(100% - 5rem);

        li:not(:last-child) {
            margin-bottom: 0.5rem;
        }
    }

    h6 {
        font-weight: var(--fontWeights500);
        font-family: var(--fontFamiliesDefault);
        margin-top: 0;
        margin-bottom: 1rem;
        padding: 0px;
        padding-inline-start: 1.8rem;
        font-size: 14px;
    }
}

.e-table-validation-errors-remaining {
    background: none;
    border: none;
    margin: 0;
    padding: 0;
    font-weight: var(--fontWeights500);
    font-family: var(--fontFamiliesDefault);
    font-size: 13px;
    line-height: 13px;
    text-decoration: underline;
    cursor: pointer;
}

.e-field-header {

    .e-option-item-menu,
    .e-table-field-mobile-search {
        min-width: 200px;
        padding-right: 16px;
        @include extra_small {
            min-width: 0;
        }
    }
}

.e-table-field-button-bar-container {
    --colorsActionMajor500: var(--colorsActionMinor500);
    --colorsActionMajor600: var(--colorsActionMinor500);
    display: inline-block;
}

.e-table-field .ag-theme-balham .ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right):not(.ag-cell-range-single-cell) {
    border-right: none;
}

.e-table-field-loading-row {
    margin: 8px;

    >div>div {
        background-color: var(--colorsActionMinor500);
    }
}

.e-table-group-total-row-cell {
    font-weight: var(--fontWeights500);
    font-family: var(--fontFamiliesDefault);
}