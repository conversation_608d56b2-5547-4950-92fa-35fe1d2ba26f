PATH: XTREEM/UI+Field+Widgets/Table+Field

## Introduction

Table fields are used in order to represent tables.

## Example:

```ts
@ui.decorators.tableField<TableField>({
    parent() {
        return this.tableBlock;
    },
    canFilter: true,
    canSelect: true,
    node: '@sage/x3-products/Product',
    columns: [
        ui.nestedFields.text({
            bind: 'product',
            canFilter: true,
            title: 'Product',
        }),
        ui.nestedFields.numeric({
            bind: 'qty',
            canFilter: true,
            title: 'Quantity',
            scale: 0,
        }),
    ],
    onChange(_id: string | number) {
        console.log('On table change');
    },
    onRowSelected(_id: string | number) {
        console.log('On row selected');
    },
    onRowUnselected(_id: string | number) {
        console.log('On row unselected');
    },
    headerBusinessActions() {
        return [this.somePageAction, this.someOtherPageAction];
    },
    dropdownActions: [
        {
            icon: 'add',
            title: 'Add',
            isDisabled() {
                return false;
            },
            onClick(recordId: any, data: any) {
                this.$.showToast(data.product, { type: 'info' });
            },
            access: {
                node: '@sage/x3-products/Product',
                property: 'qty',
            }
        },
        {
            icon: 'folder',
            title: 'Submenu',
            isDisabled() {
                return false;
            },
            children: [
                {
                    icon: 'info',
                    title: 'Info',
                    onClick(recordId, data) {
                        this.$.showToast(data.product, { type: 'info' });
                    },
                },
                {
                    icon: 'settings',
                    title: 'Version',
                    isDisabled(recordId, data) {
                        return !data.version;
                    },
                    onClick(recordId, data) {
                        this.$.showToast(data.version, { type: 'info' });
                    },
                }
            ]
        },
        ui.menuSeparator(),
        {
            icon: 'Delete',
            isDestructive: true,
            title: 'bind',
            isDisabled() {
                return false;
            },
            onClick(recordId: any, data: any) {
                this.$.showToast(data.product, { type: 'danger' });
            },
        }
    ],
})
products: ui.fields.Table<SalesOrderItem>;
```

## Example (Mobile):

```ts
@ui.decorators.tableField<Page, Order>({
    bind: 'orders',
    node: '@sage/xtrem-docs/Order',
    columns: [
        ui.nestedFields.text({
            bind: '_id',
            title: 'Id',
        }),
        ui.nestedFields.reference<Page, Order, Product>({
            bind: 'product',
            title: 'Product',
            valueField: 'name',
            node: '@sage/xtrem-docs/Product',
        }),
        ui.nestedFields.text({
            bind: 'barcode',
            title: 'Barcode',
        }),
        ui.nestedFields.numeric({
            bind: 'price',
            title: 'Price',
            prefix: '$',
            scale: 2,
        }),
        ui.nestedFields.image({
            bind: 'image',
            title: 'Image',
        }),
    ],
    cardView: true,
    mobileCard: {
        title: ui.nestedFields.reference<Page, Order, Product>({
            bind: 'product',
            title: 'Product',
            valueField: 'name',
            node: '@sage/xtrem-docs/Product',
        }),
        titleRight: ui.nestedFields.text({
            bind: '_id',
            title: 'Id',
        }),
        line2: ui.nestedFields.label({
            bind: 'price',
            title: 'price',
            prefix: '$',
        }),
        line2Right: ui.nestedFields.text({
            bind: 'barcode',
            title: 'Barcode',
        }),
        image: ui.nestedFields.image({
            bind: 'image',
            title: 'image',
        }),
        progressBar: ui.nestedFields.progress({
            bind: 'somePercentageValue',
        }),
    },
    parent() {
        return this.block;
    },
})
orders: ui.fields.Table<Order>;
```

### Display decorator properties

-   **canAddNewLine**: Whether new rows can be added or not. If set to `true` **infinite scroll** will be enabled and a new "phantom row" will appear at the top of the table. Additionally an "Add line" action button will be added to the table. In order to commit a new row the user can tab through all table columns or hit Command/Control + Enter at any time while the "phantom row" is in focus. Only valid rows can be committed to the table. Defaults to `false`.
-   **canExport**: Displays a button in order to enable export functionality to Excel XLSX or CSV file formats. Defaults to false.
-   **canFilter**: Whether the rows of the table can be filtered or not. Defaults to false. A filter icon will be displayed on the upper right hand corner as long as at least one table column is marked as filterable.
-   **canResizeColumns**: Whether all table columns can be resized or not. Defaults to false.
-   **canSelect**: Whether the rows of the table can be selected or not. Defaults to false.
-   **canUserHideColumns**: Whether the user can decide which table columns to display. Defaults to false.
-   **columns**: A list of nested fields to be displayed as columns. The following types can be used as nested fields: 'checkbox', 'icon', 'image', 'label', 'link', 'numeric', 'progress', 'reference', 'text', 'date', 'datetime'. **Note**: support for multiple nested fields referencing the same node is deprecated and hence discouraged. Please consider using deep bound properties instead.
-   **displayMode**: Determines how the table rows are displayed. Options are "comfortable" (default) and "compact".
-   **dropdownActions**: A list of actions that are displayed at the end of the table as a drop-down menu. See below under "Record action properties" how they can be configured.
-   **emptyStateClickableText**: string; Sets a complementary text link when no data is available in the table
-   **emptyStateText**: Sets a text when no data is available in the table
-   **hasLineNumbers**: Displays line numbers in front of each line. The line numbers are static and not related to the data
-   **hasSearchBoxMobile**: Whether on mobile devices or in card view mode a search box should be displayed or not.
-   **headerBusinessActions**: List of page actions. These actions are rendered as buttons in the header of the table.
-   **helperText**: The helper text that is displayed above the table. It is automatically picked up by the i18n engine and externalized.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **inlineActions**: A list of actions that are displayed at the end of the table as buttons. See below under "Record action properties" how they can be configured.
-   **addItemActions**: These actions are added in the add items dropdown, the actions are only rendered if the user can add lines to the table
-   **isChangeIndicatorDisabled**: Disable indicator background color that signals if a row is added or modified
-   **isDisabled**: Whether the table is disabled or not. Defaults to false.
-   **isFullWidth**: Whether the table spans all the parent width or not. It can also be defined as callback function that returns a boolean. Defaults to false.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **isHidden**: Whether the table is hidden or not. Defaults to false.
-   **isPhantomRowDisabled**: Whether the "phantom row" will be displayed or not. If set to true the "add" action button will be also removed from the table.
-   **isReadOnly**: Whether the table is read-only or not. Defaults to false.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **mainField**: The property that will be used to identify a record when showing validation errors. Defaults to "_id".
-   **mobileCard**: The definitions of the nested fields used to represent the table rows in mobile devices. If no value is provided, the first four columns are used by default.
-   **optionsMenu**: Set of predefined graphql filters displayed above the table. Can be displayed as a dropdown, toggle button or tabs menu. This list can be also be displayed as callback. Optional property.
-   **optionsMenuType**: Defines how options menu should be displayed. The options menu can be displayed as a dropdown, toggle button or tabs menu. Default is dropdown.
-   **orderBy**: The column or the set of columns which the table should be sorted by.
-   **pageSize**: Number of lines displayed by the table. The default value is 20.
-   **sidebar**: Defines the sidebar component of the table. For more details see [here](./Table+Sidebar).
-   **sortColumns**: Provides a custom function to sort columns.
-   **title**: The title that is displayed above the table. It is automatically picked up by the i18n engine and externalized.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.
Defaults to false.
-   **recordWidth**: Card with in case of card view on desktop.
-   **areCardFieldTitlesDisplayed**: Force displays nested field titles in card view.

#### Calendar view

The calendar view is an alternate view of the table that provides the following features:

- A switch that allows the user to toggle between the default table and the calendar view
- Built-in options for display (day, week, month, 3-day view)
- Event cards with customizable content based on the column definitions
- Filtering capabilities inherited from the table configuration
- Movable events with drag & drop support and dynamic colors based on row properties

Check out the calendar view example on our sandbox server by clicking [this link](https://showcase-ci-v2.eu.dev-sagextrem.com/@sage/xtrem-show-case/TableWithCalendarView/eyJfaWQiOiIxIn0=).

#### Example:
```ts
@ui.decorators.tableField<TableWithCalendarView, ShowCaseProduct>({
    // ... other table properties ...
    startDateField: 'releaseDate',
    endDateField: 'endingDate',
    cardColor(row: ShowCaseProduct) {
        switch (row.category) {
            case 'awful':
                return ui.tokens.colorsSemanticNegative500;
            case 'good':
                return ui.tokens.colorsSemanticPositive500;
            default:
                return '';
        }
    },
    isEventMovable(row: ShowCaseProduct) {
        return row.category === 'good';
    },
})
products: ui.fields.Table<ShowCaseProduct>;
```

-   **startDateField**: The node bound property which, if set, will enable the calendar view. It must be of type date.
-   **endDateField**: The node bound property that will be used as the end date for calendar events. If not defined, events are rendered as single points in time rather than time spans. Optional and must be of date type.
-   **cardColor**: A callback function that determines the color of calendar event cards. The function receives the row data as an argument and returns a valid CSS color.
-   **isEventMovable**: A callback function that determines whether calendar events can be moved by the user. The function receives the row data as an argument and should return a boolean value.

### Binding decorator properties

-   **bind**: The GraphQL object's property that the table value is bound to. If not provided, the table name is used.
-   **filter**: Graphql filter that will restrict the records displayed in the table. **Make sure that all properties used by filters have a corresponding column definition under the "*columns*" property.**
-   **isTransient**: If marked as true, the table will be excluded from the automatic data binding process (i.e. no data will be fetched from the server). The default value is false.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **node**: The name of the node that the table represents, it must be defined if you want to use the advanced filtering capabilities.
-   **mapServerRecord(record)=>record**: Allows changing record values before those are rendered to the screen. This function is invoked whenever new entities are fetched from the server. The function is not allowed to modify the ID of the record or any other metadata property. The function may not be called when values are added by the functional code on runtime, only in case of server side communication.

### Event handler decorator properties

-   **onChange**: Event triggered when the table value changes.
-   **onClick**: Event triggered when any parts of the table is clicked, no arguments provided.
-   **onEmptyStateLinkClick**: Function to be executed when the clickable text is clicked
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).
-   **onRowAdded**: Event triggered when a table row is added by the user and successfully committed to the table.
-   **onRowClick**: Event triggered when any part of a table row is clicked.
-   **onRowSelected**: Event triggered when a table row is selected.
-   **onRowUnselected**: Event triggered when a table row is unselected.
-   **onDataLoaded**: Event triggered when new data is received from the server and added to the collection store.
-   **onAllDataLoaded**: Event triggered when all the available data from the server has been loaded.

### Mobile Card decorator properties
The following types can be used as nested fields for the card body (`title`, `titleRight`, `line2`, `line2Right` and etc): 'checkbox', 'icon', 'image', 'label', 'link', 'numeric', 'progress', 'reference', 'text', 'date'.

Card body properties:
-   **title**: Title of the item card.
-   **titleRight**: Support for title, being displayed at its right. Optional property.
-   **line2**: Text that is going to be placed under the `title` in the item card. Optional property.
-   **line2Right**: Text that is going to be placed under the `titleRight` property in the item card. Optional property.
-   **line3**: Text that is going to be placed under the `line2` in the item card. Optional property.
-   **line3Right**: Text that is going to be placed under the `line2Right` property in the item card. Optional property.
-   **line4**: Text that is going to be placed under the `line3` in the item card. Optional property.
-   **line4Right**: Text that is going to be placed under the `line3Right` property in the item card. Optional property.
-   **line5**: Text that is going to be placed under the `line4` in the item card. Optional property.
-   **line5Right**: Text that is going to be placed under the `line4Right` property in the item card. Optional property.

Special card properties:
-   **image**: An image or icon field is displayed on the left side of the card. It is positioned vertically to the center. Optional property.
-   **progressBar**: A progress field which is displayed below the card content. Optional property.

### Other decorator properties

-   **fetchesDefaults**: When set to true and when the table value changes, a request to the server for default values for the whole page will be requested. False by default.

### Record action properties

#### Dropdown action properties

-   **title**: The title of the action displayed at the end of the table under a drop-down menu.
-   **icon**: An optional icon for the action, to be displayed alongside the action title.
-   **onClick**: Event triggered when an action is selected in the drop-down menu.
-   **isDisabled**: Whether the table action is enabled (disabled = false) or not (disabled = true). It can also be defined as callback function that returns a boolean. A table action is enabled by default.
-   **isHidden**: Whether the table action is hidden or not. It can also be defined as callback function that returns a boolean.
-   **children**: List of actions displayed in a submenu.
-   **access**: Access control definition. It links the availability of the action of an API node property. The action is displayed only when the user have access to property.
-   **isDestructive**: If set, the icon corresponding to the action is rendered in red.
-   **insertBefore**:  Specifies the ID of an existing action before which the current action should be inserted. This property is only considered when extending the dropdownActions by extending the table field. If the specified ID is not found, the action will be added at the end.
-   **insertAfter**: Specifies the ID of an existing action after which the current action should be inserted. This property is only considered when extending the dropdownActions by extending the table field. If the specified ID is not found, the action will be added at the end.
-   If only one dropdown action is set, then only one button is displayed, otherwise a dropdown containing the available dropdown actions.

In addition to a dropdown action, a separator line can also added using `ui.menuSeparator({ id?: string, insertAfter?: string, insertBefore?: string })`.

#### Inline action properties

Similar properties as the 'Dropdown action properties', but with the following specifics:

-   **icon**: The icon for the inline action button is mandatory
-   **buttonType**: Type of button, according to Carbon's API. Can be 'primary', 'secondary' or 'tertiary'. Optional, defaults to 'secondary'.
-   **insertBefore**:  Specifies the ID of an existing action before which the current action should be inserted. This property is only considered when extending the inlineActions by extending the table field. If the specified ID is not found, the action will be added at the end.
-   **insertAfter**: Specifies the ID of an existing action after which the current action should be inserted. This property is only considered when extending the inlineActions by extending the table field. If the specified ID is not found, the action will be added at the end.

## Runtime functions

-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **refreshRecord(recordId, skipSet=false)**: Like refetch but applied only on a row level. If the skipSet flag is set to true, the value is returned but not applied to the table.
-   **redraw(columnBind?: string)**: Redraws the grid element on the screen and executes all property callback functions. If a `columnBind` property is passed, it only redraws that column.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **getNewRecords()**: Returns the new, unsaved records that were addded to the table.
-   **setRecordValue(rowValue: any)**: Updates a single row on the table and keeps update history.
-   **getRecordByFieldValue(fieldName: keyof NestedRecordType, fieldValue: any)**: Find a row based on a property's value
-   **getRecordValue(recordId:string)**: Returns a single row. It can only return rows that were previously loaded to the client side-cache by some other user interaction.
-   **addRecord(rowValue: any)**: Adds a single row to the table and marks it as a new row.
-   **addOrUpdateRecordValue(rowValue: any)**: Add or update row in the table depending of the existence of the ID field.
-   **removeRecord(recordId:string)**: Removes a single row from the displayed table. It does NOT delete the corresponding server side entity.
-   **selectRecord(recordId:string)**: Selects a single row.
-   **generateRecordId()**: Returns an auto-generated ID that can be used for a new row.
-   **unselectRecord(recordId:string)**: Unselects a single row.
-   **unselectAllRecords()**: Unselects all selected rows.
-   **validate(forceValidation: boolean)**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result. This validation will only trigger on dirty records. If the `forceValidation` argument is set to true, all records will be validated regardless of their dirty state.
-   **validateWithDetails(forceValidation: boolean)**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID. This validation will only trigger on dirty records. If the `forceValidation` argument is set to true, all records will be validated regardless of their dirty state.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **calculateAggregatedValue**: Calculate aggregated values ('min' | 'max' | 'sum' | 'avg' | 'count'). Aggregations are calculated on the client as long as all records present on the server have been fetched otherwise `Number.NaN` is returned.
-   **hideColumn(columnBind: string)**: Hides the column
-   **showColumn(columnBind: string)**: Shows the column
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **openSidebar()**: Opens the sidebar of the table. For more details see [here](./Table+Sidebar).
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

### Column properties

-   **isHiddenDesktop**: Specifies whether the corresponding column should be displayed for desktop-size viewports.
-   **isHiddenOnMainField**: Specifies whether the column appears unchecked/hidden in the columns panel.

Please refer to the documentation of the corresponding nested field type.

### Mobile Card Properties

-   **title**: Specifies the nested component to be rendered in the top-left corner of the mobile card.
-   **titleRight**: Specifies the nested component to be rendered in the top-right corner of the mobile card.
-   **subtitle**: Specifies the nested component to be rendered in the bottom-left corner of the mobile card.
-   **subtitleRight**: Specifies the nested component to be rendered in the bottom-right corner of the mobile card.
-   **image**: Specifies nested image component to be rendered in the mobile card. If specified and the provided image source is truthy, the card displays the image. Otherwise, the card attempts to display the first three letters of the component rendered in the title component. If the title component or it's textual representation is also falsy, the component will display a placeholder instead.

## Adding columns in extension pages

Additional fields can be added to a table in extension page artifacts using the `tableFieldOverride` decorator. In this case, the `columns` decorator property accepts an `nestedFieldExtension` array. This set of nested fields comes with an additional decorator property which determines the position of the extension columns within the original table. This property is called `insertBefore`.

### Positioning extension fields within the table

The `insertBefore` decorator property determines the position of the extension field.
#### Not provided
If this decorator property is not provided, the extension field is added to the end of the columns.

#### Using the bind value
The position of the extension column can be set by providing the `bind` decorator property value of the base column that the field should be inserted before to the `insertBefore` extension column decorator property.

#### Base reference fields bound to the same property
When the base page contains reference field based columns that are bound to the same property, the position of the additional column can be set more precisely by including the `valueField` property value into the `insertBefore` extension property. This must be done by joining the two values by a double underscore in the following format: `bind__valueField`. For example if the field should be positioned before a reference field that is bound to `product` and the displayed value field is `description`, than the `insertBefore` extension property would be `product__description`.

### Example

```ts
import * as ui from '@sage/xtrem-ui';

...

export class TableExtension extends ui.PageExtension<Table, GraphApi> {

    ...

    @ui.decorators.tableFieldOverride<TableExtension, ShowCaseProduct>({
        title: 'Overridden Title',
        columns: [
            ui.nestedFieldExtensions.reference<TableExtension, ShowCaseProduct, BiodegradabilityCategory>({
                bind: 'biodegradabilityCategory',
                valueField: 'description',
                node: '@sage/xtrem-show-case-bundle/BiodegradabilityCategory',
                // This column is inserted before the `product` bound reference column that has `name` valueField configuration
                insertBefore: 'product__name',
                columns: [
                    ui.nestedFields.text({ bind: 'description', title: 'Description' }),
                    ui.nestedFields.text({ bind: 'energyToProduce', title: 'Energy to produce' })
                ],
                title: 'Biodegradability category',
                isAutoSelectEnabled: true,
            }),
            // This column goes to the end.
            ui.nestedFieldExtensions.text<TableExtension>({
                bind: 'someTransientFieldAtTheEnd',
                isTransient: true,
                title: 'Transient ext column',
            }),
            // This column goes to the end.
            ui.nestedFieldExtensions.text<TableExtension>({
                bind: 'someTransientFieldAtTheVeryEnd',
                isTransient: true,
                title: 'Transient ext column 2',
            }),
            // This column is inserted before the `provider` bound base column.
            ui.nestedFieldExtensions.numeric<TableExtension>({
                bind: 'someTransientFieldInTheMiddle',
                isTransient: true,
                title: 'Transient number',
                insertBefore: 'provider',
                scale: 5
            }),
        ]
    })
    field: ui.fields.Table;
}
```
## Overriding columns in extension pages

Table's columns can be overridden in extension page artifacts using the `tableFieldOverride` decorator. In this case, the `columnOverrides` decorator property accepts an `nestedFieldOverrides` array. This set of nested fields have to match the bind values of the columns to extend.

### Example

```ts
import * as ui from '@sage/xtrem-ui';

...

export class TableExtension extends ui.PageExtension<Table, GraphApi> {

    ...

    @ui.decorators.tableFieldOverride<TableExtension, ShowCaseProduct>({
        title: 'Overridden Title',
        columnOverrides: [
            ui.nestedFieldOverrides.numeric<TableExtension>({ // same type as the base column
                bind: 'qty', // same bind as the base column
                scale: 1, // property to override
                insertBefore: 'price', // insertBefore works the same as in nestedFieldExtensions
            }),
            ui.nestedFieldOverrides.reference<TableExtension, ShowCaseProduct, ShowCaseProvider>({
                bind: 'provider',
                valueField: 'textField', // same valueField as the base column
                title: 'Provider Overriden title', // property to override
            }),
        ]
    })
    field: ui.fields.Table;
}
```
## Overriding mobile card

Table's mobile card display can be overridden in extension page artifacts using the `tableFieldOverride` decorator. In this case, the `mobileCard` decorator property accepts a card definition object with properties as defined in [Mobile Card decorator properties](#mobile-card-decorator-properties), which for this case can also be nullable to remove the original property from the extended mobile card.

### Example

```ts
import * as ui from '@sage/xtrem-ui';

...

export class TableExtension extends ui.PageExtension<Table, GraphApi> {

    ...

    @ui.decorators.tableFieldOverride<TableExtension, ShowCaseProduct>({
        mobileCard: {
            title: ui.nestedFields.text({ bind: 'description', title: 'Description' }),
            titleRight: null,
            line2: ui.nestedFields.label<TableExtension, ShowCaseProduct>({
                bind: 'amount',
                title: 'Amount',
                prefix: '$',
            }),
        },
    })
    field: ui.fields.Table;
}
```

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Table).
