import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { TableComponentProps } from './table-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import type { DesktopTableComponent } from './desktop-table-component';

const ConnectedTableComponent = React.lazy(() => import('./table-component'));

export function AsyncConnectedTableComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="200px" />}>
            <ConnectedTableComponent {...props} />
        </React.Suspense>
    );
}

const TableComponentWithRef = React.lazy(() =>
    import('./table-component').then(c => {
        const TableComponentWithRef = React.forwardRef<DesktopTableComponent, TableComponentProps>((props, ref) => (
            <c.TableComponent ref={ref as any} {...props} />
        ));
        TableComponentWithRef.displayName = 'TableComponentWithRef';
        return {
            default: TableComponentWithRef,
        };
    }),
);

export const AsyncTableComponent = React.forwardRef<DesktopTableComponent, TableComponentProps>(
    (props, ref): React.ReactElement => {
        return (
            <React.Suspense fallback={<InputFieldSkeleton hasTitle={true} bodyHeight="200px" />}>
                <TableComponentWithRef ref={ref} {...props} />
            </React.Suspense>
        );
    },
);

AsyncTableComponent.displayName = 'AsyncTableComponent';
