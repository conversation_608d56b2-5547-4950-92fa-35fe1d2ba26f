/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import { partition } from 'lodash';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { TableControlObject } from '../../control-objects';
import { allowedMainFieldTypes } from '../../nested-fields';
import { FieldKey } from '../../types';
import type { TableDecoratorProperties, TableExtensionDecoratorProperties } from './table-component-types';
import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import {
    addColumnsToProperties,
    addMobileCardDefinitionToProperties,
    addNodeToProperties,
} from '../../../utils/data-type-utils';

class TableDecorator extends AbstractFieldDecorator<FieldKey.Table> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = TableControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<TableDecoratorProperties> {
        const properties: Partial<TableDecoratorProperties> = {};
        addNodeToProperties({ dataType, propertyDetails, properties });
        addColumnsToProperties({
            dataType,
            propertyDetails,
            properties,
            dataTypes: this.dataTypes,
            nodeTypes: this.nodeTypes,
        });
        addMobileCardDefinitionToProperties({
            dataType,
            propertyDetails,
            properties,
            dataTypes: this.dataTypes,
            nodeTypes: this.nodeTypes,
        });

        return properties;
    }
}

/**
 * Initializes the decorated member as a [Table]{@link TableControlObject} field with the provided properties
 *
 * @param properties The properties that the [Table]{@link TableControlObject} field will be initialized with
 */
export function tableField<CT extends ScreenExtension<CT>, ReferencedItemType extends ClientNode = any>(
    properties: TableDecoratorProperties<Extend<CT>, ReferencedItemType>,
): (target: CT, name: string) => void {
    // Backwards support of the renamed rowAction property. In case of X3, they may deploy older platform with new app code. We should remove this around August 2023.
    if ((properties as any).rowActions) {
        // eslint-disable-next-line no-param-reassign
        properties = {
            ...properties,
            dropdownActions: (properties as any).rowActions,
        };
    }

    if (Object.prototype.hasOwnProperty.call(properties, 'mainField')) {
        const mainField = properties.mainField;
        const [allowedColumns, disallowedColumns] = partition(properties.columns, c =>
            allowedMainFieldTypes.includes(c.type),
        );
        const allowedColumnBinds = allowedColumns.map(c => `"${c.properties.bind}"`);
        if (disallowedColumns.find(c => c.properties.bind === mainField)) {
            throw new Error(
                `"${mainField}" cannot be used as "mainField". Please use one of the following values: ${allowedColumnBinds.join(
                    ', ',
                )}.`,
            );
        }
        if (!allowedColumns.find(c => c.properties.bind === mainField)) {
            throw new Error(
                `"${mainField}" cannot be used as "mainField". Please use one of the following values: ${allowedColumnBinds.join(
                    ', ',
                )} or add a new column bound to "${mainField}".`,
            );
        }
    }
    return standardDecoratorImplementation<CT, FieldKey.Table, ReferencedItemType>(
        properties,
        TableDecorator,
        FieldKey.Table,
        true,
    );
}

export function tableFieldOverride<CT extends ScreenExtension<CT>, ReferencedItemType extends ClientNode = any>(
    properties: TableExtensionDecoratorProperties<CT, ReferencedItemType>,
): (target: CT, name: string) => void {
    return standardExtensionDecoratorImplementation<CT, FieldKey.Table, ReferencedItemType>(properties);
}
