/**
 * @packageDocumentation
 * @module root
 * */
import type { ClientNode } from '@sage/xtrem-client';
import { uniq, without } from 'lodash';
import type { AppThunkDispatch } from '../../../redux';
import { getStore } from '../../../redux';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { TableDecoratorProperties } from '../../decorator-properties';
import type { GridNestedFieldTypes } from '../../nested-fields';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldKey, PartialCollectionValueWithIds } from '../../types';
import { TableDisplayMode } from '../../types';
import { CollectionValueControlObject } from '../collection-value-field';
import type { InternalTableProperties, TableProperties } from './table-component-types';
import { openTableSidebar } from '../../../redux/actions';
import { getNestedFieldsFromProperties } from '../../../utils/nested-field-utils';
import type { TableSidebarDialogContent } from '../../../types/dialogs';

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a set of values of any type. It can contain nested fields
 */
export class TableControlObject<
    NestedRecordType extends ClientNode = any,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends CollectionValueControlObject<
    FieldKey.Table,
    NestedRecordType,
    CT,
    GridNestedFieldTypes,
    InternalTableProperties<CT, NestedRecordType>
> {
    static readonly defaultUiProperties: Partial<TableDecoratorProperties> = {
        ...ReadonlyFieldControlObject.defaultUiProperties,
        canAddNewLine: false,
        canExport: false,
        canFilter: true,
        canResizeColumns: false,
        canSelect: true,
        canUserHideColumns: true,
        displayMode: TableDisplayMode.comfortable,
        fetchesDefaults: false,
        isPhantomRowDisabled: false,
        isReadOnly: false,
        mainField: '_id',
        pageSize: 20,
    };

    /** Whether user can hide columns or not  */
    get canUserHideColumns(): boolean {
        const propertyValue = this.getUiComponentProperty('canUserHideColumns');
        return propertyValue === undefined ? true : propertyValue;
    }

    /** Whether user can hide columns or not  */
    set canUserHideColumns(newValue: boolean) {
        this.setUiComponentProperties('canUserHideColumns', newValue);
    }

    @ControlObjectProperty<TableProperties<CT, NestedRecordType>, TableControlObject<NestedRecordType, CT>>()
    /** Whether all table columns can be resized or not */
    canResizeColumns?: boolean;

    @ControlObjectProperty<TableProperties<CT, NestedRecordType>, TableControlObject<NestedRecordType, CT>>()
    /** Whether the rows of this table can be filtered or not */
    canFilter?: boolean;

    @ControlObjectProperty<TableProperties<CT, NestedRecordType>, TableControlObject<NestedRecordType, CT>>()
    /** Whether the "phantom row" will be displayed or not */
    isPhantomRowDisabled?: boolean;

    @ControlObjectProperty<TableProperties<CT, NestedRecordType>, TableControlObject<NestedRecordType, CT>>()
    /** Whether the rows of this table can be filtered or not */
    isReadOnly?: boolean;

    hideColumn(columnBind: string): void {
        const currentValue = this.getUiComponentProperty('hiddenColumns') || [];
        this.setUiComponentProperties('hiddenColumns', uniq([...currentValue, columnBind]));
    }

    showColumn(columnBind: string): void {
        const currentValue = this.getUiComponentProperty('hiddenColumns') || [];
        this.setUiComponentProperties('hiddenColumns', without(currentValue, columnBind));
    }

    /** Return records that the user just added to this table and not yet known by the server. */
    getNewRecords(): PartialCollectionValueWithIds<NestedRecordType>[] {
        this.ensureFieldHasValue();
        const value = this._getValue();
        if (!value) {
            return [];
        }

        return value.getNewRecords();
    }

    get node(): string {
        return String(this.getUiComponentProperty('node'));
    }

    /** Redraws the current table view, this function can be useful when values are updated that are used in property callbacks. */
    redraw(columnBind?: string): Promise<void> {
        return this._redraw(columnBind);
    }

    openSidebar(recordId?: string): void {
        const dispatch = getStore().dispatch as AppThunkDispatch;
        dispatch(
            openTableSidebar({
                cardDefinition: this.properties.mobileCard,
                columns: getNestedFieldsFromProperties(this.properties),
                elementId: this.elementId,
                recordId,
                screenId: this.screenId,
                sidebarDefinition: this.properties.sidebar,
            }),
        );
    }

    refresh(keepModifications = false): Promise<void> {
        const state = getStore().getState();
        const openSidebarDialogContent = Object.values(state.activeDialogs).some(
            dialog =>
                dialog.screenId === this.screenId &&
                dialog.type === 'table-sidebar' &&
                (dialog.content as TableSidebarDialogContent).elementId === this.elementId,
        );

        if (openSidebarDialogContent) {
            throw new Error('Tables cannot be refreshed while the sidebar is open');
        }

        return super.refresh(keepModifications);
    }
}
