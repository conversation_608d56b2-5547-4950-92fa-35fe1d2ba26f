import * as React from 'react';
import type { TableInternalComponentProps } from './table-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import type { DesktopTableComponent } from './desktop-table-component';

const DesktopTableComponentWithRef = React.lazy(() =>
    import('./desktop-table-component').then(({ default: DesktopTableComponent }) => {
        const TableComponentWithRef = React.forwardRef<
            DesktopTableComponent,
            React.ComponentProps<typeof DesktopTableComponent>
        >((props, ref) => <DesktopTableComponent ref={ref as any} {...props} />);
        TableComponentWithRef.displayName = 'DesktopTableComponentWithRef';
        return {
            default: TableComponentWithRef,
        };
    }),
);

export const AsyncDesktopTableComponent = React.forwardRef<DesktopTableComponent, TableInternalComponentProps>(
    (props, ref) => {
        return (
            <React.Suspense fallback={<InputFieldSkeleton hasTitle={false} bodyHeight="200px" />}>
                <DesktopTableComponentWithRef ref={ref} {...props} />
            </React.Suspense>
        );
    },
);

AsyncDesktopTableComponent.displayName = 'AsyncDesktopTableComponent';
