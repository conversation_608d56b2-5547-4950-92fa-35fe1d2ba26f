import * as React from 'react';
import { connect } from 'react-redux';
import { type ValidationResult } from '../../..';
import * as xtremRedux from '../../../redux';
import type { CollectionValue } from '../../../service/collection-data-service';
import { runAndDispatchFieldValidation } from '../../../service/dispatch-service';
import { convertFilterDecoratorToGraphQLFilter } from '../../../service/graphql-query-builder';
import type { PageDefinition } from '../../../service/page-definition';
import type { ScreenBase } from '../../../service/screen-base';
import { getScreenElement } from '../../../service/screen-base-definition';
import { ContextType, type NestedRecordId } from '../../../types';
import { getDataTestIdAttribute, isHidden } from '../../../utils/dom';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { CollectionItem } from '../../types';
import { getFieldTitle, isFieldDisabled } from '../carbon-helpers';
import { HelperText } from '../carbon-utility-components';
import { AsyncDesktopTableComponent } from './async-desktop-table-component';
import { MobileTableComponent } from './mobile-table-component';
import type {
    InternalTableProperties,
    TableComponentExternalProps,
    TableComponentProps,
    TableDecoratorProperties,
} from './table-component-types';
import type { LocalizeLocale } from '@sage/xtrem-shared';
import type { TableSummaryControlObject } from '../field-control-objects';
import type { DesktopTableComponent } from './desktop-table-component';

export const TableComponent = React.forwardRef<DesktopTableComponent, TableComponentProps>((props, ref) => (
    <TableComponentInternal tableRef={ref} {...props} />
));
TableComponent.displayName = 'TableComponent';

export class TableComponentInternal extends React.Component<TableComponentProps> {
    componentDidMount(): void {
        this.props.ensureFieldHasValue?.();
    }

    getComponentClass = (): string => {
        const className = ['e-field', 'e-table-field'];
        if (
            isHidden(this.props.item, this.props.browser) ||
            resolveByValue({
                screenId: this.props.screenId,
                skipHexFormat: true,
                propertyValue: this.props.fieldProperties.isHidden,
                rowValue: null,
            })
        ) {
            className.push('e-hidden');
        }

        if (this.props.fieldProperties.isFullWidth) {
            className.push('full-width');
        }

        if (this.props.fieldProperties.isHelperTextHidden) {
            className.push('e-helper-text-hidden');
        }

        if (this.props.fieldProperties.isTitleHidden) {
            className.push('e-title-hidden');
        }

        if (
            this.props.isParentDisabled ||
            isFieldDisabled(this.props.screenId, this.props.fieldProperties, undefined, undefined)
        ) {
            className.push('e-disabled');
        }

        if (this.props.fieldProperties.isReadOnly) {
            className.push('e-read-only');
        }

        return className.join(' ');
    };

    onRowClick =
        (recordId: NestedRecordId, newRecord?: CollectionItem, isModifierKeyPushed = false) =>
        (): void => {
            if (this.props.value) {
                const temporaryRecords =
                    resolveByValue({
                        propertyValue: this.props.fieldProperties.additionalLookupRecords,
                        screenId: this.props.screenId,
                        rowValue: null,
                        fieldValue: null,
                        skipHexFormat: true,
                    }) || [];
                const rowValue =
                    this.props.fieldProperties.isNewEnabled && newRecord
                        ? newRecord
                        : splitValueToMergedValue(this.props.value.getRawRecord({ id: recordId, temporaryRecords }));

                if (this.props.onRowClick) {
                    this.props.onRowClick(rowValue, isModifierKeyPushed);
                } else {
                    triggerFieldEvent(
                        this.props.screenId,
                        this.props.elementId,
                        'onRowClick',
                        recordId,
                        splitValueToMergedValue(rowValue),
                        isModifierKeyPushed as any,
                    );
                }
            }
        };

    render(): React.ReactNode {
        const isMobileTable = this.props.fieldProperties.cardView || !this.props.browser.greaterThan.s;
        const hasOnRowClickListener =
            this.props.onRowClick || (this.props.fieldProperties as TableDecoratorProperties<any>).onRowClick;
        const isNavigationPanel = this.props.contextType === ContextType.navigationPanel;

        return (
            <div
                className={this.getComponentClass()}
                style={{
                    height: this.props.fixedHeight ? this.props.fixedHeight + (isNavigationPanel ? 0 : 52) : undefined,
                }}
                data-testid={getDataTestIdAttribute(
                    'table',
                    getFieldTitle(this.props.screenId, this.props.fieldProperties, null),
                    this.props.elementId,
                )}
            >
                {!isMobileTable && (
                    <AsyncDesktopTableComponent
                        ref={this.props.tableRef}
                        accessBindings={this.props.accessBindings}
                        additionalLookupRecords={this.props.additionalLookupRecords}
                        bulkActions={this.props.bulkActions}
                        contextType={this.props.contextType}
                        dataTypes={this.props.dataTypes}
                        elementId={this.props.elementId}
                        enumTypes={this.props.enumTypes}
                        errorMessage={this.props.errorMessage}
                        fieldProperties={this.props.fieldProperties}
                        fixedHeight={this.props.fixedHeight ? this.props.fixedHeight : undefined}
                        isAutoSelectEnabled={this.props.isAutoSelectEnabled}
                        isLookupDialog={this.props.isLookupDialog}
                        isParentDisabled={this.props.isParentDisabled}
                        isReadOnly={this.props.fieldProperties.isReadOnly}
                        isUsingInfiniteScroll={
                            this.props.fieldProperties.canAddNewLine || this.props.isUsingInfiniteScroll
                        }
                        locale={this.props.locale}
                        selectionMode={this.props.selectionMode}
                        nodeTypes={this.props.nodeTypes}
                        numberOfVisibleRows={this.props.numberOfVisibleRows}
                        onFocus={this.props.onFocus}
                        onRowClick={this.onRowClick}
                        recordContext={this.props.recordContext}
                        screenId={this.props.screenId}
                        setFieldProperties={this.props.setFieldProperties}
                        setGlobalLoading={this.props.setGlobalLoading}
                        tableUserSettings={this.props.tableUserSettings}
                        username={this.props.username}
                        validationErrors={this.props.validationErrors}
                        value={this.props.value}
                    />
                )}
                {isMobileTable && (
                    <MobileTableComponent
                        accessBindings={this.props.accessBindings}
                        additionalLookupRecords={this.props.additionalLookupRecords}
                        availableColumns={this.props.availableColumns}
                        bulkActions={this.props.bulkActions}
                        canDragCard={this.props.canDragCard}
                        contextType={this.props.contextType}
                        dataTypes={this.props.dataTypes}
                        elementId={this.props.elementId}
                        enumTypes={this.props.enumTypes}
                        fieldProperties={this.props.fieldProperties}
                        fixedHeight={this.props.fixedHeight}
                        groupByField={this.props.groupByField}
                        isAutoSelectEnabled={this.props.isAutoSelectEnabled}
                        isParentDisabled={this.props.isParentDisabled}
                        isUsingInfiniteScroll={this.props.isUsingInfiniteScroll}
                        locale={this.props.locale}
                        nodeTypes={this.props.nodeTypes}
                        onRowClick={hasOnRowClickListener ? this.onRowClick : undefined}
                        recordContext={this.props.recordContext}
                        screenId={this.props.screenId}
                        searchText={this.props.searchText}
                        setFieldProperties={this.props.setFieldProperties}
                        tableUserSettings={this.props.tableUserSettings}
                        validationErrors={this.props.validationErrors}
                        value={this.props.value}
                    />
                )}
                {this.props.fieldProperties.helperText && (
                    <HelperText helperText={this.props.fieldProperties.helperText} />
                )}
            </div>
        );
    }
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: TableComponentExternalProps): TableComponentProps => {
    const screenDefinition = state.screenDefinitions[props.screenId] as PageDefinition;
    const fieldProperties = screenDefinition.metadata.uiComponentProperties[
        props.elementId
    ] as TableDecoratorProperties;
    const pageProperties = screenDefinition.metadata.uiComponentProperties[props.screenId];

    if (fieldProperties.onEmptyStateLinkClick) {
        fieldProperties.onEmptyStateLinkClick.bind(getScreenElement(screenDefinition));
    }

    return {
        ...props,
        accessBindings: screenDefinition.accessBindings || {},
        browser: state.browser,
        dataTypes: state.dataTypes,
        ensureFieldHasValue: (
            screenDefinition.metadata.controlObjects[props.elementId] as TableSummaryControlObject
        ).ensureFieldHasValue.bind(screenDefinition.metadata.controlObjects[props.elementId]),
        enumTypes: state.enumTypes,
        fieldProperties: {
            ...fieldProperties,
            filter: convertFilterDecoratorToGraphQLFilter(screenDefinition, fieldProperties.filter),
            isTransient: pageProperties.isTransient || fieldProperties.isTransient,
        },
        locale: (state.applicationContext?.locale as LocalizeLocale) || 'base',
        nodeTypes: state.nodeTypes,
        setFieldProperties: xtremRedux.actions.actionStub,
        tableUserSettings: state.screenDefinitions?.[props.screenId]?.userSettings?.[props.elementId] || {},
        username: state.applicationContext?.login,
        validate: xtremRedux.actions.actionStub,
        validationErrors: screenDefinition.errors[props.elementId] || [],
        value: screenDefinition.values[props.elementId],
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: TableComponentExternalProps,
): Partial<TableComponentProps> => {
    return {
        setFieldProperties: (elementId: string, value: InternalTableProperties<ScreenBase>): any => {
            dispatch(xtremRedux.actions.setFieldProperties(props.screenId, elementId, value));
        },
        setGlobalLoading: (loaderState: boolean): any => {
            dispatch(xtremRedux.actions.setGlobalLoading(loaderState));
        },
        validate: (elementId: string, value: CollectionValue): Promise<ValidationResult[] | undefined> =>
            runAndDispatchFieldValidation(props.screenId, elementId, value.getNormalizedChangedRecords()),
    };
};

export const ConnectedTableComponent = connect(mapStateToProps, mapDispatchToProps)(TableComponent);

export default ConnectedTableComponent;
