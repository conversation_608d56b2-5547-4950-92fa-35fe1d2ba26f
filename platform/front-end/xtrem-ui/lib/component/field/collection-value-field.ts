import type { ClientN<PERSON>, Filter } from '@sage/xtrem-client';
import type { Dict } from '@sage/xtrem-shared';
import { CollectionValue } from '../../service/collection-data-service';
import type { AggregationMethod, InternalValue } from '../../service/collection-data-types';
import { fetchNestedDefaultValues } from '../../service/graphql-service';
import type { GraphQLFilter } from '../../service/graphql-utils';
import type { ScreenBase } from '../../service/screen-base';
import type { ValidationResult } from '../../service/screen-base-definition';
import { showToast } from '../../service/toast-service';
import type { NestedRecordId, ScreenBaseGenericType, ScreenExtension } from '../../types';
import { xtremConsole } from '../../utils/console';
import { getNestedFieldsFromProperties } from '../../utils/nested-field-utils';
import { resolveByValue } from '../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../utils/transformers';
import type { ValueOrCallbackWithFieldValue } from '../../utils/types';
import type { NestedFieldTypes } from '../nested-fields';
import { ControlObjectProperty } from '../property-decorators/control-object-property-decorator';
import type { ReadonlyFieldProperties } from '../readonly-field-control-object';
import { ReadonlyFieldControlObject } from '../readonly-field-control-object';
import type {
    CollectionValueFieldKey,
    FieldControlObjectConstructorProps,
    OrderByType,
    PartialCollectionValue,
    PartialCollectionValueWithIds,
} from '../types';
import type { HasFilter, HasServerRecordMapperFunction } from './traits';

export interface CollectionValueFieldProperties<
    CT extends ScreenExtension<CT> = ScreenBase,
    NestedRecordType extends ClientNode = any,
> extends HasFilter<CT, NestedRecordType>,
        HasServerRecordMapperFunction<CT, NestedRecordType>,
        ReadonlyFieldProperties<CT> {
    /** Selected records identifiers */
    selectedRecords?: NestedRecordId[];

    /** The GraphQL node that the table represents, needed for filtering */
    node?: keyof ScreenBaseGenericType<CT>;

    /** The column or the set of columns which the table should be sorted by */
    orderBy?: OrderByType<NestedRecordType>;

    /** Number of lines displayed by default in the table, if not defined it defaults to 20 */
    pageSize?: number;

    /** Whether the records of the table can be selected or not. Defaults to true */
    canSelect?: boolean;

    /** Indicate additional warning message, rendered as tooltip and blue border. */
    infoMessage?: ValueOrCallbackWithFieldValue<CT, string, any, NestedRecordType>;

    /** Indicate additional information, rendered as tooltip and orange border. */
    warningMessage?: ValueOrCallbackWithFieldValue<CT, string, any, NestedRecordType>;
}

export abstract class CollectionValueControlObject<
    FT extends CollectionValueFieldKey,
    NestedRecordType extends ClientNode = any,
    CT extends ScreenExtension<CT> = ScreenBase,
    AvailableNestedFieldTypes extends NestedFieldTypes = NestedFieldTypes,
    FieldProps extends CollectionValueFieldProperties<CT, NestedRecordType> = CollectionValueFieldProperties<
        CT,
        NestedRecordType
    >,
> extends ReadonlyFieldControlObject<CT, CollectionValueFieldKey, FieldProps, NestedRecordType> {
    protected readonly _isFieldDirty: (screenId: string, elementId: string) => boolean;

    protected _setFieldDirty: (screenId: string, elementId: string) => void;

    protected _setFieldClean: (screenId: string, elementId: string) => void;

    constructor(properties: FieldControlObjectConstructorProps<FT>) {
        super(properties);
        this._isFieldDirty = properties.isFieldDirty;
        this._setFieldDirty = properties.setFieldDirty;
        this._setFieldClean = properties.setFieldClean;
    }

    private readonly createCollectionValue = (
        value: PartialCollectionValue<NestedRecordType>[],
        filter?: GraphQLFilter<NestedRecordType>,
    ): CollectionValue<NestedRecordType> => {
        const columns = getNestedFieldsFromProperties<NestedRecordType>(this.uiComponentProperties as any);
        return new CollectionValue<NestedRecordType>({
            bind: this.uiComponentProperties.bind,
            screenId: this.screenId,
            elementId: this.elementId,
            isTransient: !!this.getUiComponentProperty('isTransient'),
            hasNextPage: false,
            orderBy: [this.getUiComponentProperty('orderBy')],
            columnDefinitions: [columns],
            nodes: [this.getUiComponentProperty('node') as string],
            mapServerRecordFunctions: [this.getUiComponentProperty('mapServerRecord')],
            filter: [filter],
            initialValues: value,
        });
    };

    public ensureFieldHasValue(): CollectionValue<NestedRecordType> {
        let value = this._getValue();
        if (!value) {
            value = this.createCollectionValue([], this.filter);
            this._setValue(value);
        }

        return value;
    }

    /** Graphql filter that will restrict the records displayed in the table */
    get filter(): GraphQLFilter<NestedRecordType> | undefined {
        return resolveByValue({
            fieldValue: undefined,
            propertyValue: this.getUiComponentProperty('filter'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /** Graphql filter that will restrict the records displayed in the table */
    set filter(filter: GraphQLFilter<NestedRecordType> | undefined) {
        this.setUiComponentProperties('filter', filter);
        this.refresh(true).catch(() => {
            /* Intentional fire and forget */
        });
    }

    @ControlObjectProperty<
        CollectionValueFieldProperties<CT, NestedRecordType>,
        CollectionValueControlObject<FT, NestedRecordType, CT, AvailableNestedFieldTypes, FieldProps>
    >()
    /** Number of lines displayed by default in the table, if not defined it defaults to 20 */
    pageSize?: number;

    @ControlObjectProperty<
        CollectionValueFieldProperties<CT, NestedRecordType>,
        CollectionValueControlObject<FT, NestedRecordType, CT, AvailableNestedFieldTypes, FieldProps>
    >()
    /** Whether the records of this table can be selected or not */
    canSelect?: boolean;

    get isDirty(): boolean {
        return this._isFieldDirty(this.screenId, this.elementId);
    }

    set isDirty(newValue: boolean) {
        if (newValue) {
            this._setFieldDirty(this.screenId, this.elementId);
        } else {
            this._setFieldClean(this.screenId, this.elementId);
        }
    }

    /**
     * Indicate additional warning message, rendered as tooltip and blue border.
     */
    get infoMessage(): string | undefined {
        return resolveByValue({
            fieldValue: this.value,
            propertyValue: this.getUiComponentProperty('infoMessage'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /**
     * Indicate additional warning message, rendered as tooltip and blue border.
     */
    set infoMessage(infoMessage: string | undefined) {
        this.setUiComponentProperties('infoMessage', infoMessage);
    }

    /**
     * Indicate additional information, rendered as tooltip and orange border
     */
    get warningMessage(): string | undefined {
        return resolveByValue({
            fieldValue: this.value,
            propertyValue: this.getUiComponentProperty('warningMessage'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /**
     * Indicate additional information, rendered as tooltip and orange border
     */
    set warningMessage(warningMessage: string | undefined) {
        this.setUiComponentProperties('warningMessage', warningMessage);
    }

    /**
     * Return all table records known to the client, it does not include records which were not previously fetched by some
     * user interaction such as pagination
     * */
    get value(): PartialCollectionValueWithIds<NestedRecordType>[] {
        const value = this._getValue();
        if (!value) {
            return [];
        }

        return value.getFormattedActiveRecords() as PartialCollectionValueWithIds<NestedRecordType>[];
    }

    /**
     * Resets the value of the table, all pending changes are be overridden and all cached records are discarded
     * */
    set value(newValue: PartialCollectionValueWithIds<NestedRecordType>[]) {
        if (!this.getUiComponentProperty('isTransient')) {
            xtremConsole.warn(
                'Reassigning table values is discouraged. Please update individual records using the setRecordValue(), addRecord(), removeRecord() functions.',
            );
        }
        const filter = this.getUiComponentProperty('filter') as Filter<NestedRecordType> | undefined;

        this._setValue(this.createCollectionValue(newValue, typeof filter !== 'function' ? filter : undefined));
    }

    async revalidate(fn: (record: PartialCollectionValueWithIds<NestedRecordType>) => boolean): Promise<void> {
        const value = this._getValue();
        if (!value) {
            return;
        }
        const invalidateValidationState = (
            invalidRecord: InternalValue<NestedRecordType>,
            shouldNotifySubscribers = false,
        ): void => {
            value.addOrUpdateRecordValue({
                recordData: { ...invalidRecord, __validationState: {} },
                level: invalidRecord.__level,
                shouldNotifySubscribers,
                includeUnloaded: true,
            });
        };
        value.getInvalidUnloadedRecords().forEach(unloadedRecord => invalidateValidationState(unloadedRecord));
        const invalidLoadedeRecord = value.getInvalidLoadedRecords().filter(fn);
        invalidLoadedeRecord.forEach(record => {
            invalidateValidationState(record, true);
        });
        await Promise.all(invalidLoadedeRecord.map(recordData => value.runValidationOnRecord({ recordData })));
    }

    /** Update the value of a single record in the collection */
    setRecordValue(recordData: PartialCollectionValueWithIds<NestedRecordType>): void {
        const value = this.ensureFieldHasValue();
        value.setRecordValue({ recordData });
    }

    /** Return a single record that is already known to the client */
    getRecordValue(recordId: string): PartialCollectionValueWithIds<NestedRecordType> | null {
        this.ensureFieldHasValue();
        const value = this._getValue();
        if (!value) {
            return null;
        }

        return splitValueToMergedValue(value.getRawRecord({ id: recordId }));
    }

    /** Return a single record that is already known to the client along with all its internal properties */
    getInternalRowValue(recordId: string): PartialCollectionValueWithIds<NestedRecordType> | null {
        this.ensureFieldHasValue();
        const value = this._getValue();
        if (!value) {
            return null;
        }

        return value.getRawRecord({ id: recordId, cleanMetadata: false });
    }

    /** Add a single record in the table and in its dataset */
    addRecord(recordData: PartialCollectionValue<NestedRecordType>): PartialCollectionValueWithIds<NestedRecordType> {
        return this.addOrUpdateRecordValue(recordData) as PartialCollectionValueWithIds<NestedRecordType>;
    }

    /** Add a single record in the table and in its dataset with default values coming from the server */
    async addRecordWithDefaults(
        recordData?: PartialCollectionValue<NestedRecordType>,
    ): Promise<PartialCollectionValueWithIds<NestedRecordType>> {
        const value = this.ensureFieldHasValue();
        const fetchedRecordValue: Dict<any> = await fetchNestedDefaultValues({
            screenId: this.screenId,
            elementId: this.elementId,
            data: recordData,
        });
        delete fetchedRecordValue.nestedDefaults._id;
        return value.addOrUpdateRecordValue({ recordData: { ...recordData, ...fetchedRecordValue.nestedDefaults } });
    }

    /** Add or update record in the table depending of the existence of the ID field */
    addOrUpdateRecordValue(
        recordData: PartialCollectionValue<NestedRecordType>,
    ): PartialCollectionValueWithIds<NestedRecordType> {
        const value = this.ensureFieldHasValue();
        return splitValueToMergedValue(
            value.addOrUpdateRecordValue({
                recordData,
            }),
        );
    }

    /** Remove a single record from the table and its dataset */
    removeRecord(recordId: NestedRecordId): void {
        const value = this.ensureFieldHasValue();
        value.removeRecord({ recordId });
    }

    /** Gets a record by a given field and value */
    getRecordByFieldValue(
        fieldName: keyof NestedRecordType,
        fieldValue: any,
    ): PartialCollectionValue<NestedRecordType> | null {
        const value = this.ensureFieldHasValue();
        return value.getRawRecordByFieldValue({ fieldName: String(fieldName), fieldValue });
    }

    /** Selected records identifiers */
    get selectedRecords(): NestedRecordId[] {
        this.ensureFieldHasValue();
        const propertyValue = this.getUiComponentProperty('selectedRecords') as string[] | undefined;
        return propertyValue || [];
    }

    /** Selected records identifiers */
    set selectedRecords(recordIds: NestedRecordId[]) {
        this.ensureFieldHasValue();
        this.setUiComponentProperties('selectedRecords', [...recordIds]);
    }

    /** Adds the record identifier to the set of selected items */
    selectRecord(recordId: NestedRecordId): void {
        this.ensureFieldHasValue();
        const recordIds = this.selectedRecords;
        if (recordIds.indexOf(String(recordId)) === -1) {
            this.setUiComponentProperties('selectedRecords', [...recordIds, String(recordId)]);
        }
    }

    /** Removes the record identifier from the set of selected items */
    unselectRecord(recordId: NestedRecordId): void {
        this.ensureFieldHasValue();
        const recordIds = this.selectedRecords;
        const recordIdIndex = recordIds.indexOf(String(recordId));
        if (recordIdIndex !== -1) {
            const updatedRecordIds = [...recordIds];
            updatedRecordIds.splice(recordIdIndex, 1);
            this.setUiComponentProperties('selectedRecords', updatedRecordIds);
        }
    }

    /** Unselects all items in the collection */
    unselectAllRecords(): void {
        this.ensureFieldHasValue();
        this.setUiComponentProperties('selectedRecords', []);
    }

    /** Returns an auto-generated ID that can be used for a new record */
    generateRecordId(): string {
        const value = this.ensureFieldHasValue();
        return value.generateIndex();
    }

    async refresh(keepModifications = false): Promise<void> {
        await this._refresh({ keepPageInfo: true, keepModifications }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }

    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result
     */
    async validate(forceValidate = false): Promise<string[]> {
        const validationResult = await this.validateWithDetails(forceValidate);
        return validationResult.map(v => v.message);
    }

    /**
     * Triggers the field validation rules. Since the validation rules might be asynchronous,
     * this method returns a promise that must be awaited to get the validation result. Compared to the `validate` method
     * it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
     */
    async validateWithDetails(forceValidate = false): Promise<ValidationResult[]> {
        const value = this._getValue();
        if (value) {
            return value.validate(forceValidate);
        }
        return [];
    }

    async refreshRecord(
        recordId: string,
        skipUpdate = false,
    ): Promise<PartialCollectionValueWithIds<NestedRecordType>> {
        const value = this.ensureFieldHasValue();
        return value.refreshRecord({ recordId, skipUpdate });
    }

    /**
     * Calculate aggregated values ('min' | 'max' | 'sum' | 'avg' | 'count').
     * Returns Number.NaN if not all records have been fetched from the server.
     */
    calculateAggregatedValue({
        aggregationKey,
        aggregationMethod,
        level,
        parentId,
    }: {
        aggregationMethod: AggregationMethod;
        aggregationKey: string;
        level?: number | null;
        parentId?: string | null;
    }): number {
        const value = this.ensureFieldHasValue();
        return value.calculateAggregatedValue({
            aggregationKey,
            aggregationMethod,
            level,
            parentId,
        });
    }
}
