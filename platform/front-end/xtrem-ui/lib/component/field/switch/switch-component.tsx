import Switch from 'carbon-react/esm/components/switch';
import * as React from 'react';
import { connect } from 'react-redux';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent, triggerNestedFieldEvent } from '../../../utils/events';
import { getCommonCarbonComponentProperties, isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-utils';
import type { SwitchComponentProps } from './switch-types';
import { localize } from '../../../service/i18n-service';

export const SwitchComponent: React.FC<SwitchComponentProps> = React.memo((props: SwitchComponentProps) => {
    const onChangeHandler = (): void => {
        if (props.isNested) {
            triggerNestedFieldEvent(
                props.screenId,
                props.parentElementId!,
                props.fieldProperties as any,
                'onChange',
                props.handlersArguments!.rowValue._id,
                props.handlersArguments!.rowValue,
            );
        } else {
            triggerFieldEvent(props.screenId, props.elementId, 'onChange');
        }
    };

    const onClick = (): void => {
        if (props.isNested) {
            triggerNestedFieldEvent(
                props.screenId,
                props.parentElementId!,
                props.fieldProperties as any,
                'onClick',
                props.handlersArguments!.rowValue._id,
                props.handlersArguments!.rowValue,
            );
        } else {
            triggerFieldEvent(props.screenId, props.elementId, 'onClick');
        }
    };

    const onChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
        const value = event.target.checked;
        if (props.value !== value) {
            handleChange(props.elementId, value, props.setFieldValue, props.validate, onChangeHandler);
        }
    };

    const switchRef = React.useRef(null);
    const isReadOnly = isFieldReadOnly(
        props.screenId,
        props.fieldProperties,
        props.value,
        props.handlersArguments?.rowValue,
    );
    const isDisabled = isFieldDisabled(
        props.screenId,
        props.fieldProperties,
        props.value,
        props.handlersArguments?.rowValue,
    );

    const commonProps = getCommonCarbonComponentProperties(props);
    // Carbon throws an error if a validation error is passed on to disabled fields
    if (commonProps.error && (isReadOnly || isDisabled)) {
        delete commonProps.error;
    }

    return (
        <CarbonWrapper
            {...props}
            className="e-switch-field"
            componentName="switch"
            componentRef={switchRef}
            handlersArguments={props.handlersArguments}
            noReadOnlySupport={true}
            value={!!props.value || false}
            readOnlyDisplayValue={
                props.value
                    ? localize('@sage/xtrem-ui/switch-on-caps', 'ON')
                    : localize('@sage/xtrem-ui/switch-off-caps', 'OFF')
            }
        >
            <Switch
                ref={switchRef}
                {...commonProps}
                checked={!!props.value || false}
                disabled={isDisabled || isReadOnly}
                labelHelp={props.fieldProperties.title ? props.fieldProperties.titleHelp : undefined}
                onChange={onChange}
                onClick={onClick}
                reverse={true}
                size={props.fieldProperties.size}
                value="checked"
            />
        </CarbonWrapper>
    );
});

SwitchComponent.displayName = 'SwitchComponent';

export const ConnectedSwitchComponent = connect(mapStateToProps(), mapDispatchToProps())(SwitchComponent);

export default ConnectedSwitchComponent;
