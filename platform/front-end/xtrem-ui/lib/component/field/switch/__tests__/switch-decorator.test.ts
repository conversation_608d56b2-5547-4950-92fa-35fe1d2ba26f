import type { Page } from '../../../..';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import { switchField } from '../switch-decorator';
import type { SwitchDecoratorProperties } from '../switch-types';

describe('Switch decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'switchField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should set default values when no component properties provided', () => {
        switchField({})({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: SwitchDecoratorProperties<ScreenBase> =
            pageMetadata.uiComponentProperties[fieldId];
        expect(mappedComponentProperties.onChange).toBeUndefined();
        expect(mappedComponentProperties.onClick).toBeUndefined();
        expect(mappedComponentProperties.size).toBe('small');
    });

    it('should inherit false abstract-field booleans when no provided', () => {
        switchField({})({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: SwitchDecoratorProperties<ScreenBase> =
            pageMetadata.uiComponentProperties[fieldId];
        expect(mappedComponentProperties.isHiddenMobile).toBe(false);
        expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
        expect(mappedComponentProperties.isFullWidth).toBe(false);
        expect(mappedComponentProperties.isHidden).toBe(false);
        expect(mappedComponentProperties.isTransient).toBe(false);
    });

    it('should set values when component properties provided', () => {
        const clickFunc: () => void = jest.fn().mockImplementation(() => {});
        const changeFunc: () => void = jest.fn().mockImplementation(() => {});

        switchField({
            onChange: changeFunc,
            onClick: clickFunc,
            helperText: 'helper text',
            titleHelp: 'title help',
            size: 'large',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: SwitchDecoratorProperties<ScreenBase> =
            pageMetadata.uiComponentProperties[fieldId];
        expect(mappedComponentProperties.onChange).not.toBeUndefined();
        expect(mappedComponentProperties.onChange).toBe(changeFunc);
        expect(mappedComponentProperties.onClick).not.toBeUndefined();
        expect(mappedComponentProperties.onClick).toBe(clickFunc);
        expect(mappedComponentProperties.helperText).toBe('helper text');
        expect(mappedComponentProperties.titleHelp).toBe('title help');
        expect(mappedComponentProperties.size).toBe('large');
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(switchField, pageMetadata, fieldId);
        });
    });
});
