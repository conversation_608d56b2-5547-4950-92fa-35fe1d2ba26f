import type { <PERSON><PERSON><PERSON> } from '../../../types';
import { SwitchControlObject } from '../../../control-objects';
import type { SwitchProperties } from '../switch-types';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';

describe('Switch Field', () => {
    let switchField: SwitchControlObject;
    let fieldProperties: SwitchProperties;

    beforeEach(() => {
        fieldProperties = {
            title: 'TEST_FIELD_TITLE',
            titleHelp: 'TEST_FIELD_TITLE_HELP',
        };
    });

    describe('getters and setters', () => {
        beforeEach(() => {
            switchField = buildControlObject<FieldKey.Switch>(SwitchControlObject, {
                fieldValue: true,
                fieldProperties,
            });
        });

        it('getting field value', () => {
            expect(switchField.value).toEqual(true);
        });

        it('getting isReadOnly', () => {
            expect(switchField.isReadOnly).toBeFalsy();
        });

        it('getting title help', () => {
            expect(switchField.titleHelp).toEqual('TEST_FIELD_TITLE_HELP');
        });

        it('should set the title', () => {
            const testFixture = 'Test Switch Field Title';
            expect(fieldProperties.title).not.toEqual(testFixture);
            switchField.title = testFixture;
            expect(fieldProperties.title).toEqual(testFixture);
        });

        it('should set the helper text', () => {
            const testFixture = 'Test Switch Field Helper Text';
            expect(fieldProperties.helperText).not.toEqual(testFixture);
            switchField.helperText = testFixture;
            expect(fieldProperties.helperText).toEqual(testFixture);
        });

        it('should set isDisabled property', () => {
            const testFixture = true;
            expect(fieldProperties.isDisabled).not.toEqual(testFixture);
            switchField.isDisabled = testFixture;
            expect(fieldProperties.isDisabled).toEqual(testFixture);
        });

        it('should set isHelperTextHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHelperTextHidden).not.toEqual(testFixture);
            switchField.isHelperTextHidden = testFixture;
            expect(fieldProperties.isHelperTextHidden).toEqual(testFixture);
        });

        it('should set isHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHidden).not.toEqual(testFixture);
            switchField.isHidden = testFixture;
            expect(fieldProperties.isHidden).toEqual(testFixture);
        });

        it('should set isReadOnly property', () => {
            const testFixture = true;
            expect(fieldProperties.isReadOnly).not.toEqual(testFixture);
            switchField.isReadOnly = testFixture;
            expect(fieldProperties.isReadOnly).toEqual(testFixture);
        });

        it('should set isTitleHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isTitleHidden).not.toEqual(testFixture);
            switchField.isTitleHidden = testFixture;
            expect(fieldProperties.isTitleHidden).toEqual(testFixture);
        });

        it('should set the title help', () => {
            const testFixture = 'Test Switch Field Title Help';
            expect(fieldProperties.titleHelp).not.toEqual(testFixture);
            switchField.titleHelp = testFixture;
            expect(fieldProperties.titleHelp).toEqual(testFixture);
        });
    });

    describe('focus', () => {
        const focus = jest.fn();
        beforeEach(() => {
            switchField = buildControlObject<FieldKey.Switch>(SwitchControlObject, {
                fieldValue: true,
                fieldProperties,
                focus,
            });
        });

        it('should execute focus', () => {
            jest.useFakeTimers();
            expect(focus).not.toHaveBeenCalled();
            switchField.focus();
            jest.runAllTimers();
            expect(focus).toHaveBeenCalled();
        });
    });
});
