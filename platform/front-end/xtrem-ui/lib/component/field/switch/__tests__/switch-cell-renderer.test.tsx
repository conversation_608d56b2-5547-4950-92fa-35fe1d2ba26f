import * as React from 'react';
import { render, waitFor } from '@testing-library/react';
import { SwitchRenderer } from '../switch-cell-renderer';
import type { NestedSwitchProperties } from '../switch-types';
import { CellWrapper } from '../../../ui/table-shared/cell/cell-wrapper';
import type { CellParams } from '../../../../utils/ag-grid/ag-grid-column-config';
import { userEvent } from '../../../../__tests__/test-helpers';
import * as nestedFields from '../../../nested-fields';
import { CollectionValue } from '../../../../service/collection-data-service';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';
import '@testing-library/jest-dom';

jest.useFakeTimers();

const SCREEN_ID = 'TestScreen';
const FIELD_ID = 'TestField';
const COLUMN_ID = 'TestColumn';
const ROW_INDEX = 5;

const getTestProps = (
    options?: Partial<CellParams<NestedSwitchProperties, boolean> & { onChange: (value: boolean) => void }>,
): CellParams<NestedSwitchProperties, boolean> & { onChange: (value: boolean) => void } => {
    const value = options?.value ?? false;
    const fieldProperties = {
        bind: COLUMN_ID,
        ...(options?.fieldProperties && options?.fieldProperties),
        wrapper: CellWrapper,
    };

    const colDef = { field: COLUMN_ID, context: { isEditable: jest.fn().mockReturnValue(true) } } as any;
    const columnApi = { getAllColumns: jest.fn(() => [{ field: COLUMN_ID }]) } as any;

    const eGridCell = document.createElement('div');
    const props: CellParams<NestedSwitchProperties, boolean> & { onChange: (value: boolean) => void } = {
        locale: 'en-US',
        isParentFieldDisabled: false,
        columnId: 'test-column-id',
        api: {
            getColumns: jest.fn(() => [
                {
                    field: 'test-column-id',
                },
            ]),
            getFocusedCell: () => eGridCell,
        } as any,
        eventKey: null,
        initialValue: value,
        colDef,
        column: {
            colDef,
            columnApi,
            getColId: () => 'test-column-id',
        } as any,
        context: {} as any,
        data: {},
        eGridCell,
        eParentOfValue: {} as any,
        elementId: FIELD_ID,
        fieldProperties,
        node: { setDataValue: jest.fn(), rowIndex: ROW_INDEX } as any,
        onChange: jest.fn(),
        onValueChange: jest.fn(),
        setTooltip: jest.fn(),
        screenId: SCREEN_ID,
        tableElementId: FIELD_ID,
        value,
        valueFormatted: value ? 'True' : 'False',
        formatValue: jest.fn(),
        getValue: jest.fn(),
        refreshCell: jest.fn(),
        setValue: jest.fn(),
        stopEditing: jest.fn(),
        registerRowDragger: () => {},
        isTree: false,
        collectionValue: () =>
            new CollectionValue({
                screenId: 'test-screen',
                elementId: 'testField',
                isTransient: false,
                hasNextPage: false,
                orderBy: [{}],
                columnDefinitions: [
                    [nestedFields.text<any, any>({ bind: '_id' }), nestedFields.text<any, any>({ bind: 'anyField' })],
                ],
                nodeTypes: {},
                nodes: ['@sage/xtrem-test/AnyNode'],
                filter: [undefined],
                initialValues: [],
                fieldType: CollectionFieldTypes.DESKTOP_TABLE,
            }),
    };

    return props;
};

describe('Switch Cell Renderer', () => {
    it('should render with default props', () => {
        const component = render(<SwitchRenderer {...getTestProps()} />);
        expect(component.getByTestId(`${FIELD_ID}-${ROW_INDEX}-0`)).toBeInTheDocument();
    });

    it('should not be checked by default', () => {
        const component = render(<SwitchRenderer {...getTestProps()} />);
        expect(component.getByRole('switch', { checked: false })).toBeInTheDocument();
    });

    it('should not be checked given "false" value', () => {
        const component = render(<SwitchRenderer {...getTestProps({ value: false })} />);
        expect(component.getByRole('switch', { checked: false })).toBeInTheDocument();
    });

    it('should be checked given "true" value', () => {
        const component = render(<SwitchRenderer {...getTestProps({ value: true })} />);
        expect(component.getByRole('switch', { checked: true })).toBeInTheDocument();
    });

    it('should call onChange property', async () => {
        const props = getTestProps();
        const component = render(<SwitchRenderer {...props} />);

        expect(component.getByRole('switch', { checked: false })).toBeInTheDocument();

        await userEvent.click(component.getByRole('switch'));
        await waitFor(() => {
            expect(props.node.setDataValue).toHaveBeenCalledWith('test-column-id', true);
        });
    });
});
