import { renderWithRedux, applyActionMocks } from '../../../../__tests__/test-helpers';

import * as xtremRedux from '../../../../redux';
import { cleanup, fireEvent, render, waitFor } from '@testing-library/react';
import * as React from 'react';
import type { SwitchDecoratorProperties } from '../switch-types';
import type { ScreenBase } from '../../../../service/screen-base';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import * as dispatchService from '../../../../service/dispatch-service';
import * as events from '../../../../utils/events';
import ConnectedSwitchComponent, { SwitchComponent } from '../switch-component';

describe('switch component', () => {
    const screenId = 'TestPage';
    const fieldId = 'test-switch-field';
    let triggerNestedFieldEventMock: jest.MockInstance<any, any>;
    let triggerFieldEvent: jest.MockInstance<any, any>;

    const setup = (
        switchProps: SwitchDecoratorProperties<ScreenBase> = {},
        value: FieldInternalValue<FieldKey.Switch> = false,
    ) => {
        const initialState = {
            screenDefinitions: {
                [screenId]: {
                    metadata: {
                        uiComponentProperties: {
                            [fieldId]: switchProps,
                        },
                    },
                    values: {
                        ...(value && { [fieldId]: value }),
                    },
                    errors: {},
                },
            },
        };
        const utils = renderWithRedux<FieldKey.Switch, any>(
            <ConnectedSwitchComponent screenId={screenId} elementId={fieldId} />,
            {
                initialState,
                fieldType: FieldKey.Switch,
                fieldValue: value,
                fieldProperties: switchProps,
                elementId: fieldId,
                screenId,
                mockActions: true,
            },
        ) as any;

        const switchField = utils.getByTestId('e-field-bind-test-switch-field', { exact: false });
        const switchInput = switchField.getElementsByTagName('input');
        const clickInput = (value: boolean) => {
            fireEvent.click(switchInput[0], { target: { value } });
        };

        return {
            ...utils,
            switchField,
            switchInput: switchInput[0],
            clickInput,
        };
    };

    const setupNested = (
        switchProps: SwitchDecoratorProperties<ScreenBase> = {},
        value: FieldInternalValue<FieldKey.Switch> = false,
    ) => {
        const { queryByTestId } = render(
            <SwitchComponent
                screenId={screenId}
                elementId={fieldId}
                locale="en-US"
                parentElementId="fakeParentElementId"
                fieldProperties={switchProps}
                value={value}
                validate={jest.fn().mockResolvedValue(undefined)}
                onFocus={jest.fn()}
                removeNonNestedErrors={jest.fn()}
                setFieldValue={jest.fn().mockResolvedValue(undefined)}
                isNested={true}
                handlersArguments={{
                    rowValue: {
                        _id: '12',
                        someOtherField: true,
                    },
                }}
            />,
        );

        const switchInput = queryByTestId('e-field-bind-test-switch-field', { exact: false })!.getElementsByTagName(
            'input',
        );

        const clickInput = (value: boolean) => {
            fireEvent.click(switchInput[0], { target: { value } });
        };

        return { queryByTestId, clickInput, switchInput };
    };

    beforeEach(() => {
        triggerNestedFieldEventMock = jest.spyOn(events, 'triggerNestedFieldEvent').mockImplementation();
        triggerFieldEvent = jest.spyOn(events, 'triggerFieldEvent').mockImplementation();
        jest.spyOn(dispatchService, 'runAndDispatchFieldValidation').mockResolvedValue(undefined);
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
        cleanup();
    });

    it('can render with redux with defaults', () => {
        const { switchField, switchInput } = setup({ title: 'Test Field Title' });
        expect(switchField).toHaveTextContent('Test Field Title');
        expect(switchInput).toBeInTheDocument();
    });

    it('can render with redux with value', () => {
        const { switchField, switchInput } = setup({ title: 'Test Field Title' }, true);
        expect(switchField).toHaveTextContent('ON');
        expect(switchInput).toBeChecked();
    });

    it('can render hidden', () => {
        const { switchField } = setup({ title: 'Test Field Title', isHidden: true });
        expect((switchField as HTMLDivElement).classList).toContain('e-hidden');
    });

    it('can render disabled', () => {
        const { switchInput } = setup({ title: 'Test Field Title', isDisabled: true });
        expect(switchInput).toHaveAttribute('disabled');
    });

    it('should render read-only', () => {
        const { switchInput } = setup({ title: 'Test Field Title', isReadOnly: true });
        expect(switchInput).toHaveAttribute('disabled');
    });

    it('should render helperText', () => {
        const { switchField } = setup({ title: 'Test Field Title', helperText: 'This is a helper text' });
        expect(switchField).toHaveTextContent('This is a helper text');
    });

    it('should render title help only when title is defined', () => {
        const { switchField } = setup({ titleHelp: 'This is a title help' });
        expect(switchField.querySelector('div[role="tooltip"]')).toBeNull();
    });

    it('can switch value on click', async () => {
        const { clickInput, store } = setup();
        store.clearActions();
        jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue(
            () => Promise.resolve({ type: 'SetFieldValue' }) as any,
        );
        expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
        clickInput(true);
        expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(screenId, fieldId, true, true);
    });

    describe('info and warning', () => {
        it('should render with an info message', async () => {
            const { baseElement } = setup({ title: 'Test Field Title', infoMessage: 'Info message!!' });
            fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]'));
            await waitFor(() => {
                expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
            });
        });

        it('should render with an info message from a callback', async () => {
            const { baseElement } = setup({ title: 'Test Field Title', infoMessage: () => 'Info message!!' });
            fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]'));
            await waitFor(() => {
                expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
            });
        });

        it('should render with an warning message', async () => {
            const { baseElement } = setup({ title: 'Test Field Title', warningMessage: 'Warning message!!' });

            fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]'));
            await waitFor(() => {
                expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Warning message!!');
            });
        });

        it('should render with an warning message from a callback', async () => {
            const { baseElement } = setup({ title: 'Test Field Title', warningMessage: () => 'Warning message!!' });
            fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]'));
            await waitFor(() => {
                expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Warning message!!');
            });
        });

        it('should prioritize warnings over info messages', () => {
            const { baseElement } = setup({
                title: 'Test Field Title',
                warningMessage: 'Info message!!',
                infoMessage: 'Info message',
            });
            expect(baseElement.querySelector('[data-element="info"]')).toBeNull();
            expect(baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
        });
    });

    describe('event handlers', () => {
        beforeEach(() => {
            jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue(
                () => Promise.resolve({ type: 'SetFieldValue' }) as any,
            );
        });

        it('should trigger onClick event', async () => {
            const { clickInput, store } = setup();
            store.clearActions();
            expect(triggerNestedFieldEventMock).not.toHaveBeenCalled();
            expect(triggerFieldEvent).not.toHaveBeenCalled();
            clickInput(true);
            expect(triggerNestedFieldEventMock).not.toHaveBeenCalled();
            await waitFor(() => {
                expect(triggerFieldEvent).toHaveBeenCalledTimes(2);
            });
            expect(triggerNestedFieldEventMock).not.toHaveBeenCalled();
            expect(triggerFieldEvent).toHaveBeenCalledWith(screenId, fieldId, 'onClick');
        });

        it('should trigger onChange event', async () => {
            const { clickInput, store } = setup();
            store.clearActions();
            expect(triggerNestedFieldEventMock).not.toHaveBeenCalled();
            expect(triggerFieldEvent).not.toHaveBeenCalled();
            clickInput(true);
            expect(triggerNestedFieldEventMock).not.toHaveBeenCalled();
            await waitFor(() => {
                expect(triggerFieldEvent).toHaveBeenCalledTimes(2);
            });
            expect(triggerNestedFieldEventMock).not.toHaveBeenCalled();
            expect(triggerFieldEvent).toHaveBeenCalledWith(screenId, fieldId, 'onChange');
        });

        it('should trigger onClick event for nested switch', async () => {
            const { clickInput } = setupNested();
            expect(triggerNestedFieldEventMock).not.toHaveBeenCalled();
            expect(triggerFieldEvent).not.toHaveBeenCalled();
            clickInput(true);
            await waitFor(() => {
                expect(triggerNestedFieldEventMock).toHaveBeenCalledTimes(2);
            });
            expect(triggerFieldEvent).not.toHaveBeenCalled();
            expect(triggerNestedFieldEventMock).toHaveBeenCalledWith(
                screenId,
                'fakeParentElementId',
                {},
                'onClick',
                '12',
                { _id: '12', someOtherField: true },
            );
        });

        it('should trigger onChange event for nested switch', async () => {
            const { clickInput } = setupNested();
            expect(triggerNestedFieldEventMock).not.toHaveBeenCalled();
            expect(triggerFieldEvent).not.toHaveBeenCalled();
            clickInput(true);
            await waitFor(() => {
                expect(triggerNestedFieldEventMock).toHaveBeenCalledTimes(2);
            });
            expect(triggerFieldEvent).not.toHaveBeenCalled();
            expect(triggerNestedFieldEventMock).toHaveBeenCalledWith(
                screenId,
                'fakeParentElementId',
                {},
                'onChange',
                '12',
                { _id: '12', someOtherField: true },
            );
        });
    });
});
