/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import type { FieldComponentProps, FieldKey, FieldWidth } from '../../types';
import type { SwitchProperties } from './switch-types';
/**
 * [Field]{@link EditableFieldControlObject} that holds a boolean value
 */
export class SwitchControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.Switch,
    FieldComponentProps<FieldKey.Switch>
> {
    static readonly defaultUiProperties: Partial<FieldComponentProps<FieldKey.Switch>> = {
        ...EditableFieldControlObject.defaultUiProperties,
        size: 'small',
    };

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    @ControlObjectProperty<SwitchProperties<CT>, SwitchControlObject<CT>>()
    /**
     * Support for the title property
     * When set, and title is also set, beside it, an Info icon appears
     * When hovered, titleHelp appears as a tooltip.
     */
    titleHelp?: string;

    @ControlObjectProperty<SwitchProperties<CT>, SwitchControlObject<CT>>()
    /**
     * Size of the switch, it can be `small` or `large`. Default value is `small
     */
    size?: Exclude<FieldWidth, 'medium'>;
}
