import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { CellProps } from '../../../utils/ag-grid/ag-grid-column-config';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasParent,
    Nested,
    NestedChangeable,
    NestedClickable,
    NestedValidatable,
    Validatable,
} from '../traits';

export interface SwitchCellRendererProps extends CellProps<NestedSwitchProperties> {
    onChange: (value: boolean) => void;
}

export interface SwitchProperties<CT extends ScreenBase = ScreenBase, ContextNodeType = void>
    extends Omit<EditableFieldProperties<CT, ContextNodeType>, 'isMandatory'> {
    size?: 'small' | 'large';
    titleHelp?: string;
}

export interface SwitchDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<SwitchProperties<CT, void>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        Validatable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>> {}

export interface NestedSwitchProperties<CT extends ScreenBase = ScreenBase, ContextNodeType extends ClientNode = any>
    extends NestedPropertiesWrapper<SwitchProperties<CT, ContextNodeType>>,
        Nested<ContextNodeType>,
        NestedChangeable<CT>,
        NestedValidatable<CT, boolean, ContextNodeType>,
        NestedClickable<CT, ContextNodeType> {}

export type SwitchComponentProps = BaseEditableComponentProperties<
    SwitchDecoratorProperties<any>,
    boolean,
    NestedFieldsAdditionalProperties
>;
