/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { SwitchControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { SwitchDecoratorProperties } from './switch-types';

export { NestedSwitchProperties } from './switch-types';

class SwitchDecorator extends AbstractFieldDecorator<FieldKey.Switch> {
    protected _controlObjectConstructor = SwitchControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

/**
 * Initializes the decorated member as a [Switch]{@link SwitchControlObject} field with the provided properties
 *
 * @param properties The properties that the [Switch]{@link SwitchControlObject} field will be initialized with
 */
export function switchField<T extends ScreenExtension<T>>(
    properties: SwitchDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Switch>(properties, SwitchDecorator, FieldKey.Switch);
}

export function switchFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<SwitchDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Switch>(properties);
}
