import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { SwitchComponentProps } from './switch-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedSwitchComponent = React.lazy(() => import('./switch-component'));

export function AsyncConnectedSwitchComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);

    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedSwitchComponent {...props} />
        </React.Suspense>
    );
}

const SwitchComponent = React.lazy(() => import('./switch-component').then(c => ({ default: c.SwitchComponent })));

export function AsyncSwitchComponent(props: SwitchComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <SwitchComponent {...props} />
        </React.Suspense>
    );
}
