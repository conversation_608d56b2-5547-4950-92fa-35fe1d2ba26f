PATH: XTREEM/UI+Field+Widgets/Switch+Field

## Introduction

A switch field represents a boolean value. An ON state means true and OFF false. The switch field is also available as a nested field in tables and pods.

## Example:

```ts
@ui.decorators.switchField<SwitchFieldPage>({
    parent() {
        return this.anyBlock;
    },
    bind: 'aCollectionProperty',
    helperText: 'This text goes underneath the field',
    isDisabled: false,
    isHidden: false,
    isReadOnly: false,
    title: 'A Switch field',
    titleHelp: 'This is a text that goes beside the title, displayed when hovering the info icon',
    onClick() {
        console.log('Doing something when the field is clicked');
    },
    onChange() {
        console.log('Doing something when the field value is changed');
    },
})
aSimpleSwitchField: ui.fields.Switch;
```

### Display decorator properties

-   **isDisabled**: Whether the switch is able to trigger onClick decorator property (isDisabled = false) or not (isDisabled = true). It can also be defined as callback function that returns a boolean. A switch is enabled by default.
-   **isHidden**: Whether the switch is visible (isHidden = false) or not (isHidden = true). A switch is visible by default.
-   **isFullWidth**: Whether a field should take the full width of the screen.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **isReadOnly**: Whether the field is editable (isReadOnly = false) or not (isReadOnly = true). The difference with disabled is that isReadOnly suggests that the field is never editable. It can be defined as a boolean, or conditionally by a callback that returns a boolean.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **helperText**: The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **size**: Vertical size of the field, it can be `small`, or `large`. It is set to small by default.
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **titleHelp**: Support for the title property. When set, and title is also set, beside it, an Info icon appears. By hovering that icon, titleHelp appears as a tooltip.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.

### Binding decorator properties

-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

### Event handler decorator properties

-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.
-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

### Other decorator properties

-   **fetchesDefaults**: When set to true and when the switch value changes, a request to the server for default values for the whole page will be requested. False by default.

## Runtime functions

-   **focus()**: Moves the focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Switch).
