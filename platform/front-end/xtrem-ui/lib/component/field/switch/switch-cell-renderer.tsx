import Switch from 'carbon-react/esm/components/switch';
import React from 'react';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import type { NestedSwitchProperties } from './switch-types';
import { SPACEBAR } from '../../../utils/keyboard-event-utils';
import { usePrevious } from '@sage/xtrem-ui-components';

export const SwitchRenderer: React.FC<
    CellParams<NestedSwitchProperties, boolean> & { onChange: (value: boolean) => void }
> = React.memo(
    ({
        api,
        colDef,
        column,
        data,
        eventKey,
        fieldProperties,
        eGridCell,
        initialValue,
        isEditing,
        isParentFieldDisabled,
        isTableReadOnly,
        node,
        onValueChange,
        node: { rowIndex },
        screenId,
        stopEditing,
        tableElementId,
        value,
        ...rest
    }) => {
        const switchRef = React.useRef<React.ElementRef<typeof Switch> | null>(null);
        const [checked, setChecked] = React.useState<boolean>((isEditing ? initialValue : value) || false);
        const previousChecked = usePrevious(checked);

        React.useEffect(() => {
            const focusedCell = api.getFocusedCell();
            if (focusedCell?.column?.getColId?.() === column.getColId() && focusedCell?.rowIndex === rowIndex) {
                switchRef.current?.focus();
            }
        }, [api, column, rowIndex, isEditing]);

        const onFocus = React.useCallback(() => {
            switchRef.current?.focus();
        }, []);

        React.useEffect(() => {
            eGridCell.addEventListener('focus', onFocus);
            return (): void => {
                eGridCell.removeEventListener('focus', onFocus);
            };
        }, [eGridCell, onFocus]);

        // react to external value changes
        React.useEffect(() => {
            setChecked(value);
        }, [value]);

        // update grid value upon state changes
        React.useEffect(() => {
            if (previousChecked === undefined || checked === previousChecked) {
                return;
            }
            node.setDataValue(column.getColId(), checked);
            if (isEditing) {
                onValueChange(checked);
            }
            node.setDataValue(column.getColId(), checked);
            // onValueChange is not memoized by ag-grid
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [checked, column, node, isEditing, previousChecked]);

        const onChange = React.useCallback<(newValue: boolean) => void>(newValue => {
            setChecked(newValue);
        }, []);

        React.useEffect(() => {
            if (isEditing) {
                switchRef.current?.focus();
            }
        }, [isEditing]);

        const isDisabled = React.useMemo(
            () =>
                isParentFieldDisabled ||
                resolveByValue({
                    screenId,
                    propertyValue: fieldProperties.isDisabled,
                    rowValue: splitValueToMergedValue(data),
                    fieldValue: value,
                    skipHexFormat: true,
                }),
            [data, fieldProperties.isDisabled, isParentFieldDisabled, screenId, value],
        );

        const isReadOnly = React.useMemo(
            () =>
                resolveByValue({
                    screenId,
                    propertyValue: fieldProperties.isReadOnly,
                    rowValue: splitValueToMergedValue(data),
                    fieldValue: value,
                    skipHexFormat: true,
                }),
            [data, fieldProperties.isReadOnly, screenId, value],
        );

        const isColumnDisabled = React.useMemo(() => !colDef.context.isEditable(data), [colDef, data]);

        const labelHelp = React.useMemo<string | undefined>(
            () => (fieldProperties.title ? fieldProperties.titleHelp : undefined),
            [fieldProperties.title, fieldProperties.titleHelp],
        );

        const disabled = React.useMemo(
            () => Boolean(isDisabled || isReadOnly || isTableReadOnly || isColumnDisabled),
            [isColumnDisabled, isDisabled, isReadOnly, isTableReadOnly],
        );

        const onSwitchChange = React.useCallback<NonNullable<React.ComponentProps<typeof Switch>['onChange']>>(
            e => onChange(e.target.checked),
            [onChange],
        );

        const onKeyDownCapture = React.useCallback<
            NonNullable<React.ComponentProps<typeof Switch>['onKeyDownCapture']>
        >(
            e => {
                if (e.code === SPACEBAR) {
                    e.stopPropagation();
                    onChange(!(e.target as HTMLInputElement).checked);
                }
            },
            [onChange],
        );

        const onClickCapture = React.useCallback<NonNullable<React.ComponentProps<typeof Switch>['onClickCapture']>>(
            e => {
                if (!isEditing) {
                    // prevent ag-grid from entering edit mode when checkbox is clicked
                    e.stopPropagation();
                }
                onChange((e?.target as HTMLInputElement).checked);
            },
            [isEditing, onChange],
        );

        const dataTestId = React.useMemo(
            () => `${tableElementId}-${rowIndex}-${(api.getColumns() ?? []).indexOf(column) + 1}`,
            [api, column, rowIndex, tableElementId],
        );

        const renderSwitch = React.useCallback(() => {
            return (
                <div className="e-switch-cell-renderer" data-testid={dataTestId}>
                    <Switch
                        ref={switchRef}
                        aria-label={colDef.headerName}
                        checked={checked}
                        disabled={disabled}
                        labelHelp={labelHelp}
                        onChange={onSwitchChange}
                        reverse={true}
                        size="small"
                        onKeyDownCapture={onKeyDownCapture}
                        onClickCapture={onClickCapture}
                    />
                </div>
            );
        }, [
            checked,
            colDef.headerName,
            dataTestId,
            disabled,
            labelHelp,
            onClickCapture,
            onKeyDownCapture,
            onSwitchChange,
        ]);

        if (isEditing) {
            // no wrapper in edit mode
            return renderSwitch();
        }

        return (
            <fieldProperties.wrapper
                api={api}
                colDef={colDef}
                column={column}
                data={data}
                eGridCell={eGridCell}
                eventKey={eventKey}
                fieldProperties={fieldProperties}
                initialValue={initialValue}
                isEditing={isEditing}
                isParentFieldDisabled={isParentFieldDisabled}
                isTableReadOnly={isTableReadOnly}
                node={node}
                onValueChange={onValueChange}
                screenId={screenId}
                stopEditing={stopEditing}
                tableElementId={tableElementId}
                value={value}
                {...rest}
                textAlign="center"
            >
                {renderSwitch()}
            </fieldProperties.wrapper>
        );
    },
);

SwitchRenderer.displayName = 'SwitchRenderer';
