/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a boolean value
 */
export class CheckboxControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.Checkbox,
    FieldComponentProps<FieldKey.Checkbox>
> {
    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }
}
