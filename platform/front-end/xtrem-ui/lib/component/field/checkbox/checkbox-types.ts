import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseErrorableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasParent,
    Nested,
    NestedChangeable,
    NestedClickable,
    Sizable,
    Validatable,
} from '../traits';

export interface CheckboxProperties<CT extends ScreenBase = ScreenBase, ContextNodeType = void>
    extends EditableFieldProperties<CT, ContextNodeType> {
    isValueReversed?: boolean;
}

export interface CheckboxDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<CheckboxProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        Changeable<CT>,
        Sizable,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Validatable<CT>,
        HasParent<CT, BlockControlObject<CT>> {
    isReversed?: boolean;

    // Removes the padding that aligns the checkbox with other fields in a page.
    noPadding?: boolean;
}

export interface NestedCheckboxProperties<CT extends ScreenBase = ScreenBase, ContextNodeType extends ClientNode = any>
    extends NestedPropertiesWrapper<CheckboxProperties<CT, ContextNodeType>>,
        Nested<ContextNodeType>,
        NestedChangeable<CT>,
        NestedClickable<CT, ContextNodeType>,
        Sizable {
    isReversed?: boolean;
}

export type CheckboxComponentProps = BaseErrorableComponentProperties<
    CheckboxProperties,
    boolean,
    NestedFieldsAdditionalProperties
>;
