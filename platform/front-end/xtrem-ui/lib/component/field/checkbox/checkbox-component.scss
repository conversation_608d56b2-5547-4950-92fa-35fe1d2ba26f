.e-checkbox-field {

    /* Add padding so that checkbox is center-aligned with other input fields */
    &::before {
        content: "";
        padding-top: 38px;
    }
    
    &.e-no-padding::before {
        padding-top: 0;
    }

    *:focus {
        outline: 3px solid #ffb500;
        outline-offset: 0;
    }

    [data-element="label"] {
        white-space: normal;
    }

    [data-element="help"] {
        padding-left: 0;
    }

    /* Add margin so that helper text is aligned with other input fields */
    &>div>div>div>div {
        margin-bottom: 18px;
    }

    &.e-invalid {
        svg {
            color: var(--colorsSemanticNegative500);

            .checkbox-outline {
                fill: var(--colorsSemanticNegative500);
            }
        }
    }

    .e-checkbox-field-read-only {
        padding-top: 0;
        display: flex;
    }

    .common-input__label {
        display: inline-block;
        margin: 0;
        line-height: 21px;
        padding-left: 8px;
    }
}

// We don't need to preserve this space in nested read only contexts like pods
.e-field-nested .e-checkbox-field.e-read-only::before {
    padding-top: 0;
}

.e-nested-cell-field-checkbox div:has(>input[type="checkbox"]) {
    padding: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
}
