import { Checkbox } from 'carbon-react/esm/components/checkbox';
import * as React from 'react';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { NestedCheckboxProperties } from '../../nested-fields-properties';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import { ENTER, SPACEBAR } from '../../../utils/keyboard-event-utils';
import { noop } from 'lodash';

export const CheckboxCellRenderer = React.memo<CellParams<NestedCheckboxProperties, boolean>>(
    ({
        colDef,
        column,
        data,
        eGridCell,
        eventKey,
        fieldProperties,
        initialValue,
        isEditing,
        isParentFieldDisabled,
        isTableReadOnly,
        node,
        onValueChange,
        screenId,
        stopEditing,
        value,
        ...rest
    }) => {
        const checkboxRef = React.useRef<React.ElementRef<typeof Checkbox> | null>(null);

        const onFocus = React.useCallback(() => {
            checkboxRef.current?.focus();
        }, []);

        React.useEffect(() => {
            if (isEditing) {
                return noop;
            }
            (eGridCell as HTMLDivElement).addEventListener('focus', onFocus);
            return (): void => {
                (eGridCell as HTMLDivElement).removeEventListener('focus', onFocus);
            };
        }, [eGridCell, isEditing, onFocus]);

        const onChange = React.useCallback<(newValue: boolean) => void>(
            newValue => {
                if (onValueChange) {
                    onValueChange(newValue);
                } else {
                    node.setDataValue(column.getColId(), newValue);
                }
            },
            // onValueChange is not memoized by ag-grid
            // eslint-disable-next-line react-hooks/exhaustive-deps
            [column, node],
        );

        React.useEffect(() => {
            if (isEditing) {
                checkboxRef.current?.focus();
            }
        }, [isEditing]);

        const isDisabled = React.useMemo(
            () =>
                isParentFieldDisabled ||
                resolveByValue({
                    screenId,
                    propertyValue: fieldProperties.isDisabled,
                    rowValue: splitValueToMergedValue(data),
                    fieldValue: value,
                    skipHexFormat: true,
                }),
            [data, fieldProperties.isDisabled, isParentFieldDisabled, screenId, value],
        );

        const isFieldReadOnly = React.useMemo(
            () =>
                resolveByValue({
                    screenId,
                    propertyValue: fieldProperties.isReadOnly,
                    rowValue: splitValueToMergedValue(data),
                    fieldValue: value,
                    skipHexFormat: true,
                }),
            [data, fieldProperties.isReadOnly, screenId, value],
        );

        const isColumnDisabled = React.useMemo(() => !colDef.context.isEditable(data), [colDef, data]);

        const onCheckboxChange = React.useCallback<NonNullable<React.ComponentProps<typeof Checkbox>['onChange']>>(
            e => onChange(e.target.checked),
            [onChange],
        );

        const onKeyDownCapture = React.useCallback<
            NonNullable<React.ComponentProps<typeof Checkbox>['onKeyDownCapture']>
        >(
            e => {
                if (e.code === SPACEBAR || e.code === ENTER) {
                    e.stopPropagation();
                    onChange(!(e.target as HTMLInputElement).checked);
                }
            },
            [onChange],
        );

        const onClickCapture = React.useCallback<NonNullable<React.ComponentProps<typeof Checkbox>['onClickCapture']>>(
            e => {
                if (!isEditing) {
                    // prevent ag-grid from entering edit mode when checkbox is clicked
                    e.stopPropagation();
                }
                onChange((e?.target as HTMLInputElement).checked);
            },
            [isEditing, onChange],
        );

        const renderCheckbox = React.useCallback(() => {
            return (
                <div
                    style={{
                        margin: '3px',
                    }}
                >
                    <div
                        style={{
                            display: 'flex',
                            flex: 1,
                            justifyContent: 'center',
                        }}
                    >
                        <Checkbox
                            ref={checkboxRef}
                            aria-label={colDef.headerName}
                            value="checked"
                            checked={value}
                            disabled={Boolean(isDisabled || isFieldReadOnly || isTableReadOnly || isColumnDisabled)}
                            onChange={onCheckboxChange}
                            onKeyDownCapture={onKeyDownCapture}
                            onClickCapture={onClickCapture}
                        />
                    </div>
                </div>
            );
        }, [
            colDef.headerName,
            value,
            isDisabled,
            isFieldReadOnly,
            isTableReadOnly,
            isColumnDisabled,
            onCheckboxChange,
            onKeyDownCapture,
            onClickCapture,
        ]);

        if (isEditing) {
            // no wrapper in edit mode
            return renderCheckbox();
        }

        return (
            <fieldProperties.wrapper
                colDef={colDef}
                column={column}
                data={data}
                eGridCell={eGridCell}
                eventKey={eventKey}
                fieldProperties={fieldProperties}
                initialValue={initialValue}
                isEditing={isEditing}
                isParentFieldDisabled={isParentFieldDisabled}
                isTableReadOnly={isTableReadOnly}
                node={node}
                onValueChange={onValueChange}
                screenId={screenId}
                stopEditing={stopEditing}
                value={value}
                {...rest}
                textAlign="center"
            >
                {renderCheckbox()}
            </fieldProperties.wrapper>
        );
    },
);

CheckboxCellRenderer.displayName = 'CheckboxCellRenderer';
