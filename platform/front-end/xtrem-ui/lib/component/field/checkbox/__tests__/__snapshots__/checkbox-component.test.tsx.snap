// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Checkbox component connected Snapshots should render disabled 1`] = `
.c3 {
  margin-bottom: var(--fieldSpacing);
}

.c2 + .c2 {
  margin-top: 16px;
}

.c3.c3.c3 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c13 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
  color: var(--colorsUtilityYin030);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  box-sizing: border-box;
  margin-bottom: 0;
  padding-left: var(--spacing100);
  width: 30%;
}

.c9 {
  cursor: pointer;
  opacity: 0;
  margin: 0;
  position: relative;
  z-index: 2;
}

.c7 {
  display: inline-block;
  position: relative;
}

.c1 {
  width: 100% !important;
}

.c1 .c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c1 .c10 {
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  padding-top: 0;
  width: auto;
}

.c1 .c8:hover,
.c1 .c12:hover,
.c1 .c8:focus,
.c1 .c12:focus {
  outline: none;
  cursor: not-allowed;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c6 {
  padding-top: 1px;
}

.c0 svg {
  background-color: var(--colorsUtilityYang100);
}

.c0 .c8,
.c0 svg {
  height: 16px;
  position: absolute;
  padding: 1px;
}

.c0 .c10 {
  width: auto;
  -webkit-flex: 0 1 auto;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
}

.c0 svg {
  background-color: var(--colorsUtilityDisabled400);
  border: 1px solid var(--colorsUtilityDisabled600);
}

.c0 svg path {
  fill: var(--colorsUtilityDisabled400);
}

<div>
  <div
    class="e-field e-checkbox-field e-disabled"
    data-label="Test Checkbox Title"
    data-testid="e-checkbox-field e-field-label-testCheckboxTitle e-field-bind-test-checkbox-field"
  >
    <div
      class="c0"
      data-component="checkbox"
      disabled=""
    >
      <div
        class="c1"
        disabled=""
      >
        <div
          class="c2 c3"
        >
          <div
            class="c4 c5"
            data-role="field-line"
          >
            <div
              class="c6 c7"
              data-role="checkable-input"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-label="Test Checkbox Title"
                disabled=""
                id="TestPage-test-checkbox-field"
                name="test-checkbox-field"
                role="checkbox"
                type="checkbox"
                value="checked"
              />
              <div
                class=""
              >
                <svg
                  data-role="checkable-svg"
                  focusable="false"
                  height="10"
                  viewBox="0 0 12 10"
                  width="12"
                >
                  <path
                    d="M.237 6.477A.752.752 0 0 1 .155 5.47l.851-1.092a.63.63 0 0 1 .934-.088l2.697 1.964, 4.674-6a.63.63 0 0 1 .933-.088l1.015.917c.28.254.317.703.081 1.005L6.353 8.492a.725.725, 0 0 1-.095.16l-.85 1.093a.637.637 0 0 1-.626.244.638.638 0 0 1-.335-.16L.237 6.476z"
                    fill="#FFFFFF"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
            <div
              class="c10 c11"
              data-role="label-container"
              id="label-container-TestPage-test-checkbox-field-label"
              width="30"
            >
              <label
                class="c12 c13"
                data-element="label"
                disabled=""
                for="TestPage-test-checkbox-field"
                id="TestPage-test-checkbox-field-label"
              >
                Test Checkbox Title
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Checkbox component connected Snapshots should render helperText 1`] = `
.c3 {
  margin-bottom: var(--fieldSpacing);
}

.c2 + .c2 {
  margin-top: 16px;
}

.c3.c3.c3 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c14 {
  display: block;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin-top: 8px;
  white-space: pre-wrap;
  margin-left: 30%;
  padding-left: 0;
}

.c12 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  box-sizing: border-box;
  margin-bottom: 0;
  padding-left: var(--spacing100);
  width: 30%;
}

.c9 {
  cursor: pointer;
  opacity: 0;
  margin: 0;
  position: relative;
  z-index: 2;
}

.c7 {
  display: inline-block;
  position: relative;
}

.c1 {
  width: 100% !important;
}

.c1 .c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c1 .c10 {
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  padding-top: 0;
  width: auto;
}

.c1 .c13 {
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c6 {
  padding-top: 1px;
}

.c0 svg {
  background-color: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
}

.c0 .c8,
.c0 svg {
  height: 16px;
  position: absolute;
  padding: 1px;
}

.c0 .c10 {
  width: auto;
  -webkit-flex: 0 1 auto;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
}

.c0 .c13 {
  margin-left: 16px;
  margin-top: 0;
  padding-left: var(--spacing100);
}

<div>
  <div
    class="e-field e-checkbox-field"
    data-label="Test Checkbox Title"
    data-testid="e-checkbox-field e-field-label-testCheckboxTitle e-field-bind-test-checkbox-field"
  >
    <div
      class="c0"
      data-component="checkbox"
    >
      <div
        class="c1"
      >
        <div
          class="c2 c3"
        >
          <div
            class="c4 c5"
            data-role="field-line"
          >
            <div
              class="c6 c7"
              data-role="checkable-input"
            >
              <input
                aria-describedby="TestPage-test-checkbox-field-field-help"
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-label="Test Checkbox Title"
                id="TestPage-test-checkbox-field"
                name="test-checkbox-field"
                role="checkbox"
                type="checkbox"
                value="checked"
              />
              <div
                class=""
              >
                <svg
                  data-role="checkable-svg"
                  focusable="false"
                  height="10"
                  viewBox="0 0 12 10"
                  width="12"
                >
                  <path
                    d="M.237 6.477A.752.752 0 0 1 .155 5.47l.851-1.092a.63.63 0 0 1 .934-.088l2.697 1.964, 4.674-6a.63.63 0 0 1 .933-.088l1.015.917c.28.254.317.703.081 1.005L6.353 8.492a.725.725, 0 0 1-.095.16l-.85 1.093a.637.637 0 0 1-.626.244.638.638 0 0 1-.335-.16L.237 6.476z"
                    fill="#FFFFFF"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
            <div
              class="c10 c11"
              data-role="label-container"
              id="label-container-TestPage-test-checkbox-field-label"
              width="30"
            >
              <label
                class="c12"
                data-element="label"
                for="TestPage-test-checkbox-field"
                id="TestPage-test-checkbox-field-label"
              >
                Test Checkbox Title
              </label>
            </div>
          </div>
          <span
            class="c13 c14"
            data-element="help"
            id="TestPage-test-checkbox-field-field-help"
          >
            This is a helper text
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Checkbox component connected Snapshots should render hidden 1`] = `
.c3 {
  margin-bottom: var(--fieldSpacing);
}

.c2 + .c2 {
  margin-top: 16px;
}

.c3.c3.c3 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c12 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  box-sizing: border-box;
  margin-bottom: 0;
  padding-left: var(--spacing100);
  width: 30%;
}

.c9 {
  cursor: pointer;
  opacity: 0;
  margin: 0;
  position: relative;
  z-index: 2;
}

.c7 {
  display: inline-block;
  position: relative;
}

.c1 {
  width: 100% !important;
}

.c1 .c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c1 .c10 {
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  padding-top: 0;
  width: auto;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c6 {
  padding-top: 1px;
}

.c0 svg {
  background-color: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
}

.c0 .c8,
.c0 svg {
  height: 16px;
  position: absolute;
  padding: 1px;
}

.c0 .c10 {
  width: auto;
  -webkit-flex: 0 1 auto;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
}

<div>
  <div
    class="e-field e-checkbox-field e-hidden"
    data-label="Test Checkbox Title"
    data-testid="e-checkbox-field e-field-label-testCheckboxTitle e-field-bind-test-checkbox-field"
  >
    <div
      class="c0"
      data-component="checkbox"
    >
      <div
        class="c1"
      >
        <div
          class="c2 c3"
        >
          <div
            class="c4 c5"
            data-role="field-line"
          >
            <div
              class="c6 c7"
              data-role="checkable-input"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-label="Test Checkbox Title"
                id="TestPage-test-checkbox-field"
                name="test-checkbox-field"
                role="checkbox"
                type="checkbox"
                value="checked"
              />
              <div
                class=""
              >
                <svg
                  data-role="checkable-svg"
                  focusable="false"
                  height="10"
                  viewBox="0 0 12 10"
                  width="12"
                >
                  <path
                    d="M.237 6.477A.752.752 0 0 1 .155 5.47l.851-1.092a.63.63 0 0 1 .934-.088l2.697 1.964, 4.674-6a.63.63 0 0 1 .933-.088l1.015.917c.28.254.317.703.081 1.005L6.353 8.492a.725.725, 0 0 1-.095.16l-.85 1.093a.637.637 0 0 1-.626.244.638.638 0 0 1-.335-.16L.237 6.476z"
                    fill="#FFFFFF"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
            <div
              class="c10 c11"
              data-role="label-container"
              id="label-container-TestPage-test-checkbox-field-label"
              width="30"
            >
              <label
                class="c12"
                data-element="label"
                for="TestPage-test-checkbox-field"
                id="TestPage-test-checkbox-field-label"
              >
                Test Checkbox Title
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Checkbox component connected Snapshots should render read-only 1`] = `
.c0 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c0::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

<div>
  <div
    class="e-field e-checkbox-field e-read-only"
    data-label="Test Checkbox Title"
    data-testid="e-checkbox-field e-field-label-testCheckboxTitle e-field-bind-test-checkbox-field"
  >
    <div
      class="e-checkbox-field-read-only common-input"
    >
      <div>
        <span
          class="c0"
          data-component="icon"
          data-element="cross"
          data-role="icon"
          font-size="small"
          type="cross"
        />
      </div>
      <div>
        <label
          class="common-input__label"
          data-element="label"
          data-testid="e-field-label"
        >
          Test Checkbox Title
        </label>
        <span
          class="common-input__help-text"
          data-element="help"
          data-testid="e-field-helper-text"
        >
           
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Checkbox component connected Snapshots should render with default properties 1`] = `
.c3 {
  margin-bottom: var(--fieldSpacing);
}

.c2 + .c2 {
  margin-top: 16px;
}

.c3.c3.c3 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c12 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  box-sizing: border-box;
  margin-bottom: 0;
  padding-left: var(--spacing100);
  width: 30%;
}

.c9 {
  cursor: pointer;
  opacity: 0;
  margin: 0;
  position: relative;
  z-index: 2;
}

.c7 {
  display: inline-block;
  position: relative;
}

.c1 {
  width: 100% !important;
}

.c1 .c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c1 .c10 {
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  padding-top: 0;
  width: auto;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c6 {
  padding-top: 1px;
}

.c0 svg {
  background-color: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
}

.c0 .c8,
.c0 svg {
  height: 16px;
  position: absolute;
  padding: 1px;
}

.c0 .c10 {
  width: auto;
  -webkit-flex: 0 1 auto;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
}

<div>
  <div
    class="e-field e-checkbox-field"
    data-label="Test Checkbox Title"
    data-testid="e-checkbox-field e-field-label-testCheckboxTitle e-field-bind-test-checkbox-field"
  >
    <div
      class="c0"
      data-component="checkbox"
    >
      <div
        class="c1"
      >
        <div
          class="c2 c3"
        >
          <div
            class="c4 c5"
            data-role="field-line"
          >
            <div
              class="c6 c7"
              data-role="checkable-input"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-label="Test Checkbox Title"
                id="TestPage-test-checkbox-field"
                name="test-checkbox-field"
                role="checkbox"
                type="checkbox"
                value="checked"
              />
              <div
                class=""
              >
                <svg
                  data-role="checkable-svg"
                  focusable="false"
                  height="10"
                  viewBox="0 0 12 10"
                  width="12"
                >
                  <path
                    d="M.237 6.477A.752.752 0 0 1 .155 5.47l.851-1.092a.63.63 0 0 1 .934-.088l2.697 1.964, 4.674-6a.63.63 0 0 1 .933-.088l1.015.917c.28.254.317.703.081 1.005L6.353 8.492a.725.725, 0 0 1-.095.16l-.85 1.093a.637.637 0 0 1-.626.244.638.638 0 0 1-.335-.16L.237 6.476z"
                    fill="#FFFFFF"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
            <div
              class="c10 c11"
              data-role="label-container"
              id="label-container-TestPage-test-checkbox-field-label"
              width="30"
            >
              <label
                class="c12"
                data-element="label"
                for="TestPage-test-checkbox-field"
                id="TestPage-test-checkbox-field-label"
              >
                Test Checkbox Title
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Checkbox component connected Snapshots should render without value 1`] = `
.c3 {
  margin-bottom: var(--fieldSpacing);
}

.c2 + .c2 {
  margin-top: 16px;
}

.c3.c3.c3 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c12 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  box-sizing: border-box;
  margin-bottom: 0;
  padding-left: var(--spacing100);
  width: 30%;
}

.c9 {
  cursor: pointer;
  opacity: 0;
  margin: 0;
  position: relative;
  z-index: 2;
}

.c7 {
  display: inline-block;
  position: relative;
}

.c1 {
  width: 100% !important;
}

.c1 .c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c1 .c10 {
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  padding-top: 0;
  width: auto;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c6 {
  padding-top: 1px;
}

.c0 svg {
  background-color: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
}

.c0 .c8,
.c0 svg {
  height: 16px;
  position: absolute;
  padding: 1px;
}

.c0 .c10 {
  width: auto;
  -webkit-flex: 0 1 auto;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
}

<div>
  <div
    class="e-field e-checkbox-field"
    data-label="Test Checkbox Title"
    data-testid="e-checkbox-field e-field-label-testCheckboxTitle e-field-bind-test-empty-checkbox-field"
  >
    <div
      class="c0"
      data-component="checkbox"
    >
      <div
        class="c1"
      >
        <div
          class="c2 c3"
        >
          <div
            class="c4 c5"
            data-role="field-line"
          >
            <div
              class="c6 c7"
              data-role="checkable-input"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-label="Test Checkbox Title"
                id="TestPage-test-empty-checkbox-field"
                name="test-empty-checkbox-field"
                role="checkbox"
                type="checkbox"
                value="checked"
              />
              <div
                class=""
              >
                <svg
                  data-role="checkable-svg"
                  focusable="false"
                  height="10"
                  viewBox="0 0 12 10"
                  width="12"
                >
                  <path
                    d="M.237 6.477A.752.752 0 0 1 .155 5.47l.851-1.092a.63.63 0 0 1 .934-.088l2.697 1.964, 4.674-6a.63.63 0 0 1 .933-.088l1.015.917c.28.254.317.703.081 1.005L6.353 8.492a.725.725, 0 0 1-.095.16l-.85 1.093a.637.637 0 0 1-.626.244.638.638 0 0 1-.335-.16L.237 6.476z"
                    fill="#FFFFFF"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
            <div
              class="c10 c11"
              data-role="label-container"
              id="label-container-TestPage-test-empty-checkbox-field-label"
              width="30"
            >
              <label
                class="c12"
                data-element="label"
                for="TestPage-test-empty-checkbox-field"
                id="TestPage-test-empty-checkbox-field-label"
              >
                Test Checkbox Title
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Checkbox component unconnected Snapshots should render with false state 1`] = `
.c3 {
  margin-bottom: var(--fieldSpacing);
}

.c2 + .c2 {
  margin-top: 16px;
}

.c3.c3.c3 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c12 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  box-sizing: border-box;
  margin-bottom: 0;
  padding-left: var(--spacing100);
  width: 30%;
}

.c9 {
  cursor: pointer;
  opacity: 0;
  margin: 0;
  position: relative;
  z-index: 2;
}

.c7 {
  display: inline-block;
  position: relative;
}

.c1 {
  width: 100% !important;
}

.c1 .c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c1 .c10 {
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  padding-top: 0;
  width: auto;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c6 {
  padding-top: 1px;
}

.c0 svg {
  background-color: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
}

.c0 .c8,
.c0 svg {
  height: 16px;
  position: absolute;
  padding: 1px;
}

.c0 .c10 {
  width: auto;
  -webkit-flex: 0 1 auto;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
}

<div>
  <div
    class="e-field e-checkbox-field"
    data-label="Test Checkbox Title"
    data-testid="e-checkbox-field e-field-label-testCheckboxTitle e-field-bind-test-checkbox-field"
  >
    <div
      class="c0"
      data-component="checkbox"
    >
      <div
        class="c1"
      >
        <div
          class="c2 c3"
        >
          <div
            class="c4 c5"
            data-role="field-line"
          >
            <div
              class="c6 c7"
              data-role="checkable-input"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="c8 c9"
                data-label="Test Checkbox Title"
                id="TestPage-test-checkbox-field"
                name="test-checkbox-field"
                role="checkbox"
                type="checkbox"
                value="checked"
              />
              <div
                class=""
              >
                <svg
                  data-role="checkable-svg"
                  focusable="false"
                  height="10"
                  viewBox="0 0 12 10"
                  width="12"
                >
                  <path
                    d="M.237 6.477A.752.752 0 0 1 .155 5.47l.851-1.092a.63.63 0 0 1 .934-.088l2.697 1.964, 4.674-6a.63.63 0 0 1 .933-.088l1.015.917c.28.254.317.703.081 1.005L6.353 8.492a.725.725, 0 0 1-.095.16l-.85 1.093a.637.637 0 0 1-.626.244.638.638 0 0 1-.335-.16L.237 6.476z"
                    fill="#FFFFFF"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
            <div
              class="c10 c11"
              data-role="label-container"
              id="label-container-TestPage-test-checkbox-field-label"
              width="30"
            >
              <label
                class="c12"
                data-element="label"
                for="TestPage-test-checkbox-field"
                id="TestPage-test-checkbox-field-label"
              >
                Test Checkbox Title
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Checkbox component unconnected Snapshots should render with true state 1`] = `
.c3 {
  margin-bottom: var(--fieldSpacing);
}

.c2 + .c2 {
  margin-top: 16px;
}

.c3.c3.c3 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c12 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  box-sizing: border-box;
  margin-bottom: 0;
  padding-left: var(--spacing100);
  width: 30%;
}

.c9 {
  cursor: pointer;
  opacity: 0;
  margin: 0;
  position: relative;
  z-index: 2;
}

.c7 {
  display: inline-block;
  position: relative;
}

.c1 {
  width: 100% !important;
}

.c1 .c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c1 .c10 {
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  padding-top: 0;
  width: auto;
}

.c0 {
  margin-bottom: var(--fieldSpacing);
}

.c0 .c6 {
  padding-top: 1px;
}

.c0 svg {
  background-color: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
}

.c0 .c8,
.c0 svg {
  height: 16px;
  position: absolute;
  padding: 1px;
}

.c0 .c10 {
  width: auto;
  -webkit-flex: 0 1 auto;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
}

<div>
  <div
    class="e-field e-checkbox-field"
    data-label="Test Checkbox Title"
    data-testid="e-checkbox-field e-field-label-testCheckboxTitle e-field-bind-test-checkbox-field"
  >
    <div
      class="c0"
      data-component="checkbox"
    >
      <div
        class="c1"
      >
        <div
          class="c2 c3"
        >
          <div
            class="c4 c5"
            data-role="field-line"
          >
            <div
              class="c6 c7"
              data-role="checkable-input"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                checked=""
                class="c8 c9"
                data-label="Test Checkbox Title"
                id="TestPage-test-checkbox-field"
                name="test-checkbox-field"
                role="checkbox"
                type="checkbox"
                value="checked"
              />
              <div
                class=""
              >
                <svg
                  data-role="checkable-svg"
                  focusable="false"
                  height="10"
                  viewBox="0 0 12 10"
                  width="12"
                >
                  <path
                    d="M.237 6.477A.752.752 0 0 1 .155 5.47l.851-1.092a.63.63 0 0 1 .934-.088l2.697 1.964, 4.674-6a.63.63 0 0 1 .933-.088l1.015.917c.28.254.317.703.081 1.005L6.353 8.492a.725.725, 0 0 1-.095.16l-.85 1.093a.637.637 0 0 1-.626.244.638.638 0 0 1-.335-.16L.237 6.476z"
                    fill="#FFFFFF"
                    fill-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
            <div
              class="c10 c11"
              data-role="label-container"
              id="label-container-TestPage-test-checkbox-field-label"
              width="30"
            >
              <label
                class="c12"
                data-element="label"
                for="TestPage-test-checkbox-field"
                id="TestPage-test-checkbox-field-label"
              >
                Test Checkbox Title
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
