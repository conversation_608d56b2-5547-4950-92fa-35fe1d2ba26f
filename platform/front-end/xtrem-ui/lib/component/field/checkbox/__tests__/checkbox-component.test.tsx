import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    applyActionMocks,
} from '../../../../__tests__/test-helpers';

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import { FieldKey } from '../../../types';
import { CheckboxComponent, ConnectedCheckboxComponent } from '../checkbox-component';
import type { CheckboxProperties } from '../checkbox-types';
import { cleanup, fireEvent, render, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

describe('Checkbox component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: CheckboxProperties;

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test Checkbox Title',
        };
        jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue(
            () => Promise.resolve({ type: 'SetFieldValue' }) as any,
        );
    });

    afterEach(() => {
        cleanup();
        jest.resetAllMocks();
        applyActionMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Checkbox, state, screenId, 'test-checkbox-field', mockFieldProperties, false);
            addFieldToState(FieldKey.Checkbox, state, screenId, 'test-empty-checkbox-field', mockFieldProperties, null);
            mockStore = getMockStore(state);
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCheckboxComponent screenId={screenId} elementId="test-checkbox-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCheckboxComponent screenId={screenId} elementId="test-checkbox-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCheckboxComponent screenId={screenId} elementId="test-checkbox-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render read-only', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCheckboxComponent screenId={screenId} elementId="test-checkbox-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCheckboxComponent screenId={screenId} elementId="test-checkbox-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render without value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCheckboxComponent screenId={screenId} elementId="test-empty-checkbox-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render read-only using a conditional declaration', () => {
                mockFieldProperties.isReadOnly = () => {
                    return true;
                };

                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedCheckboxComponent screenId={screenId} elementId="test-checkbox-field" />
                    </Provider>,
                );

                expect(wrapper.container.querySelector('input[type="checkbox"]')).toBeNull();

                mockFieldProperties.isReadOnly = () => {
                    return false;
                };

                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedCheckboxComponent screenId={screenId} elementId="test-checkbox-field" />
                    </Provider>,
                );

                expect(wrapper.container.querySelector('input[type="checkbox"]')).not.toBeNull();
            });
        });

        describe('Interactions', () => {
            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCheckboxComponent screenId={screenId} elementId="test-checkbox-field" />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="help"]')).not.toBeNull();
                expect(container.querySelector('[data-element="help"]')).toHaveTextContent('This is a helper text');
            });

            it('Should not render helperText', () => {
                mockFieldProperties.helperText = '';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCheckboxComponent screenId={screenId} elementId="test-checkbox-field" />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="help"]')).toBeNull();
            });

            it('should update parent on value', async () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCheckboxComponent screenId={screenId} elementId="test-checkbox-field" />
                    </Provider>,
                );
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                const checkbox = container.querySelector('input[type="checkbox"]')!;
                fireEvent.click(checkbox, { target: { checked: false } });
                expect(xtremRedux.actions.setFieldValue).toHaveBeenCalled();
                expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(
                    screenId,
                    'test-checkbox-field',
                    true,
                    true,
                );
            });
        });
    });

    describe('unconnected', () => {
        describe('Snapshots', () => {
            it('should render with false state', () => {
                const { container } = render(
                    <CheckboxComponent
                        elementId="test-checkbox-field"
                        fieldProperties={mockFieldProperties}
                        value={false}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with true state', () => {
                const { container } = render(
                    <CheckboxComponent
                        elementId="test-checkbox-field"
                        fieldProperties={mockFieldProperties}
                        value={true}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with an info message', async () => {
                mockFieldProperties.infoMessage = 'Info message!!';
                const { baseElement } = render(
                    <CheckboxComponent
                        elementId="test-checkbox-field"
                        fieldProperties={mockFieldProperties}
                        value={true}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an info message from a callback', async () => {
                mockFieldProperties.infoMessage = () => 'Info message!!';
                const { baseElement } = render(
                    <CheckboxComponent
                        elementId="test-checkbox-field"
                        fieldProperties={mockFieldProperties}
                        value={true}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an warning message', async () => {
                mockFieldProperties.warningMessage = 'Warning message!!';
                const { baseElement } = render(
                    <CheckboxComponent
                        elementId="test-checkbox-field"
                        fieldProperties={mockFieldProperties}
                        value={true}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with an warning message from a callback', async () => {
                mockFieldProperties.warningMessage = () => 'Warning message!!';
                const { baseElement } = render(
                    <CheckboxComponent
                        elementId="test-checkbox-field"
                        fieldProperties={mockFieldProperties}
                        value={true}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });
        });
    });
});
