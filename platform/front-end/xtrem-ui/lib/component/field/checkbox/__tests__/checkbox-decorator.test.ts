import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import { checkboxField } from '../checkbox-decorator';

describe('Checkbox decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'checkboxField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(checkboxField, pageMetadata, fieldId);
        });
    });
});
