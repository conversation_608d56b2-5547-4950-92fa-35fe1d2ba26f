import { <PERSON><PERSON><PERSON> } from '../../../types';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { CheckboxControlObject } from '../../../control-objects';

describe('Checkbox Field', () => {
    const screenId = 'TestPage';
    let checkboxField: CheckboxControlObject;

    beforeEach(() => {
        checkboxField = createFieldControlObject<FieldKey.Checkbox>(
            FieldKey.Checkbox,
            screenId,
            CheckboxControlObject,
            'test',
            true,
            {
                title: 'TEST_FIELD_TITLE',
                isHidden: true,
                isDisabled: true,
            },
        );
    });

    it('getting field value', () => {
        expect(checkboxField.value).toEqual(true);
    });
});
