import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { CheckboxComponentProps } from './checkbox-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedCheckboxComponent = React.lazy(() => import('./checkbox-component'));

export function AsyncConnectedCheckboxComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedCheckboxComponent {...props} />
        </React.Suspense>
    );
}

const CheckboxComponent = React.lazy(() =>
    import('./checkbox-component').then(c => ({ default: c.CheckboxComponent })),
);

export function AsyncCheckboxComponent(props: CheckboxComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <CheckboxComponent {...props} />
        </React.Suspense>
    );
}
