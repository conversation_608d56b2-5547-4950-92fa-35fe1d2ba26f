/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { CheckboxControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { CheckboxDecoratorProperties } from './checkbox-types';

class CheckboxDecorator extends AbstractFieldDecorator<FieldKey.Checkbox> {
    protected _controlObjectConstructor = CheckboxControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

/**
 * Initializes the decorated member as a [Checkbox]{@link CheckboxControlObject} field with the provided properties
 *
 * @param properties The properties that the [Checkbox]{@link CheckboxControlObject} field will be initialized with
 */
export function checkboxField<T extends ScreenExtension<T>>(
    properties: CheckboxDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Checkbox>(properties, CheckboxDecorator, FieldKey.Checkbox);
}

export function checkboxFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<CheckboxDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Checkbox>(properties);
}
