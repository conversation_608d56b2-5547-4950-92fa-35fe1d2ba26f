import { Checkbox } from 'carbon-react/esm/components/checkbox';
import * as React from 'react';
import { connect } from 'react-redux';
import { Icon } from '../../ui/icon/icon-component';
import { getCommonCarbonComponentProperties } from '../carbon-helpers';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { CheckboxDecoratorProperties } from './checkbox-types';
import { handleChange } from '../../../utils/abstract-fields-utils';

export class CheckboxComponent extends EditableFieldBaseComponent<
    CheckboxDecoratorProperties,
    boolean,
    NestedFieldsAdditionalProperties
> {
    onChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
        const displayValue = event.target.checked;

        const value = this.props.fieldProperties.isValueReversed ? !displayValue : displayValue;
        if (this.props.value !== value) {
            handleChange(
                this.props.elementId,
                value,
                this.props.setFieldValue,
                this.props.validate,
                this.triggerChangeListener,
            );
        }
    };

    render(): React.ReactNode {
        const value = !!this.props.value || false;

        const displayValue = this.props.fieldProperties.isValueReversed ? !value : value;
        const iconValue = value ? 'tick' : 'cross';
        const noPadding = this.props.fieldProperties.noPadding ? ' e-no-padding' : '';

        return (
            <div
                ref={this.componentRef}
                {...this.getBaseAttributesDivWrapper(
                    'checkbox',
                    `e-checkbox-field${noPadding}`,
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
                onClick={!this.isReadOnly() ? undefined : this.getClickHandler()}
            >
                {this.isReadOnly() ? (
                    <div className="e-checkbox-field-read-only common-input">
                        <Icon type={iconValue} />
                        <div>
                            {(this.props.shouldRenderLabelInNestedReadOnlyMode || !this.props.isNested) && (
                                <FieldLabel label={this.getTitle()} />
                            )}
                            <HelperText helperText={this.props.fieldProperties.helperText} />
                        </div>
                    </div>
                ) : (
                    <Checkbox
                        checked={displayValue}
                        value="checked"
                        onClick={this.getClickHandler()}
                        onChange={this.onChange}
                        reverse={this.props.fieldProperties.isReversed}
                        {...getCommonCarbonComponentProperties(this.props)}
                    />
                )}
            </div>
        );
    }
}

export const ConnectedCheckboxComponent = connect(mapStateToProps(), mapDispatchToProps())(CheckboxComponent);

export default ConnectedCheckboxComponent;
