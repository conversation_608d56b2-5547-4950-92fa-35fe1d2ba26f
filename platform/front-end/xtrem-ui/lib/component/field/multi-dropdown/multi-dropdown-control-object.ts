/**
 * @packageDocumentation
 * @module root
 * */
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { MultiDropdownProperties } from './multi-dropdown-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a value from a set of given values
 */
export class MultiDropdownControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.MultiDropdown,
    FieldComponentProps<FieldKey.MultiDropdown>
> {
    @ControlObjectProperty<MultiDropdownProperties<CT>, MultiDropdownControlObject<CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<MultiDropdownProperties<CT>, MultiDropdownControlObject<CT>>()
    /** Icon of the input field. It will be placed on the right side. */
    icon?: IconType;

    @ControlObjectProperty<MultiDropdownProperties<CT>, MultiDropdownControlObject<CT>>()
    /** Color of the icon, only supported in tile containers */
    iconColor?: string;

    @ControlObjectProperty<MultiDropdownProperties<CT>, MultiDropdownControlObject<CT>>()
    /** Indicator, whether sounds play on successful/erroneous selection */
    isSoundDisabled?: boolean;

    @FieldControlObjectResolvedProperty<MultiDropdownProperties<CT>, MultiDropdownControlObject<CT>>()
    /** Options to be displayed in the MultiDropdown element */
    options?: string[];

    @ControlObjectProperty<MultiDropdownProperties<CT>, MultiDropdownControlObject<CT>>()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    @ControlObjectProperty<MultiDropdownProperties<CT>, MultiDropdownControlObject<CT>>()
    /** Text to be displayed inline after the field value */
    prefix?: string;

    @ControlObjectProperty<MultiDropdownProperties<CT>, MultiDropdownControlObject<CT>>()
    /** Text to be displayed inline before the field value */
    postfix?: string;

    @ControlObjectProperty<MultiDropdownProperties<CT>, MultiDropdownControlObject<CT>>()
    /**  The minimum number of items that has to be selected*/
    minItems?: number;

    @ControlObjectProperty<MultiDropdownProperties<CT>, MultiDropdownControlObject<CT>>()
    /**  The maximum number of items that has to be selected*/
    maxItems?: number;

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }
}
