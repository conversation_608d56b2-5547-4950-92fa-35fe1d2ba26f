import * as React from 'react';
import { connect } from 'react-redux';
import type { XtremAppState } from '../../../redux';
import { ContextType } from '../../../types';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { addOptionsAndLocalizationToProps, splitValueToMergedValue } from '../../../utils/transformers';
import type { SelectItem } from '../../ui/select/select-component';
import { Select } from '../../ui/select/select-component';
import { getCommonCarbonComponentProperties, getLabelTitle } from '../carbon-helpers';
import { CarbonWrapper } from '../carbon-wrapper';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { createSelectItemFromOption, getItemsFromProps } from '../select/select-utils';
import type {
    MultiDropdownComponentProps,
    MultiDropdownInternalProps,
    MultiDropdownDecoratorProperties,
} from './multi-dropdown-types';

export class MultiDropdownComponent extends EditableFieldBaseComponent<
    MultiDropdownDecoratorProperties,
    string[],
    MultiDropdownInternalProps
> {
    private readonly onChange = (items: SelectItem[]): void => {
        handleChange(
            this.props.elementId,
            items.map(v => v.id),
            this.props.setFieldValue,
            this.props.validate,
            this.triggerChangeListener,
        );
    };

    private readonly getBorderColor = (): string | undefined => {
        return this.props.contextType === ContextType.pod ? 'transparent' : undefined;
    };

    private readonly getItems = (searchText?: string): SelectItem[] => {
        if (this.props.fieldProperties.optionType) {
            return getItemsFromProps(this.props, searchText, splitValueToMergedValue(this.props.recordContext));
        }

        if (this.props.fieldProperties.options) {
            const options: string[] = resolveByValue({
                propertyValue: this.props.fieldProperties.options,
                screenId: this.props.screenId,
                skipHexFormat: true,
                fieldValue: this.props.value,
                rowValue: this.props.recordContext,
            });

            return options.map(option => createSelectItemFromOption(option, this.props));
        }

        return [];
    };

    private readonly getReadOnlyValue = (): string => {
        const value = this.props.value || [];
        const items = this.getItems();
        return value.map(v => items.find(i => i.id === v)?.displayedAs).join(', ');
    };

    render(): React.ReactNode {
        const carbonProps = getCommonCarbonComponentProperties(this.props);
        const value = this.props.value
            ? this.props.value.map(v => createSelectItemFromOption(v, this.props))
            : undefined;
        const label = !this.props.fieldProperties.isTitleHidden
            ? getLabelTitle(this.props.screenId, this.props.fieldProperties, this.props.handlersArguments?.rowValue)
            : undefined;
        const {
            fieldProperties: { isFullWidth, icon, placeholder, helperText, size },
            validationErrors,
            onFocus,
            handlersArguments,
        } = this.props;
        return (
            <CarbonWrapper
                {...this.props}
                className="e-multi-dropdown-field"
                componentName="multi-dropdown"
                componentRef={this.componentRef}
                handlersArguments={handlersArguments}
                helperText={helperText}
                noReadOnlySupport={true}
                value={this.props.value}
                readOnlyDisplayValue={this.getReadOnlyValue()}
            >
                <Select
                    {...carbonProps}
                    autoSelect={false}
                    borderColor={this.getBorderColor()}
                    disabled={this.isDisabled()}
                    disablePills={true}
                    hasInputSearch={false}
                    error={validationErrors?.[0]?.message}
                    fullWidth={isFullWidth}
                    getItems={(searchText: string): Promise<SelectItem[]> => Promise.resolve(this.getItems(searchText))}
                    helperText={helperText}
                    icon={icon}
                    inputId={carbonProps.id}
                    label={label}
                    initialSelectedItems={value}
                    isSortedAlphabetically={this.props.fieldProperties.isSortedAlphabetically}
                    selectedItems={value}
                    isMultiSelect={true}
                    minLookupCharacters={0}
                    onSelectedItemsChange={this.onChange}
                    onInputFocus={onFocus}
                    placeholder={placeholder}
                    readOnly={this.isReadOnly()}
                    size={size}
                    testId="e-multi-dropdown-field-input"
                    screenId={this.props.screenId}
                    elementId={this.props.elementId}
                    isSoundDisabled={resolveByValue({
                        screenId: this.props.screenId,
                        propertyValue: this.props.fieldProperties.isSoundDisabled,
                        skipHexFormat: true,
                        fieldValue: null,
                        rowValue: null,
                    })}
                />
            </CarbonWrapper>
        );
    }
}

export const ConnectedMultiDropdownComponent = connect(
    (state: XtremAppState, externalProps: FieldComponentExternalProperties) =>
        addOptionsAndLocalizationToProps(state, mapStateToProps()(state, externalProps) as MultiDropdownComponentProps),
    mapDispatchToProps(),
)(MultiDropdownComponent);

export default ConnectedMultiDropdownComponent;
