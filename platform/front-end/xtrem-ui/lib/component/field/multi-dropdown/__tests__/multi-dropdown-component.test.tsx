import { getMockState, getMockStore, getMockPageDefinition } from '../../../../__tests__/test-helpers';

import { render } from '@testing-library/react';
import * as React from 'react';
import type { MultiDropdownComponentProps } from '../multi-dropdown-types';
import { MultiDropdownComponent } from '../multi-dropdown-component';
import type * as xtremRedux from '../../../../redux';

const selectMock = jest.fn();

jest.mock('../../../ui/select/select-component', () => ({
    Select: (props: any) => {
        selectMock(props);
        return (
            <div
                data-testid="mockSelectComponent"
                id="mockMultiLookupDialog"
                data-open={props.isOpen ? 'true' : 'false'}
            />
        );
    },
}));

describe('Multi dropdown component', () => {
    let props: MultiDropdownComponentProps;
    let state: xtremRedux.XtremAppState;
    const screenId = 'TestScreenId';

    beforeEach(() => {
        selectMock.mockClear();
        props = {
            elementId: 'test-elementId',
            fieldProperties: {},
            screenId,
            setFieldValue: jest.fn(),
            validate: jest.fn(),
            onFocus: jest.fn(),
            removeNonNestedErrors: jest.fn(),
            locale: 'en-US',
        };

        state = getMockState({
            enumTypes: {
                RockPaperScissorsEnum: ['rock', 'paper', 'scissors', 'lizard', 'spock'],
                RockPaperScissorsTranslatedEnum: ['rock', 'paper', 'scissors', 'lizard', 'spock'],
            },
            translations: {
                'en-US': {
                    '@sage/xtrem-test/enums__rock_paper_scissors_translated_enum__rock': 'Translated Rock',
                    '@sage/xtrem-test/enums__rock_paper_scissors_translated_enum__paper': 'Translated Paper',
                    '@sage/xtrem-test/enums__rock_paper_scissors_translated_enum__scissors': 'Translated Scissors',
                    '@sage/xtrem-test/enums__rock_paper_scissors_translated_enum__lizard': 'Translated Lizard',
                    '@sage/xtrem-test/enums__rock_paper_scissors_translated_enum__spock': 'Translated Spock',
                },
            },
        });
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        getMockStore(state);
    });

    const getMultiDropdownField = () => {
        return <MultiDropdownComponent {...props} />;
    };

    it('Should render the component with only mandatory props', () => {
        const wrapper = render(getMultiDropdownField());
        const multiDropdownField = wrapper.queryByTestId('e-multi-dropdown-field e-field-bind-test-elementId');
        expect(multiDropdownField).toBeTruthy();
        expect(multiDropdownField!).toMatchSnapshot();
    });

    it('Select component should receive the value with SelectItem format', () => {
        props.value = ['patata', 'foo'];
        render(getMultiDropdownField());
        const selectArgument = selectMock.mock.calls[0];
        const expectedSelectedItems = [
            { id: 'patata', value: 'patata', displayedAs: 'patata' },
            { id: 'foo', value: 'foo', displayedAs: 'foo' },
        ];
        expect(selectArgument[0].selectedItems).toEqual(expectedSelectedItems);
    });

    it('should render component in read-only mode with localized options if translations are available', () => {
        props.value = ['rock', 'paper'];
        props.enumOptions = ['rock', 'paper', 'patata', 'apple'];
        props.localizedOptions = {
            rock: 'Translated Rock',
            paper: 'Translated Paper',
            scissors: 'Translated Scissors',
            lizard: 'Translated Lizard',
            spock: 'Translated Spock',
        };
        props.fieldProperties.optionType = '@sage/xtrem-test/RockPaperScissorsTranslatedEnum';
        props.nestedReadOnlyField = true;
        const wrapper = render(getMultiDropdownField());
        expect(wrapper.queryByTestId('e-field-value')).toHaveTextContent('Translated Rock, Translated Paper');
    });

    it('should render component in read-only mode without translations if they are unavailable', () => {
        props.value = ['rock', 'paper'];
        props.fieldProperties.options = ['rock', 'paper', 'patata', 'apple'];
        props.fieldProperties.optionType = '@sage/xtrem-test/RockPaperScissorsEnum';
        props.nestedReadOnlyField = true;
        const wrapper = render(getMultiDropdownField());
        expect(wrapper.queryByTestId('e-field-value')).toHaveTextContent('rock, paper');
    });

    it('should render component in read-only mode without translations if no option type is provided', () => {
        props.value = ['rock', 'paper', 'patata'];
        props.fieldProperties.options = ['rock', 'paper', 'patata', 'apple'];
        props.nestedReadOnlyField = true;
        const wrapper = render(getMultiDropdownField());
        expect(wrapper.queryByTestId('e-field-value')).toHaveTextContent('rock, paper, patata');
    });

    it('should render component in read-only mode with using the map property', () => {
        props.value = ['rock', 'paper', 'patata'];
        props.fieldProperties.options = ['rock', 'paper', 'patata', 'apple'];
        props.fieldProperties.map = (v): string => `mapped ${v}`;
        props.nestedReadOnlyField = true;
        const wrapper = render(getMultiDropdownField());
        expect(wrapper.queryByTestId('e-field-value')).toHaveTextContent('mapped rock, mapped paper, mapped patata');
    });

    it('should render component in read-only mode with using the map property using the record context', () => {
        props.value = ['rock', 'paper', 'patata'];
        props.fieldProperties.options = ['rock', 'paper', 'patata', 'apple'];
        props.fieldProperties.map = (v, rowValue): string => `mapped ${rowValue._id} ${v}`;
        props.nestedReadOnlyField = true;
        props.recordContext = { _id: 32 };
        const wrapper = render(getMultiDropdownField());
        expect(wrapper.queryByTestId('e-field-value')).toHaveTextContent(
            'mapped 32 rock, mapped 32 paper, mapped 32 patata',
        );
    });
});
