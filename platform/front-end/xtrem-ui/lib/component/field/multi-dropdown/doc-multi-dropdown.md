PATH: XTREEM/UI+Field+Widgets/Multi+Dropdown+Field

## Introduction

Multi-dropdown fields can be used to used to select multiple items from a collection.
It is also available as a nested field in the navigation panel, tables, charts and calendars.

## Example:

```ts
    @ui.decorators.multiDropdownField<MultiDropdown>({
        isTransient: true,
        options() {
            return ['one', 'two', 'three', 'four', 'five'];
        },
        title: 'With Options',
        map(value: any) {
            switch (value) {
                case 'one':
                    return 'One';
                case 'two':
                    return 'Two';
                case 'three':
                    return 'Three';
                case 'four':
                    return 'Four';
                case 'five':
                    return 'Five';
                default:
                    return value;
            }
        },
        onChange() {
            ui.console.log("Do something when field's value has changed.");
        },
        onClick() {
            ui.console.log('Do something when field has been clicked.');
        },
        onError(error, screenId, elementId) {
            ui.console.log("Do something when field's callback has thrown an error", { error, screenId, elementId });
        },
        parent() {
            return this.block;
        },
    })
    multiDropdownField: ui.fields.MultiDropdown;
```

### Display decorator properties


-   **helperText**: The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **insertBefore**: The field before this extension field is inserted.
-   **icon**: Icon to be displayed on the right side of the input. If the multi-dropdown field has a lookup dialog, the icon will be displayed on the left of the lookup icon. The list of icons are defined by [DLS](https://brand.sage.com/d/NdbrveWvNheA/foundations#/icons/icons).
-   **iconColor**: Color of the input icon, only supported if the field is rendered within a tile container.
-   **isDisabled**: Whether the HTML element is disabled or not. Defaults to false The difference with readOnly is that disabled suggests that the field is not editable for some validation reason (e.g. a button which can't be clicked due to validation errors.
-   **isHelperTextHidden**: Specifies whether the field's helper text should be displayed or not.
-   **isFullWidth**: Specifies whether the field should take the full width.
-   **isHidden**: Specifies whether the component should be displayed or not.
-   **isHiddenDesktop**: Specifies whether the component should be displayed for desktop-size viewports.
-   **isHiddenMobile**: Specifies whether the component should be displayed for mobile-size viewports.
-   **infoMessage**: Indicate additional warning message, rendered as tooltip and blue border.
-   **isReadOnly**: Specifies whether the field is editable or not. The difference between isDisabled and isReadOnly, is that isReadOnly suggests that the field is never editable, whereas isDisabled suggest that the field is currently not editable. It can also be defined as callback function that returns a boolean.
-   **isSortedAlphabetically**: Forces the options to be rendered in alphabetical order
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **placeholder**: Placeholder text which is displayed inside the field body when the field is empty. It is automatically picked up by the i18n engine and externalized.
-   **postfix**: Text to be displayed inline after the field value
-   **prefix**: Text to be displayed inline before the field value
-   **size**: Vertical size of the field, it can be `small`, `medium` or `large`. It is set to medium by default.
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **warningMessage**: Indicate additional information, rendered as tooltip and orange border
-   **width**: The width of the block relative to the section where it is place Must be a number between 1 and 12, both included
-   **minItems**: Set the minimum number of items that has to be selected.
-   **maxItems**: Set the maximum number of items that can be selected.


### Binding decorator properties

-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **optionType**: The GraphQL node that the select options will be fetched from When using this property, the node must be an Enum
-   **map**: Function that can be used to set/transform the component text
-   **options**: The list of options available in the select field
-   **parent**: The container in which this component will render

### Event handler decorator properties
-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.
-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

### Validator decorator properties

-   **isMandatory**: Specifies whether the field is mandatory or not. When enabled, empty values will raise a validation error message.  It can also be defined as callback function that returns a boolean.
-   **validation**: Custom validation callback, the new value is provided in the argument. If the function returns a non-empty string (or promise resolving to a non-empty string), it will be used as a validation error message. If returns a falsy value, the field is considered to be valid.

### Access bindings decorator properties:

-   **access**: An object with the properties **node** and **bind** to defined an explicit page access binding. If not present the **node** and **bind** binding decorator properties described just above are used.

### Other decorator properties

-   **fetchesDefaults**: When set to true and when the multi-reference value changes, a request to the server for default values for the whole page will be requested. False by default.

#### Runtime Functions

-   **refresh()**: Refetches the field's values from the server and updates the user interface.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/MultiDropdown).
