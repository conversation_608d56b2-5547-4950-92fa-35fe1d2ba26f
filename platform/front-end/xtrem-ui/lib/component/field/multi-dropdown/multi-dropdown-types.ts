import type { ClientNode } from '@sage/xtrem-client';
import type { Dict } from '@sage/xtrem-shared';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { BlockControlObject } from '../../container/container-control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasIcon,
    HasOptions,
    <PERSON>Parent,
    HasPlaceholder,
    HasSound,
    Mappable,
    MaxItems,
    MinItems,
    Nested,
    NestedChangeable,
    NestedClickable,
    NestedMaxItems,
    NestedMinItems,
    NestedValidatable,
    Postfixable,
    Prefixable,
    Sizable,
    Validatable,
} from '../traits';

export interface MultiDropdownProperties<CT extends ScreenBase = ScreenBase, ContextNodeType = void>
    extends EditableFieldProperties<CT, ContextNodeType>,
        Clickable<CT>,
        HasIcon,
        HasPlaceholder,
        HasOptions<CT>,
        HasSound<CT>,
        Mappable<CT>,
        Postfixable<CT, ContextNodeType>,
        Prefixable<CT, ContextNodeType>,
        Sizable,
        MinItems<CT>,
        MaxItems<CT> {}

export interface MultiDropdownDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<MultiDropdownProperties<CT>, '_controlObjectType'>,
        Changeable<CT>,
        HasParent<CT, BlockControlObject<CT>>,
        HasSound<CT>,
        Mappable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Sizable,
        Validatable<CT, string[]>,
        MinItems<CT>,
        MaxItems<CT> {}

type NestedUnionType = 'onClick' | 'minItems' | 'maxItems';
export interface NestedMultiDropdownProperties<
    CT extends ScreenBase = ScreenBase,
    ContextNodeType extends ClientNode = any,
> extends Omit<NestedPropertiesWrapper<MultiDropdownProperties<CT, ContextNodeType>>, NestedUnionType>,
        Nested<ContextNodeType>,
        NestedChangeable<CT>,
        NestedClickable<CT, ContextNodeType>,
        NestedValidatable<CT, string[], ContextNodeType>,
        Sizable,
        NestedMinItems<CT>,
        NestedMaxItems<CT> {}

export type MultiDropdownExtensionDecoratorProperties<T extends ScreenExtension<T>> =
    ChangeableOverrideDecoratorProperties<MultiDropdownDecoratorProperties<Extend<T>>, T>;

export interface MultiDropdownInternalProps {
    enumOptions?: string[];
    localizedOptions?: Dict<string>;
    screenId: string;
    value?: string[];
}

export type MultiDropdownComponentProps = BaseEditableComponentProperties<
    MultiDropdownDecoratorProperties,
    string[],
    MultiDropdownInternalProps & NestedFieldsAdditionalProperties
>;
