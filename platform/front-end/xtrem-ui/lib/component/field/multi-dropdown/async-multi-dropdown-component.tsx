import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { MultiDropdownComponentProps } from './multi-dropdown-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedMultiDropdownComponent = React.lazy(() => import('./multi-dropdown-component'));

export function AsyncConnectedMultiDropdownComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedMultiDropdownComponent {...props} />
        </React.Suspense>
    );
}

const MultiDropdownComponent = React.lazy(() =>
    import('./multi-dropdown-component').then(c => ({ default: c.MultiDropdownComponent })),
);

export function AsyncMultiDropdownComponent(props: MultiDropdownComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <MultiDropdownComponent {...props} />
        </React.Suspense>
    );
}
