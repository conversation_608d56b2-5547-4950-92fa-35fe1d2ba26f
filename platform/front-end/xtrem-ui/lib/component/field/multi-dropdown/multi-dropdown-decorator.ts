/**
 * @packageDocumentation
 * @module root
 * */
import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import { addDisabledToProperties, addOptionTypeToProperties } from '../../../utils/data-type-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { MultiDropdownControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type {
    MultiDropdownDecoratorProperties,
    MultiDropdownExtensionDecoratorProperties,
} from './multi-dropdown-types';

class MultiDropdownDecorator extends AbstractFieldDecorator<FieldKey.MultiDropdown> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = MultiDropdownControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<MultiDropdownDecoratorProperties> {
        const properties: Partial<MultiDropdownDecoratorProperties> = {};
        addOptionTypeToProperties({ propertyDetails, dataType, properties });
        addDisabledToProperties({
            propertyDetails,
            dataType,
            properties,
        });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [MultiDropdown]{@link MultiDropdownControlObject} field with the provided properties
 *
 * @param properties The properties that the [MultiDropdown]{@link MultiDropdownControlObject} field will be initialized with
 */
export function multiDropdownField<T extends ScreenExtension<T>>(
    properties: MultiDropdownDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.MultiDropdown>(
        properties,
        MultiDropdownDecorator,
        FieldKey.MultiDropdown,
    );
}

export function multiDropdownFieldOverride<T extends ScreenExtension<T>>(
    properties: MultiDropdownExtensionDecoratorProperties<T>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.MultiDropdown>(properties);
}
