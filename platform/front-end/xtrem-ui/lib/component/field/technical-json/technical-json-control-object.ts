/**
 * @packageDocumentation
 * @module root
 */
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { FieldComponentProps, FieldKey } from '../../types';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a value from a set of given values.
 */
export class TechnicalJsonControlObject<
    T = any,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends ReadonlyFieldControlObject<CT, FieldKey.TechnicalJson, FieldComponentProps<FieldKey.TechnicalJson>> {
    public get value(): T {
        return this._getValue() as T;
    }

    public set value(newValue: T) {
        this._setValue(newValue as T);
    }
}
