import type { ScreenExtension } from '../../../types';
import type { ScreenBase } from '../../../service/screen-base';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';

export interface TechnicalJsonProperties<CT extends ScreenExtension<CT> = ScreenBase>
    extends ReadonlyFieldProperties<CT> {}

export interface TechnicalJsonDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<TechnicalJsonProperties<CT>, '_controlObjectType'> {}
