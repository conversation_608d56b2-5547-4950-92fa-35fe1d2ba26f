PATH: XTREEM/UI+Field+Widgets/Technical+JSON+Field

## Introduction

The Technical JSON Field is used to handle JSON data directly without displaying it in the UI. It is designed for advanced use cases where raw JSON manipulation and saving is required in a programmatically way just manipulating field.value property.

## Example:

```ts
 // Note: localizedTitle is a JSON object that contains the translations (something like { 'en-US': 'Hello', 'fr-FR': 'Bonjour' })
    // title is only editing the translation for the current locale
    @ui.decorators.technicalJsonField<WorkflowActionUpdateEntity>({})
    localizedTitle: ui.fields.TechnicalJson<LocalizedText>;
```

### Binding decorator properties
-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
- 
-   