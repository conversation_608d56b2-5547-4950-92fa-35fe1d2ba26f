import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { TechnicalJsonControlObject } from './technical-json-control-object';
import type { TechnicalJsonDecoratorProperties } from './technical-json-types';

class TechnicalJsonDecorator extends AbstractFieldDecorator<FieldKey.TechnicalJson> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = TechnicalJsonControlObject;
}

/**
 * Initializes the decorated member as a [TechnicalJsonDecorator]{@link TechnicalJsonDecoratorControlObject} field with the provided properties.
 *
 * @param properties The properties that the [TechnicalJsonDecorator]{@link TechnicalJsonDecoratorControlObject} field will be initialized with.
 */
export function technicalJsonField<T extends ScreenExtension<T>>(
    properties: TechnicalJsonDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.TechnicalJson>(
        properties,
        TechnicalJsonDecorator,
        FieldKey.TechnicalJson,
    );
}

export function technicalJsonFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<TechnicalJsonDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.TechnicalJson>(properties);
}
