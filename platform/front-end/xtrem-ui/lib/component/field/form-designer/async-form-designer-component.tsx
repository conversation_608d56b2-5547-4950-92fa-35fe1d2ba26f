import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { FormDesignerComponentProperties } from './form-designer-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedFormDesignerComponent = React.lazy(() => import('./form-designer-component'));

export function AsyncConnectedFormDesignerComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedFormDesignerComponent {...props} />
        </React.Suspense>
    );
}

const FormDesignerComponent = React.lazy(() =>
    import('./form-designer-component').then(c => ({ default: c.FormDesignerComponent })),
);

export function AsyncFormDesignerComponent(props: FormDesignerComponentProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <FormDesignerComponent {...props} />
        </React.Suspense>
    );
}
