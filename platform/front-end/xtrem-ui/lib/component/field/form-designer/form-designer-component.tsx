import * as React from 'react';
import { connect } from 'react-redux';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    DataModelProperty,
    GetObjectDetailsFunction,
    OnObjectInsertFunction,
    DocumentContextProvider,
} from '@sage/xtrem-document-editor';
import { XtremDocumentEditor, OBJECT_TYPE_ROOT, OBJECT_TYPE_GLOBAL_PROPERTIES } from '@sage/xtrem-document-editor';
import { camelCase, debounce, memoize } from 'lodash';
import { fetchNodeDetails, fetchNodeNames } from '../../../service/node-information-service';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { localize, localizeEnumMember } from '../../../service/i18n-service';
import { FieldHeader } from '../field-header';
import { HelperText } from '../carbon-utility-components';
import type { BinaryValue } from '../../types';
import { handleChange } from '../../../utils/abstract-fields-utils';
import type { FormDesignerComponentProps, FormDesignerDecoratorProperties } from './form-designer-types';
import type { ToastProps } from 'carbon-react/esm/components/toast';
import { showToast } from '../../../service/toast-service';
import type { MultiRootEditor } from '@ckeditor/ckeditor5-editor-multi-root';
import type { Unsubscribe } from '../../../redux/middleware/action-subscription-middleware';
import { subscribeToActions } from '../../../redux/middleware/action-subscription-middleware';
import { ActionType } from '../../../redux';
import type { FilterParameter } from '@sage/xtrem-ui-components';
import { triggerFieldEvent } from '../../../utils/events';
import { GraphQLKind, GraphQLTypes } from '../../../types';
import { carbonLocale } from '../../../utils/carbon-locale';
import { objectKeys } from '@sage/xtrem-shared';
import { shouldIncludeDocumentEditorProperty } from '../../../utils/document-editor-utils';

const additionalPropsCommonValues = {
    enumType: null,
    isCustom: false,
    dataType: '',
    targetNode: '',
    isStored: false,
    isOnInputType: true,
    isOnOutputType: true,
    isMutable: false,
};

const memoizedFetchNodeDetails = memoize(fetchNodeDetails);

export interface FormDesignerComponentState {
    value: string;
    headerValue: string;
    footerValue: string;
    isDirty: boolean;
}
export class FormDesignerComponent extends EditableFieldBaseComponent<
    FormDesignerDecoratorProperties,
    BinaryValue,
    NestedFieldsAdditionalProperties,
    FormDesignerComponentState
> {
    private actionSubscription: Unsubscribe;

    constructor(props: FormDesignerComponentProps) {
        super(props);
        this.state = {
            value: this.props.value?.value ? String(this.props.value?.value) : '',
            headerValue: this.props.fieldProperties?.headerValue ? String(this.props.fieldProperties?.headerValue) : '',
            footerValue: this.props.fieldProperties?.footerValue ? String(this.props.fieldProperties?.footerValue) : '',
            isDirty: false,
        };
    }

    componentWillReceiveProps(nextProps: Readonly<FormDesignerComponentProps>): void {
        if (nextProps.value?.value !== this.state.value) {
            this.setState({
                value: this.props.value?.value ? String(this.props.value?.value) : '',
            });
        }

        if (nextProps.fieldProperties?.headerValue !== this.state.headerValue) {
            const currentHeaderValue = this.props.fieldProperties?.headerValue;
            const nextHeaderValue = nextProps.fieldProperties?.headerValue;

            const newHeaderValue = String(currentHeaderValue || nextHeaderValue || '');

            this.setState({
                headerValue: newHeaderValue,
            });
        }

        if (nextProps.fieldProperties?.footerValue !== this.state.footerValue) {
            this.setState({
                footerValue: this.props.fieldProperties?.footerValue
                    ? String(this.props.fieldProperties?.footerValue)
                    : '',
            });
        }
    }

    componentWillUnmount(): void {
        this.actionSubscription?.();
    }

    private readonly onReadyEvent = debounce(() => {
        triggerFieldEvent(this.props.screenId, this.props.elementId, 'onReady');
    }, 50);

    private readonly onChange = (newValue: string): void => {
        this.setState({ value: newValue }, () => {
            handleChange(
                this.props.elementId,
                { value: newValue },
                this.props.setFieldValue,
                this.props.validate,
                this.triggerChangeListener,
            );
        });
    };

    private readonly onHeaderChange = (newValue: string): void => {
        this.setState({ headerValue: newValue }, () => {
            this.props.setFieldProperties?.(this.props.elementId, {
                ...this.props.fieldProperties,
                headerValue: newValue,
            });
            triggerFieldEvent(this.props.screenId, this.props.elementId, 'onHeaderValueChanged', newValue);
        });
    };

    private readonly onFooterChange = (newValue: string): void => {
        this.setState({ footerValue: newValue }, () => {
            this.props.setFieldProperties?.(this.props.elementId, {
                ...this.props.fieldProperties,
                footerValue: newValue,
            });
            triggerFieldEvent(this.props.screenId, this.props.elementId, 'onFooterValueChanged', newValue);
        });
    };

    private readonly getObjectDetails: GetObjectDetailsFunction = async ({
        objectType,
        contextType,
        canParentFilter,
        canParentSort,
    }) => {
        if (objectType === OBJECT_TYPE_GLOBAL_PROPERTIES) {
            const params =
                resolveByValue<FilterParameter[]>({
                    propertyValue: this.props.fieldProperties.parameters,
                    screenId: this.props.screenId,
                    skipHexFormat: true,
                    rowValue: null, // This is not a field context, so no row value can be present
                }) ?? [];

            const headerAndFooterOnly: DataModelProperty[] =
                contextType === 'header' || contextType === 'footer'
                    ? [
                          {
                              name: 'pageNumberCurrent',
                              label: localize('@sage/xtrem-ui/form-designer-var-page-number', 'Page number'),
                              kind: GraphQLKind.Scalar,
                              type: GraphQLTypes.String,
                              canFilter: false,
                              canSort: false,
                              ...additionalPropsCommonValues,
                          },
                          {
                              name: 'pageNumberTotal',
                              label: localize(
                                  '@sage/xtrem-ui/form-designer-var-total-number-of-pages',
                                  'Total number of pages',
                              ),
                              kind: GraphQLKind.Scalar,
                              type: GraphQLTypes.String,
                              canFilter: false,
                              canSort: false,
                              ...additionalPropsCommonValues,
                          },
                      ]
                    : [];

            return [
                {
                    name: '@root.currentDate',
                    label: localize('@sage/xtrem-ui/form-designer-var-current-date', 'Current date'),
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.Date,
                    canFilter: false,
                    canSort: false,
                    ...additionalPropsCommonValues,
                },
                {
                    name: '@root.user',
                    label: localize('@sage/xtrem-ui/form-designer-var-generation-by', 'Generated by'),
                    kind: GraphQLKind.Scalar,
                    type: GraphQLTypes.String,
                    canFilter: false,
                    canSort: false,
                    ...additionalPropsCommonValues,
                },
                ...headerAndFooterOnly,
                ...params.map<DataModelProperty>(param => ({
                    name: `@root.${param.name}`,
                    label: localize('@sage/xtrem-ui/form-designer-var-parameter', 'Parameter - {{name}}', {
                        name: param.label || param.name,
                    }),
                    kind: GraphQLKind.Scalar,
                    type: param.type,
                    canFilter: false,
                    canSort: false,
                    ...additionalPropsCommonValues,
                })),
            ];
        }
        if (objectType === OBJECT_TYPE_ROOT) {
            const nodeNames = await fetchNodeNames(true);
            return objectKeys(nodeNames).reduce<DataModelProperty[]>((acc, name) => {
                const label = nodeNames[name];
                const [namespace, node] = name.split('/').slice(-2);

                acc.push({
                    canFilter: true,
                    canSort: true,
                    kind: 'OBJECT',
                    label,
                    name: camelCase(name.split('/')[2]),
                    type: node,
                    iconType: 'csv',
                    node,
                    namespace: camelCase(namespace),
                    enumType: null,
                    isCustom: false,
                    dataType: '',
                    targetNode: '',
                    isStored: false,
                    isOnInputType: false,
                    isOnOutputType: false,
                    isMutable: false,
                });
                return acc;
            }, []);
        }
        const nodeDetails = await memoizedFetchNodeDetails({
            locale: this.props.locale,
            nodeName: objectType,
            getCollections: true,
        });

        return objectKeys(nodeDetails).reduce<DataModelProperty[]>((acc, name) => {
            const item = nodeDetails[name];
            const isListItem = item.kind === 'LIST' || item.isCollection;

            if (!shouldIncludeDocumentEditorProperty(item.type as string)) {
                return acc;
            }

            acc.push({
                ...item,
                iconType: isListItem ? 'list_view' : 'csv',
                kind: isListItem ? 'LIST' : item.kind,
                canFilter: canParentFilter && item.canFilter,
                canSort: canParentSort && item.canSort,
            });
            return acc;
        }, [] as DataModelProperty[]);
    };

    private readonly onEditorReady = (editor: MultiRootEditor): void => {
        this.actionSubscription = subscribeToActions(async action => {
            if (
                action.type === ActionType.ExecuteFormDesignCommand &&
                action.value.screenId === this.props.screenId &&
                action.value.elementId === this.props.elementId &&
                editor.execute
            ) {
                const currentRoot = editor.model.document.selection.getFirstRange()?.start.root.rootName;
                if (currentRoot !== 'body') {
                    // We must focus on the document body.
                    editor.model.change(writer => {
                        const rootElement = editor.model.document.getRoot('body')?.getChild(0);
                        if (rootElement) {
                            writer.setSelection(rootElement, 'after');
                        }
                    });
                }
                const args = action.value.args || [];
                editor.execute(action.value.command, ...args);
            }
        });

        this.onReadyEvent();
    };

    private readonly onObjectInsert: OnObjectInsertFunction = (path, property, insertMode) =>
        new Promise(resolve => {
            const rootPath = property.namespace ? `${property.namespace}.${path}` : path;
            if (insertMode === 'recordContext') {
                resolve({
                    path: `${rootPath}.query.edges.0.node`,
                });
            } else if (insertMode === 'list') {
                resolve({
                    path: `${rootPath}.query.edges`,
                    subPath: 'node',
                });
            }

            resolve({
                path: rootPath,
            });
        });

    private readonly getDocumentParameters = (): Promise<FilterParameter[]> => {
        const params =
            resolveByValue({
                propertyValue: this.props.fieldProperties.parameters,
                screenId: this.props.screenId,
                skipHexFormat: true,
                rowValue: null, // This is not a field context, so no row value can be present
            }) ?? [];
        return Promise.resolve(params);
    };

    private readonly onDisplayToast = (content: string, type: ToastProps['variant']): void => {
        showToast(content, { type });
    };

    private readonly contextProvider: DocumentContextProvider = {
        getObjectDetails: this.getObjectDetails,
        onObjectInsert: this.onObjectInsert,
        getDocumentParameters: this.getDocumentParameters,
    };

    render(): React.ReactNode {
        const classNames = ['e-form-designer-field'];

        if (this.props.fixedHeight) {
            classNames.push('e-form-designer-field-fixed-height');
        }

        return (
            <div
                {...this.getBaseAttributesDivWrapper(
                    'form-designer',
                    classNames.join(' '),
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
                style={{ height: this.props.fixedHeight }}
            >
                <FieldHeader
                    elementId={this.props.elementId}
                    screenId={this.props.screenId}
                    title={this.getTitle()}
                    isDisabled={this.isDisabled()}
                    isTitleHidden={this.props.fieldProperties.isTitleHidden}
                />
                <XtremDocumentEditor
                    carbonLocale={carbonLocale}
                    contextProvider={this.contextProvider}
                    footerValue={this.state.footerValue}
                    headerValue={this.state.headerValue}
                    isDisabled={this.isDisabled()}
                    locale={this.props.locale}
                    localize={localize}
                    localizeEnumMember={localizeEnumMember}
                    onChange={this.onChange}
                    onDisplayNotification={this.onDisplayToast}
                    onFooterChange={this.onFooterChange}
                    onHeaderChange={this.onHeaderChange}
                    onReady={this.onEditorReady}
                    paperOrientation={this.props.fieldProperties.paperOrientation || 'portrait'}
                    paperSize={this.props.fieldProperties.paperSize || 'a4'}
                    value={this.state.value || ''}
                    marginBottom={this.props.fieldProperties.marginBottom}
                    marginLeft={this.props.fieldProperties.marginLeft}
                    marginRight={this.props.fieldProperties.marginRight}
                    marginTop={this.props.fieldProperties.marginTop}
                />
                {this.props.fieldProperties.helperText && (
                    <HelperText helperText={this.props.fieldProperties.helperText} />
                )}
            </div>
        );
    }
}

export const ConnectedFormDesignerComponent = connect(mapStateToProps(), mapDispatchToProps())(FormDesignerComponent);

export default ConnectedFormDesignerComponent;
