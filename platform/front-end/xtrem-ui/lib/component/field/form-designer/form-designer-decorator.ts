/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FormDesignerControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { FormDesignerDecoratorProperties, FormDesignerExtensionDecoratorProperties } from './form-designer-types';

class FormDesignerDecorator extends AbstractFieldDecorator<FieldKey.FormDesigner> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = FormDesignerControlObject;
}

/**
 * Initializes the decorated member as a [FormDesigner]{@link FormDesignerControlObject} field with the provided properties
 *
 * @param properties The properties that the [FormDesigner]{@link FormDesignerControlObject} field will be initialized with
 */
export function formDesignerField<CT extends ScreenExtension<CT>>(
    properties: FormDesignerDecoratorProperties<Extend<CT>>,
): (target: CT, name: string) => void {
    return standardDecoratorImplementation<CT, FieldKey.FormDesigner>(
        properties,
        FormDesignerDecorator,
        FieldKey.FormDesigner,
        true,
    );
}

export function formDesignerFieldOverride<CT extends ScreenExtension<CT>>(
    properties: FormDesignerExtensionDecoratorProperties<CT>,
): (target: CT, name: string) => void {
    return standardExtensionDecoratorImplementation<CT, FieldKey.FormDesigner>(properties);
}
