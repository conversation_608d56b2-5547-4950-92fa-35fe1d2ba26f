PATH: XTREEM/UI+Field+Widgets/Form+Designer+Field

## Introduction

The form designer field is a rich text editor component which generates HTML content that can be printed using the xtrem-reporting service. It allows the users to browser the application's data model and insert collection and property placeholders to the document body. These placeholders are populated with data when the document is printed via the xtrem-reporting package.

## Example
```ts
@ui.decorators.formDesignerField<FormDesigner>({
    isDisabled: false,
    title: 'Document',
    helperText: 'Some helper text that goes under the field',
    parent() {
        return this.fieldBlock;
    },
})
field: ui.fields.FormDesigner;
```


## Decorator Properties

#### Display Properties

-   **helperText**: Specifies the helper text displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Specifies whether the field is editable or not. The difference between isDisabled and isReadOnly, is that isReadOnly suggests that the field is never editable, whereas isDisabled suggest that the field is currently not editable. It can also be defined as callback function that returns a boolean.
-   **isFullWidth**: Specifies whether the field should take the full width.
-   **isHelperTextHidden**: Specifies whether the field's helper text should be displayed or not.
-   **isHidden**: Specifies whether the component should be displayed or not.
-   **isTitleHidden**: Specifies whether the field's title should be displayed or not.
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **width**: Specifies the field's width. The width can be defined by using field size categories which are remapped to actually width values by the framework depending on the screen size and the container size that the field is in.
-   **paperSize**: Paper size of the editor view, defaults to A4.
-   **paperOrientation**: Paper orientation of the editor view, defaults to portrait.

#### Data Binding Properties

-   **bind**: Specifies the GraphQL object's property the field's value is bound ot. If not provided, the field's name is used.
-   **isTransient**: If marked true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

#### Event Handler Properties

-   **onChange()**: Triggered when the field's value changed and the focus is about to move away from the field. No arguments are provided.
-   **onClick()**: Triggered when the field is clicked. No arguments are provided.

#### Validation Properties

-   **isMandatory**: Specifies whether the field is mandatory or not. When enabled, empty values will raise an validation error message. It can also be defined as callback function that returns a boolean.
-   **validation()**: Custom validation callback with the new value provided as argument. If the function returns a non-empty string (or promise resolving to a non-empty string), the return value will be used as validation error message. If the function returns a falsy value, the field is considered to be valid.

#### Extension Properties

-   **insertBefore()**: Inserts the current field before the returned field, only for extension pages.

### Other Decorator Properties

#### Runtime Functions
-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeEditorCommand(commandName:string, ...args)**: Executes a CK editor command. The first argument is the the command name, the rest are the arguments of the command.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/FormDesigner).
