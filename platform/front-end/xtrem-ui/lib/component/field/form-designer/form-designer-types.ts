import type { FilterParameter } from '@sage/xtrem-ui-components';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { OverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { ValueOrCallback } from '../../../utils/types';
import type { BlockControlObject, SectionControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { BinaryValue, FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { Changeable, Clickable, ExtensionField, HasParent } from '../traits';
import type { PaperOrientation, PaperSize } from '@sage/xtrem-document-editor';

export interface FormDesignerDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends FormDesignerProperties<CT>,
        Changeable<CT>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>> {
    /** The parameters of the form editor */
    parameters?: ValueOrCallback<CT, FilterParameter[]>;

    /** Function to be executed when the editor is ready for interaction by the user */
    onReady?: (this: CT) => void;

    /** Triggered when the user changes the header content  */
    onHeaderValueChanged?: (this: CT, newHeaderValue: string) => void;

    /** Triggered when the user changes the footer content  */
    onFooterValueChanged?: (this: CT, newFooterValue: string) => void;
}

export interface FormDesignerExtensionDecoratorProperties<CT extends ScreenExtension<CT>>
    extends OverrideDecoratorProperties<FormDesignerDecoratorProperties<Extend<CT>>> {}

export interface FormDesignerProperties<CT extends ScreenExtension<CT> = ScreenBase>
    extends EditableFieldProperties<CT> {
    /** Paper size of the editor view, defaults to A4 */
    paperSize?: PaperSize;

    /** Paper orientation of the editor view, defaults to portrait */
    paperOrientation?: PaperOrientation;

    /** HTML content of the header */
    headerValue?: string;

    /** HTML content of the footer */
    footerValue?: string;

    /** Top margin in centimeters */
    marginTop?: number;

    /** Bottom margin in centimeters */
    marginBottom?: number;

    /** Right margin in centimeters */
    marginRight?: number;

    /** Left margin in centimeters */
    marginLeft?: number;
}

export type FormDesignerComponentProperties = BaseEditableComponentProperties<FormDesignerProperties, BinaryValue>;

export type FormDesignerComponentProps = BaseEditableComponentProperties<
    FormDesignerDecoratorProperties,
    BinaryValue,
    NestedFieldsAdditionalProperties & { isDirty?: boolean }
>;
