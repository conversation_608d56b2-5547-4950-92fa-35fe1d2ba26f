import type { FilterParameter } from '@sage/xtrem-ui-components';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { FormDesignerDecoratorProperties } from './form-designer-types';
import * as xtremRedux from '../../../redux';
import type { PaperOrientation, PaperSize } from '@sage/xtrem-document-editor';

export class FormDesignerControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.FormDesigner,
    FieldComponentProps<FieldKey.FormDesigner>
> {
    @ControlObjectProperty<FormDesignerDecoratorProperties<CT>, FormDesignerControlObject<CT>>()
    /** Document editor parameters */
    parameters: FilterParameter[];

    @ControlObjectProperty<FormDesignerDecoratorProperties<CT>, FormDesignerControlObject<CT>>()
    /** Paper orientation of the editor view, defaults to portrait */
    paperOrientation: PaperOrientation;

    @ControlObjectProperty<FormDesignerDecoratorProperties<CT>, FormDesignerControlObject<CT>>()
    /** Paper size of the editor view, defaults to A4 */
    paperSize: PaperSize;

    @ControlObjectProperty<FormDesignerDecoratorProperties<CT>, FormDesignerControlObject<CT>>()
    /** HTML content of the header */
    headerValue: string;

    @ControlObjectProperty<FormDesignerDecoratorProperties<CT>, FormDesignerControlObject<CT>>()
    /** HTML content of the footer */
    footerValue: string;

    @ControlObjectProperty<FormDesignerDecoratorProperties<CT>, FormDesignerControlObject<CT>>()
    /** Top margin in centimeters */
    marginTop: number;

    @ControlObjectProperty<FormDesignerDecoratorProperties<CT>, FormDesignerControlObject<CT>>()
    /** Bottom margin in centimeters */
    marginBottom: number;

    @ControlObjectProperty<FormDesignerDecoratorProperties<CT>, FormDesignerControlObject<CT>>()
    /** Right margin in centimeters */
    marginRight: number;

    @ControlObjectProperty<FormDesignerDecoratorProperties<CT>, FormDesignerControlObject<CT>>()
    /** Left margin in centimeters */
    marginLeft: number;

    executeEditorCommand(command: string, ...args: any[]): void {
        xtremRedux.getStore().dispatch({
            type: xtremRedux.ActionType.ExecuteFormDesignCommand,
            value: {
                elementId: this.elementId,
                screenId: this.screenId,
                command,
                args,
            },
        });
    }
}
