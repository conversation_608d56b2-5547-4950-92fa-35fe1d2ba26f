const executeMock = jest.fn();
const XtremDocumentEditor = jest.fn<JSX.Element, any>((props: any) => {
    props.onReady({ execute: executeMock });
    return <div id="mockXtremActionMock" data-testid="mockXtremActionMock" />;
});
jest.mock('@sage/xtrem-document-editor', () => ({ XtremDocumentEditor }));

import {
    addFieldToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';

import * as React from 'react';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type * as xtremRedux from '../../../../redux';
import type { FormDesignerDecoratorProperties } from '../form-designer-types';
import { cleanup, render } from '@testing-library/react';
import { Provider } from 'react-redux';
import ConnectedFormDesignerComponent from '../form-designer-component';
import { FieldKey } from '@sage/xtrem-shared';
import type { ScreenBase } from '../../../../service/screen-base';

describe('form designer component', () => {
    const screenId = 'TestPage';
    const elementId = 'testField';

    let state: xtremRedux.XtremAppState;
    let store: MockStoreEnhanced<xtremRedux.XtremAppState>;

    let fieldProperties: FormDesignerDecoratorProperties<ScreenBase>;

    beforeEach(() => {
        executeMock.mockReset();
        fieldProperties = {
            title: 'test title',
            onChange: jest.fn(),
            onClick: jest.fn(),
            onError: jest.fn(),
        };

        state = getMockState();

        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
    });

    afterEach(() => {
        applyActionMocks();
        cleanup();
    });

    const getConnectedField = store => (
        <Provider store={store}>
            <ConnectedFormDesignerComponent screenId={screenId} elementId={elementId} />
        </Provider>
    );

    it('should render with a title', () => {
        fieldProperties.title = 'Test title';
        addFieldToState(FieldKey.FormDesigner, state, screenId, elementId, fieldProperties, null);
        store = getMockStore(state);
        const { queryByTestId } = render(getConnectedField(store));

        expect(queryByTestId('e-field-label')).toHaveTextContent('Test title');
    });
});
