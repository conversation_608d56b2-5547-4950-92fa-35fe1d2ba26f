import { getMockStore } from '../../../../__tests__/test-helpers';

import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import { FormDesignerControlObject } from '../../../control-objects';
import type { FieldKey } from '../../../types';
import type { FormDesignerProperties } from '../form-designer-types';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { ActionType, type XtremAppState } from '../../../../redux';

describe('form designer control object', () => {
    let formDesignerControlObject: FormDesignerControlObject;
    let formDesignerProperties: FormDesignerProperties;
    let stringValue: string;
    let mockStore: MockStoreEnhanced<XtremAppState>;

    beforeEach(() => {
        mockStore = getMockStore();
        formDesignerProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
        };
        stringValue = 'TEST_VALUE';
        formDesignerControlObject = buildControlObject<FieldKey.FormDesigner>(FormDesignerControlObject, {
            fieldValue: { value: stringValue },
            fieldProperties: formDesignerProperties,
        });
    });

    it('getting field value', () => {
        expect(formDesignerControlObject.value).toEqual({ value: stringValue });
    });

    it('should set the title', () => {
        const newValue = 'Test form designer field title';
        expect(formDesignerProperties.title).not.toEqual(newValue);
        formDesignerControlObject.title = newValue;
        expect(formDesignerProperties.title).toEqual(newValue);
    });

    it('should dispatch a redux action when a command is executed', () => {
        expect(mockStore.getActions()).toEqual([]);
        formDesignerControlObject.executeEditorCommand('bold', ['some', 'args']);
        expect(mockStore.getActions()).toEqual([
            {
                type: ActionType.ExecuteFormDesignCommand,
                value: {
                    args: [['some', 'args']],
                    command: 'bold',
                    elementId: 'fieldName',
                    screenId: 'TestPage',
                },
            },
        ]);
    });
});
