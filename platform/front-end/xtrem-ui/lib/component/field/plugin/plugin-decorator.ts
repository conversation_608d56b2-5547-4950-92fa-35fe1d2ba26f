/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { PluginControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { PluginDecoratorProperties } from './plugin-types';

class PluginDecorator extends AbstractFieldDecorator<FieldKey.Plugin> {
    protected _controlObjectConstructor = PluginControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

export function pluginField<T extends ScreenExtension<T>, PluginCustomDecoratorProperties extends {} = {}>(
    properties: PluginDecoratorProperties<Extend<T>> & PluginCustomDecoratorProperties,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Plugin>(properties, PluginDecorator, FieldKey.Plugin);
}

export function pluginFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<PluginDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Plugin>(properties);
}
