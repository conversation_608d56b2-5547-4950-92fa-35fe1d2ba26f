import * as React from 'react';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { PluginComponentFieldProps } from './plugin-types';

const ConnectedPluginComponent = React.lazy(() => import('./plugin-component'));

export function AsyncConnectedPluginComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="200px" />}>
            <ConnectedPluginComponent {...props} />
        </React.Suspense>
    );
}

const PluginComponent = React.lazy(() => import('./plugin-component').then(c => ({ default: c.PluginComponent })));

export function AsyncPluginComponent(props: PluginComponentFieldProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={true} bodyHeight="200px" />}>
            <PluginComponent {...props} />
        </React.Suspense>
    );
}
