import { camelCase } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../../redux';
import { errorDialog } from '../../../service/dialog-service';
import { executeGraphqlQuery } from '../../../service/graphql-utils';
import { showToast } from '../../../service/toast-service';
import type { PageDefinition } from '../../../service/page-definition';
import type { XtremUiPluginComponentProps } from '../../../service/plugin-service';
import type { IRouter } from '../../../service/router';
import { Router } from '../../../service/router';
import { notifyConsumerOnError } from '../../../service/telemetry-service';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { EditableFieldComponentProperties, FieldComponentExternalProperties } from '../field-base-component-types';
import type { PluginDecoratorProperties, PluginAdditionalProps, PluginComponentFieldProps } from './plugin-types';

export class PluginComponent extends EditableFieldBaseComponent<
    any,
    PluginDecoratorProperties,
    any,
    PluginAdditionalProps
> {
    private readonly router: IRouter;

    constructor(props: PluginComponentFieldProps) {
        super(props);
        this.router = new Router(this.props.screenId);
    }

    setFieldValue = (value: any): void => {
        if (this.props.value !== value) {
            this.handleChange(
                this.props.elementId,
                value,
                this.props.setFieldValue,
                this.props.validate,
                this.triggerChangeListener,
            );
        }
    };

    setFieldProperties = (propertyName: string, propertyValue: any): void => {
        if (this.props.setFieldProperties) {
            this.props.setFieldProperties(this.props.elementId, {
                ...this.props.fieldProperties,
                [propertyName]: propertyValue,
            });
        }
    };

    componentDidCatch(error: Error): void {
        errorDialog(this.props.screenId, 'Plugin error', error);
        notifyConsumerOnError(error);
    }

    render(): React.ReactNode {
        const pluginImplementation = this.props.pluginImplementation;
        const Component: React.ComponentType<XtremUiPluginComponentProps> = pluginImplementation.component;
        const hasTitle = this.getTitle() && !this.props.fieldProperties.isTitleHidden;
        const hasHelperText = this.props.fieldProperties.helperText && !this.props.fieldProperties.isHelperTextHidden;
        let fixedHeight: number | undefined = this.props.fixedHeight;

        if (fixedHeight && hasTitle) {
            fixedHeight -= 20;
        }

        if (fixedHeight && hasHelperText) {
            fixedHeight -= 16;
        }

        return (
            <div
                ref={this.componentRef}
                {...this.getBaseAttributesDivWrapper(
                    'plugin',
                    `e-plugin-field e-plugin-field-type-${camelCase(pluginImplementation.name)}`,
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
            >
                {hasTitle && <FieldLabel label={this.getTitle()} />}
                <Component
                    browser={this.props.browser}
                    elementId={this.props.elementId}
                    executeQuery={(query: string): any => executeGraphqlQuery({ query })}
                    fieldProperties={this.props.fieldProperties}
                    fixedHeight={fixedHeight}
                    locale={this.props.locale}
                    queryParameters={this.props.queryParameters}
                    router={this.router}
                    screenId={this.props.screenId}
                    setFieldProperty={this.setFieldProperties}
                    setFieldValue={this.setFieldValue}
                    showToast={showToast}
                    username={this.props.username}
                    value={this.props.value || null}
                />
                {hasHelperText && <HelperText helperText={this.props.fieldProperties.helperText} />}
            </div>
        );
    }
}

const extendedMapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: FieldComponentExternalProperties,
): PluginComponentFieldProps => {
    const componentProperties = mapStateToProps()(state, props) as EditableFieldComponentProperties<
        PluginDecoratorProperties,
        any
    >;

    const screenDefinition = state.screenDefinitions[props.screenId] as PageDefinition;

    return {
        ...props,
        ...componentProperties,
        username: state.applicationContext!.login!,
        pluginImplementation: state.plugins[componentProperties.fieldProperties.pluginPackage],
        queryParameters: screenDefinition.queryParameters,
    };
};

export const ConnectedPluginComponent = connect(extendedMapStateToProps, mapDispatchToProps())(PluginComponent);

export default ConnectedPluginComponent;
