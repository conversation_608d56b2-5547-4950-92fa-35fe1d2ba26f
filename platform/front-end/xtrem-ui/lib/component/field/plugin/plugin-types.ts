import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type { FieldControlObjectInstance } from '../../types';
import type { BlockControlObject, SectionControlObject } from '../../control-objects';
import type { Changeable, Clickable, ExtensionField, HasParent } from '../traits';
import type { ScreenBase } from '../../../service/screen-base';
import type { LocalizeLocale } from '@sage/xtrem-shared';
import type { XtremUiPlugin, ReduxResponsive } from '../../../plugin';
import type { QueryParameters } from '../../../utils/types';

export interface PluginDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<PluginProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        Changeable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>> {}

export interface PluginProperties<CT extends ScreenBase = ScreenBase> extends EditableFieldProperties<CT> {
    pluginPackage: string;
}

export interface PluginAdditionalProps {
    locale: LocalizeLocale;
    username: string;
    pluginImplementation: XtremUiPlugin;
    queryParameters: QueryParameters;
    browser?: ReduxResponsive;
}

export type PluginComponentFieldProps = BaseEditableComponentProperties<
    PluginDecoratorProperties,
    any,
    PluginAdditionalProps
>;
