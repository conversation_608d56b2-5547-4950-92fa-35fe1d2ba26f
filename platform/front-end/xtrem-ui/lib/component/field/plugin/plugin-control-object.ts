/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';

const blacklistedDecoratorProperties = ['bind', 'parent', 'pluginPackage', 'validate'];

/**
 * [Field]{@link EditableFieldControlObject} that holds a boolean value
 */
export class PluginControlObject<
    PluginCustomDecoratorProperties extends {} = {},
    CT extends ScreenExtension<CT> = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.Plugin, FieldComponentProps<FieldKey.Plugin>> {
    setProperty<K extends keyof PluginCustomDecoratorProperties>(
        property: keyof PluginCustomDecoratorProperties,
        newValue: PluginCustomDecoratorProperties[K],
    ): void {
        if (blacklistedDecoratorProperties.includes(String(property))) {
            throw new Error(`${String(property)} cannot be updated on runtime.`);
        }

        this.setUiComponentProperties(property as any, newValue);
    }

    getProperty<K extends keyof PluginCustomDecoratorProperties>(
        property: keyof PluginCustomDecoratorProperties,
    ): PluginCustomDecoratorProperties[K] {
        return this.getUiComponentProperty(String(property) as any);
    }
}
