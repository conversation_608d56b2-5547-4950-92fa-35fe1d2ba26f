import type { <PERSON><PERSON><PERSON> } from '../../../types';
import { PluginControlObject } from '../../../control-objects';
import type { PluginProperties } from '../plugin-types';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';

interface CustomDecoratorProperties {
    aNumericProperty: number;
    aStringProperty: string;
}

describe('Plugin control object', () => {
    let pluginControlObject: PluginControlObject<CustomDecoratorProperties>;
    let pluginProperties: PluginProperties & CustomDecoratorProperties;
    let pluginValue: string;

    beforeEach(() => {
        pluginProperties = {
            title: 'TEST_FIELD_TITLE',
            pluginPackage: '@sage/myPluginPackage',
            aNumericProperty: 1234,
            aStringProperty: 'test',
        };
        pluginValue = 'value';

        pluginControlObject = buildControlObject<FieldKey.Plugin>(PluginControlObject, {
            fieldValue: pluginValue,
            fieldProperties: pluginProperties,
        }) as PluginControlObject<CustomDecoratorProperties>;
    });

    it('getting field value', () => {
        expect(pluginControlObject.value).toEqual(pluginValue);
    });

    it('should return a number string', () => {
        expect(typeof pluginControlObject.value).toBe('string');
    });

    it('should set the title', () => {
        const testFixture = 'Test Plugin Field Title';
        expect(pluginProperties.title).not.toEqual(testFixture);
        pluginControlObject.title = testFixture;
        expect(pluginProperties.title).toEqual(testFixture);
    });

    it('should set custom numeric decorator property', () => {
        expect(pluginProperties.aNumericProperty).toEqual(1234);
        pluginControlObject.setProperty('aNumericProperty', 5432);
        expect(pluginProperties.aNumericProperty).toEqual(5432);
        expect(pluginControlObject.getProperty('aNumericProperty')).toEqual(5432);
    });

    it('should set custom string decorator property', () => {
        expect(pluginProperties.aStringProperty).toEqual('test');
        pluginControlObject.setProperty('aStringProperty', 'another test');
        expect(pluginProperties.aStringProperty).toEqual('another test');
        expect(pluginControlObject.getProperty('aStringProperty')).toEqual('another test');
    });

    it('should throw an exception when tries to update a forbidden property on runtime', () => {
        expect(() => pluginControlObject.setProperty('pluginPackage' as any, 'some random package')).toThrow(
            'pluginPackage cannot be updated on runtime.',
        );
        expect(() => pluginControlObject.setProperty('parent' as any, 'some random package')).toThrow(
            'parent cannot be updated on runtime.',
        );
        expect(() => pluginControlObject.setProperty('bind' as any, 'some random package')).toThrow(
            'bind cannot be updated on runtime.',
        );
    });
});
