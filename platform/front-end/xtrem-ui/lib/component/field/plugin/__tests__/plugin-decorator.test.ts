import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { PluginDecoratorProperties } from '../plugin-types';
import { pluginField } from '../plugin-decorator';

interface CustomDecoratorProperties {
    aNumericProperty: number;
    aStringProperty: string;
}

describe('Plugin decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;
    let onClickMock: jest.Mock;
    let onChangeMock: jest.Mock;

    beforeEach(() => {
        fieldId = 'pluginField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
        onClickMock = jest.fn();
        onChangeMock = jest.fn();
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should set default values when no component properties provided', () => {
            pluginField<Page, CustomDecoratorProperties>({
                pluginPackage: '@sage/myPluginPackage',
                aNumericProperty: 1234,
                aStringProperty: 'someString',
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});

            const mappedComponentProperties: PluginDecoratorProperties<Page> = pageMetadata.uiComponentProperties[
                fieldId
            ] as PluginDecoratorProperties<Page>;

            expect(mappedComponentProperties.onClick).toBeUndefined();
            expect(mappedComponentProperties.onChange).toBeUndefined();
        });

        it('should set values when component properties provided', () => {
            pluginField<Page, CustomDecoratorProperties>({
                pluginPackage: '@sage/myPluginPackage',
                aNumericProperty: 1234,
                aStringProperty: 'someString',
                onClick: onClickMock,
                onChange: onChangeMock,
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});

            const mappedComponentProperties: PluginDecoratorProperties<Page> & CustomDecoratorProperties = pageMetadata
                .uiComponentProperties[fieldId] as PluginDecoratorProperties<Page> & CustomDecoratorProperties;
            expect(mappedComponentProperties.pluginPackage).toBe('@sage/myPluginPackage');
            expect(mappedComponentProperties.aNumericProperty).toBe(1234);
            expect(mappedComponentProperties.aStringProperty).toBe('someString');
            expect(mappedComponentProperties.onClick).toBe(onClickMock);
            expect(mappedComponentProperties.onChange).toBe(onChangeMock);
            expect(onClickMock).not.toHaveBeenCalled();
            expect(onChangeMock).not.toHaveBeenCalled();
        });

        it('should assign onClick handler', () => {
            testOnClickHandler(pluginField, pageMetadata, fieldId);
        });
    });
});
