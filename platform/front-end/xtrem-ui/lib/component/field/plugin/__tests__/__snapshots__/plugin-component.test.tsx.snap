// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Plugin component connected Snapshots should render disabled 1`] = `
<div>
  <div
    class="e-field e-plugin-field e-plugin-field-type-myTestPlugin e-disabled"
    data-label="Test Plugin Field Title"
    data-testid="e-plugin-field e-field-label-testPluginFieldTitle e-field-bind-test-plugin-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Plugin Field Title
    </label>
    <span
      id="fake-plugin"
    >
      <span
        id="fake-plugin-value"
      >
        value
      </span>
    </span>
  </div>
</div>
`;

exports[`Plugin component connected Snapshots should render helperText 1`] = `
<div>
  <div
    class="e-field e-plugin-field e-plugin-field-type-myTestPlugin"
    data-label="Test Plugin Field Title"
    data-testid="e-plugin-field e-field-label-testPluginFieldTitle e-field-bind-test-plugin-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Plugin Field Title
    </label>
    <span
      id="fake-plugin"
    >
      <span
        id="fake-plugin-value"
      >
        value
      </span>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
      This is a helper text
    </span>
  </div>
</div>
`;

exports[`Plugin component connected Snapshots should render hidden 1`] = `
<div>
  <div
    class="e-field e-plugin-field e-plugin-field-type-myTestPlugin e-hidden"
    data-label="Test Plugin Field Title"
    data-testid="e-plugin-field e-field-label-testPluginFieldTitle e-field-bind-test-plugin-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Plugin Field Title
    </label>
    <span
      id="fake-plugin"
    >
      <span
        id="fake-plugin-value"
      >
        value
      </span>
    </span>
  </div>
</div>
`;

exports[`Plugin component connected Snapshots should render in read only mode 1`] = `
<div>
  <div
    class="e-field e-plugin-field e-plugin-field-type-myTestPlugin e-read-only"
    data-label="Test Plugin Field Title"
    data-testid="e-plugin-field e-field-label-testPluginFieldTitle e-field-bind-test-plugin-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Plugin Field Title
    </label>
    <span
      id="fake-plugin"
    >
      <span
        id="fake-plugin-value"
      >
        value
      </span>
    </span>
  </div>
</div>
`;

exports[`Plugin component connected Snapshots should render with full-width 1`] = `
<div>
  <div
    class="e-field e-plugin-field e-plugin-field-type-myTestPlugin e-full-width"
    data-label="Test Plugin Field Title"
    data-testid="e-plugin-field e-field-label-testPluginFieldTitle e-field-bind-test-plugin-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Plugin Field Title
    </label>
    <span
      id="fake-plugin"
    >
      <span
        id="fake-plugin-value"
      >
        value
      </span>
    </span>
  </div>
</div>
`;
