import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremUiPluginComponentProps } from '../../../../plugin';
import type * as xtremRedux from '../../../../redux';
import type { PluginProperties } from '../../../control-objects';
import { FieldKey } from '../../../types';
import { ConnectedPluginComponent } from '../plugin-component';
import { render } from '@testing-library/react';

jest.mock('../../../../service/dialog-service', () => ({ errorDialog: jest.fn() }));

describe('Plugin component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: PluginProperties;

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test Plugin Field Title',
            pluginPackage: '@sage/myPluginPackage',
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            const state = getMockState({
                plugins: {
                    '@sage/myPluginPackage': {
                        name: 'myTestPlugin',
                        component: (props: XtremUiPluginComponentProps) => (
                            <span id="fake-plugin">
                                <span id="fake-plugin-value">{props.value}</span>
                            </span>
                        ),
                        transformFromGraphValue: jest.fn(() => 'myMockedValue'),
                    },
                },
            });
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Plugin, state, screenId, 'test-plugin-field', mockFieldProperties, 'value');
            addFieldToState(FieldKey.Plugin, state, screenId, 'test-empty-plugin-field', mockFieldProperties, null);
            addFieldToState(
                FieldKey.Plugin,
                state,
                screenId,
                'broken-plugin-field',
                {
                    pluginPackage: '@sage/brokenPlugin',
                },
                null,
            );
            mockStore = getMockStore(state);
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedPluginComponent screenId={screenId} elementId="test-plugin-field" />
                    </Provider>,
                );

                expect(container.querySelector('#fake-plugin-value')).toHaveTextContent('value');
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedPluginComponent screenId={screenId} elementId="test-plugin-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedPluginComponent screenId={screenId} elementId="test-plugin-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with full-width', () => {
                mockFieldProperties.isFullWidth = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedPluginComponent screenId={screenId} elementId="test-plugin-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render in read only mode', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedPluginComponent screenId={screenId} elementId="test-plugin-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedPluginComponent screenId={screenId} elementId="test-plugin-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render without value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedPluginComponent screenId={screenId} elementId="test-empty-plugin-field" />
                    </Provider>,
                );

                expect(container.querySelector('#fake-plugin-value')).toHaveTextContent('');
            });
        });
    });
});
