import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { SeparatorDecoratorProperties } from '../separator-types';
import { separatorField } from '../separator-decorator';

describe('Separator decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;
    let onClickMock: jest.Mock;
    let onChangeMock: jest.Mock;

    beforeEach(() => {
        fieldId = 'separatorField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
        onClickMock = jest.fn();
        onChangeMock = jest.fn();
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should set default values when no component properties provided', () => {
            separatorField({})({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});

            const mappedComponentProperties: SeparatorDecoratorProperties<Page> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.isDisabled).toBeFalsy();
            expect(mappedComponentProperties.isFullWidth).toBeFalsy();
            expect(mappedComponentProperties.isHidden).toBeFalsy();
            expect(mappedComponentProperties.isInvisible).toBeFalsy();
            expect(mappedComponentProperties.onClick).toBeUndefined();
        });

        it('should set values when component properties provided', () => {
            separatorField({
                isDisabled: true,
                isFullWidth: true,
                isHidden: true,
                isInvisible: true,
                onClick: onClickMock,
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});

            const mappedComponentProperties: SeparatorDecoratorProperties<Page> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.isDisabled).toBeTruthy();
            expect(mappedComponentProperties.isFullWidth).toBeTruthy();
            expect(mappedComponentProperties.isHidden).toBeTruthy();
            expect(mappedComponentProperties.isInvisible).toBeTruthy();
            expect(mappedComponentProperties.onClick).toBe(onClickMock);
            expect(onClickMock).not.toHaveBeenCalled();
            expect(onChangeMock).not.toHaveBeenCalled();
        });

        it('should assign onClick handler', () => {
            testOnClickHandler(separatorField, pageMetadata, fieldId);
        });
    });
});
