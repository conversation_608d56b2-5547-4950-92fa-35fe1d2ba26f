import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { FieldKey } from '../../../types';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import type * as xtremRedux from '../../../../redux';
import * as events from '../../../../utils/events';
import { ConnectedSeparatorComponent } from '../separator-component';
import type { SeparatorProperties } from '../separator-types';
import { fireEvent, render } from '@testing-library/react';

const screenId = 'SeparatorTestPage';
const fieldId = 'test-separator-field';
let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;
let mockFieldProperties: SeparatorProperties = {};
let separatorComponent: React.ReactElement;

describe('Separator component', () => {
    const renderComponent = () => {
        return render(separatorComponent);
    };
    describe('connected', () => {
        beforeEach(() => {
            mockFieldProperties = {};
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Separator, state, screenId, fieldId, mockFieldProperties);

            mockStore = getMockStore(state);
            separatorComponent = (
                <Provider store={mockStore}>
                    <ConnectedSeparatorComponent screenId={screenId} elementId={fieldId} />
                </Provider>
            );
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        describe('Snapshots', () => {
            const matchSnapshot = () => {
                const { container } = renderComponent();
                expect(container).toMatchSnapshot();
            };
            it('should render with default properties', () => {
                matchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                matchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                matchSnapshot();
            });

            it('should render horizontally positioned', () => {
                mockFieldProperties.isFullWidth = true;
                matchSnapshot();
            });
            it('should render horizontally positioned', () => {
                mockFieldProperties.isInvisible = true;
                matchSnapshot();
            });
        });

        describe('Interactions', () => {
            it('should trigger the triggerAction redux action on click', () => {
                const { queryByTestId } = renderComponent();

                expect(events.triggerFieldEvent).not.toHaveBeenCalled();

                fireEvent.click(queryByTestId('e-separator-field', { exact: false })!);

                expect(events.triggerFieldEvent).toHaveBeenCalledTimes(1);
                expect(events.triggerFieldEvent).toHaveBeenCalledWith(screenId, fieldId, 'onClick');
            });

            it('should not trigger the triggerAction redux action on click when isDisabled is true', () => {
                mockFieldProperties.isDisabled = true;
                const { queryByTestId } = renderComponent();

                expect(events.triggerFieldEvent).not.toHaveBeenCalled();

                fireEvent.click(queryByTestId('e-separator-field', { exact: false })!);

                expect(events.triggerFieldEvent).not.toHaveBeenCalled();
            });
        });
    });
});
