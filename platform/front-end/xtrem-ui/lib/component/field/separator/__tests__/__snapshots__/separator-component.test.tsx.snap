// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Separator component connected Snapshots should render disabled 1`] = `
<div>
  <div
    class="e-field e-separator-field"
    data-testid="e-separator-field e-field-bind-test-separator-field"
  >
    <div
      class="e-separator-field-vertical"
    />
  </div>
</div>
`;

exports[`Separator component connected Snapshots should render hidden 1`] = `
<div>
  <div
    class="e-field e-separator-field e-hidden"
    data-testid="e-separator-field e-field-bind-test-separator-field"
  >
    <div
      class="e-separator-field-vertical"
    />
  </div>
</div>
`;

exports[`Separator component connected Snapshots should render horizontally positioned 1`] = `
<div>
  <div
    class="e-field e-separator-field e-separator-field-horizontal"
    data-testid="e-separator-field e-field-bind-test-separator-field"
  />
</div>
`;

exports[`Separator component connected Snapshots should render horizontally positioned 2`] = `
<div>
  <div
    class="e-field e-separator-field e-separator-field-invisible"
    data-testid="e-separator-field e-field-bind-test-separator-field"
  >
    <div
      class="e-separator-field-vertical"
    />
  </div>
</div>
`;

exports[`Separator component connected Snapshots should render with default properties 1`] = `
<div>
  <div
    class="e-field e-separator-field"
    data-testid="e-separator-field e-field-bind-test-separator-field"
  >
    <div
      class="e-separator-field-vertical"
    />
  </div>
</div>
`;
