import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { BaseControlObject } from '../../base-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldControlObjectConstructorProps } from '../../types';
import { FieldKey } from '../../types';
import type { SeparatorProperties } from './separator-types';

export class SeparatorControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends BaseControlObject<
    CT,
    SeparatorProperties
> {
    public layout: FieldControlObjectConstructorProps<FieldKey.Separator>['layout'];

    public parent?: FieldControlObjectConstructorProps<FieldKey.Separator>['parent'];

    public insertBefore?: FieldControlObjectConstructorProps<FieldKey.Separator>['insertBefore'];

    public insertAfter?: FieldControlObjectConstructorProps<FieldKey.Separator>['insertAfter'];

    static defaultUiProperties: Partial<SeparatorProperties> = {
        ...BaseControlObject.defaultUiProperties,
        isFullWidth: false,
        isHidden: false,
    };

    constructor({
        screenId,
        elementId,
        getUiComponentProperties,
        setUiComponentProperties,
        layout,
        parent,
    }: FieldControlObjectConstructorProps<FieldKey.Separator>) {
        super(screenId, elementId, getUiComponentProperties, setUiComponentProperties, FieldKey.Separator);
        this.layout = layout;
        this.parent = parent;
    }

    @ControlObjectProperty<SeparatorProperties<CT>, SeparatorControlObject<CT>>()
    /** Whether the HTML element is visible or not */
    isHidden?: boolean;

    @ControlObjectProperty<SeparatorProperties<CT>, SeparatorControlObject<CT>>()
    /** It makes the separator element invisible. The separator still preserves its space in the layout, but the line is not displayed. */
    isInvisible?: boolean;
}
