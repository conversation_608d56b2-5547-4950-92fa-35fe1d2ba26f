import React from 'react';
import { connect } from 'react-redux';
import { getDataTestIdAttribute } from '../../../utils/dom';
import { triggerFieldEvent } from '../../../utils/events';
import { isFieldDisabled } from '../carbon-helpers';
import { mapReadonlyStateToProps } from '../field-base-component';
import type { BaseReadonlyComponentProperties } from '../field-base-component-types';
import type { SeparatorDecoratorProperties } from './separator-types';

export function SeparatorComponent(
    props: BaseReadonlyComponentProperties<SeparatorDecoratorProperties, void>,
): React.ReactElement {
    const onClick = (): void => {
        if (!isFieldDisabled(props.screenId, props.fieldProperties, props.value, null)) {
            triggerFieldEvent(props.screenId, props.elementId, 'onClick');
        }
    };

    const classNames = ['e-field e-separator-field'];
    if (props.fieldProperties.isFullWidth) {
        classNames.push('e-separator-field-horizontal');
    }

    if (props.fieldProperties.isInvisible) {
        classNames.push('e-separator-field-invisible');
    }
    if (props.fieldProperties.isHidden) {
        classNames.push('e-hidden');
    }

    return (
        <div
            data-testid={getDataTestIdAttribute('separator', undefined, props.elementId)}
            className={classNames.join(' ')}
            onClick={onClick}
        >
            {!props.fieldProperties.isFullWidth && <div className="e-separator-field-vertical" />}
        </div>
    );
}

export const ConnectedSeparatorComponent = connect(mapReadonlyStateToProps())(SeparatorComponent);

export default ConnectedSeparatorComponent;
