PATH: XTREEM/UI+Field+Widgets/Separator+Field

## Introduction

Separator fields are simple component that will provide the visual effect of separating other fields, either vertically or horizontally.

## Example:

```ts
@ui.decorators.separatorField<SelectField>({
        parent() {
            return this.eventsBlock;
        },
        isHidden: true,
        isFullWidth: true,
        isDisabled: true,
        onClick() {
            console.log('onClick event has been triggered in the separator field.');
        },
    })
    separatorField: ui.fields.Separator;
```

### Display decorator properties:

-   **isDisabled**: Whether the separator is able to trigger onClick decorator property (isDisabled = false) or not (isDisabled = true). It can also be defined as callback function that returns a boolean. A separator is enabled by default.
-   **isHidden**: Whether the separator is visible (isHidden = false) or not (isHidden = true). A separator is visible by default.
-   **isFullWidth**: Whether the separator should take the full width (isFullWidth = true) or not (isFullWidth = false). Besides that, when in full width, the separator will be displayed horizontally. Otherwise, it will be displayed in a vertical position. A separator is not full width by default.
-   **isInvisible**: It makes the separator element invisible. The separator still preserves its space in the layout, but the line is not displayed.

### Event handler decorator properties:

-   **onClick**: Event triggered when any parts of the separator is clicked, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Text).
