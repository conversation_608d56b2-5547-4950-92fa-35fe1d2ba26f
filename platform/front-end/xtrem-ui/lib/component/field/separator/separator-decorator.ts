import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { SeparatorControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { SeparatorDecoratorProperties } from './separator-types';

class SeparatorDecorator extends AbstractFieldDecorator<FieldKey.Separator> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = SeparatorControlObject;
}

/**
 * Initializes the decorated member as a [Separator]{@link SeparatorControlObject} field with the provided properties
 *
 * @param properties The properties that the [Separator]{@link SeparatorControlObject} field will be initialized with
 */
export function separatorField<T extends ScreenExtension<T>>(
    properties: SeparatorDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Separator>(properties, SeparatorDecorator, FieldKey.Separator);
}

export function separatorFieldOverride<T extends ScreenExtension<T>>(
    properties: ClickableOverrideDecoratorProperties<SeparatorDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Separator>(properties);
}
