import type { BaseReadonlyComponentProperties } from '../field-base-component-types';
import type { ValueOrCallbackWithFieldValue } from '../../../utils/types';
import type { ComponentProperties } from '../../base-control-object';
import type { ScreenBase } from '../../../service/screen-base';
import type { FieldControlObjectInstance } from '../../types';
import type { BlockControlObject } from '../../control-objects';
import type { Clickable, ExtensionField, HasParent } from '../traits';

export interface SeparatorProperties<CT extends ScreenBase = ScreenBase> extends ComponentProperties<CT> {
    /** Whether the field should span all the parent width */
    isFullWidth?: boolean;
    /** Whether the HTML element is hidden or not. Defaults to false */
    isHidden?: ValueOrCallbackWithFieldValue<CT, boolean>;

    /** It makes the separator element invisible. The separator still preserves its space in the layout, but the line is not displayed. */
    isInvisible?: boolean;
}

export interface SeparatorDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<SeparatorProperties, '_controlObjectType'>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>> {}

export type SeparatorComponentProperties = BaseReadonlyComponentProperties<SeparatorDecoratorProperties, void>;
