## Documentation - Separator
| name         | optional | description                                                                                                                                                                                                                                    | type                                                                                                   | default                                                                                                                                              |
| ------------ | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------- |
| access       | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">AccessConfiguration</pre>                                                       | <pre lang="javascript">{&nbsp;node:&nbsp;'<node>',&nbsp;bind:&nbsp;'<bind>'&nbsp;}</pre>                                                             |
| insertBefore | true     | The field before this extension field is inserted                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;T</pre>                                            | <pre lang="javascript">undefined</pre>                                                                                                               |
| isDisabled   | true     | Whether the HTML element is disabled or not. Defaults to false The difference with readOnly is that disabled suggests that the field is not editable for some validation reason (e.g. a button which can't be clicked due to validation errors | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre> | <pre lang="javascript">false</pre>                                                                                                                   |
| isFullWidth  | true     | Whether the field should span all the parent width                                                                                                                                                                                             | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                                                                                   |
| isHidden     | true     | Whether the HTML element is hidden or not. Defaults to false                                                                                                                                                                                   | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre> | <pre lang="javascript">false</pre>                                                                                                                   |
| isInvisible  | true     | It makes the separator element invisible. The separator still preserves its space in the layout, but the line is not displayed                                                                                                                 | <pre lang="javascript">boolean</pre>                                                                   | <pre lang="javascript">false</pre>                                                                                                                   |
| onClick      | true     | Function to be executed when the field is clicked                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                         | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                                                                                          |
| onError      | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">ErrorHandlerFunction<CT></pre>                                                  | <pre lang="javascript">function(error,screenId,elementId<br>)&nbsp;{<br>console.error({&nbsp;error,&nbsp;screenId,&nbsp;elementId&nbsp;})<br>}</pre> |
| parent       | true     | The container in which this component will render                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;P</pre>                                            | <pre lang="javascript">undefined</pre>                                                                                                               |