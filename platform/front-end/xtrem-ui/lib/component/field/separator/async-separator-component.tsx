import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import type { SeparatorComponentProperties } from './separator-types';

const ConnectedSeparatorComponent = React.lazy(() => import('./separator-component'));

export function AsyncConnectedSeparatorComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedSeparatorComponent {...props} />
        </React.Suspense>
    );
}

const SeparatorComponent = React.lazy(() =>
    import('./separator-component').then(e => ({ default: e.SeparatorComponent })),
);

export function AsyncSeparatorComponent(props: SeparatorComponentProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={false} bodyHeight="200px" />}>
            <SeparatorComponent {...props} />
        </React.Suspense>
    );
}
