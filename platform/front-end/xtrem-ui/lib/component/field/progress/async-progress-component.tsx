import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { ProgressComponentProps } from './progress-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedProgressComponent = React.lazy(() => import('./progress-component'));

export function AsyncConnectedProgressComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedProgressComponent {...props} />
        </React.Suspense>
    );
}

const ProgressComponent = React.lazy(() =>
    import('./progress-component').then(c => ({ default: c.ProgressComponent })),
);

export function AsyncProgressComponent(props: ProgressComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <ProgressComponent {...props} />
        </React.Suspense>
    );
}
