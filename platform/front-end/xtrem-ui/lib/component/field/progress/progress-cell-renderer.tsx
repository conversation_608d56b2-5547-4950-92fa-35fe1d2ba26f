import * as React from 'react';
import ProgressTracker from 'carbon-react/esm/components/progress-tracker';
import type { NestedProgressProperties } from '../../nested-fields-properties';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';

export const ProgressCellRenderer: React.FC<CellParams<NestedProgressProperties, number>> = React.memo(props => {
    if (props.value === undefined) {
        return null;
    }
    const value = props.value || 0;
    const displayedValue = `${String(Math.round(value))} %`;

    return (
        <props.fieldProperties.wrapper {...props}>
            <div
                className="e-field-progress-progressbar e-progress-bar-label-hidden"
                data-testid="e-field-progress-progressbar"
                title={displayedValue}
            >
                <ProgressTracker length="100%" progress={value} size="large" />
            </div>
        </props.fieldProperties.wrapper>
    );
});

ProgressCellRenderer.displayName = 'ProgressCellRenderer';
