/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import { xtremConsole } from '../../../utils/console';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { ProgressControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { ProgressDecoratorProperties } from './progress-types';

class ProgressDecorator extends AbstractFieldDecorator<FieldKey.Progress> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = ProgressControlObject;
}

/**
 * Initializes the decorated member as a [Progress]{@link ProgressControlObject} field with the provided properties
 *
 * @param properties The properties that the [Progress]{@link ProgressControlObject} field will be initialized with
 */
export function progressField<T extends ScreenExtension<T>>(
    properties: ProgressDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Progress>(properties, ProgressDecorator, FieldKey.Progress);
}

export function progressFieldOverride<T extends ScreenExtension<T>>(
    properties: ClickableOverrideDecoratorProperties<ProgressDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    if (properties.color) {
        xtremConsole.warn('Custom colors on progress indicator are now deprecated');
    }
    return standardExtensionDecoratorImplementation<T, FieldKey.Progress>(properties);
}
