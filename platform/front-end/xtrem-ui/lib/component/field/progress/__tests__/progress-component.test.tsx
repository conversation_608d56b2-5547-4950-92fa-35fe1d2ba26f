import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { FieldKey } from '../../../types';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import type { XtremAppState } from '../../../../redux';
import { ConnectedProgressComponent, ProgressComponent } from '../progress-component';
import type { ReadonlyFieldProperties } from '../../../readonly-field-control-object';
import { render, cleanup } from '@testing-library/react';

describe('ProgressComponent', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: ReadonlyFieldProperties;

    beforeEach(() => {
        cleanup();
        mockFieldProperties = {
            title: 'Test Field Title',
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Progress, state, screenId, 'test-progress-field', mockFieldProperties, 42);
            addFieldToState(FieldKey.Progress, state, screenId, 'test-empty-progress-field', mockFieldProperties, null);
            mockStore = getMockStore(state);
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedProgressComponent screenId={screenId} elementId="test-progress-field" />
                    </Provider>,
                );
                expect(wrapper.queryByTestId('e-field-bind-test-progress-field', { exact: false })).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedProgressComponent screenId={screenId} elementId="test-progress-field" />
                    </Provider>,
                );
                expect(wrapper.queryByTestId('e-field-bind-test-progress-field', { exact: false })).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedProgressComponent screenId={screenId} elementId="test-progress-field" />
                    </Provider>,
                );
                expect(wrapper.queryByTestId('e-field-bind-test-progress-field', { exact: false })).toMatchSnapshot();
            });

            it('should render without value', () => {
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedProgressComponent screenId={screenId} elementId="test-empty-progress-field" />
                    </Provider>,
                );
                expect(wrapper.queryByTestId('e-field-bind-test-progress-field', { exact: false })).toMatchSnapshot();
            });

            it('should render with a title', () => {
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedProgressComponent screenId={screenId} elementId="test-progress-field" />
                    </Provider>,
                );
                const title = wrapper.queryByTestId('e-field-label');
                expect(title).toHaveTextContent('Test Field Title');
            });

            it('should render with a hidden title', () => {
                mockFieldProperties.isTitleHidden = true;

                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedProgressComponent screenId={screenId} elementId="test-progress-field" />
                    </Provider>,
                );
                expect(wrapper.queryByTestId('e-field-label')).toBeNull();
            });
        });

        describe('Interactions', () => {
            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedProgressComponent screenId={screenId} elementId="test-progress-field" />
                    </Provider>,
                );

                expect(wrapper.queryByTestId('e-field-helper-text')).not.toBeNull();
                expect(wrapper.queryByTestId('e-field-helper-text')).toHaveTextContent('This is a helper text');
            });
        });
    });

    describe('unconnected', () => {
        describe('Snapshots', () => {
            it('should render just fine', () => {
                const wrapper = render(
                    <ProgressComponent
                        elementId="test-progress-field"
                        fieldProperties={mockFieldProperties}
                        value={50}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );
                expect(wrapper.queryByTestId('e-field-bind-test-progress-field', { exact: false })).toMatchSnapshot();
            });
        });

        describe('Interactions', () => {
            it('should update the value on external update', () => {
                let wrapper = render(
                    <ProgressComponent
                        elementId="test-progress-field"
                        fieldProperties={mockFieldProperties}
                        value={25}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(
                    wrapper.baseElement.querySelector<HTMLElement>('[data-element="current-progress-label"]')!,
                ).toHaveTextContent('25');

                cleanup();

                wrapper = render(
                    <ProgressComponent
                        elementId="test-progress-field"
                        fieldProperties={mockFieldProperties}
                        value={35}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(
                    wrapper.baseElement.querySelector<HTMLElement>('[data-element="current-progress-label"]')!,
                ).toHaveTextContent('35');

                cleanup();

                wrapper = render(
                    <ProgressComponent
                        elementId="test-progress-field"
                        fieldProperties={mockFieldProperties}
                        value={60}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );
                expect(
                    wrapper.baseElement.querySelector<HTMLElement>('[data-element="current-progress-label"]')!,
                ).toHaveTextContent('60');
            });
        });
    });
});
