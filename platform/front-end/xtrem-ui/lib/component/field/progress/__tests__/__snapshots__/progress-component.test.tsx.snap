// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ProgressComponent connected Snapshots should render helperText 1`] = `
.c0 {
  text-align: center;
  white-space: nowrap;
  width: 100%;
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  background-color: var(--colorsSemanticNeutral200);
  border: 1px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius400);
  overflow-x: hidden;
  width: 100%;
  min-height: -webkit-fit-content;
  min-height: -moz-fit-content;
  min-height: fit-content;
  box-sizing: border-box;
}

.c2 {
  display: inline-block;
  font-weight: 500;
}

.c1 {
  text-align: start;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 4px;
  font-size: var(--fontSizes100);
  margin-bottom: var(--spacing100);
}

.c4 {
  position: relative;
  left: 0;
  background-color: var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius400);
  width: 42%;
  min-width: 2px;
  height: var(--sizing100);
}

<div
  class="e-field e-progress-field"
  data-label="Test Field Title"
  data-testid="e-progress-field e-field-label-testFieldTitle e-field-bind-test-progress-field"
>
  <label
    class="common-input__label"
    data-element="label"
    data-testid="e-field-label"
  >
    Test Field Title
  </label>
  <div
    class="c0"
    data-component="progress-bar"
  >
    <span
      class="c1"
      data-role="values-label"
    >
      <span
        class="c2"
        data-element="current-progress-label"
      >
        42%
      </span>
      <span
        data-element="custom-preposition"
      >
        of
      </span>
      <span
        class="c2"
        data-element="max-progress-label"
      >
        100%
      </span>
    </span>
    <span
      aria-hidden="true"
      class="c3"
      data-role="progress-bar"
    >
      <span
        class="c4"
        data-element="inner-bar"
        data-role="inner-bar"
      />
    </span>
  </div>
  <span
    class="common-input__help-text"
    data-element="help"
    data-testid="e-field-helper-text"
  >
    This is a helper text
  </span>
</div>
`;

exports[`ProgressComponent connected Snapshots should render hidden 1`] = `
.c0 {
  text-align: center;
  white-space: nowrap;
  width: 100%;
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  background-color: var(--colorsSemanticNeutral200);
  border: 1px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius400);
  overflow-x: hidden;
  width: 100%;
  min-height: -webkit-fit-content;
  min-height: -moz-fit-content;
  min-height: fit-content;
  box-sizing: border-box;
}

.c2 {
  display: inline-block;
  font-weight: 500;
}

.c1 {
  text-align: start;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 4px;
  font-size: var(--fontSizes100);
  margin-bottom: var(--spacing100);
}

.c4 {
  position: relative;
  left: 0;
  background-color: var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius400);
  width: 42%;
  min-width: 2px;
  height: var(--sizing100);
}

<div
  class="e-field e-progress-field e-hidden"
  data-label="Test Field Title"
  data-testid="e-progress-field e-field-label-testFieldTitle e-field-bind-test-progress-field"
>
  <label
    class="common-input__label"
    data-element="label"
    data-testid="e-field-label"
  >
    Test Field Title
  </label>
  <div
    class="c0"
    data-component="progress-bar"
  >
    <span
      class="c1"
      data-role="values-label"
    >
      <span
        class="c2"
        data-element="current-progress-label"
      >
        42%
      </span>
      <span
        data-element="custom-preposition"
      >
        of
      </span>
      <span
        class="c2"
        data-element="max-progress-label"
      >
        100%
      </span>
    </span>
    <span
      aria-hidden="true"
      class="c3"
      data-role="progress-bar"
    >
      <span
        class="c4"
        data-element="inner-bar"
        data-role="inner-bar"
      />
    </span>
  </div>
</div>
`;

exports[`ProgressComponent connected Snapshots should render with default properties 1`] = `
.c0 {
  text-align: center;
  white-space: nowrap;
  width: 100%;
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  background-color: var(--colorsSemanticNeutral200);
  border: 1px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius400);
  overflow-x: hidden;
  width: 100%;
  min-height: -webkit-fit-content;
  min-height: -moz-fit-content;
  min-height: fit-content;
  box-sizing: border-box;
}

.c2 {
  display: inline-block;
  font-weight: 500;
}

.c1 {
  text-align: start;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 4px;
  font-size: var(--fontSizes100);
  margin-bottom: var(--spacing100);
}

.c4 {
  position: relative;
  left: 0;
  background-color: var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius400);
  width: 42%;
  min-width: 2px;
  height: var(--sizing100);
}

<div
  class="e-field e-progress-field"
  data-label="Test Field Title"
  data-testid="e-progress-field e-field-label-testFieldTitle e-field-bind-test-progress-field"
>
  <label
    class="common-input__label"
    data-element="label"
    data-testid="e-field-label"
  >
    Test Field Title
  </label>
  <div
    class="c0"
    data-component="progress-bar"
  >
    <span
      class="c1"
      data-role="values-label"
    >
      <span
        class="c2"
        data-element="current-progress-label"
      >
        42%
      </span>
      <span
        data-element="custom-preposition"
      >
        of
      </span>
      <span
        class="c2"
        data-element="max-progress-label"
      >
        100%
      </span>
    </span>
    <span
      aria-hidden="true"
      class="c3"
      data-role="progress-bar"
    >
      <span
        class="c4"
        data-element="inner-bar"
        data-role="inner-bar"
      />
    </span>
  </div>
</div>
`;

exports[`ProgressComponent connected Snapshots should render without value 1`] = `null`;

exports[`ProgressComponent unconnected Snapshots should render just fine 1`] = `
.c0 {
  text-align: center;
  white-space: nowrap;
  width: 100%;
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  background-color: var(--colorsSemanticNeutral200);
  border: 1px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius400);
  overflow-x: hidden;
  width: 100%;
  min-height: -webkit-fit-content;
  min-height: -moz-fit-content;
  min-height: fit-content;
  box-sizing: border-box;
}

.c2 {
  display: inline-block;
  font-weight: 500;
}

.c1 {
  text-align: start;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 4px;
  font-size: var(--fontSizes100);
  margin-bottom: var(--spacing100);
}

.c4 {
  position: relative;
  left: 0;
  background-color: var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius400);
  width: 50%;
  min-width: 2px;
  height: var(--sizing100);
}

<div
  class="e-field e-progress-field"
  data-label="Test Field Title"
  data-testid="e-progress-field e-field-label-testFieldTitle e-field-bind-test-progress-field"
>
  <label
    class="common-input__label"
    data-element="label"
    data-testid="e-field-label"
  >
    Test Field Title
  </label>
  <div
    class="c0"
    data-component="progress-bar"
  >
    <span
      class="c1"
      data-role="values-label"
    >
      <span
        class="c2"
        data-element="current-progress-label"
      >
        50%
      </span>
      <span
        data-element="custom-preposition"
      >
        of
      </span>
      <span
        class="c2"
        data-element="max-progress-label"
      >
        100%
      </span>
    </span>
    <span
      aria-hidden="true"
      class="c3"
      data-role="progress-bar"
    >
      <span
        class="c4"
        data-element="inner-bar"
        data-role="inner-bar"
      />
    </span>
  </div>
</div>
`;
