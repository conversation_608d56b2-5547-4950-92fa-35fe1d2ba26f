import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import { progressField } from '../progress-decorator';

describe('Progress Decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'textField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(progressField, pageMetadata, fieldId);
        });
    });
});
