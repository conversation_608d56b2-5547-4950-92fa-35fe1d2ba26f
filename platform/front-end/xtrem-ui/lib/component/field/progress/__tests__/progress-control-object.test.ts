import { <PERSON><PERSON>ey } from '../../../types';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { ProgressControlObject } from '../../../control-objects';

describe('Progress Field', () => {
    const screenId = 'TestPage';
    let progressField: ProgressControlObject;

    beforeEach(() => {
        progressField = createFieldControlObject<FieldKey.Progress>(
            FieldKey.Progress,
            screenId,
            ProgressControlObject,
            'test',
            50,
            {
                color: '#00ff00',
                isDisabled: true,
                isHidden: true,
                title: 'TEST_FIELD_TITLE',
            },
        );
    });

    it('getting field value', () => {
        expect(progressField.value).toEqual(50);
    });

    describe('setting and getting updated values', () => {
        it('should set the helper text', () => {
            const newHelperText = 'NEW_TEST_FIELD_HELPER_TEXT';
            expect(progressField.helperText).not.toEqual(newHelperText);
            progressField.helperText = newHelperText;
            expect(progressField.helperText).toEqual(newHelperText);
        });

        it('should set the title', () => {
            const newTitle = 'NEW_TEST_FIELD_TITLE';
            expect(progressField.title).not.toEqual(newTitle);
            progressField.title = newTitle;
            expect(progressField.title).toEqual(newTitle);
        });
    });
});
