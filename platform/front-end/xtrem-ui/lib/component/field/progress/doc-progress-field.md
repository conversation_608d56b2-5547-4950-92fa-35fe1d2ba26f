PATH: XTREEM/UI+Field+Widgets/Progress+Field

## Introduction

Progress field can indicate a percentage value, it is rendered as a progress bar.

## Example:

```ts
@ui.decorators.progressField<NumericField>({
    parent() {
        return this.numericBlock;
    },
    title: 'A progress field',
    onClick() {
        console.log('Doing something when the field is clicked');
    },
    helperText: 'Wow! So much progress!'
})
aSimpleProgressField: ui.fields.Progress;
```

### Display decorator properties:

-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **helperText**: The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized. If the helper text is not provided, the value is of the field is displayed in its place.
-   **currentProgressLabel**:  Value to display as current progress. Defaults to the value as percentage
-   **maxProgressLabel**:  Value to display as the maximum progress limit. Defaults to "100%"
-   **areProgressLabelsHidden**: Hides the status labels, defaults to false

Unsupported properties:
-   **color**: Due to a DLS restriction, the color of the progress bar cannot be set by the application code anymore. Progress bars that are incomplete are displayed with a slate color and complete progress bars are displayed with a success color. Any value set to the `color` property is ignored.

### Binding decorator properties:

-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

### Event handler decorator properties:

-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

#### Runtime Functions

-   **refresh()**: Refetches the field's values from the server and updates the user interface.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **isDirty()**: Sets or gets the dirty state of the field.
