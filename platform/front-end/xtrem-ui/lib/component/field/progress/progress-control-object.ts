/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { ProgressProperties } from './progress-types';

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a numeric value and represents it as a percentage (e.g. progress bar)
 */
export class ProgressControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends ReadonlyFieldControlObject<
    CT,
    FieldKey.Progress,
    FieldComponentProps<FieldKey.Progress>
> {
    @FieldControlObjectResolvedProperty<ProgressProperties<CT>, ProgressControlObject<CT>>()
    /**
     * @deprecated
     * The font color of the HTML field */
    color?: string;

    @FieldControlObjectResolvedProperty<ProgressProperties<CT>, ProgressControlObject<CT>>()
    /* Value to display as current progress. Defaults to the value as percentage */
    currentProgressLabel?: string;

    @FieldControlObjectResolvedProperty<ProgressProperties<CT>, ProgressControlObject<CT>>()
    /* Value to display as the maximum progress limit. Defaults to "100%" */
    maxProgressLabel?: string;

    @FieldControlObjectResolvedProperty<ProgressProperties<CT>, ProgressControlObject<CT>>()
    /* Hides the status labels, defaults to false */
    areProgressLabelsHidden?: boolean;
}
