import type { BaseReadonlyComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { ScreenBase } from '../../../service/screen-base';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { Clickable, ExtensionField, HasParent, Nested, NestedClickable, Sizable, Colorable } from '../traits';
import type { FieldControlObjectInstance } from '../../types';
import type { BlockControlObject } from '../../control-objects';
import type { ClientNode } from '@sage/xtrem-client';

export interface ProgressDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<ProgressProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        HasParent<CT, BlockControlObject<CT>>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Sizable {}

export interface NestedProgressProperties<CT extends ScreenBase = ScreenBase, NodeType extends ClientNode = any>
    extends Omit<ProgressProperties<CT>, 'bind'>,
        Nested<NodeType>,
        NestedClickable<CT, NodeType>,
        Sizable {}

export interface ProgressProperties<CT extends ScreenBase = ScreenBase>
    extends ReadonlyFieldProperties<CT>,
        Colorable<CT> {
    /* Value to display as current progress. In case of progress fields rendered in a cell, the label is not displayed. Defaults to the value as percentage */
    currentProgressLabel?: string;

    /* Value to display as the maximum progress limit. In case of progress fields rendered in a cell, the label is not displayed. Defaults to "100%" */
    maxProgressLabel?: string;

    /* Hides the status labels, defaults to false */
    areProgressLabelsHidden?: boolean;
}

export type ProgressComponentProps = BaseReadonlyComponentProperties<
    ProgressProperties,
    number,
    NestedFieldsAdditionalProperties
>;
