import * as React from 'react';
import { connect } from 'react-redux';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { mapDispatchToProps, mapReadonlyStateToProps, ReadonlyFieldBaseComponent } from '../field-base-component';
import type { NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { ProgressDecoratorProperties } from './progress-types';
import ProgressTracker from 'carbon-react/esm/components/progress-tracker';

export class ProgressComponent extends ReadonlyFieldBaseComponent<
    ProgressDecoratorProperties,
    number,
    NestedFieldsAdditionalProperties
> {
    render(): React.ReactNode {
        const value = this.props.value || 0;
        const { helperText, isTitleHidden, areProgressLabelsHidden, currentProgressLabel, maxProgressLabel } =
            this.props.fieldProperties;
        return (
            <div
                {...this.getBaseAttributesDivWrapper(
                    'progress',
                    !areProgressLabelsHidden ? 'e-progress-field' : 'e-progress-field e-progress-bar-label-hidden',
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
                onClick={this.getClickHandler()}
            >
                {!isTitleHidden && <FieldLabel label={this.getTitle()} />}
                <ProgressTracker
                    length="100%"
                    progress={Math.round(value * 100) / 100}
                    currentProgressLabel={areProgressLabelsHidden ? undefined : currentProgressLabel}
                    maxProgressLabel={areProgressLabelsHidden ? undefined : maxProgressLabel}
                />

                {helperText && <HelperText helperText={helperText} />}
            </div>
        );
    }
}

export const ConnectedProgressComponent = connect(mapReadonlyStateToProps(), mapDispatchToProps())(ProgressComponent);

export default ConnectedProgressComponent;
