import type { UseComboboxStateChangeTypes } from 'downshift';
import React from 'react';
import {
    getInitialCellEditorState,
    setDefaultAgGridInputStyles,
} from '../../../utils/ag-grid/ag-grid-cell-editor-utils';
import { shouldRenderDropdownAbove } from '../../../utils/table-component-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { SelectItem } from '../../ui/select/select-component';
import { Select } from '../../ui/select/select-component';
import type { SelectEditorProps } from './select-types';
import { createSelectItemFromOption, getItemsFromProps } from './select-utils';
import { FieldKey } from '../../types';

export const SelectCellEditor: React.FC<SelectEditorProps> = React.memo(props => {
    const selectInputRef = React.useRef<HTMLInputElement>(null);
    const {
        api,
        column,
        data,
        eGridCell,
        elementId,
        eventKey,
        fieldProperties,
        initialValue,
        tableElementId,
        localizedOptions,
        onValueChange,
        node: { rowIndex },
        screenId,
        stopEditing,
    } = props;
    const { isSortedAlphabetically } = fieldProperties;
    const { highlightOnFocus, value } = getInitialCellEditorState({ eventKey, initialValue });
    const startValue = localizedOptions && localizedOptions[value] ? localizedOptions[value] : value || '';
    const selectedRecord = startValue === '' ? undefined : createSelectItemFromOption(startValue, props);
    const columnIndex = (column && api.getColumns()?.indexOf(column)) ?? 0;
    const dataTestId = `${tableElementId}-${rowIndex}-${columnIndex + 1}`;
    const width = eGridCell?.style?.width || '200px';
    const onSelected = (selectedItem?: SelectItem | null, selectionType?: UseComboboxStateChangeTypes): void => {
        onValueChange(selectedItem?.id ?? null);
        if (selectionType !== '__input_blur__') {
            stopEditing(true);
        }
    };
    const getItems = (searchText: string): Promise<SelectItem[]> => {
        const items = getItemsFromProps(props, searchText, splitValueToMergedValue(props.data));
        return Promise.resolve(items);
    };

    React.useEffect(() => {
        const input = selectInputRef.current as HTMLInputElement;
        setDefaultAgGridInputStyles(input);
        const inputDiv = input.parentNode as HTMLDivElement;
        inputDiv.style.width = '100%';
        inputDiv.style.height = '100%';
        inputDiv.style.border = 'none';
        inputDiv.style.display = 'flex';
        inputDiv.style.alignItems = 'center';
        input.focus();
        if (highlightOnFocus) {
            input.select();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const commonProps = {
        elementId,
        getItems,
        isSortedAlphabetically,
        minLookupCharacters: 0,
        onSelected,
        ref: selectInputRef,
        screenId,
        selectedItem: selectedRecord,
        testId: `${dataTestId}-input`,
    };

    const selectProps = {
        ...commonProps,
        autoSelect: true,
        initialInputValue: startValue,
        isInTable: true,
        shouldRenderOptionsAbove: shouldRenderDropdownAbove({
            isPhantomRow: !!data?.__phantom,
            pageSize: api.paginationGetPageSize(),
            rowIndex: rowIndex ?? 0,
        }),
    };

    const dropdownListProps = {
        ...commonProps,
        hasInputSearch: false,
        initialInputValue: '',
    };
    const p = fieldProperties._controlObjectType === FieldKey.DropdownList ? dropdownListProps : selectProps;
    return (
        <div data-testid={dataTestId} style={{ width }}>
            <Select {...p} variant="plain" />
        </div>
    );
});
SelectCellEditor.displayName = 'SelectCellEditor';
