import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import { getValidOptionValuesForSelect } from './select-utils';
import type { SelectProperties } from './select-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a value from a set of given values
 */
export class SelectControlObject<
    ReferencedEnumType extends string = string,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.Select, FieldComponentProps<FieldKey.Select>> {
    @ControlObjectProperty<SelectProperties<CT>, SelectControlObject<ReferencedEnumType, CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<SelectProperties<CT>, SelectControlObject<ReferencedEnumType, CT>>()
    /** Icon of the input field. It will be placed on the right side. */
    icon?: IconType;

    @ControlObjectProperty<SelectProperties<CT>, SelectControlObject<ReferencedEnumType, CT>>()
    /** Color of the icon, only supported in tile containers */
    iconColor?: string;

    @ControlObjectProperty<SelectProperties<CT>, SelectControlObject<ReferencedEnumType, CT>>()
    /** Indicator, whether sounds play on successful/erroneous selection */
    isSoundDisabled?: boolean;

    @FieldControlObjectResolvedProperty<SelectProperties<CT>, SelectControlObject<ReferencedEnumType, CT>>()
    /** Options to be displayed in the select element */
    options?: string[];

    @ControlObjectProperty<SelectProperties<CT>, SelectControlObject<ReferencedEnumType, CT>>()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    /**
     * The GraphQL node that the select options will be fetched from.
     * When using this property, the node must be an Enum
     */
    get optionType(): string | undefined {
        return this.getUiComponentProperty('optionType');
    }

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    /** Field's value, only valid options can be set as value. */
    set value(newValue: ReferencedEnumType | null) {
        const validOptions = getValidOptionValuesForSelect(this.options || [], this.optionType);
        if (newValue === null || validOptions.indexOf(newValue) !== -1) {
            this._setValue(newValue as string);
        } else {
            throw new Error(
                `${newValue} is not a valid option of the ${this.elementId} field. Valid options: ${validOptions.join(
                    ', ',
                )}`,
            );
        }
    }

    /** Field's value, only valid options can be set as value. */
    get value(): ReferencedEnumType | null {
        return (this._getValue() as ReferencedEnumType) || null;
    }
}
