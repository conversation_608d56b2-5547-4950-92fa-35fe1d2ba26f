import type { ClientNode } from '@sage/xtrem-client';
import type { Dict } from '@sage/xtrem-shared';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { BlockControlObject, TileControlObject } from '../../control-objects';
import type { NestedFilterSelectProperties } from '../../decorators';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Changeable,
    ExtensionField,
    HasEmptyValue,
    HasIcon,
    HasInputValueChangeListener,
    HasOptionDetailsType,
    HasOptions,
    HasParent,
    HasPlaceholder,
    HasSound,
    Mappable,
    Nested,
    NestedChangeable,
    NestedValidatable,
    Postfixable,
    Prefixable,
    Sizable,
    Validatable,
} from '../traits';

export interface SelectProperties<CT extends ScreenBase = ScreenBase, ContextNodeType = void>
    extends EditableFieldProperties<CT, ContextNodeType>,
        HasIcon,
        HasPlaceholder,
        HasOptions<CT>,
        HasSound<CT>,
        Postfixable<CT, ContextNodeType>,
        Prefixable<CT, ContextNodeType>,
        Sizable {}

export interface SelectEditorProps extends CellParams<NestedSelectProperties> {
    enumOptions?: string[];
    localizedOptions?: Dict<string>;
}

export interface SelectInternalProps {
    enumOptions?: string[];
    localizedOptions?: Dict<string>;
    screenId: string;
    value?: string;
}

export interface FilterSelectCellEditorProps extends CellParams<NestedFilterSelectProperties> {
    localizedNew: string;
}

export interface SelectDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<SelectProperties<CT, void>, '_controlObjectType'>,
        Changeable<CT>,
        HasParent<CT, BlockControlObject<CT> | TileControlObject<CT>>,
        HasInputValueChangeListener<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Validatable<CT>,
        Mappable<CT>,
        HasOptionDetailsType<CT>,
        HasSound<CT>,
        Sizable {}

export interface NestedSelectProperties<CT extends ScreenBase = ScreenBase, ContextNodeType extends ClientNode = any>
    extends NestedPropertiesWrapper<SelectProperties<CT, ContextNodeType>>,
        NestedChangeable<CT>,
        Nested<ContextNodeType>,
        Sizable,
        Mappable<CT>,
        HasEmptyValue,
        HasOptionDetailsType<CT>,
        NestedValidatable<CT, string, ContextNodeType> {}

export type SelectExtensionDecoratorProperties<T extends ScreenExtension<T>> = ChangeableOverrideDecoratorProperties<
    SelectDecoratorProperties<Extend<T>>,
    T
>;

export type SelectAdditionalProperties = NestedFieldsAdditionalProperties & SelectInternalProps;

export type SelectComponentProps = BaseEditableComponentProperties<
    SelectDecoratorProperties,
    string,
    SelectAdditionalProperties
>;
