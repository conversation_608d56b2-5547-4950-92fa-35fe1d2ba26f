PATH: XTREEM/UI+Field+Widgets/Select+Field

## Introduction

The select field represents a value selection input in form of a dropdown control within the user interface. This field is also available as a nested and extended field.

## Example

```ts
@ui.decorators.selectField<Page>({
    helperText: 'Helper text displayed below the field.',
    isDisabled: false,
    isFullWidth: false,
    isHelperTextHidden: false,
    isHidden: false,
    isMandatory: true,
    isTransient: false,
    optionType: '@sage/xtrem-docs/Category',
    placeholder: 'Select a category...',
    size: 'medium',
    title: 'Select Field',
    width: 'medium',
    onChange() {
        console.log(`Do something when the field's value has changed.`);
    },
    parent() {
        return this.block;
    },
})
field: ui.fields.Select<CategoryType>;
```

```ts
@ui.decorators.selectField<PageExtension>({
    ...,
    title: 'Extended Select Field',
    insertBefore() {
        return this.field;
    },
    parent() {
        return this.block;
    },
})
extension: ui.fields.Select;
```

```ts
@ui.decorators.tableField<Page>({
    ...,
    columns: [
        ...,
        ui.nestedFields.Select({
            bind: 'category',
            optionType: '@sage/xtrem-docs/Category',
            title: 'Category',
        }),
    ]
})
table: ui.fields.Table<Node>;
```

## Decorator Properties

#### Display Properties

-   **helperText**: The helper text displayed below the field. This property is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.
-   **isHelperTextHidden**: Determines whether the field's helper text is displayed or not.
-   **isHidden**: Determines whether the field is displayed or not.
-   **isTitleHidden**: Determines whether the field's title is displayed or not.
-   **isSortedAlphabetically**: Forces the options to be rendered in alphabetical order.
-   **placeholder**: Placeholder text which is displayed inside the field body when the field is empty. It is automatically picked up by the i18n engine and externalized.
-   **size**: Size the field should be rendered in. The options are "small", "medium" and "large".
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **icon**: Icon to be displayed on the right side of the input. The list of icons are defined by [DLS](https://brand.sage.com/d/NdbrveWvNheA/foundations#/icons/icons).
-   **iconColor**: Color of the input icon, only supported if the field is rendered within a tile container.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.

#### Binding Properties

-   **bind**: Determines the associated GraphQL node's property the field's value will be bound to.
-   **isTransient**: Determines whether the field will be excluded from the automatic data binding process or not.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **map()**: Custom callback to transform the field's value. The new value and grid's row data (if applicable) is provided as arguments. If implemented, the callbacks return value will be used as the field's value.
-   **mapDetails()**: Custom callback to dynamically set additional information about each option, it is displayed on each line in the selection dropdown.
-   **options**: List of options available to select from. It can also be defined as callback function that returns a string array.

#### Validation Properties

-   **isMandatory**: Determines whether leaving the field empty will raise a validation error. It can also be defined as callback function that returns a boolean.
-   **optionType**: GraphQL enum type. The enum members are used as options and they are displayed in a localized form.

#### Event Handler Properties

-   **onChange**: Handler triggered when the field's value has changed.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).
-   **onInputValueChange**: Triggered when the user stops typing into the input of the field. The execution is debounced by 150ms to cater for continuos typing.

### Other Decorator Properties

-   **fetchesDefaults**: When set to true and when the select value changes, a request to the server for default values for the whole page will be requested. False by default.

#### Runtime Properties

-   **refresh()**: Refetches the field's value from the server and updates the user interface.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result.
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Select).
