import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { SelectComponentProps } from './select-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedSelectComponent = React.lazy(() => import('./select-component'));

export function AsyncConnectedSelectComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedSelectComponent {...props} />
        </React.Suspense>
    );
}

const SelectComponent = React.lazy(() => import('./select-component').then(c => ({ default: c.SelectComponent })));

export function AsyncSelectComponent(props: SelectComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <SelectComponent {...props} />
        </React.Suspense>
    );
}
