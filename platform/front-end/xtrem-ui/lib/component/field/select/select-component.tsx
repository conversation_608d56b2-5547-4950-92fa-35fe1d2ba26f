import { debounce } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import type { XtremAppState } from '../../../redux';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { addOptionsAndLocalizationToProps } from '../../../utils/transformers';
import type { SelectItem } from '../../ui/select/select-component';
import { Select } from '../../ui/select/select-component';
import { getCommonCarbonComponentProperties, getLabelTitle } from '../carbon-helpers';
import { CarbonWrapper } from '../carbon-wrapper';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { SelectComponentProps, SelectDecoratorProperties, SelectAdditionalProperties } from './select-types';
import { createSelectItemFromOption, getItemsFromProps } from './select-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';

export class SelectComponent extends EditableFieldBaseComponent<
    SelectDecoratorProperties,
    string,
    SelectAdditionalProperties,
    {}
> {
    static resolveDisplayValueFromProps(props: SelectComponentProps, value = ''): string {
        const selectItem = createSelectItemFromOption(value, props);
        return selectItem.displayedAs || selectItem.value;
    }

    private readonly onInputValueChanged = debounce(async (searchText: string) => {
        await triggerFieldEvent(this.props.screenId, this.props.elementId, 'onInputValueChange', searchText);
    }, 150);

    onChange = (selectedItem: SelectItem | undefined, isOrganicChange: boolean): void => {
        if (isOrganicChange) {
            handleChange(
                this.props.elementId,
                selectedItem ? selectedItem.id : null,
                this.props.setFieldValue,
                this.props.validate,
                this.triggerChangeListener,
            );
        }
    };

    getItems = (searchText: string): Promise<SelectItem[]> => {
        if (this.props.fieldProperties.optionType) {
            const items = getItemsFromProps(this.props, searchText, this.props.recordContext) || [];

            return Promise.resolve(items);
        }

        if (this.props.fieldProperties.options) {
            const options: string[] = resolveByValue({
                propertyValue: this.props.fieldProperties.options,
                screenId: this.props.screenId,
                skipHexFormat: true,
                fieldValue: this.props.value,
                rowValue: this.props.recordContext,
            });

            const items = options.map(option => createSelectItemFromOption(option, this.props));

            return Promise.resolve(items);
        }

        return Promise.resolve([]);
    };

    render(): React.ReactNode {
        const carbonProps = getCommonCarbonComponentProperties(this.props);
        const value = this.props.value ? createSelectItemFromOption(this.props.value, this.props) : undefined;
        const initialInputValue = SelectComponent.resolveDisplayValueFromProps(this.props, this.props.value);
        const label = !this.props.fieldProperties.isTitleHidden
            ? getLabelTitle(this.props.screenId, this.props.fieldProperties, this.props.handlersArguments?.rowValue)
            : undefined;
        const {
            fieldProperties: { isFullWidth, icon, placeholder, helperText, size },
            onFocus,
            handlersArguments,
        } = this.props;
        return (
            <CarbonWrapper
                {...this.props}
                className="e-select-field"
                componentName="select"
                componentRef={this.componentRef}
                handlersArguments={handlersArguments}
                helperText={helperText}
                noReadOnlySupport={true}
                value={this.props.value}
                readOnlyDisplayValue={initialInputValue}
            >
                <Select
                    {...carbonProps}
                    autoSelect={true}
                    disabled={this.isDisabled()}
                    elementId={this.props.elementId}
                    fullWidth={isFullWidth}
                    getItems={this.getItems}
                    helperText={helperText}
                    icon={icon}
                    inputId={carbonProps.id}
                    isSortedAlphabetically={this.props.fieldProperties.isSortedAlphabetically}
                    label={label}
                    minLookupCharacters={0}
                    noHelperTextInItem={true}
                    onChange={this.onChange}
                    onInputChange={this.onInputValueChanged}
                    onInputFocus={onFocus}
                    placeholder={placeholder}
                    readOnly={this.isReadOnly()}
                    refetchIfChanged={this.props.fieldProperties.options}
                    screenId={this.props.screenId}
                    selectedItem={value}
                    size={size}
                    testId="e-select-field-input"
                    isSoundDisabled={resolveByValue({
                        screenId: this.props.screenId,
                        propertyValue: this.props.fieldProperties.isSoundDisabled,
                        skipHexFormat: true,
                        fieldValue: null,
                        rowValue: null,
                    })}
                />
            </CarbonWrapper>
        );
    }
}

export const ConnectedSelectComponent = connect(
    (state: XtremAppState, externalProps: FieldComponentExternalProperties) =>
        addOptionsAndLocalizationToProps(state, mapStateToProps()(state, externalProps) as SelectComponentProps),
    mapDispatchToProps(),
)(SelectComponent);

export default ConnectedSelectComponent;
