import { uniq } from 'lodash';
import { getStore } from '../../../redux';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { schemaTypeNameFromNodeName, splitValueToMergedValue } from '../../../utils/transformers';
import type { SelectItem } from '../../ui/select/select-component';
import type { HasOptionDetailsType, HasOptionType, HasOptions, Mappable } from '../traits';
import type { Dict } from '@sage/xtrem-shared';

export const SELECT_EMPTY_VALUE = '_emptyValue';

interface SelectLikeComponentProperties {
    enumOptions?: string[];
    localizedOptions?: Dict<string>;
    screenId: string;
    value?: any;
    fieldProperties: Mappable<any> &
        HasOptionType &
        HasOptions<any> &
        HasOptionDetailsType<any> & { hasEmptyValue?: boolean };
    recordContext?: any;
}

export const createSelectItemFromOption = (
    option: string = '',
    props?: SelectLikeComponentProperties,
    displayedAs?: string,
): SelectItem => {
    const helperText =
        resolveByValue({
            propertyValue: props?.fieldProperties.mapDetails,
            rowValue: props?.recordContext,
            fieldValue: option,
            screenId: props?.screenId,
            skipHexFormat: true,
        }) || undefined;

    if (props?.fieldProperties.map) {
        const mappedValue = resolveByValue({
            propertyValue: props.fieldProperties.map,
            rowValue: props.recordContext,
            fieldValue: option,
            screenId: props.screenId,
            skipHexFormat: true,
        });

        return {
            id: option === SELECT_EMPTY_VALUE ? ' ' : option,
            value: mappedValue ?? option,
            helperText,
            displayedAs: option === SELECT_EMPTY_VALUE ? ' ' : (displayedAs ?? mappedValue),
        };
    }
    return {
        id: option === SELECT_EMPTY_VALUE ? ' ' : option,
        value: props?.localizedOptions?.[option] ?? option,
        helperText,
        displayedAs: option === SELECT_EMPTY_VALUE ? ' ' : (displayedAs ?? props?.localizedOptions?.[option] ?? option),
    };
};

export const getItemsFromProps = (
    props: SelectLikeComponentProperties,
    currentSearchText?: string,
    rowValue?: any,
): SelectItem[] => {
    const optionList = resolveByValue<string[]>({
        propertyValue: props.fieldProperties.options,
        screenId: props.screenId,
        skipHexFormat: true,
        rowValue: splitValueToMergedValue(rowValue),
        fieldValue: props.value,
    });

    let options: string[] = props.enumOptions || optionList || [];
    if (options?.length > 0 && optionList?.length > 0) {
        // If both optionType and the options defined, we restrict the available items by the options array, but we still use the translations from the option type.
        options = options.filter(o => optionList.indexOf(o) !== -1);
    }

    if (props.fieldProperties.hasEmptyValue) {
        options.unshift(SELECT_EMPTY_VALUE);
    }

    let uniqueOptions = uniq(options)
        .map(i => createSelectItemFromOption(i, props))
        .filter(
            o => !currentSearchText || String(o.value).toLowerCase().indexOf(currentSearchText.toLowerCase()) !== -1,
        );

    if (props.fieldProperties.isSortedAlphabetically) {
        uniqueOptions = uniqueOptions.sort((a, b) =>
            (a.displayedAs || String(a.value)).localeCompare(b.displayedAs || String(b.value)),
        );
    }

    return uniqueOptions;
};

export const getValidOptionValuesForSelect = (options: string[] = [], optionType?: string): string[] => {
    if (optionType) {
        return getStore().getState().enumTypes[schemaTypeNameFromNodeName(optionType)];
    }

    return options;
};

export const isMatchingSearch = (currentSearchText: string, value?: string): boolean =>
    value !== undefined &&
    value !== null &&
    typeof value === 'string' &&
    value.toLowerCase().includes(currentSearchText.toLowerCase());
