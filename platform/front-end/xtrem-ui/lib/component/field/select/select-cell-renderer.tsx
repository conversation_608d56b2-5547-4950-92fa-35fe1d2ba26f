import React from 'react';
import type { SelectEditorProps } from './select-types';

export const SelectRenderer: React.FC<SelectEditorProps> = React.memo(props => {
    const { fieldProperties, tableElementId } = props;
    if (props.data && props.colDef!.field) {
        return (
            <fieldProperties.wrapper {...props}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span
                        data-testid={`${tableElementId}-${props.node.rowIndex}-${
                            props.api.getColumns()!.indexOf(props.column!) + 1
                        }`}
                    >
                        {props.localizedOptions?.[props.value] ?? props.value}
                    </span>
                </div>
            </fieldProperties.wrapper>
        );
    }
    return null;
});

SelectRenderer.displayName = 'SelectRenderer';
