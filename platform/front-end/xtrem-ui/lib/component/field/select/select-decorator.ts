import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import { addDisabledToProperties, addOptionTypeToProperties } from '../../../utils/data-type-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { SelectControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { SelectDecoratorProperties, SelectExtensionDecoratorProperties } from './select-types';

class SelectDecorator extends AbstractFieldDecorator<FieldKey.Select> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = SelectControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<SelectDecoratorProperties> {
        const properties: Partial<SelectDecoratorProperties> = {};
        addOptionTypeToProperties({ propertyDetails, dataType, properties });
        addDisabledToProperties({
            propertyDetails,
            dataType,
            properties,
        });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [Select]{@link SelectControlObject} field with the provided properties
 *
 * @param properties The properties that the [Select]{@link SelectControlObject} field will be initialized with
 */
export function selectField<T extends ScreenExtension<T>>(
    properties: SelectDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Select>(properties, SelectDecorator, FieldKey.Select);
}

export function selectFieldOverride<T extends ScreenExtension<T>>(
    properties: SelectExtensionDecoratorProperties<T>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Select>(properties);
}
