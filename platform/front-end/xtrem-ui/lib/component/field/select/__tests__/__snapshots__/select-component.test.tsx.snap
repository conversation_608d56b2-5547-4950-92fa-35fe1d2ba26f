// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Select component connected Interactions Should not render helperText 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-0-menu"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-0-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-0-label"
      class="e-ui-select-dropdown"
      id="downshift-0-menu"
      style="position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component connected Interactions should render helperText 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c10 {
  display: block;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin-top: 8px;
  white-space: pre-wrap;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-0-menu"
                aria-describedby="TestPage-test-select-field-field-help"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-0-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <span
          class="c10"
          data-element="help"
          id="TestPage-test-select-field-field-help"
        >
          This is a helper text
        </span>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-0-label"
      class="e-ui-select-dropdown"
      id="downshift-0-menu"
      style="top: -8px; position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render disabled 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
  color: var(--colorsUtilityYin030);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
  color: var(--colorsUtilityYin030);
  cursor: not-allowed;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background: var(--colorsUtilityDisabled400);
  border-color: var(--colorsUtilityDisabled600);
  cursor: not-allowed;
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field e-disabled"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              disabled=""
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              disabled=""
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                disabled=""
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder=""
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-0-label"
      class="e-ui-select-dropdown"
      id="downshift-0-menu"
      style="position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render helperText 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c10 {
  display: block;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin-top: 8px;
  white-space: pre-wrap;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-0-menu"
                aria-describedby="TestPage-test-select-field-field-help"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-0-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <span
          class="c10"
          data-element="help"
          id="TestPage-test-select-field-field-help"
        >
          This is a helper text
        </span>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-0-label"
      class="e-ui-select-dropdown"
      id="downshift-0-menu"
      style="top: -8px; position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render hidden 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field e-hidden"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-0-menu"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-0-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-0-label"
      class="e-ui-select-dropdown"
      id="downshift-0-menu"
      style="position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render in read only mode 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background-color: var(--colorsUtilityReadOnly400);
  border-color: var(--colorsUtilityReadOnly600);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              readonly=""
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder=""
                readonly=""
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render in read-only mode with a prefix and a postfix 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background-color: var(--colorsUtilityReadOnly400);
  border-color: var(--colorsUtilityReadOnly600);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              readonly=""
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder=""
                readonly=""
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render with a postfix 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-0-menu"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-0-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-0-label"
      class="e-ui-select-dropdown"
      id="downshift-0-menu"
      style="position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render with a prefix 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-0-menu"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-0-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-0-label"
      class="e-ui-select-dropdown"
      id="downshift-0-menu"
      style="position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render with default properties 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-0-menu"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-0-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-0-label"
      class="e-ui-select-dropdown"
      id="downshift-0-menu"
      style="position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render with full-width 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field e-full-width"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-0-menu"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-0-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-0-label"
      class="e-ui-select-dropdown"
      id="downshift-0-menu"
      style="position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render with various field sizes 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing400);
}

.c6 .c7 {
  padding: 0 var(--spacing100);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-0-menu"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-0-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-0-label"
      class="e-ui-select-dropdown"
      id="downshift-0-menu"
      style="position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render with various field sizes 2`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c10 .c7 {
  padding: 0 var(--spacing100);
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-3-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-3-menu"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-3-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-3-label"
      class="e-ui-select-dropdown"
      id="downshift-3-menu"
      style="position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render with various field sizes 3`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c10 .c7 {
  padding: 0 var(--spacing100);
}

.c11 .c7 {
  padding: 0 var(--spacing150);
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing600);
}

.c6 .c7 {
  padding: 0 var(--spacing200);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-6-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-6-menu"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-6-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-6-label"
      class="e-ui-select-dropdown"
      id="downshift-6-menu"
      style="position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component connected Snapshots should render without value 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-empty-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-empty-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-empty-select-field"
              id="TestPage-test-empty-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-0-menu"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-empty-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value=""
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-0-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-0-label"
      class="e-ui-select-dropdown"
      id="downshift-0-menu"
      style="position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-empty-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;

exports[`Select component unconnected Snapshots should render just fine 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-select-field"
    data-label="Test Field Title"
    data-testid="e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field"
  >
    <div
      aria-expanded="false"
      aria-haspopup="listbox"
      aria-owns="downshift-0-menu"
      class="e-ui-select-input-wrapper"
      role="combobox"
    >
      <div
        class="c0 c1"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-TestPage-test-select-field-label"
            width="30"
          >
            <label
              class="c4"
              data-element="label"
              for="TestPage-test-select-field"
              id="TestPage-test-select-field-label"
            >
              Test Field Title
            </label>
          </div>
          <div
            class="c5"
            data-role="input-presentation-container"
          >
            <div
              class="c6"
              role="presentation"
              style="padding-right: 20px; flex-wrap: nowrap;"
            >
              <div
                class="e-ui-select-input-left-children"
              />
              <input
                aria-autocomplete="list"
                aria-controls="downshift-0-menu"
                aria-invalid="false"
                aria-label="Test Field Title"
                autocomplete="off"
                class="c7 c8 e-field-select-input-text"
                data-element="input"
                data-testid="e-select-field-input"
                id="TestPage-test-select-field"
                name="testcarb-onco-mpon-ents-uniqguidmock"
                placeholder="Please Select..."
                style="text-overflow: ellipsis; min-width: 0;"
                type="text"
                value="Option1"
              />
              <div
                class="e-ui-select-inline-dropdown"
                id="downshift-0-toggle-button"
                tabindex="-1"
              >
                <div>
                  <span
                    class="c9"
                    data-component="icon"
                    data-element="dropdown"
                    data-role="icon"
                    font-size="small"
                    type="dropdown"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-busy="true"
      aria-labelledby="downshift-0-label"
      class="e-ui-select-dropdown"
      id="downshift-0-menu"
      style="position: relative; width: 100%;"
    >
      <ul
        aria-labelledby="TestPage-test-select-field"
        data-testid="e-ui-select-dropdown"
        role="listbox"
        style="max-height: 18px; border-radius: 4px;"
      />
    </div>
  </div>
</div>
`;
