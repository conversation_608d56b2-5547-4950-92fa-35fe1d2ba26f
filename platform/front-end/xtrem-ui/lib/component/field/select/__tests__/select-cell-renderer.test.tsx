import * as React from 'react';
import { render } from '@testing-library/react';
import { SelectRenderer } from '../select-cell-renderer';
import type { SelectEditorProps } from '../select-types';
import { CellWrapper } from '../../../ui/table-shared/cell/cell-wrapper';
import '@testing-library/jest-dom';
import { CollectionValue } from '../../../../service/collection-data-service';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';
import * as nestedFields from '../../../nested-fields';

describe('select cell renderer', () => {
    let props: SelectEditorProps;
    beforeEach(() => {
        props = {
            locale: 'en-US',
            columnId: 'test-column-id',
            data: {},
            initialValue: '',
            value: '',
            onValueChange: jest.fn(),
            valueFormatted: null,
            isParentFieldDisabled: false,
            collectionValue: () =>
                new CollectionValue({
                    screenId: 'test-screen',
                    elementId: 'testField',
                    isTransient: false,
                    hasNextPage: false,
                    orderBy: [{}],
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                        ],
                    ],
                    nodeTypes: {},
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                }),
            column: {
                colDef: {
                    field: 'test-column-id',
                },
                columnApi: {
                    getAllColumns: jest.fn(() => [
                        {
                            field: 'test-column-id',
                        },
                    ]),
                } as any,
            } as any,
            screenId: 'test-screen',
            elementId: 'test-table',
            tableElementId: 'test-table',
            isTree: false,
            colDef: {
                context: {
                    screenId: 'test-screen',
                    columnId: 'test-column-id',
                    isEditable: () => false,
                },
                field: 'test-column-id',
                cellRendererParams: {
                    locale: 'en-US',
                    screenId: 'test-screen',
                    elementId: 'test-table',
                    columnId: 'test-column-id',
                    tableElementId: 'test-table',
                    isParentFieldDisabled: false,
                    fieldProperties: {
                        bind: 'test-column-id',
                        wrapper: CellWrapper,
                    },
                    isTree: false,
                    collectionValue: () =>
                        new CollectionValue({
                            screenId: 'test-screen',
                            elementId: 'testField',
                            isTransient: false,
                            hasNextPage: false,
                            orderBy: [{}],
                            columnDefinitions: [
                                [
                                    nestedFields.text<any, any>({ bind: '_id' }),
                                    nestedFields.text<any, any>({ bind: 'anyField' }),
                                ],
                            ],
                            nodeTypes: {},
                            nodes: ['@sage/xtrem-test/AnyNode'],
                            filter: [undefined],
                            initialValues: [],
                            fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                        }),
                },
            },
            eventKey: null,
            fieldProperties: {
                bind: 'test-column-id',
                wrapper: CellWrapper,
            },
            stopEditing: jest.fn(),
            getValue: jest.fn(),
            setValue: jest.fn(),
            setTooltip: jest.fn(),
            formatValue: jest.fn(),
            refreshCell: jest.fn(),
            api: {
                getColumns: jest.fn(() => [
                    {
                        field: 'test-column-id',
                    },
                ]),
            } as any,
            node: {
                rowIndex: 3,
            } as any,
            context: {} as any,
            eGridCell: {} as any,
            eParentOfValue: {} as any,
            registerRowDragger: () => {},
        };
    });

    it('should render with an empty value', () => {
        const component = render(<SelectRenderer {...props} />);
        expect(component.getByTestId('test-table-3-0')).toHaveTextContent('');
    });

    it('should not render if no data is provided', () => {
        props.data = null;
        const component = render(<SelectRenderer {...props} />);
        expect(component.container.firstChild).toEqual(null);
    });

    it('should render a non-localized value', () => {
        props.value = 'anyValue';
        const component = render(<SelectRenderer {...props} />);
        expect(component.getByTestId('test-table-3-0')).toHaveTextContent('anyValue');
    });

    it('should render a localized value', () => {
        props.value = 'anyValue';
        props.enumOptions = ['anyValue', 'anotherValue'];
        props.localizedOptions = { anyValue: 'Any Value', anotherValue: 'Another Value' };
        const component = render(<SelectRenderer {...props} />);
        expect(component.getByTestId('test-table-3-0')).toHaveTextContent('Any Value');
    });
});
