import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import { SelectControlObject } from '../../../control-objects';
import type { <PERSON>Key } from '../../../types';
import type { SelectProperties } from '../select-types';
import * as stateUtils from '../../../../utils/state-utils';
import { getMockPageDefinition } from '../../../../__tests__/test-helpers';

jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() => getMockPageDefinition('TestPage'));

describe('Select control object', () => {
    let selectControlObject: SelectControlObject;
    let selectProperties: SelectProperties;
    let selectValue: string;

    beforeEach(() => {
        selectProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
            options: ['Option1', 'Option2'],
        };
        selectValue = 'Option1';
        selectControlObject = buildControlObject<FieldKey.Select>(SelectControlObject, {
            fieldValue: selectValue,
            fieldProperties: selectProperties,
        });
    });

    it('getting field value', () => {
        expect(selectControlObject.value).toEqual(selectValue);
    });

    it('should set field value to null', () => {
        expect(() => {
            selectControlObject.value = null;
        }).not.toThrow();
    });

    it('should set field value to a valid option', () => {
        expect(() => {
            selectControlObject.value = 'Option2';
        }).not.toThrow();
    });

    it('should not set field value to an invalid option', () => {
        expect(() => {
            selectControlObject.value = 'Option3';
        }).toThrow('Option3 is not a valid option of the fieldName field. Valid options: Option1, Option2');
    });

    it('should set the title', () => {
        const testFixture = 'Test Numeric Field Title';
        expect(selectProperties.title).not.toEqual(testFixture);
        selectControlObject.title = testFixture;
        expect(selectProperties.title).toEqual(testFixture);
    });

    it('should set the field options', () => {
        const testFixture = ['option3', 'option4'];
        expect(selectProperties.options).not.toEqual(testFixture);
        selectControlObject.options = testFixture;
        expect(selectProperties.options).toEqual(testFixture);
    });

    it('should set the icon value', () => {
        const testFixture = 'add';
        expect(selectProperties.icon).not.toEqual(testFixture);
        selectControlObject.icon = testFixture;
        expect(selectProperties.icon).toEqual(testFixture);
    });

    it('should resolve callback style option list', () => {
        selectProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
            options() {
                return ['Option12', 'Option32'];
            },
        };
        selectValue = 'Option1';
        selectControlObject = buildControlObject<FieldKey.Select>(SelectControlObject, {
            fieldValue: selectValue,
            fieldProperties: selectProperties,
        });

        expect(selectControlObject.options).toEqual(['Option12', 'Option32']);
    });

    describe('options as callback', () => {
        beforeEach(() => {
            selectProperties = {
                title: 'TEST_FIELD_TITLE',
                isHidden: true,
                isDisabled: true,
                options() {
                    return ['Option12', 'Option32'];
                },
            };
            selectValue = 'Option1';
            selectControlObject = buildControlObject<FieldKey.Select>(SelectControlObject, {
                fieldValue: selectValue,
                fieldProperties: selectProperties,
            });
        });

        it('should resolve callback style option list', () => {
            expect(selectControlObject.options).toEqual(['Option12', 'Option32']);
        });

        it('should set field value to a valid option', () => {
            expect(() => {
                selectControlObject.value = 'Option12';
            }).not.toThrow();
        });

        it('should not set field value to an invalid option', () => {
            expect(() => {
                selectControlObject.value = 'Option3';
            }).toThrow('Option3 is not a valid option of the fieldName field. Valid options: Option12, Option32');
        });
    });
});
