import * as resolveValueUtils from '../../../../utils/resolve-value-utils';
import * as selectUtils from '../select-utils';

describe('select utils tests', () => {
    const screenId = 'TestScreen';
    beforeEach(() => {
        jest.spyOn(resolveValueUtils, 'resolveByValue').mockImplementation(params => params.propertyValue);
    });

    describe('getItemsFromProps', () => {
        it('should create select item array from simple option array', () => {
            const result = selectUtils.getItemsFromProps({
                screenId,
                fieldProperties: { options: ['item1', 'item2', 'item3', 'item4'] },
            });
            expect(result).toEqual([
                {
                    id: 'item1',
                    value: 'item1',
                    displayedAs: 'item1',
                },
                {
                    id: 'item2',
                    value: 'item2',
                    displayedAs: 'item2',
                },
                {
                    id: 'item3',
                    value: 'item3',
                    displayedAs: 'item3',
                },
                {
                    id: 'item4',
                    value: 'item4',
                    displayedAs: 'item4',
                },
            ]);
        });

        it('should create sorted select item array from simple option array', () => {
            const result = selectUtils.getItemsFromProps({
                screenId,
                fieldProperties: { options: ['item4', 'item2', 'item3', 'item1'], isSortedAlphabetically: true },
            });
            expect(result).toEqual([
                {
                    id: 'item1',
                    value: 'item1',
                    displayedAs: 'item1',
                },
                {
                    id: 'item2',
                    value: 'item2',
                    displayedAs: 'item2',
                },
                {
                    id: 'item3',
                    value: 'item3',
                    displayedAs: 'item3',
                },
                {
                    id: 'item4',
                    value: 'item4',
                    displayedAs: 'item4',
                },
            ]);
        });

        it('should create select item array from enum option source with localized values', () => {
            const result = selectUtils.getItemsFromProps({
                screenId,
                fieldProperties: {
                    optionType: '@sage/test-package/MyEnum',
                    options: ['item1', 'item2', 'item3', 'item4'],
                },
                localizedOptions: { item1: 'D', item2: 'B', item3: 'C', item4: 'A' },
            });
            expect(result).toEqual([
                {
                    id: 'item1',
                    value: 'D',
                    displayedAs: 'D',
                },
                {
                    id: 'item2',
                    value: 'B',
                    displayedAs: 'B',
                },
                {
                    id: 'item3',
                    value: 'C',
                    displayedAs: 'C',
                },
                {
                    id: 'item4',
                    value: 'A',
                    displayedAs: 'A',
                },
            ]);
        });

        it('should create sorted select item array from enum option source with localized values', () => {
            const result = selectUtils.getItemsFromProps({
                screenId,
                fieldProperties: {
                    optionType: '@sage/test-package/MyEnum',
                    options: ['item1', 'item2', 'item3', 'item4'],
                    isSortedAlphabetically: true,
                },
                localizedOptions: { item1: 'D', item2: 'B', item3: 'C', item4: 'A' },
            });
            expect(result).toEqual([
                {
                    id: 'item4',
                    value: 'A',
                    displayedAs: 'A',
                },
                {
                    id: 'item2',
                    value: 'B',
                    displayedAs: 'B',
                },
                {
                    id: 'item3',
                    value: 'C',
                    displayedAs: 'C',
                },
                {
                    id: 'item1',
                    value: 'D',
                    displayedAs: 'D',
                },
            ]);
        });
    });
});
