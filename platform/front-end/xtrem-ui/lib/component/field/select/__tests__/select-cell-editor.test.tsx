import * as React from 'react';
import { render } from '@testing-library/react';
import { SelectCellEditor } from '../select-cell-editor';
import type { SelectEditorProps } from '../select-types';
import { CellWrapper } from '../../../ui/table-shared/cell/cell-wrapper';
import { CollectionValue } from '../../../../service/collection-data-service';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';
import * as nestedFields from '../../../nested-fields';

describe('select cell renderer', () => {
    let props: SelectEditorProps;
    beforeEach(() => {
        props = {
            locale: 'en-US',
            columnId: 'test-column-id',
            data: {},
            initialValue: '',
            value: '',
            onValueChange: jest.fn(),
            setTooltip: jest.fn(),
            valueFormatted: null,
            isParentFieldDisabled: false,
            column: {
                colDef: {
                    field: 'test-column-id',
                },
                columnApi: {
                    getAllColumns: jest.fn(() => [
                        {
                            field: 'test-column-id',
                        },
                    ]),
                } as any,
            } as any,
            screenId: 'test-screen',
            elementId: 'test-table',
            tableElementId: 'test-table',
            isTree: false,
            colDef: {
                context: {
                    screenId: 'test-screen',
                    columnId: 'test-column-id',
                    isEditable: () => false,
                },
                field: 'test-column-id',
                cellRendererParams: {
                    locale: 'en-US',
                    columnId: 'test-column-id',
                    elementId: 'test-table',
                    screenId: 'test-screen',
                    tableElementId: 'test-table',
                    isParentFieldDisabled: false,
                    fieldProperties: {
                        bind: 'test-column-id',
                        wrapper: CellWrapper,
                    },
                    isTree: false,
                    collectionValue: () =>
                        new CollectionValue({
                            screenId: 'test-screen',
                            elementId: 'testField',
                            isTransient: false,
                            hasNextPage: false,
                            orderBy: [{}],
                            columnDefinitions: [
                                [
                                    nestedFields.text<any, any>({ bind: '_id' }),
                                    nestedFields.text<any, any>({ bind: 'anyField' }),
                                ],
                            ],
                            nodeTypes: {},
                            nodes: ['@sage/xtrem-test/AnyNode'],
                            filter: [undefined],
                            initialValues: [],
                            fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                        }),
                },
            },
            eventKey: null,
            fieldProperties: {
                bind: 'test-column-id',
                wrapper: CellWrapper,
            },
            stopEditing: jest.fn(),
            getValue: jest.fn(),
            setValue: jest.fn(),
            formatValue: jest.fn(),
            refreshCell: jest.fn(),
            api: {
                paginationGetPageSize: () => 20,
                getColumns: jest.fn(() => [
                    {
                        field: 'test-column-id',
                    },
                ]),
            } as any,
            node: {
                rowIndex: 3,
            } as any,
            context: {} as any,
            eGridCell: {} as any,
            eParentOfValue: {} as any,
            registerRowDragger: () => {},
            collectionValue: () =>
                new CollectionValue({
                    screenId: 'test-screen',
                    elementId: 'testField',
                    isTransient: false,
                    hasNextPage: false,
                    orderBy: [{}],
                    columnDefinitions: [
                        [
                            nestedFields.text<any, any>({ bind: '_id' }),
                            nestedFields.text<any, any>({ bind: 'anyField' }),
                        ],
                    ],
                    nodeTypes: {},
                    nodes: ['@sage/xtrem-test/AnyNode'],
                    filter: [undefined],
                    initialValues: [],
                    fieldType: CollectionFieldTypes.DESKTOP_TABLE,
                }),
        };
    });

    it('should render with an empty value', () => {
        const component = render(<SelectCellEditor {...props} />);
        const input: any = component.getByTestId('test-table-3-0-input');
        expect(input).toHaveAttribute('placeholder', 'Please Select...');
        expect(input.value).toEqual('');
    });

    it('should not render if no data is provided', () => {
        props.data = null;
        const component = render(<SelectCellEditor {...props} />);
        const input: any = component.getByTestId('test-table-3-0-input');
        expect(input).toHaveAttribute('placeholder', 'Please Select...');
        expect(input.value).toEqual('');
    });

    it('should render a non-localized value', () => {
        props.initialValue = 'anyValue';
        const component = render(<SelectCellEditor {...props} />);
        const input = component.getByTestId('test-table-3-0-input') as HTMLInputElement;
        expect(input!.value).toEqual('anyValue');
    });

    it('should render a localized value', () => {
        props.initialValue = 'anyValue';
        props.enumOptions = ['anyValue', 'anotherValue'];
        props.localizedOptions = { anyValue: 'Any Value', anotherValue: 'Another Value' };
        const component = render(<SelectCellEditor {...props} />);
        const input: any = component.getByTestId('test-table-3-0-input');
        expect(input.value).toEqual('Any Value');
    });
});
