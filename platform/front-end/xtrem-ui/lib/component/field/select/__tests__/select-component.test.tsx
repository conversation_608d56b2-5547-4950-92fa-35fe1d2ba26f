import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    applyActionMocks,
    renderWithRedux,
} from '../../../../__tests__/test-helpers';

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import * as i18nService from '../../../../service/i18n-service';
import type { Dict } from '@sage/xtrem-shared';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import { ConnectedSelectComponent, SelectComponent } from '../select-component';
import type { SelectDecoratorProperties } from '../../../decorators';
import { fireEvent, render, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

jest.useFakeTimers();

describe('Select component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: SelectDecoratorProperties & { localizedOptions?: Dict<string> };
    const fieldId = 'test-select-field';
    let localizeSpy: jest.SpyInstance<string, [string, string]> | null = null;

    beforeEach(() => {
        jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue(
            () => Promise.resolve({ type: 'SetFieldValue' }) as any,
        );

        jest.spyOn(i18nService, 'localize').mockImplementation((_, value) => value);

        localizeSpy = jest.spyOn(i18nService, 'localizeEnumMember').mockImplementation(() => 'Molt be');
        mockFieldProperties = {
            title: 'Test Field Title',
            options: ['Option1', 'Option2', 'Option3'],
        };
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    describe('connected', () => {
        describe('integration', () => {
            const setup = (
                selectProps: SelectDecoratorProperties = mockFieldProperties,
                value: FieldInternalValue<FieldKey.Select> = '',
            ) => {
                const initialState = {
                    screenDefinitions: {
                        [screenId]: {
                            metadata: {
                                uiComponentProperties: {
                                    [fieldId]: selectProps,
                                },
                            },
                            ...(value && {
                                values: {
                                    [fieldId]: value,
                                },
                            }),
                        },
                    },
                };

                const utils = renderWithRedux<FieldKey.Select, any>(
                    <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />,
                    {
                        initialState,
                        fieldType: FieldKey.Select,
                        fieldValue: value,
                        fieldProperties: selectProps,
                        elementId: fieldId,
                        screenId,
                    },
                );

                const select = utils.getByTestId(
                    'e-select-field e-field-label-testFieldTitle e-field-bind-test-select-field',
                ) as HTMLDivElement;

                const input = select.querySelector(
                    'input[data-testid="e-select-field-input"]',
                ) as HTMLInputElement | null;
                const getIcon = (iconType: string) =>
                    select.querySelector(`span[data-component="icon"][data-element="${iconType}"]`);
                const getListItems = () => Array.from(select.querySelectorAll('li'));

                return {
                    ...utils,
                    getIcon,
                    input,
                    select,
                    getListItems,
                };
            };

            it('can render with redux with defaults', () => {
                const { select, input } = setup();
                expect(select).toHaveTextContent('Test Field Title');
                expect(input).toBeInTheDocument();
            });

            it('can render with icon', () => {
                const { select, input, getIcon } = setup({ ...mockFieldProperties, icon: 'scan' });
                expect(select).toHaveTextContent('Test Field Title');
                expect(input).toBeInTheDocument();
                expect(getIcon('scan')).toBeInTheDocument();
            });

            it('should set value on change', async () => {
                const { input, getListItems } = setup(mockFieldProperties, undefined);
                fireEvent.click(input!);
                let listItems: HTMLLIElement[] = [];
                await waitFor(() => {
                    listItems = getListItems();
                    expect(listItems).toHaveLength(3);
                });
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                fireEvent.click(listItems![1]!);
                await waitFor(() => {
                    expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledTimes(1);
                    expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(
                        'TestPage',
                        'test-select-field',
                        'Option2',
                        true,
                    );
                });
            });

            it('should display additional details if the mapDetails() property is implemented', async () => {
                const { input, getListItems } = setup(
                    {
                        ...mockFieldProperties,
                        mapDetails(value: string): string {
                            return `${value} details`;
                        },
                    },
                    undefined,
                );
                fireEvent.click(input!);
                let listItems: HTMLLIElement[] = [];
                await waitFor(() => {
                    listItems = getListItems();
                    expect(listItems).toHaveLength(3);
                });

                expect(listItems[0].querySelector('.e-ui-select-suggestion-helper')).toHaveTextContent(
                    'Option1 details',
                );
                expect(listItems[1].querySelector('.e-ui-select-suggestion-helper')).toHaveTextContent(
                    'Option2 details',
                );
                expect(listItems[2].querySelector('.e-ui-select-suggestion-helper')).toHaveTextContent(
                    'Option3 details',
                );
            });

            it('should set value on change when using an "options" callback', async () => {
                const { input, getListItems } = setup({
                    ...mockFieldProperties,
                    title: 'Test Field Title',
                    options: () => {
                        return ['OptionCallback1', 'OptionCallback2', 'OptionCallback3'];
                    },
                });
                fireEvent.click(input!);
                let listItems: HTMLLIElement[] = [];
                await waitFor(() => {
                    listItems = getListItems();
                    expect(listItems).toHaveLength(3);
                });
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                fireEvent.click(listItems![1]!);
                await waitFor(() => {
                    expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledTimes(1);
                    expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(
                        'TestPage',
                        'test-select-field',
                        'OptionCallback2',
                        true,
                    );
                });
            });

            it('should set the value if the user types in an existing label', async () => {
                const { input } = setup();
                fireEvent.change(input!, { target: { value: 'Option2' } });
                await waitFor(() => {
                    expect(input!.value).toBe('Option2');
                });
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                fireEvent.blur(input!, { target: { id: 'stuff' } });
                await waitFor(() => {
                    expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledTimes(1);
                    expect(xtremRedux.actions.setFieldValue).toHaveBeenNthCalledWith(
                        1,
                        'TestPage',
                        'test-select-field',
                        'Option2',
                        true,
                    );
                });
            });
        });

        describe('Snapshots', () => {
            let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

            beforeEach(() => {
                const state = getMockState();
                state.enumTypes.MyLocalizedEnum = ['Option1', 'bad'];
                state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
                addFieldToState(FieldKey.Select, state, screenId, fieldId, mockFieldProperties, 'Option1');
                addFieldToState(FieldKey.Select, state, screenId, 'test-empty-select-field', mockFieldProperties, null);
                mockStore = getMockStore(state);
            });

            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with localized content', () => {
                mockFieldProperties.optionType = '@any/any-package-name/MyLocalizedEnum';
                expect(localizeSpy).not.toHaveBeenCalled();
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(localizeSpy).toHaveBeenCalled();
                expect(localizeSpy).toHaveBeenCalledWith('@any/any-package-name/MyLocalizedEnum', 'Option1');
                expect(localizeSpy).toHaveBeenCalledWith('@any/any-package-name/MyLocalizedEnum', 'bad');

                expect(container.querySelector('input')!).toHaveValue('Molt be');
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render disabled when parent container is disabled', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} isParentDisabled={true} />
                    </Provider>,
                );

                expect(container.querySelector('input')).toBeDisabled();
            });

            it('should render in read only mode', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with a prefix', () => {
                mockFieldProperties.prefix = '$';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with a postfix', () => {
                mockFieldProperties.postfix = 'Kg';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with an info message', async () => {
                mockFieldProperties.infoMessage = 'Info message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an info message from a callback', async () => {
                mockFieldProperties.infoMessage = () => 'Info message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an warning message', async () => {
                mockFieldProperties.warningMessage = 'Warning message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with an warning message from a callback', async () => {
                mockFieldProperties.warningMessage = () => 'Warning message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with full-width', () => {
                mockFieldProperties.isFullWidth = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render in read-only mode with a prefix and a postfix', () => {
                mockFieldProperties.isReadOnly = true;
                mockFieldProperties.postfix = 'Kg';
                mockFieldProperties.prefix = '$';

                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render without value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId="test-empty-select-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with various field sizes', () => {
                mockFieldProperties.size = 'small';
                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'medium';
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'large';
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();
            });

            it('should localize its value in nested read-only mode', () => {
                mockFieldProperties.optionType = '@any/any-package-name/MyLocalizedEnum';
                expect(localizeSpy).not.toHaveBeenCalled();
                localizeSpy!.mockReturnValue('LOCALIZED VALUE');
                const { queryByTestId } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} nestedReadOnlyField={true} />
                    </Provider>,
                );

                expect(queryByTestId('e-field-value')).toHaveTextContent('LOCALIZED VALUE');
            });

            it('should render read-only using a conditional declaration', () => {
                mockFieldProperties.isReadOnly = () => {
                    return true;
                };

                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(wrapper.container.querySelector('input[readonly]')).not.toBeNull();

                mockFieldProperties.isReadOnly = () => {
                    return false;
                };

                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(wrapper.container.querySelector('input[readonly]')).toBeNull();
            });

            it('should render asterisk when the field is mandatory', () => {
                mockFieldProperties.isMandatory = false;

                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(wrapper.container.querySelector('label')).toHaveTextContent('Test Field Title');

                mockFieldProperties.isMandatory = true;

                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(wrapper.container.querySelector('label')).toHaveTextContent('Test Field Title *');
            });
        });

        describe('Interactions', () => {
            let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;
            beforeEach(() => {
                const state = getMockState();
                state.enumTypes.MyLocalizedEnum = ['Option1', 'bad'];
                state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
                addFieldToState(FieldKey.Select, state, screenId, fieldId, mockFieldProperties, 'Option1');
                addFieldToState(FieldKey.Select, state, screenId, 'test-empty-select-field', mockFieldProperties, null);
                mockStore = getMockStore(state);
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('Should not render helperText', () => {
                mockFieldProperties.helperText = '';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should set the value to null when user types in a non existing label and there is no previous valid selection', async () => {
                const state = getMockState();
                state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
                addFieldToState(FieldKey.Select, state, screenId, fieldId, mockFieldProperties, '');
                addFieldToState(FieldKey.Select, state, screenId, 'test-empty-select-field', mockFieldProperties, null);
                mockStore = getMockStore(state);
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                const input = container.querySelector('input')!;
                fireEvent.change(input, { target: { value: 'goo' } });
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                const relatedTarget = new EventTarget();
                fireEvent.blur(input, { relatedTarget });
                await waitFor(() => {
                    expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                });
            });

            it('should restore previous valid selection when user types in a non existing label and leaves the input', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                const input = container.querySelector('input')!;
                fireEvent.change(input, { target: { value: 'Option1' } });
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                const relatedTarget = new EventTarget();
                fireEvent.blur(input, { relatedTarget });
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                expect(input).toHaveValue('Option1');
            });

            it('should not set the value if the user types in the currently selected element', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                const input = container.querySelector('input')!;
                fireEvent.change(input, { target: { value: 'Option1' } });
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                const relatedTarget = new EventTarget();
                fireEvent.blur(input, { relatedTarget });

                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
            });

            it('should set the value to null if the user clears the input', async () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                const input = container.querySelector('input')!;

                fireEvent.change(input, { target: { value: '' } });
                const relatedTarget = new EventTarget();
                fireEvent.blur(input, { relatedTarget });

                await waitFor(() => {
                    expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(
                        'TestPage',
                        'test-select-field',
                        null,
                        true,
                    );
                });
            });

            /**
             * TODO: clarify expected behavior when Escape is hit.
             * https://www.w3.org/TR/wai-aria-practices/examples/combobox/aria1.1pattern/listbox-combo.html
             * recommends to clear the input. If we want a different behavior then this should also
             * be aligned to what the reference field & filter-select do (i.e. we should edit "lib/component/ui/select/select-component.tsx")
             */
            it('should reset to original value on Escape keydown', async () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                const input = container.querySelector('input')!;
                fireEvent.change(input, { target: { value: 'Option2' } });
                fireEvent.keyDown(input, { key: 'Escape' });

                await waitFor(() => {
                    expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                });
            });

            it('should consolidate value to store on Enter keydown', async () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedSelectComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                const input = container.querySelector('input')!;

                fireEvent.change(input, { target: { value: 'Option2' } });
                fireEvent.keyDown(input, { key: 'Enter' });

                await waitFor(() => {
                    expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledTimes(1);
                    expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(
                        'TestPage',
                        'test-select-field',
                        'Option2',
                        true,
                    );
                });
            });
        });
    });

    describe('unconnected', () => {
        describe('Snapshots', () => {
            it('should render just fine', () => {
                const value = 'Option1';
                const { container } = render(
                    <SelectComponent
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        value={value}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );
                expect(container).toMatchSnapshot();
            });
            it('should render just fine with localized content', () => {
                const value = 'veryGood';
                mockFieldProperties.optionType = '@any/any-package-name/MyEnum';
                mockFieldProperties.options = ['veryGood', 'bad'];
                const { container } = render(
                    <SelectComponent
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        localizedOptions={{ bad: 'Mal', veryGood: 'Molt be' }}
                        value={value}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );
                expect(container.querySelector('input')).toHaveValue('Molt be');
            });

            it('should set the value to null if options change', async () => {
                const value = 'Option1';
                const setFieldValueMock = jest.fn().mockResolvedValue(undefined);
                const { container, rerender } = render(
                    <SelectComponent
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        value={value}
                        setFieldValue={setFieldValueMock}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                expect(container.querySelector('input')).toHaveValue('Option1');

                rerender(
                    <SelectComponent
                        elementId={fieldId}
                        fieldProperties={{ ...mockFieldProperties, options: ['Option 4', 'Option 5'] }}
                        value={value}
                        setFieldValue={setFieldValueMock}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        locale="en-US"
                    />,
                );

                await waitFor(() => {
                    expect(setFieldValueMock).toHaveBeenCalledTimes(1);
                    expect(setFieldValueMock).toHaveBeenNthCalledWith(1, 'test-select-field', null);
                });
            });
        });
    });
});
