/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode, Dict } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import { showToast } from '../../../service/toast-service';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { FilterEditorProperties } from './filter-editor-types';
import type { DefaultPropertyType, FilterParameter } from '@sage/xtrem-ui-components';

/**
 * [Field]{@link EditableFieldControlObject} that holds a value from a set of given values or sets a new value to be
 * saved later. The type of GraphQL object must be specified through the 'node' property, while the 'valueField' and
 * 'helperTextField' properties define which properties of the GraphQL object will be displayed in the field and will
 * be matched against the user provided text.
 */
export class FilterEditorControlObject<
    ReferencedItemType extends ClientNode = any,
    CT extends ScreenBase = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.FilterEditor, FieldComponentProps<FieldKey.FilterEditor>> {
    static readonly defaultUiProperties: Partial<FilterEditorProperties> = {
        parameterMode: 'creation',
        filterParameters: [],
        mode: 'table',
        ...EditableFieldControlObject.defaultUiProperties,
    };

    @ControlObjectProperty<FilterEditorProperties<CT>, FilterEditorControlObject<ReferencedItemType, CT>>()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<FilterEditorProperties<CT>, FilterEditorControlObject<ReferencedItemType, CT>>()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    @ControlObjectProperty<FilterEditorProperties<CT>, FilterEditorControlObject<ReferencedItemType, CT>>()
    selectedProperties?: Dict<DefaultPropertyType>;

    @ControlObjectProperty<FilterEditorProperties<CT>, FilterEditorControlObject<ReferencedItemType, CT>>()
    filterParameters?: FilterParameter[];

    @ControlObjectProperty<FilterEditorProperties<CT>, FilterEditorControlObject<ReferencedItemType, CT>>()
    parameterMode?: 'creation' | 'usage';

    @ControlObjectProperty<FilterEditorProperties<CT>, FilterEditorControlObject<ReferencedItemType, CT>>()
    node?: string;

    @ControlObjectProperty<FilterEditorProperties<CT>, FilterEditorControlObject<ReferencedItemType, CT>>()
    mode?: 'table' | 'pod';

    /** Refetches data from the server */
    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }
}
