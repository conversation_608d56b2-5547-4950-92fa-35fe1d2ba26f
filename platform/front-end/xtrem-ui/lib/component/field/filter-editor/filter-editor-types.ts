import type { Dict, FilterProperty } from '@sage/xtrem-shared';
import type { DefaultPropertyType, FilterParameter } from '@sage/xtrem-ui-components';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type { Changeable, Clickable, ExtensionField, HasParent, HasPlaceholder, Sizable } from '../traits';

export interface FilterEditorProperties<CT extends ScreenExtension<CT> = ScreenBase, NodeType = void>
    extends EditableFieldProperties<CT, NodeType>,
        HasPlaceholder,
        Sizable {
    /** The GraphQL node that the field suggestions will be fetched from */
    node?: string;

    /** List of properties that are displayed as fields for the filter */
    selectedProperties?: Dict<DefaultPropertyType>;

    /** External filter parameters, ignored in parameter usage mode */
    filterParameters?: FilterParameter[];

    /** Whether the filter should create new parameters or should display a list of existing parameters */
    parameterMode?: 'creation' | 'usage';

    /** Sets the way the filter editor is displayed. Default table */
    mode?: 'table' | 'pod';

    /** Action callback for Property column */
    propertyAction?: (this: CT) => void;

    /** Custom label for Property column action */
    propertyActionLabel?: string;

    /** Action callback for Value column */
    filterValueAction?: (this: CT) => void;

    /** Custom label for Value column action */
    filterValueActionLabel?: string;
}
export interface FilterEditorDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<FilterEditorProperties<CT>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>>,
        Sizable {
    /** Ensures that all columns sized automatically  */
    automaticColumnsSpacing?: boolean;
    /** Whether the parent column is hidden in the property picker dropdown */
    isParentColumnHidden?: boolean;
}

export type FilterEditorExtensionDecoratorProperties<CT extends ScreenExtension<CT>> =
    ChangeableOverrideDecoratorProperties<FilterEditorDecoratorProperties<Extend<CT>>, CT>;

export type FilterEditorComponentProps = BaseEditableComponentProperties<
    FilterEditorDecoratorProperties<any>,
    FilterEditorFieldValue
>;

export interface FilterEditorFieldValue {
    filters: (FilterProperty & { _id: string })[];
    parameters: FilterParameter[];
}
