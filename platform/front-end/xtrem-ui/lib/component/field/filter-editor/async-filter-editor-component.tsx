import * as React from 'react';
import type { FilterEditorComponentProps } from './filter-editor-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import type { FieldComponentExternalProperties } from '../field-base-component-types';

const ConnectedFilterEditorComponent = React.lazy(() => import('./filter-editor-component'));

export function AsyncConnectedFilterEditorComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedFilterEditorComponent {...props} />
        </React.Suspense>
    );
}

const FilterEditorComponent = React.lazy(() =>
    import('./filter-editor-component').then(c => ({ default: c.FilterEditorComponent })),
);

export function AsyncFilterEditorComponent(props: FilterEditorComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <FilterEditorComponent {...props} />
        </React.Suspense>
    );
}
