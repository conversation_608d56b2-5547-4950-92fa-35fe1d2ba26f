PATH: XTREEM/UI+Field+Widgets/Filter+Editor+Field

## Introduction

Filter editor field is meant for usage in wizards for example for data export and import. It can be bound to a JSON property on the server.

## Example:

```ts

@ui.decorators.filterEditorField<FilterEditor>({
    parent() {
        return this.fieldBlock;
    },
    isFullWidth: true,
    node: 'ShowCaseEmployee',
    selectedProperties: {
        lastName: {
            label: 'Last name',
            data: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
                name: 'lastName',
                canFilter: true,
                canSort: true,
                label: 'Last name',
            },
            id: 'lastName',
            key: 'lastName',
            labelKey: 'Last name',
            labelPath: 'Last name',
        },
        firstName: {
            label: 'First name',
            data: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
                name: 'firstName',
                canFilter: true,
                canSort: true,
                label: 'First name',
            },
            id: 'firstName',
            key: 'firstName',
            labelKey: 'First name',
            labelPath: 'First name',
        },
    },
    onChange() {
       console.log("The filters have changed!")
    },
})
field: ui.fields.FilterEditor;

```

### Display decorator properties

-   **isDisabled**: Whether the File is able to trigger onClick decorator property (isDisabled = false) or not (isDisabled = true). A File is enabled by default. It can also be defined as callback function that returns a boolean.
-   **isHidden**: Whether the File is visible (isHidden = false) or not (isHidden = true). A File is visible by default.
-   **isFullWidth**: Whether a field should take the full width of the screen.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **helperText**: The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **node**: The node type that the properties belong to.
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **selectedProperties**: An array with the selected properties that the user can choose from. See the example above for format details.
-   **parameterMode**: Whether the filter should create new parameters or should display a list of existing parameters.
-   **filterParameters**: External filter parameters, ignored in parameter usage mode.
-   **propertyAction**: Action callback for Property column.
-   **propertyActionLabel**: Custom label for Property column action.
-   **filterValueAction**: Action callback for Value column.
-   **filterValueActionLabel**: Custom label for Value column action.

### Binding decorator properties

-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

### Event handler decorator properties

-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.
-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

### Other decorator properties

-   **fetchesDefaults**: When set to true and when the file value changes, a request to the server for default values for the whole page will be requested. False by default.

## Runtime functions

-   **focus()**: Moves the focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/FilterEditor).
