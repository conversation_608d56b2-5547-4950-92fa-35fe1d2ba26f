import { GraphQLTypes } from '@sage/xtrem-shared';
import type { Page } from '../../../..';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { filterEditorField } from '../filter-editor-decorator';
import type { FilterEditorDecoratorProperties } from '../filter-editor-types';

describe('FilterEditor decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'filterEditorField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should set default values when no component properties provided', () => {
        filterEditorField({})({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: FilterEditorDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as FilterEditorDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onChange).toBeUndefined();
        expect(mappedComponentProperties.onClick).toBeUndefined();
    });

    it('should inherit false abstract-field booleans when no provided', () => {
        filterEditorField({})({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: FilterEditorDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as FilterEditorDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.isHiddenMobile).toBe(false);
        expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
        expect(mappedComponentProperties.isFullWidth).toBe(false);
        expect(mappedComponentProperties.isHidden).toBe(false);
        expect(mappedComponentProperties.isTransient).toBe(false);
    });

    it('should set values when component properties provided', () => {
        const clickFunc: () => void = jest.fn().mockImplementation(() => {});
        const changeFunc: () => void = jest.fn().mockImplementation(() => {});

        filterEditorField({
            onChange: changeFunc,
            onClick: clickFunc,
            helperText: 'helper text',
            title: 'title',
            parameterMode: 'creation',
            filterParameters: [{ name: 'test', type: GraphQLTypes.Decimal, label: 'Test' }],
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: FilterEditorDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as FilterEditorDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onChange).not.toBeUndefined();
        expect(mappedComponentProperties.onChange).toBe(changeFunc);
        expect(mappedComponentProperties.onClick).not.toBeUndefined();
        expect(mappedComponentProperties.onClick).toBe(clickFunc);
        expect(mappedComponentProperties.helperText).toBe('helper text');
        expect(mappedComponentProperties.title).toBe('title');
        expect(mappedComponentProperties.parameterMode).toEqual('creation');
        expect(mappedComponentProperties.filterParameters).toEqual([
            { name: 'test', type: GraphQLTypes.Decimal, label: 'Test' },
        ]);
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(filterEditorField, pageMetadata, fieldId);
        });
    });
});
