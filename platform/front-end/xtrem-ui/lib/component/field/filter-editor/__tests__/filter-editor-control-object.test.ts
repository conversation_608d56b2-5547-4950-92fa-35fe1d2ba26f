import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import { FilterEditorControlObject } from '../../../control-objects';
import type { FieldKey } from '../../../types';
import type { FilterEditorFieldValue, FilterEditorProperties } from '../filter-editor-types';

describe('FilterEditor Field', () => {
    let fileField: FilterEditorControlObject;
    let fieldProperties: FilterEditorProperties;

    const fileValue: FilterEditorFieldValue = {
        filters: [],
        parameters: [],
    };

    beforeEach(() => {
        fieldProperties = {
            title: 'TEST_FIELD_TITLE',
        };
    });

    describe('getters and setters', () => {
        beforeEach(() => {
            fileField = buildControlObject<FieldKey.FilterEditor>(FilterEditorControlObject, {
                fieldValue: fileValue,
                fieldProperties,
            });
        });

        it('getting field value', () => {
            expect(fileField.value).toEqual(fileValue);
        });

        it('getting isReadOnly', () => {
            expect(fileField.isReadOnly).toBeFalsy();
        });

        it('should set the title', () => {
            const testFixture = 'Test FilterEditor Field Title';
            expect(fieldProperties.title).not.toEqual(testFixture);
            fileField.title = testFixture;
            expect(fieldProperties.title).toEqual(testFixture);
        });

        it('should set the helper text', () => {
            const testFixture = 'Test FilterEditor Field Helper Text';
            expect(fieldProperties.helperText).not.toEqual(testFixture);
            fileField.helperText = testFixture;
            expect(fieldProperties.helperText).toEqual(testFixture);
        });

        it('should set isDisabled property', () => {
            const testFixture = true;
            expect(fieldProperties.isDisabled).not.toEqual(testFixture);
            fileField.isDisabled = testFixture;
            expect(fieldProperties.isDisabled).toEqual(testFixture);
        });

        it('should set isHelperTextHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHelperTextHidden).not.toEqual(testFixture);
            fileField.isHelperTextHidden = testFixture;
            expect(fieldProperties.isHelperTextHidden).toEqual(testFixture);
        });

        it('should set isHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHidden).not.toEqual(testFixture);
            fileField.isHidden = testFixture;
            expect(fieldProperties.isHidden).toEqual(testFixture);
        });

        it('should set isReadOnly property', () => {
            const testFixture = true;
            expect(fieldProperties.isReadOnly).not.toEqual(testFixture);
            fileField.isReadOnly = testFixture;
            expect(fieldProperties.isReadOnly).toEqual(testFixture);
        });

        it('should set isTitleHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isTitleHidden).not.toEqual(testFixture);
            fileField.isTitleHidden = testFixture;
            expect(fieldProperties.isTitleHidden).toEqual(testFixture);
        });
    });

    describe('focus', () => {
        const focus = jest.fn();
        beforeEach(() => {
            fileField = buildControlObject<FieldKey.FilterEditor>(FilterEditorControlObject, {
                fieldValue: fileValue,
                fieldProperties,
                focus,
            });
        });

        it('should execute focus', () => {
            jest.useFakeTimers();
            expect(focus).not.toHaveBeenCalled();
            fileField.focus();
            jest.runAllTimers();
            expect(focus).toHaveBeenCalled();
        });
    });
});
