import * as React from 'react';
import { applyActionMocks, renderWithRedux, userEvent } from '../../../../__tests__/test-helpers';
import type { ScreenBase } from '../../../../service/screen-base';
import type { FieldInternalValue } from '../../../types';
import { FieldKey, GraphQLTypes } from '../../../types';
import ConnectedFilterEditorComponent from '../filter-editor-component';
import type { FilterEditorDecoratorProperties, FilterEditorFieldValue } from '../filter-editor-types';
import type { XtremAppState } from '../../../../redux/state';
import type { DeepPartial } from 'ts-essentials';
import type { Dict } from '@sage/xtrem-shared';
import { fireEvent, waitFor } from '@testing-library/react';
import * as abstractFieldUtils from '../../../../utils/abstract-fields-utils';
import type { FormattedNodeDetails } from '../../../../service/metadata-types';
import { GraphQLKind } from '../../../../types';
import '@testing-library/jest-dom';

jest.useFakeTimers();

const nodeTypes: Dict<FormattedNodeDetails> = {
    ShowCaseEmployee: {
        name: 'ShowCaseEmployee',
        title: 'ShowCaseEmployee',
        packageName: '@sage/xtrem-test',
        properties: {
            _access: {
                type: '_OutputAccessBinding',
                kind: GraphQLKind.List,
                isCollection: false,
            },
            _customData: {
                type: 'Json',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _sourceId: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _id: {
                type: 'Id',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            firstName: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            lastName: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            city: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            email: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            country: {
                type: 'ShowCaseCountry',
                kind: 'OBJECT',
                isCollection: false,
            },
            manager: {
                type: 'ShowCaseEmployee',
                kind: 'OBJECT',
                isCollection: false,
            },
            teamMembers: {
                type: 'ShowCaseEmployee',
                kind: 'OBJECT',
                isCollection: true,
            },
            _etag: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _createStamp: {
                type: 'Datetime',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _updateStamp: {
                type: 'Datetime',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
        },
        mutations: {},
    },
    ShowCaseCountry: {
        name: 'ShowCaseCountry',
        title: 'ShowCaseCountry',
        packageName: '@sage/xtrem-test',
        properties: {
            _access: {
                type: '_OutputAccessBinding',
                kind: GraphQLKind.List,
                isCollection: false,
            },
            _customData: {
                type: 'Json',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _sourceId: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _id: {
                type: 'Id',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            code: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            phoneCountryCode: {
                type: GraphQLTypes.Int,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            name: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            employeesInCountry: {
                type: 'ShowCaseEmployee',
                kind: 'OBJECT',
                isCollection: true,
            },
            _etag: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _createStamp: {
                type: 'Datetime',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
            _updateStamp: {
                type: 'Datetime',
                kind: GraphQLKind.Scalar,
                isCollection: false,
            },
        },
        mutations: {},
    },
};
const filterEditorValue: FilterEditorFieldValue = {
    filters: [],
    parameters: [],
};

const defaultFilterEditorProps: Partial<FilterEditorDecoratorProperties<ScreenBase>> = {
    node: 'ShowCaseEmployee',
    selectedProperties: {
        lastName: {
            label: 'Last name',
            data: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
                name: 'lastName',
                canFilter: true,
                canSort: true,
                label: 'Last name',
                dataType: '',
                enumType: '',
                isCustom: false,
                isStored: true,
                isOnInputType: true,
                isOnOutputType: true,
                targetNode: '',
                isMutable: false,
            },
            id: 'lastName',
            key: 'lastName',
            labelKey: 'Last name',
            labelPath: 'Last name',
        },
        firstName: {
            label: 'First name',
            data: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
                name: 'firstName',
                canFilter: true,
                canSort: true,
                label: 'First name',
                dataType: '',
                enumType: '',
                isCustom: false,
                isStored: true,
                isOnInputType: true,
                isOnOutputType: true,
                targetNode: '',
                isMutable: false,
            },
            id: 'firstName',
            key: 'firstName',
            labelKey: 'First name',
            labelPath: 'First name',
        },
        email: {
            label: 'Email',
            data: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
                name: 'email',
                canFilter: true,
                canSort: true,
                label: 'Email',
                dataType: '',
                enumType: '',
                isCustom: false,
                isStored: true,
                isOnInputType: true,
                isOnOutputType: true,
                targetNode: '',
                isMutable: false,
            },
            id: 'email',
            key: 'email',
            labelKey: 'Email',
            labelPath: 'Email',
        },
        city: {
            label: 'City',
            data: {
                type: GraphQLTypes.String,
                kind: GraphQLKind.Scalar,
                isCollection: false,
                name: 'city',
                canFilter: true,
                canSort: true,
                label: 'City',
                dataType: '',
                enumType: '',
                isCustom: false,
                isStored: true,
                isOnInputType: true,
                isOnOutputType: true,
                targetNode: '',
                isMutable: false,
            },
            id: 'city',
            key: 'city',
            labelKey: 'City',
            labelPath: 'City',
        },
    },
};

describe('filter-editor component', () => {
    let handleChangeSpy: jest.SpyInstance;

    beforeEach(() => {
        handleChangeSpy = jest.spyOn(abstractFieldUtils, 'handleChange');
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.resetAllMocks();
        jest.useRealTimers();
        applyActionMocks();
    });

    const screenId = 'TestPage';
    const fieldId = 'test-filter-editor-field';

    const setup = (
        filterEditorProps: Partial<FilterEditorDecoratorProperties<ScreenBase>> = {},
        value: FieldInternalValue<FieldKey.FilterEditor> = filterEditorValue,
    ) => {
        const fieldProperties = { ...defaultFilterEditorProps, ...filterEditorProps };

        const initialState: DeepPartial<XtremAppState> = {
            nodeTypes,
            screenDefinitions: {
                [screenId]: {
                    metadata: {
                        uiComponentProperties: {
                            [fieldId]: fieldProperties,
                        },
                    },
                    values: {
                        ...(value && { [fieldId]: value }),
                    },
                    errors: {},
                },
            },
        };
        const utils = renderWithRedux<FieldKey.FilterEditor, any>(
            <ConnectedFilterEditorComponent screenId={screenId} elementId={fieldId} />,
            {
                initialState,
                fieldType: FieldKey.FilterEditor,
                fieldValue: value,
                fieldProperties,
                elementId: fieldId,
                screenId,
                mockActions: true,
            },
        );

        const filterEditorField = utils.getByTestId('e-field-bind-test-filter-editor-field', { exact: false });

        const getPropertyCell = async (rowId: number) => {
            return utils.findByTestId(`e-widget-editor-filter-property-${rowId}`) as Promise<HTMLInputElement>;
        };

        const setPropertyCell = async ({ rowId, value }: { rowId: number; value: string }) => {
            await userEvent.type(await getPropertyCell(rowId), value);
            await waitFor(async () => {
                expect((await getPropertyCell(rowId)).value).toBe(value);
            });
        };

        const getTypeCell = async (rowId: number) => {
            return utils.findByTestId(`e-widget-editor-filter-type-${rowId}`) as Promise<HTMLInputElement>;
        };

        const setTypeCell = async ({ rowId, value }: { rowId: number; value: string }) => {
            await userEvent.type(await getTypeCell(rowId), value);
            await waitFor(async () => {
                expect((await getTypeCell(rowId)).value).toBe(value);
            });
        };

        const getParameterCell = async (rowId: number) => {
            return utils.findByTestId(`e-widget-editor-filter-parameter-${rowId}`) as Promise<HTMLInputElement>;
        };

        const setParameterCell = async ({
            rowId,
            value,
            expectedRowId = rowId,
        }: {
            rowId: number;
            value: boolean;
            expectedRowId?: number;
        }) => {
            const parameterCell = await getParameterCell(rowId);
            if (parameterCell.checked === value) {
                return;
            }
            await waitFor(async () => {
                await userEvent.click(parameterCell);
                expect((await getParameterCell(expectedRowId)).checked).toBe(value);
            });
        };

        const getValueCell = async (rowId: number) => {
            return utils.findByTestId(`e-widget-editor-filter-value-${rowId}`) as Promise<HTMLInputElement>;
        };

        const setValueCell = async ({ rowId, value }: { rowId: number; value: string }) => {
            await userEvent.type(await getValueCell(rowId), value);
            await waitFor(async () => {
                expect(((await getValueCell(rowId)) as HTMLInputElement).value).toBe(value);
            });
        };

        return {
            ...utils,
            filterEditorField,
            getPropertyCell,
            setPropertyCell,
            getTypeCell,
            getParameterCell,
            getValueCell,
            setTypeCell,
            setParameterCell,
            setValueCell,
        };
    };

    it('can render with redux with defaults', async () => {
        const { filterEditorField, findByTestId } = setup({ title: 'Test Field Title' });
        expect(filterEditorField).toHaveTextContent('Test Field Title');
        expect(filterEditorField).toHaveTextContent('No data to display');
        expect((await findByTestId('add-item-button')).textContent).toBe('Add filter');
    });

    it('can render with redux without selected properties', async () => {
        const { filterEditorField, queryByTestId } = setup({ title: 'Test Field Title', selectedProperties: {} });
        expect(filterEditorField).toHaveTextContent('Test Field Title');
        expect(filterEditorField).toHaveTextContent('You cannot filter the current values');
        expect(filterEditorField).toHaveTextContent('You can select different data or continue without filtering');
        expect(queryByTestId('add-item-button')).toBe(null);
    });

    it('can add multiple filters with and without parameters', async () => {
        const {
            findByTestId,
            queryByTestId,
            findAllByTestId,
            setPropertyCell,
            getPropertyCell,
            getTypeCell,
            getParameterCell,
            getValueCell,
            setTypeCell,
            setParameterCell,
            setValueCell,
        } = setup();
        expect(queryByTestId('1')).toBe(null);
        fireEvent.click(await findByTestId('add-item-button'));
        await findByTestId('1');
        const addButtons = await findAllByTestId('flat-table-add-button');
        expect(addButtons.length).toBe(1);
        const removeButtons = await findAllByTestId('flat-table-remove-button');
        expect(removeButtons.length).toBe(1);

        const propertyCell = await getPropertyCell(1);
        expect(propertyCell.disabled).toBe(false);

        const typeCell = await getTypeCell(1);
        expect(typeCell.disabled).toBe(true);

        const parameterCell = await getParameterCell(1);
        expect(parameterCell.disabled).toBe(false);

        const filterValueCell = await getValueCell(1);
        expect(filterValueCell.disabled).toBe(true);

        expect(handleChangeSpy).not.toHaveBeenCalled();

        // set property
        await setPropertyCell({ rowId: 1, value: 'Last name' });
        expect(handleChangeSpy).not.toHaveBeenCalled();

        // set filter type
        await setTypeCell({ rowId: 1, value: 'Contains' });
        expect(handleChangeSpy).not.toHaveBeenCalled();

        // set parameter
        await setParameterCell({ rowId: 1, value: true });

        await waitFor(() => {
            expect(handleChangeSpy).toHaveBeenCalledTimes(1);
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                1,
                'test-filter-editor-field',
                {
                    filters: [
                        {
                            _id: '1',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'lastName',
                            id: 'lastName',
                            key: undefined,
                            label: 'Last name',
                            labelKey: undefined,
                            labelPath: 'Last name',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'Last name',
                                    name: 'lastName',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'lastName',
                                key: 'lastName',
                                label: 'Last name',
                                labelKey: 'Last name',
                                labelPath: 'Last name',
                            },
                        },
                    ],
                    parameters: [{ label: 'Last name', name: 'lastName', type: 'String' }],
                },
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
        });

        // add second row
        expect(queryByTestId('2')).toBe(null);
        await userEvent.click(await findByTestId('flat-table-add-button'));
        await findByTestId('2');

        // set property
        await setPropertyCell({ rowId: 2, value: 'First name' });
        expect(handleChangeSpy).toHaveBeenCalledTimes(1);

        // set filter type
        await setTypeCell({ rowId: 2, value: 'Contains' });
        expect(handleChangeSpy).toHaveBeenCalledTimes(1);

        // set parameter
        await setParameterCell({ rowId: 2, value: true });

        await waitFor(() => {
            expect(handleChangeSpy).toHaveBeenCalledTimes(2);
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                2,
                'test-filter-editor-field',
                {
                    filters: [
                        {
                            _id: '1',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'lastName',
                            id: 'lastName',
                            key: undefined,
                            label: 'Last name',
                            labelKey: undefined,
                            labelPath: 'Last name',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'Last name',
                                    name: 'lastName',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'lastName',
                                key: 'lastName',
                                label: 'Last name',
                                labelKey: 'Last name',
                                labelPath: 'Last name',
                            },
                        },
                        {
                            _id: '2',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'firstName',
                            id: 'firstName',
                            key: undefined,
                            label: 'First name',
                            labelKey: undefined,
                            labelPath: 'First name',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'First name',
                                    name: 'firstName',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'firstName',
                                key: 'firstName',
                                label: 'First name',
                                labelKey: 'First name',
                                labelPath: 'First name',
                            },
                        },
                    ],
                    parameters: [
                        { label: 'Last name', name: 'lastName', type: 'String' },
                        { label: 'First name', name: 'firstName', type: 'String' },
                    ],
                },
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
        });

        // add third row
        expect(queryByTestId('3')).toBe(null);
        await userEvent.click(await findByTestId('flat-table-add-button'));
        await findByTestId('3');

        // set property
        await setPropertyCell({ rowId: 3, value: 'Email' });
        expect(handleChangeSpy).toHaveBeenCalledTimes(2);

        // set filter type
        await setTypeCell({ rowId: 3, value: 'Contains' });
        expect(handleChangeSpy).toHaveBeenCalledTimes(2);

        // set parameter
        await setParameterCell({ rowId: 3, value: true });

        await waitFor(() => {
            expect(handleChangeSpy).toHaveBeenCalledTimes(3);
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                3,
                'test-filter-editor-field',
                {
                    filters: [
                        {
                            _id: '1',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'lastName',
                            id: 'lastName',
                            key: undefined,
                            label: 'Last name',
                            labelKey: undefined,
                            labelPath: 'Last name',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'Last name',
                                    name: 'lastName',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'lastName',
                                key: 'lastName',
                                label: 'Last name',
                                labelKey: 'Last name',
                                labelPath: 'Last name',
                            },
                        },
                        {
                            _id: '2',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'firstName',
                            id: 'firstName',
                            key: undefined,
                            label: 'First name',
                            labelKey: undefined,
                            labelPath: 'First name',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'First name',
                                    name: 'firstName',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'firstName',
                                key: 'firstName',
                                label: 'First name',
                                labelKey: 'First name',
                                labelPath: 'First name',
                            },
                        },
                        {
                            _id: '3',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Email',
                                name: 'email',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'email',
                            id: 'email',
                            key: undefined,
                            label: 'Email',
                            labelKey: undefined,
                            labelPath: 'Email',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'Email',
                                    name: 'email',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'email',
                                key: 'email',
                                label: 'Email',
                                labelKey: 'Email',
                                labelPath: 'Email',
                            },
                        },
                    ],
                    parameters: [
                        { label: 'Last name', name: 'lastName', type: 'String' },
                        { label: 'First name', name: 'firstName', type: 'String' },
                        { label: 'Email', name: 'email', type: 'String' },
                    ],
                },
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
        });

        // uncheck parameter for last name
        await setParameterCell({ rowId: 1, value: false, expectedRowId: 3 });
        await waitFor(() => {
            expect(handleChangeSpy).toHaveBeenCalledTimes(5);
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                4,
                'test-filter-editor-field',
                {
                    filters: [
                        {
                            _id: '2',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'firstName',
                            id: 'firstName',
                            key: undefined,
                            label: 'First name',
                            labelKey: undefined,
                            labelPath: 'First name',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'First name',
                                    name: 'firstName',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'firstName',
                                key: 'firstName',
                                label: 'First name',
                                labelKey: 'First name',
                                labelPath: 'First name',
                            },
                        },
                        {
                            _id: '3',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Email',
                                name: 'email',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'email',
                            id: 'email',
                            key: undefined,
                            label: 'Email',
                            labelKey: undefined,
                            labelPath: 'Email',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'Email',
                                    name: 'email',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'email',
                                key: 'email',
                                label: 'Email',
                                labelKey: 'Email',
                                labelPath: 'Email',
                            },
                        },
                    ],
                    parameters: [
                        {
                            label: 'First name',
                            name: 'firstName',
                            type: GraphQLTypes.String,
                        },
                        {
                            label: 'Email',
                            name: 'email',
                            type: GraphQLTypes.String,
                        },
                    ],
                },
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                5,
                'test-filter-editor-field',
                {
                    filters: [
                        {
                            _id: '1',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'firstName',
                            id: 'firstName',
                            key: undefined,
                            label: 'First name',
                            labelKey: undefined,
                            labelPath: 'First name',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'First name',
                                    name: 'firstName',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'firstName',
                                key: 'firstName',
                                label: 'First name',
                                labelKey: 'First name',
                                labelPath: 'First name',
                            },
                        },
                        {
                            _id: '2',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Email',
                                name: 'email',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'email',
                            id: 'email',
                            key: undefined,
                            label: 'Email',
                            labelKey: undefined,
                            labelPath: 'Email',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'Email',
                                    name: 'email',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'email',
                                key: 'email',
                                label: 'Email',
                                labelKey: 'Email',
                                labelPath: 'Email',
                            },
                        },
                    ],
                    parameters: [
                        { label: 'First name', name: 'firstName', type: 'String' },
                        { label: 'Email', name: 'email', type: 'String' },
                    ],
                },
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
        });

        // check that last name is still visible at the bottom
        await waitFor(async () => {
            expect((await getPropertyCell(3)).value).toBe('Last name');
            expect((await getTypeCell(3)).value).toBe('Contains');
            expect((await getParameterCell(3)).checked).toBe(false);
            const valueCell = await getValueCell(3);
            expect(valueCell.disabled).toBe(false);
            expect(valueCell.value).toBe('');
        });

        // set value for last name
        await setValueCell({ rowId: 3, value: 'f' });
        await waitFor(() => {
            expect(handleChangeSpy).toHaveBeenCalledTimes(6);
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                6,
                'test-filter-editor-field',
                {
                    filters: [
                        {
                            _id: '1',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'firstName',
                            id: 'firstName',
                            key: undefined,
                            label: 'First name',
                            labelKey: undefined,
                            labelPath: 'First name',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'First name',
                                    name: 'firstName',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'firstName',
                                key: 'firstName',
                                label: 'First name',
                                labelKey: 'First name',
                                labelPath: 'First name',
                            },
                        },
                        {
                            _id: '2',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Email',
                                name: 'email',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'email',
                            id: 'email',
                            key: undefined,
                            label: 'Email',
                            labelKey: undefined,
                            labelPath: 'Email',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'Email',
                                    name: 'email',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'email',
                                key: 'email',
                                label: 'Email',
                                labelKey: 'Email',
                                labelPath: 'Email',
                            },
                        },
                        {
                            _id: '3',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'f',
                            id: 'lastName',
                            key: undefined,
                            label: 'Last name',
                            labelKey: undefined,
                            labelPath: 'Last name',
                            parameter: false,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'Last name',
                                    name: 'lastName',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'lastName',
                                key: 'lastName',
                                label: 'Last name',
                                labelKey: 'Last name',
                                labelPath: 'Last name',
                            },
                        },
                    ],
                    parameters: [
                        {
                            label: 'First name',
                            name: 'firstName',
                            type: GraphQLTypes.String,
                        },
                        {
                            label: 'Email',
                            name: 'email',
                            type: GraphQLTypes.String,
                        },
                    ],
                },
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
        });

        // recheck parameter switch
        await setParameterCell({ rowId: 3, value: true });
        await waitFor(() => {
            expect(handleChangeSpy).toHaveBeenCalledTimes(7);
            expect(handleChangeSpy).toHaveBeenNthCalledWith(
                7,
                'test-filter-editor-field',
                {
                    filters: [
                        {
                            _id: '1',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'First name',
                                name: 'firstName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'firstName',
                            id: 'firstName',
                            key: undefined,
                            label: 'First name',
                            labelKey: undefined,
                            labelPath: 'First name',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'First name',
                                    name: 'firstName',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'firstName',
                                key: 'firstName',
                                label: 'First name',
                                labelKey: 'First name',
                                labelPath: 'First name',
                            },
                        },
                        {
                            _id: '2',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Email',
                                name: 'email',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'email',
                            id: 'email',
                            key: undefined,
                            label: 'Email',
                            labelKey: undefined,
                            labelPath: 'Email',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'Email',
                                    name: 'email',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'email',
                                key: 'email',
                                label: 'Email',
                                labelKey: 'Email',
                                labelPath: 'Email',
                            },
                        },
                        {
                            _id: '3',
                            data: {
                                canFilter: true,
                                canSort: true,
                                isCollection: false,
                                kind: GraphQLKind.Scalar,
                                label: 'Last name',
                                name: 'lastName',
                                type: GraphQLTypes.String,
                                dataType: '',
                                enumType: '',
                                isCustom: false,
                                isStored: true,
                                isOnInputType: true,
                                isOnOutputType: true,
                                targetNode: '',
                                isMutable: false,
                            },
                            filterType: 'contains',
                            filterValue: 'lastName',
                            id: 'lastName',
                            key: undefined,
                            label: 'Last name',
                            labelKey: undefined,
                            labelPath: 'Last name',
                            parameter: true,
                            property: {
                                data: {
                                    canFilter: true,
                                    canSort: true,
                                    isCollection: false,
                                    kind: GraphQLKind.Scalar,
                                    label: 'Last name',
                                    name: 'lastName',
                                    type: GraphQLTypes.String,
                                    dataType: '',
                                    enumType: '',
                                    isCustom: false,
                                    isStored: true,
                                    isOnInputType: true,
                                    isOnOutputType: true,
                                    targetNode: '',
                                    isMutable: false,
                                },
                                id: 'lastName',
                                key: 'lastName',
                                label: 'Last name',
                                labelKey: 'Last name',
                                labelPath: 'Last name',
                            },
                        },
                    ],
                    parameters: [
                        {
                            label: 'First name',
                            name: 'firstName',
                            type: GraphQLTypes.String,
                        },
                        {
                            label: 'Email',
                            name: 'email',
                            type: GraphQLTypes.String,
                        },
                        {
                            label: 'Last name',
                            name: 'lastName',
                            type: GraphQLTypes.String,
                        },
                    ],
                },
                expect.anything(),
                expect.anything(),
                expect.anything(),
            );
        });
    }, 90000);
});
