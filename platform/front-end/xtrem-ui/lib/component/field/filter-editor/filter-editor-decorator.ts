import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FilterEditorControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { FilterEditorDecoratorProperties, FilterEditorExtensionDecoratorProperties } from './filter-editor-types';

class FilterEditorDecorator extends AbstractFieldDecorator<FieldKey.FilterEditor> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = FilterEditorControlObject;
}

/**
 * Initializes the decorated member as a [FilterEditor]{@link FilterEditorControlObject} field with the provided properties.
 *
 * @param properties The properties that the [FilterEditor]{@link FilterEditorControlObject} field will be initialized with.
 */
export function filterEditorField<T extends ScreenExtension<T>>(
    properties: FilterEditorDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.FilterEditor>(
        properties as any,
        FilterEditorDecorator,
        FieldKey.FilterEditor,
        true,
    );
}

export function filterEditorFieldOverride<T extends ScreenExtension<T>>(
    properties: FilterEditorExtensionDecoratorProperties<T>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.FilterEditor>(properties);
}
