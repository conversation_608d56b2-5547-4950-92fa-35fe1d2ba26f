import * as React from 'react';
import { connect } from 'react-redux';
import type { FilterParameter, FilterTableProps } from '@sage/xtrem-ui-components';
import { FilterTableComponent, usePrevious } from '@sage/xtrem-ui-components';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { FilterEditorComponentProps } from './filter-editor-types';
import { localize, localizeEnumMember } from '../../../service/i18n-service';
import { isEqual } from 'lodash';
import { carbonLocale } from '../../../utils/carbon-locale';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent } from '../../../utils/events';
import {
    getFieldTitle,
    isFieldDisabled,
    isFieldHelperTextHidden,
    isFieldTitleHidden,
    getFieldHelperText,
} from '../carbon-helpers';
import Label from 'carbon-react/esm/__internal__/label';
import { HelperText } from '../carbon-utility-components';
import { getScreenElement } from '../../../service/screen-base-definition';
import * as xtremRedux from '../../../redux';

export const changeEventHandler = (screenId: string, elementId: string) => (): Promise<void> =>
    triggerFieldEvent(screenId, elementId, 'onChange');

export function FilterEditorComponent(props: FilterEditorComponentProps): React.ReactElement {
    const previousExternalFilters = usePrevious(props.value?.filters || []);
    const [filters, setFilters] = React.useState<NonNullable<FilterTableProps['value']>>(
        props.value?.filters.map((f, index) => ({ ...f, _id: String(index + 1) })) || [],
    );

    const onFiltersChange = React.useCallback<FilterTableProps['onChange']>(
        (newFilters): void => {
            if (!isEqual(newFilters || [], filters)) {
                setFilters(newFilters);
            }
        },
        [filters],
    );

    React.useEffect(() => {
        const filtersChanged = !isEqual(props.value?.filters || [], filters);
        const isExternalValueUpdate = !isEqual(previousExternalFilters, props.value?.filters || []);

        if (filtersChanged && !isExternalValueUpdate) {
            const parameters = filters
                .filter(d => d.parameter && d.data && d.id)
                .map<FilterParameter>(d => ({ name: d.id, label: d?.label, type: d!.data.type }));

            handleChange(
                props.elementId,
                { filters, parameters },
                props.setFieldValue,
                props.validate,
                changeEventHandler(props.screenId, props.elementId),
            );
        }
    }, [
        filters,
        props.elementId,
        props.screenId,
        props.setFieldValue,
        props.validate,
        props.value?.filters,
        previousExternalFilters,
    ]);

    React.useEffect(() => {
        const newFilters = props.value?.filters || [];
        const isExternalValueUpdate = !isEqual(previousExternalFilters, props.value?.filters || []);
        if (!isEqual(newFilters, filters) && isExternalValueUpdate) {
            setFilters(newFilters);
        }
    }, [filters, previousExternalFilters, props.value]);

    const isDisabled = isFieldDisabled(props.screenId, props.fieldProperties, props.value, null);

    const title = getFieldTitle(props.screenId, props.fieldProperties, null);
    const isTitleHidden = isFieldTitleHidden(props.screenId, props.fieldProperties, null);
    const helperText = getFieldHelperText(props.screenId, props.fieldProperties, null);
    const isHelperTextHidden = isFieldHelperTextHidden(props.screenId, props.fieldProperties, null);

    const handleFilterValueAction = React.useCallback(() => {
        if (props.fieldProperties.filterValueAction) {
            props.fieldProperties.filterValueAction.apply(
                getScreenElement(xtremRedux.getStore().getState().screenDefinitions[props.screenId]),
            );
        }
    }, [props.fieldProperties.filterValueAction, props.screenId]);

    const handlePropertyAction = React.useCallback(() => {
        if (props.fieldProperties.propertyAction) {
            props.fieldProperties.propertyAction.apply(
                getScreenElement(xtremRedux.getStore().getState().screenDefinitions[props.screenId]),
            );
        }
    }, [props.fieldProperties.propertyAction, props.screenId]);

    return (
        <CarbonWrapper
            {...props}
            className="e-filter-editor-field"
            componentName="filter-editor"
            helperText={props.fieldProperties.helperText}
            noReadOnlySupport={true}
            value={props.value}
        >
            {title && !isTitleHidden && (
                <div className="e-field-title">
                    <Label error={props.validationErrors?.[0]?.message} htmlFor={undefined as unknown as string}>
                        {title}
                    </Label>
                </div>
            )}
            <div data-testid="e-filter-editor-field-wrapper">
                <FilterTableComponent
                    propertyAction={props.fieldProperties.propertyAction ? handlePropertyAction : undefined}
                    propertyActionLabel={props.fieldProperties.propertyActionLabel}
                    filterValueAction={props.fieldProperties.filterValueAction ? handleFilterValueAction : undefined}
                    filterValueActionLabel={props.fieldProperties.filterValueActionLabel}
                    mode={props.fieldProperties.mode || 'table'}
                    carbonLocale={carbonLocale}
                    isDisabled={isDisabled}
                    locale={props.locale}
                    localize={localize}
                    localizeEnumMember={localizeEnumMember}
                    node={props.fieldProperties.node?.toString()}
                    nodeNames={{}}
                    onChange={onFiltersChange}
                    parameterMode={props.fieldProperties.parameterMode || 'creation'}
                    parameters={props.fieldProperties.filterParameters || []}
                    selectedProperties={props.fieldProperties.selectedProperties}
                    value={filters}
                    automaticColumnsSpacing={props.fieldProperties.automaticColumnsSpacing}
                    isParentColumnHidden={props.fieldProperties.isParentColumnHidden}
                />
            </div>
            {helperText && !isHelperTextHidden && <HelperText helperText={helperText} />}
        </CarbonWrapper>
    );
}

export const ConnectedFilterEditorComponent = connect(mapStateToProps(), mapDispatchToProps())(FilterEditorComponent);

export default ConnectedFilterEditorComponent;
