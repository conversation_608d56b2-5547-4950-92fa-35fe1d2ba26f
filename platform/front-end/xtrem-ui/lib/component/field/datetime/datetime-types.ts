import type { ClientNode } from '@sage/xtrem-client';
import type { Datetime } from '@sage/xtrem-date-time';
import type { <PERSON>Key } from '@sage/xtrem-shared';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import type { DatePropertyValue, ValueOrCallback } from '../../../utils/types';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedField, NestedFieldHandlersArguments, NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasParent,
    Nested,
    NestedChangeable,
    NestedValidatable,
    Sizable,
    Validatable,
} from '../traits';

export interface DatetimeProperties<CT extends ScreenExtension<CT> = ScreenBase, NodeType = any>
    extends EditableFieldProperties<CT, NodeType>,
        Sizable {
    isTimeZoneHidden?: boolean;
    timeZone?: string;
    minDate?: ValueOrCallback<CT, DatePropertyValue>;
    maxDate?: ValueOrCallback<CT, DatePropertyValue>;
    defaultDate?: Date | Datetime | string;
}

export interface DatetimeDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    ReferencedItemType extends ClientNode = any,
> extends Omit<DatetimeProperties<CT, ReferencedItemType>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>>,
        Sizable,
        Validatable<CT, string> {}

export interface NestedDatetimeProperties<CT extends ScreenBase = ScreenBase, ContextNodeType extends ClientNode = any>
    extends NestedPropertiesWrapper<DatetimeProperties<CT, ContextNodeType>>,
        NestedChangeable<CT>,
        Nested<ContextNodeType>,
        Sizable,
        NestedValidatable<CT, string, ContextNodeType> {}

export type DatetimeExtensionDecoratorProperties<
    CT extends ScreenExtension<CT>,
    ReferencedItemType extends ClientNode = any,
> = ChangeableOverrideDecoratorProperties<DatetimeDecoratorProperties<Extend<CT>, ReferencedItemType>, CT>;

export type DatetimeValue = Datetime | null;

export interface DatetimeComponentProps<T extends ClientNode = any>
    extends BaseEditableComponentProperties<DatetimeDecoratorProperties<any, T>, DatetimeValue> {
    columnDefinition?: NestedField<ScreenBase, FieldKey.Datetime>;
    isNested?: boolean;
    parentElementId?: string;
    handlersArguments?: NestedFieldHandlersArguments;
}
