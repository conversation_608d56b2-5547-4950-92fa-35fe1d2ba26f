import React, { useEffect } from 'react';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import { localize } from '../../../service/i18n-service';
import { ContextType } from '../../../types';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { generateFieldId, getLabelTitle, isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import { DatetimeInputComponent } from '../../ui/datetime/datetime-input-component';
import { datetimeToCalendarDate, makeDatetime } from '../../ui/datetime/datetime-utils';
import type { DatetimeValue, NestedDatetimeProperties } from './datetime-types';
import type { CalendarDate } from '@internationalized/date';
import { Time } from '@sage/xtrem-date-time';
import { datePropertyValueToCalendarDate } from '../../../utils/date-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';

export function DatetimeCellEditor({
    fieldProperties,
    tableElementId: parentElementId,
    screenId,
    node,
    api,
    column,
    eGridCell,
    elementId,
    locale,
    value,
    data,
    onValueChange,
}: CellParams<NestedDatetimeProperties, DatetimeValue | undefined>): React.ReactNode {
    const allColumns = React.useMemo(() => api.getColumns() || [], [api]);
    const width = React.useMemo(() => eGridCell?.style?.width || '250px', [eGridCell?.style?.width]);
    const containerRef = React.useRef<HTMLDivElement>(null);
    const dateTimeRef = React.useRef<HTMLInputElement>(null);
    const [currentValue, setCurrentValue] = React.useState(value || null);
    const [isPopoverOpen, setIsPopoverOpen] = React.useState<boolean>(false);
    const [time, setTime] = React.useState<string | null>(value?.time?.toString() || null);
    const [date, setDate] = React.useState<CalendarDate | null>(
        value ? datetimeToCalendarDate(value, fieldProperties?.timeZone || 'UTC') : null,
    );

    const setFieldValue = React.useCallback<(bind: string, value: unknown) => Promise<void>>(
        async (_, value) => {
            onValueChange(value);
        },
        [onValueChange],
    );

    const onChange = React.useCallback(
        (newValue: DatetimeValue | null) => {
            if (newValue?.toString() === value?.toString()) {
                return;
            }
            handleChange(elementId, newValue, setFieldValue);
            setCurrentValue(newValue);
        },
        [elementId, setFieldValue, value],
    );

    useEffect(() => {
        if (time && date) {
            onChange(makeDatetime(date, Time.parse(time)));
        } else if (!time && !date && currentValue) {
            onChange(null);
        }
    }, [time, date, onChange, currentValue]);

    const onDateChange = React.useCallback(
        (date: CalendarDate | null) => {
            setDate(date);
        },
        [setDate],
    );

    const onTimeChange = React.useCallback(
        (time: string | null) => {
            setTime(time);
        },
        [setTime],
    );

    const isDisabled = React.useMemo(
        () => isFieldDisabled(screenId, fieldProperties, currentValue, null),
        [currentValue, fieldProperties, screenId],
    );

    const isReadOnly = React.useMemo(
        () => isFieldReadOnly(screenId, fieldProperties, currentValue, null),
        [currentValue, fieldProperties, screenId],
    );

    const fieldId = React.useMemo(
        () =>
            generateFieldId({ screenId, elementId, contextType: ContextType.table, fieldProperties, isNested: false }),
        [elementId, fieldProperties, screenId],
    );

    const resolvedTitle = React.useMemo(
        () => getLabelTitle(screenId, fieldProperties, null),
        [fieldProperties, screenId],
    );

    const ariaLabelTitle = React.useMemo(
        () => resolvedTitle || localize('@sage/xtrem-ui/datetime-aria-label', 'Date and time'),
        [resolvedTitle],
    );

    const onPopperOpenChange = React.useCallback(
        (isOpen: boolean) => {
            setIsPopoverOpen(isOpen);
        },
        [setIsPopoverOpen],
    );

    const minDate = React.useMemo(
        () =>
            datePropertyValueToCalendarDate(
                resolveByValue({
                    propertyValue: fieldProperties.minDate,
                    screenId,
                    rowValue: null,
                    fieldValue: null,
                    skipHexFormat: true,
                }) || new Date(1970, 0, 1),
            ),
        [fieldProperties.minDate, screenId],
    );

    const maxDate = React.useMemo(
        () =>
            datePropertyValueToCalendarDate(
                resolveByValue({
                    propertyValue: fieldProperties.maxDate,
                    screenId,
                    rowValue: null,
                    fieldValue: null,
                    skipHexFormat: true,
                }) || new Date(2100, 11, 31),
            ),
        [fieldProperties.maxDate, screenId],
    );

    return (
        <div
            className="ag-cell-edit-wrapper e-nested-cell-field-datetime"
            data-testid={`${parentElementId}-${node.rowIndex}-${allColumns.indexOf(column) + 1}`}
            style={{ width }}
        >
            <div className="e-datetime-container" ref={containerRef} tabIndex={0}>
                <div className="e-date-input-wrapper">
                    <DatetimeInputComponent
                        inputRef={dateTimeRef}
                        nestedRowId={data?._id ? `-${data._id}` : ''}
                        aria-label={ariaLabelTitle}
                        date={date}
                        elementId={elementId}
                        fieldId={`${fieldId}-start-date`}
                        isDisabled={isDisabled}
                        isPopoverOpen={isPopoverOpen}
                        isReadOnly={isReadOnly}
                        isTimeZoneHidden={fieldProperties.isTimeZoneHidden}
                        locale={locale}
                        maxDate={maxDate}
                        minDate={minDate}
                        onDateChange={onDateChange}
                        onPopperOpenChange={onPopperOpenChange}
                        onTimeChange={onTimeChange}
                        rangeStartDate={null}
                        screenId={screenId}
                        time={time}
                        timeZone={fieldProperties?.timeZone}
                        size="small"
                        title={undefined}
                        type="single"
                    />
                </div>
            </div>
        </div>
    );
}
