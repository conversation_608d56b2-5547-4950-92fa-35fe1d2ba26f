import React, { useEffect, useMemo, useRef } from 'react';
import { connect } from 'react-redux';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-component';
import {
    generateFieldId,
    getFieldIndicatorStatus,
    isFieldDisabled,
    isFieldReadOnly,
    getLabelTitle,
} from '../carbon-helpers';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent, triggerNestedFieldEvent } from '../../../utils/events';
import { useFocus } from '../../../utils/hooks/effects/use-focus';
import { deepMerge } from '@sage/xtrem-shared';
import { set } from 'lodash';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { DatetimeInputComponent } from '../../ui/datetime/datetime-input-component';
import type { CalendarDate } from '@internationalized/date';
import type { DatetimeComponentProps } from './datetime-types';
import { Time, type Datetime } from '@sage/xtrem-date-time';
import { datetimeToCalendarDate, makeDatetime } from '../../ui/datetime/datetime-utils';
import { localize } from '../../../service/i18n-service';
import { datePropertyValueToCalendarDate } from '../../../utils/date-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';

export function DatetimeComponent({
    locale,
    elementId,
    screenId,
    setFieldValue,
    validate,
    isInFocus,
    fieldProperties,
    isParentDisabled,
    isParentHidden,
    contextType,
    validationErrors,
    value,
    handlersArguments,
    columnDefinition,
    isNested,
    parentElementId,
    ...rest
}: DatetimeComponentProps): JSX.Element {
    const containerRef = useRef<HTMLDivElement>(null);
    const dateTimeRef = useRef<HTMLInputElement>(null);
    const [currentValue, setCurrentValue] = React.useState(value || null);
    const [isPopoverOpen, setIsPopoverOpen] = React.useState<boolean>(false);
    const [time, setTime] = React.useState<string | null>(value?.time?.toString() || null);
    const [date, setDate] = React.useState<CalendarDate | null>(
        value ? datetimeToCalendarDate(value, fieldProperties?.timeZone || 'UTC') : null,
    );

    useFocus(dateTimeRef, isInFocus);

    useEffect(() => {
        setCurrentValue(value || null);
        setTime(value?.time?.toString() || null);
        setDate(value ? datetimeToCalendarDate(value, fieldProperties?.timeZone || 'UTC') : null);
    }, [value, fieldProperties?.timeZone]);

    const triggerChangeListener = React.useCallback(
        (newValue: Datetime | null) => (): void => {
            if (isNested && handlersArguments?.onChange && parentElementId) {
                const rowValue =
                    newValue !== undefined
                        ? deepMerge(
                              handlersArguments.rowValue,
                              set(
                                  {},
                                  convertDeepBindToPathNotNull(
                                      columnDefinition?.properties?.bind || fieldProperties?.bind || elementId,
                                  ),
                                  newValue,
                              ),
                          )
                        : handlersArguments?.rowValue;
                triggerNestedFieldEvent(
                    screenId,
                    parentElementId || elementId,
                    fieldProperties as any,
                    'onChange',
                    rowValue?._id,
                    rowValue,
                );
            } else {
                triggerFieldEvent(screenId, elementId, 'onChange');
            }
        },
        [
            isNested,
            handlersArguments?.onChange,
            handlersArguments?.rowValue,
            parentElementId,
            columnDefinition?.properties?.bind,
            fieldProperties,
            elementId,
            screenId,
        ],
    );

    const onUserChange = React.useCallback(
        (newDate: CalendarDate | null, newTime: string | null) => {
            if (newDate && newTime) {
                const newDatetime = makeDatetime(newDate, Time.parse(newTime));
                if (newDatetime?.toString() !== value?.toString()) {
                    handleChange(elementId, newDatetime, setFieldValue, validate, triggerChangeListener(newDatetime));
                    setCurrentValue(newDatetime);
                }
            } else if (!newDate && !newTime && currentValue) {
                handleChange(elementId, null, setFieldValue, validate, triggerChangeListener(null));
                setCurrentValue(null);
            }
        },
        [elementId, setFieldValue, triggerChangeListener, validate, value, currentValue],
    );

    const onDateChange = React.useCallback(
        (date: CalendarDate | null) => {
            setDate(date);
            onUserChange(date, time);
        },
        [onUserChange, time],
    );

    const onTimeChange = React.useCallback(
        (time: string | null) => {
            setTime(time);
            onUserChange(date, time);
        },
        [onUserChange, date],
    );

    const { isTitleHidden, helperText, isHelperTextHidden, isTimeZoneHidden } = fieldProperties;

    const { error, warning, info } = React.useMemo(
        () =>
            getFieldIndicatorStatus({
                validationErrors,
                screenId,
                value,
                fieldProperties,
                isParentDisabled,
                isParentHidden,
            }),
        [fieldProperties, isParentDisabled, isParentHidden, screenId, validationErrors, value],
    );

    const isDisabled = useMemo(
        () => isFieldDisabled(screenId, fieldProperties, currentValue, null),
        [currentValue, fieldProperties, screenId],
    );

    const isReadOnly = useMemo(
        () => isFieldReadOnly(screenId, fieldProperties, currentValue, null),
        [currentValue, fieldProperties, screenId],
    );

    const fieldId = useMemo(
        () => generateFieldId({ screenId, elementId, contextType, fieldProperties, isNested: false }),
        [contextType, elementId, fieldProperties, screenId],
    );

    const resolvedTitle = useMemo(() => getLabelTitle(screenId, fieldProperties, null), [fieldProperties, screenId]);

    const minDate = useMemo(
        () =>
            datePropertyValueToCalendarDate(
                resolveByValue({
                    propertyValue: fieldProperties.minDate,
                    screenId,
                    rowValue: null,
                    fieldValue: null,
                    skipHexFormat: true,
                }) || new Date(1970, 0, 1),
            ),
        [fieldProperties.minDate, screenId],
    );

    const maxDate = useMemo(
        () =>
            datePropertyValueToCalendarDate(
                resolveByValue({
                    propertyValue: fieldProperties.maxDate,
                    screenId,
                    rowValue: null,
                    fieldValue: null,
                    skipHexFormat: true,
                }) || new Date(2100, 11, 31),
            ),
        [fieldProperties.maxDate, screenId],
    );

    return (
        <CarbonWrapper
            contextType={contextType}
            elementId={elementId}
            handlersArguments={handlersArguments}
            value={value}
            screenId={screenId}
            isNested={isNested}
            parentElementId={parentElementId}
            columnDefinition={columnDefinition}
            isInFocus={isInFocus}
            fieldProperties={fieldProperties}
            isParentDisabled={isParentDisabled}
            isParentHidden={isParentHidden}
            locale={locale}
            {...rest}
            noReadOnlySupport={true}
            className="e-datetime-field"
            componentName="datetime"
        >
            {!isTitleHidden && resolvedTitle && (
                <FieldLabel label={resolvedTitle} errorMessage={error} warningMessage={warning} infoMessage={info} />
            )}
            <div className="e-datetime-container" ref={containerRef}>
                <div className="e-date-input-wrapper" ref={dateTimeRef}>
                    <DatetimeInputComponent
                        inputRef={dateTimeRef}
                        nestedRowId={handlersArguments?.rowValue?._id ? `-${handlersArguments.rowValue._id}` : ''}
                        aria-label={localize('@sage/xtrem-ui/date-time-component-aria-label', 'Select date and time')}
                        date={date}
                        elementId={elementId}
                        fieldId={fieldId}
                        isDisabled={isDisabled}
                        isPopoverOpen={isPopoverOpen}
                        isReadOnly={isReadOnly}
                        isTimeZoneHidden={isTimeZoneHidden}
                        locale={locale}
                        timeZone={fieldProperties?.timeZone}
                        maxDate={maxDate}
                        minDate={minDate}
                        onDateChange={onDateChange}
                        onPopperOpenChange={isOpen => setIsPopoverOpen(isOpen)}
                        onTimeChange={onTimeChange}
                        rangeStartDate={null}
                        screenId={screenId}
                        time={time}
                        type="single"
                    />
                </div>
            </div>
            {!isHelperTextHidden && helperText && <HelperText helperText={helperText} />}
        </CarbonWrapper>
    );
}

export const ConnectedDatetimeRangeComponent = connect(mapStateToProps(), mapDispatchToProps())(DatetimeComponent);

export default ConnectedDatetimeRangeComponent;
