/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import type { Datetime } from '@sage/xtrem-date-time';
import { isNil } from 'lodash';
import type { ScreenBase } from '../../../service/screen-base';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { DatetimeProperties, DatetimeValue } from './datetime-types';

type AllowableDate = Datetime | Date | string;
export class DatetimeControlObject<
    ReferencedItemType extends ClientNode = any,
    CT extends ScreenBase = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.Datetime, FieldComponentProps<FieldKey.Datetime>> {
    @ControlObjectProperty<DatetimeProperties<CT>, DatetimeControlObject<ReferencedItemType, CT>>()
    isTimeZoneHidden?: boolean;

    @ControlObjectProperty<DatetimeProperties<CT>, DatetimeControlObject<ReferencedItemType, CT>>()
    timeZone?: string;

    @ControlObjectProperty<DatetimeProperties<CT>, DatetimeControlObject<ReferencedItemType, CT>>()
    minDate?: AllowableDate;

    @ControlObjectProperty<DatetimeProperties<CT>, DatetimeControlObject<ReferencedItemType, CT>>()
    maxDate?: AllowableDate;

    @ControlObjectProperty<DatetimeProperties<CT>, DatetimeControlObject<ReferencedItemType, CT>>()
    defaultDate?: AllowableDate;

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    get value(): DatetimeValue | null {
        return this._getValue();
    }

    set value(value: DatetimeValue | null) {
        if (isNil(value)) {
            this._setValue(null);
            return;
        }
        this._setValue(value);
    }
}
