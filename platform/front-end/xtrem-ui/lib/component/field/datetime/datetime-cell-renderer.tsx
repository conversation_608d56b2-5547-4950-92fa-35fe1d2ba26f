import React from 'react';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import { formatDatetime } from '../../ui/datetime/datetime-utils';
import type { NestedDatetimeProperties, DatetimeValue } from './datetime-types';

export const DatetimeCellRenderer: React.FC<CellParams<NestedDatetimeProperties, DatetimeValue | undefined>> =
    React.memo(props => {
        const fieldProperties = React.useMemo(() => props.fieldProperties, [props.fieldProperties]);

        const value = React.useMemo(() => formatDatetime({ date: props.value }), [props.value]);

        const dataTestId = React.useMemo(
            () =>
                `${props.tableElementId}-${props.node.rowIndex}-${props.api.getColumns()!.indexOf(props.column!) + 1}`,
            [props.api, props.column, props.node.rowIndex, props.tableElementId],
        );

        return (
            <fieldProperties.wrapper {...props}>
                {value === '' ? null : (
                    <div data-testid={dataTestId} className="e-datetime-cell-renderer">
                        <span data-testid={`${dataTestId}`} className="e-datetime-cell-renderer-start">
                            {value}
                        </span>
                    </div>
                )}
            </fieldProperties.wrapper>
        );
    });

DatetimeCellRenderer.displayName = 'DatetimeCellRenderer';
