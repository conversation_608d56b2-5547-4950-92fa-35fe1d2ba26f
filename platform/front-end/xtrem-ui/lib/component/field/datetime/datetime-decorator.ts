/**
 * @packageDocumentation
 * @module root
 */
import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { DatetimeControlObject } from './datetime-control-object';
import { FieldKey } from '../../types';
import type { DatetimeDecoratorProperties, DatetimeExtensionDecoratorProperties } from './datetime-types';

class DatetimeDecorator extends AbstractFieldDecorator<FieldKey.Datetime> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = DatetimeControlObject;
}

/**
 * Initializes the decorated member as a [dateTime]{@link DateTimeControlObject} field with the provided properties.
 *
 * @param properties The properties that the [dateTime]{@link DateTimeControlObject} field will be initialized with.
 */
export function dateTimeField<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: DatetimeDecoratorProperties<Extend<T>, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Datetime>(
        properties,
        DatetimeDecorator,
        FieldKey.Datetime,
        true,
    );
}

export function dateTimeFieldOverride<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: DatetimeExtensionDecoratorProperties<T, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Datetime>(properties);
}
