import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { DatetimeComponentProps } from './datetime-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedDatetimeComponent = React.lazy(() => import('./datetime-component'));

export function AsyncConnectedDatetimeComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedDatetimeComponent {...props} />
        </React.Suspense>
    );
}

const DatetimeComponent = React.lazy(() =>
    import('./datetime-component').then(c => ({ default: c.DatetimeComponent })),
);

export function AsyncDatetimeComponent(props: DatetimeComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <DatetimeComponent {...props} />
        </React.Suspense>
    );
}
