/**
 * @packageDocumentation
 * @module root
 * */
import type { ClientNode } from '@sage/xtrem-client';
import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import {
    addColumnsToProperties,
    addDisabledToProperties,
    addHelperTextFieldToProperties,
    addNodeToProperties,
    addValueFieldToProperties,
} from '../../../utils/data-type-utils';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { MultiReferenceControlObject } from '../../control-objects';
import type { NestedFieldTypes } from '../../nested-fields';
import type { NestedExtensionField } from '../../nested-fields-extensions';
import type { NestedOverrideField } from '../../nested-fields-overrides';
import { FieldKey } from '../../types';
import type {} from '../traits';
import type { MultiReferenceDecoratorProperties } from './multi-reference-types';

export enum MultiReferenceDecoratorPropertiesKeys {
    isDropdownDisabled = 'isDropdownDisabled',
    onOpenLookupDialog = 'onOpenLookupDialog',
    onCloseLookupDialog = 'onCloseLookupDialog',
    minLookupCharacters = 'minLookupCharacters',
}

type BaseMultiReferenceExtensionDecoratorProperties<CT extends ScreenBase = ScreenBase> = {
    onCloseLookupDialogAfter?: (this: CT, _id: any) => void;
    onOpenLookupDialogAfter?: (this: CT, _id: any) => void;
};

export interface MultiReferenceExtensionDecoratorProperties<
    T extends ScreenExtension<T>,
    NodeType extends ClientNode = any,
> extends BaseMultiReferenceExtensionDecoratorProperties<Extend<T>>,
        ChangeableOverrideDecoratorProperties<
            Omit<MultiReferenceDecoratorProperties<Extend<T>, NodeType>, 'isMultiReferenceDialogOpen'>,
            Extend<T>
        > {
    /** The definitions of the nested fields used to represent the table rows */
    columns?: NestedExtensionField<T, NestedFieldTypes, NodeType>[];

    /** Allows overriding existing column properties in the base page's columns */
    columnOverrides?: NestedOverrideField<T, NestedFieldTypes, NodeType>[];
}

class MultiReferenceDecorator extends AbstractFieldDecorator<FieldKey.MultiReference> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = MultiReferenceControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<MultiReferenceDecoratorProperties> {
        const properties: Partial<MultiReferenceDecoratorProperties> = {};
        addNodeToProperties({ dataType, propertyDetails, properties });
        addColumnsToProperties(
            {
                dataType,
                propertyDetails,
                properties,
                dataTypes: this.dataTypes,
                nodeTypes: this.nodeTypes,
            },
            true,
        );
        addValueFieldToProperties({
            dataType,
            propertyDetails,
            properties,
        });
        addHelperTextFieldToProperties({
            dataType,
            propertyDetails,
            properties,
        });
        addDisabledToProperties({
            propertyDetails,
            dataType,
            properties,
        });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [MultiReference]{@link MultiReferenceControlObject} field with the provided properties
 *
 * @param properties The properties that the [MultiReference]{@link MultiReferenceControlObject} field will be initialized with
 */
export function multiReferenceField<T extends ScreenExtension<T>, NodeType extends ClientNode = any>(
    properties: Omit<MultiReferenceDecoratorProperties<Extend<T>, NodeType>, 'isMultiReferenceDialogOpen'>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.MultiReference>(
        properties as any,
        MultiReferenceDecorator,
        FieldKey.MultiReference,
        true,
    );
}

export function multiReferenceFieldOverride<T extends ScreenExtension<T>, NodeType extends ClientNode = any>(
    properties: MultiReferenceExtensionDecoratorProperties<T, NodeType>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.MultiReference, NodeType>(properties);
}
