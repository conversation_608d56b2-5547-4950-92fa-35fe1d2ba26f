import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenBaseGenericType, ScreenExtension } from '../../../types';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance, ReferenceRecursiveOrderBy } from '../../types';
import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { PropertyValueType } from '../reference/reference-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasColumns,
    HasDynamicLookupSuggestions,
    HasFilter,
    HasHelperTextField,
    HasIcon,
    HasInputValueChangeListener,
    HasLookupDialogTitle,
    HasNestedFilter,
    HasParent,
    HasSound,
    HasTunnel,
    HasValueField,
    MaxItems,
    MinItems,
    Nested,
    NestedChangeable,
    NestedClickable,
    NestedHasLookupDialogTitle,
    NestedMaxItems,
    NestedMinItems,
    Sizable,
    Validatable,
} from '../traits';
import type { MultiReferenceDecoratorPropertiesKeys } from './multi-reference-decorator';

export interface MultiReferenceProperties<
    CT extends ScreenExtension<CT> = ScreenBase,
    MultiReferencedItemType extends ClientNode = any,
    ContextNodeType = any,
> extends EditableFieldProperties<CT, ContextNodeType>,
        HasFilter<CT, MultiReferencedItemType>,
        HasIcon,
        HasSound<CT>,
        Partial<HasColumns<CT, MultiReferencedItemType>>,
        Sizable,
        HasValueField<MultiReferencedItemType>,
        HasHelperTextField<MultiReferencedItemType>,
        MinItems<CT>,
        MaxItems<CT>,
        HasTunnel<MultiReferencedItemType> {
    /** Whether the rows of the reference lookup can be filtered or not. Defaults to true */
    canFilter?: boolean;
    /** The GraphQL node property that will be used to display an image in front of the values. t*/
    imageField?: PropertyValueType<MultiReferencedItemType>;
    /** Minimum number of characters to trigger server lookup. If `0` supplied, the first page of suggestions will be fetched on focus. Defaults to 3 */
    minLookupCharacters?: number;
    /** The GraphQL node that the field suggestions will be fetched from */
    node: keyof ScreenBaseGenericType<CT>;
    /** The column or the set of columns which the table should be sorted by */
    orderBy?: ReferenceRecursiveOrderBy<MultiReferencedItemType>;
    /** Placeholder to be displayed in the field body */
    placeholder?: string;
    /** Function to be executed when the reference's dialog is opened */
    onOpenLookupDialog?: (this: CT, _id: string) => void;
    /** Function to be executed when the reference's dialog is closed */
    onCloseLookupDialog?: (this: CT, _id: string) => void;
    /** Auto-select item if search matches only one element */
    isMultiReferenceDialogOpen?: boolean;
    /** Should suggestions be based on all known columns? */
    shouldSuggestionsIncludeColumns?: boolean;
    /** Selected records */
    selectedRecords?: string[];
}

export interface MultiReferenceDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    ContextNodeType extends ClientNode = any,
> extends Omit<MultiReferenceProperties<CT, ContextNodeType>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        HasInputValueChangeListener<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>>,
        HasSound<CT>,
        HasDynamicLookupSuggestions<CT, ContextNodeType>,
        HasLookupDialogTitle<CT>,
        Sizable,
        MinItems<CT>,
        MaxItems<CT> {
    /** Minimum number of characters to trigger server lookup */
    [MultiReferenceDecoratorPropertiesKeys.minLookupCharacters]?: number;
    /** Whether the dropdown is disabled or not. If disabled the lookup dialog will be forcefully enabled. */
    [MultiReferenceDecoratorPropertiesKeys.isDropdownDisabled]?: boolean;
}

type UnionMultiReference = 'filter' | 'minItems' | 'maxItems';
export interface NestedMultiReferenceProperties<
    CT extends ScreenBase = ScreenBase,
    ContextNodeType extends ClientNode = any,
    MultiReferencedNode extends ClientNode = any,
> extends Omit<
            NestedPropertiesWrapper<MultiReferenceProperties<CT, MultiReferencedNode, ContextNodeType>>,
            UnionMultiReference
        >,
        NestedChangeable<CT>,
        NestedClickable<CT, ContextNodeType>,
        Nested<ContextNodeType>,
        Sizable,
        HasNestedFilter<CT, ContextNodeType, MultiReferencedNode>,
        Validatable<CT, MultiReferencedNode[]>,
        NestedHasLookupDialogTitle<CT, ContextNodeType>,
        NestedMinItems<CT>,
        NestedMaxItems<CT> {
    /** Minimum number of characters to trigger server lookup */
    [MultiReferenceDecoratorPropertiesKeys.minLookupCharacters]?: number;
    /** Whether the dropdown is disabled or not. If disabled the lookup dialog will be set to true. */
    [MultiReferenceDecoratorPropertiesKeys.isDropdownDisabled]?: boolean;
}

export interface MultiReferenceComponentProps<T extends ClientNode = any>
    extends BaseEditableComponentProperties<
        MultiReferenceDecoratorProperties<any, T>,
        T[],
        NestedFieldsAdditionalProperties
    > {}

export interface ConnectedMultiReferenceComponentProps<T extends ClientNode = any>
    extends MultiReferenceComponentProps<T> {
    hasAccessToTunnelPage: boolean;
    openTunnel: (fieldProperties: MultiReferenceDecoratorProperties, value: any) => Promise<any | null>;
}

export interface MultiReferenceComponentState {
    id: string;
    isNestedLookupDialogOpen?: boolean;
}
