import type { ClientNode } from '@sage/xtrem-client';
import type { UseComboboxStateChangeTypes } from 'downshift';
import { useCombobox } from 'downshift';
import { debounce } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import uid from 'uid';
import * as xtremRedux from '../../../redux';
import { lookupDialog } from '../../../service/dialog-service';
import { fetchReferenceFieldSuggestions } from '../../../service/graphql-service';
import type { LookupDialogContent } from '../../../types/dialogs';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { ReferenceDecoratorProperties } from '../../decorator-properties';
import { text } from '../../nested-fields';
import type { CollectionItem } from '../../types';
import type { SelectItem } from '../../ui/select/select-component';
import { Select } from '../../ui/select/select-component';
import { getCommonCarbonComponentProperties, getLabelTitle } from '../carbon-helpers';
import { CarbonWrapper } from '../carbon-wrapper';
import { EditableFieldBaseComponent, mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { EditableFieldComponentProperties, FieldComponentExternalProperties } from '../field-base-component-types';
import type { PropertyValueType } from '../reference/reference-types';
import { getReferenceSelectedRecord, hasLookupIcon, nodesToSelectItems } from '../reference/reference-utils';
import type {
    ConnectedMultiReferenceComponentProps,
    MultiReferenceComponentProps,
    MultiReferenceComponentState,
    MultiReferenceDecoratorProperties,
} from './multi-reference-types';
import { getElementAccessStatusWithoutId } from '../../../utils/access-utils';

export class MultiReferenceComponent<T extends ClientNode = any> extends EditableFieldBaseComponent<
    MultiReferenceDecoratorProperties<any, T>,
    T[],
    ConnectedMultiReferenceComponentProps<T>,
    MultiReferenceComponentState
> {
    private readonly lookupButtonRef: React.RefObject<HTMLButtonElement> = React.createRef();

    private readonly selectInputRef: React.RefObject<HTMLInputElement>;

    constructor(props: ConnectedMultiReferenceComponentProps<T>) {
        super(props);
        this.state = { id: uid(16) };
        this.selectInputRef = React.createRef();
    }

    private readonly onInputValueChanged = debounce(async (searchText: string, type?: UseComboboxStateChangeTypes) => {
        if (type !== useCombobox.stateChangeTypes.FunctionSelectItem) {
            await triggerFieldEvent(this.props.screenId, this.props.elementId, 'onInputValueChange', searchText);
        }
    }, 150);

    private readonly onMobileInputChange = async (
        searchText: string,
        type?: UseComboboxStateChangeTypes,
    ): Promise<void> => {
        this.mobileAutoSelect(type);
        await this.onInputValueChanged(searchText, type);
    };

    componentDidMount(): void {
        // On mobile if there are no columns by default we add "valueField" & "helperTextField"
        if (
            !this.props.fieldProperties.columns &&
            !this.isReadOnly() &&
            !(this.props.browser?.greaterThan.s ?? true) &&
            this.props.setFieldProperties
        ) {
            this.props.setFieldProperties(this.props.elementId, {
                ...this.props.fieldProperties,
                columns: [this.props.fieldProperties.valueField, this.props.fieldProperties.helperTextField]
                    .filter(c => !!c)
                    .map(c => {
                        // Hacky as query generation works with object as well as strings
                        const bind = typeof c === 'object' ? c : String(c);
                        // If we remove the "as" we get a type error from TS
                        // NOSONAR
                        return text({ bind: bind as PropertyValueType<T> });
                    }),
            });
        }
    }

    componentDidUpdate(prevProps: MultiReferenceComponentProps<T>): void {
        if (
            !!prevProps.fieldProperties.isMultiReferenceDialogOpen &&
            !this.props.fieldProperties.isMultiReferenceDialogOpen &&
            this.componentRef.current
        ) {
            const input = this.getFocusableElement(this.componentRef.current);
            if (input && document.activeElement !== input) {
                input.focus();
            }
        }
        if (this.props.value !== prevProps.value) {
            this.props.setFieldProperties?.(this.props.elementId, {
                ...this.props.fieldProperties,
                selectedRecords: (this.props.value ?? []).map(record => record._id),
            });
        }
    }

    onOpenTunnel = async (isNew = false): Promise<void> => {
        const newValue = await this.props.openTunnel(this.props.fieldProperties, isNew ? null : this.props.value);
        // Only update the value if the tunnel results some changes. If null returned, no changes were done.
        if (newValue) {
            handleChange(
                this.props.elementId,
                [...(this.props.value || []), newValue],
                this.props.setFieldValue,
                this.props.validate,
                this.triggerChangeListener,
            );
        }
    };

    handleOnCreateNewItemLinkClick = (): void => {
        this.onOpenTunnel(true);
    };

    private readonly onChange = (selectedRecords: CollectionItem[]): void => {
        handleChange(
            this.props.elementId,
            selectedRecords,
            this.props.setFieldValue,
            this.props.validate,
            this.triggerChangeListener,
        );
    };

    private readonly getLookupContent = (): LookupDialogContent => {
        return {
            isMultiSelect: true,
            contextNode: this.props.contextNode,
            fieldId: this.props.elementId,
            fieldProperties: this.props.fieldProperties as ReferenceDecoratorProperties,
            isLinkCreateNewText: !!this.props.fieldProperties.tunnelPage,
            recordContext: this.props.recordContext,
            selectedRecordId: this.props.value?.map(record => record._id) || [],
            valueField: this.props.fieldProperties.valueField,
            level: this.props.level,
            value: this.props.value,
            createTunnelLinkText: this.props.fieldProperties.createTunnelLinkText,
            onCreateNewItemLinkClick: this.handleOnCreateNewItemLinkClick,
            onLookupDialogClose: async (action): Promise<void> => {
                if (this.props.setFieldProperties) {
                    this.props.setFieldProperties(this.props.elementId, {
                        ...this.props.fieldProperties,
                        isMultiReferenceDialogOpen: false,
                        ...((action === 'cancel' || action === 'close') && {
                            selectedRecords: this.props.value?.map(record => record._id) || [],
                        }),
                    });
                }

                const stateUpdate = {} as MultiReferenceComponentState;

                if (this.props.parentElementId && this.state.isNestedLookupDialogOpen) {
                    stateUpdate.isNestedLookupDialogOpen = false;
                }

                this.setState(stateUpdate, () => {
                    setTimeout(() => {
                        this.lookupButtonRef.current?.focus();
                    }, 500);
                });
            },
        };
    };

    private readonly openLookupDialog = async (): Promise<void> => {
        try {
            const selectedRecords = await lookupDialog(this.props.screenId, 'info', this.getLookupContent());
            this.onChange(selectedRecords);
        } catch {
            /* intentionally left empty */
        }
    };

    private readonly hasLookupIcon = (): boolean =>
        hasLookupIcon({
            screenId: this.props.screenId,
            fieldProperties: this.props.fieldProperties,
            value: this.props.value,
            rowValue: this.props.handlersArguments?.rowValue
                ? splitValueToMergedValue(this.props.handlersArguments?.rowValue)
                : this.props.handlersArguments?.rowValue,
        });

    private readonly getBorderColor = (): string | undefined => {
        // INFO: In case the border of nested multi-reference components should not be rendered,
        //       this function can return 'transparent' as follows:
        //       return this.props.contextType === ContextType.pod ? 'transparent' : undefined;
        return undefined;
    };

    private readonly isReadOnlyAndHasPicture = (): boolean =>
        !!this.props.fieldProperties.imageField && (this.isReadOnly() || this.isDisabled());

    private readonly mobileAutoSelect = debounce(async (type?: UseComboboxStateChangeTypes): Promise<void> => {
        const isOrganicChange =
            this.selectInputRef.current?.value !== '' && type !== useCombobox.stateChangeTypes.FunctionSelectItem;
        if (isOrganicChange) {
            this.openLookupDialog();
        }
    }, 200);

    render(): React.ReactNode {
        let className = 'e-multi-reference-field';
        if (this.hasLookupIcon()) {
            className += ' e-multi-reference-field-lookup';
        }
        if (this.isReadOnlyAndHasPicture()) {
            className += ' e-multi-reference-inline-picture';
        }
        if (!this.isReadOnly() && !(this.props.browser?.greaterThan.s ?? true)) {
            return this.renderMobile(className);
        }
        return this.renderDesktop(className);
    }

    private readonly renderMobile = (className: string): React.ReactNode => {
        const carbonProps = getCommonCarbonComponentProperties(this.props);
        const { value, fieldProperties } = this.props;
        const helperText = this.props.fieldProperties.helperText;
        const wrapperStyle =
            helperText && !this.props.fieldProperties.isHelperTextHidden
                ? { alignItems: 'center' }
                : { alignItems: 'flex-end' };
        const selectedRecords = (value ?? [])
            .map(v =>
                getReferenceSelectedRecord({
                    value: v,
                    fieldProperties,
                }),
            )
            .filter(Boolean) as SelectItem[];
        // This will be used by the CarbonWrapper in case the field is nested
        const initialInputValue = this.props.isNested ? selectedRecords.map(s => s.value).join(', ') : '';
        return (
            <CarbonWrapper
                {...this.props}
                className={className}
                componentName="multi-reference"
                componentRef={this.componentRef}
                handlersArguments={this.props.handlersArguments}
                helperText={helperText}
                noReadOnlySupport={true}
                value={initialInputValue}
            >
                <div className="e-multi-reference-field" style={wrapperStyle}>
                    <Select
                        {...carbonProps}
                        borderColor={this.getBorderColor()}
                        disabled={this.isDisabled()}
                        fullWidth={this.props.fieldProperties.isFullWidth}
                        getItems={this.getItems}
                        hasLookupIcon={true}
                        helperText={helperText}
                        icon={this.props.fieldProperties.icon}
                        isSortedAlphabetically={true}
                        // this maps to the select's input text, which will always initially be empty
                        // the internal pills will come from 'selectedRecords'
                        initialInputValue=""
                        inputId={carbonProps.id}
                        onInputChange={this.onMobileInputChange}
                        isDropdownDisabled={true}
                        isMultiSelect={true}
                        label={getLabelTitle(
                            this.props.screenId,
                            this.props.fieldProperties,
                            this.props.handlersArguments?.rowValue,
                        )}
                        lookupButtonRef={this.lookupButtonRef}
                        lookupIconId={`e-multi-reference-field-lookup-icon-${this.state.id}`}
                        minLookupCharacters={this.getMinLookupCharacters()}
                        onInputFocus={this.props.onFocus}
                        onLookupIconClick={this.openLookupDialog}
                        onSelectedItemsChange={this.onChange}
                        placeholder={this.props.fieldProperties.placeholder}
                        preventSelectionOnBlur={
                            Boolean(this.props.fieldProperties.isMultiReferenceDialogOpen) ||
                            Boolean(this.state.isNestedLookupDialogOpen)
                        }
                        readOnly={this.isReadOnly()}
                        ref={this.selectInputRef}
                        selectedItems={selectedRecords}
                        testId={`e-multi-reference-field-lookup-search-text-${this.state.id}`}
                        screenId={this.props.screenId}
                        elementId={this.props.elementId}
                        isSoundDisabled={resolveByValue({
                            screenId: this.props.screenId,
                            propertyValue: this.props.fieldProperties.isSoundDisabled,
                            skipHexFormat: true,
                            fieldValue: null,
                            rowValue: null,
                        })}
                    />
                </div>
            </CarbonWrapper>
        );
    };

    private readonly getItems = async (filterValue: string): Promise<SelectItem[]> => {
        const nodes = await fetchReferenceFieldSuggestions({
            fieldProperties: this.props.fieldProperties,
            screenId: this.props.screenId,
            fieldId: this.props.elementId,
            filterValue,
            parentElementId: this.props.parentElementId,
            recordContext: this.props.handlersArguments?.rowValue,
            contextNode: this.props.contextNode,
            level: this.props.level,
        });

        return nodesToSelectItems<T>({
            nodes,
            fieldProperties: this.props.fieldProperties,
        });
    };

    private readonly getMinLookupCharacters = (): number => this.props.fieldProperties.minLookupCharacters ?? 3;

    private readonly renderDesktop = (className: string): React.ReactNode => {
        const selectedRecords = (this.props.value ?? [])
            .map(v =>
                getReferenceSelectedRecord({
                    value: v,
                    fieldProperties: this.props.fieldProperties,
                }),
            )
            .filter(Boolean) as SelectItem[];
        const helperText = this.props.fieldProperties.helperText;
        const carbonProps = getCommonCarbonComponentProperties(this.props);
        // This will be used by the CarbonWrapper in case the field is nested
        const initialInputValue = this.props.isNested ? selectedRecords.map(s => s.value).join(', ') : '';

        return (
            <CarbonWrapper
                {...this.props}
                className={className}
                componentName="multi-reference"
                componentRef={this.componentRef}
                helperText={this.props.fieldProperties.helperText}
                noReadOnlySupport={true}
                value={initialInputValue}
            >
                <div onClick={this.getClickHandler()} className="e-multi-reference-field-body">
                    <Select
                        disabled={this.isDisabled()}
                        error={this.props.validationErrors?.[0]?.message}
                        info={carbonProps.info}
                        warning={carbonProps.warning}
                        fullWidth={this.props.fieldProperties.isFullWidth}
                        getItems={this.getItems}
                        hasLookupIcon={this.hasLookupIcon()}
                        onInputChange={this.onInputValueChanged}
                        helperText={helperText}
                        icon={this.props.fieldProperties.icon}
                        // this maps to the select's input text, which will always initially be empty
                        // the internal pills will come from 'selectedRecords'
                        initialInputValue=""
                        inputId={carbonProps.id}
                        isSortedAlphabetically={true}
                        isDropdownDisabled={this.props.fieldProperties.isDropdownDisabled}
                        isMultiSelect={true}
                        label={
                            !this.props.fieldProperties.isTitleHidden
                                ? getLabelTitle(
                                      this.props.screenId,
                                      this.props.fieldProperties,
                                      this.props.handlersArguments?.rowValue,
                                  )
                                : undefined
                        }
                        lookupIconId={`e-multi-reference-field-lookup-icon-${this.state.id}`}
                        lookupButtonRef={this.lookupButtonRef}
                        minLookupCharacters={this.getMinLookupCharacters()}
                        onSelectedItemsChange={this.onChange}
                        onInputFocus={this.props.onFocus}
                        onLookupIconClick={this.openLookupDialog}
                        placeholder={this.props.fieldProperties.placeholder}
                        readOnly={this.isReadOnly()}
                        selectedItems={selectedRecords}
                        size={this.props.fieldProperties.size}
                        testId={`e-multi-reference-field-lookup-input-${this.state.id}`}
                        screenId={this.props.screenId}
                        elementId={this.props.elementId}
                        isSoundDisabled={resolveByValue({
                            screenId: this.props.screenId,
                            propertyValue: this.props.fieldProperties.isSoundDisabled,
                            skipHexFormat: true,
                            fieldValue: null,
                            rowValue: null,
                        })}
                        createTunnelLinkText={this.props.fieldProperties.createTunnelLinkText}
                        onCreateNewItemLinkClick={this.handleOnCreateNewItemLinkClick}
                        node={this.props.fieldProperties.node}
                        isLinkCreateNewText={!!this.props.fieldProperties.tunnelPage}
                    />
                </div>
            </CarbonWrapper>
        );
    };

    isRelatedTargetEqualTo = (e: React.FocusEvent<HTMLInputElement>, input: string): boolean =>
        (e.relatedTarget as any)?.id === `${input}-${this.state.id}`;
}

const extendedMapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: FieldComponentExternalProperties,
): ConnectedMultiReferenceComponentProps => {
    const componentProperties = mapStateToProps()(state, props) as EditableFieldComponentProperties<
        MultiReferenceDecoratorProperties,
        any
    >;
    const screenDefinition = state.screenDefinitions[props.screenId];
    const hasAccessToTunnelPage =
        !!componentProperties.fieldProperties.tunnelPage &&
        getElementAccessStatusWithoutId(screenDefinition.accessBindings, {
            node: String(componentProperties.fieldProperties.node),
            bind: '$read',
        }) === 'authorized';

    return {
        ...props,
        ...componentProperties,
        hasAccessToTunnelPage,
        openTunnel: xtremRedux.actions.actionStub,
    };
};

const extendedMapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: MultiReferenceComponentProps,
): Partial<ConnectedMultiReferenceComponentProps> => {
    const defaultMapDispatchToProps = mapDispatchToProps<MultiReferenceDecoratorProperties>()(dispatch, props);
    return {
        ...defaultMapDispatchToProps,
        openTunnel: (fieldProperties: MultiReferenceDecoratorProperties, value: any): Promise<any> =>
            dispatch(
                xtremRedux.actions.openTunnel({
                    elementId: props.elementId,
                    parentElementId: props.parentElementId,
                    screenId: props.screenId,
                    recordContext: props.recordContext,
                    contextNode: props.contextNode,
                    fieldProperties,
                    value,
                }),
            ),
        setFieldProperties: (elementId: string, value: any): void => {
            dispatch(xtremRedux.actions.setFieldProperties(props.screenId, elementId, value));
        },
    };
};

export const ConnectedMultiReferenceComponent = connect(
    extendedMapStateToProps,
    extendedMapDispatchToProps,
)(MultiReferenceComponent);

export default ConnectedMultiReferenceComponent;
