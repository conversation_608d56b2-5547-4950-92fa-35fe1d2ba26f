/**
 * @packageDocumentation
 * @module root
 * */
import type { ClientNode } from '@sage/xtrem-client';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import { noop } from 'lodash';
import { lookupDialog } from '../../../service/dialog-service';
import { fetchReferenceFieldSuggestions } from '../../../service/graphql-service';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import type { ScreenBase } from '../../../service/screen-base';
import { showToast } from '../../../service/toast-service';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { CollectionItem, FieldComponentProps, FieldKey, PartialCollectionValue } from '../../types';
import { getReferenceValueField } from '../reference/reference-utils';
import type { MultiReferenceDecoratorProperties } from './multi-reference-types';
import { getStore } from '../../../redux';
import { setFieldValue } from '../../../redux/actions';

/**
 * [Field]{@link EditableFieldControlObject} that holds a reference to an existing GraphQL object. The type of GraphQL object
 * must be specified through the 'node' property, while the 'valueField' and 'helperTextField'
 * properties define which properties of the GraphQL object will be displayed in the field
 * and will be matched against the user provided text
 */

export class MultiReferenceControlObject<
    MultiReferencedItemType extends ClientNode = any,
    CT extends ScreenBase = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.MultiReference, FieldComponentProps<FieldKey.MultiReference>> {
    static readonly defaultUiProperties: Partial<MultiReferenceDecoratorProperties> = {
        ...EditableFieldControlObject.defaultUiProperties,
        canFilter: true,
        minLookupCharacters: 3,
    };

    /** Graphql filter that will restrict the results of the reference field */
    get filter(): GraphQLFilter<MultiReferencedItemType> | undefined {
        return resolveByValue({
            fieldValue: undefined,
            propertyValue: this.getUiComponentProperty('filter'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /** Graphql filter that will restrict the results of the reference field */
    set filter(filter: GraphQLFilter<MultiReferencedItemType> | undefined) {
        this.setUiComponentProperties('filter', filter);
        this.refresh().catch(() => {
            /* Intentional fire and forget */
        });
    }

    /** The GraphQL node property that will be displayed below the reference field */
    get helperTextField(): string | undefined {
        return this.getUiComponentProperty('helperTextField') as string | undefined;
    }

    /** The GraphQL node property that will be used to display an image in front of the values. t*/
    get imageField(): string | undefined {
        return this.getUiComponentProperty('imageField') as string | undefined;
    }

    /** The GraphQL node that the field suggestions will be fetched from */
    get node(): string {
        return String(this.getUiComponentProperty('node'));
    }

    @ControlObjectProperty<
        MultiReferenceDecoratorProperties<CT, MultiReferencedItemType>,
        MultiReferenceControlObject<MultiReferencedItemType, CT>
    >()
    /** The helper text underneath the field */
    helperText?: string;

    @ControlObjectProperty<
        MultiReferenceDecoratorProperties<CT>,
        MultiReferenceControlObject<MultiReferencedItemType, CT>
    >()
    /** Icon of the input field. It will be placed on the right side. */
    icon?: IconType;

    @ControlObjectProperty<
        MultiReferenceDecoratorProperties<CT>,
        MultiReferenceControlObject<MultiReferencedItemType, CT>
    >()
    /** Color of the icon, only supported in tile containers */
    iconColor?: string;

    @ControlObjectProperty<
        MultiReferenceDecoratorProperties<CT>,
        MultiReferenceControlObject<MultiReferencedItemType, CT>
    >()
    /** Indicator, whether sounds play on successful/erroneous selection */
    isSoundDisabled?: boolean;

    @ControlObjectProperty<
        MultiReferenceDecoratorProperties<CT>,
        MultiReferenceControlObject<MultiReferencedItemType, CT>
    >()
    /** Placeholder to be displayed in the field body */
    placeholder?: string;

    @ControlObjectProperty<
        MultiReferenceDecoratorProperties<CT>,
        MultiReferenceControlObject<MultiReferencedItemType, CT>
    >()
    /**  The minimum number of items that has to be selected*/
    minItems?: number;

    @ControlObjectProperty<
        MultiReferenceDecoratorProperties<CT>,
        MultiReferenceControlObject<MultiReferencedItemType, CT>
    >()
    /**  The maximum number of items that has to be selected*/
    maxItems?: number;

    @ControlObjectProperty<
        MultiReferenceDecoratorProperties<CT>,
        MultiReferenceControlObject<MultiReferencedItemType, CT>
    >()
    /** Lookup Dialog title **/
    lookupDialogTitle?: string;

    /** Field's value */
    get value(): PartialCollectionValue<MultiReferencedItemType>[] {
        const values = this._getValue();
        return (values ?? []).map(v => splitValueToMergedValue(v));
    }

    /** Field's value */
    set value(newValues: PartialCollectionValue<MultiReferencedItemType & { $id: string }>[]) {
        const newItems = (newValues ?? []).map(v => {
            if (v && v.$id) {
                const remappedValue = { ...v, _id: v.$id };
                delete remappedValue.$id;
            }
            return v;
        });
        this._setValue(newItems);
    }

    /** The GraphQL node property that will be used as the reference field value */
    get valueField(): string | undefined {
        return getReferenceValueField(this.uiComponentProperties);
    }

    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }

    focus(): void {
        this._focus();
    }

    openDialog(): void {
        this.setUiComponentProperties('isMultiReferenceDialogOpen', true);
        lookupDialog(this.screenId, 'info', {
            contextNode: this.getUiComponentProperty('node'),
            createTunnelLinkText: undefined,
            fieldId: this.elementId,
            fieldProperties: this._getUiComponentProperties(this.screenId, this.elementId),
            isLinkCreateNewText: undefined,
            isMultiSelect: true,
            level: undefined,
            onCreateNewItemLinkClick: undefined,
            parentElementId: undefined,
            recordContext: undefined,
            searchText: undefined,
            selectedRecordId: (this.value || []).reduce((accu, next) => {
                return next?._id ? [...accu, (next as ClientNode)._id] : accu;
            }, [] as string[]),
            value: this.value,
            valueField: this.getUiComponentProperty('valueField'),
        })
            .catch(noop)
            .then(selection => {
                const { dispatch, getState } = getStore();
                handleChange<Partial<CollectionItem>[] | null>(
                    this.elementId,
                    selection ?? null,
                    async () => {
                        await setFieldValue(this.screenId, this.elementId, selection, true)(dispatch, getState);
                    },
                    this.validateWithDetails.bind(this),
                    () => {
                        triggerFieldEvent(this.screenId, this.elementId, 'onChange');
                    },
                );
            });
    }

    async fetchSuggestions(searchText: string): Promise<PartialCollectionValue<MultiReferencedItemType>[]> {
        const result = await fetchReferenceFieldSuggestions({
            fieldProperties: this.properties,
            screenId: this.screenId,
            fieldId: this.elementId,
            filterValue: searchText,
        });

        if (result) {
            // The remove edges function return an object with integer keys so we need to convert it back to an array.
            return Object.values(result).map((e: any) => splitValueToMergedValue(e));
        }

        return [];
    }
}
