import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { MultiReferenceComponentProps } from './multi-reference-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { useDispatch } from 'react-redux';
import * as xtremRedux from '../../../redux';
import type { ScreenBaseDefinition } from '../../../service/screen-base-definition';
import { getElementAccessStatusWithoutId } from '../../../utils/access-utils';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

const ConnectedMultiReferenceComponent = React.lazy(() => import('./multi-reference-component'));

export function AsyncConnectedMultiReferenceComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedMultiReferenceComponent {...props} />
        </React.Suspense>
    );
}

const MultiReferenceComponent = React.lazy(() =>
    import('./multi-reference-component').then(c => ({ default: c.MultiReferenceComponent })),
);

export function AsyncMultiReferenceComponent(props: MultiReferenceComponentProps): React.ReactElement {
    const dispatch = useDispatch();
    const openTunnel = React.useCallback(
        async () =>
            dispatch(
                xtremRedux.actions.openTunnel({
                    screenId: props.screenId,
                    elementId: props.elementId,
                    fieldProperties: props.fieldProperties,
                    parentElementId: props.parentElementId,
                    recordContext: props.recordContext,
                    contextNode: props.contextNode,
                    value: props.value,
                }),
            ),
        [dispatch, props],
    );

    const screenDefinition = useDeepEqualSelector<xtremRedux.XtremAppState, ScreenBaseDefinition>(
        s => s.screenDefinitions[props.screenId],
    );

    const hasAccessToTunnelPage =
        !!props.fieldProperties.tunnelPage &&
        getElementAccessStatusWithoutId(screenDefinition.accessBindings, {
            node: String(props.fieldProperties.node),
            bind: '$read',
        }) === 'authorized';
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <MultiReferenceComponent
                {...props}
                fieldProperties={{
                    ...props.fieldProperties,
                    helperTextField: props.fieldProperties.helperTextField,
                    imageField: props.fieldProperties.imageField,
                    valueField: props.fieldProperties.valueField,
                }}
                openTunnel={openTunnel}
                hasAccessToTunnelPage={hasAccessToTunnelPage}
            />
        </React.Suspense>
    );
}
