import type { Page } from '../../../..';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { MultiReferenceDecoratorProperties } from '../multi-reference-types';
import { multiReferenceField } from '../multi-reference-decorator';

type MultiReferenceType = {
    _id: string;
    name: string;
};
describe('MultiReference Decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'multiReferenceField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should set default values when no component properties provided', () => {
        multiReferenceField<any, MultiReferenceType>({
            node: '@sage/xtrem-ui-test/test-property',
            valueField: 'name',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: MultiReferenceDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as MultiReferenceDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onChange).toBeUndefined();
        expect(mappedComponentProperties.onClick).toBeUndefined();
    });

    it('should inherit false abstract-field booleans when no provided', () => {
        multiReferenceField<any, MultiReferenceType>({
            node: '@sage/xtrem-ui-test/test-property',
            valueField: 'name',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: MultiReferenceDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as MultiReferenceDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.isHiddenMobile).toBe(false);
        expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
        expect(mappedComponentProperties.isFullWidth).toBe(false);
        expect(mappedComponentProperties.isHidden).toBe(false);
        expect(mappedComponentProperties.isTransient).toBe(false);
    });

    it('should set values when component properties provided', () => {
        const clickFunc: () => void = jest.fn().mockImplementation(() => {});
        const changeFunc: () => void = jest.fn().mockImplementation(() => {});

        multiReferenceField<any, MultiReferenceType>({
            onChange: changeFunc,
            onClick: clickFunc,
            minLookupCharacters: 5,
            node: '@sage/xtrem-ui/test-property',
            valueField: 'name',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: MultiReferenceDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as MultiReferenceDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onChange).not.toBeUndefined();
        expect(mappedComponentProperties.onChange).toBe(changeFunc);
        expect(mappedComponentProperties.onClick).not.toBeUndefined();
        expect(mappedComponentProperties.onClick).toBe(clickFunc);
        expect(mappedComponentProperties.minLookupCharacters).toBe(5);
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(multiReferenceField, pageMetadata, fieldId);
        });
    });
});
