import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import type { GraphQLFilter } from '../../../../service/graphql-utils';
import { MultiReferenceControlObject } from '../../../control-objects';
import type { MultiReferenceDecoratorProperties } from '../../../decorators';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import * as graphqlService from '../../../../service/graphql-service';

describe('MultiReference Field', () => {
    const screenId = 'TestPage';
    const fieldID = 'test';
    const multiReferenceValue = 'TEST VALUE';
    const multiReferenceHelperText = 'TEST HELPER TEXT';
    const valueField = 'theFieldWithTheValue';
    const helperTextField = 'theFieldWithTheHelperText';
    const values = [{ [valueField]: multiReferenceValue, [helperTextField]: multiReferenceHelperText, _id: '123' }];
    const properties = {
        title: 'TEST_FIELD_TITLE',
        isHidden: true,
        isDisabled: true,
        valueField,
        helperTextField,
        node: '@sage/xtrem-ui-test/test-property',
    };
    let multiReferenceFieldControlObject: MultiReferenceControlObject;

    describe('properties', () => {
        beforeEach(() => {
            multiReferenceFieldControlObject = createFieldControlObject<FieldKey.MultiReference>(
                FieldKey.MultiReference,
                screenId,
                MultiReferenceControlObject,
                fieldID,
                values,
                properties,
            );
        });

        it('should retrieve the field helper text property name', () => {
            expect(multiReferenceFieldControlObject.helperTextField).toEqual(helperTextField);
        });

        it('should retrieve the field value property name', () => {
            expect(multiReferenceFieldControlObject.valueField).toEqual(valueField);
        });

        it('should retrieve the field value', () => {
            expect(multiReferenceFieldControlObject.value).toEqual([
                {
                    theFieldWithTheHelperText: 'TEST HELPER TEXT',
                    theFieldWithTheValue: 'TEST VALUE',
                    _id: '123',
                },
            ]);
        });

        it('should remove __ keys from multiReference field values', () => {
            const multiReferenceFieldWithNestedUnderscoreKeys = (multiReferenceFieldControlObject =
                createFieldControlObject<FieldKey.MultiReference>(
                    FieldKey.MultiReference,
                    screenId,
                    MultiReferenceControlObject,
                    'testWithNested',
                    [
                        {
                            item__name: {
                                name: 'TEST',
                                _id: '17',
                            },
                            _id: '16',
                        },
                    ],
                    properties,
                ));
            expect(multiReferenceFieldWithNestedUnderscoreKeys.value).toEqual([
                {
                    item: {
                        name: 'TEST',
                        _id: '17',
                    },
                    _id: '16',
                },
            ]);
        });

        it('should return an empty array for empty value', () => {
            const emptyMultiReferenceField = (multiReferenceFieldControlObject =
                createFieldControlObject<FieldKey.MultiReference>(
                    FieldKey.MultiReference,
                    screenId,
                    MultiReferenceControlObject,
                    'testEmpty',
                    null,
                    properties,
                ));
            expect(emptyMultiReferenceField.value).toEqual([]);
        });
    });

    describe('properties update', () => {
        it('should set the value', () => {
            const updatedValue = [
                {
                    theFieldWithTheHelperText: 'TEST HELPER TEXT',
                    theFieldWithTheValue: 'TEST VALUE',
                    _id: '123',
                },
            ];
            multiReferenceFieldControlObject.value = updatedValue;
            expect(multiReferenceFieldControlObject.value).toEqual(updatedValue);
        });

        it('should set the title', () => {
            const updatedTitle = 'Test Numeric Field Title';
            expect(multiReferenceFieldControlObject.title).not.toEqual(updatedTitle);
            multiReferenceFieldControlObject.title = updatedTitle;
            expect(multiReferenceFieldControlObject.title).toEqual(updatedTitle);
        });
    });

    describe('GraphQl filter property', () => {
        let setUiComponentProperties: jest.Mock<void, [string, string, MultiReferenceDecoratorProperties]>;
        let refresh: jest.Mock<Promise<FieldInternalValue<FieldKey.MultiReference>>>;
        let newProperties: MultiReferenceDecoratorProperties;
        const executeTest = async (filter: GraphQLFilter | (() => GraphQLFilter)) => {
            multiReferenceFieldControlObject.filter = filter;
            expect(setUiComponentProperties).toHaveBeenCalled();
            expect(refresh).toHaveBeenCalled();
            expect(newProperties.filter).toEqual(filter);
        };

        beforeEach(() => {
            setUiComponentProperties = jest.fn(
                (_screenId: string, _elementId: string, _value: MultiReferenceDecoratorProperties) => {
                    newProperties = { ..._value };
                },
            );

            refresh = jest.fn();
            multiReferenceFieldControlObject = createFieldControlObject<FieldKey.MultiReference>(
                FieldKey.MultiReference,
                screenId,
                MultiReferenceControlObject,
                fieldID,
                values,
                properties,
                { setUiComponentProperties, refresh },
            );
        });

        it('should update filter with GraphQL filter object', () => {
            executeTest({ description: { _regex: 'policy', _options: 'i' } });
        });

        it('should update filter with function', () => {
            executeTest(() => ({ description: { _regex: 'policy', _options: 'i' } }));
        });
    });

    describe('fetchSuggestions', () => {
        beforeEach(() => {
            jest.spyOn(graphqlService, 'fetchReferenceFieldSuggestions').mockResolvedValue([
                {
                    _id: '1',
                    property: 'asd',
                } as any,
                {
                    _id: '2',
                    property: 'asd2',
                    client__name: { _id: 1, name: 'John Doe' },
                    client__orderNumber: { _id: 1, orderNumber: '12123' },
                } as any,
            ]);

            multiReferenceFieldControlObject = createFieldControlObject<FieldKey.MultiReference>(
                FieldKey.MultiReference,
                screenId,
                MultiReferenceControlObject,
                fieldID,
                values,
                properties,
            );
        });

        it('should fetch suggestions from the server when this function is called and return them to the application code', async () => {
            const suggestions = await multiReferenceFieldControlObject.fetchSuggestions('my query');
            expect(suggestions).toBeInstanceOf(Array);
            expect(suggestions[0]._id).toEqual('1');
            expect(suggestions[1]._id).toEqual('2');
        });

        it('should remap split multiReference values to objects', async () => {
            const suggestions = await multiReferenceFieldControlObject.fetchSuggestions('my query');
            expect(suggestions[1].client).toEqual({
                _id: 1,
                name: 'John Doe',
                orderNumber: '12123',
            });
        });
    });
});
