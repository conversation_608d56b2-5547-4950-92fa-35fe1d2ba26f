import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    renderWithRedux,
    applyActionMocks,
} from '../../../../__tests__/test-helpers';

import { fireEvent, render, waitFor } from '@testing-library/react';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import * as graphqlService from '../../../../service/graphql-service';
import type { ScreenBase } from '../../../../service/screen-base';
import * as events from '../../../../utils/events';
import type { GridNestedFieldTypes, NestedField } from '../../../nested-fields';
import type { FieldInternalValue } from '../../../types';
import { Field<PERSON>ey, GraphQLTypes } from '../../../types';
import { NumericControlObject } from '../../numeric/numeric-control-object';
import { TextControlObject } from '../../text/text-control-object';
import { ConnectedMultiReferenceComponent, MultiReferenceComponent } from '../multi-reference-component';
import type { MultiReferenceDecoratorProperties } from '../multi-reference-types';
import * as i18n from '../../../../service/i18n-service';
import type { Class } from 'utility-types';
import * as AbstractFieldUtilsModule from '../../../../utils/abstract-fields-utils';
import type { DeepPartial } from 'ts-essentials';
import { GraphQLKind } from '../../../../types';
import '@testing-library/jest-dom';

const imageValue =
    'iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==';

type NodeType = {
    _id: string;
    code: string;
    column1?: string;
    column2?: string;
    name: string;
    image?: string;
    product?: {
        _id: string;
        name: string;
    };
};
describe('MultiReference component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: MultiReferenceDecoratorProperties<ScreenBase, NodeType>;
    const fieldId = 'test-multi-reference-field';
    let handleChangeSpy: jest.SpyInstance | null = null;

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test Field Title',
            valueField: 'code',
            node: '@sage/xtrem-ui-test/test-property',
        };

        jest.spyOn(graphqlService, 'fetchReferenceFieldSuggestions').mockResolvedValue([
            { _id: '1', code: 'Test code 1', name: 'Test name 1' },
            { _id: '2', code: 'Test code 2', name: 'Test name 2' },
            { _id: '3', code: 'Test code 3', name: 'Test name 3' },
            { _id: '4', code: 'Test code 4', name: 'Test name 4' },
        ] as any);
        handleChangeSpy = jest.spyOn(AbstractFieldUtilsModule, 'handleChange');
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    describe('connected', () => {
        let state: xtremRedux.XtremAppState;
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        const getMultiReferenceField = (multiReferenceFieldId = fieldId) => (
            <Provider store={mockStore}>
                <ConnectedMultiReferenceComponent screenId={screenId} elementId={multiReferenceFieldId} />
            </Provider>
        );

        const getMultiReferenceFieldForMobile = (multiReferenceFieldId = fieldId) => {
            state = {
                ...state,
                browser: {
                    ...state.browser,
                    greaterThan: { ...state.browser.greaterThan, s: false },
                },
            };
            mockStore = getMockStore(state);

            return getMultiReferenceField(multiReferenceFieldId);
        };

        const getColumns = (): NestedField<any, GridNestedFieldTypes, NodeType>[] => [
            {
                defaultUiProperties: {
                    ...TextControlObject.defaultUiProperties,
                    bind: 'column1',
                },
                properties: {
                    bind: 'column1',
                    title: 'Column 1',
                },
                type: FieldKey.Text,
            },
            {
                defaultUiProperties: {
                    ...NumericControlObject.defaultUiProperties,
                    bind: 'column2',
                },
                properties: {
                    bind: 'column2',
                    title: 'Column 2',
                },
                type: FieldKey.Numeric,
            },
        ];

        beforeEach(() => {
            state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState<FieldKey.MultiReference, any, NodeType>(
                FieldKey.MultiReference,
                state,
                screenId,
                fieldId,
                mockFieldProperties,
                [
                    {
                        code: 'XX123',
                        name: 'Capsule corp.',
                        image: { value: imageValue },
                    },
                ],
            );
            addFieldToState<FieldKey.MultiReference, any, NodeType>(
                FieldKey.MultiReference,
                state,
                screenId,
                'test-empty-multi-reference-field',
                mockFieldProperties,
                null,
            );
            mockStore = getMockStore(state);
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        describe('integration', () => {
            let fetchReferenceFieldSuggestionsMock;
            const defaultFetchMultiReferenceFieldSuggestionsMock = ({
                filterValue: searchText,
            }: {
                filterValue?: string;
            }): Promise<any> => {
                const edges = [
                    { _id: 1, code: 'Wine', name: 'Wine' },
                    { _id: 2, code: 'White wine', name: 'White wine' },
                    { _id: 3, code: 'Cheese', name: 'Cheese' },
                ].filter(item => !searchText || item.name.toLowerCase().indexOf(searchText.toLowerCase()) !== -1);
                return Promise.resolve(edges);
            };
            const setup = (
                multiReferenceProps: MultiReferenceDecoratorProperties<ScreenBase, NodeType> = mockFieldProperties,
                value: FieldInternalValue<FieldKey.MultiReference> = [],
                fetchReferenceFieldSuggestions = defaultFetchMultiReferenceFieldSuggestionsMock,
            ) => {
                fetchReferenceFieldSuggestionsMock = jest
                    .spyOn(graphqlService, 'fetchReferenceFieldSuggestions')
                    .mockImplementation(fetchReferenceFieldSuggestions);
                const initialState: DeepPartial<xtremRedux.XtremAppState> = {
                    nodeTypes: {
                        TestProperty: {
                            name: 'TestProperty',
                            title: 'TestProperty',
                            properties: {
                                code: {
                                    type: GraphQLTypes.String,
                                    kind: GraphQLKind.Scalar,
                                    isOnOutputType: true,
                                    canFilter: true,
                                },
                            },
                        },
                    },
                    screenDefinitions: {
                        [screenId]: {
                            metadata: {
                                uiComponentProperties: {
                                    [fieldId]: multiReferenceProps,
                                },
                            },
                            ...(value && {
                                values: {
                                    [fieldId]: value,
                                },
                            }),
                        },
                    },
                };
                const utils = renderWithRedux<FieldKey.MultiReference, any, NodeType>(
                    <ConnectedMultiReferenceComponent screenId={screenId} elementId={fieldId} />,
                    {
                        mockStore,
                        initialState,
                        fieldType: FieldKey.MultiReference,
                        fieldValue: value,
                        fieldProperties: multiReferenceProps,
                        elementId: fieldId,
                        screenId,
                    },
                );
                // const input = utils.getByTestId('e-multi-reference-field-lookup-input-uniqguidmock');
                const multiReference = utils.getByTestId(
                    'e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field',
                ) as HTMLDivElement;

                const input = multiReference.querySelector(
                    'input[data-testid="e-multi-reference-field-lookup-input-uniqguidmock"]',
                );
                const getIcon = (iconType: string) =>
                    multiReference.querySelector(`span[data-component="icon"][data-element="${iconType}"]`);

                const getDropdown = () => utils.getByTestId('e-ui-select-dropdown');
                const changeInput = (value: string) => {
                    fireEvent.change(input!, { target: { value } });
                };
                const blurInput = (target: object) => {
                    fireEvent.blur(input!, { target });
                };
                const getDropdownItems = () => {
                    return utils.getAllByTestId('e-ui-select-suggestion-value', {});
                };
                const getSelectedItems = () => {
                    return Array.from(utils.container.querySelectorAll('[data-component="pill"]') || []).map(
                        p => p.textContent,
                    );
                };
                return {
                    ...utils,
                    changeInput,
                    blurInput,
                    getIcon,
                    input,
                    getDropdown,
                    getDropdownItems,
                    multiReference,
                    getSelectedItems,
                };
            };

            afterEach(() => {
                jest.restoreAllMocks();
            });

            it('can render with redux with defaults', () => {
                const { input, multiReference } = setup();
                expect(multiReference).toHaveTextContent('Test Field Title');
                expect(input).toBeInTheDocument();
            });

            it('can search text', async () => {
                const { changeInput, getDropdownItems, input } = setup();
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');

                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                const dropdownItems = getDropdownItems();

                expect(dropdownItems).toHaveLength(2);
                expect(dropdownItems[0].textContent).toBe('White wine');
                expect(dropdownItems[1].textContent).toBe('Wine');
            });

            it('can select item', async () => {
                const { changeInput, input, getDropdownItems, getByText, getSelectedItems } = setup();
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                const dropdownItems = getDropdownItems();
                const selectedItem = dropdownItems[1].textContent as string;
                fireEvent.click(getByText(selectedItem));
                await waitFor(() => expect(input!.getAttribute('value')).toBe(''));
                expect(getSelectedItems()).toEqual(['XX123', selectedItem]);
            });

            it('can select and deselect item', async () => {
                const { changeInput, input, getDropdownItems, getByText, getSelectedItems } = setup();
                changeInput('wine');
                expect(input!.getAttribute('value')).toBe('wine');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                const dropdownItems = getDropdownItems();
                const selectedItem = dropdownItems[1].textContent as string;
                fireEvent.click(getByText(selectedItem));
                await waitFor(() => expect(input!.getAttribute('value')).toBe(''));
                expect(getSelectedItems()).toEqual(['XX123', selectedItem]);
                changeInput('');
                fireEvent.blur(input as any);
                await waitFor(() => expect(input!.getAttribute('value')).toBe(''));
            });

            it('can render "Type to search..." upon first focus', async () => {
                const { input, getByText } = setup(undefined, [{ code: '' }]);
                fireEvent.click(input as any);
                expect(graphqlService.fetchReferenceFieldSuggestions).not.toHaveBeenCalled();
                expect(getByText('Type to search...')).toBeInTheDocument();
            });

            it('can select with nested valueField', async () => {
                const items: any[] = [{ name: 'product1' }, { name: 'product2' }, { name: 'product3' }];
                const { getDropdownItems, input, getByText, changeInput, getSelectedItems } = setup(
                    {
                        ...mockFieldProperties,
                        valueField: { product: { name: true } } as any,
                    },
                    [{}],
                    ({ filterValue: searchText }: { filterValue?: string }) => {
                        const edges = items
                            .filter(
                                item => !searchText || item.name.toLowerCase().indexOf(searchText.toLowerCase()) !== -1,
                            )
                            .map(({ name }) => ({ product: { name } }));

                        return Promise.resolve(edges);
                    },
                );
                changeInput('product');
                expect(input!.getAttribute('value')).toBe('product');
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                const dropdownItems = getDropdownItems();
                expect(dropdownItems.length).toBe(3);
                const selectedItem = dropdownItems[0].textContent as string;
                fireEvent.click(getByText(selectedItem));
                await waitFor(() => expect(getSelectedItems()).toEqual([selectedItem]));
            });

            it('can sort by order-by property', async () => {
                const { changeInput, input } = setup({
                    ...mockFieldProperties,
                    minLookupCharacters: 0,
                    orderBy: { name: 1 },
                });
                changeInput('wi');
                expect(input!.getAttribute('value')).toBe('wi');
                const expected = {
                    fieldId: 'test-multi-reference-field',
                    fieldProperties: {
                        _controlObjectType: 'MultiReference',
                        minLookupCharacters: 0,
                        node: '@sage/xtrem-ui-test/test-property',
                        orderBy: {
                            name: 1,
                        },
                        title: 'Test Field Title',
                        valueField: 'code',
                    },
                    filterValue: 'wi',
                    screenId: 'TestPage',
                };
                await waitFor(() =>
                    expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledWith(expected),
                );
            });

            it('can sort by first column', async () => {
                const { changeInput, input } = setup({
                    ...mockFieldProperties,
                    minLookupCharacters: 0,
                    columns: getColumns(),
                });
                // Do not mock 'fetchReferenceFieldSuggestions'
                fetchReferenceFieldSuggestionsMock.mockRestore();
                // Mock 'fetchReferenceFieldData' instead
                jest.spyOn(graphqlService, 'fetchReferenceFieldData').mockResolvedValue({ query: { edges: [] } });
                changeInput('wi');
                expect(input!.getAttribute('value')).toBe('wi');
                const expected = {
                    fieldProperties: {
                        _controlObjectType: 'MultiReference',
                        columns: [
                            {
                                defaultUiProperties: {
                                    bind: 'column1',
                                    isFullWidth: false,
                                    isHelperTextHidden: false,
                                    isHidden: false,
                                    isHiddenDesktop: false,
                                    isHiddenMobile: false,
                                    isMandatory: false,
                                    isReadOnly: false,
                                    isTitleHidden: false,
                                    isTransient: false,
                                },
                                properties: { bind: 'column1', title: 'Column 1' },
                                type: FieldKey.Text,
                            },
                            {
                                defaultUiProperties: {
                                    bind: 'column2',
                                    isFullWidth: false,
                                    isHelperTextHidden: false,
                                    isHidden: false,
                                    isHiddenDesktop: false,
                                    isHiddenMobile: false,
                                    isMandatory: false,
                                    isReadOnly: false,
                                    isTitleHidden: false,
                                    isTransient: false,
                                },
                                properties: { bind: 'column2', title: 'Column 2' },
                                type: FieldKey.Numeric,
                            },
                        ],
                        minLookupCharacters: 0,
                        node: '@sage/xtrem-ui-test/test-property',
                        title: 'Test Field Title',
                        valueField: 'code',
                    },
                    isFilterLimitedToDataset: false,
                    screenId: 'TestPage',
                    valueField: 'code',
                    elementId: 'test-multi-reference-field',
                    filter: { _or: [{ code: { _regex: 'wi', _options: 'i' } }] },
                    orderBy: { column1: 1, _id: 1 },
                };
                await waitFor(() => expect(graphqlService.fetchReferenceFieldData).toHaveBeenCalledWith(expected));
            });

            it('can render "No results are available." when minLookupCharacters is zero upon first focus', async () => {
                const { getDropdownItems, input, getByText } = setup(
                    {
                        ...mockFieldProperties,
                        minLookupCharacters: 0,
                    },
                    [{ code: '' }],
                );
                fireEvent.click(input as any);
                await waitFor(() => expect(graphqlService.fetchReferenceFieldSuggestions).toHaveBeenCalledTimes(1));
                const dropdownItems = getDropdownItems();
                expect(getByText('No results are available.')).toBeInTheDocument();
                expect(dropdownItems).toHaveLength(3);
            });

            it('can render with icon', () => {
                const { getIcon } = setup({ ...mockFieldProperties, icon: 'scan' });
                expect(getIcon('scan')).toBeInTheDocument();
            });

            it('should clear the input field the user does not select a valid value', async () => {
                state.screenDefinitions[screenId].values[fieldId] = {};
                const { changeInput, input, blurInput } = setup();
                changeInput('Random');
                expect(input!.getAttribute('value')).toBe('Random');
                blurInput({ id: 'stuff' });
                await waitFor(() => {
                    expect(input!.getAttribute('value')).toBe('');
                });
            });

            it('should reset to empty value on Escape keydown', () => {
                const { input, changeInput } = setup(
                    {
                        ...mockFieldProperties,
                        minLookupCharacters: 0,
                    },
                    [
                        {
                            _id: 'XX123',
                            code: '1234567',
                            name: 'Capsule corp.',
                            column1: 'column1',
                            column2: 'column2',
                        },
                    ],
                );
                expect(input!.getAttribute('value')).toBe('');

                changeInput('12345678');
                expect(input!.getAttribute('value')).toBe('12345678');
                fireEvent.keyDown(input!, { key: 'Escape' });
                expect(input!.getAttribute('value')).toBe('12345678');
                fireEvent.keyDown(input!, { key: 'Escape' });
                expect(input!.getAttribute('value')).toBe('');
            });
        });

        describe('snapshots', () => {
            it('should render with default properties', () => {
                const container = render(getMultiReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(getMultiReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(getMultiReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render with full-width', () => {
                mockFieldProperties.isFullWidth = true;
                const { container } = render(getMultiReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render in read-only mode', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = render(getMultiReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render with onOpenLookupDialog callback', () => {
                mockFieldProperties.onOpenLookupDialog = () => {};
                const { container } = render(getMultiReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render with onCloseLookupDialog callback', () => {
                mockFieldProperties.onCloseLookupDialog = () => {};
                const { container } = render(getMultiReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render without a placeholder when any item is selected', () => {
                const { container } = render(getMultiReferenceField());
                expect(container.querySelector('input')).toHaveAttribute('placeholder', '');
            });

            it('should render helperText', () => {
                mockFieldProperties.helperTextField = 'name';
                const { container } = render(getMultiReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render with columns', () => {
                mockFieldProperties.columns = getColumns();
                const { container } = render(getMultiReferenceField());
                expect(container).toMatchSnapshot();
            });

            it('should render empty when no value', () => {
                const { container } = render(getMultiReferenceField('test-empty-multi-reference-field'));
                expect(container).toMatchSnapshot();
            });

            it('should render with a placeholder when no item is selected', () => {
                const { container } = render(getMultiReferenceField('test-empty-multi-reference-field'));
                expect(container.querySelector('input')).toHaveAttribute('placeholder', 'Please Select...');
            });

            it('should render in mobile mode', () => {
                const multiReferenceComponent = getMultiReferenceFieldForMobile();
                const { container } = render(multiReferenceComponent);
                expect(container).toMatchSnapshot();
            });

            it('should render with various field sizes', () => {
                mockFieldProperties.size = 'small';
                let wrapper = render(getMultiReferenceField());
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'medium';
                wrapper = render(getMultiReferenceField());
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'large';
                wrapper = render(getMultiReferenceField());
                expect(wrapper.container).toMatchSnapshot();
            });

            it('should render read-only using a conditional declaration', () => {
                mockFieldProperties.isReadOnly = () => {
                    return true;
                };

                let wrapper = render(getMultiReferenceField());

                expect(wrapper.container.querySelector('input[readonly]')).not.toBeNull();

                mockFieldProperties.isReadOnly = () => {
                    return false;
                };

                wrapper = render(getMultiReferenceField());

                expect(wrapper.container.querySelector('input[readonly]')).toBeNull();
            });
        });

        describe('interactions', () => {
            describe('lookup dialog render', () => {
                beforeEach(() => {
                    mockFieldProperties.columns = getColumns();
                    mockFieldProperties.isMultiReferenceDialogOpen = true;
                    jest.spyOn(xtremRedux.actions, 'openDialog');
                });

                afterEach(() => {
                    jest.clearAllMocks();
                });

                it('should open lookup dialog on desktop', async () => {
                    const { queryByTestId } = render(getMultiReferenceField());
                    const lookupButton = queryByTestId('e-ui-select-lookup-button');
                    expect(lookupButton).not.toBeNull();
                    expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(0);
                    fireEvent.click(lookupButton as HTMLElement);
                    await waitFor(() => {
                        expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(1);
                        expect(xtremRedux.actions.openDialog).toHaveBeenLastCalledWith(
                            expect.any(Number),
                            expect.objectContaining({ title: 'Test Field Title' }),
                        );
                    });
                });

                it('should open lookup dialog on mobile', async () => {
                    const { queryByRole } = render(getMultiReferenceFieldForMobile());
                    const selectInput = queryByRole('textbox');
                    expect(selectInput).not.toBeNull();
                    expect(xtremRedux.actions.openDialog).toHaveBeenCalledTimes(0);
                    fireEvent.change(selectInput as HTMLInputElement, { target: { value: 'search' } });
                    await waitFor(() => {
                        expect(xtremRedux.actions.openDialog).toHaveBeenLastCalledWith(
                            expect.any(Number),
                            expect.objectContaining({ title: 'Test Field Title' }),
                        );
                    });
                });

                it('should trigger onInputValueChange when the value of the field has changed with some delay', async () => {
                    const { container } = render(
                        <MultiReferenceComponent<NodeType>
                            elementId={fieldId}
                            fieldProperties={mockFieldProperties}
                            value={[
                                {
                                    _id: 'XX123',
                                    code: '1234567',
                                    name: 'Capsule corp.',
                                    column1: 'column1',
                                    column2: 'column2',
                                },
                            ]}
                            setFieldValue={jest.fn().mockResolvedValue(undefined)}
                            validate={jest.fn().mockResolvedValue(undefined)}
                            removeNonNestedErrors={jest.fn()}
                            screenId={screenId}
                            onFocus={jest.fn()}
                            locale="en-US"
                            openTunnel={jest.fn()}
                            hasAccessToTunnelPage={false}
                        />,
                    );

                    const input = container.querySelector('input')!;

                    expect(input).not.toBeNull();

                    fireEvent.change(input, { target: { value: '1234567890' } });

                    expect(events.triggerFieldEvent).not.toHaveBeenCalled();
                    await waitFor(() => {
                        expect(events.triggerFieldEvent).toHaveBeenCalledWith(
                            screenId,
                            fieldId,
                            'onInputValueChange',
                            '1234567890',
                        );
                    });
                });
            });
        });
    });

    describe('unconnected', () => {
        let setFieldValueMock: jest.Mock;
        let validateMock: jest.Mock;
        let removeErrorsFromFieldMock: jest.Mock;
        const defaultValue = () => [
            {
                _id: 'XX123',
                code: 'XX123',
                name: 'Capsule corp.',
                column1: 'column1',
                column2: 'column2',
            },
        ];
        const mountMultiReferenceComponent = (
            customFieldProperties: Partial<
                React.ComponentProps<Class<MultiReferenceComponent<NodeType>>>['fieldProperties']
            > = {},
            value = defaultValue(),
        ) => {
            return render(
                <MultiReferenceComponent<NodeType>
                    elementId={fieldId}
                    locale="en-US"
                    fieldProperties={{ ...mockFieldProperties, ...customFieldProperties }}
                    value={value}
                    setFieldValue={setFieldValueMock}
                    validate={validateMock}
                    removeNonNestedErrors={removeErrorsFromFieldMock}
                    screenId={screenId}
                    onFocus={jest.fn()}
                    openTunnel={jest.fn()}
                    hasAccessToTunnelPage={false}
                />,
            );
        };

        beforeEach(() => {
            jest.useFakeTimers();
            setFieldValueMock = jest.fn().mockResolvedValue(undefined);
            validateMock = jest.fn().mockResolvedValue(undefined);
            removeErrorsFromFieldMock = jest.fn();
            jest.spyOn(i18n, 'localize').mockImplementation((_, value) => value);
            jest.spyOn(events, 'triggerFieldEvent').mockImplementation(jest.fn());
        });

        afterEach(() => {
            jest.useRealTimers();
        });

        describe('snapshots', () => {
            const renderComponent = () =>
                render(
                    <MultiReferenceComponent<NodeType>
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        locale="en-US"
                        value={[
                            {
                                _id: 'XX123',
                                code: 'XX123',
                                name: 'Capsule corp.',
                                column1: 'column1',
                                column2: 'column2',
                            },
                        ]}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={removeErrorsFromFieldMock}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        openTunnel={jest.fn()}
                        hasAccessToTunnelPage={false}
                    />,
                ).container;

            it('should render just fine', () => {
                const container = renderComponent();
                expect(container).toMatchSnapshot();
            });

            it('should render with an info message', async () => {
                mockFieldProperties.infoMessage = 'Info message!!';
                const { baseElement } = render(
                    <MultiReferenceComponent<NodeType>
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        locale="en-US"
                        value={[
                            {
                                _id: 'XX123',
                                code: 'XX123',
                                name: 'Capsule corp.',
                                column1: 'column1',
                                column2: 'column2',
                            },
                        ]}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={removeErrorsFromFieldMock}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        openTunnel={jest.fn()}
                        hasAccessToTunnelPage={false}
                    />,
                );
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an info message from a callback', async () => {
                mockFieldProperties.infoMessage = () => 'Info message!!';
                const { baseElement } = render(
                    <MultiReferenceComponent<NodeType>
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        locale="en-US"
                        value={[
                            {
                                _id: 'XX123',
                                code: 'XX123',
                                name: 'Capsule corp.',
                                column1: 'column1',
                                column2: 'column2',
                            },
                        ]}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={removeErrorsFromFieldMock}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        openTunnel={jest.fn()}
                        hasAccessToTunnelPage={false}
                    />,
                );
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an warning message', async () => {
                mockFieldProperties.warningMessage = 'Warning message!!';
                const { baseElement } = render(
                    <MultiReferenceComponent<NodeType>
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        locale="en-US"
                        value={[
                            {
                                _id: 'XX123',
                                code: 'XX123',
                                name: 'Capsule corp.',
                                column1: 'column1',
                                column2: 'column2',
                            },
                        ]}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={removeErrorsFromFieldMock}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        openTunnel={jest.fn()}
                        hasAccessToTunnelPage={false}
                    />,
                );
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with an warning message from a callback', async () => {
                mockFieldProperties.warningMessage = () => 'Warning message!!';
                const { baseElement } = render(
                    <MultiReferenceComponent<NodeType>
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        locale="en-US"
                        value={[
                            {
                                _id: 'XX123',
                                code: 'XX123',
                                name: 'Capsule corp.',
                                column1: 'column1',
                                column2: 'column2',
                            },
                        ]}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={removeErrorsFromFieldMock}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        openTunnel={jest.fn()}
                        hasAccessToTunnelPage={false}
                    />,
                );
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with warning taking priority over info', async () => {
                mockFieldProperties.warningMessage = () => 'Warning message!!';
                mockFieldProperties.infoMessage = 'Info message!!';
                const { baseElement } = render(
                    <MultiReferenceComponent<NodeType>
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        locale="en-US"
                        value={[
                            {
                                _id: 'XX123',
                                code: 'XX123',
                                name: 'Capsule corp.',
                                column1: 'column1',
                                column2: 'column2',
                            },
                        ]}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={removeErrorsFromFieldMock}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        openTunnel={jest.fn()}
                        hasAccessToTunnelPage={false}
                    />,
                );
                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });
        });

        describe('interactions', () => {
            const triggerOnBlur = (wrapper: HTMLElement, relatedTarget?: { id?: string }) => {
                const multiReference = wrapper.querySelector('input')!;
                expect(multiReference).not.toBeNull();
                fireEvent.blur(multiReference, { relatedTarget: relatedTarget || new EventTarget() });
            };

            it('should not call handleChange on blur', () => {
                const { container } = mountMultiReferenceComponent();

                const multiReference = container.querySelector('input')!;
                expect(multiReference).not.toBeNull();
                triggerOnBlur(container);

                expect(handleChangeSpy).not.toHaveBeenCalled();
            });

            it('should clear value when an empty value is set', () => {
                const { container, rerender } = mountMultiReferenceComponent();

                expect(container.querySelector('input')).toHaveValue('');
                expect(Array.from(container.querySelectorAll('.e-ui-select-label')).map(l => l.textContent)).toEqual([
                    'XX123',
                ]);
                rerender(
                    <MultiReferenceComponent<NodeType>
                        elementId={fieldId}
                        locale="en-US"
                        fieldProperties={{ ...mockFieldProperties }}
                        value={null as any}
                        setFieldValue={setFieldValueMock}
                        validate={validateMock}
                        removeNonNestedErrors={removeErrorsFromFieldMock}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        openTunnel={jest.fn()}
                        hasAccessToTunnelPage={false}
                    />,
                );
                expect(container.querySelector('input')).toHaveValue('');
                expect(Array.from(container.querySelectorAll('.e-ui-select-label')).map(l => l.textContent)).toEqual(
                    [],
                );
            });

            it('value should be the same', () => {
                const { container, rerender } = mountMultiReferenceComponent();

                expect(container.querySelector('input')).toHaveValue('');
                expect(Array.from(container.querySelectorAll('.e-ui-select-label')).map(l => l.textContent)).toEqual([
                    'XX123',
                ]);
                rerender(
                    <MultiReferenceComponent<NodeType>
                        elementId={fieldId}
                        locale="en-US"
                        fieldProperties={{ ...mockFieldProperties }}
                        value={[{ code: 'XX124' }] as any}
                        setFieldValue={setFieldValueMock}
                        validate={validateMock}
                        removeNonNestedErrors={removeErrorsFromFieldMock}
                        screenId={screenId}
                        onFocus={jest.fn()}
                        openTunnel={jest.fn()}
                        hasAccessToTunnelPage={false}
                    />,
                );
                expect(container.querySelector('input')).toHaveValue('');
                expect(Array.from(container.querySelectorAll('.e-ui-select-label')).map(l => l.textContent)).toEqual([
                    'XX124',
                ]);
            });

            it('should not call handleChange on blur when component is read only', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = mountMultiReferenceComponent();

                triggerOnBlur(container);
                expect(handleChangeSpy).not.toHaveBeenCalled();
            });

            it('should not call handleChange on blur when component is disable', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = mountMultiReferenceComponent();

                triggerOnBlur(container);
                expect(handleChangeSpy).not.toHaveBeenCalled();
            });

            it('should NOT call handleChange on blur when previously selected item is restored', () => {
                const { container } = mountMultiReferenceComponent();

                fireEvent.change(container.querySelector('input')!, { target: { value: 'anything' } });
                triggerOnBlur(container);
                expect(handleChangeSpy).not.toHaveBeenCalled();
            });
        });
    });
});
