// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`MultiReference component connected snapshots should render disabled 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
  color: var(--colorsUtilityYin030);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c9 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
  color: var(--colorsUtilityYin030);
  cursor: not-allowed;
}

.c9:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c9::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background: var(--colorsUtilityDisabled400);
  border-color: var(--colorsUtilityDisabled600);
  cursor: not-allowed;
}

.c6 .c8 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-multi-reference-field e-disabled"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                disabled=""
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                disabled=""
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c8 c9 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  disabled=""
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-multi-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component connected snapshots should render empty when no value 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c9 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c9::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-multi-reference-field"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-empty-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-empty-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-empty-multi-reference-field"
                id="TestPage-test-empty-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                />
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c7 c8 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-empty-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder="Please Select..."
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c9"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-empty-multi-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component connected snapshots should render helperText 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c14 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c14::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c12 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
  padding: 0 28px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  width: 24px;
  padding: 0;
  line-height: 15px;
}

<div>
  <div
    class="e-field e-multi-reference-field"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                      <button
                        aria-label="remove pill"
                        class="c8 c9"
                        data-component="icon-button"
                        data-element="close"
                        type="button"
                      >
                        <span
                          class="c10 c11"
                          data-component="icon"
                          data-element="cross"
                          data-role="icon"
                          font-size="small"
                          type="cross"
                        />
                      </button>
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c12 c13 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c10 c14"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-multi-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component connected snapshots should render hidden 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c14 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c14::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c12 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
  padding: 0 28px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  width: 24px;
  padding: 0;
  line-height: 15px;
}

<div>
  <div
    class="e-field e-multi-reference-field e-hidden"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                      <button
                        aria-label="remove pill"
                        class="c8 c9"
                        data-component="icon-button"
                        data-element="close"
                        type="button"
                      >
                        <span
                          class="c10 c11"
                          data-component="icon"
                          data-element="cross"
                          data-role="icon"
                          font-size="small"
                          type="cross"
                        />
                      </button>
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c12 c13 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c10 c14"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-multi-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component connected snapshots should render in mobile mode 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c16 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c16:hover {
  color: #006437;
  background-color: transparent;
}

.c16::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e96f";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c12 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c14 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: transparent;
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
  padding: 0px;
  width: 40px;
  min-height: 40px;
}

.c14:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c14 .c10 {
  color: var(--colorsActionMajor500);
}

.c14:hover {
  background: var(--colorsActionMajor600);
  color: var(--colorsActionMajorYang100);
}

.c14:hover .c10 {
  color: var(--colorsActionMajorYang100);
}

.c14 .c10 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c14 .c10 svg {
  margin-top: 0;
}

.c15 {
  border-radius: var(--borderRadius050);
  background: transparent;
  padding: var(--spacing100);
  color: var(--colorsActionMinor500);
  padding-left: var(--spacing150);
  padding-right: var(--spacing150);
}

.c15 .c10 {
  position: absolute;
}

.c15 .c10 {
  color: var(--colorsActionMinor500);
}

.c15:hover {
  color: var(--colorsActionMinorYang100);
  background: var(--colorsActionMinor600);
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
  padding: 0 28px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  width: 24px;
  padding: 0;
  line-height: 15px;
}

<div>
  <div
    class="e-field e-multi-reference-field"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field"
      style="align-items: flex-end;"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 0px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                      <button
                        aria-label="remove pill"
                        class="c8 c9"
                        data-component="icon-button"
                        data-element="close"
                        type="button"
                      >
                        <span
                          class="c10 c11"
                          data-component="icon"
                          data-element="cross"
                          data-role="icon"
                          font-size="small"
                          type="cross"
                        />
                      </button>
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c12 c13 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-search-text-uniqguidmock"
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0; overflow: hidden; flex: 1 1 0%;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-lookup-button"
                  style="flex: 0 0 40px;"
                >
                  <button
                    aria-label="lookup"
                    class="c14 c15"
                    data-component="button-minor"
                    data-component-size="medium"
                    data-testid="e-ui-select-lookup-button"
                    draggable="false"
                    id="e-multi-reference-field-lookup-icon-uniqguidmock"
                    type="button"
                  >
                    <span
                      aria-hidden="true"
                      class="c10 c16"
                      color="--colorsActionMajor500"
                      data-component="icon"
                      data-element="lookup"
                      data-role="icon"
                      font-size="small"
                      type="lookup"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component connected snapshots should render in read-only mode 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c9 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c9:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c9::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c9::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background-color: var(--colorsUtilityReadOnly400);
  border-color: var(--colorsUtilityReadOnly600);
}

.c6 .c8 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

<div>
  <div
    class="e-field e-multi-reference-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                readonly=""
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c8 c9 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  readonly=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component connected snapshots should render with columns 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c16 {
  position: relative;
  color: var(--colorsActionMajor500);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c16:hover {
  color: #006437;
  background-color: transparent;
}

.c16::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e96f";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c12 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c14 {
  padding-left: var(--spacing300);
  padding-right: var(--spacing300);
  margin: var(--spacing000);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-flow: wrap;
  -ms-flex-flow: wrap;
  flex-flow: wrap;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  vertical-align: middle;
  outline-offset: 0;
  border: 2px solid transparent;
  box-sizing: border-box;
  font-weight: 500;
  -webkit-text-decoration: none;
  text-decoration: none;
  border-radius: var(--borderRadius400);
  background: transparent;
  border-color: transparent;
  color: var(--colorsActionMajor500);
  font-size: var(--fontSizes100);
  min-height: 40px;
  padding: 0px;
  width: 40px;
  min-height: 40px;
}

.c14:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c14 .c10 {
  color: var(--colorsActionMajor500);
}

.c14:hover {
  background: var(--colorsActionMajor600);
  color: var(--colorsActionMajorYang100);
}

.c14:hover .c10 {
  color: var(--colorsActionMajorYang100);
}

.c14 .c10 {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1px;
  height: 20px;
  width: 20px;
}

.c14 .c10 svg {
  margin-top: 0;
}

.c15 {
  border-radius: var(--borderRadius050);
  background: transparent;
  padding: var(--spacing100);
  color: var(--colorsActionMinor500);
  padding-left: var(--spacing150);
  padding-right: var(--spacing150);
}

.c15 .c10 {
  position: absolute;
}

.c15 .c10 {
  color: var(--colorsActionMinor500);
}

.c15:hover {
  color: var(--colorsActionMinorYang100);
  background: var(--colorsActionMinor600);
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
  padding: 0 28px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  width: 24px;
  padding: 0;
  line-height: 15px;
}

<div>
  <div
    class="e-field e-multi-reference-field e-multi-reference-field-lookup"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 0px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                      <button
                        aria-label="remove pill"
                        class="c8 c9"
                        data-component="icon-button"
                        data-element="close"
                        type="button"
                      >
                        <span
                          class="c10 c11"
                          data-component="icon"
                          data-element="cross"
                          data-role="icon"
                          font-size="small"
                          type="cross"
                        />
                      </button>
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c12 c13 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0; overflow: hidden; flex: 1 1 0%;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-lookup-button"
                  style="flex: 0 0 40px;"
                >
                  <button
                    aria-label="lookup"
                    class="c14 c15"
                    data-component="button-minor"
                    data-component-size="medium"
                    data-testid="e-ui-select-lookup-button"
                    draggable="false"
                    id="e-multi-reference-field-lookup-icon-uniqguidmock"
                    type="button"
                  >
                    <span
                      aria-hidden="true"
                      class="c10 c16"
                      color="--colorsActionMajor500"
                      data-component="icon"
                      data-element="lookup"
                      data-role="icon"
                      font-size="small"
                      type="lookup"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-multi-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component connected snapshots should render with default properties 1`] = `
Object {
  "asFragment": [Function],
  "baseElement": .c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c14 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c14::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c12 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
  padding: 0 28px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  width: 24px;
  padding: 0;
  line-height: 15px;
}

<body>
    <div
      aria-live="polite"
      aria-relevant="additions text"
      id="a11y-status-message"
      role="status"
      style="border: 0px; height: 1px; overflow: hidden; padding: 0px; position: absolute; width: 1px;"
    >
      No results are available.
    </div>
    <div>
      <div
        class="e-field e-multi-reference-field"
        data-label="Test Field Title"
        data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
      >
        <div
          class="e-multi-reference-field-body"
        >
          <div
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="downshift-0-menu"
            class="e-ui-select-input-wrapper"
            role="combobox"
          >
            <div
              class="c0 c1"
            >
              <div
                class="c2"
                data-role="field-line"
              >
                <div
                  class="c3"
                  data-role="label-container"
                  id="label-container-TestPage-test-multi-reference-field-label"
                  width="30"
                >
                  <label
                    class="c4"
                    data-element="label"
                    for="TestPage-test-multi-reference-field"
                    id="TestPage-test-multi-reference-field-label"
                  >
                    Test Field Title
                  </label>
                </div>
                <div
                  class="c5"
                  data-role="input-presentation-container"
                >
                  <div
                    class="c6"
                    role="presentation"
                    style="padding-right: 20px; flex-wrap: nowrap;"
                  >
                    <div
                      class="e-ui-select-input-left-children"
                    >
                      <div
                        class="e-ui-select-label"
                        data-testid="e-ui-select-XX123-pill"
                        tabindex="-1"
                      >
                        <span
                          class="c7"
                          data-component="pill"
                        >
                          XX123
                          <button
                            aria-label="remove pill"
                            class="c8 c9"
                            data-component="icon-button"
                            data-element="close"
                            type="button"
                          >
                            <span
                              class="c10 c11"
                              data-component="icon"
                              data-element="cross"
                              data-role="icon"
                              font-size="small"
                              type="cross"
                            />
                          </button>
                        </span>
                      </div>
                    </div>
                    <input
                      aria-autocomplete="list"
                      aria-controls="downshift-0-menu"
                      aria-invalid="false"
                      aria-label="Test Field Title"
                      autocomplete="off"
                      class="c12 c13 e-field-select-input-text"
                      data-element="input"
                      data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                      id="TestPage-test-multi-reference-field"
                      name="testcarb-onco-mpon-ents-uniqguidmock"
                      placeholder=""
                      style="text-overflow: ellipsis; min-width: 0;"
                      type="text"
                      value=""
                    />
                    <div
                      class="e-ui-select-inline-dropdown"
                      id="downshift-0-toggle-button"
                      tabindex="-1"
                    >
                      <div>
                        <span
                          class="c10 c14"
                          data-component="icon"
                          data-element="dropdown"
                          data-role="icon"
                          font-size="small"
                          type="dropdown"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            aria-busy="true"
            aria-labelledby="downshift-0-label"
            class="e-ui-select-dropdown"
            id="downshift-0-menu"
            style="position: relative; width: 100%;"
          >
            <ul
              aria-labelledby="TestPage-test-multi-reference-field"
              data-testid="e-ui-select-dropdown"
              role="listbox"
              style="max-height: 18px; border-radius: 4px;"
            />
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": .c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c14 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c14::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c12 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
  padding: 0 28px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  width: 24px;
  padding: 0;
  line-height: 15px;
}

<div>
    <div
      class="e-field e-multi-reference-field"
      data-label="Test Field Title"
      data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
    >
      <div
        class="e-multi-reference-field-body"
      >
        <div
          aria-expanded="false"
          aria-haspopup="listbox"
          aria-owns="downshift-0-menu"
          class="e-ui-select-input-wrapper"
          role="combobox"
        >
          <div
            class="c0 c1"
          >
            <div
              class="c2"
              data-role="field-line"
            >
              <div
                class="c3"
                data-role="label-container"
                id="label-container-TestPage-test-multi-reference-field-label"
                width="30"
              >
                <label
                  class="c4"
                  data-element="label"
                  for="TestPage-test-multi-reference-field"
                  id="TestPage-test-multi-reference-field-label"
                >
                  Test Field Title
                </label>
              </div>
              <div
                class="c5"
                data-role="input-presentation-container"
              >
                <div
                  class="c6"
                  role="presentation"
                  style="padding-right: 20px; flex-wrap: nowrap;"
                >
                  <div
                    class="e-ui-select-input-left-children"
                  >
                    <div
                      class="e-ui-select-label"
                      data-testid="e-ui-select-XX123-pill"
                      tabindex="-1"
                    >
                      <span
                        class="c7"
                        data-component="pill"
                      >
                        XX123
                        <button
                          aria-label="remove pill"
                          class="c8 c9"
                          data-component="icon-button"
                          data-element="close"
                          type="button"
                        >
                          <span
                            class="c10 c11"
                            data-component="icon"
                            data-element="cross"
                            data-role="icon"
                            font-size="small"
                            type="cross"
                          />
                        </button>
                      </span>
                    </div>
                  </div>
                  <input
                    aria-autocomplete="list"
                    aria-controls="downshift-0-menu"
                    aria-invalid="false"
                    aria-label="Test Field Title"
                    autocomplete="off"
                    class="c12 c13 e-field-select-input-text"
                    data-element="input"
                    data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                    id="TestPage-test-multi-reference-field"
                    name="testcarb-onco-mpon-ents-uniqguidmock"
                    placeholder=""
                    style="text-overflow: ellipsis; min-width: 0;"
                    type="text"
                    value=""
                  />
                  <div
                    class="e-ui-select-inline-dropdown"
                    id="downshift-0-toggle-button"
                    tabindex="-1"
                  >
                    <div>
                      <span
                        class="c10 c14"
                        data-component="icon"
                        data-element="dropdown"
                        data-role="icon"
                        font-size="small"
                        type="dropdown"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          aria-busy="true"
          aria-labelledby="downshift-0-label"
          class="e-ui-select-dropdown"
          id="downshift-0-menu"
          style="position: relative; width: 100%;"
        >
          <ul
            aria-labelledby="TestPage-test-multi-reference-field"
            data-testid="e-ui-select-dropdown"
            role="listbox"
            style="max-height: 18px; border-radius: 4px;"
          />
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`MultiReference component connected snapshots should render with full-width 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c14 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c14::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c12 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
  padding: 0 28px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  width: 24px;
  padding: 0;
  line-height: 15px;
}

<div>
  <div
    class="e-field e-multi-reference-field e-full-width"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                      <button
                        aria-label="remove pill"
                        class="c8 c9"
                        data-component="icon-button"
                        data-element="close"
                        type="button"
                      >
                        <span
                          class="c10 c11"
                          data-component="icon"
                          data-element="cross"
                          data-role="icon"
                          font-size="small"
                          type="cross"
                        />
                      </button>
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c12 c13 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c10 c14"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-multi-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component connected snapshots should render with onCloseLookupDialog callback 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c14 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c14::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c12 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
  padding: 0 28px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  width: 24px;
  padding: 0;
  line-height: 15px;
}

<div>
  <div
    class="e-field e-multi-reference-field"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                      <button
                        aria-label="remove pill"
                        class="c8 c9"
                        data-component="icon-button"
                        data-element="close"
                        type="button"
                      >
                        <span
                          class="c10 c11"
                          data-component="icon"
                          data-element="cross"
                          data-role="icon"
                          font-size="small"
                          type="cross"
                        />
                      </button>
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c12 c13 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c10 c14"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-multi-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component connected snapshots should render with onOpenLookupDialog callback 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c14 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c14::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c12 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
  padding: 0 28px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  width: 24px;
  padding: 0;
  line-height: 15px;
}

<div>
  <div
    class="e-field e-multi-reference-field"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                      <button
                        aria-label="remove pill"
                        class="c8 c9"
                        data-component="icon-button"
                        data-element="close"
                        type="button"
                      >
                        <span
                          class="c10 c11"
                          data-component="icon"
                          data-element="cross"
                          data-role="icon"
                          font-size="small"
                          type="cross"
                        />
                      </button>
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c12 c13 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c10 c14"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-multi-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component connected snapshots should render with various field sizes 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c14 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c14::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing400);
}

.c6 .c12 {
  padding: 0 var(--spacing100);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 16px;
  line-height: 16px;
  font-size: 12px;
  padding: 0 8px;
  padding: 0 22px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  padding: 0;
  line-height: 16px;
}

.c7 .c8 .c10 {
  top: -2px;
}

.c7 .c8 .c10:before {
  font-size: 16px;
}

<div>
  <div
    class="e-field e-multi-reference-field"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                      <button
                        aria-label="remove pill"
                        class="c8 c9"
                        data-component="icon-button"
                        data-element="close"
                        type="button"
                      >
                        <span
                          class="c10 c11"
                          data-component="icon"
                          data-element="cross"
                          data-role="icon"
                          font-size="small"
                          type="cross"
                        />
                      </button>
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c12 c13 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c10 c14"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-multi-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component connected snapshots should render with various field sizes 2`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c14 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c14::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c15 .c12 {
  padding: 0 var(--spacing100);
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c12 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c16 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c16 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c16 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c16 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c16 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c16 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c16 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c16 .c8 {
  padding: 0;
  line-height: 16px;
}

.c16 .c8 .c10 {
  top: -2px;
}

.c16 .c8 .c10:before {
  font-size: 16px;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
  padding: 0 28px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  width: 24px;
  padding: 0;
  line-height: 15px;
}

<div>
  <div
    class="e-field e-multi-reference-field"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-3-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                      <button
                        aria-label="remove pill"
                        class="c8 c9"
                        data-component="icon-button"
                        data-element="close"
                        type="button"
                      >
                        <span
                          class="c10 c11"
                          data-component="icon"
                          data-element="cross"
                          data-role="icon"
                          font-size="small"
                          type="cross"
                        />
                      </button>
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-3-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c12 c13 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-3-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c10 c14"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-3-label"
        class="e-ui-select-dropdown"
        id="downshift-3-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-multi-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component connected snapshots should render with various field sizes 3`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c14 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c14::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c15 .c12 {
  padding: 0 var(--spacing100);
}

.c16 .c12 {
  padding: 0 var(--spacing150);
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing600);
}

.c6 .c12 {
  padding: 0 var(--spacing200);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c17 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c17 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c17 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c17 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c17 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c17 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c17 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c17 .c8 {
  padding: 0;
  line-height: 16px;
}

.c17 .c8 .c10 {
  top: -2px;
}

.c17 .c8 .c10:before {
  font-size: 16px;
}

.c18 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c18 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c18 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c18 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c18 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c18 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c18 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c18 .c8 {
  width: 24px;
  padding: 0;
  line-height: 15px;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 24px;
  line-height: 24px;
  font-size: 14px;
  padding: 0 8px;
  padding: 0 32px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  width: 28px;
  padding: 0;
  line-height: 16px;
}

<div>
  <div
    class="e-field e-multi-reference-field"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-6-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                      <button
                        aria-label="remove pill"
                        class="c8 c9"
                        data-component="icon-button"
                        data-element="close"
                        type="button"
                      >
                        <span
                          class="c10 c11"
                          data-component="icon"
                          data-element="cross"
                          data-role="icon"
                          font-size="small"
                          type="cross"
                        />
                      </button>
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-6-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c12 c13 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-6-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c10 c14"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-6-label"
        class="e-ui-select-dropdown"
        id="downshift-6-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-multi-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`MultiReference component unconnected snapshots should render just fine 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c1.c1.c1 {
  margin: var(--spacing000);
}

.c2 {
  display: block;
}

.c14 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c14::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e910";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c11 {
  position: relative;
  color: var(--colorsYin090);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c11::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91d";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c13 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c13:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c13::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c13::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c12 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

.c9 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c9.c9 {
  padding: var(--spacing000);
}

.c9:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c9:hover {
  cursor: pointer;
}

.c9::-moz-focus-inner {
  border: none;
}

.c9 .c10 {
  position: relative;
}

.c9 .c10:focus {
  border: none;
}

.c7 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
  padding: 0 28px 0 8px;
}

.c7 .c8 {
  -webkit-appearance: none;
  border-radius: var(--borderRadius000);
  border: none;
  bottom: 0;
  font-size: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 20px;
  margin: 0;
  line-height: 16px;
}

.c7 .c8:focus {
  background-color: var(--colorsSemanticNeutral600);
  border-radius: var(--borderRadius000) var(--borderRadius025) var(--borderRadius025) var(--borderRadius000);
}

.c7 .c8:focus::-moz-focus-inner {
  border: 0;
}

.c7 .c8:focus .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8:hover {
  background-color: var(--colorsSemanticNeutral600);
  color: var(--colorsSemanticNeutralYang100);
  cursor: pointer;
}

.c7 .c8:hover .c10 {
  color: var(--colorsSemanticNeutralYang100);
}

.c7 .c8 .c10 {
  height: unset;
  width: unset;
  color: var(--colorsSemanticNeutralYang100);
  color: var(--colorsUtilityYin090);
}

.c7 .c8 {
  width: 24px;
  padding: 0;
  line-height: 15px;
}

<div>
  <div
    class="e-field e-multi-reference-field"
    data-label="Test Field Title"
    data-testid="e-multi-reference-field e-field-label-testFieldTitle e-field-bind-test-multi-reference-field"
  >
    <div
      class="e-multi-reference-field-body"
    >
      <div
        aria-expanded="false"
        aria-haspopup="listbox"
        aria-owns="downshift-0-menu"
        class="e-ui-select-input-wrapper"
        role="combobox"
      >
        <div
          class="c0 c1"
        >
          <div
            class="c2"
            data-role="field-line"
          >
            <div
              class="c3"
              data-role="label-container"
              id="label-container-TestPage-test-multi-reference-field-label"
              width="30"
            >
              <label
                class="c4"
                data-element="label"
                for="TestPage-test-multi-reference-field"
                id="TestPage-test-multi-reference-field-label"
              >
                Test Field Title
              </label>
            </div>
            <div
              class="c5"
              data-role="input-presentation-container"
            >
              <div
                class="c6"
                role="presentation"
                style="padding-right: 20px; flex-wrap: nowrap;"
              >
                <div
                  class="e-ui-select-input-left-children"
                >
                  <div
                    class="e-ui-select-label"
                    data-testid="e-ui-select-XX123-pill"
                    tabindex="-1"
                  >
                    <span
                      class="c7"
                      data-component="pill"
                    >
                      XX123
                      <button
                        aria-label="remove pill"
                        class="c8 c9"
                        data-component="icon-button"
                        data-element="close"
                        type="button"
                      >
                        <span
                          class="c10 c11"
                          data-component="icon"
                          data-element="cross"
                          data-role="icon"
                          font-size="small"
                          type="cross"
                        />
                      </button>
                    </span>
                  </div>
                </div>
                <input
                  aria-autocomplete="list"
                  aria-controls="downshift-0-menu"
                  aria-invalid="false"
                  aria-label="Test Field Title"
                  autocomplete="off"
                  class="c12 c13 e-field-select-input-text"
                  data-element="input"
                  data-testid="e-multi-reference-field-lookup-input-uniqguidmock"
                  id="TestPage-test-multi-reference-field"
                  name="testcarb-onco-mpon-ents-uniqguidmock"
                  placeholder=""
                  style="text-overflow: ellipsis; min-width: 0;"
                  type="text"
                  value=""
                />
                <div
                  class="e-ui-select-inline-dropdown"
                  id="downshift-0-toggle-button"
                  tabindex="-1"
                >
                  <div>
                    <span
                      class="c10 c14"
                      data-component="icon"
                      data-element="dropdown"
                      data-role="icon"
                      font-size="small"
                      type="dropdown"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        aria-busy="true"
        aria-labelledby="downshift-0-label"
        class="e-ui-select-dropdown"
        id="downshift-0-menu"
        style="position: relative; width: 100%;"
      >
        <ul
          aria-labelledby="TestPage-test-multi-reference-field"
          data-testid="e-ui-select-dropdown"
          role="listbox"
          style="max-height: 18px; border-radius: 4px;"
        />
      </div>
    </div>
  </div>
</div>
`;
