import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { MessageComponentProps } from './message-types';

const ConnectedMessageComponent = React.lazy(() => import('./message-component'));

export function AsyncConnectedMessageComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedMessageComponent {...props} />
        </React.Suspense>
    );
}

const MessageComponent = React.lazy(() => import('./message-component').then(c => ({ default: c.MessageComponent })));

export function AsyncMessageComponent(props: MessageComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader size="small" />}>
            <MessageComponent {...props} />
        </React.Suspense>
    );
}
