/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { MessageControlObject } from './message-control-object';
import type { MessageDecoratorProperties } from './message-types';

class MessageDecorator extends AbstractFieldDecorator<FieldKey.Message> {
    protected _controlObjectConstructor = MessageControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

export function messageField<T extends ScreenExtension<T>>(
    properties: MessageDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Message>(properties, MessageDecorator, FieldKey.Message);
}

export function messageFieldOverride<T extends ScreenExtension<T>>(
    properties: ClickableOverrideDecoratorProperties<MessageDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Message>(properties);
}
