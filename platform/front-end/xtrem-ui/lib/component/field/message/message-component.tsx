/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import * as React from 'react';
import { connect } from 'react-redux';
import { triggerFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-utils';
import type { MessageComponentProps } from './message-types';
import * as showdown from 'showdown';
import { escape as lodashEscape } from 'lodash';
import Message from 'carbon-react/esm/components/message';

const converter = new showdown.Converter();

export function MessageComponent(props: MessageComponentProps): React.ReactElement {
    const { fieldProperties, screenId, value, elementId } = props;
    const getClickHandler = (): Promise<void> => triggerFieldEvent(screenId, elementId, 'onClick');
    const displayedContent = value || fieldProperties.content;
    const resolvedTitle = resolveByValue({
        screenId,
        propertyValue: fieldProperties.title,
        skipHexFormat: true,
        fieldValue: value,
        rowValue: null, // Not available as nested field
    });

    return (
        <CarbonWrapper
            {...props}
            className="e-message-field"
            componentName="message"
            noReadOnlySupport={false}
            value={value || fieldProperties.content}
        >
            {resolvedTitle && !props.fieldProperties.isTitleHidden && <FieldLabel label={resolvedTitle} />}
            <Message showCloseIcon={false} variant={props.fieldProperties.variant}>
                {fieldProperties.isMarkdown ? (
                    <span
                        className="e-dialog-text-content"
                        onClick={getClickHandler}
                        // eslint-disable-next-line react/no-danger
                        dangerouslySetInnerHTML={{
                            __html: converter.makeHtml(lodashEscape(displayedContent)),
                        }}
                    />
                ) : (
                    displayedContent
                )}
            </Message>
            {fieldProperties.helperText && !fieldProperties.isHelperTextHidden && (
                <HelperText helperText={fieldProperties.helperText} />
            )}
        </CarbonWrapper>
    );
}

export const ConnectedMessageComponent = connect(mapStateToProps(), mapDispatchToProps())(MessageComponent);

export default ConnectedMessageComponent;
