import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { MessageProperties, MessageVariant } from './message-types';

export class MessageControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends ReadonlyFieldControlObject<
    CT,
    FieldKey.Message,
    FieldComponentProps<FieldKey.Message>
> {
    static readonly defaultUiProperties: Partial<FieldComponentProps<FieldKey.Message>> = {
        ...ReadonlyFieldControlObject.defaultUiProperties,
    };

    @ControlObjectProperty<MessageProperties<CT>, MessageControlObject<CT>>()
    content?: string;

    @ControlObjectProperty<MessageProperties<CT>, MessageControlObject<CT>>()
    variant?: MessageVariant;
}
