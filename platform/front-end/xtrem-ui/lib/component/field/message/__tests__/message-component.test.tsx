import { renderWithRedux, applyActionMocks } from '../../../../__tests__/test-helpers';

import * as React from 'react';
import type { MessageDecoratorProperties } from '../message-types';
import type { ScreenBase } from '../../../../service/screen-base';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import ConnectedMessageComponent from '../message-component';

describe('message component', () => {
    const screenId = 'TestPage';
    const fieldId = 'testMessage';

    const setup = (
        staticContentProps: MessageDecoratorProperties<ScreenBase> = {},
        value: FieldInternalValue<FieldKey.Message> | null = 'Value of the field',
    ) => {
        const initialState = {
            screenDefinitions: {
                [screenId]: {
                    metadata: {
                        uiComponentProperties: {
                            [fieldId]: staticContentProps,
                        },
                    },
                    values: {
                        ...(value && { [fieldId]: value as any }),
                    },
                    errors: {},
                },
            },
        };
        const utils = renderWithRedux<FieldKey.Message, any>(
            <ConnectedMessageComponent screenId={screenId} elementId={fieldId} />,
            {
                initialState,
                fieldType: FieldKey.Message,
                fieldValue: value as any,
                fieldProperties: staticContentProps,
                elementId: fieldId,
                screenId,
                mockActions: true,
            },
        ) as any;

        const messageField = utils.getByTestId('e-field-bind-testMessage', { exact: false });
        const staticContentBody = messageField.querySelectorAll('[data-element="content-body"]');

        return {
            ...utils,
            messageField,
            messageComponent: staticContentBody[0],
        };
    };

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    it('can render with redux with defaults', () => {
        const { messageField, messageComponent } = setup({ title: 'Test Field Title' });
        expect(messageField).toHaveTextContent('Test Field Title');
        expect(messageComponent).toBeInTheDocument();
    });

    it('can render with redux with value', () => {
        const { messageComponent } = setup({ title: 'Test Field Title' }, 'This is the value');
        expect(messageComponent).toHaveTextContent('This is the value');
    });

    it('should render the content if no value is available', () => {
        const { messageComponent } = setup({ title: 'Test Field Title', content: 'This is the content' }, null);
        expect(messageComponent).toHaveTextContent('This is the content');
    });

    it('should render helperText', () => {
        const { messageField } = setup({ title: 'Test Field Title', helperText: 'This is a helper text' });
        expect(messageField).toHaveTextContent('This is a helper text');
    });
});
