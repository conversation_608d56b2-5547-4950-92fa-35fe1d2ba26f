import type { ScreenBase } from '../../../service/screen-base';
import type { BlockControlObject, SectionControlObject } from '../../control-objects';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type { Clickable, ExtensionField, HasParent } from '../traits';

export type MessageVariant = 'error' | 'info' | 'success' | 'warning';

export interface MessageProperties<CT extends ScreenBase = ScreenBase> extends ReadonlyFieldProperties<CT> {
    content?: string;
    isMarkdown?: boolean;
    variant?: MessageVariant;
}

export interface MessageDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<MessageProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>> {}

export type MessageComponentProps = BaseEditableComponentProperties<MessageDecoratorProperties<any>, string>;
