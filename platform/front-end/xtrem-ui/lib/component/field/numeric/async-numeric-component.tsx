import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { NumericComponentProps } from './numeric-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';

const ConnectedNumericComponent = React.lazy(() => import('./numeric-component'));

export function AsyncConnectedNumericComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedNumericComponent {...props} />
        </React.Suspense>
    );
}

const NumericComponent = React.lazy(() => import('./numeric-component').then(c => ({ default: c.NumericComponent })));

export function AsyncNumericComponent(props: NumericComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <NumericComponent {...props} />
        </React.Suspense>
    );
}
