import {
    addFieldToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    renderWithRedux,
} from '../../../../__tests__/test-helpers';

import { fireEvent, render, waitFor } from '@testing-library/react';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import { ConnectedNumericComponent, NumericComponent } from '../numeric-component';
import type { NumericDecoratorProperties } from '../numeric-types';
import '@testing-library/jest-dom';

describe('Numeric component', () => {
    const screenId = 'TestPage';
    const fieldId = 'test-numeric-field';
    let mockFieldProperties: NumericDecoratorProperties;
    let mockFieldPropertiesWithResolvableScale: NumericDecoratorProperties;
    let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;
    let mockState: xtremRedux.XtremAppState;

    beforeEach(() => {
        mockFieldProperties = {
            title: 'Test Field Title',
            scale: 1,
        };
        mockFieldPropertiesWithResolvableScale = {
            ...mockFieldProperties,
            scale: (value: any) => {
                if (value > 13) {
                    return 6;
                }
                return 5;
            },
        };
    });

    beforeEach(() => {
        mockState = getMockState();
        mockState.screenDefinitions[screenId] = getMockPageDefinition(screenId);
        addFieldToState(FieldKey.Numeric, mockState, screenId, fieldId, mockFieldProperties, 12.3456);
        addFieldToState(FieldKey.Numeric, mockState, screenId, 'test-empty-numeric-field', mockFieldProperties, null);
        addFieldToState(
            FieldKey.Numeric,
            mockState,
            screenId,
            'test-dynamically-scaled-numeric-field',
            mockFieldPropertiesWithResolvableScale,
            null,
        );
        mockStore = getMockStore(mockState);
        jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue(
            () => Promise.resolve({ type: 'SetFieldValue' }) as any,
        );
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    describe('connected', () => {
        describe('integration', () => {
            const setup = (
                textProps: NumericDecoratorProperties = mockFieldProperties,
                value: FieldInternalValue<FieldKey.Numeric> = 0,
            ) => {
                const initialState = {
                    screenDefinitions: {
                        [screenId]: {
                            metadata: {
                                uiComponentProperties: {
                                    [fieldId]: textProps,
                                },
                            },
                            ...(value && {
                                values: {
                                    [fieldId]: value,
                                },
                            }),
                        },
                    },
                };

                const utils = renderWithRedux(<ConnectedNumericComponent screenId={screenId} elementId={fieldId} />, {
                    mockStore,
                    initialState,
                    fieldType: FieldKey.Numeric,
                    fieldValue: value,
                    fieldProperties: textProps,
                    elementId: fieldId,
                    screenId,
                });

                const numeric = utils.getByTestId(
                    'e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field',
                ) as HTMLDivElement;

                const input = numeric.querySelector('input[data-testid="e-ui-decimal-input"]');
                const getIcon = (iconType: string) =>
                    numeric.querySelector(`span[data-component="icon"][data-element="${iconType}"]`);

                const changeValue = (value: string) => {
                    fireEvent.change(input as any, { target: { value } });
                    fireEvent.blur(input as any);
                };

                return {
                    ...utils,
                    changeValue,
                    getIcon,
                    input,
                    numeric,
                };
            };

            it('can render with redux with defaults', () => {
                const { numeric, input } = setup();
                expect(numeric).toHaveTextContent('Test Field Title');
                expect(input).toBeInTheDocument();
            });

            it('can render with icon', () => {
                const { numeric, input, getIcon } = setup({ ...mockFieldProperties, icon: 'scan' });
                expect(numeric).toHaveTextContent('Test Field Title');
                expect(input).toBeInTheDocument();
                expect(getIcon('scan')).toBeInTheDocument();
            });

            it('can render with mandatory validation enabled', () => {
                const { numeric } = setup({ ...mockFieldProperties, isMandatory: true });
                expect(numeric).toHaveTextContent('Test Field Title *');
            });

            it('can render with non-zero validation enabled', () => {
                const { numeric } = setup({ ...mockFieldProperties, isNotZero: true });
                expect(numeric).toHaveTextContent('Test Field Title *');
            });
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render without a scale', () => {
                mockFieldProperties.scale = undefined;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render disabled', () => {
                mockFieldProperties.isDisabled = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with full-width', () => {
                mockFieldProperties.isFullWidth = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render in read only mode', () => {
                mockFieldProperties.isReadOnly = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with a prefix', () => {
                mockFieldProperties.prefix = '$';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with a postfix', () => {
                mockFieldProperties.postfix = 'Kg';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render in read-only mode with a prefix and a postfix', () => {
                mockFieldProperties.isReadOnly = true;
                mockFieldProperties.postfix = 'Kg';
                mockFieldProperties.prefix = '$';

                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render without value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId="test-empty-numeric-field" />
                    </Provider>,
                );

                expect(container).toMatchSnapshot();
            });

            it('should render with various field sizes', () => {
                mockFieldProperties.size = 'small';
                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'medium';
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();

                mockFieldProperties.size = 'large';
                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(wrapper.container).toMatchSnapshot();
            });

            it('should render read-only using a conditional declaration', () => {
                mockFieldProperties.isReadOnly = () => {
                    return true;
                };

                let wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(wrapper.container.querySelector('input[readonly]')).not.toBeNull();

                mockFieldProperties.isReadOnly = () => {
                    return false;
                };

                wrapper = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                expect(wrapper.container.querySelector('input[readonly]')).toBeNull();
            });

            it('should render with an info message', async () => {
                mockFieldProperties.infoMessage = 'Info message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an info message from a callback', async () => {
                mockFieldProperties.infoMessage = () => 'Info message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an warning message', async () => {
                mockFieldProperties.warningMessage = 'Warning message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with an warning message from a callback', async () => {
                mockFieldProperties.warningMessage = () => 'Warning message!!';
                const { baseElement } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            describe('unit', () => {
                it('should render with a unit in English US with 2 decimals', () => {
                    mockState.applicationContext!.locale = 'en-US';
                    mockStore = getMockStore(mockState);
                    mockFieldProperties.unit = { id: 'EUR', symbol: '€', decimalDigits: 2 };
                    mockFieldProperties.scale = undefined;
                    const { container } = render(
                        <Provider store={mockStore}>
                            <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                        </Provider>,
                    );
                    expect(container).toMatchSnapshot();
                });
                it('should render with a unit in German with 2 decimal', () => {
                    mockState.applicationContext!.locale = 'de-DE';
                    mockStore = getMockStore(mockState);
                    mockFieldProperties.unit = { id: 'EUR', symbol: '€', decimalDigits: 2 };
                    mockFieldProperties.scale = undefined;
                    const { container } = render(
                        <Provider store={mockStore}>
                            <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                        </Provider>,
                    );
                    expect(container).toMatchSnapshot();
                });
                it('should render with a unit in English US with 0 decimal', () => {
                    mockState.applicationContext!.locale = 'en-US';
                    mockStore = getMockStore(mockState);
                    mockFieldProperties.unit = { id: 'HUF', symbol: 'Ft', decimalDigits: 0 };
                    mockFieldProperties.scale = undefined;
                    const { container } = render(
                        <Provider store={mockStore}>
                            <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                        </Provider>,
                    );
                    expect(container).toMatchSnapshot();
                });

                it('should render with a unit in German with 0 decimal', () => {
                    mockState.applicationContext!.locale = 'de-DE';
                    mockStore = getMockStore(mockState);
                    mockFieldProperties.unit = { id: 'HUF', symbol: 'Fr', decimalDigits: 0 };
                    mockFieldProperties.scale = undefined;
                    const { container } = render(
                        <Provider store={mockStore}>
                            <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                        </Provider>,
                    );
                    expect(container).toMatchSnapshot();
                });
                it('should render the number with the specified scale when both the unit and the scale is defined', () => {
                    mockState.applicationContext!.locale = 'de-DE';
                    mockStore = getMockStore(mockState);
                    mockFieldProperties.unit = { id: 'HUF', symbol: 'Fr', decimalDigits: 0 };
                    mockFieldProperties.scale = 4;
                    const { container } = render(
                        <Provider store={mockStore}>
                            <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                        </Provider>,
                    );
                    expect(container).toMatchSnapshot();
                });
            });
        });

        describe('Interactions', () => {
            it('should render helperText', () => {
                mockFieldProperties.helperText = 'This is a helper text';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="help"]')).not.toBeNull();
            });

            it('Should not render helperText', () => {
                mockFieldProperties.helperText = '';
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedNumericComponent screenId={screenId} elementId={fieldId} />
                    </Provider>,
                );
                expect(container.querySelector('[data-element="help"]')).toBeNull();
            });
        });
    });

    describe('unconnected', () => {
        it('should render just fine', () => {
            const { container } = render(
                <Provider store={mockStore}>
                    <NumericComponent
                        elementId={fieldId}
                        fieldProperties={mockFieldProperties}
                        value={12.3456}
                        setFieldValue={jest.fn().mockResolvedValue(undefined)}
                        validate={jest.fn().mockResolvedValue(undefined)}
                        removeNonNestedErrors={jest.fn()}
                        screenId={screenId}
                        locale="en-US"
                        onFocus={jest.fn()}
                    />
                </Provider>,
            );

            expect(container).toMatchSnapshot();
        });

        describe('Interactions', () => {
            it('should resolve scale value with the row data argument if it is provided', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <NumericComponent
                            elementId="test-dynamically-scaled-numeric-field"
                            fieldProperties={{
                                ...mockFieldPropertiesWithResolvableScale,
                                scale(value, rowValue: any) {
                                    return rowValue!.aProperty.thatDetermines.theScale;
                                },
                            }}
                            value={12.345678}
                            setFieldValue={jest.fn().mockResolvedValue(undefined)}
                            validate={jest.fn().mockResolvedValue(undefined)}
                            removeNonNestedErrors={jest.fn()}
                            screenId={screenId}
                            onFocus={jest.fn()}
                            locale="en-US"
                            handlersArguments={{ rowValue: { aProperty: { thatDetermines: { theScale: 4 } } } }}
                        />
                    </Provider>,
                );

                expect(container.querySelector('input')).toHaveValue('12.3457');
            });

            it('should reset to original value on Escape keydown', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <NumericComponent
                            elementId={fieldId}
                            fieldProperties={mockFieldProperties}
                            value={12.2}
                            setFieldValue={jest.fn().mockResolvedValue(undefined)}
                            validate={jest.fn().mockResolvedValue(undefined)}
                            removeNonNestedErrors={jest.fn()}
                            screenId={screenId}
                            onFocus={jest.fn()}
                            locale="en-US"
                        />
                    </Provider>,
                );

                const input = container.querySelector('input[data-element="input"]')!;
                fireEvent.change(input, { target: { value: '12.3' } });
                expect(input).toHaveValue('12.3');

                fireEvent.keyDown(input, { key: 'Escape' });
                expect(input).toHaveValue('12.2');
            });
        });
    });
});
