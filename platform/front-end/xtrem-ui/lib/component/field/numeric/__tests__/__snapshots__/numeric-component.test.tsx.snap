// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Numeric component connected Snapshots should render disabled 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
  color: var(--colorsUtilityYin030);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
  color: var(--colorsUtilityYin030);
  cursor: not-allowed;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background: var(--colorsUtilityDisabled400);
  border-color: var(--colorsUtilityDisabled600);
  cursor: not-allowed;
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field e-disabled"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            disabled=""
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            disabled=""
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              disabled=""
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              placeholder=""
              type="text"
              value="12.3"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render helperText 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c9 {
  display: block;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin-top: 8px;
  white-space: pre-wrap;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-describedby="TestPage-test-numeric-field-field-help"
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12.3"
            />
          </div>
        </div>
      </div>
      <span
        class="c9"
        data-element="help"
        id="TestPage-test-numeric-field-field-help"
      >
        This is a helper text
      </span>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render hidden 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field e-hidden"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12.3"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render in read only mode 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background-color: var(--colorsUtilityReadOnly400);
  border-color: var(--colorsUtilityReadOnly600);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            readonly=""
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              placeholder=""
              readonly=""
              type="text"
              value="12.3"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render in read-only mode with a prefix and a postfix 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
  background-color: var(--colorsUtilityReadOnly400);
  border-color: var(--colorsUtilityReadOnly600);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field e-read-only"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            readonly=""
            role="presentation"
          >
            <span
              class="e-field-prefix"
            >
              $
            </span>
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              placeholder=""
              readonly=""
              type="text"
              value="12.3"
            />
            <span
              class="e-field-postfix"
            >
              Kg
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render with a postfix 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12.3"
            />
            <span
              class="e-field-postfix"
            >
              Kg
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render with a prefix 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <span
              class="e-field-prefix"
            >
              $
            </span>
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12.3"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render with default properties 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12.3"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render with full-width 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field e-full-width"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12.3"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render with various field sizes 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing400);
}

.c6 .c7 {
  padding: 0 var(--spacing100);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12.3"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render with various field sizes 2`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c9 .c7 {
  padding: 0 var(--spacing100);
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12.3"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render with various field sizes 3`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c9 .c7 {
  padding: 0 var(--spacing100);
}

.c10 .c7 {
  padding: 0 var(--spacing150);
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing600);
}

.c6 .c7 {
  padding: 0 var(--spacing200);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12.3"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render without a scale 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots should render without value 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-empty-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-empty-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-empty-numeric-field"
            id="TestPage-test-empty-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-empty-numeric-field"
              name="test-empty-numeric-field"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots unit should render the number with the specified scale when both the unit and the scale is defined 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12,3456"
            />
            <span
              class="e-field-postfix"
            >
              Fr
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots unit should render with a unit in English US with 0 decimal 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <span
              class="e-field-prefix"
            >
              Ft
            </span>
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots unit should render with a unit in English US with 2 decimals 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <span
              class="e-field-prefix"
            >
              €
            </span>
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12.35"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots unit should render with a unit in German with 0 decimal 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12"
            />
            <span
              class="e-field-postfix"
            >
              Fr
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component connected Snapshots unit should render with a unit in German with 2 decimal 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12,35"
            />
            <span
              class="e-field-postfix"
            >
              €
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Numeric component unconnected should render just fine 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  background: transparent;
  border: none;
  color: var(--colorsUtilityYin090);
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: var(--fontSizes100);
  outline: none;
  padding: 0;
  margin: 0;
  width: 30px;
  border-radius: var(--borderRadius050);
  text-align: left;
}

.c8:-webkit-autofill {
  background-clip: text;
  -webkit-background-clip: text;
}

.c8::-webkit-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::-moz-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8:-ms-input-placeholder {
  color: var(--colorsUtilityYin055);
}

.c8::placeholder {
  color: var(--colorsUtilityYin055);
}

.c5 {
  -webkit-flex: 0 0 70%;
  -ms-flex: 0 0 70%;
  flex: 0 0 70%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  max-width: 100%;
}

.c6 {
  -webkit-align-items: stretch;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  background: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
  box-sizing: border-box;
  cursor: text;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  margin: 0;
  border-radius: var(--borderRadius050);
  min-height: var(--sizing500);
}

.c6 .c7 {
  padding: 0 var(--spacing150);
}

.c6 input::-ms-clear {
  display: none;
}

.c6 input::-webkit-contacts-auto-fill-button {
  display: none !important;
}

<div>
  <div
    class="e-field e-numeric-field e-decimal-field"
    data-label="Test Field Title"
    data-testid="e-numeric-field e-field-label-testFieldTitle e-field-bind-test-numeric-field"
  >
    <div
      class="c0 c1"
    >
      <div
        class="c2"
        data-role="field-line"
      >
        <div
          class="c3"
          data-role="label-container"
          id="label-container-TestPage-test-numeric-field-label"
          width="30"
        >
          <label
            class="c4"
            data-element="label"
            for="TestPage-test-numeric-field"
            id="TestPage-test-numeric-field-label"
          >
            Test Field Title
          </label>
        </div>
        <div
          class="c5"
          data-role="input-presentation-container"
        >
          <div
            class="c6"
            role="presentation"
          >
            <input
              aria-invalid="false"
              autocomplete="off"
              class="c7 c8"
              data-element="input"
              data-label="Test Field Title"
              data-testid="e-ui-decimal-input"
              id="TestPage-test-numeric-field"
              name="test-numeric-field"
              type="text"
              value="12.3"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
