import type { <PERSON><PERSON><PERSON> } from '../../../types';
import { NumericControlObject } from '../../../control-objects';
import type { NumericProperties } from '../numeric-types';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';

describe('Numeric control object', () => {
    let numericControlObject: NumericControlObject;
    let numericProperties: NumericProperties;
    let numericValue: number;

    beforeEach(() => {
        numericProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
        };
        numericValue = 1.3;
        numericControlObject = buildControlObject<FieldKey.Numeric>(NumericControlObject, {
            fieldValue: numericValue,
            fieldProperties: numericProperties,
        });
    });

    it('getting field value', () => {
        expect(numericControlObject.value).toEqual(numericValue);
    });

    it('should return a number value', () => {
        expect(typeof numericControlObject.value).toBe('number');
    });

    it('should set the title', () => {
        const testFixture = 'Test Numeric Field Title';
        expect(numericProperties.title).not.toEqual(testFixture);
        numericControlObject.title = testFixture;
        expect(numericProperties.title).toEqual(testFixture);
    });

    it('should set the scale digits', () => {
        const testFixture = 5;
        expect(numericProperties.scale).not.toEqual(testFixture);
        numericControlObject.scale = testFixture;
        expect(numericProperties.scale).toEqual(testFixture);
    });

    it('should set the value prefix', () => {
        const testFixture = '$$';
        expect(numericProperties.prefix).not.toEqual(testFixture);
        numericControlObject.prefix = testFixture;
        expect(numericProperties.prefix).toEqual(testFixture);
    });

    it('should set the value postfix', () => {
        const testFixture = '££';
        expect(numericProperties.postfix).not.toEqual(testFixture);
        numericControlObject.postfix = testFixture;
        expect(numericProperties.postfix).toEqual(testFixture);
    });

    it('should set the min value', () => {
        const testFixture = 2;
        expect(numericProperties.min).not.toEqual(testFixture);
        numericControlObject.min = testFixture;
        expect(numericProperties.min).toEqual(testFixture);
    });

    it('should set the max value', () => {
        const testFixture = 32;
        expect(numericProperties.max).not.toEqual(testFixture);
        numericControlObject.max = testFixture;
        expect(numericProperties.max).toEqual(testFixture);
    });
});
