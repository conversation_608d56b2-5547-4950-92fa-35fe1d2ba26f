import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { NumericDecoratorProperties } from '../numeric-types';
import { numericField } from '../numeric-decorator';

describe('Numeric decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;
    let onClickMock: jest.Mock;
    let onChangeMock: jest.Mock;

    beforeEach(() => {
        fieldId = 'numericField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
        onClickMock = jest.fn();
        onChangeMock = jest.fn();
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should set default values when no component properties provided', () => {
            numericField({})({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});

            const mappedComponentProperties: NumericDecoratorProperties<Page> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.scale).toBeUndefined();
            expect(mappedComponentProperties.prefix).toBeUndefined();
            expect(mappedComponentProperties.postfix).toBeUndefined();
            expect(mappedComponentProperties.max).toBeUndefined();
            expect(mappedComponentProperties.min).toBeUndefined();
            expect(mappedComponentProperties.onClick).toBeUndefined();
            expect(mappedComponentProperties.onChange).toBeUndefined();
        });

        it('should set values when component properties provided', () => {
            numericField({
                scale: 1,
                prefix: 'prefix_test',
                postfix: 'postfix_test',
                max: 99,
                min: 11,
                onClick: onClickMock,
                onChange: onChangeMock,
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});

            const mappedComponentProperties: NumericDecoratorProperties<Page> =
                pageMetadata.uiComponentProperties[fieldId];
            expect(mappedComponentProperties.scale).toBe(1);
            expect(mappedComponentProperties.prefix).toBe('prefix_test');
            expect(mappedComponentProperties.postfix).toBe('postfix_test');
            expect(mappedComponentProperties.max).toBe(99);
            expect(mappedComponentProperties.min).toBe(11);
            expect(mappedComponentProperties.onClick).toBe(onClickMock);
            expect(mappedComponentProperties.onChange).toBe(onChangeMock);
            expect(onClickMock).not.toHaveBeenCalled();
            expect(onChangeMock).not.toHaveBeenCalled();
        });

        it('should assign onClick handler', () => {
            testOnClickHandler(numericField, pageMetadata, fieldId);
        });
    });
});
