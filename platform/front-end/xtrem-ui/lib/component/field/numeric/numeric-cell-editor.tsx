import React from 'react';
import { localize } from '../../../service/i18n-service';
import { defaultCellEditorStyles, getInitialCellEditorState } from '../../../utils/ag-grid/ag-grid-cell-editor-utils';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import { getScalePrefixPostfixFromUnit, parseLocalizedNumberStringToNumber } from '../../../utils/formatters';
import { splitValueToMergedValue } from '../../../utils/transformers';
import type { NestedNumericProperties } from '../../nested-fields-properties';
import { onNumericInputKeyDown } from './numeric-cell-utils';
import { RecordActionType } from '../../../service/collection-data-types';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { get } from 'lodash';
import type { Unsubscribe } from 'redux';

interface NumericEditorState {
    value: string;
    highlightOnFocus: boolean;
}

export default class NumericEditor extends React.Component<CellParams<NestedNumericProperties>, NumericEditorState> {
    private readonly input: React.RefObject<HTMLInputElement>;

    private valueChangeSubscription: Unsubscribe;

    constructor(props: CellParams<NestedNumericProperties>) {
        super(props);
        this.state = getInitialCellEditorState({
            eventKey: props.eventKey,
            initialValue: props.initialValue,
            isNumeric: true,
        });
        this.input = React.createRef();
        this.onKeyDown = this.onKeyDown.bind(this);
        this.onChange = this.onChange.bind(this);
        // We need to call onValueChange to reflect the getInitialCellEditorState value to the parent, for example if the user starts editing inserting a number to not lose it.
        this.props.onValueChange(this.state.value);
    }

    componentDidMount(): void {
        const input = this.input.current;
        if (!input) {
            return;
        }
        input.focus();
        if (this.state.highlightOnFocus) {
            input.select();
            this.setState({
                highlightOnFocus: false,
            });
        } else {
            // when we started editing, we want the caret at the end, not the start.
            // this comes into play in two scenarios: a) when user hits F2 and b)
            // when user hits a printable character, then on IE (and only IE) the caret
            // was placed after the first character, thus 'apply' would end up as 'pplea'
            const length = input.value ? input.value.length : 0;
            if (length > 0) {
                // The selection cannot be set on number input types, so we temporarily switch the type to text.
                input.setSelectionRange(length, length);
            }
        }

        input.addEventListener('keydown', this.onKeyDown as any);

        this.valueChangeSubscription = this.props
            .collectionValue()
            ?.subscribeForValueChanges(this.onExternalValueChange);
    }

    componentWillUnmount(): void {
        this.input.current?.removeEventListener('keydown', this.onKeyDown as any);
        this.valueChangeSubscription?.();
    }

    onChange(event: React.ChangeEvent<HTMLInputElement>): void {
        this.setState({ value: event.target.value });

        if (event.target.value === '') {
            this.props.onValueChange(null);
        } else {
            this.props.onValueChange(
                parseLocalizedNumberStringToNumber(
                    event.target.value,
                    localize('@sage/xtrem-ui/number-format-separator', '.'),
                ),
            );
        }
    }

    onKeyDown(event: React.KeyboardEvent<HTMLInputElement>): void {
        const rowValue = splitValueToMergedValue(this.props.data);
        const computedUnitProperties = getScalePrefixPostfixFromUnit(
            this.props.screenId,
            this.props.locale,
            this.props.colDef?.cellEditorParams?.fieldProperties,
            rowValue,
        );

        onNumericInputKeyDown({
            event,
            screenId: this.props.screenId,
            scale: this.props.colDef?.cellEditorParams?.fieldProperties?.scale,
            fieldValue: this.props.value,
            rowValue,
            unitScale: computedUnitProperties?.scale,
        });
    }

    onExternalValueChange = (type: RecordActionType, rowValue: any): void => {
        if (type !== RecordActionType.MODIFIED || rowValue._id !== this.props.data._id) {
            return;
        }

        const { value } = getInitialCellEditorState({
            eventKey: this.props.eventKey,
            initialValue: get(rowValue, convertDeepBindToPathNotNull(this.props.fieldProperties.bind)),
            isNumeric: true,
        });

        if (this.state.value === value) {
            return;
        }

        this.props.onValueChange(value);

        this.setState({ value }, () => {
            this.input.current?.focus();
            this.input.current?.select();
        });
    };

    render(): React.ReactNode {
        return (
            <div className="ag-cell-edit-wrapper">
                <input
                    className="e-numeric-cell-editor"
                    data-testid={`${this.props.tableElementId}-${this.props.node.rowIndex}-${
                        this.props.api.getColumns()!.indexOf(this.props.column!) + 1
                    }`}
                    ref={this.input}
                    type="text"
                    value={this.state.value}
                    onChange={this.onChange}
                    style={defaultCellEditorStyles}
                />
            </div>
        );
    }
}
