import type { ClientNode } from '@sage/xtrem-client';
import type { ScreenBase } from '../../../service/screen-base';
import type { ValueOrCallback, ValueOrCallbackWitRecordValue } from '../../../utils/types';
import type { BlockControlObject, TileControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { NestedPropertiesWrapper } from '../../nested-fields';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type {
    Changeable,
    Clickable,
    ExtensionField,
    HasIcon,
    HasInputValueChangeListener,
    HasMaxMin,
    HasParent,
    HasPlaceholder,
    HasScale,
    HasUnit,
    Nested,
    NestedChangeable,
    NestedClickable,
    NestedGroupAggregations,
    NestedHasUnit,
    NestedValidatable,
    Postfixable,
    Prefixable,
    Sizable,
    Validatable,
} from '../traits';

export interface NumericProperties<CT extends ScreenBase = ScreenBase, ContextNodeType = void>
    extends EditableFieldProperties<CT, ContextNodeType>,
        HasIcon,
        HasPlaceholder,
        HasScale<CT, ContextNodeType>,
        HasMaxMin<CT>,
        Postfixable<CT, ContextNodeType>,
        Prefixable<CT, ContextNodeType>,
        Sizable {
    /** Validation property, ensures that there is a value in the input that is not equal to zero. */
    isNotZero?: ValueOrCallback<CT, boolean>;
}

export interface NumericDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<NumericProperties<CT>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasInputValueChangeListener<CT>,
        HasParent<CT, BlockControlObject<CT> | TileControlObject<CT>>,
        HasUnit<CT>,
        Sizable,
        Validatable<CT, number> {}

export interface NestedNumericProperties<CT extends ScreenBase = ScreenBase, ContextNodeType extends ClientNode = any>
    extends NestedPropertiesWrapper<NumericProperties<CT, ContextNodeType>>,
        Nested<ContextNodeType>,
        NestedChangeable<CT>,
        NestedClickable<CT, ContextNodeType>,
        NestedGroupAggregations,
        NestedHasUnit<CT, ContextNodeType>,
        NestedValidatable<CT, number, ContextNodeType>,
        Sizable {
    isTableReadOnly?: boolean;
    /** The maximum value allowed for the numeric field */
    max?: ValueOrCallbackWitRecordValue<CT, number, ContextNodeType>;
    /** The minimum value allowed for the numeric field */
    min?: ValueOrCallbackWitRecordValue<CT, number, ContextNodeType>;
    /** Validation property, ensures that there is a value in the input that is not equal to zero. */
    isNotZero?: ValueOrCallbackWitRecordValue<CT, boolean, ContextNodeType>;
}

export type NumericComponentProps = BaseEditableComponentProperties<
    NumericProperties,
    number,
    NestedFieldsAdditionalProperties
>;
