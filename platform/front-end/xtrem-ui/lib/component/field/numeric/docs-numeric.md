## Documentation - Numeric
| name               | optional | description                                                                                                                                                                                                                                    | type                                                                                                                                                                                                                                 | default                                                                                                                                              |
| ------------------ | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------- |
| access             | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">AccessConfiguration</pre>                                                                                                                                                                                     | <pre lang="javascript">{&nbsp;node:&nbsp;'<node>',&nbsp;bind:&nbsp;'<bind>'&nbsp;}</pre>                                                             |
| bind               | true     | The GraphQL node that the field’s value is bound to. If not provided, the field name is used instead */ // eslint-disable-next-line @typescript-eslint/no-unused-vars                                                                          | <pre lang="javascript">ReferenceValueType<any></pre>                                                                                                                                                                                 | <pre lang="javascript">undefined</pre>                                                                                                               |
| fetchesDefaults    | true     | Should fetch default values for other fields when the field's value is changed                                                                                                                                                                 | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                 | <pre lang="javascript">false</pre>                                                                                                                   |
| helperText         | true     | A text that will be displayed below the field                                                                                                                                                                                                  | <pre lang="javascript">string</pre>                                                                                                                                                                                                  | <pre lang="javascript">''</pre>                                                                                                                      |
| icon               | true     | Icon of the input field. It will be placed on the right side                                                                                                                                                                                   | <pre lang="javascript">IconType</pre>                                                                                                                                                                                                | <pre lang="javascript">undefined</pre>                                                                                                               |
| iconColor          | true     | Color of the icon, only supported in tile containers                                                                                                                                                                                           | <pre lang="javascript">string</pre>                                                                                                                                                                                                  | <pre lang="javascript">''</pre>                                                                                                                      |
| infoMessage        | true     | Indicate additional warning message, rendered as tooltip and blue border                                                                                                                                                                       | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;ContextNodeType,&nbsp;Dict<any>></pre>                                                                                                                    | <pre lang="javascript">''</pre>                                                                                                                      |
| insertBefore       | true     | The field before this extension field is inserted                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;T</pre>                                                                                                                                                                          | <pre lang="javascript">undefined</pre>                                                                                                               |
| isDisabled         | true     | Whether the HTML element is disabled or not. Defaults to false The difference with readOnly is that disabled suggests that the field is not editable for some validation reason (e.g. a button which can't be clicked due to validation errors | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                                               | <pre lang="javascript">false</pre>                                                                                                                   |
| isFullWidth        | true     | Whether the field spans all the parent width or not. Defaults to false                                                                                                                                                                         | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                 | <pre lang="javascript">false</pre>                                                                                                                   |
| isHelperTextHidden | true     | Whether the field helper text is hidden or not. Defaults to false                                                                                                                                                                              | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                 | <pre lang="javascript">false</pre>                                                                                                                   |
| isHidden           | true     | Whether the HTML element is hidden or not. Defaults to false                                                                                                                                                                                   | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                                               | <pre lang="javascript">false</pre>                                                                                                                   |
| isHiddenDesktop    | true     | Whether the element is hidden or not in desktop devices. Defaults to false                                                                                                                                                                     | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                 | <pre lang="javascript">false</pre>                                                                                                                   |
| isHiddenMobile     | true     | Whether the element is hidden or not in mobile devices. Defaults to false                                                                                                                                                                      | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                 | <pre lang="javascript">false</pre>                                                                                                                   |
| isMandatory        | true     | Whether is mandatory to provide a value for the field or not. Defaults to false                                                                                                                                                                | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;ContextNodeType,&nbsp;Dict<any>></pre>                                                                                                                   | <pre lang="javascript">false</pre>                                                                                                                   |
| isNotZero          | true     | Validation property, ensures that there is a value in the input that is not equal to zero                                                                                                                                                      | <pre lang="javascript">ValueOrCallback<CT,&nbsp;boolean></pre>                                                                                                                                                                       | <pre lang="javascript">undefined</pre>                                                                                                               |
| isReadOnly         | true     | Whether the field value can only be read or not. Defaults to false The difference with disabled is that readOnly suggests that the field is never editable e.g. the id field of an object                                                      | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;boolean,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                                               | <pre lang="javascript">false</pre>                                                                                                                   |
| isTitleHidden      | true     | Whether the element title is hidden or not. Defaults to false                                                                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                 | <pre lang="javascript">false</pre>                                                                                                                   |
| isTransient        | true     | Whether the value is bound to a GraphQL node (transient = false) or not (transient = true). Defaults to false                                                                                                                                  | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                 | <pre lang="javascript">false</pre>                                                                                                                   |
| isTransientInput   | true     | Whether the value is bound only to GraphQL mutations (isTransientInput = true) or not (isTransientInput = false). Defaults to false                                                                                                            | <pre lang="javascript">boolean</pre>                                                                                                                                                                                                 | <pre lang="javascript">false</pre>                                                                                                                   |
| max                | true     | The maximum value allowed for the numeric field                                                                                                                                                                                                | <pre lang="javascript">ValueOrCallback<CT,&nbsp;number></pre>                                                                                                                                                                        | <pre lang="javascript">undefined</pre>                                                                                                               |
| min                | true     | The minimum value allowed for the numeric field                                                                                                                                                                                                | <pre lang="javascript">ValueOrCallback<CT,&nbsp;number></pre>                                                                                                                                                                        | <pre lang="javascript">undefined</pre>                                                                                                               |
| onChange           | true     | Function to be executed when the field's value changes                                                                                                                                                                                         | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                                                                                                                                                       | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                                                                                          |
| onClick            | true     | Function to be executed when the field is clicked                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;void</pre>                                                                                                                                                                       | <pre lang="javascript">function&nbsp;()&nbsp;{&nbsp;}</pre>                                                                                          |
| onError            | true     |                                                                                                                                                                                                                                                | <pre lang="javascript">ErrorHandlerFunction<CT></pre>                                                                                                                                                                                | <pre lang="javascript">function(error,screenId,elementId<br>)&nbsp;{<br>console.error({&nbsp;error,&nbsp;screenId,&nbsp;elementId&nbsp;})<br>}</pre> |
| onInputValueChange | true     | Function to be executed when the input of the field changes                                                                                                                                                                                    | <pre lang="javascript">(this:&nbsp;CT,&nbsp;inputValue:&nbsp;string)&nbsp;=>&nbsp;void</pre>                                                                                                                                         | <pre lang="javascript">function(inputValue<br>)&nbsp;{<br><br>}</pre>                                                                                |
| parent             | true     | The container in which this component will render                                                                                                                                                                                              | <pre lang="javascript">(this:&nbsp;CT)&nbsp;=>&nbsp;P</pre>                                                                                                                                                                          | <pre lang="javascript">undefined</pre>                                                                                                               |
| placeholder        | true     | Placeholder to be displayed in the field body                                                                                                                                                                                                  | <pre lang="javascript">string</pre>                                                                                                                                                                                                  | <pre lang="javascript">''</pre>                                                                                                                      |
| postfix            | true     | Text to be displayed inline after the field value                                                                                                                                                                                              | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                                                | <pre lang="javascript">''</pre>                                                                                                                      |
| prefix             | true     | Text to be displayed inline before the field value                                                                                                                                                                                             | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                                                | <pre lang="javascript">''</pre>                                                                                                                      |
| scale              | true     | Number of digits after the numeric field value decimal point Must be in the range 0 - 20, inclusive                                                                                                                                            | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;number,&nbsp;number,&nbsp;Dict<any>></pre>                                                                                                                             | <pre lang="javascript">0</pre>                                                                                                                       |
| size               | true     | The field's vertical size, default is medium                                                                                                                                                                                                   | <pre lang="javascript">FieldWidth</pre>                                                                                                                                                                                              | <pre lang="javascript">'medium'</pre>                                                                                                                |
| title              | true     | The title of the HTML element                                                                                                                                                                                                                  | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;any,&nbsp;Dict<any>></pre>                                                                                                                                | <pre lang="javascript">''</pre>                                                                                                                      |
| validation         | true     | Additional validation rules, can be a function or a regex (e.g. /^AZ]$/i                                                                                                                                                                       | <pre lang="javascript">RegExp&nbsp;|&nbsp;((this:&nbsp;CT,&nbsp;value:&nbsp;V)&nbsp;=>&nbsp;string&nbsp;|&nbsp;undefined)&nbsp;|&nbsp;((this:&nbsp;CT,&nbsp;value:&nbsp;V)&nbsp;=>&nbsp;Promise<string&nbsp;|&nbsp;undefined>)</pre> | <pre lang="javascript">function(value,&nbsp;value<br>)&nbsp;{<br><br>}</pre>                                                                         |
| warningMessage     | true     | Indicate additional information, rendered as tooltip and orange border                                                                                                                                                                         | <pre lang="javascript">ValueOrCallbackWithFieldValue<CT,&nbsp;string,&nbsp;ContextNodeType,&nbsp;Dict<any>></pre>                                                                                                                    | <pre lang="javascript">''</pre>                                                                                                                      |
| width              | true     | The width of the block relative to the section where it is place Must be a number between 1 and 12, both included                                                                                                                              | <pre lang="javascript">FieldWidth</pre>                                                                                                                                                                                              | <pre lang="javascript">'medium'</pre>                                                                                                                |