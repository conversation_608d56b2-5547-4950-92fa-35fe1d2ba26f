import { deepMerge } from '@sage/xtrem-shared';
import Textbox from 'carbon-react/esm/components/textbox';
import { debounce, set } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import { localize } from '../../../service/i18n-service';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { isKeyPressedNumeric } from '../../../utils/ag-grid/ag-grid-cell-editor-utils';
import { triggerFieldEvent, triggerNestedFieldEvent } from '../../../utils/events';
import {
    formatNumericValue,
    getScalePrefixPostfixFromUnit,
    parseLocalizedNumberStringToNumber,
} from '../../../utils/formatters';
import { useFocus } from '../../../utils/hooks/effects/use-focus';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../../utils/transformers';
import { getCommonCarbonComponentProperties } from '../carbon-helpers';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-component';
import { isInputLimitedByScale } from './numeric-cell-utils';
import type { NumericComponentProps } from './numeric-types';
import type { HasUnit } from '../traits';

export function NumericComponent(props: NumericComponentProps): React.ReactElement {
    const {
        screenId,
        fieldProperties,
        value,
        handlersArguments,
        locale,
        elementId,
        parentElementId,
        columnDefinition,
        isInFocus,
        isNested,
        setFieldValue,
        validate,
    } = props;
    const ref = React.useRef<HTMLInputElement>(null);
    useFocus(ref, isInFocus);

    const computedUnitProperties = getScalePrefixPostfixFromUnit(
        screenId,
        locale,
        fieldProperties as HasUnit<any>,
        handlersArguments?.rowValue,
    );

    const { scale } = fieldProperties;
    const sep = React.useMemo(() => localize('@sage/xtrem-ui/number-format-separator', '.', {}, locale), [locale]);
    const resolvedScale = React.useMemo(
        () =>
            resolveByValue<number>({
                screenId,
                propertyValue: scale,
                skipHexFormat: true,
                rowValue: handlersArguments?.rowValue ? splitValueToMergedValue(handlersArguments?.rowValue) : null,
                fieldValue: null,
            }) ??
            computedUnitProperties?.scale ??
            0,
        // explicitly add value as a dependency to trigger recalculation when value changes
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [handlersArguments?.rowValue, computedUnitProperties, scale, screenId, value],
    );

    const formatValue = React.useCallback(
        () =>
            formatNumericValue({
                screenId,
                value,
                scale: resolvedScale,
                rowValue: handlersArguments?.rowValue,
            }),
        [handlersArguments?.rowValue, resolvedScale, screenId, value],
    );

    const [internalValue, setInternalValue] = React.useState<string>(formatValue);

    const changeEventHandler = React.useCallback(
        (newValue?: any): void => {
            if (isNested && handlersArguments?.onChange && parentElementId) {
                const rowValue =
                    newValue !== undefined
                        ? deepMerge(
                              handlersArguments.rowValue,
                              set(
                                  {},
                                  convertDeepBindToPathNotNull(
                                      columnDefinition?.properties?.bind || fieldProperties?.bind || elementId,
                                  ),
                                  newValue,
                              ),
                          )
                        : handlersArguments?.rowValue;
                triggerNestedFieldEvent(
                    screenId,
                    parentElementId || elementId,
                    fieldProperties as any,
                    'onChange',
                    rowValue?._id,
                    rowValue,
                );
            } else {
                triggerFieldEvent(screenId, elementId, 'onChange');
            }
        },
        [
            isNested,
            handlersArguments?.onChange,
            handlersArguments?.rowValue,
            parentElementId,
            columnDefinition?.properties?.bind,
            fieldProperties,
            elementId,
            screenId,
        ],
    );

    const clickEventHandler = React.useCallback((): void => {
        if (isNested && handlersArguments?.onClick && parentElementId) {
            triggerNestedFieldEvent(
                screenId,
                parentElementId,
                fieldProperties as any,
                'onClick',
                ...(handlersArguments?.onClick as [
                    rowId: string,
                    rowData: any,
                    level?: number,
                    ancestorIds?: string[],
                ]),
            );
        } else {
            triggerFieldEvent(screenId, elementId, 'onClick');
        }
    }, [screenId, elementId, parentElementId, isNested, handlersArguments, fieldProperties]);

    const isUnchangedValue = React.useCallback((): boolean => {
        const numericValue = parseLocalizedNumberStringToNumber(internalValue, sep);

        // Do not trigger the change event if the value has not changed.
        if (
            (value === undefined && numericValue === undefined) ||
            (value === null && numericValue === null) ||
            value === numericValue
        ) {
            return true;
        }

        // Do not trigger the change event if the value has not changed when taking the scale into account.
        if (
            value !== undefined &&
            value !== null &&
            numericValue !== undefined &&
            numericValue !== null &&
            resolvedScale !== undefined &&
            resolvedScale !== null &&
            Number(value).toFixed(resolvedScale) === numericValue.toFixed(resolvedScale)
        ) {
            return true;
        }

        return false;
    }, [internalValue, resolvedScale, sep, value]);

    const setFormattedValue = React.useCallback(() => {
        setInternalValue(formatValue);
    }, [formatValue]);

    const onInputValueChanged = React.useMemo(
        () =>
            debounce(async (searchText: string) => {
                await triggerFieldEvent(screenId, elementId, 'onInputValueChange', searchText);
            }, 150),
        [screenId, elementId],
    );

    const onBlur = React.useCallback(() => {
        const numericValue = parseLocalizedNumberStringToNumber(internalValue, sep);
        if (isUnchangedValue()) return;

        handleChange(elementId, numericValue, setFieldValue, validate, changeEventHandler);
    }, [changeEventHandler, elementId, internalValue, isUnchangedValue, sep, setFieldValue, validate]);

    const onChange = React.useCallback(
        (ev: React.ChangeEvent<HTMLInputElement>) => {
            setInternalValue(ev.target.value);
            if (isUnchangedValue()) return;

            onInputValueChanged(ev.target.value);
        },
        [isUnchangedValue, onInputValueChanged],
    );

    const onKeyDown = React.useCallback(
        (ev: React.KeyboardEvent<HTMLInputElement>) => {
            const { key, altKey, metaKey, ctrlKey } = ev;
            if (key === 'Enter') {
                onBlur();
                return;
            }

            if (key === 'Escape') {
                setFormattedValue();
                return;
            }
            if (key === sep && resolvedScale === 0) {
                // If the user tries to use the separator character in an integer field.
                ev.preventDefault();
                ev.stopPropagation();
                return;
            }

            if (
                isKeyPressedNumeric(ev) &&
                isInputLimitedByScale({
                    event: ev,
                    scale: resolvedScale,
                    screenId,
                    fieldValue: value,
                    rowValue: handlersArguments?.rowValue,
                })
            ) {
                ev.stopPropagation();
                ev.preventDefault();
                return;
            }

            if (
                isKeyPressedNumeric(ev) ||
                [
                    'ArrowLeft',
                    'ArrowRight',
                    'Tab',
                    'Control',
                    'Meta',
                    'Shift',
                    'Delete',
                    'Backspace',
                    'End',
                    'Home',
                    'PageUp',
                    'PageDown',
                ].indexOf(key) !== -1 ||
                altKey ||
                metaKey ||
                ctrlKey
            ) {
                return;
            }

            ev.preventDefault();
            ev.stopPropagation();
        },
        [onBlur, sep, resolvedScale, setFormattedValue, value, handlersArguments?.rowValue, screenId],
    );

    React.useEffect(() => {
        // Update the internal value when the value changes
        setFormattedValue();
    }, [setFormattedValue]);

    return (
        <CarbonWrapper
            {...props}
            className={`e-numeric-field${resolvedScale ? ' e-decimal-field' : ''}`}
            componentName="numeric"
            handlersArguments={handlersArguments}
            noReadOnlySupport={true}
            value={internalValue}
            readOnlyDisplayValue={internalValue}
        >
            <Textbox
                {...getCommonCarbonComponentProperties(props)}
                data-testid="e-ui-decimal-input"
                inputIcon={fieldProperties.icon}
                onBlur={onBlur}
                onChange={onChange}
                onClick={clickEventHandler}
                onKeyDown={onKeyDown}
                ref={ref}
                value={internalValue}
            />
        </CarbonWrapper>
    );
}

export const ConnectedNumericComponent = connect(mapStateToProps(), mapDispatchToProps())(NumericComponent);

export default ConnectedNumericComponent;
