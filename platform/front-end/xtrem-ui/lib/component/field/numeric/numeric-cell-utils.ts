import { localize } from '../../../service/i18n-service';
import type { ScreenBase } from '../../../service/screen-base';
import {
    isFinishedEditingPressed,
    isKeyPressedNumeric,
    isLeftOrRight,
} from '../../../utils/ag-grid/ag-grid-cell-editor-utils';
import { getNumberComponentsFromLocalizedNumberString } from '../../../utils/formatters';
import { isDeleteOrBackspace, isEsc } from '../../../utils/keyboard-event-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import type { ValueOrCallbackWithFieldValue } from '../../../utils/types';

export function onNumericInputKeyDown({
    event,
    scale: scaleProperty,
    screenId,
    fieldValue,
    rowValue,
    unitScale,
}: {
    event: React.KeyboardEvent<HTMLInputElement>;
    screenId: string;
    fieldValue?: any;
    rowValue?: any;
    scale: ValueOrCallbackWithFieldValue<ScreenBase, number, string>;
    unitScale?: number;
}): void {
    const isCopyOrPaste = (event.ctrlKey || event.metaKey) && (event.key === 'c' || event.key === 'v');
    if (isEsc(event) || isCopyOrPaste) {
        return;
    }

    if (isLeftOrRight(event) || isDeleteOrBackspace(event)) {
        event.stopPropagation();
        return;
    }

    if (!isFinishedEditingPressed(event) && !isKeyPressedNumeric(event) && event.preventDefault) {
        event.preventDefault();
    }

    if (
        isKeyPressedNumeric(event) &&
        isInputLimitedByScale({
            event,
            screenId,
            scale: scaleProperty,
            fieldValue,
            rowValue,
            unitScale,
        })
    ) {
        event.preventDefault();
    }
}

export function isInputLimitedByScale({
    event,
    scale: scaleProperty,
    screenId,
    fieldValue,
    rowValue,
    unitScale,
}: {
    event: React.KeyboardEvent<HTMLInputElement>;
    screenId: string;
    fieldValue?: any;
    rowValue?: any;
    scale: ValueOrCallbackWithFieldValue<ScreenBase, number, string, any>;
    unitScale?: number;
}): boolean {
    const components = getNumberComponentsFromLocalizedNumberString(
        event.currentTarget.value,
        localize('@sage/xtrem-ui/number-format-separator', '.'),
    );
    const inputPosition = event.currentTarget?.selectionStart || 0;
    const scale =
        resolveByValue({
            fieldValue: fieldValue || null,
            propertyValue: scaleProperty,
            rowValue: rowValue || null,
            screenId,
            skipHexFormat: true,
        }) ?? unitScale;

    // Allow unlimited decimals if scale is undefined.
    if (typeof scale !== 'number') {
        return false;
    }

    // Ignore integer values if value does not contain decimal separator.
    if (!event.currentTarget.value.includes(localize('@sage/xtrem-ui/number-format-separator', '.'))) {
        return false;
    }

    // Limit decimals if scale is exceeded.
    if (scale <= components.decimal.length) {
        // The line below is added to allow the user to insert numbers before the decimal separator
        // and replace selections with numeric inputs.
        return inputPosition > event.currentTarget.value.length - components.decimal.length - 1;
    }

    return false;
}
