/**
 * @packageDocumentation
 * @module root
 * */
import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { NumericControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { NumericDecoratorProperties } from './numeric-types';
import { addDisabledToProperties, addMaxMinToProperties, addScaleToProperties } from '../../../utils/data-type-utils';

class NumericDecorator extends AbstractFieldDecorator<FieldKey.Numeric> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = NumericControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<NumericDecoratorProperties> {
        const properties: Partial<NumericDecoratorProperties> = {};
        addScaleToProperties({ dataType, propertyDetails, properties });
        addMaxMinToProperties({ dataType, propertyDetails, properties });
        addDisabledToProperties({
            propertyDetails,
            dataType,
            properties,
        });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [Numeric]{@link NumericControlObject} field with the provided properties
 *
 * @param properties The properties that the [Numeric]{@link NumericControlObject} field will be initialized with
 */
export function numericField<T extends ScreenExtension<T>>(
    properties: NumericDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Numeric>(properties, NumericDecorator, FieldKey.Numeric);
}

export function numericFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<NumericDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Numeric>(properties);
}
