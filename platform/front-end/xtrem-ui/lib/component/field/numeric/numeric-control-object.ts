/**
 * @packageDocumentation
 * @module root
 * */
import type { Unit } from '@sage/xtrem-shared';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { NumericDecoratorProperties } from './numeric-types';
import type { UnitMode } from '../traits';

/**
 * [Field]{@link EditableFieldControlObject} that contains a numeric value
 */
export class NumericControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.Numeric,
    FieldComponentProps<FieldKey.Numeric>
> {
    @ControlObjectProperty<NumericDecoratorProperties<CT>, NumericControlObject<CT>>()
    /** Icon of the input field. It will be placed on the right side. */
    icon?: IconType;

    @ControlObjectProperty<NumericDecoratorProperties<CT>, NumericControlObject<CT>>()
    /** Color of the icon, only supported in tile containers */
    iconColor?: string;

    @FieldControlObjectResolvedProperty<NumericDecoratorProperties<CT>, NumericControlObject<CT>>()
    /**
     * Number of digits after the numeric field value decimal point.
     * Must be in the range 0 - 20, inclusive.
     */
    scale?: number;

    @FieldControlObjectResolvedProperty<NumericDecoratorProperties<CT>, NumericControlObject<CT>>()
    /** Text to be displayed inline after the field value */
    prefix?: string;

    @FieldControlObjectResolvedProperty<NumericDecoratorProperties<CT>, NumericControlObject<CT>>()
    /** Text to be displayed inline before the field value */
    postfix?: string;

    @FieldControlObjectResolvedProperty<NumericDecoratorProperties<CT>, NumericControlObject<CT>>()
    /** The maximum value allowed for the numeric field */
    max?: number;

    @FieldControlObjectResolvedProperty<NumericDecoratorProperties<CT>, NumericControlObject<CT>>()
    /** The minimum value allowed for the numeric field */
    min?: number;

    @ControlObjectProperty<NumericDecoratorProperties<CT>, NumericControlObject<CT>>()
    /** The helper text underneath the field */
    placeholder?: string;

    /** Validation property, ensures that there is a value in the input that is not equal to zero. */
    @FieldControlObjectResolvedProperty<NumericDecoratorProperties<CT>, NumericControlObject<CT>>()
    isNotZero?: boolean;

    /** Unit of measure, if set the prefix/postfix and the scale is automatically set based on the user's locale */
    @FieldControlObjectResolvedProperty<NumericDecoratorProperties<CT>, NumericControlObject<CT>>()
    unit?: Unit | null;

    /** When the unit is set, this mode determines whether the unit should be handled as a unit of measure or a currency. Defaults to 'currency'. */
    @FieldControlObjectResolvedProperty<NumericDecoratorProperties<CT>, NumericControlObject<CT>>()
    unitMode?: UnitMode;

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }
}
