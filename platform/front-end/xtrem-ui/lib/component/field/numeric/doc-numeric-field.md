PATH: XTREEM/UI+Field+Widgets/Numeric+Field

## Introduction

Numeric fields can be used to represent integers and decimals.
It is also available as a nested field in the navigation panel, tables, charts and calendars.

## Example:

```ts
@ui.decorators.numericField<NumericField>({
    parent() {
        return this.numericBlock;
    },
    title: 'A numeric field',
    isMandatory: false,
    isReadOnly: false,
    isTransient: false,
    onChange() {
        console.log('Doing something when the value is changed');
    },
    onClick() {
        console.log('Doing something when the field is clicked');
    },
    prefix: '$',
    postfix: 'Kg',
    helperText: 'This text goes underneath the field',
    max: 1000,
    min: -1000,
    scale: 2,
    unit: { _id: 'KG', symbol: 'Kg' , decimalDigits: 2 },
    unitMode: 'unitOfMeasure',
    validation(v: number) {
        return v === 12 ? '12 is not allowed.' : '';
    },
})
aSimpleNumericField: ui.fields.Numeric;
```

### Display decorator properties

-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **helperText**: The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **prefix**: A string that is displayed inside the field before the value, aligned to the left. It can be defined as a string, or conditionally by a callback that returns a string.
-   **postfix**: A string that is displayed inside the field after the value, aligned to the right. It can be defined as a string, or conditionally by a callback that returns a string.
-   **placeholder**: Placeholder text which is displayed inside the field body when the field is empty. It is automatically picked up by the i18n engine and externalized.
-   **size**: Vertical size of the field, it can be `small`, `medium` or `large`. It is set to medium by default.
-   **scale**: Number of decimal digits to be displayed in the field. It can be defined as a number, or conditionally by a callback that returns a number.
-   **isFullWidth**: Whether a field should take the full width of the screen.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **isReadOnly**: Whether the field is editable (isReadOnly = false) or not (isReadOnly = true). The difference with disabled is that isReadOnly suggests that the field is never editable. It can be defined as a boolean, or conditionally by a callback that returns a boolean.
-   **icon**: Icon to be displayed on the right side of the input. The list of icons are defined by [DLS](https://brand.sage.com/d/NdbrveWvNheA/foundations#/icons/icons).
-   **iconColor**: Color of the input icon, only supported if the field is rendered within a tile container.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.
-   **unit**: Automatically sets the scale and the prefix or postfix of the displayed number based on the configured unit and the currency.
-   **unitMode**: Hint for the automatic unit formatting, it can be either `'currency'` or `'unitOfMeasure'`. Defaults to `'currency'`.

### Binding decorator properties

-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

### Event handler decorator properties

-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.
-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).
-   **onInputValueChange**: Triggered when the user stops typing into the input of the field. The execution is debounced by 50ms to cater for continuos typing.

### Validator decorator properties

-   **isMandatory**: Makes the field mandatory, empty values will raise an error message. It can also be defined as callback function that returns a boolean.
-   **min**: sets the lower end of the allowed value range. It can also be defined as callback function that returns a number.
-   **max**: sets the upper end of the allowed value range. It can also be defined as callback function that returns a number.
-   **isNotZero**:  validation property, ensures that there is a value in the input that is not equal to zero. It can also be defined as callback function that returns a boolean.
-   **validation**: Custom validation callback, the new value is provided in the argument. If the function returns a non-empty string (or promise resolving to a non-empty string), it will be used as a validation error message. If returns a falsy value, the field is considered to be valid.

### Other decorator properties

-   **fetchesDefaults**: When set to true and when the numeric value changes, a request to the server for default values for the whole page will be requested. False by default.
-   **groupAggregationMethod**: Set the grouping method for computing a value for the group totals row on the main list. The value is only displayed if the main list is grouped. Available methods for this field type: `min`, `max`, `avg`, `sum`, `distinctCount`.

## Runtime functions

-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **focus()**: Moves the focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).


## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Reference).
