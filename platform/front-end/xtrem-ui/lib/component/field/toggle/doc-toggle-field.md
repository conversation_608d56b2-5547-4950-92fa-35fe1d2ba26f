PATH: XTREEM/UI+Field+Widgets/Toggle+Field

## Introduction

The Toggle Buttons field is a highly visual way to allow the user to select a single item from a small number of options.

## Example:

With options coming as enum values from the server graphQL API.
```ts
@ui.decorators.toggleField<Toggle>({
        title: 'Toggle group',
        parent() {
            return this.fieldBlock;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    field: ui.fields.Toggle;
```

Defined in the code.
```ts
@ui.decorators.toggleField<Toggle>({
        title: 'Toggle group',
        parent() {
            return this.fieldBlock;
        },
        options: ['This is option 1', 'This is option 2', 'This is option 3']
    })
    field: ui.fields.Toggle;
```


### Display decorator properties

-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.
-   **helperText**: The helper text that is displayed above the field. It is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: The field is shown but the user cannot interact with it. It can also be defined as callback function that returns a boolean.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **options**: List of options to be displayed in the Toggle buttons. It can also be defined as callback function that returns a string array.
-   **map**: Custom callback to transform the field's value. The new value and grid's row data (if applicable) is provided as arguments. If implemented, the callbacks return value will be used as the field's value.
-   **mapIcon**: Custom callback to add a icon to the field's value button. The callbacks returns the icon string name.

### Binding decorator properties

-   **optionType**: GraphQL enum field used to get the list of options.
-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

#### Validation properties

-   **isMandatory**: Specifies whether the field is mandatory or not. When enabled, empty values will raise an validation error message. It can also be defined as callback function that returns a boolean.
-   **validation()**: Custom validation callback with the new value provided as argument. If the function returns a non-empty string (or promise resolving to a non-empty string), the return value will be used as validation error message. If the function returns a falsy value, the field is considered to be valid.

### Event handler decorator properties

-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).
-   **onClick**: Triggered when any parts of the field is clicked, no arguments provided.

### Other decorator properties

-   **fetchesDefaults**: When set to true and when the toggle value changes, a request to the server for default values for the whole page will be requested. False by default.

## Runtime functions:

-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **focus()**: Moves the focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Toggle).
