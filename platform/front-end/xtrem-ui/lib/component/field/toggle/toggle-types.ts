import type { ScreenBase } from '../../../service/screen-base';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type {
    CanFetchDefaults,
    Changeable,
    Clickable,
    ExtensionField,
    HasOptions,
    HasOptionType,
    HasParent,
    Mappable,
    MappableIcon,
    Sizable,
} from '../traits';

export interface ToggleProperties<CT extends ScreenBase = ScreenBase>
    extends EditableFieldProperties<CT>,
        Sizable,
        HasOptions<CT>,
        MappableIcon<CT>,
        Mappable<CT>,
        CanFetchDefaults {}

export interface ToggleDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<ToggleProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        Changeable<CT>,
        HasParent<CT, BlockControlObject<CT>>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasOptions<CT>,
        HasOptionType,
        Sizable {}

export type ToggleComponentProps = BaseEditableComponentProperties<ToggleProperties<any>, string>;
