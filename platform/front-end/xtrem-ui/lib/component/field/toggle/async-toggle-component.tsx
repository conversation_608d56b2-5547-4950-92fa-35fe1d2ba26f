import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { ToggleComponentProps } from './toggle-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedToggleComponent = React.lazy(() => import('./toggle-component'));

export function AsyncConnectedToggleComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="80px" />}>
            <ConnectedToggleComponent {...props} />
        </React.Suspense>
    );
}

const ToggleComponent = React.lazy(() => import('./toggle-component').then(c => ({ default: c.ToggleComponent })));

export function AsyncToggleComponent(props: ToggleComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <ToggleComponent {...props} />
        </React.Suspense>
    );
}
