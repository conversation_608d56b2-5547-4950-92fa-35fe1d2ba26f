import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import type { FieldComponentProps } from '../../types';
import { FieldKey } from '../../types';
import { ToggleControlObject } from './toggle-control-object';
import type { ToggleDecoratorProperties } from './toggle-types';

class ToggleDecorator extends AbstractFieldDecorator<FieldKey.Toggle> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = ToggleControlObject;

    static readonly defaultUiProperties: Partial<FieldComponentProps<FieldKey.Toggle>> = {
        ...ToggleControlObject.defaultUiProperties,
    };
}

/**
 * Initializes the decorated member as a [Separator]{@link SeparatorControlObject} field with the provided properties
 *
 * @param properties The properties that the [Separator]{@link SeparatorControlObject} field will be initialized with
 */
export function toggleField<T extends ScreenExtension<T>>(
    properties: ToggleDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Toggle>(properties, ToggleDecorator, FieldKey.Toggle);
}

export function toggleFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<ToggleDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Toggle>(properties);
}
