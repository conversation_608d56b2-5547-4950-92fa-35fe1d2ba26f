import type { Dict } from '@sage/xtrem-shared';
import ButtonToggle from 'carbon-react/esm/components/button-toggle/button-toggle.component';
import ButtonToggleGroup from 'carbon-react/esm/components/button-toggle/button-toggle-group';
import React, { useRef } from 'react';
import { connect } from 'react-redux';
import type { XtremAppState } from '../../../redux';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { getDataTestIdAttribute } from '../../../utils/dom';
import { triggerFieldEvent } from '../../../utils/events';
import { useFocus } from '../../../utils/hooks/effects/use-focus';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { addOptionsAndLocalizationToProps, splitValueToMergedValue } from '../../../utils/transformers';
import { isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { FieldComponentExternalProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { ToggleComponentProps } from './toggle-types';
import { HelperText } from '../carbon-utility-components';
import type { IconType } from 'carbon-react/esm/components/icon/icon-type';
import type { SizeOptions } from 'carbon-react/esm/components/button/button.component';

type ToggleAdditionalProps = ToggleComponentProps & {
    localizedOptions?: Dict<string>;
    enumOptions?: string[];
} & NestedFieldsAdditionalProperties;

export function ToggleComponent(props: ToggleAdditionalProps): React.ReactElement {
    const { title, options, isTitleHidden } = props.fieldProperties;
    const firstToggleButtonRef = useRef(null);
    useFocus(firstToggleButtonRef, props.isInFocus, 'input');
    const ToggleButtonGroupProps = {
        label: title && !isTitleHidden ? title : '',
        groupName: props.elementId,
        name: props.elementId,
        value: props.value,
        onChange: (_evt: React.MouseEvent<HTMLButtonElement>, value?: string): void =>
            handleChange(
                props.elementId,
                value,
                props.setFieldValue,
                props.validate,
                triggerChangeListener(props.screenId, props.elementId),
            ),
    };
    const optionsToUse =
        props.enumOptions ||
        resolveByValue({
            propertyValue: options,
            skipHexFormat: true,
            screenId: props.screenId,
            fieldValue: props.value,
            rowValue: props.handlersArguments?.rowValue
                ? splitValueToMergedValue(props.handlersArguments?.rowValue)
                : props.handlersArguments?.rowValue,
        }) ||
        [];
    const isReadOnly =
        isFieldReadOnly(props.screenId, props.fieldProperties, props.value, props.handlersArguments?.rowValue) ||
        isFieldDisabled(props.screenId, props.fieldProperties, props.value, props.handlersArguments?.rowValue);

    return (
        <CarbonWrapper
            noReadOnlySupport={true}
            {...props}
            className="e-toggle-field"
            componentName="Toggle"
            value={props.value}
        >
            <ButtonToggleGroup
                id="e-toggle-button-group"
                data-testid={getDataTestIdAttribute('Toggle-component', 'Toggle-button-group', props.elementId)}
                {...ToggleButtonGroupProps}
                label={ToggleButtonGroupProps.label as string}
            >
                {optionsToUse.map(option =>
                    renderToggleButton(
                        option,
                        props.elementId,
                        props.onFocus,
                        props.fieldProperties.size || 'small',
                        resolveByValue({
                            propertyValue: props.fieldProperties.mapIcon,
                            rowValue: props.handlersArguments?.rowValue
                                ? splitValueToMergedValue(props.handlersArguments?.rowValue)
                                : props.handlersArguments?.rowValue,
                            fieldValue: option,
                            screenId: props.screenId,
                            skipHexFormat: true,
                        }),
                        props.localizedOptions,
                        resolveByValue({
                            propertyValue: props.fieldProperties.map,
                            rowValue: props.handlersArguments?.rowValue
                                ? splitValueToMergedValue(props.handlersArguments?.rowValue)
                                : props.handlersArguments?.rowValue,
                            fieldValue: option,
                            screenId: props.screenId,
                            skipHexFormat: true,
                        }),
                        isReadOnly,
                    ),
                )}
            </ButtonToggleGroup>
            <HelperText helperText={props.fieldProperties.helperText} />
        </CarbonWrapper>
    );
}

const triggerChangeListener = (screenId: string, elementId: string) => (): void => {
    triggerFieldEvent(screenId, elementId, 'onChange');
};

const renderToggleButton = (
    value: string,
    elementId: string,
    onFocus: () => void,
    size: SizeOptions,
    buttonIcon?: IconType,
    localizedOptions?: Dict<string>,
    mappedText?: string,
    isDisabled = false,
): React.ReactNode => {
    const label = localizedOptions && localizedOptions[value] ? localizedOptions[value] : mappedText || value;
    const carbonToggleProps = {
        value,
        label,
        size,
        key: value,
        disabled: isDisabled,
    };
    return (
        <ButtonToggle
            onFocus={onFocus}
            data-testid={getDataTestIdAttribute('toggle-component', 'Toggle-button', elementId)}
            buttonIcon={buttonIcon}
            {...carbonToggleProps}
        >
            {label}
        </ButtonToggle>
    );
};

export const ConnectedToggleComponent = connect(
    (state: XtremAppState, externalProps: FieldComponentExternalProperties) =>
        addOptionsAndLocalizationToProps(state, mapStateToProps()(state, externalProps) as ToggleComponentProps),
    mapDispatchToProps(),
)(ToggleComponent);

export default ConnectedToggleComponent;
