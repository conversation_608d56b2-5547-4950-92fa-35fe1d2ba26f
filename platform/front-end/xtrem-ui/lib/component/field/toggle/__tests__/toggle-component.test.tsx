import 'jest-styled-components';
import {
    addFieldToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';

import { ToggleComponent, ConnectedToggleComponent } from '../toggle-component';
import { render } from '@testing-library/react';
import type { ToggleComponentProps } from '../toggle-types';
import React from 'react';
import { FieldKey } from '../../../types';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import * as i18nService from '../../../../service/i18n-service';
import { Provider } from 'react-redux';

import * as stateUtils from '../../../../utils/state-utils';

jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() => getMockPageDefinition('screen-id'));

describe('Toggle component', () => {
    let screenId = 'TestPage';
    let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;
    let toggleComponentProps: ToggleComponentProps;

    beforeEach(() => {
        screenId = 'screen-id';
        toggleComponentProps = {
            elementId: 'test-toggle',
            screenId,
            fieldProperties: {
                title: 'title toggle',
                options: ['option 1', 'option 2', 'option 3'],
            },
            locale: 'en-US',
            onFocus: jest.fn(),
            setFieldValue: jest.fn().mockResolvedValue(undefined),
            validate: jest.fn(),
            removeNonNestedErrors: jest.fn(),
        };
        const state = getMockState();
        mockStore = getMockStore(state);
    });

    it('should render 3 toggle buttons with fieldProperties.options values', async () => {
        const wrapper = render(<ToggleComponent {...toggleComponentProps} />);
        expect(wrapper.getByTestId('e-Toggle-field e-field-label-titleToggle e-field-bind-test-toggle')).toBeTruthy();
        const toggleButtons = wrapper
            .getByTestId('e-Toggle-field e-field-label-titleToggle e-field-bind-test-toggle')
            .getElementsByTagName('button');

        expect(toggleButtons.length).toBe(3);
        expect(toggleButtons[0].value).toBe('option 1');
        expect(toggleButtons[1].value).toBe('option 2');
        expect(toggleButtons[2].value).toBe('option 3');
    });

    it('should render 3 toggle buttons with fieldProperties.options callback values', async () => {
        toggleComponentProps.fieldProperties.options = () => ['Test 1', 'Test 2'];
        const wrapper = render(<ToggleComponent {...toggleComponentProps} />);
        expect(wrapper.getByTestId('e-Toggle-field e-field-label-titleToggle e-field-bind-test-toggle')).toBeTruthy();
        const toggleButtons = wrapper
            .getByTestId('e-Toggle-field e-field-label-titleToggle e-field-bind-test-toggle')
            .getElementsByTagName('button');

        expect(toggleButtons.length).toBe(2);
        expect(toggleButtons[0].value).toBe('Test 1');
        expect(toggleButtons[1].value).toBe('Test 2');
    });

    it('set field value should be fired upon clicking a toggle button', () => {
        const mockSetFieldValue = jest.fn().mockResolvedValue(undefined);
        toggleComponentProps.setFieldValue = mockSetFieldValue;
        const wrapper = render(<ToggleComponent {...toggleComponentProps} />);
        const toggleButtons = wrapper
            .getByTestId('e-Toggle-field e-field-label-titleToggle e-field-bind-test-toggle')
            .getElementsByTagName('button');
        toggleButtons[0].click();
        expect(mockSetFieldValue).toHaveBeenCalledWith('test-toggle', 'option 1');
        toggleButtons[1].click();
        expect(mockSetFieldValue).toHaveBeenCalledWith('test-toggle', 'option 2');
        toggleButtons[2].click();
        expect(mockSetFieldValue).toHaveBeenCalledWith('test-toggle', 'option 3');
    });

    it('Should render with title toggle', () => {
        const wrapper = render(<ToggleComponent {...toggleComponentProps} />);
        expect(wrapper.getByText('title toggle')).toBeTruthy();
    });

    it('Should render with a hidden title', () => {
        toggleComponentProps.fieldProperties.isTitleHidden = true;
        const wrapper = render(<ToggleComponent {...toggleComponentProps} />);
        expect(() => wrapper.getAllByText('title toggle')).toThrow();
    });

    it('Snapshot', () => {
        const wrapper = render(<ToggleComponent {...toggleComponentProps} />);
        expect(wrapper.baseElement).toMatchSnapshot();
    });

    describe('connected', () => {
        let localizeSpy: jest.SpyInstance<string, [string, string]> | null = null;

        const fieldIdWithOptionType = 'myTestOptionTypeToggleButton';
        const fieldIdWithOptions = 'myTestToggleButton';
        const fieldIdWithOptionsMapped = 'myTestToggleButtonMapped';

        beforeEach(() => {
            const setFieldValueMockImpl = jest.fn(() => () => Promise.resolve());
            jest.spyOn(xtremRedux.actions, 'setFieldValue').mockImplementation(setFieldValueMockImpl);

            localizeSpy = jest
                .spyOn(i18nService, 'localizeEnumMember')
                .mockImplementation((_: any, enumName: string) => `${enumName} localized`);
            const state = getMockState();
            state.enumTypes.MyLocalizedEnum = ['option1', 'option2', 'option3'];
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);

            addFieldToState(
                FieldKey.Toggle,
                state,
                screenId,
                fieldIdWithOptionType,
                {
                    title: 'Toggle button with options from an enum',
                    optionType: '@sage/xtrem-test/MyLocalizedEnum',
                },
                'item3',
            );

            addFieldToState(
                FieldKey.Toggle,
                state,
                screenId,
                fieldIdWithOptions,
                {
                    title: 'Toggle button with hardcoded options',
                    options: ['item1', 'item2', 'item3'],
                },
                'option3',
            );

            addFieldToState(FieldKey.Toggle, state, screenId, fieldIdWithOptionsMapped, {
                title: 'Toggle button with hardcoded and mapped value',
                options: ['item1', 'item2', 'item3'],
                map(value: string) {
                    if (value === 'item1') {
                        return 'mapped item 1';
                    }
                    return value;
                },
            });

            mockStore = getMockStore(state);
        });

        afterEach(() => {
            jest.resetAllMocks();
            applyActionMocks();
        });

        it('should render toggle button with hardcoded options', () => {
            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedToggleComponent screenId={screenId} elementId={fieldIdWithOptions} />
                </Provider>,
            );

            expect(
                wrapper.getByTestId(
                    'e-Toggle-field e-field-label-toggleButtonWithHardcodedOptions e-field-bind-myTestToggleButton',
                ),
            ).toBeTruthy();
            const toggleButtons = wrapper
                .getByTestId(
                    'e-Toggle-field e-field-label-toggleButtonWithHardcodedOptions e-field-bind-myTestToggleButton',
                )
                .getElementsByTagName('button');

            expect(toggleButtons.length).toBe(3);
            expect(toggleButtons[0].value).toBe('item1');
            expect(toggleButtons[1].value).toBe('item2');
            expect(toggleButtons[2].value).toBe('item3');
        });

        it('should render toggle button with option type and localize displayed labels', () => {
            expect(localizeSpy).not.toHaveBeenCalled();

            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedToggleComponent screenId={screenId} elementId={fieldIdWithOptionType} />
                </Provider>,
            );

            expect(localizeSpy).toHaveBeenCalledTimes(3);

            expect(
                wrapper.getByTestId(
                    'e-Toggle-field e-field-label-toggleButtonWithOptionsFromAnEnum e-field-bind-myTestOptionTypeToggleButton',
                ),
            ).toBeTruthy();
            const toggleButtons = wrapper
                .getByTestId(
                    'e-Toggle-field e-field-label-toggleButtonWithOptionsFromAnEnum e-field-bind-myTestOptionTypeToggleButton',
                )
                .getElementsByTagName('button');

            expect(toggleButtons[0].value).toBe('option1');
            expect(toggleButtons[1].value).toBe('option2');
            expect(toggleButtons[2].value).toBe('option3');
        });
        it('should render toggle button with mapped values', () => {
            const wrapper = render(
                <Provider store={mockStore}>
                    <ConnectedToggleComponent screenId={screenId} elementId={fieldIdWithOptionsMapped} />
                </Provider>,
            );

            expect(
                wrapper.getByTestId(
                    'e-Toggle-field e-field-label-toggleButtonWithHardcodedAndMappedValue e-field-bind-myTestToggleButtonMapped',
                ),
            ).toBeTruthy();
            const toggleButtons = wrapper
                .getByTestId(
                    'e-Toggle-field e-field-label-toggleButtonWithHardcodedAndMappedValue e-field-bind-myTestToggleButtonMapped',
                )
                .getElementsByTagName('button');

            expect(toggleButtons.length).toBe(3);
            expect(toggleButtons[0].value).toBe('item1');
            expect(toggleButtons[0].textContent).toBe('mapped item 1');
            expect(toggleButtons[1].value).toBe('item2');
            expect(toggleButtons[2].value).toBe('item3');
        });
    });
});
