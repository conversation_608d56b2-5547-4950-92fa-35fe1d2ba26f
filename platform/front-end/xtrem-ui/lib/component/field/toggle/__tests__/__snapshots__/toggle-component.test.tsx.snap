// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Toggle component Snapshot 1`] = `
.c1 {
  margin-bottom: var(--fieldSpacing);
}

.c0 + .c0 {
  margin-top: 16px;
}

.c2 {
  display: block;
}

.c4 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.c8 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  max-width: 100%;
  font-weight: 500;
  background-color: transparent;
  cursor: pointer;
  text-align: center;
  color: var(--colorsActionMinor500);
  border: none;
  min-height: 24px;
  padding: 0 8px;
  font-size: 14px;
}

.c8:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
  z-index: 100;
}

.c8:not(:disabled):hover {
  background-color: var(--colorsActionMinor600);
  color: var(--colorsActionMinorYang100);
}

.c8[aria-pressed="true"] {
  background-color: var(--colorsActionMinor850);
  color: var(--colorsActionMinorYang100);
  cursor: auto;
}

.c6 {
  display: inline-block;
  vertical-align: middle;
}

.c6.c6.c6.c6 .c7 {
  border-radius: var(--borderRadius050);
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  box-shadow: inset 0px 0px 0px 1px var(--colorsActionMinor500);
  border-radius: var(--borderRadius100);
  padding: 4px;
  gap: 4px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<body>
  <div>
    <div
      class="e-field e-toggle-field"
      data-label="title toggle"
      data-testid="e-Toggle-field e-field-label-titleToggle e-field-bind-test-toggle"
    >
      <div
        class="c0 c1"
        data-component="button-toggle-group"
      >
        <div
          class="c2"
          data-role="field-line"
        >
          <div
            class="c3"
            data-role="label-container"
            id="label-container-testcarb-onco-mpon-ents-uniqguidmock"
            width="30"
          >
            <span
              class="c4"
              data-element="label"
              id="testcarb-onco-mpon-ents-uniqguidmock"
            >
              title toggle
            </span>
          </div>
          <div
            aria-labelledby="testcarb-onco-mpon-ents-uniqguidmock"
            class="c5"
            id="e-toggle-button-group"
            role="group"
          >
            <div
              class="c6"
              data-component="button-toggle"
            >
              <button
                aria-pressed="false"
                class="c7 c8"
                data-element="button-toggle-button"
                id="testcarb-onco-mpon-ents-uniqguidmock"
                tabindex="-1"
                value="option 1"
              >
                option 1
              </button>
            </div>
            <div
              class="c6"
              data-component="button-toggle"
            >
              <button
                aria-pressed="false"
                class="c7 c8"
                data-element="button-toggle-button"
                id="testcarb-onco-mpon-ents-uniqguidmock"
                tabindex="-1"
                value="option 2"
              >
                option 2
              </button>
            </div>
            <div
              class="c6"
              data-component="button-toggle"
            >
              <button
                aria-pressed="false"
                class="c7 c8"
                data-element="button-toggle-button"
                id="testcarb-onco-mpon-ents-uniqguidmock"
                tabindex="-1"
                value="option 3"
              >
                option 3
              </button>
            </div>
          </div>
        </div>
      </div>
      <span
        class="common-input__help-text"
        data-element="help"
        data-testid="e-field-helper-text"
      >
         
      </span>
    </div>
  </div>
</body>
`;
