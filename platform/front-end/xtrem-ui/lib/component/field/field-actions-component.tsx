import { camelCase } from 'lodash';
import React from 'react';
import { connect } from 'react-redux';
import type * as xtremRedux from '../../redux';
import { getScreenElement } from '../../service/screen-base-definition';
import { executeCallbackInScreenContext, triggerFieldEvent } from '../../utils/events';
import type { PageActionControlObject } from '../control-objects';
import type { HasFieldActions } from './traits';
import { getFieldTitle } from './carbon-helpers';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import type { ScreenExtension } from '../../types';
import type { ScreenBase } from '../../service/screen-base';
import type { IconType } from 'carbon-react/esm/components/icon';

export interface AdditionalFieldAction<CT extends ScreenExtension<CT> = ScreenBase> {
    title: string;
    onClick: (this: CT) => void;
    icon: IconType;
    id: string;
}

interface FieldActionExternalProps {
    screenId: string;
    fieldId: string;
    isDisabled?: boolean;
    additionalFieldActions?: AdditionalFieldAction[];
}

interface FieldActionProps extends FieldActionExternalProps {
    fieldActionsMap?: PageActionControlObject[];
}

function FieldActions(props: FieldActionProps): React.ReactElement | null {
    const { screenId, fieldActionsMap, isDisabled, additionalFieldActions } = props;

    if (fieldActionsMap?.length || additionalFieldActions?.length) {
        return (
            <>
                {additionalFieldActions?.map(fieldAction => {
                    const onClick = (): void => executeCallbackInScreenContext(fieldAction.onClick, screenId);
                    return (
                        <ButtonMinor
                            key={fieldAction.id}
                            buttonType="tertiary"
                            iconType={fieldAction.icon}
                            iconTooltipMessage={fieldAction.title}
                            aria-label={fieldAction.title}
                            disabled={isDisabled}
                            data-testid={`e-header-action-${fieldAction.id} e-header-action-bind-${camelCase(
                                fieldAction.id,
                            )} e-header-action-label-${camelCase(fieldAction.title)}`}
                            onClick={onClick}
                        />
                    );
                })}
                {fieldActionsMap
                    ?.filter(fieldAction => fieldAction.icon && !fieldAction.isHidden)
                    .map(fieldAction => {
                        const onClick = (): void => {
                            triggerFieldEvent(screenId, fieldAction.id, 'onClick');
                        };
                        const title = getFieldTitle(screenId, fieldAction, null);
                        return (
                            <ButtonMinor
                                key={fieldAction.id}
                                buttonType="tertiary"
                                iconType={fieldAction.icon}
                                iconTooltipMessage={title}
                                aria-label={title}
                                disabled={isDisabled || fieldAction.isDisabled || fieldAction.isHidden}
                                data-testid={`e-header-action-${fieldAction.id} e-header-action-bind-${camelCase(
                                    fieldAction.id,
                                )} e-header-action-label-${camelCase(title)}`}
                                onClick={onClick}
                            />
                        );
                    })}
            </>
        );
    }
    return null;
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: FieldActionExternalProps): FieldActionProps => {
    const screenDefinition = state.screenDefinitions[props.screenId];
    const fieldProperties = screenDefinition.metadata.uiComponentProperties[props.fieldId] as HasFieldActions<any>;
    const screenElement = getScreenElement(screenDefinition);
    const fieldActionsMap =
        typeof fieldProperties?.fieldActions === 'function' ? fieldProperties?.fieldActions.apply(screenElement) : [];
    return { ...props, fieldActionsMap };
};

export default connect(mapStateToProps)(FieldActions);
