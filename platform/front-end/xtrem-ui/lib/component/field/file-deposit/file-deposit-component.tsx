import * as React from 'react';
import { connect } from 'react-redux';
import { localize } from '../../../service/i18n-service';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { getComponentClass, getDataTestIdAttribute } from '../../../utils/dom';
import { triggerFieldEvent } from '../../../utils/events';
import { useFocus } from '../../../utils/hooks/effects/use-focus';
import { getLabelTitle, isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import { mapDispatchToProps, mapStateToProps } from '../field-base-utils';
import type { FileDepositComponentProps, FileDepositProperties } from './file-deposit-types';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { getScreenElement } from '../../../service/screen-base-definition';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import type { XtremAppState } from '../../../redux/state';
import type { EditableFieldComponentProperties, FieldComponentExternalProperties } from '../field-base-component-types';
import { errorDialog } from '../../../service/dialog-service';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import type { FileInputProps } from 'carbon-react/esm/components/file-input';
import FileInput from 'carbon-react/esm/components/file-input';
import Link from 'carbon-react/esm/components/link';
import type { FileDepositValue } from '../../types';
import {
    depositFile,
    getMimeTypeFromExtension,
    isDisallowedMimeType,
    showNotAllowedTypeToast,
} from '../../../utils/file-deposit-utils';
import { noop } from 'lodash';
import type { CarbonLinkEvent } from '../../../utils/types';

export function FileDepositComponent(props: FileDepositComponentProps): React.ReactElement {
    const hasValue = props.value?.downloadUrl !== undefined && props.value?.downloadUrl !== null;
    const fileComponentRef = React.useRef<HTMLDivElement>(null);
    const [uploadProgress, setUploadProgress] = React.useState<number>(0);
    const [isUploadInProgress, setUploadInProgress] = React.useState(false);
    useFocus(fileComponentRef, props.isInFocus, 'button');

    const isReadOnly = isFieldReadOnly(props.screenId, props.fieldProperties, props.value, null); // Not available as a nested fields
    const isDisabled = isFieldDisabled(props.screenId, props.fieldProperties, props.value, null); // Not available as a nested fields

    const changeEventHandler = React.useCallback(
        (): Promise<void> => triggerFieldEvent(props.screenId, props.elementId, 'onChange'),
        [props.screenId, props.elementId],
    );

    const clickEventHandler = React.useCallback(
        (): Promise<void> => triggerFieldEvent(props.screenId, props.elementId, 'onClick'),
        [props.screenId, props.elementId],
    );

    const onInputTypeFileChanged = async (fileList: FileList): Promise<void> => {
        if (fileList.length > 0) {
            const file = fileList[0];
            const targetNode = props.graphApi.node(props.fieldProperties.node);
            const mimeType = file.type || getMimeTypeFromExtension(file.name);

            if (
                props.fieldProperties.fileTypes &&
                isDisallowedMimeType(props.fieldProperties.fileTypes, mimeType, file.name)
            ) {
                props.onTelemetryEvent?.(`fileDepositFileMimeTypeValidationFailed-${props.elementId}`, {
                    elementId: props.elementId,
                    screenId: props.screenId,
                    mimeType,
                });
                showNotAllowedTypeToast(mimeType);
                return;
            }

            props.onTelemetryEvent?.(`fileDepositFileAdded-${props.elementId}`, {
                mimeType,
                elementId: props.elementId,
                screenId: props.screenId,
            });

            try {
                const result = await depositFile({
                    file,
                    targetNodeMutations: targetNode.mutations,
                    onUploadProgress: ev => {
                        setUploadProgress(ev.progress);
                        setUploadInProgress(ev.isInProgress);

                        if (ev.progress === 0 || ev.progress === 100) {
                            handleChange(
                                props.elementId,
                                ev.fieldValue,
                                props.setFieldValue,
                                props.validate,
                                changeEventHandler,
                            );
                        }
                    },
                    kind: props.fieldProperties.kind,
                });
                handleChange(props.elementId, result, props.setFieldValue, props.validate, changeEventHandler);
            } catch (err) {
                handleChange(props.elementId, null, props.setFieldValue, props.validate, changeEventHandler);
                errorDialog(
                    props.screenId,
                    localize('@sage/xtrem-ui/file-upload-failed', 'Failed to upload file.'),
                    err,
                );
            }
        }
    };

    const onDeleteFile = React.useCallback(
        (): void => handleChange(props.elementId, null, props.setFieldValue, props.validate, changeEventHandler),
        [changeEventHandler, props.elementId, props.setFieldValue, props.validate],
    );

    const title = getLabelTitle(props.screenId, props.fieldProperties, null); // Not available as a nested fields
    const { isTitleHidden } = props.fieldProperties;

    const infoMessage = resolveByValue({
        screenId: props.screenId,
        fieldValue: props.value,
        propertyValue: props.fieldProperties.infoMessage,
        rowValue: null,
        skipHexFormat: true,
    });

    const warningMessage = resolveByValue({
        screenId: props.screenId,
        fieldValue: props.value,
        propertyValue: props.fieldProperties.warningMessage,
        rowValue: null,
        skipHexFormat: true,
    });

    const onDownloadFile = React.useCallback(
        (ev: CarbonLinkEvent) => {
            ev.preventDefault();
            if (!props.value || isDisabled) {
                return;
            }
            const link = document.createElement('a');
            link.href = props.value?.downloadUrl;
            link.download = props.value?.filename;
            link.dispatchEvent(new MouseEvent('click'));
        },
        [isDisabled, props.value],
    );

    const getUploadStatus = React.useCallback((): FileInputProps['uploadStatus'] | undefined => {
        if (hasValue) {
            const href = props.value?.downloadUrl && !isReadOnly && !isDisabled ? props.value.downloadUrl : '#';
            const download = props.value?.filename;
            return {
                status: 'completed',
                href,
                filename: props.value?.filename ?? '',
                iconType: 'file_generic',
                onAction: isReadOnly ? undefined : onDeleteFile,
                disabled: isDisabled,
                target: '_blank',
                download,
                onClick: onDownloadFile,
                rel: 'noreferrer',
            } as FileInputProps['uploadStatus'] & { download?: string };
        }

        if (isUploadInProgress) {
            return {
                status: 'uploading',
                filename: props.value?.filename ?? '',
                onAction: noop,
                progress: uploadProgress,
                iconType: 'file_generic',
            };
        }

        return undefined;
    }, [
        hasValue,
        isDisabled,
        isReadOnly,
        isUploadInProgress,
        onDeleteFile,
        onDownloadFile,
        props.value?.downloadUrl,
        props.value?.filename,
        uploadProgress,
    ]);

    const helperText = isUploadInProgress
        ? localize('@sage/xtrem-ui/upload-in-progress', 'Uploading file...')
        : props.fieldProperties.helperText;

    const fileInputProps = {
        disabled: isDisabled,
        readOnly: isReadOnly,
        uploadStatus: getUploadStatus(),
        buttonText: localize('@sage/xtrem-ui/file-component-browse-files', 'Browse Files'),
        onChange: onInputTypeFileChanged,
        maxWidth: '100%',
        maxHeight: '200px',
        accept: props.fieldProperties.fileTypes,
        'data-testid': 'e-file-deposit-field-upload-area',
    };

    const renderBody = (): React.ReactNode => {
        if (!isReadOnly && !isDisabled) {
            return <FileInput {...fileInputProps} />;
        }

        if (hasValue) {
            return (
                // eslint-disable-next-line jsx-a11y/anchor-is-valid
                <Link
                    disabled={isDisabled}
                    href={isDisabled ? undefined : props.value?.downloadUrl}
                    onClick={onDownloadFile}
                    target="_blank"
                    rel="noreferrer"
                >
                    {props.value?.filename ?? ''}
                </Link>
            );
        }

        return <div>{localize('@sage/xtrem-ui/no-file-available', 'No file available')}</div>;
    };

    return (
        <div
            data-testid={getDataTestIdAttribute('file', title, props.elementId)}
            className={getComponentClass(props, 'e-file-deposit-field')}
            ref={fileComponentRef}
        >
            {!isTitleHidden && (
                <FieldLabel
                    label={title}
                    errorMessage={props.validationErrors?.[0].message}
                    infoMessage={infoMessage}
                    warningMessage={warningMessage}
                />
            )}
            <div
                className="e-file-deposit-field-content-wrapper"
                data-testid="e-file-deposit-field-content-wrapper"
                onClick={clickEventHandler}
            >
                {renderBody()}
            </div>
            <HelperText helperText={helperText} />
        </div>
    );
}

export const ConnectedFileDepositComponent = connect(
    (state: XtremAppState, props: FieldComponentExternalProperties): FileDepositComponentProps => {
        const screenDefinition = getPageDefinitionFromState(props.screenId, state);
        const screenElement = getScreenElement(screenDefinition);
        const basicProps = mapStateToProps()(state, props) as EditableFieldComponentProperties<
            FileDepositProperties,
            FileDepositValue
        >;

        return {
            ...props,
            ...basicProps,
            graphApi: screenElement.$.graph,
            onTelemetryEvent: state.applicationContext?.onTelemetryEvent,
        };
    },
    mapDispatchToProps(),
)(FileDepositComponent);

export default ConnectedFileDepositComponent;
