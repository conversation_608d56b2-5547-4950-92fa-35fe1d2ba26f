import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { FileDepositComponentProps } from './file-deposit-types';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';

const ConnectedFileDepositComponent = React.lazy(() => import('./file-deposit-component'));

export function AsyncConnectedFileDepositComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedFileDepositComponent {...props} />
        </React.Suspense>
    );
}

const FileDepositComponent = React.lazy(() =>
    import('./file-deposit-component').then(c => ({ default: c.FileDepositComponent })),
);

export function AsyncFileDepositComponent(props: FileDepositComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={!props.nestedReadOnlyField} />}>
            <FileDepositComponent {...props} />
        </React.Suspense>
    );
}
