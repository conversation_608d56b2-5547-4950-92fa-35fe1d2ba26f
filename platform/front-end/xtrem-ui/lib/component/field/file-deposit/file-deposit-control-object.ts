/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { FileDepositProperties } from './file-deposit-types';
/**
 * [Field]{@link EditableFieldControlObject} that holds a file stream value
 */
export class FileDepositControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends EditableFieldControlObject<
    CT,
    FieldKey.FileDeposit,
    FieldComponentProps<FieldKey.FileDeposit>
> {
    static readonly defaultUiProperties: Partial<FieldComponentProps<FieldKey.FileDeposit>> = {
        ...EditableFieldControlObject.defaultUiProperties,
    };

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    @ControlObjectProperty<FileDepositProperties<CT>, FileDepositControlObject<CT>>()
    /** File types that can be uploaded. Can be either audio/*, video/*, image/*, an extension name starting with '.'
     *  or a valid media type. Look at [IANA Media Types](https://www.iana.org/assignments/media-types/media-types.xhtml). for a complete list of standard media types.
     * It is possible to set more than one file type, simply by defining them separated by a comma.
     */
    fileTypes?: string;
}
