/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { FileDepositControlObject } from './file-deposit-control-object';
import type { FileDepositDecoratorProperties } from './file-deposit-types';

class FileDecorator extends AbstractFieldDecorator<FieldKey.FileDeposit> {
    protected _controlObjectConstructor = FileDepositControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

/**
 * Initializes the decorated member as a [File]{@link FileDepositControlObject} field with the provided properties
 *
 * @param properties The properties that the [File]{@link FileDepositControlObject} field will be initialized with
 */
export function fileDepositField<T extends ScreenExtension<T>>(
    properties: FileDepositDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.FileDeposit>(properties, FileDecorator, FieldKey.FileDeposit);
}

export function fileDepositFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<FileDepositDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.FileDeposit>(properties);
}
