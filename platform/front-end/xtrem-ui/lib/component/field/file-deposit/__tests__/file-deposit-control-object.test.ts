import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import { FileDepositControlObject } from '../../../control-objects';
import type { <PERSON>Key, FileDepositValue } from '../../../types';
import type { FileDepositProperties } from '../file-deposit-types';

describe('File Deposit Field', () => {
    let fileField: FileDepositControlObject;
    let fieldProperties: FileDepositProperties;

    const fileValue: FileDepositValue = {
        _id: '123',
        filename: 'some special data.csv',
        mimeType: 'text/csv',
        contentLength: 123456,
        downloadUrl: 'https://download.sage.com/my/special/file/123456',
        lastModified: '2022-09-20T13:58:46.485Z',
        status: 'uploaded',
        uploadUrl: '',
        kind: 'attachment',
        _createUser: {
            _id: '32',
            displayName: 'test user',
            email: '<EMAIL>',
        },
    };

    beforeEach(() => {
        fieldProperties = {
            title: 'TEST_FIELD_TITLE',
            node: '@sage/xtrem-test/TestNode',
            kind: 'upload',
        };
    });

    describe('getters and setters', () => {
        beforeEach(() => {
            fileField = buildControlObject<FieldKey.FileDeposit>(FileDepositControlObject, {
                fieldValue: fileValue,
                fieldProperties,
            });
        });

        it('getting field value', () => {
            expect(fileField.value).toEqual(fileValue);
        });

        it('getting isReadOnly', () => {
            expect(fileField.isReadOnly).toBeFalsy();
        });

        it('should set the title', () => {
            const testFixture = 'Test File Field Title';
            expect(fieldProperties.title).not.toEqual(testFixture);
            fileField.title = testFixture;
            expect(fieldProperties.title).toEqual(testFixture);
        });

        it('should set the helper text', () => {
            const testFixture = 'Test File Field Helper Text';
            expect(fieldProperties.helperText).not.toEqual(testFixture);
            fileField.helperText = testFixture;
            expect(fieldProperties.helperText).toEqual(testFixture);
        });

        it('should set isDisabled property', () => {
            const testFixture = true;
            expect(fieldProperties.isDisabled).not.toEqual(testFixture);
            fileField.isDisabled = testFixture;
            expect(fieldProperties.isDisabled).toEqual(testFixture);
        });

        it('should set isHelperTextHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHelperTextHidden).not.toEqual(testFixture);
            fileField.isHelperTextHidden = testFixture;
            expect(fieldProperties.isHelperTextHidden).toEqual(testFixture);
        });

        it('should set isHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHidden).not.toEqual(testFixture);
            fileField.isHidden = testFixture;
            expect(fieldProperties.isHidden).toEqual(testFixture);
        });

        it('should set isReadOnly property', () => {
            const testFixture = true;
            expect(fieldProperties.isReadOnly).not.toEqual(testFixture);
            fileField.isReadOnly = testFixture;
            expect(fieldProperties.isReadOnly).toEqual(testFixture);
        });

        it('should set isTitleHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isTitleHidden).not.toEqual(testFixture);
            fileField.isTitleHidden = testFixture;
            expect(fieldProperties.isTitleHidden).toEqual(testFixture);
        });

        it('should set file types', () => {
            const testFixture = 'image/*';
            expect(fieldProperties.fileTypes).not.toEqual(testFixture);
            fileField.fileTypes = testFixture;
            expect(fieldProperties.fileTypes).toEqual(testFixture);
        });
    });

    describe('focus', () => {
        const focus = jest.fn();
        beforeEach(() => {
            fileField = buildControlObject<FieldKey.FileDeposit>(FileDepositControlObject, {
                fieldValue: fileValue,
                fieldProperties,
                focus,
            });
        });

        it('should execute focus', () => {
            jest.useFakeTimers();
            expect(focus).not.toHaveBeenCalled();
            fileField.focus();
            jest.runAllTimers();
            expect(focus).toHaveBeenCalled();
        });
    });
});
