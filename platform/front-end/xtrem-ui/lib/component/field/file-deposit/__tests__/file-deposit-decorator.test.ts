import type { Page } from '../../../..';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import { fileDepositField } from '../file-deposit-decorator';
import type { FileDepositDecoratorProperties } from '../file-deposit-types';

describe('File decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'fileField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should set default values when no component properties provided', () => {
        fileDepositField({ node: '@sage/xtrem-test/TestNode', kind: 'upload' })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: FileDepositDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as FileDepositDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onChange).toBeUndefined();
        expect(mappedComponentProperties.onClick).toBeUndefined();
    });

    it('should inherit false abstract-field booleans when no provided', () => {
        fileDepositField({ node: '@sage/xtrem-test/TestNode', kind: 'upload' })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: FileDepositDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as FileDepositDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.isHiddenMobile).toBe(false);
        expect(mappedComponentProperties.isHiddenDesktop).toBe(false);
        expect(mappedComponentProperties.isFullWidth).toBe(false);
        expect(mappedComponentProperties.isHidden).toBe(false);
        expect(mappedComponentProperties.isTransient).toBe(false);
    });

    it('should set values when component properties provided', () => {
        const clickFunc: () => void = jest.fn().mockImplementation(() => {});
        const changeFunc: () => void = jest.fn().mockImplementation(() => {});

        fileDepositField({
            onChange: changeFunc,
            onClick: clickFunc,
            helperText: 'helper text',
            node: '@sage/xtrem-test/TestNode',
            fileTypes: 'image/*',
            kind: 'upload',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: FileDepositDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as FileDepositDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onChange).not.toBeUndefined();
        expect(mappedComponentProperties.onChange).toBe(changeFunc);
        expect(mappedComponentProperties.onClick).not.toBeUndefined();
        expect(mappedComponentProperties.onClick).toBe(clickFunc);
        expect(mappedComponentProperties.helperText).toBe('helper text');
        expect(mappedComponentProperties.fileTypes).toBe('image/*');
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(fileDepositField, pageMetadata, fieldId);
        });
    });
});
