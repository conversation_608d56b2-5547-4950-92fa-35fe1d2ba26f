import { renderWithRedux, applyActionMocks, getMockPageDefinition } from '../../../../__tests__/test-helpers';
import * as xtremRedux from '../../../../redux';
import { fireEvent, waitFor } from '@testing-library/react';
import * as React from 'react';
import type { FileDepositDecoratorProperties } from '../file-deposit-types';
import type { ScreenBase } from '../../../../service/screen-base';
import type { FieldInternalValue, FileDepositValue } from '../../../types';
import { FieldKey } from '../../../types';
import ConnectedFileDepositComponent from '../file-deposit-component';
import axios from 'axios';
import { GraphQLApi } from '../../../../service/graphql-api';
import '@testing-library/jest-dom';

const fileValue: FileDepositValue = {
    _id: '123',
    filename: 'some special data.csv',
    mimeType: 'text/csv',
    contentLength: 123456,
    downloadUrl: 'https://download.sage.com/my/special/file/123456',
    lastModified: '2022-09-20T13:58:46.485Z',
    status: 'uploaded',
    kind: 'attachment',
    uploadUrl: '',
    _createUser: {
        _id: '32',
        displayName: 'test user',
        email: '<EMAIL>',
    },
};

describe('file component', () => {
    const screenId = 'TestPage';
    const fieldId = 'test-file-field';

    const setup = (
        fileProps: Partial<FileDepositDecoratorProperties<ScreenBase>>,
        value: FieldInternalValue<FieldKey.FileDeposit> | null = fileValue,
    ) => {
        const pageDefinition = getMockPageDefinition(
            screenId,
            {},
            {
                uiComponentProperties: {
                    [fieldId]: { ...fileProps },
                },
            },
        );
        pageDefinition.values = {
            ...(value && { [fieldId]: value }),
        };
        const initialState = {
            screenDefinitions: { [screenId]: pageDefinition },
        };
        const utils = renderWithRedux<FieldKey.FileDeposit, any>(
            <ConnectedFileDepositComponent screenId={screenId} elementId={fieldId} />,
            {
                initialState,
                fieldType: FieldKey.FileDeposit,
                fieldValue: value,
                fieldProperties: { ...fileProps, node: '@sage/xtrem-test/TestNode', kind: 'upload' },
                elementId: fieldId,
                screenId,
                mockActions: true,
                partialPageDefinition: {
                    page: { $: { graph: new GraphQLApi({} as any) } } as any,
                },
            },
        );

        const fileDepositField = utils.getByTestId('e-field-bind-test-file-field', { exact: false });

        const link = fileDepositField.querySelector('[data-component="link"] a');
        const button = fileDepositField.querySelector('[data-component="link"] button');

        const actionButton: any = () => fileDepositField.querySelector('button[type="button"]');
        const clickActionButton = () => {
            fireEvent.click(actionButton());
        };

        const getInput = () => utils.getByTestId('e-file-deposit-field-upload-area', { exact: false });

        return {
            ...utils,
            fileDepositField,
            actionButton,
            clickActionButton,
            link,
            getInput,
            button,
        };
    };

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    it('can render with redux with defaults', () => {
        const { actionButton, fileDepositField } = setup({ title: 'Test Field Title' }, null);
        expect(fileDepositField).toHaveTextContent('Test Field Title');
        expect(actionButton()).toBeInTheDocument();
    });

    it('can render with redux with value', () => {
        const { link } = setup({ title: 'Test Field Title' }, fileValue);
        expect(link).toBeInTheDocument();
        expect(link).toHaveAttribute('href', 'https://download.sage.com/my/special/file/123456');
        expect(link).toHaveTextContent('some special data.csv');
    });

    it('can render hidden', () => {
        const { fileDepositField } = setup({ title: 'Test Field Title', isHidden: true });
        expect(fileDepositField).toHaveClass('e-hidden');
    });

    it('can render disabled without a title', () => {
        const { fileDepositField } = setup({ title: 'Test Field Title', isTitleHidden: true });
        expect(fileDepositField).not.toHaveTextContent('Test Field Title');
    });

    it('can render disabled with value', () => {
        const { link, button } = setup({ title: 'Test Field Title', isDisabled: true }, fileValue);
        expect(link).not.toBeInTheDocument();
        expect(button).toBeDisabled();
    });

    it('can render read only with value', () => {
        const { link } = setup({ title: 'Test Field Title', isReadOnly: true }, fileValue);
        expect(link).toBeInTheDocument();
        expect(link).toHaveAttribute('href');
    });

    it('should render helperText', () => {
        const { queryByTestId } = setup({ title: 'Test Field Title', helperText: 'This is a helper text' });
        expect(queryByTestId('e-field-helper-text')).toHaveTextContent('This is a helper text');
    });

    it('browse file button appears when deleting previous uploaded file', async () => {
        const { store, clickActionButton } = setup({ title: 'Test Field Title' }, fileValue);
        store.clearActions();
        jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue({
            type: 'SetFieldValue',
            then: jest.fn(arg => {
                arg();
                return { catch: jest.fn() };
            }),
        } as any);
        expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
        clickActionButton();
        expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(screenId, fieldId, null, true);
    });

    describe('upload', () => {
        beforeEach(() => {
            jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue({
                type: 'SetFieldValue',
                then: jest.fn(arg => {
                    arg();
                    return { catch: jest.fn() };
                }),
            } as any);
        });

        it('Should not upload the file if the mime type is not allowed', async () => {
            const { store, getInput } = setup({ title: 'Test Field Title', fileTypes: 'image/png' }, null);
            store.clearActions();
            expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();

            fireEvent.change(getInput(), {
                target: {
                    files: [
                        {
                            name: 'index.js',
                            type: 'text/javascript',
                            lastModified: 12345678,
                            contentLength: 8765432,
                            otherFileStuff: true,
                        },
                    ],
                },
            });

            await waitFor(() => {
                expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();
                expect(axios.post).not.toHaveBeenCalled();
            });
        });

        it('should create entry in the server when the user selects a file', async () => {
            const { store, getInput } = setup({ title: 'Test Field Title' }, null);
            store.clearActions();
            (axios.post as unknown as jest.SpyInstance).mockResolvedValue({
                data: {
                    xtremTest: {
                        testNode: {
                            create: {
                                _id: '43',
                                uploadUrl: 'http://upload.here',
                            },
                        },
                    },
                },
            });
            expect(axios.post).not.toHaveBeenCalled();
            expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();

            fireEvent.change(getInput(), {
                target: {
                    files: [
                        {
                            name: 'My new file.png',
                            type: 'image/png',
                            lastModified: 12345678,
                            contentLength: 8765432,
                            otherFileStuff: true,
                        },
                    ],
                },
            });

            await waitFor(() => {
                expect(axios.post).toHaveBeenCalled();
            });

            expect(axios.post).toHaveBeenCalledWith(
                '/api',
                {
                    query: `mutation {
    xtremTest {
        testNode {
            create (data: {filename: "My new file.png", mimeType: "image/png", lastModified: "1970-01-01T03:25:45.678Z", contentLength: undefined, status: "created", kind: "upload"}) {
                _id
                uploadUrl
                _createStamp
                _createUser {
                    displayName
                }
            }
        }
    }
}`,
                },
                { headers: { 'Accept-Language': 'en-US' } },
            );
        });
    });
});
