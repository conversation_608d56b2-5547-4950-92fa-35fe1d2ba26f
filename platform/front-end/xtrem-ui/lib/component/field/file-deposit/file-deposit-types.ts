import type { OnTelemetryEventFunction } from '../../../redux/state';
import type { GraphQLApi } from '../../../service/graphql-api';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenBaseGenericType } from '../../../types';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { FieldControlObjectInstance, FileDepositValue } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type { Changeable, Clickable, ExtensionField, HasParent } from '../traits';

export interface FileDepositProperties<
    CT extends ScreenBase = ScreenBase,
    K extends FileDepositValue['kind'] = FileDepositValue['kind'],
> extends EditableFieldProperties<CT> {
    /** File types that can be uploaded. Can be either audio/*, video/*, image/*, an extension name starting with '.'
     *  or a valid media type. Look at [IANA Media Types](https://www.iana.org/assignments/media-types/media-types.xhtml). for a complete list of standard media types.
     * It is possible to set more than one file type, simply by defining them separated by a comma.
     */
    fileTypes?: string;
    /** Name of the uploaded file */

    /** The GraphQL node that the field suggestions will be fetched from */
    node: keyof ScreenBaseGenericType<CT>;
    kind: K;
}

export interface FileDepositDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<FileDepositProperties<CT>, '_controlObjectType'>,
        Changeable<CT>,
        Clickable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>> {}

export type FileDepositComponentProps = BaseEditableComponentProperties<
    FileDepositDecoratorProperties,
    FileDepositValue,
    {
        graphApi: GraphQLApi<any>;
        onTelemetryEvent?: OnTelemetryEventFunction;
    }
>;
