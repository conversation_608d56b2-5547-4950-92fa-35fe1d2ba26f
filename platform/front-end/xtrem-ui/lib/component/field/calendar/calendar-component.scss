.e-calendar-field {
    position: relative;

    @include small_and_below {


        @include full_width_mobile_field;
    }

    .fc-theme-standard .fc-scrollgrid {
        border: 1px solid var(--fc-border-color, #ddd) 0;
    }

    .fc-theme-standard td,
    .fc-theme-standard th {
        border-right: none;
    }

    .e-field-header .e-field-actions-wrapper button {
        margin-top: 0;
    }

    .e-day-header {
        cursor: pointer;
    }

    .e-field-actions-wrapper {
        position: absolute;
        right: 8px;
        top: 32px;
    }

    .e-field-header .e-calendar-view-select {
        width: 120px;
        height: 32px;
    }

    .e-field-header {
        div[role='menu'] {
            background: var(--colorsYang100);
            border-color: var(--colorsYang100);
            box-shadow: var(--boxShadow200);

            button.e-calendar-view-select-item {
                border: none;
                cursor: pointer;
                margin-left: 0;

                &:hover {
                    background: var(--colorsUtilityMajor025);

                    span {
                        background: var(--colorsUtilityMajor025);
                    }
                }
            }
        }
    }

    .e-field-header .e-calendar-view-select-item {
        background-color: var(--colorsYang100);

        span {
            margin-left: 16px;
            color: var(--colorsActionMajor500);
        }
    }

    .e-field-header .e-calendar-view-select,
    .e-field-header .e-calendar-view-select button {
        padding-left: 16px;
        padding-right: 16px;
        width: 120px;
    }
}