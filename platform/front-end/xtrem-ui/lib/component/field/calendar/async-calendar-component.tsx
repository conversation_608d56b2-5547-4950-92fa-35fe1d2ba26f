import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { CalendarComponentInternalProps } from './calendar-types';

const ConnectedCalendarComponent = React.lazy(() => import('./calendar-component'));

export function AsyncConnectedCalendarComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedCalendarComponent {...props} />
        </React.Suspense>
    );
}

const CalendarComponent = React.lazy(() =>
    import('./calendar-component').then(c => ({ default: c.CalendarComponent })),
);

export function AsyncCalendarComponent(props: CalendarComponentInternalProps): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader size="small" />}>
            <CalendarComponent {...props} />
        </React.Suspense>
    );
}
