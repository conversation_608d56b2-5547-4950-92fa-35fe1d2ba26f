/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import { showToast } from '../../../service/toast-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { datePropertyValueToDateString } from '@sage/xtrem-date-time';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import type { DatePropertyValue } from '../../../utils/types';
import type { NestedFieldTypes } from '../../nested-fields';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldKey } from '../../types';
import { CollectionValueControlObject } from '../collection-value-field';
import type { CalendarProperties } from './calendar-types';

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a calendar
 */

export class CalendarControlObject<
    CT extends ScreenExtension<CT> = ScreenBase,
    CalendarEventType extends ClientNode = any,
> extends CollectionValueControlObject<
    FieldKey.Calendar,
    CalendarEventType,
    CT,
    NestedFieldTypes,
    CalendarProperties<CT, CalendarEventType>
> {
    static readonly defaultUiProperties: Partial<CalendarProperties> = {
        ...ReadonlyFieldControlObject.defaultUiProperties,
        canFilter: true,
    };

    /** Graphql filter that will restrict the events displayed in the calendar */
    get filter(): GraphQLFilter<CalendarEventType> | undefined {
        return resolveByValue({
            fieldValue: undefined,
            propertyValue: this.getUiComponentProperty('filter'),
            rowValue: undefined,
            screenId: this.screenId,
            skipHexFormat: true,
        });
    }

    /** Graphql filter that will restrict the events displayed in the calendar */
    set filter(filter: GraphQLFilter<CalendarEventType> | undefined) {
        this.setUiComponentProperties('filter', filter);
        this.refresh().catch(() => {
            /* Intentional fire and forget */
        });
    }

    /** The maximum date the user is allowed to navigate */
    get maxDate(): DatePropertyValue | undefined {
        return this.getUiComponentProperty('maxDate');
    }

    /** The maximum date the user is allowed to navigate */
    set maxDate(newValue: DatePropertyValue | undefined) {
        const date = newValue ? datePropertyValueToDateString(newValue, true) : newValue;
        this.setUiComponentProperties('maxDate', date);
    }

    /** The minimum date the user is allowed to navigate */
    get minDate(): DatePropertyValue | undefined {
        return this.getUiComponentProperty('minDate');
    }

    /** The minimum date the user is allowed to navigate */
    set minDate(newValue: DatePropertyValue | undefined) {
        const date = newValue ? datePropertyValueToDateString(newValue, true) : newValue;
        this.setUiComponentProperties('minDate', date);
    }

    async refresh(): Promise<void> {
        await this._refresh({ keepPageInfo: true }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    }
}
