import { kebabCase } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import type { Filter } from '../../../service/filter-service';
import { getGraphQLFilter } from '../../../service/filter-service';
import { showToast } from '../../../service/toast-service';
import { getScreenElement } from '../../../service/screen-base-definition';
import { refreshField } from '../../../service/field-state-service';
import { getPropertyScalarType } from '../../../utils/abstract-fields-utils';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import { findDeepPropertyType } from '../../../utils/node-utils';
import { schemaTypeNameFromNodeName } from '../../../utils/transformers';
import { <PERSON>Key } from '../../types';
import type { FilterManagerField } from '../../ui/filter/filter-manager';
import { FiltersComponent, FiltersLabels } from '../../ui/filter/filters-component';
import { HelperText } from '../carbon-utility-components';
import { mapReadonlyStateToProps, ReadonlyFieldBaseComponent } from '../field-base-component';
import type { FieldComponentExternalProperties, ReadonlyFieldComponentProperties } from '../field-base-component-types';
import { FieldHeader } from '../field-header';
import type {
    CalendarComponentAdditionalProps,
    CalendarComponentInternalProps,
    CalendarComponentProps,
    CalendarDecoratorProperties,
    CalendarState,
    InternalCalendarProperties,
} from './calendar-types';
import type { CollectionValue } from '../../../service/collection-data-service';
import type { CalendarView } from '../../ui/calendar-body/calendar-body-types';
import { AsyncCalendarBodyComponent } from '../../ui/calendar-body/async-calendar-body-component';
import type { ReferenceDecoratorProperties } from '../reference/reference-types';
import type { NestedField } from '../../nested-fields';
import { CalendarViewSelector } from '../../ui/calendar-body/calendar-view-selector';
import type { NodePropertyType } from '../../../types';
import { objectKeys } from '@sage/xtrem-shared';

export class CalendarComponent extends ReadonlyFieldBaseComponent<
    InternalCalendarProperties,
    CollectionValue,
    CalendarComponentAdditionalProps & { fieldProperties: InternalCalendarProperties },
    CalendarState
> {
    constructor(props: CalendarComponentInternalProps) {
        super(props);

        this.state = {
            filters: [],
            selectedView: 'dayGridMonth',
            firstRender: true,
            selectedRecordId: this.props.selectedRecordId,
        };
    }

    private readonly onFilterChange = (): Promise<void> => {
        const nestedFields: FilterManagerField[] = this.getEventItems();
        const filter = getGraphQLFilter(this.state.filters, nestedFields);
        if (this.props.setFieldProperties) {
            this.props.setFieldProperties(this.props.elementId, {
                ...this.props.fieldProperties,
                activeUserFilter: filter,
            });
        }
        return refreshField({
            screenId: this.props.screenId,
            elementId: this.props.elementId,
            keepPageInfo: true,
        }).catch(e => {
            showToast(e.message || e, { type: 'warning' });
        });
    };

    private readonly saveFilters = async (filters: Filter[]): Promise<void> => {
        this.setState({ filters }, () => this.onFilterChange());
    };

    private readonly onSelectView = (selectedView: CalendarView): void => {
        this.setState({
            selectedView,
        });
    };

    private readonly getTypedEventItems = (
        listItem: InternalCalendarProperties['eventCard'],
        node?: NodePropertyType,
    ): {
        type: any;
        properties: any;
    }[] => {
        return objectKeys(listItem)
            .filter(key => listItem[key])
            .map(key => {
                const nestedField = listItem[key] as NestedField<any, any>;
                const fieldType =
                    findDeepPropertyType(
                        schemaTypeNameFromNodeName(node),
                        nestedField.properties.bind,
                        this.props.nodeTypes,
                        true,
                    )?.type || FieldKey.Text;
                return {
                    type: nestedField.type,
                    properties: {
                        ...nestedField.properties,
                        type: getPropertyScalarType(
                            this.props.nodeTypes,
                            fieldType,
                            nestedField.type,
                            (nestedField.properties as ReferenceDecoratorProperties).valueField,
                        ),
                    },
                };
            });
    };

    private readonly getEventItems = (): {
        type: any;
        properties: any;
    }[] => {
        const listItem = this.props.fieldProperties.eventCard;
        const node = this.props.fieldProperties.node;
        return this.getTypedEventItems(listItem, node);
    };

    render(): React.ReactNode {
        // IG: If the calendar is init as hidden it will not render correctly when changed to not hidden.
        // TODO: We should do that only in the FIRST render. After that we will use the display: none used on
        // getComponentClassx
        if (this.getResolvedProperty('isHidden') && this.state.firstRender) {
            return null;
        }
        return (
            <div
                {...this.getBaseAttributesDivWrapper(
                    'calendar',
                    `e-calendar-field e-calendar-field-type-${kebabCase(String(this.state.selectedView))}`,
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    this.props.isNested,
                )}
            >
                <FieldHeader
                    elementId={this.props.elementId}
                    screenId={this.props.screenId}
                    title={this.getTitle()}
                    isDisabled={this.isDisabled()}
                    isTitleHidden={this.props.fieldProperties.isTitleHidden}
                >
                    {this.props.isGreaterThanSmall && (
                        <CalendarViewSelector onSelectView={this.onSelectView} selectedView={this.state.selectedView} />
                    )}
                    {this.props.fieldProperties.canFilter && this.props.fieldProperties.node && (
                        <FiltersComponent
                            screenId={this.props.screenId}
                            fields={this.getEventItems()}
                            handleSave={this.saveFilters}
                            filters={this.state.filters}
                            isDisabled={this.isDisabled()}
                        />
                    )}
                </FieldHeader>
                {this.props.fieldProperties.canFilter && (
                    <FiltersLabels
                        screenId={this.props.screenId}
                        fields={this.getEventItems()}
                        handleSave={this.saveFilters}
                        filters={this.state.filters}
                        isDisabled={this.isDisabled()}
                    />
                )}
                <AsyncCalendarBodyComponent
                    cardColor={this.props.fieldProperties.cardColor}
                    elementId={this.props.elementId}
                    endDateField={this.props.fieldProperties.endDateField}
                    eventCard={this.props.fieldProperties.eventCard}
                    fieldProperties={this.props.fieldProperties}
                    fixedHeight={this.props.fixedHeight}
                    isGreaterThanSmall={this.props.isGreaterThanSmall}
                    screenId={this.props.screenId}
                    selectedView={this.state.selectedView}
                    startDateField={this.props.fieldProperties.startDateField}
                    value={this.props.value}
                />
                {this.props.fieldProperties.helperText && (
                    <HelperText helperText={this.props.fieldProperties.helperText} />
                )}
            </div>
        );
    }
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: FieldComponentExternalProperties,
): CalendarComponentProps => {
    const componentProperties = mapReadonlyStateToProps()(state, props) as ReadonlyFieldComponentProperties<
        CalendarDecoratorProperties,
        CollectionValue
    >;

    if (componentProperties.fieldProperties.onDayClick) {
        componentProperties.fieldProperties.onDayClick = componentProperties.fieldProperties.onDayClick.bind(
            getScreenElement(state.screenDefinitions[props.screenId]),
        );
    }

    if (componentProperties.fieldProperties.onEventClick) {
        componentProperties.fieldProperties.onEventClick = componentProperties.fieldProperties.onEventClick.bind(
            getScreenElement(state.screenDefinitions[props.screenId]),
        );
    }

    const pageDefinition = getPageDefinitionFromState(props.screenId, state);

    return {
        isGreaterThanSmall: state.browser.greaterThan.s,
        ...props,
        ...componentProperties,
        loading: state.loading.globalLoading,
        selectedRecordId: pageDefinition.selectedRecordId,
        setFieldProperties: xtremRedux.actions.actionStub,
        nodeTypes: state.nodeTypes,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: CalendarComponentProps,
): Partial<CalendarComponentProps> => ({
    setFieldProperties: (elementId: string, value: InternalCalendarProperties): void => {
        dispatch(xtremRedux.actions.setFieldProperties(props.screenId, elementId, value));
    },
});

export const ConnectedCalendarComponent = connect(mapStateToProps, mapDispatchToProps)(CalendarComponent);

export default ConnectedCalendarComponent;
