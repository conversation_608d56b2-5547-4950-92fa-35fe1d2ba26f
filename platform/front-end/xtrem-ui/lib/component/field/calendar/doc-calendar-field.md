PATH: XTREEM/UI+Field+Widgets/Calendar+Field

## Introduction

The calendar field represents calendar within the user interface. The field is also available as an extension field.

## Example

```ts
ui.decorators.calendarField<Page, Node>({
    bind: 'products',
    canFilter: true,
    endDateField: 'depricationDate'
    eventCard: {
        title: ui.nestedFields.text({ bind: 'product' }),
        titleRight: ui.nestedFields.numerc({ bind: 'msrp' }),
        line2: ui.nestedFields.text({ bind: 'description' }),
        line2Right: ui.nestedFields.numeric({ bind: 'quantity' }),
    },
    filter: { quantity: { _gt: 0 } },
    helperText: 'Helper text displayed below the field.',
    isDisabled: false,
    isFullWidth: true,
    isHelperTextHidden: false,
    isHidden: false,
    isTitleHidden: false,
    isTransient: false,
    maxDate: '2025-12-31',
    minDate: '2000-01-01',
    node: '@sage/xtrem-docs/Product',
    startDateField: 'releaseDate',
    title: 'Calendar Field',
    cardColor(event: CalendarEventType) {
        return event.msrp > 100 ? ui.tokens.colorsSemanticPositive500 : undefined;
    },
    fieldActions() {
        return [this.action];
    },
    onDayClick(date) {
        console.log(`Do something when the field is clicked.`);
    },
    onEventClick(_id, event) {
        console.log(`Do something when the event is clicked.`);
    },
    parent() {
        return this.block;
    },
})
field: ui.fields.Calendar;
```

#### Use of field in extended page:

```ts
ui.decorators.calendarField<Page, Node>({
    ...,
    title: 'Extended Calendar Field',
    insertBefore() {
        return this.field;
    },
    parent() {
        return this.block;
    },
});
extension: ui.fields.Calendar;
```

## Decorator Properties

#### Display Properties

-   **cardColor**: Custom callback function to determine the color of the field's event card color. The event is provided as argument. If the function returns a valid css backgroundColor value, this value is used as the background for the associated event. Otherwise, the default styling is used.
-   **helperText**: The helper text displayed below the field. This property is automatically picked up by the i18n engine and externalized.
-   **isDisabled**: Determines whether the field is disabled or not. It can also be defined as callback function that returns a boolean.
-   **isFullWidth**: Determines whether the field takes up the full width of the grid or not.
-   **isHelperTextHidden**: Determines whether the field's helper text is displayed or not.
-   **isHidden**: Determines whether the field is displayed or not.
-   **isTitleHidden**: Determines whether the field's title is displayed or not.
-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. When declared as a callback within the column of a nested grid, the column id is provided as a parameter. It is automatically picked up by the i18n engine and externalized for translation.

#### Binding Properties

-   **bind**: Determines the associated GraphQL node's property the field's value will be bound to.
-   **endDateField**: Specifies node property to be used as end date for the associated event.
-   **filter**: GraphQL fliter that will restrict the records displayed in the field. **Make sure that all properties used by filters have a corresponding column definition under the "*columns*" property.**
-   **isTransient**: Determines whether the field will be excluded from the automatic data binding process or not.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.
-   **node**: Name of the node that the field represents. This field must be defined, to use advanced filtering capabilities.
-   **startDateField**: Specifies node property to be used as start date for the associated event.

#### Validation Properties

-   **maxDate**: Determines the maximum date value the field will navigate to.
-   **minDate**: Determines the minimum date value the field will navigate to.

#### Event Handler Properties

-   **onDayClick()**: Handler triggered when a calendar day is clicked.
-   **onEventClick()**: Handler triggered when a calendar event is clicked.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/Calendar).
