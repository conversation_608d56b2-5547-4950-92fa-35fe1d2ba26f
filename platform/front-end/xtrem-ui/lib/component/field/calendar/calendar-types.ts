import type { ClientNode } from '@sage/xtrem-client';
import type { Dict } from '@sage/xtrem-shared';
import type { CollectionValue } from '../../../service/collection-data-service';
import type { Filter } from '../../../service/filter-service';
import type { GraphQLFilter } from '../../../service/graphql-utils';
import type { ScreenBase } from '../../../service/screen-base';
import type { NestedRecordId, ScreenBaseGenericType } from '../../../types';
import type { DatePropertyValue } from '../../../utils/types';
import type { BlockControlObject, CollectionValueFieldProperties } from '../../control-objects';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseReadonlyComponentProperties } from '../field-base-component-types';
import type {
    <PERSON><PERSON>ield,
    HasCalendarConfigurationOptions,
    <PERSON><PERSON>ieldActions,
    <PERSON><PERSON>ener<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
} from '../traits';
import type { CalendarView } from '../../ui/calendar-body/calendar-body-types';
import type { CardDefinition } from '../../ui/card/card-component';
import type { PropertyValueType } from '../reference/reference-types';
import type { FormattedNodeDetails } from '../../../service/metadata-types';

export interface CalendarProperties<CT extends ScreenBase = ScreenBase, CalendarEventType extends ClientNode = any>
    extends CollectionValueFieldProperties<CT>,
        HasCalendarConfigurationOptions<CT, CalendarEventType> {
    // @override to make the property mandatory
    startDateField: PropertyValueType<CalendarEventType>;

    /** The definitions of the nested fields used to represent the calendar events */
    eventCard: CardDefinition<CT, CalendarEventType>;

    /** Event handler that will be called when a day is clicked */
    onDayClick?: (this: CT, date: Date) => void;

    /** Event handler that will be called when an event is clicked */
    onEventClick?: (this: CT, _id: string, event: CalendarEventType, isModifierKeyPushed: boolean) => void;

    /** The GraphQL node that the table represents, needed for filtering */
    node?: keyof ScreenBaseGenericType<CT>;

    /** Whether the events of the calendar can be filtered or not. Defaults to true */
    canFilter?: boolean;
}

export interface InternalCalendarProperties<CT extends ScreenBase = ScreenBase>
    extends CalendarDecoratorProperties<CT> {
    rangeStart: DatePropertyValue;
    rangeEnd: DatePropertyValue;
    activeUserFilter?: GraphQLFilter;
}

export interface CalendarDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    CalendarEventType extends ClientNode = any,
> extends CalendarProperties<CT, CalendarEventType>,
        HasFieldActions<CT>,
        Mappable<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasGenericErrorHandler<CT>,
        HasParent<CT, BlockControlObject<CT>> {}

export type BaseCalendarExtensionDecoratorProperties<
    CT extends ScreenBase = ScreenBase,
    CalendarEventType extends ClientNode = any,
> = {
    onDayClickAfter?: (this: CT, date: Date) => void;
    onEventClickAfter?: (this: CT, _id: NestedRecordId, event: CalendarEventType, isModifierKeyPushed: boolean) => void;
};

export interface CalendarComponentAdditionalProps {
    isGreaterThanSmall: boolean;
    loading: boolean;
    nodeTypes: Dict<FormattedNodeDetails>;
    selectedRecordId: string | null;
    setFieldProperties: (elementId: string, value: InternalCalendarProperties) => void;
}

export type CalendarComponentProps = BaseReadonlyComponentProperties<
    CalendarDecoratorProperties,
    CollectionValue,
    CalendarComponentAdditionalProps
>;

export type CalendarComponentInternalProps = CalendarComponentProps & { fieldProperties: InternalCalendarProperties };

export interface CalendarState {
    filters: Filter[];
    firstRender: boolean;
    selectedRecordId: string | null;
    selectedView: CalendarView;
}
