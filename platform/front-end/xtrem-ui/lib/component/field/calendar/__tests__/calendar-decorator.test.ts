import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata } from '../../../../__tests__/test-helpers';
import * as nestedFields from '../../../nested-fields';
import { calendarField } from '../calendar-decorator';
import type { CalendarDecoratorProperties } from '../calendar-types';

describe('Calendar decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;
    const fields: CalendarDecoratorProperties<any> = {
        startDateField: 'fieldDate',
        eventCard: { title: nestedFields.text<any, any>({ bind: 'bindprop' }) },
    };
    beforeEach(() => {
        fieldId = 'calendarField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should set minDate field when parameter type is string', () => {
        calendarField({
            ...fields,
            minDate: '2019-10-30',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties = pageMetadata.uiComponentProperties[
            fieldId
        ] as CalendarDecoratorProperties<Page>;

        expect(mappedComponentProperties.minDate).toBe('2019-10-30');
    });

    it('should set maxDate field when parameter type is string', () => {
        calendarField({
            ...fields,
            maxDate: '2019-10-30',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties = pageMetadata.uiComponentProperties[
            fieldId
        ] as CalendarDecoratorProperties<Page>;

        expect(mappedComponentProperties.maxDate).toBe('2019-10-30');
    });

    it('should set minDate field when parameter type is Date Object', () => {
        const minDate = new Date();
        calendarField({
            ...fields,
            minDate,
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties = pageMetadata.uiComponentProperties[
            fieldId
        ] as CalendarDecoratorProperties<Page>;

        expect(mappedComponentProperties.minDate).toBe(minDate);
    });

    it('should set maxDate field when parameter type is is Date Object', () => {
        const maxDate = new Date();
        calendarField({
            ...fields,
            maxDate,
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties = pageMetadata.uiComponentProperties[
            fieldId
        ] as CalendarDecoratorProperties<Page>;

        expect(mappedComponentProperties.maxDate).toBe(maxDate);
    });

    describe('mapping values', () => {
        it('should set default values when no component properties provided', () => {
            calendarField({
                ...fields,
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties = pageMetadata.uiComponentProperties[
                fieldId
            ] as CalendarDecoratorProperties<Page>;
            expect(mappedComponentProperties.map).toBeUndefined();
        });
    });
});
