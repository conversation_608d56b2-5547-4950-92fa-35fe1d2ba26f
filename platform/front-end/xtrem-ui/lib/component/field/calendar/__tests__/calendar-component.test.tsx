jest.mock('../../../ui/calendar-body/async-calendar-body-component');

import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { GraphqlCollection } from '../../../types';
import { FieldKey } from '../../../types';
import { nestedFields } from '../../../..';
import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import type * as xtremRedux from '../../../../redux';
import type { ScreenBase } from '../../../../service/screen-base';
import { ConnectedCalendarComponent } from '../calendar-component';
import type { CalendarDecoratorProperties } from '../calendar-types';
import { render } from '@testing-library/react';

describe('Calendar component', () => {
    const screenId = 'TestPage';
    let mockFieldProperties: CalendarDecoratorProperties<ScreenBase>;
    let mockValue: GraphqlCollection;

    beforeEach(() => {
        jest.useFakeTimers().setSystemTime(new Date('2020-01-05T12:00:00.000Z'));
        mockFieldProperties = {
            title: 'Test calendar',
            startDateField: 'fieldDate',
            eventCard: {
                title: nestedFields.text<any, any>({ bind: 'fieldTitle' }),
            },
        };
        mockValue = {
            data: [
                {
                    _id: '12',
                    title: 'test event',
                },
            ],
            pageInfo: {},
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Calendar, state, screenId, 'test-calendar-field', mockFieldProperties, mockValue);
            addFieldToState(FieldKey.Calendar, state, screenId, 'test-empty-calendar-field', mockFieldProperties, null);
            mockStore = getMockStore(state);
        });

        afterEach(() => {
            jest.resetAllMocks();
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCalendarComponent screenId={screenId} elementId="test-calendar-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCalendarComponent screenId={screenId} elementId="test-calendar-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render without value', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedCalendarComponent screenId={screenId} elementId="test-empty-calendar-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });
        });
    });
});
