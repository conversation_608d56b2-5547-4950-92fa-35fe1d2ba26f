import * as nestedFields from '../../../nested-fields';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { CalendarControlObject } from '../../../control-objects';
import { <PERSON>Key } from '../../../types';
import type { CalendarDecoratorProperties } from '../calendar-types';
import { CollectionValue } from '../../../../service/collection-data-service';
import { CollectionFieldTypes } from '../../../../service/collection-data-types';

describe('Calendar control object', () => {
    let value: CollectionValue;
    const screenId = 'TestPage';
    let calendarField: CalendarControlObject;
    const properties = {
        title: 'TEST_FIELD_TITLE',
        isHidden: true,
        isDisabled: true,
        startDateField: 'datefield',
        eventCard: {
            title: nestedFields.text<any, any>({ bind: 'textfield' }),
        },
    } as CalendarDecoratorProperties;
    const elementId = 'test';

    beforeEach(() => {
        value = new CollectionValue({
            screenId,
            elementId,
            isTransient: false,
            hasNextPage: false,
            orderBy: [{}],
            columnDefinitions: [
                [
                    nestedFields.text<any, any>({ bind: '_id' }),
                    nestedFields.text<any, any>({ bind: 'anyField' }),
                    nestedFields.numeric({ bind: 'someOtherField' }),
                ],
            ],
            nodeTypes: {},
            nodes: ['@sage/xtrem-test/AnyNode'],
            filter: [undefined],
            initialValues: [],
            fieldType: CollectionFieldTypes.POD,
        });

        calendarField = createFieldControlObject<FieldKey.Calendar>(
            FieldKey.Calendar,
            screenId,
            CalendarControlObject,
            elementId,
            value,
            properties,
        );
    });

    it('getting field value', () => {
        expect(calendarField.value).toEqual([]);
    });

    it('should set minDate field when parameter type is Date object', () => {
        const minDate = new Date();
        expect(calendarField.minDate).not.toEqual(minDate.toISOString());
        calendarField.minDate = minDate;
        expect(calendarField.minDate).toEqual(minDate.toISOString());
    });

    it('should set maxDate field when parameter type is Date object', () => {
        const maxDate = new Date();
        expect(calendarField.maxDate).not.toEqual(maxDate.toISOString());
        calendarField.maxDate = maxDate;
        expect(calendarField.maxDate).toEqual(maxDate.toISOString());
    });

    it('should set minDate field when parameter type is string', () => {
        const minDate = '2022-11-12';
        expect(calendarField.minDate).not.toEqual(minDate);
        calendarField.minDate = minDate;
        expect(calendarField.minDate).toEqual(`${minDate}T00:00:00.000Z`);
    });

    it('should set maxDate field when parameter type is string', () => {
        const maxDate = '2022-12-31';
        expect(calendarField.maxDate).not.toEqual(maxDate);
        calendarField.maxDate = maxDate;
        expect(calendarField.maxDate).toEqual(`${maxDate}T00:00:00.000Z`);
    });
});
