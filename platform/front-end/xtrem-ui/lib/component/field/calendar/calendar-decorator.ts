/**
 * @packageDocumentation
 * @module root
 * */

import type { ClientNode } from '@sage/xtrem-client';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { OverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { CalendarControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { BaseCalendarExtensionDecoratorProperties, CalendarDecoratorProperties } from './calendar-types';

export type CalendarExtensionDecoratorProperties<
    CT extends ScreenExtension<CT>,
    ReferencedItemType extends ClientNode = any,
> = BaseCalendarExtensionDecoratorProperties<Extend<CT>> &
    OverrideDecoratorProperties<CalendarDecoratorProperties<Extend<CT>, ReferencedItemType>>;
class CalendarDecorator extends AbstractFieldDecorator<FieldKey.Calendar> {
    protected _controlObjectConstructor = CalendarControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

/**
 * Initializes the decorated member as a [Calendar]{@link CalendarControlObject} field with the provided properties
 *
 * @param properties The properties that the [Calendar]{@link CalendarControlObject} field will be initialized with
 */
export function calendarField<T extends ScreenExtension<T>, CalendarEventType extends ClientNode = any>(
    properties: CalendarDecoratorProperties<Extend<T>, CalendarEventType>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Calendar>(
        properties,
        CalendarDecorator,
        FieldKey.Calendar,
        true,
    );
}

export function calendarFieldOverride<T extends ScreenExtension<T>, ReferencedItemType extends ClientNode = any>(
    properties: CalendarExtensionDecoratorProperties<T, ReferencedItemType>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Calendar>(properties);
}
