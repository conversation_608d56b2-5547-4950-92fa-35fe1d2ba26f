import * as React from 'react';
import type { ScreenBase } from '../../service/screen-base';
import type { ContextType } from '../../types';
import { triggerFieldEvent, triggerNestedFieldEvent } from '../../utils/events';
import { resolveByValue } from '../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../utils/transformers';
import type { EditableFieldProperties } from '../editable-field-control-object';
import type { NestedFieldHandlersArguments } from '../nested-fields';
import type { ReadonlyFieldProperties } from '../readonly-field-control-object';
import { getCommonCarbonComponentProperties, isFieldDisabled, isFieldReadOnly } from './carbon-helpers';
import { FieldLabel, HelperText } from './carbon-utility-components';
import { ReadonlyFieldBaseComponent } from './field-base-component';
import type { Clickable, HasUnit, Postfixable, Prefixable } from './traits';
import type { NestedFieldsAdditionalProperties } from './field-base-component-types';
import CarbonTooltip from 'carbon-react/esm/components/tooltip';
import { isNil } from 'lodash';
import { getScalePrefixPostfixFromUnit } from '../../utils/formatters';

export interface CarbonWrapperProps {
    children: React.ReactNode;
    className: string;
    componentName: string;
    componentRef?: React.RefObject<any>;
    contextType?: ContextType;
    handlersArguments?: NestedFieldHandlersArguments;
    helperText?: string;
    isReadOnly?: ((value: any, rowValue?: any) => boolean) | boolean;
    map?: (value?: any) => string;
    nestedReadOnlyField?: boolean;
    shouldRenderLabelInNestedReadOnlyMode?: boolean;
    noReadOnlySupport?: boolean;
    isNested?: boolean;
    readOnlyDisplayValue?: string;
    readOnlyTooltip?: string;
    isTitleHidden?: boolean;
}

export class CarbonWrapper extends ReadonlyFieldBaseComponent<
    EditableFieldProperties & Prefixable<ScreenBase> & Postfixable<ScreenBase>,
    any,
    CarbonWrapperProps
> {
    getMappedValue = (): string => (this.props.map ? this.props.map(this.props.value) : (this.props.value ?? ''));

    getDisplayValue(): string {
        if (isNil(this.props.readOnlyDisplayValue)) {
            return this.getMappedValue();
        }

        return this.props.readOnlyDisplayValue;
    }

    onClick = async (): Promise<void> => {
        const fieldProperties: Clickable<ScreenBase> & ReadonlyFieldProperties = this.props
            .fieldProperties as Clickable<ScreenBase> & ReadonlyFieldProperties;
        const nestedFieldsAdditionalProperties = this.props as NestedFieldsAdditionalProperties;

        const isDisabled = isFieldDisabled(this.props.screenId, fieldProperties, this.props.value, undefined);

        if (!isDisabled) {
            if (nestedFieldsAdditionalProperties.isNested) {
                await triggerNestedFieldEvent(
                    this.props.screenId,
                    nestedFieldsAdditionalProperties.parentElementId || this.props.elementId,
                    fieldProperties as any,
                    'onClick',
                    nestedFieldsAdditionalProperties.handlersArguments?.rowValue._id,
                    nestedFieldsAdditionalProperties.handlersArguments?.rowValue,
                );
            } else {
                await triggerFieldEvent(this.props.screenId, this.props.elementId, 'onClick');
            }
        }
    };

    getReadonlyValueNode = (rowValue: any): React.ReactElement => {
        const readOnlyClasses = ['e-field-read-only'];
        if (this.props.nestedReadOnlyField) {
            readOnlyClasses.push('e-field-nested-no-input');
        }

        const computedUnitProperties = getScalePrefixPostfixFromUnit(
            this.props.screenId,
            this.props.locale,
            this.props.fieldProperties as HasUnit<any>,
            rowValue,
        );

        const prefix =
            resolveByValue({
                fieldValue: this.props.value,
                propertyValue: this.props.fieldProperties.prefix,
                rowValue,
                skipHexFormat: true,
                screenId: this.props.screenId,
            }) ?? computedUnitProperties?.prefix;

        const formattedPrefix = prefix ? `${prefix} ` : '';

        const postfix =
            resolveByValue({
                fieldValue: this.props.value,
                propertyValue: this.props.fieldProperties.postfix,
                rowValue,
                skipHexFormat: true,
                screenId: this.props.screenId,
            }) ??
            computedUnitProperties?.postfix ??
            '';

        const formattedPostfix = postfix ? ` ${postfix}` : '';

        return (
            <span data-testid="e-field-value" className={readOnlyClasses.join(' ')} onClick={this.onClick}>
                {`${formattedPrefix}${this.getDisplayValue()}${formattedPostfix}`}
            </span>
        );
    };

    render(): React.ReactNode {
        const carbonProps = getCommonCarbonComponentProperties(this.props as any);
        const rowValue = this.props.handlersArguments?.rowValue
            ? splitValueToMergedValue(this.props.handlersArguments?.rowValue)
            : undefined;

        return (
            <div
                ref={this.props.componentRef}
                {...this.getBaseAttributesDivWrapper(
                    this.props.componentName,
                    this.props.className,
                    this.props.contextType,
                    this.props.handlersArguments?.rowValue,
                    Boolean(this.props.isNested),
                )}
            >
                {this.props.nestedReadOnlyField ||
                (isFieldReadOnly(
                    this.props.screenId,
                    this.props.fieldProperties,
                    this.props.value,
                    rowValue,
                    this.props.contextType,
                ) &&
                    !this.props.noReadOnlySupport) ? (
                        <div>
                            {(!this.props.nestedReadOnlyField || this.props.shouldRenderLabelInNestedReadOnlyMode) &&
                            !this.props.fieldProperties.isTitleHidden && (
                                <FieldLabel
                                    label={this.getTitle()}
                                    htmlFor={carbonProps.id}
                                    className={
                                        this.props.shouldRenderLabelInNestedReadOnlyMode &&
                                        this.props.nestedReadOnlyField
                                            ? 'e-nested-read-only-label'
                                            : ''
                                    }
                                />
                            )}
                            <div>
                                {this.props.readOnlyTooltip && (
                                <CarbonTooltip message={this.props.readOnlyTooltip}>
                                    {this.getReadonlyValueNode(rowValue)}
                                </CarbonTooltip>
                            )}
                                {!this.props.readOnlyTooltip && this.getReadonlyValueNode(rowValue)}
                                {!this.props.nestedReadOnlyField && (
                                <HelperText
                                    helperText={this.props.helperText || this.props.fieldProperties.helperText}
                                />
                            )}
                            </div>
                        </div>
                ) : (
                    this.props.children
                )}
            </div>
        );
    }
}
