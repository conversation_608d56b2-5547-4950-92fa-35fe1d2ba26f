import Link from 'carbon-react/esm/components/link';
import * as React from 'react';
import { connect } from 'react-redux';
import { getRouter } from '../../../service/router';
import { REG_EXP_URL_PATTERN } from '../../../utils/constants';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapReadonlyStateToProps, ReadonlyFieldBaseComponent } from '../field-base-component';
import type { NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { LinkDecoratorProperties } from './link-types';
import { getPage, getQueryParameters, getTextLink } from './link-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';

export class LinkComponent extends ReadonlyFieldBaseComponent<
    LinkDecoratorProperties,
    string,
    NestedFieldsAdditionalProperties
> {
    private readonly onClick = (e: React.MouseEvent<HTMLButtonElement>): void => {
        e.preventDefault();
        e.stopPropagation();
        const modifierKeyPushed = e.metaKey || e.ctrlKey;

        const page = getPage(this.props.screenId, this.props.fieldProperties, this.props.value);

        if (!page) {
            this.getClickHandler()();
            return;
        }

        if (page.match(REG_EXP_URL_PATTERN)) {
            // External link, opening on a new tab
            window.open(page, '_blank', 'noopener=true');
        } else if (modifierKeyPushed) {
            window.open(
                getTextLink(this.props.screenId, this.props.fieldProperties, this.props.value),
                '_blank',
                'noopener=true',
            );
        } else {
            getRouter(this.props.screenId).goTo(
                page,
                getQueryParameters(this.props.screenId, this.props.fieldProperties, this.props.value),
            );
        }
    };

    getFocusableElement(element: HTMLElement): HTMLButtonElement | null {
        return element.querySelector('button');
    }

    render(): React.ReactNode {
        const icon = resolveByValue({
            screenId: this.props.screenId,
            propertyValue: this.props.fieldProperties.icon,
            rowValue: null,
            fieldValue: null,
            skipHexFormat: true,
        });

        const body = (
            <Link
                disabled={this.isDisabled()}
                onClick={this.onClick}
                icon={icon}
                href={getTextLink(this.props.screenId, this.props.fieldProperties, this.props.value)}
            >
                {this.getValue() || ''}
            </Link>
        );

        if (this.props.isNested) {
            return (
                <div
                    {...this.getBaseAttributesDivWrapper(
                        'link',
                        'e-link-field',
                        this.props.contextType,
                        this.props.handlersArguments?.rowValue,
                        true,
                    )}
                >
                    {this.props.parentElementId === '$navigationPanel' && this.props.screenId !== '$dashboard'
                        ? this.getValue()
                        : body}
                </div>
            );
        }

        const { isTitleHidden } = this.props.fieldProperties;
        return (
            <CarbonWrapper
                {...this.props}
                className="e-link-field"
                componentRef={this.componentRef}
                componentName="link"
                handlersArguments={this.props.handlersArguments}
                value={this.props.value}
            >
                {!isTitleHidden && <FieldLabel label={this.getTitle()} />}
                {body}
                <HelperText helperText={this.props.fieldProperties.helperText} />
            </CarbonWrapper>
        );
    }
}

export const ConnectedLinkComponent = connect(mapReadonlyStateToProps())(LinkComponent);

export default ConnectedLinkComponent;
