import type { LinkDecoratorProperties, NestedLinkProperties } from './link-types';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import type { QueryParameters } from '../../../utils/types';

export const getQueryParameters = (
    screenId: string,
    fieldProperties: Omit<LinkDecoratorProperties | NestedLinkProperties, 'onClick'>,
    fieldValue: any,
    rowValue?: any,
): QueryParameters => {
    return resolveByValue<QueryParameters>({
        screenId,
        propertyValue: fieldProperties.queryParameters,
        rowValue,
        fieldValue,
        skipHexFormat: true,
    });
};

export const getQueryParametersAsString = (
    screenId: string,
    fieldProperties: Omit<LinkDecoratorProperties | NestedLinkProperties, 'onClick'>,
    fieldValue: any,
    rowValue?: any,
): string => {
    const queryParameters = getQueryParameters(screenId, fieldProperties, fieldValue, rowValue);
    if (!queryParameters) {
        return '';
    }
    return btoa(JSON.stringify(queryParameters));
};

export const getPage = (
    screenId: string,
    fieldProperties: Omit<LinkDecoratorProperties | NestedLinkProperties, 'onClick'>,
    fieldValue: any,
    rowValue?: any,
): string => {
    return resolveByValue({
        screenId,
        propertyValue: fieldProperties.page,
        rowValue,
        fieldValue,
        skipHexFormat: true,
    });
};

export const getTextLink = (
    screenId: string,
    fieldProperties: Omit<LinkDecoratorProperties | NestedLinkProperties, 'onClick'>,
    fieldValue: any,
    rowValue?: any,
): string => {
    const queryParameters = getQueryParametersAsString(screenId, fieldProperties, fieldValue, rowValue);
    const page = getPage(screenId, fieldProperties, fieldValue, rowValue);
    if (!queryParameters) {
        return page || '#';
    }
    return page ? `${page}/${queryParameters}` : '#';
};
