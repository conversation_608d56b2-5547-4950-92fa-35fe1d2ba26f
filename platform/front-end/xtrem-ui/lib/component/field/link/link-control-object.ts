import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import { ReadonlyFieldControlObject } from '../../readonly-field-control-object';
import type { FieldComponentProps, FieldKey } from '../../types';
import type { LinkDecoratorProperties } from './link-types';
import type { QueryParameters } from '../../../utils/types';

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a link to another page
 */
export class LinkControlObject<CT extends ScreenExtension<CT> = ScreenBase> extends ReadonlyFieldControlObject<
    CT,
    FieldKey.Link,
    FieldComponentProps<FieldKey.Link>
> {
    @ControlObjectProperty<LinkDecoratorProperties<CT>, LinkControlObject<CT>>()
    /** The page to navigate to on link click */
    page?: string;

    @ControlObjectProperty<LinkDecoratorProperties<CT>, LinkControlObject<CT>>()
    queryParameters?: QueryParameters;

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }
}
