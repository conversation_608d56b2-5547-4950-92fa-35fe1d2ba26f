/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ClickableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { LinkControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { LinkDecoratorProperties } from './link-types';

class LinkDecorator extends AbstractFieldDecorator<FieldKey.Link> {
    protected _controlObjectConstructor = LinkControlObject;

    protected _layout = AbstractFieldLayoutBuilder;
}

/**
 * Initializes the decorated member as a [Link]{@link LinkControlObject} field with the provided properties
 *
 * @param properties The properties that the [Link]{@link LinkControlObject} field will be initialized with
 */
export function linkField<T extends ScreenExtension<T>>(
    properties: LinkDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Link>(properties, LinkDecorator, FieldKey.Link);
}

export function linkFieldOverride<T extends ScreenExtension<T>>(
    properties: ClickableOverrideDecoratorProperties<LinkDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Link>(properties);
}
