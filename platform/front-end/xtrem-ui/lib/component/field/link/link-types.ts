import type { ScreenBase } from '../../../service/screen-base';
import type { ReadonlyFieldProperties } from '../../readonly-field-control-object';
import type { BaseReadonlyComponentProperties, NestedFieldsAdditionalProperties } from '../field-base-component-types';
import type { FieldControlObjectInstance } from '../../types';
import type {
    CanBeMandatory,
    Clickable,
    ExtensionField,
    HasDynamicIcon,
    HasDynamicNestedIcon,
    HasParent,
    Mappable,
    Nested,
    NestedClickable,
    Sizable,
} from '../traits';
import type { BlockControlObject } from '../../control-objects';
import type { ClientNode } from '@sage/xtrem-client';
import type { QueryParameters, ValueOrCallbackWithFieldValue } from '../../../utils/types';

export interface LinkProperties<CT extends ScreenBase = ScreenBase> extends ReadonlyFieldProperties<CT> {}

export interface LinkDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<LinkProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        Mappable<CT>,
        Sizable,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT>>,
        HasDynamicIcon<CT> {
    /** The page to navigate to on link click */
    page?: ValueOrCallbackWithFieldValue<CT, string, void>;
    queryParameters?: ValueOrCallbackWithFieldValue<CT, QueryParameters, string, void>;
}

export interface NestedLinkProperties<CT extends ScreenBase = ScreenBase, ContextNodeType extends ClientNode = any>
    extends Omit<LinkProperties<CT>, 'bind'>,
        NestedClickable<CT, ContextNodeType>,
        Mappable<CT>,
        Nested<ContextNodeType>,
        Sizable,
        CanBeMandatory<CT, ContextNodeType>,
        HasDynamicNestedIcon<CT, ContextNodeType> {
    /** The page to navigate to on link click */
    page?: ValueOrCallbackWithFieldValue<CT, string, ContextNodeType>;
    queryParameters?: ValueOrCallbackWithFieldValue<CT, QueryParameters, string, ContextNodeType>;
}

export type LinkComponentProps = BaseReadonlyComponentProperties<
    LinkProperties,
    string,
    NestedFieldsAdditionalProperties
>;
