import * as React from 'react';
import * as xtremRedux from '../../../redux';
import { REG_EXP_URL_PATTERN } from '../../../utils/constants';
import { triggerNestedFieldEvent } from '../../../utils/events';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { getArtifactDescription, splitValueToMergedValue } from '../../../utils/transformers';
import type { NestedLinkProperties } from '../../nested-fields-properties';
import type { CellParams } from '../../../utils/ag-grid/ag-grid-column-config';
import { getPage, getQueryParameters, getTextLink } from './link-utils';
import Icon from 'carbon-react/esm/components/icon';
import { navigationPanelId } from '../../container/navigation-panel/navigation-panel-types';

export const LinkCellRenderer: React.FC<CellParams<NestedLinkProperties>> = React.memo(
    ({
        api,
        colDef,
        column,
        data,
        eGridCell,
        fieldProperties,
        isParentFieldDisabled,
        node,
        screenId,
        value,
        tableElementId,
        ...rest
    }) => {
        const linkRef = React.useRef<React.ElementRef<'a'> | null>(null);

        const onFocus = React.useCallback(() => {
            linkRef.current?.focus();
        }, []);

        React.useEffect(() => {
            eGridCell.addEventListener('focus', onFocus);
            return (): void => {
                eGridCell.removeEventListener('focus', onFocus);
            };
        }, [eGridCell, onFocus]);

        const icon = React.useMemo(
            () =>
                resolveByValue({
                    screenId,
                    propertyValue: fieldProperties.icon,
                    rowValue: splitValueToMergedValue(data),
                    fieldValue: value,
                    skipHexFormat: true,
                }),
            [data, fieldProperties.icon, screenId, value],
        );

        const iconColor = React.useMemo(
            () =>
                resolveByValue({
                    screenId,
                    propertyValue: fieldProperties.iconColor,
                    rowValue: splitValueToMergedValue(data),
                    fieldValue: value,
                    skipHexFormat: true,
                }),
            [data, fieldProperties.iconColor, screenId, value],
        );

        const isDisabled = React.useMemo(
            () =>
                isParentFieldDisabled ||
                resolveByValue({
                    screenId,
                    propertyValue: fieldProperties.isDisabled,
                    rowValue: splitValueToMergedValue(data),
                    fieldValue: value,
                    skipHexFormat: true,
                }),
            [data, fieldProperties.isDisabled, isParentFieldDisabled, screenId, value],
        );

        const onClick = React.useCallback<React.MouseEventHandler<HTMLAnchorElement>>(
            async e => {
                e.preventDefault();
                e.stopPropagation();
                const modifierKeyPushed = e.metaKey || e.ctrlKey;

                const page = getPage(screenId, fieldProperties, value, data);
                if (!page) {
                    await triggerNestedFieldEvent(
                        screenId,
                        colDef?.cellRendererParams.tableElementId,
                        fieldProperties,
                        'onClick',
                        data._id,
                        splitValueToMergedValue(data),
                    );
                    return;
                }
                if (page.match(REG_EXP_URL_PATTERN)) {
                    // External link, opening in a new tab
                    window.open(page, '_blank', 'noopener=true');
                } else if (modifierKeyPushed) {
                    window.open(getTextLink(screenId, fieldProperties, value, data), '_blank', 'noopener=true');
                } else if (tableElementId !== navigationPanelId || getArtifactDescription(page).name !== screenId) {
                    // If the link is on the navigation panel and the screen is the same as the current screen, we don't want to navigate with a proper navigation event
                    await (xtremRedux.getStore().dispatch as xtremRedux.AppThunkDispatch)(
                        xtremRedux.actions.navigate(page, getQueryParameters(screenId, fieldProperties, value, data)),
                    );
                }
            },
            [colDef?.cellRendererParams.tableElementId, data, fieldProperties, screenId, tableElementId, value],
        );

        return (
            <fieldProperties.wrapper
                api={api}
                colDef={colDef}
                column={column}
                data={data}
                eGridCell={eGridCell}
                fieldProperties={fieldProperties}
                isParentFieldDisabled={isParentFieldDisabled}
                node={node}
                screenId={screenId}
                value={value}
                tableElementId={tableElementId}
                {...rest}
            >
                {icon && <Icon type={icon} color={iconColor} />}
                {!isDisabled && (
                    <a
                        ref={linkRef}
                        className="e-link-cell-renderer-anchor"
                        data-testid="e-link-cell-value"
                        href={getTextLink(screenId, fieldProperties, value, data)}
                        onClick={onClick}
                    >
                        {value}
                    </a>
                )}
                {isDisabled && <span data-testid="e-link-cell-value">{value}</span>}
            </fieldProperties.wrapper>
        );
    },
);

LinkCellRenderer.displayName = 'LinkCellRenderer';
