import * as React from 'react';
import Loader from 'carbon-react/esm/components/loader';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { LinkComponentProps } from './link-types';

const ConnectedLinkComponent = React.lazy(() => import('./link-component'));

export function AsyncConnectedLinkComponent(props: FieldComponentExternalProperties): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader />}>
            <ConnectedLinkComponent {...props} />
        </React.Suspense>
    );
}

const LinkComponent = React.lazy(() => import('./link-component').then(c => ({ default: c.LinkComponent })));

export function AsyncLinkComponent(props: LinkComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<Loader size="small" />}>
            <LinkComponent {...props} />
        </React.Suspense>
    );
}
