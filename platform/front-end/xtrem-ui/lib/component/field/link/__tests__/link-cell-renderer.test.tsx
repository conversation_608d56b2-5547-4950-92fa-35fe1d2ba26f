import { getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';

import type { CellParams } from '../../../../utils/ag-grid/ag-grid-column-config';
import { FieldKey } from '../../../types';
import type { NestedLinkProperties } from '../link-types';
import * as React from 'react';
import { render, cleanup } from '@testing-library/react';
import { LinkCellRenderer } from '../link-cell-renderer';
import '@testing-library/jest-dom';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type * as xtremRedux from '../../../../redux';
import { Provider } from 'react-redux';

describe('link cell renderer', () => {
    const screenId = 'TestPage';
    let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;
    let props: CellParams<NestedLinkProperties>;
    beforeEach(() => {
        const state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);

        const eGridCell = document.createElement('div');
        mockStore = getMockStore(state);
        props = {
            columnId: 'test-column-id',
            api: {
                getColumns: jest.fn(() => [
                    {
                        field: 'test-column-id',
                    },
                ]),
                getFocusedCell: () => eGridCell,
            } as any,
            screenId,
            value: 'http://bbc.co.uk',
            fieldProperties: {
                _controlObjectType: FieldKey.Link,
                title: 'Test title',
                bind: 'link',
                wrapper: ({ children }) => <div>{children}</div>,
            },
            eGridCell,
            data: {
                _id: '4',
                link: 'http://bbc.co.uk/',
                title: 'Test title',
                icon: 'arrow_up',
            },
            isParentFieldDisabled: false,
        } as unknown as CellParams<NestedLinkProperties>;
    });

    afterEach(() => {
        cleanup();
    });

    it('should render simple link cell', () => {
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <LinkCellRenderer {...props} />
            </Provider>,
        );
        const linkElement = queryByTestId('e-link-cell-value');
        expect(linkElement).toHaveTextContent(props.value);
        expect(linkElement).toHaveAttribute('href', '#');
        expect(linkElement?.tagName).toEqual('A');
    });
    it('should render disabled link cell', () => {
        props.fieldProperties.isDisabled = true;
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <LinkCellRenderer {...props} />
            </Provider>,
        );
        const linkElement = queryByTestId('e-link-cell-value');
        expect(linkElement).toHaveTextContent(props.value);
        expect(linkElement?.tagName).toEqual('SPAN');
    });

    it('should render callback disabled link cell', () => {
        props.fieldProperties.isDisabled = (_id, rowValue) => {
            return rowValue?.title === 'Test title';
        };
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <LinkCellRenderer {...props} />
            </Provider>,
        );
        const linkElement = queryByTestId('e-link-cell-value');
        expect(linkElement).toHaveTextContent(props.value);
        expect(linkElement?.tagName).toEqual('SPAN');
    });

    it('should render link cell with page reference', () => {
        props.fieldProperties.page = 'https://www.sage.com/';
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <LinkCellRenderer {...props} />
            </Provider>,
        );
        const linkElement = queryByTestId('e-link-cell-value');
        expect(linkElement).toHaveTextContent(props.value);
        expect(linkElement).toHaveAttribute('href', 'https://www.sage.com/');
    });

    it('should render link cell with page callback', () => {
        props.fieldProperties.page = (_id, rowValue) => {
            return rowValue?.link;
        };
        const { queryByTestId } = render(
            <Provider store={mockStore}>
                <LinkCellRenderer {...props} />
            </Provider>,
        );
        const linkElement = queryByTestId('e-link-cell-value');
        expect(linkElement).toHaveTextContent(props.value);
        expect(linkElement).toHaveAttribute('href', props.data.link);
    });

    it('should render link cell with icon', () => {
        props.fieldProperties.icon = 'arrow_down';
        const { baseElement } = render(
            <Provider store={mockStore}>
                <LinkCellRenderer {...props} />
            </Provider>,
        );
        const iconElement = baseElement.querySelector('[data-component="icon"]');
        expect(iconElement).toBeInTheDocument();
        expect(iconElement).toHaveAttribute('type', 'arrow_down');
    });

    it('should render link cell with icon  callback', () => {
        props.fieldProperties.icon = (_id, rowValue) => {
            return rowValue?.icon;
        };
        const { baseElement } = render(
            <Provider store={mockStore}>
                <LinkCellRenderer {...props} />
            </Provider>,
        );
        const iconElement = baseElement.querySelector('[data-component="icon"]');
        expect(iconElement).toBeInTheDocument();
        expect(iconElement).toHaveAttribute('type', 'arrow_up');
    });
});
