import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { LinkDecoratorProperties } from '../link-types';
import { linkField } from '../link-decorator';

describe('Link decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;
    let onClickMock: jest.Mock;

    beforeEach(() => {
        fieldId = 'linkField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
        onClickMock = jest.fn();
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        it('should set values when component properties provided', () => {
            linkField({
                onClick: onClickMock,
                page: 'testUrl',
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties = pageMetadata.uiComponentProperties[
                fieldId
            ] as LinkDecoratorProperties<Page>;
            expect(mappedComponentProperties.page).toBe('testUrl');
            expect(mappedComponentProperties.onClick).toBe(onClickMock);
        });

        it('should assign onClick handler', () => {
            testOnClickHandler(linkField, pageMetadata, fieldId);
        });
    });
});
