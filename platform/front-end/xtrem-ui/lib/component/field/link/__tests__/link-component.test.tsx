import { addFieldToState, getMockPageDefinition, getMockState, getMockStore } from '../../../../__tests__/test-helpers';
import * as router from '../../../../service/router';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import { FieldKey } from '../../../types';
import type { XtremAppState } from '../../../../redux';
import { ConnectedLinkComponent, LinkComponent } from '../link-component';
import type { LinkDecoratorProperties } from '../link-types';
import type { Dict } from '@sage/xtrem-shared';
import { fireEvent, render } from '@testing-library/react';

describe('LinkField', () => {
    const screenId = 'TestPage';
    let navigateMock: jest.Mock<any, any> | null = null;

    let mockFieldProperties: LinkDecoratorProperties;
    const linkPage = '@sage/x3-show-case/AnyPage';

    beforeEach(() => {
        navigateMock = jest.fn();
        jest.spyOn(router, 'getRouter').mockImplementation(() => {
            return {
                goTo: navigateMock,
            } as any;
        });
        mockFieldProperties = {
            title: 'Test Link',
            page: linkPage,
        };
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<XtremAppState>;

        beforeEach(() => {
            const state = getMockState();
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);
            addFieldToState(FieldKey.Link, state, screenId, 'test-link-field', mockFieldProperties, null);
            addFieldToState(FieldKey.Link, state, screenId, 'test-empty-link-field', mockFieldProperties, null);
            mockStore = getMockStore(state);
        });

        describe('Snapshots', () => {
            it('should render with default properties', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLinkComponent screenId={screenId} elementId="test-link-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render without CarbonWrapper if is nested', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLinkComponent screenId={screenId} elementId="test-link-field" isNested={true} />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render hidden', () => {
                mockFieldProperties.isHidden = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLinkComponent screenId={screenId} elementId="test-link-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with full-width', () => {
                mockFieldProperties.isFullWidth = true;
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLinkComponent screenId={screenId} elementId="test-link-field" />
                    </Provider>,
                );
                expect(container).toMatchSnapshot();
            });

            it('should render with a title', () => {
                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLinkComponent screenId={screenId} elementId="test-link-field" />
                    </Provider>,
                );
                const title = container.querySelector('[data-element="label"]');
                expect(title).toHaveTextContent('Test Link');
            });

            it('should render with a hidden title', () => {
                mockFieldProperties.isTitleHidden = true;

                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLinkComponent screenId={screenId} elementId="test-link-field" />
                    </Provider>,
                );
                const title = container.querySelector('[data-element="label"]');
                expect(title).toBeNull();
            });

            it('should render with an icon', () => {
                mockFieldProperties.icon = 'add';

                const { container } = render(
                    <Provider store={mockStore}>
                        <ConnectedLinkComponent screenId={screenId} elementId="test-link-field" />
                    </Provider>,
                );
                const icon = container.querySelector('[data-component="icon"]');
                expect(icon).not.toBeNull();
            });
        });
    });

    describe('unconnected', () => {
        let windowOpenMock: jest.Mock<any, any> | null = null;
        beforeEach(() => {
            windowOpenMock = jest.fn();
            window.open = windowOpenMock as any;
        });

        it('should navigate internally when a page is set to the page property', () => {
            const { container } = render(
                <LinkComponent
                    screenId={screenId}
                    elementId="test-link-field"
                    fieldProperties={mockFieldProperties}
                    onFocus={jest.fn()}
                    locale="en-US"
                />,
            );
            expect(navigateMock).not.toHaveBeenCalled();
            expect(windowOpenMock).not.toHaveBeenCalled();

            fireEvent.click(container.querySelector('a')!);

            expect(windowOpenMock).not.toHaveBeenCalled();
            expect(navigateMock).toHaveBeenCalledTimes(1);
            expect(navigateMock).toHaveBeenCalledWith(linkPage, undefined);
        });

        it('should navigate internally when a page is set to the page callback property', () => {
            mockFieldProperties.page = () => '@sage/some-package/Page';
            const { container } = render(
                <LinkComponent
                    screenId={screenId}
                    elementId="test-link-field"
                    fieldProperties={mockFieldProperties}
                    onFocus={jest.fn()}
                    locale="en-US"
                />,
            );
            expect(navigateMock).not.toHaveBeenCalled();
            expect(windowOpenMock).not.toHaveBeenCalled();

            fireEvent.click(container.querySelector('a')!);

            expect(windowOpenMock).not.toHaveBeenCalled();
            expect(navigateMock).toHaveBeenCalledTimes(1);
            expect(navigateMock).toHaveBeenCalledWith('@sage/some-package/Page', undefined);
        });

        it('should navigate internally and pass on the query parameter', () => {
            mockFieldProperties.queryParameters = { myTestParam: true };
            const { container } = render(
                <LinkComponent
                    screenId={screenId}
                    elementId="test-link-field"
                    fieldProperties={mockFieldProperties}
                    value="1234"
                    onFocus={jest.fn()}
                    locale="en-US"
                />,
            );

            fireEvent.click(container.querySelector('a')!);

            expect(navigateMock).toHaveBeenCalledWith(linkPage, { myTestParam: true });
        });

        it('should navigate internally and pass on the value as the requested query parameter when they are defined as a callback', () => {
            mockFieldProperties.queryParameters = (value: string): Dict<string> => ({
                myTestParam: value,
            });
            const { container } = render(
                <LinkComponent
                    screenId={screenId}
                    elementId="test-link-field"
                    fieldProperties={mockFieldProperties}
                    value="1234"
                    onFocus={jest.fn()}
                    locale="en-US"
                />,
            );

            fireEvent.click(container.querySelector('a')!);

            expect(navigateMock).toHaveBeenCalledWith(linkPage, { myTestParam: '1234' });
        });

        it('should open a new tab when the link is external', () => {
            mockFieldProperties.page = 'http://www.bbc.co.uk/';
            const { container } = render(
                <LinkComponent
                    screenId={screenId}
                    elementId="test-link-field"
                    fieldProperties={mockFieldProperties}
                    onFocus={jest.fn()}
                    locale="en-US"
                />,
            );
            expect(navigateMock).not.toHaveBeenCalled();
            expect(windowOpenMock).not.toHaveBeenCalled();

            fireEvent.click(container.querySelector('a')!);

            expect(navigateMock).not.toHaveBeenCalled();
            expect(windowOpenMock).toHaveBeenCalledTimes(1);
            expect(windowOpenMock).toHaveBeenCalledWith(mockFieldProperties.page, '_blank', 'noopener=true');
        });

        it('should not render a button when the link has parentElementId as $navigationPanel', () => {
            const { container } = render(
                <LinkComponent
                    parentElementId="$navigationPanel"
                    isNested={true}
                    screenId={screenId}
                    elementId="test-link-field"
                    fieldProperties={mockFieldProperties}
                    onFocus={jest.fn()}
                    locale="en-US"
                />,
            );
            expect(container.querySelector('a')).toBeNull();
        });

        it('should render a button when the link has parentElementId as $navigationPanel but the screenId is $dashboard', () => {
            const { container } = render(
                <LinkComponent
                    parentElementId="$navigationPanel"
                    isNested={true}
                    screenId="$dashboard"
                    elementId="test-link-field"
                    fieldProperties={mockFieldProperties}
                    onFocus={jest.fn()}
                    locale="en-US"
                />,
            );
            expect(container.querySelector('a')).not.toBeNull();
        });
    });
});
