import { <PERSON><PERSON><PERSON> } from '../../../types';
import { createFieldControlObject } from '../../../../__tests__/test-helpers';
import { LinkControlObject } from '../../../control-objects';

describe('Link Field', () => {
    const screenId = 'TestPage';
    let linkFieldControlObject: LinkControlObject;
    const linkPage = 'https://www.google.com';
    const linkValue = 'parameterValue';

    beforeEach(() => {
        linkFieldControlObject = createFieldControlObject<FieldKey.Link>(
            FieldKey.Link,
            screenId,
            LinkControlObject,
            'test',
            linkValue,
            {
                title: 'TEST_FIELD_TITLE',
                isHidden: true,
                isDisabled: true,
                page: linkPage,
            },
        );
    });

    it('should retrieve the link page', () => {
        expect(linkFieldControlObject.page).toEqual(linkPage);
    });

    it('should return a string link', () => {
        expect(typeof linkFieldControlObject.page).toBe('string');
    });
});
