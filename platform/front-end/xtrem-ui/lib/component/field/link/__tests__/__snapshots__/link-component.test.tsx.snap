// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LinkField connected Snapshots should render hidden 1`] = `
.c0 > a,
.c0 > button {
  font-size: var(--fontSizes100);
  color: var(--colorsActionMajor500);
}

.c0 > a:hover,
.c0 > button:hover {
  color: var(--colorsActionMajor600);
}

.c0 > a:focus,
.c0 > button:focus {
  background-color: var(--colorsSemanticFocus250);
  border-radius: var(--borderRadius025);
}

.c0 > a:any-link:hover,
.c0 > button:hover {
  cursor: pointer;
}

.c0 > a,
.c0 > button {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.c0 > a:focus,
.c0 > button:focus {
  color: var(--colorsActionMajorYin090);
  outline: none;
}

<div>
  <div
    class="e-field e-link-field e-hidden"
    data-label="Test Link"
    data-testid="e-link-field e-field-label-testLink e-field-bind-test-link-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Link
    </label>
    <span
      class="c0"
      data-component="link"
    >
      <a
        data-role="link-anchor"
        href="@sage/x3-show-case/AnyPage"
      >
        <span
          class=""
        />
      </a>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`LinkField connected Snapshots should render with default properties 1`] = `
.c0 > a,
.c0 > button {
  font-size: var(--fontSizes100);
  color: var(--colorsActionMajor500);
}

.c0 > a:hover,
.c0 > button:hover {
  color: var(--colorsActionMajor600);
}

.c0 > a:focus,
.c0 > button:focus {
  background-color: var(--colorsSemanticFocus250);
  border-radius: var(--borderRadius025);
}

.c0 > a:any-link:hover,
.c0 > button:hover {
  cursor: pointer;
}

.c0 > a,
.c0 > button {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.c0 > a:focus,
.c0 > button:focus {
  color: var(--colorsActionMajorYin090);
  outline: none;
}

<div>
  <div
    class="e-field e-link-field"
    data-label="Test Link"
    data-testid="e-link-field e-field-label-testLink e-field-bind-test-link-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Link
    </label>
    <span
      class="c0"
      data-component="link"
    >
      <a
        data-role="link-anchor"
        href="@sage/x3-show-case/AnyPage"
      >
        <span
          class=""
        />
      </a>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`LinkField connected Snapshots should render with full-width 1`] = `
.c0 > a,
.c0 > button {
  font-size: var(--fontSizes100);
  color: var(--colorsActionMajor500);
}

.c0 > a:hover,
.c0 > button:hover {
  color: var(--colorsActionMajor600);
}

.c0 > a:focus,
.c0 > button:focus {
  background-color: var(--colorsSemanticFocus250);
  border-radius: var(--borderRadius025);
}

.c0 > a:any-link:hover,
.c0 > button:hover {
  cursor: pointer;
}

.c0 > a,
.c0 > button {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.c0 > a:focus,
.c0 > button:focus {
  color: var(--colorsActionMajorYin090);
  outline: none;
}

<div>
  <div
    class="e-field e-link-field e-full-width"
    data-label="Test Link"
    data-testid="e-link-field e-field-label-testLink e-field-bind-test-link-field"
  >
    <label
      class="common-input__label"
      data-element="label"
      data-testid="e-field-label"
    >
      Test Link
    </label>
    <span
      class="c0"
      data-component="link"
    >
      <a
        data-role="link-anchor"
        href="@sage/x3-show-case/AnyPage"
      >
        <span
          class=""
        />
      </a>
    </span>
    <span
      class="common-input__help-text"
      data-element="help"
      data-testid="e-field-helper-text"
    >
       
    </span>
  </div>
</div>
`;

exports[`LinkField connected Snapshots should render without CarbonWrapper if is nested 1`] = `
.c0 > a,
.c0 > button {
  font-size: var(--fontSizes100);
  color: var(--colorsActionMajor500);
}

.c0 > a:hover,
.c0 > button:hover {
  color: var(--colorsActionMajor600);
}

.c0 > a:focus,
.c0 > button:focus {
  background-color: var(--colorsSemanticFocus250);
  border-radius: var(--borderRadius025);
}

.c0 > a:any-link:hover,
.c0 > button:hover {
  cursor: pointer;
}

.c0 > a,
.c0 > button {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.c0 > a:focus,
.c0 > button:focus {
  color: var(--colorsActionMajorYin090);
  outline: none;
}

<div>
  <div
    class="e-field e-link-field"
    data-label="Test Link"
    data-nested="true"
    data-testid="e-link-field e-field-label-testLink e-field-bind-test-link-field"
  >
    <span
      class="c0"
      data-component="link"
    >
      <a
        data-role="link-anchor"
        href="@sage/x3-show-case/AnyPage"
      >
        <span
          class=""
        />
      </a>
    </span>
  </div>
</div>
`;
