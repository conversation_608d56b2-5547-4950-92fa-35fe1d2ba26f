import { type LocalizeLocale, deepMerge } from '@sage/xtrem-shared';
import { get, isEqual, isNil, set } from 'lodash';
import * as React from 'react';
import * as xtremRedux from '../../redux';
import { runAndDispatchFieldValidation } from '../../service/dispatch-service';
import type { ScreenBase } from '../../service/screen-base';
import type { ValidationResult } from '../../service/screen-base-definition';
import { getScreenElement } from '../../service/screen-base-definition';
import { ContextType } from '../../types';
import { handleChange } from '../../utils/abstract-fields-utils';
import { getComponentClass, getDataTestIdAttribute } from '../../utils/dom';
import { triggerFieldEvent, triggerNestedFieldEvent } from '../../utils/events';
import { convertDeepBindToPathNotNull } from '../../utils/nested-field-utils';
import { resolveByValue } from '../../utils/resolve-value-utils';
import { splitValueToMergedValue } from '../../utils/transformers';
import type { ValueOrCallbackKeysExtractor, ValueOrCallbackReturnExtractor } from '../../utils/types';
import type { UiComponentProperties } from '../abstract-ui-control-object';
import type { PageProperties } from '../control-objects';
import type { EditableFieldProperties } from '../editable-field-control-object';
import type { ErrorableFieldProperties } from '../errorable-field-control-object';
import type { NestedFieldsProperties } from '../nested-fields';
import type { ReadonlyFieldProperties } from '../readonly-field-control-object';
import type { FieldProps } from './carbon-helpers';
import { getFieldTitle, isFieldDisabled, isFieldReadOnly, onFocusHandler } from './carbon-helpers';
import type {
    BaseEditableComponentProperties,
    BaseErrorableComponentProperties,
    BaseReadonlyComponentProperties,
    EditableFieldComponentProperties,
    FieldBaseComponentProperties,
    FieldComponentExternalProperties,
    NestedFieldsAdditionalProperties,
    ReadonlyFieldComponentProperties,
} from './field-base-component-types';
import type { Clickable, Mappable } from './traits';

interface BaseAttributesDivWrapper {
    'data-testid': string;
    className: string;
}

export class FieldBaseComponent<
    CoreProps extends BaseReadonlyComponentProperties<FieldUiProperties, FieldValue, AdditionalProperties> &
        NestedFieldsAdditionalProperties,
    FieldUiProperties extends ReadonlyFieldProperties,
    FieldValue,
    AdditionalProperties = {},
    ComponentState = {},
> extends React.Component<CoreProps & NestedFieldsAdditionalProperties, ComponentState> {
    protected componentRef = React.createRef<HTMLDivElement>();

    shouldComponentUpdate(nextProps: CoreProps, nextState: any): boolean {
        if (this.constructor.name === 'CarbonWrapper') {
            return true;
        }

        const isMandatory = (this.props.fieldProperties as EditableFieldProperties).isMandatory ?? false;
        const validationErrors =
            (this.props as unknown as EditableFieldComponentProperties<FieldUiProperties, FieldValue>)
                .validationErrors ?? false;

        if (!isEqual(this.state, nextState)) {
            return true;
        }

        if (!isEqual(this.props.value, nextProps.value)) {
            return true;
        }

        if (!isEqual(this.props.fieldProperties, nextProps.fieldProperties)) {
            return true;
        }

        if (this.props.isInFocus !== nextProps.isInFocus) {
            return true;
        }

        if (this.props.isParentReadOnly !== nextProps.isParentReadOnly) {
            return true;
        }
        if (!isEqual(this.props.recordContext, nextProps.recordContext)) {
            return true;
        }

        // Trigger updates on detail-panel-component when validation is updated:
        if (isMandatory && !this.props.value) {
            return true;
        }
        if (
            !isEqual(
                validationErrors,
                (nextProps as unknown as EditableFieldComponentProperties<FieldUiProperties, FieldValue>)
                    .validationErrors,
            )
        ) {
            return true;
        }

        return false;
    }

    componentDidUpdate(prevProps: CoreProps): void {
        // TODO: This can be probably replaced by Carbon 12's autofocus
        const previousProps = prevProps as BaseReadonlyComponentProperties<any, any>;
        const currentProps = this.props as BaseReadonlyComponentProperties<any, any>;
        // TODO: Sometimes the 'CarbonWrapper' triggers the update instead of the actual component (i.e. reference)
        const componentRef =
            this.constructor.name === 'CarbonWrapper'
                ? (this.props as any)?.componentRef?.current
                : this.componentRef.current;
        if (!previousProps.isInFocus && currentProps.isInFocus && componentRef) {
            const elementInFocus = document.activeElement;
            const input = this.getFocusableElement(componentRef);
            if (input && elementInFocus !== input) {
                input.focus();
                onFocusHandler(currentProps as FieldProps);
            }
        }
    }

    /**
     * @deprecated Use handleChange from abstract-fields-utils instead of this.
     */
    public handleChange(
        elementId: string,
        value: any, // TODO Type this properly
        setFieldValue: (bind: string, value: any) => Promise<void>,
        validate?: (bind: string, value: any) => Promise<ValidationResult[] | undefined>,
        onChange?: () => void,
    ): void {
        handleChange(elementId, value, setFieldValue, validate, onChange);
    }

    protected getResolvedProperty<K extends ValueOrCallbackKeysExtractor<FieldUiProperties>>(
        key: K,
        skipHexFormat = true,
    ): ValueOrCallbackReturnExtractor<FieldUiProperties, K> {
        return resolveByValue({
            screenId: this.props.screenId,
            propertyValue: this.props.fieldProperties[key],
            skipHexFormat,
            rowValue: this.props.handlersArguments?.rowValue
                ? splitValueToMergedValue(this.props.handlersArguments?.rowValue)
                : null,
            fieldValue: null,
        });
    }

    /**
     * 'data-label' is used for alignment purposes only
     *
     * @private
     * @memberof FieldBaseComponent
     */
    private readonly getDataLabel = ({
        contextType,
        title,
    }: {
        contextType?: ContextType;
        title?: string;
    }):
        | {
              'data-label': string;
          }
        | undefined => {
        if (contextType === ContextType.navigationPanel || (!this.props.fieldProperties.isTitleHidden && title)) {
            return { 'data-label': title ?? '' };
        }
        return undefined;
    };

    private readonly getComponentClass = (specificClassNames: string, rowValue?: any): string => {
        return getComponentClass(
            // TS 5.2 replaced FieldValue by any
            // this.props as unknown as BaseEditableComponentProperties<FieldUiProperties, FieldValue>,
            this.props as unknown as BaseEditableComponentProperties<FieldUiProperties, any>,
            specificClassNames,
            rowValue,
        );
    };

    getBaseAttributesDivWrapper = (
        componentName: string,
        specificClassNames: string,
        contextType?: ContextType,
        rowValue?: any,
        isNested = false,
    ): BaseAttributesDivWrapper => {
        const title = this.getTitle();
        return {
            'data-testid': getDataTestIdAttribute(componentName, title, this.props.elementId),
            className: this.getComponentClass(specificClassNames, rowValue),
            ...this.getDataLabel({ contextType, title }),
            ...(isNested && { 'data-nested': 'true' }),
        };
    };

    getFocusableElement(fieldContainer: HTMLElement): HTMLElement | null {
        return fieldContainer.querySelector('input');
    }

    getValue = (): FieldValue | undefined => {
        const map = (this.props.fieldProperties as Mappable<ScreenBase>).map;
        if (map) {
            return resolveByValue({
                screenId: this.props.screenId,
                fieldValue: this.props.value,
                rowValue: this.props.handlersArguments?.rowValue
                    ? splitValueToMergedValue(this.props.handlersArguments?.rowValue)
                    : this.props.handlersArguments?.rowValue,
                skipHexFormat: true,
                propertyValue: map,
            });
        }
        return this.props.value;
    };

    getClickHandler = () => (): void => {
        const nestedFieldsAdditionalProperties = this.props as NestedFieldsAdditionalProperties;

        if (nestedFieldsAdditionalProperties.isNested) {
            triggerNestedFieldEvent(
                this.props.screenId,
                nestedFieldsAdditionalProperties.parentElementId || this.props.elementId,
                this.props.fieldProperties as NestedFieldsProperties<any>,
                'onClick',
                ...(nestedFieldsAdditionalProperties.handlersArguments!.onClick as [
                    rowId: string,
                    rowData: any,
                    level?: number,
                    ancestorIds?: string[],
                ]),
            );
        } else {
            triggerFieldEvent(this.props.screenId, this.props.elementId, 'onClick');
        }
    };

    getTitle(): string {
        return getFieldTitle(
            this.props.screenId,
            this.props.fieldProperties as UiComponentProperties,
            this.props.handlersArguments?.rowValue,
        );
    }

    triggerChangeListener = (newValue?: any): void => {
        const nestedFieldsAdditionalProperties = this.props as NestedFieldsAdditionalProperties;

        if (nestedFieldsAdditionalProperties.isNested) {
            // Merge rowValue with new value from change event to ensure latest changes are sent to listeners
            const rowValue =
                newValue !== undefined
                    ? deepMerge(
                          nestedFieldsAdditionalProperties.handlersArguments!.rowValue,
                          set(
                              {},
                              convertDeepBindToPathNotNull(
                                  nestedFieldsAdditionalProperties.columnDefinition?.properties?.bind ||
                                      this.props.fieldProperties?.bind ||
                                      this.props.elementId,
                              ),
                              newValue,
                          ),
                      )
                    : nestedFieldsAdditionalProperties.handlersArguments?.rowValue;
            triggerNestedFieldEvent(
                this.props.screenId,
                nestedFieldsAdditionalProperties.parentElementId || this.props.elementId,
                this.props.fieldProperties as NestedFieldsProperties<any>,
                'onChange',
                rowValue?._id,
                rowValue,
            );
        } else {
            triggerFieldEvent(this.props.screenId, this.props.elementId, 'onChange');
        }
    };

    isDisabled(): boolean {
        return (
            Boolean(this.props.isParentDisabled) ||
            isFieldDisabled(
                this.props.screenId,
                this.props.fieldProperties,
                this.props.value,
                this.props.handlersArguments?.rowValue,
            )
        );
    }
}

const shouldFieldBaseUpdate = (props: any, nextProps: any, parentShouldComponentUpdate: boolean): boolean => {
    if (parentShouldComponentUpdate) {
        return true;
    }

    if (!isEqual(props.validationErrors, nextProps.validationErrors)) {
        return true;
    }

    if (props.isInFocus !== nextProps.isInFocus) {
        return true;
    }

    // INFO: Rerender all fields nested in sidebar's to ensure callback function values are
    //       updated when they depend on another field's value (XT-50323).
    if (props.contextType === 'sidebar') {
        return true;
    }

    return false;
};

export class ReadonlyFieldBaseComponent<
    FieldUiProperties extends ReadonlyFieldProperties,
    FieldValue,
    AdditionalProperties = {},
    ComponentState = {},
> extends FieldBaseComponent<
    BaseReadonlyComponentProperties<FieldUiProperties, FieldValue, AdditionalProperties>,
    FieldUiProperties,
    FieldValue,
    AdditionalProperties,
    ComponentState
> {
    shouldComponentUpdate(
        nextProps: FieldBaseComponentProperties<FieldUiProperties, FieldValue, AdditionalProperties>,
        nextState: any,
    ): boolean {
        const parentShouldComponentUpdate: boolean = super.shouldComponentUpdate?.(nextProps, nextState);

        if (shouldFieldBaseUpdate(this.props, nextProps, parentShouldComponentUpdate)) {
            return true;
        }

        return false;
    }
}

export class EditableFieldBaseComponent<
    FieldUiProperties extends EditableFieldProperties,
    FieldValue,
    AdditionalProperties = {},
    ComponentState = {},
> extends FieldBaseComponent<
    BaseEditableComponentProperties<FieldUiProperties, FieldValue, AdditionalProperties>,
    FieldUiProperties,
    FieldValue,
    AdditionalProperties,
    ComponentState
> {
    shouldComponentUpdate(
        nextProps: FieldBaseComponentProperties<FieldUiProperties, FieldValue, AdditionalProperties>,
        nextState: any,
    ): boolean {
        const parentShouldComponentUpdate: boolean = super.shouldComponentUpdate?.(nextProps, nextState);

        if (shouldFieldBaseUpdate(this.props, nextProps, parentShouldComponentUpdate)) {
            return true;
        }

        return false;
    }

    isReadOnly(): boolean {
        return (
            Boolean(this.props.isParentReadOnly) ||
            isFieldReadOnly(
                this.props.screenId,
                this.props.fieldProperties,
                this.props.value,
                this.props.handlersArguments?.rowValue,
                this.props.contextType,
            )
        );
    }
}

export class ErrorableFieldBaseComponent<
    FieldUiProperties extends ErrorableFieldProperties,
    FieldValue,
    AdditionalProperties = {},
    ComponentState = {},
> extends FieldBaseComponent<
    BaseErrorableComponentProperties<FieldUiProperties, FieldValue, AdditionalProperties>,
    FieldUiProperties,
    FieldValue,
    AdditionalProperties,
    ComponentState
> {
    shouldComponentUpdate(
        nextProps: FieldBaseComponentProperties<FieldUiProperties, FieldValue, AdditionalProperties>,
        nextState: any,
    ): boolean {
        const parentShouldComponentUpdate: boolean = super.shouldComponentUpdate?.(nextProps, nextState);

        if (shouldFieldBaseUpdate(this.props, nextProps, parentShouldComponentUpdate)) {
            return true;
        }

        return false;
    }

    isReadOnly(): boolean {
        return isFieldReadOnly(
            this.props.screenId,
            this.props.fieldProperties,
            this.props.value,
            this.props.handlersArguments?.rowValue,
        );
    }
}

export const mapReadonlyStateToProps = () =>
    // eslint-disable-next-line func-names
    function (
        state: xtremRedux.XtremAppState,
        props: FieldComponentExternalProperties,
    ): ReadonlyFieldComponentProperties<UiComponentProperties, any> {
        const fieldProperties = state.screenDefinitions[props.screenId].metadata.uiComponentProperties[props.elementId];
        const screenElement = getScreenElement(state.screenDefinitions[props.screenId]);
        const fieldValue = state.screenDefinitions[props.screenId].values[props.elementId];
        const pageNode = (
            state.screenDefinitions[props.screenId].metadata.uiComponentProperties[props.screenId] as PageProperties
        ).node;

        const componentProperties: ReadonlyFieldComponentProperties<
            ReadonlyFieldProperties & Clickable<ScreenBase> & Mappable<ScreenBase>,
            any
        > = {
            // We always normalize undefined to null here
            value: isNil(fieldValue) ? null : fieldValue,
            browser: state.browser,
            nodeTypes: state.nodeTypes,
            pageNode,
            fieldProperties,
            locale: (state.applicationContext?.locale as LocalizeLocale) || 'base',
            isInFocus:
                !!state.focusPosition &&
                state.focusPosition.elementId === props.elementId &&
                state.focusPosition.screenId === props.screenId,
            onFocus: xtremRedux.actions.actionStub,
        };

        // TODO Define a Trait for headerActions
        /* eslint-disable */
        const headerActions = get<any, string>(fieldProperties, 'headerActions');
        if (fieldProperties && typeof headerActions === 'function') {
            set(componentProperties.fieldProperties, 'headerActionsMap', headerActions.apply(screenElement));
        }
        /* eslint-enable */

        return componentProperties;
    };

export const mapStateToProps = () =>
    // eslint-disable-next-line func-names
    function (
        state: xtremRedux.XtremAppState,
        props: FieldComponentExternalProperties,
    ): EditableFieldComponentProperties<UiComponentProperties, any> {
        return {
            ...mapReadonlyStateToProps()(state, props),
            validationErrors: state.screenDefinitions[props.screenId].errors[props.elementId],
            setFieldValue: xtremRedux.actions.actionStub,
            removeNonNestedErrors: xtremRedux.actions.actionStub,
            validate: xtremRedux.actions.actionStub,
            setFieldProperties: xtremRedux.actions.actionStub,
        };
    };

export const mapDispatchToProps = <Properties extends UiComponentProperties = UiComponentProperties>(
    callback?: (
        dispatch: xtremRedux.AppThunkDispatch,
        props: FieldComponentExternalProperties,
    ) => Partial<EditableFieldComponentProperties<Properties, any>>,
) =>
    // eslint-disable-next-line func-names
    function (
        dispatch: xtremRedux.AppThunkDispatch,
        props: FieldComponentExternalProperties,
    ): Partial<EditableFieldComponentProperties<Properties, any>> {
        let dispatchToProps = {
            setFieldValue: (elementId: string, value: any): Promise<void> =>
                dispatch(xtremRedux.actions.setFieldValue(props.screenId, elementId, value, true)),
            onFocus: (row?: string, nestedField?: string): void => {
                dispatch(xtremRedux.actions.setFocusPosition(props.screenId, props.elementId, row, nestedField));
            },
            validate: (elementId: string, value: any): Promise<ValidationResult[] | undefined> =>
                runAndDispatchFieldValidation(props.screenId, elementId, value),
            removeNonNestedErrors: (elementId: string): void => {
                dispatch(xtremRedux.actions.removeNonNestedErrors(props.screenId, elementId));
            },
            setFieldProperties: (elementId: string, value: Properties): void => {
                dispatch(xtremRedux.actions.setFieldProperties(props.screenId, elementId, value));
            },
        };
        if (callback) {
            dispatchToProps = {
                ...dispatchToProps,
                ...callback(dispatch, props),
            };
        }
        return dispatchToProps;
    };
