PATH: XTREEM/UI+Field+Widgets/Selection+Card+Field

## Introduction

Selection card fields are used to display a list of options with card buttons.

## Example:

With options coming as enum values from the server graphQL API.
```ts
@ui.decorators.selectionCardField<Radio>({
        title: 'Cards',
        parent() {
            return this.fieldBlock;
        },
        icon() {
            return 'apple';
        },
        description(option) {
            return option;
        },
        optionType: '@sage/xtrem-show-case/ShowCaseProductCategory',
    })
    field: ui.fields.SelectionCard;
```

Defined in the code.
```ts
@ui.decorators.selectionCardField<SelectionCard>({
        title: 'Cards',
        parent() {
            return this.fieldBlock;
        },
        icon() {
            return 'apple';
        },
        description(option) {
            return option;
        },
        options: ['This is option 1', 'This is option 2', 'This is option 3']
    })
    field: ui.fields.SelectionCard;
```


### Display decorator properties

-   **title**: The title that is displayed above the field. The title can be provided as a string, or a callback function returning a string. It is automatically picked up by the i18n engine and externalized for translation.
-   **helperText**: The helper text that is displayed below the field. It is automatically picked up by the i18n engine and externalized.
-   **icon**: The icon that is displayed inside the field.
-   **description**: The text that is displayed below the value inside the field.
-   **prefix**: A string that is displayed inside the field before the value, aligned to the left.
-   **postfix**: A string that is displayed inside the field after the value, aligned to the right.
-   **isTitleHidden**: Whether the field title above the field should be displayed and its vertical space preserved.
-   **isHelperTextHidden**: Whether the helper text underneath the field should be displayed and its vertical space preserved.
-   **isSortedAlphabetically**: Forces the options to be rendered in alphabetical order.
-   **options**: List of options to be displayed in the selectionCard buttons. It can also be defined as callback function that returns a string array.
-   **info**: Indicate additional warning message, rendered as tooltip and blue border. It can also be defined as callback function that returns a string.
-   **warning**: Indicate additional information, rendered as tooltip and orange border. It can also be defined as callback function that returns a string.
-   **hasFilter**: If set to true, a filter input is displayed above the cards which allow the user to filter the list of selection options.

### Binding decorator properties

-   **optionType**: GraphQL enum field used to get the list of options.
-   **bind**: The GraphQL object's property that the field's value is bound to. If not provided, the field's name is used.
-   **isTransient**: If marked as true, the field will be excluded from the automatic data binding process.
-   **isTransientInput**: If marked as true the field will be bound only to GraphQL mutations and excluded from all queries. Defaults to false.

### Event handler decorator properties

-   **onChange**: Triggered when the field value changed and the focus is about to move away from the field, no arguments provided.
-   **onError**: Handles errors thrown from the callback functions, more about error handling can be found [here](./Error+Handlers).

### Other decorator properties

-   **fetchesDefaults**: When set to true and when the selectionCard value changes, a request to the server for default values for the whole page will be requested. False by default.

## Runtime functions
-   **refresh()**: Refetches the field's value from the server and updates it on the screen, only for non-transient pages.
-   **focus()**: Moves the focus to the field.
-   **getNextField(isFocusable)**: Returns the next field instance. The order is calculated by the page prototype. If the isFocusable argument is set to true, it returns the next visible, enabled and non read-only field. It only considers the committed page state, so `commitValueAndPropertyChanges` call might be required beforehand to get the expected result.
-   **validate()**: Triggers the field validation rules. Since the validation rules might be asynchronous, this method returns a promise that must be awaited to get the validation result
-   **validateWithDetails()**: In addition to the functionality of `validate` it returns more details, including the rule that failed and where applicable, the row ID and colum ID.
-   **fetchDefault(skipSet)**: Force re-fetches default value for the field. If the `skipSet` flag is set to true, it returns the default values but not apply them to the screen.
-   **isDirty()**: Sets or gets the dirty state of the field.
-   **executeOnChange(executeErrorHandlers?: boolean)**: Programmatically triggers the field's `onChange` event handler. The optional parameter `executeErrorHandlers` (defaults to `false`) determines error handling behavior: when `true`, errors are delegated to application code; when `false`, errors are intercepted by the [error handling system](./Error+Handlers).

## Sandbox

Check out this field type on our sandbox server by clicking [this link](http://showcase.dev-sagextrem.com/@sage/xtrem-show-case/SelectionCard).
