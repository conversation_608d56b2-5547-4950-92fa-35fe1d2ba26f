import type { Dict } from '@sage/xtrem-shared';
import React from 'react';
import { connect } from 'react-redux';
import type { XtremAppState } from '../../../redux';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { getDataTestIdAttribute } from '../../../utils/dom';
import { triggerFieldEvent } from '../../../utils/events';
import { useFocus } from '../../../utils/hooks/effects/use-focus';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { addOptionsAndLocalizationToProps } from '../../../utils/transformers';
import { getFieldIndicatorStatus, getFieldTitle, isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import { HelperText, FieldLabel } from '../carbon-utility-components';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import type { SelectionCardComponentProps } from './selection-card-types';
import { resolveDetailedIcon } from '../../../utils/detailed-icons-utils';
import { SelectionCard } from '@sage/xtrem-ui-components';
import Search from 'carbon-react/esm/components/search';
import { localize } from '../../../service/i18n-service';

type SelectionCardAdditionalProps = SelectionCardComponentProps & {
    localizedOptions?: Dict<string>;
    enumOptions?: string[];
};

export function SelectionCardComponent(props: SelectionCardAdditionalProps): React.ReactElement {
    const [filterValue, setFilterValue] = React.useState<string>('');

    const componentRef = React.useRef<React.ElementRef<'button'>>(null);
    useFocus(componentRef, props.isInFocus, 'button');

    const getDescription = React.useCallback(
        (option: string) =>
            resolveByValue({
                propertyValue: props.fieldProperties.description,
                skipHexFormat: true,
                screenId: props.screenId,
                fieldValue: option,
                rowValue: null, // Nested selection-cards are not supported
            }),
        [props.fieldProperties.description, props.screenId],
    );

    const getTitle = React.useCallback(
        (option: string) =>
            props.localizedOptions && props.localizedOptions[option]
                ? props.localizedOptions[option]
                : props.fieldProperties.map?.apply({}, [option]) || option,
        [props.fieldProperties.map, props.localizedOptions],
    );

    const optionsToUse = React.useMemo(() => {
        const options =
            props.enumOptions ||
            resolveByValue({
                propertyValue: props.fieldProperties.options,
                skipHexFormat: true,
                screenId: props.screenId,
                fieldValue: props.value,
                rowValue: null, // Nested selection-cards are not supported
            }) ||
            [];

        const sortedOptions = props.fieldProperties.isSortedAlphabetically
            ? [...options].sort((a, b) => {
                  const aLabel: string =
                      props.localizedOptions && props.localizedOptions[a]
                          ? props.localizedOptions[a]
                          : props.fieldProperties.map?.apply({}, [a]) || a;
                  const bLabel: string =
                      props.localizedOptions && props.localizedOptions[b]
                          ? props.localizedOptions[b]
                          : props.fieldProperties.map?.apply({}, [b]) || b;
                  return aLabel.localeCompare(bLabel);
              })
            : options;

        return sortedOptions.filter(option => {
            if (!props.fieldProperties.hasFilter || !filterValue) {
                return true;
            }

            if (getDescription(option).toLowerCase().includes(filterValue.toLowerCase())) {
                return true;
            }
            if (getTitle(option).toLowerCase().includes(filterValue.toLowerCase())) {
                return true;
            }

            return false;
        });
    }, [
        filterValue,
        getDescription,
        getTitle,
        props.enumOptions,
        props.fieldProperties.hasFilter,
        props.fieldProperties.isSortedAlphabetically,
        props.fieldProperties.map,
        props.fieldProperties.options,
        props.localizedOptions,
        props.screenId,
        props.value,
    ]);

    const isMandatory = React.useMemo(
        () =>
            resolveByValue<boolean>({
                propertyValue: props.fieldProperties.isMandatory,
                skipHexFormat: true,
                screenId: props.screenId,
                fieldValue: props.value,
                rowValue: null, // Nested selection-cards are not supported
            }),
        [props.fieldProperties.isMandatory, props.screenId, props.value],
    );

    const isReadOnly = React.useMemo(
        () =>
            isFieldDisabled(props.screenId, props.fieldProperties, props.value, null) ||
            isFieldReadOnly(props.screenId, props.fieldProperties, props.value, null),
        [props.fieldProperties, props.screenId, props.value],
    );

    const onClick = React.useCallback(
        (option: string): (() => void) =>
            () => {
                handleChange(
                    props.elementId,
                    option,
                    props.setFieldValue,
                    props.validate,
                    triggerChangeListener(props.screenId, props.elementId),
                );
            },
        [props.elementId, props.screenId, props.setFieldValue, props.validate],
    );

    const title = React.useMemo(() => {
        const resolvedTitle = getFieldTitle(props.screenId, props.fieldProperties, null);
        return `${resolvedTitle || ''}${isMandatory ? ' *' : ''}`;
    }, [props.fieldProperties, props.screenId, isMandatory]);

    const hasTitle = React.useMemo(
        () => !props.fieldProperties.isTitleHidden && title !== '' && title !== undefined,
        [props.fieldProperties.isTitleHidden, title],
    );

    const testId = React.useMemo(
        () => getDataTestIdAttribute('selection-card-component', 'selection-card-button', props.elementId),
        [props.elementId],
    );

    const getIcon = React.useCallback(
        (option: string) =>
            resolveDetailedIcon(
                resolveByValue({
                    propertyValue: props.fieldProperties.icon,
                    skipHexFormat: true,
                    screenId: props.screenId,
                    fieldValue: option,
                    rowValue: null, // Nested selection-cards are not supported
                }),
            ),
        [props.fieldProperties.icon, props.screenId],
    );

    const { error, warning, info } = getFieldIndicatorStatus(props);

    return (
        <CarbonWrapper
            noReadOnlySupport={true}
            {...props}
            className="e-selection-card-field"
            componentName="selection-card"
            value={props.value}
            componentRef={componentRef}
        >
            {hasTitle && <FieldLabel label={title} errorMessage={error} warningMessage={warning} infoMessage={info} />}
            {props.fieldProperties.hasFilter && (
                <Search
                    placeholder={localize('@sage/xtrem-ui/selection-card-filter-placeholder', 'Filter...')}
                    value={filterValue}
                    onChange={e => setFilterValue(e.target.value)}
                    mb="8px"
                    mt="8px"
                    data-testid="e-selection-card-filter"
                />
            )}
            <div className="selection-card-container">
                {optionsToUse.map(option => {
                    return (
                        <SelectionCard
                            key={option}
                            ref={componentRef}
                            _id={testId}
                            isSelected={props.value === option}
                            isReadOnly={isReadOnly}
                            title={getTitle(option)}
                            icon={getIcon(option)}
                            description={getDescription(option)}
                            onClick={onClick(option)}
                        />
                    );
                })}
            </div>
            <HelperText helperText={props.fieldProperties.helperText} />
        </CarbonWrapper>
    );
}

const triggerChangeListener = (screenId: string, elementId: string) => (): void => {
    triggerFieldEvent(screenId, elementId, 'onChange');
};

export const ConnectedSelectionCardComponent = connect(
    (state: XtremAppState, externalProps: FieldComponentExternalProperties) =>
        addOptionsAndLocalizationToProps(state, mapStateToProps()(state, externalProps) as SelectionCardComponentProps),
    mapDispatchToProps(),
)(SelectionCardComponent);

export default ConnectedSelectionCardComponent;
