import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import type { SelectionCardComponentProps } from './selection-card-types';

const ConnectedSelectionCardComponent = React.lazy(() => import('./selection-card-component'));

export function AsyncConnectedSelectionCardComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} />}>
            <ConnectedSelectionCardComponent {...props} />
        </React.Suspense>
    );
}

const SelectionCardComponent = React.lazy(() =>
    import('./selection-card-component').then(c => ({ default: c.SelectionCardComponent })),
);

export function AsyncSelectionCardComponent(props: SelectionCardComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={true} bodyHeight="200px" />}>
            <SelectionCardComponent {...props} />
        </React.Suspense>
    );
}
