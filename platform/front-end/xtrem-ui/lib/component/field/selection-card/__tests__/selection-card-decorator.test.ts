import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { selectionCard } from '../selection-card-decorator';
import type { SelectionCardDecoratorProperties } from '../selection-card-types';

describe('selectionCard decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'selectionCard';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should set default values when no component properties provided', () => {
            selectionCard({
                icon() {
                    return 'apple';
                },
                description(v) {
                    return v;
                },
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties = pageMetadata.uiComponentProperties[
                fieldId
            ] as SelectionCardDecoratorProperties;
            expect(mappedComponentProperties.options).toBeUndefined();
            expect(mappedComponentProperties.optionType).toBeUndefined();
            expect(mappedComponentProperties.onChange).toBeUndefined();
            expect(mappedComponentProperties.icon('')).toBe('apple');
            expect(mappedComponentProperties.description('description')).toBe('description');
        });

        it('should set values when component properties provided', () => {
            const mockOptions = ['option1', 'anotherOption'];
            const mockOptionType = '@any/enum/Type';
            selectionCard({
                options: mockOptions,
                optionType: mockOptionType,
                icon() {
                    return 'apple';
                },
                description(v) {
                    return v;
                },
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});

            const mappedComponentProperties = pageMetadata.uiComponentProperties[
                fieldId
            ] as SelectionCardDecoratorProperties;
            expect(mappedComponentProperties.options).toEqual(mockOptions);
            expect(mappedComponentProperties.optionType).toEqual(mockOptionType);
            expect(mappedComponentProperties.icon('')).toBe('apple');
            expect(mappedComponentProperties.description('description')).toBe('description');
        });

        it('should assign onClick handler', () => {
            testOnClickHandler(selectionCard, pageMetadata, fieldId);
        });
    });
});
