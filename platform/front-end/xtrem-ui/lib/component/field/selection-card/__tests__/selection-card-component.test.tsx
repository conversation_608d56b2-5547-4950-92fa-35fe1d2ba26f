import 'jest-styled-components';
import {
    addFieldToState,
    applyActionMocks,
    getMockPageDefinition,
    getMockState,
    getMockStore,
} from '../../../../__tests__/test-helpers';

import { SelectionCardComponent, ConnectedSelectionCardComponent } from '../selection-card-component';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import type { SelectionCardComponentProps } from '../selection-card-types';
import React from 'react';
import { FieldKey } from '../../../types';
import type { MockStoreEnhanced } from 'redux-mock-store';
import * as xtremRedux from '../../../../redux';
import * as i18nService from '../../../../service/i18n-service';
import { Provider } from 'react-redux';
import * as actions from '../../../../redux/actions';
import * as stateUtils from '../../../../utils/state-utils';
import '@testing-library/jest-dom';

jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() => getMockPageDefinition('screen-id'));

describe('SelectionCard component', () => {
    let selectionCardComponentProps: SelectionCardComponentProps;
    beforeEach(() => {
        selectionCardComponentProps = {
            elementId: 'test-selectionCard',
            screenId: 'screen-id',
            locale: 'en-US',
            fieldProperties: {
                title: 'title selectionCard',
                options: ['option 1', 'option 2', 'option 3'],
                icon() {
                    return 'apple';
                },
                description(v) {
                    return `Description: ${v}`;
                },
            },
            onFocus: jest.fn(),
            setFieldValue: jest.fn().mockResolvedValue(undefined),
            validate: jest.fn(),
            removeNonNestedErrors: jest.fn(),
        };
    });

    it('should render 3 selection cards with fieldProperties.options values', async () => {
        render(<SelectionCardComponent {...selectionCardComponentProps} />);

        const selectionCardTitles = (await screen.findAllByTestId('e-selection-card-title')) as HTMLDivElement[];
        expect(selectionCardTitles.length).toBe(3);
        expect(selectionCardTitles[0].textContent).toBe('option 1');
        expect(selectionCardTitles[1].textContent).toBe('option 2');
        expect(selectionCardTitles[2].textContent).toBe('option 3');
        const selectionCardDescriptions = (await screen.findAllByTestId(
            'e-selection-card-description',
        )) as HTMLDivElement[];
        expect(selectionCardDescriptions.length).toBe(3);
        expect(selectionCardDescriptions[0].textContent).toBe('Description: option 1');
        expect(selectionCardDescriptions[1].textContent).toBe('Description: option 2');
        expect(selectionCardDescriptions[2].textContent).toBe('Description: option 3');
    });

    it('set field value should be fired upon clicking a selectionCard button', async () => {
        const mockSetFieldValue = jest.fn().mockResolvedValue(undefined);
        selectionCardComponentProps.setFieldValue = mockSetFieldValue;
        render(<SelectionCardComponent {...selectionCardComponentProps} />);

        const selectionCardTitles = (await screen.findAllByTestId('e-selection-card-title')) as HTMLDivElement[];

        selectionCardTitles[0].click();
        expect(mockSetFieldValue).toHaveBeenCalledWith('test-selectionCard', 'option 1');

        selectionCardTitles[1].click();
        expect(mockSetFieldValue).toHaveBeenCalledWith('test-selectionCard', 'option 2');

        selectionCardTitles[2].click();
        expect(mockSetFieldValue).toHaveBeenCalledWith('test-selectionCard', 'option 3');
    });

    it('Should render with title selectionCard', () => {
        render(<SelectionCardComponent {...selectionCardComponentProps} />);
        screen.getByText('title selectionCard');
    });

    it('Snapshot', () => {
        const wrapper = render(<SelectionCardComponent {...selectionCardComponentProps} />);
        expect(wrapper.baseElement).toMatchSnapshot();
    });

    describe('connected', () => {
        let mockStore: MockStoreEnhanced<xtremRedux.XtremAppState>;
        let localizeSpy: jest.SpyInstance<string, [string, string]> | null = null;

        const screenId = 'TestPage';
        const fieldIdWithOptionType = 'myTestOptionTypeSelectionCardButton';
        const fieldIdWithOptionTypeSorted = 'myTestOptionTypeSelectionCardButtonSorted';
        const fieldIdWithCallbackOptions = 'myTestCallbackOptionsTypeSelectionCardButton';
        const fieldIdWithOptions = 'myTestSelectionCardButton';
        const fieldIdWithOptionsSorted = 'myTestSelectionCardButtonSorted';
        const fieldIdWithOptionsMapped = 'fieldIdWithOptionsMapped';

        beforeEach(() => {
            const setFieldValueMockImpl = jest.fn(() => () => Promise.resolve());
            jest.spyOn(xtremRedux.actions, 'setFieldValue').mockImplementation(setFieldValueMockImpl);

            localizeSpy = jest
                .spyOn(i18nService, 'localizeEnumMember')
                .mockImplementation((_: any, enumName: string) => `${enumName} localized`);
            const state = getMockState();
            state.enumTypes.MyLocalizedEnum = ['option1', 'option2', 'option3'];
            state.enumTypes.MyLocalizedUnsortedEnum = ['option3', 'option2', 'option1'];
            state.screenDefinitions[screenId] = getMockPageDefinition(screenId);

            addFieldToState(
                FieldKey.SelectionCard,
                state,
                screenId,
                fieldIdWithOptionType,
                {
                    title: 'SelectionCard button with options from an enum',
                    optionType: '@sage/xtrem-test/MyLocalizedEnum',
                    isMandatory: true,
                    icon() {
                        return 'apple';
                    },
                    description(v) {
                        return v;
                    },
                },
                'item3',
            );

            addFieldToState(
                FieldKey.SelectionCard,
                state,
                screenId,
                fieldIdWithOptionTypeSorted,
                {
                    title: 'SelectionCard button with sorted options from an enum',
                    optionType: '@sage/xtrem-test/MyLocalizedUnsortedEnum',
                    isMandatory: true,
                    isSortedAlphabetically: true,
                    icon() {
                        return 'apple';
                    },
                    description(v) {
                        return v;
                    },
                },
                'item3',
            );

            addFieldToState(
                FieldKey.SelectionCard,
                state,
                screenId,
                fieldIdWithOptions,
                {
                    title: 'SelectionCard button with hardcoded options',
                    options: ['item1', 'item2', 'item3'],
                    icon() {
                        return 'apple';
                    },
                    description(v) {
                        return v;
                    },
                },
                'option3',
            );

            addFieldToState(
                FieldKey.SelectionCard,
                state,
                screenId,
                fieldIdWithOptionsSorted,
                {
                    title: 'SelectionCard button with sorted hardcoded options',
                    options: ['item3', 'item1', 'item2'],
                    isSortedAlphabetically: true,
                    icon() {
                        return 'apple';
                    },
                    description(v) {
                        return v;
                    },
                },
                'option3',
            );

            addFieldToState(
                FieldKey.SelectionCard,
                state,
                screenId,
                fieldIdWithCallbackOptions,
                {
                    title: 'SelectionCard button with hardcoded options',
                    options() {
                        return ['callback1', 'callback2', 'callback3'];
                    },
                    icon() {
                        return 'apple';
                    },
                    description(v) {
                        return v;
                    },
                },
                'option3',
            );

            addFieldToState(FieldKey.SelectionCard, state, screenId, fieldIdWithOptionsMapped, {
                title: 'SelectionCard button with hardcoded and mapped value',
                options: ['item1', 'item2', 'item3'],
                map(value: string) {
                    if (value === 'item1') {
                        return 'mapped item 1';
                    }
                    return value;
                },
                icon() {
                    return 'apple';
                },
                description(v) {
                    return v;
                },
            });

            mockStore = getMockStore(state);
        });

        afterEach(() => {
            jest.clearAllMocks();
            applyActionMocks();
        });

        it('should render selection cardwith hardcoded options', async () => {
            render(
                <Provider store={mockStore}>
                    <ConnectedSelectionCardComponent screenId={screenId} elementId={fieldIdWithOptions} />
                </Provider>,
            );

            const selectionCardTitles = (await screen.findAllByTestId('e-selection-card-title')) as HTMLDivElement[];

            expect(selectionCardTitles).toHaveLength(3);
            expect(actions.setFieldValue).not.toHaveBeenCalled();

            expect(selectionCardTitles[0].textContent).toBe('item1');
            selectionCardTitles[0].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestSelectionCardButton',
                'item1',
                true,
            );

            expect(selectionCardTitles[1].textContent).toBe('item2');
            selectionCardTitles[1].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestSelectionCardButton',
                'item2',
                true,
            );

            expect(selectionCardTitles[2].textContent).toBe('item3');
            selectionCardTitles[2].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestSelectionCardButton',
                'item3',
                true,
            );
        });

        it('should render selection card with hardcoded, sorted options', async () => {
            render(
                <Provider store={mockStore}>
                    <ConnectedSelectionCardComponent screenId={screenId} elementId={fieldIdWithOptionsSorted} />
                </Provider>,
            );

            const selectionCardTitles = (await screen.findAllByTestId('e-selection-card-title')) as HTMLDivElement[];

            expect(selectionCardTitles).toHaveLength(3);
            expect(actions.setFieldValue).not.toHaveBeenCalled();

            expect(selectionCardTitles[0].textContent).toBe('item1');
            selectionCardTitles[0].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestSelectionCardButtonSorted',
                'item1',
                true,
            );

            expect(selectionCardTitles[1].textContent).toBe('item2');
            selectionCardTitles[1].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestSelectionCardButtonSorted',
                'item2',
                true,
            );

            expect(selectionCardTitles[2].textContent).toBe('item3');
            selectionCardTitles[2].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestSelectionCardButtonSorted',
                'item3',
                true,
            );
        });

        it('should render selection card with title', async () => {
            const element = render(
                <Provider store={mockStore}>
                    <ConnectedSelectionCardComponent screenId={screenId} elementId={fieldIdWithOptions} />
                </Provider>,
            );
            const label = (await element.findByTestId('e-field-label')) as HTMLLabelElement;

            expect(label).toHaveTextContent('SelectionCard button with hardcoded options');
        });

        it('should render selection card with title if the field is mandatory', async () => {
            const element = render(
                <Provider store={mockStore}>
                    <ConnectedSelectionCardComponent screenId={screenId} elementId={fieldIdWithOptionType} />
                </Provider>,
            );

            const label = (await element.findByTestId('e-field-label')) as HTMLLabelElement;
            expect(label).toHaveTextContent('SelectionCard button with options from an enum *');
        });

        it('should render selection card with callback options', async () => {
            render(
                <Provider store={mockStore}>
                    <ConnectedSelectionCardComponent screenId={screenId} elementId={fieldIdWithCallbackOptions} />
                </Provider>,
            );

            const selectionCardTitles = (await screen.findAllByTestId('e-selection-card-title')) as HTMLDivElement[];

            expect(selectionCardTitles).toHaveLength(3);
            expect(actions.setFieldValue).not.toHaveBeenCalled();

            expect(selectionCardTitles[0].textContent).toBe('callback1');
            selectionCardTitles[0].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                screenId,
                fieldIdWithCallbackOptions,
                'callback1',
                true,
            );

            expect(selectionCardTitles[1].textContent).toBe('callback2');
            selectionCardTitles[1].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                screenId,
                fieldIdWithCallbackOptions,
                'callback2',
                true,
            );

            expect(selectionCardTitles[2].textContent).toBe('callback3');
            selectionCardTitles[2].click();
            expect(actions.setFieldValue).toHaveBeenLastCalledWith(
                screenId,
                fieldIdWithCallbackOptions,
                'callback3',
                true,
            );
        });

        it('should render selection card with option type and localize displayed labels', async () => {
            expect(localizeSpy).not.toHaveBeenCalled();

            render(
                <Provider store={mockStore}>
                    <ConnectedSelectionCardComponent screenId={screenId} elementId={fieldIdWithOptionType} />
                </Provider>,
            );

            expect(localizeSpy).toHaveBeenCalledTimes(3);

            const selectionCardTitles = (await screen.findAllByTestId('e-selection-card-title')) as HTMLDivElement[];
            expect(selectionCardTitles).toHaveLength(3);
            expect(actions.setFieldValue).not.toHaveBeenCalled();

            expect(selectionCardTitles[0].textContent).toBe('option1 localized');
            selectionCardTitles[0].click();
            expect(xtremRedux.actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestOptionTypeSelectionCardButton',
                'option1',
                true,
            );

            expect(selectionCardTitles[1].textContent).toBe('option2 localized');
            selectionCardTitles[1].click();
            expect(xtremRedux.actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestOptionTypeSelectionCardButton',
                'option2',
                true,
            );

            expect(selectionCardTitles[2].textContent).toBe('option3 localized');
            selectionCardTitles[2].click();
            expect(xtremRedux.actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestOptionTypeSelectionCardButton',
                'option3',
                true,
            );
        });

        it('should render selection card with option type and localize displayed labels when sorted', async () => {
            expect(localizeSpy).not.toHaveBeenCalled();

            render(
                <Provider store={mockStore}>
                    <ConnectedSelectionCardComponent screenId={screenId} elementId={fieldIdWithOptionTypeSorted} />
                </Provider>,
            );

            expect(localizeSpy).toHaveBeenCalledTimes(3);

            const selectionCardTitles = (await screen.findAllByTestId('e-selection-card-title')) as HTMLDivElement[];
            expect(selectionCardTitles).toHaveLength(3);
            expect(actions.setFieldValue).not.toHaveBeenCalled();

            expect(selectionCardTitles[0].textContent).toBe('option1 localized');
            selectionCardTitles[0].click();
            expect(xtremRedux.actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestOptionTypeSelectionCardButtonSorted',
                'option1',
                true,
            );

            expect(selectionCardTitles[1].textContent).toBe('option2 localized');
            selectionCardTitles[1].click();
            expect(xtremRedux.actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestOptionTypeSelectionCardButtonSorted',
                'option2',
                true,
            );

            expect(selectionCardTitles[2].textContent).toBe('option3 localized');
            selectionCardTitles[2].click();
            expect(xtremRedux.actions.setFieldValue).toHaveBeenLastCalledWith(
                'TestPage',
                'myTestOptionTypeSelectionCardButtonSorted',
                'option3',
                true,
            );
        });

        it('should render selection card with mapped values', async () => {
            render(
                <Provider store={mockStore}>
                    <ConnectedSelectionCardComponent screenId={screenId} elementId={fieldIdWithOptionsMapped} />
                </Provider>,
            );

            const selectionCardTitles = (await screen.findAllByTestId('e-selection-card-title')) as HTMLDivElement[];

            expect(selectionCardTitles.length).toBe(3);
            expect(selectionCardTitles[0].textContent).toBe('mapped item 1');
            expect(selectionCardTitles[1].textContent).toBe('item2');
            expect(selectionCardTitles[2].textContent).toBe('item3');
        });

        it('should filter the options if the filter input is used', async () => {
            selectionCardComponentProps.fieldProperties.hasFilter = true;
            const { findAllByTestId, baseElement } = render(
                <SelectionCardComponent {...selectionCardComponentProps} />,
            );
            let selectionCardTitles = (await findAllByTestId('e-selection-card-title')) as HTMLDivElement[];

            expect(selectionCardTitles.length).toBe(3);
            expect(selectionCardTitles[0].textContent).toBe('option 1');
            expect(selectionCardTitles[1].textContent).toBe('option 2');
            expect(selectionCardTitles[2].textContent).toBe('option 3');

            const input = baseElement.querySelector('[data-testid="e-selection-card-filter"] input')!;
            fireEvent.focus(input);
            fireEvent.change(input, { target: { value: 'option 1' } });

            await waitFor(async () => {
                selectionCardTitles = (await findAllByTestId('e-selection-card-title')) as HTMLDivElement[];
                expect(selectionCardTitles.length).toBe(1);
                expect(selectionCardTitles[0].textContent).toBe('option 1');
            });
        });

        describe('info and warning', () => {
            it('should render with an info message', async () => {
                selectionCardComponentProps.fieldProperties.infoMessage = 'Info message!!';
                const { baseElement } = render(<SelectionCardComponent {...selectionCardComponentProps} />);

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an info message from a callback', async () => {
                selectionCardComponentProps.fieldProperties.infoMessage = () => 'Info message!!';
                const { baseElement } = render(<SelectionCardComponent {...selectionCardComponentProps} />);

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="info"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent('Info message!!');
                });
            });

            it('should render with an warning message', async () => {
                selectionCardComponentProps.fieldProperties.warningMessage = 'Warning message!!';
                const { baseElement } = render(<SelectionCardComponent {...selectionCardComponentProps} />);

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should render with an warning message from a callback', async () => {
                selectionCardComponentProps.fieldProperties.warningMessage = () => 'Warning message!!';
                const { baseElement } = render(<SelectionCardComponent {...selectionCardComponentProps} />);

                fireEvent.mouseEnter(baseElement.querySelector('[data-element="warning"]')!);
                await waitFor(() => {
                    expect(baseElement.querySelector('[data-element="tooltip"]')).toHaveTextContent(
                        'Warning message!!',
                    );
                });
            });

            it('should prioritize warnings over info messages', () => {
                selectionCardComponentProps.fieldProperties.warningMessage = () => 'Warning message!!';
                selectionCardComponentProps.fieldProperties.infoMessage = () => 'Info message!!';
                const wrapper = render(<SelectionCardComponent {...selectionCardComponentProps} />);

                expect(wrapper.baseElement.querySelector('[data-element="info"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-element="warning"]')).not.toBeNull();
            });

            it('should prioritize validation errors over warnings and info messages', () => {
                selectionCardComponentProps.fieldProperties.warningMessage = () => 'Warning message!!';
                selectionCardComponentProps.fieldProperties.infoMessage = () => 'Info message!!';
                selectionCardComponentProps.validationErrors = [
                    { elementId: fieldIdWithOptions, screenId, validationRule: 'isMandatory', message: 'Error' },
                ];
                const wrapper = render(<SelectionCardComponent {...selectionCardComponentProps} />);
                expect(wrapper.baseElement.querySelector('[data-element="info"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-element="warning"]')).toBeNull();
                expect(wrapper.baseElement.querySelector('[data-element="error"]')).not.toBeNull();
            });
        });
    });
});
