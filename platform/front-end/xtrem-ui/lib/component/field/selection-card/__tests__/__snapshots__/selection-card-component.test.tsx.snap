// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SelectionCard component Snapshot 1`] = `
<body>
  <div>
    <div
      class="e-field e-selection-card-field"
      data-label="title selectionCard"
      data-testid="e-selection-card-field e-field-label-titleSelectionCard e-field-bind-test-selectionCard"
    >
      <label
        class="common-input__label"
        data-element="label"
        data-testid="e-field-label"
      >
        title selectionCard
      </label>
      <div
        class="selection-card-container"
      >
        <button
          aria-label="option 1"
          class="e-selection-card"
          data-testid="e-selection-card-e-selection-card-component-field e-field-label-selectionCardButton e-field-bind-test-selectionCard"
          type="button"
        >
          <div
            class="e-selection-card-image"
            data-testid="e-selection-card-image"
          >
            <img
              alt="option 1"
              data-testid="e-selection-card-image"
              src="/images/detailed-icons/90x90_apple_green-on-transparent_icon.svg"
            />
          </div>
          <div
            class="e-selection-card-content"
            data-testid="e-selection-card-content"
          >
            <div
              class="e-selection-card-title"
              data-testid="e-selection-card-title"
            >
              option 1
            </div>
            <div
              class="e-selection-card-description"
              data-testid="e-selection-card-description"
            >
              Description: option 1
            </div>
          </div>
        </button>
        <button
          aria-label="option 2"
          class="e-selection-card"
          data-testid="e-selection-card-e-selection-card-component-field e-field-label-selectionCardButton e-field-bind-test-selectionCard"
          type="button"
        >
          <div
            class="e-selection-card-image"
            data-testid="e-selection-card-image"
          >
            <img
              alt="option 2"
              data-testid="e-selection-card-image"
              src="/images/detailed-icons/90x90_apple_green-on-transparent_icon.svg"
            />
          </div>
          <div
            class="e-selection-card-content"
            data-testid="e-selection-card-content"
          >
            <div
              class="e-selection-card-title"
              data-testid="e-selection-card-title"
            >
              option 2
            </div>
            <div
              class="e-selection-card-description"
              data-testid="e-selection-card-description"
            >
              Description: option 2
            </div>
          </div>
        </button>
        <button
          aria-label="option 3"
          class="e-selection-card"
          data-testid="e-selection-card-e-selection-card-component-field e-field-label-selectionCardButton e-field-bind-test-selectionCard"
          type="button"
        >
          <div
            class="e-selection-card-image"
            data-testid="e-selection-card-image"
          >
            <img
              alt="option 3"
              data-testid="e-selection-card-image"
              src="/images/detailed-icons/90x90_apple_green-on-transparent_icon.svg"
            />
          </div>
          <div
            class="e-selection-card-content"
            data-testid="e-selection-card-content"
          >
            <div
              class="e-selection-card-title"
              data-testid="e-selection-card-title"
            >
              option 3
            </div>
            <div
              class="e-selection-card-description"
              data-testid="e-selection-card-description"
            >
              Description: option 3
            </div>
          </div>
        </button>
      </div>
      <span
        class="common-input__help-text"
        data-element="help"
        data-testid="e-field-helper-text"
      >
         
      </span>
    </div>
  </div>
</body>
`;
