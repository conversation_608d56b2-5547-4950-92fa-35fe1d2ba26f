import type { <PERSON><PERSON><PERSON> } from '../../../types';
import { SelectionCardControlObject } from '../../../control-objects';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import type { SelectionCardProperties } from '../selection-card-types';
import * as stateUtils from '../../../../utils/state-utils';
import { getMockPageDefinition } from '../../../../__tests__/test-helpers';

jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() => getMockPageDefinition('TestPage'));

describe('SelectionCard control object', () => {
    let selectionCardControlObject: SelectionCardControlObject;
    let selectionCardFieldProperties: SelectionCardProperties;
    let selectionCardValue: string;

    beforeEach(() => {
        selectionCardFieldProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
            options: ['randomValue1', 'randomValue2'],
            icon() {
                return 'apple';
            },
            description(v) {
                return v;
            },
        };
        selectionCardValue = 'SELECTION CARD TEST VALUE';
        selectionCardControlObject = buildControlObject<FieldKey.SelectionCard>(SelectionCardControlObject, {
            fieldValue: selectionCardValue,
            fieldProperties: selectionCardFieldProperties,
        });
    });

    it('should get the field value', () => {
        expect(selectionCardControlObject.value).toEqual(selectionCardValue);
    });

    it('should set field value to null', () => {
        expect(() => {
            selectionCardControlObject.value = null;
        }).not.toThrow();
    });

    it('should set field value to a valid option', () => {
        expect(() => {
            selectionCardControlObject.value = 'randomValue1';
        }).not.toThrow();
    });

    it('should not set field value to an invalid option', () => {
        expect(() => {
            selectionCardControlObject.value = 'randomValue3';
        }).toThrow(
            'randomValue3 is not a valid option of the fieldName field. Valid options: randomValue1, randomValue2',
        );
    });

    it('should get the option type field value', () => {
        expect(selectionCardControlObject.optionType).toEqual(selectionCardFieldProperties.optionType);
    });

    it('should set the title', () => {
        const newValue = 'Test SelectionCard Field Title';
        expect(selectionCardFieldProperties.title).not.toEqual(newValue);
        selectionCardControlObject.title = newValue;
        expect(selectionCardFieldProperties.title).toEqual(newValue);
    });

    it('should get the title', () => {
        expect(selectionCardFieldProperties.title).toEqual(selectionCardFieldProperties.title);
    });

    it('should set the options', () => {
        const newValue = ['value1', 'value2'];
        expect(selectionCardFieldProperties.options).not.toEqual(newValue);
        selectionCardControlObject.options = newValue;
        expect(selectionCardFieldProperties.options).toEqual(newValue);
    });

    it('should get the options', () => {
        expect(selectionCardFieldProperties.options).toEqual(selectionCardFieldProperties.options);
    });

    it('should resolve callback style option list', () => {
        selectionCardFieldProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
            options() {
                return ['Option12', 'Option32'];
            },
            icon() {
                return 'apple';
            },
            description(v) {
                return v;
            },
        };

        selectionCardValue = 'SELECTION CARD TEST VALUE';
        selectionCardControlObject = buildControlObject<FieldKey.SelectionCard>(SelectionCardControlObject, {
            fieldValue: selectionCardValue,
            fieldProperties: selectionCardFieldProperties,
        });

        expect(selectionCardControlObject.options).toEqual(['Option12', 'Option32']);
    });
});
