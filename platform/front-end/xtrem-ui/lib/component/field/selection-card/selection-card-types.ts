import type { ScreenBase } from '../../../service/screen-base';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type {
    CanFetchDefaults,
    Changeable,
    ExtensionField,
    HasOptions,
    HasParent,
    Mappable,
    Sizable,
    Validatable,
} from '../traits';

export type DetailedIconType =
    | 'accounting'
    | 'addons'
    | 'animal'
    | 'apple'
    | 'arrow-down'
    | 'arrow-left'
    | 'arrow-right'
    | 'arrow-up'
    | 'asset_mgt'
    | 'award'
    | 'bag'
    | 'bakery'
    | 'barcode'
    | 'bicycle'
    | 'binocular'
    | 'blank-template'
    | 'book-search'
    | 'book-tag'
    | 'book'
    | 'box-distribution'
    | 'bright'
    | 'building-and-people'
    | 'building'
    | 'business-mgt'
    | 'business-scale'
    | 'calculator'
    | 'calendar'
    | 'camera'
    | 'card-stacked'
    | 'card-transaction'
    | 'card'
    | 'cart-warehouse-2'
    | 'cart-warehouse'
    | 'cart'
    | 'certificate'
    | 'chart-bar'
    | 'chat'
    | 'check'
    | 'checkbox'
    | 'checklist'
    | 'chemical'
    | 'chess'
    | 'chevron-down'
    | 'chevron-left'
    | 'chevron-right'
    | 'chevron-up'
    | 'click'
    | 'clock'
    | 'close'
    | 'clothes'
    | 'cloud-connected'
    | 'cloud-currency-dollar'
    | 'cloud-currency-euro'
    | 'cloud-currency-pound'
    | 'cloud-data'
    | 'cloud-down'
    | 'cloud-service'
    | 'cloud-up'
    | 'cloud'
    | 'coffee'
    | 'compass'
    | 'computer-wifi'
    | 'connected'
    | 'consultant'
    | 'conversation'
    | 'conveyer-belt'
    | 'cooking'
    | 'cpu'
    | 'credit-card'
    | 'crowd'
    | 'crown'
    | 'dart-board'
    | 'dashboard-setting'
    | 'data'
    | 'database'
    | 'decline'
    | 'desktop'
    | 'device-setting'
    | 'devices'
    | 'dollar'
    | 'double-check'
    | 'download'
    | 'dump-truck'
    | 'ear'
    | 'ecomm'
    | 'excavator'
    | 'eye'
    | 'factory'
    | 'favorite'
    | 'filter'
    | 'financial-services-euro'
    | 'financial-services-pound'
    | 'financial-services_dollar-green-on-transparent_icon.svg'
    | 'financials-2'
    | 'financials'
    | 'flag'
    | 'flower-abs'
    | 'folder-share'
    | 'folder-write'
    | 'folder'
    | 'food-serve'
    | 'food'
    | 'form'
    | 'gauge'
    | 'gears'
    | 'glasses'
    | 'globe'
    | 'grad-cap'
    | 'green-offering'
    | 'green'
    | 'handshake'
    | 'happy'
    | 'heart'
    | 'hide'
    | 'holiday'
    | 'home'
    | 'hourglass'
    | 'hub'
    | 'idea'
    | 'incline'
    | 'industry'
    | 'info-tip'
    | 'info'
    | 'integration'
    | 'jewelry'
    | 'key-hole'
    | 'keys'
    | 'lab'
    | 'label'
    | 'laptop'
    | 'light-bulb'
    | 'lightning'
    | 'like'
    | 'link'
    | 'locations'
    | 'lock'
    | 'lock_unlocked'
    | 'magnifying-glass'
    | 'mail-opened'
    | 'mail'
    | 'map'
    | 'medical'
    | 'megaphone'
    | 'memo'
    | 'microphone'
    | 'minus'
    | 'mobile-purchase-dollar'
    | 'mobile-purchase-euro'
    | 'mobile-purchase-pound'
    | 'mobile-wifi'
    | 'money-bill'
    | 'money-dollar'
    | 'money-euro'
    | 'money-pound'
    | 'money-received-dollar'
    | 'money-received-euro'
    | 'money-received-pound'
    | 'money-stacked'
    | 'monitor-click'
    | 'mouse'
    | 'name-tag'
    | 'newspaper'
    | 'non-profit'
    | 'note'
    | 'notebook'
    | 'office'
    | 'page'
    | 'payment'
    | 'payroll'
    | 'pen'
    | 'pencil'
    | 'people-chat'
    | 'people-network-2'
    | 'people-network'
    | 'people-search'
    | 'people-settings'
    | 'person'
    | 'phone'
    | 'pie-break'
    | 'pin-dropped'
    | 'pin-map'
    | 'pin'
    | 'play-button'
    | 'plus'
    | 'point'
    | 'pound'
    | 'power'
    | 'presentation'
    | 'print'
    | 'processing'
    | 'puzzle'
    | 'question-circle'
    | 'question'
    | 'receipts'
    | 'recycle'
    | 'redo'
    | 'remote'
    | 'rocket'
    | 'safe'
    | 'satelite'
    | 'savings'
    | 'scissors'
    | 'sd-card'
    | 'secured-sign-in'
    | 'server'
    | 'service'
    | 'setting'
    | 'share'
    | 'shoes'
    | 'shopping-bag'
    | 'shuffle'
    | 'sign'
    | 'sim'
    | 'smartphone'
    | 'stationeries'
    | 'store'
    | 'support'
    | 'switch-board'
    | 'sync'
    | 'tab'
    | 'table'
    | 'tablet-hand'
    | 'tablet'
    | 'thermometer'
    | 'time-clock'
    | 'timer'
    | 'tools'
    | 'trash-bin'
    | 'travel'
    | 'truck'
    | 'two-ways'
    | 'undo'
    | 'user-cards'
    | 'user-message'
    | 'video-game'
    | 'video-guide'
    | 'video'
    | 'wallet'
    | 'warehouse'
    | 'warning-2'
    | 'warning'
    | 'weather'
    | 'wireless'
    | 'wrench'
    | 'writing'
    | 'zoom-in'
    | 'zoom-out';

export interface SelectionCardProperties<CT extends ScreenBase = ScreenBase>
    extends EditableFieldProperties<CT>,
        Sizable,
        HasOptions<CT>,
        CanFetchDefaults,
        Mappable<CT> {
    icon: (option: string) => DetailedIconType;
    description: (option: string) => string;
    hasFilter?: boolean;
}

export interface SelectionCardDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<SelectionCardProperties<CT>, '_controlObjectType'>,
        Changeable<CT>,
        HasParent<CT, BlockControlObject<CT>>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Validatable<CT>,
        Sizable {}

export type SelectionCardComponentProps = BaseEditableComponentProperties<SelectionCardProperties, string>;
