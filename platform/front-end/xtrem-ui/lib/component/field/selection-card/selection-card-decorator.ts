import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { SelectionCardControlObject } from './selection-card-control-object';
import type { SelectionCardDecoratorProperties } from './selection-card-types';

class SelectionCardDecorator extends AbstractFieldDecorator<FieldKey.SelectionCard> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = SelectionCardControlObject;
}

/**
 * Initializes the decorated member as a [SelectionCard]{@link SelectionCardControlObject} field with the provided properties
 *
 * @param properties The properties that the [SelectionCard]{@link SelectionCardControlObject} field will be initialized with
 */
export function selectionCard<T extends ScreenExtension<T>>(
    properties: SelectionCardDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.SelectionCard>(
        properties,
        SelectionCardDecorator,
        FieldKey.SelectionCard,
    );
}

export function selectionCardOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<SelectionCardDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.SelectionCard>(properties);
}
