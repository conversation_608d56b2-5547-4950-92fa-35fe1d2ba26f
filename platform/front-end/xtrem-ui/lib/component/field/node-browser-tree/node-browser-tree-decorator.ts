/**
 * @packageDocumentation
 * @module root
 * */

import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import { standardDecoratorImplementation } from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { NodeBrowserTreeControlObject } from '../../control-objects';
import { FieldKey } from '../../types';
import type { NodeBrowserTreeDecoratorProperties } from './node-browser-tree-types';

class NodeBrowserTreeDecorator extends AbstractFieldDecorator<FieldKey.NodeBrowserTree> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = NodeBrowserTreeControlObject;
}

/**
 * Initializes the decorated member as a [NodeBrowserTree]{@link NodeBrowserTreeControlObject} field with the provided properties
 *
 * @param properties The properties that the [NodeBrowserTree]{@link NodeBrowserTreeControlObject} field will be initialized with
 */
export function nodeBrowserTreeField<CT extends ScreenExtension<CT>>(
    properties: NodeBrowserTreeDecoratorProperties<Extend<CT>>,
): (target: CT, name: string) => void {
    return standardDecoratorImplementation<CT, FieldKey.NodeBrowserTree>(
        properties,
        NodeBrowserTreeDecorator,
        FieldKey.NodeBrowserTree,
        true,
    );
}
