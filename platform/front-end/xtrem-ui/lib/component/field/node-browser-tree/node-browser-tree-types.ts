import type { Dict, Property, TreeElement } from '@sage/xtrem-shared';
import type { NodeDetails } from '../../../service/node-information-service';
import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenBaseGenericType } from '../../../types';
import type { BlockControlObject, SectionControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type {
    CanFetchDefaults,
    Changeable,
    Clickable,
    ExtensionField,
    HasFieldActions,
    HasParent,
    Validatable,
} from '../traits';

export interface NodeBrowserTreeProperties<CT extends ScreenBase = ScreenBase> extends EditableFieldProperties<CT> {
    node?: keyof ScreenBaseGenericType<CT>;
    filter?: (this: CT, nodes: NodeDetails[]) => NodeDetails[];
}

export type NodeBrowserTreeValue = Dict<TreeElement<NodeDetails>>;

export interface NodeBrowserTreeDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<NodeBrowserTreeProperties<CT>, '_controlObjectType'>,
        Clickable<CT>,
        Changeable<CT>,
        HasFieldActions<CT>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        HasParent<CT, BlockControlObject<CT> | SectionControlObject<CT>>,
        Validatable<CT, NodeBrowserTreeValue>,
        CanFetchDefaults {
    filter?: (this: CT, nodes: NodeDetails[]) => NodeDetails[];
    fetchItems?: (this: CT, treeNode: Property) => Promise<Property[]>;
}

export type NodeBrowserTreeComponentProps = BaseEditableComponentProperties<
    NodeBrowserTreeDecoratorProperties,
    NodeBrowserTreeValue
>;
