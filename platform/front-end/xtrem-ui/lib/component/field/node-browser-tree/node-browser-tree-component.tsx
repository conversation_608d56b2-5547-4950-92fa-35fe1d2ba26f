import React from 'react';
import { connect } from 'react-redux';
import type { Dict, NodeDetails, Property, TreeElement } from '@sage/xtrem-shared';
import type { GraphQLTypes } from '../../../types';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { mapDispatchToProps, mapStateToProps } from '../field-base-utils';
import { NodeBrowserTree } from '../../ui/node-browser-tree/node-browser-tree';
import type { NodeBrowserTreeComponentProps } from './node-browser-tree-types';
import { getComponentClass, getDataTestIdAttribute } from '../../../utils/dom';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { getLabelTitle, isFieldDisabled, isFieldReadOnly } from '../carbon-helpers';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { triggerFieldEvent } from '../../../utils/events';
import { useFocus } from '../../../utils/hooks/effects/use-focus';
import type * as xtremRedux from '../../../redux';
import { getScreenElement, type ScreenBaseDefinition } from '../../../service/screen-base-definition';
import { useDeepEqualSelector } from '../../../utils/hooks/use-deep-equal-selector';

export const NodeBrowserTreeComponent: React.FC<NodeBrowserTreeComponentProps> = React.memo(props => {
    const screenDefinition = useDeepEqualSelector<xtremRedux.XtremAppState, ScreenBaseDefinition>(
        state => state.screenDefinitions[props.screenId],
    );
    const { fieldProperties, setFieldValue, validate, value, elementId, isInFocus } = props;
    const { filter, node, fetchItems } = fieldProperties;

    const componentRef = React.useRef(null);
    useFocus(componentRef, isInFocus, 'input');

    const changeEventHandler = React.useCallback(
        (): Promise<void> => triggerFieldEvent(props.screenId, props.elementId, 'onChange'),
        [props.screenId, props.elementId],
    );

    const changeClickHandler = React.useCallback(
        (): Promise<void> => triggerFieldEvent(props.screenId, props.elementId, 'onClick'),
        [props.screenId, props.elementId],
    );

    const onCheckedItemsUpdated = React.useCallback(
        (checkedItems: Dict<TreeElement<{ type: GraphQLTypes | string }>>): void => {
            handleChange(elementId, checkedItems, setFieldValue, validate, changeEventHandler);
        },
        [elementId, setFieldValue, validate, changeEventHandler],
    );

    const isReadOnly = isFieldReadOnly(props.screenId, props.fieldProperties, props.value, null); // Not available as a nested fields
    const isDisabled = isFieldDisabled(props.screenId, props.fieldProperties, props.value, null); // Not available as a nested fields
    const title = getLabelTitle(props.screenId, props.fieldProperties, null); // Not available as a nested fields
    const { isTitleHidden } = props.fieldProperties;

    const boundFilter = React.useCallback(
        (nodes: NodeDetails[]): NodeDetails[] => {
            if (filter) {
                return filter?.apply(getScreenElement(screenDefinition), [nodes]);
            }
            throw new Error('filter is not defined');
        },
        [filter, screenDefinition],
    );

    const boundFetchItems = React.useCallback(
        (property: Property): Promise<Property[]> => {
            if (fetchItems) {
                return fetchItems?.apply(getScreenElement(screenDefinition), [property]);
            }
            throw new Error('fetchItems is not defined');
        },
        [fetchItems, screenDefinition],
    );

    const infoMessage = React.useMemo(
        () =>
            resolveByValue({
                screenId: props.screenId,
                fieldValue: props.value,
                propertyValue: props.fieldProperties.infoMessage,
                rowValue: null,
                skipHexFormat: true,
            }),
        [props.screenId, props.value, props.fieldProperties.infoMessage],
    );

    const warningMessage = React.useMemo(
        () =>
            resolveByValue({
                screenId: props.screenId,
                fieldValue: props.value,
                propertyValue: props.fieldProperties.warningMessage,
                rowValue: null,
                skipHexFormat: true,
            }),
        [props.screenId, props.value, props.fieldProperties.warningMessage],
    );

    return (
        <div
            data-testid={getDataTestIdAttribute('node-browser-tree', title, props.elementId)}
            className={getComponentClass(props, 'e-node-browser-tree')}
            onClick={changeClickHandler}
            ref={componentRef}
        >
            {!isTitleHidden && (
                <FieldLabel
                    label={title}
                    errorMessage={props.validationErrors?.[0].message}
                    infoMessage={infoMessage}
                    warningMessage={warningMessage}
                />
            )}
            <NodeBrowserTree
                locale={props.locale}
                filter={filter ? boundFilter : undefined}
                checkedItems={value || {}}
                node={node}
                fetchItems={fetchItems ? boundFetchItems : undefined}
                onCheckedItemsUpdated={onCheckedItemsUpdated}
                isDisabled={isDisabled}
                isReadOnly={isReadOnly}
            />
            <HelperText helperText={props.fieldProperties.helperText} />
        </div>
    );
});

export const ConnectedNodeBrowserTree = connect(mapStateToProps(), mapDispatchToProps())(NodeBrowserTreeComponent);

export default ConnectedNodeBrowserTree;

NodeBrowserTreeComponent.displayName = 'NodeBrowserTreeComponent';
