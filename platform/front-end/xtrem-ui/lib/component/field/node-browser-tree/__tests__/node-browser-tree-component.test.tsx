import * as React from 'react';
import { renderWithRedux, applyActionMocks } from '../../../../__tests__/test-helpers';
import { ConnectedNodeBrowserTree } from '../node-browser-tree-component';
import type { NodeBrowserTreeDecoratorProperties } from '../node-browser-tree-types';
import type { FieldInternalValue } from '../../../types';
import { FieldKey } from '../../../types';
import * as nodeInformationService from '../../../../service/node-information-service';
import { GraphQLKind, GraphQLTypes } from '../../../../types';
import { fireEvent, waitFor } from '@testing-library/dom';
import * as xtremRedux from '../../../../redux';
import * as events from '../../../../utils/events';

describe('Node Browser Tree component', () => {
    const screenId = 'TestPage';
    const fieldId = 'test-node-browser-tree-field';

    const setup = (
        fieldProps: Partial<NodeBrowserTreeDecoratorProperties>,
        value: FieldInternalValue<FieldKey.NodeBrowserTree> = {},
    ) => {
        const initialState = {
            screenDefinitions: {
                [screenId]: {
                    metadata: {
                        uiComponentProperties: {
                            [fieldId]: { ...fieldProps },
                        },
                    },
                    values: {
                        ...(value && { [fieldId]: value }),
                    },
                    errors: {},
                },
            },
        };
        const utils = renderWithRedux<FieldKey.NodeBrowserTree, any>(
            <ConnectedNodeBrowserTree screenId={screenId} elementId={fieldId} />,
            {
                initialState,
                fieldType: FieldKey.NodeBrowserTree,
                fieldValue: value,
                fieldProperties: { ...fieldProps },
                elementId: fieldId,
                screenId,
                mockActions: true,
            },
        );

        const nodeBrowserTreeField = utils.getByTestId('e-field-bind-test-node-browser-tree-field', { exact: false });

        return {
            ...utils,
            nodeBrowserTreeField,
        };
    };

    beforeEach(() => {
        jest.spyOn(xtremRedux.actions, 'setFieldValue').mockReturnValue(
            () => Promise.resolve({ type: 'SetFieldValue' }) as any,
        );
        jest.spyOn(nodeInformationService, 'fetchNodeDetails').mockResolvedValue({
            firstName: {
                canFilter: true,
                canSort: true,
                label: 'First Name',
                name: 'firstName',
                kind: GraphQLKind.Scalar,
                type: GraphQLTypes.String,
                dataType: '',
                enumType: '',
                isCustom: false,
                isStored: true,
                isOnInputType: true,
                isOnOutputType: true,
                targetNode: '',
                isMutable: false,
            },
            lastName: {
                canFilter: true,
                canSort: true,
                label: 'Last Name',
                name: 'lastName',
                kind: GraphQLKind.Scalar,
                type: GraphQLTypes.String,
                dataType: '',
                enumType: '',
                isCustom: false,
                isStored: true,
                isOnInputType: true,
                isOnOutputType: true,
                targetNode: '',
                isMutable: false,
            },
            dateOfBirth: {
                canFilter: true,
                canSort: true,
                label: 'Date of birth',
                name: 'dateOfBirth',
                kind: GraphQLKind.Scalar,
                type: GraphQLTypes.Date,
                dataType: '',
                enumType: '',
                isCustom: false,
                isStored: true,
                isOnInputType: true,
                isOnOutputType: true,
                targetNode: '',
                isMutable: false,
            },
        });
    });

    afterEach(() => {
        jest.resetAllMocks();
        applyActionMocks();
    });

    it('should render hidden', () => {
        const { nodeBrowserTreeField } = setup({
            title: 'Test Field Title',
            isHidden: true,
            node: '@sage/xtrem-test/TestNode',
        });
        expect(nodeBrowserTreeField).toHaveClass('e-hidden');
    });

    it('should render title', () => {
        const { queryByTestId } = setup({ title: 'Test Field Title', node: '@sage/xtrem-test/TestNode' });
        expect(queryByTestId('e-field-label')).toHaveTextContent('Test Field Title');
    });

    it('should not render title if it is hidden', () => {
        const { queryByTestId } = setup({ title: 'Test Field Title', isTitleHidden: true });
        expect(queryByTestId('e-field-label')).toEqual(null);
    });

    it('should render helperText', () => {
        const { queryByTestId } = setup({
            title: 'Test Field Title',
            helperText: 'This is a helper text',
            node: '@sage/xtrem-test/TestNode',
        });
        expect(queryByTestId('e-field-helper-text')).toHaveTextContent('This is a helper text');
    });

    it('should not render helper text if it is hidden', () => {
        const { queryByTestId } = setup({
            title: 'Test Field Title',
            helperText: 'This is a helper text',
            isTitleHidden: true,
            node: '@sage/xtrem-test/TestNode',
        });
        expect(queryByTestId('e-field-label')).toEqual(null);
    });

    it('should disable search box if field is disabled', () => {
        const { queryByTestId } = setup({
            title: 'Test Field Title',
            isDisabled: true,
            node: '@sage/xtrem-test/TestNode',
        });
        expect(queryByTestId('e-node-browser-filter')).toHaveAttribute('disabled');
    });

    it('should disable selection checkboxes box if field is disabled', async () => {
        const { queryByTestId } = setup({
            title: 'Test Field Title',
            isDisabled: true,
            node: '@sage/xtrem-test/TestNode',
        });

        await waitFor(() => {
            expect(queryByTestId('e-tree-view-checkbox-label-firstName', { exact: false })).toHaveAttribute('disabled');
            expect(queryByTestId('e-tree-view-checkbox-label-lastName', { exact: false })).toHaveAttribute('disabled');
            expect(queryByTestId('e-tree-view-checkbox-label-dateOfBirth', { exact: false })).toHaveAttribute(
                'disabled',
            );
        });
    });

    it('should disable search box if field is disabled by callback function', () => {
        const { queryByTestId } = setup({
            title: 'Test Field Title',
            node: '@sage/xtrem-test/TestNode',
            isDisabled: jest.fn().mockReturnValue(true),
        });
        expect(queryByTestId('e-node-browser-filter')).toHaveAttribute('disabled');
    });

    it('should trigger the set field value action when a property is selected', async () => {
        const { queryByTestId } = setup({
            title: 'Test Field Title',
            node: '@sage/xtrem-test/TestNode',
        });

        await waitFor(() => {
            expect(queryByTestId('e-tree-view-checkbox-label-lastName', { exact: false })).not.toBeNull();
        });

        expect(xtremRedux.actions.setFieldValue).not.toHaveBeenCalled();

        fireEvent.click(queryByTestId('e-tree-view-checkbox-label-lastName', { exact: false })!);

        await waitFor(() => {
            expect(xtremRedux.actions.setFieldValue).toHaveBeenCalledWith(
                screenId,
                fieldId,
                {
                    lastName: {
                        data: {
                            canFilter: true,
                            canSort: true,
                            kind: GraphQLKind.Scalar,
                            label: 'Last Name',
                            name: 'lastName',
                            type: GraphQLTypes.String,
                            dataType: '',
                            enumType: '',
                            isCustom: false,
                            isStored: true,
                            isOnInputType: true,
                            isOnOutputType: true,
                            targetNode: '',
                            isMutable: false,
                        },
                        id: 'lastName',
                        key: 'lastName',
                        label: 'Last Name',
                        labelKey: 'Last Name',
                        labelPath: 'Last Name',
                    },
                },
                true,
            );
        });
    });

    it('should trigger the onChange event for the application code', async () => {
        const { queryByTestId } = setup({
            title: 'Test Field Title',
            node: '@sage/xtrem-test/TestNode',
        });

        await waitFor(() => {
            expect(queryByTestId('e-tree-view-checkbox-label-lastName', { exact: false })).not.toBeNull();
        });

        expect(events.triggerFieldEvent).not.toHaveBeenCalled();

        fireEvent.click(queryByTestId('e-tree-view-checkbox-label-lastName', { exact: false })!);

        await waitFor(() => {
            expect(events.triggerFieldEvent).toHaveBeenCalledWith(screenId, fieldId, 'onChange');
        });
    });

    it('should render entry checked if it is already defined in the field value definition', async () => {
        const { queryByTestId } = setup(
            {
                title: 'Test Field Title',
                node: '@sage/xtrem-test/TestNode',
            },
            {
                lastName: {
                    data: {
                        canFilter: true,
                        canSort: true,
                        kind: GraphQLKind.Scalar,
                        label: 'Last Name',
                        name: 'lastName',
                        type: GraphQLTypes.String,
                        dataType: '',
                        enumType: '',
                        isCustom: false,
                        isStored: true,
                        isOnInputType: true,
                        isOnOutputType: true,
                        targetNode: '',
                        isMutable: false,
                    },
                    id: 'lastName',
                    key: 'lastName',
                    label: 'Last Name',
                    labelKey: 'Last Name',
                    labelPath: 'Last Name',
                },
            },
        );

        await waitFor(() => {
            expect(
                (queryByTestId('e-tree-view-checkbox-label-lastName', { exact: false }) as HTMLInputElement).checked,
            ).toBe(true);
            expect(
                (queryByTestId('e-tree-view-checkbox-label-firstName', { exact: false }) as HTMLInputElement).checked,
            ).toBe(false);
            expect(
                (queryByTestId('e-tree-view-checkbox-label-dateOfBirth', { exact: false }) as HTMLInputElement).checked,
            ).toBe(false);
        });
    });
});
