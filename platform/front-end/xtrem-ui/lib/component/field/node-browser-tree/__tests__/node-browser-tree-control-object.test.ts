import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import type { <PERSON>Key } from '../../../types';
import { NodeBrowserTreeControlObject } from '../../../control-objects';
import type { NodeBrowserTreeProperties, NodeBrowserTreeValue } from '../node-browser-tree-types';
import { GraphQLKind } from '../../../../types';

describe('Node Browser Tree control object', () => {
    let nodeBrowserTreeField: NodeBrowserTreeControlObject;
    let fieldProperties: NodeBrowserTreeProperties;

    const nodeBrowserTreeValue: NodeBrowserTreeValue = {
        value: {
            key: '',
            labelKey: '',
            label: '',
            data: {
                canFilter: false,
                canSort: false,
                kind: GraphQLKind.List,
                label: '',
                name: '',
                type: '',
            },
        } as any,
    };

    beforeEach(() => {
        fieldProperties = {
            title: 'TEST_FIELD_TITLE',
        };
    });

    describe('getters and setters', () => {
        beforeEach(() => {
            nodeBrowserTreeField = buildControlObject<FieldKey.NodeBrowserTree>(NodeBrowserTreeControlObject, {
                fieldProperties,
                fieldValue: nodeBrowserTreeValue,
            });
        });

        it('getting field value', () => {
            expect(nodeBrowserTreeField.value).toEqual(nodeBrowserTreeValue);
        });

        it('should set the title', () => {
            const testFixture = 'Test Node Browser Field Title';
            expect(fieldProperties.title).not.toEqual(testFixture);
            nodeBrowserTreeField.title = testFixture;
            expect(fieldProperties.title).toEqual(testFixture);
        });

        it('should set the helper text', () => {
            const testFixture = 'Test Node Browser Field Helper Text';
            expect(fieldProperties.helperText).not.toEqual(testFixture);
            nodeBrowserTreeField.helperText = testFixture;
            expect(fieldProperties.helperText).toEqual(testFixture);
        });

        it('should set isDisabled property', () => {
            const testFixture = true;
            expect(fieldProperties.isDisabled).not.toEqual(testFixture);
            nodeBrowserTreeField.isDisabled = testFixture;
            expect(fieldProperties.isDisabled).toEqual(testFixture);
        });

        it('should set isHelperTextHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHelperTextHidden).not.toEqual(testFixture);
            nodeBrowserTreeField.isHelperTextHidden = testFixture;
            expect(fieldProperties.isHelperTextHidden).toEqual(testFixture);
        });

        it('should set isHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isHidden).not.toEqual(testFixture);
            nodeBrowserTreeField.isHidden = testFixture;
            expect(fieldProperties.isHidden).toEqual(testFixture);
        });

        it('should set isTitleHidden property', () => {
            const testFixture = true;
            expect(fieldProperties.isTitleHidden).not.toEqual(testFixture);
            nodeBrowserTreeField.isTitleHidden = testFixture;
            expect(fieldProperties.isTitleHidden).toEqual(testFixture);
        });
    });
});
