import type { Page } from '../../../..';
import * as pageMetaData from '../../../../service/page-metadata';
import type { ScreenBase } from '../../../../service/screen-base';
import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import { nodeBrowserTreeField } from '../node-browser-tree-decorator';
import type { NodeBrowserTreeDecoratorProperties } from '../node-browser-tree-types';

describe('Node Browser Tree decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'nodeBrowserTreeField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should set default values when no component properties provided', () => {
        nodeBrowserTreeField({})({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: NodeBrowserTreeDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as NodeBrowserTreeDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onClick).toBeUndefined();
    });

    it('should set values when component properties provided', () => {
        const clickFunc: () => void = jest.fn().mockImplementation(() => {});

        nodeBrowserTreeField({
            onClick: clickFunc,
            helperText: 'helper text',
        })({} as Page, fieldId);
        pageMetadata.fieldThunks[fieldId]({}, {});
        const mappedComponentProperties: NodeBrowserTreeDecoratorProperties<ScreenBase> = pageMetadata
            .uiComponentProperties[fieldId] as NodeBrowserTreeDecoratorProperties<ScreenBase>;
        expect(mappedComponentProperties.onClick).not.toBeUndefined();
        expect(mappedComponentProperties.onClick).toBe(clickFunc);
        expect(mappedComponentProperties.helperText).toBe('helper text');
    });

    describe('mapping values', () => {
        it('should assign onClick handler', () => {
            testOnClickHandler(nodeBrowserTreeField, pageMetadata, fieldId);
        });
    });
});
