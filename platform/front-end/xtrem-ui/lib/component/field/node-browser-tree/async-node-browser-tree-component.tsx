import * as React from 'react';
import type { FieldComponentExternalProperties } from '../field-base-component-types';
import { InputFieldSkeleton } from '../../ui/input-field-skeleton';
import { hasConnectedSkeletonFieldTitle } from '../../../utils/async-component-utils';
import type { NodeBrowserTreeComponentProps } from './node-browser-tree-types';

const ConnectedNodeBrowserTree = React.lazy(() => import('./node-browser-tree-component'));

export function AsyncConnectedNodeBrowserTreeComponent(props: FieldComponentExternalProperties): React.ReactElement {
    const hasHeader = hasConnectedSkeletonFieldTitle(props);
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={hasHeader} bodyHeight="200px" />}>
            <ConnectedNodeBrowserTree {...props} />
        </React.Suspense>
    );
}

const NodeBrowserTreeComponent = React.lazy(() =>
    import('./node-browser-tree-component').then(e => ({ default: e.NodeBrowserTreeComponent })),
);

export function AsyncNodeBrowserTreeComponent(props: NodeBrowserTreeComponentProps): React.ReactElement {
    return (
        <React.Suspense fallback={<InputFieldSkeleton hasTitle={false} bodyHeight="200px" />}>
            <NodeBrowserTreeComponent {...props} />
        </React.Suspense>
    );
}
