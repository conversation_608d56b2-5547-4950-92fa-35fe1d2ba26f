/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenBaseGenericType, ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { ControlObjectProperty } from '../../property-decorators/control-object-property-decorator';
import type { FieldKey, FieldComponentProps } from '../../types';
import type { NodeBrowserTreeDecoratorProperties, NodeBrowserTreeProperties } from './node-browser-tree-types';

/**
 * [Field]{@link ReadonlyFieldControlObject} that holds a set of values of any type. It can contain nested fields
 */
export class NodeBrowserTreeControlObject<
    CT extends ScreenExtension<CT> = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.NodeBrowserTree, FieldComponentProps<FieldKey.NodeBrowserTree>> {
    static readonly defaultUiProperties: Partial<NodeBrowserTreeDecoratorProperties> = {
        ...EditableFieldControlObject.defaultUiProperties,
    };

    @ControlObjectProperty<NodeBrowserTreeProperties<CT>, NodeBrowserTreeControlObject<CT>>()
    node: keyof ScreenBaseGenericType<CT>;
}
