import type { DataTypeDetails, NodeDetailsProperty } from '../../../service/metadata-types';
import type { Extend } from '../../../service/page-extension';
import type { ScreenExtension } from '../../../types';
import { addDisabledToProperties, addOptionTypeToProperties } from '../../../utils/data-type-utils';
import type { ChangeableOverrideDecoratorProperties } from '../../../utils/decorator-utils';
import {
    standardDecoratorImplementation,
    standardExtensionDecoratorImplementation,
} from '../../../utils/decorator-utils';
import { AbstractFieldDecorator } from '../../abstract-field-decorator';
import { AbstractFieldLayoutBuilder } from '../../abstract-field-layout-builder';
import { FieldKey } from '../../types';
import { RadioControlObject } from './radio-control-object';
import type { RadioDecoratorProperties } from './radio-types';

class RadioDecorator extends AbstractFieldDecorator<FieldKey.Radio> {
    protected _layout = AbstractFieldLayoutBuilder;

    protected _controlObjectConstructor = RadioControlObject;

    getComponentPropertiesFromDataType(
        dataType: DataTypeDetails,
        propertyDetails: NodeDetailsProperty,
    ): Partial<RadioDecoratorProperties> {
        const properties: Partial<RadioDecoratorProperties> = {};
        addOptionTypeToProperties({ propertyDetails, dataType, properties });
        addDisabledToProperties({
            propertyDetails,
            dataType,
            properties,
        });
        return properties;
    }
}

/**
 * Initializes the decorated member as a [Radio]{@link RadioControlObject} field with the provided properties
 *
 * @param properties The properties that the [Radio]{@link RadioControlObject} field will be initialized with
 */
export function radioField<T extends ScreenExtension<T>>(
    properties: RadioDecoratorProperties<Extend<T>>,
): (target: T, name: string) => void {
    return standardDecoratorImplementation<T, FieldKey.Radio>(properties, RadioDecorator, FieldKey.Radio);
}

export function radioFieldOverride<T extends ScreenExtension<T>>(
    properties: ChangeableOverrideDecoratorProperties<RadioDecoratorProperties<Extend<T>>, Extend<T>>,
): (target: T, name: string) => void {
    return standardExtensionDecoratorImplementation<T, FieldKey.Radio>(properties);
}
