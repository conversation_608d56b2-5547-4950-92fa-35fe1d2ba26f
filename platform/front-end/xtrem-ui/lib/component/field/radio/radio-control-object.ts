/**
 * @packageDocumentation
 * @module root
 * */

import type { ScreenBase } from '../../../service/screen-base';
import type { ScreenExtension } from '../../../types';
import { EditableFieldControlObject } from '../../editable-field-control-object';
import { FieldControlObjectResolvedProperty } from '../../property-decorators/control-object-resolved-property-decorator';
import type { FieldComponentProps, FieldKey } from '../../types';
import { getValidOptionValuesForSelect } from '../select/select-utils';
import type { RadioProperties } from './radio-types';

/**
 * [Field]{@link EditableFieldControlObject} that holds a value from a set of given values
 */
export class RadioControlObject<
    ReferencedEnumType extends string = string,
    CT extends ScreenExtension<CT> = ScreenBase,
> extends EditableFieldControlObject<CT, FieldKey.Radio, FieldComponentProps<FieldKey.Radio>> {
    @FieldControlObjectResolvedProperty<RadioProperties<CT>, RadioControlObject<ReferencedEnumType, CT>>()
    /** Options to be displayed in the select element */
    options?: string[];

    /**
     * The GraphQL node that the select options will be fetched from.
     * When using this property, the node must be an Enum
     */
    get optionType(): string | undefined {
        return this.getUiComponentProperty('optionType');
    }

    /** Moves the browser focus to this field */
    focus(): void {
        this._focus();
    }

    /** Field's value, only valid options can be set as value. */
    set value(newValue: ReferencedEnumType | null) {
        const validOptions = getValidOptionValuesForSelect(this.options || [], this.optionType);

        if (newValue === null || validOptions.indexOf(newValue) !== -1) {
            this._setValue(newValue as string);
        } else {
            throw new Error(
                `${newValue} is not a valid option of the ${this.elementId} field. Valid options: ${validOptions.join(
                    ', ',
                )}`,
            );
        }
    }

    /** Field's value, only valid options can be set as value. */
    get value(): ReferencedEnumType | null {
        return (this._getValue() as ReferencedEnumType) || null;
    }
}
