import type { ScreenBase } from '../../../service/screen-base';
import type { BlockControlObject } from '../../control-objects';
import type { EditableFieldProperties } from '../../editable-field-control-object';
import type { FieldControlObjectInstance } from '../../types';
import type { BaseEditableComponentProperties } from '../field-base-component-types';
import type {
    CanFetchDefaults,
    Changeable,
    ExtensionField,
    HasOptions,
    HasParent,
    Mappable,
    Sizable,
    Validatable,
} from '../traits';

export interface RadioProperties<CT extends ScreenBase = ScreenBase>
    extends EditableFieldProperties<CT>,
        Sizable,
        HasOptions<CT>,
        CanFetchDefaults,
        Mappable<CT> {}

export interface RadioDecoratorProperties<CT extends ScreenBase = ScreenBase>
    extends Omit<RadioProperties<CT>, '_controlObjectType'>,
        Changeable<CT>,
        HasParent<CT, BlockControlObject<CT>>,
        ExtensionField<CT, FieldControlObjectInstance<any>>,
        Validatable<CT>,
        Sizable {}

export type RadioComponentProps = BaseEditableComponentProperties<RadioProperties, string>;

// https://github.com/Sage/carbon/blob/master/src/__experimental__/components/radio-button/radio-button-group.d.ts
export interface CarbonRadioButtonGroupProps {
    name: string;
    legend: string;
    groupName: string;
    label: string;
    labelHelp?: string;
    hasError?: boolean;
    error?: string;
    warning?: string;
    info?: string;
    value?: string;
    onChange?: (ev: React.ChangeEvent<HTMLInputElement>) => void;
    styleOverride?: {
        root?: object;
        content?: object;
        legend?: object;
    };
}

// https://github.com/Sage/carbon/blob/master/src/__experimental__/components/radio-button/radio-button.d.ts
export interface CarbonRadioButtonProps {
    key: string;
    checked?: boolean;
    disabled?: boolean;
    error?: boolean;
    fieldHelpInline?: boolean;
    id?: string;
    inputWidth?: number;
    label?: string;
    labelAlign?: 'left' | 'right';
    labelWidth?: number;
    name?: string;
    onChange?: (ev: React.ChangeEvent<HTMLElement>) => void;
    reverse?: boolean;
    size?: 'small' | 'large';
    value: string;
}
