// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Radio component Snapshot 1`] = `
.c4 {
  font-style: normal;
  font-size: 14px;
  font-weight: 400;
  text-transform: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  line-height: 21px;
  margin: 0 0 16px;
  padding: 0;
  border: 0;
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  white-space: nowrap;
  color: rgba(0,0,0,0.90);
}

.c12 {
  margin-bottom: var(--fieldSpacing);
}

.c11 + .c11 {
  margin-top: 16px;
}

.c12.c12.c12 {
  margin-top: var(--spacing000);
  margin-bottom: var(--spacing000);
}

.c14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c21 {
  color: var(--colorsUtilityYin090);
  display: block;
  font-weight: var(--fontWeights500);
}

.c20 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 8px;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  box-sizing: border-box;
  margin-bottom: 0;
  padding-left: var(--spacing100);
  width: 30%;
}

.c18 {
  cursor: pointer;
  opacity: 0;
  margin: 0;
  position: relative;
  z-index: 2;
}

.c16 {
  display: inline-block;
  position: relative;
}

.c10 {
  width: 100% !important;
}

.c10 .c13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c10 .c19 {
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  padding-top: 0;
  width: auto;
}

.c8 {
  margin-bottom: var(--fieldSpacing);
}

.c8 .c15 {
  padding-top: 1px;
}

.c8 svg {
  background-color: var(--colorsUtilityYang100);
  border: 1px solid var(--colorsUtilityMajor300);
}

.c8 .c17,
.c8 svg {
  height: 16px;
  position: absolute;
  padding: 1px;
}

.c8 .c19 {
  width: auto;
  -webkit-flex: 0 1 auto;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
}

.c1 {
  margin: 0;
  margin-bottom: var(--fieldSpacing);
  border: none;
  padding: 0;
  min-width: 0;
  min-inline-size: 0;
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  line-height: 24px;
}

.c2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: var(--spacing100);
  padding: 0;
  font-weight: var(--fontWeights500);
  color: var(--colorsUtilityYin090);
  text-align: left;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  width: -moz-available;
}

.c9 {
  margin-bottom: var(--spacing150);
}

.c9:last-of-type {
  margin-bottom: 0;
}

.c9.c9 .c11 {
  margin: 0;
}

.c9 svg {
  padding: 1px;
}

.c9 circle {
  r: 5;
}

.c9 .c19 {
  -webkit-flex: 1 1 calc(100% - 44px);
  -ms-flex: 1 1 calc(100% - 44px);
  flex: 1 1 calc(100% - 44px);
}

.c6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c0 .c7 {
  display: inline-block;
}

.c0 .c7:not(:last-child) {
  margin-right: 72px;
}

.c0 .c5 {
  display: block;
}

<body>
  <div>
    <div
      class="e-field c0 e-radio-field"
      data-label="title radio"
      data-testid="e-radio-field e-field-label-titleRadio e-field-bind-test-radio"
    >
      <fieldset
        class="c1"
        data-component="radiogroup"
      >
        <legend
          class="c2"
          data-element="legend"
          data-role="legend"
        >
          <span
            class="c3"
          >
            title radio
          </span>
        </legend>
        <p
          class="c4"
          color="blackOpacity90"
        />
        <div
          class="c5 c6"
          data-component="radio-button-group"
          role="radiogroup"
        >
          <div
            class="c7 c8 c9"
            data-component="radio-button"
          >
            <div
              class="c10"
            >
              <div
                class="c11 c12"
              >
                <div
                  class="c13 c14"
                  data-role="field-line"
                >
                  <div
                    class="c15 c16"
                    data-role="checkable-input"
                  >
                    <input
                      aria-invalid="false"
                      class="c17 c18 e-radio-button-item"
                      data-testid="e-radio-component-field e-field-label-radioButton e-field-bind-test-radio"
                      id="testcarb-onco-mpon-ents-uniqguidmock"
                      name="test-radio"
                      role="radio"
                      type="radio"
                      value="option 1"
                    />
                    <div
                      class=""
                    >
                      <svg
                        data-role="radio-svg"
                        focusable="false"
                        viewBox="0 0 15 15"
                      >
                        <g
                          fill="none"
                          fill-rule="evenodd"
                          stroke="none"
                          stroke-width="1"
                        >
                          <circle
                            class="radio-button-check"
                            cx="7.5"
                            cy="7.5"
                            fill="#FFFFFF"
                            r="5"
                          />
                        </g>
                      </svg>
                    </div>
                  </div>
                  <div
                    class="c19 c20"
                    data-role="label-container"
                    id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                    width="30"
                  >
                    <label
                      class="c21"
                      data-element="label"
                      for="testcarb-onco-mpon-ents-uniqguidmock"
                      id="testcarb-onco-mpon-ents-uniqguidmock-label"
                    >
                      option 1
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="c7 c8 c9"
            data-component="radio-button"
          >
            <div
              class="c10"
            >
              <div
                class="c11 c12"
              >
                <div
                  class="c13 c14"
                  data-role="field-line"
                >
                  <div
                    class="c15 c16"
                    data-role="checkable-input"
                  >
                    <input
                      aria-invalid="false"
                      class="c17 c18 e-radio-button-item"
                      data-testid="e-radio-component-field e-field-label-radioButton e-field-bind-test-radio"
                      id="testcarb-onco-mpon-ents-uniqguidmock"
                      name="test-radio"
                      role="radio"
                      type="radio"
                      value="option 2"
                    />
                    <div
                      class=""
                    >
                      <svg
                        data-role="radio-svg"
                        focusable="false"
                        viewBox="0 0 15 15"
                      >
                        <g
                          fill="none"
                          fill-rule="evenodd"
                          stroke="none"
                          stroke-width="1"
                        >
                          <circle
                            class="radio-button-check"
                            cx="7.5"
                            cy="7.5"
                            fill="#FFFFFF"
                            r="5"
                          />
                        </g>
                      </svg>
                    </div>
                  </div>
                  <div
                    class="c19 c20"
                    data-role="label-container"
                    id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                    width="30"
                  >
                    <label
                      class="c21"
                      data-element="label"
                      for="testcarb-onco-mpon-ents-uniqguidmock"
                      id="testcarb-onco-mpon-ents-uniqguidmock-label"
                    >
                      option 2
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="c7 c8 c9"
            data-component="radio-button"
          >
            <div
              class="c10"
            >
              <div
                class="c11 c12"
              >
                <div
                  class="c13 c14"
                  data-role="field-line"
                >
                  <div
                    class="c15 c16"
                    data-role="checkable-input"
                  >
                    <input
                      aria-invalid="false"
                      class="c17 c18 e-radio-button-item"
                      data-testid="e-radio-component-field e-field-label-radioButton e-field-bind-test-radio"
                      id="testcarb-onco-mpon-ents-uniqguidmock"
                      name="test-radio"
                      role="radio"
                      type="radio"
                      value="option 3"
                    />
                    <div
                      class=""
                    >
                      <svg
                        data-role="radio-svg"
                        focusable="false"
                        viewBox="0 0 15 15"
                      >
                        <g
                          fill="none"
                          fill-rule="evenodd"
                          stroke="none"
                          stroke-width="1"
                        >
                          <circle
                            class="radio-button-check"
                            cx="7.5"
                            cy="7.5"
                            fill="#FFFFFF"
                            r="5"
                          />
                        </g>
                      </svg>
                    </div>
                  </div>
                  <div
                    class="c19 c20"
                    data-role="label-container"
                    id="label-container-testcarb-onco-mpon-ents-uniqguidmock-label"
                    width="30"
                  >
                    <label
                      class="c21"
                      data-element="label"
                      for="testcarb-onco-mpon-ents-uniqguidmock"
                      id="testcarb-onco-mpon-ents-uniqguidmock-label"
                    >
                      option 3
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </fieldset>
      <span
        class="common-input__help-text"
        data-element="help"
        data-testid="e-field-helper-text"
      >
         
      </span>
    </div>
  </div>
</body>
`;
