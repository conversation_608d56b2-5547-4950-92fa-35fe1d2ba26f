import { getMockPageMetadata, testOnClickHandler } from '../../../../__tests__/test-helpers';
import type { Page } from '../../../../service/page';
import * as pageMetaData from '../../../../service/page-metadata';
import { radioField } from '../radio-decorator';
import type { RadioDecoratorProperties } from '../radio-types';

describe('radio decorator', () => {
    let fieldId: string;
    let pageMetadata: pageMetaData.PageMetadata;

    beforeEach(() => {
        fieldId = 'radioField';
        pageMetadata = getMockPageMetadata();
        jest.spyOn(pageMetaData, 'getPageMetadata').mockReturnValue(pageMetadata);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('mapping values', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should set default values when no component properties provided', () => {
            radioField({})({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});
            const mappedComponentProperties = pageMetadata.uiComponentProperties[fieldId] as RadioDecoratorProperties;
            expect(mappedComponentProperties.options).toBeUndefined();
            expect(mappedComponentProperties.optionType).toBeUndefined();
            expect(mappedComponentProperties.onChange).toBeUndefined();
        });

        it('should set values when component properties provided', () => {
            const mockOptions = ['option1', 'anotherOption'];
            const mockOptionType = '@any/enum/Type';
            radioField({
                options: mockOptions,
                optionType: mockOptionType,
            })({} as Page, fieldId);
            pageMetadata.fieldThunks[fieldId]({}, {});

            const mappedComponentProperties = pageMetadata.uiComponentProperties[fieldId] as RadioDecoratorProperties;
            expect(mappedComponentProperties.options).toEqual(mockOptions);
            expect(mappedComponentProperties.optionType).toEqual(mockOptionType);
        });

        it('should assign onClick handler', () => {
            testOnClickHandler(radioField, pageMetadata, fieldId);
        });
    });
});
