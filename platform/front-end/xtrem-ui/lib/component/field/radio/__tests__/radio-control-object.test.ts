import type { <PERSON><PERSON><PERSON> } from '../../../types';
import { RadioControlObject } from '../../../control-objects';
import { buildControlObject } from '../../../../__tests__/test-helpers/control-object-helpers';
import type { RadioProperties } from '../radio-types';
import * as stateUtils from '../../../../utils/state-utils';
import { getMockPageDefinition } from '../../../../__tests__/test-helpers';

jest.spyOn(stateUtils, 'getPageDefinitionFromState').mockImplementation(() => getMockPageDefinition('TestPage'));

describe('Radio control object', () => {
    let radioControlObject: RadioControlObject;
    let radioFieldProperties: RadioProperties;
    let radioValue: string;

    beforeEach(() => {
        radioFieldProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
            options: ['randomValue1', 'randomValue2'],
        };
        radioValue = 'RADIO TEST VALUE';
        radioControlObject = buildControlObject<FieldKey.Radio>(RadioControlObject, {
            fieldValue: radioValue,
            fieldProperties: radioFieldProperties,
        });
    });

    it('should get the field value', () => {
        expect(radioControlObject.value).toEqual(radioValue);
    });

    it('should set field value to null', () => {
        expect(() => {
            radioControlObject.value = null;
        }).not.toThrow();
    });

    it('should set field value to a valid option', () => {
        expect(() => {
            radioControlObject.value = 'randomValue1';
        }).not.toThrow();
    });

    it('should not set field value to an invalid option', () => {
        expect(() => {
            radioControlObject.value = 'randomValue3';
        }).toThrow(
            'randomValue3 is not a valid option of the fieldName field. Valid options: randomValue1, randomValue2',
        );
    });

    it('should get the option type field value', () => {
        expect(radioControlObject.optionType).toEqual(radioFieldProperties.optionType);
    });

    it('should set the title', () => {
        const newValue = 'Test Radio Field Title';
        expect(radioFieldProperties.title).not.toEqual(newValue);
        radioControlObject.title = newValue;
        expect(radioFieldProperties.title).toEqual(newValue);
    });

    it('should get the title', () => {
        expect(radioFieldProperties.title).toEqual(radioFieldProperties.title);
    });

    it('should set the options', () => {
        const newValue = ['value1', 'value2'];
        expect(radioFieldProperties.options).not.toEqual(newValue);
        radioControlObject.options = newValue;
        expect(radioFieldProperties.options).toEqual(newValue);
    });

    it('should get the options', () => {
        expect(radioFieldProperties.options).toEqual(radioFieldProperties.options);
    });

    it('should resolve callback style option list', () => {
        radioFieldProperties = {
            title: 'TEST_FIELD_TITLE',
            isHidden: true,
            isDisabled: true,
            options() {
                return ['Option12', 'Option32'];
            },
        };

        radioValue = 'RADIO TEST VALUE';
        radioControlObject = buildControlObject<FieldKey.Radio>(RadioControlObject, {
            fieldValue: radioValue,
            fieldProperties: radioFieldProperties,
        });

        expect(radioControlObject.options).toEqual(['Option12', 'Option32']);
    });
});
